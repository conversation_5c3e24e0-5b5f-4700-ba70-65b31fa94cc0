//*****************************************************************************
//File: hwlib.h
//Describe:硬件驱动处理层
//Author：wangzhenglong
//Date: 2016.8.11
//*****************************************************************************

#ifndef _HW_LIB_H_
#define _HW_LIB_H_

#include<linux/kernel.h>
#include<linux/module.h>
#include<linux/uaccess.h>
#include<linux/delay.h>
#include<linux/slab.h>
#include<linux/time.h>

#include "wtdefine.h"
#include "../general/devlib/ioctlcmd.h"
#include "../general/wterror.h"

extern struct workqueue_struct *wt_queue;
extern struct dev_unit *pBPdev;             //背板资源指针

int LedIOEx_init(struct dev_unit *pdev);

int wt_check_direct_reg_mask(struct dev_unit *pdev, int addr, int value, int mask);

int wt_check_direct_reg(struct dev_unit *pdev, int addr, int value);

int wt_write_direct_multi_reg(struct RegType *pRegType, int count, struct dev_unit *pdev);

int wt_read_direct_multi_reg(struct RegType *pRegType, int count, struct dev_unit *pdev);

int wt_bp_spi_direct_for_write(int SPIConfig, int TXData, const unsigned int *Reg, struct dev_unit *pdev);

int wt_bp_spi_direct_for_read(int SPIConfig, int TXData, int *pData, unsigned int *Reg, struct dev_unit *pdev);

int wt_smbus_for_write(int DiveceAddr, int DiveceComReg, int SMBusConfig, int Data, unsigned int *Reg, struct dev_unit *pdev);

int wt_smbus_for_read(int DiveceAddr, int DiveceComReg, int SMBusConfig, int *pData, unsigned int *Reg, struct dev_unit *pdev);

/*--------------单元板(背板/VSA、VSG业务板)公共接口------------------------*/
// 获取单元板硬件版本
int wt_GetUnitBoardHWVersion(struct dev_unit *pdev);
int WT_GetUnitBoardHWVersion(int DataLength, void  *arg, struct dev_unit *pdev);
//单元板FPGA信息
int WT_GetUnitBoardFPGAInfo(int DataLength,  void  *arg, struct dev_unit *pdev);
//获取单元板修订版本
int WT_GetUnitBoardRevisionId(int DataLength, void  *arg, struct dev_unit *pdev);
int WT_SetUnitBoardPciDelay(int DataLength, void *arg, struct dev_unit *pdev);
//Flash
int wt_WriteRomPage(int Addr, int *pBuf, unsigned int *Reg, struct dev_unit *pdev);

int wt_ReadRomPage(int Addr, int *pBuf, unsigned int *Reg, struct dev_unit *pdev);

int WT_WriteRomPage(int DataLength, void  *arg, struct dev_unit *pdev);

int WT_ReadRomPage(int DataLength, void  *arg, struct dev_unit *pdev);

//电压侦测
int WT_ReadUBVoltChannelValue(int DataLength,  void  *arg, struct dev_unit *pdev);

//温度传感器
int WT_ReadUBTemperature(int DataLength,  void  *arg, struct dev_unit *pdev);
/****************业务单元板(VSA/VSG)通用接口***********/
/****************VSA单元类型***********/
int WT_GetTrigAddrStart(int DataLength, void *arg, struct dev_unit *pdev);

//快速AGC
int WT_SetAGCEnable(int DataLength,  void  *arg, struct dev_unit *pdev);

int WT_SetAGCGateValue(int DataLength,  void  *arg, struct dev_unit *pdev);

//VSA链路常规配置
int WT_SetADCPowerDown(int DataLength,  void  *arg, struct dev_unit *pdev);

/****************背板单元类型***********/
//加密芯片
int WT_CryptoMemInit(int DataLength, void *arg, struct dev_unit *pdev);

int WT_GetCryptoMemInfo(int DataLength, void *arg, struct dev_unit *pdev);

int WT_GetCryptoMemSN(int DataLength, void *arg, struct dev_unit *pdev);

int WT_GetCryptoMemCode(int DataLength, void *arg, struct dev_unit *pdev);

/*--------------PCI寄存器读写------------------------*/
int WT_WriteDirectReg(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadDirectReg(int DataLength, void *arg, struct dev_unit *pdev);

int WT_CheckDirectReg(int DataLength, void *arg, struct dev_unit *pdev);

int WT_WriteDirectMultiReg(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadDirectMultiReg(int DataLength, void *arg, struct dev_unit *pdev);

//TODO开关板Flash

//TODO控制板

//**********************************************************************
typedef int (*pFunc)(int DataLength, void *arg, struct dev_unit *pdev);

extern const pFunc pFuncArray[];

#endif
