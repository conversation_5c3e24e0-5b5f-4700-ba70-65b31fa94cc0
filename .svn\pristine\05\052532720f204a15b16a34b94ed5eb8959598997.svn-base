/*
 * vsahandler.h
 * vsa callback
 *  Created on: 2019-3-14
 *      Author: Administrator
 */

#ifndef VSAHANDLER_H_
#define VSAHA<PERSON>LER_H_
#include "scpi/scpi.h"
#include "scpi_gen_gle.h"

#ifdef __cplusplus
extern "C" {
#endif

#define GetBit(x, y)            (((x) >> (y)) & 0x1u)            //获取第y位

enum WT_GLE_FRAMETYPE
{
    WT_GLE_FRAMETYPE_auto = 0,
    WT_GLE_FRAMETYPE_1,
    WT_GLE_FRAMETYPE_2,
    WT_GLE_FRAMETYPE_3,
    WT_GLE_FRAMETYPE_4,
};

enum WT_GLE_CTRLINFOTYPE
{
    WT_GLE_CTRLINFOTYPE_A1 = 0,
    WT_GLE_CTRLINFOTYPE_A2,
    WT_GLE_CTRLINFOTYPE_A3,
    WT_GLE_CTRLINFOTYPE_A4,
    WT_GLE_CTRLINFOTYPE_A5,
    WT_GLE_CTRLINFOTYPE_A6,
    WT_GLE_CTRLINFOTYPE_A7,
    WT_GLE_CTRLINFOTYPE_B1,
    WT_GLE_CTRLINFOTYPE_B2,
    WT_GLE_CTRLINFOTYPE_B3,
    WT_GLE_CTRLINFOTYPE_B4,
    WT_GLE_CTRLINFOTYPE_B5,
};

    int SetVsaAlzParam(SPCIUserParam *attr, int SendMonitor = true);
    int ResponseReadyLiteResult(scpi_t *context, int litetype, int streamID, int segmentID);
    scpi_result_t RstDoubleVectorLite(
        scpi_t *context,
        int smp,
        double *data,
        int Points,
        int streamID,
        int segmentID,
        int type);
    scpi_result_t RstComplexVectorLite(
        scpi_t *context,
        int smp,
        Complex *data,
        int Points,
        int streamID,
        int segmentID,
        int type);
    //配置
    scpi_result_t SetVsaFrequency(scpi_t *context);
    scpi_result_t SetVsaMaxPower(scpi_t * context);
    scpi_result_t SetVsaSamplingTime(scpi_t * context);
    scpi_result_t SetVsaPort(scpi_t * context);
    scpi_result_t SetVsaTriggerType(scpi_t * context);
    scpi_result_t SetVsaTriggerLevel(scpi_t * context);
    scpi_result_t SetVsaTriggerTimeOut(scpi_t * context);
    scpi_result_t SetVsaTriggerPreTime(scpi_t * context);
    scpi_result_t SetVsaTriggerGapTime(scpi_t * context);
    scpi_result_t SetVsaTriggerEdge(scpi_t * context);
    scpi_result_t SetVsaFreqOffset(scpi_t * context);
    scpi_result_t SetVsaAlzFrameIndex(scpi_t * context);
    scpi_result_t SetVsaTimeOutWaittingSec(scpi_t * context);
    scpi_result_t SetVsaAlzBandwidthMode(scpi_t * context);
    scpi_result_t SetVsaSampleRate(scpi_t * context);
    scpi_result_t SetVsaSampleRateMode(scpi_t * context);
    scpi_result_t SetVsaMaxIFG(scpi_t * context);
    scpi_result_t SetVsaExtGain(scpi_t * context);
    scpi_result_t SetVsaAlzDemod(scpi_t * context);
    scpi_result_t SetVsaAlzSpectrumWideMode(scpi_t * context);
    scpi_result_t SetVsaDemod(scpi_t * context);
    scpi_result_t SetVsaDCOffsetI(scpi_t *context);
    scpi_result_t SetVsaDCOffsetQ(scpi_t *context);

    scpi_result_t SetVsaAlzTimeOut(scpi_t *context);
    scpi_result_t SetVsaAlzIQSwapFlag(scpi_t * context);
    scpi_result_t SetVsaAlzFreqOffset(scpi_t * context);
    scpi_result_t SetVsaAlzIQReversedFlag(scpi_t *context);
    scpi_result_t SetVsaAlzPacketStartPoint(scpi_t *context);
    scpi_result_t SetVsaAlzFilterTime(scpi_t *context);
    scpi_result_t SetVsaAlzCCDFFlag(scpi_t *context);
    scpi_result_t SetVsaAlzSpectrumFlag(scpi_t *context);
    scpi_result_t SetVsaAlzAvgTimes(scpi_t *context);
    scpi_result_t SetVsaNoiseCalibrationStart(scpi_t *context);
    scpi_result_t GetVsaNoiseCalibrationStatus(scpi_t *context);
    scpi_result_t SetVsaNoiseCalibrationStop(scpi_t *context);
    scpi_result_t GetVsaNoiseCalibrationPortValid(scpi_t *context);
    scpi_result_t SetVsaNoiseCalSelfStart(scpi_t *context);
    scpi_result_t GetVsaNoiseCalSelfStatus(scpi_t *context);
    scpi_result_t SetVsaNoiseCalSelfStop(scpi_t *context);
    scpi_result_t GetVsaNoiseCalibrationData(scpi_t *context);
    scpi_result_t SetVsaNoiseCalibrationData(scpi_t *context);
    scpi_result_t SetVsaExtendEVMStatus(scpi_t *context);
    scpi_result_t SetVsaIterativeEVMStatus(scpi_t *context);
    scpi_result_t SetVsaSncEVMStatus(scpi_t *context);
    scpi_result_t SetVsaCcEVMStatus(scpi_t *context);

    scpi_result_t SetVsaOFDMPhaseTracking(scpi_t * context);
    scpi_result_t SetVsaOFDMChannelEstimation(scpi_t * context);
    scpi_result_t SetVsaOFDMTimingTracking(scpi_t * context);
    scpi_result_t SetVsaOFDMFrequencySync(scpi_t * context);
    scpi_result_t SetVsaOFDMAmplitudeTracking(scpi_t * context);
    scpi_result_t SetVsaOFDMMimoMaxPowerDiff(scpi_t * context);
    scpi_result_t SetVsaDSSSEvmMethod(scpi_t * context);
    scpi_result_t SetVsaDSSSDCRemove(scpi_t * context);
    scpi_result_t SetVsaDSSSEqualizerTypes(scpi_t * context);
    scpi_result_t SetVsaDSSSPhaseTracking(scpi_t * context);
    scpi_result_t SetVsa11nSpectrumMaskVersion(scpi_t * context);
    scpi_result_t SetVsaAlzClockRate(scpi_t * context);
    scpi_result_t SetVsaAlzWifiMimoMode(scpi_t * context);
    scpi_result_t SetVsaAlzPSDUBits(scpi_t *context);
    scpi_result_t SetVsaAlzOFDMNonHTDuplicateBW(scpi_t *context);
    scpi_result_t SetVsaAlzOFDMPreambleAverage(scpi_t * context);
    scpi_result_t SetVsaAlzOFDMIQCompensation(scpi_t * context);
    scpi_result_t SetVsaAlzOFDMEqualizerSmoothing(scpi_t * context);
    scpi_result_t SetVsaAlzOFDMICISuppression(scpi_t * context);
    scpi_result_t SetVsaAlzOFDMLdpcDecodeIterationTimes(scpi_t * context);
    scpi_result_t SetVsaAlzSfoCompensation(scpi_t * context);
    scpi_result_t SetVsaAlzObwCalculateMethod(scpi_t * context);
    scpi_result_t SetVsaAlzMacSecurityMode(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionWEP(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionKeyTypeName(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionSSID(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionPassPhrase(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionNonce(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionPSK(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionTK(scpi_t *context);
    scpi_result_t SetVsaAlzMacDecryptionCCMPGCMPAmsduCapable(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionKeyTypeName(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionPassPhrase(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionN12(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionBK(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionTK(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionAADVersion(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionAADFrameControlHTC(scpi_t *context);
    scpi_result_t SetVsaAlzMacWAPIDecryptionAmsduCapableField(scpi_t *context);

    //framefilter
    scpi_result_t SetVsaAlzFrameFilter(scpi_t *context);

    scpi_result_t SetVsaSignalFileAsCapture_V2(scpi_t * context);

    scpi_result_t SetVsaAlzHardwareDecodeSwitch(scpi_t * context);
    scpi_result_t SetVsaAlzHMatrix(scpi_t * context);

    scpi_result_t SetVsaFlatnessCalCompensateEnable(scpi_t * context);
    scpi_result_t SetVsaIQImbCompensateEnable(scpi_t * context);
    scpi_result_t GetVsaFlatnessCalCompensate(scpi_t * context);
    scpi_result_t GetVsaIQImbCompensate(scpi_t * context);

    //查询配置
    scpi_result_t GetVsaCfgFrequency(scpi_t * context);
    scpi_result_t GetVsaCfgMaxPower(scpi_t * context);
    scpi_result_t GetVsaCfgSamplingTime(scpi_t * context);
    scpi_result_t GetVsaCfgPort(scpi_t * context);
    scpi_result_t GetVsaCfgTriggerType(scpi_t * context);
    scpi_result_t GetVsaCfgTriggerLevel(scpi_t * context);
    scpi_result_t GetVsaCfgTriggerTimeOut(scpi_t * context);
    scpi_result_t GetVsaCfgTriggerPreTime(scpi_t * context);
    scpi_result_t GetVsaCfgExtGain(scpi_t * context);
    scpi_result_t GetVsaDemod(scpi_t * context);
    scpi_result_t GetVsaTimeOutWaittingSec(scpi_t * context);
    scpi_result_t GetVsaAlzFrameIndex(scpi_t * context);
    scpi_result_t GetVsaMaxIFG(scpi_t * context);
    scpi_result_t GetVsaActualMaxPower(scpi_t * context);
    scpi_result_t GetVsaTriggerGapTime(scpi_t * context);
    scpi_result_t GetVsaTriggerEdge(scpi_t * context);
    //采集分析
    scpi_result_t SetVsaAutoRange(scpi_t * context);
    scpi_result_t SetVsaCapture(scpi_t * context);
    scpi_result_t SetVsaCaptureAsync(scpi_t *context);
    scpi_result_t GetVsaCaptureStatus(scpi_t * context);
    scpi_result_t SetVsaStopCapture(scpi_t * context);
    scpi_result_t SetVsaSignalFileAsCapture(scpi_t * context);
    scpi_result_t SetVsaAnalyzeStart(scpi_t *context);
    scpi_result_t SetSaveVsaRawIQDataEnable(scpi_t *context);
    scpi_result_t SetAgcSamplingTime(scpi_t * context);
    scpi_result_t GetAgcSamplingTime(scpi_t * context);

    //分析完查询demo为unkown的原因错误码
    scpi_result_t SetVsaAnalyzeResult(scpi_t *context);

    //CMIMO配置
    scpi_result_t SetVsaSaveCMIMORefFile(scpi_t * context);
    scpi_result_t SetVsaAnalyzeCMIMORefFile(scpi_t * context);
    scpi_result_t GetVsaAnalyzeCMIMORefFile(scpi_t * context);
    //获取分析结果
    scpi_result_t GetVsaAnalyzFpOnSymbolCntNotEnoughErr(scpi_t * context);
    scpi_result_t GetVsaBaseResult(scpi_t * context);
    scpi_result_t GetVsaCompositeBaseResult(scpi_t * context);
    scpi_result_t GetVsaMIMOStreamRealNstsIndex(scpi_t * context);
    scpi_result_t GetVsaRstSpectCarrierLeakage(scpi_t * context);
    scpi_result_t GetVsaRstSpectOBW99(scpi_t * context);
    scpi_result_t GetVsaRstSpectMaskErrorPercent(scpi_t * context);
    scpi_result_t GetVsaRstSpectPeakFrequency(scpi_t * context);
    scpi_result_t GetVsaRstSpectrumData(scpi_t * context);
    scpi_result_t GetVsaRstSpectrumMaskData(scpi_t * context);
    scpi_result_t GetVsaRstSpectMargin(scpi_t * context);
    //    scpi_result_t GetVsaRstCWFreqOffset(scpi_t * context);
    //    scpi_result_t GetVsaRstZWaveFreqOffset(scpi_t * context);
    scpi_result_t GetVsaRstRawData(scpi_t * context);
    scpi_result_t GetVsaRstPowerPeak(scpi_t * context);
    scpi_result_t GetVsaRstPowerFrame(scpi_t * context);
    scpi_result_t GetVsaRstPowerAll(scpi_t * context);
    scpi_result_t GetVsaRstFrameCount(scpi_t * context);
    scpi_result_t GetVsaRstEvmAll(scpi_t * context);
    scpi_result_t GetVsaRstEvmAllPercent(scpi_t * context);
    scpi_result_t GetVsaRstEvmPeak(scpi_t * context);
    scpi_result_t GetVsaRstEvmPeakPercent(scpi_t * context);
    scpi_result_t GetVsaRstFreqError(scpi_t * context);
    scpi_result_t GetVsaRstClkError(scpi_t * context);
    scpi_result_t GetVsaRstIQMatchAmp(scpi_t * context);
    scpi_result_t GetVsaRstIQMatchPhase(scpi_t * context);
    scpi_result_t GetVsaRstIQMatchPhaseError(scpi_t * context);
    scpi_result_t GetVsaRstDataRateMbps(scpi_t * context);
    scpi_result_t GetVsaRstRampOnTime(scpi_t * context);
    scpi_result_t GetVsaRstRampOffTime(scpi_t * context);
    scpi_result_t GetVsaRstOfdmNumberSymbols(scpi_t * context);
    scpi_result_t GetVsaRstEvmDataDb(scpi_t * context);
    scpi_result_t GetVsaRstEvmPilotDb(scpi_t * context);
    scpi_result_t GetVsaRstFlatnessPassed(scpi_t * context);
    scpi_result_t GetVsaRstOfdmFlatnessSectionValue(scpi_t * context);
    scpi_result_t GetVsaRstOfdmFlatnessSectionMargin(scpi_t * context);
    scpi_result_t GetVsaRstDsssIQOffset(scpi_t * context);
    scpi_result_t GetVsaRstDsssCarrierSuppression(scpi_t * context);
    scpi_result_t GetVsaRstPsduLength(scpi_t * context);
    scpi_result_t GetVsaRstPsduCRC(scpi_t *context);

    scpi_result_t GetVsaDataInfoDemod(scpi_t *context);
    scpi_result_t GetVsaDataInfo(scpi_t *context);
    scpi_result_t GetVsaSLECTRInfo(scpi_t *context);
    scpi_result_t GetVsaRstEvmMargin(scpi_t * context);
    scpi_result_t GetVsaRstLeagkageMargin(scpi_t * context);
    scpi_result_t GetVsaRstFreqerrMargin(scpi_t * context);
    scpi_result_t GetVsaRstClockErrMargin(scpi_t * context);
    scpi_result_t GetVsaIQDataBwv(scpi_t * context);
    scpi_result_t GetVsaRawIQDataBwv(scpi_t *context);

    //mu-mimo
    scpi_result_t GetVsaRstMuRuCnt(scpi_t *context);
    scpi_result_t GetVsaRstMuRuInfo(scpi_t * context);
    scpi_result_t GetVsaRstMuRuUserInfo(scpi_t * context);
    scpi_result_t GetVsaRstMuRuUserInfo_V2(scpi_t *context);
    //IBF
    scpi_result_t SetVsaRstIBFCalibrationChannelEstDutTX(scpi_t *context);
    scpi_result_t SetVsaRstIBFCalibrationChannelEstDutRX(scpi_t *context);
    scpi_result_t GetVsaRstIBFCalibrationResult(scpi_t *context);
    scpi_result_t GetVsaRstIBFPowerVerification(scpi_t *context);
    scpi_result_t GetVsaRstIBFCalculateChannelProfile(scpi_t *context);
    scpi_result_t GetVsaRstIBFCalculateChannelAmplitudeAngle(scpi_t *context);

    //H矩阵解析前的数据结果
    scpi_result_t GetVsaHMatInitMIMOStreamCount(scpi_t * context);
    scpi_result_t GetVsaRstHMatInitPowerPeak(scpi_t * context);
    scpi_result_t GetVsaRstHMatInitPowerFrame(scpi_t * context);
    scpi_result_t GetVsaRstHMatInitPowerAll(scpi_t * context);

    //TB
    scpi_result_t SetVsaAxTbAlzParam(scpi_t *context);  //配置tb分析参数
    scpi_result_t SetVsaAxTbAlzParamFlag(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamMUFlag(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserID(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamGILTFSize(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamNumLTF(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamLDPCExtra(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamPE(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamAFactor(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamStbc(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamDoppler(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamMidPeriodicity(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserNum(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserNSS(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserMCS(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserSegment(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserRUIndex(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserCodingType(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserDCM(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserAID(scpi_t *context);
    scpi_result_t SetVsaAxTbAlzParamUserNNstart(scpi_t *context);

    //BT part
    //配置
    scpi_result_t SetVsaBTAlzRate(scpi_t * context);
    scpi_result_t SetVsaBTAlzPacketType(scpi_t * context);
    scpi_result_t SetVsaBTAlzACPViewRangeType(scpi_t * context);
    scpi_result_t SetVsaBTAlzACPSweepTimes(scpi_t * context);
    //BLE
    scpi_result_t SetVsaBTAlzBleEnhancedMode(scpi_t * context);
    scpi_result_t SetVsaBTAlzBlePhysicalChannelPDUType(scpi_t * context);
    scpi_result_t SetVsaBTAlzBleSyncMode(scpi_t * context);
    scpi_result_t SetVsaBTAlzBleAccessAddress(scpi_t * context);
    scpi_result_t SetVsaBTAlzBleChannelIndex(scpi_t * context);

    //结果
    scpi_result_t GetVsaBTPacketType(scpi_t * context);
    scpi_result_t GetVsaBTPacketLength(scpi_t * context);
    scpi_result_t GetVsaBTPacketDataRate(scpi_t * context);
    scpi_result_t GetVsaBTPacketInitFrequencyError(scpi_t * context);

    scpi_result_t GetVsaBTFreqDrift(scpi_t * context);
    scpi_result_t GetVsaBTFreqDriftRate(scpi_t * context);
    scpi_result_t GetVsaBTMaxCarrierFreq(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF1Valid(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF1Avgrage(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF1Maximun(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2Valid(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2Avgrage(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2Maximun(scpi_t * context);
    scpi_result_t GetVsaEdrEvmValid(scpi_t * context);
    scpi_result_t GetVsaEdrEvmCarrierFreqBuf(scpi_t * context);
    scpi_result_t GetVsaEdrEvm(scpi_t * context);
    scpi_result_t GetVsaEdrEvmPeak(scpi_t * context);
    scpi_result_t GetVsaBTPowerDiff(scpi_t * context);
    scpi_result_t GetVsaBT99Pct(scpi_t * context);
    scpi_result_t GetVsaBTEDRBelowThresholdPct(scpi_t * context);
    scpi_result_t GetVsaBTOmegaI(scpi_t * context);
    scpi_result_t GetVsaBTOmegaO(scpi_t * context);
    scpi_result_t GetVsaBTOmegaIO(scpi_t * context);
    scpi_result_t GetVsaBTBw20dbPassed(scpi_t * context);
    scpi_result_t GetVsaBTBw20dbSpectrum(scpi_t * context);
    scpi_result_t GetVsaBTBw20dbOBWandRBW(scpi_t * context);
    scpi_result_t GetVsaBLEDriftDetailValid(scpi_t * context);
    scpi_result_t GetVsaBLEFnMaximun(scpi_t * context);
    scpi_result_t GetVsaBLEF0FnMaximun(scpi_t * context);
    scpi_result_t GetVsaBLEDeltaF1F0(scpi_t * context);
    scpi_result_t GetVsaBLEDeltaFnFn5Maximun(scpi_t * context);
    scpi_result_t GetVsaBLEDeltaF0F3(scpi_t * context);
    scpi_result_t GetVsaBLEDeltaF0Fn3(scpi_t * context);
    scpi_result_t GetVsaBTSpectAcp(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2PassPercent(scpi_t * context);
    scpi_result_t GetVsaBTFreqOffsetHeader(scpi_t * context);
    scpi_result_t GetVsaBTFreqOffsetSync(scpi_t * context);
    scpi_result_t GetVsaBTFreqDeviation(scpi_t * context);
    scpi_result_t GetVsaBTFreqDeviationPeaktoPeak(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2AvAccess(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2MaxAccess(scpi_t * context);
    scpi_result_t GetVsaBTSpectAcpMask(scpi_t * context);
    scpi_result_t GetVsaBTEdrEvmVsTime(scpi_t * context);
    scpi_result_t GetVsaBTFrequencyError(scpi_t * context);
    scpi_result_t GetVsaBTBw20dbSpectrumArbData(scpi_t * context);
    scpi_result_t GetVsaBTSpectAcpArbData(scpi_t * context);
    scpi_result_t GetVsaBTSpectAcpMaskArbData(scpi_t * context);
    scpi_result_t GetVsaBTEdrEvmVsTimeArbData(scpi_t * context);
    scpi_result_t GetVsaBTFrequencyErrorArbData(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF1MINimun(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF2MINimun(scpi_t * context);
    scpi_result_t GetVsaBTMaxFreqVar(scpi_t * context);
    scpi_result_t GetVsaBLEF0FnAvg(scpi_t * context);

    scpi_result_t GetVsaBTBREDRLAP(scpi_t * context);
    scpi_result_t GetVsaBTBREDRUAP(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEInfo(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEType(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEDurationTime(scpi_t * context);
    scpi_result_t GetVsaBTLT_ADDR(scpi_t * context);
    scpi_result_t GetVsaBTFlowCtrl(scpi_t * context);
    scpi_result_t GetVsaBTACKIndication(scpi_t * context);
    scpi_result_t GetVsaBTSeqnum(scpi_t * context);
    scpi_result_t GetVsaBTLLID(scpi_t * context);
    scpi_result_t GetVsaBTFlow(scpi_t * context);
    scpi_result_t GetVsaBTPayloadsize(scpi_t * context);
    scpi_result_t GetVsaBTEIR(scpi_t * context);
    scpi_result_t GetVsaBTSR(scpi_t * context);
    scpi_result_t GetVsaBTClassOfDevice(scpi_t * context);
    scpi_result_t GetVsaBTLtaddr(scpi_t * context);
    scpi_result_t GetVsaBTClk27b2(scpi_t * context);
    scpi_result_t GetVsaBTBLEMappers(scpi_t * context);
    scpi_result_t GetVsaBTVoiceField(scpi_t * context);
    scpi_result_t GetVsaBTPayloadHeader(scpi_t * context);
    scpi_result_t GetVsaBTPattern(scpi_t * context);
    scpi_result_t GetVsaBTEDRGFSKPower(scpi_t * context);
    scpi_result_t GetVsaBTEDRDPSKPower(scpi_t * context);
    scpi_result_t GetVsaBTEDRGFSKPowerPeak(scpi_t * context);
    scpi_result_t GetVsaBTEDRDPSKPowerPeak(scpi_t * context);
    scpi_result_t GetVsaBTEDRGuardtime(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrAvg(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrPeak(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrSubAvg(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEFsiMax(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEFsiMin(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEFs1SubFp(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEFsiSubF0Max(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEFsiSubFsi3Max(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrRefAvg(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrRefDev(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrRefDevMax(scpi_t * context);
    scpi_result_t GetVsaBTBLECTEPwrAvgSlot(scpi_t * context);
    scpi_result_t GetVsaBTBREDRHeaderBin(scpi_t * context);

    scpi_result_t GetVsaBTEDRSynSeqErrorBitNumber(scpi_t * context);
    scpi_result_t GetVsaBTEDRTrailerErrorBitNumber(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF199p9Precent(scpi_t * context);
    scpi_result_t GetVsaBTDeltaF299p9Precent(scpi_t * context);
    scpi_result_t GetVsaBTPacketPayloadHeaderBits(scpi_t * context);
    scpi_result_t GetVsaBTPsduBits(scpi_t * context);
    scpi_result_t GetVsaBTPsduCRC(scpi_t * context);

    scpi_result_t GetVsaBLEEnhancedMode(scpi_t * context);
    scpi_result_t GetVsaBTLeakagePower(scpi_t * context);
    scpi_result_t GetVsaBTDifferencebetweenPowerPeakAndAverage(scpi_t * context);
    scpi_result_t GetVsaBLEDeltaF2AvgDivDeltaF1Avg(scpi_t * context);
    scpi_result_t GetVsaBTEDRPhaseDifferenceArbData(scpi_t * context);
    scpi_result_t GetVsaBTBrBLEFrequencyDeviationArbData(scpi_t * context);
    scpi_result_t GetVsaBTBw20dbSpectrumFrequencyLow(scpi_t * context);
    scpi_result_t GetVsaBTBw20dbSpectrumFrequencyHigh(scpi_t * context);

    scpi_result_t GetVsaBTInfo(scpi_t * context);   //get bt all bt info view result
    //GPRF
    scpi_result_t SetVsaAlzRBW(scpi_t * context);

    scpi_result_t GetVsaRstFrequencyOffset(scpi_t * context);
    scpi_result_t GetVsaRstLTFSNR(scpi_t * context);
    scpi_result_t GetVsaRstPSDUSNR(scpi_t * context);

    //8080
    scpi_result_t SetVsa8080Mode(scpi_t * context);
    scpi_result_t SetVsa8080Port(scpi_t * context);
    scpi_result_t SetVsa8080RefPower(scpi_t * context);
    scpi_result_t SetVsa8080TriggerLevel(scpi_t * context);

    scpi_result_t GetVsa8080Mode(scpi_t * context);
    scpi_result_t GetVsa8080Port(scpi_t * context);
    scpi_result_t GetVsa8080RefPower(scpi_t * context);
    scpi_result_t GetVsa8080TriggerLevel(scpi_t * context);

    //ZigBee
    scpi_result_t SetVsaZigbeeAnalyzeOptimise(scpi_t * context);
    scpi_result_t GetVsaZigbeeEvmPsdu(scpi_t * context);
    scpi_result_t GetVsaZigbeeEvmPsduPercent(scpi_t * context);
    scpi_result_t GetVsaZigbeeEvmShrPhr(scpi_t * context);
    scpi_result_t GetVsaZigbeeEvmShrPhrPercent(scpi_t * context);
    scpi_result_t GetVsaZigbeeEvmOffset(scpi_t * context);
    scpi_result_t GetVsaZigbeeEvmOffsetPercent(scpi_t * context);
    //zigbee graphic data
    scpi_result_t GetVsaZigbeeEyeRealGraphic(scpi_t * context);
    scpi_result_t GetVsaZigbeeEyeImagGraphic(scpi_t * context);
    scpi_result_t GetVsaZigbeePhaseErrorVsChip(scpi_t * context);

    //sle
    //配置
    scpi_result_t SetVsaGleAlzFrameType(scpi_t * context);
    scpi_result_t SetVsaGleAlzBandWidth(scpi_t * context);
    scpi_result_t SetVsaGleAlzCtrlinfoType(scpi_t * context);
    scpi_result_t SetVsaGleAlzPID(scpi_t * context);
    scpi_result_t SetVsaGleAlzPayloadCrcType(scpi_t * context);
    scpi_result_t SetVsaGleAlzPayloadCrcSeed(scpi_t * context);
    scpi_result_t SetVsaGleAlzPilotDensity(scpi_t * context);
    scpi_result_t SetVsaGleAlzPolarEncodePathNum(scpi_t * context);
    scpi_result_t SetVsaGleAlzScramble(scpi_t * context);
    scpi_result_t SetVsaGleAlzChannelType(scpi_t * context);
    scpi_result_t SetVsaGleAlzFreqRange(scpi_t * context);
    scpi_result_t SetVsaGleAlzBroadcastChannelNo(scpi_t * context);
    scpi_result_t SetVsaGleAlzSlotIndex(scpi_t * context);
    scpi_result_t SetVsaGleAlzSyncMode(scpi_t * context);
    scpi_result_t SetVsaGleAlzMSequenceNum(scpi_t * context);
    scpi_result_t SetVsaGleAlzAccessCode(scpi_t * context);
    scpi_result_t SetVsaGleAlzRRCFilter(scpi_t * context);
    scpi_result_t SetVsaGleAlzPayloadAlzMode(scpi_t * context);
    scpi_result_t SetVsaGleAlzMCS(scpi_t * context);

    //结果
    scpi_result_t GetVsaGleCtrlInfoRstSymoblConst(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstSymoblConstARB(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstSymoblPilotConst(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstSymoblPilotConstARB(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstSymoblRefConst(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstSymoblRefConstARB(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstEVMTime(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoRstEVMTimeARB(scpi_t * context);

    scpi_result_t GetVsaGleAcpData(scpi_t * context);
    scpi_result_t GetVsaGleAcpMask(scpi_t * context);
    scpi_result_t GetVsaGleAcpDataARB(scpi_t * context);
    scpi_result_t GetVsaGleAcpMaskARB(scpi_t * context);
    scpi_result_t GetVsaGleEyePointARB(scpi_t * context);
    scpi_result_t GetVsaGleEyePoint(scpi_t * context);
    scpi_result_t GetVsaGleFrameType(scpi_t * context);
    scpi_result_t GetVsaGleBandWidth(scpi_t * context);
    scpi_result_t GetVsaGlePID(scpi_t * context);
    scpi_result_t GetVsaGlePayloadLen(scpi_t * context);
    scpi_result_t GetVsaGlePayloadCrcType(scpi_t * context);
    scpi_result_t GetVsaGlePayloadCrc(scpi_t * context);
    scpi_result_t GetVsaGleSyncSignal(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfo(scpi_t * context);
    scpi_result_t GetVsaGlePayloadBin(scpi_t * context);
    //scpi_result_t GetVsaGleMinFreqErr(scpi_t * context);
    scpi_result_t GetVsaGleDeltaFd1Avg(scpi_t * context);
    scpi_result_t GetVsaGleDeltaFd1Max(scpi_t * context);
    scpi_result_t GetVsaGleDeltaFd1Min(scpi_t * context);
    scpi_result_t GetVsaGleDeltaFd2Avg(scpi_t * context);
    scpi_result_t GetVsaGleDeltaFd2Min(scpi_t * context);
    scpi_result_t GetVsaGleZeroCrossingErr(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoEvmAvg(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoEvmPeak(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoEvm99PCT(scpi_t * context);
    scpi_result_t GetVsaGleEvmAvg(scpi_t * context);
    scpi_result_t GetVsaGleEvmPeak(scpi_t * context);
    scpi_result_t GetVsaGleEvm99PCT(scpi_t * context);
    scpi_result_t GetVsaGleInitFreqErr(scpi_t * context);
    scpi_result_t GetVsaGleFreqDrift(scpi_t * context);
    scpi_result_t GetVsaGleFreqDriftRate(scpi_t * context);
    scpi_result_t GetVsaGleSymbolClkErr(scpi_t * context);
    scpi_result_t GetVsaGleMaxTimeDev(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoType(scpi_t * context);
    scpi_result_t GetVsaGleCtrlInfoCRC(scpi_t * context);

    //WiSUN 分析配置
    scpi_result_t SetVsaWiSUNAlzOFDMOption(scpi_t * context);
    scpi_result_t SetVsaWiSUNAlzPhyOFDMInterLeaving(scpi_t * context);
    scpi_result_t SetVsaWiSUNAlzOQPSKFreqBand(scpi_t * context);
    scpi_result_t SetVsaWiSUNAlzFSKDataRate(scpi_t * context);
    scpi_result_t SetVsaWiSUNAlzFSKAcpCalMode(scpi_t * context);
    scpi_result_t SetVsaWiSUNAlzFSKChannelSpacing(scpi_t * context);
    scpi_result_t SetVsaWiSUNAlzFSKModulationIndex(scpi_t * context);

    //WiSUN 结果
    scpi_result_t GetVsaWiSUNPHRCRCCheck(scpi_t * context);
    scpi_result_t GetVsaWiSUNPHRRateField(scpi_t * context);
    scpi_result_t GetVsaWiSUNPHRFrameLength(scpi_t * context);
    scpi_result_t GetVsaWiSUNPHRScrambler(scpi_t * context);
    scpi_result_t GetVsaWiSUNPHRBit(scpi_t * context);
    scpi_result_t GetVsaWiSUNPHRCRCBit(scpi_t * context);
    scpi_result_t GetVsaWiSUNDataRate(scpi_t * context);
    scpi_result_t GetVsaWiSUNBW(scpi_t * context);
    scpi_result_t GetVsaWiSUNSymbolCount(scpi_t * context);
    scpi_result_t GetVsaWiSUNCodingRate(scpi_t * context);
    scpi_result_t GetVsaWiSUNModulationType(scpi_t * context);
    scpi_result_t GetVsaWiSUNPSDULength(scpi_t * context);
    scpi_result_t GetVsaWiSUNPSDUCRC(scpi_t * context);
    scpi_result_t GetVsaWiSUNFSKEyePoint(scpi_t * context);
    scpi_result_t GetVsaWiSUNFSKShrSfdBits(scpi_t * context);
    scpi_result_t GetVsaWiSUNFSKPhrBits(scpi_t * context);
    scpi_result_t GetVsaWiSUNFSKPhyPayloadBits(scpi_t * context);
    scpi_result_t GetVsaWiSUNFSKCrcBits(scpi_t * context);
    scpi_result_t GetVsaWiSUNAcpPwrData(scpi_t * context);
    scpi_result_t GetVsaWiSUNAcpChannelData(scpi_t * context);
    scpi_result_t GetVsaWiSUNAcpMask(scpi_t * context);
    scpi_result_t GetVsaWiSUNAcpWidth(scpi_t * context);
    scpi_result_t GetVsaWiSUNAcpValidnum(scpi_t * context);
    scpi_result_t GetVsaWiSUNPhaseErrorVsChip(scpi_t * context);
    scpi_result_t GetVsaWiSUNEyeRealGraphic(scpi_t * context);
    scpi_result_t GetVsaWiSUNEyeImagGraphic(scpi_t * context);
    scpi_result_t GetVsaWiSUNPSDUBit(scpi_t * context);
    scpi_result_t GetVsaWiSUNPSDUBitLength(scpi_t * context);


    //ZWAVE 分析参数
    scpi_result_t GetVsaZWAVeDataRate(scpi_t * context);

    //ZWAVE 结果
    scpi_result_t GetVsaZWAVeEyeGraphic(scpi_t * context);

    //文件处理
    scpi_result_t CheckWaveFileExist(scpi_t *context);
    scpi_result_t DelWaveFile(scpi_t *context);

    scpi_result_t GetVsaRstFrameLocation(scpi_t *context);
    scpi_result_t GetVsaRstPointPower(scpi_t *context);
    scpi_result_t GetVsaRstPointAvgPower(scpi_t *context);
    scpi_result_t GetVsaRstPointIQ(scpi_t *context);

    scpi_result_t GetVsaRstPointPowerLite(scpi_t *context);
    scpi_result_t GetVsaRstPointAvgPowerLite(scpi_t *context);
    scpi_result_t GetVsaRstPointIQLite(scpi_t *context);

    scpi_result_t GetVsaRstSpectrumDataARB(scpi_t *context);
    scpi_result_t GetVsaRstSpectrumMaskDataARB(scpi_t *context);
    scpi_result_t GetVsaRstSymoblConstARB(scpi_t *context);
    scpi_result_t GetVsaRstSymoblPilotConstARB(scpi_t *context);
    scpi_result_t GetVsaRstSymoblRefConstARB(scpi_t *context);
    scpi_result_t GetVsaRstCarrierSymoblEvmARB(scpi_t *context);
    scpi_result_t GetVsaRstCarrierSymoblEvmARB_AVG(scpi_t *context);
    scpi_result_t GetVsaRstPilotCarrierSymoblEvmARB(scpi_t *context);
    scpi_result_t GetVsaRstPilotCarrierSymoblEvmARB_AVG(scpi_t *context);
    scpi_result_t GetVsaRstSymoblEVM(scpi_t *context);
    scpi_result_t GetVsaRstSymoblEVM_AVG(scpi_t *context);
    scpi_result_t GetVsaRstSymoblEVMPilot(scpi_t *context);
    scpi_result_t GetVsaRstFlatnessData(scpi_t *context);
    scpi_result_t GetVsaRstFlatnessMaskUp(scpi_t *context);
    scpi_result_t GetVsaRstFlatnessMaskDown(scpi_t *context);
    scpi_result_t GetVsaRstFlatnessSection(scpi_t *context);
    scpi_result_t GetVsaRstFlatnessSectionMargin(scpi_t *context);
    scpi_result_t GetAlzAdcSMPFreq(scpi_t *context);

    scpi_result_t GetVsaRstSpectrumSpan(scpi_t *context);
    scpi_result_t GetVsaRstSpectrumCenterFreq(scpi_t *context);
    scpi_result_t GetVsaRstSpectrumRBW(scpi_t *context);
    scpi_result_t GetVsaRstSpectrumOBW(scpi_t *context);
    scpi_result_t GetVsaRstImbalanceCal(scpi_t *context);
    scpi_result_t SetVsaImbalanceCal(scpi_t *context);

    scpi_result_t GetVsaRstCCDFProb(scpi_t *context);
    scpi_result_t GetVsaRstCCDFPowerRef(scpi_t *context);
    scpi_result_t GetVsaRstCCDFStart(scpi_t *context);
    scpi_result_t GetVsaRstCCDFScale(scpi_t *context);
    scpi_result_t GetVsaRstCCDFPercentPower(scpi_t *context);

    scpi_result_t GetVsaRst11b_FreqerrMargin(scpi_t *context);
    scpi_result_t GetVsaRst11b_ClockErrMargin(scpi_t *context);
    scpi_result_t GetVsaRst11b_EVMTime(scpi_t *context);
    scpi_result_t GetVsaRst11b_EVMTimeAVG(scpi_t *context);
    scpi_result_t GetVsaRst11b_EVMTimeChip(scpi_t *context);
    scpi_result_t GetVsaRst11b_EyeGraphic(scpi_t *context);
    scpi_result_t GetVsaRst11b_RampOnPower(scpi_t *context);
    scpi_result_t GetVsaRst11b_RampOnPowerPeak(scpi_t *context);
    scpi_result_t GetVsaRst11b_RampOnMask(scpi_t *context);
    scpi_result_t GetVsaRst11b_RampOffPower(scpi_t *context);
    scpi_result_t GetVsaRst11b_RampOffPowerPeak(scpi_t *context);
    scpi_result_t GetVsaRst11b_RampOffMask(scpi_t *context);
    scpi_result_t GetVsaRst11b_PhaseError(scpi_t *context);
    scpi_result_t GetVsaRst11b_BitError(scpi_t *context);
    scpi_result_t GetVsaRst11b_PreambleFreqError(scpi_t *context);
    scpi_result_t GetVsaRst11b_LO_RBW(scpi_t *context);
    scpi_result_t GetVsaRst11b_LO_Span(scpi_t *context);
    scpi_result_t GetVsaRst11b_LO_Value(scpi_t *context);
    scpi_result_t GetVsaRst11b_FreqErrVsTime(scpi_t *context);
    scpi_result_t GetVsaRstOFDM_PreambleFreqErr(scpi_t *context);
    scpi_result_t GetVsaRstOFDM_PreambleFreqErrValid(scpi_t *context);

    scpi_result_t GetVsaMIMOStreamCount(scpi_t *context);

    scpi_result_t GetVsaRstSIGBSymoblConstARB(scpi_t *context);
    scpi_result_t GetVsaRstSIGBSymoblPilotConstARB(scpi_t *context);
    scpi_result_t GetVsaRstSIGBSymoblRefConstARB(scpi_t *context);

    scpi_result_t GetVsaRstAllUserSymoblConstARB(scpi_t *context);
    scpi_result_t GetVsaRstAllUserSymoblPilotConstARB(scpi_t *context);
    scpi_result_t GetVsaRstAllUserSymoblRefConstARB(scpi_t *context);

    scpi_result_t GetVsaRstMUAllUserSymoblConstARB(scpi_t *context);
    scpi_result_t GetVsaRstMUAllUserSymoblPilotConstARB(scpi_t *context);
    scpi_result_t GetVsaRstMUAllUserSymoblRefConstARB(scpi_t *context);

    scpi_result_t GetVsaFile2TBParam(scpi_t *context);
    scpi_result_t GetVsaFile2TBParam_Json(scpi_t *context);
    scpi_result_t GetVsaRst_TBUnusedToneErr(scpi_t *context);

    scpi_result_t GetVsaFile2SLEParam(scpi_t *context);

    //获取结果相关参考标准值
    scpi_result_t GetVsaRstSpecification(scpi_t *context);

    scpi_result_t GetVsaRstChannelPhaseResponse(scpi_t *context);
    scpi_result_t GetVsaRstChannelAmplitudeResponse(scpi_t *context);
    scpi_result_t GetVsaRstSymbolPhaseError(scpi_t *context);
    scpi_result_t GetVsaRstSymbolAmplitude(scpi_t *context);

    scpi_result_t GetVsaRstMimoPowerTable(scpi_t *context);    //power table

    //平均相关的内容
    scpi_result_t SetAverageSetting(scpi_t *context);
    scpi_result_t GetAverageBaseResult(scpi_t *context);
    scpi_result_t GetAverageResultARB(scpi_t *context);
    scpi_result_t SetAverageMinCount(scpi_t *context);
    scpi_result_t GetCurAverageCount(scpi_t *context);
    scpi_result_t GetMimoAverageVompositeResult(scpi_t *context);
    scpi_result_t CleanAverage(scpi_t *context);
    scpi_result_t GetSLEAverageResult(scpi_t *context);
    scpi_result_t GetSLEAverageResultARB(scpi_t *context);
    scpi_result_t GetBTAverageResult(scpi_t *context);
    scpi_result_t GetBTAverageResultARB(scpi_t *context);
    scpi_result_t Get3GPPAverageResultARB(scpi_t *context);
    scpi_result_t Get3GPPAverageResult(scpi_t *context);
    scpi_result_t Set3GPPAverageSetting(scpi_t *context);

    //特殊的接口，模拟旧api接口使用
    scpi_result_t GetVectorResult(scpi_t *context);
    scpi_result_t GetVectorResultElementCount(scpi_t *context);
    scpi_result_t GetVectorResultElementSize(scpi_t *context);

    //自动线衰修正
    scpi_result_t SetVSAAutoPowerCorrect(scpi_t *context);

    scpi_result_t GetVsaRstSpectrumPointPower(scpi_t *context);

    // 3GPP获取结果
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumEmissionDataARB(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumEmissionMaskDataARB(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumEmissionMaskSegDataARB(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumBadPointcnt(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumFreqStart(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumFreqCenter(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumFreqEnd(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSymoblConstARB(scpi_t *context);    
    scpi_result_t SCPI_3GPP_GetVsaRstSymoblPilotConstARB(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSummaryLTE(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSummaryNR(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSummaryNRUL(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSummaryNIOT(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPBCHSSBInfo(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstEvmRms(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstEvmPeak(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstEvmDmrs(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPkgAvgPwrdBm(scpi_t *context);    
    scpi_result_t SCPI_3GPP_GetVsaRstPkgPeakPwrdBm(scpi_t *context);    
    scpi_result_t SCPI_3GPP_GetVsaRstFrmAvgPwrdBm(scpi_t *context);    
    scpi_result_t SCPI_3GPP_GetVsaRstFrmPeakPwrdBm(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstEutraResult(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstNrUtraResult(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstUtraResult(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSEMResult(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstInEmisResult(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSubcarrierAvgPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstReferenceSignalReceivingPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstReceivedSignalStrengthIndication(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstReferenceSignalReceivingQuality(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSignaltoNoiseRatio(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstLinkDirect(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstChannel(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSlotIdx(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstCodeword(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCwModulate(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstCwScrambling(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstCwChannelCodingType(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstCwCrc(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFormat(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstHarqAckInfo(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstBitSeq(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstEvmMaxCarrierLen(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSSBNum(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstEvmCarrierEvm(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSymoblEVM(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPilotSymoblEVM(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessMaxFlatNum(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessFlatRes(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessFlatMaskUp(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessFlatMaskDown(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessRange(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessRipple(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFlatnessCrossRipple(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstInEmisEmission(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstInEmisEmisRef(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstInEmisEmisRefSeg(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstInEmisMarginMin(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstInEmisMarginMinIdx(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraFreq(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraAclRatio(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraFreq(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraAclRatio(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraFreq(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraAclRatio(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrMagnErr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrErrRms(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrMagnErrPeak(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrMagnErrDmrs(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrs(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrRms(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrPeak(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrDmrs(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstModulationSymbolsEvm(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstUEPwrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPhaseDiffOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstDynamicLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstUpperLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMaxPhaseDis(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMinSlotDis(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstExceedUpperLimitNum(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstExceedDynamicLimitNum(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFreqErrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFreqErrLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstFreqErr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMagnErrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMagnErrLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaMagnitudeErrorMagnErrlimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaMagnitudeErrorVsChipMagnErrChip(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaMagnitudeErrorVsChipMagnErrChiplimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaPhaseErrorVsChipPhaseErrChip(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaPhaseErrorVsChipPhaseErrChiplimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaEvmOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaEvmLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaEvmChip(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaEvmChipLimit(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPCCHPwrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPDCHPwrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotHSDPCCHPwrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPCCHPwr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPDCHPwr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotHSDPCCHPwr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPCCHErrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPDCHErrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEHSDPCCHErrOut(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPCCHErr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPDCHErr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEHSDPCCHErr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPCCHsf(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPDCHsf(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRelativeCDEHSDPCCHsf(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDMIQSignalCodeNum(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPISignalIMonitorval(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDPQSignalIMonitorval(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDEISignalIMonitorval(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaCDEQSignalIMonitorval(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaUEPowerPwr(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaIQOffset(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaPhaseDisPhaseDiff(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSummaryWCDMA(scpi_t *context);
    // GSM
    scpi_result_t SCPI_3GPP_GetVsaRstPowerSymbol(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstUpMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstDownMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstXMark(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstAveragePower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstTscType(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstType(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasurePartMin(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasurePartMax(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasureSV(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationFrequency(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationSymbol(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationTime(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchFrequency(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchPower(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchMask(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchSymbol(scpi_t *context);
    scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchTime(scpi_t *context);

    scpi_result_t SCPI_3GPP_GetVsaRstSummaryGSM(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif //VSAHANDLER_H_
