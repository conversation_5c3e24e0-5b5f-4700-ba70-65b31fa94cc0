#include "scpi_3gpp_alz_lte.h"

#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_common.h"
#include "scpi_3gpp_base.h"
#include "json_convert.h"
#include "wtlog.h"

using namespace cellular;
namespace c = cellular;

#define SHOW_3GPP_LTE_ALZ_PARAM_VALUE (1)

namespace {
    // 定义各制式支持的频率值(单位:Hz)
    constexpr int WCDMA_FREQS[] = {2000};
    constexpr int NR_FREQS[] = {15000, 30000, 60000, 120000};
    constexpr int NBIOT_FREQS[] = {3750, 15000, 30000};

    // 检查LTE频率值是否有效
    bool isValidLTEFreq(int freq) {
        // 检查频率是否在范围内且为10的倍数
        return (freq >= 1000 && freq <= 500000) && (freq % 10 == 0);
    }

    // 检查值是否在给定数组中
    bool isValueInArray(int value, const int arr[], size_t size) {
        return std::find(arr, arr + size, value) != arr + size;
    }

    // 制式与频率映射结构 
    struct StandardFreqs {
        std::function<bool(int)> validator;
    };

    // 制式到频率的映射表
    const std::map<int, StandardFreqs> STANDARD_FREQ_MAP = {
        {ALG_3GPP_STD_NB_IOT, {isValidLTEFreq}},
        {ALG_3GPP_STD_4G, {isValidLTEFreq}},
        {ALG_3GPP_STD_5G, {[](int f) { return isValueInArray(f, NR_FREQS, sizeof(NR_FREQS)/sizeof(int)); }}},
        {ALG_3GPP_STD_WCDMA, {[](int f) { return isValueInArray(f, WCDMA_FREQS, sizeof(WCDMA_FREQS)/sizeof(int)); }}}
    };
}

static inline Alg_3GPP_AlzIn4g &Lte(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->vsaAlzParam.analyzeParam3GPP.LTE;
}

static int BinaryFind(int array[], int len, int target)
{
    int left = 0;
    int right = len - 1;
    while (left <= right)
    {
        int mid = (left + right) / 2;

        if (array[mid] == target)
        {
            return mid;
        }
        else if (array[mid] > target)
        {
            right = mid - 1;
        }
        else
        {
            left = mid + 1;
        }
    }

    return -1;
}

bool endsWith(std::string const &str, std::string const &suffix)
{
    if (str.length() < suffix.length())
    {
        return false;
    }
    return str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

bool is_3gpp_wave_ref_match_channel_type(int LinkDirect, int ChannType)
{
    bool match = false;
    if (LinkDirect == ALG_3GPP_UL)
    {
        switch (ChannType)
        {
        case ALG_4G_PUSCH:
        case ALG_4G_PUCCH:
            match = true;
            break;
        default:
            break;
        }
    }
    else
    {
        switch (ChannType)
        {
        case ALG_4G_PDSCH:
        case ALG_4G_PDCCH:
            match = true;
            break;
        default:
            break;
        }
    }
    return match;
}

// static void GetAlzParamFromRef3GPP(AlzParam3GPP &analyzeParam3GPP, SPCIUserParam::RefAlzConfig3GPP &RefAnalyzeParam3GPP, int subframe, int chanType)
// {
//     // 设置基本参数
//     analyzeParam3GPP.Standard = RefAnalyzeParam3GPP.Standard;

//     if (RefAnalyzeParam3GPP.IsLoadRef == 0)
//     {
//         RefAnalyzeParam3GPP.IsUseRef = 0;
//     }
//     else
//     {
//         if (is_3gpp_wave_ref_match_channel_type(RefAnalyzeParam3GPP.LinkDirect, chanType))
//         {
//             RefAnalyzeParam3GPP.IsUseRef = 1;
//         }
//         else
//         {
//             RefAnalyzeParam3GPP.IsUseRef = 0;
//         }
//     }

//     if (RefAnalyzeParam3GPP.IsUseRef == 0)
//     {
//         SCPI_AlzParam vsaAlzParam;
//         vsaAlzParam.Reset_AlzParam_3gpp(&analyzeParam3GPP, chanType);
//     }
//     else
//     {
//         WTLog::Instance().WriteLog(LOG_DEBUG, "Get AlzParam subframe = %d chanType = %d\n", subframe, chanType);
//         memcpy(&analyzeParam3GPP.LTE, &RefAnalyzeParam3GPP.RefAlg_3GPP_AlzIn4g[subframe], sizeof(RefAnalyzeParam3GPP.RefAlg_3GPP_AlzIn4g[subframe]));
//     }
// }

static void CopyAlzParam3GPP(AlzParam3GPP_SCPI *d, const AlzParam3GPP *s)
{
    d->Version = 1;
    d->analyzeGroup = s->analyzeGroup;
    d->DcFreqCompensate = s->DcFreqCompensate;
    d->Standard = s->Standard;
    d->SpectrumRBW = s->SpectrumRBW;
    memcpy(d->rf_band, s->rf_band, sizeof(d->rf_band));
    memcpy(d->rf_channel, s->rf_channel, sizeof(d->rf_channel));

    switch (d->Standard)
    {
    case ALG_3GPP_STD_4G:
        memcpy(&d->Param.LTE, &s->LTE, sizeof(d->Param.LTE));
        break;
    case ALG_3GPP_STD_5G:
        memcpy(&d->Param.NR, &s->NR, sizeof(d->Param.NR));
        break;
    case ALG_3GPP_STD_NB_IOT:
        memcpy(&d->Param.NBIOT, &s->NBIOT, sizeof(d->Param.NBIOT));
        break;
    case ALG_3GPP_STD_WCDMA:
        memcpy(&d->Param.WCDMA, &s->WCDMA, sizeof(d->Param.WCDMA));
        break;
    default:
        break;
    }
}

scpi_result_t SCPI_3GPP_GetAnalyParamJson(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (attr->vsaAlzParam.analyzeParam3GPP.ErrorCode != WT_ERR_CODE_OK)
        {
            iRet = attr->vsaAlzParam.analyzeParam3GPP.ErrorCode + WT_ALG_3GPP_BASE_ERROR;
            break;
        }

        nlohmann::json root;
        if (IsAlg3GPPStandardType(attr->vsaParam.Demode))
        {
            cellular_param_to_json(root, attr->vsaAlzParam.analyzeParam3GPP);
        }

        string strJson = root.dump();

        WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI_3GPP_GetAnalyParamJson:\n%s\n", strJson.c_str());

        SCPI_ResultArbitraryBlock(context, strJson.c_str(), strJson.length());
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_GetAnalyParamArb(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Version = 1;
    // int SubFrame = 0;
    // int ChannType = 0;

    // verison, 分析结构体参数的版本号

    // <subfream> <ChannType>
    // -1, 取当前分析配置

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamInt(context, &Version, true))
        {
            //    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            //    break;
        }

        // SCPI_ParamInt(context, &ChannType, false);
        // WTLog::Instance().WriteLog(LOG_DEBUG, "SubFrame=%d ChannType=%d\n", SubFrame, ChannType);
        // if (SubFrame == -1)
        // {
        //     // TODO 是否需要判断协议类型?
        //     // SCPI_ResultArbitraryBlock(context, (const char *)&(attr->vsaAlzParam.analyzeParam3GPP), sizeof(attr->vsaAlzParam.analyzeParam3GPP));
        // }
        // else
        // {
        //     GetAlzParamFromRef3GPP(attr->vsaAlzParam.analyzeParam3GPP, attr->m_RefAnalyzeParam3GPP, SubFrame, ChannType);
        //     // SCPI_ResultArbitraryBlock(context, (const char *)&(attr->vsaAlzParam.analyzeParam3GPP), sizeof(attr->vsaAlzParam.analyzeParam3GPP));
        // }

        if (IsAlg3GPPStandardType(attr->vsaParam.Demode))
        {
            AlzParam3GPP_SCPI *pParam = new AlzParam3GPP_SCPI();
            bzero(pParam, sizeof(AlzParam3GPP_SCPI));

            CopyAlzParam3GPP(pParam, &(attr->vsaAlzParam.analyzeParam3GPP));

            // PrintAlg_3GPP_AlzInNBIOT(&(pParam->Param.NBIOT));

            SCPI_ResultArbitraryBlock(context, (const char *)pParam, sizeof(AlzParam3GPP_SCPI));

            delete pParam;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

// 简单加密
inline unsigned char SimpleEncryp(unsigned char c)
{
    return ~(((c & 0x93) & 0x93) | (c & 0x6c));
}

scpi_result_t SCPI_3GPP_SetAnalyParamRefLoadArb(scpi_t *context)
{
#define PAYLOAD_3GPP_PARAM_OFFSET ((4) * sizeof(int))
    int iRet = WT_ERR_CODE_OK;
    const char *data = nullptr;
    size_t len = 0;

    if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
    {
        return SCPI_RES_ERR;
    }

    do
    {
        if (len < PAYLOAD_3GPP_PARAM_OFFSET)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        // 文件头部对比, 结构 Header1,Header2,Demode,Length,Payload
        const int *Header = (const int *)data;
        const int Demode = Header[2];
        const int Length = Header[3];

        if (Length <= 0)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        // 数据完整性
        // WTLog::Instance().WriteLog(LOG_DEBUG, "ARB len=%lu Payload Length=%d Total=%lu\n", len, Length, Length + PAYLOAD_3GPP_PARAM_OFFSET);
        if ((Length + PAYLOAD_3GPP_PARAM_OFFSET) != len)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (Header[0] != WAVE_CFG_BASE_HEAD)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Demode != attr->vsaAlzParam.analyzeParam3GPP.Standard)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Demode do not match! Set Demode=%d Cur Demode=%d\n", Demode, attr->vsaAlzParam.analyzeParam3GPP.Standard);
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (is3GPPWaveCfgHead(Header[1]))
        {
#if 1
            // 解密, 注意必须与API加密过程匹配使用
            unsigned char *Payload = (unsigned char *)(data + PAYLOAD_3GPP_PARAM_OFFSET);
            for (int i = 0; i < Length; ++i)
            {
                *(Payload + i) = SimpleEncryp(*(Payload + i));
            }
#endif

            WTLog::Instance().WriteLog(LOG_DEBUG, "set 3gpp param from json\n%s\n", data + PAYLOAD_3GPP_PARAM_OFFSET);

            nlohmann::json JsonRoot;
            JsonRoot = nlohmann::json::parse(data + PAYLOAD_3GPP_PARAM_OFFSET, data + PAYLOAD_3GPP_PARAM_OFFSET + Length);
            if (JsonRoot.empty())
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "set 3gpp param from json JsonRoot is empty\n");
                iRet = WT_ERR_CODE_GENERAL_ERROR;
                break;
            }

            iRet = cellular_param_from_json(attr->vsaAlzParam.analyzeParam3GPP, JsonRoot);
        }
        else
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
        }
    } while (0);

#undef PAYLOAD_3GPP_PARAM_OFFSET
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAnalyParamRefClear(scpi_t *context)
{
    (void)context;
    // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    // attr->m_RefAnalyzeParam3GPP.IsLoadRef = 0;
    return SCPI_ResultOK(context, SCPI_RES_OK);
}

scpi_result_t SCPI_3GPP_SetAnalysisParamArb(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;

    const char *data = nullptr;
    size_t len = 0;
    AnalyzeParam AlzParam;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->vsaAlzParam.Reset_AlzParam(AlzParam.analyzeParam3GPP);
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        if (len < sizeof(AlzParam3GPP))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        memcpy(&attr->vsaAlzParam.analyzeParam3GPP, data, sizeof(attr->vsaAlzParam.analyzeParam3GPP));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzGroup(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.analyzeGroup = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetRfBand(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int StreamID = 0;
    do
    {
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.rf_band[StreamID] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "StreamID=" << StreamID << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetRfChannel(scpi_t *context)
{
    extern BandChanItem LTE_ULBandChanMap[];
    extern BandChanFreqItem LTE_DLBandChanFreqMap[];
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int StreamID = 0;
    do
    {
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->vsaAlzParam.analyzeParam3GPP.Standard != ALG_3GPP_STD_4G)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.rf_channel[StreamID] = Value;

        int LinkDirect = 0;
        switch (attr->vsaAlzParam.analyzeParam3GPP.Standard)
        {
            case ALG_3GPP_STD_WCDMA:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.WCDMA.LinkDirect;
                break;
            case ALG_3GPP_STD_4G:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.LTE.ChanType;
                break;
            case ALG_3GPP_STD_5G:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.NR.LinkDirect;
                break;
            case ALG_3GPP_STD_NB_IOT:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect;
                break;
            default:
                break;
        }

        if (LinkDirect == ALG_3GPP_UL)
        {
            for (int i = 0; i < BAND_CHANNEL_ITEM_MAX; i++)
            {
                if (Value > LTE_ULBandChanMap[i].ChannelMax)
                {
                    continue;
                }

                if (Value >= LTE_ULBandChanMap[i].ChannelMin)
                {
                    if (StreamID == 0)
                    {
                        attr->vsaParam.Freq = (Value + LTE_ULBandChanMap[i].Delta) / 10.0 * MHz_API;
                    }
                    else
                    {
                        attr->vsaParam.Freq2 = (Value + LTE_ULBandChanMap[i].Delta) / 10.0 * MHz_API;
                    }
                }
                else
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            }
            IF_BREAK(iRet);
        }
        else if (attr->vsaAlzParam.analyzeParam3GPP.Standard == ALG_3GPP_STD_4G && LinkDirect == ALG_4G_PDSCH)
        {
            for (int i = 0; i < BAND_CHANNEL_ITEM_MAX; i++)
            {
                if (Value > LTE_DLBandChanFreqMap[i].ChannelMax)
                {
                    continue;
                }

                if (Value >= LTE_DLBandChanFreqMap[i].N_ref)
                {
                    if (StreamID == 0)
                    {
                        attr->vsaParam.Freq = ((Value - LTE_DLBandChanFreqMap[i].N_ref) / 10.0 + LTE_DLBandChanFreqMap[i].F_ref) * MHz_API;
                    }
                    else
                    {
                        attr->vsaParam.Freq2 = ((Value - LTE_DLBandChanFreqMap[i].N_ref) / 10.0 + LTE_DLBandChanFreqMap[i].F_ref) * MHz_API;
                    }
                }
                else
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            }
            IF_BREAK(iRet);
        }
        else
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "StreamID=" << StreamID << "; Value=" << Value << "; Freq1=" << attr->vsaParam.Freq << "; Freq2=" << attr->vsaParam.Freq2 << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAnalyMeasureCommonParam(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int StreamID = 0;
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        const size_t arraySize = 3;
        std::array<bool, arraySize> Value = {false};

        for (int i = 0; i < arraySize; i++)
        {
            scpi_bool_t  tmpValue = false;
            if (!SCPI_ParamBool(context, &tmpValue, true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            Value[i] = tmpValue;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasPowerGraph = Value[0];
        attr->vsaAlzParam.analyzeParam3GPP.MeasSpectrum = Value[1];
        attr->vsaAlzParam.analyzeParam3GPP.MeasCCDF = Value[2];
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value[0]=" << Value[0] << "; Value[1]=" << Value[1] << "; Value[2]=" << Value[2] << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzDcFreqCompensate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.DcFreqCompensate = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpectrumRBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1000 || Value > 500000 || Value % 10 != 0) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.SpectrumRBW = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasurePower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasPowerGraph = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureCcdf(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasCCDF = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureSpectrum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasSpectrum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPkgOffset(scpi_t *context)
{

    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.PkgAlzOffset = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzParamDemode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = ALG_3GPP_STD_5G;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!IsAlg3GPPStandardType(Value))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.Reset_AlzParam(attr->vsaAlzParam.analyzeParam3GPP, Value, ALG_3GPP_UL, false);

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLinkDirect(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(ALG_3GPP_UL, ALG_3GPP_DL))
        .Result();

    if (iRet == WT_OK) {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        bool is3GPP = IsAlg3GPPStandardType(attr->vsaAlzParam.analyzeParam3GPP.Standard);
        if (is3GPP) {
            auto &param3gpp = attr->vsaAlzParam.analyzeParam3GPP;
            attr->vsaAlzParam.Reset_AlzParam(param3gpp, param3gpp.Standard, value, false);
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}
//**************************************************************************************************
// LTE
//**************************************************************************************************
scpi_result_t SCPI_3GPP_SetAlzCyclicPrefix(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).CyclicPrefix = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzUEID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 65535))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).UeID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzChannalType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {ALG_4G_PUSCH, ALG_4G_PDSCH})
        .Result();

    if (iRet == WT_OK) {
        Lte(context).ChanType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzNetSignal(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 288))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).NSValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCAState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).CarrAggrState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellIndex(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Param(value, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Cell[param].CellIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Cell[param].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPhyID(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Param(value, INT_RANGE(0, 503))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Cell[param].PhyCellID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellBW(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .ParamFromList(value, {1400000, 3000000, 5000000, 10000000, 15000000, 20000000})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Cell[param].ChannelBW = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellDUPLexing(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Param(value, INT_RANGE(ALG_3GPP_FDD, ALG_3GPP_TDD))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Cell[param].Duplexing = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellULDLConfig(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Param(value, INT_RANGE(0, 6))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Cell[param].ULDLConfig = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellSpecialSubframeConfig(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, ALG_4G_MAX_CELL_NUM - 1))
        .Param(value, INT_RANGE(0, 8))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Cell[param].SpecialSubfrmConfig = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschIndex(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].CellIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschRBConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RBCount = 0;
    int RBOffset = 0;
    int CellID = 0;
    int CellRBMax = -1;
    int Find = -1;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &RBCount, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &RBOffset, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        switch (attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].ChannelBW)
        {
        case 1400000:
            CellRBMax = pusch_pb_1_4[sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]) - 1];
            Find = BinaryFind(pusch_pb_1_4, sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]), RBCount);
            break;
        case 3000000:

            CellRBMax = pusch_pb_3[sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]) - 1];
            Find = BinaryFind(pusch_pb_3, sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]), RBCount);
            break;
        case 5000000:
            CellRBMax = pusch_pb_5[sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]) - 1];
            Find = BinaryFind(pusch_pb_5, sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]), RBCount);
            break;
        case 10000000:
            CellRBMax = pusch_pb_10[sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]) - 1];
            Find = BinaryFind(pusch_pb_10, sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]), RBCount);
            break;
        case 15000000:
            CellRBMax = pusch_pb_15[sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]) - 1];
            Find = BinaryFind(pusch_pb_15, sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]), RBCount);
            break;
        case 20000000:
            CellRBMax = pusch_pb_20[sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]) - 1];
            Find = BinaryFind(pusch_pb_20, sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]), RBCount);
            break;
        default:
            break;
        }

        if (Find == -1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellRBMax <= 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (RBCount + RBOffset > CellRBMax)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].RBNum = RBCount;
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].RBOffset = RBOffset;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RBCount=" << RBCount << " RBOffset=" << RBOffset << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschPrecoding(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].Precoding = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschLayerNum(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .ParamFromList(value, {1, 2, 4})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].LayerNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschAntennaNum(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .ParamFromList(value, {1, 2, 4})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].AntennaNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschCodebookIdx(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 15))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].CodebookIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschGroupHop(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].GroupHop = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschSequenceHop(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].SequenceHop = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschDeltaSeqShift(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 29))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].DeltaSeqShift = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschN1Dmrs(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .ParamFromList(value, {0, 2, 3, 4, 6, 8, 9, 10})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].N1Dmrs = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschCyclicShiftField(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 7))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].CyclicShiftField = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschCodeword(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(1, 2))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].Codeword = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschModulate(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int value1 = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, c::arraySize(Lte(context).Pusch[0].Modulate) - 1))
        .ParamFromList(value1, {0, 2, 4, 6, 8})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].Modulate[value] = value1;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschChanDecodeState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].ChanCodingState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschScramble(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].Scramble = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschMCSMode(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].McsCfgMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschMCSCfg(scpi_t *context)
{
    int param = 0;
    int value[4] = {0};
    
    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value[0], INT_RANGE(0, c::arraySize(Lte(context).Pusch[0].Mcs) - 1))
        .Param(value[1], INT_RANGE(0, 28))
        .Param(value[2], INT_RANGE(1, 253440))
        .Param(value[3], INT_RANGE(0, 3))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].Mcs[value[0]] = value[1];
        Lte(context).Pusch[param].PayloadSize[value[0]] = value[2];
        Lte(context).Pusch[param].RedunVerIdx[value[0]] = value[3];
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschEnable256QAM(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].Enable256QAM = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschRBDetMode(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pusch) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pusch[param].RBDetMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasSubFrame(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 99))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.MeasSubfrmIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasDmrsConsState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.DmrsConsState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureUnit(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.MeasureUnit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureExcludeAbnormalSymbol(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        // Lte(context).MeasInfo.ExAbnSymbFlg = value; // deprecated
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSyncMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.SyncMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSubframeOffset(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 9))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.MeasSubfrmOffset = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSubframeCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.MeasSubfrmCount = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSlotType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.MeasSlotType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvm(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.EvmEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationMerr(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.MErrEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationPerr(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.PErrEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvmSubcarrier(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.EvmSubcarEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationIbe(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.IBEEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEsFlat(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.ESFlatEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationIQConst(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.IQConstelEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerDynamic(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.PwrDynEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumAclr(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.ACLREnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEmissionMask(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.SpectEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumTxMeas(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.TxMeasureEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumDecodingResult(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.DecodingEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumDmrsCons(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.DmrsConsState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbol(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.EvmSymbEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbolIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.EvmSymbIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbolWindowType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.EvmSymbWinType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.ModEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumOBWEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.OBWEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.SEMEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerPmonEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.PMonitorEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.PowerEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationStaticCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.ModStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvmWindowNcp(scpi_t *context)
{
    const int arrsize = c::arraySize(Lte(context).MeasInfo.Modulate.EvmWinNcp);
    int value[arrsize] = {0};
    int iRet = ScpiChecker(context)
        .Param(value[0], INT_RANGE(0, 1))
        .Param(value[1], INT_RANGE(0, 1))
        .Param(value[2], INT_RANGE(0, 1))
        .Param(value[3], INT_RANGE(0, 1))
        .Param(value[4], INT_RANGE(0, 1))
        .Param(value[5], INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        memcpy(Lte(context).MeasInfo.Modulate.EvmWinNcp, value, arrsize);
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvmWindowEcp(scpi_t *context)
{
    const int arrsize = c::arraySize(Lte(context).MeasInfo.Modulate.EvmWinEcp);
    int value[arrsize] = {0};
    int iRet = ScpiChecker(context)
        .Param(value[0], INT_RANGE(0, 1))
        .Param(value[1], INT_RANGE(0, 1))
        .Param(value[2], INT_RANGE(0, 1))
        .Param(value[3], INT_RANGE(0, 1))
        .Param(value[4], INT_RANGE(0, 1))
        .Param(value[5], INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        memcpy(Lte(context).MeasInfo.Modulate.EvmWinEcp, value, arrsize);
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationExpPeriodLead(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.ExPeriodLeading = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationExpPeriodLag(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.ExPeriodLagging = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationExpAbnormalSymbol(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.ExAbnSymbFlg = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEqualizer(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Modulate.Equalizer = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMStatNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.SEMStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMMeasFilter(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.MeasFilter = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMACLRStatNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Spectrum.ACLRStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumUTRAEnable(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(1, 2))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        if (param == 1) {
            Lte(context).MeasInfo.Spectrum.UTRA1Enable = value;
        }
        else {
            Lte(context).MeasInfo.Spectrum.UTRA2Enable = value;
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEUTRAEnable(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(1, 2))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        if (param == 1) {
            Lte(context).MeasInfo.Spectrum.EUTRA1Enable = value;
        }
        else {
            Lte(context).MeasInfo.Spectrum.EUTRA2Enable = value;
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerTimeMask(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.TimeMaskType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerTimeLead(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(-1000, 1000))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.Leading = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerTimeLag(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(-1000, 1000))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.Lagging = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerStaticCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.PowerStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerHighDynamicMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).MeasInfo.Power.HighDynmMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasEvmCfg(scpi_t *context)
{
    int value = 0;
    int value1 = 0;
    int value2 = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Param(value1, INT_RANGE(0, 13))
        .Param(value2, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        // Lte(context).MeasInfo.Modulate.EvmSubcarrierState = value; // deprecated
        // Lte(context).MeasInfo.Modulate.EvmSymbIndx = value1; // deprecated
        // Lte(context).MeasInfo.Modulate.EvmSymbPosType = value2; // deprecated
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschSymbOffset(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 4))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.SymbOffset = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschResourceAllocation(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {0, 2})
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.ResAllocateType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschVRBAssignment(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.VRBAssignment = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRBGBitmap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    // 根据带宽确定RBG Bitmap长度
    int ChannelBW = attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[0].ChannelBW;
    int bitmapLength = 0;

    switch (ChannelBW) {
        case 1400000:
            bitmapLength = 6;
            break;
        case 3000000:
            bitmapLength = 8;
            break;
        case 5000000:
            bitmapLength = 13;
            break;
        case 10000000:
            bitmapLength = 17;
            break;
        case 15000000:
            bitmapLength = 19;
            break;
        case 20000000:
            bitmapLength = 25;
            break;
        default:
            return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    // 使用vector替代固定大小数组，提高安全性
    std::vector<char> bitmapBuffer(bitmapLength, 0);

    // 从SCPI参数读取bitmap值（从高位到低位）
    for (int i = bitmapLength - 1; i >= 0; i--) {
        int paramValue = 0;
        if (!SCPI_ParamInt(context, &paramValue, true)) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (paramValue < 0 || paramValue > 1) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        bitmapBuffer[i] = static_cast<char>(paramValue);
    }

    // 如果参数读取成功，复制到目标结构体
    if (iRet == WT_ERR_CODE_OK) {
        memcpy(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RBGBitmap, 
               bitmapBuffer.data(), 
               bitmapLength);
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRBNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.RBNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRBOffset(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 99))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.RBOffset = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPbchState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.PbchState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPrecoding(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.Precoding = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschLayerNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4})
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.LayerNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschAntennaNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4})
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.AntennaNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschCyclicDelayDiversity(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.CyclicDelayDiversity = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschCodebookIdx(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 15))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.CodebookIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschCodeword(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 2))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.Codeword = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschChanDecodeState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.ChanCodingState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschScramble(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.Scramble = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschMcsCfgMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.McsCfgMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschIRConfigMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.IRConfigMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschTxMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 9))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.TxMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschUECategory(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 12))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.UECategory = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschMcsTable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 4))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.McsTable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschTBSTalternativeIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 4))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).Pdsch.TbsIndexAlt = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if ((Value < DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx]) - 1][0] || 
             Value > DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx]) - 1][1]) && 
            (Value < DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx])][0] || 
             Value > DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx])][1]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Mcs[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschModulate(scpi_t *context)
{
    int value = 0;
    int value1 = 0;

    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .ParamFromList(value1, {2, 4, 6, 8})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.Modulate[value] = value1;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPayloadSize(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pdsch.PayloadSize) - 1))
        .Param(value, INT_RANGE(1, 253440))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.PayloadSize[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRedunVerIdx(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pdsch.RedunVerIdx) - 1))
        .Param(value, INT_RANGE(0, 3))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.RedunVerIdx[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschSoftChanBit(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pdsch.SoftChanBit) - 1))
        .Param(value, INT_RANGE(3200, 58675200))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.SoftChanBit[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschNIR(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).Pdsch.NIR) - 1))
        .Param(value, INT_RANGE(800, 3667200))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.NIR[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPA(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {-6.02, -4.77, -3.01, -1.77, 0.0, 0.97, 2.04, 3.01})
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.PA = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPB(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 3))
        .Result();
    
    if (iRet == WT_OK) {
        Lte(context).Pdsch.PB = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        // Lte(context).LimitInfo.ModLimitMode = value; // deprecated
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmRmsState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].EvmRms.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmRmsLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].EvmRms.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmPeakState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].EvmPeak.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmPeakLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].EvmPeak.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrRmsState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].MErrRms.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrRmsLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].MErrRms.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrPeakState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].MErrPeak.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrPeakLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].MErrPeak.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrRmsState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].PhErrRms.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrRmsLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 180))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].PhErrRms.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrPeakState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].PhErrPeak.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrPeakLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 180))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].PhErrPeak.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitFreqErrState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].FreqErr.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitFreqErrLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].FreqErr.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIQOffsetState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].IQOffset.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIQOffsetPwrLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value[4] = {0};
        int minSize = min(static_cast<int>(ARRAYSIZE(Value)), context->parser_state.numberOfParameters);
        for (int i = 0; i < minSize; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < -256 || Value[i] > 256)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int i = 0; i < minSize; i++)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IQOffset.PwrLimit[i] = Value[i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].IBE.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEGenMin(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].IBE.GenMin = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEGenEvm(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].IBE.GenEVM = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEGenPwr(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].IBE.GenPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEIqImage(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].IBE.IQImage[0] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIQOffsetPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value[4] = {0};
        int minSize = min(static_cast<int>(ARRAYSIZE(Value)), context->parser_state.numberOfParameters);
        for (int i = 0; i < minSize; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < -256 || Value[i] > 256)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int i = 0; i < minSize; i++)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.IQOffsetPwr[i] = Value[i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].SpectFlat.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatRange(scpi_t *context)
{
    int params[2] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .CommandParam(params[1], INT_RANGE(1, 2))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        if (params[1] == 1) {
            Lte(context).LimitInfo.ModLimit[params[0]].SpectFlat.Range1 = value;
        } else {
            Lte(context).LimitInfo.ModLimit[params[0]].SpectFlat.Range2 = value;
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatMax1Min2(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].SpectFlat.Max1Min2 = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatMax2Min1(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].SpectFlat.Max2Min1 = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatEdgeFreq(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 20))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.ModLimit[param].SpectFlat.EdgeFreq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitMode(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        // Lte(context).LimitInfo.SpectLimitMode = value; // deprecated
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitObwState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[param].OBWLimit.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitObwLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 40))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[param].OBWLimit.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemLimitState(scpi_t *context)
{
    int params[3] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 3);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit) - 1))
        .CommandParam(params[2], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit[0]) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].SEMLimit[params[1]][params[2]].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemStartFreq(scpi_t *context)
{
    int params[3] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 3);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit) - 1))
        .CommandParam(params[2], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit[0]) - 1))
        .Param(value, DOUBLE_RANGE(0, 25))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].SEMLimit[params[1]][params[2]].StartFreq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemStopFreq(scpi_t *context)
{
    int params[3] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 3);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit) - 1))
        .CommandParam(params[2], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit[0]) - 1))
        .Param(value, DOUBLE_RANGE(0, 25))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].SEMLimit[params[1]][params[2]].StopFreq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemPwr(scpi_t *context)
{
    int params[3] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 3);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit) - 1))
        .CommandParam(params[2], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit[0]) - 1))
        .Param(value, DOUBLE_RANGE(-100, 100))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].SEMLimit[params[1]][params[2]].LimitPower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemRbw(scpi_t *context)
{
    int params[3] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 3);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit) - 1))
        .CommandParam(params[2], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].SEMLimit[0]) - 1))
        .ParamFromList(value, {30 * KHz, 50 * KHz, 100 * KHz, 150 * KHz, 200 * KHz, 1 * MHz})
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].SEMLimit[params[1]][params[2]].RBW = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraRealState(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].UtraLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].UtraLimit[params[1]].RelState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraRealLimit(scpi_t *context)
{
    int params[2] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].UtraLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].UtraLimit[params[1]].RelLimit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraAbsState(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].UtraLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].UtraLimit[params[1]].AbsState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraAbsPwr(scpi_t *context)
{
    int params[2] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].UtraLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].UtraLimit[params[1]].AbsPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraRealState(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].EUtraLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].EUtraLimit[params[1]].RelState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraRealLimit(scpi_t *context)
{
    int params[2] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].EUtraLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].EUtraLimit[params[1]].RelLimit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraAbsState(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].EUtraLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].EUtraLimit[params[1]].AbsState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraAbsPwr(scpi_t *context)
{
    int params[2] = {0};
    double value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit) - 1))
        .CommandParam(params[1], INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SpectLimit[0].EUtraLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SpectLimit[params[0]].EUtraLimit[params[1]].AbsPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.PwrLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.PwrLimit[param].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitOnPwrUpper(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.PwrLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.PwrLimit[param].OnLimitUpper = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitOnPwrLower(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.PwrLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.PwrLimit[param].OnLimitLower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitOffPwr(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.PwrLimit) - 1))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.PwrLimit[param].OffLimit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemAddTestTol(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, c::arraySize(Lte(context).LimitInfo.SEMAddTestTol) - 1))
        .Param(value, DOUBLE_RANGE(-5, 5))
        .Result();

    if (iRet == WT_OK) {
        Lte(context).LimitInfo.SEMAddTestTol[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
