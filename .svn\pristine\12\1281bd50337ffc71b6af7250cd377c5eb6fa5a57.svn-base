#ifndef __WT_ALG_ENV__
#define __WT_ALG_ENV__

#include <string>
#include <cstdio>

#include "../devlib/devtype.h"
#include "alg/includeAll.h"
#include "algreset.h"

static const double WT_ADC_MAX_VOL = 1.125;
static const int WT_ADC_CODE_TOP = 8192;  // 14bit ADC
static const int WT_ADC_POW_Z = 50;

#define File_EVM_OFFSET "_Net.ini"
#define FILE_LINE_SIZE  128

// 算法环境初始化
class AlgEnv
{
public:
    static const AlgEnv &Instance()
    {
        static AlgEnv Env;
        return Env;
    }

    // 获取缩放系数
    double GetScaleVal(int DataType) const
    {
        if (DataType == enDataFormat_Int16)
        {
            return WT_ADC_MAX_VOL * 1000 / WT_ADC_CODE_TOP;
        }
        else
        {
            return 1;
        }
    }

    void Init(void) const
    {
    }

    double m_VsaPwrBaseDelta = 0;
    double m_VsgPwrBaseDelta = 0;
    double m_VsaPwrDelta[WT_BW_NUM] = {0, };
    double m_VsgPwrDelta[WT_BW_NUM] = {0, };

    // 以下数据根据 带宽 和 通道估计设置 有传给算法模块, 对结果做EVM补偿用 
    stEvmOffset m_EvmOffset120M;    // 120M Raw Long Symbols
    stEvmOffset m_EvmOffset120MFP;  // 120M Full packet
    stEvmOffset m_EvmOffset240M;    // 240M Raw Long Symbols
    stEvmOffset m_EvmOffset240MFP;  // 240M Full packet

private:
    AlgEnv(void)
    {
        AlgReset::Instance().AlgGetReaderLockT();
        WT_Algorithm_DLLinit();
        WT_Algorithm_DLLinit_Vsg();
        AlgReset::Instance().AlgReleaseLockT();
        Algvsg_3GPP_LibInit();
        Algvsa_3GPP_AlgorithmDllInit();
        Algvsa_3GPP_AlgorithmInit();
        GetEvmOffset();
    }

    ~AlgEnv(void)
    {
        AlgReset::Instance().AlgGetReaderLockT();
        WT_Algorithm_DLLterm();
        WT_Algorithm_DLLterm_Vsg();
        AlgReset::Instance().AlgReleaseLockT();
        Algvsg_3GPP_LibDeinit();
        Algvsa_3GPP_AlgorithmDeinit();
        Algvsa_3GPP_AlgorithmDllDeinit();
    }

    void GetEvmOffset(void)
    {
        FILE *pFile = NULL;
        char line_buff[FILE_LINE_SIZE + 1] = { 0 };
        std::string Path = WTConf::GetDir() + "/" + File_EVM_OFFSET;

        pFile = fopen(Path.c_str(), "rb");
        if(NULL != pFile)
        {
            while(NULL != fgets(line_buff, FILE_LINE_SIZE, pFile))
            {
                if(1 == sscanf(line_buff, "AG_120=%lf,%lf", &m_EvmOffset120M.Offset_11AG, &m_EvmOffset120MFP.Offset_11AG))
                {

                }
                else if(1 == sscanf(line_buff, "B_120=%lf,%lf", &m_EvmOffset120M.Offset_11B, &m_EvmOffset120MFP.Offset_11B))
                {

                }
                else if(1 == sscanf(line_buff, "HT20_120=%lf,%lf", &m_EvmOffset120M.Offset_11N_20M, &m_EvmOffset120MFP.Offset_11N_20M))
                {

                }
                else if(1 == sscanf(line_buff, "HT40_120=%lf,%lf", &m_EvmOffset120M.Offset_11N_40M, &m_EvmOffset120MFP.Offset_11N_40M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT20_120=%lf,%lf", &m_EvmOffset120M.Offset_11AC_20M, &m_EvmOffset120MFP.Offset_11AC_20M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT40_120=%lf,%lf", &m_EvmOffset120M.Offset_11AC_40M, &m_EvmOffset120MFP.Offset_11AC_40M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT80_120=%lf,%lf", &m_EvmOffset120M.Offset_11AC_80M, &m_EvmOffset120MFP.Offset_11AC_80M))
                {

                }
                //11ax
                else if(1 == sscanf(line_buff, "AXSU20_120=%lf,%lf", &m_EvmOffset120M.Offset_11AXSU_20M, &m_EvmOffset120MFP.Offset_11AXSU_20M))
                {

                }
                else if(1 == sscanf(line_buff, "AXSU40_120=%lf,%lf", &m_EvmOffset120M.Offset_11AXSU_40M, &m_EvmOffset120MFP.Offset_11AXSU_40M))
                {

                }
                else if(1 == sscanf(line_buff, "AXSU80_120=%lf,%lf", &m_EvmOffset120M.Offset_11AXSU_80M, &m_EvmOffset120MFP.Offset_11AXSU_80M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA20_120=%lf,%lf", &m_EvmOffset120M.Offset_11AXOFDMA_20M, &m_EvmOffset120MFP.Offset_11AXOFDMA_20M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA40_120=%lf,%lf", &m_EvmOffset120M.Offset_11AXOFDMA_40M, &m_EvmOffset120MFP.Offset_11AXOFDMA_40M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA80_120=%lf,%lf", &m_EvmOffset120M.Offset_11AXOFDMA_80M, &m_EvmOffset120MFP.Offset_11AXOFDMA_80M))
                {

                }
                //end 11ax
                else if(1 == sscanf(line_buff, "BT2M_120=%lf,%lf", &m_EvmOffset120M.Offset_BT_2M, &m_EvmOffset120MFP.Offset_BT_2M))
                {

                }
                else if(1 == sscanf(line_buff, "BT3M_120=%lf,%lf", &m_EvmOffset120M.Offset_BT_3M, &m_EvmOffset120MFP.Offset_BT_3M))
                {

                }
                else if(1 == sscanf(line_buff, "ZB2G_120=%lf,%lf", &m_EvmOffset120M.Offset_ZigBee_2G, &m_EvmOffset120MFP.Offset_ZigBee_2G))
                {

                }
                else if(1 == sscanf(line_buff, "AG_240=%lf,%lf", &m_EvmOffset240M.Offset_11AG, &m_EvmOffset240MFP.Offset_11AG))
                {

                }
                else if(1 == sscanf(line_buff, "B_240=%lf,%lf", &m_EvmOffset240M.Offset_11B, &m_EvmOffset240MFP.Offset_11B))
                {

                }
                else if(1 == sscanf(line_buff, "HT20_240=%lf,%lf", &m_EvmOffset240M.Offset_11N_20M, &m_EvmOffset240MFP.Offset_11N_20M))
                {

                }
                else if(1 == sscanf(line_buff, "HT40_240=%lf,%lf", &m_EvmOffset240M.Offset_11N_40M, &m_EvmOffset240MFP.Offset_11N_40M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT20_240=%lf,%lf", &m_EvmOffset240M.Offset_11AC_20M, &m_EvmOffset240MFP.Offset_11AC_20M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT40_240=%lf,%lf", &m_EvmOffset240M.Offset_11AC_40M, &m_EvmOffset240MFP.Offset_11AC_40M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT80_240=%lf,%lf", &m_EvmOffset240M.Offset_11AC_80M, &m_EvmOffset240MFP.Offset_11AC_80M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT160_240=%lf,%lf", &m_EvmOffset240M.Offset_11AC_160M, &m_EvmOffset240MFP.Offset_11AC_160M))
                {

                }
                else if(1 == sscanf(line_buff, "VHT8080_240=%lf,%lf", &m_EvmOffset240M.Offset_11AC_80_80M, &m_EvmOffset240MFP.Offset_11AC_80_80M))
                {

                }
                //11ax
                else if(1 == sscanf(line_buff, "AXSU20_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXSU_20M, &m_EvmOffset240MFP.Offset_11AXSU_20M))
                {

                }
                else if(1 == sscanf(line_buff, "AXSU40_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXSU_40M, &m_EvmOffset240MFP.Offset_11AXSU_40M))
                {

                }
                else if(1 == sscanf(line_buff, "AXSU80_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXSU_80M, &m_EvmOffset240MFP.Offset_11AXSU_80M))
                {

                }
                else if(1 == sscanf(line_buff, "AXSU160_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXSU_160M, &m_EvmOffset240MFP.Offset_11AXSU_160M))
                {

                }
                else if(1 == sscanf(line_buff, "AXSU8080_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXSU_80_80M, &m_EvmOffset240MFP.Offset_11AXSU_80_80M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA20_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXOFDMA_20M, &m_EvmOffset240MFP.Offset_11AXOFDMA_20M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA40_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXOFDMA_40M, &m_EvmOffset240MFP.Offset_11AXOFDMA_40M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA80_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXOFDMA_80M, &m_EvmOffset240MFP.Offset_11AXOFDMA_80M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA160_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXOFDMA_160M, &m_EvmOffset240MFP.Offset_11AXOFDMA_160M))
                {

                }
                else if(1 == sscanf(line_buff, "AXOFDMA8080_240=%lf,%lf", &m_EvmOffset240M.Offset_11AXOFDMA_80_80M, &m_EvmOffset240MFP.Offset_11AXOFDMA_80_80M))
                {

                }
                //end 11ax
                else if(1 == sscanf(line_buff, "BT2M_240=%lf,%lf", &m_EvmOffset240M.Offset_BT_2M, &m_EvmOffset240MFP.Offset_BT_2M))
                {

                }
                else if(1 == sscanf(line_buff, "BT3M_240=%lf,%lf", &m_EvmOffset240M.Offset_BT_3M, &m_EvmOffset240MFP.Offset_BT_3M))
                {

                }
                else if(1 == sscanf(line_buff, "ZB2G_240=%lf,%lf", &m_EvmOffset240M.Offset_ZigBee_2G, &m_EvmOffset240MFP.Offset_ZigBee_2G))
                {

                }
                else if(1 == sscanf(line_buff, "VSA_PWR_BASE=%lf", &m_VsaPwrBaseDelta))
                {

                }
                else if(1 == sscanf(line_buff, "VSG_PWR_BASE=%lf", &m_VsgPwrBaseDelta))
                {

                }
                else if(1 == sscanf(line_buff, "VSA_PWR_DELTA=%lf,%lf,%lf,%lf,%lf",
                                    &m_VsaPwrDelta[WT_BW_CW], &m_VsaPwrDelta[WT_BW_20M],
                                    &m_VsaPwrDelta[WT_BW_40M], &m_VsaPwrDelta[WT_BW_80M], &m_VsaPwrDelta[WT_BW_160M]))
                {

                }
                else if(1 == sscanf(line_buff, "VSG_PWR_DELTA=%lf,%lf,%lf,%lf,%lf",
                                    &m_VsgPwrDelta[WT_BW_CW], &m_VsgPwrDelta[WT_BW_20M],
                                    &m_VsgPwrDelta[WT_BW_40M], &m_VsgPwrDelta[WT_BW_80M], &m_VsgPwrDelta[WT_BW_160M]))
                {

                }

                memset(line_buff, 0, sizeof(line_buff));
            }

            fclose(pFile);
        }
    }
};

#endif