//*****************************************************************************
//  File: link.cpp
//  WT-Server连接管理
//  Data: 2016.7.28
//*****************************************************************************
#include "link.h"
#include <unistd.h>
#include <fcntl.h>
#include <algorithm>
#include <exception>
#include <cerrno>
#include <cstring>
#include "wterror.h"
#include "wtlog.h"
#include "license.h"
#include "socket.h"
#include "service/service.h"
#include "service/monitor.h"

#include "service/autobaking.h"
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "subtaskmgr.h"
#include "devlib.h"
#include "service/sncmanager.h"

using namespace std;

static const int SUB_LINK_NUM = 3;     //每个用户的子连接数
static const int MAX_USER_MUM = 16;    //最大用户数
static const int MIN_LINK_THREAD = 16; //最少的连接线程数
static const int RESERCED_THREAD = 10; //预留给程序内部用的线程

LinkMgr::LinkMgr(int SocketFd, wtev::loop_ref &loop)
    : m_SocketFd(SocketFd), m_WRSocket(SocketFd), m_EvLoop(loop), m_IOWatcher(loop)
{
    SetAutoRunTimer();
}

int LinkMgr::SetAutoRunTimer(void)
{
    // 进程启动一段时间后开启SUB_TASKMGR, 每SUB_TASKMGR_TIMER_INTERVAL秒一次计时器
    int Value = 0;      // 单位秒
    DevConf::Instance().GetItemVal("InCalAfterServer", Value);
    if (Value < 20)
    {
        Value = 20;
    }

    m_EvTimer.set<LinkMgr, &LinkMgr::SrvTimerCb>(this);
    m_EvTimer.start(Value, SUB_TASKMGR_TIMER_INTERVAL);

    m_RunCnt = AUTO_BAKING_KEEP_TIME;   // 随意初始化的值, 后续会改变

    //WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr add AutoBaking's timer done\n");
    return 0;
}

bool LinkMgr::QueryManagerBootSeconds()
{
    WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerBootSeconds call in\n");
    const char *IP = "127.0.0.1";
    int Port = 7002;

    struct sockaddr_in addr;  
    socklen_t addr_len =sizeof(struct sockaddr_in);
    int fd = 0;
    int len = 0;

    if((fd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
       WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerBootSeconds create socket failed\n");
       return false;
    }

    // WRSocket::SetNonblock(s);
    struct timeval timeout;
    timeout.tv_sec = 0;//秒
    timeout.tv_usec = 500000;//微秒
    if (setsockopt(fd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) == -1)
    {
        close(fd);
        return false;
    }

    bzero(&addr, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(Port);
    addr.sin_addr.s_addr = inet_addr(IP);

    const char request[30] = "scan";
    if (-1 == sendto(fd, request, 30, 0, (struct sockaddr *)&addr, addr_len))
    {
        WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerBootSeconds send \"scan\" failed\n");
        close(fd);
        return false;
    }

    // sleep(0.5);
    char buffer[2048] = {0};
    if ((len = recvfrom(fd, buffer, sizeof(buffer), 0, (struct sockaddr *)&addr, &addr_len)) < 0)
    {
        WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerBootSeconds recv respond failed\n");
        close(fd);
        return false;
    }
    close(fd);

    // 查找持续在线时间 Online last Time:
    buffer[2048-1] = 0;
    const char *p = strstr(buffer, "Online last Time:");
    if (p == NULL)
    {
        WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerBootSeconds respond content cannot find \"Online last Time:\"\n");
        return false;
    }

    int year = -1;
    int month = -1;
    int day = -1;
    int hour = -1;
    int min = -1;
    int second = -1;

    sscanf(p+17, "%d-%d-%d,%d-%d-%d", &year, &month, &day, &hour, &min, &second);
    if (year < 0 || month < 0 || day < 0 || hour < 0 || min < 0 || second < 0)
    {
        WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerInfo find hold time failed [%d-%d-%d, %d-%d-%d]\n", year, month, day, hour, min, second);
        return false;
    }

    int total = year*(12*30*24*3600) + month*(30*24*3600) + day*(24*3600) + hour*3600 + min*60 + second;
    WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr::QueryManagerInfo  Manager boot hold time [%d]s\n", total);

    // TODO 是否要判断total 小于0呢?

    if (total >= AUTO_BAKING_KEEP_TIME)
    {
        m_RunCnt = 0;
    }
    else
    {
        m_RunCnt = (AUTO_BAKING_KEEP_TIME - total) / SUB_TASKMGR_TIMER_INTERVAL;
    }

    return true;
}

// 启动自动校准后, 就不在做自动运行了，防止冲突
#if 0
void LinkMgr::SrvTimerCb(wtev::timer &watcher, int revents)
{
    WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr call in AutoBaking's timer SrvTimerCb, left count[%d]\n", m_RunCnt);
    (void)watcher;
    (void)revents;

    // Server 进程可能重启过
    if (m_RunCnt >= AUTO_BAKING_KEEP_TIME)  // 初始值
    {
        // 查询Manager的启动时间
        bool ret = QueryManagerBootSeconds();
        if (!ret)   // 查询失败就不开启自动运行
        {
            return;
        }
    }

    // 如果运行超过了AUTO_BAKING_KEEP_TIME就停止计数了
    m_RunCnt--;
    if (m_RunCnt < 0)
    {
        WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr AutoBaking's timer out of date , and delete AutoBaking's timer\n");
        m_EvTimer.stop();   // 删除观察者

        AutoBakingSerive::Instance().Stop();
        
        return;
    }

    // AutoBaking
    if (!AutoBakingSerive::Instance().IsRunning())
    {
        // 没有用户连接
        if (m_SrvLink.empty())
        {
            if (!AutoBakingSerive::Instance().QueryExistMgrLinkInfo())
            {
                WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr start running AutoBaking's service\n");

                m_TaskPool->AddTask((void*)0, bind(&LinkMgr::StartAutoTest, this, placeholders::_1));

                m_TaskPool->AddTask((void*)1, bind(&LinkMgr::StartAutoTest, this, placeholders::_1));
            }

        }
    }
}
#else
// 自动校准子任务
void LinkMgr::SrvTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    m_TaskPool->AddTask(nullptr, bind(&LinkMgr::SrvTimerThread, this));
}

//新建线程执行
void LinkMgr::SrvTimerThread()
{
    SubTaskmgr::Instance().NoticeTimeTick();
}
#endif

// 新建线程执行
void LinkMgr::SncCalThread()
{
    SncCalMgr::Instance().Run();
    SncCalMgr::Instance().Stop();
}

void LinkMgr::StartAutoTest(void *Arg)
{
    // 0是vsa, 1是vsg
    long type = (long)Arg;

    if (type == 0)
    {
        AutoBakingSerive::Instance().AutoStartVsaTest();
    }
    else
    {
        AutoBakingSerive::Instance().AutoStartVsgTest();
    }  
}

// 创建线程池，并注册link socket的监控
int LinkMgr::Activate(void)
{
    License::Instance().GetLinkNum(m_UserNum);
    WTLog::Instance().WriteLog(LOG_DEBUG, "================ LinkMgr::Activate m_UserNum = %d\n", m_UserNum);
    if (m_UserNum == 0)
    {
        m_UserNum = 1;  //最少一个连接
    }
    else
    {
        if (m_UserNum > MAX_USER_MUM)
        {
            m_UserNum = MAX_USER_MUM;
            WTLog::Instance().LOGERR(WT_LIC_VALUE_INVALID, "link number > MAX_USER_MUM");
        }
    }

    try
    {
        m_TaskPool.reset(new ThreadPool(max(m_UserNum * SUB_LINK_NUM + RESERCED_THREAD, MIN_LINK_THREAD)));
        m_TaskPool->Activate();
    }
    catch(system_error const &e)
    {
        WTLog::Instance().LOGERR(WT_CREATE_THREAD_FAILED, "create thread pool failed");
        return WT_CREATE_THREAD_FAILED;
    }

    if (WRSocket::SetNonblock(m_SocketFd) != WT_OK)
    {
        WTLog::Instance().LOGERR(errno, "SetNonblock failed");
    }

    m_IOWatcher.set<LinkMgr, &LinkMgr::LinkMsgCb>(this);
    m_IOWatcher.start(m_SocketFd, EV_READ);
    m_TaskPool->AddTask(nullptr, bind(&LinkMgr::SncCalThread, this));
    return WT_OK;
}

// 从WT-Link接收消息并进行相应的处理
void LinkMgr::LinkMsgCb(wtev::io &watcher, int revents)
{
    (void)watcher;

    if (revents & EV_READ)
    {
        int Fd = -1;
        int Ret = WT_OK;
        LinkMsg Msg;

        {
            unique_lock<mutex> SockLock(m_SocketMutex);
            Ret = m_WRSocket.RecvFdMsg(Fd, &Msg, sizeof(Msg));
            SockLock.unlock();
        }

        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "RecvFdMsg failed");
            return;
        }

        // 如果存在自动运行就关闭掉
        SubTaskmgr::Instance().StopAutoBakingService();

        if (Msg.Cmd == LINK_CMD_NEW)
        {
            AcceptLink(Fd, Msg.Type, Msg.SID);
        }
        else if (Msg.Cmd == LINK_CMD_CLOSE_ALL)
        {
            StopAllService();

            if(Msg.Type == MGR_LINK)
            {
                MonitorMgr::Instance().DisConnectAllMonitor();
            }

            unique_lock<mutex> Lock(m_LinkMutex);
            m_Exclude = false;
            Lock.unlock();
        }
        else
        {
            WTLog::Instance().LOGERR(WT_ERROR, "Link Cmd error");
        }
    }
}

void LinkMgr::StopAllService(void)
{
    unique_lock<mutex> Lock(m_LinkMutex);

    for (auto &Srv : m_SrvLink)
    {
        const LinkInfo &Link = Srv->GetLink();
        if (Link.GetType() != DIAGNOSIS_LINK)
        {
            Srv->Stop();
        }
    }

    Lock.unlock();
}

void LinkMgr::RespSuccToClient(int Fd)
{
    int Len = 0;
    char Resp[] = "connection ok";

    WRSocket Sock(Fd, 0);
    Sock.Send(Resp, (sizeof(Resp) - 1), Len);
}

void LinkMgr::AcceptLink(int Fd, int Type, int SID)
{
    int Ret = WT_LINK_TYPE_ERROR;

    switch(Type)
    {
        case SRV_EXCLUDE_LINK:
            WTLog::Instance().WriteLog(LOG_DEBUG, "LinkMgr accept a link is SRV_EXCLUDE_LINK, Kick off all links\n");
            SubTaskmgr::Instance().NoticeHasExcludeLink(true);
            StopAllService();
            Ret = StartLinSrv(Fd, Type, SID);
            break;

        case SRV_NORMAL_LINK:
        case SRV_SUB_LINK:
        case DIAGNOSIS_LINK:
            Ret = StartLinSrv(Fd, Type, SID);
            break;

        case MON_LINK:
            Ret = MonitorMgr::Instance().AddMonitor(m_EvLoop, Fd);
            break;

        default:
            Ret = WT_LINK_TYPE_ERROR;
            WTLog::Instance().LOGERR(WT_LINK_TYPE_ERROR, "unknown link type");
    }

    if (Ret == WT_OK)
    {
        RespSuccToClient(Fd);
    }
    else
    {
        CloseLink(Fd, Type, SID);
    }
}

// 线程函数体，调用service的线程实体函数
void LinkMgr::LinkService(shared_ptr<Service> LinkSrv, void *Arg)
{
    (void)Arg;

    LinkSrv->Run();

    StopLinkSrv(LinkSrv);
}

// 创建link service并加入到任务队列
int LinkMgr::StartLinSrv(int Fd, int Type, int SID)
{
    bool Exclude = (Type == SRV_EXCLUDE_LINK) ? true : m_Exclude;

    LinkInfo Link(Fd, Type, SID);
    shared_ptr<Service> LinkSrv = make_shared<Service>(Link, Exclude);
    if (LinkSrv == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc Service failed");
        return WT_ALLOC_FAILED;
    }

    lock_guard<mutex> Lock(m_LinkMutex);

    if (Type == SRV_NORMAL_LINK)
    {
        if ((m_UserCnt == m_UserNum) || m_Exclude)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "LinkMgr accept a link failed! MaxUserNum[%d], CurUserCnt[%d], Exclude Link status[%d]\n", m_UserNum, m_UserCnt, m_Exclude);
            WTLog::Instance().LOGERR(WT_LINK_CNT_FULL, "user is full");
            return WT_LINK_CNT_FULL;
        }

        m_UserCnt++;
    }
    else if (Type == SRV_EXCLUDE_LINK)
    {
        m_Exclude = true;
    }

    m_SrvLink.push_back(LinkSrv);
    m_TaskPool->AddTask(nullptr, bind(&LinkMgr::LinkService, this, LinkSrv, placeholders::_1));

    return WT_OK;
}

// 断开客户连接
void LinkMgr::StopLinkSrv(shared_ptr<Service> &LinkSrv)
{
    const LinkInfo &Link = LinkSrv->GetLink();

    unique_lock<mutex> Lock(m_LinkMutex);

    if (Link.GetType() == SRV_NORMAL_LINK)
    {
        m_UserCnt--;
    }

    m_SrvLink.remove(LinkSrv);

    bool haveSrvLink = false;
    for (auto &Srv : m_SrvLink)
    {
        const LinkInfo &Link = Srv->GetLink();
        if (Link.GetType() >= SRV_EXCLUDE_LINK && Link.GetType() <= SRV_SUB_LINK)
        {
            haveSrvLink = true;
            break;
        }
    }
    if (!haveSrvLink)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "void LinkMgr::StopLinkSrv: set full duplex enable false\n";
        DevLib::Instance().SetFullDuplexEnable(0);
    }

    // 查找是否存在独占连接，一台仪器组MIMO的时候，主从都是独占的...
    bool HasExclude = false;
    for (auto &Srv : m_SrvLink)
    {
        if  (Srv->IsExclude())
        {
            HasExclude = true;
            break;
        }
    }

    if (!HasExclude)
    {
        m_Exclude = false;
        SubTaskmgr::Instance().NoticeHasExcludeLink(false);
    }

    Lock.unlock();

    CloseLink(Link.GetFd(), Link.GetType(), Link.GetSID());
}

void LinkMgr::CloseLink(int Fd, int Type, int SID)
{
    close(Fd);

    int Ret = WT_OK;
    Fd = -1;
    LinkMsg Msg(LINK_CMD_CLOSE, Type, SID);

    unique_lock<mutex> SockLock(m_SocketMutex);

    Ret = m_WRSocket.SendFdMsg(Fd, &Msg, sizeof(Msg));

    SockLock.unlock();

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SendFdMsg failed");
    }
    RetSetHWConfig();
}

void LinkMgr::SaveStackData(void)
{
    for(auto &Item : m_SrvLink)
    {
        Item->SaveStackData();
    }
}

bool LinkMgr::HasLink(void)
{
    unique_lock<mutex> Lock(m_LinkMutex);
    return !(m_SrvLink.empty());
}

int LinkMgr::RetSetHWConfig()
{
    unique_lock<mutex> Lock(m_LinkMutex);
    bool UserLink = false;
    for (auto &Item : m_SrvLink)
    {
        int Type = Item->GetLinkType();
        if (Type == SRV_EXCLUDE_LINK || Type == SRV_NORMAL_LINK || Type == SRV_SUB_LINK)
        {
            UserLink = true;
        }
    }

    if (UserLink == false)
    {
        return DevLib::Instance().ReSetLOComMode();
    }
    else
    {
        return WT_OK;
    }
}