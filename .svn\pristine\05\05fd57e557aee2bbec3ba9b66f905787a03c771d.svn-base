//*****************************************************************************
//  File: service.cpp
//  业务处理相关
//  Data: 2016.8.3
//*****************************************************************************
#include "service.h"
#include <unistd.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <net/if.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <iostream>
#include <pthread.h>
#include "wterror.h"
#include "wtlog.h"
#include "secure.h"
#include "business.h"
#include "basefun.h"
#include "userconnmgr.h"

using namespace std;

Service::Service(const LinkInfo &Link, bool Exclude)
    : m_StopEv(m_EvLoop), m_Link(Link), m_Exclude(Exclude), m_LinkIO(m_EvLoop)
{
    m_StopEv.set<Service, &Service::StopCb>(this);
    m_StopEv.set_priority(EV_MAXPRI);
    m_StopEv.start();
}

Service::~Service()
{
    // Service 析构调用的主要过程
    // Task->LinkMgr::LinkService()
    //        =>Service::Run() => msg loop
    //        =>LinkMgr::StopLinkSrv()
    //            => remove Service => delete Service => ~Service()

    StopBns();
}

string Service::GetPeerIP(int Fd)
{
    struct sockaddr_in Saddr;
    socklen_t Len = sizeof(Saddr);

    if (!getpeername(Fd, (struct sockaddr *)&Saddr, &Len))
    {
        return inet_ntoa(Saddr.sin_addr);
    }
    else
    {
        return "";
    }
}

void Service::Run(void)
{
    //如果已经有设置停止service则退出
    if (m_StopEv.async_pending())
    {
        return;
    }
    m_ThreadId = gettid();
    WTLog::Instance().WriteLog(LOG_DEBUG, "Service::Run tid = %d, Fd=%d\n", m_ThreadId, m_Link.GetFd());
    char peerIP[16] = {0};
    int peerPort = 0;

    Basefun::GetSockPeerInfo(m_Link.GetFd(), peerIP, peerPort);
    UserConnMgr::Instance().AddorSetExtUserInfobyThreadId(std::this_thread::get_id(), peerIP, peerPort);

    string IP = GetPeerIP(m_Link.GetFd());

    //TODO 需要根据连接协议是SCPI还是自定义来创建不同的连接对象
    m_ExtConn = make_shared<WTConnector>(m_Link.GetFd(), IsLocalIP(IP.c_str()));

    //创建业务处理对象并注册命令处理函数
    m_Bsn.reset(new Business(this, m_ExtConn, m_Exclude, m_EvLoop));
    m_ExtConn->SetBsn(m_Bsn.get());

    //注册连接读事件
    WRSocket::SetNonblock(m_Link.GetFd());
    WRSocket::SetKeepAlive(m_Link.GetFd(), true, 1, 1, 3, 5);

    m_LinkIO.set_userdata(m_ExtConn.get());
    m_LinkIO.set<Service, &Service::LinkHandler>(this);
    m_LinkIO.start(m_Link.GetFd(), EV_READ);

    m_EvLoop.run();
}

//通过发送异步消息停止service
void Service::Stop(void)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "Service::Stop tid=%d\n", m_ThreadId);
    m_StopEv.send();
}

bool Service::IsLocalIP(const char *IP)
{
    if (!strcmp("127.0.0.1", IP))
    {
        return true;
    }
    return strcmp(WTDeviceInfo::Instance().GetDeviceDetailedInfo().IP, IP) == 0;
}

//检查要连接的从机是否就是本子仪器
bool Service::IsLocalAddr(const char *IP, int SubId)
{
    if (SubId != DevMgr::GetSrvID())
    {
        return false;
    }

    return IsLocalIP(IP);
}

int Service::CheckConnectResult(WRSocket &Sock)
{
    char Resp[] = "connection ok";
#define RESP_STRING_SIZE (sizeof(Resp) - 1)

    char Buff[RESP_STRING_SIZE + 1] = {0}; // 空间 = strlen(Resp) + 1
    int RecvSize = 0;
    int Ret = WT_OK;

    // 超时1s方式接收数据
    Ret = Sock.Recv(Buff, RESP_STRING_SIZE, RecvSize, 1000000);
    if (Ret != WT_OK)
    {
        if (Ret == WT_SOCKET_CLOSED)
        {
            Ret = WT_SOCKET_SLAVE_DATA_SEND_FAILED;
        }
        else if (Ret == WT_SOCKET_DATA_RECV_TIMEOUT)
        {
            Ret = WT_SOCKET_SLAVE_DATA_RECV_TIMEOUT;
        }
        return Ret;
    }


    if (strncmp((char *)Buff, Resp, RecvSize))
    {
        return WT_COMFIRM_ACCEPT_FAILED;
    }

    return WT_OK;
}

int Service::VerifyConnect(const char *IP, int Fd, int SubId)
{
    int Len = 0;
    char Buf[64] = {0};
    WRSocket Sock(Fd);

    //开始验证
    strcpy(Buf, "WT4xxConnection");
    int Ret = Sock.Send(Buf, 30, Len);
    if (Ret != WT_OK)
    {
        if (Ret == WT_SOCKET_CLOSED)
        {
            Ret = WT_SOCKET_SLAVE_DATA_SEND_FAILED;
        }
        return Ret;
    }

#define RECV_BUFF_LENGTH 16
    char RecvBuff[RECV_BUFF_LENGTH] = {0};
    // 超时1s方式接收数据
    Ret = Sock.Recv(RecvBuff, RECV_BUFF_LENGTH, Len, 1000000);
    if (Ret != WT_OK)
    {
        if (Ret == WT_SOCKET_CLOSED)
        {
            Ret = WT_SOCKET_SLAVE_DATA_SEND_FAILED;
        }
        else if (Ret == WT_SOCKET_DATA_RECV_TIMEOUT)
        {
            Ret = WT_SOCKET_SLAVE_DATA_RECV_TIMEOUT;
        }
        return Ret;
    }

    //加密数据并发送
    if (IsLocalAddr(IP, SubId))
    {
        strcpy(Buf, "WT4xxSEMultiConn");
    }
    else if (m_Link.GetType() == SRV_EXCLUDE_LINK )
    {
        bool Connected = false;
        for (const auto &Slave : m_SlaveConn)
        {
            Connected = Slave->IsSameDev(IP, SubId);
            if (Connected)
            {
                break;
            }
        }

        strcpy(Buf, Connected ? "WT4xxSEMultiConn" : "WT4xxSEForceConn");
    }
    else if (m_Link.GetType() == SRV_NORMAL_LINK)
    {
        strcpy(Buf, "WT4xxSNormalConn");
    }
    else if (m_Link.GetType() == SRV_SUB_LINK)
    {
        strcpy(Buf, "WT4xxSEMultiConn");
    }
    else
    {
        return WT_LINK_TYPE_ERROR;
    }

    Secure::Encrypt(reinterpret_cast<int *>(&Buf[0]), 4, reinterpret_cast<u32 *>(RecvBuff));
    memcpy(&Buf[16], &SubId, sizeof(int));

    Ret = Sock.Send(Buf, 30, Len);
    if (Ret != WT_OK)
    {
        if (Ret == WT_SOCKET_CLOSED)
        {
            Ret = WT_SOCKET_SLAVE_DATA_SEND_FAILED;
        }
        return Ret;
    }

    return CheckConnectResult(Sock);
}

//连接MIMO从机，并将其加入到事件队列
int Service::AddMimoDev(int Chain, const char *IP, int SubId)
{
    if (Chain <= 0)
    {
        WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "MIMO chain id error");
        return WT_STREAM_ID_ERROR;
    }

    int Port;
    DevConf::Instance().GetTcpPort(Port);

    int fd = WRSocket::ConnectAddr(IP, Port);
    if (fd < 0)
    {
        WTLog::Instance().LOGERR(WT_CONNECT_MIMO_FAILED, "connect MIMO DEV failed");
        return WT_CONNECT_MIMO_FAILED;
    }

    WRSocket::SetNonblock(fd);
    WRSocket::SetKeepAlive(fd, true, 1, 1, 3, 5);//m_Link.GetFd()
    int Ret = VerifyConnect(IP, fd, SubId); 
    if (Ret != WT_OK)
    {
        close(fd);
        WTLog::Instance().LOGERR(Ret, "verify connect failed");

        // 不允许直接返回WT_SOCKET_CLOSED, 这样会导致主机与上位机的链路断开.
        if (Ret == WT_SOCKET_CLOSED)
        {
            Ret = WT_SOCKET_SLAVE_IO_ERROR;
        }

        return Ret;
    }

    char localIP[16] = {0};
    int localPort = 0;
    Basefun::GetSockInfo(fd, localIP, localPort);
    UserConnMgr::Instance().SetMimoSlaveUserInfobyThreadId(std::this_thread::get_id(), Chain, localIP, localPort);

    //如果是新的chain则申请相应的资源，已存在相同chain的仪器则进行替换即可
    if (Chain > (signed)m_SlaveConn.size())
    {
        //TODO需要根据连接协议是SCPI还是自定义来创建不同的连接对象
        shared_ptr<Connector> Conn = make_shared<WTConnector>(fd, IsLocalIP(IP), m_SlaveConn.size());
        if (Conn == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc WRSocket failed");
            close(fd);
            return WT_ALLOC_FAILED;
        }

        Conn->SetDevAddr(IP, SubId);
        Conn->SetBsn(m_Bsn.get());
        Conn->SetVerified(true);   // VerifyConnect()已经确认连接成功, 后续无需再确认

        //将连接socket添加到loop事件中
        unique_ptr<wtev::io> w(new wtev::io(m_EvLoop));
        w->set_userdata(Conn.get());
        w->set<Service, &Service::LinkHandler>(this);
        w->start(fd, EV_READ);
        m_SlaveIO.push_back(move(w));

        m_Bsn->AddMimoDev(Conn);
        m_SlaveConn.push_back(move(Conn));

        std::unique_ptr<char[]> pBuf;
        int Length = sizeof(UserConnInfo)*3;
        pBuf.reset(new(std::nothrow) char[Length]);
        int InfoLen = 0;
        if(UserConnMgr::Instance().GetSlaveUserInfo(std::this_thread::get_id(), Chain, pBuf.get(), InfoLen) == WT_OK)
        {
            if(InfoLen == Length)
            {
                m_SlaveConn.back()->SendConnInfoToSalve(pBuf.get(), InfoLen);//发送主机对从机发起连接的连接端的信息到主机
            }
        }

        m_SlaveConn.back()->SetSlaveIFG(m_Bsn->GetVsg().GetVsgIfgEnable());
        //WTLog::Instance().WriteLog(LOG_DEBUG, "#####InfoLen=%d, (int)sizeof(UserConnInfo) = %d\n",InfoLen,(int)sizeof(UserConnInfo));
        //if(InfoLen == (int)sizeof(UserConnInfo))    //只是vsa的连接才去获取从机lic
        {
            m_SlaveConn.back()->SendGetLicInfoToSalve();
        }
    }
    else
    {
        auto iter = m_SlaveConn.begin();
        std::advance(iter, Chain - 1);
        close((*iter)->GetSockFd());
        (*iter)->ResetSockFd(fd);

        auto iter1 = m_SlaveIO.begin();
        std::advance(iter1, Chain - 1);
        (*iter1)->start(fd, EV_READ);

        std::unique_ptr<char[]> pBuf;
        int Length = sizeof(UserConnInfo)*3;
        pBuf.reset(new(std::nothrow) char[Length]);
        int InfoLen = 0;
        if(UserConnMgr::Instance().GetSlaveUserInfo(std::this_thread::get_id(), Chain, pBuf.get(), InfoLen) == WT_OK)
        {
            if(InfoLen == Length)
            {
                (*iter)->SendConnInfoToSalve(pBuf.get(), InfoLen);//发送主机对从机发起连接的连接端的信息到主机
            }
        }

        (*iter)->SetSlaveIFG(m_Bsn->GetVsg().GetVsgIfgEnable());
        //WTLog::Instance().WriteLog(LOG_DEBUG, "#####InfoLen=%d, (int)sizeof(UserConnInfo) = %d\n",InfoLen,(int)sizeof(UserConnInfo));
        //if(InfoLen == (int)sizeof(UserConnInfo))    ////只是vsa的连接才去获取从机lic
        {
            (*iter)->SendGetLicInfoToSalve();
        }
    }

    return WT_OK;
}

int Service::DelMimoDev(int Chain)
{
    if (Chain > (signed)m_SlaveConn.size() || Chain == 0)
    {
        WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "MIMO chain id error");
        return WT_STREAM_ID_ERROR;
    }

    //删除从机连接信息，置零
    UserConnMgr::Instance().DeleteSlaveUserConnInfo(std::this_thread::get_id(), Chain);

    auto iter = m_SlaveConn.begin();
    std::advance(iter, Chain - 1);

    m_Bsn->DelMimoDev(*iter);
    close((*iter)->GetSockFd());
    m_SlaveConn.erase(iter);

    auto iter1 = m_SlaveIO.begin();
    std::advance(iter1, Chain - 1);
    (*iter1)->stop();
    (*iter1)->set_userdata(nullptr);
    m_SlaveIO.erase(iter1);

    return WT_OK;
}

void Service::LinkHandler(wtev::io &watcher, int revents)
{
    Connector *Conn = static_cast<Connector *>(watcher.userdata);

    if (revents & EV_READ)
    {
        int Ret = Conn->RequestEvent();
        if (Ret == WT_SOCKET_CLOSED)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Service::LinkHandler WT_SOCKET_CLOSED tid=%d\n", m_ThreadId);
            CloseLink(*Conn);
            if(Conn->IsLinkToSlave())
            {
                CloseLink(*m_ExtConn);
            }
        }
        else if (Conn->IsExistCachedCmd()) // 缓存中存在命令可执行, 就手动生成一次事件
        {
            m_EvLoop.feed_fd_event((Conn->GetSock()).GetFd(), EV_READ);
        }
    }
}

//停止业务释放占用的资源并停止ev loop
void Service::StopCb(wtev::async &watcher, int revents)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "Service::StopCb tid=%d\n", m_ThreadId);
    (void)watcher;
    (void)revents;

    StopBns();

    m_StopEv.stop();

    m_EvLoop.break_loop();
}

void Service::CloseLink(Connector &Conn)
{
    //对外连接断开时停止服务
    if (Conn == *m_ExtConn)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Service::CloseLink, tid=%d\n", gettid());
        StopBns();

        m_EvLoop.break_loop();
    }
    else  //与MIMO从机的连接断开处理
    {
        auto IoIter = m_SlaveIO.begin();
        for (auto Iter = m_SlaveConn.begin(); Iter != m_SlaveConn.end(); Iter++, IoIter++)
        {
            //TODO 需要通知主机
            if (**Iter == Conn)
            {
                close(Conn.GetSockFd());
                m_Bsn->DelMimoDev(*Iter);
                m_SlaveConn.erase(Iter);

                (*IoIter)->stop();
                (*IoIter)->set_userdata(nullptr);
                m_SlaveIO.erase(IoIter);

                //上报从机错误IO信息（不再上报，仅记录）
                //不应记录错误信息到ErrorLib里，因为从机连接断开后，主机也会断开连接，导致API在本次连接中可能查询不到本次错误信息，
                //导致此错误在下一次连接时被查询到，meter认为出错而断开连接。
                //从另一个角度来说，仅和当次连接相关关的错误信息，都不应该在下一次连接时返回。
                //ErrorLib::Instance().AddErrCode(WT_SOCKET_SLAVE_IO_ERROR);
                WTLog::Instance().LOGERR(WT_SOCKET_SLAVE_IO_ERROR, "Slave link close");
                break;
            }
        }
    }
}

void Service::StopBns(void)
{
    if (NULL != m_Bsn)
    {
        //通知同个user的连接stop，并删除当前连接的连接信息
        UserConnMgr::Instance().NotifySrcWaitStop(std::this_thread::get_id());
        DevMgr::Instance().NotifyAllThread();
        UserConnMgr::Instance().DeleteUserConnChain(std::this_thread::get_id());
        if (BroadcastVsg::Instance().IsBroadcastUser())
        {
            BroadcastVsg::Instance().DelVsgUser();
        }
        m_Bsn->Terminate();

        for (auto &Item : m_SlaveConn)
        {
            close(Item->GetSockFd());
        }
        m_SlaveConn.clear();

        for (auto &Item : m_SlaveIO)
        {
            Item->stop();
            Item->set_userdata(nullptr);
        }
        m_SlaveIO.clear();

        // 清理对象, 防重入
        m_Bsn.reset(nullptr);
    }
}

void Service::SaveStackData(void)
{
    if(NULL != m_Bsn)
    {
        m_Bsn->GetVsa().SaveStackData();
        m_Bsn->GetVsg().SaveStackData();
    }
}
