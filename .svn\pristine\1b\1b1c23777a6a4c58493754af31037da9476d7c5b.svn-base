#include "mgmt_frame.h"

void mgmt_frame()
{
	std::vector<u8>rx_mac = { 0xc0,0xa5,0xdd,0xba,0xd0,0xf6 };
	std::vector<u8>tx_mac = { 0x64,0x6e,0x97,0xd3,0xec,0xeb };
	std::vector<u8>data = { 0xb4,0x00,0xe4,0x00 };
	//管理帧                    B7 B6 B5 B4 B3 B2 B1 B0      值
	//管理帧                    -- -- -- -- 0  0  0  0
	//管理帧                    *           0
	//Association request       0000                         0x00,0x00    连接要求                          
	//Association response      0001                         0x10,0x00    连接应答
	//Reassociation request     0010                         0x20,0x00    重新连接要求
	//Reassociation response    0011                         0x30,0x00    重新连接应答
	//Probe request             0100                         0x40,0x00    探查要求
	//Probe response            0101                         0x50,0x00    探查应答
	//Beacon                    1000                         0x80,0x00    导引信号
	//ATIM                      1001                         0x90,0x00    数据代传指示通知信号 Announcement Traffic Indication Message
	//Disassociation            1010                         0xa0,0x00    解除连接
	//Authentication            1011                         0xb0,0x00    身份验证
	//Deauthentication          1100                         0xc0,0x00    解除认证
	for (auto &item : rx_mac)
	{
		data.emplace_back(item);
	}

	for (auto &item : tx_mac)
	{
		data.emplace_back(item);
	}

	create_pcap("./SaveWave/RTS.pcap", (char *)&data[0], data.size());
}

int Generator_MF_AssociationRequest_Frame(MF_AssociationRequest* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};


int Generator_MF_ReassociationRequest_Frame(MF_ReassociationRequest* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};

int Generator_MF_AssociationResponse_Frame(MF_AssociationResponse* frm)
{
	frm->data.clear();
	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}
	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}
	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}
	Generator_FCS(frm->data, frm->frameCheckSeq);
	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
};

int Generator_MF_ReassociationResponse_Frame(MF_ReassociationResponse* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};

int Generator_MF_ProbeRequest_Frame(MF_ProbeRequest* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};



int Generator_MF_Authentication_Frame(MF_Authentication* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};

int Generator_MF_ATIM_Frame(MF_ATIM* frm)
{
	frm->data.clear();
	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	//HTControl
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}
	//for (int i = 0; i < frm->len; i++)
	//{
	//	frm->data.emplace_back(frm->FrameBody[i]);
	//}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
};


int Generator_MF_Disassociation_Frame(MF_Disassociation* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};


int Generator_MF_Deauthentication_Frame(MF_Deauthentication* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
};


int Generator_MF_ProbeResponse_Frame(MF_ProbeResponse* frm)
{
	frm->data.clear();
	unsigned char probe_response[] = {
/*		0x50, 0x5e, 0x2d, 0xe3, 0x40, 0xbd, 0x1e, 0xaa, 0x73, 0x92, 0x61, 0xaa, 0x5d, 0x2a, 0x2d, 0x74,
		0x8f, 0x41, 0xd1, 0x93, 0x9b, 0xd5, 0x12, 0x55, */0x43, 0xe7, 0xf2, 0xa7, 0xb6, 0x3d, 0xa7, 0x24,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff };

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	if (frm->UserDefineFrameBody)
	{
		for (int i = 0; i < frm->len; i++)
		{
			frm->data.emplace_back(frm->FrameBody[i]);
		}
	}
	else
	{
		//添加固定帧体
		for (auto &item : probe_response)
		{
			frm->data.emplace_back(item);
		}
	}
	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
	//framebody
	for (int i = 0; i <frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
};

int Generator_MF_ProbeResponse_Frame(MF_ProbeResponse* frm, MAC_Frame *pnParameters)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}
	
	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	if (!frm->bCrypto)
	{
		//更新TimeStamp
		int offset = 0;
		if (1 == nHTCOrder) offset = 4;
		frm->data[24 + offset] = (u8)(pnParameters->timeStamp.Timestamp & 0xff);
		frm->data[25 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff00) >> 8);
		frm->data[26 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff0000) >> 16);
		frm->data[27 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff000000) >> 24);
		frm->data[28 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff00000000) >> 32);
		frm->data[29 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff0000000000) >> 40);
		frm->data[30 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff000000000000) >> 48);
		frm->data[31 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff00000000000000) >> 56);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
}

int Generator_MF_Beacon_Frame(MF_Beacon* frm)
{
	frm->data.clear();
	unsigned char beacon[] = {
		//0x80,0x00,0x00,0x00,0xff,0xff,0xff,0xff,0xff,0xff,0x80,0x8f,0x1d,0xdf,0x87,0x6b,
		//0x80,0x8f,0x1d,0xdf,0x87,0x6b,0x50,0x6d,
		0x96,0x41,0x1a,0x31,0x14,0x01,0x00,0x00,0x64,0x00,0x11,0x14,0x00,0x0c,0x69,0x54,
		0x65,0x73,0x74,0x2d,0x4a,0x69,0x61,0x6f,0x66,0x75,0x01,0x08,0x82,0x84,0x8b,0x96,
		0x12,0x24,0x48,0x6c,0x03,0x01,0x01,0x05,0x04,0x00,0x01,0x00,0x00,0x07,0x06,0x43,
		0x4e,0x20,0x01,0x09,0x14,0x20,0x01,0x00,0x23,0x02,0x3f,0x00,0x46,0x05,0xf2,0x00,
		0x01,0x00,0x00,0x33,0x0a,0x08,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x2a,
		0x01,0x04,0x32,0x04,0x0c,0x18,0x30,0x60,0xdd,0x16,0x00,0x50,0xf2,0x01,0x01,0x00,
		0x00,0x50,0xf2,0x04,0x01,0x00,0x00,0x50,0xf2,0x04,0x01,0x00,0x00,0x50,0xf2,0x02,
		0x30,0x14,0x01,0x00,0x00,0x0f,0xac,0x04,0x01,0x00,0x00,0x0f,0xac,0x04,0x01,0x00,
		0x00,0x0f,0xac,0x02,0x00,0x00,0x0b,0x05,0x02,0x00,0x00,0x12,0x7a,0x2d,0x1a,0xef,
		0x11,0x17,0xff,0xff,0xff,0xff,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
		0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3d,0x16,0x01,0x05,0x06,0x00,0x00,
		0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
		0x00,0x4a,0x0e,0x14,0x00,0x0a,0x00,0x2c,0x01,0xc8,0x00,0x14,0x00,0x05,0x00,0x19,
		0x00,0xbf,0x0c,0xb1,0x01,0xc0,0x33,0xaa,0xff,0x18,0x06,0xaa,0xff,0x18,0x06,0xc0,
		0x05,0x00,0x00,0x00,0xaa,0xff,0x7f,0x08,0x00,0x00,0x08,0x00,0x00,0x00,0x00,0x00,
		0xdd,0x18,0x00,0x50,0xf2,0x02,0x01,0x01,0x00,0x00,0x03,0xa4,0x00,0x00,0x27,0xa4,
		0x00,0x00,0x42,0x43,0x5e,0x00,0x62,0x32,0x2f,0x00,0xdd,0x07,0x00,0x0c,0x43,0x0b,
		0x00,0x00,0x00,0xdd,0x21,0x00,0x0c,0xe7,0x08,0x00,0x00,0x00,0xbf,0x0c,0xb1,0x01,
		0xc0,0x33,0x2a,0xff,0x92,0x04,0x2a,0xff,0x92,0x04,0xc0,0x05,0x00,0x00,0x00,0x2a,
		0xff,0xc3,0x03,0x01,0x02,0x02
	};

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	if (frm->UserDefineFrameBody)
	{
		for (int i = 0; i < frm->len; i++)
		{
			frm->data.emplace_back(frm->FrameBody[i]);
		}
	}
	else
	{
		//添加固定帧体
		for (auto &item : beacon)
		{
			frm->data.emplace_back(item);
		}
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
	//framebody
	for (int i = 0; i <frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
};

int Generator_MF_Beacon_Frame(MF_Beacon* frm, MAC_Frame *pnParameters)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	if (!frm->bCrypto)
	{
		int offset = 0;
		if (1 == nHTCOrder) offset = 4;
		//更新TimeStamp
		frm->data[24 + offset] = (u8)(pnParameters->timeStamp.Timestamp & 0xff);
		frm->data[25 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff00) >> 8);
		frm->data[26 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff0000) >> 16);
		frm->data[27 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff000000) >> 24);
		frm->data[28 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff00000000) >> 32);
		frm->data[29 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff0000000000) >> 40);
		frm->data[30 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff000000000000) >> 48);
		frm->data[31 + offset] = (u8)((pnParameters->timeStamp.Timestamp & 0xff00000000000000) >> 56);
	}
	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;
};

int Generator_MF_ActionNoAck_Frame(MF_ActionNoAck* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	
	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}
	

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;
}

int Generator_MF_Action_Frame(MF_Action* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}
	
	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}
	
	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}

	return WT_ERR_CODE_OK;

}

int Generator_MF_TimingAdvertisement_Frame(MF_TimingAdvertisement* frm)
{
	frm->data.clear();
	unsigned char action[] = {
		/*0xd0, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc4, 0xe9, 0x84, 0x2f, 0x4d, 0xde,
		0xc4, 0xe9, 0x84, 0x2f, 0x4d, 0xde, 0x60, 0x3a,*/
		0x0d, 0x01, 0x82, 0x25, 0x00, 0x00, 0x1f, 0x4c, 0x00, 0x00, 0x00, 0xc4, 0xe9, 0x84, 0x2f, 0x4d,
		0xde, 0x61, 0x00, 0x00, 0x00, 0x12, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x84,
		0x16, 0xf9, 0xeb, 0xc9, 0xae, 0x4f, 0x00, 0x00, 0x00
	};

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	if (frm->UserDefineFrameBody)
	{
		for (int i = 0; i < frm->len; i++)
		{
			frm->data.emplace_back(frm->FrameBody[i]);
		}

		//更新TimeStamp
		if (frm->data.size() > 31)
		{
			frm->data[24] = (u8)frm->TimeStamp[0];
			frm->data[25] = (u8)frm->TimeStamp[1];
			frm->data[26] = (u8)frm->TimeStamp[2];
			frm->data[27] = (u8)frm->TimeStamp[3];
			frm->data[28] = (u8)frm->TimeStamp[4];
			frm->data[29] = (u8)frm->TimeStamp[5];
			frm->data[30] = (u8)frm->TimeStamp[6];
			frm->data[31] = (u8)frm->TimeStamp[7];
		}
	}
	else
	{
		//添加固定帧体
		for (auto &item : action)
		{
			frm->data.emplace_back(item);
		}

		//更新TimeStamp
		if (frm->data.size() > 31)
		{
			frm->data[24] = (u8)frm->TimeStamp[0];
			frm->data[25] = (u8)frm->TimeStamp[1];
			frm->data[26] = (u8)frm->TimeStamp[2];
			frm->data[27] = (u8)frm->TimeStamp[3];
			frm->data[28] = (u8)frm->TimeStamp[4];
			frm->data[29] = (u8)frm->TimeStamp[5];
			frm->data[30] = (u8)frm->TimeStamp[6];
			frm->data[31] = (u8)frm->TimeStamp[7];
		}
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;

}

int Generator_MF_Reserved_Frame(MF_Reserved* frm)
{
	frm->data.clear();

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address2)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address3)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->SeqCtl)
	{
		frm->data.emplace_back(item);
	}

	int nHTCOrder = (frm->frameControl[1] & 0b10000000) >> 7;
	if (1 == nHTCOrder)
	{
		for (auto &item : frm->HTControl)
		{
			frm->data.emplace_back(item);
		}
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;

}

int Generator_ExtF_DMGBeacon_Frame(ExtF_DMGBeacon* frm)
{
	frm->data.clear();
	unsigned char bmg_beacon[] = {
	/*0x0c, 0x80, 0x87, 0x39, 0x7f, 0x1e, 0x03, 0x38, 0x60, 0xa7,*/
	0x7c, 0x8f, 0x89, 0x01, 0x28, 0x14, 0x10, 0x25, 0x90, 0x01, 0x68, 0x6a, 0xac, 0x56,
	0x01, 0xcc, 0xb9, 0x31, 0x00, 0x39, 0x7d, 0x34, 0x12, 0x82, 0xa3, 0x20, 0xca, 0xc0, 0x9c, 0x97,
	0x44, 0xd1, 0x66, 0xeb, 0x46, 0x85, 0x03, 0x00, 0x21, 0x49, 0xcd, 0xcc, 0x9f, 0xcd, 0xc6, 0x01,
	0x9c, 0xef, 0x34, 0xf1, 0x00, 0x30, 0x80, 0x85, 0x18, 0x04, 0xd0, 0xe9, 0x50, 0x14, 0xf0, 0x08,
	0x00, 0xd8, 0xc9, 0x7c, 0x2f, 0xaf, 0x03, 0x06, 0x20, 0x06, 0xa4, 0xb7, 0x7f, 0x30, 0x77, 0x7e,
	0x43, 0x19, 0xf1, 0xd6, 0x28, 0x8f, 0x83, 0xb0, 0x9c, 0x18, 0x00, 0x44, 0x9a, 0x03, 0x3c, 0xc2,
	0xcb, 0x1b, 0x25, 0x01, 0x84, 0x98, 0xaa, 0x51, 0x00, 0x18, 0x0e, 0xcb, 0xe1, 0xdf, 0xa8, 0x1a,
	0x03, 0x2a, 0x00, 0x7a, 0x33, 0xdb, 0x8d, 0x01, 0x18, 0xf5, 0x42, 0x36, 0x12, 0x3f
	};
	//0x7c, 0x8f, 0x89, 0x01, 0x28, 0x14, 0x10, 0x25

	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	if (frm->UserDefineFrameBody)
	{
		for (int i = 0; i < frm->len; i++)
		{
			frm->data.emplace_back(frm->FrameBody[i]);
		}
	}
	else
	{
		//添加固定帧体
		for (auto &item : bmg_beacon)
		{
			frm->data.emplace_back(item);
		}
		//更新TimeStamp
		frm->data[10] = (u8)frm->TimeStamp[0];
		frm->data[11] = (u8)frm->TimeStamp[1];
		frm->data[12] = (u8)frm->TimeStamp[2];
		frm->data[13] = (u8)frm->TimeStamp[3];
		frm->data[14] = (u8)frm->TimeStamp[4];
		frm->data[15] = (u8)frm->TimeStamp[5];
		frm->data[16] = (u8)frm->TimeStamp[6];
		frm->data[17] = (u8)frm->TimeStamp[7];
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;

}


int Generator_ExtF_Reserved_Frame(ExtF_Reserved* frm)
{
	frm->data.clear();
	for (auto &item : frm->frameControl)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->duration)
	{
		frm->data.emplace_back(item);
	}

	for (auto &item : frm->Address1)
	{
		frm->data.emplace_back(item);
	}

	for (int i = 0; i < frm->len; i++)
	{
		frm->data.emplace_back(frm->FrameBody[i]);
	}

	Generator_FCS(frm->data, frm->frameCheckSeq);

	for (auto &item : frm->frameCheckSeq)
	{
		frm->data.emplace_back(item);
	}
	return WT_ERR_CODE_OK;

}