#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include <sstream>
#include <cmath>
#include <sys/time.h>
#include <unistd.h>
#include <strings.h>
#include <algorithm>
#include "basehead.h"
#include "vsahandler.h"
#include "commonhandler.h"
#include "vsa_ofdma_info.h"
#include "vsa_eht.h"
#include "tester.h"
#include "internal.h"
#include "tester_mt.h"
#include "wlan_encryption_api.h"
#include "alzdef.h"
#include "scpi_monitor.h"
#include "../../general/protocolsub.h"
#include "diskfun.h"
#include "wterror.h"
#include "wtlog.h"
#include "scpi_3gpp_alz_json_format.h"
#include "scpi_3gpp_base.h"

using namespace std;

// inline function
static scpi_result_t GetRstDoubleData(scpi_t *context, const char *ParamStr);
static scpi_result_t GetRstIntData(scpi_t *context, const char *ParamStr);
static scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb = false);
static scpi_result_t GetRstIntVectorData(scpi_t *context, const char *ParamStr, bool arb = false);
static scpi_result_t GetRstIntData(scpi_t *context, std::vector<const char *> &ParamStr);
// end inline function
static bool SupportMIMODemod(int demod)
{
    bool isOK = false;
    switch (demod)
    {
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        isOK = true;
        break;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        isOK = true;
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        isOK = true;
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        isOK = true;
        break;
    default:
        break;
    }

    return isOK;
}

enum SpectWideMode
{
    OFF = 0,
    MODE_1G,
    MODE_AUTO,
};

static bool SupportSpectrumWideMode(SPCIUserParam *attr, int WideMode)
{
    /*
        1、WIFI 8080, 160+160, 11B,BT,ZIGBEE 这些都不支持 spectrum wide mode
        2、其他协议，中心频点必须在一定范围内才支持
        3、采样率必须是4800MHz
    */
    bool isSuppotWide = false;
    if (WideMode == OFF)
    {
        return isSuppotWide;
    }
    int demod = attr->vsaParam.Demode;
    double CenterFreq = attr->vsaParam.Freq;
    double smpRate = attr->vsaParam.SamplingFreq;
    int BW = 20;
    int needCheckFreq = true;
    switch (demod)
    {
    case WT_DEMOD_11AG:
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11BE_20M:
        BW = 20;
        break;
    case WT_DEMOD_11N_40M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11BE_40M:
        BW = 40;
        break;
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11BE_80M:
        BW = 80;
        break;
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11BE_160M:
        BW = 160;
        break;
    case WT_DEMOD_11BE_320M:
        BW = 320;
        break;
    default:
        needCheckFreq = false;
        break;
    }

    if (needCheckFreq)
    {
#ifdef WT418_FW
        (void)BW;
        if(smpRate > 120 * MHz_API)
        {
            if (5180 * MHz_API - 0.1 * MHz_API < CenterFreq && CenterFreq < 7020 * MHz_API + 0.1 * MHz_API) // 宽带宽，支持频点范围5280-6920，采样率480M及其上
            {
                isSuppotWide = true;
            }
        }
#else
        // WTLog::Instance().WriteLog(LOG_DEBUG, "###Wide =%d, BW=%d######\n", WideMode, BW);
        if(smpRate > 240 * MHz_API)
        {
            if (WideMode == MODE_1G || (WideMode == MODE_AUTO && BW == 320)) // 1G; auto 320
            {
                if (CenterFreq > 5280 * MHz_API - 0.1 * MHz_API && CenterFreq < 6920 * MHz_API + 0.1 * MHz_API) // 宽带宽，支持频点范围5280-6920，采样率480M及其上
                {
                    isSuppotWide = true;
                }
            }
            else if (BW == 160) // auto ,160
            {
                if (CenterFreq > 5180 * MHz_API - 0.1 * MHz_API && CenterFreq < 7020 * MHz_API + 0.1 * MHz_API) // 宽带宽，支持频点范围，采样率480M及其上
                {
                    isSuppotWide = true;
                }
            }
            else // auto ,20/40/80
            {
                if (CenterFreq > 4900 * MHz_API - 0.1 * MHz_API && CenterFreq < 7300 * MHz_API + 0.1 * MHz_API) // 宽带宽，支持频点范围，采样率480M及其上
                {
                    isSuppotWide = true;
                }
            }
        }
#endif
    }
    return isSuppotWide;
}

static void SaveRawData(SPCIUserParam *attr, int DataType)
{
    if (1 != attr->m_save_avg_wave.enable)
    {
        return;
    }

    // 创建文件夹
    const std::string RawWaveDir = "/tmp/wave/rawwaveFiles/";
    std::string cmd = std::string("mkdir -p ") + RawWaveDir;
    do_system_cmd(cmd);

    int MaxCnt = attr->m_save_avg_wave.file_count;
    // 检查文件夹下的文件数量，如果达到指定数量RawFileMaxCnt，就先删除最旧的一个信号文件再保存~
    cmd = std::string("ls -l ") + RawWaveDir + std::string(" | wc -l");
    int Cnt = 0;
    std::string result = "0";
    do_system_cmd(cmd, result);
    if (result.length() > 0 && std::isdigit(result.at(0)))
    {
        Cnt = std::stoi(result) - 1;
        if (Cnt >= MaxCnt)
        {
            int last = Cnt - MaxCnt;
            stringstream bash_cmd;
            bash_cmd << std::string("cd ");
            bash_cmd << RawWaveDir;
            bash_cmd << std::string(" && ");
            bash_cmd << std::string("ls -tr");
            bash_cmd << std::string(" | head -");
            bash_cmd << last;
            bash_cmd << std::string(" | xargs rm");
            do_system_cmd(bash_cmd.str());
        }
    }

    int StartUs = 0;
    int EndUs = 0;
    int FileType = attr->m_save_avg_wave.file_type;
    string FileName = "";
    struct tm *Mtm;
    char NameBuf[256] = {0};
    struct timeval tv;
    time_t Now;

    time(&Now);
    Mtm = localtime(&Now);
    gettimeofday(&tv, NULL);

    sprintf(NameBuf, "TmpSavedata_%04d-%02d-%02d_%02d-%02d-%02d-%06ld.%s",
            1900 + Mtm->tm_year,
            Mtm->tm_mon + 1,
            Mtm->tm_mday,
            Mtm->tm_hour,
            Mtm->tm_min,
            Mtm->tm_sec,
            tv.tv_usec,
            (0 == FileType ? "csv" : "bwv"));

    FileName = RawWaveDir + std::string(NameBuf);
    WT_SaveSignal_V2(attr->ConnID, DataType, FileName.c_str(), StartUs, EndUs);
}

static void VsaAlzParamCorrection(SPCIUserParam *attr)
{
    //针对ICI和EQ的特殊处理
    if (attr->m_Devm.FEMMode)
    {
        attr->vsaAlzParam.analyzeParamWifi.ICISuppression = 0;
    }
}

int SetVsaAlzParam(SPCIUserParam *attr, int SendMonitor)
{
    int paramsSize = 0;
    int alzType = WT_ALZ_PARAM_FFT;
    int iRet = WT_ERR_CODE_OK;
    AnalyzeParam *alzParam = nullptr;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "attr->vsaParam.Demode = " << attr->vsaParam.Demode << endl;

    VsaAlzParamCorrection(attr);

    switch (attr->vsaParam.Demode)
    {
    case WT_DEMOD_CW:
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamFft);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamFft;
        break;
    case WT_DEMOD_BT:
        alzType = WT_ALZ_PARAM_BT;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamBt);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamBt;
        break;
    case WT_DEMOD_ZIGBEE:
        alzType = WT_ALZ_PARAM_ZIGBEE;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamZigBee);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamZigBee;
        break;
    case WT_DEMOD_GLE:
        alzType = WT_ALZ_PARAM_GLE;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamSparkLink);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamSparkLink;
        break;
    case ALG_3GPP_STD_WCDMA:
    case ALG_3GPP_STD_5G:
    case ALG_3GPP_STD_4G:
    case ALG_3GPP_STD_NB_IOT:
    {
        g_Alg3GPPSwitchCaseFlg = true;
        alzType = WT_ALZ_PARAM_3GPP;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParam3GPP);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParam3GPP;
#if 0
        // 下发API之前打印下发给下位机的分析参数
        Json::Value root;
        Format_3GPP_ALZ_Json(root, attr->vsaAlzParam.analyzeParam3GPP);
        Json::StyledWriter writer;
        string strJson = writer.write(root);
        WTLog::Instance().WriteLog(LOG_DEBUG, "3GPP ParamJson:\n%s\n", strJson.c_str());
#endif
        break;
    }
    case WT_DEMOD_LRWPAN_FSK:
    case WT_DEMOD_LRWPAN_OQPSK:
    case WT_DEMOD_LRWPAN_OFDM:
        alzType = WT_ALZ_PARAM_WSUN;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamWiSun);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamWiSun;
        break;
    case WT_DEMOD_ZWAVE:
        alzType = WT_ALZ_PARAM_ZWAVE;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamZWave);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamZWave;
        break;
    default:
        alzType = WT_ALZ_PARAM_WIFI;
        paramsSize = sizeof(attr->vsaAlzParam.analyzeParamWifi);
        alzParam = (AnalyzeParam *)&attr->vsaAlzParam.analyzeParamWifi;
        break;
    }

    do
    {
        attr->m_WTTimer.StartTimer("WT_SetGeneralAnalyzeParam");
        iRet = WT_SetGeneralAnalyzeParam(attr->ConnID, &attr->vsaAlzParam.commonAnalyzeParam);
        attr->m_WTTimer.StopTimer("WT_SetGeneralAnalyzeParam");
        if (WT_ERR_CODE_OK != iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_SetGeneralAnalyzeParam fail :" << iRet << endl;
            break;
        }

        // Set 11az analyze parameter
        if (WT_DEMOD_11AZ_20M <= attr->vsaParam.Demode && attr->vsaParam.Demode <= WT_DEMOD_11AZ_160M)
        {
            int Type = 3;
            attr->m_WTTimer.StartTimer("WT_SetExternAnalyzeParam");
            iRet = WT_SetExternAnalyzeParam(attr->ConnID, attr->vsaParam.Demode, Type, &(attr->m_AzAlyParam), sizeof(AlzParam11az));
            attr->m_WTTimer.StopTimer("WT_SetExternAnalyzeParam");
            if (WT_ERR_CODE_OK != iRet)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_Set11AZAnalyzeParam fail :" << iRet << endl;
                break;
            }
        }

        int timeOut = attr->vsaAlzParam.timeOut * 1000; // 转换成毫秒
        attr->m_WTTimer.StartTimer("WT_Analyze");
        WTLog::Instance().WriteLog(LOG_DEBUG, "attr->vsaParam.Demode = %d, attr->m_CMIMORefFile[0]=%d\n", attr->vsaParam.Demode, attr->m_CMIMORefFile[0]);
        if (SupportMIMODemod(attr->vsaParam.Demode) && attr->m_CMIMORefFile[0] != '\0')
        {
            
            iRet = WT_Analyze(
                attr->ConnID,
                alzType,
                alzParam,
                paramsSize,
                (const char *)attr->m_CMIMORefFile,
                attr->AlzFrameID,
                timeOut);
        }
        else
        {
            iRet = WT_Analyze(
                attr->ConnID,
                alzType,
                alzParam,
                paramsSize,
                nullptr,
                attr->AlzFrameID,
                timeOut);
        }
        attr->m_WTTimer.StopTimer("WT_Analyze");
        attr->Reset_LiteResult(LITE_ENUM::LITE_ENUM_Power, LITE_ENUM::LITE_VSG_ENUM_Power);
    } while (0);

    if (SendMonitor)
    {
        // 分析成功后处理监视机的内容
        shared_ptr<Monitor> MonitorObj = MonitorMgr::Instance().GetMonitorByPort(attr->GetMoniPort());
        if (MonitorObj)
        {
            // 发送分析参数到监视机
            MonitorObj->SendVsaAlzParam(alzType, &attr->vsaAlzParam.commonAnalyzeParam, sizeof(AlzParamComm), alzParam, paramsSize);
            // 发送结果到监视机
            MonitorObj->ParseMonitorCmd(attr->ConnID);
        }
    }

    // 检查是否需要保存分析的原始数据，需要则保存
    SaveRawData(attr, WT_SAVE_RAW_DATA);

    return iRet;
}

static void VsaAutoPowerCorrect(SPCIUserParam *attr)
{
    const double EPSILON = 1e-6;
    for (int i = 0; attr->m_vsaAutoPowerCorrectFileName[i].length() > 0 && i < attr->m_vsaAutoPowerCorrectFileName.size(); ++i)
    {
        if (attr->m_VSAPowerCorrectionJson[i].isArray() && attr->m_VSAPowerCorrectionJson[i].size() > 0)
        {
            for (u32 j = 0; j < attr->m_VSAPowerCorrectionJson[i].size(); j++)
            {
                if (0 == CompareDouble(attr->vsaParam.Freq, attr->m_VSAPowerCorrectionJson[i][j]["Freq"].asDouble(), EPSILON))
                {
                    attr->vsaParam.ExtPathLoss[i] = attr->m_VSAPowerCorrectionJson[i][j]["Correction"].asDouble();
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsaAutoPowerCorrect ExtGain = " << attr->vsaParam.ExtPathLoss[i] << std::endl;
                }
                if (0 != CompareDouble(attr->vsaParam.Freq2, 0.0, EPSILON) &&
                    0 == CompareDouble(attr->vsaParam.Freq2, attr->m_VSAPowerCorrectionJson[i][j]["Freq"].asDouble(), EPSILON))
                {
                    attr->vsaParam.ExtPathLoss2[i] = attr->m_VSAPowerCorrectionJson[i][j]["Correction"].asDouble();
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsaAutoPowerCorrect ExtGain2 = " << attr->vsaParam.ExtPathLoss2[i] << std::endl;
                }
            }
        }
    }
}

// 发送vsa参数到监视机
static void UpdateVsaParamToMonitor(SPCIUserParam *attr)
{
    shared_ptr<Monitor> MonitorObj = MonitorMgr::Instance().GetMonitorByPort(attr->GetMoniPort());
    if (MonitorObj)
    {
        // 发送vsa参数到监视机
        MonitorObj->SendParam((char *)&(attr->vsaParam), sizeof(VsaParameter), MON_VSA_STATUS);
    }
}

static int SetVsaAvgDataCapture(SPCIUserParam *attr, int mode)
{
    int iRet = WT_ERR_CODE_OK;
    // 参数合法性判断
    attr->MinAvgCount = (attr->MinAvgCount > attr->avgParam.AvgCount) ? attr->avgParam.AvgCount : attr->MinAvgCount;
    int AvgCount = 0;
    // 最多尝试两倍次数，避免陷入有帧无帧（等）切换，不断重置平均计数导致无限循环
    for (int LoopCount = 0; LoopCount <= (2 * attr->MinAvgCount); LoopCount++)
    {
        attr->m_WTTimer.StartTimer("WT_DataCapture");
        iRet = WT_DataCapture(attr->ConnID);
        attr->m_WTTimer.StopTimer("WT_DataCapture");
        if (iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_DataCapture fail :" << iRet << std::endl;
            break;
        }

        // 先获取一次平均次数，决定是否需分析数据
        attr->m_WTTimer.StartTimer("WT_GetCurrAverageCount");
        iRet = WT_GetCurrAverageCount(attr->ConnID, &AvgCount);
        attr->m_WTTimer.StopTimer("WT_GetCurrAverageCount");
        // WTLog::Instance().WriteLog(LOG_DEBUG, "WT_GetCurrAverageCount = %d\n", AvgCount);
        if (iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_GetCurrAverageCount fail :" << iRet << std::endl;
            break;
        }

        iRet = WT_ERR_CODE_OK;
        // 若开启了滑动平均，循环过程仍需每次分析，但不发送结果给从机
        if (AvgCount < (attr->MinAvgCount - 1))
        {
            iRet = SetVsaAlzParam(attr, false);
            if (iRet)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetVsaAlzParam fail :" << iRet << std::endl;
                break;
            }
        }
        // 最后一次默认采集完成后直接分析数据，开启平均后不能多次分析同一份数据
        else
        {
            if (0 == mode)
            {
                iRet = SetVsaAlzParam(attr, true);
                if (WT_ERR_CODE_OK != iRet)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetVsaAlzParam fail :" << iRet << endl;
                }
            }
            break;
        }
    }
    return iRet;
}
static int SetNrAlzParam(int connID, int demod, AnalyzeParam *analyzeParams, int paramsSize)
{
    if (ALG_3GPP_STD_5G == demod)
    {
        return WT_SetAlzParam(connID, WT_ALZ_PARAM_3GPP, analyzeParams, paramsSize);
    }

    return WT_ERR_CODE_OK;
}

static int SetVsaCapture(scpi_t *context, int mode)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_ERR_CODE_OK;
    TimeTick tick("SetVsaCapture");
    do
    {
        int WideBandSetResult = WT_ERR_CODE_OK;
        attr->m_WTTimer.StartTimer("WT_SetWideSpectrumEnable");
        if (attr->mSpectrumWideMode && SupportSpectrumWideMode(attr, attr->mSpectrumWideMode))
        {
            WideBandSetResult = WT_SetWideSpectrumEnable(attr->ConnID, attr->mSpectrumWideMode);
        }
        else
        {
            WideBandSetResult = WT_SetWideSpectrumEnable(attr->ConnID, 0);
        }
        attr->m_WTTimer.StopTimer("WT_SetWideSpectrumEnable");

        // 自动线衰修正
        VsaAutoPowerCorrect(attr);
        attr->m_WTTimer.StartTimer("WT_SetVSA_V2");
        iRet = WT_SetVSA_V2(attr->ConnID, &(attr->vsaParam), &attr->vsaExtParam);
        attr->m_WTTimer.StopTimer("WT_SetVSA_V2");
        IF_BREAK(iRet);
        attr->m_WTTimer.StartTimer("WT_GetVSAParameter_V2");
        iRet = WT_GetVSAParameter_V2(attr->ConnID, 0, &attr->vsaParam, &attr->vsaExtParam);
        attr->m_WTTimer.StopTimer("WT_GetVSAParameter_V2");
        IF_BREAK(iRet);

        // wide mode开关要判断频点，如果配置参数前打开失败，则重配一次，保证功能立即生效
        if (WideBandSetResult && attr->mSpectrumWideMode && SupportSpectrumWideMode(attr, attr->mSpectrumWideMode))
        {
            attr->m_WTTimer.StartTimer("WT_SetWideSpectrumEnable");
            iRet = WT_SetWideSpectrumEnable(attr->ConnID, attr->mSpectrumWideMode);
            attr->m_WTTimer.StopTimer("WT_SetWideSpectrumEnable");
        }

        SetNrAlzParam(attr->ConnID, attr->vsaParam.Demode, (AnalyzeParam *)&attr->vsaAlzParam.analyzeParam3GPP, sizeof(attr->vsaAlzParam.analyzeParam3GPP));

        // 发送vsa参数到监视机
        UpdateVsaParamToMonitor(attr);

        if (attr->avgParam.AvgCount > 1 && attr->avgParam.AvgType == WT_CAPTURE_MOVING_AVERAGE)
        {
            iRet = SetVsaAvgDataCapture(attr, mode);
        }
        else
        {
            attr->m_WTTimer.StartTimer("WT_DataCapture");
            iRet = WT_DataCapture(attr->ConnID);
            attr->m_WTTimer.StopTimer("WT_DataCapture");
            if (WT_ERR_CODE_OK != iRet)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_DataCapture fail :" << iRet << endl;
                break;
            }

            // 默认采集完成后直接分析数据
            // 异步启动时，默认不分析，但若开启了滑动平均，仍需每次分析
            if (0 == mode)
            {
                iRet = SetVsaAlzParam(attr);
                if (WT_ERR_CODE_OK != iRet)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetVsaAlzParam fail :" << iRet << endl;
                    break;
                }
            }
        }
    } while (0);

    return iRet;
}

static int VsaCaptureThread(scpi_t *context, int mode)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    TimeTick tick("VsaCaptureThread");

    attr->m_VsaThreadStatus = THREAD_RUNNING;

    iRet = SetVsaCapture(context, mode);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetVsaCapture result = " << iRet << std::endl;

    attr->m_VsaThreadStatus = THREAD_STOPPED;

    return iRet;
}

scpi_result_t SetVsa8080Mode(scpi_t *context)
{
    int ParamVal = 0;
    if (!SCPI_ParamInt(context, &ParamVal, true))
    {
        return SCPI_RES_ERR;
    }
    // attr->vsaExtParam.WIFI8080DulPortMode = ParamVal;

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsa8080Port(scpi_t *context)
{
    int port[WT_SUB_TESTER_INDEX_MAX / 2 + 1] = {WT_PORT_RF1};
    EMPTY_PARAM_ERROR(context);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    // 必须成对出现
    ILLEGAL_PARAM_RETURN(0 != context->parser_state.numberOfParameters % 2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        if (!SCPI_ParamInt(context, &port[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsaExtParam.VsaRfPort[i] = port[i];
        ILLEGAL_PARAM_RETURN(port[i] < WT_PORT_OFF || port[i] >= WT_PORT_MAX);
    }

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaExtParam.VsaRfPort" << i << "=" << port[i] << endl;
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsa8080RefPower(scpi_t *context)
{
    double power[WT_SUB_TESTER_INDEX_MAX / 2 + 1] = {0};
    EMPTY_PARAM_ERROR(context);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    // 必须成对出现
    ILLEGAL_PARAM_RETURN(0 != context->parser_state.numberOfParameters % 2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        if (!SCPI_ParamDouble(context, &power[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsaExtParam.RfPort_MaxPower[i] = power[i];
    }

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaExtParam.MaxPower" << i << " = " << power[i] << endl;
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsa8080TriggerLevel(scpi_t *context)
{
    double power[WT_SUB_TESTER_INDEX_MAX / 2 + 1] = {0};
    EMPTY_PARAM_ERROR(context);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    // 必须成对出现
    ILLEGAL_PARAM_RETURN(0 != context->parser_state.numberOfParameters % 2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        if (!SCPI_ParamDouble(context, &power[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsaExtParam.RfPort_TrigLevel[i] = power[i];
    }

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaExtParam.TrigLevel" << i << " = " << power[i] << endl;
    }

    return SCPI_ResultOK(context);
}

scpi_result_t GetVsa8080Mode(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    SCPI_ResultInt(context, attr->vsaExtParam.WIFI8080DulPortMode);

    return SCPI_RES_OK;
}

scpi_result_t GetVsa8080Port(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultInt(context, attr->vsaExtParam.VsaRfPort[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsa8080RefPower(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, attr->vsaExtParam.RfPort_MaxPower[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsa8080TriggerLevel(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, attr->vsaExtParam.RfPort_TrigLevel[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t SetVsaFrequency(scpi_t *context)
{
    scpi_number_t Freq[2];
    EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < context->parser_state.numberOfParameters && i < 2; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &Freq[i], true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(Freq[i].value > 8000 * MHz_API || Freq[i].value < 0) // 频点设置大于8G或者小于0，视为超范围
    }
    if (context->parser_state.numberOfParameters > 1)
    {
        attr->vsaParam.Freq = Freq[0].value;
        attr->vsaParam.Freq2 = Freq[1].value;
    }
    else
    {
        attr->vsaParam.Freq = Freq[0].value;
        attr->vsaParam.Freq2 = 0.0;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Freq=" << attr->vsaParam.Freq / MHz_API << "MHz ,"
         << "Freq2=" << attr->vsaParam.Freq2 / MHz_API << "MHz" << endl;

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaMaxPower(scpi_t *context)
{
    scpi_number_t power[WT_SUB_TESTER_INDEX_MAX];
    EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &power[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsaParam.MaxPower[i] = power[i].value;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "MaxPower=" << attr->vsaParam.MaxPower[0] << " dBm" << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaSamplingTime(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.SmpTime = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SmpTime=" << attr->vsaParam.SmpTime << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaPort(scpi_t *context)
{
    EMPTY_PARAM_ERROR(context);
    int port[WT_SUB_TESTER_INDEX_MAX] = {WT_PORT_OFF};

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamInt(context, &port[i], true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(port[i] < WT_PORT_OFF || port[i] >= WT_PORT_MAX);
        attr->vsaParam.RfPort[i] = port[i];
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RfPort=" << attr->vsaParam.RfPort[0] << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTriggerType(scpi_t *context)
{
    int ParamVal = WT_TRIG_TYPE_FREE_RUN_API;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_TRIG_TYPE_FREE_RUN_API || ParamVal > WT_TRIG_TYPE_IF_API);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.TrigType = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TrigType=" << attr->vsaParam.TrigType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTriggerLevel(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.TrigLevel = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TrigLevel=" << attr->vsaParam.TrigLevel << " dB" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTriggerTimeOut(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.TrigTimeout = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TrigTimeout=" << attr->vsaParam.TrigTimeout << " S" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTriggerPreTime(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.TrigPretime = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TrigPretime=" << attr->vsaParam.TrigPretime << " S" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTriggerGapTime(scpi_t *context)
{
    scpi_number_t ParamVal;
    int Change = false;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value < 1e-7 || ParamVal.value > 5e-3);

        if (CompareDouble(attr->vsaTrigParam.GapTime, ParamVal.value, 1e-8) != 0)
        {
            Change = true;
        }
        attr->vsaTrigParam.GapTime = ParamVal.value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Trig GapTime=" << attr->vsaTrigParam.GapTime << " S" << endl;
    } while (0);

    if (Change)
    {
        WT_SetVSATrigParam(attr->ConnID, &(attr->vsaTrigParam));
    }
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTriggerEdge(scpi_t *context)
{
    int ParamVal = WT_TRIG_DEGE_POSITIVE_API;
    int Change = false;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_TRIG_DEGE_POSITIVE_API || ParamVal > WT_TRIG_DEGE_NEGATIVE_API);

        if (attr->vsaTrigParam.Edge != ParamVal)
        {
            Change = true;
        }
        attr->vsaTrigParam.Edge = ParamVal;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Trig Edge=" << attr->vsaTrigParam.Edge << endl;
    } while (0);

    if (Change)
    {
        WT_SetVSATrigParam(attr->ConnID, &(attr->vsaTrigParam));
    }
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaFreqOffset(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > 60 * MHz_API || ParamVal.value < -60 * MHz_API) // 判断可设置范围

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.FreqOffset = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FreqOffset=" << attr->vsaParam.FreqOffset / MHz_API << " MHz" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzFrameIndex(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->AlzFrameID = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AlzFrameID=" << attr->AlzFrameID << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaTimeOutWaittingSec(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.TimeoutWaiting = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TimeoutWaiting=" << attr->vsaParam.TimeoutWaiting << " S" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzBandwidthMode(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_USER_DEFINED > ParamVal || WT_AUTO_DETECT < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.AutoDetect = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.AutoDetect=" << ParamVal << endl;

    } while (0);
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaSampleRate(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > MAX_SMAPLE_RATE_API || ParamVal.value < 0);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.SamplingFreq = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SamplingFreq=" << attr->vsaParam.SamplingFreq / MHz_API << " MHz" << endl;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaSampleRateMode(scpi_t *context)
{
    int ParamVal = 0;
    if (!SCPI_ParamInt(context, &ParamVal, true))
    {
        return SCPI_RES_ERR;
    }
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->VsaSampleRateMode = ParamVal;
    attr->vsaParam.SamplingFreq = GetRealSampleRate(ParamVal, attr->vsaParam.Demode);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "###attr->vsaParam.SamplingFreq = " << attr->vsaParam.SamplingFreq << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaMaxIFG(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.MaxIFGGap = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaParam.MaxIFGGap=" << attr->vsaParam.MaxIFGGap << " S" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaExtGain(scpi_t *context)
{
    scpi_number_t gain[WT_SUB_TESTER_INDEX_MAX];
    EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int ID = 1;
    double *pathLoss = nullptr;
    SCPI_CommandNumbers(context, &ID, 1);
    if (ID > 2)
    {
        ID = 2;
    }
    if (ID < 1)
    {
        ID = 1;
    }
    pathLoss = (ID <= 1 ? attr->vsaParam.ExtPathLoss : attr->vsaParam.ExtPathLoss2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &gain[i], true))
        {
            return SCPI_RES_ERR;
        }
        pathLoss[i] = gain[i].value;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << ID << ", ExtPathLoss=" << pathLoss[0] << " dB" << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzTimeOut(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.timeOut = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaAlzParam timeout = " << attr->vsaAlzParam.timeOut << " S" << std::endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzDemod(scpi_t *context)
{
    int ParamVal = WT_DEMOD_11AG;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.Demode=" << attr->vsaAlzParam.analyzeParamWifi.Demode << endl;
        if (ParamVal == WT_DEMOD_LRWPAN_FSK || ParamVal == WT_DEMOD_LRWPAN_OQPSK || ParamVal == WT_DEMOD_LRWPAN_OFDM)
        {
            attr->vsaAlzParam.analyzeParamWiSun.Demode = ParamVal;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.Demode = ParamVal;
        }

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzSpectrumWideMode(scpi_t *context)
{
    int ParamVal = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        // 0:off;
        // 1:1G模式，固定左右偏移380M,显示正负500M的频谱
        // 2:auto模式：当带宽为20,40,80M时，不需左右偏移，和正常的SISO采样一样；当带宽为160M，左右偏移280M，显示±320M的频谱（和3系列一样）；
        //       当带宽为320M,左右偏移380M，显示±500M的频谱
        if (ParamVal < 0 || ParamVal > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (ParamVal == 0 || (ParamVal && SupportSpectrumWideMode(attr, ParamVal)))
        {
            attr->mSpectrumWideMode = ParamVal;
        }
        else
        {
            attr->mSpectrumWideMode = 0;
            iRet = WT_WIDE_BAND_FREQ_ERROR;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "attr->mSpectrumWideMode = " << attr->mSpectrumWideMode << endl;
        // 先不配置给固件，等启动时配置
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaDemod(scpi_t *context)
{
    int ParamVal = WT_DEMOD_11AG;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaParam.Demode = ParamVal;

        if (IsAlg3GPPStandardType(attr->vsaParam.Demode))
        {
            attr->vsaAlzParam.Reset_AlzParam(attr->vsaAlzParam.analyzeParam3GPP, ParamVal, false, false);
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaParam.Demode=" << attr->vsaParam.Demode << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaDCOffsetI(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaExtParam.DCOffsetI = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaExtParam.DCOffsetI=" << attr->vsaExtParam.DCOffsetI << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaDCOffsetQ(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaExtParam.DCOffsetQ = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsaExtParam.DCOffsetQ=" << attr->vsaExtParam.DCOffsetQ << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzIQReversedFlag(scpi_t *context)
{
    int ParamVal = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        if (ParamVal < WT_IQ_IQReversion_DISABLED || ParamVal > WT_IQ_IQReversion_ENABLED)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.commonAnalyzeParam.IQReversion = ParamVal;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzPacketStartPoint(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ParamVal = (0 > ParamVal ? 0 : ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.commonAnalyzeParam.ManualPktStart = ParamVal;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzFilterTime(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ParamVal = (0 > ParamVal ? 0 : ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.commonAnalyzeParam.FilterPktByTime = ParamVal;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzCCDFFlag(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ParamVal = (0 == ParamVal ? 0 : 1);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.commonAnalyzeParam.CcdfFlag = ParamVal;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzSpectrumFlag(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ParamVal = (0 == ParamVal ? 0 : 1);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.commonAnalyzeParam.SpectrumFlag = ParamVal;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzIQSwapFlag(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal != WT_IQ_SWAP_DISABLED && ParamVal != WT_IQ_SWAP_ENABLED);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.commonAnalyzeParam.IQSwap = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzFreqOffset(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > 60 * MHz_API || ParamVal.value < -60 * MHz_API); // 判断可设置范围
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.commonAnalyzeParam.FreqOffset = ParamVal.value;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaOFDMPhaseTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_PH_CORR_OFF || WT_PH_CORR_MOVING_AVG < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.PhsCorrMode = ParamVal;
            cout << "analyzeParamWiSun.PhsCorrMode=" << attr->vsaAlzParam.analyzeParamWiSun.PhsCorrMode << endl;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.PhsCorrMode = ParamVal;
            cout << "analyzeParamWifi.PhsCorrMode=" << attr->vsaAlzParam.analyzeParamWifi.PhsCorrMode << endl;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.PhsCorrMode=" << attr->vsaAlzParam.analyzeParamWifi.PhsCorrMode << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaOFDMChannelEstimation(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_CH_EST_RAW || WT_CH_EST_RAW_FULL < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.ChEstimate = ParamVal;
            cout << "analyzeParamWiSun.ChEstimate=" << attr->vsaAlzParam.analyzeParamWiSun.ChEstimate << endl;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.ChEstimate = ParamVal;
            cout << "analyzeParamWifi.ChEstimate=" << attr->vsaAlzParam.analyzeParamWifi.ChEstimate << endl;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.ChEstimate=" << attr->vsaAlzParam.analyzeParamWifi.ChEstimate << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaOFDMTimingTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_SYM_TIM_OFF || WT_SYM_TIM_ON < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.SynTimeCorr = ParamVal;
            cout << "analyzeParamWiSun.SynTimeCorr=" << attr->vsaAlzParam.analyzeParamWiSun.SynTimeCorr << endl;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.SynTimeCorr = ParamVal;
            cout << "analyzeParamWifi.SynTimeCorr=" << attr->vsaAlzParam.analyzeParamWifi.SynTimeCorr << endl;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.SynTimeCorr=" << attr->vsaAlzParam.analyzeParamWifi.SynTimeCorr << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaOFDMFrequencySync(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_FREQ_SYNC_SHORT_TRAIN || WT_FREQ_SYNC_AUTO < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.FreqSyncMode = ParamVal;
            cout << "analyzeParamWiSun.FreqSyncMode=" << attr->vsaAlzParam.analyzeParamWiSun.FreqSyncMode << endl;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.FreqSyncMode = ParamVal;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.FreqSyncMode=" << attr->vsaAlzParam.analyzeParamWifi.FreqSyncMode << endl;
        }

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaOFDMAmplitudeTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_AMPL_TRACK_OFF || WT_AMPL_TRACK_ON < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.AmplTrack = ParamVal;
            cout << "analyzeParamWiSun.AmplTrack=" << attr->vsaAlzParam.analyzeParamWiSun.AmplTrack << endl;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.AmplTrack = ParamVal;
            cout << "analyzeParamWifi.AmplTrack=" << attr->vsaAlzParam.analyzeParamWifi.AmplTrack << endl;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.AmplTrack=" << attr->vsaAlzParam.analyzeParamWifi.AmplTrack << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaOFDMMimoMaxPowerDiff(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 99 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.MimoMaxPowerDiff = ParamVal;

        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.MimoMaxPowerDiff=" << attr->vsaAlzParam.analyzeParamWifi.MimoMaxPowerDiff << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaDSSSEvmMethod(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_11B_STANDARD_TX_ACC || WT_11B_STANDARD_2016_TX_ACC < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.Method11b = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.Method11b=" << attr->vsaAlzParam.analyzeParamWifi.Method11b << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaDSSSDCRemove(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_DC_REMOVAL_OFF || WT_DC_REMOVAL_ON < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.DCRemoval = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.DCRemoval=" << attr->vsaAlzParam.analyzeParamWifi.DCRemoval << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaDSSSEqualizerTypes(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(
            WT_EQ_OFF != ParamVal &&
            WT_EQ_5_TAPS != ParamVal &&
            WT_EQ_7_TAPS != ParamVal &&
            WT_EQ_9_TAPS != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.EqTaps = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.EqTaps=" << attr->vsaAlzParam.analyzeParamWifi.EqTaps << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaDSSSPhaseTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_PH_CORR_11b_OFF != ParamVal && WT_PH_CORR_11b_ON != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.PhsCorrMode11B = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.PhsCorrMode11B=" << attr->vsaAlzParam.analyzeParamWifi.PhsCorrMode11B << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsa11nSpectrumMaskVersion(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(0 != ParamVal && 1 != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.SpectrumMaskVersion = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.SpectrumMaskVersion=" << attr->vsaAlzParam.analyzeParamWifi.SpectrumMaskVersion << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzClockRate(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_CLOCK_RATE_1 != ParamVal && WT_CLOCK_RATE_1_2 != ParamVal && WT_CLOCK_RATE_1_4 != ParamVal && WT_CLOCK_RATE_1_5 != ParamVal && WT_CLOCK_RATE_1_8 != ParamVal && WT_CLOCK_RATE_1_10 != ParamVal && WT_CLOCK_RATE_1_20 != ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaAlzParam.analyzeParamWiSun.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.ClockRate = ParamVal;
            cout << "analyzeParamWiSun.ClockRate=" << attr->vsaAlzParam.analyzeParamWiSun.ClockRate << endl;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.ClockRate = ParamVal;

            cout << "analyzeParamWifi.ClockRate=" << attr->vsaAlzParam.analyzeParamWifi.ClockRate << endl;
        }

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzWifiMimoMode(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(0 != ParamVal && 1 != ParamVal);

        int StreamID = 0;
        SCPI_ParamInt(context, &StreamID, false);
        ILLEGAL_PARAM_RETURN(0 > StreamID || 8 <= StreamID);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.MIMOAnalysisMode = ParamVal;
        attr->vsaAlzParam.analyzeParamWifi.SpecialAnalyzeStream = StreamID;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.MIMOAnalysisMode=" << attr->vsaAlzParam.analyzeParamWifi.MIMOAnalysisMode <<" ,stream=" << StreamID << std::endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzPSDUBits(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        if (ParamVal)
        {
            ParamVal = 1;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.FullCRCFlag = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.FullCRCFlag=" << (int)attr->vsaAlzParam.analyzeParamWifi.FullCRCFlag << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzOFDMNonHTDuplicateBW(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.NonHTDupBW = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.NonHTDupBW=" << attr->vsaAlzParam.analyzeParamWifi.NonHTDupBW << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzOFDMPreambleAverage(scpi_t *context)
{

    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 1 < ParamVal);
#if 0 // Preamble Avg SCPI,任意配置都回复‘处理OK无异常’，在新版手册上备注命令已过期，引导客户使用EQ相关命令
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.PreambleAverage = ParamVal;
        WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.PreambleAverage = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.PreambleAverage);
#endif

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzOFDMIQCompensation(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 1 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.IQCompensation = ParamVal;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.IQCompensation = ParamVal;
        }

        // WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.IQCompensation = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.IQCompensation);

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzOFDMEqualizerSmoothing(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 2 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->m_TestMode == TESTMODE_RD && attr->m_Devm.FEMMode == 0)
        {
            switch (ParamVal)
            {
            case 0:
                ParamVal = 2;
                break;
            case 1:
                ParamVal = 1;
                break;
            case 2:
                ParamVal = 2;
                break;
            default:
                break;
            }
        }
        attr->vsaAlzParam.analyzeParamWifi.EqualizerSmoothing = ParamVal;

        WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.EqualizerSmoothing = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.EqualizerSmoothing);

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzOFDMICISuppression(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 2 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.ICISuppression = ParamVal;
        WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.ICISuppression = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.ICISuppression);
        // static void ParamCorrection(SPCIUserParam *attr)
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzOFDMLdpcDecodeIterationTimes(scpi_t *context)
{
    int ParamVal = 3;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 1 || 10 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.LdpcDecodeIterationTimes = ParamVal;

        // WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.LdpcDecodeIterationTimes  = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.LdpcDecodeIterationTimes);

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzObwCalculateMethod(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 1 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.OBWCalcFlag = ParamVal;

        // WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.OBWCalcFlag = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.OBWCalcFlag);

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzHardwareDecodeSwitch(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || 1 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWifi.HardwareDecodeFlag = ParamVal;

        // WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeParamWifi.HardwareDecodeFlag = %d\n\n", attr->vsaAlzParam.analyzeParamWifi.HardwareDecodeFlag);

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzHMatrix(scpi_t *context)
{
    do
    {
        int Enable = 0;
        if (!SCPI_ParamInt(context, &Enable, true))
        {
            return SCPI_RES_ERR;
        }
        // 开关，0：OFF；1：ON
        ILLEGAL_PARAM_RETURN(Enable < 0 || 1 < Enable);

        int RxAntNum = 0;
        int TxAntNum = 0;
        Complex Mat[8][8] = {0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (Enable == 1)
        {
            // 使能H矩阵功能是检查lic，如果不存在，直接不给配置~
            if (attr->CheckBusinessLic(WT_CHANNEL_MATRIX_API) != true)
            {
                SCPI_ErrorPush(context, WT_ERR_CODE_LICENSE_ERROR);
                return SCPI_RES_ERR;
            }

            if (!SCPI_ParamInt(context, &RxAntNum, true))
            {
                return SCPI_RES_ERR;
            }
            // Rx天线数，0~8
            ILLEGAL_PARAM_RETURN(RxAntNum < 0 || 8 < RxAntNum);

            if (!SCPI_ParamInt(context, &TxAntNum, true))
            {
                return SCPI_RES_ERR;
            }
            // Tx天线数，0~8
            ILLEGAL_PARAM_RETURN(TxAntNum < 0 || 8 < TxAntNum);

            int Cnt = RxAntNum * TxAntNum * 2; // complex 2个double
            if (Cnt > 0)
            {
                double Value[Cnt] = {0};
                for (int i = 0; i < Cnt; i++)
                {
                    if (!SCPI_ParamDouble(context, &Value[i], true))
                    {
                        return SCPI_RES_ERR;
                    }
                }

                int index = 0;
                for (int i = 0; i < RxAntNum; i++)
                {
                    for (int j = 0; j < TxAntNum; j++)
                    {
                        Mat[i][j][0] = Value[index++];
                        Mat[i][j][1] = Value[index++];
                    }
                }
            }
        }

        attr->vsaAlzParam.commonAnalyzeParam.HmatrixEnable = Enable;
        attr->vsaAlzParam.commonAnalyzeParam.HmatRxAntennaNum = RxAntNum;
        attr->vsaAlzParam.commonAnalyzeParam.HmatTxAntennaNum = TxAntNum;
        memcpy((char *)attr->vsaAlzParam.commonAnalyzeParam.HMatValue, (char *)Mat, sizeof(Mat));

        // WTLog::Instance().WriteLog(LOG_DEBUG, "Hmat = %d,%d,%d,(%lf,%lf),%p\n\n", attr->vsaAlzParam.commonAnalyzeParam.HmatrixEnable,attr->vsaAlzParam.commonAnalyzeParam.HmatRxAntennaNum,
        // attr->vsaAlzParam.commonAnalyzeParam.HmatTxAntennaNum,attr->vsaAlzParam.commonAnalyzeParam.HMatValue[0][0][0],
        // attr->vsaAlzParam.commonAnalyzeParam.HMatValue[0][0][1],attr);

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacSecurityMode(scpi_t *context)
{
    do
    {
        char ModeName[256] = {0};
        size_t copyLen = 0;

        if (!SCPI_ParamCopyText(context, ModeName, sizeof(ModeName) - 1, &copyLen, true))
        {
            return SCPI_RES_ERR;
        }

        bool FindCorrectAlg = false;
        std::vector<string> SecurityAlgorithmName;
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_NONE);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_WEP40);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_WEP104);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_WEP128);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_TKIP);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_CCMP128);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_CCMP256);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_GCMP128);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_GCMP256);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_SM4_OFB);
        SecurityAlgorithmName.push_back(WT_MAC_ENCRYPT_SM4_GCM);
        for (auto &Item : SecurityAlgorithmName)
        {
            if (strcasecmp(Item.c_str(), ModeName) == 0)
            {
                FindCorrectAlg = true;
                break;
            }
        }
        ILLEGAL_PARAM_RETURN(FindCorrectAlg == false);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        // 开启解密功能，需要先判断lic
        if (strcasecmp(WT_MAC_ENCRYPT_NONE, ModeName) != 0)
        {
            if (attr->CheckBusinessLic(WT_MAC_ENCRYPTION_API) != true)
            {
                SCPI_ErrorPush(context, WT_ERR_CODE_LICENSE_ERROR);
                return SCPI_RES_ERR;
            }
        }

        attr->MacDecryptionSetting.SecurityAlgorithm = string(ModeName);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SecurityMode=" << attr->MacDecryptionSetting.SecurityAlgorithm.c_str() << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacDecryptionWEP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Type = 0;
        int Format = 0;
        char Key[256] = {0};
        size_t copyLen = 0;

        // Format
        if (!SCPI_ParamInt(context, &Format, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        // key
        if (!SCPI_ParamCopyText(context, Key, sizeof(Key) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        string ModeName = attr->MacDecryptionSetting.SecurityAlgorithm;
        std::map<std::string, int> WepTypeMap = {
            {WT_MAC_ENCRYPT_WEP40, WEP_40},
            {WT_MAC_ENCRYPT_WEP104, WEP_104},
            {WT_MAC_ENCRYPT_WEP128, WEP_128},
        };

        transform(ModeName.begin(), ModeName.end(), ModeName.begin(), ::toupper); // 强制转下大写判断，配置时不区分大小写

        auto itor = WepTypeMap.find(ModeName);
        if (itor == WepTypeMap.end())
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        Type = itor->second;

        ILLEGAL_PARAM_RETURN(Type < WEP_40 || WEP_128 < Type);
        ILLEGAL_PARAM_RETURN(Format < ASCII_FORMAT || HEX_FORMAT < Format);
        int KeyLen = strlen(Key);
        // WTLog::Instance().WriteLog(LOG_DEBUG, "Get KeyLen = %d, sizeof(Key)=%d,copyLen = %d\n", KeyLen, (int)sizeof(Key), (int)copyLen);
        // KeyLen长度说明：1、如果是WEP-40 ASCII码格式，长度为5个字符；2、如果是WEP-10 16进制模式，长度为10个字符
        // 3、如果是WEP-104 ASCII码格式，长度为13个字符；4、如果是WEP-104 16进制格式，长度为26个字节
        ILLEGAL_PARAM_RETURN((Type == WEP_40 && Format == ASCII_FORMAT && KeyLen != 5) ||
                             (Type == WEP_40 && Format == HEX_FORMAT && KeyLen != 10) ||
                             (Type == WEP_104 && Format == ASCII_FORMAT && KeyLen != 13) ||
                             (Type == WEP_104 && Format == HEX_FORMAT && KeyLen != 26) ||
                             (Type == WEP_128 && Format == ASCII_FORMAT && KeyLen != 16) ||
                             (Type == WEP_128 && Format == HEX_FORMAT && KeyLen != 32));
        attr->MacDecryptionSetting.WepSet.SecutityFormat = Format;
        attr->MacDecryptionSetting.WepSet.PassPhrase = string(Key);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzMacDecryptionKeyTypeName(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionKeyTypeName %s\n",ParamBuf);

    bool FindCorrectType = false;
    std::vector<string> SecurityKeyTypeName;

    SecurityKeyTypeName.push_back(WT_MAC_ENCRYPT_KEY_WPA_PWD);
    SecurityKeyTypeName.push_back(WT_MAC_ENCRYPT_KEY_WPA_PSK);
    SecurityKeyTypeName.push_back(WT_MAC_ENCRYPT_KEY_WPA_TK);

    for (auto &Item : SecurityKeyTypeName)
    {
        if (strcasecmp(Item.c_str(), ParamBuf) == 0)
        {
            FindCorrectType = true;
            break;
        }
    }

    ILLEGAL_PARAM_RETURN(FindCorrectType == false);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.TkSet.KeyTypeName = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "KeyTypeName=" << attr->MacDecryptionSetting.TkSet.KeyTypeName.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacDecryptionSSID(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionSSID %s\n",ParamBuf);

    // 1-32字节
    ILLEGAL_PARAM_RETURN(copyLen < 1 || copyLen > 32);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.TkSet.SSID = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SSID=" << attr->MacDecryptionSetting.TkSet.SSID.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacDecryptionPassPhrase(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionPassPhrase %s\n", ParamBuf);

    // 1-32字节
    if (copyLen < 8 || copyLen > 64)
    {
        return SCPI_RES_ERR;
    }

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.TkSet.PassPhrase = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PassPhrase=" << attr->MacDecryptionSetting.TkSet.PassPhrase.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacDecryptionNonce(scpi_t *context)
{
    char ParamBuf[2][256] = {0};
    size_t copyLen = 0;

    for (int i = 0; i < 2; i++)
    {
        if (!SCPI_ParamCopyText(context, ParamBuf[i], sizeof(ParamBuf[i]) - 1, &copyLen, true))
        {
            return SCPI_RES_ERR;
        }

        // 32字节
        ILLEGAL_PARAM_RETURN(copyLen != 64);
    }

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.TkSet.ANonce = string(ParamBuf[0]);
    attr->MacDecryptionSetting.TkSet.SNonce = string(ParamBuf[1]);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ANonce=" << attr->MacDecryptionSetting.TkSet.ANonce.c_str() << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SNonce=" << attr->MacDecryptionSetting.TkSet.SNonce.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacDecryptionPSK(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionPSK %s\n", ParamBuf);

    // 32字节
    ILLEGAL_PARAM_RETURN(copyLen != 64);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.TkSet.Psk = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Psk=" << attr->MacDecryptionSetting.TkSet.Psk.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacDecryptionTK(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionTK %s\n", ParamBuf);

    // 32字节
    ILLEGAL_PARAM_RETURN(copyLen % 2);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.TkSet.TemporalKey = std::string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TemporalKey=" << attr->MacDecryptionSetting.TkSet.TemporalKey.c_str() << endl;
    return SCPI_ResultOK(context);
}

// CCMP/GCMP
scpi_result_t SetVsaAlzMacDecryptionCCMPGCMPAmsduCapable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Field = 0;

        if (!SCPI_ParamInt(context, &Field, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        ILLEGAL_PARAM_RETURN(0 != Field && 1 != Field);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->MacDecryptionSetting.TkSet.AmsduCapable = Field;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AmsduCapable=" << attr->MacDecryptionSetting.TkSet.AmsduCapable << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

// WAPI
scpi_result_t SetVsaAlzMacWAPIDecryptionKeyTypeName(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionKeyTypeName %s\n", ParamBuf);

    bool FindCorrectType = false;
    std::vector<string> SecurityKeyTypeName;

    SecurityKeyTypeName.push_back(WT_MAC_ENCRYPT_KEY_WAPI_PWD);
    SecurityKeyTypeName.push_back(WT_MAC_ENCRYPT_KEY_WAPI_BK);
    SecurityKeyTypeName.push_back(WT_MAC_ENCRYPT_KEY_WAPI_TK);

    for (auto &Item : SecurityKeyTypeName)
    {
        if (strcasecmp(Item.c_str(), ParamBuf) == 0)
        {
            FindCorrectType = true;
            break;
        }
    }

    ILLEGAL_PARAM_RETURN(FindCorrectType == false);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.WapiSet.KeyTypeName = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "KeyTypeName=" << attr->MacDecryptionSetting.WapiSet.KeyTypeName.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionPassPhrase(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionPassPhrase %s\n", ParamBuf);

    // 8字节
    ILLEGAL_PARAM_RETURN(copyLen != 16);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.WapiSet.PassPhrase = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PassPhrase=" << attr->MacDecryptionSetting.WapiSet.PassPhrase.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionN12(scpi_t *context)
{
    char ParamBuf[2][256] = {0};
    size_t copyLen = 0;

    for (int i = 0; i < 2; i++)
    {
        if (!SCPI_ParamCopyText(context, ParamBuf[i], sizeof(ParamBuf[i]) - 1, &copyLen, true))
        {
            return SCPI_RES_ERR;
        }

        // 32字节
        ILLEGAL_PARAM_RETURN(copyLen != 64);
    }

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.WapiSet.N1 = string(ParamBuf[0]);
    attr->MacDecryptionSetting.WapiSet.N2 = string(ParamBuf[1]);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "N1=" << attr->MacDecryptionSetting.WapiSet.N1.c_str() << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "N2=" << attr->MacDecryptionSetting.WapiSet.N2.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionBK(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionBK %s\n", ParamBuf);

    // 16字节
    ILLEGAL_PARAM_RETURN(copyLen != 32);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.WapiSet.BK = string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Psk=" << attr->MacDecryptionSetting.WapiSet.BK.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionTK(scpi_t *context)
{
    char ParamBuf[256] = {0};
    size_t copyLen = 0;

    if (!SCPI_ParamCopyText(context, ParamBuf, sizeof(ParamBuf) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetVsaAlzMacDecryptionTK %s\n", ParamBuf);

    ILLEGAL_PARAM_RETURN(copyLen % 2 || (copyLen != 64 && copyLen != 32));

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->MacDecryptionSetting.WapiSet.TemporalKey = std::string(ParamBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TemporalKey=" << attr->MacDecryptionSetting.WapiSet.TemporalKey.c_str() << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionAADVersion(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Version = 2006;

        if (!SCPI_ParamInt(context, &Version, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        ILLEGAL_PARAM_RETURN(2006 != Version && 2020 != Version);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->MacDecryptionSetting.WapiSet.AADVersion = Version;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AADVersion=" << attr->MacDecryptionSetting.WapiSet.AADVersion << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionAADFrameControlHTC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Htc = 0;

        if (!SCPI_ParamInt(context, &Htc, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        ILLEGAL_PARAM_RETURN(0 != Htc && 1 != Htc);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->MacDecryptionSetting.WapiSet.AADFrameCtlHTC = Htc;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AADFrameCtlHTC=" << attr->MacDecryptionSetting.WapiSet.AADFrameCtlHTC << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzMacWAPIDecryptionAmsduCapableField(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Field = 0;

        if (!SCPI_ParamInt(context, &Field, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        ILLEGAL_PARAM_RETURN(0 != Field && 1 != Field);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->MacDecryptionSetting.WapiSet.AADAmsduCapableField = Field;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AADAmsduCapableField=" << attr->MacDecryptionSetting.WapiSet.AADAmsduCapableField << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
// end WAPI

scpi_result_t SetVsaAlzRBW(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > 1000 * KHz_API || ParamVal.value < 1 * KHz_API);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamFft.Rbw = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamFft.Rbw=" << attr->vsaAlzParam.analyzeParamFft.Rbw / KHz_API << " KHz" << endl;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaAlzSfoCompensation(scpi_t *context)
{
    int Flag;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &Flag, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        ILLEGAL_PARAM_RETURN(Flag != 0 && Flag != 1 && Flag != 2);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempDemo = attr->vsaParam.Demode;
        if (tempDemo == WT_DEMOD_LRWPAN_OFDM || tempDemo == WT_DEMOD_LRWPAN_FSK || tempDemo == WT_DEMOD_LRWPAN_OQPSK)
        {
            attr->vsaAlzParam.analyzeParamWiSun.SfoCompensation = Flag;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamWifi.SfoCompensation = (char)Flag;
            cout << "analyzeParamWifi.SfoCompensation = " << (int)(attr->vsaAlzParam.analyzeParamWifi.SfoCompensation) << endl;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamWifi.SfoCompensation = " << (int)(attr->vsaAlzParam.analyzeParamWifi.SfoCompensation) << endl;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzFrameFilter(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        ResultFilter Filter;
        memset(&Filter, 0, sizeof(ResultFilter));

        if (!SCPI_ParamInt(context, &Filter.IsEnable, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        ILLEGAL_PARAM_RETURN(0 != Filter.IsEnable && 1 != Filter.IsEnable);

        if(Filter.IsEnable == 1)
        {
            if (!SCPI_ParamInt(context, &Filter.FilterType, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            ILLEGAL_PARAM_RETURN(Filter.FilterType < FILT_BY_PSDULENGTH || FILT_BY_POWER < Filter.FilterType);

            if (!SCPI_ParamInt(context, &Filter.CompareType, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            ILLEGAL_PARAM_RETURN(Filter.CompareType < GE || NE < Filter.CompareType);

            double Value = 0;
            if (!SCPI_ParamDouble(context, &Value, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            if(Filter.FilterType == FILT_BY_PSDULENGTH)
            {
                Filter.FiltPsduLength = (int)Value;
            }
            else if(Filter.FilterType == FILT_BY_POWER)
            {
                Filter.FiltPower = Value;
            }
            else
            {
                Filter.FiltDataRate = Value;
            }
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_SetWifiFrameFilter(attr->ConnID, &Filter);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "iRet = %d,filter=(%d,%d,%d,%d,%lf,%lf)\n",iRet, Filter.IsEnable, Filter.FilterType, Filter.CompareType, Filter.FiltPsduLength,Filter.FiltPower,Filter.FiltDataRate);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAutoRange(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (TESTER_RUN_DIGIT_IQ == attr->TesterMajorMode)
        {
            break;
        }
        attr->m_WTTimer.StartTimer("WT_SetWideSpectrumEnable");
        // 优化处理，AGC强制把宽频谱功能关闭
        iRet = WT_SetWideSpectrumEnable(attr->ConnID, 0);
        attr->m_WTTimer.StopTimer("WT_SetWideSpectrumEnable");
        attr->m_WTTimer.StartTimer("WT_SetVSAAutorange_V2");
        iRet = WT_SetVSAAutorange_V2(attr->ConnID, &(attr->vsaParam), &(attr->vsaExtParam));
        attr->m_WTTimer.StopTimer("WT_SetVSAAutorange_V2");
        if (iRet)
        {
            break;
        }

        UpdateVsaParamToMonitor(attr);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetAgcSamplingTime(scpi_t *context)
{
    int iRet = WT_OK;
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_VSA_AGC_SAMPLING_TIME;
        SubCmd.SendBuf = reinterpret_cast<char*>(&ParamVal.value);
        SubCmd.SendDataLen = sizeof(ParamVal.value);
        SubCmd.RecvBuf = nullptr;
        SubCmd.RecvBufLen = 0;
        iRet = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(iRet);
    return SCPI_ResultOK(context);
}


scpi_result_t GetAgcSamplingTime(scpi_t *context)
{
    double ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_GET_VSA_AGC_SAMPLING_TIME;
        SubCmd.SendBuf = nullptr;
        SubCmd.SendDataLen = 0;
        SubCmd.RecvBuf = reinterpret_cast<char*>(&ParamVal);
        SubCmd.RecvBufLen = sizeof(ParamVal);
        Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(Ret);
    SCPI_ResultDouble(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AGC Sampling Time = " << ParamVal * 1e6 << " us " << endl;
    return SCPI_ResultOK(context);
}


scpi_result_t GetVsaCaptureStatus(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int status = WT_VSG_VSA_STATE_TIMEOUT;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (WT_SYNC_START == attr->m_VsaStartMode)
        {
            iRet = WT_GetCurrVSAStatu(attr->ConnID, &status);
            IF_BREAK(iRet);
        }
        else
        {
            if (THREAD_RUNNING == attr->m_VsaThreadStatus)
            {
                status = WT_VSG_VSA_STATE_RUNNING;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "################ GetVsaCaptureStatus RUNNING" << endl;
            }
            else
            {
                if (attr->m_vsaThread.valid())
                {
                    iRet = attr->m_vsaThread.get();
                }
                // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "################ m_vsaThread.get: " << PoutN(iRet) << endl;
                switch (iRet)
                {
                case WT_ERR_CODE_TIMEOUT:
                    status = WT_VSG_VSA_STATE_TIMEOUT;
                    break;
                case WT_ERR_CODE_OK:
                    status = WT_VSG_VSA_STATE_DONE;
                    break;
                case WT_ERR_CODE_CAPTURE_DATA_ERROR:
                    status = WT_VSG_VSA_STATE_ERR_DONE;
                    break;
                default:
                    status = WT_VSG_VSA_STATE_TIMEOUT;
                    break;
                }
                // iRet = WT_GetCurrVSAStatu(attr->ConnID, &status);
                // IF_BREAK(iRet);
            }
        }
    } while (0);

    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, status);

    return SCPI_RES_OK;
}

scpi_result_t SetVsaCaptureAsync(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    TimeTick tick("SetVsaCaptureAsync");

    if (attr->avgParam.AvgCount > 1 && attr->avgParam.AvgType == WT_CAPTURE_MOVING_AVERAGE)
    {
        int mode = 1;
        SCPI_ParamInt(context, &mode, false);
        if (THREAD_RUNNING == attr->m_VsaThreadStatus)
        {
            attr->m_WTTimer.StartTimer("WT_StopDataCapture");
            iRet = WT_StopDataCapture(attr->ConnID);
            attr->m_WTTimer.StopTimer("WT_StopDataCapture");
            if (THREAD_STOPPED != attr->m_VsaThreadStatus)
            {
                attr->m_vsaThread.get();
            }
        }

        if (THREAD_RUNNING != attr->m_VsaThreadStatus)
        {
            if (attr->m_vsaThread.valid())
            {
                attr->m_VsaThreadStatus = THREAD_IDEL;
                attr->m_vsaThread.get();
            }

            attr->m_VsaStartMode = WT_ASYNC_START;
            attr->m_vsaThread = std::async(std::launch::async, &VsaCaptureThread, context, mode);

            iRet = CheckThreadStart(attr->m_vsaThread, &attr->m_VsaThreadStatus);
        }
    }
    else
    {
        do
        {
            int WideBandSetResult = WT_ERR_CODE_OK;
            attr->m_WTTimer.StartTimer("WT_SetWideSpectrumEnable");
            if (attr->mSpectrumWideMode && SupportSpectrumWideMode(attr, attr->mSpectrumWideMode))
            {
                WideBandSetResult = WT_SetWideSpectrumEnable(attr->ConnID, attr->mSpectrumWideMode);
            }
            else
            {
                WideBandSetResult = WT_SetWideSpectrumEnable(attr->ConnID, 0);
            }

            SetNrAlzParam(attr->ConnID, attr->vsaParam.Demode, (AnalyzeParam *)&attr->vsaAlzParam.analyzeParam3GPP, sizeof(attr->vsaAlzParam.analyzeParam3GPP));

            attr->m_WTTimer.StopTimer("WT_SetWideSpectrumEnable");
            VsaAutoPowerCorrect(attr);
            attr->m_WTTimer.StartTimer("WT_SetVSA_V2");
            iRet = WT_SetVSA_V2(attr->ConnID, &(attr->vsaParam), &attr->vsaExtParam);
            attr->m_WTTimer.StopTimer("WT_SetVSA_V2");
            IF_BREAK(iRet);
            attr->m_WTTimer.StartTimer("WT_GetVSAParameter_V2");
            iRet = WT_GetVSAParameter_V2(attr->ConnID, 0, &attr->vsaParam, &attr->vsaExtParam);
            attr->m_WTTimer.StopTimer("WT_GetVSAParameter_V2");
            IF_BREAK(iRet);

            // wide mode开关要判断频点，如果配置参数前打开失败，则重配一次，保证功能立即生效
            if (WideBandSetResult && attr->mSpectrumWideMode && SupportSpectrumWideMode(attr, attr->mSpectrumWideMode))
            {
                attr->m_WTTimer.StartTimer("WT_SetWideSpectrumEnable");
                iRet = WT_SetWideSpectrumEnable(attr->ConnID, attr->mSpectrumWideMode);
                attr->m_WTTimer.StopTimer("WT_SetWideSpectrumEnable");
            }

            UpdateVsaParamToMonitor(attr);

            // 这里需注意，配置为WT_SYNC_START并非错误
            // 非平均模式时保留原来做法，调用WT_DataCaptureAsync后,scpi就返回了，对SCPI流程来说仍是同步的
            // 启用平均时，创建新线程去处理VSA事件，并在新线程轮询VSA状态，这对scpi来说才是异步处理
            attr->m_VsaStartMode = WT_SYNC_START;
            attr->m_WTTimer.StartTimer("WT_DataCaptureAsync");
            iRet = WT_DataCaptureAsync(attr->ConnID);
            attr->m_WTTimer.StopTimer("WT_DataCaptureAsync");
            if (WT_ERR_CODE_OK != iRet)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_DataCapture fail :" << iRet << endl;
                break;
            }
        } while (0);
    }
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaCapture(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int mode = 0;
    SCPI_ParamInt(context, &mode, false);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->m_VsaStartMode = WT_SYNC_START;
    iRet = SetVsaCapture(context, mode);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAnalyzeStart(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAlzParam(attr);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaStopCapture(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->m_WTTimer.StartTimer("WT_StopDataCapture");
    int iRet = WT_StopDataCapture(attr->ConnID);
    attr->m_WTTimer.StopTimer("WT_StopDataCapture");
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAnalyzeResult(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SubCmdType SubCmd;
    SubCmd.Cmd = SUB_CMD_GET_VSA_ALZ_RESULT;
    SubCmd.SendBuf = nullptr;
    SubCmd.SendDataLen = 0;
    SubCmd.RecvBuf = reinterpret_cast<char*>(&ParamVal);
    SubCmd.RecvBufLen = sizeof(ParamVal);
    Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);

    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "####Vsa analyze result=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetSaveVsaRawIQDataEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal != WT_IQ_SWAP_DISABLED && ParamVal != WT_IQ_SWAP_ENABLED);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_SVAE_VSA_ADC_DATA;
        SubCmd.SendBuf = reinterpret_cast<char *>(&ParamVal);
        SubCmd.SendDataLen = sizeof(ParamVal);
        SubCmd.RecvBuf = nullptr;
        SubCmd.RecvBufLen = 0;
        Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(Ret);
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaSignalFileAsCapture(scpi_t *context)
{
    char buf[256] = {0};
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_OK;
    std::vector<std::string> low_name_list;
    do
    {
        if(Diskfun::Instance().IsDisableSaveSignal() == true)       // 判断硬盘容量是否允许保存
        {
            WTLog::Instance().LOGERR(WT_DISK_CAPACITY_ERROR, "scpi_Disk capacity is too less");
            iRet = WT_DISK_CAPACITY_ERROR;
            return SCPI_ResultOK(context, iRet);    //在这里直接返回错误信息
        }

        if (context->parser_state.numberOfParameters > 1)
        {
            return SetVsaSignalFileAsCapture_V2(context);
        }

        if (!SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, true))
        {
            return SCPI_RES_ERR;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WaveName=" << buf << endl;
        iRet = check_waveform_exist_v2(context, buf, low_name_list, false, false);
        if (iRet)
        {
            break;
        }

        // 处理下文件路径
        std::string tmpWave(buf);
        std::string wavePath(SCPI_WaveDir());
        if (tmpWave.at(0) == '/')
        {
            tmpWave = string(".") + tmpWave;
        }
        wavePath += tmpWave;

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int wave2Flag = 0;
        if (WT_DEMOD_11AX_80_80M == attr->vsaParam.Demode || WT_DEMOD_11AC_80_80M == attr->vsaParam.Demode || WT_DEMOD_11BE_80_80M == attr->vsaParam.Demode || WT_DEMOD_11BE_160_160M == attr->vsaParam.Demode)
        {
            wave2Flag = 1;
        }

        iRet = WT_LoadSignalAsCapture(attr->ConnID, wavePath.c_str(), low_name_list[0].c_str(), wave2Flag);
        if (WT_ERR_CODE_OK != iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Load file:" << wavePath << " , fail :" << iRet << std::endl;
            break;
        }
        iRet = SetVsaAlzParam(attr);

        // remove_low_file(low_name);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}



scpi_result_t SetVsaSignalFileAsCapture_V2(scpi_t *context)
{
    char buffer[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    int wave2Flag = 0;
    do
    {
        // Name,wave2flag,file data
        if (!SCPI_ParamCopyText(context, buffer, sizeof(buffer) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamInt(context, &wave2Flag, true))
        {
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ARB data len = " << len << endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WaveName=" << buffer << endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Wave2 Flag=" << wave2Flag << endl;

        if (len > 0)
        {
            SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
            std::string fileName(buffer);
            std::string firstName(buffer);
            std::string extName(".bwv");
            std::vector<std::string> low_name_list;
            const std::string dirName(SCPI_WaveDir());
            size_t pos = fileName.find_last_of(".");

            if (std::string::npos != pos)
            {
                firstName = fileName.substr(0, pos);
                extName = fileName.substr(pos);
            }
            make_file_dir(SCPI_WaveDir(), fileName);
            fileName = dirName + firstName + std::string("_") + get_random_name() + extName;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI VSA Save File Name = " << fileName << std::endl;

            iRet = WriteFile(fileName, data, len);
            if (0 == access(fileName.c_str(), R_OK))
            {
                // remove bwv/csv file
                char cmd[1024] = {0};
                sprintf(cmd, "mv '%s' '%s%s%s'", fileName.c_str(), dirName.c_str(), firstName.c_str(), extName.c_str());
                do_system_cmd(cmd);

                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;

                fileName = dirName + firstName + extName;
            }

            iRet = check_waveform_exist_v2(context, buffer, low_name_list, false, false);
            if (WT_ERR_CODE_OK == iRet)
            {
                iRet = WT_LoadSignalAsCapture(attr->ConnID, fileName.c_str(), low_name_list[0].c_str(), wave2Flag);
                if (iRet)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Load file:" << fileName << " , fail :" << iRet << std::endl;
                    break;
                }
                iRet = SetVsaAlzParam(attr);

                // remove_low_file(low_name);
            }
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaFlatnessCalCompensateEnable(scpi_t *context)
{
    int Enable = 1; // 默认vsg校准硬件补偿开
    if (!SCPI_ParamInt(context, &Enable, true))
    {
        return SCPI_RES_ERR;
    }

    ILLEGAL_PARAM_RETURN(Enable != 0 && Enable != 1); // 判断范围

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Enable=" << Enable << endl;

    int iRet = WT_ERR_CODE_OK;
    iRet = WT_SetVsaCalDataCompensate(attr->ConnID, Enable);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaIQImbCompensateEnable(scpi_t *context)
{
    int Enable = 1; // 默认vsg校准硬件补偿开
    if (!SCPI_ParamInt(context, &Enable, true))
    {
        return SCPI_RES_ERR;
    }

    ILLEGAL_PARAM_RETURN(Enable != 0 && Enable != 1); // 判断范围

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Enable=" << Enable << endl;

    int iRet = WT_ERR_CODE_OK;
    iRet = WT_SetVsaIQImbCompensate(attr->ConnID, Enable);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaFlatnessCalCompensate(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_GetVsaFlatnessCalCompensate(attr->ConnID, &ParamVal);

    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsaFlatnessCalCompensate=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t GetVsaIQImbCompensate(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_GetVsaIQImbCompensate(attr->ConnID, &ParamVal);

    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsaIQImbCompensate=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t GetVsaCfgFrequency(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.Freq);
    SCPI_ResultDouble(context, attr->vsaParam.Freq2);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgMaxPower(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, attr->vsaParam.MaxPower[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaActualMaxPower(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    VsaParameter VsaParam;
    int iRet = WT_GetVSAParameter_V2(attr->ConnID, 0, &VsaParam, nullptr);
    IF_ERR_RETURN(iRet);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, VsaParam.MaxPower[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgSamplingTime(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.SmpTime);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgPort(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultInt(context, attr->vsaParam.RfPort[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgTriggerType(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultInt(context, attr->vsaParam.TrigType);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgTriggerLevel(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.TrigLevel);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgTriggerTimeOut(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.TrigTimeout);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgTriggerPreTime(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.TrigPretime);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgExtGain(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int32_t ID = 1;
    double *pathLoss = nullptr;
    SCPI_CommandNumbers(context, &ID, 1);
    if (ID > 2)
    {
        ID = 2;
    }
    if (ID < 1)
    {
        ID = 1;
    }
    pathLoss = (ID <= 1 ? attr->vsaParam.ExtPathLoss : attr->vsaParam.ExtPathLoss2);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, pathLoss[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaCfgExtGain2(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, attr->vsaParam.ExtPathLoss2[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaDemod(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultInt(context, attr->vsaParam.Demode);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaTimeOutWaittingSec(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.TimeoutWaiting);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaAlzFrameIndex(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultInt(context, attr->AlzFrameID);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaMaxIFG(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaParam.MaxIFGGap);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaTriggerGapTime(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaTrigParam.GapTime);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaTriggerEdge(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsaTrigParam.Edge);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaAnalyzFpOnSymbolCntNotEnoughErr(scpi_t *context)
{
    double ErrCode = 0;
    int RetCode = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    int iRet = WT_GetResult(attr->ConnID, WT_RES_EVM_INVALID_ERROR_CODE, &ErrCode);
    //WTLog::Instance().WriteLog(LOG_DEBUG, "ErrCode = %d, %d\n", (int)ErrCode,iRet);
    if(iRet == WT_ERR_CODE_OK && (int)ErrCode > 0)
    {
        if((int)ErrCode == ALGERR_EvmErr_SymNotEnough_FullpacketOn)
        {
            RetCode = WT_ERR_CODE_EVM_INVAIL_FP_ON_SYMBOL_NOT_ENOUGH;
        }
        else if((int)ErrCode == ALGERR_FrmCntErr)
        {
            RetCode = WT_ERR_CODE_EVM_INTERATIVE_FRAME_COUNT_NOT_ENOUGH;
        }
    }
    SCPI_ResultInt(context, RetCode);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaBaseResult(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    VsaBaseResult Result;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_BASE_RESULT, &Result, sizeof(Result), streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, Result.PowerFrame);
    SCPI_ResultDouble(context, Result.PowerAll);
    SCPI_ResultDouble(context, Result.PowerPeak);
    SCPI_ResultDouble(context, Result.EvmAll);
    SCPI_ResultDouble(context, Result.EvmPeak);
    SCPI_ResultDouble(context, Result.EvmData);
    SCPI_ResultDouble(context, Result.EvmPilot);
    SCPI_ResultDouble(context, Result.EvmPsdu);
    SCPI_ResultDouble(context, Result.EvmShrPhr);
    SCPI_ResultDouble(context, Result.FreqOffset);
    SCPI_ResultDouble(context, Result.CarrierLeakage);
    SCPI_ResultDouble(context, Result.SymClkErr);
    SCPI_ResultDouble(context, Result.PhaseErr);
    SCPI_ResultDouble(context, Result.IQImbAmp);
    SCPI_ResultDouble(context, Result.IQImbPhase);
    
    return SCPI_RES_OK;
}

scpi_result_t GetVsaCompositeBaseResult(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    VsaBaseResult Result;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_BASE_RESULT_COMPOSITE, &Result, sizeof(Result));
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, Result.PowerFrame);
    SCPI_ResultDouble(context, Result.PowerAll);
    SCPI_ResultDouble(context, Result.PowerPeak);
    SCPI_ResultDouble(context, Result.EvmAll);
    SCPI_ResultDouble(context, Result.EvmPeak);
    SCPI_ResultDouble(context, Result.EvmData);
    SCPI_ResultDouble(context, Result.EvmPilot);
    SCPI_ResultDouble(context, Result.EvmPsdu);
    SCPI_ResultDouble(context, Result.EvmShrPhr);
    SCPI_ResultDouble(context, Result.FreqOffset);
    SCPI_ResultDouble(context, Result.CarrierLeakage);
    SCPI_ResultDouble(context, Result.SymClkErr);
    SCPI_ResultDouble(context, Result.PhaseErr);
    SCPI_ResultDouble(context, Result.IQImbAmp);
    SCPI_ResultDouble(context, Result.IQImbPhase);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaMIMOStreamRealNstsIndex(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double NstsIndex[16] = {0};
    double StreamCnt = 0;

    int iRet = WT_GetResult(attr->ConnID, WT_RES_STREAM_COUNT, &StreamCnt);
    IF_ERR_RETURN(iRet);
    for (int i = 0; i < (int)StreamCnt; i++)
    {
        iRet = WT_GetResult(attr->ConnID, WT_RES_STREAM_NSTS_INDEX, &NstsIndex[i], i, 0);
        if (iRet != WT_ERR_CODE_OK)
        {
            break;
        }
    }
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, (int)StreamCnt);
    for (int i = 0; i < (int)StreamCnt; i++)
    {
        SCPI_ResultInt(context, (int)NstsIndex[i]);
    }
    // WTLog::Instance().WriteLog(LOG_DEBUG, "##StreamCnt=%d,%d,%d,%d,%d###\n",(int)StreamCnt,(int)NstsIndex[0],(int)NstsIndex[1],(int)NstsIndex[2],(int)NstsIndex[3]);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaMIMOStreamCount(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double result = 0;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_STREAM_COUNT, &result);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, (int)result);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaDataInfoDemod(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Demo = -1;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, (int)Demo);

    return SCPI_RES_OK;
}

static void Resp11ahDataInfo_preamble_short(scpi_t *context, DataInfo11ah &DataInfo)
{
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.ValidFlag);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.ReservedB0);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.BW);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.STBC);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.ULDL);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.NSS);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.Nsts);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.ID);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.ShortGi);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.CodingType);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.LDPCExtra);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.MCS);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.Smoothing);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.IsAggregation);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.Length);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.ResponseIndication);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.TravelingPilots);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.NDPIndication);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.CRC);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.Tail);
    SCPI_ResultInt(context, DataInfo.S1GShortInfo.SigBitLen);
    stringstream msg;
    for (int i = 0; i < DataInfo.S1GShortInfo.SigBitLen; i++)
    {
        msg << DataInfo.S1GShortInfo.SigBit[i];
    }
    SCPI_ResultText(context, msg.str().c_str());
}

static void Resp11ahDataInfo_preamble_long_su(scpi_t *context, DataInfo11ah &DataInfo)
{
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.ValidFlag);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.BW);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.STBC);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.ULDL);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.NSS);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.Nsts);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.ID);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.ShortGi);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.CodingType);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.LDPCExtra);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.MCS);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.BeamChange);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.Smoothing);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.IsAggregation);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.Length);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.ResponseIndication);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.TravelingPilots);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.ReservedA2B12);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.CRC);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.Tail);
    SCPI_ResultInt(context, DataInfo.S1GLongSuInfo.SigABitLen);
    stringstream msg;
    for (int i = 0; i < DataInfo.S1GLongSuInfo.SigABitLen; i++)
    {
        msg << DataInfo.S1GLongSuInfo.SigABit[i];
    }
    SCPI_ResultText(context, msg.str().c_str());
}

static void Resp11ahDataInfo_preamble_long_mu(scpi_t *context, DataInfo11ah &DataInfo)
{
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.ValidFlag);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.BW);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.STBC);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.ReservedA1B2);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.NSS);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.Nsts);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.GroupID);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.CodingTypeI);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.CodingTypeII);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.ReservedA2B1);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.Length);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.ResponseIndication);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.TravelingPilots);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.CRC);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.Tail);
    SCPI_ResultInt(context, DataInfo.S1GLongMuInfo.SigABitLen);
    stringstream msg;
    for (int i = 0; i < DataInfo.S1GLongMuInfo.SigABitLen; i++)
    {
        msg << DataInfo.S1GLongMuInfo.SigABit[i];
    }
    SCPI_ResultText(context, msg.str().c_str());
}

static void Resp11ahDataInfo_preamble_1M(scpi_t *context, DataInfo11ah &DataInfo)
{
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.ValidFlag);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.BW);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.STBC);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.NSS);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.Nsts);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.ShortGi);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.CodingType);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.LDPCExtra);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.ReservedB6);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.MCS);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.IsAggregation);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.Length);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.ResponseIndication);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.Smoothing);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.TravelingPilots);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.NDPIndication);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.CRC);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.Tail);
    SCPI_ResultInt(context, DataInfo.S1GS1MInfo.SigBitLen);
    stringstream msg;
    for (int i = 0; i < DataInfo.S1GS1MInfo.SigBitLen; i++)
    {
        msg << DataInfo.S1GS1MInfo.SigBit[i];
    }
    SCPI_ResultText(context, msg.str().c_str());
}

static scpi_result_t GetVsaDataInfo_11ah(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11ah DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);

    SCPI_ResultInt(context, DataInfo.PreambleType);
    SCPI_ResultInt(context, DataInfo.UserNumber);

    if (enAlg_S1G_Preamble_Type_SHORT == DataInfo.PreambleType)
    {
        Resp11ahDataInfo_preamble_short(context, DataInfo);
    }
    else if (enAlg_S1G_Preamble_Type_LONG_SU == DataInfo.PreambleType)
    {
        Resp11ahDataInfo_preamble_long_su(context, DataInfo);
    }
    else if (enAlg_S1G_Preamble_Type_LONG_MU == DataInfo.PreambleType)
    {
        Resp11ahDataInfo_preamble_long_mu(context, DataInfo);
    }
    else if (enAlg_S1G_Preamble_Type_1M == DataInfo.PreambleType)
    {
        Resp11ahDataInfo_preamble_1M(context, DataInfo);
    }
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11ag(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11ag DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultDouble(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.SymbolCnt);
    SCPI_ResultInt(context, DataInfo.PsduLen);
    SCPI_ResultInt(context, DataInfo.CodeingRate);
    SCPI_ResultInt(context, DataInfo.Modulation);
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11b(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11b DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultDouble(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.Length);
    SCPI_ResultInt(context, DataInfo.PsduLen);
    SCPI_ResultInt(context, DataInfo.PreambleType);
    SCPI_ResultInt(context, DataInfo.SfdPass);
    SCPI_ResultInt(context, DataInfo.HeaderPass);
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11n(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11n DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultDouble(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.SymbolCnt);
    SCPI_ResultInt(context, DataInfo.CodeingRate);
    SCPI_ResultInt(context, DataInfo.PsduLen);
    SCPI_ResultInt(context, DataInfo.Modulation);
    SCPI_ResultInt(context, DataInfo.HTSigValid);
    SCPI_ResultInt(context, DataInfo.Mcs);
    SCPI_ResultInt(context, DataInfo.Cbw);
    SCPI_ResultInt(context, DataInfo.HTLen);
    SCPI_ResultInt(context, DataInfo.Smooth);
    SCPI_ResultInt(context, DataInfo.NotSnd);
    SCPI_ResultInt(context, DataInfo.Aggreg);
    SCPI_ResultInt(context, DataInfo.STBC);
    SCPI_ResultInt(context, DataInfo.FecCode);
    SCPI_ResultInt(context, DataInfo.ShortGi);
    SCPI_ResultInt(context, DataInfo.ExtSStrms);
    SCPI_ResultInt(context, DataInfo.Crc);
    SCPI_ResultInt(context, DataInfo.Tail);
    SCPI_ResultInt(context, DataInfo.LsigValid);
    SCPI_ResultInt(context, DataInfo.Rate);
    SCPI_ResultInt(context, DataInfo.Len);
    SCPI_ResultInt(context, DataInfo.Parity);
    SCPI_ResultInt(context, DataInfo.LsigTail);
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11ac(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11ac DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultDouble(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.SymbolCnt);
    SCPI_ResultInt(context, DataInfo.CodeingRate);
    SCPI_ResultInt(context, DataInfo.Modulation);
    SCPI_ResultInt(context, DataInfo.PsduLen);
    SCPI_ResultInt(context, DataInfo.VHTSigAValid);
    SCPI_ResultInt(context, DataInfo.Mcs);
    SCPI_ResultInt(context, DataInfo.Bw);
    SCPI_ResultInt(context, DataInfo.STBC);
    SCPI_ResultInt(context, DataInfo.FecCode);
    SCPI_ResultInt(context, DataInfo.ShortGi);
    SCPI_ResultInt(context, DataInfo.Nvhtltf);
    SCPI_ResultInt(context, DataInfo.Nsts);
    SCPI_ResultInt(context, DataInfo.Nss);
    SCPI_ResultInt(context, DataInfo.GroupId);
    SCPI_ResultInt(context, DataInfo.VHTSigBValid);
    SCPI_ResultInt(context, DataInfo.McsB);
    SCPI_ResultInt(context, DataInfo.Len);
    SCPI_ResultInt(context, DataInfo.LsigValid);
    SCPI_ResultInt(context, DataInfo.Rate);
    SCPI_ResultInt(context, DataInfo.LsigLen);
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11ax(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11ax DataInfo;
    int BSS_Color = 63;
    double EVM_SIGB_ALL = 0.0;
    double EVM_SIGB_PILOT = 0.0;
    double EVM_SIGB_DATA = 0.0;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_SIGA_BSS_COLOR, &BSS_Color, sizeof(BSS_Color));
    IF_ERR_RETURN(iRet);
    if (HE_MU == DataInfo.PsduFormat)
    {
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_SIGB_EVM_ALL, &EVM_SIGB_ALL, sizeof(EVM_SIGB_ALL));
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_SIGB_EVM_PILOT_DB, &EVM_SIGB_PILOT, sizeof(EVM_SIGB_PILOT));
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_SIGB_EVM_DATA_DB, &EVM_SIGB_DATA, sizeof(EVM_SIGB_DATA));
        IF_ERR_RETURN(iRet);
    }

    SCPI_ResultInt(context, Demo);
    SCPI_ResultInt(context, DataInfo.HeSigAValid);
    SCPI_ResultInt(context, DataInfo.Bw);
    SCPI_ResultInt(context, DataInfo.PsduFormat);

    int RUCnt = DataInfo.UserNum;
    SCPI_ResultInt(context, RUCnt);                // RU count
    SCPI_ResultInt(context, DataInfo.RealUserNum); // User count
    SCPI_ResultDouble(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.SymbolCnt);
    SCPI_ResultInt(context, DataInfo.HeltfNum);
    SCPI_ResultDouble(context, DataInfo.HeltfLen);
    SCPI_ResultInt(context, DataInfo.HeltType);
    SCPI_ResultDouble(context, DataInfo.GILen);
    SCPI_ResultDouble(context, DataInfo.HeDataSymLen);
    SCPI_ResultDouble(context, DataInfo.FrameLen);
    // BSS color
    SCPI_ResultInt(context, BSS_Color);
    if (DataInfo.PsduFormat == HE_SU || DataInfo.PsduFormat == HE_ERSU)
    {
        SCPI_ResultInt(context, DataInfo.PreTxBF); // beamchange， 仅HE-SU,HE-ER有效
    }
    SCPI_ResultInt(context, DataInfo.LDPCExtra);
    SCPI_ResultInt(context, DataInfo.PE);
    SCPI_ResultInt(context, DataInfo.PeLen);
    SCPI_ResultInt(context, DataInfo.PreFEC);
    SCPI_ResultInt(context, DataInfo.Doppler);
    SCPI_ResultInt(context, DataInfo.Midamble_Periodicity);

    if (HE_MU == DataInfo.PsduFormat)
    {
        SCPI_ResultInt(context, DataInfo.HeSigBValid);
        SCPI_ResultInt(context, DataInfo.SigBDcm);
        SCPI_ResultInt(context, DataInfo.SigBMcs);
        SCPI_ResultInt(context, DataInfo.SigBSymbol);
        SCPI_ResultInt(context, DataInfo.SIGBCompression);
        SCPI_ResultDouble(context, EVM_SIGB_ALL);
        SCPI_ResultDouble(context, EVM_SIGB_PILOT);
        SCPI_ResultDouble(context, EVM_SIGB_DATA);

        stringstream msg;
        for (int i = 0; i < DataInfo.Common8BitLen; i++)
        {
            msg << DataInfo.Common8Bit[i];
        }
        SCPI_ResultText(context, msg.str().c_str());
    }

    // Lsig
    SCPI_ResultInt(context, DataInfo.LsigValid);
    SCPI_ResultInt(context, DataInfo.LsigRate);
    SCPI_ResultInt(context, DataInfo.LsigLen);
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11be(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
    DataInfo11Be *Info = DataInfoBuf.get();
    int iRet = WT_GetVectorResult(attr->ConnID,
                                    WT_RES_DATA_INFO,
                                    Info,
                                    sizeof(DataInfo11Be));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultInt(context, Info->common.PPDU);
    SCPI_ResultInt(context, Info->common.RUCnt);
    SCPI_ResultInt(context, Info->common.UserCnt);
    SCPI_ResultInt(context, Info->common.ActiveUserCnt);
    SCPI_ResultDouble(context, Info->common.DataRate);
    SCPI_ResultInt(context, Info->common.NSTS);
    SCPI_ResultInt(context, Info->common.SymbolCnt);
    SCPI_ResultDouble(context, Info->common.PELen);
    SCPI_ResultDouble(context, Info->common.GILen);
    SCPI_ResultDouble(context, Info->common.LTFLen);
    SCPI_ResultDouble(context, Info->common.SymbolLen);
    SCPI_ResultDouble(context, Info->common.PreambleLen);
    SCPI_ResultDouble(context, Info->common.DataLen);
    SCPI_ResultDouble(context, Info->common.FrameLen);
    ////////////////////////////
    SCPI_ResultInt(context, Info->usig.CRC);
    SCPI_ResultInt(context, Info->usig.PhyVersion);
    SCPI_ResultInt(context, Info->usig.BW);
    SCPI_ResultInt(context, Info->usig.ULDL);
    SCPI_ResultInt(context, Info->usig.BSSColor);
    SCPI_ResultInt(context, Info->usig.TXOP);
    SCPI_ResultInt(context, Info->usig.Disregard);
    SCPI_ResultInt(context, Info->usig.B25Valid);
    SCPI_ResultInt(context, Info->usig.CompressionMode);
    SCPI_ResultInt(context, Info->usig.U2B2Valid);
    SCPI_ResultInt(context, Info->usig.PuncBitLen);
    if (Info->usig.PuncBitLen > 0)
    {
        for (int i = 0; i < Info->usig.PuncBitLen; i++)
        {
            SCPI_ResultInt(context, Info->usig.PuncBit[i]);
        }
    }
    else
    {
        Info->usig.PuncBitLen = 0;
        for (int i = 0; i < Info->usig.PuncBitLen; i++)
        {
            SCPI_ResultInt(context, Info->usig.PuncBit[i]);
        }
    }

    SCPI_ResultInt(context, Info->usig.U2B8Valid);
    SCPI_ResultInt(context, Info->usig.SIGMCS);
    SCPI_ResultInt(context, Info->usig.SIGSymbol);
    /////////////////////
    SCPI_ResultInt(context, Info->sig.CRC);
    SCPI_ResultInt(context, Info->sig.SpatialReuseLen);
    for (int i = 0; i < Info->sig.SpatialReuseLen; i++)
    {
        SCPI_ResultInt(context, Info->sig.SpatialReuse[i]);
    }
    SCPI_ResultInt(context, Info->sig.GILTFSize);
    SCPI_ResultInt(context, Info->sig.LTFSymbol);
    SCPI_ResultInt(context, Info->sig.LDPCExtra);
    SCPI_ResultInt(context, Info->sig.PreFEC);
    SCPI_ResultInt(context, Info->sig.PEDisambiguity);
    SCPI_ResultInt(context, Info->sig.Disregard);
    SCPI_ResultInt(context, Info->sig.Common9BitNum);

    stringstream msg;
    for (int i = 0; i < Info->sig.Common9BitNum; i++)
    {
        for (int j = 8; j >= 0; j--) // 9bit
        {
            msg << ((Info->sig.Common9Bit[i] >> j) & 0x01);
        }
    }
    // 显示内容为01的字符串，保持和ax一致
    SCPI_ResultText(context, msg.str().c_str());
    //////////////////////////////
    SCPI_ResultInt(context, Info->lsig.CRC);
    SCPI_ResultInt(context, Info->lsig.PSDULen);
    SCPI_ResultDouble(context, Info->lsig.DataRate);
    SCPI_ResultInt(context, Info->lsig.ParityBit);
    SCPI_ResultInt(context, Info->lsig.ParityCheck);

    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_bluetooth(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfoBT DataInfo;
    DataInfoBTPlus DataInfoPlus;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfoPlus, sizeof(DataInfoPlus));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultDouble(context, DataInfo.InitFreqErr);
    SCPI_ResultInt(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.PktType);
    SCPI_ResultInt(context, DataInfo.PktLen);
    
    // SCPI_ResultInt(context, DataInfoPlus.Payload_header_len);
    // int PayloadHeader[16] = {0};
    // for (int i = 0; i < DataInfoPlus.Payload_header_len; i++)
    // {
    //     PayloadHeader[i] = GetBit(DataInfo.PayloadHeader, DataInfoPlus.Payload_header_len - i - 1);
    //     SCPI_ResultInt(context, PayloadHeader[i]);
    // }
    SCPI_ResultInt(context, DataInfo.PayloadHeader);
    //SCPI_ResultInt(context, DataInfoPlus.Payload_header_len);
    // int PayloadHeader[16] = {0};
    // for (int i = 0; i < DataInfoPlus.Payload_header_len; i++)
    // {
    //     PayloadHeader[i] = GetBit(DataInfo.PayloadHeader, DataInfoPlus.Payload_header_len - i - 1);
    //     SCPI_ResultInt(context, PayloadHeader[i]);
    // }
    SCPI_ResultInt(context, DataInfo.CrcStatus);
    SCPI_ResultInt(context, DataInfoPlus.LAP);
    SCPI_ResultInt(context, DataInfoPlus.UAP);
    SCPI_ResultInt(context, DataInfoPlus.LT_ADDR);
    SCPI_ResultInt(context, DataInfoPlus.Flow);
    SCPI_ResultInt(context, DataInfoPlus.ARQN);
    SCPI_ResultInt(context, DataInfoPlus.SEQN);
    SCPI_ResultInt(context, DataInfoPlus.LLID);
    SCPI_ResultInt(context, DataInfoPlus.mFlow);
    SCPI_ResultInt(context, DataInfoPlus.PayLoadSize);
    for (int i = 0; i < sizeof(DataInfoPlus.VoiceField) / sizeof(DataInfoPlus.VoiceField[0]); i++)
    {
        SCPI_ResultInt(context, DataInfoPlus.VoiceField[i]);
    }
    SCPI_ResultInt(context, DataInfoPlus.Payload_EIR);
    SCPI_ResultInt(context, DataInfoPlus.Payload_SR);
    SCPI_ResultInt(context, DataInfoPlus.Payload_ClassofDevice);
    SCPI_ResultInt(context, DataInfoPlus.Payload_LTAddr);
    SCPI_ResultInt(context, DataInfoPlus.Payload_CLK27b2);
    SCPI_ResultInt(context, DataInfoPlus.BLEMapperS);
    for (int i = 0; i < sizeof(DataInfoPlus.Header_bin) / sizeof(DataInfoPlus.Header_bin[0]); i++)
    {
        SCPI_ResultInt(context, DataInfoPlus.Header_bin[i]);
    }
    SCPI_ResultInt(context, DataInfoPlus.Pattern);

    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11az(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11az DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);

    SCPI_ResultInt(context, DataInfo.HeSigAValid);
    SCPI_ResultInt(context, DataInfo.PuncturingMode);
    SCPI_ResultInt(context, DataInfo.Bw);
    SCPI_ResultInt(context, DataInfo.PsduFormat);
    SCPI_ResultInt(context, DataInfo.RuNum);
    SCPI_ResultInt(context, DataInfo.UserNum);
    SCPI_ResultInt(context, DataInfo.HeltfNum);
    SCPI_ResultInt(context, DataInfo.HeltType);
    SCPI_ResultDouble(context, DataInfo.HeltfLen);
    SCPI_ResultDouble(context, DataInfo.GILen);
    SCPI_ResultDouble(context, DataInfo.FrameLen);
    SCPI_ResultInt(context, DataInfo.BSSColor);
    SCPI_ResultInt(context, DataInfo.PreTxBF);
    SCPI_ResultInt(context, DataInfo.LDPCExtra);
    SCPI_ResultInt(context, DataInfo.PE);
    SCPI_ResultDouble(context, DataInfo.PeLen);
    SCPI_ResultInt(context, DataInfo.PreFEC);
    SCPI_ResultInt(context, DataInfo.Doppler);
    SCPI_ResultInt(context, DataInfo.Midamble_Periodicity);
    SCPI_ResultInt(context, DataInfo.TXOP);
    SCPI_ResultInt(context, DataInfo.SpatialReuseNum);
    for (int i = 0; i < DataInfo.SpatialReuseNum; i++)
    {
        SCPI_ResultInt(context, DataInfo.SpatialReuse[i]);
    }

    SCPI_ResultInt(context, DataInfo.SigABitLen);
    for (int i = 0; i < DataInfo.SigABitLen; i++)
    {
        SCPI_ResultInt(context, DataInfo.SigABit[i]);
    }
    SCPI_ResultInt(context, DataInfo.LsigValid);
    SCPI_ResultInt(context, DataInfo.LsigRate);
    SCPI_ResultInt(context, DataInfo.LsigLen);
    SCPI_ResultInt(context, DataInfo.LsigBitLen);
    for (int i = 0; i < DataInfo.LsigBitLen; i++)
    {
        SCPI_ResultInt(context, DataInfo.LsigBit[i]);
    }
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_11ba(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfo11ba DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultInt(context, DataInfo.BW);
    SCPI_ResultInt(context, DataInfo.LSigParityPassed);
    SCPI_ResultInt(context, DataInfo.LSigRate);
    SCPI_ResultInt(context, DataInfo.LSigLength);
    SCPI_ResultInt(context, DataInfo.SubChanNum);
    for (int i = 0; i < DataInfo.SubChanNum; i++)
    {
        SCPI_ResultInt(context, DataInfo.PunctureFlag[i]);
        SCPI_ResultInt(context, DataInfo.DataRateMode[i]);
        SCPI_ResultInt(context, DataInfo.PsduLength[i]);
        SCPI_ResultInt(context, DataInfo.PsduCRC[i]);
    }
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_zwave(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfoZwave DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);
    SCPI_ResultDouble(context, DataInfo.InitFreqErr);
    SCPI_ResultDouble(context, DataInfo.FreqDeviRMS);
    SCPI_ResultDouble(context, DataInfo.FreqDeviMAX);
    SCPI_ResultDouble(context, DataInfo.FreqDeviMIN);
    SCPI_ResultDouble(context, DataInfo.ZeroErrRMS);
    SCPI_ResultDouble(context, DataInfo.ZeroErrPeak);
    SCPI_ResultDouble(context, DataInfo.SymbolClockErr);
    SCPI_ResultDouble(context, DataInfo.SymbolClockJitter);
    SCPI_ResultDouble(context, DataInfo.DataRate);
    SCPI_ResultInt(context, DataInfo.PsduLen);
    SCPI_ResultInt(context, DataInfo.PsdeCrcPass);
    SCPI_ResultInt(context, DataInfo.SymbolCount);
    printf("[]DataInfo.InitFreqErr = %f\n\n\n", DataInfo.InitFreqErr);
    printf("[]DataInfo.FreqDeviRMS = %f\n\n\n", DataInfo.FreqDeviRMS);
    printf("[]DataInfo.FreqDeviMAX = %f\n\n\n", DataInfo.FreqDeviMAX);
    printf("[]DataInfo.FreqDeviMIN = %f\n\n\n", DataInfo.FreqDeviMIN);
    printf("[]DataInfo.ZeroErrRMS = %f\n\n\n", DataInfo.ZeroErrRMS);

    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_sle(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfoSparkLink DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);

    SCPI_ResultInt(context, DataInfo.FrmType);
    SCPI_ResultInt(context, DataInfo.Bandwidth);
    SCPI_ResultInt(context, DataInfo.PID);
    SCPI_ResultInt(context, DataInfo.PayloadLen);
    SCPI_ResultInt(context, DataInfo.PayloadCrcType);
    SCPI_ResultInt(context, DataInfo.PayloadCrc);
    SCPI_ResultInt(context, DataInfo.CtrlInfoType);
    SCPI_ResultInt(context, DataInfo.CtrlInfoCrc);

    SCPI_ResultDouble(context, DataInfo.Delta_fd1_Avg);
    SCPI_ResultDouble(context, DataInfo.Delta_fd1_Max);
    SCPI_ResultDouble(context, DataInfo.Delta_fd1_Min);
    SCPI_ResultDouble(context, DataInfo.Delta_fd2_Avg);
    SCPI_ResultDouble(context, DataInfo.Delta_fd2_Min);
    SCPI_ResultDouble(context, DataInfo.EvmAvg);
    SCPI_ResultDouble(context, DataInfo.EvmPeak);
    SCPI_ResultDouble(context, DataInfo.Evm99PCT);
    SCPI_ResultDouble(context, DataInfo.Init_Freq_Error);
    SCPI_ResultDouble(context, DataInfo.Max_Freq_Drift);
    SCPI_ResultDouble(context, DataInfo.Freq_Drift_Rate);
    SCPI_ResultDouble(context, DataInfo.CtrlInfoEvmAvg);
    SCPI_ResultDouble(context, DataInfo.CtrlInfoEvmPeak);
    SCPI_ResultDouble(context, DataInfo.CtrlInfoEvm99PCT);
    SCPI_ResultDouble(context, DataInfo.ZeroCrossingErr);
    SCPI_ResultDouble(context, DataInfo.SymClkErr);
    SCPI_ResultDouble(context, DataInfo.MaxTimeDev);
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaDataInfo_Wisun(scpi_t *context, int Demo)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    DataInfoWiSun DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Demo);

    switch (Demo)
    {
    case WT_DEMOD_LRWPAN_OFDM:
    {
        SCPI_ResultInt(context, DataInfo.PHR_Flag);
        SCPI_ResultInt(context, DataInfo.Rate_MCS);
        SCPI_ResultInt(context, DataInfo.Frame_Len);
        SCPI_ResultInt(context, DataInfo.Scrambler);
        SCPI_ResultInt(context, DataInfo.Symbol_Cnt);
        SCPI_ResultInt(context, DataInfo.Coding_rate);
        SCPI_ResultInt(context, DataInfo.Modulation);
        SCPI_ResultInt(context, DataInfo.PSDU_Cnt);
        SCPI_ResultInt(context, DataInfo.PSDU_CRC);

        SCPI_ResultDouble(context, DataInfo.Date_Rate);
        SCPI_ResultDouble(context, DataInfo.BW);

        for (int i = 0; i < sizeof(DataInfo.PHR_Bit) / sizeof(DataInfo.PHR_Bit[0]); i++)
        {
            SCPI_ResultInt(context, DataInfo.PHR_Bit[i]);
        }
        for (int i = 0; i < sizeof(DataInfo.CRC_Bit) / sizeof(DataInfo.CRC_Bit[0]); i++)
        {
            SCPI_ResultInt(context, DataInfo.CRC_Bit[i]);
        }
        break;
    }
    case WT_DEMOD_LRWPAN_OQPSK:
    {
        SCPI_ResultInt(context, DataInfo.PHR_Flag);
        SCPI_ResultInt(context, DataInfo.Spreading_Mode);
        SCPI_ResultInt(context, DataInfo.Rate_Mode);
        SCPI_ResultInt(context, DataInfo.Frame_Len);
        SCPI_ResultInt(context, DataInfo.Chip_Rate);

        SCPI_ResultDouble(context, DataInfo.Date_Rate);
        break;
    }
    case WT_DEMOD_LRWPAN_FSK:
    {
        SCPI_ResultInt(context, DataInfo.Modulation);
        SCPI_ResultInt(context, DataInfo.PhyFSKPreambleLength);
        SCPI_ResultInt(context, DataInfo.PhySunFskSfd);
        SCPI_ResultInt(context, DataInfo.PhyFcsFecEnabled);
        SCPI_ResultInt(context, DataInfo.PhyFSkFecScheme);
        SCPI_ResultInt(context, DataInfo.PhyFskFecInterleavingRsc);
        SCPI_ResultInt(context, DataInfo.ModeSwitch);
        SCPI_ResultInt(context, DataInfo.FCSType);
        SCPI_ResultInt(context, DataInfo.DataWhitening);
        SCPI_ResultInt(context, DataInfo.Frame_Len);
        SCPI_ResultInt(context, DataInfo.CRC);
        SCPI_ResultDouble(context, DataInfo.Fdev_Min_2fsk);
        SCPI_ResultDouble(context, DataInfo.Fdev_Min_2fsk_Value);
        SCPI_ResultDouble(context, DataInfo.Fdev_Max_2fsk);
        SCPI_ResultDouble(context, DataInfo.Fdev_Max_2fsk_Value);
        SCPI_ResultDouble(context, DataInfo.Fedv_Rms_2fsk);
        SCPI_ResultDouble(context, DataInfo.Zero_Cross_Tolerance_Min);
        SCPI_ResultDouble(context, DataInfo.Zero_Cross_Tolerance_Max);
        SCPI_ResultDouble(context, DataInfo.Zero_Cross_Tolerance_Rms);
        break;
    }
    default:
        break;
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaDataInfo(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Demo = -1;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
    IF_ERR_RETURN(iRet);
    using data_info_fun = struct
    {
        int min_standard;
        int max_standard;
        std::function<scpi_result_t(scpi_t *, int)> fun;
    };
    std::vector<data_info_fun> call_data_info_table = {
        {WT_DEMOD_11AG, WT_DEMOD_11AG, GetVsaDataInfo_11ag},
        {WT_DEMOD_11P_5M, WT_DEMOD_11P_10M, GetVsaDataInfo_11ag},
        {WT_DEMOD_11B, WT_DEMOD_11B, GetVsaDataInfo_11b},
        {WT_DEMOD_11N_20M, WT_DEMOD_11N_40M, GetVsaDataInfo_11n},
        {WT_DEMOD_11AC_20M, WT_DEMOD_11AC_80_80M, GetVsaDataInfo_11ac},
        {WT_DEMOD_11AX_20M, WT_DEMOD_11AX_80_80M, GetVsaDataInfo_11ax},
        {WT_DEMOD_11BE_20M, WT_DEMOD_11BE_160_160M, GetVsaDataInfo_11be},
        {WT_DEMOD_BT, WT_DEMOD_BT, GetVsaDataInfo_bluetooth},
        {WT_DEMOD_ZWAVE, WT_DEMOD_ZWAVE, GetVsaDataInfo_zwave},
        {WT_DEMOD_11BA_20M, WT_DEMOD_11BA_80M, GetVsaDataInfo_11ba},
        {WT_DEMOD_11AZ_20M, WT_DEMOD_11AZ_160M, GetVsaDataInfo_11az},
        {WT_DEMOD_11AH_1M, WT_DEMOD_11AH_16M, GetVsaDataInfo_11ah},
        {WT_DEMOD_GLE, WT_DEMOD_GLE, GetVsaDataInfo_sle},
        {WT_DEMOD_LRWPAN_FSK, WT_DEMOD_LRWPAN_OFDM, GetVsaDataInfo_Wisun},
    };

    int standard = (int)Demo;
    for (auto &item : call_data_info_table)
    {
        if (item.min_standard <= standard && standard <= item.max_standard)
        {
            return item.fun(context, standard);
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaSLECTRInfo(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    DataInfoSparkLink DataInfo;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    

    CtrlInfoCfg CtrInfo;
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_SPARK_CTRLINFO, &CtrInfo, sizeof(CtrInfo));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.CtrlInfoType);

    switch (DataInfo.CtrlInfoType)
    {
    case WT_GLE_CTRLINFOTYPE_A1:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA1.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA1.BroadType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA1.PacketType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA1.DataLength);
        break;
    case WT_GLE_CTRLINFOTYPE_A2:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.PacketType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.EmptyPacketInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.SndSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.RevSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.RevSysFrmInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA2.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_A3:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.PacketType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.EmptyPacketInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.SndSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.RevSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.ScheduleInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA3.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_A4:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.PacketType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.EmptyPacketInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.SndSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.RevSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.ScheduleInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA4.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_A5:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.PacketType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.EmptyPacketInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.SndSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.RevSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.ScheduleInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA5.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_A6:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.PacketType);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.DataPacketSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.DataPacketGrp);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.EndInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.RevSysFrmInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA6.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_A7:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA7.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA7.DataPacketSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA7.DataPacketGrp);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeA7.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_B1:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.FrmFormatInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.HarqFeedback);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.DataPacketSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.UpperLinkInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB1.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_B2:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB2.HarqFeedback);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB2.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB2.UpperLinkInd);

        break;
    case WT_GLE_CTRLINFOTYPE_B3:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB3.DataPacketGrp);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB3.DataPacketSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB3.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB3.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB3.MaxDataPacketSNInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB3.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_B4:
       SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.BrdcastSetFlag);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.BrdcastSetUpdateInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.DataPacketSN);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.FlowCtrlInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.MaxDataPacketSNInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB4.DataLength);

        break;
    case WT_GLE_CTRLINFOTYPE_B5:
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.MsgTypeInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.ConnectInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.DiscoveryInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.DirectInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.NonDirectInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.DataUpdateInd);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.Mcs);
        SCPI_ResultInt(context, CtrInfo.CtrlInfoTypeB5.DataLength);

        break;
    default:
        break;
    }

    return SCPI_RES_OK;
}

scpi_result_t SetVsaRstIBFCalibrationChannelEstDutTX(scpi_t *context)
{
    int ParamVal = WT_DEMOD_11AG;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_BeamformingCalibrationChannelEstDutTX(attr->ConnID, ParamVal);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaRstIBFCalibrationChannelEstDutRX(scpi_t *context)
{
    EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    scpi_number_t dutChannelEst;
    std::vector<double> dutChannelEstList;
    for (int i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &dutChannelEst, true))
        {
            return SCPI_RES_ERR;
        }
        dutChannelEstList.push_back(dutChannelEst.value);
    }
    int iRet = WT_BeamformingCalibrationChannelEstDutRX(attr->ConnID, &dutChannelEstList[0], dutChannelEstList.size());

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaRstIBFCalibrationResult(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    unique_ptr<double[]> ResultBuf = nullptr;
    const int bufSize = 4096;
    int ResultLen = 0;
    ResultBuf.reset(new (std::nothrow) double[bufSize]);

    int iRet = WT_BeamformingCalibrationResult(attr->ConnID, ResultBuf.get(), &ResultLen);
    IF_ERR_RETURN(iRet);

    for (int i = 0; i < ResultLen; i++)
    {
        SCPI_ResultDouble(context, ResultBuf.get()[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstIBFPowerVerification(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    double diffPower = 0;
    int iRet = WT_BeamformingVerification(attr->ConnID, &diffPower);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, diffPower);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstIBFCalculateChannelProfile(scpi_t *context)
{
    int ParamVal = WT_DEMOD_11AG;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        unique_ptr<double[]> ResultBuf = nullptr;
        int bufSize = 8192;
        ResultBuf.reset(new (std::nothrow) double[bufSize]);

        iRet = WT_BeamformingCalculateChannelProfile(attr->ConnID, ParamVal, ResultBuf.get(), bufSize);
        IF_ERR_RETURN(iRet);

        for (int i = 0; i < bufSize; i++)
        {
            SCPI_ResultDouble(context, ResultBuf.get()[i]);
        }
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstIBFCalculateChannelAmplitudeAngle(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        unique_ptr<double[]> ResultBuf = nullptr;
        int ResultLen = 8192 * 2;
        ResultBuf.reset(new (std::nothrow) double[ResultLen]);
        int ValidSpatialStreams = 0;
        int DataLen = 0;

        iRet = WT_BeamformingCalculateChannelAmplitudeandAngle_BCM(attr->ConnID, &ValidSpatialStreams, &DataLen, ResultBuf.get(), &ResultLen);
        IF_ERR_RETURN(iRet);

        SCPI_ResultInt(context, ValidSpatialStreams);
        SCPI_ResultInt(context, DataLen);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "ValidSpatialStreams = %d, DataLen=%d, result=%d\n",ValidSpatialStreams, DataLen, ResultLen);
        for (int i = 0; i < DataLen * 2; i++)
        {
            //WTLog::Instance().WriteLog(LOG_DEBUG, "%lf,", ResultBuf.get()[i]);
            SCPI_ResultDouble(context, ResultBuf.get()[i]);
        }
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstSpectCarrierLeakage(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_SPECTRUM_CARRIER_LEAKAGE);
}

scpi_result_t GetVsaRstSpectOBW99(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_SPECTRUM_OBW);
}

scpi_result_t GetVsaRstSpectMaskErrorPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_SPECTRUM_MASK_ERR);
}

scpi_result_t GetVsaRstSpectPeakFrequency(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_SPECTRUM_PEAK_FREQ);
}

scpi_result_t GetVsaRstSpectrumData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_Y);
}

scpi_result_t GetVsaRstSpectrumDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_Y, true);
}

scpi_result_t GetVsaRstSpectrumMaskData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_MASK);
}

scpi_result_t GetVsaRstSpectMargin(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_MARGIN_DATA);
}

scpi_result_t GetVsaRstRawData(scpi_t *context)
{
    // TODO 目前获取的是补偿后的采集数据,需要获取原始数据要修改
    return GetRstDoubleVectorData(context, WT_RES_IQ);
}

scpi_result_t GetVsaRstPowerPeak(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_POWER_PEAK);
}

scpi_result_t GetVsaRstPowerFrame(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_RMS_DB_NO_GAP);
}

scpi_result_t GetVsaRstPowerAll(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_RMS_DB);
}

scpi_result_t GetVsaRstFrameCount(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_POWER_FRAME_COUNT);
}

scpi_result_t GetVsaRstEvmAll(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_ALL);
}

scpi_result_t GetVsaRstEvmAllPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_ALL_PERCENT);
}

scpi_result_t GetVsaRstEvmPeak(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_PEAK);
}

scpi_result_t GetVsaRstEvmPeakPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_PEAK_PERCENT);
}

scpi_result_t GetVsaRstFreqError(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_FREQ_ERR_HZ);
}

scpi_result_t GetVsaRstClkError(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_SYMBOL_CLOCK_ERR);
}

scpi_result_t GetVsaRstIQMatchAmp(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_IQ_MATCH_AMP_DB);
}

scpi_result_t GetVsaRstIQMatchPhase(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_IQ_MATCH_PHASE);
}

scpi_result_t GetVsaRstIQMatchPhaseError(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->vsaParam.Demode == WT_DEMOD_11B)
    {
        return GetRstDoubleData(context, WT_RES_11B_PHASE_ERR);
    }
    else if (attr->vsaParam.Demode == WT_DEMOD_ZIGBEE)
    {
        return GetRstDoubleData(context, WT_RES_ZIGBEE_PHASE_ERR);
    }
    else
    {
        return GetRstDoubleData(context, WT_RES_OFDM_PHASE_ERR);
    }
}

scpi_result_t GetVsaRstDataRateMbps(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_OFDM_DATA_RATE_MBPS);
}

scpi_result_t GetVsaRstRampOnTime(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_RAMP_ON_TIME);
}

scpi_result_t GetVsaRstRampOffTime(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_RAMP_OFF_TIME);
}

scpi_result_t GetVsaRstOfdmNumberSymbols(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_OFDM_NUMBER_SYMBOLS);
}

scpi_result_t GetVsaRstEvmDataDb(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_DATA_DB);
}

scpi_result_t GetVsaRstEvmPilotDb(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_PILOT_DB);
}

scpi_result_t GetVsaRstFlatnessPassed(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED);
}

scpi_result_t GetVsaRstOfdmFlatnessSectionValue(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE);
}

scpi_result_t GetVsaRstOfdmFlatnessSectionMargin(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN);
}

scpi_result_t GetVsaRstDsssIQOffset(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_IQ_DC_OFFSET);
}

scpi_result_t GetVsaRstDsssCarrierSuppression(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_11B_CARR_SUPPRESSION);
}

scpi_result_t GetVsaRstPsduLength(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_PSDU_LENGTH);
}

scpi_result_t GetVsaRstPsduCRC(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_PSDU_CRC);
}

scpi_result_t GetVsaRstEvmMargin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EVM_MARGIN);
}

scpi_result_t GetVsaRstLeagkageMargin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_LEAGKAGE_MARGIN);
}

scpi_result_t GetVsaRstFreqerrMargin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_FREQ_ERR_MARGIN);
}

scpi_result_t GetVsaRstClockErrMargin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_CLOCK_ERR_MARGIN);
}

scpi_result_t GetVsaRstFrequencyOffset(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_FREQ_ERR_HZ);
}

scpi_result_t GetVsaRstLTFSNR(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_LTF_SNR_DB);
}

scpi_result_t GetVsaRstPSDUSNR(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_PSDU_SNR_DB);
}
// H-Matrix
scpi_result_t GetVsaHMatInitMIMOStreamCount(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_HMAT_INIT_STREAM_COUNT);
}

scpi_result_t GetVsaRstHMatInitPowerPeak(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_HMAT_INIT_POWER_PEAK);
}

scpi_result_t GetVsaRstHMatInitPowerFrame(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_HMAT_INIT_RMS_DB_NO_GAP);
}

scpi_result_t GetVsaRstHMatInitPowerAll(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_HMAT_INIT_RMS_DB);
}

// BT Part
// 配置
scpi_result_t SetVsaBTAlzRate(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_BT_DATARATE_Auto > ParamVal && WT_BT_BLE_500K < ParamVal); // AUTO|1M|2M|3M|BLE_1M|BLE_2M|BLE_125K|BLE_500K
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTDataRate = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze Rate=" << attr->vsaAlzParam.analyzeParamBt.BTDataRate << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzPacketType(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        // 0 ~26,NULL|POLL|FHS|DH1|DH3|DH5|DM1|DM3|DM5|HV1|HV2|HV3|DV|AUX1|EV3|EV4|EV5|2DH1|2DH3|2DH5|3DH1|3DH3|3DH5|2EV3|2EV5|3EV3|3EV5
        ILLEGAL_PARAM_RETURN(WT_BT_PACKETTYPE_NULL > ParamVal && WT_BT_PACKETTYPE_3_EV5 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTPktType = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze Package Type=" << attr->vsaAlzParam.analyzeParamBt.BTPktType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzACPViewRangeType(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        
        ILLEGAL_PARAM_RETURN(0 > ParamVal && 2 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.ACPViewRangeType = ParamVal;
        
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze ACPViewRangeType =" << attr->vsaAlzParam.analyzeParamBt.ACPViewRangeType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzACPSweepTimes(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        
        ILLEGAL_PARAM_RETURN(1 > ParamVal && 10 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.ACPSweepTimes = ParamVal;
        
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze ACPSweepTimes =" << attr->vsaAlzParam.analyzeParamBt.ACPSweepTimes << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzBleEnhancedMode(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal && 1 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTBleEnhance = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze BTBleEnhance =" << attr->vsaAlzParam.analyzeParamBt.BTBleEnhance << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzBlePhysicalChannelPDUType(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        
        ILLEGAL_PARAM_RETURN(0 > ParamVal && 1 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTBlePDUPktype = ParamVal;

        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze BTBlePDUPktype =" << attr->vsaAlzParam.analyzeParamBt.BTBlePDUPktype << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzBleSyncMode(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        
        ILLEGAL_PARAM_RETURN(0 > ParamVal && 1 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTBleSyncMode = ParamVal;

        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze BTBleSyncMode =" << attr->vsaAlzParam.analyzeParamBt.BTBleSyncMode << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzBleAccessAddress(scpi_t * context)
{
    unsigned int ParamVal = 0;
    do
    {
        if (!SCPI_ParamUnsignedInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTBleAccessAddress = ParamVal;
        
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze BTBleAccessAddress =" << attr->vsaAlzParam.analyzeParamBt.BTBleAccessAddress << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaBTAlzBleChannelIndex(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        
        ILLEGAL_PARAM_RETURN(0 > ParamVal && 39 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamBt.BTBleChannelIndex = ParamVal;
        
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BT analyze BTBleChannelIndex =" << attr->vsaAlzParam.analyzeParamBt.BTBleChannelIndex << endl;
    } while (0);

    return SCPI_ResultOK(context);
}
// 结果
scpi_result_t GetVsaBTInfo(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Demo = -1;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
    IF_ERR_RETURN(iRet);

    if (Demo != WT_DEMOD_BT)
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        IF_ERR_RETURN(iRet);
    }

    DataInfoBT DataInfo;
    DataInfoBTPlus DataInfoPlus;
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfoPlus, sizeof(DataInfoBTPlus));
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, DataInfo.FreqDrift);
    SCPI_ResultDouble(context, DataInfo.FreqDriftRate);
    SCPI_ResultDouble(context, DataInfo.F1Max);
    SCPI_ResultDouble(context, DataInfo.F1Avg);
    SCPI_ResultDouble(context, DataInfoPlus.Delta_F1_Min);
    SCPI_ResultDouble(context, DataInfo.F2Max);
    SCPI_ResultDouble(context, DataInfo.F2Avg);
    SCPI_ResultDouble(context, DataInfoPlus.Delta_F2_Min);
    SCPI_ResultDouble(context, DataInfoPlus.Delta_F2_99p9PCT);

    SCPI_ResultDouble(context, DataInfo.OmegaI);
    SCPI_ResultDouble(context, DataInfo.OmegaIO);
    SCPI_ResultDouble(context, DataInfo.OmegaO);
    SCPI_ResultDouble(context, DataInfo.DevmAvg);
    SCPI_ResultDouble(context, DataInfo.DevmPeak);
    SCPI_ResultDouble(context, DataInfo.DiffPower);
    SCPI_ResultDouble(context, DataInfo.MaxFreqVar);
    SCPI_ResultDouble(context, DataInfo.Devm);
    SCPI_ResultDouble(context, DataInfoPlus.EDR_GuardTime);
    SCPI_ResultInt(context, DataInfoPlus.EDR_SynSeq_ErrBit_Num);
    SCPI_ResultInt(context, DataInfoPlus.EDR_Trailer_ErrBit_Num);

    SCPI_ResultDouble(context, DataInfoPlus.Delta_F1_99p9PCT);
    SCPI_ResultDouble(context, DataInfo.FnMax);
    SCPI_ResultDouble(context, DataInfo.F0FnMax);
    SCPI_ResultDouble(context, DataInfoPlus.F0FnAvg);
    SCPI_ResultDouble(context, DataInfo.FnFn5Max);
    SCPI_ResultDouble(context, DataInfo.F1F0);
    SCPI_ResultDouble(context, DataInfo.F0F3);
    SCPI_ResultDouble(context, DataInfo.FnFn3);
    SCPI_ResultDouble(context, DataInfoPlus.Freq_offset_sync);
    if (attr->CheckBusinessLic(WT_BT5_1_API) == true) // 判断5.1license，目前就控制CTE内容的显示
    {
        SCPI_ResultInt(context, DataInfoPlus.CTEInfo);
        SCPI_ResultInt(context, DataInfoPlus.CTEType);
        SCPI_ResultInt(context, DataInfoPlus.CTE_DurationT);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Avg);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Peak);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Peak_sub_Avg);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Fsi_Max);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Fsi_Min);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Fs1_sub_Fp);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Fsi_sub_F0_Max);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Fsi_sub_Fsi3_Max);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Ref_Avg);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Ref_DevDivAvg);
        SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Pn_DevDivAvg_Max);
        for (int i = 0; i < sizeof(DataInfoPlus.CTE_Pwr_Avg_Slot) / sizeof(DataInfoPlus.CTE_Pwr_Avg_Slot[0]); i++)
        {
            SCPI_ResultDouble(context, DataInfoPlus.CTE_Pwr_Avg_Slot[i]);
        }
    }
    else
    {
        int UnvalidIntVal = -999;
        double UnvalidDoubleVal = -999.99;
        SCPI_ResultInt(context, UnvalidIntVal);
        SCPI_ResultInt(context, UnvalidIntVal);
        SCPI_ResultInt(context, UnvalidIntVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        SCPI_ResultDouble(context, UnvalidDoubleVal);
        for (int i = 0; i < sizeof(DataInfoPlus.CTE_Pwr_Avg_Slot) / sizeof(DataInfoPlus.CTE_Pwr_Avg_Slot[0]); i++)
        {
            SCPI_ResultDouble(context, UnvalidDoubleVal);
        }
    }

    SCPI_ResultDouble(context, DataInfoPlus.EDR_GFSKPower);
    SCPI_ResultDouble(context, DataInfoPlus.EDR_DPSKPower);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaBTPacketType(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PACKET_TYPE);
}

scpi_result_t GetVsaBTPacketLength(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PACKET_LENGTH);
}

scpi_result_t GetVsaBTPacketDataRate(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PACKET_DATA_RATE);
}

scpi_result_t GetVsaBTPacketInitFrequencyError(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_PACKET_INIT_FREQ_ERR);
}

scpi_result_t GetVsaBTFreqDrift(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_CARR_FREQ_DRIFT);
}

scpi_result_t GetVsaBTFreqDriftRate(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_CARR_FREQ_DRIFT_RATE);
}

scpi_result_t GetVsaBTMaxCarrierFreq(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_CARR_FREQ_TOL);
}

scpi_result_t GetVsaBTDeltaF1Valid(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_DELTA_F1_VALID);
}

scpi_result_t GetVsaBTDeltaF1Avgrage(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F1_AVG);
}

scpi_result_t GetVsaBTDeltaF1Maximun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F1_MAX);
}

scpi_result_t GetVsaBTDeltaF2Valid(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_DELTA_F2_VALID);
}

scpi_result_t GetVsaBTDeltaF2Avgrage(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_AVG);
}

scpi_result_t GetVsaBTDeltaF2Maximun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_MAX);
}

scpi_result_t GetVsaEdrEvmValid(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_RMS_DEVM_VALID);
}

scpi_result_t GetVsaEdrEvmCarrierFreqBuf(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_CARR_FREQ_BUF);
}

scpi_result_t GetVsaEdrEvm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_RMS_DEVM);
}

scpi_result_t GetVsaEdrEvmPeak(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_PK_DEVM);
}

scpi_result_t GetVsaBTPowerDiff(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_REL_PWR);
}

scpi_result_t GetVsaBT99Pct(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_PROB_99_EVM_PASS);
}

scpi_result_t GetVsaBTEDRBelowThresholdPct(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_PROB_99_EVM_PASS_PERCENT);
}

scpi_result_t GetVsaBTOmegaI(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_OMEAG_I);
}

scpi_result_t GetVsaBTOmegaO(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_OMEAG_O);
}

scpi_result_t GetVsaBTOmegaIO(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_OMEAG_IO);
}

scpi_result_t GetVsaBTBw20dbPassed(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_BANDWIDTH_20DB_Passed);
}

scpi_result_t GetVsaBTBw20dbSpectrum(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_BANDWIDTH_20DB);
}

scpi_result_t GetVsaBTBw20dbOBWandRBW(scpi_t *context)
{
    scpi_result_t result = GetRstDoubleVectorData(context, WT_RES_BT_BANDWIDTH_20DB_OBW);
    if (SCPI_RES_OK == result)
    {
        result = GetRstDoubleVectorData(context, WT_RES_BT_BANDWIDTH_20DB_RBW);
    }
    return result;
}

scpi_result_t GetVsaBLEDriftDetailValid(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_BLE_DRIFT_DETAIL_VALID);
}

scpi_result_t GetVsaBLEFnMaximun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BLE_FnMax);
}

scpi_result_t GetVsaBLEF0FnMaximun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BLE_F0FnMax);
}

scpi_result_t GetVsaBLEDeltaF1F0(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BLE_Delta_F1F0);
}

scpi_result_t GetVsaBLEDeltaFnFn5Maximun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BLE_F0Fn5_Max);
}

scpi_result_t GetVsaBLEDeltaF0F3(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BLE_Delta_F0F3);
}

scpi_result_t GetVsaBLEDeltaF0Fn3(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BLE_Delta_F0FN3);
}

scpi_result_t GetVsaBTSpectAcp(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_SPEC_ACP);
}

scpi_result_t GetVsaBTSpectAcpMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_SPEC_ACP_MASK);
}

scpi_result_t GetVsaBTDeltaF2PassPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_PASS_PERCENT);
}

scpi_result_t GetVsaBTFreqOffsetHeader(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_FREQ_OFFSET_HEADER);
}

scpi_result_t GetVsaBTFreqOffsetSync(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_FREQ_OFFSET_SYNC);
}

scpi_result_t GetVsaBTFreqDeviation(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_RES_FREQ_DEV);
}

scpi_result_t GetVsaBTFreqDeviationPeaktoPeak(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_RES_FREQ_DEV_PK_TO_PK);
}

scpi_result_t GetVsaBTDeltaF2AvAccess(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_AVG_ACCESS);
}

scpi_result_t GetVsaBTDeltaF2MaxAccess(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_MAX_ACCESS);
}

scpi_result_t GetVsaBTEdrEvmVsTime(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_MAX_DEVM_AVG);
}

scpi_result_t GetVsaBTFrequencyError(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_CARR_FREQ_BUF);
}

scpi_result_t GetVsaBTBw20dbSpectrumArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_BANDWIDTH_20DB, true);
}

scpi_result_t GetVsaBTSpectAcpArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_SPEC_ACP, true);
}

scpi_result_t GetVsaBTSpectAcpMaskArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_SPEC_ACP_MASK, true);
}

scpi_result_t GetVsaBTEdrEvmVsTimeArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_MAX_DEVM_AVG, true);
}

scpi_result_t GetVsaBTFrequencyErrorArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BT_CARR_FREQ_BUF, true);
}

scpi_result_t GetVsaBTDeltaF1MINimun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F1_MIN);
}

scpi_result_t GetVsaBTDeltaF2MINimun(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_MIN);
}

scpi_result_t GetVsaBTMaxFreqVar(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_EDR_MAX_FREQ_VAR);
}

scpi_result_t GetVsaBLEF0FnAvg(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_F0FN_AVG);
}

scpi_result_t GetVsaBTBREDRLAP(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BR_EDR_LAP);
}

scpi_result_t GetVsaBTBREDRUAP(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BR_EDR_UAP);
}

scpi_result_t GetVsaBTBLECTEInfo(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BLE_CTEINFO);
}

scpi_result_t GetVsaBTBLECTEType(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BLE_CTEType);
}

scpi_result_t GetVsaBTBLECTEDurationTime(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BLE_CTE_DURATIONT);
}

scpi_result_t GetVsaBTLT_ADDR(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_LT_ADDR);
}

scpi_result_t GetVsaBTFlowCtrl(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_FLOW_CTRL);
}

scpi_result_t GetVsaBTACKIndication(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_ACK_INDICATION);
}

scpi_result_t GetVsaBTSeqnum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_SEQ_NUM);
}

scpi_result_t GetVsaBTLLID(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_LLID);
}

scpi_result_t GetVsaBTFlow(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_FLOW);
}

scpi_result_t GetVsaBTPayloadsize(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PAYLOADSIZE);
}

scpi_result_t GetVsaBTEIR(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PAYLOAD_EIR);
}

scpi_result_t GetVsaBTSR(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PAYLOAD_SR);
}

scpi_result_t GetVsaBTClassOfDevice(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PAYLOAD_CLASS_OF_DEVICE);
}

scpi_result_t GetVsaBTLtaddr(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PAYLOAD_LTADDR);
}

scpi_result_t GetVsaBTClk27b2(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PAYLOAD_CLK27B2);
}

scpi_result_t GetVsaBTBLEMappers(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_BLEMAPPERS);
}

scpi_result_t GetVsaBTVoiceField(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_BT_VOICE_FIELD);
}

scpi_result_t GetVsaBTPayloadHeader(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_BT_PAYLOAD_HEADER);
}

scpi_result_t GetVsaBTPattern(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_PATTERN);
}

scpi_result_t GetVsaBTEDRGFSKPower(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EDR_GFSK_POWER);
}

scpi_result_t GetVsaBTEDRDPSKPower(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EDR_DPSK_POWER);
}

scpi_result_t GetVsaBTEDRGFSKPowerPeak(scpi_t * context)
{
   return GetRstDoubleData(context, WT_RES_EDR_GFSK_POWER_PEAK);
}

scpi_result_t GetVsaBTEDRDPSKPowerPeak(scpi_t * context)
{
   return GetRstDoubleData(context, WT_RES_EDR_DPSK_POWER_PEAK);
}

scpi_result_t GetVsaBTEDRGuardtime(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_EDR_GUARD_TIME);
}

scpi_result_t GetVsaBTBLECTEPwrAvg(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_PWR_AVG);
}

scpi_result_t GetVsaBTBLECTEPwrPeak(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_PWR_PEAK);
}

scpi_result_t GetVsaBTBLECTEPwrSubAvg(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_PWR_SUB_AVG);
}

scpi_result_t GetVsaBTBLECTEFsiMax(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FSI_MAX);
}

scpi_result_t GetVsaBTBLECTEFsiMin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FSI_MIN);
}

scpi_result_t GetVsaBTBLECTEFs1SubFp(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FS1_SUB_FP);
}

scpi_result_t GetVsaBTBLECTEFsiSubF0Max(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FSI_SUB_F0_MAX);
}

scpi_result_t GetVsaBTBLECTEFsiSubFsi3Max(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FSI_SUB_FSI3_MAX);
}

scpi_result_t GetVsaBTBLECTEPwrRefAvg(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FWR_REF_AVG);
}

scpi_result_t GetVsaBTBLECTEPwrRefDev(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FWR_REF_Dev);
}

scpi_result_t GetVsaBTBLECTEPwrRefDevMax(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_CTE_FWR_REF_Dev_MAX);
}

scpi_result_t GetVsaBTBLECTEPwrAvgSlot(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BLE_CTE_PWR_AVG_SLOT);
}

scpi_result_t GetVsaBTBREDRHeaderBin(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_BR_EDR_HEADERBIN);
}

scpi_result_t GetVsaBTEDRSynSeqErrorBitNumber(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_EDR_SYNSEQ_ERR_BIT_NUM);
}

scpi_result_t GetVsaBTEDRTrailerErrorBitNumber(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_EDR_TRAILER_ERR_BIT_NUM);
}

scpi_result_t GetVsaBTDeltaF199p9Precent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F1_99p9_PRECENT);
}

scpi_result_t GetVsaBTDeltaF299p9Precent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DELTA_F2_99p9_PRECENT);
}

scpi_result_t GetVsaBTPacketPayloadHeaderBits(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_BT_PACKET_PAYLOAD_HEADER_BITS, true);
}

scpi_result_t GetVsaBTPsduBits(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_BT_PSDU_BIN, true);
}

scpi_result_t GetVsaBTPsduCRC(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BT_PSDU_CRC);
}

scpi_result_t GetVsaBLEEnhancedMode(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_BLE_ENHANCED_MODE);
}

scpi_result_t GetVsaBTLeakagePower(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_LEAKAGE_POWER);
}

scpi_result_t GetVsaBTDifferencebetweenPowerPeakAndAverage(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_DIFF_POWER_PEAK_VS_AVG);
}

scpi_result_t GetVsaBLEDeltaF2AvgDivDeltaF1Avg(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BLE_DELTAF2AVG_DIV_DELTAF1AVG);
}

scpi_result_t GetVsaBTEDRPhaseDifferenceArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EDR_PHASE_DIFFERENCE, true);
}

scpi_result_t GetVsaBTBrBLEFrequencyDeviationArbData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_BR_BLE_FREQ_DEVIATION, true);
}

scpi_result_t GetVsaBTBw20dbSpectrumFrequencyLow(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BANDWIDTH_20DB_FREQ_LOW);
}

scpi_result_t GetVsaBTBw20dbSpectrumFrequencyHigh(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_BT_BANDWIDTH_20DB_FREQ_HIGH);
}

scpi_result_t SetVsaZigbeeAnalyzeOptimise(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamZigBee.Optimize = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamZigBee.Optimize=" << attr->vsaAlzParam.analyzeParamZigBee.Optimize << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t GetVsaZigbeeEvmPsdu(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_ZIGBEE_EVM_PSDU);
}

scpi_result_t GetVsaZigbeeEvmPsduPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_ZIGBEE_EVM_PSDU_PERCENT);
}

scpi_result_t GetVsaZigbeeEvmShrPhr(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_ZIGBEE_EVM_SHRPHR);
}

scpi_result_t GetVsaZigbeeEvmShrPhrPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_ZIGBEE_EVM_SHRPHR_PERCENT);
}

scpi_result_t GetVsaZigbeeEvmOffset(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_ZIGBEE_EVM_OFFSET_DB);
}

scpi_result_t GetVsaZigbeeEvmOffsetPercent(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_ZIGBEE_EVM_OFFSET_PERCENT);
}

scpi_result_t GetVsaZigbeeEyeRealGraphic(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ZIGBEE_EYE_REAL, true);
}

scpi_result_t GetVsaZigbeeEyeImagGraphic(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ZIGBEE_EYE_IMAG, true);
}

scpi_result_t GetVsaZigbeePhaseErrorVsChip(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ZIGBEE_PHASE_ERR_VS_CHIP, true);
}


scpi_result_t SCPI_3GPP_GetVsaRstSpectrumEmissionDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_RXSPECTEMIS, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumEmissionMaskDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_SPECTEMISMASK, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumEmissionMaskSegDataARB(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_SPECTRAL_EMISMASKSEG, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumBadPointcnt(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_BADPOINTCNT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumFreqStart(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_FREQ_START, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumFreqCenter(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_FREQ_CENTER, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumFreqEnd(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_FREQ_END, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSymoblConstARB(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_RXPOINT_HIGH_DATA : WT_RES_3GPP_EVM_RXPOINT_LOW_DATA, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSymoblPilotConstARB(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_RXPOINT_HIGH_PILOT : WT_RES_3GPP_EVM_RXPOINT_LOW_PILOT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSummaryLTE(scpi_t *context)
{
#define UNVALID_DOUBLE_VAL            (-999.99)
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    struct {
        double RmsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PeakEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double DmrsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double CarrierLeakage = UNVALID_DOUBLE_VAL;
        double IqImbaAmp= UNVALID_DOUBLE_VAL;
        double IqImbaPhs= UNVALID_DOUBLE_VAL;
        double FreqErr= UNVALID_DOUBLE_VAL;
        double FrmAvgPwrdBm= UNVALID_DOUBLE_VAL;
        double FrmPeakPwrdBm= UNVALID_DOUBLE_VAL;
        double obw_bw= UNVALID_DOUBLE_VAL;
        double EutraResult= UNVALID_DOUBLE_VAL;
        double UtraResult= UNVALID_DOUBLE_VAL;
        double SEMResult= UNVALID_DOUBLE_VAL;
        double InEmisResult= UNVALID_DOUBLE_VAL;
        double FlatResult= UNVALID_DOUBLE_VAL;
    }Summary;

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_RMSEVM, Summary.RmsEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_PEAKEVM, Summary.PeakEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_DMRSEVM, Summary.DmrsEvm, sizeof(Summary.DmrsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRRMS, Summary.MagnErrRms, sizeof(Summary.MagnErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRPEAK, Summary.MagnErrPeak, sizeof(Summary.MagnErrPeak), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRDMRS, Summary.MagnErrDmrs, sizeof(Summary.MagnErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRRMS, Summary.PhaseErrRms, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRPEAK, Summary.PhaseErrPeak, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRDMRS, Summary.PhaseErrDmrs, sizeof(Summary.PhaseErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_CARRIER_LEAKAGE, &Summary.CarrierLeakage, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_IQ_MATCH_AMP_DB, &Summary.IqImbaAmp, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_IQ_MATCH_PHASE, &Summary.IqImbaPhs, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_FREQ_ERR_HZ, &Summary.FreqErr, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMAVGPWRDBM, &Summary.FrmAvgPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMPEAKPWRDBM, &Summary.FrmPeakPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_OBW, &Summary.obw_bw, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_ACLR_EUTRARESULT, &Summary.EutraResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_ACLR_UTRARESULT, &Summary.UtraResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SPECTRAL_SEMRESULT, &Summary.SEMResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_INEMIS_INEMISRESULT, &Summary.InEmisResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED, &Summary.FlatResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    SCPI_ResultDouble(context, Summary.RmsEvm[0]);
    SCPI_ResultDouble(context, Summary.RmsEvm[1]);
    SCPI_ResultDouble(context, Summary.PeakEvm[0]);
    SCPI_ResultDouble(context, Summary.PeakEvm[1]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[0]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[1]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[0]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[1]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[0]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[1]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[1]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[1]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[1]);    
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[1]);
    SCPI_ResultDouble(context, Summary.CarrierLeakage);
    SCPI_ResultDouble(context, Summary.IqImbaAmp);
    SCPI_ResultDouble(context, Summary.IqImbaPhs);
    SCPI_ResultDouble(context, Summary.FreqErr);
    SCPI_ResultDouble(context, Summary.FrmAvgPwrdBm);
    SCPI_ResultDouble(context, Summary.FrmPeakPwrdBm);
    SCPI_ResultDouble(context, Summary.obw_bw);
    SCPI_ResultInt(context, Summary.EutraResult);
    SCPI_ResultInt(context, Summary.UtraResult);
    SCPI_ResultInt(context, Summary.SEMResult);
    SCPI_ResultInt(context, Summary.InEmisResult);
    SCPI_ResultInt(context, Summary.FlatResult);
#undef UNVALID_DOUBLE_VAL
    return SCPI_RES_OK;
}

scpi_result_t SCPI_3GPP_GetVsaRstSummaryNR(scpi_t *context)
{
#define UNVALID_DOUBLE_VAL            (-999.99)
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    struct {
        double RmsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PeakEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double DmrsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double CarrierLeakage = UNVALID_DOUBLE_VAL;
        double FreqErr = UNVALID_DOUBLE_VAL;
        double FrmAvgPwrdBm = UNVALID_DOUBLE_VAL;
        double FrmPeakPwrdBm = UNVALID_DOUBLE_VAL;
        double obw_bw = UNVALID_DOUBLE_VAL;
    }Summary;

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_RMSEVM, Summary.RmsEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_PEAKEVM, Summary.PeakEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_DMRSEVM, Summary.DmrsEvm, sizeof(Summary.DmrsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRRMS, Summary.MagnErrRms, sizeof(Summary.MagnErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRPEAK, Summary.MagnErrPeak, sizeof(Summary.MagnErrPeak), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRDMRS, Summary.MagnErrDmrs, sizeof(Summary.MagnErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRRMS, Summary.PhaseErrRms, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRPEAK, Summary.PhaseErrPeak, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRDMRS, Summary.PhaseErrDmrs, sizeof(Summary.PhaseErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_CARRIER_LEAKAGE, &Summary.CarrierLeakage, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_FREQ_ERR_HZ, &Summary.FreqErr, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMAVGPWRDBM, &Summary.FrmAvgPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMPEAKPWRDBM, &Summary.FrmPeakPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_OBW, &Summary.obw_bw, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    SCPI_ResultDouble(context, Summary.RmsEvm[0]);
    SCPI_ResultDouble(context, Summary.RmsEvm[1]);
    SCPI_ResultDouble(context, Summary.PeakEvm[0]);
    SCPI_ResultDouble(context, Summary.PeakEvm[1]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[0]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[1]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[0]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[1]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[0]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[1]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[1]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[1]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[1]);    
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[1]);
    SCPI_ResultDouble(context, Summary.CarrierLeakage);
    SCPI_ResultDouble(context, Summary.FreqErr);
    SCPI_ResultDouble(context, Summary.FrmAvgPwrdBm);
    SCPI_ResultDouble(context, Summary.FrmPeakPwrdBm);
    SCPI_ResultDouble(context, Summary.obw_bw);
#undef UNVALID_DOUBLE_VAL
    return SCPI_RES_OK;
}

scpi_result_t SCPI_3GPP_GetVsaRstSummaryNRUL(scpi_t *context)
{
#define UNVALID_DOUBLE_VAL            (-999.99)
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    struct {
        double RmsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PeakEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double DmrsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double IQOffset = UNVALID_DOUBLE_VAL;
        double IQImbAmp = UNVALID_DOUBLE_VAL;
        double IQImbPhase = UNVALID_DOUBLE_VAL;
        double FreqErr = UNVALID_DOUBLE_VAL;
        double FrmAvgPwrdBm = UNVALID_DOUBLE_VAL;
        double FrmPeakPwrdBm = UNVALID_DOUBLE_VAL;
        double RSRP = UNVALID_DOUBLE_VAL;
        double RSSI = UNVALID_DOUBLE_VAL;
        double RSRQ = UNVALID_DOUBLE_VAL;
        double SNR = UNVALID_DOUBLE_VAL;
        double obw_bw = UNVALID_DOUBLE_VAL;
        double NrUtraResult = UNVALID_DOUBLE_VAL;
        double UtraResult = UNVALID_DOUBLE_VAL;
        double SEMResult = UNVALID_DOUBLE_VAL;
        double InEmisResult = UNVALID_DOUBLE_VAL;
        double FlatResult = UNVALID_DOUBLE_VAL;
    }Summary;

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_RMSEVM, Summary.RmsEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_PEAKEVM, Summary.PeakEvm, sizeof(Summary.PeakEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_DMRSEVM, Summary.DmrsEvm, sizeof(Summary.DmrsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRRMS, Summary.MagnErrRms, sizeof(Summary.MagnErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRPEAK, Summary.MagnErrPeak, sizeof(Summary.MagnErrPeak), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRDMRS, Summary.MagnErrDmrs, sizeof(Summary.MagnErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRRMS, Summary.PhaseErrRms, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRPEAK, Summary.PhaseErrPeak, sizeof(Summary.PhaseErrPeak), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRDMRS, Summary.PhaseErrDmrs, sizeof(Summary.PhaseErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_CARRIER_LEAKAGE, &Summary.IQOffset, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_IQ_MATCH_AMP_DB, &Summary.IQImbAmp, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_IQ_MATCH_PHASE, &Summary.IQImbPhase, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_FREQ_ERR_HZ, &Summary.FreqErr, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMAVGPWRDBM, &Summary.FrmAvgPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMPEAKPWRDBM, &Summary.FrmPeakPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_POWER, &Summary.RSRP, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_RECEIVED_SIGNAL_STRENGTH_INDICATION, &Summary.RSSI, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_QUALITY, &Summary.RSRQ, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SIGNAL_TO_NOISE_RATIO, &Summary.SNR, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_OBW, &Summary.obw_bw, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_ACLR_NRUTRARESULT, &Summary.NrUtraResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_ACLR_UTRARESULT, &Summary.UtraResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SPECTRAL_SEMRESULT, &Summary.SEMResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_INEMIS_INEMISRESULT, &Summary.InEmisResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED, &Summary.FlatResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    SCPI_ResultDouble(context, Summary.RmsEvm[0]);
    SCPI_ResultDouble(context, Summary.RmsEvm[1]);
    SCPI_ResultDouble(context, Summary.PeakEvm[0]);
    SCPI_ResultDouble(context, Summary.PeakEvm[1]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[0]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[1]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[0]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[1]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[0]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[1]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[1]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[1]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[1]);    
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[1]);
    SCPI_ResultDouble(context, Summary.IQOffset);
    SCPI_ResultDouble(context, Summary.IQImbAmp);
    SCPI_ResultDouble(context, Summary.IQImbPhase);
    SCPI_ResultDouble(context, Summary.FreqErr);
    SCPI_ResultDouble(context, Summary.FrmAvgPwrdBm);
    SCPI_ResultDouble(context, Summary.FrmPeakPwrdBm);
    SCPI_ResultDouble(context, Summary.RSRP);
    SCPI_ResultDouble(context, Summary.RSSI);
    SCPI_ResultDouble(context, Summary.RSRQ);
    SCPI_ResultDouble(context, Summary.SNR);
    SCPI_ResultDouble(context, Summary.obw_bw);
    SCPI_ResultInt(context, static_cast<int>(Summary.NrUtraResult));
    SCPI_ResultInt(context, static_cast<int>(Summary.UtraResult));
    SCPI_ResultInt(context, static_cast<int>(Summary.SEMResult));
    SCPI_ResultInt(context, static_cast<int>(Summary.InEmisResult));
    SCPI_ResultInt(context, static_cast<int>(Summary.FlatResult));
#undef UNVALID_DOUBLE_VAL
    return SCPI_RES_OK;
}

scpi_result_t SCPI_3GPP_GetVsaRstSummaryNIOT(scpi_t *context)
{
#define UNVALID_DOUBLE_VAL            (-999.99)
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    struct{
        double RmsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PeakEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double DmrsEvm[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double MagnErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrRms[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrPeak[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double PhaseErrDmrs[2] = {UNVALID_DOUBLE_VAL,UNVALID_DOUBLE_VAL};
        double FreqErr= UNVALID_DOUBLE_VAL;
        double CarrierLeakage= UNVALID_DOUBLE_VAL;
        double FrmAvgPwrdBm= UNVALID_DOUBLE_VAL;
        double FrmPeakPwrdBm= UNVALID_DOUBLE_VAL;
        double SCAvgPwrdBm= UNVALID_DOUBLE_VAL;
        double RSRP= UNVALID_DOUBLE_VAL;
        double RSSI= UNVALID_DOUBLE_VAL;
        double RSRQ= UNVALID_DOUBLE_VAL;
        double SNR= UNVALID_DOUBLE_VAL;
        double obw_bw= UNVALID_DOUBLE_VAL;
        double UtraResult= UNVALID_DOUBLE_VAL;
        double SEMResult= UNVALID_DOUBLE_VAL;
        double InEmisResult= UNVALID_DOUBLE_VAL;
    }Summary;

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_RMSEVM, Summary.RmsEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_PEAKEVM, Summary.PeakEvm, sizeof(Summary.RmsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_EVM_DMRSEVM, Summary.DmrsEvm, sizeof(Summary.DmrsEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRRMS, Summary.MagnErrRms, sizeof(Summary.MagnErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRPEAK, Summary.MagnErrPeak, sizeof(Summary.MagnErrPeak), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRDMRS, Summary.MagnErrDmrs, sizeof(Summary.MagnErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRRMS, Summary.PhaseErrRms, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRPEAK, Summary.PhaseErrPeak, sizeof(Summary.PhaseErrRms), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRDMRS, Summary.PhaseErrDmrs, sizeof(Summary.PhaseErrDmrs), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_FREQ_ERR_HZ, &Summary.FreqErr, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_CARRIER_LEAKAGE, &Summary.CarrierLeakage, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMAVGPWRDBM, &Summary.FrmAvgPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FRMPEAKPWRDBM, &Summary.FrmPeakPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SCAVGPWRDBM, &Summary.SCAvgPwrdBm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}  
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_POWER, &Summary.RSRP, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_RECEIVED_SIGNAL_STRENGTH_INDICATION, &Summary.RSSI, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_QUALITY, &Summary.RSRQ, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SIGNAL_TO_NOISE_RATIO, &Summary.SNR, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_SPECTRUM_OBW, &Summary.obw_bw, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_ACLR_UTRARESULT, &Summary.UtraResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SPECTRAL_SEMRESULT, &Summary.SEMResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_INEMIS_INEMISRESULT, &Summary.InEmisResult, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    SCPI_ResultDouble(context, Summary.RmsEvm[0]);
    SCPI_ResultDouble(context, Summary.PeakEvm[0]);
    SCPI_ResultDouble(context, Summary.DmrsEvm[0]);
    SCPI_ResultDouble(context, Summary.MagnErrRms[0]);
    SCPI_ResultDouble(context, Summary.MagnErrPeak[0]);
    SCPI_ResultDouble(context, Summary.MagnErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrRms[0]);
    SCPI_ResultDouble(context, Summary.PhaseErrPeak[0]); 
    SCPI_ResultDouble(context, Summary.PhaseErrDmrs[0]);
    SCPI_ResultDouble(context, Summary.FreqErr);
    SCPI_ResultDouble(context, Summary.CarrierLeakage);
    SCPI_ResultDouble(context, Summary.FrmAvgPwrdBm);
    SCPI_ResultDouble(context, Summary.FrmPeakPwrdBm);
    SCPI_ResultDouble(context, Summary.SCAvgPwrdBm);
    SCPI_ResultDouble(context, Summary.RSRP);
    SCPI_ResultDouble(context, Summary.RSSI);
    SCPI_ResultDouble(context, Summary.RSRQ);
    SCPI_ResultDouble(context, Summary.SNR);
    SCPI_ResultDouble(context, Summary.obw_bw);
    SCPI_ResultInt(context, Summary.UtraResult);
    SCPI_ResultInt(context, Summary.SEMResult);
    SCPI_ResultInt(context, Summary.InEmisResult);
#undef UNVALID_DOUBLE_VAL
    return SCPI_RES_OK;
}

scpi_result_t SCPI_3GPP_GetVsaRstPBCHSSBInfo(scpi_t *context)
{
#define UNVALID_DOUBLE_VAL            (-999.99)
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_SSB_NUM, &Result, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}

    double SSBIdx[(int)Result];
    memset(SSBIdx, UNVALID_DOUBLE_VAL, sizeof(SSBIdx));
    double State[(int)Result];
    memset(State, UNVALID_DOUBLE_VAL, sizeof(State));
    double PSSEvm[(int)Result];
    memset(PSSEvm, UNVALID_DOUBLE_VAL, sizeof(PSSEvm));
    double SSSEvm[(int)Result];
    memset(SSSEvm, UNVALID_DOUBLE_VAL, sizeof(SSSEvm));
    double PBCHEvm[(int)Result];
    memset(PBCHEvm, UNVALID_DOUBLE_VAL, sizeof(PBCHEvm));
    double Crc[(int)Result];
    memset(Crc, UNVALID_DOUBLE_VAL, sizeof(Crc));
    double HalfFrm[(int)Result];
    memset(HalfFrm, UNVALID_DOUBLE_VAL, sizeof(HalfFrm));
    double SFN[(int)Result];
    memset(SFN, UNVALID_DOUBLE_VAL, sizeof(SFN));
    double DmrsPos[(int)Result];
    memset(DmrsPos, UNVALID_DOUBLE_VAL, sizeof(DmrsPos));
    double SCSComm[(int)Result];
    memset(SCSComm, UNVALID_DOUBLE_VAL, sizeof(SCSComm));
    double SCOffset[(int)Result];
    memset(SCOffset, UNVALID_DOUBLE_VAL, sizeof(SCOffset));
    double CoresetZero[(int)Result];
    memset(CoresetZero, UNVALID_DOUBLE_VAL, sizeof(CoresetZero));
    double SSZero[(int)Result];
    memset(SSZero, UNVALID_DOUBLE_VAL, sizeof(SSZero));
    double CellBarre[(int)Result];
    memset(CellBarre, UNVALID_DOUBLE_VAL, sizeof(CellBarre));
    double InFreqResel[(int)Result];
    memset(InFreqResel, UNVALID_DOUBLE_VAL, sizeof(InFreqResel));

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_INDEX, SSBIdx, sizeof(SSBIdx), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_STATE, State, sizeof(State), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_PSS_EVM, PSSEvm, sizeof(PSSEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_SSS_EVM, SSSEvm, sizeof(SSSEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_PBCH_EVM, PBCHEvm, sizeof(PBCHEvm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_CRC, Crc, sizeof(Crc), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_HALF_FRAME, HalfFrm, sizeof(HalfFrm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_SYSTEM_FRAME_NUM, SFN, sizeof(SFN), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_DMRS_POS, DmrsPos, sizeof(DmrsPos), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_COMMON_SUB_SPACING, SCSComm, sizeof(SCSComm), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_SUB_OFFSET, SCOffset, sizeof(SCOffset), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_CORESET_ZERO, CoresetZero, sizeof(CoresetZero), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_SEARCH_SPACE_ZERO, SSZero, sizeof(SSZero), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_CELL_BARRED, CellBarre, sizeof(CellBarre), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_3GPP_SSB_INTRA_FREQ_RESEL, InFreqResel, sizeof(InFreqResel), streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);}

    for (int i = 0; i < (int)Result; i++)
    {
        SCPI_ResultInt(context, ((int *)SSBIdx)[i]);
        SCPI_ResultInt(context, ((int *)State)[i]);
        SCPI_ResultDouble(context, PSSEvm[i]);
        SCPI_ResultDouble(context, SSSEvm[i]);
        SCPI_ResultDouble(context, PBCHEvm[i]);
        SCPI_ResultInt(context, ((int *)Crc)[i]);
        SCPI_ResultInt(context, ((int *)HalfFrm)[i]);
        SCPI_ResultInt(context, ((int *)SFN)[i]);
        SCPI_ResultInt(context, ((int *)DmrsPos)[i]);
        SCPI_ResultInt(context, ((int *)SCSComm)[i]);
        SCPI_ResultInt(context, ((int *)SCOffset)[i]);
        SCPI_ResultInt(context, ((int *)CoresetZero)[i]);
        SCPI_ResultInt(context, ((int *)SSZero)[i]);
        SCPI_ResultInt(context, ((int *)CellBarre)[i]);
        SCPI_ResultInt(context, ((int *)InFreqResel)[i]);
    }
#undef UNVALID_DOUBLE_VAL
    return SCPI_RES_OK;
}

scpi_result_t SCPI_3GPP_GetVsaRstEvmRms(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_RMSEVM, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstEvmPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_PEAKEVM, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstEvmDmrs(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_DMRSEVM, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstPkgAvgPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_PKGAVGPWRDBM);
}

scpi_result_t SCPI_3GPP_GetVsaRstPkgPeakPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_PKGPEAKPWRDBM);
}

scpi_result_t SCPI_3GPP_GetVsaRstFrmAvgPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_FRMAVGPWRDBM);
}

scpi_result_t SCPI_3GPP_GetVsaRstFrmPeakPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_FRMPEAKPWRDBM);
}
scpi_result_t SCPI_3GPP_GetVsaRstEutraResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_EUTRARESULT);
}
scpi_result_t SCPI_3GPP_GetVsaRstNrUtraResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_NRUTRARESULT);
}
scpi_result_t SCPI_3GPP_GetVsaRstUtraResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_UTRARESULT);
}
scpi_result_t SCPI_3GPP_GetVsaRstSEMResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_SPECTRAL_SEMRESULT);
}
scpi_result_t SCPI_3GPP_GetVsaRstInEmisResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INEMIS_INEMISRESULT);
}
scpi_result_t SCPI_3GPP_GetVsaRstSubcarrierAvgPower(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_SCAVGPWRDBM);
}
scpi_result_t SCPI_3GPP_GetVsaRstReferenceSignalReceivingPower(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_POWER);
}
scpi_result_t SCPI_3GPP_GetVsaRstReceivedSignalStrengthIndication(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_RECEIVED_SIGNAL_STRENGTH_INDICATION);
}
scpi_result_t SCPI_3GPP_GetVsaRstReferenceSignalReceivingQuality(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_QUALITY);
}
scpi_result_t SCPI_3GPP_GetVsaRstSignaltoNoiseRatio(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_SIGNAL_TO_NOISE_RATIO);
}
scpi_result_t SCPI_3GPP_GetVsaRstLinkDirect(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_LINKDIRECT);
}
scpi_result_t SCPI_3GPP_GetVsaRstChannel(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_CHANNEL);
}
scpi_result_t SCPI_3GPP_GetVsaRstSlotIdx(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_SLOT);
}
scpi_result_t SCPI_3GPP_GetVsaRstCodeword(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_CODEWORD);
}
scpi_result_t SCPI_3GPP_GetVsaCwModulate(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_MODULATE, false);
}
scpi_result_t SCPI_3GPP_GetVsaRstCwScrambling(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_SCRAMBLING, false);
}
scpi_result_t SCPI_3GPP_GetVsaRstCwChannelCodingType(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_CHANNELCODINGTYPE, false);
}
scpi_result_t SCPI_3GPP_GetVsaRstCwCrc(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_CRC, false);
}
scpi_result_t SCPI_3GPP_GetVsaRstFormat(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_FORMAT, false);
}
scpi_result_t SCPI_3GPP_GetVsaRstHarqAckInfo(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_HARQ_ACK, false);
}
scpi_result_t SCPI_3GPP_GetVsaRstBitSeq(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstIntVectorData(context, (value == 1) ? WT_RES_3GPP_INFO_CW_BITSEQ_1 : WT_RES_3GPP_INFO_CW_BITSEQ_0, true);
}
scpi_result_t SCPI_3GPP_GetVsaRstEvmMaxCarrierLen(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_EVM_MAXCARRIERLEN);
}
scpi_result_t SCPI_3GPP_GetVsaRstSSBNum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_SSB_NUM);
}
scpi_result_t SCPI_3GPP_GetVsaRstEvmCarrierEvm(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_CARRIEREVM, true);
}
scpi_result_t SCPI_3GPP_GetVsaRstSymoblEVM(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_SYMBEVM_LOW_PILOT : WT_RES_3GPP_EVM_SYMBEVM_LOW_DATA, true);
}
scpi_result_t SCPI_3GPP_GetVsaRstPilotSymoblEVM(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_SYMBEVM_HIGH_PILOT : WT_RES_3GPP_EVM_SYMBEVM_HIGH_DATA, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessMaxFlatNum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_FLATNESS_MAXFLATNUM);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessFlatRes(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_DATA, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessFlatMaskUp(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessFlatMaskDown(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessRange(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_FLATNESS_RANGETYPE, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessRipple(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleData(context, (value == 1) ? WT_RES_3GPP_FLATNESS_RIPPLE1 : WT_RES_3GPP_FLATNESS_RIPPLE2);
}

scpi_result_t SCPI_3GPP_GetVsaRstFlatnessCrossRipple(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleData(context, (value == 1) ? WT_RES_3GPP_FLATNESS_MAXR2SUBMINR1 : WT_RES_3GPP_FLATNESS_MAXR1SUBMINR2);
}

scpi_result_t SCPI_3GPP_GetVsaRstInEmisEmission(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_INEMIS_RBEMIS, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstInEmisEmisRef(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_INEMIS_EMISREF, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstInEmisEmisRefSeg(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_INEMIS_EMISREFSEG, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstInEmisMarginMin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_INEMIS_MARGINMIN);
}

scpi_result_t SCPI_3GPP_GetVsaRstInEmisMarginMinIdx(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INEMIS_MARGINMINIDX);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_POWER, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_MASK, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraFreq(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_FREQ, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLREUtraAclRatio(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_ACLRATIO, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_POWER, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_MASK, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraFreq(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_FREQ, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraAclRatio(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_ACLRATIO, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_POWER, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_MASK, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraFreq(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_FREQ, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSpectrumACLRUtraAclRatio(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_ACLRATIO, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrMagnErr(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MPERR_MAGNERR_HIGH : WT_RES_3GPP_MPERR_MAGNERR_LOW, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrErrRms(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERRRMS, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrMagnErrPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERRPEAK, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrMagnErrDmrs(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERRDMRS, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrs(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MPERR_PHASEERR_HIGH : WT_RES_3GPP_MPERR_PHASEERR_LOW, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrRms(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRRMS, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRPEAK, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrDmrs(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRDMRS, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstModulationSymbolsEvm(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_POINTEVM, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstUEPwrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_UEPOWER_POWEROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstPhaseDiffOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_PHASEDIS_DIFFOUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstDynamicLimit(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_PHASEDIS_DYNAMICLIMIT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstUpperLimit(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_PHASEDIS_UPPERLIMIT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstMaxPhaseDis(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_PHASEDIS_MAXPHASEDIS);
}

scpi_result_t SCPI_3GPP_GetVsaRstMinSlotDis(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_PHASEDIS_MINSLOTDIS);
}

scpi_result_t SCPI_3GPP_GetVsaRstExceedUpperLimitNum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_PHASEDIS_EXCEEDUPPERLIMITNUM);
}

scpi_result_t SCPI_3GPP_GetVsaRstExceedDynamicLimitNum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_PHASEDIS_EXCEEDDYNAMICLIMITNUM);
}

scpi_result_t SCPI_3GPP_GetVsaRstFreqErrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_FREQERR_FREQERROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFreqErrLimit(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_FREQERR_FREQERRLIMIT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstFreqErr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_FREQERR_FREQERR, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstMagnErrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstMagnErrLimit(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_MPERR_MAGNERRLIMIT);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERROUT, false);
}

scpi_result_t SCPI_3GPP_GetVsaRstMPErrPhaseErrLimit(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRLIMIT, false);
}

scpi_result_t SCPI_3GPP_GetVsaMagnitudeErrorVsChipMagnErrChip(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_CHIP, true);
}

scpi_result_t SCPI_3GPP_GetVsaMagnitudeErrorVsChipMagnErrChiplimit(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_MPERR_CHIPLIMIT);
}

scpi_result_t SCPI_3GPP_GetVsaPhaseErrorVsChipPhaseErrChip(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASECHIP, true);
}

scpi_result_t SCPI_3GPP_GetVsaPhaseErrorVsChipPhaseErrChiplimit(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASECHIPLIMIT, true);
}

scpi_result_t SCPI_3GPP_GetVsaEvmOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_EVMOUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaEvmLimit(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_EVM_LIMIT);
}

scpi_result_t SCPI_3GPP_GetVsaEvmChip(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_CHIP, true);
}

scpi_result_t SCPI_3GPP_GetVsaEvmChipLimit(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_EVM_CHIPLIMIT);
}

scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPCCHPwrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDP_DPCCHPWROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPDCHPwrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDP_DPDCHPWROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotHSDPCCHPwrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDP_HSDPCCHPWROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPCCHPwr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDP_DPCCHPWR, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotDPDCHPwr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDP_DPDCHPWR, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDPVsSlotHSDPCCHPwr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDP_HSDPCCHPWR, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPCCHErrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHERROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPDCHErrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHERROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEHSDPCCHErrOut(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHERROUT, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPCCHErr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHERR, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPDCHErr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHERR, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEHSDPCCHErr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHERR, true);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPCCHsf(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHSF);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEDPDCHsf(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHSF);
}

scpi_result_t SCPI_3GPP_GetVsaRelativeCDEHSDPCCHsf(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHSF);
}

scpi_result_t SCPI_3GPP_GetVsaCDMIQSignalCodeNum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_CDM_CODENUM);
}

scpi_result_t SCPI_3GPP_GetVsaCDPISignalIMonitorval(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CDM_CDP_IMONITORVAL, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDPQSignalIMonitorval(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CDM_CDP_QMONITORVAL, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDEISignalIMonitorval(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CDM_CDE_IMONITORVAL, true);
}

scpi_result_t SCPI_3GPP_GetVsaCDEQSignalIMonitorval(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_CDM_CDE_QMONITORVAL, true);
}

scpi_result_t SCPI_3GPP_GetVsaUEPowerPwr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_UEPOWER_PWR, true);
}

scpi_result_t SCPI_3GPP_GetVsaIQOffset(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_IQOFFSET, true);
}

scpi_result_t SCPI_3GPP_GetVsaPhaseDisPhaseDiff(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_PHASEDIS_PHASEDIFF, true);
}

scpi_result_t SCPI_3GPP_GetVsaRstSummaryWCDMA(scpi_t *context)
{
#define UNVALID_DOUBLE_VAL            (-999.99)
    int iRet = WT_ERR_CODE_OK;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    double Power = UNVALID_DOUBLE_VAL;
    double RmsEvm = UNVALID_DOUBLE_VAL;
    double PeakEvm = UNVALID_DOUBLE_VAL;
    double MagnErrRms = UNVALID_DOUBLE_VAL;
    double MagnErrPeak = UNVALID_DOUBLE_VAL;
    double PhaseErrRms = UNVALID_DOUBLE_VAL;
    double PhaseErrPeak = UNVALID_DOUBLE_VAL;
    double IQOffset = UNVALID_DOUBLE_VAL;
    double IqImbaAmp = UNVALID_DOUBLE_VAL;
    double IqImbaPhs = UNVALID_DOUBLE_VAL;
    double FreqErr = UNVALID_DOUBLE_VAL;
    double PhaseDiff = UNVALID_DOUBLE_VAL;

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_UEPOWER_PWR, &Power, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {Power = UNVALID_DOUBLE_VAL;}

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_EVM_RMSEVM, &RmsEvm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){ IF_ERR_RETURN(iRet);} else {RmsEvm = UNVALID_DOUBLE_VAL;}

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_EVM_PEAKEVM, &PeakEvm, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {PeakEvm = UNVALID_DOUBLE_VAL; }

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRRMS, &MagnErrRms, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){ IF_ERR_RETURN(iRet);} else {MagnErrRms = UNVALID_DOUBLE_VAL;}

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_MPERR_MAGNERRPEAK, &MagnErrPeak, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){ IF_ERR_RETURN(iRet);} else {MagnErrPeak = UNVALID_DOUBLE_VAL;}

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRRMS, &PhaseErrRms, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){ IF_ERR_RETURN(iRet);} else {PhaseErrRms = UNVALID_DOUBLE_VAL;}

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_MPERR_PHASEERRPEAK, &PhaseErrPeak, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {PhaseErrPeak = UNVALID_DOUBLE_VAL; }

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_IQOFFSET, &IQOffset, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {IQOffset = UNVALID_DOUBLE_VAL; }

    iRet = WT_GetResult(attr->ConnID, WT_RES_IQ_MATCH_AMP_DB, &IqImbaAmp, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {IqImbaAmp = UNVALID_DOUBLE_VAL; }

    iRet = WT_GetResult(attr->ConnID, WT_RES_IQ_MATCH_PHASE, &IqImbaPhs, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {IqImbaPhs = UNVALID_DOUBLE_VAL; }

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_FREQERR_FREQERR, &FreqErr, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {FreqErr = UNVALID_DOUBLE_VAL; }

    iRet = WT_GetResult(attr->ConnID, WT_RES_3GPP_PHASEDIS_PHASEDIFF, &PhaseDiff, streamID, segmentID);
    if(iRet != WT_RESULT_UNVALID){IF_ERR_RETURN(iRet);} else {PhaseDiff = UNVALID_DOUBLE_VAL; }

    SCPI_ResultDouble(context, Power);
    SCPI_ResultDouble(context, RmsEvm);
    SCPI_ResultDouble(context, PeakEvm);
    SCPI_ResultDouble(context, MagnErrRms);
    SCPI_ResultDouble(context, MagnErrPeak);
    SCPI_ResultDouble(context, PhaseErrRms);
    SCPI_ResultDouble(context, PhaseErrPeak);
    SCPI_ResultDouble(context, IQOffset);
    SCPI_ResultDouble(context, IqImbaAmp);
    SCPI_ResultDouble(context, IqImbaPhs);
    SCPI_ResultDouble(context, FreqErr);
    SCPI_ResultDouble(context, PhaseDiff);

#undef UNVALID_DOUBLE_VAL
    return SCPI_RES_OK;
}
// inline function
static scpi_result_t GetRstDoubleData(scpi_t *context, const char *ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, ParamStr, &Result, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, Result);

    return SCPI_RES_OK;
}

static scpi_result_t GetRstIntData(scpi_t *context, const char *ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, ParamStr, &Result, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, (int)Result);

    return SCPI_RES_OK;
}

static scpi_result_t GetRstIntData(scpi_t *context, std::vector<const char *> &ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    std::vector<double> allResult;
    for (auto &item : ParamStr)
    {
        double Result = 0;
        int iRet = WT_GetResult(attr->ConnID, item, &Result, streamID, segmentID);
        IF_ERR_RETURN(iRet);
        allResult.push_back(Result);
    }

    for (auto &item : allResult)
    {
        SCPI_ResultInt(context, (int)item);
    }
    return SCPI_RES_OK;
}

static scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);
    if (!arb)
    {
        int DoubleCnt = ResultSize / sizeof(double);
        for (int i = 0; i < DoubleCnt; i++)
        {
            double *TmpData = (double *)ResultBuf.get();
            double Value = *(double *)(TmpData + i);
            SCPI_ResultDouble(context, Value);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }

    return SCPI_RES_OK;
}

static scpi_result_t GetRstIntVectorData(scpi_t *context, const char *ParamStr, bool arb)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);
    if (!arb)
    {
        int DoubleCnt = ResultSize / sizeof(int);
        for (int i = 0; i < DoubleCnt; i++)
        {
            int *TmpData = (int *)ResultBuf.get();
            int Value = *(int *)(TmpData + i);
            SCPI_ResultInt(context, Value);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }
    return SCPI_RES_OK;
}

static scpi_result_t GetRstCharVectorData(scpi_t *context, const char *ParamStr, bool arb)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);
    if (!arb)
    {
        int DoubleCnt = ResultSize / sizeof(char);
        for (int i = 0; i < DoubleCnt; i++)
        {
            char *TmpData = (char *)ResultBuf.get();
            char Value = *(char *)(TmpData + i);
            SCPI_ResultInt(context, Value);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }
    return SCPI_RES_OK;
}


// end inline function

// TB
static std::string print_tb_alz_json(AlzParamAxTriggerBase *param)
{
    if (param && 1 != param->TBFlag)
    {
        return "";
    }

    Json::Value root;
    Json::Value item;

    root["MUMIMO flag"] = param->TBMUMIMOFlag;
    root["Num of LTF symbols"] = param->NumLTF;
    root["Total users"] = param->UserNum;
    root["LDPC extra symbol"] = param->LDPCSym;
    // root["User ID"] = param->UserID;
    root["PE Disambiguity"] = param->PEDisamb;
    root["Pre-FEC padding factor"] = param->AFactor;
    root["GI+LTF Size"] = param->GILTFSize;
    root["STBC"] = param->STBC;
    root["Doppler"] = param->Doppler;
    root["Midamble Periodicity"] = param->Midamble_Periodicity;

    for (int i = 0; i < param->UserNum; i++)
    {
        item[i]["NSS"] = param->Stream[i];
        item[i]["MCS"] = param->MCS[i];
        item[i]["Segment"] = param->Segment[i];
        item[i]["RU Index"] = param->RUIndex[i];
        item[i]["Conding type"] = param->Conding[i];
        item[i]["DCM"] = param->DCM[i];
        item[i]["AID"] = param->AID[i];
        item[i]["NSS start"] = param->NSSStart[i];
    }
    root["User"] = item;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << root.toStyledString() << std::endl;

    return root.toStyledString();
}

//SLE
static std::string print_sle_alz_json(AlzParamSparkLink*param)
{
    if (param == nullptr)
    {
        return "";
    }

    Json::Value root;

    root[" FrmType "] = param->FrmType;
    root[" Bandwidth "] = param->Bandwidth;
    root[" CtrlInfoType "] = param->CtrlInfoType;
    root[" PayloadCrcType "] = param->PayloadCrcType;
    root[" PayloadCrcSeed "] = param->PayloadCrcSeed;
    root[" SlotIndex "] = param->SlotIndex;
    root[" PilotDens "] = param->PilotDens;
    root[" BoardIndex "] = param->BoardIndex;
    root[" PolarEncodePathNum "] = param->PolarEncodePathNum;
    root[" PID "] = param->PID;
    root[" Scramble "] = param->Scramble;
    root[" ChannelType "] = param->ChannelType;
    root[" FreqRange "] = param->FreqRange;
    root[" MSeqNo "] = param->MSeqNo;
    root[" SyncSource "] = param->SyncSource;
    root[" PayloadAnalyzeMode "] = param->PayloadAnalyzeMode;
    root[" MCS "] = param->MCS;
    root[" RaisedRootCosineFilter "] = param->RaisedRootCosineFilter;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << root.toStyledString() << std::endl;
    return root.toStyledString();
}

scpi_result_t SetVsaAxTbAlzParam(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    const char *Data = nullptr;
    size_t DataLen = 0;
    int Type = -1;

    if (!SCPI_ParamInt(context, &Type, true))
    {
        return SCPI_RES_ERR;
    }

    ILLEGAL_PARAM_RETURN(Type < -1 || Type > 1); // 判断范围

    if (Type != -1)
    {
        if (!SCPI_ParamArbitraryBlock(context, &Data, &DataLen, true))
        {
            return SCPI_RES_ERR;
        }
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set VSA TB analyze type =" << Type << ",ARB data len = " << DataLen << endl;

    // 特殊处理，SCPI Type为1时，转成api的2，见AX_TB_ANALYZE_ENUM枚举的AX_TB_REF_FILE
    const int tbAnalyzeType[] = {AX_TB_ANALYZE_EXIT, AX_TB_BASE_TYPE, AX_TB_REF_FILE};
    Type = tbAnalyzeType[Type + 1];

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set VSA TB Demod = " << attr->vsaParam.Demode << std::endl;

    iRet = WT_SetExternAnalyzeParam(attr->ConnID, attr->vsaParam.Demode, Type, (void *)Data, DataLen);

    if (AX_TB_BASE_TYPE == Type && DataLen == sizeof(AlzParamAxTriggerBase))
    {
        print_tb_alz_json((AlzParamAxTriggerBase *)Data);
    }

    return SCPI_ResultOK(context, iRet);
}

static int GetVsaTbIntParam(scpi_t *context, int *Value, int maxNum, int minNum)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (*Value < minNum || *Value > maxNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t SetVsaAxTbAlzParamFlag(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    do
    {
        iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.TBFlag, 1, 0);
        IF_BREAK(iRet);
        iRet = WT_SetExternAnalyzeParam(attr->ConnID, attr->vsaParam.Demode, AX_TB_BASE_TYPE, (void *)&attr->tbAnanlyzeParam, sizeof(AlzParamAxTriggerBase));

        if (1 == attr->tbAnanlyzeParam.TBFlag)
        {
            print_tb_alz_json(&attr->tbAnanlyzeParam);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamMUFlag(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.TBMUMIMOFlag, 1, 0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserID(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.UserID, 144, 1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamGILTFSize(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.GILTFSize, 2, 0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamNumLTF(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int NumLTF = 0;
    const int NumLtfSymbols[][2] = {{0, 1}, {1, 2}, {2, 4}, {3, 6}, {4, 8}};
    int iRet = GetVsaTbIntParam(context, &NumLTF, 4, 0);
    attr->tbAnanlyzeParam.NumLTF = NumLtfSymbols[NumLTF][1];
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamLDPCExtra(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.LDPCSym, 1, 0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamPE(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.PEDisamb, 1, 0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamAFactor(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.AFactor, 4, 1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamStbc(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.STBC, 1, 0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamDoppler(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.Doppler, 1, 0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamMidPeriodicity(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.Midamble_Periodicity, 2, 1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserNum(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = GetVsaTbIntParam(context, &attr->tbAnanlyzeParam.UserNum, 144, 1);
    return SCPI_ResultOK(context, iRet);
}

static int SetVsaAxTbAlzUserParam(scpi_t *context,
                                  int *target,
                                  int maxValue,
                                  int minValue,
                                  int offset = 0)
{
    int value = 0;
    int UserID = 0;
    int32_t numbers[1] = {0};
    int iRet = WT_ERR_CODE_OK;
    SCPI_CommandNumbers(context, numbers, 1);
    UserID = numbers[0];
    do
    {
        if (!SCPI_ParamInt(context, &value, true))
        {
            iRet = SCPI_RES_ERR;
        }

        if (value < minValue || value > maxValue)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (0 == UserID)
        {
            for (int i = 0; i < BE_RU_COUNT; i++)
            {
                target[i] = value + offset;
            }
        }
        else
        {
            target[UserID - 1] = value + offset;
        }
    } while (0);
    return iRet;
}

scpi_result_t SetVsaAxTbAlzParamUserNSS(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.Stream[0],
        8,
        1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserMCS(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.MCS[0],
        15,
        0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserSegment(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.Segment[0],
        4,
        1,
        -1);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserRUIndex(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.RUIndex[0],
        127,
        0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserCodingType(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.Conding[0],
        1,
        0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserDCM(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.DCM[0],
        1,
        0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserAID(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.AID[0],
        2007,
        1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAxTbAlzParamUserNNstart(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = SetVsaAxTbAlzUserParam(
        context,
        &attr->tbAnanlyzeParam.NSSStart[0],
        8,
        1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaRstMuRuCnt(scpi_t *context)
{
    if (WT_ERR_CODE_OK == is11BE(context))
    {
        return GetVsaRst_11Be_MUMIMO_RUCntInfo(context);
    }
    int iRet = WT_ERR_CODE_OK;
    double Demo = -1;
    // mu-mimo的总ru数，和每个ru有无效性
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
        if (iRet)
        {
            break;
        }

        if (!(WT_DEMOD_11AX_20M <= (int)Demo && (int)Demo <= WT_DEMOD_11AX_80_80M))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        DataInfo11ax DataInfo;
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
        if (iRet)
        {
            break;
        }

        if (DataInfo.UserNum < 1)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        int Size = sizeof(RuOfdmaInfo11ax) * DataInfo.UserNum;
        unique_ptr<RuOfdmaInfo11ax[]> ResultBuf = nullptr;
        ResultBuf.reset(new (std::nothrow) RuOfdmaInfo11ax[DataInfo.UserNum]);

        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_MU_MIMO_OFDMA_INFO, ResultBuf.get(), Size);
        if (iRet)
        {
            break;
        }

        RuOfdmaInfo11ax *OfdmaInfo = &(ResultBuf.get()[0]);

        SCPI_ResultInt(context, DataInfo.UserNum);
        for (int i = 0; i < DataInfo.UserNum; i++, OfdmaInfo++)
        {
            SCPI_ResultInt(context, OfdmaInfo->ValidFlag);
        }

    } while (0);

    IF_ERR_RETURN(iRet);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstMuRuInfo(scpi_t *context)
{
    if (WT_ERR_CODE_OK == is11BE(context))
    {
        return GetVsaRst_11Be_MUMIMO_RUInfo(context);
    }

    // mu-mimo ru 综合信息
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    int RuID = 1;
    SCPI_ParamInt(context, &RuID, false);

    double Demo = -1;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
    IF_ERR_RETURN(iRet);

    ILLEGAL_PARAM_RETURN(!(WT_DEMOD_11AX_20M <= (int)Demo && (int)Demo <= WT_DEMOD_11AX_80_80M));

    DataInfo11ax DataInfo;
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    ILLEGAL_PARAM_RETURN(DataInfo.MuMimoFlag == 0 || RuID < 1 || DataInfo.UserNum < RuID);

    int Size = sizeof(RuOfdmaInfo11ax) * DataInfo.UserNum;
    unique_ptr<RuOfdmaInfo11ax[]> ResultBuf = nullptr;
    ResultBuf.reset(new (std::nothrow) RuOfdmaInfo11ax[DataInfo.UserNum]);

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_MU_MIMO_OFDMA_INFO, ResultBuf.get(), Size);
    IF_ERR_RETURN(iRet);

    RuOfdmaInfo11ax *OfdmaInfo = &(ResultBuf.get()[RuID - 1]);

    SCPI_ResultInt(context, OfdmaInfo->ValidFlag);
    SCPI_ResultInt(context, OfdmaInfo->Ue_Num);
    SCPI_ResultInt(context, OfdmaInfo->RuNsts);
    for (int i = 0; i < OfdmaInfo->RuNsts; i++)
    {
        for (int j = 0; j < OfdmaInfo->RuNsts; j++)
        {
            SCPI_ResultDouble(context, OfdmaInfo->q_matrix_r[i][j]);
            SCPI_ResultDouble(context, OfdmaInfo->q_matrix_i[i][j]);
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstMuRuUserInfo_V2(scpi_t *context)
{
    if (WT_ERR_CODE_OK == is11BE(context))
    {
        return GetVsaRst_11Be_MUMIMO_RU_UserInfo_V2(context);
    }

    // mu-mimo ru 综合信息
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int32_t numbers[3] = {0, 0, 0};
    int RuID = 1;
    int UserID = 1;
    int StreamID = 0;

    SCPI_CommandNumbers(context, numbers, 3);
    RuID = numbers[0];
    UserID = numbers[1];
    StreamID = numbers[2];

    double Demo = -1;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
    IF_ERR_RETURN(iRet);

    ILLEGAL_PARAM_RETURN(!(WT_DEMOD_11AX_20M <= (int)Demo && (int)Demo <= WT_DEMOD_11AX_80_80M));

    DataInfo11ax DataInfo;
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    ILLEGAL_PARAM_RETURN(DataInfo.MuMimoFlag == 0 || RuID < 1 || DataInfo.UserNum < RuID);

    int Size = sizeof(RuOfdmaInfo11ax) * DataInfo.UserNum;
    unique_ptr<RuOfdmaInfo11ax[]> ResultBuf = nullptr;
    ResultBuf.reset(new (std::nothrow) RuOfdmaInfo11ax[DataInfo.UserNum]);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_MU_MIMO_OFDMA_INFO, ResultBuf.get(), Size);
    IF_ERR_RETURN(iRet);

    RuOfdmaInfo11ax *OfdmaInfo = &(ResultBuf.get()[RuID - 1]);

    ILLEGAL_PARAM_RETURN(UserID < 1 || UserID > OfdmaInfo->Ue_Num);
    UserID -= 1;

    SCPI_ResultInt(context, OfdmaInfo->ValidFlag);
    SCPI_ResultInt(context, OfdmaInfo->Ue_Num);

    SCPI_ResultInt(context, OfdmaInfo->User[UserID].ULDL);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].UserId);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].ToneWide);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].ToneIdx);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Isegment);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Mcs);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Modulation);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].LDPC);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].CodingRate);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].STBC);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].DCM);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Nsts);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].PsduCrc);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].PsduLen);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].UserAID);
    SCPI_ResultDouble(context, OfdmaInfo->User[UserID].PowerFactor);
    SCPI_ResultDouble(context, OfdmaInfo->User[UserID].UserRate);
    if (StreamID == 0 || OfdmaInfo->User[UserID].Nsts < 2) // SISO或者MIMO指定stream ID = 0时
    {
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].UserPower);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].PilotEvm);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].DataEvm);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].AllEvm);
    }
    else
    {
        int index = StreamID - 1;
        int tmpID = 0;
        if (GetNSSStartID_11axMUMIMO(OfdmaInfo->User[UserID], tmpID))
        {
            index += tmpID;
        }
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].UserPowerNsts[index]);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].PilotEvmNsts[index]);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].DateEvmNsts[index]);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].AllEvmNsts[index]);
    }
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Beamformed);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRstMuRuUserInfo(scpi_t *context)
{
    if (WT_ERR_CODE_OK == is11BE(context))
    {
        return GetVsaRst_11Be_MUMIMO_RU_UserInfo(context);
    }

    // mu-mimo ru 综合信息
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    int RuID = 1;
    int UserID = 1;
    int StreamID = 0;
    SCPI_ParamInt(context, &RuID, false);
    SCPI_ParamInt(context, &UserID, false);
    SCPI_ParamInt(context, &StreamID, false);

    double Demo = -1;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
    IF_ERR_RETURN(iRet);

    ILLEGAL_PARAM_RETURN(!(WT_DEMOD_11AX_20M <= (int)Demo && (int)Demo <= WT_DEMOD_11AX_80_80M));

    DataInfo11ax DataInfo;
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    ILLEGAL_PARAM_RETURN(DataInfo.MuMimoFlag == 0 || RuID < 1 || DataInfo.UserNum < RuID);

    int Size = sizeof(RuOfdmaInfo11ax) * DataInfo.UserNum;
    unique_ptr<RuOfdmaInfo11ax[]> ResultBuf = nullptr;
    ResultBuf.reset(new (std::nothrow) RuOfdmaInfo11ax[DataInfo.UserNum]);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_MU_MIMO_OFDMA_INFO, ResultBuf.get(), Size);
    IF_ERR_RETURN(iRet);

    RuOfdmaInfo11ax *OfdmaInfo = &(ResultBuf.get()[RuID - 1]);

    ILLEGAL_PARAM_RETURN(UserID < 1 || UserID > OfdmaInfo->Ue_Num);
    UserID -= 1;

    SCPI_ResultInt(context, OfdmaInfo->ValidFlag);
    SCPI_ResultInt(context, OfdmaInfo->Ue_Num);

    SCPI_ResultInt(context, OfdmaInfo->User[UserID].ULDL);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].UserId);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].ToneWide);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].ToneIdx);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Isegment);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Mcs);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Modulation);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].LDPC);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].CodingRate);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].STBC);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].DCM);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Nsts);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].PsduCrc);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].PsduLen);
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].UserAID);
    SCPI_ResultDouble(context, OfdmaInfo->User[UserID].PowerFactor);
    SCPI_ResultDouble(context, OfdmaInfo->User[UserID].UserRate);
    if (StreamID == 0 || OfdmaInfo->User[UserID].Nsts < 2) // SISO或者MIMO指定stream ID = 0时
    {
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].UserPower);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].PilotEvm);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].DataEvm);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].AllEvm);
    }
    else
    {
        int index = StreamID - 1;
        int tmpID = 0;
        if (GetNSSStartID_11axMUMIMO(OfdmaInfo->User[UserID], tmpID))
        {
            index += tmpID;
        }
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].UserPowerNsts[index]);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].PilotEvmNsts[index]);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].DateEvmNsts[index]);
        SCPI_ResultDouble(context, OfdmaInfo->User[UserID].AllEvmNsts[index]);
    }
    SCPI_ResultInt(context, OfdmaInfo->User[UserID].Beamformed);
    return SCPI_RES_OK;
}

static int SaveVsaWaveform(int ID, string &FileName, int DataType, int fileType, int startUs, int EndUs)
{
    struct tm *Mtm;
    time_t Now;
    time(&Now);
    Mtm = localtime(&Now);
    char NameBuf[256] = {0};
    sprintf(NameBuf, "TmpSavedata_%d-%d-%d_%d-%d-%d.%s",
            1900 + Mtm->tm_year,
            Mtm->tm_mon + 1,
            Mtm->tm_mday,
            Mtm->tm_hour,
            Mtm->tm_min,
            Mtm->tm_sec,
            (0 == fileType ? "bwv" : "csv"));

    FileName = SCPI_WaveDir() + string(NameBuf);
    return WT_SaveSignal_V2(ID, DataType, FileName.c_str(), startUs, EndUs);
}

static scpi_result_t RespFileARB(scpi_t *context, string &FileName)
{
    std::unique_ptr<char[]> FileData = nullptr;
    int FileLen = 0;
    int iRet = ReadFile(FileName, FileData, FileLen);
    IF_ERR_RETURN(iRet);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileName = " << FileName << ", len = " << FileLen << std::endl;

    if (FileLen > 0)
    {
        SCPI_ResultArbitraryBlock(context, (char *)(FileData.get()), FileLen);
    }
    // 最后删除临时保存的文件
    if (0 == access(FileName.c_str(), R_OK))
    {
        // remove bwv/csv file
        string cmd = string("rm -rf ") + "'" + FileName + "'";
        do_system_cmd(cmd);
    }
    return SCPI_RES_OK;
}

static scpi_result_t GetVsaIQWaveform(scpi_t *context, int DataType)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double StartTime = 0.0;
    double EndTime = 0.0;
    int fileType = 0;
    // 下发的时间单位为s，调用api使用的是us
    SCPI_ParamDouble(context, &StartTime, false);
    SCPI_ParamDouble(context, &EndTime, false);
    SCPI_ParamInt(context, &fileType, false);

    int startUS = StartTime * 1e6;
    int endUS = EndTime * 1e6;
    string FileName;
    int iRet = SaveVsaWaveform(attr->ConnID, FileName, DataType, fileType, startUS, endUS);
    IF_ERR_RETURN(iRet);

    return RespFileARB(context, FileName);
}

scpi_result_t GetVsaIQDataBwv(scpi_t *context)
{
    return GetVsaIQWaveform(context, WT_SAVE_COMPENSATED_DATA);
}

scpi_result_t GetVsaRawIQDataBwv(scpi_t *context)
{
    return GetVsaIQWaveform(context, WT_SAVE_RAW_DATA);
}

scpi_result_t CheckWaveFileExist(scpi_t *context)
{
    char WaveNameBuf[256] = {0};
    size_t NameLen = 0;

    if (!SCPI_ParamCopyText(context, WaveNameBuf, sizeof(WaveNameBuf) - 1, &NameLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "check WaveName=" << WaveNameBuf << endl;
    int iRet = WT_ERR_CODE_OK;
    int Exist = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_QueryTesterWaveFileOrRefFile(attr->ConnID, WaveNameBuf, &Exist);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "iRet = " << iRet << ",File exist = " << Exist << endl;
    if (Exist == 0) // 文件不存在
    {
        iRet = WT_ERR_CODE_TESTER_NO_WAVE;
    }
    SCPI_ResultInt(context, iRet);
    return SCPI_RES_OK;
}

scpi_result_t DelWaveFile(scpi_t *context)
{
    char WaveNameBuf[256] = {0};
    size_t NameLen = 0;

    if (!SCPI_ParamCopyText(context, WaveNameBuf, sizeof(WaveNameBuf) - 1, &NameLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "delete WaveName=" << WaveNameBuf << endl;
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_DeleteTesterWaveFileOrRefFile(attr->ConnID, WaveNameBuf);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "iRet = " << iRet << endl;
    remove_low_file(WaveNameBuf, attr->ClientFd, attr->ConnID);  // 在删除信号文件的时候将用户对应的low文件删除
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaSaveCMIMORefFile(scpi_t *context)
{
    char fileName[256] = {0};
    size_t NameLen = 0;
    const char *data = nullptr;
    size_t len = 0;
    string extName(".wtref");
    size_t pos = -1;
    int iRet = WT_ERR_CODE_GENERAL_ERROR;

    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &NameLen, true))
    {
        return SCPI_RES_ERR;
    }

    pos = string(fileName).find_last_of(extName);
    if (string::npos == pos)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Invalid CMIMO reference file name: " << fileName << endl;
        return SCPI_RES_ERR;
    }

    if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
    {
        return SCPI_RES_ERR;
    }

    if (len > 0)
    {
        string cmd;
        string SaveName(fileName);
        string firstName(fileName);
        const string dirName(SCPI_WaveDir());

        pos = SaveName.find_last_of(extName);
        if (string::npos != pos)
        {
            firstName = SaveName.substr(0, pos);
        }
        pos = firstName.find_last_of("/");
        if (string::npos != pos)
        {
            cmd = string("mkdir -p ") + dirName + firstName.substr(0, pos);
            do_system_cmd(cmd);
        }
        // SaveName = dirName + string(fileName);
        SaveName = dirName + "ExtRef" + extName;

        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Save CMIMO reference file name = " << SaveName << endl;
        iRet = WriteFile(SaveName, data, len);

        if (iRet == WT_ERR_CODE_OK)
        {
            string DestName = string(fileName);
            // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Save CMIMO DestName name = " << DestName << endl;
            // 必须经过api调用下，下位机用到的参考文件格式和上面下发时的是不一致的，api转了下
            SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
            iRet = WT_AddTesterWaveFileOrRefFile(attr->ConnID, SaveName.c_str(), DestName.c_str(), 0);
        }
    }
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAnalyzeCMIMORefFile(scpi_t *context)
{
    char fileName[256] = {0};
    size_t NameLen = 0;
    int flag = 0;

    if (!SCPI_ParamInt(context, &flag, true))
    {
        return SCPI_RES_ERR;
    }
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    if (0 == flag)
    {
        memset(attr->m_CMIMORefFile, 0, sizeof(attr->m_CMIMORefFile)); // 退出CMIMO分析
    }
    else
    {
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &NameLen, true))
        {
            return SCPI_RES_ERR;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set CMIMO reference file name = " << fileName << endl;
        strcpy(attr->m_CMIMORefFile, fileName);
    }
    return SCPI_ResultOK(context);
}

scpi_result_t GetVsaAnalyzeCMIMORefFile(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    if (attr->m_CMIMORefFile[0] != '\0')
    {
        SCPI_ResultInt(context, 1);
        SCPI_ResultText(context, attr->m_CMIMORefFile);
    }
    else
    {
        SCPI_ResultInt(context, 0);
    }
    return SCPI_RES_OK;
}
scpi_result_t GetVsaRstFrameLocation(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_FRAME_LOCATION, false);
}

scpi_result_t GetVsaRstPointPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_POINTS_POWER, true);
}

scpi_result_t GetVsaRstPointAvgPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WIN_AVG_POWER, true);
}

scpi_result_t GetVsaRstPointIQ(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_IQ, true);
}

scpi_result_t GetVsaRstSpectrumMaskDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_MASK, true);
}

scpi_result_t GetVsaRstSymoblConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CONST_DATA, true);
}
scpi_result_t GetVsaRstSymoblPilotConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CONST_PILOT, true);
}
scpi_result_t GetVsaRstSymoblRefConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CONST_REF, true);
}
scpi_result_t GetVsaRstCarrierSymoblEvmARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_CARRIER_DB, true);
}
scpi_result_t GetVsaRstCarrierSymoblEvmARB_AVG(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_CARRIER_AVG_DB, true);
}
scpi_result_t GetVsaRstPilotCarrierSymoblEvmARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_CARRIER_PILOT_DB, true);
}
scpi_result_t GetVsaRstPilotCarrierSymoblEvmARB_AVG(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_CARRIER_PILOT_AVG_DB, true);
}
scpi_result_t GetVsaRstSymoblEVM(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_SYM_DB, true);
}
scpi_result_t GetVsaRstSymoblEVM_AVG(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_SYM_AVG_DB, true);
}
scpi_result_t GetVsaRstSymoblEVMPilot(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_SYM_PILOT_DB, true);
}
scpi_result_t GetVsaRstFlatnessData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_DATA, true);
}
scpi_result_t GetVsaRstFlatnessMaskUp(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA, true);
}
scpi_result_t GetVsaRstFlatnessMaskDown(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA, true);
}
scpi_result_t GetVsaRstFlatnessSection(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE, true);
}
scpi_result_t GetVsaRstFlatnessSectionMargin(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN, true);
}

scpi_result_t GetAlzAdcSMPFreq(scpi_t *context)
{
    // return GetRstDoubleVectorData(context, WT_RES_SMP_FREQ);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double result = 0.0;
    int iRet = WT_GetResult(attr->ConnID, WT_RES_SMP_FREQ, &result);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, result);
    return SCPI_RES_OK;
}

/**
 * @brief find the nearest position from max index or min index to pos[offset]
 *
 * @param pos : original position
 * @param max_index : max value position
 * @param min_index : min value position
 * @param offset : start position to compare
 * @return int : awalys zero
 */
static int find_nearest_index(vector<int> &pos, int max_index, int min_index, int offset = 0)
{
    int tmp_max = abs(pos[offset] - max_index);
    int tmp_min = abs(pos[offset] - min_index);

    int index_max = offset;
    int index_min = offset;
    for (int i = offset + 1; i < pos.size(); i++)
    {
        if (tmp_max > abs(pos[i] - max_index))
        {
            tmp_max = abs(pos[i] - max_index);
            index_max = i;
            continue;
        }

        if (tmp_min > abs(pos[i] - min_index))
        {
            tmp_min = abs(pos[i] - min_index);
            index_min = i;
        }
    }
    pos[index_min] = min_index;
    pos[index_max] = max_index;
    return 0;
}
/**
 * @brief Set the max min point object
 *
 * @param v: double vector to find
 * @param pos : all positon result
 */
static void set_max_min_point(vector<double> &v, vector<int> &pos)
{
    auto result_max = std::max_element(v.begin(), v.end());
    auto result_min = std::min_element(v.begin(), v.end());

    auto index_1 = std::distance(v.begin(), result_max);
    auto index_2 = std::distance(v.begin(), result_min);

    find_nearest_index(pos, index_1, index_2);
}

static int get_time_window_size(int Points)
{
    int time_window_size = 8;//8us
    int water_points = 480000;//480MHz@1ms
    if (Points <= water_points)
    {
        time_window_size = 2;
    }
    else if (Points > water_points && Points < 4*water_points)
    {
        time_window_size = 4;
    }
    return time_window_size;
}

scpi_result_t RstDoubleVectorLite(
    scpi_t *context,
    int smp,
    double *data,
    int Points,
    int streamID,
    int segmentID,
    int litetype)
{
    int time_window_size = get_time_window_size(Points); //8us
    int PointsPerMicroSecend = ceil((double)smp/MHz_API);
    int PointsPerMicroSecend_Low = floor((double)smp/MHz_API);
    const int PointMin = 4; //resample point count per time window

    int PointsPerWindow = time_window_size * PointsPerMicroSecend;
    if (PointsPerMicroSecend - PointsPerMicroSecend_Low > 0)
    {
        double nearest_points = ((double)smp/MHz_API) * time_window_size;
        PointsPerWindow = (int)nearest_points;
    }

    int TotalWindows = Points / PointsPerWindow;
    int Remain = Points % PointsPerWindow;
    int LoopPoint = (0 == Remain ? Points : (TotalWindows * PointsPerWindow));
    double ResampleRate = 0.0;

    double *pLite = nullptr;
    std::unique_ptr<u8[]> Lite = nullptr;
    u32 mem_alloc_bytes = 0;
    int total_index = 0;
    do
    {
        ResampleRate = PointMin;
        ResampleRate /= time_window_size;

        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Original Points = " << Points
        //        << ", LoopPoint = " << LoopPoint
        //        << ", PointMin = " << PointMin
        //        << ", TotalWindows = " << TotalWindows
        //        << ", Windows size = " << time_window_size << " us"
        //        << ", Resample rate = " << ResampleRate
        //        << ", PointsPerWindow = " << PointsPerWindow
        //        << ", Remain = " << Remain
        //        << std::endl;

        mem_alloc_bytes = sizeof(double) + (PointMin * TotalWindows * sizeof(double));
        Lite.reset(new u8[mem_alloc_bytes]);

        //first double store resample rate
        double *pDouble = (double*)Lite.get();
        *pDouble = ResampleRate*MHz_API;
        pDouble++;
        //after resample rate followed double value
        pLite = static_cast<double *>(pDouble);

        std::vector<int> randIndex(PointMin);
        std::vector<int> resample_index(PointMin);

        {
            int step = PointsPerWindow / (PointMin - 1);
            for (int i = 0, j = 0; i < PointsPerWindow; i += step, j++)
            {
                resample_index[j] = (0 == i ? 0 : (i - 1));
            }
            resample_index[PointMin - 1] = (PointsPerWindow - 1);
        }

        std::vector<double> tmpData(PointsPerWindow);
        for (int i = 0; i < LoopPoint; i += PointsPerWindow)
        {
            memcpy(&tmpData[0], &data[i], PointsPerWindow * sizeof(double));
            //restore index value
            memcpy(&randIndex[0], &resample_index[0], resample_index.size() * sizeof(int));
            //find max and min value position
            set_max_min_point(tmpData, randIndex);

            for (int j = 0; j < PointMin; j++, total_index++)
            {
                pLite[total_index] = data[i + randIndex[j]];
            }
        }

    } while (0);

    if (Lite != nullptr)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Lite.get(), mem_alloc_bytes);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        LitePointResult *pLiteResult = &attr->m_litePoint[litetype][streamID][segmentID];
        pLiteResult->valid = true;
        pLiteResult->value_len = mem_alloc_bytes;
        pLiteResult->value = std::move(Lite);
    }

    return SCPI_RES_OK;
}

scpi_result_t RstComplexVectorLite(
    scpi_t *context,
    int smp,
    Complex *data,
    int Points,
    int streamID,
    int segmentID,
    int litetype)
{
    int time_window_size = get_time_window_size(Points); //8us
    int PointsPerMicroSecend = ceil((double)smp/MHz_API);
    int PointsPerMicroSecend_Low = floor((double)smp/MHz_API);

    const int PointMin = 4; //resample point count per time window
    int PointsPerWindow = time_window_size * PointsPerMicroSecend;
    if (PointsPerMicroSecend - PointsPerMicroSecend_Low > 0)
    {
        double nearest_points = ((double)smp/MHz_API) * time_window_size;
        PointsPerWindow = (int)nearest_points;
    }
    int TotalWindows = Points / PointsPerWindow;
    int Remain = Points % PointsPerWindow;
    int LoopPoint = (0 == Remain ? Points : (TotalWindows * PointsPerWindow));
    double ResampleRate = 0.0;

    Complex *pLite = nullptr;
    std::unique_ptr<u8[]> Lite = nullptr;
    u32 mem_alloc_bytes = 0;
    int total_index = 0;
    do
    {
        ResampleRate = PointMin;
        ResampleRate /= time_window_size;

        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Original Points = " << Points
        //        << ", LoopPoint = " << LoopPoint
        //        << ", PointMin = " << PointMin
        //        << ", TotalWindows = " << TotalWindows
        //        << ", Windows size = " << time_window_size << " us"
        //        << ", Resample rate = " << ResampleRate
        //        << std::endl;

        mem_alloc_bytes = sizeof(double) + (PointMin * TotalWindows * 2 * sizeof(double));
        Lite.reset(new u8[mem_alloc_bytes]);

        // first double store resample rate
        double *pDouble = (double *)Lite.get();
        *pDouble = ResampleRate * MHz_API;
        pDouble++;
        // after resample rate followed complex value
        pLite = (Complex *)pDouble;

        std::vector<int> randIndex_I(PointMin);
        std::vector<int> randIndex_Q(PointMin);
        std::vector<int> resample_index(PointMin);
        {
            int step = PointsPerWindow / (PointMin - 1);
            for (int i = 0, j = 0; i < PointsPerWindow; i += step, j++)
            {
                resample_index[j] = (0 == i ? 0 : (i - 1));
            }
            resample_index[PointMin - 1] = (PointsPerWindow - 1);
        }
        std::vector<double> tmpData_I(PointsPerWindow);
        std::vector<double> tmpData_Q(PointsPerWindow);

        for (int i = 0; i < LoopPoint; i += PointsPerWindow)
        {
            for (int j = 0; j < PointsPerWindow; j++)
            {
                tmpData_I[j] = data[i + j][0];
                tmpData_Q[j] = data[i + j][1];
            }

            memcpy(&randIndex_I[0], &resample_index[0], resample_index.size() * sizeof(int));
            memcpy(&randIndex_Q[0], &resample_index[0], resample_index.size() * sizeof(int));

            set_max_min_point(tmpData_I, randIndex_I);
            set_max_min_point(tmpData_Q, randIndex_Q);

            for (int j = 0; j < PointMin; j++, total_index++)
            {
                pLite[total_index][0] = data[i + randIndex_I[j]][0];
                pLite[total_index][1] = data[i + randIndex_Q[j]][1];
            }
        }
    } while (0);

    if (Lite != nullptr)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Lite.get(), mem_alloc_bytes);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        LitePointResult *pLiteResult = &attr->m_litePoint[litetype][streamID][segmentID];
        pLiteResult->valid = true;
        pLiteResult->value_len = mem_alloc_bytes;
        pLiteResult->value = std::move(Lite);
    }

    return SCPI_RES_OK;
}

int ResponseReadyLiteResult(scpi_t *context, int litetype, int streamID, int segmentID)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    LitePointResult *pLiteResult = &attr->m_litePoint[litetype][streamID][segmentID];
    do
    {
        if (!pLiteResult->valid)
        {
            return -1;
        }

        if (pLiteResult->value_len > 0 && pLiteResult->value != nullptr)
        {
            SCPI_ResultArbitraryBlock(context, (char *)pLiteResult->value.get(), pLiteResult->value_len);
            break;
        }

    } while (0);

    return 0;
}

static scpi_result_t GetRstDoubleVectorDataLite(
    scpi_t *context,
    const char *ParamStr,
    int litetype)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    if (0 == ResponseReadyLiteResult(context, litetype, streamID, segmentID))
    {
        return SCPI_RES_OK;
    }

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int smp = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }
    IF_ERR_RETURN(iRet);

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_SMP_FREQ, &smp, sizeof(smp));
    IF_ERR_RETURN(iRet);

    if (ElementSize > sizeof(double))
    {
        return RstComplexVectorLite(
            context,
            smp,
            (Complex *)ResultBuf.get(),
            ElementCount,
            streamID,
            segmentID,
            litetype);
    }
    return RstDoubleVectorLite(
        context,
        smp,
        (double *)ResultBuf.get(),
        ElementCount,
        streamID,
        segmentID,
        litetype);
}

scpi_result_t GetVsaRstPointPowerLite(scpi_t *context)
{
    return GetRstDoubleVectorDataLite(context, WT_RES_POINTS_POWER, LITE_ENUM::LITE_ENUM_Power);
}

scpi_result_t GetVsaRstPointAvgPowerLite(scpi_t *context)
{
    return GetRstDoubleVectorDataLite(context, WT_RES_WIN_AVG_POWER, LITE_ENUM::LITE_ENUM_AvgPower);
}

scpi_result_t GetVsaRstPointIQLite(scpi_t *context)
{
    return GetRstDoubleVectorDataLite(context, WT_RES_IQ, LITE_ENUM::LITE_ENUM_IQ);
}

scpi_result_t GetVsaRstSpectrumSpan(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_SPECTRUM_FREQ_SPAN);
}

scpi_result_t GetVsaRstSpectrumCenterFreq(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_SPECTRUM_FREQ_CENTER);
}

scpi_result_t GetVsaRstSpectrumRBW(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_SPECTRUM_RBW);
}

scpi_result_t GetVsaRstSpectrumOBW(scpi_t *context)
{
    std::vector<const char *> paramStr;
    int Number[1] = {0};
    int iRet = WT_ERR_CODE_OK;
    if (!SCPI_CommandNumbers(context, Number, 1))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    if (Number[0] <= 1)
    {
        paramStr.push_back(WT_RES_SPECTRUM_OBW);
        paramStr.push_back(WT_RES_SPECTRUM_OBW_START_FREQ);
        paramStr.push_back(WT_RES_SPECTRUM_OBW_END_FREQ);
    }
    else
    {
        paramStr.push_back(WT_RES_SPECTRUM_OBW1);
        paramStr.push_back(WT_RES_SPECTRUM_OBW_START_FREQ1);
        paramStr.push_back(WT_RES_SPECTRUM_OBW_END_FREQ1);
    }
    return GetRstIntData(context, paramStr);
}

scpi_result_t GetVsaRstImbalanceCal(scpi_t *context)
{
    int Number[1] = {0};
    int segmentID = 0;
    int iRet = WT_ERR_CODE_OK;
    if (!SCPI_CommandNumbers(context, Number, 1))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    double imbAmp = 0.0;
    double imbPhase = 0.0;
    double timeSkew = 0.0;
    segmentID = (Number[0] <= 1 ? 1 : 2);

    iRet = WT_CalculateImbalance(attr->ConnID, &imbAmp, &imbPhase, &timeSkew, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, imbAmp);
    SCPI_ResultDouble(context, imbPhase);
    SCPI_ResultDouble(context, timeSkew);
    return SCPI_RES_OK;
}

scpi_result_t SetVsaImbalanceCal(scpi_t *context)
{
    int Number[1] = {0};
    int segmentID = 0;
    int enable = 0;
    double imbAmp = 0.0;
    double imbPhase = 0.0;
    double timeSkew = 0.0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &enable, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamDouble(context, &imbAmp, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamDouble(context, &imbPhase, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamDouble(context, &timeSkew, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

    } while (0);

    IF_ERR_RETURN(iRet);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    segmentID = (Number[0] <= 1 ? 1 : 2);

    iRet = WT_SetFixedImbalance(attr->ConnID, enable, imbAmp, imbPhase, timeSkew, segmentID);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaRstCCDFProb(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CCDF_PROB, true);
}

scpi_result_t GetVsaRstCCDFPowerRef(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CCDF_POWER_REL_DB, true);
}

scpi_result_t GetVsaRstCCDFStart(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_CCDF_START);
}

scpi_result_t GetVsaRstCCDFScale(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_CCDF_SCALE);
}

scpi_result_t GetVsaRstCCDFPercentPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CCDF_PERCENT_POWER, true);
}

scpi_result_t GetVsaRst11b_FreqerrMargin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_11B_FREQ_ERR_MARGIN);
}

scpi_result_t GetVsaRst11b_ClockErrMargin(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_11B_CLOCK_ERR_MARGIN);
}

scpi_result_t GetVsaRst11b_EVMTime(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_TIME, true);
}

scpi_result_t GetVsaRst11b_EVMTimeAVG(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_TIME_AVG, true);
}

scpi_result_t GetVsaRst11b_EVMTimeChip(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_EVM_TIME_CHIP, true);
}

scpi_result_t GetVsaRst11b_EyeGraphic(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11B_EYE, true);
}

scpi_result_t GetVsaRst11b_RampOnPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_RAMP_ON_POWER_INST, true);
}

scpi_result_t GetVsaRst11b_RampOnPowerPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_RAMP_ON_POWER_PEAK, true);
}

scpi_result_t GetVsaRst11b_RampOnMask(scpi_t *context)
{
    int Number[1] = {0};
    int iRet = WT_ERR_CODE_OK;
    if (!SCPI_CommandNumbers(context, Number, 1))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    if (Number[0] <= 1)
    {
        return GetRstDoubleVectorData(context, WT_RES_RAMP_ON_POWER_MARK1, true);
    }
    return GetRstDoubleVectorData(context, WT_RES_RAMP_ON_POWER_MARK2, true);
}

scpi_result_t GetVsaRst11b_RampOffPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_RAMP_OFF_POWER_INST, true);
}

scpi_result_t GetVsaRst11b_RampOffPowerPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_RAMP_OFF_POWER_PEAK, true);
}

scpi_result_t GetVsaRst11b_RampOffMask(scpi_t *context)
{
    int Number[1] = {0};
    int iRet = WT_ERR_CODE_OK;
    if (!SCPI_CommandNumbers(context, Number, 1))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    if (Number[0] <= 1)
    {
        return GetRstDoubleVectorData(context, WT_RES_RAMP_OFF_POWER_MARK1, true);
    }
    return GetRstDoubleVectorData(context, WT_RES_RAMP_OFF_POWER_MARK2, true);
}

scpi_result_t GetVsaRst11b_PhaseError(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_11B_PHASE_ERR);
}

scpi_result_t GetVsaRst11b_BitError(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_11B_BIT_RATE);
}

scpi_result_t GetVsaRst11b_PreambleFreqError(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11B_FREQ_ERR, true);
}

scpi_result_t GetVsaRst11b_LO_RBW(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_LO_LEAKAGE_RBW);
}

scpi_result_t GetVsaRst11b_LO_Span(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_LO_LEAKAGE_SPAN);
}

scpi_result_t GetVsaRst11b_LO_Value(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_LO_LEAKAGE, true);
}

scpi_result_t GetVsaRst11b_FreqErrVsTime(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11B_FREQ_ERR_TIME, true);
}

scpi_result_t GetVsaRstOFDM_PreambleFreqErr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_PREAMBLE_FREQ_ERR_HZ);
}

scpi_result_t GetVsaRstOFDM_PreambleFreqErrValid(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_PREAMBLE_FREQ_ERR_FLAG);
}

scpi_result_t GetVsaRstSIGBSymoblConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SIGB_CONST_DATA, true);
}
scpi_result_t GetVsaRstSIGBSymoblPilotConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SIGB_CONST_PILOT, true);
}
scpi_result_t GetVsaRstSIGBSymoblRefConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SIGB_CONST_REF, true);
}

scpi_result_t GetVsaRstAllUserSymoblConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ALL_USER_CONST_DATA, true);
}
scpi_result_t GetVsaRstAllUserSymoblPilotConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ALL_USER_CONST_PILOT, true);
}
scpi_result_t GetVsaRstAllUserSymoblRefConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ALL_USER_CONST_REF, true);
}

scpi_result_t GetVsaRstMUAllUserSymoblConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_MU_MIMO_ALL_USER_CONST_DATA, true);
}
scpi_result_t GetVsaRstMUAllUserSymoblPilotConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_MU_MIMO_ALL_USER_CONST_PILOT, true);
}
scpi_result_t GetVsaRstMUAllUserSymoblRefConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_MU_MIMO_ALL_USER_CONST_REF, true);
}

static scpi_result_t DotTbFile2TbAlzParam(scpi_t *context, bool is_json = false)
{
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    AlzParamAxTriggerBase dest;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }

        iRet = WT_File2TBParam(attr->ConnID, AX_TB_REF_FILE, (void *)data, len, &dest);

    } while (0);

    IF_ERR_RETURN(iRet);

    if (!is_json)
    {
        SCPI_ResultArbitraryBlock(context, (const char *)&dest, sizeof(dest));
    }
    else
    {
        std::string result = print_tb_alz_json(&dest);
        SCPI_ResultArbitraryBlock(context, result.c_str(), result.length());
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaFile2SLEAlzParam(scpi_t *context, bool is_json = false)
{
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    AlzParamSparkLink dest;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }

        iRet = WT_File2SLEParam(attr->ConnID, AX_TB_REF_FILE, (void *)data, len, &dest);

    } while (0);
    IF_ERR_RETURN(iRet);

    if (!is_json)
    {
        SCPI_ResultArbitraryBlock(context, (const char *)&dest, sizeof(dest));
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FrmType = " << dest.FrmType << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Bandwidth = " << dest.Bandwidth << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CtrlInfoType = " << dest.CtrlInfoType << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PayloadCrcType = " << dest.PayloadCrcType << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PayloadCrcSeed = " << dest.PayloadCrcSeed << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SlotIndex = " << dest.SlotIndex << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PilotDens = " << dest.PilotDens << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BoardIndex = " << dest.BoardIndex << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PolarEncodePathNum = " << dest.PolarEncodePathNum << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PID = " << dest.PID << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Scramble = " << dest.Scramble << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ChannelType = " << dest.ChannelType << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FreqRange = " << dest.FreqRange << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "MSeqNo = " << dest.MSeqNo << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SyncSource = " << dest.SyncSource << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PayloadAnalyzeMode = " << dest.PayloadAnalyzeMode << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "MCS = " << dest.MCS << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RaisedRootCosineFilter = " << dest.RaisedRootCosineFilter << std::endl;
    }
    else
    {
        std::string result = print_sle_alz_json(&dest);
        SCPI_ResultArbitraryBlock(context, result.c_str(), result.length());
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaFile2SLEParam(scpi_t *context)
{
    return GetVsaFile2SLEAlzParam(context);
}

scpi_result_t GetVsaFile2TBParam(scpi_t *context)
{
    return DotTbFile2TbAlzParam(context);
}

scpi_result_t GetVsaFile2TBParam_Json(scpi_t *context)
{
    return DotTbFile2TbAlzParam(context, true);
}

scpi_result_t GetVsaRst_TBUnusedToneErr(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_TB_UNUSED_TONE_ERROR, true);
}

scpi_result_t GetVsaRstSpecification(scpi_t *context)
{
    scpi_result_t iRet = SCPI_RES_OK;
    do
    {
        GetRstDoubleData(context, WT_RES_FREQ_ERR_SPECIFICATION_PPM);
        GetRstDoubleData(context, WT_RES_CLOCK_ERR_SPECIFICATION_PPM);
        GetRstDoubleData(context, WT_RES_LEAGKAGE_SPECIFICATION_DB);
        GetRstDoubleData(context, WT_RES_EVM_SPECIFICATION_DB);

    } while (0);

    return iRet;
}

scpi_result_t GetVsaRstChannelPhaseResponse(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_CHANNEL_PHASE_RESPONSE, true);
}

scpi_result_t GetVsaRstChannelAmplitudeResponse(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_CHANNEL_AMPLITUDE_RESP, true);
}

scpi_result_t GetVsaRstSymbolPhaseError(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SYMBOL_PHASE_ERROR, true);
}

scpi_result_t GetVsaRstSymbolAmplitude(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_OFDM_SYMBOL_AMPLITUDE, true);
}

scpi_result_t GetVsaRstMimoPowerTable(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    PowerTableInfo TableInfo;
    int iRet = WT_ERR_CODE_OK;

    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_POWER_TABLE, &TableInfo, sizeof(PowerTableInfo), streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, TableInfo.PowerFrame); // PowerFrame
    SCPI_ResultDouble(context, TableInfo.PowerPeak);  // PowerPeak
    for (int i = 0; i < MAX_DF_NUM; i++)
    {
        SCPI_ResultInt(context, TableInfo.ValidFig[i]); // ValidFig,8个
    }
    for (int i = 0; i < MAX_DF_NUM; i++)
    {
        if (TableInfo.ValidFig[i] == 1)
        {
            SCPI_ResultDouble(context, TableInfo.StrmPwr[i]); // StrmPwr，8个
        }
        else
        {
            SCPI_ResultDouble(context, UNVALID_VAL);
        }
    }

    return SCPI_RES_OK;
}

scpi_result_t SetAverageSetting(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int AvgType = 0;
        int AvgMethod = 0;
        int AvgCount = 1; // 平均次数为1时，认为不开启平均~

        // 平均开关on or off
        if (!SCPI_ParamInt(context, &AvgType, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        ILLEGAL_PARAM_RETURN(AvgType < 0 || AvgType > 1);

        if (AvgType == 1)
        {
            // AvgMethod：0-4
            if (!SCPI_ParamInt(context, &AvgMethod, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            ILLEGAL_PARAM_RETURN(AvgMethod < 0 || AvgMethod > 3);

            // AvgCount：1-50
            if (!SCPI_ParamInt(context, &AvgCount, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            ILLEGAL_PARAM_RETURN(AvgCount < 1 || AvgCount > 50);
        }
        else
        {
            AvgMethod = 2; // 滑动平均
            AvgCount = 1;  // 平均次数为1，即没有开启平均
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
#if 0 // debug
        if (attr->avgParam.AvgType != AvgMethod || attr->avgParam.AvgCount != AvgCount)
        {
            attr->MinAvgCount = AvgCount;
        }
#endif
        attr->avgParam.AvgCount = AvgCount;
        attr->avgParam.AvgType = AvgMethod;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(attr->avgParam.AvgCount) << Pout(attr->avgParam.AvgType) << endl;
        iRet = WT_SetVSAAverageParameter(attr->ConnID, &attr->avgParam);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetAverageMinCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int AvgMinCount = 1; // 最小平均平均次数
        if (!SCPI_ParamInt(context, &AvgMinCount, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        ILLEGAL_PARAM_RETURN(AvgMinCount < 0);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->MinAvgCount = AvgMinCount;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(attr->MinAvgCount) << endl;
        iRet = WT_SetVSAAverageParameter(attr->ConnID, &attr->avgParam);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetCurAverageCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int AvgCount = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_GetCurrAverageCount(attr->ConnID, &AvgCount);
    if(iRet == WT_ERR_CODE_OK)
    {
        SCPI_ResultInt(context, AvgCount);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetAverageBaseResult(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    VsaAverageResult Result[ArrCnt];
    memset((char *)Result, 0, sizeof(VsaAverageResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_GetAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        for(int i = 0; i < ArrCnt; i++)
        {
            SCPI_ResultDouble(context, Result[i].BaseResult.PowerFrame);
            SCPI_ResultDouble(context, Result[i].BaseResult.PowerAll);
            SCPI_ResultDouble(context, Result[i].BaseResult.PowerPeak);
            SCPI_ResultDouble(context, Result[i].BaseResult.EvmAll);
            SCPI_ResultDouble(context, Result[i].BaseResult.EvmPeak);
            SCPI_ResultDouble(context, Result[i].BaseResult.EvmData);
            SCPI_ResultDouble(context, Result[i].BaseResult.EvmPilot);
            SCPI_ResultDouble(context, Result[i].BaseResult.EvmPsdu);
            SCPI_ResultDouble(context, Result[i].BaseResult.EvmShrPhr);
            SCPI_ResultDouble(context, Result[i].BaseResult.FreqOffset);
            SCPI_ResultDouble(context, Result[i].BaseResult.CarrierLeakage);
            SCPI_ResultDouble(context, Result[i].BaseResult.SymClkErr);
            SCPI_ResultDouble(context, Result[i].BaseResult.PhaseErr);
            SCPI_ResultDouble(context, Result[i].BaseResult.IQImbAmp);
            SCPI_ResultDouble(context, Result[i].BaseResult.IQImbPhase);
        }
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetAverageResultARB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    VsaAverageResult Result[ArrCnt];
    memset((char *)Result, 0, sizeof(VsaAverageResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_GetAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Result, sizeof(VsaAverageResult) * ArrCnt);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetBTAverageResultARB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    //VsaAverageResult Result[ArrCnt];
    VsaBTCommResult Result1[ArrCnt];
    //VsaSLECommResult Result2[ArrCnt];

    memset((char *)Result1, 0, sizeof(VsaBTCommResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int demod = attr->vsaParam.Demode;
    if (demod == WT_DEMOD_BT)
    {
        iRet = WT_GetBTAverageResult(attr->ConnID, &Result1[0], &Result1[1], &Result1[2], 0, streamID, segmentID);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "[WT_GetBTAverageResult]iRet = %d\n\n\n", iRet);
    }
    // else if (demod == WT_DEMOD_GLE)
    // {
    //     iRet = WT_GetSLEAverageResult(attr->ConnID, &Result2[0], &Result2[1], &Result2[2], 0, streamID, segmentID);
    // }

    if (iRet == WT_ERR_CODE_OK)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Result1, sizeof(VsaBTCommResult) * ArrCnt);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetSLEAverageResultARB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    VsaSLECommResult Result[ArrCnt];
    memset((char *)Result, 0, sizeof(VsaSLECommResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_GetSLEAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Result, sizeof(VsaSLECommResult) * ArrCnt);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetBTAverageResult(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    VsaBTCommResult Result[ArrCnt];
    memset((char *)Result, 0, sizeof(VsaBTCommResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_GetBTAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        //平均值
        SCPI_ResultDouble(context, Result[0].BaseResult.PowerFrame);
        SCPI_ResultDouble(context, Result[0].BaseResult.PowerAll);
        SCPI_ResultDouble(context, Result[0].BaseResult.PowerPeak);

        SCPI_ResultDouble(context, Result[0].Init_Freq_Error);
        SCPI_ResultDouble(context, Result[0].BR_Maxmum_Freq_Drift);
        SCPI_ResultDouble(context, Result[0].BR_Freq_Drift_Rate);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F1_Max);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F1_Avg);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F1_Min);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F2_Max);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F2_Avg);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F2_Min);
        SCPI_ResultDouble(context, Result[0].BR_BLE_Delta_F2_99p9PCT);

        SCPI_ResultDouble(context, Result[0].EDR_Omega_i);
        SCPI_ResultDouble(context, Result[0].EDR_Max_Omega_io);
        SCPI_ResultDouble(context, Result[0].EDR_Max_Omega_o);
        
        SCPI_ResultDouble(context, Result[0]. EDR_DEVM_Avg);
        SCPI_ResultDouble(context, Result[0].EDR_DEVM_Peak);
        SCPI_ResultDouble(context, Result[0].EDR_Diff_Power);
        SCPI_ResultDouble(context, Result[0].EDR_Max_FreqVar);
        SCPI_ResultDouble(context, Result[0].EDR_DEVM_99PCT);
        SCPI_ResultDouble(context, Result[0].EDR_GuardTime);

        SCPI_ResultDouble(context, Result[0].BLE_FnMax);
        SCPI_ResultDouble(context, Result[0].BLE_F0FnMax);
        SCPI_ResultDouble(context, Result[0].BLE_F0FnAvg);
        SCPI_ResultDouble(context, Result[0].BLE_F0Fn5_Max);
        SCPI_ResultDouble(context, Result[0].BLE_Delta_F1F0);
        SCPI_ResultDouble(context, Result[0].BLE_Delta_F0F3);
        SCPI_ResultDouble(context, Result[0].BLE_Delta_F0Fn3);
        SCPI_ResultDouble(context, Result[0].BLE_Freq_offset_sync);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Pwr_Avg);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Pwr_Peak);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Pwr_Peak_sub_Avg);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Fsi_Max);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Fsi_Min);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Fs1_sub_Fp);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Fsi_sub_F0_Max);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Fsi_sub_Fsi3_Max);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Pwr_Ref_Avg);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Pwr_Ref_DevDivAvg);
        SCPI_ResultDouble(context, Result[0].BLE_CTE_Pwr_Pn_DevDivAvg_Max);
        SCPI_ResultDouble(context, Result[0].BLE_Delta_F1_99p9PCT);
        SCPI_ResultInt(context, Result[0].DeltaF1Len);
        SCPI_ResultInt(context, Result[0].DeltaF2Len);
        SCPI_ResultDouble(context, Result[0].EDR_GFSK_Power);
        SCPI_ResultDouble(context, Result[0].EDR_GFSK_Power_Peak);
        SCPI_ResultDouble(context, Result[0].EDR_DPSK_Power);
        SCPI_ResultDouble(context, Result[0].EDR_DPSK_Power_Peak);
        


        //最大值
        SCPI_ResultDouble(context, Result[1].BaseResult.PowerFrame);
        SCPI_ResultDouble(context, Result[1].BaseResult.PowerAll);
        SCPI_ResultDouble(context, Result[1].BaseResult.PowerPeak);

        SCPI_ResultDouble(context, Result[1].Init_Freq_Error);
        SCPI_ResultDouble(context, Result[1].BR_Maxmum_Freq_Drift);
        SCPI_ResultDouble(context, Result[1].BR_Freq_Drift_Rate);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F1_Max);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F1_Avg);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F1_Min);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F2_Max);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F2_Avg);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F2_Min);
        SCPI_ResultDouble(context, Result[1].BR_BLE_Delta_F2_99p9PCT);

        SCPI_ResultDouble(context, Result[1].EDR_Omega_i);
        SCPI_ResultDouble(context, Result[1].EDR_Max_Omega_io);
        SCPI_ResultDouble(context, Result[1].EDR_Max_Omega_o);
        
        SCPI_ResultDouble(context, Result[1].EDR_DEVM_Avg);
        SCPI_ResultDouble(context, Result[1].EDR_DEVM_Peak);
        SCPI_ResultDouble(context, Result[1].EDR_Diff_Power);
        SCPI_ResultDouble(context, Result[1].EDR_Max_FreqVar);
        SCPI_ResultDouble(context, Result[1].EDR_DEVM_99PCT);
        SCPI_ResultDouble(context, Result[1].EDR_GuardTime);
        
        SCPI_ResultDouble(context, Result[1].BLE_FnMax);
        SCPI_ResultDouble(context, Result[1].BLE_F0FnMax);
        SCPI_ResultDouble(context, Result[1].BLE_F0FnAvg);
        SCPI_ResultDouble(context, Result[1].BLE_F0Fn5_Max);
        SCPI_ResultDouble(context, Result[1].BLE_Delta_F1F0);
        SCPI_ResultDouble(context, Result[1].BLE_Delta_F0F3);
        SCPI_ResultDouble(context, Result[1].BLE_Delta_F0Fn3);
        SCPI_ResultDouble(context, Result[1].BLE_Freq_offset_sync);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Pwr_Avg);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Pwr_Peak);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Pwr_Peak_sub_Avg);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Fsi_Max);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Fsi_Min);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Fs1_sub_Fp);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Fsi_sub_F0_Max);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Fsi_sub_Fsi3_Max);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Pwr_Ref_Avg);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Pwr_Ref_DevDivAvg);
        SCPI_ResultDouble(context, Result[1].BLE_CTE_Pwr_Pn_DevDivAvg_Max);
        SCPI_ResultDouble(context, Result[1].BLE_Delta_F1_99p9PCT);
        SCPI_ResultInt(context, Result[1].DeltaF1Len);
        SCPI_ResultInt(context, Result[1].DeltaF2Len);
        SCPI_ResultDouble(context, Result[1].EDR_GFSK_Power);
        SCPI_ResultDouble(context, Result[1].EDR_GFSK_Power_Peak);
        SCPI_ResultDouble(context, Result[1].EDR_DPSK_Power);
        SCPI_ResultDouble(context, Result[1].EDR_DPSK_Power_Peak);

        //最小值
        SCPI_ResultDouble(context, Result[2].BaseResult.PowerFrame);
        SCPI_ResultDouble(context, Result[2].BaseResult.PowerAll);
        SCPI_ResultDouble(context, Result[2].BaseResult.PowerPeak);

        SCPI_ResultDouble(context, Result[2].Init_Freq_Error);
        SCPI_ResultDouble(context, Result[2].BR_Maxmum_Freq_Drift);
        SCPI_ResultDouble(context, Result[2].BR_Freq_Drift_Rate);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F1_Max);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F1_Avg);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F1_Min);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F2_Max);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F2_Avg);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F2_Min);
        SCPI_ResultDouble(context, Result[2].BR_BLE_Delta_F2_99p9PCT);

        SCPI_ResultDouble(context, Result[2].EDR_Omega_i);
        SCPI_ResultDouble(context, Result[2].EDR_Max_Omega_io);
        SCPI_ResultDouble(context, Result[2].EDR_Max_Omega_o);
        
        SCPI_ResultDouble(context, Result[2].EDR_DEVM_Avg);
        SCPI_ResultDouble(context, Result[2].EDR_DEVM_Peak);
        SCPI_ResultDouble(context, Result[2].EDR_Diff_Power);
        SCPI_ResultDouble(context, Result[2].EDR_Max_FreqVar);
        SCPI_ResultDouble(context, Result[2].EDR_DEVM_99PCT);
        SCPI_ResultDouble(context, Result[2].EDR_GuardTime);
        
        SCPI_ResultDouble(context, Result[2].BLE_FnMax);
        SCPI_ResultDouble(context, Result[2].BLE_F0FnMax);
        SCPI_ResultDouble(context, Result[2].BLE_F0FnAvg);
        SCPI_ResultDouble(context, Result[2].BLE_F0Fn5_Max);
        SCPI_ResultDouble(context, Result[2].BLE_Delta_F1F0);
        SCPI_ResultDouble(context, Result[2].BLE_Delta_F0F3);
        SCPI_ResultDouble(context, Result[2].BLE_Delta_F0Fn3);
        SCPI_ResultDouble(context, Result[2].BLE_Freq_offset_sync);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Pwr_Avg);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Pwr_Peak);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Pwr_Peak_sub_Avg);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Fsi_Max);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Fsi_Min);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Fs1_sub_Fp);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Fsi_sub_F0_Max);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Fsi_sub_Fsi3_Max);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Pwr_Ref_Avg);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Pwr_Ref_DevDivAvg);
        SCPI_ResultDouble(context, Result[2].BLE_CTE_Pwr_Pn_DevDivAvg_Max);
        SCPI_ResultDouble(context, Result[2].BLE_Delta_F1_99p9PCT);
        SCPI_ResultInt(context, Result[2].DeltaF1Len);
        SCPI_ResultInt(context, Result[2].DeltaF2Len);
        SCPI_ResultDouble(context, Result[2].EDR_GFSK_Power);
        SCPI_ResultDouble(context, Result[2].EDR_GFSK_Power_Peak);
        SCPI_ResultDouble(context, Result[2].EDR_DPSK_Power);
        SCPI_ResultDouble(context, Result[2].EDR_DPSK_Power_Peak);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetSLEAverageResult(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    VsaSLECommResult Result[ArrCnt];
    memset((char *)Result, 0, sizeof(VsaSLECommResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_GetSLEAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        //平均值
        SCPI_ResultDouble(context, Result[0].BaseResult.PowerFrame);
        SCPI_ResultDouble(context, Result[0].BaseResult.PowerAll);
        SCPI_ResultDouble(context, Result[0].BaseResult.PowerPeak);

        SCPI_ResultDouble(context, Result[0].Max_Freq_Drift);
        SCPI_ResultDouble(context, Result[0].Freq_Drift_Rate);
        SCPI_ResultDouble(context, Result[0].EvmAvg);
        SCPI_ResultDouble(context, Result[0].EvmPeak);
        SCPI_ResultDouble(context, Result[0].Evm99PCT);
        SCPI_ResultDouble(context, Result[0].CtlInfo_EvmAvg);
        SCPI_ResultDouble(context, Result[0].CtlInfo_EvmPeak);
        SCPI_ResultDouble(context, Result[0].CtlInfo_Evm99PCT);
        SCPI_ResultDouble(context, Result[0].Delta_Fd1_Avg);
        SCPI_ResultDouble(context, Result[0].Delta_Fd1_Max);
        SCPI_ResultDouble(context, Result[0].Delta_Fd1_Min);
        SCPI_ResultDouble(context, Result[0].Delta_Fd2_Avg);
        SCPI_ResultDouble(context, Result[0].Delta_Fd2_Min);

        //最大值
        SCPI_ResultDouble(context, Result[1].BaseResult.PowerFrame);
        SCPI_ResultDouble(context, Result[1].BaseResult.PowerAll);
        SCPI_ResultDouble(context, Result[1].BaseResult.PowerPeak);

        SCPI_ResultDouble(context, Result[1].Max_Freq_Drift);
        SCPI_ResultDouble(context, Result[1].Freq_Drift_Rate);
        SCPI_ResultDouble(context, Result[1].EvmAvg);
        SCPI_ResultDouble(context, Result[1].EvmPeak);
        SCPI_ResultDouble(context, Result[1].Evm99PCT);
        SCPI_ResultDouble(context, Result[1].CtlInfo_EvmAvg);
        SCPI_ResultDouble(context, Result[1].CtlInfo_EvmPeak);
        SCPI_ResultDouble(context, Result[1].CtlInfo_Evm99PCT);
        SCPI_ResultDouble(context, Result[1].Delta_Fd1_Avg);
        SCPI_ResultDouble(context, Result[1].Delta_Fd1_Max);
        SCPI_ResultDouble(context, Result[1].Delta_Fd1_Min);
        SCPI_ResultDouble(context, Result[1].Delta_Fd2_Avg);
        SCPI_ResultDouble(context, Result[1].Delta_Fd2_Min);
        //最小值
        SCPI_ResultDouble(context, Result[2].BaseResult.PowerFrame);
        SCPI_ResultDouble(context, Result[2].BaseResult.PowerAll);
        SCPI_ResultDouble(context, Result[2].BaseResult.PowerPeak);

        SCPI_ResultDouble(context, Result[2].Max_Freq_Drift);
        SCPI_ResultDouble(context, Result[2].Freq_Drift_Rate);
        SCPI_ResultDouble(context, Result[2].EvmAvg);
        SCPI_ResultDouble(context, Result[2].EvmPeak);
        SCPI_ResultDouble(context, Result[2].Evm99PCT);
        SCPI_ResultDouble(context, Result[2].CtlInfo_EvmAvg);
        SCPI_ResultDouble(context, Result[2].CtlInfo_EvmPeak);
        SCPI_ResultDouble(context, Result[2].CtlInfo_Evm99PCT);
        SCPI_ResultDouble(context, Result[2].Delta_Fd1_Avg);
        SCPI_ResultDouble(context, Result[2].Delta_Fd1_Max);
        SCPI_ResultDouble(context, Result[2].Delta_Fd1_Min);
        SCPI_ResultDouble(context, Result[2].Delta_Fd2_Avg);
        SCPI_ResultDouble(context, Result[2].Delta_Fd2_Min);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t GetMimoAverageVompositeResult(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int segmentID = 0;
    VsaBaseResult Result;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    SCPI_ParamInt(context, &segmentID, false);
    memset((char *)&Result, 0, sizeof(VsaBaseResult));

    iRet = WT_GetAvgBaseCompositeResult(attr->ConnID, &Result, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        SCPI_ResultArbitraryBlock(context, (char *)&Result, sizeof(VsaBaseResult));
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t CleanAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        iRet = WT_ClrVSAAvgData(attr->ConnID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVectorResult(scpi_t *context)
{
    char resultstring[256] = {0};
    size_t copyLen = 0;
    int resultsize = 0;
    int streamID = 0;
    int segmentID = 0;
    std::unique_ptr<char[]> ResultBuf = nullptr;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (!SCPI_ParamCopyText(context, resultstring, sizeof(resultstring) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }
    if (!SCPI_ParamInt(context, &resultsize, true))
    {
        return SCPI_RES_ERR;
    }
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;
    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    ResultBuf.reset(new (std::nothrow) char[resultsize]);
    int iRet = WT_GetVectorResult(attr->ConnID, resultstring, ResultBuf.get(), resultsize, streamID, segmentID);

    IF_ERR_RETURN(iRet);
    SCPI_ResultArbitraryBlock(context, ResultBuf.get(), resultsize);
    return SCPI_RES_OK;
}

scpi_result_t GetVectorResultElementCount(scpi_t *context)
{
    char resultstring[256] = {0};
    size_t copyLen = 0;
    unsigned int elementcount = 0;
    int streamID = 0;
    int segmentID = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (!SCPI_ParamCopyText(context, resultstring, sizeof(resultstring) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;
    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    int iRet = WT_GetVectorResultElementCount(attr->ConnID, resultstring, &elementcount, streamID, segmentID);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, elementcount);
    return SCPI_RES_OK;
}

scpi_result_t GetVectorResultElementSize(scpi_t *context)
{
    char resultstring[256] = {0};
    size_t copyLen = 0;
    unsigned int elementsize = 0;
    int streamID = 0;
    int segmentID = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (!SCPI_ParamCopyText(context, resultstring, sizeof(resultstring) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;
    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    int iRet = WT_GetVectorResultElementSize(attr->ConnID, resultstring, &elementsize, streamID, segmentID);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, elementsize);
    return SCPI_RES_OK;
}

scpi_result_t SetVSAAutoPowerCorrect(scpi_t *context)
{
    int Flag = 0;
    int iRet = WT_OK;
    char FileName[1024] = {0};
    size_t FileNameLen = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (!SCPI_ParamInt(context, &Flag, true))
    {
        return SCPI_RES_ERR;
    }

    for (int i = 0; i < attr->m_vsaAutoPowerCorrectFileName.size(); ++i)
    {
        attr->m_vsaAutoPowerCorrectFileName[i] = "";
        attr->m_VSAPowerCorrectionJson[i].clear();
    }

    if (Flag == true)
    {
        for (int i = 0; i < context->parser_state.numberOfParameters - 1 && i < attr->m_vsaAutoPowerCorrectFileName.max_size(); ++i)
        {
            if (!SCPI_ParamCopyText(context, FileName, sizeof(FileName) - 1, &FileNameLen, true))
            {
                return SCPI_RES_ERR;
            }
            attr->m_vsaAutoPowerCorrectFileName[i] = FileName;
            char Buf[1024] = POWER_CORRECTION_TABLE;
            strcat(Buf, FileName);
            iRet = ParseJSONFile(Buf, attr->m_VSAPowerCorrectionJson[i]);
        }
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaRstSpectrumPointPower(scpi_t *context)
{
    double Offset = 0;
    int streamID = 0;
    int segmentID = 0;
    double Power = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (!SCPI_ParamDouble(context, &Offset, true))
    {
        return SCPI_RES_ERR;
    }
    if (!SCPI_ParamInt(context, &streamID, true))
    {
        return SCPI_RES_ERR;
    }
    if (!SCPI_ParamInt(context, &segmentID, true))
    {
        return SCPI_RES_ERR;
    }

    int iRet = WT_GetSpectrumPointPower(attr->ConnID, Offset, &Power, streamID, segmentID);
    IF_ERR_RETURN(iRet);
    SCPI_ResultDouble(context, Power);

    return SCPI_RES_OK;
}

scpi_result_t SetVsaAlzAvgTimes(scpi_t *context)
{
    int ParamVal = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        if (ParamVal < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.commonAnalyzeParam.AvgTimes = ParamVal;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//sle结果
scpi_result_t GetVsaGleCtrlInfoRstSymoblConst(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_CONST_DATA, false);
}

scpi_result_t GetVsaGleCtrlInfoRstSymoblConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_CONST_DATA, true);
}

scpi_result_t GetVsaGleCtrlInfoRstSymoblPilotConst(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_CONST_PILOT, false);
}

scpi_result_t GetVsaGleCtrlInfoRstSymoblPilotConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_CONST_PILOT, true);
}

scpi_result_t GetVsaGleCtrlInfoRstSymoblRefConst(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_CONST_REF, false);
}

scpi_result_t GetVsaGleCtrlInfoRstSymoblRefConstARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_CONST_REF, true);
}

scpi_result_t GetVsaGleCtrlInfoRstEVMTime(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_EVM_TIME, false);
}

scpi_result_t GetVsaGleCtrlInfoRstEVMTimeARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_CTRINFO_EVM_TIME, true);
}

scpi_result_t GetVsaGleAcpData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_GLE_SPEC_ACP, false);
}

scpi_result_t GetVsaGleAcpMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_GLE_SPEC_ACP_MASK, false);
}

scpi_result_t GetVsaGleAcpDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_GLE_SPEC_ACP, true);
}

scpi_result_t GetVsaGleAcpMaskARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_GLE_SPEC_ACP_MASK, true);
}

scpi_result_t GetVsaGleEyePoint(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_GLE_EYE, false);
}

scpi_result_t GetVsaGleEyePointARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_GLE_EYE, true);
}

scpi_result_t GetVsaGleFrameType(scpi_t * context)
{
    return GetRstIntData(context, WT_RES_GLE_FRAME_TYPE);
}

scpi_result_t GetVsaGleBandWidth(scpi_t * context)
{
    return GetRstIntData(context, WT_RES_GLE_BAND_WIDTH);
}

scpi_result_t GetVsaGlePID(scpi_t * context)
{
    return GetRstIntData(context, WT_RES_GLE_PID);
}

scpi_result_t GetVsaGlePayloadLen(scpi_t * context)
{
    return GetRstIntData(context, WT_RES_GLE_PAYLOAD_LEN);
}

scpi_result_t GetVsaGlePayloadCrcType(scpi_t * context)
{
    return GetRstIntData(context, WT_RES_GLE_PAYLOAD_CRC_TYPE);
}

scpi_result_t GetVsaGlePayloadCrc(scpi_t * context)
{
    return GetRstIntData(context, WT_RES_GLE_PAYLOAD_CRC);
}

scpi_result_t GetVsaGleSyncSignal(scpi_t * context)
{
    return GetRstCharVectorData(context, WT_RES_SPARK_SYNC, false);
}

scpi_result_t GetVsaGleCtrlInfo(scpi_t * context)
{
    return GetRstCharVectorData(context, WT_RES_SPARK_CTRL, false);
}

scpi_result_t GetVsaGlePayloadBin(scpi_t * context)
{
    return GetRstCharVectorData(context, WT_RES_SPARK_PAYLOAD, false);
}

scpi_result_t GetVsaGleDeltaFd1Avg(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_DELTA_FD1_AVG);
}

scpi_result_t GetVsaGleDeltaFd1Max(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_DELTA_FD1_MAX);
}

scpi_result_t GetVsaGleDeltaFd1Min(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_DELTA_FD1_MIN);
}

scpi_result_t GetVsaGleDeltaFd2Avg(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_DELTA_FD2_AVG);
}

scpi_result_t GetVsaGleDeltaFd2Min(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_DELTA_FD2_MIN);
}

scpi_result_t GetVsaGleZeroCrossingErr(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_ZERO_CROSSING_ERR);
}

scpi_result_t GetVsaGleCtrlInfoEvmAvg(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_CTRL_INFO_EVM_AVG);
}

scpi_result_t GetVsaGleCtrlInfoEvmPeak(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_CTRL_INFO_EVM_PEAK);
}

scpi_result_t GetVsaGleCtrlInfoEvm99PCT(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_CTRL_INFO_EVM_99PCT);
}

scpi_result_t GetVsaGleEvmAvg(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_EVM_AVG);
}

scpi_result_t GetVsaGleEvmPeak(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_EVM_PEAK);
}

scpi_result_t GetVsaGleEvm99PCT(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_EVM_99PCT);
}

scpi_result_t GetVsaGleInitFreqErr(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_INIT_FREQ_ERR);
}

scpi_result_t GetVsaGleFreqDrift(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_MAX_FREQ_DRIFT);
}

scpi_result_t GetVsaGleFreqDriftRate(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_FREQ_DRIFT_RATE);
}

scpi_result_t GetVsaGleSymbolClkErr(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_SYM_CLK_ERR);
}

scpi_result_t GetVsaGleMaxTimeDev(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_MAX_TIME_DEV);
}

scpi_result_t GetVsaGleCtrlInfoType(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_CTRL_INFO_TYPE);
}

scpi_result_t GetVsaGleCtrlInfoCRC(scpi_t * context)
{
    return GetRstDoubleData(context, WT_RES_GLE_CTRL_INFO_CRC);
}

//sle结果

//sle分析参数

scpi_result_t SetVsaGleAlzPayloadAlzMode(scpi_t *context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        ILLEGAL_PARAM_RETURN(1 > ParamVal || 2 < ParamVal);
        int FrmType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        if (FrmType == WT_GLE_FRAMETYPE_1)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PayloadAnalyzeMode = 1;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PayloadAnalyzeMode = ParamVal;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.PayloadAnalyzeMode =" << attr->vsaAlzParam.analyzeParamSparkLink.PayloadAnalyzeMode << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzMCS(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 12 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int FrmType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        int Mode = attr->vsaAlzParam.analyzeParamSparkLink.PayloadAnalyzeMode;
        if (FrmType == WT_GLE_FRAMETYPE_1)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.MCS = 0;
        }
        else
        {
            if (Mode == 1)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.MCS = ParamVal;
            }
            else
            {
                attr->vsaAlzParam.analyzeParamSparkLink.MCS = 0;
            }
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.MCS =" << attr->vsaAlzParam.analyzeParamSparkLink.MCS << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzRRCFilter(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 1 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamSparkLink.RaisedRootCosineFilter = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.RaisedRootCosineFilter =" << attr->vsaAlzParam.analyzeParamSparkLink.RaisedRootCosineFilter << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzFrameType(scpi_t *context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(WT_GLE_FRAMETYPE_1 > ParamVal || WT_GLE_FRAMETYPE_4 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamSparkLink.FrmType = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.FrmType =" << attr->vsaAlzParam.analyzeParamSparkLink.FrmType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzBandWidth(scpi_t *context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(WT_GLE_FRAMETYPE_1 > ParamVal || WT_GLE_FRAMETYPE_4 < ParamVal);
        ILLEGAL_PARAM_RETURN(WT_GLE_FRAMETYPE_3 == ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamSparkLink.Bandwidth = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.Bandwidth =" << attr->vsaAlzParam.analyzeParamSparkLink.Bandwidth << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzCtrlinfoType(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->vsaAlzParam.analyzeParamSparkLink.FrmType == 1 || attr->vsaAlzParam.analyzeParamSparkLink.FrmType == 2)
        {
            ILLEGAL_PARAM_RETURN(WT_GLE_CTRLINFOTYPE_A1 > ParamVal || WT_GLE_CTRLINFOTYPE_A7 < ParamVal);
        }
        else
        {
            ILLEGAL_PARAM_RETURN(WT_GLE_CTRLINFOTYPE_B1 > ParamVal || WT_GLE_CTRLINFOTYPE_B5 < ParamVal);
        }
        attr->vsaAlzParam.analyzeParamSparkLink.CtrlInfoType = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.CtrlInfoType =" << attr->vsaAlzParam.analyzeParamSparkLink.CtrlInfoType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzPID(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 0xffffff < ParamVal);//0xffffff

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        int tempSyncSource = attr->vsaAlzParam.analyzeParamSparkLink.SyncSource;

        if (tempType == 1 || tempType == 2)
        {
            if (tempSyncSource == 0)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PID = ParamVal;
            }
            else
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PID = 0;
            }
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PID = ParamVal;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.PID =" << attr->vsaAlzParam.analyzeParamSparkLink.PID << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzPayloadCrcType(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(1 > ParamVal || 2 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.CtrlInfoType;
        int tempFramType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;

        if (tempFramType == 1)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType = 1;
        }
        else if (tempFramType == 2)
        {
            if (tempType == WT_GLE_CTRLINFOTYPE_A1)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType = 1;
            }
            else
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType = ParamVal;
            }
        }
        else
        {
            if (tempType == WT_GLE_CTRLINFOTYPE_B5)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType = 1;
            }
            else
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType = ParamVal;
            }
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.PayloadCrcType =" << attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzPayloadCrcSeed(scpi_t *context)
{
    unsigned int ParamVal = 0;
    do
    {
        if (!SCPI_ParamUnsignedInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.CtrlInfoType;
        int tempFramType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        int tempCRC = attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcType;
        if (tempFramType == 1)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = 5592405; // 0x555555
        }
        else if (tempFramType == 2)
        {
            if (tempType == WT_GLE_CTRLINFOTYPE_A1)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = 5592405; // 0x555555
            }
            else
            {
                if (tempCRC == 1)
                {
                    ILLEGAL_PARAM_RETURN(0xffffff < ParamVal); // 0xffffff
                    attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = ParamVal;
                }
                else
                {
                    ILLEGAL_PARAM_RETURN(0xFFFFFFFF < ParamVal); // 0xFFFFFFFF
                    attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = ParamVal;
                }
            }
        }
        else
        {
            if (tempType == WT_GLE_CTRLINFOTYPE_B5)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = 5592405; // 0x555555
            }
            else
            {
                if (tempCRC == 1)
                {
                    ILLEGAL_PARAM_RETURN(0xffffff < ParamVal); // 0xffffff
                    attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = ParamVal;
                }
                else
                {
                    ILLEGAL_PARAM_RETURN(0xFFFFFFFF < ParamVal); // 0xFFFFFFFF
                    attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed = ParamVal;
                }
            }
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.PayloadCrcSeed =" << attr->vsaAlzParam.analyzeParamSparkLink.PayloadCrcSeed << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzPilotDensity(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        //ILLEGAL_PARAM_RETURN(4 != ParamVal && 8 != ParamVal &&  16 != ParamVal && 0 != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        if (tempType == WT_GLE_FRAMETYPE_1)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PilotDens = 0;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PilotDens = ParamVal;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.PilotDens =" << attr->vsaAlzParam.analyzeParamSparkLink.PilotDens << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzPolarEncodePathNum(scpi_t *context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(1 > ParamVal || 32 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        if (tempType == WT_GLE_FRAMETYPE_1)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PolarEncodePathNum = 1;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.PolarEncodePathNum = ParamVal;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.PolarEncodePathNum =" << attr->vsaAlzParam.analyzeParamSparkLink.PolarEncodePathNum << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzScramble(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 1 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamSparkLink.Scramble = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.Scramble =" << attr->vsaAlzParam.analyzeParamSparkLink.Scramble << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzChannelType(scpi_t *context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(1 > ParamVal || 2 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.CtrlInfoType;

        if (tempType != WT_GLE_CTRLINFOTYPE_A1 && tempType != WT_GLE_CTRLINFOTYPE_B5)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.ChannelType = 1;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.ChannelType = 2;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.ChannelType =" << attr->vsaAlzParam.analyzeParamSparkLink.ChannelType << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzFreqRange(scpi_t *context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(WT_GLE_FRAMETYPE_auto > ParamVal || WT_GLE_FRAMETYPE_3 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.CtrlInfoType;

        if (tempType != 0 && tempType != 11)
        {
            attr->vsaAlzParam.analyzeParamSparkLink.FreqRange = 1;
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.FreqRange = ParamVal;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.FreqRange = " << attr->vsaAlzParam.analyzeParamSparkLink.FreqRange << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzBroadcastChannelNo(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(WT_GLE_CTRLINFOTYPE_A1 > ParamVal || WT_GLE_CTRLINFOTYPE_B3 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempFreqRange = attr->vsaAlzParam.analyzeParamSparkLink.FreqRange;

        WTLog::Instance().WriteLog(LOG_DEBUG, "tempFreqRange = %d \n\n\n", tempFreqRange);

        if (tempFreqRange == 1)
        {
            ILLEGAL_PARAM_RETURN(WT_GLE_CTRLINFOTYPE_A1 > ParamVal || WT_GLE_CTRLINFOTYPE_A4 < ParamVal);
            attr->vsaAlzParam.analyzeParamSparkLink.BoardIndex = ParamVal;
        }
        else if (tempFreqRange == 2)
        {
            ILLEGAL_PARAM_RETURN((WT_GLE_CTRLINFOTYPE_A5 > ParamVal || WT_GLE_CTRLINFOTYPE_A7 < ParamVal) && ParamVal != 0);
            attr->vsaAlzParam.analyzeParamSparkLink.BoardIndex = ParamVal;
        }
        else
        {
            ILLEGAL_PARAM_RETURN((WT_GLE_CTRLINFOTYPE_B1 > ParamVal || WT_GLE_CTRLINFOTYPE_B3 < ParamVal) && ParamVal != 0);
            attr->vsaAlzParam.analyzeParamSparkLink.BoardIndex = ParamVal;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.BoardIndex =" << attr->vsaAlzParam.analyzeParamSparkLink.BoardIndex << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzSlotIndex(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.ChannelType;
        if (tempType != 0 && tempType != 11)
        {
            if (ParamVal >= 0 && ParamVal <= 1073741823) // 0x3ffffffff
            {
                attr->vsaAlzParam.analyzeParamSparkLink.SlotIndex = ParamVal;
            }
        }
        else
        {
            attr->vsaAlzParam.analyzeParamSparkLink.SlotIndex = 0;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.SlotIndex =" << attr->vsaAlzParam.analyzeParamSparkLink.SlotIndex << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzSyncMode(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 1 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.analyzeParamSparkLink.SyncSource = ParamVal;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.SyncSource =" << attr->vsaAlzParam.analyzeParamSparkLink.SyncSource << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzMSequenceNum(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 5 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int tempType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;
        int tempSyncSource = attr->vsaAlzParam.analyzeParamSparkLink.SyncSource;
        if (tempType == 1 || tempType == 2)
        {

            if (tempSyncSource == 0)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.MSeqNo = 1;
            }
            else
            {
                attr->vsaAlzParam.analyzeParamSparkLink.MSeqNo = 0;
            }
        }
        else
        {
            if (tempSyncSource == 0)
            {
                attr->vsaAlzParam.analyzeParamSparkLink.MSeqNo = ParamVal;
            }
            else
            {
                attr->vsaAlzParam.analyzeParamSparkLink.MSeqNo = 0;
            }
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.MSeqNo =" << attr->vsaAlzParam.analyzeParamSparkLink.MSeqNo << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaGleAlzAccessCode(scpi_t *context)
{
    char Value[128] = {0};
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_OK;
    memset(Value, 0, sizeof(Value));
    const int stdLenTab[4] = {32, 64, 31, 63};
    int TempBuf[64] = {0};
    do
    {
        if (!SCPI_ParamCopyText(context, Value, sizeof(Value), &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int tempSyncSource = attr->vsaAlzParam.analyzeParamSparkLink.SyncSource;
        int tempFrmType = attr->vsaAlzParam.analyzeParamSparkLink.FrmType;

        if (copyLen != stdLenTab[attr->vsaAlzParam.analyzeParamSparkLink.FrmType - subType1])
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < copyLen; i++)
        {
            TempBuf[i] = Value[i] - '0';
            // WTLog::Instance().WriteLog(LOG_DEBUG, "TempBuf[%d] = %d\n\n", i, TempBuf[i]);
        }
        if (tempSyncSource == 1)
        {
            switch (tempFrmType)
            {
            case WT_GLE_FRAMETYPE_1:
                memcpy(attr->vsaAlzParam.analyzeParamSparkLink.SyncSeq, TempBuf, stdLenTab[0] * sizeof(int));
                break;
            case WT_GLE_FRAMETYPE_2:
                memcpy(attr->vsaAlzParam.analyzeParamSparkLink.SyncSeq, TempBuf, stdLenTab[1] * sizeof(int));
                break;
            case WT_GLE_FRAMETYPE_3:
                memcpy(attr->vsaAlzParam.analyzeParamSparkLink.SyncSeq, TempBuf, stdLenTab[2] * sizeof(int));
                break;
            case WT_GLE_FRAMETYPE_4:
                memcpy(attr->vsaAlzParam.analyzeParamSparkLink.SyncSeq, TempBuf, stdLenTab[3] * sizeof(int));
                break;
            default:
                break;
            }
        }
        else
        {
            char buf[32] = {'1','1','1','1','0','0','0','0','1','1','1','1','0','0','0','0','1','1','1','1','0','0','0','0','1','1','1','1','0','0','0','0'};
            for (int j = 0; j < 32; j++)
            {
                TempBuf[j] = buf[j] - '0';
            }
            memcpy(attr->vsaAlzParam.analyzeParamSparkLink.SyncSeq, TempBuf, stdLenTab[0] * sizeof(int));
        }



        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "analyzeParamSparkLink.SyncSeq =" << attr->vsaAlzParam.analyzeParamSparkLink.SyncSeq << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);

}
//sle分析参数}

//wisun分析参数

scpi_result_t SetVsaWiSUNAlzOFDMOption(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 3 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.Mr_OFDM_Option = ParamVal;

        cout << "AnalyzeParamWiSun.Mr_OFDM_Option =" << attr->vsaAlzParam.analyzeParamWiSun.Mr_OFDM_Option << endl;
    } while (0);

    return SCPI_ResultOK(context);

}

scpi_result_t SetVsaWiSUNAlzPhyOFDMInterLeaving(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 1 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.Phy_OFDM_Interleaving = ParamVal;

        cout << "AnalyzeParamWiSun.Phy_OFDM_Interleaving =" << attr->vsaAlzParam.analyzeParamWiSun.Phy_OFDM_Interleaving << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsaWiSUNAlzOQPSKFreqBand(scpi_t * context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 17 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.FreqBand = ParamVal;

        cout << "AnalyzeParamWiSun.FreqBand =" << attr->vsaAlzParam.analyzeParamWiSun.FreqBand << endl;
    } while (0);

    return SCPI_ResultOK(context);

}

scpi_result_t SetVsaWiSUNAlzFSKDataRate(scpi_t * context)
{
    int ParamVal = 100000;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(2400 > ParamVal || 600000 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.DataRate = ParamVal;

        cout << "AnalyzeParamWiSun.DataRate =" << attr->vsaAlzParam.analyzeParamWiSun.DataRate << endl;
    } while (0);

    return SCPI_ResultOK(context);

}

scpi_result_t SetVsaWiSUNAlzFSKAcpCalMode(scpi_t * context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0 > ParamVal || 1 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.AcpCalMode = ParamVal;

        cout << "AnalyzeParamWiSun.AcpCalMode =" << attr->vsaAlzParam.analyzeParamWiSun.AcpCalMode << endl;
    } while (0);

    return SCPI_ResultOK(context);

}

scpi_result_t SetVsaWiSUNAlzFSKChannelSpacing(scpi_t * context)
{
    int ParamVal = 100000;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(12500 > ParamVal || 600000 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.ChannelSpacing = ParamVal;

        cout << "AnalyzeParamWiSun.ChannelSpacing =" << attr->vsaAlzParam.analyzeParamWiSun.ChannelSpacing << endl;
    } while (0);

    return SCPI_ResultOK(context);

}

scpi_result_t SetVsaWiSUNAlzFSKModulationIndex(scpi_t * context)
{
    double ParamVal = 0.5;
    do
    {
        if (!SCPI_ParamDouble(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(0.33 > ParamVal || 2 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamWiSun.ModulationIndex = ParamVal;

        cout << "AnalyzeParamWiSun.ModulationIndex =" << attr->vsaAlzParam.analyzeParamWiSun.ModulationIndex << endl;
    } while (0);

    return SCPI_ResultOK(context);

}
//wisun分析参数

// wisun结果
scpi_result_t GetVsaWiSUNPHRCRCCheck(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_PHR_CRC_CHECK);
}

scpi_result_t GetVsaWiSUNPHRRateField(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_PHR_MCS);
}

scpi_result_t GetVsaWiSUNPHRFrameLength(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_PHR_FRAME_LENGTH);
}

scpi_result_t GetVsaWiSUNPHRScrambler(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_PHR_SCRAMBLER);
}

scpi_result_t GetVsaWiSUNPHRBit(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_WISUN_PHR_BIT);
}

scpi_result_t GetVsaWiSUNPHRCRCBit(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_WISUN_PHR_CRC_BIT);
}

scpi_result_t GetVsaWiSUNDataRate(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_WISUN_DATA_RATE);
}

scpi_result_t GetVsaWiSUNBW(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_WISUN_BW);
}

scpi_result_t GetVsaWiSUNSymbolCount(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_SYMBOL_COUNT);
}

scpi_result_t GetVsaWiSUNCodingRate(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_CODING_RATE);
}

scpi_result_t GetVsaWiSUNModulationType(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_MODULATION_TYPE);
}

scpi_result_t GetVsaWiSUNPSDULength(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_PSDU_LENGTH);
}

scpi_result_t GetVsaWiSUNPSDUCRC(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WISUN_PSDU_CRC);
}

scpi_result_t GetVsaWiSUNFSKEyePoint(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WISUN_FSK_EYE, true);
}

scpi_result_t GetVsaWiSUNFSKShrSfdBits(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_WISUN_FSK_SHR_SFD_BIT,true);
}

scpi_result_t GetVsaWiSUNFSKPhrBits(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_WISUN_FSK_PHR_BIT,true);
}

scpi_result_t GetVsaWiSUNFSKPhyPayloadBits(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_WISUN_FSK_PHY_PAYLOAD_BIT,true);
}

scpi_result_t GetVsaWiSUNFSKCrcBits(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_WISUN_FSK_CRC_BIT,true);
}

scpi_result_t GetVsaWiSUNAcpMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WSUN_FSK_SPEC_ACP_MASK, true);
}

scpi_result_t GetVsaWiSUNAcpPwrData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WSUN_FSK_SPEC_ACP_Y, true);
}

scpi_result_t GetVsaWiSUNAcpChannelData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WSUN_FSK_SPEC_ACP_X, true);
}

scpi_result_t GetVsaWiSUNAcpWidth(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_WSUN_FSK_SPEC_ACP_WIDTH);
}

scpi_result_t GetVsaWiSUNAcpValidnum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_WSUN_FSK_SPEC_ACP_VALID_NUM);
}

scpi_result_t GetVsaWiSUNPhaseErrorVsChip(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WISUN_PHASE_ERR_VS_CHIP, true);
}

scpi_result_t GetVsaWiSUNEyeRealGraphic(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WISUN_EYE_REAL, true);
}

scpi_result_t GetVsaWiSUNEyeImagGraphic(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WISUN_EYE_IMAG, true);
}

scpi_result_t GetVsaWiSUNPSDUBit(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_OK;
    int bufLen = 0;
    unique_ptr<char[]> buf_bit = nullptr;
    iRet = WT_GetVectorResultElementCount(attr->ConnID, WT_RES_EXPORT_PSDU_BIT, (u32 *)&bufLen);
    IF_ERR_RETURN(iRet);
    buf_bit.reset(new char[bufLen]);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_EXPORT_PSDU_BIT, buf_bit.get(), (u32)bufLen);
    IF_ERR_RETURN(iRet);

    SCPI_ResultArbitraryBlock(context, (char *)(buf_bit.get()), bufLen);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaWiSUNPSDUBitLength(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_OK;
    int bufLen = 0;
    unique_ptr<char[]> buf_bit = nullptr;
    iRet = WT_GetVectorResultElementCount(attr->ConnID, WT_RES_EXPORT_PSDU_BIT, (u32 *)&bufLen);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, bufLen);
    return SCPI_RES_OK;
}
// wisun结果

// ZWAVe 分析参数
scpi_result_t GetVsaZWAVeDataRate(scpi_t * context)
{
    int ParamVal = 1;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        ILLEGAL_PARAM_RETURN(1 > ParamVal || 3 < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParamZWave.ZwaveRate = ParamVal;

        cout << "analyzeParamZWave.ZwaveRate =" << attr->vsaAlzParam.analyzeParamZWave.ZwaveRate << endl;
    } while (0);

    return SCPI_ResultOK(context);

}
// ZWAVe 分析参数

// ZWAVe 结果
scpi_result_t GetVsaZWAVeEyeGraphic(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_ZWAVE_EYE, true);
}

// ZWAVe 结果
scpi_result_t SetVsaNoiseCalibrationStart(scpi_t *context)
{
    int ParamVal = 0;
    int iRet = WT_ERR_CODE_OK;
    int PortList[8] = {0};
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        for (int i = 0; i < 8; i++)
        {
            if (!SCPI_ParamInt(context, &ParamVal, false))
            {
                return SCPI_RES_ERR;
            }
            if (ParamVal < 0 || ParamVal > 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            PortList[i] = ParamVal;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"PortList["<<i<<"] = "<<PortList[i]<<std::endl;
        }
        iRet = WT_SetVsaNoiseCalibrationStart(attr->ConnID, PortList);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaNoiseCalibrationStop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"SetVsaNoiseCalibrationStop"<<std::endl;
        iRet = WT_SetVsaNoiseCalibrationStop(attr->ConnID);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaNoiseCalibrationStatus(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Status = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"GetVsaNoiseCalibrationStatus"<<std::endl;
        iRet = WT_GetVsaNoiseCalibrationStatus(attr->ConnID, Status);
    } while (0);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Status);
    return SCPI_RES_OK;
}

scpi_result_t SetVsaNoiseCalSelfStart(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        int ForceStart = true;
        SCPI_ParamInt(context, &ForceStart, false);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"SetVsaNoiseCalSelfStart ForceStart=" << ForceStart <<std::endl;

        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_SNC_CAL_SELF_START;
        SubCmd.SendBuf = reinterpret_cast<char *>(&ForceStart);
        SubCmd.SendDataLen = sizeof(int);
        SubCmd.RecvBuf = nullptr;
        SubCmd.RecvBufLen = 0;
        SubCmd.RecvTimeOut = 6000;
        iRet = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaNoiseCalSelfStop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"SetVsaNoiseCalSelfStop"<<std::endl;
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_SNC_CAL_SELF_STOP;
        SubCmd.SendBuf = nullptr;
        SubCmd.SendDataLen = 0;
        SubCmd.RecvBuf = nullptr;
        SubCmd.RecvBufLen = 0;
        iRet = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsaNoiseCalSelfStatus(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Status = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"GetVsaNoiseCalSelfStatus"<<std::endl;
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_SNC_CAL_SELF_QUERY;
        SubCmd.SendBuf = nullptr;
        SubCmd.SendDataLen = 0;
        SubCmd.RecvBuf = reinterpret_cast<char*>(&Status);
        SubCmd.RecvBufLen = sizeof(Status);
        iRet = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, Status);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaNoiseCalibrationPortValid(scpi_t *context)
{

    int iRet = WT_ERR_CODE_OK;
    int Status[8][8] = {0};
    int TesterCount = 1;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        iRet = WT_GetVsaNoiseCalibrationPortValid(attr->ConnID, Status, TesterCount);
        for(int j = 0; j < TesterCount;j++)
        {
            for (int i = 0; i < 8; i++)
            {
                SCPI_ResultInt(context, Status[j][i]);
            }
        }
    } while (0);
    IF_ERR_RETURN(iRet);

    return SCPI_RES_OK;
}

scpi_result_t SetVsaExtendEVMStatus(scpi_t *context)
{
    enum ExtendEVM
    {
        Extend_EVM_NORMAL,
        Extend_EVM_VECTORAVG,
        Extend_EVM_DUALVSA,
        Extend_EVM_NOISECOMPENSATION,
    };
    int iRet = WT_ERR_CODE_OK;
    int Status = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamInt(context, &Status, true))
        {
            return SCPI_RES_ERR;
        }
        if (Status < Extend_EVM_NORMAL || Status > Extend_EVM_NOISECOMPENSATION)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if(Status != Extend_EVM_VECTORAVG)
        {
            attr->vsaAlzParam.commonAnalyzeParam.AvgTimes = 0;
        }
        iRet = WT_SetVsaExtendEVMStatus(attr->ConnID, Status);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Get3GPPAverageResultARB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    Vsa3GPPCommResult Result[ArrCnt];
    Alg_3GPP_AlzSlotNBIOT Result2[ArrCnt];
    memset((char *)Result, 0, sizeof(Vsa3GPPCommResult) * ArrCnt);
    memset((char *)Result2, 0, sizeof(Alg_3GPP_AlzSlotNBIOT) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_Get3GPPAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if(Result[0].Standard == ALG_3GPP_STD_NB_IOT)
    {
        memcpy(&Result2[0], &(Result[0].NBIOT), sizeof(Alg_3GPP_AlzSlotNBIOT));
        memcpy(&Result2[1], &(Result[1].NBIOT), sizeof(Alg_3GPP_AlzSlotNBIOT));
        memcpy(&Result2[2], &(Result[2].NBIOT), sizeof(Alg_3GPP_AlzSlotNBIOT));
    }
    if (iRet == WT_ERR_CODE_OK)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Result2, sizeof(Vsa3GPPCommResult) * ArrCnt);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}

scpi_result_t Set3GPPAverageSetting(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int AvgType = 0;
        int AvgCount = 1; // 平均次数为1时，认为不开启平均~
        int AvgMethod = 0;
        // 平均开关on or off
        if (!SCPI_ParamInt(context, &AvgType, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (AvgType < 0 || AvgType > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (AvgType == 1)
        {
            AvgMethod = WT_SEGMENT_MOVING_AVERAGE;
            if (!SCPI_ParamInt(context, &AvgCount, true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            if (AvgCount < 1 || AvgCount > 120)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        else
        {
            AvgMethod = WT_SEGMENT_MOVING_AVERAGE;
            AvgCount = 1;  // 平均次数为1，即没有开启平均
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->avgParam.AvgCount = AvgCount;
        attr->avgParam.AvgType = AvgMethod;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(attr->avgParam.AvgCount) << Pout(attr->avgParam.AvgType) << endl;
        iRet = WT_SetVSAAverageParameter(attr->ConnID, &attr->avgParam);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Get3GPPAverageResult(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    int ArrCnt = 3;
    int streamID = 0;
    int segmentID = 0;

    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    Vsa3GPPCommResult Result[ArrCnt];
    memset((char *)Result, 0, sizeof(Vsa3GPPCommResult) * ArrCnt);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_Get3GPPAverageResult(attr->ConnID, &Result[0], &Result[1], &Result[2], 0, streamID, segmentID);

    if (iRet == WT_ERR_CODE_OK)
    {
        if(Result[0].Standard == ALG_3GPP_STD_NB_IOT)
        {  //平均值
            SCPI_ResultDouble(context, Result[0].NBIOT.FreqErr);
            SCPI_ResultDouble(context, Result[0].NBIOT.IQOffset);
            SCPI_ResultDouble(context, Result[0].NBIOT.OBW);
            SCPI_ResultDouble(context, Result[0].NBIOT.TxPower);
            SCPI_ResultDouble(context, Result[0].NBIOT.PeakPower);
            SCPI_ResultDouble(context, Result[0].NBIOT.SCPower);
            SCPI_ResultDouble(context, Result[0].NBIOT.RSRP);
            SCPI_ResultDouble(context, Result[0].NBIOT.RSSI);
            SCPI_ResultDouble(context, Result[0].NBIOT.RSRQ);
            SCPI_ResultDouble(context, Result[0].NBIOT.SNR);
            SCPI_ResultDouble(context, Result[0].NBIOT.RmsEvmPCT);
            SCPI_ResultDouble(context, Result[0].NBIOT.PeakEvmPCT);
            SCPI_ResultDouble(context, Result[0].NBIOT.DmrsEvmPCT);
            SCPI_ResultDouble(context, Result[0].NBIOT.MagnErrRms);
            SCPI_ResultDouble(context, Result[0].NBIOT.MagnErrPeak);
            SCPI_ResultDouble(context, Result[0].NBIOT.MagnErrDmrs);
            SCPI_ResultDouble(context, Result[0].NBIOT.PhaseErrRms);
            SCPI_ResultDouble(context, Result[0].NBIOT.PhaseErrPeak);
            SCPI_ResultDouble(context, Result[0].NBIOT.PhaseErrDmrs);

            //最大值
            SCPI_ResultDouble(context, Result[1].NBIOT.FreqErr);
            SCPI_ResultDouble(context, Result[1].NBIOT.IQOffset);
            SCPI_ResultDouble(context, Result[1].NBIOT.OBW);
            SCPI_ResultDouble(context, Result[1].NBIOT.TxPower);
            SCPI_ResultDouble(context, Result[1].NBIOT.PeakPower);
            SCPI_ResultDouble(context, Result[1].NBIOT.SCPower);
            SCPI_ResultDouble(context, Result[1].NBIOT.RSRP);
            SCPI_ResultDouble(context, Result[1].NBIOT.RSSI);
            SCPI_ResultDouble(context, Result[1].NBIOT.RSRQ);
            SCPI_ResultDouble(context, Result[1].NBIOT.SNR);
            SCPI_ResultDouble(context, Result[1].NBIOT.RmsEvmPCT);
            SCPI_ResultDouble(context, Result[1].NBIOT.PeakEvmPCT);
            SCPI_ResultDouble(context, Result[1].NBIOT.DmrsEvmPCT);
            SCPI_ResultDouble(context, Result[1].NBIOT.MagnErrRms);
            SCPI_ResultDouble(context, Result[1].NBIOT.MagnErrPeak);
            SCPI_ResultDouble(context, Result[1].NBIOT.MagnErrDmrs);
            SCPI_ResultDouble(context, Result[1].NBIOT.PhaseErrRms);
            SCPI_ResultDouble(context, Result[1].NBIOT.PhaseErrPeak);
            SCPI_ResultDouble(context, Result[1].NBIOT.PhaseErrDmrs);

            //最小值
            SCPI_ResultDouble(context, Result[2].NBIOT.FreqErr);
            SCPI_ResultDouble(context, Result[2].NBIOT.IQOffset);
            SCPI_ResultDouble(context, Result[2].NBIOT.OBW);
            SCPI_ResultDouble(context, Result[2].NBIOT.TxPower);
            SCPI_ResultDouble(context, Result[2].NBIOT.PeakPower);
            SCPI_ResultDouble(context, Result[2].NBIOT.SCPower);
            SCPI_ResultDouble(context, Result[2].NBIOT.RSRP);
            SCPI_ResultDouble(context, Result[2].NBIOT.RSSI);
            SCPI_ResultDouble(context, Result[2].NBIOT.RSRQ);
            SCPI_ResultDouble(context, Result[2].NBIOT.SNR);
            SCPI_ResultDouble(context, Result[2].NBIOT.RmsEvmPCT);
            SCPI_ResultDouble(context, Result[2].NBIOT.PeakEvmPCT);
            SCPI_ResultDouble(context, Result[2].NBIOT.DmrsEvmPCT);
            SCPI_ResultDouble(context, Result[2].NBIOT.MagnErrRms);
            SCPI_ResultDouble(context, Result[2].NBIOT.MagnErrPeak);
            SCPI_ResultDouble(context, Result[2].NBIOT.MagnErrDmrs);
            SCPI_ResultDouble(context, Result[2].NBIOT.PhaseErrRms);
            SCPI_ResultDouble(context, Result[2].NBIOT.PhaseErrPeak);
            SCPI_ResultDouble(context, Result[2].NBIOT.PhaseErrDmrs);
        }
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_ResultOK(context, iRet);
    }
}
//矢量平均
scpi_result_t SetVsaIterativeEVMStatus(scpi_t *context)
{
    enum IterativeEVM
    {
        Iterative_EVM_OFF,
        Iterative_EVM_ON,
    };
    int iRet = WT_ERR_CODE_OK;
    int Status = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamInt(context, &Status, true))
        {
            return SCPI_RES_ERR;
        }
        if (Status < Iterative_EVM_OFF || Status > Iterative_EVM_ON)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Status == Iterative_EVM_OFF)
        {
            attr->vsaAlzParam.commonAnalyzeParam.AvgTimes = 0;
        }
        iRet = WT_SetVsaIterativeEVMStatus(attr->ConnID, Status);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

//噪声补偿
scpi_result_t SetVsaSncEVMStatus(scpi_t *context)
{
    enum SncEVM
    {
        SNC_EVM_OFF,
        SNC_EVM_ON,
    };
    int iRet = WT_ERR_CODE_OK;
    int Status = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamInt(context, &Status, true))
        {
            return SCPI_RES_ERR;
        }
        if (Status < SNC_EVM_OFF || Status > SNC_EVM_ON)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        iRet = WT_SetVsaSncEVMStatus(attr->ConnID, Status);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaCcEVMStatus(scpi_t *context)
{
    enum CcEVM
    {
        CC_EVM_OFF,
        CC_EVM_ON,
    };
    int iRet = WT_ERR_CODE_OK;
    int Status = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamInt(context, &Status, true))
        {
            return SCPI_RES_ERR;
        }
        if (Status < CC_EVM_OFF || Status > CC_EVM_ON)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        iRet = WT_SetVsaCcEVMStatus(attr->ConnID, Status);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}
