#ifndef __DIG_TASK_H__
#define __DIG_TASK_H__
#include <unistd.h>
#include <string>
#include <sys/types.h>
#include <conf.h>
#include <memory>
#include <set>
#include <atomic>
#include <vector>
#include <list>

#include <sys/time.h>

#include "wtev++.h"
#include "devtype.h"
#include "digstruct.h"
#include "digethernet.h"

// 数字IQ控制
class DigTaskLib
{
public:
    DigTaskLib();
    ~DigTaskLib();

    //*****************************************************************************
    // 设置DST MAC
    // 参数[IN] : MacArray：MAC数组
    // 返回值: 无
    //*****************************************************************************
    void SetDstMac(uint8_t *MacArray);

    //*****************************************************************************
    // 设置发送参数
    // 参数[IN] : Config:VSG配置参数;
    // 返回值: 成功或错误码
    //*****************************************************************************
    int VSGSetParam(VsgDigConfigType Config);

    //*****************************************************************************
    // 设置待发送的PN数据, 配置一个通道的全部发送任务的数据
    // 参数[IN] : PnItemVector:待发送的数据； ChannelId:MIMO时的Channel Id
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetPNItem(const std::vector<DigPnItem> &PnItemVector, int ChannelId);

    //*****************************************************************************
    // 设置待发送的PN数据, 配置一个发送任务的全部通道的数据,保存到硬盘
    // 参数[IN] : PnItemVector:待发送的数据
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetPNItemHardDisk(const std::vector<DigPnItem> &PnItemVector, int FrameId);

    //*****************************************************************************
    // 设置接收参数
    // 参数[IN] : Config:VSA配置参数;
    // 返回值: 成功或错误码
    //*****************************************************************************
    int VSASetParam(VsaDigConfigType Config);

    //*****************************************************************************
    // 获取VSA单通道采集到是数据长度
    // 返回值: 数据长度
    //*****************************************************************************
    int VSAGetChannelDataLen();

    //*****************************************************************************
    // 获取接收到的数据
    // 参数[OUT] : pBuf:拷贝数据的目标地址
    // 参数[IN] : Size:单流要拷贝的数据长度；ChannelId：数据Channel序号；
    // 参数[IN] : ChannelTotal:数据Channel总数；
    // 返回值: 成功或错误码
    //*****************************************************************************
    int CaptureData(void *pBuf, uint32_t Size, int ChannelId, int ChannelTotal);

    //*****************************************************************************
    // 开始接受、发送数据
    // 参数[IN] : Type：业务类型，Interface：交互模式重新启动
    // 返回值: 成功或错误码
    //*****************************************************************************
    int Start(int Type);

    //*****************************************************************************
    // 停止发送数据
    // 参数[IN] : Type：业务类型
    // 返回值: 成功或错误码
    //*****************************************************************************
    int Stop(int Type);

    //*****************************************************************************
    // 获取运行状态
    // 参数[IN] : Type：业务类型
    // 返回值: 运行状态
    //*****************************************************************************
    int GetStatus(int Type);

    //*****************************************************************************
    // 获取当前VSG已发送次数
    // 参数[IN] : 无
    // 返回值: VSG已发送次数
    //*****************************************************************************
    int GetVsgSendCnt() { return m_SendCnt; };

    //*****************************************************************************
    // 获取TBT AP模式的SIFS结果，单位 秒
    // 参数[IN] : FrameId: 帧ID
    // 参数[OUT] : SIFS: TBT AP模式的SIFS结果
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetTBTApSIFS(std::vector<double> &SIFS);

    int GetVsaMaxBitsCnt(void) { return m_VsaDigConfig.MaxBitCnt; };
    int GetVsgMaxBitsCnt(void) { return m_VsgDigConfig.MaxBitCnt; };

    int GetPacketCnt(int Type) { return m_PacketCnt[Type]; };
private:

    //*****************************************************************************
    // 停止正在允许的DIG单元
    // 参数[IN] : Type：业务类型
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StopRunning(int Type);

    int SendData(int FrameId, int StartMode, int ResetZone, int ClearZone);
    int RecvData(int FrameId, int StartMode, int ResetZone);
    void Notify(int StartMode, int Status);
    int CheckChannelList(int Type);

    void SaveRecvData(int FrameId);
    void ReadSendData(int FrameId);

    struct TaskType
    {
        int Direction; // SEND_TASK:send, RECV_TASK:Recv
        int FrameId;   // 当前交互的帧ID
        int Status;    // 状态
        TaskType() : Status(WT_DIGTIAL_STATUS_RUNNING) {}
    };

    struct DigTaskType
    {
        int CurStep;                // 当前执行的步骤
        int LastDirection;          // 上一次的数据方向
        int StartMode;              // 启动模式
        int LoopCnt;                // 循环次数
        int Status;                 // 状态
        std::vector<TaskType> Task; // 任务
        DigTaskType() : CurStep(0), LastDirection(-1), StartMode(-1), LoopCnt(1), Status(WT_DIGTIAL_STATUS_RUNNING) {}
        bool operator==(const DigTaskType &DigTask) { return this->StartMode == DigTask.StartMode; }
    };

    void DoTaskThread(DigTaskType Task);

    enum
    {
        DIQ_RUNNING,       // VSA或VSG运行中
        DIQ_RUNNING_INTER, // 交互模式运行中
        DIQ_STOP,          // 停止
    };

    enum
    {
        DIQ_SEND_DATA_MEMORY,    // 待发送数据存在硬盘
        DIQ_SEND_DATA_HARD,      // 待发送数据存在内存
    };

    enum
    {
        RECV_TASK = DEV_TYPE_VSA,
        SEND_TASK = DEV_TYPE_VSG,
    };

    struct DIQPnData
    {
        SendPnList PnData;
        int IfgMode;                                          // IFG模式
        int TotalPacketMax;                                   // 随机IFG最大值
        int TotalPacketMin;                                   // 随机IFG最小值
        volatile int Ready;                                   // 数据是否准备好
        double VsaVsgDelay;                                   // VSA-VSG，SIFS，单位秒
        DIQPnData() : IfgMode(FIXED_IFG_MODE), Ready(true) {}
    };

    struct DIQPnDataSave
    {
        unsigned long ChannelTotal;                             // 通道总数
        unsigned long DataOffset[MAX_NUM_OF_CHANNEL]; // 数据地址
        unsigned long PacketCnt[MAX_NUM_OF_CHANNEL];  // 网络包数
        unsigned long DataLen[MAX_NUM_OF_CHANNEL];    // 数据长度
    };

    struct DIQHardDiskData
    {
#define MAX_READY_FRAME 3
        int PnSaveMode;
        std::vector<int> ReadyFrame; // 已经读取数据的帧
    };

    std::atomic<int> m_Active[MOD_TYPE_COUNT]; // 当前活动状态
    int m_SetZone[MOD_TYPE_COUNT];             // EthernetLib是否运行中，用于判断STOP时是否需通知EthernetLib
    int m_PacketCnt[MOD_TYPE_COUNT] = {0};     // 网络包计数
    uint8_t m_SrcMac[6];                       // 发送数据源MAC地址
    uint8_t m_DstMac[6];                       // 发送数据目标MAC地址
    int m_Convert = false;                     //是否翻转数据
    
    // VSG相关成员
    std::list<DIQPnData> m_SendPnData; // 待发送的Pn数据列表
    volatile uint32_t m_SendCnt = 0;   // 已发送次数
    std::atomic<int> m_SendStatus;     // 发送状态
    VsgDigConfigType m_VsgDigConfig;   // DIG相关配置      
    DIQHardDiskData m_PnSaveMode;      // Pn数据存储模式, 0:提前存在m_SendPnData，1:存在硬盘，发送前读取    

    // VSA相关成员
    std::list<ChannelPnList> m_RecvPnData; // 接收到的PN数据
    std::atomic<int> m_RecvStatus;         // 接收状态
    VsaDigConfigType m_VsaDigConfig;       // DIG相关配置

    // 交互模式相关成员
    struct timeval m_SIFSStart;             // 交互模式SIFS计时
    struct timeval m_SIFSEnd;               // 交互模式SIFS计时
    struct timeval m_StartTime;             // 交互模式启动时间
    std::vector<double> m_TBTApSIFS;        // TBT AP SIFS
    
};
#endif