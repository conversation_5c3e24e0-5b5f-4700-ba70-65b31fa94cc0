//*****************************************************************************
//  File: conf.h
//  配置文件解析
//  Data: 2016.7.21
//*****************************************************************************

#include "conf.h"
#include <unistd.h>
#include <cstdlib>
#include <sstream>
#include <iostream>
#include <cstring>
#include <sys/stat.h>
#include "wterror.h"
#include "devlib/devlib.h"
#include "wtlog.h"


using namespace std;

long long DynamicPasswd::GetDynamicPasswd()
{
    string CurDir = WTConf::GetDir();
    struct stat FileStat;
    if (stat((CurDir + "/WT-Manager").c_str(), &FileStat) != 0)
    {
        return 0;
    }
    long long ManagerTime = FileStat.st_mtime;

    if (stat((CurDir + "/WT-Drv.ko").c_str(), &FileStat) != 0)
    {
        return 0;
    }

    long long DrvTime = FileStat.st_mtime;
    return ((ManagerTime ^ DrvTime) ^ 0xF0F0FF00F0F0FF00);
}

string WTConf::GetDir(void)
{
    char Buf[1024];
    int Cnt = readlink("/proc/self/exe", Buf, 1024);
    while (Cnt--)
    {
        if (Buf[Cnt] == '/')
        {
            Buf[Cnt] = '\0';
            break;
        }
    }

    return Buf;
}

// ‘#’开头的为注释，条目格式为 : item = val
int WTConf::GetItemVal(const string &Item, string &Val, int pos)
{
    std::unique_lock<std::mutex> Lock(m_Mutex);
    if (!m_FStream.is_open())
    {
        return WT_CONF_FILE_ERROR;
    }

    if (m_FStream.fail())
    {
        m_FStream.clear();
    }
    m_FStream.seekg(pos);

    string Line;
    string::size_type Begin, End = string::npos;

    while (getline(m_FStream, Line))
    {
        // 跳过空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            continue;
        }

        Begin = Line.find(Item, Begin);
        if (Begin == string::npos)
        {
            continue;
        }

        End = Line.find_first_not_of("= ", Begin + Item.length());
        break;
    }

    if (End == string::npos)
    {
        Val.clear();
        return WT_CONF_ITEM_ERROR;
    }

    Val = Line.substr(End, (Line.find_last_not_of(" #\t\f\v\n\r") - End + 1));

    return WT_OK;
}

int WTConf::SetItemVal(const std::unordered_map<std::string, std::string> &CfgMap)
{
    std::unique_lock<std::mutex> Lock(m_Mutex);
    if (!m_FStream.is_open())
    {
        return WT_CONF_FILE_ERROR;
    }

    if (m_FStream.fail())
    {
        m_FStream.clear();
    }
    m_FStream.seekg(0);
    stringstream NewStrs;
    string Line;
    string::size_type Begin;

    // 丢弃原配置
    while (getline(m_FStream, Line))
    {
        // 空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            NewStrs << Line << endl;
            continue;
        }

        bool ChangeFlag = false;
        for (auto &Iter : CfgMap)
        {
            // 替换信息
            if (Line.find(Iter.first, Begin) != string::npos && Iter.second.size() > 0)
            {
                NewStrs << Iter.first.c_str() << " = " << Iter.second.c_str() << endl;
                ChangeFlag = true;
                break;
            }
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << __FUNCTION__  << Line.c_str() << std::endl;
        if (ChangeFlag)
        {
            continue;
        }

        NewStrs << Line << endl;
    }

    if (NewStrs.str().empty())
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << __FUNCTION__  << " NewStrs is empty" << std::endl;
        return -1;
    }
    // 保存数据到文件
    m_FStream.close();

    ofstream OutFile(m_ConfFile.GetFileName(), fstream::out | fstream::trunc);
    OutFile << NewStrs.str();
#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << NewStrs.str() << endl;
#endif
    OutFile.close();
    m_ConfFile.Reset();
    m_FStream.open(m_ConfFile.GetDecryptName());
    return WT_OK;
}

// ‘#’开头的为注释，条目格式为 : item = val
int WTConf::GetItemVal(const string &Item, int &Val, int pos)
{
    int Ret = WT_OK;
    string Str;

    Ret = GetItemVal(Item, Str, pos);
    if (Ret == WT_OK)
    {
        try
        {
            Val = stoul(Str, nullptr, 0);
        }
        catch (const std::exception &e)
        {
            std::cerr << __FUNCTION__ << __LINE__ << e.what() << '\n';
            Ret = WT_ARG_ERROR;
        }
    }
    return Ret;
}

int DevConf::GetTcpPort(int &Port)
{
    return GetItemVal("port", Port);
}

int DevConf::GetInterface(int DevType, string &Interface)
{
    if (DevType == INTERFACE_DEV_TYPE_WT448_WT418)
    {
        return GetItemVal("interface", Interface);
    }
    else
    {
        return GetItemVal("interface_wt428", Interface);
    }
}

int DevConf::GetALLGUIString(std::stringstream &OStr)
{
    if (!m_FStream.is_open())
    {
        return WT_CONF_FILE_ERROR;
    }

    string Line;
    string::size_type Begin;

    if (m_FStream.fail())
    {
        m_FStream.clear();
    }
    m_FStream.seekg(0);

    // 丢弃原配置
    while (getline(m_FStream, Line))
    {
        // 空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            continue;
        }
        Begin = Line.find("GUI_", Begin);
        if (Begin == string::npos)
        {
            continue;
        }
        OStr << Line << endl;
    }
    return WT_OK;
}

int DevConf::GetGUIVersion(std::vector<std::string> &GUIType, std::vector<std::string> &GUIVer)
{
    if (!m_FStream.is_open())
    {
        return WT_CONF_FILE_ERROR;
    }

    string Line;
    string::size_type Begin, TypeEnd, End = string::npos;
    const string Item = "GUI_";
    string TypeTmp;
    string VerTmp;

    if (m_FStream.fail())
    {
        m_FStream.clear();
    }
    m_FStream.seekg(0);

    // 丢弃原配置
    while (getline(m_FStream, Line))
    {
        // 空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            continue;
        }

        Begin = Line.find(Item, Begin);
        if (Begin == string::npos)
        {
            continue;
        }

        TypeEnd = Line.find(" = ", Begin + Item.length());
        if (TypeEnd == string::npos)
        {
            continue;
        }
        TypeTmp = Line.substr(Begin + Item.length(), TypeEnd - (Begin + Item.length()));

        End = Line.find_first_not_of("= ", TypeEnd);
        if (End != string::npos)
        {
            VerTmp = Line.substr(End, Line.find_first_of(" #", End) - End);
            GUIType.push_back(TypeTmp);
            GUIVer.push_back(VerTmp);
        }
    }
    return WT_OK;
}

int BaseConf::GetDevNum(int &Num)
{
    return GetItemVal("devnum", Num);
}

int BaseConf::SeekSubDev(int DevId, int pos)
{
    std::unique_lock<std::mutex> Lock(m_Mutex);
    if (!m_FStream.is_open())
    {
        return WT_CONF_FILE_ERROR;
    }

    if (m_FStream.fail())
    {
        m_FStream.clear();
    }
    m_FStream.seekg(pos);

    string Line;
    string::size_type Begin;

    while (getline(m_FStream, Line))
    {
        // 跳过空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            continue;
        }

        Begin = Line.find("subdev", Begin);
        if (Begin == string::npos)
        {
            continue;
        }

        if (--DevId == 0)
        {
            break;
        }
    }

    return DevId == 0 ? WT_OK : WT_CONF_ITEM_ERROR;
}

int BaseConf::GetDevCfg(int DevId, DevCfg &Cfg)
{
    int Ret = SeekSubDev(DevId);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    string str;
    std::unique_lock<std::mutex> Lock(m_Mutex);
    int pos = m_FStream.tellg();
    Lock.unlock();

    GetItemVal("portmask", Cfg.PortMask, pos);
    GetItemVal("vsamask", Cfg.VsaMask, pos);
    GetItemVal("vsgmask", Cfg.VsgMask, pos);

    return WT_OK;
}

void BaseConf::SetDevNum(int Num, stringstream &DstStrs)
{
    string Line;
    string::size_type Begin;
    std::unique_lock<std::mutex> Lock(m_Mutex);
    while (getline(m_FStream, Line))
    {
        // 不符合替换目标的直接复制到新stream中
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            DstStrs << Line << endl;
            continue;
        }

        // 先替换devnum值
        Begin = Line.find("devnum", Begin);
        if (Begin != string::npos)
        {
            DstStrs << "devnum = " << Num << endl;
            break;
        }

        DstStrs << Line << endl;
    }
}

void BaseConf::StripSubdev(stringstream &DstStrs)
{
    bool DevFlag = false; // 标注当前是否处于subdev的配置区间
    string Line;
    string::size_type Begin;

    std::unique_lock<std::mutex> Lock(m_Mutex);
    // 丢弃原subdev配置
    while (getline(m_FStream, Line))
    {
        // 空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            if (!DevFlag)
            {
                DstStrs << Line << endl;
            }

            continue;
        }

        if (DevFlag)
        {
            Begin = Line.find_first_of('}', Begin);
            if (Begin != string::npos)
            {
                DevFlag = false;
            }

            continue;
        }

        Begin = Line.find("subdev", Begin);
        if (Begin != string::npos)
        {
            DevFlag = true;
            continue;
        }

        DstStrs << Line << endl;
    }
}

int BaseConf::SetDevCfg(const vector<DevCfg> &Cfg)
{
    std::unique_lock<std::mutex> Lock(m_Mutex);
    if (!m_FStream.is_open())
    {
        return WT_CONF_FILE_ERROR;
    }

    if (m_FStream.fail())
    {
        m_FStream.clear();
    }
    m_FStream.seekg(0);

    Lock.unlock();
    stringstream NewStrs;
    SetDevNum(Cfg.size(), NewStrs);

    // 写入新的子仪器配置
    for (const auto &Conf : Cfg)
    {
        NewStrs << "subdev {" << endl
                << "    portmask = 0x" << hex << Conf.PortMask << endl
                << "    vsamask = 0x" << Conf.VsaMask << endl
                << "    vsgmask = 0x" << Conf.VsgMask << endl
                << "}" << endl;
    }

    StripSubdev(NewStrs);

    if (NewStrs.str().empty())
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << __FUNCTION__ << " NewStrs is empty" << std::endl;
        return -1;
    }

     Lock.lock();
    // 保存数据到文件
    m_FStream.close();

    ofstream OutFile(m_ConfFile.GetFileName(), fstream::out | fstream::trunc);
    OutFile << NewStrs.str();
    OutFile.close();

    m_ConfFile.Reset();
    m_FStream.open(m_ConfFile.GetDecryptName());
    return WT_OK;
}

int BaseConf::SetNetCfg(char *IP, char *GateWay, char *MAC, char *NetMask, char *DevName)
{
    //std::unique_lock<std::mutex> ErrLock(m_Mutex);

    std::unordered_map<std::string, std::string> CfgMap;
    CfgMap.insert(make_pair("IP", IP));
    CfgMap.insert(make_pair("Gateway", GateWay));
    CfgMap.insert(make_pair("Mac", MAC));
    CfgMap.insert(make_pair("Mask", NetMask));
    if (DevName != nullptr)
    {
        CfgMap.insert(make_pair("DevName", DevName));
    }
    return SetItemVal(CfgMap);
}

int BaseConf::SetLogFlag(int PrintLogLevel, int SaveLogLevel)
{
    //std::unique_lock<std::mutex> ErrLock(m_Mutex);

    std::unordered_map<std::string, std::string> CfgMap;
    CfgMap.insert(make_pair("PrintLogLevel", to_string(PrintLogLevel)));
    CfgMap.insert(make_pair("SaveLogLevel", to_string(SaveLogLevel)));
    return SetItemVal(CfgMap);
}
