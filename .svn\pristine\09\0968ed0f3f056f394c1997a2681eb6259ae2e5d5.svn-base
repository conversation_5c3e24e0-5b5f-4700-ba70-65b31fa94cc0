#pragma once
using u32 = unsigned int;
using s32 = int;
using u16 = unsigned short;
using s16 = short;
using u8 = unsigned char;
using s8 = char;

#define MAC_LEN 6

#define MAX_TID_COUNT      64
#define MAX_TF_TID_COUNT   16
#define MAX_USERINFO_COUNT 64
#define MAX_PADDING_COUNT  128
#define MAX_STA_INFO_COUNT 64
#define MAX_DATA_LEN       18000  //20220511 11454->18000
#define MAX_DATARATE_COUNT 8
#define MAX_EXT_DATARATE_COUNT 255
#define MAX_COUNTRY_INFO_COUNT 83
#define MAX_CHANNEL_MAP_COUNT 64
#define MAX_USERINPUT_PREAMBLE_LEN  8
#define MAX_USERINPUT_FRAMEBODY_LEN  (1024*8)
#define MAX_AMPDU_LENGTH (6500631)

#define MAX_MAC_FRAME_COUNT 20480
#define MAX_MSDU_LENGTH  (4096)//2304-->4096,支持错误验证
#define MAX_MSDU_COUNT  (64)
#define MAX_EOF_PADDING_LENGTH (256)
#define MAX_TKIP_TK_LENGTH  (32)
#define MAX_CCMP_TK_LENGTH  (32)
#define MAX_GCMP_TK_LENGTH  (32)
#define MAX_WAPI_TK_LENGTH  (32)
#define MAX_RESERVED_FRAME_USERDEFINE_LENGTH (1024)

enum DataType
{
    DataType_Start = 0,
    ALL0,
    ALL1,
    Repeat01,
    PRBS3,
    PRBS5,
    PRBS7,
    PRBS9,
    PRBS15,
    DataType_End
};

enum RfStandard
{
    IEEE802_11_a_g_OFDM,
    IEEE802_11_b_g_DSSS,
    IEEE802_11_n,
    IEEE802_11_ac,
    IEEE802_11_ax,
    IEEE802_11_be,
    Bluetooth,
    ContinuousWaves
};

enum eSecutityMode
{
    SecutityMode_Disable = 0,
    SecutityMode_WEP,
    SecutityMode_TKIP,
    SecutityMode_CCMP,
    SecutityMode_GCMP,
    SecutityMode_WAPI_OFB,
    SecutityMode_WAPI_GCM,
};
//密码输入方式
enum eKeyInputType
{
    KeyInputType_WPA_PWD = 0, //WAPI 下为WAPI-PWD
    KeyInputType_WPA_PSK,     //WAPI 下为WAPI-BK
    KeyInputType_WPA_TK,      //WAPI 下为WAPI-TK
};

enum eWAPIKeyInputType
{
    KeyInputType_WAPI_PWD = 0, //WAPI 下为WAPI-PWD
    KeyInputType_WAPI_BK,     //WAPI 下为WAPI-BK
    KeyInputType_WAPI_TK,      //WAPI 下为WAPI-TK
};

typedef struct
{
    u8 mac_1[MAC_LEN];
    u8 mac_2[MAC_LEN]; //可选参数
    u8 mac_3[MAC_LEN]; //可选参数
    u8 mac_4[MAC_LEN]; //可选参数
}stMAC_ADDR;

typedef struct
{
    int TODS;
    int FromDS;
    int MoreFlag;
    int Retry;
} stFrameCtrl_Flag1;

typedef struct
{
    int BandwidthIndication;   //3bits B8-B10
    int DynamicIndication;     //1bit B11
    int PowerMgmt;             //1bit B12
    int MoreData;              //1bit B13
    int FlowCtrl;              //1bit B14
    int NextTWTInfoPresent;    //1bit B15
} stFrameCtrl_TACK;



typedef struct
{
    int Version;       //2 bits
    int Type;          //2 bits
    int Subtype;       //4 bits

    stFrameCtrl_TACK TACK_FC;         //8 bits B8-B15 Control Frame TACK Only

    stFrameCtrl_Flag1 flags;          //4 bits except Control Frame Extension
    int ControlFrameExtension[4];     //4 bits Control Frame Extension Only

    int PwrManger;     //1 bits
    int Moredata;      //1 bits
    int ProtectedFrame;//1 bits
    int HTC_Order;     //1 bits
} stFrameCtrl;


#pragma region ControlFrame_BlockAck

typedef struct
{
    int FragmentNum;       //4 bits B0-B3
    int StartingSeqNum;    //12 bits B4-B15
} stBlockAckReq_Basic_SubField;


typedef struct
{
    int Reserved;       //12 bits B0-B11
    int TID;   //4 bits B12-B15
} stBlockAckReq_MultiTID_PerTIDInfo;

typedef struct
{
    int AID11;       //11 bits B0-B10
    int AckType;     //1 bit   B11
    int TID;         //4 bits B12-B15
    //int Reserved;       //12 bits B0-B11 在MultiTID中使用该变量(ax协议)，目前可以不传值，默认为0x000
} stBlockAckReq_MultiTID_PerAID_TIDInfo;

typedef struct
{
    //stBlockAckReq_MultiTID_PerTIDInfo preTIDInfo;   //2 bytes
    stBlockAckReq_MultiTID_PerAID_TIDInfo preTIDInfo; //2 bytes
    int FragmentNum;                                  //4 bits B0-B3
    int StartingSeqNum;                               //12 bits B4-B15
} stBlockAckReq_MultiTID_TIDInfo;

typedef struct
{
    int StartingSeqControl;       //16 bits B0-B15
    u8 GCRGroupAddr[MAC_LEN];     //48 bits B16-B63
} stBlockAckReq_GCR_SubField;


typedef struct
{
    int BarAckPolicy;      //1 bits
    int MultiTID;          //1 bits
    int CompressedBitmap;  //1 bits
    int GCR;               //2 bits 20211221 Modified
    int Reserved;          //7 bits 20211221 Modified
    int TID_INFO;          //4 bits
} stBlockAck_BarControl;

typedef struct
{
    int StartingSeqControl;      //2 bytes
    u8 BlockAckBitmaps[128];     //128 bytes
} stBlockAck_Basic_SubField;

typedef struct
{
    int StartingSeqControl;      //2 bytes
    u8 BlockAckBitmaps[8];       //8 bytes
} stBlockAck_Compressed_SubField;

typedef struct
{
    //stBlockAckReq_MultiTID_PerTIDInfo preTIDInfo;     //2 bytes
    stBlockAckReq_MultiTID_PerAID_TIDInfo preTIDInfo; //2 bytes
    //int StartingSeqControl;                   //2 bytes
    int FragmentNum;                            //4 bits B0-B3
    int StartingSeqNum;                         //12 bits B4-B15
    u8 BlockAckBitmaps[128];                    //8 bytes
    u8 BitmapLen = 8;                           //bitmap长度,计算用,不以传入值为准

    //新增适配MultiSTA AID11等于2045时的变体类型
    u8 Reserved[4];
    u8 RA[6];
} stBlockAck_MultiTID_TIDInfo;//4 bytes


typedef struct
{
    stBlockAckReq_MultiTID_PerTIDInfo preTIDInfo; //2 bytes
                                                //int StartingSeqControl;                   //2 bytes
    int FragmentNum;                            //4 bits B0-B3
    int StartingSeqNum;                         //12 bits B4-B15
} stBlockAck_TF_MultiTID_TIDInfo;//4 bytes

typedef struct
{
    stBlockAck_MultiTID_TIDInfo MultiTIDInfo[MAX_TID_COUNT];
    int len;//此值无意义，废弃
} stBlockAck_MultiTID_SubField;

typedef struct
{
    stBlockAckReq_MultiTID_TIDInfo MultiTIDInfo[MAX_TID_COUNT];
    int len;
} stBlockAckReq_MultiTID_SubField;

#pragma endregion

#pragma region ControlFrame_VHT_NDP

typedef struct
{
    int AID12;                                  //12 bits B0-B11
    int Feedback;                               //1 bits B12
    int NcIndex;                                //3 bits B13-B15
} stSTAInfo;
#pragma endregion
#pragma region ControlFrame_HE_NDP
typedef struct
{
    int RUStartIndex;                          //7 bits B0-B6
    int RUEndIndex;                            //7 bits B7-B13
} stPartialBWInfo;

typedef struct
{
    int AID11;                                  //11 bits B0-B10 //not 2047
    stPartialBWInfo partialBWInfo;              //14 bits B11-B24
    int FeedbackTypeNg;                         //2 bits B25-B26
    int Disambiguation;                         //1 bit B27
    int CodebookSize;                           //1 bit B28
    int NcIndex;                                //3 bits B29-B31
} stSTAInfo_HENot2047;

typedef struct
{
    int AID11;                                  //11 bits B0-B10 //2047
    int DisallowedSubchannelBitmap;             //8 bits B11-B18
    int Reserved1;                              //8 bits B19-B26
    int Disambiguation;                         //1 bit B27
    int Reserved2;                              //4 bits B28-B31
} stSTAInfo_HE2047;

#pragma endregion
#pragma region ControlFrame_EHT_NDP

typedef struct
{
    int Ranging;                                   //1 bits B0     EHT固定为1,HE固定为0
    int HE;                                        //1 bits B1     HE&EHT固定为1
    int TokenNum;                                  //6 bits B2-B7
} stSoundingDialogToken;

typedef struct
{
    int Resolution;                          //1 bit B0
    int FeedbackBitmap;                      //8 bits B1-B8
} stPartialBWInfo_EHT;

typedef struct
{
    int AID11;                                  //11 bits B0-B10 //not 2047
    stPartialBWInfo_EHT partialBWInfo;          //9 bits B11-B19
    int Reserved1;                              //1 bit B20
    int NcIndex;                                //4 bits B21-B24
    int FeedbackTypeNg;                         //2 bits B25-B26
    int Disambiguation;                         //1 bit B27
    int CodebookSize;                           //1 bit B28
    int Reserved2;                              //3 bits B29-B31
} stSTAInfo_EHT;
#pragma endregion
#pragma region ControlFrame_Ext
typedef struct
{
    int BeamformingTraining;                       //1 bits B0
    int IsInitiatorTXSS;                           //1 bits B1
    int IsResponderTXSS;                           //1 bits B2
    int TotalNumberOfSectors;                      //7 bits B3-B9
    int NumberOfRxDMGAntennas;                     //2 bits B10-B11
    int Reserved;                                  //4 bits B12-B15
} stBFControlOfGrant;

typedef struct
{
    int BeamformingTraining;                       //1 bits B0
    int IsInitiatorTXSS;                           //1 bits B1
    int IsResponderTXSS;                           //1 bits B2
    int RXSSLength;                                //6 bits B3-B8
    int RXSSTxRate;                                //1 bits B9
    int Reserved;                                  //6 bits B10-B15
} stBFControl;

typedef struct
{
    int BeamLinkMaintenanceUnitIndex;              //1 bits B0
    int BeamLinkMaintenanceValue;                  //6 bits B1-B6
    int IsMaster;                                  //1 bits B7
} stBeamformedLinkMaintenance;

typedef struct
{
    int TID;                                 //4 bits B0-B3
    int AllocationType;                      //3 bits B4-B6
    int SourceAID;                           //8 bits B7-B14
    int DestinationAID;                      //8 bits B15-B22
    int AllocationDuration;                  //16bits B23-B38
    int Reserved;                            //1 bits B39
} stDynamicAllocationInfo;

typedef struct
{
    int Direction;                          //1 bits B0
    int CDOWN;                              //9 bits B1-B9
    int SectorID;                           //6 bits B10-B15
    int DMGAntennaID;                       //2 bits B16-B17
    int RXSSLength;                         //6 bits B18-B23
} stExtSSW;

typedef struct
{
    int TotalSectorsInISS;                  //9 bits B0
    int NumberOfRxDMGAntennas;              //2 bits B1-B9
    int Reserved1;                          //5 bits B10-B15
    int PollRequired;                       //1 bits B16
    int Reserved2;                          //7 bits B17-B23
} stExtSSWFeedback;

typedef struct
{
    int SectorsSelect;                      //6 bits B0-B5
    int DMGAntennaSelect;                   //2 bits B6-B7
    int SNRReport;                          //8 bits B8-B15
    int PollRequired;                       //1 bits B16
    int Reserved;                           //7 bits B17-B23
} stExtSSWFeedbackNotTrans;

typedef struct
{
    int LRX;                                //5 bits B0-B4
    int TX_TRN_REQ;                         //1 bits B5
    int MID_REQ;                            //1 bits B6
    int BC_REQ;                             //1 bits B7
    int MID_Grant;                          //1 bits B8
    int BC_Grant;                           //1 bits B9
    int Chain_FBCK_CAP;                     //1 bits B10
    int TXSectorID;                         //6 bits B11-B16
    int Other_AID;                          //8 bits B17-B24
    int TXAntennaID;                        //2 bits B25-B26
    int Reserved;                           //5 bits B27-B31
} stExtBRPRequest;

typedef struct
{
    int BarAckPolicy;      //1 bits B0
    int BarType;           //4 bits B1-B4
    int Reserved;          //7 bits B5-B11
    int TID_INFO;          //4 bits B12-B15
} stTriggerFrame_BarControl;

typedef struct
{
    int StartingSeqControl;       //16 bits B0-B15
} stTriggerFrame_GCR_BarInformation;

typedef struct
{
    int StartingSpatialStream;                      //3 bits B26-28
    int NumberOfSpatialStreams;                     //3 bits B29-31

} stCF_TF_UserInfo_SSAllocation;

//Basic Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 1
typedef struct
{
    int MPDU_MU_SpacingFactor;                    //2 bits B0-B1
    int TID_AggregationLimit;                     //3 bits B2-B4
    int Reserved;                                 //1 bits B5
    int PreferredAC;                              //2 bits B6-B7
} stCF_TF_UI_TrigDenpUI_Basic;

//BFRP Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 1
typedef struct
{
    int FeedbackSegmentRetransmissionBitmap;      //1byte
} stCF_TF_UI_TrigDenpUI_BFRP;

//MU-BAR Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 1
typedef struct
{
    stTriggerFrame_BarControl BarControl;          //2byte
    //Compressed
    int FragmentNum;                               //4 bits B0-B3
    int StartingSeqNum;                            //12 bits B4-B15
    //MuBar
    stBlockAck_TF_MultiTID_TIDInfo MultiTIDInfo[MAX_TF_TID_COUNT];//variable
} stCF_TF_UI_TrigDenpUI_MU_BAR;


//MU-RTS Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 0

//BSRP Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 0

//GCR MU-BAR Tigger Trigger
//TriggerDependentCommonInfo 1
//TriggerDependentUserInfo 0
typedef struct
{
    stTriggerFrame_BarControl BarControl;               //2byte
    stTriggerFrame_GCR_BarInformation BarInformation;   //2byte
} stCF_TF_CI_TrigDenpCI_GCR_MU_BAR;


//BQRP Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 0


//NFRP Tigger Trigger
//TriggerDependentCommonInfo 0
//TriggerDependentUserInfo 1

typedef struct
{
    int ResourceRequest;                             //1 bits B0
    int Reserved;                                    //15 bits B1-B15
} stCF_TF_UI_FeedbackType_NFRP;

typedef struct
{
    int RUAllocationRegion;             //1 bits B12
    int RUAllocation;                   //7 bits B13-B19
}stCF_TF_UserInfo_RUAllocation;

typedef struct
{
    //SS Allocation
    int StartSpatialStream;             //3 bits B26-B28
    int NumberOfSpatialStreams;         //3 bits B29-B31

    //RA-RU Information [AID12 = 0 or 2045 enable]
    int NumberOfRARU;                  //5 bits B26-B30
    int MoreRARU;                      //1 bits B31

}stCF_TF_UserInfo_SSAllocation_RARUInfo;


typedef struct
{
    int StartSpatialStream;             //4 bits B26-B29
    int NumberOfSpatialStreams;         //2 bits B30-B31

    //RA-RU Information [AID12 = 0 or 2045 enable]
    int NumberOfRARU;                  //5 bits B26-B30
    int MoreRARU;                      //1 bits B31
}stCF_TF_UserInfo_SSAllocation_RARUInfo_EHT;

typedef struct
{
    int StartingAID;                                 //12 bits B0-B11
    int Reserved1;                                   //9 bits B12-B20
    int FeedbackType;                                //4 bits B21-B24
    int Reserved2;                                   //7 bits B25-B31
    int ULTargetReceivePower;                        //7 bits B32-B38
    int NumberOfSpatiallyMultiplexedUsers;           //1 bits B39
} stCF_TF_UI_NFRP;

typedef struct
{
    int AID12;                                      //12 bits B0-B11
    int PHYVersionID;                               //3 bits B12-B14
    int ULBandwidthExtension;                       //2 bits B15-B16
    int SpatialReuse1;                              //4 bits B17-B20
    int SpatialReuse2;                              //4 bits B21-B24
    int U_SIGDisregardAndValidate;                  //12 bits B25-B36
    int Reserved;                                   //3 bits B37-B39
} stCF_TF_UI_Special;

typedef struct
{
    int AID12;                                      //12 bits B0-B11
    stCF_TF_UserInfo_RUAllocation RUAllocation;     //8 bits B12-B19
    int ULFECCodingType;                            //1 bits B20
    int ULHE_MCS;                                   //4 bits B21-B24
    int Reserved;                                   //1 bits B25 (Reserved for EHT)
    stCF_TF_UserInfo_SSAllocation_RARUInfo_EHT SSAllocation_RARUInfo_EHT; //6 bits B26-B31
    int ULTagetReceivePower;                        //7 bits B32-B38
    int PS160;                                      //1 bits B39  (PS160 for EHT)
} stCF_TF_UI_EHT;

typedef struct
{
    int AID12;                                      //12 bits B0-B11
    stCF_TF_UserInfo_RUAllocation RUAllocation;     //8 bits B12-B19
    int ULFECCodingType;                            //1 bits B20
    int ULHE_MCS;                                   //4 bits B21-B24
    int UL_DCM;                                     //1 bits B25
    stCF_TF_UserInfo_SSAllocation_RARUInfo SSAllocation_RARUInfo; //6 bits B26-B31
    int ULTagetReceivePower;                        //7 bits B32-B38
    int Reserved;                                   //1 bits B39  (PS160 for EHT)
} stCF_TF_UI_HE;

typedef struct
{
    int NumberOfRA_RU;                      //5 bits B26-30
    int MoreRA_RU;                          //1 bits B31
} stCF_TF_UserInfo_RA_RUInfo;

typedef struct
{
    int SpatialResue1;                      //4 bits B0-B3
    int SpatialResue2;                      //4 bits B4-B7
    int SpatialResue3;                      //4 bits B8-B11
    int SpatialResue4;                      //4 bits B12-B15
} stCF_TF_CommonInfo_UISpatialReuse;

typedef struct CF_TF_UserInfo
{
    int AID12;                                      //12 bits B0-B11
    int PS160;                                      //1 bit B39 (PS160 for EHT,Reserved for HE)

    stCF_TF_UI_NFRP UserInfo_NFRP;                  //40 bits bit0-B39
    stCF_TF_UI_Special UserInfo_Special;            //40 bits bit0-B39 for EHT Special
    stCF_TF_UI_HE UserInfo_HE;                      //40 bits bit0-B39 for HE
    stCF_TF_UI_EHT UserInfo_EHT;                    //40 bits bit0-B39 for EHT

    //var TriggerDependentUserInfo;                 //variable
    stCF_TF_UI_TrigDenpUI_Basic TriggerDependentUserInfo_Basic;
    stCF_TF_UI_TrigDenpUI_BFRP TriggerDependentUserInfo_BFRP;
    stCF_TF_UI_TrigDenpUI_MU_BAR TriggerDependentUserInfo_MUBar;
} stCF_TF_UserInfo;

typedef struct
{
    stCF_TF_UserInfo UserInfo[MAX_USERINFO_COUNT];
    int Len;         //UserInfoList实际有效长度0-64
} stCF_TF_UserInfoList;

typedef struct
{
    //u8 Padding[MAX_PADDING_COUNT];
    int Len;         //Padding实际有效长度0-128之间，不超过MAX_PADDING_COUNT
} stCF_TF_Padding;

typedef struct
{
    int TriggerType;                             //4 bits B0-B3
    int ULLength;                                //12 bits B4-B15
    int MoreTF;                                  //1 bits B16
    int CSRequired;                              //1 bits B17
    int ULBW;                                    //2 bits B18-B19
    int GI_HELTF_Type;                           //2 bits B20-B21
    int MU_MIMO_HELTFMode;                       //1 bits B22
    int NumberOfHELTFSymbols_MidamblePeriodicity;//3 bits B23-B25
    int UL_STBC ;                                //1 bits B26
    int LDPCExtraSymbolSegment;                  //1 bits B27
    int APTxPower;                               //6 bits B28-B33
    int PreFECPaddingFactor;                     //2 bits B34-B35
    int PEDisambiguity;                          //1 bits B36
    //int ULSpatialReuse;                        //16 bits B37-B52
    u8 SpatialReuse[4];                        //16 bits B37-B52 for EHT&HT
    int Doppler;                                 //1 bits B53
    int UL_HE_SIG_A2_Reserbed;                   //9 bits B54-B62
    int Reserved;                                //1 bits B63
    //EHT variant
    int HE_ETH_P160;                             //1 bits B54
    int SpecialUserInfoFieldPresent;             //1 bits B55
    //int Reserved1_EHT;                           //7 bits B56-B62
    //int Reserved2_EHT;                           //1 bits B63

    stCF_TF_CI_TrigDenpCI_GCR_MU_BAR TriggerDependentCommonInfo_GCR_MU_BAR;//
} stCF_TF_CommonInfo;

typedef struct
{
    int QMFSeqNumber;                       //10 bits B4-B13
    int ACI;                                //2 bits B14-B15
} stCF_TriggerFrame;

#pragma endregion


#pragma region DataFrame

typedef struct
{
    int QMFSeqNumber;                       //10 bits B4-B13
    int ACI;                                //2 bits B14-B15
} stDF_SeqNumber;
typedef struct
{
    int FragmentNumber;                     //4 bits B0-B3
    stDF_SeqNumber SeqNumber;             //12 bits B4-B15
} stDF_SeqControl;

typedef struct
{
    int BUForAC_VO;               //1 bits B10
    int BUForAC_VI;               //1 bits B11
    int BUForAC_BE;               //1 bits B12
    int BUForAC_BK;               //1 bits B13
} stDF_BufferedAC;


typedef struct
{
    int TID;                      //4 bits B0-B3
    int EOSP;                     //1 bits B4
    int AckPolocy;                //2 bits B5-B6
    int A_MSDUPresent;            //1 bits B7
    int A_MSDUType;               //1 bits B8
    int RDG_MorePPDU;             //1 bits B9
    stDF_BufferedAC bufferedAC;     //4 bits B10-B13
    int Reserved;                 //1 bits B14
    int ACConstraint;             //1 bits B15
} stDF_QoSControlInDMG;

typedef struct
{
    int QosLastByteType;          //占位用,上层信号参数还原时用于信息辨别，上层需求，不在PSDU信号生成中处理
    int TID;                      //4 bits B0-B3
    int EOSP;                     //1 bits B4
    int AckPolocy;                //2 bits B5-B6
    int A_MSDUPresent;            //1 bits B7
    int LastByte;                 //8 bits B8-B15
} stDF_QoSControl;

typedef struct
{
    int TRQ;                    //1 bits B1
    int MAI;                    //4 bits B2-B5
    int MFSI;                   //3 bits B6-B8
    int MFB_ASELS;              //7 bits B9-B15
}stLinkAdaptationControl;
typedef struct
{
    stLinkAdaptationControl linkAdaptation;//15 bits B1-B15
    int CalibrationPosition;               //2 bits B16-B17
    int CalibrationSequence;               //2 bits B18-B19
    int Reserved1;                         //2 bits B20-B21
    int CSI_Steering;                      //2 bits B22-B23
    int HT_NDP_Announcement;               //1 bits B24
    int Reserved2;                         //4 bits B25-B28
    int DEI;                               //1 bits B29
} stDF_HTControlMiddle;

typedef struct
{
    int NUM_STS;       //3 bits B9-B11
    int VHT_MCS;       //4 bits B12-B15
    int BW;            //2 bits B16-B17
    int SNR;           //6 bits B18-B23
}stDF_MBF;

typedef struct
{
    //int Reserved;              //1 bits B1
    //int TYPE_B1;              //1 bits B1
    int MRQ;                   //1 bits B2
    int MSI_STBC;              //3 bits B3-B5
    int MFSI_GID_L;            //3 bits B6-B8
    stDF_MBF MFB;                //15 bits B9-B23
    int GID_H;                 //3 bits B24-B26
    int CodingType;            //1 bits B27
    int FBTxType;              //1 bits B28
    int UnsolicitedMFB;        //1 bits B29
} stDF_HTControlMiddleOfVHT;


typedef struct
{
    int ControlID;              //4 bits B0-B3
    int MRQ;                   //1 bits B2
    int MSI_STBC;              //3 bits B3-B5
    int MFSI_GID_L;            //3 bits B6-B8
    stDF_MBF MFB;                //15 bits B9-B23
    int GID_H;                 //3 bits B24-B26
    int CodingType;            //1 bits B27
    int FBTxType;              //1 bits B28
    int UnsolicitedMFB;        //1 bits B29
} stDF_HTControlMiddleOfHE;

//TRSControl CtrlID=0,Length=26
typedef struct
{
    int ULDataSymbols;         //5 bits B0-B4
    int RUAllocation;          //8 bits B5-B12
    int APTxPower;             //5 bits B13-B17
    int ULTargetReceivePower;  //5 bits B18-B22
    int ULHE_MCS;              //2 bits B23-B24
    int Reserved;              //1 bits B25
}stDF_HTControl_HE_TRSControl;

//OMControl CtrlID=1,Length=12
typedef struct
{
    int RxNSS;              //3 bits B0-B3
    int ChannelWidth;       //2 bits B3-B4
    int ULMUDiable;         //1 bits B5
    int TxNSTS;             //3 bits B6-B8
    int ERSUDisable;        //1 bits B9
    int DLMU_MIMOResoundRecommendation; //1 bits B10
    int ULMUDataDisable;    //1 bits B11
}stDF_HTControl_HE_OMControl;

//HLAControl CtrlID=2,Length=26
typedef struct
{
    int UnsolicitedMFB;   //1 bits B0
    int MRQ;              //1 bits B1
    int NSS;              //3 bits B2-B4
    int HE_MCS;           //4 bits B5-B8
    int DCM;              //1 bits B9
    int RUAlloction;      //8 bits B10-B17
    int BW;               //2 bits B18-B19
    int MSI_PartialPPDUParameters; //3 bits B20-b22
    int TxBeaforming;     //1 bits B23
    int ULHE_TB_PPDU_MFB; //1 bits B24
    int Reserved;         //1 bits B25
}stDF_HTControl_HE_HLAControl;

//BSRControl CtrlID=3,Length=26
typedef struct
{
    int ACIBitmap;        //4 bits B0-B3
    int DeltaTID;         //2 bits B4-B5
    int ACIHigh;          //2 bits B6-B7
    int ScalingFactor;    //2 bits B8-B9
    int QueueSizeHigh;    //8 bits B10-b17
    int QueueSizeAll;     //8 bits B18-B25
}stDF_HTControl_HE_BSRControl;

//UPHControl CtrlID=4,Length=8
typedef struct
{
    int ULPowerHeadroom;   //5 bits B0-B4
    int MinimumTrasmitPowerFlag; //1 bits B5
    int Reserved;          //2 bits B6-B7
}stDF_HTControl_HE_UPHControl;

//BQRControl CtrlID=5,Length=10
typedef struct
{
    int AvailableChannelBitmap; //8 bits B0-B7
    int Reserved;          //2 bits B8-B9
}stDF_HTControl_HE_BQRControl;

//CASControl CtrlID=6,Length=8
typedef struct
{
    int ACConstraint;     //1 bits B0
    int RDG_MorePPDU;     //1 bits B1
    int PSRT_PPDU;        //1 bits B2
    int Reserved;         //5 bits B3-B7
}stDF_HTControl_HE_CASControl;

//ONESControl CtrlID=15,Length=26
typedef struct
{
    u8 data[26] = { 1 };//26位全1
}stDF_HTControl_HE_ONESControl;

//EHT_OMControl CtrlID=7,Length=6
typedef struct
{
    int RxNSSExtension;        //1 bits B0
    int ChannelWidthExtension; //1 bits B1
    int TxNSTSExtension;       //1 bits B2
    int Reserved;              //3 bits B3-B5
}stDF_HTControl_HE_EHT_OMControl;

//SRSControl CtrlID=8,Length=10
typedef struct
{
    int PPDUResponseDuration; //8 bits B0-B7
    int Reserved;             //2 bits B8-B9
}stDF_HTControl_HE_SRSControl;

//AARControl CtrlID=10,Length=20
typedef struct
{
    int AssistedAPLinkIDBitmap; //16 bits B0-B15
    int Reserved;               //4 bits B16-B19
}stDF_HTControl_HE_AARControl;

//30bit
typedef struct
{
    int ControlID; //4 bits B0-B3
    //TRSControl CtrlID=0,Length=26
    stDF_HTControl_HE_TRSControl TRSControl;
    //OMControl CtrlID=1,Length=12
    stDF_HTControl_HE_OMControl OMControl;
    //HLAControl CtrlID=2,Length=26
    stDF_HTControl_HE_HLAControl HLAControl;
    //BSRControl CtrlID=3,Length=26
    stDF_HTControl_HE_BSRControl BSRControl;
    //UPHControl CtrlID=4,Length=8
    stDF_HTControl_HE_UPHControl UPHControl;
    //BQRControl CtrlID=5,Length=10
    stDF_HTControl_HE_BQRControl BQRControl;
    //CASControl CtrlID=6,Length=8
    stDF_HTControl_HE_CASControl CASControl;
    //ONESControl CtrlID=15,Length=26
    stDF_HTControl_HE_ONESControl ONESControl;

    //EHT_OMControl CtrlID=7,Length=6
    stDF_HTControl_HE_EHT_OMControl EHT_OMControl;
    //SRSControl CtrlID=8,Length=10
    stDF_HTControl_HE_SRSControl SRSControl;
    //AARControl CtrlID=10,Length=20
    stDF_HTControl_HE_AARControl AARControl;

} stDF_HTControl_ControlSubfield;

//30bit
typedef struct
{
    int ACtrlCount;
    stDF_HTControl_ControlSubfield Control[3]; //数组最多3个

    int PaddingLength;
    u8 Padding[30] = { 0 };
}stDF_HTControl_AControl;

typedef struct
{
    int VHT;                               //1 bits B0 (TYPE_B0 别名)
    int TYPE_B1;                           //1 bits B1

    //union
    //{
    stDF_HTControlMiddle htControlMiddle;    //29 bits B1-B29
    stDF_HTControlMiddleOfVHT htControlMiddleOfVHT; //28 bits B2-B29
    //};
    int ACConstraint;                      //1 bits B30
    int RDG_MorePPDU;                      //1 bits B31

    //HE&EHT
    stDF_HTControl_AControl AControl;    //30bits B2-B31
} stDF_HTControl;

#pragma endregion

#pragma region MgmtFrame

typedef struct
{
    int AuthenticationAlgorithmNum;    //16bit B0-B15
} stMF_AuthenticationAlgorithmNum;

typedef struct
{
    int SeqNum;    //16bit B0-B15
} stMF_AuthenticationTransactionSeqNum;

typedef struct
{
    int BeaconInterval;    //16bit B0-B15
} stMF_BeaconInterval;


typedef struct
{
    int ESS;               //1bit B0
    int IBSS;              //1bit B1
    int CF_Pollable;       //1bit B2
    int CF_PollRequest;    //1bit B3
    int Privacy;           //1bit B4
    int ShortPreamble;     //1bit B5 802.11b
    int Reserved1;         //1bit B6
    int Reserved2;         //1bit B7
    int SpectrumMgmt;      //1bit B8
    int QoS;               //1bit B9
    int ShotSlotTime;      //1bit B10
    int APSD;              //1bit B11
    int RadioMeasurement;  //1bit B12
    int Reserved3;         //1bit B13
    int DelayedBlockAck;   //1bit B14
    int ImmediateBlockAck; //1bit B15
} stMF_CapabilityInfo;//Non-DMG STA

typedef struct
{
    int ListenInterval; //16bit B0-B15
} stMF_ListenInterval;

typedef struct
{
    int AssociationID; //16bit B0-B15
} stMF_AssociationID;

typedef struct
{
    long long Timestamp; //64bit B0-B63

} stMF_Timestamp;

typedef struct
{
    int ReasonCode; //16bit B0-B15

} stMF_ReasonCode;

typedef struct
{
    int StatusCode; //16bit B0-B15

} stMF_StatusCode;

typedef struct
{
    int DataRate;         //7bit B0-B6
    int MandatoryFlag;    //1bit B7
} stMF_DataRate;

typedef struct
{
    int ElementID;         //1byte
    int Length;            //1byte
    stMF_DataRate DataRate[MAX_DATARATE_COUNT];
} stMF_SupportedRates; //3-10 byte

typedef struct
{
    int ElementID;         //1byte
    int Length;            //1byte
    stMF_DataRate DataRate[MAX_EXT_DATARATE_COUNT];
} stMF_ExtendedSupportedRates;

typedef struct
{
    int ElementID;         //1byte
    int Length;            //1byte
    int DwellTime;         //2byte
    int HopSet;            //1byte
    int HopPattern;        //1byte
    int HopIndex;          //1byte
} stMF_FHParameterSet;       //7byte

typedef struct
{
    int ElementID;         //1byte
    int Length;            //1byte
    int CFPCount;          //1byte
    int CFPPeriod;         //1byte
    int CFPMaxDuration;    //2byte
    int CFPDurRemaining;   //2byte
}stMF_CFParameterSet;//8byte 无竞争参数集

typedef struct
{
    int ElementID;         //1byte
    int Length;            //1byte
    int CurrentChannel;    //1byte
} stMF_DSParameterSet;       //3byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int DTIMCount;             //1byte
    int DTIMPeriod;            //1byte
    int BitmapControl;         //1byte
    int PartialVirtualbitmap;  //1-251byte
} stMF_TrafficIndicationMap;
//TIM 传输指示映射 TIM 6-256byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int ATIMWindow;            //2byte
} stMF_IBSSParameterSet; //4byte IBSS参数集

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    u8 SSID[32];             // 32byte
} stMF_SSID;//Service Set Identity [2-34byte]

typedef struct
{
    int FirstChannelNumber;    //1byte
    int NumberOfChannels;      //1byte
    int MaxTransmitPower;      //1byte
} stMF_CountryInfo_ContraintTriplet;

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int CountryString;         //3byte
    stMF_CountryInfo_ContraintTriplet contraintTri[MAX_COUNTRY_INFO_COUNT];//3byte
    int Pad;                   //1byte
} stMF_CountryInfo;              //8-256 byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int ChallengeText[253];    //1-253 byte
} stMF_FHHoppingParameter;

typedef struct
{

} stMF_FHPatternParameter;//variable

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int LocalPowerConstraint;  //1byte
} stMF_PowerConstraint;//3byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int SwitchTime;            //2byte
    int SwitchTimeout;         //2byte
} stMF_ChannelSwitchTiming;//6byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int QuietCount;            //1byte
    int QuietPeriod;           //1byte
    int QuietDuration;         //2byte
    int QuietOffset;           //2byte
} stMF_Quiet;//8byte

typedef struct
{
    int ChannelNumber;      //1byte
    int Map;                //1byte
} stMF_ChannelMap;//2byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int DFSOwner;             //6byte
    int DFSRecoveryInterval;                //1byte
    stMF_ChannelMap ChannelMap[MAX_CHANNEL_MAP_COUNT];  //2xn byte
} stMF_IBSS_DFS;//variable

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int TransmitPower;         //1byte
    int LinkMargin;            //1byte
} stMF_TPCReport;//4byte

typedef struct
{
    int NonERP_Present;         //1bit B0
    int User_Protection;        //1bit B1
    int Barker_Preamble_Mode;   //1bit B2
    int Reserved;               //5bit B3-B7
} stMF_EPRParameters;//1byte

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    stMF_EPRParameters EPRParameters;         //1byte
} stMF_EPRInfomation;//3byte

typedef struct
{
    int OUI;            //3byte
    int SuiteType;      //1byte
} stMF_CipherSuite;//4byte
typedef struct
{
    int ElementID;                        //1byte
    int Length;                           //1byte
    int Version;                          //2byte
    stMF_CipherSuite GroupDataCipherSuite;             //0 or 4byte
    int PairwiseCipherSuiteCount;         //0 or 2byte
    int PairwiseCipherSuiteList;          //0 or (4 x m)byte
    int AKMSuiteCount;                    //0 or 2byte
    int AKMSuiteList;                     //0 or (4 x n)byte
    int RSNCapabilities;                  //0 or 2byte
    int PMKIDCount;                       //0 or 2byte
    int PMKIDList;                        //0 or (16 x s)byte
    int GroupManagementCipherSuite;       //0 or 4byte

} stMF_RSNE;//variable

typedef struct
{
    int ElementID;             //1byte
    int Length;                //1byte
    int ChallengeText[253];    //1-253 byte
} stMF_ChallengeText;

#pragma endregion
typedef struct
{
    bool UserDefinePreambleEnable;          //启用用户定义前导部分，数据帧时使用
    bool LoadFrameBodyEnable;               //启用导入PSDU Data帧数据载荷，对应界面Load PsduBit开关，和结构体中Len成对使用
    bool UserDefineMgmtBodyDataTypeEnable;  //启用用户定义管理帧Framebody长度和DataType,而不再使用固定帧体填充，和接口结构体中dataType及len配对使用
    //数据长度
    int PreambleLen;               //前导的有效长度
    int Len;                       //导入PSDU Data帧数据载荷的长度
    //数据
    u8 UserDefineDataPreamble[MAX_USERINPUT_PREAMBLE_LEN];
    u8 UserDefineFrameBodyData[MAX_USERINPUT_FRAMEBODY_LEN];
}stFrameBody;


typedef struct
{
    u8 Address4[6];                //6 bytes
    u8 Address5[6];                //6 bytes
    u8 Address6[6];                //6 bytes
}stMeshAddressExtension;

typedef struct
{
    //Mesh Flags 1byte
    int AddressExtensionMode;                   //2 bits B0-B1
    int Reserved;                               //6 bits B2-B7

    int MeshTTL;                                //1 byte
    int MeshSequenceNumber;                     //4 bytes
    stMeshAddressExtension MeshAddressExtension;//0,6,12 bytes
}stMeshControlField;


typedef struct
{
    //802.2 Frame Format (LLC)

    //D-D-D-D-D-D-U-I/G
    u8 DSAP[8];                   //8 bits

    //S-S-S-S-S-S-U-C/R
    u8 SSAP[8];                   //8 bits

    //I Frames(16 bits)             X-X-X-X-X-X-X-0 = I Frame  NS-NS-NS-NS-NS-NS-NS-0-NR-NR-NR-NR-NR-NR-P/F
    //Supervisory Frames(16 bits)   X-X-X-X-X-X-0-1 = Supervisory Frame    0-0-0-0-S-S-0-1-NR-NR-NR-NR-NR-NR-NR-P/F
    //Unnumbered Frames(8 bits)     X-X-X-X-X-X-1-1 = Unnumbered frame  M-M-M-P/F-M-M-1-1 0-0-0-V-Z-Y-W-X
    u8 Control[16];//8 or 16 bits

    //THE IEEE 802.3 SNAP  (仅在Control = 0x3时(即Unnumbered Frames)以下参数有效,否则协议未定义以下变量位置)
    u8 VendorCode[3];     //3 bytes
    u8 Type[2];           //2 bytes

}stLLCHeader;


typedef struct
{
    u8 DA[6];
    u8 SA[6];
    int Length;
    stMeshControlField MeshControlField;

    bool bLLCHeaderEnable;                    //是否开启LLC头自定义
    stLLCHeader LLCHeader;                    //802.3 LLC Header

    DataType dataType;                        //DataType_Start时启用数组源,否则使用数据类型和长度自动生成
    //u8 MSDUPayload[MAX_MSDU_LENGTH];
}stAMSDUSubFrame;

typedef struct
{
    int Count;                                       //真实数量
    stAMSDUSubFrame AMSDUSubFrame[MAX_MSDU_COUNT];   //数组
}stAMSDU;

enum WEPType
{
    WEP_40 = 0,
    WEP_104,
    WEP_128,
};

enum TKIPType
{
    TKIP_IV16 = 0,
    TKIP_IV32,
};

enum CCMPType
{
    CCMP_128 = 0,
    CCMP_256,
};

enum GCMPType
{
    GCMP_128 = 0,
    GCMP_256,
};

typedef struct
{
    char passphrase[64];
    u32 passphrase_len;
    char ssid[32];
    u32 ssid_len;
}stPSK_KeyParam;

typedef struct
{
    char passphrase[8];
    u32 passphrase_len;
}stWAPI_BK_KeyParam;

typedef struct
{
    u8 psk[32];        //仅WPA-PSK输入方式时需输入参数
    u8 snonce[32];
    u8 anonce[32];
    u8 saddr[6];       //无需界面输入参数
    u8 aaddr[6];       //无需界面输入参数
}stTK_KeyParam;

typedef struct
{
    u8 bk[16];        //仅WAPI-TK输入方式时需输入参数
    u8 anonce[32];    //AE nonce(AP) n1
    u8 snonce[32];    //ASUE nonce(Station) n2
    u8 saddr[6];      //无需界面输入参数(ASUE MAC)STA
    u8 aaddr[6];      //无需界面输入参数(AE MAC)AP
}stWAPI_TK_KeyParam;

typedef struct
{
    int aad_mode;       // AAD 构造方式：0 = 2006版本；1 = 2020版本；SMS4-OFB默认2006版， SMS4-GCM 默认2020版
    int fc_htc;         // 0 = 使用framecontrol里面的htc值，1 = 强制framecontrol里面的htc值等于0。默认值0
    int amsdu_capab;    // 0 = 表示STA or SPP A-MSDU Capab = 0， 1 = 表示STA and SPP A-MSDU Capab = 1    
} stWAPI_ExtInfo;

typedef struct
{
    u8 tk[MAX_TKIP_TK_LENGTH];         //仅TK输入类型时界面需输入参数
    u32 tk_len;                        //仅TK输入类型时界面需输入参数

    struct st_TKIP_IV16
    {
        u8 TSC1;
        u8 WEPSeed;
        u8 TSC0;

        u8 Rsvd;    //5bits:bit0-4
        u8 ExtIV;   //1bit:bit5
        u8 KeyID;   //2bits:bit6-7
    } IV16;

    struct st_TKIP_IV32
    {
        u8 TSC2;
        u8 TSC3;
        u8 TSC4;
        u8 TSC5;
    } IV32;
}stTKIP_IV;

typedef struct
{
    u8 tk[MAX_CCMP_TK_LENGTH];

    u8 PN0;
    u8 PN1;
    u8 Rsvd;

    u8 KeyID_Rsvd;    //5bits:bit0-4
    u8 KeyID_ExtIV;   //1bit:bit5
    u8 KeyID_KeyID;   //2bits:bit6-7

    u8 PN2;
    u8 PN3;
    u8 PN4;
    u8 PN5;
}stCCMP_Header;

typedef struct
{
    u8 tk[MAX_GCMP_TK_LENGTH];

    u8 PN0;
    u8 PN1;
    u8 Rsvd;

    u8 KeyID_Rsvd;    //5bits:bit0-4
    u8 KeyID_ExtIV;   //1bit:bit5
    u8 KeyID_KeyID;   //2bits:bit6-7

    u8 PN2;
    u8 PN3;
    u8 PN4;
    u8 PN5;
}stGCMP_Header;

typedef struct
{
    //WAPI-TK 私有输入项：在 SMS4-OFB 时总共 32bytes， SMS4-GCM 时 16bytes
    u8 tk[MAX_WAPI_TK_LENGTH];  //32 bytes

    u8 KeyIdx;        //1 byte
    u8 Rsvd;          //1 byte
    u8 PN[16];        //16 bytes
}stWAPI_Header;

typedef struct
{
    u8 InitVector[3];    //3 Bytes[bit 0-23]
    u8 Pad;              //6 Bits [bit 24-29]
    u8 KeyID;            //2 Bits [bit 30-31]

    int InitVectorLen;   //最大为3
}stWEP_IV;

typedef struct
{
    WEPType Type;
    u8 WPP40Key[5];
    u8 WEP104Key[13];
    u8 WEP128Key[16];
    stWEP_IV IV;
    bool bUserDefineICV;     //默认false,当值为true时,使用用户自定义ICV
    u8 ICV[4];

    int KeyLen = 0;
}stWEPSetting;

typedef struct
{
    TKIPType Type;
    stTKIP_IV IV;

    //默认false,当值为true时,使用用户自定义MIC、ICV
    bool bUserDefineICV;
    bool bUserDefineMIC;
    u8 UserDefined_MIC[8];
    u8 UserDefined_ICV[4];
}stTKIPSetting;

typedef struct
{
    CCMPType Type;
    stCCMP_Header Header;

    //默认false,当值为true时,使用用户自定义MIC
    bool bUserDefineMIC;
    u8 UserDefined_MIC[16];//CCMP128时为8;CCMP256时为16,自适应
    int amsdu_capablity;
}stCCMPSetting;

typedef struct
{
    GCMPType Type;
    stGCMP_Header Header;

    //默认false,当值为true时,使用用户自定义MIC
    bool bUserDefineMIC;
    u8 UserDefined_MIC[16];
    int amsdu_capablity;
}stGCMPSetting;

typedef struct
{
    stWAPI_Header Header;

    //默认false,当值为true时,使用用户自定义MIC
    bool bUserDefineMIC;
    u8 UserDefined_MIC[16];
}stWAPI_OFBSetting;

typedef struct
{
    stWAPI_Header Header;

    //默认false,当值为true时,使用用户自定义MIC
    bool bUserDefineMIC;
    u8 UserDefined_MIC[16];
}stWAPI_GCMSetting;

typedef struct
{
    eSecutityMode Mode;          //默认值False
    stWEPSetting WEPSetting;
    stTKIPSetting TKIPSetting;
    stCCMPSetting CCMPSetting;
    stGCMPSetting GCMPSetting;
    stWAPI_OFBSetting WAPI_OFBSetting;
    stWAPI_GCMSetting WAPI_GCMSetting;

    //仅在TKIP、CCMP、GCMP模式下有意义
    eKeyInputType KeyInputType;
    stPSK_KeyParam PSK_KeyParam;
    stTK_KeyParam TK_KeyParam;

    //仅在WAPI模式下有意义
    eWAPIKeyInputType WAPIKeyInputType;
    stWAPI_BK_KeyParam WAPI_BK_KeyParam;
    stWAPI_TK_KeyParam WAPI_TK_KeyParam;

    //仅WAPI的两种加密方式需要
    stWAPI_ExtInfo WAPI_ExtInfoParam;

    u8 PSK[32];                //无需界面输入，中间变量
    u32 PSKLen;                //无需界面输入，中间变量
}stSecuritySetting;

typedef struct
{
    int Delimiter_EOF;                //1bit B0
    int Reserved;                     //1bit B1
    int MPDULength;                   //14bits B2-B15
    int CRC;                          //8bits B16-B23
    int DelimiterSignature;           //8bits B24-B31
}stMPDUDelimiter;

typedef struct
{
    long long int NextTWT;            //45bits B0-B44
    int TWTFlowIdentifier;            //3bits B45-47
}stNextTWTInfo_SuspendDuration;

using MAC_Frame = struct MAC_Frame_T
{
    stFrameCtrl Fctrl;    //必须参数

    //PS-Poll ID共用此参数 (值为2bytes前14bits AID，bit15和16bit均为1)
    int Duration;         //必须参数

    stMAC_ADDR MAC_Addr;  //根据Fctrl的参数设置对应的MAC值
    int Sequence;       //可选参数，data frame和manager frame 都有此参数
    int QOS;            //可选参数，data frame有此参数
    int HTCtrl;         //可选参数

    //FrameBody body;     //可选参数

    stBlockAck_BarControl blockAck_BarCtrl;

    //BlockAckReq + BlockAck
    int FragmentNum;       //4 bits B0-B3
    int StartingSeqNum;    //12 bits B4-B15
    stBlockAck_MultiTID_SubField blockAck_MultiTID_SubField;
    u8 GCRGroupAddr[MAC_LEN];     //48 bits B16-B63

    //BlockAck
    //Basic BlockAck variant len = 128
    //Compressed BlockAck variant len = 8
    //Extended Compressed BlockAck variant len = 8
    //GCR Block Ack variant len = 8
    u8 BlockAckBitmaps[128];     //128 bytes

    //数据帧FrameBody长度,管理帧自定义帧体类型时的长度
    int len; //(VHT/HE/EHT 共用StaInfo 长度变量)
    stSoundingDialogToken soundingDialogToken;
    stSTAInfo staInfo[MAX_STA_INFO_COUNT];

    //HE NDPA
    stSTAInfo_HENot2047 staInfo_HENot2047[MAX_STA_INFO_COUNT];
    stSTAInfo_HE2047 staInfo_HE2047[MAX_STA_INFO_COUNT];
    //HE NDPA StaInfo类型选择器,为0:staInfo_HENot2047类型;为1:staInfo_HE2047类型
    int HEStaInfoTypeSelect[MAX_STA_INFO_COUNT];

    //EHT NDPA
    stSTAInfo_EHT staInfo_EHT[MAX_STA_INFO_COUNT];

    //BlockAck_MultiTID_SubField blockAck_MultiTID_SubField;
    int RBUFCAP;               //byte 1

    //Trigger Frame
    //Poll Frame
    int ResponseOffset;
    stDynamicAllocationInfo dynamicAllocationInfo;
    stBFControl bfControl;
    stBFControlOfGrant bfControlOfGrant;
    stExtSSW extSSW;
    stExtSSWFeedback extSSWFeedback;
    stExtSSWFeedbackNotTrans extSSWFeedbackNotTrans;
    stExtBRPRequest extBRPRequest;
    stBeamformedLinkMaintenance extBeamformedLinkMaintenance;

    //数据帧
    DataType dataType;
    stDF_SeqControl dataFrame_SeqControl;
    //数据帧 QoS
    stDF_QoSControl dataFrame_QoSControl;
    stDF_QoSControlInDMG dataFrame_QoSControlInDMG;
    stDF_HTControl dataFrame_HTControl;

    //管理帧
    stMF_CapabilityInfo capabilityInfo;
    int ListenInterval;                        //16bit B0-B15
    stMF_SSID SSID;
    stMF_SupportedRates supportRates;
    stMF_ExtendedSupportedRates extSupportRates;
    u8 CurrentAPAdress[6];
    stMF_StatusCode statusCode;               //2byte
    stMF_AssociationID associationID;         //2byte
    int AuthenticationAlgorithmNum;           //16bit B0-B15
    int AuthenticationTransactionSeqNum;      //16bit B0-B15
    stMF_ChallengeText chanllengeText;        //3-255 byte
    stMF_Timestamp timeStamp;                 //8byte
    stMF_BeaconInterval beaconInterval;       //2byte
    stMF_ReasonCode reasonCode;               //2byte

    RfStandard rfStandard;                  //协议标准
    bool bAggregation;                      //802.11n时标识是否为聚合帧，默认为是

    u8 mac_frame_data[MAX_MAC_FRAME_COUNT]; //返回值byte数组
    int frame_data_len;                     //返回byte数组实际有效内容长度
    int frame_mpdu_len;                     //返回byte数组剔除delimiter和padding后的实际有效内容长度

    //bool bMisInputEnable = false;         //异常输入Enable,弃用
    u8 manualFCS[4];                        //用户手动FCS
    bool bFCSEnable = false;                //默认false,当用户启用手动FCS时，FCS可能为错误值，用于错误验证

    //int ID;                                    //PS-Poll ID 2bytes 和Duration共用
    u8 FeedbackSegmentRetransmissionBitmap;      //Beamforming Report Poll Feedback Segment Retransmission Bitmap 2bytes
    u8 CarriedFrameControl[2];                   //ControlWrapper Carried Frame Control
    //u8 CarriedFrame[2312];                     //(弃用，使用数据类型定义替代)ControlWrapper Carried Frame,实际有效长度由len变量决定。


    stFrameBody framebody;                     //增加用户自定义framebody字段功能，对于数据帧来说，framebody为自定义的framebody前缀

    bool bAMSDU_Enable;                        //AMSDU Enable,使用AMSDU生成Payload
    bool bMSDU_Enable;                         //MSDU打开开关,非聚合模式，使用MSDU格式生成Payload，仅使用AMSDU首个成员
    stAMSDU AMSDU;                             //A-MSDU的输入结构体

    bool bUserDefinedDelimiterEnable;          //用户自定义Delimiter开关,默认false [此处注意bool 是一个字节]
    //u8 UserDefinedDelimeter[4];              //用户定义delimiter内容,被取代
    stMPDUDelimiter UserDefinedDelimeter;      //用户定义delimiter内容

    u8 ReservedFrame_UserDefine[MAX_RESERVED_FRAME_USERDEFINE_LENGTH]; //Reserved帧用户自定义部分
    int ReservedFrame_UserDefineLength;                                //Reserved帧用户自定义部分实际使用长度

    bool bUserDefinedEOFPaddingEnable;                 //用户自定义EOFPadding启用开关,默认false [此处注意bool 是一个字节]
    u8 UserDefinedEOFPadding[MAX_EOF_PADDING_LENGTH];  //用户定义的EOFPadding内容
    int UserDefinedEOFPaddingLength;                   //用户定义的EOFPadding实际长度

    //Trigger Frame TF
    stCF_TF_CommonInfo CommonInfo;
    stCF_TF_UserInfoList UserInfoList;
    stCF_TF_Padding Padding;

    //TACK
    int BeaconSequence;                      //1byte
    long long int PentapartialTimestamp;     //5bytes
    stNextTWTInfo_SuspendDuration NextTWTInfo_SuspendDuration;//6bytes

    //Security
    stSecuritySetting SecuritySetting;        //结构体默认Enable=False,不开启

    //错误信息描述
    int ErrorCode;                            //错误码
    char ErrorDesc[256];                      //错误描述
    bool IsEOF;								  //是否为MPDU EOF帧,默认为FALSE
    bool bIsNullFrame;                        //默认为FALSE,是否为空帧

    //是否为整帧导入
    bool bLoadFullMPDUData;                   //导入整帧,默认为false;为true时,framebody中 UserDefineFrameBodyData&Len决定导入数据内容
    bool bLoadFullMPDUDataWithFCS;            //整帧导入带FCS,默认为带,值为true
    bool bLoadFullMPDUDataWithDelimiter;      //整帧导入的数据内是否自带Delimiter,默认为带,值为true
};
