#include "scpi_gen_11ac_mumimo.h"
#include "commonhandler.h"
#include <iostream>
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "TesterWave.h"
#include <sys/time.h>
#include <math.h>

int Is11acMU(void *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context);
        int demod = attr->PnWifi.get()->commonParam.standard;
        int PPDU = attr->PnWifi.get()->commonParam.subType;
        if (demod < WT_DEMOD_11AC_20M || demod > WT_DEMOD_11AC_80_80M)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (PPDU != VHT_MUMIMO_PPDU)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

static int GetMUMIMOIntParam(scpi_t *context, int minNum, int maxNum, int &Value)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11acMU(attr);
        if (iRet)
        {
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Value < minNum || Value > maxNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t Set11AC_MU_MIMO_GroupID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 1;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 1, 62, Value);
        if (iRet)
        {
            break;
        }
        attr->PnWifi.get()->PN11ac_MUMIMO.GroupID = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_TxOPPS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 0, 1, Value);
        if (iRet)
        {
            break;
        }
        attr->PnWifi.get()->PN11ac_MUMIMO.TXOP_PS_NOT_ALLOWED = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_GI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 0, 1, Value);
        if (iRet)
        {
            break;
        }
        attr->PnWifi.get()->PN11ac_MUMIMO.GI = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

static int GetMUMIMOUserIntParam(scpi_t *context, int &UserID, int &Value, int minNum, int maxNum)
{
    int iRet = WT_ERR_CODE_OK;
    int Number[1] = {0};
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11acMU(attr);
        if (iRet)
        {
            break;
        }

        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        UserID = Number[0];

        if (UserID < 0 || UserID > AC_MUMIMO_4_USER)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Value < minNum || Value > maxNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

    } while (0);

    return iRet;
}

scpi_result_t Set11AC_MU_MIMO_UserCnt(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 0, MUMIMO_8_USER, Value);
        if (iRet)
        {
            break;
        }
        attr->PnWifi.get()->PN11ac_MUMIMO.UserNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_QMat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 0, 1, Value);
        if (iRet)
        {
            break;
        }

        attr->PnWifi.get()->PN11ac_MUMIMO.Qmat.Q_Flag = Value;
         
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_QMatNtx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 1, 8, Value);
        if (iRet)
        {
            break;
        }
        attr->PnWifi.get()->PN11ac_MUMIMO.Qmat.Q_NTX = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_QMatType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOIntParam(context, 0, 2, Value);
        if (iRet)
        {
            break;
        }
        attr->PnWifi.get()->PN11ac_MUMIMO.Qmat.Q_Type = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_QMatDelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value[8] = {0};
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11acMU(attr);
        if (iRet)
        {
            break;
        }


        int MinNum = 0;
        int MaxNum = 400;
        for (int i = 0; i < 8; i++) //必须8个数据
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }

            if (Value[i] < MinNum || Value[i] > MaxNum)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int j = 0; j < 8; j++)
        {
            attr->PnWifi.get()->PN11ac_MUMIMO.Qmat.QDelay[j] = Value[j];
        }
        
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_QMatMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11acMU(attr);
        if (iRet)
        {
            break;
        }

        int Cnt = context->parser_state.numberOfParameters;
        double Value[Cnt] = {0};
        int Diment = (int)sqrt(Cnt / 2);
        if (0 != (Cnt % 2) || Diment < 1 || Diment > 8) //必须是成对的数据,n*n的二维数组，n（1-8）
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        for (int i = 0; i < context->parser_state.numberOfParameters; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }
        }

        int index = 0;
        for (int j = 0; j < Diment; j++)
        {
            for (int k = 0; k < Diment; k++)
            {
                attr->PnWifi.get()->PN11ac_MUMIMO.Qmat.MatrValue[j][k][0] = Value[index++];
                attr->PnWifi.get()->PN11ac_MUMIMO.Qmat.MatrValue[j][k][1] = Value[index++];
            }
        }
        
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

#define SetMUMIMO_UserValue(attr, UserID, Value, Name)                       \
    do                                                                       \
    {                                                                        \
        if (0 == UserID)                                                     \
        {                                                                    \
            for (int i = 0; i < AC_MUMIMO_4_USER; i++)                       \
            {                                                                \
                attr->PnWifi.get()->PN11ac_MUMIMO.User[i].Name = Value;      \
            }                                                                \
        }                                                                    \
        else                                                                 \
        {                                                                    \
            attr->PnWifi.get()->PN11ac_MUMIMO.User[UserID - 1].Name = Value; \
        }                                                                    \
    } while (0)

scpi_result_t Set11AC_MU_MIMO_MCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int UserID = 0;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOUserIntParam(context, UserID, Value, 0, 13);
        if (iRet)
        {
            break;
        }

        SetMUMIMO_UserValue(attr, UserID, Value, MCS);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_NSS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int UserID = 0;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOUserIntParam(context, UserID, Value, 1, 8);
        if (iRet)
        {
            break;
        }

        SetMUMIMO_UserValue(attr, UserID, Value, NSS);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_MU_MIMO_CodingType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int UserID = 0;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetMUMIMOUserIntParam(context, UserID, Value, 0, 1);
        if (iRet)
        {
            break;
        }

        SetMUMIMO_UserValue(attr, UserID, Value, CodingType);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}