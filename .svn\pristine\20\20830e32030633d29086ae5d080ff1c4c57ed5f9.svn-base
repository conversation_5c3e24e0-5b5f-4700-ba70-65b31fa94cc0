/*
 * @Description: hw_cmd:set or get LO mode,set or get IQ mode
 * @Autor: MaYongFeng
 * @Date: 2022-07-19 12:30:16
 * @Time: 2022-07-19 14:07:35
 */
#include <string>
#include <iostream>
#include <jsoncpp/json/json.h>

#include "scpi_hw_cmd.h"
#include "basehead.h"
#include "commonhandler.h"
#include "tester.h"
#include "wtlog.h"

enum WT_LOCom_Mode
{
    LO_IS_COM_MODE,
    LO_IS_NOT_COM_MODE,
    LO_COM_MODE_IS_UNKNOWED
};

enum WT_IQ_Mode
{
    RFIQ_MODE,
    ANALOGIQ_MODE,
};

scpi_result_t SCPI_GetLoMode(scpi_t *context)
{
    int Number[1] = {0};
    int iRet = WT_ERR_CODE_OK;
    int Mode = LO_COM_MODE_IS_UNKNOWED;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        int ModId = Number[0]-1; //WT448 :ModId为[0-4]//Number[0]为[0-5]
        iRet = WT_GetLoMode(attr->ConnID, &Mode ,ModId);
    }while(0);
    IF_ERR_RETURN(iRet);
    //上位机的开关与下位机相反
    Mode = Mode == LO_IS_COM_MODE ? 1 : 0;
    SCPI_ResultInt(context, Mode);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetLoMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Number[1] = {0};
        int Mode = LO_IS_NOT_COM_MODE;
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        int ModId = Number[0]-1;
        if (!SCPI_ParamInt(context, &Mode, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        //上位机的开关与下位机相反
        Mode = Mode == 1 ? LO_IS_COM_MODE : LO_IS_NOT_COM_MODE;

        iRet = WT_SetLoMode(attr->ConnID, Mode , ModId);
        IF_ERR_RETURN(iRet);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_SetIQMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    EMPTY_PARAM_ERROR(context);

    int *IQMode = new int[context->parser_state.numberOfParameters];
    int ModNum = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (; ModNum < context->parser_state.numberOfParameters && ModNum < MAX_VSA_UNIT_COUNT * (WT_SUB_TESTER_INDEX_MAX + 1); ModNum++)
    {
        if (!SCPI_ParamInt(context, &IQMode[ModNum], true))
        {
            delete[] IQMode;
            return SCPI_RES_ERR;
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI_SetIQMode IQMode[%d]=%d\n", ModNum, IQMode[ModNum]);
    }
    iRet = WT_SetIQMode(attr->ConnID, IQMode, ModNum);
    delete[] IQMode;
    
    IF_ERR_RETURN(iRet);
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetIQMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int IQMode[MAX_VSA_UNIT_COUNT * WT_SUB_TESTER_INDEX_MAX+1] = {RFIQ_MODE};
    int modcount = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    iRet = WT_GetIQMode(attr->ConnID, IQMode, &modcount);
    IF_ERR_RETURN(iRet);
    for (int i = 0; i < modcount; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<__FILE__<<__LINE__<<std::endl;
        SCPI_ResultDouble(context, IQMode[i]);
    }
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<__FILE__<<__LINE__<<std::endl;
    return SCPI_RES_OK;
}
