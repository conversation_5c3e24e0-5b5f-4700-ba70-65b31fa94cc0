#include <winSock2.h>
#include <windows.h>
#include <mstcpip.h>
#include <iostream>
#include "wterrorAll.h"
#include "TypeDef.h"
#include "IOControl_TCP.h"
#include "Logger.h"
#include "Usual.h"
using namespace std;

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib,"WT.Tester.API.Common.lib")
#define FILE_AND_LINE           __FUNCTION__, __LINE__
#define FILE_LINE_FORMAT        "%s,(%d): "

int IOControl_TCP::GetLocalIpPort(char *IP, short *port)
{
    union NetAddrBuf
    {
        char dataBuf[128];
        struct sockaddr addr;
    };

    NetAddrBuf NetAddr;
	SOCKADDR_IN *addr_v4 = nullptr;
	int addr_len = sizeof(NetAddrBuf);
	int iRet = WT_ERR_CODE_GENERAL_ERROR;
	do
	{
		if (m_Socket == INVALID_SOCKET)
		{
			break;
		}

		if (nullptr == IP || nullptr == port)
		{
			break;
		}

        memset(&NetAddr, 0, sizeof(NetAddrBuf));

		if (0 == getsockname(m_Socket, &NetAddr.addr, &addr_len))
		{
			if (NetAddr.addr.sa_family == AF_INET)
			{
				addr_v4 = (SOCKADDR_IN *)&NetAddr.addr;
				*port = ntohs(addr_v4->sin_port);
				strcpy(IP, inet_ntoa(addr_v4->sin_addr));
				iRet = WT_ERR_CODE_OK;
			}
		}
	} while (0);

	return iRet;
}

int IOControl_TCP::IONonBlocking(int on)
{
	unsigned long set_fionbio = on;
	if (ioctlsocket(m_Socket, FIONBIO, (unsigned long *)&set_fionbio) == SOCKET_ERROR)
	{
		Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"ioctlsocket error(%d)", FILE_AND_LINE, IOSetErrorCode(WSAGetLastError()));
		return WT_ERR_CODE_CONNECT_FAIL;
	}
	return WT_ERR_CODE_OK;
}

int IOControl_TCP::IOWriteWait(int timeout_ms)
{
	fd_set  wfds, xfds;
	struct timeval tv;
	struct timeval *tp;
	int  errnum;
	int err = WT_ERR_CODE_OK;

	FD_ZERO(&wfds);
	FD_SET(m_Socket, &wfds);
	FD_ZERO(&xfds);
	FD_SET(m_Socket, &xfds);

	if (timeout_ms > 0)
	{
		tv.tv_sec = timeout_ms / 1000;
		tv.tv_usec = (timeout_ms % 1000) * 1000;

		tp = &tv;
	}
	else
	{
		tp = nullptr;
	}

	WSASetLastError(ERROR_SUCCESS);
	IOSetErrorCode(WSAGetLastError());
	while (true)
	{
		switch (select(1, (fd_set *)0, &wfds, &xfds, tp))
		{
		case -1:
			IOSetErrorCode(WSAGetLastError());
			errnum = IOGetErrorCode();
			if (errnum == WSAEINPROGRESS
				|| errnum == WSAEWOULDBLOCK
				|| errnum == WSAEINTR)
			{
				continue;
			}
			Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"%s select error(%d) %d Ms", FILE_AND_LINE, m_AliasName, errnum, timeout_ms);
			return WT_ERR_CODE_GENERAL_ERROR;
		case 0:
			WSASetLastError(WSAETIMEDOUT);
			IOSetErrorCode(WSAGetLastError());
			Logger::WriteLog(eumLogType_Warning, FILE_LINE_FORMAT"%s select timeout:%d Ms", FILE_AND_LINE, m_AliasName, timeout_ms);
			return WT_ERR_CODE_TIMEOUT;
		default:
			return WT_ERR_CODE_OK;
		}
	}

}

int IOControl_TCP::IOReadWait(int timeout_ms)
{
	fd_set  rfds, xfds;
	struct timeval tv;
	struct timeval *tp;
	int  errnum;

	FD_ZERO(&rfds);
	FD_SET(m_Socket, &rfds);
	FD_ZERO(&xfds);
	FD_SET(m_Socket, &xfds);

	if (timeout_ms > 0)
	{
		tv.tv_sec = timeout_ms / 1000;
		tv.tv_usec = (timeout_ms % 1000) * 1000;

		tp = &tv;
	}
	else
	{
		tp = nullptr;
	}


	WSASetLastError(ERROR_SUCCESS);
	IOSetErrorCode(WSAGetLastError());
	while (true)
	{
		// Windows doesn't let us select on an empty fd_set
		// Windows select() ignores its first argument
		switch (select(1, &rfds, (fd_set *)0, &xfds, tp))
		{
		case -1:
			IOSetErrorCode(WSAGetLastError());
			errnum = IOGetErrorCode();
			if (errnum == WSAEINPROGRESS
				|| errnum == WSAEWOULDBLOCK
				|| errnum == WSAEINTR)
			{
				continue;
			}
			Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"%s select error(%d),%d Ms", FILE_AND_LINE, m_AliasName, errnum, timeout_ms);
			return WT_ERR_CODE_GENERAL_ERROR;
		case 0:
			WSASetLastError(WSAETIMEDOUT);
			IOSetErrorCode(WSAGetLastError());
			Logger::WriteLog(eumLogType_Warning, FILE_LINE_FORMAT"%s select timeout:%d Ms", FILE_AND_LINE, m_AliasName, timeout_ms);
			return WT_ERR_CODE_TIMEOUT;
		default:
			return WT_ERR_CODE_OK;
		}
	}
}

void IOControl_TCP::IOInitializeState()
{
	WORD version = 0;
	WSADATA data;
	if (m_socket_inited)
	{
		return;
	}

	FillMemory(&data, sizeof(WSADATA), 0);

	version = MAKEWORD(2, 0);

	if (WSAStartup(version, &data) != 0)
	{
		Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"%s WSAStartup error(%d)", FILE_AND_LINE, m_AliasName, IOSetErrorCode(WSAGetLastError()));
		return;
	}
	if (LOBYTE(data.wVersion) != 2 || HIBYTE(data.wVersion) != 0)
	{
		WSACleanup();
		Logger::WriteLog(eumLogType_Error, "%s(%d): LOBYTE(data.wVersion) = %d"
			", HIBYTE(data.wVersion) = %d", __FUNCTION__, __LINE__,
			LOBYTE(data.wVersion), HIBYTE(data.wVersion));
		return;
	}

	m_socket_ended = false;
	m_socket_inited = true;
}

void IOControl_TCP::IOTerminateState()
{
	if (m_socket_ended)
	{
		return;
	}
	IODisconnect();

	if (WSACleanup() == SOCKET_ERROR)
	{
		Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"%s WSACleanup error(%d)", FILE_AND_LINE, m_AliasName, IOSetErrorCode(WSAGetLastError()));
		return;
	}
	m_socket_ended = true;
	m_socket_inited = false;
	memset(m_AliasName, 0, sizeof(m_AliasName));
}

int IOControl_TCP::IOConnect(const char *ip, int port, int timeOutMs)
{
	int err = 0;
	SOCKADDR_IN  mSocketAddr;
	do
	{
		if (!m_socket_inited)
		{
			err = WT_ERR_CODE_CONNECT_FAIL;
			break;
		}
		// 校验ip信息
		if (inet_addr(ip) == INADDR_NONE)
		{
			err = WT_ERR_CODE_GENERAL_ERROR;
			break;
		}

		//socket创建
		m_Socket = socket(PF_INET, SOCK_STREAM, 0);
		if (m_Socket == INVALID_SOCKET)
		{
			err = WT_ERR_CODE_CONNECT_FAIL;
			break;
		}

		// 设置非阻塞方式连接
		err = IONonBlocking(1);
		if (WT_ERR_CODE_OK != err)
		{
			err = WT_ERR_CODE_CONNECT_FAIL;
			break;
		}

		// 连接
		mSocketAddr.sin_family = AF_INET;
		mSocketAddr.sin_port = htons(port);
		mSocketAddr.sin_addr.S_un.S_addr = inet_addr(ip);
		connect(m_Socket, (SOCKADDR *)&mSocketAddr, sizeof(SOCKADDR));

		//select 检查socket是否可写，以此来检查socket是否创建成功
		err = IOWriteWait(timeOutMs);
		if (WT_ERR_CODE_OK != err)
		{
			err = WT_ERR_CODE_CONNECT_FAIL;
			break;
		}

		// 设回阻塞模式
		err = IONonBlocking(0);
		if (WT_ERR_CODE_OK != err)
		{
			err = WT_ERR_CODE_CONNECT_FAIL;
			break;
		}

		err = SetSocketOptions();
		if (err == SOCKET_ERROR)
		{
			err = WT_ERR_CODE_CONNECT_FAIL;
			break;
		}
	} while (0);

	if (WT_ERR_CODE_OK != err)
	{
        Logger::WriteLog(eumLogType_Log, "%s Local sock: %4d, Connect server IP: %15s, port: %d Fail!, Error:(%d)",
            m_AliasName,
            m_Socket,
            ip,
            port,
            err);
		IODisconnect();
	}
	else
	{
		char localAddr[16] = { 0 };
		short localPort = -1;
		if (WT_ERR_CODE_OK == GetLocalIpPort(localAddr, &localPort))
		{
			Logger::WriteLog(eumLogType_Log, "%s Connect OK! sock: %4d , local IP: %15s, port: %d ; server IP: %15s, port: %d",
				m_AliasName,
				m_Socket,
				localAddr,
				(unsigned short)localPort,
				ip,
				port);
		}
	}
	return err;
}

int IOControl_TCP::IODisconnect()
{
	int err = 0;
	int sock_err = 0;
	Logger::WriteLog(eumLogType_Log, "%s Disconnect OK! sock: %4d ", m_AliasName, m_Socket);
	WSASetLastError(ERROR_SUCCESS);
	if (m_Socket != INVALID_SOCKET)
	{
		shutdown(m_Socket, SD_BOTH);
		err = closesocket(m_Socket);
		if (0 != err)
		{
			err = closesocket(m_Socket);
		}
	}
	m_Socket = INVALID_SOCKET;

	return WT_ERR_CODE_OK;
}

int IOControl_TCP::IOSockWrite(const char *buf, unsigned int size, unsigned int timeOutMs)
{
	if (timeOutMs > 0 && IOWriteWait(timeOutMs) != WT_ERR_CODE_OK)
	{
		return -1;
	}
	int ret = -1;
	int errnum = 0;
	while (true)
	{
		ret = send(m_Socket, buf, (int)size, 0);
		switch (ret)
		{
		case -1:
			IOSetErrorCode(WSAGetLastError());
			errnum = IOGetErrorCode();
			if (errnum == WSAEINPROGRESS
				|| errnum == WSAEWOULDBLOCK
				|| errnum == WSAEINTR)
			{
				Sleep(1);
				continue;
			}
			Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"%s send len=%d, error=%d", FILE_AND_LINE, m_AliasName, ret, errnum);
			return ret;
		default:
			return ret;
		}
	}
	return ret;
}

int IOControl_TCP::IOTimedWrite(const char *buf, unsigned int len, int timeout_ms)
{

	if (timeout_ms > 0 && IOWriteWait(timeout_ms) != WT_ERR_CODE_OK)
	{
		return -1;
	}
	return IOSockWrite(buf, len, 0);

}

int IOControl_TCP::IOSockRead(char *buf, unsigned int size, unsigned int timeOutMs)
{
	if (timeOutMs > 0 && IOReadWait(timeOutMs) != WT_ERR_CODE_OK)
	{
		return -1;
	}
	int ret = -1;
	int errnum = 0;
	while (true)
	{
		ret = recv(m_Socket, buf, (int)size, 0);
		switch (ret)
		{
		case -1:
			IOSetErrorCode(WSAGetLastError());
			errnum = IOGetErrorCode();
			if (errnum == WSAEINPROGRESS
				|| errnum == WSAEWOULDBLOCK
				|| errnum == WSAEINTR)
			{
				Sleep(1);
				continue;
			}
			Logger::WriteLog(eumLogType_Error, FILE_LINE_FORMAT"%s recv len=%d, error=%d", FILE_AND_LINE, m_AliasName, ret, errnum);
			return ret;
		default:
			return ret;
		}
	}

	return ret;
}

int IOControl_TCP::IOTimedRead(char *buf, unsigned int len, int timeout_ms)
{
	if (timeout_ms > 0 && IOReadWait(timeout_ms) != WT_ERR_CODE_OK)
	{
		return -1;
	}
	return IOSockRead(buf, len, 0);
}

int IOControl_TCP::IOSend(const char *sendBuff, unsigned int buffLength, unsigned int timeOutMs)
{
	const char *mbuf = sendBuff;
	u32 mlen = buffLength;
	s32  time_ms = timeOutMs;

	s32 len;
	u32 onceMaxSendLen = 40 * 1460;
	u32 onceSendLen;
	u32 totalSendLen = mlen;
	s32 err = WT_ERR_CODE_OK;

	WSASetLastError(ERROR_SUCCESS);
	//配置为非阻塞模式
	IONonBlocking(1);
	while (mlen > 0)
	{
		if (onceMaxSendLen > mlen)
		{
			onceSendLen = mlen;
		}
		else
		{
			onceSendLen = onceMaxSendLen;
		}
		len = IOTimedWrite(mbuf, onceSendLen, timeOutMs);
		if (len == onceSendLen)
		{
			mbuf += onceSendLen;
			mlen -= onceSendLen;
		}
		else if (-1 == len || 0 == len)
		{
			Logger::WriteLog(eumLogType_Warning, FILE_LINE_FORMAT"%s len=%d, sock: %d", FILE_AND_LINE, m_AliasName, len, m_Socket);
			return -1;
		}
		else
		{
			break;
		}
	}
	//配置为阻塞模式
	IONonBlocking(0);
	if (mlen == 0)
	{
		return totalSendLen;
	}
	else
	{
		return totalSendLen - mlen;
	}
	return 0;
}

int IOControl_TCP::IORecv(char *recvBuff, unsigned int buffLength, unsigned int timeOutMs)
{
	char *mbuf = recvBuff;
	u32 mlen = buffLength;
	s32  time_ms = timeOutMs;

	s32 len;
	u32 onceMaxRecvLen = 40 * 1460;
	u32 onceRecvLen = 0;
	u32 totalRecvLen = 0;
	s32 err = WT_ERR_CODE_OK;

	WSASetLastError(ERROR_SUCCESS);
	//配置为非阻塞模式
	IONonBlocking(1);
	while (mlen > 0)
	{
		if (onceMaxRecvLen > mlen)
		{
			onceRecvLen = mlen;
		}
		else
		{
			onceRecvLen = onceMaxRecvLen;
		}
		len = IOTimedRead(mbuf, onceRecvLen, timeOutMs);
		if (len > 0)
		{
			mbuf += len;
			mlen -= len;
			totalRecvLen += len;
		}
		else if (-1 == len || 0 == len)
		{
			if (0 == totalRecvLen)
			{
				break;
			}
			Logger::WriteLog(eumLogType_Warning, FILE_LINE_FORMAT"%s len=%d, sock: %d", FILE_AND_LINE, m_AliasName, len, m_Socket);
			break;
		}
	}
	//配置为阻塞模式
	IONonBlocking(0);
	return totalRecvLen;
}

int IOControl_TCP::IOCleanBuff()
{
	unsigned long argp = 0;
	char buff[8192];
	const u32 BuffSize = sizeof(buff);
	int iRet = WT_ERR_CODE_OK;

	//配置为阻塞模式
	IONonBlocking(0);
	do
	{
		if (ioctlsocket(m_Socket, FIONREAD, &argp) != 0)
		{
			Logger::WriteLog(eumLogType_Error, "ioctlsocket FIONREAD");
			iRet = WT_ERR_CODE_GENERAL_ERROR;
			break;
		}

		int recLen = 0;
		int tmpLen = argp;
		while (tmpLen > 0)
		{
			if (tmpLen >= BuffSize)
			{
				recLen = recv(m_Socket, buff, BuffSize, 0);
				if (recLen > 0)
				{
					tmpLen -= recLen;
				}
				else
				{
					//recv error
					break;
				}
			}
			else
			{
				recv(m_Socket, buff, tmpLen, 0);
				break;
			}
		}
	} while (0);

	return iRet;
}

int IOControl_TCP::IOExchange(const char *sendBuff, unsigned int sendBuffLength, char *recvBuff, unsigned int recvBuffLength)
{
	if (0 >= IOSend(sendBuff, sendBuffLength, 100))
	{
		return -1;
	}
	if (0 >= IORecv(recvBuff, recvBuffLength, 5000))
	{
		return -1;
	}

	return 0;
}

IOControl_TCP::IOControl_TCP()
{
	m_Socket = INVALID_SOCKET;
	m_socket_inited = false;
	m_socket_ended = false;
	m_ErrorCode = WT_ERR_CODE_OK;
	strcpy(m_AliasName, __FUNCTION__);
	IOInitializeState();
}

IOControl_TCP::~IOControl_TCP()
{
    IOTerminateState();
#ifdef LINUX
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "IOControl_TCP destruct" << std::endl;
#endif
}

int IOControl_TCP::SetSocketOptions()
{
	char set_nodelay = 1;
	s32 sendBuffSize = 2048;  //设置发送缓冲区大小为2048，不宜设置太大，以提高程序的性能
	s32 sendTimeOut_ms = 3000;   //发送超时3000ms
	s32 merr = 0;
	// 禁止Nagle算法，发送小包数据时不再连包发送，而是立刻发送
	do
	{
		set_nodelay = 1;
		merr = setsockopt(m_Socket, IPPROTO_TCP, TCP_NODELAY, &set_nodelay, sizeof(set_nodelay));
		if (merr == SOCKET_ERROR)
		{
			break;
		}
		merr = setsockopt(m_Socket, SOL_SOCKET, SO_SNDBUF, (const char *)&sendBuffSize, sizeof(sendBuffSize));
		if (merr == SOCKET_ERROR)
		{
			break;
		}

		merr = setsockopt(m_Socket, SOL_SOCKET, SO_SNDTIMEO, (char *)&sendTimeOut_ms, sizeof(sendTimeOut_ms));
		if (merr == SOCKET_ERROR)
		{
			break;
		}

		merr = IOSetKeepAlive(true, 3000, 1000);
		if (merr == SOCKET_ERROR)
		{
			break;
		}
	} while (0);
	return merr;
}

int IOControl_TCP::IOSetKeepAlive(bool Enable, int KeepIdleMs, int KeepIntvlMs)
{
	int Val = 0;
	int iRet = 0;
	if (Enable)
	{
		Val = 1;
		iRet = setsockopt(m_Socket, SOL_SOCKET, SO_KEEPALIVE, (char *)&Val, sizeof(Val));

		tcp_keepalive in;
		unsigned long ulBytesReturn = 0;
		memset(&in, 0, sizeof(in));
		/*The value specified in the onoff member determines if TCP keep-alive is enabled or disabled.
		If the onoff member is set to a nonzero value, TCP keep-alive is enabled and the other members in the structure are used.
		The keepalivetime member specifies the timeout, in milliseconds, with no activity until the first keep-alive packet is sent.
		The keepaliveinterval member specifies the interval, in milliseconds, between when successive keep-alive packets are sent if no acknowledgement is received*/
		in.keepalivetime = KeepIdleMs;
		in.keepaliveinterval = KeepIntvlMs;
		in.onoff = 1;

		iRet = WSAIoctl(m_Socket,   /*A descriptor identifying a socket*/
			SIO_KEEPALIVE_VALS,     /*The control code for the operation. Use SIO_KEEPALIVE_VALS for this operation*/
			&in,                    /*A pointer to the input buffer. This parameter should point to a tcp_keepalive structure*/
			sizeof(in),             /*The size, in bytes, of the input buffer. This parameter should equal to or greater than the size of the tcp_keepalive structure pointed to by the lpvInBuffer parameter*/
			nullptr,                /*A pointer to the output buffer. This parameter is unused for this operation.*/
			0,                      /*The size, in bytes, of the output buffer. This parameter must be set to zero*/
			&ulBytesReturn,         /*A pointer to a variable that receives the size, in bytes, of the data stored in the output buffer. This returned parameter points to a DWORD value of zero for this operation, since there is no output*/
			nullptr,                /*A pointer to a WSAOVERLAPPED structure,If socket s was created without the overlapped attribute, the lpOverlapped parameter is ignored*/
			nullptr);               /*A pointer to the completion routine called when the operation has been completed (ignored for non-overlapped sockets).*/
	}
	else
	{
		iRet = setsockopt(m_Socket, SOL_SOCKET, SO_KEEPALIVE, (char *)&Val, sizeof(Val));
	}

	return iRet;
}

int IOControl_TCP::IOGetConnectState()
{
	int errnum = true;
	if (m_Socket != INVALID_SOCKET)
	{
		errnum = IOGetErrorCode();
		if (errnum == ERROR_SUCCESS
			|| errnum == WSAEINPROGRESS
			|| errnum == WSAEWOULDBLOCK
			|| errnum == WSAEINTR
			|| errnum == WSAETIMEDOUT)
		{
			errnum = true;
		}
		else
		{
			errnum = false;
		}
	}
	return (m_Socket != INVALID_SOCKET) && m_socket_inited && errnum;
}

int IOControl_TCP::IOGetHandle()
{
	return m_Socket;
}

int IOControl_TCP::IOGetErrorCode()
{
	return m_ErrorCode;
}
int IOControl_TCP::IOSetErrorCode(int code)
{
	m_ErrorCode = code;
	return m_ErrorCode;
}

void IOControl_TCP::IOSetAliasName(const char *name)
{
	if (nullptr != name)
	{
		memset(m_AliasName, 0, sizeof(m_AliasName));
		strcpy(m_AliasName, name);
	}
}

const char *IOControl_TCP::IOGetAliasName()
{
	return m_AliasName;
}

int IOControl_TCP::IOGetConnectDetail(char *buffer, int buffersize)
{
	char localAddr[16] = { 0 };
	short localPort = -1;
	int iRet = GetLocalIpPort(localAddr, &localPort);
	if (WT_ERR_CODE_OK == iRet)
	{
		if (buffersize >= sizeof(localAddr) + sizeof(int))
		{
			sprintf(buffer, "%s:%d", localAddr, (unsigned short)localPort);
            Logger::WriteLog(eumLogType_Log, "%s sock: %4d, local IP: %s, port: %d",
                m_AliasName,
                m_Socket,
                localAddr,
                (unsigned short)localPort);
		}
	}
	return iRet;
}