//*****************************************************************************
//  File: xxxx.h
//  基类
//  Data:
//*****************************************************************************
#ifndef __WT_BASE_H__
#define __WT_BASE_H__

#include "wtev++.h"
#include "wterror.h"
#include "devlib.h"
#include "devmgr.h"

class WTBase
{
public:
    WTBase(const wtev::loop_ref &Loop, WT_DEV_RES_TYPE ModType)
        : m_FinishEv(Loop),
          m_ModType(ModType)
    {
        // WTBase 类的硬件完成回调暂时不用先
        m_FinishEv.set<WTBase, &WTBase::HwOpFin>(this);
        m_FinishEv.start();
        m_Notify = std::bind(&WTBase::Complete, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
    }

    virtual ~WTBase()
    {
        m_FinishEv.stop();
        m_Notify = nullptr;
        FreeMod();
    }

    int SetRFLinkState(int state)
    {
        return DevLib::Instance().SetRFPort(m_AllocModId, static_cast<WT_DEV_TYPE>(m_ModType), m_RFPort, state);
    }

    void FreeMod()
    {
        if (m_AllocModId != -1)
        {
            SetRFLinkState(WT_RF_STATE_OFF);
            DevMgr::Instance().FreeMod(m_ModType, m_AllocModId);
            m_AllocModId = -1;
        }
    }

public:
    // WTBase 类的硬件完成回调暂时不用先
    void Complete(int Type, int DevId, int Status)
    {
        (void)Type;
        (void)DevId;
        (void)Status;
        return;
    }

    // WTBase 类的硬件完成回调暂时不用先
    void HwOpFin(wtev::async &watcher, int revents)
    {
        (void)watcher;
        (void)revents;
        return;
    }

    int AllocMod(NotifyFunc Notify, int TimeOut, int key = 181)
    {
        int Ret = DevMgr::Instance().AllocMod(m_ModType, Notify, TimeOut, key, m_AllocModId, m_NeedModId);
        if (Ret != WT_OK)
        {
            m_AllocModId = -1;
            //WTLog::Instance().WriteLog(LOG_DEBUG, "[WTBase]AllocMod Fail, m_ModType=%d m_NeedModId=%d Ret=%d[0X%X]\n", m_ModType, m_NeedModId, Ret, Ret);
            WTLog::Instance().WriteLog(LOG_SUB_MGR, "[WTBase]AllocMod Fail, m_ModType=%d m_NeedModId=%d Ret=%d[0X%X]\n",
                                       m_ModType, m_NeedModId, Ret, Ret);
        }

        return Ret;
    }

    // 指定当前校准的端口, 1-8，请调用者检测参数RFPort的范围
    int SetRFPort(int RFPort)
    {
        int Ret = WT_OK;
        m_RFPort = RFPort;
        m_NeedModId = -1;

        if (m_ModType == DEV_RES_VSA)
        {
            Ret = DevLib::Instance().GetModId(DEV_TYPE_VSA, m_RFPort, m_NeedModId);
        }
        else if (m_ModType == DEV_RES_VSG)
        {
            Ret = DevLib::Instance().GetModId(DEV_TYPE_VSG, m_RFPort, m_NeedModId);
        }
        else
        {
            Ret = WT_UNITBOARD_TYPE_ERROR;
        }

        return Ret;
    }

    // 记录当前单元的运行状态
    void SetModRunState(bool state)
    {
        m_ModHasRun = state;
    }

    // 指定待校准的频点
    void SetFreq(int Freq)
    {
        m_Freq = Freq;
    }

    // 待校准频率MHz
    int GetFreqMHz(void)
    {
        return m_Freq;
    }

    // 待校准频率Hz
    double GetFreqHz(void)
    {
        return m_Freq * 1000000.0;
    }

    // 待校准端口
    int GetRFPort(void)
    {
        return m_RFPort;
    }

    int GetModId(void)
    {
        return m_AllocModId;
    }
    
    virtual int Stop(bool ForceSetSwitch=true) = 0;
    virtual int Start() = 0;
    virtual int SetMod(double power) = 0;

private:
    // std::unique_ptr<char *> m_Param; // 当前硬件的参数
protected:
    wtev::async m_FinishEv; // 硬件操作完成异步事件
    NotifyFunc m_Notify;
    WT_DEV_RES_TYPE m_ModType; // = DEV_RES_VSA;    // 单元的类型, vsa 或者 vsg
    int m_RFPort = -1;         // 1-8 RF1-RF8
    int m_NeedModId = -1;      // 端口对应的单元
    int m_AllocModId = -1;     // 实际分配到的单元
    bool m_ModHasRun = false;  // 分配的模块是否已经运行
    int m_Freq;                // 当前待校准的频点
};

#endif
