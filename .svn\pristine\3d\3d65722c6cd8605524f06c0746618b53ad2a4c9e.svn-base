#include "basehead.h"
#include <iostream>
#include "commonhandler.h"
#include "scpi_3gpp_alz_lte.h"
#include "scpi_3gpp_base.h"

#include "cellular_param_from_json.h"
#include "cellular_param_to_json.h"
#include "wtlog.h"

#define SHOW_3GPP_LTE_ALZ_PARAM_VALUE (1)

namespace {
    // 定义各制式支持的频率值(单位:Hz)
    constexpr int WCDMA_FREQS[] = {2000};
    constexpr int NR_FREQS[] = {15000, 30000, 60000, 120000};
    constexpr int NBIOT_FREQS[] = {3750, 15000, 30000};

    // 检查LTE频率值是否有效
    bool isValidLTEFreq(int freq) {
        // 检查频率是否在范围内且为10的倍数
        return (freq >= 1000 && freq <= 500000) && (freq % 10 == 0);
    }

    // 检查值是否在给定数组中
    bool isValueInArray(int value, const int arr[], size_t size) {
        return std::find(arr, arr + size, value) != arr + size;
    }

    // 制式与频率映射结构 
    struct StandardFreqs {
        std::function<bool(int)> validator;
    };

    // 制式到频率的映射表
    const std::map<int, StandardFreqs> STANDARD_FREQ_MAP = {
        {ALG_3GPP_STD_NB_IOT, {isValidLTEFreq}},
        {ALG_3GPP_STD_4G, {isValidLTEFreq}},
        {ALG_3GPP_STD_5G, {[](int f) { return isValueInArray(f, NR_FREQS, sizeof(NR_FREQS)/sizeof(int)); }}},
        {ALG_3GPP_STD_WCDMA, {[](int f) { return isValueInArray(f, WCDMA_FREQS, sizeof(WCDMA_FREQS)/sizeof(int)); }}}
    };
}


static int BinaryFind(int array[], int len, int target)
{
    int left = 0;
    int right = len - 1;
    while (left <= right)
    {
        int mid = (left + right) / 2;

        if (array[mid] == target)
        {
            return mid;
        }
        else if (array[mid] > target)
        {
            right = mid - 1;
        }
        else
        {
            left = mid + 1;
        }
    }

    return -1;
}

bool endsWith(std::string const &str, std::string const &suffix)
{
    if (str.length() < suffix.length())
    {
        return false;
    }
    return str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

bool is_3gpp_wave_ref_match_channel_type(int LinkDirect, int ChannType)
{
    bool match = false;
    if (LinkDirect == ALG_3GPP_UL)
    {
        switch (ChannType)
        {
        case ALG_4G_PUSCH:
        case ALG_4G_PUCCH:
            match = true;
            break;
        default:
            break;
        }
    }
    else
    {
        switch (ChannType)
        {
        case ALG_4G_PDSCH:
        case ALG_4G_PDCCH:
            match = true;
            break;
        default:
            break;
        }
    }
    return match;
}

// static void GetAlzParamFromRef3GPP(AlzParam3GPP &analyzeParam3GPP, SPCIUserParam::RefAlzConfig3GPP &RefAnalyzeParam3GPP, int subframe, int chanType)
// {
//     // 设置基本参数
//     analyzeParam3GPP.Standard = RefAnalyzeParam3GPP.Standard;

//     if (RefAnalyzeParam3GPP.IsLoadRef == 0)
//     {
//         RefAnalyzeParam3GPP.IsUseRef = 0;
//     }
//     else
//     {
//         if (is_3gpp_wave_ref_match_channel_type(RefAnalyzeParam3GPP.LinkDirect, chanType))
//         {
//             RefAnalyzeParam3GPP.IsUseRef = 1;
//         }
//         else
//         {
//             RefAnalyzeParam3GPP.IsUseRef = 0;
//         }
//     }

//     if (RefAnalyzeParam3GPP.IsUseRef == 0)
//     {
//         SCPI_AlzParam vsaAlzParam;
//         vsaAlzParam.Reset_AlzParam_3gpp(&analyzeParam3GPP, chanType);
//     }
//     else
//     {
//         WTLog::Instance().WriteLog(LOG_DEBUG, "Get AlzParam subframe = %d chanType = %d\n", subframe, chanType);
//         memcpy(&analyzeParam3GPP.LTE, &RefAnalyzeParam3GPP.RefAlg_3GPP_AlzIn4g[subframe], sizeof(RefAnalyzeParam3GPP.RefAlg_3GPP_AlzIn4g[subframe]));
//     }
// }

static void CopyAlzParam3GPP(AlzParam3GPP_SCPI *d, const AlzParam3GPP *s)
{
    d->Version = 1;
    d->analyzeGroup = s->analyzeGroup;
    d->DcFreqCompensate = s->DcFreqCompensate;
    d->Standard = s->Standard;
    d->SpectrumRBW = s->SpectrumRBW;
    memcpy(d->rf_band, s->rf_band, sizeof(d->rf_band));
    memcpy(d->rf_channel, s->rf_channel, sizeof(d->rf_channel));

    switch (d->Standard)
    {
    case ALG_3GPP_STD_4G:
        memcpy(&d->Param.LTE, &s->LTE, sizeof(d->Param.LTE));
        break;
    case ALG_3GPP_STD_5G:
        memcpy(&d->Param.NR, &s->NR, sizeof(d->Param.NR));
        break;
    case ALG_3GPP_STD_NB_IOT:
        memcpy(&d->Param.NBIOT, &s->NBIOT, sizeof(d->Param.NBIOT));
        break;
    case ALG_3GPP_STD_WCDMA:
        memcpy(&d->Param.WCDMA, &s->WCDMA, sizeof(d->Param.WCDMA));
        break;
    default:
        break;
    }
}

scpi_result_t SCPI_3GPP_GetAnalyParamJson(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Version = 1;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamInt(context, &Version, true))
        {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        }

        if (attr->vsaAlzParam.analyzeParam3GPP.ErrorCode != WT_ERR_CODE_OK)
        {
            iRet = attr->vsaAlzParam.analyzeParam3GPP.ErrorCode + WT_ALG_3GPP_BASE_ERROR;
            break;
        }

        nlohmann::json root;
        if (IsAlg3GPPStandardType(attr->vsaParam.Demode))
        {
            cellular_param_to_json(root, attr->vsaAlzParam.analyzeParam3GPP);
        }

        string strJson = root.dump();

        WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI_3GPP_GetAnalyParamJson:\n%s\n", strJson.c_str());

        SCPI_ResultArbitraryBlock(context, strJson.c_str(), strJson.length());
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_GetAnalyParamArb(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Version = 1;
    // int SubFrame = 0;
    // int ChannType = 0;

    // verison, 分析结构体参数的版本号

    // <subfream> <ChannType>
    // -1, 取当前分析配置

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamInt(context, &Version, true))
        {
            //    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            //    break;
        }

        // SCPI_ParamInt(context, &ChannType, false);
        // WTLog::Instance().WriteLog(LOG_DEBUG, "SubFrame=%d ChannType=%d\n", SubFrame, ChannType);
        // if (SubFrame == -1)
        // {
        //     // TODO 是否需要判断协议类型?
        //     // SCPI_ResultArbitraryBlock(context, (const char *)&(attr->vsaAlzParam.analyzeParam3GPP), sizeof(attr->vsaAlzParam.analyzeParam3GPP));
        // }
        // else
        // {
        //     GetAlzParamFromRef3GPP(attr->vsaAlzParam.analyzeParam3GPP, attr->m_RefAnalyzeParam3GPP, SubFrame, ChannType);
        //     // SCPI_ResultArbitraryBlock(context, (const char *)&(attr->vsaAlzParam.analyzeParam3GPP), sizeof(attr->vsaAlzParam.analyzeParam3GPP));
        // }

        if (IsAlg3GPPStandardType(attr->vsaParam.Demode))
        {
            AlzParam3GPP_SCPI *pParam = new AlzParam3GPP_SCPI();
            bzero(pParam, sizeof(AlzParam3GPP_SCPI));

            CopyAlzParam3GPP(pParam, &(attr->vsaAlzParam.analyzeParam3GPP));

            // PrintAlg_3GPP_AlzInNBIOT(&(pParam->Param.NBIOT));

            SCPI_ResultArbitraryBlock(context, (const char *)pParam, sizeof(AlzParam3GPP_SCPI));

            delete pParam;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

// 简单加密
inline unsigned char SimpleEncryp(unsigned char c)
{
    return ~(((c & 0x93) & 0x93) | (c & 0x6c));
}

scpi_result_t SCPI_3GPP_SetAnalyParamRefLoadArb(scpi_t *context)
{
#define PAYLOAD_3GPP_PARAM_OFFSET ((4) * sizeof(int))
    int iRet = WT_ERR_CODE_OK;
    const char *data = nullptr;
    size_t len = 0;

    if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
    {
        return SCPI_RES_ERR;
    }

    do
    {
        if (len < PAYLOAD_3GPP_PARAM_OFFSET)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        // 文件头部对比, 结构 Header1,Header2,Demode,Length,Payload
        const int *Header = (const int *)data;
        const int Demode = Header[2];
        const int Length = Header[3];

        if (Length <= 0)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        // 数据完整性
        // WTLog::Instance().WriteLog(LOG_DEBUG, "ARB len=%lu Payload Length=%d Total=%lu\n", len, Length, Length + PAYLOAD_3GPP_PARAM_OFFSET);
        if ((Length + PAYLOAD_3GPP_PARAM_OFFSET) != len)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (Header[0] != WAVE_CFG_BASE_HEAD)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Demode != attr->vsaAlzParam.analyzeParam3GPP.Standard)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Demode do not match! Set Demode=%d Cur Demode=%d\n", Demode, attr->vsaAlzParam.analyzeParam3GPP.Standard);
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (is3GPPWaveCfgHead(Header[1]))
        {
#if 1
            // 解密, 注意必须与API加密过程匹配使用
            unsigned char *Payload = (unsigned char *)(data + PAYLOAD_3GPP_PARAM_OFFSET);
            for (int i = 0; i < Length; ++i)
            {
                *(Payload + i) = SimpleEncryp(*(Payload + i));
            }
#endif

            WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI_3GPP_SetAnalyParamRefLoadArb\n%s\n", data + PAYLOAD_3GPP_PARAM_OFFSET);

            nlohmann::json JsonRoot;
            JsonRoot = nlohmann::json::parse(data + PAYLOAD_3GPP_PARAM_OFFSET, data + PAYLOAD_3GPP_PARAM_OFFSET + Length);
            if (JsonRoot.empty())
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI_3GPP_SetAnalyParamRefLoadArb JsonRoot is empty\n");
                iRet = WT_ERR_CODE_GENERAL_ERROR;
                break;
            }

            iRet = set_cellular_param_from_json(attr->vsaAlzParam.analyzeParam3GPP, JsonRoot);
        }
        else
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
        }
    } while (0);

#undef PAYLOAD_3GPP_PARAM_OFFSET
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAnalyParamRefClear(scpi_t *context)
{
    (void)context;
    // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    // attr->m_RefAnalyzeParam3GPP.IsLoadRef = 0;
    return SCPI_ResultOK(context, SCPI_RES_OK);
}

scpi_result_t SCPI_3GPP_SetAnalysisParamArb(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;

    const char *data = nullptr;
    size_t len = 0;
    AnalyzeParam AlzParam;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->vsaAlzParam.Reset_AlzParam(AlzParam.analyzeParam3GPP);
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        if (len < sizeof(AlzParam3GPP))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        memcpy(&attr->vsaAlzParam.analyzeParam3GPP, data, sizeof(attr->vsaAlzParam.analyzeParam3GPP));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzGroup(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.analyzeGroup = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetRfBand(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int StreamID = 0;
    do
    {
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.rf_band[StreamID] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "StreamID=" << StreamID << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetRfChannel(scpi_t *context)
{
    extern BandChanItem LTE_ULBandChanMap[];
    extern BandChanFreqItem LTE_DLBandChanFreqMap[];
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int StreamID = 0;
    do
    {
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->vsaAlzParam.analyzeParam3GPP.Standard != ALG_3GPP_STD_4G)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.rf_channel[StreamID] = Value;

        int LinkDirect = 0;
        switch (attr->vsaAlzParam.analyzeParam3GPP.Standard)
        {
            case ALG_3GPP_STD_WCDMA:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.WCDMA.LinkDirect;
                break;
            case ALG_3GPP_STD_4G:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.LTE.ChanType;
                break;
            case ALG_3GPP_STD_5G:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.NR.LinkDirect;
                break;
            case ALG_3GPP_STD_NB_IOT:
                LinkDirect = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect;
                break;
            default:
                break;
        }

        if (LinkDirect == ALG_3GPP_UL)
        {
            for (int i = 0; i < BAND_CHANNEL_ITEM_MAX; i++)
            {
                if (Value > LTE_ULBandChanMap[i].ChannelMax)
                {
                    continue;
                }

                if (Value >= LTE_ULBandChanMap[i].ChannelMin)
                {
                    if (StreamID == 0)
                    {
                        attr->vsaParam.Freq = (Value + LTE_ULBandChanMap[i].Delta) / 10.0 * MHz_API;
                    }
                    else
                    {
                        attr->vsaParam.Freq2 = (Value + LTE_ULBandChanMap[i].Delta) / 10.0 * MHz_API;
                    }
                }
                else
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            }
            IF_BREAK(iRet);
        }
        else if (attr->vsaAlzParam.analyzeParam3GPP.Standard == ALG_3GPP_STD_4G && LinkDirect == ALG_4G_PDSCH)
        {
            for (int i = 0; i < BAND_CHANNEL_ITEM_MAX; i++)
            {
                if (Value > LTE_DLBandChanFreqMap[i].ChannelMax)
                {
                    continue;
                }

                if (Value >= LTE_DLBandChanFreqMap[i].N_ref)
                {
                    if (StreamID == 0)
                    {
                        attr->vsaParam.Freq = ((Value - LTE_DLBandChanFreqMap[i].N_ref) / 10.0 + LTE_DLBandChanFreqMap[i].F_ref) * MHz_API;
                    }
                    else
                    {
                        attr->vsaParam.Freq2 = ((Value - LTE_DLBandChanFreqMap[i].N_ref) / 10.0 + LTE_DLBandChanFreqMap[i].F_ref) * MHz_API;
                    }
                }
                else
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            }
            IF_BREAK(iRet);
        }
        else
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "StreamID=" << StreamID << "; Value=" << Value << "; Freq1=" << attr->vsaParam.Freq << "; Freq2=" << attr->vsaParam.Freq2 << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAnalyMeasureCommonParam(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int StreamID = 0;
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        const size_t arraySize = 3;
        std::array<bool, arraySize> Value = {false};

        for (int i = 0; i < arraySize; i++)
        {
            scpi_bool_t  tmpValue = false;
            if (!SCPI_ParamBool(context, &tmpValue, true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            Value[i] = tmpValue;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasPowerGraph = Value[0];
        attr->vsaAlzParam.analyzeParam3GPP.MeasSpectrum = Value[1];
        attr->vsaAlzParam.analyzeParam3GPP.MeasCCDF = Value[2];
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value[0]=" << Value[0] << "; Value[1]=" << Value[1] << "; Value[2]=" << Value[2] << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzDcFreqCompensate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.DcFreqCompensate = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpectrumRBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1000 || Value > 500000 || Value % 10 != 0) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.SpectrumRBW = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasurePower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasPowerGraph = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureCcdf(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasCCDF = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureSpectrum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.MeasSpectrum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.CyclicPrefix = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.UeID = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzChannalType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < ALG_4G_PUSCH || Value > ALG_4G_PHICH)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        // attr->vsaAlzParam.Reset_3GPPAlzLteChanParam(attr->vsaAlzParam.analyzeParam3GPP.LTE, Value);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.ChanType = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPkgOffset(scpi_t *context)
{

    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.PkgAlzOffset = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzParamDemode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = ALG_3GPP_STD_5G;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!IsAlg3GPPStandardType(Value))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.Reset_AlzParam(attr->vsaAlzParam.analyzeParam3GPP, Value);

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzNetSignal(scpi_t *context)
{

    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 288)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.NSValue = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_3GPP_SetAlzLinkDirect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = ALG_3GPP_UL;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_UL && Value != ALG_3GPP_DL)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (IsAlg3GPPStandardType(attr->vsaAlzParam.analyzeParam3GPP.Standard))
        {
            attr->vsaAlzParam.Reset_AlzParam(attr->vsaAlzParam.analyzeParam3GPP, attr->vsaAlzParam.analyzeParam3GPP.Standard, Value);
        }

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCAState(scpi_t *context)
{

    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.CarrAggrState = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != CellID)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].CellIdx = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].State = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPhyID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].PhyCellID = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    scpi_number_t rate;
    int ChannelBW = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        ChannelBW = rate.value;
        switch (ChannelBW)
        {
        case 1400000:
        case 3000000:
        case 5000000:
        case 10000000:
        case 15000000:
        case 20000000:
            ChannelBW = rate.value;
            break;
        default:
            break;
        }

        if (ChannelBW == 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].ChannelBW = ChannelBW;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << ChannelBW << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellDUPLexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].Duplexing = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
scpi_result_t SCPI_3GPP_SetAlzCACellULDLConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].ULDLConfig = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellSpecialSubframeConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].SpecialSubfrmConfig = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != CellID)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].CellIdx = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].State = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschRBConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RBCount = 0;
    int RBOffset = 0;
    int CellID = 0;
    int CellRBMax = -1;
    int Find = -1;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &RBCount, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &RBOffset, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        switch (attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[CellID].ChannelBW)
        {
        case 1400000:
            CellRBMax = pusch_pb_1_4[sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]) - 1];
            Find = BinaryFind(pusch_pb_1_4, sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]), RBCount);
            break;
        case 3000000:

            CellRBMax = pusch_pb_3[sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]) - 1];
            Find = BinaryFind(pusch_pb_3, sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]), RBCount);
            break;
        case 5000000:
            CellRBMax = pusch_pb_5[sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]) - 1];
            Find = BinaryFind(pusch_pb_5, sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]), RBCount);
            break;
        case 10000000:
            CellRBMax = pusch_pb_10[sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]) - 1];
            Find = BinaryFind(pusch_pb_10, sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]), RBCount);
            break;
        case 15000000:
            CellRBMax = pusch_pb_15[sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]) - 1];
            Find = BinaryFind(pusch_pb_15, sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]), RBCount);
            break;
        case 20000000:
            CellRBMax = pusch_pb_20[sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]) - 1];
            Find = BinaryFind(pusch_pb_20, sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]), RBCount);
            break;
        default:
            break;
        }

        if (Find == -1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellRBMax <= 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (RBCount + RBOffset > CellRBMax)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].RBNum = RBCount;
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].RBOffset = RBOffset;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RBCount=" << RBCount << " RBOffset=" << RBOffset << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Precoding = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschLayerNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].LayerNum = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].AntennaNum = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschCodebookIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].CodebookIdx = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
scpi_result_t SCPI_3GPP_SetAlzCACellPuschGroupHop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].GroupHop = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschSequenceHop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].SequenceHop = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschDeltaSeqShift(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 29)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].DeltaSeqShift = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschN1Dmrs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;
    static int Choice[] = {0, 2, 3, 4, 6, 8, 9, 10};
    bool Match = false;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < (sizeof(Choice) / sizeof(Choice[0])); ++i)
        {
            if (Choice[i] == Value)
            {
                Match = true;
                break;
            }
        }

        if (!Match)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].N1Dmrs = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschCyclicShiftField(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].CyclicShiftField = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschCodeword(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Codeword = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Indx = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Modulate[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Indx=" << Indx << " Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschChanDecodeState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].ChanCodingState = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschScramble(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Scramble = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschMCSMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].ChanDecodeState == 0)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].McsCfgMode = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschMCSCfg(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int Indx = 0;
    int Mcs = 1;
    int PayloadSize = 0;
    int RedunVerIdx = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Mcs, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].ChanDecodeState == 0 || attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].McsCfgMode == 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        // if (Mcs < UL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Enable256QAM][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Modulate[Indx]) / 2][0]
        // || Mcs > UL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Enable256QAM][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Modulate[Indx]) / 2][1])
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        if (!SCPI_ParamInt(context, &PayloadSize, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (PayloadSize < 1 || PayloadSize > 253440)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &RedunVerIdx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (RedunVerIdx < 0 || RedunVerIdx > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Mcs[Indx] = Mcs;
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].PayloadSize[Indx] = PayloadSize;
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].RedunVerIdx[Indx] = RedunVerIdx;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Indx=" << Indx << " Mcs=" << Mcs << " PayloadSize=" << PayloadSize << " RedunVerIdx=" << RedunVerIdx << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschEnable256QAM(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].ChanDecodeState == 0 || attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].McsCfgMode == 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        // if ((attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Modulate[0] == 8 || attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Modulate[1] == 8) && Value != 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].Enable256QAM = Value;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzCACellPuschRBDetMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pusch[CellID].RBDetMode = Value;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasSubFrame(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasSubfrmIdx = Value;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasDmrsConsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.DmrsConsState = Value;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureUnit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasureUnit = Value;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzMeasureExcludeAbnormalSymbol(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        //SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        //attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.ExAbnSymbFlg = Value;

#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        cout << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSEMLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMLimitMode = Value;*/  //先屏蔽，待合并分支时根据实际情况放开
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSEMLimitState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_3GPP_SEM_LIM_SET)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMLimit[Idx].State = Value;*/  //先屏蔽，待合并分支时根据实际情况放开
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSEMLimitStartFreq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_3GPP_SEM_LIM_SET)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 25)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMLimit[Idx].StartFreq = Value;*/  //先屏蔽，待合并分支时根据实际情况放开

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSEMLimitStopFreq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_3GPP_SEM_LIM_SET)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 25)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMLimit[Idx].StopFreq = Value;*/  //先屏蔽，待合并分支时根据实际情况放开

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSEMLimitPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_3GPP_SEM_LIM_SET)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -100 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMLimit[Idx].LimitPower = Value;*/  //先屏蔽，待合并分支时根据实际情况放开

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSEMLimitRBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_3GPP_SEM_LIM_SET)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 30 * KHz && Value != 100 * KHz && Value != 1 * MHz)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        //SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        //attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMLimit[Idx].RBW = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSyncMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.SyncMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSubframeOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasSubfrmOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSubframeCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasSubfrmCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSlotType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasSlotType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvm(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.EvmEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationMerr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.MErrEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationPerr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.PErrEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvmSubcarrier(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.EvmSubcarEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationIbe(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.IBEEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEsFlat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.ESFlatEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationIQConst(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.IQConstelEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerDynamic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.PwrDynEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumAclr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.ACLREnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEmissionMask(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.SpectEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumTxMeas(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.TxMeasureEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumDecodingResult(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.DecodingEnable = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumDmrsCons(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.DmrsConsState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbol(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.EvmSymbEnable = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbolIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.EvmSymbIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbolWindowType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.EvmSymbWinType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.ModEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumOBWEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.OBWEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.SEMEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerPmonEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.PMonitorEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.PowerEnable = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationStaticCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.ModStatNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvmWindowNcp(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        constexpr int arrsize = ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.EvmWinNcp);
        int arr[arrsize] = {0};
        for (int i = 0; i < arrsize; i++)
        {
            if (!SCPI_ParamInt(context, &arr[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        memcpy(attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.EvmWinNcp, arr, arrsize);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEvmWindowEcp(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        constexpr int arrsize = ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.EvmWinEcp);
        int arr[arrsize] = {0};
        for (int i = 0; i < arrsize; i++)
        {
            if (!SCPI_ParamInt(context, &arr[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        memcpy(attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.EvmWinEcp, arr, arrsize);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationExpPeriodLead(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 0 || Value > 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.ExPeriodLeading = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationExpPeriodLag(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 0 || Value > 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.ExPeriodLagging = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationExpAbnormalSymbol(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.ExAbnSymbFlg = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureModulationEqualizer(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 0 || Value > 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Modulate.Equalizer = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMStatNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.SEMStatNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMMeasFilter(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 1 || Value > 1000)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.MeasFilter = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumSEMACLRStatNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.ACLRStatNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumUTRAEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Index = 0;
        int Value = 0;
        if (!SCPI_CommandNumbers(context, &Index, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Index < 1 || Index > 2 || Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Index == 1)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.UTRA1Enable = static_cast<char>(Value & 0xFF);
        }
        else
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.UTRA2Enable = static_cast<char>(Value & 0xFF);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasureSpectrumEUTRAEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Index = 0;
        int Value = 0;
        if (!SCPI_CommandNumbers(context, &Index, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Index < 1 || Index > 2 || Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Index == 1)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.EUTRA1Enable = static_cast<char>(Value & 0xFF);
        }
        else
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Spectrum.EUTRA2Enable = static_cast<char>(Value & 0xFF);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerTimeMask(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 1 || Value > 1000)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.TimeMaskType = static_cast<char>(Value & 0xFF);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerTimeLead(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -1000 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.Leading = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerTimeLag(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -1000 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.Lagging = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerStaticCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.PowerStatNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzLteMeasurePowerHighDynamicMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 1 || Value > 1000)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.HighDynmMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_3GPP_SetAlzMeasEvmCfg(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int State = 0;
    int Indx = 0;
    int PosType = 0;

    do
    {
        if (!SCPI_ParamInt(context, &State, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &PosType, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (State != 0 && State != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (PosType != 0 && PosType != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.EvmSubcarrierState = State;
        // attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.EvmSymbIndx = Indx;
        // attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.EvmSymbPosType = PosType;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "State=" << State << " Indx=" << Indx << " PosType=" << PosType << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschSymbOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.SymbOffset = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschResourceAllocation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.ResAllocateType = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        cout << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschVRBAssignment(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.VRBAssignment = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        cout << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRBGBitmap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int tmpValue = 0;
    char Buf[128] = {0};
    int len = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int ChannelBW = attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[0].ChannelBW;
    switch (ChannelBW)
    {
        case 1400000:
            len = 6;
            break;
        case 3000000:
            len = 8;
            break;
        case 5000000:
            len = 13;
            break;
        case 10000000:
            len = 17;
            break;
        case 15000000:
            len = 19;
            break;
        case 20000000:
            len = 25;
            break;
        default:
            break;
    }

    do
    {
        for (int i = len - 1; i >= 0; i--)
        {
            if (!SCPI_ParamInt(context, &tmpValue, true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (tmpValue < 0 || tmpValue > 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            
            Buf[i] = (char)tmpValue;
        }
        IF_BREAK(iRet);

        memcpy(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RBGBitmap, Buf, len);
#if DEBUG_SHOW_LTE
        std::cout << "attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RBGBitmap = " << std::endl;
        for (int i = 0; i < len; i++)
        {
            std::cout << static_cast<int>(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RBGBitmap[i]);
        }
        std::cout << std::endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RBNum = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
scpi_result_t SCPI_3GPP_SetAlzPdschRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 99)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RBOffset = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPbchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.PbchState = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Precoding = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschLayerNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.LayerNum = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.AntennaNum = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschCyclicDelayDiversity(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.CyclicDelayDiversity = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschCodebookIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.CodebookIdx = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschCodeword(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Codeword = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschChanDecodeState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.ChanCodingState = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschScramble(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Scramble = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschMcsCfgMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsCfgMode = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschIRConfigMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.IRConfigMode = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschTxMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.TxMode = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschUECategory(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 12)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.UECategory = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschMcsTable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschTBSTalternativeIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        // if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.ChanDecodeState == 0 && Value != 0)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.TbsIndexAlt = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // if (attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.ChanDecodeState == 0 || attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsCfgMode == 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        if ((Value < DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx]) - 1][0] || 
             Value > DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx]) - 1][1]) && 
            (Value < DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx])][0] || 
             Value > DL_MODULE_MCS_MAP[attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.McsTable][(attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx])][1]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Mcs[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.Modulate[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPayloadSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 253440)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.PayloadSize[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschRedunVerIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.RedunVerIdx[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschSoftChanBit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 3200 || Value > 58675200)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.SoftChanBit[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschNIR(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Indx = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Indx, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Indx < 0 || Indx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 800 || Value > 3667200)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.NIR[Indx] = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPA(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        for (double value : DL_UE_PDSCHPA_MAP)
        {
            if (CompareDouble(value, Value, 1e-3) == 0)
            {
                iRet = WT_ERR_CODE_OK;
                break;
            }
        }

        if (iRet == WT_ERR_CODE_PARAMETER_MISMATCH)
        {
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.PA = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPdschPB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsaAlzParam.analyzeParam3GPP.LTE.Pdsch.PB = Value;
#if SHOW_3GPP_LTE_ALZ_PARAM_VALUE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmRmsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].EvmRms.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmRmsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].EvmRms.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmPeakState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].EvmPeak.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitEvmPeakLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].EvmPeak.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrRmsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].MErrRms.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrRmsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].MErrRms.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrPeakState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].MErrPeak.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitMerrPeakLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].MErrPeak.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrRmsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].PhErrRms.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrRmsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 180)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].PhErrRms.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrPeakState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].PhErrPeak.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitPherrPeakLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 180)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].PhErrPeak.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitFreqErrState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].FreqErr.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitFreqErrLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].FreqErr.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIQOffsetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IQOffset.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIQOffsetPwrLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value[4] = {0};
        int minSize = min(static_cast<int>(ARRAYSIZE(Value)), context->parser_state.numberOfParameters);
        for (int i = 0; i < minSize; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < -256 || Value[i] > 256)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int i = 0; i < minSize; i++)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IQOffset.PwrLimit[i] = Value[i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEGenMin(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.GenMin = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEGenEvm(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.GenEVM = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEGenPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.GenPwr = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIBEIqImage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.IQImage[0] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitIQOffsetPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value[4] = {0};
        int minSize = min(static_cast<int>(ARRAYSIZE(Value)), context->parser_state.numberOfParameters);
        for (int i = 0; i < minSize; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < -256 || Value[i] > 256)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int i = 0; i < minSize; i++)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].IBE.IQOffsetPwr[i] = Value[i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].SpectFlat.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatRange(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Param[2] = {0};
        SCPI_CommandNumbers(context, Param, 2);
        int ModLimitIdx = Param[0];
        int RangeIdx = Param[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit) ||
            RangeIdx < 1 || RangeIdx > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (RangeIdx == 1)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].SpectFlat.Range1 = Value;
        }
        else if (RangeIdx == 2)
        {
            attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].SpectFlat.Range2 = Value;
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatMax1Min2(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].SpectFlat.Max1Min2 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatMax2Min1(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].SpectFlat.Max2Min1 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzModLimitSpecFlatEdgeFreq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int ModLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &ModLimitIdx, 1);
        if (ModLimitIdx < 0 || ModLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 20)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.ModLimit[ModLimitIdx].SpectFlat.EdgeFreq = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitObwState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int SpecLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &SpecLimitIdx, 1);
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].OBWLimit.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitObwLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int SpecLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &SpecLimitIdx, 1);
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 40)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].OBWLimit.Limit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemLimitState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[3] = {0, 0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 3);
        int SpecLimitIdx = Num[0];
        int SEMLimitIdx0 = Num[1];
        int SEMLimitIdx1 = Num[2];

        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            SEMLimitIdx0 < 0 || SEMLimitIdx0 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit) ||
            SEMLimitIdx1 < 0 || SEMLimitIdx1 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit[0]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].SEMLimit[SEMLimitIdx0][SEMLimitIdx1].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemStartFreq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[3] = {0, 0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 3);
        int SpecLimitIdx = Num[0];
        int SEMLimitIdx0 = Num[1];
        int SEMLimitIdx1 = Num[2];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            SEMLimitIdx0 < 0 || SEMLimitIdx0 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit) ||
            SEMLimitIdx1 < 0 || SEMLimitIdx1 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit[0]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 25)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].SEMLimit[SEMLimitIdx0][SEMLimitIdx1].StartFreq = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemStopFreq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[3] = {0, 0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 3);
        int SpecLimitIdx = Num[0];
        int SEMLimitIdx0 = Num[1];
        int SEMLimitIdx1 = Num[2];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            SEMLimitIdx0 < 0 || SEMLimitIdx0 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit) ||
            SEMLimitIdx1 < 0 || SEMLimitIdx1 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit[0]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 25)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].SEMLimit[SEMLimitIdx0][SEMLimitIdx1].StopFreq = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[3] = {0, 0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 3);
        int SpecLimitIdx = Num[0];
        int SEMLimitIdx0 = Num[1];
        int SEMLimitIdx1 = Num[2];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            SEMLimitIdx0 < 0 || SEMLimitIdx0 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit) ||
            SEMLimitIdx1 < 0 || SEMLimitIdx1 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit[0]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -100 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].SEMLimit[SEMLimitIdx0][SEMLimitIdx1].LimitPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemRbw(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[3] = {0, 0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 3);
        int SpecLimitIdx = Num[0];
        int SEMLimitIdx0 = Num[1];
        int SEMLimitIdx1 = Num[2];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            SEMLimitIdx0 < 0 || SEMLimitIdx0 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit) ||
            SEMLimitIdx1 < 0 || SEMLimitIdx1 >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].SEMLimit[0]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value= 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 30 * KHz && Value != 50 * KHz && Value != 100 * KHz && Value != 150 * KHz && Value != 200 * KHz && Value != 1 * MHz)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].SEMLimit[SEMLimitIdx0][SEMLimitIdx1].RBW = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraRealState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[2] = {0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 2);
        int SpecLimitIdx = Num[0];
        int UtraLimitIdx = Num[1];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            UtraLimitIdx < 0 || UtraLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].UtraLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].UtraLimit[UtraLimitIdx].RelState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraRealLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[2] = {0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 2);
        int SpecLimitIdx = Num[0];
        int UtraLimitIdx = Num[1];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            UtraLimitIdx < 0 || UtraLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].UtraLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].UtraLimit[UtraLimitIdx].RelLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[2] = {0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 2);
        int SpecLimitIdx = Num[0];
        int UtraLimitIdx = Num[1];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            UtraLimitIdx < 0 || UtraLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].UtraLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].UtraLimit[UtraLimitIdx].AbsState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitUtraAbsPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Num[2] = {0, 0};
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, Num, 2);
        int SpecLimitIdx = Num[0];
        int UtraLimitIdx = Num[1];
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit) ||
            UtraLimitIdx < 0 || UtraLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[0].UtraLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].UtraLimit[UtraLimitIdx].AbsPwr = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraRealState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int SpecLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &SpecLimitIdx, 1);
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].EUtraLimit.RelState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraRealLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int SpecLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &SpecLimitIdx, 1);
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].EUtraLimit.RelLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int SpecLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &SpecLimitIdx, 1);
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].EUtraLimit.AbsState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzSpecLimitEutraAbsPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int SpecLimitIdx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &SpecLimitIdx, 1);
        if (SpecLimitIdx < 0 || SpecLimitIdx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit[SpecLimitIdx].EUtraLimit.AbsPwr = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.PwrLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.PwrLimit[Idx].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitOnPwrUpper(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.PwrLimit[Idx].OnLimitUpper = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitOnPwrLower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.PwrLimit[Idx].OnLimitLower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_3GPP_SetAlzPowerLimitOffPwr(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -256 || Value > 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.PwrLimit[Idx].OffLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_3GPP_SetAlzSpecLimitSemAddTestTol(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SpectLimit))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -5 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.LTE.LimitInfo.SEMAddTestTol[Idx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
