#pragma once
class WaveForm_CSV
{
public: 
    static WaveForm_CSV &Instance()
    {
        static WaveForm_CSV obj;
        return obj;
    }
    s32 GetPnStructData(const char *fileName, void *data, int len);
    s32 GetPnStructSize(const char *fileName, int *len);
    s32 ConvertFrom_CSVMIMO(const char *fileName, s32 freq, s32 index, stPNFileInfo *pResult, int pnMode);
    s32 ConvertFrom_CSVMIMO(const char *fileName, s32 freq, s32 StreamIndex, s32 PnIndex, stPNFileInfo *pResult, int pnMode);
    s32 SaveCsvFile(stPNFileInfo *pResult, const char *fileName, s32 streamIndex);
    s32 CatenateMutiPnCsvFiles(const MutiPNCatenateInfo *catenateInfo);
    s32 GetPNCountFromFile(const char *fileName, s32 &RetPnCount, int *PnOrder);

protected:
    s32 GetPNInfoFromFileMIMO(const char *fileName, s32 index, stPNFileInfo *pResult, int pnMode);
    s32 GetPNInfoFromFileMIMO(const char *fileName, s32 StreamIndex, s32 PnIndex, stPNFileInfo *pResult, int pnMode);
private:
    WaveForm_CSV();
    ~WaveForm_CSV();
};