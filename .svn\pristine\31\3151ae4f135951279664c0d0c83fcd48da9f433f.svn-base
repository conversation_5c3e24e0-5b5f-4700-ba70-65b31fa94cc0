//*****************************************************************************
//  File: manage.cpp
//  实现manager的各种对外业务
//  Date: 2016.7.11
//*****************************************************************************
#include "manage.h"

#include <new>
#include <iostream>
#include <dirent.h>
#include <unistd.h>
#include <cstring>

#include "wterror.h"
#include "wtlog.h"
#include "socket.h"
#include "conf.h"
#include "wtprotocol.h"
#include "device.h"

using namespace std;

Manager::~Manager()
{
    m_LinkWatcher.stop();
    m_ManagerCommWatcher.stop();
    m_BroadcastIO.stop();
    m_EvLoop.break_loop();
}

void Manager::Start(void)
{
    WTDeviceInfo::Instance();
    
    m_LinkWatcher.set<Manager, &Manager::LinkMsgCb>(this);
    m_LinkWatcher.start(m_SocketFd, EV_READ);

    //向外界广播本设备信息的定时器启动
    if (StartBroadcaseServer() > 0)
    {
#ifdef DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "start broadcase server!" << endl;
#endif
        m_BroadcastIO.set<Manager, &Manager::BroadcaseIOCb>(this);
        m_BroadcastIO.start(m_BroadcastSockFd, EV_READ);
    }
    else
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Broadcase Server start error!");
    }
}

//从WT-Link接收消息并进行相应的处理
void Manager::LinkMsgCb(wtev::io &Watcher, int Revents)
{
    (void)Watcher;

    if (WTDeviceInfo::Instance().GetDevUpgradeState() == UPGRADE_BUSY_STATE)
    {
        return;
    }
    if (Revents & EV_READ)
    {
        int Fd = -1;
        LinkMsg Msg;
        int Ret = m_WRSocket.RecvFdMsg(Fd, &Msg, sizeof(Msg));

        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "RecvFdMsg failed");
            return;
        }

        if (Msg.Cmd == LINK_CMD_NEW)
        {
            AcceptLink(Fd, Msg.Type, Msg.SID);
        }
        else
        {
            WTLog::Instance().LOGERR(WT_ERROR, "Link Cmd error");
        }
    }
}

bool Manager::IsExistCachedCmd(WRSocket &Sock)
{
    int UsedSize = Sock.GetBufUsedSize();
    if (UsedSize >= sizeof(CmdHeader))
    {
        void *Buf = Sock.GetRxBuf();
        if (UsedSize >= (static_cast<CmdHeader *>(Buf)->Length + sizeof(CmdHeader)))
        {
            return true;
        }
    }
    return false;
}

int Manager::ProcLinkData(WRSocket &Sock, void *Buf, int Len)
{
    int Ret = WT_ERROR;
    int Status = InterProt::Instance().CheckCmd(Buf, Len);

    if (Status == CMD_STATUS_OK)
    {
        Ret = InterProt::Instance().ExecCmd(Buf, Len, &Sock);
        if (Ret == WT_CMD_NO_HANDLER)
        {
            InterProt::Instance().SendAck(Sock, static_cast<CmdHeader *>(Buf), Ret);
        }

        Sock.ResetCmdBuf(static_cast<CmdHeader *>(Buf)->Length + sizeof(CmdHeader));

        if (IsExistCachedCmd(Sock)) // 缓存中存在命令可执行, 就手动生成一次事件
        {
            m_EvLoop.feed_fd_event(Sock.GetFd(), EV_READ);
        }
    }
    else if (Status == CMD_STATUS_UNCMP)
    {
        //命令总长度超过Buf长度，需要扩展接收buf的长度
        if (InterProt::Instance().GetCmdLen(Buf) >= Sock.GetBufSize())
        {
            Ret = Sock.ExpandBuf(InterProt::Instance().GetCmdLen(Buf));
            if (Ret != WT_OK)
            {
                Sock.ResetBuf();
                WTLog::Instance().LOGERR(Ret, "EXPAND buf failed");
            }
        }
        else
        {
            Ret = WT_OK;
        }
    }
    else
    {
        Sock.ResetBuf();
        WTLog::Instance().LOGERR(WT_ERROR, "cmd header error");
        Ret = WT_CMD_ERROR;
    }

    return Ret;
}

void Manager::CommDataHandlerCb(wtev::io &Watcher, int Revents)
{
    (void)Watcher;
    (void)Revents;
    int Ret = WT_OK;

    if (Revents & EV_READ)
    {
        int Len = 0;
        CommData *ClientData = (CommData *)m_Data.get();
        Ret = (ClientData->WrSock).Recv(Len, false);

        if (Ret == WT_OK)
        {
            Ret = ProcLinkData(ClientData->WrSock, (ClientData->WrSock).GetRxBuf(), Len);
            if (Ret == WT_SOCKET_CLOSED)
            {
                //close fd并通知link
                CloseLink(ClientData->WrSock.GetFd(), ClientData->Type, ClientData->SID);
            }
        }
        else //socket异常或被关闭
        {
            if (Ret != WT_SOCKET_CLOSED)
            {
                WTLog::Instance().LOGERR(Ret, "socket error");
            }

            //对外连接断开时停止服务
            CloseLink(ClientData->WrSock.GetFd(), ClientData->Type, ClientData->SID);
            return;
        }
    }
}

void Manager::AcceptLink(int Fd, int Type, int SID)
{
    int Len = 0;
    WRSocket WrSock(Fd);
    WrSock.Send("connection ok", strlen("connection ok"), Len); //收到连接并判断可以接受后才发送Connection OK的信息

    int ManagerPort = 5026;
    WTConf wtconf(WTConf::GetDir() + "/scpi.conf");
    wtconf.GetItemVal("ManagerPort", ManagerPort); //通过配置文件读取文件获取TCP监听端口
    string PeerInfo = "";
    try
    {
        PeerInfo = Basefun::shell_exec((string("ss -nt sport = ") + to_string(ManagerPort) + " | grep ESTAB | awk \'{print $NF}\'").c_str());
    }
    catch (const std::exception &e)
    {
        std::cerr << __FUNCTION__ << __LINE__ << e.what() << '\n';
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "Get mgr link peer info error");
    }
    
    //注册链接读取数据事件
    auto Pos = PeerInfo.find_first_of(':');
    auto PosEnd = PeerInfo.find_last_not_of(" \t\f\v\n\r");
    string PeerIp = Pos > 0 ? PeerInfo.substr(0, Pos) : "0";
    string PeerPort = Pos < PosEnd ? PeerInfo.substr(Pos + 1, PosEnd - Pos) : "0";

    m_Data.reset(new (std::nothrow) CommData(LINK_CMD_CLOSE, Type, SID, PeerIp, PeerPort, Fd));
    m_ManagerCommWatcher.set<Manager, &Manager::CommDataHandlerCb>(this);
    m_ManagerCommWatcher.start(Fd, EV_READ);
    CmdHeader Header;
    Header.SerialNum = 0;
    TunnelMgr::Instance().EnableRunInCal(&Header, 0);
}

void Manager::CloseLink(int Fd, int Type, int SID)
{
    int Value = 0;
    //先判断临时文件是否存在,存在即把校准Value值设为0(关闭自校准)
    if (access("/tmp/notIntercal.txt", F_OK) != 0)
    {
        // 读取自校准相关配置
        DevConf::Instance().GetItemVal("InternalCal", Value);
        if (Value != 0)
        {
            CmdHeader Header;
            Header.SerialNum = 0;
            TunnelMgr::Instance().EnableRunInCal(&Header, 1);
        }
    }

    //延时以确保EnableRunInCal发送信息完成。
    usleep(10000);
    TunnelMgr::Instance().ReleaseTunnel();
    shutdown(Fd, SHUT_RDWR); //close 前先关闭下通讯
    close(Fd);

    m_ManagerCommWatcher.stop(); //关闭获取数据的事件循环

    LinkMsg Msg(LINK_CMD_CLOSE, Type, SID);

    int Ret = m_WRSocket.SendFdMsg(-1, &Msg, sizeof(Msg));
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SendFdMsg failed");
    }

    m_Data.reset(nullptr);  //断开时重置下连接信息
}

#define UDPRCV_BUFFSIZE (2 * 1024)
int Manager::StartBroadcaseServer(void)
{
    int Err = -1;
    const int SoBroadcast = 1;
    int Port = 7002;

    m_BroadcastAddr.sin_family = AF_INET;
    m_BroadcastAddr.sin_port = htons(Port);
    m_BroadcastAddr.sin_addr.s_addr = htonl(INADDR_ANY);
    bzero(&(m_BroadcastAddr.sin_zero), 8);
    m_BroadcastSockFd = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_BroadcastSockFd == -1)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "socket creat error");
        return 0;
    }
    Err = setsockopt(m_BroadcastSockFd, SOL_SOCKET, SO_BROADCAST, &SoBroadcast, sizeof(SoBroadcast));
    if (Err == -1)
    {
        WTLog::Instance().LOGERR(WT_SOCKET_SET_ERROR, "socket set error");
        return 0;
    }
    Err = bind(m_BroadcastSockFd, (struct sockaddr *)&m_BroadcastAddr, sizeof(struct sockaddr));
    if (Err == -1)
    {
        WTLog::Instance().LOGERR(WT_SOCKET_BIND_ERROR, "Broadcast socket bind error");
        return 0;
    }

    return 1;
}

void Manager::BroadcaseIOCb(wtev::io &Watcher, int Revents)
{
    (void)Watcher;
    (void)Revents;
    socklen_t client_size;
    int BufLen = 0;
    std::unique_ptr<char[]>BroadcastBuff(new (std::nothrow)char[UDPRCV_BUFFSIZE]);
    client_size = sizeof(m_ClientAddr);
    m_BroadcastAddr.sin_addr.s_addr = htonl(INADDR_ANY);
    BufLen = recvfrom(m_BroadcastSockFd, BroadcastBuff.get(), UDPRCV_BUFFSIZE, 0, (struct sockaddr *)&m_ClientAddr, &client_size);
    if (BufLen == -1)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Broadcast socket read error!");
        return;
    }

    char *DataBuff = (char *)BroadcastBuff.get();

    if (memcmp(DataBuff, "scan", 4) == 0)
    {
        bzero(DataBuff, UDPRCV_BUFFSIZE); /* 清空缓冲区 */

        WTDeviceInfo::Instance().ReCheckNetInfo();

        //要广播的信息
        sprintf(DataBuff, "TYPE:%s;NAME:%s;IP:%s;SN:%s;VERSION:%s;",
                WTDeviceInfo::Instance().GetDeviceType(), WTDeviceInfo::Instance().GetDeviceDetailedInfo().Name, WTDeviceInfo::Instance().GetDeviceDetailedInfo().IP,
                WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN, WTDeviceInfo::Instance().GetDeviceDetailedInfo().FwVersion);

        //追加查詢开机时间时长信息
        time_t TimeNow = 0;
        time(&TimeNow);
        int LastTicks = TimeNow - BootTimeTicks;
        TIME_TYPE TMBoot;
        Basefun::Seconds2TimeType(BootTimeTicks, &TMBoot);

        sprintf(DataBuff + strlen(DataBuff), "Boot Time:%04d-%02d-%02d,%02d-%02d-%02d;", TMBoot.year, TMBoot.mon, TMBoot.mday, TMBoot.hour, TMBoot.min, TMBoot.sec);
        sprintf(DataBuff + strlen(DataBuff), "Online last Time:%04d-%02d-%02d,%02d-%02d-%02d;",
                (LastTicks / (12 * 30 * 24 * 3600)), (LastTicks / (30 * 24 * 3600)) % 12, (LastTicks / (24 * 3600)) % 30,
                (LastTicks / 3600) % 24, (LastTicks / 60) % 60, LastTicks % 60);
        if (m_Data == nullptr)
        {
            sprintf(DataBuff + strlen(DataBuff), "MgrLink:0;\r\n");
        }
        else
        {
            sprintf(DataBuff + strlen(DataBuff), "MgrLink:1;PeerIp:%s;PeerPort:%s;\r\n", m_Data.get()->PeerIp.c_str(), m_Data.get()->PeerPort.c_str());
        }
        //WTLog::Instance().WriteLog(LOG_DEBUG, "strlen(DataBuff) = %ld\n", strlen(DataBuff));
        BufLen = strlen(DataBuff);
        if (BufLen > UDPRCV_BUFFSIZE)
        {
            BufLen = UDPRCV_BUFFSIZE;
        }

        //Send
        m_BroadcastAddr.sin_addr.s_addr = htonl(INADDR_BROADCAST); //端口号不变,广播出去
        sendto(m_BroadcastSockFd, DataBuff, BufLen, 0, (struct sockaddr *)&m_ClientAddr, sizeof(m_ClientAddr));

        int Flag = 0;
        DevConf::Instance().GetItemVal("PrintfBroadcastInfo", Flag);
        if (Flag == 1)
        {
            BackPlaneUnitInfo BPInfo;
            memset(&BPInfo, 0, sizeof(BackPlaneUnitInfo));

            //背板
            if (DevLib::Instance().GetBackPlaneInfo(BPInfo) == WT_OK)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "Read Board SN = %s\n", BPInfo.SN);
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "DataBuff=%s\n", DataBuff);
        }
    }
    DataBuff = nullptr;
}
