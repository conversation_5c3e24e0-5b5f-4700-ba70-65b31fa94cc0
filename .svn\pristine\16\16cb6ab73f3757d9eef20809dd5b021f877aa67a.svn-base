//*****************************************************************************
//  File: devmgr.h
//  设备资源管理
//  Data: 2016.8.5
//  SetSrvID()为类的初始化接口，需要在所有接口之前被调用一次
//*****************************************************************************
#ifndef __WT_DEV_MGT_H__
#define __WT_DEV_MGT_H__

#include <mutex>
#include <condition_variable>
#include <vector>
#include <functional>
#include <thread>
#include "wtev++.h"
#include <set>

using namespace std;

enum WT_DEV_RES_TYPE
{
    DEV_RES_VSA,
    DEV_RES_VSG,
    DEV_RES_MAX
};

using NotifyFunc = std::function<void(int, int, int)>;

#define WT_RFPORT_NUM 9            //RF端口数
#define LED_LAST_TIME_US (1e6 / 2) //LED亮灯时间0.5s

//设备资源管理类
class DevMgr
{
public:
    //*****************************************************************************
    // 设置当前的子仪器ID，此接口为本类的初始化接口，需要在所有接口之前被调用一次
    // 参数[IN]: Id : 子仪器ID
    // 返回值: 无
    //*****************************************************************************
    static void SetSrvID(int Id);

    //*****************************************************************************
    // 获取当前子仪器ID
    // 参数: 无
    // 返回值: 子仪器ID
    //*****************************************************************************
    static int GetSrvID(void)
    {
        return m_ServerID;
    }

    //*****************************************************************************
    // 获取DevMgr对象
    // 参数: 无
    // 返回值: DevMgr对象
    //*****************************************************************************
    static DevMgr &Instance();

    //*****************************************************************************
    // 申请硬件模块
    // 参数[IN]: Type : 硬件类型
    //           Notify : 硬件完成操作后的通知回掉函数
    //           Timeout : 申请等待超时时间
    //           Key : 申请者的特征码
    //           SpecId : 申请指定的硬件单元，此参数为-1表示动态分配
    // 参数[OUT]: DevId : 硬件模块ID
    // 返回值: 硬件模块ID
    //*****************************************************************************
    int AllocMod(int Type, const NotifyFunc &Notify, int Timeout, unsigned long long Key, int &DevId, int SpecId = -1);

    //*****************************************************************************
    // 释放硬件模块
    // 参数[IN]: Type : 硬件类型
    //          DevId : 硬件模块ID
    // 返回值: 成功或错误码
    //*****************************************************************************
    int FreeMod(int Type, int DevId);

    //*****************************************************************************
    // 获取模块资源数
    // 参数[IN]: Type : 硬件类型，VSA/VSG
    // 返回值: 模块数
    //*****************************************************************************
    int GetModNum(int Type);

    //*****************************************************************************
    // 通知其他对象释放已申请的资源
    // 参数[IN]: FreeType: 要释放的硬件类型，VSA/VSG
    // 返回值: 无
    //*****************************************************************************    
    void NotifyFreeResources(int FreeType, const NotifyFunc &Notify);

    //*****************************************************************************
    // 查询是否有线程在等待资源
    // 参数[IN]: Type : 硬件类型
    //          DevId : 硬件模块ID
    // 返回值: true or false
    //*****************************************************************************
    bool QueryExistWait(int Type, int DevId);

    //*****************************************************************************
    // 设置硬件操作完成后的信号回掉函数
    // 参数[IN]: loop : 信号所属的ev loop
    // 返回值: 无
    //*****************************************************************************
    void SetHwCb(const wtev::loop_ref &loop);

    void SetHwLedCb(const wtev::loop_ref &loop);

    int PortLedOn(int Type, int Port);

    int PortLedOff(int Port, bool Now = false);

    //连接断开时，通知并唤醒同一个用户的其他连接
    int NotifyAllThread();

    //硬件操作完成的回掉函数
    void HwOpFin(wtev::sig &watcher, int revents);

    //通知指定单元查询完成状态
    void HwOpFinMod(int Type, int ModId);

    //是否有用户正在申请该单元
    int IsAllocating(int Type, int SpecId);

    // delete copy and move constructors and assign operators
    DevMgr(DevMgr const &) = delete;            // Copy construct
    DevMgr(DevMgr &&) = delete;                 // Move construct
    DevMgr &operator=(DevMgr const &) = delete; // Copy assign
    DevMgr &operator=(DevMgr &&) = delete;      // Move assign

    
    int ATTCalOpFin(wtev::sig &watcher, int revents);

private:
    DevMgr();
    ~DevMgr() {}

    void CheckLed(wtev::timer &watcher, int revents);

    void PrintDevOwner(int Type, unsigned long long Key, int DevId = -1);

    //设备模块信息
    struct ModInfo
    {
        int Type;                    //单元模块类型
        int DevId;                   //模块ID
        bool Free = true;            //是否空闲
        unsigned int WaitHead = 0;   //请求队列头部
        unsigned int WaitTail = 0;   //请求队列尾部
        unsigned long long Key;      //特征码，用于记录当前资源的拥有者
        NotifyFunc Notify = nullptr; //硬件操作完成后的通知回调函数
        set<int> OverdueSet;         //过期的申请队列号

        ModInfo(int Type, int Id) : Type(Type), DevId(Id) {}
        ModInfo() {}
    };

    struct Resources
    {
        unsigned int WaitHead = 0;    //请求队列头部
        unsigned int WaitTail = 0;    //请求队列尾部
        int AllocNum = 0;             //表示头部请求从等待到申请到资源期间一共有多少资源被申请走
        int FreeNum = 0;              //空闲的资源数量
        std::mutex Mutex;             //资源锁
        std::condition_variable Cond; //资源条件变量
        std::vector<ModInfo> Mods;    //资源信息
        set<int> OverdueSet;          //过期的申请队列号
    };

    //动态分配
    int DynamicAlloc(Resources &Dev, int Timeout, int &ModIndex);

    //指定分配
    int SpecAlloc(Resources &Dev, int SpecId, int Timeout, int &ModIndex);

    //保存线程对应的连接信息，主要为了资源等待取消
    int AddThreadConnInfo(std::thread::id id, char *UserIp, int UserPort);
private:
    static int m_ServerID;                       //当前子仪器ID
    wtev::sig m_SigEv;                           //SIGIO信号的watcher
    Resources m_VsaMods;                         //VSA模块资源
    Resources m_VsgMods;                         //VSG模块资源
    wtev::timer m_LedEv;                         //LED定时器
    struct PortInfo
    {
        std::mutex PortMutex;         // 端口LED锁
        std::thread::id PortUserId;   // 端口使用者的ID，为tid
        unsigned long LedOnTime;      // RF口LED打开时间，0表示未打开
        unsigned long LedOffTime;     // RF口LED关闭时间
    };
    PortInfo m_Ports[WT_RFPORT_NUM];
};

#endif
