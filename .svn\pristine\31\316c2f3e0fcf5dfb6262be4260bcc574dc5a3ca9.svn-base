//*********************************************************************************
//  File: bnshandler.h
//  协议与实际数据处理逻辑分离，本文针对协议数据处理部分逻辑
//  Data: 2016.11.1
//*********************************************************************************
#ifndef BNSHANDLER_H_
#define BNSHANDLER_H_

#include <vector>
#include <string>

#include "upgrade.h"
#include "license.h"

struct GUIVersion
{
    char LicTechName[32];
    char Version[40];
};

class BnsHandler
{
public:
    //*****************************************************************************
    // 函数: SetDeviceIPHandler()
    // 功能: 协议中设置设备信息实际逻辑处理
    // 参数 [IN]：Data：设备信息
    // 参数 [IN]：DataLen：Data数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetDeviceInfoHandler(void *Data, int DataLen);

    //*****************************************************************************
    // 函数: SetSubDeviceCfgHandler()
    // 功能: 协议中设置子仪器划分实际逻辑处理
    // 参数 [IN]：Data：子仪器划分信息
    // 参数 [IN]：DataLen：Data数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetSubDeviceCfgHandler(void *Data, int DataLen);

    //*****************************************************************************
    // 函数: GetSubDeviceCfgHandler()
    // 功能: 协议中获取子仪器划分信息实际逻辑处理
    // 参数 [OUT]：Data：子仪器划分信息
    // 参数 [OUT]：DataLen：Data数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetSubDeviceCfgHandler(char *DataBuf, int &DataLen);

    //*****************************************************************************
    // 函数: GetHardwareInfoHandler()
    // 功能: 硬件相关信息的获取
    // 参数 [OUT]：Data：硬件信息
    // 参数 [OUT]：DataLen：Data数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetHardwareInfoHandler(char *DataBuf, int &DataLen);

    //*****************************************************************************
    // 函数: GetLicenseInfoHandler()
    // 功能: 获取license相关信息数据处理，主要包括license类型，Technology类型，有效开始时间，有效结束时间等
    // 参数 [OUT]：LicItemsInfo：lincense信息的vector
    // 参数 [OUT]：DataLen：获取到的vector数据字节总大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLicenseInfoHandler(std::vector<LicItemInfo> &LicItemsInfo, int &DataLen);

    //*****************************************************************************
    // 函数: GetLogHandler()
    // 功能: 获取日志数据实际处理逻辑
    // 参数 [IN]：Data：查询类型
    // 参数 [OUT]：Record：日志内容
    // 参数 [OUT]：VectorSize：日志内容总size
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLogHandler(void *Data, std::vector<std::string> &Record, int &VectorSize);

    //*****************************************************************************
    // 函数: RollbackFirmwareHandler()
    // 功能: 固件回退分析处理handler
    // 参数 [IN]：Data：回退类型，恢复出厂还是恢复上一个版本
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int RollbackFirmwareHandler(void *Data);

    //*****************************************************************************
    // 函数: GetGUIVersionHander()
    // 功能: 获取GUI文件对应的版本信息
    // 参数 [OUT]：GUIVersionInfo：GUI文件版本信息的vector
    // 参数 [OUT]：DataLen：获取到的vector数据字节总大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetGUIVersionHander(std::vector<GUIVersion> &GUIVersionInfo, int &DataLen);

    //*****************************************************************************
    // 函数: DeleteAllLicenses()
    // 功能: 删除仪器所有license
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int DeleteAllLicenses(void);

    //*****************************************************************************
    // 函数: DeleteSubNet()
    // 功能: 删除子网口配置
    // 返回值：成功或者失败错误码
    //*****************************************************************************
	int DeleteSubNet(void);
private:
    //1的个数对应Cnt转成对应位上设置为一，eg：Cnt=4, Result=0x1111;
    int IntToBinaryCnt(int Cnt);
};
#endif /* BNSHANDLER_H_ */
