#include "scpi_3gpp_gen_nbiot.h"

#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular;

static inline Alg_NBIOT_WaveGenType &Nbiot(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->Pn3GPP->NBIOT;
}

scpi_result_t SCPI_NBLOT_SetLinkDirect(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Nbiot(context).LinkDirect = value;
        GetVsgDefaultParamFrom3GPPCallback(ALG_3GPP_STD_NB_IOT, value, *(attr->Pn3GPP.get()));
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(ALG_3GPP_FILTER_NON, ALG_3GPP_FILTER_WOLA))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.Type = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterSampleRate(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, INT_MAX))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.Fs = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterMaxOrder(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {128, 256, 512})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.MaxOrder = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterFpassFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.FpassFactor = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterFstopFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.FstopFactor = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterPassRipple(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 0.3))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.PassRipple = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterStopAtten(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 100.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.StopAtten = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterRollOffFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.RollOffFactor = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterCutOffFreqShift(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-1.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.CutOffFreqShift = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterWindowLenFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.WindowLenFactor = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellOperMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.OperationMode = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellBW(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {200000, 3000000, 5000000, 10000000, 15000000, 20000000})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.ChannelBW = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellRBIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(-47, 134))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.RBIdx = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellNBCellID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 503))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.NBCellID = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellGroupHopping(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.GrpHopping = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneCyclicShift3(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.TTCShift = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneCyclicShift6(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.STCShift = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellBaseSequenceMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.BaseSeqMode = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneBaseSequence3(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 11))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.TTBaseSeq = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneBaseSequence6(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.STBaseSeq = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneBaseSequence12(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 29))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.TWBaseSeq = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellDeltaSequenceShift(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 29))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.DeltaSeqShift = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 65535))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.UeID = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEScrambling(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.Scrambling = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEDataType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.DataType = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEInitialization(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 0x7FFFFF))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.Initialization = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEChanCodeingState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.ChanCodingState = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedChanType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.ChanType = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschFormat(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Format = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCSpace(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {15000, 3750})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.SCSpacing = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschStartSubFrame(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.StartSubfrm = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschReptitions(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4, 8, 16, 32, 64, 128})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Repetitions = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschNumResUnits(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 3, 4, 5, 6, 8, 10})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.RUs = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCIndicationMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.UseIsc = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCIndication(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 47))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Isc = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 3, 6, 12})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.SubcarrierNum = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCStart(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 47))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.StartSubcarrier = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschModulate(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 18})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Modulate = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschMscMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.UseMcs = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschMsc(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Mcs = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschTBSizeIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.TBSIdx = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschRVIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {0, 2})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.StartRVIdx = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschAckResFieldMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.UseACKResField = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschAckResField(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 15))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.ACKResField = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 47))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.SubcarrierIdx = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschHarqAckInfo(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.HarqAckInfo = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciUser(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.User = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciDciFormat(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.DciFormat = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciSearchSpace(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.SearchSpace = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0Isc(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 47))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.N0.Isc = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0Iru(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 7))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.N0.Iru = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0Idelay(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.N0.Idelay = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0Imcs(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 10))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.AhrSchedule.Dci.N0.Imcs = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0RedunVer(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N0.RedunVer = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0Irep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N0.Irep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0NewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N0.NewDataInd = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN0DciRepNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N0.DciRepNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1OrderInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.OrderInd = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1NprachRep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.NprachRep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1NprachSC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 47)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.NprachSC = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1Idelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.Idelay = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1Isf(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.Isf = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1Imcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.Imcs = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1Irep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.Irep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1NewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.NewDataInd = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1HarqAckRes(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.HarqAckRes = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1DciRepNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.DciRepNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN1DistanceType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N1.DistanceType = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2PagFlg(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.PagFlg = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2SysInfoModifEDRX(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.SysInfoModifEDRX = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2SysInfoModif(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.SysInfoModif = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2Isf(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.Isf = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2Imcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.Imcs = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2Irep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.Irep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciN2DciRepNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.N2.DciRepNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciStartSubfrm(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 70)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.StartSubfrm = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciNpdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.NpdcchFormat = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleDciNcceIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Dci.NcceIdx = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.Precoding = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.Scrambling = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.ChanCodingState = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchUseMIB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.UseMIB = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSFN(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1200)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.SFN = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoHyperSFN(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1020)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.HyperSFN = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSchedInfoSIB1(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.SchedInfoSIB1 = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSysInfoValueTag(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 31)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.SysInfoValueTag = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoABEnabled(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.ABEnabled = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoOptModeInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        char text[256] = {0};
        size_t copyLen = 0;
        int Value = 0;
        if (!SCPI_ParamCopyText(context, text, sizeof(text) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        
        Value = std::stoi(text, nullptr, 2);
        if (Value < 0 || Value > 177)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        memcpy(attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.OptModeInfo, text, sizeof(char)*7);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSpareBit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        char text[256] = {0};
        size_t copyLen = 0;
        int Value = 0;
        if (!SCPI_ParamCopyText(context, text, sizeof(text) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        
        Value = std::stoi(text, nullptr, 2);
        if (Value < 0 || Value > 3777)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        memcpy(attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.SpareBit, text, sizeof(char)*11);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleSIB1Precoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.SIB1.Precoding = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleSIB1Scrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.SIB1.Scrambling = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleSIB1ChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.SIB1.ChanCodingState = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleSIB1TBSIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.SIB1.TBSIdx = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdcchStartSymb(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdcch.StartSymb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdcchScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdcch.Scrambling = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdcchPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdcch.Precoding = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdschStartSymb(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdsch.StartSymb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdschScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdsch.Scrambling = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdschPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdsch.Precoding = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpdschChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npdsch.ChanCodingState = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleConfigType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].ConfigType = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciUser(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.User = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciDciFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.DciFormat = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciSearchSpace(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.SearchSpace = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Isc(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, 2))
        .Param(value, INT_RANGE(0, 47))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.NonAhrSchedule[param].Dci.N0.Isc = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Iru(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.Iru = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Idelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.Idelay = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Imcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.Imcs = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0RedunVer(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.RedunVer = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Irep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.Irep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0NewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.NewDataInd = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN0DciRepNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N0.DciRepNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1OrderInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.OrderInd = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1NprachRep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.NprachRep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1NprachSC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 47)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.NprachSC = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Idelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.Idelay = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Isf(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.Isf = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Imcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.Imcs = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Irep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.Irep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1NewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.NewDataInd = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1HarqAckRes(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.HarqAckRes = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1DciRepNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.DciRepNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN1DistanceType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N1.DistanceType = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2PagFlg(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.PagFlg = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2SysInfoModifEDRX(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.SysInfoModifEDRX = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2SysInfoModif(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.SysInfoModif = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2Isf(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.Isf = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2Imcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.Imcs = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2Irep(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.Irep = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciN2DciRepNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.N2.DciRepNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciStartSubfrm(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 70)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.StartSubfrm = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciNpdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.NpdcchFormat = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciNcceIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.NcceIdx = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdcchStartSymb(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdcch.StartSymb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdcchScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdcch.Scrambling = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdcchPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdcch.Precoding = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdschStartSymb(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdsch.StartSymb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdschScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdsch.Scrambling = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdschPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdsch.Precoding = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleNpdschChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Npdsch.ChanCodingState = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//DL cell
scpi_result_t SCPI_NBLOT_DL_SetCellBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (200 * KHz_API) &&
            Value != (3 * MHz_API) &&
            Value != (5 * MHz_API) &&
            Value != (10 * MHz_API) &&
            Value != (15 * MHz_API) &&
            Value != (20 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.ChannelBW = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLtePhysicalCellID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.PhyCellID = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLteRARNTI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 60)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.RARNTI = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLteTxAntenna(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.LteAntennaNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLteResFillState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.LteREsFillState = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLteModulation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 2;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_QPSK && 
            Value != ALG_3GPP_16QAM &&
            Value != ALG_3GPP_64QAM &&
            Value != ALG_3GPP_256QAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.Modulation = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLteDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < ALG_3GPP_PSDU_PN9 || Value > ALG_3GPP_PSDU_1010)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.DataType = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellLteInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Lte.Initialization = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellNBCellID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.NBCellID = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellNBAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.NBAntennaNum = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellAnchorCarrierOperationMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Anchor.OperationMode = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellAnchorCarrierRBIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -47 || Value > 134)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Anchor.RBIdx = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellAnchorCarrierType1Rmax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4 && Value != 8 &&
            Value != 16 && Value != 32 && Value != 64 && Value != 128 &&
            Value != 256 && Value != 512 && Value != 1024 && Value != 2048)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Anchor.CSSType1Rmax = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellAnchorCarrierType2Rmax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4 && Value != 8 &&
            Value != 16 && Value != 32 && Value != 64 && Value != 128 &&
            Value != 256 && Value != 512 && Value != 1024 && Value != 2048)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Anchor.CSSType2.Rmax = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellAnchorCarrierType2G(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 4.0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CompareDouble(Value, 1.5) != 0 && 
            CompareDouble(Value, 2) != 0 &&
            CompareDouble(Value, 4) != 0 &&
            CompareDouble(Value, 8) != 0 &&
            CompareDouble(Value, 16) != 0 &&
            CompareDouble(Value, 32) != 0 &&
            CompareDouble(Value, 48) != 0 &&
            CompareDouble(Value, 64) != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Anchor.CSSType2.G = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellAnchorCarrierType2Offset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0.0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CompareDouble(Value, 0) != 0 && 
            CompareDouble(Value, 0.125) != 0 &&
            CompareDouble(Value, 0.25) != 0 &&
            CompareDouble(Value, 0.375))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.Anchor.CSSType2.Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellNonAnchorCarrierIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NonAnchorIndex = 0;
        SCPI_CommandNumbers(context, &NonAnchorIndex, 1);
        if (NonAnchorIndex < 0 || NonAnchorIndex > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.NonAnchor[NonAnchorIndex].Index = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellNonAnchorCarrierState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NonAnchorIndex = 0;
        SCPI_CommandNumbers(context, &NonAnchorIndex, 1);
        if (NonAnchorIndex < 0 || NonAnchorIndex > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.NonAnchor[NonAnchorIndex].State = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellNonAnchorCarrierOperationMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NonAnchorIndex = 0;
        SCPI_CommandNumbers(context, &NonAnchorIndex, 1);
        if (NonAnchorIndex < 0 || NonAnchorIndex > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.NonAnchor[NonAnchorIndex].OperationMode = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetCellNonAnchorCarrierRBIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int NonAnchorIndex = 0;
        SCPI_CommandNumbers(context, &NonAnchorIndex, 1);
        if (NonAnchorIndex < 0 || NonAnchorIndex > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -47 || Value > 134)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Cell.NonAnchor[NonAnchorIndex].RBIdx = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//DL UE
scpi_result_t SCPI_NBLOT_DL_SetUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.UeID = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetUECategory(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.UeCategory = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetUESpecificSearchSpaceRmax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4 && Value != 8 &&
            Value != 16 && Value != 32 && Value != 64 && Value != 128 &&
            Value != 256 && Value != 512 && Value != 1024 && Value != 2048)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.USS.Rmax = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetUESpecificSearchSpaceG(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 4.0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CompareDouble(Value, 1.5) != 0 && 
            CompareDouble(Value, 2) != 0 &&
            CompareDouble(Value, 4) != 0 &&
            CompareDouble(Value, 8) != 0 &&
            CompareDouble(Value, 16) != 0 &&
            CompareDouble(Value, 32) != 0 &&
            CompareDouble(Value, 48) != 0 &&
            CompareDouble(Value, 64) != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.USS.G = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetUESpecificSearchSpaceOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0.0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CompareDouble(Value, 0) != 0 && 
            CompareDouble(Value, 0.125) != 0 &&
            CompareDouble(Value, 0.25) != 0 &&
            CompareDouble(Value, 0.375))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.USS.Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetUEDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < ALG_3GPP_PSDU_PN9 || Value > ALG_3GPP_PSDU_1010)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.DataType = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_DL_SetUEInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NBIOT.DL.Ue.Initialization = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}
