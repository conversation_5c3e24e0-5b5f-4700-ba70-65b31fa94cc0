﻿#ifndef ALG_3GPP_LIST_EXPORT_H_
#define ALG_3GPP_LIST_EXPORT_H_

#ifdef _WIN32
  #ifdef WTALG3GPPLIST_EXPORTS
    #define ALG_3GPP_LIST_API extern __declspec(dllexport)
  #else
    #ifndef __cplusplus
        #define ALG_3GPP_LIST_API __declspec(dllimport)
    #else
        #define ALG_3GPP_LIST_API extern "C" __declspec(dllimport)
    #endif
  #endif
#else /*Linux Envir*/
    #ifndef __cplusplus
        #define ALG_3GPP_LIST_API extern __attribute__ ((visibility ("default")))
    #else
        #define ALG_3GPP_LIST_API extern "C" __attribute__ ((visibility ("default")))
    #endif
#endif

#endif /* ALG_3GPP_LIST_EXPORT_H_ */
