//*****************************************************************************
//  File: main.cpp
//  WT-Manager程序入口文件，调用各模块初始化，创建主循环
//  Data: 2016.7.8
//*****************************************************************************
#include <signal.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/sysinfo.h>
#include <cstdio>
#include <iostream>
#include <sstream>
#include <unistd.h>
#include "wtev++.h"
#include "wterror.h"
#include "socket.h"
#include "conf.h"
#include "wtlog.h"
#include "launch.h"
#include "monitor.h"
#include "manage.h"
#include "devlib.h"
#include "license.h"
#include "basefun.h"
#include "tunnel.h"
#include "ldpc.h"
#include "protocol.h"
#include "version.h"
#include "wt-calibration.h"
#include "crypto.h"
#include "device.h"
#include "upgrade.h"
#include "../general/ft4222lib/upgradefpga.h"
#include "wtsecurelib.h"

#include "main.h"
using namespace std;

#ifdef DEBUG
//保证程序CTRL+C退出时能正常记录coverage信息
extern "C" void __gcov_flush(void);
static void SignalCb(struct ev_loop *loop, struct ev_signal *w, int revents)
{
    (void)w;
    (void)revents;
    ev_break(loop);
    #ifdef COVERAGE
    __gcov_flush();
    #endif
}

void coutss(const stringstream &ss)
{
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << ss.str() << endl;
}
#endif

void RecordBootTime();  // 记录仪器开机时间 与 manager启动时间

int main(int argc, char *argv[])
{
    int Ret = WT_OK;

    //提供快捷查询仪器固件版本号的方式。操作方式：./WT-Manager -v 或者./WT-Manager -version
    if(argc > 1)
    {
        if ((strcmp(argv[1], "-v") == 0) || (strcmp(argv[1], "-version") == 0) || (strcmp(argv[1], "-V") == 0))
        {
            printf("Current Firmware Version: %s\n", WTGetFwVersion());
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "complice time:" << __DATE__ << " " << __TIME__ << endl;
            exit(1);
        }

        if ((strcmp(argv[1], "cm-init") == 0) || (strcmp(argv[1], "CM-INIT") == 0))
        {
            Ret = CryptoLib::Instance().CryptoMemProgram(argc - 2, &argv[2]); // 亮全部led
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CryptoMemInfo Ret =" << Ret << endl;
            exit(1);
        }

        if ((strcmp(argv[1], "ldpc-test") == 0) || (strcmp(argv[1], "LDPC-TEST") == 0))
        {
            // 日志类初始化
            WTLog::SetLogName(WTConf::GetDir() + "/manager.db");
            WTLog::SetFiFoName(WTConf::GetDir() + "/fifo.manager");
            WTLog::Instance().SetLogPrintEnalbe((2 << LOG_TYPE_END) - 1); // manager的日志输出都打开
            WTConf::DevCfg Cfg;
            BaseConf::Instance().GetDevCfg(1, Cfg);
            DevLib::SetMask(Cfg.VsaMask, Cfg.VsgMask);
            DevLib::Instance().DevLibInit();
            HwDecodeResMgr::Instance();
            Ret = LdpcTest();
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LdpcTest Ret =" << Ret << endl;
            exit(1);
        }

        if ((strcmp(argv[1], "bcc-test") == 0) || (strcmp(argv[1], "BCC-TEST") == 0))
        {
            // 日志类初始化
            WTLog::SetLogName(WTConf::GetDir() + "/manager.db");
            WTLog::SetFiFoName(WTConf::GetDir() + "/fifo.manager");
            WTLog::Instance().SetLogPrintEnalbe((2 << LOG_TYPE_END) - 1); // manager的日志输出都打开
            WTConf::DevCfg Cfg;
            BaseConf::Instance().GetDevCfg(1, Cfg);
            DevLib::SetMask(Cfg.VsaMask, Cfg.VsgMask);
            DevLib::Instance().DevLibInit();
            HwDecodeResMgr::Instance();
            Ret = BccTest();
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BccTest Ret =" << Ret << endl;
            exit(1);
        }

        if (argc > 3)
        {
            FT4222Dev::Instance().PrintAllDevice();
            if ((strcmp(argv[1], "fpga-upgrade") == 0) || (strcmp(argv[1], "FPGA-UPGRADE") == 0))
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "fpga-upgrade type:" << argv[2] << ", file:" << argv[3] << endl;
                if (access(argv[3], F_OK) == 0)
                {
                    int Retsult = 0;
                    if ((strcmp(argv[2], "back") == 0) || (strcmp(argv[1], "BACK") == 0))
                    {
                        UpgradeFpga FpgaObj(argv[3]);
                        Retsult = FpgaObj.BrunFpgaData(UPGRADE_FPGA_BACKPLANE);
                    }
                    else if ((strcmp(argv[2], "busi") == 0) || (strcmp(argv[1], "BUSI") == 0) ||
                             (strcmp(argv[2], "vsa") == 0) || (strcmp(argv[1], "VSA") == 0) ||
                             (strcmp(argv[2], "vsg") == 0) || (strcmp(argv[1], "VSG") == 0))
                    {
                        UpgradeFpga FpgaObj(argv[3]);
                        Retsult = FpgaObj.BrunFpgaData(UPGRADE_FPGA_BASEBOARD);
                    }
                    else
                    {
                        Retsult = -1;
                        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "fpga-upgrade type error" << endl;
                    }
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "fpga-upgrade result = " << Retsult << endl;
                }
                else
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "fpga-upgrade file error" << endl;
                }
                exit(1);
            }
        }
    }

    WTFileSecure::SetDynamicPasswd(DynamicPasswd::GetDynamicPasswd());

    // 日志类初始化
    WTLog::SetLogName(WTConf::GetDir() + "/manager.db");
    WTLog::SetFiFoName(WTConf::GetDir() + "/fifo.manager");
    WTLog::SetLogPreName("WT_MANAGER");
    WTLog::Instance().SetLogPrintEnalbe((2 << LOG_TYPE_END) - 1); //manager的日志输出都打开

    WTFileSecure::Init();

    // int ServerCnt = 0;
    // Ret = BaseConf::Instance().GetDevNum(ServerCnt);
    // if (ServerCnt <= 1)
    // {
    //     vector<WTConf::DevCfg> DevCfgVector;
    //     WTConf::DevCfg DevAssign;
    //     DevAssign.PortMask = 0xff;
    //     DevAssign.VsaMask = 0xf;
    //     DevAssign.VsgMask = 0xf;
    //     DevCfgVector.push_back(DevAssign);
    //     BaseConf::Instance().SetDevCfg(DevCfgVector);
    // }

    //启动数字IQ网卡
    std::string IqEth;
    Ret = DevConf::Instance().GetItemVal("DigitalLibInterface", IqEth);
    if (WT_OK != Ret || IqEth.size() < 4 || IqEth.find("eth") == std::string::npos)
    {
        IqEth = "eth1";
    }
    string SetEthUp = "ip link set dev " + IqEth + " up";
    Basefun::LinuxSystem(SetEthUp.c_str());

    // //初始化校准库, 参数: 单元个数, 每个单元映射端口数
    // int Count = DevLib::Instance().GetUnitBoardModNum(DEV_TYPE_VSG);
    // int UnitMapRfport = WT_RF_1;
    // for (int unit = 0; UnitMapRfport < WT_RF_MAX;++UnitMapRfport)
    // {
    //     DevLib::Instance().GetModId(DEV_TYPE_VSG, UnitMapRfport, unit);
    //     if(unit != 0)
    //     {
    //         break;
    //     }
    // }
    // wt_calibration_initial(Count, --UnitMapRfport);

    // 子管道
    TunnelMgr::Instance();
    (void)WTDeviceInfo::Instance();
    //(部分仪器存在冷去后第一次开机时,SN获取失败的问题，在这里做规避处理)
    //检测背板SN是否合法，不合法则重新获取SN
    // for (int Cnt = 1; WTDeviceInfo::Instance().SnSelfTest() != WT_OK && Cnt <= 4; Cnt++)
    // {
    //     WTLog::Instance().LOGSYSOPT("Device Reget Sn");
    //     //重新读取SN间隔分别为10s,20s,30s,40s.最多重取4次，共100s
    //     sleep(10 * Cnt);
    //     WTDeviceInfo::Instance().DeviceRegetSn();
    // }

    //程序启动，先灯全亮，然后灯全灭，然后亮power红灯
    DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_OFF);           //关闭全部led
    sleep(1);
    DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_ON);            //亮全部led
    sleep(1);
    DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_OFF);           //关闭全部led
    DevLib::Instance().SetLedStatus(LED_POWER, LED_STATUS_RED);         //亮power red led
    sleep(1);

    //创建业务对象以便获取业务板信息，及对业务板进行升级操作。
    DevLib::Instance().CreateBusiObj();
    WTDeviceInfo::Instance().SaveBoardHwVersion();
    Upgrade::Instance().CheckAndReplaceLibCal(true);
    
    DevLib::Instance().CheckbackPlaneInfo();
    if (access((WTConf::GetDir() + "/../upgrade.do").c_str(), F_OK) == 0)
    {
        if (DevLib::Instance().RecordHWInfo() == WT_OK)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "==========DeviceFirmwareRollbackHandler");
            Ret = Upgrade::Instance().DeviceFirmwareRollbackHandler();
            WTLog::Instance().WriteLog(LOG_DEBUG, "DeviceFirmwareRollbackHandler Ret = %#x\n", Ret);
            if (Ret == WT_OK)
            {
                Basefun::LinuxSystem((WTConf::GetDir() + "/../upgrade.sh").c_str());
                Basefun::LinuxSystem("reboot");
            }
        }
        exit(0);
    }

    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "hhhhhhhhhhhhhhhhh" << argc << std::endl;

    int ServerLaunchStat = 0;
    Launcher &Boot = Launcher::Instance();
    Ret = Boot.LauchProcess();
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_LAUNCH_ERROR, "LauchProcess wrror!");

        //启动进程异常或者失败，点亮error的灯
        DevLib::Instance().SetErrorLed(LED_STATUS_ON); // error led

        // server启动失败的情况下，依然确保manager和link的启动；Link启动失败才不启动manager进程
        if (Ret != WT_FORK_SERVER_FAILED)
        {
            exit(1);
        }
        else
        {
            ServerLaunchStat = WT_FORK_SERVER_FAILED;
        }
    }

    //使用异步时需要将socket设置成非阻塞模式
    int Sock = Boot.GetMgrSocket();
    fcntl(Sock, F_SETFL, fcntl(Sock, F_GETFL) | O_NONBLOCK);

    wtev::default_loop Loop;

    //注册WT-Link socket事件，广播设备信息，监控通讯
    Manager Manage(Sock, Loop);
    Manage.Start();

    WTDeviceInfo::Instance().GetSystemInfoByUsbStorage();
    //启动监控
    Monitor Mon(Loop, &Boot, Sock, ServerLaunchStat);
    Mon.RegisterMonitor();
    Boot.SetReRegisterServerHandle(bind(&Monitor::ReRegisterServer, &Mon, placeholders::_1));
    Mon.Run();

    // 记录Manager进程的启动时间
    time_t ManagerBootTime = 0;
    time(&ManagerBootTime);
    Manage.SetBootTime(ManagerBootTime);

    // 仪器Linux开机时间 与 manager启动时间 写入到数据库中
    RecordBootTime();

#ifdef DEBUG
    wtev::sig SigIntEv(Loop);
    SigIntEv.set_(NULL, SignalCb);
    SigIntEv.start(SIGINT);

    wtev::sig SigTermEv(Loop);
    SigTermEv.set_(NULL, SignalCb);
    SigTermEv.start(SIGTERM);
#endif

    Loop.run();

    return 0;
}

// 记录仪器开机时间 与 manager启动时间
void RecordBootTime()
{
    struct sysinfo info;
    time_t cur_time = 0;
    time_t boot_time = 0;

    sysinfo(&info);
    time(&cur_time);

    if (cur_time > info.uptime)
    {
        boot_time = cur_time - info.uptime;
    }
    else
    {
        boot_time = info.uptime - cur_time;
    }

    char Buff[256] = {0};
    TIME_TYPE OsBootTM;
    TIME_TYPE CurTM;

    Basefun::Seconds2TimeType(boot_time, &OsBootTM);
    Basefun::Seconds2TimeType(cur_time, &CurTM);

    sprintf(Buff, "Os boot time: %04d-%02d-%02d %02d:%02d:%02d, manager boot time: %04d-%02d-%02d %02d:%02d:%02d, Fw:%s",
        OsBootTM.year, OsBootTM.mon, OsBootTM.mday, OsBootTM.hour, OsBootTM.min, OsBootTM.sec,
        CurTM.year, CurTM.mon, CurTM.mday, CurTM.hour, CurTM.min, CurTM.sec, 
        WTGetFwVersion());

/*
    // 以下用法是错误的, localtime 是不安全
    struct tm *boot_ptm = localtime(&boot_time);
    struct tm *cur_ptm =  localtime(&cur_time);

    sprintf(Buff, "Os boot time: %d-%-d-%d %d:%d:%d, manager boot time: %d-%-d-%d %d:%d:%d",
        boot_ptm->tm_year + 1900, boot_ptm->tm_mon + 1, boot_ptm->tm_mday, boot_ptm->tm_hour, boot_ptm->tm_min, boot_ptm->tm_sec,
        cur_ptm->tm_year + 1900, cur_ptm->tm_mon + 1, cur_ptm->tm_mday, cur_ptm->tm_hour, cur_ptm->tm_min, cur_ptm->tm_sec);
*/
    WTLog::Instance().LOGSYSOPT(Buff);
}
