#include "devfactory.h"

#include <fcntl.h>
#include <unistd.h>
#include <sstream>
#include <sys/ioctl.h>
#include <sys/mman.h>  

#include "devdef.h"
#include "devlib.h"
#include "../wtlog.h"
#include "../wterror.h"

DevVsa *DevFactory::CreateVsaMod(int ModId, bool IsSetFdAndMap)
{
    std::ostringstream Stream;
    Stream << WTVSAFILEPATH << ModId;

    if (access(Stream.str().c_str(), F_OK) != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " access vsa board" << ModId << " faild" << std::endl;
        return nullptr;
    }

    int fd = open(Stream.str().c_str(), O_RDWR | O_CLOEXEC);
    if (fd < 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " open vsa board" << ModId << " faild" << std::endl;
        return nullptr;
    }

    DevVsa *Vsa = nullptr;
    int Version = 0;
    if (GetUbHwVersion(fd, Version) == WT_OK)   //TODO获取硬件版本
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--- Vsa Mod" << ModId << " hw version =" << Version << " ---" << std::endl;
        Vsa = new DevVsa(Version, m_JsonRoot);
    }
    if (Vsa != nullptr)
    {
        Vsa->SetModFd(ModId, fd);
        if (IsSetFdAndMap)
        {
            Vsa->SetDrvFasync();
        }
    }
    else
    {
        close(fd);
    }

    return Vsa;
}

DevVsg *DevFactory::CreateVsgMod(int ModId, bool IsSetFdAndMap)
{
    std::ostringstream Stream;
    Stream << WTVSGFILEPATH << ModId;

    if (access(Stream.str().c_str(), F_OK) != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " access vsg board" << ModId << " faild" << std::endl;
        return nullptr;
    }

    int fd = open(Stream.str().c_str(), O_RDWR | O_CLOEXEC);
    if (fd < 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " open vsg board" << ModId << " faild" << std::endl;
        return nullptr;
    }

    DevVsg *Vsg = nullptr;
    int Version = 0;
    if (GetUbHwVersion(fd, Version) == WT_OK)   //TODO获取硬件版本
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--- Vsg Mod" << ModId << " hw version =" << Version << " ---" << std::endl;
        Vsg = new DevVsg(Version, m_JsonRoot);
    }
    if (Vsg != nullptr)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " CreateVsgBoard Success" << std::endl;
        Vsg->SetModFd(ModId, fd);
        if (IsSetFdAndMap)
        {
            Vsg->SetDrvFasync();
        }
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " CreateVsgBoard Failed" << std::endl;
        close(fd);
    }

    return Vsg;
}

DevBack *DevFactory::CreateBackBoard()
{
    std::ostringstream Stream;
    Stream << WTBACKFILEPATH;

    if (access(Stream.str().c_str(), F_OK) != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " access back board faild" << std::endl;
        return nullptr;
    }

    int fd = open(Stream.str().c_str(), O_RDWR | O_CLOEXEC);
    if (fd < 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " open back board faild" << std::endl;
        return nullptr;
    }

    DevBack *Back = nullptr;
    int Version = 0;
    if (GetUbHwVersion(fd, Version) == WT_OK)   //TODO获取硬件版本
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--- Back " << "hw version=" << Version << " ---" << std::endl;
        Back = new DevBack(Version, m_JsonRoot);
    }

    if (Back != nullptr)
    { 
        Back->SetModFd(0, fd);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " CreateBackBoard Success" << std::endl;
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " CreateBackBoard Failed" << std::endl;
        close(fd);
    }
    return Back;
}

int DevFactory::GetUbHwVersion(int fd, int &HWVersion)
{
    int Ret = WT_OK;
    int cmd = IOCTL_CMD(GET_UNIT_BOARD_HW_VERSION, sizeof(int));
    //打开设备文件
    if ((Ret = ioctl(fd, cmd, &HWVersion)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GetUnitBoardHWVersion ioctl error");
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetUbHwVersion ret = %d, version = %#x\n", Ret, HWVersion);
    return Ret;
}
