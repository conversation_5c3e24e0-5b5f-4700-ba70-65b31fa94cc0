#pragma once
#ifndef _IOCONTROL_PIPE_H__
#define _IOCONTROL_PIPE_H__
#include "IOControl.h"



class IOControl_API IOControl_Pipe :
    public IOControl
{
public:
    IOControl_Pipe();
    virtual ~IOControl_Pipe();

    virtual void IOInitializeState();
    virtual void IOTerminateState();
    virtual int IOConnect(const char *ip, int port, int timeOutMs = 3000);
    virtual int IODisconnect();
    virtual int IOSend(const char *sendBuff, unsigned int buffLength, unsigned int timeOutMs);
    virtual int IORecv(char *recvBuff, unsigned int buffLength, unsigned int timeOutMs);
    virtual int IOCleanBuff();
    virtual int IOExchange(const char *sendBuff, unsigned int sendBuffLength, char *recvBuff, unsigned int recvBuffLength);
    virtual int IOGetConnectState();
    virtual int IOGetHandle();
    virtual int IOGetErrorCode();
    virtual void IOSetAliasName(const char *name);
    virtual const char *IOGetAliasName();
    virtual int IOGetConnectDetail(char *buffer, int buffersize);

private:
    int m_nRxTimeout_ms;
    int m_nTxTimeout_ms;
    void *m_shell;
    char m_AliasName[256];
};

#endif

