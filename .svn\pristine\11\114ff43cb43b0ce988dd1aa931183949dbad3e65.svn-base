//*****************************************************************************
//  File: business.h
//  业务处理
//  Data: 2016.8.11
//*****************************************************************************
#ifndef __WT_BUSN_H__
#define __WT_BUSN_H__

#include <sys/stat.h>
#include <memory>
#include <queue>
#include <string>

#include "wtev++.h"
#include "wt_protocol.h"
#include "connector.h"
#include "vsa.h"
#include "vsg.h"
#include "templib.h"
#include "../../general/digital/digitallib.h"

class Service;

struct GUIFileInfo      //获取的GUI文件信息
{
    char FileName[256] = {0};       //相对目录+文件名;
    int FileDataLen = 0;            //文件大小
    std::unique_ptr<ReadFile> File; //文件内容
};

struct GUIVersion
{
    char LicTechName[32];
    char Version[40];
};

//子网信息
struct SubIP
{
    int Type;           //IP Type
    char IP[16];        //详细的IP
};

//器件参数配置
struct ComponentParam
{
    int BoardID;        //单板编号
    int LinkID;         //链路编号
    int ComponentID;    //器件编号
    int ChipID;         //片选编号
    int Addr;           //包含寄存器地址编号
};

class Business
{
public:
    Business(Service *pSrv, std::shared_ptr<Connector> ExtConn, bool Exclude, const wtev::loop_ref &Loop)
        : m_Service(pSrv), m_Vsa(make_shared<WTVsa>(ExtConn, Exclude, Loop)), m_Vsg(make_shared<WTVsg>(ExtConn, Exclude, Loop)), m_ExtConn(ExtConn)
    {
        // 由于TB模式需要同时控制VSA/VSG，所以讲VSG对象指针传递给VSA对象，以便TB模式的控制。
        // TB测试时会在同一连接下发VSA/VSG参数，可以在同一BUSI对象内申请VSG/VSA资源和处理API命令。
        // 正常WIFI模式，在不同连接下发VSA/VSG参数，在不同的BUSI对象申请VSA/VSG资源和处理API命令。
        GetVsa().SetVsg(m_Vsg);
        GetVsg().SetVsa(m_Vsa);

        // 设置DIG业务对象
        m_DigLib.reset(new (std::nothrow) DigitalLib);
        GetVsa().SetDigLib(m_DigLib);
        GetVsg().SetDigLib(m_DigLib);
    }

    ~Business() { Terminate(); }

    //*****************************************************************************
    // 停止所有的业务
    // 参数: 无
    // 返回值: 无
    //*****************************************************************************
    void Terminate();

    //*****************************************************************************
    // 连接MIMO从机
    // 参数[IN]: Chain : 从机对应的天线
    //           IP ：从机IP地址
    //        SubId : 子仪器ID
    // 返回值: 成功或错误码
    //*****************************************************************************
    int ConnectMimoDev(int Chain, const char *IP, int SubId);

    //*****************************************************************************
    // 断开MIMO从机
    // 参数[IN]: Chain : 从机对应的天线
    // 返回值: 成功或错误码
    //*****************************************************************************
    int DisconnMimoDev(int Chain);

    //*****************************************************************************
    // 添加MIMO从机
    // 参数[IN]: Conn : 与MIMO从机的连接
    // 返回值: 无
    //*****************************************************************************
    void AddMimoDev(std::shared_ptr<Connector> Conn);

    //*****************************************************************************
    // 删除MIMO从机
    // 参数[IN]: Conn : 与MIMO从机的连接
    // 返回值: 无
    //*****************************************************************************
    void DelMimoDev(std::shared_ptr<Connector> Conn);

    //*****************************************************************************
    // 函数: GetGUIFileVersionByType()
    // 功能: 通过协议类型字符串来获取对应的GUI文件的版本（配置文件中获取）
    // 参数 [IN]：Type：协议类型字符串
    // 参数 [OUT]：FileVersionBuf：版本信息
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetGUIFileVersionByType(const char *Type, GUIVersion &GUIVersionInfo);

    //*****************************************************************************
    // 函数: GetGUIFileByType()
    // 功能: 通过协议类型字符串来获取GUI文件
    // 参数 [IN]：Type：协议类型字符串
    // 参数 [OUT]：FileBuf：文件内容
    // 参数 [OUT]：Len：文件字节长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetGUIFileByType(const char *Type, std::vector<GUIFileInfo> &GUIFile, int &Len);

    //*****************************************************************************
    // 函数: GetDeviceTemperatureHandler()
    // 功能: 获取设备相关器件的温度
    // 参数 [IN]：Data：协议传过来的数据（暂时没使用，保留）
    // 参数 [OUT]：TemperBuf：获取到的温度的信息
    //            DataLen：获取到的温度的信息的数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetDeviceTemperatureHandler(void *Data, char *TemperBuf, int &DataLen);

    //*****************************************************************************
    // 函数: GetDeviceTemperatureHandler()
    // 功能: 获取设备相关器件的温度
    // 参数 [IN]：Data：协议传过来的数据（暂时没使用，保留）
    // 参数 [OUT]：TemperBuf：获取到的温度的信息
    //            DataLen：获取到的温度的信息的数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetHistoryTemperatureHandler(void *Data, char *TemperBuf, int &DataLen);

    //*****************************************************************************
    // 函数: GetFanSpeedHandler()
    // 功能: 获取器件的参数值
    // 参数 [IN]：Data：
    // 参数 [OUT]：Speed：风速
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetFanSpeedHandler(void *Data, int &Speed);

    //*****************************************************************************
    // 函数: SetFanSpeedHandler()
    // 功能: 设置器件的参数值
    // 参数 [IN]：Data：风速
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetFanSpeedHandler(void *Data);

    //*****************************************************************************
    // 函数: GetComponentParamValue()
    // 功能: 获取器件的参数值
    // 参数 [IN]：Param : 器件信息
    // 参数 [OUT]：Data：读取到的数据
    //             DataLen：数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetComponentParamValue(ComponentParam *Param, void *Data, int &DataLen);

    //*****************************************************************************
    // 函数: SetComponentParamValue()
    // 功能: 设置器件的参数值
    // 参数 [IN]：Param : 器件信息
    //            Data：配置数据
    //            DataLen：Data数据大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetComponentParamValue(ComponentParam *Param, void *Data, int DataLen);

    //*****************************************************************************
    // 功能: 设置MIMO参数
    // 参数 [IN]：Id : MIMO设备ID，0：本机，1 - n从机ID
    //         Param : MIMO参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetMimoParam(int Id, void *Param);

    //*****************************************************************************
    // 函数: GetLicenseInfoHandler()
    // 功能: 获取license相关信息数据处理，主要包括license类型，Technology类型，有效开始时间，有效结束时间等
    // 参数 [OUT]：LicItemsInfo：lincense信息的vector
    // 参数 [OUT]：DataLen：获取到的vector数据字节总大小
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLicenseInfoHandler(std::vector<LicItemInfo> &LicItemsInfo, int &DataLen);

    //*****************************************************************************
    // 函数: GetCurSubDeviceCfgHandler()
    // 功能: 获取当前子仪器的硬件资源划分信息
    // 参数 [OUT]：Cfg：当前子仪器的资源分配信息
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetCurSubDeviceCfgHandler(WTConf::DevCfg &Cfg);

    //*****************************************************************************
    // 函数: SigFileTx
    // 功能: 信号文件下发
    // 参数 [IN]：Name : 信号文件名
    //            Data : 文件数据
    //            Len : 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SigFileTx(const std::string &Name, void *Data, int Len);

    //*****************************************************************************
    // 函数: SigFileExist
    // 功能: 信号文件是否存在
    // 参数 [IN]：Name : 信号文件名
    // 参数 [OUT]：Exist : 是否存在
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SigFileExist(const std::string &Name, int &Exist);

    //*****************************************************************************
    // 函数: DelSigFile
    // 功能: 删除信号文件
    // 参数 [IN]：Name : 信号文件名
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int DelSigFile(const std::string &Name);

    //*****************************************************************************
    // 函数: GetSigFileList
    // 功能: 获取信号文件列表
    // 参数 [IN]: Dir : 目录
    // 参数 [OUT]：FileList : 文件列表
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetSigFileList(std::vector<std::string> &FileList, const std::string &Dir);

    //*****************************************************************************
    // 函数: GetSigFile
    // 功能: 获取信号文件
    // 参数 [IN]：Name : 信号文件名
    // 参数 [OUT]：Data: 信号数据
    //             Len : 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetSigFile(const std::string &Name, void **Data, int &Len);

    //*****************************************************************************
    // 功能: 设置校准数据
    // 参数 [IN]：Name : 校准数据文件名
    //            Data : 文件数据
    //            Len : 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetCalData(const char *Name, void *Data, int Len);

    //*****************************************************************************
    // 功能: 获取校准数据
    // 参数 [IN]：Name : 校准数据文件名
    //            Buf : 保存数据用的buffer
    //         BufLen : buffer长度
    // 参数 [OUT]：DataLen: 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetCalData(const char *Name, void *Buf, int BufLen, int &DataLen);

    //*****************************************************************************
    // 功能: 保存线衰文件
    // 参数 [IN]：Name : 保存线衰文件名
    //            Buf : 保存数据用的buffer
    //         BufLen : buffer长度
    // 参数 [OUT]：DataLen: 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetPathLossFile(void *Data, int Len);

    //*****************************************************************************
    // 功能: 读取线衰文件
    // 参数 [IN]：Name : 读取线衰文件名
    //            Buf : 读取数据用的buffer
    //         BufLen : buffer长度
    // 参数 [OUT]：DataLen: 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetPathLossFile(void **Data, int &Len);

    //*****************************************************************************
    // 函数: DeleteSubNet()
    // 功能: 删除子网口配置
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int DeleteSubNet(void);

    WTVsa &GetVsa()
    {
        return *m_Vsa.get();
    }

    WTVsg &GetVsg()
    {
        return *m_Vsg.get();
    }

    std::shared_ptr<WTVsg> &GetVsgPtr()
    {
        return m_Vsg;
    }

    //*****************************************************************************
    // 发送meter控制参数给监视机
    // 参数[IN]: MeterParam: meter控制参数内容
    //          Len : 数据长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    void SendMeterParamtoMon(const void *MeterParam, int Len);

    //*****************************************************************************
    // 函数: GetLogHandler()
    // 功能: 获取日志数据实际处理逻辑
    // 参数 [IN]：Data：查询类型
    // 参数 [IN]: DataLen：查询内容总长度，主要为了下发sql语句时截取sql有用~
    // 参数 [OUT]：Record：日志内容
    // 参数 [OUT]：VectorSize：日志内容总size
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLogHandler(void *Data, int DataLen, std::vector<std::string> &Record, int &VectorSize);

    //*****************************************************************************
    // 函数: GetLogFlag()
    // 功能: 获取不同日志保存控制标识值数据
    // 参数 [OUT]：Data：不同类型日志是否保存控制标识内容总数据：日志类型1+日志类型使能标识值1+日志类型2+日志类型使能标识值2+...
    // 参数 [OUT]：Len：sizeof（int）*2*Cnt，返回的总数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLogFlag(void *Data, int &Len);

    //*****************************************************************************
    // 函数: SetLogFlag()
    // 功能: 设置日志保存使能标识
    // 参数 [IN]：Data：协议下发的内容：日志类型1+日志类型使能标识值1+日志类型2+日志类型使能标识值2+...
    // 参数 [IN]：Len：内容总长度，必须是sizeof（int）*2的整数倍
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetLogFlag(void *Data, int Len);

    //*****************************************************************************
    // 函数: CustomizeFileTx
    // 功能: 信号文件下发
    // 参数 [IN]：Name : 信号文件名
    //            Data : 文件数据
    //            Len : 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int CustomizeFileTx(const std::string &Name, void *Data, int Len);

    //*****************************************************************************
    // 函数: GetCustomizeFile
    // 功能: 上传信号文件
    // 参数 [IN]：Name : 信号文件名
    // 参数[OUT]：Data : 文件数据
    //            Len : 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetCustomizeFile(const std::string &Name, void **Data, int &Len);

    //主机获取连接发起端的连接信息
    int SendUserConnInfo(void *Data, int Len);

private:
    bool IsMimo(void)
    {
        return !m_SlaveConn.empty();
    }

    //信号文件管理部分，要根据相应传入的文件名目录创建二级目录
    void CreatePath(const std::string &LocalDir, std::string &NeedCreatePath);
    std::string GetLowWaveDir() { return "/tmp/low_wave/"; }
    std::string GetWaveDir() { return "/tmp/wave/"; }
private:
    Service *m_Service;    //对应的service类
    std::shared_ptr<DigitalLib> m_DigLib;
    std::shared_ptr<WTVsa> m_Vsa;
    std::shared_ptr<WTVsg> m_Vsg;
    std::shared_ptr<Connector> m_ExtConn;              //外部连接
    std::list<std::shared_ptr<Connector>> m_SlaveConn; //与MIMO从机连接
    std::unique_ptr<SigFile> m_SigFile;                //信号文件读取缓存
    std::unique_ptr<ReadFile> m_ReadFile;
};

#endif
