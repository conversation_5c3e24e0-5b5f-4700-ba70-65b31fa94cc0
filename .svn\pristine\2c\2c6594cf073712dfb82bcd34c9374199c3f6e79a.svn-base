#ifndef _VSA_TF_H_
#define _VSA_TF_H_
#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif
    //Trigger frame info
    scpi_result_t GetVsaRstTriggerFrameInfo(scpi_t * context);
    scpi_result_t GetVsaRstTF_TrigType(scpi_t * context);
    scpi_result_t GetVsaRstTF_MoreTF(scpi_t * context);
    scpi_result_t GetVsaRstTF_Length(scpi_t * context);
    scpi_result_t GetVsaRstTF_CSRequired(scpi_t * context);
    scpi_result_t GetVsaRstTF_BandWidth(scpi_t * context);
    scpi_result_t GetVsaRstTF_MModeLTF(scpi_t * context);
    scpi_result_t GetVsaRstTF_GILTF(scpi_t * context);
    scpi_result_t GetVsaRstTF_LTFSymNum(scpi_t * context);
    scpi_result_t GetVsaRstTF_STBC(scpi_t * context);
    scpi_result_t GetVsaRstTF_LDPCExtra(scpi_t * context);
    scpi_result_t GetVsaRstTF_APTxPower(scpi_t * context);
    scpi_result_t GetVsaRstTF_AFactor(scpi_t * context);
    scpi_result_t GetVsaRstTF_PEDisambiguity(scpi_t * context);
    scpi_result_t GetVsaRstTF_Doppler(scpi_t * context);
    scpi_result_t GetVsaRstTF_MidPeriodicity(scpi_t * context);
    scpi_result_t GetVsaRstTF_SReuse(scpi_t * context);
    scpi_result_t GetVsaRstTF_TBUserNumber(scpi_t * context);
    scpi_result_t GetVsaRstTF_AID(scpi_t * context);
    scpi_result_t GetVsaRstTF_RUIndex(scpi_t * context);
    scpi_result_t GetVsaRstTF_Segment(scpi_t * context);
    scpi_result_t GetVsaRstTF_Coding(scpi_t * context);
    scpi_result_t GetVsaRstTF_MCS(scpi_t * context);
    scpi_result_t GetVsaRstTF_DCM(scpi_t * context);
    scpi_result_t GetVsaRstTF_SSStream(scpi_t * context);
    scpi_result_t GetVsaRstTF_NSS(scpi_t * context);
    scpi_result_t GetVsaRstTF_RSSI(scpi_t * context);
    scpi_result_t GetVsaRstTF_MUSpaingFactor(scpi_t * context);
    scpi_result_t GetVsaRstTF_TidAggLimit(scpi_t * context);
    scpi_result_t GetVsaRstTF_PreferredAC(scpi_t * context);
#ifdef __cplusplus
}
#endif

#endif