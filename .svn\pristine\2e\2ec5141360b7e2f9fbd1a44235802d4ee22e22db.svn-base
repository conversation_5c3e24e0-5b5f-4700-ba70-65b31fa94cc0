#include "scpi_gen_11ax_er.h"

#include "commonhandler.h"
#include <iostream>
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include <sys/time.h>

//er和su，实际使用的是同一个结构体
int Is11axER(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnWifi.get()->commonParam.standard;
        int PPDU = attr->PnWifi.get()->commonParam.subType;
        if (demod < WT_DEMOD_11AX_20M || demod > WT_DEMOD_11AX_80_80M)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (HE_EXTEND_PPDU != PPDU)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

static int GetERIntParam(scpi_t * context, int minNum, int maxNum, int &Value)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11axER(attr);
        
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Value < minNum || Value > maxNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}


scpi_result_t Set11AX_ER_MCS(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 11, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.MCS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_Coding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.CodingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_GLTFSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 4, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.GILTFSize = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_Doppler(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Doppler = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_BeamChange(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.BeamChange = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_PE(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.PE = Value;
        if (!Value)
        {
            break;
        }
        iRet = GetERIntParam(context, 0, 2, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.PE_Type = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_TXOP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 127, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.TXOP = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_ULDL(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.UL_DL = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_NSS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 1, 8, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.NSS = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_DCM(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.DCM = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_SpatialReuse(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 15, Value);
        IF_BREAK(iRet);
        memset(attr->PnWifi.get()->PN11ax_SU.Spatial_Reuse, 0,
            sizeof(attr->PnWifi.get()->PN11ax_SU.Spatial_Reuse));

        attr->PnWifi.get()->PN11ax_SU.Spatial_Reuse[0] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_STBC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.STBC = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_BssColor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 63, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.BSScolor = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_MidamblePeriodicity(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 1, 2, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Midamble_Periodicity = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_Beamformed(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Beamformed = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_ER_SoundingNDP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetERIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.SoundingNDP = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
