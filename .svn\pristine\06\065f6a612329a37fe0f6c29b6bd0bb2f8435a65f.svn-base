#ifndef _SCPI_GEN_11AH_SU_H_
#define _SCPI_GEN_11AH_SU_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif
    //commone setting
    scpi_result_t Set11AH_PPDUFormat(scpi_t *context);

    //su
    scpi_result_t Set11AH_SU_DUPIndication(scpi_t *context);
    scpi_result_t Set11AH_SU_NDPMode(scpi_t *context);
    scpi_result_t Set11AH_SU_NDPCmacPPDUBodybit(scpi_t *context);
    scpi_result_t Set11AH_SU_STBC(scpi_t *context);
    scpi_result_t Set11AH_SU_ULDL(scpi_t *context);
    scpi_result_t Set11AH_GuardInterval(scpi_t *context);
    scpi_result_t Set11AH_SU_NSS(scpi_t * context);
    scpi_result_t Set11AH_SU_MCS(scpi_t *context);
    scpi_result_t Set11AH_SU_CodingType(scpi_t *context);
    scpi_result_t Set11AH_SU_BSSColor(scpi_t *context);
    scpi_result_t Set11AH_SU_PartialAID(scpi_t *context);
    scpi_result_t Set11AH_SU_Smoothing(scpi_t *context);
    scpi_result_t Set11AH_SU_Beamformed(scpi_t *context);

    scpi_result_t Set11AH_SU_AGG(scpi_t *context);
    scpi_result_t Set11AH_SU_ResponseIndication(scpi_t *context);
    scpi_result_t Set11AH_SU_TravelingPilots(scpi_t *context);
    //Q_MAT
    scpi_result_t Set11AH_SU_QMat(scpi_t *context);
    scpi_result_t Set11AH_SU_QMatNtx(scpi_t *context);
    scpi_result_t Set11AH_SU_QMatType(scpi_t *context);
    scpi_result_t Set11AH_SU_QMatDelay(scpi_t *context);
    scpi_result_t Set11AH_SU_QMatMap(scpi_t *context);
#ifdef __cplusplus
}
#endif

#endif
