#include "vsa_11az.h"
#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "commonhandler.h"
#include "tester.h"
#include "internal.h"
#include "tester_mt.h"
#include "vsa_data_info.h"

static int Is11AZDemo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    (void)context;
    // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    // int demod = attr->vsaParam.Demode;
    // if (demod < WT_DEMOD_11AZ_20M || demod > WT_DEMOD_11AZ_160M)
    // {
    //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    // }
    return iRet;
}

// /*****************************set analyze param**********************************/
static int SetVsaAzAlzIntParam(scpi_t *context, u32 *Param, int minValue, int maxValue)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            IF_ERR_RETURN(iRet);
        }
        if (Value < minValue || Value > maxValue)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            IF_ERR_RETURN(iRet);
        }

        *Param = Value;
    } while (0);
    return iRet;
}

scpi_result_t SetVsaAlzAzSecureMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    u32 ParamValue = 0;
    iRet = SetVsaAzAlzIntParam(context, &ParamValue, 0, 1);
    attr->m_AzAlyParam.SecureMode = ParamValue;
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzAzTxWindow(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    u32 ParamValue = 0;
    iRet = SetVsaAzAlzIntParam(context, &ParamValue, 0, 1);
    attr->m_AzAlyParam.TxWinFlg = ParamValue;
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzAzUserCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    u32 ParamValue = 0;
    iRet = SetVsaAzAlzIntParam(context, &ParamValue, 1, 8);
    attr->m_AzAlyParam.UserNum = ParamValue;
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzAzUserNss(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AZDemo(context);
        if (iRet)
        {
            break;
        }

        int Number[1] = {0};
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        int UserID = Number[0];
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if(0 == UserID)
        {
            for(int i = 0; i < ALG_11AZ_MAX_USER_NUM; i++)
            {
                attr->m_AzAlyParam.User[i].Nsts = Value;
            }
        }
        else
        {
            attr->m_AzAlyParam.User[UserID - 1].Nsts = Value;
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}
scpi_result_t SetVsaAlzAzUserLTFRepeat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AZDemo(context);
        if (iRet)
        {
            break;
        }

        int Number[1] = {0};
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        int UserID = Number[0];
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if(0 == UserID)
        {
            for(int i = 0; i < ALG_11AZ_MAX_USER_NUM; i++)
            {
                attr->m_AzAlyParam.User[i].LtfRep = Value;
            }
        }
        else
        {
            attr->m_AzAlyParam.User[UserID - 1].LtfRep = Value;
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

static unsigned char HexChar2Byte(const char value)
{
    char tmp = toupper(value);
    return (tmp > '9' ? (tmp - 'A' + 10) : (tmp - '0'));
}

static int ConvertHexStringToBytes(unsigned char *Src, unsigned char *Dest, int length)
{
    unsigned char *srcTemp = Src;
    unsigned char c = 0;
    for (int i = 0; i < length; i++)
    {
        c = HexChar2Byte(*srcTemp++);
        if (c >= 16)
        {
            return -1;
        }
        Dest[i] = (c << 4);
        c = HexChar2Byte(*srcTemp++);
        if (c >= 16)
        {
            return -1;
        }
        Dest[i] |= c;
    }
    return 0;
}

scpi_result_t SetVsaAlzAzUserLTFIV(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AZDemo(context);
        if (iRet)
        {
            break;
        }

        int Number[1] = {0};
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        int UserID = Number[0];
        char Value[256] = {0};
        size_t copyLen = 0;
        const int MAX_ARR_CNT = 16;
        int IntValueArr[MAX_ARR_CNT] = {0};
        if (!SCPI_ParamCopyText(context, Value, sizeof(Value) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        unsigned char Key[MAX_ARR_CNT] = {0};
        ConvertHexStringToBytes((unsigned char *)Value, Key, MAX_ARR_CNT);

        for(int i = 0; i < MAX_ARR_CNT; i++)
        {
            IntValueArr[i] = Key[i];
        }
        
        if(0 == UserID)
        {
            for(int i = 0; i < ALG_11AZ_MAX_USER_NUM; i++)
            {
                memcpy(attr->m_AzAlyParam.User[i].LtfIv, IntValueArr, sizeof(int) * MAX_ARR_CNT);

            }
        }
        else
        {
            memcpy(attr->m_AzAlyParam.User[UserID - 1].LtfIv, IntValueArr, sizeof(int) * MAX_ARR_CNT);
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaAlzAzUserLTFKey(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AZDemo(context);
        if (iRet)
        {
            break;
        }

        int Number[1] = {0};
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        int UserID = Number[0];
        char Value[256] = {0};
        size_t copyLen = 0;
        const int MAX_ARR_CNT = 16;
        int IntValueArr[MAX_ARR_CNT] = {0};
        if (!SCPI_ParamCopyText(context, Value, sizeof(Value) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        unsigned char Key[MAX_ARR_CNT] = {0};
        ConvertHexStringToBytes((unsigned char *)Value, Key, MAX_ARR_CNT);

        for(int i = 0; i < MAX_ARR_CNT; i++)
        {
            IntValueArr[i] = Key[i];
        }
        
        if(0 == UserID)
        {
            for(int i = 0; i < ALG_11AZ_MAX_USER_NUM; i++)
            {
                memcpy(attr->m_AzAlyParam.User[i].LtfKey, IntValueArr, sizeof(int) * MAX_ARR_CNT);

            }
        }
        else
        {
            memcpy(attr->m_AzAlyParam.User[UserID - 1].LtfKey, IntValueArr, sizeof(int) * MAX_ARR_CNT);
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

/*******************************get vsa result***********************************/
static int Is11AZ(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11AZ_20M, WT_DEMOD_11AZ_160M);
}

#define Resq11azDataInfo(name, isDouble)                                                    \
do                                                                                          \
{                                                                                           \
    DataInfo11az DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = Is11AZ(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)

#define Resq11azDataInfoBitInfo(name, lenName)                                              \
do                                                                                          \
{                                                                                           \
    DataInfo11az DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = Is11AZ(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    int BitLen = (int)DataInfo.lenName;                                                     \
    SCPI_ResultInt(context, BitLen);                                                        \
    if(BitLen > 0)                                                                          \
    {                                                                                       \
        unique_ptr<char[]> tmpBuf(new char[BitLen + 50]);                                   \
        int tmpLen = 0;                                                                     \
        for (int i = 0; i < BitLen; i++)                                                    \
        {                                                                                   \
            tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", DataInfo.name[i]);               \
        }                                                                                   \
        SCPI_ResultText(context, (const char *)tmpBuf.get());                               \
    }                                                                                       \
} while (0)

scpi_result_t GetVsaRst_11az_SIGACRC(scpi_t *context)
{
    Resq11azDataInfo(HeSigAValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_PunctureMode(scpi_t *context)
{
    Resq11azDataInfo(PuncturingMode, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_BW(scpi_t *context)
{
    Resq11azDataInfo(Bw, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_PPDU(scpi_t *context)
{
    Resq11azDataInfo(PsduFormat, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_RUCnt(scpi_t *context)
{
    Resq11azDataInfo(RuNum, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_UserCnt(scpi_t *context)
{
    Resq11azDataInfo(UserNum, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LTFCnt(scpi_t *context)
{
    Resq11azDataInfo(HeltfNum, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LTFType(scpi_t *context)
{
    Resq11azDataInfo(HeltType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LTFLen(scpi_t *context)
{
    Resq11azDataInfo(HeltfLen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_GILen(scpi_t *context)
{
    Resq11azDataInfo(GILen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_FrameLen(scpi_t *context)
{
    Resq11azDataInfo(FrameLen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_BSSColor(scpi_t *context)
{
    Resq11azDataInfo(BSSColor, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_BeamChange(scpi_t *context)
{
    Resq11azDataInfo(PreTxBF, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LDPCExtra(scpi_t *context)
{
    Resq11azDataInfo(LDPCExtra, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_PEDisambiguity(scpi_t *context)
{
    Resq11azDataInfo(PE, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_PELen(scpi_t *context)
{
    Resq11azDataInfo(PeLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_PreFecPadFactor(scpi_t *context)
{
    Resq11azDataInfo(PreFEC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_Doppler(scpi_t *context)
{
    Resq11azDataInfo(Doppler, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_MidamblePeriodicity(scpi_t *context)
{
    Resq11azDataInfo(Midamble_Periodicity, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_TXOP(scpi_t *context)
{
    Resq11azDataInfo(TXOP, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_SReuse(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    unique_ptr<DataInfo11az> DataInfo = make_unique<DataInfo11az>();
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, DataInfo.get(), sizeof(DataInfo11az));
    IF_ERR_RETURN(iRet);

    for (int i = 0; i < 4; i++)
    {
        SCPI_ResultInt(context, DataInfo.get()->SpatialReuse[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_SigA_Bit(scpi_t *context)
{
    Resq11azDataInfoBitInfo(SigABit, SigABitLen);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LSIGParity(scpi_t *context)
{
    Resq11azDataInfo(LsigValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LSIGRate(scpi_t *context)
{
    Resq11azDataInfo(LsigRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LSIGLen(scpi_t *context)
{
    Resq11azDataInfo(LsigLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_LSig_Bit(scpi_t *context)
{
    Resq11azDataInfoBitInfo(LsigBit, LsigBitLen);
    return SCPI_RES_OK;
}

//arb data

static scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);
    if (!arb)
    {
        int DoubleCnt = ResultSize / sizeof(double);
        for (int i = 0; i < DoubleCnt; i++)
        {
            double *TmpData = (double *)ResultBuf.get();
            double Value = *(double *)(TmpData + i);
            SCPI_ResultDouble(context, Value);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_Users_FlatnessPassed(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);
    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    const char *ParamStr = WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_PASSED;
    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);

    int IntCnt = ResultSize / sizeof(int);
    for (int i = 0; i < IntCnt; i++)
    {
        int *TmpData = (int *)ResultBuf.get();
        int Value = *(int *)(TmpData + i);
        SCPI_ResultInt(context, Value);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11az_Users_FlatnessData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_DATA, true);
}

scpi_result_t GetVsaRst_11az_Users_FlatnessMaskUp(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA, true);
}

scpi_result_t GetVsaRst_11az_Users_FlatnessMaskDown(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA, true);
}

scpi_result_t GetVsaRst_11az_Users_FlatnessSection(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE, true);
}

scpi_result_t GetVsaRst_11az_Users_FlatnessSectionMargin(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN, true);
}

scpi_result_t GetVsaRst_11az_Users_ChannelPhaseResponse(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_CHANNEL_PHASE_RESPONSE, true);
}

scpi_result_t GetVsaRst_11az_Users_ChannelAmplitudeResponse(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_11AZ_ALL_USERS_OFDM_CHANNEL_AMPLITUDE_RESP, true);
}