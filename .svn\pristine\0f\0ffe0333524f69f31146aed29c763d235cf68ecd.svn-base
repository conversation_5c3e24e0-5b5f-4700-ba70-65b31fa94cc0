//*********************************************************************************
//  File: upgrade.cpp
//  功能点：设备软件升级，license升级，升级后必须即刻重启~
//  Data: 2016.8.8
//*********************************************************************************
#include "upgrade.h"

#include <iostream>
#include <cstring>
#include <dirent.h>
#include <unistd.h>//close
#include <fstream>
#include <string>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <thread>
#include <stdlib.h>

#include "license.h"
#include "secure.h"
#include "wterror.h"
#include "basefun.h"
#include "wtlog.h"
#include "conf.h"
#include "version.h"
#include "main.h"
#include "devtype.h"
#include "devlib.h"
#include "launch.h"
#include "shmkeyword.h"
#include "../general/ft4222lib/upgradefpga.h"
#include "wtsecurelib.h"
#include "wtlog.h"

using namespace std;
static int MAX_UPDATE_FILES_NUM = 8;//目前升级文件包中最多有八个文件

#define UPGRADE_BASE_MULIT_THREAD 0
#define UPGRADE_FPGA_VC_VD_VERSION 3

static void reverse_buff(void *buff, int size)
{
    unsigned char *p = (unsigned char *)buff;
    for (int i = 0; i < size; ++i)
    {
        *(p + i) = ~(*(p + i));
    }
}

static void mixer_buff(void *buff, int size)
{
#define KEY_VALUE (0xB7)
    unsigned char *p = (unsigned char *)buff;
    for (int i = 0; i < size; ++i)
    {
        *(p + i) = (*(p + i)) ^ KEY_VALUE;
    }
#undef KEY_VALUE
}

Upgrade::Upgrade()
{
    //申请共享内存
    int shmid = shmget(UPGRADE_KEY_SHARE_MEM, sizeof(UpgradeStep), IPC_CREAT | 0666);
    if (shmid == -1)
    {
        m_UpgradeStepPtr = nullptr;
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "shmget failed");
        return;
    }

    UpgradeStep *shm_buf = reinterpret_cast<UpgradeStep *>(shmat(shmid, NULL, SHM_RND));
    if (shm_buf == (void *)-1)
    {
        m_UpgradeStepPtr = nullptr;
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "shmat failed");
        return;
    }
    m_UpgradeStepPtr = shm_buf;
    memset(m_UpgradeStepPtr, 0 , sizeof(UpgradeStep));
}

Upgrade::~Upgrade()
{
    //释放共享内存
    if (m_UpgradeStepPtr != nullptr)
    {
        shmdt(m_UpgradeStepPtr);
        m_UpgradeStepPtr = nullptr;
    }
}

Upgrade &Upgrade::Instance(void)
{
    static Upgrade UpgradeInstance;
    return UpgradeInstance;
}

int Upgrade::WriteUpgradeStep(int Step)
{
    if (Step >= UPGRADE_STEP_MAX || Step < 0)
    {
        return WT_ARG_ERROR;
    }

    //往共享内存写入数据
    m_UpgradeStepPtr->CurStep = Step;
    if (Step == UPGRADE_STEP_ACCTEPT_CMD)
    {
        memset(&(m_UpgradeStepPtr->Timep[UPGRADE_STEP_ACCTEPT_CMD]), 0, sizeof(time_t) * (UPGRADE_STEP_MAX - UPGRADE_STEP_ACCTEPT_CMD));
    }
    time(&(m_UpgradeStepPtr->Timep[m_UpgradeStepPtr->CurStep]));
    WTLog::Instance().WriteLog(LOG_DEBUG, "Upgrade set step = %d\n", Step);
    return WT_OK;
}

int Upgrade::ProLicensePackageHandler(void *Data, int Len, std::vector<LicItemInfo> &LicItemsInfo)
{
    //分析截取license升级文件,升级license操作
    int Ret = WT_OK;
    if(Len < 480)    //lic 不能少于lic头的大小
    {
        Ret = WT_LICENSE_UPGRADE_ERROR;
        WTLog::Instance().LOGERR(Ret, "License file not correct!Pay attention to the correct file format");
    }
    else
    {
        //WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_BUSY_STATE);
        //DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_ON);            //升级时亮全部led
        Ret = License::Instance().ParseLicensePackage((unsigned char *)Data, Len);
        if(Ret == WT_OK)
        {
            Ret = License::Instance().GetPackageLicenseInfos(LicItemsInfo);
            if(Ret == WT_OK)
            {
                if(LicItemsInfo.size() == 0)
                {
                    Ret = WT_LICENSE_NUM_ERROR;
                    WTLog::Instance().LOGERR(Ret, "Get no Package License Infos ,package error");
                }
            }
        }
    }
    if(Ret == WT_OK)
    {
        WTLog::Instance().LOGSYSOPT("Load license package successfully.");
    }
    else
    {
        WTLog::Instance().LOGSYSOPT("Load license package fail.");
    }
    return Ret;
}

int Upgrade::UpdateLicPackageHandler(char *pErrorInfo, int &Len)
{
    int Ret = WT_OK;
    Ret = License::Instance().UpdateLicensePackage(pErrorInfo, Len);

    if(Ret == WT_OK)
    {
        WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_BUSY_STATE);
        DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_ON);            //升级时亮全部led
    }
    if(Ret == WT_OK)
    {
        WTLog::Instance().LOGSYSOPT("Update license successfully.");
    }
    else
    {
        WTLog::Instance().LOGSYSOPT("Update license fail.");
    }
    return Ret;
}

int Upgrade::UpdateLicenseHandler(void *Data, int Len)
{
    //分析截取license升级文件,升级license操作
    int Ret = WT_OK;
    if(Len < LIC_DATA_LENGTH_MINIMUM)    //lic 不能少于lic头的大小
    {
        Ret = WT_LICENSE_UPGRADE_ERROR;
        WTLog::Instance().LOGERR(Ret, "License file not correct!Pay attention to the correct file format");
    }
    else
    {
        Ret = License::Instance().UpdateLicFile((unsigned char *)Data, Len);
        if(Ret == WT_OK)
        {
            WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_BUSY_STATE);
            DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_ON);            //升级时亮全部led
        }
    }

    return Ret;
}

int Upgrade::UpgradePackageAnalyse(unsigned char *Buf, const int Len)
{
    int Ret = WT_OK;

    //获取并解密文件内容
    WriteUpgradeStep(UPGRADE_STEP_DECRYPT);
    m_UpgradeFiles.clear();
    Ret = DecryptPackage(Buf, Len, m_UpgradeFiles);

    if (Ret == WT_OK)
    {
        //验证升级包是否合法
        WriteUpgradeStep(UPGRADE_STEP_ANALYSE);
        Ret = IsFwVersionInPackIllegal();
        if (Ret != WT_OK)
        {
            Basefun::LinuxSystem("rm /home/<USER>/bin/fpga -rf"); //不升级，删除临时升级文件
        }
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "UpgradePackageAnalyse Ret=%d\n", Ret);
    return Ret;
}

int Upgrade::IsFwVersionInPackIllegal(void)
{
    int Ret = WT_OK;
    //判断要升级或者回退的固件版本和当前版本是否兼容的，升级或者回退的固件版本信息在压缩包里面
    UpgradeFileInfo &FileInfo = m_UpgradeFiles[UPG_FILE_TAR];
    if (m_UpgradeFiles.size() > UPG_FILE_TAR && FileInfo.FileLen > 0) //解压逻辑版本
    {
        //解压前先删除一次无效信息
        remove(WTConf::GetDir().append("/fwversion.txt").c_str());
        remove(WTConf::GetDir().append("/crypto_file_list.txt").c_str());
        //解压出固件版本信息文件
        string TarFileName = "/tmp/uprade.tar.gz";
        Basefun::WriteFile(TarFileName.c_str(), (unsigned char *)(FileInfo.FileData.get()), FileInfo.FileLen);
        char Str[256] = {0};
        sprintf(Str, "tar -zxvpf /tmp/uprade.tar.gz -C %s bin/fwversion.txt", WTConf::GetDir().append("/../").c_str());
        Basefun::LinuxSystem(Str);

        sprintf(Str, "tar -zxvpf /tmp/uprade.tar.gz -C %s bin/crypto_file_list.conf", WTConf::GetDir().append("/../").c_str());
        Basefun::LinuxSystem(Str);

#if !SECURE_DISABLE
        if (access(WTConf::GetDir().append("/crypto_file_list.conf").c_str(), F_OK) != 0)
        {
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Update version do not have crypto_file_list info!");
            Ret = WT_UPGRADE_VERSION_LOW;
        }
        else
#endif
        if (access(WTConf::GetDir().append("/fwversion.txt").c_str(), F_OK) != 0)
        {
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Update version do not have version info!");
        }
        else
        {
            string UpdateVerStr = "";
            int SncCalDataVersion = 0;
            int SupportWt428SwVd = 0;
            WTConf Conf(WTConf::GetDir() + "/fwversion.txt");
            Conf.GetItemVal("FwVersion", UpdateVerStr);
            Conf.GetItemVal("snc_cal_data_version", SncCalDataVersion);
            Conf.GetItemVal("wt428_support_switch_vd", SupportWt428SwVd);

            string CurFacVerStr = "";
            //读取出厂版本固件，作为标准版本来判断
            GetFwVersionFormPackage(WTConf::GetDir().append("/WT4xx-FW-factory.upg"), CurFacVerStr);

            WTVersion FactorVer(CurFacVerStr);
            WTVersion CurDevVer(WTGetFwVersion());
            WTVersion CurUpdateVer(UpdateVerStr);
            WTVersion PreB03A("1.1.0.292");  //特殊处理，该版本允许升级

            if (CurUpdateVer > FactorVer || CurUpdateVer == FactorVer)
            {
                int TesterHwType = HW_WT428;
                int SwbHwVersion = 0;
                DevLib::Instance().GetTesterHwType(TesterHwType);
                DevLib::Instance().GetSwbHwVersion(SwbHwVersion);

                if ((TesterHwType == HW_WT428 && CurUpdateVer != PreB03A) && (SwbHwVersion >= VERSION_D && !SupportWt428SwVd))
                {
                    Ret = WT_UPGRADE_VERSION_LOW;
                    sprintf(Str, "wt428 UpdateVer=%s, SwbHwVersion=%d, SupportSwitchVd=%d", UpdateVerStr.c_str(), SwbHwVersion, SupportWt428SwVd);
                    WTLog::Instance().LOGERR(Ret, Str);
                }
                else
                {
                    Ret = WT_OK;   //success
	                if (CurUpdateVer < CurDevVer || SncCalDataVersion < 2)
	                {
	                    sprintf(Str, "DevVer=%s, UpdateVer=%s del noise data", WTGetFwVersion(), UpdateVerStr.c_str());
	                    WTLog::Instance().LOGSYSOPT(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/28C16F0E-1C98-4E1E-B459-2E772F0B575E*", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/606BEB8CC96127E9C2E66CD56FB3B57A7D94600646394743F91AD98F43BF7E66021CDB193ECA5AFB.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/8DF3E92A3844AFA55141DC1741488979B1C33AEC1062EE81F8F3C9CC3AD86723DE610A48408B6152.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/93A647AE44EC7CEA18D0AB65DE62F87187870A12E5FB7BE8C17BC1A0DEE4D97ADB0C562F4B8477C5.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/1940930E6A6FA7C848FFA3FE1FCE468B8C19815A0675A55EEF3D3C50D36C5EF4FF504DD424C97691.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/5E06C9A2BBB4A13D5EA437EB974B99446D2BFA3B0139AC3F74C435B386E66644EB37B24793ED1561.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/6C0472D9689E95B53043B2CC232824019B1E84C835E56F248106D8D25B21EC10FFC7918067EA8014.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                    sprintf(Str, "rm -f %s/calibration/VSA/3D4B446787D264A6A5BEAAC325F608FFE4D88816E53BAEDF557AFF2F4E28006F7F0F39632F700749.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);                    
	                    sprintf(Str, "rm -f %s/calibration/VSA/5BEFCC4ADA1EEB3C6CBD3198B23C347307F3E2E6F68C792CA187B31A3439B0F7426261A1FAE9FB6E.wtfile", WTConf::GetDir().c_str());
	                    printf("del: %s\n", Str);
	                    Basefun::LinuxSystem(Str);
	                }
                }
            }
            else
            {
                Ret = WT_UPGRADE_VERSION_LOW;
                sprintf(Str, "CurDevVer=%s, UpdateVer=%s, FacVer=%s", WTGetFwVersion(), UpdateVerStr.c_str(), CurFacVerStr.c_str());
                WTLog::Instance().LOGERR(Ret, Str);
            }
        }
        remove(WTConf::GetDir().append("/fwversion.txt").c_str());
    }
    return Ret;
}

int Upgrade::UpgradeHandle(void)
{
    int Ret = WT_OK;
    int UpgradeRet = WT_OK;

    // Lambda函数
    auto CheckUpgradeRet = [&] {if (UpgradeRet != WT_OK && Ret == WT_OK)Ret = UpgradeRet; };

    WTLog::Instance().WriteLog(LOG_DEBUG, "GetDevUpgradeState = %d \n", WTDeviceInfo::Instance().GetDevUpgradeState());
    if (WTDeviceInfo::Instance().GetDevUpgradeState() == UPGRADE_BUSY_STATE)
    {
        do
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "m_UpgradeFiles.size() = %ld \n", m_UpgradeFiles.size());
            if (m_UpgradeFiles.size() == MAX_UPDATE_FILES_NUM)
            {
                DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_ON); //升级时亮全部led

                //说明，升级操作的过程必须按下面的顺序执行
                Basefun::LinuxSystem("pkill WT-Server*"); /* 不管升级哪一个程序，都要停止WT-Server */
                UpgradeRet = Launcher::Instance().ShutdownServers();
                RetBreak(UpgradeRet, "UpgradeHandle ShutdownServers faild\n");
                WriteUpgradeStep(UPGRADE_STEP_UNZIP);
                UpgradeFileInfo &FileInfo = m_UpgradeFiles[UPG_FILE_TAR];
                if (FileInfo.FileLen > 0)
                {
                    //step1 解压压缩包到固件目录
                    string TarFileName = "/tmp/uprade.tar.gz";
                    Basefun::WriteFile(TarFileName.c_str(), (unsigned char *)(FileInfo.FileData.get()), FileInfo.FileLen);
                    char Str[256] = {0};
                    sprintf(Str, "tar -zxvpf /tmp/uprade.tar.gz -C %s", WTConf::GetDir().append("/../").c_str());
                    Basefun::LinuxSystem(Str);
                    remove(TarFileName.c_str());
                }

                //step3 判断是否需要更新软件包
                WriteUpgradeStep(UPGRADE_STEP_DEB_SETUP);
                CheckAndReplaceLibCal();
                UpgradeDebSetup(WTConf::GetDir().append("/deb/").c_str());

                //step4 判断是否有系统配置文件要更换
                WriteUpgradeStep(UPGRADE_STEP_SYS_CONFIG);
                string SysCfgFile = WTConf::GetDir() + "/syscfg/syscfg.sh";
                if (access(SysCfgFile.c_str(), F_OK) == 0)
                {
                    Basefun::LinuxSystem(SysCfgFile.c_str());
                    remove(SysCfgFile.c_str());
                }

                //step5 判断是否需升级背板FPGA
                WriteUpgradeStep(UPGRADE_STEP_BACK_FPGA);
                UpgradeRet = UpdateFPGA(DEV_TYPE_BACK);
                CheckUpgradeRet();
                RetWarnning(UpgradeRet, "UpgradeHandle UpdateFPGA DEV_TYPE_BACK faild");

                //step6 判断是否需升级基带板FPGA
                WriteUpgradeStep(UPGRADE_STEP_BASE_FPGA);
                UpgradeRet = UpdateFPGA(DEV_TYPE_BUSI);
                CheckUpgradeRet();
                RetWarnning(UpgradeRet, "UpgradeHandle UpdateFPGA DEV_TYPE_BUSI faild");

                //最后确保删除临时起作用的文件
                WriteUpgradeStep(UPGRADE_STEP_CLEAR_RESTORE);
                Basefun::LinuxSystem((string("rm -rf ") + WTConf::GetDir() + "/fpga").c_str());
                remove(WTConf::GetDir().append("/fwversion.txt").c_str());

                //文件加密
                WTFileSecure::Init();
                WriteUpgradeStep(UPGRADE_STEP_FINISH);

                WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_FINISH_STATE);
            }
            m_UpgradeFiles.clear();
        } while (0);
        CheckUpgradeRet();
        if (UpgradeRet != WT_OK && m_UpgradeStepPtr->CurStep >= UPGRADE_STEP_DEB_SETUP)
        {
            WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_ERROR_STATE);
        }
    }
    return Ret;
}

int Upgrade::UpdateFirmwareHandler(void *Data, int DataLen)
{
    int Ret = WT_OK;
    string CurDir = "";
    CurDir = WTConf::GetDir(); //获取程序的当前目录
    DIR *Dir = NULL;           /* 路径句柄 */
    char Str[512] = {0};

    string PacketName;
    std::unique_ptr<unsigned char[]>pData(new (std::nothrow) unsigned char[UPGRADE_BUFF_SIZE]);
    unsigned int FwLen = DataLen - MAX_NAME_SIZE;
    unsigned char *pFileBuf = nullptr;

    do
    {
        //分析截取升级文件
        WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_BUSY_STATE);
        WriteUpgradeStep(UPGRADE_STEP_WR);
        if (0 == FwLen)
        {
            //升级前从已保存的升级包读取数据
            PacketName = string((char *)Data);
            pFileBuf = (unsigned char *)pData.get();
            FwLen = Basefun::ReadFile((char *)Data, pFileBuf, UPGRADE_BUFF_SIZE);
            WTLog::Instance().WriteLog(LOG_DEBUG, "UpdateFirmwareHandler use tmp file, file=%s\n", PacketName.c_str());
            if (0 >= FwLen)
            {
                Ret = WT_UPGRADE_NO_PACKAGE;
                WTLog::Instance().LOGERR(Ret, "Upgrade package not exit");
                break;
            }
        }
        else
        {
            //升级前先把当前升级包保存到临时文件中
            PacketName = string("/tmp/wt-fw.upg");
            pFileBuf = (unsigned char *)Data + MAX_NAME_SIZE;
            Basefun::WriteFile(PacketName.c_str(), pFileBuf, FwLen);
            WTLog::Instance().WriteLog(LOG_DEBUG, "UpdateFirmwareHandler use sock data, file=%s\n", PacketName.c_str());
        }
        //解析当前的升级包
        Ret = UpgradePackageAnalyse(pFileBuf, FwLen);
        if (WT_OK == Ret)
        {
            WriteUpgradeStep(UPGRADE_STEP_BAK);
            //分析升级文件正确，则保存上一个版本，和当前版本的升级包
            //保存当前运行版本升级包为上一个版本wt-fw.upg-->wt-fw-prev.upg
            if (NULL == (Dir = opendir((CurDir + "/prev/").c_str())))
            {
                memset(Str, 0, sizeof(Str));
                sprintf(Str, "%s/prev", CurDir.c_str());
                mkdir(Str, 0777);
            }
            else
            {
                closedir(Dir);
            }

            if (NULL == (Dir = opendir((CurDir + "/curfw/").c_str())))
            {
                memset(Str, 0, sizeof(Str));
                sprintf(Str, "%s/curfw", CurDir.c_str());
                mkdir(Str, 0777);
            }
            else
            {
                closedir(Dir);
            }

            memset(Str, 0, sizeof(Str));
            rename((CurDir + "/curfw/wt-fw.upg").c_str(), (CurDir + "/prev/wt-fw-prev.upg").c_str());

            //保存新版本升级包为当前版本wt-fw.upg
            //解析正确把当前升级文件保存
            memset(Str, 0, sizeof(Str));
            rename(PacketName.c_str(), (CurDir + "/curfw/wt-fw.upg").c_str());

            Ret = UpgradeHandle(); //升级操作
        }
    } while (0);

    if (Ret == WT_OK)
    {
        WTLog::Instance().LOGSYSOPT("FW upgrade by software successfully.");
    }
    else
    {
        WTLog::Instance().LOGSYSOPT("FW upgrade  by software fail.");
    }
    if (Ret != WT_OK)
    {
        WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_IDLE_STATE);
    }
    return Ret;
}

#define MODULE_UPGRADE_PATH "/tmp/ModuleUpgrade"
#define MODULE_UPGRADE_FILE "/tmp/ModuleUpgrade.tar.gz"
#define MODULE_UPGRADE_SHELL "/tmp/LittleModuleUpgrade.sh"
#define GZ_HEAD_FLAG 0x1F8B08
int Upgrade::ModuleUpdateHandler(void *Data, int DataLen)
{
    //下发的模块.upg升级包内容格式为：int CRC+int checksum + xx.tar.gz包
    int Ret = WT_OK;
    //设置升级状态
    WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_BUSY_STATE);

    //删除可能存在的旧文件
    Basefun::LinuxSystem((string("rm -f ") + MODULE_UPGRADE_SHELL).c_str());
    Basefun::LinuxSystem((string("rm -rf ") + MODULE_UPGRADE_PATH).c_str());

    char *FileBuf = nullptr;
    int FileLen = 0;
    unsigned short FileCRC = 0;
    int FileCheckSum = 0;
    string PacketName;

    std::unique_ptr<unsigned char[]>pData(new (std::nothrow) unsigned char[UPGRADE_BUFF_SIZE]);
    unsigned int FwLen = DataLen - MAX_NAME_SIZE;

    do
    {
        if (0 == FwLen)
        {
            //升级前从已保存的升级包读取数据
            PacketName = string((char *)Data);
            int Len = Basefun::ReadFile(PacketName.c_str(), pData.get(), UPGRADE_BUFF_SIZE);
            if ((2 * sizeof(int)) >= Len)
            {
                Ret = WT_UPGRADE_MODULE_CHECK_ERROE;
                WTLog::Instance().LOGERR(Ret, "Upgrade package not exit");
                break;
            }

            FileBuf = (char *)pData.get() + 2 * sizeof(int);
            FileLen = Len - 2 * sizeof(int);
            FileCRC = *(unsigned short *)pData.get();
            FileCheckSum = *(int *)(pData.get() + sizeof(int));
        }
        else
        {
            //升级前先把当前升级包保存到临时文件中
            FileBuf = (char *)Data + MAX_NAME_SIZE + 2 * sizeof(int);
            FileLen = DataLen - MAX_NAME_SIZE - 2 * sizeof(int);
            FileCRC = *(unsigned short *)((char *)Data + 256);
            FileCheckSum = *((int *)((char *)Data + 256) + 1);
        }

        unsigned short CRC = (unsigned int)(Secure::GetCRC16((unsigned char *)FileBuf, FileLen)); //16位CRC校验码计算
        int CheckSum = Secure::GetChecksum((unsigned char *)FileBuf, FileLen);                    //校验和计算
        if (FileCRC != CRC)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Module Update Package Crc error" << endl;
            Ret = WT_UPGRADE_MODULE_CHECK_ERROE;
            break;
        }
        else if (FileCheckSum != CheckSum)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Module Update Package CheckSum error" << endl;
            Ret = WT_UPGRADE_MODULE_CHECK_ERROE;
            break;
        }

        //判断是否是gzip文件
        unsigned char *pFlag = new (FileBuf) unsigned char;
        if (pFlag[0] == 0x1F && pFlag[1] == 0x8B && pFlag[2] && 0x08)
        {
            // gzip file
        }
        else if (pFlag[0] == (unsigned char)(~0x1F) && pFlag[1] == (unsigned char)(~0x8B) && pFlag[2] == (unsigned char)(~0x08))
        {
            // gzip file 按照比特位反转格式
            reverse_buff(FileBuf, FileLen);
        }
        else if (pFlag[0] == (unsigned char)(0x1F^0xB7) && pFlag[1] == (unsigned char)(0x8B^0xB7) && pFlag[2] == (unsigned char)(0x08^0xB7))
        {
            // gzip file 按照比特位异或0xB7
            mixer_buff(FileBuf, FileLen);
        }
        else
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Module Update Package not gzip file" << endl;
            Ret = WT_UPGRADE_MODULE_CHECK_ERROE;
            break;
        }

        //把tar.gz文件先保存下来
        string TarPacketName = string(MODULE_UPGRADE_FILE);
        Basefun::WriteFile(TarPacketName.c_str(), (unsigned char *)FileBuf, FileLen);

        Basefun::LinuxSystem((string("tar -xzvf ") + TarPacketName + " -C /tmp/").c_str()); /* 解压升级包 */

        //判断文件是否包含升级文件夹
        if (access(MODULE_UPGRADE_PATH, F_OK) != 0)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Module Update, no exist ModuleUpgrade.tar.gz" << endl;
            Ret = WT_FILE_OPEN_ERROR;
            break;
        }
        Basefun::LinuxSystem("pkill WT-Server*");
        Launcher::Instance().ShutdownServers();
        DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_ON);            //升级时亮全部led
        DevLib::Instance().DevLibRelease();

        string CurDir = WTConf::GetDir(); //获取程序的当前目录
        if (access(MODULE_UPGRADE_SHELL, F_OK) == 0)
        {
            string UpgradeCmd = string(MODULE_UPGRADE_SHELL) + " " + MODULE_UPGRADE_PATH + " " + CurDir;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << UpgradeCmd << endl;
            Basefun::LinuxSystem(UpgradeCmd.c_str());
        }
        else
        {
            DIR *Dir = NULL; /* 路径句柄 */
            struct dirent *File = NULL;
            Dir = opendir(MODULE_UPGRADE_PATH);
            //遍历FilePath目录下的所有文件
            while ((File = readdir(Dir)) != NULL)
            {
                // 排除当前目录和上级目录
                if (!strncmp(File->d_name, ".", 1))
                {
                    continue;
                }
                else if(File->d_type == 4)
                {
                    string UpgradeCmd = string("cp -rf ") + MODULE_UPGRADE_PATH + "/" + File->d_name + " " + CurDir;
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << UpgradeCmd << endl;
                    Basefun::LinuxSystem(UpgradeCmd.c_str());
                }
                else if (strstr(File->d_name, "WT-Manager") == NULL)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (string(MODULE_UPGRADE_PATH) + "/" + File->d_name).c_str() << endl;
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (CurDir + "/" + File->d_name).c_str() << endl;
                    //将除WT-Manager外的文件拷贝到bin目录
                    Ret |= rename((string(MODULE_UPGRADE_PATH) + "/" + File->d_name).c_str(), (CurDir + "/" + File->d_name).c_str());
                }
            }
            closedir(Dir);
            if(Ret != WT_OK)
            {
                Ret = WT_ARG_ERROR;
            }
        }
    } while (0);

    if (Ret == WT_OK)
    {
        WTLog::Instance().LOGSYSOPT("Module Update successfully.");
    }
    else
    {
        WTLog::Instance().LOGSYSOPT("Module Update fail.");
    }

    if (Ret != WT_OK)
    {
        WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_IDLE_STATE);
    }
    return Ret;
}


int Upgrade::UpdatebyLocalFile(string FilePath)
{
    int Ret = 0;
    int Len = 0;
    std::unique_ptr<unsigned char[]>Buf(new(std::nothrow) unsigned char[UPGRADE_BUFF_SIZE]);

    DIR *Dir = NULL;    // 路径句柄
    struct dirent *File = NULL;

    if (NULL == Buf)
    {
        WTLog::Instance().LOGERR(WT_UPGRADE_NO_MEM, "Memory apply error");
        return WT_UPGRADE_NO_MEM;
    }

    //获取对应目录下的升级包
    do
    {
        Dir = opendir(FilePath.c_str());
        if(Dir == NULL)
        {
            Ret = WT_UPGRADE_NO_PACKAGE;
        }
        else
        {
            //遍历FilePath目录下的所有文件
            while ((File = readdir(Dir)) != NULL)
            {
                // 排除当前目录和上级目录
                if (!strncmp(File->d_name, ".", 1))
                {
                    continue;
                }
                else if (strstr(File->d_name, ".upg") != NULL)
                {
                    WriteUpgradeStep(UPGRADE_STEP_WR);
                    string FileName = FilePath + File->d_name;
                    Len = Basefun::ReadFile(FileName.c_str(), Buf.get(), UPGRADE_BUFF_SIZE);

                    if (0 >= Len)
                    {
                        Ret = WT_UPGRADE_NO_PACKAGE;
                        WTLog::Instance().LOGERR(Ret, "Upgrade package not exit");
                    }
                    break;
                }
            }
            closedir(Dir);
            if(Len > 0) //获取文件正确
            {
                if (Ret == 0)
                {
                    //分析截取升级文件
                    WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_BUSY_STATE);
                    if((Ret = UpgradePackageAnalyse(Buf.get(), Len)) == WT_OK)
                    {
                        //升级操作
                        Ret = UpgradeHandle();
                    }
                }
            }
            else
            {
                Ret = WT_UPGRADE_NO_PACKAGE;
                WTLog::Instance().LOGERR(Ret, "Upgrade package not exit");
            }
        }
    }
    while (0);

    if(Ret != WT_OK)
    {
        WTDeviceInfo::Instance().SetDevUpgradeState(UPGRADE_IDLE_STATE);
    }
    return Ret;
}

int Upgrade::DeviceRestoreHandler(void)
{
    int Ret = WT_OK;
    string CurDir = WTConf::GetDir() + "/";

    Ret = UpdatebyLocalFile(CurDir);
    if(Ret == WT_OK)
    {
        //恢复出厂固件时删除仪器中的线衰文件
        string PathLossFileName = WTConf::GetDir()+"/pathloss/PathLossFile";
        remove(PathLossFileName.c_str());

        //删除子网口配置文件
        string File = WTConf::GetDir().append("/eth/subnet.conf");
        remove(File.c_str());
    }
    if(Ret == WT_OK)
    {
        WTLog::Instance().LOGSYSOPT("FW restore to factory version successfully.");
    }
    else
    {
        WTLog::Instance().LOGSYSOPT("FW restore to factory version fail.");
    }
    return Ret;
}

int Upgrade::DeviceFirmwareRollbackHandler(void)
{
    int Ret = WT_OK;
    string CurDir = WTConf::GetDir() + "/prev/";
    Ret = UpdatebyLocalFile(CurDir);
    if(Ret == WT_OK)
    {
        WTLog::Instance().LOGSYSOPT("FW rollback to last version successfully.");
    }
    else
    {
        WTLog::Instance().LOGSYSOPT("FW rollback to last version fail.");
    }
    return Ret;
}

int Upgrade::GetFwVersionFormPackage(const std::string PackageName, std::string &VersionInfo)
{
    int Ret = WT_OK;
    unique_ptr<unsigned char[]> PackFileBuf = nullptr;
    PackFileBuf.reset(new (std::nothrow) unsigned char[UPGRADE_BUFF_SIZE]);
    if (PackFileBuf == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed .");
        return WT_ALLOC_FAILED;
    }
    else
    {
        int Len = 0;
        Len = Basefun::ReadFile(PackageName.c_str(), (unsigned char *)PackFileBuf.get(), UPGRADE_BUFF_SIZE);
        unsigned char *Buf = PackFileBuf.get();
        std::vector<UpgradeFileInfo> FileList;

        Ret = DecryptPackage(Buf, Len, FileList, UPG_FILE_TAR);
        if (FileList.size() > UPG_FILE_TAR && FileList[UPG_FILE_TAR].FileLen > 0) //解压指定升级包中的固件版本信息
        {
            //解压出固件版本信息文件
            string TarFileName = "/tmp/tmp.tar.gz";
            Basefun::WriteFile(TarFileName.c_str(), (unsigned char *)(FileList[UPG_FILE_TAR].FileData.get()), FileList[UPG_FILE_TAR].FileLen);
            char Str[256] = {0};
            sprintf(Str, "tar -zxvpf /tmp/tmp.tar.gz -C %s bin/fwversion.txt", "/tmp/");
            Basefun::LinuxSystem(Str);

            if (access("/tmp/bin/fwversion.txt", F_OK) != 0)
            {
                Ret = WT_OPEN_FILE_FAILED;
                WTLog::Instance().LOGERR(Ret, "do not have version info!");
            }
            else
            {
                WTConf Conf("/tmp/bin/fwversion.txt");
                Conf.GetItemVal("FwVersion", VersionInfo);
            }
            remove("/tmp/bin/fwversion.txt");
        }
        else
        {
            Ret = WT_OPEN_FILE_FAILED;
            WTLog::Instance().LOGERR(Ret, "do not have version info!");
        }
        return Ret;
    }
}

int Upgrade::DecryptPackage(unsigned char *Buf, const int Len, std::vector<UpgradeFileInfo> &FileList, int FileIndex)
{
    int Ret = WT_OK;

    if (Len < 1040)
    {
        Ret = WT_UPGRADE_NO_PACKAGE;
        WTLog::Instance().LOGERR(Ret, "Upgrade package not exit");
        return Ret;
    }

    uint8_t Random[17] = {0};
    char *Config = NULL;
    char *FileBuf = NULL;
    unsigned int CRC = 0;
    unsigned int CheckSum = 0;
    int TotalLen = 1040;

    do
    {
        // step 1 获取随机数
        memcpy(Random, Buf, 16);

        // step 2 获取并解密文件信息列表
        Config = (char *)&Buf[16];
        Secure::Decrypt((int *)Config, 1024 / 4, (u32 *)Random); //加解密的接口调用
        FileBuf = (char *)&Buf[1040];
        int *UpgradeDevType = new (&Config[1000]) int;
        if (*UpgradeDevType != WTDeviceInfo::Instance().GetDevType())
        {
            Ret = WT_UPGRADE_VERSION_ERROE;
            char pBuf[1024];
            snprintf(pBuf, 1024, "Upgrade DevType Error, Cur Dev Type=%d, Upgrade package Type=%d\n", WTDeviceInfo::Instance().GetDevType(), *UpgradeDevType);
            WTLog::Instance().LOGERR(Ret, pBuf);
            break;
        }

        // step 3 获取并解密文件内容
        FileList.clear();
        for (int i = 0; i < MAX_UPDATE_FILES_NUM; i++)
        {
            UpgradeFileInfo FileInfo;
            int RealFileBufLen = 0;
            do
            {
                int FileLen = 0;
                FileLen = *(int *)&Config[i * 100];
                if (0 == FileLen)
                {
                    break;
                }
                if (FileLen > Len - TotalLen)
                {
                    Ret = WT_UPGRADE_FILE_SIZE_ERR;
                    WTLog::Instance().LOGERR(Ret, "Upgrade file size error");
                    break;
                }
                RealFileBufLen = ((FileLen - 1) / 4 + 1) * 4; // 因为buf是以4字节对齐，所以需要重新计算实际占用buf大小

                Secure::Decrypt((int *)FileBuf, RealFileBufLen / 4, (uint32_t *)Random);   //解密
                CRC = (unsigned int)(Secure::GetCRC16((unsigned char *)FileBuf, FileLen)); //16位CRC校验码计算
                CheckSum = Secure::GetChecksum((unsigned char *)FileBuf, FileLen);         //校验和计算

                if (CRC != *(int *)&Config[i * 100 + 4])
                {
                    Ret = WT_UPGRADE_CRC_ERR;
                    WTLog::Instance().LOGERR(Ret, "Upgrade file CRC error");
                    break;
                }
                else if (CheckSum != *(int *)&Config[i * 100 + 8])
                {
                    Ret = WT_UPGRADE_CHECKSUM_ERR;
                    WTLog::Instance().LOGERR(Ret, "Upgrade file checksum error");
                    break;
                }
                else
                {
                    // 记录升级文件信息
                    if (FileLen > 0)
                    {
                        FileInfo.FileData.reset(new (std::nothrow) char[FileLen]);
                        if (FileInfo.FileData == nullptr)
                        {
                            Ret = WT_ALLOC_FAILED;
                            WTLog::Instance().LOGERR(Ret, "Upgrade file alloc buf error");
                            break;
                        }
                        memcpy((char *)(FileInfo.FileData.get()), FileBuf, FileLen);
                        FileInfo.FileLen = RealFileBufLen;
                        strcpy(FileInfo.FilePath, &Config[i * 100 + 28]);
                    }
                }
            } while (0);

            if (Ret != WT_OK)
                break;
            FileBuf += RealFileBufLen;
            TotalLen += RealFileBufLen;
            FileList.push_back(move(FileInfo));

            //仅解获取压特定文件块
            if (FileIndex != UPG_FILE_INVALID && i == FileIndex)
            {
                break;
            }
        }
    } while (0);

    return Ret;
}

#define DEB_UPGRADE_SHELL "Installpkg.sh"
void Upgrade::UpgradeDebSetup(const char *DirName)
{
    string ShellPath = string(DirName) + DEB_UPGRADE_SHELL;
    if (access(ShellPath.c_str(), F_OK) == 0)
    {
        string InstallCmd = ShellPath + " " + DirName;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << InstallCmd << endl;
        Basefun::LinuxSystem(InstallCmd.c_str());
        //删除目录避免回退版本时重复升级
        Basefun::LinuxSystem((string("rm -rf ") + DirName).c_str());
    }
}

//根据硬件版本替换校准库，主要为了应对旧版本VA仪器升级到合并后的版本
int Upgrade::CheckAndReplaceLibCal(int ReBootEnable)
{
    int Ret = WT_OK;
    bool NeedReboot = false;
    int TesterHwType = HW_WT428;
    Ret = DevLib::Instance().GetTesterHwType(TesterHwType);
    if (TesterHwType == HW_WT448)
    {
        string CalLibVaPath = WTConf::GetDir() + "/libwt-calibration_va.so";
        int BoradVer = JudgeBoardVersion(DEV_TYPE_BACK, 0);
        switch (BoradVer)
        {
        case BOARD_VERSION_VER_A:
            if ((access(CalLibVaPath.c_str(), F_OK) == 0))
            {
                string CalLibPath = WTConf::GetDir() + "/libwt-calibration.so";
                Basefun::LinuxSystem(("mv " + CalLibVaPath + " " + CalLibPath).c_str());
                NeedReboot = true;
                WTLog::Instance().WriteLog(LOG_DEBUG, "Use old calibration lib\n");
            }
            else
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "cant find va calibration lib\n");
            }
            break;
        default:
            Basefun::LinuxSystem(("rm -f " + CalLibVaPath).c_str());
            WTLog::Instance().WriteLog(LOG_DEBUG, "Use new calibration lib\n");
            break;
        }
    }

    if (ReBootEnable && NeedReboot)
    {
        Basefun::LinuxSystem("reboot");
    }

    return Ret;
}

#define CPLD_UPGRAND_RETRY_CNT 1
void Upgrade::UpdateBaseMultiThread(int ModId)
{
    int IsNeedUpgrade;
    int TryCnt = 0;
    std::string Path;
    GetFPGAPath(DEV_TYPE_BUSI, ModId, Path, IsNeedUpgrade);
    if (IsNeedUpgrade)
    {
        for (int TryCnt = 0; TryCnt < CPLD_UPGRAND_RETRY_CNT; TryCnt++)
        {
            m_BaseBurnRet[ModId] = DevLib::Instance().BaseFpgaUpgrade(ModId, DEV_TYPE_BUSI, Path);
            if (m_BaseBurnRet[ModId] == WT_OK)
            {
                break;
            }
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "Upgrade BaseBoard%d m_BaseBurnRet=%#x, Try %d times\n", ModId, m_BaseBurnRet[ModId], TryCnt);
    }
    else
    {
        m_BaseBurnRet[ModId] = WT_OK;
        WTLog::Instance().WriteLog(LOG_DEBUG, "Not need upgrade this board, Exit...\n");
    }
}

int Upgrade::UpdateFPGA(int Type)
{
    int Ret = WT_OK;

    if (Type == DEV_TYPE_BACK)
    {
        int TryCnt = 0;
        std::string Path;
        int IsNeedUpgrade;

        GetFPGAPath(Type, 0, Path, IsNeedUpgrade);
        if (IsNeedUpgrade)
        {
            for (TryCnt = 0; TryCnt < CPLD_UPGRAND_RETRY_CNT; TryCnt++)
            {
                UpgradeFpga FpgaObj(Path);
                Ret = FpgaObj.BrunFpgaData(UPGRADE_FPGA_BACKPLANE);
                if (Ret == WT_OK)
                {
                    break;
                }
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "Upgrade Back BackBurnRet=%#x, Try %d times\n", Ret, TryCnt);
        }
        else
        {
            Ret = WT_OK;
            WTLog::Instance().WriteLog(LOG_DEBUG, "Not need upgrade this board, Exit...\n");
        }

        if (Ret != WT_OK)
        {
            Ret = WT_UPGRADE_BACK_FPGA_FAIL;
        }
    }
    else
    {
        int BusiNum = DevLib::Instance().GetUnitBoardModNum(DEV_TYPE_BUSI);
#if UPGRADE_BASE_MULIT_THREAD
        std::thread Threads[BusiNum];
        for (int i = 0; i < BusiNum; i++)
        {
            Threads[i] = std::thread(&Upgrade::UpdateBaseMultiThread, this, i);
        }

        for (int i = 0; i < BusiNum; i++)
        {
            Threads[i].join();
            if (m_BaseBurnRet[i] != WT_OK && Ret == WT_OK)
            {
                Ret = WT_UPGRADE_VSA_FPGA_FAIL;
            }
        }
#else
        for (int i = 0; i < BusiNum; i++)
        {
            UpdateBaseMultiThread(i);
        }

        for (int i = 0; i < BusiNum; i++)
        {
            if (m_BaseBurnRet[i] != WT_OK && Ret == WT_OK)
            {
                Ret = WT_UPGRADE_VSA_FPGA_FAIL;
            }
        }
#endif
    }
    return Ret;
}

//根据FPGA版本号区别硬件类型
int Upgrade::JudgeBoardVersion(int Type, int ModId)
{
    DeviceHwInfo HwInfo;
    WTDeviceInfo::Instance().ReadBoardHwVersion(HwInfo);
    const DeviceInfo DevInfo = WTDeviceInfo::Instance().GetDeviceDetailedInfo();

    string BPHWVersion;
    if (Type == DEV_TYPE_BACK)
    {
        if (WTDeviceInfo::CheckHwVersionValid(DevInfo.BPInfo.BPHWVersion) == WT_OK)
        {
            BPHWVersion = DevInfo.BPInfo.BPHWVersion;
        }
        else if (HwInfo.BackInfoIsOk == true)
        {
            BPHWVersion = HwInfo.BackPlaneHwVersion;
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevInfo.BPInfo.BPHWVersion=%s, HwInfo.BackPlaneHwVersion=%s, BPHWVersion=%s\n",
               DevInfo.BPInfo.BPHWVersion, HwInfo.BackPlaneHwVersion.c_str(), BPHWVersion.c_str());
    }
    else
    {
        if (WTDeviceInfo::CheckHwVersionValid(DevInfo.BusiBoardInfo[ModId].BBHWVersion) == WT_OK)
        {
            BPHWVersion = DevInfo.BusiBoardInfo[ModId].BBHWVersion;
        }
        else if (HwInfo.BusiInfoIsOk == true)
        {
            BPHWVersion = HwInfo.BusiHwVersion;
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevInfo.BusiBoardInfo[ModId].BBHWVersion=%s, HwInfo.BusiHwVersion=%s, BPHWVersion=%s\n",
               DevInfo.BusiBoardInfo[ModId].BBHWVersion, HwInfo.BusiHwVersion.c_str(), BPHWVersion.c_str());
    }

    unsigned int Data[4];
    sscanf(BPHWVersion.c_str(), "%x.%x.%x.%x", &Data[3], &Data[2], &Data[1], &Data[0]);

    int Version = BOARD_VERSION_VER_A + Data[0];
    WTLog::Instance().WriteLog(LOG_DEBUG, "##Get Board version =%d\n", Version);

    return Version;
}

int Upgrade::GetFPGAPath(int Type, int ModId, std::string &Path, int &IsNeedUpgrade)
{
    IsNeedUpgrade = false;
    Path.clear();

    string DevTypeStr;
    int TesterHwType = HW_WT428;
    int SwbHwVersion = 0;
    DevLib::Instance().GetTesterHwType(TesterHwType);
    DevLib::Instance().GetSwbHwVersion(SwbHwVersion);
    switch (TesterHwType)
    {
    case HW_WT418:
        DevTypeStr = "WT418";
        break;
    case HW_WT428:
        DevTypeStr = "WT428";
        break;
    case HW_WT448:
    default:
        DevTypeStr = "WT448";
        break;
    }

    const DeviceInfo DevInfo = WTDeviceInfo::Instance().GetDeviceDetailedInfo();
    int BoradVer = JudgeBoardVersion(Type, ModId);
    if ((TesterHwType == HW_WT428) && (SwbHwVersion >= VERSION_D))
    {
        BoradVer = UPGRADE_FPGA_VC_VD_VERSION;    //VC和VD的仪器在硬件版本号的基础上需要根据开关板版本号进行判别
    }
    string BackItem;
    string BaseItem;
    string VersionStr;
    string DevVerStr;
    switch (BoradVer)
    {
    case BOARD_VERSION_VER_A:
    case BOARD_VERSION_VER_B:
    case BOARD_VERSION_VER_C:
    case BOARD_VERSION_VER_D:
    case BOARD_VERSION_VER_E:
    case BOARD_VERSION_VER_F:
    case BOARD_VERSION_VER_G:
    case BOARD_VERSION_VER_H:
        BackItem = DevTypeStr + "_BACK_V" + (char)('A' + BoradVer - BOARD_VERSION_VER_A);
        BaseItem = DevTypeStr + "_BASE_V" + (char)('A' + BoradVer - BOARD_VERSION_VER_A);
        break;
    default:
        BackItem = DevTypeStr + "_BACK_UNKNOW";
        BaseItem = DevTypeStr + "_BASE_UNKNOW";
        break;
    }

    WTConf FpgaVersion(WTConf::GetDir() + "/fpga/fpgaversion.conf");
    int ForceUpgrade = false;
    FpgaVersion.GetItemVal("WT448_FORCE_UPGRADE", ForceUpgrade);

    if (Type == DEV_TYPE_BACK)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << BackItem.c_str() << endl;
        if (WTDeviceInfo::CheckFpgaVersionValid(DevInfo.BPInfo.FPGAVersion) == WT_OK)
        {
            DevVerStr = string(DevInfo.BPInfo.FPGAVersion);
        }
        else
        {
            DevVerStr = "0.0.0.0";
        }

        if (FpgaVersion.GetItemVal(BackItem, VersionStr) == WT_OK)
        {
            if (ForceUpgrade || VersionStr != DevVerStr)
            {
                Path = WTConf::GetDir() + "/fpga/back/" + VersionStr + BACK_FPGA_FILE;
                IsNeedUpgrade = true;
            }
        }
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << BaseItem.c_str() << endl;
        if (WTDeviceInfo::CheckFpgaVersionValid(DevInfo.BusiBoardInfo[ModId].FPGAVersion) == WT_OK)
        {
            DevVerStr = string(DevInfo.BusiBoardInfo[ModId].FPGAVersion);
        }
        else
        {
            DevVerStr = "0.0.0.0";
        }

        if (FpgaVersion.GetItemVal(BaseItem, VersionStr) == WT_OK)
        {
            if (ForceUpgrade || VersionStr != DevVerStr)
            {
                Path = WTConf::GetDir() + "/fpga/base/" + VersionStr + BASE_FPGA_FILE;
                IsNeedUpgrade = true;
            }
        }
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "GetFPGAPath Type%d: Dev version = %s, Pkg version = %s, Path = %s, ForceUpgrade=%d, IsNeedUpgrade = %d\n",
           Type, DevVerStr.c_str(), VersionStr.c_str(), Path.c_str(), ForceUpgrade, IsNeedUpgrade);
    return WT_OK;
}
