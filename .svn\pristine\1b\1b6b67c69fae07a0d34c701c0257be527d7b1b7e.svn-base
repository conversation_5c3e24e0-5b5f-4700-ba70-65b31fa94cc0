#include <linux/kernel.h>
#include <linux/uaccess.h>
#include <linux/delay.h>
#include <linux/time.h>

#include "wtdefine.h"
#include "hwlib.h"
#include "wtbackdefine.h"
#include "wtbusidefine.h"
#include "wtbacklib.h"
#include "wtswitch_va.h"
#include "wtswitch_vb.h"
#include "wtswitch_428va.h"
#include "wtswitch_428vb.h"
#include "wtswitch_vd.h"
#include "wtswitch_418va.h"
#include "../general/wterror.h"
#include "../general/devlib/ioctlcmd.h"

#include "wtswitch.h"

// 开关板真值控制状态数据
static int WTSwitchSelect[SWTICH_PART_MAX][SWITCH_UNIT_MAX][BUSI_UB_TYPE_COUNT][SWITCH_SELECT_MAX] = {{{{0}}}}; //存储当前PORT与PORTSTATE(两块开关板，两个开关单元，两个部分链路，三条信息)
static unsigned int g_SwitchValueInit[SWITCH_SW_CTRL_REG_MAX] = {0};                                            //开关板初始状态值
unsigned int g_SwitchValue[SWITCH_SW_CTRL_REG_MAX] = {0};                                                       //用于开关真值计算
unsigned int g_SwitchBitMap[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX][SWITCH_BIT_MAP_MAX] = {{0}};                      //开关枚举与寄存器BIT的映射表
unsigned int g_SwitchShiftReg[SWTICH_PART_MAX][SWITCH_SW_CTRL_REG_MAX] = {{0}};                                 //开关板寄存器当前值备份
unsigned int g_Switchlistmode[BUSI_UB_TYPE_COUNT] = {0,0};
static const unsigned int SwitchShiftRegAddr_VA[SWTICH_PART_MAX][SWITCH_SHIFT_REG_GROUP_COUNT][SW_SHIFT_REG_MEMBER_COUNT] =
    {
        /* 参考 12.1寄存器配置汇总 */
        /* A部分 */
        {
            {0x0140, 0x0144, 0x0148, 0x014C, 0x0148}, /* shift_0 */
            {0x0154, 0x0158, 0x015C, 0x0160, 0x015C},
            {0x0168, 0x016C, 0x0170, 0x0174, 0x0170},
            {0x017C, 0x0180, 0x0184, 0x0188, 0x0184},
            {0x0190, 0x0194, 0x0198, 0x019C, 0x0198},
            {0x01A4, 0x01A8, 0x01AC, 0x01B0, 0x01AC}, /* shift_5 */
        },

        /* B部分 */
        {
            {0x01B8, 0x01BC, 0x01C0, 0x01C4, 0x01C0},
            {0x01CC, 0x01D0, 0x01D4, 0x01D8, 0x01D4},
            {0x01E0, 0x01E4, 0x01E8, 0x01EC, 0x01E8},
            {0x01F4, 0x01F8, 0x01FC, 0x0200, 0x01FC},
            {0x0208, 0x020C, 0x0210, 0x0214, 0x0210},
            {0x021C, 0x0220, 0x0224, 0x0228, 0x0224},
        }};

// 开关板移位寄存器发送数据字长
static const unsigned int SwitchShiftRegLen_VA[SWITCH_SHIFT_REG_GROUP_COUNT] = {
    32,
    8,
    8,
    8,
    8,
    24};

// 开关板移位寄存器控制开关BIT掩码
static const unsigned int SW_SHIFT_REG_MASK_VA[SWITCH_SW_CTRL_REG_MAX] = {
    0x3FFFFFFF,
    0xFF,
    0xFF,
    0xFF,
    0xFF,
    0x0, // SHIFT5用于控制ATT, 不控开关
    0xF, // PE42553寄存器
    0xF, // PA控制寄存器
};

// 开关板移位寄存器地址定义
static const unsigned int SwitchShiftRegAddr_VB[SWTICH_PART_MAX][SWITCH_SHIFT_REG_GROUP_COUNT][SW_SHIFT_REG_MEMBER_COUNT] =
    {
        /* 参考 12.1寄存器配置汇总 */
        /* A部分 */
        {
            {0x0140, 0x0144, 0x0148, 0x014C, 0x0148}, /* shift_0 */
            {0x0154, 0x0158, 0x015C, 0x0160, 0x015C},
            {0x0168, 0x016C, 0x0170, 0x0174, 0x0170},
            {0x0000, 0x0000, 0x0000, 0x0000, 0x0000}, /* no use */
            {0x0000, 0x0000, 0x0000, 0x0000, 0x0000},
            {0x0000, 0x0000, 0x0000, 0x0000, 0x0000}, /* shift_5 */
        },

        /* B部分 */
        {
            {0x01B8, 0x01BC, 0x01C0, 0x01C4, 0x01C0},
            {0x01CC, 0x01D0, 0x01D4, 0x01D8, 0x01D4},
            {0x01E0, 0x01E4, 0x01E8, 0x01EC, 0x01E8},
            {0x0000, 0x0000, 0x0000, 0x0000, 0x0000}, /* no use */
            {0x0000, 0x0000, 0x0000, 0x0000, 0x0000},
            {0x0000, 0x0000, 0x0000, 0x0000, 0x0000},
        }};

// 开关板移位寄存器发送数据字长
static const unsigned int SwitchShiftRegLen_VB[SWITCH_SHIFT_REG_GROUP_COUNT] = {
    24,
    32,
    32,
    0,
    0,
    0};

// 开关板移位寄存器控制开关BIT掩码
static const unsigned int SW_SHIFT_REG_MASK_VB[SWITCH_SW_CTRL_REG_MAX] = {
    0x00FFFFFF,
    0xFF81FF01,
    0x01FF81FF,
    0x0,
    0x0,
    0x0,
    0xF, // PE42553寄存器
    0x0, // PA控制寄存器
    0xF, //PAC
};

static const unsigned int SwitchAttRegBit_VB[SWITCH_PART_PORT_MAX][2] = {
    /* 控制ATT的寄存器变号, 开始管教bit */
    {SWITCH_SHIFT_1_CR, 1},
    {SWITCH_SHIFT_1_CR, 17},
    {SWITCH_SHIFT_2_CR, 25},
    {SWITCH_SHIFT_2_CR, 9},
};

// 开关板移位寄存器地址定义
static const unsigned int SwitchShiftRegAddr_428VA[SWTICH_PART_MAX][SWITCH_SHIFT_REG_GROUP_COUNT][SW_SHIFT_REG_MEMBER_COUNT] =
    {
        /* 参考 12.1寄存器配置汇总 */
        /* A部分 */
        {
            {0x0140, 0x0144, 0x0148, 0x014C, 0x0148}, /* shift_0 */
            {0x0154, 0x0158, 0x015C, 0x0160, 0x015C},
            {0x0168, 0x016C, 0x0170, 0x0174, 0x0170},
            {0x01B8, 0x01BC, 0x01C0, 0x01C4, 0x01C0},
            {0x01CC, 0x01D0, 0x01D4, 0x01D8, 0x01D4},
            {0x01E0, 0x01E4, 0x01E8, 0x01EC, 0x01E8},
        },

        /* B部分 WT428不分A B部分处理，为兼容，B部分于A部分重复*/
        {
            {0x0140, 0x0144, 0x0148, 0x014C, 0x0148}, /* shift_0 */
            {0x0154, 0x0158, 0x015C, 0x0160, 0x015C},
            {0x0168, 0x016C, 0x0170, 0x0174, 0x0170},
            {0x01B8, 0x01BC, 0x01C0, 0x01C4, 0x01C0},
            {0x01CC, 0x01D0, 0x01D4, 0x01D8, 0x01D4},
            {0x01E0, 0x01E4, 0x01E8, 0x01EC, 0x01E8},
        }};

// 开关板移位寄存器发送数据字长
static const unsigned int SwitchShiftRegLen_428VA[SWITCH_SHIFT_REG_GROUP_COUNT] = {
    32,
    32,
    24,
    32,
    32,
    24};

// 开关板移位寄存器控制开关BIT掩码
static const unsigned int SW_SHIFT_REG_MASK_428VA[SWITCH_SW_CTRL_REG_MAX] = {
    0xC0FFFFFF,
    0xFFFFC0FF,
    0xC0FFC0,
    0xFFFFC0FF,
    0xFFFFC0FF,
    0xC0FFC0,
    0xF, // PE42553寄存器
    0xF, // PA控制寄存器
};

static const unsigned int SwitchAttRegBit_428VA[SWTICH_PART_MAX][SWITCH_PART_PORT_MAX][2] = {
    /* 控制ATT的寄存器变号, 开始管教bit */
    {
        {SWITCH_SHIFT_3_CR, 8},
        {SWITCH_SHIFT_5_CR, 16},
        {SWITCH_SHIFT_5_CR, 0},
        {SWITCH_SHIFT_4_CR, 8},
    },
    {
        {SWITCH_SHIFT_2_CR, 16},
        {SWITCH_SHIFT_2_CR, 0},
        {SWITCH_SHIFT_1_CR, 8},
        {SWITCH_SHIFT_0_CR, 24},
    },
};

// 开关板移位寄存器地址定义
static const unsigned int SwitchShiftRegAddr_418VA[SWTICH_PART_MAX][SWITCH_SHIFT_REG_GROUP_COUNT][SW_SHIFT_REG_MEMBER_COUNT] =
{
    /* 参考 12.1寄存器配置汇总 */
    /* A部分 */
    {
        {0x0014<<2, 0x0016<<2, 0x0018<<2, 0x0019<<2, 0x0018<<2}, /* shift_0 */
        {0x0015<<2, 0x0017<<2, 0x0018<<2, 0x0019<<2, 0x0018<<2},
        {0x001a<<2, 0x001b<<2, 0x001c<<2, 0x001d<<2, 0x001c<<2},
        {0x0020<<2, 0x0021<<2, 0x0022<<2, 0x0023<<2, 0x0022<<2},
        {0x0024<<2, 0x0025<<2, 0x0026<<2, 0x0027<<2, 0x0026<<2},
        {0x0000<<2, 0x0000<<2, 0x0000<<2, 0x0000<<2, 0x0000<<2},
    },
    /*B部分，为兼容，B部分于A部分重复*/
    {
        {0x0014<<2, 0x0016<<2, 0x0018<<2, 0x0019<<2, 0x0018<<2}, /* shift_0 */
        {0x0015<<2, 0x0017<<2, 0x0018<<2, 0x0019<<2, 0x0018<<2},
        {0x001a<<2, 0x001b<<2, 0x001c<<2, 0x001d<<2, 0x001c<<2},
        {0x0020<<2, 0x0021<<2, 0x0022<<2, 0x0023<<2, 0x0022<<2},
        {0x0024<<2, 0x0025<<2, 0x0026<<2, 0x0027<<2, 0x0026<<2},
        {0x0000<<2, 0x0000<<2, 0x0000<<2, 0x0000<<2, 0x0000<<2},
    }
};

static const unsigned int SwitchShiftSPIConfig_418VA[SWITCH_SHIFT_REG_GROUP_COUNT] = {
    0x1828,
    0x1828,
    0x1820,
    0x1820,
    0x1820,
};

// 开关板移位寄存器发送数据字长,WT418的SPIConfig暂与寄存器有效位无关
static const unsigned int SwitchShiftRegLen_418VA[SWITCH_SHIFT_REG_GROUP_COUNT] = {
    32,
    8,
    32,
    32,
    32,
};

// 开关板移位寄存器控制开关BIT掩码
static const unsigned int SW_SHIFT_REG_MASK_418VA[SWITCH_SW_CTRL_REG_MAX] = {
    0xFFC0FFFF,
    0x000000C0,
    0x03FF03FF,
    0x03FF03FF,
    0xFFC0FFC0,
    0xFF,
    0XFF,
};

static const unsigned int SwitchAttRegBit_418VA[SWITCH_PORT_MAX][2] = {
    /* 控制ATT的寄存器编号, 开始管脚bit */
    {SWITCH_SHIFT_1_CR, 0},
    {SWITCH_SHIFT_0_CR, 16},
    {SWITCH_SHIFT_2_CR, 26},
    {SWITCH_SHIFT_2_CR, 10},
    {SWITCH_SHIFT_3_CR, 26},
    {SWITCH_SHIFT_3_CR, 10},
    {SWITCH_SHIFT_4_CR, 0},
    {SWITCH_SHIFT_4_CR, 16},
};

// 兼容VA,VB数据结构
static const unsigned int(*SwitchShiftRegAddr)[SWITCH_SHIFT_REG_GROUP_COUNT][SW_SHIFT_REG_MEMBER_COUNT];
static const unsigned int(*SwitchShiftRegLen);
static const unsigned int(*SW_SHIFT_REG_MASK);

unsigned long long RFSwitchTablebak[3] = {0};

FUNC_SWITCH_SELECT g_pVsg_Select_SW = Vsg_Select_SW_VA;
FUNC_SWITCH_SELECT g_pVsa_Select_SW = Vsa_Select_SW_VA;
FUNC_SWITCH_SELECT g_pVsg_Select_CTL3 = Vsg_Select_CTL3_VA;
/*-----------------射频板--------------------*/
int WT_SetTXPort(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct SwitchRFPortSetType RFPortSetTemp;
    if (copy_from_user(&RFPortSetTemp, arg, DataLength))
    {
        dbg("WT_SetRXPort info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    wt_SetSwitchStatus(pdev->type, RFPortSetTemp.SwitchId, RFPortSetTemp.SubPort, RFPortSetTemp.Mode, RFPortSetTemp.State);
    return WT_OK;
}

int WT_SetRXPort(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct SwitchRFPortSetType RFPortSetTemp;
    if (copy_from_user(&RFPortSetTemp, arg, DataLength))
    {
        dbg("WT_SetRXPort info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    wt_SetSwitchStatus(pdev->type, RFPortSetTemp.SwitchId, RFPortSetTemp.SubPort, RFPortSetTemp.Mode, RFPortSetTemp.State);
    return WT_OK;
}

int WT_GetSwitchValueBak(int DataLength, void *arg, struct dev_unit *pdev)
{
    return 0;
}

int WT_InitSwitch(struct dev_unit *pdev, int SpecSwitchFlag)
{
    int RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;
    int i = 0;

    dbg_print("WT_InitSwitch testertype=%d, swbversion=%d, SpecSwitchFlag=%d!!\n",
           pdev->testertype, pdev->swbversion, SpecSwitchFlag);
    if (pdev->testertype == HW_WT418)
    {
        SwitchShiftRegAddr = SwitchShiftRegAddr_418VA;
        SwitchShiftRegLen = SwitchShiftRegLen_418VA;
        SW_SHIFT_REG_MASK = SW_SHIFT_REG_MASK_418VA;
        g_pVsg_Select_SW = Vsg_Select_SW_418VA;
        g_pVsa_Select_SW = Vsa_Select_SW_418VA;
        g_pVsg_Select_CTL3 = Vsg_Select_CTL3_418VA;

        RegCnt = WT418_SWITCH_SW_CTRL_REG_MAX;
    }
    else if (pdev->testertype >= HW_WT428)
    {
        SwitchShiftRegAddr = SwitchShiftRegAddr_428VA;
        SwitchShiftRegLen = SwitchShiftRegLen_428VA;
        SW_SHIFT_REG_MASK = SW_SHIFT_REG_MASK_428VA;

        if (pdev->swbversion == VERSION_A)
        { // VA为原版非广播
            g_pVsg_Select_SW = Vsg_Select_SW_428VA;
            g_pVsa_Select_SW = Vsa_Select_SW_428VA;
            g_pVsg_Select_CTL3 = Vsg_Select_CTL3_428VA;
        }
        else
        { // VB为降成本版
            g_pVsg_Select_SW = Vsg_Select_SW_428VB;
            g_pVsa_Select_SW = Vsa_Select_SW_428VB;
            g_pVsg_Select_CTL3 = Vsg_Select_CTL3_428VB;
        }

        RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;
    }
    else
    {
        if (pdev->version == VERSION_A)
        {
            SwitchShiftRegAddr = SwitchShiftRegAddr_VA;
            SwitchShiftRegLen = SwitchShiftRegLen_VA;
            SW_SHIFT_REG_MASK = SW_SHIFT_REG_MASK_VA;

            g_pVsg_Select_SW = Vsg_Select_SW_VA;
            g_pVsa_Select_SW = Vsa_Select_SW_VA;
            g_pVsg_Select_CTL3 = Vsg_Select_CTL3_VA;

            RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;
        }
        else
        {
            SwitchShiftRegAddr = SwitchShiftRegAddr_VB;
            SwitchShiftRegLen = SwitchShiftRegLen_VB;
            SW_SHIFT_REG_MASK = SW_SHIFT_REG_MASK_VB;
            if (SpecSwitchFlag || (pdev->swbversion >= VERSION_A && pdev->swbversion <= VERSION_C))
            {
            g_pVsg_Select_SW = Vsg_Select_SW_VB;
            g_pVsa_Select_SW = Vsa_Select_SW_VB;
            g_pVsg_Select_CTL3 = Vsg_Select_CTL3_VB;

            RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT_VB;
        }
            else
            {
                g_pVsg_Select_SW = Vsg_Select_SW_VD;
                g_pVsa_Select_SW = Vsa_Select_SW_VD;
                g_pVsg_Select_CTL3 = Vsg_Select_CTL3_VD;

                RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT_VB;
    }
        }
    }

    for (i = SWITCH_SHIFT_0_CR; i < RegCnt; ++i)
    {
        wt_SetSwitchShiftReg(SWTICH_PART_A, i, 0);
        wt_SetSwitchShiftReg(SWTICH_PART_B, i, 0);
    }
    return 0;
}

// 设置开关板ATT
int wt_SetSwbAttCode_VA(int SwitchId, int SubPort, int Data, struct dev_unit *pdev)
{
    const int SwAttReg = SWITCH_SHIFT_5_CR; // SW ATT控制寄存器在移位寄存器中的编号
    int ShiftBit = 0;
    unsigned RegData = 0;

    if (SubPort < WT_RF_PORT_RF1 || SubPort > WT_RF_PORT_RF4)
    {
        dbg("wt_SetSwbAttCode_VA Set Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    SubPort = SubPort - WT_RF_PORT_RF1;
    ShiftBit = SubPort * 6; // 一个att占用6bit

    wt_GetSwitchShiftReg(SwitchId, SwAttReg, &RegData);
    
    RegData &= ~(0x3f << ShiftBit);
    RegData |= ((Data & 0x3F) << ShiftBit);

    return wt_SetSwitchShiftReg(SwitchId, SwAttReg, RegData);
}

int wt_SetSwbAttCode_VB(int SwitchId, int SubPort, int Data, struct dev_unit *pdev)
{
    int i = 0;
    int temp = 0;

    int ShiftBit = 0;
    // unsigned int SwAttRegRxAddr = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;

    if (SubPort < WT_RF_PORT_RF1 || SubPort > WT_RF_PORT_RF4)
    {
        dbg("wt_SetSwbAttCode_VB Set Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    SubPort = SubPort - WT_RF_PORT_RF1;
    ShiftBit = SwitchAttRegBit_VB[SubPort][1];
    SwAttReg = SwitchAttRegBit_VB[SubPort][0];

    wt_GetSwitchShiftReg(SwitchId, SwAttReg, &RegData);

    //bit高低位取反
    temp = 0;
    for (i = 0; i < 6; i++)
    {
        temp <<= 1;
        temp += Data & 0x1;
        Data >>= 1;
    }

    //衰减方向取反
    Data = 0x3f - temp;
    //printk("wt_SetSwbAttCode_VB Port=%d Data=%d\n", SubPort, Data);

    RegData &= ~(0x3f << ShiftBit);
    RegData |= ((Data & 0x3F) << ShiftBit);

    return wt_SetSwitchShiftReg(SwitchId, SwAttReg, RegData);
}

int wt_SetSwbAttCode_428VA(int SwitchId, int SubPort, int Data, struct dev_unit *pdev)
{
    int ShiftBit = 0;
    // unsigned int SwAttRegRxAddr = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;

    SubPort = SubPort - WT_RF_PORT_RF1;
    if (SubPort < SWITCH_PART_PORT_1 || SubPort >= SWITCH_PART_PORT_MAX ||
        SwitchId < SWTICH_PART_A || SwitchId >= SWTICH_PART_MAX)
    {
        dbg("wt_SetSwbAttCode_428VA Get Switch %d Port %d Att Code. Port invaild\n", SwitchId, SubPort);
        return WT_ARG_ERROR;
    }

    SwAttReg = SwitchAttRegBit_428VA[SwitchId][SubPort][0];
    ShiftBit = SwitchAttRegBit_428VA[SwitchId][SubPort][1];
    wt_GetSwitchShiftReg(SwitchId, SwAttReg, &RegData);

    //衰减方向取反
    Data = 0x3f - Data;
    dbg_print("wt_SetSwbAttCode_428VA Port=%d Data=%d\n", SubPort, Data);

    RegData &= ~(0x3f << ShiftBit);
    RegData |= ((Data & 0x3f) << ShiftBit);

    return wt_SetSwitchShiftReg(SwitchId, SwAttReg, RegData);
}

int wt_SetSwbAttCode_418VA(int SubPort, int Data, struct dev_unit *pdev)
{
    int i = 0;
    int temp = 0;
    int ShiftBit = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;
    int AttReversal[SWITCH_PORT_MAX] = {1,1,0,0,0,0,1,1};
    SubPort = SubPort - WT_RF_PORT_RF1;

    if (SubPort < SWITCH_PORT_1 || SubPort >= SWITCH_PORT_MAX )
    {
        dbg("wt_SetSwbAttCode_418VA Get Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    SwAttReg = SwitchAttRegBit_418VA[SubPort][0];
    ShiftBit = SwitchAttRegBit_418VA[SubPort][1];
    wt_GetSwitchShiftReg(SWTICH_PART_A, SwAttReg, &RegData);

    //bit高低位取反
    if(AttReversal[SubPort])
    {
        for (i = 0; i < 6; i++)
        {
            temp <<= 1;
            temp += Data & 0x1;
            Data >>= 1;
        }
        Data = temp;
    }

    //衰减方向取反
    Data = 0x3f - Data;
    RegData &= ~(0x3f << ShiftBit);
    RegData |= ((Data & 0x3f) << ShiftBit);
    dbg_print("wt_SetSwbAttCode_418VA Port=%d Data=%d\n", SubPort, Data);
    return wt_SetSwitchShiftReg(SWTICH_PART_A, SwAttReg, RegData);
}


int wt_GetSwbAttCode_VA(int SwitchId, int SubPort, int *Data, struct dev_unit *pdev)
{
    int ShiftBit = 0;
    unsigned int RegData = 0;
    const int SwAttReg = SWITCH_SHIFT_5_CR;

    if (SubPort < WT_RF_PORT_RF1 || SubPort > WT_RF_PORT_RF4)
    {
        dbg("wt_GetSwbAttCode_VA Get Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    SubPort = SubPort - WT_RF_PORT_RF1;
    ShiftBit = SubPort * 6; // 一个att占用6bit
    wt_GetSwitchShiftReg(SwitchId, SwAttReg, &RegData);
    *Data = (RegData & (0x3f << ShiftBit)) >> ShiftBit;

    return WT_OK;
}

int wt_GetSwbAttCode_VB(int SwitchId, int SubPort, int *Data, struct dev_unit *pdev)
{
    int ShiftBit = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;
    int temp;
    int i = 0;

    if (SubPort < WT_RF_PORT_RF1 || SubPort > WT_RF_PORT_RF4)
    {
        dbg("wt_GetSwbAttCode_VB Get Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    SubPort = SubPort - WT_RF_PORT_RF1;
    ShiftBit = SwitchAttRegBit_VB[SubPort][1];
    SwAttReg = SwitchAttRegBit_VB[SubPort][0];

    wt_GetSwitchShiftReg(SwitchId, SwAttReg, &RegData);
    *Data = (RegData >> ShiftBit) & 0x3f;

    //bit高低位取反
    temp = 0;
    for (i = 0; i < 6; i++)
    {
        temp <<= 1;
        temp += *Data & 0x1;
        *Data >>= 1;
    }

    //衰减方向取反
    *Data = 0x3f - temp;
    //printk("wt_GetSwbAttCode_VB Port=%d Data=%d\n", SubPort, *Data);
    return WT_OK;
}

int wt_GetSwbAttCode_428VA(int SwitchId, int SubPort, int *Data, struct dev_unit *pdev)
{
    int ShiftBit = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;

    SubPort = SubPort - WT_RF_PORT_RF1;
    if (SubPort < SWITCH_PART_PORT_1 || SubPort >= SWITCH_PART_PORT_MAX ||
        SwitchId < SWTICH_PART_A || SwitchId >= SWTICH_PART_MAX)
    {
        dbg("wt_GetSwbAttCode_428VA Get Switch %d Port %d Att Code. Port invaild\n", SwitchId, SubPort);
        return WT_ARG_ERROR;
    }

    ShiftBit = SwitchAttRegBit_428VA[SwitchId][SubPort][1];
    SwAttReg = SwitchAttRegBit_428VA[SwitchId][SubPort][0];

    wt_GetSwitchShiftReg(SwitchId, SwAttReg, &RegData);
    *Data = (RegData >> ShiftBit) & 0x3f;

    //衰减方向取反
    *Data = 0x3f - *Data;
    dbg_print("wt_GetSwbAttCode_428VA Port=%d Data=%d\n", SubPort, *Data);
    return WT_OK;
}

int wt_GetSwbAttCode_418VA(int SubPort, int *Data, struct dev_unit *pdev)
{
    int ShiftBit = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;
    int temp = 0;
    int i = 0;
    int AttReversal[SWITCH_PORT_MAX] = {1,1,0,0,0,0,1,1};
    SubPort = SubPort - WT_RF_PORT_RF1;
    if (SubPort < SWITCH_PORT_1 || SubPort >= SWITCH_PORT_MAX )
    {
        dbg("wt_GetSwbAttCode_418VA Get Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    ShiftBit = SwitchAttRegBit_418VA[SubPort][1];
    SwAttReg = SwitchAttRegBit_418VA[SubPort][0];

    wt_GetSwitchShiftReg(SWTICH_PART_A, SwAttReg, &RegData);
    *Data = (RegData >> ShiftBit) & 0x3f;
    //bit高低位取反
    if(AttReversal[SubPort])
    {
    //bit高低位取反
        temp = 0;
        for (i = 0; i < 6; i++)
        {
            temp <<= 1;
            temp += *Data & 0x1;
            *Data >>= 1;
        }
        *Data = temp;
    }

    //衰减方向取反
    *Data = 0x3f - *Data;
    return WT_OK;
}

int wt_ReadSwbAttCodeFromCache_418VA(int SubPort, int *AttArr, int ShiftCache[])
{
    // 328CE AttArr[0] 表示开关板端口att
    // TODO 428C AttArr[0] 表示开关板vsg端口? AttArr[1] 表示开关板vsa端口? 

    int ShiftBit = 0;
    unsigned int SwAttReg = 0;
    unsigned int RegData = 0;
    int temp = 0;
    int i = 0;
    int AttCode = 0;
    int AttReversal[SWITCH_PORT_MAX] = {1,1,0,0,0,0,1,1};
    SubPort = SubPort - WT_RF_PORT_RF1;

    if (SubPort < SWITCH_PORT_1 || SubPort >= SWITCH_PORT_MAX )
    {
        dbg("wt_ReadSwbAttCodeFromCache_418VA Get Port %d Att Code. Port invaild\n", SubPort);
        return WT_ARG_ERROR;
    }

    ShiftBit = SwitchAttRegBit_418VA[SubPort][1];
    SwAttReg = SwitchAttRegBit_418VA[SubPort][0];

    RegData = ShiftCache[SwAttReg];

    AttCode = (RegData >> ShiftBit) & 0x3f;
    //bit高低位取反
    if(AttReversal[SubPort])
    {
        //bit高低位取反
        temp = 0;
        for (i = 0; i < 6; i++)
        {
            temp <<= 1;
            temp += AttCode & 0x1;
            AttCode >>= 1;
        }
        AttCode = temp;
    }

    //衰减方向取反
    AttCode = 0x3f - AttCode;

    AttArr[0] = AttCode;
    return WT_OK;
}

int WT_SetSwbAttCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct DeviceConfig DevfgTemp;
    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_SetSwbAttCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->testertype == HW_WT418)
    {
        return wt_SetSwbAttCode_418VA(DevfgTemp.RegTypeData.Addr, DevfgTemp.RegTypeData.Data, pdev);
    }
    else if(pdev->testertype >= HW_WT428)
    {
        return wt_SetSwbAttCode_428VA(DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Addr, DevfgTemp.RegTypeData.Data, pdev);
    }
    else
    {
        if (pdev->version == VERSION_A)
        {
            return wt_SetSwbAttCode_VA(DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Addr, DevfgTemp.RegTypeData.Data, pdev);
        }
        else
        {
            return wt_SetSwbAttCode_VB(DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Addr, DevfgTemp.RegTypeData.Data, pdev);
        }
    }
}

int WT_GetSwbAttCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct DeviceConfig DevfgTemp;

    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_GetSwbAttCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    DevfgTemp.RegTypeData.Data = 0;

    if(pdev->testertype == HW_WT418)
    {
        wt_GetSwbAttCode_418VA(DevfgTemp.RegTypeData.Addr, &DevfgTemp.RegTypeData.Data, pdev);
    }
    else if (pdev->testertype >= HW_WT428)
    {
        wt_GetSwbAttCode_428VA(DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Addr, &DevfgTemp.RegTypeData.Data, pdev);
    }
    else
    {
        if (pdev->version == VERSION_A)
        {
            wt_GetSwbAttCode_VA(DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Addr, &DevfgTemp.RegTypeData.Data, pdev);
        }
        else
        {
            wt_GetSwbAttCode_VB(DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Addr, &DevfgTemp.RegTypeData.Data, pdev);
        }
    }
    if (copy_to_user(arg, &DevfgTemp, DataLength))
    {
        dbg("WT_GetSwbAttCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_WriteSwitchStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteSwitchStatus info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return WT_OK;
}

int WT_ReadSwitchStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    struct RegType RegTypeTemp;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadSwitchStatus info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    RegTypeTemp.Data = 0;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("WT_ReadSwitchStatus info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return ret;
}

int WT_SetSwitchStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_SetSwitchStatus info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return WT_OK;
}

int WT_WriteSwitch42553(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    struct RegType RegTemp;
    int Index = 0;
    int PortAddr = BACK_SW_1_42553_CTL;
    int Data = 0;

    if (copy_from_user(&RegTemp, arg, DataLength))
    {
        dbg("WT_WriteSwitch42553 info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (RegTemp.Addr < WT_RF_PORT_RF1 || RegTemp.Addr > WT_RF_PORT_RF8)
    {
        dbg("WT_WriteSwitch42553 Port %d error. Port invaild\n", RegTemp.Addr);
        return WT_OK;
    }
    else if (RegTemp.Addr < WT_RF_PORT_RF5)
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF1;
        PortAddr = BACK_SW_1_42553_CTL;
    }
    else
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF5;
        PortAddr = BACK_SW_2_42553_CTL;
    }

    //回读code值
    Data = wt_read_direct_reg(pdev, PortAddr);
    RegTemp.Data ? SetBit(Data, Index) : ClearBit(Data, Index);
    wt_write_direct_reg(pdev, PortAddr, Data);
    return Ret;
}

int WT_ReadSwitch42553(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    struct RegType RegTemp;
    int Index = 0;
    int PortAddr = BACK_SW_1_42553_CTL;
    int Data = 0;

    if (copy_from_user(&RegTemp, arg, DataLength))
    {
        dbg("WT_ReadSwitch42553 info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (RegTemp.Addr < WT_RF_PORT_RF1 || RegTemp.Addr > WT_RF_PORT_RF8)
    {
        dbg("WT_ReadSwitch42553 Port %d error. Port invaild\n", RegTemp.Addr);
        return WT_OK;
    }
    else if (RegTemp.Addr < WT_RF_PORT_RF5)
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF1;
        PortAddr = BACK_SW_1_42553_CTL;
    }
    else
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF5;
        PortAddr = BACK_SW_2_42553_CTL;
    }

    //回读code值
    Data = wt_read_direct_reg(pdev, PortAddr);
    RegTemp.Data = GetBit(Data, Index);

    if (copy_to_user(arg, &RegTemp, DataLength))
    {
        dbg("WT_ReadSwitch42553 info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return Ret;
}

int WT_WriteSwitchPa(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    struct RegType RegTemp;
    int Index = 0;
    int PortAddr = BACK_SW_1_PA_EN;
    int Data = 0;

    if (copy_from_user(&RegTemp, arg, DataLength))
    {
        dbg("WT_WriteSwitchPa info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if (RegTemp.Addr < WT_RF_PORT_RF1 || RegTemp.Addr > WT_RF_PORT_RF8)
    {
        dbg("WT_WriteSwitchPa Port %d error. Port invaild\n", RegTemp.Addr);
        return WT_OK;
    }
    else if (RegTemp.Addr < WT_RF_PORT_RF5)
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF1;
        PortAddr = BACK_SW_1_PA_EN;
    }
    else
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF5;
        PortAddr = BACK_SW_2_PA_EN;
    }

    if (pdev->testertype == HW_WT448 && pdev->version == VERSION_A)
    {
        //回读code值
        Data = wt_read_direct_reg(pdev, PortAddr);
        RegTemp.Data ? SetBit(Data, Index) : ClearBit(Data, Index);
        wt_write_direct_reg(pdev, PortAddr, Data);
    }
    else
    {
        ;
    }

    return Ret;
}

int WT_ReadSwitchPa(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    struct RegType RegTemp;
    int Index = 0;
    int PortAddr = BACK_SW_1_PA_EN;
    int Data = 0;

    if (copy_from_user(&RegTemp, arg, DataLength))
    {
        dbg("WT_ReadSwitchPa info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (RegTemp.Addr < WT_RF_PORT_RF1 || RegTemp.Addr > WT_RF_PORT_RF8)
    {
        dbg("WT_ReadSwitchPa Port %d error. Port invaild\n", RegTemp.Addr);
        return WT_OK;
    }
    else if (RegTemp.Addr < WT_RF_PORT_RF5)
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF1;
        PortAddr = BACK_SW_1_PA_EN;
    }
    else
    {
        Index = RegTemp.Addr - WT_RF_PORT_RF5;
        PortAddr = BACK_SW_2_PA_EN;
    }

    if (pdev->testertype == HW_WT448 && pdev->version == VERSION_A)
    {
        //回读code值
        Data = wt_read_direct_reg(pdev, PortAddr);
        RegTemp.Data = GetBit(Data, Index);
    }
    else
    {
        RegTemp.Data = 0;
    }

    if (copy_to_user(arg, &RegTemp, DataLength))
    {
        dbg("WT_ReadSwitchPa info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return Ret;
}

int WT_SetSwitchState(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct SwitchPortSetType RegTypeTemp;
    // dbg("WT_SetSwitchState in!\n");
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_SetSwitchState info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    wt_SetSwitchStatus(RegTypeTemp.Type, RegTypeTemp.SwitchId, RegTypeTemp.SubPort, RegTypeTemp.Mode, RegTypeTemp.State);
    // dbg("WT_SetSwitchState out ret=%d!\n", WT_OK);
    return WT_OK;
}

int WT_SetSwitchShiftReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct ShiftRegData RegData;
    if (copy_from_user(&RegData, arg, DataLength))
    {
        dbg("WT_SetSwitchShiftReg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    return wt_SetSwitchShiftReg(RegData.SwitchId, RegData.RegId, RegData.RegValue);
}

int WT_GetSwitchShiftReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct ShiftRegData RegData;
    if (copy_from_user(&RegData, arg, DataLength))
    {
        dbg("WT_GetSwitchShiftReg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    RegData.RegValue = 0;
    wt_GetSwitchShiftReg(RegData.SwitchId, RegData.RegId, &RegData.RegValue);

    if (copy_to_user(arg, &RegData, DataLength))
    {
        dbg("WT_GetSwitchShiftReg info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int wt_SetSwitchStatus(int Type, int SwitchId, int SubPort, int StateMode, int State)
{
    struct dev_unit *pdev = pBPdev;
    int i = 0;
    int j = 0;
    int SwPartID = SwitchId;
    int PortPartID = SubPort - WT_RF_PORT_RF1;
    unsigned int Sw42553Addr = BACK_SW_1_42553_CTL;
    unsigned int Sw42553Index = SWITCH_PE42553_CTRL;
    unsigned int SwPaAddr = BACK_SW_1_PA_EN;
    unsigned int SwPacAddr = BACK_SW_PAC_CTL_1;
    unsigned int Array[SWITCH_SW_CTRL_REG_MAX] = {0};
    unsigned int SwitchValue[SWITCH_SW_CTRL_REG_MAX] = {0};
    unsigned int Temp = 0;
    int RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;
    struct SwitchPortType SwitchPort[BUSI_UB_TYPE_COUNT * SWITCH_UNIT_MAX];
    int SetPortNum = 0;
    int SwUnitID = WT428_SWTICH_UNIT_A;

    if(pdev->testertype == HW_WT418)
    {
        SwUnitID = (SubPort - WT_RF_PORT_RF1) / SWITCH_PORT_MAX;
    }
    else if(pdev->testertype == HW_WT448)
    {
        SwUnitID= (SubPort - WT_RF_PORT_RF1) / WT448_SWITCH_UNIT_PORT_MAX;
    }
    else if(pdev->testertype == HW_WT428)
    {
        SwUnitID= (SubPort - WT_RF_PORT_RF1) / WT428_SWITCH_UNIT_PORT_MAX;
    }

    dbg_print("wt_SetSwitchStatus Type=%d SwPartID=%d SwUnitID=%d SubPort=%d StateMode=%d State=%d\n", Type, SwitchId, SwUnitID, SubPort, StateMode, State);
    if (Type >= BUSI_UB_TYPE_COUNT || SubPort <= WT_RF_PORT_OFF || (pdev->testertype != HW_WT418 && SubPort > WT_RF_PORT_RF4) || State >= WT_PORT_STATE_MAX || SwitchId >= SWTICH_PART_MAX)
    {
        dbg("wt_SetSwitchStatus WT_ARG_ERROR Type=%d, SwPartID=%d, SwUnitID=%d, SubPort=%d, StateMode=%d, State=%d", Type, SwitchId, SwUnitID, SubPort, StateMode, State);
        return WT_ARG_ERROR;
    }

    WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_PORT] = PortPartID;
    WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] = State;
    WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_MODE] = StateMode;

    if (State == WT_RF_STATE_FULL_DUPLEX_PI)
    {
        WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_PORT] = WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_PORT];
        WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] = WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE];
        WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_MODE] = WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_MODE];
    }

    // VSA VSG PI PA DET状态不允许使用同一端口。
    if (WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_PORT] == WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_PORT] &&
        (WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] == WT_RF_STATE_PI ||
         WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] == WT_RF_STATE_PA_1 ||
         WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] == WT_RF_STATE_PA_2 ||
         WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] == WT_RF_STATE_DET_PI ||
         WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] == WT_RF_STATE_DET_PA_1 ||
         WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] == WT_RF_STATE_DET_PA_2) &&
        (WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] == WT_RF_STATE_PI ||
         WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] == WT_RF_STATE_PA_1 ||
         WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] == WT_RF_STATE_PA_2 ||
         WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] == WT_RF_STATE_DET_PI ||
         WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] == WT_RF_STATE_DET_PA_1 ||
         WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] == WT_RF_STATE_DET_PA_2))
    {
        WTSwitchSelect[SwPartID][SwUnitID][!Type][SWITCH_SELECT_STATE] = WT_RF_STATE_OFF;
    }

    if(pBPdev->testertype == HW_WT418)
    {
        for (j = 0; j < BUSI_UB_TYPE_COUNT; j++, SetPortNum++)
        {
            SwitchPort[SetPortNum].Type = j;
            SwitchPort[SetPortNum].SubPort = WTSwitchSelect[0][SwUnitID][j][SWITCH_SELECT_PORT];
            SwitchPort[SetPortNum].State = WTSwitchSelect[0][SwUnitID][j][SWITCH_SELECT_STATE];
            if(WTSwitchSelect[0][SwUnitID][j][SWITCH_SELECT_STATE] == WT_SW_STATE_MODE_SISO || WTSwitchSelect[0][SwUnitID][j][SWITCH_SELECT_MODE] == WT_SW_STATE_MODE_BROADCAST)
            {
                SwitchPort[SetPortNum].Mode = WTSwitchSelect[0][SwUnitID][j][SWITCH_SELECT_MODE];
            }
#if DEBUG_SHOW_SW_BIT_NAME
            dbg_print("Type=%d, Port=%d, State=%d, 8080Mode=%d\n",
                    SwitchPort[SetPortNum].Type, SwitchPort[SetPortNum].SubPort, SwitchPort[SetPortNum].State, SwitchPort[SetPortNum].Mode);
#endif
        }
    }
    else if (pBPdev->testertype == HW_WT428)
    {
        for (i = 0; i < SWTICH_PART_MAX; i++)
        {
            for (j = 0; j < BUSI_UB_TYPE_COUNT; j++, SetPortNum++)
            {
                SwitchPort[SetPortNum].Type = j;
                SwitchPort[SetPortNum].SubPort = WTSwitchSelect[i][SwUnitID][j][SWITCH_SELECT_PORT] +
                                                 i * SWITCH_PART_PORT_MAX;
                SwitchPort[SetPortNum].State = WTSwitchSelect[i][SwUnitID][j][SWITCH_SELECT_STATE];

                //两个单元都是8080时才选择8080，避免使用8080后，一个单元切回SISO，但另一个单位未重新配置的情况
                SwitchPort[SetPortNum].Mode = WTSwitchSelect[i][SwUnitID][j][SWITCH_SELECT_MODE] == WTSwitchSelect[!i][SwUnitID][j][SWITCH_SELECT_MODE]
                                                  ? WTSwitchSelect[i][SwUnitID][j][SWITCH_SELECT_MODE]
                                                  : WT_SW_STATE_MODE_SISO;
#if DEBUG_SHOW_SW_BIT_NAME
                dbg_print("Type=%d, Port=%d, State=%d, 8080Mode=%d\n",
                       SwitchPort[SetPortNum].Type, SwitchPort[SetPortNum].SubPort, SwitchPort[SetPortNum].State, SwitchPort[SetPortNum].Mode);
#endif
            }
        }
    }
    else
    {
        for (i = 0; i < WT448_SWITCH_UNIT_MAX; i++)
        {
            for (j = 0; j < BUSI_UB_TYPE_COUNT; j++, SetPortNum++)
            {
                SwitchPort[SetPortNum].Type = j;
                SwitchPort[SetPortNum].SubPort = WTSwitchSelect[SwPartID][i][j][SWITCH_SELECT_PORT];
                SwitchPort[SetPortNum].State = WTSwitchSelect[SwPartID][i][j][SWITCH_SELECT_STATE];

                //两个单元都是8080时才选择8080，避免使用8080后，一个单元切回SISO，但另一个单位未重新配置的情况 ?
                SwitchPort[SetPortNum].Mode = WTSwitchSelect[SwPartID][i][j][SWITCH_SELECT_MODE] == WTSwitchSelect[SwPartID][!i][j][SWITCH_SELECT_MODE]
                                                  ? WTSwitchSelect[SwPartID][i][j][SWITCH_SELECT_MODE]
                                                  : WT_SW_STATE_MODE_SISO;
#if DEBUG_SHOW_SW_BIT_NAME
                dbg_print("%d:%d, Type=%d, Port=%d, State=%d, 8080Mode=%d\n",
                       i, j, SwitchPort[SetPortNum].Type, SwitchPort[SetPortNum].SubPort, SwitchPort[SetPortNum].State, SwitchPort[SetPortNum].Mode);
#endif
            }
        }
    }

    wt_GetSwitchState(SwitchPort, SetPortNum, SwitchValue);

#if DEBUG_SHOW_SW_BIT_NAME
    for (i = 0; i < SWITCH_SW_CTRL_REG_MAX; ++i)
    {
        dbg_print("  #wt_GetSwitchState SwitchValue[%d]=0x%x\n", i, SwitchValue[i]);
    }
#endif

    Sw42553Addr = SwitchId ? BACK_SW_2_42553_CTL : BACK_SW_1_42553_CTL;
    SwPaAddr = SwitchId ? BACK_SW_2_PA_EN : BACK_SW_1_PA_EN;

    if (pdev->testertype >= HW_WT418)
    {
        RegCnt = WT418_SWITCH_SW_CTRL_REG_MAX;
    }
    else if (pdev->testertype >= HW_WT428)
    {
        Sw42553Index = SwitchId ? WT428_PE42553_CTRL2 : WT428_PE42553_CTRL1;
        RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;
    }
    else
    {
        Sw42553Index = SWITCH_PE42553_CTRL;
        RegCnt = (pdev->version >= VERSION_B) ? SWITCH_SHIFT_REG_GROUP_COUNT_VB : SWITCH_SHIFT_REG_GROUP_COUNT;
    }

    if(g_Switchlistmode [0] || g_Switchlistmode[1])
    {
        for (i = SWITCH_SHIFT_0_CR; i < RegCnt; ++i)
        {
            wt_GetSwitchShiftReg(SwPartID, i, &(Array[i]));
            Temp = Array[i];
            Array[i] |= (SwitchValue[i] & (SW_SHIFT_REG_MASK[i] & GetVirtualSwitchMask(Type, SubPort, i)));
            Array[i] &= (SwitchValue[i] | ~(SW_SHIFT_REG_MASK[i] & GetVirtualSwitchMask(Type, SubPort, i)));
            dbg_print("wt_SetSwitchStatus Reg%d, OrgData=0x%x, SwitchValue=0x%x, Mask=0x%x, Data=0x%x\n",
                i, Temp, SwitchValue[i], SW_SHIFT_REG_MASK[i] & GetVirtualSwitchMask(Type, SubPort, i), Array[i]);
            wt_SetSwitchShiftReg(SwPartID, i, Array[i]);
            // wt_GetSwitchShiftReg(SwPartID, i, &Temp, pdev);
        }
    }
    else
    {
        for (i = SWITCH_SHIFT_0_CR; i < RegCnt; ++i)
        {
            wt_GetSwitchShiftReg(SwPartID, i, &(Array[i]));
            Temp = Array[i];
            Array[i] |= (SwitchValue[i] & (SW_SHIFT_REG_MASK[i]));
            Array[i] &= (SwitchValue[i] | (~SW_SHIFT_REG_MASK[i]));
            dbg_print("wt_SetSwitchStatus Reg%d, OrgData=0x%x, SwitchValue=0x%x, Mask=0x%x, Data=0x%x\n",
                i, Temp, SwitchValue[i], SW_SHIFT_REG_MASK[i], Array[i]);
            wt_SetSwitchShiftReg(SwPartID, i, Array[i]);
            // wt_GetSwitchShiftReg(SwPartID, i, &Temp, pdev);
        }
    }

    if(pdev->testertype != HW_WT418)
    {
        // SW_PE42553_CTL
        Array[Sw42553Index] = wt_read_direct_reg(pdev, Sw42553Addr);
        Array[Sw42553Index] |= (SwitchValue[Sw42553Index] & SW_SHIFT_REG_MASK[Sw42553Index]);
        Array[Sw42553Index] &= (SwitchValue[Sw42553Index] | (~SW_SHIFT_REG_MASK[Sw42553Index]));
        wt_write_direct_reg(pdev, Sw42553Addr, Array[Sw42553Index]);
        Temp = wt_read_direct_reg(pdev, Sw42553Addr);
        dbg_print(" #SW_PE42553_CTL write=%x read=%x, Addr=%#x\n", Array[Sw42553Index], Temp, Sw42553Addr);

        if (pdev->testertype == HW_WT448 && pdev->version == VERSION_A)
        {
            // SW_PA_CTL
            i = SWITCH_PA_CTRL;
            Array[i] = wt_read_direct_reg(pdev, SwPaAddr);
            Array[i] |= (SwitchValue[i] & SW_SHIFT_REG_MASK[i]);
            Array[i] &= (SwitchValue[i] | (~SW_SHIFT_REG_MASK[i]));
            wt_write_direct_reg(pdev, SwPaAddr, Array[i]);
            Temp = wt_read_direct_reg(pdev, SwPaAddr);
            // printk(" #SW_PA_CTL write=%x read=%x\n", Array[i], Temp);
        }
    }
    if (pdev->testertype == HW_WT448 && pdev->swbversion >= VERSION_D)
    {
        // SW_PAC_CTL
        i = SWITCH_PAC_CTRL;
        Array[i] = wt_read_direct_reg(pdev, SwPacAddr);
        Array[i] |= (SwitchValue[i] & SW_SHIFT_REG_MASK[i]);
        Array[i] &= (SwitchValue[i] | (~SW_SHIFT_REG_MASK[i]));
        wt_write_direct_reg(pdev, SwPacAddr, Array[i]);
        Temp = wt_read_direct_reg(pdev, SwPacAddr);
        dbg_print(" #SW_PAC_CTL write=%x read=%x\n", Array[i], Temp);
    }

    return WT_OK;
}

// Type: VSA,VSG
// Port: [0,1,2,3]
// PortState: WT_PORT_STATE_E or WT_PORT_STATE_EX_E 且必须 < WT_RF_STATE_EX_MAX
inline int wt_SelectPort_S1(int Type, int SubPort, int PortState)
{
    dbg_print("set Type=%d SubPort=%d State=%d\n", Type, SubPort, PortState);
    if (Type == DEV_TYPE_VSA)
    {
        return g_pVsa_Select_SW(SubPort, PortState);
        // return Vsa_Select_SW_VA(Port, PortState);
    }
    else
    {
        return g_pVsg_Select_SW(SubPort, PortState);
        // return Vsg_Select_SW_VA(Port, PortState);
    }
}

int WT_SetSwitchCfg(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct SwitchCfgType SwCfgTemp;
    int Ret = WT_OK;

    if (copy_from_user(&SwCfgTemp, arg, DataLength))
    {
        dbg("WT_SetSwitchCfg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (SwCfgTemp.Type == SWITCH_INIT)
    {
        Ret = wt_SetSwitchStatus(DEV_TYPE_VSA, SWTICH_PART_A, WT_RF_PORT_RF1, WT_SW_STATE_MODE_SISO, WT_RF_STATE_INIT);
        Ret |= wt_SetSwitchStatus(DEV_TYPE_VSA, SWTICH_PART_B, WT_RF_PORT_RF1, WT_SW_STATE_MODE_SISO, WT_RF_STATE_INIT);
    }
    else if (SwCfgTemp.Type == SWITCH_MAP)
    {
        //开关板真值表初始化
        WT_InitSwitch(pdev, SwCfgTemp.SpecSwitchFlag);
        if (copy_from_user(g_SwitchBitMap, SwCfgTemp.Addr, SwCfgTemp.Size))
        {
            dbg("WT_SetSwitchCfg info copy_from_user failed!\n");
            return WT_CPY_FROM_USR_FAILED;
        }
        wt_GetInitState();
    }
    return Ret;
}

int wt_GetInitState(void)
{
    int Port = SWITCH_PART_PORT_1;
    int Type = DEV_TYPE_VSA;
    int SwitchUnitPortMax = (pBPdev->testertype == HW_WT448)
                                ? WT448_SWITCH_UNIT_PORT_MAX
                                : ((pBPdev->testertype == HW_WT418) 
                                    ? SWITCH_PORT_MAX 
                                    : WT428_SWITCH_UNIT_PORT_MAX);
    int SwitchPartPortMax = ((pBPdev->testertype == HW_WT418) 
                                    ? SWITCH_PORT_MAX 
                                    : SWITCH_PART_PORT_MAX);
    int SwPartID = 0;
    int SwUnitID = 0;

    memset(g_SwitchValue, 0, sizeof(g_SwitchValue));
    for (Type = DEV_TYPE_VSA; Type < DEV_TYPE_BACK; ++Type)
    {
        for (Port = SWITCH_PART_PORT_1; Port < SWITCH_PORT_MAX; ++Port)
        {
            SwPartID = Port / (SwitchPartPortMax);
            SwUnitID = (int)(Port % SwitchPartPortMax) / SwitchUnitPortMax;
            dbg_print(" wt_GetInitState SwPartID=%d SwUnitID=%d Type=%d Port=%d state=%d\n",
                   SwPartID, SwUnitID, Type, Port, WT_RF_STATE_INIT);
            wt_SelectPort_S1(Type, Port, WT_RF_STATE_INIT);

            //初始化当前选择的端口状态
            WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_PORT] = Port % SwitchPartPortMax;
            WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_STATE] = WT_RF_STATE_INIT;
            WTSwitchSelect[SwPartID][SwUnitID][Type][SWITCH_SELECT_MODE] = WT_SW_STATE_MODE_SISO;
        }
    }

    memcpy(g_SwitchValueInit, g_SwitchValue, sizeof(g_SwitchValue));

    for (Port = 0; Port < SWITCH_SW_CTRL_REG_MAX; ++Port)
    {
        dbg_print(" #sw reg[%d]=%d\n", Port, g_SwitchValue[Port]);
    }

    return 0;
}

int wt_GetSwitchState(struct SwitchPortType *SwitchPort, int PortNum, unsigned int *Value)
{
    int Ret = 0;
    int i = 0;
    int flag = true;
#if DEBUG_SHOW_SW_BIT_NAME
    dbg_print("wt_GetSwitchState PortNum=%d\n", PortNum);
#endif
    do
    {
        for(i=0;i<PortNum;i++)
        {
            if(SwitchPort[i].Mode == WT_SW_STATE_MODE_BROADCAST)
            {
                flag = false;
                break;
            }
        }
        if(flag)
        {
            memcpy(g_SwitchValue, g_SwitchValueInit, sizeof(g_SwitchValue));
        }

        for (i = 0; i < PortNum; i++)
        {
            // OFF不用选，和INIT一样就行
            if (SwitchPort[i].State != WT_RF_STATE_OFF && SwitchPort[i].State != WT_RF_STATE_INIT)
            {
#if DEBUG_SHOW_SW_BIT_NAME
                dbg_print("wt_SelectPort_S1 SwitchPort[%d].Type=%d, SubPort=%d, State=%d\n", i, SwitchPort[i].Type, SwitchPort[i].SubPort, SwitchPort[i].Mode);
#endif
                Ret = wt_SelectPort_S1(SwitchPort[i].Type, SwitchPort[i].SubPort, WT_RF_STATE_SISO + SwitchPort[i].Mode);
                Ret = wt_SelectPort_S1(SwitchPort[i].Type, SwitchPort[i].SubPort, SwitchPort[i].State);
            }
        }
        memcpy(Value, g_SwitchValue, sizeof(g_SwitchValue));
    } while (0);

    return Ret;
}

int wt_SetSwitchShiftReg(int SwId, int RegId, int Data)
{
    struct dev_unit *pdev = pBPdev;
    int SPIConfig = 0x9200;
    int DataReadBack = 0;
    unsigned int Reg[SPI_REG_TYPE_MAX] = {0};

    if (SwId >= SWTICH_PART_MAX)
    {
        return WT_DEVICE_ID_ERROR;
    }
    if(pdev->testertype == HW_WT418)
    {
        if (RegId >= WT418_SWITCH_SW_CTRL_REG_MAX)
        {
            return WT_OK;
        }
    }
    else if (pdev->testertype >= HW_WT428 || pdev->version == VERSION_A)
    {
        if (RegId >= SWITCH_SHIFT_REG_GROUP_COUNT)
        {
            return WT_DEVICE_ID_ERROR;
        }
    }
    else
    {
        if (RegId >= SWITCH_SHIFT_REG_GROUP_COUNT)
        {
            return WT_DEVICE_ID_ERROR;
        }
        else if (RegId >= SWITCH_SHIFT_REG_GROUP_COUNT_VB)
        {
            return WT_OK;
        }
    }

    if(pdev->testertype == HW_WT418)
    {
        SPIConfig = SwitchShiftSPIConfig_418VA[RegId];
    }
    else
    {
        // 发送字节是根据寄存器字长设置的
        SPIConfig = SPIConfig | (SwitchShiftRegLen[RegId] & 0x3F);
    }

    Reg[SPI_REG_TX_TYPE] = SwitchShiftRegAddr[SwId][RegId][SPI_REG_TX_TYPE];
    Reg[SPI_REG_RX_TYPE] = SwitchShiftRegAddr[SwId][RegId][SPI_REG_RX_TYPE];
    Reg[SPI_REG_CTRL1_TYPE] = SwitchShiftRegAddr[SwId][RegId][SPI_REG_CTRL1_TYPE];
    Reg[SPI_REG_CTRL2_TYPE] = SwitchShiftRegAddr[SwId][RegId][SPI_REG_CTRL2_TYPE];

    if (pdev->testertype == HW_WT418)
    {
        //SWITCH_SHIFT_0_CR和SWITCH_SHIFT_1_CR为同一个器件寄存器的高低位。
        int TempRegData = 0;
        if (RegId == SWITCH_SHIFT_0_CR)
        {
                wt_GetSwitchShiftReg(SwId, SWITCH_SHIFT_1_CR, &TempRegData);
                wt_write_direct_reg(pdev,
                                    SwitchShiftRegAddr[SwId][SWITCH_SHIFT_1_CR][SPI_REG_TX_TYPE],
                                    TempRegData);
        }
        else if (RegId == SWITCH_SHIFT_1_CR)
        {
                wt_GetSwitchShiftReg(SwId, SWITCH_SHIFT_0_CR, &TempRegData);
                wt_write_direct_reg(pdev,
                                    SwitchShiftRegAddr[SwId][SWITCH_SHIFT_0_CR][SPI_REG_TX_TYPE],
                                    TempRegData);
        }
    }

    g_SwitchShiftReg[SwId][RegId] = Data;
    if (SwitchShiftRegAddr[SwId][RegId][SPI_REG_TX_TYPE] == SwitchShiftRegAddr[!SwId][RegId][SPI_REG_TX_TYPE])
    {
        g_SwitchShiftReg[!SwId][RegId] = g_SwitchShiftReg[SwId][RegId];
    }
    // printk("wt_SetSwitchShiftReg SPIConfig=0x%x Tx=0x%x Rx=0x%x CTRL1=0x%x CTRL2=0x%x\n",
    //        SPIConfig, Reg[SPI_REG_TX_TYPE], Reg[SPI_REG_RX_TYPE], Reg[SPI_REG_CTRL1_TYPE], Reg[SPI_REG_CTRL2_TYPE]);

    if (pdev->testertype == HW_WT418 && (RegId == WT418_PAC_CTRL3 || RegId == WT418_LOOP_CTRL5))
    {
        //WT418_PAC_CTRL3与WT418_LOOP_CTRL5为FPGA直控
        wt_write_direct_reg(pdev,
                            RegId == WT418_PAC_CTRL3 ? WT418_SW_PAC_CTL3 : WT418_SW_LOOP_CTL5,
                            Data);
    }
    else
    {
        wt_bp_spi_direct_for_write(SPIConfig, Data, Reg, pdev);
    }

#if DEBUG_SHOW_SW_BIT_NAME
    dbg_print("wt_SetSwitchShiftReg SwId=%d, RegId=%d, write=%x\n", SwId, RegId, Data);
#endif

    wt_GetSwitchShiftReg(SwId, RegId, &DataReadBack);
    if (DataReadBack != Data)
    {
        dbg_print("warnning-------- Reset Switch Shift Reg=%#x, DataReadBack=%#x\n", Data, DataReadBack);
        wt_bp_spi_direct_for_write(SPIConfig, Data, Reg, pdev);
    }
    return WT_OK;
}


int wt_GetSwitchShiftReg(int SwId, int RegId, int *Data)
{
    struct dev_unit *pdev = pBPdev;
    unsigned int Addr = SwitchShiftRegAddr[SwId][RegId][SPI_REG_TX_TYPE];

    if (SwId >= SWTICH_PART_MAX)
    {
        *Data = 0;
        return WT_OK;
    }

    if(pdev->testertype == HW_WT418)
    {
        if (RegId >= WT418_SWITCH_SW_CTRL_REG_MAX)
        {
            *Data = 0;
            return WT_OK;
        }
    }
    else if (pdev->testertype >= HW_WT428 || pdev->version == VERSION_A)
    {
        if (RegId >= SWITCH_SHIFT_REG_GROUP_COUNT)
        {
            *Data = 0;
            return WT_OK;
        }
    }
    else
    {
        if (RegId >= SWITCH_SHIFT_REG_GROUP_COUNT_VB)
        {
            *Data = 0;
            return WT_OK;
        }
    }

    if (pdev->testertype >= HW_WT418)
    {
        //WT418_PAC_CTRL3与WT418_LOOP_CTRL5为FPGA直控
        Addr = (RegId == WT418_PAC_CTRL3
                    ? WT418_SW_PAC_CTL3
                    : (RegId == WT418_LOOP_CTRL5
                           ? WT418_SW_LOOP_CTL5
                           : SwitchShiftRegAddr_418VA[SwId][RegId][SPI_REG_TX_TYPE]));
    }
    else
    {
        if (pdev->testertype >= HW_WT428 || pdev->version >= VERSION_B)
        {
            Addr = SwitchShiftRegAddr[SwId][RegId][SPI_REG_TX_TYPE]; //移位寄存器RX不能回读，所以读TX（上一次写的值）
        }
        else
        {
            Addr = SwitchShiftRegAddr[SwId][RegId][SPI_REG_RX_TYPE];
        }
    }

    *Data = wt_read_direct_reg(pdev, Addr);
#if DEBUG_SHOW_SW_BIT_NAME
    dbg_print("wt_GetSwitchShiftReg SwId=%d, RegId=%d, read=%x\n", SwId, RegId, *Data);
#endif
    if (*Data != g_SwitchShiftReg[SwId][RegId])
    {
        dbg_print("warnning-------- wt_GetSwitchShiftReg Addr=%#x, Data=%#x, bak=%#x\n", Addr, *Data, g_SwitchShiftReg[SwId][RegId]);
    }
    return WT_OK;
}

int WT_SetSwitchCTL3(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int i = 0;
    struct SwitchRFPortSetType RFPortSetTemp;
    unsigned int SwitchValue[SWITCH_SW_CTRL_REG_MAX] = {0};
    int SwitchPort = SWITCH_PORT_1;
    int RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;

    if (copy_from_user(&RFPortSetTemp, arg, DataLength))
    {
        dbg("WT_SetSwitchCTL3 info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->testertype >= HW_WT418)
    {
        RegCnt = WT418_SWITCH_SW_CTRL_REG_MAX;
    }
    else if (pdev->testertype >= HW_WT428)
    {
        RegCnt = SWITCH_SHIFT_REG_GROUP_COUNT;
    }

    dbg_print("WT_SetSwitchCTL3 SwitchId=%d, SubPort=%d, State=%d\n", RFPortSetTemp.SwitchId, RFPortSetTemp.SubPort, RFPortSetTemp.State);
    for (i = 0; i < RegCnt; i++)
    {
        ret = wt_GetSwitchShiftReg(RFPortSetTemp.SwitchId, i, &(SwitchValue[i]));
        retWarnning(ret, "WT_SetSwitchCTL3 wt_GetSwitchShiftReg failed!\n");
    }

    SwitchPort = (pBPdev->testertype == HW_WT428) ? (RFPortSetTemp.SubPort - WT_RF_PORT_RF1 + RFPortSetTemp.SwitchId * SWITCH_PART_PORT_MAX)
                                                  : (RFPortSetTemp.SubPort - WT_RF_PORT_RF1);
    memcpy(g_SwitchValue, SwitchValue, sizeof(g_SwitchValue));
    g_pVsg_Select_CTL3(SwitchPort, RFPortSetTemp.State);

    for (i = 0; i < RegCnt; i++)
    {
        if (SwitchValue[i] != g_SwitchValue[i])
        {
            dbg_print("====wt_SetSwitchShiftReg Reg=%d, OldValue=%#x, NewValue=%#x\n", i, SwitchValue[i], g_SwitchValue[i]);
            ret = wt_SetSwitchShiftReg(RFPortSetTemp.SwitchId, i, g_SwitchValue[i]);
            retWarnning(ret, "WT_SetSwitchCTL3 wt_SetSwitchShiftReg failed!\n");
        }
    }

    return ret;
}

int WT_SetSwitchVsgGapPower(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    struct SwitchVsgIfgType SwitchVsgIfgData;
    int UnitPort = WT_RF_PORT_RF1;
    int RegValue = 0;
    if ((pdev->testertype == HW_WT448 && pdev->version == VERSION_A))
    {
        return WT_OK;
    }

    if (copy_from_user(&SwitchVsgIfgData, arg, DataLength))
    {
        dbg("WT_SetSwitchVsgGapPower info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (SwitchVsgIfgData.Port == WT_RF_PORT_OFF)
    {
        return WT_OK;
    }

    if (pdev->testertype == HW_WT418)
    {
        UnitPort = SwitchVsgIfgData.Port - WT_RF_PORT_RF1;
        RegValue = wt_read_direct_reg(pdev, BUSI_VSG_GAP_POWER_PORT);
        if (SwitchVsgIfgData.Status)
        {
            RegValue |= (0x1u << UnitPort);
        }
        else
        {
            RegValue &= ~(0x1u << UnitPort);
        }
        dbg_print("WT_SetSwitchVsgGapPower Addr=%#x, Data=%#x\n", BUSI_VSG_GAP_POWER_PORT, RegValue);
        wt_write_direct_reg(pdev, BUSI_VSG_GAP_POWER_PORT, RegValue);
        return Ret;
    }
    else if (pdev->testertype == HW_WT448)
    {
        UnitPort = (SwitchVsgIfgData.Port - WT_RF_PORT_RF1) % WT448_SWITCH_UNIT_PORT_MAX;
    }
    else if (pdev->testertype == HW_WT428)
    {
        UnitPort = (SwitchVsgIfgData.Port - WT_RF_PORT_RF1) % WT428_SWITCH_UNIT_PORT_MAX;
    }
    else
    {
        return WT_OK;
    }

    SwitchVsgIfgData.Status = (SwitchVsgIfgData.Status ? 0x10 : 0x00) + UnitPort;
    SwitchVsgIfgData.Solt = BACK_VSG_GAP_POWER_SOLT1 + SwitchVsgIfgData.Solt * 4;
    dbg_print("WT_SetSwitchVsgGapPower Addr=%#x, Data=%#x\n", SwitchVsgIfgData.Solt, SwitchVsgIfgData.Status);
    wt_write_direct_reg(pdev, SwitchVsgIfgData.Solt, SwitchVsgIfgData.Status);
    return Ret;
}

int WT_SetSwitchVsgGapPowerCtrlMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct SwitchIfgCtrlMode IfgCtrlMode;
    int RegValue = 0;
    if (copy_from_user(&IfgCtrlMode, arg, DataLength))
    {
        dbg("WT_SetSwitchVsgGapPowerCtrlMode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->testertype == HW_WT418)
    {
        RegValue = wt_read_direct_reg(pdev, BUSI_VSG_GAP_POWER_MODE);
        if (IfgCtrlMode.Status)
        {
            RegValue |= (0x1u << (IfgCtrlMode.Port - WT_RF_PORT_RF1));
        }
        else
        {
            RegValue &= ~(0x1u << (IfgCtrlMode.Port - WT_RF_PORT_RF1));
        }
        dbg_print("WT_SetSwitchVsgGapPowerCtrlMode Data=%#x\n", RegValue);
        wt_write_direct_reg(pdev, BUSI_VSG_GAP_POWER_MODE, RegValue);
    }
    return ret;
}

int WT_SetSwitchMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Status;
    if (copy_from_user(&Status, arg, DataLength))
    {
        dbg("WT_SetSwitchMode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if ((Status >> 1) & 0x1u)
    {
        g_Switchlistmode[1] = Status & 0x1u;
    }
    else
    {
        g_Switchlistmode[0] = Status & 0x1u;
    }

    dbg_print("WT_SetSwitchMode g_Switchlistmode [0]=%d \n", g_Switchlistmode[0]);
    dbg_print("WT_SetSwitchMode g_Switchlistmode [1]=%d \n", g_Switchlistmode[1]);
    return WT_OK;
}