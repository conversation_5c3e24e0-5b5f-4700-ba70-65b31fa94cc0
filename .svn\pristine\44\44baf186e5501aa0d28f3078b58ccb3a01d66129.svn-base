/*
 * scpi_cal_cmd.h
 *
 *  Created on: 2020-11-12
 *      Author: lifen
 */

#ifndef SCPI_CAL_CMD_H_
#define SCPI_CAL_CMD_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif
    scpi_result_t GetCalibrationState(scpi_t *context);
    scpi_result_t SetCalibrationState(scpi_t *context);
    scpi_result_t CalibrationDataReload(scpi_t *context);
    scpi_result_t SetInCalRunMode(scpi_t *context);
    scpi_result_t SetCalibrationFlatnessMode(scpi_t *context);
    scpi_result_t SetSelfCalStartorStop(scpi_t *context);
    scpi_result_t GetSelfCalibrationStatus(scpi_t *context);
#ifdef __cplusplus
}
#endif

#endif /* SCPI_CAL_CMD_H_ */
