#include "includeall.h"
#ifndef LINUX
#pragma comment(lib, "Dbghelp.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib,"WT.Tester.API.Common.lib")
#pragma comment(lib,"WT.Tester.API.IOControl.lib")
#pragma comment(lib,"WT.Tester.API.PNFileProcess.lib")
#endif
#ifndef LINUX
extern volatile unsigned int PN_OPERATE_LOCK = 0; //TODO 如果后续内存不够，考虑所有连接功用一个PN时启用
#else
extern atomic_flag PN_OPERATE_LOCK = ATOMIC_FLAG_INIT;
#endif
using namespace std;

void InstrumentHandle::InitPnBuf(void *pData)
{
    stPNFileInfo *tmpData = static_cast<stPNFileInfo*>(pData);
    m_pnInfos = tmpData;
}

InstrumentHandle::InstrumentHandle(int comuType)
{
    switch (comuType)
    {
    case WT_COMU_TYPE_TCP:
        m_pIoControlVsa.reset(new IOControl_TCP());
        m_pIoControlVsg.reset(new IOControl_TCP());
        m_pIoControlMisc.reset(new IOControl_TCP());
        m_pIoControlVsa->IOSetAliasName("VSA");
        m_pIoControlVsg->IOSetAliasName("VSG");
        m_pIoControlMisc->IOSetAliasName("Misc");
        break;
    default:
        break;
    }

    m_pCryptology.reset(new CryptologyWT4xx());

    m_currSubTesterCount = 0;
    m_vsgAc8080Flag = false;
    m_vsaAc8080Flag = false;
    m_vsgPnRefFlag = true;
    m_VsaParamUpdate = false;
    m_VsgParamUpdate = false;
    m_currTestMode = TEST_SISO_API;
    m_vecPnItem.clear();
    m_vecPnItemHead.clear();
    memset(&m_VsgWaveParameter, 0, sizeof(m_VsgWaveParameter));
    memset(m_SaveIQDataRange, 0, sizeof(m_SaveIQDataRange));
    memset(&m_VsaParameterBack, 0, sizeof(m_VsaParameterBack));
    memset(&m_VsgParameterBack, 0, sizeof(m_VsgWaveParameterBack));
    memset(&m_VsgWaveParameterBack, 0, sizeof(m_VsgWaveParameterBack));
    memset(&m_vsaExternParam, 0, sizeof(m_vsaExternParam));
    memset(&m_vsgExternParam, 0, sizeof(m_vsgExternParam));
    memset(&m_TbVarParam, 0, sizeof(m_TbVarParam));
    m_errcodeCount = 0;
    memset(m_HardErrorCode, 0, sizeof(m_HardErrorCode));
    memset(&m_TbVarParam, 0, sizeof(m_TbVarParam));
    memset(&m_3GPPWaveCreateAlzParam, 0, sizeof(m_3GPPWaveCreateAlzParam));

    m_vecPnItemHeadBack.clear();
    m_vecPnItemBack.clear();
    m_VsaParamSerialNum = 0;
    m_VsgParamSerialNum = 0;
    m_VsgWaveSerialNum = 0;
    m_VsgPnSerialNum = 0;

    m_FwVersion = "1.0.0.1";
    m_linkType = 0;
    m_vsgMasterMode = 0;
    m_vsaMasterMode = 0;
    m_tmpSampleFreq = 0.0;
    m_isvsa160mFlag = false;
    m_WaveExtName = "low";
    m_LowWaveToken = 0x58335457;
    m_LastSetVsgParamResult = WT_ERR_CODE_GENERAL_ERROR;
    m_LastSetVsaParamResult = WT_ERR_CODE_GENERAL_ERROR;
    GetDefaultParameter(&m_vsaParam, &m_avgParam, &m_vsgParam, nullptr, nullptr);
    m_vsaParam.RfPort[0] = WT_PORT_OFF;
    m_vsgParam.RfPort[0] = WT_PORT_OFF;
    m_vecConnectUnit.clear();
    m_TesterInfoList.clear();
    memset(m_AnalyzeParamInfo, 0, sizeof(m_AnalyzeParamInfo));
    memset(&m_Beaforming, 0, sizeof(m_Beaforming));
    m_VsaClockRate = 1;
    m_vsgAnalyzeType = WT_ALZ_PARAM_WIFI;
    m_SpectrumWideEnable = false;
    m_WifiFilterEnable = false;
    m_HandleScenario = HANDLE_SCENARIO_NORMAL;
    m_PacStatus = PAC_STATUS_IDEL;
    ////////////////////////////
#ifndef LINUX
    m_VsaSockLocker = 0;
    m_VsgSockLocker = 0;
    m_MiscSockLocker = 0;
#endif
    ////////////////////////////
    m_pnInfos = nullptr;
    m_FwRecvTimeOut = 500 * 1000;
    m_TesterRunMode = TESTER_RUN_NOMAL;

    m_RUSubCarrierCnt = 16;
    m_WaveRUSubCarrier.reset(new RUCarrierInfo[m_RUSubCarrierCnt]);

    m_WaveExternSettingLen = 0;
    m_WaveExternSetting = nullptr;

    m_maxSampleRate = MAX_SMAPLE_RATE_API;
}

InstrumentHandle::~InstrumentHandle()
{
    //多线程时，delete时其他的PAC,PER还在处理时，会出现IO为空指针问题
#ifdef LINUX
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_HandleScenario = " << m_HandleScenario << std::endl;
#endif
#ifdef LINUX
    GetUsableIOControler(IOCONTROL_VSA);
    m_pIoControlVsa.reset(nullptr);
    m_pIoControlVsg.reset(nullptr);
    m_pIoControlMisc.reset(nullptr);
    ResleaseCurrIOControler(IOCONTROL_VSA);
#else
    switch (m_HandleScenario)
    {
    case HANDLE_SCENARIO_ALL_SHARED:
        GetUsableIOControler(IOCONTROL_VSA);
        m_pIoControlVsa.reset(nullptr);
        m_pIoControlVsg.reset(nullptr);
        m_pIoControlMisc.reset(nullptr);
        ResleaseCurrIOControler(IOCONTROL_VSA);
        break;
    case HANDLE_SCENARIO_VSA_VSG_SHARED:
        GetUsableIOControler(IOCONTROL_VSA);
        GetUsableIOControler(IOCONTROL_MISC);
        m_pIoControlVsa.reset(nullptr);
        m_pIoControlVsg.reset(nullptr);
        m_pIoControlMisc.reset(nullptr);
        ResleaseCurrIOControler(IOCONTROL_VSA);
        ResleaseCurrIOControler(IOCONTROL_MISC);
        break;
    default:
        GetUsableIOControler(IOCONTROL_VSA);
        GetUsableIOControler(IOCONTROL_VSG);
        GetUsableIOControler(IOCONTROL_MISC);
        m_pIoControlVsa.reset(nullptr);
        m_pIoControlVsg.reset(nullptr);
        m_pIoControlMisc.reset(nullptr);
        ResleaseCurrIOControler(IOCONTROL_VSA);
        ResleaseCurrIOControler(IOCONTROL_VSG);
        ResleaseCurrIOControler(IOCONTROL_MISC);

        break;
    }
#endif
    memset(m_AnalyzeParamInfo, 0, sizeof(m_AnalyzeParamInfo));
#ifdef LINUX
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "InstrumentHandle destruct" << std::endl;
#endif
}

int InstrumentHandle::GetCurrSubTesterCfg(SubTesterCfg *testerCfg)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)testerCfg;
    pstRecvBuff.buff_len = sizeof(SubTesterCfg);
    pstRecvBuff.data_len = sizeof(SubTesterCfg);

    return Exchange(0, CMD_GET_CUR_DEV_RES_CONF, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);
}

int InstrumentHandle::GetSlaveTesterSubTesterCfg(
    int slaveTesterID,
    SubTesterCfg *testerCfg)
{
    int err = WT_ERR_CODE_OK;
    A_ASSERT(testerCfg);
    IOControl_TCP tmpIoControl;
    tmpIoControl.IOSetAliasName("GetSlaveTesterSubTesterCfg");
    const char *plainText = m_pCryptology->GetPlainText(m_deviceType, WT_CONNECT_TYPE_MultiUser);
    err = VerifyConnect(&m_vecConnectUnit[slaveTesterID], PORT_HOST, plainText, strlen(plainText) * sizeof(char), &tmpIoControl);
    if (WT_ERR_CODE_OK != err)
    {
        return err;
    }
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)testerCfg;
    pstRecvBuff.buff_len = sizeof(SubTesterCfg);
    pstRecvBuff.data_len = sizeof(SubTesterCfg);

    err = ProExchange(0, CMD_GET_CUR_DEV_RES_CONF, nullptr, 0, &pstRecvBuff, 1, &tmpIoControl, 1000, 3000);

    tmpIoControl.IODisconnect();
    return err;
}

int InstrumentHandle::GetTesterInfo(TesterInfo *info)
{
    int err = WT_ERR_CODE_OK;
    if (false == m_masterTesterInfo.bInited)
    {
        err = GetTesterInfo();
        if (WT_ERR_CODE_OK != err)
        {
            return err;
        }
    }
    memcpy(info, &m_masterTesterInfo.info, sizeof(TesterInfo));
    return err;

}

int InstrumentHandle::GetConnectStatus()
{
    int iRet = WT_ERR_CODE_CONNECT_FAIL;
#ifdef LINUX
    //默认只有一个socket连接
    if (m_pIoControlVsa)
    {
        if (m_pIoControlVsa->IOGetConnectState())
        {
            iRet = WT_ERR_CODE_OK;
        }
    }

#else
    switch (m_HandleScenario)
    {
    case HANDLE_SCENARIO_ALL_SHARED:
        if (m_pIoControlVsa)
        {
            if (m_pIoControlVsa->IOGetConnectState())
            {
                iRet = WT_ERR_CODE_OK;
            }
        }
        break;
    case HANDLE_SCENARIO_VSA_VSG_SHARED:
        if (m_pIoControlVsa && m_pIoControlMisc)
        {
            if (m_pIoControlVsa->IOGetConnectState() &&
                m_pIoControlMisc->IOGetConnectState())
            {
                iRet = WT_ERR_CODE_OK;
            }
        }
        break;
    default:
        if (m_pIoControlVsa && m_pIoControlVsg && m_pIoControlMisc)
        {
            if (m_pIoControlVsa->IOGetConnectState() &&
                m_pIoControlVsg->IOGetConnectState() &&
                m_pIoControlMisc->IOGetConnectState())
            {
                iRet = WT_ERR_CODE_OK;
            }
        }
        break;
    }
#endif // LINUX



    return iRet;
}

int InstrumentHandle::TranBuffToTesterInfo(
    char *buff,
    int buffSize,
    TesterOverview *info,
    int testerMaxCount,
    int *testerActulCount)
{
    int err = WT_ERR_CODE_OK;
    string strBuff("");
    int count = 0;
    size_t index = 0;
    if ('\0' != buff[0] && strlen(buff) > 0)
    {
        strBuff = string(buff);

        vector<string>InfoList;
        vector<string>lineStr;
        InfoList = UsualKit::split(strBuff, "\r\n");
        for (index = 0; index < InfoList.size() && count < testerMaxCount; index++)
        {
            memset(info + count, 0, sizeof(TesterOverview));
            lineStr = UsualKit::split(InfoList[index], ";");
            for (size_t i = 0; i < lineStr.size(); i++)
            {
                vector<string>contentStr;
                contentStr = UsualKit::split(lineStr[i], ":");
                if (2 == contentStr.size() && contentStr[1].length() > 0)
                {
                    if (contentStr[0] == string("TYPE"))
                    {
                        if (contentStr[1].length() <= sizeof(info[count].TesterType) - 1)
                        {
                            strcpy(info[count].TesterType, contentStr[1].c_str());
                        }
                        else
                        {
                            strncpy(info[count].TesterType, contentStr[1].c_str(), sizeof(info[count].TesterType) - 1);
                        }
                    }
                    else if (contentStr[0] == string("NAME"))
                    {
                        if (contentStr[1].length() <= sizeof(info[count].Name) - 1)
                        {
                            strcpy(info[count].Name, contentStr[1].c_str());
                        }
                        else
                        {
                            strncpy(info[count].Name, contentStr[1].c_str(), sizeof(info[count].Name) - 1);
                        }
                    }
                    else if (contentStr[0] == string("IP"))
                    {
                        if (contentStr[1].length() <= sizeof(info[count].Ip) - 1)
                        {
                            strcpy(info[count].Ip, contentStr[1].c_str());
                        }
                        else
                        {
                            strncpy(info[count].Ip, contentStr[1].c_str(), sizeof(info[count].Ip) - 1);
                        }
                    }
                    else if (contentStr[0] == string("SN"))
                    {
                        if (contentStr[1].length() <= sizeof(info[count].SN) - 1)
                        {
                            strcpy(info[count].SN, contentStr[1].c_str());
                        }
                        else
                        {
                            strncpy(info[count].SN, contentStr[1].c_str(), sizeof(info[count].SN) - 1);
                        }
                    }
                    else if (contentStr[0] == string("VERSION"))
                    {
                        if (contentStr[1].length() <= sizeof(info[count].FwVersion) - 1)
                        {
                            strcpy(info[count].FwVersion, contentStr[1].c_str());
                        }
                        else
                        {
                            strncpy(info[count].FwVersion, contentStr[1].c_str(), sizeof(info[count].FwVersion) - 1);
                        }
                    }
                }
            }
            if (lineStr.size() > 0)
            {
                count++;
            }
        }
    }

    *testerActulCount = count;
    return WT_ERR_CODE_OK;
}
#ifndef LINUX
int InstrumentHandle::QuerySpecTester(
    const char *ipAddr,
    TesterOverview *tersterinfo)
{
    int merr;
    int scans;
    unsigned int bufflen;
    SOCKET msock;
    BOOL fBroadcast;
    SOCKADDR_IN  mSocketAddr;
    WORD wVersionRequested;
    WSADATA wsaData;
    const int MaxBufSize = 4 * MAX_BUFF_SIZE;
    char recvBuff[MaxBufSize] = { 0 };

    //请求一个版本1.2的WinSock
    wVersionRequested = MAKEWORD(2, 1);
    merr = WSAStartup(wVersionRequested, &wsaData);
    if (merr != 0)
    {
        Logger::WriteLog(eumLogType_Error, "Environment Init\n");
        return WT_ERR_CODE_GENERAL_ERROR;

    }
    if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 1)
    {
        Logger::WriteLog(eumLogType_Error, "socket version\n");
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    //socket创建; UDP(SOCK_DGRAM)
    msock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (msock == INVALID_SOCKET)
    {
        Logger::WriteLog(eumLogType_Error, "socket create\n");
        WSACleanup();
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    do
    {
        //参数设置
        fBroadcast = 0;
        merr = setsockopt(msock, SOL_SOCKET, SO_BROADCAST, (char *)&fBroadcast, sizeof(BOOL));
        if (SOCKET_ERROR == merr)
        {
            merr = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        //绑定
        mSocketAddr.sin_family = AF_INET;           //地址簇
        mSocketAddr.sin_port = htons(0);        //端口
        mSocketAddr.sin_addr.S_un.S_addr = htonl(INADDR_ANY);    //转换网络地址ulong
        merr = bind(msock, (SOCKADDR *)&mSocketAddr, sizeof(SOCKADDR));
        if (0 != merr)
        {
            merr = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        /*struct linger linger = { 0 };
        linger.l_onoff = 0;
        linger.l_linger = 0;
        merr = setsockopt(msock, SOL_SOCKET, SO_DONTLINGER, (char *)&linger, sizeof(linger));
        if (SOCKET_ERROR == merr)
        {
        merr = WT_ERR_CODE_GENERAL_ERROR;
        break;
        }*/

        scans = 0;
        bufflen = 0;
        SOCKADDR_IN  client_addr;
        int client_size;

        Logger::WriteLog(eumLogType_Log, "Strart Scan %d\n", scans);
        //广播命令
        client_addr.sin_family = AF_INET;
        client_addr.sin_addr.S_un.S_addr = inet_addr(ipAddr);
        client_addr.sin_port = htons(7002);
        client_size = sizeof(SOCKADDR);
        char sendBuff[30] = { 0 };
        sprintf(sendBuff, "scan");
        unsigned int Len = sendto(msock, sendBuff, sizeof(char) * 30, 0, (SOCKADDR *)&client_addr, sizeof(SOCKADDR_IN));
        if (Len != sizeof(char) * 30)
        {
            merr = WT_ERR_CODE_TIMEOUT;
            break;
        }

        //接收命令 设置接收超时N秒
        unsigned int  TimeOut = 100;
        if (setsockopt(msock, SOL_SOCKET, SO_RCVTIMEO, (char *)&TimeOut, sizeof(TimeOut)) == SOCKET_ERROR)
        {
            Logger::WriteLog(eumLogType_Error, "setsockopt SO_RCVTIMEO\n");
            merr = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        //接收, 可以接收到端口的所有包,(广播包等)
        Len = recvfrom(msock, recvBuff, MaxBufSize, 0, (SOCKADDR *)&client_addr, &client_size);
        if (Len == SOCKET_ERROR)
        {
            merr = WT_ERR_CODE_TIMEOUT;
            break;
        }
        int count = 0;
        if ((merr = TranBuffToTesterInfo(recvBuff, MaxBufSize, tersterinfo, 1, &count)) != WT_ERR_CODE_OK)
        {
            break;
        }
    } while (0);

    closesocket(msock);
    WSACleanup();
    return merr;
}
#else
int InstrumentHandle::QuerySpecTester(
    const char *ipAddr,
    TesterOverview *tersterInfo)
{
    int merr = 0;
    int scans = 0;
    unsigned int bufflen = 0;
    int msock = -1;
    int fBroadcast = 0;
    struct sockaddr_in  mSocketAddr;
    const int MaxBufSize = 4 * MAX_BUFF_SIZE;
    char recvBuff[MaxBufSize] = { 0 };

#ifndef INVALID_SOCKET
#define INVALID_SOCKET		-1
#define SOCKET_ERROR		-1
#endif

    //socket创建; UDP(SOCK_DGRAM)
    msock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (msock == INVALID_SOCKET)
    {
        Logger::WriteLog(eumLogType_Error, "socket create\n");
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    do
    {
        //参数设置
        fBroadcast = 0;
        merr = setsockopt(msock, SOL_SOCKET, SO_BROADCAST, (char *)&fBroadcast, sizeof(fBroadcast));
        if (SOCKET_ERROR == merr)
        {
            merr = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        //绑定
        mSocketAddr.sin_family = AF_INET;           //地址簇
        mSocketAddr.sin_port = htons(0);        //端口
        mSocketAddr.sin_addr.s_addr = htonl(INADDR_ANY);    //转换网络地址ulong
        merr = bind(msock, (struct sockaddr *)&mSocketAddr, sizeof(struct sockaddr_in));
        if (0 != merr)
        {
            merr = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        scans = 0;
        bufflen = 0;
        struct sockaddr_in  client_addr;
        int client_size;

        Logger::WriteLog(eumLogType_Log, "Strart Scan %d\n", scans);
        //广播命令
        client_addr.sin_family = AF_INET;
        client_addr.sin_addr.s_addr = inet_addr(ipAddr);
        client_addr.sin_port = htons(7002);
        client_size = sizeof(struct sockaddr);
        char sendBuff[30] = { 0 };
        sprintf(sendBuff, "scan");
        unsigned int Len = sendto(msock, sendBuff, sizeof(char) * 30, 0, (struct sockaddr *)&client_addr, sizeof(struct sockaddr_in));
        if (Len != sizeof(char) * 30)
        {
            merr = WT_ERR_CODE_TIMEOUT;
            break;
        }

        //接收命令 设置接收超时N秒
        unsigned int  RecvTimeOut_ms = 500;
        struct timeval TimeOut;
        TimeOut.tv_sec = RecvTimeOut_ms / 1000;
        TimeOut.tv_usec = (RecvTimeOut_ms % 1000) * 1000;
        if (setsockopt(msock, SOL_SOCKET, SO_RCVTIMEO, (char *)&TimeOut, sizeof(TimeOut)) == SOCKET_ERROR)
        {
            Logger::WriteLog(eumLogType_Error, "setsockopt SO_RCVTIMEO\n");
            merr = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        //接收, 可以接收到端口的所有包,(广播包等)
        Len = recvfrom(msock, recvBuff, MaxBufSize, 0, (struct sockaddr *)&client_addr, (socklen_t*)&client_size);
        if (Len == SOCKET_ERROR)
        {
            merr = WT_ERR_CODE_TIMEOUT;
            break;
        }
        int count = 0;
        if ((merr = TranBuffToTesterInfo(recvBuff, MaxBufSize, tersterInfo, 1, &count)) != WT_ERR_CODE_OK)
        {
            break;
        }
    } while (0);

    if (msock != INVALID_SOCKET)
    {
        close(msock);
    }
    return merr;
}
#endif

int InstrumentHandle::CorrectionInstrumentType(
    char *IP,
    int *type)
{
    TesterOverview tersterInfo;
    int err = QuerySpecTester(IP, &tersterInfo);

    if (WT_ERR_CODE_OK == err)
    {
        if (string(tersterInfo.TesterType).find("448") != string::npos)
        {
            *type = TEST_TYPE_ENUM_WT448;
        }
        else if (string(tersterInfo.TesterType).find("428") != string::npos)
        {
            *type = TEST_TYPE_ENUM_WT428;
        }
        else if (string(tersterInfo.TesterType).find("418") != string::npos || string(tersterInfo.TesterType).find("328CE") != string::npos)
        {
            *type = TEST_TYPE_ENUM_WT418;
        }
    }
    else
    {
#ifdef LINUX
        WTLog::Instance().WriteLog(LOG_DEBUG, "InstrumentHandle::CorrectionInstrumentType: err：%d\n", err);
#endif
    }
    return err;
}


int InstrumentHandle::QueryRunTimeConfiguration(
    char *resultBuff,
    unsigned int resultBuffSize,
    char *ConfigParam)
{
    A_ASSERT(resultBuff && ConfigParam);

    int moniType = *(int *)(resultBuff);
    if (moniType != MON_METER_SETTING)
    {
        return WT_ERR_CODE_MONITOR_TYPE_MISMATCH;
    }

    if (ConfigParam)
    {
        int datasize = *(int *)(resultBuff + sizeof(int));
        memcpy(ConfigParam, resultBuff + 2 * sizeof(int), datasize);
    }
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SetExternalGain(double extGain)
{

    return 0;
}

int InstrumentHandle::AddMimoTester(ConnectedUnit connUnit)
{
    return AddMimoTester(m_currSubTesterCount, connUnit);
}

int InstrumentHandle::AddMimoTester(
    int signalID,
    ConnectedUnit connUnit)
{
    if ((0 == connUnit.SubTesterIndex) || (connUnit.SubTesterIndex > 2))           //子仪器编号不合理即返回
    {
        return WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    if (signalID >= MAX_NUM_OF_CHNNEL)           //仪器最大能添加的子仪器数
    {
        return WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    int err = WT_ERR_CODE_OK;
    do
    {
        //数字IQ模式不实际连接MIMO从机
        if (TESTER_RUN_DIGIT_IQ == m_TesterRunMode)
        {
            break;
        }
#ifdef LINUX
        //只有一个socket连接
        err = AddMimoTester(signalID, connUnit, IOCONTROL_VSA);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
#else
        err = AddMimoTester(signalID, connUnit, IOCONTROL_VSA);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        err = AddMimoTester(signalID, connUnit, IOCONTROL_VSG);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        err = AddMimoTester(signalID, connUnit, IOCONTROL_MISC);
#endif
    } while (0);

    do
    {
        if (TESTER_RUN_DIGIT_IQ == m_TesterRunMode)
        {
            if (m_currSubTesterCount >= MAX_NUM_OF_CHNNEL)
            {
                err = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            else
            {
                m_currSubTesterCount++;
            }
            break;
        }

        if (WT_ERR_CODE_OK != err)
        {
            m_testCount.clear();
            m_testCount.insert(make_pair((string)m_masterTesterInfo.info.IP, m_masterTesterInfo.info.BusiBoardCount));   //留下最后一个元素
            m_testCount[(string)m_masterTesterInfo.info.IP]--;  //还能添加该ip的个数
            break;
        }

        if (signalID < MAX_NUM_OF_CHNNEL)
        {
            if (m_currSubTesterCount >= MAX_NUM_OF_CHNNEL)
            {
                err = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            for (auto itor = m_TesterInfoList.begin(); itor != m_TesterInfoList.end(); itor++)
            {
                //WT-318 NOT SUPPORT 2*2 MIMO WITH THE SAME IP
                if (string(itor->IP) == string(connUnit.Ip))
                {
                    if (TEST_TYPE_ENUM_WT318 == itor->TesterType)
                    {
                        err = WT_ERR_CODE_CONNECT_FAIL;
                        break;
                    }
                }
            }

            m_currSubTesterCount++;
            m_vecConnectUnit.push_back(connUnit);

            TesterInfo testinfo;
            map<string, int>::iterator iter;
            iter = m_testCount.find((string)connUnit.Ip);

            if (iter == m_testCount.end())           //不在当前map中
            {
                if (0 == strcmp(m_masterTesterInfo.info.IP, connUnit.Ip))
                {
                    err = GetTesterInfo(&testinfo);
                }
                else
                {
                    err = GetSlaveTesterInfo(signalID, &testinfo);
                }

                if (0 == testinfo.BusiBoardCount)                //无业务板则返回
                {
                    err = WT_ERR_CODE_GENERAL_ERROR;
                    break;
                }
                m_testCount.insert(make_pair((string)testinfo.IP, testinfo.BusiBoardCount)); //有单元板则添加
                m_TesterInfoList.push_back(testinfo);
            }
            else     //已经在当前map中
            {
                if (((m_testCount.find((string)connUnit.Ip))->second) == -1)
                {
#ifdef LINUX
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "==>>IP = " << connUnit.Ip << ", testinfo.BusiBoardCount = " << m_testCount[connUnit.Ip] << std::endl;
#endif
                    err = WT_ERR_CODE_GENERAL_ERROR;
                    break;
                }
            }

            m_testCount[(string)connUnit.Ip]--;
#ifdef LINUX
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "IP = " << connUnit.Ip << ", testinfo.BusiBoardCount = " << m_testCount[connUnit.Ip] << std::endl;
#endif
            if (0 == m_testCount[(string)connUnit.Ip])
            {
                m_testCount[(string)connUnit.Ip] = -1;
            }
        }
        else
        {
            err = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
    } while (0);

    //TODO 标记VSA、VSG参数必须重新配置，临时通过修改不可能的值达到该效果
    m_VsaParamUpdate = true;
    m_VsgParamUpdate = true;
#ifdef LINUX
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TesterRunMode = " << m_TesterRunMode << ", m_currSubTesterCount = " << m_currSubTesterCount << ", m_currTestMode = " << m_currTestMode << std::endl;
#endif
    return err;
}

int InstrumentHandle::AddMimoTester(
    int signalID,
    ConnectedUnit connUnit,
    int controlType)
{
    char proBuff[MAX_BUFF_SIZE] = { 0 };
    memcpy(proBuff, &signalID, sizeof(int));
    memcpy(proBuff + sizeof(int), &connUnit, sizeof(ConnectedUnit));
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.data_len = sizeof(ConnectedUnit) + sizeof(int);
    pstSendBuff.buff_len = sizeof(ConnectedUnit) + sizeof(int);

    return Exchange(0, CMD_ADD_MIMO_DEV, &pstSendBuff, 1, nullptr, 0, controlType, 1000, 2000);
}

int InstrumentHandle::RemoveMimoTester()
{
    int err = WT_ERR_CODE_OK;

    if (TESTER_RUN_DIGIT_IQ != m_TesterRunMode)
    {
#ifdef LINUX
        for (int signalID = m_currSubTesterCount - 1; signalID > 0; signalID--)
        {
            err += RemoveMimoTester(signalID, IOCONTROL_VSA);
        }
#else
        for (int signalID = m_currSubTesterCount - 1; signalID > 0; signalID--)
        {
            err += RemoveMimoTester(signalID, IOCONTROL_VSA);
            err += RemoveMimoTester(signalID, IOCONTROL_VSG);
            err += RemoveMimoTester(signalID, IOCONTROL_MISC);
        }
#endif
        auto itor = m_vecConnectUnit.begin() + 1;
        m_vecConnectUnit.erase(itor, m_vecConnectUnit.end());    //删除除主机以外的从机
        m_TesterInfoList.erase(m_TesterInfoList.begin() + 1, m_TesterInfoList.end());
    }

    //删除所有的从机后VSA、VSG的ValideNum的有效性值为1
    m_vsaParam.ValidNum = 1;
    m_vsgParam.ValidNum = 1;
    //TODO 标记VSA、VSG参数必须重新配置，临时通过修改不可能的值达到该效果
    m_VsaParamUpdate = true;
    m_VsgParamUpdate = true;
    //只剩下主机
    m_currSubTesterCount = 1;
    m_testCount.clear();
    m_testCount.insert(make_pair(m_masterTesterInfo.info.IP, m_masterTesterInfo.info.BusiBoardCount));   //留下最后一个元素
    m_testCount[m_masterTesterInfo.info.IP]--;  //还能添加该ip的个数

    return err;
}

int InstrumentHandle::RemoveMimoTester(
    int signalID,
    int controlType)
{
    char proBuff[MAX_BUFF_SIZE] = { 0 };
    memcpy(proBuff, &signalID, sizeof(int));
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(proBuff);
    pstSendBuff.data_len = sizeof(signalID);
    return Exchange(0, CMD_DEL_MIMO_DEV, &pstSendBuff, 1, nullptr, 0, controlType);
}


int InstrumentHandle::StartRecord()
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::PauseRecord()
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::FinishRecord()
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetTesterAllRecordNames(char *recordNameBuffer,
    int recordNameBufferSize,
    int *recordCount)
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::ReadRecord(
    char *recordName,
    char *Data,
    int dataLength)
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetPnDescription(
    char *fileName,
    char *description,
    unsigned int size)
{

    stPNFileInfo *tmpPnInfo = m_pnInfos;
    int err = WT_ERR_CODE_OK;
    ENTER_LOCK(PN_OPERATE_LOCK);
    err = GetPNFileInfoFromFileMIMO(fileName, (int)(m_vsaParam.SamplingFreq / MHz_API + 0.1), 0, 0, &tmpPnInfo[0], PN_VSA_SCENE_MODE);
    if (WT_ERR_CODE_OK == err)
    {
        if (tmpPnInfo[0].descreption != nullptr)
        {
            int copysize = size < strlen(tmpPnInfo[0].descreption) ? size : strlen(tmpPnInfo[0].descreption);
            memcpy(description, tmpPnInfo[0].descreption, copysize);
            err = WT_ERR_CODE_OK;
        }
    }
    EXIT_LOCK(PN_OPERATE_LOCK);

    return err;
}

int InstrumentHandle::PnFileProcess(const char *filename,
    const char *rename,
    int wave2Flag,
    int opType,
    unique_ptr<char[]> &buffer,
    size_t *realSize)
{
    int chainNum = 0;
    u32 buffSize = 0;
    int err = WT_ERR_CODE_OK;
    ENTER_LOCK(PN_OPERATE_LOCK);
    do
    {
        err = ParseSignalFile(filename, rename, wave2Flag, &buffSize, opType, &chainNum);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        buffSize = buffSize + SUP_MAX_BUFF_SIZE;
        //分配内存
        buffer.reset(new (std::nothrow) char[buffSize]);
        if (nullptr == buffer.get())
        {
            err = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
            break;
        }
        memset(buffer.get(), 0, buffSize);
        err = GetStandardSigFileBuffer(filename,
            rename,
            buffer.get(),
            buffSize,
            wave2Flag,
            realSize,
            opType,
            &chainNum);
    } while (0);
    EXIT_LOCK(PN_OPERATE_LOCK);
    return err;
}

int InstrumentHandle::PnFileProcess(const char *filename,
    const char *rename,
    int wave2Flag,
    int opType,
    vector<memCollector> &buffers)
{
    int chainNum = 0;
    u32 buffSize = 0;
    int err = WT_ERR_CODE_OK;

    do
    {
        err = ParseSignalFile(filename, rename, wave2Flag, &buffSize, opType, &chainNum);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        err = GetStandardSigFileBuffer(filename,
            rename,
            buffers,
            wave2Flag,
            opType,
            &chainNum);
    } while (0);

    return err;
}


void InstrumentHandle::BackupPnItem()
{
    m_vecPnItemBack.clear();
    for (auto itor = m_vecPnItem.begin(); itor != m_vecPnItem.end(); itor++)
    {
        m_vecPnItemBack.push_back(*itor);
    }
    m_vecPnItemHeadBack.clear();
    for (auto itor = m_vecPnItemHead.begin(); itor != m_vecPnItemHead.end(); itor++)
    {
        m_vecPnItemHeadBack.push_back(*itor);
    }
}


int InstrumentHandle::SetCmimoRefFile(const char *fileName)
{
    /*ExchangeBuff pstSendBuff = { 0 };
    pstSendBuff.chpHead = (char*)fileName;
    pstSendBuff.buff_len = MAX_NAME_SIZE;
    pstSendBuff.data_len = MAX_NAME_SIZE;
    return Exchange(0, CMD_SET_REF_FILE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);*/
    return WT_ERR_CODE_OK;
}


int InstrumentHandle::VerifyConnect(ConnectedUnit *connUnit,
    unsigned int port,
    const char *pcPlaintext,
    unsigned int plaintext_len,
    IOControl *pIoControl)
{
    char recvBuff[MAX_BUFF_SIZE] = { 0 };
    char password[100] = { 0 };

    if (pIoControl == nullptr)
    {
        return WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    if (pIoControl->IOConnect(connUnit->Ip, port) != WT_ERR_CODE_OK)
    {
        Logger::WriteLog(eumLogType_Error, "Connect Failed:ioconnect failed");
        return WT_ERR_CODE_CONNECT_FAIL;
    }
    int err = WT_ERR_CODE_OK;
    const char *authText = m_pCryptology->GetAuthText();
    err = pIoControl->IOExchange(authText, strlen(authText), recvBuff, 16);
    if (err < 0)
    {
        Logger::WriteLog(eumLogType_Error, "Connect failed,port:%d:send auth text failed", port);
        return WT_ERR_CODE_CONNECT_FAIL;
    }

    //对接收到的随机数加密
    //const char *plainText=m_pCryptology->GetPlainText(m_deviceType,connType);
    memcpy(password, pcPlaintext, plaintext_len);
    m_pCryptology->Encrypt((int *)password, plaintext_len / 4, (int *)recvBuff, 4);
    pIoControl->IOCleanBuff();
    memcpy(password + 16, &(connUnit->SubTesterIndex), sizeof(int));
    memset(recvBuff, 0, MAX_BUFF_SIZE);
    err = pIoControl->IOExchange(password, 30, recvBuff, 13);
    if (err < 0)
    {
        Logger::WriteLog(eumLogType_Error, "Connect failed,port:%d:verify failed\n", port);
        return WT_ERR_CODE_CONNECT_FAIL;
    }
    if (nullptr != strstr(recvBuff, "mgrlink exist"))
    {
        Logger::WriteLog(eumLogType_Error, "Connect failed,port:%d:connection refused\n", port);
        return WT_ERR_CODE_CONNECT_REFUSED;
    }
    if (nullptr != strstr(recvBuff, "license error"))
    {
        Logger::WriteLog(eumLogType_Error, "Connect failed,port:%d:license error\n", port);
        return WT_ERR_CODE_LICENSE_ERROR;
    }
    //连接后，如果不能正常发送，就认为是连接被拒绝
    /*int sendLen=pIoControl->IOSend(recvBuff,strlen(recvBuff)*sizeof(char),100);
    if (sendLen<=0)
    {
    Logger::WriteLog(eumLogType_Log,"Connect is refused,port:%d!!!\n",port);
    return WT_ERR_CODE_CONNECT_REFUSED;
    }*/
    Logger::WriteLog(eumLogType_Log, "%s Connect success,port:%d!!!\n", pIoControl->IOGetAliasName(), port);
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::DisconnectDevice()
{
    m_pIoControlVsa->IODisconnect();
    m_pIoControlVsg->IODisconnect();
    m_pIoControlMisc->IODisconnect();

    m_vecMasterLicense.clear();
    m_masterTesterInfo.bInited = false;

    memset(m_AnalyzeParamInfo, 0, sizeof(m_AnalyzeParamInfo));
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::ProSendWrapperData(int mode,
    int fun,
    ExchangeBuff *sendBuff,
    unsigned int sendBuffCnt,
    char *WrapperHeader,
    IOControl *pIoControl,
    unsigned int sendTimeOutMs)
{
    size_t send_len = 0;
    size_t i = 0;
    int err = WT_ERR_CODE_OK;
    char TxBuff[TX_BUFF_LEN] = { 0 };

    A_ASSERT(pIoControl);
    send_len = m_pCryptology->GetProAckHeadSize();
    if (0 == sendBuffCnt)
    {
        send_len += 4;
    }
    else
    {
        for (i = 0; i < sendBuffCnt; i++)
        {
            send_len += sendBuff[i].data_len;
        }
    }
    do
    {
        //封包
        err = m_pCryptology->WrapUpPackage(mode, fun, sendBuff, sendBuffCnt, TxBuff);
        if (err < 0)
        {
            err = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        pIoControl->IOCleanBuff();
        //发送数据头
        int Len = pIoControl->IOSend(TxBuff, m_pCryptology->GetProCmdSize(), sendTimeOutMs);
        if (Len < 0)
        {
            Logger::WriteLog(eumLogType_Error, "IOSend failed (mode %d fun %d),error code:%d\n", mode, fun, err);
            err = WT_ERR_CODE_TIMEOUT;
            break;
        }
        //发送数据
        for (int i = 0; i < sendBuffCnt; i++)
        {
            Len = pIoControl->IOSend(sendBuff[i].chpHead, sendBuff[i].data_len, sendTimeOutMs);
            if (Len < 0)
            {
                Logger::WriteLog(eumLogType_Error, "IOSend failed (mode %d fun %d),error code:%d\n", mode, fun, err);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }

    } while (0);

    if (WT_ERR_CODE_OK == err)
    {
        memcpy(WrapperHeader, TxBuff, m_pCryptology->GetProCmdSize());
    }

    return err;
}

int InstrumentHandle::ProcFindHeader(int mode,
    char *headptr,
    IOControl *pIoControl,
    int recvTimeOutMs,
    int *startIndex,
    int *currentPos,
    int *recvLen)
{
    int offset = 0;
    int err = WT_ERR_CODE_OK;
    int len = 0;
    int index = 0;
    int size = 0;
    int header_size = 0;
    int start_flag_size = 0;
    const char *data = nullptr;
    switch (mode)
    {
    case TYPE_CMD:
        header_size = m_pCryptology->GetProCmdHeadSize();
        start_flag_size = m_pCryptology->GetProCmdStartFlagSize();
        data = m_pCryptology->GetProCmdStartFlag(nullptr, 0);
        break;
    default:
        header_size = m_pCryptology->GetProAckHeadSize();
        start_flag_size = m_pCryptology->GetProAckStartFlagSize();
        data = m_pCryptology->GetProAckStartFlag(nullptr, 0);
        break;
    }
    offset = 0;
    do
    {
        len = pIoControl->IORecv(headptr, header_size, recvTimeOutMs);
        if (len <= 0)
        {
            Logger::WriteLog(eumLogType_Warning, "Rcv head timeout (fun %d)\n", mode);
            err = WT_ERR_CODE_TIMEOUT;
            goto func_exit;
        }
        char *pchTmp = (char *)&data;
        if (nullptr == pchTmp)
        {
            err = WT_ERR_CODE_GENERAL_ERROR;
            goto func_exit;
        }

        for (index = 0; index < (unsigned int)len; index++)
        {
            if (headptr[index] == pchTmp[offset])
            {
                offset++;
                if (start_flag_size == offset)
                {
                    // 找到协议头 start_flag
                    break;
                }
                continue;
            }
            else if (headptr[index] == pchTmp[0])
            {
                offset = 1;
            }
            else
            {
                offset = 0;
            }
        } /* for loop */
        if (start_flag_size == offset)
        {
            break;
        }
    } while (1);                     // TODO???

    index += 1;

    if (WT_ERR_CODE_OK == err)
    {
        if (startIndex)
        {
            *startIndex = index;
        }
        if (currentPos)
        {
            *currentPos = offset;
        }
        if (recvLen)
        {
            *recvLen = len;
        }
    }
func_exit:
    return err;
}


int InstrumentHandle::VerifyProAckHeader(
    char *sendBuf,
    char *recvBuf)
{
    int err = WT_ERR_CODE_OK;
    int errFromFir = 0;
    int verifyResult = m_pCryptology->VerifyProAckHeadExceptStartFlag(recvBuf, sendBuf, &errFromFir);
    if (((verifyResult & NET_ID_TOO_SMALL) != NET_REPLY_OK) || ((verifyResult & NET_FUN_MISMATCH) != NET_REPLY_OK))
    {
        return WT_ERR_CODE_LAST;
    }
    else if (verifyResult == NET_NO_REPLY_DATA)
    {
        err = WT_ERR_CODE_OK;
        goto func_exit;
    }
    else if (((verifyResult & NET_LENGTH_TOO_SHORT) != NET_REPLY_OK) ||
        ((verifyResult & NET_ID_TOO_LARGE) != NET_REPLY_OK))
    {
        dbg("NET_LENGTH_TOO_SHORT or NET_ID_TOO_LARGE\n");
        err = WT_ERR_CODE_GENERAL_ERROR;
        goto func_exit;
    }
    else if (((verifyResult & NET_RESULT_FAIL) != NET_REPLY_OK))
    {
        dbg("NET_RESULT_FAIL\n");
        err = errFromFir;
        goto func_exit;
    }
func_exit:
    return err;
}

int InstrumentHandle::ProcExchangeHeader(int mode,
    int fun,
    char *sendBuf,
    IOControl *pIoControl,
    int recvTimeOutMs,
    int *dataLength)
{
    char head_buff[256] = { 0 };
    char recvBuf[256] = { 0 };
    int offset = 0;
    int err = WT_ERR_CODE_OK;
    int len = 0;
    int index = 0;
    int size = 0;
    do
    {
        offset = 0;
        memset(head_buff, 0, sizeof(head_buff));
        err = ProcFindHeader(TYPE_ACK, head_buff, pIoControl, recvTimeOutMs, &index, &offset, &len);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        // 正好读到的4字节与协议开始标志完全匹配
        if (index == len)
        {
            size = m_pCryptology->GetProAckSize() - m_pCryptology->GetProAckHeadSize();
        }
        // 除了读到4字节的协议开始标志外，还读取到了后面的一些字节
        else
        {
            len -= index;
            memcpy(&recvBuf[offset], &head_buff[index], len);
            offset += len;
            size = m_pCryptology->GetProAckNoStartFlagSize() - len;
        }
        if (size != 0)
        {
            len = pIoControl->IORecv(&recvBuf[offset], size, recvTimeOutMs);
            if (len != size)
            {
                Logger::WriteLog(eumLogType_Error, "Pro len mismatch (mode %d fun %d), req: %d, rcv: %d\n", mode, fun, size, len);
                err = WT_ERR_CODE_TIMEOUT;
                goto func_exit;
            }
        }
        err = VerifyProAckHeader(sendBuf, recvBuf);
        if (WT_ERR_CODE_LAST == err)
        {
            continue;
        }
    } while (0);
    if (WT_ERR_CODE_OK == err && dataLength)
    {
        *dataLength = m_pCryptology->GetDataLength(recvBuf);
    }
func_exit:
    return err;
}

int InstrumentHandle::ProExchange(int mode,
    int fun,
    ExchangeBuff *sendBuff,
    unsigned int sendBuffCnt,
    ExchangeBuff *recvBuff,
    unsigned int recvBuffCnt,
    IOControl *pIoControl,
    unsigned int sendTimeOutMs,
    unsigned int recvTimeOutMs,
    CmdHeader *cmdHead)
{
    int err = WT_ERR_CODE_OK;
    char chBuff[TX_BUFF_LEN] = { 0 };
    char *packgeBuff = nullptr;
    packgeBuff = chBuff;

    err = ProSendWrapperData(mode, fun, sendBuff, sendBuffCnt, packgeBuff, pIoControl, sendTimeOutMs);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    do
    {
        int dataLength = 0;
        int protocalDataLen = dataLength;
        err = ProcExchangeHeader(mode,
            fun,
            packgeBuff,
            pIoControl,
            recvTimeOutMs,
            &dataLength);
        if (WT_ERR_CODE_OK != err || dataLength <= 0)
        {
            break;
        }
        A_ASSERT(recvBuff);
        A_ASSERT(recvBuffCnt > 0);
        if (dataLength > 0 && recvBuffCnt > 0)
        {
            protocalDataLen = dataLength;
            int TotalBufLen = 0;
            for (int i = 0; i < recvBuffCnt; i++)
            {
                recvBuff[i].data_len = 0;
                TotalBufLen += recvBuff[i].buff_len;
            }
            for (int i = 0; i < recvBuffCnt; i++)
            {
                int recvLen = 0;
                int buf_size = dataLength >= recvBuff[i].buff_len ? recvBuff[i].buff_len : dataLength;
                if (recvBuff[i].chpHead && recvBuff[i].buff_len > 0)
                {
                    memset(recvBuff[i].chpHead, 0, buf_size);
                }
                recvLen = pIoControl->IORecv(recvBuff[i].chpHead, buf_size, recvTimeOutMs);
                if (recvLen != buf_size)
                {
                    Logger::WriteLog(eumLogType_Error, "select_rcv %d (mode %d fun %d): req %d , rcv %d\n", i, mode, fun, buf_size, recvLen);
                    err = WT_ERR_CODE_TIMEOUT;
                    goto func_exit;
                }
                recvBuff[i].data_len = recvLen;
                dataLength -= recvLen;
            }
            if (dataLength > 0)
            {
                Logger::WriteLog(eumLogType_Error, "buff size not enough (mode %d fun %d), buff size %d , rcv %d\n", mode, fun, TotalBufLen, protocalDataLen);
                const int packetSize = 1024;
                char tmpBuf[packetSize] = { 0 };
                int loopCnt = dataLength / packetSize;
                for (int i = 0; i < loopCnt; i++)
                {
                    int tmpLen = pIoControl->IORecv(tmpBuf, packetSize, recvTimeOutMs);
                    if (tmpLen <= 0)
                    {
                        break;
                    }
                }
                pIoControl->IOCleanBuff();
                err = WT_ERR_CODE_BUFFER_TOO_SHORT;
                if (1 == recvBuffCnt)
                {
                    recvBuff[0].data_len = protocalDataLen;
                }
                goto func_exit;
            }
        }
    } while (0);
func_exit:
    //dbg("err = %#X\n", err);
    if (cmdHead)
    {
        memcpy(cmdHead, packgeBuff, sizeof(CmdHeader));
    }
    return err;
}

int InstrumentHandle::Diagnose(
    DiagnoseSetting setting,
    char *diaInfo,
    unsigned int infoSize)
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::AnalyzeDiagnoseLog()
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SisoConnect(
    ConnectedUnit *conUnit,
    int connType)
{
    A_ASSERT(conUnit);
    int err = WT_ERR_CODE_OK;

    ConnectedUnit mConnUnit;
    memcpy(&mConnUnit, conUnit, sizeof(ConnectedUnit));
    //不关心子仪器的情况下，子仪器ID设为1；
    if (mConnUnit.SubTesterIndex == WT_SUB_TESTER_INDEX_AUTO)
    {
        //如果是Manager连接，子仪器ID设为0
        if (WT_CONNECT_TYPE_MANAGE == connType)
        {
            mConnUnit.SubTesterIndex = 0;
        }
        else
        {
            mConnUnit.SubTesterIndex = WT_SUB_TESTER_INDEX0;
        }

    }
    const char *plainText = m_pCryptology->GetPlainText(m_deviceType, connType);
    if (nullptr == plainText)
    {
        return WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    char plainStr[MAX_BUFF_SIZE] = { 0 };
    //sprintf(plainStr,"%s%d",plainText,connUnit->subTesterIndex);
    do
    {
        err = VerifyConnect(&mConnUnit, PORT_HOST, plainText, strlen(plainText) * sizeof(char), m_pIoControlVsa.get());
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
#ifndef LINUX
        if (WT_CONNECT_TYPE_FORCE == connType || WT_CONNECT_TYPE_NORMAL == connType)
        {
            sprintf(plainStr, "%s", m_pCryptology->GetPlainText(m_deviceType, WT_CONNECT_TYPE_MultiUser));
            err = VerifyConnect(&mConnUnit, PORT_HOST, plainStr, strlen(plainStr) * sizeof(char), m_pIoControlVsg.get());
            if (WT_ERR_CODE_OK != err)
            {
                break;
            }
            sprintf(plainStr, "%s", m_pCryptology->GetPlainText(m_deviceType, WT_CONNECT_TYPE_MultiUser));
            err = VerifyConnect(&mConnUnit, PORT_HOST, plainStr, strlen(plainStr) * sizeof(char), m_pIoControlMisc.get());
        }
        else
#endif
        {
            //指针不再重复，有对应的内存值而且不连接。只是在使用时设置为统一一个VSA连接
            SetVsaVsgLinkShared(HANDLE_SCENARIO_ALL_SHARED); //manger连接,monitor连接
        }

    } while (0);
    if (WT_ERR_CODE_OK != err)
    {
        DisconnectDevice();
    }
    return  err;
}

int InstrumentHandle::GetDefaultParameter(VsaParameter *vsaParam,
    VsaAvgParameter *avgParam,
    VsgParameter *vsgParam,
    VsgWaveParameter *waveParam,
    VsgPattern *vsgPattern)
{
    unsigned int queryIndex = 0;
    const int UnitMask = 0;
    const double SamplingFreq = m_maxSampleRate;    //默认采样率240MHz
    const double SampleTime = 2 * Ms;       //默认采样时间2ms
    const double CenterFreq = 2412 * MHz_API;     //默认中心频点2412MHz
    const double CenterFreq_2 = 0.0;
    const double FreqOffset = 0.0;
    const double TrigLevel = -31.0;
    const double TrigTimeout = 0.2;
    const double ExtPathLoss = 0.0;
    const double TrigPretime = 20 * Us;     //默认触发前置时间20us
    const double MaxIFGGap = 0.1;           //0.1s
    const double VsaTimeoutWaiting = 4.0;   //VSA默认资源等待超时时间为4s
    const double VsgTimeoutWaiting = 8.0;   //VSG默认资源等待超时时间为8s
    const double VsgPowerLevel = -10.0;     //VSG默认发送功率-10dBm
    const double VsaMaxPowerLevel = 0.0;   //VSA默认参考电平0dBm
    const double PowerOffset = 0.0;
    //VSA
    if (nullptr != vsaParam)
    {
        memset(vsaParam, 0, sizeof(VsaParameter));
        vsaParam->Freq = CenterFreq;
        vsaParam->Freq2 = CenterFreq_2;
        vsaParam->FreqOffset = FreqOffset;
        for (queryIndex = 0; queryIndex < WT_SUB_TESTER_INDEX_MAX; queryIndex++)
        {
            vsaParam->VsaUnitMask[queryIndex] = UnitMask;
            vsaParam->MaxPower[queryIndex] = VsaMaxPowerLevel;
            vsaParam->RfPort[queryIndex] = WT_PORT_RF1;
            vsaParam->ExtPathLoss[queryIndex] = ExtPathLoss;
            vsaParam->ExtPathLoss2[queryIndex] = ExtPathLoss;
        }
        vsaParam->ValidNum = 1;
        vsaParam->Demode = WT_DEMOD_11AG;
        vsaParam->TrigType = WT_TRIG_TYPE_FREE_RUN_API;
        vsaParam->TrigLevel = TrigLevel;
        vsaParam->TrigTimeout = TrigTimeout;
        vsaParam->SamplingFreq = SamplingFreq;
        vsaParam->SmpTime = SampleTime;
        vsaParam->TrigPretime = TrigPretime;
        vsaParam->MaxIFGGap = MaxIFGGap;
        vsaParam->TimeoutWaiting = VsaTimeoutWaiting;
    }
    if (nullptr != avgParam)
    {
        memset(avgParam, 0, sizeof(VsaAvgParameter));
        avgParam->AvgCount = 1;
        avgParam->AvgType = 0;
    }

    //VSG
    if (nullptr != vsgParam)
    {
        memset(vsgParam, 0, sizeof(VsgParameter));
        vsgParam->Freq = CenterFreq;
        vsgParam->Freq2 = CenterFreq_2;
        vsgParam->FreqOffset = FreqOffset;
        for (queryIndex = 0; queryIndex < WT_SUB_TESTER_INDEX_MAX; queryIndex++)
        {
            vsgParam->Power[queryIndex] = VsgPowerLevel;
            vsgParam->RfPort[queryIndex] = WT_PORT_RF1;
            vsgParam->VsgUnitMask[queryIndex] = UnitMask;
            vsgParam->ExtPathLoss[queryIndex] = ExtPathLoss;
            vsgParam->ExtPathLoss2[queryIndex] = ExtPathLoss;
        }
        vsgParam->ValidNum = 1;
        vsgParam->SamplingFreq = SamplingFreq;
        vsgParam->TimeoutWaiting = VsgTimeoutWaiting;
        memset(vsgParam->Reserved, 0, sizeof(vsgParam->Reserved));
    }
    if (nullptr != vsgPattern)
    {
        memset(vsgPattern, 0, sizeof(VsgPattern));
        vsgPattern->Repeat = 1;
    }
    if (nullptr != waveParam)
    {
        memset(waveParam, 0, sizeof(VsgWaveParameter));
    }

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::ClearSampRateFromFileFlag(void)
{
    m_VsgSampRateFromFileFlag = false;

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetSlaveTesterInfo(int slaveTesterID, TesterInfo *testerInfo)
{
    int err = WT_ERR_CODE_OK;
    A_ASSERT(testerInfo);
    CrypTesterInfo crypInfo;
    IOControl_TCP tmpIoControl;
    memset(&crypInfo, 0, sizeof(crypInfo));
    tmpIoControl.IOSetAliasName("GetSlaveTesterInfo");
    const char *plainText = m_pCryptology->GetPlainText(m_deviceType, WT_CONNECT_TYPE_MultiUser);
    do
    {
        err = VerifyConnect(&m_vecConnectUnit[slaveTesterID], PORT_HOST, plainText, strlen(plainText) * sizeof(char), &tmpIoControl);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        ExchangeBuff pstRecvBuff;
        pstRecvBuff.chpHead = (char *)&crypInfo;
        pstRecvBuff.buff_len = sizeof(CrypTesterInfo);
        pstRecvBuff.data_len = sizeof(CrypTesterInfo);

        err = ProExchange(0, CMD_GET_DEV_INFO, nullptr, 0, &pstRecvBuff, 1, &tmpIoControl, 1000, 3000);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        memcpy(testerInfo->IP, crypInfo.IP, IP_MAX_LEN);
        memcpy(testerInfo->SubMask, crypInfo.SubMask, IP_MAX_LEN);
        memcpy(testerInfo->GateWay, crypInfo.GateWay, IP_MAX_LEN);
        testerInfo->TesterType = TEST_TYPE_ENUM_WT448;
        memcpy(testerInfo->SN, crypInfo.SN, WT_SN_MAX_LEN);
        memcpy(testerInfo->Name, crypInfo.Name, WT_COM_MAX_LEN);
        memcpy(testerInfo->Mac, crypInfo.Mac, MAC_ADDR_MAX_LEN);
        memcpy(testerInfo->FwVersion, crypInfo.FwVersion, WT_COM_MAX_LEN);
        memcpy(testerInfo->ALGVersion, crypInfo.ALGVersion, WT_COM_MAX_LEN);
        testerInfo->BusiBoardCount = crypInfo.BusiBoardCount;
        memcpy(&testerInfo->BPInfo, &crypInfo.BPInfo, sizeof(BackPlaneInfo_API));
        memcpy(testerInfo->BusiBoardInfo, crypInfo.BusiBoardInfo, WT_MODULES_MAX_NUM * sizeof(BusinessBoardInfo_API));
        memcpy(testerInfo->ProduceDate, crypInfo.ProduceDate, WT_VER_DATA_LEN);

        CorrectionInstrumentType(testerInfo->IP, &testerInfo->TesterType);
        tmpIoControl.IODisconnect();
    } while (0);

    return err;
}

int InstrumentHandle::GetSlaveTesterLicense(int slaveTesterID,
    LicItemInfo_API *licInfo,
    int licenseMaxCount,
    int *licActualcount)
{
    if (slaveTesterID > m_vecConnectUnit.size())
    {
        return WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    int err = WT_ERR_CODE_OK;
    A_ASSERT(licInfo);
    A_ASSERT(licActualcount);
    do
    {
        if (string(m_TesterInfoList[0].IP) == string(m_vecConnectUnit[slaveTesterID].Ip))
        {
            //和主机IP一致，认为是同一台仪器。直接返回主机license
            err = GetLicense(licInfo, licenseMaxCount, licActualcount);
        }
        else
        {
            IOControl_TCP tmpIoControl;
            tmpIoControl.IOSetAliasName("GetSlaveTesterLicense");
            const char *plainText = m_pCryptology->GetPlainText(m_deviceType, WT_CONNECT_TYPE_MultiUser);

            //主机和从机IP不一致，多机MIMO。先连接从机，再获取license信息
            err = VerifyConnect(&m_vecConnectUnit[slaveTesterID], PORT_HOST, plainText, strlen(plainText) * sizeof(char), &tmpIoControl);
            if (WT_ERR_CODE_OK != err)
            {
                break;
            }

            err = GetLicense(licInfo, licenseMaxCount, licActualcount, &tmpIoControl);
            tmpIoControl.IODisconnect();
        }

    } while (0);


    return err;
}



IOControl *InstrumentHandle::GetUsableIOControler(int controlType)
{
#ifdef LINUX
    //只有一个socket连接
    switch (controlType)
    {
    case IOCONTROL_VSA:
    case IOCONTROL_VSG:
    case IOCONTROL_MISC:
        ENTER_LOCK(m_VsaSockLocker);
        return m_pIoControlVsa.get();
    default:
        return nullptr;
    }
#else
    switch (m_HandleScenario)
    {
    case HANDLE_SCENARIO_ALL_SHARED:
        switch (controlType)
        {
        case IOCONTROL_VSA:
        case IOCONTROL_VSG:
        case IOCONTROL_MISC:
            ENTER_LOCK(m_VsaSockLocker);
            return m_pIoControlVsa.get();
        default:
            return nullptr;
        }
    case HANDLE_SCENARIO_VSA_VSG_SHARED:
        switch (controlType)
        {
        case IOCONTROL_VSA:
        case IOCONTROL_VSG:
            ENTER_LOCK(m_VsaSockLocker);
            return m_pIoControlVsa.get();
        case IOCONTROL_MISC:
            ENTER_LOCK(m_MiscSockLocker);
            return m_pIoControlMisc.get();
        default:
            return nullptr;
        }
    default:
        switch (controlType)
        {
        case IOCONTROL_VSA:
            ENTER_LOCK(m_VsaSockLocker);
            return m_pIoControlVsa.get();
        case IOCONTROL_VSG:
            ENTER_LOCK(m_VsgSockLocker);
            return m_pIoControlVsg.get();
        case IOCONTROL_MISC:
            ENTER_LOCK(m_MiscSockLocker);
            return m_pIoControlMisc.get();
        default:
            return nullptr;
        }
    }
#endif
}

void InstrumentHandle::ResleaseCurrIOControler(int controlType)
{
#ifdef LINUX
    //只有一个socket连接
    switch (controlType)
    {
    case IOCONTROL_VSA:
    case IOCONTROL_VSG:
    case IOCONTROL_MISC:
        EXIT_LOCK(m_VsaSockLocker);
        break;
    default:
        break;
    }
#else
    switch (m_HandleScenario)
    {
    case HANDLE_SCENARIO_ALL_SHARED:
        switch (controlType)
        {
        case IOCONTROL_VSA:
        case IOCONTROL_VSG:
        case IOCONTROL_MISC:
            EXIT_LOCK(m_VsaSockLocker);
            break;
        default:
            break;
        }
    case HANDLE_SCENARIO_VSA_VSG_SHARED:
        switch (controlType)
        {
        case IOCONTROL_VSA:
        case IOCONTROL_VSG:
            EXIT_LOCK(m_VsaSockLocker);
            break;
        case IOCONTROL_MISC:
            EXIT_LOCK(m_MiscSockLocker);
            break;
        default:
            break;
        }
    default:
        switch (controlType)
        {
        case IOCONTROL_VSA:
            EXIT_LOCK(m_VsaSockLocker);
            break;
        case IOCONTROL_VSG:
            EXIT_LOCK(m_VsgSockLocker);
            break;
        case IOCONTROL_MISC:
            EXIT_LOCK(m_MiscSockLocker);
            break;
        default:
            break;
        }
    }
#endif
}

int InstrumentHandle::Exchange(
    int mode,
    int fun,
    ExchangeBuff *sendBuff,
    unsigned int sendBuffCnt,
    ExchangeBuff *recvBuff,
    unsigned int recvBuffCnt,
    int controlType,
    unsigned int sendTimeOutMs /*= 1000*/,
    unsigned int recvTimeOutMs /*= 3000*/,
    CmdHeader *cmdHead)
{
    IOControl *pIoControl = GetUsableIOControler(controlType);
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }

    CmdHeader tmpHeader;
    memset(&tmpHeader, 0, sizeof(tmpHeader));
    int err = ProExchange(mode, fun, sendBuff, sendBuffCnt, recvBuff, recvBuffCnt, pIoControl, sendTimeOutMs, recvTimeOutMs, &tmpHeader);

    if (cmdHead)
    {
        memcpy(cmdHead, &tmpHeader, sizeof(tmpHeader));
    }

    if (IOCONTROL_VSA == controlType)
    {
        Logger::PrintDebug(__FUNCTION__, __LINE__, "VSA fun = 0x%X, exchange err=%d(0x%X), id=%d\n",
            fun,
            err,
            err,
            tmpHeader.SerialNum);
    }

    if (WT_ERR_CODE_OK != err)
    {
        switch (controlType)
        {
        case IOCONTROL_VSA:
            Logger::PrintDebug(__FUNCTION__, __LINE__, "VSA fun = 0x%X, exchange err=%d(0x%X), socket status %s(%d)\n",
                fun,
                err,
                err,
                (false == pIoControl->IOGetConnectState() ? "Disconnect" : "OK"),
                pIoControl->IOGetErrorCode());
            break;
        case IOCONTROL_VSG:
            Logger::PrintDebug(__FUNCTION__, __LINE__, "VSG fun = 0x%X, exchange err=%d(0x%X), socket status %s(%d)\n",
                fun,
                err,
                err,
                (false == pIoControl->IOGetConnectState() ? "Disconnect" : "OK"),
                pIoControl->IOGetErrorCode());
            break;
        default:
            Logger::PrintDebug(__FUNCTION__, __LINE__, "MISC fun = 0x%X, exchange err=%d(0x%X), socket status %s(%d)\n",
                fun,
                err,
                err,
                (false == pIoControl->IOGetConnectState() ? "Disconnect" : "OK"),
                pIoControl->IOGetErrorCode());
            break;
        }
    }
    ResleaseCurrIOControler(controlType);
    return err;
}


int InstrumentHandle::ProRecvSpeciallyCmdData(
    int fun,
    ExchangeBuff *recvBuff,
    unsigned int recvBuffCnt,
    IOControl *pIoControl,
    unsigned int recvTimeOutMs/*=3000*/)
{
    A_ASSERT(recvBuff && pIoControl);

    char head_buff[MAX_BUFF_SIZE] = { 0 };
    int rcvCnt = 0;
    unsigned int cnt = 0;
    int iret = WT_ERR_CODE_GENERAL_ERROR;
    A_ASSERT(pIoControl);
    do
    {
        rcvCnt = pIoControl->IORecv(head_buff, m_pCryptology->GetProCmdHeadSize(), recvTimeOutMs);
        if (rcvCnt <= 0)
        {
            Logger::WriteLog(eumLogType_Warning, "Rcv head timeout\n");
            iret = WT_ERR_CODE_TIMEOUT;
            break;
        }

        const char *data = m_pCryptology->GetProCmdStartFlag(nullptr, 0);
        char *pchTmp = (char *)&data;
        if (nullptr == pchTmp)
        {
            iret = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        for (unsigned int i = 0; i < rcvCnt; i++)
        {
            if (head_buff[i] == pchTmp[i])
            {
                cnt++;
                if (m_pCryptology->GetProCmdStartFlagSize() == cnt)
                {
                    // 找到协议头 start_flag
                    break;
                }

                continue;
            }
            else if (head_buff[i] == pchTmp[0])
            {
                cnt = 1;
            }
            else
            {
                cnt = 0;
            }
        }
        // 找到协议头 start_flag
        if (m_pCryptology->GetProCmdStartFlagSize() == cnt)
        {
            if (m_pCryptology->GetFunFromCmdHead(head_buff) != fun)
            {
                continue;
            }
            else
            {
                iret = WT_ERR_CODE_OK;
                break;
            }

        }
    } while (0);

    if (WT_ERR_CODE_OK == iret)
    {
        unsigned int dataLength = m_pCryptology->GetCmdDataLength(head_buff);

        unsigned int recvDataLength = 0;
        for (unsigned int i = 0; i < recvBuffCnt; i++)
        {
            recvDataLength += recvBuff[i].buff_len;
        }
        m_RealRecvDataLen = dataLength;
        if (recvDataLength < dataLength)
        {
            iret = WT_ERR_CODE_BUFFER_TOO_SHORT;
        }
        else
        {
            rcvCnt = pIoControl->IORecv(recvBuff->chpHead, dataLength, recvTimeOutMs);
            iret = rcvCnt > 0 ? WT_ERR_CODE_OK : WT_ERR_CODE_TIMEOUT;
        }
    }
    else
    {
        pIoControl->IOCleanBuff();
    }
    return iret;
}

int InstrumentHandle::ProGetSpeciallyCmdDataSize(
    int fun,
    unsigned int *dataSize,
    IOControl *pIoControl,
    unsigned int recvTimeOutMs /*=3000*/)
{
    A_ASSERT(dataSize && pIoControl);

    char head_buff[MAX_BUFF_SIZE] = { 0 };
    int rcvCnt = 0;
    unsigned int cnt = 0;
    int iret = WT_ERR_CODE_GENERAL_ERROR;
    A_ASSERT(pIoControl);
    do
    {
        int bufLen = m_pCryptology->GetProCmdHeadSize();
        rcvCnt = pIoControl->IORecv(head_buff, bufLen, recvTimeOutMs);
        if (rcvCnt <= 0)
        {
            Logger::WriteLog(eumLogType_Warning, "Rcv head timeout\n");
            iret = WT_ERR_CODE_TIMEOUT;
            break;
        }

        const char *data = m_pCryptology->GetProCmdStartFlag(nullptr, 0);
        char *pchTmp = (char *)&data;
        if (nullptr == pchTmp)
        {
            iret = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        for (unsigned int i = 0; i < rcvCnt; i++)
        {
            if (head_buff[i] == pchTmp[cnt])
            {
                cnt++;
                if (m_pCryptology->GetProCmdStartFlagSize() == cnt)
                {
                    // 找到协议头 start_flag
                    break;
                }

                continue;
            }
            else if (head_buff[i] == pchTmp[0])
            {
                cnt = 1;
            }
            else
            {
                cnt = 0;
            }
        }
        // 找到协议头 start_flag
        if (m_pCryptology->GetProCmdStartFlagSize() == cnt)
        {
            if (m_pCryptology->GetFunFromCmdHead(head_buff) != fun)
            {
                continue;
            }
            else
            {
                iret = WT_ERR_CODE_OK;
                break;
            }

        }
    } while (0);
    if (WT_ERR_CODE_OK == iret)
    {
        *dataSize = m_pCryptology->GetCmdDataLength(head_buff);
    }
    else
    {
        pIoControl->IOCleanBuff();
    }
    return iret;
}

int InstrumentHandle::ProGetSpeciallyCmdData(
    int fun,
    ExchangeBuff *recvBuff,
    unsigned int recvBuffCnt,
    IOControl *pIoControl,
    unsigned int recvTimeOutMs /*= 3000*/)
{
    A_ASSERT(recvBuff && pIoControl);

    unsigned int recvDataLength = 0;
    for (unsigned int i = 0; i < recvBuffCnt; i++)
    {
        recvDataLength += recvBuff[i].buff_len;
    }

    int rcvCnt = pIoControl->IORecv(recvBuff->chpHead, recvDataLength, recvTimeOutMs);

    return rcvCnt > 0 ? WT_ERR_CODE_OK : WT_ERR_CODE_TIMEOUT;
}

int InstrumentHandle::SwitchMode(int targetMode)
{
    m_currTestMode = targetMode;
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetTesterErrorCode(
    int *errCode,
    char *errMsg,
    unsigned int errMsgSize)
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetFileSampleFreq(double *SampleFreq)
{
    if (m_tmpSampleFreq > MHz_API)
    {
        *SampleFreq = m_tmpSampleFreq;
    }
    else
    {
        *SampleFreq = m_tmpSampleFreq * MHz_API;    //单位转化为Hz
    }
    return WT_ERR_CODE_OK;
}


int InstrumentHandle::GetCurrAverageCount(int *count)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)count;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    return Exchange(0, CMD_GET_CUR_AVG_CNT, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);
}

int InstrumentHandle::SetMasterMode(
    int vsaMasterMode,
    int vsgMasterMode)
{
    m_vsaMasterMode = vsaMasterMode;
    m_vsgMasterMode = vsgMasterMode;
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::RecvGUIDataAndSave(
    int controlType,
    unsigned int dataSize,
    const char *saveDir)
{
    int err = WT_ERR_CODE_OK;
    size_t guiFileInfoSize = MAX_NAME_SIZE + 4;
    size_t guiBuffSize = SUP_MAX_BUFF_SIZE * 5;
    char guiFileInfo[MAX_BUFF_SIZE] = { 0 };
    char guiFileName[MAX_NAME_SIZE] = { 0 };

    unsigned int currRecvSize = 0;
    char basename[MAX_NAME_SIZE] = { 0 };
    unique_ptr<char[]>guiFileBuff(new (std::nothrow)char[guiBuffSize]);

    if (nullptr == guiFileBuff.get())
    {
        err = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
        goto func_exit;
    }


    while (currRecvSize < dataSize)
    {
        //先收取名字和文件大小
        err = ProRecv(controlType, guiFileInfo, guiFileInfoSize, 3000);
        if (err <= 0)
        {
            err = WT_ERR_CODE_TIMEOUT;
            break;
        }
        currRecvSize += guiFileInfoSize;
        memcpy(guiFileName, guiFileInfo, MAX_NAME_SIZE);
        u32 tmpDataSize = 0;
        memcpy(&tmpDataSize, (guiFileInfo + MAX_NAME_SIZE), sizeof(tmpDataSize));
        if (tmpDataSize > guiBuffSize)
        {
            //realloc memory buffer
            guiFileBuff.reset(new (std::nothrow) char[tmpDataSize]);
            if (nullptr == guiFileBuff.get())
            {
                err = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                break;
            }
            guiBuffSize = tmpDataSize;
        }
        err = ProRecv(controlType, guiFileBuff.get(), tmpDataSize, 3000);
        if (err <= 0)
        {
            err = WT_ERR_CODE_TIMEOUT;
            break;
        }
        currRecvSize += tmpDataSize;

        if (1)
        {
            const char *path = saveDir;
            const char *last = path + strlen(path) - 1;
            if (*last == '/' || *last == '\\')
            {
                last--;
            }
            strncpy(basename, path, last - path + 1);

            std::string guiSaveFileName = std::string(basename) + std::string("/") + std::string(guiFileName);
            MakeFileDirExsit((char *)guiSaveFileName.c_str(), MAX_NAME_SIZE);
            FILE *fp = fopen(guiSaveFileName.c_str(), "wb");
            if (nullptr == fp)
            {
                err = WT_ERR_CODE_GENERAL_ERROR;
                break;
            }
            int writeSize = fwrite(guiFileBuff.get(), 1, tmpDataSize, fp);
            fclose(fp);
        }

        err = WT_ERR_CODE_OK;
    }
func_exit:

    return err;
}

int InstrumentHandle::ProRecv(
    int controlType,
    char *recvBuff,
    unsigned int buffLength,
    unsigned int timeOutMs)
{
    IOControl *pIoControl = GetUsableIOControler(controlType);
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }
    int err = pIoControl->IORecv(recvBuff, buffLength, timeOutMs);
    ResleaseCurrIOControler(controlType);
    return err;
}

int InstrumentHandle::ProRecv_V2(
    int controlType,
    char *recvBuff,
    unsigned int buffLength,
    unsigned int timeOutMs)
{
    IOControl *pIoControl = GetUsableIOControler(controlType);
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }
    int len = 0;
    char *ptr = recvBuff;
    while (true)
    {
        len = pIoControl->IORecv(ptr, 1, timeOutMs);
        if (len < 1 || ptr - recvBuff >= buffLength)
        {
            break;
        }
        ptr++;
    }
    len = ptr - recvBuff;

    ResleaseCurrIOControler(controlType);
    return len;
}

int InstrumentHandle::ProSend(
    int controlType,
    const char *sendBuff,
    unsigned int buffLength,
    unsigned int timeOutMs)
{
    IOControl *pIoControl = GetUsableIOControler(controlType);
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }
    int err = pIoControl->IOSend(sendBuff, buffLength, timeOutMs);
    ResleaseCurrIOControler(controlType);
    return err;
}

void InstrumentHandle::MakeFileDirExsit(
    char *fileName,
    unsigned int fileNameSize)
{
    UsualKit::LinuxDirToWindir(fileName, fileNameSize);
    char tmpFileName[MAX_NAME_SIZE] = { 0 };
    memcpy(tmpFileName, fileName, strlen(fileName) + 1);
    char *pos = strrchr(tmpFileName, '\\');
    if (nullptr == pos)
    {
        return;
    }
    char fileDir[MAX_NAME_SIZE] = { 0 };
    memcpy(fileDir, tmpFileName, pos - tmpFileName + 1);

    UsualKit::MakeDirs(fileDir);
}

int InstrumentHandle::ProGetSpeciallyAckDataSize(int mode,
    int fun,
    unsigned int *dataSize,
    ExchangeBuff *sendBuff,
    unsigned int sendBuffCnt,
    int controlType,
    unsigned int sendTimeOutMs /*= 1000*/,
    unsigned int recvTimeOutMs /*= 3000*/,
    bool careResult)
{
    IOControl *pIoControl = GetUsableIOControler(controlType);
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }

    int err = WT_ERR_CODE_OK;
    char chBuff[TX_BUFF_LEN] = { 0 };
    char *packgeBuff = nullptr;
    int dataLength = 0;
    packgeBuff = chBuff;
    do
    {
        err = ProSendWrapperData(mode, fun, sendBuff, sendBuffCnt, packgeBuff, pIoControl, sendTimeOutMs);
        if (WT_ERR_CODE_OK != err)
        {
            goto func_exit;
        }
        dataLength = 0;
        err = ProcExchangeHeader(mode,
            fun,
            packgeBuff,
            pIoControl,
            recvTimeOutMs,
            &dataLength);
    } while (0);

    if (dataSize && WT_ERR_CODE_OK == err && true == careResult)
    {
        *dataSize = dataLength;
    }
    else if (dataSize && false == careResult)
    {
        *dataSize = dataLength;
    }
func_exit:
    ResleaseCurrIOControler(controlType);
    return err;
}

int InstrumentHandle::ProGetSpeciallyAckDataSize_LockFree(int mode, int fun, unsigned int * dataSize, ExchangeBuff * sendBuff, unsigned int sendBuffCnt, IOControl *pIoControl, unsigned int sendTimeOutMs, unsigned int recvTimeOutMs, bool careResult)
{
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }

    int err = WT_ERR_CODE_OK;
    char chBuff[TX_BUFF_LEN] = { 0 };
    char *packgeBuff = nullptr;
    int dataLength = 0;
    packgeBuff = chBuff;
    do
    {
        err = ProSendWrapperData(mode, fun, sendBuff, sendBuffCnt, packgeBuff, pIoControl, sendTimeOutMs);
        if (WT_ERR_CODE_OK != err)
        {
            goto func_exit;
        }
        dataLength = 0;
        err = ProcExchangeHeader(mode,
            fun,
            packgeBuff,
            pIoControl,
            recvTimeOutMs,
            &dataLength);
    } while (0);

    if (dataSize && WT_ERR_CODE_OK == err && true == careResult)
    {
        *dataSize = dataLength;
    }
    else if (dataSize && false == careResult)
    {
        *dataSize = dataLength;
    }
func_exit:
    return err;
}

int InstrumentHandle::ProRecv_LockFree(IOControl *pIoControl, char * recvBuff, unsigned int buffLength, unsigned int timeOutMs)
{
    if (nullptr == pIoControl)
    {
        return WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
    }
    int err = pIoControl->IORecv(recvBuff, buffLength, timeOutMs);
    return err;
}


int InstrumentHandle::CheckPnClockRate(int clockRate)
{
    const int clockRateSupport[] = { 1, 2, 4, 5, 8, 10, 20 };
    bool validClockRate = false;

    //目前支持的clock rate
    for (int i = 0; i < ARRAYSIZE(clockRateSupport); i++)
    {
        if (clockRateSupport[i] == clockRate)
        {
            validClockRate = true;
            break;
        }
    }

    if (!validClockRate)
    {
        clockRate = 1;//default clock rate
    }

    return clockRate;
}

int InstrumentHandle::SetVsaVsgLinkShared(int SharedType)
{
#ifdef LINUX
    return WT_ERR_CODE_OK;
#else
    m_HandleScenario = SharedType;
    return WT_ERR_CODE_OK;
#endif
}

void InstrumentHandle::memdump(const char *filename, int id)
{
    stringstream dmp;
    const string newLine("\r\n");

    if (1)
    {
        dmp << "VsaParameter Serial Number=" << m_VsaParamSerialNum << "(" << std::hex << std::showbase << m_VsaParamSerialNum << ")" << std::dec << newLine;
        dmp << "VsgParameter Serial Number=" << m_VsgParamSerialNum << "(" << std::hex << std::showbase << m_VsgParamSerialNum << ")" << std::dec << newLine;
        dmp << "VsgWaveParameter Serial Number=" << m_VsgWaveSerialNum << "(" << std::hex << std::showbase << m_VsgWaveSerialNum << ")" << std::dec << newLine;
        dmp << "PN Serial Number=" << m_VsgPnSerialNum << "(" << std::hex << std::showbase << m_VsgPnSerialNum << ")" << std::dec << newLine;
        dmp << "vsgAc8080Flag=" << m_vsgAc8080Flag << newLine;
        dmp << "vsaAc8080Flag=" << m_vsaAc8080Flag << newLine;
        dmp << "vsgPnRefFlag=" << m_vsgPnRefFlag << newLine;
        dmp << "VSA IO status=" << m_pIoControlVsa->IOGetConnectState() << newLine;
        dmp << "VSG IO status=" << m_pIoControlVsg->IOGetConnectState() << newLine;
        dmp << "MISC IO status=" << m_pIoControlMisc->IOGetConnectState() << newLine;

        PrintParam obj;
        obj.SetFileName(filename);
        obj.SetTitle("Common status");
        obj.PrintHeader();
        obj.PrintAdd(dmp.str());
    }

    if (1)
    {
        PrintParam_VSA obj_1(&m_vsaParam, string("VsaParameter"), filename);
        obj_1.RUN();
        PrintParam_VSA obj_2(&m_VsaParameterBack, string("VsaParameterBack"), filename);
        obj_2.RUN();
    }

    if (1)
    {
        PrintParam_VSA_Ext obj(&m_vsaExternParam, string("ExtendVsaParameter"), filename);
        obj.RUN();
    }

    if (1)
    {
        PrintParam_VSG obj_1(&m_vsgParam, string("VsgParameter"), filename);
        obj_1.RUN();
        PrintParam_VSG obj_2(&m_VsgParameterBack, string("VsgParameterBack"), filename);
        obj_2.RUN();
    }

    if (1)
    {
        PrintParam_VSG_Ext obj(&m_vsgExternParam, string("ExtendVsgParameter"), filename);
        obj.RUN();
    }

    if (1)
    {
        PrintParam_VsgWaveParameter obj_1(&m_VsgWaveParameter, string("VsgWaveParameter"), filename);
        obj_1.RUN();
        PrintParam_VsgWaveParameter obj_2(&m_VsgWaveParameterBack, string("VsgWaveParameterBack"), filename);
        obj_2.RUN();
    }

    if (1)
    {
        PrintParam_PnItem obj_1(&m_vecPnItemHead, &m_vecPnItem, string("Current"), filename);
        obj_1.RUN();
        PrintParam_PnItem obj_2(&m_vecPnItemHeadBack, &m_vecPnItemBack, string("Previous"), filename);
        obj_2.RUN();
    }

    if (1)
    {
        PrintParam_AnalyzeParam obj_1((AnalyzeParam *)m_AnalyzeParamInfo[0].analyzeParam, m_vsaParam.Demode, string("VSA analyzeParam"), filename);
        obj_1.RUN();
        PrintParam_AnalyzeParam obj_2((AnalyzeParam *)m_AnalyzeParamInfo[1].analyzeParam, m_vsgAnalyzeType, string("VSG analyzeParam"), filename);
        obj_2.RUN();
    }
}
#define Bufize (64)
int InstrumentHandle::GetSyncSource(void* pnParameters)
{
	int iRet = WT_ERR_CODE_OK;
	int syncSeqBuf[Bufize * sizeof(int)] = { 0 };
	int synSeqLen = 0;
	iRet = GetSynSeq(syncSeqBuf, &synSeqLen);
	if (iRet)
	{
		iRet = WT_VSG_ALG_BASE_ERROR;
	}
	GenWaveGleStruct* pInconfig = static_cast<GenWaveGleStruct*>(pnParameters);
	memcpy(pInconfig->GlePacketSet.SyncSeq, syncSeqBuf, synSeqLen);
	for (int i = 0; i < synSeqLen; i++)
	{
#ifdef LINUX
		WTLog::Instance().WriteLog(LOG_DEBUG, "[  GetSynSeq    ]SyncSeq[%d] = %d\n\n\n", i, pInconfig->GlePacketSet.SyncSeq[i]);
#endif
	}


	return iRet;
}

int InstrumentHandle::GetSynSeq(int* m_synSeq, int *m_synSeqLen)
{
	ExchangeBuff pstRecvBuff;
	pstRecvBuff.chpHead = (char*)m_synSeq;
	pstRecvBuff.buff_len = Bufize * sizeof(int);
	pstRecvBuff.data_len = Bufize * sizeof(int);

	int iRet = Exchange(0, CMD_GEN_VSG_GEN_SLE_SYNSQR, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);
	*m_synSeqLen = pstRecvBuff.data_len;

	return iRet;

}
