//*****************************************************************************
//File: devlib.h
//Describe:硬件操作控制层
//Author：wangzhenglong
//Date: 2016.8.1
//*****************************************************************************

#ifndef __DEVLIB_H__
#define __DEVLIB_H__

#include <map>
#include <functional>
#include <fstream>
#include <jsoncpp/json/json.h>
#include <unistd.h>
#include <mutex>
#include <condition_variable>

#include "../wtypes.h"
#include "ioctlcmd.h"
#include "wt-calibration.h"
#include "devdef.h"
#include "wtev++.h"
#include "wtcal.h"
#include "pll.h"
#include "wtsecurelib.h"

#define VSA_ORIGIN_DATA_ENABLE_REG (0x7D << 2) // 蜂窝采集时是否 追加采集原始数据(240M)
#define VSA_ORIGIN_DATA_ENABLE_VAl (0x1)
#define VSA_ORIGIN_DATA_DISABLE_VAl (0x0)

class DevBase;
extern "C" const char *GetBuildDate();   //获取软件的编译时间

//硬件设备控制
class DevLib
{
public:
    using CmdFunctor = std::function<int(int, WT_DEV_TYPE, int, int &)>;            //器件查询设置接口类型
    using CmdFunctorDouble = std::function<int(int, WT_DEV_TYPE, int, double &)>;   //器件查询设置接口类型

    struct CmdWRCallBack
    {
        CmdFunctor WriteFunc;                               //器件设置接口
        CmdFunctor ReadFunc;                                //器件查询接口
    };

    struct CmdWRDoubleCallBack
    {
        CmdFunctorDouble WriteFunc;                          //器件设置接口
        CmdFunctorDouble ReadFunc;                           //器件查询接口
    };

    //*****************************************************************************
    //设置共本振模式，共用VSG的LMX2820
    //参数[IN] : ModId：模块ID
    //参数[IN] : Data：0为共本振，1为非共本振
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetLOComMode(int ModId, int Data);
    //*****************************************************************************
    //获取共本振模式，共用VSG的LMX2820
    //参数[IN] : ModId：模块ID
    //参数[OUT] : Data：0为共本振，1为非共本振
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetLOComMode(int ModId, int &Data);

    int ReSetLOComMode();

    //*****************************************************************************
    //设置VSG射频板boost开关
    //参数[IN] ： ModId：模块ID  Status:0关 1开
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetBoostStatus(int ModId, int Status);

    //*****************************************************************************
    //设置VSA射频板LNA状态开关
    //参数[IN] ： ModId：模块ID  Status:0关 1开
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetVsaLNAStatus(int ModId, int Status);


    //*****************************************************************************
    //设置DevLib对象的VSA/VSG单元掩码，此接口需在DevLib类的所有接口之前被调用一次
    //参数[IN] : VSAMask：VSA单元掩码    VSGMask：VSG单元掩码
    //返回值: 无
    //*****************************************************************************
    static void SetMask(int VSAMask, int VSGMask);

    //*****************************************************************************
    //获取DevLib对象
    //参数[IN] : 无
    //返回值: DevLib对象指针
    //*****************************************************************************
    static DevLib &Instance(void);

    //*****************************************************************************
    //获取硬件版本号
    //参数[IN] : ModId：模块ID  DevType:单元板类型
    //参数[OUT] : Version 硬件版本号
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetHardwareVersion(int ModId, WT_DEV_TYPE DevType, int &Version);

    //*****************************************************************************
    //获取射频板硬件版本号
    //参数[IN] : ModId：模块ID  DevType:单元板类型
    //参数[OUT] : Version 射频板硬件版本号
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetBusiFPGAVersion(int ModId, WT_DEV_TYPE DevType, int &Version);

    //*****************************************************************************
    //获取开关板硬件版本号
    //参数[OUT] : Version 开关板硬件版本号
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwbHwVersion(int &Version);

    int GetTesterHwType(int &TesterType);

    int GetRevisionId(int ModId, WT_DEV_TYPE DevType, int &RevisionId);

    int GetRfHwVersion(int ModId, WT_DEV_TYPE DevType, int &Version);

    int GetLoHwVersion(int ModId, WT_DEV_TYPE DevType, int &Version);

    int GetUBCount(WT_DEV_TYPE DevType, int &Count);

    void VSACreate(int Mask);

    void VSGCreate(int Mask);

    friend void* MultThreadVSACreate(void * args);

    friend void* MultThreadVSGCreate(void * args);

    // 获取CMD设置的独占设备的标志位
    int GetOccupyFlag();

    //*****************************************************************************
    //初始化业务板硬件参数
    //参数[IN] : 无
    //返回值: 成功或者错误码
    //*****************************************************************************
    int DevLibInit(void);

    int CreateBusiObj(void);

    int VSGDacStatusMonitor(const wtev::loop_ref &loop);

    void VsgDacTimerCb(wtev::timer &watcher, int revents);

    int ShowHWInfo(void);

    int CheckbackPlaneInfo(bool Cmd = false);

    int RecordHWInfo(void);

    void DevLibRelease();

    int SetUnitBoardDevData(int ModId, WT_DEV_TYPE DevType, int DevId, int ChipId, int Addr, void *Arg, int Length);

    int GetUnitBoardDevData(int ModId, WT_DEV_TYPE DevType, int DevId, int ChipId, int Addr, void *Arg, int &Length);

    int GetDevVoltCnt(int &VoltCnt, int &Length);

    int GetDevVoltInfo(void *Data, char *VoltBuf, int &VoltCnt, int &Length);
    //=========UNIT BOAET============
    static int GetUnitBoardModNum(WT_DEV_TYPE DevType);

    int GetUBVoltValue(int ModId, WT_DEV_TYPE DevType, double &TempValue);

    int GetUBTempValue(int ModId, WT_DEV_TYPE DevType, double &TempValue);

    int SetUnitBoardPciDelay(int ModId, WT_DEV_TYPE DevType, int WriteDelay, int ReadDelay);

    //*****************************************************************************
    //直接写寄存器数据,不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：寄存器地址 Data: 寄存器数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteDirectReg(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //直接读寄存器数据,不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
    //参数[IN] : ModId：模块ID  DevType:单元板类型 Addr：寄存器地址
    //参数[OUT] : Data: 寄存器数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadDirectReg(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data);
    
    //==========BUSI BOARD===========
    int GetAllBusiBoardInfo(std::vector<BusinessBoardUnitInfo> &AllBusiBoardInfo);

    int GetRFTemperatureAverage(int ModId, WT_DEV_TYPE DevType, double &TempAverage);

    int GetCompleteClrStatus(int ModId, WT_DEV_TYPE DevType, int &Status);

    int GetRFTemperature(int ModId, WT_DEV_TYPE DevType, int TempId, double &TempValue);

    //*****************************************************************************
    //清除驱动里缓存的工作模式
    //参数[IN] ： ModId：模块ID  DevType:单元板类型
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ClearWorkMode(int ModId, WT_DEV_TYPE DevType);

    int SetRFPort(int ModId, WT_DEV_TYPE DevType, int Port, int State);

    int BaseFpgaUpgrade(int ModId, WT_DEV_TYPE DevType, std::string Path, int NotProgram = 0);
    int BaseFpgaUpgrade(int ModId, WT_DEV_TYPE DevType, unsigned char *pData, unsigned int Len, int NotProgram = 0);

    int SetDebugAtt(int ModId, WT_DEV_TYPE DevType, int Index, int Data);

    int SetATTCalConfig(int ModId, WT_DEV_TYPE DevType, ATTCalConfigType &Config);

    int SetExtMode(int ModId, WT_DEV_TYPE DevType, ExtModeType ExtMode);

    int ClearExtMode(int ModId);

    int LdpcCompute(int ModId, LdpcParamType *LdpcParam, int Count);

    int BccCompute(int ModId, const BccParamType &BccParam);
    //============VSA BOARD=====================
    int VSASetConfig(int ModId, const VSAConfigType &VSAConfig, Rx_Parm &RXParm);

    // 设置到工作点状态
    int VSASetWorkPointConfig(int ModId, const VSAConfigType &VSAConfig, Rx_Parm &RXParm);

    int VSAStart(int ModId, int Mode = WT_START_MODE_NORMAL);

    int VSAStop(int ModId);

    int VSAFinish(int ModId);

    int VSASetCaptureOriDataSample(int ModId, VSAConfigType &VsaConfig);

    int VSACaptureOriData(int ModId, void *pBuf, int Size, int ExtraSmpOffset);

    int VSACaptureData(int ModId, void *pBuf, int Size);

    int VSASetCalConfig(int ModId, const Rx_Parm &RXParm);

    int VSADown(int ModId);

    int VSAGetStatus(int ModId);

    int VSAGetGainParameter(int ModId, Rx_Gain_Parm &GainParm);

    int VSASetCalibrationFlat(int Enable);

    int TBTApModeStart(int ModId, int VsgUnitSel = 0);

    int VSASetTBTStaParam(int ModId, int Delay);

    int VSAGetIQCode(int ModId, int &GainCode);

    int VSAGetResultSIFS(int ModId, std::vector<double> &SIFS);
    //============VSG BOARD================
    int VSGStart(int ModId, int Mode = WT_START_MODE_NORMAL);

    int VSGStop(int ModId);

    int VSGDown(int ModId);

    int VSGFinish(int ModId);

    int VSGSetConfig(int ModId, const VSGConfigType &VSGConfig, Tx_Parm &TXParm);
    int SetfastVsgPower(int ModId, double Power);

    int SetVsaFreqAndPower(int ModId,VSAConfigType &VSAConfig);

    // 设置到工作点状态
    int VSGSetWorkPointConfig(int ModId, const VSGConfigType &VSGConfig, Tx_Parm &TXParm);

    int VSGSetCalConfig(int ModId, const Tx_Parm &TXParm);

    int VSGGetStatus(int ModId);

    int VSGSetPNItem(int ModId, const std::vector<RfPnItem> &PnItemVector, double ResamppleFreq);

    int VSGGetGainParameter(int ModId, Tx_Gain_Parm &GainParm);

    int VSGSetIfgCtrlEnable(int Status);

    //TBT
    int VSGTBTtaStart(int ModId, int DevMode);

    int VSGSetTBTStaParam(int ModId, TBTStaParamType StaConfig);
    //=========BACK PLANE============
    int GetDevClkState(int &ClkState);

    int SetErrorLed(int Status = LED_STATUS_ON, int ErrorCode = -1);

    int SetPortLedOff();

    int GetBackPlaneInfo(BackPlaneUnitInfo &BPInfo);

    int WTCryptoMemVerify(CryptoMemInfoType &CMInfo);

    int SetLedStatus(WT_LED_INDEX_E LedId, WT_LED_STATUS Status);

    int GetSwbPortTemperater(int Port, double &TempValue);

    int GetSBType(int &SBType);

    //根据模块类型及端口获取要分配的单元号
    int GetModId(WT_DEV_TYPE DevType, int RFPort, int &ModId);

    //根据端口获取开关板ID
    int GetSwitchId(int RFPort, int &SwitchId);

    int IsSwbExist(int SwitchId, int &Exist);

    //*****************************************************************************
    //获取仪器(用于风扇调速的)温度
    //参数[OUT] : 无关
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetDevAvgTemp(double &DevTempValue);

    //*****************************************************************************
    //设置参与调速的风扇的速度
    //参数[IN] : SpeedLevel:风扇速度
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetManagerFanSpeed(int  SpeedLevel);

    //*****************************************************************************
    //禁止/使能 风扇调速功能
    //参数[IN] : Enable:使能开关
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ManagerFanSpeedSwitch(int Enable);

    //*****************************************************************************
    //设置风扇速度
    //参数[IN] : FanId:风扇编号(WT_FAN_ID_E)   Speed：速度
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetFanSpeed(int FanId, const int Speed);

    //*****************************************************************************
    //获取风扇速度
    //参数[OUT] : FanId:风扇编号(WT_FAN_ID_E)  Speed：转速
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetFanSpeed(int FanId, int &Speed);

    //*****************************************************************************
    //获取风扇速度
    //参数[OUT] : FanId:风扇编号(WT_FAN_ID_E)  PwmPer：PWM百分比    Speed：转速  
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetFanSpeedAndPwm(int FanId, int &PwmPer, int &Speed);

    //*****************************************************************************
    //获取开关板端口检测的功率
    //参数[IN] : Port: 要读取的端口
    //参数[OUT] : Power: 该端口检测到的功率
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchPortPower(int Port, double &Power);

    //*****************************************************************************
    //获取开关板端口检测的功率Code
    //参数[IN] : Port: 要读取的端口
    //参数[OUT] : PowerCode: 该端口检测到的功率CODE
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchPortPowerCode(int Port, int &PowerCode);

    //*****************************************************************************
    //获取开关板内部检测的功率
    //参数[IN] : Port: 要读取的端口
    //参数[OUT] : Power: 该端口检测到的功率
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchInnerPower(int Port, double &Power);

    //*****************************************************************************
    //获取开关板内部检测的功率Code
    //参数[IN] : Port: 要读取的端口
    //参数[OUT] : PowerCode: 该端口检测到的功率CODE
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchInnerPowerCode(int Port, int &PowerCode);

    //*****************************************************************************
    //获取缓存的开关板真值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchValueBak(unsigned long long *Data);

    //*****************************************************************************
    //获取开关板状态信息 
    //参数[IN] : SwitchId:开关板ID(WT_SWITCH_ID_E)
    //参数[OUT] : Data: 开关状态
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchStatus(int SwitchId, int &Data);

    //*****************************************************************************
    //设置开关板移位寄存器
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetSwitchShiftReg(int ModId, int Addr, int Data);
    int GetSwitchShiftReg(int ModId, int Addr, int &Data);

    //*****************************************************************************
    //读AD7682通道转换温度的VALUE
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Channel：地址  Value：读取到的电压值
    //Channel取值: BUSI_AD7682_TX_SEL与BUSI_AD7682_TX_SEL_EXT, 或BUSI_AD7682_RX_SEL与BUSI_AD7682_RX_SEL_EXT 
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetAD7682ChannelTemperature(int ModId, WT_DEV_TYPE DevType, int Channel, double &Value);

    //开关板
    //*****************************************************************************
    //设置背板ATT CODE
    //参数[IN] : Port: 要设置的端口
    //参数[IN] : Code: 要设置的code
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetSwbAttCode(int Port, int Code);

    //*****************************************************************************
    //获取背板ATT CODE
    //参数[IN] : Port: 要获取的端口
    //参数[OUT] : Code: 要获取的code
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwbAttCode(int Port, int &Code);

    //*****************************************************************************
    //配置射频板，共本振模式下使用
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   TXFreq：频率  FreqParm：频率配置参数  WorkMode:当前业务单元工作模式
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetFreqWithConfirm(int ModId, WT_DEV_TYPE DevType,double Freq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode);

    //*****************************************************************************
    //模拟IQ信号内/外链路切换开关
    //参数[IN] ：ModId:模块ID  AnalogIQSW:切换开关（0:内链路普通RF模式 1：外链路模拟IQ模式）
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetAnalogIQSW(int ModId, int AnalogIQSW);
    int GetAnalogIQSW(int ModId, int &AnalogIQSW);

    //*****************************************************************************
    //获取DeBug标志
    //参数[OUT] : Data: DeBug标志
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetDebugFlag(int &Data);

    //*****************************************************************************
    //设置DeBug标志
    //参数[IN] : Data: DeBug标志
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDebugFlag(int Data);
    //广播调试接口
    void GetBroadcastPortPower(double Power[WT_RF_MAX]);
    int BroadcastPortDebug();
private:
    int HWConfigInit();
    int HWConfigSave();
    int PortMapInit();

    DevLib();
    ~DevLib();

    //*****************************************************************************
    //器件设置回调函数绑定
    //参数[IN] : CmdId:器件编号  Functor：回调函数
    //返回值: 无
    //*****************************************************************************
    void CmdSetFunctorBind(int CmdId, CmdFunctor &&Functor);

    //*****************************************************************************
    //器件查询回调函数绑定
    //参数[IN] : CmdId:器件编号  Functor：回调函数
    //返回值: 无
    //*****************************************************************************
    void CmdGetFunctorBind(int CmdId, CmdFunctor &&Functor);

    //*****************************************************************************
    //器件查询设置接口初始化
    //参数[IN] : 无
    //返回值: 无
    //*****************************************************************************
    void CmdFunctorInit(void);
    //=============================CMD 控制接口============================
    /*-------------------------------------------------单元板(背板/VSA、VSG业务板)公共接口-------------------------------------------------*/
    //PCI寄存器
    //*****************************************************************************
    //直接写寄存器组数据,不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
    //参数[IN] : ModId：模块ID  DevType:单元板类型  RegTypeDataArray：寄存器组参数
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteDirectMultiReg(int ModId, WT_DEV_TYPE DevType, const std::vector<RegType> &RegTypeDataArray);

    //*****************************************************************************
    //直接读寄存器组数据,不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
    //参数[IN] : ModId：模块ID  DevType:单元板类型
    //参数[OUT] : RegTypeDataArray：寄存器组参数
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadDirectMultiReg(int ModId, WT_DEV_TYPE DevType, std::vector<RegType> &RegTypeDataArray);

    //*****************************************************************************
    //写寄存器组数据(VSA/VSG UNIT)
    //参数[IN] : ModId：模块ID  DevType:单元板类型  RegTypeDataArray：寄存器组参数
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteMultiReg(int ModId, WT_DEV_TYPE DevType, const std::vector<RegType> &RegTypeDataArray);

    //*****************************************************************************
    //读寄存器组数据(VSA/VSG UNIT)
    //参数[IN] : ModId：模块ID  DevType:单元板类型
    //参数[OUT] : RegTypeDataArray：寄存器组参数
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadMultiReg(int ModId, WT_DEV_TYPE DevType, std::vector<RegType> &RegTypeDataArray);

    int GetUBVoltValueX100(int ModId, WT_DEV_TYPE DevType, int &VoltValue);

    int GetUBTempValueX100(int ModId, WT_DEV_TYPE DevType, int &TempValue);

    int GetCryptoMemInfoCmd(int ModId, WT_DEV_TYPE DevType);

    //Flash
    //*****************************************************************************
    //写单元板Flash,测试接口
    //参数[IN] : ModId:模块ID  DevType:单元板类型
    //Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteFlashSectionTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //写单元板Flash,测试接口
    //参数[IN] : ModId:模块ID  DevType:单元板类型
    //Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteFlashPageTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //读单元板Flash,测试接口
    //参数[IN] : ModId:模块ID  DevType:单元板类型  Addr：地址
    //参数[OUT]: Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadFlashPageTest(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data);

    //*****************************************************************************
    //写单元板Flash（基带板、背板）
    //参数[IN] : ModId:模块ID  DevType:单元板类型
    //RomAddr：Rom基地址 DataBuf：缓冲区数据地址
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteRomPage(int ModId, WT_DEV_TYPE DevType, u32 RomAddr, void *DataBuf, int ChipID);

    //*****************************************************************************
    //读单元板Flash（基带板、背板）
    //参数[IN] : ModId:模块ID  DevType:单元板类型  RomAddr：Rom基地址
    //参数[OUT]: DataBuf：缓冲区数据地址
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadRomPage(int ModId, WT_DEV_TYPE DevType, u32 RomAddr, void *DataBuf, int ChipID);

    /*-------------------------------------------------business board-------------------------------------------------*/
    //*****************************************************************************
    //显示射频板信息
    //参数[IN] ： ModId：模块ID  DevType:单元板类型
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ShowRFInfo(int ModId, WT_DEV_TYPE DevType);

    //AD5611
    //*****************************************************************************
    //写AD5611
    //参数[IN] :ModId:模块ID  DevId：芯片选择  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteAD5611(int ModId, int DevId, int Data);
    
    //*****************************************************************************
    //读AD5611
    //参数[IN] :ModId:模块ID  DevId：芯片选择  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadAD5611(int ModId, int DevId, int &Data);

    //AD7682 DAC电压检测芯片
    //*****************************************************************************
    //写AD7682
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteAD7682(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);
    
    //*****************************************************************************
    //读AD7682
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadAD7682(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data);

    //*****************************************************************************
    //读AD7682通道转换电压的VALUE
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Channel：地址  VoltValue：读取到的CODE
    //Channel取值: BUSI_AD7682_TX_SEL与BUSI_AD7682_TX_SEL_EXT, 或BUSI_AD7682_RX_SEL与BUSI_AD7682_RX_SEL_EXT 
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetAD7682ChannelCode(int ModId, WT_DEV_TYPE DevType, int Channel, int &Value);

    //*****************************************************************************
    //读AD7682通道转换电压的VALUE
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Channel：地址  VoltValue：读取到的电压值
    //Channel取值: BUSI_AD7682_TX_SEL与BUSI_AD7682_TX_SEL_EXT, 或BUSI_AD7682_RX_SEL与BUSI_AD7682_RX_SEL_EXT 
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetAD7682ChannelVolt(int ModId, WT_DEV_TYPE DevType, int Channel, double &VoltValue);
    
    //*****************************************************************************
    //读AD7689通道转换电压的VALUE
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Channel：地址  VoltValue：读取到的CODE
    //Channel取值: BUSI_AD7689_CHANL_SEL_
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetAD7689ChannelCode(int ModId, WT_DEV_TYPE DevType, int Channel, int &Value);

    //*****************************************************************************
    //读AD7689通道转换电压的VALUE
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Channel：地址  VoltValue：读取到的CODE
    //Channel取值: BUSI_AD7689_CHANL_SEL_
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetAD7689ChannelVolt(int ModId, WT_DEV_TYPE DevType, int Channel, double &VoltValue);

    //ADF4106 时钟板上用到两片ADF4106作为频综
    //*****************************************************************************
    //写ADF4106
    //参数[IN] :ModId:模块ID  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteADF4106(int ModId, int Addr, int Data);

    //*****************************************************************************
    //读ADF4106
    //参数[IN] :ModId:模块ID  Addr：地址  pData：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadADF4106(int ModId, int Addr, int &Data);

    //HMC7044 基带板时钟芯片,将背板送过来的100M参考时钟分配给射频板和本振板使用
    //*****************************************************************************
    //写HMC7044锁相环
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteHM7044(int ModId, int Addr, int Data);

    //*****************************************************************************
    //读HMC7044锁相环
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Addr：地址  pData：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadHM7044(int ModId, int Addr, int &Data);

    //LTC5594
    //*****************************************************************************
    //写LTC5594
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLTC5594(int ModId, int Addr, int Data);

    //*****************************************************************************
    //读LTC5594
    //参数[IN] :ModId:模块ID  DevType:业务板类型  Addr：地址  pData：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLTC5594(int ModId, int Addr, int &Data);

    //*****************************************************************************
    //写LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLMX2594(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData);

    //*****************************************************************************
    //读LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLMX2594(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData);

    //*****************************************************************************
    //写LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLMX2820(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData);

    //*****************************************************************************
    //读LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLMX2820(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData);

    //*****************************************************************************
    //配置LMX2594频率
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   Freq：频率  PowerLevel：增益
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetMixFreqPower(int ModId, WT_DEV_TYPE DevType, int Freq, int PowerLevel);

    //*****************************************************************************
    //配置LMX2594频率
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   Freq：频率  PowerLevel：增益
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetModFreqPower(int ModId, WT_DEV_TYPE DevType, int Freq, int PowerLevel);
    int SetModFreqPowerHz(int ModId, WT_DEV_TYPE DevType, int Freq, int PowerLevel);

    //BB AD7091 基带板电压检测芯片
    //*****************************************************************************
    //写BB AD7091电压器件寄存器
    //参数[IN] : ModId:模块ID
    //参数[IN] : RegAddr：芯片寄存器地址 VoltData：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteBusiAD7091(int ModId, const int RegAddr, int VoltData);

    //*****************************************************************************
    //读BB AD7091电压器件寄存器
    //参数[IN] : ModId:模块ID  RegAddr：芯片寄存器地址
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadBusiAD7091(int ModId, const int RegAddr, int &VoltData);

    //*****************************************************************************
    //获取AD7091电压检测数据
    //参数[IN] ：ModId：模块ID  Channel：通道地址
    //参数[OUT] ：Data：AD7091射频板电压检测数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetBBVoltValue(int ModId, int Channel, int &VoltValue);

    //*****************************************************************************
    //写LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLoShift(int ModId, WT_DEV_TYPE DevType, long long RegData);

    //*****************************************************************************
    //读LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLoShift(int ModId, WT_DEV_TYPE DevType, long long &RegData);

    //*****************************************************************************
    //写LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLoShiftBit(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData);

    //*****************************************************************************
    //读LMX2594内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLoShiftBit(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData);

    //*****************************************************************************
    //配置HMC705
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   Data：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetLoHMC705(int ModId, WT_DEV_TYPE DevType, int Data);

    //*****************************************************************************
    //配置Loop Filter
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   Data：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetLoLoopFilter(int ModId, WT_DEV_TYPE DevType, int Data);

    //*****************************************************************************
    //配置Freq Channel
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   Data：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetLoFreqChannel(int ModId, WT_DEV_TYPE DevType, int Data);

    //*****************************************************************************
    //写LO DDS内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetLoDDSReg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData);

    //*****************************************************************************
    //读LO DDS内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetLoDDSReg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData);

    //*****************************************************************************
    //设置DDS输出频率
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    Freq:输出频率 单位Hz
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDDSFreq(int ModId, WT_DEV_TYPE DevType, unsigned int Freq);

    //*****************************************************************************
    //设置DDS2输出信号的相位和幅度设置
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    DacFsCurrent：DAC电流 单位uA
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDDSFsCurrent(int ModId, WT_DEV_TYPE DevType, unsigned int DacFsCurrent);

    //*****************************************************************************
    //写射频板移位寄存器
    //参数[IN] ：ModId：模块ID   DevType:单元板类型  RegId:编号  Data: .
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetRfShiftReg(int ModId, WT_DEV_TYPE DevType, int RegId, int Data);

    //*****************************************************************************
    //读射频板移位寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegId:编号
    //参数[OUT] : Data：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetRfShiftReg(int ModId, WT_DEV_TYPE DevType, int RegId, int &Data);

    //ADC AD9684
    //*****************************************************************************
    //写AD9684内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteADCReg(int ModId, int RegAddr, int RegData);

    //*****************************************************************************
    //读AD9684内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadADCReg(int ModId, int RegAddr, int &RegData);

    //ADC AD9142
    //*****************************************************************************
    //写AD142内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteDACReg(int ModId, int RegAddr, int RegData);

    //*****************************************************************************
    //读AD142内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadDACReg(int ModId, int RegAddr, int &RegData);

    //ATT
    //*****************************************************************************
    //设置衰减器件的衰减值
    //参数[IN] ：ModId：模块ID   DevType:单元板类型  DevId: 衰减器件编号,取值为ATT_ID_E    Code：code值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetATTCode(int ModId, WT_DEV_TYPE DevType, int DevId, int Code);

    //*****************************************************************************
    //获取衰减器件的衰减值
    //参数[IN] ：ModId：模块ID   DevType:单元板类型  DevId: 衰减器件编号,取值为ATT_ID_E
    //参数[OUT] ：Code：code值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetATTCode(int ModId, WT_DEV_TYPE DevType, int DevId, int &Code);

    //*****************************************************************************
    //获取射频板温度（通过LM74数据转换为温度的100倍，调试接口专用）
    //参数[IN] ：ModId：模块ID   DevType:单元板类型
    //参数[OUT] ：TempValue：射频板温度
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetRFTemperatureX100(int ModId, WT_DEV_TYPE DevType, int TempId, int &TempValue);

    //*****************************************************************************
    //设置波段选择
    //参数[IN] ：ModId：模块ID   DevType:单元板类型  Freq:频率  BandWorkMode: 频段与工作模式
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetBusiBand(int ModId, WT_DEV_TYPE DevType, int Freq, int BandWorkMode);

    //*****************************************************************************
    //设置VSG到指定的频点与输出功率
    //参数[IN] ：ModId：模块ID   DevType:单元板类型  Freq:频率MHz  Power: 输出功率 DBm*100.
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetFreqOutputPower(int ModId, WT_DEV_TYPE DevType, int Freq, int Power);

    //*****************************************************************************
    //配置链路频率增益
    //参数[IN] ：ModId:模块ID  DevType:业务板类型   Freq：频率(KHz)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetBusiBoardFreq(int ModId, WT_DEV_TYPE DevType, int Freq);

    //*****************************************************************************
    //开启业务板VSA/VSG
    //参数[IN] : ModId：模块ID DevType:单元板类型
    //返回值: 成功或者错误码
    //*****************************************************************************
    int BusiStart(int ModId, WT_DEV_TYPE DevType);

    //*****************************************************************************
    //停止业务板VSA/VSG
    //参数[IN] : ModId：模块ID DevType:单元板类型
    //返回值: 成功或者错误码
    //*****************************************************************************
    int BusiStop(int ModId, WT_DEV_TYPE DevType);

    //*****************************************************************************
    //设置链路本振、DAC为停止模式，ATT最大,AC90+80校准时用
    //参数[IN] : ModId：模块ID DevType:单元板类型
    //返回值: 成功或者错误码
    //*****************************************************************************
    int BusiDown(int ModId, WT_DEV_TYPE DevType);

    //*****************************************************************************
    //设置调频参考时钟
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetModLoRefSel(int ModId, WT_DEV_TYPE DevType, int Data);
    int GetModLoRefSel(int ModId, WT_DEV_TYPE DevType, int &Data);
    /*-------------------------------------------------VSA board-------------------------------------------------*/
    //*****************************************************************************
    //设置rx直流偏移
    //参数[IN] ： ModId：模块ID  Icode：I code值   Qcode: Q code值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetRXDCOffset(int ModId, int Icode, int Qcode);
    /*-------------------------------------------------VSG board-------------------------------------------------*/
    //*****************************************************************************
    //设置DAC I/Q增益code
    //参数[IN] : ModId：模块ID  Icode：IDAC增益值  Qcode：QDAC增益值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDacIQGainCode(int ModId, int ICode, int QCode);

    //*****************************************************************************
    //调整IDAC偏移
    //参数[IN] : ModId：模块ID  IOffset：IDAC偏移值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetIOffset(int ModId, int IOffset);

    //*****************************************************************************
    //调整QDAC偏移
    //参数[IN] : ModId：模块ID  QOffset：QDAC偏移值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetQOffset(int ModId, int QOffset);

    //*****************************************************************************
    //设置TX直流偏移
    //参数[IN] : ModId：模块ID IOffset：IDAC偏移值  QOffset：QDAC偏移值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetTXDCOffset(int ModId, int IOffset, int QOffset);

    //*****************************************************************************
    //设置DAC增益,乘以100倍
    //参数[IN] : ModId：模块ID  Remain：DAC增益值(100倍整数值)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDacGainX100(int ModId, int Remain);

    /*-------------------------------------------------   BACK   -------------------------------------------------*/
    //*****************************************************************************
    //设置晶振的code值
    //参数[IN] :OCXOId:晶振ID,取值为WT_OCXO_ID_E   OCXOCode：晶振code值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetOCXOCode(int OCXOId, int OCXOCode);

    //*****************************************************************************
    //读取刚刚设置晶振的code值
    //参数[OUT] :OCXOId:晶振ID,取值为WT_OCXO_ID_E   OCXOCode:晶振code值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetOCXOCode(int OCXOId, int &OCXOCode);

    //*****************************************************************************
    //获取开关板AD9228寄存器的值
    //参数[IN] : Addr:寄存器地址
    //参数[OUT] : Data: 寄存器值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadSwitchAD9228(int Addr, int &Data);

    //开关板(2X2/2X4)读写控制
    //*****************************************************************************
    //写入开关板AD9228寄存器
    //参数[IN] : DevId: 芯片选择  Addr:寄存器地址 Data: 寄存器值
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteSwitchAD9228(int Addr, int Data);

    //HM7043
    //*****************************************************************************
    //写HM7043芯片
    //参数[INT] : Addr:地址, Data：数据
    //返回值：成功或者错误码
    //*****************************************************************************
    int WriteClockHM7043(int Addr, int Data);

    //*****************************************************************************
    //读HM7043芯片
    ////参数[INT] : Addr:地址
    //参数[OUT] : Data：数据
    //返回值：成功或者错误码
    //*****************************************************************************
    int ReadClockHM7043(int Addr, int &Data);

    //*****************************************************************************
    //读射频板版本号
    ////参数[INT] : Channel: 通道片选
    //参数[OUT] : Version: 版本
    //返回值：成功或者错误码
    //*****************************************************************************
    int ReadRfVersion(int Channel, int &Version);

    //*****************************************************************************
    //加密芯片CLK翻转
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)  VoltData：写入的数据(12bit)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int InitCryptoAT88(int Loop);

    //*****************************************************************************
    //烧录加密芯片
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)  VoltData：写入的数据(12bit)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteCryptoAT88(int Cmd, int Addr, int Len, char *Data);

    //*****************************************************************************
    //读取加密芯片
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)
    //参数[OUT] : VoltData：所读的数据(12bit)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadCryptoAT88(int Cmd, int Addr, int Len, char *Data);

    //*****************************************************************************
    //写网口板AD7091电压检测器件
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)  VoltData：写入的数据(12bit)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteBackAD7091Reg(const int RegAddr, int VoltData);

    //*****************************************************************************
    //读网口板AD7091电压检测器件
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)
    //参数[OUT] : VoltData：所读的数据(12bit)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadBackAD7091Reg(const int RegAddr, int &VoltData);

    //*****************************************************************************
    //读网口板AD7091电压
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)
    //参数[OUT] : VoltData：电压数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetBackChannelVoltValue(int ChannelAddr, double &VoltValue);

    //*****************************************************************************
    //读网口板AD7091通道转换的数值
    //参数[IN] : RegAddr：芯片寄存器地址(5bit)
    //参数[OUT] : Data：所读的数据(12bit)
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetBackAd7091ChannelValue(const int RegAddr, int &Data);

    //风扇
    //*****************************************************************************
    //设置风扇寄存器
    //参数[IN] :  ReaAddr：寄存器地址  RegData：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteFanReg(int ReaAddr, int RegData);

    //*****************************************************************************
    //获取风扇寄存器数据
    //参数[OUT] : ReaAddr：寄存器地址  RegData：回读的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadFanReg(int ReaAddr, int &RegData);

    //LED
    //*****************************************************************************
    //写LED IO扩展（移位寄存器）
    //参数[IN] : Addr：芯片寄存器地址 Data：寄存器数据（低16位为芯片1，高16位为芯片2）
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLedIOExtReg(int Addr, int Data);

    //*****************************************************************************
    //读LED IO扩展（移位寄存器）
    //参数[IN] : Addr：芯片寄存器地址 Data：寄存器数据（低16位为芯片1，高16位为芯片2）
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLedIOExtReg(int Addr, int &Data);

    //*****************************************************************************
    //以bit写LED IO扩展（移位寄存器）
    //参数[IN] : LedId：Led IO端口号  Status：IO端口状态
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteLedIOExtBit(int LedId, int Status);

    //*****************************************************************************
    //以bit读LED IO扩展（移位寄存器）
    //参数[IN] : LedId：Led IO端口号
    //参数[OUT] : Status：IO端口状态
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadLedIOExtBit(int LedId, int &Status);

    //*****************************************************************************
    //写Pa状态
    //参数[INT] : Port:地址, Status：状态
    //返回值：成功或者错误码
    //*****************************************************************************
    int WriteSwitchPa(int Port, int Status);

    //*****************************************************************************
    //读pa状态
    //参数[INT] : Port:地址
    //参数[OUT] : Status：状态
    //返回值：成功或者错误码
    //*****************************************************************************
    int ReadSwitchPa(int Port, int &Status);

    //*****************************************************************************
    //写42553状态
    //参数[INT] : Port:地址, Status：状态
    //返回值：成功或者错误码
    //*****************************************************************************
    int WriteSwitch42553(int Port, int Status);

    //*****************************************************************************
    //读42553状态
    //参数[INT] : Port:地址
    //参数[OUT] : Status：状态
    //返回值：成功或者错误码
    //*****************************************************************************
    int ReadSwitch42553(int Port, int &Status);

    //*****************************************************************************
    //写42553状态
    //参数[INT] : Port:地址, Status：状态
    //返回值：成功或者错误码
    //*****************************************************************************
    int WritePllAdf4002(int DeviceId, int Data);

    //*****************************************************************************
    //读42553状态
    //参数[INT] : Port:地址
    //参数[OUT] : Status：状态
    //返回值：成功或者错误码
    //*****************************************************************************
    int ReadPllAdf4002(int DeviceId, int &Data);

    /*-------------------------------------------------   SWITCH   -------------------------------------------------*/

    //*****************************************************************************
    //通过背板写开关板状态
    //参数[IN] : ModId: 0, VSA, 1, VSG
    //参数[IN] : DevType:
    //参数[IN] : Port:
    //参数[IN] : State: 高8表示siso/8080, 低8bit表示state
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetSwitchState(int ModId, WT_DEV_TYPE DevType, int Port, int State);

    //获取开关板硬件版本
    //*****************************************************************************
    //获取开关板状态信息
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetSwitchBoardVersion(int SwitchId, int &SBVersion);

    /*-------------------------------------------------   TEST   -------------------------------------------------*/
    int SaveDmaData(int ModId, WT_DEV_TYPE DevType, int Index);

    int WriteXDMA(int ModId, WT_DEV_TYPE DevType, int Channel, int Lenght);

    int ReadXDMA(int ModId, WT_DEV_TYPE DevType, int Channel, int Lenght);
    
    inline int CheckPciReg(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);
    int GetStressTestStatus(void *pData, int &lenght);
    int UnitBoardStressTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);
    int BackPlaneStressTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);
    int BusiBoardStressTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //读测试接口
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetDevApiTest(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data);

    //*****************************************************************************
    //写测试接口
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDevApiTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    // 校准时设置占据资源标志位, 保持链路，设备状态不变化
    int SetOccupyFlag(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //器件相应时间DEBUG配置接口
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetDevResponseDebug(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //器件相应时间DEBUG查询接口
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetDevResponseDebug(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data);

    //*****************************************************************************
    //测试接口
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int GetContinueTestResult(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data);

    //*****************************************************************************
    //测试接口
    //参数[IN] : ModId：模块ID  DevType:单元板类型  Addr：地址  Data：写入的数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int SetContinueTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data);

    //*****************************************************************************
    //跑马灯测试
    //参数[IN] : args：无效
    //返回值: 无
    //*****************************************************************************
    friend void* LedTest(int Data);
	
    //*****************************************************************************
    //GET SN测试
    //参数[IN] : args：无效
    //返回值: 无
    //*****************************************************************************
    friend void* SnGetTest(void* args);

    void ThreadBaseFpgaUpgrade(int ModId);
    //*****************************************************************************
    //写HMC833内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型    RegAddr:内部寄存器地址
    //           RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int WriteHMC833Reg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData);

    //*****************************************************************************
    //读HMC833内部寄存器
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  RegAddr:内部寄存器地址
    //参数[OUT] : RegData：数据
    //返回值: 成功或者错误码
    //*****************************************************************************
    int ReadHMC833Reg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData);

    //*****************************************************************************
    //查询本振锁定状态
    //参数[IN] ：ModId:模块ID  DevType:业务板类型  LoId:本振Id
    //参数[OUT] : Lock:锁定状态
    //返回值: 成功或者错误码
    //*****************************************************************************
    int CheckLOIsLock(int ModId, WT_DEV_TYPE DevType, int LoId, int &Lock);

//============================================================
public:
    struct FanSpeedManagerType
    {
        int Enable;
        double TargetTemp;                                              //目标温度
        int Stage;                                                      //调速阶段  启动阶段先让仪器温度升到预定的范围，再进行正常的调速。
        int StartTemp;                                                  //当仪器温度达到该温度值就退出启动阶段
        int StartStageDefaultSpeed;                                     //启动阶段风扇度
        int NormalStageDefaultSpeed;                                    //启动阶段风扇度
        wtev::timer FanTimer;                                           //当风扇调速timeout检测的定时器
        int TimerInterval;                                              //调速间隔
        int Mask;                                                       //参与调速的风扇掩码
        int StepSize;                                                   //调速步距
        int SpeedLevel;                                                 //当前调速等级
        double LastTemp;                                                //上次读取的调速温度
        double TempThreshold1;                                          //温度下阈值
        double TempThreshold2;                                          //温度上阈值
        double TempThresholdInTargetRange;                              //目标区间内的调速阈值
        int TempLimit1;                                                 //温度最小限值
        int TempLimit2;                                                 //温度最大限值
        double TempDiff1;                                               //本次温度与上次温度比较的差值。
        double TempDiff2;                                               //本次温度与上次温度比较的差值。
        int SpeedLimit1;                                                //速度最小限值
        int SpeedLimit2;                                                //速度最大限值
        int Debug;                                                      //Debug时，手动设置温度来作调速依据
        double DebugTempValue;                                          //Debug温度值
        int LogTextEnable;                                              //是否输出log记录文件
        char LogName[200];                                              //log文件名称
        std::ofstream Fs;                                               //log记录文件
    };

    struct ContinueTestType
    {
        int Running = false;
        int Type;
        int Total;
        int SuccessCnt;
        int FailedCnt;
    };

    struct StressTestType
    {
        int Status;
        int TotalCnt;
        int FailedCnt;
        int OpcCntPerLoop;
        double Usetime;

        enum WT_STRESS_TEST_STATUS_E
        {
            STRESS_TEST_STATUS_SUCCESS,
            STRESS_TEST_STATUS_FAILD,
            STRESS_TEST_STATUS_RUNNING,
        };

        void Set(int pStatus, int pTotalCnt, int pFailedCnt, double pUsetime)
        {
            Status = pStatus;
            TotalCnt = pTotalCnt;
            FailedCnt = pFailedCnt;
            Usetime = pUsetime;
        }

        void Reset()
        {
            Set(STRESS_TEST_STATUS_RUNNING, 0, 0, 0);
            OpcCntPerLoop = 1;
        }
    };

    typedef struct
    {
        int Mode;                             ///LOCommonMode
        int ModId;                            ///ModId
    }LoModeParam;


private:
    static int m_VSAMask;                                               //VSA单元掩码
    static int m_VSGMask;                                               //VSG单元掩码
    int        VSAInitMask;                                             //VSA初始化掩码
    int        VSGInitMask;                                             //VSA初始化掩码
    std::mutex Mutex;                                                   //资源锁
    std::map<int, int> m_PortMap;                                       //端口映射
    std::condition_variable Cond;                                       //资源条件变量
    DevBase *m_Devs[UB_TYPE_COUNT][MAX_BUSINESS_NUM] = { { nullptr }, };      //硬件对象
    std::ifstream m_JsonIfstream;                                       //器件初始化脚本文件流
    Json::Value m_JsonRoot;                                             //Json解析结果对象
    std::map<int, CmdWRCallBack> m_CmdWRCallBack;                       //器件编号和查询设置接口关联表
    std::map<int, CmdWRDoubleCallBack> m_CmdWRDoubleCallBack;           //器件编号和查询设置接口关联表
    FanSpeedManagerType FanSpeedManager;                                //风扇自动调速对象
    wtev::timer m_VsgDacTimerEv;                                        //当风扇调速timeout检测的定时器
    ContinueTestType m_ContinueTest;
    StressTestType m_StressTest;
    int CalOccupyFlag = 0; // VSA单元使用完后是否立即释放单元(0是正常模式，1是校准模式下占有该单元不释放)
    int m_BaseBurnRet[MAX_BUSINESS_NUM];
    int m_DeBugFlag = 0;                                        //DeBug标志，为1时不相应API命令。
    double m_BroadcastPortPower[WT_RF_MAX] = {0,0,0,0,0,0,0,0}; //旧版端口功率配置
    bool m_BroadcastPortDebug = false;
};

#endif
