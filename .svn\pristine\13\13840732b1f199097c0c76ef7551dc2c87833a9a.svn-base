#include "scpi_gen_11ax_mu.h"
#include "commonhandler.h"
#include <iostream>
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "TesterWave.h"
#include <sys/time.h>
#include "scpi_gen_11ax_mumimo.h"

scpi_result_t Set11AX_MU_STBC(scpi_t *context)
{
    return Set11AX_MU_MIMO_STBC(context);
}

scpi_result_t Set11AX_MU_TonePlan(scpi_t * context)
{
    return Set11AX_MU_MIMO_TonePlan(context);
}

scpi_result_t Set11AX_MU_CenterRU(scpi_t *context)
{
    return Set11AX_MU_MIMO_CenterRU(context);
}

scpi_result_t Set11AX_MU_RUValid(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int valid[AX_RU_COUNT] = { 0 };
    do
    {
        if(context->parser_state.numberOfParameters < 1)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11axMU(attr);
        if (iRet)
        {
            break;
        }

        for (int i = 0; i < context->parser_state.numberOfParameters && i < AX_RU_COUNT; i++)
        {
            SCPI_ParamInt(context, &valid[i], false);
            attr->PnWifi.get()->PN11ax_MU.RU[i].UserNum = (valid[i] > 0 ? 1 : 0);
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_MU_SIGBMCS(scpi_t *context)
{
    return Set11AX_MU_MIMO_SIGBMCS(context);
}

scpi_result_t Set11AX_MU_SIGBDCM(scpi_t *context)
{
    return Set11AX_MU_MIMO_SIGBDCM(context);
}

scpi_result_t Set11AX_MU_ULDL(scpi_t *context)
{
    return Set11AX_MU_MIMO_ULDL(context);
}

scpi_result_t Set11AX_MU_SpatialReuse(scpi_t *context)
{
    return Set11AX_MU_MIMO_SpatialReuse(context);
}

scpi_result_t Set11AX_MU_GLTFSize(scpi_t *context)
{
    return Set11AX_MU_MIMO_GLTFSize(context);
}

scpi_result_t Set11AX_MU_TXOP(scpi_t *context)
{
    return Set11AX_MU_MIMO_TXOP(context);
}

scpi_result_t Set11AX_MU_BSSColor(scpi_t *context)
{
    return Set11AX_MU_MIMO_BSSColor(context);
}

scpi_result_t Set11AX_MU_Doppler(scpi_t *context)
{
    return Set11AX_MU_MIMO_Doppler(context);
}

scpi_result_t Set11AX_MU_MidamblePeriodicity(scpi_t *context)
{
    return Set11AX_MU_MIMO_MidamblePeriodicity(context);
}

scpi_result_t Set11AX_MU_PE(scpi_t *context)
{
    return Set11AX_MU_MIMO_PE(context);
}


scpi_result_t Set11AX_MU_RUMCS(scpi_t * context)
{
    return Set11AX_MU_MIMO_RUMCS(context);
}

scpi_result_t Set11AX_MU_RUPowerFactor(scpi_t * context)
{
    return Set11AX_MU_MIMO_RUPowerFactor(context);
}

scpi_result_t Set11AX_MU_RUAID(scpi_t * context)
{
    return Set11AX_MU_MIMO_RUAID(context);
}

scpi_result_t Set11AX_MU_RUDCM(scpi_t *context)
{
    return Set11AX_MU_MIMO_RUDCM(context);
}

scpi_result_t Set11AX_MU_RUCoding(scpi_t *context)
{
    return Set11AX_MU_MIMO_RUCoding(context);
}

scpi_result_t Set11AX_MU_RUNSS(scpi_t *context)
{
    return Set11AX_MU_MIMO_RUNSS(context);
}

scpi_result_t Set11AX_MU_RUBeamformed(scpi_t *context)
{
    return Set11AX_MU_MIMO_RUBeamformed(context);
}
