/*
 * scpiutils.cpp
 *
 *  Created on: 2022-11-1
 *      Author: Administrator
 */
// #include "scpi/scpi.h"
// #include "basehead.h"
#include "scpi_3gpp_utils.h"

#include <cstdio>
#include <future>
#include <iostream>
#include <string>
#include <thread>
#include <vector>

#include "scpiutils.h"
#include "wterror.h"
#include "commonhandler.h"
#include "vsahandler.h"
#include "vsghandler.h"

#include "cellular_analyze/scpi_3gpp_alz_gsm.h"
#include "cellular_analyze/scpi_3gpp_alz_wcdma.h"
#include "cellular_analyze/scpi_3gpp_alz_lte.h"
#include "cellular_analyze/scpi_3gpp_alz_nr5g.h"
#include "cellular_analyze/scpi_3gpp_alz_nbiot.h"

#include "cellular_wavegen/scpi_3gpp_gen_gsm.h"
#include "cellular_wavegen/scpi_3gpp_gen_wcdma.h"
#include "cellular_wavegen/scpi_3gpp_gen_lte.h"
#include "cellular_wavegen/scpi_3gpp_gen_nr5g.h"
#include "cellular_wavegen/scpi_3gpp_gen_nbiot.h"

#include "cellular_result/scpi_3gpp_result_general.h"
#include "cellular_result/scpi_3gpp_result_gsm.h"

using namespace cellular::alz::gsm;
using namespace cellular::alz::nr;

using namespace cellular::result::general;
using namespace cellular::result::gsm;

static const scpi_command_t scpi_wt_3gpp_comm_cmd[] = {
    // Test
    {"WT:LTE:SOURce:VERSion?", SCPI_LTE_QueryLTEVersion, 0, SCPI_QUERY_CMD},

    // 通用分析参数
    {"WT:CELLular:SENSe:CONFigure:ANALy:DEMOd", SCPI_3GPP_SetAlzParamDemode, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONFigure:ANALy:DEMOd", SCPI_3GPP_SetAlzParamDemode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DEMOd", SCPI_3GPP_SetAlzParamDemode, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:DEMOd", SCPI_3GPP_SetAlzParamDemode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DEMOd", SCPI_3GPP_SetAlzParamDemode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DEMOd", SCPI_3GPP_SetAlzParamDemode, 0, SCPI_SEQUENTIAL},
    {"WT[:CELLular][:WCDMA][:LTE][:NIOT][:NR]:SENSe:CONFigure:ANALy:LINK:DIREct", SCPI_3GPP_SetAlzLinkDirect, 0, SCPI_SEQUENTIAL},
    
    {"WT[:LTE][:NIOT]:SENSe:CONFigure:ANALy:AlzGroup", SCPI_3GPP_SetAlzGroup, 0, SCPI_SEQUENTIAL},  // analyzeGroup
    {"WT[:LTE][:NR][:NIOT]:SENSe:CONFigure:ANALy[:STREam#]:RFBAnd", SCPI_3GPP_SetRfBand, 0, SCPI_SEQUENTIAL},  // rf_band
    {"WT[:LTE][:NR][:NIOT]:SENSe:CONFigure:ANALy[:STREam#]:RFCHannel", SCPI_3GPP_SetRfChannel, 0, SCPI_SEQUENTIAL},  // rf_channel
    {"WT[:LTE][:NR][:NIOT]:SENSe:CONFigure:ANALy:DC:FREQ:COMP", SCPI_3GPP_SetAlzDcFreqCompensate, 0, SCPI_SEQUENTIAL},  // DcFreqCompensate
    {"WT:CELLular:SENSe:CONFigure:ANALy:MEAS:POWEr", SCPI_3GPP_SetAlzMeasurePower, 0, SCPI_SEQUENTIAL},  // MeasPowerGraph
    {"WT:CELLular:SENSe:CONFigure:ANALy:MEAS:CCDF", SCPI_3GPP_SetAlzMeasureCcdf, 0, SCPI_SEQUENTIAL},  // MeasCcdf
    {"WT:CELLular:SENSe:CONFigure:ANALy:MEAS:SPECtrum", SCPI_3GPP_SetAlzMeasureSpectrum, 0, SCPI_SEQUENTIAL},  // MeasSpectrum
    {"WT:CELLular:SENSe:CONFigure:ANALy:SPECtrum:RBW", SCPI_3GPP_SetAlzSpectrumRBW, 0, SCPI_SEQUENTIAL},  // SpectrumRBW
    {"WT[:LTE][:NR][:NIOT]:SENSe:CONFigure:ANALy:SPECtrum:RBW", SCPI_3GPP_SetAlzSpectrumRBW, 0, SCPI_SEQUENTIAL},  // SpectrumRBW

    {"WT[:CELLular][:WCDMA][:LTE][:NIOT][:NR]:SENSe:CONFigure:ANALy:PARAm:REF:LARB", SCPI_3GPP_SetAnalyParamRefLoadArb, 0, SCPI_SEQUENTIAL}, // 加载ARB
    {"WT[:CELLular][:WCDMA][:LTE][:NIOT][:NR]:SENSe:CONFigure:ANALy:PARAm:REF:CLEAr", SCPI_3GPP_SetAnalyParamRefClear, 0, SCPI_SEQUENTIAL},  // 清空参考
    {"WT[:CELLular][:WCDMA][:LTE][:NIOT][:NR]:SENSe:CONFigure:ANALy:PARAm:ARB", SCPI_3GPP_SetAnalysisParamArb, 0, SCPI_SEQUENTIAL},
    {"WT[:CELLular][:WCDMA][:LTE][:NIOT][:NR]:SENSe:FETCh:ANALy:PARAm:ARB?", SCPI_3GPP_GetAnalyParamArb, 0, SCPI_QUERY_CMD},
    {"WT[:CELLular][:WCDMA][:LTE][:NIOT][:NR]:SENSe:FETCh:ANALy:PARAm:JSON?", SCPI_3GPP_GetAnalyParamJson, 0, SCPI_QUERY_CMD},

    {"WT:FW:SENSe:CONFigure:ANALy:MEASure:COMMon:PARAm", SCPI_3GPP_SetAnalyMeasureCommonParam, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_wavegen_cmd[] = {
    {"WT[:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:FLATness:FACTor", Set3GPPWaveGenFlatnessFactor, 0, SCPI_SEQUENTIAL}, // FlatFactor
    {"WT[:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:PHASe:NOISe:FLAG", Set3GPPWaveGenPhaseNoiseFlag, 0, SCPI_SEQUENTIAL}, // PhaseNoiseFlag
    {"WT[:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:PHASe:NOISe:FACTor#", Set3GPPWaveGenPhaseNoiseFactor, 0, SCPI_SEQUENTIAL}, // PhaseNoiseFactor
    {"WT:CELLular:SOURce:CONFigure:WAVE:FUNCtion:TYPE", Set3GPPWaveGenFunctionType, 0, SCPI_SEQUENTIAL}, // FunctionType
};

static const scpi_command_t scpi_wt_3gpp_result_cmd[] = {
    // 注: 此处注释的scpi命令定义在scpiutils.cpp中, 是与具体协议无关的, 公共的命令, 放在这里是方便整理和查询
    // 注意不要与WiFi的命令同名

    // IQ signal视图
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:POINts:IQ:DATA?", GetVsaRstPointIQ, 0, SCPI_QUERY_CMD},  // capturedata, capturecnt

    // Power 视图
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:POWer:FRAMe:LOCAtion?", GetVsaRstFrameLocation, 0, SCPI_QUERY_CMD},  // SubfrmStart, SubfrmEnd
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:POINts:POWEr?", GetVsaRstPointPower, 0, SCPI_QUERY_CMD},  // PwrdBm
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:NR][:GSM]:SENSe:FETCh:POINts:AVG:POWEr?", GetVsaRstPointAvgPower, 0, SCPI_QUERY_CMD},  // PwrWinAvgdBm
    // Power value
    {"WT[:CELLular][:WCDMA]:SENSe:FETCh:PACKage:AVG:POWR?", SCPI_3GPP_GetVsaRstPkgAvgPwrdBm, 0, SCPI_QUERY_CMD},  // PkgAvgPwrdBm
    {"WT[:CELLular][:WCDMA]:SENSe:FETCh:PACKage:PEAK:POWR?", SCPI_3GPP_GetVsaRstPkgPeakPwrdBm, 0, SCPI_QUERY_CMD},  // PkgPeakPwrdBm
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SUBFrame:AVG:POWR?", SCPI_3GPP_GetVsaRstFrmAvgPwrdBm, 0, SCPI_QUERY_CMD},  // FrmAvgPwrdBm
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SUBFrame:PEAK:POWR?", SCPI_3GPP_GetVsaRstFrmPeakPwrdBm, 0, SCPI_QUERY_CMD},  // FrmPeakPwrdBm
    {"WT[:CELLular][:NIOT]:SENSe:FETCh:SUBCarrier:AVG:POWEr?", SCPI_3GPP_GetVsaRstSubcarrierAvgPower, 0, SCPI_QUERY_CMD},  // SCAvgPwrdBm

    // CCDF 视图
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:POWEr:REF?", GetVsaRstCCDFPowerRef, 0, SCPI_QUERY_CMD},  // CCDF.frm_ccdf
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:PROB?", GetVsaRstCCDFProb, 0, SCPI_QUERY_CMD},  // CCDF.ccdf_mask
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:STARt?", GetVsaRstCCDFStart, 0, SCPI_QUERY_CMD},  // CCDF.frm_ccdf_start
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE]:SENSe:FETCh:CCDF:SCALe?", GetVsaRstCCDFScale, 0, SCPI_QUERY_CMD},  // CCDF.frm_ccdf_scale
    // CCDF.frm_ccdf_end 无命令
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:PERcentage:POWEr?", GetVsaRstCCDFPercentPower, 0, SCPI_QUERY_CMD},  // CCDF.ccdf_power

    // Spectrum 视图
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:DATA:ARB?", GetVsaRstSpectrumDataARB, 0, SCPI_QUERY_CMD},  // Spectral.spectral_all_dbr
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:DATA?", GetVsaRstSpectrumData, 0, SCPI_QUERY_CMD},  // Spectral.spectral_all_dbr
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:RBW?", GetVsaRstSpectrumRBW, 0, SCPI_QUERY_CMD},  // Spectral.spectral_rbw
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:FREQuency:SPAN?", GetVsaRstSpectrumSpan, 0, SCPI_QUERY_CMD},  // Spectral.spectral_span
    {"WT[:CELLular][:GSM][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:FREQ:STARt?", SCPI_3GPP_GetVsaRstSpectrumFreqStart, 0, SCPI_QUERY_CMD},  // Spectral.rf_start_freq
    {"WT[:CELLular][:GSM][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:FREQ:CENTer?", SCPI_3GPP_GetVsaRstSpectrumFreqCenter, 0, SCPI_QUERY_CMD},  // Spectral.rf_center_freq
    {"WT[:CELLular][:GSM][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:FREQ:END?", SCPI_3GPP_GetVsaRstSpectrumFreqEnd, 0, SCPI_QUERY_CMD},  // Spectral.rf_end_freq
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:OBW:FREQuency:SEGMent#?", GetVsaRstSpectrumOBW, 0, SCPI_QUERY_CMD},  // Spectral.obw_startfreq, obw_endfreq, obw_bw

    // Spectrum Emission Mask 视图
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:EMISsion:DATA:ARB?", SCPI_3GPP_GetVsaRstSpectrumEmissionDataARB, 0, SCPI_QUERY_CMD},  // Spectral.RxSpectEmis
    {"WT:CELLular:SENSe:FETCh:ADD:SPECtrum:EMISsion:DATA:ARB?", SCPI_3GPP_GetVsaRstAddSpectrumEmissionDataARB, 0, SCPI_QUERY_CMD},  // Spectral.AddSpectEmis
    {"WT:CELLular:SENSe:FETCh:ADD:SPECtrum:EMISsion:SEG:DATA:ARB?", SCPI_3GPP_GetVsaRstAddSpectrumEmissionSegDataARB, 0, SCPI_QUERY_CMD},  // Spectral.AddSpectEmisSeg
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:EMISsion:MASK:DATA:ARB?", SCPI_3GPP_GetVsaRstSpectrumEmissionMaskDataARB, 0, SCPI_QUERY_CMD},  // Spectral.SpectEmisMask
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:EMISsion:MASK:SEG:DATA:ARB?", SCPI_3GPP_GetVsaRstSpectrumEmissionMaskSegDataARB, 0, SCPI_QUERY_CMD},  // Spectral.EmisMaskSeg
    // Spectral.spectral_span, rf_center_freq, rf_start_freq, rf_end_freq 同 Spectrum 视图
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:SEMMarg:NEG?", SCPI_3GPP_GetVsaRstSpectrumSemNeg, 0, SCPI_QUERY_CMD},   // Spectral.SemMargNeg
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:SEMMarg:POS?", SCPI_3GPP_GetVsaRstSpectrumSemPos, 0, SCPI_QUERY_CMD},   // Spectral.SemMargPos
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:SEM:MARGin:NEG?", SCPI_3GPP_GetVsaRstSpectrumSemMarginNeg, 0, SCPI_QUERY_CMD},  // SemMargNeg, repeat
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:SEM:MARGin:POS?", SCPI_3GPP_GetVsaRstSpectrumSemMarginPos, 0, SCPI_QUERY_CMD},  // SemMargPos, repeat
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:SEM:MARGin:ALL?", SCPI_3GPP_GetVsaRstSpectrumSemMarginAll, 0, SCPI_QUERY_CMD},  // SemMargNeg + SemMargPos
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:BADPointcnt?", SCPI_3GPP_GetVsaRstSpectrumBadPointcnt, 0, SCPI_QUERY_CMD},  // Spectral.badpointcnt

    // Spectrum ACLR UTRA 视图
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:ACLR:UTRA:POWEr?", SCPI_3GPP_GetVsaRstSpectrumACLRUtraPower, 0, SCPI_QUERY_CMD},  // ACLR.Utra[i].Power
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:ACLR:UTRA:MASK?", SCPI_3GPP_GetVsaRstSpectrumACLRUtraMask, 0, SCPI_QUERY_CMD},  // ACLR.Utra[i].Mask
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:ACLR:UTRA:FREQ?", SCPI_3GPP_GetVsaRstSpectrumACLRUtraFreq, 0, SCPI_QUERY_CMD},  // ACLR.Utra[i].Freq
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:ACLR:UTRA:ACLRatio?", SCPI_3GPP_GetVsaRstSpectrumACLRUtraAclRatio, 0, SCPI_QUERY_CMD},  // ACLR.Utra[i].AclRatio

    // Spectrum ACLR E-UTRA 视图
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:ACLR:EUTRA:POWEr?", SCPI_3GPP_GetVsaRstSpectrumACLREUtraPower, 0, SCPI_QUERY_CMD},  // ACLR.Eutra[i].Power
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:ACLR:EUTRA:MASK?", SCPI_3GPP_GetVsaRstSpectrumACLREUtraMask, 0, SCPI_QUERY_CMD},  // ACLR.Eutra[i].Mask
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:ACLR:EUTRA:FREQ?", SCPI_3GPP_GetVsaRstSpectrumACLREUtraFreq, 0, SCPI_QUERY_CMD},  // ACLR.Eutra[i].Freq
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:ACLR:EUTRA:ACLRatio?", SCPI_3GPP_GetVsaRstSpectrumACLREUtraAclRatio, 0, SCPI_QUERY_CMD},  // ACLR.Eutra[i].AclRatio
    {"WT[:LTE]:SENSe:FETCh:SPECtrum:ACLR:ALL:ACLRatio?", SCPI_3GPP_GetVsaRstSpectrumACLRAclRatio, 0, SCPI_QUERY_CMD},  // Eutra[i].AclRatio + Utra[i].AclRatio

    // Spectrum ACLR NR-UTRA 视图
    {"WT[:NR]:SENSe:FETCh:SPECtrum:ACLR:NRUTRA:POWEr?", SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraPower, 0, SCPI_QUERY_CMD},  // ACLR.NrUtra[i].Power
    {"WT[:NR]:SENSe:FETCh:SPECtrum:ACLR:NRUTRA:MASK?", SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraMask, 0, SCPI_QUERY_CMD},  // ACLR.NrUtra[i].Mask
    {"WT[:NR]:SENSe:FETCh:SPECtrum:ACLR:NRUTRA:FREQ?", SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraFreq, 0, SCPI_QUERY_CMD},  // ACLR.NrUtra[i].Freq
    {"WT[:NR]:SENSe:FETCh:SPECtrum:ACLR:NRUTRA:ACLRatio?", SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraAclRatio, 0, SCPI_QUERY_CMD},  // ACLR.NrUtra[i].AclRatio

    // Constellation(星座图)
    {"WT:CELLular:SENSe:FETCh:SYMBol:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},  // Evm.RxPoint
    {"WT:GSM:SENSe:FETCh:SYMBol:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:FETCh:SYMBol:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:SYMBol:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:SYMBol:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:SYMBol:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},

    {"WT:CELLular:SENSe:FETCh:SYMBol:PILOt:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblPilotConstARB, 0, SCPI_QUERY_CMD},  // Evm.RxPoint
    {"WT:LTE:SENSe:FETCh:SYMBol:PILOt:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:SYMBol:PILOt:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:SYMBol:PILOt:CONSt:DATA?", SCPI_3GPP_GetVsaRstSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    
    // {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SYMBol:REFerence:CONSt:DATA?", GetVsaRstSymoblRefConstARB, 0, SCPI_QUERY_CMD},  // Evm.RefPoint, 参考星座图点

    // Evm vs. Subcarrier 视图
    {"WT:LTE:SENSe:FETCh:CARRier:SYMBol:EVM?", SCPI_3GPP_GetVsaRstEvmCarrierEvm, 0, SCPI_QUERY_CMD},  // Evm.CarrierEvm
    {"WT:NR:SENSe:FETCh:CARRier:SYMBol:EVM?", SCPI_3GPP_GetVsaRstEvmCarrierEvm, 0, SCPI_QUERY_CMD},  // Evm.CarrierEvm
    {"WT[:LTE][:NR]:SENSe:FETCh:CARRier:SYMBol:LENGth?", SCPI_3GPP_GetVsaRstEvmMaxCarrierLen, 0, SCPI_QUERY_CMD},  // Evm.MaxCarrierLen

    // EVM vs. Symbol 视图
    {"WT:CELLular:SENSe:FETCh:SYMBol:EVM?", SCPI_3GPP_GetVsaRstSymoblEVM, 0, SCPI_QUERY_CMD},  // Evm.SymbEvm
    {"WT:GSM:SENSe:FETCh:SYMBol:EVM?", SCPI_3GPP_GetVsaRstSymoblEVM, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:SYMBol:EVM?", SCPI_3GPP_GetVsaRstSymoblEVM, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:SYMBol:EVM?", SCPI_3GPP_GetVsaRstSymoblEVM, 0, SCPI_QUERY_CMD},

    {"WT:CELLular:SENSe:FETCh:SYMBol:EVM:PILOt?", SCPI_3GPP_GetVsaRstPilotSymoblEVM, 0, SCPI_QUERY_CMD},  // Evm.SymbEvm
    {"WT:LTE:SENSe:FETCh:SYMBol:EVM:PILOt?", SCPI_3GPP_GetVsaRstPilotSymoblEVM, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:SYMBol:EVM:PILOt?", SCPI_3GPP_GetVsaRstPilotSymoblEVM, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:SYMBol:EVM:PILOt?", SCPI_3GPP_GetVsaRstPilotSymoblEVM, 0, SCPI_QUERY_CMD},

    // Magnitude Error 视图
    {"WT[:CELLular][:GSM][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:MAGN:ERR?", SCPI_3GPP_GetVsaRstMPErrMagnErr, 0, SCPI_QUERY_CMD},  // MPErr.MagnErr
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:MAGN:ERR:RMS?", SCPI_3GPP_GetVsaRstMPErrErrRms, 0, SCPI_QUERY_CMD},  // MPErr.MagnErrRms
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:MAGN:ERR:PEAK?", SCPI_3GPP_GetVsaRstMPErrMagnErrPeak, 0, SCPI_QUERY_CMD},  // MPErr.MagnErrPeak
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:MAGN:ERR:DMRS?", SCPI_3GPP_GetVsaRstMPErrMagnErrDmrs, 0, SCPI_QUERY_CMD},  // MPErr.MagnErrDmrs

    // Phase Error 视图
    {"WT[:CELLular][:GSM][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:PHASe:ERR?", SCPI_3GPP_GetVsaRstMPErrPhaseErrs, 0, SCPI_QUERY_CMD},  // MPErr.PhaseErr
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:PHASe:ERR:RMS?", SCPI_3GPP_GetVsaRstMPErrPhaseErrRms, 0, SCPI_QUERY_CMD},  // MPErr.PhaseErrRms
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:PHASe:ERR:PEAK?", SCPI_3GPP_GetVsaRstMPErrPhaseErrPeak, 0, SCPI_QUERY_CMD},  // MPErr.PhaseErrPeak
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:MPERr:PHASe:ERR:DMRS?", SCPI_3GPP_GetVsaRstMPErrPhaseErrDmrs, 0, SCPI_QUERY_CMD},  // MPErr.PhaseErrDmrs

    // Spectrum Flatness 视图
    {"WT[:LTE][:NR]:SENSe:FETCh:SPECtrum:FLATness:MAXFlatNum?", SCPI_3GPP_GetVsaRstFlatnessMaxFlatNum, 0, SCPI_QUERY_CMD},  // Flatness.MaxFlatNum
    {"WT[:LTE][:NR]:SENSe:FETCh:SPECtrum:FLATness:RANGe?", SCPI_3GPP_GetVsaRstFlatnessRange, 0, SCPI_QUERY_CMD},  // Flatness.RangeType
    {"WT[:LTE][:NR]:SENSe:FETCh:SPECtrum:FLATness:Ripple?", SCPI_3GPP_GetVsaRstFlatnessRipple, 0, SCPI_QUERY_CMD},  // Flatness.Ripple
    {"WT[:LTE][:NR]:SENSe:FETCh:SPECtrum:FLATness:CROSs:Ripple?", SCPI_3GPP_GetVsaRstFlatnessCrossRipple, 0, SCPI_QUERY_CMD},  // Flatness.MaxRSubMinR
    {"WT[:LTE][:NR]:SENSe:FETCh:SPECtrum:FLATness:ALL:Ripple?", SCPI_3GPP_GetVsaRstFlatnessAllRipple, 0, SCPI_QUERY_CMD},  // Ripple + MaxRSubMinR

    // Inband Emission 视图
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:INEMis:EMISsion:DATA?", SCPI_3GPP_GetVsaRstInEmisEmission, 0, SCPI_QUERY_CMD},  // InEmis.RBEmis
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:INEMis:EMISsion:MASK:DATA?", SCPI_3GPP_GetVsaRstInEmisEmisRef, 0, SCPI_QUERY_CMD},  // InEmis.EmisRef
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:INEMis:EMISsion:MASK:SEG:DATA?", SCPI_3GPP_GetVsaRstInEmisEmisRefSeg, 0, SCPI_QUERY_CMD},  // InEmis.EmisRefSeg
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:INEMis:MARGin:MIN?", SCPI_3GPP_GetVsaRstInEmisMarginMin, 0, SCPI_QUERY_CMD},  // InEmis.MargMin
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:INEMis:MARGin:MIN:INDEx?", SCPI_3GPP_GetVsaRstInEmisMarginMinIdx, 0, SCPI_QUERY_CMD},  // InEmis.MargMinIdx

    // Summary 视图
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:UTRA:PASSed?", SCPI_3GPP_GetVsaRstUtraResult, 0, SCPI_QUERY_CMD},  // ACLR.UtraResult
    {"WT[:LTE]:SENSe:FETCh:EUTRa:PASSed?", SCPI_3GPP_GetVsaRstEutraResult, 0, SCPI_QUERY_CMD},  // ACLR.EutraResult
    {"WT[:NR]:SENSe:FETCh:NRUTRa:PASSed?", SCPI_3GPP_GetVsaRstNrUtraResult, 0, SCPI_QUERY_CMD},  // ACLR.NrUtraResult
    {"WT:CELLular:SENSe:FETCh:ACLR:RESUlt?", SCPI_3GPP_GetVsaRstACLRResult, 0, SCPI_QUERY_CMD},  // ACLR.AclrResult
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:SEM:PASSed?", SCPI_3GPP_GetVsaRstSEMResult, 0, SCPI_QUERY_CMD},  // Spectral.SEMResult
    {"WT[:NR][:NIOT]:SENSe:FETCh:RECEived:POWEr?", SCPI_3GPP_GetVsaRstReferenceSignalReceivingPower, 0, SCPI_QUERY_CMD},  // RSRP
    {"WT[:NR][:NIOT]:SENSe:FETCh:RECEived:INDIcation?", SCPI_3GPP_GetVsaRstReceivedSignalStrengthIndication, 0, SCPI_QUERY_CMD},  // RSSI
    {"WT[:NR][:NIOT]:SENSe:FETCh:RECEived:QUALity?", SCPI_3GPP_GetVsaRstReferenceSignalReceivingQuality, 0, SCPI_QUERY_CMD},  // RSRQ
    {"WT[:NR][:NIOT]:SENSe:FETCh:RECEived:SNR?", SCPI_3GPP_GetVsaRstSignaltoNoiseRatio, 0, SCPI_QUERY_CMD},  // SNR
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:INEMis:PASSed?", SCPI_3GPP_GetVsaRstInEmisResult, 0, SCPI_QUERY_CMD},  // InEmis.InEmisResult

    // Data Info value
    {"WT[:CELLular][:LTE][:NR][:NIOT]:SENSe:FETCh:LINKdirect?", SCPI_3GPP_GetVsaRstLinkDirect, 0, SCPI_QUERY_CMD},  // LinkDirect
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:CHANnel?", SCPI_3GPP_GetVsaRstChannel, 0, SCPI_QUERY_CMD},  // Channel
    {"WT[:CELLular][:LTE][:NR]:SENSe:FETCh:CODEword?", SCPI_3GPP_GetVsaRstCodeword, 0, SCPI_QUERY_CMD},  // Codeword
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:CW:MODUlate?", SCPI_3GPP_GetVsaCwModulate, 0, SCPI_QUERY_CMD},  // Cw.Modulate
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:CW:SCRAmbling?", SCPI_3GPP_GetVsaRstCwScrambling, 0, SCPI_QUERY_CMD},  // Cw.Scrambling
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:CW:CHANnel:CODIng:TYPE?", SCPI_3GPP_GetVsaRstCwChannelCodingType, 0, SCPI_QUERY_CMD},  // Cw.ChannelCodingType
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:CW:CRC?", SCPI_3GPP_GetVsaRstCwCrc, 0, SCPI_QUERY_CMD},  // Cw.Crc

    // PHY Bit Information value
    {"WT[:CELLular][:WCDMA]:SENSe:FETCh:CW:BITLen?", SCPI_3GPP_GetVsaRstBitLen, 0, SCPI_QUERY_CMD},  // BitLen
    {"WT[:CELLular][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:CW:BITSeq?", SCPI_3GPP_GetVsaRstBitSeq, 0, SCPI_QUERY_CMD},  // Cw[i].BitSeq

    // 以下为具体的协议相关
    // Error Vector Magnitude(EVM)
    {"WT:CELLular:SENSe:FETCh:EVM:RMS?", SCPI_3GPP_GetVsaRstEvmRms, 0, SCPI_QUERY_CMD},  // Evm.EvmRms
    {"WT:LTE:SENSe:FETCh:EVM:RMS?", SCPI_3GPP_GetVsaRstEvmRms, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:EVM:RMS?", SCPI_3GPP_GetVsaRstEvmRms, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:EVM:RMS?", SCPI_3GPP_GetVsaRstEvmRms, 0, SCPI_QUERY_CMD},

    {"WT:CELLular:SENSe:FETCh:EVM:PEAK?", SCPI_3GPP_GetVsaRstEvmPeak, 0, SCPI_QUERY_CMD},  // Evm.PeakEvmPCT(not found)
    {"WT:LTE:SENSe:FETCh:EVM:PEAK?", SCPI_3GPP_GetVsaRstEvmPeak, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:EVM:PEAK?", SCPI_3GPP_GetVsaRstEvmPeak, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:EVM:PEAK?", SCPI_3GPP_GetVsaRstEvmPeak, 0, SCPI_QUERY_CMD},

    {"WT:CELLular:SENSe:FETCh:EVM:DMRS?", SCPI_3GPP_GetVsaRstEvmDmrs, 0, SCPI_QUERY_CMD},  // Evm.DmrsEvmPCT(not found)
    {"WT:LTE:SENSe:FETCh:EVM:DMRS?", SCPI_3GPP_GetVsaRstEvmDmrs, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:EVM:DMRS?", SCPI_3GPP_GetVsaRstEvmDmrs, 0, SCPI_QUERY_CMD},
    {"WT:NIOT:SENSe:FETCh:EVM:DMRS?", SCPI_3GPP_GetVsaRstEvmDmrs, 0, SCPI_QUERY_CMD},

    // LTE
    {"WT:LTE:SENSe:FETCh:POWEr:DYNAmics:NUM?", SCPI_3GPP_GetVsaRstPowerDynamicsNum, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:POWEr:DYNAmics:TRACe:TIME?", SCPI_3GPP_GetVsaRstPowerDynamicsTraceTime, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:POWEr:DYNAmics:TRACe:POWEr?", SCPI_3GPP_GetVsaRstPowerDynamicsTracePower, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:POWEr:DYNAmics:OUTEr:POWEr?", SCPI_3GPP_GetVsaRstPowerDynamicsOuterPower, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:EVM:SYMBol:CARRier:LENGth?", SCPI_3GPP_GetVsaRstEvmSymbolCarrierLength, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:EVM:SYMBol:CARRier:NUM?", SCPI_3GPP_GetVsaRstEvmSymbolCarrierNum, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCh:EVM:SYMBol:CARRier:EVM?", SCPI_3GPP_GetVsaRstEvmSymbolCarrierEvm, 0, SCPI_QUERY_CMD},
};

//平均相关的命令
static const scpi_command_t scpi_wt_3gpp_average_cmd[] = {
    {"WT:NBIOT:SENSe:CONFigure:AVG:CLEAn", CleanAverage, 0, SCPI_SEQUENTIAL},
    {"WT:NBIOT:SENSe:CONFigure:AVG:SETting", Set3GPPAverageSetting, 0, SCPI_SEQUENTIAL},
    // {"WT:SENSe:CONFigure:AVG:MIN:COUNt", SetAverageMinCount, 0, SCPI_SEQUENTIAL}, //3gpp暂不支持多次采集平均
    //{"WT[:WIFI]:SENSe:FETCh:MIMO:AVG:COMPosite:RESUlt?", GetMimoAverageVompositeResult, 0, SCPI_QUERY_CMD},//3gpp暂不支持多流平均
    {"WT:NBIOT:SENSe:FETCh:AVG:RESUlt?", Get3GPPAverageResult, 0, SCPI_QUERY_CMD},
    {"WT:NBIOT:SENSe:FETCh:AVG:RESUlt:ARB?", Get3GPPAverageResultARB, 0, SCPI_QUERY_CMD},    
};

static const scpi_command_t scpi_wt_3gpp_gsm_analyze_cmd[] = {
    {"WT:GSM:SENSe:CONFigure:ANALy:MEASure:CONTrol:SlOT:OFFSet", SetVsaGsmSlotOffset, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONFigure:ANALy:MEASure:CONTrol:NUMBof:SLOT", SetVsaGsmNumbOfslot, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONFigure:ANALy:MEASure:CONTrol:MEASure:SLOT", SetVsaGsmMeasureSlot, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:PVT:FILTER", SetVsaGsmPvtFilter, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:SPEC:MOD:OFFSet:STATe#", SetVsaGsmSpecModOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:SPEC:MOD:FREQ:OFFSet#", SetVsaGsmSpecModFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:SPEC:SWITch:OFFSet:STATe#", SetVsaGsmSpecSwtOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:SPEC:SWITch:FREQ:OFFSet#", SetVsaGsmSpecSwtFreqOffset, 0, SCPI_SEQUENTIAL},
    // GSM Limits Modulation
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMRms:LIMIt", SetVsaGsmLimitModEvmRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMRms:CURRent", SetVsaGsmLimitModEvmRmsCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMRms:AVERage", SetVsaGsmLimitModEvmRmsAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMRms:MAX", SetVsaGsmLimitModEvmRmsMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMPeak:LIMIt", SetVsaGsmLimitModEvmPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMPeak:CURRent", SetVsaGsmLimitModEvmPeakCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMPeak:AVERage", SetVsaGsmLimitModEvmPeakAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMPeak:MAX", SetVsaGsmLimitModEvmPeakMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMThreshold:LIMIt", SetVsaGsmLimitModEvmThreLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:EVMThreshold:STATe", SetVsaGsmLimitModEvmThreState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRrms:LIMIt", SetVsaGsmLimitModMerrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRrms:CURRent", SetVsaGsmLimitModMerrRmsCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRrms:AVERage", SetVsaGsmLimitModMerrRmsAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRrms:MAX", SetVsaGsmLimitModMerrRmsMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRpeak:LIMIt", SetVsaGsmLimitModMerrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRpeak:CURRent", SetVsaGsmLimitModMerrPeakCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRpeak:AVERage", SetVsaGsmLimitModMerrPeakAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRpeak:MAX", SetVsaGsmLimitModMerrPeakMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRthreshold:LIMIt", SetVsaGsmLimitModMerrThreLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:MERRthreshold:STATe", SetVsaGsmLimitModMerrThreState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrrms:LIMIt", SetVsaGsmLimitModPherRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrrms:CURRent", SetVsaGsmLimitModPherRmsCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrrms:AVERage", SetVsaGsmLimitModPherRmsAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrrms:MAX", SetVsaGsmLimitModPherRmsMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrpeak:LIMIt", SetVsaGsmLimitModPherPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrpeak:CURRent", SetVsaGsmLimitModPherPeakCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrpeak:AVERage", SetVsaGsmLimitModPherPeakAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrpeak:MAX", SetVsaGsmLimitModPherPeakMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrthreshold:LIMIt", SetVsaGsmLimitModPherThreLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:PHERrthreshold:STATe", SetVsaGsmLimitModPherThreState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQOFfset:LIMIt", SetVsaGsmLimitModIqOffSetLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQOFfset:CURRent", SetVsaGsmLimitModIqOffSetCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQOFfset:AVERage", SetVsaGsmLimitModIqOffSetAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQOFfset:MAX", SetVsaGsmLimitModIqOffSetMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQIMb:LIMIt", SetVsaGsmLimitModIqImbLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQIMb:CURRent", SetVsaGsmLimitModIqImbCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQIMb:AVERage", SetVsaGsmLimitModIqImbAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:IQIMb:MAX", SetVsaGsmLimitModIqImbMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:FREQerr:LIMIt", SetVsaGsmLimitModFreqErrLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:FREQerr:CURRent", SetVsaGsmLimitModFreqErrCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:FREQerr:AVERage", SetVsaGsmLimitModFreqErrAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:FREQerr:MAX", SetVsaGsmLimitModFreqErrMax, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:TIMErr:LIMIt", SetVsaGsmLimitModTimeErrLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:TIMErr:CURRent", SetVsaGsmLimitModTimeErrCurrent, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:TIMErr:AVERage", SetVsaGsmLimitModTimeErrAverage, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:MODE#:TIMErr:MAX", SetVsaGsmLimitModTimeErrMax, 0, SCPI_SEQUENTIAL},
    // GSM Limits Power vs Time
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:AVG#:STATe", SetVsaGsmLimitPvtAvgState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:AVG#:FPCL", SetVsaGsmLimitPvtFpcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:AVG#:TPCL", SetVsaGsmLimitPvtTpcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:AVG#:LOWEr", SetVsaGsmLimitPvtLower, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:AVG#:UPPEr", SetVsaGsmLimitPvtUpper, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:GPeriod:STATe", SetVsaGsmLimitPvtGState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVT:GPeriod:LIMIt", SetVsaGsmLimitPvtGLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STATe", SetVsaGsmLimitPURiseState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STARt:TIME", SetVsaGsmLimitPURiseStartTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STARt:RELAtive", SetVsaGsmLimitPURiseStartRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STARt:ABS:STATe", SetVsaGsmLimitPURiseAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STARt:ABS:LIMIt", SetVsaGsmLimitPURiseAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STOP:TIME", SetVsaGsmLimitPURiseStopTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STOP:RELAtive", SetVsaGsmLimitPURiseStopRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STOP:ABS:STATe", SetVsaGsmLimitPURiseStopAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:STOP:ABS:LIMIt", SetVsaGsmLimitPURiseStopAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:DYNAmic#:STATe", SetVsaGsmLimitPURiseDynamicState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:DYNAmic#:STARt:PCL", SetVsaGsmLimitPURiseDynamicStartPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:DYNAmic#:END:PCL", SetVsaGsmLimitPURiseDynamicEndPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:RISEdge#:DYNAmic#:CORRection", SetVsaGsmLimitPURiseDynamicCorrection, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STATe", SetVsaGsmLimitPUUseState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STARt:TIME", SetVsaGsmLimitPUUseStartTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STARt:RELAtive", SetVsaGsmLimitPUUseStartRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STARt:ABS:STATe", SetVsaGsmLimitPUUseStartAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STARt:ABS:LIMIt", SetVsaGsmLimitPUUseStartAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STOP:TIME", SetVsaGsmLimitPUUseStopTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STOP:RELAtive", SetVsaGsmLimitPUUseStopRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STOP:ABS:STATe", SetVsaGsmLimitPUUseStopAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:STOP:ABS:LIMIt", SetVsaGsmLimitPUUseStopAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:DYNAmic#:STATe", SetVsaGsmLimitPUUseDynamicState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:DYNAmic#:STARt:PCL", SetVsaGsmLimitPUUseDynamicStartPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:DYNAmic#:END:PCL", SetVsaGsmLimitPUUseDynamicEndPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:USEPart#:DYNAmic#:CORRection", SetVsaGsmLimitPUUseDynamicCorrection, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STATe", SetVsaGsmLimitPUFallState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STARt:TIME", SetVsaGsmLimitPUFallStartTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STARt:RELAtive", SetVsaGsmLimitPUFallStartRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STARt:ABS:STATe", SetVsaGsmLimitPUFallAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STARt:ABS:LIMIt", SetVsaGsmLimitPUFallAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STOP:TIME", SetVsaGsmLimitPUFallStopTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STOP:RELAtive", SetVsaGsmLimitPUFallStopRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STOP:ABS:STATe", SetVsaGsmLimitPUFallStopAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:STOP:ABS:LIMIt", SetVsaGsmLimitPUFallStopAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:DYNAmic#:STATe", SetVsaGsmLimitPUFallDynamicState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:DYNAmic#:STARt:PCL", SetVsaGsmLimitPUFallDynamicStartPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:DYNAmic#:END:PCL", SetVsaGsmLimitPUFallDynamicEndPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTUp#:FALLedge#:DYNAmic#:CORRection", SetVsaGsmLimitPUFallDynamicCorrection, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STATe", SetVsaGsmLimitPLUseState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STARt:TIME", SetVsaGsmLimitPLUseStartTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STARt:RELAtive", SetVsaGsmLimitPLUseStartRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STARt:ABS:STATe", SetVsaGsmLimitPLUseStartAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STARt:ABS:LIMIt", SetVsaGsmLimitPLUseStartAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STOP:TIME", SetVsaGsmLimitPLUseStopTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STOP:RELAtive", SetVsaGsmLimitPLUseStopRelative, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STOP:ABS:STATe", SetVsaGsmLimitPLUseStopAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:STOP:ABS:LIMIt", SetVsaGsmLimitPLUseStopAbsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:DYNAmic#:STATe", SetVsaGsmLimitPLUseDynamicState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:DYNAmic#:STARt:PCL", SetVsaGsmLimitPLUseDynamicStartPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:DYNAmic#:END:PCL", SetVsaGsmLimitPLUseDynamicEndPcl, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:PVTLow#:USEPart#:DYNAmic#:CORRection", SetVsaGsmLimitPLUseDynamicCorrection, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:MOD#:REFLow:POWEr", SetVsaGsmLimitSpecModRefLowPower, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:MOD#:REFHigh:POWEr", SetVsaGsmLimitSpecModRefHighPower, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:MOD#:FREQ:OFFSet#:STATE", SetVsaGsmLimitSpecModFreqOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:MOD#:FREQ:OFFSet#:LOW:PWRRel", SetVsaGsmLimitSpecModFreqOffsetLowPowerRel, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:MOD#:FREQ:OFFSet#:HIGH:PWRRel", SetVsaGsmLimitSpecModFreqOffsetHighPowerRel, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:MOD#:FREQ:OFFSet#:PWRAbs", SetVsaGsmLimitSpecModFreqOffsetPowerAbs, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:SWIT#:REFPwr#:POWEr", SetVsaGsmLimitSpecSwitRefPower, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:SWIT#:REFPwr#:LEVEl", SetVsaGsmLimitSpecSwitRefLevel, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:SWIT#:FREQ:OFFSet#:STATE", SetVsaGsmLimitSpecSwitFreqOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SENSe:CONF:LIMIt:SPEC:SWIT#:FREQ:OFFSet#:PWRRel#", SetVsaGsmLimitSpecSwitFreqOffsetPowerRel, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_gsm_wavegen_cmd[] = {
    {"WT:GSM:SOURce:SEQUence:MODE", SCPI_GSM_SetSequenceMode, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SYMBol:RATE:MODE", SCPI_GSM_SetSymbolRateMode, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SEQUence:LENGth", SCPI_GSM_SetSequenceLength, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT:TIME:MODE", SCPI_GSM_SetSlotTimeMode, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:FILTer:PARAm", SCPI_GSM_SetFilterParam, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:POWEr:RAMP:TIME", SCPI_GSM_SetPowerRampTime, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:POWEr:RAMP:RISE:DELAy", SCPI_GSM_SetPowerRampRiseDelay, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:POWEr:RAMP:FALL:DELAy", SCPI_GSM_SetPowerRampFallDelay, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:SLOT#:BURSt:TYPE", SCPI_GSM_SetSlotBurstType, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:LEVEl", SCPI_GSM_SetSlotLevel, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:ATTEnuation", SCPI_GSM_SetSlotAttenuation, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:REPEtition", SCPI_GSM_SetSlotReptition, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:REPEtition:SLOT:NUM", SCPI_GSM_SetSlotRepSlotNum, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:SLOT#:NB#:MODUlate", SCPI_GSM_SetSlotNBModulate, 0, SCPI_SEQUENTIAL},      
    {"WT:GSM:SOURce:SLOT#:NB#:RATE:TYPE", SCPI_GSM_SetSlotNBRateType, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:NB#:DATA:TYPE", SCPI_GSM_SetSlotNBDataType, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:NB#:USE:STEAl:FLAG", SCPI_GSM_SetSlotNBUseStealFlag, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:NB#:FLAG:VALUe", SCPI_GSM_SetSlotNBFlagValue, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:NB#:TSC:SET", SCPI_GSM_SetSlotNBTscSet, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:NB#:TSC", SCPI_GSM_SetSlotNBTsc, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:SLOT#:AB:DATA:TYPE", SCPI_GSM_SetSlotABDataType, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:AB:SYNC", SCPI_GSM_SetSlotABSync, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:SLOT#:SB:DATA:TYPE", SCPI_GSM_SetSlotSBDataType, 0, SCPI_SEQUENTIAL},
    {"WT:GSM:SOURce:SLOT#:SB:ETSC", SCPI_GSM_SetSlotSBEtsc, 0, SCPI_SEQUENTIAL},

    {"WT:GSM:SOURce:SLOT#:FCB:FIXEd", SCPI_GSM_SetSlotFCBFixed, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_gsm_result_cmd[] = {
    // Summary
    {"WT:GSM:SENSe:FETCh:SUMMary?", SCPI_3GPP_GetVsaRstSummaryGSM, 0, SCPI_QUERY_CMD},

    {"WT:GSM:SENSe:FETCh:POWEr:SYMBol?", SCPI_3GPP_GetVsaRstPowerSymbol, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:POWEr?", SCPI_3GPP_GetVsaRstPowerPower, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:MEASure:BURSt:NUM?", SCPI_3GPP_GetVsaRstPowerMeasureBurstNum, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:BURSt:UP:MASK?", SCPI_3GPP_GetVsaRstPowerBurstUpMask, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:BURSt:DOWN:MASK?", SCPI_3GPP_GetVsaRstPowerBurstDownMask, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:BURSt:XMARk?", SCPI_3GPP_GetVsaRstPowerBurstXMark, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:BURSt:AVERage:POWEr?", SCPI_3GPP_GetVsaRstPowerBurstAveragePower, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:BURSt:TSC:TYPE?", SCPI_3GPP_GetVsaRstPowerBurstTscType, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:BURSt:TYPe?", SCPI_3GPP_GetVsaRstPowerBurstType, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:MEASure:PART:MIN?", SCPI_3GPP_GetVsaRstPowerMeasurePartMin, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:MEASure:PART:MAX?", SCPI_3GPP_GetVsaRstPowerMeasurePartMax, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:POWEr:MEASure:SV?", SCPI_3GPP_GetVsaRstPowerMeasureSV, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:MODUlation:FREQuency?", SCPI_3GPP_GetVsaRstSpectrumModulationFrequency, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:MODUlation:POWEr?", SCPI_3GPP_GetVsaRstSpectrumModulationPower, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:MODUlation:MASK?", SCPI_3GPP_GetVsaRstSpectrumModulationMask, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:MODUlation:SYMBol?", SCPI_3GPP_GetVsaRstSpectrumModulationSymbol, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:MODUlation:TIME?", SCPI_3GPP_GetVsaRstSpectrumModulationTime, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:SWITch:FREQuency?", SCPI_3GPP_GetVsaRstSpectrumSwitchFrequency, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:SWITch:POWEr?", SCPI_3GPP_GetVsaRstSpectrumSwitchPower, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:SWITch:MASK?", SCPI_3GPP_GetVsaRstSpectrumSwitchMask, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:SWITch:SYMBol?", SCPI_3GPP_GetVsaRstSpectrumSwitchSymbol, 0, SCPI_QUERY_CMD},
    {"WT:GSM:SENSe:FETCh:SPECtrum:SWITch:TIME?", SCPI_3GPP_GetVsaRstSpectrumSwitchTime, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_3gpp_wcdma_wavegen_cmd[] = {
    {"WT:WCDMA:SOURce:CONFigure:WAVE:LINK", SCPI_WCDMA_SetLinkDirect, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:MODE", SCPI_WCDMA_UL_SetGenWaveMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:SCRAmbling:MODE", SCPI_WCDMA_UL_SetGenScramblingMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:SCRAmbling:CODE", SCPI_WCDMA_UL_SetGenScramblingCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:DPCCh:SLOT:FORMat", SCPI_WCDMA_UL_SetGenDPCCHSlotFormat, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:DPCCh:TFCI", SCPI_WCDMA_UL_SetGenDPCCHTfci, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:DPCCh:TPC:DATA:TYPE", SCPI_WCDMA_UL_SetGenDPCCHTpcDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:UL:DPCCh:POWEr", SCPI_WCDMA_UL_SetGenDPCCHPower, 0, SCPI_SEQUENTIAL},
    // DPDCH
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:STATe", SCPI_WCDMA_SetULDpdchState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:OVERall:SYMBol:RATE", SCPI_WCDMA_SetULDpdchOverallSymbolRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:STATe", SCPI_WCDMA_SetULDpdchChannelCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:INTErleaver:STATe", SCPI_WCDMA_SetULDpdchChannelCodingInterleaverState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:STATe", SCPI_WCDMA_SetULDpdchChannelCodingDtchState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:DATA:TYPE", SCPI_WCDMA_SetULDpdchChannelCodingDtchDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:INITialization", SCPI_WCDMA_SetULDpdchChannelCodingDtchInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:TRANsport:TIME:INTErval", SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportTimeInterval, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:TRANsport:BLOCks", SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportBlocks, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:TRANsport:BLOCk:SIZE", SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportBlockSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:SIZE:OF:CRC", SCPI_WCDMA_SetULDpdchChannelCodingDtchSizeOfCRC, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:RATE:MATChing:ATTRibute", SCPI_WCDMA_SetULDpdchChannelCodingDtchRateMatchingAttribute, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:ERROr:PROTection", SCPI_WCDMA_SetULDpdchChannelCodingDtchErrorProtection, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DTCH#:INTErleaver:STATe", SCPI_WCDMA_SetULDpdchChannelCodingDtchInterleaverState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:STATe", SCPI_WCDMA_SetULDpdchChannelCodingDcchState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:DATA:TYPE", SCPI_WCDMA_SetULDpdchChannelCodingDcchDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:INITialization", SCPI_WCDMA_SetULDpdchChannelCodingDcchInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:TRANsport:TIME:INTErval", SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportTimeInterval, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:TRANsport:BLOCks", SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportBlocks, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:TRANsport:BLOCk:SIZE", SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportBlockSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:SIZE:OF:CRC", SCPI_WCDMA_SetULDpdchChannelCodingDcchSizeOfCRC, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:RATE:MATChing:ATTRibute", SCPI_WCDMA_SetULDpdchChannelCodingDcchRateMatchingAttribute, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:ERROr:PROTection", SCPI_WCDMA_SetULDpdchChannelCodingDcchErrorProtection, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:CODIng:DCCH:INTErleaver:STATe", SCPI_WCDMA_SetULDpdchChannelCodingDcchInterleaverState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:PHY:CHANnel:CONFig:DATA:TYPE#", SCPI_WCDMA_SetULDpdchPhyChannelConfigDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:PHY:CHANnel:CONFig:INITialization#", SCPI_WCDMA_SetULDpdchPhyChannelConfigInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:DPDCh:CHANnel:POWEr", SCPI_WCDMA_SetULDpdchChannelPower, 0, SCPI_SEQUENTIAL},

    //HS-DPCCH
    {"WT:WCDMA:SOURce:WAVE:UL:HS:DPCCh:STATe", SCPI_WCDMA_SetULHsdpcchState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:HS:DPCCh:STARt:DELAy", SCPI_WCDMA_SetULHsdpcchStartDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:HS:DPCCh:INTEr:TTIDist", SCPI_WCDMA_SetULHsdpcchInterTTIDist, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:HS:DPCCh:ACKPattern#", SCPI_WCDMA_SetULHsdpcchACKPattern, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:HS:DPCCh:CQIPattern#", SCPI_WCDMA_SetULHsdpcchCQIPattern, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:UL:HS:DPCCh:POWEr", SCPI_WCDMA_SetULHsdpcchPower, 0, SCPI_SEQUENTIAL},

    //HS-DPCCH
    {"WT:WCDMA:SOURce:WAVE:UL:EDPCch:STATe", SCPI_WCDMA_SetULEdpcchState, 0, SCPI_SEQUENTIAL},

    //HS-DPCCH
    {"WT:WCDMA:SOURce:WAVE:UL:EDPDch:STATe", SCPI_WCDMA_SetULEdpdchState, 0, SCPI_SEQUENTIAL},
    //DL
    {"WT:WCDMA:SOURce:WAVE:DL:COMMon:SCRAmbling:STATe", SCPI_WCDMA_SetDLCommonScramblingState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:COMMon:SCRAmbling:CODE", SCPI_WCDMA_SetDLCommonScramblingCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:COMMon:OCNS:MODE:STATe", SCPI_WCDMA_SetDLCommonOCNSModeState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:COMMon:OCNS:MODE", SCPI_WCDMA_SetDLCommonOCNSMode, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:PCPIch:STATe", SCPI_WCDMA_SetDLPCPICHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCPIch:SYMBrate", SCPI_WCDMA_SetDLPCPICHSymbRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCPIch:CHANcode", SCPI_WCDMA_SetDLPCPICHChanCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCPIch:POWEr", SCPI_WCDMA_SetDLPCPICHPower, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:PSCH:STATe", SCPI_WCDMA_SetDLPSCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PSCH:SYMBrate", SCPI_WCDMA_SetDLPSCHSymbRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PSCH:POWEr", SCPI_WCDMA_SetDLPSCHPower, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:PCCPch:STATe", SCPI_WCDMA_SetDLPCCPCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCCPch:SYMBrate", SCPI_WCDMA_SetDLPCCPCHSymbRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCCPch:CHANcode", SCPI_WCDMA_SetDLPCCPCHChanCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCCPch:DATAtype", SCPI_WCDMA_SetPCCPCHDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCCPch:INIT", SCPI_WCDMA_SetPCCPCHInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:PCCPch:POWEr", SCPI_WCDMA_SetPCCPCHPower, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:DPCH:NUM", SCPI_WCDMA_SetDLDPCHNum, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:STATe", SCPI_WCDMA_SetDLDPCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:SLOT:FORMat", SCPI_WCDMA_SetDLDPCHSlotFormat, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:SYMBol:RATE", SCPI_WCDMA_SetDLDPCHSymbolRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODE", SCPI_WCDMA_SetDLDPCHChanCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:STATe", SCPI_WCDMA_SetDLDPCHChanCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:INTEr:LEAVer2:STATe", SCPI_WCDMA_SetDLDPCHChanCodingInterleaver2State, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:STATe", SCPI_WCDMA_SetDLDPCHChanCodingDTCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:DATA:TYPE", SCPI_WCDMA_SetDLDPCHChanCodingDTCHDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:INIT", SCPI_WCDMA_SetDLDPCHChanCodingDTCHInit, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:TRANsport:TIME:INTErval", SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransTimeInterval, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:TRANsport:BLOCk:NUM", SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransBlockNum, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:TRANsport:BLOCk:SIZE", SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransBlockSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:CRC:SIZE", SCPI_WCDMA_SetDLDPCHChanCodingDTCHCrcSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:RATE:MATCh:ATTRibute", SCPI_WCDMA_SetDLDPCHChanCodingDTCHRateMatchAttribute, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:ERROr:PROTect", SCPI_WCDMA_SetDLDPCHChanCodingDTCHErrorProtect, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DTCH#:INTEr:LEAVer:STATe", SCPI_WCDMA_SetDLDPCHChanCodingDTCHInterleaverState, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:STATe", SCPI_WCDMA_SetDLDPCHChanCodingDCCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:DATA:TYPE", SCPI_WCDMA_SetDLDPCHChanCodingDCCHDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:INIT", SCPI_WCDMA_SetDLDPCHChanCodingDCCHInit, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:TRANsport:TIME:INTErval", SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransTimeInterval, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:TRANsport:BLOCk:NUM", SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransBlockNum, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:TRANsport:BLOCk:SIZE", SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransBlockSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:CRC:SIZE", SCPI_WCDMA_SetDLDPCHChanCodingDCCHCrcSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:RATE:MATCh:ATTRibute", SCPI_WCDMA_SetDLDPCHChanCodingDCCHRateMatchAttribute, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:ERROr:PROTect", SCPI_WCDMA_SetDLDPCHChanCodingDCCHErrorProtect, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:CHAN:CODing:DCCH:INTEr:LEAVer:STATe", SCPI_WCDMA_SetDLDPCHChanCodingDCCHInterleaverState, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:DATA:TYPE", SCPI_WCDMA_SetDLDPCHDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:INIT", SCPI_WCDMA_SetDLDPCHInit, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:TPC:DATA:TYPE", SCPI_WCDMA_SetDLDPCHTpcDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:POWEr", SCPI_WCDMA_SetDLDPCHPower, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:DL:DPCH#:TIMIng:OFFSet", SCPI_WCDMA_SetDLDPCHTimingOffset, 0, SCPI_SEQUENTIAL},

    //Filter
    {"WT:WCDMA:SOURce:WAVE:GENEral:FILEer:TYPE", SCPI_WCDMA_SetULGeneralFilterType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:WAVE:GENEral:SEQ:LEN", SCPI_WCDMA_SetULGeneralSeqLen, 0, SCPI_SEQUENTIAL},

    // S-SCH
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SSCH:STATe", SCPI_WCDMA_SetDLSSCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SSCH:SYMBol:RATE", SCPI_WCDMA_SetDLSSCHSymbolRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SSCH:SYMBol:POWEr", SCPI_WCDMA_SetDLSSCHPower, 0, SCPI_SEQUENTIAL},

    // SCCPCH
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:STATe", SCPI_WCDMA_SetDLSCCPCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:SLOT:FORMat", SCPI_WCDMA_SetDLSCCPCHSlotFormat, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:SYMBol:RATE", SCPI_WCDMA_SetDLSCCPCHSymbolRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:CHANnel:CODE", SCPI_WCDMA_SetDLSCCPCHChannelCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:POWEr", SCPI_WCDMA_SetDLSCCPCHPower, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:DATA:TYPE", SCPI_WCDMA_SetDLSCCPCHDataType, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:INITialization", SCPI_WCDMA_SetDLSCCPCHInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SOURce:CONFigure:WAVE:DL:SCCPCh:TIME:OFFSet", SCPI_WCDMA_SetDLSCCPCHTimeOffset, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_wcdma_analyze_cmd[] = {
    // UL General Configuration
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:SCRAmbling:CODE", SCPI_WCDMA_UL_General_SetAlzScramblingCode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:DPCCh:SLOT:FORMat", SCPI_WCDMA_UL_General_SetAlzDPCCHSlotFormat, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:CONFiguration", SCPI_WCDMA_UL_General_SetAlzConfiguration, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:DPDCh:AVAIlable", SCPI_WCDMA_UL_General_SetAlzDPDCHAvailable, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:MEASurement:LENGth", SCPI_WCDMA_UL_General_SetAlzMeasurementLength, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:SYNChronisation", SCPI_WCDMA_UL_General_SetAlzSynchronisation, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:SLOT:NEMBer", SCPI_WCDMA_UL_General_SetAlzSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:UL:GENEral:CDP:SPREading:FACTor", SCPI_WCDMA_UL_General_SetAlzCDPSpreadingFactor, 0, SCPI_SEQUENTIAL},
    // Measure
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PEAK:MAGNitude:ERROr:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzPeakMagnErrLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PEAK:MAGNitude:ERROr:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzPeakMagnErrLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:RMS:MAGNitude:ERROr:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzRmsMagnErrLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:RMS:MAGNitude:ERROr:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzRmsMagnErrLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PEAK:EVM:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzPeakEvmLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PEAK:EVM:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzPeakEvmLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:RMS:EVM:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzRmsEvmLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:RMS:EVM:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzRmsEvmLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PEAK:PHASe:ERROr:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzPeakPhaseErrLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PEAK:PHASe:ERROr:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzPeakPhaseErrLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:RMS:PHASe:ERROr:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzRmsPhaseErrLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:RMS:PHASe:ERROr:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzRmsPhaseErrLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:CARRier:FREQuency:ERROr:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzCarrFreqErrLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:CARRier:FREQuency:ERROr:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzCarrFreqErrLimitValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:PHASe:DISContinuity:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzPhaseDiscLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:UPPEr:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzUpperLimit, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:DYNAmic:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzDynamicLimit, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:ACLR:LIMIt1:MODE", SCPI_WCDMA_Measure_SetAlzAclrLimit1Mode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:ACLR:UTRA:LIMIt1:VALUe", SCPI_WCDMA_Measure_SetAlzAclrUtraLimit1Value, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:ACLR:LIMIt2:MODE", SCPI_WCDMA_Measure_SetAlzAclrLimit2Mode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:ACLR:UTRA:LIMIt2:VALUe", SCPI_WCDMA_Measure_SetAlzAclrUtraLimit2Value, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:EMIS:MASK:LIMIt:AD:MODE", SCPI_WCDMA_Measure_SetAlzEmisMaskLimitADMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:EMIS:MASK:LIMIt:AD:VALUe", SCPI_WCDMA_Measure_SetAlzEmisMaskLimitADValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:EMIS:MASK:LIMIt:EF:MODE", SCPI_WCDMA_Measure_SetAlzEmisMaskLimitEFMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:EMIS:MASK:LIMIt:EF:VALUe", SCPI_WCDMA_Measure_SetAlzEmisMaskLimitEFValue, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:MEASure:UNIT", SCPI_WCDMA_Measure_SetAlzMeasureUnit, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:ACLR:ABS:LIMIt:MODE", SCPI_WCDMA_Measure_SetAlzMeasureAclrAbsLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:MEASure:ABS:LIMIt:VALUe", SCPI_WCDMA_Measure_SetAlzMeasureAbsLimitValue, 0, SCPI_SEQUENTIAL},

    // DL General Configuration
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:SCRAmbling:STATe", SCPI_WCDMA_DL_General_SetAlzScramblingState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:SCRAmbling:CODE", SCPI_WCDMA_DL_General_SetAlzScramblingCode, 0, SCPI_SEQUENTIAL},

    // DL Channel Configuration
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:CPCH:PCPIch:STATe", SCPI_WCDMA_DL_CPCH_SetAlzPCPICHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:CPCH:PCPIch:POWEr", SCPI_WCDMA_DL_CPCH_SetAlzPCPICHPower, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:CPCH:PSCH:STATe", SCPI_WCDMA_DL_CPCH_SetAlzPSCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:CPCH:PSCH:POWEr", SCPI_WCDMA_DL_CPCH_SetAlzPSCHPower, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:CPCH:SSCH:STATe", SCPI_WCDMA_DL_CPCH_SetAlzSSCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:CPCH:SSCH:POWEr", SCPI_WCDMA_DL_CPCH_SetAlzSSCHPower, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH:NUM", SCPI_WCDMA_DL_DPCH_SetAlzDPCHNum, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:STATe", SCPI_WCDMA_DL_DPCH_SetAlzDPCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:SLOTformat", SCPI_WCDMA_DL_DPCH_SetAlzDPCHSlotFormat, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:SYMBrate", SCPI_WCDMA_DL_DPCH_SetAlzDPCHSymbRate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:CHANcode", SCPI_WCDMA_DL_DPCH_SetAlzDPCHChanCode, 0, SCPI_SEQUENTIAL},
#
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:STATe", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:INTErleaver2statate", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHInterleaver2Statate, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:STATe", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:TTI", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTTI, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:TBCOunt", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTbCount, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:TBSIze", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTbSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:CRC", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHCrc, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:RMATtribute", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHRmAttribute, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:EPROtection", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHEProtection, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DTCH#:INTErleaverstat", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHInterleaverStat, 0, SCPI_SEQUENTIAL},

    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:STATe", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHState, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:TTI", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTTI, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:TBCOunt", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTbCount, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:TBSIze", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTbSize, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:CRC", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHCrc, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:RMATtribute", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHRmAttribute, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:EPROtection", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHEProtection, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:DCH:DCCH:INTErleaverstat", SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHInterleaverStat, 0, SCPI_SEQUENTIAL},
    {"WT:WCDMA:SENSe:CONFigure:ANALy:DL:DPCH#:POWEr", SCPI_WCDMA_DL_DPCH_SetAlzDPCHPower, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_wcdma_result_cmd[] = {
    // UE Power
    {"WT[:WCDMA]:SENSe:FETCh:UEPOwer:PWR:OUT?", SCPI_3GPP_GetVsaRstUEPwrOut, 0, SCPI_QUERY_CMD}, // UEPower.PwrOut
    // Phase Discontinuity
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:PHASe:DIFF:OUT?", SCPI_3GPP_GetVsaRstPhaseDiffOut, 0, SCPI_QUERY_CMD}, // PhaseDis.PhaseDiffOut
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:DYNAmic:LIMIt?", SCPI_3GPP_GetVsaRstDynamicLimit, 0, SCPI_QUERY_CMD}, // PhaseDis.DynamicLimit
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:UPPEr:LIMIt?", SCPI_3GPP_GetVsaRstUpperLimit, 0, SCPI_QUERY_CMD}, // PhaseDis.UpperLimit
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:MAX:PHASe:DIS?", SCPI_3GPP_GetVsaRstMaxPhaseDis, 0, SCPI_QUERY_CMD}, // PhaseDis.MaxPhaseDis
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:MIN:SLOT:DIS?", SCPI_3GPP_GetVsaRstMinSlotDis, 0, SCPI_QUERY_CMD}, // PhaseDis.MinSlotDis
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:EXCEed:UPPEr:LIMIt:NUM?", SCPI_3GPP_GetVsaRstExceedUpperLimitNum, 0, SCPI_QUERY_CMD}, // PhaseDis.ExceedUpperLimitNum
    {"WT[:WCDMA]:SENSe:FETCh:PHASe:DIS:EXCEed:DYNAmic:LIMIt:NUM?", SCPI_3GPP_GetVsaRstExceedDynamicLimitNum, 0, SCPI_QUERY_CMD}, // PhaseDis.ExceedDynamicLimitNum
    // Frequency Error
    {"WT[:WCDMA]:SENSe:FETCh:FREQ:ERR:OUT?", SCPI_3GPP_GetVsaRstFreqErrOut, 0, SCPI_QUERY_CMD}, // FreqErr.FreqErrOut
    {"WT[:WCDMA]:SENSe:FETCh:FREQ:ERR:LIMIt?", SCPI_3GPP_GetVsaRstFreqErrLimit, 0, SCPI_QUERY_CMD}, // FreqErr.FreqErrLimit
    {"WT[:WCDMA]:SENSe:FETCh:FREQ:ERR?", SCPI_3GPP_GetVsaRstFreqErr, 0, SCPI_QUERY_CMD}, // FreqErr.FreqErr
    // Magnitude Error
    {"WT[:WCDMA]:SENSe:FETCh:MAGN:ERR:OUT?", SCPI_3GPP_GetVsaRstMagnErrOut, 0, SCPI_QUERY_CMD}, // MagnErr.MagnErrOut
    {"WT[:WCDMA]:SENSe:FETCh:MAGN:ERR:LIMIt?", SCPI_3GPP_GetVsaRstMagnErrLimit, 0, SCPI_QUERY_CMD}, // MagnErr.MagnErrLimit
    // Phase Error
    {"WT[:WCDMA]:SENSe:FETCh:MPERr:PHASe:ERR:OUT?", SCPI_3GPP_GetVsaRstMPErrPhaseErrOut, 0, SCPI_QUERY_CMD}, // MPErr.PhaseErrOut
    {"WT[:WCDMA]:SENSe:FETCh:MPERr:PHASe:ERR:LIMIt?", SCPI_3GPP_GetVsaRstMPErrPhaseErrLimit, 0, SCPI_QUERY_CMD}, // MPErr.PhaseErrLimit
    // Magnitude Error vs Chip
    {"WT:WCDMA:SENSe:MAGNitude:ERROr:VS:CHIP:MAGNerrchip?", SCPI_3GPP_GetVsaMagnitudeErrorVsChipMagnErrChip, 0, SCPI_QUERY_CMD}, // MPErr.MagnErrChip
    {"WT:WCDMA:SENSe:MAGNitude:ERROr:VS:CHIP:MAGNerrchiplimit?", SCPI_3GPP_GetVsaMagnitudeErrorVsChipMagnErrChiplimit, 0, SCPI_QUERY_CMD}, // MPErr.MagnErrChipLimit
    {"WT:WCDMA:SENSe:PHASE:ERROr:VS:CHIP:PHASeerrchip?", SCPI_3GPP_GetVsaPhaseErrorVsChipPhaseErrChip, 0, SCPI_QUERY_CMD}, // MPErr.PhaseErrChip
    {"WT:WCDMA:SENSe:PHASE:ERROr:VS:CHIP:PHASeerrchiplimit?", SCPI_3GPP_GetVsaPhaseErrorVsChipPhaseErrChiplimit, 0, SCPI_QUERY_CMD}, // MPErr.PhaseErrChipLimit
    // Error Vector Magnitude
    {"WT:WCDMA:SENSe:ERROr:VECTor:MAGNitude:EVMOut?", SCPI_3GPP_GetVsaEvmOut, 0, SCPI_QUERY_CMD}, // Evm.EvmOut
    {"WT:WCDMA:SENSe:ERROr:VECTor:MAGNitude:EVMLimit?", SCPI_3GPP_GetVsaEvmLimit, 0, SCPI_QUERY_CMD}, // Evm.EvmLimit
    {"WT:WCDMA:SENSe:FETCh:EVM:RMS?", SCPI_3GPP_GetVsaRstEvmRms, 0, SCPI_QUERY_CMD}, // Evm.EvmRms
    {"WT:WCDMA:SENSe:FETCh:EVM:PEAK?", SCPI_3GPP_GetVsaRstEvmPeak, 0, SCPI_QUERY_CMD}, // Evm.EvmPeak
    // EVM vs Chip
    {"WT:WCDMA:SENSe:ERROr:VECTor:MAGNitude:VS:CHIP?", SCPI_3GPP_GetVsaEvmChip, 0, SCPI_QUERY_CMD}, // Evm.EvmChip
    {"WT:WCDMA:SENSe:ERROr:VECTor:MAGNitude:VS:CHIPlimit?", SCPI_3GPP_GetVsaEvmChipLimit, 0, SCPI_QUERY_CMD}, // Evm.EvmChipLimit
    // CDP vs Slot
    {"WT:WCDMA:SENSe:CDP:VS:SLOT:VS:DPCCh:PWROut?", SCPI_3GPP_GetVsaCDPVsSlotDPCCHPwrOut, 0, SCPI_QUERY_CMD}, // CDP.DPCCHPwrOut
    {"WT:WCDMA:SENSe:CDP:VS:SLOT:VS:DPDCh:PWROut?", SCPI_3GPP_GetVsaCDPVsSlotDPDCHPwrOut, 0, SCPI_QUERY_CMD}, // CDP.DPDCHPwrOut
    {"WT:WCDMA:SENSe:CDP:VS:SLOT:VS:HSDPcch:PWROut?", SCPI_3GPP_GetVsaCDPVsSlotHSDPCCHPwrOut, 0, SCPI_QUERY_CMD}, // CDP.HSDPCCHPwrOut
    {"WT:WCDMA:SENSe:CDP:VS:SLOT:VS:DPCCh:PWR?", SCPI_3GPP_GetVsaCDPVsSlotDPCCHPwr, 0, SCPI_QUERY_CMD}, // CDP.DPCCHPwr
    {"WT:WCDMA:SENSe:CDP:VS:SLOT:VS:DPDCh:PWR?", SCPI_3GPP_GetVsaCDPVsSlotDPDCHPwr, 0, SCPI_QUERY_CMD}, // CDP.DPDCHPwr
    {"WT:WCDMA:SENSe:CDP:VS:SLOT:VS:HSDPcch:PWR?", SCPI_3GPP_GetVsaCDPVsSlotHSDPCCHPwr, 0, SCPI_QUERY_CMD}, // CDP.HSDPCCHPwr
    // CDP I-Signal
    {"WT:WCDMA:SENSe:CDM:IQSIgnal:CODEnum?", SCPI_3GPP_GetVsaCDMIQSignalCodeNum, 0, SCPI_QUERY_CMD}, // CDM.CodeNum
    {"WT:WCDMA:SENSe:CDP:ISIGnal:IMONitorval?", SCPI_3GPP_GetVsaCDPISignalIMonitorval, 0, SCPI_QUERY_CMD}, // CDP.IMonitorVal
    // CDP Q-Signal
    {"WT:WCDMA:SENSe:CDP:QSIGnal:QMONitorval?", SCPI_3GPP_GetVsaCDPQSignalIMonitorval, 0, SCPI_QUERY_CMD}, // CDP.QMonitorVal
    // Summary
    {"WT:WCDMA:SENSe:FETCh:SUMMary?", SCPI_3GPP_GetVsaRstSummaryWCDMA, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:FETCh:MEASure:SYNC:SLOT:ID?", SCPI_3GPP_GetVsaRstSyncSlotId, 0, SCPI_QUERY_CMD}, // SyncSlotId
    {"WT:WCDMA:SENSe:UE:POWEr:PWR?", SCPI_3GPP_GetVsaUEPowerPwr, 0, SCPI_QUERY_CMD}, // UEPower.Pwr
    {"WT:WCDMA:SENSe:IQ:OFFSet?", SCPI_3GPP_GetVsaIQOffset, 0, SCPI_QUERY_CMD}, // IqOffset
    {"WT:WCDMA:SENSe:PHASedis:PHASediff?", SCPI_3GPP_GetVsaPhaseDisPhaseDiff, 0, SCPI_QUERY_CMD}, // PhaseDis.PhaseDiff
    // Data Info
    {"WT:WCDMA:SENSe:FETCh:DEINfo:TRCH:ON:NUM?", SCPI_3GPP_GetVsaRstDeinfoTrchOnNum, 0, SCPI_QUERY_CMD}, // Deinfo.TrchOnNum

    // Relative CDE(unused)
    {"WT:WCDMA:SENSe:RELAtive:CDE:DPCCh:ERROut?", SCPI_3GPP_GetVsaRelativeCDEDPCCHErrOut, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:DPDCh:ERROut?", SCPI_3GPP_GetVsaRelativeCDEDPDCHErrOut, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:HSDPcch:ERROut?", SCPI_3GPP_GetVsaRelativeCDEHSDPCCHErrOut, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:DPCCh:ERR?", SCPI_3GPP_GetVsaRelativeCDEDPCCHErr, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:DPDCh:ERR?", SCPI_3GPP_GetVsaRelativeCDEDPDCHErr, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:HSDPcch:ERR?", SCPI_3GPP_GetVsaRelativeCDEHSDPCCHErr, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:DPCChsf?", SCPI_3GPP_GetVsaRelativeCDEDPCCHsf, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:DPDChsf?", SCPI_3GPP_GetVsaRelativeCDEDPDCHsf, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:RELAtive:CDE:HSDPcchsf?", SCPI_3GPP_GetVsaRelativeCDEHSDPCCHsf, 0, SCPI_QUERY_CMD},
    // CDE I-Signal(unused)
    {"WT:WCDMA:SENSe:CDE:ISIGnal:IMONitorval?", SCPI_3GPP_GetVsaCDEISignalIMonitorval, 0, SCPI_QUERY_CMD},
    {"WT:WCDMA:SENSe:CDE:QSIGnal:QMONitorval?", SCPI_3GPP_GetVsaCDEQSignalIMonitorval, 0, SCPI_QUERY_CMD},
};

// 蜂窝SCPI命令:一部分命令与WIFI兼容，一部分命令LTE独有，需筛选请搜索":LTE"
static const scpi_command_t scpi_wt_3gpp_lte_wavegen_cmd[] = {
    // 基础部分
    {"WT:LTE:SOURce:CONFigure:WAVE:LINK", SCPI_LTE_SetLinkDirect, 0, SCPI_SEQUENTIAL},

    // GeneralInfo
    {"WT[:LTE]:SOURce:CONFigure:WAVE:GENerate:MODE", SCPI_LTE_SetGenWaveMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:GENerate:POSition", SCPI_LTE_SetGenWaveNo, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:GENerate:SEQUence:LEN", SCPI_LTE_SetGenWaveSequenceLen, 0, SCPI_SEQUENTIAL},

    // 滤波器
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:TYPE", SCPI_LTE_SetFilterType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:SAMPle", SCPI_LTE_SetFilterOverFs, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:ORDer:MAX", SCPI_LTE_SetFilterMaxOrder, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:FPASs:FACTor", SCPI_LTE_SetFilterFpassFactor, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:FSTOp:FACTor", SCPI_LTE_SetFilterFstopFactor, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:PASS:RIPPle", SCPI_LTE_SetFilterPassRipple, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:STOP:ATTEn", SCPI_LTE_SetFilterStopAtten, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:ROFActor", SCPI_LTE_SetFilterRollOffFactor, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:CUTOff:FFACtor", SCPI_LTE_SetFilterCutOffFrqFactor, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:OPTImization", SCPI_LTE_SetFilterOptimization, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:CUTOff:FSHIft", SCPI_LTE_SetFilterCutOffFreqShift, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:FILTer:SMOOth:FACTor", SCPI_LTE_SetFilterSmoothFactor, 0, SCPI_SEQUENTIAL},

    // CELL:UL
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:CA:STATe", SCPI_LTE_SetCarrierAggregation, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:INDex", SCPI_LTE_SetCellIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:STATe", SCPI_LTE_SetCellState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:CID", SCPI_LTE_SetCellCid, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:BW", SCPI_LTE_SetCellBW, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:DUPLexing", SCPI_LTE_SetDuolexing, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:TDD:UDConf", SCPI_LTE_SetULDLConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:TDD:SPSConf", SCPI_LTE_SetSpecialSubframeConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CA]:CELL#:REFSig:DMRS", SCPI_LTE_SetN1Dmrs, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:CPC", SCPI_LTE_SetCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:REFSig:GRPHopping", SCPI_LTE_SetULGroupHop, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:REFSig:SEQHopping", SCPI_LTE_SetULSequenceHop, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:REFSig:DSSHift", SCPI_LTE_SetULDeltaSeqShift, 0, SCPI_SEQUENTIAL},

    // CELL:DL
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:CONF:MODE", SCPI_LTE_SetDLPdschScheduling, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:CA:STATe", SCPI_LTE_SetCarrierAggregation, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:INDex", SCPI_LTE_SetCellIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:STATe", SCPI_LTE_SetCellState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:CID", SCPI_LTE_SetCellCid, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:BW", SCPI_LTE_SetCellBW, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:DUPLexing", SCPI_LTE_SetDuolexing, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:TDD:UDConf", SCPI_LTE_SetULDLConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:TDD:SPSConf", SCPI_LTE_SetSpecialSubframeConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:POFFset", SCPI_LTE_SetCellDLPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:PSTart", SCPI_LTE_SetCellDLPdschStart, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:PHICh:NGParameter", SCPI_LTE_SetCellDLPhichResource, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:CA]:CELL#:PHICh:DURation", SCPI_LTE_SetCellDLPhichDuration, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:CPC", SCPI_LTE_SetCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:SYNC:TXANtenna", SCPI_LTE_SetSyncTxAntenna, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:SYNC:PPOWer", SCPI_LTE_SetPsyncPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:SYNC:SPOWer", SCPI_LTE_SetSsyncPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:REFSig:POWer", SCPI_LTE_SetRefSignalPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:PDSCh:PB", SCPI_LTE_SetPdschPB, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:PBCH:RATBa", SCPI_LTE_SetPbchRatioRho, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:PDCCh:RATBa", SCPI_LTE_SetPdcchRatioRho, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:MIMO:CONFiguration", SCPI_LTE_SetTxAntennaNum, 0, SCPI_SEQUENTIAL},

    // UE:UL
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE:ID", SCPI_LTE_SetUserID, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE:POWer", SCPI_LTE_SetUserPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE:MODE", SCPI_LTE_SetUserMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:DATA", SCPI_LTE_SetUserPushData, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:INITpattern", SCPI_LTE_SetUserPushPattern, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:TXMode", SCPI_LTE_SetUserPushTxMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:NAPort", SCPI_LTE_SetUserPushNAPort, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:SCRambling:STATe", SCPI_LTE_SetUserScramblingState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:CCODing:STATe", SCPI_LTE_SetUserCCodingState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:CCODing:MODE", SCPI_LTE_SetUserCCodingMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL:UE[:CELL#]:PUSCh:EN256QAM", SCPI_LTE_SetUserEnable256QAM, 0, SCPI_SEQUENTIAL},

    // UE:DL
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:ID", SCPI_LTE_SetUserID, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:SCRambling:STATe", SCPI_LTE_SetUserScramble, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:CCODing:STATe", SCPI_LTE_SetUserChanCodingState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:APM:MODE", SCPI_LTE_SetUserAPMapping, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:CATEgory", SCPI_LTE_SetUserUECategory, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:APM:CBINdex", SCPI_LTE_SetUserCodebookIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:DATA", SCPI_LTE_SetUserDataType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:INITpattern", SCPI_LTE_SetUserInitialization, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:PA", SCPI_LTE_SetUserPdschPA, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:TXM", SCPI_LTE_SetUserTransmitMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:MCSTable", SCPI_LTE_SetUserMcsTable, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:UE:TBAL", SCPI_LTE_SetUserTBSTalternativeIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:SCHEdule:PDSCh", SCPI_LTE_SetSchedulePdsch, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:SCHEdule:HARQ:PROC:NUM", SCPI_LTE_SetScheduleHarqProcNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:SCHEdule:NEW:DATA:INDIc", SCPI_LTE_SetScheduleNewDataIndic, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONFigure:WAVE:DL:SCHEdule:MCS", SCPI_LTE_SetScheduleMcs, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:PUSCh", SCPI_LTE_SetDLSchedPusch, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:NR:PDCCh:SYMB:NUM", SCPI_LTE_SetDLSchedNrPdcchSymbNum, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:NR:DL:DCI:PDCCh:FORMat", SCPI_LTE_SetDLNrDLDciPdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:NR:DL:DCI:CCE:IDX", SCPI_LTE_SetDLNrDLDciCceIdx, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:NR:UL:DCI:PDCCh:FORMat", SCPI_LTE_SetDLSchedNrULDciPdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:NR:UL:DCI:CCE:IDX", SCPI_LTE_SetDLSchedNrULDciCceIdx, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:SP:PDCCh:SYMB:NUM", SCPI_LTE_SetDLSchedSpPdcchSymbNum, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:SP:DL:DCI:PDCCh:FORMat", SCPI_LTE_SetDLSpDLDciPdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:SP:DL:DCI:CCE:IDX", SCPI_LTE_SetDLSpDLDciCceIdx, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:SP:UL:DCI:PDCCh:FORMat", SCPI_LTE_SetDLSchedSpULDciPdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SOURce:CONF:WAVE:DL:SCHEd:SP:UL:DCI:CCE:IDX", SCPI_LTE_SetDLSchedSpULDciCceIdx, 0, SCPI_SEQUENTIAL},

    // Frame:UL
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:UE]:CONSubframes:PUCCh", SCPI_LTE_SetSubfUserPucchConfCount, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:UE]:CONSubframes:PUSCh", SCPI_LTE_SetSubfUserPuschConfCount, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:SUBF#]:UE:STATe", SCPI_LTE_SetSubfUserState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUCCh:STATe", SCPI_LTE_SetSubfUserPucchState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:STATe", SCPI_LTE_SetSubfUserPuschState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:RBSEtnum", SCPI_LTE_SetSubfUserPuschRBSETnum, 0, SCPI_SEQUENTIAL}, // 命令名需对照罗德进行改动
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:SET#:RBCount", SCPI_LTE_SetSubfUserPuschRBCount, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:SET#:VRBoffset", SCPI_LTE_SetSubfUserPuschVRBoffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:POWer", SCPI_LTE_SetSubfUserPuschPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:FHOP:STATe", SCPI_LTE_SetSubfUserPuschFhopState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:PRECoding:SCHeme", SCPI_LTE_SetSubfUserPuschPrecodingScheme, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:PRECoding:NOLayers", SCPI_LTE_SetSubfUserPuschPrecodingNOLayers, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:PRECoding:NAPused", SCPI_LTE_SetSubfUserPuschPrecodingNAPused, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:PRECoding:CBINdex", SCPI_LTE_SetSubfUserPuschCBINdex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:DRS:CYCShift", SCPI_LTE_SetSubfUserPuschDrsCYCShift, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:CODWords", SCPI_LTE_SetSubfUserPuschCodewords, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE:PUSCh:MCSMode", SCPI_LTE_SetSubfUserPuschMcsMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE[:CW#]:PUSCh:MCS", SCPI_LTE_SetSubfUserCwPuschMcs, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE[:CW#]:PUSCh:MODulation", SCPI_LTE_SetSubfUserCwPuschModulation, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE[:CW#]:PUSCh:CCODing:TBSize", SCPI_LTE_SetSubfUserCwPuschCCodingTBsize, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:UL[:CELL#][:SUBF#]:UE[:CW#]:PUSCh:CCODing:RVINdex", SCPI_LTE_SetSubfUserCwPuschCCodingRVINdex, 0, SCPI_SEQUENTIAL},

    // Frame:DL
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:CONSubframes", SCPI_LTE_SetSubfrmCfgNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:OCNG:FLAG", SCPI_LTE_SetSubfrmOcngFlag, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:DUMMy:MODulation", SCPI_LTE_SetSubfrmDummyModulate, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:DUMMy:DATA:TYPE", SCPI_LTE_SetSubfrmDummyDataType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL:BUR", SCPI_LTE_SetOCNGFlag, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PBCH:STATe", SCPI_LTE_SetSubfrmPbchState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PBCH:SCRambling:STATe", SCPI_LTE_SetSubfrmPbchScrambling, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PBCH:PRECoding:SCHeme", SCPI_LTE_SetSubfrmPbchPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PBCH:SOFFset", SCPI_LTE_SetSubfrmPbchSNFOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PBCH:MSPare", SCPI_LTE_SetSubfrmPbchSpareBit, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PCFIch:STATe", SCPI_LTE_SetSubfrmPcfichState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PCFIch:SCRambling:STATe", SCPI_LTE_SetSubfrmPcfichScrambling, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PCFIch:PRECoding:SCHeme", SCPI_LTE_SetSubfrmPcfichPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PCFIch:POWer", SCPI_LTE_SetSubfrmPcfichPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PCFIch:CREGion", SCPI_LTE_SetSubfrmPcfichPDCCHSymNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PHICh:POWer", SCPI_LTE_SetSubfrmPhichPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PHICh:ACKInfo", SCPI_LTE_SetSubfrmPhichACKInfo, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:FORMat", SCPI_LTE_SetSubfrmPdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DUMMy:CCEType", SCPI_LTE_SetSubfrmPdcchDummyCCEType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DATA:TYPE", SCPI_LTE_SetSubfrmPdcchDataType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:POWer", SCPI_LTE_SetSubfrmPdcchPower, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDCCh:AUTO:SCHEd:DL:DCI", SCPI_LTE_SetSubfrmPdcchAutoSchedDLDCI, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:NOPDcchs", SCPI_LTE_SetSubfrmPdcchSymbNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:PDCCh:FORMat", SCPI_LTE_SetSubfrmPdcchDCIDLPDCCHFormat, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:CCEIdx", SCPI_LTE_SetSubfrmPdcchDCIDLCCEIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:STATe", SCPI_LTE_SetSubfrmPdcchDCIDLState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:USER", SCPI_LTE_SetSubfrmPdcchDCIDLUser, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:FORMat", SCPI_LTE_SetSubfrmPdcchDCIDLFormat, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:SEARch:SPACe", SCPI_LTE_SetSubfrmPdcchDCIDLSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:RES:ALLOcate:HEADer", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ResAllocateHeader, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:RES:BLK:ASSIgn", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ResBlkAssign, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:MCS", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1MCS, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:HARQ:PROC:NUM", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1HarqProcNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:NEWData:IND", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1NewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:RVIDx", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1RvIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:TPCCommand", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1TPCCommand, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1:DL:ASSIgnment", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1DLAssignment, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:MODE", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:VRB:ASSIgnment", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AVRBAssignment, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:RESBlk:CONFig", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AResBlkConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:RBCount", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARBNumber, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:RBOFfset", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARBOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:RES:Blk:ASSIgn", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AResBlkAssign, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:MCS", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AMCS, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:HARQ:PROC:NUM", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AHarqProcNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:NEWData:IND", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ANewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:RVIDx", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARvIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:TPCCommand", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ATPCCommand, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:DL:F1A:DL:ASSIgnment", SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ADLAssignment, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:STATe", SCPI_LTE_SetSubfrmPdcchDCIULState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:USER", SCPI_LTE_SetSubfrmPdcchDCIULUser, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:FORMat", SCPI_LTE_SetSubfrmPdcchDCIULDCIFormat, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:SEARch:SPACe", SCPI_LTE_SetSubfrmPdcchDCIULSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:PDCCH:FORMat", SCPI_LTE_SetSubfrmPdcchDCIULPDCCHFormat, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:CCEIdx", SCPI_LTE_SetSubfrmPdcchDCIULCCEIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:FREQ:HOP", SCPI_LTE_SetSubfrmPdcchDCIULFormat0FreqHop, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:RES:BlkAssign", SCPI_LTE_SetSubfrmPdcchDCIULFormat0ResBlkAssign, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:MCS", SCPI_LTE_SetSubfrmPdcchDCIULFormat0MCS, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:NEWData:IND", SCPI_LTE_SetSubfrmPdcchDCIULFormat0NewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:TPCCommand", SCPI_LTE_SetSubfrmPdcchDCIULFormat0TPCCommand, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:CYCLic:SHIFtFor:DMRS", SCPI_LTE_SetSubfrmPdcchDCIULFormat0CyclicShiftForDMRS, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:UL:INDEx", SCPI_LTE_SetSubfrmPdcchDCIULFormat0ULIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:DAI", SCPI_LTE_SetSubfrmPdcchDCIULFormat0DAI, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:CSI:RESQuest", SCPI_LTE_SetSubfrmPdcchDCIULFormat0CSIResquest, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:ENCC:PDCCh:DCI:UL:F0:RES:ALLOCate:TYPE", SCPI_LTE_SetSubfrmPdcchDCIULFormat0ResAllocateType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:STATe", SCPI_LTE_SetSubfrmPdschState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:RES:ALLOcate:TYPE", SCPI_LTE_SetSubfrmPdschResAllocateType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:VRB:ASSIgnment", SCPI_LTE_SetSubfrmPdschVRBAssignment, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:RBGBitmap", SCPI_LTE_SetSubfrmPdschRBGBitmap, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:RBCount", SCPI_LTE_SetSubfrmPdschRBNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:AOC", SCPI_LTE_SetSubfrmPdschAutoOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:RBOFfset", SCPI_LTE_SetSubfrmPdschRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:SYMoffset", SCPI_LTE_SetSubfrmPdschSymbOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:PRECoding:SCHeme", SCPI_LTE_SetSubfrmPdschPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:PRECoding:NOLayers", SCPI_LTE_SetSubfrmPdschLayerNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:PRECoding:CDD", SCPI_LTE_SetSubfrmPdschCyclicDelayDiversity, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:PRECoding:CBINdex", SCPI_LTE_SetSubfrmPdschCodebookIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:IRCFg:MODE", SCPI_LTE_SetSubfrmPdschCwIRConfigMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:CODWords", SCPI_LTE_SetSubfrmPdschCodeword, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh:MCSMode", SCPI_LTE_SetSubfrmPdschMCSConfigMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh[:CW#]:MCS", SCPI_LTE_SetSubfrmPdschCwMcs, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh[:CW#]:MODulation", SCPI_LTE_SetSubfrmPdschCwModulate, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh[:CW#]:CCODing:TBSize", SCPI_LTE_SetSubfrmPdschCwPayloadSize, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh[:CW#]:CCODing:RVINdex", SCPI_LTE_SetSubfrmPdschCwRedunVerIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh[:CW#]:CCODing:IR:BSIZe", SCPI_LTE_SetSubfrmPdschCwNIR, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SOURce:CONFigure:WAVE:DL[:FRM#][:SUBF#]:PDSCh[:CW#]:CCODing:SCBC", SCPI_LTE_SetSubfrmPdschCwSoftChanBit, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_lte_analyze_cmd[] = {
    {"WT:CELLular:SENSe:CONFigure:ANALy:MEAS:POWEr", SCPI_3GPP_SetAlzMeasurePower, 0, SCPI_SEQUENTIAL},  // MeasPowerGraph
    {"WT:CELLular:SENSe:CONFigure:ANALy:MEAS:CCDF", SCPI_3GPP_SetAlzMeasureCcdf, 0, SCPI_SEQUENTIAL},  // MeasCcdf
    {"WT:CELLular:SENSe:CONFigure:ANALy:MEAS:SPECtrum", SCPI_3GPP_SetAlzMeasureSpectrum, 0, SCPI_SEQUENTIAL},  // MeasSpectrum
    {"WT:CELLular:SENSe:CONFigure:ANALy:SPECtrum:RBW", SCPI_3GPP_SetAlzSpectrumRBW, 0, SCPI_SEQUENTIAL},  // SpectrumRBW
    {"WT[:LTE]:SENSe:CONFigure:ANALy:CPC", SCPI_3GPP_SetAlzCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:UE:ID", SCPI_3GPP_SetAlzUEID, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:CHANnal", SCPI_3GPP_SetAlzChannalType, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PKG:OFFSet", SCPI_3GPP_SetAlzPkgOffset, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:NET:SIGNal", SCPI_3GPP_SetAlzNetSignal, 0, SCPI_SEQUENTIAL},

    // CELL 分析参数
    {"WT[:LTE]:SENSe:CONFigure:ANALy:CA:STATe", SCPI_3GPP_SetAlzCAState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:INDEx", SCPI_3GPP_SetAlzCACellIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:STATe", SCPI_3GPP_SetAlzCACellState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:CID", SCPI_3GPP_SetAlzCACellPhyID, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:BW", SCPI_3GPP_SetAlzCACellBW, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:DUPLexing", SCPI_3GPP_SetAlzCACellDUPLexing, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:TDD:UDConf", SCPI_3GPP_SetAlzCACellULDLConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:TDD:SPSConf", SCPI_3GPP_SetAlzCACellSpecialSubframeConfig, 0, SCPI_SEQUENTIAL},

    // 子帧分析参数
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:INDEx", SCPI_3GPP_SetAlzCACellPuschIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:STATe", SCPI_3GPP_SetAlzCACellPuschState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:RBConf", SCPI_3GPP_SetAlzCACellPuschRBConfig, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:PRECoding", SCPI_3GPP_SetAlzCACellPuschPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:LAYEr:NUM", SCPI_3GPP_SetAlzCACellPuschLayerNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:TXANtenna:NUM", SCPI_3GPP_SetAlzCACellPuschAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:CODEbook:INDEx", SCPI_3GPP_SetAlzCACellPuschCodebookIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:GRPHopping", SCPI_3GPP_SetAlzCACellPuschGroupHop, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:SEQHopping", SCPI_3GPP_SetAlzCACellPuschSequenceHop, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:DSSHift", SCPI_3GPP_SetAlzCACellPuschDeltaSeqShift, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:DMRS", SCPI_3GPP_SetAlzCACellPuschN1Dmrs, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:CYCShift", SCPI_3GPP_SetAlzCACellPuschCyclicShiftField, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:CODWords", SCPI_3GPP_SetAlzCACellPuschCodeword, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:MODUlate", SCPI_3GPP_SetAlzCACellPuschModulate, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:CHAN:DECOding:STATe", SCPI_3GPP_SetAlzCACellPuschChanDecodeState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:SCRAmble", SCPI_3GPP_SetAlzCACellPuschScramble, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:MCSMode", SCPI_3GPP_SetAlzCACellPuschMCSMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:MCSCfg", SCPI_3GPP_SetAlzCACellPuschMCSCfg, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:EN256QAM", SCPI_3GPP_SetAlzCACellPuschEnable256QAM, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy[:CA]:CELL#:PUSCh:RB:DETEct:MODE", SCPI_3GPP_SetAlzCACellPuschRBDetMode, 0, SCPI_SEQUENTIAL},

    // 其他分析参数
    {"WT[:LTE]:SENSe:CONFigure:ANALy:MEAS:SUBFrame", SCPI_3GPP_SetAlzMeasSubFrame, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:MEAS:EVMCfg", SCPI_3GPP_SetAlzMeasEvmCfg, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:MEAS:DMRS:CONStellation:STATe", SCPI_3GPP_SetAlzMeasDmrsConsState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:MEAS:UNIT", SCPI_3GPP_SetAlzMeasureUnit, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:MEAS:EXCLude:ABNOrmal:SYMBol", SCPI_3GPP_SetAlzMeasureExcludeAbnormalSymbol, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SYNC:MODE", SCPI_3GPP_SetAlzLteMeasureSyncMode, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SUBFrame:OFFSet", SCPI_3GPP_SetAlzLteMeasureSubframeOffset, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SUBFrame:COUNt", SCPI_3GPP_SetAlzLteMeasureSubframeCount, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SLOT:TYPE", SCPI_3GPP_SetAlzLteMeasureSlotType, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:EVM", SCPI_3GPP_SetAlzLteMeasureModulationEvm, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:MERR", SCPI_3GPP_SetAlzLteMeasureModulationMerr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:PERR", SCPI_3GPP_SetAlzLteMeasureModulationPerr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:EVM:SUBCarrier", SCPI_3GPP_SetAlzLteMeasureModulationEvmSubcarrier, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:IBE", SCPI_3GPP_SetAlzLteMeasureModulationIbe, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:ES:FLAT", SCPI_3GPP_SetAlzLteMeasureModulationEsFlat, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlation:IQ:CONSt", SCPI_3GPP_SetAlzLteMeasureModulationIQConst, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:DYNAmic", SCPI_3GPP_SetAlzLteMeasurePowerDynamic, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:ACLR", SCPI_3GPP_SetAlzLteMeasureSpectrumAclr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:EMISsion:MASK", SCPI_3GPP_SetAlzLteMeasureSpectrumEmissionMask, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:TX:MEAS", SCPI_3GPP_SetAlzLteMeasureSpectrumTxMeas, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:DECOding:RESUlt", SCPI_3GPP_SetAlzLteMeasureSpectrumDecodingResult, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:DMRS:CONStellation", SCPI_3GPP_SetAlzLteMeasureSpectrumDmrsCons, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:EVM:SYMBol", SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbol, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:EVM:SYMBol:INDEx", SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbolIndex, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:EVM:SYMBol:WINDow:TYPE", SCPI_3GPP_SetAlzLteMeasureSpectrumEvmSymbolWindowType, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:MOD:ENABle", SCPI_3GPP_SetAlzLteMeasureModulationEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:OBW:ENABle", SCPI_3GPP_SetAlzLteMeasureSpectrumOBWEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:SEM:ENABle", SCPI_3GPP_SetAlzLteMeasureSpectrumSEMEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:PMONitor:ENABle", SCPI_3GPP_SetAlzLteMeasurePowerPmonEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:ENABle", SCPI_3GPP_SetAlzLteMeasurePowerEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:STATic:COUNt", SCPI_3GPP_SetAlzLteMeasureModulationStaticCount, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:EVM:WINDow:NCP", SCPI_3GPP_SetAlzLteMeasureModulationEvmWindowNcp, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:EVM:WINDow:ECP", SCPI_3GPP_SetAlzLteMeasureModulationEvmWindowEcp, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:EXPEriod:LEAD", SCPI_3GPP_SetAlzLteMeasureModulationExpPeriodLead, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:EXPEriod:LAG", SCPI_3GPP_SetAlzLteMeasureModulationExpPeriodLag, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:EXABnormal:SYMBol", SCPI_3GPP_SetAlzLteMeasureModulationExpAbnormalSymbol, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:MODUlate:EQUAlizer", SCPI_3GPP_SetAlzLteMeasureModulationEqualizer, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:SEM:STAT:NUM", SCPI_3GPP_SetAlzLteMeasureSpectrumSEMStatNum, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:SEM:MEAS:FILTer", SCPI_3GPP_SetAlzLteMeasureSpectrumSEMMeasFilter, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:SEM:ACLR:STAT:NUM", SCPI_3GPP_SetAlzLteMeasureSpectrumSEMACLRStatNum, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:UTRA#:ENABle", SCPI_3GPP_SetAlzLteMeasureSpectrumUTRAEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:SPECtrum:EUTRA#:ENABle", SCPI_3GPP_SetAlzLteMeasureSpectrumEUTRAEnable, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:TIME:MASK", SCPI_3GPP_SetAlzLteMeasurePowerTimeMask, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:TIME:LEAD", SCPI_3GPP_SetAlzLteMeasurePowerTimeLead, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:TIME:LAG", SCPI_3GPP_SetAlzLteMeasurePowerTimeLag, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:STATic:COUNt", SCPI_3GPP_SetAlzLteMeasurePowerStaticCount, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MEAS:POWEr:HIGH:DYNAmic:MODE", SCPI_3GPP_SetAlzLteMeasurePowerHighDynamicMode, 0, SCPI_SEQUENTIAL},

    // DL PDSCH分析参数
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:SYMoffset", SCPI_3GPP_SetAlzPdschSymbOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:RESOurce:ALLOcation:TYPE", SCPI_3GPP_SetAlzPdschResourceAllocation, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:VRBAssign", SCPI_3GPP_SetAlzPdschVRBAssignment, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:RBGBitmap", SCPI_3GPP_SetAlzPdschRBGBitmap, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:RBCount", SCPI_3GPP_SetAlzPdschRBNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:RBOFfset", SCPI_3GPP_SetAlzPdschRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PBCH:STATe", SCPI_3GPP_SetAlzPdschPbchState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PRECoding:SCHeme", SCPI_3GPP_SetAlzPdschPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PRECoding:NOLayers", SCPI_3GPP_SetAlzPdschLayerNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:MIMO:CONFiguration", SCPI_3GPP_SetAlzPdschAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PRECoding:CDD", SCPI_3GPP_SetAlzPdschCyclicDelayDiversity, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PRECoding:CBINdex", SCPI_3GPP_SetAlzPdschCodebookIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:CODWords", SCPI_3GPP_SetAlzPdschCodeword, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:CCODing:STATe", SCPI_3GPP_SetAlzPdschChanDecodeState, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:SCRambling:STATe", SCPI_3GPP_SetAlzPdschScramble, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:MCSMode", SCPI_3GPP_SetAlzPdschMcsCfgMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:IRCFg:MODE", SCPI_3GPP_SetAlzPdschIRConfigMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:TX:MODE", SCPI_3GPP_SetAlzPdschTxMode, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:UECAtegory", SCPI_3GPP_SetAlzPdschUECategory, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:MCSTable", SCPI_3GPP_SetAlzPdschMcsTable, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:TBAL", SCPI_3GPP_SetAlzPdschTBSTalternativeIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:MCS", SCPI_3GPP_SetAlzPdschMcs, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:MODulation", SCPI_3GPP_SetAlzPdschModulate, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:CCODing:TBSize", SCPI_3GPP_SetAlzPdschPayloadSize, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:CCODing:RVINdex", SCPI_3GPP_SetAlzPdschRedunVerIdx, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:CCODing:IR:BSIZe", SCPI_3GPP_SetAlzPdschNIR, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:CCODing:ICBC", SCPI_3GPP_SetAlzPdschSoftChanBit, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PA", SCPI_3GPP_SetAlzPdschPA, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE]:SENSe:CONFigure:ANALy:PDSCh:PB", SCPI_3GPP_SetAlzPdschPB, 0, SCPI_SEQUENTIAL},

    // Limits Modulation
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit:MODE", SCPI_3GPP_SetAlzModLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:EVM:RMS:STATe", SCPI_3GPP_SetAlzModLimitEvmRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:EVM:RMS:LIMIt", SCPI_3GPP_SetAlzModLimitEvmRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:EVM:PEAK:STATe", SCPI_3GPP_SetAlzModLimitEvmPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:EVM:PEAK:LIMIt", SCPI_3GPP_SetAlzModLimitEvmPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:MERR:RMS:STATe", SCPI_3GPP_SetAlzModLimitMerrRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:MERR:RMS:LIMIt", SCPI_3GPP_SetAlzModLimitMerrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:MERR:PEAK:STATe", SCPI_3GPP_SetAlzModLimitMerrPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:MERR:PEAK:LIMIt", SCPI_3GPP_SetAlzModLimitMerrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:PHERr:RMS:STATe", SCPI_3GPP_SetAlzModLimitPherrRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:PHERr:RMS:LIMIt", SCPI_3GPP_SetAlzModLimitPherrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:PHERr:PEAK:STATe", SCPI_3GPP_SetAlzModLimitPherrPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:PHERr:PEAK:LIMIt", SCPI_3GPP_SetAlzModLimitPherrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:FREQ:ERR:STATe", SCPI_3GPP_SetAlzModLimitFreqErrState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:FREQ:ERR:LIMIt", SCPI_3GPP_SetAlzModLimitFreqErrLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IQOFfset:STATe", SCPI_3GPP_SetAlzModLimitIQOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IQOFfset:PWRLimit", SCPI_3GPP_SetAlzModLimitIQOffsetPwrLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IBE:STATe", SCPI_3GPP_SetAlzModLimitIBEState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IBE:GEN:MIN", SCPI_3GPP_SetAlzModLimitIBEGenMin, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IBE:GEN:EVM", SCPI_3GPP_SetAlzModLimitIBEGenEvm, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IBE:GEN:POWEr", SCPI_3GPP_SetAlzModLimitIBEGenPwr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IBE:IQ:IMAGe", SCPI_3GPP_SetAlzModLimitIBEIqImage, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:IBE:IQOFfset:POWEr", SCPI_3GPP_SetAlzModLimitIQOffsetPwr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:SPEC:FLAT:STATe", SCPI_3GPP_SetAlzModLimitSpecFlatState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:SPEC:FLAT:RANGe#", SCPI_3GPP_SetAlzModLimitSpecFlatRange, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:SPEC:FLAT:MAX1:MIN2", SCPI_3GPP_SetAlzModLimitSpecFlatMax1Min2, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:SPEC:FLAT:MAX2:MIN1", SCPI_3GPP_SetAlzModLimitSpecFlatMax2Min1, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:MODLimit#:SPEC:FLAT:EDGE:FREQ", SCPI_3GPP_SetAlzModLimitSpecFlatEdgeFreq, 0, SCPI_SEQUENTIAL},

    // Limits Spectrum
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit:MODE", SCPI_3GPP_SetAlzSpecLimitMode, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:OBW:STATe", SCPI_3GPP_SetAlzSpecLimitObwState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:OBW:LIMIt", SCPI_3GPP_SetAlzSpecLimitObwLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:SEM#:AREA#:STATe", SCPI_3GPP_SetAlzSpecLimitSemLimitState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:SEM#:AREA#:STARt:FREQ", SCPI_3GPP_SetAlzSpecLimitSemStartFreq, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:SEM#:AREA#:STOP:FREQ", SCPI_3GPP_SetAlzSpecLimitSemStopFreq, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:SEM#:AREA#:POWEr", SCPI_3GPP_SetAlzSpecLimitSemPwr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:SEM#:AREA#:RBW", SCPI_3GPP_SetAlzSpecLimitSemRbw, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:UTRA#:REAL:STATe", SCPI_3GPP_SetAlzSpecLimitUtraRealState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:UTRA#:REAL:LIMIt", SCPI_3GPP_SetAlzSpecLimitUtraRealLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:UTRA#:ABS:STATe", SCPI_3GPP_SetAlzSpecLimitUtraAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:UTRA#:ABS:POWEr", SCPI_3GPP_SetAlzSpecLimitUtraAbsPwr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:EUTRa#:REAL:STATe", SCPI_3GPP_SetAlzSpecLimitEutraRealState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:EUTRa#:REAL:LIMIt", SCPI_3GPP_SetAlzSpecLimitEutraRealLimit, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:EUTRa#:ABS:STATe", SCPI_3GPP_SetAlzSpecLimitEutraAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SPEClimit#:EUTRa#:ABS:POWEr", SCPI_3GPP_SetAlzSpecLimitEutraAbsPwr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:POWErlimit#:STATe", SCPI_3GPP_SetAlzPowerLimitState, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:POWErlimit#:ONPWr:UPPEr", SCPI_3GPP_SetAlzPowerLimitOnPwrUpper, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:POWErlimit#:ONPWr:LOWer", SCPI_3GPP_SetAlzPowerLimitOnPwrLower, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:POWErlimit#:OFFPWr", SCPI_3GPP_SetAlzPowerLimitOffPwr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:ANALy:SEMAdd:TEST:TOL#", SCPI_3GPP_SetAlzSpecLimitSemAddTestTol, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_lte_result_cmd[] = {
    // Summary
    {"WT:LTE:SENSe:FETCh:SUMMary?", SCPI_3GPP_GetVsaRstSummaryLTE, 0, SCPI_QUERY_CMD},
    // EVM vs. Symbol
    {"WT:LTE:SENSe:FETCh:SYMBol:EVM?", SCPI_3GPP_GetVsaRstSymoblEVM, 0, SCPI_QUERY_CMD}, // Evm.SymbEvm
    // Spectrum Flatness
    {"WT:LTE:SENSe:FETCh:SPECtrum:FLATness:DATA?", SCPI_3GPP_GetVsaRstFlatnessFlatRes, 0, SCPI_QUERY_CMD}, // Flatness.FlatRes
    {"WT:LTE:SENSe:FETCh:SPECtrum:FLATness:MASK:UP:DATA?", SCPI_3GPP_GetVsaRstFlatnessFlatMaskUp, 0, SCPI_QUERY_CMD}, // Flatness.FlatMaskUp
    {"WT:LTE:SENSe:FETCh:SPECtrum:FLATness:MASK:DOWN:DATA?", SCPI_3GPP_GetVsaRstFlatnessFlatMaskDown, 0, SCPI_QUERY_CMD}, // Flatness.FlatMaskDown
    // Result
    {"WT[:LTE]:SENSe:FETCh:IQ:MATCh:AMP?", GetVsaRstIQMatchAmp, 0, SCPI_QUERY_CMD}, // IqImbaAmp
    {"WT[:LTE]:SENSe:FETCh:IQ:MATCh:PHASe?", GetVsaRstIQMatchPhase, 0, SCPI_QUERY_CMD}, // IqImbaPhs
};

static const scpi_command_t scpi_wt_3gpp_nr5g_wavegen_cmd[] = {
    {"WT:NR:SOURce:CONFigure:WAVE:LINK", SCPI_NR5G_SetLinkDirect, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:VERSion", SCPI_NR5G_SetVersion, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:GEN:SEQUence:LENGth", SCPI_NR5G_SetGenSequenceLength, 0, SCPI_SEQUENTIAL},

    // filter
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:TYPE", SCPI_NR5G_SetFilterType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:SAMPle:RATE", SCPI_NR5G_SetFilterSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:MAXinum:ORDer", SCPI_NR5G_SetFilterMaxOrder, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:PASS:BAND:FREQuency:FACTor", SCPI_NR5G_SetFilterFpassFactor, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:STOP:BAND:FREQuency:FACTor", SCPI_NR5G_SetFilterFstopFactor, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:PASS:BAND:RIPPle", SCPI_NR5G_SetFilterPassRipple, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:STOP:BAND:ATTEnuation", SCPI_NR5G_SetFilterStopAtten, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:MODE", SCPI_NR5G_SetFilterMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:SMOOth:FACTor", SCPI_NR5G_SetFilterSmoothFactor, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:FILTer:CUT:OFF:FREQuency:FACTor", SCPI_NR5G_SetFilterCutOffFrqFactor, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:UL:RF:PHASe:COMPensation", SCPI_NR5G_UL_SetRFPhaseCompensation, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL:NUM", SCPI_NR5G_UL_SetCellNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:STATe", SCPI_NR5G_UL_SetCellState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:DEPLoyment", SCPI_NR5G_UL_SetCellDeployment, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:BW", SCPI_NR5G_UL_SetCellBW, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:TXBW#:SUB:CARRier:SPACing", SCPI_NR5G_UL_SetCellTxBWSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:TXBW#:STATe", SCPI_NR5G_UL_SetCellTxBWState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:TXBW#:MAX:RB:NUMber", SCPI_NR5G_UL_SetCellTxBWMaxRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:TXBW#:OFFSet", SCPI_NR5G_UL_SetCellTxBWOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:TXBW#:K0U", SCPI_NR5G_UL_SetCellTxBWK0U, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:DUPLexing", SCPI_NR5G_UL_SetCellDuplexing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:REF:SCSPacing", SCPI_NR5G_UL_SetCellRefSCSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SLOT:PERIod", SCPI_NR5G_UL_SetCellSlotPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SLOT:NUMBer", SCPI_NR5G_UL_SetCellSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SPECial:SLOT:INDEx", SCPI_NR5G_UL_SetCellSpecialSlotIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:PHYSical:ID", SCPI_NR5G_UL_SetCellPhysicalID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:DMRS:TYPE:A:POSition", SCPI_NR5G_UL_SetCellDMRSTypeAPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:FREQuency", SCPI_NR5G_UL_SetCellFrequency, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:ID", SCPI_NR5G_UL_SetUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:SCRAmbling", SCPI_NR5G_UL_SetUEScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:DATA:TYPE", SCPI_NR5G_UL_SetUEDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:INITialization", SCPI_NR5G_UL_SetUEInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:CHANnel:CODing:STATe", SCPI_NR5G_UL_SetUEChannelCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:SUB:CARRier:SPACing", SCPI_NR5G_UL_SetUEBwpSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:CYCLic:PREFix", SCPI_NR5G_UL_SetUEBwpCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:RB:NUMber", SCPI_NR5G_UL_SetUEBwpRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:RB:OFFSet", SCPI_NR5G_UL_SetUEBwpRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:TRANsform:PRECoder", SCPI_NR5G_UL_SetUEBwpPuschTransformPrecoder, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:TX:CONFig", SCPI_NR5G_UL_SetUEBwpPuschTxConfig, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:TPMI", SCPI_NR5G_UL_SetUEBwpPuschTPMI, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DATA:SCRAmble:ID", SCPI_NR5G_UL_SetUEBwpPUSChDataScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:MCS:TABle", SCPI_NR5G_UL_SetUEBwpPuschMCSTable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:FREQuency:HOPPing:MODE", SCPI_NR5G_UL_SetUEBwpPuschFreqHoppingMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:RESOurce:ALLOcation:TYPE", SCPI_NR5G_UL_SetUEBwpPuschResourceAllocationType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DMRS:TYPE", SCPI_NR5G_UL_SetUEBwpPuschDMRSType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DMRS:MAX:LENgth", SCPI_NR5G_UL_SetUEBwpPuschDMRSMaxLen, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DMRS:ADDitional:POSition:INDEx", SCPI_NR5G_UL_SetUEBwpPuschDMRSAddPosInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DMRS:USE:RSIXteen:DMRS", SCPI_NR5G_UL_SetUEBwpPuschDMRSUseSixteenDmrs, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DMRS:SCRAmbling:ID", SCPI_NR5G_UL_SetUEBwpPuschDMRSScramblingId, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:UE:BWP#:PUSCh:DMRS:NPUSch:ID", SCPI_NR5G_UL_SetUEBwpPuschDMRSNpuschID, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe:NUMber", SCPI_NR5G_UL_SetSubFrameTotalNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SLOT:CONFig:NUMBer", SCPI_NR5G_UL_SetSubFrameSlotConfigNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:STATe", SCPI_NR5G_UL_SetSubFrameSlotPuschState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:MAPPing:TYPE", SCPI_NR5G_UL_SetSubFrameSlotPuschMappingType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:SYMBol:NUMber", SCPI_NR5G_UL_SetSubFrameSlotPuschSymbolNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:SYMBol:OFFSet", SCPI_NR5G_UL_SetSubFrameSlotPuschSymbofOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:RB:NUMber", SCPI_NR5G_UL_SetSubFrameSlotPuschRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:RB:OFFSet", SCPI_NR5G_UL_SetSubFrameSlotPuschRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:LAYEr:NUMber", SCPI_NR5G_UL_SetSubFrameSlotPuschLayerNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:ANTEnna:NUMber", SCPI_NR5G_UL_SetSubFrameSlotPuschAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:MODUlate", SCPI_NR5G_UL_SetSubFrameSlotPuschModulate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:CDM:GROUps:WO:DATA", SCPI_NR5G_UL_SetSubFrameSlotPuschCDMGroupsWithoutData, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:DMRS:SYMBol:LENgth", SCPI_NR5G_UL_SetSubFrameSlotPuschDMRSSymbolLen, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:DMRS:ANTEnna:PORT", SCPI_NR5G_UL_SetSubFrameSlotPuschDMRSAntennaPort, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:NSCR:ID:TYPE", SCPI_NR5G_UL_SetSubFrameSlotPuschNScrIDType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:NSC:ID", SCPI_NR5G_UL_SetSubFrameSlotPuschNSCID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:NRS:ID:TYPE", SCPI_NR5G_UL_SetSubFrameSlotPuschNRSIDType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:GROUp:OR:SEQUence:HOPPing", SCPI_NR5G_UL_SetSubFrameSlotPuschGroupSequenceHopping, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:MCS", SCPI_NR5G_UL_SetSubFrameSlotPuschMCS, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:RV:INDEx", SCPI_NR5G_UL_SetSubFrameSlotPuschRVIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:POWer", SCPI_NR5G_UL_SetSubFrameSlotPuschPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:UL:CELL#:SUB:FRAMe#:SLOT#:PUSCh:DMRS:POWer", SCPI_NR5G_UL_SetSubFrameSlotPuschDMRSPower, 0, SCPI_SEQUENTIAL},

    //DL CELL
    {"WT:NR:SOURce:CONFigure:WAVE:DL:RFPHase:COMPensation", SCPI_NR5G_DL_SetRFPhaseCompensation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL:NUMBer", SCPI_NR5G_DL_SetCellNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:STATe", SCPI_NR5G_DL_SetCellState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:CIF:PRESent", SCPI_NR5G_DL_SetCellCIFPresent, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:DEPLoyment", SCPI_NR5G_DL_SetCellDeployment, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:FREQuency", SCPI_NR5G_DL_SetCellFrequency, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:CHANnel:BW", SCPI_NR5G_DL_SetCellChannelBW, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PHYSical:ID", SCPI_NR5G_DL_SetCellPhysicalID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:DMRS:TYPEa:POS", SCPI_NR5G_DL_SetCellDMRSTypeAPos, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SHARed:SPECtrum:ACCEss", SCPI_NR5G_DL_SetCellSharedSpectrumAccess, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PDSCh:HAC:BOOK", SCPI_NR5G_DL_SetCellPDSCHHACBook, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PDSCh:HAC:BOOK:RST", SCPI_NR5G_DL_SetCellPDSCHHACBookRst, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUL", SCPI_NR5G_DL_SetCellSUL, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SYS:FRAMe:NUMBer:OFFSet", SCPI_NR5G_DL_SetCellSysFrameNumberOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:OCNG:STATe", SCPI_NR5G_DL_SetCellOCNGState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:OCNG:MODE", SCPI_NR5G_DL_SetCellOCNGMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:OCNG:MODUlation", SCPI_NR5G_DL_SetCellOCNGModulation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:OCNG:DATA:TYPE", SCPI_NR5G_DL_SetCellOCNGDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:OCNG:POWEr", SCPI_NR5G_DL_SetCellOCNGPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:TXBW#:SUBCarrier:SPACing", SCPI_NR5G_DL_SetCellTxBWSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:TXBW#:STATe", SCPI_NR5G_DL_SetCellTxBWState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:TXBW#:MAX:RB:NUMBer", SCPI_NR5G_DL_SetCellTxBWMaxRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:TXBW#:OFFSet", SCPI_NR5G_DL_SetCellTxBWOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:TXBW#:K0U", SCPI_NR5G_DL_SetCellTxBWK0U, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:DUPLexing", SCPI_NR5G_DL_SetCellDuplexing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:REF:SCSPacing", SCPI_NR5G_DL_SetCellRefSCSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SLOT:PERIod", SCPI_NR5G_DL_SetCellSlotPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SLOT:NUMBer", SCPI_NR5G_DL_SetCellSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SPECial:SLOT:INDEx", SCPI_NR5G_DL_SetCellSpecialSlotIndex, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:STATe", SCPI_NR5G_DL_SetCellPbchState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:SUBCarrier:SPACing", SCPI_NR5G_DL_SetCellPbchSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:RB:OFFSet", SCPI_NR5G_DL_SetCellPbchRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:SUBCarrier:OFFSet", SCPI_NR5G_DL_SetCellPbchSubCarrierOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:CASE", SCPI_NR5G_DL_SetCellPbchCase, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:LENGth", SCPI_NR5G_DL_SetCellPbchLength, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:POSItion", SCPI_NR5G_DL_SetCellPbchPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:BURSt:SET:PERIod", SCPI_NR5G_DL_SetCellPbchBurstSetPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:HALF:FRAMe:INDEx", SCPI_NR5G_DL_SetCellPbchHalfFrameIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:PBCH:POWEr", SCPI_NR5G_DL_SetCellPbchPBCHPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:PSS:POWEr", SCPI_NR5G_DL_SetCellPbchPSSPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:SSS:POWEr", SCPI_NR5G_DL_SetCellPbchSSSPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:OFFSet:REF:TYPE", SCPI_NR5G_DL_SetCellPbchOffsetRefType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:COMMon:SUBCarrier:SPACing", SCPI_NR5G_DL_SetCellPbchMIBCommonSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:SSB:SUBCarrier:OFFSet", SCPI_NR5G_DL_SetCellPbchMIBSSBSubCarrierOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:COREset:ZERO", SCPI_NR5G_DL_SetCellPbchMIBCORESETZero, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:SEARch:SPACe:ZERO", SCPI_NR5G_DL_SetCellPbchMIBSearchSpaceZero, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:CELL:BARRed", SCPI_NR5G_DL_SetCellPbchMIBCellBarred, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:INTRa:FREQuency:RESElection", SCPI_NR5G_DL_SetCellPbchMIBIntraFrequencyReselection, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PBCH:MIB:AUTO:SCOFfset", SCPI_NR5G_DL_SetCellPbchMIBAutoSCOffset, 0, SCPI_SEQUENTIAL},

    // DL UE
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:ID", SCPI_NR5G_DL_SetUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:SCRAmbling", SCPI_NR5G_DL_SetUEScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:DATA:TYPE", SCPI_NR5G_DL_SetUEDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:INITialization", SCPI_NR5G_DL_SetUEInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:CHANnel:CODIng:STATe", SCPI_NR5G_DL_SetUEChannelCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:SCELlL:GROUp:WATIme", SCPI_NR5G_DL_SetUEScellGroupWithinTime, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:PHACk:SHOT:FEED:RST", SCPI_NR5G_DL_SetUEPhackShotFeedRst, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:PHACk:ACK:NACK:FEED:MODE", SCPI_NR5G_DL_SetUEPhackAckNackFeedMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:PHACk:NFI:TOTAl:INCLude", SCPI_NR5G_DL_SetUEPhackNfiTotalInclude, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:PHACk:UL:TOTAl:INCLude", SCPI_NR5G_DL_SetUEPhackUlTotalInclude, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:PHACk:SLASsign:IDX", SCPI_NR5G_DL_SetUEPhackSlasignIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:PUCCh:SCELl:DYN", SCPI_NR5G_DL_SetUEPuschScellDyn, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:HAREt:INDIc", SCPI_NR5G_DL_SetUEHaretInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:SUB:CARRier:SPACing", SCPI_NR5G_DL_SetUEBwpSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:CYCLic:PREFix", SCPI_NR5G_DL_SetUEBwpCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:RB:NUMBer", SCPI_NR5G_DL_SetUEBwpRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:RB:OFFSet", SCPI_NR5G_DL_SetUEBwpRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:USE:SCRAmble:ID", SCPI_NR5G_DL_SetUEBwpPdschUseScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DATA:SCRAmble:ID", SCPI_NR5G_DL_SetUEBwpPdschDataScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:VRB:TO:PRB:INTErleaver", SCPI_NR5G_DL_SetUEBwpPdschVrbToPrbInterleaver, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:MCS:TABLe", SCPI_NR5G_DL_SetUEBwpPdschMCSTable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:RESOurce:ALLOcation:TYPE", SCPI_NR5G_DL_SetUEBwpPdschResourseAllocation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DMRS:TYPE", SCPI_NR5G_DL_SetUEBwpPdschDmrsConfigType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DMRS:MAX:LENGth", SCPI_NR5G_DL_SetUEBwpPdschDmrsMaxLength, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DMRS:ADDItional:POSItion:INDEx", SCPI_NR5G_DL_SetUEBwpPdschDmrsAddPosIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DMRS:SCRAmbling:ID0", SCPI_NR5G_DL_SetUEBwpPdschDmrsScramblingId0, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DMRS:SCRAmbling:ID1", SCPI_NR5G_DL_SetUEBwpPdschDmrsScramblingId1, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:DMRS:USE:RST", SCPI_NR5G_DL_SetUEBwpPdschDmrsUseRst, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:STATe", SCPI_NR5G_DL_SetUEBwpCoresetState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:SYMBol:NUMBer", SCPI_NR5G_DL_SetUEBwpCoresetSymbolNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:SYMBol:OFFSet", SCPI_NR5G_DL_SetUEBwpCoresetSymbolOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:RB:NUMBer", SCPI_NR5G_DL_SetUEBwpCoresetRBNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:RB:OFFSet", SCPI_NR5G_DL_SetUEBwpCoresetRBOffset, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:MAX:CWNUm:PERDci", SCPI_NR5G_DL_SetUEBwpPdschMaxCWNumPerDCI, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:RBGSize:TYPE", SCPI_NR5G_DL_SetUEBwpPdschRBGSizeType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:MAX:CBGPer:TB", SCPI_NR5G_DL_SetUEBwpPdschMaxCBGPerTB, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:CBG:FLUSh:INDIc", SCPI_NR5G_DL_SetUEBwpPdschCbgFlushInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:PRECoding", SCPI_NR5G_DL_SetUEBwpPdschPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:PRBUnd:TYPE", SCPI_NR5G_DL_SetUEBwpPdschPrBundType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:MIN:ENTRies:NUM", SCPI_NR5G_DL_SetUEBwpPdschMinEntriesNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:PRIOrity:INDIc", SCPI_NR5G_DL_SetUEBwpPdschPriorityInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PDSCh:HARC:PROC:NUM", SCPI_NR5G_DL_SetUEBwpPdschHarProcNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:FDREs:MAX:USEBitmap", SCPI_NR5G_DL_SetUEBwpCoresetFDResUseBitmap, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:BITMap", SCPI_NR5G_DL_SetUEBwpCoresetBitMap, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:COREset:ID", SCPI_NR5G_DL_SetUEBwpCoresetCoresetID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:PREGran:TYPE", SCPI_NR5G_DL_SetUEBwpCoresetPreGranType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:USEDmrs:SCRAm:ID", SCPI_NR5G_DL_SetUEBwpCoresetUseDmrsScramID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:DMRS:SCRAm:ID", SCPI_NR5G_DL_SetUEBwpCoresetDmrsScramID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:DMRS:REFPoint", SCPI_NR5G_DL_SetUEBwpCoresetDmrsRefPoint, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:INTErleave:STATe", SCPI_NR5G_DL_SetUEBwpCoresetInterleaveState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:BUNDle:SIZE", SCPI_NR5G_DL_SetUEBwpCoresetBundleSize, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:SHFIt:IDX", SCPI_NR5G_DL_SetUEBwpCoresetShfitIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:INTErleaver:SIZE", SCPI_NR5G_DL_SetUEBwpCoresetInterleaverSize, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:COREset:MAM:CANDi", SCPI_NR5G_DL_SetUEBwpCoresetMaxCandi, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:REPOrt:TRIGger:SIZE", SCPI_NR5G_DL_SetUEBwpCoresetReportTriggerSize, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PMNOitor:ADAPt:INDIc", SCPI_NR5G_DL_SetUEBwpCoresetPmnoitorAdaptInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:SRS:RES:SET:INDIc", SCPI_NR5G_DL_SetUEBwpCoresetSrsResSetInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:SRS:REST:SET:INDIc", SCPI_NR5G_DL_SetUEBwpCoresetSrsRestSetInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PRECoding:INFO:SECOnd", SCPI_NR5G_DL_SetUEBwpCoresetPrecodingInfoSecond, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:SRS:OFFSet:INDIc", SCPI_NR5G_DL_SetUEBwpCoresetSrsOffsetInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:MAX:SECOnd:DL:ASSIgn", SCPI_NR5G_DL_SetUEBwpCoresetMaxSecondDlAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:MAX:THIRd:DL:ASSIgn", SCPI_NR5G_DL_SetUEBwpCoresetMaxThirdDlAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:PMNOitor:ADAPt:INDIc:FT", SCPI_NR5G_DL_SetUEBwpCoresetPmnoitorAdaptIndF11, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:ENHAnce:THREE:CODEbook:INDIc", SCPI_NR5G_DL_SetUEBwpCoresetEnhanceThreeCodebookInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:SRS:OFFSet:INDIC:FT", SCPI_NR5G_DL_SetUEBwpCoresetSrsOffsetIndF11, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWP#:GROUpP:NUM", SCPI_NR5G_DL_SetUEBwpCoresetGroupPNum, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:SUB:CARRier:SPACing", SCPI_NR5G_DL_SetUEBwpUlSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:CYCLic:PREFix", SCPI_NR5G_DL_SetUEBwpUlCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:RB:NUMBer", SCPI_NR5G_DL_SetUEBwpUlRBNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:RB:OFFSet", SCPI_NR5G_DL_SetUEBwpUlRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:TRANsform:PRECoder", SCPI_NR5G_DL_SetUEBwpUlPuschTransformPrecoder, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:MAX:RANK", SCPI_NR5G_DL_SetUEBwpUlPuschMaxRank, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:TX:CONFig", SCPI_NR5G_DL_SetUEBwpUlPuschTxConfig, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:TPMI", SCPI_NR5G_DL_SetUEBwpUlPuschTPMI, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:USE:SCRAmble:ID", SCPI_NR5G_DL_SetUEBwpUlPuschUsePuschScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DATA:SCRAmble:ID", SCPI_NR5G_DL_SetUEBwpUlPuschDataScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:MCS:TABLe", SCPI_NR5G_DL_SetUEBwpUlPuschMcsTab, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:FREQ:HOPMode", SCPI_NR5G_DL_SetUEBwpUlPuschFreqHopMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:FREQ:MAX:CBGPer:TB", SCPI_NR5G_DL_SetUEBwpUlPuschFreqMaxCBGPerTB, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:FREQ:MIN:SCHEd:OFFSet", SCPI_NR5G_DL_SetUEBwpUlPuschFreqMinSchedOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:FREQ:HARQ:PROC:NUM", SCPI_NR5G_DL_SetUEBwpUlPuschFreqHarqProcNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:PRIOrity:INDIC", SCPI_NR5G_DL_SetUEBwpUlPuschPriorityIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:INVAlid:SYMBol:INDIc", SCPI_NR5G_DL_SetUEBwpUlPuschInvalidSymbolIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:OLPC:PARAM", SCPI_NR5G_DL_SetUEBwpUlPuschOlpcParam, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:PUSCh:SET:LIST", SCPI_NR5G_DL_SetUEBwpUlPuschSetList, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:UL:ACCEss:CONF:LIST", SCPI_NR5G_DL_SetUEBwpUlAccessConfigList, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:USE:SECOnd:TPC:COMMand", SCPI_NR5G_DL_SetUEBwpUlUseSecondTpcCommand, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:RESOurce:ALLOcation", SCPI_NR5G_DL_SetUEBwpUlPuschResourceAllocation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:TYPE", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsConfigType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:MAX:LENGth", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsMaxLength, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:ADDItional:POSItion", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsAdditionalPos, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:SCRAmbling:ID0", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsScramblingID0, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:SCRAmbling:ID1", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsScramblingID1, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:USE:RST:DMRS", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsUseRstDmrs, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUSCh:DMRS:NPUSch:ID", SCPI_NR5G_DL_SetUEBwpUlPuschDmrsNPuschID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:USE:INTErface", SCPI_NR5G_DL_SetUEBwpUlPucchUseInterface, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:TIMIng:MAP", SCPI_NR5G_DL_SetUEBwpUlPucchTimingMap, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:CPTExt", SCPI_NR5G_DL_SetUEBwpUlPucchCptext, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:USE:SECOnd:TPC:COMMand", SCPI_NR5G_DL_SetUEBwpUlPucchUseSecondTpcCommand, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:SRS:RESOurce:SETS", SCPI_NR5G_DL_SetUEBwpUlPucchSrsResourceSets, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:SRS:RESOurce:NUM", SCPI_NR5G_DL_SetUEBwpUlPucchSrsResourceNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:UE:BWPUl#:PUCCh:SRS:PORT:NUM", SCPI_NR5G_DL_SetUEBwpUlPucchSrsPortNum, 0, SCPI_SEQUENTIAL},

    //DL Frame
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:PDSCh:SCHEduling:TYPE", SCPI_NR5G_DL_SetPdschSchedulingType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:RESTrict:SEARch:SPACe", SCPI_NR5G_DL_SetRestrictSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF:CONFig:NUM", SCPI_NR5G_DL_SetSubfrmCfgNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SLOT:CONFig:NUM", SCPI_NR5G_DL_SetSubfrmSlotCfgNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:STATe", SCPI_NR5G_DL_SetSubfrmSlotPdcchState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:UNUSed:CCES", SCPI_NR5G_DL_SetSubfrmSlotPdcchUnusedCCEs, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DATA:TYPE", SCPI_NR5G_DL_SetSubfrmSlotPdcchDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:INIT", SCPI_NR5G_DL_SetSubfrmSlotPdcchInit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:TCI:PRESent:DCI", SCPI_NR5G_DL_SetSubfrmSlotPdcchTciPresentDci, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:AUTO:DCI", SCPI_NR5G_DL_SetSubfrmSlotPdcchAutoDci, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:STATe", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:USAGe", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciUsage, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SEARch:SPACe", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:AGGRegation:LEVEl", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciAggregationLevel, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:CANDidate", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciCandidate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:CCE:INDEx", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciCCEIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:TDALloc:TAB:TYPE", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciTDAllocTabType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:POWEr", SCPI_NR5G_DL_SetSubfrmSlotPdcchPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:PDSCh:POWEr", SCPI_NR5G_DL_SetSubfrmSlotPdcchPdschPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:FREQuency:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchDF10_FreqDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:TIME:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchDF10_TimeDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:VRB:TO:PRB:MAPPing", SCPI_NR5G_DL_SetSubfrmPdcchDF10_VRBtoPRBMapping, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:MODUlation:CODing:SCHEme", SCPI_NR5G_DL_SetSubfrmPdcchDF10_ModulCodingScheme, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:NEW:DATA:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF10_NewDataIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:REDUndancy:VERSion:INDEx", SCPI_NR5G_DL_SetSubfrmPdcchDF10_RedunVerIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:HARQ:PROCess:NUM", SCPI_NR5G_DL_SetSubfrmPdcchDF10_HARQProcNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:DL:ASSIgn:INDEx", SCPI_NR5G_DL_SetSubfrmPdcchDF10_DLAssignIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:TPC:CMD:FOR:SCHEduled:PUCCh", SCPI_NR5G_DL_SetSubfrmPdcchDF10_TPCCmd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:PUCCh:RESOurce:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF10_PucchResIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:PDSCh:TO:HARQ:FEEDback:TIMing:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF10_PdschToHARQ, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat10:CHANnel:ACCEss:CPTExt", SCPI_NR5G_DL_SetSubfrmPdcchDF10_ChannelAccessCptExt, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:CIF", SCPI_NR5G_DL_SetSubfrmPdcchDF11_CIF, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:BW:PARt:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF11_BWPartIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:FREQuency:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchDF11_FreqDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:TIME:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchDF11_TimeDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:VRB:TO:PRB:MAPPing", SCPI_NR5G_DL_SetSubfrmPdcchDF11_VRBtoPRBMapping, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PRB:BUNDling:SIZE:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PRBBundSizeIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:RATE:MATCh:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF11_RateMatchIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:ZP:CSIRs:TRIGger", SCPI_NR5G_DL_SetSubfrmPdcchDF11_ZPCSIRSTrig, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:MODUlation:CODIng:SCHEme:TB1", SCPI_NR5G_DL_SetSubfrmPdcchDF11_MCS_TB1, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:NEW:DATA:INDIcator:TB1", SCPI_NR5G_DL_SetSubfrmPdcchDF11_NewDataIndic_TB1, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:REDUndancy:VERSion:INDEx:TB1", SCPI_NR5G_DL_SetSubfrmPdcchDF11_RedunVerIdx_TB1, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:MODUlation:CODIng:SCHEme:TB2", SCPI_NR5G_DL_SetSubfrmPdcchDF11_MCS_TB2, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:NEW:DATA:INDIcator:TB2", SCPI_NR5G_DL_SetSubfrmPdcchDF11_NewDataIndic_TB2, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:REDUndancy:VERSion:INDEx:TB2", SCPI_NR5G_DL_SetSubfrmPdcchDF11_RedunVerIdx_TB2, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:HARQ:PROCess:NUM", SCPI_NR5G_DL_SetSubfrmPdcchDF11_HARQProcNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:DL:ASSIgn:INDEx", SCPI_NR5G_DL_SetSubfrmPdcchDF11_DLAssignIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:TPC:CMD:For:SCHEduled:PUCCh", SCPI_NR5G_DL_SetSubfrmPdcchDF11_TPCCmdForPucch, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:SECOnd:TPC:CMD:For:SCHEduled:PUCCh", SCPI_NR5G_DL_SetSubfrmPdcchDF11_SecondTPCCmdForPucch, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PUCCh:RESOurce:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PucchResIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PDSCh:TO:HARQ:FEEDback:TIMing:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PdschToHarqIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:ONE:SHOT:HACK:REQ", SCPI_NR5G_DL_SetSubfrmPdcchDF11_OneShotHackReq, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:ENHAnce:TYPE:THREE:CODEbook:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_EnhanceTypeThreeCodebookIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PDSCh:GROUp:IDX", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PdschGroupIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:NEW:FEED:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_NewFeedIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:REQ:PDSCH:GROUp:NUM", SCPI_NR5G_DL_SetSubfrmPdcchDF11_ReqPdschGroupNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:HARQ:ACK:RET:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_HarqAckRetIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:ANTEnna:PORTs", SCPI_NR5G_DL_SetSubfrmPdcchDF11_AntennaPorts, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:TX:CONFig:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchDF11_TxConfigIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:SRS:REQUest", SCPI_NR5G_DL_SetSubfrmPdcchDF11_SRSReq, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:SRS:OFFSet:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_SRSOffsetIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:CBG:TX:INFO", SCPI_NR5G_DL_SetSubfrmPdcchDF11_CBGTI, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:CBG:FLUShing:OUT:INFO", SCPI_NR5G_DL_SetSubfrmPdcchDF11_CBGFI, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:DMRS:SEQUence:INIT", SCPI_NR5G_DL_SetSubfrmPdcchDF11_DMRSSeqInit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PRIOrity:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PriorityIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:CHANNel:ACCEss:CPEXt", SCPI_NR5G_DL_SetSubfrmPdcchDF11_ChannelAccessCpext, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:MIN:APP:SCHE:OFFSet:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_MinAppSchedOffsetIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:SCELl:DORMancy:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_CellDormancyIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PMONitor:ADAPt:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PmonAdapInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:FORMat11:PUCCh:CELL:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchDF11_PucchCellIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:STATe", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:USAGe", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleUsage, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:SEARch:SPACe", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:AGGRegation:LEVEL", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleAggregationLevel, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:CANDidate", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleCandidate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:CCE:INDEx", SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleCCEIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:FREQuency:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_FreqDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:TIME:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_TimeDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:FREQ:HOP:FLAG", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_FreqHopFlg, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:MODUlation:CODing:SCHEme", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_ModulCodingScheme, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:NEW:DATA:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_NewDataIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:REDUndancy:VERSion:INDEx", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_RedunVerIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:HARQ:PROCess:NUM", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_HARQProcNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:TPC:CMD:For:SCHEduled:PUCCh", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_TPCCmd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:CHANNel:ACCEss:CPEXt", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_ChannelAccessCpext, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat00:US:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_USIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:CIF", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_CIF, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:RESErve:BITS", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_ReserveBits, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:US:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_USIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:FREQuency:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_FreqDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:TIME:DOMAin:RESOurce:ASSIgn", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_TimeDomainResAssign, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:MODUlation:CODing:SCHEme", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_ModulCodingScheme, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:NEW:DATA:INDIcator", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_NewDataIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:REDUndancy:VERSion:INDEx", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_RedunVerIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:HARQ:PROCess:NUM", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_HARQProcNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:DL:ASSIgn:INDEx#", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_DLAssignIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:TPC:CMD:For:SCHEduled:PUSCh#", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_TPCCmd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:SRS:RESOurce:SET:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_SRSResSetInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:SRS:RESOurce:INDIc#", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_SRSResInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:PRE:INFO:LAYER:NUM#", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_PreInfoLayerNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:ANTEnna:PORTs", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_AntennaPorts, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:SRS:REQUest", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_SRSReq, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:SRS:OFFSet:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_SRSOffsetIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:CSI:REQUest", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_CSIReq, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:CBGTi", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_CBGTI, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:PTRS:DMRS:ASSOciate#", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_PTRSDmrsAssociate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:DMRS:SEQ:INIT", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_DmrsSeqInit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:ULSCh:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_ULSchIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:CHANnel:ACCEss:CPEXt", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_ChannelAccessCpext, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:OLOOp:PCTR:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_OLoopPctrIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:PRIOrity:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_PriorityIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:INVAlid:SYMBol:PATTern:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_InvalidSymbPatternIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:PMONitor:ADAPt:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_PmonAdapIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDCCh:DCI:SCHEdule:FORMat01:MIN:APP:SCHEd:OFFSet:INDIc", SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_MinAppSchdOffsetIndic, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:STATe", SCPI_NR5G_DL_SetSubfrmPdschState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:MAPPing:TYPE", SCPI_NR5G_DL_SetSubfrmPdschMappingType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:SYMBol:NUM", SCPI_NR5G_DL_SetSubfrmPdschSymbolNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:SYMBol:OFFSet", SCPI_NR5G_DL_SetSubfrmPdschSymbolOffset, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:RES:ALLOc", SCPI_NR5G_DL_SetSubfrmPdschResAlloc, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:BIT:MAP", SCPI_NR5G_DL_SetSubfrmPdschBitmap, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:RB:NUM", SCPI_NR5G_DL_SetSubfrmPdschRBNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:RB:OFFSet", SCPI_NR5G_DL_SetSubfrmPdschRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:CODEword:NUM", SCPI_NR5G_DL_SetSubfrmPdschCWNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:LAYEr:NUM", SCPI_NR5G_DL_SetSubfrmPdschLayerNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:ANTEnna:NUM", SCPI_NR5G_DL_SetSubfrmPdschAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:CDM:GROUps:WO:DATA", SCPI_NR5G_DL_SetSubfrmPdschCmdGroupsWOData, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:DMRS:SYMBol:LENGth", SCPI_NR5G_DL_SetSubfrmPdschDmrsSymbolLen, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:DMRS:ANTEnna:PORt#", SCPI_NR5G_DL_SetSubfrmPdschDMRSAntennaPort, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:NSCRid:TYPE", SCPI_NR5G_DL_SetSubfrmPdschNscridType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:NSCId", SCPI_NR5G_DL_SetSubfrmPdschNscid, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:DMRS:POWEr", SCPI_NR5G_DL_SetSubfrmPdschDmrsPower, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:TRANsport:BLOCk#:MODUlate", SCPI_NR5G_DL_SetSubfrmPdschTransBlockMod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:TRANsport:BLOCk#:MCS", SCPI_NR5G_DL_SetSubfrmPdschTransBlockMCS, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:TRANsport:BLOCk#:REDUndancy:VERSion:INDEx", SCPI_NR5G_DL_SetSubfrmPdschTransBlockRedunVerIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:CSIRs:CELL:INDEx", SCPI_NR5G_DL_SetSubfrmPdschCSIRSCellIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SOURce:CONFigure:WAVE:DL:CELL#:SUBF#:SLOT#:PDSCh:CSIRs:STATe", SCPI_NR5G_DL_SetSubfrmPdschCSIRSState, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_nr5g_analyze_cmd[] = {

    {"WT:NR:SENSe:CONFigure:ANALy:REDCap:ENABle", SCPI_NR5G_SetAlzRedcapEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VERSion", SCPI_NR5G_SetAlzVersion, 0, SCPI_SEQUENTIAL},

    //UL general
    {"WT:NR:SENSe:CONFigure:ANALy:UL:RF:PHASe:COMPensation", SCPI_NR5G_UL_SetAlzRFPhaseCompensation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:DUPLexing", SCPI_NR5G_UL_SetAlzDuplexing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:SLOT:PERIod", SCPI_NR5G_UL_SetAlzSlotPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:TDD#:UL:SLOT:NUMBer", SCPI_NR5G_UL_SetAlzTddULSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:TDD#:DL:SLOT:NUMBer", SCPI_NR5G_UL_SetAlzTddDlSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:TDD#:UL:SYMBol:NUMBer", SCPI_NR5G_UL_SetAlzTddULSymbolNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:TDD#:DL:SYMBol:NUMBer", SCPI_NR5G_UL_SetAlzTddDlSymbolNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:NSVAlue", SCPI_NR5G_UL_SetAlzNSValue, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL:NUM", SCPI_NR5G_UL_SetAlzCellNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:FREQuency", SCPI_NR5G_UL_SetAlzFrequency, 0, SCPI_SEQUENTIAL},

    //assign views
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:EVM:ENABle", SCPI_NR5G_SetAlzViewEvmEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:MERR:ENABle", SCPI_NR5G_SetAlzViewMerrEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:PERR:ENABle", SCPI_NR5G_SetAlzViewPerrEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:EVMSubcare:ENABle", SCPI_NR5G_SetAlzViewEvmSubcarEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:IBE:ENABle", SCPI_NR5G_SetAlzViewIbeEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:ESFLat:ENABle", SCPI_NR5G_SetAlzViewEsflatEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:IQ:CONStel:ENABle", SCPI_NR5G_SetAlzViewIqconstelEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:TX:PWR:ENABle", SCPI_NR5G_SetAlzViewTxPwrEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:PMONitor:ENABle", SCPI_NR5G_SetAlzViewPMonitorEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:PWR:DYN:ENABle", SCPI_NR5G_SetAlzViewPwrDynEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:SPECT:ENABle", SCPI_NR5G_SetAlzViewSpectEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:ACLR:ENABle", SCPI_NR5G_SetAlzViewACLREnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:VIEW:TX:MEASure:ENABle", SCPI_NR5G_SetAlzViewTxMeasureEnable, 0, SCPI_SEQUENTIAL},
    // {"WT:NR:SENSe:CONFigure:ANALy:VIEW:PDSCh:INFO:ENABle", SCPI_NR5G_SetAlzViewPdschInfoEnable, 0, SCPI_SEQUENTIAL},
    // {"WT:NR:SENSe:CONFigure:ANALy:VIEW:SSB:INFO:ENABle", SCPI_NR5G_SetAlzViewSSBInfoEnable, 0, SCPI_SEQUENTIAL},

    //UL Cell
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BW", SCPI_NR5G_UL_SetAlzCellBW, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:PHYSical:ID", SCPI_NR5G_UL_SetAlzCellPhysicalID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:DMRS:TYPE:A:POSItion", SCPI_NR5G_UL_SetAlzCellDmrsTypeAPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:USE:SCSPacing", SCPI_NR5G_UL_SetAlzCellUseScSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:OFFSet:TO:CARRier", SCPI_NR5G_UL_SetAlzCellOffsetToCarrier, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:SUB:CARRier:SPACing", SCPI_NR5G_UL_SetAlzCellBwpSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:CYCLic:PREFix", SCPI_NR5G_UL_SetAlzCellBwpCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:RB:NUMber", SCPI_NR5G_UL_SetAlzCellBwpRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:RB:OFFSet", SCPI_NR5G_UL_SetAlzCellBwpRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:CONFig:TYPE#", SCPI_NR5G_UL_SetAlzCellBwpConfigType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:TRANsform:PRECoder", SCPI_NR5G_UL_SetAlzCellBwpTransformPrecoder, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:DMRS:MAX:LENgth#", SCPI_NR5G_UL_SetAlzCellBwpDMRSMaxLen, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:DMRS:ADDitional:POSition#", SCPI_NR5G_UL_SetAlzCellBwpDMRSAddPosInd, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:DMRS:USE:RSIXteen:DMRS", SCPI_NR5G_UL_SetAlzCellBwpDMRSUseSixteenDmrs, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:CELL#:BWP:DMRS:NPUSch:ID", SCPI_NR5G_UL_SetAlzCellBwpDMRSNpuschID, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SENSe:CONFigure:ANALy:UL:CHANnel:TYPE", SCPI_NR5G_UL_SetAlzChannelType, 0, SCPI_SEQUENTIAL},

    //UL PUSCh
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:MAPPing:TYPE", SCPI_NR5G_UL_SetAlzPuschMappingType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:SYMBol:NUMBer", SCPI_NR5G_UL_SetAlzPuschSymbolNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:SYMBol:OFFSet", SCPI_NR5G_UL_SetAlzPuschSymbolOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:RB:DETEct:MODE", SCPI_NR5G_UL_SetAlzPuschRBDetectMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:RB:NUMBer", SCPI_NR5G_UL_SetAlzPuschRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:RB:OFFSet", SCPI_NR5G_UL_SetAlzPuschRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:LAYEr:NUMBer", SCPI_NR5G_UL_SetAlzPuschLayerNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:ANTEnna:NUMBer", SCPI_NR5G_UL_SetAlzPuschAntennaNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:CDM:GROUps:WO:DATA", SCPI_NR5G_UL_SetAlzPuschCDMGroupsWoData, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:DMRS:ANTEnna:PORT", SCPI_NR5G_UL_SetAlzPuschDmrsAntennaPort, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:DMRS:SYMBol:LENGth", SCPI_NR5G_UL_SetAlzPuschDmrsSybolLength, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:DMRS:SEQUence:GENEration:INIT", SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenInit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:DMRS:SEQUence:GENEration:DMRS:ID", SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:DMRS:SEQUence:GENEration:NSCId", SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenNscid, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:GROUp:OR:SEQUence:HOPPing", SCPI_NR5G_UL_SetAlzPuschGroupOrSequenceHopping, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:MODUlate", SCPI_NR5G_UL_SetAlzPuschModulate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:CHANnel:CODEing:STATe", SCPI_NR5G_UL_SetAlzPuschChannelCodeingState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:SCRAmbling", SCPI_NR5G_UL_SetAlzPuschScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:MCS", SCPI_NR5G_UL_SetAlzPuschMCS, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:UEID", SCPI_NR5G_UL_SetAlzPuschUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:UL:PUSCh#:RVINdex", SCPI_NR5G_UL_SetAlzPuschRVIndex, 0, SCPI_SEQUENTIAL},
 
    //DL
    {"WT:NR:SENSe:CONFigure:ANALy:DL:TDD#:UL:SLOT:NUMBer", SCPI_NR5G_DL_SetAlzTddULSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:TDD#:DL:SLOT:NUMBer", SCPI_NR5G_DL_SetAlzTddDlSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:TDD#:UL:SYMBol:NUMBer", SCPI_NR5G_DL_SetAlzTddULSymbolNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:TDD#:DL:SYMBol:NUMBer", SCPI_NR5G_DL_SetAlzTddDlSymbolNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:DUPLexing", SCPI_NR5G_DL_SetAlzDuplexing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:PERIod", SCPI_NR5G_DL_SetAlzSlotPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:NUM", SCPI_NR5G_DL_SetAlzSlotNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SPECial:SLOT:INDEx", SCPI_NR5G_DL_SetAlzSpecialSlotIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:RF:PHASe:COMPensation", SCPI_NR5G_DL_SetAlzRFPhaseCompensation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL:NUM", SCPI_NR5G_DL_SetAlzCellNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:FREQuency", SCPI_NR5G_DL_SetAlzCellFrequency, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BW", SCPI_NR5G_DL_SetAlzCellBW, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:PHYSical:ID", SCPI_NR5G_DL_SetAlzCellPhysicalID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:DMRS:TYPE:A:POSItion", SCPI_NR5G_DL_SetAlzCellDmrsTypeAPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:USE:SCSPacing", SCPI_NR5G_DL_SetAlzCellUseScSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:OFFSet:TO:CARRier", SCPI_NR5G_DL_SetAlzCellOffsetToCarrier, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:SUB:CARRier:SPACing", SCPI_NR5G_DL_SetAlzCellBwpSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:CYCLic:PREFix", SCPI_NR5G_DL_SetAlzCellBwpCyclicPrefix, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:RB:NUM", SCPI_NR5G_DL_SetAlzCellBwpRBNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:RB:OFFSet", SCPI_NR5G_DL_SetAlzCellBwpRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:VRB:TO:PRB:INTer:LEAVer", SCPI_NR5G_DL_SetAlzCellBwpPdschVrbToPrbInterLeaver, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:MCS:TABLe", SCPI_NR5G_DL_SetAlzCellBwpPdschMcsTable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:RESOurce:ALLOcation", SCPI_NR5G_DL_SetAlzCellBwpPdschResourceAlloc, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:RBG:SIZE:TYPE", SCPI_NR5G_DL_SetAlzCellBwpPdschRBGSizeType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:CONFig:TYPE", SCPI_NR5G_DL_SetAlzCellBwpPdschConfigType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:MAX:LENGth", SCPI_NR5G_DL_SetAlzCellBwpPdschMaxLen, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:ADDItional:POSItion", SCPI_NR5G_DL_SetAlzCellBwpPdschAdditionalPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:PDSCh:USE:RSIXteen:DMRS", SCPI_NR5G_DL_SetAlzCellBwpPdschUseSixteenDmrs, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:STATe", SCPI_NR5G_DL_SetAlzCellBwpCoresetState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:SYMBol:NUMBer", SCPI_NR5G_DL_SetAlzCellBwpCoresetSymNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:SYMBol:OFFSet", SCPI_NR5G_DL_SetAlzCellBwpCoresetSymbolOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:FD:RES", SCPI_NR5G_DL_SetAlzCellBwpCoresetFDRes, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:RB:NUMBer", SCPI_NR5G_DL_SetAlzCellBwpCoresetRBNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:RB:OFFSet", SCPI_NR5G_DL_SetAlzCellBwpCoresetRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CELL#:BWP:COREset:BIT:MAP", SCPI_NR5G_DL_SetAlzCellBwpCoresetBitMap, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:CHANnel:TYPE", SCPI_NR5G_DL_SetAlzChannelType, 0, SCPI_SEQUENTIAL},

    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:OFFSet:REF:TYPE", SCPI_NR5G_DL_SetAlzScheSlotPbchOffSetRefType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:STATe", SCPI_NR5G_DL_SetAlzScheSlotPbchState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:SUBCarrier:SPACing", SCPI_NR5G_DL_SetAlzScheSlotPbchSubSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:RB:OFFSet", SCPI_NR5G_DL_SetAlzScheSlotPbchRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:SUBCarrier:OFFSet", SCPI_NR5G_DL_SetAlzScheSlotPbchSubOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:CASE", SCPI_NR5G_DL_SetAlzScheSlotPbchCase, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:LENGth", SCPI_NR5G_DL_SetAlzScheSlotPbchLength, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:POSItion", SCPI_NR5G_DL_SetAlzScheSlotPbchPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:BURSt:SET:PERIod", SCPI_NR5G_DL_SetAlzScheSlotPbchBurstSetPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PBCH:ALLOcation:HALF:FRAMe:INDEx", SCPI_NR5G_DL_SetAlzScheSlotPbchHalfFrameIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDCCh:ALLOcation:STATe", SCPI_NR5G_DL_SetAlzScheSlotPdcchState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:MAPPing:TYPE", SCPI_NR5G_DL_SetAlzScheSlotPdschMapType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:NUMBer:SYMBols", SCPI_NR5G_DL_SetAlzScheSlotPdschSymbNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:STARt:SYMBol", SCPI_NR5G_DL_SetAlzScheSlotPdschSymbOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:RB:AUTO", SCPI_NR5G_DL_SetAlzScheSlotPdschRBDetMode, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:NUMBer:RBS", SCPI_NR5G_DL_SetAlzScheSlotPdschRBNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:STARt:RB", SCPI_NR5G_DL_SetAlzScheSlotPdschRBOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:BIT:MAP", SCPI_NR5G_DL_SetAlzScheSlotPdschBitMap, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:LAYEr:NUMBer", SCPI_NR5G_DL_SetAlzScheSlotPdschLayerNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:ANTEnna:NUMBer", SCPI_NR5G_DL_SetAlzScheSlotPdschAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:CDM:GROUps:WO:DATA", SCPI_NR5G_DL_SetAlzScheSlotPdschCDMGroupsWoData, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:DMRS:SYMBol:LENGth", SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsSymbLen, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:DMRS:ANTEnna:PORT#", SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsAntPort, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:DMRS:SEQUence:GENEration:INITialization", SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsInitType, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:DMRS:SEQUence:GENEration:DRMSid", SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:DMRS:SEQUence:GENEration:NSCId", SCPI_NR5G_DL_SetAlzScheSlotPdschNSCID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:CODEwords", SCPI_NR5G_DL_SetAlzScheSlotPdschCodewords, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:MODUlate#", SCPI_NR5G_DL_SetAlzScheSlotPdschModulate, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:CHANnel:CODIng:STATe", SCPI_NR5G_DL_SetAlzScheSlotPdschChanCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:SCRAmbling", SCPI_NR5G_DL_SetAlzScheSlotPdschScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:USE:PDSCh:SCRAmbling:ID", SCPI_NR5G_DL_SetAlzScheSlotPdschUsePdschScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:DATA:SCRAmbling:ID", SCPI_NR5G_DL_SetAlzScheSlotPdschDataScrambleID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:UE:ID", SCPI_NR5G_DL_SetAlzScheSlotPdschUeID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:MCS#", SCPI_NR5G_DL_SetAlzScheSlotPdschMCS, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:DL:SLOT:ALLOcation:CHANnel#:PDSCh:ALLOcation:RV:INDEx#", SCPI_NR5G_DL_SetAlzScheSlotPdschRvIdx, 0, SCPI_SEQUENTIAL},

    // Measure
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:SUBFrame:INDEx", SCPI_NR5G_Measure_SetAlzSubFrameIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:SLOT:INDEx", SCPI_NR5G_Measure_SetAlzSlotIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:DMRS:CONStellation:STATe", SCPI_NR5G_Measure_SetAlzDmrsConsState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:EVM:DELEte:DC:FLAG", SCPI_NR5G_Measure_SetAlzEvmDeleteDcFlag, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:EVM:VS:SUBCarrier", SCPI_NR5G_Measure_SetAlzEVMSubcarrier, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:SYMBol:INDEx", SCPI_NR5G_Measure_SetAlzSymbolIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:WINDow:POSItion", SCPI_NR5G_Measure_SetAlzWindowPosition, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:EXCAbn:SYMBol", SCPI_NR5G_Measure_SetAlzExcAbnormalSymbol, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:EXCEption:ENAble", SCPI_NR5G_Measure_SetAlzExceptionEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:NRB:VIEW:FILTer:ENAble", SCPI_NR5G_Measure_SetAlzVFilterEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:NRB:VIEW:FILTer:NUMBer", SCPI_NR5G_Measure_SetAlzNrbViewFilter, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:SUBFrame:NUMBer", SCPI_NR5G_Measure_SetAlzSfNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:SLOT:ALL:ENAble", SCPI_NR5G_Measure_SetAlzSlotAllEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:STATistic:COUNt", SCPI_NR5G_Measure_SetAlzStatisticCount, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:TXDC:OFFSet", SCPI_NR5G_Measure_SetAlzTxDcOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:SEM:STAT:NUMBer", SCPI_NR5G_Measure_SetAlzSEMStatNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:ACLR:STAT:NUMBer", SCPI_NR5G_Measure_SetAlzAclrStatNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:UTRA:ENABle", SCPI_NR5G_Measure_SetAlzUtraEnable, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:PWR:DYNAmic:STAT:NUMBer", SCPI_NR5G_Measure_SetAlzPwrDynStatNum, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:ANALy:MEASure:TX:PWR:STAT:NUMBer", SCPI_NR5G_Measure_SetAlzTxPwrStatNum, 0, SCPI_SEQUENTIAL},

    // Nr5g Limits
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:EVMRms:STATe", SetVsaNrLimitModEvmRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:EVMRms:LIMIt", SetVsaNrLimitModEvmRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:EVMPeak:STATe", SetVsaNrLimitModEvmPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:EVMPeak:LIMIt", SetVsaNrLimitModEvmPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:MERRrms:STATe", SetVsaNrLimitModMerrRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:MERRrms:LIMIt", SetVsaNrLimitModMerrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:MERRpeak:STATe", SetVsaNrLimitModMerrPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:MERRpeak:LIMIt", SetVsaNrLimitModMerrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:PHERrrms:STATe", SetVsaNrLimitModPherrRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:PHERrrms:LIMIt", SetVsaNrLimitModPherrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:PHERrpeak:STATe", SetVsaNrLimitModPherrPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:PHERrpeak:LIMIt", SetVsaNrLimitModPherrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:FREQerr:STATe", SetVsaNrLimitModFreqErrState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:FREQerr:LIMIt", SetVsaNrLimitModFreqErrLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IQOFfset:STATe", SetVsaNrLimitModIqOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IQOFfset:VALUe", SetVsaNrLimitModIqOffsetValue, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IBE:STATe", SetVsaNrLimitModIBEState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IBE:GENMin", SetVsaNrLimitModIBEGenMin, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IBE:GENEvm", SetVsaNrLimitModIBEGenEvm, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IBE:GENPwr", SetVsaNrLimitModIBEGenPwr, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IBE:IQIMg", SetVsaNrLimitModIBEIqImag, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:IBE:IQOFfset", SetVsaNrLimitModIBEIqOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:SPEC:FLAT:STATe", SetVsaNrLimitModSpecFlatState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:SPEC:FLAT:RANGe#", SetVsaNrLimitModSpecFlatRange, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:SPEC:FLAT:MAX#", SetVsaNrLimitModSpecFlatMax, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:MODE#:SPEC:FLAT:EDGE:DIST", SetVsaNrLimitModSpecFlatEdgeDist, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:OBW:STATe", SetVsaNrLimitSpecObwState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:OBW:LIMIt", SetVsaNrLimitSpecObwLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:SEM#:STATe", SetVsaNrLimitSpecSemState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:SEM#:STARt:FREQ", SetVsaNrLimitSpecSemStartFreq, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:SEM#:STOP:FREQ", SetVsaNrLimitSpecSemStopFreq, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:SEM#:LIMIt:PWR", SetVsaNrLimitSpecSemLimitPwr, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:SEM#:RBW", SetVsaNrLimitSpecSemRbw, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:UTRA#:RELE:STATe", SetVsaNrLimitSpecUtraRelState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:UTRA#:RELE:LIMIt", SetVsaNrLimitSpecUtraRelLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:UTRA#:ABS:STATe", SetVsaNrLimitSpecUtraAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:UTRA#:ABS:PWR", SetVsaNrLimitSpecUtraAbsPwr, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:NR:RELE:STATe", SetVsaNrLimitSpecNrRelState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:NR:RELE:LIMIt", SetVsaNrLimitSpecNrRelLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:NR:ABS:STATe", SetVsaNrLimitSpecNrAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SPEC#:NR:ABS:PWR", SetVsaNrLimitSpecNrAbsPwr, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:SEM:TEST:TOL", SetVsaNrLimitSemTestTol, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:ACLR:TEST:TOL", SetVsaNrLimitAclrTestTol, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:PWR:STATe", SetVsaNrLimitPwrState, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:PWR:OFF", SetVsaNrLimitPwrOff, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONF:LIMIt:PWR:TEST:TOL", SetVsaNrLimitPwrTestTol, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_nr5g_result_cmd[] = {
    // Summary
    {"WT:NR:SENSe:FETCh:SUMMaryUL?", SCPI_3GPP_GetVsaRstSummaryNRUL, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCh:SUMMary?", SCPI_3GPP_GetVsaRstSummaryNR, 0, SCPI_QUERY_CMD},
    // Spectrum Flatness
    {"WT:NR:SENSe:FETCh:SPECtrum:FLATness:DATA?", SCPI_3GPP_GetVsaRstFlatnessFlatRes, 0, SCPI_QUERY_CMD}, // Flatness.FlatRes
    {"WT:NR:SENSe:FETCh:SPECtrum:FLATness:MASK:UP:DATA?", SCPI_3GPP_GetVsaRstFlatnessFlatMaskUp, 0, SCPI_QUERY_CMD}, // Flatness.FlatMaskUp
    {"WT:NR:SENSe:FETCh:SPECtrum:FLATness:MASK:DOWN:DATA?", SCPI_3GPP_GetVsaRstFlatnessFlatMaskDown, 0, SCPI_QUERY_CMD}, // Flatness.FlatMaskDown

    {"WT[:NR]:SENSe:FETCh:SLOT:INDEx?", SCPI_3GPP_GetVsaRstSlotIdx, 0, SCPI_QUERY_CMD},
    {"WT[:NR]:SENSe:FETCh:PBCH:SSB:NUMBer?", SCPI_3GPP_GetVsaRstSSBNum, 0, SCPI_QUERY_CMD},
    {"WT[:NR]:SENSe:FETCh:PBCH:SSB:INFO?", SCPI_3GPP_GetVsaRstPBCHSSBInfo, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_3gpp_nbiot_wavegen_cmd[] = {

    {"WT:NIOT:SOURce:CONFigure:WAVE:LINK", SCPI_NBLOT_SetLinkDirect, 0, SCPI_SEQUENTIAL},

    //filter
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:TYPE", SCPI_NBIOT_SetFilterType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:SAMPle:RATE", SCPI_NBIOT_SetFilterSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:MAXinum:ORDer", SCPI_NBIOT_SetFilterMaxOrder, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:PASS:BAND:FREQuency:FACTor", SCPI_NBIOT_SetFilterFpassFactor, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:STOP:BAND:FREQuency:FACTor", SCPI_NBIOT_SetFilterFstopFactor, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:PASS:BAND:RIPPle", SCPI_NBIOT_SetFilterPassRipple, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:STOP:BAND:ATTEnuation", SCPI_NBIOT_SetFilterStopAtten, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:ROLL:OFF:FACTor", SCPI_NBIOT_SetFilterRollOffFactor, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:CUT:OFF:FREQuency:SHIFt", SCPI_NBIOT_SetFilterCutOffFreqShift, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:FILTer:WINdow:LENgth:FACTor", SCPI_NBIOT_SetFilterWindowLenFactor, 0, SCPI_SEQUENTIAL},

    // UL Cell
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:OPERation:MODE", SCPI_NBLOT_UL_SetCellOperMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:BW", SCPI_NBLOT_UL_SetCellBW, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:RB:INDEx", SCPI_NBLOT_UL_SetCellRBIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:NCID", SCPI_NBLOT_UL_SetCellNBCellID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:GROUp:HOPPing", SCPI_NBLOT_UL_SetCellGroupHopping, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:THRee:TONE:CYCLic:SHIFt", SCPI_NBLOT_UL_SetCellToneCyclicShift3, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:SIX:TONE:CYCLic:SHIFt", SCPI_NBLOT_UL_SetCellToneCyclicShift6, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:BASE:SEQUence:MODE", SCPI_NBLOT_UL_SetCellBaseSequenceMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:THRee:TONE:BASE:SEQUence", SCPI_NBLOT_UL_SetCellToneBaseSequence3, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:SIX:TONE:BASE:SEQUence", SCPI_NBLOT_UL_SetCellToneBaseSequence6, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:TWELve:TONE:BASE:SEQUence", SCPI_NBLOT_UL_SetCellToneBaseSequence12, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:CELL:DELTa:SEQUence:SHIFt", SCPI_NBLOT_UL_SetCellDeltaSequenceShift, 0, SCPI_SEQUENTIAL},

    // UL UE
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:UE:ID", SCPI_NBLOT_UL_SetUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:UE:SCRambling", SCPI_NBLOT_UL_SetUEScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:UE:DATA:TYPE", SCPI_NBLOT_UL_SetUEDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:UE:INITialization", SCPI_NBLOT_UL_SetUEInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:UE:CHANnel:CODEing:STATe", SCPI_NBLOT_UL_SetUEChanCodeingState, 0, SCPI_SEQUENTIAL},

    // UL Schedule
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:CHANnel:TYPE", SCPI_NBLOT_UL_SetSchedChanType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:FORMat", SCPI_NBLOT_UL_SetSchedNPuschFormat, 0, SCPI_SEQUENTIAL},
    // UL Schedule NPUSch F1
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:SUB:CARRier:SPACing", SCPI_NBLOT_UL_SetSchedNPuschSubCSpace, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:STARt:SUB:FRAMe", SCPI_NBLOT_UL_SetSchedNPuschStartSubFrame, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:REPEtitions", SCPI_NBLOT_UL_SetSchedNPuschReptitions, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:NUMber:RESOurce:UNITs", SCPI_NBLOT_UL_SetSchedNPuschNumResUnits, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:SUB:CARRier:INDIcation:MODE", SCPI_NBLOT_UL_SetSchedNPuschSubCIndicationMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:SUB:CARRier:INDIcation", SCPI_NBLOT_UL_SetSchedNPuschSubCIndication, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:SUB:CARRier:NUMber", SCPI_NBLOT_UL_SetSchedNPuschSubCNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:SUB:CARRier:STARt", SCPI_NBLOT_UL_SetSchedNPuschSubCStart, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:MODUlate", SCPI_NBLOT_UL_SetSchedNPuschModulate, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:MCS:MODE", SCPI_NBLOT_UL_SetSchedNPuschMscMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:MCS", SCPI_NBLOT_UL_SetSchedNPuschMsc, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:TBSize:INDEx", SCPI_NBLOT_UL_SetSchedNPuschTBSizeIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:RV:INDEx", SCPI_NBLOT_UL_SetSchedNPuschRVIndex, 0, SCPI_SEQUENTIAL},
    // UL Schedule NPUSch F2
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:ACK:RESource:FIELd:MODE", SCPI_NBLOT_UL_SetSchedNPuschAckResFieldMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:ACK:RESource:FIELd", SCPI_NBLOT_UL_SetSchedNPuschAckResField, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:SUB:CARRier:INDEx", SCPI_NBLOT_UL_SetSchedNPuschSubCIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:UL:SCHEdule:NPUSch:HARQ:ACK:INFO", SCPI_NBLOT_UL_SetSchedNPuschHarqAckInfo, 0, SCPI_SEQUENTIAL},

    // DL info
    // DL Cell
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:BW", SCPI_NBLOT_DL_SetCellBW, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:PHYSical:CELL:ID", SCPI_NBLOT_DL_SetCellLtePhysicalCellID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:RA:RNTI", SCPI_NBLOT_DL_SetCellLteRARNTI, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:TX:ANTEnna:NUMber", SCPI_NBLOT_DL_SetCellLteTxAntenna, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:RES:FILL:STATe", SCPI_NBLOT_DL_SetCellLteResFillState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:MODUlation", SCPI_NBLOT_DL_SetCellLteModulation, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:DATA:TYPE", SCPI_NBLOT_DL_SetCellLteDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:LTE:INITialization", SCPI_NBLOT_DL_SetCellLteInitialization, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:NB:CELL:ID", SCPI_NBLOT_DL_SetCellNBCellID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:NB:ANTEnna:NUMber", SCPI_NBLOT_DL_SetCellNBAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:ANCHor:CARRier:OPERation:MODE", SCPI_NBLOT_DL_SetCellAnchorCarrierOperationMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:ANCHor:CARRier:RB:INDEx", SCPI_NBLOT_DL_SetCellAnchorCarrierRBIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:ANCHor:CARRier:TYPE:ONE:COMMon:SEARch:SPACe:MAX:REPEtition", SCPI_NBLOT_DL_SetCellAnchorCarrierType1Rmax, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:ANCHor:CARRier:TYPE:TWO:COMMon:SEARch:SPACe:MAX:REPEtition", SCPI_NBLOT_DL_SetCellAnchorCarrierType2Rmax, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:ANCHor:CARRier:TYPE:TWO:COMMon:SEARch:SPACe:STARt:SUB:FRAMe", SCPI_NBLOT_DL_SetCellAnchorCarrierType2G, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:ANCHor:CARRier:TYPE:TWO:COMMon:SEARch:OFFSet", SCPI_NBLOT_DL_SetCellAnchorCarrierType2Offset, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:NON:ANCHor:CARRier#:INDEx", SCPI_NBLOT_DL_SetCellNonAnchorCarrierIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:NON:ANCHor:CARRier#:STATe", SCPI_NBLOT_DL_SetCellNonAnchorCarrierState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:NON:ANCHor:CARRier#:OPERation:MODE", SCPI_NBLOT_DL_SetCellNonAnchorCarrierOperationMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:CELL:NON:ANCHor:CARRier#:RB:INDEx", SCPI_NBLOT_DL_SetCellNonAnchorCarrierRBIndex, 0, SCPI_SEQUENTIAL},

    // DL UE
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:ID", SCPI_NBLOT_DL_SetUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:CATEgory", SCPI_NBLOT_DL_SetUECategory, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:SPECific:SEARch:SPACe:MAX:REPEtition", SCPI_NBLOT_DL_SetUESpecificSearchSpaceRmax, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:SPECific:SEARch:SPACe:STARt:SUB:FRAMe", SCPI_NBLOT_DL_SetUESpecificSearchSpaceG, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:SPECific:SEARch:SPACe:OFFSet", SCPI_NBLOT_DL_SetUESpecificSearchSpaceOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:DATA:TYPE", SCPI_NBLOT_DL_SetUEDataType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:UE:INITialization", SCPI_NBLOT_DL_SetUEInitialization, 0, SCPI_SEQUENTIAL},

    // DL Ahr Schedule
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:USER", SCPI_NBLOT_DL_SetAnchorScheduleDciUser, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:FORMat", SCPI_NBLOT_DL_SetAnchorScheduleDciDciFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:SEARch:SPACe", SCPI_NBLOT_DL_SetAnchorScheduleDciSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:SUB:CARRier", SCPI_NBLOT_DL_SetAnchorScheduleDciN0Isc, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:RESOurce:UNIT", SCPI_NBLOT_DL_SetAnchorScheduleDciN0Iru, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:DELAy", SCPI_NBLOT_DL_SetAnchorScheduleDciN0Idelay, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:MCS", SCPI_NBLOT_DL_SetAnchorScheduleDciN0Imcs, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:REDUndancy:VERSion", SCPI_NBLOT_DL_SetAnchorScheduleDciN0RedunVer, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN0Irep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:NEW:DATA", SCPI_NBLOT_DL_SetAnchorScheduleDciN0NewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N0:SUB:FRAMe:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN0DciRepNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:ORDEr", SCPI_NBLOT_DL_SetAnchorScheduleDciN1OrderInd, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:NPRAch:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN1NprachRep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:NPRAch:SUB:CARRier", SCPI_NBLOT_DL_SetAnchorScheduleDciN1NprachSC, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:DELAy", SCPI_NBLOT_DL_SetAnchorScheduleDciN1Idelay, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:SUB:FRAMe", SCPI_NBLOT_DL_SetAnchorScheduleDciN1Isf, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:MCS", SCPI_NBLOT_DL_SetAnchorScheduleDciN1Imcs, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN1Irep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:NEW:DATA", SCPI_NBLOT_DL_SetAnchorScheduleDciN1NewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:HARQ:ACK:RES", SCPI_NBLOT_DL_SetAnchorScheduleDciN1HarqAckRes, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:SUB:FRAMe:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN1DciRepNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N1:DISTance:TYPE", SCPI_NBLOT_DL_SetAnchorScheduleDciN1DistanceType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:PAG:FLG", SCPI_NBLOT_DL_SetAnchorScheduleDciN2PagFlg, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:SYSTem:MODIfy:EDRX", SCPI_NBLOT_DL_SetAnchorScheduleDciN2SysInfoModifEDRX, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:SYSTem:MODIfy", SCPI_NBLOT_DL_SetAnchorScheduleDciN2SysInfoModif, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:SUB:FRAMe", SCPI_NBLOT_DL_SetAnchorScheduleDciN2Isf, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:MCS", SCPI_NBLOT_DL_SetAnchorScheduleDciN2Imcs, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN2Irep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:N2:SUB:FRAMe:REPEtition:NUM", SCPI_NBLOT_DL_SetAnchorScheduleDciN2DciRepNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:STARt:SUB:FRAMe", SCPI_NBLOT_DL_SetAnchorScheduleDciStartSubfrm, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:NPDCch:FORMat", SCPI_NBLOT_DL_SetAnchorScheduleDciNpdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:DCI:NCCE:INDEx", SCPI_NBLOT_DL_SetAnchorScheduleDciNcceIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:PRECoding", SCPI_NBLOT_DL_SetAnchorScheduleNpbchPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:SCRAmbling", SCPI_NBLOT_DL_SetAnchorScheduleNpbchScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:CHANnel:CODIng:STATe", SCPI_NBLOT_DL_SetAnchorScheduleNpbchChanCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:ENALe", SCPI_NBLOT_DL_SetAnchorScheduleNpbchUseMIB, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:SFN", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSFN, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:HYPEr:SFN", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoHyperSFN, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:SCHEdule:SIB1", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSchedInfoSIB1, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:SYSTem:VALUe:TAG", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSysInfoValueTag, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:ACCEss:ENABled", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoABEnabled, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:OPERation:MODE", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoOptModeInfo, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:NPBCh:MIB:SPARe:BIT", SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoSpareBit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:SIB1:PRECoding", SCPI_NBLOT_DL_SetAnchorScheduleSIB1Precoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:SIB1:SCRAmbling", SCPI_NBLOT_DL_SetAnchorScheduleSIB1Scrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:SIB1:CHANnel:CODIng:STATe", SCPI_NBLOT_DL_SetAnchorScheduleSIB1ChanCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:SIB1:TBS:INDEx", SCPI_NBLOT_DL_SetAnchorScheduleSIB1TBSIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdcch:STARt:SYMB", SCPI_NBLOT_DL_SetAnchorScheduleNpdcchStartSymb, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdcch:SCRAmbling", SCPI_NBLOT_DL_SetAnchorScheduleNpdcchScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdcch:PRECoding", SCPI_NBLOT_DL_SetAnchorScheduleNpdcchPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdsch:STARt:SYMB", SCPI_NBLOT_DL_SetAnchorScheduleNpdschStartSymb, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdsch:SCRAmbling", SCPI_NBLOT_DL_SetAnchorScheduleNpdschScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdsch:PRECoding", SCPI_NBLOT_DL_SetAnchorScheduleNpdschPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:ANCHor:SCHEdule:Npdsch:CHANnel:CODIng:STATe", SCPI_NBLOT_DL_SetAnchorScheduleNpdschChanCodingState, 0, SCPI_SEQUENTIAL},
    // DL Non Ahr Schedule
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:CONFig:TYPE", SCPI_NBLOT_DL_SetNonAhrScheduleConfigType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:USER", SCPI_NBLOT_DL_SetNonAhrScheduleDciUser, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:FORMat", SCPI_NBLOT_DL_SetNonAhrScheduleDciDciFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:SEARch:SPACe", SCPI_NBLOT_DL_SetNonAhrScheduleDciSearchSpace, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:SUB:CARRier", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Isc, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:RESOurce:UNIT", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Iru, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:DELAy", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Idelay, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:MCS", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Imcs, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:REDUndancy:VERSion", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0RedunVer, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0Irep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:NEW:DATA", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0NewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N0:SUB:FRAMe:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN0DciRepNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:ORDEr", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1OrderInd, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:NPRAch:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1NprachRep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:NPRAch:SUB:CARRier", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1NprachSC, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:DELAy", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Idelay, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:SUB:FRAMe", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Isf, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:MCS", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Imcs, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1Irep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:NEW:DATA", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1NewDataInd, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:HARQ:ACK:RES", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1HarqAckRes, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:SUB:FRAMe:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1DciRepNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N1:DISTance:TYPE", SCPI_NBLOT_DL_SetNonAhrScheduleDciN1DistanceType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:PAG:FLG", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2PagFlg, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:SYSTem:MODIfy:EDRX", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2SysInfoModifEDRX, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:SYSTem:MODIfy", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2SysInfoModif, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:SUB:FRAMe", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2Isf, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:MCS", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2Imcs, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2Irep, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:N2:SUB:FRAMe:REPEtition:NUM", SCPI_NBLOT_DL_SetNonAhrScheduleDciN2DciRepNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:STARt:SUB:FRAMe", SCPI_NBLOT_DL_SetNonAhrScheduleDciStartSubfrm, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:NPDCch:FORMat", SCPI_NBLOT_DL_SetNonAhrScheduleDciNpdcchFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:DCI:NCCE:INDEx", SCPI_NBLOT_DL_SetNonAhrScheduleDciNcceIdx, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdcch:STARt:SYMB", SCPI_NBLOT_DL_SetNonAhrScheduleNpdcchStartSymb, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdcch:SCRAmbling", SCPI_NBLOT_DL_SetNonAhrScheduleNpdcchScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdcch:PRECoding", SCPI_NBLOT_DL_SetNonAhrScheduleNpdcchPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdsch:STARt:SYMB", SCPI_NBLOT_DL_SetNonAhrScheduleNpdschStartSymb, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdsch:SCRAmbling", SCPI_NBLOT_DL_SetNonAhrScheduleNpdschScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdsch:PRECoding", SCPI_NBLOT_DL_SetNonAhrScheduleNpdschPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SOURce:CONFigure:WAVE:DL:NON:ANCHor#:SCHEdule:Npdsch:CHANnel:CODIng:STATe", SCPI_NBLOT_DL_SetNonAhrScheduleNpdschChanCodingState, 0, SCPI_SEQUENTIAL},

};

static const scpi_command_t scpi_wt_3gpp_nbiot_analyze_cmd[] = {
    //Measure
    {"WT:NIOT:SENSe:CONFigure:ANALy:MEASure:MODUlation:CONStellation:SHOW:PILOt", SCPI_NBIOT_SetAnalyzeMeasureModulationConstShowPilot, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:MEASure:MEASure:UNIT", SCPI_NBIOT_SetAlzMeasureUnit, 0, SCPI_SEQUENTIAL},
    //UL general
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:DUPLexing", SCPI_NBIOT_SetAnalyzeDuplexing, 0, SCPI_SEQUENTIAL},          // 加载ARB
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:OPERation:MODE", SCPI_NBIOT_SetAnalyzeOperationMode, 0, SCPI_SEQUENTIAL}, // 清空参考
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:BW", SCPI_NBIOT_SetAnalyzeChnnelBandWidth, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:RB:INDEx", SCPI_NBIOT_SetAnalyzeResourceBlockIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:CELL:ID", SCPI_NBIOT_SetAnalyzeCellID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:CHANnel:TYPE", SCPI_NBIOT_SetAnalyzeChannelType, 0, SCPI_SEQUENTIAL},

    //UL NPUSCH
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:FORMat", SCPI_NBIOT_SetAnalyzeNPuschFormat, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:SUB:CARRier:SPACing", SCPI_NBIOT_SetAnalyzeNPuschSubCarrierSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:REPEtitions", SCPI_NBIOT_SetAnalyzeNPuschRepetTimes, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:NUMber:RESOurce:UNITs", SCPI_NBIOT_SetAnalyzeNPuschResourceUnitsNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:SUB:CARRier:NUMber", SCPI_NBIOT_SetAnalyzeNPuschSubCarrierNumber, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:SUB:CARRier:STARt", SCPI_NBIOT_SetAnalyzeNPuschSubCarrierStart, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:CYCLic:SHIFt", SCPI_NBIOT_SetAnalyzeNPuschCyclicShift, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:GROUp:HOPPing", SCPI_NBIOT_SetAnalyzeNPuschGroupHopping, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:DELTa:SEQUence:SHIFt", SCPI_NBIOT_SetAnalyzeNPuschDeltaSquenceShift, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:MODUlate", SCPI_NBIOT_SetAnalyzeNPuschModulate, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:CHANnel:CODEing:STATe", SCPI_NBIOT_SetAnalyzeNPuschChannelCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:SCRambling", SCPI_NBIOT_SetAnalyzeNPuschScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:UE:ID", SCPI_NBIOT_SetAnalyzeNPuschUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:STARt:SUB:FRAMe", SCPI_NBIOT_SetAnalyzeNPuschStartSubFrame, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:TBSize:INDEx", SCPI_NBIOT_SetAnalyzeNPuschTransportBlockSizeIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:UL:NPUSch:STARt:RV:INDEx", SCPI_NBIOT_SetAnalyzeNPuschRVStartIndex, 0, SCPI_SEQUENTIAL},

    //DL general
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:DUPLexing", SCPI_NBIOT_SetAnalyzeDLDuplexing, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:OPERation:MODE", SCPI_NBIOT_SetAnalyzeDLOperationMode, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:CARRier:TYPE", SCPI_NBIOT_SetAnalyzeDLCarrierType, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:BW", SCPI_NBIOT_SetAnalyzeDLChnnelBandWidth, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:RB:INDEx", SCPI_NBIOT_SetAnalyzeDLResourceBlockIndex, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:CELL:ID", SCPI_NBIOT_SetAnalyzeDLCellID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:LTE:CELL:ID", SCPI_NBIOT_SetAnalyzeDLLTECellID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:LTE:ANTEnna:NUMber", SCPI_NBIOT_SetAnalyzeDL_LTEAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NB:ANTEnna:NUMber", SCPI_NBIOT_SetAnalyzeDL_NBAntennaNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:SIB1:SWITch", SCPI_NBIOT_SetAnalyzeDLSIB1Switch, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:SIB1:SCHEduling:INFO", SCPI_NBIOT_SetAnalyzeDLSIB1SchedulingInfo, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPSS:POWer", SCPI_NBIOT_SetAnalyzeDLNPSSPower, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NSSS:POWer", SCPI_NBIOT_SetAnalyzeDLNSSSPower, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:CHANnel:TYPE", SCPI_NBIOT_SetAnalyzeDLChannelType, 0, SCPI_SEQUENTIAL},

    //DL NPDSCH
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:SUB:FRAMe:NUMber", SCPI_NBIOT_SetAnalyzeDLNpdschSubFrameNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:REPEtitions:NUMber", SCPI_NBIOT_SetAnalyzeDLNpdschRepetNum, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:MCS", SCPI_NBIOT_SetAnalyzeDLNpdschMCS, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:STARt:SYMBol", SCPI_NBIOT_SetAnalyzeDLNpdscStartSymbol, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:STARt:SUB:FRAMe", SCPI_NBIOT_SetAnalyzeDLNpdscStartSubFrame, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:PRE:CODIng", SCPI_NBIOT_SetAnalyzeDLNpdscPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:CHANnel:CODIng:STATe", SCPI_NBIOT_SetAnalyzeDLNpdscChanCodingState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:SCRAmbling", SCPI_NBIOT_SetAnalyzeDLNpdscScrambling, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:UE:ID", SCPI_NBIOT_SetAnalyzeDLNpdscUEID, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:DL:NPDSch:POWer", SCPI_NBIOT_SetAnalyzeDLNpdscPower, 0, SCPI_SEQUENTIAL},

    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:MODLimit:MODE", SCPI_NBIOT_SetLimitModLimitMode, 0, SCPI_SEQUENTIAL},
    //Modulation
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:EVM:RMS:STATe", SCPI_NBIOT_SetLimitEvmRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:EVM:RMS:LIMIt", SCPI_NBIOT_SetLimitEvmRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:EVM:PEAK:STATe", SCPI_NBIOT_SetLimitEvmPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:EVM:PEAK:LIMIt", SCPI_NBIOT_SetLimitEvmPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:MERR:RMS:STATe", SCPI_NBIOT_SetLimitMerrRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:MERR:RMS:LIMIt", SCPI_NBIOT_SetLimitMerrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:MERR:PEAK:STATe", SCPI_NBIOT_SetLimitMerrPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:MERR:PEAK:LIMIt", SCPI_NBIOT_SetLimitMerrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:PHERr:RMS:STATe", SCPI_NBIOT_SetLimitPherrRmsState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:PHERr:RMS:LIMIt", SCPI_NBIOT_SetLimitPherrRmsLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:PHERr:PEAK:STATe", SCPI_NBIOT_SetLimitPherrPeakState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:PHERr:PEAK:LIMIt", SCPI_NBIOT_SetLimitPherrPeakLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:FREQ:ERRLow:STATe", SCPI_NBIOT_SetLimitFreqErrlowState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:FREQ:ERRLow:LIMIt", SCPI_NBIOT_SetLimitFreqErrlowLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:FREQ:ERRHigh:STATe", SCPI_NBIOT_SetLimitFreqErrhighState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:FREQ:ERRHigh:LIMIt", SCPI_NBIOT_SetLimitFreqErrhighLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IQ:OFFSet:STATe", SCPI_NBIOT_SetLimitIQOffsetState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IQ:OFFSet:PWR:LIMIt", SCPI_NBIOT_SetLimitIQOffsetPwrLimit, 0, SCPI_SEQUENTIAL},

    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IBE:STATe", SCPI_NBIOT_SetLimitIbeState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IBE:GENMin", SCPI_NBIOT_SetLimitIbeGenmin, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IBE:GENPwr", SCPI_NBIOT_SetLimitIbeGenpwr, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IBE:IQ:IMAGe", SCPI_NBIOT_SetLimitIbeIQImage, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:IBE:IQ:OFFSet:PWR:LIMIt", SCPI_NBIOT_SetLimitIbeIQOffsetPwrLimit, 0, SCPI_SEQUENTIAL},

    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:SPECt:LIMIt:MODE", SCPI_NBIOT_SetLimitSpectLimitMode, 0, SCPI_SEQUENTIAL},
    //Spectrum
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:OBW:LIMIt:STATe", SCPI_NBIOT_SetLimitOBWLimitState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:OBW:LIMIt:LIMIt", SCPI_NBIOT_SetLimitOBWLimitLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:SEMLimit#:STATe", SCPI_NBIOT_SetLimitSEMLimitState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:SEMLimit#:STARtfreq", SCPI_NBIOT_SetLimitSEMLimitStartFreq, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:SEMLimit#:STOPfreq", SCPI_NBIOT_SetLimitSEMLimitStopFreq, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:SEMLimit#:STARtpower", SCPI_NBIOT_SetLimitSEMLimitStartPower, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:SEMLimit#:STOPpower", SCPI_NBIOT_SetLimitSEMLimitStopPower, 0, SCPI_SEQUENTIAL},

    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:GSM:RELState", SCPI_NBIOT_SetLimitGSMRelState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:GSM:RELLimit", SCPI_NBIOT_SetLimitGSMRelLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:GSM:ABSState", SCPI_NBIOT_SetLimitGSMRAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:GSM:ABSPwr", SCPI_NBIOT_SetLimitGSMRAbsPwr, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:UTRA:RELState", SCPI_NBIOT_SetLimitUTRARelState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:UTRA:RELLimit", SCPI_NBIOT_SetLimitUTRARelLimit, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:UTRA:ABSState", SCPI_NBIOT_SetLimitUTRARAbsState, 0, SCPI_SEQUENTIAL},
    {"WT:NIOT:SENSe:CONFigure:ANALy:LIMItinfo:UTRA:ABSPwr", SCPI_NBIOT_SetLimitUTRARAbsPwr, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_3gpp_nbiot_result_cmd[] = {
    // Summary
    {"WT:NIOT:SENSe:FETCh:SUMMary?", SCPI_3GPP_GetVsaRstSummaryNIOT, 0, SCPI_QUERY_CMD},

    {"WT[:NIOT]:SENSe:FETCh:FORMat?", SCPI_3GPP_GetVsaRstFormat, 0, SCPI_QUERY_CMD}, // DeInfo.Format
    {"WT[:NIOT]:SENSe:FETCh:HARQ:ACK?", SCPI_3GPP_GetVsaRstHarqAckInfo, 0, SCPI_QUERY_CMD}, // DeInfo.HarqAckInfo
    {"WT[:NIOT]:SENSe:FETCh:MODUlation:SYMBols:EVM?", SCPI_3GPP_GetVsaRstModulationSymbolsEvm, 0, SCPI_QUERY_CMD}, // Evm.PointEvm
};

#define scpi_cmd_insert(name) scpi_vec.insert(std::end(scpi_vec), std::begin(name), std::end(name))

void SCPI_Insert_Extend_3GPP(vector<scpi_command_t> &scpi_vec)
{
    // 一些通用
    scpi_cmd_insert(scpi_wt_3gpp_comm_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_wavegen_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_result_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_average_cmd);

    // GSM
    scpi_cmd_insert(scpi_wt_3gpp_gsm_analyze_cmd);

    // GSM
    scpi_cmd_insert(scpi_wt_3gpp_gsm_wavegen_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_gsm_result_cmd);

    // WCDMA
    scpi_cmd_insert(scpi_wt_3gpp_wcdma_wavegen_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_wcdma_analyze_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_wcdma_result_cmd);

    // LTE
    scpi_cmd_insert(scpi_wt_3gpp_lte_wavegen_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_lte_analyze_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_lte_result_cmd);

    // NR
    scpi_cmd_insert(scpi_wt_3gpp_nr5g_wavegen_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_nr5g_analyze_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_nr5g_result_cmd);

    // NBIot
    scpi_cmd_insert(scpi_wt_3gpp_nbiot_wavegen_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_nbiot_analyze_cmd);
    scpi_cmd_insert(scpi_wt_3gpp_nbiot_result_cmd);
}
