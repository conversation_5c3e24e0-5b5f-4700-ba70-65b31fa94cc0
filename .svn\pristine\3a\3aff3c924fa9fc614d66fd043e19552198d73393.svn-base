//*****************************************************************************
//  File: device.cpp
//  describe: 设备信息
//  Data: 2016.9.1
//*****************************************************************************
#include "device.h"

#include <iostream>
#include <fstream>
#include <cstring>
#include <stdio.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <netinet/if_ether.h>
#include <sys/shm.h>		//共享内存
#include <fcntl.h>
#include <jsoncpp/json/json.h>

#include "wterror.h"
#include "basefun.h"
#include "conf.h"
#include "basefun.h"
#include "wtlog.h"
#include "wterror.h"
#include "version.h"
#include "devlib/devlib.h"
#include "devlib/ioctlcmd.h"
#include "license.h"
#include "alg/includeAll.h"
#include "crypto.h"
#include "shmkeyword.h"
#include "ft4222lib/upgradefpga.h"

using namespace std;
WTDeviceInfo &WTDeviceInfo::Instance(void)
{
    static WTDeviceInfo ObjDeviceInfo;
    return ObjDeviceInfo;
}

const char *WTDeviceInfo::GetDeviceType()
{
    return (char *)m_DevTypeStr;
}

int WTDeviceInfo::GetDevType()
{
    return m_DevType;
}

int WTDeviceInfo::SetDeviceIP(const char *IPBuf)
{
    if (!~inet_addr(IPBuf))
    {
        WTLog::Instance().LOGERR(WT_IP_ERROR, "Incorrect ip address!");
        return WT_IP_ERROR;
    }

    //配置仪器ip时，检测仪器如果配置有子网口信息，看看有没有ip冲突的。冲突时，删除子网口配置
    RereadSubNetworkInfo();

    STRNCPY_USER(m_DevInfo.IP, IPBuf);
    return WT_OK;
}

int WTDeviceInfo::SetSubmask(const char *MaskBuf)
{
    if (!~inet_addr(MaskBuf))
    {
        WTLog::Instance().LOGERR(WT_IP_ERROR, "Incorrect ip address!");
        return WT_IP_ERROR;
    }

    STRNCPY_USER(m_DevInfo.SubMask, MaskBuf);

    return WT_OK;
}

int WTDeviceInfo::SetGateWay(const char *GateWayBuf)
{
    if (!~inet_addr(GateWayBuf))
    {
        WTLog::Instance().LOGERR(WT_IP_ERROR, "Incorrect ip address!");
        return WT_IP_ERROR;
    }

    STRNCPY_USER(m_DevInfo.GateWay, GateWayBuf);

    return WT_OK;
}

int WTDeviceInfo::SetDeviceName(const char *NameBuf, int NameLen)
{
    if (NameLen > (signed)sizeof(m_DevInfo.Name))
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Setting Device NameBuf's length out of ranger!");
        return WT_ERROR;
    }
    STRNCPY_USER(m_DevInfo.Name, NameBuf);


    return WT_OK;
}

int WTDeviceInfo::SetDeviceMac(const char *MacBuf)
{
    STRNCPY_USER(m_DevInfo.Mac, MacBuf);

    return WT_OK;
}

const DeviceInfo& WTDeviceInfo::GetDeviceDetailedInfo()
{
    return m_DevInfo;
}

int WTDeviceInfo::GetDeviceSubNetInfo(char *pBuf, int &InfoSize)
{
    RereadSubNetworkInfo(); //Server和Manager属不同进程，进程间同步m_SysIP用文件
    //拷贝IP消息
    IPType *pIP = reinterpret_cast<IPType *>(pBuf);
    IPInfo *pSysIP = m_SysIP.SubIP;
    InfoSize = 0;
    for (int i = 0; i < sizeof(SystemIPInfo) / sizeof(IPInfo); i++, InfoSize += sizeof(IPType))
    {
        if (pSysIP[i].Vaild)
        {
            memcpy(&pIP[i], pSysIP[i].IP, sizeof(IPType));
        }
        else
        {
            memset(&pIP[i], 0, sizeof(IPType));
        }
    }
    return WT_OK;
}

bool WTDeviceInfo::GetLinkStatus(char* netName)
{
    char Cmd[256] = {0};
    string Result;

    sprintf(Cmd, "%s %s %s", "ethtool", netName, "|grep \"Link detected\"|awk '{print $3}'");

    Result = Basefun::shell_exec((const char*)Cmd);
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetLinkStatus Cmd:%s, Result:%s\n", Cmd, Result.c_str());


    if (Result.find("yes") == 0)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "%s:%s\n", netName, "Linked");
        return true;
    } 
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "%s:%s\n", netName, "Nolink");
        return false;
    }

}

int WTDeviceInfo::GetDeviceSubNetLink(bool *pBuf, int &InfoSize)
{
    InfoSize = 0;
    char netName[6] = {0};
    for (int i = 0; i < MAX_SUB_NET_NUM; i++, InfoSize += sizeof(bool))
    {
         sprintf(netName, "%s%d", "eth1", i);
         pBuf[i] = GetLinkStatus(netName);
    }
    return WT_OK;
}

int WTDeviceInfo::GetDeviceIpAddressType(bool *IsDchp, int &InfoSize)
{
    char Cmd[256] = {0};
    string Result;
    string EthInterface;

    DevConf::Instance().GetInterface((m_DevType == MODEL_TYPE_WT428 || m_DevType == MODEL_TYPE_WT428H) ? INTERFACE_DEV_TYPE_WT428 : INTERFACE_DEV_TYPE_WT448, EthInterface);
    sprintf(Cmd, "ip addr show %s |grep dynamic", EthInterface.c_str());

    Result = Basefun::shell_exec((const char*)Cmd);
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetDeviceIpAddressType Cmd:%s, Result:%s\n", Cmd, Result.c_str());

    if (Result.find("dynamic") != string::npos)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "The ip is dynamic\n");
        *IsDchp =  true;
    } 
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "The ip is static\n");
        *IsDchp =  false;
    }

    InfoSize = sizeof(bool);
    return WT_OK;
}


int WTDeviceInfo::SetDevSubNetInfo(const SystemIPInfo &SubIPInfo)
{
    memcpy(&m_SysIP, &SubIPInfo, sizeof(SystemIPInfo));
    return WT_OK;
}

int WTDeviceInfo::GetDevUpgradeState()
{
    return m_IsUpgrade;
}

int WTDeviceInfo::SetDevUpgradeState(int Flag)
{
    m_IsUpgrade = Flag;
    return WT_OK;
}

int WTDeviceInfo::CompareAddrSegment(const char *IP, const char *DutIP, const char *NetMask)
{
    struct in_addr AddrIP;
    struct in_addr AddrNetMake;
    struct in_addr AddrIPSeg;
    struct in_addr DutAddrIP;
    struct in_addr DutAddrIPSeg;
    char IPSeg[16] = {0}, DutIPSeg[16] = {0};
    memset(&AddrIP, 0, sizeof(struct in_addr));
    memset(&AddrNetMake, 0, sizeof(struct in_addr));
    memset(&AddrIPSeg, 0, sizeof(struct in_addr));
    memset(&DutAddrIP, 0, sizeof(struct in_addr));
    memset(&DutAddrIPSeg, 0, sizeof(struct in_addr));

    inet_aton(IP, &AddrIP);
    inet_aton(NetMask, &AddrNetMake);
    AddrIPSeg.s_addr = AddrIP.s_addr & AddrNetMake.s_addr;
    memcpy(IPSeg, inet_ntoa(AddrIPSeg), strlen(inet_ntoa(AddrIPSeg)));

    inet_aton(DutIP, &DutAddrIP);
    DutAddrIPSeg.s_addr = DutAddrIP.s_addr & AddrNetMake.s_addr;
    memcpy(DutIPSeg, inet_ntoa(DutAddrIPSeg), strlen(inet_ntoa(DutAddrIPSeg)));

#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "the segment of " << IP << "," << IPSeg << "," << DutIP << "," << DutIPSeg << endl;
#endif

    return strcmp(IPSeg, DutIPSeg);
}

int WTDeviceInfo::SetSubIP(int Index, char *SubIP)
{
    char Str[256] = {0};
    string File = WTConf::GetDir() + "/eth/network.sh";

    sprintf(Str, "%s %d %s", File.c_str(), Index, SubIP);
#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Sub Ip=" << Str << endl;
#endif
    Basefun::LinuxSystem(Str);

    return 0;
}

int WTDeviceInfo::SetDutIP(const SystemIPInfo &SubIPInfo)
{
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << PoutN(SubIPInfo.DutIP.IP)
         << PoutN(SubIPInfo.DutAsServerIP.IP)
         << PoutN(SubIPInfo.TftpServerIP.IP)
         << PoutN(SubIPInfo.TftpClientIP.IP)
         << PoutN(SubIPInfo.TftpServerPcIP.IP)
         << PoutN(SubIPInfo.TftpServerPcIP2.IP);

    if (!SubIPInfo.DutIP.Vaild)
    {
        return WT_ARG_ERROR;
    }

    string Cmd = WTConf::GetDir() + "/eth/nat.sh" + " " + SubIPInfo.DutIP.IP;
    if (SubIPInfo.DutAsServerIP.Vaild)
    {
        Cmd = Cmd + " " + SubIPInfo.DutAsServerIP.IP;
    }
    else if (SubIPInfo.TftpServerIP.Vaild && SubIPInfo.TftpClientIP.Vaild && SubIPInfo.TftpServerPcIP.Vaild)
    {
        // server和client ip不同表示dut为client， pc为server
        Cmd = Cmd + " " + SubIPInfo.TftpServerIP.IP + " " + SubIPInfo.TftpClientIP.IP + " " + SubIPInfo.TftpServerPcIP.IP;
        if (SubIPInfo.TftpServerPcIP2.Vaild && SubIPInfo.TftpServerPcIP2.Compare(SubIPInfo.TftpServerPcIP))
        {
            Cmd = Cmd + " " + SubIPInfo.TftpServerPcIP2.IP;
        }
    }

#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set DUT Ip=" << Cmd << endl;
#endif
    int Ret = system(Cmd.c_str());
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetDutIP Ret = %d\n", Ret);
    return WT_OK;
}

void WTDeviceInfo::SaveSubNetworkInfo()
{
    string SubNetFile = WTConf::GetDir() + "/eth/subnet.conf";
    ofstream OutFile(SubNetFile, fstream::out | fstream::binary | fstream::trunc); //| ios::noreplace
    OutFile.write(reinterpret_cast<char *>( &m_SysIP), sizeof(m_SysIP) );
    OutFile.close();
}

int WTDeviceInfo::RereadSubNetworkInfo()
{
    int Ret = WT_OK;
    string SubNetFile = WTConf::GetDir() + "/eth/subnet.conf";
    ifstream InFile(SubNetFile, fstream::in | fstream::binary);
    if (InFile)
    {
        InFile.read(reinterpret_cast<char *>(&m_SysIP), sizeof(m_SysIP));
        //仪器启动时检测是否有ip冲突，设备的ip和子网口的配置冲突，则不配置子网口,并删除子网口配置
        if (!InFile || CheckSystemIPInfo(m_SysIP) != WT_OK)
        {
            Ret = WT_SAME_SEGMENT;
            WTLog::Instance().LOGERR(Ret, "Delete the subnet setting when start,");
            remove(SubNetFile.c_str());
        }
        InFile.close();
    }
    else
    {
        Ret = WT_OPEN_FILE_FAILED;
        wtlog::error(SOURCE_LOCATION, Ret, "Cannot open file subnet.conf!");
    }

    if (Ret != WT_OK)
    {
        //将所有IP设定为无效
        IPInfo *pSysIP = m_SysIP.SubIP;
        for (int i = 0; i < sizeof(SystemIPInfo) / sizeof(IPInfo); i++)
        {
            pSysIP[i].Vaild = 0;
        }
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "RereadSubNetworkInfo =%d\n", Ret);
    return Ret;
}

int WTDeviceInfo::CheckSystemIPInfo(const SystemIPInfo &IPInfo)
{
    int Ret = WT_OK;

    do
    {
        //判断DUT IP是否有效
        if (!IPInfo.DutIP.Vaild)
        {
            Ret = WT_SUBIP_INSUFFICIENT; //返回错误信息给client
            WTLog::Instance().LOGERR(Ret, "Too Less subIpInfo, please set dut IP");
            break;
        }

        //仪器ip和DUT的ip不能同网段
        if (!CompareAddrSegment(m_DevInfo.IP, IPInfo.DutIP.IP, m_DevInfo.SubMask))
        {
            Ret = WT_SAME_SEGMENT;
            WTLog::Instance().LOGERR(Ret, "device ip and dut_ip or dut_ip in same segment,when power on");
            break;
        }

        // PC As Server时, 实际的Server和Client不能相同
        if (!IPInfo.DutAsServerIP.Vaild)
        {
            if (IPInfo.TftpServerIP.Vaild && IPInfo.TftpServerIP.Compare(IPInfo.TftpClientIP) == 0)
            {
                Ret = WT_SAME_SEGMENT;
                WTLog::Instance().LOGERR(Ret, "pc as server, server ip and client is same");
                break;
            }
        }

        for (int i = 0; i < MAX_SUB_NET_NUM; i++)
        {
            //判断8个子网口IP是否有效
            if (!IPInfo.SubIP[i].Vaild)
            {
                Ret = WT_SUBIP_INSUFFICIENT; //返回错误信息给client
                WTLog::Instance().LOGERR(Ret, "Too Less subIpInfo,please set eth0-eth7 IP");
                break;
            }

            //子网口IP不允许重复
            for (int j = i + 1; j < MAX_SUB_NET_NUM; j++)
            {
                if (!IPInfo.SubIP[i].Compare(IPInfo.SubIP[j]))
                {
                    Ret = WT_SAME_SEGMENT;
                    WTLog::Instance().LOGERR(Ret, "sub_ip is duplicated");
                    break;
                }
            }

            //用（子虚拟ip,or Dut ip）与（设备ip）比较判断是否在同一网段，是则返回错误
            if (!CompareAddrSegment(m_DevInfo.IP, IPInfo.SubIP[i].IP, m_DevInfo.SubMask))
            {
                Ret = WT_SAME_SEGMENT;
                WTLog::Instance().LOGERR(Ret, "device ip and sub_ip in same segment");
                break;
            }

            //比较（子虚拟ip）与（DUT ip）是否在同一个网段,是则返回错误
            if (!CompareAddrSegment(IPInfo.DutIP.IP, IPInfo.SubIP[i].IP, m_DevInfo.SubMask))
            {
                Ret = WT_SAME_SEGMENT;
                WTLog::Instance().LOGERR(Ret, "sub_ip and dut ip in same segment");
                break;
            }
        }
    } while (0);
    return Ret;
}

int WTDeviceInfo::SetSubNetInfo(IPType *pIP, int IPNumber)
{
    int Ret = WT_OK;
    int IsIPChange = false;

    SystemIPInfo IPInfoTemp;    //已经由构造函数进行初始化
    IPInfo *pIPInfoTemp = IPInfoTemp.SubIP;
    
    IPInfo *pSysIP = m_SysIP.SubIP;
    for (int i = 0; i < IPNumber && i < sizeof(SystemIPInfo) / sizeof(IPInfo); i++)
    {
        //判断IP是否合法
        if (~inet_addr(pIP[i]))
        {
            pIPInfoTemp[i].Vaild = 1;
            STRNCPY_USER(pIPInfoTemp[i].IP, pIP[i]);
        }

        WTLog::Instance().WriteLog(LOG_DEBUG, "[%d] pIP:%s, pIPInfoTemp Vaild:%d,IP:%s; pSysIP Vaild:%d,IP:%s\n",
               i, pIP[i], pIPInfoTemp[i].Vaild, pIPInfoTemp[i].IP, pSysIP[i].Vaild, pSysIP[i].IP);

        //比较是否有更新
        if (pIPInfoTemp[i].Compare(pSysIP[i]))
        {
            IsIPChange = true;
        }
    }

    Ret = CheckSystemIPInfo(IPInfoTemp);
    RetAssert(Ret, "CheckSystemIPInfo error");

    for (int i = 0; i < MAX_SUB_NET_NUM; i++)
    {
        //固定重配,由脚本处理重配判断
        SetSubIP(i + 1, IPInfoTemp.SubIP[i].IP);
    }
    Basefun::LinuxSystem("netplan apply");

    WTLog::Instance().WriteLog(LOG_DEBUG, "IsIPChange = %d\n", IsIPChange);
    if (IsIPChange)
    {
        SetDutIP(IPInfoTemp);
        SetDevSubNetInfo(IPInfoTemp);
        SaveSubNetworkInfo(); // save sub net setting
    }
    return WT_OK;
}

int WTDeviceInfo::GetMacInfo(IPType pIP, MacType MacAddr, DevNameType DevName)
{
    //判断IP是否合法
    if (~inet_addr(pIP))
    {
        int Sock = socket(AF_INET, SOCK_DGRAM | SOCK_CLOEXEC, 0);
        struct arpreq ArpReq;
        struct sockaddr_in *Sin;
        memset(&ArpReq, 0, sizeof(ArpReq));
        Sin = (struct sockaddr_in*)&(ArpReq.arp_pa);
        Sin->sin_family = AF_INET;
        inet_aton(pIP, &Sin->sin_addr);
        memcpy(ArpReq.arp_dev, DevName, sizeof(DevNameType));
        if (ioctl(Sock, SIOCGARP, &ArpReq) < 0)
        {
            close(Sock);
            return WT_IOCTL_FAILED;
        }
        else
        {
            if(ArpReq.arp_flags & ATF_COM)
            {
                unsigned char *mac = (unsigned char *)ArpReq.arp_ha.sa_data;
                        WTLog::Instance().WriteLog(LOG_DEBUG, "MAC: %02x:%02x:%02x:%02x:%02x:%02x\n",
                                mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
                sprintf(MacAddr, "%02x:%02x:%02x:%02x:%02x:%02x",mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
            }
        }
    }
    else
    {
        return WT_IP_ERROR;
    }
    return WT_OK;
}

#define WT_SUB_NET_INIT_FILEPATH "/tmp/wt_subnet_inited"
void WTDeviceInfo::SetSubNetworkInfoWhenRestart()
{
    int Ret = RereadSubNetworkInfo();

    // 判断内存临时文件是否存在，避免重复初始化子网口
    if (access(WT_SUB_NET_INIT_FILEPATH, F_OK) != 0)
    {
        // 生成内存临时文件
        int fd = open(WT_SUB_NET_INIT_FILEPATH, O_CREAT, 0644);
        close(fd);

        if (Ret == WT_OK)
        {
        for (int i = 0; i < MAX_SUB_NET_NUM; i++)
        {
            SetSubIP(i + 1, m_SysIP.SubIP[i].IP);
        }
        SetDutIP(m_SysIP);
        Basefun::LinuxSystem("netplan apply");
    }
}

}

int WTDeviceInfo::ReCheckNetInfo(void)
{
    if(m_IsIpAssigned)
    {
        return WT_OK;
    }
    else
    {
        return CheckNetInfo();
    }
}

int WTDeviceInfo::CheckNetInfo(int second)
{
    int Ret = WT_OK;
    char IP[16] = {0};
    char MAC[40] = {0};
    char NetMask[20] = {0};
    char Braodcast[20] = {0};
    char GateWay[20] = {0};

    //m_IsIpAssigned 初值为true是为了初始化网口信息的时候等待10s。
    for(int count = 0; count <=second;)
    {
        //读取仪器实际的IP和MAC
        GetDeviceRealNetInfo(IP, MAC, NetMask, Braodcast, GateWay);

#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_IP=" << m_DevInfo.IP << " m_mac=" << m_DevInfo.Mac << " real IP=" << IP << " realMac=" << MAC << endl;
#endif
        //比较实际获取的ip和mac，与本地base.conf文件的配置信息是否一致
        if ((strcmp(m_DevInfo.IP, IP) == 0) && (strcmp(m_DevInfo.Mac, MAC) == 0))
        {
            m_IsIpAssigned = true;
            return Ret;
        }
        else if (IP[0] == 0)
        {
            m_IsIpAssigned = false;
            if(count < second)
            {
                sleep(1.0);
                count++;
                continue;
            }
            else
            {
                return Ret;
            }
        }
        else
        {
            m_IsIpAssigned = true;
            break;
        }
    }

    //比较配置文件IP，mac内容与实际获取的不一样，则修改配置文件对应内容，把修改的内容写入base.conf文件
    SetDeviceIP(IP);
    if(MAC[0] != 0)
    {
        SetDeviceMac(MAC);
    }
    SetSubmask(NetMask);
    BaseConf::Instance().SetNetCfg(IP, GateWay, MAC, NetMask);
    return Ret;
}

int WTDeviceInfo::RestoreDeviceSetting(void)
{
    //默认IP，子码掩码，网关，设备名称,子仪器配置划分，默认Devnum=1
    string IPBuf = "**************";
    string MaskBuf = "*************";
    string GateWayBuf = "************";
    string NameBuf = "ITESTWT3xxTester";
    //int DevNum = 1;

    //TODO 补全需要恢复出厂设置的参数等
    SetDeviceIP(IPBuf.c_str());
    SetSubmask(MaskBuf.c_str());
    SetGateWay(GateWayBuf.c_str());
    SetDeviceName(NameBuf.c_str(), NameBuf.length());
    SetDeviceInfo(true);

    //恢复出厂删除线衰文件
    string PathLossFileName = WTConf::GetDir()+"/pathloss/PathLossFile";
    remove(PathLossFileName.c_str());

    //子仪器划分的信息
    vector<WTConf::DevCfg> DevCfgVector;
    WTConf::DevCfg DevAssign;
    DevAssign.PortMask = 0xff;
    DevAssign.VsaMask = 0xf;
    DevAssign.VsgMask = 0xf;
    DevCfgVector.push_back(DevAssign);
    BaseConf::Instance().SetDevCfg(DevCfgVector);
    return WT_OK;
}


void WTDeviceInfo::DeviceInfoInit(void)
{
    memset(&m_DevInfo, 0, sizeof(DeviceInfo));
    GetBoardVersion();
    GetDeviceInfo();
    CheckNetInfo(10);
}

WTDeviceInfo::WTDeviceInfo()
{
    int shmid = shmget(DEVICE_KEY_SHARE_MEM, sizeof(WTDeviceInfo), IPC_CREAT | 0666);
    if (shmid == -1)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "shmget failed");
        DeviceInfoInit();
        return;
    }
    u8 *shm_buf = (u8 *)shmat(shmid, NULL, SHM_RND);
    if (shm_buf == (void *)-1)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "shmat failed");
        DeviceInfoInit();
        return;
    }

    //判断内存临时文件是否存在，若不存在，则调用GetBoardVersion()获取硬件信息，否则从共享内存拷贝m_DevInfo。
    //原因：server启动加快后，三个进程在更短间隔内分别去读取SN码，小概率出现读到的SN码不准确。
    if (access(std::string(WT_DEVICE_INIT_FILEPATH).c_str(), F_OK) != 0)
    {
        //重新从硬件获取SN等硬件信息，生成内存临时文件
        int fd = open(std::string(WT_DEVICE_INIT_FILEPATH).c_str(), O_CREAT, 0644);
        close(fd);
        DeviceInfoInit();
        memcpy(shm_buf, &m_DevInfo, sizeof(DeviceInfo));
#if DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "WTDeviceInfo init and save!\n");
#endif
    }
    else
    {   
        //从共享内存拷贝m_DevInfo，避免多次读取SN码。
        memcpy(&m_DevInfo, shm_buf, sizeof(DeviceInfo));
        GetDeviceInfo();
        CheckNetInfo();
#if DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "Copy WTDeviceInfo to new process\n");
#endif
    }
    shmdt(shm_buf);
    shm_buf = nullptr;
    SetSubNetworkInfoWhenRestart(); //read sub net setting from config n make it works
}

int WTDeviceInfo::GetDeviceRealNetInfo(char *IP, char *MAC, char *NetMask, char *Broadcast, char *GateWay)
{
    int Sock;
    struct sockaddr_in Sin;
    struct ifreq ifr;

    int Ret = WT_OK;
    string Interface;

    Ret = DevConf::Instance().GetInterface((m_DevType != MODEL_TYPE_WT428)
                                               ? INTERFACE_DEV_TYPE_WT448
                                               : INTERFACE_DEV_TYPE_WT428,
                                           Interface);

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get network interface failed");
        return Ret;
    }

    Sock = socket(AF_INET, SOCK_DGRAM | SOCK_CLOEXEC, 0);
    if (Sock < 0)
    {
        WTLog::Instance().LOGERR(WT_CREATE_SOCKET_FAILED, "Create socket error");
        return WT_CREATE_SOCKET_FAILED;
    }
    //strncpy(ifr.ifr_name, EthName, IFNAMSIZ);
    STRNCPY_USER(ifr.ifr_name, Interface.c_str());
    /* ip */
    if (ioctl(Sock, SIOCGIFADDR, &ifr) < 0)
    {
        close(Sock);
        return WT_IOCTL_FAILED;
    }
    memcpy(&Sin, &ifr.ifr_addr, sizeof(Sin));
    sprintf(IP, "%s", inet_ntoa(Sin.sin_addr));
    /* mac */
    if(ioctl(Sock, SIOCGIFHWADDR, &ifr) < 0)
    {
        close(Sock);
        return WT_IOCTL_FAILED;
    }
    sprintf(MAC, "%02x:%02x:%02x:%02x:%02x:%02x",
            (unsigned char)ifr.ifr_hwaddr.sa_data[0],
            (unsigned char)ifr.ifr_hwaddr.sa_data[1],
            (unsigned char)ifr.ifr_hwaddr.sa_data[2],
            (unsigned char)ifr.ifr_hwaddr.sa_data[3],
            (unsigned char)ifr.ifr_hwaddr.sa_data[4],
            (unsigned char)ifr.ifr_hwaddr.sa_data[5]);
    /* 获取子网掩码 */
    if (ioctl(Sock, SIOCGIFNETMASK, &ifr) < 0)
    {
        close(Sock);
        return WT_IOCTL_FAILED;
    }
    memcpy(&Sin, &ifr.ifr_addr, sizeof(Sin));
    sprintf(NetMask, "%s", inet_ntoa(Sin.sin_addr));
    /* 广播地址 */
    if(ioctl(Sock, SIOCGIFBRDADDR, &ifr) < 0)
    {
        close(Sock);
        return WT_IOCTL_FAILED;
    }
    memcpy(&Sin, &ifr.ifr_addr, sizeof(Sin));
    sprintf(Broadcast, "%s", inet_ntoa(Sin.sin_addr));

    /*gateway*/
    (void)GateWay;
    close(Sock);
    return WT_OK;
}

int WTDeviceInfo::GetDeviceInfo(void)
{
    string Str = "";

    BaseConf::Instance().GetItemVal("DevName", Str);
    STRNCPY_USER(m_DevInfo.Name, Str.c_str());

    BaseConf::Instance().GetItemVal("IP", Str);
    STRNCPY_USER(m_DevInfo.IP, Str.c_str());

    BaseConf::Instance().GetItemVal("Mask", Str);
    STRNCPY_USER(m_DevInfo.SubMask, Str.c_str());

    BaseConf::Instance().GetItemVal("Gateway", Str);
    STRNCPY_USER(m_DevInfo.GateWay, Str.c_str());

    BaseConf::Instance().GetItemVal("Mac", Str);
    STRNCPY_USER(m_DevInfo.Mac, Str.c_str());

    int Version = 0;
    DevLib::Instance().GetHardwareVersion(0, DEV_TYPE_BACK, Version);

    BaseConf::Instance().GetItemVal("Model", Str);
    STRNCPY_USER(m_DevTypeStr, Str.c_str());
    Basefun::string_to_upper(m_DevTypeStr);

    string TypeStr(m_DevTypeStr);
    m_DevType = MODEL_TYPE_WT448;
    if (TypeStr.compare("WT448") == 0)
    {
        m_DevType = MODEL_TYPE_WT448;
    }
    else if (TypeStr.compare("WT428") == 0)
    {
        m_DevType = MODEL_TYPE_WT428;
    }
    else if (TypeStr.compare("WT328CE") == 0)
    {
        m_DevType = MODEL_TYPE_WT328CE;
    }
    else if (TypeStr.compare("WT428C") == 0)
    {
        m_DevType = MODEL_TYPE_WT428C;
    }
    else if (TypeStr.compare("WT428H") == 0)
    {
        m_DevType = MODEL_TYPE_WT428H;
    }
    STRNCPY_USER(m_DevInfo.FwVersion, WTGetFwVersion());

    return WT_OK;
}

int WTDeviceInfo::SetDeviceInfo(bool SaveandSetFlag)
{
    std::string EthInterface;
    DevConf::Instance().GetInterface((m_DevType == MODEL_TYPE_WT428 || m_DevType == MODEL_TYPE_WT428H)
                                         ? INTERFACE_DEV_TYPE_WT428
                                         : INTERFACE_DEV_TYPE_WT448,
                                     EthInterface);
    int IsDHCP = false;
    if (!strncmp(m_DevInfo.IP, "0.0.0.0", strlen("0.0.0.0")))
    {
        IsDHCP = true;
    }
    else
    {
        BaseConf::Instance().SetNetCfg(m_DevInfo.IP, m_DevInfo.GateWay, m_DevInfo.Mac, m_DevInfo.SubMask, m_DevInfo.Name);
    }

    //2、编辑网络配置文件，修改系统配置/etc/netplan/00-installer-config.yaml的文件内容
    if (true == SaveandSetFlag)
    {
        stringstream NewStrs;
        string ConfFile = "/etc/netplan/00-installer-config.yaml";
        NewStrs << "# This is the network config written by 'subiquity'\n";
        NewStrs << "network:\n";
        NewStrs << "    ethernets:\n";
        if (IsDHCP)
        {
            NewStrs << "        " << EthInterface << ":\n";
            NewStrs << "            dhcp4: yes\n";
            NewStrs << "            dhcp6: no\n";
            if (EthInterface.find("eth0") == std::string::npos)
            {
                NewStrs << "        eth0:\n";
                NewStrs << "            dhcp4: yes\n";
                NewStrs << "            dhcp6: no\n";
            }
        }
        else
        {
            int SubMaskCount = 0;
            int Mask[4] = {0};
            sscanf(m_DevInfo.SubMask, "%d.%d.%d.%d", Mask, Mask + 1, Mask + 2, Mask + 3);
            int MaskInt = (Mask[3] << 24) + (Mask[2] << 16) + (Mask[1] << 8) + Mask[0];
            for (int i = 0; i < 32; i++)
            {
                if (MaskInt & 0x1)
                {
                    SubMaskCount++;
                }
                MaskInt = MaskInt >> 1;
            }

            NewStrs << "        " << EthInterface << ":\n";
            NewStrs << "            dhcp4: no\n";
            NewStrs << "            dhcp6: no\n";
            NewStrs << "            addresses: [" << m_DevInfo.IP << "/" << SubMaskCount << "]" << endl;
            NewStrs << "            gateway4: " << m_DevInfo.GateWay << endl;
            if (EthInterface.find("eth0") == std::string::npos)
            {
                NewStrs << "        eth0:\n";
                NewStrs << "            dhcp4: no\n";
                NewStrs << "            dhcp6: no\n";
                NewStrs << "            addresses: [" << m_DevInfo.IP << "/" << SubMaskCount << "]" << endl;
                NewStrs << "            gateway4: " << m_DevInfo.GateWay << endl;
            }
        }
        NewStrs << "\n";
        NewStrs << "    version: 2\n";
        NewStrs << "\n";
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << NewStrs.str() << endl;
        ofstream OutFile(ConfFile, fstream::out | fstream::trunc);
        OutFile << NewStrs.str();
        OutFile.close();
    }
    return WT_OK;
}

int WTDeviceInfo::DeviceSnVerify(const char *SN, const char *Code)
{
#if DEBUG
//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DeviceSn=" << m_DevInfo.SN << "  LicenseSn=" << SN << std::endl;
#endif
    (void)Code;
    // (void)SN;
    // return WT_OK;
    if (strlen(m_DevInfo.SN) != strlen(SN))
    {
        return  WT_ENCRYPT_ILLEGAL;
    }
    else
    {
        return strcmp(m_DevInfo.SN, SN) ? WT_ENCRYPT_ILLEGAL : 0;
    }
}

int WTDeviceInfo::SnSelfTest(char* SN)
{
    char *pTest = NULL;
    pTest = (SN != NULL) ? SN : m_DevInfo.SN;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Device Sn Self Test Sn =" << pTest << std::endl;
    if (strncmp(pTest, "WT", strlen("WT")) == 0)
    {
        char InnerSN[32]; //内部SN码
        char DevType[32]; //设备类型
        int pos = std::string(pTest).find_first_of('-', 0);
        if (pos != std::string::npos)
        {
            strncpy(DevType, pTest, pos);
            strcpy(InnerSN, pTest + pos + 1);
            if (strlen(DevType) > 0 && strlen(InnerSN))
            {
                for (int i = 0; i < strlen(InnerSN); i++)
                {
                    if (InnerSN[i] < '0' || InnerSN[i] > '9')
                    {

                        WTLog::Instance().LOGOPERATE("Sn Self Test Failed");
                        return -1;
                    }
                }
                WTLog::Instance().LOGOPERATE("Sn Self Test Success");
                return WT_OK;
            }
        }
    }
    WTLog::Instance().LOGOPERATE("Sn Self Test Failed");
    return -1;
}

int WTDeviceInfo::DeviceRegetSn(void)
{
    int Ret = WT_OK;
    char SN[CM_ZONE_SIZE];
    //获取背板加密芯片SN码
    Ret = CryptoLib::Instance().GetCryptoMemSN(0, DEV_TYPE_BACK, SN);
    RetWarnning(Ret, "GetCryptoMemSN failed");
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Device Reget Sn =" << SN << std::endl;
    strcpy(m_DevInfo.SN, SN);

    if (Ret == WT_OK)
    {
        int shmid = shmget(DEVICE_KEY_SHARE_MEM, sizeof(WTDeviceInfo), IPC_CREAT | 0666);
        if (shmid == -1)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "shmget failed");
            return -1;
        }
        u8 *shm_buf = (u8 *)shmat(shmid, NULL, SHM_RND);
        if (shm_buf == (void *)-1)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "shmat failed");
            return -1;
        }
        memcpy(shm_buf, &m_DevInfo, sizeof(DeviceInfo));
        WTLog::Instance().LOGOPERATE("WTDeviceInfo resave!" );
        shmdt(shm_buf);
        shm_buf = nullptr;
    }
    return Ret;
}

extern "C" char *WT_Algorithm_DllVersion(void);
extern const char* wt_calibration_version();
int WTDeviceInfo::GetBoardVersion(void)
{
    int Ret = WT_OK;
    m_CurDir = WTConf::GetDir(); //获取当前目录

    //TODO 从配置文件读取硬件详细的信息
    WTLog::Instance().WriteLog(LOG_DEBUG, "get Build Date=%s\n", WTGetBuildDate());
    WTLog::Instance().WriteLog(LOG_DEBUG, "get Firmware version=%s\n", WTGetFwVersion());
    const char *CalibrationVersion = wt_calibration_version();
    WTLog::Instance().WriteLog(LOG_DEBUG, "get calibration version=%s\n", CalibrationVersion);
    char *AlgVersion = WT_Algorithm_DllVersion();   // 获取vsa算法版本接口
    char *AlgVsgVersion = WT_Algorithm_DllVersion_Vsg();   // 获取vsg算法版本接口
    memcpy(m_DevInfo.ALGVersion, AlgVersion, strlen(AlgVersion));
    WTLog::Instance().WriteLog(LOG_DEBUG, "get alg vsa version=%s\n", m_DevInfo.ALGVersion);
    WTLog::Instance().WriteLog(LOG_DEBUG, "get alg vsg version=%s\n", AlgVsgVersion);

    std::vector<BusinessBoardUnitInfo> AllBusiBoardInfo;
    BackPlaneUnitInfo BPInfo;
    memset(&BPInfo, 0, sizeof(BackPlaneUnitInfo));

    //背板
    if((Ret = DevLib::Instance().GetBackPlaneInfo(BPInfo)) == WT_OK)
    {
        memcpy(m_DevInfo.SN, BPInfo.SN, sizeof(m_DevInfo.SN));
        memcpy(m_DevInfo.BPInfo.BPHWVersion, BPInfo.BPHWVersion, sizeof(m_DevInfo.BPInfo.BPHWVersion));
        memcpy(m_DevInfo.BPInfo.FPGADate, BPInfo.FPGADate, sizeof(m_DevInfo.BPInfo.FPGADate));
        memcpy(m_DevInfo.BPInfo.FPGATime, BPInfo.FPGATime, sizeof(m_DevInfo.BPInfo.FPGATime));
        memcpy(m_DevInfo.BPInfo.FPGAVersion, BPInfo.FPGAVersion, sizeof(m_DevInfo.BPInfo.FPGAVersion));
        memcpy(m_DevInfo.BPInfo.RemarkInfo, BPInfo.RemarkInfo, sizeof(m_DevInfo.BPInfo.RemarkInfo));
        memcpy(m_DevInfo.BPInfo.SwitchHWVersion, BPInfo.SwitchHWVersion, sizeof(m_DevInfo.BPInfo.SwitchHWVersion));
    }
    else if (Ret == WT_UNITBOARD_NOT_EXIST)
    {
        string Str = "";
        BaseConf::Instance().GetItemVal("SN", Str);
        STRNCPY_USER(m_DevInfo.SN, Str.c_str());
    }
    //单元板
    if((Ret = DevLib::Instance().GetAllBusiBoardInfo(AllBusiBoardInfo)) == WT_OK)
    {
        m_DevInfo.BusiBoardCount = AllBusiBoardInfo.size();
        for(int i = 0; i < (signed)AllBusiBoardInfo.size(); i++)
        {
            memcpy(m_DevInfo.BusiBoardInfo[i].Type, AllBusiBoardInfo[i].Type, sizeof(m_DevInfo.BusiBoardInfo[i].Type));
            memcpy(m_DevInfo.BusiBoardInfo[i].SN, AllBusiBoardInfo[i].SN, sizeof(m_DevInfo.BusiBoardInfo[i].SN));
            memcpy(m_DevInfo.BusiBoardInfo[i].BBHWVersion, AllBusiBoardInfo[i].HWVersion, sizeof(m_DevInfo.BusiBoardInfo[i].BBHWVersion));
            memcpy(m_DevInfo.BusiBoardInfo[i].FPGAVersion, AllBusiBoardInfo[i].FPGAVersion, sizeof(m_DevInfo.BusiBoardInfo[i].FPGAVersion));
            memcpy(m_DevInfo.BusiBoardInfo[i].FPGADate, AllBusiBoardInfo[i].FPGADate, sizeof(m_DevInfo.BusiBoardInfo[i].FPGADate));
            memcpy(m_DevInfo.BusiBoardInfo[i].FPGATime, AllBusiBoardInfo[i].FPGATime, sizeof(m_DevInfo.BusiBoardInfo[i].FPGATime));
            memcpy(m_DevInfo.BusiBoardInfo[i].RFHWVersion, AllBusiBoardInfo[i].RFHWVersion, sizeof(m_DevInfo.BusiBoardInfo[i].RFHWVersion));
            memcpy(m_DevInfo.BusiBoardInfo[i].RemarkInfo, AllBusiBoardInfo[i].RemarkInfo, sizeof(m_DevInfo.BusiBoardInfo[i].RemarkInfo));
        }
    }
    return WT_OK;
}

int WTDeviceInfo::GetSystemInfoByUsbStorage(void)
{
    string DiskString = Basefun::shell_exec("fdisk -l | grep -oE  '/dev/sd[b-z][1-9]'");
    istringstream DiskIsStream(DiskString);
    string DstDir = " /mnt";
    string Line;
    while (getline(DiskIsStream, Line))
    {
        if (access(Line.c_str(), F_OK) == 0)
        {
            Basefun::LinuxSystem((string("mount ") + Line + DstDir).c_str());

            //如果U盘存在启动脚本则额外执行启动脚本，用于紧急问题处理
            if (access("/mnt/WT4xxStartScript.sh", F_OK) == 0)
            {
                Basefun::LinuxSystem("/mnt/WT4xxStartScript.sh");
            }

            //组织系统信息
            stringstream NewStrs;
            NewStrs << "Tester Name:" << m_DevInfo.Name << endl;
            NewStrs << "Module Name:" << m_DevType << endl;
            NewStrs << "Serial Number:" << m_DevInfo.SN << endl;
            NewStrs << "IP Address:" << m_DevInfo.IP << endl;
            NewStrs << "Submask Address:" << m_DevInfo.SubMask << endl;
            NewStrs << "Gateway Address:" << m_DevInfo.GateWay << endl;
            NewStrs << "Mac Address:" << m_DevInfo.Mac << endl;
            NewStrs << "FW Version:" << m_DevInfo.FwVersion << endl;
            NewStrs << "Alg Version:" << m_DevInfo.ALGVersion << endl;
            const char *CalibrationVersion = wt_calibration_version();
            NewStrs << "Calibration Version:" << CalibrationVersion << endl;

            DevConf::Instance().GetALLGUIString(NewStrs);

            NewStrs << "HW Version:" << endl;
            NewStrs << "Back: FPGAVersion:" << m_DevInfo.BPInfo.FPGAVersion
                    << ",BP HWVersion:" << m_DevInfo.BPInfo.BPHWVersion
                    << ",SWVersion" << m_DevInfo.BPInfo.SwitchHWVersion << endl;
            for (int i = 0; i < m_DevInfo.BusiBoardCount; i++)
            {
                NewStrs << m_DevInfo.BusiBoardInfo[i].Type << ": SN:" << m_DevInfo.BusiBoardInfo[i].SN
                        << ",FPGAVersion:" << m_DevInfo.BusiBoardInfo[i].FPGAVersion
                        << ",BB Version:" << m_DevInfo.BusiBoardInfo[i].BBHWVersion
                        << ",RFVersion=" << m_DevInfo.BusiBoardInfo[i].RFHWVersion << endl;
            }

            std::vector<LicItemInfo> LicItemsInfo;
            if (WT_OK == License::Instance().GetAllLicItemsInfo(LicItemsInfo))
            {
                for (int i = 0; i < LicItemsInfo.size(); i++)
                {
                    char TmpStr[100] = {0};
                    sprintf(TmpStr, "%s,startime:%d-%d-%d %d:%d:%d,endtime:%d-%d-%d %d:%d:%d,resourceNum:%d", LicItemsInfo[i].LicName,
                            LicItemsInfo[i].StartTime.year, LicItemsInfo[i].StartTime.mon, LicItemsInfo[i].StartTime.mday,
                            LicItemsInfo[i].StartTime.hour, LicItemsInfo[i].StartTime.min, LicItemsInfo[i].StartTime.sec,
                            LicItemsInfo[i].EndTime.year, LicItemsInfo[i].EndTime.mon, LicItemsInfo[i].EndTime.mday,
                            LicItemsInfo[i].EndTime.hour, LicItemsInfo[i].EndTime.min, LicItemsInfo[i].EndTime.sec,
                            LicItemsInfo[i].ResourceNum);
                    NewStrs << TmpStr << endl;
                }
            }

            string InfoFileName = "";
            InfoFileName.append("/mnt/");
            InfoFileName.append(m_DevInfo.SN);
            InfoFileName.append("_Dev_info.txt");
            ofstream OutFile(InfoFileName, fstream::out | fstream::trunc);
            OutFile << NewStrs.str();
            OutFile.close();

            //ifconfig info subnet info
            Basefun::LinuxSystem("ifconfig -a > /mnt/ifconfig.cfg && cp /home/<USER>/bin/eth/subnet.conf /mnt/");
            Basefun::LinuxSystem((string("umount ") + DstDir).c_str());
        }
    }
    return WT_OK;
}

int WTDeviceInfo::GetDeviceVersionInfo(std::string &JsonVerInfoStr)
{
    Json::StyledWriter Swriter;
    Json::Value VersionInfo;

    VersionInfo["VsaAlgLibVer"] = WT_Algorithm_DllVersion();           // 获取vsa算法版本信息
    VersionInfo["VsgAlgLibVer"] = WT_Algorithm_DllVersion_Vsg();       // 获取vsg算法版本信息
    VersionInfo["3GPPVsaAlgLibVer"] = Callback_3GPP_VsaDLLVersion();   // 获取vsa算法版本信息
    VersionInfo["3GPPVsgAlgLibVer"] = Callback_3GPP_VsgDllVersion();   // 获取vsg算法版本信息
    VersionInfo["CalibrationLibVer"] = wt_calibration_version();       // 获取校准库的版本信息
    VersionInfo["Ft4222Info"] = FT4222Dev::Instance().GetFt4222INfo(); // 获取FT4222版本信息

    JsonVerInfoStr = Swriter.write(VersionInfo);

    return WT_OK;
}

int WTDeviceInfo::SaveBoardHwVersion()
{
    int RevisionId = 0;
    string BusiBaordName;
    int SWBHwVersion = VERSION_E;
    string FileDir = WTConf::GetDir() + "/HwInfo";

    if (-1 == access(FileDir.c_str(), F_OK)) //pathloss 不存在则创建
    {
        if (-1 == mkdir(FileDir.c_str(), 0755)) //创建目录
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Create Path Error!" << endl;
        }
    }

    DevLib::Instance().GetSwbHwVersion(SWBHwVersion);
    if (DevLib::Instance().GetRevisionId(0, DEV_TYPE_BACK, RevisionId) == WT_OK &&
        WTDeviceInfo::CheckFpgaVersionValid(m_DevInfo.BPInfo.FPGAVersion)  == WT_OK &&
        strncmp(m_DevInfo.BPInfo.BPHWVersion, "ff.ff.", strlen("ff.ff.")) != 0 &&
        strcmp(m_DevInfo.BPInfo.BPHWVersion, "00.00.00.00") != 0 && strcmp(m_DevInfo.BPInfo.BPHWVersion, "0.0.0.0") != 0 &&
        WTDeviceInfo::CheckHwVersionValid(m_DevInfo.BPInfo.SwitchHWVersion) == WT_OK)
    {
        string FileName= FileDir + "/BackHwVersion.txt";
        ofstream OutFile(FileName, fstream::out);
        OutFile << "BackFpgaVersion = " << m_DevInfo.BPInfo.FPGAVersion << std::endl;
        OutFile << "BackPlaneHwVersion = " << m_DevInfo.BPInfo.BPHWVersion << std::endl;
        OutFile << "SwitchBoardHwVersion = " << m_DevInfo.BPInfo.SwitchHWVersion << std::endl;
        OutFile << "BackPlaneRevisionId = " << RevisionId << std::endl;
        OutFile << "SwbVersion = " << SWBHwVersion << std::endl;
        OutFile.close();    
        WTFileSecure::EncryptFile(FileName);
    }

    for(int i = 0; i< m_DevInfo.BusiBoardCount ; i++)
    {
        BusiBaordName = "Busi";
        if (DevLib::Instance().GetRevisionId(0, DEV_TYPE_BUSI, RevisionId) == WT_OK &&
            WTDeviceInfo::CheckFpgaVersionValid(m_DevInfo.BusiBoardInfo[i].FPGAVersion)  == WT_OK &&
            strncmp(m_DevInfo.BusiBoardInfo[i].BBHWVersion, "ff.ff.", strlen("ff.ff.")) != 0 &&
            strcmp(m_DevInfo.BusiBoardInfo[i].BBHWVersion, "00.00.00.00") != 0 && strcmp(m_DevInfo.BusiBoardInfo[i].BBHWVersion, "0.0.0.0") != 0 &&
            WTDeviceInfo::CheckHwVersionValid(m_DevInfo.BusiBoardInfo[i].RFHWVersion) == WT_OK)
        {
            string FileName= FileDir + "/" + BusiBaordName +"HwVersion.txt";
            ofstream OutFile(FileName, fstream::out);
            OutFile << (BusiBaordName + "FpgaVersion = ") << m_DevInfo.BusiBoardInfo[i].FPGAVersion << std::endl;
            OutFile << (BusiBaordName + "HwVersion = ") << m_DevInfo.BusiBoardInfo[i].BBHWVersion << std::endl;
            OutFile << (BusiBaordName + "RFHWVersion = ") << m_DevInfo.BusiBoardInfo[i].RFHWVersion << std::endl;
            OutFile << (BusiBaordName + "RevisionId = ") << RevisionId << std::endl;     
            OutFile.close();
            WTFileSecure::EncryptFile(FileName);
            break;
        }
    }
    return WT_OK;
}

int WTDeviceInfo::ReadBoardHwVersion(DeviceHwInfo &HwInfo)
{
    int Ret = WT_OK;
    string FileDir = WTConf::GetDir() + "/HwInfo";
    do
    {
        Ret = WT_OK;
        WTConf Conf(FileDir + "/BackHwVersion.txt");
        Ret |= Conf.GetItemVal("BackPlaneHwVersion", HwInfo.BackPlaneHwVersion);
        Ret |= Conf.GetItemVal("BackPlaneRevisionId", HwInfo.BackPlaneRevisionId);
        Ret |= Conf.GetItemVal("SwitchBoardHwVersion", HwInfo.SwitchBoardHwVersion);
        Ret |= Conf.GetItemVal("SwbVersion", HwInfo.SwbVersion);
        if (Ret == WT_OK)
        {
            HwInfo.BackInfoIsOk = true;
        }
    } while (0);

    do
    {
        Ret = WT_OK;
        WTConf Conf(FileDir + "/BusiHwVersion.txt");
        Ret |= Conf.GetItemVal("BusiFpgaVersion", HwInfo.BusiFpgaVersion);
        Ret |= Conf.GetItemVal("BusiHwVersion", HwInfo.BusiHwVersion);
        Ret |= Conf.GetItemVal("BusiRevisionId", HwInfo.BusiRevisionId);
        Ret |= Conf.GetItemVal("BusiRfHwVersion", HwInfo.BusiRfHwVersion);
        if (Ret == WT_OK)
        {
            HwInfo.BusiInfoIsOk = true;
        }
    } while (0);
    return WT_OK;
}

int WTDeviceInfo::CheckFpgaVersionValid(const char *VersionStr)
{
    unsigned int Data[4];
    sscanf(VersionStr, "%x.%x.%x.%x", &Data[3], &Data[2], &Data[1], &Data[0]);
    unsigned int Version = ((Data[3] & 0xFF) << 24) |
                           ((Data[2] & 0xFF) << 16) |
                           ((Data[1] & 0xFF) << 8) |
                           ((Data[0] & 0xFF) << 0);
                           
    //当某段大于F0时，逻辑需提前进位。
    if (Data[0] > 0xF0 || Data[1] > 0xF0 || Data[2] > 0xF0 || Data[3] > 0xF0)
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
    else if (Version == 0 || Version == 1 || (Version & 0xFF) == 0)
    {
        return WT_UNITBOARD_NOT_EXIST;
    }

    return WT_OK;
}

int WTDeviceInfo::CheckHwVersionValid(const char *VersionStr)
{
    if (strlen(VersionStr) == 0)
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
    unsigned int Data[4];
    sscanf(VersionStr, "%x.%x.%x.%x", &Data[3], &Data[2], &Data[1], &Data[0]);

    if ((Data[0] & 0x7) == 0x7 || (Data[1] & 0x7) == 0x7 || (Data[2] & 0x7) == 0x7 || (Data[3] & 0x7) == 0x7)
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
    return WT_OK;
}
