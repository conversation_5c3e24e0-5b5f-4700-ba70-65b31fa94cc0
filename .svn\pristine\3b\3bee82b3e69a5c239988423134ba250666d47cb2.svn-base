#ifndef ALG_3GPP_VSGDEF_H_
#define ALG_3GPP_VSGDEF_H_

#include "alg_3gpp_apidef.h"
#include "alg_3gpp_apidef_4g.h"
#include "alg_3gpp_apidef_5g.h"
#include "alg_3gpp_apidef_nbiot.h"
#include "alg_3gpp_apidef_wcdma.h"
#include "alg_3gpp_apidef_gsm.h"

typedef struct
{
    int standard;            //Standard, reference ALG_3GPP_STANDARD_TYPE,ex. 5G,4G,3G,2G
    int subType;             //Subtype ex.3G:WCDMA, CDMA2000, TDS-CDMA
    int bandwidth;
    int samplingRate;        //Sample rate: unit(Hz)
    int NSS;
    int segment;
    double FreqErr;          //Frequency error 
    double IQImbalanceAmp;   //IQ Imbalance Amplitude Factor
    double IQImbalancePhase; //IQ Imbalance Phase Factor
    double DCOffset_I;       //DC offset I Factor
    double DCOffset_Q;       //DC offset Q Factor
    double ClockErr;        //Clock Error(ppm)
    double Snr;
    double Gap;              //Unit: Sec,default 10us, 0.00001s
    double FlatFactor;
    int ReducePARA;
    int PhaseNoiseFlg;
    double PhaseNoiseFactor[6];
    int Reserved[236];       //Reversed
} PNSetBaseType;

typedef struct {
    PNSetBaseType CommonParam;

    union {
        Alg_5G_WaveGenType NR;
        Alg_4G_WaveGenType LTE;
        Alg_NBIOT_WaveGenType NBIOT;
        Alg_WCDMA_WaveGenType WCDMA;
        Alg_GSM_WaveGenType GSM;
        
        /* Maximum input parameter memory limit 4M */
        char Reserved[1024*1024*4];
    };

    void *Extdata;
} Alg_3GPP_WaveGenType;

#endif /* ALG_3GPP_VSGDEF_H_ */