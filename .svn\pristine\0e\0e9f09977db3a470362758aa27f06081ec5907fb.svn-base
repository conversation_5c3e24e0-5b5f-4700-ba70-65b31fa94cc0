#!/bin/bash 

lspci -v > /home/<USER>/pciinfo.txt

basenum=`lspci -d 3333:3333 -vv | grep -P  'Region \d: Memory at [a-f0-9]+' | wc -l`
echo "basenum Memorybar num ="$basenum

#开机时检测PCI设备正常，不正常则重启
if [ $basenum -lt 2 ]; then
	echo "reboot $basenum" >> /home/<USER>/pcireboot.txt
	cnt=`cat /home/<USER>/pcireboot.txt | wc -l`
	if [ $cnt -lt 2 ];then
	   reboot
	fi
elif [ -e "/home/<USER>/pcireboot.txt" ]; then
	rm /home/<USER>/pcireboot.txt
fi
