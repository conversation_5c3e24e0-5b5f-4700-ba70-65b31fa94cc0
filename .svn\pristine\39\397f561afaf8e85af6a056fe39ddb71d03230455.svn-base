//*****************************************************************************
//  File: vsa.cpp
//  vsa业务处理
//  Data: 2016.8.10
//*****************************************************************************
#include "vsa.h"
#include <unistd.h>
#include <dirent.h>
#include <new>
#include <cstring>
#include <sys/time.h>
#include <cerrno>
#include "wt-calibration.h"
#include "devlib.h"
#include "monitor.h"
#include "wtypes.h"
#include "calconf.h"
#include "basefun.h"
#include "math.h"
#include "devcmd.h"
#include "wtlog.h"

using namespace std;
using namespace placeholders;

#define AGC_DEBUG   1
#define FRAME_POWER_OFFSET        13    //AGC根据所有档位的帧功率计算参考电平时的偏移值，经验值
#define PEAK_POWER_OFFSET         3     //AGC根据所有档位的峰值功率计算参考电平时的偏移值，经验值
#define TRIG_LEVEL_OFFSET         -20   //触发电平加上参考电平应该等于帧功率（无帧时取平均功率）减13，经验值
#define TRIG_LEVEL_OFFSET2         5    //触发电平加上参考电平应该等于帧功率（无帧时取平均功率）减13，经验值
#define TB_TF_AGC_WAIT_TIME_EXT   0.2
#define POEWR_LEVEL_ADJUST_THRESHOLD    5.0 //若上次参考电平调整幅度大于5dBm，仍需再调整一次,
#define POEWR_LEVEL_BIG_THRESHOLD    10.0 //如当前当前参考电平远大于峰值功率，当前挡位以上的挡位无效
#define POEWR_PEAK_ERROR_THRESHOLD   10.0 //峰值功率不能大于参考电平10dBm，否则为异常
#define MIN_SIGNAL_TO_NOISE_RATE   5     //最小信噪比

#define IMPULSE_ERROR_ABSOLUTE_THRESHOLD    17.04 // 10log(8192^2 + 8192^2) - 64.23 = 17.04, 最大峰值功率
#define IMPULSE_ERROR_RELATIVE_THRESHOLD    50.0 //峰均比过大的信号认为是毛刺，验值
#define IMPULSE_ERROR_BIG_THRESHOLD  30.0
static const double MIN_TEMP_DIFF = 0.5; //温度变化最小值，超过此值则需要做温度补偿

VsaAutoRange::VsaAutoRange()
{
    string Val;
    if (DevConf::Instance().GetItemVal("RefOffset", Val) == WT_OK)
    {
        m_RefOffset = atof(Val.c_str());
    }

    if (DevConf::Instance().GetItemVal("RangeDiffMin", Val) == WT_OK)
    {
        m_RangeDiffMin = atof(Val.c_str());
    }

    if (DevConf::Instance().GetItemVal("RangeDiffMax", Val) == WT_OK)
    {
        m_RangeDiffMax = atof(Val.c_str());
    }
    m_CurTrigLevelOffSet = TRIG_LEVEL_OFFSET;
}

int VsaAutoRange::ChecckPowerPeak(double Power, double PeakPower, double FinalGain, bool HasFrame)
{
    int Ret = true;
    if (PeakPower + FinalGain > IMPULSE_ERROR_ABSOLUTE_THRESHOLD + PEAK_POWER_OFFSET)
    {
        Ret = false;
    }
    else if (HasFrame && PeakPower - Power > IMPULSE_ERROR_RELATIVE_THRESHOLD)
    {
        Ret = false;
    }
    else if (!HasFrame && PeakPower - Power > IMPULSE_ERROR_BIG_THRESHOLD)
    {
        Ret = false;
    }
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ChecckPowerPeak:" << Pout(Power) << Pout(PeakPower) << Pout(FinalGain) << Pout(Ret) << endl;
    return Ret;
}

int VsaAutoRange::AdjustRefRangeExtGain(double ExtGain)
{
    //处理外部线衰
    for (int i = 0; i < m_RefRange.size(); i++)
    {
        m_RefRange[i].RefDown = m_RefRangeBase[i].RefDown + ExtGain;
        m_RefRange[i].RefUp = m_RefRangeBase[i].RefUp + ExtGain;
    }
    return WT_OK;
}

int VsaAutoRange::RecordCurLevel(double Ampl, double Power, double PeakPower, double MinPower, bool HasFrame, int TrigType, int Demode)
{
    (void)(Demode);
    m_LevelIdx = 0;
    for (auto &Range : m_RefRange)
    {
        if (Ampl > Range.RefDown && Ampl <= Range.RefUp)
        {
            m_LastAmplBak = m_CurAmplBak;
            m_CurAmplBak = Ampl;
            m_CurAmpl = Ampl;

            //有帧的情况下，PowerLevel代表帧功率，没帧的情况下，PowerLevel代表平均功率。
            Range.PowerLevel = Power;
            Range.PeakPower = PeakPower;
            Range.Ampl = Ampl;
            Range.MinPower = MinPower;
            if (HasFrame)
            {
                Range.HasFrame = HasFrame;
            }

#if 1
            // 新调整方法: 用来规避sin信号 TrigType模式下硬件偶尔能够触发采集数据的问题
            if (!Range.Verified)
            {
                if ((TrigType == WT_TRIG_TYPE_IF && HasFrame == false) || (TrigType == WT_TRIG_TYPE_FREE_RUN))
                {
                    Range.Verified = true;
                    m_VerifiedCnt++;
                }
            }
#else
            // 原始方法
            if (!Range.Verified && TrigType == WT_TRIG_TYPE_FREE_RUN)
            {
                Range.Verified = true;
                m_VerifiedCnt++;
            }
#endif

            return WT_OK;
        }

        m_LevelIdx++;
    }

    //没有找到对应的挡位，终止autorange
    SetCompleteState(AGC_COMPLETE_FAILED);
    return WT_ARG_ERROR;
}

double VsaAutoRange::GetAdjacentLevel(void)
{
    bool AllRangeHasFrame = true;

    for (const auto &Range : m_RefRange)
    {
        if (!Range.HasFrame)
        {
            AllRangeHasFrame = false;
        }
    }

    int k = 0;
    double Min;
    double Diff;
    double RefLevel;

#if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug AdjustRefRange using all! AllRangeHasFrame:%d\n",AllRangeHasFrame);
#endif

    if (AllRangeHasFrame == true)
    {
        //所有挡位都有帧的情况下，使用帧功率计算参考电平。
        Min = 0xFFFFFFFF;           //赋一个大值以便于比较。
        for (int i = 0; i < m_RefRange.size() - 1; i++)
        {
            Diff = std::abs(m_RefRange[i].PowerLevel - m_RefRange[i + 1].PowerLevel);
            if (Diff < Min && m_RefRange[i].Valid == true && m_RefRange[i + 1].Valid == true)
            {
                Min = Diff;
                k = i;
            }
        }
        //这里没有处理只有一个档位有效的情况，认为硬件与校准数据正常的情况下，这种情况不可能出现。
        RefLevel = std::max(m_RefRange[k].PowerLevel, m_RefRange[k + 1].PowerLevel) + FRAME_POWER_OFFSET;
    }
    else
    {
        //有挡位没有分析到帧的情况下，使用峰值功率计算参考电平。
        Min = 0xFFFFFFFF;           //赋一个大值以便于比较。
        for (int i = 0; i < m_RefRange.size() - 1; i++)
        {
            //当该档峰值功率小于该档最小参考电平-PEAK_POWER_OFFSET时，认为该档的参考电平都过高，不再考虑该档。
            //作此处理是因为空采AGC时，PeakPower和参考电平带来的泄露正相关，不适合用最小峰值功率差的算法。
            if(m_RefRange[i].PeakPower < m_RefRange[i].RefDown - PEAK_POWER_OFFSET)
            {
                continue;
            }

            Diff = std::abs(m_RefRange[i].PeakPower - m_RefRange[i + 1].PeakPower);
            if (Diff < Min && m_RefRange[i].Valid == true && m_RefRange[i + 1].Valid == true)
            {
                Min = Diff;
                k = i;
            }
        }

        if(Min != 0xFFFFFFFF)
        {
            RefLevel = std::max(m_RefRange[k].PeakPower, m_RefRange[k + 1].PeakPower) + PEAK_POWER_OFFSET;
        }
        else
        {
            int i = 0;
            for (i = m_RefRange.size() - 1; i > 0; i--)
            {
                if (m_RefRange[i].Valid == true)
                {
                    break;
                }
            }
            //没有连续两个有效档位时，直接用有效的最低档位的PeakPower来计算参考电平。
            RefLevel = m_RefRange[i].PeakPower + PEAK_POWER_OFFSET;
        }
    }

    if (RefLevel > m_MaxRefLevel)
    {
        RefLevel = m_MaxRefLevel;
    }
    else if (RefLevel < m_MinRefLevel)
    {
        RefLevel = m_MinRefLevel;
    }

    return RefLevel;
}

double VsaAutoRange::AdjustTrigLevel()
{
    double TrigLevel;

    if (m_RefRange[m_LevelIdx].PowerLevel - m_RefRange[m_LevelIdx].MinPower > MIN_SIGNAL_TO_NOISE_RATE)
    {
        TrigLevel = (m_RefRange[m_LevelIdx].PowerLevel + m_RefRange[m_LevelIdx].MinPower) / 2 - m_CurAmpl;
    }
    else
    {
        if (abs(m_CurAmpl - m_MinRefLevel) < 0.0001)
        {
            TrigLevel = m_RefRange[m_LevelIdx].PowerLevel - m_CurAmpl + m_CurTrigLevelOffSet;
        }
        else
        {
            TrigLevel = m_RefRange[m_LevelIdx].PowerLevel - m_RefRange[m_LevelIdx].RefUp + m_CurTrigLevelOffSet;
        }
    }

    if (TrigLevel > m_MaxTrigLevel)
    {
        TrigLevel = m_MaxTrigLevel;
    }
    else if (TrigLevel < m_MinTrigLevel)
    {
        TrigLevel = m_MinTrigLevel;
    }
    m_RefRange[m_LevelIdx].ValidTrigLevel = true;

    return TrigLevel;
}

double VsaAutoRange:: AdjustBstTrigLevel()
{
    double TrigLevel;

    TrigLevel = m_RefRange[m_LevelIdx].PowerLevel - m_CurAmpl + m_CurTrigLevelOffSet;

    if (TrigLevel > m_MaxTrigLevel)
    {
        TrigLevel = m_MaxTrigLevel;
    }
    else if (TrigLevel < m_MinTrigLevel)
    {
        TrigLevel = m_MinTrigLevel;
    }

    m_RefRange[m_LevelIdx].ValidTrigLevel = true;
    return TrigLevel;
}

double VsaAutoRange::AdjustRefRange(double PeakPower, bool HasFrame, int Demode)
{
    int i = 0;
    int IsTryAllRange = true;

    double RefOffset = m_RefOffset;
    if (Demode == WT_DEMOD_CW)
    {
        RefOffset += 0.5;
    }

    double RefLevel = PeakPower + RefOffset;
    if (RefLevel > m_MaxRefLevel)
    {
        RefLevel = m_MaxRefLevel;
    }
    else if (RefLevel < m_MinRefLevel)
    {
        RefLevel = m_MinRefLevel;
    }

    //加异常保护，峰值功率不能大于参考电平10dBm，否则为异常,重新尝试当前挡位
    if (m_RefRange[m_LevelIdx].PeakPower - m_RefRange[m_LevelIdx].Ampl > POEWR_PEAK_ERROR_THRESHOLD)
    {
        return m_CurAmpl;
    }

    //如当前挡位削顶，去掉当前挡位以下的挡位无效，如当前当前参考电平远大于峰值功率，当前挡位以上的挡位无效
    if (FliterRefRange() < 0)
    {
        return m_RefRange[m_LevelIdx].RefUp;
    }

    for (const auto &Range : m_RefRange)
    {
        if (!Range.Verified)
        {
            IsTryAllRange = false;
            break;
        }
    }

    //所有挡位都已尝试过，直接计算参考电平。
    if (IsTryAllRange)
    {
        RefLevel = GetAdjacentLevel();
        SetRefLevelState(true);
        return RefLevel;
    }

    //如果此参考电平所属的挡位已经使用过，且还有空档位，则切换其他的挡位
    for (const auto &Range : m_RefRange)
    {
        if (!HasFrame || (m_RefRange[m_LevelIdx].PowerLevel - m_RefRange[m_LevelIdx].MinPower < POEWR_PEAK_ERROR_THRESHOLD)
            || (Range.RefUp >= RefLevel && Range.RefDown < RefLevel && Range.Verified))
        {
            if (PeakPower < m_CurAmpl)
            {
                for (i = m_LevelIdx + 1; i < m_RefRange.size(); i++)
                {
                    if (!m_RefRange[i].Verified)
                    {
                        RefLevel = m_RefRange[i].RefUp;
                        break;
                    }
                    else if (!m_RefRange[i].Valid || Basefun::CompareDouble(m_RefRange[i].Ampl, m_RefRange[i].PeakPower) < 0)
                    {
                        //虽然下一个挡位已经尝试过，但是发生了削顶，在下一个挡位进行微调
                        RefLevel = AdjustRefLevel(PeakPower, false, Demode);
                        break;
                    }
                }
            }
            else
            {
                for (i = m_LevelIdx - 1; i >= 0; i--)
                {
                    if (!m_RefRange[i].Verified)
                    {
                        RefLevel = m_RefRange[i].RefUp;
                        break;
                    }
                }
            }

            //调整的方向已经没有可用的档位，在当前档位微调参考电平。
            if (i < 0 || i == m_RefRange.size())
            {
                RefLevel = AdjustRefLevel(PeakPower, false, Demode);
            }

            break;
        }
    }

    return RefLevel;
}

double VsaAutoRange::AdjustRefLevel(double PeakPower, bool HasFrame, int Demode)
{
    //如果在合适的范围内则认为调整成功
    double RangeDiffMin = m_RangeDiffMin;
    double RangeDiffMax = m_RangeDiffMax;
    double RefOffset = m_RefOffset;

    //当与信号源时钟不一致时，信号源为SIN0Mhz时，AGC到的参考电平可能因太接近峰值功率而削顶，导致功率跳动。
    //所以未解析到帧或者连续信号AGC时，参考电平多留0.5dB的余量
    if (!HasFrame || Demode == WT_DEMOD_CW)
    {
        RangeDiffMin += 0.5;
        RangeDiffMax += 0.5;
        RefOffset += 0.5;
    }

    // 添加异常保护，峰值功率不能大于参考电平?dBm，否则为异常,重新尝试当前挡位
    if (m_RefRange[m_LevelIdx].PeakPower - m_RefRange[m_LevelIdx].Ampl > POEWR_PEAK_ERROR_THRESHOLD)
    {
        return m_CurAmpl;
    }

    if ((m_CurAmpl - PeakPower > RangeDiffMin && m_CurAmpl - PeakPower < RangeDiffMax && abs(m_CurAmplBak - m_LastAmplBak) < POEWR_LEVEL_ADJUST_THRESHOLD)
       || (PeakPower < m_MinRefLevel && abs(m_CurAmpl - m_MinRefLevel) < 0.0001))
    {
        SetRefLevelState(true);
        return m_CurAmpl;
    }

    double RefLevel = PeakPower + RefOffset;
    if (RefLevel > m_MaxRefLevel)
    {
        RefLevel = m_MaxRefLevel;
    }
    else if (RefLevel < m_MinRefLevel)
    {
        RefLevel = m_MinRefLevel;
    }

    m_CurAmpl = RefLevel;

    return RefLevel;
}

//如果都使用过则直接查找最合适的参考电平，否则使用未测试过的
double VsaAutoRange::GetUnusedRef()
{
    double RefLevel = 0;

    if (m_VerifiedCnt == m_RefRange.size())
    {
        RefLevel = GetAdjacentLevel();
        SetRefLevelState(true);
    }
    else
    {
        if (Basefun::CompareDouble(m_CurAmpl, m_MaxRefLevel) >= 0)
        {
            //达到参考电平上限时，全部位于参考电平上限以上的档位不再考虑，认为已经尝试过。
            for (auto &Range : m_RefRange)
            {
                if (Basefun::CompareDouble(Range.RefDown, m_MaxRefLevel) >= 0 && !Range.Verified)
                {
#if AGC_DEBUG
                    WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Range[%f ~ %f] over the m_MaxRefLevel[%f],Invalid!\n",Range.RefUp,Range.RefDown,m_MaxRefLevel);
#endif
                    Range.Verified = true;
                    Range.Valid = false;        //该档位无效
                    m_VerifiedCnt++;
                }
            }
        }
        else
        {
            //达到参考电平下限时，全部位于参考电平下限以下的档位不再考虑，认为已经尝试过。
            for (auto &Range : m_RefRange)
            {
                if (Basefun::CompareDouble(Range.RefUp, m_MinRefLevel) <= 0 && !Range.Verified)
                {
#if AGC_DEBUG
                    WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Range[%f ~ %f] under the m_MinRefLevel[%f],Invalid!\n",Range.RefUp,Range.RefDown,m_MinRefLevel);
#endif
                    Range.Verified = true;
                    Range.Valid = false;        //该档位无效
                    m_VerifiedCnt++;
                }
            }
        }

        //如当前挡位削顶，去掉当前挡位以下的挡位无效，如当前当前参考电平远大于峰值功率，当前挡位以上的挡位无效
        if (FliterRefRange() < 0)
        {
            return m_RefRange[m_LevelIdx].RefUp;
        }

        if (m_VerifiedCnt == m_RefRange.size())
        {
            RefLevel = GetAdjacentLevel();
            SetRefLevelState(true);
        }
        else
        {
            //切换档位前，将当前档位设定为已经尝试过。
            if(!m_RefRange[m_LevelIdx].Verified)
            {
                m_RefRange[m_LevelIdx].Verified = true;
                m_VerifiedCnt++;
            }
            for (const auto &Range : m_RefRange)
            {
                if (!Range.Verified)
                {
                    RefLevel = Range.RefUp;
                    break;
                }
            }
        }
    }

    return RefLevel;
}

int VsaAutoRange::FliterRefRange()
{
    if (Basefun::CompareDouble(m_CurAmpl, m_MaxRefLevel) > 0)
    {
        m_RefRange[m_LevelIdx].Ampl = m_MaxRefLevel;
    }
    else if (Basefun::CompareDouble(m_CurAmpl, m_MinRefLevel) < 0)
    {
        m_RefRange[m_LevelIdx].Ampl = m_MinRefLevel;
    }

    // 理论上峰值功率大会于参考电平，大于则为异常，留1dBm余量作为判断误差
    if (m_RefRange[m_LevelIdx].PeakPower - m_RefRange[m_LevelIdx].Ampl > 1)
    {
        for (int i = m_LevelIdx + 1; i < m_RefRange.size(); i++)
        {
            if (!m_RefRange[i].Verified)
            {
#if AGC_DEBUG
                WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Range[%f ~ %f] under the Ampl, PeakPower [%f, %f], Invalid!\n",
                       m_RefRange[i].RefUp, m_RefRange[i].RefDown, m_RefRange[m_LevelIdx].Ampl, m_RefRange[m_LevelIdx].PeakPower);
#endif
                m_RefRange[i].Verified = true;
                m_RefRange[i].Valid = false; // 该档位无效
                m_VerifiedCnt++;
            }
        }

        if (m_RefRange[m_LevelIdx].PeakPower > m_RefRange[m_LevelIdx].RefUp)
        {
            m_RefRange[m_LevelIdx].Verified = true;
            m_RefRange[m_LevelIdx].Valid = false; // 该档位无效
            return WT_OK;
        }

        return -1; // 当前参考电平比峰值功率小 用当前档位的最大值再尝试一次
    }
    // 参考电平远大于峰值功率，当前之上的挡位无效; 有帧时才判断，无帧时可能抓到GAP功率很低，并不准确
    else if ((m_RefRange[m_LevelIdx].Ampl - m_RefRange[m_LevelIdx].PeakPower > POEWR_LEVEL_BIG_THRESHOLD) && m_RefRange[m_LevelIdx].HasFrame)
    {
        for (int i = m_LevelIdx - 1; i >= 0; i--)
        {
            if (!m_RefRange[i].Verified)
            {
#if AGC_DEBUG
                WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Range[%f ~ %f] big then Ampl, [Ampl, PeakPower] = [%f, %f], Invalid!\n",
                       m_RefRange[i].RefUp, m_RefRange[i].RefDown, m_RefRange[m_LevelIdx].Ampl, m_RefRange[m_LevelIdx].PeakPower);
#endif
                m_RefRange[i].Verified = true;
                m_RefRange[i].Valid = false; // 该档位无效
                m_VerifiedCnt++;
            }
        }
        return 1; // 当前情况暂无特殊处理
    }

    return WT_OK;
}

void VsaAutoRange::SetAndBackupParam(VsaParam &Param)
{
    m_TestType = Param.Type;
    m_UserSmpTime = Param.SamplingTime;
    m_TrigPreTime = Param.TrigPreTime;
    m_Freq2 = Param.Freq2;
    m_ModMask = Param.VsaMask;
    m_UserAmpl = Param.Ampl;
    m_UserTrigLevel = Param.TrigLevel;
    m_UserTrigType = Param.TrigType;
    m_UserAmpl2 = Param.Ampl2;
    m_UserTrigLevel2 = Param.TrigLevel2;
    m_ExtGain = Param.ExtGain;
    m_ExtGain2 = Param.ExtGain2;

    Param.Type = TEST_SISO;
    Param.Ampl = GetDefaultAmpl();
    Param.Ampl2 = GetDefaultAmpl();
    Param.ExtGain = 0;
    Param.ExtGain2 = 0;

    if (m_UserTrigType != WT_TRIG_TYPE_FREE_RUN)
    {
        Param.TrigLevel = GetDefaultTrigLevel();
        Param.TrigLevel2 = GetDefaultTrigLevel();
    }

    Param.SamplingTime = GetDefaultSmpTime();
    Param.TrigPreTime = GetDefaultPreSmpTime();
    Param.TrigType = WT_TRIG_TYPE_IF;

    //如果8080开启了双端口或双射频参数模式，agc时则使用两个参考电平。
    if (Param.IsUseDualParam())
    {
        Param.Type = TEST_80_80M_AGC;
    }
    else if (Param.IsAC8080())  //80+80时只使用一个模块autorange
    {
        Param.Freq2 = 0;
        Param.VsaMask = 0;
    }
}

void VsaAutoRange::RestoreParam(VsaParam &Param, bool IsAgcOK)
{
    Param.Type = m_TestType;
    Param.SamplingTime = m_UserSmpTime;
    Param.TrigPreTime = m_TrigPreTime;
    Param.Freq2 = m_Freq2;
    Param.VsaMask = m_ModMask;
    Param.TrigType = m_UserTrigType;
    Param.ExtGain = m_ExtGain;
    Param.ExtGain2 = m_ExtGain2;

    if (!IsAgcOK)
    {
        Param.Ampl2 = m_UserAmpl2;
        Param.Ampl = m_UserAmpl;
        Param.TrigLevel2 = m_UserTrigLevel2;
        Param.TrigLevel = m_UserTrigLevel;
    }
    else
    {
        Param.Ampl += Param.ExtGain;
        Param.Ampl2 += Param.ExtGain2;
    }
#if 1
    WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Result=%d Ampl=%lf,Ampl2=%lf,TrigLevel=%lf,TrigLevel2=%lf\n", IsAgcOK, Param.Ampl, Param.Ampl2, Param.TrigLevel, Param.TrigLevel2);
#endif
}

void VsaAutoRange::SetRefLevelState(bool state)
{
    m_RefLevelState = state;
    if (state)
    {
        bool IsTriggerable = false;
        for (int i = 0; i < m_RefRange.size(); i++)
        {
            if (m_RefRange[i].Verified && m_RefRange[i].Valid && m_RefRange[i].HasFrame)
            {
                IsTriggerable = true;
            }
        }
        if (m_UserTrigType == WT_TRIG_TYPE_FREE_RUN)
        {
            SetCompleteState(AGC_COMPLETE_PASS);
        }
        else if (!IsTriggerable)
        {
            SetCompleteState(AGC_COMPLETE_PASS);
        }
    }
}

void VsaAutoRange::CheckCompleteState(int TrigType, bool HasFrame, double &CurTrigLevel)
{
    // Trigger模式下需调整参考电平和触发电平，FreeRun模式下只调整参考电平
    if (TrigType == WT_TRIG_TYPE_IF && m_UserTrigType == WT_TRIG_TYPE_IF && HasFrame)
    {
        SetCompleteState(AGC_COMPLETE_PASS);
    }
    else if ((TrigType == WT_TRIG_TYPE_IF && m_UserTrigType == WT_TRIG_TYPE_IF && !HasFrame) ||
             (TrigType == WT_TRIG_TYPE_FREE_RUN && m_UserTrigType == WT_TRIG_TYPE_IF))
    {
        if (Basefun::CompareDouble(CurTrigLevel, m_LastValidTrigLevel, 1) == 0)
        {
            m_CurTrigLevelOffSet += TRIG_LEVEL_OFFSET2;
            CurTrigLevel += TRIG_LEVEL_OFFSET2;
        }
        else if (CurTrigLevel <= m_MinTrigLevel)
        {
            m_CurTrigLevelOffSet += TRIG_LEVEL_OFFSET2;
            CurTrigLevel = m_RefRange[m_LevelIdx].PowerLevel - m_CurAmpl + m_CurTrigLevelOffSet;
        }
        m_LastValidTrigLevel = CurTrigLevel;
        if (m_CurTrigLevelOffSet >= 0) // 超出Trigger level的尝试范围
        {
            SetCompleteState(AGC_COMPLETE_PASS);
        }
    }
}

bool VsaParam::IsAgumentLegal(void) const
{
    stringstream Strs;
    Strs.clear();
    Strs.str("");
    Strs << "VsaParam:{" << endl \
        <<"    Freq:" << Freq << endl \
        <<"    Freq2:" << Freq2 << endl \
        <<"    FreqOffset:" << FreqOffset << endl \
        <<"    RFPort:" << RFPort << endl \
        <<"    Type:" << Type << endl \
        <<"    MasterMode:" << MasterMode << endl \
        <<"    SignalId:" << SignalId << endl \
        <<"    VsaMask:" << VsaMask << endl \
        <<"    TrigType:" << TrigType << endl \
        <<"    AllocTimeout:" << AllocTimeout << endl \
        <<"    Is160M:" << Is160M << endl \
        <<"    ExtGain:" << ExtGain << endl \
        <<"    Ampl:" << Ampl << endl \
        <<"    ExtGain2:" << ExtGain2 << endl \
        <<"    SamplingTime:" << SamplingTime << endl \
        <<"    SamplingFreq:" << SamplingFreq << endl \
        <<"    TrigPreTime:" << TrigPreTime << endl \
        <<"    TrigTimeout:" << TrigTimeout << endl \
        <<"    TrigLevel:" << TrigLevel << endl \
        <<"    MaxIFG:" << MaxIFG << endl \
        <<"    RFPort2:" << RFPort2 << endl \
        <<"    DulPortMode:" << DulPortMode << endl \
        <<"    Ampl2:" << Ampl2 << endl \
        <<"    TrigLevel2:" << TrigLevel2 << endl \
        <<"    Demode:" << Demode << endl \
        <<"    DCOffsetI:" << DCOffsetI << endl \
        <<"    DCOffsetQ:" << DCOffsetQ << endl \
        <<"}" << endl;
    const string& Str2 = Strs.str();
    WTLog::Instance().LOGOPERATE(Str2.c_str());

    if (!DigModeLib::Instance().IsDigMode())
    {
        if (SamplingTime <= 0 || SamplingTime * SamplingFreq > MAX_SAMPLEING_TIME * DEFAULT_SMAPLE_RATE) // 最大采样长度为20ms
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "set vsaparam''s capture length err,range : 0-0.1 * 240e6!");
            return false;
        }
    }
    else
    {
        if (SamplingTime <= 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "set vsaparam''s capture length err,range : 0-0.02 * 240e6!");
            return false;
        }
    }

    if(TrigPreTime < 0)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "set vsaparam''s TrigPreTime err,must >= 0!");
        return false;
    }
    if (TrigPreTime >= SamplingTime)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "set vsaparam''s TrigPreTime err,must < SamplingTime!");
        return false;
    }

    if (IsDualPortMode())
    {
        if (RFPort != 0 && RFPort2 != 0)
        {
            int ModId = 0;
            int ModId2 = 0;
            DevLib::Instance().GetModId(DEV_TYPE_VSA, RFPort, ModId);
            DevLib::Instance().GetModId(DEV_TYPE_VSA, RFPort2, ModId2);
            if (ModId2 == ModId)
            {
                WTLog::Instance().LOGERR(WT_ARG_ERROR, "80+80 Dul Mode cannt use same unit!");
                return false;
            }
        }
    }

    return true;
}

void VsaParam::ParamTest()
{
    Freq = 400000000;
}

WTVsa::WTVsa(std::shared_ptr<Connector> ExtConn, bool Exclude, const wtev::loop_ref &Loop)
    : WTBsn(ExtConn, Exclude, Loop), m_AutoRangeEv(Loop), m_HwOpFinPollingEv(Loop)
{
    m_AutoRangeEv.set<WTVsa, &WTVsa::WaitTrigger>(this);
    m_HwOpFinPollingEv.set<WTVsa, &WTVsa::HwOpFinPolling>(this);
    m_HwOpFinPollingEv.start(0, 0.5);

    DevConf::Instance().GetItemVal("VSASaveRawDataMaxNum" , m_SaveRawDataMax);
}

//VSA采集参数转换为devlib的VSA配置参数
VSAConfigType WTVsa::TransConfig(const VsaParam &Param, int Index)
{
    VSAConfigType Config;

    Config.DcOffsetI = TransDacCode(Param.DCOffsetI);
    Config.DcOffsetQ = TransDacCode(Param.DCOffsetQ);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WTVsa::TransConfig:"
         << Pout(Param.DCOffsetI) << Pout(Config.DcOffsetI)
         << Pout(Param.DCOffsetQ) << Pout(Config.DcOffsetQ)
         << endl;
    Config.FreqOffsetHz = Param.FreqOffset;
    Config.BaseBandGain = 0;
    Config.RFPortState = WT_RF_STATE_PI;
    Config.SamplingTime = Param.SamplingTime;
    Config.SamplingFreq = Param.SamplingFreq;
    Config.TrigPreTime = Param.TrigPreTime;
    Config.TrigGapTime = m_TrigParam.GapTime;
    Config.TrigEdge = m_TrigParam.Edge;

    if (Param.TrigPreTime < 1e-6) //pre trigger最小为1us
    {
        Config.TrigPreTime = 1e-6;
    }
    else
    {
        Config.TrigPreTime = Param.TrigPreTime;
    }
    Config.TrigTimeout = Param.TrigTimeout;
    Config.TrigLevel = Param.TrigLevel;
    Config.RFPort = Param.RFPort;
    Config.TrigType = Param.TrigType;
    Config.DeviceMode = GetDevMode(Param, Index);
    if(m_NoiseCalStatus.NoiseCalFlag)
    {
        Config.NoiseCompensation = CAL_NOISE_CAL_DATA;
    }
    else
    {
        Config.NoiseCompensation = CAL_NOISE_DISABLE;
        if (m_ExtendEVMStu.SncEVM == SNC_EVM_ON)
        {
            int port_list[8] = {1, 1, 1, 1, 1, 1, 1, 1};
            int PortNoiseStatus[8] = {0};
            int Ret = wt_calibration_get_noise_data_status(port_list, (CAL_NOISE_DATA_STATE *)PortNoiseStatus);
            if (Ret == WT_OK && PortNoiseStatus[Config.RFPort - WT_RF_1] != COMP_FILE_NO_EXISTS)
            {
                Config.NoiseCompensation = CAL_NOISE_DATA_ENABLE;
            }
        }
    }

    if (!Param.IsAC8080() || Index == 0)
    {
        Config.Ampl = Param.Ampl - Param.ExtGain;
        Config.Freq = Param.Freq;
    }
    else
    {
        Config.Ampl = Param.Ampl - Param.ExtGain2;
        Config.Freq = Param.Freq2;

        //80+80使用双射频参数时，从机使用第二个端口
        if (Param.IsUseDualParam())
        {
            Config.RFPort = Param.RFPort2;
            Config.TrigLevel = Param.TrigLevel2;
            Config.Ampl = Param.Ampl2 - Param.ExtGain2;
            //80+80双端口AGC时，两个模块独立运行，分别配置触发类型
            if (Param.Type == TEST_80_80M_AGC)
            {
                Config.TrigType = m_TrigType2;
            }
        }
    }

#if AGC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Set mod index=%d, port=%d Ampl=%lf, TrigLevel=%lf, TrigType=%d \n", Index, Config.RFPort, Config.Ampl, Config.TrigLevel, Config.TrigType);
#endif
    return Config;
}

void WTVsa::SendParamToMon(void)
{
    list<shared_ptr<Monitor>> Mons;
    MonitorMgr::Instance().GetMonitors(Mons);

    if (m_Param == nullptr)
    {
        return ;
    }

    //如果是非连续160，需要将频率数据还原再配置回去
    bool Is160 = m_Param[0].Is160() && m_Param[0].IsAC8080();
    if (Is160)
    {
        m_Param[0].Freq += 40e6;
        m_Param[0].Freq2 = 0;
    }

    for (auto &Mon : Mons)
    {
        if (Mon->IsMonPort(m_Param[0].RFPort))
        {
            Mon->SendVsaParam(&m_Param[0], sizeof(m_Param[0]));
        }
    }

    if (Is160)
    {
        m_Param[0].Freq -= 40e6;
        m_Param[0].Freq2 = m_Param[0].Freq + 80e6;
    }
}

int WTVsa::StartModWithParam(int ParamIdx)
{
    m_CurDataNoAlzFlag = true;
    for (int i = 0; i < (signed)m_Mods.size() && i < 2; i++)
    {
        VSAConfigType Config = TransConfig(m_Param[ParamIdx], i);

        int Ret = DevLib::Instance().VSASetConfig(m_Mods[i].ModId, Config, m_CapData[i].CalParam[0]);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "set vsa param to mod failed");
            return Ret;
        }
    }

    //使用多个模块时第一个模块为主，其他为从，需要先启动从，所以反向迭代
    for (auto iter = m_Mods.rbegin(); iter != m_Mods.rend(); iter++)
    {
        iter->Status = WT_RX_TX_STATE_RUNNING;
        int Ret = DevLib::Instance().VSAStart(iter->ModId);
        if (Ret != WT_OK)
        {
            iter->Status = WT_RX_TX_STATE_ERR_DONE;
            WTLog::Instance().LOGERR(Ret, "start module failed");
            return Ret;
        }

        //80+80双端口模式时，从机使用第二个端口
        if (m_Param[ParamIdx].IsDualPortMode() && !iter->IsMaster)
        {
            iter->RFPort = m_Param[ParamIdx].RFPort2;
        }
        else
        {
            iter->RFPort = m_Param[ParamIdx].RFPort;
        }

        m_CapParam[ParamIdx] = m_Param[ParamIdx];
        DevMgr::Instance().PortLedOn(DEV_TYPE_VSA, iter->RFPort);
    }

    m_FinCnt = 0;
    return WT_OK;
}

int WTVsa::SetActualAmpl(int Index)
{
    //实际参考电平和配置的差异大于1说明异常
    double ExtGain;
    double ParamAmpl;
    double VsaAmpl;
    if (!m_Param[0].IsAC8080() || Index == 0)
    {
        ExtGain = m_Param[0].ExtGain;
        ParamAmpl = m_Param[0].Ampl;
        VsaAmpl = m_CapData[Index].CalParam[0].rx_gain_parm.rx_sw_gain.actual_mpl + ExtGain;
        if (abs(VsaAmpl - ParamAmpl) >= 1)
        {
            if (m_AutoRange != nullptr)
            {
                VsaAmpl > ParamAmpl ? m_AutoRange->SetAmplRangeMin(VsaAmpl) : m_AutoRange->SetAmplRangeMax(VsaAmpl);
            }
            m_Param[0].Ampl = VsaAmpl;
        }
    }
    else if (1 < sizeof(m_CapData) / sizeof(m_CapData[0]))
    {
        if (m_Param[0].IsUseDualParam())
        {
            ExtGain = m_Param[0].ExtGain2;
            ParamAmpl = m_Param[0].Ampl2;
        VsaAmpl = m_CapData[Index].CalParam[0].rx_gain_parm.rx_sw_gain.actual_mpl + ExtGain;
            if (abs(VsaAmpl - ParamAmpl) >= 1)
            {
                if (m_AutoRange2 != nullptr)
                {
                    VsaAmpl > ParamAmpl ? m_AutoRange2->SetAmplRangeMin(VsaAmpl) : m_AutoRange2->SetAmplRangeMax(VsaAmpl);
                }
                m_Param[0].Ampl2 = VsaAmpl;
            }
        }
    }
    return WT_OK;
}

int WTVsa::SetDig()
{
    SetDigConfig(false);

    if(!m_DigChanList.size())
    {
        WTLog::Instance().LOGERR(WT_PARAM_NUM_ERROR, "WTVsg::SetDig m_DigChanList = 0");
        return WT_PARAM_NUM_ERROR;
    }

    int Ret = WT_OK;
    VsaDigConfigType Config;
    Config.ISTimeout = m_DigParam.VSAISTimeout;
    Config.RecvTimeout = m_DigParam.RecvTimeout;
    Config.ActionMask = m_DigParam.VsaActionMask;
    Config.MaxBitCnt = m_DigParam.VsaMaxBitCnt;

    if (Basefun::CompareDouble(m_Param[0].SamplingTime, DIG_IQ_LONG_DATA_THR, 1e-12) <= 0)
    {
        Config.SampleCnt = (uint64_t)(m_Param[0].SamplingFreq * m_Param[0].SamplingTime);
        Config.TrigPreCnt = (uint64_t)(m_Param[0].SamplingFreq * m_Param[0].TrigPreTime);
        Config.FrameTotal = 1;
        Config.IsLongRecv = false;
    }
    else
    {
        Config.SampleCnt = (uint64_t)(DIG_IQ_LONG_DATA_SPLIT * m_Param[0].SamplingFreq);
        Config.TrigPreCnt = 0;
        Config.FrameTotal = m_Param[0].SamplingTime / DIG_IQ_LONG_DATA_SPLIT;
        Config.IsLongRecv = true;
    }

    Config.ChannelTotal = m_DigChanList.size();
    Config.ChannelList = m_DigChanList;
    Config.Notify = std::bind(&WTVsa::ProcDigFin, this, std::placeholders::_1);
    Ret = GetDigLib().VSASetParam(Config);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set vsa dig param failed");
        return Ret;
    }

    for (int i = 0; i < m_DigChanList.size(); i++)
    {
        memset(&m_CapData[0].CalParam[i], 0, sizeof(Rx_Parm));
        m_CapParam[i] = m_Param[0];
        m_CapParam[i].Ampl = m_CapParam[i].Ampl2 = 0;
        m_CapData[0].CalParam[i].freq = m_Param[0].Freq + m_Param[0].FreqOffset;
        m_CapData[0].CalParam[i].sample_freq = m_Param[0].SamplingFreq;
        m_CapData[0].CalParam[i].rf_port = m_Param[0].RFPort;
        m_CapData[0].CalParam[i].rx_gain_parm.rx_sw_gain.final_gain = -6.94; // 将I、Q都为±(1<<14)定义为+30dBm
    }
    SetDigConfig(true);
    return WT_OK;
}

int WTVsa::SetMod(int Index)
{
    if(DigModeLib::Instance().IsDigMode())
    {
        return SetDig();
    }

    int Ret = WT_OK;
    m_AlzFlag = false;

    if (!CheckTestTypeLic(m_Param[0].Type))
    {
        return WT_LIC_NOT_EXIST;
    }
    if (Index < 0)
    {
        if (!m_Param[0].IsAC8080())
        {

            for (int i = 0; i < (signed)m_Mods.size(); i++)
            {
                memset(&m_CapData[0].CalParam[i].rx_spec_flat_comp_parm, 0, sizeof(Rx_Spec_Flat_Comp_Parm));
                VSAConfigType Config = TransConfig(m_Param[0], i);

                Ret = DevLib::Instance().VSASetConfig(m_Mods[i].ModId, Config, m_CapData[0].CalParam[i]);
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "set vsa param to mod failed");
                    return Ret;
                }
                SetActualAmpl(0);
            }
        }
        else
        {
            if (m_Mods.size() < 2)
            {
                WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "module num less than 2");
                return WT_MOD_NUM_ERROR;
            }

            for (int i = 0; i < 2; i++)
            {
                VSAConfigType Config = TransConfig(m_Param[0], i);

                Ret = DevLib::Instance().VSASetConfig(m_Mods[i].ModId, Config, m_CapData[i].CalParam[0]);
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "set vsa param to mod failed");
                    return Ret;
                }
                SetActualAmpl(i);
            }
        }
    }
    else
    {
        memset(&m_CapData[Index].CalParam[0].rx_spec_flat_comp_parm, 0, sizeof(Rx_Spec_Flat_Comp_Parm));
        VSAConfigType Config = TransConfig(m_Param[0], Index);

        Ret = DevLib::Instance().VSASetConfig(m_Mods[Index].ModId, Config, m_CapData[Index].CalParam[0]);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "set vsa param to mod failed");
            return Ret;
        }
        SetActualAmpl(Index);
    }
    //将参数发送给监视机
    SendParamToMon();

    return Ret;
}

int WTVsa::SetExtMode(int ModId)
{
    //当前都在VSG对象处理
    (void)ModId;
    return WT_OK;
}

int WTVsa::StartModDig()
{
    m_CurDataNoAlzFlag = true;
    m_UseFile = false;
    return GetDigLib().Start(DIG_STRAR_VSA);
}


int WTVsa::StartMod(int ModId)
{
    m_CurDataNoAlzFlag = true;
    int i = GetModIndex(ModId);
    if (m_Param[0].Type == TEST_80_80M_AGC)
    {
        m_CapParam[0] = m_Param[0];
    }
    else if (i < 2)
    {
        m_CapData[i].DataNum = 0;
        m_CapData[i].OriDataNum = 0;
        DevLib::Instance().WriteDirectReg(ModId, DEV_TYPE_VSA, VSA_ORIGIN_DATA_ENABLE_REG, 0);

        //保存抓取数据时所使用的参数
        if (i == 0)
        {
            for (int j = 0; j < m_ParamNum && j < MAX_NUM_OF_CHNNEL; j++)
            {
                m_CapParam[j] = m_Param[j];
                if (j > 0 && m_CapParam[j].Is160()) //非连续8080记录从机配置参数时要记录修改后的信息
                {
                    if (!m_CapParam[j].IsAC8080())
                    {
                        m_CapParam[j].Freq = m_CapParam[j].Freq - 40e6;
                        m_CapParam[j].Freq2 = m_CapParam[j].Freq + 80e6;
                        m_CapParam[j].ExtGain2 = m_CapParam[j].ExtGain;
                    }
                }
            }
        }
    }

    //初次启动时如果是多次平均则将之前的数据清除
    if ((m_AvgMode == MULTI_CNT_AVG) && (m_CapData[i].CapCnt >= m_CapNum || !m_LocalStart))
    {
        for (int j = 0; j < MAX_NUM_OF_CHNNEL; j++)
        {
            m_CapData[i].MultiData[j].clear();
        }

        m_CapData[i].CapCnt = 0;
    }

    m_UseFile = false;
    WTLog::Instance().WriteLog(LOG_CMD_TRACE, "**************WTVsa::StartMod, m_UseFile=%d, tid=%d ******\n", m_UseFile, gettid());
    return DevLib::Instance().VSAStart(ModId);
}

//MIMO从机返回启动完成的命令后主机获取从机的数据
int WTVsa::MimoStartFin()
{
    int Ret = WT_OK;

    for (auto &Conn : m_SlaveConn)
    {
        if(IsWideBandOn())
        {
            Ret = Conn->GetSlaveThreeVsaCapData();//开启正负240M频谱时，获取从机三次采集数据
        }
        else
        {
            Ret = Conn->GetVsaData();
        }
        if (Ret != WT_OK)
        {
            break;
        }
    }

    return Ret;
}

#ifdef WT418_FW
int WTVsa::SingleCapSecondPacket(int ModIndex, DataBufInfo &DataInfo)
{
    int Ret = WT_OK;

    int Offset1 = 0, Offset2 = 0;
    int Addr1 = 0; // Offset寄存器地址
    int FixedSmpCnt = 0;
    VSAConfigType Config = TransConfig(m_Param[0], ModIndex);

    /* 蜂窝采样率下, Offset为0时 有28个左右的采样点是无效的, 而240M采样率下 几乎所有采样点都有效,
        第二次采样时需要计算240M相对蜂窝采样率的Offset 保证两次采样数据基本一致 */
    if (Basefun::CompareDouble(Config.SamplingFreq, 122.88 * MHz, 0.1) == 0)
    {
        Addr1 = 0xB9 << 2;
        FixedSmpCnt = 28;
    }
    else if (Basefun::CompareDouble(Config.SamplingFreq, 61.44 * MHz, 0.1) == 0)
    {
        Addr1 = 0xBA << 2;
        FixedSmpCnt = 26;
    }
    else if (Basefun::CompareDouble(Config.SamplingFreq, 30.72 * MHz, 0.1) == 0)
    {
        Addr1 = 0xBB << 2;
        FixedSmpCnt = 24;
    }
    else
    {
        return WT_ARG_ERROR;
    }

    // 读蜂窝采样率的Offset
    if (DevLib::Instance().ReadDirectReg(m_Mods[ModIndex].ModId, DEV_TYPE_VSA, Addr1, Offset1) != WT_OK)
    {
        return Ret;
    }
    // 读240M的Offset
    if (DevLib::Instance().ReadDirectReg(m_Mods[ModIndex].ModId, DEV_TYPE_VSA, 0xB3 << 2, Offset2) != WT_OK)
    {
        return Ret;
    }

    // 计算240M相对蜂窝采样率 的额外偏移字节数
    int ExtraSmpOffset = (int)(ceil((double)(Offset1 / sizeof(int) - FixedSmpCnt) / Config.SamplingFreq * DEFAULT_SMAPLE_RATE - Offset2 / sizeof(int)) * sizeof(int));
    ExtraSmpOffset = ExtraSmpOffset / sizeof(int) * sizeof(int);

    // 采集240M原始数据 要重新配置采样率、采样点数
    Config.SamplingFreq = DEFAULT_SMAPLE_RATE;
    Ret = DevLib::Instance().VSASetCaptureOriDataSample(m_Mods[ModIndex].ModId, Config);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = GetOriCaptureData(m_Mods[ModIndex].ModId, DataInfo, ExtraSmpOffset);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    /*ofstream Out("./240M.csv", ios::out | ios::binary);
    Out << "StartData\n";
    stIQDat *Data = (stIQDat *)(DataInfo.Buf.get());
    for (int i = 0; i < round(DataInfo.DataLen  / sizeof(stIQDat)); ++i)
    {
        Out << Data[i].s16Real << "," << Data[i].s16Imag << "\n";
    }
    Out.close();*/


    return Ret;
}
#endif

bool WTVsa::SingleCapProc(int ModIndex, int Cnt)
{
    int Ret = WT_OK;
#ifdef WT418_FW
    if (m_NeedCaptureOriData)
    {
        Ret = DevLib::Instance().WriteDirectReg(m_Mods[ModIndex].ModId, DEV_TYPE_VSA, VSA_ORIGIN_DATA_ENABLE_REG, VSA_ORIGIN_DATA_ENABLE_VAl);
        if (Ret != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WriteDirectReg failed; Addr=" << VSA_ORIGIN_DATA_ENABLE_REG << ", WriteData=" << VSA_ORIGIN_DATA_ENABLE_VAl << ", Ret=" << Ret << "\n";
            return true;
        }
    }
#endif

    //80+80时不同模块的采集数据需要放在不同的地方，非80+80时则按先后次序放在一起
    int i = !m_CapParam[0].IsAC8080() ? 0 : ModIndex;
    int DataIndex = m_CapParam[0].GetTestType() != TEST_SWITCHED_MIMO ? 0 : m_CapData[i].DataNum;
    DataBufInfo &DataInfo = m_CapData[i].DataInfo[DataIndex];

    Ret = GetCaptureData(m_Mods[ModIndex].ModId, DataInfo);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }

    /*ofstream Out("./122.88M.csv", ios::out | ios::binary);
    Out << "StartData\n";
    stIQDat *Data = (stIQDat *)(DataInfo.Buf.get());
    for (int i = 0; i < round(DataInfo.DataLen  / sizeof(stIQDat)); ++i)
    {
        Out << Data[i].s16Real << "," << Data[i].s16Imag << "\n";
    }
    Out.close();*/

#ifdef WT418_FW
    if (m_NeedCaptureOriData)
    {
        DataIndex = m_CapParam[0].GetTestType() != TEST_SWITCHED_MIMO ? 0 : m_CapData[i].OriDataNum;
        DataBufInfo &OriDataInfo = m_CapData[i].OriDataInfo[DataIndex];
        Ret = DevLib::Instance().WriteDirectReg(m_Mods[ModIndex].ModId, DEV_TYPE_VSA, VSA_ORIGIN_DATA_ENABLE_REG, VSA_ORIGIN_DATA_DISABLE_VAl);
        if (Ret != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WriteDirectReg failed; Addr=" << VSA_ORIGIN_DATA_ENABLE_REG << ", WriteData=" << VSA_ORIGIN_DATA_DISABLE_VAl << ", Ret=" << Ret << "\n";
            return true;
        }

        Ret = SingleCapSecondPacket(ModIndex, OriDataInfo); // 采集第二包数据(240M采样率的原始数据)
        if (Ret != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SingleCapSecondPacket failed; Ret=" << Ret << "\n";
        }
    }
#endif

    bool Release = true;

    m_CapData[i].DataNum++;
    m_CapData[i].OriDataNum++;

    //所有模块都完成才返回
    if (m_Mods.size() == Cnt)
    {
        //需要等待所有从机数据都收到后才能返回
        if (m_CapParam[0].GetTestType() != TEST_SWITCHED_MIMO)
        {
            Release = true;
            if (m_CapData[i].DataNum == 1 + m_SlaveConn.size())
            {
                m_RunStatus = MOD_RUN_FINISH;
            }
        }
        else
        {
            //switched MIMO模式下需要采集完所有的才能返回
            if (m_CapData[i].DataNum < m_ParamNum)
            {
                Release = false;
                Ret = StartModWithParam(m_CapData[i].DataNum);
                if (Ret != WT_OK)
                {
                    m_RunStatus = Ret;
                    Release = true;
                }
            }
            else
            {
                Release = true;
                m_RunStatus = MOD_RUN_FINISH;
            }
        }
    }

    return Release;
}

bool WTVsa::MultiCapProc(int ModIndex, int Cnt)
{
    //80+80时不同模块的采集数据需要放在不同的地方，非80+80时则按先后次序放在一起
    int i = !m_CapParam[0].IsAC8080() ? 0 : ModIndex;

    DataBufInfo &DataInfo = m_CapData[i].DataInfo[0];

    int Ret = GetCaptureData(m_Mods[ModIndex].ModId, DataInfo);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }

    //多次采集平均模式下需要将数据放到队列中
    m_CapData[i].MultiData[0].push_back(move(DataInfo));
    m_CapData[i].DataNum++;

    bool Complete = false;  //本次采集是否完成
    if (m_CapData[i].DataNum == 1 + m_SlaveConn.size())  //所有天线都采集完成才算一次完整采集完成
    {
        m_CapData[i].CapCnt++;
        Complete = m_Mods.size() == Cnt;
    }

    //滑动平均模式下采集完成即可返回, 多次平均需要等到采集到指定次数后才能返回
    if (m_AvgMode == SLIDE_AVG || m_CapData[i].CapCnt >= m_CapNum)
    {
        if (Complete)
        {
            m_RunStatus = MOD_RUN_FINISH;
        }

        return true;
    }
    else if (Complete)
    {
        StartAgain();
        return false;
    }
    else
    {
        return m_CapData[i].MultiData[0].size() >= m_CapNum;
    }
}

bool WTVsa::NrWideBandCapProc(int ModIndex, int Cnt) //宽频一次采集时，该函数会进来4次，中间频谱下采样数据+中间频谱原始数据+左右各一次的原始数据
{
    (void)Cnt;
    int Seg = 0; // 蜂窝中当前不支持segment，Seg填0
    DataBufInfo DataInfo;
    DataBufInfo OriDataInfo;
    int Ret;
    double FreqOffset = 0.0;
    string Val;

    if (DevConf::Instance().GetItemVal("FrqOffset", Val) == WT_OK)
    {
        CurSpectWideOffset = atof(Val.c_str());
        WTLog::Instance().WriteLog(LOG_DEBUG, "NrWideBandCapProc CurSpectWideOffset=%f\n", CurSpectWideOffset);
    }

    if(ThreeData[Seg][0][1].size() >= NR_WIDE_BAND_MAX_CAP_CNT) //第三维，第二项放的是原始数据，总共大小是3
    {
        ThreeData[Seg][0][0].clear();
        ThreeData[Seg][0][1].clear();
        CalParam[Seg][0].clear();
    }

    Ret = DevLib::Instance().WriteDirectReg(m_Mods[ModIndex].ModId, DEV_TYPE_VSA, VSA_ORIGIN_DATA_ENABLE_REG, VSA_ORIGIN_DATA_ENABLE_VAl);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }

    Ret = GetCaptureData(m_Mods[ModIndex].ModId, DataInfo);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }

    Ret = DevLib::Instance().WriteDirectReg(m_Mods[ModIndex].ModId, DEV_TYPE_VSA, VSA_ORIGIN_DATA_ENABLE_REG, VSA_ORIGIN_DATA_DISABLE_VAl);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }
    Ret = SingleCapSecondPacket(ModIndex, OriDataInfo);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }

    if (ThreeData[Seg][0][0].size() == 0)
    { //第三维，第一项放的是下采样数据，只有中间频需要下采样数据
        ThreeData[Seg][0][0].push_back(move(DataInfo));
        ThreeData[Seg][0][1].push_back(move(OriDataInfo));
        if (50*1e6 <= m_Alg.GetAlzParamNrAlzBand() &&   m_Alg.GetAlzParamNrAlzBand() <= 70*1e6)
        {
            m_CapData[Seg].DataNum++;
            m_RunStatus = MOD_RUN_FINISH;
            CalParam[Seg][0].push_back(m_CapData[Seg].CalParam[0]);
            memcpy(&BakParam, &m_Param[0], sizeof(VsaParam));
            memcpy(&BakCapParam, &m_CapParam[0], sizeof(VsaParam));
            return true;
        }
    }
    else
    {
        ThreeData[Seg][0][1].push_back(move(OriDataInfo)); //存放原始数据
    }
    CalParam[Seg][0].push_back(m_CapData[Seg].CalParam[0]);

    if(0 < ThreeData[Seg][0][1].size() && ThreeData[Seg][0][1].size() < NR_WIDE_BAND_MAX_CAP_CNT)
    {
        //先备份原始采集参数,只备份一次
        if(ThreeData[Seg][0][1].size() == 1)
        {
            memcpy(&BakParam, &m_Param[0], sizeof(VsaParam));
            memcpy(&BakCapParam, &m_CapParam[0], sizeof(VsaParam));
        }

        m_Param[0].TrigType = WT_TRIG_TYPE_FREE_RUN;
        m_Param[0].Type = TEST_SISO;

        if (ThreeData[Seg][0][1].size() == 1) //采集完中心频点的240M后，采集左边240M数据，偏移第一次
        {
            FreqOffset -= CurSpectWideOffset;
        }
        else //采集右边240M数据
        {
            FreqOffset += CurSpectWideOffset;
        }
        m_Param[0].Freq = BakParam.Freq + FreqOffset;

        Ret = StartModWithParam(0);
        if(Ret != WT_OK)
        {
            m_RunStatus = Ret;
            return true;
        }
        else
        {
            //左右两边采集时，增加超时机制，规避同一个板两个模块执行重叠冲突问题
            AddAGCWaitTrigger(m_Param[0].MaxIFG + m_Param[0].SamplingTime*2, ModIndex);
            return false;
        }
    }
    else  //中间频以及左右两频数据采集完成
    {
        m_CapData[Seg].DataNum++;
        if(m_CapNum <= 1) //single capture
        {
            //拷贝中间采集的正常数据到原来的m_CapData中，方便获取原始数据使用
            m_CapData[Seg].DataInfo[0].BufLen = ThreeData[Seg][0][0][0].BufLen;
            m_CapData[Seg].DataInfo[0].DataLen = ThreeData[Seg][0][0][0].BufLen;
            m_CapData[Seg].DataInfo[0].Buf.reset(new(std::nothrow) char[m_CapData[Seg].DataInfo[0].BufLen]);
            memcpy(m_CapData[Seg].DataInfo[0].Buf.get(), ThreeData[Seg][0][0][0].Buf.get(), m_CapData[Seg].DataInfo[0].BufLen);
            m_CapData[Seg].CalParam[0] = CalParam[Seg][0][0];
            m_RunStatus = MOD_RUN_FINISH;
            return true;
        }
        else
        {
            DataBufInfo TmpDataInfo;
            TmpDataInfo.BufLen = ThreeData[Seg][0][0][0].BufLen;
            TmpDataInfo.Buf.reset(new(std::nothrow) char[TmpDataInfo.BufLen]);
            memcpy(TmpDataInfo.Buf.get(), ThreeData[Seg][0][0][0].Buf.get(), TmpDataInfo.BufLen);
            m_CapData[Seg].MultiData[0].push_back(move(TmpDataInfo));

            m_CapData[Seg].CapCnt++;
            //滑动平均模式下采集完成即可返回, 多次平均需要等到采集到指定次数后才能返回
            if (m_AvgMode == SLIDE_AVG || m_CapData[Seg].CapCnt >= m_CapNum)
            {
                m_RunStatus = MOD_RUN_FINISH;
                return true;
            }
            else
            {
                //多次算数平均，完成一次500M采集，恢复原来的采集参数
                memcpy(&m_Param[0], &BakParam, sizeof(VsaParam));
                memcpy(&m_CapParam[0], &BakCapParam, sizeof(VsaParam));

                //把恢复的配置参数配置到硬件
                SetMod();

                StartAgain();
                return false;
            }
        }
    }
}

bool WTVsa::ThreeCapProc(int ModIndex, int Cnt)
{
    //80+80时不同模块的采集数据需要放在不同的地方，非80+80时则按先后次序放在一起
    int i = !m_CapParam[0].IsAC8080() ? 0 : ModIndex;
    //由于目前最大采集240M，组成500M频谱一次需要WIDE_BAND_MAX_CAP_CNT组数据，大于等于WIDE_BAND_MAX_CAP_CNT时，表示新的一次采集
    if(ThreeData[i][0][0].size() >= WIDE_BAND_MAX_CAP_CNT)
    {
        ThreeData[i][0][0].clear();
        CalParam[i][0].clear();
    }

    DataBufInfo DataInfo;

    int Ret = GetCaptureData(m_Mods[ModIndex].ModId, DataInfo);
    if (Ret != WT_OK)
    {
        m_RunStatus = Ret;
        return true;
    }

    ThreeData[i][0][0].push_back(move(DataInfo));
    CalParam[i][0].push_back(m_CapData[i].CalParam[0]);

    //vector ThreeData[i][0]的size最大为WIDE_BAND_MAX_CAP_CNT，vector下标0代表中间正常采集的数据，下标1代表左边采集的数据，下标2代表右边采集的数据
    if(0 < ThreeData[i][0][0].size() && ThreeData[i][0][0].size() < WIDE_BAND_MAX_CAP_CNT)  //准备左边或者右边的采集
    {
        //先备份原始采集参数,只备份一次
        if(ThreeData[i][0][0].size() == 1)
        {
            memcpy(&BakParam, &m_Param[0], sizeof(VsaParam));
            memcpy(&BakCapParam, &m_CapParam[0], sizeof(VsaParam));
        }

        if (m_Mods.size() == Cnt)//8080需要两个模块都完成才进行下一次
        {
            //规避杂散问题：Wide Mode时，中间频点的频谱取+-100MHz的数据，两边频谱需偏移200MHz抓取，也从+-100MHz位置开始取频谱值
            //左右采集时，配置新的采集参数，使用freerun，测试模式转为siso（模块内部单独重采）采集长度为200us，指定的中心频点左右偏移200M采集
            m_Param[0].TrigType = WT_TRIG_TYPE_FREE_RUN;
            m_Param[0].SamplingTime = CAP_TIME_S; //500us
            //m_Param[0].TrigPreTime = 0.00001; //前置时间固定20us，不能比采样时间长
            m_Param[0].Type = TEST_SISO;
            double FreqOffset = 0.0;


            if(IsWideBandSpectEnable == 1 || (IsWideBandSpectEnable == 2 && GetCurBandwidth() == 320))
            {
                if(ThreeData[i][0][0].size() == 1)//采集完中心频点的240M后，采集左边240M数据，偏移第一次
                {
                        FreqOffset -= CAP_FREQ_OFFSET;
                        CurSpectWideOffset = CAP_FREQ_OFFSET;
                }
                else if(ThreeData[i][0][0].size() == 2) // 采集右边240M数据
                {
                        FreqOffset += CAP_FREQ_OFFSET;
                        CurSpectWideOffset = CAP_FREQ_OFFSET;
                }
                else if(ThreeData[i][0][0].size() == 3)//采集完中心频点的240M后，采集左边240M数据，偏移第二次
                {
                        FreqOffset -= CAP_FREQ_OFFSET1;
                        CurSpectWideOffset = CAP_FREQ_OFFSET1;
                }
                else // 采集右边240M数据
                {
                        FreqOffset += CAP_FREQ_OFFSET1;
                        CurSpectWideOffset = CAP_FREQ_OFFSET1;
                }
            }

            //WTLog::Instance().WriteLog(LOG_DEBUG, "CurSpectWideOffset=%lf\n", CurSpectWideOffset);

            m_Param[0].Freq = BakParam.Freq + FreqOffset;
            if(m_Param[0].Freq2 > 0)
            {
                m_Param[0].Freq2 = BakParam.Freq2 + FreqOffset;
            }

            //配置新的采集参数到硬件，并启动采集
            Ret = StartModWithParam(0);
            if(Ret != WT_OK)
            {
                m_RunStatus = Ret;
                return true;
            }
            else
            {
                //左右两边采集时，增加超时机制，规避同一个板两个模块执行重叠冲突问题
                AddAGCWaitTrigger(m_Param[0].MaxIFG + m_Param[0].SamplingTime*2, ModIndex);
                return false;
            }
        }
        return false;
    }
    else    //500M的三组数据采集完成，ThreeData[i][0].size() == WIDE_BAND_MAX_CAP_CNT
    {
        m_CapData[i].DataNum++; //一次采集完成
        if(m_CapNum <= 1)       //single capture
        {
            //拷贝中间采集的正常数据到原来的m_CapData中，方便获取原始数据使用,三次采集的buflen和datalen有点混用了，后续需要优化改掉~
            m_CapData[i].DataInfo[0].BufLen = ThreeData[i][0][0][0].BufLen;
            m_CapData[i].DataInfo[0].DataLen = ThreeData[i][0][0][0].BufLen;
            m_CapData[i].DataInfo[0].Buf.reset(new(std::nothrow) char[m_CapData[i].DataInfo[0].BufLen]);
            memcpy(m_CapData[i].DataInfo[0].Buf.get(), ThreeData[i][0][0][0].Buf.get(), m_CapData[i].DataInfo[0].BufLen);
            m_CapData[i].CalParam[0] = CalParam[i][0][0];

            //所有模块都完成才返回
            bool Release = true;
            if (m_Mods.size() == Cnt)
            {
                //需要等待所有从机数据都收到后才能返回
                if (m_CapParam[0].GetTestType() != TEST_SWITCHED_MIMO)
                {
                    Release = true;
                    if (m_CapData[i].DataNum == 1 + m_SlaveConn.size())
                    {
                        m_RunStatus = MOD_RUN_FINISH;
                    }
                }
                else
                {
                    //switched MIMO模式下需要采集完所有的才能返回
                    if (m_CapData[i].DataNum < m_ParamNum)
                    {
                        Release = false;
                        Ret = StartModWithParam(m_CapData[i].DataNum);
                        if (Ret != WT_OK)
                        {
                            m_RunStatus = Ret;
                            Release = true;
                        }
                    }
                    else
                    {
                        Release = true;
                        m_RunStatus = MOD_RUN_FINISH;
                    }
                }
            }
            return Release;
        }
        else    //Multi capture
        {
            //多次采集平均模式下需要将数据放到队列中
            DataBufInfo TmpDataInfo;
            TmpDataInfo.BufLen = ThreeData[i][0][0][0].BufLen;
            TmpDataInfo.DataLen = ThreeData[i][0][0][0].BufLen;
            TmpDataInfo.Buf.reset(new(std::nothrow) char[TmpDataInfo.BufLen]);
            memcpy(TmpDataInfo.Buf.get(), ThreeData[i][0][0][0].Buf.get(), TmpDataInfo.BufLen);
            m_CapData[i].MultiData[0].push_back(move(TmpDataInfo));

            bool Complete = false;  //本次采集是否完成
            if (m_CapData[i].DataNum == 1 + m_SlaveConn.size())  //所有天线都采集完成才算一次完整采集完成
            {
                m_CapData[i].CapCnt++;
                Complete = m_Mods.size() == Cnt;
            }

            //滑动平均模式下采集完成即可返回, 多次平均需要等到采集到指定次数后才能返回
            if (m_AvgMode == SLIDE_AVG || m_CapData[i].CapCnt >= m_CapNum)
            {
                if (Complete)
                {
                    m_RunStatus = MOD_RUN_FINISH;
                }

                return true;
            }
            else if (Complete)
            {
                //多次算数平均，完成一次500M采集，恢复原来的采集参数
                memcpy(&m_Param[0], &BakParam, sizeof(VsaParam));
                memcpy(&m_CapParam[0], &BakCapParam, sizeof(VsaParam));

                //把恢复的配置参数配置到硬件
                SetMod();

                StartAgain();
                return false;
            }
            else
            {
                return m_CapData[i].MultiData[0].size() >= m_CapNum;
            }
        }
    }
}

// 数字IQ操作完成事件回掉函数
void WTVsa::ProcDigFin(int Status)
{
    int Ret = WT_OK;

    for (auto Ch : m_DigChanList)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "======WTVsa::ProcDigFin Ch=%d\n", Ch);
    }

    if (Status == WT_DIGTIAL_STATUS_DONE)
    {
        // 不是超长包时才获取数据，超长包时直接保存文件到本地
        if (Basefun::CompareDouble(m_Param[0].SamplingTime, DIG_IQ_LONG_DATA_THR, 1e-12) <= 0)
        {
            for (int i = 0; i < m_DigChanList.size(); i++)
            {
                DataBufInfo &DataInfo = m_CapData[0].DataInfo[i];

                // 如果信号buffer长度小于信号数据大小则重新申请内存
                int DateSize = GetDigLib().VSAGetChannelDataLen();

                if (DataInfo.BufLen < DateSize || DataInfo.Buf == nullptr)
                {
                    DataInfo.Buf.reset(new (std::nothrow) char[DateSize]);
                    if (DataInfo.Buf == nullptr)
                    {
                        DataInfo.BufLen = 0;
                        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
                        m_RunStatus = WT_RX_TX_STATE_ERR_DONE;
                        return;
                    }
                    DataInfo.BufLen = DateSize;
                }

                Ret = GetDigLib().CaptureData(DataInfo.Buf.get(), DateSize, m_DigChanList[i], m_DigChanList.size());
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Status, "vsa dig iq CaptureData error");
                    m_RunStatus = WT_RX_TX_STATE_ERR_DONE;
                    break;
                }
                DataInfo.DataLen = DateSize;
            }
        }

        m_CapData[0].DataNum = m_DigChanList.size();
        m_RunStatus = MOD_RUN_FINISH;

        if (GetDigLib().GetTBTApSIFS(m_TBTSIFS) == WT_OK)
        {
            m_Alg.CopySIFS(m_TBTSIFS);
        }
    }
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WTVsa::ProcDigFin stauts = %d\n", Status);
        WTLog::Instance().LOGERR(Status, "vsa dig iq error");
        m_RunStatus = WT_VSA_CAPTURE_FAILED;
    }
}

//数据采集完成处理函数，从硬件获取数据，并通知外部连接数据采集已完成，
//如果是MIMO模式则需要所有的从机都已采集完成才能通知外部连接。
bool WTVsa::ProcModFin(int Id, int Cnt)
{
    m_HwOpFinPolling = 0;

    if (m_FinAgent != nullptr)
    {
        return m_FinAgent(Id);
    }
    //AGC期间单元完成后不能将ATT配置到最大，否则主路完成后会将8080的共用ATT设置到63，导致从路信号衰减不正确。
    DevLib::Instance().VSAFinish(Id);
    //不管是否采集成功都清除已经分析的VSA数据
    m_Alg.Clear();
    bool Release = true;
    int i = GetModIndex(Id);
    int Status = m_Mods[i].Status;

    //通知WaitTrig，消除超时计时，本单元已经采集完成
    DeleteAGCWaitTrigger(i);

    if (Status == WT_RX_TX_STATE_DONE)
    {
        if(IsWideBandOn() || (ALG_3GPP_STD_5G == m_Alg.GetAlzParamDemode() && (m_Alg.GetAlzParamNrAlzBand() >= 50*1e6))) // && m_CurExtDemode == WT_ALZ_PARAM_WIFI
        {
            if (ALG_3GPP_STD_5G == m_Alg.GetAlzParamDemode())
            {
                Release = NrWideBandCapProc(i, Cnt);
            }
            else
            {
                Release = ThreeCapProc(i, Cnt);
            }

            if(Release == true && ThreeData[i][0][0].size() != 0)//一次完整500M采集完成（第一次采集就失败返回时不用恢复参数）
            {
                //恢复原来的采集参数
                memcpy(&m_Param[0], &BakParam, sizeof(VsaParam));
                memcpy(&m_CapParam[0], &BakCapParam, sizeof(VsaParam));

                //把恢复的配置参数配置到硬件
                SetMod();
            }
        }
        else
        {
            if (m_CapNum <= 1)
            {
                Release = SingleCapProc(i, Cnt);
            }
            else
            {
                Release = MultiCapProc(i, Cnt);
            }
        }
    }
    else if(IsWideBandOn() && ThreeData[i][0][0].size() > 0 && Status == WT_RX_TX_STATE_TIMEOUT && m_Param[0].TrigType == WT_TRIG_TYPE_FREE_RUN)
    {
        WTLog::Instance().LOGOPERATE("Dma time out and restart.");
        StartModWithParam(0);   //左右free采集失败时重新开启一次
        return false;
    }
    else
    {
        WTLog::Instance().LOGERR(Status, "capture data failed");
        m_RunStatus = WT_VSA_CAPTURE_FAILED;
    }

    return Release;
}

void WTVsa::ClearMod(int NeedFreeCnt)
{
    if (NeedFreeCnt == -1) // NeedFreeCnt=-1, 表示TBT模式，VSG已完成，通知VSA释放资源。
    {
        if (GetVsg().IsTBTModeFinish()) // VSA/VSG都完成, 释放VSG VSA资源
        {
            for (auto &Mod : m_Mods)
            {
                DevLib::Instance().ClearExtMode(Mod.ModId);
            }
            GetVsa().GetModInfo()->clear();
            GetVsg().GetModInfo()->clear();
            GetVsa().ClearTBTMode();                //清除模式标记
            GetVsg().ClearTBTMode();
        }
    }
    else if (NeedFreeCnt == m_Mods.size()) //判断模块是否全部完成
    {
        if (!IsTBTMode()) //非TBT模式时，释放资源。
        {
            m_Mods.clear();
        }
        else // TBT模式
        {
            GetVsg().SetTBTModeVsaFinish(); //设置完成标记
            GetVsg().ClearMod(-1);         //通知VSG，VSA/VSG都完成后才能释放VSA资源。
        }
    }
}

#ifdef WT418_FW
int WTVsa::GetOriCaptureData(int ModId, DataBufInfo &DataInfo, int ExtraSmpOffset)
{
    int DateSize = round(m_CapParam[0].SamplingTime * DEFAULT_SMAPLE_RATE * sizeof(short) * 2);
    // 如果信号buffer长度小于信号数据大小则重新申请内存
    if (DataInfo.BufLen < DateSize || DataInfo.Buf == nullptr)
    {
        DataInfo.Buf.reset(new(std::nothrow) char[DateSize]);
        if (DataInfo.Buf == nullptr)
        {
            DataInfo.BufLen = 0;
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
            return WT_ALLOC_FAILED;
        }

        DataInfo.BufLen = DateSize;
    }

    int Ret = DevLib::Instance().VSACaptureOriData(ModId, DataInfo.Buf.get(), DateSize, ExtraSmpOffset);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get capture data failed");
        return Ret;
    }

    if (IsTBTMode())
    {
        int Ret = DevLib::Instance().VSAGetResultSIFS(ModId, m_TBTSIFS);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "get capture data failed");
            return Ret;
        }
        m_Alg.CopySIFS(m_TBTSIFS);
    }

    DataInfo.DataLen = DateSize;

    return WT_OK;
}
#endif

int WTVsa::GetCaptureData(int ModId, DataBufInfo &DataInfo)
{
    int DateSize = m_CapParam[0].GetSigLen();
    // 如果信号buffer长度小于信号数据大小则重新申请内存
    if (DataInfo.BufLen < DateSize || DataInfo.Buf == nullptr)
    {
        DataInfo.Buf.reset(new(std::nothrow) char[DateSize]);
        if (DataInfo.Buf == nullptr)
        {
            DataInfo.BufLen = 0;
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
            return WT_ALLOC_FAILED;
        }

        DataInfo.BufLen = DateSize;
    }

    int Ret = DevLib::Instance().VSACaptureData(ModId, DataInfo.Buf.get(), DateSize);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get capture data failed");
        return Ret;
    }

    if (IsTBTMode())
    {
        int Ret = DevLib::Instance().VSAGetResultSIFS(ModId, m_TBTSIFS);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "get capture data failed");
            return Ret;
        }
        if(IsTBTStaMode())//TBTSta的第一帧SIFS固定为0；
        {
            m_TBTSIFS[0] = 0.0;
        }
        m_Alg.CopySIFS(m_TBTSIFS);
    }

    DataInfo.DataLen = DateSize;

    return WT_OK;
}

int WTVsa::GetAlzParamByDemode(int Demode, std::unique_ptr<char[]> &ParamData, int &Len)
{
    int Size = sizeof(VsaAlzParam);
    std::unique_ptr<char[]> TmpAlzParam;
    TmpAlzParam.reset(new(std::nothrow) char[Size]);

    if (TmpAlzParam == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "get alzparam alloc buffer failed");
        return WT_ALLOC_FAILED;
    }

    const void *AlzParam = nullptr;
    void *Buf = TmpAlzParam.get();
    int ParamLen = 0;
    int Ret = GetAlzParam(&AlzParam, ParamLen);
    VsaAlzParam AlzParamData;

    memset((void *)&AlzParamData, 0, sizeof(VsaAlzParam));
    memcpy(&AlzParamData, (char *)AlzParam, sizeof(VsaAlzParam));

    if(AlzParam != nullptr && Ret == WT_OK)
    {
        *(int *)Buf = WT_ALZ_PARAM_COMMON;
        Len += sizeof(int);
        memcpy(((char *)Buf + sizeof(int)), &AlzParamData.CommParam, sizeof(AlzParamComm));
        Len += sizeof(AlzParamComm);

        if(Demode != WT_ALZ_PARAM_COMMON)
        {
            *(int *)((char *)Buf + Len) = Demode;
            Len += sizeof(int);
            if(Demode == WT_ALZ_PARAM_FFT)
            {
                memcpy(((char *)Buf + Len), &AlzParamData.FFTParam, sizeof(AlzParamFFT));
                Len += sizeof(AlzParamFFT);
            }
            else if(Demode == WT_ALZ_PARAM_WIFI)
            {
                memcpy(((char *)Buf + Len), &AlzParamData.WifiParam, sizeof(AlzParamWifi));
                Len += sizeof(AlzParamWifi);
            }
            else if(Demode == WT_ALZ_PARAM_BT)
            {
                memcpy(((char *)Buf + Len), &AlzParamData.BTParam, sizeof(AlzParamBT));
                Len += sizeof(AlzParamBT);
            }
            else if(Demode == WT_ALZ_PARAM_ZIGBEE)
            {
                memcpy(((char *)Buf + Len), &AlzParamData.ZigBeeParam, sizeof(AlzParamZigBee));
                Len += sizeof(AlzParamZigBee);
            }
            else if(Demode == WT_ALZ_PARAM_ZWAVE)
            {
                memcpy(((char *)Buf + Len), &AlzParamData.ZwaveParam, sizeof(AlzParamZwave));
                Len += sizeof(AlzParamZwave);
            }
            else if(Demode == WT_ALZ_PARAM_GLE)
            {
                memcpy(((char *)Buf + Len), &AlzParamData.AnalyzeParamSparkLink, sizeof(AlzParamSparkLink));
                Len += sizeof(AlzParamSparkLink);
            }
        }
        ParamData.reset(TmpAlzParam.release());
        Buf = nullptr;
    }
    return Ret;
}

void WTVsa::SendAlzParamToMon(int Demode)
{
    //发送分析参数到相应的监视机
    list<shared_ptr<Monitor>> Mons;
    MonitorMgr::Instance().GetMonitors(Mons);
    int ParamLen = 0;
    std::unique_ptr<char[]> AlzParamBuf;

    if (m_Param == nullptr)
    {
        return ;
    }

    int Ret = GetAlzParamByDemode(Demode, AlzParamBuf, ParamLen);

    if(AlzParamBuf != nullptr && Ret == WT_OK)
    {
        for (auto &Mon : Mons)
        {
            if (Mon->IsMonPort(m_Param[0].RFPort))
            {
                Mon->SendVsaAlzParam(AlzParamBuf.get(), ParamLen);
            }
        }
    }
}

int WTVsa::ClearAvgData(int Flag)
{
    int Ret = WT_OK;
    if(Flag == false)
    {
        for(auto &CapData: m_CapData)
        {
            CapData.CapCnt = 0;
        }
        m_Alg.ClearAvgData();

        //把数据也清空下
        for (int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            for(auto &CapData: m_CapData)
            {
                CapData.MultiData[i].clear();
            }
        }
    }
    else
    {
        m_Alg.ClearAvgData();
        for(auto &CapData: m_CapData)
        {
            CapData.CapCnt = 1;
        }
        for (int j = 0; j < MAX_NUM_OF_CHNNEL; j++)
        {
            for(auto &CapData: m_CapData)
            {
                CapData.MultiData[j].erase(CapData.MultiData[j].begin(),--CapData.MultiData[j].end());
            }
        }
    }
    m_NeedClearAvgData = false;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<__FUNCTION__<<":Ret = "<<Ret<<std::endl;
    return Ret;
}

int WTVsa::SetAlzParam(int Type, void *Param, int Len)
{
    int ParamLen = 0;
    VsaAlzParam *AlzParam;
    const void **Data = (const void **)(&AlzParam);
    m_Alg.GetAlzParam(Data, ParamLen);
    if(m_Alg.IsAlzParamChanged(Type, Param, Len))
    {
        m_NeedClearAvgData = true;   //当下发的分析改变时，清空平均数据
    }
    int Ret = m_Alg.SetAlzParam(Type, Param, Len);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "alzparam error");
        return Ret;
    }

    SendAlzParamToMon(Type);
    m_CurExtDemode = Type;
    return WT_OK;
}

int WTVsa::GetAlzParam(const void **Param, int &Len)
{
    return m_Alg.GetAlzParam(Param, Len);
}

int WTVsa::AdjustCapParam(bool HasFrame, double Power, double PeakPower, double MinPower)
{
    if (!m_AutoRange->ChecckPowerPeak(Power, PeakPower, m_CapData[0].CalParam[0].rx_gain_parm.rx_sw_gain.final_gain, HasFrame))
    {
#ifdef DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Master frame " << HasFrame << ", peak power " << PeakPower
             << ", again ref " << m_Param[0].Ampl << " ********************" << endl;
#endif
        return WT_OK;
    }

    m_AutoRange->AdjustRefRangeExtGain(m_Param[0].ExtGain);

    double RefLevel = m_Param[0].Ampl;
    int Demode = m_Param[0].GetDemode();
    int Ret = m_AutoRange->RecordCurLevel(RefLevel, Power, PeakPower, MinPower, HasFrame, m_Param[0].TrigType, Demode);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "reference level error");
        return Ret;
    }

    if(m_AutoRange->IsRefLevelpass())
    {
        m_AutoRange->CheckCompleteState(m_Param[0].TrigType, HasFrame, m_Param[0].TrigLevel);
    }
    else if (HasFrame)
    {
        RefLevel = m_AutoRange->AdjustRefLevel(PeakPower, HasFrame, Demode);
        m_Param[0].TrigLevel = m_AutoRange->AdjustTrigLevel();
    }
    else
    {
        //当前挡位之前能获取到帧，但是更改参考电平后无法获取到帧，此时需要更改trig level
        if (m_AutoRange->RangeHasFrame() && m_AutoRange->NeedValidTrigLevel())
        {
            m_Param[0].TrigLevel = m_AutoRange->AdjustTrigLevel();
        }
        else if (m_AutoRange->IsBoundRef()) //参考电平达到上下限
        {
            do
            {
                if (m_AutoRange->NeedValidTrigLevel())
                {
                    double RefTrigLevel = m_AutoRange->AdjustTrigLevel();
                    if (Basefun::CompareDouble(m_Param[0].TrigLevel, RefTrigLevel) != 0)
                    {
                        m_Param[0].TrigLevel = RefTrigLevel;
                        break;
                    }
                }
                //达到最大或最小电平时如果已经遍历了所有挡位则找出差异最小的档位作为参考功率，否则使用没测试过的挡位
                RefLevel = m_AutoRange->GetUnusedRef();
            } while (0);
        }
        else //切换挡位
        {
            RefLevel = m_AutoRange->AdjustRefRange(PeakPower, HasFrame, Demode);
        }

        //有可能是由于信号帧特别长，导致AGC时找不到帧，但采集时（超时时间与采集时间更长）仍可能触发到信号。
        //所以调整触发电平仍有意义，在没有帧功率的情况下，也要再调整触发电平，
        //不能在SetActualAmpl函数里恢复，避免恢复AGC参数时配置的触发电平与response的触发电平不一致。
        if(m_AutoRange->IsRefLevelpass() && m_AutoRange->GetUserTrigType() != WT_TRIG_TYPE_FREE_RUN)
        {
            m_Param[0].TrigLevel = m_AutoRange->AdjustBstTrigLevel();
        }
    }

    #ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Master frame " << HasFrame << ", peak power " << PeakPower
         << ", last ref " << m_Param[0].Ampl << ", new ref " << RefLevel << " ********************" << endl;
    #endif

    m_Param[0].Ampl = RefLevel;
    m_Param[0].TrigType = WT_TRIG_TYPE_IF;

    return WT_OK;
}

//80+80双端口模式时，对从机AGC过程的处理。
int WTVsa::AdjustCapParam2(bool HasFrame, double Power, double PeakPower, double MinPower)
{
    if (!m_AutoRange2->ChecckPowerPeak(Power, PeakPower, m_CapData[1].CalParam[0].rx_gain_parm.rx_sw_gain.final_gain, HasFrame))
    {
#ifdef DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Slave frame " << HasFrame << ", peak power " << PeakPower
             << ", again ref 2: " << m_Param[0].Ampl2 << " ********************" << endl;
#endif
        return WT_OK;
    }

    m_AutoRange2->AdjustRefRangeExtGain(m_Param[0].ExtGain2);

    double RefLevel = m_Param[0].Ampl2;
    int Demode = m_Param[0].GetDemode();
    int Ret = m_AutoRange2->RecordCurLevel(RefLevel, Power, PeakPower, MinPower, HasFrame, m_TrigType2, Demode);

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "reference level error");
        return Ret;
    }
    if(m_AutoRange2->IsRefLevelpass())
    {
        m_AutoRange2->CheckCompleteState(m_TrigType2, HasFrame, m_Param[0].TrigLevel2);
    }
    else if (HasFrame)
    {
        RefLevel = m_AutoRange2->AdjustRefLevel(PeakPower, HasFrame, Demode);
        m_Param[0].TrigLevel2 = m_AutoRange2->AdjustTrigLevel();
    }
    else
    {
        // 当前挡位之前能获取到帧，但是更改参考电平后无法获取到帧，此时需要更改trig level
        if (m_AutoRange2->RangeHasFrame() && m_AutoRange2->NeedValidTrigLevel())
        {
            m_Param[0].TrigLevel2 = m_AutoRange2->AdjustTrigLevel();
        }
        else if (m_AutoRange2->IsBoundRef()) //参考电平达到上下限
        {
            do
            {
                if (m_AutoRange2->NeedValidTrigLevel())
                {
                    double RefTrigLevel = m_AutoRange2->AdjustTrigLevel();
                    if (Basefun::CompareDouble(m_Param[0].TrigLevel2, RefTrigLevel) != 0)
                    {
                        m_Param[0].TrigLevel2 = RefTrigLevel;
                        break;
                    }
                }
                //达到最大或最小电平时如果已经遍历了所有挡位则找出差异最小的档位作为参考功率，否则使用没测试过的挡位
                RefLevel = m_AutoRange2->GetUnusedRef();
            } while (0);
        }
        else //切换挡位
        {
            RefLevel = m_AutoRange2->AdjustRefRange(PeakPower, HasFrame, Demode);
        }

        //有可能是由于信号帧特别长，导致AGC时找不到帧，但采集时（超时时间与采集时间更长）仍可能触发到信号。
        //所以调整触发电平仍有意义，在没有帧功率的情况下，也要再调整触发电平，
        //不能在SetActualAmpl函数里恢复，避免恢复AGC参数时配置的触发电平与response的触发电平不一致。
        if(m_AutoRange2->IsRefLevelpass() && m_AutoRange2->GetUserTrigType() != WT_TRIG_TYPE_FREE_RUN)
        {
            m_Param[0].TrigLevel2 = m_AutoRange2->AdjustBstTrigLevel();
        }
    }

    #ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Slave frame " << HasFrame << ", peak power " << PeakPower
         << ", last ref 2：" << m_Param[0].Ampl2 << ", new ref " << RefLevel << " ********************" << endl;
    #endif

    m_Param[0].Ampl2 = RefLevel;
    m_TrigType2 = WT_TRIG_TYPE_IF;

    return WT_OK;
}

// 主机(从机的SISO)失败会走这里(注意主机的从机ACK不应该走这个流程)
void WTVsa::AutoRangeErr(int ErrCode)
{
    // 主机失败后的参数还原
    m_AutoRange->RestoreParam(m_Param[0], false);
    if (IsTBTApAgcing())
    {
        GetVsg().StopRunningMod();
        GetVsg().RestoreParam();
    }

    ClearFinAgent();    // 双单元这里会清理，所以任何一个失败都会停止流程.
                        // TODO, 考虑下如果一个单元AGC失败后ClearFinAgent,
                        // 另一个单元刚好出现硬件完成事件会走 WTVsa::ProcModFin 采集数据流程
                        // 经过分析，这样是没有影响的, 最多可能就是多采集一次数据.

    SetCurFlowResult(ErrCode);  // 保存错误码

    // 任何一个单元出错将另一和单元也设置为完成状态.
    m_AutoRange->SetCompleteState(AGC_COMPLETE_FAILED);
    m_AutoRange2->SetCompleteState(AGC_COMPLETE_FAILED);

    // 8080两单元其中之一AutoRangeErr, 顺便把另一个单元的Trigger也清理掉, 防止可能的误操作.
    m_AGCWait.clear();

    do
    {
        m_Mods.clear();
    } while (0);

    // SISO完成 或者 MIMO主从都完成.
    if ((!IsMIMOMaster()) || IsMimoDone())
    {
        // 可能存在失败, 但是主机也失败了, 不管从机错误码了
        // ErrCode = GetCurFlowResult();
        m_ExtConn->Response(ErrCode);       // AGC 完成流程(主机失败)

        ClearCurFlowResult();
        SetAgcingState(false);
        m_ExtConn->CancelSilent();
    }
    else    // 从机还有没完成, Response放从机ACK流程中去处理
    {
    }
}

void WTVsa::TransParamIn8080DulPortMode(VsaParam *Param, int ModIdx)
{
    if (ModIdx == 1)
    {
        Param[0].Type = TEST_SISO;
        Param[0].VsaMask = 0;

        Param[0].Ampl = Param[0].Ampl2;
        Param[0].Freq = Param[0].Freq2;
        Param[0].TrigLevel = Param[0].TrigLevel2;
        Param[0].ExtGain2 = Param[0].ExtGain;

        Param[0].Ampl2 = 0;
        Param[0].Freq2 = 0;
        Param[0].TrigLevel2 = 0;
    }
    else
    {
        Param[0].Type = TEST_SISO;
        Param[0].VsaMask = 0;

        Param[0].Ampl2 = 0;
        Param[0].Freq2 = 0;
        Param[0].TrigLevel2 = 0;
    }
    Param[0].DulPortMode = 0;
}

void WTVsa::FlatResponseDataClear(Rx_Parm &CalParam)
{
    memset(CalParam.rx_spec_flat_comp_parm.bb_comp_gain, 0, sizeof(CalParam.rx_spec_flat_comp_parm.bb_comp_gain));
    memset(CalParam.rx_spec_flat_comp_parm.rf_comp_gain, 0, sizeof(CalParam.rx_spec_flat_comp_parm.rf_comp_gain));
    CalParam.rx_spec_flat_comp_parm.bb_comp_len = 0;
    CalParam.rx_spec_flat_comp_parm.rf_comp_len = 0;
}

int WTVsa::ProcAutoRangeData(int ModIdx)
{
    int Ret = WT_OK;
    bool HasFrame = false;
    double Power, PeakPower, MinPower;
    auto &DataInfo = m_CapData[ModIdx].DataInfo[0];

    Ret = GetCaptureData(m_Mods[ModIdx].ModId, DataInfo);
    if (Ret != WT_OK)
    {
        return Ret;
    }
    FlatResponseDataClear(m_CapData[ModIdx].CalParam[0]);
#if 0
    //直接从算法获取第二Segment的功率数据
    //VSN:55588 算法库修改，8080合并调用在算法完成后，此方法不再适用。
    m_Alg.Clear();
    m_Alg.SetData(DataInfo, i, m_CapParam[0], m_CapData[ModIdx].CalParam[0], m_ExtendEVMStu);
    Ret = m_Alg.AlzPower(ModIdx, HasFrame, Power, PeakPower, MinPower, m_CapParam[0].GetDemode());
#else
    m_Alg.Clear();

    if (m_Param[0].Type == TEST_80_80M_AGC)
    {
        //TEST_80_80M_AGC时，先备份Cap参数，将80+80的Cap参数转换为SISO的第一Segment的参数，获取功率数据后，再还原Cap参数。
        VsaParam CapParmBackup;
        CapParmBackup = m_CapParam[0];
        TransParamIn8080DulPortMode(m_CapParam, ModIdx);
        m_Alg.SetData(DataInfo, 0, m_CapParam[0], m_CapData[ModIdx].CalParam[0], m_ExtendEVMStu);
        m_CapParam[0] = CapParmBackup;
    }
    else
    {
        //正常模式时，不必进行参数转换。
        m_Alg.SetData(DataInfo, 0, m_CapParam[0], m_CapData[ModIdx].CalParam[0], m_ExtendEVMStu);
    }
    Ret = m_Alg.AlzPower(0, HasFrame, Power, PeakPower, MinPower, m_CapParam[0].GetDemode());
#endif

#if AGC_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------AGCDebug"
         << ", ModIdx =" << ModIdx
         << ", HasFrame =" << HasFrame
         << ", Power =" << Power
         << ", PeakPower=" << PeakPower
         << ", Minpower " << MinPower 
         << "######" << std::endl;
#endif

    if (Ret != WT_OK)
    {
        return Ret;
    }

    if(m_Param[0].IsUseDualParam() && ModIdx == 1)
    {
        Ret = AdjustCapParam2(HasFrame, Power, PeakPower, MinPower);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }
    else if(ModIdx == 0)
    {
        Ret = AdjustCapParam(HasFrame, Power, PeakPower, MinPower);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }
    else
    {
#if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Error  ModIdx = %d########\n", ModIdx);
#endif
        return WT_ARG_ERROR;
    }

    if (m_AutoRange->IsComplete() && m_AutoRange2->IsComplete())
    {
        // 正常AGC流程Pass后的还原参数
        m_AutoRange->RestoreParam(m_Param[0], true);
        if (IsTBTApAgcing())
        {
            GetVsg().StopRunningMod();
            std::vector<ModInfo> *VsgModInfo = GetVsg().GetModInfo();
            VsgModInfo->clear();
            GetVsg().RestoreParam();
        }
        //80+80非双端口模式时要要恢复使用多个模块
        if (m_Param[0].IsAC8080() && !m_Param[0].IsUseDualParam())
        {
            Ret = AllocMod(m_Param[0].VsaMask);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }
#if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug mod Freq = %lf,Freq2 = %lf\n", m_Param[0].Freq, m_Param[0].Freq2);
#endif
    }
    return WT_OK;
}

//返回true表示已完成处理过程，硬件资源可释放，否则资源不可释放
bool WTVsa::AutoRangeProc(int ModId)
{
    int i = GetModIndex(ModId);
    int Status = m_Mods[i].Status;
    int Ret = WT_OK;

    //通知WaitTrig，本单元已经Trig完成
    DeleteAGCWaitTrigger(i);

    int AnalogIQSW = 0;
    Ret = DevLib::Instance().GetAnalogIQSW(ModId, AnalogIQSW);
    if (Ret != WT_OK)
    {
        AutoRangeErr(Ret);
        return true;
    }
    if (AnalogIQSW)
    {
        if (Status == WT_RX_TX_STATE_DONE)
        {
            Ret = ProcAutoRangeDataBaseBand(i);
        }
        else
        {
            AutoRangeErr(WT_VSA_CAPTURE_FAILED);
            return true;
        }
    }
    else if (Status == WT_RX_TX_STATE_DONE)
    {
        Ret = ProcAutoRangeData(i);
        if (Ret != WT_OK)
        {
            AutoRangeErr(Ret);
            return true;
        }
    }
    else if (Status == WT_RX_TX_STATE_TIMEOUT) //trigger超时采用freerun
    {
        if (i == 0 || !m_Param[0].IsUseDualParam())
        {
            m_Param[0].TrigType = WT_TRIG_TYPE_FREE_RUN;
        }
        else
        {
            m_TrigType2 = WT_TRIG_TYPE_FREE_RUN;
        }
    }
    else
    {
        AutoRangeErr(WT_VSA_CAPTURE_FAILED);
        return true;
    }

    // m_AutoRange 与 m_AutoRange2 任何一个还没有完成.
    if ((!m_AutoRange->IsComplete() && i == 0)
       || (!m_AutoRange2->IsComplete() && i == 1))
    {
        //未完成时需要再设置一次参数。
        Ret = SetMod(i);
        m_Mods[i].IsConfig = Ret == WT_OK;
        if (Ret != WT_OK)
        {
            AutoRangeErr(i);
            return true;
        }

        i == 0 ? m_RetryCnt++ : m_RetryCnt2++;
        if (m_RetryCnt > 15 || m_RetryCnt2 > 15)
        {
            AutoRangeErr(WT_RETRY_EXCEED_MAX_CNT);
            WTLog::Instance().LOGERR(WT_RETRY_EXCEED_MAX_CNT, "retry count exceed 15");
            return true;
        }

        Ret = StartAllMod(i);
        if (Ret != WT_OK)
        {
            AutoRangeErr(Ret);
            return true;
        }

        if ((m_Param[0].TrigType != WT_TRIG_TYPE_FREE_RUN && i == 0) || (m_TrigType2 != WT_TRIG_TYPE_FREE_RUN && i == 1))
        {
            if (IsTBTApAgcing())
            {
                AddAGCWaitTrigger(TB_TF_AGC_WAIT_TIME_EXT + m_Param[0].SamplingTime + GetVsg().GetVsgPeriod(), i);
            }
            else
            {
                AddAGCWaitTrigger(m_Param[0].MaxIFG + m_Param[0].SamplingTime, i);
            }
        }

        return false;
    }

    // 注意点: 只要流程走到这里说明 AGC 没有任何错误发生, 因为任何出错流程都已经在前面return了
    if (m_AutoRange->IsCompletePass() && m_AutoRange2->IsCompletePass())
    {
        m_Mods.clear();
        AutoRangeCheckAndResponse();
        return true;
    }
    else if(m_Param[0].IsDualParamMode())
    {
        //80+80双射频参数模式，主机AGC完成时，从机AGC启动.
        if(m_AutoRange->IsComplete() && i == 0 && !m_AutoRange2->IsComplete())
        {
#if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug 80+80 Dual Param Mode start agc mod2\n");
#endif
            //重新设置主单元，确保ATT0的值符合主单元的参考电平配置。
            //(当主单元的参考电平是通过计算得到的时候，最后ATT0的值不符合主单元的参考电平)
            Ret = SetMod(0);
            if (Ret != WT_OK)
            {
                AutoRangeErr(Ret);
                return true;
            }

            Ret = SetMod(1);
            m_Mods[1].IsConfig = Ret == WT_OK;
            if (Ret != WT_OK)
            {
                AutoRangeErr(Ret);
                return true;
            }

            Ret = StartAllMod(1);
            if (Ret != WT_OK)
            {
                AutoRangeErr(Ret);
                return true;
            }
            if ((m_TrigType2 != WT_TRIG_TYPE_FREE_RUN))
            {
                AddAGCWaitTrigger(m_Param[0].MaxIFG + m_Param[0].SamplingTime, 1);
            }
        }
        return false;
    }
    else
    {
        return false;
    }
}

int WTVsa::ProcAutoRangeDataBaseBand(int ModIdx)
{
    int Ret = WT_OK;
    bool HasFrame = false;
    double Power, PeakPower, MinPower;
    auto &DataInfo = m_CapData[ModIdx].DataInfo[0];

    Ret = GetCaptureData(m_Mods[ModIdx].ModId, DataInfo);
    if (Ret != WT_OK)
    {
        return Ret;
    }
    m_Alg.Clear();
    m_Alg.SetData(DataInfo, 0, m_CapParam[0], m_CapData[ModIdx].CalParam[0], m_ExtendEVMStu);
    Ret = m_Alg.AlzPower(0, HasFrame, Power, MinPower, PeakPower);
    //Ret |= m_Alg.AlzMinAvgPower(0, MinPower);
    if (Ret != WT_OK)
    {
        m_Param[0].TrigLevel = m_AutoRange->GetDefaultTrigLevel();
    }
    else
    {
        m_Param[0].TrigLevel = (Power + MinPower) / 2;
        m_AutoRange->RestoreParamBaseBand(m_Param[0]);
    }

    m_Param[0].TrigType = m_AutoRange->GetUserTrigType();
    m_AutoRange->SetCompleteState(AGC_COMPLETE_PASS);
    return WT_OK;
}

// 主机完成时调用, 响应上位机前必须先检查MIMO的完成情况
void WTVsa::AutoRangeCheckAndResponse(void)
{
    ClearFinAgent();

    // !IsMIMOMaster() 单机完成
    // Mimo模式主机完成 且 IsMimoDone() 从机都完成
    if ((!IsMIMOMaster()) || IsMimoDone())
    {
        if (GetCurFlowResult() == WT_OK)    // 没有故障
        {
            AutoRangeResponse();            // AGC 完成流程(OK, 从机先完成, 主机后完成)
        }
        else
        {
            m_ExtConn->Response(GetCurFlowResult()); // AGC 完成流程(从机失败)
        }

        ClearCurFlowResult();
        SetAgcingState(false);
        m_ExtConn->CancelSilent();
    }
    else   // 主机完成, MIMO未完成
    {
    }
}


int WTVsa::AutoRangeResponse(void)
{
    int Ret = WT_OK;

    // 160拆分成8080时参数返回给上位机需要恢复成原来的样子
    if (m_Param[0].Is160())
    {
        m_Param[0].Freq += 40 * 1e6;
        m_Param[0].Freq2 = 0;
    }

#if 1
    WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Resopnse apml1=%lf, apml2=%lf, TrigLevel1=%lf, TrigLevel2=%lf\n",m_Param[0].Ampl, m_Param[0].Ampl2, m_Param[0].TrigLevel, m_Param[0].TrigLevel2);
#endif

    if (!IsMIMOMaster())
    {
        Ret = m_ExtConn->SendAutoRangeResult(1, &m_Param[0], sizeof(m_Param[0]));
    }
    else if (IsMimoDone())
    {
        Ret = m_ExtConn->SendAutoRangeResult(m_SlaveConn.size() + 1,
                                             &m_Param[0],
                                             sizeof(VsaParam) * (m_SlaveConn.size() + 1));
    }

    if (m_Param[0].Is160())
    {
        m_Param[0].Freq -= 40 * 1e6;
        m_Param[0].Freq2 = m_Param[0].Freq + 80 * 1e6;
    }

    SetAgcingState(false);
    return Ret;
}

void WTVsa::ClearFinAgent(void)
{
    m_FinAgent = nullptr;
}

//80+80时由于是使用的同一个RF口，只需要一个模块做测试即可，简化实现
int WTVsa::AutoRange(Connector *Conn, void *Param)
{
    int Ret = WT_OK;

    if (DigModeLib::Instance().IsDigMode())
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        WTLog::Instance().LOGERR(Ret, "Dig mode does not support agc");
        return Ret;
    }

    if (!Conn->IsLinkToSlave())
    {
        ClearCurFlowResult();   // 清空上一条命令的状态信息

        //需要先配置才能autorange
        if (m_ParamNum <= m_SlaveConn.size())
        {
            WTLog::Instance().LOGERR(WT_NEED_SET_MOD, "no config param");
            return WT_NEED_SET_MOD;
        }

        m_RetryCnt = 0;
        m_RetryCnt2 = 0;
        m_AutoRange.reset(new(std::nothrow) VsaAutoRange);
        if (m_AutoRange == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc autorange info failed");
            return WT_ALLOC_FAILED;
        }
        m_AutoRange->SetSmpTime(m_AgcSamplingTime * ((double)MAX_SMAPLE_RATE / m_Param[0].SamplingFreq));
        m_TrigType2 = WT_TRIG_TYPE_IF;
        m_AutoRange2.reset(new(std::nothrow) VsaAutoRange);
        if (m_AutoRange2 == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc autorange info failed");
            return WT_ALLOC_FAILED;
        }
        m_AutoRange2->SetSmpTime(m_AgcSamplingTime);
        // MIMO转发命令到从机
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "start mimo autorange failed");
                return Ret;
            }

            Conn->SetRsp(false);
        }

        // 到这里时从机已经开始工作了, 所以要开始记录错误码了
        SetAgcingState(true);
        m_ExtConn->StartSilent();   // Conn 开始静默 也就是AGC加锁, 不响应其他的任何外部命令了

        int AnalogIQSW = 0;
        int ModId = 0;
        Ret = DevLib::Instance().GetModId(DEV_TYPE_VSA, m_Param[0].RFPort, ModId);
        RetWarnning(Ret,"GetModId fail");
        Ret = DevLib::Instance().GetAnalogIQSW(ModId, AnalogIQSW);
        RetWarnning(Ret,"GetAnalogIQSW fail");

        if (AnalogIQSW)
        {
            m_AutoRange2->SetCompleteState(AGC_COMPLETE_PASS);
            m_AutoRange->SetAndBackupParamBaseBand(m_Param[0]);
        }
        else
        {
            if (!m_Param[0].IsUseDualParam())
            {
                m_AutoRange2->SetCompleteState(AGC_COMPLETE_PASS);
            }
            else
            {
                m_AutoRange2->BackupParamUserTrigType(m_Param[0].TrigType);
            }
            m_AutoRange->SetAndBackupParam(m_Param[0]);
        }

        do
        {
            //80+80双射频参数模式时，同时先启动主机AGC，主机AGC完成后再启动从机AGC.
            if(m_Param[0].IsDualParamMode())
            {
                Ret = AllocSetMod(false);
                if(Ret != WT_OK)
                {
                    break;
                }
                Ret = StartAllMod(0);
            }
            else
            {
                //80+80双端口模式时，同时启动主从机AGC.
                Ret = AllocSetMod(true);
            }
        }while(0);

        if (Ret != WT_OK)
        {
            SetCurFlowResult(Ret);
            // 主机未执行AGC的参数还原
            m_AutoRange->RestoreParam(m_Param[0], false);

            // 单机SISO就要设置立即返回响应
            if (!IsMIMOMaster())
            {
                SetAgcingState(false);
                m_ExtConn->CancelSilent();
                Conn->SetRsp(true);
            }

            return Ret;     // 注意这个错误不会立刻返回给上位机，因为从机已经运行了, 还需要等待从机完成.
        }

        Conn->SetRsp(false);
        SetFinAgent(bind(&WTVsa::AutoRangeProc, this, _1));

        AddAGCWaitTrigger(m_Param[0].MaxIFG + m_Param[0].SamplingTime, 0);

        //双端口模式时同时开启对从机单元的检测。
        if(m_Param[0].IsDualPortMode())
        {
            AddAGCWaitTrigger(m_Param[0].MaxIFG + m_Param[0].SamplingTime, 1);
        }
    }
    else    // 主机收到从机的ACK
    {
        ProcMimoAck(Conn, false);
        Ret = Conn->GetCmdResult();

        if (Ret == WT_OK)   // 当前从机返回成功的ACK
        {
            Param = (char *)Param + sizeof(int);
            auto iter = m_SlaveConn.begin();
            for (int i = 0; i < (signed)m_SlaveConn.size(); i++, iter++)
            {
                if (**iter == *Conn)
                {
                    m_Param[i+1] = *static_cast<VsaParam *>(Param);
                }
            }

            if (IsMimoDone())   // MIMO都完成, 当前ACK是最后一个MIMO从机
            {
                // 主机完成(可能有错误码)
                if (m_AutoRange->IsComplete() && m_AutoRange2->IsComplete())
                {
                    if (GetCurFlowResult() == WT_OK)    // 查看有没错误码
                    {
                        Ret = AutoRangeResponse();      // AGC 完成流程(OK, 主机先完成, 从机后完成)
                    }
                    else // 主从都已经完成, 但是有错误发生
                    {
                        m_ExtConn->Response(GetCurFlowResult()); // AGC 完成流程(Failed, 最后的从机是成功)
                    }

                    ClearCurFlowResult();
                    SetAgcingState(false);
                    m_ExtConn->CancelSilent();
                }
                else // 主机未完成, Response让主机执行
                {
                }
            }
            else    // 还有从机没有完成
            {
            }
        }
        else    // 从机失败
        {
            // 当期从机是最后完成
            if (IsMimoDone() && m_AutoRange->IsComplete() && m_AutoRange2->IsComplete())
            {
                SetCurFlowResult(Ret);
                m_ExtConn->Response(GetCurFlowResult()); // AGC 完成流程(Failed, 最后的从机是失败)

                ClearCurFlowResult();
                SetAgcingState(false);
                m_ExtConn->CancelSilent();
            }
            else    // 还有从机 或者 还有主机 没有完成
            {
                SetCurFlowResult(Ret);
            }
        }
    }

    return Ret;
}

int WTVsa::AddAGCWaitTrigger(double Interval, int Index)
{
    std::unique_lock<std::mutex> ErrLock(m_AGCWaitMutex);
    for (int i = 0; i<m_AGCWait.size(); i++)
    {
        if (m_AGCWait[i].Index == Index)
        {
            m_AGCWait.erase(m_AGCWait.begin() + i);
            break;
        }
    }

    struct timeval Start;
    gettimeofday(&Start, nullptr);
    struct AutoRangeWait WaitInfo(Start.tv_sec * 1e6 + Start.tv_usec, (long)(Interval * 1e6), Index);
    m_AGCWait.push_back(WaitInfo);
    if (!m_AutoRangeEv.is_active())
    {
 #if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug m_AutoRangeEv.start\n");
        WTLog::Instance().WriteLog(LOG_DEBUG, "WaitTrigger Interval= %ld us\n",(long)(Interval * 1e6));
        WTLog::Instance().WriteLog(LOG_DEBUG, "WaitTrigger Index= %d \n",Index);
 #endif
        m_AutoRangeEv.start(0, 300 * 1e-6);
    }
    return WT_OK;
}

int WTVsa::Clear500MWaitRigger(void)
{
    //api下发主动停止vsa模块后，删除等待超时计时
    if(!IsAgcing())
    {
        m_AGCWait.clear();
        m_AutoRangeEv.stop();
    }

    if(m_Param != nullptr && IsWideBandOn())
    {
        //恢复原来的采集参数
        memcpy(&m_Param[0], &BakParam, sizeof(VsaParam));
        memcpy(&m_CapParam[0], &BakCapParam, sizeof(VsaParam));

        //把恢复的配置参数配置到硬件
        SetMod();
    }

    return WT_OK;
}

void WTVsa::Clear500MCapData(void)
{
    //api重新下发启动vsa模块时，需要先清空下500M采集数据
    for(int i = 0; i < MAX_SEGMENT_CNT; i++)
    {
        for(int j = 0; j < MAX_NUM_OF_CHNNEL; j++)
        {
            ThreeData[i][j][0].clear();
            ThreeData[i][j][1].clear();
            CalParam[i][j].clear();
        }
    }
}

int WTVsa::DeleteAGCWaitTrigger(int Index)
{
    std::unique_lock<std::mutex> ErrLock(m_AGCWaitMutex);
    for (int i=0; i<m_AGCWait.size(); i++)
    {
        if (m_AGCWait[i].Index == Index)
        {
            m_AGCWait.erase(m_AGCWait.begin() + i);
            break;
        }
    }

    if (m_AGCWait.empty())
    {
#if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug m_AutoRangeEv.stop\n");
#endif
        m_AutoRangeEv.stop();
    }
    return WT_OK;
}

void WTVsa::WaitTrigger(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    std::unique_lock<std::mutex> ErrLock(m_AGCWaitMutex);
    if (m_AGCWait.empty())
    {
#if AGC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug m_AutoRangeEv.stop\n");
#endif
        m_AutoRangeEv.stop();
        return;
    }

    struct timeval End;
    gettimeofday(&End, nullptr);
    long EndTime = End.tv_sec * 1e6 + End.tv_usec;

    std::vector<int> EraseVector;
    for (int i = 0; i < m_AGCWait.size(); i++)
    {
        if (m_FinMods & (1 << m_Mods[m_AGCWait[i].Index].ModId))
        {
            EraseVector.push_back(i);   //设置完成标记
        }
        else if (EndTime - m_AGCWait[i].StartTime > m_AGCWait[i].IntervalTime)
        {
            EraseVector.push_back(i);   //设置完成标记
#if AGC_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "AGCDebug Mod Index %d trigger timerout!\n", m_AGCWait[i].Index);
            WTLog::Instance().WriteLog(LOG_DEBUG, "WaitTrigger Index= %d \n",i);
            WTLog::Instance().WriteLog(LOG_DEBUG, "Used times = %ld us \n IntervalTime = %ld us",EndTime - m_AGCWait[i].StartTime,m_AGCWait[i].IntervalTime);
#endif
            DevLib::Instance().VSAStop(m_Mods[m_AGCWait[i].Index].ModId); //超时情况下要停止VSA
            m_Mods[m_AGCWait[i].Index].IsConfig = false;
            m_Notify(DEV_RES_VSA, m_Mods[m_AGCWait[i].Index].ModId, WT_RX_TX_STATE_TIMEOUT);
        }
    }

    //将已完成的单元清除
    while (!EraseVector.empty())
    {
        m_AGCWait.erase(m_AGCWait.begin() + EraseVector.back());
        EraseVector.pop_back();
    }
}

int WTVsa::LoadSigFile(const std::string &Name, const void *Data, int Len)
{
    int Ret = WT_OK;
    string FileName = GetLowWaveDir() + Name;

    if (Len > 0)
    {
        Ret = SaveFile(FileName, Data, Len);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Vsa SaveFile " << FileName << ", Len = " << Len << std::endl;
    }
    else
    {
        Ret = access(FileName.c_str(), F_OK);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "access " << FileName << ", error = " << Ret << std::endl;
        Ret = (Ret == 0 ? WT_OK : WT_OPEN_FILE_FAILED);
    }

    if (Ret == WT_OK)
    {
        m_Alg.Clear();
        Ret = m_Alg.LoadSigFile(FileName, m_ExtendEVMStu);
        if (Ret == WT_OK)
        {
            m_UseFile = true;
            m_CurDataNoAlzFlag = true;
        }
    }

    WTLog::Instance().WriteLog(LOG_CMD_TRACE, "**************WTVsa::LoadSigFile, m_UseFile=%d, tid=%d ******\n", m_UseFile, gettid());
    return Ret;
}

void WTVsa::SaveRawData(void)
{
#define RAW_DATA_PATH "/tmp/wave/rawdata/"
    if (1 != m_SaveRawDataEnable)
    {
        return;
    }

    // 检查并创建目录
    struct stat FileStat;
    if ((stat(RAW_DATA_PATH, &FileStat) != 0) || !S_ISDIR(FileStat.st_mode))
    {
        if (-1 == mkdir(RAW_DATA_PATH, 0755)) // 建立目录
        {
            WTLog::Instance().LOGERR(WT_ERROR, "Create Dir Failed!");
        }
    }

    // 检查文件夹下的文件数量，如果达到指定数量MaxCnt，就先删除最旧的一个信号文件再保存~
    int Cnt = 0;
    std::string result = Basefun::shell_exec((std::string("ls -l ") + RAW_DATA_PATH + std::string(" | wc -l")).c_str());
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << PoutN(result);
    if (result.length() > 0 && std::isdigit(result.at(0)))
    {
        Cnt = std::stoi(result) - 1;
        if (Cnt >= m_SaveRawDataMax)
        {
            int last = Cnt - m_SaveRawDataMax;
            stringstream bash_cmd;
            bash_cmd << std::string("cd ");
            bash_cmd << RAW_DATA_PATH;
            bash_cmd << std::string(" && ");
            bash_cmd << std::string("ls -tr");
            bash_cmd << std::string(" | head -");
            bash_cmd << last;
            bash_cmd << std::string(" | xargs rm");
            Basefun::LinuxSystem(bash_cmd.str().c_str());
        }
    }

    struct timeval timetemp;
    gettimeofday(&timetemp, NULL);
    string SavePath = string(RAW_DATA_PATH) + "rawdata_" + to_string((long)(timetemp.tv_sec * 1e6 + timetemp.tv_usec)) + ".csv";
    std::ofstream Ofs(SavePath.c_str(), fstream::out | fstream::trunc);
    if (!Ofs)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << __FILE__ << " line:" << __LINE__ << " open output file error!" << std::endl;
        return;
    }

    typedef short ComplexShort[2];
    for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
    {
        for (int i = 0; i < m_CapData[Seg].DataNum; i++)
        {
            Ofs << "StartData," << endl;
            ComplexShort *pDataS = reinterpret_cast<ComplexShort *>(m_CapData[Seg].DataInfo[i].Buf.get());
            for (int j = 0; j < m_CapData[Seg].DataInfo[i].DataLen / sizeof(ComplexShort); j++)
            {
                Ofs << pDataS[j][0] << "," << pDataS[j][1] << "," << endl;
            }
        }
    }
    Ofs.flush();
    Ofs.close();
}

int WTVsa::AlzSingleData(int FrameId)
{
    int Ret = WT_OK;

    if (IsMIMOMaster() && !IsMimoDone())
    {
        WTLog::Instance().LOGERR(WT_MIMO_DATA_UNCOMPLETE, "mimo data uncomplete");
        return WT_MIMO_DATA_UNCOMPLETE;
    }

    m_Alg.Clear();

    if(m_CurDataNoAlzFlag == false)
    {
        m_Alg.SetAnalyzeMode(1);
    }

    //如果分析参数为8080，判断采集的数据是不是有两段的，是否为8080，没有就报错~解决普通协议单次采集仅一路数据后转标准为8080来分析，分析成功的问题
    if(!m_CapParam[0].IsAC8080() && m_Param[0].IsAC8080())
    {
        WTLog::Instance().LOGERR(WT_SIG_FILE_ERROR, "8080 needs two segments data.");
        return WT_SIG_FILE_ERROR;
    }
    for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
    {
        for (int i = 0; i < m_CapData[Seg].DataNum; i++)
        {
//            //增加500M采集数据保存，默认不打开
//            int Is500MPrintf = 0;
//            DevConf::Instance().GetItemVal("Is500MSpectPrintf", Is500MPrintf);
//            if(Is500MPrintf == 1)
//            {
//                for(int k = 0; k < ThreeData[Seg][i].size(); k++)
//                {
//                    int cnt = ThreeData[Seg][i][k].BufLen/(sizeof(short)*2);
//                    short *Code = (short *)(ThreeData[Seg][i][k].Buf.get());
//                    char buf1[128];
//                    sWTLog::Instance().WriteLog(LOG_DEBUG, buf1, "%d_%d_%dData.csv", Seg, i,k);
//                    std::ofstream DataFile(WTConf::GetDir() + buf1, fstream::out | fstream::trunc);
//                    for(int k =0; k < cnt; k++)
//                    {
//                        char Buf[128];
//                        sWTLog::Instance().WriteLog(LOG_DEBUG, Buf, "%d,%d\n", Code[k * 2], Code[k * 2 + 1]);
//                        DataFile <<  Buf;
//                    }
//                    DataFile.close();
//                }
//            }
            if (IsAlg3GPPStandardType(m_Alg.GetAlzParamDemode()))
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "AlzSingleData CurSpectWideOffset=%f, ThreeData[Seg][i][1].size=%lu, i=%d, seg=%d m_Alg.GetAlzParamNrAlzBand()=%d\n",
                    CurSpectWideOffset, ThreeData[Seg][i][1].size(), i, Seg, m_Alg.GetAlzParamNrAlzBand());
                if ((IsWideBandOn() || m_Alg.GetAlzParamNrAlzBand() >= 50*1e6))
                {
                     if ((m_Alg.GetAlzParamNrAlzBand() > 70*1e6 && ThreeData[Seg][i][1].size() >= NR_WIDE_BAND_MAX_CAP_CNT)
                         || (m_Alg.GetAlzParamNrAlzBand() <= 70*1e6 && ThreeData[Seg][i][1].size() >= 1))
                     {
                         m_Alg.Set3gppNrWideBandData(ThreeData[Seg][i][0], ThreeData[Seg][i][1], m_CapParam[0], m_CapData[0].CalParam[0],CurSpectWideOffset);
                     }
                }
                else
                {
                    m_Alg.SetData3GPP(m_CapData[0].DataInfo[0], m_CapParam[0], m_CapData[0].CalParam[0]);
                }
            }
            else
            {
                if(IsWideBandOn() && ThreeData[Seg][i][0].size() >= WIDE_BAND_MAX_CAP_CNT)
                {
                    m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                        m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu,
                        ThreeData[Seg][i][0], (int)IsWideBandOn(), CAP_POINT_CNT, CurSpectWideOffset);
                }
                else
                {
                    m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                        m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu);
                }
            }
        }
    }

    if (!m_RefFile.empty())
    {
        Ret = m_Alg.SetCmimoRefFile(m_RefFile);
        CheckRet(Ret);
    }
    if (m_SaveRawDataEnable)
    {
        SaveRawData();
    }
    if (IsAlg3GPPStandardType(m_Alg.GetAlzParamDemode()))
    {
        Ret = (m_Alg.Get3GPPAvgParam() >= 1) ? m_Alg.AlzAvgData3GPP(FrameId, m_AvgFrameNum, m_AvgMode) : m_Alg.AlzFrameData3GPP(FrameId);
    }
    else
    {
        Ret = m_AvgFrameNum <= 1 ? m_Alg.AlzFrameData(FrameId, true) : m_Alg.AlzData(FrameId, m_AvgFrameNum);
    }
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "analysis failed");
    }

    return Ret;
}

int WTVsa::GetCmimoMode()
{
    if (m_RefFile.empty())
    {
        return false;
    }
    else
    {
        return true;
    }
}

int WTVsa::AlzMultiData(int FrameId)
{
    int Ret = WT_OK;
    int SegNum = !m_CapParam[0].IsAC8080() ? 1 : 2;

    if (m_AvgMode == MULTI_CNT_AVG)
    {
        m_Alg.ClearAvgData();

        for (int i = 0; i < m_CapData[0].CapCnt; i++)
        {
            m_Alg.Clear();

            for (int Seg = 0; Seg < SegNum; Seg++)
            {
                for (int j = 0; j < 1 + m_SlaveConn.size(); j++)
                {
                    auto Iter = m_CapData[Seg].MultiData[j].begin();
                    advance(Iter, i);
                    if(IsWideBandOn() && ThreeData[Seg][j][0].size() >= WIDE_BAND_MAX_CAP_CNT)
                    {
                        m_Alg.SetData(*Iter, Seg, m_CapParam[j], m_CapData[Seg].CalParam[j], m_ExtendEVMStu,
                                ThreeData[Seg][j][0], (int)IsWideBandOn(), CAP_POINT_CNT, CurSpectWideOffset);
                    }
                    else
                    {
                        m_Alg.SetData(*Iter, Seg, m_CapParam[j], m_CapData[Seg].CalParam[j], m_ExtendEVMStu);
                    }
                }
            }

            Ret = m_Alg.AlzAvgData(FrameId);
            CheckBreak(Ret);
        }
    }
    else
    {
        //滑动平均模式下如果是第一次采集需要清除算法之前的平均结果
        if (m_CapData[0].CapCnt == 1)
        {
            m_Alg.ClearAvgData();
        }
        else if (m_CapData[0].CapCnt > m_CapNum)
        {
            m_Alg.Clear();

            //如果采集次数超过最大数量，则需要对结果数据做滑动运算，即剥离第一次的数据
            for (int Seg = 0; Seg < SegNum; Seg++)
            {
                for (int j = 0; j < 1 + m_SlaveConn.size(); j++)
                {
                    if(IsWideBandOn() && ThreeData[Seg][j][0].size() >= WIDE_BAND_MAX_CAP_CNT)
                    {
                        m_Alg.SetData(m_CapData[Seg].MultiData[j].front(), Seg, m_CapParam[j], m_CapData[Seg].CalParam[j], m_ExtendEVMStu,
                                    ThreeData[Seg][j][0], (int)IsWideBandOn(), CAP_POINT_CNT, CurSpectWideOffset);
                    }
                    else
                    {
                        m_Alg.SetData(m_CapData[Seg].MultiData[j].front(), Seg,
                                    m_CapParam[j], m_CapData[Seg].CalParam[j], m_ExtendEVMStu);
                    }
                }
            }

            m_Alg.StripAvgData(FrameId);

            for (int Seg = 0; Seg < SegNum; Seg++)
            {
                for (int j = 0; j < 1 + m_SlaveConn.size(); j++)
                {
                    m_CapData[Seg].MultiData[j].pop_front();
                }
            }
        }

        m_Alg.Clear();

        for (int Seg = 0; Seg < SegNum; Seg++)
        {
            for (int j = 0; j < 1 + m_SlaveConn.size(); j++)
            {
                if(IsWideBandOn() && ThreeData[Seg][j][0].size() >= WIDE_BAND_MAX_CAP_CNT)
                {
                    m_Alg.SetData(m_CapData[Seg].MultiData[j].back(), Seg, m_CapParam[j], m_CapData[Seg].CalParam[j], m_ExtendEVMStu,
                            ThreeData[Seg][j][0], (int)IsWideBandOn(), CAP_POINT_CNT, CurSpectWideOffset);
                }
                else
                {
                    m_Alg.SetData(m_CapData[Seg].MultiData[j].back(), Seg,
                                  m_CapParam[j], m_CapData[Seg].CalParam[j], m_ExtendEVMStu);
                }
            }
        }

        Ret = m_Alg.AlzAvgData(FrameId);
        int Cnt = 0;
        GetCurAvgCnt(Cnt);
        if(Cnt ==0)
        {
            ClearAvgData();
        }
        if(Cnt == 1 && m_Alg.GetSlipClrFlag()) //滑动平均时，协议变更或者有帧转无帧，无帧转有帧清空下采集数据保留当次数据
        {
            for(auto &CapData: m_CapData)
            {
                CapData.CapCnt = 1;
            }

            for (int j = 0; j < MAX_NUM_OF_CHNNEL; j++)
            {
                for(auto &CapData: m_CapData)
                {
                    CapData.MultiData[j].erase(CapData.MultiData[j].begin(),--CapData.MultiData[j].end());
                }
            }
        }
    }

    return Ret;
}

int WTVsa::Analyze(int FrameId, const std::string &RefFile)
{
    int Ret = WT_OK;

    m_Alg.SetAnalyzeMode(); //配置analyzemode，默认为，表示第一次分析

    if (RefFile.empty())
    {
        m_RefFile.clear();
    }
    else
    {
        m_RefFile = GetWaveDir() + RefFile;
    }

    WTLog::Instance().WriteLog(LOG_CMD_TRACE, "**************WTVsa::Analyze, m_UseFile=%d, tid=%d ******\n", m_UseFile, gettid());
    if (!m_UseFile)
    {
        m_Alg.SetIQImbReset(0);
        if (m_Param == nullptr)
        {
            WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "not capture data yet");
            return WT_NO_SIG_DATA;
        }

        Ret = m_CapNum <= 1 ? AlzSingleData(FrameId) : AlzMultiData(FrameId);
    }
    else
    {
        if (!m_RefFile.empty())
        {
            Ret = m_Alg.SetCmimoRefFile(m_RefFile);
        }

        if (Ret == WT_OK)
        {
            if (m_CurDataNoAlzFlag == false)
            {
                m_Alg.SetAnalyzeMode(1);
            }
            m_Alg.SetIQImbReset(1);
            if (IsAlg3GPPStandardType(m_Alg.GetAlzParamDemode()))
            {
                Ret = m_Alg.AlzFrameData3GPP(FrameId);
            }
            else
            {
                Ret = m_Alg.AlzFrameData(FrameId);
            }
        }
    }

    if (Ret == WT_OK)
    {
        //如果不是文件加载分析，需要检测从机lic
        if(m_UseFile || (Ret = CheckSlaveBusinessLic()) == WT_OK)
        {
            SendParamToMon();
            SendAlzParamToMon(m_CurExtDemode);
            SendResultToMon();
            m_AlzFlag = true;
        }
        else
        {
            m_AlzFlag = false;
        }
    }
    else
    {
        m_AlzFlag = false;
    }

    m_CurDataNoAlzFlag = false;
    //分析完之后恢复Analyze为全部分析（全部分析evm，power，specstrum等）
    m_Alg.ResetAnalyzeGroup();
    return Ret;
}

int WTVsa::GetAnalyzeErrResult(void)
{
    return m_Alg.GetAnalyzeErrResult();
}

void WTVsa::SendResultToMon(void)
{
    char ErrBuf[128] = {0};
    list<shared_ptr<Monitor>> Mons;
    MonitorMgr::Instance().GetMonitors(Mons);

    if (m_Param == nullptr)
    {
        return;
    }

    for (auto &Mon : Mons)
    {
        if (Mon->IsMonPort(m_CapParam[0].RFPort))
        {
            list<string> Results = Mon->GetVsaResult();
            vector<MonVsaResult> ResultData;

            for (const auto &Item : Results)
            {
                int Seg = 0;    //取综合的
                for(int StreamId = 0; StreamId < (1 + m_SlaveConn.size()); StreamId++)
                {
                    MonVsaResult Temp;
                    memset(&Temp, 0, sizeof(MonVsaResult));
                    void *Data = nullptr;
                    int Len = 0;
                    int DataType = -1;

                    int Ret = m_Alg.GetVsaResult(Item, StreamId, Seg, &Data, Len, DataType);
                    if (Ret == WT_OK)
                    {
                        Temp.StreamID = StreamId;
                        Temp.SegmentID = Seg;
                        Temp.DataLen = Len;
                        Temp.DataType = DataType;
                        Temp.Data = Data;
                        STRNCPY_USER(Temp.Name, Item.c_str());
                        ResultData.push_back(Temp);
                    }
                    else
                    {
                        sprintf(ErrBuf, "monitor get vsa result [%s] failed", Item.c_str());
                        WTLog::Instance().LOGERR(Ret, ErrBuf);
                    }
                }
            }

            Mon->SendVsaResult(ResultData);
            m_Alg.FreeVsaResult();
        }
    }
}

int WTVsa::SetAvgParam(int AvgType, int AvgMode, int AvgNum)
{
    // 每次配置平均参数时将采集次数清理
    for (auto &CapData : m_CapData)
    {
        CapData.CapCnt = 0;
    }

    for (int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
    {
        for (auto &CapData : m_CapData)
        {
            CapData.MultiData[i].clear();
        }
    }

    if (AvgType == MULTI_CAP_AVG)
    {
        m_AvgMode = AvgMode;
        m_CapNum = AvgNum;
        m_AvgFrameNum = 0;
    }
    else
    {
        //SINGLE_CAP_AVG
        m_AvgMode = AvgMode;
        m_CapNum = 1;          //多帧平均模式下VSA只需要采集一次
        m_AvgFrameNum = AvgNum;
    }
    if(m_AvgMode == SLIDE_AVG && m_CapNum == 1)
    {
        m_Alg.Set3GPPAvgParam(AvgNum);
    }
    else
    {
        m_Alg.Set3GPPAvgParam(0);
    }
    
    return WT_OK;
}

int WTVsa::GetAlzData(const std::string &Type, int Stream, int Segment, void **Data, int &Len, int &DataType)
{
    if (IsAlg3GPPStandardType(m_Alg.GetAlzParamDemode()))
    {
        return m_Alg.GetVsaResult3GPP(Type, Stream, Segment, Data, Len, DataType);
    }
    else
    {
        return m_Alg.GetVsaResult(Type, Stream, Segment, Data, Len, DataType);
    }
}

int WTVsa::Get11axUserAlzData(int UserID, const std::string &Type, int Stream, int Segment, void **Data, int &Len, int &DataType)
{
    return m_Alg.Get11axUserVsaResult(UserID, Type, Stream, Segment, Data, Len, DataType);
}

int WTVsa::GetAvgData(int Idx, int Stream, int Segment, BufInfo *ResultInfo)
{
    int Ret = WT_OK;
    const VsaCommResult *Result = nullptr;
    const VsaSleCommResult *SleResult = nullptr;
    VsaBTCommResult2 *BtResult = nullptr;
    VsaBTCommResult *BtResult1 = nullptr;

    if (Segment < 0 || Segment > 2)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "Segment error");
        return WT_ARG_ERROR;
    }

    //Idx为0表示获取总的平均结果
    if (Idx == 0)
    {
        const VsaCommResult *AvgResult, *MaxResult, *MinResult;
        const VsaBTCommResult *AvgBTResult1, *MaxBTResult1, *MinBTResult1;
        const VsaSleCommResult *AvgSleResult, *MaxSleResult, *MinSleResult;
        const Vsa3GPPCommResult *Avg3GPPResult, *Max3GPPResult, *Min3GPPResult;

        if (m_Alg.m_Demod == WT_DEMOD_BT)
        {
            Ret = m_Alg.GetBTAvgResult(Segment, Stream, &AvgBTResult1, &MaxBTResult1, &MinBTResult1);
        }
        else if (m_Alg.m_Demod == WT_DEMOD_GLE)
        {
            Ret = m_Alg.GetSleAvgResult(Segment, Stream, &AvgSleResult, &MaxSleResult, &MinSleResult);
        }
        else if (IsAlg3GPPStandardType(m_Alg.m_Demod))
        {
            Ret = m_Alg.Get3GPPAvgResult(Segment, Stream, &Avg3GPPResult, &Max3GPPResult, &Min3GPPResult);
        }
        else
        {
            Ret = m_Alg.GetAvgResult(Segment, Stream, &AvgResult, &MaxResult, &MinResult);
        }
        
        if (Ret == WT_OK)
        {
            if (m_Alg.m_Demod == WT_DEMOD_BT)
            {
                
                ResultInfo[0].Data = (void *)AvgBTResult1;
                ResultInfo[0].Len = sizeof(VsaBTCommResult);
                ResultInfo[1].Data = (void *)MaxBTResult1;
                ResultInfo[1].Len = sizeof(VsaBTCommResult);
                ResultInfo[2].Data = (void *)MinBTResult1;
                ResultInfo[2].Len = sizeof(VsaBTCommResult);
            }
            else if(m_Alg.m_Demod == WT_DEMOD_GLE)
            {
                ResultInfo[0].Data = (void *)AvgSleResult;
                ResultInfo[0].Len = sizeof(VsaSleCommResult);
                ResultInfo[1].Data = (void *)MaxSleResult;
                ResultInfo[1].Len = sizeof(VsaSleCommResult);
                ResultInfo[2].Data = (void *)MinSleResult;
                ResultInfo[2].Len = sizeof(VsaSleCommResult);
            }
            else if (IsAlg3GPPStandardType(m_Alg.m_Demod))
            {
                ResultInfo[0].Data = (void *)(Avg3GPPResult);
                ResultInfo[0].Len = sizeof(Vsa3GPPCommResult);
                ResultInfo[1].Data = (void *)(Max3GPPResult);
                ResultInfo[1].Len = sizeof(Vsa3GPPCommResult);
                ResultInfo[2].Data = (void *)(Min3GPPResult);
                ResultInfo[2].Len = sizeof(Vsa3GPPCommResult);
            }
            else
            {
                ResultInfo[0].Data = (void *)AvgResult;
                ResultInfo[0].Len = sizeof(VsaCommResult);
                ResultInfo[1].Data = (void *)MaxResult;
                ResultInfo[1].Len = sizeof(VsaCommResult);
                ResultInfo[2].Data = (void *)MinResult;
                ResultInfo[2].Len = sizeof(VsaCommResult);
            }
        }
    }
    else if (m_CapNum <= 1) //采集次数为0表示当前为多帧平均
    {
        //分析指定帧并获取结果
        Ret = m_Alg.AlzFrameData(Idx - 1);
        if (Ret == WT_OK)
        {
            if (m_Alg.m_Demod == WT_DEMOD_BT)
            {
                Ret = m_Alg.GetBTCommResult(Segment, Stream, &BtResult);
                BtResult1 = BtResult;
                ResultInfo[0].Data = (void *)BtResult1;
                ResultInfo[0].Len = sizeof(VsaBTCommResult);
            }
            else if (m_Alg.m_Demod == WT_DEMOD_GLE)
            {
                Ret = m_Alg.GetSleCommResult(Segment, Stream, &SleResult);
                ResultInfo[0].Data = (void *)SleResult;
                ResultInfo[0].Len = sizeof(VsaSleCommResult);
            }
            else
            {
                Ret = m_Alg.GetCommResult(Segment, Stream, &Result);
                ResultInfo[0].Data = (void *)Result;
                ResultInfo[0].Len = sizeof(VsaCommResult);
            }
        }
    }
    else if (Idx <= m_CapData[Segment].MultiData[0].size())   //指定某一次的结果需要重新分析原始数据来获取
    {
        m_Alg.Clear();

        for (int i = 0; i < 1 + m_SlaveConn.size(); i++)
        {
            auto iter = m_CapData[Segment].MultiData[0].begin();
            std::advance(iter, Idx - 1);
            if(IsWideBandOn() && ThreeData[Segment][i][0].size() >= WIDE_BAND_MAX_CAP_CNT)
            {
                m_Alg.SetData(*iter, Segment, m_CapParam[i], m_CapData[Segment].CalParam[i], m_ExtendEVMStu,
                        ThreeData[Segment][i][0], (int)IsWideBandOn(), CAP_POINT_CNT, CurSpectWideOffset);
            }
            else
            {
                m_Alg.SetData(*iter, Segment, m_CapParam[i], m_CapData[Segment].CalParam[i], m_ExtendEVMStu);
            }
            Ret = m_Alg.AlzFrameData(0);
            if (Ret == WT_OK)
            {
                if (m_Alg.m_Demod == WT_DEMOD_BT)
                {
                    Ret = m_Alg.GetBTCommResult(Segment, Stream, &BtResult);
                    BtResult1 = BtResult;
                    ResultInfo[0].Data = (void *)BtResult1;
                    ResultInfo[0].Len = sizeof(VsaBTCommResult);
                }
                else if (m_Alg.m_Demod == WT_DEMOD_GLE)
                {
                    Ret = m_Alg.GetSleCommResult(Segment, Stream, &SleResult);
                    ResultInfo[0].Data = (void *)SleResult;
                    ResultInfo[0].Len = sizeof(VsaSleCommResult);
                }
                else
                {
                    Ret = m_Alg.GetCommResult(Segment, Stream, &Result);
                    ResultInfo[0].Data = (void *)Result;
                    ResultInfo[0].Len = sizeof(VsaCommResult);
                }
            }
        }
    }
    else
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "index error");
        return WT_ARG_ERROR;
    }

    return Ret;
}

int WTVsa::GetAvgDataComposite(int Segment, BufInfo *ResultInfo)
{
    int Ret = WT_OK;

    if (Segment < 0 || Segment > 2)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "Segment error");
        return WT_ARG_ERROR;
    }

    const VsaBaseResult *AvgResultComposite;
    Ret = m_Alg.GetAvgResultComposite(Segment, &AvgResultComposite);
    if (Ret == WT_OK)
    {
        ResultInfo[0].Data = (void *)AvgResultComposite;
        ResultInfo[0].Len = sizeof(VsaBaseResult);
    }

    return Ret;
}

void WTVsa::FreeAlzData(void)
{
    m_Alg.FreeVsaResult();
}

int WTVsa::GetCurAvgCnt(int &Cnt)
{
    if (m_CapNum <= 1 && m_AvgFrameNum <= 1)
    {
        Cnt = 0;
    }
    else
    {
        m_Alg.GetCurAvgCnt(Cnt);
    }

    return WT_OK;
}

int WTVsa::GetRawData(int Chain, int &SegNum, BufInfo *CalData, BufInfo *CapData)
{
    if (m_Param == nullptr)
    {
        WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "not capture data yet");
        return WT_NO_SIG_DATA;
    }

    if (m_RunStatus != MOD_RUN_FINISH)
    {
        //大于0表示出错了
        return m_RunStatus > 0 ? m_RunStatus : WT_MOD_IS_RUNNING;
    }

    SegNum = !m_CapParam[0].IsAC8080() ? 1 : 2;
    for (int Seg = 0; Seg < SegNum; Seg++)
    {
        if(Chain < 0 || Chain >= m_CapData[Seg].DataNum)
        {
            WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "current stream not exist!");
            return WT_STREAM_ID_ERROR;
        }
        else
        {
            CalData[Seg].Data = &m_CapData[Seg].CalParam[Chain];
            CalData[Seg].Len = sizeof(Rx_Parm);
            m_Alg.GetIQImbParam(Seg, Chain, m_CapData[Seg].CalParam[Chain]);

            CapData[Seg].Data = m_CapData[Seg].DataInfo[Chain].Buf.get();
            CapData[Seg].Len = m_CapData[Seg].DataInfo[Chain].DataLen;
            if(CapData[Seg].Len <= 0 && m_CapData[Seg].MultiData[Chain].size() > 0)
            {
                CapData[Seg].Data = m_CapData[Seg].MultiData[Chain].back().Buf.get();
                CapData[Seg].Len = m_CapData[Seg].MultiData[Chain].back().DataLen;
            }
        }
    }

    return WT_OK;
}

int WTVsa::GetVsaCalParam(int Chain, int &SegNum, Rx_Parm *CalParm)
{
    if (m_Param == nullptr)
    {
        WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "not capture data yet");
        return WT_NO_SIG_DATA;
    }

    if (m_RunStatus != MOD_RUN_FINISH)
    {
        //大于0表示出错了
        return m_RunStatus > 0 ? m_RunStatus : WT_MOD_IS_RUNNING;
    }

    SegNum = !m_CapParam[0].IsAC8080() ? 1 : 2;
    for (int Seg = 0; Seg < SegNum; Seg++)
    {
        if (Chain < 0 || Chain >= m_CapData[Seg].DataNum)
        {
            WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "current stream not exist!");
            return WT_STREAM_ID_ERROR;
        }
        else
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "CalConf::Instance().GetVSAFlatnessComp()= %d\n", CalConf::Instance().GetVSAFlatnessComp());
            if (!CalConf::Instance().GetVSAFlatnessComp() || DigModeLib::Instance().IsDigMode())
            {
                memset(&CalParm[Seg], 0, sizeof(Rx_Parm));
            }
            else
            {
                memcpy(&CalParm[Seg], &m_CapData[Seg].CalParam[Chain], sizeof(Rx_Parm));
            }

            WTLog::Instance().WriteLog(LOG_DEBUG, "GetVsaCalParam seg%d:%d\n", Seg, CalParm->rx_spec_flat_comp_parm.bb_comp_len);
            for (int i = 0; i < CalParm->rx_spec_flat_comp_parm.bb_comp_len; i++)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "%lf\n", CalParm->rx_spec_flat_comp_parm.bb_comp_gain[i]);
            }

            m_Alg.GetIQImbParam(Seg, Chain, CalParm[Seg]);
        }
    }

    return WT_OK;
}

int WTVsa::RecvRawData(Connector *Conn, int Result, int SegNum, BufInfo *CalData, BufInfo *CapData)
{
    if (Result != WT_OK)
    {
        if (Result != WT_MOD_IS_RUNNING)
        {
            m_ExtConn->Response(Result);
            WTLog::Instance().LOGERR(Result, "get capture data from mimo device failed");
        }
        else if (m_RunStatus != MOD_NOT_RUNNING)  //模块正在运行则重新获取
        {
            usleep(100);   //防止等待完成期间过快的反复查询
            Conn->GetVsaData();
        }

        return WT_OK;
    }

    bool Complete = false;  //本次采集是否完成
    auto iter = m_SlaveConn.begin();
    for (int i = 0; i < (signed)m_SlaveConn.size() && (i + 1) < MAX_NUM_OF_CHNNEL; i++, iter++)
    {
        if (*Conn == **iter)
        {
            for(int Seg = 0; Seg < SegNum; Seg++)
            {
                DataBufInfo &DataInfo = m_CapData[Seg].DataInfo[i + 1];

                //将数据copy到buffer中，如果buffer长度过小则重新申请
                if (DataInfo.BufLen < CapData[Seg].Len)
                {
                    DataInfo.Buf.reset(new(std::nothrow) char[CapData[Seg].Len]);
                    if (DataInfo.Buf == nullptr)
                    {
                        DataInfo.BufLen = 0;
                        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
                        return WT_ALLOC_FAILED;
                    }

                    DataInfo.BufLen = CapData[Seg].Len;
                }

                DataInfo.DataLen = CapData[Seg].Len;
                m_CapData[Seg].CalParam[i + 1] = *static_cast<Rx_Parm *>(CalData[Seg].Data);
                memcpy(DataInfo.Buf.get(), CapData[Seg].Data, CapData[Seg].Len);

                if (++m_CapData[Seg].DataNum == 1 + m_SlaveConn.size()) //mimo多次平均时所有仪器都采集完成一次，才算完成一次采集
                {
                    m_CapData[Seg].CapCnt++;
                    Complete = true;
                }

                if (m_CapNum > 1)
                {
                    m_CapData[Seg].MultiData[i + 1].push_back(move(DataInfo));
                }
            }

            break;
        }
    }

    if (Complete)
    {
        if (m_CapNum <= 1 || m_AvgMode != MULTI_CNT_AVG || m_CapData[0].CapCnt >= m_CapNum)
        {
            m_RunStatus = MOD_RUN_FINISH;
        }
        else
        {
            StartAgain();
        }
    }

    return WT_OK;
}

int WTVsa::SaveSignal(int DataType, const std::string &File)
{
    if (!m_AlzFlag)
    {
        m_Alg.Clear();

        for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
        {
            for (int i = 0; i < m_CapData[Seg].DataNum; i++)
            {
                m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                              m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu);
            }
        }

        if (DataType == WT_COMPESATE_DATA)
        {
            m_Alg.AlzFrameData(0);
            m_AlzFlag = true;
        }
    }

    return m_Alg.SaveSignal(DataType, File);
}

int WTVsa::GetRefLevelRange(int Type, double Freq, double &MaxRef, double &MinRef)
{
    // TODO 需要从校准文件中获取
    (void)Type;
    (void)Freq;

    MaxRef = 30;
    MinRef = -30;

    return WT_OK;
}

int WTVsa::SetVsaFlatnessCal(int Enable)
{
    m_Alg.SetVsaFlatnessCal(Enable);
    return WT_OK;
}
int WTVsa::SetVsaIQImbCal(int Enable)
{
    m_Alg.SetVsaIQImbCal(Enable);
    return WT_OK;
}

int WTVsa::CalcIQImp(int Segment, double &Ampl, double &Phase, double &TimeSkew)
{
    if (Segment < 1 || Segment > 2)
    {
        WT_DEBUG(WT_ARG_ERROR, "segment error!");
        return WT_ARG_ERROR;
    }

    return m_Alg.CalcIQImblance(Segment, Ampl, Phase, TimeSkew);
}

int WTVsa::SetStaticIQImb(int Segment, double Ampl, double Phase, double TimeSkew)
{
    if (Segment < 1 || Segment > 2)
    {
        WT_DEBUG(WT_ARG_ERROR, "segment error!");
        return WT_ARG_ERROR;
    }

    m_Alg.SetStaticIQParam(Segment, Ampl, Phase, TimeSkew);
    ClearAvgData(); //配置开启关闭IQ不平衡时，清空平均数据，否则avg不准确

    return WT_OK;
}

int WTVsa::ClrStaticIQImb(int Segment)
{
    if (Segment < 1 || Segment > 2)
    {
        WT_DEBUG(WT_ARG_ERROR, "segment error!");
        return WT_ARG_ERROR;
    }

    m_Alg.ClrStaticIQParam(Segment);
    ClearAvgData(); //配置开启关闭IQ不平衡时，清空平均数据，否则avg不准确

    return WT_OK;
}

int WTVsa::SetSaveRawDataEnable(int Enable)
{
    m_SaveRawDataEnable = Enable;
    return WT_OK;
}

int WTVsa::StartAgain()
{
    int Ret = WT_OK;
    for (auto &Conn : m_SlaveConn)
    {
        if ((Ret = m_ExtConn->StartSlaveVsa(Conn.get())) != WT_OK)
        {
            return Ret;
        }

        m_DevStaMask = 0;
    }

    //MIMO时不在此处启动，从机完成后启动
    if (!IsMIMOMaster())
    {
        Ret = StartAllMod();
    }

    m_LocalStart = true;

    return Ret;
}

int WTVsa::GetRunStatus()
{
    int Status = WT_RX_TX_STATE_ERR_DONE;

    if (m_RunStatus == MOD_NOT_RUNNING)
    {
        //多次采集过程中被停止也认为是完成了
        if (m_AvgMode == MULTI_CNT_AVG && m_CapNum > 1 && m_CapData[0].CapCnt > 0)
        {
            Status = WT_RX_TX_STATE_DONE;
        }
        else
        {
            Status = WT_RX_TX_STATE_ERR_DONE;
        }
    }
    else if (m_RunStatus > 0)
    {
        Status = WT_RX_TX_STATE_ERR_DONE;
    }
    else
    {
        Status = WT_RX_TX_STATE_RUNNING;
    }

    return Status;
}

int WTVsa::AlzBeamformingCalChEstTx(int Demode)
{
    int Ret = WT_OK;

    if (!m_UseFile)
    {
        m_Alg.Clear();

        for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
        {
            for (int i = 0; i < m_CapData[Seg].DataNum; i++)
            {
                m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                              m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu);
            }
        }
    }

    Ret = m_Alg.AlzBeamformingCalChEstDUTTx(Demode);
    return Ret;
}

int WTVsa::BeamformingCalChEstRx(void *Data, int Len)
{
    int Ret = WT_OK;
    Ret = m_Alg.BeamformingCalChEstDUTRx(Data, Len);
    return Ret;
}

int WTVsa::AlzBeamformingResult(void **RstBuf, int &Len)
{
    int Ret =  WT_OK;
    Ret = m_Alg.AlzBeamformingResult(RstBuf, Len);
    return Ret;
}

int WTVsa::AlzBeamformingVerification(double &RstDiffPower)
{
    int Ret = WT_OK;

    if (!m_UseFile)
    {
        m_Alg.Clear();

        for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
        {
            for (int i = 0; i < m_CapData[Seg].DataNum; i++)
            {
                m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                              m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu);
            }
        }
    }

    Ret = m_Alg.AlzBeamformingVerification(RstDiffPower);

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "analysis failed");
    }

    return Ret;
}

int WTVsa::AlzBeamformingCalChProfile(int Demode, void *RstBuf, int &Len)
{
    int Ret = WT_OK;

    if (!m_UseFile)
    {
        m_Alg.Clear();

        for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
        {
            for (int i = 0; i < m_CapData[Seg].DataNum; i++)
            {
                m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                              m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu);
            }
        }
    }

    Ret = m_Alg.AlzBeamformingCalChProfile(Demode, RstBuf, Len);
    return Ret;
}

int WTVsa::AlzBeamformingCalChAmplitudeAngleBCM(void *RstBuf, int &Len)
{
    int Ret =  WT_OK;
    Ret = m_Alg.AlzBeamformingCalChAmplitudeAngleBCM(RstBuf, Len);
    return Ret;
}

int WTVsa::AlzPer(int &FrameResult)
{
    int Ret = WT_OK;

    if (!m_UseFile)
    {
        m_Alg.Clear();

        for (int Seg = 0; Seg < (!m_CapParam[0].IsAC8080() ? 1 : 2); Seg++)
        {
            for (int i = 0; i < m_CapData[Seg].DataNum; i++)
            {
                m_Alg.SetData(m_CapData[Seg].DataInfo[i], Seg,
                              m_CapParam[i], m_CapData[Seg].CalParam[i], m_ExtendEVMStu);
            }
        }
    }

    Ret = m_Alg.AlzPer(FrameResult);
    return Ret;
}

int WTVsa::SetExtralAlzParam(int Demode, int Type, void *Param, int Len)
{
    int Ret = m_Alg.SetExtralAlzParam(Demode, Type, Param, Len);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Extral base alzparam error");
        return Ret;
    }
    return Ret;
}

int WTVsa::SetAnalyseGroup(vector<string> &AlzTypes)
{
    int Ret = WT_OK;

    if(AlzTypes.empty())
    {
        return WT_ERROR;
    }

    Ret = m_Alg.SetAlzGroup(AlzTypes);
    return Ret;
}

int WTVsa::SetResultFilterSetting(void *Param, int Len)
{
    int Ret = m_Alg.SetResultFilterSetting(Param, Len);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set filter error.");
        return Ret;
    }
    return Ret;
}

int WTVsa::CheckFreqValidwhileWideBandOn(int WideBandFlag, double CurFreq)
{
    int iRet = WT_OK;
    if(WideBandFlag)
    {
        double MinFreq = 4900 * 1e6; //5280 * 1e6;
        double MaxFreq = 7300 * 1e6; //6920 * 1e6;  //SCPI已经判断过频点范围，这里就放开最大限制，不做 复杂判断
        //判断当前的频点是否符合规格要求的：wide band规格支持频点范围5280 ~ 6920Mhz
        if(CurFreq < MinFreq || CurFreq > MaxFreq)
        {
            iRet = WT_WIDE_BAND_FREQ_ERROR;
            WTLog::Instance().LOGERR(iRet, "Cur freq can not support wideband function.");
        }
    }
    return iRet;
}

int WTVsa::CheckParamValid(void)
{
    if(WT_OK == CheckFreqValidwhileWideBandOn(IsWideBandSpectEnable, m_Param[0].Freq))
    {
        return true;
    }
    else
    {
        return false;
    }
}

//协议下发配置使能开关
int WTVsa::Set240MSpectOnFlag(Connector *Conn, int Flag)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        //开关打开时，先判断当前的频点是否符合规格要求的：wide band规格支持频点范围5180 ~ 5720Mhz
        if(Flag && m_Param != nullptr)
        {
            Ret = CheckFreqValidwhileWideBandOn(Flag, m_Param[0].Freq);
            if(Ret != WT_OK)
            {
                return Ret;
            }
        }

        //MIMO时需要同时配置从机开启正负240M频谱功能
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "query mimo device failed");
                return Ret;
            }

            Conn->SetRsp(false);
        }

        IsWideBandSpectEnable = Flag;

        //重新配置500M开关时清空500M采集数据
        for(int i = 0; i < MAX_SEGMENT_CNT; i++)
        {
            for(int j = 0; j < MAX_NUM_OF_CHNNEL; j++)
            {
                ThreeData[i][j][0].clear();
                ThreeData[i][j][1].clear();
                CalParam[i][j].clear();
            }
        }
    }
    else
    {
        //所有从机正确时返回状态
        Ret= ProcMimoAck(Conn);
    }

    return Ret;
}

int WTVsa::SetVsaTrigParam(Connector *Conn, VsaTrigParam *TrigParam)
{
    int Ret = WT_OK;
    if (!Conn->IsLinkToSlave())
    {
        // MIMO时需要同时配置从机开启正负240M频谱功能
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "query mimo device failed");
                return Ret;
            }

            Conn->SetRsp(false);
        }

        // GapTime区间 0.1 to 5ms
        TrigParam->GapTime = (TrigParam->GapTime < 1e-7)
                                 ? 1e-7
                                 : ((TrigParam->GapTime > 5e-3)
                                        ? 5e-3
                                        : TrigParam->GapTime);
        TrigParam->Edge = TrigParam->Edge ? 1 : 0;
        m_TrigParam = *TrigParam;
    }
    else
    {
        //所有从机正确时返回状态
        Ret = ProcMimoAck(Conn);
    }
    return Ret;
}

int WTVsa::GetThreeCapRawData(int Chain, int &SegNum, vector<BufInfo> &CalData, vector<BufInfo> &CapData)
{
    if (m_Param == nullptr)
    {
        WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "not capture data yet");
        return WT_NO_SIG_DATA;
    }

    if (m_RunStatus != MOD_RUN_FINISH)
    {
        //大于0表示出错了
        return m_RunStatus > 0 ? m_RunStatus : WT_MOD_IS_RUNNING;
    }

    SegNum = !m_CapParam[0].IsAC8080() ? 1 : 2;
    for (int Seg = 0; Seg < SegNum; Seg++)
    {
        if(Chain < 0 || Chain >= m_CapData[Seg].DataNum)
        {
            WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "current stream not exist!");
            return WT_STREAM_ID_ERROR;
        }
        else
        {
            if(ThreeData[Seg][Chain][0].size() < WIDE_BAND_MAX_CAP_CNT)
            {
                return WT_MOD_IS_RUNNING;
            }
            for(int i = 0; i < ThreeData[Seg][Chain][0].size(); i++)
            {
                BufInfo TmpBuf;
                TmpBuf.Data = ThreeData[Seg][Chain][i][0].Buf.get();
                TmpBuf.Len = ThreeData[Seg][Chain][i][0].DataLen;
                CapData.push_back(TmpBuf);

                BufInfo TmpCalBuf;
                TmpCalBuf.Data =&CalParam[Seg][Chain][i];
                TmpCalBuf.Len = sizeof(Rx_Parm);
                CalData.push_back(TmpCalBuf);
            }
        }
    }

    return WT_OK;
}

int WTVsa::RecvThreeCapRawData(Connector *Conn, int Result, int SegNum, vector<BufInfo> &CalData, vector<BufInfo> &CapData)
{
    if (Result != WT_OK)
    {
        if (Result != WT_MOD_IS_RUNNING)
        {
            m_ExtConn->Response(Result);
            WTLog::Instance().LOGERR(Result, "get capture data from mimo device failed");
        }
        else if (m_RunStatus != MOD_NOT_RUNNING)  //模块正在运行则重新获取
        {
            usleep(100);   //防止等待完成期间过快的反复查询
            Conn->GetSlaveThreeVsaCapData();//开启正负宽频谱时，获取从机WIDE_BAND_MAX_CAP_CNT次采集数据
        }

        return WT_OK;
    }

    bool Complete = false;  //本次采集是否完成
    auto iter = m_SlaveConn.begin();

    for (int i = 0; i < (signed)m_SlaveConn.size() && (i + 1) < MAX_NUM_OF_CHNNEL; i++, iter++)
    {
        if (*Conn == **iter)
        {
            for(int Seg = 0; Seg < SegNum; Seg++)
            {
                ThreeData[Seg][i+1][0].clear();
                CalParam[Seg][i+1].clear();
                for(int j = 0; j < WIDE_BAND_MAX_CAP_CNT; j++)
                {
                    DataBufInfo TmpBuf;
                    TmpBuf.BufLen = CapData[j + WIDE_BAND_MAX_CAP_CNT*Seg].Len;
                    TmpBuf.Buf.reset(new(std::nothrow) char[TmpBuf.BufLen]);
                    memcpy(TmpBuf.Buf.get(), CapData[j + WIDE_BAND_MAX_CAP_CNT*Seg].Data, TmpBuf.BufLen);
                    ThreeData[Seg][i+1][0].push_back(move(TmpBuf));

                    Rx_Parm TmpCal;
                    TmpCal = *static_cast<Rx_Parm *>(CalData[j + WIDE_BAND_MAX_CAP_CNT*Seg].Data);
                    CalParam[Seg][i+1].push_back(TmpCal);
                }

                //保存数据到原来m_CapData中，使其能正常分析，和api获取当前采集的数据
                DataBufInfo &DataInfo = m_CapData[Seg].DataInfo[i + 1];

                //将数据copy到buffer中，如果buffer长度过小则重新申请
                if (DataInfo.BufLen < ThreeData[Seg][i+1][0][0].BufLen)
                {
                    DataInfo.Buf.reset(new(std::nothrow) char[ThreeData[Seg][i+1][0][0].BufLen]);
                    if (DataInfo.Buf == nullptr)
                    {
                        DataInfo.BufLen = 0;
                        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
                        return WT_ALLOC_FAILED;
                    }

                    DataInfo.BufLen = ThreeData[Seg][i+1][0][0].BufLen;
                }

                DataInfo.DataLen = ThreeData[Seg][i+1][0][0].BufLen;
                memcpy(DataInfo.Buf.get(), ThreeData[Seg][i+1][0][0].Buf.get(), ThreeData[Seg][i+1][0][0].BufLen);
                m_CapData[Seg].CalParam[i + 1] = CalParam[Seg][i+1][0];
                if (m_CapNum > 1)
                {
                    m_CapData[Seg].MultiData[i + 1].push_back(move(DataInfo));
                }

                if (++m_CapData[Seg].DataNum == 1 + m_SlaveConn.size()) //mimo多次平均时所有仪器都采集完成一次，才算完成一次采集
                {
                    m_CapData[Seg].CapCnt++;
                    Complete = true;
                }
            }
            break;
        }
    }

    if (Complete)
    {
        if (m_CapNum <= 1 || m_AvgMode != MULTI_CNT_AVG || m_CapData[0].CapCnt >= m_CapNum)
        {
            m_RunStatus = MOD_RUN_FINISH;
        }
        else
        {
            StartAgain();
        }
    }
    return WT_OK;
}

int WTVsa::GetCurBandwidth(void)
{
    int Demode = m_Param[0].GetDemode();
    int bandWidth = 20;

    switch (Demode)
    {
    case WT_DEMOD_11N_40M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BA_40M:
    case WT_DEMOD_11AZ_40M:
        bandWidth = 40;
        break;
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BA_80M:
    case WT_DEMOD_11AZ_80M:
        bandWidth = 80;
        break;
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AC_80_80M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11AZ_160M:
        bandWidth = 160;
        break;
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        bandWidth = 320;
        break;
    default:
        bandWidth = 20;
        break;
    }

    return bandWidth;
}
bool WTVsa::IsWideBandOn(void)
{
#ifdef WT418_FW
    return IsWideBandSpectEnable > 0 && m_Param[0].SamplingFreq > 120*1e6;   //328CE 宽频谱只在采样率大于等于240M时支持。且频点在5180 * 1e6~7020 * 1e6内;
#else
    return (IsWideBandSpectEnable == 1 || (IsWideBandSpectEnable == 2 && GetCurBandwidth() > 80)) && m_Param[0].SamplingFreq > 240*1e6;   //宽频谱只在采样率等于480M时支持。且频点在5280 * 1e6~6920 * 1e6内;
#endif
}

void WTVsa::SaveStackData(void)
{
    m_Alg.SaveStackDataFile();
}

void WTVsa::BackUpVsaParam(void)
{
    if (m_Param == nullptr || !IsWideBandOn())
    {
        return ;
    }
    memcpy(&BakParam, &m_Param[0], sizeof(VsaParam));
    memcpy(&BakCapParam, &m_CapParam[0], sizeof(VsaParam));
}

void WTVsa::HwOpFinPolling(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    if (m_HwOpFinPolling == 0)
    {
        m_HwOpFinPolling = 1;
    }
    else
    {
        for (auto iter = m_Mods.rbegin(); iter != m_Mods.rend(); iter++)
        {
            if (iter->Status == WT_RX_TX_STATE_RUNNING && iter->IsConfig == true)
            {
                DevMgr::Instance().HwOpFinMod(DEV_TYPE_VSA, iter->ModId);
            }
        }
    }
}

int WTVsa::StartTBTAp(Connector *Conn)
{
    int Ret = WT_OK;

    //检查TFlicense,tb mode需要同时存在tb和tf license，tb license在分析时检查
    if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AC) != WT_OK)
    {
        Ret = WT_LIC_NOT_EXIST;
        WTLog::Instance().LOGERR(Ret, "TF license not exist");
        return Ret;
    }

    if ((Ret = GetVsa().CheckTBTParam(TBT_MODE_AP)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsa parameter error, can not start Tb mode");
        return Ret;
    }

    if ((Ret = GetVsg().CheckTBTParam(TBT_MODE_AP)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsg parameter error, can not start Tb mode");
        return Ret;
    }

    if (DigModeLib::Instance().IsDigMode())
    {
        //允许重新启动
        Ret = GetVsa().StopRunningMod();
        Ret = GetVsg().StopRunningMod();

        if (GetVsg().GetDigConfig() == false)
        {
            Ret = GetVsg().SetDig();
            RetAssert(Ret, "Dig mode set param failed");
        }
        if (GetVsa().GetDigConfig() == false)
        {
            Ret = GetVsa().SetDig();
            RetAssert(Ret, "Dig mode set param failed");
        }

        m_CurDataNoAlzFlag = true;
        m_UseFile = false;
        Ret = GetDigLib().Start(DIG_STRAR_TBT_AP);
        if (Ret == WT_OK)
        {
            GetVsg().SetRunStatusFlag(MOD_NOT_RUNNING);
            GetVsa().SetRunStatusFlag(MOD_RUNNING);
        }
        return Ret;
    }

    //主机连接上位机的连接
    if (!Conn->IsLinkToSlave())
    {
        m_LocalStart = false;
        ClearCurFlowResult();

        //允许重新启动
        GetVsa().StopRunningMod();
        GetVsg().StopRunningMod();

        //MIMO模式下需要启动从机
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "start mimo device failed");
                return Ret;
            }
            Conn->SetRsp(false);
            m_StopSlave = 0;
        }

        //设置Tb模式标志
        GetVsa().SetTBTMode(TBT_MODE_AP);
        GetVsg().SetTBTMode(TBT_MODE_AP);

        //此时开始申请并配置硬件模块
        Ret = GetVsg().AllocSetMod(false);
        if (Ret != WT_OK)
        {
            SetCurFlowResult(Ret);
            StopSlave(Ret, CMD_START_TBT_AP);
            return Ret;
        }

        //此时开始申请并配置硬件模块
        Ret = GetVsa().AllocSetMod(false);
        if (Ret != WT_OK)
        {
            SetCurFlowResult(Ret);
            StopSlave(Ret, CMD_START_TBT_AP);
            return Ret;
        }

        //MIMO模式下主机需要等待从机启动完成后才能启动
        if (!IsMIMOMaster())
        {
            if(GetVsg().GetModInfo()->empty() || GetVsa().GetModInfo()->empty())
            {
                StopSlave(WT_MOD_NUM_ERROR, CMD_START_TBT_AP);
                return WT_MOD_NUM_ERROR;
            }

            GetVsa().SetTBPairMod(GetVsg().GetModInfo()->at(0).ModId);
            GetVsg().SetTBPairMod(GetVsa().GetModInfo()->at(0).ModId);
            GetVsg().ClearTBTModeFinish();

            //启动完成后立即返回响应
            Ret = GetVsa().StartAllMod();
            if (Ret != WT_OK)
            {
                StopSlave(Ret, CMD_START_TBT_AP);
                return Ret;
            }

            Ret = GetVsg().StartAllMod();
            if (Ret != WT_OK)
            {
                StopSlave(Ret, CMD_START_TBT_AP);
                return Ret;
            }
        }
    }
    else
    {
        ProcMimoAck(Conn, false);
        Ret = Conn->GetCmdResult();
        if (Ret == WT_OK)
        {
            if (m_LocalStart) //本地启动时命令不match，不会更新m_DevStaMask，需要手动更新
            {
                auto iter = m_SlaveConn.begin();
                for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
                {
                    if (*Conn == **iter)
                    {
                        m_DevStaMask |= 1 << i;
                        break;
                    }
                }
            }

            //收到从机的启动响应后再启动主机
            if (IsMimoDone() && GetCurFlowResult() == WT_OK)
            {
                usleep(200); //延时保证从机FPGA的运行完成

                if(GetVsg().GetModInfo()->empty() || GetVsa().GetModInfo()->empty())
                {
                    StopSlave(WT_MOD_NUM_ERROR, CMD_START_TBT_AP);
                    return WT_MOD_NUM_ERROR;
                }

                GetVsa().SetTBPairMod(GetVsg().GetModInfo()->at(0).ModId);
                GetVsg().SetTBPairMod(GetVsa().GetModInfo()->at(0).ModId);
                GetVsg().ClearTBTModeFinish();

                Ret = GetVsa().StartAllMod();
                if (Ret != WT_OK)
                {
                    StopSlave(Ret, CMD_START_TBT_AP);
                    return Ret;
                }

                Ret = GetVsg().StartAllMod();
                if (Ret != WT_OK)
                {
                    StopSlave(Ret, CMD_START_TBT_AP);
                    return Ret;
                }

                if (!m_LocalStart)
                {
                    Ret = m_ExtConn->Response(Ret);
                }

                if (Ret == WT_OK)
                {
                    Ret = MimoStartFin();
                }
            }
        }
        else
        {
            StopSlave(Ret, CMD_START_TBT_AP);
        }
    }
    return Ret;
}

//配置参数给硬件模块
void WTVsa::SetStop()
{
    if (DigModeLib::Instance().IsDigMode())
    {
        if (m_RunStatus == MOD_RUNNING)
        {
            m_Alg.Clear();
        }
        GetDigLib().Stop(DEV_TYPE_VSA);
        GetVsa().SetDigConfig(false);
        GetVsa().SetRunStatusFlag(MOD_NOT_RUNNING);

        if (GetVsg().IsTBTMode() || GetVsa().IsTBTMode())
        {
            GetDigLib().Stop(DEV_TYPE_VSG);
            GetVsg().SetDigConfig(false);
            GetVsg().SetRunStatusFlag(MOD_NOT_RUNNING);
        }
    }
    else
    {
        bool IsClear = false;
        for (auto &Mod : m_Mods)
        {
            if (false == IsClear && Mod.Status == WT_RX_TX_STATE_RUNNING)
            {
                m_Alg.Clear();
                IsClear = true;
            }
            GetVsa().StopMod(Mod);
        }
        GetVsa().SetRunStatusFlag(MOD_NOT_RUNNING);
        m_Mods.clear();

        // TBT模式也需停止VSG
        if (GetVsg().IsTBTMode() || GetVsa().IsTBTMode())
        {
            std::vector<ModInfo> *VsgModInfo = GetVsg().GetModInfo();
            for (auto &Mod : *VsgModInfo)
            {
                GetVsg().StopMod(Mod);
            }
            GetVsg().SetRunStatusFlag(MOD_NOT_RUNNING);
            VsgModInfo->clear();
        }
    }
}

int WTVsa::StopTBTAp(Connector *Conn)
{
    if (DigModeLib::Instance().IsDigMode())
    {
        int Ret = WT_OK;
        Ret |= GetDigLib().Stop(DEV_TYPE_VSG);
        GetVsg().SetDigConfig(false);
        GetVsg().SetRunStatusFlag(MOD_NOT_RUNNING);

        Ret |= GetDigLib().Stop(DEV_TYPE_VSA);
        GetVsa().SetDigConfig(false);
        GetVsa().SetRunStatusFlag(MOD_NOT_RUNNING);
        return Ret;
    }
    return Stop(Conn);
}

int WTVsa::GetTBTApStatus(Connector *Conn, int &Status)
{
    int Ret = WT_OK;
    GetVsg().QueryStatus(Conn, Status);
    //WTLog::Instance().WriteLog(LOG_DEBUG, "=========Get vsg TBTApStatus=%d, Ret=%#x\n", Status, Ret);
    if (Status != WT_RX_TX_STATE_DONE)
    {
        return Ret;
    }
    else
    {
        Ret = GetVsa().QueryStatus(Conn, Status);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "=========Get vsa TBTApStatus=%d, Ret=%#x\n", Status, Ret);
        return Ret;
    }
}

int WTVsa::CheckTBTParam(int mode)
{
    if (m_Param.get() == nullptr || m_ParamNum == 0)
    {
        WTLog::Instance().LOGERR(WT_NEED_SET_MOD, "Vsa NO config parameter");
    }
    else if (m_Param[0].IsAC8080())
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsa parameter can not be 8080");
    }
    else if (m_Param[0].Type != TEST_SISO && m_Param[0].Type != TEST_SELF_MIMO && m_Param[0].Type != TEST_MULTI_MIMO)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "Vsa parameter Type Error");
    }
    else if (m_CapNum != 1)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "Vsa can not use avgrage");
    }
    else
    {
        if (!DigModeLib::Instance().IsDigMode())
        {
            if (Basefun::CompareDouble(m_Param[0].SamplingFreq, MAX_SMAPLE_RATE))
            {
                WTLog::Instance().LOGERR(WT_ARG_ERROR, "Vsa parameter SamplingFreq must be default");
                return WT_ARG_ERROR;
            }

            if (mode == TBT_MODE_AP)
            {
                // vsg-vsa Vsa TrigType must be FREE_RUN
                m_Param[0].TrigType = WT_TRIG_TYPE_FREE_RUN;
            }
            else if (mode == TBT_MODE_STA)
            {
                // vsa-vsg  Vsa TrigType must be Trig
                m_Param[0].TrigType = WT_TRIG_TYPE_IF;
            }
            else if (mode == TBT_MODE_AGC)
            {
                // rf inter agc Vsa TrigType must be Trig
                m_Param[0].TrigType = WT_TRIG_TYPE_IF;
            }
        }
        return WT_OK;
    }
    return WT_ARG_ERROR;
}

int WTVsa::AutoRangeTBTAp(Connector *Conn, void *Param)
{
    int Ret = WT_OK;

    if (DigModeLib::Instance().IsDigMode())
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        WTLog::Instance().LOGERR(Ret, "Dig mode does not support agc");
        return Ret;
    }

    if ((Ret = GetVsa().CheckTBTParam(TBT_MODE_AGC)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsa parameter error, can not start Tb mode");
        return Ret;
    }

    if ((Ret = GetVsg().CheckTBTParam(TBT_MODE_AGC)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsg parameter error, can not start Tb mode");
        return Ret;
    }

    if (!Conn->IsLinkToSlave())
    {
        m_LocalStart = false;
        ClearCurFlowResult();

        //允许重新启动
        GetVsa().StopRunningMod();
        GetVsg().StopRunningMod();

        m_RetryCnt = 0;
        m_RetryCnt2 = 0;
        m_AutoRange.reset(new(std::nothrow) VsaAutoRange);
        if (m_AutoRange == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc autorange info failed");
            return WT_ALLOC_FAILED;
        }
        m_AutoRange->SetSmpTime(m_AgcSamplingTime);

        m_AutoRange2.reset(new(std::nothrow) VsaAutoRange);
        if (m_AutoRange2 == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc autorange info failed");
            return WT_ALLOC_FAILED;
        }
        m_TrigType2 = WT_TRIG_TYPE_IF;
        m_AutoRange2->SetCompleteState(AGC_COMPLETE_PASS);
        m_AutoRange2->SetSmpTime(m_AgcSamplingTime);

        //MIMO模式下需要启动从机
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "start mimo device failed");
                return Ret;
            }
            Conn->SetRsp(false);
            m_StopSlave = 0;
        }

        // 到这里时从机已经开始工作了, 所以要开始记录错误码了
        SetAgcingState(true);
        SetTBTApAgcingState(true);
        m_ExtConn->StartSilent();   // Conn 开始静默 也就是AGC加锁, 不响应其他的任何外部命令了
        m_AutoRange->SetAndBackupParam(m_Param[0]);
        GetVsg().SetAndBackupParamTb();
        do
        {
            //此时开始申请并配置硬件模块
            Ret = GetVsg().AllocSetMod(false);
            CheckBreak(Ret);

            //此时开始申请并配置硬件模块
            Ret = GetVsa().AllocSetMod(false);
            CheckBreak(Ret);

            if (GetVsg().GetModInfo()->empty() || GetVsa().GetModInfo()->empty())
            {
                Ret = WT_MOD_NUM_ERROR;
                CheckBreak(Ret);
            }
            Ret = GetVsg().StartAllMod();
            CheckBreak(Ret);

            //延时，以确保VSG信号稳定。
            usleep(10000);

            Ret = GetVsa().StartAllMod();
            CheckBreak(Ret);
        } while (0);

        if (Ret != WT_OK)
        {
            SetCurFlowResult(Ret);
            // 主机未执行AGC的参数还原
            m_AutoRange->RestoreParam(m_Param[0], false);
            GetVsg().StopRunningMod();
            GetVsg().RestoreParam();
            // 单机SISO就要设置立即返回响应
            if (!IsMIMOMaster())
            {
                SetAgcingState(false);
                m_ExtConn->CancelSilent();
                Conn->SetRsp(true);
            }
            return Ret;     // 注意这个错误不会立刻返回给上位机，因为从机已经运行了, 还需要等待从机完成.
        }

        Conn->SetRsp(false);
        SetFinAgent(bind(&WTVsa::AutoRangeProc, this, _1));

        AddAGCWaitTrigger(TB_TF_AGC_WAIT_TIME_EXT + m_Param[0].SamplingTime + GetVsg().GetVsgPeriod(), 0);
    }
    else    // 主机收到从机的ACK
    {
        ProcMimoAck(Conn, false);
        Ret = Conn->GetCmdResult();

        if (Ret == WT_OK)   // 当前从机返回成功的ACK
        {
            Param = (char *)Param + sizeof(int);
            auto iter = m_SlaveConn.begin();
            for (int i = 0; i < (signed)m_SlaveConn.size(); i++, iter++)
            {
                if (**iter == *Conn)
                {
                    m_Param[i+1] = *static_cast<VsaParam *>(Param);
                }
            }

            if (IsMimoDone())   // MIMO都完成, 当前ACK是最后一个MIMO从机
            {
                // 主机完成(可能有错误码)
                if (m_AutoRange->IsComplete())
                {
                    if (GetCurFlowResult() == WT_OK)    // 查看有没错误码
                    {
                        Ret = AutoRangeResponse();      // AGC 完成流程(OK, 主机先完成, 从机后完成)
                    }
                    else // 主从都已经完成, 但是有错误发生
                    {
                        m_ExtConn->Response(GetCurFlowResult()); // AGC 完成流程(Failed, 最后的从机是成功)
                    }

                    ClearCurFlowResult();
                    SetAgcingState(false);
                    m_ExtConn->CancelSilent();
                }
                else // 主机未完成, Response让主机执行
                {
                }
            }
            else    // 还有从机没有完成
            {
            }
        }
        else    // 从机失败
        {
            // 当期从机是最后完成
            if (IsMimoDone() && m_AutoRange->IsComplete() && m_AutoRange2->IsComplete())
            {
                SetCurFlowResult(Ret);
                m_ExtConn->Response(GetCurFlowResult()); // AGC 完成流程(Failed, 最后的从机是失败)

                ClearCurFlowResult();
                SetAgcingState(false);
                m_ExtConn->CancelSilent();
            }
            else    // 还有从机 或者 还有主机 没有完成
            {
                SetCurFlowResult(Ret);
            }
        }
    }
    return Ret;
}


int WTVsa::StartTBTSta(Connector *Conn)
{
    int Ret = WT_OK;

    //检查TFlicense,tb mode需要同时存在tb和tf license，tb license在分析时检查
    if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AC) != WT_OK)
    {
        Ret = WT_LIC_NOT_EXIST;
        WTLog::Instance().LOGERR(Ret, "TF license not exist");
        return Ret;
    }

    if ((Ret = GetVsa().CheckTBTParam(TBT_MODE_STA)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsa parameter error, can not start Tb mode");
        return Ret;
    }

    if ((Ret = GetVsg().CheckTBTParam(TBT_MODE_STA)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsg parameter error, can not start Tb mode");
        return Ret;
    }

    if (DigModeLib::Instance().IsDigMode())
    {
        //允许重新启动
        Ret = GetVsa().StopRunningMod();
        Ret = GetVsg().StopRunningMod();

        if (GetVsg().GetDigConfig() == false)
        {
            Ret = GetVsg().SetDig();
            RetAssert(Ret, "Dig mode set param failed");
        }
        if (GetVsa().GetDigConfig() == false)
        {
            Ret = GetVsa().SetDig();
            RetAssert(Ret, "Dig mode set param failed");
        }

        m_CurDataNoAlzFlag = true;
        m_UseFile = false;
        Ret = GetDigLib().Start(DIG_STRAR_TBT_STA);
        if (Ret == WT_OK)
        {
            GetVsg().SetRunStatusFlag(MOD_NOT_RUNNING);
            GetVsg().SetRunStatusFlag(MOD_RUNNING);
        }
        return Ret;
    }

    if (!Conn->IsLinkToSlave())
    {
        m_LocalStart = false;
        ClearCurFlowResult();

        //允许重新启动
        GetVsa().StopRunningMod();
        GetVsg().StopRunningMod();

        //MIMO模式下需要启动从机
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "start mimo device failed");
                return Ret;
            }
            Conn->SetRsp(false);
            m_StopSlave = 0;
        }

        //设置Tb模式标志
        GetVsa().SetTBTMode(TBT_MODE_STA);
        GetVsg().SetTBTMode(TBT_MODE_STA);

        //此时开始申请并配置硬件模块
        Ret = GetVsg().AllocSetMod(false);
        if (Ret != WT_OK)
        {
            SetCurFlowResult(Ret);
            StopSlave(Ret, CMD_START_TBT_STA);
            return Ret;
        }

        //此时开始申请并配置硬件模块
        Ret = GetVsa().AllocSetMod(false);
        if (Ret != WT_OK)
        {
            SetCurFlowResult(Ret);
            StopSlave(Ret, CMD_START_TBT_STA);
            return Ret;
        }

        //MIMO模式下主机需要等待从机启动完成后才能启动
        if (!IsMIMOMaster())
        {
            if(GetVsg().GetModInfo()->empty() || GetVsa().GetModInfo()->empty())
            {
                StopSlave(WT_MOD_NUM_ERROR, CMD_START_TBT_STA);
                return WT_MOD_NUM_ERROR;
            }

            GetVsa().SetTBPairMod(GetVsg().GetModInfo()->at(0).ModId);
            GetVsg().SetTBPairMod(GetVsa().GetModInfo()->at(0).ModId);
            GetVsg().ClearTBTModeFinish();

            Ret = GetVsg().StartAllMod();
            if (Ret != WT_OK)
            {
                StopSlave(Ret, CMD_START_TBT_STA);
                return Ret;
            }

            //启动完成后立即返回响应
            Ret = GetVsa().StartAllMod();
            if (Ret != WT_OK)
            {
                StopSlave(Ret, CMD_START_TBT_STA);
                return Ret;
            }
        }
    }
    else
    {
        ProcMimoAck(Conn, false);
        Ret = Conn->GetCmdResult();
        if (Ret == WT_OK)
        {
            if (m_LocalStart) //本地启动时命令不match，不会更新m_DevStaMask，需要手动更新
            {
                auto iter = m_SlaveConn.begin();
                for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
                {
                    if (*Conn == **iter)
                    {
                        m_DevStaMask |= 1 << i;
                        break;
                    }
                }
            }

            //收到从机的启动响应后再启动主机
            if (IsMimoDone() && GetCurFlowResult() == WT_OK)
            {
                usleep(200); //延时保证从机FPGA的运行完成

                if(GetVsg().GetModInfo()->empty() || GetVsa().GetModInfo()->empty())
                {
                    StopSlave(WT_MOD_NUM_ERROR, CMD_START_TBT_STA);
                    return WT_MOD_NUM_ERROR;
                }

                GetVsa().SetTBPairMod(GetVsg().GetModInfo()->at(0).ModId);
                GetVsg().SetTBPairMod(GetVsa().GetModInfo()->at(0).ModId);
                GetVsg().ClearTBTModeFinish();

                Ret = GetVsg().StartAllMod();
                if (Ret != WT_OK)
                {
                    StopSlave(Ret, CMD_START_TBT_STA);
                    return Ret;
                }

                Ret = GetVsa().StartAllMod();
                if (Ret != WT_OK)
                {
                    StopSlave(Ret, CMD_START_TBT_STA);
                    return Ret;
                }

                if (!m_LocalStart)
                {
                    Ret = m_ExtConn->Response(Ret);
                }

                if (Ret == WT_OK)
                {
                    Ret = MimoStartFin();
                }
            }
        }
        else
        {
            StopSlave(Ret, CMD_START_TBT_STA);
        }
    }
    return Ret;
}

int WTVsa::StopTBTSta(Connector *Conn)
{
    if (DigModeLib::Instance().IsDigMode())
    {
        int Ret = WT_OK;
        Ret |= GetDigLib().Stop(DEV_TYPE_VSA);
        GetVsa().SetDigConfig(false);
        GetVsa().SetRunStatusFlag(MOD_NOT_RUNNING);

        Ret |= GetDigLib().Stop(DEV_TYPE_VSG);
        GetVsg().SetDigConfig(false);
        GetVsg().SetRunStatusFlag(MOD_NOT_RUNNING);
        return Ret;
    }
    return Stop(Conn);
}

int WTVsa::GetTBTStaStatus(Connector *Conn, int &Status)
{
    int Ret = WT_OK;
    QueryStatus(Conn, Status);
    //WTLog::Instance().WriteLog(LOG_DEBUG, "=========Get vsa TBTStaStatus=%d, Ret=%#x\n", Status, Ret);
    if (Status != WT_RX_TX_STATE_DONE)
    {
        return Ret;
    }
    else
    {
        Ret = GetVsg().QueryStatus(Conn, Status);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "=========Get vsg TBTStaStatus=%d, Ret=%#x\n", Status, Ret);
        return Ret;
    }
}

int WTVsa::SetTBTStaParam(const int Delay)
{
    int Ret = WT_OK;
    for (auto &Mod : m_Mods)
    {
        Ret |= DevLib::Instance().VSASetTBTStaParam(Mod.ModId, Delay);
    }
    return WT_OK;
}

int WTVsa::CheckSlaveBusinessLic(void)
{
    int Ret = WT_OK;

    if(m_SlaveConn.size() == 0) //没有从机，不作处理
    {
        return WT_OK;   //分析不提示lic错误，最终只是结果不完整
    }

    for (int i = 0; i < (int)m_SlaveConn.size(); i++)
    {
        //WTLog::Instance().WriteLog(LOG_DEBUG, "m_SlaveLicInfo[%d].size = %d\n",i, (int)m_SlaveLicInfo[i].size());
        Ret = m_Alg.CheckSlaveDemodLic(m_SlaveLicInfo[i]);
        if(Ret != WT_OK)
        {
            break;
        }
    }
    return WT_OK;   //分析不提示lic错误，最终只是结果不完整
}

int WTVsa::RecvSlaveLicInfo(Connector *Conn, int Result, void *Data, int DataLen)
{
    (void)Result;
    auto iter = m_SlaveConn.begin();
    //WTLog::Instance().WriteLog(LOG_DEBUG, "RecvSlaveLicInfo m_SlaveConn.size() = %d\n",(int)m_SlaveConn.size());
    for (int i = 0; i < (signed)m_SlaveConn.size(); i++, iter++)
    {
        if (*Conn == **iter)
        {
            //获取对应从机license
            int Cnt = DataLen / sizeof(LicItemInfo);
            LicItemInfo *Info = (LicItemInfo *)Data;
            m_SlaveLicInfo[i].clear();

            //WTLog::Instance().WriteLog(LOG_DEBUG, "Get Slave i=%d,lic cnt = %d\n",i, Cnt);
            for(int j = 0; j < Cnt; j++)
            {
                //WTLog::Instance().WriteLog(LOG_DEBUG, "lic type =%d,lic value = %d,resnum=%d\n",Info->LicType, Info->LicValue, Info->ResourceNum);
                m_SlaveLicInfo[i].push_back(move(*Info));
                Info++;
            }
            break;
        }
    }
    return WT_OK;
}

int WTVsa::SetDigParam(const DigParam &Param)
{
    auto &VsaCh = Param.VSAChannelIdList;
    auto &VsgCh = Param.VSGChannelIdList;

#define PoutC(n)                  #n<<"="<<(int)n<<","
#define PoutCh(Ch) \
    PoutC(Ch[0]) << PoutC(Ch[1]) << PoutC(Ch[2]) << PoutC(Ch[3]) << PoutC(Ch[4]) << PoutC(Ch[5]) << PoutC(Ch[6]) << PoutC(Ch[7]) << "\n"

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DigParam:{\n"
         << "\t" << PoutN(Param.TriggerTimeout)
         << "\t" << PoutN(Param.RecvTimeout)
         << "\t" << PoutN(Param.VSGISTimeout)
         << "\t" << PoutN(Param.VSAISTimeout)
         << std::hex
         << "\t" << PoutN(Param.VsaActionMask)
         << "\t" << PoutN(Param.VsgActionMask)
         << std::dec
         << "\t" << PoutN(Param.VsaMaxBitCnt)
         << "\t" << PoutN(Param.VsgMaxBitCnt)
         << "\t" << PoutCh(VsaCh)
         << "\t" << PoutCh(VsgCh)
         << "\t}" << endl;

    if (Basefun::CompareDouble(m_DigParam.RecvTimeout, Param.RecvTimeout, 10e-9) ||
        Basefun::CompareDouble(m_DigParam.VSAISTimeout, Param.VSAISTimeout, 10e-9) ||
        m_DigParam.VsaMaxBitCnt != Param.VsaMaxBitCnt ||

        m_DigParam.VsaActionMask != Param.VsaActionMask ||
        memcmp(m_DigParam.VSAChannelIdList, Param.VSAChannelIdList, sizeof(m_DigParam.VSAChannelIdList)))
    {
        GetVsa().SetDigConfig(false);
    }

    if (Basefun::CompareDouble(m_DigParam.TriggerTimeout, Param.TriggerTimeout, 10e-9) ||
        Basefun::CompareDouble(m_DigParam.VSGISTimeout, Param.VSGISTimeout, 10e-9) ||
        m_DigParam.VsgActionMask != Param.VsgActionMask ||
        m_DigParam.VsgMaxBitCnt != Param.VsgMaxBitCnt ||
        memcmp(m_DigParam.VSGChannelIdList, Param.VSGChannelIdList, sizeof(m_DigParam.VSGChannelIdList)) ||
        memcmp(m_DigParam.DstMac, Param.DstMac, sizeof(m_DigParam.DstMac)))
    {
        GetVsg().SetDigConfig(false);
    }

    SetChannelIdList(Param.VSAChannelIdList, sizeof(Param.VSAChannelIdList) / sizeof(Param.VSAChannelIdList[0]));
    GetVsg().SetChannelIdList(Param.VSGChannelIdList, sizeof(Param.VSGChannelIdList) / sizeof(Param.VSGChannelIdList[0]));

    m_DigParam = Param;
    if (m_DigParam.VSAISTimeout > m_DigParam.RecvTimeout)
    {
        m_DigParam.VSAISTimeout = m_DigParam.RecvTimeout;
    }
    if (m_DigParam.VSGISTimeout > m_DigParam.TriggerTimeout)
    {
        m_DigParam.VSGISTimeout = m_DigParam.TriggerTimeout;
    }
    GetDigLib().SetDstMac((uint8_t *)m_DigParam.DstMac);
    return WT_OK;
}

int WTVsa::CheckCalParam(int mode)
{
    (void)mode;
    if (m_Param.get() == nullptr || m_ParamNum == 0)
    {
        WTLog::Instance().LOGERR(WT_NEED_SET_MOD, "Vsa NO config parameter");
    }
    else if (m_Param[0].IsAC8080())
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsa parameter can not be 8080");
    }
    else if (m_Param[0].TrigType != WT_TRIG_TYPE_FREE_RUN)
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsa must be free run");
    }
    else if (Basefun::CompareDouble(m_Param[0].SamplingFreq, 7500000) && Basefun::CompareDouble(m_Param[0].SamplingFreq, 15000000))
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsa SamplingFreq must be 7.5M or 15M");
    }
    else if (m_Mods.size() != 1)
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsa m_Mods.size must be 1");
    }
    else if (!m_Mods[0].IsConfig)
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsa Mods not config");
    }
    else if (GetVsg().GetModInfo()->size() > 0 &&
             (GetVsg().GetModInfo()->at(0).ModId != GetVsa().GetModInfo()->at(0).ModId))
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "VsaPort and VsgPort not in same unit");
    }
    return WT_OK;
}

//返回true表示已完成处理过程，硬件资源可释放，否则资源不可释放
bool WTVsa::ATTCalProc(int ModId)
{
    int Ret = WT_OK;
    bool avg_complete = false;
    int ModIdx = GetModIndex(ModId);
    do
    {
        CheckRet(Ret);
        int Status = m_Mods[ModIdx].Status;
        if (Status == WT_RX_TX_STATE_DONE) //采集完成
        {
            m_ATTCalStatus.Capture_count++;
            //通知WaitTrig，消除超时计时，本单元已经采集完成
            DeleteAGCWaitTrigger(ModIdx);
            auto &DataInfo = m_CapData[ModIdx].DataInfo[0];
            Ret = GetCaptureData(m_Mods[ModIdx].ModId, DataInfo);
            CheckBreak(Ret);
            Ret = AlzSingleData(0);
            CheckBreak(Ret);
            {
                //获取结果
                bool ready_to_avg = false;
                void *DataBuf = nullptr;
                int DataSize;
                int DataType;
                //取y轴功率值(double *)DataBufWT_RES_SPECTRUM_Y
                Ret = m_Alg.GetVsaResult("y", 0, 0, &DataBuf, DataSize, DataType);
                CheckBreak(Ret);
                double *Datadouble = (double *)((char *)DataBuf); //转化成数组
                int FreqOffset_point;
                FreqOffset_point = DataSize / DataType / 2 + m_ATTCalStatus.Cfg.FreqOffset / 1e5; //频谱图的频率点间距为 1e5 Hz
#ifdef DEBUG
                {                                                                                 //内容输出
                    // for (int i = 0; i < DataSize / DataType; i++)//输出每个点的功率值
                    //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "power=[" << i << "] = " << Datadouble[i] << endl;
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "============================"
                         << "DataSize=" << DataSize
                         << "DataType=" << DataType
                         << "============================"
                         << endl;
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "count=" << DataSize / DataType << endl
                         << "m_CapParam.SamplingTime" << m_CapParam[0].SamplingTime << endl
                         << "m_CapParam.SamplingFreq" << m_CapParam[0].SamplingFreq << endl
                         << "DataBuf[" << FreqOffset_point << "] = " << Datadouble[FreqOffset_point] << endl
                         << endl;
                }
#endif
                if (m_ATTCalStatus.Capture_count <= m_ATTCalStatus.Cfg.AvgCountMax) //已采集足够数据
                {
                    if (m_ATTCalStatus.Capture_count >= m_ATTCalStatus.Cfg.AvgCount) //已采集足够数据
                    {
                        if ((abs( //将最早两包进行判断
                                 m_ATTCalStatus.power_ready_to_avg[(m_ATTCalStatus.Capture_count) % m_ATTCalStatus.Cfg.AvgCount] -
                                 m_ATTCalStatus.power_ready_to_avg[(m_ATTCalStatus.Capture_count + 1) % m_ATTCalStatus.Cfg.AvgCount]) < m_ATTCalStatus.Cfg.AvgDiff) ||
                                m_ATTCalStatus.Capture_count == m_ATTCalStatus.Cfg.AvgCountMax)
                        {
                            {
                                if (m_ATTCalStatus.Capture_count == m_ATTCalStatus.Cfg.AvgCountMax)
                                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_ATTCalStatus.Capture_count == countmax" << endl;
                            }
                            ready_to_avg = true;
                        }
                    }
                    m_ATTCalStatus.power_ready_to_avg[(m_ATTCalStatus.Capture_count - 1) % m_ATTCalStatus.Cfg.AvgCount] = Datadouble[FreqOffset_point];
                }
                if (ready_to_avg)
                {
                    //取平均
                    for (int i = 0; i < m_ATTCalStatus.Cfg.AvgCount; i++)
                    {
                        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_ATTCalStatus.power_ready_to_avg[" << i << "] = " << m_ATTCalStatus.power_ready_to_avg[i] << endl;
                        m_ATTCalStatus.result_power += m_ATTCalStatus.power_ready_to_avg[i];
                    }
                    m_ATTCalStatus.result_power /= m_ATTCalStatus.Cfg.AvgCount;
                    avg_complete = true; //平均完成
                }
            }
        }
        else
        {
            WTLog::Instance().LOGERR(Ret, (string("ATTCalProc Status = ") + to_string(Status)).c_str());
            Ret = WT_VSA_CAPTURE_FAILED;
            CheckBreak(Ret);
        }
        if (avg_complete)
        {
            m_ATTCalStatus.Capture_count = 0;
            if (m_ATTCalStatus.result_power)
            {
                m_ATTCalStatus.Result.Gain[m_ATTCalStatus.CurCode] = m_ATTCalStatus.result_power;
                m_ATTCalStatus.result_power = 0.0; //归零
            }
            else
            {
                m_ATTCalStatus.Result.Gain[m_ATTCalStatus.CurCode] = -96.0;
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "ATTCalProc AttId=%d, Code=%d, result_power=%lf\n",
                   m_ATTCalStatus.Cfg.ATTId, m_ATTCalStatus.CurCode, m_ATTCalStatus.Result.Gain[m_ATTCalStatus.CurCode]);

            if (m_ATTCalStatus.CurCode == m_ATTCalStatus.Cfg.EndCode||m_ATTCalStatus.Enable==false)
            {
                ATTCalResponse(WT_OK);
                return true;
            }
            m_ATTCalStatus.Cfg.Direction ? ++m_ATTCalStatus.CurCode : --m_ATTCalStatus.CurCode;
            if (m_ATTCalStatus.CurCode > ATT_CODE_MAX)
            {
                m_ATTCalStatus.CurCode = 0;
            }
            else if (m_ATTCalStatus.CurCode < 0)
            {
                m_ATTCalStatus.CurCode = ATT_CODE_MAX;
            }
            if (static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type) == DEV_TYPE_BACK)
            {
                Ret = DevLib::Instance().SetSwbAttCode(m_ATTCalStatus.Cfg.SwAttPort, m_ATTCalStatus.CurCode);
            }
            else if (m_ATTCalStatus.Cfg.ATTId == RF_ATT_SW)
            {
                Ret = DevLib::Instance().SetDebugAtt(ModId, static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type),
                                                     WT_ATT_CAL_SW_ATT, m_ATTCalStatus.CurCode);
            }
            else
            {
                Ret = DevLib::Instance().SetDebugAtt(ModId, static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type),
                                                     WT_ATT_CAL_RF_ATT0 + m_ATTCalStatus.Cfg.ATTId, m_ATTCalStatus.CurCode);
            }
            CheckBreak(Ret);
        }
        if (m_ATTCalStatus.Cfg.DelayProc)
        {
            usleep(m_ATTCalStatus.Cfg.DelayProc);
        }

        Ret = DevLib::Instance().VSAStart(ModId, WT_START_MODE_NORMAL);
        m_CurDataNoAlzFlag = true;
        m_Mods[GetModIndex(ModId)].Status = WT_RX_TX_STATE_RUNNING;
        CheckBreak(Ret);
        Ret = AddAGCWaitTrigger(1.0, ModIdx);
        CheckBreak(Ret);
    } while (0);

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, (string("ATTCalProc Ret = ") + to_string(Ret)).c_str());
        ATTCalResponse(Ret);
        return true;
    }
    else
    {
        return false;
    }
}

void WTVsa::ATTCalResponse(int ErrCode)
{
    int Ret = WT_OK;
    ATTCalStop();
    if (ErrCode == WT_OK)
    {
        Ret = m_ExtConn->Response(&m_ATTCalStatus.Result, sizeof(m_ATTCalStatus.Result));
    }
    else
    {
        Ret = m_ExtConn->Response(ErrCode);
        WTLog::Instance().LOGERR(Ret, "ATTCalResponse error");
    }

    gettimeofday(&m_ATTCalStatus.tpend, NULL);
    WTLog::Instance().WriteLog(LOG_DEBUG, "ATTCalStart use time %lf us\n",
           (m_ATTCalStatus.tpend.tv_sec - m_ATTCalStatus.tpstart.tv_sec) * 1e6 +
               (m_ATTCalStatus.tpend.tv_usec - m_ATTCalStatus.tpstart.tv_usec));
}

int WTVsa::ATTCalStop()
{
    DeleteAGCWaitTrigger(0);
    m_ATTCalStatus.Enable = false;
    ClearFinAgent();
    return WT_OK;
}

int WTVsa::ATTCalStart(Connector *Conn, ATTCalCfg &Cfg)
{
    int Ret = WT_OK;

    gettimeofday(&m_ATTCalStatus.tpstart, NULL);

    m_ATTCalStatus.Enable = false;

    if ((Ret = GetVsa().CheckCalParam(WT_START_MODE_ATT_CAL)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsa parameter error, can not start Cal mode");
        return Ret;
    }

    if ((Ret = GetVsg().CheckCalParam(WT_START_MODE_ATT_CAL)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Vsg parameter error, can not start Cal mode");
        return Ret;
    }
    do
    {
        memcpy(&m_ATTCalStatus.Cfg, &Cfg, sizeof(ATTCalCfg));
        if (m_ATTCalStatus.Cfg.Ver < 0) //校准版本，目前只有0版本
        {
            Ret = WT_ARG_ERROR;
            WTLog::Instance().LOGERR(Ret, "ATTCal.Ver error");
            return Ret;
        }
#ifdef DEBUG
        {
            // ATT参数输出
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Ver=" << m_ATTCalStatus.Cfg.Ver << endl
            << "Type=" << m_ATTCalStatus.Cfg.Type << endl
            << "ATTId=" << m_ATTCalStatus.Cfg.ATTId << endl
            << "SwAttPort=" << m_ATTCalStatus.Cfg.SwAttPort << endl
            << "Direction=" << m_ATTCalStatus.Cfg.Direction << endl
            << "StartCode=" << m_ATTCalStatus.Cfg.StartCode << endl
            << "EndCode=" << m_ATTCalStatus.Cfg.EndCode << endl
            << "SwAttInit=" << m_ATTCalStatus.Cfg.SwAttInit << endl;
            for (int i = 0; i < ATT_COUNT_MAX; i++)
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ATTInitArray[" << i << "]=" << m_ATTCalStatus.Cfg.ATTInitArray[i] << endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "IsStartVSG=" << m_ATTCalStatus.Cfg.IsStartVSG << endl
            << "DelayStart=" << m_ATTCalStatus.Cfg.DelayStart << endl
            << "DelayProc=" << m_ATTCalStatus.Cfg.DelayProc << endl
            << "AvgDiff=" << m_ATTCalStatus.Cfg.AvgDiff << endl
            << "AvgCount=" << m_ATTCalStatus.Cfg.AvgCount << endl
            << "AvgCountMax=" << m_ATTCalStatus.Cfg.AvgCountMax << endl
            << "FreqOffset=" << m_ATTCalStatus.Cfg.FreqOffset << endl;
        }
#endif
        memset(&m_ATTCalStatus.Result, 0, sizeof(ATTCalResult));
        m_ATTCalStatus.Enable = true;
        m_ATTCalStatus.CurCode = m_ATTCalStatus.Cfg.StartCode;
        m_ATTCalStatus.power_ready_to_avg.reset(new (std::nothrow) double[m_ATTCalStatus.Cfg.AvgCount]{-96.0});
        if (m_ATTCalStatus.power_ready_to_avg.get() == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when ATTCalStart");
            return WT_ALLOC_FAILED;
        }

        Ret = AllocSetMod(false); //申请模块
        CheckBreak(Ret);

        SetFinAgent(bind(&WTVsa::ATTCalProc, this, _1));
        if (static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type) != DEV_TYPE_BACK)
        {
            Ret = DevLib::Instance().SetDebugAtt(m_Mods[0].ModId, static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type),
                                                 WT_ATT_CAL_SW_ATT, Cfg.SwAttInit);
            CheckBreak(Ret);
            for (int i = 0; i < ATT_COUNT_MAX; ++i)
            {
                Ret = DevLib::Instance().SetDebugAtt(m_Mods[0].ModId, static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type),
                                                     WT_ATT_CAL_RF_ATT0 + i, Cfg.ATTInitArray[i]);
                CheckBreak(Ret);
            }
        }

        if (m_ATTCalStatus.Cfg.IsStartVSG)
        {
            Ret = DevLib::Instance().VSGStart(m_Mods[0].ModId, WT_START_MODE_ATT_CAL);
            CheckBreak(Ret);
        }
        CheckBreak(Ret);
        if (static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type) == DEV_TYPE_BACK)
        {
            Ret = DevLib::Instance().SetSwbAttCode(m_ATTCalStatus.Cfg.SwAttPort, m_ATTCalStatus.CurCode);
        }
        else
        {
            Ret = DevLib::Instance().SetDebugAtt(m_Mods[0].ModId, static_cast<WT_DEV_TYPE>(m_ATTCalStatus.Cfg.Type),
                                                 WT_ATT_CAL_RF_ATT0 + m_ATTCalStatus.Cfg.ATTId, m_ATTCalStatus.CurCode);
        }
        CheckBreak(Ret);
        if (m_ATTCalStatus.Cfg.DelayStart)
        {
            usleep(m_ATTCalStatus.Cfg.DelayStart);
        }

        {
            //设置分析参数
            std::unique_ptr<AlzParamComm>commonAnalyzeParam(new AlzParamComm);
            Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_COMMON, commonAnalyzeParam.get(), sizeof(AlzParamComm));
        }
        m_Mods[0].RFPort = m_Param[0].RFPort;
        Ret = DevLib::Instance().VSAStart(m_Mods[0].ModId, WT_START_MODE_NORMAL);
        m_CurDataNoAlzFlag = true;
        m_Mods[0].Status = WT_RX_TX_STATE_RUNNING;
        CheckBreak(Ret);

        Ret = AddAGCWaitTrigger(1.0, GetModIndex(m_Mods[0].ModId));
        CheckBreak(Ret);
    } while (0);

    if (Ret == WT_OK)
    {
        Conn->SetRsp(false);
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "ATTCalStart error");
        if (m_Mods[0].Status == WT_RX_TX_STATE_RUNNING)
        {
            DevLib::Instance().VSAStop(m_Mods[0].ModId);
        }
        ATTCalStop();
    }
    return Ret;
}

int WTVsa::SetDevAnalogIQMode(const int *const AnalogIQMode, int Num)
{
    int Ret = WT_OK;
    int ModNum;

    Ret = DevLib::Instance().GetUBCount(DEV_TYPE_VSA, ModNum);
    CheckRet(Ret);
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetAnalogIQSW ModNum%d : ParamNum%d\n", ModNum, Num);

    //本机执行
    for (int i = 0; i < ModNum && i < Num; i++)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "SetAnalogIQSW mod%d : %d\n", i, AnalogIQMode[i]);
        Ret = DevLib::Instance().SetAnalogIQSW(i, AnalogIQMode[i] ? true : false);
        if (Ret != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModNum = " << ModNum << std::endl;
            WTLog::Instance().LOGERR(Ret, "DevLib::Instance().SetAnalogIQSW failed");
        }
    }
    return Ret;
}

int WTVsa::GetDevAnalogIQMode(int *AnalogIQMode, int Num)
{
    int Ret = WT_OK;
    int ModNum;
    Ret = DevLib::Instance().GetUBCount(DEV_TYPE_VSA, ModNum);

    for (int i = 0; i < ModNum && i < Num; i++)
    {
        Ret = DevLib::Instance().GetAnalogIQSW(i, AnalogIQMode[i]);
        if (Ret != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModNum = " << ModNum << std::endl;
            WTLog::Instance().LOGERR(Ret, "DevLib::Instance().GetAnalogIQSW failed");
        }
    }
    return Ret;
}

void VsaAutoRange::RestoreParamBaseBand(VsaParam &Param)
{
    Param.TrigType = m_UserTrigType;
    Param.SamplingTime = m_UserSmpTime;
    Param.TrigPreTime = m_TrigPreTime;

    if (Param.TrigLevel > m_MaxTrigLevel)
    {
        Param.TrigLevel = m_MaxTrigLevel;
    }
    else if (Param.TrigLevel < m_MinTrigLevel)
    {
        Param.TrigLevel = m_MinTrigLevel;
    }
}

bool VsaParam::CheckBaseBandParam(void)
{
    int AnalogIQSW = 0;
    int ModId = 0;
    int Ret = WT_OK;
    Ret = DevLib::Instance().GetModId(DEV_TYPE_VSA, RFPort, ModId);
    RetWarnning(Ret,"GetModId fail");
    Ret = DevLib::Instance().GetAnalogIQSW(ModId, AnalogIQSW);
    RetWarnning(Ret,"GetAnalogIQSW fail");
    if (AnalogIQSW)
    {
        Ampl = 0.0;
    }
    return true;
}

void VsaAutoRange::SetAndBackupParamBaseBand(VsaParam &Param)
{
    m_UserTrigType = Param.TrigType;
    m_TrigPreTime = Param.TrigPreTime;
    m_UserSmpTime = Param.SamplingTime;
    Param.SamplingTime = 2000 * 1e-6;   //2ms
    Param.TrigPreTime = GetDefaultPreSmpTime();
    Param.TrigType = WT_TRIG_TYPE_FREE_RUN;
}

int WTVsa::GetSpectrumPointPower(double Offset, double &Power, int signalID, int segmentID)
{
    int iRet = WT_OK;
    int elementSize = 0;
    int elementCount = 0;
    int DataLen = 0;
    void *Data;
    void *rbwdata = 0;
    int rbwSize = 0;

    do
    {
        iRet = m_Alg.GetVsaResult("y", signalID, segmentID, &Data, DataLen, elementSize);
        if (iRet)
        {
            break;
        };
        elementCount = DataLen / elementSize;

        iRet = m_Alg.GetVsaResult("spectrum.rbw", signalID, segmentID, &rbwdata, DataLen, rbwSize);
        if (iRet)
        {
            break;
        };
        int rbw = ((int *)rbwdata)[0];
        int off = elementCount / 2 + (int)(Offset / rbw);
        WTLog::Instance().WriteLog(LOG_DEBUG, "elementCount=%d Offset=%f,rbw=%d off=%d\n", elementCount, Offset, rbw, off);

        if (off > 0 && off < elementCount)
        {
            // int OffsetPoint = (elementCount / 2 + Offset/1e5);//频谱点间距1e5
            Power = ((double *)Data)[off];
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Data[" << off << "] = " << ((double *)Data)[off] << endl;
        }
        else
        {
            iRet = WT_ARG_ERROR;
        }

    } while (0);

    return iRet;
}

bool WTVsa::NoiseCalProc(int ModId)
{
    int Ret = WT_OK;
    int ModIdx = GetModIndex(ModId);
    int Status = m_Mods[ModIdx].Status;
    bool LoopDown = false;
    do
    {
        if(!m_NoiseCalStatus.NoiseCalFlag)//中途停止
        {
            break;
        }
        else if (Status == WT_RX_TX_STATE_DONE) // 采集完成
        {
            // 分析
            m_NoiseCalStatus.Capture_count++;
            auto &DataInfo = m_CapData[0].DataInfo[0];
            Ret = GetCaptureData(m_Mods[ModIdx].ModId, DataInfo);
            CheckBreak(Ret);
            m_CapData[0].DataNum = 1;
            Ret = AlzSingleData(0);
            CheckBreak(Ret);

            // 取结果数据
            void *DataBuf = nullptr;
            int DataSize;
            int DataType;
            // 取y轴功率值(double *)DataBufWT_RES_SPECTRUM_Y
            Ret = m_Alg.GetVsaResult("y", 0, 0, &DataBuf, DataSize, DataType);
            CheckBreak(Ret);
            double *Datadouble = (double *)((char *)DataBuf);
            // 取频谱点
            int Scale = (DataSize / DataType)/(m_NoiseCalStatus.noise_info.smp_rate/1e6);
            int SpectrumPointIndex = m_NoiseCalStatus.noise_info.spec_start;
            int DataPointIndex = 0;
            int TotalPoint = ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span);
            // for(int i =0;i<DataSize/DataType;i++)
            // {
            //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************************"
            //             << "Datadouble [" << i << "] = " << Datadouble[i]
            //             << "****************************"
            //             << std::endl;
            // }
            //取数据
            for (; SpectrumPointIndex < DataSize / DataType && SpectrumPointIndex < m_NoiseCalStatus.noise_info.spec_end; DataPointIndex++)
            {
                int j = 0;
                double sum = 0.0;
                for (; j < m_NoiseCalStatus.noise_info.spec_avg_span && SpectrumPointIndex < DataSize / DataType && SpectrumPointIndex < m_NoiseCalStatus.noise_info.spec_end; j++, SpectrumPointIndex++)
                {
                    sum += Datadouble[SpectrumPointIndex * Scale + (DataSize / DataType) / 2];
                }
                if (DataPointIndex < TotalPoint) // 已采集足够数据
                {
                    m_NoiseCalStatus.power_ready_to_avg[DataPointIndex][m_NoiseCalStatus.Capture_count] = sum / j;
                    // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************************"
                    //           << "m_NoiseCalStatus.power_ready_to_avg [" << DataPointIndex << "][" << m_NoiseCalStatus.Capture_count << "] = " << (sum / j)
                    //           << "****************************"
                    //           << std::endl;
                }
            }
            // 取平均
            if (m_NoiseCalStatus.Capture_count >= m_NoiseCalStatus.noise_info.avg)
            {
                //进行平均
                Freq_Level_Data_List temp;
                memset(&temp,0,sizeof(temp));
                temp.freq = m_Param[0].Freq/1e6;
                temp.link_idx = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].link_idx;
                temp.level_sub_idx = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].level_sub_idx;
                temp.level_all_idx = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].level_all_idx;
                temp.power_level = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].power_level;
                temp.real_plevel = m_Param[0].Ampl;
                for (int j = 0; j < TotalPoint; j++)
                {
                    double sum = 0.0;
                    for (int k = 1; k <= m_NoiseCalStatus.noise_info.avg; k++)
                    {
                        sum += m_NoiseCalStatus.power_ready_to_avg[j][k];
                    }
                    temp.data[j] = sum / (m_NoiseCalStatus.noise_info.avg);
                    // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "============================"
                    //           << "m_NoiseCalStatus.Data [" << j << "] = " << (sum / (m_NoiseCalStatus.noise_info.avg))
                    //           << "============================"
                    //           << std::endl;
                }
                m_NoiseCalStatus.Data.push_back(temp);
                m_NoiseCalStatus.CurCount++;
                //判断循环是否结束
                if (m_NoiseCalStatus.CurCount < m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].count)
                {
                    m_NoiseCalStatus.Capture_count = 0;
                }
                else
                {
                    DevMgr::Instance().PortLedOff(m_Mods[ModIdx].RFPort);
                    for (int i = 0; i < m_NoiseCalStatus.CurCount; i++)
                    {
                        for(int j = 0; j < ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span); j++)
                        {
                            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
                                    << "Port["<<m_NoiseCalStatus.CurPort<<"].CurCount [" << i << "]("<<m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[i].freq * 1e6<<").Power["<<j<<"] = " << m_NoiseCalStatus.Data[i].data[j]
                                    << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
                                    << std::endl;
                        }
                    }
                    Ret = wt_calibration_update_noise_data(m_NoiseCalStatus.CurPort, &(m_NoiseCalStatus.Data[0]), m_NoiseCalStatus.CurCount);
                    CheckBreak(Ret);
                    do
                    {
                        m_NoiseCalStatus.CurPort++;
                    } while (m_NoiseCalStatus.CurPort < 8 && !m_NoiseCalStatus.port_list[m_NoiseCalStatus.CurPort]);

                    if (m_NoiseCalStatus.CurPort >= 8)
                    {
                        LoopDown = true;
                        break;
                    }
                    else
                    {
                        LoopDown = false;
                        m_NoiseCalStatus.CurCount = 0;
                        m_NoiseCalStatus.Capture_count = 0;
                        m_NoiseCalStatus.Data.clear();
                    }
                }
                //设置参数
                Ret = SetCalParam();
                CheckBreak(Ret);
            }
        }
        else
        {
            WTLog::Instance().LOGERR(Ret, (string("NoiseCalProc Status = ") + to_string(Status)).c_str());
            Ret = WT_VSA_CAPTURE_FAILED;
            CheckBreak(Ret);
        }
        Ret = SetParamToMod(false);
        CheckBreak(Ret);
        Ret = StartAllMod();
        CheckBreak(Ret);
    } while (0);

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, (string("NoiseCalProc Ret = ") + to_string(Ret)).c_str());
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_ERR_DONE;
        m_NoiseCalStatus.ErrorCode = Ret;
        NoiseCalStop();
        return true;
    }
    else if (LoopDown || !m_NoiseCalStatus.NoiseCalFlag)
    {
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_DONE;
        NoiseCalStop();
        return true;
    }
    else
    {
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_RUNNING;
        return false;
    }
}

int WTVsa::NoiseCalStart(int port_list[8])
{
    int Ret = WT_OK;
    bool LoopDown = false;
    do
    {
        for(int i =0;i<8;i++)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"PortList ["<<i<<"] = "<<port_list[i]<<std::endl;
            m_NoiseCalStatus.port_list[i] = port_list[i];
        }
        // 获取Cal参数
        Ret = wt_calibration_get_noise_cal_list(port_list, m_NoiseCalStatus.port_cal_list, &m_NoiseCalStatus.noise_info);
        CheckBreak(Ret);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************************CalParam****************************"<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.avg = " <<m_NoiseCalStatus.noise_info.avg<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.rbw = " <<m_NoiseCalStatus.noise_info.rbw<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.smp_time = " <<m_NoiseCalStatus.noise_info.smp_time<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.smp_rate = " <<m_NoiseCalStatus.noise_info.smp_rate<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.spec_start = " <<m_NoiseCalStatus.noise_info.spec_start<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.spec_end = " <<m_NoiseCalStatus.noise_info.spec_end<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_NoiseCalStatus.noise_info.spec_avg_span = " <<m_NoiseCalStatus.noise_info.spec_avg_span<< std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************************CalParam****************************"<< std::endl;
        // 初始化Cal参数
        m_NoiseCalStatus.CurPort = 0;
        for(int i =0;i<8;i++)
        {
            if(m_NoiseCalStatus.port_list[i])
            {
                m_NoiseCalStatus.CurPort = i;
                break;
            }
        }
        if(!m_NoiseCalStatus.port_list[m_NoiseCalStatus.CurPort])
        {
            LoopDown = true;
            break;
        }
        m_NoiseCalStatus.NoiseCalFlag = true;
        m_NoiseCalStatus.CurCount = 0;
        m_NoiseCalStatus.Capture_count = 0;
        m_NoiseCalStatus.ErrorCode = WT_OK;
        m_NoiseCalStatus.Data.clear();
        // 申请平均所用内存
        m_NoiseCalStatus.power_ready_to_avg = new (std::nothrow) double *[((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span) + 1];
        if (m_NoiseCalStatus.power_ready_to_avg == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when NoiseCalStart");
            Ret = WT_ALLOC_FAILED;
            CheckBreak(Ret);
        }
        for (int i = 0; i < ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span) + 1; i++)
        {
            m_NoiseCalStatus.power_ready_to_avg[i] = new (std::nothrow) double[m_NoiseCalStatus.noise_info.avg + 1]();
        }
        if (m_NoiseCalStatus.power_ready_to_avg == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when NoiseCalStart");
            Ret = WT_ALLOC_FAILED;
            CheckBreak(Ret);
        }
        //停止正在运行的VSA
        Ret = StopRunningMod();
        CheckBreak(Ret);
        //设置参数
        Ret = SetCalParam();
        CheckBreak(Ret);
        // 设置回调函数
        SetFinAgent(bind(&WTVsa::NoiseCalProc, this, _1));
        // 申请模块
        Ret = AllocSetMod(false);
        CheckBreak(Ret);
        Ret = StartAllMod();
        CheckBreak(Ret);
    } while (0);

    if (Ret == WT_OK)
    {
        if(LoopDown)
        {
            m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_DONE;
        }
        else
        {
            m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_RUNNING;
        }
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "NoiseCalStart error");
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_ERR_DONE;
        NoiseCalStop();
    }
    return Ret;
}

int WTVsa::NoiseCalStop()
{
    if (m_NoiseCalStatus.power_ready_to_avg != nullptr)
    {
        for (int i = 0; i < ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span) + 1; i++)
        {
            delete[] m_NoiseCalStatus.power_ready_to_avg[i];
            m_NoiseCalStatus.power_ready_to_avg[i] = nullptr;
        }
        delete[] m_NoiseCalStatus.power_ready_to_avg;
        m_NoiseCalStatus.power_ready_to_avg = nullptr;
    }
    m_NoiseCalStatus.NoiseCalFlag = false;
    ClearFinAgent();
    return WT_OK;
}

int WTVsa::SetCalParam()
{
    int Ret = WT_OK;
    do
    {
        // 配置VSA参数
        VsaParam Param;
        memset(&Param, 0, sizeof(VsaParam));
        Param.RFPort = m_NoiseCalStatus.CurPort+1;
        Param.Freq = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].freq * 1e6;
        Param.Ampl = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].power_level;
        Param.Freq2 = 0.0;
        Param.FreqOffset = 0.0;
        Param.Type = TEST_SISO;
        Param.MasterMode = 0;
        Param.SignalId = 0;
        Param.VsaMask = 0;
        Param.TrigType = WT_TRIG_TYPE_FREE_RUN;
        Param.AllocTimeout = 4.0;
        Param.Is160M = 0;
        Param.ExtGain = 0;
        Param.SamplingTime = m_NoiseCalStatus.noise_info.smp_time;
        Param.SamplingFreq = m_NoiseCalStatus.noise_info.smp_rate;
        Param.TrigPreTime = 20 * Us;
        Param.TrigTimeout = 0.2;
        Param.TrigLevel = -31.0;
        Param.MaxIFG = 0.1;
        Param.RFPort2 = 0;
        Param.DulPortMode = 0;
        Param.Ampl2 = 0.0;
        Param.TrigLevel2 = 0.0;
        Param.Demode = WT_DEMOD_CW|0xA5B70000;
        Param.DCOffsetI = 0;
        Param.DCOffsetQ = 0;
        if (m_MaxParamNum < 1)
        {
            m_Param.reset(new (std::nothrow) VsaParam);
            if (m_Param == nullptr)
            {
                m_MaxParamNum = 0;
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc switched mimo param buffer failed");
                Ret = WT_ALLOC_FAILED;
                break;
            }
            m_MaxParamNum = 1;
        }
        memcpy(m_Param.get(), &Param, sizeof(VsaParam));
        m_ParamNum = 1;
        AlzParamComm commonAnalyzeParam;
        Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_COMMON, &commonAnalyzeParam, sizeof(AlzParamComm));
        CheckBreak(Ret);
        AlzParamFFT FFTAnalyzeParam;
        FFTAnalyzeParam.Rbw = m_NoiseCalStatus.noise_info.rbw;
        Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_FFT, &FFTAnalyzeParam, sizeof(AlzParamFFT));
        CheckBreak(Ret);
        m_Param[0].IsAgumentLegal();
    } while (0);
    return Ret;
}

int WTVsa::GetNoiseCalValid(Connector *Conn,int Status[8][8])
{
    int port_list[8] = {1, 1, 1, 1, 1, 1, 1, 1};
    int Ret = WT_OK;
    if (!Conn->IsLinkToSlave())
    {
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "GetNoiseCalValid failed");
            }
            Ret = wt_calibration_get_noise_data_status(port_list, (CAL_NOISE_DATA_STATE *)m_PortNoiseStatus[0]);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "GetNoiseCalValid failed");
            }
            else
            {
                Conn->SetRsp(false);
            }
        }
        else
        {
            Ret = wt_calibration_get_noise_data_status(port_list, (CAL_NOISE_DATA_STATE *)Status[0]);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "GetNoiseCalValid failed");
            }
        }
    }
    else
    {
        if (Ret == WT_OK)
        {
            auto iter = m_SlaveConn.begin();
            for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
            {
                if (*Conn == **iter)
                {
                    for(int j = 0;j<8;j++)
                    {
                        m_PortNoiseStatus[i+1][j] = Status[0][j];
                    }
                    m_DevStaMask |= 1 << i;
                    break;
                }
            }
        }
        else
        {
            Ret = m_ExtConn->Response(Ret);
        }

        if (IsMimoDone())
        {
            memcpy(Status,m_PortNoiseStatus, sizeof(int)*8*8);
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"Status :"<<std::endl;
            for(int i = 0;i<8;i++)
            {
                for(int j =0;j<8;j++)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"["<<Status[i][j]<<"] ";
                }
                WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<std::endl;
            }
            Ret = m_ExtConn->Response(m_PortNoiseStatus, sizeof(int)*8*8);
        }
    }
    return Ret;
}
int WTVsa::SetExtendEVMStatus(Connector *Conn, int Status)
{
    int Ret = WT_OK;
    if (!Conn->IsLinkToSlave())
    {
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "SetExtendEVMStatus failed");
            }
        }
        switch (Status)
        {
        case Extend_EVM_NORMAL:
        {
            m_ExtendEVMStu.IterativeEVM = Iterative_EVM_OFF;
            m_ExtendEVMStu.SncEVM = SNC_EVM_OFF;
            m_ExtendEVMStu.CcEVM = CC_EVM_OFF;
            break;
        }
        case Extend_EVM_VECTORAVG:
        {
            m_ExtendEVMStu.IterativeEVM = Iterative_EVM_ON;
            break;
        }
        case Extend_EVM_DUALVSA:
        {
            m_ExtendEVMStu.CcEVM = CC_EVM_ON;
            break;
        }
        case Extend_EVM_NOISECOMPENSATION:
        {
            m_ExtendEVMStu.SncEVM = SNC_EVM_ON;
            break;
        }
        default:
            break;
        }
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "SetExtendEVMStatus failed");
            Conn->SetRsp(true);
        }
    }
    else
    {
        ProcMimoAck(Conn);
    }
    return Ret;
}

int WTVsa::SetIterativeEVMStatus(Connector *Conn, int Status)
{
    int Ret = WT_OK;
    if (!Conn->IsLinkToSlave())
    {
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "SetIterativeEVMStatus failed");
            }
        }
        m_ExtendEVMStu.IterativeEVM = Status;
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "SetIterativeEVMStatus failed");
            Conn->SetRsp(true);
        }
    }
    else
    {
        ProcMimoAck(Conn);
    }
    return Ret;
}

int WTVsa::SetSncEVMStatus(Connector *Conn, int Status)
{
    int Ret = WT_OK;
    if (!Conn->IsLinkToSlave())
    {
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "SetSNC_EVMStatus failed");
            }
        }
        m_ExtendEVMStu.SncEVM = Status;
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "SetSNC_EVMStatus failed");
            Conn->SetRsp(true);
        }
    }
    else
    {
        ProcMimoAck(Conn);
    }
    return Ret;
}

int WTVsa::SetCcEVMStatus(Connector *Conn, int Status)
{
    int Ret = WT_OK;
    if (!Conn->IsLinkToSlave())
    {
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "SetCC_EVMStatus failed");
            }
        }
        m_ExtendEVMStu.CcEVM = Status;
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "SetCC_EVMStatus failed");
            Conn->SetRsp(true);
        }
    }
    else
    {
        ProcMimoAck(Conn);
    }
    return Ret;
}


void WTVsa::SetVsaAGCSamplingTime(double SamplingTime)
{
    //AGC配置采样时长是300us ~ 20ms
    if ((SamplingTime < 300 * 1e-6) || (SamplingTime > 20 * 1e-3))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Set VSA AGC Sampling Time : %lf, out of range.\n", SamplingTime);
        return;
    }
    m_AgcSamplingTime = SamplingTime;
    WTLog::Instance().WriteLog(LOG_DEBUG, "Set VSA AGC Sampling Time = %lf us\n", SamplingTime * 1e6);
}
