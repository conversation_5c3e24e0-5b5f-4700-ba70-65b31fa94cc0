#include <string>
#include "wtlog.h"
#include "vsg.h" // 借用 VsgParam
#include "vsgv2.h"
#include "devtype.h" // WT_RF_STATE_PA
#include "wt-calibration.h"

#define IN_CAL_USE_FPGA_DATA (0) // 使用fpga生成的信号
#define IN_CAl_USE_SIG_FLLE (1) // 自校准PN数据来自仪器内的信号文件

WTVsgV2::WTVsgV2(const wtev::loop_ref &Loop)
    : WTBase(Loop, DEV_RES_VSG)
{
    m_Compensate = nullptr;
    for (int i = 0; i < DEV_BOARD_NUM; ++i)
    {
        m_AmendWaveFlag[i] = false;
    }
}

int WTVsgV2::SetPn()
{
    int Ret = WT_OK;
    std::vector<RfPnItem> PnItemVector;
    std::vector<SegBehaviorType> SegBehaviorVector;

#if IN_CAL_USE_FPGA_DATA
    Ret = DevLib::Instance().WriteDirectReg(m_AllocModId, DEV_TYPE_VSG, AUTO_CAL_VSG_REG, 1);
#elif (IN_CAl_USE_SIG_FLLE == 1)
    // PN信号补偿修正
    if (m_NeedModId != -1 && !m_AmendWaveFlag[m_NeedModId])
    {
        Ret = AmendWaveData();
        if (Ret != WT_OK)
        {
            WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubService] Service AmendWaveData Error tid=%u Ret=%d[0X%X]\n", pthread_self(), Ret, Ret);
            return Ret;
        }
        else
        {
            m_AmendWaveFlag[m_NeedModId] = true;
        }
    }

    RfPnItem PnData;
    SegBehaviorType SegBehaviorParam;
    // std::vector<RfPnItem> PnItemVector;
    // std::vector<SegBehaviorType> SegBehaviorVector;
    memset(&SegBehaviorParam, 0, sizeof(SegBehaviorParam));
    memset(&PnData, 0, sizeof(PnData));

    PnData.Addr = (unsigned long)m_VsgDataInfo.PnData;  // PN数据来自仪器内的信号文件
    PnData.Len = m_VsgDataInfo.PnDataLen;
    PnItemVector.push_back(PnData);
    SegBehaviorParam.PnIndex = 0;
    SegBehaviorParam.PnDataLen = m_VsgDataInfo.PnDataLen;
    SegBehaviorParam.ReSampleRate = DEFAULT_SMAPLE_RATE;
    SegBehaviorParam.IFG = 0;
    SegBehaviorParam.PnIfg = 0;
    SegBehaviorParam.PnHead = 0;
    SegBehaviorParam.PnTail = 0;
    SegBehaviorParam.IFGCnt = 0;
    SegBehaviorParam.PnIfgCnt = 0;
    SegBehaviorParam.PnHeadCnt = 0;
    SegBehaviorParam.PnTailCnt = 0;
    SegBehaviorParam.IFGCntLen = 0;
    SegBehaviorParam.PnDataExtendCnt = 0;
    SegBehaviorParam.PnDataRepeat = 1;
    SegBehaviorParam.PnRepeat = 1;
    SegBehaviorParam.PnDataSendLenCnt = SegBehaviorParam.PnDataLen / sizeof(stIQDat);
    SegBehaviorVector.push_back(SegBehaviorParam);
    // 调试使用, 输出信号文件用于与校准设置的信号文件做对比
    // FILE *f = fopen("pn_cal.data", "wb");
    // fwrite(m_VsgDataInfo.PnData, m_VsgDataInfo.PnDataLen, 1, f);
    // fclose(f);
    //PnItemVector.push_back(PnCfg);
    //Ret = DevLib::Instance().VSGSetPNItem(m_AllocModId, PnItemVector, MAX_SMAPLE_RATE);
#else
// bin/wave/pn_cal.data
// pn_cal.data是设置到硬件时的抓到的RAW 数据
// 数据与与长度都是特定的，主要是调试对比功率使用
// 所以发布版本不能走这条分支
#error "Not support flow"
    PnCfg.Addr = (unsigned long)m_VsgDataInfo.PnData;
    PnCfg.Len = m_VsgDataInfo.PnDataLen;

    PnCfg.Loop = 0;
    PnCfg.IFG = 0;
    PnCfg.PnIfg = 0.0;
    PnCfg.PnHead = 0.0;
    PnCfg.PnTail = 0.0;
    PnCfg.PnRepeat = 1;
    PnCfg.FreqOffset = 0;
    PnCfg.PowerOffset = 0;
    PnCfg.TBTStartVsaLen = TB_START_VSA_POS;

    char buff[960000] = {0};
    std::string dir = WTConf::GetDir();
    dir += "/wave/pn_cal.data";
    FILE *f = fopen(dir.c_str(), "rb");
    fread(buff, 960000, 1, f);
    fclose(f);

    PnCfg.Addr = (unsigned long)buff;
    PnCfg.Len = 960000;
    PnItemVector.push_back(PnCfg);
    //Ret = DevLib::Instance().VSGSetPNItem(m_AllocModId, PnItemVector, MAX_SMAPLE_RATE);
#endif
    (void)Ret;
    // TODO : FPGA重构，已修改待观察
    return DevLib::Instance().VSGSetPNItem(m_AllocModId, PnItemVector, SegBehaviorVector);
    return 0 ;
}

int WTVsgV2::SetMod(double TxPower)
{
    int Ret = WT_OK;
    VSGConfigType Config;
    memset(&Config, 0, sizeof(Config));
    Config.Gain = 0;
    Config.FreqOffsetHz = 0.0;
    Config.SamplingRate = MAX_SMAPLE_RATE;
    Config.RFPort = GetRFPort();
    Config.DeviceMode = DEVICE_MODE_SISO;
    Config.Freq = GetFreqHz();
    Config.VsgIfgStatus = 0;
    Config.Power = TxPower;

    memset(&m_CalParam, 0, sizeof(Tx_Parm));

    struct ExtModeType ExtMode;
    ExtMode.Mode = WT_VSG_MODE_NORMAL;
    DevLib::Instance().SetExtMode(m_AllocModId, DEV_TYPE_VSG, ExtMode);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set vsg ext mode failed");
        return Ret;
    }

    Ret = DevLib::Instance().VSGSetWorkPointConfig(m_AllocModId, Config, m_CalParam);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = DevLib::Instance().SetBoostStatus(m_AllocModId, m_CalParam.tx_gain_parm.is_pa_on);
    if (Ret != WT_OK)
    {
        return Ret;
    }
    // 获取Trigger配置信息
    std::vector<VSGTriggerType> VSGTriggerVector;
    VSGTriggerType VSGTriggerParam = {WT_VSA_VSG_TRIG_TYPE_FREE_RUN, // FPGA固定在duration时间点会停止当前segment,0表示无限
                                int(6 * 1e-4 * DEFAULT_SMAPLE_RATE),//tiggeroffset小于200us FPGA VSG发送会出问题，但在单segment时tiggeroffset时间不体现 //当配置本振配置比较多的时候，trigger offset也要延长，在此取最大值。
                                0};
    VSGTriggerVector.push_back(VSGTriggerParam);
    // 配置VSG Trigger
    Ret = DevLib::Instance().VSGSetTrig(m_AllocModId, VSGTriggerVector);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSG VSGSetTrig failed");
    unsigned long long TempTimeoutCnt = round(3 * DEFAULT_SMAPLE_RATE);
    CommonTrigType TrigComParam = {TempTimeoutCnt, 0, 1};//大循环用VsgSeqTrigRepeatRum会自带不小于200us的trigger offset，所以只能用arbrepeat和arb附加gap点数实现，故VsgSeqTrigRepeatRum固定为1。
    Ret = DevLib::Instance().SetTrigComParam(m_AllocModId, DEV_TYPE_VSG, TrigComParam);

    return Ret;
}

int WTVsgV2::Start()
{
    int Ret = DevLib::Instance().VSGStart(m_AllocModId);
    if (Ret != WT_OK)
    {
        SetModRunState(false);
    }
    else
    {
        SetModRunState(true);
    }

    return Ret;
}

int WTVsgV2::GetInner8318PowerCode(int &PowerCode)
{
    int rf = GetRFPort();
    return DevLib::Instance().GetSwitchInnerPowerCode(rf, PowerCode);
}

int WTVsgV2::GetInner8318AvgPowerCode(int &PowerCode)
{
#define CAP_MAX_TIMES (100) // 采样总次数
#define USEFULL_TIMES (5)   // 采样有效数据次数
// #define SHAKE_RANGE (8)    // 抖动范围
#define SHAKE_RANGE (16)    // 抖动范围

    int Ret = WT_OK;
    int LastCode = 0;       // 上一次Code
    int OnceCode = 0;       // 单次读取Code
    int Sum = 0;            // 有效数据总和
    int Count = 0;          // 有效数据个数
    int Port = GetRFPort(); // 1~8,not 0~7

    for (int i = 0; i < CAP_MAX_TIMES; ++i)
    {
        Ret = DevLib::Instance().GetSwitchInnerPowerCode(Port, OnceCode);
        // WTLog::Instance().LOGERR(LOG_SUB_MGR, "GetSwitchInnerPowerCode Ret=%d, Port=%d Code=%d\n", Ret, Port, OnceCode);
        WTLog::Instance().WriteLog(LOG_DEBUG, "GetSwitchInnerPowerCode Ret=%d, Port=%d Code=%d\n", Ret, Port, OnceCode);
        if (Ret != WT_OK)
        {
            return Ret;
        }

        if (Count == 0)
        {
            Count = 1;
            Sum = OnceCode;
            LastCode = OnceCode;
        }
        else
        {
            // 差异较大，则认为前面的数据是异常的. 当前数据是有效数据
            int TempDiff = LastCode - OnceCode;
            if (TempDiff < -SHAKE_RANGE || TempDiff > SHAKE_RANGE)
            {
                LastCode = OnceCode;
                Count = 1;
                Sum = OnceCode;
            }
            else
            {
                LastCode = OnceCode;
                Sum += OnceCode;
                ++Count;

                if (Count >= USEFULL_TIMES)
                {
                    break;
                }
            }
        }
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "GetSwitchInnerAvgPowerCode Count=%d \n", Count);
    if (Count < USEFULL_TIMES) // 认为状态不稳定
    {
        return WT_INNER_8318_POWER_CODE_NOT_STABLE;
    }
    else
    {
        PowerCode = Sum / Count;
        return WT_OK;
    }
}

int WTVsgV2::Stop()
{
    int Ret = WT_OK;
    if (m_AllocModId != -1)
    {
#if IN_CAL_USE_FPGA_DATA
        Ret = DevLib::Instance().WriteDirectReg(m_AllocModId, DEV_TYPE_VSG, AUTO_CAL_VSG_REG, 0);
        if (Ret != WT_OK)
        {
            WTLog::Instance().WriteLog(LOG_SUB_MGR, "[WTVsgV2] Vsg FPGA stop failed Ret=%d[0X%X], addr=%#x\n", Ret, Ret, AUTO_CAL_VSG_REG);
            return Ret;
        }
#endif
        if (m_ModHasRun)
        {
            Ret = DevLib::Instance().VSGStop(m_AllocModId);
            SetModRunState(false);
        }
        SetRFLinkState(WT_RF_STATE_OFF);
    }

    return Ret;
}

int WTVsgV2::LoadPnData()
{
#if (IN_CAl_USE_SIG_FLLE && !IN_CAL_USE_FPGA_DATA)
    // TODO, 最好用宏定义
#if WT418_FW
    string WaveFile = WTConf::GetDir() + "/configuration/Sin1MHz_240M.low";
#else
    string WaveFile = WTConf::GetDir() + "/configuration/Sin1MHz_480M.low";
#endif

    if (WaveFile.empty())
    {
        WTLog::Instance().LOGERR(WT_IN_CAL_SIG_FILE_LOSE, "in_cal sigfile not exist");
        return WT_NO_SIG_DATA;
    }

    WTFileSecure fileSecure(WaveFile);
    WaveFile = fileSecure.GetDecryptName();
    WTLog::Instance().WriteLog(LOG_DEBUG, "WTVsgV2::LoadPnData %s\n", WaveFile.c_str());

    m_VsgDataInfo.WaveFile.reset(new (std::nothrow) SigFile(WaveFile, SigFile::READ));

    SigFileInfo *FileInfo = nullptr;
    int Ret = m_VsgDataInfo.WaveFile->GetContent(&FileInfo);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG sigfile error");
        return Ret;
    }

    m_VsgDataInfo.FileInfo = FileInfo;
    if (FileInfo != nullptr)
    {
        //按最大长度计算内存
        m_VsgDataInfo.PointNum = 0;
        int SigNum = FileInfo->GetSigNum();

        for (int i = 0; i < SigNum; i++)
        {
            m_VsgDataInfo.PointNum = max(m_VsgDataInfo.PointNum, FileInfo->SigHeader[i].SampleCount);
        }
    }
    else
    {
        m_VsgDataInfo.PointNum = m_VsgDataInfo.WaveFile->GetFileSize() / (signed)sizeof(Complex);
    }

    //PN使用的数据是将原始数据从double转换为short
    m_VsgDataInfo.PnDataLen = m_VsgDataInfo.PointNum * sizeof(int);

    m_VsgDataInfo.Buf.reset(new (std::nothrow) char[m_VsgDataInfo.PnDataLen]);
    if (m_VsgDataInfo.Buf == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc vsg buffer failed");
        return WT_ALLOC_FAILED;
    }

    m_VsgDataInfo.PnData = m_VsgDataInfo.Buf.get();
#endif
    return WT_OK;
}

int WTVsgV2::AmendWaveData()
{
    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }

    Rf_Config_Parm RfParam;
    wt_calibration_get_configuration(&RfParam);

    double DacMargin = RfParam.dac_margin;



    m_Compensate->AmendWaveData(&(m_VsgDataInfo.FileInfo->SigHeader[0]), DacMargin, &m_CalParam, m_VsgDataInfo.PnData);

    return WT_OK;
}