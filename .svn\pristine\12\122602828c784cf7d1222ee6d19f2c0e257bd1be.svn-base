
#include <iostream>
#include <fstream>
#include "basehead.h"
#include "scpi_wavegenerator.h"
#include "commonhandler.h"
#include "scpi_gen_wifi_psdu.h"
#include "vsahandler.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include <sys/time.h>
#include <math.h>
#include <dirent.h>
#include "scpi_gen_bt.h"
#include "wtlog.h"
#include "cellular_analyze/json_convert.h"
#include "scpi_3gpp_common.h"

#define IF_RANGE_OUT(x, min_a, max_b)          \
    if ((min_a) > (x) || (max_b) < (x))        \
    {                                          \
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH; \
        break;                                 \
    }

#define GENERATOR_TMP_WAVE      "/tmp/tmpwave/"

scpi_result_t SetWaveGenDemod(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;

    do
    {
        if (!SCPI_ParamInt(context, &demod, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->WaveGenDemod = demod;
        switch (demod)
        {
        case WT_DEMOD_BT:
            iRet = WT_GetDefaultWaveParameterBTV2(attr->ConnID, attr->PnBt.get());
            attr->PnBt->commonParam.standard = demod;
            break;
        case WT_DEMOD_CW:
            iRet = WT_GetDefaultWaveParameterCW(attr->ConnID, attr->PnCW.get());
            attr->PnCW->commonParam.standard = demod;
            break;
        case WT_DEMOD_GLE:
            iRet = WT_GetDefaultWaveParameterGLE(attr->ConnID, attr->PnGLE.get());
            attr->PnGLE->commonParam.standard = demod;
            attr->PnGLE->commonParam.ClockRate = 1;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            iRet = WT_GetDefaultWaveParameterWiSun(attr->ConnID, attr->PnWiSun.get());
            attr->PnWiSun->commonParam.standard = demod;
            attr->PnWiSun->commonParam.ClockRate = 1;
            break;

        default:
            if (IsAlg3GPPStandardType(demod))
            {
                cellular::wavegen::SetCellularWaveGenCommonParam(demod, attr->Pn3GPP->CommonParam);
                attr->Pn3GPP->CommonParam.standard = demod;
                break;
            }
            iRet = WT_GetDefaultWaveParameterWifi(attr->ConnID, demod, 0, attr->PnWifi.get());
            attr->PnWifi->commonParam.standard = demod;
            break;
        }
		attr->WaveUserDefineExtendParam.clear();
        WT_SetPNFileExternSettingData(attr->ConnID, nullptr, 0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenSampleRate(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;
    scpi_number_t rate;

    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        switch (demod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.samplingRate = rate.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.samplingRate = rate.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.samplingRate = rate.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.samplingRate = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "samplingRate=" << attr->PnWiSun->commonParam.samplingRate << endl;
            break;
        default:
            if (IsAlg3GPPStandardType(demod))
            {
                if ((demod == ALG_3GPP_STD_4G && rate.value != 122880000 && rate.value != 30720000 ) ||
                    (demod == ALG_3GPP_STD_5G && rate.value != 122880000 && rate.value != 240*MHz_API))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                else
                {
                    attr->Pn3GPP->CommonParam.samplingRate = rate.value;
                }
                break;
            }
            attr->PnWifi->commonParam.samplingRate = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "samplingRate=" << attr->PnWifi->commonParam.samplingRate << endl;
            break;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenOFDMClockRate(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int ClockRate = 1;

    do
    {
        if (!SCPI_ParamInt(context, &ClockRate, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (ClockRate == 0)
        {
            ClockRate = 1;
        }
        //clockrate 取值：1，2,4,5，8,10,20
        int RateRange[] = {1, 2, 4, 5, 8, 10, 20};
        bool Exits = false;

        for(int i = 0; i < (sizeof(RateRange)/sizeof(RateRange[0])); i++)
        {
            if(ClockRate == RateRange[i])
            {
                Exits = true;
                break;
            }
        }
        if(!Exits)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->PnGLE->commonParam.standard == WT_DEMOD_GLE)
        {
            attr->PnGLE->commonParam.ClockRate = 1;
        }
        attr->PnWifi->commonParam.ClockRate = ClockRate;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenBandWidth(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 20;
    int demod = WT_DEMOD_11AG;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        switch (demod)
        {
        case WT_DEMOD_GLE:
            if (Value >= SLE_BANDWIDTH_1 && Value <= SLE_BANDWIDTH_4)
            {
                if (Value == SLE_BANDWIDTH_3)
                {
                    iRet = SCPI_ERROR_ILLEGAL_PARAMETER_VALUE;
                }
                else
                {
                    attr->PnGLE->commonParam.bandwidth = Value;
                }
            }
            else
            {
                iRet = SCPI_ERROR_ILLEGAL_PARAMETER_VALUE;
            }
            break;
        default:
            attr->PnWifi->commonParam.bandwidth = Value;
            break;
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenFreqOffset(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;
    scpi_number_t rate;

    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        switch (demod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.FreqErr = rate.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.FreqErr = rate.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.FreqErr = rate.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.FreqErr = rate.value;
            break; 
        default:
            if (IsAlg3GPPStandardType(demod))
            {
                attr->Pn3GPP->CommonParam.FreqErr = rate.value;
                break;
            }
            attr->PnWifi->commonParam.FreqErr = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FreqErr=" << attr->PnWifi->commonParam.FreqErr << endl;
            break;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenIQImbAmp(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;
    scpi_number_t rate;
    double limit = 5.0;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        // fabs(IQImbAmp) <= 5.0
        if (rate.value > limit)
        {
            rate.value = limit;
        }

        if (rate.value < -limit)
        {
            rate.value = -limit;
        }

        switch (demod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.IQImbalanceAmp = rate.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.IQImbalanceAmp = rate.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.IQImbalanceAmp = rate.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.IQImbalanceAmp = rate.value;
            break;
        default:
            if (IsAlg3GPPStandardType(demod))
            {
                attr->Pn3GPP->CommonParam.IQImbalanceAmp = rate.value;
                break;
            }
            attr->PnWifi->commonParam.IQImbalanceAmp = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "IQImbalanceAmp=" << attr->PnWifi->commonParam.IQImbalanceAmp << endl;
            break;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenIQImbPhase(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;
    scpi_number_t rate;
    double limit = 20.0;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        // fabs(IQImbPhase) <= 20.0
        if (rate.value > limit)
        {
            rate.value = limit;
        }

        if (rate.value < -limit)
        {
            rate.value = -limit;
        }

        switch (demod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.IQImbalancePhase = rate.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.IQImbalancePhase = rate.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.IQImbalancePhase = rate.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.IQImbalancePhase = rate.value;
            break;
        default:
            if (IsAlg3GPPStandardType(demod))
            {
                attr->Pn3GPP->CommonParam.IQImbalancePhase = rate.value;
                break;
            }
            attr->PnWifi->commonParam.IQImbalancePhase = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "IQImbalancePhase=" << attr->PnWifi->commonParam.IQImbalancePhase << endl;
            break;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenDCOffsetI(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;
    scpi_number_t rate;

    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        switch (demod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.DCOffset_I = rate.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.DCOffset_I = rate.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.DCOffset_I = rate.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.DCOffset_I = rate.value;
            break;
        default:
            if (IsAlg3GPPStandardType(demod))
            {
                attr->Pn3GPP->CommonParam.DCOffset_I = rate.value;
                break;
            }
            attr->PnWifi->commonParam.DCOffset_I = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DCOffset_I=" << attr->PnWifi->commonParam.DCOffset_I << endl;
            break;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenDCOffsetQ(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = WT_DEMOD_11AG;
    scpi_number_t rate;

    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        demod = attr->WaveGenDemod;
        switch (demod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.DCOffset_Q = rate.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.DCOffset_Q = rate.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.DCOffset_Q = rate.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.DCOffset_Q = rate.value;
            break;
        default:
            if (IsAlg3GPPStandardType(demod))
            {
                attr->Pn3GPP->CommonParam.DCOffset_Q = rate.value;
                break;
            }
            attr->PnWifi->commonParam.DCOffset_Q = rate.value;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DCOffset_Q=" << attr->PnWifi->commonParam.DCOffset_Q << endl;
            break;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenIFG(scpi_t * context)
{
    scpi_number_t gap;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &gap, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        switch (attr->WaveGenDemod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.Gap = gap.value;
            attr->PnBt->commonParam.TailGapValidFlag = 0;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.Gap = gap.value;
            attr->PnCW->commonParam.TailGapValidFlag = 0;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.Gap = gap.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.Gap = gap.value;
            break;
        default:
            attr->PnWifi->commonParam.Gap = gap.value;
            attr->PnWifi->commonParam.TailGapValidFlag = 0;
            break;
        }
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetWaveGenHeadandTailIFG(scpi_t * context)
{
    scpi_number_t gap[2];
    do
    {
        for(int i = 0; i < 2; i++)
        {
            if (!SCPI_ParamNumber(context, nullptr, &gap[i], true))
            {
                return SCPI_RES_ERR;
            }
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        switch (attr->WaveGenDemod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.TailGapValidFlag = 1;
            attr->PnBt->commonParam.Gap = gap[0].value;
            attr->PnBt->commonParam.TailGap = gap[1].value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.TailGapValidFlag = 1;
            attr->PnCW->commonParam.Gap = gap[0].value;
            attr->PnCW->commonParam.TailGap = gap[1].value;
            break;
        default:
            if (IsAlg3GPPStandardType(attr->WaveGenDemod))
            {
                attr->Pn3GPP->CommonParam.Gap = gap[0].value;
                break;
            }
            attr->PnWifi->commonParam.TailGapValidFlag = 1;
            attr->PnWifi->commonParam.Gap = gap[0].value;
            attr->PnWifi->commonParam.TailGap = gap[1].value;
            break;
        }
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetWaveGenSNR(scpi_t *context)
{
    scpi_number_t SNR;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &SNR, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        switch (attr->WaveGenDemod)
        {
        case WT_DEMOD_BT:
            attr->PnBt->commonParam.Snr = SNR.value;
            break;
        case WT_DEMOD_CW:
            attr->PnCW->commonParam.Snr = SNR.value;
            break;
        case WT_DEMOD_GLE:
            attr->PnGLE->commonParam.Snr = SNR.value;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            attr->PnWiSun->commonParam.Snr = SNR.value;
            break;
        default:
            if (IsAlg3GPPStandardType(attr->WaveGenDemod))
            {
                attr->Pn3GPP->CommonParam.Snr = SNR.value;
                break;
            }
            attr->PnWifi->commonParam.Snr = SNR.value;
            break;
        }
    } while (0);

    return SCPI_ResultOK(context);
}

/*配置特殊扩展开关，当打开时生成SISO信号文件的时候，将信号复制3份，变成一个4流的信号，每一路的信号是一样的，配合客户测试
“DUT有多根天线，如果多根天线同时收一样的信号，比单根天线收会有增益”的场景*/
scpi_result_t SetWaveGenSpatialExtension(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int SwitchExt = 0;

    do
    {
        if (!SCPI_ParamInt(context, &SwitchExt, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(SwitchExt, 0, 1);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->PnWifi->commonParam.SpatialExtension = SwitchExt;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenNtx(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int NumberOfTransmitChains = 1;

    do
    {
        if (!SCPI_ParamInt(context, &NumberOfTransmitChains, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(NumberOfTransmitChains, 1, 8);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->PnWifi->commonParam.Ntx = NumberOfTransmitChains;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenPhaseNoiseEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int enable = 0;
    do
    {
        if (!SCPI_ParamInt(context, &enable, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(enable, 0, 1);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->PnWifi->commonParam.PhaseNoiseFlag = enable;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenPhaseNoise(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    scpi_number_t phase_noise;
    do
    {
        phase_noise.value = -95.0;//default value

        if (!SCPI_ParamNumber(context, nullptr, &phase_noise, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->PnWifi->commonParam.PhaseNoise = phase_noise.value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenPhaseNoisePllBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    scpi_number_t phase_noise_pll_bw;
    do
    {
        phase_noise_pll_bw.value = 100.0;//default value, valid only phose noise enable flag = 1
        if (!SCPI_ParamNumber(context, nullptr, &phase_noise_pll_bw, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->PnWifi->commonParam.PhaseNoisePLLBW = phase_noise_pll_bw.value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenClockError(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    scpi_number_t clock_error;
    do
    {
        clock_error.value = 0.0;//default value
        if (!SCPI_ParamNumber(context, nullptr, &clock_error, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (IsAlg3GPPStandardType(attr->WaveGenDemod))
        {
            if (clock_error.value < -20.0 || clock_error.value > 20.0)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            attr->Pn3GPP->CommonParam.ClockErr = clock_error.value;
        }
        else
        {
            attr->PnWifi->commonParam.ClockError = clock_error.value;
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenTimeout(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Timeout = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Timeout, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_SetWaveGeneratorTimeout(attr->ConnID, Timeout);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SetWaveGenACPPDU(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int subType = VHT_SU_PPDU;
    do
    {
        if (!SCPI_ParamInt(context, &subType, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int demod = attr->PnWifi->commonParam.standard;
        int PPDU = subType;

        IF_RANGE_OUT(demod, WT_DEMOD_11AC_20M, WT_DEMOD_11AC_80_80M);
        IF_RANGE_OUT(PPDU, VHT_SU_PPDU, VHT_MUMIMO_PPDU);

        attr->PnWifi->commonParam.subType = PPDU;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11BE_EHT_PPDU(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int subType = EHT_MU_PPDU;
    do
    {
        if (!SCPI_ParamInt(context, &subType, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int demod = attr->PnWifi->commonParam.standard;
        int PPDU = subType;

        IF_RANGE_OUT(demod, WT_DEMOD_11BE_20M, WT_DEMOD_11BE_160_160M);
        IF_RANGE_OUT(PPDU, EHT_MU_PPDU, EHT_TB_PPDU);

        attr->PnWifi->commonParam.subType = PPDU;
        iRet = WT_GetDefaultWaveParameterWifi(attr->ConnID, demod, PPDU, attr->PnWifi.get());
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "commonParam.bandwidth = " << attr->PnWifi->commonParam.bandwidth << std::endl;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenAxPPDU(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int subType = HE_SU_PPDU;
    do
    {
        if (!SCPI_ParamInt(context, &subType, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int demod = attr->PnWifi->commonParam.standard;
        int PPDU = subType;

        IF_RANGE_OUT(demod, WT_DEMOD_11AX_20M, WT_DEMOD_11AX_80_80M);
        IF_RANGE_OUT(PPDU, HE_SU_PPDU, HE_EXTEND_PPDU);

        attr->PnWifi->commonParam.subType = PPDU;
        iRet = WT_GetDefaultWaveParameterWifi(attr->ConnID, demod, PPDU, attr->PnWifi.get());
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenWifiDuplicate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->PnWifi->commonParam.Duplicate = (0 == Value ? 0 : 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenAWGN(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    SCPI_ParamInt(context, &Value, false);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGenPeakAvgRatioOption(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->PnWifi->commonParam.ReducePARA = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

int Is11B(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnWifi->commonParam.standard;
        if (demod != WT_DEMOD_11B)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t Set11B_DataRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int DataRate[] = {Mbps11, Mbps5point5, Mbps2, Mbps1};
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11B(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 3);

        attr->PnWifi->PN11b.DataRate = DataRate[Value];
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11B_Preamble(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11B(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11b.Preamble = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11B_FilterEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11B(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11b.Fir_11b_22M = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

int Is11AG(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnWifi->commonParam.standard;
        if (demod != WT_DEMOD_11AG)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t Set11AG_DataRate(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AG(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 7);
        attr->PnWifi->PN11a.DataRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

int Is11N(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnWifi->commonParam.standard;
        IF_RANGE_OUT(demod, WT_DEMOD_11N_20M, WT_DEMOD_11N_40M);
    } while (0);

    return iRet;
}

scpi_result_t Set11N_MCS(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 32);
        attr->PnWifi->PN11n.MCS = Value;
        attr->PnWifi->PN11n.NSS = (Value / 8) + 1;
        if (32 == Value)
        {
            attr->PnWifi->PN11n.NSS = 1;
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_FrameType(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 1, 2);
        attr->PnWifi->PN11n.HtFrmType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_Sounding(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11n.NotSounding = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_STBC(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 2);
        attr->PnWifi->PN11n.STBC = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_Smoothing(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11n.SmoothingEnable = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_AGG(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11n.isAggregation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_GI(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11n.isShortGI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_Coding(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11n.CodingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_SoundingNDP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11n.SoundingNDP = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_ESS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 3);
        attr->PnWifi->PN11n.ESS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_QMat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);

        attr->PnWifi.get()->PN11n.Qmat.Q_Flag = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_QMatNtx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(Value, 1, 8);

        attr->PnWifi.get()->PN11n.Qmat.Q_NTX = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_QMatType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(Value, 0, 2);
        attr->PnWifi.get()->PN11n.Qmat.Q_Type = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_QMatDelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value[8] = {0};
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        IF_BREAK(iRet);

        for(int i = 0; i < 8; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            IF_RANGE_OUT(Value[i], -400, 400);
        }

        memcpy(attr->PnWifi.get()->PN11n.Qmat.QDelay, Value, sizeof(Value));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11N_QMatMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {   SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11N(attr);
        IF_BREAK(iRet);

        int Cnt = context->parser_state.numberOfParameters;
        double Value[Cnt] = {0};
        int Diment = (int)sqrt(Cnt / 2);

        if (0 != (Cnt % 2) || Diment < 1 || Diment > 8) //必须是成对的数据,n*n的二维数组，n（1-8）
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        for (int i = 0; i < context->parser_state.numberOfParameters; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }
        }

        int index = 0;
        for (int i = 0; i < Diment; i++)
        {
            for (int j = 0; j < Diment; j++)
            {
                attr->PnWifi->PN11n.Qmat.MatrValue[i][j][0] = Value[index++];
                attr->PnWifi->PN11n.Qmat.MatrValue[i][j][1] = Value[index++];
            }
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

int Is11AC(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnWifi->commonParam.standard;
        if (demod < WT_DEMOD_11AC_20M || demod > WT_DEMOD_11AC_80_80M)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t Set11AC_MCS(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 13);
        attr->PnWifi->PN11ac.MCS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_NSS(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 1, 8);
        attr->PnWifi->PN11ac.NSS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_Beamformed(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11ac.Beamformed = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_SoundingNDP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11ac.SoundingNDP = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_GroupID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }

        if (0 != Value && 63 != Value)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->PnWifi->PN11ac.GroupID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_PartialAID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 511);
        attr->PnWifi->PN11ac.PartialAID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_TXOPPS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11ac.TXOP_PS_NOT_ALLOWED = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_STBC(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11ac.STBC = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_GI(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11ac.isShortGI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_Coding(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);
        attr->PnWifi->PN11ac.CodingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_SU_RUQMat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(Value, 0, 1);

        attr->PnWifi.get()->PN11ac.Qmat.Q_Flag = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_SU_RUQMatNtx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(Value, 1, 8);

        attr->PnWifi.get()->PN11ac.Qmat.Q_NTX = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_SU_RUQMatType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_RANGE_OUT(Value, 0, 2);
        attr->PnWifi.get()->PN11ac.Qmat.Q_Type = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_SU_RUQMatDelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value[8] = {0};
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11AC(attr);
        IF_BREAK(iRet);

        for(int i = 0; i < 8; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            IF_RANGE_OUT(Value[i], -400, 400);
        }

        memcpy(attr->PnWifi.get()->PN11ac.Qmat.QDelay, Value, sizeof(Value));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AC_SU_RUQMatMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Cnt = context->parser_state.numberOfParameters;
        double Value[Cnt] = {0};
        int Diment = (int)sqrt(Cnt / 2);

        if (0 != (Cnt % 2) || Diment < 1 || Diment > 8) //必须是成对的数据,n*n的二维数组，n（1-8）
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        for (int i = 0; i < context->parser_state.numberOfParameters; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }
        }

        int index = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        for (int i = 0; i < Diment; i++)
        {
            for (int j = 0; j < Diment; j++)
            {
                attr->PnWifi->PN11ac.Qmat.MatrValue[i][j][0] = Value[index++];
                attr->PnWifi->PN11ac.Qmat.MatrValue[i][j][1] = Value[index++];
            }
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}
/**
 * @brief wave generator create file at GENERATOR_TMP_WAVE directory
 *
 * @param fileName: only file name, not include path name
 */
void ModifyFilePath(string &fileName)
{
    string SaveName(fileName);
    string firstName(fileName);
    string extName(".bwv");
    const string dirName(GENERATOR_TMP_WAVE);
    size_t pos = SaveName.find_last_of(".");
    if (string::npos != pos)
    {
        firstName = SaveName.substr(0, pos);
        extName = SaveName.substr(pos);
    }
    pos = firstName.find_last_of("/");
    if (string::npos != pos)
    {
        string cmd = string("mkdir -p ") + dirName + firstName.substr(0, pos);
        do_system_cmd(cmd.c_str());
    }

    fileName = dirName + firstName + extName;
}

static void WIFI_MPDU_PSDU(GenWaveWifiStruct_API *pnParameters)
{
    //11ac,11ax默认AGG ON，此时MPDU必须大于0。如果MPDU COUNT = 1,以MPDU[0] length 赋值到PSDU length
    int maxNSS = 0;
    switch (pnParameters->commonParam.standard)
    {
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
    {
        if(pnParameters->PN11n.isAggregation)
        {
            WIFI_PSDU *psdu = &pnParameters->PN11n.psdu;
            if (0 == psdu->MPDUCount)
            {
                psdu->MPDUCount = 1;
                if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                {
                    psdu->MPDULength[0] = 1024;
                }
                else
                {
                    psdu->MPDULength[0] = psdu->psduLen;
                }
            }
            else if (1 == psdu->MPDUCount)
            {
                psdu->psduLen = psdu->MPDULength[0];
            }
        }
        break;
    }
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        if (VHT_SU_PPDU == pnParameters->commonParam.subType)
        {
            WIFI_PSDU *psdu = &pnParameters->PN11ac.psdu;
            if (0 == psdu->MPDUCount)
            {
                psdu->MPDUCount = 1;
                if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                {
                    psdu->MPDULength[0] = 1024;
                }
                else
                {
                    psdu->MPDULength[0] = psdu->psduLen;
                }
            }
            else if (1 == psdu->MPDUCount)
            {
                psdu->psduLen = psdu->MPDULength[0];
            }
            maxNSS = pnParameters->PN11ac.NSS;
        }
        else
        {
            for (int i = 0; i < pnParameters->PN11ac_MUMIMO.UserNum; i++)
            {
                maxNSS += pnParameters->PN11ac_MUMIMO.User[i].NSS;
                WIFI_PSDU *psdu = &pnParameters->PN11ac_MUMIMO.User[i].psdu;
                if (0 == psdu->MPDUCount)
                {
                    psdu->MPDUCount = 1;
                    if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                    {
                        psdu->MPDULength[0] = 1024;
                    }
                    else
                    {
                        psdu->MPDULength[0] = psdu->psduLen;
                    }
                }
                else if (1 == psdu->MPDUCount)
                {
                    psdu->psduLen = psdu->MPDULength[0];
                }
            }
        }
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        if (HE_SU_PPDU == pnParameters->commonParam.subType || HE_EXTEND_PPDU == pnParameters->commonParam.subType)
        {
            WIFI_PSDU *psdu = &pnParameters->PN11ax_SU.psdu;
            if (0 == psdu->MPDUCount)
            {
                psdu->MPDUCount = 1;
                if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                {
                    psdu->MPDULength[0] = 1024;
                }
                else
                {
                    psdu->MPDULength[0] = psdu->psduLen;
                }
            }
            else if (1 == psdu->MPDUCount)
            {
                psdu->psduLen = psdu->MPDULength[0];
            }

            maxNSS = pnParameters->PN11ax_SU.NSS;
        }
        else if (HE_MU_PPDU == pnParameters->commonParam.subType)
        {
            for (int i = 0; i < AX_USER_COUNT; i++)
            {
                int tmpNSS = 0;
                for (int j = 0; j < pnParameters->PN11ax_MU.RU[i].UserNum; j++)
                {
                    tmpNSS += pnParameters->PN11ax_MU.RU[i].User[j].NSS;
                    WIFI_PSDU *psdu = &pnParameters->PN11ax_MU.RU[i].User[j].psdu;
                    if (0 == psdu->MPDUCount)
                    {
                        psdu->MPDUCount = 1;
                        if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                        {
                            psdu->MPDULength[0] = 1024;
                        }
                        else
                        {
                            psdu->MPDULength[0] = psdu->psduLen;
                        }
                    }
                    else if (1 == psdu->MPDUCount)
                    {
                        psdu->psduLen = psdu->MPDULength[0];
                    }
                }
                if (tmpNSS > maxNSS)
                {
                    maxNSS = tmpNSS;
                }
            }
        }
        else if (HE_TB_PPDU == pnParameters->commonParam.subType)
        {
            for (int segment = 0; segment < MAX_SEGMENT; segment++)
            {
                for (int i = 0; i < AX_USER_COUNT; i++)
                {
                    int tmpNSS = 0;
                    for (int j = 0; j < pnParameters->PN11ax_TB.RU[segment][i].UserNum; j++)
                    {
                        tmpNSS += pnParameters->PN11ax_TB.RU[segment][i].User[j].NSS;
                        WIFI_PSDU *psdu = &pnParameters->PN11ax_TB.RU[segment][i].User[j].psdu;
                        if (0 == psdu->MPDUCount)
                        {
                            psdu->MPDUCount = 1;
                            if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                            {
                                psdu->MPDULength[0] = 1024;
                            }
                            else
                            {
                                psdu->MPDULength[0] = psdu->psduLen;
                            }
                        }
                        else if (1 == psdu->MPDUCount)
                        {
                            psdu->psduLen = psdu->MPDULength[0];
                        }
                    }
                    if (tmpNSS > maxNSS)
                    {
                        maxNSS = tmpNSS;
                    }
                }
            }
        }
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        if (EHT_MU_PPDU == pnParameters->commonParam.subType)
        {
            for (int i = 0; i < BE_RU_COUNT; i++)
            {
                int tmpNSS = 0;
                for (int j = 0; j < pnParameters->PN11be_MU.RU[i].UserNum; j++)
                {
                    tmpNSS += pnParameters->PN11be_MU.RU[i].User[j].NSS;
                    WIFI_PSDU *psdu = &pnParameters->PN11be_MU.RU[i].User[j].psdu;
                    if (0 == psdu->MPDUCount)
                    {
                        psdu->MPDUCount = 1;
                        if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                        {
                            psdu->MPDULength[0] = 1024;
                        }
                        else
                        {
                            psdu->MPDULength[0] = psdu->psduLen;
                        }
                    }
                    else if (1 == psdu->MPDUCount)
                    {
                        psdu->psduLen = psdu->MPDULength[0];
                    }
                }
                if (tmpNSS > maxNSS)
                {
                    maxNSS = tmpNSS;
                }
            }
        }
        else if (EHT_TB_PPDU == pnParameters->commonParam.subType)
        {
            for (int segment = 0; segment < BE_MAX_SEGMENT; segment++)
            {
                for (int i = 0; i < AX_USER_COUNT; i++)
                {
                    int tmpNSS = 0;
                    for (int j = 0; j < pnParameters->PN11be_TB.RU[segment][i].UserNum; j++)
                    {
                        tmpNSS += pnParameters->PN11be_TB.RU[segment][i].User[j].NSS;
                        WIFI_PSDU *psdu = &pnParameters->PN11be_TB.RU[segment][i].User[j].psdu;
                        if (0 == psdu->MPDUCount)
                        {
                            psdu->MPDUCount = 1;
                            if(psdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024，避免mpdu超范围
                            {
                                psdu->MPDULength[0] = 1024;
                            }
                            else
                            {
                                psdu->MPDULength[0] = psdu->psduLen;
                            }
                        }
                        else if (1 == psdu->MPDUCount)
                        {
                            psdu->psduLen = psdu->MPDULength[0];
                        }
                    }
                    if (tmpNSS > maxNSS)
                    {
                        maxNSS = tmpNSS;
                    }
                }
            }
        }
        break;
    default:
        break;
    }

    if (maxNSS > pnParameters->commonParam.NSS)
    {
        pnParameters->commonParam.NSS = maxNSS;
    }
}

// 字节加密
static unsigned char SimpleEncryp(unsigned char c)
{
    return ~(((c & 0x93) & 0x93) | (c & 0x6c));
}

std::string GetRefFileName(const char *fileName)
{
    std::string ref_file_name = fileName;
    std::string ref_file_ext = ".3gppref";
    size_t pos = ref_file_name.find_last_of(".");
    if (pos != std::string::npos)
    {
        ref_file_name = ref_file_name.substr(0, pos);
    }
    ref_file_name += ref_file_ext;
    return ref_file_name;
}

static void Save3GPPWaveGenRefParam(std::shared_ptr<AlzParam3GPP> alzParam,
                                    int demod,
                                    const char *fileName)
{
    if (!alzParam || !fileName)
    {
        return;
    }

    nlohmann::json root;
    cellular_param_to_json(root, *alzParam, true);
    std::string json_str = root.dump();
    int length = json_str.length();
    char* serialized_string = strdup(json_str.c_str());
    wtlog::info("demod: %d\n", demod);
    wtlog::info("Save 3GPP WaveGen Ref:\n%s\n", serialized_string);

#if 1
    // 加密, 注意必须与SCPI加载过程匹配使用
    unsigned char *p = (unsigned char *)serialized_string;
    for (int i = 0; i < length; ++i)
    {
        *(p + i) = SimpleEncryp(*(p + i));
    }
#endif

    int header[2] = {WAVE_CFG_BASE_HEAD, WAVE_CFG_SUB_LTE};
    switch (demod)
    {
    case WAVE_CFG_SUB_GSM:
        header[1] = WAVE_CFG_SUB_GSM;
        break;
    case ALG_3GPP_STD_WCDMA:
        header[1] = WAVE_CFG_SUB_WCDMA;
        break;
    case ALG_3GPP_STD_5G:
        header[1] = WAVE_CFG_SUB_NR;
        break;
    case ALG_3GPP_STD_4G:
        header[1] = WAVE_CFG_SUB_LTE;
        break;
    case ALG_3GPP_STD_NB_IOT:
        header[1] = WAVE_CFG_SUB_NB_IOT;
        break;
    default:
        break;
    }

    std::string refName = GetRefFileName(fileName);
    FILE *fp = fopen(refName.c_str(), "wb");
    if (fp)
    {
        fwrite((const void *)header, sizeof(header), 1, fp);
        fwrite((const void *)(&demod), sizeof(demod), 1, fp);
        fwrite((const void *)(&length), sizeof(length), 1, fp);
        fwrite(serialized_string, length, 1, fp);
        fclose(fp);
    }

    free(serialized_string);  // 释放内存
}

int WaveGen(SPCIUserParam *attr, string fileName, MutiPNExtendInfo *MutiPnExInfo)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = attr->WaveGenDemod;

    ModifyFilePath(fileName);
    switch (demod)
    {
    case WT_DEMOD_BT:
        //PrintBt(attr->PnBt.get());
		add_wave_extend_param(attr);
        iRet = WT_WaveGeneratorBTV2(attr->ConnID, fileName.c_str(), attr->PnBt.get(), MutiPnExInfo);
        break;
    case WT_DEMOD_CW:
        iRet = WT_WaveGeneratorCW(attr->ConnID, fileName.c_str(), attr->PnCW.get(), MutiPnExInfo);
        break;
    case WT_DEMOD_GLE:
        iRet = WT_WaveGeneratorSLE(attr->ConnID, fileName.c_str(), attr->PnGLE.get(), MutiPnExInfo);
        break;
    case WT_DEMOD_LRWPAN_FSK:
    case WT_DEMOD_LRWPAN_OQPSK:
    case WT_DEMOD_LRWPAN_OFDM:
        iRet = WT_WaveGeneratorWiSun(attr->ConnID, fileName.c_str(), attr->PnWiSun.get());
        break;
    default:
        if (IsAlg3GPPStandardType(demod))
        {
            std::shared_ptr<AlzParam3GPP> pAlz3gpp = nullptr;
            iRet = WT_WaveGenerator3GPP(attr->ConnID,
                                        fileName.c_str(),
                                        attr->Pn3GPP.get(),
                                        sizeof(Alg_3GPP_WaveGenType),
                                        pAlz3gpp);
            if (pAlz3gpp)
            {
                Save3GPPWaveGenRefParam(pAlz3gpp, demod, fileName.c_str());
            }
            else
            {
                wtlog::error("WaveGenerator3GPP RefFile Save failed");
            }
            break;
        }
        ConvertPNStruct(attr);
        WIFI_MPDU_PSDU(attr->PnWifi.get());
        iRet = WT_WaveGeneratorWifi(attr->ConnID, fileName.c_str(), attr->PnWifi.get(), MutiPnExInfo);
        break;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Create %s %s, iRet = %d(0x%X)\r\n", fileName.c_str(), (WT_ERR_CODE_OK == iRet ? "OK" : "Fail"), iRet, iRet);

    return iRet;
}

static int Is11axER(int demod, int PPDU)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (demod < WT_DEMOD_11AX_20M || demod > WT_DEMOD_11AX_80_80M)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (HE_EXTEND_PPDU != PPDU)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

long GetFileSize(string &FileName)
{
    std::ifstream in(FileName);
    in.seekg(0,ios::end);
    long size = 0;
    size = in.tellg();
    in.close();
    return size;
}


int GenerateWave(SPCIUserParam *attr, const char *fileName, MutiPNExtendInfo *MutiPnExInfo = NULL)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = attr->WaveGenDemod;
    int subType = attr->PnWifi->commonParam.subType;
    int bandWidth = 20;

    switch (demod)
    {
    case WT_DEMOD_11N_40M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BA_40M:
    case WT_DEMOD_11AZ_40M:
        bandWidth = 40;
        break;
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BA_80M:
    case WT_DEMOD_11AZ_80M:
        bandWidth = 80;
        break;
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AC_80_80M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11AZ_160M:
        bandWidth = 160;
        break;
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        bandWidth = 320;
        break;
    default:
        bandWidth = 20;
        break;
    }

    //HE-ER 10M,20M; ah 1M,2M,4M,8M,16M
    if (WT_ERR_CODE_OK != Is11axER(demod, subType) && !(WT_DEMOD_11AH_1M <= demod && demod <= WT_DEMOD_11AH_16M))
    {
        attr->PnWifi->commonParam.bandwidth = bandWidth;
    }

    iRet = WaveGen(attr, fileName, MutiPnExInfo);

    return iRet;
}

bool isSLE(SPCIUserParam *attr)
{
    int demod = attr->WaveGenDemod;
    if (demod == WT_DEMOD_GLE)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool isTFTB(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    int demod = attr->WaveGenDemod;
    int ppdu = attr->PnWifi->commonParam.subType;
    WIFI_PSDU *psdu = nullptr;
    GenWaveWifiStruct_API *pnParameters = attr->PnWifi.get();
    switch (demod)
    {
    case WT_DEMOD_11AG:
        psdu = &pnParameters->PN11a.psdu;
        break;
    case WT_DEMOD_11B:
        psdu = &pnParameters->PN11b.psdu;
        break;
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        psdu = &pnParameters->PN11n.psdu;
        break;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        psdu = &pnParameters->PN11ac.psdu;
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        if (HE_SU_PPDU == ppdu || HE_EXTEND_PPDU == ppdu)
        {
            psdu = &pnParameters->PN11ax_SU.psdu;
        }
        else if (HE_MU_PPDU == ppdu)
        {
            for (int i = 0; i < AX_RU_COUNT; i++)
            {
                if (pnParameters->PN11ax_MU.RU[i].UserNum > 0)
                {
                    psdu = &pnParameters->PN11ax_MU.RU[i].User[0].psdu;
                    break;
                }
            }
        }
        else if (HE_TB_PPDU == ppdu)
        {
            iRet = WT_ERR_CODE_OK;
        }
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
    case WT_DEMOD_11BE_80_80M:
        if (EHT_MU_PPDU == ppdu)
        {
            for (int i = 0; i < BE_RU_COUNT; i++)
            {
                if (pnParameters->PN11be_MU.RU[i].UserNum > 0)
                {
                    psdu = &pnParameters->PN11be_MU.RU[i].User[0].psdu;
                    break;
                }
            }
        }
        else if (EHT_TB_PPDU == ppdu)
        {
            iRet = WT_ERR_CODE_OK;
        }
        break;
    case WT_DEMOD_11AZ_20M:
    case WT_DEMOD_11AZ_40M:
    case WT_DEMOD_11AZ_80M:
    case WT_DEMOD_11AZ_160M:
    {
        if (HE_TB_RANGING_PPDU == ppdu)
        {
            iRet = WT_ERR_CODE_OK;
        }
        break;
    }
    default:
        break;
    }

    if (psdu && PSDUType_TriggerFrame == psdu->psduType)
    {
        iRet = WT_ERR_CODE_OK;
    }

    return (WT_ERR_CODE_OK == iRet);
}

int GetGeneratedFileContext(SPCIUserParam *attr, char *fileName, std::unique_ptr<char[]> &RetData, int &RetSize)
{
    int iRet = WT_ERR_CODE_OK;

    //实际信号文件+配置文件，eg：.tf .tb .tb_mumimo等
    //组合生成信号最终返回的二进制内容：int FileCnt + int File1Size + char[32]File1SuffixName + char[n]File1Data+...int File2Size+...
    string RetFileName[10];
    int FileCnt = 0;    //文件数
    RetSize = sizeof(int);    //需要返回的数据总大小

    //先遍历获取所有文件，以及要返回的总长度
    RetFileName[FileCnt] = string(fileName);
    ModifyFilePath(RetFileName[FileCnt]);
    if (0 == access(RetFileName[FileCnt].c_str(), R_OK))
    {
        RetSize += sizeof(int);
        RetSize += sizeof(char) * 32;
        RetSize += GetFileSize(RetFileName[FileCnt]);
        FileCnt ++;
    }
    //WTLog::Instance().WriteLog(LOG_DEBUG, "filename = %s\n", RetFileName[FileCnt].c_str());


    if (IsAlg3GPPStandardType(attr->WaveGenDemod))
    {
        RetFileName[FileCnt] = RetFileName[0].substr(0, RetFileName[0].rfind(".")) + string(".3gppref");
        if (0 == access(RetFileName[FileCnt].c_str(), R_OK))
        {
            RetSize += sizeof(int);
            RetSize += sizeof(char) * 32;
            RetSize += GetFileSize(RetFileName[FileCnt]);
            FileCnt++;
        }
    }
    else if(isTFTB(attr)) //.tb
    {
        DIR *Dir = NULL; // 路径句柄
        struct dirent *File = NULL;
        string FilePath = RetFileName[0].substr(0, RetFileName[0].rfind("/"));
        Dir = opendir(FilePath.c_str());

        //遍历FilePath目录下的所有文件
        int StartPos = RetFileName[0].rfind("/") + 1;
        int StrLen = RetFileName[0].find_last_of(".") - StartPos;
        string RealFileName = RetFileName[0].substr(StartPos, StrLen);
        while ((File = readdir(Dir)) != NULL)
        {
            if(strstr(File->d_name, RealFileName.c_str()) != NULL && strstr(File->d_name, ".tb") != NULL)
            {
                RetFileName[FileCnt] = FilePath.c_str() + string("/") + string(File->d_name);
                if (0 == access(RetFileName[FileCnt].c_str(), R_OK))
                {
                    RetSize += sizeof(int);
                    RetSize += sizeof(char) * 32;
                    RetSize += GetFileSize(RetFileName[FileCnt]);
                    FileCnt++;
                }
                
            }
        }
        closedir(Dir);
    }

    if(isSLE(attr)) //.sle
    {
        RetFileName[FileCnt] = RetFileName[0].substr(0, RetFileName[0].rfind(".")) + string(".sle");
        if (0 == access(RetFileName[FileCnt].c_str(), R_OK))
        {
            RetSize += sizeof(int);
            RetSize += sizeof(char) * 32;
            RetSize += GetFileSize(RetFileName[FileCnt]);
            FileCnt++;
        }
    }

    //返回结果内容
    //WTLog::Instance().WriteLog(LOG_DEBUG, "RetSize = %d\n",RetSize);
    RetData.reset(new(std::nothrow) char[RetSize]);
    char *pBuf = RetData.get();
    memset(pBuf, 0, RetSize);
    if(FileCnt != 0)
    {
        memcpy(pBuf, &FileCnt, sizeof(int)); //file num
        //WTLog::Instance().WriteLog(LOG_DEBUG, "FileCnt = %d\n",FileCnt);
        pBuf += sizeof(int);
        for(int i = 0; i < FileCnt; i++)
        {
            std::unique_ptr<char[]>SigFileData = nullptr;
            int FileLen = 0;
            ReadFile(RetFileName[i], SigFileData, FileLen);

            //WTLog::Instance().WriteLog(LOG_DEBUG, "i = %d, FileLen = %d\n",i, FileLen);    //filelen
            memcpy(pBuf, &FileLen, sizeof(int));
            pBuf += sizeof(int);

            string SuffixStr = RetFileName[i].substr(RetFileName[i].find_last_of('.') + 1); //suffix
            //WTLog::Instance().WriteLog(LOG_DEBUG, "suffixstr = %s\n",SuffixStr.c_str());
            memcpy(pBuf, SuffixStr.c_str(), SuffixStr.length());
            pBuf += sizeof(char) * 32;

            memcpy(pBuf, SigFileData.get(), FileLen);   //data
            pBuf += FileLen;

            //最后删除临时保存的文件
            if (0 == access(RetFileName[i].c_str(), R_OK))
            {
                //remove bwv/csv file
                string cmd = string("rm -rf ") + "'" + RetFileName[i] + "'";
                do_system_cmd(cmd.c_str());
            }

        }
    }
    else
    {
        iRet = WT_ERR_CODE_GENERATE_FAIL;
    }
    return iRet;
}

/**
 * @brief move waveform from tmp/tmpwave dir to /tmp/wave dir
 *
 * @param src_file : source waveform path
 * @param dst_file : destination waveform path
 * @param have_tb : is tb waveform
 */
static void move_wave(std::string src_file, std::string dst_file, bool have_tb = false)
{
    std::string cmd;

    make_file_dir(SCPI_WaveDir(), dst_file);

    cmd = "mv ";
    cmd += src_file;
    cmd += " ";
    cmd += SCPI_WaveDir();
    cmd += dst_file;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
    do_system_cmd(cmd);

    if (have_tb)
    {
        std::string tb_ext = std::string(".tb");
        std::string tb_dst = dst_file.substr(0, dst_file.rfind(".")) + tb_ext;
        std::string tb_src = src_file.substr(0, src_file.rfind(".")) + tb_ext;
        cmd = "mv ";
        cmd += tb_src;
        cmd += " ";
        cmd += SCPI_WaveDir();
        cmd += tb_dst;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
        do_system_cmd(cmd);
    }
}

/**
 * @brief move waveform from tmp/tmpwave dir to /tmp/wave dir
 *
 * @param src_file : source waveform path
 * @param dst_file : destination waveform path
 */
static void move_file(std::string src_file, std::string dst_file)
{
    std::string cmd;

    make_file_dir(SCPI_WaveDir(), dst_file);

    cmd = "mv ";
    cmd += src_file;
    cmd += " ";
    cmd += SCPI_WaveDir();
    cmd += dst_file;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
    do_system_cmd(cmd);
}

/**
 * @brief Get the save wave name object
 *
 * @param fileType : 0 = bwv, 1 = csv
 * @param name : waveform name
 * @return std::string : correct waveform name
 */
static std::string get_save_wave_name(int fileType, std::string name)
{
    std::string ext_bwv(".bwv");
    std::string ext_csv(".csv");
    std::string ext_wave = (0 == fileType ? ext_bwv : ext_csv);
    auto pos = name.rfind(ext_wave);
    if (pos == std::string::npos || pos != name.length() - ext_wave.length())
    {
        if (pos == std::string::npos)
        {
            if (0 == fileType)
            {
                pos = name.rfind(ext_csv);
            }
            else
            {
                pos = name.rfind(ext_bwv);
            }

            if (pos != std::string::npos)
            {
                name = name.substr(0, pos) + ext_wave;
            }
            else
            {
                name += ext_wave;
            }
        }
        else
        {
            pos = name.rfind(".");
            name = name.substr(0, pos) + ext_wave;
        }
    }
    return name;
}

scpi_result_t SCPIWaveGenGo(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    size_t copyLen = 0;
    char fileName[256] = { 0 };
    struct tm *Mtm;
    time_t Now;
    int fileType = 0;//0 == bwv, 1 == csv
    std::string saveWaveName("");

    memset(fileName, 0, sizeof(fileName));
    SCPI_ParamInt(context, &fileType, false);
    SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, false);
    if (copyLen > 0)
    {
        saveWaveName = fileName;
        saveWaveName = get_save_wave_name(fileType, saveWaveName);
    }

    time(&Now);
    Mtm = localtime(&Now);
    sprintf(fileName, "./SaveWave/scpi-%d-%d-%d_%d-%d-%d.%s",
            1900 + Mtm->tm_year,
            Mtm->tm_mon + 1,
            Mtm->tm_mday,
            Mtm->tm_hour,
            Mtm->tm_min,
            Mtm->tm_sec,
            0 == fileType ? "bwv" : "csv");


    iRet = GenerateWave(attr, fileName);
    if(iRet != WT_ERR_CODE_OK)
    {
        SCPI_ErrorPush(context, (iRet));
        return SCPI_RES_ERR;
    }
    else    //生成成功，组合返回生成信号或者配置文件内容
    {

        if (0 == saveWaveName.length()) // Send waveform data to peer socket
        {
            int RetSize = 0;
            std::unique_ptr<char[]> RetData = nullptr;
            iRet = GetGeneratedFileContext(attr, fileName, RetData, RetSize);
            if(iRet != WT_ERR_CODE_OK || RetData == nullptr)
            {
                SCPI_ErrorPush(context, (iRet));
                return SCPI_RES_ERR;
            }
            else
            {
                SCPI_ResultArbitraryBlock(context, (char*)(RetData.get()), RetSize);
                return SCPI_RES_OK;
            }
        }
        else // move waveform to tester's wave dir
        {
            std::string src_file(fileName);
            ModifyFilePath(src_file);
            move_wave(src_file, saveWaveName, isTFTB(attr));

            if (IsAlg3GPPStandardType(attr->WaveGenDemod))
            {
                move_file(src_file.substr(0, src_file.rfind(".")) + std::string(".3gppref"), saveWaveName.substr(0, saveWaveName.rfind(".")) + std::string(".3gppref"));
            }

            return SCPI_RES_OK;
        }
    }
}

static int LoadGeneratedFileAsCapture(scpi_t *context, string &fileName)
{
    int iRet = WT_ERR_CODE_OK;
    std::vector<std::string> low_name_list;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        iRet = check_waveform_exist_v2(context, (char *)fileName.c_str(), low_name_list, false, false);
        if (iRet)
        {
            break;
        }

        int wave2Flag = 0;
        if (attr->vsaParam.Demode == WT_DEMOD_11AC_80_80M || attr->vsaParam.Demode == WT_DEMOD_11AX_80_80M)
        {
            wave2Flag = 1;
        }

        //处理下文件路径
        std::string tmpWave(fileName);
        std::string wavePath(SCPI_WaveDir());
        if (tmpWave.at(0) == '/')
        {
            tmpWave = string(".") + tmpWave;
        }
        wavePath += tmpWave;

        iRet = WT_LoadSignalAsCapture(attr->ConnID, wavePath.c_str(), low_name_list[0].c_str(), wave2Flag);
        if (WT_ERR_CODE_OK != iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Load file:" << wavePath << " , fail :" << iRet << std::endl;
            break;
        }
        /* 配置tb分析参数 */
        if (isTFTB(attr))
        {
            std::string tb_RefFile = fileName.substr(0, fileName.rfind(".")) + string(".tb");
            if (0 == access(tb_RefFile.c_str(), R_OK))
            {
                std::unique_ptr<char[]> SigFileData = nullptr;
                int FileLen = 0;
                ReadFile(tb_RefFile, SigFileData, FileLen);
                iRet = WT_SetExternAnalyzeParam(attr->ConnID,
                                                attr->vsaAlzParam.analyzeParamWifi.Demode,
                                                AX_TB_REF_FILE,
                                                (void *)SigFileData.get(), FileLen);
            }
            else
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "can't find " << tb_RefFile << std::endl;
            }
        }
        /* 配置通用分析参数 */
        iRet = SetVsaAlzParam(attr);
        /* 退出tb分析模式 */
        if (isTFTB(attr))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set TB analyze flag = -1" << std::endl;
            iRet = WT_SetExternAnalyzeParam(attr->ConnID,
                                            attr->vsaAlzParam.analyzeParamWifi.Demode,
                                            AX_TB_ANALYZE_EXIT,
                                            nullptr, 0);
        }
        // remove_low_file(low_name_list[0]);
    } while (0);
    return iRet;
}

#if 0
static void RemoveOtherFile(string fileName, bool userDefine = false)
{
    size_t StartPos = fileName.find_last_of("/") + 1;
    size_t EndPos = fileName.find_last_of(".");
    string SaveName = fileName.substr(StartPos, EndPos - StartPos);
    string dirName = fileName.substr(0, StartPos);
    char cmd[256] = {0};

    if (userDefine)
    {
        const std::string lowFile(".low");
        std::vector<std::string>filesList;
        GetPathFilesName(dirName, filesList);
        for (auto &item : filesList)
        {
            if (std::string::npos != item.find(SaveName))
            {
                std::string subStr = item.substr(item.length() - lowFile.length());
                if (subStr != lowFile)
                {
                    sprintf(cmd, "rm -rf %s", item.c_str());
                    WTLog::Instance().WriteLog(LOG_DEBUG, "%s\n", cmd);
                    do_system_cmd(cmd);
                }
            }
        }
    }
    else
    {
        sprintf(cmd, "rm -rf %s%s.*", dirName.c_str(), SaveName.c_str());
        WTLog::Instance().WriteLog(LOG_DEBUG, "%s\n", cmd);
        do_system_cmd(cmd);
    }
}

#endif

scpi_result_t SCPIWaveGentoVsaLoadAsCapture(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    std::string saveWaveName("");
    char fileName[256] = { 0 };
    struct tm *Mtm;
    time_t Now;

    char buf[256] = {0};
    size_t copyLen = 0;

    time(&Now);
    Mtm = localtime(&Now);
    sprintf(fileName, "./SaveWave/scpi-%d-%d-%d_%d-%d-%d.bwv",1900 + Mtm->tm_year, Mtm->tm_mon + 1, Mtm->tm_mday, Mtm->tm_hour, Mtm->tm_min, Mtm->tm_sec);
    do
    {
        SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, false);
        if (copyLen > 0)
        {
            //如果自定义了文件名，保存到正常信号文件目录
            saveWaveName = std::string(buf);
            saveWaveName = get_save_wave_name(0, saveWaveName);
        }

        iRet = GenerateWave(attr, fileName);
        if(iRet)
        {
            break;
        }
        //生成成功，加载到vsa分析
        const string DirName(GENERATOR_TMP_WAVE);
        string RealName = DirName + fileName;
        if (copyLen > 0) //save waveform to wave dir
        {
            move_wave(RealName, saveWaveName, isTFTB(attr));
            RealName = saveWaveName;
        }

        iRet = LoadGeneratedFileAsCapture(context, RealName);

    }while(0);

    return SCPI_ResultOK(context, iRet);
}

static int ApplyWaveformToSource(scpi_t * context, string &fileName)
{
    std::vector<std::string> low_name_list;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = check_waveform_exist_v2(context, (char *)fileName.c_str(), low_name_list);
    if (WT_ERR_CODE_OK == iRet)
    {
        // reove the last vsg .low file
        // remove_all_vsg_low(attr);

        //配置pn文件名
        attr->vsgPattern.resize(low_name_list.size());
        for(int i = 0; i < low_name_list.size(); i++)
        {
            memset(attr->vsgPattern[i].WaveName, 0, MAX_NAME_SIZE);
            memcpy(attr->vsgPattern[i].WaveName, low_name_list[i].c_str(), low_name_list[i].length());
            attr->vsgPattern[i].WaveType = SIG_USERFILE;
        }
        attr->NeedSetVSGPattern = true;
    }
    return iRet;
}

scpi_result_t SCPIWaveGentoSetVsgWave(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    std::string saveWaveName("");
    char fileName[256] = { 0 };
    struct tm *Mtm;
    time_t Now;

    char buf[256] = {0};
    size_t copyLen = 0;

    time(&Now);
    Mtm = localtime(&Now);
    sprintf(fileName, "./SaveWave/scpi-%d-%d-%d_%d-%d-%d.bwv",1900 + Mtm->tm_year, Mtm->tm_mon + 1, Mtm->tm_mday, Mtm->tm_hour, Mtm->tm_min, Mtm->tm_sec);
    do
    {
        SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, false);

        if(copyLen > 0)
        {
            //如果自定义了文件名，保存到正常信号文件目录
            saveWaveName = std::string(buf);
            saveWaveName = get_save_wave_name(0, saveWaveName);
        }

        iRet = GenerateWave(attr, fileName);
        if(iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GenerateWave fail" << std::endl;
            break;
        }

        //生成成功，应用到VSG
        const string DirName(GENERATOR_TMP_WAVE);
        string RealName = DirName + fileName;
        if (copyLen > 0) // save waveform to wave dir
        {
            move_wave(RealName, saveWaveName, isTFTB(attr));
            RealName = saveWaveName;
        }
        iRet = ApplyWaveformToSource(context, RealName);
        if (iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ApplyWaveformToSource fail" << std::endl;
        }
    }while(0);

    return SCPI_ResultOK(context, iRet);
}

static int IsCW(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnCW->commonParam.standard;
        if (WT_DEMOD_CW != demod)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t SetWaveGen_CW_psduLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsCW(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 100, 4096); //range 100~4096
        attr->PnCW->psduLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_CW_Type(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsCW(attr);
        if (iRet)
        {
            break;
        }
        IF_RANGE_OUT(Value, 0, 1); //range 0~1

        const int Type[] = {Sin0Hz, Sin1MHz};
        attr->PnCW->commonParam.subType = Type[Value];
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetPNFileExternSettingData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    const char *data = nullptr;
    size_t len = 0;

    do
    {
        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_SetPNFileExternSettingData(attr->ConnID, (void *)data, len);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t CleanPNFileExternSettingData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_SetPNFileExternSettingData(attr->ConnID, nullptr, 0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetMPRepeatGap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double IFG = 0.0;

    do
    {
        if (!SCPI_ParamDouble(context, &IFG, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (IFG < 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_MutiPNExtendInfo.MutiPNIFG = IFG;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetMPRepeatCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int repeat = 0;

    do
    {
        if (!SCPI_ParamInt(context, &repeat, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (repeat < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_MutiPNExtendInfo.MutiPNRepeat = repeat;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetMPGap(scpi_t *context) // 配置HG、PG
{
    int iRet = WT_ERR_CODE_OK;
    int i = 0;
    double Gap[2]; // 0：HG 1:PG
    //int demod = 0;

    do
    {
        for (i = 0; i < 2; ++i)
        {
            if (!SCPI_ParamDouble(context, &Gap[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Gap[i] < 0.0)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for (i = 0; i < 2; ++i)
        {
            attr->m_MutiPNExtendInfo.MutiWaveGap[i] = Gap[i]; // 0:HG  1:PG
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetMPSaveTempPn(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    char fileName[256] = { 0 };
    int fileType = 0;//0 == bwv, 1 == csv
    int idx = 0;

    memset(fileName, 0, sizeof(fileName));
    if (!SCPI_ParamInt(context, &idx, true))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        SCPI_ErrorPush(context, (iRet));
        return SCPI_RES_ERR;
    }

    if (!SCPI_ParamInt(context, &fileType, true))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        SCPI_ErrorPush(context, (iRet));
        return SCPI_RES_ERR;
    }

    sprintf(fileName, "./Temp_%d_%d.%s", gettid(), idx, 0 == fileType ? "bwv" : "csv");

    attr->m_MutiPNExtendInfo.pnIndex = attr->m_MutiPNFilesName.size();
    iRet = GenerateWave(attr, fileName, &attr->m_MutiPNExtendInfo);
    if(iRet != WT_ERR_CODE_OK)
    {
        SCPI_ErrorPush(context, (iRet));
        return SCPI_RES_ERR;
    }

    int demod = attr->WaveGenDemod;
    MutiPNUserGenParam tmpParam;
    switch (demod)
    {
    case WT_DEMOD_BT:
        tmpParam.BtParam = *(attr->PnBt.get());
        break;
    case WT_DEMOD_CW:
        tmpParam.CwParam = *(attr->PnCW.get());
        break;
    case WT_DEMOD_GLE:
        tmpParam.GleParam = *(attr->PnGLE.get());
        break;
    default:
        tmpParam.wifiParam = *(attr->PnWifi.get());
        break;
    }
    attr->m_MutiPNUserGenParam.push_back(tmpParam);
    attr->m_MutiPNFilesName.push_back(string(fileName));
    attr->m_MutiPNFileStreamCnt.push_back(attr->m_MutiPNExtendInfo.streamCnt);

    SCPI_ResultInt(context, iRet);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetMPCreatePn(scpi_t *context)
{
    int i = 0;
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    size_t copyLen = 0;
    MutiPNCatenateInfo info;

    if (!SCPI_ParamCopyText(context, info.fileName, sizeof(info.fileName) - 1, &copyLen, true))
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        SCPI_ErrorPush(context, (iRet));
        return SCPI_RES_ERR;
    }

    string str = string(info.fileName);
    size_t pos = str.find_last_of('/');
    if (pos != string::npos)
    {
        str = "/tmp/tmpwave/" + str.substr(pos);
    }
    else
    {
        str = "/tmp/tmpwave/" + str;
    }
    memset(info.fileName, 0, 256);
    memcpy(info.fileName, str.data(), str.size());

    for (i = 0; i <  attr->m_MutiPNFilesName.size(); i++)
    {
        if (!SCPI_ParamInt(context, &info.index[i], true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            SCPI_ErrorPush(context, (iRet));
            return SCPI_RES_ERR;
        }

        if (info.index[i] < 0 || info.index[i] >= attr->m_MutiPNFilesName.size())
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            SCPI_ErrorPush(context, (iRet));
            return SCPI_RES_ERR;
        }
        
        info.srcPnFiles[i] = const_cast<char *>(attr->m_MutiPNFilesName[i].data());
        info.streamCnt[i] = attr->m_MutiPNFileStreamCnt[i];
    }
    info.srcFilesNum = i;
    info.userParam = const_cast<MutiPNUserGenParam *>(attr->m_MutiPNUserGenParam.data());

    iRet = WT_WaveGeneratorCatenateFiles(attr->ConnID, &info);
    if (iRet != WT_ERR_CODE_OK)
    {
        SCPI_ErrorPush(context, (iRet));
        return SCPI_RES_ERR;
    }
    else
    {
        // 返回文件长度+数据
        std::unique_ptr<char[]> data = nullptr;
        std::unique_ptr<char[]>SigFileData = nullptr;
        int FileLen = 0;
        int FileNum = 1;
        if (ReadFile(info.fileName, SigFileData, FileLen) != WT_ERR_CODE_OK)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            SCPI_ErrorPush(context, (iRet));
            return SCPI_RES_ERR;
        }

        unsigned int dataLen = FileLen + sizeof(int) * 2 + sizeof(char) * 32;
        data.reset(new(std::nothrow) char[dataLen]);
        char *pData = data.get();
        memset(pData, 0, dataLen);
        memcpy(pData, &FileNum, sizeof(int));
        pData += sizeof(int);

        memcpy(pData, &FileLen, sizeof(int));
        pData += sizeof(int);

        string SuffixStr = ((string)info.fileName).substr(((string)info.fileName).find_last_of('.') + 1); //suffix
        memcpy(pData, SuffixStr.c_str(), SuffixStr.length());
        pData += sizeof(char) * 32;

        memcpy(pData, SigFileData.get(), FileLen);

        SCPI_ResultArbitraryBlock(context, (char*)(data.get()), dataLen);

        // 删除文件
        /*str = string("rm -rf ") + (string)(info.fileName);
        do_system_cmd(str.c_str());*/

        attr->m_MutiPNDstFileName.push_back((string)(info.fileName));
    }

    for (int i = 0; i < attr->m_MutiPNFilesName.size(); ++i)
    {
        str = string("rm -rf ") + "/tmp/tmpwave/" + attr->m_MutiPNFilesName[i];
        do_system_cmd(str.c_str());
    }
    attr->m_MutiPNUserGenParam.clear();
    attr->m_MutiPNFilesName.clear();
    attr->m_MutiPNFileStreamCnt.clear();
    return SCPI_ResultOK(context, iRet);
}
