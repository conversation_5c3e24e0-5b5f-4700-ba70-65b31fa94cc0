#pragma once
#ifndef _INSTRUMENT_HANDLE_H__
#define _INSTRUMENT_HANDLE_H__

#include <vector>
#include <map>
#include <string>
#include <memory>
using namespace std;
#define MIN_SAMPLE_RATE     (15 * MHz)


#define ValidSegmentSignal(seg, sig)    \
	if (seg > 2 || sig > MAX_NUM_OF_CHNNEL) \
	{   \
		return WT_ERR_CODE_PARAMETER_MISMATCH;\
	}
enum WaveCfgHeadEnum
{
    WAVE_CFG_BASE_HEAD = 0x11223344,
    WAVE_CFG_SUB_TB = 0x11223301,
    WAVE_CFG_SUB_TB_MUMIMO = 0x11223302,
    WAVE_CFG_SUB_EHT_TB = 0x11223303,
    WAVE_CFG_SUB_AZ_HE_TB = 0x11223304,
    WAVE_CFG_SUB_LTE = 0x11223350,
    WAVE_CFG_SUB_NR = 0x11223351,
    WAVE_CFG_SUB_NB_IOT = 0x11223352,
    WAVE_CFG_SUB_WCDMA = 0x11223353,
};

typedef struct
{
    unique_ptr<char[]> pData;	//need new memory
    char *pData2;				//just memory point
    size_t DataSize;			//data size to send
    bool isNeedFree;			//if isNeedFree == false, pData=nullptr; other wise pData2=nullptr
}memCollector;

inline bool IsAlg3GPPStandardType(const int& Type)
{
    if (ALG_3GPP_STANDARD_TYPE::ALG_3GPP_STD_5G <= Type && 
        ALG_3GPP_STANDARD_TYPE::ALG_3GPP_STD_WCDMA >= Type)
    {
        return true;
    }
    return false;
}

extern bool g_Alg3GPPSwitchCaseFlg;

class  InstrumentHandle
{
public:
    InstrumentHandle(int comuType = WT_COMU_TYPE_TCP);
    virtual ~InstrumentHandle();
public:
    virtual int QueryTesterVersionInfo(char *testerInfo);
    virtual int ConnectDevice(ConnectedUnit *connUnit, int unitCount, int connType);
    virtual int DisconnectDevice();
    virtual int FactoryReset();
    virtual int SetTesterIpCfg(const char *ipAddr, const char *subMask, const char *netGate, const char *testerName);
    virtual int SetSubTesterCfg(SubTesterCfg *testerCfg, int testerCount);
    virtual int GetSubTesterCfg(SubTesterCfg testerCfg[WT_SUB_TESTER_INDEX_MAX], int *subTesterCount);
    virtual int GetCurrSubTesterCfg(SubTesterCfg *testerCfg);
    virtual int GetSlaveTesterSubTesterCfg(int slaveTesterID, SubTesterCfg *testerCfg);
    virtual int GetTesterInfo(TesterInfo *info);
    virtual int GetConnectStatus();
    virtual int GetSlaveTesterInfo(int slaveTesterID, TesterInfo *testerInfo);
    virtual int SelfCalibration(bool isStart = true);
    virtual int QuerySelfCalibrationPercent(int *percent);
    virtual int SelfCalibrationAutoRunning(int isAutoRun);

    virtual int GetLicense(LicItemInfo_API *testerLicense, int licenseMaxCount, int *licActualcount);
    virtual int GetSlaveTesterLicense(int slaveTesterID, LicItemInfo_API *licInfo, int licenseMaxCount, int *licActualcount);
    virtual int GetNetInfo(VirtualNetType *netInfo);
    virtual int GetNetLink(bool *LinkStatus);
    virtual int GetTesterIpAddressType(bool *IsDhcp);
    virtual int SetNetInfo(VirtualNetType *netInfo);
    virtual int FirmwareUpdate(const char *packageFile, char *upgradePackage, unsigned int packageSize);
    virtual int FirmwareUpdate(ExchangeBuff *pstSendBuff, int n);
    virtual int LicenseUpdate(char *licensePackage, unsigned int packageSize);
    virtual int LicensePack(char *licensePackage, unsigned int packageSize, LicItemInfo_API *licInfo, unsigned int *actualSize);
    virtual int LicPackUpdate(char *Message);
    virtual int FirmwareRestore(int restoreOption);
    virtual int SetRunTimeConfiguration(char *config, unsigned int size);
    virtual int QueryRunTimeConfiguration(char *resultBuff, unsigned int resultBuffSize, char *ConfigParam);
    virtual int SetExternalGain(double extGain);
    virtual int WriteCalFile(char *fileBuffer, unsigned int fileBuffSize, const char *fileName);
    virtual int GetCalFile(const char *fileName, char *fileBuff, unsigned int buffSize);
    virtual int SetTempCal(int value);
    virtual int SetFlatnessCal(int value);
    virtual int SetCalData(char *data, unsigned int dataSize);
    virtual int GetCalData(char *data, unsigned int dataSize);
    virtual int ShutDownDevice();
    virtual int DeleteAllLicense();
    virtual int ResetSubNetConfiguration();
    virtual int SwitchMode(int targetMode);
    virtual int AddMimoTester(ConnectedUnit connUnit);
    virtual int RemoveMimoTester();

    virtual int GetGUIVersion(GUIVersion *guiVersion, unsigned int maxGuiVersionCnt, unsigned int *actualGuiVersionCnt);
    virtual int GetGUIFileVersion(const char *techName, GUIVersion *version);
    virtual int GetGUIFile(const char *techName, const char *saveDir);

    virtual int BeamformingCalibrationChannelEstDutTX(int demod);
    virtual int BeamformingCalibrationChannelEstDutRX(double *dutChannelEst, int dutChannelEstLength);
    virtual int BeamformingCalibrationResult(double *resultAngle, int *resultAngleLength);
    virtual int BeamformingVerification(double *diffPower);
    virtual int BeamformingCalculateChannelProfile(int demod, double *resultBuf, int *resultLength);
    virtual int BeamformingCalculateChannelAmplitudeandAngleBCM(int *ValidSpatialStreams, int *DataLen, double *resultBuf, int *resultLength);

    virtual int WriteRemoteFile(const char *filename, char *buffer, unsigned int buffer_size);
    virtual int ReadRemoteFile(const char *filename, unsigned int *filesize, char *buffer, unsigned int buffer_size);
    virtual int ExecShellCmd(const char *cmd, char *buffer, unsigned int buffer_size);

    virtual int SetMoniObj(int obj);
    virtual int MoniConfig(int action, int dataType, const char *anaParamString);
    virtual int GetMoniResultSize(unsigned int *dataSize);
    virtual int GetMoniResult(char *resultBuff, unsigned int resultBuffSize, int *dataType);
    virtual int QueryMoniVSAParam(char *resultBuff, unsigned int resultBuffSize, VsaParameter *vsaParam);
    virtual int QueryMoniVSGParam(char *resultBuff, unsigned int resultBuffSize, VsgParameter *vsgParam);
    virtual int QueryMoniVSAResult(char *resultBuff, unsigned int resultBuffSize, const char *anaParamString, int signalID, int segmentID, char **dataPtr, unsigned int *elementCount, unsigned int *elementSize);
    virtual int QueryMoniVSAAnalyzeParam(char *resultBuff, unsigned int resultBuffSize, AlzParamComm *commonAnalyzeParam, void *analyzeParam, unsigned int paramSize, int *signalType);
    virtual int QueryMoniVSGPattern(char *resultBuff, unsigned int resultBuffSize, VsgPattern *vsgPattern, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount, unsigned int *actualPnItemCount, unsigned int *actualPnHeadCount);
    virtual int GetDefaultParameter(VsaParameter *vsaParam, VsaAvgParameter *avgParam, VsgParameter *vsgParam, VsgWaveParameter *waveParam, VsgPattern *vsgPattern);
    virtual int SetVSAParam(VsaParameter *vsaParam, ExtendVsaParameter *extParam);
    virtual int ClearSampRateFromFileFlag(void);
    virtual int SetVSAAverageParam(VsaAvgParameter *avgParam);
    virtual int SetAnalyzeParam(int signalType, void *analyzeParam, int paramSize);
    virtual int SetExternAnalyzeParam(int demod, int ParamType, void *param, int paramSize);
    virtual int GetVSAParam(int demodType, VsaParameter *vsaParam, ExtendVsaParameter *extvsaParam);
    virtual int GetAnalyzeParam(AlzParamComm *commonAnalyzeParam, void *analyzeParam, unsigned int paramSize, int *signalType);
    virtual int SetVSAParamAutorange(VsaParameter *vsaParam, ExtendVsaParameter *extParam);
    virtual int DataCapture();
    virtual int PauseDataCapture();
    virtual int DataCaptureAsync();
    virtual int StopDataCapture();
    virtual int GetCurrVSAStatu(int *statu);
    virtual int SetAnalyzeGroupParam(char *str);

    virtual int TesterLogSettingInfo(int type, int *log_cfg, unsigned int size);
    virtual int ClrVSAAvgData();
    virtual int SetVSATrigParam(VsaTrigParam *Param);
    virtual int GetVSATrigParam(VsaTrigParam* Param);
    virtual int GetAvgBaseCompositeResult(VsaBaseResult *result, int segmentID);

    virtual int SetVSAAverageParam(int averageType, int averageMethod, int averageCount);
    virtual int GetPnDescription(char *fileName, char *description, unsigned int size);
    virtual int LoadDataAsCapture(const char *fileName, const char *saveFileName, int acWave2);
    virtual int SaveSignal(const char *fileName,
        int saveOption,
        char *signalBuff,
        unsigned int signalBuffSize);

    virtual int SaveCurrIQDataToFile(const char *fileName, int signalID);
    virtual int SaveCurrIQDataToFile(int saveType, const char *fileName, char *calParam, unsigned int calParamSize, int signalID);
    virtual int SaveOriginalIQDataToFile(const char *fileName, char *calParam, unsigned int calParamSize, int signalID);
    virtual int SetSaveCurrIQDataCursor(int startUs, int endUs);

    virtual int MoniSaveCurrIQDataToFile(int saveType, const char *fileName, char *calParam, unsigned int calParamSize, int signalID);
    virtual int MoniSaveCurrIQDataToFile(const char *fileName, int signalID);
    virtual int MoniSaveOriginalIQDataToFile(const char *fileName);
    virtual int MoniGetIQVectorResult(int cmd, int &streamNum, int &segmentCnt, vector<int> &elementdataBytes, vector<void *>&result, IOControl *pIO);
    virtual int MoniGetIQVectorResult(
        int cmd,
        int &streamNum,
        int &segmentCnt,
        vector<int> &IQBytes,
        vector<void *>&IQresult,
        vector<int> &CalBytes,
        vector<void *>&Calresult);
    virtual int SaveVsaWaveForm(const char* fileName, int streamCnt);
    virtual int SetVsaPNStructInfo(
        stPNFileInfo *pnInfoPtr,
        int streamID,
        int segmentID,
        int segmentCnt,
        int dataBytes,
        int dataType
    );
    virtual int SetVsaPNCalParam(
        stPNFileInfo *pnInfoPtr,
        void *BB,
        void *RF,
        void *NS,
        int NsCount,
        int segmentCnt,
        bool platFlag);

    virtual int SetGeneralAnalyzeParam(AlzParamComm *commonAnalyzeParam);
    virtual int Analyze(int frameID, const char *refFileName, unsigned int timeoutMs = 15000);

    virtual int CalculateImbalance(double *imbAmp, double *imbPhase, double *timeSkew, int segmentID);
    virtual int SetFixedImbalance(int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID);
    virtual int SetVsgFixedImbalance(int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID);    
    
    virtual int GetResult(const char *anaParamString, double *result, int signalID, int segmentID);
    virtual int GetBaseResult(VsaBaseResult *result, int signalID, int segmentID);
    virtual int GetVectorResultElementSize(const char *anaParamString, unsigned int *elementSize, int signalID, int segmentID);
    virtual int GetVectorResultElementCount(const char *anaParamString, unsigned int *elementCount, int signalID, int segmentID);
    virtual int GetVectorResult(const char *anaParamString, void *result, unsigned int resultSize, int signalID, int segmentID);
    virtual int GetAverageResult(VsaAverageResult *result, VsaAverageResult *max, VsaAverageResult *min, int times, int signalID, int segmentID);
	virtual int GetSLEAverageResult(VsaSLECommResult* result, VsaSLECommResult* max, VsaSLECommResult* min, int times, int signalID, int segmentID);
    virtual int Get3GPPAverageResult(Vsa3GPPCommResult *result, Vsa3GPPCommResult *max, Vsa3GPPCommResult *min, int times, int signalID, int segmentID);
	virtual int GetBTAverageResult(VsaBTCommResult* result, VsaBTCommResult* max, VsaBTCommResult* min, int times, int signalID, int segmentID);
	virtual int GetCurrAverageCount(int *count);
    virtual int StartRecord();
    virtual int PauseRecord();
    virtual int FinishRecord();

    virtual int GetTesterAllRecordNames(char *recordNameBuffer, int recordNameBufferSize, int *recordCount);
    virtual int ReadRecord(char *recordName, char *Data, int dataLength);
    virtual int GetMonitorInfos(MonitorInfo *monitorInfo, unsigned int maxMonInfoCount, unsigned int *actualMonInfoCount);
    virtual int SetVSGParam(VsgParameter *vsgParam, ExtendVsgParameter *extParam = nullptr, double TBTDelay = 0.0);
    virtual int SetVSGWaveParam(VsgWaveParameter *vsgWaveParam);
    virtual int SetVSGPattern(VsgPattern *vsgPattern, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount);

    virtual int GetVSGParam(VsgParameter *vsgParam, VsgWaveParameter *vsgWaveParam, VsgPattern *vsgPattern, ExtendVsgParameter* extParam);
    virtual int GetVSGPattern(VsgPattern *vsgPattern, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount, unsigned int *actualPnItemCount, unsigned int *actualPnHeadCount);

    virtual int AnalyzeVSGData(const char *waveName, int demodType, AnalyzeParam *analyzeParams, int paramsSize, int timeOutMs = 15000);
    virtual int GetVSGDataResultElementSize(const char *anaParamString, unsigned int *elementSize, int signalID, int segmentID);
    virtual int GetVSGDataResultElementCount(const char *anaParamString, unsigned int *elementCount, int signalID, int segmentID);
    virtual int GetVSGDataVectorResult(const char *anaParamString, void *result, unsigned int resultSize, int signalID, int segmentID);
    virtual int GetRefLvlRange(double freq, int option, int *upperLimit, int *lowerLimit);
    virtual int GetTxPowerRange(double freq, int option, int *upperLimit, int *lowerLimit);
	virtual int GetSynSeq(int* synSeq, int *synSeqLen);
	virtual int GetSyncSource(void* pnParameters);

    virtual int StartVSG();
    virtual int AsynStartVSG();
    virtual int GetCurrVSGStatu(int *statu);
    virtual int PauseVSG();
    virtual int StopVSG();

    virtual int WaveGenerator_Common(
        int cmd,
        const char *fileName,
        vector<ExchangeBuff> &pSendList,
        void *pnParameters);
    virtual int GetDefaultWaveParameterBT(GenWaveBtStruct_API *pnParameters);
    virtual int GetDefaultWaveParameterBTV2(GenWaveBtStructV2 *pnParameters);
    virtual int GetDefaultWaveParameterCW(GenWaveCwStruct *pnParameters);
    virtual int GetDefaultWaveParameterWifi(int demod, int ppdu, GenWaveWifiStruct_API *pnParameters);
	virtual int GetDefaultWaveParameterGLE(GenWaveGleStruct* pnParameters);
    virtual int GetDefaultWaveParameter3GPP(int demod, int link, void *pnParameters, int Paramlen);
    virtual int GetDefaultWaveParameterWiSun(GenWaveWisunStruct* pnParameters);
    virtual int WaveGeneratorCatenateFiles(const MutiPNCatenateInfo *catenateInfo);
    virtual int WaveGeneratorBT(const char *fileName, GenWaveBtStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGeneratorBTV2(const char *fileName, GenWaveBtStructV2 *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGeneratorCW(const char *fileName, GenWaveCwStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGeneratorWiFi(const char *fileName, GenWaveWifiStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
	virtual int WaveGeneratorGLE(const char *fileName, GenWaveGleStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
	virtual int WaveGeneratorWiSun(const char* fileName, GenWaveWisunStruct* pnParameters);

    virtual int WaveGenerator3GPP(const char *fileName, void *pnParameters, int Paramlen);
    virtual int GenSensetivityWave(PerMacParameter *MacParameter, char *WaveName);
    virtual int GetSensetivityResult(int demod, int *AckCnt);
    virtual int StartPerTesting(PerMacParameter *MacParameter, PerActionParameter *perParameter, void(*pfunCallback)(PerResultParameter progress));
    virtual int StopPerTesting();

    virtual int DeleteTesterWaveFileOrRefFile(const char *fileName);
    virtual int AddTesterWaveFileOrRefFile(const char *fileName, const char *saveFileName, int acWave2);
    virtual int GetTesterAllWaveFileOrRefFileNames(const char *path, char *fileNameBuffer, int fileNameBuffSize, unsigned int *fileCount);
    virtual int QueryTesterWaveFileOrRefFile(const char *fileName, int *waveExists);
    virtual int GetTesterWaveFileOrRefFileSize(const char *fileName, unsigned int *fileSize);
    virtual int GetTesterWaveFileOrRefFile(const char *fileName, char *fileBuff, unsigned int fileBuffSize);
    virtual int SetCmimoRefFile(const char *fileName);

    virtual int SetPathLossFile(const char *fileName);
    virtual int GetPathLossFileSize(unsigned int *fileSize);
    virtual int GetPathLossFile(const char *fileName, unsigned int fileSize);
    virtual int GetFileSampleFreq(double *SampleFreq);


    virtual int GetHardErrorCodeSize(int *errorCodeAcount);
    virtual int GetSlaveHardErrorCodeSize(int slaveTesterID, int *errorCodeAcount);
    virtual int GetHardErrorCode(int *hardErrCode, int errorCodeAcount);
    virtual int GetSlaveHardErrorCode(int *hardErrCode, int slaveTesterID, int errorCodeAcount);
    virtual int GetHardwareTemperature(DevTemperature *temp);
    virtual int GetTemperatureHistory(int *Cnt, DevTempSave *info, unsigned int infoSize);
    virtual int GetVoltage(DeviceVoltage *voltage, int voltageCnt, int *count);
    virtual int GetFanSpeed(int *speed);
    virtual int SetFanSpeed(int speed);
    virtual int GetRxGain(int ModuleID, char *buf, unsigned int bufSize);
    virtual int GetTxGain(int ModuleID, char *buf, unsigned int bufSize);
    virtual int ReadLog(LogFilter *filter, char *filterLog, int logMaxBuff);
    virtual int GetTesterSystemTime(TIME_TYPE_API *sysTime);
    virtual int Diagnose(DiagnoseSetting setting, char *diaInfo, unsigned int infoSize);
    virtual int GetTesterErrorCode(int *errCode, char *errMsg, unsigned int errMsgSize);
    virtual int AnalyzeDiagnoseLog();
    virtual int RebootTester();
    virtual int InitCalData();
    virtual int SetComponentValue(ComponentLocation componentLocation, int componentValue);
    virtual int GetComponentValue(ComponentLocation componentLocation, char *componentValue, int bufSize, int *dataLen);
    virtual int SetMasterMode(int vsaMasterMode, int vsgMasterMode);
    virtual int SetFPGAIFG(int onff);
    virtual int GetFPGAIFG(int *onff);
    virtual string GetFwVersion() { return m_FwVersion; }
    virtual void SetFwVersion(string ver) { m_FwVersion = ver; }
    virtual void memdump(const char *filename, int id);
    virtual int PacCalData(PacDataAvg *buf, int BufCnt, int *retCnt);
    virtual int PacStartGetData(PacParameter *param, PacAttribute *attribute, void(*pfunCallback)(PacProgressParameter *progress));
    virtual int PacDumpResult(int mode);
    virtual int PacGetModeCalData(int mode, int *Cnt, PacDataInfo **data);
    virtual int PacStop();
    virtual int SetWifiFrameFilter(ResultFilter *filter);
    virtual int SetWideSpectrumEnable(int onff);
    virtual void InitPnBuf(void *pData);
    virtual int TB_Init();
    virtual int TB_Release();
    virtual int TB_Start(int timeout_ms);
    virtual int TB_Stop();
    virtual int TB_GetStatus(int *status);
    virtual int TB_SetParam(InterBindParameter *Param, int vsaTrigger = WT_TRIG_TYPE_FREE_RUN_API);
    virtual int TB_AutoRange(InterBindParameter *Param);
    virtual int PacSectionParameter(PacSectionParam *param);
    virtual int PacGetResult(PacRsData *PacData);
    virtual int AxValidCommonBit(int demod, int *commonBit, int *userInRU, int *RUCnt, int *center26RU, int *RUPos);
    virtual int BeValidCommonBit(int demod, int fullBand, int *commonBits, int *Punctured, int *userInRU, int *TotalRU, int *RUPos);
    virtual int AxTF2TB(const char *fileName, GenWaveWifiStruct_API *pnParameters);
    virtual int File2TBParam(int ParamType, void *src_param, int paramSize, AlzParamAxTriggerBase *dest, int demod = WT_DEMOD_11AX_20M);
	virtual int File2SLEParam(int ParamType, void* src_param, int paramSize, AlzParamSparkLink* dest);
    virtual int FileRefParam(int ParamType, void *src_param, int paramSize, AnalyzeParam *dest);
    virtual int SetWaveCalDataCompensate(int ON_FF);
    virtual int SetVsgIQImbCompensate(int ON_FF);
    virtual int SetVsaCalDataCompensate(int ON_FF);
    virtual int SetVsaIQImbCompensate(int ON_FF);
    virtual int GetVsgFlatnessCalCompensate(int *ON_FF);
    virtual int GetVsgIQImbCompensate(int *ON_FF);
    virtual int GetVsaFlatnessCalCompensate(int *ON_FF);
    virtual int GetVsaIQImbCompensate(int *ON_FF);
    virtual int SetVSGFemParamter(FemParameter *param);
    virtual s32 GetRUSubCarrier();
    virtual s32 SetRUSubCarreir(const char *fileName);
    virtual s32 SplitWavegeneratorExtOutData(const void *pData);
    virtual int TF_Start(int timeout_ms);
    virtual int TF_Stop();
    virtual int TF_GetStatus(int *status);
    virtual int TF_SetParam(InterBindParameter *Param, int vsaTrigger = WT_TRIG_TYPE_FREE_RUN_API);
    virtual int GetVSGSendPacketCnt(int *Cnt);
    virtual int SetBroadcastEnable(int Status);
    virtual int GetBroadcastEnable(int &Status);
    virtual int SetBroadcastDebugEnable(int Status, double Power[WT_PORT_RF8 - WT_PORT_OFF]);
    virtual int GetBroadcastRunStatus(int &Status);
    virtual int CleanTesterCustomerWave();
    virtual int QueryTesterDiskUseInfo(TesterDiskUsage *testerInfo);
    virtual int SetSubNetAutoNeg(int Enable);
    virtual int GetSubNetAutoNeg(int *ON_FF);
    virtual int SetDigtalIQParam(DigtalIQParam *param);
    virtual int SetDigtalIQTestFixture(DigtalIQTestFixture *param);
    virtual int SetDigtalIQMode(int mode);
    virtual int AckConnectInfo(char *data, int len);
    virtual int GetConnectDetail(char *buffer, int buffersize);
    virtual int StartFastAttCal(ATTCalCfg_API *config, ATTCalResult_API *result);
    virtual int StopFastAttCal();
    virtual int SetPNFileExternSettingData(void *data, int len);
    virtual int SetMaxSampleRate(double maxRate);
    virtual int SubCmdHandle(SubCmdType* SubCmd);
    virtual void UpdateVsgParam(VsgParameter *param, ExtendVsgParameter *extParam);
    virtual int CreateLowWave(int type, const char *filename, const char *savename);
    virtual int GetPnCount(const char * filename, int &RetPnCount, int *PnOrder);
    virtual int GetConnectInfo(char* Ip, int* Port);
	virtual int TestConnectStatus();
    virtual int GetDockerAppList(char *buffer, int buflen, int *AppCnt);
    virtual int SetDockerAppEnable(char *name, int onoff);
    virtual int DelDockerApp(char *name);
    virtual int SetLOMode(int mode,int ModId);
	virtual int GetLOMode(int *mode, int ModId);
    virtual int SetIQMode(int *mode, int len);
    virtual int GetIQMode(int *mode, int *len);
    virtual int GetSpectrumPointPower(double Offset, double* Power, int signalID, int segmentID);
    virtual int SetWaveGeneratorTimeout(int Time);
    virtual int GetWaveGenCBFReportField(CBFReport *ReportField);
    virtual int SetVsaNoiseCalibrationStart(int PortList[8]);
    virtual int SetVsaNoiseCalibrationStop();
    virtual int GetVsaNoiseCalibrationStatus(int &Status);
    virtual int GetVsaNoiseCalibrationPortValid(int Status[8][8], int &TesterCount);
    virtual int SetVsaExtendEVMStatus(int Status);
    virtual int SetVsaIterativeEVMStatus(int Status);
    virtual int SetVsaSncEVMStatus(int Status);
    virtual int SetVsaCcEVMStatus(int Status);
protected:
    virtual int ResetPNFileExternSettingData();
    virtual int SetVsaVsgLinkShared(int SharedType = HANDLE_SCENARIO_NORMAL);

    virtual int PacSetInstrument(double freq, PacAttribute *attribute);
    virtual int PacDataCapture(int mode, int AvgCnt, double freq);
    virtual void resetPacInstance(int mode);
    virtual void addPacInstance(int mode, PacDataInfo &data);

    void TruncatePnInfo(stPNFileInfo *tmpPnFileInfo);
    int PerDataCapture(PerActionParameter *perParameter, VsaParameter *vsaParam);
    int PerFindRefPower(VsaParameter *vsaParam, VsgPattern *vsgPattern, VsgParameter *vsgParam);
    int SisoConnect(ConnectedUnit *conUnit, int connType);
    int VerifyConnect(ConnectedUnit *conUnit, unsigned int port, const char *pcPlaintext, unsigned int plaintext_len, IOControl *pIoControl);


    virtual int GetStandardSigFileBuffer(
        const char *fileName,
        const char *actualFileName,
        char *buff,
        size_t buffSize,
        int ac8080Flag,
        size_t *actualSigFileBuffSize,
        int controlType,
        int *chainNum);
    virtual int GetStandardSigFileBuffer(
        const char *fileName,
        const char *actualFileName,
        vector<memCollector> &buffers,
        int ac8080Flag,
        int controlType,
        int *chainNum);

    int CheckPnClockRate(int clockRate);
    int GetBaseResult(VsaBaseResult *result, int signalID, int controlType, int segmentID);
    int GetVectorResult(const char *anaParamString, void *result, unsigned int resultSize, unsigned int *elementCount, unsigned int *elementSize, int controlType, int signalID, int segmentID);
    int GetVectorResultInfo(const char *anaParamString, unsigned int *elementCount, unsigned int *elementSize, int controlType, int signalID, int segmentID);
    int SetVSAParam(VsaParameter *vsaParam, ExtendVsaParameter *extParam, int controlType);
    int Analyze(int frameID, const char *refFileName, int controlType, unsigned int timeoutMs);
    virtual int AddMimoTester(int signalID, ConnectedUnit connUnit);
    virtual int AddMimoTester(int signalID, ConnectedUnit connUnit, int controlType);
    virtual int RemoveMimoTester(int signalID, int controlType);
    int GetTesterInfo();
    int GetTesterInfo(TesterInfo *info, int controlType);
    int GetLicense(LicItemInfo_API *testerLicense, int licenseMaxCount, int *licActualcount, int controlType);
    int GetLicense(LicItemInfo_API *testerLicense, int licenseMaxCount, int *licActualcount, IOControl *pIOControl);
    int GetLicense();
    int SetAnalyzeParam(int signalType, void *analyzeParam, int paramSize, int controlType);
    virtual int SetExternAnalyzeParam(int demod, int ParamType, void *param, int paramSize, int controlType);
    IOControl *GetUsableIOControler(int controlType);
    void ResleaseCurrIOControler(int controlType);
    int Exchange(int mode, int fun, ExchangeBuff *sendBuff, unsigned int sendBuffCnt, ExchangeBuff *recvBuff, unsigned int recvBuffCnt, int controlType, unsigned int sendTimeOutMs = 1000, unsigned int recvTimeOutMs = 3000, CmdHeader *cmdHead = nullptr);
    int ProExchange(int mode, int fun, ExchangeBuff *sendBuff, unsigned int sendBuffCnt, ExchangeBuff *recvBuff, unsigned int recvBuffCnt, IOControl *pIoControl, unsigned int sendTimeOutMs, unsigned int recvTimeOutMs, CmdHeader *cmdHead = nullptr);
    int ProRecvSpeciallyCmdData(int fun, ExchangeBuff *recvBuff, unsigned int recvBuffCnt, IOControl *pIoControl, unsigned int recvTimeOutMs = 3000);
    int ProGetSpeciallyCmdDataSize(int fun, unsigned int *dataSize, IOControl *pIoControl, unsigned int recvTimeOutMs = 3000);
    int ProGetSpeciallyCmdData(int fun, ExchangeBuff *recvBuff, unsigned int recvBuffCnt, IOControl *pIoControl, unsigned int recvTimeOutMs = 3000);
    int ProGetSpeciallyAckDataSize(int mode, int fun, unsigned int *dataSize, ExchangeBuff *sendBuff, unsigned int sendBuffCnt, int controlType, unsigned int sendTimeOutMs = 1000, unsigned int recvTimeOutMs = 3000, bool careResult = true);

    int ProGetSpeciallyAckDataSize_LockFree(int mode, int fun, unsigned int *dataSize, ExchangeBuff *sendBuff, unsigned int sendBuffCnt, IOControl *IO, unsigned int sendTimeOutMs = 1000, unsigned int recvTimeOutMs = 3000, bool careResult = true);
    int ProRecv_LockFree(IOControl *IO, char *recvBuff, unsigned int buffLength, unsigned int timeOutMs);

    int ProRecv(int controlType, char *recvBuff, unsigned int buffLength, unsigned int timeOutMs);
    int ProSend(int controlType, const char *sendBuff, unsigned int buffLength, unsigned int timeOutMs);
    int ProRecv_V2(int controlType, char *recvBuff, unsigned int buffLength, unsigned int timeOutMs);
    int RecvGUIDataAndSave(int controlType, unsigned int dataSize, const char *saveDir);

    virtual int ParseSignalFile(const char *fileName, const char *SaveName, int ac8080Flag, unsigned int *allocatedMemory, int controlType, int *chainNum);

    void MakeFileDirExsit(char *fileName, unsigned int fileNameSize);
    virtual int LoadDataAsCapture(const char *fileName, const char *saveFileName, int controlType, int acWave2);


    int QuerySpecTester(const char *ipAddr, TesterOverview *tersterInfo);
    int TranBuffToTesterInfo(char *buff, int buffSize, TesterOverview *info, int testerMaxCount, int *testerActulCount);
    int CorrectionInstrumentType(char *IP, int *type);

    int PnFileProcess(const char *filename,
        const char *rename,
        int wave2Flag,
        int opType,
        unique_ptr<char[]> &buffer,
        size_t *buffersize);

    int PnFileProcess(const char *filename,
        const char *rename,
        int wave2Flag,
        int opType,
        vector<memCollector> &buffers);

    int PnFileProcess(const char *filename,
        const char *rename,
        unique_ptr<char[]> &buffer,
        size_t *buffersize);

    bool CompareAnalyzeParam(int signalType, void *analyzeParam, int paramSize, int controlType);
    int ProSendWrapperData(int mode, int fun, ExchangeBuff *sendBuff, unsigned int sendBuffCnt, char *WrapperHeader, IOControl *pIoControl, unsigned int sendTimeOutMs);
    int ProcFindHeader(int mode,
        char *headptr,
        IOControl *pIoControl,
        int recvTimeOutMs,
        int *startIndex,
        int *currentPos,
        int *recvLen);
    int VerifyProAckHeader(char *sendBuf, char *recvBuf);
    int ProcExchangeHeader(int mode,
        int fun,
        char *sendBuf,
        IOControl *pIoControl,
        int recvTimeOutMs,
        int *dataLength);

    bool isSameVsaParam(VsaParameter *param);
    bool isSameVsgParam(VsgParameter *param);
    bool isSameVsaParam(ExtendVsaParameter *param);
    void BackupPnItem();
    int ProcWifiVsaSpecial();

    virtual int AckLocalConnectInfo();
    virtual int axMuPsduTimeOut(GenWaveWifiStruct_API *generatorSet);
    virtual s32 SaveTBParam(GenWaveWifiStruct_API *Pn, const char *fileName);
    virtual int GetTBVariableParameter();
    virtual int GetExtParamFromFW(int standard, void* Param);

    virtual int CreateWaveWiFi(const char *fileName, GenWaveWifiStruct_API *pnParameters);
    virtual int CreateWaveWifiTbDomainMultUser(const char *fileName, GenWaveWifiStruct_API *pnParameters);
    virtual int GetTbDomainSingleUserWave(const double PowerScale, string &TbMUMIMOUserFileName, vector<stPNFileInfo> &PnInfo, unique_ptr<stPNDat[]> Data[], int &MaxSampleCnt);
    virtual int CombineTbDomainUserWave(const char *fileName, GenWaveWifiStruct_API *pnParameters);
    virtual int CreateTbDomainMUPerUserWave(const char *fileName, GenWaveWifiStruct_API *pnParameters);
    s32 CreateWaveLocal(const char *fileName, void *pnParameters);
    s32 SaveTFParam(GenWaveWifiStruct_API *Pn, const char *fileName = "TFset.tf");
    virtual s32 SaveTBMUMIMOParam(GenWaveWifiStruct_API *pnParameters, const char *fileName);
    virtual s32 isTriggerFrame(GenWaveWifiStruct_API *pnParameters);
    virtual s32 SaveTbTfParam(GenWaveWifiStruct_API *Pn, const char *fileName);
    virtual int Save3GPPRefParam(Alg_3GPP_WaveGenType * pnParameters, const char * fileName);
    void DefaultTbAlzParam(int demod, AlzParamAxTriggerBase *alzTB);
    int PnDataParamValid(stPNFileInfo *pn, int maxStream = WT_SUB_TESTER_INDEX_MAX);
	virtual s32 SaveSleParam(GenWaveGleStruct* Pn, const char* fileName);
	virtual s32 SaveSLEParam(GenWaveGleStruct* pnParameters, const char* fileName);

protected:
    struct stTesterInfo
    {
        TesterInfo info;
        bool bInited;
    };
    enum IOCONTROL_TYPE
    {
        IOCONTROL_VSA,
        IOCONTROL_VSG,
        IOCONTROL_MISC
    };
    enum PERSTATUS_ENUM
    {
        PERSTATUS_IDLE,
        PERSTATUS_WORKING,
        PERSTATUS_CANCLEING
    };
    enum HANDLESCENARIO
    {
        HANDLE_SCENARIO_NORMAL,
        HANDLE_SCENARIO_VSA_VSG_SHARED,
        HANDLE_SCENARIO_TB_TEST = HANDLE_SCENARIO_VSA_VSG_SHARED,
        HANDLE_SCENARIO_PAC_TEST = HANDLE_SCENARIO_VSA_VSG_SHARED,
        HANDLE_SCENARIO_ALL_SHARED,
    };
    enum EXTRAL_ALZ_PARAM_ENUM
    {
        EXTRAL_ALZ_PARAM_TRIGGER_BASE,
        EXTRAL_ALZ_PARAM_TRIGGER_FRAME,
    };
    enum TESTER_RUN_MODE_TYPE
    {
        TESTER_RUN_NOMAL = 0,
        TESTER_RUN_DIGIT_IQ = 1,
    };
    int m_deviceType;
    int m_currSubTesterCount;
    int m_currTestMode;
    int m_vsgMasterMode;
    int m_vsaMasterMode;
    unique_ptr<IOControl>m_pIoControlVsa;
    unique_ptr<IOControl>m_pIoControlVsg;
    unique_ptr<IOControl>m_pIoControlMisc;
    int m_linkType;

    int m_FwRecvTimeOut;
    unique_ptr<CryptologyWT4xx> m_pCryptology;
    stPNFileInfo *m_pnInfos;

    bool m_vsgAc8080Flag;
    bool m_vsaAc8080Flag;
    bool m_vsgPnRefFlag;

    int m_VsaParamUpdate;
    int m_VsgParamUpdate;

    VsaParameter m_vsaParam;
    VsgParameter m_vsgParam;
    bool m_VsgSampRateFromFileFlag = false; //是否从信号文件解析出信号采样率,如果用户重新下发采样率，要把该标志清除 
    VsaAvgParameter m_avgParam;
    VsgWaveParameter m_VsgWaveParameter;
    ExtendVsaParameter m_vsaExternParam;
    ExtendVsgParameter m_vsgExternParam;

    vector<PnItemHead_API> m_vecPnItemHead;
    vector<ExtPnItem> m_vecPnItem;

    stTesterInfo m_masterTesterInfo;
    vector<ConnectedUnit>m_vecConnectUnit;
    vector<LicItemInfo_API> m_vecMasterLicense;
    map<string, int> m_testCount;
    vector<TesterInfo>m_TesterInfoList;

    double m_tmpSampleFreq;
    bool m_isvsa160mFlag;
    string m_WaveExtName;
    u32 m_RealRecvDataLen;
#ifndef LINUX
    volatile u32 m_VsaSockLocker;
    volatile u32 m_VsgSockLocker;
    volatile u32 m_MiscSockLocker;
#else
    atomic_flag m_VsaSockLocker = ATOMIC_FLAG_INIT;
    atomic_flag m_VsgSockLocker = ATOMIC_FLAG_INIT;
    atomic_flag m_MiscSockLocker = ATOMIC_FLAG_INIT;
#endif
    u32 m_LowWaveToken;
    struct AnalyzeParamInfo
    {
        int sigType;
        char analyzeParam[TX_BUFF_LEN];
        u32 paramSize;
        int controlType;
    };
    AnalyzeParamInfo m_AnalyzeParamInfo[IOCONTROL_VSG + 1];//0 == VSA, 1==VSG
    WT_Beamforming m_Beaforming;
    int m_LastSetVsaParamResult;
    int m_LastSetVsgParamResult;
    int m_SaveIQDataRange[2];
    VsaParameter m_VsaParameterBack;
    VsgParameter m_VsgParameterBack;
    VsgWaveParameter m_VsgWaveParameterBack;
    vector<PnItemHead_API> m_vecPnItemHeadBack;
    vector<ExtPnItem> m_vecPnItemBack;

    u32 m_VsaParamSerialNum;
    u32 m_VsgParamSerialNum;
    u32 m_VsgWaveSerialNum;
    u32 m_VsgPnSerialNum;
    string m_FwVersion;
    volatile int m_PerStataus;

    s32 m_vsgAnalyzeType;
    bool m_SpectrumWideEnable;
    bool m_WifiFilterEnable;
    int m_TesterRunMode;
    s32 m_HandleScenario;
    AxTbVariableParameter m_TbVarParam;
    AlzParam3GPP m_3GPPWaveCreateAlzParam;   // 
    int m_VsaClockRate;
    vector<PacDataInfo> m_PacDataInfo[WT_PAC_LOAD_ENUM + 1];
    volatile u32 m_PacStatus;
    int m_errcodeCount;
    int m_HardErrorCode[MAX_HARDERRCODE_SIZE];
    double m_maxSampleRate;
    std::unique_ptr<RUCarrierInfo[]>m_WaveRUSubCarrier;
    int m_RUSubCarrierCnt;
    unique_ptr<char[]>m_WaveExternSetting;
    int m_WaveExternSettingLen;
    //wave generator channel mode path loss
    double m_WaveChannelModePathLoss;
    int m_GenTimeout = 0; //ms

    MutiPNExtendInfo *m_MutiPNExInfo = nullptr;
};

#endif
