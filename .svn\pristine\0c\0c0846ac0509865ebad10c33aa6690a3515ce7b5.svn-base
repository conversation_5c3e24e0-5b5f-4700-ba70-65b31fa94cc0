//*****************************************************************************
//File: rfswitch.h
//Describe:射频板链路真值表管理
//Author：yuanyongchun
//Date: 2019.07.10
//*****************************************************************************
#ifndef _RF_SWITCH_H_
#define _RF_SWITCH_H_

#include <linux/kernel.h>
#include <linux/module.h>

#include "wtdefine.h"
#include "wtbusidefine.h"
#include "../general/devlib/ioctlcmd.h"

struct RF_ATT_T
{
    int shift_reg;       // 移位寄存器编号
    int shift_reg_mask;  // 移位寄存器中控制ATT的bit掩码
    int shift_start_bit; // 开始bit位, 用于数值移位
    bool is_reverse;     // 数值是否反转
};

extern unsigned int RfSwicthMask[BUSI_UB_TYPE_COUNT][RF_SW_CTRL_TYPE_COUNT][RF_SHIFT_REG_COUNT];

//射频板链路真值表初始化
void rf_switch_table_init(struct dev_unit *pdev);
//-------------------------------------------------
///////////////////////////////////////////////////////////////////////
// WT418VA 射频板开关
///////////////////////////////////////////////////////////////////////
void rf_rx_switch_table_init_428C(void);
void rf_rx_lo_switch_table_init_428C(void);
void rf_tx_switch_table_init_428C_VD(void);
void rf_tx_lo_switch_table_init_428C_VD(void);
void rf_att_ctrl_data_table_init_428C_VD(void);
void rf_tx_switch_table_init_418va(void);
void rf_tx_lo_switch_table_init_418va(void);
void rf_rx_switch_table_init_418va(void);
void rf_rx_lo_switch_table_init_418va(void);
void rf_att_ctrl_data_table_init_418va(void);
///////////////////////////////////////////////////////////////////////
// VA 射频板开关
///////////////////////////////////////////////////////////////////////
void rf_tx_switch_table_init_va(void);
void rf_rx_switch_table_init_va(void);
void rf_tx_lo_switch_table_init_va(void);
void rf_rx_lo_switch_table_init_va(void);
void rf_att_ctrl_data_table_init_va(void);

///////////////////////////////////////////////////////////////////////
// VB 射频板开关
///////////////////////////////////////////////////////////////////////
void rf_tx_switch_table_init_vb(void);
void rf_rx_switch_table_init_vb(void);
void rf_tx_lo_switch_table_init_vb(void);
void rf_rx_lo_switch_table_init_vb(void);
void rf_att_ctrl_data_table_init_vb(void);

void lo_tx_HMC705_table_init(void);
void lo_rx_HMC705_table_init(void);

int lo_loop_filter_table_init(int DataLength,  void  *arg, struct dev_unit *pdev);
void lo_loop_filter_table_release(void);

void lo_tx_freq_channel_table_init(void);
void lo_rx_freq_channel_table_init(void);

void lo_tx_freq_channel_table_init_VB(void);
void lo_rx_freq_channel_table_init_VB(void);

//获取TX的射频板链路真值表
int wt_GetTxSwitchTableData(struct dev_unit *pdev, struct FreqBandType FreqBandTypeTemp, unsigned int SwitchTable[3]);
//获取RX的射频板链路真值表
int wt_GetRxSwitchTableData(struct dev_unit *pdev, struct FreqBandType FreqBandTypeTemp, unsigned int SwitchTable[3]);

// 获取射频链路Lo支路上的开关真值表
int wt_GetTxLoSwitchTableData(struct FreqBandType *FreqBandTypeTemp, unsigned int SwitchTable[3]);
int wt_GetRxLoSwitchTableData(struct FreqBandType *FreqBandTypeTemp, unsigned int SwitchTable[3]);

int wt_GetLoHMC705TableData(struct dev_unit *pdev, int DivRatioN, long long* LoShift);

int wt_GetLoLoopFilterData(struct dev_unit *pdev, int LFIndex, long long *LoShift);

int wt_GetLoFreqChannelData(struct dev_unit *pdev, int LFIndex, long long *LoShift);
#endif
