/*
 * @Description: 3GPP：NR5G生成信号相关命令
 * @Autor: tyling
 * @Date: 2023-6-14 12:30:16
 */
#ifndef SCPI_3GPP_GEN_WCDMA_H_
#define SCPI_3GPP_GEN_WCDMA_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif

    // 上下行
    scpi_result_t SCPI_WCDMA_SetLinkDirect(scpi_t *context);

    //**************************************************************************************************
    // UL
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_UL_SetGenWaveMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_SetGenScramblingMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_SetGenScramblingCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHSlotFormat(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHTfci(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHTpcDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHPower(scpi_t *context);
    // DPDCH
    scpi_result_t SCPI_WCDMA_SetULDpdchState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchOverallSymbolRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingInterleaverState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchInitialization(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportTimeInterval(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportBlocks(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportBlockSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchSizeOfCRC(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchRateMatchingAttribute(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchErrorProtection(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchInterleaverState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchInitialization(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportTimeInterval(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportBlocks(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportBlockSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchSizeOfCRC(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchRateMatchingAttribute(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchErrorProtection(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchInterleaverState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchPhyChannelConfigDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchPhyChannelConfigInitialization(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULDpdchChannelPower(scpi_t *context);

    //HS-DPCCH
    scpi_result_t SCPI_WCDMA_SetULHsdpcchState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULHsdpcchStartDelay(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULHsdpcchInterTTIDist(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULHsdpcchACKPattern(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULHsdpcchCQIPattern(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULHsdpcchPower(scpi_t *context);

    //HS-DPCCH
    scpi_result_t SCPI_WCDMA_SetULEdpcchState(scpi_t *context);

    //HS-DPCCH
    scpi_result_t SCPI_WCDMA_SetULEdpdchState(scpi_t *context);

    //**************************************************************************************************
    // DL
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_SetDLCommonScramblingState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLCommonScramblingCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLCommonOCNSModeState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLCommonOCNSMode(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLPCPICHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPCPICHSymbRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPCPICHChanCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPCPICHPower(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLPSCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPSCHSymbRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPSCHPower(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLPCCPCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPCCPCHSymbRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLPCCPCHChanCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetPCCPCHDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetPCCPCHInitialization(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetPCCPCHPower(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLDPCHNum(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHSlotFormat(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHSymbolRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingInterleaver2State(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHInit(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransTimeInterval(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransBlockNum(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransBlockSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHCrcSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHRateMatchAttribute(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHErrorProtect(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHInterleaverState(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHInit(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransTimeInterval(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransBlockNum(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransBlockSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHCrcSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHRateMatchAttribute(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHErrorProtect(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHInterleaverState(scpi_t *context);

    scpi_result_t SCPI_WCDMA_SetDLDPCHDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHInit(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHTpcDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHPower(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLDPCHTimingOffset(scpi_t *context);

    //**************************************************************************************************
    // Filter
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_SetULGeneralFilterType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetULGeneralSeqLen(scpi_t *context);

    // S-SCH
    scpi_result_t SCPI_WCDMA_SetDLSSCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSSCHSymbolRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSSCHPower(scpi_t *context);

    // SCCPCH
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHSlotFormat(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHSymbolRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHChannelCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHPower(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHDataType(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHInitialization(scpi_t *context);
    scpi_result_t SCPI_WCDMA_SetDLSCCPCHTimeOffset(scpi_t *context);
#ifdef __cplusplus
}
#endif

#endif
