/*
 * scpi_internal_cmd.cpp
 *
 *  Created on: 2020-11-12
 *      Author: lifen
 */
#include <string>
#include <iostream>
#include <jsoncpp/json/json.h>

#include "scpi_cal_cmd.h"
#include "basehead.h"
#include "commonhandler.h"
#include "internal.h"
#include "tester_cal.h"
#include "../../../extlib/include/wt-calibration.h"

scpi_result_t CalibrationDataReload(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;

    iRet = WT_InitCalData(ConnID);
    IF_ERR_RETURN(iRet);

//    SCPI_ResultInt(context, iRet);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetCalibrationState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    Cal_Opt Opt;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;

    int32_t numbers[1] = {0};
    SCPI_CommandNumbers(context, numbers, 1);
    iRet = WT_GetCalData(ConnID, (char *)&Opt, sizeof(Cal_Opt));

    IF_ERR_RETURN(iRet);

    int iState = 0;
    switch (numbers[0])
    {
    case 0:
        iState = Opt.tx_cal_mode_flag;
        break;
    case 1:
        iState = Opt.rx_cal_mode_flag;
        break;
    case 2:
        iState = Opt.tx_link_gain_comp_flag;
        break;
    case 3:
        iState = Opt.rx_link_gain_comp_flag;
        break;
    case 4:
        iState = Opt.tx_temp_comp_flag;
        break;
    case 5:
        iState = Opt.rx_temp_comp_flag;
        break;
    case 6:
        iState = Opt.tx_iq_imb_flag;
        break;
    case 7:
        iState = Opt.rx_iq_imb_flag;
        break;
    case 8:
        iState = Opt.tx_spec_flat_flag;
        break;
    case 9:
        iState = Opt.rx_spec_flat_flag;
        break;
    default:
        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
        break;
    }

    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, iState);
    return SCPI_RES_OK;
}

scpi_result_t SetCalibrationState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    Cal_Opt Opt;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    int iSetValue = 0;

    iRet = WT_GetCalData(ConnID, (char *)&Opt, sizeof(Cal_Opt));

    int32_t numbers[1] = {0};

    SCPI_CommandNumbers(context, numbers, 1);
    do
    {
        if (!SCPI_ParamInt(context, &iSetValue, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        switch (numbers[0])
        {
        case 0:
            Opt.tx_cal_mode_flag = bool(iSetValue);
            break;
        case 1:
            Opt.rx_cal_mode_flag = bool(iSetValue);
            break;
        case 2:
            Opt.tx_link_gain_comp_flag = bool(iSetValue);
            break;
        case 3:
            Opt.rx_link_gain_comp_flag = bool(iSetValue);
            break;
        case 4:
            Opt.tx_temp_comp_flag = bool(iSetValue);
            break;
        case 5:
            Opt.rx_temp_comp_flag = bool(iSetValue);
            break;
        case 6:
            Opt.tx_iq_imb_flag = bool(iSetValue);
            break;
        case 7:
            Opt.rx_iq_imb_flag = bool(iSetValue);
            break;
        case 8:
            Opt.tx_spec_flat_flag = bool(iSetValue);
            break;
        case 9:
            Opt.rx_spec_flat_flag = bool(iSetValue);
            break;
        default:
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        IF_ERR_RETURN(iRet);

        iRet = WT_SetCalData(ConnID, (char *)&Opt, sizeof(Cal_Opt));

    } while (0);

    IF_ERR_RETURN(iRet);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetInCalRunMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    int iRunMode = 0;

    do
    {
        if (!SCPI_ParamInt(context, &iRunMode, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = WT_SelfCalibrationAutoRunning(ConnID, iRunMode);
        IF_ERR_RETURN(iRet);

    } while (0);

//    SCPI_ResultInt(context, iRet);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetCalibrationFlatnessMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    int iMode = 0;

    do
    {
        if (!SCPI_ParamInt(context, &iMode, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = WT_SetFlatnessCal(ConnID, iMode);
        IF_ERR_RETURN(iRet);

    } while (0);

//    SCPI_ResultInt(context, iRet);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetSelfCalStartorStop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    int value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = WT_SelfCalibration(ConnID, value);
        IF_ERR_RETURN(iRet);

    } while (0);

    //SCPI_ResultInt(context, iRet);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetSelfCalibrationStatus(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    int StatusPercent = 0;
    iRet = WT_QuerySelfCalibrationPercent(ConnID,&StatusPercent);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, StatusPercent);
    return SCPI_RES_OK;
}