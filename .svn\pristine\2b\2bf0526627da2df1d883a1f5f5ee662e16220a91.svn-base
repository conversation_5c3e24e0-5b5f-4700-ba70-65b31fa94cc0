#pragma once
#ifndef LINUX
#include <WinSock2.h>
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <iostream>
#include <fstream>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <io.h>
#include <sstream>
#include <algorithm>
#include <map>
#include <process.h>
#else
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <iostream>
#include <fstream>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <sstream>
#include <algorithm>
#include <map>
#include <math.h>
#include <sys/types.h>          /* See NOTES */
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <unistd.h>
#include <sys/time.h>
#include <future>
#endif

//通用部分
#include "TesterCommon.h"
#include "internal.h"
#include "PNDef.h"
#include "Usual.h"
#include "Logger.h"
#include "Pac.h"
#include "CryptologyWT4xx.h"
#include "IOControl.h"
#include "IOControl_TCP.h"

//Wrapper部分
#include "TesterWrapper.h"
#include "InstrumentHandle.h"
#include "WT4XXWrapper.h"
#include "TesterManager.h"

//对外部分
#include "tester.h"
#include "tester_mt.h"
#include "tester_admin.h"
#include "tester_cal.h"