#!/bin/bash

targetdir=$1
fpgadir=$(dirname $(readlink -f $0))

function read_dir()
{
	type=`echo $1 | tr 'A-Z' 'a-z'`
	path=$fpgadir/$type

	if [ ! -d "$targetdir/fpga/$type" ]; then
		mkdir $targetdir/fpga/$type
	fi

	for file in `ls $path` #注意此处这是两个反引号，表示运行系统命令
	do

		if [ -d $path/$file ] #注意此处之间一定要加上空格，否则会报错
		then
			if [ $file = $2 ] #注意此处之间一定要加上空格，否则会报错
			then
				echo "cp -r $path/$file $targetdir/fpga/$type"
				cp -r $path/$file $targetdir/fpga/$type
			fi
		fi
	done
}

function read_version()
{
	for version in `cat $fpgadir/fpgaversion.conf | grep -E "^\s*\w*$1" | grep -oP "[a-f0-9]+\.[a-f0-9]+\.[a-f0-9]+\.[a-f0-9]+"`
	do
		if [ ! -z $version ]; then 
			read_dir $1 $version
		fi
	done
}

if [ ! -d "$targetdir/fpga" ]; then
    mkdir $targetdir/fpga
else
	rm -rf $targetdir/fpga/*
fi
cp $fpgadir/fpgaversion.conf $targetdir/fpga/fpgaversion.conf

read_version BACK
read_version BASE

