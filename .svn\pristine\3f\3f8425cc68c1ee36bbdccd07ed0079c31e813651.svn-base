
#ifndef HEADER_SMS4_LCL_H
#define HEADER_SMS4_LCL_H

#ifdef __cplusplus
extern "C" {
#endif

extern u8  SBOX[256];
extern unsigned int SBOX32L[256 * 256];
extern unsigned int SBOX32H[256 * 256];


#define GET32(pc)  (					\
	((unsigned int)(pc)[0] << 24) ^			\
	((unsigned int)(pc)[1] << 16) ^			\
	((unsigned int)(pc)[2] <<  8) ^			\
	((unsigned int)(pc)[3]))

#define PUT32(st, ct)					\
	(ct)[0] = (u8)((st) >> 24);		\
	(ct)[1] = (u8)((st) >> 16);		\
	(ct)[2] = (u8)((st) >>  8);		\
	(ct)[3] = (u8)(st)

#define ROT32(x,i)					\
	(((x) << (i)) | ((x) >> (32-(i))))

#define S32(A)						\
	((SBOX[((A) >> 24)       ] << 24) ^		\
	 (SBOX[((A) >> 16) & 0xff] << 16) ^		\
	 (SBOX[((A) >>  8) & 0xff] <<  8) ^		\
	 (SBOX[((A))       & 0xff]))

#define ROUNDS(x0, x1, x2, x3, x4)		\
	ROUND(x0, x1, x2, x3, x4, 0);		\
	ROUND(x1, x2, x3, x4, x0, 1);		\
	ROUND(x2, x3, x4, x0, x1, 2);		\
	ROUND(x3, x4, x0, x1, x2, 3);		\
	ROUND(x4, x0, x1, x2, x3, 4);		\
	ROUND(x0, x1, x2, x3, x4, 5);		\
	ROUND(x1, x2, x3, x4, x0, 6);		\
	ROUND(x2, x3, x4, x0, x1, 7);		\
	ROUND(x3, x4, x0, x1, x2, 8);		\
	ROUND(x4, x0, x1, x2, x3, 9);		\
	ROUND(x0, x1, x2, x3, x4, 10);		\
	ROUND(x1, x2, x3, x4, x0, 11);		\
	ROUND(x2, x3, x4, x0, x1, 12);		\
	ROUND(x3, x4, x0, x1, x2, 13);		\
	ROUND(x4, x0, x1, x2, x3, 14);		\
	ROUND(x0, x1, x2, x3, x4, 15);		\
	ROUND(x1, x2, x3, x4, x0, 16);		\
	ROUND(x2, x3, x4, x0, x1, 17);		\
	ROUND(x3, x4, x0, x1, x2, 18);		\
	ROUND(x4, x0, x1, x2, x3, 19);		\
	ROUND(x0, x1, x2, x3, x4, 20);		\
	ROUND(x1, x2, x3, x4, x0, 21);		\
	ROUND(x2, x3, x4, x0, x1, 22);		\
	ROUND(x3, x4, x0, x1, x2, 23);		\
	ROUND(x4, x0, x1, x2, x3, 24);		\
	ROUND(x0, x1, x2, x3, x4, 25);		\
	ROUND(x1, x2, x3, x4, x0, 26);		\
	ROUND(x2, x3, x4, x0, x1, 27);		\
	ROUND(x3, x4, x0, x1, x2, 28);		\
	ROUND(x4, x0, x1, x2, x3, 29);		\
	ROUND(x0, x1, x2, x3, x4, 30);		\
	ROUND(x1, x2, x3, x4, x0, 31)

void sms4_init_sbox32(void);

#ifdef __cplusplus
}
#endif

#endif
