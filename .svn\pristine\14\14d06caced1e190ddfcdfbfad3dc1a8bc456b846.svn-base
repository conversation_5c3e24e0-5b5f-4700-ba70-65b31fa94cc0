//*****************************************************************************
//  File: business.cpp
//  业务处理
//  Data: 2016.9.1
//*****************************************************************************
#include "business.h"
#include <unistd.h>
#include <iostream>
#include <fstream>
#include <dirent.h>
#include <arpa/inet.h>
#include <string.h>
#include "wt-calibration.h"
#include "service.h"
#include "devlib.h"
#include "license.h"
#include "conf.h"
#include "monitor.h"
#include "userconnmgr.h"
#include "devcmd.h"
#include "wtlog.h"

using namespace std;

struct MimoParam
{
    int TrigDelay;  //Trigger延时，单位ns
    // TODO 待添加
};

void Business::Terminate()
{
    m_Vsa->Terminate();
    m_Vsg->Terminate();
}

int Business::ConnectMimoDev(int Chain, const char *IP, int SubId)
{
    //添加从机时，增加mimo lic的判断
    if(License::Instance().CheckBusinessLicItem(WT_WIFI_MIMO) != WT_OK)
    {
        int iRet = WT_LIC_NOT_EXIST;
        WTLog::Instance().LOGERR(iRet, "MIMO license not exist");
        return iRet;
    }

    if (m_Service)
    {
        return m_Service->AddMimoDev(Chain, IP, SubId);
    }
    else
    {
        return WT_CMD_ERROR;
    }
}

int Business::DisconnMimoDev(int Chain)
{
    if (m_Service)
    {
        return m_Service->DelMimoDev(Chain);
    }
    else
    {
        return WT_CMD_ERROR;
    }
}

void Business::AddMimoDev(std::shared_ptr<Connector> Conn)
{
    m_Vsa->AddMimoDev(Conn);
    m_Vsg->AddMimoDev(Conn);
    m_SlaveConn.push_back(std::move(Conn));
}

void Business::DelMimoDev(std::shared_ptr<Connector> Conn)
{
    m_Vsa->DelMimoDev(Conn);
    m_Vsg->DelMimoDev(Conn);
    m_SlaveConn.remove(Conn);
}

int Business::GetGUIFileVersionByType(const char *Type, GUIVersion &GUIVersionInfo)
{
    //从配置文件中获取gui文件的版本
    int Ret = WT_OK;
    string Path = WTConf::GetDir(); //获取当前目录
    string Str = "";
    char Item[56] = {0};
    
    sprintf(Item, "GUI_%s", Type);
    Ret = DevConf::Instance().GetItemVal(Item, Str);
    if (Ret == WT_OK)
    {
        STRNCPY_USER(GUIVersionInfo.LicTechName, Type);
        STRNCPY_USER(GUIVersionInfo.Version, Str.c_str());
    }
    return Ret;
}

int Business::GetGUIFileByType(const char *Type, std::vector<GUIFileInfo> &GUIFile, int &Len)
{
    string GUIFileDir = WTConf::GetDir() + string("/GUIFile/") + Type + string("/");
    queue<string> DirQueue;
    DIR *Dir;
    string FilePath = "";
    string DirName = "";
    int pos = strlen(GUIFileDir.c_str());
    int Ret = WT_OK;

    if (!(Dir = opendir(GUIFileDir.c_str())))
    {
        WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Get GUIfile Failed, not exist!");
        return WT_OPEN_FILE_FAILED;
    }
    closedir(Dir);

    DirQueue.push(GUIFileDir.c_str());
    struct dirent *Ent = nullptr;

    while(!DirQueue.empty())
    {
        DirName = DirQueue.front();
        DirQueue.pop();
        
        Dir = opendir(DirName.c_str());
        if(!Dir)
        {
            continue;
        }
        while ((Ent = readdir(Dir)))
        {
            if ((0 == strcmp(".", Ent->d_name) ) || (0 == strcmp("..", Ent->d_name) ) )
            {
                continue;
            }
            if (DT_DIR == Ent->d_type  ||   DT_LNK ==Ent->d_type )    //若为目录或一级目录软链接类型，加入到队列中
            {
                DirQueue.push(DirName + Ent->d_name + "/");
            }
            if (DT_REG == Ent->d_type )     //若为文件，则添加到vector中
            {
                GUIFileInfo FileInfo;
                FilePath = DirName.substr(pos, strlen(DirName.c_str())); //不要/tmp/wave/

                if (-1 == access((DirName + Ent->d_name).c_str(), F_OK))   //不存在该文件
                {
                    WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Cannot open GUI file!");
                    Ret = WT_OPEN_FILE_FAILED;
                    break;
                }

                std::unique_ptr<ReadFile> File;
                File.reset(new(std::nothrow) ReadFile(DirName + Ent->d_name));
                if (nullptr == File  || nullptr ==  File->GetFileBuf() )
                {
                	File.reset(nullptr);
                    WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Cannot open GUI file!");
                    Ret = WT_OPEN_FILE_FAILED;
                    break;
                }
                else
                {
                    FileInfo.FileDataLen = File->GetFileSize();
                    STRNCPY_USER(FileInfo.FileName, (FilePath + Ent->d_name).c_str());
                    FileInfo.File.reset(File.release());
                    GUIFile.push_back(move(FileInfo));
                    Len += FileInfo.FileDataLen + 256 +sizeof(int);
                    Ret = WT_OK;
                }

            }
        }
        closedir(Dir);
    }
    return Ret;
}

int Business::GetDeviceTemperatureHandler(void *Data, char *TemperBuf, int &DataLen)
{
    (void)Data;
    DevTemperature *DevTemp = static_cast<DevTemperature *>((void *)TemperBuf);
    TempLib::Instance().GetDevTemperater(*DevTemp);
    DataLen = sizeof(DevTemperature);
    return WT_OK;
}

int Business::GetHistoryTemperatureHandler(void *Data, char *TemperBuf, int &DataLen)
{
    (void)Data;
    int SaveCnt = TempLib::Instance().GetSaveAllData(TemperBuf + sizeof(int));
    *reinterpret_cast<int *>(TemperBuf) = SaveCnt;
    DataLen = sizeof(int) + SaveCnt * sizeof(DevTempSave);
    return WT_OK;
}

int Business::GetFanSpeedHandler(void *Data, int &Speed)
{
    //获取仪器风扇转速,目前处理的是单个的，后续有变再作修改
    int DevID = *(int *)Data;
    int Ret = DevLib::Instance().GetFanSpeed((WT_FAN_ID_E)DevID, Speed);
#if DEBUG
    //WTLog::Instance().WriteLog(LOG_DEBUG, "get fan Speed = %d\n", Speed);
#endif
    if(Ret == WT_OK)
    {
        if(Speed <= 0)  //获取速度不正确
        {
            Ret = WT_GET_FAN_SPEED_ERROR;
        }
    }

    return Ret;
}

int Business::SetFanSpeedHandler(void *Data)
{
    //获取data中的值，并设置风扇转速
    int DevID = *(int *)Data;
    int Speed = *(int *)((char *)Data + sizeof(int));

#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set DevID=" << DevID << ",Speed = " << Speed << endl;
#endif

    return DevLib::Instance().SetFanSpeed(DevID, Speed);
}

int Business::GetComponentParamValue(ComponentParam *Param, void *Data, int &DataLen)
{
    int Ret = WT_OK;

    //根据器件相关信息来获取器件的参数值code
    Ret = DevLib::Instance().GetUnitBoardDevData(Param->LinkID, (WT_DEV_TYPE)Param->BoardID,
                                                 Param->ComponentID, Param->ChipID, Param->Addr, Data, DataLen);
#if DEBUG
    if (Param->ComponentID != DEV_CMD_BB_LO_SHIFT)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get Component ID=" << Param->ComponentID << ", BoardID=" << Param->BoardID << ", LinkID="
             << Param->LinkID << ", ChipID=" << Param->ChipID << hex << ", RegID=0x" << Param->Addr;
        if (DataLen == sizeof(int))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << ", RegData=0x" << *(int *)Data << dec << ", RegData=" << *(int *)Data << endl;
        }
        else
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << dec << endl;
        }
    }
#endif

    return Ret;
}

//只能单个器件的设置，每次设置，设置当前器件所需要的所有的参数值
int Business::SetComponentParamValue(ComponentParam *Param, void *Data, int DataLen)
{
#if DEBUG
    if (DataLen == sizeof(int))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set component ID=" << Param->ComponentID << ", BoardID=" << Param->BoardID << ", LinkID="
             << Param->LinkID << ", ChipID=" << Param->ChipID << ", RegID=0x" << hex << Param->Addr << ", RegData=0x"
             << *(int*)(Data) << dec << ", RegData=" << *(int*)(Data) << endl;
    }
#endif
    //根据器件编号设置器件参数
    return DevLib::Instance().SetUnitBoardDevData(Param->LinkID, (WT_DEV_TYPE)Param->BoardID, \
                                                  Param->ComponentID, Param->ChipID, Param->Addr, (char *)Data, DataLen);
}

int Business::SetMimoParam(int Id, void *Param)
{
    if (Id == 0 || !IsMimo())
    {
        // TODO
        (void)Param;
    }
    else
    {
        // TODO 转发命令
    }

    return WT_OK;
}

int Business::GetLicenseInfoHandler(std::vector<LicItemInfo> &LicItemsInfo, int &DataLen)
{
    int Ret = WT_OK;

    Ret = License::Instance().GetAllLicItemsInfo(LicItemsInfo);
    if(Ret == WT_OK)
    {
        if(LicItemsInfo.empty())
        {
            WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, "Get no illegal lic from the license file!");
            Ret = WT_LIC_FILE_ILLEGAL;
        }
        else
        {
            DataLen = sizeof(LicItemInfo) * LicItemsInfo.size();
        }
    }
    return Ret;
}

int Business::GetCurSubDeviceCfgHandler(WTConf::DevCfg &Cfg)
{
    int Ret = WT_OK;
    int CurServerId = 0;

    CurServerId = DevMgr::GetSrvID();
    Ret = BaseConf::Instance().GetDevCfg(CurServerId, Cfg);

    return Ret;
}


int Business::SigFileTx(const std::string &Name, void *Data, int Len)
{
    //1.临时文件-临时文件表现为不带目录，没有 /
    if ( -1 == Name.find("/"))
    {
        std::ofstream Fs;
        Fs.open((GetLowWaveDir() + Name).c_str(), std::fstream::out | std::fstream::trunc);
        if (!Fs.is_open())
        {
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Save Signal File Failed");
            return WT_OPEN_FILE_FAILED;
        }

        Fs.write((char *)Data, Len);    //文件写入数据内容
        Fs.close();

        return WT_OK;
    }

    //2.信号文件 - 存在该路径
    int FirstDirPathPos = Name.find_first_of("/");
    string FirstDirPath = Name.substr(0, FirstDirPathPos); //获取一级目录文件名

    int Pos = Name.find_last_of("/");
    string DirPath = GetLowWaveDir() + Name.substr(0, Pos);
    struct stat FileStat;
    std::ofstream Fs;

    //存在该文件目录，则将文件放入该路径
    if ((stat(DirPath.c_str(), &FileStat) == 0) && S_ISDIR(FileStat.st_mode))
    {
        //打开文件
        Fs.open((GetLowWaveDir() + Name).c_str(), std::fstream::out | std::fstream::trunc);
        if (!Fs.is_open())
        {
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Save Signal File Failed");
            return WT_OPEN_FILE_FAILED;
        }

        Fs.write((char *)Data, Len);    //文件写入数据内容
        Fs.close();

        return WT_OK;
    }

    //3.信号文件 - 不存在该路径 不存在该目录，则先创建该目录再放入,目录需要逐级创建
    else
    {
        //如果连一级目录都没有，则要创建
        std::string low_dir("/low_wave/");
        if (-1 == access((WTConf::GetDir() + low_dir + FirstDirPath).c_str(), F_OK))
        {
            if (-1 == mkdir((WTConf::GetDir() + low_dir + FirstDirPath).c_str(), 0755))  //建立目录
            {
                WTLog::Instance().LOGERR(WT_ERROR, "Create Dir Failed!");
                return WT_ERROR;
            }
            if (-1 == symlink((WTConf::GetDir() + low_dir + FirstDirPath).c_str(), (GetLowWaveDir() + FirstDirPath).c_str())) //建立软链接
            {
                WTLog::Instance().LOGERR(WT_ERROR, "Create symbol link Failed!");
                return WT_ERROR;
            }
        }

        string LinkDir = GetLowWaveDir();
        string NeedCreatePath = Name.substr(0, Pos);
        CreatePath(LinkDir, NeedCreatePath);

        Fs.open((GetLowWaveDir() + Name).c_str(), std::fstream::out | std::fstream::trunc);
        if (!Fs.is_open())
        {
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Save Signal File Failed");
            return WT_OPEN_FILE_FAILED;
        }

        Fs.write((char *)Data, Len);    //文件写入数据内容
        Fs.close();

        return WT_OK;
    }
}

void Business::CreatePath(const std::string &LocalDir, std::string &NeedCreatePath)
{
    int iPos = 0;
    string Dir = LocalDir;
    string CurCreateDir = "";
    int Result;

    while (iPos >= 0)
    {
        iPos = NeedCreatePath.find('/');
        CurCreateDir = CurCreateDir + NeedCreatePath.substr(0, iPos);
        Dir = Dir + CurCreateDir;
        if (-1 == (Result = access(Dir.c_str(), 0)))   //该目录不存在
        {
            if (-1 == (Result = mkdir(Dir.c_str(), 0755)))     //创建目录
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Create path error!" << endl;
            }
        }
        Dir = Dir + "/";
        NeedCreatePath = NeedCreatePath.substr(iPos + 1, NeedCreatePath.size());
        CurCreateDir = "";
    }
}

int Business::SigFileExist(const std::string &Name, int &Exist)
{
    int Ret = WT_ERROR;
    string File = GetWaveDir() + Name;
    struct stat FileStat;
    int StatRet = stat(File.c_str(),&FileStat);
    if (0 == StatRet && S_ISREG(FileStat.st_mode)) //检查是否是文件
    {
        Exist = 1;
        Ret = WT_OK;
    }
    else
    {
        if (ENOENT == errno )    //不存在该文件，也需要返回不存在的标志，
        {
            Exist = 0;
            Ret = WT_OK;
        }
        else
        {
            Ret = WT_ERROR;
        }
    }
    return Ret;
}

int Business::DelSigFile(const std::string &Name)
{
    int Ret = WT_OK;

    //判断Name如果是以"Wifi/","./Wifi/","BT/","./BT/"不能操作删除， "User","./User/",不能操作删除
    if(Name.find("Wifi/") == 0 || Name.find("./Wifi/") == 0 || Name == "Wifi" || Name == "./Wifi" ||
            Name.find("BT/") == 0 || Name.find("./BT/") == 0 || Name == "BT" || Name == "./BT" ||
            Name == "User/" || Name == "./User/" || Name == "./" || Name == "User" || Name == "./User" ||
            Name == "User/8080/" || Name == "./User/8080/" || Name == "User/8080" || Name == "./User/8080" ||
            Name == "." || Name == "..")
    {
        return WT_UNAUTHORIZED_ACCESS;
    }

    string File = GetWaveDir() + Name;
    struct stat FileStat;
    int StatRet = stat(File.c_str(), &FileStat);
    if (0 == StatRet)
    {
        if(S_ISREG(FileStat.st_mode))   //如果是文件直接删除
        {
            Ret = remove(File.c_str()) == 0 ? WT_OK : WT_ERROR;
        }
        else if(S_ISDIR(FileStat.st_mode))  //如果是文件夹，一并删除其目录下的所有文件包括子目录
        {
            string cmd = string("rm -rf ") + "'" + File.c_str() + "'";
            //WTLog::Instance().WriteLog(LOG_DEBUG, "cmd = %s\n",cmd.c_str());
            Basefun::LinuxSystem(cmd.c_str());
        }
    }
    else
    {
        printf("stat(%s) errno=%d Mesg:%s\n", File.c_str(), errno, strerror(errno)); 
        Ret = WT_ERROR;
    }
    return Ret;
}

int Business::GetSigFileList(vector<string> &FileList, const std::string &Dire)
{
    queue<string> DirQueue;
    string DirName = GetWaveDir();
    bool IsFile = false;
    if ("." != Dire)
    {
        DirName += Dire;
        if("/" != DirName.substr(strlen(DirName.c_str())-1, 1)) //如果下发的路径结尾没有带斜杠，则需要补上斜杠
        {
            DirName += "/";
        }
    }

    string FilePath = "";

    DIR *Dir;
    if (!(Dir = opendir(DirName.c_str())))
    {
        WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Open Signal File Failed");
        return WT_OPEN_FILE_FAILED;
    }
    closedir(Dir);

    DirQueue.push(DirName.c_str());
    struct dirent *Ent;
    while (!DirQueue.empty())
    {
        DirName = DirQueue.front();
        DirQueue.pop();

        Dir = opendir(DirName.c_str());
        if ("./" != DirName.substr(10, strlen(DirName.c_str())))    //根目录的路径不添加到列表中
        {
            FileList.push_back(DirName.substr(10, strlen(DirName.c_str())));
        }
        if (!Dir)
        {
            continue;
        }

        while ((Ent = readdir(Dir)))
        {
            if ((0 == strcmp(".", Ent->d_name) ) || (0 == strcmp("..", Ent->d_name) ) )
            {
                continue;
            }
            IsFile = false;
            std::string DirTemp = DirName + Ent->d_name + "/";
            if (DT_DIR == Ent->d_type) //若为目录加入到队列中
            {
                DirQueue.push(DirTemp);
            }
            else if (DT_LNK == Ent->d_type) //若为软链接类型，还再需判断是目录还是文件
            {
                DIR *Dir2 = opendir(DirTemp.c_str());
                if (Dir2 != NULL)
                {
                    closedir(Dir2);
                    DirQueue.push(DirTemp);
                }
                else
                {
                    IsFile = true;
                }
            }

            if (DT_REG == Ent->d_type || IsFile == true) //若为文件，则添加到列表中
            {
                FilePath = DirName.substr(10, strlen(DirName.c_str())); //不要/tmp/wave/
                string tmppath = FilePath.substr(0,FilePath.find_last_of("/") + 1);
                for (auto iter = FileList.begin(); iter < FileList.end(); iter++)
                {
                    if (*iter == tmppath || *iter == "./8080/")//TODO 
                    {
                        FileList.erase(iter);
                        break;
                    }
                }
                FileList.push_back(FilePath + Ent->d_name);
            }
        }
        closedir(Dir);
    }
    return 0;
}

int Business::GetSigFile(const std::string &Name, void **Data, int &Len)
{
    string File = GetLowWaveDir() + Name;

    if (-1 == access(File.c_str(), F_OK))   //不存在该文件
    {
        return WT_OPEN_WAVE_FAILED;
    }

    m_SigFile.reset(new(std::nothrow) SigFile(File, SigFile::READ));
    if (nullptr == m_SigFile  || nullptr ==  m_SigFile->GetFileBuf() )
    {
        return WT_OPEN_FILE_FAILED;
    }
    else
    {
        *Data = const_cast<void*>(m_SigFile->GetFileBuf());
        Len = m_SigFile->GetFileSize();
        return WT_OK;
    }
}

int Business::SetCalData(const char *Name, void *Data, int Len)
{
    return wt_calibration_set_calibration_data((char *)Name, (char *)Data, Len);
}

int Business::GetCalData(const char *Name, void *Buf, int BufLen, int &DataLen)
{
    DataLen = wt_calibration_get_calibration_data((char *)Name, (char *)Buf, BufLen, 0);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"GetCalData  Name="<<Name<<" BufLen="<<BufLen<<"   DataLen="<<DataLen<<std::endl;
    return DataLen ? WT_OK : WT_GET_CAL_FILE_FAILED;
}

int Business::SetPathLossFile(void *Data, int Len)
{
    int Result;
    string Dir = WTConf::GetDir() + "/pathloss/";
    if (0 == Len)       //文件大小为空，则删除文件
    {
        Result = remove((Dir + "PathLossFile").c_str());
        return Result == WT_OK ? WT_OK : WT_ERROR;
    }

    if (-1 == (Result = access(Dir.c_str(),0))) //pathloss 不存在则创建
    {
        if (-1 == (Result = mkdir(Dir.c_str(), 0755)))//创建目录
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Create Path Error!" << endl;
        }
    }
    std::ofstream Fs;
    string PathLossFileName = "PathLossFile";
    Fs.open((Dir + PathLossFileName).c_str(), std::fstream::out | std::fstream::trunc);
    if (!Fs.is_open())
    {
        WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Save Signal File Failed");
        return WT_OPEN_FILE_FAILED;
    }

    Fs.write((char *)Data,Len);
    Fs.close();

    return WT_OK;
}

int Business::GetPathLossFile(void **Data, int &Len)
{
    string PathLossFileName = WTConf::GetDir()+"/pathloss/PathLossFile";
    m_SigFile.reset(new(std::nothrow) SigFile(PathLossFileName, SigFile::READ));
    
    if (nullptr == m_SigFile || nullptr == m_SigFile->GetFileBuf())
    {
        return WT_OPEN_FILE_FAILED;
    }
    else
    {
        *Data = const_cast<void*>(m_SigFile->GetFileBuf());
        Len = m_SigFile->GetFileSize();
        return WT_OK;
    }
}

int Business::DeleteSubNet(void)
{
    int Ret = WT_OK;

    string File = WTConf::GetDir().append("/eth/subnet.conf");
    Ret = remove(File.c_str()) == 0 ? WT_OK : WT_ERROR;
    if(Ret == WT_OK)
    {
        Ret = WTDeviceInfo::Instance().RereadSubNetworkInfo();
    }
    return Ret;
}

void Business::SendMeterParamtoMon(const void *MeterParam, int Len)
{
    list<shared_ptr<Monitor>> Mons;
    MonitorMgr::Instance().GetMonitors(Mons);

    for (auto &Mon : Mons)
    {
        Mon->SendMeterInfo(MeterParam, Len);
    }
}

int Business::GetLogHandler(void *Data, int DataLen, std::vector<std::string> &Record, int &VectorSize)
{
    int Ret = WT_OK;
    int QueryType = *(int *)Data;


    //根据相应的查询条件获取符合的log
    switch(QueryType)
    {
    case QueryByServerID:
    {
        int ServerID = *((int *)Data + 1);
        char DbName[128] = {0};
        struct timeval tv;
        gettimeofday(&tv, NULL);
        sprintf(DbName, "/server%d.db",ServerID);
        WTLog::Instance().Query(0, tv.tv_sec * 1000000 + tv.tv_usec, Record, WTConf::GetDir() + DbName);
        break;
    }
    case QueryByHardware:
    {
        break;
    }
    case QueryByTime:
    {
        double BeginTime = 0;
        double EndTime = 0;

        BeginTime = *(double*)((char *)Data + sizeof(int));
        EndTime = *(double*)((char *)Data + sizeof(int) + sizeof(double));

        WTLog::Instance().Query((long)BeginTime, (long)EndTime, Record, WTConf::GetDir() + "/manager.db");
        WTLog::Instance().Query((long)BeginTime, (long)EndTime, Record, WTConf::GetDir() + "/server1.db");
        break;
    }
    case QueryByOperate:
    {
        break;
    }
    case QueryAll:
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        WTLog::Instance().Query(0, tv.tv_sec * 1000000 + tv.tv_usec, Record, WTConf::GetDir() + "/manager.db");
        WTLog::Instance().Query(0, tv.tv_sec * 1000000 + tv.tv_usec, Record, WTConf::GetDir() + "/server1.db");
        break;
    }
    case QueryBySQL:
    {
        string SQLStr = "";
        SQLStr.append((char *)Data+sizeof(int), DataLen - sizeof(int));
        WTLog::Instance().WriteLog(LOG_DEBUG, "####Get SQL Str = %s##len=%d###\n",SQLStr.c_str(),DataLen);
        WTLog::Instance().Query("", SQLStr, Record);
        break;
    }
    default:
        break;
    }

    if(Record.empty())
    {
        Ret = WT_GET_LOG_ERROR;
        WTLog::Instance().LOGERR(Ret, "query log is empty.");
    }
    for (int i = 0; i < (signed)Record.size(); i++)
    {
        VectorSize += Record[i].length();
    }
    return Ret;
}

int Business::GetLogFlag(void *Data, int &Len)
{
    int Ret = WT_OK;
    int Flag = 0;
    Ret = DevConf::Instance().GetItemVal("PrintLogLevel", Flag);
    if(Ret == WT_OK)
    {
        *((int*)Data) = Flag;
    }

    Flag = 0;
    Ret = DevConf::Instance().GetItemVal("SaveLogLevel", Flag);
    if(Ret == WT_OK)
    {
        *((int*)Data + 1) = Flag;
    }

    Len = sizeof(int) * 2;
    return Ret;
}

int Business::SetLogFlag(void *Data, int Len)
{
    int Ret = WT_OK;
    int PrintLogLevel = 0;
    int SaveLogLevel = 0;

    for(int i = 0; i < Len/(sizeof(int)*2); i++)
    {
        PrintLogLevel = *((int*)Data + 2*i);
        SaveLogLevel = *((int*)Data + 1 + 2*i);
    }

    BaseConf::Instance().SetLogFlag(PrintLogLevel, SaveLogLevel);
    return Ret;
}


int Business::CustomizeFileTx(const std::string &Name, void *Data, int Len)
{
    int PathPos;
    string FilePath;
    string RelPath = Name;
    std::ofstream Fs;

    FilePath.erase();
    while ((PathPos = RelPath.find_first_of("/")) != std::string::npos)
    {
        FilePath.append(RelPath.substr(0, PathPos + 1));
        RelPath.assign(RelPath.substr(PathPos + 1));
        if (-1 == access(FilePath.c_str(), F_OK))
        {
            WTLog::Instance().LOGERR(WT_ERROR, "Create Dir Failed!");
            return WT_ERROR;
        }
    }

    FilePath.append(RelPath);

    Fs.open(FilePath.c_str(), std::fstream::out | std::fstream::trunc);
    if (!Fs.is_open())
    {
        WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Save Signal File Failed");
        return WT_OPEN_FILE_FAILED;
    }

    Fs.write((char *)Data, Len);    //文件写入数据内容
    Fs.close();

    return WT_OK;
}


int Business::GetCustomizeFile(const std::string &Name, void **Data, int &Len)
{
    if (-1 == access(Name.c_str(), F_OK))   //不存在该文件
    {
        return WT_OPEN_WAVE_FAILED;
    }

    m_ReadFile.reset(new(std::nothrow) ReadFile(Name));
    if (nullptr == m_ReadFile || nullptr == m_ReadFile->GetFileBuf())
    {
        return WT_OPEN_FILE_FAILED;
    }
    else
    {
        *Data = const_cast<void*>(m_ReadFile->GetFileBuf());
        Len = m_ReadFile->GetFileSize();
        return WT_OK;
    }
}

int Business::SendUserConnInfo(void *Data, int Len)
{
    int Ret = WT_OK;

    int Cnt = Len / sizeof(UserConnInfo);

    string StrFlag = "";

    for(int i = 0; i < Cnt; i++)
    {
        if (i == 0)
        {
            StrFlag = StrFlag + ((UserConnInfo *)((char *)Data+sizeof(UserConnInfo)*i))->IP + std::to_string(((UserConnInfo *)((char *)Data+sizeof(UserConnInfo)*i))->port);
        }
        else
        {
            StrFlag = StrFlag + std::to_string(((UserConnInfo *)((char *)Data+sizeof(UserConnInfo)*i))->port);
        }
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "StrFlag = %s\n",StrFlag.c_str());
    for(int i = 0; i < Cnt; i++)
    {
        UserConnMgr::Instance().SignSameUserFlagByIPandPort(((UserConnInfo *)((char *)Data+sizeof(UserConnInfo)*i))->IP, ((UserConnInfo *)((char *)Data+sizeof(UserConnInfo)*i))->port, (void *)StrFlag.c_str());
    }

    return Ret;
}
