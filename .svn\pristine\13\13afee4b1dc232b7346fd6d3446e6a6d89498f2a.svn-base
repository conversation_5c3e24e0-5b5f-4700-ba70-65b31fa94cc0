//*****************************************************************************
//File: devlib.cpp
//Describe:硬件操作控制层
//Author：
//Date:
//Revise:
//*****************************************************************************

#include "devlib.h"

#include <sstream>
#include <iostream>
#include <iomanip>

#include <string.h>
#include <dirent.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <sys/ioctl.h>
#include <signal.h>
#include <stdio.h>
#include <math.h>
#include <thread>

#include "devcmd.h"
#include "ioctlcmd.h"
#include "errorlib.h"
#include "conf.h"
#include "version.h"
#include "../wterror.h"
#include "wtlog.h"
#include "wtypes.h"
#include "devfactory.h"
#include "crypto.h"
#include "../device.h"
#include "../basefun.h"
#include "../ft4222lib/upgradefpga.h"
#include "../ft4222lib/epcs.h"
#include "wtxdma.h"
#include "templib.h"

#define BUSSI_INIT_WAIT_TIME  10            //初始化业务板等待时间（秒）

int DevLib::m_VSAMask = 0x0;
int DevLib::m_VSGMask = 0x0;

void DevLib::SetMask(int VSAMask, int VSGMask)
{
    m_VSAMask = VSAMask;
    m_VSGMask = VSGMask;
}

DevLib &DevLib::Instance(void)
{
    static DevLib DevLibInstance;
    return DevLibInstance;
}

int DevLib::GetHardwareVersion(int ModId, WT_DEV_TYPE DevType, int &Version)
{
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Version = m_Devs[DevType][ModId]->GetVersion();
        return WT_OK;
    }
    else
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
}

int DevLib::GetBusiFPGAVersion(int ModId, WT_DEV_TYPE DevType, int &Version)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Version = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetBusiFPGAVersion();
        return WT_OK;
    }
    return Ret;
}

DevLib::DevLib()
{
    int Ret = WT_OK;

    //初始化调试校准接口
    CmdFunctorInit();

    //设置输出格式
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setiosflags(std::ios::fixed) << std::setprecision(2);

    //打开单元板器件初始化脚本文件
    Ret = HWConfigInit();
    RetWarnning(Ret, "HWConfigInit failed!");

    DevFactory Factory(m_JsonRoot);

    m_Devs[DEV_TYPE_BACK][0] = Factory.CreateBackBoard();

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->Init();
        PortMapInit();
    }
}

DevLib::~DevLib()
{
    try
    {
        DevLibRelease();
    }
    catch (...)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevLibRelease unknow exception");
    }
}

void DevLib::VSACreate(int Mask)
{
    int Ret = WT_OK;
    int i = 0;
    DevFactory Factory(m_JsonRoot);
    while (Mask)
    {
        if (Mask & (1 << i))
        {
            DevVsa *Vsa = Factory.CreateVsaMod(i);
            if (Vsa != nullptr)
            {
                Ret |= Vsa->Init(static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0]));
            }
            m_Devs[DEV_TYPE_VSA][i] = Vsa;
            Mask &= ~(1 << i);
            VSAInitMask &= ~(1 << i);
        }
        i++;
    }
    Cond.notify_all();
    return;
}

void DevLib::VSGCreate(int Mask)
{
    int Ret = WT_OK;
    int i = 0;
    DevFactory Factory(m_JsonRoot);
    while (Mask)
    {
        if (Mask & (1 << i))
        {
            DevVsg *Vsg = Factory.CreateVsgMod(i);
            if (Vsg != nullptr)
            {
                Ret |= Vsg->Init(static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0]));
            }
            m_Devs[DEV_TYPE_VSG][i] = Vsg;
            Mask &= ~(1 << i);
            VSGInitMask &= ~(1 << i);
        }
        i++;
    }
    Cond.notify_all();
    return;
}

void *MultThreadVSACreate(void *args)
{
    int Mask = *(int *)args;
    DevLib::Instance().VSACreate(Mask);
    return NULL;
}

void *MultThreadVSGCreate(void *args)
{
    int Mask = *(int *)args;
    DevLib::Instance().VSGCreate(Mask);
    return NULL;
}


int DevLib::CreateBusiObj(void)
{
    DevFactory Factory(m_JsonRoot);
    int Ret = WT_OK;
    int ModNum = 0;
    int Mask = 0;
    int i = 0;

    ModNum = GetUnitBoardModNum(DEV_TYPE_VSA);
    for (i = 0, Mask = (1 << ModNum) - 1; Mask; i++)
    {
        if (Mask & (1 << i))
        {
            DevVsa *Vsa = Factory.CreateVsaMod(i, false);
            if (Vsa != nullptr)
            {
                Ret |= Vsa->InitForManager();
            }
            m_Devs[DEV_TYPE_VSA][i] = Vsa;
            Mask &= ~(1 << i);
        }
    }

    ModNum = GetUnitBoardModNum(DEV_TYPE_VSG);
    for (i = 0, Mask = (1 << ModNum) - 1; Mask; i++)
    {
        if (Mask & (1 << i))
        {
            DevVsg *Vsg = Factory.CreateVsgMod(i, false);
            if (Vsg != nullptr)
            {
                Ret |= Vsg->InitForManager();
            }
            m_Devs[DEV_TYPE_VSG][i] = Vsg;
            Mask &= ~(1 << i);
        }
    }
    return Ret;
}

int DevLib::DevLibInit(void)
{
    if (m_Devs[DEV_TYPE_BACK][0] == nullptr)
    {
        return WT_DEVFILE_OPEN_FAILED;
    }

    int Ret = WT_OK;
    VSAInitMask = m_VSAMask;
    VSGInitMask = m_VSGMask;

    auto WaitTime = std::chrono::milliseconds(BUSSI_INIT_WAIT_TIME * 1000);
    std::unique_lock<std::mutex> Lock(Mutex);


    DevLib::Instance().VSACreate(m_VSAMask);
    DevLib::Instance().VSGCreate(m_VSGMask);

    // pthread_t tids[BUSI_UB_TYPE_COUNT];
    // Ret = pthread_create(&tids[DEV_TYPE_VSA], NULL, MultThreadVSACreate, (void *)&m_VSAMask);
    // if (Ret != 0)
    // {
    //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA pthread_create error:error_code=" << Ret << std::endl;
    //     Ret = WT_THREAD_CREATE_FAILE;
    //     ErrorLib::Instance().AddErrCode(Ret);
    // }

    // Ret = pthread_create(&tids[DEV_TYPE_VSG], NULL, MultThreadVSGCreate, (void *)&m_VSGMask);
    // if (Ret != 0)
    // {
    //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG pthread_create error:error_code=" << Ret << std::endl;
    //     Ret = WT_THREAD_CREATE_FAILE;
    //     ErrorLib::Instance().AddErrCode(Ret);
    // }

    //等待VSA/VSG创建并初始化完成
    if (!Cond.wait_for(Lock, WaitTime, [this] { return !(VSAInitMask || VSGInitMask); }))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "=============================Bussi Init Timeout!" << std::endl;
        Ret = WT_BUSI_INIT_TIMEROUT;
        ErrorLib::Instance().AddErrCode(Ret);
        return Ret;
    }

    return Ret;
}

// step1） 软件轮询 dac 0x5号寄存器bit6是否为1，如果为1执行step2）
// step2） 将dac  0x25寄存器先写0x0，然后再写0x1
void DevLib::VsgDacTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    for(int ModId = 0; ModId< MAX_BUSINESS_NUM; ModId++)
    {
        if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
        {
            static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->CheckDacStatus();
        }
    }
}

int DevLib::VSGDacStatusMonitor(const wtev::loop_ref &loop)
{
    m_VsgDacTimerEv.set(loop);
    m_VsgDacTimerEv.set<DevLib, &DevLib::VsgDacTimerCb>(this);
    m_VsgDacTimerEv.set(1, 1);
    m_VsgDacTimerEv.start();
    return WT_OK;
}

int DevLib::CheckbackPlaneInfo(bool Cmd)
{
    int Ret = WT_OK;

    if (Cmd || (access((WTConf::GetDir() + HW_UPGRADE_TEST_CONFIG).c_str(), F_OK) == 0))
    {
#if 0
        if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
        {
            if(!Cmd)
            {
                sleep(10);
            }

            BackPlaneUnitInfo BPInfo;
            memset(&BPInfo, 0, sizeof(BackPlaneUnitInfo));
            Ret = DevLib::Instance().GetBackPlaneInfo(BPInfo);
            RetWarnning(Ret, "GetBackPlaneInfo failed!");
            std::ofstream ofs((WTConf::GetDir() + HW_UPGRADE_TEST_RESULT).c_str(), std::fstream::out | std::fstream::app);
            int CheckBpVersion = WTDeviceInfo::CheckFpgaVersionValid(BPInfo.FPGAVersion);
            int CheckHwVersion = WTDeviceInfo::CheckHwVersionValid(BPInfo.BPHWVersion);
            int CheckSwVersion = WTDeviceInfo::CheckHwVersionValid(BPInfo.SwitchHWVersion);

            ofs << "=========================" << std::endl;
            ofs << Basefun::shell_exec("date");
            ofs << "BackPlane HW Info:" << std::endl;
            ofs << "BP FPGAVersion=" << BPInfo.FPGAVersion << ", Check=" << CheckBpVersion << "\n"
                << ", BPHWVersion=" << BPInfo.BPHWVersion << ", Check=" << CheckHwVersion << "\n"
                << ", FPGADate=" << BPInfo.FPGADate << ", FPGATime=" << BPInfo.FPGATime << "\n"
                << ", ETH(SWB) Version=" << BPInfo.SwitchHWVersion << ", Check=" << CheckSwVersion << "\n";
            ofs.close();

            UpgradeFpga FpgaObj;
            Ret = FpgaObj.SelfTest(UPGRADE_FPGA_BACKPLANE);

            if ((CheckBpVersion == WT_OK) &&
                (CheckHwVersion == WT_OK) &&
                (CheckSwVersion == WT_OK) &&
                (Ret == WT_OK))
            {
                Basefun::LinuxSystem((WTConf::GetDir() + HW_UPGRADE_TEST_SHELL).c_str());
                Basefun::LinuxSystem("sync");
                usleep(1000);
                Basefun::LinuxSystem("reboot -f");
            }
        }
#else

        if (RecordHWInfo() == WT_OK)
        {
            int BusiNum = GetUnitBoardModNum(DEV_TYPE_BUSI);
#if 1
            std::thread Threads[BusiNum];
            for (int i = 0; i < BusiNum; i++)
            {
                Threads[i] = std::thread(&DevLib::ThreadBaseFpgaUpgrade, this, i);
            }

            for (int i = 0; i < BusiNum; i++)
            {
                Threads[i].join();
                if (m_BaseBurnRet[i] != WT_OK && Ret == WT_OK)
                {
                    Ret = WT_UPGRADE_VSA_FPGA_FAIL;
                }
            }
#else
            for (int i = 0; i < BusiNum; i++)
            {
                Ret = ThreadBaseFpgaUpgrade(i);
            }

            for (int i = 0; i < BusiNum; i++)
            {
                if (m_BaseBurnRet[i] != WT_OK && Ret == WT_OK)
                {
                    Ret = WT_UPGRADE_VSA_FPGA_FAIL;
                }
            }
#endif
            if (Ret == WT_OK)
            {
                Basefun::LinuxSystem((WTConf::GetDir() + HW_UPGRADE_TEST_SHELL).c_str());
                Basefun::LinuxSystem("sync");
                usleep(1000);
                Basefun::LinuxSystem("reboot -f");
            }
        }
#endif
    }
    return WT_OK;
}

void DevLib::ThreadBaseFpgaUpgrade(int ModId)
{
    m_BaseBurnRet[ModId] = BaseFpgaUpgrade(ModId, DEV_TYPE_BUSI, WTConf::GetDir() + BASE_FPGA_CMD_FILE);
}

int DevLib::RecordHWInfo(void)
{
    int Ret = WT_OK;
    std::ofstream ofs((WTConf::GetDir() + HW_RECORD_RESULT).c_str(), std::fstream::out | std::fstream::app);
    ofs << "=========================" << std::endl;
    ofs << Basefun::shell_exec("date");
    ofs << WTGetFwVersion() << std::endl;
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        BackPlaneUnitInfo BPInfo;
        memset(&BPInfo, 0, sizeof(BackPlaneUnitInfo));
        Ret = DevLib::Instance().GetBackPlaneInfo(BPInfo);
        RetWarnning(Ret, "GetBackPlaneInfo failed!");
        ofs << "BackPlane HW"
            << ", BP FPGAVersion=" << BPInfo.FPGAVersion
            << ", BPHWVersion=" << BPInfo.BPHWVersion
            << ", ETH(SWB) Version=" << BPInfo.SwitchHWVersion
            << ", FPGADate=" << BPInfo.FPGADate << ", FPGATime=" << BPInfo.FPGATime
            << std::endl;
    }

    std::vector<BusinessBoardUnitInfo> AllBusiBoardInfo;
    if ((Ret = DevLib::Instance().GetAllBusiBoardInfo(AllBusiBoardInfo)) == WT_OK && AllBusiBoardInfo.size() > 0)
    {
        if (strncmp(AllBusiBoardInfo[0].FPGAVersion, "3.1.b.1", strlen("3.1.b.1")) == 0)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "AllBusiBoardInfo[0].FPGAVersion = %s\n", AllBusiBoardInfo[0].FPGAVersion);
            Ret = WT_ARG_ERROR;
        }

        for (int i = 0; i < (signed)AllBusiBoardInfo.size(); i++)
        {
            if (m_Devs[DEV_TYPE_BUSI][i] != nullptr)
            {
                ofs << "BaseBoard HW ModId" << i
                    << ", BB FPGAVersion=" << AllBusiBoardInfo[i].FPGAVersion
                    << ", BB ModuleVersion=" << AllBusiBoardInfo[i].HWVersion
                    << ", FPGADate=" << AllBusiBoardInfo[i].FPGADate << ", FPGATime=" << AllBusiBoardInfo[i].FPGATime
                    << std::endl;
            }
        }
    }
    ofs.close();
    WTLog::Instance().WriteLog(LOG_DEBUG, "RecordHWInfo Ret = %#x\n", Ret);
    return Ret;
}

int DevLib::ShowHWInfo(void)
{
    int Ret = WT_OK;
    double TempValue;
    double TempValue2;
    double TempValue3;
    std::vector<VoltInfoType> VoltInfoVec;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\n-----------------HW Devlib Version = "
              << WTGetBuildDate() << "-----------------\n"
              << std::endl;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_DAC_GAIN_ON(ATT步进):" << (WT_DAC_GAIN_ON == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RF_LO_POWER(本振电源开关):" << (RF_LO_POWER == 0 ? "不用时关闭" : "一直打开") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_RX_BAND_ON(RX通道选择):" << (WT_RX_BAND_ON == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_RX_FILTER_ON(RX滤波器通道选择):" << (WT_RX_FILTER_ON == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_RX_SUB_BAND_ON(RX5G子波段链路选择):" << (WT_RX_SUB_BAND_ON == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_RX_FREQ_MANUAL(RX配置本振频率):" << (WT_RX_FREQ_MANUAL == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_OCXO(晶振):" << (WT_OCXO == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_FREQ_ALLOCATION(本振分频):" << (WT_FREQ_ALLOCATION == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_BOOST_MANUAL(放大器):" << (WT_BOOST_MANUAL == 0 ? "默认配置文件数据有效" : "校准数据有效") << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_ADCONVST(电压控制信号):" << (WT_ADCONVST == 1 ? "打开" : "关闭\n") << std::endl;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr && m_Devs[DEV_TYPE_BACK][0]->GetTesterType() != HW_WT418)
    {
        //获取背板及开关板电压
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetBPAllVoltValue();
        RetWarnning(Ret, "GetBPAllVoltValue failed!");
        VoltInfoVec = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetUBVoltInfo();
        for (auto Iter : VoltInfoVec)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Iter.Board << " AD7091 Channel" << Iter.VoltChannel
                        << "  " << Iter.VoltChannelInfo << " =" << std::setiosflags(std::ios::fixed)
                        << std::setprecision(2) << Iter.VoltValue * Iter.MultVolt << "V" << std::endl;
        }

        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchAllVoltValue();
        RetWarnning(Ret, "GetSwitchAllVoltValue failed!");
        VoltInfoVec = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSWBVoltInfo();
        for (auto Iter : VoltInfoVec)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Iter.Board << " AD7091 Channel" << Iter.VoltChannel
                        << "  " << Iter.VoltChannelInfo << " =" << std::setiosflags(std::ios::fixed)
                        << std::setprecision(2) << Iter.VoltValue * Iter.MultVolt << "V" << std::endl;
        }

        for (int i = WT_RF_1; i < WT_RF_MAX; i++)
        {
            if (GetSwbPortTemperater(i, TempValue) == WT_OK)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SWB Port Temperature Port[" << i << "]=" << std::setiosflags(std::ios::fixed)
                            << std::setprecision(2) << TempValue << "℃" << std::endl;
            }
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::endl;
    }

    for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
    {
        for (int i = 0; i < BUSI_UB_TYPE_COUNT; i++)
        {
            //获取本振板温度信息
            if (m_Devs[i][ModId] != nullptr && static_cast<DevBusiness *>(m_Devs[i][ModId])->GetLoExist())
            {
                TempLib::Instance().GetBusiOscillatorTemperater(i, ModId, MOD_LO_TEMP, TempValue);
                TempLib::Instance().GetBusiOscillatorTemperater(i, ModId, MIX_LO_TEMP, TempValue2);
                if (i == DEV_TYPE_VSA)
                {
                    TempLib::Instance().GetBusiOscillatorTemperater(i, ModId, DEM_TEMP, TempValue3);
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA BOARD MOD" << ModId
                              << std::setiosflags(std::ios::fixed) << std::setprecision(2)
                              << " MOD LO Temperature=" << TempValue << "℃,"
                              << " MIX LO Temperature=" << TempValue2 << "℃,"
                              << " DEM Temperature=" << TempValue3 << "℃" << std::endl;
                }
                else
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG BOARD MOD" << ModId
                              << std::setiosflags(std::ios::fixed) << std::setprecision(2)
                              << " MOD LO Temperature=" << TempValue << "℃,"
                              << " MIX LO Temperature=" << TempValue2 << "℃" << std::endl;
                }
            }
        }

        if (m_Devs[DEV_TYPE_BUSI][ModId] != nullptr)
        {
            //获取基带板电压
            Ret = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_BUSI][ModId])->GetBBAllVoltValue();
            VoltInfoVec = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_BUSI][ModId])->GetUBVoltInfo();
            for (auto &Iter : VoltInfoVec)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Iter.Board << " MOD" << ModId << " AD7091 Channel" << Iter.VoltChannel
                          << "  " << Iter.VoltChannelInfo << " =" << std::setiosflags(std::ios::fixed)
                          << std::setprecision(2) << Iter.VoltValue * Iter.MultVolt << "V" << std::endl;
            }
        }

        for (int i = 0; i < BUSI_UB_TYPE_COUNT; i++)
        {
            //获取射频板信息
            if (m_Devs[i][ModId] != nullptr && static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRfExist())
            {
                //获取射频板电压
                Ret = static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRFAllVoltValue();
                RetWarnning(Ret, "GetRFAllVoltValue failed!");
                VoltInfoVec = static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRFVoltInfo();
                for (auto &Iter : VoltInfoVec)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Iter.Board << " MOD" << ModId  << " AD7682 Channel" << Iter.VoltChannel
                              << "  " << Iter.VoltChannelInfo << " =" << std::setiosflags(std::ios::fixed)
                              << std::setprecision(2) << Iter.VoltValue * Iter.MultVolt << "V" << std::endl;
                }
            }
        }
    }

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        BackPlaneInfo BPInfo = WTDeviceInfo::Instance().GetDeviceDetailedInfo().BPInfo;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\nBackPlane HW Info:" << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BP FPGAVersion=" << BPInfo.FPGAVersion << ", BPHWVersion=" << BPInfo.BPHWVersion
                  << ", FPGADate=" << BPInfo.FPGADate
                  << ", FPGATime=" << BPInfo.FPGATime
                  << ", ETH(SWB) Version=" << BPInfo.SwitchHWVersion
                  << ", SN=" << WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN << std::endl;
    }

    BusinessBoardInfo BusiBoardInfo;
    if (WTDeviceInfo::Instance().GetDeviceDetailedInfo().BusiBoardCount > 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\nBusinessBoard HW Info:" << std::endl;
        for (int i = 0; i < WTDeviceInfo::Instance().GetDeviceDetailedInfo().BusiBoardCount; i++)
        {
            BusiBoardInfo = WTDeviceInfo::Instance().GetDeviceDetailedInfo().BusiBoardInfo[i];
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << BusiBoardInfo.Type << " FPGAVersion=" << BusiBoardInfo.FPGAVersion
                      << ", FPGADate=" << BusiBoardInfo.FPGADate
                      << ", FPGATime=" << BusiBoardInfo.FPGATime
                      << ", HWVersion=" << BusiBoardInfo.BBHWVersion
                      << ", RFHWVersion=" << BusiBoardInfo.RFHWVersion
                      << ", SN=" << BusiBoardInfo.SN << std::endl;
        }
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\nLO Info:" << std::endl;
    //本振锁定信息
    for (int i = 0; i < BUSI_UB_TYPE_COUNT; i++)
    {
        for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
        {
            if (m_Devs[i][ModId] != nullptr)
            {
                if (dynamic_cast<DevBusiness *>(m_Devs[i][ModId])->GetLoExist())
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (i == DEV_TYPE_VSA ? "VSA" : "VSG") << " ModId=" << ModId << "  ";
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix:" << (dynamic_cast<DevBusiness *>(m_Devs[i][ModId])->CheckLOIsLock(LoMix) ? "locked!   " : "unlocked!   ");
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMod:" << (dynamic_cast<DevBusiness *>(m_Devs[i][ModId])->CheckLOIsLock(LoMod) ? "locked!   " : "unlocked!   ");
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::endl;
                }
                else
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (i == DEV_TYPE_VSA ? "VSA" : "VSG") << " ModId=" << ModId << "  Lo Board not exist!" << std::endl;
                }
            }
        }
    }

    FT4222Dev::Instance().PrintAllDevice();
    return WT_OK;
}

int DevLib::HWConfigInit(void)
{
    //打开JSon文本
    WTFileSecure HwInitConf(HW_INIT_FILE_NAME);
    m_JsonIfstream.open(HwInitConf.GetDecryptName());

	Json::Reader JsonReader; //Json解析器对象
    if (!m_JsonIfstream.is_open())
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===HW Init Json file open failed!" << std::endl;
        ErrorLib::Instance().AddErrCode(WT_CONF_FILE_ERROR);
        return WT_CONF_FILE_ERROR;
    }

    //解析JSon文本
    if (!JsonReader.parse(m_JsonIfstream, m_JsonRoot))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===HW Init Json file parse failed!" << std::endl;
        ErrorLib::Instance().AddErrCode(WT_JSON_PARSE_FAILED);
        return WT_JSON_PARSE_FAILED;
    }
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===HW Init Json file parse Success!" << std::endl;
    return WT_OK;
}

int DevLib::HWConfigSave(void)
{
    Json::StyledWriter JsonWriter; // Json解析器对象

    std::ofstream fout;
    fout.open(WTConf::GetDir() + "/" HW_INIT_FILE_NAME, std::ios::out);

    if (!fout.is_open())
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===HW Init Json file open failed!" << std::endl;
        ErrorLib::Instance().AddErrCode(WT_CONF_FILE_ERROR);
        return WT_CONF_FILE_ERROR;
    }

    fout << JsonWriter.write(m_JsonRoot);
    fout.close();
    WTFileSecure::EncryptFile(HW_INIT_FILE_NAME);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===HW Init Json file Save Success!" << std::endl;
    return WT_OK;
}

int DevLib::PortMapInit(void)
{
    Json::Value PortMap;
    int TesterType = HW_WT428;
    int HwVersion = VERSION_B;
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        TesterType = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetTesterType();
        HwVersion = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetVersion();
    }
    if(TesterType == HW_WT418)
    {
        PortMap = m_JsonRoot["PortMapping_WT418_VA"];        
    }
    else if (TesterType == HW_WT428)
    {
        PortMap = m_JsonRoot["PortMapping_WT428_VA"];
    }
    else
    {
        if (HwVersion >= VERSION_B)
        {
            PortMap = m_JsonRoot["PortMapping_VB"];
        }
        else
        {
            PortMap = m_JsonRoot["PortMapping_VA"];
        }
    }

    int Port;
    int Modulate;
    if (PortMap.isArray())
    {
        for (int i = 0; i < PortMap.size(); i++)
        {
            Port = std::strtol(PortMap[i]["Port"].asString().c_str(), 0, 0);
            Modulate = std::strtol(PortMap[i]["Modulate"].asString().c_str(), 0, 0);
            m_PortMap[Port] = Modulate;
        }
    }
    return WT_OK;
}

void DevLib::DevLibRelease(void)
{
    //关闭器件初始化脚本文件
    if (m_JsonIfstream.is_open())
    {
        m_JsonIfstream.close();
    }

    for (int i = 0; i < DEV_TYPE_BACK; i++)
    {
        for (int j = 0; j < MAX_BUSINESS_NUM; j++)
        {
            if (m_Devs[i][j] != nullptr)
            {
                m_Devs[i][j]->Release();
                delete m_Devs[i][j];
            }
        }
    }
}

void DevLib::CmdSetFunctorBind(int CmdId, CmdFunctor &&Functor)
{
    m_CmdWRCallBack[CmdId].WriteFunc = Functor;
}

void DevLib::CmdGetFunctorBind(int CmdId, CmdFunctor &&Functor)
{
    m_CmdWRCallBack[CmdId].ReadFunc = Functor;
}

void DevLib::CmdFunctorInit(void)
{
    //PCIE寄存器
    SET_CMD_BIND(DEV_CMD_PCI_REG, DevLib::WriteDirectReg);
    GET_CMD_BIND(DEV_CMD_PCI_REG, DevLib::ReadDirectReg);

    //FLASH
    SET_CMD_BIND(DEV_CMD_UB_FLASH, DevLib::WriteFlashPageTest);
    GET_CMD_BIND(DEV_CMD_UB_FLASH, DevLib::ReadFlashPageTest);
    SET_CMD_BIND(DEV_CMD_UB_FLASH_SECTION, DevLib::WriteFlashSectionTest);
    GET_CMD_BIND(DEV_CMD_UB_FLASH_SECTION, DevLib::ReadFlashPageTest);

    //单元板电压温度检测模块
    GET_CMD_BIND_1_2_4(DEV_CMD_UB_VOLTAGE, DevLib::GetUBVoltValueX100);
    GET_CMD_BIND_1_2_4(DEV_CMD_UB_TEMPERATURE, DevLib::GetUBTempValueX100);

    //Hareware Version
    GET_CMD_BIND_1_2_4(DEV_CMD_UB_HARDWARE_VERSION, DevLib::GetHardwareVersion);

    GET_CMD_BIND_1_2_4(DEV_CMD_UB_REVISION_ID, DevLib::GetRevisionId);

    GET_CMD_BIND_2_4(DEV_CMD_UB_COUNT, DevLib::GetUBCount);

    GET_CMD_BIND_4(DEV_CMD_TESTER_HW_TYPE, DevLib::GetTesterHwType);
    /*-----------------------------业务基带板---------------------------*/
    //AD5611
    SET_CMD_BIND_1_3_4(DEV_CMD_BB_AD5611, DevLib::WriteAD5611);
    GET_CMD_BIND_1_3_4(DEV_CMD_BB_AD5611, DevLib::ReadAD5611);

    //AD7682
    // SET_CMD_BIND(DEV_CMD_BB_AD7682, DevLib::WriteAD7682);
    // GET_CMD_BIND(DEV_CMD_BB_AD7682, DevLib::ReadAD7682);
    GET_CMD_BIND(DEV_CMD_BB_GET_AD7682_CODE, DevLib::GetAD7682ChannelCode);
    // GET_CMD_BIND(DEV_CMD_BB_GET_AD7682_TEMP, DevLib::GetAD7682ChannelVolt);

    //AD7689
    GET_CMD_BIND(DEV_CMD_SW_GET_AD7689_CODE, DevLib::GetAD7689ChannelCode);  

    //ADF4106
    SET_CMD_BIND_1_3_4(DEV_CMD_BB_ADF4106, DevLib::WriteADF4106);
    GET_CMD_BIND_1_3_4(DEV_CMD_BB_ADF4106, DevLib::ReadADF4106);

    //HM7044
    SET_CMD_BIND_1_3_4(DEV_CMD_BB_HM7044, DevLib::WriteHM7044);
    GET_CMD_BIND_1_3_4(DEV_CMD_BB_HM7044, DevLib::ReadHM7044);

    //LTC5594
    SET_CMD_BIND_1_3_4(DEV_CMD_BB_LTC5594, DevLib::WriteLTC5594);
    GET_CMD_BIND_1_3_4(DEV_CMD_BB_LTC5594, DevLib::ReadLTC5594);

    //BB_BOARD_INFO
    GET_CMD_BIND(DEV_CMD_BB_BOARD_INFO, DevLib::ReadDirectReg);

    //LMX2594
    SET_CMD_BIND(DEV_CMD_BB_LMX2594, DevLib::WriteLMX2594);
    GET_CMD_BIND(DEV_CMD_BB_LMX2594, DevLib::ReadLMX2594);
    SET_CMD_BIND(DEV_CMD_BB_LMX2594_FREQ, DevLib::SetMixFreqPower);

    //LMXHMC833
    SET_CMD_BIND(DEV_CMD_BB_HMC833, DevLib::WriteHMC833Reg);
    GET_CMD_BIND(DEV_CMD_BB_HMC833, DevLib::ReadHMC833Reg);

    //Check Lo 
    GET_CMD_BIND(DEV_CMD_CHECK_LO_LOCK, DevLib::CheckLOIsLock);

    //单元板电压检测模块
    SET_CMD_BIND_1_3_4(DEV_CMD_BB_AD7091, DevLib::WriteBusiAD7091);
    GET_CMD_BIND_1_3_4(DEV_CMD_BB_AD7091, DevLib::ReadBusiAD7091);
    GET_CMD_BIND_1_3_4(DEV_CMD_BB_VOLTAGE, DevLib::GetBBVoltValue);

    //ADC9684 ADC模数转换器
    SET_CMD_BIND_1_3_4(DEV_CMD_RF_ADC_AD9684, DevLib::WriteADCReg);
    GET_CMD_BIND_1_3_4(DEV_CMD_RF_ADC_AD9684, DevLib::ReadADCReg);

    //AD9142 DAC数模转换器
    SET_CMD_BIND_1_3_4(DEV_CMD_RF_DAC_AD9142, DevLib::WriteDACReg);
    GET_CMD_BIND_1_3_4(DEV_CMD_RF_DAC_AD9142, DevLib::ReadDACReg);

    // 本振板DDS
    SET_CMD_BIND(DEV_CMD_BB_DDS, DevLib::SetLoDDSReg);
    GET_CMD_BIND(DEV_CMD_BB_DDS, DevLib::GetLoDDSReg);
    SET_CMD_BIND_1_2_4(DEV_CMD_LO_DDS_FREQ, DevLib::SetDDSFreq);
    SET_CMD_BIND_1_2_4(DEV_CMD_LO_DDS_CURR, DevLib::SetDDSFsCurrent);

    // 射频板移位寄存器
    SET_CMD_BIND(DEV_CMD_RF_ATT_SHIFT, DevLib::SetRfShiftReg);
    GET_CMD_BIND(DEV_CMD_RF_ATT_SHIFT, DevLib::GetRfShiftReg);

    //本振板
    SET_CMD_BIND(DEV_CMD_BB_LO_SHIFT_BIT, DevLib::WriteLoShiftBit);
    GET_CMD_BIND(DEV_CMD_BB_LO_SHIFT_BIT, DevLib::ReadLoShiftBit);
    SET_CMD_BIND_1_2_4(DEV_CMD_LO_HMC705, DevLib::SetLoHMC705);
    SET_CMD_BIND_1_2_4(DEV_CMD_LO_LOOP_FILTER, DevLib::SetLoLoopFilter);
    SET_CMD_BIND_1_2_4(DEV_CMD_LO_FREQ_CHANNEL, DevLib::SetLoFreqChannel);
    SET_CMD_BIND_1_2_4(DEV_CMD_LO_FREQ_CHANNEL_VB, DevLib::SetLoFreqChannel);

    //本振板频率
    SET_CMD_BIND(DEV_CMD_LO_MOD_FREQ, DevLib::SetModFreqPower);
    SET_CMD_BIND(DEV_CMD_LO_MOD_FREQ_HZ, DevLib::SetModFreqPowerHz);
    SET_CMD_BIND(DEV_CMD_LO_MIX_FREQ, DevLib::SetMixFreqPower);

    //本振板频率
    SET_CMD_BIND(DEV_CMD_BB_LMX2820, DevLib::WriteLMX2820);
    GET_CMD_BIND(DEV_CMD_BB_LMX2820, DevLib::ReadLMX2820);

    SET_CMD_BIND_1_2_4(DEV_CMD_MOD_REF_SEL, DevLib::SetModLoRefSel);
    GET_CMD_BIND_1_2_4(DEV_CMD_MOD_REF_SEL, DevLib::GetModLoRefSel);
    //共本振模式
    SET_CMD_BIND_1_4(DEV_CMD_LO_COM_MODE,DevLib::SetLOComMode);
    GET_CMD_BIND_1_4(DEV_CMD_LO_COM_MODE,DevLib::GetLOComMode);

    //模拟IQ信号内/外链路切换开关
    SET_CMD_BIND_1_4(DEV_CMD_ANALOG_IQ_SW,DevLib::SetAnalogIQSW);
    GET_CMD_BIND_1_4(DEV_CMD_ANALOG_IQ_SW,DevLib::GetAnalogIQSW);
    /*-----------------------------   VSA   ---------------------------*/
    //RX DC offset
    SET_CMD_BIND_1_3_4(DEV_CMD_VSA_DC_OFFSET, DevLib::SetRXDCOffset);
    /*-----------------------------   VSG   ---------------------------*/
    //TX DC offset
    SET_CMD_BIND_1_4(DEV_CMD_VSG_DC_I_OFFSET, DevLib::SetIOffset);
    SET_CMD_BIND_1_4(DEV_CMD_VSG_DC_Q_OFFSET, DevLib::SetQOffset);

    //DAC IQ可调增益
    SET_CMD_BIND_1_3_4(DEV_CMD_VSG_DAC_GAIN_IQ_CODE, DevLib::SetDacIQGainCode);

    SET_CMD_BIND_1_4(DEV_CMD_VSG_DAC_GAIN, DevLib::SetDacGainX100);

    SET_CMD_BIND_1_4(DEV_CMD_VSG_BOOST, DevLib::SetBoostStatus);
    SET_CMD_BIND_1_4(DEV_CMD_VSA_LNA, DevLib::SetVsaLNAStatus);

    SET_CMD_BIND_4(DEV_CMD_VSG_IFG_ENABLE, DevLib::VSGSetIfgCtrlEnable);
    ///*-----------------------------业务板---------------------------*/
    GET_CMD_BIND_1_2(DEV_CMD_UB_INFO, DevLib::ShowRFInfo);

    //ATT
    SET_CMD_BIND(DEV_CMD_RF_ATT, DevLib::SetATTCode);
    GET_CMD_BIND(DEV_CMD_RF_ATT, DevLib::GetATTCode);
    //波段开关设置
    SET_CMD_BIND(DEV_CMD_RF_SET_BAND, DevLib::SetBusiBand);

    //频率增益波段开关设置
    SET_CMD_BIND_1_2_4(DEV_CMD_BB_FREQENCY, DevLib::SetBusiBoardFreq);

    //开启VSA/VSG
    SET_CMD_BIND_1_2(DEV_CMD_BB_START, DevLib::BusiStart);

    //停止VSA/VSG
    SET_CMD_BIND_1_2(DEV_CMD_BB_STOP, DevLib::BusiStop);

    //VSA/VSG POWER DOWN
    SET_CMD_BIND_1_2(DEV_CMD_BB_DOWN, DevLib::BusiDown);

    // 设置RF链路状态，功能同DevLib::SetSwitchState
    SET_CMD_BIND(DEV_CMD_RF_SET_PORT_STATE, DevLib::SetRFPort);

    // 校准8318期间调整频率与功率
    SET_CMD_BIND(DEV_CMD_VSG_FREQ_OUTPUT_POWER, DevLib::SetFreqOutputPower);
    ///*---------------------------背板---------------------*/
    //晶振
    SET_CMD_BIND_3_4(DEV_CMD_BP_AD5611, DevLib::SetOCXOCode);
    GET_CMD_BIND_3_4(DEV_CMD_BP_AD5611, DevLib::GetOCXOCode);

    GET_CMD_BIND_3_4(DEV_CMD_SWB_INNER_POWER_CODE, DevLib::GetSwitchInnerPowerCode);

    SET_CMD_BIND_3_4(DEV_CMD_SWB_AD9228, DevLib::WriteSwitchAD9228);
    GET_CMD_BIND_3_4(DEV_CMD_SWB_AD9228, DevLib::ReadSwitchAD9228);
    GET_CMD_BIND_3_4(DEV_CMD_SWB_PORT_POWER_CODE, DevLib::GetSwitchPortPowerCode);

    //时钟
    SET_CMD_BIND_3_4(DEV_CMD_BP_HM7043, DevLib::WriteClockHM7043);
    GET_CMD_BIND_3_4(DEV_CMD_BP_HM7043, DevLib::ReadClockHM7043);

    //射频板版本号
    GET_CMD_BIND_3_4(DEV_CMD_BP_RFVERSION, DevLib::ReadRfVersion);

    //单元板版本信息
    GET_CMD_BIND(DEV_CMD_BP_BOARD_INFO, DevLib::ReadDirectReg);
    SET_CMD_BIND(DEV_CMD_BP_BOARD_INFO, DevLib::WriteDirectReg);
    //AT88
    SET_CMD_BIND_4(DEV_CMD_BP_AT88_INIT, DevLib::InitCryptoAT88);

    //FLASH
    SET_CMD_BIND(DEV_CMD_BP_FLASH, DevLib::WriteDirectReg);
    GET_CMD_BIND(DEV_CMD_BP_FLASH, DevLib::ReadDirectReg);

    //开关板电压检测模块
    SET_CMD_BIND_3_4(DEV_CMD_BP_AD7091, DevLib::WriteBackAD7091Reg);
    GET_CMD_BIND_3_4(DEV_CMD_BP_AD7091, DevLib::ReadBackAD7091Reg);
    GET_CMD_BIND_3_4(DEV_CMD_BP_AD7091_CHANNEL_CODE, DevLib::GetBackAd7091ChannelValue);

    //风扇
    SET_CMD_BIND_3_4(DEV_CMD_BP_FAN, DevLib::WriteFanReg);                     //写风扇寄存器
    GET_CMD_BIND_3_4(DEV_CMD_BP_FAN, DevLib::ReadFanReg);                      //读风扇寄存器
    SET_CMD_BIND_3_4(DEV_CMD_BP_FAN_SPEED, DevLib::SetFanSpeed);               //设置风扇速度

    //SWITCH
    SET_CMD_BIND_3_4(DEV_CMD_SWB_ATT_CODE, DevLib::SetSwbAttCode);
    GET_CMD_BIND_3_4(DEV_CMD_SWB_ATT_CODE, DevLib::GetSwbAttCode);

    //灯（TCA9539 LED IO扩展）
    SET_CMD_BIND_3_4(DEV_CMD_BP_LED, DevLib::WriteLedIOExtBit);
    GET_CMD_BIND_3_4(DEV_CMD_BP_LED, DevLib::ReadLedIOExtBit);
    SET_CMD_BIND_3_4(DEV_CMD_BP_TCA9539_REG, DevLib::WriteLedIOExtReg);
    GET_CMD_BIND_3_4(DEV_CMD_BP_TCA9539_REG, DevLib::ReadLedIOExtReg);

    //Pa
    SET_CMD_BIND_3_4(DEV_CMD_BP_SW_PA, DevLib::WriteSwitchPa);
    GET_CMD_BIND_3_4(DEV_CMD_BP_SW_PA, DevLib::ReadSwitchPa);

    //42553
    SET_CMD_BIND_3_4(DEV_CMD_BP_SW_42553, DevLib::WriteSwitch42553);
    GET_CMD_BIND_3_4(DEV_CMD_BP_SW_42553, DevLib::ReadSwitch42553);

    //adf4002
    SET_CMD_BIND_3_4(DEV_CMD_BP_SW_ADF4002, DevLib::WritePllAdf4002);
    GET_CMD_BIND_3_4(DEV_CMD_BP_SW_ADF4002, DevLib::ReadPllAdf4002);

    SET_CMD_BIND(DEV_CMD_BP_TRIG_DEBUG, DevLib::WriteDirectReg);
    GET_CMD_BIND(DEV_CMD_BP_TRIG_DEBUG, DevLib::ReadDirectReg);
    ///*---------------------------开关板---------------------*/
    GET_CMD_BIND_2_3_4(DEV_CMD_SWB_GET_MOD_BY_PORT, DevLib::GetModId);
    SET_CMD_BIND(DEV_CMD_SWB_SET_PORT_STATE, DevLib::SetSwitchState);
    SET_CMD_BIND_1_3_4(DEV_CMD_SWB_SHIFT_SPI_REG, DevLib::SetSwitchShiftReg);
    GET_CMD_BIND_1_3_4(DEV_CMD_SWB_SHIFT_SPI_REG, DevLib::GetSwitchShiftReg);

    ///*---------------------------测试---------------------*/
    //save xdma data
    SET_CMD_BIND_1_2_4(DEV_CMD_SAVE_DMA_DATA, DevLib::SaveDmaData);

    //XDMA WR TEST
    SET_CMD_BIND(DEV_CMD_XDMA_WRITE, DevLib::WriteXDMA);
    SET_CMD_BIND(DEV_CMD_XDMA_READ, DevLib::ReadXDMA);
    SET_CMD_BIND(DEV_CMD_XDMA_TEST, DevLib::TestXDMA);

    //stress test
    SET_CMD_BIND(DEV_CMD_UB_STRESS_TEST, DevLib::UnitBoardStressTest);
    SET_CMD_BIND(DEV_CMD_BP_STRESS_TEST, DevLib::BackPlaneStressTest);
    SET_CMD_BIND(DEV_CMD_BB_STRESS_TEST, DevLib::BusiBoardStressTest);

    //临时测试用
    GET_CMD_BIND(DEV_CMD_TEST, DevLib::GetDevApiTest);
    SET_CMD_BIND(DEV_CMD_TEST, DevLib::SetDevApiTest);

    // 校准独占标志
    SET_CMD_BIND(DEV_CMD_CAL_OCCUPY, DevLib::SetOccupyFlag);

    GET_CMD_BIND_4(DEV_CMD_DEBUG_FLAG, DevLib::GetDebugFlag);
    SET_CMD_BIND_4(DEV_CMD_DEBUG_FLAG, DevLib::SetDebugFlag);

    // 器件响应时间DEBUG配置
    SET_CMD_BIND(DEV_CMD_DEV_RESPONSE_DEBUG, DevLib::SetDevResponseDebug);
    GET_CMD_BIND(DEV_CMD_DEV_RESPONSE_DEBUG, DevLib::GetDevResponseDebug);
    //TODO:控制板
}

//单元板器件参数设置与获取
int DevLib::SetUnitBoardDevData(int ModId, WT_DEV_TYPE DevType, int DevId, int ChipId, int Addr, void *Arg, int Length)
{
    int Data;
    int Ret = WT_OK;

    if (DevId == DEV_CMD_BP_AD7091 || DevId == DEV_CMD_BP_VOLTAGE || DevId == DEV_CMD_BP_FAN ||
        DevId == DEV_CMD_SWB_AD9228 || DevId == DEV_CMD_BP_AD7091_CHANNEL_CODE || DevId == DEV_CMD_BB_LMX2820 ||
        DevId == DEV_CMD_UB_STRESS_TEST || DevId == DEV_CMD_BP_STRESS_TEST || DevId == DEV_CMD_BB_STRESS_TEST)
    {
        Addr = SET_DEVID(ChipId, Addr);
    }

    auto Iter = m_CmdWRCallBack.find(DevId);
    if (Iter == m_CmdWRCallBack.end() || nullptr == Iter->second.WriteFunc)
    {
        switch (DevId)
        {
        case DEV_CMD_BP_AT88_WR:
        {
            char WriteData[1024] = {0};
            snprintf(WriteData, sizeof(WriteData), "%#x", *(int *)Arg);
            WTLog::Instance().WriteLog(LOG_DEBUG, "DEV_CMD_BP_AT88_WR Data=%s\n", WriteData);
            Ret = WriteCryptoAT88(ChipId && 0xFF, (Addr >> 8) && 0xFFFF, Addr && 0xFF, WriteData);
            Ret = WT_OK;
            break;
        }
        case DEV_CMD_BB_LO_SHIFT:
        {
            Data = (*(int *)Arg);
            long long WData = (((long long)Addr & 0xFFFFFFFF) << 8) + (Data & 0xFF);
            Ret = WriteLoShift(ModId, DevType, WData);
            break;
        }
        case DEV_CMD_FPGA_UPGRADE:
        {
            m_VsgDacTimerEv.stop();
            WTLog::Instance().WriteLog(LOG_DEBUG, "DEV_CMD_FPGA_UPGRADE DevType=%d, active=%d\n", DevType, *(int *)Arg);
            if (DevType == DEV_TYPE_BACK)
            {
                UpgradeFpga FpgaObj(WTConf::GetDir() + BACK_FPGA_CMD_FILE);
                FpgaObj.BrunFpgaData(UPGRADE_FPGA_BACKPLANE, *(int *)Arg);
            }
            else
            {
                // UpgradeFpga FpgaObj(WTConf::GetDir() + BASE_FPGA_CMD_FILE);
                // FpgaObj.BrunFpgaData(UPGRADE_FPGA_BASEBOARD, *(int *)Arg);

                BaseFpgaUpgrade(ModId, DevType, WTConf::GetDir() + BASE_FPGA_CMD_FILE, *(int *)Arg);
            }
            m_VsgDacTimerEv.start();
            Ret = WT_OK;
            break;
        }
        case DEV_CMD_READ_BACK_FPGA_DATA:
        {
            m_VsgDacTimerEv.stop();
            UpgradeFpga FpgaObj;
            if (DevType == DEV_TYPE_BACK)
            {
                FpgaObj.ReadFpgaData(UPGRADE_FPGA_BACKPLANE, WTConf::GetDir() + BACK_FPGA_READ_BACK_CMD_FILE);
            }
            else
            {
                //FpgaObj.ReadFpgaData(UPGRADE_FPGA_BASEBOARD, WTConf::GetDir() + BASE_FPGA_READ_BACK_CMD_FILE);
            }
            Ret = WT_OK;
            m_VsgDacTimerEv.start();
            break;
        }
        case DEV_CMD_FPGA_UPGRADE_SELF_TEST:
        {
            m_VsgDacTimerEv.stop();
            UpgradeFpga FpgaObj;
            if (DevType == DEV_TYPE_BACK)
            {
                FpgaObj.SelfTest(UPGRADE_FPGA_BACKPLANE);
            }
            else
            {
                //FpgaObj.SelfTest(UPGRADE_FPGA_BASEBOARD);
            }
            Ret = WT_OK;
            m_VsgDacTimerEv.start();
            break;
        }
        case DEV_CMD_CHECK_BOARD_INFO:
        {
            CheckbackPlaneInfo(true);
            Ret = WT_OK;
            break;
        }
        case DEV_CMD_VSG_BROADCAST_PORT_POWER:
        {
            m_BroadcastPortDebug = true;
            if (Addr >= WT_RF_OFF && Addr < WT_RF_MAX)
            {
                m_BroadcastPortPower[Addr] = ((double)(*(int *)Arg))/100.0;
            }
            Ret = WT_OK;
            break;
        }
        default:
            WTLog::Instance().LOGERR(WT_DEVICE_ID_ERROR, "Device ID error. Or device don't allow write operation!");
            return WT_DEVICE_ID_ERROR;
        }
    }
    else
    {
        if (nullptr == Iter->second.WriteFunc)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "Device don't allow write operation!!");
            return WT_ARG_ERROR;
        }

        if (Length == sizeof(int))
        {
            Data = (*(int *)Arg);
            Ret = Iter->second.WriteFunc(ModId, DevType, Addr, Data);
        }
    }
    return Ret;
}

int DevLib::GetUnitBoardDevData(int ModId, WT_DEV_TYPE DevType, int DevId, int ChipId, int Addr, void *Arg, int &Length)
{
    int Ret = WT_OK;
    int Data = 0;
    Length = 0;

    if (DevId == DEV_CMD_BP_AD7091 || DevId == DEV_CMD_BP_VOLTAGE || DevId == DEV_CMD_BP_FAN ||
        DevId == DEV_CMD_SWB_AD9228 || DevId == DEV_CMD_BP_AD7091_CHANNEL_CODE || DevId == DEV_CMD_BB_LMX2820 ||
        DevId == DEV_CMD_UB_STRESS_TEST || DevId == DEV_CMD_BP_STRESS_TEST || DevId == DEV_CMD_BB_STRESS_TEST)
    {
        Addr = SET_DEVID(ChipId, Addr);
    }

    auto Iter = m_CmdWRCallBack.find(DevId);
    if (Iter == m_CmdWRCallBack.end() || nullptr == Iter->second.ReadFunc)
    {
        switch (DevId)
        {
        case DEV_CMD_BP_AT88_WR:
        {
            char ReadData[8 * 32] = {0};
            Ret = ReadCryptoAT88(ChipId && 0xFF, (Addr >> 8) && 0xFFFF, Addr && 0xFF, ReadData);
            Length = 0;
            for (int i = 0; i < (Addr && 0xFF); i++)
            {
                Length += snprintf((char *)Arg + Length, sizeof(ReadData) - Length, "0x%#x,", ReadData[i]);
            }
            ((char *)Arg)[Length] = 0;
            break;
        }
        case DEV_CMD_ENCRYPT_SN:
            Ret = CryptoLib::Instance().GetCryptoMemSN(ModId, DevType, (char *)Arg);
            Length = strlen((char *)Arg);
            break;
        case DEV_CMD_ENCRYPT_CODE:
            Ret = CryptoLib::Instance().GetCryptoMemCode(ModId, DevType, (char *)Arg);
            Length = strlen((char *)Arg);
            break;
        case DEV_CMD_BB_LO_SHIFT:
        {
            long long RBData;
            Ret = ReadLoShift(ModId, DevType, RBData);
            sprintf((char *)Arg, "LoShift=%#llx", RBData);
            Length = strlen((char *)Arg);
            WTLog::Instance().WriteLog(LOG_DEBUG, "===ReadLoShift DevType%d ModId%d Ret=%#x RBData=%#llx, ReS=%s, Length=%d\n",
                   DevType, ModId, Ret, RBData, (char *)Arg, Length);
            break;
        }
        case DEV_CMD_BP_FAN_SPEED:
        {
            int Pwm = 0;
            int Sp = 0;
            Ret = GetFanSpeedAndPwm(Addr, Pwm, Sp);
            *((int *)Arg) = Pwm;
            *((int *)Arg + 1) = Sp;
            Length = sizeof(int) * 2;
            break;
        }
        case DEV_CMD_BP_VOLTAGE:
        {
            double Volt;
            Ret = DevLib::GetBackChannelVoltValue(Addr, Volt);
            (*(double *)Arg) = Volt;
            Length = sizeof(double);
            break;
        }
        case DEV_CMD_SWB_PORT_TEMP:
        {
            double Temp;
            Ret = DevLib::GetSwbPortTemperater(Addr, Temp);
            (*(double *)Arg) = Temp;
            Length = sizeof(double);
            break;
        }
        case DEV_CMD_BB_AD7682_TEMPERATURE:
        {
            double Temp;
            Ret = DevLib::GetAD7682ChannelTemperature(ModId, DevType, Addr, Temp);
            (*(double *)Arg) = Temp;
            Length = sizeof(double);
            break;
        }
        case DEV_CMD_SW_GET_AD7689_VOLT:
        {
            double Temp;
            Ret = DevLib::GetAD7689ChannelVolt(ModId, DevType, Addr, Temp);
            (*(double *)Arg) = Temp;
            Length = sizeof(double);
            break;
        }        
        case DEV_CMD_UB_STRESS_TEST:
        case DEV_CMD_BP_STRESS_TEST:
        case DEV_CMD_BB_STRESS_TEST:
        {
            Ret = DevLib::GetStressTestStatus(Arg, Length);
            break;
        }
        case DEV_CMD_VSG_BROADCAST_PORT_POWER:
        {
            m_BroadcastPortDebug = true;
            if (Addr >= WT_RF_OFF && Addr < WT_RF_MAX)
            {
                *(double*)Arg = m_BroadcastPortPower[Addr];
            }
            Length = sizeof(double);
            Ret = WT_OK;
            break;
        }
        default:
            WTLog::Instance().LOGERR(WT_DEVICE_ID_ERROR, "Device ID error. Or device don't allow read operation!");
            return WT_DEVICE_ID_ERROR;
        }
    }
    else
    {
        if (nullptr == Iter->second.ReadFunc)
        {
            WTLog::Instance().LOGERR(WT_DEVICE_ID_ERROR, "Device don't allow read operation!!");
            return WT_DEVICE_ID_ERROR;
        }

        Ret = Iter->second.ReadFunc(ModId, DevType, Addr, Data);
        (*(int *)Arg) = Data;
        Length = sizeof(int);
    }
    return Ret;
}

int DevLib::GetDevVoltCnt(int &VoltCnt, int &Length)
{
    VoltCnt = 0;
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        //获取背板电压
        VoltCnt += static_cast<DevBase *>(m_Devs[DEV_TYPE_BACK][0])->GetUBVoltInfo().size();
        //获取开关板电压
        VoltCnt += static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSWBVoltInfo().size();
    }

    for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
    {
        if (m_Devs[DEV_TYPE_BUSI][ModId] != nullptr)
        {
            //获取基带板电压
            VoltCnt += static_cast<DevBase *>(m_Devs[DEV_TYPE_BUSI][ModId])->GetUBVoltInfo().size();
            //获取射频板信息
            for (int i = 0; i < BUSI_UB_TYPE_COUNT; i++)
            {
                if (static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRfExist())
                {
                //获取射频板电压
                    VoltCnt += static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRFVoltInfo().size();
            }
        }
    }
    }
    Length = sizeof(VoltInfoType) * VoltCnt;
    return WT_OK;
}

int DevLib::GetDevVoltInfo(void *Data, char *VoltBuf, int &VoltCnt, int &Length)
{
    int Ret = WT_OK;
    (void)Data;
    VoltCnt = 0;
    VoltInfoType *VoltAddr = reinterpret_cast<VoltInfoType *>(VoltBuf);
    std::vector<VoltInfoType> VoltInfoVec;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetBPAllVoltValue();
        RetWarnning(Ret, "GetBPAllVoltValue failed!");
        //添加背板电压信息
        VoltInfoVec = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetUBVoltInfo();
        for (int i = 0; i < VoltInfoVec.size(); VoltCnt++, i++)
        {
            memcpy(VoltAddr + VoltCnt, &VoltInfoVec[i], sizeof(VoltInfoType));
        }

        //添加开关板电压信息
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchAllVoltValue();
        RetWarnning(Ret, "GetSwitchAllVoltValue failed!");
        VoltInfoVec = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSWBVoltInfo();
        for (int i = 0; i < VoltInfoVec.size(); VoltCnt++, i++)
        {
            memcpy(VoltAddr + VoltCnt, &VoltInfoVec[i], sizeof(VoltInfoType));
        }
    }

    for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
    {
        if (m_Devs[DEV_TYPE_BUSI][ModId] != nullptr)
        {
            Ret = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_BUSI][ModId])->GetBBAllVoltValue();
            RetWarnning(Ret, "GetBBAllVoltValue failed!");
            //获取基带板电压
            VoltInfoVec = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_BUSI][ModId])->GetUBVoltInfo();
            for (int i = 0; i < VoltInfoVec.size(); VoltCnt++, i++)
            {
                strcat(VoltInfoVec[i].VoltChannelInfo, (std::string(" MOD") + std::to_string(ModId)).c_str());
                memcpy(VoltAddr + VoltCnt, &VoltInfoVec[i], sizeof(VoltInfoType));
            }

            //获取射频板信息
            for (int i = 0; i < BUSI_UB_TYPE_COUNT; i++)
            {
                if (static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRfExist())
                {
                //获取射频板电压
                    Ret = static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRFAllVoltValue();
                RetWarnning(Ret, "GetRFAllVoltValue failed!");
                    VoltInfoVec = static_cast<DevBusiness *>(m_Devs[i][ModId])->GetRFVoltInfo();
                for (int i = 0; i < VoltInfoVec.size(); VoltCnt++, i++)
                {
                        strcat(VoltInfoVec[i].VoltChannelInfo, (std::string(" MOD") + std::to_string(ModId)).c_str());
                    memcpy(VoltAddr + VoltCnt, &VoltInfoVec[i], sizeof(VoltInfoType));
                }
            }
        }
    }
    }
    Length = sizeof(VoltInfoType) * VoltCnt;
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetDevVoltCnt = %d\n",VoltCnt);
    return WT_OK;
}


//===========UNIT BOARD==============
int DevLib::GetUnitBoardModNum(WT_DEV_TYPE DevType)
{
    int i = 0;
    DIR *Dirptr = NULL;
    struct dirent *Entry;
    std::string DevName = (DevType == DEV_TYPE_VSG ? "wtvsg" : (DevType == DEV_TYPE_VSA ? "wtvsa" : "wtback"));

    if ((Dirptr = opendir("/dev")) == NULL)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "GetUnitBoardModNum opendir failed!");
        return 0;
    }
    else
    {
        Entry = readdir(Dirptr);
        while (Entry)
        {
            if (strncmp(Entry->d_name, DevName.c_str(), 5) == 0)
            {
                i++;
            }
            Entry = readdir(Dirptr);
        }
        closedir(Dirptr);
    }

    return i;
}

int DevLib::GetRevisionId(int ModId, WT_DEV_TYPE DevType, int &RevisionId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = m_Devs[DevType][ModId]->GetRevisionId(RevisionId);
    }
    return Ret;
}

int DevLib::GetUBCount(WT_DEV_TYPE DevType, int &Count)
{
    Count = GetUnitBoardModNum(DevType);
    return WT_OK;
}

int DevLib::GetUBVoltValue(int ModId, WT_DEV_TYPE DevType, double &TempValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = m_Devs[DevType][ModId]->GetUBTempValue(TempValue);
    }

    return Ret;
}

int DevLib::GetUBTempValue(int ModId, WT_DEV_TYPE DevType, double &TempValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = m_Devs[DevType][ModId]->GetUBTempValue(TempValue);
    }

    return Ret;
}

//===========BUSI BOARD=============
int DevLib::ShowRFInfo(int ModId, WT_DEV_TYPE DevType)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ShowRFInfo();
    }

    return Ret;
}

int DevLib::GetAllBusiBoardInfo(std::vector<BusinessBoardUnitInfo> &AllBusiBoardInfo)
{
    UnitFPGAInfo FPGAInfo;
    BusinessBoardUnitInfo BBInfo;
    int Slot = 0;
    int Ret = WT_OK;
    int fd;
    int cmd;
    std::ostringstream Stream;
    std::string Filename;

    int Type = DEV_TYPE_VSA;
    for (int i = 0; i < MAX_BUSINESS_NUM; i++)
    {
        Filename = (Type == DEV_TYPE_VSA ? WTVSAFILEPATH : WTVSGFILEPATH);
        Stream.str("");
        Stream << Filename << i;
        if (access(Stream.str().c_str(), F_OK) != 0)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << " access vsa board" << i << " faild" << std::endl;
            continue;
        }

        memset(&BBInfo, 0, sizeof(BusinessBoardUnitInfo));
        memset(&FPGAInfo, 0, sizeof(UnitFPGAInfo));

        //打开设备文件
        fd = open(Stream.str().c_str(), O_RDWR | O_CLOEXEC);
        //获取单元板FPGA硬件信息
        cmd = IOCTL_CMD(GET_UNIT_BOARD_FPGA_INFO, sizeof(UnitFPGAInfo));
        if ((Ret = ioctl(fd, cmd, &FPGAInfo)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "GetAllBusiBoardInfo GetUBFPGAInfo ioctl error");
            close(fd);
            return Ret;
        }
        else
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get base unit board fpga info success" << std::endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Version = " << FPGAInfo.FPGAVersion << std::endl;
        }

        if ((Ret = ioctl(fd, IOCTL_CMD(GET_BUSI_BOARD_SLOT, sizeof(int)), &Slot)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "GetAllBusiBoardInfo GET_BUSI_BOARD_SLOT ioctl error");
            close(fd);
            return Ret;
        }
        else
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Slot = " << Slot << std::endl;
        }
        close(fd);

        sprintf(BBInfo.FPGAVersion, "%x.%x.%x.%x",
                (FPGAInfo.FPGAVersion >> 24) & 0xFF,
                (FPGAInfo.FPGAVersion >> 16) & 0xFF,
                (FPGAInfo.FPGAVersion >> 8) & 0xFF,
                (FPGAInfo.FPGAVersion) & 0xFF); //单元板FPGA逻辑版本号

        sprintf(BBInfo.FPGADate, "%04x-%02x-%02x",
                (FPGAInfo.FPGADate >> 16) & 0xFFFF,
                (FPGAInfo.FPGADate >> 8) & 0xFF,
                FPGAInfo.FPGADate & 0xFF); //单元板FPGA编译日期(年月日)

        sprintf(BBInfo.FPGATime, "%02x:%02x",
                (FPGAInfo.FPGATime >> 8) & 0xFF, FPGAInfo.FPGATime & 0xFF); //单元板FPGA编译时间(时分)

        if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
        {
            BusiBoardHwInfo HwInfo = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetBusiHwVersion(Slot);

            sprintf(BBInfo.HWVersion, "%x.%x.%x.%x",
                    0, 0, 0, HwInfo.BBHwVersion & 0x7); //单元板硬件模块版本

            sprintf(BBInfo.RFHWVersion, "%x.%x.%x.%x",
                    0, 0, 0, HwInfo.RfHwVersion & 0x7); //射频板硬件模块版本
        }

        sprintf(BBInfo.Type, "%s", "BUSI"); //单元板类型
        strcpy(BBInfo.RemarkInfo, "");
        AllBusiBoardInfo.push_back(BBInfo);
    }
    return WT_OK;
}

int DevLib::GetRFTemperatureAverage(int ModId, WT_DEV_TYPE DevType, double &TempAverage)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetRFTemperatureAverage(TempAverage);
    }
    return Ret;
}

int DevLib::GetCompleteClrStatus(int ModId, WT_DEV_TYPE DevType, int &Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetCompleteClrStatus(Status);
    }

    return Ret;
}

int DevLib::GetRFTemperature(int ModId, WT_DEV_TYPE DevType, int TempId, double &TempValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetRFTemperature(TempId, TempValue);
    }
    return Ret;
}

int DevLib::ClearWorkMode(int ModId, WT_DEV_TYPE DevType)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ClearWorkMode();
    }

    return Ret;
}

int DevLib::SetRFPort(int ModId, WT_DEV_TYPE DevType, int Port, int State)
{
    // CMD封装
    // State 高16bit是siso与8080
    // State 低16bit是PI/PA等状态
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr && m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetRFPort(Port, ((State&0xFF0000) >> 16), ((State&0xFF)),
        static_cast<WT_SB_CONFIG_TYPE_E>(static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSBType()));
    }

    return Ret;
}

int DevLib::GetRfHwVersion(int ModId, WT_DEV_TYPE DevType, int &Version)
{
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Version = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetRfHwVersion();
        return WT_OK;
    }
    else
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
}
//============VSA BOARD=============
int DevLib::VSASetConfig(int ModId, const VSAConfigType &VSAConfig, Rx_Parm &RXParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetParam(VSAConfig, RXParm);
    }

    return Ret;
}

int DevLib::VSASetConfigList(int ModId, std::vector<VSAConfigType> &VSAConfigList, std::vector<Rx_Parm> &RXParmList, std::vector<SeqTimeType> &SeqTime)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetParamList(VSAConfigList, RXParmList, SeqTime);
    }

    return Ret;
}

int DevLib::VSASetWorkPointConfig(int ModId, const VSAConfigType &VSAConfig, Rx_Parm &RXParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa*>(m_Devs[DEV_TYPE_VSA][ModId])->SetWorkPointParam(VSAConfig, RXParm);
    }

    return Ret;
}

int DevLib::VSAStart(int ModId, int Mode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->Start(Mode);
    }

    return Ret;
}

int DevLib::VSAStartList(int ModId, int Mode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->StartList(Mode);
    }

    return Ret;
}

int DevLib::VSAStop(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->Stop();
    }

    return Ret;
}

int DevLib::VSAFinish(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->Finish();
    }

    return Ret;
}

#ifdef WT418_FW
int DevLib::VSASetCaptureOriDataSample(int ModId, VSAConfigType &VsaConfig)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetCaptureOriDataSample(VsaConfig);
    }

    return Ret;
}

int DevLib::VSACaptureOriData(int ModId, void *pBuf, int Size, int ExtraSmpOffset)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->CaptureOriData(pBuf, Size, ExtraSmpOffset);
    }

    return Ret;
}
#endif

int DevLib::VSACaptureData(int ModId, void *pBuf, int Size, int ListModeOffset)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->CaptureData(pBuf, Size, ListModeOffset);
    }

    return Ret;
}

int DevLib::VSASetCalConfig(int ModId, const Rx_Parm &RXParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetCalConfig(RXParm);
    }

    return Ret;
}

int DevLib::VSADown(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->Down();
    }

    return Ret;
}

int DevLib::VSAGetStatus(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->GetStatus();
    }
    return Ret;
}

int DevLib::VSAGetGainParameter(int ModId, Rx_Gain_Parm &GainParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->GetGainParam(GainParm);
    }
    return Ret;
}

int DevLib::VSASetCalibrationFlat(int Enable)
{
    for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
    {
        if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
        {
            static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetCalibrationFlat(Enable);
        }
    }
    return WT_OK;
}

int DevLib::TBTApModeStart(int ModId, int VsgUnitSel)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa*>(m_Devs[DEV_TYPE_VSA][ModId])->TBTApModeStart(VsgUnitSel);
    }
    return Ret;
}

int DevLib::VSASetTBTStaParam(int ModId, int Delay)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa*>(m_Devs[DEV_TYPE_VSA][ModId])->SetTBTStaParam(Delay);
    }
    return Ret;
}

int DevLib::VSAGetIQCode(int ModId, int &GainCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa*>(m_Devs[DEV_TYPE_VSA][ModId])->VSAGetIQCode(GainCode);
    }
    return Ret;
}

int DevLib::VSAGetResultSIFS(int ModId, std::vector<double> &SIFS)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa*>(m_Devs[DEV_TYPE_VSA][ModId])->GetResultSIFS(SIFS);
    }
    return Ret;
}

int DevLib::VSAGetFpgaCapturePower(int ModId, double &Power)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (ModId >= 0 && ModId < MAX_BUSINESS_NUM)
    {
        if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
        {
            Power = -999.0;
            int PowerMWe6 = 0;     // 毫瓦值
            Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->GetFpgaCapturePower(PowerMWe6);
            if (Ret == WT_OK)
            {
                if (PowerMWe6 > 0)
                {
                    Power = 10 * log10(PowerMWe6 / 1000000.0);
                }
            }
        }
    }
    return Ret;
}

//==============VSG BOARD=============
int DevLib::VSGSetConfig(int ModId, const VSGConfigType &VSGConfig, Tx_Parm &TXParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetParam(VSGConfig, TXParm);
    }

    return Ret;
}

int DevLib::VSGSetConfigList(int ModId, std::vector<VSGConfigType> VSGConfigList, std::vector<Tx_Parm> TXParmList, std::vector<SeqTimeType> SeqTime, std::vector<int> SyncParam, int Repet)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetParamList(VSGConfigList, TXParmList, SeqTime, SyncParam, Repet);
    }

    return Ret;
}

int DevLib::VSGSetWorkPointConfig(int ModId, const VSGConfigType &VSGConfig, Tx_Parm &TXParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg*>(m_Devs[DEV_TYPE_VSG][ModId])->SetWorkPointParam(VSGConfig, TXParm);
    }

    return Ret;
}

int DevLib::VSGStart(int ModId, int Mode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->Start(Mode);
    }

    return Ret;
}

int DevLib::VSGStartList(int ModId, int Mode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->StartList(Mode);
    }

    return Ret;
}

int DevLib::VSGStop(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->Stop();
    }

    return Ret;
}

int DevLib::VSGDown(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->Down();
    }

    return Ret;
}

int DevLib::VSGFinish(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->Finish();
    }

    return Ret;
}

int DevLib::VSGSetCalConfig(int ModId, const Tx_Parm &TXParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetCalConfig(TXParm);
    }

    return Ret;
}

int DevLib::VSGGetStatus(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->GetStatus();
    }

    return Ret;
}

int DevLib::VSGSetPNItem(int ModId, const std::vector<RfPnItem> &PnItemVector, double ResamppleFreq)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetPNItem(PnItemVector, ResamppleFreq);
    }

    return Ret;
}

int DevLib::VSGSetPNItemList(int ModId, const std::vector<RfPnItem> &PnItemVector, const std::vector<double> ResamppleFreq, std::vector<SegRfPnItem> SegRfPnItemVector)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetPNItemList(PnItemVector, ResamppleFreq, SegRfPnItemVector);
    }

    return Ret;
}

int DevLib::VSGGetGainParameter(int ModId, Tx_Gain_Parm &GainParm)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->GetGainParam(GainParm);
    }
    return Ret;
}

int DevLib::VSGSetIfgCtrlEnable(int Enable)
{
    int Ret = WT_OK;
    for (int i = 0; i < MAX_BUSINESS_NUM; i++)
    {
        if (m_Devs[DEV_TYPE_VSG][i] != nullptr)
        {
            Ret |= static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][i])->SetIfgCtrlEnable(Enable);
        }
    }
    return Ret;
}

int DevLib::VSGTBTtaStart(int ModId, int DevMode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->TBTStaStart(DevMode);
    }

    return Ret;
}

int DevLib::VSGSetTBTStaParam(int ModId, TBTStaParamType StaConfig)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetTBTStaParam(StaConfig);
    }
    return Ret;
}


//===========BACK PLANE============
int DevLib::GetDevClkState(int &ClkState)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetDevClkState(ClkState);
    }
    return Ret;
}

int DevLib::SetErrorLed(int Status, int ErrorCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetErrorLed(Status, ErrorCode);
    }
    return Ret;
}

int DevLib::SetPortLedOff()
{
    int Ret = WT_OK;
    for (int i = LED_RF1; i <= LED_RF8; i++)
    {
        Ret |= SetLedStatus((WT_LED_INDEX_E)i, LED_STATUS_OFF);
    }
    return Ret;
}

int DevLib::GetBackPlaneInfo(BackPlaneUnitInfo &BPInfo)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetBoardInfo(BPInfo);
    }
    return Ret;
}

int DevLib::WTCryptoMemVerify(CryptoMemInfoType &CMInfo)
{
    int Ret = WT_OK;
    std::string SNStr = "";
    std::ostringstream CMInfoSNStr;
#if CRYPTO_MEM_DEBUG
    BaseConf::Instance().GetItemVal("SN", SNStr);

    CMInfoSNStr << CMInfo.DevType << "-" << CMInfo.InnerSN;
    if (CMInfoSNStr.str().length() != SNStr.length())
    {
        return WT_ENCRYPT_ILLEGAL;
    }
    else
    {
        Ret = strcmp(CMInfoSNStr.str().c_str(), SNStr.c_str());
        return Ret ? WT_ENCRYPT_ILLEGAL : WT_OK;
    }
#else
    int cmd = IOCTL_CMD(WT_CRYPTO_MEM_VERIFY, sizeof(CMInfo));
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        if ((Ret = ioctl(m_Fd[DEV_TYPE_BACK][0], cmd, &CMInfo)) != WT_OK)
        {

            WTLog::Instance().LOGERR(Ret, "WTCryptoMemVerify ioctl error");
            return Ret;
        }
    }
    return WT_OK;
#endif
}

int DevLib::SetLedStatus(WT_LED_INDEX_E LedId, WT_LED_STATUS Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetLedStatus(LedId, Status);
    }
    return Ret;
}

int DevLib::SetFanSpeed(int FanId, const int Speed)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        if (FanId == 0)
        {
            Ret = WT_OK;
            //ID为0时，设置所有风扇的转速
            for (int i = 1; i <= 6; i++)
            {
                Ret |= static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetFanSpeed(i, Speed);
            }
        }
        else
        {
            Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetFanSpeed(FanId, Speed);
        }
    }
    return Ret;
}

int DevLib::GetFanSpeed(int FanId, int &Speed)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        int PwmPer = 0;
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetFanSpeed(FanId, PwmPer, Speed);
    }
    return Ret;
}

int DevLib::GetFanSpeedAndPwm(int FanId, int &PwmPer, int &Speed)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetFanSpeed(FanId, PwmPer, Speed);
    }
    return Ret;
}

//开关板温度传感器AD7091
int DevLib::GetSwbPortTemperater(int Port, double &TempValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwbPortTemperater(Port, TempValue);
    }
    return Ret;
}

int DevLib::GetSBType(int &SBType)
{
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        SBType = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSBType();
        return WT_OK;
    }
    return WT_UNITBOARD_NOT_EXIST;
}

int DevLib::GetSwbHwVersion(int &Version)
{
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Version = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwbHwVersion();
        return WT_OK;
    }
    else
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
}

int DevLib::GetTesterHwType(int &TesterType)
{
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        TesterType = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetTesterType();
    }
    else
    {
        int Type = WTDeviceInfo::Instance().GetDevType();
        if (Type == MODEL_TYPE_WT328CE)
        {
            TesterType = HW_WT418;
        }
        else if (Type == MODEL_TYPE_WT448)
        {
            TesterType = HW_WT448;
        }
        else
        {
            TesterType = HW_WT428;
        }
    }
    return WT_OK;
}

//根据模块类型及端口获取要分配的单元号
int DevLib::GetModId(WT_DEV_TYPE DevType, int RFPort, int &ModId)
{
    (void)DevType;
    auto Iter = m_PortMap.find(RFPort);
    if (Iter != m_PortMap.end())
    {
        ModId = m_PortMap[RFPort];
    }
    else
    {
        ModId = 0;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetModId Port = %d, ModId = %d \n", RFPort, ModId);
    return WT_OK;
}

int DevLib::GetSwitchId(int RFPort, int &SwitchId)
{
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        SwitchId = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchId(RFPort);
    }
    else
    {
        SwitchId = SWTICH_PART_A;
    }
    return WT_OK;
}

int DevLib::IsSwbExist(int SwitchId, int &Exist)
{
    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Exist = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->IsSwbExist(SwitchId);
        return WT_OK;
    }
    return WT_UNITBOARD_NOT_EXIST;
}

//=============================CMD 控制接口============================
/*--------------单元板(背板/VSA、VSG业务板)公共接口------------------------*/
int DevLib::SetUnitBoardPciDelay(int ModId, WT_DEV_TYPE DevType, int WriteDelay, int ReadDelay)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBase *>(m_Devs[DevType][ModId])->SetUnitBoardPciDelay(WriteDelay, ReadDelay);
    }
    return Ret;
}

int DevLib::WriteDirectReg(int ModId, WT_DEV_TYPE DevType, int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBase *>(m_Devs[DevType][ModId])->WriteDirectReg(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadDirectReg(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBase *>(m_Devs[DevType][ModId])->ReadDirectReg(Addr, Data);
    }
    return Ret;
}

int DevLib::GetUBVoltValueX100(int ModId, WT_DEV_TYPE DevType, int &VoltValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    double VoltTemp;
    Ret = GetUBVoltValue(ModId, DevType, VoltTemp);
    VoltValue = VoltTemp * 100;
    return Ret;
}

int DevLib::GetUBTempValueX100(int ModId, WT_DEV_TYPE DevType, int &TempValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    double Value;
    Ret = GetUBTempValue(ModId, DevType, Value);
    TempValue = Value * 100;
    return Ret;
    return WT_OK;
}

int DevLib::GetCryptoMemInfoCmd(int ModId, WT_DEV_TYPE DevType)
{
    return CryptoLib::Instance().GetCryptoMemInfoCmd(ModId, DevType);
}

//Flash
int DevLib::WriteFlashSectionTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data)
{
    int Ret = WT_OK;
    int RomChip = 0;
    int i = 0;
    int Buf[FLASH_PAGE_SIZE / 4];
    for (int Page = 0; Page <= FLASH_PAGE_SIZE; Page++)
    {
        for (i = 0; i < FLASH_PAGE_SIZE / 4; i++)
        {
            Buf[i] = i + Data + Page * (FLASH_PAGE_SIZE / 4);
        }
        if (DevType == DEV_TYPE_BACK)
        {
            RomChip = ModId ? SWITCH_2_FLASH : SWITCH_1_FLASH;
        }
        Ret = WriteRomPage(0, DevType, Addr + Page * FLASH_PAGE_SIZE, Buf, RomChip);
    }
    return Ret;
}

int DevLib::WriteFlashPageTest(int ModId, WT_DEV_TYPE DevType, int Addr, int Data)
{
    int RomChip = 0;
    int Buf[FLASH_PAGE_SIZE / 4];
    for (int i = 0; i < FLASH_PAGE_SIZE / 4; i++)
    {
        Buf[i] = i + Data;
    }
    if (DevType == DEV_TYPE_BACK)
    {
        RomChip = ModId ? SWITCH_2_FLASH : SWITCH_1_FLASH;
    }
    return WriteRomPage(0, DevType, Addr, Buf, RomChip);
}

int DevLib::ReadFlashPageTest(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data)
{
    (void)Data;
    int Ret = WT_OK;
    int Buf[FLASH_PAGE_SIZE / 4];
    int RomChip = 0;

    memset(Buf, 0, sizeof(Buf));
    if (DevType == DEV_TYPE_BACK)
    {
        RomChip = ModId ? SWITCH_2_FLASH : SWITCH_1_FLASH;
    }
    Ret = ReadRomPage(0, DevType, Addr, Buf, RomChip);
    RetAssert(Ret, "ReadRomPage failed!");

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ReadRomPage Buf =" << std::endl;
    for (int i = 0; i < FLASH_PAGE_SIZE / 4; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Buf[i] << std::endl;
    }

    return WT_OK;
}

//Rom
int DevLib::WriteRomPage(int ModId, WT_DEV_TYPE DevType, u32 RomAddr, void *DataBuf, int ChipID)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBase *>(m_Devs[DevType][ModId])->WriteRomPage(RomAddr, DataBuf, ChipID);
    }
    return Ret;
}

int DevLib::ReadRomPage(int ModId, WT_DEV_TYPE DevType, u32 RomAddr, void *DataBuf, int ChipID)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBase *>(m_Devs[DevType][ModId])->ReadRomPage(RomAddr, DataBuf, ChipID);
    }
    return Ret;
}

/*----------------------------business board-------------------------------*/
//AD5611
int DevLib::WriteAD5611(int ModId, int DevId, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->WriteAD5611(DevId, Data);
    }
    return Ret;
}

int DevLib::ReadAD5611(int ModId, int DevId, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->ReadAD5611(DevId, Data);
    }
    return Ret;
}

//AD7682 DAC电压检测芯片
int DevLib::WriteAD7682(int ModId, WT_DEV_TYPE DevType, int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->WriteAD7682(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadAD7682(int ModId, WT_DEV_TYPE DevType, int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ReadAD7682(Addr, Data);
    }
    return Ret;
}

int DevLib::GetAD7682ChannelCode(int ModId, WT_DEV_TYPE DevType, int Channel, int &Value)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetAD7682ChannelCode(Channel, Value);
    }
    return Ret;
}

int DevLib::GetAD7682ChannelVolt(int ModId, WT_DEV_TYPE DevType, int Channel, double &VoltValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetAD7682ChannelVolt(Channel, VoltValue);
    }
    return Ret;
}

int DevLib::GetAD7682ChannelTemperature(int ModId, WT_DEV_TYPE DevType, int Channel, double &Value)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetAD7682ChannelTemperature(Channel, Value);
    }
    return Ret;
}
//AD7689
int DevLib::GetAD7689ChannelCode(int ModId, WT_DEV_TYPE DevType, int Channel, int &Value)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetAD7689ChannelCode(Channel, Value);
    }
    return Ret;
}

int DevLib::GetAD7689ChannelVolt(int ModId, WT_DEV_TYPE DevType, int Channel, double &VoltValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetAD7689ChannelVolt(Channel, VoltValue);
    }
    return Ret;
}

//ADF4106 时钟板上用到两片ADF4106作为频综
int DevLib::WriteADF4106(int ModId, int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->WriteADF4106(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadADF4106(int ModId, int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->ReadADF4106(Addr, Data);
    }
    return Ret;
}

//HMC7044基带板时钟芯片
int DevLib::WriteHM7044(int ModId, int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->WriteHM7044(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadHM7044(int ModId, int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->ReadHM7044(Addr, Data);
    }
    return Ret;
}

//LTC5594
int DevLib::WriteLTC5594(int ModId, int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->WriteLTC5594(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadLTC5594(int ModId, int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->ReadLTC5594(Addr, Data);
    }
    return Ret;
}

//LMX2594
int DevLib::WriteLMX2594(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->WriteLMX2594(RegAddr, RegData);
    }
    return Ret;
}

int DevLib::ReadLMX2594(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ReadLMX2594(RegAddr, RegData);
    }
    return Ret;
}

int DevLib::WriteBusiAD7091(int ModId, const int RegAddr, int VoltData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->WriteAD7091(RegAddr, VoltData);
    }
    return Ret;
}

int DevLib::ReadBusiAD7091(int ModId, const int RegAddr, int &VoltData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->ReadAD7091(RegAddr, VoltData);
    }
    return Ret;
}

int DevLib::GetBBVoltValue(int ModId, int Channel, int &VoltValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    double Value = 0;
    if (m_Devs[0][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[0][ModId])->GetBBVoltValue(Channel, Value);
    }
    VoltValue = Value * 100;
    return Ret;
}

int DevLib::WriteLoShift(int ModId, WT_DEV_TYPE DevType, long long RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->WriteLoShift(RegData);
    }
    return Ret;
}

int DevLib::ReadLoShift(int ModId, WT_DEV_TYPE DevType, long long &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ReadLoShift(RegData);
    }
    return Ret;
}

int DevLib::WriteLoShiftBit(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData)
{
    int Ret = WT_OK;
    long long LoShiftData = 0;

    Ret = ReadLoShift(ModId, DevType, LoShiftData);
    RetAssert(Ret, "ReadLoShift error");
    RegData ? SetBit(LoShiftData, RegAddr) : ClearBit(LoShiftData, RegAddr);
    Ret = WriteLoShift(ModId, DevType, LoShiftData);

    return Ret;
}

int DevLib::ReadLoShiftBit(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData)
{
    int Ret = WT_OK;
    long long LoShiftData = 0;

    Ret = ReadLoShift(ModId, DevType, LoShiftData);
    RegData = GetBit(LoShiftData, RegAddr);

    return Ret;
}

int DevLib::SetLoHMC705(int ModId, WT_DEV_TYPE DevType, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetLoHMC705(Data);
    }
    return Ret;
}

int DevLib::SetLoLoopFilter(int ModId, WT_DEV_TYPE DevType, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetLoLoopFilter(Data);
    }
    return Ret;
}

int DevLib::SetLoFreqChannel(int ModId, WT_DEV_TYPE DevType, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetLoFreqChannel(Data);
    }
    return Ret;
}

int DevLib::SetLoDDSReg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->WriteDDS(RegAddr, RegData);
    }

    return Ret;
}

int DevLib::GetLoDDSReg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ReadDDS(RegAddr, RegData);
    }

    return Ret;
}

int DevLib::SetDDSFreq(int ModId, WT_DEV_TYPE DevType, unsigned int Freq)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetDDSFreq((double)Freq / KHz);
    }

    return Ret;
}

int DevLib::SetDDSFsCurrent(int ModId, WT_DEV_TYPE DevType, unsigned int DacFsCurrent)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetDDSFsCurrent(DacFsCurrent);
    }

    return Ret;
}

// RF 移位寄存器
int DevLib::SetRfShiftReg(int ModId, WT_DEV_TYPE DevType, int RegId, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->WriteATTAndShift(RegId, 0, Data);
    }

    return Ret;
}

int DevLib::GetRfShiftReg(int ModId, WT_DEV_TYPE DevType, int RegId, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType == DEV_TYPE_BACK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::GetRfShiftReg DevType error\n");
        return WT_ARG_ERROR;
    }

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ReadATTAndShift(RegId, 0, Data);
    }

    return Ret;
}

//ADC AD9684
int DevLib::WriteADCReg(int ModId, int RegAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_VSA][ModId])->WriteAdcDac(RegAddr, RegData);
    }
    return Ret;
}
int DevLib::ReadADCReg(int ModId, int RegAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_VSA][ModId])->ReadAdcDac(RegAddr, RegData);
    }
    return Ret;
}

//ADC AD9142
int DevLib::WriteDACReg(int ModId, int RegAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_VSG][ModId])->WriteAdcDac(RegAddr, RegData);
    }
    return Ret;
}

int DevLib::ReadDACReg(int ModId, int RegAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DEV_TYPE_VSG][ModId])->ReadAdcDac(RegAddr, RegData);
    }
    return Ret;
}

int DevLib::SetATTCode(int ModId, WT_DEV_TYPE DevType, int DevId, int Code)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (Code < 0 || Code > ATT_CODE_MAX)
    {
        WTLog::Instance().LOGERR(WT_ATTCODE_OVER_RANGE, "SetATTCode ATT Code error!");
        return WT_ATTCODE_OVER_RANGE;
    }
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetATTCode(DevId, Code);
    }
    return Ret;
}

int DevLib::GetATTCode(int ModId, WT_DEV_TYPE DevType, int DevId, int &Code)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetATTCode(DevId, Code);
        RetAssert(Ret, "ReadATT failed!");
    }
    return Ret;
}


int DevLib::GetRFTemperatureX100(int ModId, WT_DEV_TYPE DevType, int TempId, int &TempValue)
{
    double Data = 0.0;
    if (GetRFTemperature(ModId, DevType, TempId, Data) != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_GET_TEMPERATURE_ERR, "GetRFTemperature error");
        return WT_GET_TEMPERATURE_ERR;
    }

    TempValue = static_cast<int>(Data * 100);

    return WT_OK;
}

int DevLib::WriteLMX2820(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])
                  ->WriteLMX2820(static_cast<LO_ID_E>(GET_DEVID(RegAddr)), GET_DEVDATA(RegAddr), RegData);
    }
    return Ret;
}

int DevLib::ReadLMX2820(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])
                  ->ReadLMX2820(static_cast<LO_ID_E>(GET_DEVID(RegAddr)), GET_DEVDATA(RegAddr), RegData);
    }
    return Ret;
}

int DevLib::SetModFreqPower(int ModId, WT_DEV_TYPE DevType, int Freq, int PowerLevel)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetModFreqPower(fabs((double)Freq / KHz), PowerLevel, Freq < 0);

        if (Ret == WT_OK)
        {
            // 调本振时, 一起调滤波器通道
            Ret |= static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetRfModLoSwitch(fabs((double)Freq / KHz));
        }
    }
    return Ret;
}

int DevLib::SetModFreqPowerHz(int ModId, WT_DEV_TYPE DevType, int Freq, int PowerLevel)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetModFreqPower(fabs(Freq), PowerLevel, Freq < 0);

        if (Ret == WT_OK)
        {
            // 调本振时, 一起调滤波器通道
            Ret |= static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetRfModLoSwitch(fabs(Freq));
        }
    }
    return Ret;
}

int DevLib::SetMixFreqPower(int ModId, WT_DEV_TYPE DevType, int Freq, int PowerLevel)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetMixFreqPower(Freq, PowerLevel);
    }
    return Ret;
}

int DevLib::SetBusiBand(int ModId, WT_DEV_TYPE DevType, int FreqMerge, int BandMerge)
{
    int LoMod = FreqMerge & 0xFFFF;
    int LoMix = (FreqMerge & 0xFFFF0000) >> 16;
    int LoModBand = BandMerge & 0xFFFF;
    int LoMixBand = (BandMerge & 0xFFFF0000) >> 16;
  
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetBand((double)LoMod, (double)LoMix, (WT_RF_MOD_BAND_E)LoModBand, (WT_RF_MIX_BAND_E)LoMixBand);
    }
    return Ret;
}

int DevLib::SetFreqOutputPower(int ModId, WT_DEV_TYPE DevType, int Freq, int Power)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (DevType != DEV_TYPE_VSG)
    {
        return WT_UNITBOARD_NOT_EXIST;
    }

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg*>(m_Devs[DEV_TYPE_VSG][ModId])->SetFreqOutputPower(Freq, Power);
    }

    return Ret;
}

int DevLib::SetBusiBoardFreq(int ModId, WT_DEV_TYPE DevType, int Freq)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetBusiBoardFreq(Freq);
    }
    return Ret;
}

int DevLib::BusiStart(int ModId, WT_DEV_TYPE DevType)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->BusiStart();
    }
    return Ret;
}

int DevLib::BusiStop(int ModId, WT_DEV_TYPE DevType)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->BusiStop();
    }
    return Ret;
}

int DevLib::BusiDown(int ModId, WT_DEV_TYPE DevType)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->BusiDown();
    }
    return Ret;
}

int DevLib::BaseFpgaUpgrade(int ModId, WT_DEV_TYPE DevType, std::string Path, int NotProgram)
{
    int Ret = WT_OK;
    std::unique_ptr<unsigned char[]> pData(new (std::nothrow) unsigned char[EPCS160_SIZE]);
    int Len = Basefun::ReadFile(Path.c_str(), pData.get(), EPCS160_SIZE);
    WTLog::Instance().WriteLog(LOG_DEBUG, "BaseFpgaUpgrade ReadFile Len = %#x\n", Len);
    if (Len < 0)
    {
        return WT_FILE_OPEN_ERROR;
    }

    Ret = BaseFpgaUpgrade(ModId, DevType, pData.get(), Len, NotProgram);
    sleep(5);
    RetWarnning(Ret, "BaseFpgaUpgrade error");
    WTLog::Instance().WriteLog(LOG_DEBUG, "BaseFpgaUpgrade Ret = %d\n", Ret);
    return Ret;
}

int DevLib::BaseFpgaUpgrade(int ModId, WT_DEV_TYPE DevType, unsigned char *pData, unsigned int Len, int NotProgram)
{
    //基带板升级时，不存在单元板不认定为错误
    int Ret = WT_OK;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->BaseFpgaUpgrade(pData, Len, NotProgram);
    }
    return Ret;
}

int DevLib::SetModLoRefSel(int ModId, WT_DEV_TYPE DevType, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetModLoRefSel(Data);
    }
    return Ret;
}

int DevLib::GetModLoRefSel(int ModId, WT_DEV_TYPE DevType, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->GetModLoRefSel(Data);
    }
    return Ret;
}

int DevLib::SetDebugAtt(int ModId, WT_DEV_TYPE DevType, int Index, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetDebugAtt(Index, Data);
    }
    return Ret;
}

int DevLib::SetATTCalConfig(int ModId, WT_DEV_TYPE DevType, ATTCalConfigType &Config)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetATTCalConfig(Config);
    }
    return Ret;
}

int DevLib::SetExtMode(int ModId, WT_DEV_TYPE DevType, ExtModeType ExtMode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetExtMode DevType=%d, ModId=%d, ExtMode=%d\n", DevType, ModId, ExtMode.Mode);
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DevType][ModId])->SetExtMode(ExtMode);
    }
    return Ret;
}

int DevLib::ClearExtMode(int ModId)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    ExtModeType ExtMode;
    ExtMode.Mode = WT_VSG_MODE_NORMAL;
    if (m_Devs[DEV_TYPE_BUSI][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_BUSI][ModId])->SetExtMode(ExtMode);
    }
    return Ret;
}

int DevLib::LdpcCompute(int ModId, LdpcParamType *LdpcParam, int Count)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_BUSI][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_BUSI][ModId])->LdpcCompute(LdpcParam, Count);
    }
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::LdpcCompute ModId=%d nullptr\n", ModId);
    }
    return Ret;
}

int DevLib::BccCompute(int ModId, const BccParamType &BccParam)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_BUSI][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_BUSI][ModId])->BccCompute(BccParam);
    }
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::BccCompute ModId=%d nullptr\n", ModId);
    }
    return Ret;
}

/*-----------------------------   VSA   ---------------------------*/
int DevLib::SetRXDCOffset(int ModId, int Icode, int Qcode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetDCOffset(Icode, Qcode, true);
    }
    return Ret;
}

/*-----------------------------   VSG   ---------------------------*/
int DevLib::SetDacIQGainCode(int ModId, int ICode, int QCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetITuneGain(ICode);
        RetAssert(Ret, "SetDacIQGainCode I code failed!");

        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetQTuneGain(QCode);
        RetAssert(Ret, "SetDacIQGainCode Q code failed!");
    }
    return Ret;
}

int DevLib::SetIOffset(int ModId, int IOffset)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetIOffset(IOffset);
        RetAssert(Ret, "SetIOffset I code failed!");
    }
    return WT_OK;
}

int DevLib::SetQOffset(int ModId, int QOffset)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetQOffset(QOffset);
        RetAssert(Ret, "SetQOffset I code failed!");
    }
    return WT_OK;
}

int DevLib::SetTXDCOffset(int ModId, int IOffset, int QOffset)
{
    int Ret = WT_OK;

    Ret = SetIOffset(ModId, IOffset);
    RetAssert(Ret, "SetTXDCOffset I Offset failed!");

    Ret = SetQOffset(ModId, QOffset);
    RetAssert(Ret, "SetTXDCOffset Q Offset failed!");

    return WT_OK;
}

int DevLib::SetBoostStatus(int ModId, int Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetBoostStatus(Status);
    }
    return Ret;
}

int DevLib::SetVsaLNAStatus(int ModId, int Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetLNAStatus(Status);
    }
    return Ret;
}

int DevLib::SetDacGainX100(int ModId, int Remain)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        double RemainDouble = (double)Remain / 100.0;
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetDacGain(RemainDouble);
    }
    return Ret;
}

/*-----------------------------   BACK   ---------------------------*/
int DevLib::SetOCXOCode(int OCXOId, int OCXOCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetOCXOCode(OCXOId, OCXOCode);
    }
    return Ret;
}

int DevLib::GetOCXOCode(int OCXOId, int &OCXOCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetOCXOCode(OCXOId, OCXOCode);
    }
    return Ret;
}

//HMC1031
int DevLib::WriteClockHM7043(int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteClockHM7043(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadClockHM7043(int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadClockHM7043(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadRfVersion(int Channel, int &Version)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadRfVersion(Channel, Version);
    }
    return Ret;
}

int DevLib::InitCryptoAT88(int Loop)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->InitCryptoAT88(Loop);
    }
    return Ret;
}

int DevLib::WriteCryptoAT88(int Cmd, int Addr, int Len, char *Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteCryptoAT88(Cmd, Addr, Len, Data);
    }
    return Ret;
}

int DevLib::ReadCryptoAT88(int Cmd, int Addr, int Len, char *Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadCryptoAT88(Cmd, Addr, Len, Data);
    }
    return Ret;
}

int DevLib::WriteFanReg(int ReaAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteFanReg(GET_DEVID(ReaAddr), GET_DEVDATA(ReaAddr), RegData);
    }
    return Ret;
}

int DevLib::ReadFanReg(int ReaAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadFanReg(GET_DEVID(ReaAddr), GET_DEVDATA(ReaAddr), RegData);
    }
    return Ret;
}

int DevLib::WriteLedIOExtReg(int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "========SetLedStatus RegAddr=%d, Reg Data=%#x========\n", Addr, Data);
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteLedIOExtReg(Addr, Data);
    }
    return Ret;
}

int DevLib::WriteLedIOExtBit(int LedId, int Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "========SetLedStatus RegBitId=%d, BitStatus=%d========\n", LedId, Status);
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteLedIOExtBit(LedId, Status);
    }
    return Ret;
}

int DevLib::ReadLedIOExtReg(int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadLedIOExtReg(Addr, Data);
    }
    return Ret;
}

int DevLib::ReadLedIOExtBit(int LedId, int &Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadLedIOExtBit(LedId, Status);
    }
    return Ret;
}

int DevLib::WriteSwitchPa(const int Port, int Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteSwitchPa(Port, Status);
    }
    return Ret;
}

int DevLib::ReadSwitchPa(const int Port, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadSwitchPa(Port, Data);
    }
    return Ret;
}

int DevLib::WriteSwitch42553(const int Port, int Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteSwitch42553(Port, Status);
    }
    return Ret;
}

int DevLib::ReadSwitch42553(const int Port, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadSwitch42553(Port, Data);
    }
    return Ret;
}

int DevLib::WritePllAdf4002(int DeviceId, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WritePllAdf4002(DeviceId, Data);
    }
    return Ret;
}

int DevLib::ReadPllAdf4002(int DeviceId, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadPllAdf4002(DeviceId, Data);
    }
    return Ret;
}

/*-----------------------------   ETH(SWITCH)   ---------------------------*/
int DevLib::WriteBackAD7091Reg(int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteBackAD7091Reg(GET_DEVID(Addr), GET_DEVDATA(Addr), Data);
    }
    return Ret;
}

int DevLib::ReadBackAD7091Reg(int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadBackAD7091Reg(GET_DEVID(Addr), GET_DEVDATA(Addr), Data);
    }
    return Ret;
}

int DevLib::GetBackChannelVoltValue(int Addr, double &VoltValue)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetBackChannelVoltValue(GET_DEVID(Addr), GET_DEVDATA(Addr), VoltValue);
    }
    return Ret;
}

int DevLib::GetBackAd7091ChannelValue(int Addr, int &Value)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetBackAd7091ChannelValue(GET_DEVID(Addr), GET_DEVDATA(Addr), Value);
    }
    return Ret;
}

int DevLib::ReadSwitchAD9228(int Addr, int &Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->ReadSwitchAD9228(GET_DEVID(Addr), GET_DEVDATA(Addr), Data);
    }
    return Ret;
}

int DevLib::WriteSwitchAD9228(int Addr, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->WriteSwitchAD9228(GET_DEVID(Addr), GET_DEVDATA(Addr), Data);
    }
    return Ret;
}

int DevLib::GetSwitchPortPower(int Port, double &Power)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    int PowerCode = 0;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchPortPowerCode(Port, PowerCode);
    }

    Power = ((double)PowerCode / 4095) * 1.0;
    return Ret;
}

int DevLib::GetSwitchPortPowerCode(int Port, int &PowerCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchPortPowerCode(Port, PowerCode);
    }
    return Ret;
}

int DevLib::GetSwitchInnerPower(int Port, double &Power)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    int PowerCode = 0;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchInnerPowerCode(Port, PowerCode);
    }

    // 转电压值
    Power = ((double)PowerCode / 65535) * 2.5;
    return Ret;
}

int DevLib::GetSwitchInnerPowerCode(int Port, int &PowerCode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchInnerPowerCode(Port, PowerCode);
    }
    return Ret;
}

int DevLib::SetSwitchState(int ModId, WT_DEV_TYPE DevType, int Port, int State)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (DevType == DEV_TYPE_BACK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::SetSwitchState11 return %d\n", WT_ARG_ERROR);
        return WT_ARG_ERROR;
    }
    else if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetSwitchState(DevType, Port, State);
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::SetSwitchState ModId=%d, DevType=%d, Port=%d, State=%d, return %d\n", ModId, DevType, Port, State, Ret);
    return Ret;
}

int DevLib::SetSwitchShiftReg(int SwId, int RegAddr, int Data)
{
    // WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::SetSwitchShiftReg  RegAddr=0x%x Data=0x%x\n", RegAddr, Data);
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetSwitchShiftReg(SwId, RegAddr, Data);
    }
    // WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::SetSwitchShiftReg SwId=0x%x, RegAddr=0x%x Data=0x%x\n", SwId, RegAddr, Data);

    return Ret;
}

int DevLib::GetSwitchShiftReg(int SwId, int RegAddr, int &Data)
{
    // WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::SetSwitchShiftReg  RegAddr=0x%x Data=0x%x\n", RegAddr, Data);
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchShiftReg(SwId, RegAddr, Data);
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "DevLib::GetSwitchShiftReg SwId=0x%x, RegAddr=0x%x Data=0x%x\n", SwId, RegAddr, Data);
    return Ret;
}

int DevLib::GetSwitchBoardVersion(int SwitchId, int &SBVersion)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchBoardVersion(SwitchId, SBVersion);
    }
    return Ret;
}

int DevLib::SetSwbAttCode(int Port, int Code)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->SetSwbAttCode(Port, Code, 0);
    }
    return Ret;
}

int DevLib::GetSwbAttCode(int Port, int &Code)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwbAttCode(Port, Code, 0);
    }
    return Ret;
}

int DevLib::GetSwitchValueBak(unsigned long long *Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_BACK][0] != nullptr)
    {
        Ret = static_cast<DevBack *>(m_Devs[DEV_TYPE_BACK][0])->GetSwitchValueBak(Data);
    }
    return Ret;
}

int DevLib::SetLOComMode(int ModId, int Data)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (ModId >= 0 && ModId < MAX_BUSINESS_NUM)
    {
        if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr && m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
        {
            Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetLOComMode(Data);
            RetAssert(Ret, "DevVsa SetLOComMode failed!");
            Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetLOComMode(Data);
            RetAssert(Ret, "DevVsg SetLOComMode failed!");
        }
    }
    else
    {
        for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
        {
            if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr && m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
            {
                Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetLOComMode(Data);
                RetAssert(Ret, "DevVsa SetLOComMode failed!");
                Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetLOComMode(Data);
                RetAssert(Ret, "DevVsg SetLOComMode failed!");
            }
        }
    }
#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetLOComMode  ModId =" << ModId << "  Data =" << Data << std::endl;
#endif
    return Ret;
}

int DevLib::ReSetLOComMode()
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    for (int ModId = 0; ModId < MAX_BUSINESS_NUM; ModId++)
    {
        if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr && m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
        {
            Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->ReSetLoMode();
            RetAssert(Ret, "DevVsa SetLOComMode failed!");
            Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->ReSetLoMode();
            RetAssert(Ret, "DevVsg SetLOComMode failed!");
        }
    }
    return Ret;
}

int DevLib::GetLOComMode(int ModId, int &Data)
{

    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (ModId >= 0 && ModId <= MAX_BUSINESS_NUM)
    {
        if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr && m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
        {
            Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->GetLOComMode(Data);
            RetAssert(Ret, "DevVsa GetLOComMode failed!");
            int Vsa_Mode = Data;
            Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->GetLOComMode(Data);
            RetAssert(Ret, "DevVsg GetLOComMode failed!");

            if (Vsa_Mode != Data)
            {
                Ret = WT_ERROR;
                RetAssert(Ret, "DevVsg and DevVsa LoComMode isnot same");
            }
        }
    }
    else
    {
        if (m_Devs[DEV_TYPE_VSA][0] != nullptr)
        {
            Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][0])->GetLOComMode(Data);
            RetAssert(Ret, "DevVsa GetLOComMode failed!");
        }
    }

#if DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetLOComMode  ModId =" << ModId << "  Data =" << Data << std::endl;
#endif
    return Ret;
}

int DevLib::SetFreqWithConfirm(int ModId, WT_DEV_TYPE DevType, double Freq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DevType][ModId] != nullptr && DevType == DEV_TYPE_VSG)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DevType][ModId])->SetFreqWithConfirm(Freq, FreqParm, WorkMode);
    }
    else if (m_Devs[DevType][ModId] != nullptr && DevType == DEV_TYPE_VSA)
    {
        Ret = static_cast<DevVsa *>(m_Devs[DevType][ModId])->SetFreqWithConfirm(Freq, FreqParm, WorkMode);
    }
    else
    {
        Ret = WT_ERROR;
    }
    return Ret;
}

int DevLib::SetAnalogIQSW(int ModId, int AnalogIQSW)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr )
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetAnalogIQSW(AnalogIQSW);
        RetAssert(Ret, "DevVsg SetAnalogIQSW failed!");
    }
    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr )
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->SetAnalogIQSW(AnalogIQSW);
    }

    return Ret;
}

int DevLib::GetAnalogIQSW(int ModId, int &AnalogIQSW)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DEV_TYPE_VSA][ModId] != nullptr )
    {
        Ret = static_cast<DevVsa *>(m_Devs[DEV_TYPE_VSA][ModId])->GetAnalogIQSW(AnalogIQSW);
    }
    return Ret;
}

int DevLib::GetDebugFlag(int &Data)
{
    Data = m_DeBugFlag;
    return WT_OK;
}

int DevLib::SetDebugFlag(int Data)
{
    m_DeBugFlag = Data ? 1 : 0;
    return WT_OK;
}

int DevLib::WriteHMC833Reg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->WriteHMC833Reg(RegAddr, RegData);
    }
    return Ret;
}

int DevLib::ReadHMC833Reg(int ModId, WT_DEV_TYPE DevType, int RegAddr, int &RegData)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->ReadHMC833Reg(RegAddr, RegData);
    }
    return Ret;
}

int DevLib::CheckLOIsLock(int ModId, WT_DEV_TYPE DevType, int LoId, int &Lock)
{

    if (m_Devs[DevType][ModId] != nullptr)
    {
        Lock = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->CheckLOIsLock((LO_ID_E)LoId);
    }
    else
    {
        return WT_UNITBOARD_NOT_EXIST;
    }
    return WT_OK;
}

void DevLib::GetBroadcastPortPower(double Power[WT_RF_MAX])
{
    for (int i = 0; i < WT_RF_MAX; i++)
    {
        Power[i] = m_BroadcastPortPower[i];
    }
}

int DevLib::BroadcastPortDebug()
{
    return m_BroadcastPortDebug;
}

int DevLib::SetfastVsgPower(int ModId, double Power)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetfastVsgPower(Power);
    }

    return Ret;
}

int DevLib::SetfastVsgFreq(int ModId, double Freq)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;

    if (m_Devs[DEV_TYPE_VSG][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DEV_TYPE_VSG][ModId])->SetfastVsgFreq(Freq);
    }

    return Ret;
}

int DevLib::SetListModeStatus(int ModId, WT_DEV_TYPE DevType, int Status)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevBusiness *>(m_Devs[DevType][ModId])->SetListModeStatus(Status);
    }
    return Ret;
}

int DevLib::GetSeqProgress(int ModId, WT_DEV_TYPE DevType,int &Progress)
{
    int Ret = WT_UNITBOARD_NOT_EXIST;
    if (m_Devs[DevType][ModId] != nullptr)
    {
        Ret = static_cast<DevVsg *>(m_Devs[DevType][ModId])->GetSeqProgress(Progress);
    }
    return Ret;
}
