
#ifndef __SysDef_API_H__
#define __SysDef_API_H__
#ifndef LINUX
#pragma warning (disable:4996)              ///屏蔽unsafe警告

///基础类型定义
typedef unsigned char       u8;
typedef char                s8;
typedef unsigned short      u16;
typedef short               s16;
typedef unsigned int        u32;
typedef int                 s32;
typedef float               FP32;
typedef double              FP64;
typedef void               *PVOID;
typedef unsigned __int64    u64;
typedef __int64             s64;
typedef double  Complex[2];
#else
///基础类型定义
typedef unsigned char       u8;
typedef char                s8;
typedef unsigned short      u16;
typedef short               s16;
typedef unsigned int        u32;
typedef int                 s32;
typedef float               FP32;
typedef double              FP64;
typedef void               *PVOID;
typedef double  Complex[2];
#endif

#define IN
#define OUT
#define INOUT


#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif


#endif
