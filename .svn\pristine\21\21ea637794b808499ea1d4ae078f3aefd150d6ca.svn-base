#ifndef _WT_SWITCH_H_
#define _WT_SWITCH_H_

typedef int (*FUNC_SWITCH_SELECT)(int , int );

extern FUNC_SWITCH_SELECT g_pVsg_Select_SW;
extern FUNC_SWITCH_SELECT g_pVsa_Select_SW;

struct SwitchPortType
{
    int Type;
    int SubPort;
    int State;
    int Mode;
};

//PORT
int WT_SetTXPort(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetRXPort(int DataLength, void *arg, struct dev_unit *pdev);
int WT_GetSwitchValueBak(int DataLength, void *arg, struct dev_unit *pdev);

int WT_InitSwitch(struct dev_unit *pdev, int SpecSwitchFlag);


/*----------------开关板---------------*/
int WT_SetSwitchState(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetSwitchShiftReg(int DataLength, void *arg, struct dev_unit *pdev);
int WT_GetSwitchShiftReg(int DataLength, void *arg, struct dev_unit *pdev);

inline int wt_SelectPort_S1(int Type, int Port, int PortState);
int wt_GetInitState(void);
int wt_GetSwitchState(struct SwitchPortType *SwitchPort, int PortNum, unsigned int *Value);

//
int WT_SetSwitchCfg(int DataLength, void *arg, struct dev_unit *pdev);

int wt_SetSwitchStatus(int Type, int SwitchId, int SubPort, int StateMode, int State);
int wt_SetSwitchShiftReg(int SwId, int RegId, int data);
int wt_GetSwitchShiftReg(int SwId, int RegId, int *data);


//开关板额外ATT
int wt_SetSwbAttCode_VA(int SwitchId, int SubPort, int Data, struct dev_unit *pdev);
int wt_GetSwbAttCode_VA(int SwitchId, int SubPort, int *Data, struct dev_unit *pdev);
int wt_SetSwbAttCode_VB(int SwitchId, int SubPort, int Data, struct dev_unit *pdev);
int wt_GetSwbAttCode_VB(int SwitchId, int SubPort, int *Data, struct dev_unit *pdev);
int wt_SetSwbAttCode_428VA(int SwitchId, int SubPort, int Data, struct dev_unit *pdev);
int wt_GetSwbAttCode_428VA(int SwitchId, int SubPort, int *Data, struct dev_unit *pdev);
int wt_SetSwbAttCode_418VA(int SubPort, int Data, struct dev_unit *pdev);
int wt_GetSwbAttCode_418VA(int SubPort, int *Data, struct dev_unit *pdev);

int wt_ReadSwbAttCodeFromCache_418VA(int SubPort, int *AttArr, int ShiftCache[]);

int WT_SetSwbAttCode(int DataLength, void *arg, struct dev_unit *pdev);
int WT_GetSwbAttCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_WriteSwitchStatus(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadSwitchStatus(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetSwitchStatus(int DataLength, void *arg, struct dev_unit *pdev);

//设置开关板PA
int WT_WriteSwitchPa(int DataLength, void *arg, struct dev_unit *pdev);
int WT_ReadSwitchPa(int DataLength, void *arg, struct dev_unit *pdev);

//设置开关板42553
int WT_WriteSwitch42553(int DataLength, void *arg, struct dev_unit *pdev);
int WT_ReadSwitch42553(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetSwitchCTL3(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetSwitchVsgGapPower(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetSwitchVsgGapPowerCtrlMode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetSwitchMode(int DataLength, void *arg, struct dev_unit *pdev);

#define SW_SHIFT_REG_MEMBER_COUNT (5)
#define WT_PORT_STATE_MAX WT_RF_STATE_MAX

#endif