#include "scpi_3gpp_gen_nbiot.h"

#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular;

static inline Alg_NBIOT_WaveGenType &Nbiot(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->Pn3GPP->NBIOT;
}

scpi_result_t SCPI_NBLOT_SetLinkDirect(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Nbiot(context).LinkDirect = value;
        GetVsgDefaultParamFrom3GPPCallback(ALG_3GPP_STD_NB_IOT, value, *(attr->Pn3GPP.get()));
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(ALG_3GPP_FILTER_NON, ALG_3GPP_FILTER_WOLA))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.Type = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterSampleRate(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, INT_MAX))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.Fs = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterMaxOrder(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {128, 256, 512})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.MaxOrder = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterFpassFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.FpassFactor = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterFstopFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.FstopFactor = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterPassRipple(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 0.3))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.PassRipple = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterStopAtten(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 100.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.StopAtten = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterRollOffFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.RollOffFactor = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterCutOffFreqShift(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-1.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.CutOffFreqShift = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetFilterWindowLenFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.WindowLenFactor = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellOperMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.OperationMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellBW(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {200000, 3000000, 5000000, 10000000, 15000000, 20000000})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.ChannelBW = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellRBIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(-47, 134))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.RBIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellNBCellID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 503))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.NBCellID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellGroupHopping(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.GrpHopping = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneCyclicShift3(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.TTCShift = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneCyclicShift6(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.STCShift = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellBaseSequenceMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.BaseSeqMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneBaseSequence3(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 11))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.TTBaseSeq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneBaseSequence6(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.STBaseSeq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellToneBaseSequence12(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 29))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.TWBaseSeq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetCellDeltaSequenceShift(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 29))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Cell.DeltaSeqShift = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 65535))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.UeID = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEScrambling(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.Scrambling = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEDataType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.DataType = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEInitialization(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 0x7FFFFF))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.Initialization = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetUEChanCodeingState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Ue.ChanCodingState = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedChanType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.ChanType = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschFormat(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Format = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschSubCSpace(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {15000, 3750})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.SCSpacing = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschStartSubFrame(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.StartSubfrm = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschReptitions(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4, 8, 16, 32, 64, 128})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.Repetitions = value;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBLOT_UL_SetSchedNPuschNumResUnits(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 3, 4, 5, 6, 8, 10})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).UL.Schedule.Npusch.RUs = value;
    }

    return SCPI_ResultOK(context, iRet);
}
