#pragma once
#ifndef _CRYPTOLOGY_H__
#define _CRYPTOLOGY_H__

typedef struct
{
	s8 *chpHead;        /* BUFF 的头指针 */
	u32 buff_len;       /* BUFF 总长度 */
	u32 data_len;       /* BUFF 有效数据长度 */
} ExchangeBuff;



#define VERIFY_REQ_LEN 256

class Cryptology
{
public:
    Cryptology() {};
    virtual ~Cryptology(){};

	virtual u32 GetProAckHeadSize() = 0;
	virtual u32 GetProAckStartFlagSize() = 0;
	virtual u32 GetProAckSize() = 0;
	virtual u32 GetProAckNoStartFlagSize() = 0;

	virtual u32 GetProCmdHeadSize() = 0;
	virtual u32 GetProCmdStartFlagSize() = 0;
	virtual u32 GetProCmdSize() = 0;
	virtual u32 GetProCmdNoStartFlagSize() = 0;
	virtual s32 GetFunFromCmdHead(s8 *head_buff) = 0;

	virtual u32 GetDataLength(s8 *srcProHead) = 0;
	virtual u32 GetCmdDataLength(s8 *srcProHead) = 0;
	virtual const s8 *GetProAckStartFlag(s8 *package, u32 packageSize) = 0;
	virtual const s8 *GetProCmdStartFlag(s8 *package, u32 packageSize) = 0;

	virtual s32 GetComuPort() = 0;
	virtual const s8 *GetPlainText(s32 deviceType, s32 connType) = 0;
	virtual const s8 *GetAuthText() = 0;
	virtual s32 VerifyProAckHeadExceptStartFlag(s8 *ackHead, s8 *cmdHead, s32 *errCodeFromFir) = 0;

	//加密
	virtual s32 Encrypt(s32 *text, s32 textLen, s32 *random, s32 randomLen) = 0;
	//解密
	virtual s32 Decrypt(s32 *text, s32 textLen, s32 *random, s32 randomLen) = 0;

};
#endif

