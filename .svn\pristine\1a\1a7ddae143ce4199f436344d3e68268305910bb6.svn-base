#include "switch.h"

#include <iostream>
#include <string>
#include <stdio.h>
#include <unistd.h>
#include <cstdlib>
#include <string.h>

#include "../wterror.h"
#include "defines.h"
#include "../wtlog.h"
#include "devlib.h"

#define SWITCH_CFG_FILE_RELATIVE_PATH "/configuration/"

SwitchCfg &SwitchCfg::Instance(void)
{
    static SwitchCfg SwitchCfgInstance;
    return SwitchCfgInstance;
}

SwitchCfg::SwitchCfg()
{
    memset(m_SwitchBitMap, 0, GetSwitchBitMapSize());
}

void SwitchCfg::InitSwbCfg(int TesterType, int Version, int SpecSwitchFlag)
{
    int RegIndex = 0;
    int BitIndex = 0;
    const string *WT448SwitchBitName = NULL;
    const string(*SwitchBitName)[WT_SWITCH_REG_BIT_LENGTH] = NULL;

    memset(m_SwitchBitMap, 0, GetSwitchBitMapSize());
    if (TesterType == HW_WT418)
    {
        if (Version >= VERSION_D)
        {
            WT448SwitchBitName = WT428CSwitchBitName_VA;
            SwitchBitName = SwitchBitName_428C_VD;
        }
        else
        {
            WT448SwitchBitName = WT418SwitchBitName_VA;
        	SwitchBitName = SwitchBitName_418_VA;
        }
    }
    else if (TesterType == HW_WT428)
    {
        WT448SwitchBitName = WT428SwitchBitName_428VA_VB;
        SwitchBitName = SwitchBitName_428VA_VB;
    }
    else
    {
        if (Version == VERSION_A)
        {
            WT448SwitchBitName = WT448SwitchBitName_VA;
            SwitchBitName = SwitchBitName_VA;
        }
        else if (SpecSwitchFlag || (Version & 0x7) == VERSION_B || (Version & 0x7) == VERSION_C)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"if(Version == VERSION_B||Version == VERSION_C)"<<std::endl;
            WT448SwitchBitName = WT448SwitchBitName_VB;
            SwitchBitName = SwitchBitName_VB;
        }
        else if ((Version & 0x7) >= VERSION_D)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"(Version >= VERSION_D)"<<std::endl;
            WT448SwitchBitName = WT448SwitchBitName_VD;
            SwitchBitName = SwitchBitName_VD;
        }
    }

    for (int i = 0; i < WT_SWITCH_REG_CTRL_BIT_COUNT_MAX; ++i)
    {
        const string &BitNameStr = WT448SwitchBitName[i];
        if (BitNameStr.length() == 0)
        {
            break;
        }

        GetSwitchBitIndex(BitNameStr, RegIndex, BitIndex, SwitchBitName);
        m_SwitchBitMap[i][SWITCH_BIT_MAP_REG] = RegIndex;
        m_SwitchBitMap[i][SWITCH_BIT_MAP_BIT] = BitIndex;
        //WTLog::Instance().WriteLog(LOG_DEBUG, "m_SwitchBitMap %s i=%d 0=%d 1=%d\n", BitNameStr.c_str(), i, m_SwitchBitMap[i][SWITCH_BIT_MAP_REG], m_SwitchBitMap[i][SWITCH_BIT_MAP_BIT]);
    }
}

int SwitchCfg::GetSwitchBitIndex(const string &Line, int &RegIndex, int &BitIndex, const string (*SwitchBitName)[WT_SWITCH_REG_BIT_LENGTH])
{
    int i = 0;
    int j = 0;
    int find = 0;

    for (i = 0; i < SWITCH_SW_CTRL_REG_MAX; ++i)
    {
        for (j = 0; j < WT_SWITCH_REG_BIT_LENGTH; ++j)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "GetSwitchBitIndex m_SwitchBitName[%d][%d]=%s\n", i, j, SwitchBitName[i][j].c_str());
            if (SwitchBitName[i][j].length() == 0)
            {
                break;
            }

            if (Line == SwitchBitName[i][j])
            {
                find = 1;
                break;
            }
        }

        if (find == 1)
        {
            break;
        }
    }

    if (i < SWITCH_SW_CTRL_REG_MAX)
    {
        //WTLog::Instance().WriteLog(LOG_DEBUG, "index=%d, %d: %s: %s\n", i, j, SwitchBitName[i][j].c_str(), Line.c_str());
        RegIndex = i;
        BitIndex = j;
        return WT_OK;
    }
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "SwitchCfg::GetSwitchBitIndex Cannot find: %s\n", Line.c_str());
        return -1;
    }
}

/////////////////////////////////////////////////////////////////////////
// VA开关板定义
/////////////////////////////////////////////////////////////////////////

const string SwitchCfg::WT448SwitchBitName_VA[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX] = {
    // shift0
    "RF_TX1_CTL1",
    "RF_TX1_CTL2",
    "RF_TX1_CTL3",
    "RF_TX1_CTL4",
    "RF_TX_CTL5",
    "RF_TX2_CTL6",
    "RF_TX2_CTL7",
    "RF_TX2_CTL8",
    "RF_TX2_CTL9",
    "RF_RX1_CTL1",
    "RF_RX1_CTL2",
    "RF_RX1_CTL3",
    "RF_RX1_CTL4",
    "RF_RX_CTL5",
    "RF_RX2_CTL6",
    "RF_RX2_CTL7",
    "RF_RX2_CTL8",
    "RF_RX2_CTL9",
    "TX1_CTL",
    "TX2_CTL",
    "RX1_S_CTL",
    "RX1_P1_CTL1",
    "RX1_P1_CTL2",
    "RX1_P2_CTL1",
    "RX1_P2_CTL2",
    "RX2_S_CTL",
    "RX2_P3_CTL1",
    "RX2_P3_CTL2",
    "RX2_P4_CTL1",
    "RX2_P4_CTL2",

    // shift1
    "P1_S_CTL1",
    "P1_S1_CTL2",
    "P1_T_CTL3",
    "P1_R_CTL4",
    "P1_R_CTL6",
    "P1_B_CTL7",
    "TX1_Port1",
    "RX1_Port1",

    // shift2
    "P2_S_CTL1",
    "P2_S1_CTL2",
    "P2_T_CTL3",
    "P2_R_CTL4",
    "P2_R_CTL6",
    "P2_B_CTL7",
    "TX1_Port2",
    "RX1_Port2",

    // shift3
    "P3_S_CTL1",
    "P3_S1_CTL2",
    "P3_T_CTL3",
    "P3_R_CTL4",
    "P3_R_CTL6",
    "P3_B_CTL7",
    "TX2_Port3",
    "RX2_Port3",

    // shift4
    "P4_S_CTL1",
    "P4_S1_CTL2",
    "P4_T_CTL3",
    "P4_R_CTL4",
    "P4_R_CTL6",
    "P4_B_CTL7",
    "TX2_Port4",
    "RX2_Port4",

    // shift5
    // No use

    // 42553
    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",

    // SW_PA寄存器
    "Port1_Pa",
    "Port2_Pa",
    "Port3_Pa",
    "Port4_Pa",
};

const string SwitchCfg::SwitchBitName_VA[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH] = {
    // shift0
    {
        "RF_TX1_CTL1",
        "RF_TX1_CTL2",
        "RF_TX1_CTL3",
        "RF_TX1_CTL4",
        "RF_TX_CTL5",
        "RF_TX2_CTL6",
        "RF_TX2_CTL7",
        "RF_TX2_CTL8",
        "RF_TX2_CTL9",
        "RF_RX1_CTL1",
        "RF_RX1_CTL2",
        "RF_RX1_CTL3",
        "RF_RX1_CTL4",
        "RF_RX_CTL5",
        "RF_RX2_CTL6",
        "RF_RX2_CTL7",
        "RF_RX2_CTL8",
        "RF_RX2_CTL9",
        "TX1_CTL",
        "TX2_CTL",
        "RX1_S_CTL",
        "RX1_P1_CTL1",
        "RX1_P1_CTL2",
        "RX1_P2_CTL1",
        "RX1_P2_CTL2",
        "RX2_S_CTL",
        "RX2_P3_CTL1",
        "RX2_P3_CTL2",
        "RX2_P4_CTL1",
        "RX2_P4_CTL2",
    },

    // shift1
    {
        "P1_S_CTL1",
        "P1_S1_CTL2",
        "P1_T_CTL3",
        "P1_R_CTL4",
        "P1_R_CTL6",
        "P1_B_CTL7",
        "TX1_Port1",
        "RX1_Port1",
    },

    // shift2
    {
        "P2_S_CTL1",
        "P2_S1_CTL2",
        "P2_T_CTL3",
        "P2_R_CTL4",
        "P2_R_CTL6",
        "P2_B_CTL7",
        "TX1_Port2",
        "RX1_Port2",
    },

    // shift3
    {
        "P3_S_CTL1",
        "P3_S1_CTL2",
        "P3_T_CTL3",
        "P3_R_CTL4",
        "P3_R_CTL6",
        "P3_B_CTL7",
        "TX2_Port3",
        "RX2_Port3",
    },

    // shift4
    {
        "P4_S_CTL1",
        "P4_S1_CTL2",
        "P4_T_CTL3",
        "P4_R_CTL4",
        "P4_R_CTL6",
        "P4_B_CTL7",
        "TX2_Port4",
        "RX2_Port4"},

    // shift5
    // No use
    {},

    // 42553
    {
        "P1_T_CTL5",
        "P2_T_CTL5",
        "P3_T_CTL5",
        "P4_T_CTL5",
    },

    // SW PA
    {
        "Port1_Pa",
        "Port2_Pa",
        "Port3_Pa",
        "Port4_Pa",
    },
};

/////////////////////////////////////////////////////////////////////////
// VB开关板定义
/////////////////////////////////////////////////////////////////////////

const string SwitchCfg::WT448SwitchBitName_VB[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX] = {
    // shift0
    "RF_TX1_CTL3",
    "RF_TX1_CTL1",
    "RF_TX_CTL5",
    "RF_TX1_CTL2",
    "RF_TX2_CTL7",
    "RF_TX2_CTL6",
    "RF_TX2_CTL9",
    "RF_TX2_CTL8",

    "RX1_P1_CTL1",
    "TX1_P1_CTL1",
    "RX1_P2_CTL1",
    "TX1_P2_CTL1",
    "RX2_P3_CTL1",
    "TX2_P3_CTL1",
    "RX2_P4_CTL1",
    "TX2_P4_CTL1",

    "RF_RX2_CTL6",
    "RF_RX2_CTL7",
    "RF_RX2_CTL8",
    "RF_RX2_CTL9",
    "RF_RX_CTL5",
    "RF_RX1_CTL2",
    "RF_RX1_CTL3",
    "RF_RX1_CTL4",

    // shift1
    "RF_RX1_CTL1",
    // "Port1_A5",   // ATT
    // "Port1_A4",
    // "Port1_A3",
    // "Port1_A2",
    // "Port1_A1",
    // "Port1_A0",
    // "TP8",

    "P1_R_CTL6",
    "RX1_P1_CTL2",
    "P1_B_CTL7",
    "P1_T_CTL3",
    "P1_R_CTL4",
    "P1_S_CTL1",
    "TX1_P1_CTL2",
    "P1_S1_CTL2",

    "RX1_S_CTL",
    // "Port2_A5", // ATT
    // "Port2_A4",
    // "Port2_A3",
    // "Port2_A2",
    // "Port2_A1",
    // "Port2_A0",
    "P2_R_CTL4",

    "P2_R_CTL6",
    "RX1_P2_CTL2",
    "P2_B_CTL7",
    "TX1_CTL",
    "P2_T_CTL3",
    "P2_S_CTL1",
    "P2_S1_CTL2",
    "TX1_P2_CTL2",

    // shift2
    "P4_R_CTL6",
    "RX2_P4_CTL2",
    "P4_B_CTL7",
    "P4_T_CTL3",
    "P4_R_CTL4",
    "P4_S_CTL1",
    "TX2_P4_CTL2",
    "P4_S1_CTL2",

    "TX2_CTL",
    // "Port4_A5",  // ATT
    // "Port4_A4",
    // "Port4_A3",
    // "Port4_A2",
    // "Port4_A1",
    // "Port4_A0",
    "RF_TX1_CTL4",

    "P3_R_CTL6",
    "RX2_P3_CTL2",
    "P3_B_CTL7",
    "RX2_S_CTL",
    "P3_T_CTL3",
    "P3_S_CTL1",
    "TX2_P3_CTL2",
    "P3_S1_CTL2",

    "P3_R_CTL4",
    // "Port3_A5",  // ATT
    // "Port3_A4",
    // "Port3_A3",
    // "Port3_A2",
    // "Port3_A1",
    // "Port3_A0",
    // "TP6",

    // shift3
    // shift4
    // shift5

    // 42553
    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",

    // SW_PA寄存器
    // "Port1_Pa",
    // "Port2_Pa",
    // "Port3_Pa",
    // "Port4_Pa",
};

const string SwitchCfg::SwitchBitName_VB[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH] = {
    // shift0
    {
        "RF_TX1_CTL3",
        "RF_TX1_CTL1",
        "RF_TX_CTL5",
        "RF_TX1_CTL2",
        "RF_TX2_CTL7",
        "RF_TX2_CTL6",
        "RF_TX2_CTL9",
        "RF_TX2_CTL8",

        "RX1_P1_CTL1",
        "TX1_P1_CTL1",
        "RX1_P2_CTL1",
        "TX1_P2_CTL1",
        "RX2_P3_CTL1",
        "TX2_P3_CTL1",
        "RX2_P4_CTL1",
        "TX2_P4_CTL1",

        "RF_RX2_CTL6",
        "RF_RX2_CTL7",
        "RF_RX2_CTL8",
        "RF_RX2_CTL9",
        "RF_RX_CTL5",
        "RF_RX1_CTL2",
        "RF_RX1_CTL3",
        "RF_RX1_CTL4",
    },

    // shift1
    {
        "RF_RX1_CTL1",
        "Port1_A5",
        "Port1_A4",
        "Port1_A3",
        "Port1_A2",
        "Port1_A1",
        "Port1_A0",
        "TP8",

        "P1_R_CTL6",
        "RX1_P1_CTL2",
        "P1_B_CTL7",
        "P1_T_CTL3",
        "P1_R_CTL4",
        "P1_S_CTL1",
        "TX1_P1_CTL2",
        "P1_S1_CTL2",

        "RX1_S_CTL",
        "Port2_A5",
        "Port2_A4",
        "Port2_A3",
        "Port2_A2",
        "Port2_A1",
        "Port2_A0",
        "P2_R_CTL4",

        "P2_R_CTL6",
        "RX1_P2_CTL2",
        "P2_B_CTL7",
        "TX1_CTL",
        "P2_T_CTL3",
        "P2_S_CTL1",
        "P2_S1_CTL2",
        "TX1_P2_CTL2",
    },

    // shift2
    {
        "P4_R_CTL6",
        "RX2_P4_CTL2",
        "P4_B_CTL7",
        "P4_T_CTL3",
        "P4_R_CTL4",
        "P4_S_CTL1",
        "TX2_P4_CTL2",
        "P4_S1_CTL2",

        "TX2_CTL",
        "Port4_A5",
        "Port4_A4",
        "Port4_A3",
        "Port4_A2",
        "Port4_A1",
        "Port4_A0",
        "RF_TX1_CTL4",

        "P3_R_CTL6",
        "RX2_P3_CTL2",
        "P3_B_CTL7",
        "RX2_S_CTL",
        "P3_T_CTL3",
        "P3_S_CTL1",
        "TX2_P3_CTL2",
        "P3_S1_CTL2",

        "P3_R_CTL4",
        "Port3_A5",
        "Port3_A4",
        "Port3_A3",
        "Port3_A2",
        "Port3_A1",
        "Port3_A0",
        "TP6",
    },

    // shift3
    {},

    // shift4
    {},

    // shift5
    // No use
    {},

    // 42553
    {
        "P1_T_CTL5",
        "P2_T_CTL5",
        "P3_T_CTL5",
        "P4_T_CTL5",
    },

    // SW PA
    {},
};
const string SwitchCfg::WT448SwitchBitName_VD[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX] = {
    // shift0
    "RF_TX1_CTL3",
    "RF_TX1_CTL1",
    "RF_TX_CTL5",
    "RF_TX1_CTL2",
    "RF_TX2_CTL7",
    "RF_TX2_CTL6",
    "RF_TX2_CTL9",
    "RF_TX2_CTL8",

    "RX1_P1_CTL1",
    "TX1_P1_CTL1",
    "RX1_P2_CTL1",
    "TX1_P2_CTL1",
    "RX2_P3_CTL1",
    "TX2_P3_CTL1",
    "RX2_P4_CTL1",
    "TX2_P4_CTL1",

    "RF_RX2_CTL6",
    "RF_RX2_CTL7",
    "RF_RX2_CTL8",
    "RF_RX2_CTL9",
    "RF_RX_CTL5",
    "RF_RX1_CTL2",
    "RF_RX1_CTL3",
    "RF_RX1_CTL4",

    // shift1
    "RF_RX1_CTL1",
    // "Port1_A5",   // ATT
    // "Port1_A4",
    // "Port1_A3",
    // "Port1_A2",
    // "Port1_A1",
    // "Port1_A0",
    // "TP8",

    "P1_R_CTL6",
    "RX1_P1_CTL2",
    "P1_B_CTL7",
    "P1_T_CTL3",
    "P1_R_CTL4",
    //"Reversed",
    "TX1_P1_CTL2",
    "P1_S1_CTL2",

    "RX1_S_CTL",
    // "Port2_A5", // ATT
    // "Port2_A4",
    // "Port2_A3",
    // "Port2_A2",
    // "Port2_A1",
    // "Port2_A0",
    "P2_R_CTL4",

    "P2_R_CTL6",
    "RX1_P2_CTL2",
    "P2_B_CTL7",
    "TX1_CTL",
    "P2_T_CTL3",
    //"Reversed",
    "P2_S1_CTL2",
    "TX1_P2_CTL2",

    // shift2
    "P4_R_CTL6",
    "RX2_P4_CTL2",
    "P4_B_CTL7",
    "P4_T_CTL3",
    "P4_R_CTL4",
    //"Reversed",
    "TX2_P4_CTL2",
    "P4_S1_CTL2",

    "TX2_CTL",
    // "Port4_A5",  // ATT
    // "Port4_A4",
    // "Port4_A3",
    // "Port4_A2",
    // "Port4_A1",
    // "Port4_A0",
    "RF_TX1_CTL4",

    "P3_R_CTL6",
    "RX2_P3_CTL2",
    "P3_B_CTL7",
    "RX2_S_CTL",
    "P3_T_CTL3",
    //"Reversed",
    "TX2_P3_CTL2",
    "P3_S1_CTL2",

    "P3_R_CTL4",
    // "Port3_A5",  // ATT
    // "Port3_A4",
    // "Port3_A3",
    // "Port3_A2",
    // "Port3_A1",
    // "Port3_A0",
    // "TP6",

    // shift3
    // shift4
    // shift5

    // 42553
    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",

    // SW_PA寄存器
    // "Port1_Pa",
    // "Port2_Pa",
    // "Port3_Pa",
    // "Port4_Pa",

    //PAC
    "P1_S_FPGA_CTL1",
    "P2_S_FPGA_CTL1",
    "P3_S_FPGA_CTL1",
    "P4_S_FPGA_CTL1",
};

const string SwitchCfg::SwitchBitName_VD[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH] = {
    // shift0
    {
        "RF_TX1_CTL3",
        "RF_TX1_CTL1",
        "RF_TX_CTL5",
        "RF_TX1_CTL2",
        "RF_TX2_CTL7",
        "RF_TX2_CTL6",
        "RF_TX2_CTL9",
        "RF_TX2_CTL8",

        "RX1_P1_CTL1",
        "TX1_P1_CTL1",
        "RX1_P2_CTL1",
        "TX1_P2_CTL1",
        "RX2_P3_CTL1",
        "TX2_P3_CTL1",
        "RX2_P4_CTL1",
        "TX2_P4_CTL1",

        "RF_RX2_CTL6",
        "RF_RX2_CTL7",
        "RF_RX2_CTL8",
        "RF_RX2_CTL9",
        "RF_RX_CTL5",
        "RF_RX1_CTL2",
        "RF_RX1_CTL3",
        "RF_RX1_CTL4",
    },

    // shift1
    {
        "RF_RX1_CTL1",
        "Port1_A5",
        "Port1_A4",
        "Port1_A3",
        "Port1_A2",
        "Port1_A1",
        "Port1_A0",
        "TP8",

        "P1_R_CTL6",
        "RX1_P1_CTL2",
        "P1_B_CTL7",
        "P1_T_CTL3",
        "P1_R_CTL4",
        "Reversed",
        "TX1_P1_CTL2",
        "P1_S1_CTL2",

        "RX1_S_CTL",
        "Port2_A5",
        "Port2_A4",
        "Port2_A3",
        "Port2_A2",
        "Port2_A1",
        "Port2_A0",
        "P2_R_CTL4",

        "P2_R_CTL6",
        "RX1_P2_CTL2",
        "P2_B_CTL7",
        "TX1_CTL",
        "P2_T_CTL3",
        "Reversed",
        "P2_S1_CTL2",
        "TX1_P2_CTL2",
    },

    // shift2
    {
        "P4_R_CTL6",
        "RX2_P4_CTL2",
        "P4_B_CTL7",
        "P4_T_CTL3",
        "P4_R_CTL4",
        "Reversed",
        "TX2_P4_CTL2",
        "P4_S1_CTL2",

        "TX2_CTL",
        "Port4_A5",
        "Port4_A4",
        "Port4_A3",
        "Port4_A2",
        "Port4_A1",
        "Port4_A0",
        "RF_TX1_CTL4",

        "P3_R_CTL6",
        "RX2_P3_CTL2",
        "P3_B_CTL7",
        "RX2_S_CTL",
        "P3_T_CTL3",
        "Reversed",
        "TX2_P3_CTL2",
        "P3_S1_CTL2",

        "P3_R_CTL4",
        "Port3_A5",
        "Port3_A4",
        "Port3_A3",
        "Port3_A2",
        "Port3_A1",
        "Port3_A0",
        "TP6",
    },

    // shift3
    {},

    // shift4
    {},

    // shift5
    // No use
    {},

    // 42553
    {
        "P1_T_CTL5",
        "P2_T_CTL5",
        "P3_T_CTL5",
        "P4_T_CTL5",
    },

    // SW PA
    {},

    //PAC
    {
        "P1_S_FPGA_CTL1",
        "P2_S_FPGA_CTL1",
        "P3_S_FPGA_CTL1",
        "P4_S_FPGA_CTL1",
    }
};

/////////////////////////////////////////////////////////////////////////
// 428 VA_VB开关板定义
/////////////////////////////////////////////////////////////////////////

const string SwitchCfg::WT428SwitchBitName_428VA_VB[] = {
    // shift3
    "RF_RX1_CTL2",
    "RF_RX1_CTL4",
    "RF_RX1_CTL1",
    "RF_RX_CTL5",
    "RF_RX2_CTL8",
    "RF_RX2_CTL9",
    "RF_RX2_CTL6",
    "RF_RX2_CTL7",
    "Port1_A0",
    "Port1_A1",
    "Port1_A2",
    "Port1_A3",
    "Port1_A4",
    "Port1_A5",
    "P1_S_CTL1",
    "RF_RX1_CTL3",
    "P1_R_CTL4",
    "TX1_P1_CTL1",
    "P1_RX_CTL=P1_RF_CTL",    //降成本版与广播2版共用
    "P1_B_CTL7",
    "RX1_CTL_P12",
    "RX1_P1_CTL1",
    "RX1_P1_CTL2",
    "P1_R_CTL6",
    "TX1_P1_CTL2",
    "TX1_CTL_P12",
    "RX1_P2_CTL2",
    "P2_R_CTL6",
    "P2_R_CTL4",
    "P2_S_CTL1",
    "P1_S1_CTL2",
    "P1_T_CTL3",

    // shift4
    "P3_R_CTL6",
    "TX1_P3_CTL2",
    "TX1_P3_CTL1",
    "P3_RX_CTL=P3_RF_CTL",    //降成本版与广播2版共用
    "P3_B_CTL7",
    "RX1_CTL_P34",
    "RX1_P3_CTL1",
    "RX1_P3_CTL2",
    "Port4_A0",
    "Port4_A1",
    "Port4_A2",
    "Port4_A3",
    "Port4_A4",
    "Port4_A5",
    "P3_S1_CTL2",
    "P3_T_CTL3",
    "P4_S_CTL1",
    "P3_TX_CTL=P4_RF_CTL",    //降成本版与广播2版共用
    "P4_B_CTL7",
    "TX1_CTL_P34",
    "RX1_P4_CTL1",
    "RX1_P4_CTL2",
    "P4_R_CTL6",
    "P4_R_CTL4",
    "TX1_P4_CTL1",
    "P4_S1_CTL2",
    "P4_T_CTL3",
    "TX1_P4_CTL2",
    //"Reserved",
    "P4_RX_CTL",
    "P2_RX_CTL",
    "P4_TX_CTL",


    // shift5
    "Port3_A0",
    "Port3_A1",
    "Port3_A2",
    "Port3_A3",
    "Port3_A4",
    "Port3_A5",
    "P2_TX_CTL=RF_TX1_CTL",    //降成本版与广播2版共用
    "TX1_CTL_S",
    "P2_B_CTL7",
    "P3_R_CTL4",
    "P3_S_CTL1",
    "P2_S1_CTL2",
    "P2_T_CTL3",
    "TX1_P2_CTL2",
    "TX1_P2_CTL1",
    "P1_TX_CTL=P2_RF_CTL",    //降成本版与广播2版共用
    "Port2_A0",
    "Port2_A1",
    "Port2_A2",
    "Port2_A3",
    "Port2_A4",
    "Port2_A5",
    "RX1_S_CTL",
    "RX1_P2_CTL1",

    // shift0
    "RF_TX2_CTL8",
    "RF_TX2_CTL7",
    "RF_TX1_CTL2",
    "RF_TX1_CTL1",
    "RF_TX1_CTL4",
    "RF_TX1_CTL3",
    "RF_TX_CTL5",
    "RF_TX2_CTL6",
    "P8_B_CTL7",
    "RF_TX2_CTL9",
    " P8_TX_CTL",
    "P8_S1_CTL2",
    "P8_T_CTL3",
    "TX2_P8_CTL2",
    "P8_RX_CTL=P8_RF_CTL",    //降成本版与广播2版共用
    "TX2_P8_CTL1",
    "P7_S1_CTL2",
    "P7_TX_CTL",
    "RX2_P8_CTL1",
    "RX2_P8_CTL2",
    "P8_R_CTL6",
    "P8_R_CTL4",
    "P8_S_CTL1",
    "P7_T_CTL3",
    "Port8_A0",
    "Port8_A1",
    "Port8_A2",
    "Port8_A3",
    "Port8_A4",
    "Port8_A5",
    "TX2_CTL_P78",
    "P7_RX_CTL=RF_TX2_CTL",    //降成本版与广播2版共用

    // shift1
    "P7_R_CTL6",
    "P7_RF_CTL",
    "TX2_P7_CTL2",
    "TX2_P7_CTL1",
    "P7_B_CTL7",
    "RX2_CTL_P78",
    "RX2_P7_CTL1",
    "RX2_P7_CTL2",
    "Port7_A0",
    "Port7_A1",
    "Port7_A2",
    "Port7_A3",
    "Port7_A4",
    "Port7_A5",
    "P7_R_CTL4",
    "P7_S_CTL1",
    "RX2_P6_CTL1",
    "P6_S1_CTL2",
    "P6_T_CTL3",
    "TX2_P6_CTL2",
    "TX2_P6_CTL1",
    "P6_TX_CTL=P6_RF_CTL",    //降成本版与广播2版共用
    "P6_B_CTL7",
    "RX2_S_CTL",
    "P5_T_CTL3",
    "RX2_P6_CTL2",
    "P6_R_CTL6",
    "P6_R_CTL4",
    "P6_S_CTL1",
    "TX2_CTL_S",
    "TX2_CTL_P56",
    "P5_S1_CTL2",

    // shift2
    "Port6_A0",
    "Port6_A1",
    "Port6_A2",
    "Port6_A3",
    "Port6_A4",
    "Port6_A5",
    "P5_TX_CTL",
    "P6_RX_CTL",
    "P5_R_CTL6",
    "TX2_P5_CTL2",
    "TX2_P5_CTL1",
    "P5_RX_CTL=P5_RF_CTL",    //降成本版与广播2版共用
    "P5_B_CTL7",
    "RX2_CTL_P56",
    "RX2_P5_CTL1",
    "RX2_P5_CTL2",
    "Port5_A0",
    "Port5_A1",
    "Port5_A2",
    "Port5_A3",
    "Port5_A4",
    "Port5_A5",
    "P5_R_CTL4",
    "P5_S_CTL1",

    // 42553 SW1
    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",

    // 42553 SW2
    "P5_T_CTL5",
    "P6_T_CTL5",
    "P7_T_CTL5",
    "P8_T_CTL5",
};

const string SwitchCfg::SwitchBitName_428VA_VB[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH] = {
    // shift0
    {
        "RF_TX2_CTL8",
        "RF_TX2_CTL7",
        "RF_TX1_CTL2",
        "RF_TX1_CTL1",
        "RF_TX1_CTL4",
        "RF_TX1_CTL3",
        "RF_TX_CTL5",
        "RF_TX2_CTL6",
        "P8_B_CTL7",
        "RF_TX2_CTL9",
        " P8_TX_CTL",
        "P8_S1_CTL2",
        "P8_T_CTL3",
        "TX2_P8_CTL2",
        "P8_RX_CTL=P8_RF_CTL",    //降成本版与广播2版共用
        "TX2_P8_CTL1",
        "P7_S1_CTL2",
        "P7_TX_CTL",
        "RX2_P8_CTL1",
        "RX2_P8_CTL2",
        "P8_R_CTL6",
        "P8_R_CTL4",
        "P8_S_CTL1",
        "P7_T_CTL3",
        "Port8_A0",
        "Port8_A1",
        "Port8_A2",
        "Port8_A3",
        "Port8_A4",
        "Port8_A5",
        "TX2_CTL_P78",
        "P7_RX_CTL=RF_TX2_CTL",    //降成本版与广播2版共用
    },

    // shift1
    {
        "P7_R_CTL6",
        "P7_RF_CTL",
        "TX2_P7_CTL2",
        "TX2_P7_CTL1",
        "P7_B_CTL7",
        "RX2_CTL_P78",
        "RX2_P7_CTL1",
        "RX2_P7_CTL2",
        "Port7_A0",
        "Port7_A1",
        "Port7_A2",
        "Port7_A3",
        "Port7_A4",
        "Port7_A5",
        "P7_R_CTL4",
        "P7_S_CTL1",
        "RX2_P6_CTL1",
        "P6_S1_CTL2",
        "P6_T_CTL3",
        "TX2_P6_CTL2",
        "TX2_P6_CTL1",
        "P6_TX_CTL=P6_RF_CTL",    //降成本版与广播2版共用
        "P6_B_CTL7",
        "RX2_S_CTL",
        "P5_T_CTL3",
        "RX2_P6_CTL2",
        "P6_R_CTL6",
        "P6_R_CTL4",
        "P6_S_CTL1",
        "TX2_CTL_S",
        "TX2_CTL_P56",
        "P5_S1_CTL2",
    },

    // shift2
    {
        "Port6_A0",
        "Port6_A1",
        "Port6_A2",
        "Port6_A3",
        "Port6_A4",
        "Port6_A5",
        "P5_TX_CTL",
        "P6_RX_CTL",
        "P5_R_CTL6",
        "TX2_P5_CTL2",
        "TX2_P5_CTL1",
        "P5_RX_CTL=P5_RF_CTL",    //降成本版与广播2版共用
        "P5_B_CTL7",
        "RX2_CTL_P56",
        "RX2_P5_CTL1",
        "RX2_P5_CTL2",
        "Port5_A0",
        "Port5_A1",
        "Port5_A2",
        "Port5_A3",
        "Port5_A4",
        "Port5_A5",
        "P5_R_CTL4",
        "P5_S_CTL1",
    },

    // shift3
    {
        "RF_RX1_CTL2",
        "RF_RX1_CTL4",
        "RF_RX1_CTL1",
        "RF_RX_CTL5",
        "RF_RX2_CTL8",
        "RF_RX2_CTL9",
        "RF_RX2_CTL6",
        "RF_RX2_CTL7",
        "Port1_A0",
        "Port1_A1",
        "Port1_A2",
        "Port1_A3",
        "Port1_A4",
        "Port1_A5",
        "P1_S_CTL1",
        "RF_RX1_CTL3",
        "P1_R_CTL4",
        "TX1_P1_CTL1",
        "P1_RX_CTL=P1_RF_CTL",    //降成本版与广播2版共用
        "P1_B_CTL7",
        "RX1_CTL_P12",
        "RX1_P1_CTL1",
        "RX1_P1_CTL2",
        "P1_R_CTL6",
        "TX1_P1_CTL2",
        "TX1_CTL_P12",
        "RX1_P2_CTL2",
        "P2_R_CTL6",
        "P2_R_CTL4",
        "P2_S_CTL1",
        "P1_S1_CTL2",
        "P1_T_CTL3",
    },

    // shift4
    {
        "P3_R_CTL6",
        "TX1_P3_CTL2",
        "TX1_P3_CTL1",
        "P3_RX_CTL=P3_RF_CTL",    //降成本版与广播2版共用
        "P3_B_CTL7",
        "RX1_CTL_P34",
        "RX1_P3_CTL1",
        "RX1_P3_CTL2",
        "Port4_A0",
        "Port4_A1",
        "Port4_A2",
        "Port4_A3",
        "Port4_A4",
        "Port4_A5",
        "P3_S1_CTL2",
        "P3_T_CTL3",
        "P4_S_CTL1",
        "P3_TX_CTL=P4_RF_CTL",    //降成本版与广播2版共用
        "P4_B_CTL7",
        "TX1_CTL_P34",
        "RX1_P4_CTL1",
        "RX1_P4_CTL2",
        "P4_R_CTL6",
        "P4_R_CTL4",
        "TX1_P4_CTL1",
        "P4_S1_CTL2",
        "P4_T_CTL3",
        "TX1_P4_CTL2",
        "Reserved",
        "P4_RX_CTL",
        "P2_RX_CTL",
        "P4_TX_CTL",
    },

    // shift5
    {
        // shift5
        "Port3_A0",
        "Port3_A1",
        "Port3_A2",
        "Port3_A3",
        "Port3_A4",
        "Port3_A5",
        "P2_TX_CTL=RF_TX1_CTL",    //降成本版与广播2版共用
        "TX1_CTL_S",
        "P2_B_CTL7",
        "P3_R_CTL4",
        "P3_S_CTL1",
        "P2_S1_CTL2",
        "P2_T_CTL3",
        "TX1_P2_CTL2",
        "TX1_P2_CTL1",
        "P1_TX_CTL=P2_RF_CTL",    //降成本版与广播2版共用
        "Port2_A0",
        "Port2_A1",
        "Port2_A2",
        "Port2_A3",
        "Port2_A4",
        "Port2_A5",
        "RX1_S_CTL",
        "RX1_P2_CTL1",
    },

    // 42553 SW1
    {
        "P1_T_CTL5",
        "P2_T_CTL5",
        "P3_T_CTL5",
        "P4_T_CTL5",
    },

    // 42553 SW2
    {
        "P5_T_CTL5",
        "P6_T_CTL5",
        "P7_T_CTL5",
        "P8_T_CTL5",
    },
};

const string SwitchCfg::WT418SwitchBitName_VA[] = {
    // RX
    "RX_S_CTL",
    "RX_M1_CTL",
    "RX_M2_CTL",
    "RX_12_CTL",
    "RX_34_CTL",
    "RX_56_CTL",
    "RX_78_CTL",

    "P1_R_CTL10",
    "P2_R_CTL10",
    "P3_R_CTL10",
    "P4_R_CTL10",
    "P5_R_CTL10",
    "P6_R_CTL10",
    "P7_R_CTL10",
    "P8_R_CTL10",

    "P1_R_CTL8",
    "P2_R_CTL8",
    "P3_R_CTL8",
    "P4_R_CTL8",
    "P5_R_CTL8",
    "P6_R_CTL8",
    "P7_R_CTL8",
    "P8_R_CTL8",

    "P1_R_CTL6",
    "P2_R_CTL6",
    "P3_R_CTL6",
    "P4_R_CTL6",
    "P5_R_CTL6",
    "P6_R_CTL6",
    "P7_R_CTL6",
    "P8_R_CTL6",

    "P1_R_CTL4",
    "P2_R_CTL4",
    "P3_R_CTL4",
    "P4_R_CTL4",
    "P5_R_CTL4",
    "P6_R_CTL4",
    "P7_R_CTL4",
    "P8_R_CTL4",
    //TX
    "P1_T_CTL12",
    "P2_T_CTL12",
    "P3_T_CTL12",
    "P4_T_CTL12",
    "P5_T_CTL12",
    "P6_T_CTL12",
    "P7_T_CTL12",
    "P8_T_CTL12",

    "P1_T_CTL11",
    "P2_T_CTL11",
    "P3_T_CTL11",
    "P4_T_CTL11",
    "P5_T_CTL11",
    "P6_T_CTL11",
    "P7_T_CTL11",
    "P8_T_CTL11",

    "P1_T_CTL9",
    "P2_T_CTL9",
    "P3_T_CTL9",
    "P4_T_CTL9",
    "P5_T_CTL9",
    "P6_T_CTL9",
    "P7_T_CTL9",
    "P8_T_CTL9",

    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",
    "P5_T_CTL5",
    "P6_T_CTL5",
    "P7_T_CTL5",
    "P8_T_CTL5",

    "P1_T_CTL3",
    "P2_T_CTL3",
    "P3_T_CTL3",
    "P4_T_CTL3",
    "P5_T_CTL3",
    "P6_T_CTL3",
    "P7_T_CTL3",
    "P8_T_CTL3",

    // BETWEEN
    "P1_B_CTL7",
    "P2_B_CTL7",
    "P3_B_CTL7",
    "P4_B_CTL7",
    "P5_B_CTL7",
    "P6_B_CTL7",
    "P7_B_CTL7",
    "P8_B_CTL7",

    "P1_S1_CTL2",
    "P2_S1_CTL2",
    "P3_S1_CTL2",
    "P4_S1_CTL2",
    "P5_S1_CTL2",
    "P6_S1_CTL2",
    "P7_S1_CTL2",
    "P8_S1_CTL2",

    "P1_S_CTL1",
    "P2_S_CTL1",
    "P3_S_CTL1",
    "P4_S_CTL1",
    "P5_S_CTL1",
    "P6_S_CTL1",
    "P7_S_CTL1",
    "P8_S_CTL1",
};

const string SwitchCfg::WT428CSwitchBitName_VA[] = {
    // RX
    "RX_S_CTL",
    "RX_M1_CTL",
    "RX_M2_CTL",
    "RX_12_CTL",
    "RX_34_CTL",
    "RX_56_CTL",
    "RX_78_CTL",

    "P1_R_CTL10",
    "P2_R_CTL10",
    "P3_R_CTL10",
    "P4_R_CTL10",
    "P5_R_CTL10",
    "P6_R_CTL10",
    "P7_R_CTL10",
    "P8_R_CTL10",

    "P1_R_CTL8",
    "P2_R_CTL8",
    "P3_R_CTL8",
    "P4_R_CTL8",
    "P5_R_CTL8",
    "P6_R_CTL8",
    "P7_R_CTL8",
    "P8_R_CTL8",

    "P1_R_CTL6",
    "P2_R_CTL6",
    "P3_R_CTL6",
    "P4_R_CTL6",
    "P5_R_CTL6",
    "P6_R_CTL6",
    "P7_R_CTL6",
    "P8_R_CTL6",

    //TX
    "P1_T_CTL11",
    "P2_T_CTL11",
    "P3_T_CTL11",
    "P4_T_CTL11",
    "P5_T_CTL11",
    "P6_T_CTL11",
    "P7_T_CTL11",
    "P8_T_CTL11",

    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",
    "P5_T_CTL5",
    "P6_T_CTL5",
    "P7_T_CTL5",
    "P8_T_CTL5",

    "P1_T_CTL3",
    "P2_T_CTL3",
    "P3_T_CTL3",
    "P4_T_CTL3",
    "P5_T_CTL3",
    "P6_T_CTL3",
    "P7_T_CTL3",
    "P8_T_CTL3",

    // BETWEEN
    "P1_B_CTL7",
    "P2_B_CTL7",
    "P3_B_CTL7",
    "P4_B_CTL7",
    "P5_B_CTL7",
    "P6_B_CTL7",
    "P7_B_CTL7",
    "P8_B_CTL7",

    "P1_S1_CTL2",
    "P2_S1_CTL2",
    "P3_S1_CTL2",
    "P4_S1_CTL2",
    "P5_S1_CTL2",
    "P6_S1_CTL2",
    "P7_S1_CTL2",
    "P8_S1_CTL2"
};

const string SwitchCfg::SwitchBitName_418_VA[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH] = {
    // shift0
    {
        /*shift0分高位32和低位8共40bits
        "Port1_A5",
        "Port1_A4",
        "Port1_A3",
        "Port1_A2",
        "Port1_A1",
        "Port1_A0",
        "P1_S_CTL1",
        "P1_S1_CTL2",*/
        "Reverse",
        "RX_12_CTL",
        "RX_34_CTL",
        "RX_56_CTL",
        "RX_78_CTL",
        "RX_M1_CTL",
        "RX_M2_CTL",
        "RX_S_CTL",
        "P1_R_CTL4",
        "P1_R_CTL6",
        "P1_R_CTL8",
        "P1_R_CTL10",
        "P1_T_CTL11",
        "P1_T_CTL12",
        "P1_B_CTL7",
        "P1_T_CTL9",
        "Port2_A5",
        "Port2_A4",
        "Port2_A3",
        "Port2_A2",
        "Port2_A1",
        "Port2_A0",
        "P2_S_CTL1",
        "P2_S1_CTL2",
        "P2_R_CTL4",
        "P2_R_CTL6",
        "P2_R_CTL8",
        "P2_R_CTL10",
        "P2_T_CTL11",
        "P2_T_CTL12",
        "P2_B_CTL7",
        "P2_T_CTL9",
    },
    {
        "Port1_A5",
        "Port1_A4",
        "Port1_A3",
        "Port1_A2",
        "Port1_A1",
        "Port1_A0",
        "P1_S_CTL1",
        "P1_S1_CTL2",
    },
    // shift1
    {
        "P4_T_CTL9",
        "P4_B_CTL7",
        "P4_T_CTL12",
        "P4_T_CTL11",
        "P4_R_CTL10",
        "P4_R_CTL8",
        "P4_R_CTL6",
        "P4_R_CTL4",
        "P4_S1_CTL2",
        "P4_S_CTL1",
        "Port4_A0",
        "Port4_A1",
        "Port4_A2",
        "Port4_A3",
        "Port4_A4",
        "Port4_A5",
        "P3_T_CTL9",
        "P3_B_CTL7",
        "P3_T_CTL12",
        "P3_T_CTL11",
        "P3_R_CTL10",
        "P3_R_CTL8",
        "P3_R_CTL6",
        "P3_R_CTL4",
        "P3_S1_CTL2",
        "P3_S_CTL1",
        "Port3_A0",
        "Port3_A1",
        "Port3_A2",
        "Port3_A3",
        "Port3_A4",
        "Port3_A5",
    },
    // shift2
    {
        "P6_T_CTL9",
        "P6_B_CTL7",
        "P6_T_CTL12",
        "P6_T_CTL11",
        "P6_R_CTL10",
        "P6_R_CTL8",
        "P6_R_CTL6",
        "P6_R_CTL4",
        "P6_S1_CTL2",
        "P6_S_CTL1",
        "Port6_A0",
        "Port6_A1",
        "Port6_A2",
        "Port6_A3",
        "Port6_A4",
        "Port6_A5",
        "P5_T_CTL9",
        "P5_B_CTL7",
        "P5_T_CTL12",
        "P5_T_CTL11",
        "P5_R_CTL10",
        "P5_R_CTL8",
        "P5_R_CTL6",
        "P5_R_CTL4",
        "P5_S1_CTL2",
        "P5_S_CTL1",
        "Port5_A0",
        "Port5_A1",
        "Port5_A2",
        "Port5_A3",
        "Port5_A4",
        "Port5_A5",
    },
    // shift3
    {
        "Port7_A5",
        "Port7_A4",
        "Port7_A3",
        "Port7_A2",
        "Port7_A1",
        "Port7_A0",
        "P7_S_CTL1",
        "P7_S1_CTL2",
        "P7_R_CTL4",
        "P7_R_CTL6",
        "P7_R_CTL8",
        "P7_R_CTL10",
        "P7_T_CTL11",
        "P7_T_CTL12",
        "P7_B_CTL7",
        "P7_T_CTL9",
        "Port8_A5",
        "Port8_A4",
        "Port8_A3",
        "Port8_A2",
        "Port8_A1",
        "Port8_A0",
        "P8_S_CTL1",
        "P8_S1_CTL2",
        "P8_R_CTL4",
        "P8_R_CTL6",
        "P8_R_CTL8",
        "P8_R_CTL10",
        "P8_T_CTL11",
        "P8_T_CTL12",
        "P8_B_CTL7",
        "P8_T_CTL9",
    },

    //PAC
    {
        "P1_T_CTL3",
        "P2_T_CTL3",
        "P3_T_CTL3",
        "P4_T_CTL3",
        "P5_T_CTL3",
        "P6_T_CTL3",
        "P7_T_CTL3",
        "P8_T_CTL3",
    },

    //Loop
    {
        "P1_T_CTL5",
        "P2_T_CTL5",
        "P3_T_CTL5",
        "P4_T_CTL5",
        "P5_T_CTL5",
        "P6_T_CTL5",
        "P7_T_CTL5",
        "P8_T_CTL5",
    },
};
const string SwitchCfg::SwitchBitName_428C_VD[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH] = {
    // shift0 控ATT
    {

    },

    // shift1 控ATT
    {

    },

    // shift2 32bit
    {
        /*"Reverse",
        "RX_78_CTL",
        "RX_56_CTL",
        "RX_12_CTL",
        "RX_34_CTL",
        "P1_R_CTL8",
        "P1_R_CTL10",
        "P1_R_CTL6",*/

        "Reverse",
        "Reverse",
        "P2_R_CTL10",
        "P2_R_CTL8",
        "P2_R_CTL6",
        "RX_S_CTL",
        "RX_M1_CTL",
        "RX_M2_CTL",

        "Reverse",
        "Reverse",
        "P4_R_CTL8",
        "P4_R_CTL10",
        "P4_R_CTL6",
        "P3_R_CTL8",
        "P3_R_CTL10",
        "P3_R_CTL6",

        "Reverse",
        "Reverse",
        "P6_R_CTL8",
        "P6_R_CTL10",
        "P6_R_CTL6",
        "P5_R_CTL8",
        "P5_R_CTL10",
        "P5_R_CTL6",

        "Reverse",
        "Reverse",
        "P8_R_CTL8",
        "P8_R_CTL10",
        "P8_R_CTL6",
        "P7_R_CTL8",
        "P7_R_CTL10",
        "P7_R_CTL6",
    },

    // shift3 8bit
    {
        "Reverse",
        "RX_78_CTL",
        "RX_56_CTL",
        "RX_12_CTL",
        "RX_34_CTL",
        "P1_R_CTL8",
        "P1_R_CTL10",
        "P1_R_CTL6",
    },

    // shift4 ATT
    {

    },

    // shift5 ATT
    {

    },

    // shift6
    {
        "Reverse",
        "Reverse",
        "P7_B_CTL7",
        "P7_T_CTL11",
        "P7_S1_CTL2",
        "P8_B_CTL7",
        "P8_T_CTL11",
        "P8_S1_CTL2",

        "Reverse",
        "Reverse",
        "P5_B_CTL7",
        "P5_T_CTL11",
        "P5_S1_CTL2",
        "P6_B_CTL7",
        "P6_T_CTL11",
        "P6_S1_CTL2",

        "Reverse",
        "Reverse",
        "P3_B_CTL7",
        "P3_T_CTL11",
        "P3_S1_CTL2",
        "P4_B_CTL7",
        "P4_T_CTL11",
        "P4_S1_CTL2",

        "Reverse",
        "Reverse",
        "P1_B_CTL7",
        "P1_T_CTL11",
        "P1_S1_CTL2",
        "P2_S1_CTL2",
        "P2_T_CTL11",
        "P2_B_CTL7",
    },

    //PAC
    {
        "P1_T_CTL3",
        "P2_T_CTL3",
        "P3_T_CTL3",
        "P4_T_CTL3",
        "P5_T_CTL3",
        "P6_T_CTL3",
        "P7_T_CTL3",
        "P8_T_CTL3",
    },

    //Loop
    {
        "P1_T_CTL5",
        "P2_T_CTL5",
        "P3_T_CTL5",
        "P4_T_CTL5",
        "P5_T_CTL5",
        "P6_T_CTL5",
        "P7_T_CTL5",
        "P8_T_CTL5",
    },
};