#ifndef ALG_3GPP_VSGDEF_GSM_H_
#define ALG_3GPP_VSGDEF_GSM_H_

#include "alg_3gpp_apidef.h"

#define ALG_GSM_MAX_SLOT_NUM         8

/* GSM burst Type */
#define ALG_GSM_NB                   0
#define ALG_GSM_AB                   1
#define ALG_GSM_SB                   2
#define ALG_GSM_FCB                  3
#define ALG_GSM_DUMMY                4

/* Modulation Type */
#define ALG_GSM_GMSK                 0
#define ALG_GSM_QPSK                 1
#define ALG_GSM_8PSK                 2
#define ALG_GSM_16QAM                3
#define ALG_GSM_32QAM                4

#define ALG_GSM_FCB_STANDARD         0
#define ALG_GSM_FCB_COMPACT          1

/**************************************************************************************************/
/*                                   WaveGenerate Configurate Start                               */
/**************************************************************************************************/
typedef struct {
    int DataType;

    /* # NB */
    int ModType;
    int RateType;
    int UseStealFlag;
    int FlagValue;
    int TscSet;
    int Tsc;
    int Reserved[15];
} Alg_GSM_NormalBurstType;

typedef struct {
    /* # AB */
    int DataType;
    int Synch;
    int Reserved[14];
} Alg_GSM_AccessBurstType;

typedef struct {
    /* # SB */
    int DataType;
    int Etsc;
    int Reserved[14];
} Alg_GSM_SyncBurstType;

typedef struct {
    /* # FCB */
    int Fixed;
    int Reserved[15];
} Alg_GSM_FreqBurstType;

typedef struct {
    int BurstType;
    int SlotLevel;
    double SlotAttenuation;
    int SlotRepition;
    int RepSlotNum;
    int Reserved[15];

    union {
        Alg_GSM_NormalBurstType NB[2];
        Alg_GSM_AccessBurstType AB;
        Alg_GSM_SyncBurstType SB;
        Alg_GSM_FreqBurstType FCB;
    };
} Alg_GSM_SlotType;

typedef struct {
    double FilterParameter;
    
    int Reserved[16];
} Alg_GSM_FilterType;

typedef struct {
    double RampTime;
    double RiseDelay;
    double FallDelay;
    
    int Reserved[16];
} Alg_GSM_PowRampType;

typedef struct {
    int SequenceMode;
    int SymbRateMode;
    int GenSequenceLen;
    int SlotTimeMode;
    int Reserved[4];

    Alg_GSM_SlotType Slot[ALG_GSM_MAX_SLOT_NUM];

    Alg_GSM_FilterType Filter;

    Alg_GSM_PowRampType PowerRamp;

} Alg_GSM_WaveGenType;

/**************************************************************************************************/
/*                                   WaveGenerate Configurate End                                 */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        VSA Configurate Start                                   */
/**************************************************************************************************/
typedef struct {
    int OffsetState[20];
    double FreqOffset[20];
    int Reserved[16];
} Alg_GSM_SpectModInType;

typedef struct {
    int OffsetState[20];
    double FreqOffset[20];
    int Reserved[16];
} Alg_GSM_SpectSwtInType;

typedef struct {
    int Current;
    int Average;
    int Max;
    double LimitValue;
} Alg_GSM_ModuAllType;

typedef struct {
    Alg_GSM_ModuAllType EvmRms;
    Alg_GSM_ModuAllType EvmPeak;
    Alg_3GPP_BaseLimitType Evm95Percent;
    Alg_GSM_ModuAllType MErrRms;
    Alg_GSM_ModuAllType MErrPeak;
    Alg_3GPP_BaseLimitType MErr95Percent;
    Alg_GSM_ModuAllType PhErrRms;
    Alg_GSM_ModuAllType PhErrPeak;
    Alg_3GPP_BaseLimitType PhErr95Percent;
    Alg_GSM_ModuAllType IQOffset;
    Alg_GSM_ModuAllType IQImbalance;
    Alg_GSM_ModuAllType FreError;
    Alg_GSM_ModuAllType TimeError;
} Alg_GSM_ModulateLimitType;

typedef struct {
    int State;
    int FromPCL;
    int ToPCL;
    double Lower;
    double Upper;
} Alg_GSM_PvTAvgBurstPowerType;

typedef struct {
    double Time;
    double LevelRel;
    Alg_3GPP_BaseLimitType LevelAbs;
} Alg_GSM_PvTAvgStaticEdgeType;

typedef struct {
    Alg_GSM_PvTAvgStaticEdgeType Start;
    Alg_GSM_PvTAvgStaticEdgeType Stop;
} Alg_GSM_PvTAvgStaticType;

typedef struct {
    int State;
    int StartPCL;
    int EndPCL;
    double Correction;
} Alg_GSM_PvTAvgDynamicType;

typedef struct {
    int State;
    Alg_GSM_PvTAvgStaticType StaticLimt;
    Alg_GSM_PvTAvgDynamicType DynamicLimt[5];
} Alg_GSM_PvTAvgAreType;

typedef struct {
    Alg_GSM_PvTAvgAreType RiseEdgeLimit[4];
    Alg_GSM_PvTAvgAreType UsefulPartLimit[3];
    Alg_GSM_PvTAvgAreType FallEdgeLimit[4];
} Alg_GSM_PvTUpperTemType;

typedef struct {
    Alg_GSM_PvTAvgAreType UsefulPartLimit[5];
} Alg_GSM_PvTLowerTemType;

typedef struct {
    Alg_GSM_PvTAvgBurstPowerType AvgLimit[10];
    Alg_3GPP_BaseLimitType GuardPeriod;
    Alg_GSM_PvTUpperTemType UpperTemLimit[3];
    Alg_GSM_PvTLowerTemType LowerTemlimit[3];
} Alg_GSM_PowerVsTimeLimitType;

typedef struct {
    double LowPwr;
    double HighPwr;
} Alg_GSM_SpectModRefPwrType;

typedef struct {
    int State;
    double LowPwrRel;
    double HighPwrRel;
    double AbsPwr;
} Alg_GSM_SpectModFreOffsetType;

typedef struct {
    Alg_GSM_SpectModRefPwrType RefPwrLimit;
    Alg_GSM_SpectModFreOffsetType FreOffsetLimit[20];
} Alg_GSM_SpectModulationType;

typedef struct {
    int State;
    double LimitValue[10];
} Alg_GSM_SpectSwiFreOffsetType;

typedef struct {
    Alg_3GPP_BaseLimitType RefPower[10];
    Alg_GSM_SpectSwiFreOffsetType FreOffsetLimit[20];
} Alg_GSM_SpectSwitchType;

typedef struct {
    Alg_GSM_ModulateLimitType ModLimit[3];
    Alg_GSM_PowerVsTimeLimitType PVTLimit;
    Alg_GSM_SpectModulationType SpecModLimit[3];
    Alg_GSM_SpectSwitchType SpecSwiLimit[3];
} Alg_3GPP_LimitInGSM;

typedef struct {
    int SlotOffset;
    int NumbOfSlot;
    int MeasureSlot;
    int PvTFilter;
    int Reserved[4];

    Alg_GSM_SpectModInType SpectMod;

    Alg_GSM_SpectSwtInType SpectSwt;

    Alg_3GPP_LimitInGSM LimitInfo;
} Alg_3GPP_AlzInGSM;
/**************************************************************************************************/
/*                                         VSA Configurate End                                    */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                           VSA Output Start                                     */
/**************************************************************************************************/
typedef struct {
    int UpMaskNum;
    Complex UpMask[24];
    int DownMaskNum;
    Complex DownMask[24];
    double XMark[2];
    double AvgPwr;
    int BurstType;
    int TSCType;
} Alg_GSM_BurstPwrInfo;

typedef struct {
    int InfoFlg;
    
    int MeasBurstNum;

    int PowerNum;
    double *Symb; /* X-axis */
    double *Power;/* Y-axis */

    double MeasPartMin;
    double MeasPartMax;
    double MeasBurstPwr;
    double MeasSV[12];

    Alg_GSM_BurstPwrInfo Burst[8];

    int TestResult;
} Alg_GSM_PowerOutInfo;

typedef struct {
    int InfoFlg;

    int PointEvmLen;
    Complex *PointEvm;

    int SymbLen;
    Complex *SymbEvm;

    double EvmRms;
    double EvmPeak;
    double Evm95th;
} Alg_GSM_EvmOutInfo;

typedef struct {
    int InfoFlg;

    int ErrLen;
    Complex *MagnErr;
    Complex *PhaseErr;

    double MagnErrRms;
    double MagnErrPeak;
    double MagnErr95th;

    double PhaseErrRms;
    double PhaseErrPeak;
    double PhaseErr95th;
} Alg_GSM_MPErrInfo;

typedef struct {
    int InfoFlg;

    Complex Freq[41]; /* X-axis */
    double Mask[41]; /* Y-axis */
    double Power[41];/* Y-axis */

    int TimeNum;
    double *Symb; /* X-axis */
    double *Time; /* Y-axis */

    double RefPwr;
    int TestResult; /* PASS/FAIL */
} Alg_GSM_SpectResultInfo;

typedef struct {
    /* Power vs. Time Results */
    Alg_GSM_PowerOutInfo Power;

    Alg_GSM_EvmOutInfo Evm;

    Alg_GSM_MPErrInfo MPErr;

    Alg_GSM_SpectResultInfo SpectMod;

    Alg_GSM_SpectResultInfo SpectSwt;

    double IqOffset;
    double IqImba;
    double FreqErr;
    double TimingErr;

    int BurstType;
    char DemodSymb[142];
    int BitNum;
    char DemodBit[568];
} Alg_3GPP_AlzOutGSM;
/**************************************************************************************************/
/*                                            VSA Output End                                      */
/**************************************************************************************************/

#endif /* ALG_3GPP_VSGDEF_GSM_H_ */