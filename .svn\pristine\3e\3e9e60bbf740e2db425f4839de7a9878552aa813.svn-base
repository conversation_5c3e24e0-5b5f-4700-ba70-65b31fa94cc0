
#ifndef __SCPI_CONFIG_H_
#define __SCPI_CONFIG_H_
#include <stdio.h>


#define LIBSCPI_API extern __attribute__((visibility("default")))


#ifdef	__cplusplus
extern "C" {
#endif

#ifdef _MSC_VER
#define strcasecmp stricmp
#define strncasecmp  strnicmp
#endif


#define LINE_ENDING_CR          "\r"
#define LINE_ENDING_LF          "\n"
#define LINE_ENDING_CRLF        "\r\n"	


#define SCPI_LINE_ENDING        LINE_ENDING_CRLF

#define USE_FULL_ERROR_LIST 0

#define USE_USER_ERROR_LIST 0

#define SCPI_INPUT_BUFFER_LENGTH (16*1024*1024)

#ifdef	__cplusplus
}
#endif

#endif
