//*****************************************************************************
//  File: devmgr.cpp
//  设备资源管理
//  Data: 2016.8.8
//  采用先到先得的方式申请资源，先申请的先获取，避免出现饿死现象
//*****************************************************************************
#include "devmgr.h"
#include <signal.h>
#include <sys/time.h>
#include <cstring>
#include <arpa/inet.h>

#include "conf.h"
#include "wterror.h"
#include "wtlog.h"
#include "devlib.h"
#include "license.h"
#include "userconnmgr.h"

using namespace std;

int DevMgr::m_ServerID = 1;

void DevMgr::SetSrvID(int Id)
{
    m_ServerID = Id;
}

DevMgr &DevMgr::Instance(void)
{
    static DevMgr Mgr;
    return Mgr;
}

DevMgr::DevMgr()
{
    int Ret = WT_OK;
    WTConf::DevCfg Cfg;

    //从配置文件中获取子仪器的配置信息并初始化资源
    Ret = BaseConf::Instance().GetDevCfg(m_ServerID, Cfg);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get sub dev cfg failed");

        //获取配置失败后采用默认配置
        Cfg.PortMask = 0x3;
        Cfg.VsaMask = 0x1;
        Cfg.VsgMask = 0x1;
    }

    int i = 0;
    while (Cfg.VsaMask)
    {
        if (Cfg.VsaMask & (1 << i))
        {
            if (License::Instance().CheckVSAUnit(i) == WT_OK)
            {
                m_VsaMods.Mods.push_back(ModInfo(DEV_RES_VSA, i)); //硬件单元编号从1开始
                m_VsaMods.FreeNum += 1;
            }

            Cfg.VsaMask &= ~(unsigned)(1 << i);
        }

        i++;
    }

    i = 0;
    while (Cfg.VsgMask)
    {
        if (Cfg.VsgMask & (1 << i))
        {
            if (License::Instance().CheckVSGUnit(i) == WT_OK)
            {
                m_VsgMods.Mods.push_back(ModInfo(DEV_RES_VSG, i)); //硬件单元编号从1开始
                m_VsgMods.FreeNum += 1;
            }

            Cfg.VsgMask &= ~(unsigned)(1 << i);
        }

        i++;
    }

    for (i = 0; i < WT_RFPORT_NUM; i++)
    {
        m_Ports[i].LedOnTime = 0;
        m_Ports[i].LedOffTime = 0;
    }
}

int DevMgr::AllocMod(int Type, const NotifyFunc &Notify, int Timeout, unsigned long long Key, int &DevId, int SpecId)
{
    int Ret, Index = 0;
    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;

    if (SpecId == -1)
    {
        Ret = DynamicAlloc(Dev, Timeout, Index);
    }
    else
    {
        Ret = SpecAlloc(Dev, SpecId, Timeout, Index);
    }

    if (Ret == WT_OK)
    {
        Dev.Mods[Index].Notify = Notify;
        Dev.Mods[Index].Key = Key;
        DevId = Dev.Mods[Index].DevId;
    }
    else if (Ret == WT_ALLOC_MOD_TIMEOUT)
    {
        PrintDevOwner(Type, Key, SpecId);
    }
    return Ret;
}

// 判断是否用户已经取消等待申请资源
#define IsUserCancel() (UserConnMgr::Instance().IsCurThreadNeedStopSrcWaitting(std::this_thread::get_id()))

// 必须按照申请顺序获取模块资源。由于同时存在多个模块资源，所以在排队时头部的几个申请者都能获取资源，
// 这几个可以不按照顺序获取，因为这几个之间实质上没有竞争关系。
// 但是waithead索引只能由处于头部的申请者更新，不然头部的申请者可能长时间申请不到资源，而是被其后的申请者先获取到了。
int DevMgr::DynamicAlloc(Resources &Dev, int Timeout, int &ModIndex)
{
    bool Got = false;
    int Ret = WT_OK;
    auto WaitTime = chrono::milliseconds(Timeout * 1000);

    // start time
    // using std::chrono::system_clock;
    // system_clock::time_point start = system_clock::now();

    unique_lock<mutex> Lock(Dev.Mutex);
    unsigned int Index = Dev.WaitTail++;

    // Lambda函数
    auto WaitDevOrUserCancel = [&Dev, Index]() {
        return ((Dev.FreeNum > Index - Dev.WaitHead) || (IsUserCancel()));
    };

    // Lambda函数
    auto WaitUserCancel = []() {
        return IsUserCancel();
    };

    do
    {
        // 等待有足够的空闲单元, 或者用户取消
        if (!Dev.Cond.wait_for(Lock, WaitTime, WaitDevOrUserCancel))
        {
            // wait_for超时, 说明一直没有单元释放,而且用户没有取消
            Ret = WT_ALLOC_MOD_TIMEOUT;
            break;
        }
        else // 存在空闲单元 或者 用户取消
        {
            if (IsUserCancel()) // 用户取消流程
            {
                Ret = WT_CANCEL_ALLOC_MOD;
                break;
            }
            else // 存在空闲单元流程
            {
                ModIndex = 0;
                for (auto &Mod : Dev.Mods)
                {
                    if (Mod.Free && (Mod.WaitHead == Mod.WaitTail))
                    {
                        // 分配到资源
                        Got = true;
                        Mod.Free = false;
                        Dev.FreeNum--;
                        break;
                    }

                    ModIndex++;
                }

                // 如果没有获取到模块说明空闲的模块都是被指定使用的，需要继续等待。
                // 此处不continue而是直接等待是因为此时 Dev.FreeNum <= Index - Dev.WaitHead 条件一定不满足，
                // 所以直接continue会一直进行死循环。
                // 这段是根据原先代码的逻辑修改的, 有个疑问: 既然分配不到单元为什么一定要继续等下去呢?
                if (!Got)
                {
                    // TODO 这里没有必要等待WaitTime, 应该减掉已经花过的时间.
                    //system_clock::time_point now = system_clock::now();

                    //auto WaitTime2 = now - start;
                    //if (WaitTime2 > WaitTime)
                    //{
                    //    Ret = WT_ALLOC_MOD_TIMEOUT;
                    //    break;
                    //}

                    //WaitTime2 = WaitTime + start - now;

                    //if (Dev.Cond.wait_for(Lock, WaitTime, WaitUserCancel) == cv_status::timeout)
                    if (!Dev.Cond.wait_for(Lock, WaitTime, WaitUserCancel))
                    {
                        // WaitTime超时
                        Ret = WT_ALLOC_MOD_TIMEOUT;
                        break;
                    }
                    else
                    {
                        // 用户取消
                        Ret = WT_CANCEL_ALLOC_MOD;
                        break;
                    }
                }
            }
        }
    } while (!Got);

    if (Index == Dev.WaitHead) // 队列头部用户
    {
        ++Dev.WaitHead;

        // 循环检查下一个用户是否在过期ID Set里
        set<int>::iterator iter = Dev.OverdueSet.begin();
        while ((iter = Dev.OverdueSet.find(Dev.WaitHead)) != Dev.OverdueSet.end())
        {
            Dev.OverdueSet.erase(Dev.WaitHead);
            ++Dev.WaitHead;
        }
    }
    else // 队列中间用户, 则放置到集合中去
    {
        Dev.OverdueSet.insert(Index);
    }

#if 0
    // 这里AllocNum++是干什么呢? 
    // 如果两个线程同时申请到单元，两个线程都会执行Dev.AllocNum++;
    // 但是这两个线程的Index肯定是不一样的, 这里代码的逻辑是让前面的线程(Index == Dev.WaitHead)
    // 来修改Dev.WaitHead的值.
    // 问题点, 何不直接: Dev.WaitHead++ 呢?
    // 答案揭晓, 如果存在2个资源, 0,1两个用户排队，1如果先走了流程，这时候的情况是Dev.WaitHead为0，index为1, 如果它将Dev.WaitHead++了，则对0用户可能产生影响.
    Dev.AllocNum++;
    if (Index == Dev.WaitHead)
    {
        Dev.WaitHead += Dev.AllocNum;
        Dev.AllocNum = 0;
    }
#endif

    return Ret;
}

int DevMgr::SpecAlloc(Resources &Dev, int SpecId, int Timeout, int &ModIndex)
{
    int Ret = WT_OK;
    int i = 0;
    for (i = 0; i < Dev.Mods.size(); i++)
    {
        if (Dev.Mods[i].DevId == SpecId)
        {
            break;
        }
    }

    if (i == Dev.Mods.size())
    {
        WTLog::Instance().LOGERR(WT_SPEC_DEVID_ERROR, "SpecId not exist");
        return WT_SPEC_DEVID_ERROR;
    }

    ModIndex = i;
    auto &Mod = Dev.Mods[i];
    auto WaitTime = chrono::milliseconds(Timeout * 1000);
    unique_lock<mutex> Lock(Dev.Mutex);

    unsigned int Index = Mod.WaitTail++;
    // Lambda函数
    auto WaitDevOrUserCancel = [&Mod, Index]
    {
        return ((Mod.Free && Index == Mod.WaitHead) || (IsUserCancel()));
    };

    // 模块处于空闲状态，且当前请求处于队列头部方可使用该模块
    // c++11语法要点, wait_for未返回之前会将自动将Lock释放, 返回前会自动获取锁
    if (!Dev.Cond.wait_for(Lock, WaitTime, WaitDevOrUserCancel))
    {
        Ret = WT_ALLOC_MOD_TIMEOUT;
    }
    else // 存在空闲资源 或者 用户取消
    {
        if (IsUserCancel())
        {
            Ret = WT_CANCEL_ALLOC_MOD;
        }
        else
        {
            Mod.Free = false;
            Dev.FreeNum--;
        }
    }

    if (Index == Mod.WaitHead) // 队列头部用户
    {
        ++Mod.WaitHead;

        // 循环检查下一个用户是否在过期ID Set里
        set<int>::iterator iter = Mod.OverdueSet.begin();
        while ((iter = Mod.OverdueSet.find(Mod.WaitHead)) != Mod.OverdueSet.end())
        {
            Mod.OverdueSet.erase(Mod.WaitHead);
            ++Mod.WaitHead;
        }
    }
    else // 队列中间用户, 则放置到集合中去
    {
        Mod.OverdueSet.insert(Index);
    }

    return Ret;
}

int DevMgr::IsAllocating(int Type, int SpecId)
{
    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;
    int i = 0;
    for (i = 0; i < Dev.Mods.size(); i++)
    {
        if (Dev.Mods[i].DevId == SpecId)
        {
            break;
        }
    }

    if (i == Dev.Mods.size() || Dev.Mods[i].WaitHead == Dev.Mods[i].WaitTail)
    {
        return false;
    }
    else
    {
        return true;
    }
}

int DevMgr::FreeMod(int Type, int DevId)
{
    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;
    lock_guard<mutex> Lock(Dev.Mutex);

    for (auto &Item : Dev.Mods)
    {
        if (Item.DevId == DevId)
        {
            if (!Item.Free)
            {
                //清除驱动缓存工作模式，避免配置背板工作模式时，误配为master
                DevLib::Instance().ClearWorkMode(DevId, static_cast<WT_DEV_TYPE>(Type)); 
                Item.Free = true;
                Dev.FreeNum += 1;
                Dev.Cond.notify_all(); //唤醒在等待的申请
            }

            break;
        }
    }

    return WT_OK;
} //lint !e550

int DevMgr::GetModNum(int Type)
{
    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;

    return Dev.Mods.size();
} //lint !e550

void DevMgr::NotifyFreeResources(int FreeType, const NotifyFunc &Notify)
{
    Resources &Dev = (FreeType == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;

    //检查VSA的状态
    for (const auto &Item : Dev.Mods)
    {
        if (!Item.Free)
        {
            if (Item.Notify && (&Item.Notify != &Notify))
            {
                Item.Notify(FreeType, 0, WT_RX_TX_STATE_NEED_FREE);
                break;
            }
        }
    }
}

void DevMgr::SetHwCb(const wtev::loop_ref &loop)
{
    m_SigEv.set(loop);
    m_SigEv.set<DevMgr, &DevMgr::HwOpFin>(this);
    m_SigEv.start(SIGIO);

    m_LedEv.set(loop);
    m_LedEv.set<DevMgr, &DevMgr::CheckLed>(this);
    m_LedEv.start(0, 0.5);
}

void DevMgr::HwOpFin(wtev::sig &watcher, int revents)
{
    (void)watcher;
    (void)revents;

    int Ret = WT_OK, Status = 0;
    unique_lock<mutex> VsaLock(m_VsaMods.Mutex);
    //检查VSA的状态
    for (const auto &Item : m_VsaMods.Mods)
    {
        if (!Item.Free)
        {
            Ret = DevLib::Instance().GetCompleteClrStatus(Item.DevId, DEV_TYPE_VSA, Status);
            if (Ret == WT_OK && (Status & 0x80000000))
            {
                if (Item.Notify)
                {
                    Item.Notify(Item.Type, Item.DevId, Status & ~(0x1u << 31));
                }
            }
        }
    }
    VsaLock.unlock();

    unique_lock<mutex> VsgLock(m_VsgMods.Mutex);
    //检查VSG的状态
    for (const auto &Item : m_VsgMods.Mods)
    {
        if (!Item.Free)
        {
            Ret = DevLib::Instance().GetCompleteClrStatus(Item.DevId, DEV_TYPE_VSG, Status);
            if (Ret == WT_OK && (Status & 0x80000000))
            {
                if (Item.Notify)
                {
                    Item.Notify(Item.Type, Item.DevId, Status & ~(0x1u << 31));
                }
            }
        }
    }
    VsgLock.unlock();
}

void DevMgr::HwOpFinMod(int Type, int ModId)
{
    int Ret = WT_OK, Status = 0;
    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;
    unique_lock<mutex> Lock(Type == DEV_RES_VSA ? m_VsaMods.Mutex : m_VsgMods.Mutex);

    //检查状态
    for (const auto &Item : Dev.Mods)
    {
        if (Item.DevId == ModId)
        {
            if (!Item.Free)
            {
                Ret = DevLib::Instance().GetCompleteClrStatus(Item.DevId, static_cast<WT_DEV_TYPE>(Type), Status);
                if (Ret == WT_OK && (Status & 0x80000000))
                {
                    if (Item.Notify)
                    {
                        Item.Notify(Item.Type, Item.DevId, Status & ~(0x1u << 31));
                    }
                }
            }
            break;
        }
    }
    Lock.unlock();
}

void DevMgr::CheckLed(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;

    struct timeval Val;
    gettimeofday(&Val, nullptr);

    for (int i = 0; i < WT_RFPORT_NUM; i++)
    {
        unique_lock<mutex> Lock(m_Ports[i].PortMutex);
        if (m_Ports[i].LedOnTime == 0 || m_Ports[i].LedOnTime > m_Ports[i].LedOffTime)
        {
            continue;
        }

        //LED亮灯超时则关闭
        if (Val.tv_sec * 1e6 + Val.tv_usec >= m_Ports[i].LedOnTime + LED_LAST_TIME_US)
        {
            DevLib::Instance().SetLedStatus((WT_LED_INDEX_E)(i + 1), LED_STATUS_OFF);
            m_Ports[i].LedOnTime = 0;
        }
    }
}

int DevMgr::PortLedOn(int Type, int Port)
{
    if (Port <= 0)
    {
        char buff[128] = { 0 };
        sprintf(buff, "PortLedOn Port must be > 0, cur Port=%d, type=%d", Port, Type);
        WTLog::Instance().LOGERR(WT_ARG_ERROR, buff);
        return WT_ARG_ERROR;
    }

    int Ret = WT_OK;
    int Index = Port - 1;
    std::thread::id tid = std::this_thread::get_id();

    struct timeval Val;
    gettimeofday(&Val, nullptr);

    do
    {
        unique_lock<mutex> Lock(m_Ports[Index].PortMutex);
        if(m_Ports[Index].LedOnTime == 0||m_Ports[Index].PortUserId != tid)
        {
            Ret = DevLib::Instance().SetLedStatus((WT_LED_INDEX_E)Port, Type == DEV_RES_VSA ? LED_VSA_ON : LED_VSG_ON);
        }

    } while (0);

    if (Ret == WT_OK)
    {
        m_Ports[Index].LedOnTime = Val.tv_sec * 1e6 + Val.tv_usec;
    }
    else
    {
        m_Ports[Index].LedOnTime = 0;
    }
    m_Ports[Index].PortUserId = tid;
    return Ret;
}

int DevMgr::PortLedOff(int Port, bool Now)
{
    if (Port <= 0)
    {
        char buff[128] = { 0 };
        sprintf(buff, "PortLedOff Port must be > 0, cur Port=%d", Port);
        WTLog::Instance().LOGERR(WT_ARG_ERROR, buff);
        return WT_ARG_ERROR;
    }
    int Index = Port - 1;

    struct timeval Val;
    gettimeofday(&Val, nullptr);

    unique_lock<mutex> Lock(m_Ports[Index].PortMutex);
    if (m_Ports[Index].PortUserId != std::this_thread::get_id())
    {
        return WT_OK;
    }

    if (m_Ports[Index].LedOnTime)
    {
        if (!Now) //记录需要关闭的时间
        {
            m_Ports[Index].LedOffTime = Val.tv_sec * 1e6 + Val.tv_usec;
        }
        else //立即关闭
        {
            DevLib::Instance().SetLedStatus((WT_LED_INDEX_E)Port, LED_STATUS_OFF);
            m_Ports[Index].LedOnTime = 0;
        }
    }

    return WT_OK;
}

int DevMgr::NotifyAllThread()
{
    m_VsaMods.Cond.notify_all(); //唤醒在等待的申请
    m_VsgMods.Cond.notify_all(); //唤醒在等待的申请
    return WT_OK;
}

// 理论上是需要加锁访问的
bool DevMgr::QueryExistWait(int Type, int DevId)
{
    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;

    // 查看动态分配
    if (Dev.WaitHead != Dev.WaitTail)
    {
        return true;
    }

    // 查看指定分配
    for (auto &Mod : Dev.Mods)
    {
        if (Mod.DevId == DevId)
        {
            if (Mod.WaitHead != Mod.WaitTail)
            {
                return true;
            }

            break;
        }
    }

    return false;
}

void DevMgr::PrintDevOwner(int Type, unsigned long long Key, int DevId)
{
    char buff[256] = {0};
    char *str = buff;

    if(Type == DEV_RES_VSA)
    {
        sprintf(str, "VSA Alloc Mod %d failed.\n", DevId);
    }
    else
    {
        sprintf(str, "VSG Alloc Mod %d failed.\n", DevId);
    }
    str += strlen(str);

    in_addr AddrApplicant;
    AddrApplicant.s_addr = (Key & 0xFFFFFFFF);
    sprintf(str, "Applicant is ip:%s, port:%lld\n", inet_ntoa(AddrApplicant), ((Key >> 32) & 0xFFFF));
    str += strlen(str);

    Resources &Dev = (Type == DEV_RES_VSA) ? m_VsaMods : m_VsgMods;
    for (int Index = 0; Index < Dev.Mods.size(); ++Index)
    {
        if (Dev.Mods[Index].Free == false && (DevId == -1 || Dev.Mods[Index].DevId == DevId))
        {
            in_addr AddrOwner;

            AddrOwner.s_addr = Dev.Mods[Index].Key & 0xFFFFFFFF;

            sprintf(str, "Cur Mod%d owner is ip:%s, port:%lld\n", Dev.Mods[Index].DevId, inet_ntoa(AddrOwner), ((Dev.Mods[Index].Key >> 32) & 0xFFFF));
            str += strlen(str);
        }
    }
    WTLog::Instance().LOG_LEVEL(Key == 181 ? LOG_SUB_MGR : LOG_ERROR, WT_ALLOC_MOD_TIMEOUT, buff);
}

int DevMgr::ATTCalOpFin(wtev::sig &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    WTLog::Instance().WriteLog(LOG_DEBUG, "=====ProcATTCalFin\n");
    return WT_OK;
}