/*#################################################################

                          基本类型定义模块

###################################################################
Name: StructuresDef.h
Creat: FanZhichao
Date : 2012-09-24
Dscp :
*******************************************************************************/

#ifndef __StructuresDef_H__
#define __StructuresDef_H__

#define MAX_11AX_USER    74
#define AX_RU_COUNT     MAX_11AX_USER
#define MUMIMO_8_USER 8
#define MAX_SEGMENT 2
#define MAX_SEGMENTBE 4
#define AC_MUMIMO_4_USER 4
#define AH_MUMIMO_4_USER 4

#define MAX_MPDU_COUNT 64

//#define MAX_STREAM_COUNT 8
#ifdef _SISO_MODE
#define MAX_STREAM_COUNT 1
#else
	#ifdef _WT418
	#define MAX_STREAM_COUNT 1
	#else
		#ifdef _WT428
		#define MAX_STREAM_COUNT 4
		#else
		#define MAX_STREAM_COUNT 8
		#endif
	#endif
#endif

#define MAX_TAP_NUM 18
#define MAX_CLUSTER_NUM 6

/* 11BA define */
#define MAX_SEGMENT_11BA_NUM 4

#define ALG_11AZ_MAX_USER_NUM   MAX_11AX_USER
#define AZ_RU_COUNT     AX_RU_COUNT

#ifndef Complex_Def
#define Complex_Def
typedef double  Complex[2];
#endif
#include "alg_3gpp_vsgdef.h"
/* IQ数据的结构*/
typedef struct
{
	double dReal;   /*实部*/
	double dImag;   /*虚部*/
} stPNDat;


typedef struct stIQDat_
{

	short s16Real;  /*实部*/
	short s16Imag;  /*虚部*/

} stIQDat;


typedef struct stReg_
{
	unsigned int u32Addr;   /*寄存器地址*/
	unsigned int u32Dat;    /*寄存器数据*/
} stReg;


/* 设备信息 */
typedef struct stDeviceInfo_TAG
{
	char ip[16];                        // IP地址
	char SubMask[16];                   // 子网掩码
	char GateAddr[16];                  // 网关
	char SN[80];                        // SN码
	char name[40];                      // 别名     "XGIGA Tester"
	char MacAddr[18];                   // MAC 地址 "DC-A4-B5-C7-E1-F8"
	char FW_Version[40];                // 固件版本
	char HW_Version[40];                // 硬件版本
	char RF_Version[40];                // 射频板版本
	char cal_date[20];                  // 校准日期 "2012-05-04 18:36:56"

} stDeviceInfo;

typedef struct Power_Offset_Tag
{
	double freq;                            // 频点,MHz
	double offset;                          // 补偿值,dB

	struct Power_Offset_Tag *pre;
	struct Power_Offset_Tag *next;
} PowerOffset;

typedef struct Psdu_Set_Tag
{
    char *UserDefinedPSDU;
    int PSDUType;
    int PSDULen;
    int WaveGap;
    int Scrambler;
    int CRCEnable;
    int ExtraTxAntennaNo;
    int MACHeaderEnable;
	unsigned char FrameCtrlByte[2];
	unsigned char Duration[2];
	unsigned char Sequence[2];
    unsigned char MACAddress1[6];
    unsigned char MACAddress2[6];
    unsigned char MACAddress3[6];
    unsigned char MACAddress4[6];
} stPsduSet;

typedef struct Wave_Set_Tag
{
    int Standard;               //射频标准
    int SignalType;             //速率
    int Bandwidth;
    unsigned int DACRate;       //Dac采样速率
    int PacketType;             //包类型(11n)
    int STBC;                   //STBC
    int StreamsCnt;             //数据流数量(11n、11ac、11ax)
    int SoundingPacket;         //Sounding
    int Smoothing;				//11N
    int Aggregation;
	int CodingType;
    int GuardInterval;
    int MPDUCount;
    int MPDUElementCnt[64];
    int McsValid;
    int Mcs;
    int Preamble;               //长短前导
    int Fir_11b_22M;            //11b信号滤波
    int Flag_8080M;             //8080标志位
    int ClockRate;  
    double FreqErr;             //频偏，ppm
    double IQImbalanceAmp;
    double IQImbalancePhase;
    double DCOffset_I;
    double DCOffset_Q;	
} stWaveSet;

typedef struct
{
    int TriggerType;
    int	TBLength;
    int	MoreTF;
    int	CSRequired;
    int	TBULBW;
    int	TBGILTF;
    int	TBMMMode;
    int	TBLTFSym;
    int	TBSTBC;
    int	TBLDPCExtra;
    int	APTxPower;
    int TBAfactor;
    int	TBPE;
    int mPad;
    int TBSR[4];
    int	TBDoppler;
    int	TBUserNum;
    int	TBAID[MAX_11AX_USER];
    int TBRUIndex[MAX_11AX_USER];
    int TBSegment[MAX_11AX_USER];
    int TBCoding[MAX_11AX_USER];
    int TBMCS[MAX_11AX_USER];
    int TBDCM[MAX_11AX_USER];
    int TBSSStart[MAX_11AX_USER];
    int TBSSCount[MAX_11AX_USER];
    int TBRSSI[MAX_11AX_USER];
    int TBSpacingFactor[MAX_11AX_USER];
    int	TBAggLimit[MAX_11AX_USER];
    int	TBPreAC[MAX_11AX_USER];
    int	TBMultiplexing[MAX_11AX_USER];
    int TBRxTxMap[MAX_11AX_USER];
    int TBBARInfoLen;
    unsigned char 	TBBARControl[4];
    unsigned char 	TBBARInfo[4];
    int TBMidamble_Periodicity;
    int PE_Disambiguity;
    int ExpectedTBType;

    int TriggerAPType; // 0: non-EHT HE AP  1: EHT AP
    /* EHT common info added */
    int HE_EHT_P160;    // primary 160M or not, value: 0/1
    int SpecialUserInfoFieldFlag; // whether special user info is exist, value: 0/1
    /* EHT user info added */
    int PS160[MAX_11AX_USER];
    /* Special user info added */
    int TBULBWExt; // 0/1/2/3
    int TBEHTSR[2]; // EHT Spatial Reuse
    int Res;

    double Reserved[214];   //����λ
} TriggerFrameSetting;

typedef struct Packet_Set_Tag
{
	//*************BlueTooth 输入设置项定义 **************//
	//Header format
	int LT_ADDR;                    //  3bit  LT_ADDR：3bit，取值0~7
	int PackType;                       //  4bit  TYPE: 分组包类型：目前主要支持DH1，DH3，DH5,2DH1,2DH3,2DH5,3DH1,3DH3,3DH5等
	int Flow;                       //  1bit  FLOW: FLOW_Control：1bit，取值0~1
	int ARQN;                       //  1bit  ARQN: ACK Indication：1bit，取值0~1
	int SEQN;                       //  1bit  SEQN: Seq Number Inder：1bit，取值0~1

	//Payload
	int LLID;                       // Logical Link Identifier(LLID):取值0~3 :默认：1
	int DataWhitening;              // 数据是否需要加噪（加噪值为1，不加噪值为0，默认不加噪：0）
	int payLoadType;                //在BLE测试的时候指定负载的类型（PackType选在LE_TEST时候有效）
	int mFlow;                      //Flow取值：0,1

	//PHY Parameters
	double FreqDeviation;           // Freq Deviation：115kHz-300kHz，默认160kHz
	double ModuIdex;                //Modulation Index：取值：0.01~0.99(只支持两位小数)   由原来的FreqDeviation字段修改
	int BTProductIdex;              //Filter BT Product 取值：0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9
	int RolloffIdex;                //EDR Filter Rolloff  取值：0.3，0.35,0.40,0.45,0.50
	int GuardTime;                  //EDR Guard Time us;IQXEL（0~10000us，只能取整数）

	//Device Address Part
	unsigned int LAP;                        // LAP(Lower Address Part)：24bit 默认：0x000000
	unsigned char  UAP;                        // UAP(Upper Address Part)：8bit 默认：0x47
	unsigned int NAP;                        // NAP(Non-significant Address Part)：16bit 默认：0x0000

	//Packet Ramp Time
	double PowerRampTime;           //us 默认：2us
	double PowerSettlingTime;       //us 默认：4us

	//LE packetType
	int LE_PayloadType;				//Payload type 0~7
	unsigned int LE_SyncWord;	    //32bit 默认取值94826e8e(最左边为低位 如A=1010)

	int BTHeadZeroCount;            //BT信号头部添零
	int BTAddZeroCount;             //BT信号尾部添零

	int BLEMapperS;        //只对enPacketType_LE_Coded_Test有效，取值2或者8
} stPacketSet;

typedef struct Wave_Set_Tag_ZWave
{
	//******************Z-Wave 输入设置项定义 **************//
	//stWaveSet包含项Standard、DACRate、FreqErr
//	int Standard;               //射频标准
//	unsigned int DACRate;       //DAC采样速率
//	double FreqErr;             //频偏，ppm
	int SignalType;		        //R1=1/R2=2/R3=3
	int ZWave_PSDULen;			//PSDU长度，单位Byte
	int CRCEnable;

	//zwave特有输入设置项
	int ZWave_PSDUType;	       //Random/ALL0/ALL1/ALT01/PN9/PN15/user
	char Payload_UserTypeCode; //8位2进制数 ,ZWave_PSDUType=user有效
	char Preamble_Byte;        //preamble Byte数量
	char SOF_bit;              //0或者1 0为00001111 1为11110000

	//payload 头信息 以10进制数传入
	unsigned int HomeID;
	int SourceNodeID;
	int FrameControl;
	int DestinationNodeID;

	int DataWhitening;
}stWaveSetZWave;

typedef struct Generator_Set_Tag
{
	stPsduSet PsduSet;
	stWaveSet WaveSet;
	stPacketSet PacketSet;
	stWaveSetZWave WaveSetZWave;
} stGeneratorSet;

#define OFFSET_POINT_COUNT 200

typedef struct SpectrumOffset_Tag
{
	int valid;
	double firstOffset[OFFSET_POINT_COUNT];
	double lastOffset[OFFSET_POINT_COUNT];
} stSpectrumOffset;

#define SKIP_SYMBOLS_COUNT 20

enum CODING
{
	BCC,
	LDPC
};

enum WT_EXT_PARAM_ENUM
{
    WT_EXT_PARAM_PSDU = 1,                  ///< PSDU扩展参数
    WT_EXT_PARAM_PHY_LSIG = 2,              ///< LSIG字段自定义
    WT_EXT_PARAM_PHY_HTSIG = 3,             ///< HT-SIG字段自定义
    WT_EXT_PARAM_PHY_SIGA = 4,              ///< SIGA字段自定义
    WT_EXT_PARAM_PHY_VHT_SIGB = 5,          ///< VHT SIGB字段自定义
    WT_EXT_PARAM_PHY_HE_SIGB = 6,           ///< HE SIGB字段自定义
    WT_EXT_PARAM_PHY_EHT_SIGB = 7,          ///< EHT SIGB字段自定义
    WT_EXT_PARAM_PHY_EHT_USIG = 8,          ///< EHT U-SIG字段自定义
    WT_EXT_PARAM_PHY_HE_POST_FEC_PADDING = 9,   ///< HE Post FEC padding字段自定义
    WT_EXT_PARAM_PHY_EHT_POST_FEC_PADDING = 10  ///< EHT Post FEC padding字段自定义
};

enum VSG_OUT_EXT_DATA_ENUM
{
    VSG_OUT_EXT_DATA_RU_CARRIER_INFO = 1,          ///< RU载波分配信息
    VSG_OUT_EXT_DATA_CHANNEL_MODEL_PATHLOSS = 2,   ///< 信道模型线衰
	VSG_OUT_EXT_DATA_CBF_REPORT_FIELD = 3,         ///< CBF report field data
};

typedef struct
{
    int Type;                               ///< 扩展数据类型
    int Len;                                ///< 扩展数据总长度，单位bytes
    union
    {
        void *Next;
        int Word[2];
    }Field;                                      ///< 指向下一个ExtParamHdr
    int Reserved[30];                       ///< 保留位
}ExtParamHdr;

typedef struct
{
    int SegmentID;                          ///< 段ID号
    int RUID;                               ///< RU ID号
    int UserID;                             ///< RU下的User ID号
    int DataLen;                            ///< 数据长度，单位bytes
    unsigned char *Data;                               ///< 数据
}UserDefinedPSDU;

typedef UserDefinedPSDU UserDefinedPostFECPadding;

typedef struct
{
    int TotalLen;                           ///< TotalLen之后数据总长度，不包括自身int类型4字节
    int Reserved[31];                       ///< 保留位
}VsgOutExtDataHdr;

typedef struct
{
    int Type;                               ///< 输出数据类型
    int Len;                                ///< 输出数据长度，单位bytes
    int Data;                               ///< 数据
}VsgOutExtDataStru;

typedef struct
{
    int NSSLen;                             //0表示对于的NSS数据无效
    char subcarrier[4096];                  //每个NSS的子载波信息， 0表示无效
    int Reserved[127];                      //保留位
}RUCarrierInfo;

typedef struct CmimoReferenceHeaderInfo_Tag
{
	int standard;
	int bandwidth;
	int modulationCodingScheme;
	int coding;                      //enum CODING
	int numPattens;
	int numSpatialStreams;
	int numSpaceTimeStreams;
	int numSymbols;
	int numTones;
	int skipSymbols[SKIP_SYMBOLS_COUNT];
	int validSkipSymbolsCount;
} CmimoReferenceHeaderInfo;

typedef struct
{
	int	   ModelType;                 //0:无 1-6分别对应A-F
	double CarrierFrequency;          //默认5250M，值400 ~ 7125，单位MHz
	double Speed;                     //默认1.5，0 ~ 10，单位km/h
	double TxRxDistance;              //默认3，0 ~ 100，单位m
	double PowerLineFrequency;	      //默认60, 50，60，单位Hz
	int    LargeScaleFadingEffect;    //默认3，0-No 1-Pashloss 2-Shade 3-Pashloss and Shade

	int    FluorescentEffect;         //(Model D/E才有) 默认1-ON，0-OFF
	double GaussianStD;               //(Model D/E才有) 默认0.0107, 范围0~1
	double GaussianMean;              //(Model D/E才有) 默认0.0203, 范围-1~1
	int    Direction;                 //默认0-DownLink 1-UpLink
	int    RandSeed;                  //默认200,取值0~4095, 0为由系统时间构造的随机种子
	double TxAntSpacing;              //默认0.5	取值0~1
	double RxAntSpacing;              //默认0.5	取值0~1

	int    TapNum;
	int    ClusterNum;
	int    PathDelays[MAX_TAP_NUM];                       //范围0~1500，单位ns
	double PowerPerAngle[MAX_CLUSTER_NUM][MAX_TAP_NUM];   //范围0~-50，单位dB
	double RxAoADegrees[MAX_CLUSTER_NUM];                 //范围0~360，单位度
	double RxASDegrees[MAX_CLUSTER_NUM];                  //范围0~360，单位度
	double TxAoDDegrees[MAX_CLUSTER_NUM];                 //范围0~360，单位度
	double TxASDegrees[MAX_CLUSTER_NUM];                  //范围0~360，单位度
	double Dbp;                                           //范围0~100，单位米
	double SlopeBeforeDbp;                                //范围0~100，单位米
	double SlopeAfterDbp;                                 //范围0~100，单位米，必须大于SlopeBeforeDbp
	double ShadowfadingBeforeDbp;                         //范围0~50，单位dB
	double ShadowfadingAfterDbp;                          //范围0~50，单位dB，必须大于ShadowfadingBeforeDbp

	int Resvered[127];
}WlanChannelModelsParam;

typedef struct
{
    int psduLen;                            //psdu长度
    int psduType;                           //psdu类型. PSDU_TYPE_ENUM
    int scrambler;                          //加扰码
    int CRCCheckEnable;                     //CRC校验使能。1表示Enable，0表示Disable
    int MacHeaderEnable;                    //MAC头使能，决定psdu是否包含MAC。1表示Enable，0表示Disable
    unsigned char FrameControl[2];          //控制模式-用户项. 十六进制表示，格式：0011
    unsigned char DurationID[2];            //控制模式-延迟. 十六进制表示，格式：001A
    unsigned char SequenceControl[2];       //控制模式-序列. 十六进制表示，格式：0A11
    unsigned char MacAddress1[6];           //Mac1.   十六进制表示，格式：11AA22BB33CC
    unsigned char MacAddress2[6];           //Mac2.   十六进制表示，格式：11AA22BB33CC
    unsigned char MacAddress3[6];           //Mac3.   十六进制表示，格式：11AA22BB33CC
    unsigned char MacAddress4[6];           //Mac4.   十六进制表示，格式：11AA22BB33CC
    int MPDUCount;
    int MPDULength[MAX_MPDU_COUNT];
    int FixedRandomSeed;
	int BaseBandTestFlag;                   //0：正常模式，1：没有AMPDU头和CRC
	int EofPaddingType;                     //0：全0，1：AMPDU子帧
    int Reserved[188];    //保留位
} WIFI_PSDU;

typedef struct
{
	int Q_Flag;
	int Q_NTX;
	int Q_Type;
	int QDelay[8];
	Complex MatrValue[8][8];
	int Reserved[128]; //保留位
}MUMIMO_QMat;


typedef struct
{
    int DataRate;                           //11a信号类型. DATARATE_11A_ENUM
    WIFI_PSDU psdu;
    int Reserved[128];    //保留位
} Set11A;

typedef struct
{
    int DataRate;                           //11b信号类型. DATARATE_11B_ENUM
    int Preamble;                           //长短前导. 详细内容见enum PREAMBLE_TYPE
    int Fir_11b_22M;                        //11b信号滤波 1使用滤波器
    WIFI_PSDU psdu;
    int Reserved[128];    //保留位
} Set11B;

typedef struct
{
    int MCS;                                //11n/11ac信号类型
    int htFrmType;                          //11n  GF/MF. HT_FRAME_TYPE_ENUM
    int NSS;                                //空间流数量
    int soundingEnable;                     //加噪使能
    int smoothingEnable;
    int isAggregation;                      //是否开启集合，开启psdu前面会增加4字节的信息
    int isShortGI;                          //是否短GI
    int CodingType;                         //BCC/LDPC
    int STBC;
    WIFI_PSDU psdu;
    int ESS;
    int SoundingNDP;
#ifdef _WT448
	MUMIMO_QMat Qmat;                 ///< Q矩阵
#endif
    int Reserved[126];    //保留位
} Set11N;

typedef struct
{
    int MCS;                                //11n/11ac信号类型
    int NSS;                                //空间流数量
    int isShortGI;                          //是否短GI
    int CodingType;                         //BCC/LDPC
    int STBC;
    WIFI_PSDU psdu;
    int GroupID;                            //0,63 
    int PartialAID;
    int TXOP_PS_NOT_ALLOWED;
    int Beamformed;
    int SoundingNDP;
#ifdef _WT448
	MUMIMO_QMat Qmat;                 ///< Q矩阵
#endif
    int Reserved[123];    //保留位
} Set11AC;


typedef struct
{
    int STBC;                                   //STBC
    int UL_DL;                                  //数据上下行 默认为0
    int MCS;                                    //范围值0~11
    int NSS;                                    //数据流数1~8，Doppler生效时NSS最大为4
    int CodingType;                             //BCC/LDPC,0=BCC,1=LDPC 
    int PE;                                     //(0/1), 0=disable, 1=enable; 默认值为0
    int GILTFSize;                              //11ax的数据类型0~3 .默认值为3
    int Spatial_Reuse[4];
    int BeamChange;                             //(0/1),默认为1
    int BSScolor;                               //BSS color,0 - 63 (initial 63)
    int TXOP;                                   //TXOP Duration,0 - 127 (initial 127)
    int Doppler;                                //Doppler,0 :Disable(initial), 1 : Enable
    int DCM;                                    //指示是否需要双载波调制 0/1。0=disable, 1=enable
    int PE_Type;                                //0=0us, 1=8us, 2=16us; 仅在PE=1时有效
    int Midamble_Periodicity;                   //(1/2)，1=10symbol, 2=20symbol。仅在Doppler=1时有效
    WIFI_PSDU psdu;
    int Beamformed;
    int SoundingNDP;
#ifdef _WT448
	MUMIMO_QMat Qmat_Preamble;                 ///< 前导Q矩阵
#endif
    int Reserved[126];                          //保留位
}Set11AX_SU;

typedef struct
{
    int AID;                     //User的AID号.1 - 2007(initial 1)
    double PowerFact;            //RU功控因子。取值[0.5, 0.707, 1, 1.414, 2]默认值 1
    int CodingType;              //BCC 
    int DCM;                     //指示是否需要双载波调制 0/1。0=disable, 1=enable
    int MCS;                     //范围值0~11
    int NSS;                     //数据流数1~8，Doppler生效时NSS最大为4
    WIFI_PSDU psdu;
    int Beamformed;
    int Reserved[127];           //保留位
}MUMIMO_User;


typedef struct
{
    int UserNum;//取值[0~8]
    MUMIMO_QMat Qmat;
    MUMIMO_User User[MUMIMO_8_USER];
    int Reserved[128]; //保留位
}MUMIMO_RU;

typedef struct
{
    int FullBand;                               //[0~1],1==full band
    int STBC;                                   //STBC
    int UL_DL;                                  //数据上下行 默认为0
    int PE;                                     //(0/1), 0=disable, 1=enable; 默认值为0
    int GILTFSize;                              //11ax的数据类型0~3 .默认值为3
    int BSScolor;                               //BSS color,0 - 63 (initial 63)
    int TXOP;                                   //TXOP Duration,0 - 127 (initial 127)
    int Doppler;                                //Doppler,0 :Disable(initial), 1 : Enable
    int Spatial_Reuse[4];                       //
    int PE_Type;                                //0=0us, 1=8us, 2=16us; 仅在PE=1时有效
    int Midamble_Periodicity;                   //1/2)，1=10symbol, 2=20symbol。仅在Doppler=1时有效

    MUMIMO_RU RU[AX_RU_COUNT];

    int SIGBMCS;                                //(0~5)%指示mcs 值范围0~5
    int SIGBDCM;                                //(0/1) %指示是否用双载波调制
    int SIGB_Compression;                       //(0/1) %指示sigB是否存在，默认为1

    int RuAllocMap[8];                          //11ax的common8bit 以十进制形式传下来
    int RuAllocExtra[2];                        //对应80M和160M独有的设置
    int PuncturingMode;
    int Reserved[127];                          //保留位
} Set11AX_MU;

#define BE_BAND_20M_CNT     16
#define BE_RU_COUNT         144
typedef struct
{
    int FullBand;                               ///< SU和全带宽MU-MIMO是0，OFDMA是1。初始值0
    int STBC;                                   ///< STBC.0=disable, 1=enable; 默认值为0
    int Punctured[BE_BAND_20M_CNT];             ///< 取值范围[0-1]，初始值1; 0=Punctured, 1=No-Punctured
    int RuAllocMap[BE_BAND_20M_CNT];            ///< 11BE的common bit 以十进制形式传下来，每一个代表20M，总共320M
    int SIGBMCS;                                ///< 取值范围[0~5]，默认值2
    int UL_DL;                                  ///< 数据上下行 默认为0
    int Spatial_Reuse[4];                       ///< 取值范围[0~15]，默认值0
    int GILTFSize;                              ///< 取值范围[0~3]，默认值为3。0=4xEHT-LTF + 0.8GI，1=2xHE-LTF + 0.8GI，2=2xHE-LTF + 1.6GI，3=4xHE-LTF + 3.2GI
    int TXOP;                                   ///< TXOP Duration,取值范围[0~127],默认值127
    int BSScolor;                               ///< BSS color,取值范围[0~63]，默认值63
    int Doppler;                                ///< 多普勒开关,0=disable, 1=enable。默认disable
    int Midamble_Periodicity;                   ///< 取值范围[1~2]，1=10symbol, 2=20symbol。仅在Doppler=1时有效
    int PE;                                     ///< 取值范围[0~1]，0=disable, 1=enable; 默认值为disable
    int PE_Type;                                ///< 取值范围[0~2]，0=0us, 1=8us, 2=16us; 仅在PE=1时有效
    MUMIMO_RU RU[BE_RU_COUNT];                  ///< RU配置
    int SoundingNDP;
    int AdditionalEHTLTF;
    int RuAllocDiff80M;
    int channel320Ind;
    int Reserved[124];
}Set11BE_MU;

typedef struct
{
	int AID;                     //User的AID号.1 - 2007(initial 1)
	double PowerFact;            //RU功控因子。取值[0.5, 0.707, 1, 1.414, 2]默认值 1
	int CodingType;              //BCC 
	int DCM;                     //指示是否需要双载波调制 0/1。0=disable, 1=enable
	int MCS;                     //范围值0~11
	int NSS;                     //数据流数1~8，Doppler生效时NSS最大为4
	int NSSStart;
	int SegmentIndex;
	int RuIndex;
	WIFI_PSDU psdu;
	int Ttb_ns;                  //客户定制,针对TB帧单独添加CSD时长,单位ns,建议范围[-750,+750]
	int iReserved;              //结构对齐用，保留位
	double FreqErr;              //客户定制,针对TB帧单独添加的频偏,单位hz,建议范围[-312.5K*4,+312.5K*4]
	double PowerScale;           //客户定制,针对TB帧单独添加的功率系数,范围[0.1，1]
	int Reserved[122];           //保留位
}TB_MUMIMO_User;

typedef struct
{
	int UserNum;//取值[0~8]
	MUMIMO_QMat Qmat;
	TB_MUMIMO_User User[MUMIMO_8_USER];
	int Reserved[128]; //保留位
}TB_MUMIMO_RU;

typedef struct
{
    int Mode;                                   ///< 0 = non NDP mode,1 = Feedback mode。默认值0 
    int StartingAID;                            ///< 取值范围[0-2047]，仅Mode=1时有效
    int MultiplexingFlag;                       ///< 复用开关,0=disable, 1=enable。默认disable。仅Mode=1时有效    
    int FEEDBACK_STATUS;                        ///< 取值范围[0,1]，默认值0，仅Mode=1时有效
    int Reserved[8];
}TB_NDP;

typedef struct
{
    int STBC;
	int NumLTFSymbols;
	int MULTFMode;
	int GILTFSize;                              //11ax的数据类型0~3 .默认值为3
	int Spatial_Reuse[4];                       //
	int TXOP;                                   //TXOP Duration,0 - 127 (initial 127)
	int BSScolor;                               //BSS color,0 - 63 (initial 63)
	int Doppler;                                //Doppler,0 :Disable(initial), 1 : Enable
	int Midamble_Periodicity;                   //(1/2)，1=10symbol, 2=20symbol。仅在Doppler=1时有效
	int PE;                                     //(0/1), 0=disable, 1=enable; 默认值为0
	int PE_Type;                                //0=0us, 1=8us, 2=16us; 仅在PE=1时有效
	int TBMUMIMOFlag;
	int RUNum[MAX_SEGMENT];
	TB_MUMIMO_RU RU[MAX_SEGMENT][AX_RU_COUNT];
	int LDPC_Extra;                             //TBLDPCExtra
	int Pre_FECfactor;                          //TBAfactor
	int PE_Disambiguity;                        //PE_Disambiguity
    TB_NDP NDP;                                 //TB NDP设置
	int UserBuildUpMode;                        //客户定制,针对TB帧单独添加CSD、Freq、powerscale的使能开关，0-关闭  1-打开
	int Reserved[112];                          //保留位
} Set11AX_TB;

typedef struct
{
    int STBC;
	int NumLTFSymbols;
	int MULTFMode;
	int GILTFSize;                              //11ax的数据类型0~3 .默认值为3
	int Spatial_Reuse[4];                       //
	int TXOP;                                   //TXOP Duration,0 - 127 (initial 127)
	int BSScolor;                               //BSS color,0 - 63 (initial 63)
	int Doppler;                                //Doppler,0 :Disable(initial), 1 : Enable
	int Midamble_Periodicity;                   //(1/2)，1=10symbol, 2=20symbol。仅在Doppler=1时有效
	int PE;                                     //(0/1), 0=disable, 1=enable; 默认值为0
	int PE_Type;                                //0=0us, 1=8us, 2=16us; 仅在PE=1时有效
	int TBMUMIMOFlag;
	int RUNum[MAX_SEGMENTBE];
	TB_MUMIMO_RU RU[MAX_SEGMENTBE][AX_RU_COUNT];
	int LDPC_Extra;                             //TBLDPCExtra
	int Pre_FECfactor;                          //TBAfactor
	int PE_Disambiguity;                        //PE_Disambiguity
    TB_NDP NDP;                                 //TB NDP设置
    int SIG1_Disregard;
    int Valid_B2;
    int SIG2_Disregard;
    int channel320Ind;
	int UserBuildUpMode;                        //客户定制,针对TB帧单独添加CSD、Freq、powerscale的使能开关，0-关闭  1-打开
	int Reserved[108];                          //保留位
} Set11BE_TB;

typedef struct
{
    int CodingType;              //BCC/LDPC
    int MCS;                     //范围值0~11
    int NSS;                     //数据流数1~8
    WIFI_PSDU psdu;
    int Reserved[128];           
}AC_MUMIMO_User;
 
typedef struct
{
    int UserNum;                            //1~4
    int GI;
    int GroupID;                            //1~62 
    int TXOP_PS_NOT_ALLOWED;
	MUMIMO_QMat Qmat;
    AC_MUMIMO_User User[AC_MUMIMO_4_USER];
    int Reserved[128];
}Set11AC_MUMIMO;

typedef struct
{
    int DupIndication;
    int NDPMode;
    int CmacNdpBit[37];
    int STBC;
    int UL_DL;
    int IsShortGI;  
    int MCS;                                //11n/11ah信号类型
    int NSS;                                //空间流数量   
    int CodingType;                         //BCC/LDPC
    int BSScolor;
    int PartialAID;
    int Smoothing;
    int Beamformed;
    int Aggregation;
    int ResponseIndication;
    int TravelPilot;
    WIFI_PSDU psdu;    
//#ifdef _WT448
	MUMIMO_QMat Qmat;                 ///< Q矩阵
//#endif
    int Reserved[256];                //保留位
} Set11AH;

typedef struct
{
    int CodingType;              //BCC/LDPC
    int MCS;                     //范围值0~11
    int NSS;                     //数据流数1~4
    WIFI_PSDU psdu;
    int Reserved[128];           
}AH_MUMIMO_User;

typedef struct
{
    int UserNum;                            //1~4
    int IsShortGI;
    int GroupID;                            //1~62 
    int ResponseIndication;
    int TravelPilot;
	MUMIMO_QMat Qmat;
    AH_MUMIMO_User User[AH_MUMIMO_4_USER];
    int Reserved[256];
}Set11AH_MUMIMO;


/* Start 11BA新增 */
typedef struct
{
	int FrameCtrlType;                 //Frame control type, 0~4. 0: WUR Beacon; 1: WUR Wake-up; 2: WUR Vendor Specific; 3: WUR Discovery; 4: WUR Short Wake-up     
	int Protected;                     //0~1; 0: CRC; 1: MIC(完整性保护); fixed 0 at present.
	int IDField;                       //0~0xFFF
	int TypeDependCtrl;                //0~0xFFF
	int Reserved[128];
} MacHeadInfoCfg11Ba;

typedef struct
{
	int SyncExampleIndex;              //Synchronization Example Index, 取值范围1~3;
	int DataExampleIndex;              //Data Example index, 取值范围1~3;
	int DataRateMode;                  //Data Rate Mode. 取值范围0~1; 0：LDR；1：HDR
	int PsduType;                      //Psdu type. 取值范围0~6; 0：USER_DEFINED；1：ALL0；2：ALL1；3：ALT01；4：RANDOM；5：PRBS9；6：PRBS15
	int PsduLen;                       //Psdu length
	MacHeadInfoCfg11Ba  MacHeadCfg;    //Mac head            
    int EnableEmbeddedBSSID;           // 0/1
    int EmbeddedBSSID;                 // Use 0 ~ 15 bit
    int Reserved[126];                 //保留位
} PsduConfigType11Ba;

typedef struct
{
	int PunchMode[MAX_SEGMENT_11BA_NUM];                 //打孔模式，仅80M带宽有效，取值范围0~1，默认1
	PsduConfigType11Ba PsduCfg[MAX_SEGMENT_11BA_NUM];    //最大4个分段
	int Reserved[128];                                   //保留位
} Set11BA;
/* End 11BA新增 */

/***** 802.11AZ Start *****/
typedef struct {
    int Nsts;
    int LtfRep;
    int LtfKey[16];
    int LtfIv[16];
    int Reserved[128];
} Set11AzUser;

typedef struct {
    /* # HE-SIG-A Field for HE ranging */
    int STBC;
    int UL_DL;
    int MCS;
    /* BCC/LDPC,0=BCC,1=LDPC */
    int CodingType;
    /* dual carrier modulation, 0=disable, 1=enable */
    int DCM;
    /* GI duration and HE-LTF size: 2(2x HE-LTF + 1.6us GI) */
    int GILTFSize;
    int BeamChange;
    /* Doppler,0 :Disable(initial), 1 : Enable */
    int Doppler;
    int Midamble_Periodicity;
    int Beamformed;

    /* # HE-SIG-A Field for HE ranging or HE TB ranging */
    /* BSS color,0 - 63 (initial 63) */
    int BSScolor;
    int Spatial_Reuse[4];
    /* TXOP Duration,0 - 127 (initial 127) */
    int TXOP;

    /* # Ranging NDP parameter for HE ranging or HE TB ranging */
    int SecureMode;
    int UserNum;
    /* Frequency domain window: 0(OFF), 1(ON)*/
    int TxWinFlg;
    int Reserved[128];

    Set11AzUser User[ALG_11AZ_MAX_USER_NUM];
} Set11AZ;

typedef struct
{
    int AID;                     // User的AID号.1 - 2007(initial 1)
    int CodingType;              // BCC 
    int DCM;                     // 指示是否需要双载波调制 0/1。0=disable, 1=enable
    int MCS;                     // 范围值0~11
    int NSS;                     // 数据流数1~8，Doppler生效时NSS最大为4
    int NSSStart;
    int SegmentIndex;
    int RuIndex;

    int LtfRep;
    int LtfIv[16];
    int LtfKey[16];

    int Reserved[128];           // 保留位
} AZ_TB_MUMIMO_User;

typedef struct
{
    int UserNum; // 取值[0~8]
    MUMIMO_QMat Qmat;
    AZ_TB_MUMIMO_User User[MUMIMO_8_USER];
    int Reserved[128]; // 保留位
} AZ_TB_MUMIMO_RU;

typedef struct {
    int NumLTFSymbols;
    int LDPC_Extra;
    int Pre_FECfactor;
    /* must be 1 */
    int GILTFSize;
    /* 0:Disable(initial); 1:Enable */
    int Doppler;
    /* 1:10 symbol; 2:20 symbol. take effect only Doppler=1 */
    int Midamble_Periodicity;
    /* 0:Disable(initial); 1:Enable */
    int PE;
    /* 0:0us 1:8us 2:16us. take effect only PE=1 */
    int PE_Type;
    int STBC;

    /* # HE-SIG-A Field for HE ranging or HE TB ranging */
    /* BSS color,0 - 63 (initial 63) */
    int BSScolor;
    int Spatial_Reuse[4];
    /* TXOP Duration,0 - 127 (initial 127) */
    int TXOP;
    /* # Ranging NDP parameter for HE ranging or HE TB ranging */
    int SecureMode;
    int TxWinFlg;
    int TBMUMIMOFlag;

    int RUNum[MAX_SEGMENT];
    AZ_TB_MUMIMO_RU RU[MAX_SEGMENT][AZ_RU_COUNT];

    int Reserved[128];
}Set11AZ_TB;
/***** 802.11AZ End *****/

/***** CBF 相关参数 *****/

typedef struct {
	int MUFlag;
	int N_row;
	int Ng;
	int CodebookIndex;
	Complex ChannelMatrix[64];
}FeedbackBfContrlConf;

typedef struct{ 
	int SNR[8];
	int Angle[56];
	int AngleLen;
	int Nsc;
	int MUBF_DeltaSNR[8];
	int MUBF_Nsc;
	int DeltaSNRLen;
	int HE_RUStartIndex;
	int HE_RUEndIndex;
	int EHT_PartialBWInfo;
}CBFUserInfo;

typedef struct {
	int UserNum;
	CBFUserInfo UeBFInfo[BE_RU_COUNT];
}CBFReport;
/***** CBF 相关参数  END*****/

typedef struct
{
    //standard和subType确定要生成那种信号类型
    int standard;            //信号标准. 参考：WT_STANDARD_ENUM
    int subType;             //subType: 类似ax的ppdu type
    int bandwidth;           //带宽, 单位：MHz (20:20M; 40:40M; 80:80M; 160:160M; 160:8080M)
    int samplingRate;        //采样速率, 单位：Hz
    int NSS;                 //stream数量
    int segment;             //segment数量，默认值等于1，80+80M信号时配置值等于2
    double FreqErr;          //频偏
    double IQImbalanceAmp;   //IQ不平衡幅度调整
    double IQImbalancePhase; //IQ不平衡相位调整
    double DCOffset_I;       //DC offset I调整
    double DCOffset_Q;       //DC offset Q调整
    double ClockRate;        //Clock rate
    double Snr;
    double Gap;              //Unit: Sec,default 10us, 0.00001s
    int SpatialExtension;
    int Duplicate;
    int ReducePARA;
	int TailGapValidFlag;
	double TailGap;          //Unit: Sec,default 10us, 0.00001s
	int Ntx;                 //天线数
    int PhaseNoiseFlag;
    double PhaseNoise;
    double PhaseNoisePLLBW;
    double ClockError;
	int FeedbackBfEnable;
    int Reserved[237];       //保留
} PNSettingBase;

typedef struct
{
    PNSettingBase commonParam;
    union
    {
        Set11A PN11a;
        Set11B PN11b;
        Set11N PN11n;
        Set11AC PN11ac;
        Set11AC_MUMIMO PN11ac_MUMIMO;
        Set11AX_SU PN11ax_SU;
        Set11AX_MU PN11ax_MU;
        Set11AX_TB PN11ax_TB;
		Set11BA PN11ba;
        Set11AZ PN11az;
        Set11AZ_TB PN11az_TB;
#ifdef _WT448
		Set11BE_MU PN11be_MU;
		Set11BE_TB PN11be_TB;
        Set11AH PN11ah;
        Set11AH_MUMIMO PN11ah_MUMIMO;
#else
		Set11BE_MU *PN11be_MU;
		Set11BE_TB *PN11be_TB;
#endif
    };
    TriggerFrameSetting TrigFrame;
#ifdef _WT448
	WlanChannelModelsParam ChannelModel;
	FeedbackBfContrlConf FBconf;
#endif
	
    void *ExtendParam;
}GenWaveWifiStruct;

typedef struct
{
    PNSettingBase commonParam;
    int psduLen;
    int Reserved[128];    //保留
}GenWaveCwStruct;

/*******************************************************************************************************************/
/*                                               BT Structure Start                                                */
/*******************************************************************************************************************/
enum Alg_BtProtocolType {
    ALG_BT_BR_EDR = 0,
    ALG_BT_BLE
};

/* BT BR/EDR packet type */
enum Alg_BtBrEdrPacketType
{
    /* Common */
    ALG_BT_PACKET_ID = 0,
    ALG_BT_PACKET_NULL,
    ALG_BT_PACKET_POLL,
    ALG_BT_PACKET_FHS,
    /* ACL 1M */
    ALG_BT_PACKET_DM1,
    ALG_BT_PACKET_DH1,
    ALG_BT_PACKET_DM3,
    ALG_BT_PACKET_DH3,
    ALG_BT_PACKET_DM5,
    ALG_BT_PACKET_DH5,
    ALG_BT_PACKET_AUX1,
    /* ACL 2M/3M */
    ALG_BT_PACKET_2_DH1,
    ALG_BT_PACKET_2_DH3,
    ALG_BT_PACKET_2_DH5,
    ALG_BT_PACKET_3_DH1,
    ALG_BT_PACKET_3_DH3,
    ALG_BT_PACKET_3_DH5,
    /* SOC 1M */
    ALG_BT_PACKET_HV1,           //1表示1/3的FEC
    ALG_BT_PACKET_HV2,           //2表示2/3的FEC
    ALG_BT_PACKET_HV3,           //3表示无FEC
    ALG_BT_PACKET_DV,            //DV: 一个单时隙中包含ACL与SCO数据
    /* eSCO 1M */
    ALG_BT_PACKET_EV3,
    ALG_BT_PACKET_EV4,
    ALG_BT_PACKET_EV5,
    /* eSCO 2M/3M */
    ALG_BT_PACKET_2_EV3,
    ALG_BT_PACKET_2_EV5,
    ALG_BT_PACKET_3_EV3,
    ALG_BT_PACKET_3_EV5,
};

/* BT BLE packet type */
enum Alg_BtBlePacketType {
    ALG_BT_PACKET_LE_TEST = 0,
	ALG_BT_PACKET_LE_ADVERTISING = 1
};

enum Alg_BtBleFormatType {
    ALG_BT_BLE_1M = 0,
    ALG_BT_BLE_2M,
    ALG_BT_BLE_CODED
};

typedef struct {
    int LLID;                       // Logical Link Identifier(LLID):取值0~3 :默认：1
    int mFlow;                      //Flow取值：0,1
    int payLoadType;                //Payload type 0~7
    int PayLoadSize;                // payload(PSDU)

    int Reversed[8];
} BtBrEdrNormalInfo;

typedef struct {
    unsigned char EIR;              //1bit
    unsigned char SR;               //2bit
    int ClassofDevice;              //24bit
    int LTAddr;                     //3bit
    int CLK27b2;                    //26bit
    int Reversed[8];
} BtBrEdrFHSInfo;

typedef struct {
    /* Voice Filed */
    char VoiceField[10];

    /* Data Field */
    int LLID;                       // Logical Link Identifier(LLID):取值0~3 :默认：1
    int mFlow; //Flow取值：0,1
    int payLoadType; //Payload type 0~7
    int PayLoadSize; // payload(PSDU)

    int Reversed[8];
} BtBrEdrDVInfo;

typedef struct {
    int PackType; /* Pack Type, reference Alg_BtBrEdrPacketType */

    /* PHY Parameters */
    double ModuIdex;                //Modulation Index：取值：0.01~0.99(只支持两位小数)   由原来的FreqDeviation字段修改
    int BTProductIdex;              //Filter BT Product 取值：0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9
    int RolloffIdex;                //EDR Filter Rolloff  取值：0.3，0.35,0.40,0.45,0.50
    int GuardTime;                  //EDR Guard Time us;范围0~10us

    /* Device Address Part */
    unsigned int LAP;               // LAP(Lower Address Part)：24bit 默认：0x000000
    unsigned char UAP;              // UAP(Upper Address Part)：8bit 默认：0x47
    unsigned int NAP;               // NAP(Non-significant Address Part)：16bit 默认：0x0000

    /* Header format */
    int LT_ADDR;                    //  3bit  LT_ADDR：3bit，取值0~7
    int Flow;                       //  1bit  FLOW: FLOW_Control：1bit，取值0~1
    int ARQN;                       //  1bit  ARQN: ACK Indication：1bit，取值0~1
    int SEQN;                       //  1bit  SEQN: Seq Number Inder：1bit，取值0~1

    int DataWhitening; // 数据是否需要加噪（加噪值为1，不加噪值为0，默认不加噪：0）

    /* Packet Ramp Time */
    double PowerRampTime;           //us 默认：2us
	int CRC;                        // 0: CRC Pass, 1: CRC Fail
    int Reversed[7];                //保留位，防止又增加原来位置对不上

    /* Payload */
    union {
        BtBrEdrNormalInfo NormalPacket;
        BtBrEdrFHSInfo FHSPacket;
        BtBrEdrDVInfo DVPacket;
    };
} BtBrEdrSet;

typedef struct {
    int BLEMapperS;                 //只对enPacketType_LE_Coded_Test有效，取值2或者8

    int Reversed[8]; /* Reversed */
} BtBleTestCodedInfo;

typedef struct {
    /* CTE: Constant Tone Extension */
    int CTEInfo;                        //1bit 1，表示存在CTEInfo field和CTE；  0，表示不存在，默认为0
    int CTEtime;                        //5bit 范围2-20，对应16-160us；
    int CTEType;                        //2bit 0-AoA , 1-AOD 1us slot ,  2-AOD 2us slot , 3-reserved
    int AntNum;
    double AntGain[4];

    int Reversed[8]; /* Reversed */
} BtBleTestUncodedInfo;

typedef struct {
    //LE Packet Settings
    unsigned int LE_SyncWord;        //32bit 默认取值94826e8e(最左边为低位 如A=1010)

    //Payload
    int payLoadType;                //Payload type 0~7
    int PayLoadSize;                // payload(PSDU)

    int BleEnhancedMode;  // 0:标准BLE，1:速率增强型BLE

    int Reversed[8]; /* Reversed */

    union {
        BtBleTestCodedInfo Coded;
        BtBleTestUncodedInfo Uncoded;
    };
} BtBleTestInfo;

typedef struct {
	unsigned int AccessAddress;        //32bit Ĭ��0x8E89BED6(�����Ϊ��λ ��A=1010)
	int ChannelIndex;     //0-39
	int BLEMapperS;
	int reserved[15];
} BtBleAdvertisingInfo;

typedef struct {
    int PackType; /* Pack Type. Refer to Alg_BtBlePacketType */
    int FormatType; /* Pack Formate Type: 0(LE 1M), 1(LE 2M), 2(LE Code). Refer to enum Alg_BtBleFormatType */

    //PHY Parameters
    double ModuIdex; //Modulation Index：取值：0.01~0.99(只支持两位小数)   由原来的FreqDeviation字段修改
    int BTProductIdex; /* Filter BT Product: 0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9 */

    //Packet Ramp Time
    double PowerRampTime; /* default 2us */
	int CRC;              /* 0: CRC Pass, 1: CRC Fail */
    int Reversed[15]; /* Reversed */

    union {
        BtBleTestInfo TestPacket;
		BtBleAdvertisingInfo AdvertisingPacket;
    };

} BtBleSet;

typedef struct
{
    PNSettingBase commonParam; /* subType type. Refer to enum Alg_BtProtocolType */
    union {
        BtBrEdrSet BrEdrSet;
        BtBleSet BleSet;
    };
	
    int Reserved[126];    /* Reversed */
	void* ExtendParam;
}GenWaveBtStruct;

/*******************************************************************************************************************/
/*                                               BT Structure Stop                                                 */
/*******************************************************************************************************************/

/*******************************************************************************************************************/
/*                                               GLE Structure Start                                                 */
/*******************************************************************************************************************/
typedef struct{
    int Mcs;                 //8bits-链路质量MCS,取值范围0~11
    int BroadType;           //3bits-广播类型,取值0~3
    int PacketType;          //3bits-包类型,当信道带宽bandwidth=0（对应1MHz）时，取值范围0~5，当信道带宽bandwidth为非0（对应2/4MHz）时，取值范围1~5
    int DataLength;         //8bits-数据长度
    int Reversed[8];
}Alg_GleCtrlInfoTypeA1;

typedef struct{
    int Mcs;                //8bits-链路质量MCS,取值范围0~11
    int PacketType;         //2bits-包类型指示
    int EmptyPacketInd;     //1bit-空包指示
    int SndSN;              //1bit-发送序列号
    int RevSN;              //1bit-接收序列号
    int FlowCtrlInd;        //1bit-流控指示
    int RevSysFrmInd;       //1bit-接收系统帧指示 
    int DataLength;        //11bits-数据长度
    int Reversed[4];
}Alg_GleCtrlInfoTypeA2;

typedef struct{
    int Mcs;                //8bits-链路质量MCS,取值范围0~11
    int PacketType;         //2bits-包类型指示
    int EmptyPacketInd;     //1bit-空包指示
    int SndSN;              //5bit-发送序列号
    int RevSN;              //5bit-接收序列号
    int FlowCtrlInd;        //1bit-流控指示  
    int ScheduleInd;        //1bit-异步链路调度指示
    int DataLength;        //11bits-数据长度
    int Reversed[4];
}Alg_GleCtrlInfoTypeA3;

typedef struct{
    int Mcs;                //8bits-链路质量MCS,取值范围0~11
    int PacketType;         //2bits-包类型指示
    int EmptyPacketInd;     //1bit-空包指示
    int SndSN;              //1bit-发送序列号
    int RevSN;              //8bits-接收序列号
    int FlowCtrlInd;        //1bit-流控指示   
    int ScheduleInd;        //1bit-异步链路调度指示
    int DataLength;        //11bits-数据长度
    int Reversed[4];
}Alg_GleCtrlInfoTypeA4;

typedef struct{
    int Mcs;                //8bits-链路质量MCS,取值范围0~11
    int PacketType;         //2bits-包类型指示
    int EmptyPacketInd;     //1bit-空包指示
    int SndSN;              //1bit-发送序列号
    int RevSN;              //1bit-接收序列号
    int FlowCtrlInd;        //1bit-流控指示  
    int ScheduleInd;        //1bit-异步链路调度指示
    int DataLength;        //11bits-数据长度
    int Reversed[4];
}Alg_GleCtrlInfoTypeA5;

typedef struct{
    int Mcs;                //8bits-链路质量MCS,取值范围0~11
    int PacketType;         //2bits-包类型指示
    int DataPacketSN;       //5bits-数据包序列号
    int DataPacketGrp;      //3bits-数据包分组
    int EndInd;             //1bit-结尾指示  
    int RevSysFrmInd;       //1bit-接收系统帧指示
    int DataLength;        //11bits-数据长度
    int Reversed[5];
}Alg_GleCtrlInfoTypeA6;

typedef struct{
    int Mcs;                //8bits-链路质量MCS,取值范围0~11
    int DataPacketSN;       //5bits-数据包序列号
    int DataPacketGrp;      //3bits-数据包分组
    int DataLength;        //11bits-数据长度
    int Reversed[8];
}Alg_GleCtrlInfoTypeA7;

typedef struct{
    int FrmFormatInd;       //1bit-帧格式指示 
    int HarqFeedback;        //8bits-Harq反馈
    int DataPacketSN;       //1bit-数据包序列号
    int Mcs;                //4bits-链路质量MCS,取值范围0~11      
    int DataLength;        //11bits-数据长度
    int FlowCtrlInd;        //1bit-流控指示
    int UpperLinkInd;       //1bit-上级链路指示
    int Reversed[5];
}Alg_GleCtrlInfoTypeB1;

typedef struct{
    int HarqFeedback;       //25bits-Harq反馈
    int FlowCtrlInd;        //1bit-流控指示
    int UpperLinkInd;       //1bit-上级链路指示
    int Reversed[9];
}Alg_GleCtrlInfoTypeB2;

typedef struct{
    int DataPacketGrp;      //5bits-数据包分组 
    int DataPacketSN;       //5bits-数据包序列号
    int Mcs;                //4bits-链路质量MCS,取值范围0~11      
    int DataLength;        //11bits-数据长度
    int FlowCtrlInd;        //1bit-流控指示
    int MaxDataPacketSNInd; //1bit-最大数据包序列号指示
    int Reversed[6];
}Alg_GleCtrlInfoTypeB3;

typedef struct{
    int BrdcastSetFlag;       //5bits-广播集合标识
    int BrdcastSetUpdateInd;  //1bit-广播集合数据更新指示
    int DataPacketSN;         //4bits-数据包序列号
    int Mcs;                  //4bits-链路质量MCS,取值范围0~11      
    int DataLength;          //11bits-数据长度
    int FlowCtrlInd;          //1bit-流控指示
    int MaxDataPacketSNInd;   //1bit-最大数据包序列号指示
    int Reversed[5];
}Alg_GleCtrlInfoTypeB4;

typedef struct{
    int MsgTypeInd;         //3bits-消息类型指示,当信道带宽bandwidth=0（对应1MHz）时，取值范围0~5，当信道带宽bandwidth为非0（对应2/4MHz）时，取值范围1~5
    int ConnectInd;         //1bit-是否可连接指示
    int DiscoveryInd;       //1bit-是否可发现指示
    int DirectInd;          //1bit-是否包含定向内容指示
    int NonDirectInd;       //1bit-是否包含非定向内容指示
    int DataUpdateInd;      //1bit-数据更新指示
    int Mcs;                //4bits-链路质量MCS,取值范围0~11    
    int DataLength;        //8bits-数据长度
    int Reversed[4];
}Alg_GleCtrlInfoTypeB5;

typedef struct
{
    //基础参数
    unsigned int PhyID;                             //物理层ID,取值0~65535(因有效长度为24bits，故用U32表示),默认1  
    unsigned int SlotIndex;                         //基础时隙序号，取值0~(2^30-1)
    unsigned int CrcDataSeed;                       //数据部分计算CRC的种子(初始值),界面用16进制表示，当CrcType为CRC24A时，取值范围0~0xFFFFFF,当CrcType为CRC32，取值范围0~0xFFFFFFFF,默认取0x555555   
    unsigned int MSeqNo;                           //m序列的序列号,取值0~5 
    unsigned int CrcType;                          //CRC类型，取值0~1, 0:CRC24A,1:CRC32   
    unsigned int DataType;                         //数据信息的数据类型，取值0~7
    unsigned int PilotDensity;                     //导频密度，取值0~2, GLE_PILOT_DENSITY_4 = 0,GLE_PILOT_DENSITY_8 = 1,GLE_PILOT_DENSITY_16 = 2 // revise:value is 0/4/8/16     
    double ModIndex;                                //GFSK调制指数，取值0.45~0.5,默认0.5
    unsigned int Scramble;                          // 是否加扰, 0:OFF, 1:ON
    
    //控制信息相关参数
    unsigned int BroadIndex;                       //广播索引，取值1~3,默认1
    unsigned int CtrlInfoType;                     /*控制信息类型,取值范围0~11,当subType(无线帧类型)为1和2时, 取值GLE_CTRL_INFO_TYPE_A1(0)~GLE_CTRL_INFO_TYPE_A7(6),
                                                      当subType为3和4时, 取值GLE_CTRL_INFO_TYPE_B1(7)~GLE_CTRL_INFO_TYPE_B5(11) */                                                                                                     
	unsigned int FreqBand;                         //频带指示，取值0~2，0: 2.4G，1: 5.1G，2: 5.8G，默认取0（后面是界面显示值）
	unsigned int ChannelNo;                        //信道号，取值0~2, 当FreqBand =0时，0: 0, 1：22, 2:78;  当FreqBand =1时，0: 80, 1:96, 2:176; 当FreqBand =2时，0: 278, 1:364, 2:384，默认取0（后面是界面显示值）。

    unsigned int SyncSource;       //同步方式，取值0~1, 0: Standard Protocol(此时PhyID有效,SyncSeq字段无效变灰), 1: AccessCode(此时PhyID无效变灰,SyncSeq字段有效); 默认0.
    unsigned int SyncSeq[64];      /* 同步bit序列，每个元素的取值为0或1，当subType = 1时，有效长度为32bits,
                                       当subType = 2时，有效长度为64bits，当subType = 3时，有效长度为31bits,
                                       当subType = 4时，有效长度为63bits，默认为10101010...的Bit序列 */
    unsigned int ScheSlotLength;   //调度时隙长度, 取值范围0~4，默认0，分别对应0: 125us, 1: 100us, 2: 75us, 3: 50us, 4: 25us。  
    unsigned int ScheSlotNum;      //调度时隙数, 取值范围1~1000,默认5 
	int Reserved[254];             //256->254
    union {        
        Alg_GleCtrlInfoTypeA1  CtrlInfoTypeA1;
        Alg_GleCtrlInfoTypeA2  CtrlInfoTypeA2;
        Alg_GleCtrlInfoTypeA3  CtrlInfoTypeA3;
        Alg_GleCtrlInfoTypeA4  CtrlInfoTypeA4;
        Alg_GleCtrlInfoTypeA5  CtrlInfoTypeA5;
        Alg_GleCtrlInfoTypeA6  CtrlInfoTypeA6;
        Alg_GleCtrlInfoTypeA7  CtrlInfoTypeA7;
        Alg_GleCtrlInfoTypeB1  CtrlInfoTypeB1;
        Alg_GleCtrlInfoTypeB2  CtrlInfoTypeB2;
        Alg_GleCtrlInfoTypeB3  CtrlInfoTypeB3;
        Alg_GleCtrlInfoTypeB4  CtrlInfoTypeB4;
        Alg_GleCtrlInfoTypeB5  CtrlInfoTypeB5;
    };
   
}SparkLinkGlePacketSet;

typedef struct
{
    PNSettingBase commonParam;
    SparkLinkGlePacketSet GlePacketSet;
    int Reserved[128];    //保留
}GenWaveGleStruct;

/*GLE输出*/
typedef struct _stTxGLE_Out
{
	Complex *outData[MAX_STREAM_COUNT];               //输出数据
	int outLen[MAX_STREAM_COUNT];
    int synSeq[64];                 //输出同步Bit序列
	int synSeqLen;                  //输出同步序列长度
} stTxGLE_Out;

/*******************************************************************************************************************/
/*                                               GLE Structure Stop                                                 */
/*******************************************************************************************************************/
/*******************************************************************************************************************/
/*                                               Wisun Structure Start                                             */
/*******************************************************************************************************************/

typedef struct
{
    int PsduType; // 0~7
    int CRCCheckEnable;
    int PsduLength; // 0~4095
    int MacHeaderEnable;

    unsigned char FrameControl[2];
    unsigned char DestPanID[2]; // '0000' - 'FFFF'
    unsigned char DestAddr[8]; // '0000 0000 0000 0000' - 'FFFF FFFF FFFF FFFF'
    unsigned char SrcPanID[2]; // '0000' - 'FFFF'
    unsigned char SrcAddr[8]; // '0000 0000 0000 0000' - 'FFFF FFFF FFFF FFFF'

    int Reserved[128];
} WiSun_PSDU;

typedef struct
{
    int option; // 0, 1, 2, 3
    int MCS; // 0 ~ 6
    int scrambler; // 0 ~ 3
    int phyOFDMInterleaving; // PIB interleaving: 0, 1

    WiSun_PSDU PSDU;

    int Reserved[128];
} SetWiSun_MROFDM;

typedef struct
{
    int freqBand;
    int spreadingMode;
    int rateMode;
    int chipRate;

    WiSun_PSDU PSDU;

    int Reserved[128];
} SetWiSun_MROQPSK;

typedef struct
{
    int phyFSKPreambleLength;          // Preamble重复次数   4-64
    int ModeSwitch;                    // 决定PPDU是否是mode switch类型， 只支持0
    int PhySunFskSfd;                  // 决定PHR.SFD的填充内容     0-1
    int PhyFskFecEnabled;              // 是否FEC     0-1
    int PhyFskFecScheme;               // FEC方式     0-NRNSC  1-RSC
    int PhyFskFecInterleavingRsc;      // 是否交织    0-1
    int PhyFskScramblePsdu;            // 是否加扰(data whitening)   0-1
    int FCSType;                       // PHR的FCSType   0-4字节FCS   1-2字节FCS
    int Modulation;                    // 调制模式
    double DataRate;                   // 符号速率b/s
    double ModulationIndex;            // 调制指数
    WiSun_PSDU PSDU;
    int Reserved[116];
} SetWiSun_MRFSK;


typedef struct
{
    PNSettingBase commonParam;
    union
    {
        SetWiSun_MROFDM PNWiSun_MROFDM;
        SetWiSun_MROQPSK PNWiSun_MROQPSK;
		SetWiSun_MRFSK PNWiSun_MRFSK;
    };

    int Reserved[128];    //保留
}GenWaveWisunStruct;

typedef struct _stTxWisun_Out
{
    Complex *outData[MAX_STREAM_COUNT];               //输出数据
    int outLen[MAX_STREAM_COUNT];

    int Reserved[128];    //保留
} stTxWisun_Out;
/*******************************************************************************************************************/
/*                                               Wisun Structure Stop                                              */
/*******************************************************************************************************************/

/*******************************************************************************************************************/
/*                                               SLB Structure Start                                                 */
/*******************************************************************************************************************/
#define SLB_MAX_CARRIER_NUMBER     5
#define SLB_MAX_USER_NUMBER        5

typedef struct
{
	int CPType;
	int GTEnable;
	int FrameFormat;
	int SuperframeIndex;
	int Noffset;
	int CtrlInfoSymbNum;
	int SysInfoUpdateFlag;
	int SysInfoUpdatePeriod;
	int SysInfoTransPeriod;
	int SysInfoSuperFrameNum;
	int Reserved[128];    /* Reversed */
}SlbPbchInfoCfg;

typedef struct
{
	int DomainFlag;
	int DomainSyncID;
	int SysSymbOverheadPeriod;
	int SysSymbOverheadNum;
	int SsbState;
	SlbPbchInfoCfg PbchInfoCfg;
	int Reserved[128];    /* Reversed */
}SlbCarrierInfoCfg;

typedef struct
{
	int StateFlag;
	int UeID;
	int CBGCapacity;
	int DataInfoType;
	int ScrambleEnable;
	int ChannelCodingState;
	int DataSource;
	int Reserved[128];    /* Reversed */
}SlbUserInfoCfg;

typedef struct
{
	int SamplingLength;
	int CodingBlockNum;
	int CodingType;
	int Modulation;
	int CrcLength;
	int FreqSubcarrierBitMap[40];
	int TimeSymbBitMap[8];
	int DmrsTimeSymbBitMap[96];
	int FramePeriod;
	int FrameOffset;
	int Reserved[128];    /* Reversed */
}SlbDataInfoCfgType1;

typedef struct
{
	int CrossSuperframeIndication;
	int FreqRegBitMap[10];
	int DmrsPortNum;
	int CombFlag;
	int LayerNum;
	int MCS;
	int FrameScheduleStart;
	int FrameScheduleLength;
	int HarqID;
	int ScheTypeIndication;
	int DataTypeIndication;
	int FreqInterleaverEnable;
	int Reserved[128];    /* Reversed */
}SlbDataInfoCfgType2;

typedef struct
{
	int StateFlag;
	int ChannelType;
	SlbDataInfoCfgType1 DataInfoCfgType1;
	SlbDataInfoCfgType2 DataInfoCfgType2;
	int Reserved[128];    /* Reversed */
}SlbScheInfoCfg;

typedef struct
{
	int CarrierNum;
	SlbCarrierInfoCfg  CarrierInfoCfg[SLB_MAX_CARRIER_NUMBER];
	SlbUserInfoCfg   UserInfoCfg[SLB_MAX_USER_NUMBER];
	SlbScheInfoCfg   ScheInfoCfg[SLB_MAX_CARRIER_NUMBER][SLB_MAX_USER_NUMBER];
	int Reserved[128];    /* Reversed */
}SparkLinkBasicInfoCfg;

typedef struct
{
	PNSettingBase commonParam;
	SparkLinkBasicInfoCfg SlbInfoCfg;
	int Reserved[128];    //保留
}GenWaveSlbStruct;

/*SLB输出*/
typedef struct _stTxSLB_Out
{
	Complex *outData[MAX_STREAM_COUNT];               //输出数据
	int outLen[MAX_STREAM_COUNT];
	int Reserved[256];    //保留
} stTxSLB_Out;

/*******************************************************************************************************************/
/*                                               SLB Structure Stop                                                 */
/*******************************************************************************************************************/

//license demo 测试
//#define Spec_Test

#endif  // __StructuresDef_H__
