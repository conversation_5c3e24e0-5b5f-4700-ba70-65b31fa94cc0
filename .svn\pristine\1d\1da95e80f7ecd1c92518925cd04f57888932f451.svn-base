/*
 * @Description: 3GPP：NR5G配置分析参数相关命令
 * @Autor:
 * @Date: 20231103
 */
#ifndef SCPI_3GPP_ALZ_NR5G_H_
#define SCPI_3GPP_ALZ_NR5G_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif
    //**************************************************************************************************
    // UL
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_UL_SetAlzRFPhaseCompensation(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzDuplexing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSlotPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzTddULSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzTddDlSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzTddULSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzTddDlSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzNSValue(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellNum(scpi_t *context);

    // UL Cell
    scpi_result_t SCPI_NR5G_UL_SetAlzFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBW(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellPhysicalID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellDmrsTypeAPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellUseScSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellOffsetToCarrier(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpCyclicPrefix(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpConfigType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpTransformPrecoder(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSMaxLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSAddPosInd(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSUseSixteenDmrs(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSNpuschID(scpi_t *context);

    // scpi_result_t SCPI_NR5G_UL_SetAlzUEID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzChannelType(scpi_t *context);
    // UL PUSCH
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschMappingType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBDetectMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschLayerNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschAntennaNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschCDMGroupsWoData(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsAntennaPort(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSybolLength(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenInit(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenNscid(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschGroupOrSequenceHopping(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschModulate(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschChannelCodeingState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschScrambling(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschMCS(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschUEID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRVIndex(scpi_t *context);

    //**************************************************************************************************
    // DL
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_DL_SetAlzFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzTddULSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzTddDlSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzTddULSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzTddDlSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzDuplexing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSlotPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSpecialSlotIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzRFPhaseCompensation(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellNum(scpi_t *context);

    // DL Cell
    scpi_result_t SCPI_NR5G_DL_SetAlzCellFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBW(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellPhysicalID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellDmrsTypeAPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellUseScSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellOffsetToCarrier(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCyclicPrefix(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschVrbToPrbInterLeaver(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMcsTable(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschResourceAlloc(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschRBGSizeType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschConfigType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMaxLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschAdditionalPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschUseSixteenDmrs(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymbolOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetFDRes(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetBitMap(scpi_t *context);

    // DL Scheduled Slot Allocation
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchOffSetRefType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchCase(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchLength(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchBurstSetPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchHalfFrameIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdcchState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMapType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBDetMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschBitMap(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschLayerNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschAntennaNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCDMGroupsWoData(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsSymbLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsAntPort(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsInitType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschNSCID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCodewords(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschModulate(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschChanCodingState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschScrambling(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUsePdschScrambleID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDataScrambleID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUeID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMCS(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRvIdx(scpi_t *context);

    scpi_result_t SCPI_NR5G_DL_SetAlzChannelType(scpi_t *context);

    //**************************************************************************************************
    // Measure Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_Measure_SetAlzSubFrameIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSlotIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzExceptionEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzVFilterEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzNrbViewFilter(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSfNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSlotAllEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzStatisticCount(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzTxDcOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSEMStatNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzAclrStatNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzUtraEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzPwrDynStatNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzTxPwrStatNum(scpi_t *context);

    scpi_result_t SCPI_NR5G_SetAlzRedcapEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzVersion(scpi_t *context);
    
    // assign views
    scpi_result_t SCPI_NR5G_SetAlzViewEvmEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewMerrEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewPerrEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewEvmSubcarEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewIbeEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewEsflatEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewIqconstelEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewTxPwrEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewPMonitorEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewPwrDynEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewSpectEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewACLREnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewTxMeasureEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewPdschInfoEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewSSBInfoEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewEvmSymbEnable(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewEvmSymbIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewEvmSymbPosType(scpi_t *context);
    scpi_result_t SCPI_NR5G_SetAlzViewDmrsConsState(scpi_t *context);
    
    // nr5g limits
    scpi_result_t SetVsaNrLimitModEvmRmsState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModEvmRmsLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModEvmPeakState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModEvmPeakLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModMerrRmsState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModMerrRmsLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModMerrPeakState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModMerrPeakLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModPherrRmsState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModPherrRmsLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModPherrPeakState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModPherrPeakLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModFreqErrState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModFreqErrLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIqOffsetState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIqOffsetValue(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIBEState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIBEGenMin(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIBEGenEvm(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIBEGenPwr(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIBEIqImag(scpi_t *context);
    scpi_result_t SetVsaNrLimitModIBEIqOffset(scpi_t *context);
    scpi_result_t SetVsaNrLimitModSpecFlatState(scpi_t *context);
    scpi_result_t SetVsaNrLimitModSpecFlatRange(scpi_t *context);
    scpi_result_t SetVsaNrLimitModSpecFlatMax(scpi_t *context);
    scpi_result_t SetVsaNrLimitModSpecFlatEdgeDist(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecObwState(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecObwLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecSemState(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecSemStartFreq(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecSemStopFreq(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecSemLimitPwr(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecSemRbw(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecUtraRelState(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecUtraRelLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecUtraAbsState(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecUtraAbsPwr(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecNrRelState(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecNrRelLimit(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecNrAbsState(scpi_t *context);
    scpi_result_t SetVsaNrLimitSpecNrAbsPwr(scpi_t *context);
    scpi_result_t SetVsaNrLimitSemTestTol(scpi_t *context);
    scpi_result_t SetVsaNrLimitAclrTestTol(scpi_t *context);
    scpi_result_t SetVsaNrLimitPwrState(scpi_t *context);
    scpi_result_t SetVsaNrLimitPwrOff(scpi_t *context);
    scpi_result_t SetVsaNrLimitPwrTestTol(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif // SCPI_3GPP_ALZ_NR5G_H_
