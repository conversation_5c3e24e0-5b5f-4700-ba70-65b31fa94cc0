/***************************************************************************************************
 * This confidential and proprietary software may be only used as authorized by a licensing 
 * agreement from iTest Technology Co., Ltd.
 * (C) COPYRIGHT 2020 iTest Technology Co., Ltd. ALL RIGHTS RESERVED
 *--------------------------------------------------------------------------------------------------
 * Filename             : alg_3gpp_list_api.h
 * Author               : Linden
 * Data                 : 2024-08
 * Description          :       
 * Modification History :
 * Data            By          Version         Change Description
 *--------------------------------------------------------------------------------------------------
 * Reference to the source file.
 **************************************************************************************************/

#ifndef ALG_3GPP_LIST_API_H_
#define ALG_3GPP_LIST_API_H_

/**************************************************************************************************/
/*                                          include                                               */
/**************************************************************************************************/
#include "alg_3gpp_list_export.h"

/**************************************************************************************************/
/*                                 Macro or Debug Configuration                                   */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                      Enum Definition                                           */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                    Struct Definition                                           */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                  Extern Variable Statement                                     */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                  Extern Function Statement                                     */
/**************************************************************************************************/
ALG_3GPP_LIST_API s32 Callback_3GPP_ListMain(const Alg_3GPP_ListInType *pInput, Alg_3GPP_ListOutType *pOutput);

#endif /* ALG_3GPP_LIST_API_H_ */
