
#include "includes.h"
#include "common.h"
#include "sms4.h"
#include "sms4_common.h"
#include "sms4_lcl.h"
#include "wpa_debug.h"

/*
* The input and output encrypted as though 128bit ofb mode is being used.
* The extra state information to record how much of the 128bit block we have
* used is contained in *num;
*/
void CRYPTO_ofb128_encrypt(const u8 *in, u8 *out,
    size_t len, const void *key,
    u8 ivec[16], int *num, block128_f block)
{
    u32 n;
    size_t l = 0;

    n = *num;

#if !defined(OPENSSL_SMALL_FOOTPRINT)
    if (16 % sizeof(size_t) == 0) { /* always true actually */
        do {
            while (n && len) {
                *(out++) = *(in++) ^ ivec[n];
                --len;
                n = (n + 1) % 16;
            }
# if defined(STRICT_ALIGNMENT)
            if (((size_t)in | (size_t)out | (size_t)ivec) % sizeof(size_t) !=
                0)
                break;
# endif
            /* block size = 16bytes */
            while (len >= 16) {
                (*block) (ivec, ivec, key); /* IV(n)-->CIPH(k)-->OUTPUT BLOCK(n) */
                for (; n < 16; n += sizeof(size_t))
                {
                    *(size_t *)(out + n) =
                        *(size_t *)(in + n) ^ *(size_t *)(ivec + n); /* CIPHERTEXT(n) = PLAINTEXT(n) ^ OUTPUT BLOCK(n) */
                }
                len -= 16;
                out += 16;
                in += 16;
                n = 0;
            }

            if (len) {
                (*block) (ivec, ivec, key); /* IV(n)-->CIPH(k)-->OUTPUT BLOCK(n) */
                while (len--) {
                    /* remain length */
                    out[n] = in[n] ^ ivec[n];
                    ++n;
                }
            }
            *num = n;
            return;
        } while (0);
    }
    /* the rest would be commonly eliminated by x86* compiler */
#endif
    while (l < len) {
        if (n == 0) {
            (*block) (ivec, ivec, key);
        }
        out[l] = in[l] ^ ivec[n];
        ++l;
        n = (n + 1) % 16;
    }

    *num = n;
}

void sms4_ofb128_encrypt(const u8 *in, u8 *out,
	size_t len, const sms4_key_t *key, u8 *iv, int *num)
{
	CRYPTO_ofb128_encrypt(in, out, len, key, iv, num, (block128_f)sms4_encrypt);
}

int sms4_ofb_ae(const u8 *key, size_t key_len, u8 *iv, size_t iv_len,
    const u8 *plain, size_t plain_len, u8 *crypt)
{
    s32 num = 0;
    sms4_key_t sms4_key;
    memset(&sms4_key, 0, sizeof(sms4_key));
    sms4_set_encrypt_key(&sms4_key, key);
    sms4_ofb128_encrypt((const u8*)plain, crypt, plain_len, &sms4_key, iv, &num);
    return 0;
}
