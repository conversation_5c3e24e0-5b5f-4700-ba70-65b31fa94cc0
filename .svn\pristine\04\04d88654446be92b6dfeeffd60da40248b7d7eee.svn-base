//*****************************************************************************
//  File: fliesecure.cpp
//  describe: 仪器文件加密
//  Data: 2016.7.21
//*****************************************************************************
#include <string>
#include <iostream>
#include <fstream>
#include <memory>
#include <queue>
#include <cstring>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <sstream>

#include "../general/wterror.h"
#include "../general/secure.h"
#include "../general/basefun.h"
#include "../general/devlib/defines.h"
#include "../general/wtlog.h"


#include "filesecure.h"

using namespace std;
#define FILE_SECURE_LIST_NAME "crypto_file_list.conf"
#define FILE_SECURE_TMP_PATH "/wttmp"
#define ENCRYPTO_EXT_NAME ".wtfile"

#define FILE_SECURE_DEBUG_LOG_LEVEL 0 // LEVEL: 0， 1， 2， 3， 4， 5; 前期配置为1，调试时为2/3，稳定后可改为0

const char *CryptoTable =
    "4538A0CD9E5936B42D976F240FA50B53A458ECE681C9597B7E67AC2088C2A42910E143FEABCDA16B\
10D4476E7DC8D55D026D4D9D75DEC5FC8C88C978461DF7661263513C76821EA64AFA9AD58A3B91EB\
A75A085117432402C08241EDB1B83C364ABCD985FF976E8699D75648550E500E62004D31770875BB\
3B53426EDBA364ABA8DEDBEB270DB0FA2F9D9DD7DE2100B5649E0C2D861DC8EAEC0DBEFBB4FF9DA4\
3DD8486E0CC204F2FB2B3E720595A3F09B04262542830DBCC37F94A44A9BA93207AA2EFCA0D83B9F\
ED825929FA5938D00933CA4ABE393513D1CAF56A8F87B76308457F97F23E64CF16B0EFED8E7DBF44\
9D49D86BD74F5F1D24EDCF3C72C3B819";

const unsigned int FileSecure::m_FilePasswd[4] =
    {
        0x558f5310,
        0x4c6bc750,
        0x189dfa28,
        0xab4df757,
};

long long FileSecure::m_DynamicPasswd = 0x0;

FileSecure &FileSecure::Instance(void)
{
    static FileSecure FileSecureInstance;
    return FileSecureInstance;
}

void FileSecure::CheckEnvironment(void)
{
#if FILE_SECURE_DEBUG_LOG_LEVEL > 0
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::CheckEnvironment" << endl;
#endif
    m_Vaild = false;
    do
    {
        string CurDir = GetDir();
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::CheckEnvironment CurDir=" << CurDir << endl;
#endif
        auto Pos = CurDir.find("/home/<USER>");
        if (Pos != 0)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }
        Pos = CurDir.find("/bin");
        if (Pos == string::npos)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }
        if (access((CurDir + "/WT-Drv.ko").c_str(), F_OK) != 0)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }

        if (access((CurDir + "/WT-Manager").c_str(), F_OK) != 0)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }

        string SystemInfo = Basefun::shell_exec("uname -a");
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::CheckEnvironment SystemInfo=" << SystemInfo << endl;
#endif
        auto Pos4xx = SystemInfo.find("Linux wt4xx 5.4.0-53-generic #59-Ubuntu");
        auto Pos400 = SystemInfo.find("Linux wt400 5.4.0-53-generic #59-Ubuntu");
        if (Pos4xx == string::npos && Pos400 == string::npos)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }

        Pos = SystemInfo.find("x86_64 x86_64 x86_64 GNU/Linux");
        if (Pos == string::npos)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }

        struct stat FileStat;
        if (stat((CurDir + "/WT-Manager").c_str(), &FileStat) != 0)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }
        long long ManagerTime = FileStat.st_mtime;

        if (stat((CurDir + "/WT-Drv.ko").c_str(), &FileStat) != 0)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }

        long long DrvTime = FileStat.st_mtime;
        long long DynamicPasswd = ((ManagerTime ^ DrvTime) ^ 0xF0F0FF00F0F0FF00);

        if (DynamicPasswd != m_DynamicPasswd)
        {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "line" << __LINE__ << ":CheckEnvironment error" << endl;
#endif
            break;
        }

        m_Vaild = true;
    } while (0);
}

FileSecure::FileSecure() : m_Vaild(false)
{
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::FileSecure" << endl;
#endif
    CheckEnvironment();
    DecryptPasswd();
    if (m_Vaild == false)
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure Vaild false" << endl;
#endif
        return;
    }

    string ListName = GetDir() + "/" + FILE_SECURE_LIST_NAME;
    string ListEncryptName;
    CheckFile(ListName, ListEncryptName, true);
    m_NameList[ListName] = ListEncryptName;
    if (access(ListName.c_str(), F_OK) == 0)
    {
        EncryptFile(ListName);
    }
    else if (access(ListEncryptName.c_str(), F_OK) != 0)
    {
        Basefun::LinuxSystem(("touch \"" + ListEncryptName + "\"").c_str());
    }

    int SrcLen = 0;
    std::shared_ptr<char[]> FileData;
    Decrypt(m_NameList[ListName], FileData, SrcLen);
    stringstream NewStrs;
    string Line;
    string::size_type Begin, End = string::npos;
    NewStrs.write(FileData.get(), SrcLen);
    while (getline(NewStrs, Line))
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Line << endl;
#endif
        // 去掉空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#' || Line[Begin] < ' ')
        {
            continue;
        }
        Line = Line.substr(Begin);
        // 去掉结尾注释
        End = Line.find_last_of('#');
        if (End != string::npos)
        {
            Line = Line.substr(0, End);
        }
        // 去掉结尾空格
        End = Line.find_last_not_of(' ');
        if (End != string::npos)
        {
            Line = Line.substr(0, End);
        }
        // 相对路径转换为绝对路径
        if (Line[0] != '/')
        {
            Line = GetDir() + "/" + Line;
        }
        // 判断是文件来说路径
        if (Line[Line.size() - 1] != '/')
        {
            string EncryptName;
            CheckFile(Line, EncryptName, true);
            m_NameList[Line] = EncryptName;
        }
        else
        {
            Line = Line.substr(0, Line.size() - 1);
            m_PathList.insert(Line);
        }
    }
#if FILE_SECURE_DEBUG_LOG_LEVEL > 0
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::FileSecure m_NameList:" << '\n';
    for (const auto &File : m_NameList)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(File.first) << PoutN(File.second);
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::FileSecure m_PathList:" << '\n';
    for (const auto &Path : m_PathList)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << PoutN(Path);
    }
#endif
}

void FileSecure::Init(void)
{
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::Init m_NameList" << '\n';
#endif

    if (m_Vaild == false)
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure Vaild false" << endl;
#endif
        return;
    }

    for (const auto &File : m_NameList)
    {
        if (access(File.first.c_str(), F_OK) == 0)
        {
            EncryptFile(File.first);
        }
    }
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure::Init m_PathList" << '\n';
#endif
    for (const auto &Path : m_PathList)
    {
        queue<string> DirQueue;
        DirQueue.push(Path + "/");

        while (!DirQueue.empty())
        {
            string DirName = DirQueue.front();
            DirQueue.pop();
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(DirName) << '\n';
#endif
            DIR *Dir = opendir(DirName.c_str());
            if (Dir)
            {
                struct dirent *Ent = nullptr;
                while ((Ent = readdir(Dir)))
                {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(Ent->d_name) << Pout((int)Ent->d_type) << '\n';
#endif
                    if ((0 == strcmp(".", Ent->d_name)) || (0 == strcmp("..", Ent->d_name)))
                    {
                        continue;
                    }

                    if (DT_DIR == Ent->d_type || DT_LNK == Ent->d_type) // 若为目录或一级目录软链接类型，加入到队列中
                    {
                        DirQueue.push(DirName + Ent->d_name + "/");
                    }
                    if (DT_REG == Ent->d_type) // 若为文件，则添加到vector中
                    {
                        string File(Ent->d_name);
                        if (File.find(ENCRYPTO_EXT_NAME) == string::npos)
                        {
                            File = DirName + File;
                            if (access(File.c_str(), F_OK) == 0)
                            {
                                EncryptFile(File);
                            }
                        }
                    }
                }
                closedir(Dir);
            }
        }
    }
#if FILE_SECURE_DEBUG_LOG_LEVEL > 0
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Init m_NameList:" << '\n';
    for (const auto &File : m_NameList)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(File.first) << PoutN(File.second);
    }
#endif
}

string FileSecure::GetDir(void)
{
    char Buf[1024];
    int Cnt = readlink("/proc/self/exe", Buf, 1024);
    while (Cnt--)
    {
        if (Buf[Cnt] == '/')
        {
            Buf[Cnt] = '\0';
            break;
        }
    }

    return Buf;
}

int FileSecure::Encrypt(const string &FileName, std::shared_ptr<char[]> &FileData, int &SrcLen, int &FileLen)
{
    ifstream ifs(FileName, std::ios::binary);
    if (!ifs)
    {
        return WT_FILE_OPEN_ERROR;
    }

    struct stat statbuf;
    stat(FileName.c_str(), &statbuf);
    SrcLen = statbuf.st_size;
    if (SrcLen < 0)
    {
        ifs.close();
        return WT_FILE_OPEN_ERROR;
    }
    FileLen = ((int)statbuf.st_size + 3) & (~3);
    FileData.reset(new (std::nothrow) char[FileLen]);
    ifs.read(FileData.get(), statbuf.st_size);
    memset(FileData.get() + statbuf.st_size, 0, FileLen - statbuf.st_size);
    ifs.close();
    Secure::Encrypt(reinterpret_cast<int *>(FileData.get()), FileLen / 4, m_FilePasswd);
    return WT_OK;
}

int FileSecure::Decrypt(const string &EncryptName, std::shared_ptr<char[]> &FileData, int &SrcLen)
{
    struct stat statbuf;
    int Ret;
    Ret = stat(EncryptName.c_str(), &statbuf);
    //WTLog::Instance().WriteLog(LOG_DEBUG, "EncryptName%s, buf.st_size%ld, Ret%d\n", EncryptName.c_str(), statbuf.st_size, Ret);

    if (Ret != WT_OK)
    {
        return Ret;
    }
    FileData.reset(new (std::nothrow) char[statbuf.st_size]);

    ifstream ifs(EncryptName, std::ios::binary);
    if (!ifs)
    {
        return WT_FILE_OPEN_ERROR;
    }
    ifs.read(reinterpret_cast<char *>(&SrcLen), sizeof(SrcLen));
    if (!ifs.good() || SrcLen < 0)
    {
        ifs.close();
        return WT_FILE_OPEN_ERROR;
    }
    int FileLen = statbuf.st_size - sizeof(FileLen);
    ifs.read(FileData.get(), FileLen);
    ifs.close();
    Secure::Decrypt(reinterpret_cast<int *>(FileData.get()), FileLen / 4, m_FilePasswd);
    return WT_OK;
}

int FileSecure::EncryptFile(string FileName)
{
    if (m_Vaild == false)
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure Vaild false" << endl;
#endif
        return WT_VERSION_ERROR;
    }

    string EncryptName;
    if (false == CheckFile(FileName, EncryptName, false))
    {
        return -1;
    }

    int FileLen = 0;
    int SrcLen = 0;
    std::shared_ptr<char[]> FileData(nullptr);
    if (Encrypt(FileName, FileData, SrcLen, FileLen) != WT_OK)
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 0
        WTLog::Instance().WriteLog(LOG_DEBUG, "Function %s,Line %d: Open file %s WT_FILE_OPEN_ERROR\n",
               __FUNCTION__, __LINE__, FileName.c_str());
#elif FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().WriteLog(LOG_DEBUG, "Function %s,Line %d: Open file WT_FILE_OPEN_ERROR\n", __FUNCTION__, __LINE__, );
#endif
        return WT_FILE_OPEN_ERROR;
    }

    ofstream ofs(EncryptName, std::ios::binary);
    if (!ofs)
    {
        return WT_FILE_OPEN_ERROR;
    }
    ofs.write(reinterpret_cast<char *>(&SrcLen), sizeof(SrcLen));
    ofs.write(FileData.get(), FileLen);
    ofs.close();

#if FILE_SECURE_DEBUG_LOG_LEVEL < 3
    Basefun::LinuxSystem(("rm -f \"" + FileName + "\"").c_str());
#endif
    if (m_NameList.count(FileName) == 0)
    {
        m_NameList.emplace(FileName, EncryptName);
    }

    return WT_OK;
}

int FileSecure::DecryptFile(string FileName, string &DecryptName)
{
    if (m_Vaild == false)
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FileSecure Vaild false" << endl;
#endif
        // 附加当前目录
        if (FileName.size() > 0 && FileName[0] != '/')
        {
            DecryptName = GetDir() + '/' + FileName;
        }
        else
        {
            DecryptName = FileName;
        }
        return WT_VERSION_ERROR;
    }

    string EncryptName;
    string TempName;
    if (false == DecryptCheckFile(FileName, EncryptName, TempName))
    {
        // 附加当前目录
        if (FileName.size() > 0 && FileName[0] != '/')
        {
            DecryptName = GetDir() + '/' + FileName;
        }
        else
        {
            DecryptName = FileName;
        }
        return -1;
    }

    int SrcLen = 0;
    std::shared_ptr<char[]> FileData(nullptr);
    if (Decrypt(EncryptName, FileData, SrcLen) != WT_OK)
    {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 0
        WTLog::Instance().WriteLog(LOG_DEBUG, "Function %s,Line %d: Open file %s, %s WT_FILE_OPEN_ERROR\n",
               __FUNCTION__, __LINE__, FileName.c_str(), EncryptName.c_str());
#elif FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().WriteLog(LOG_DEBUG, "Function %s,Line %d: Open file WT_FILE_OPEN_ERROR\n", __FUNCTION__, __LINE__);
#endif
        if (access(EncryptName.c_str(), F_OK) != 0 && access(FileName.c_str(), F_OK) == 0)
        {
            // 不存在加密文件缺存在明文文件
            DecryptName = FileName;
            return -1;
        }
        else
        {
            return WT_FILE_OPEN_ERROR;
        }
    }
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DecryptFile" << Pout(EncryptName) << Pout(SrcLen) << endl;
#endif
    ofstream ofs(TempName, std::ios::binary);
    if (!ofs)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Function %s,Line %d: Open file %s WT_FILE_OPEN_ERROR\n",
               __FUNCTION__, __LINE__, TempName.c_str());
        return WT_FILE_OPEN_ERROR;
    }
    ofs.write(FileData.get(), SrcLen);
    ofs.close();
    DecryptName = TempName;
    return WT_OK;
}

string FileSecure::EncryptFileName(string FileName)
{
    // 加密数据
    int DataLen = (FileName.size() + 3) & (~3);
    std::unique_ptr<char[]> EncryptData(new (std::nothrow) char[DataLen]());
    memcpy(EncryptData.get(), FileName.c_str(), FileName.size());
    Secure::Encrypt(reinterpret_cast<int *>(EncryptData.get()), DataLen / 4, m_FilePasswd);

    // 转换为字符串
    std::unique_ptr<char[]> EncryptNameBuf(new (std::nothrow) char[2 * DataLen]());
    Char2Str(EncryptNameBuf.get(), EncryptData.get(), DataLen);

    // 输出返回

    string EncryptName(EncryptNameBuf.get(), 2 * DataLen);
#if FILE_SECURE_DEBUG_LOG_LEVEL > 3
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "EncryptFileName:" << Pout(FileName) << Pout(EncryptName) << endl;
#endif
    return (EncryptName + ENCRYPTO_EXT_NAME);
}

string FileSecure::DecryptFileName(string EncryptName)
{
    size_t Pos = EncryptName.rfind(ENCRYPTO_EXT_NAME);
    if (string::npos != Pos)
    {
        EncryptName = EncryptName.substr(0, Pos);
    }
    // 字符串转换为加密数据
    int DataLen = EncryptName.size() / 2;
    std::unique_ptr<char[]> EncryptData(new (std::nothrow) char[DataLen]());
    Str2Char(EncryptData.get(), EncryptName.c_str(), EncryptName.size());

    // 解密数据
    std::unique_ptr<char[]> DecryptData(new (std::nothrow) char[DataLen]());
    Secure::Decrypt(reinterpret_cast<int *>(EncryptData.get()), DataLen / 4, m_FilePasswd);

    // 输出返回
    string DecryptName(EncryptData.get(), DataLen);
#if FILE_SECURE_DEBUG_LOG_LEVEL > 3
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DecryptFileName:" << Pout(EncryptName) << Pout(DecryptName) << endl;
#endif
    return DecryptName;
}

string FileSecure::GetTmpWave(void)
{
    if (access(FILE_SECURE_TMP_PATH, F_OK) == 0)
    {
        return FILE_SECURE_TMP_PATH;
    }
    else
    {
        return "/tmp";
    }
}

void FileSecure::Char2Str(char *Dest, const char *Src, const int SrcLen)
{
    int Bit4 = 0;
    for (int i = 0; i < SrcLen; i++)
    {
        Bit4 = (Src[i] >> 4) & 0xF;
        if (Bit4 >= 10)
            Dest[2 * i] = Bit4 - 10 + 'A';
        else
            Dest[2 * i] = Bit4 + '0';

        Bit4 = Src[i] & 0xF;
        if (Bit4 >= 10)
            Dest[2 * i + 1] = Bit4 - 10 + 'A';
        else
            Dest[2 * i + 1] = Bit4 + '0';
    }
}

void FileSecure::Str2Char(char *Dest, const char *Src, const int Length)
{
    int Count = Length / 2;
    for (int i = 0; i < Count; i++)
    {
        if (*Src >= 'A')
            Dest[i] = (*Src - 'A' + 10) << 4;
        else
            Dest[i] = (*Src - '0') << 4;
        Src++;
        if (*Src >= 'A')
            Dest[i] += (*Src - 'A' + 10);
        else
            Dest[i] += (*Src - '0');
        Src++;
    }
}

bool FileSecure::CheckFile(string FileName, string &EncryptName, int Force)
{
    bool IsSecure = false;
    if (m_NameList.count(FileName) > 0)
    {
        EncryptName = m_NameList[FileName];
        IsSecure = true;
    }
    else if (true == Force)
    {
        string Name = FileName;
        string Path;
        auto Pos = FileName.find_last_of("/");
        if (Pos != string::npos && Pos > 0)
        {
            Name = FileName.substr(Pos + 1);
            Path = FileName.substr(0, Pos + 1);
        }
        EncryptName = Path + EncryptFileName(Name);
        IsSecure = true;
    }
    else
    {
        string Name = FileName;
        string Path;
        auto Pos = FileName.find_last_of("/");
        if (Pos != string::npos && Pos > 0)
        {
            Name = FileName.substr(Pos + 1);
            Path = FileName.substr(0, Pos + 1);
            string PathName = FileName;
            while (Pos != string::npos && Pos > 0)
            {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 3
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CheckFile Path:" << Pout(PathName) << '\n';
#endif
                PathName = PathName.substr(0, Pos);
                if (m_PathList.count(PathName))
                {
#if FILE_SECURE_DEBUG_LOG_LEVEL > 3
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CheckFile Path:" << Pout(Path) << Pout(Name) << '\n';
#endif
                    EncryptName = Path + EncryptFileName(Name);
                    IsSecure = true;
                    break;
                }
                Pos = PathName.find_last_of("/");
            }
        }
    }
    if (false == IsSecure)
    {
        EncryptName = FileName;
    }
#if FILE_SECURE_DEBUG_LOG_LEVEL > 1
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "EncryptFile CheckFile:\n"
         << Pout(FileName) << Pout(EncryptName) << Pout(IsSecure) << endl;
#endif
    return IsSecure;
}

bool FileSecure::DecryptCheckFile(string FileName, string &EncryptName, string &TempName)
{
    // 附加当前目录
    if (FileName.size() > 0 && FileName[0] != '/')
    {
        FileName = GetDir() + '/' + FileName;
    }

    if (CheckFile(FileName, EncryptName, false))
    {
        size_t Pos = FileName.find_last_of(".");
        string ExtName;
        if (string::npos != Pos)
        {
            ExtName = FileName.substr(Pos);
        }

        unsigned char Random[4];
        char RandomName[sizeof(Random) * 2];
        Secure::GenerateRandom(Random, sizeof(Random));
        Char2Str(RandomName, reinterpret_cast<char *>(Random), sizeof(Random));
        TempName = GetTmpWave() + "/d" + RandomName + ExtName;
#if FILE_SECURE_DEBUG_LOG_LEVEL > 2
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DecryptCheckFile:" << Pout(FileName) << Pout(TempName) << endl;
#elif FILE_SECURE_DEBUG_LOG_LEVEL > 1
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DecryptCheckFile:" << Pout(TempName) << endl;
#endif
        return true;
    }
    else
    {
        TempName = EncryptName = FileName;
        return false;
    }
}

void FileSecure::DecryptPasswd(void)
{
    auto char2int = [](char ch) -> char
    {
        char temp = 0;
        if (ch >= '0' && ch <= '9')
            temp = ch - '0';
        else if (ch >= 'a' && ch <= 'f')
            temp = ch - 'a' + 10;
        else if (ch >= 'A' && ch <= 'F')
            temp = ch - 'A' + 10;
        return temp;
    };

    if (m_Vaild == false)
    {
        memset(m_DecryptPasswd, 0, sizeof(m_DecryptPasswd));
        return;
    }

    int CryptogramLen = sizeof(m_DecryptPasswd);
    memcpy(m_DecryptPasswd, m_FilePasswd, sizeof(m_DecryptPasswd));
    char *Src = reinterpret_cast<char *>(m_DecryptPasswd);
    char *Dst = reinterpret_cast<char *>(m_DecryptPasswd);

    unsigned int index;
    for (unsigned int i = 0; i < CryptogramLen; i++)
    {
        index = (Src[i] & 0xF) + i * 16 * 2;
        Dst[i] = char2int(CryptoTable[index]);

        index = ((Src[i] >> 4) & 0xF) + i * 16 * 2 + 16;
        Dst[i] += char2int(CryptoTable[index]) * 16;
    }
#if FILE_SECURE_DEBUG_LOG_LEVEL > 3
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << hex;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(m_FilePasswd[0]) << Pout(m_FilePasswd[1]) << Pout(m_FilePasswd[2]) << Pout(m_FilePasswd[3]) << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(m_DecryptPasswd[0]) << Pout(m_DecryptPasswd[1]) << Pout(m_DecryptPasswd[2]) << Pout(m_DecryptPasswd[3]) << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << dec;
#endif
    return;
}
