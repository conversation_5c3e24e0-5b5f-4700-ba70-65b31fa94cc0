/*
 * scpi_gen_CBF.h
 *
 *  Created on: 2023-04-28
 *      Author: Administrator
 */

#ifndef SCPI_GEN_CBF_H_
#define SCPI_GEN_CBF_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif

    //set
    scpi_result_t SetCBF_FeedBackBfEnable(scpi_t *context);
    scpi_result_t SetCBF_MUFlag(scpi_t *context);
    scpi_result_t SetCBF_NumberofCarrierGrouping(scpi_t *context);
    scpi_result_t SetCBF_CodebookIndex(scpi_t *context);
    scpi_result_t SetCBF_HMatrix(scpi_t *context);

    //get
    scpi_result_t GetCBF_CompressReportFieldArb(scpi_t *context);
    scpi_result_t GetCBF_MuExclusiveReportFieldArb(scpi_t *context);
    scpi_result_t GetCBF_HE_RUStartnEndIndex(scpi_t *context);
    scpi_result_t GetCBF_ETH_PartialBWInfo(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif /* SCPI_GEN_CHANNEL_MODE_H_ */