//*****************************************************************************
//  File: basefun.cpp
//  公共基础功能函数集合
//  Data: 2016.9.1
//*****************************************************************************

#include <stdio.h>
#include <stdarg.h>
#include <iostream>
#include <time.h>
#include <fstream>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <fcntl.h>
#include <ctype.h>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <dirent.h>

#include <arpa/inet.h>  // ntohs
#include "basefun.h"
#include "wterror.h"
#include "wtlog.h"

using namespace std;

int Basefun::GetSysTime(TIME_TYPE *tm_type)
{
    time_t Timep;
    struct tm *t_tm;

    time(&Timep);
    t_tm = localtime(&Timep);
    if(t_tm == NULL)
    {
        return WT_ERROR;
    }
    tm_type->sec = t_tm->tm_sec;
    tm_type->min = t_tm->tm_min;
    tm_type->hour = t_tm->tm_hour;
    tm_type->mday = t_tm->tm_mday;
    tm_type->mon = t_tm->tm_mon + 1;
    tm_type->year = t_tm->tm_year + 1900;

    return WT_OK;
}

u64 Basefun::TimeType2Seconds(const TIME_TYPE *tm_type)
{
    struct tm t_tm;

    t_tm.tm_sec = tm_type->sec;
    t_tm.tm_min = tm_type->min;
    t_tm.tm_hour = tm_type->hour;
    t_tm.tm_mday = tm_type->mday;
    t_tm.tm_mon = tm_type->mon - 1;
    t_tm.tm_year = tm_type->year - 1900;
    t_tm.tm_isdst = 0;
    return mktime(&t_tm);
}

int Basefun::Seconds2TimeType(u64 Seconds, TIME_TYPE *tm_type)
{
    time_t Timep;
    struct tm *t_tm;

    Timep = Seconds;
    t_tm = localtime(&Timep);
    if(t_tm == NULL)
    {
        return WT_ERROR;
    }
    tm_type->sec = t_tm->tm_sec;
    tm_type->min = t_tm->tm_min;
    tm_type->hour = t_tm->tm_hour;
    tm_type->mday = t_tm->tm_mday;
    tm_type->mon = t_tm->tm_mon + 1;
    tm_type->year = t_tm->tm_year + 1900;

    return WT_OK;
}

int Basefun::WriteFile(const char *FileName, u8 *Buf, int Size)
{
    if (!FileName || !Buf || Size <= 0) {
        wtlog::error("Invalid parameters: FileName=%p, Buf=%p, Size=%d", 
            FileName, Buf, Size);
        return -1;
    }

    FILE *fp = NULL;    /* 用于打开指定的文件 */
    int Ret = 0;
    const int CHUNK_SIZE = 4096;  // 使用合适的块大小
    
    fp = fopen(FileName, "wb+e");
    if (!fp) {
        wtlog::error("Failed to open file: %s", FileName);
        return -1;
    }

    // 分块写入
    int remaining = Size;
    u8* current = Buf;
    int total_written = 0;
    
    while (remaining > 0) {
        int chunk = (remaining > CHUNK_SIZE) ? CHUNK_SIZE : remaining;
        Ret = fwrite(current, 1, chunk, fp);  // 注意参数顺序
        if (Ret != chunk) {
            wtlog::error("Write failed: expected=%d, actual=%d, total_written=%d", 
                chunk, Ret, total_written);
            fclose(fp);
            return -1;
        }
        
        remaining -= chunk;
        current += chunk;
        total_written += chunk;
    }

    if (fflush(fp) != 0) {
        wtlog::error("Failed to flush file");
        fclose(fp);
        return -1;
    }

    if (fsync(fileno(fp)) != 0) {
        wtlog::error("Failed to sync file");
        fclose(fp);
        return -1;
    }

    fclose(fp);
    return total_written;
}

int Basefun::ReadFile(const char *FileName, u8 *Buf, int Size)
{
    if (!FileName || !Buf || Size <= 0) {
        wtlog::error("Invalid parameters");
        return -1;
    }

    FILE *fp = fopen(FileName, "rb+e");
    if (!fp) {
        wtlog::error("Failed to open file: %s", FileName);
        return -1;
    }

    // 获取文件大小
    fseek(fp, 0, SEEK_END);
    long file_size = ftell(fp);
    fseek(fp, 0, SEEK_SET);

    if (file_size > Size) {
        wtlog::error("File size %ld exceeds buffer size %d", 
            file_size, Size);
        fclose(fp);
        return -1;
    }

    int Ret = fread(Buf, sizeof(char), file_size, fp);
    fclose(fp);

    if (Ret != file_size) {
        wtlog::error("Read failed: expected=%ld, actual=%d", file_size, Ret);
        return -1;
    }

    return Ret;
}

int Basefun::GetLine(char *Line, u32 Len, char **ppBuf)
{
    int BufSize = strlen(*ppBuf);
    int Ret = 0;
    int i;

    if (BufSize == 0)
    {
        return -1;
    }
    memset(Line, 0, Len);

    for (i = 0; i < BufSize; i++)
    {
        if (((*ppBuf)[i] == '\n') || ((*ppBuf)[i] == '\r'))
        {
            break;
        }
    }
    /* 去掉换行和回车 */
    while (((*ppBuf)[i] == '\n') || ((*ppBuf)[i] == '\r'))
    {
        i++;
    }

    if (i >= (signed)Len)
    {
        memcpy(Line, *ppBuf, Len);
        *ppBuf += Len;
    }
    else
    {
        if (i >= BufSize)   /* 已经到达最后一行 */
        {
            memcpy(Line, *ppBuf, BufSize);
            *ppBuf += BufSize;
        }
        else
        {
            memcpy(Line, *ppBuf, i);
            *ppBuf += i;
        }
    }

    return Ret;
}

int Basefun::CompareDouble(double dA, double dB, double dEpsilon)
{
    if (fabs(dA - dB) < dEpsilon)
    {
        return 0;
    }
    else if ((dA - dB) > dEpsilon)
    {
        return 1;
    }
    else
    {
        return -1;
    }
}

int Basefun::Near(int A, int B)
{
    int left, right, num;

    left = A % B;
    right = B - left;

    num = left > right ? (A + right) : (A - left);

    return num; 
}

/*******************************************************************************
函数: string_to_upper()
功能: 把输入的字符串中小写转换为小写，数字和大写的不转换
参数:
str: 需要转换的字符串
返回:
None
*******************************************************************************/
void Basefun::string_to_upper(char *str)
{
    int len = strlen(str);

    for (int i = 0; i<len; i++)
    {
        str[i] = toupper(str[i]);
    }
}

/*******************************************************************************
函数: shell_exec()
功能: 执行shell命令并返回操作结果
参数:
cmd: shell命令
返回:
std::string：shell命令的操作结果
*******************************************************************************/
std::string Basefun::shell_exec(const char *cmd)
{
    FILE *pipe = popen(cmd, "r");
    if (!pipe)
    {
        printf("popen errno=%d\n", errno);
        return "ERROR";
    }
    char buffer[128];
    std::string result = "";
    while (!feof(pipe))
    {
        if (fgets(buffer, 128, pipe) != NULL)
        {
            result += buffer;
        }
    }
    pclose(pipe);
    return result;
}

//*******************************************************************************
// 函数: HexDump()
// 功能: 显示内存中的数据
// 参数 [IN]：Desc: 描述HexDump目的的字符串  
//            Addr: HexDump内存地址
//            Len: HexDump的字节数
// 返回: 无
//*******************************************************************************
void Basefun::HexDump(const char *Desc, const void *Addr, int Len)
{
    int i = 0;
    unsigned char Buff[17] = {0};
    const unsigned char *Pc = (unsigned char *)Addr;

    if (Desc != NULL)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "%s\n", Desc);
    }

    if (Addr == NULL)
    {
        return;
    }

    for (i = 0; i < Len; ++i)
    {
        if ((i % 16) == 0) // 行尾处理
        {
            if (i != 0)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "    %s\n", Buff); // 4个空格 + 行尾字符串
            }

            WTLog::Instance().WriteLog(LOG_DEBUG, "  %04x", i); // 行首 offset
        }

        WTLog::Instance().WriteLog(LOG_DEBUG, " %02X", Pc[i]);   // 空格 + Hex

        if ((Pc[i] <0x20) || (Pc[i]> 0x7e)) // 非可视化字符
        {
            Buff[i % 16] = '.';
        }
        else
        {
            Buff[i % 16] = Pc[i];
        }

        Buff[(i % 16) +1] = 0;  // \0
    }

    // 最后一行不足就补空格
    while ((i % 16)!= 0)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "   ");
        i++;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "    %s\n", Buff); // 4个空格 + 行尾字符串
}

//*******************************************************************************
// 函数: GetSockPeerInfo()
// 功能: 获取socket描述符的peer端的IP, 端口信息
// 参数 [IN]：Fd: socket描述符 
// 参数 [out]:Ip: peer端的IP地址
//            Port: peer端的端口
// 返回: 无
//*******************************************************************************
void Basefun::GetSockPeerInfo(int Fd, char Ip[16], int &Port)
{
    struct sockaddr_in ClientAddr;
    socklen_t Len = sizeof(ClientAddr);

    memset(&ClientAddr, 0, Len);

    getpeername(Fd, (struct sockaddr *)&ClientAddr, &Len);

    sprintf(Ip, "%s", inet_ntoa(ClientAddr.sin_addr));
    Port = ntohs(ClientAddr.sin_port);
}

//*******************************************************************************
// 函数: GetSockInfo()
// 功能: 获取socket描述符的发起端（local）使用的IP和端口信息
// 参数 [IN]：Fd: socket描述符
// 参数 [out]:Ip: local端的IP地址
//            Port: local端使用的端口
// 返回: 无
//*******************************************************************************
void Basefun::GetSockInfo(int Fd, char Ip[16], int &Port)
{
    struct sockaddr_in ClientAddr;
    socklen_t Len = sizeof(ClientAddr);

    memset(&ClientAddr, 0, Len);

    getsockname(Fd, (struct sockaddr *)&ClientAddr, &Len);

    sprintf(Ip, "%s", inet_ntoa(ClientAddr.sin_addr));
    Port = ntohs(ClientAddr.sin_port);
}

//*******************************************************************************
// 函数: CompareDoubleAccuracy1K()
// 功能: 比较两个double数据的大小，精度1K。
// 参数  [IN]：dA dB: 要比较的两个数。
// 返回: 比较结果，dA等于dB返回0，dA大于dB返回1，dA小于dB返回-1， 
//*******************************************************************************
int Basefun::CompareDoubleAccuracy1K(double dA, double dB)
{
    const   double   dEpsilon   =   1000;
    if (fabs(dA - dB) < dEpsilon)
    {
        return 0;
    }
    else if ((dA - dB) > 0)
    {
        return 1;
    }
    else
    {
        return -1;
    }
}

void Basefun::LinuxSystem(const char *cmd)
{
    int _ = system(cmd);
    (void)_;
}

int Basefun::GetFreqIndex(int Value, const int *Array, int Size)
{
    int i = 0;
    for (i = 0; i < Size; i++)
    {
        if (Value <= Array[i])
        {
            return i;
        }
    }
    return Size - 1;
}

int Basefun::GetItemVal(const string &Line, int &Val, int Index)
{
    int Ret = WT_OK;
    string Str;

    Ret = GetItemVal(Line, Str, Index);

    if (Ret != WT_OK || Str[0] == '*' || Str[0] == ',' || Str[0] < 0x20)
    {
        Val = -1;
    }
    else
    {
        try
        {
            Val = stoul(Str, nullptr, 0);
        }
        catch (const std::exception &e)
        {
            std::cerr << __FUNCTION__ << __LINE__ << e.what() << '\n';
            Val = -1;
            Ret = WT_ARG_ERROR;
        }
    }
    return Ret;
}

int Basefun::GetItemVal(const string &Line, string &Val, int Index)
{
	int Ret = WT_OK;
	int CommaCount = 0;
	int i = 0;

	for (i = 0; i < Line.length(); i++)
	{
		if (CommaCount == Index)
		{
			break;
		}
		if (Line[i] == ',')
		{
			CommaCount++;
		}
	}
	if (CommaCount != Index)
	{
		Ret = -1;
	}
	else
	{
		/* 去掉空格和制表符 */
		while ((Line[i] == ' ') || (Line[i] == '\t'))
		{
			i++;
		}
		Val = Line.substr(i);
	}
	return Ret;
}

int Basefun::CreateDir(const char *szPathName)
{
    char DirName[256] = {0};
    strcpy(DirName, szPathName);
    int i, len = strlen(DirName);
    for (i = 1; i < len; i++)
    {
        if (DirName[i] == '/')
        {
            DirName[i] = 0;
            if (access(DirName, F_OK) != 0)
            {
                if (mkdir(DirName, 0755) == -1)
                {
                    WTLog::Instance().WriteLog(LOG_DEBUG, "mkdir   error\n");
                    return -1;
                }
            }
            DirName[i] = '/';
        }
    }

    return 0;
}

//-1:存在 0:不存在
int Basefun::IsFolderExist(const char *path)
{
    DIR *dp = NULL;
    if ((dp = opendir(path)) == NULL)
    {
        return 0;
    }

    closedir(dp);
    return -1;
}

//-1:存在 0:不存在
int Basefun::IsFileExist(const char *path)
{
    return !access(path, F_OK);
}

int Basefun::RemoveDir(const char *dir)
{
    char cur_dir[] = ".";
    char up_dir[] = "..";
    char dir_name[128];
    DIR *dirp;
    struct dirent *dp;
    struct stat dir_stat;

    // 参数传递进来的目录不存在，直接返回
    if (0 != access(dir, F_OK))
    {
        return 0;
    }

    // 获取目录属性失败，返回错误
    if (0 > stat(dir, &dir_stat))
    {
        perror("get directory stat error");
        return -1;
    }

    if (S_ISREG(dir_stat.st_mode))
    {
        // 普通文件直接删除
        remove(dir);
    }
    else if (S_ISDIR(dir_stat.st_mode))
    {
        // 目录文件，递归删除目录中内容
        dirp = opendir(dir);
        while ((dp = readdir(dirp)) != NULL)
        {
            // 忽略 . 和 ..
            if ((0 == strcmp(cur_dir, dp->d_name)) || (0 == strcmp(up_dir, dp->d_name)))
            {
                continue;
            }

            sprintf(dir_name, "%s/%s", dir, dp->d_name);
            RemoveDir(dir_name); // 递归调用
        }
        closedir(dirp);

        rmdir(dir); // 删除空目录
    }
    else
    {
        perror("unknow file type!");
    }

    return 0;
}

/////////////////////////////////////////////////////////////////////////////////////////
// 自定义打印函数start
/////////////////////////////////////////////////////////////////////////////////////////

#define _SIGN 2     // Unsigned/signed long
#define _SPECIAL 32 // 0x
#define _LARGE 64   // 是否大写
#define _BUF_MAX_SIZE 2048

static char digits[] = "0123456789abcdefghijklmnopqrstuvwxyz";
static char upper_digits[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

static int StrToNum(const char **str)
{
    int number = 0;
    while (**str >= '0' && **str <= '9')
    {
        number = number * 10 + **str - '0';
        (*str)++;
    }
    return number;
}

static void DoubleToStr(char *value, double valNumber, int precision)
{
    if (value == NULL)
    {
        return;
    }
    /*if (precision > 15)
    {
        precision = 15;
    }*/
    double number = valNumber > 0 ? valNumber : -valNumber;
    int i = 0;
    int j = 0;

    if (valNumber == 0)
    {
        value[i++] = '0';
        value[i++] = '.';
        for (j = 0; j < precision; j++)
        {
            value[i++] = '0';
        }
        value[i] = '\0';
        return;
    }
    else if (valNumber < 0)
    {
        value[i++] = '-';
    }

    // 四舍五入
    // if (precision <= 15)
    {
        double tmp = 1;
        for (j = 0; j < precision + 1; j++)
        {
            tmp /= 10;
        }
        // WTLog::Instance().WriteLog(LOG_DEBUG, "%.12lf\n", tmp);
        number += tmp * 5;
    }

    unsigned long long whole = (unsigned long long)number; // 提取整数部分
    double decimal = number - whole;                       // 提取小数部分

    // 将整数部分转换为字符串
    if (whole == 0)
    {
        value[i++] = '0';
    }
    else
    {
        while (whole > 0)
        {
            value[i++] = '0' + whole % 10;
            whole /= 10;
        }
    }
    value[i] = '\0'; // 添加字符串结尾的空字符

    // 反转整数部分字符串
    int start = valNumber > 0 ? 0 : 1;
    int end = i - 1;
    char temp = '0';
    while (start < end)
    {
        temp = value[start];
        value[start] = value[end];
        value[end] = temp;
        start++;
        end--;
    }

    // 添加小数点
    value[i++] = '.';

    // 将小数部分转换为字符串
    for (j = 0; j < precision; j++)
    {
        decimal *= 10;
        value[i++] = '0' + (int)decimal;
        decimal -= (int)decimal;
    }
    value[i] = '\0'; // 添加字符串结尾的空字符
}

static void NumToStr(char **str, long long num, int base, int size, int type)
{
    char *dig = digits;
    char tmp[1024];
    char sign = 0;
    int i = 0;
    if (type & _LARGE)
    {
        dig = upper_digits;
    }

    if (type & _SIGN)
    {
        if (num < 0)
        {
            sign = '-';
        }
    }

    num = num > 0 ? num : -num;
    if (num == 0)
    {
        tmp[i++] = '0';
    }
    else
    {
        while (num)
        {
            tmp[i++] = dig[(unsigned long long)num % base];
            num = (unsigned long long)num / base;
        }
    }

    tmp[i] = '\0';

    size -= i;
    if (sign)
    {
        size -= 1;
    }
    if (type & _SPECIAL)
    {
        size -= 2;
    }
    while (size > 0)
    {
        **str = ' ';
        (*str)++;
        size--;
    }

    if (type & _SPECIAL)
    {
        **str = '0';
        (*str)++;
        if (type & _LARGE)
        {
            **str = 'X';
            (*str)++;
        }
        else
        {
            **str = 'x';
            (*str)++;
        }
    }
    if (sign)
    {
        **str = sign;
        (*str)++;
    }
    while (i-- > 0)
    {
        **str = tmp[i];
        (*str)++;
    }
}

static void _csprintf(char *buf, const char *format, va_list args)
{
    // va_list args;
    // va_start(args, format);

    char *str = NULL;
    int filedWidth = 0; // 数据宽度
    int precision = 6;  // 浮点型精度
    int base = 10;      // 进制
    int flags = 0;
    char *s = NULL;
    char tmp[128];
    unsigned long long num = 0;
    double fnum = 0;

    for (str = buf; *format; format++)
    {
        if (*format != '%')
        {
            *str = *format;
            str++;
            continue;
        }
        flags = 0;
        base = 10;
        format++;
        filedWidth = -1;
        if (*format >= '0' && *format <= '9')
        {
            filedWidth = StrToNum(&format);
        }

        precision = -1;
        if (*format == '.')
        {
            format++;
            if (*format >= '0' && *format <= '9')
            {
                precision = StrToNum(&format);
            }
        }

        switch (*format)
        {
        case 'd':
            flags |= _SIGN;
            num = va_arg(args, int);
            NumToStr(&str, num, base, filedWidth, flags);
            continue;
        case 'f':
            fnum = va_arg(args, double);
            if (precision <= 0)
            {
                precision = 6;
            }
            DoubleToStr(tmp, fnum, precision);
            filedWidth -= strlen(tmp);
            while (filedWidth > 0)
            {
                *str = ' ';
                str++;
                filedWidth--;
            }
            s = tmp;
            while (*s)
            {
                *str = *s;
                str++;
                s++;
            }
            continue;
        case 's':
            s = va_arg(args, char *);
            while (filedWidth > 0)
            {
                *str = ' ';
                str++;
                filedWidth--;
            }
            while (*s)
            {
                *str = *s;
                str++;
                s++;
            }
            continue;
        case 'p':
            flags |= _SPECIAL;
            base = 16;
            num = va_arg(args, unsigned long long);
            if (num == 0)
            {
                char defaultStr[] = "(nil)";
                for (int i = 0; defaultStr[i] != '\0'; i++)
                {
                    *str = defaultStr[i];
                    str++;
                }
            }
            else
            {
                NumToStr(&str, num, base, filedWidth, flags);
            }
            continue;
        case 'x':
            base = 16;
            num = va_arg(args, unsigned long long);
            NumToStr(&str, num, base, filedWidth, flags);
            continue;
        case 'l':
            format++;
            if (*format == 'd')
            {
                flags |= _SIGN;
                num = va_arg(args, long);
                NumToStr(&str, num, base, filedWidth, flags);
            }
            else if (*format == 'f')
            {
                fnum = va_arg(args, double);
                if (precision <= 0)
                {
                    precision = 6;
                }
                DoubleToStr(tmp, fnum, precision);
                filedWidth -= strlen(tmp);
                while (filedWidth > 0)
                {
                    *str = ' ';
                    str++;
                    filedWidth--;
                }
                s = tmp;
                while (*s)
                {
                    *str = *s;
                    str++;
                    s++;
                }
            }
            else if (*format == 'l')
            {
                format++;
                if (*format == 'd')
                {
                    flags |= _SIGN;
                    num = va_arg(args, long long);
                    NumToStr(&str, num, base, filedWidth, flags);
                }
            }
            continue;
        default:
            if (*format != '%')
            {
                *str++ = '%';
            }
            if (*format)
            {
                *str++ = *format;
            }
            else
            {
                --format;
            }
            continue;
        }
    }
    *str = '\0';
    va_end(args);
}

int Basefun::csprintf(char *buf, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    _csprintf(buf, format, args);
    va_end(args);
    return 0;
}

int Basefun::cfprintf(int fd, const char *format, ...)
{
    char buf[_BUF_MAX_SIZE] = {0};
    va_list args;
    va_start(args, format);
    _csprintf(buf, format, args);
    va_end(args);
    return write(fd, buf, strlen(buf));
}

/////////////////////////////////////////////////////////////////////////////////////////
// 自定义打印函数end
/////////////////////////////////////////////////////////////////////////////////////////
