#include "wtswitch_vd.h"
#include "wtswitchdefine.h"
// VSG接口
static int Vsg_Select_8080(int SubPort, int PortState);
static int Vsg_Select_8080_Left(int SubPort, int PortState);
static int Vsg_Select_PI_PA(int SubPort, int PortState);
static int Vsg_Select_Loop(int SubPort, int PortState);
static int Vsg_Select_Pac(int SubPort, int PortState, int NextStep);
static int Vsg_Select_TX_RX(int SubPort, int PortState);
static int Vsg_Select_PAC_PI_PA(int SubPort, int PortState);

// VSA接口
static int Vsa_Select_8080(int SubPort, int PortState);
static int Vsa_Select_8080_Left(int SubPort, int PortState);
static int Vsa_Select_PI_PA(int SubPort, int PortState);
static int Vsa_Select_Loop(int SubPort, int PortState);
static int Vsa_Select_Pac(int SubPort, int PortState);
static int Vsa_Select_TX_RX(int SubPort, int PortState);
static int Vsa_Select_PAC_PI_PA(int SubPort, int PortState);

// SubPort: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsa_Select_SW_VD(int SubPort, int PortState)
{
    return Vsa_Select_8080(SubPort, PortState);
}

// SubPort: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsg_Select_SW_VD(int SubPort, int PortState)
{
    return Vsg_Select_8080(SubPort, PortState);
}

// SubPort: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsg_Select_CTL3_VD(int SubPort, int PortState)
{
    return Vsg_Select_Pac(SubPort, PortState, false);
}
enum WT448SwitchBit
{
    RF_TX1_CTL3,
    RF_TX1_CTL1,
    RF_TX_CTL5,
    RF_TX1_CTL2,
    RF_TX2_CTL7,
    RF_TX2_CTL6,
    RF_TX2_CTL9,
    RF_TX2_CTL8,

    RX1_P1_CTL1,
    TX1_P1_CTL1,
    RX1_P2_CTL1,
    TX1_P2_CTL1,
    RX2_P3_CTL1,
    TX2_P3_CTL1,
    RX2_P4_CTL1,
    TX2_P4_CTL1,

    RF_RX2_CTL6,
    RF_RX2_CTL7,
    RF_RX2_CTL8,
    RF_RX2_CTL9,
    RF_RX_CTL5,
    RF_RX1_CTL2,
    RF_RX1_CTL3,
    RF_RX1_CTL4,

    // shift1
    RF_RX1_CTL1,
    // "Port1_A5",   // ATT
    // "Port1_A4",
    // "Port1_A3",
    // "Port1_A2",
    // "Port1_A1",
    // "Port1_A0",
    // "TP8",

    P1_R_CTL6,
    RX1_P1_CTL2,
    P1_B_CTL7,
    P1_T_CTL3,
    P1_R_CTL4,
    //"Reversed",
    TX1_P1_CTL2,
    P1_S1_CTL2,

    RX1_S_CTL,
    // "Port2_A5", // ATT
    // "Port2_A4",
    // "Port2_A3",
    // "Port2_A2",
    // "Port2_A1",
    // "Port2_A0",
    P2_R_CTL4,

    P2_R_CTL6,
    RX1_P2_CTL2,
    P2_B_CTL7,
    TX1_CTL,
    P2_T_CTL3,
    //"Reversed",
    P2_S1_CTL2,
    TX1_P2_CTL2,

    // shift2
    P4_R_CTL6,
    RX2_P4_CTL2,
    P4_B_CTL7,
    P4_T_CTL3,
    P4_R_CTL4,
    //"Reversed",
    TX2_P4_CTL2,
    P4_S1_CTL2,

    TX2_CTL,
    // "Port4_A5",  // ATT
    // "Port4_A4",
    // "Port4_A3",
    // "Port4_A2",
    // "Port4_A1",
    // "Port4_A0",
    RF_TX1_CTL4,

    P3_R_CTL6,
    RX2_P3_CTL2,
    P3_B_CTL7,
    RX2_S_CTL,
    P3_T_CTL3,
    //"Reversed",
    TX2_P3_CTL2,
    P3_S1_CTL2,

    P3_R_CTL4,
    // "Port3_A5",  // ATT
    // "Port3_A4",
    // "Port3_A3",
    // "Port3_A2",
    // "Port3_A1",
    // "Port3_A0",
    // "TP6",

    // shift3
    // shift4
    // shift5

    // 42553
    P1_T_CTL5,
    P2_T_CTL5,
    P3_T_CTL5,
    P4_T_CTL5,

    // SW_PA寄存器
    // "Port1_Pa",
    // "Port2_Pa",
    // "Port3_Pa",
    // "Port4_Pa",

    //PAC
    P1_S_FPGA_CTL1,
    P2_S_FPGA_CTL1,
    P3_S_FPGA_CTL1,
    P4_S_FPGA_CTL1,

    WT448Switch_BitDefineMax,
};
#if DEBUG_SHOW_SW_BIT_NAME
// 注意要与WT448SwitchBitName_VD名称一一对应
static char WT448SwitchBitNames[WT448Switch_BitDefineMax][20] = {
    // shift0
    "RF_TX1_CTL3",
    "RF_TX1_CTL1",
    "RF_TX_CTL5",
    "RF_TX1_CTL2",
    "RF_TX2_CTL7",
    "RF_TX2_CTL6",
    "RF_TX2_CTL9",
    "RF_TX2_CTL8",

    "RX1_P1_CTL1",
    "TX1_P1_CTL1",
    "RX1_P2_CTL1",
    "TX1_P2_CTL1",
    "RX2_P3_CTL1",
    "TX2_P3_CTL1",
    "RX2_P4_CTL1",
    "TX2_P4_CTL1",

    "RF_RX2_CTL6",
    "RF_RX2_CTL7",
    "RF_RX2_CTL8",
    "RF_RX2_CTL9",
    "RF_RX_CTL5",
    "RF_RX1_CTL2",
    "RF_RX1_CTL3",
    "RF_RX1_CTL4",

    // shift1
    "RF_RX1_CTL1",
    // "Port1_A5",   // ATT
    // "Port1_A4",
    // "Port1_A3",
    // "Port1_A2",
    // "Port1_A1",
    // "Port1_A0",
    // "TP8",

    "P1_R_CTL6",
    "RX1_P1_CTL2",
    "P1_B_CTL7",
    "P1_T_CTL3",
    "P1_R_CTL4",
    //"Reversed",
    "TX1_P1_CTL2",
    "P1_S1_CTL2",

    "RX1_S_CTL",
    // "Port2_A5", // ATT
    // "Port2_A4",
    // "Port2_A3",
    // "Port2_A2",
    // "Port2_A1",
    // "Port2_A0",
    "P2_R_CTL4",

    "P2_R_CTL6",
    "RX1_P2_CTL2",
    "P2_B_CTL7",
    "TX1_CTL",
    "P2_T_CTL3",
    //"Reversed",
    "P2_S1_CTL2",
    "TX1_P2_CTL2",

    // shift2
    "P4_R_CTL6",
    "RX2_P4_CTL2",
    "P4_B_CTL7",
    "P4_T_CTL3",
    "P4_R_CTL4",
    //"Reversed",
    "TX2_P4_CTL2",
    "P4_S1_CTL2",

    "TX2_CTL",
    // "Port4_A5",  // ATT
    // "Port4_A4",
    // "Port4_A3",
    // "Port4_A2",
    // "Port4_A1",
    // "Port4_A0",
    "RF_TX1_CTL4",

    "P3_R_CTL6",
    "RX2_P3_CTL2",
    "P3_B_CTL7",
    "RX2_S_CTL",
    "P3_T_CTL3",
    //"Reversed",
    "TX2_P3_CTL2",
    "P3_S1_CTL2",

    "P3_R_CTL4",
    // "Port3_A5",  // ATT
    // "Port3_A4",
    // "Port3_A3",
    // "Port3_A2",
    // "Port3_A1",
    // "Port3_A0",
    // "TP6",

    // shift3
    // shift4
    // shift5

    // 42553
    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",

    // SW_PA寄存器
    // "Port1_Pa",
    // "Port2_Pa",
    // "Port3_Pa",
    // "Port4_Pa",

    //PAC
    "P1_S_FPGA_CTL1",
    "P2_S_FPGA_CTL1",
    "P3_S_FPGA_CTL1",
    "P4_S_FPGA_CTL1",
};
#endif


int Vsa_Select_8080(int SubPort, int PortState)
{
    int i = 0;
    int StateMode = WT_SW_STATE_MODE_SISO;
    const int BitMask[] = {RF_RX1_CTL1, RF_RX2_CTL6, RF_RX1_CTL2, RF_RX2_CTL7, RF_RX1_CTL3, RF_RX2_CTL8, RF_RX1_CTL4, RF_RX2_CTL9, RF_RX_CTL5};
    // const int BitSelectInit[8] = {0, 1, 0, 1, 1, 0, 1, 0};
    // const int BitSelect[2][8] = {
    //     {1, 0, 1, 0, 0, 1, 0, 1}, // No Use
    //     {1, 0, 1, 0, 0, 1, 0, 1},
    // };

    const int BitSelectInit[4][9] = {
        {0, 2, 0, 2, 1, 2, 1, 2, 0},
        {0, 2, 0, 2, 1, 2, 1, 2, 0},
        {2, 1, 2, 1, 2, 0, 2, 0, 0},
        {2, 1, 2, 1, 2, 0, 2, 0, 0},
    };

    const int BitSelectSpec[4][9] = {
        {0, 2, 0, 2, 1, 2, 1, 2, 2},
        {0, 2, 0, 2, 1, 2, 1, 2, 2},
        {2, 1, 2, 1, 2, 0, 2, 0, 2},
        {2, 1, 2, 1, 2, 0, 2, 0, 2},
    };

    const int BitSelect8080[4][9] = {
        {1, 0, 1, 0, 0, 0, 0, 0, 1},
        {1, 0, 1, 0, 0, 0, 0, 0, 1},
        {1, 0, 1, 0, 1, 1, 1, 1, 0},
        {1, 0, 1, 0, 1, 1, 1, 1, 0},
    };

    if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
    {
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelectInit[SubPort][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitMask[i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitMask[i]);
                break;
            default:
                break;
            }
        }
    }
    else if (PortState == WT_RF_STATE_SISO)
    {
        StateMode = PortState - WT_RF_STATE_SISO;
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelectSpec[SubPort][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitMask[i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitMask[i]);
                break;
            default:
                break;
            }
        }

        return 0;
    }
    else if (PortState == WT_RF_STATE_8080)
    {
        StateMode = PortState - WT_RF_STATE_SISO;

        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
#if DEBUG && DEBUG_SHOW_SW_BIT_NAME
            if (BitSelect8080[SubPort][i])
            {
                _DR_SW_SETBIT(BitMask[i]);
            }
            else
            {
                _DR_SW_CLEARBIT(BitMask[i]);
            }
#else
            BitSelect8080[SubPort][i] ? _DR_SW_SETBIT(BitMask[i]) : _DR_SW_CLEARBIT(BitMask[i]);
#endif
        }

        return 0;
    }

    return Vsa_Select_8080_Left(SubPort, PortState);
}

int Vsa_Select_8080_Left(int SubPort, int PortState)
{
    int i = 0;
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[] = {RX1_S_CTL, RX2_S_CTL};

    const int BitSelectInit[4][2] = {
        {1, 2},
        {1, 2},
        {2, 0},
        {2, 0},
    };

    const int BitSelect[4][2] = {
        {0, 2},
        {1, 2},
        {2, 0},
        {2, 1},
    };

    if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
    {
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
#if DEBUG && DEBUG_SHOW_SW_BIT_NAME
            if (BitSelectInit[PortIndex][i])
            {
                _DR_SW_SETBIT(BitMask[i]);
            }
            else
            {
                _DR_SW_CLEARBIT(BitMask[i]);
            }
#else
            BitSelectInit[PortIndex][i] ? _DR_SW_SETBIT(BitMask[i]) : _DR_SW_CLEARBIT(BitMask[i]);
#endif
        }
    }
    else if (PortState < WT_RF_STATE_MAX)
    {
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelect[PortIndex][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitMask[i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitMask[i]);
                break;
            default:
                break;
            }
        }
    }

    return Vsa_Select_PI_PA(SubPort, PortState);
}

int Vsa_Select_PI_PA(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[2][4] = {
        {RX1_P1_CTL1, RX1_P2_CTL1, RX2_P3_CTL1, RX2_P4_CTL1},
        {RX1_P1_CTL2, RX1_P2_CTL2, RX2_P3_CTL2, RX2_P4_CTL2},
    };

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[0][PortIndex]);
        _DR_SW_CLEARBIT(BitMask[1][PortIndex]);
        break;

    case WT_RF_STATE_PA_2:
    case WT_RF_STATE_LOOP_PA_2:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_CLEARBIT(BitMask[0][PortIndex]);
        _DR_SW_SETBIT(BitMask[1][PortIndex]);
        break;

    case WT_RF_STATE_PI:
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
        _DR_SW_SETBIT(BitMask[0][PortIndex]);
        _DR_SW_CLEARBIT(BitMask[1][PortIndex]);
        break;
    }

    return Vsa_Select_Loop(SubPort, PortState);
}

int Vsa_Select_Loop(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[2][4] = {
        {P1_R_CTL6, P2_R_CTL6, P3_R_CTL6, P4_R_CTL6},
        {P1_B_CTL7, P2_B_CTL7, P3_B_CTL7, P4_B_CTL7},
    };

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask[0][PortIndex]);
        break;
    default:
        _DR_SW_CLEARBIT(BitMask[0][PortIndex]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_CLEARBIT(BitMask[1][PortIndex]);
        break;

    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask[1][PortIndex]);
        break;

    default:
        break;
    }

    return Vsa_Select_Pac(SubPort, PortState);
}

int Vsa_Select_Pac(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[4] = {P1_R_CTL4, P2_R_CTL4, P3_R_CTL4, P4_R_CTL4};

    const int BitMaskTx[4] = {P1_T_CTL3, P2_T_CTL3, P3_T_CTL3, P4_T_CTL3};

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PI:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        _DR_SW_SETBIT(BitMaskTx[PortIndex]);
        break;

    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_SETBIT(BitMask[PortIndex]);
        break;
    }

    return Vsa_Select_TX_RX(SubPort, PortState);
}

int Vsa_Select_TX_RX(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[4] = {P1_S1_CTL2, P2_S1_CTL2, P3_S1_CTL2, P4_S1_CTL2};

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_SETBIT(BitMask[PortIndex]);
        break;
    }
    return Vsa_Select_PAC_PI_PA(SubPort, PortState);
}

int Vsa_Select_PAC_PI_PA(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[4] = {P1_S_FPGA_CTL1, P2_S_FPGA_CTL1, P3_S_FPGA_CTL1, P4_S_FPGA_CTL1};

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PI:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_SETBIT(BitMask[PortIndex]);
        break;
    }

    return 0;
}

int Vsg_Select_8080(int SubPort, int PortState)
{
    int i = 0;
    int StateMode = WT_SW_STATE_MODE_SISO;
    const int BitMask[] = {RF_TX2_CTL6, RF_TX1_CTL1, RF_TX2_CTL7, RF_TX1_CTL2, RF_TX2_CTL8, RF_TX1_CTL3, RF_TX2_CTL9, RF_TX1_CTL4, RF_TX_CTL5};
    // const int BitSelectInit[8] = {0, 1, 0, 1, 1, 0, 1, 0};
    // const int BitSelect[2][8] = {
    //     {0, 1, 0, 1, 1, 0, 1, 0},
    //     {1, 0, 1, 0, 0, 1, 0, 1},
    // };

    const int BitSelectInit[4][9] = {
        {0, 2, 0, 2, 1, 2, 1, 2, 1},
        {0, 2, 0, 2, 1, 2, 1, 2, 1},
        {2, 1, 2, 1, 2, 0, 2, 0, 1},
        {2, 1, 2, 1, 2, 0, 2, 0, 1},
    };

    const int BitSelectSpec[4][9] = {
        {0, 2, 0, 2, 1, 2, 1, 2, 0},
        {0, 2, 0, 2, 1, 2, 1, 2, 0},
        {2, 1, 2, 1, 2, 0, 2, 0, 1},
        {2, 1, 2, 1, 2, 0, 2, 0, 1},
    };

    const int BitSelect8080[4][9] = {
        {1, 0, 1, 0, 0, 0, 0, 0, 1},
        {1, 0, 1, 0, 0, 0, 0, 0, 1},
        {1, 0, 1, 0, 1, 1, 1, 1, 0},
        {1, 0, 1, 0, 1, 1, 1, 1, 0},
    };

    if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
    {
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelectInit[SubPort][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitSelectInit[SubPort][i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitSelectInit[SubPort][i]);
                break;
            default:
                break;
            }
        }
    }
    else if (PortState == WT_RF_STATE_SISO)
    {
        StateMode = PortState - WT_RF_STATE_SISO;
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelectSpec[SubPort][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitMask[i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitMask[i]);
                break;
            default:
                break;
            }
        }

        return 0;
    }
    else if (PortState == WT_RF_STATE_8080)
    {
        StateMode = PortState - WT_RF_STATE_SISO;
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
#if DEBUG && DEBUG_SHOW_SW_BIT_NAME
            if (BitSelect8080[SubPort][i])
            {
                _DR_SW_SETBIT(BitMask[i]);
            }
            else
            {
                _DR_SW_CLEARBIT(BitMask[i]);
            }
#else
            BitSelect8080[SubPort][i] ? _DR_SW_SETBIT(BitMask[i]) : _DR_SW_CLEARBIT(BitMask[i]);
#endif
        }

        return 0;
    }

    return Vsg_Select_8080_Left(SubPort, PortState);
}

int Vsg_Select_8080_Left(int SubPort, int PortState)
{
    int i = 0;
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[] = {TX1_CTL, TX2_CTL};

    const int BitSelectInit[4][2] = {
        {0, 2},
        {0, 2},
        {2, 1},
        {2, 1},
    };

    const int BitSelect[4][2] = {
        {1, 2},
        {0, 2},
        {2, 1},
        {2, 0},
    };

    if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
    {
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelectInit[PortIndex][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitMask[i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitMask[i]);
                break;
            default:
                break;
            }
        }
    }
    else if (PortState < WT_RF_STATE_MAX)
    {
        for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
        {
            switch (BitSelect[PortIndex][i])
            {
            case 0:
                _DR_SW_CLEARBIT(BitMask[i]);
                break;
            case 1:
                _DR_SW_SETBIT(BitMask[i]);
                break;
            default:
                break;
            }
        }
    }

    return Vsg_Select_PI_PA(SubPort, PortState);
}

int Vsg_Select_PI_PA(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[2][4] = {
        {TX1_P1_CTL1, TX1_P2_CTL1, TX2_P3_CTL1, TX2_P4_CTL1},
        {TX1_P1_CTL2, TX1_P2_CTL2, TX2_P3_CTL2, TX2_P4_CTL2},
    };

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[0][PortIndex]);
        _DR_SW_CLEARBIT(BitMask[1][PortIndex]);

        break;

    case WT_RF_STATE_PI:
    case WT_RF_STATE_PA_2:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_2:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_2:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_SETBIT(BitMask[0][PortIndex]);
        _DR_SW_CLEARBIT(BitMask[1][PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_DET_PA_1:
        _DR_SW_CLEARBIT(BitMask[0][PortIndex]);
        _DR_SW_SETBIT(BitMask[1][PortIndex]);
        break;
    }

    return Vsg_Select_Loop(SubPort, PortState);
}

int Vsg_Select_Loop(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[2][4] = {
        {P1_T_CTL5, P2_T_CTL5, P3_T_CTL5, P4_T_CTL5},
        {P1_B_CTL7, P2_B_CTL7, P3_B_CTL7, P4_B_CTL7},
    };

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:

    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_CLEARBIT(BitMask[0][PortIndex]);
        break;
    default:
        _DR_SW_SETBIT(BitMask[0][PortIndex]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_CLEARBIT(BitMask[1][PortIndex]);
        break;

    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask[1][PortIndex]);
        break;

    default:
        break;
    }

    return Vsg_Select_Pac(SubPort, PortState, true);
}

int Vsg_Select_Pac(int SubPort, int PortState, int NextStep)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[4] = {P1_T_CTL3, P2_T_CTL3, P3_T_CTL3, P4_T_CTL3};

    const int BitMaskRx[4] = {P1_R_CTL4, P2_R_CTL4, P3_R_CTL4, P4_R_CTL4};

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_OFF:
    case WT_RF_STATE_PI:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        _DR_SW_SETBIT(BitMaskRx[PortIndex]);
        break;

    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_SETBIT(BitMask[PortIndex]);
        _DR_SW_CLEARBIT(BitMaskRx[PortIndex]);
        break;
    }

    if(NextStep)
    {
        return Vsg_Select_TX_RX(SubPort, PortState);
    }
    else
    {
        return 0;
    }
}

int Vsg_Select_TX_RX(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[4] = {P1_S1_CTL2, P2_S1_CTL2, P3_S1_CTL2, P4_S1_CTL2};

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;
    }

    return Vsg_Select_PAC_PI_PA(SubPort, PortState);
}

int Vsg_Select_PAC_PI_PA(int SubPort, int PortState)
{
    int PortIndex = (SubPort - SWITCH_PORT_1) % 4;
    const int BitMask[4] = {P1_S_FPGA_CTL1, P2_S_FPGA_CTL1, P3_S_FPGA_CTL1, P4_S_FPGA_CTL1};

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMask[PortIndex]);
        break;

    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
    case WT_RF_STATE_PI:
        _DR_SW_SETBIT(BitMask[PortIndex]);
        break;
    }

    return 0;
}