#include "scpi_3gpp_alz_json_format.h"
#include "wterror.h"

#ifdef ENV_API

#if (ENV_API == 1)

#define JSON_SET_KEY_BY_IMM(Json, key, imm) \
    json_object_set_number(json_object(Json), #key, imm)

#define JSON_SET_KEY_BY_MEMBER(J<PERSON>, key, obj, member) \
    json_object_set_number(json_object(Json), #key, obj.member)

#define JSON_OBJ_DEFINE(name) \
    WT_JSON_TYPE name = json_value_init_object()

#define JSON_ARRAY_DEFINE(name) \
    WT_JSON_TYPE name = json_value_init_array()

#define JSON_SET_KEY_BY_JSON(Json, key, child) \
    json_object_set_value(json_object(Json), #key, child)

#define JSON_ARRAY_APPEND_DATA(Json, data) \
    json_array_append_number(json_array(Json), data)

#define JSON_ARRAY_APPEND_JSON(J<PERSON>, child) \
    json_array_append_value(json_array(Json), child)

#else // #if (ENV_API == 1)

#define JSON_SET_KEY_BY_IMM(Json, key, imm) \
    Json[#key] = imm

#define JSON_SET_KEY_BY_MEMBER(Json, key, obj, member) \
    Json[#key] = obj.member

#define JSON_OBJ_DEFINE(name) \
    WT_JSON_DEFINE_OBJ_TYPE name

#define JSON_ARRAY_DEFINE(name) \
    WT_JSON_DEFINE_OBJ_TYPE name

#define JSON_SET_KEY_BY_JSON(Json, key, child) \
    if (!child.isNull())                       \
    Json[#key] = child

#define JSON_ARRAY_APPEND_DATA(Json, data) \
    Json.append(data)

#define JSON_ARRAY_APPEND_JSON(Json, child) \
    Json.append(child)

#endif // #if (ENV_API == 1)

#endif // #ifdef ENV_API

static void Format_3GPP_ALZ_Alg_3GPP_MeasureIn4g(WT_JSON_TYPE pMeasInfo, Alg_3GPP_MeasureIn4g &MeasInfo)
{
    (void)pMeasInfo;
    (void)MeasInfo;
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, MeasSubfrmIdx, MeasInfo, MeasSubfrmIdx);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, EvmSubcarrierState, MeasInfo, EvmSubcarrierState);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, EvmSymbIndx, MeasInfo, EvmSymbIndx);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, EvmSymbPosType, MeasInfo, EvmSymbPosType);

    // 以下功能算法未开放, 不写这些数据
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, MeasPwrState, MeasInfo, MeasPwrState);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, MeasAclrState, MeasInfo, MeasAclrState);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, AclrLimitMode, MeasInfo, AclrLimitMode);

    JSON_ARRAY_DEFINE(pUtraLimit);
    for (int i = 0; i < ARRAYSIZE(MeasInfo.UtraLimit); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pUtraLimit, MeasInfo.UtraLimit[i]);
    }
    JSON_SET_KEY_BY_JSON(pMeasInfo, UtraLimit, pUtraLimit);

    JSON_SET_KEY_BY_MEMBER(pMeasInfo, EUtraLimit, MeasInfo, EUtraLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, MeasSEMState, MeasInfo, MeasSEMState);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, MeasEVMState, MeasInfo, MeasEVMState);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, DmrsConsState, MeasInfo, DmrsConsState);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, MeasureUnit, MeasInfo, MeasureUnit);
    JSON_SET_KEY_BY_MEMBER(pMeasInfo, ExAbnSymbFlg, MeasInfo, ExAbnSymbFlg);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzPusch4g(WT_JSON_TYPE pPusch, Alg_3GPP_AlzPusch4g *Pusch, int length)
{
    (void)pPusch;
    (void)Pusch;
    (void)length;

    for (int i = 0; i < length; ++i)
    {
        JSON_OBJ_DEFINE(pSub);

        JSON_SET_KEY_BY_MEMBER(pSub, CellIdx, Pusch[i], CellIdx);
        JSON_SET_KEY_BY_MEMBER(pSub, State, Pusch[i], State);
        JSON_SET_KEY_BY_MEMBER(pSub, RBNum, Pusch[i], RBNum);
        JSON_SET_KEY_BY_MEMBER(pSub, RBOffset, Pusch[i], RBOffset);

        JSON_SET_KEY_BY_MEMBER(pSub, Precoding, Pusch[i], Precoding);
        JSON_SET_KEY_BY_MEMBER(pSub, LayerNum, Pusch[i], LayerNum);
        JSON_SET_KEY_BY_MEMBER(pSub, AntennaNum, Pusch[i], AntennaNum);
        // JSON_SET_KEY_BY_MEMBER(pSub, CodebookIdx, Pusch[i], CodebookIdx);

        JSON_SET_KEY_BY_MEMBER(pSub, GroupHop, Pusch[i], GroupHop);
        JSON_SET_KEY_BY_MEMBER(pSub, SequenceHop, Pusch[i], SequenceHop);
        JSON_SET_KEY_BY_MEMBER(pSub, DeltaSeqShift, Pusch[i], DeltaSeqShift);
        JSON_SET_KEY_BY_MEMBER(pSub, N1Dmrs, Pusch[i], N1Dmrs);
        JSON_SET_KEY_BY_MEMBER(pSub, CyclicShiftField, Pusch[i], CyclicShiftField);

        JSON_SET_KEY_BY_MEMBER(pSub, Codeword, Pusch[i], Codeword);

        JSON_ARRAY_DEFINE(pModulate);
        for (int j = 0; j < ARRAYSIZE(Pusch[i].Modulate); ++j)
        {
            JSON_ARRAY_APPEND_DATA(pModulate, Pusch[i].Modulate[j]);
        }
        JSON_SET_KEY_BY_JSON(pSub, Modulate, pModulate);

        JSON_SET_KEY_BY_MEMBER(pSub, ChanCodingState, Pusch[i], ChanCodingState);
        JSON_SET_KEY_BY_MEMBER(pSub, Scramble, Pusch[i], Scramble);
        JSON_SET_KEY_BY_MEMBER(pSub, McsCfgMode, Pusch[i], McsCfgMode);

        JSON_ARRAY_DEFINE(pMcs);
        for (int j = 0; j < ARRAYSIZE(Pusch[i].Mcs); ++j)
        {
            JSON_ARRAY_APPEND_DATA(pMcs, Pusch[i].Mcs[j]);
        }
        JSON_SET_KEY_BY_JSON(pSub, Mcs, pMcs);

        JSON_ARRAY_DEFINE(pPayloadSize);
        for (int j = 0; j < ARRAYSIZE(Pusch[i].PayloadSize); ++j)
        {
            JSON_ARRAY_APPEND_DATA(pPayloadSize, Pusch[i].PayloadSize[j]);
        }
        JSON_SET_KEY_BY_JSON(pSub, PayloadSize, pPayloadSize);

        JSON_ARRAY_DEFINE(pRedunVerIdx);
        for (int j = 0; j < ARRAYSIZE(Pusch[i].RedunVerIdx); ++j)
        {
            JSON_ARRAY_APPEND_DATA(pRedunVerIdx, Pusch[i].RedunVerIdx[j]);
        }
        JSON_SET_KEY_BY_JSON(pSub, RedunVerIdx, pRedunVerIdx);

        JSON_SET_KEY_BY_MEMBER(pSub, Enable256QAM, Pusch[i], Enable256QAM);
        JSON_SET_KEY_BY_MEMBER(pSub, RBDetMode, Pusch[i], RBDetMode);

        JSON_ARRAY_APPEND_JSON(pPusch, pSub);
    }
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzPdsch4g(WT_JSON_TYPE pPdsch, Alg_3GPP_AlzPdsch4g Pdsch)
{
    JSON_SET_KEY_BY_MEMBER(pPdsch, SymbOffset, Pdsch, SymbOffset);
    JSON_SET_KEY_BY_MEMBER(pPdsch, ResAllocateType, Pdsch, ResAllocateType);
    JSON_SET_KEY_BY_MEMBER(pPdsch, VRBAssignment, Pdsch, VRBAssignment);
    JSON_ARRAY_DEFINE(pRBGBitmap);
    for (int j = 0; j < ARRAYSIZE(Pdsch.RBGBitmap); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pRBGBitmap, Pdsch.RBGBitmap[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, RBGBitmap, pRBGBitmap);

    JSON_SET_KEY_BY_MEMBER(pPdsch, RBNum, Pdsch, RBNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, RBOffset, Pdsch, RBOffset);
    JSON_SET_KEY_BY_MEMBER(pPdsch, PbchState, Pdsch, PbchState);

    JSON_SET_KEY_BY_MEMBER(pPdsch, Precoding, Pdsch, Precoding);
    JSON_SET_KEY_BY_MEMBER(pPdsch, LayerNum, Pdsch, LayerNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, AntennaNum, Pdsch, AntennaNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, CyclicDelayDiversity, Pdsch, CyclicDelayDiversity);
    JSON_SET_KEY_BY_MEMBER(pPdsch, CodebookIdx, Pdsch, CodebookIdx);

    JSON_SET_KEY_BY_MEMBER(pPdsch, Codeword, Pdsch, Codeword);

    JSON_ARRAY_DEFINE(pModulate);
    for (int j = 0; j < ARRAYSIZE(Pdsch.Modulate); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pModulate, Pdsch.Modulate[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, Modulate, pModulate);

    JSON_SET_KEY_BY_MEMBER(pPdsch, ChanCodingState, Pdsch, ChanCodingState);
    JSON_SET_KEY_BY_MEMBER(pPdsch, Scramble, Pdsch, Scramble);
    JSON_SET_KEY_BY_MEMBER(pPdsch, McsCfgMode, Pdsch, McsCfgMode);

    JSON_ARRAY_DEFINE(pMcs);
    for (int j = 0; j < ARRAYSIZE(Pdsch.Mcs); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pMcs, Pdsch.Mcs[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, Mcs, pMcs);

    JSON_ARRAY_DEFINE(pPayloadSize);
    for (int j = 0; j < ARRAYSIZE(Pdsch.PayloadSize); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pPayloadSize, Pdsch.PayloadSize[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, PayloadSize, pPayloadSize);

    JSON_ARRAY_DEFINE(pRedunVerIdx);
    for (int j = 0; j < ARRAYSIZE(Pdsch.RedunVerIdx); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pRedunVerIdx, Pdsch.RedunVerIdx[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, RedunVerIdx, pRedunVerIdx);

    JSON_ARRAY_DEFINE(pSoftChanBit);
    for (int j = 0; j < ARRAYSIZE(Pdsch.SoftChanBit); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pSoftChanBit, Pdsch.SoftChanBit[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, SoftChanBit, pSoftChanBit);

    JSON_SET_KEY_BY_MEMBER(pPdsch, PA, Pdsch, PA);
    JSON_SET_KEY_BY_MEMBER(pPdsch, PB, Pdsch, PB);

    JSON_ARRAY_DEFINE(pNIR);
    for (int j = 0; j < ARRAYSIZE(Pdsch.NIR); ++j)
    {
        JSON_ARRAY_APPEND_DATA(pNIR, Pdsch.NIR[j]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, NIR, pNIR);

    JSON_SET_KEY_BY_MEMBER(pPdsch, IRConfigMode, Pdsch, IRConfigMode);
    JSON_SET_KEY_BY_MEMBER(pPdsch, TxMode, Pdsch, TxMode);
    JSON_SET_KEY_BY_MEMBER(pPdsch, UECategory, Pdsch, UECategory);
    JSON_SET_KEY_BY_MEMBER(pPdsch, McsTable, Pdsch, McsTable);
    JSON_SET_KEY_BY_MEMBER(pPdsch, TbsIndexAlt, Pdsch, TbsIndexAlt);
}

static void Format_3GPP_ALZ_Alg_3GPP_LimitIn4g(WT_JSON_TYPE pLimitInfo, Alg_3GPP_LimitIn4g LimitInfo)
{
    JSON_SET_KEY_BY_MEMBER(pLimitInfo, SEMLimitMode, LimitInfo, SEMLimitMode);

    JSON_OBJ_DEFINE(pSEMLimit);
    for (int i = 0; i < ARRAYSIZE(LimitInfo.SEMLimit); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        JSON_SET_KEY_BY_MEMBER(pSub, State, LimitInfo.SEMLimit[i], State);
        JSON_SET_KEY_BY_MEMBER(pSub, StartFreq, LimitInfo.SEMLimit[i], StartFreq);
        JSON_SET_KEY_BY_MEMBER(pSub, StopFreq, LimitInfo.SEMLimit[i], StopFreq);
        JSON_SET_KEY_BY_MEMBER(pSub, LimitPower, LimitInfo.SEMLimit[i], LimitPower);
        JSON_SET_KEY_BY_MEMBER(pSub, RBW, LimitInfo.SEMLimit[i], RBW);
        JSON_ARRAY_APPEND_JSON(pSEMLimit, pSub);
    }
    JSON_SET_KEY_BY_JSON(pLimitInfo, SEMLimit, pSEMLimit);
}

static void Format_3GPP_ALZ_LTE(WT_JSON_TYPE pLTE, Alg_3GPP_AlzIn4g &LTE)
{
    JSON_SET_KEY_BY_MEMBER(pLTE, CarrAggrState, LTE, CarrAggrState);
    JSON_SET_KEY_BY_MEMBER(pLTE, CyclicPrefix, LTE, CyclicPrefix);
    JSON_SET_KEY_BY_MEMBER(pLTE, UeID, LTE, UeID);
    JSON_SET_KEY_BY_MEMBER(pLTE, ChanType, LTE, ChanType);

    JSON_ARRAY_DEFINE(pCell);
    for (int i = 0; i < ARRAYSIZE(LTE.Cell); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        JSON_SET_KEY_BY_MEMBER(pSub, CellIdx, LTE.Cell[i], CellIdx);
        JSON_SET_KEY_BY_MEMBER(pSub, State, LTE.Cell[i], State);
        JSON_SET_KEY_BY_MEMBER(pSub, PhyCellID, LTE.Cell[i], PhyCellID);
        JSON_SET_KEY_BY_MEMBER(pSub, ChannelBW, LTE.Cell[i], ChannelBW);
        JSON_SET_KEY_BY_MEMBER(pSub, Duplexing, LTE.Cell[i], Duplexing);
        JSON_SET_KEY_BY_MEMBER(pSub, ULDLConfig, LTE.Cell[i], ULDLConfig);
        JSON_SET_KEY_BY_MEMBER(pSub, SpecialSubfrmConfig, LTE.Cell[i], SpecialSubfrmConfig);
        JSON_ARRAY_APPEND_JSON(pCell, pSub);
    }
    JSON_SET_KEY_BY_JSON(pLTE, Cell, pCell);

    JSON_OBJ_DEFINE(pMeasInfo);
    Format_3GPP_ALZ_Alg_3GPP_MeasureIn4g(pMeasInfo, LTE.MeasInfo);
    JSON_SET_KEY_BY_JSON(pLTE, MeasInfo, pMeasInfo);

    JSON_OBJ_DEFINE(pLimitInfo);
    Format_3GPP_ALZ_Alg_3GPP_LimitIn4g(pLimitInfo, LTE.LimitInfo);
    JSON_SET_KEY_BY_JSON(pLTE, LimitInfo, pLimitInfo);

    switch (LTE.ChanType)
    {
    case ALG_4G_PUSCH:
    {
        JSON_ARRAY_DEFINE(pPusch);
        Format_3GPP_ALZ_Alg_3GPP_AlzPusch4g(pPusch, LTE.Pusch, ARRAYSIZE(LTE.Pusch));
        JSON_SET_KEY_BY_JSON(pLTE, Pusch, pPusch);
    }
    break;

    case ALG_4G_PDSCH:
    {
        JSON_OBJ_DEFINE(pPdsch);
        Format_3GPP_ALZ_Alg_3GPP_AlzPdsch4g(pPdsch, LTE.Pdsch);
        JSON_SET_KEY_BY_JSON(pLTE, Pdsch, pPdsch);
    }
    break;

    default:
        break;
    }
}

static void Format_3GPP_ALZ_Alg_5G_BwpSchDmrsCfg(WT_JSON_TYPE pDmrs, Alg_5G_BwpSchDmrsCfg &Dmrs)
{
    JSON_SET_KEY_BY_MEMBER(pDmrs, ConfigType, Dmrs, ConfigType);
    JSON_SET_KEY_BY_MEMBER(pDmrs, MaxLength, Dmrs, MaxLength);
    JSON_SET_KEY_BY_MEMBER(pDmrs, AdditionalPos, Dmrs, AdditionalPos);
    JSON_SET_KEY_BY_MEMBER(pDmrs, ScramblingID0, Dmrs, ScramblingID0);
    JSON_SET_KEY_BY_MEMBER(pDmrs, ScramblingID1, Dmrs, ScramblingID1);
    JSON_SET_KEY_BY_MEMBER(pDmrs, NPuschID, Dmrs, NPuschID);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzULBwpCfg(WT_JSON_TYPE pBwp, Alg_3GPP_AlzULBwpCfg &Bwp)
{
    JSON_SET_KEY_BY_MEMBER(pBwp, SCSpacing, Bwp, SCSpacing);
    JSON_SET_KEY_BY_MEMBER(pBwp, CyclicPrefix, Bwp, CyclicPrefix);
    JSON_SET_KEY_BY_MEMBER(pBwp, RBNum, Bwp, RBNum);
    JSON_SET_KEY_BY_MEMBER(pBwp, RBOffset, Bwp, RBOffset);
    JSON_SET_KEY_BY_MEMBER(pBwp, TransformPrecoder, Bwp, TransformPrecoder);
    JSON_SET_KEY_BY_MEMBER(pBwp, ResourceAllocation, Bwp, ResourceAllocation);
    JSON_SET_KEY_BY_MEMBER(pBwp, FreqHopMode, Bwp, FreqHopMode);

    JSON_OBJ_DEFINE(pDmrs);
    Format_3GPP_ALZ_Alg_5G_BwpSchDmrsCfg(pDmrs, Bwp.Dmrs);
    JSON_SET_KEY_BY_JSON(pBwp, Dmrs, pDmrs);
}

static void Format_3GPP_ALZ_Alg_5G_TxBWType(WT_JSON_TYPE pTxBW, Alg_5G_TxBWType &TxBW)
{
    JSON_SET_KEY_BY_MEMBER(pTxBW, SCSpacing, TxBW, SCSpacing);
    JSON_SET_KEY_BY_MEMBER(pTxBW, State, TxBW, State);
    JSON_SET_KEY_BY_MEMBER(pTxBW, MaxRBNumb, TxBW, MaxRBNumb);
    JSON_SET_KEY_BY_MEMBER(pTxBW, Offset, TxBW, Offset);
    JSON_SET_KEY_BY_MEMBER(pTxBW, k0u, TxBW, k0u);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzPusch5g(WT_JSON_TYPE pPusch, Alg_3GPP_AlzPusch5g &Pusch, int CellIdx)
{
    // JSON_SET_KEY_BY_MEMBER(pPusch, CellIdx, Pusch, CellIdx);
    JSON_SET_KEY_BY_IMM(pPusch, CellIdx, CellIdx);
    JSON_SET_KEY_BY_MEMBER(pPusch, State, Pusch, State);
    JSON_SET_KEY_BY_MEMBER(pPusch, MappingType, Pusch, MappingType);
    JSON_SET_KEY_BY_MEMBER(pPusch, SymNum, Pusch, SymNum);
    JSON_SET_KEY_BY_MEMBER(pPusch, SymbOffset, Pusch, SymbOffset);
    JSON_SET_KEY_BY_MEMBER(pPusch, RBDetMode, Pusch, RBDetMode);
    JSON_SET_KEY_BY_MEMBER(pPusch, RBNum, Pusch, RBNum);
    JSON_SET_KEY_BY_MEMBER(pPusch, RBOffset, Pusch, RBOffset);
    JSON_SET_KEY_BY_MEMBER(pPusch, LayerNum, Pusch, LayerNum);
    JSON_SET_KEY_BY_MEMBER(pPusch, AntennaNum, Pusch, AntennaNum);
    JSON_SET_KEY_BY_MEMBER(pPusch, CDMGrpWOData, Pusch, CDMGrpWOData);
    
    JSON_ARRAY_DEFINE(pDmrsAntPort);
    for (int i = 0; i < ARRAYSIZE(Pusch.DmrsAntPort); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pDmrsAntPort, Pusch.DmrsAntPort[i]);
    }
    JSON_SET_KEY_BY_JSON(pPusch, DmrsAntPort, pDmrsAntPort);

    JSON_SET_KEY_BY_MEMBER(pPusch, DmrsSymbLen, Pusch, DmrsSymbLen);
    JSON_SET_KEY_BY_MEMBER(pPusch, DmrsInitType, Pusch, DmrsInitType);
    JSON_SET_KEY_BY_MEMBER(pPusch, DmrsID, Pusch, DmrsID);
    JSON_SET_KEY_BY_MEMBER(pPusch, NSCID, Pusch, NSCID);
    JSON_SET_KEY_BY_MEMBER(pPusch, GrporSeqHopType, Pusch, GrporSeqHopType);
    JSON_SET_KEY_BY_MEMBER(pPusch, Modulate, Pusch, Modulate);
    JSON_SET_KEY_BY_MEMBER(pPusch, ChanCodingState, Pusch, ChanCodingState);
    JSON_SET_KEY_BY_MEMBER(pPusch, Scramble, Pusch, Scramble);
    JSON_SET_KEY_BY_MEMBER(pPusch, UeID, Pusch, UeID);
    JSON_SET_KEY_BY_MEMBER(pPusch, MCS, Pusch, MCS);
    JSON_SET_KEY_BY_MEMBER(pPusch, RvIdx, Pusch, RvIdx);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzULCell5g(WT_JSON_TYPE pCell, Alg_3GPP_AlzULCell5g &Cell)
{
    JSON_SET_KEY_BY_MEMBER(pCell, CellIdx, Cell, CellIdx);
    JSON_SET_KEY_BY_MEMBER(pCell, State, Cell, State);
    JSON_SET_KEY_BY_MEMBER(pCell, Frequency, Cell, Frequency);
    JSON_SET_KEY_BY_MEMBER(pCell, ChannelBW, Cell, ChannelBW);
    JSON_SET_KEY_BY_MEMBER(pCell, PhyCellID, Cell, PhyCellID);
    JSON_SET_KEY_BY_MEMBER(pCell, DmrsTypeAPos, Cell, DmrsTypeAPos);

    JSON_ARRAY_DEFINE(pTxBW);
    for (int i = 0; i < ARRAYSIZE(Cell.TxBW); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        Format_3GPP_ALZ_Alg_5G_TxBWType(pSub, Cell.TxBW[i]);
        JSON_ARRAY_APPEND_JSON(pTxBW, pSub);
    }
    JSON_SET_KEY_BY_JSON(pCell, TxBW, pTxBW);

    JSON_OBJ_DEFINE(pBwp);
    Format_3GPP_ALZ_Alg_3GPP_AlzULBwpCfg(pBwp, Cell.Bwp);
    JSON_SET_KEY_BY_JSON(pCell, Bwp, pBwp);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzULIn5g(WT_JSON_TYPE pUL, Alg_3GPP_AlzULIn5g &UL)
{
    JSON_SET_KEY_BY_MEMBER(pUL, Duplexing, UL, Duplexing);
    JSON_SET_KEY_BY_MEMBER(pUL, SlotPeriod, UL, SlotPeriod);
    JSON_SET_KEY_BY_MEMBER(pUL, ULSlotnumber, UL, ULSlotnumber);
    JSON_SET_KEY_BY_MEMBER(pUL, SpecialSlotIdx, UL, SpecialSlotIdx);
    JSON_SET_KEY_BY_MEMBER(pUL, CellNum, UL, CellNum);
    JSON_SET_KEY_BY_MEMBER(pUL, RfPhaseCompen, UL, RfPhaseCompen);
    JSON_SET_KEY_BY_MEMBER(pUL, EVMDelDCRBFlag, UL, EVMDelDCRBFlag);

    JSON_ARRAY_DEFINE(pCell);
    for (int i = 0; i < ARRAYSIZE(UL.Cell); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        Format_3GPP_ALZ_Alg_3GPP_AlzULCell5g(pSub, UL.Cell[i]);
        JSON_ARRAY_APPEND_JSON(pCell, pSub);
    }
    JSON_SET_KEY_BY_JSON(pUL, Cell, pCell);

    JSON_SET_KEY_BY_MEMBER(pUL, ChanType, UL, ChanType);
    
    JSON_ARRAY_DEFINE(pPusch);
    for (int i = 0; i < ARRAYSIZE(UL.Pusch); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        Format_3GPP_ALZ_Alg_3GPP_AlzPusch5g(pSub, UL.Pusch[i], i);
        JSON_ARRAY_APPEND_JSON(pPusch, pSub);
    }
    JSON_SET_KEY_BY_JSON(pUL, Pusch, pPusch);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzDLBwpPdschCfg(WT_JSON_TYPE pPdsch, Alg_3GPP_AlzDLBwpPdschCfg &Pdsch)
{
    JSON_SET_KEY_BY_MEMBER(pPdsch, VrbToPrbInterleaver, Pdsch, VrbToPrbInterleaver);
    JSON_SET_KEY_BY_MEMBER(pPdsch, McsTab, Pdsch, McsTab);
    JSON_SET_KEY_BY_MEMBER(pPdsch, ResourceAllocation, Pdsch, ResourceAllocation);
    JSON_SET_KEY_BY_MEMBER(pPdsch, ConfigType, Pdsch, ConfigType);
    JSON_SET_KEY_BY_MEMBER(pPdsch, MaxLength, Pdsch, MaxLength);
    JSON_SET_KEY_BY_MEMBER(pPdsch, AdditionalPos, Pdsch, AdditionalPos);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzDLBwpCfg(WT_JSON_TYPE pBwp, Alg_3GPP_AlzDLBwpCfg &Bwp)
{
    JSON_SET_KEY_BY_MEMBER(pBwp, SCSpacing, Bwp, SCSpacing);
    JSON_SET_KEY_BY_MEMBER(pBwp, CyclicPrefix, Bwp, CyclicPrefix);
    JSON_SET_KEY_BY_MEMBER(pBwp, RBNum, Bwp, RBNum);
    JSON_SET_KEY_BY_MEMBER(pBwp, RBOffset, Bwp, RBOffset);

    JSON_OBJ_DEFINE(pPdsch);
    Format_3GPP_ALZ_Alg_3GPP_AlzDLBwpPdschCfg(pPdsch, Bwp.Pdsch);
    JSON_SET_KEY_BY_JSON(pBwp, Pdsch, pPdsch);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzDLCell5g(WT_JSON_TYPE pCell, Alg_3GPP_AlzDLCell5g &Cell)
{
    JSON_SET_KEY_BY_MEMBER(pCell, CellIdx, Cell, CellIdx);
    JSON_SET_KEY_BY_MEMBER(pCell, State, Cell, State);
    JSON_SET_KEY_BY_MEMBER(pCell, ChannelBW, Cell, ChannelBW);
    JSON_SET_KEY_BY_MEMBER(pCell, PhyCellID, Cell, PhyCellID);
    JSON_SET_KEY_BY_MEMBER(pCell, DmrsTypeAPos, Cell, DmrsTypeAPos);
    JSON_SET_KEY_BY_MEMBER(pCell, Frequency, Cell, Frequency);

    JSON_ARRAY_DEFINE(pTxBW);
    for (int i = 0; i < ARRAYSIZE(Cell.TxBW); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        Format_3GPP_ALZ_Alg_5G_TxBWType(pSub, Cell.TxBW[i]);
        JSON_ARRAY_APPEND_JSON(pTxBW, pSub);
    }
    JSON_SET_KEY_BY_JSON(pCell, TxBW, pTxBW);

    JSON_OBJ_DEFINE(pBwp);
    Format_3GPP_ALZ_Alg_3GPP_AlzDLBwpCfg(pBwp, Cell.Bwp);
    JSON_SET_KEY_BY_JSON(pCell, Bwp, pBwp);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzPbch5g(WT_JSON_TYPE pPbch, Alg_3GPP_AlzPbch5g &Pbch)
{
    JSON_SET_KEY_BY_MEMBER(pPbch, State, Pbch, State);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzPdcch5g(WT_JSON_TYPE pPdcch, Alg_3GPP_AlzPdcch5g &Pdcch)
{
    JSON_SET_KEY_BY_MEMBER(pPdcch, State, Pdcch, State);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzPdsch5g(WT_JSON_TYPE pPdsch, Alg_3GPP_AlzPdsch5g &Pdsch)
{
    JSON_SET_KEY_BY_MEMBER(pPdsch, State, Pdsch, State);
    JSON_SET_KEY_BY_MEMBER(pPdsch, MappingType, Pdsch, MappingType);
    JSON_SET_KEY_BY_MEMBER(pPdsch, SymbNum, Pdsch, SymbNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, SymbOffset, Pdsch, SymbOffset);
    JSON_SET_KEY_BY_MEMBER(pPdsch, RBNum, Pdsch, RBNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, RBOffset, Pdsch, RBOffset);
    JSON_SET_KEY_BY_MEMBER(pPdsch, LayerNum, Pdsch, LayerNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, AntennaNum, Pdsch, AntennaNum);
    JSON_SET_KEY_BY_MEMBER(pPdsch, CDMGrpWOData, Pdsch, CDMGrpWOData);
    JSON_SET_KEY_BY_MEMBER(pPdsch, DmrsSymbLen, Pdsch, DmrsSymbLen);

    JSON_ARRAY_DEFINE(pDmrsAntPort);
    for (int i = 0; i < ARRAYSIZE(Pdsch.DmrsAntPort); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pDmrsAntPort, Pdsch.DmrsAntPort[i]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, DmrsAntPort, pDmrsAntPort);

    JSON_SET_KEY_BY_MEMBER(pPdsch, DmrsInitType, Pdsch, DmrsInitType);
    JSON_SET_KEY_BY_MEMBER(pPdsch, DmrsID, Pdsch, DmrsID);
    JSON_SET_KEY_BY_MEMBER(pPdsch, NSCID, Pdsch, NSCID);
    JSON_SET_KEY_BY_MEMBER(pPdsch, Codewords, Pdsch, Codewords);

    JSON_ARRAY_DEFINE(pModulate);
    for (int i = 0; i < ARRAYSIZE(Pdsch.Modulate); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pModulate, Pdsch.Modulate[i]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, Modulate, pModulate);

    JSON_SET_KEY_BY_MEMBER(pPdsch, ChanCodingState, Pdsch, ChanCodingState);
    JSON_SET_KEY_BY_MEMBER(pPdsch, Scrambling, Pdsch, Scrambling);
    JSON_SET_KEY_BY_MEMBER(pPdsch, UsePdschScrambleID, Pdsch, UsePdschScrambleID);
    JSON_SET_KEY_BY_MEMBER(pPdsch, DataScrambleID, Pdsch, DataScrambleID);
    JSON_SET_KEY_BY_MEMBER(pPdsch, UeID, Pdsch, UeID);

    JSON_ARRAY_DEFINE(pMCS);
    for (int i = 0; i < ARRAYSIZE(Pdsch.MCS); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pMCS, Pdsch.MCS[i]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, MCS, pMCS);

    JSON_ARRAY_DEFINE(pRvIdx);
    for (int i = 0; i < ARRAYSIZE(Pdsch.RvIdx); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pRvIdx, Pdsch.RvIdx[i]);
    }
    JSON_SET_KEY_BY_JSON(pPdsch, RvIdx, pRvIdx);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannel5g(WT_JSON_TYPE pChannel, Alg_3GPP_AlzChannel5g &Channel, int CellIdx)
{
    // JSON_SET_KEY_BY_MEMBER(pChannel, CellIdx, Channel, CellIdx);
    JSON_SET_KEY_BY_IMM(pChannel, CellIdx, CellIdx);

    JSON_OBJ_DEFINE(pPbch);
    Format_3GPP_ALZ_Alg_3GPP_AlzPbch5g(pPbch, Channel.Pbch);
    JSON_SET_KEY_BY_JSON(pChannel, Pbch, pPbch);

    JSON_OBJ_DEFINE(pPdcch);
    Format_3GPP_ALZ_Alg_3GPP_AlzPdcch5g(pPdcch, Channel.Pdcch);
    JSON_SET_KEY_BY_JSON(pChannel, Pdcch, pPdcch);

    JSON_OBJ_DEFINE(pPdsch);
    Format_3GPP_ALZ_Alg_3GPP_AlzPdsch5g(pPdsch, Channel.Pdsch);
    JSON_SET_KEY_BY_JSON(pChannel, Pdsch, pPdsch);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzDLIn5g(WT_JSON_TYPE pDL, Alg_3GPP_AlzDLIn5g &DL)
{
    JSON_SET_KEY_BY_MEMBER(pDL, Duplexing, DL, Duplexing);
    JSON_SET_KEY_BY_MEMBER(pDL, SlotPeriod, DL, SlotPeriod);
    JSON_SET_KEY_BY_MEMBER(pDL, DLSlotNumber, DL, DLSlotNumber);
    JSON_SET_KEY_BY_MEMBER(pDL, SpecialSlotIdx, DL, SpecialSlotIdx);
    JSON_SET_KEY_BY_MEMBER(pDL, RfPhaseCompen, DL, RfPhaseCompen);
    JSON_SET_KEY_BY_MEMBER(pDL, CellNum, DL, CellNum);

    JSON_ARRAY_DEFINE(pCell);
    for (int i = 0; i < ARRAYSIZE(DL.Cell); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        Format_3GPP_ALZ_Alg_3GPP_AlzDLCell5g(pSub, DL.Cell[i]);
        JSON_ARRAY_APPEND_JSON(pCell, pSub);
    }
    JSON_SET_KEY_BY_JSON(pDL, Cell, pCell);

    JSON_SET_KEY_BY_MEMBER(pDL, ChanType, DL, ChanType);
    
    JSON_ARRAY_DEFINE(pChannel);
    for (int i = 0; i < ARRAYSIZE(DL.Channel); ++i)
    {
        JSON_OBJ_DEFINE(pSub);
        Format_3GPP_ALZ_Alg_3GPP_AlzChannel5g(pSub, DL.Channel[i], i);
        JSON_ARRAY_APPEND_JSON(pChannel, pSub);
    }
    JSON_SET_KEY_BY_JSON(pDL, Channel, pChannel);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzMeasure5g(WT_JSON_TYPE pMeasure, Alg_3GPP_AlzMeasure5g &Measure)
{
    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasSubfrmIdx, Measure, MeasSubfrmIdx);
    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasSlotIdx, Measure, MeasSlotIdx);
    JSON_SET_KEY_BY_MEMBER(pMeasure, DmrsConsState, Measure, DmrsConsState);
    JSON_SET_KEY_BY_MEMBER(pMeasure, EvmSubcarrierState, Measure, EvmSubcarrierState);
    JSON_SET_KEY_BY_MEMBER(pMeasure, EvmSymbIndx, Measure, EvmSymbIndx);
    JSON_SET_KEY_BY_MEMBER(pMeasure, EvmSymbPosType, Measure, EvmSymbPosType);
    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasPwrState, Measure, MeasPwrState);
    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasAclrState, Measure, MeasAclrState);
    JSON_SET_KEY_BY_MEMBER(pMeasure, AclrLimitMode, Measure, AclrLimitMode);

    JSON_ARRAY_DEFINE(pUtraLimit);
    for (int i = 0; i < ARRAYSIZE(Measure.UtraLimit); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pUtraLimit, Measure.UtraLimit[i]);
    }
    JSON_SET_KEY_BY_JSON(pMeasure, UtraLimit, pUtraLimit);

    JSON_SET_KEY_BY_MEMBER(pMeasure, NrUtraLimit, Measure, NrUtraLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasSEMState, Measure, MeasSEMState);
    JSON_SET_KEY_BY_MEMBER(pMeasure, SEMLimitMode, Measure, SEMLimitMode);

    JSON_ARRAY_DEFINE(pSEMLimit);
    for (int i = 0; i < ARRAYSIZE(Measure.SEMLimit); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pSEMLimit, Measure.SEMLimit[i]);
    }
    JSON_SET_KEY_BY_JSON(pMeasure, SEMLimit, pSEMLimit);

    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasEVMState, Measure, MeasEVMState);
}

static void Format_3GPP_ALZ_NR(WT_JSON_TYPE pNR, Alg_3GPP_AlzIn5g &NR)
{
    JSON_SET_KEY_BY_MEMBER(pNR, LinkDirect, NR, LinkDirect);

    switch (NR.LinkDirect)
    {
    case ALG_3GPP_UL:
    {
        JSON_OBJ_DEFINE(pUL);
        Format_3GPP_ALZ_Alg_3GPP_AlzULIn5g(pUL, NR.UL);
        JSON_SET_KEY_BY_JSON(pNR, UL, pUL);
    }
    break;

    case ALG_3GPP_DL:
    {
        JSON_OBJ_DEFINE(pDL);
        Format_3GPP_ALZ_Alg_3GPP_AlzDLIn5g(pDL, NR.DL);
        JSON_SET_KEY_BY_JSON(pNR, DL, pDL);
    }
    break;

    default:
        break;
    }  

    JSON_OBJ_DEFINE(pMeasure);
    Format_3GPP_ALZ_Alg_3GPP_AlzMeasure5g(pMeasure, NR.Measure);
    JSON_SET_KEY_BY_JSON(pNR, Measure, pMeasure);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzNpuschNBIOT(WT_JSON_TYPE pNpusch, Alg_3GPP_AlzNpuschNBIOT &Npusch)
{
    JSON_SET_KEY_BY_MEMBER(pNpusch, Format, Npusch, Format);
    JSON_SET_KEY_BY_MEMBER(pNpusch, SCSpacing, Npusch, SCSpacing);
    JSON_SET_KEY_BY_MEMBER(pNpusch, Repetitions, Npusch, Repetitions);
    JSON_SET_KEY_BY_MEMBER(pNpusch, RUs, Npusch, RUs);
    JSON_SET_KEY_BY_MEMBER(pNpusch, SubcarrierNum, Npusch, SubcarrierNum);
    JSON_SET_KEY_BY_MEMBER(pNpusch, StartSubcarrier, Npusch, StartSubcarrier);

    JSON_SET_KEY_BY_MEMBER(pNpusch, CyclicShift, Npusch, CyclicShift);
    JSON_SET_KEY_BY_MEMBER(pNpusch, GrpHopping, Npusch, GrpHopping);
    JSON_SET_KEY_BY_MEMBER(pNpusch, DeltaSeqShift, Npusch, DeltaSeqShift);
    JSON_SET_KEY_BY_MEMBER(pNpusch, Modulate, Npusch, Modulate);

    JSON_SET_KEY_BY_MEMBER(pNpusch, ChanCodingState, Npusch, ChanCodingState);
    JSON_SET_KEY_BY_MEMBER(pNpusch, Scrambling, Npusch, Scrambling);
    JSON_SET_KEY_BY_MEMBER(pNpusch, StartSubfrm, Npusch, StartSubfrm);
    JSON_SET_KEY_BY_MEMBER(pNpusch, TBSIdx, Npusch, TBSIdx);
    JSON_SET_KEY_BY_MEMBER(pNpusch, StartRVIdx, Npusch, StartRVIdx);
    JSON_SET_KEY_BY_MEMBER(pNpusch, UeID, Npusch, UeID);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzNprachNBIOT(WT_JSON_TYPE pNprach, Alg_3GPP_AlzNprachNBIOT &Nprach)
{
    (void)pNprach;
    (void)Nprach;
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzULInNBIOT(WT_JSON_TYPE pUL, Alg_3GPP_AlzULInNBIOT &UL)
{
    JSON_SET_KEY_BY_MEMBER(pUL, Duplexing, UL, Duplexing);
    JSON_SET_KEY_BY_MEMBER(pUL, OperationMode, UL, OperationMode);
    JSON_SET_KEY_BY_MEMBER(pUL, ChannelBW, UL, ChannelBW);
    JSON_SET_KEY_BY_MEMBER(pUL, RBIdx, UL, RBIdx);
    JSON_SET_KEY_BY_MEMBER(pUL, NBCellID, UL, NBCellID);
    JSON_SET_KEY_BY_MEMBER(pUL, ChanType, UL, ChanType);

    JSON_OBJ_DEFINE(pNpusch);
    Format_3GPP_ALZ_Alg_3GPP_AlzNpuschNBIOT(pNpusch, UL.Npusch);
    JSON_SET_KEY_BY_JSON(pUL, Npusch, pNpusch);

    JSON_OBJ_DEFINE(pNprach);
    Format_3GPP_ALZ_Alg_3GPP_AlzNprachNBIOT(pNprach, UL.Nprach);
    JSON_SET_KEY_BY_JSON(pUL, Nprach, pNprach);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzNpdschNBIOT(WT_JSON_TYPE pNpdsch, Alg_3GPP_AlzNpdschNBIOT &Npdsch)
{
    JSON_SET_KEY_BY_MEMBER(pNpdsch, NSF, Npdsch, NSF);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, Repetitions, Npdsch, Repetitions);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, MCS, Npdsch, MCS);

    JSON_SET_KEY_BY_MEMBER(pNpdsch, StartSymb, Npdsch, StartSymb);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, StartSubfrm, Npdsch, StartSubfrm);

    // JSON_SET_KEY_BY_MEMBER(pNpdsch, Modulate, Npdsch, Modulate);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, Precoding, Npdsch, Precoding);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, ChanCodingState, Npdsch, ChanCodingState);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, Scrambling, Npdsch, Scrambling);
    JSON_SET_KEY_BY_MEMBER(pNpdsch, UeID, Npdsch, UeID);

    JSON_SET_KEY_BY_MEMBER(pNpdsch, Power, Npdsch, Power);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzDLInNBIOT(WT_JSON_TYPE pDL, Alg_3GPP_AlzDLInNBIOT &DL)
{
    (void)pDL;
    (void)DL;

    JSON_SET_KEY_BY_MEMBER(pDL, Duplexing, DL, Duplexing);
    JSON_SET_KEY_BY_MEMBER(pDL, OperationMode, DL, OperationMode);
    JSON_SET_KEY_BY_MEMBER(pDL, CarrierType, DL, CarrierType);
    JSON_SET_KEY_BY_MEMBER(pDL, ChannelBW, DL, ChannelBW);

    JSON_SET_KEY_BY_MEMBER(pDL, RBIdx, DL, RBIdx);
    JSON_SET_KEY_BY_MEMBER(pDL, NBCellID, DL, NBCellID);
    JSON_SET_KEY_BY_MEMBER(pDL, LTECellID, DL, LTECellID);

    JSON_SET_KEY_BY_MEMBER(pDL, LTEAntennaNum, DL, LTEAntennaNum);

    JSON_SET_KEY_BY_MEMBER(pDL, NBAntennaNum, DL, NBAntennaNum);
    JSON_SET_KEY_BY_MEMBER(pDL, SIB1Switch, DL, SIB1Switch);
    JSON_SET_KEY_BY_MEMBER(pDL, SchedulingInfoSIB1, DL, SchedulingInfoSIB1);
    JSON_SET_KEY_BY_MEMBER(pDL, NPssPower, DL, NPssPower);
    JSON_SET_KEY_BY_MEMBER(pDL, NSssPower, DL, NSssPower);

    JSON_SET_KEY_BY_MEMBER(pDL, ChanType, DL, ChanType);

    JSON_OBJ_DEFINE(pNpdsch);
    Format_3GPP_ALZ_Alg_3GPP_AlzNpdschNBIOT(pNpdsch, DL.Npdsch);
    JSON_SET_KEY_BY_JSON(pDL, Npdsch, pNpdsch);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzMeasureNBIOT(WT_JSON_TYPE pDL, Alg_3GPP_AlzMeasureNBIOT &Measure)
{
    JSON_SET_KEY_BY_MEMBER(pDL, StatisticAvgFlg, Measure, StatisticAvgFlg);
    JSON_SET_KEY_BY_MEMBER(pDL, StatisticCnt, Measure, StatisticCnt);
    JSON_SET_KEY_BY_MEMBER(pDL, MeasureUnit, Measure, MeasureUnit);
    JSON_SET_KEY_BY_MEMBER(pDL, ConstShowPilot, Measure, ConstShowPilot);
}

static void Format_3GPP_ALZ_NBIot(WT_JSON_TYPE pNBIOT, Alg_3GPP_AlzInNBIOT &NBIOT)
{
    (void)pNBIOT;
    (void)NBIOT;

    JSON_SET_KEY_BY_MEMBER(pNBIOT, LinkDirect, NBIOT, LinkDirect);

    switch (NBIOT.LinkDirect)
    {
    case ALG_3GPP_UL:
    {
        JSON_OBJ_DEFINE(pUL);
        Format_3GPP_ALZ_Alg_3GPP_AlzULInNBIOT(pUL, NBIOT.UL);
        JSON_SET_KEY_BY_JSON(pNBIOT, UL, pUL);
    }
    break;

    case ALG_3GPP_DL:
    {
        JSON_OBJ_DEFINE(pDL);
        Format_3GPP_ALZ_Alg_3GPP_AlzDLInNBIOT(pDL, NBIOT.DL);
        JSON_SET_KEY_BY_JSON(pNBIOT, DL, pDL);
    }
    break;

    default:
        break;
    }

    JSON_OBJ_DEFINE(pMeasure);
    Format_3GPP_ALZ_Alg_3GPP_AlzMeasureNBIOT(pMeasure, NBIOT.Measure);
    JSON_SET_KEY_BY_JSON(pNBIOT, Measure, pMeasure);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzULInWCDMA(WT_JSON_TYPE pUL, Alg_3GPP_AlzULInWCDMA &UL)
{
    (void)pUL;
    (void)UL;

    JSON_SET_KEY_BY_MEMBER(pUL, ScramblingCode, UL, ScramblingCode);
    JSON_SET_KEY_BY_MEMBER(pUL, DPCCHSlotFormat, UL, DPCCHSlotFormat);
    JSON_SET_KEY_BY_MEMBER(pUL, ChannelType, UL, ChannelType);
    JSON_SET_KEY_BY_MEMBER(pUL, DPDCHAvailable, UL, DPDCHAvailable);
    JSON_SET_KEY_BY_MEMBER(pUL, MeasureLen, UL, MeasureLen);
    JSON_SET_KEY_BY_MEMBER(pUL, SlotNum, UL, SlotNum);
    JSON_SET_KEY_BY_MEMBER(pUL, CDPSpreadFactor, UL, CDPSpreadFactor);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzMeasureInWCDMA(WT_JSON_TYPE pMeasure, Alg_3GPP_AlzMeasureWCDMA &Measure)
{
    JSON_SET_KEY_BY_MEMBER(pMeasure, AclrLimit1Mode, Measure, AclrLimit1Mode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, AclrLimit2Mode, Measure, AclrLimit2Mode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, UtraLimit1, Measure, UtraLimit1);
    JSON_SET_KEY_BY_MEMBER(pMeasure, UtraLimit2, Measure, UtraLimit2);
    JSON_SET_KEY_BY_MEMBER(pMeasure, SEMLimitADMode, Measure, SEMLimitADMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, SEMLimitEFMode, Measure, SEMLimitEFMode);

    JSON_ARRAY_DEFINE(pLimitAD);
    for (int i = 0; i < ARRAYSIZE(Measure.LimitAD); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pLimitAD, Measure.LimitAD[i]);
    }
    JSON_SET_KEY_BY_JSON(pMeasure, LimitAD, pLimitAD);

    JSON_ARRAY_DEFINE(pLimitEF);
    for (int i = 0; i < ARRAYSIZE(Measure.LimitEF); ++i)
    {
        JSON_ARRAY_APPEND_DATA(pLimitEF, Measure.LimitEF[i]);
    }
    JSON_SET_KEY_BY_JSON(pMeasure, LimitEF, pLimitEF);

    JSON_SET_KEY_BY_MEMBER(pMeasure, PeakMagnErrLimitMode, Measure, PeakMagnErrLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, RmsMagnErrLimitMode, Measure, RmsMagnErrLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, PeakMagnErrLimit, Measure, PeakMagnErrLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, RmsMagnErrLimit, Measure, RmsMagnErrLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, PeakEvmLimitMode, Measure, PeakEvmLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, RmsEvmLimitMode, Measure, RmsEvmLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, PeakEvmLimit, Measure, PeakEvmLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, RmsEvmLimit, Measure, RmsEvmLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, PeakPhaseErrLimitMode, Measure, PeakPhaseErrLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, RmsPhaseErrLimitMode, Measure, RmsPhaseErrLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, PeakPhaseErrLimit, Measure, PeakPhaseErrLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, RmsPhaseErrLimit, Measure, RmsPhaseErrLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, CFErrLimitMode, Measure, CFErrLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, CFErrLimit, Measure, CFErrLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, PhaseDisLimitMode, Measure, PhaseDisLimitMode);
    JSON_SET_KEY_BY_MEMBER(pMeasure, UpperLimit, Measure, UpperLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, DynamicLimit, Measure, DynamicLimit);
    JSON_SET_KEY_BY_MEMBER(pMeasure, MeasureUnit, Measure, MeasureUnit);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelPCPICHInWCDMA(WT_JSON_TYPE pPCPICH, Alg_WCDMA_PCPICHType &PCPICH)
{
    JSON_SET_KEY_BY_MEMBER(pPCPICH, State, PCPICH, State);
    JSON_SET_KEY_BY_MEMBER(pPCPICH, Power, PCPICH, Power);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelPSCHInWCDMA(WT_JSON_TYPE pPSCH, Alg_WCDMA_PSCHType &PSCH)
{
    JSON_SET_KEY_BY_MEMBER(pPSCH, State, PSCH, State);
    JSON_SET_KEY_BY_MEMBER(pPSCH, Power, PSCH, Power);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelSSCHInWCDMA(WT_JSON_TYPE pSSCH, Alg_WCDMA_SSCHType &SSCH)
{
    JSON_SET_KEY_BY_MEMBER(pSSCH, State, SSCH, State);
    JSON_SET_KEY_BY_MEMBER(pSSCH, Power, SSCH, Power);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHDCHDTCHInWCDMA(WT_JSON_TYPE pDTCH, Alg_WCDMA_DLCHType *DTCH, int length)
{
    (void)pDTCH;
    (void)DTCH;
    (void)length;

    for (int i = 0; i < length; ++i)
    {
        JSON_OBJ_DEFINE(pSub);

        JSON_SET_KEY_BY_MEMBER(pSub, State, DTCH[i], State);
        JSON_SET_KEY_BY_MEMBER(pSub, TTI, DTCH[i], TTI);
        JSON_SET_KEY_BY_MEMBER(pSub, TbCount, DTCH[i], TbCount);
        JSON_SET_KEY_BY_MEMBER(pSub, TbSize, DTCH[i], TbSize);
        JSON_SET_KEY_BY_MEMBER(pSub, Crc, DTCH[i], Crc);
        JSON_SET_KEY_BY_MEMBER(pSub, RmAttribute, DTCH[i], RmAttribute);
        JSON_SET_KEY_BY_MEMBER(pSub, EProtection, DTCH[i], EProtection);
        JSON_SET_KEY_BY_MEMBER(pSub, InterleaverStat, DTCH[i], InterleaverStat);

        JSON_ARRAY_APPEND_JSON(pDTCH, pSub);
    }
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHDCHDCCHInWCDMA(WT_JSON_TYPE pDCCH, Alg_WCDMA_DLCHType &DCCH)
{
    JSON_SET_KEY_BY_MEMBER(pDCCH, State, DCCH, State);
    JSON_SET_KEY_BY_MEMBER(pDCCH, TTI, DCCH, TTI);
    JSON_SET_KEY_BY_MEMBER(pDCCH, TbCount, DCCH, TbCount);
    JSON_SET_KEY_BY_MEMBER(pDCCH, TbSize, DCCH, TbSize);
    JSON_SET_KEY_BY_MEMBER(pDCCH, Crc, DCCH, Crc);
    JSON_SET_KEY_BY_MEMBER(pDCCH, RmAttribute, DCCH, RmAttribute);
    JSON_SET_KEY_BY_MEMBER(pDCCH, EProtection, DCCH, EProtection);
    JSON_SET_KEY_BY_MEMBER(pDCCH, InterleaverStat, DCCH, InterleaverStat);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHDCHInWCDMA(WT_JSON_TYPE pDCH, Alg_WCDMA_DCHType &DCH)
{
    JSON_SET_KEY_BY_MEMBER(pDCH, State, DCH, State);
    JSON_SET_KEY_BY_MEMBER(pDCH, Interleaver2Stat, DCH, Interleaver2Stat);
    
    JSON_ARRAY_DEFINE(pDTCH);
    Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHDCHDTCHInWCDMA(pDTCH, DCH.DTCH, ARRAYSIZE(DCH.DTCH));
    JSON_SET_KEY_BY_JSON(pDCH, DTCH, pDTCH);

    JSON_OBJ_DEFINE(pDCCH);
    Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHDCHDCCHInWCDMA(pDCCH, DCH.DCCH);
    JSON_SET_KEY_BY_JSON(pDCH, DCCH, pDCCH);
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHInWCDMA(WT_JSON_TYPE pDPCH, Alg_WCDMA_DLDPCHType *DPCH, int length)
{
    (void)pDPCH;
    (void)DPCH;
    (void)length;

    for (int i = 0; i < length; ++i)
    {
        JSON_OBJ_DEFINE(pSub);

        JSON_SET_KEY_BY_MEMBER(pSub, State, DPCH[i], State);
        JSON_SET_KEY_BY_MEMBER(pSub, SlotFormat, DPCH[i], SlotFormat);
        JSON_SET_KEY_BY_MEMBER(pSub, SymbRate, DPCH[i], SymbRate);
        JSON_SET_KEY_BY_MEMBER(pSub, ChanCode, DPCH[i], ChanCode);

        JSON_OBJ_DEFINE(pDCH);
        Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHDCHInWCDMA(pDCH, DPCH[i].DCH);
        JSON_SET_KEY_BY_JSON(pSub, DCH, pDCH);

        JSON_SET_KEY_BY_MEMBER(pSub, Power, DPCH[i], Power);
        JSON_ARRAY_APPEND_JSON(pDPCH, pSub);
    }
}

static void Format_3GPP_ALZ_Alg_3GPP_AlzDLInWCDMA(WT_JSON_TYPE pDL, Alg_3GPP_AlzDLInWCDMA &DL)
{
    (void)pDL;
    (void)DL;
    JSON_SET_KEY_BY_MEMBER(pDL, ScramblingState, DL, ScramblingState);
    JSON_SET_KEY_BY_MEMBER(pDL, ScramblingCode, DL, ScramblingCode);

    JSON_OBJ_DEFINE(pPCPICH);
    Format_3GPP_ALZ_Alg_3GPP_AlzChannelPCPICHInWCDMA(pPCPICH, DL.PCPICH);
    JSON_SET_KEY_BY_JSON(pDL, PCPICH, pPCPICH);

    JSON_OBJ_DEFINE(pPSCH);
    Format_3GPP_ALZ_Alg_3GPP_AlzChannelPSCHInWCDMA(pPSCH, DL.PSCH);
    JSON_SET_KEY_BY_JSON(pDL, PSCH, pPSCH);

    JSON_OBJ_DEFINE(pSSCH);
    Format_3GPP_ALZ_Alg_3GPP_AlzChannelSSCHInWCDMA(pSSCH, DL.SSCH);
    JSON_SET_KEY_BY_JSON(pDL, SSCH, pSSCH);

    JSON_SET_KEY_BY_MEMBER(pDL, DPCHNum, DL, DPCHNum);

    JSON_ARRAY_DEFINE(pDPCH);
    Format_3GPP_ALZ_Alg_3GPP_AlzChannelDPCHInWCDMA(pDPCH, DL.DPCH, ARRAYSIZE(DL.DPCH));
    JSON_SET_KEY_BY_JSON(pDL, DPCH, pDPCH);
}

static void Format_3GPP_ALZ_WCDMA(WT_JSON_TYPE pWCDMA, Alg_3GPP_AlzInWCDMA &WCDMA)
{
    (void)pWCDMA;
    (void)WCDMA;

    JSON_SET_KEY_BY_MEMBER(pWCDMA, LinkDirect, WCDMA, LinkDirect);

    switch (WCDMA.LinkDirect)
    {
    case ALG_3GPP_UL:
    {
        JSON_OBJ_DEFINE(pUL);
        Format_3GPP_ALZ_Alg_3GPP_AlzULInWCDMA(pUL, WCDMA.UL);
        JSON_SET_KEY_BY_JSON(pWCDMA, UL, pUL);
    }
    break;

    case ALG_3GPP_DL:
    {
        JSON_OBJ_DEFINE(pDL);
        Format_3GPP_ALZ_Alg_3GPP_AlzDLInWCDMA(pDL, WCDMA.DL);
        JSON_SET_KEY_BY_JSON(pWCDMA, DL, pDL);
    }
    break;

    default:
        break;
    }

    JSON_OBJ_DEFINE(pMeasure);
    Format_3GPP_ALZ_Alg_3GPP_AlzMeasureInWCDMA(pMeasure, WCDMA.Measure);
    JSON_SET_KEY_BY_JSON(pWCDMA, Measure, pMeasure);
}

void Format_3GPP_ALZ_Json(WT_JSON_TYPE pRoot, AlzParam3GPP &Param)
{
    JSON_SET_KEY_BY_IMM(pRoot, Version, 1);
    JSON_SET_KEY_BY_MEMBER(pRoot, Standard, Param, Standard);

    // 以下一小段与API中是有差异的, API只处理与协议相关的数据
    // JSON_SET_KEY_BY_MEMBER(pRoot, analyzeGroup, Param, analyzeGroup);
    JSON_SET_KEY_BY_MEMBER(pRoot, DcFreqCompensate, Param, DcFreqCompensate);
    JSON_SET_KEY_BY_MEMBER(pRoot, SpectrumRBW, Param, SpectrumRBW);

    // JSON_ARRAY_DEFINE(rf_band);
    // for (int j = 0; j < ARRAYSIZE(Param.rf_band); ++j)
    // {
    //     JSON_ARRAY_APPEND_DATA(rf_band, Param.rf_band[j]);
    // }
    // JSON_SET_KEY_BY_JSON(pRoot, rf_band, rf_band);

    // JSON_ARRAY_DEFINE(rf_channel);
    // for (int j = 0; j < ARRAYSIZE(Param.rf_channel); ++j)
    // {
    //     JSON_ARRAY_APPEND_DATA(rf_channel, Param.rf_channel[j]);
    // }
    // JSON_SET_KEY_BY_JSON(pRoot, rf_channel, rf_channel);

    g_Alg3GPPSwitchCaseFlg = true;
    switch (Param.Standard)
    {
    case ALG_3GPP_STD_4G:
    {
        JSON_OBJ_DEFINE(pLTE);
        Format_3GPP_ALZ_LTE(pLTE, Param.LTE);
        JSON_SET_KEY_BY_JSON(pRoot, LTE, pLTE);
    }
    break;

    case ALG_3GPP_STD_5G:
    {
        JSON_OBJ_DEFINE(pNR);
        Format_3GPP_ALZ_NR(pNR, Param.NR);
        JSON_SET_KEY_BY_JSON(pRoot, NR, pNR);
    }
    break;

    case ALG_3GPP_STD_NB_IOT:
    {
        JSON_OBJ_DEFINE(pNBIOT);
        Format_3GPP_ALZ_NBIot(pNBIOT, Param.NBIOT);
        JSON_SET_KEY_BY_JSON(pRoot, NBIOT, pNBIOT);
    }
    break;

    case ALG_3GPP_STD_WCDMA:
    {
        JSON_OBJ_DEFINE(pWCDMA);
        Format_3GPP_ALZ_WCDMA(pWCDMA, Param.WCDMA);
        JSON_SET_KEY_BY_JSON(pRoot, WCDMA, pWCDMA);
    }
    break;

    default:
        break;
    }
}
