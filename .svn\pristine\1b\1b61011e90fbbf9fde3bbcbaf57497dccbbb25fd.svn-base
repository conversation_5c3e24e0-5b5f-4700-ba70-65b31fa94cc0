/***************************************************************************************************
 * This confidential and proprietary software may be only used as authorized by a licensing
 * agreement from iTest Technology Co., Ltd.
 * (C) COPYRIGHT 2020 iTest Technology Co., Ltd. ALL RIGHTS RESERVED
 *--------------------------------------------------------------------------------------------------
 * Filename             : alg_3gpp_listdef.h
 * Author               : Linden
 * Data                 : 2024-08
 * Description          :
 * Modification History :
 * Data            By          Version         Change Description
 *--------------------------------------------------------------------------------------------------
 * Reference to the source file.
 **************************************************************************************************/

#ifndef ALG_3GPP_LISTDEF_H_
#define ALG_3GPP_LISTDEF_H_

#include "alg_3gpp_apidef.h"
#include "alg_3gpp_apidef_5g.h"
#include "alg_3gpp_apidef_4g.h"
#include "alg_3gpp_apidef_nbiot.h"
#include "alg_3gpp_apidef_sidelink.h"

/**************************************************************************************************/
/*                                 Algorithm List Input Start                                     */
/**************************************************************************************************/
typedef struct {
    int RFInChanNum;
    /* RF data */
    Alg_3GPP_RFInInfo RFInfo[ALG_3GPP_MAX_STREAM];

    /* RF standard, reference ALG_3GPP_STANDARD_TYPE */
    int Standard;
    union {
        Alg_5G_ListInType NR;
        Alg_4G_ListInType LTE;
        Alg_NBIOT_ListInType NBIOT;
        Alg_SL_ListInType SL;
    };
} Alg_3GPP_ListInType;

/**************************************************************************************************/
/*                                  Algorithm List Input End                                      */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                 Algorithm List Output Start                                    */
/**************************************************************************************************/

typedef struct {
    int Standard;

    union {
        Alg_5G_ListOutType NR;
        Alg_4G_ListOutType LTE;
        Alg_NBIOT_ListOutType NBIOT;
        Alg_SL_ListOutType SL;
    };
} Alg_3GPP_ListOutType;

/**************************************************************************************************/
/*                                  Algorithm List Output End                                     */
/**************************************************************************************************/

#endif /* ALG_3GPP_LISTDEF_H_ */