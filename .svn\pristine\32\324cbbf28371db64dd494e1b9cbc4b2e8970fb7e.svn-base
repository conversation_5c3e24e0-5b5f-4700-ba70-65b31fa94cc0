/*
 * basefunc.cpp
 *
 *  Created on: 2019-8-1
 *      Author: Administrator
 */
#include <iostream>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "../general/conf.h"
#include "scpi_admintool.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "tester_admin.h"
#include "server/scpi_config.h"
#include <sys/types.h>
#include <dirent.h>
#include "commonhandler.h"
#include "conf.h"
#include "wtlog.h"
#include "listmod_sequence.h"
#include "cellular_analyze/scpi_3gpp_alz_default_param.h"

TimeTick::TimeTick(std::string msg)
{
    m_info = msg;
    gettimeofday(&m_start, nullptr);
}

TimeTick::~TimeTick()
{
    gettimeofday(&m_ends, nullptr);
    struct timeval res;
    timersub(&m_ends, &m_start, &res);
    int time_ms = (res.tv_sec * 1000000 + res.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << m_info << " use time = " << time_ms << " us" << std::endl;
}

UserDefindPSDU::UserDefindPSDU()
{
    reset();
}

void UserDefindPSDU::reset()
{
    SegmentID = 0;
    RUID = 0;
    UserID = 0;
    DataLen = 0;
    Data.reset(nullptr);
}

UserPSDU::UserPSDU()
{
    for (int i = 0; i < this->GetMaxSegment(); i++)
    {
        for (int j = 0; j < BE_RU_COUNT; j++)
        {
            for (int k = 0; k < MUMIMO_8_USER; k++)
            {
                WIFI_PSDU *dest = &PSDU[i][j][k];
                memset(dest, 0, sizeof(WIFI_PSDU));
                dest->psduLen = 128;
                dest->psduType = PSDUType_RANDOM;
                dest->scrambler = 12;
                dest->CRCCheckEnable = 1;
                dest->MacHeaderEnable = 1;
                dest->FrameControl[0] = 0x08;
                dest->FrameControl[1] = 0x00;
                for (int m = 0; m < 6; m++)
                {
                    dest->MacAddress1[m] = 0xFF;
                }
                dest->MPDUCount = 0;      // 默认等于0。API层会把MPDU纠正，避免没有配置MPDU count而导致PSDU长度不一致问题
                dest->EofPaddingType = 1; // 默认为1，使用Eof padding 子帧类型计算psdu 长度
                dest->MPDULength[0] = dest->psduLen;
            }
        }
    }
}

OFDMA_RU::OFDMA_RU()
{
    for (int i = 0; i < ARRAYSIZE(RUIndex); i++)
    {
        RUIndex[i] = -1;
    }
    ToneUserCnt.clear();
}

void CalibrationStruct::Reset()
{
    for (int i = 0; i < MAX_CALIBRTION_MODE; i++)
    {
        mode[i] = i;
        mode_enable[i] = 0;
        reload = 0;
        flatnessEnable = 1;
    }
}

SCPI_AlzParam::SCPI_AlzParam()
{
    Reset(false);
}

void SCPI_AlzParam::Reset(bool is_vsa)
{
    timeOut = 60.0;
    Reset_AlzParam(&commonAnalyzeParam);
    Reset_AlzParam(&analyzeParamWifi);
    Reset_AlzParam(&analyzeParamBt);
    Reset_AlzParam(&analyzeParamZigBee);
    Reset_AlzParam(&analyzeParamFft);
    Reset_3GPPAlzLteGeneralParam(analyzeParam3GPP, true, is_vsa);
    Reset_AlzParam(&analyzeParamWiSun);
    Reset_AlzParam(&analyzeParamZWave);

}

void SCPI_AlzParam::Reset_AlzParam(AlzParamWifi *param)
{
    memset(param, 0, sizeof(AlzParamWifi));

    param->Demode = WT_DEMOD_11AG;
    param->AutoDetect = WT_USER_DEFINED;
    param->Method11b = WT_11B_STANDARD_TX_ACC;
    param->DCRemoval = WT_DC_REMOVAL_OFF;
    param->EqTaps = WT_EQ_OFF;
    param->PhsCorrMode11B = WT_PH_CORR_11b_OFF;

    param->PhsCorrMode = WT_PH_CORR_SYM_BY_SYM;
    param->ChEstimate = WT_CH_EST_RAW;
    param->SynTimeCorr = WT_SYM_TIM_ON;
    param->FreqSyncMode = WT_FREQ_SYNC_AUTO;
    param->AmplTrack = WT_AMPL_TRACK_OFF;
    param->OfdmDemodOn = 1;
    param->MIMOAnalysisMode = 0;
    param->MimoMaxPowerDiff = 30;
    param->ClockRate = 1;
    param->ICISuppression = 2;
    param->SpectrumMaskVersion = 1;
    param->EqualizerSmoothing = 2;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamBT *param)
{
    memset(param, 0, sizeof(AlzParamBT));
    param->BTDataRate = WT_BT_DATARATE_Auto;
    param->BTPktType = WT_BT_PACKETTYPE_NULL;
    param->ACPSweepTimes = 1;
    param->ACPViewRangeType = 2;   ////默认采样率的一半；acp明文返回时固定返回中间的11点，arb返回就是真实数据控制点数
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamZigBee *param)
{
    memset(param, 0, sizeof(AlzParamZigBee));
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamFFT *param)
{
    memset(param, 0, sizeof(AlzParamFFT));
    param->Rbw = 100 * KHz_API;
    param->WindowType = 4;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamComm *param)
{
    memset(param, 0, sizeof(AlzParamComm));
    param->IQSwap = WT_IQ_SWAP_DISABLED;
    param->IQReversion = WT_IQ_IQReversion_DISABLED;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamSparkLink *param)
{
    memset(param, 0, sizeof(AlzParamSparkLink));
}

void SCPI_AlzParam::Reset_AlzParam(AlzParam3GPP &Param, int Demo, bool reset_base)
{
    using namespace cellular::alz;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Reset param Standard=%d, Demo=%d, reset_base=%d\n", Param.Standard, Demo, reset_base);
    if (Param.Standard != Demo)
    {
        switch (Demo)
        {
        case ALG_3GPP_STD_GSM:
            ResetGsmAlzParam(Param);
            break;
        case ALG_3GPP_STD_WCDMA:
            Reset_3GPPAlzWCDMAGeneralParam(Param, reset_base);
            break;
        case ALG_3GPP_STD_5G:
            Reset_3GPPAlzNR5GGeneralParam(Param, reset_base);
            break;
        case ALG_3GPP_STD_NB_IOT:
            Reset_3GPPAlzNBIOTGeneralParam(Param, reset_base);
            break;
        case ALG_3GPP_STD_4G:
        default:
            Reset_3GPPAlzLteGeneralParam(Param, reset_base);
            break;
        }
    }
}

void SCPI_AlzParam::Reset_3GPPAlzLteGeneralParam(AlzParam3GPP &Param, bool reset_base, bool is_vsa)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "Reset_3GPPAlzLteGeneralParam 1 SpectrumRBW = %d\n", Param.SpectrumRBW);
    if (reset_base)
    {
        memset(&Param, 0, sizeof(AlzParam3GPP));
        Param.DcFreqCompensate = 1;
        Param.SpectrumRBW = 100000;

        // vsa关闭这几个视图结果, 提高性能
        // Power, Spectrum, CCDF
        if (is_vsa)
        {
            Param.MeasPowerGraph = 0;
            Param.MeasSpectrum = 0;
            Param.MeasCCDF = 0;
            
            Reset_3GPPAlzLteLimitInfoParam(Param.LTE.LimitInfo);
        }
        else 
        {
            // 默认开启这几个视图
            Param.MeasPowerGraph = 1;
            Param.MeasSpectrum = 1;
            Param.MeasCCDF = 1;
        }

        for(int i = 0; i<ARRAYSIZE(Param.rf_band); ++i)
        {
            Param.rf_band[i] = 1;
        }

    }
    else
    {
        memset(&Param.LTE, 0, sizeof(Param.LTE));
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Reset_3GPPAlzLteGeneralParam 2 SpectrumRBW = %d\n", Param.SpectrumRBW);

    Param.Standard = ALG_3GPP_STD_4G;

    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; i++)
    {
        Param.LTE.Cell[i].CellIdx = i;
        Param.LTE.Cell[i].State = (i == 0 ? 1 : 0);
        Param.LTE.Cell[i].ChannelBW = 20000000;
    }
    Param.LTE.MeasInfo.EvmSubcarrierState = 1;
    Param.LTE.ChanType = ALG_4G_PUSCH;
    Reset_3GPPAlzLtePuschParam(Param.LTE.Pusch);
}

void SCPI_AlzParam::Reset_3GPPAlzLteLimitInfoParam(Alg_3GPP_LimitIn4g &Param)
{
    Param.ModLimit[0].EvmRms.State = 1;
    Param.ModLimit[0].EvmRms.Limit = 17.5;
    Param.ModLimit[0].EvmPeak.Limit = 35.0;
    Param.ModLimit[0].MErrRms.Limit = 17.5;
    Param.ModLimit[0].MErrPeak.Limit = 35.0;
    Param.ModLimit[0].PhErrRms.Limit = 17.5;
    Param.ModLimit[0].PhErrPeak.Limit = 35.0;
    Param.ModLimit[0].FreqErr.State = 1;
    Param.ModLimit[0].FreqErr.Limit = 0.1;
    Param.ModLimit[0].IQOffset.State = 1;
    Param.ModLimit[0].IQOffset.PwrLimit[0] = -24.2;
    Param.ModLimit[0].IQOffset.PwrLimit[1] = -19.2;
    Param.ModLimit[0].IQOffset.PwrLimit[2] = -9.2;
    Param.ModLimit[0].IBE.State = 1;
    Param.ModLimit[0].IBE.GenMin = -29.2;
    Param.ModLimit[0].IBE.GenEVM = 17.5;
    Param.ModLimit[0].IBE.GenPwr = -57.0;
    Param.ModLimit[0].IBE.IQImage[0] = -24.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[0] = -24.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[1] = -19.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[2] = -9.2;
    Param.ModLimit[0].SpectFlat.State = 1;
    Param.ModLimit[0].SpectFlat.Range1 = 5.4;
    Param.ModLimit[0].SpectFlat.Range2 = 9.4;
    Param.ModLimit[0].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[0].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[0].SpectFlat.EdgeFreq = 3.0;

    Param.ModLimit[1].EvmRms.State = 1;
    Param.ModLimit[1].EvmRms.Limit = 12.5;
    Param.ModLimit[1].EvmPeak.Limit = 25.0;
    Param.ModLimit[1].MErrRms.Limit = 12.5;
    Param.ModLimit[1].MErrPeak.Limit = 25.0;
    Param.ModLimit[1].PhErrRms.Limit = 12.5;
    Param.ModLimit[1].PhErrPeak.Limit = 25.0;
    Param.ModLimit[1].FreqErr.State = 1;
    Param.ModLimit[1].FreqErr.Limit = 0.1;
    Param.ModLimit[1].IQOffset.State = 1;
    Param.ModLimit[1].IQOffset.PwrLimit[0] = -24.2;
    Param.ModLimit[1].IQOffset.PwrLimit[1] = -19.2;
    Param.ModLimit[1].IQOffset.PwrLimit[2] = -9.2;
    Param.ModLimit[1].IBE.State = 1;
    Param.ModLimit[1].IBE.GenMin = -29.2;
    Param.ModLimit[1].IBE.GenEVM = 12.5;
    Param.ModLimit[1].IBE.GenPwr = -57.0;
    Param.ModLimit[1].IBE.IQImage[0] = -24.2;
    Param.ModLimit[1].IBE.IQOffsetPwr[0] = -24.2;
    Param.ModLimit[1].IBE.IQOffsetPwr[1] = -19.2;
    Param.ModLimit[1].IBE.IQOffsetPwr[2] = -9.2;
    Param.ModLimit[1].SpectFlat.State = 1;
    Param.ModLimit[1].SpectFlat.Range1 = 5.4;
    Param.ModLimit[1].SpectFlat.Range2 = 9.4;
    Param.ModLimit[1].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[1].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[1].SpectFlat.EdgeFreq = 3.0;

    Param.ModLimit[2].EvmRms.State = 1;
    Param.ModLimit[2].EvmRms.Limit = 8.0;
    Param.ModLimit[2].EvmPeak.Limit = 15.0;
    Param.ModLimit[2].MErrRms.Limit = 8.0;
    Param.ModLimit[2].MErrPeak.Limit = 15.0;
    Param.ModLimit[2].PhErrRms.Limit = 8.0;
    Param.ModLimit[2].PhErrPeak.Limit = 15.0;
    Param.ModLimit[2].FreqErr.State = 1;
    Param.ModLimit[2].FreqErr.Limit = 0.1;
    Param.ModLimit[2].IQOffset.State = 1;
    Param.ModLimit[2].IQOffset.PwrLimit[0] = -24.2;
    Param.ModLimit[2].IQOffset.PwrLimit[1] = -19.2;
    Param.ModLimit[2].IQOffset.PwrLimit[2] = -9.2;
    Param.ModLimit[2].IBE.State = 1;
    Param.ModLimit[2].IBE.GenMin = -29.2;
    Param.ModLimit[2].IBE.GenEVM = 8.0;
    Param.ModLimit[2].IBE.GenPwr = -57.0;
    Param.ModLimit[2].IBE.IQImage[0] = -24.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[0] = -24.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[1] = -19.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[2] = -9.2;
    Param.ModLimit[2].SpectFlat.State = 1;
    Param.ModLimit[2].SpectFlat.Range1 = 5.4;
    Param.ModLimit[2].SpectFlat.Range2 = 9.4;
    Param.ModLimit[2].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[2].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[2].SpectFlat.EdgeFreq = 3.0;

    Param.ModLimit[3].EvmRms.State = 1;
    Param.ModLimit[3].EvmRms.Limit = 3.5;
    Param.ModLimit[3].EvmPeak.Limit = 7.0;
    Param.ModLimit[3].MErrRms.Limit = 3.5;
    Param.ModLimit[3].MErrPeak.Limit = 7.0;
    Param.ModLimit[3].PhErrRms.Limit = 3.5;
    Param.ModLimit[3].PhErrPeak.Limit = 7.0;
    Param.ModLimit[3].FreqErr.State = 1;
    Param.ModLimit[3].FreqErr.Limit = 0.1;
    Param.ModLimit[3].IQOffset.State = 1;
    Param.ModLimit[3].IQOffset.PwrLimit[0] = -24.2;
    Param.ModLimit[3].IQOffset.PwrLimit[1] = -19.2;
    Param.ModLimit[3].IQOffset.PwrLimit[2] = -9.2;
    Param.ModLimit[3].IBE.State = 1;
    Param.ModLimit[3].IBE.GenMin = -29.2;
    Param.ModLimit[3].IBE.GenEVM = 3.5;
    Param.ModLimit[3].IBE.GenPwr = -57.0;
    Param.ModLimit[3].IBE.IQImage[0] = -24.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[0] = -24.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[1] = -19.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[2] = -9.2;
    Param.ModLimit[3].SpectFlat.State = 1;
    Param.ModLimit[3].SpectFlat.Range1 = 5.4;
    Param.ModLimit[3].SpectFlat.Range2 = 9.4;
    Param.ModLimit[3].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[3].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[3].SpectFlat.EdgeFreq = 3.0;

    Param.SpectLimit[0].OBWLimit.Limit = 1.4;
    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit[0].SEMLimit); i++)
    {
        Param.SpectLimit[0].SEMLimit[i].RBW = 1000000;
    }
    Param.SpectLimit[0].SEMLimit[0].RBW = 30000;

    Param.SpectLimit[0].UtraLimit[0].RelState = 1;
    Param.SpectLimit[0].UtraLimit[0].RelLimit = 32.2;
    Param.SpectLimit[0].UtraLimit[0].AbsState = 1;
    Param.SpectLimit[0].UtraLimit[0].AbsPwr = -50.0;
    Param.SpectLimit[0].UtraLimit[1].RelLimit = 35.2;
    Param.SpectLimit[0].UtraLimit[1].AbsPwr = -50.0;
    Param.SpectLimit[0].EUtraLimit.RelState = 1;
    Param.SpectLimit[0].EUtraLimit.RelLimit = 29.2;
    Param.SpectLimit[0].EUtraLimit.AbsState = 1;
    Param.SpectLimit[0].EUtraLimit.AbsPwr = -50.0;

    Param.SpectLimit[1].OBWLimit.Limit = 3.0;
    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit[0].SEMLimit); i++)
    {
        Param.SpectLimit[1].SEMLimit[i].RBW = 1000000;
    }
    Param.SpectLimit[1].SEMLimit[0].RBW = 30000;

    Param.SpectLimit[1].UtraLimit[0].RelState = 1;
    Param.SpectLimit[1].UtraLimit[0].RelLimit = 32.2;
    Param.SpectLimit[1].UtraLimit[0].AbsState = 1;
    Param.SpectLimit[1].UtraLimit[0].AbsPwr = -50.0;
    Param.SpectLimit[1].UtraLimit[1].RelLimit = 35.2;
    Param.SpectLimit[1].UtraLimit[1].AbsPwr = -50.0;
    Param.SpectLimit[1].EUtraLimit.RelState = 1;
    Param.SpectLimit[1].EUtraLimit.RelLimit = 29.2;
    Param.SpectLimit[1].EUtraLimit.AbsState = 1;
    Param.SpectLimit[1].EUtraLimit.AbsPwr = -50.0;

    Param.SpectLimit[2].OBWLimit.Limit = 5.0;
    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit[0].SEMLimit); i++)
    {
        Param.SpectLimit[2].SEMLimit[i].RBW = 1000000;
    }
    Param.SpectLimit[2].SEMLimit[0].RBW = 30000;

    Param.SpectLimit[2].UtraLimit[0].RelState = 1;
    Param.SpectLimit[2].UtraLimit[0].RelLimit = 32.2;
    Param.SpectLimit[2].UtraLimit[0].AbsState = 1;
    Param.SpectLimit[2].UtraLimit[0].AbsPwr = -50.0;
    Param.SpectLimit[2].UtraLimit[1].RelLimit = 35.2;
    Param.SpectLimit[2].UtraLimit[1].AbsState = 1;
    Param.SpectLimit[2].UtraLimit[1].AbsPwr = -50.0;
    Param.SpectLimit[2].EUtraLimit.RelState = 1;
    Param.SpectLimit[2].EUtraLimit.RelLimit = 29.2;
    Param.SpectLimit[2].EUtraLimit.AbsState = 1;
    Param.SpectLimit[2].EUtraLimit.AbsPwr = -50.0;

    Param.SpectLimit[4].OBWLimit.Limit = 10.0;
    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit[0].SEMLimit); i++)
    {
        Param.SpectLimit[4].SEMLimit[i].RBW = 1000000;
    }
    Param.SpectLimit[4].SEMLimit[0].RBW = 30000;

    Param.SpectLimit[4].UtraLimit[0].RelState = 1;
    Param.SpectLimit[4].UtraLimit[0].RelLimit = 32.2;
    Param.SpectLimit[4].UtraLimit[0].AbsState = 1;
    Param.SpectLimit[4].UtraLimit[0].AbsPwr = -50.0;
    Param.SpectLimit[4].UtraLimit[1].RelState = 1;
    Param.SpectLimit[4].UtraLimit[1].RelLimit = 35.2;
    Param.SpectLimit[4].UtraLimit[1].AbsState = 1;
    Param.SpectLimit[4].UtraLimit[1].AbsPwr = -50.0;
    Param.SpectLimit[4].EUtraLimit.RelState = 1;
    Param.SpectLimit[4].EUtraLimit.RelLimit = 29.2;
    Param.SpectLimit[4].EUtraLimit.AbsState = 1;
    Param.SpectLimit[4].EUtraLimit.AbsPwr = -50.0;

    Param.SpectLimit[4].OBWLimit.Limit = 15.0;
    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit[0].SEMLimit); i++)
    {
        Param.SpectLimit[4].SEMLimit[i].RBW = 1000000;
    }
    Param.SpectLimit[4].SEMLimit[0].RBW = 30000;

    Param.SpectLimit[4].UtraLimit[0].RelState = 1;
    Param.SpectLimit[4].UtraLimit[0].RelLimit = 32.2;
    Param.SpectLimit[4].UtraLimit[0].AbsState = 1;
    Param.SpectLimit[4].UtraLimit[0].AbsPwr = -50.0;
    Param.SpectLimit[4].UtraLimit[1].RelState = 1;
    Param.SpectLimit[4].UtraLimit[1].RelLimit = 35.2;
    Param.SpectLimit[4].UtraLimit[1].AbsState = 1;
    Param.SpectLimit[4].UtraLimit[1].AbsPwr = -50.0;
    Param.SpectLimit[4].EUtraLimit.RelState = 1;
    Param.SpectLimit[4].EUtraLimit.RelLimit = 29.2;
    Param.SpectLimit[4].EUtraLimit.AbsState = 1;
    Param.SpectLimit[4].EUtraLimit.AbsPwr = -50.0;

    Param.SpectLimit[5].OBWLimit.Limit = 20.0;
    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit[0].SEMLimit); i++)
    {
        Param.SpectLimit[5].SEMLimit[i].RBW = 1000000;
    }
    Param.SpectLimit[5].SEMLimit[0].RBW = 30000;

    Param.SpectLimit[5].UtraLimit[0].RelState = 1;
    Param.SpectLimit[5].UtraLimit[0].RelLimit = 32.2;
    Param.SpectLimit[5].UtraLimit[0].AbsState = 1;
    Param.SpectLimit[5].UtraLimit[0].AbsPwr = -50.0;
    Param.SpectLimit[5].UtraLimit[1].RelState = 1;
    Param.SpectLimit[5].UtraLimit[1].RelLimit = 35.2;
    Param.SpectLimit[5].UtraLimit[1].AbsState = 1;
    Param.SpectLimit[5].UtraLimit[1].AbsPwr = -50.0;
    Param.SpectLimit[5].EUtraLimit.RelState = 1;
    Param.SpectLimit[5].EUtraLimit.RelLimit = 29.2;
    Param.SpectLimit[5].EUtraLimit.AbsState = 1;
    Param.SpectLimit[5].EUtraLimit.AbsPwr = -50.0;

    Param.SEMAddTestTol[0] = 0.3;
    Param.SEMAddTestTol[1] = 0.5;
}

void SCPI_AlzParam::Reset_3GPPAlzLteLinkParam(AlzParam3GPP &Param, int LinkDirect)
{
    // LTE处理比较特殊, 它的Cell与MeasInfo成员在切换上下行的时候
    // 也必须同时重置，否则这部分的配置不会更新导致分析参数异常
    WTLog::Instance().WriteLog(LOG_DEBUG, "Reset_3GPPAlzLteLinkParam 1 Param.SpectrumRBW=%d\n", Param.SpectrumRBW);

    int ChanType = -1;
    int DefaultChannal[2] = {ALG_4G_PUSCH, ALG_4G_PDSCH};
    if (LinkDirect == ALG_3GPP_UL)
    {
        if ((Param.LTE.ChanType != ALG_4G_PUSCH) && (Param.LTE.ChanType != ALG_4G_PUCCH))
        {
            ChanType = DefaultChannal[LinkDirect];
        }
    }
    else if (LinkDirect == ALG_3GPP_DL)
    {
        if ((Param.LTE.ChanType < ALG_4G_PDSCH) || (Param.LTE.ChanType > ALG_4G_PHICH))
        {
            ChanType = DefaultChannal[LinkDirect];
        }
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "Reset_3GPPAlzLteLinkParam ChanType=%d Param.ChanType=%d\n", ChanType, Param.ChanType);
    if (ChanType != -1)
    {
        memset(&Param.LTE, 0, sizeof(Param.LTE));
        for (int i = 0; i < ALG_4G_MAX_CELL_NUM; i++)
        {
            Param.LTE.Cell[i].CellIdx = i;
            Param.LTE.Cell[i].State = (i == 0 ? 1 : 0);
            Param.LTE.Cell[i].ChannelBW = 20000000;
        }
        Param.LTE.MeasInfo.EvmSubcarrierState = 1;
        Param.LTE.ChanType = -1; // 强制重置Channal分析参数
        Reset_3GPPAlzLteChanParam(Param.LTE, ChanType);
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "Reset_3GPPAlzLteLinkParam 2 Param.SpectrumRBW=%d\n", Param.SpectrumRBW);
}

void SCPI_AlzParam::Reset_3GPPAlzLteChanParam(Alg_3GPP_AlzIn4g &Param, int ChanType)
{
    if (Param.ChanType != ChanType)
    {
        Param.ChanType = ChanType;
        switch (ChanType)
        {
        case ALG_4G_PUSCH:
            Reset_3GPPAlzLtePuschParam(Param.Pusch);
            break;
        case ALG_4G_PDSCH:
            Reset_3GPPAlzLtePdschParam(Param.Pdsch);
            break;
        default:
            break;
        }
    }
}
void SCPI_AlzParam::Reset_3GPPAlzLtePuschParam(Alg_3GPP_AlzPusch4g *Param)
{
    memset(Param, 0, sizeof(Alg_3GPP_AlzPusch4g) * ALG_4G_MAX_CELL_NUM);
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; i++)
    {
        Param[i].CellIdx = i;
        Param[i].State = (i == 0 ? 1 : 0);
        Param[i].RBNum = 100;
        Param[i].LayerNum = 1;
        Param[i].AntennaNum = 1;
        Param[i].Codeword = 1;
        for (int j = 0; j < ARRAYSIZE(Param[i].PayloadSize); ++j)
        {
            Param[i].PayloadSize[j] = 1500;
        }
    }
}

void SCPI_AlzParam::Reset_3GPPAlzLtePdschParam(Alg_3GPP_AlzPdsch4g &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzPdsch4g));
    Param.ResAllocateType = 2;
    Param.RBNum = 1;
    Param.LayerNum = 1;
    Param.AntennaNum = 1;
    for (int i = 0; i < ARRAYSIZE(Param.Modulate); ++i)
    {
        Param.Modulate[i] = 2;
    }
    Param.ChanCodingState = 1;
    Param.Scramble = 1;
    Param.McsTable = 1;
    Param.TxMode = 1;
    Param.UECategory = 1;
    Param.Codeword = 1;
    
    for (int i = 0; i < ARRAYSIZE(Param.PayloadSize); ++i)
    {
        Param.PayloadSize[i] = 1500;
    }
    
    for (int i = 0; i < ARRAYSIZE(Param.NIR); ++i)
    {
        Param.NIR[i] = 3667200;
    }
    
    for (int i = 0; i < ARRAYSIZE(Param.SoftChanBit); ++i)
    {
        Param.SoftChanBit[i] = 58675200;
    }
}

void SCPI_AlzParam::Reset_3GPPAlzNR5GGeneralParam(AlzParam3GPP &Param, bool reset_base)
{
    if (reset_base)
    {
        memset(&Param, 0, sizeof(AlzParam3GPP));
        Param.DcFreqCompensate = 1;
        Param.SpectrumRBW = 30000;
        Param.MeasPowerGraph = 0;
        Param.MeasSpectrum = 0;
        Param.MeasCCDF = 0;
    }
    else
    {
        memset(&Param.NR, 0, sizeof(Param.NR));
    }

    Param.Standard = ALG_3GPP_STD_5G;
    Param.NR.Measure.EvmSubcarrierState = 1;
    Param.NR.Measure.EVMDelDCRBFlag = 1;
    Param.NR.LinkDirect = ALG_3GPP_UL;

    Reset_3GPPAlzNR5GUpLinkParam(Param.NR.UL);
}

void SCPI_AlzParam::Reset_3GPPAlzWCDMAGeneralParam(AlzParam3GPP &Param, bool reset_base)
{
    if (reset_base)
    {
        memset(&Param, 0, sizeof(AlzParam3GPP));
        Param.DcFreqCompensate = 1;
        Param.SpectrumRBW = 2000;
    }
    else
    {
        memset(&Param.WCDMA, 0, sizeof(Param.WCDMA));
    }

    Param.Standard = ALG_3GPP_STD_WCDMA;
    Param.WCDMA.LinkDirect = ALG_3GPP_UL;

    Reset_3GPPAlzWCDMAUpLinkParam(Param.WCDMA.UL);
    Reset_3GPPAlzWCDMAMeasureParam(Param.WCDMA.Measure);
}

void SCPI_AlzParam::Reset_3GPPAlzWCDMAUpLinkParam(Alg_3GPP_AlzULInWCDMA &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzULInWCDMA));
    Param.DPDCHAvailable = 1;
    Param.MeasureLen = 1;
    Param.SyncSlotId = -1;
    Param.CDPSpreadFactor = 4;
}

void SCPI_AlzParam::Reset_3GPPAlzWCDMADownLinkParam(Alg_3GPP_AlzDLInWCDMA &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzDLInWCDMA));
    Param.PCPICH.State = 1;
    Param.PSCH.State = 1;
    Param.SSCH.State = 1;
    Param.DPCHNum = 1;
    Param.DPCH[0].State = 1;
    Param.DPCH[0].ChanCode = 3;
    Param.DPCH[0].DCH.State = 1;
    for(int i = 0; i < ARRAYSIZE(Param.DPCH); i++) 
    {
        Param.DPCH[i].SlotFormat = 2;
        Param.DPCH[i].SymbRate = 15000;
        Param.DPCH[i].DCH.Interleaver2Stat = 1;
        Param.DPCH[i].DCH.DCCH.TbCount = 1;
        Param.DPCH[i].DCH.DCCH.TbSize = 100;
        Param.DPCH[i].DCH.DCCH.Crc = 16;
        Param.DPCH[i].DCH.DCCH.RmAttribute = 1;
        Param.DPCH[i].DCH.DCCH.EProtection = 3;
        Param.DPCH[i].DCH.DCCH.InterleaverStat = 1;
        for(int j = 0; j < ARRAYSIZE(Param.DPCH[i].DCH.DTCH); j++)
        {
            Param.DPCH[i].DCH.DTCH[j].TbCount = 1;
            Param.DPCH[i].DCH.DTCH[j].TbSize = 100;
            Param.DPCH[i].DCH.DTCH[j].Crc = 16;
            Param.DPCH[i].DCH.DTCH[j].RmAttribute = 1;
            Param.DPCH[i].DCH.DTCH[j].EProtection = 3;
            Param.DPCH[i].DCH.DTCH[j].InterleaverStat = 1;
        }
    }
}

void SCPI_AlzParam::Reset_3GPPAlzWCDMAMeasureParam(Alg_3GPP_AlzMeasureWCDMA &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzMeasureWCDMA));
    Param.PeakMagnErrLimitMode = 1;
    Param.PeakMagnErrLimit = 50.0;
    Param.RmsMagnErrLimitMode = 1;
    Param.RmsMagnErrLimit = 17.5;
    Param.PeakEvmLimitMode = 1;
    Param.PeakEvmLimit = 50.0;
    Param.RmsEvmLimitMode = 1;
    Param.RmsEvmLimit = 50.0;
    Param.PeakPhaseErrLimitMode = 1;
    Param.PeakPhaseErrLimit = 45.0;
    Param.RmsPhaseErrLimitMode = 1;
    Param.RmsPhaseErrLimit = 10.0;
    Param.CFErrLimitMode = 1;
    Param.CFErrLimit = 200;
    Param.PhaseDisLimitMode = 1;
    Param.UpperLimit = 66.0;
    Param.DynamicLimit = 36.0;
    Param.AclrLimit1Mode = 1;
    Param.UtraLimit1 = -32.2;
    Param.AclrLimit2Mode = 1;
    Param.UtraLimit2 = -42.2;
    Param.SEMLimitADMode = 1;
    Param.LimitAD[0] = -47.5;
    Param.LimitAD[1] = -47.5;
    Param.LimitAD[2] = -37.5;
    Param.LimitAD[3] = -33.5;
    Param.SEMLimitEFMode = 1;
    Param.LimitEF[0] = -48.5;
    Param.LimitEF[1] = -33.5;
    Param.AclrAbsLimitMode = 1;
    Param.AbsLimit = -50.0;
}

void SCPI_AlzParam::Reset_3GPPAlzWCDMALinkParam(Alg_3GPP_AlzInWCDMA &Param, int LinkDirect)
{
    if (Param.LinkDirect != LinkDirect)
    {
        memset(&Param, 0, sizeof(Alg_3GPP_AlzInWCDMA));
        Param.LinkDirect = LinkDirect;
        switch (LinkDirect)
        {
        case ALG_3GPP_UL:
            Reset_3GPPAlzWCDMAUpLinkParam(Param.UL);
            break;
        case ALG_3GPP_DL:
            Reset_3GPPAlzWCDMADownLinkParam(Param.DL);
            break;
        default:
            break;
        }
        
        Reset_3GPPAlzWCDMAMeasureParam(Param.Measure);
    }
}

void SCPI_AlzParam::Reset_3GPPAlzNR5GLinkParam(Alg_3GPP_AlzIn5g &Param, int LinkDirect)
{
    if (Param.LinkDirect != LinkDirect)
    {
        memset(&Param, 0, sizeof(Alg_3GPP_AlzIn5g));
        Param.LinkDirect = LinkDirect;
        switch (LinkDirect)
        {
        case ALG_3GPP_UL:
            Reset_3GPPAlzNR5GUpLinkParam(Param.UL);
            break;
        case ALG_3GPP_DL:
            Reset_3GPPAlzNR5GDownLinkParam(Param.DL);
            break;
        default:
            break;
        }
        Param.Measure.EvmSubcarrierState = 1;

        Reset_3GPPAlzNR5GLimitParam(Param.LimitInfo);
    }
}

void SCPI_AlzParam::Reset_3GPPAlzNR5GUpLinkParam(Alg_3GPP_AlzULIn5g &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzULIn5g));
    Param.SlotPeriod = 10;
    Param.ULSlotnumber = 9;
    Param.CellNum = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.Cell[i].CellIdx = i;
        Param.Cell[i].State = (i == 0 ? 1 : 0);
        Param.Cell[i].Frequency = 1950;
        Param.Cell[i].ChannelBW = 20 * MHz_API;
        Param.Cell[i].DmrsTypeAPos = 2;
        Param.Cell[i].TxBW[0].SCSpacing = 15 * KHz_API;
        Param.Cell[i].TxBW[1].SCSpacing = 30 * KHz_API;
        Param.Cell[i].TxBW[2].SCSpacing = 60 * KHz_API;

        Param.Cell[i].TxBW[1].State = 1;

        Param.Cell[i].TxBW[0].MaxRBNumb = 106;
        Param.Cell[i].TxBW[1].MaxRBNumb = 51;
        Param.Cell[i].TxBW[2].MaxRBNumb = 24;

        Param.Cell[i].Bwp.SCSpacing = 30 * KHz_API;
        Param.Cell[i].Bwp.RBNum = 51;
        Param.Cell[i].Bwp.ResourceAllocation = 1;
        Param.Cell[i].Bwp.Dmrs.ConfigType = 1;
        Param.Cell[i].Bwp.Dmrs.MaxLength = 1;
        Param.Cell[i].Bwp.Dmrs.AdditionalPos = 2;
    }
    Param.ChanType = ALG_4G_PUSCH;
    Reset_3GPPAlzNR5GPuschParam(Param.Pusch);
}

void SCPI_AlzParam::Reset_3GPPAlzNR5GDownLinkParam(Alg_3GPP_AlzDLIn5g &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzDLIn5g));
    Param.SlotPeriod = 10;
    Param.DLSlotNumber = 9;
    Param.CellNum = 1;
    Param.Cell[0].State = 1;
    Param.Channel[0].Pdsch.State = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.Cell[i].CellIdx = i;  // 小区号
        Param.Cell[i].Frequency = 1950; // 单位: MHz
        Param.Cell[i].ChannelBW = 100 * MHz_API;
        Param.Cell[i].DmrsTypeAPos = 2;
        Param.Cell[i].TxBW[0].SCSpacing = 15 * KHz_API;
        Param.Cell[i].TxBW[1].SCSpacing = 30 * KHz_API;
        Param.Cell[i].TxBW[2].SCSpacing = 60 * KHz_API;

        Param.Cell[i].TxBW[1].State = 1;
        
        Param.Cell[i].TxBW[0].MaxRBNumb = 0;
        Param.Cell[i].TxBW[1].MaxRBNumb = 273;
        Param.Cell[i].TxBW[2].MaxRBNumb = 135;

        Param.Cell[i].Bwp.SCSpacing = 30 * KHz_API;
        Param.Cell[i].Bwp.RBNum = 273;
        Param.Cell[i].Bwp.Pdsch.ResourceAllocation = 1;
        Param.Cell[i].Bwp.Pdsch.ConfigType = 1;
        Param.Cell[i].Bwp.Pdsch.MaxLength = 2;
        Param.Cell[i].Bwp.Pdsch.RBGSizeType = 1;

        Param.Cell[i].Bwp.Coreset.State = 1;
        Param.Cell[i].Bwp.Coreset.SymbNum = 1;
        Param.Cell[i].Bwp.Coreset.RBNum = 6;
        memset(Param.Cell[i].Bwp.Coreset.BitMap, 1, sizeof(Param.Cell[i].Bwp.Coreset.BitMap));

        Param.Channel[i].Pbch.State = 1;
        Param.Channel[i].Pbch.SCSpacing = 30000;
        Param.Channel[i].Pbch.RBOffset = (273 - 20) / 2;
        Param.Channel[i].Pbch.SCOffset = 6;
        Param.Channel[i].Pbch.PbchCase = 1;
        Param.Channel[i].Pbch.Length = 4;
        Param.Channel[i].Pbch.Position[0] = 0;
        Param.Channel[i].Pbch.Position[1] = 0;
        Param.Channel[i].Pbch.Position[2] = 1;
        Param.Channel[i].Pbch.Position[3] = 1;
        Param.Channel[i].Pbch.BurstSetPeriod = 10;

        Param.Channel[i].Pdsch.SymbNum = 13;
        Param.Channel[i].Pdsch.SymbOffset = 1;
        Param.Channel[i].Pdsch.RBNum = 273; // 与param.NR.UL.Cell[i].Bwp.RBNum初始化一致;
        Param.Channel[i].Pdsch.LayerNum = 1;
        Param.Channel[i].Pdsch.AntennaNum = 1;
        Param.Channel[i].Pdsch.CDMGrpWOData = 1;
        Param.Channel[i].Pdsch.DmrsSymbLen = 1;
        for (int j = 0; j < ARRAYSIZE(Param.Channel[i].Pdsch.DmrsAntPort); j++)
        {
            Param.Channel[i].Pdsch.DmrsAntPort[j] = 1000;
        }
        Param.Channel[i].Pdsch.Codewords = 1;
        Param.Channel[i].Pdsch.Modulate[0] = 2;
        Param.Channel[i].Pdsch.Modulate[1] = 2;
        Param.Channel[i].Pdsch.ChanCodingState = 1;
        Param.Channel[i].Pdsch.Scrambling = 1;
        Param.Channel[i].Pdsch.UeID = 14;
        memset(Param.Channel[i].Pdsch.Bitmap, 1, sizeof(Param.Channel[i].Pdsch.Bitmap));
    }
    Param.ChanType = 3;
}

void SCPI_AlzParam::Reset_3GPPAlzNR5GPuschParam(Alg_3GPP_AlzPusch5g *Param)
{
    memset(Param, 0, sizeof(Alg_3GPP_AlzPusch5g) * ALG_5G_MAX_CELL_NUM);
    Param[0].State = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param[i].SymNum = 14;
        Param[i].RBDetMode = 1;
        Param[i].RBNum = 51; // 与param.NR.UL.Cell[i].Bwp.RBNum初始化一致;
        Param[i].LayerNum = 1;
        Param[i].AntennaNum = 1;
        Param[i].DmrsSymbLen = 1;
    }
}

void SCPI_AlzParam::Reset_3GPPAlzNR5GLimitParam(Alg_3GPP_LimitIn5g &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_LimitIn5g));

    // ModLimit
    Param.ModLimit[0].EvmRms.State = 1;
    Param.ModLimit[0].EvmRms.Limit = 30;
    Param.ModLimit[0].EvmPeak.Limit = 60;
    Param.ModLimit[0].MErrRms.Limit = 30;
    Param.ModLimit[0].MErrPeak.Limit = 60;
    Param.ModLimit[0].PhErrRms.Limit = 30;
    Param.ModLimit[0].PhErrPeak.Limit = 60;
    Param.ModLimit[0].FreqErr.State = 1;
    Param.ModLimit[0].FreqErr.Limit = 0.1;
    Param.ModLimit[0].IQOffset.State = 1;
    Param.ModLimit[0].IQOffset.PwrLimit[0] = -27.2;
    Param.ModLimit[0].IQOffset.PwrLimit[1] = -24.2;
    Param.ModLimit[0].IQOffset.PwrLimit[2] = -19.2;
    Param.ModLimit[0].IQOffset.PwrLimit[3] = -9.2;
    Param.ModLimit[0].IBE.State = 1;
    Param.ModLimit[0].IBE.GenMin = -29.2;
    Param.ModLimit[0].IBE.GenEVM = 30;
    Param.ModLimit[0].IBE.GenPwr = -57;
    Param.ModLimit[0].IBE.IQImage[0] = -27.2;
    Param.ModLimit[0].IBE.IQImage[1] = -24.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[0] = -27.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[1] = -24.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[2] = -19.2;
    Param.ModLimit[0].IBE.IQOffsetPwr[3] = -9.2;
    Param.ModLimit[0].SpectFlat.State = 1;
    Param.ModLimit[0].SpectFlat.Range1 = 5.4;
    Param.ModLimit[0].SpectFlat.Range2 = 9.4;
    Param.ModLimit[0].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[0].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[0].SpectFlat.EdgeFreq = 3;

    Param.ModLimit[2].EvmRms.State = 1;
    Param.ModLimit[2].EvmRms.Limit = 17.5;
    Param.ModLimit[2].EvmPeak.Limit = 35;
    Param.ModLimit[2].MErrRms.Limit = 17.5;
    Param.ModLimit[2].MErrPeak.Limit = 35;
    Param.ModLimit[2].PhErrRms.Limit = 17.5;
    Param.ModLimit[2].PhErrPeak.Limit = 35;
    Param.ModLimit[2].FreqErr.State = 1;
    Param.ModLimit[2].FreqErr.Limit = 0.1;
    Param.ModLimit[2].IQOffset.State = 1;
    Param.ModLimit[2].IQOffset.PwrLimit[0] = -27.2;
    Param.ModLimit[2].IQOffset.PwrLimit[1] = -24.2;
    Param.ModLimit[2].IQOffset.PwrLimit[2] = -19.2;
    Param.ModLimit[2].IQOffset.PwrLimit[3] = -9.2;
    Param.ModLimit[2].IBE.State = 1;
    Param.ModLimit[2].IBE.GenMin = -29.2;
    Param.ModLimit[2].IBE.GenEVM = 17.5;
    Param.ModLimit[2].IBE.GenPwr = -57;
    Param.ModLimit[2].IBE.IQImage[0] = -27.2;
    Param.ModLimit[2].IBE.IQImage[1] = -24.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[0] = -27.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[1] = -24.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[2] = -19.2;
    Param.ModLimit[2].IBE.IQOffsetPwr[3] = -9.2;
    Param.ModLimit[2].SpectFlat.State = 1;
    Param.ModLimit[2].SpectFlat.Range1 = 5.4;
    Param.ModLimit[2].SpectFlat.Range2 = 9.4;
    Param.ModLimit[2].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[2].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[2].SpectFlat.EdgeFreq = 3;

    Param.ModLimit[3].EvmRms.State = 1;
    Param.ModLimit[3].EvmRms.Limit = 12.5;
    Param.ModLimit[3].EvmPeak.Limit = 25;
    Param.ModLimit[3].MErrRms.Limit = 12.5;
    Param.ModLimit[3].MErrPeak.Limit = 25;
    Param.ModLimit[3].PhErrRms.Limit = 12.5;
    Param.ModLimit[3].PhErrPeak.Limit = 25;
    Param.ModLimit[3].FreqErr.State = 1;
    Param.ModLimit[3].FreqErr.Limit = 0.1;
    Param.ModLimit[3].IQOffset.State = 1;
    Param.ModLimit[3].IQOffset.PwrLimit[0] = -27.2;
    Param.ModLimit[3].IQOffset.PwrLimit[1] = -24.2;
    Param.ModLimit[3].IQOffset.PwrLimit[2] = -19.2;
    Param.ModLimit[3].IQOffset.PwrLimit[3] = -9.2;
    Param.ModLimit[3].IBE.State = 1;
    Param.ModLimit[3].IBE.GenMin = -29.2;
    Param.ModLimit[3].IBE.GenEVM = 12.5;
    Param.ModLimit[3].IBE.GenPwr = -57;
    Param.ModLimit[3].IBE.IQImage[0] = -27.2;
    Param.ModLimit[3].IBE.IQImage[1] = -24.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[0] = -27.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[1] = -24.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[2] = -19.2;
    Param.ModLimit[3].IBE.IQOffsetPwr[3] = -9.2;
    Param.ModLimit[3].SpectFlat.State = 1;
    Param.ModLimit[3].SpectFlat.Range1 = 5.4;
    Param.ModLimit[3].SpectFlat.Range2 = 9.4;
    Param.ModLimit[3].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[3].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[3].SpectFlat.EdgeFreq = 3;

    Param.ModLimit[4].EvmRms.State = 1;
    Param.ModLimit[4].EvmRms.Limit = 8;
    Param.ModLimit[4].EvmPeak.Limit = 16;
    Param.ModLimit[4].MErrRms.Limit = 8;
    Param.ModLimit[4].MErrPeak.Limit = 16;
    Param.ModLimit[4].PhErrRms.Limit = 8;
    Param.ModLimit[4].PhErrPeak.Limit = 16;
    Param.ModLimit[4].FreqErr.State = 1;
    Param.ModLimit[4].FreqErr.Limit = 0.1;
    Param.ModLimit[4].IQOffset.State = 1;
    Param.ModLimit[4].IQOffset.PwrLimit[0] = -27.2;
    Param.ModLimit[4].IQOffset.PwrLimit[1] = -24.2;
    Param.ModLimit[4].IQOffset.PwrLimit[2] = -19.2;
    Param.ModLimit[4].IQOffset.PwrLimit[3] = -9.2;
    Param.ModLimit[4].IBE.State = 1;
    Param.ModLimit[4].IBE.GenMin = -29.2;
    Param.ModLimit[4].IBE.GenEVM = 8;
    Param.ModLimit[4].IBE.GenPwr = -57;
    Param.ModLimit[4].IBE.IQImage[0] = -27.2;
    Param.ModLimit[4].IBE.IQImage[1] = -24.2;
    Param.ModLimit[4].IBE.IQOffsetPwr[0] = -27.2;
    Param.ModLimit[4].IBE.IQOffsetPwr[1] = -24.2;
    Param.ModLimit[4].IBE.IQOffsetPwr[2] = -19.2;
    Param.ModLimit[4].IBE.IQOffsetPwr[3] = -9.2;
    Param.ModLimit[4].SpectFlat.State = 1;
    Param.ModLimit[4].SpectFlat.Range1 = 5.4;
    Param.ModLimit[4].SpectFlat.Range2 = 9.4;
    Param.ModLimit[4].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[4].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[4].SpectFlat.EdgeFreq = 3;

    Param.ModLimit[5].EvmRms.State = 1;
    Param.ModLimit[5].EvmRms.Limit = 3.5;
    Param.ModLimit[5].EvmPeak.Limit = 7;
    Param.ModLimit[5].MErrRms.Limit = 3.5;
    Param.ModLimit[5].MErrPeak.Limit = 7;
    Param.ModLimit[5].PhErrRms.Limit = 3.5;
    Param.ModLimit[5].PhErrPeak.Limit = 7;
    Param.ModLimit[5].FreqErr.State = 1;
    Param.ModLimit[5].FreqErr.Limit = 0.1;
    Param.ModLimit[5].IQOffset.State = 1;
    Param.ModLimit[5].IQOffset.PwrLimit[0] = -27.2;
    Param.ModLimit[5].IQOffset.PwrLimit[1] = -24.2;
    Param.ModLimit[5].IQOffset.PwrLimit[2] = -19.2;
    Param.ModLimit[5].IQOffset.PwrLimit[3] = -9.2;
    Param.ModLimit[5].IBE.State = 1;
    Param.ModLimit[5].IBE.GenMin = -29.2;
    Param.ModLimit[5].IBE.GenEVM = 3.5;
    Param.ModLimit[5].IBE.GenPwr = -57;
    Param.ModLimit[5].IBE.IQImage[0] = -27.2;
    Param.ModLimit[5].IBE.IQImage[1] = -24.2;
    Param.ModLimit[5].IBE.IQOffsetPwr[0] = -27.2;
    Param.ModLimit[5].IBE.IQOffsetPwr[1] = -24.2;
    Param.ModLimit[5].IBE.IQOffsetPwr[2] = -19.2;
    Param.ModLimit[5].IBE.IQOffsetPwr[3] = -9.2;
    Param.ModLimit[5].SpectFlat.State = 1;
    Param.ModLimit[5].SpectFlat.Range1 = 5.4;
    Param.ModLimit[5].SpectFlat.Range2 = 9.4;
    Param.ModLimit[5].SpectFlat.Max1Min2 = 6.4;
    Param.ModLimit[5].SpectFlat.Max2Min1 = 8.4;
    Param.ModLimit[5].SpectFlat.EdgeFreq = 3;

    // SpectLimit
    Param.SpectLimit[0].OBWLimit.Limit = 5;
    Param.SpectLimit[1].OBWLimit.Limit = 10;
    Param.SpectLimit[2].OBWLimit.Limit = 15;
    Param.SpectLimit[3].OBWLimit.Limit = 20;
    Param.SpectLimit[4].OBWLimit.Limit = 25;
    Param.SpectLimit[5].OBWLimit.Limit = 30;
    Param.SpectLimit[6].OBWLimit.Limit = 40;
    Param.SpectLimit[7].OBWLimit.Limit = 50;
    Param.SpectLimit[8].OBWLimit.Limit = 60;
    Param.SpectLimit[9].OBWLimit.Limit = 70;
    Param.SpectLimit[10].OBWLimit.Limit = 80;
    Param.SpectLimit[11].OBWLimit.Limit = 90;
    Param.SpectLimit[12].OBWLimit.Limit = 100;

    for (int i = 0; i < ARRAYSIZE(Param.SpectLimit); i++)
    {
        Param.SpectLimit[i].UtraLimit[0].RelState = 1;
        Param.SpectLimit[i].UtraLimit[0].RelLimit = 33;
        Param.SpectLimit[i].UtraLimit[0].AbsState = 1;
        Param.SpectLimit[i].UtraLimit[0].AbsPwr = -50;
        Param.SpectLimit[i].UtraLimit[1].RelLimit = 36;
        Param.SpectLimit[i].UtraLimit[1].AbsPwr = -50;
        Param.SpectLimit[i].NRLimit.RelState = 1;
        Param.SpectLimit[i].NRLimit.RelLimit = 30;
        Param.SpectLimit[i].NRLimit.AbsState = 1;
        Param.SpectLimit[i].NRLimit.AbsPwr = -50;
    }
    
    Param.SEMAddTestTol[0] = 1.5;
    Param.SEMAddTestTol[1] = 1.8;
    Param.SEMAddTestTol[2] = 1.8;
    
    Param.ACLRAddTestTol[0] = 0.8;
    Param.ACLRAddTestTol[1] = 1;

    Param.PowerLimit.State = 1;
    Param.PowerLimit.OffPower = -50;
    Param.PowerLimit.TestTol[0] = 1.5;
    Param.PowerLimit.TestTol[1] = 1.8;
    Param.PowerLimit.TestTol[2] = 1.7;
    Param.PowerLimit.TestTol[3] = 1.8;  
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTGeneralParam(AlzParam3GPP &Param, bool reset_base)
{
    if (reset_base)
    {
        memset(&Param, 0, sizeof(AlzParam3GPP));
        Param.DcFreqCompensate = 1;
        Param.SpectrumRBW = 30000;
        Param.MeasPowerGraph = 0;
        Param.MeasSpectrum = 0;
        Param.MeasCCDF = 0;
    }
    else
    {
        memset(&Param.NBIOT, 0, sizeof(Param.NBIOT));
    }

    Param.Standard = ALG_3GPP_STD_NB_IOT;
    Param.NBIOT.LinkDirect = ALG_3GPP_UL;
    Param.NBIOT.Measure.StatisticAvgFlg = 0;
    Param.NBIOT.Measure.StatisticCnt = 0;
    Reset_3GPPAlzNBIOTUpLinkParam(Param.NBIOT.UL);
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTLinkParam(Alg_3GPP_AlzInNBIOT &Param, int LinkDirect)
{
    if (Param.LinkDirect != LinkDirect)
    {
        memset(&Param, 0, sizeof(Alg_3GPP_AlzInNBIOT));
        Param.LinkDirect = LinkDirect;
        switch (LinkDirect)
        {
        case ALG_3GPP_UL:
            Reset_3GPPAlzNBIOTUpLinkParam(Param.UL);
            break;
        case ALG_3GPP_DL:
            Reset_3GPPAlzNBIOTDownLinkParam(Param.DL);
            break;
        default:
            break;
        }
    }
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTUpLinkParam(Alg_3GPP_AlzULInNBIOT &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzULInNBIOT));
    Param.ChannelBW = 200000;
    Param.ChanType = ALG_NBIOT_NPUSCH;
    Reset_3GPPAlzNBIOTNpuschParam(Param.Npusch);
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTDownLinkParam(Alg_3GPP_AlzDLInNBIOT &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzDLInNBIOT));
    Param.ChannelBW = 200000;
    Param.LTEAntennaNum = 1;
    Param.NBAntennaNum = 1;

    Param.ChanType = ALG_NBIOT_NPDSCH;
    WTLog::Instance().WriteLog(LOG_DEBUG, "Param.ChannelBW=%d\n", Param.ChannelBW);
    Reset_3GPPAlzNBIOTNpschParam(Param.Npdsch);
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTChanParam(Alg_3GPP_AlzInNBIOT &Param, int ChanType)
{
    switch (Param.LinkDirect)
    {
    case ALG_3GPP_DL:
        if (Param.DL.ChanType != ChanType)
        {
            Param.DL.ChanType = ChanType;
            switch (ChanType)
            {
            case ALG_NBIOT_NPDSCH:
            default:
                Reset_3GPPAlzNBIOTNpschParam(Param.DL.Npdsch);
                break;
            }
        }
        break;

    case ALG_3GPP_UL:
    default:
        if (Param.UL.ChanType != ChanType)
        {
            Param.UL.ChanType = ChanType;
            switch (ChanType)
            {
            case ALG_NBIOT_NPUSCH:
                Reset_3GPPAlzNBIOTNpuschParam(Param.UL.Npusch);
                break;
            case ALG_NBIOT_NPRACH:
                Reset_3GPPAlzNBIOTNprachParam(Param.UL.Nprach);
                break;
            default:
                break;
            }
        }
        break;
    }
    Reset_3GPPAlzNBIOTLimitInfoParam(Param.LimitInfo);
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTLimitInfoParam(Alg_3GPP_LimitInNBIOT &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_LimitInNBIOT));
    Param.EvmRms.State = 1;
    Param.EvmRms.Limit = 17.5;
    Param.EvmPeak.Limit = 35;
    Param.MErrRms.Limit = 17.5;
    Param.MErrPeak.Limit = 35;
    Param.PhErrRms.Limit = 17.5;
    Param.PhErrPeak.Limit = 35;
    Param.FreqErrLow.State = 1;
    Param.FreqErrLow.Limit = 0.2;
    Param.FreqErrHigh.State = 1;
    Param.FreqErrHigh.Limit = 0.2;
    Param.IQOffset.State = 1;
    Param.IQOffset.PwrLimit[0] = -24.2;
    Param.IQOffset.PwrLimit[1] = -19.2;
    Param.IQOffset.PwrLimit[2] = -9.2;
    Param.IBE.State = 1;
    Param.IBE.GenMin = -30;
    Param.IBE.GenPwr = -57;
    Param.IBE.IQImage[0] = -24.2;
    Param.IBE.IQOffsetPwr[0] = -24.2;
    Param.IBE.IQOffsetPwr[1] = -19.2;
    Param.IBE.IQOffsetPwr[2] = -9.2;

    Param.OBWLimit.State = 1;
    Param.OBWLimit.Limit = 200000;
    for (int i = 0; i < ALG_3GPP_SEM_LIM_SET; i++)
    {
        Param.SEMLimit[i].State = 1;
        Param.SEMLimit[i].StartFreq = 0.015;
        Param.SEMLimit[i].StopFreq = 0.085;
        Param.SEMLimit[i].StartPower = 22.9;
        Param.SEMLimit[i].StopPower = 1.1;
    }
    Param.GSM.RelState = 1;
    Param.GSM.RelLimit = 19.2;
    Param.GSM.AbsState = 1;
    Param.GSM.AbsPwr = -50;

    Param.UTRA.RelState = 1;
    Param.UTRA.RelLimit = 36.2;
    Param.UTRA.AbsState = 1;
    Param.UTRA.AbsPwr = -50;
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTNpuschParam(Alg_3GPP_AlzNpuschNBIOT &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzNpuschNBIOT));
    Param.Format = 1;
    Param.SCSpacing = 15000;
    Param.Repetitions = 1;
    Param.RUs = 1;
    Param.SubcarrierNum = 1;
    Param.Modulate = 1;
    Param.Scrambling = 1;
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTNprachParam(Alg_3GPP_AlzNprachNBIOT &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzNprachNBIOT));
}

void SCPI_AlzParam::Reset_3GPPAlzNBIOTNpschParam(Alg_3GPP_AlzNpdschNBIOT &Param)
{
    memset(&Param, 0, sizeof(Alg_3GPP_AlzNpdschNBIOT));
    Param.NSF = 1;
    Param.Repetitions = 1;
    Param.StartSubfrm = 6;
    Param.ChanCodingState = 1;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamWiSun *param)
{
    memset(param, 0, sizeof(AlzParamWiSun));
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamZwave *param)
{
    memset(param, 0, sizeof(AlzParamZwave));
}

SPCIUserParam::SPCIUserParam()
{
    ConnID = -1;
    IsMonObj = false;
    m_List = nullptr;
    Reset();
    DevConf::Instance().GetItemVal("SaveRawDataFlag", m_save_avg_wave.enable);
    DevConf::Instance().GetItemVal("TemporaryFilesNum", m_save_avg_wave.file_count);
    DevConf::Instance().GetItemVal("FileType", m_save_avg_wave.file_type);
}

SPCIUserParam::~SPCIUserParam()
{
    if (!IsMonObj)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WT_DisConnect ConnID = %d\n", this->ConnID);
        WT_DisConnect(this->ConnID);
        remove_all_vsg_low(this);
    }

    string str;
    for (int i = 0; i < m_MutiPNFilesName.size(); ++i)
    {
        str = string("rm -rf ") + "/tmp/tmpwave/" + m_MutiPNFilesName[i];
        do_system_cmd(str.c_str());
    }
    for (int i = 0; i < m_MutiPNDstFileName.size(); ++i)
    {
        str = string("rm -rf ") + m_MutiPNDstFileName[i];
        do_system_cmd(str.c_str());
    }
}

int SPCIUserParam::Connect(int Type)
{
    // 调用内置api连接仪器
    int Ret = WT_ERR_CODE_OK;
    int mode = TESTER_RUN_NOMAL;
    int unitCount = 1;
    double maxSampleRate_Digit_IQ = 640 * MHz_API;
    double maxSampleRate_RF = MAX_SMAPLE_RATE_API;
    ConnectedUnit ConnUnit;
    string MasterIp("127.0.0.1");

    std::string testjson(WTConf::GetDir() + std::string("/") + TESTER_CONFIG_FILE);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Start Connect server\n");
    WTConf wtconf(WTConf::GetDir() + "/scpi.conf");
    if (WT_OK == wtconf.GetItemVal("TesterIP", MasterIp))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's ip from scpi.conf " << MasterIp << endl;
    }
    else
    {
        MasterIp = "127.0.0.1";
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's ip from scpi.conf " << MasterIp << endl;

    if (WT_OK == GetJsonItemData(testjson, "RunMode", mode))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's run mode from teser.json = " << mode << endl;
    }

    memset(&ConnUnit, 0, sizeof(ConnUnit));
    memcpy(ConnUnit.Ip, MasterIp.c_str(), MasterIp.length());
    ConnUnit.SubTesterIndex = WT_SUB_TESTER_INDEX_AUTO;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Conecting %s,SubTesterIndex %d, ClientFd:%d...\n", ConnUnit.Ip, ConnUnit.SubTesterIndex, ClientFd);

    switch (Type)
    {
    case LINK_TYPE_FORCE:
        Ret = WT_ForceConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_NORMAL:
        Ret = WT_Connect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_SUB:
        Ret = WT_SubConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_MANAGER:
        Ret = WT_ManageConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_MONITOR:
        Ret = WT_MoniConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_DIAGNOSIS:
        Ret = WT_DialogConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    default:
        Ret = WT_ERR_CODE_CONNECT_FAIL;
        break;
    }

    if (WT_OK == Ret)
    {
        this->TesterLinkType = Type;
        this->TesterLinkMode = TESTER_AS_NOMAL_LINK;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Connect Tester "
             << " OK, ID=" << this->ConnID << endl;

        if (Type == LINK_TYPE_FORCE || Type == LINK_TYPE_NORMAL || Type == LINK_TYPE_SUB)
        {
            vsgPattern.resize(1);
            WT_GetDefaultParameter(
                this->ConnID,
                &vsaParam,
                &avgParam,
                &vsgParam,
                &waveParam,
                &vsgPattern[0]);
            NeedSetVSGPattern = true;
            switch (mode)
            {
            case TESTER_RUN_DIGIT_IQ:
                this->TesterMajorMode = mode;
                WT_SetDigtalIQMode(this->ConnID, TESTER_RUN_DIGIT_IQ);
                WT_SetMaxSampleRate(this->ConnID, maxSampleRate_Digit_IQ);
                break;
            default:
                this->TesterMajorMode = TESTER_RUN_NOMAL;
                WT_SetDigtalIQMode(this->ConnID, TESTER_RUN_NOMAL);
                WT_SetMaxSampleRate(this->ConnID, maxSampleRate_RF);
                break;
            }

            if (this->CheckBusinessLic(WT_WAVE_DECRYPT_API) != true)
            {
                WT_SetWaveEncrypted(1);
            }
            else
            {
                WT_SetWaveEncrypted(0);
            }
        }
        TesterInfo Info;
        WT_GetTesterInfo(this->ConnID, &Info);
        WT_SetWaveSNAndFW(Info.SN, Info.FwVersion);
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Connect Tester "
             << " Fail,Ret =" << Ret << endl;
    }

    return Ret;
}

int SPCIUserParam::CheckConnectStatus(bool TestConnect)
{
#define CHECK_CONNECT_INTERVAL 1 // 最多每秒测试一次连接状态
    int Ret = WT_ERR_CODE_OK;
    auto CheckAndSetTime = [&]() -> bool
    {
        struct timeval NowTime;
        gettimeofday(&NowTime, NULL);
        if ((NowTime.tv_sec - m_ConnetTestTime.tv_sec > CHECK_CONNECT_INTERVAL + 1) ||
            (NowTime.tv_sec - m_ConnetTestTime.tv_sec == CHECK_CONNECT_INTERVAL && NowTime.tv_usec > m_ConnetTestTime.tv_usec))
        {
            m_ConnetTestTime.tv_sec = NowTime.tv_sec;
            m_ConnetTestTime.tv_usec = NowTime.tv_usec;
            return TestConnect ? true : false;
        }
        else
        {
            return false;
        }
    };

    switch (TesterLinkType)
    {
    case LINK_TYPE_NORMAL:
    case LINK_TYPE_FORCE:
    case LINK_TYPE_SUB:
        if (CheckAndSetTime())
        {
            Ret = WT_TestConnectStatus(this->ConnID);
        }

        if (WT_ERR_CODE_OK == Ret)
        {
            Ret = WT_CheckConnectStatus(this->ConnID);
        }
        break;
    case LINK_TYPE_QUERY:
        Ret = WT_ERR_CODE_OK;
        break;
    default:
        Ret = WT_ERR_CODE_OK;
        break;
    }
    return Ret;
}

void SPCIUserParam::InitMem()
{
    PnRU.reset(new (std::nothrow) OFDMA_RU);
    PnPSDU.reset(new (std::nothrow) UserPSDU);

    PnWifi.reset(new (std::nothrow) GenWaveWifiStruct_API);
    PnBt.reset(new (std::nothrow) GenWaveBtStructV2);
    PnCW.reset(new (std::nothrow) GenWaveCwStruct);
    PnGLE.reset(new (std::nothrow) GenWaveGleStruct());
    Pn3GPP.reset(new (std::nothrow) Alg_3GPP_WaveGenType);
    PnWiSun.reset(new (std::nothrow) GenWaveWisunStruct());

    memset(PnWifi.get(), 0, sizeof(GenWaveWifiStruct_API));
    memset(PnBt.get(), 0, sizeof(GenWaveBtStructV2));
    memset(PnCW.get(), 0, sizeof(GenWaveCwStruct));
    memset(Pn3GPP.get(), 0, sizeof(Alg_3GPP_WaveGenType));

    WaveUserDefineExtendParam.clear();
}

void SPCIUserParam::Reset()
{
    Reset_VsaVsgParam();
    ResetWaveGenParam();
    m_CalSetting.Reset();
    Reset_LiteResult();
}

void SPCIUserParam::Reset_VsaVsgParam()
{
    if (-1 != ConnID)
    {
        vsgPattern.resize(1);
        m_FrameDelay.resize(1);
        m_FrameDelay[0] = 0;
        WT_SetMaxSampleRate(this->ConnID, MAX_SMAPLE_RATE_API);
        WT_GetDefaultParameter(ConnID, &vsaParam, &avgParam, &vsgParam, &waveParam, &vsgPattern[0]);
        NeedSetVSGPattern = true;
    }

    if (true)
    {
        memset(&vsaExtParam, 0, sizeof(vsaExtParam));
        memset(&vsgExtParam, 0, sizeof(vsgExtParam));
        memset(&m_InterParam, 0, sizeof(m_InterParam));
        memset(&tbAnanlyzeParam, 0, sizeof(tbAnanlyzeParam));
        memset(&mPacAttribute, 0, sizeof(mPacAttribute));
        memset(&mPacParam, 0, sizeof(mPacParam));
        memset(&SubNetConfig, 0, sizeof(SubNetConfig));
        memset(&m_Devm, 0, sizeof(m_Devm));
        memset(&m_AzAlyParam, 0, sizeof(m_AzAlyParam));

        // 配置默认分析参数
        vsaAlzParam.Reset(true);
        vsgAlzParam.Reset();
        m_TestMode = TESTMODE_RD;

        AlzFrameID = 1;
        VsaSampleRateMode = RATE_DEFAULT_API; // 见枚举WT_SAMPLE_RATE_MODE
        VsgSampleRateMode = RATE_DEFAULT_API; // 见枚举WT_SAMPLE_RATE_MODE
        VsgDemode = WT_DEMOD_11AG;

        mSpectrumWideMode = false;
        mPacFreqList.clear();
        memset(m_CMIMORefFile, 0, sizeof(m_CMIMORefFile));

        memset(&m_DigtalIQParam, 0, sizeof(m_DigtalIQParam));
        m_DigtalIQParam.VsaActionMask = ~0;
        m_DigtalIQParam.VsgActionMask = ~0;
        m_DigtalIQParam.VsaMaxBitCnt = m_DigtalIQParam.VsgMaxBitCnt = 13;
        memset(m_DigtalIQParam.VSAChannelIdList, -1, sizeof(m_DigtalIQParam.VSAChannelIdList));
        memset(m_DigtalIQParam.VSGChannelIdList, -1, sizeof(m_DigtalIQParam.VSGChannelIdList));

        memset(&m_DigtalIQTestFixture, 0, sizeof(m_DigtalIQTestFixture));
        m_DigtalIQTestFixture.SendTimeout = 0.01;
        m_DigtalIQTestFixture.SendPeriod = 250;
        m_DigtalIQTestFixture.SendGap = 0;
        m_DigtalIQTestFixture.SendCnt = 1;

        m_VsaThreadStatus = THREAD_IDEL;
        // WT-4xx default 8080 two port
        vsaExtParam.WIFI8080DulPortMode = 1;
        vsgExtParam.WIFI8080DulPortMode = 1;

        vsaTrigParam.Edge = WT_TRIG_DEGE_POSITIVE_API;
        vsaTrigParam.GapTime = 6e-6;
    }
}

void SPCIUserParam::ResetWaveGenParam()
{
    InitMem();

    WaveGenDemod = WT_DEMOD_11AG;
    if (-1 != ConnID)
    {
        WT_GetDefaultWaveParameterWifi(ConnID, WT_DEMOD_11AG, 0, PnWifi.get());
        WT_GetDefaultWaveParameterBTV2(ConnID, PnBt.get());
        WT_GetDefaultWaveParameterCW(ConnID, PnCW.get());
        WT_GetDefaultWaveParameterGLE(ConnID, PnGLE.get());
        WT_GetDefaultWaveParameter3GPP(ConnID, ALG_3GPP_STD_4G, 0, Pn3GPP.get(), sizeof(Alg_3GPP_WaveGenType));
        WT_GetDefaultWaveParameterWiSun(ConnID, PnWiSun.get());
    }
}

bool SPCIUserParam::CheckBusinessLic(WT_PROT_E_API LicValue)
{
    int iRet = WT_ERR_CODE_OK;
    if (LicInfos == nullptr)
    {
        int maxLicenseCnt = 256;
        int realCnt = 0;

        unique_ptr<LicItemInfo_API[]> TmpInfo(new LicItemInfo_API[maxLicenseCnt]);
        memset(TmpInfo.get(), 0, sizeof(LicItemInfo_API) * maxLicenseCnt);

        iRet = WT_GetLicense(ConnID, (LicItemInfo_API *)TmpInfo.get(), maxLicenseCnt, &realCnt);
        if (iRet != WT_ERR_CODE_OK)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "WT_GetLicense ret= %x", iRet);
            return false;
        }

        LicInfos.reset(new char[sizeof(LicItemInfo_API) * realCnt]);
        memcpy((char *)LicInfos.get(), (char *)TmpInfo.get(), realCnt * sizeof(LicItemInfo_API));
        LicItemCnt = realCnt;
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI Get lic cnt = %d\n", LicItemCnt);
    if (LicItemCnt == 0)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "get no lic~");
        return false;
    }
    else
    {
        TesterInfo DevInfo;
        memset(&DevInfo, 0, sizeof(TesterInfo));
        iRet = WT_GetTesterInfo(ConnID, &DevInfo);
        // WTLog::Instance().WriteLog(LOG_DEBUG, "WT_GetTesterInfo ret = %d\n", iRet);
        if (iRet == WT_ERR_CODE_OK)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "DevInfo.TesterType = %d\n",DevInfo.TesterType);
            if (TEST_TYPE_ENUM_WT428 == DevInfo.TesterType)
            {
                if (LicValue == WT_MAC_INTER_AC_API ||
                    LicValue == WT_MAC_INTER_AX_API ||
                    LicValue == WT_MAC_INTER_BE_API)
                {
                    LicValue = WT_INTER_API;
                }
            }
        }

        LicItemInfo_API *Info = (LicItemInfo_API *)(LicInfos.get());
        bool Find = false;

        for (int i = 0; i < LicItemCnt; i++)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "i = %d, Info->LicType = %d,Info->LicValue=%d,LicValue=%d\n",i,Info->LicType,Info->LicValue , LicValue);
            if (Info->LicType == WT_PROT_TYPE_API && Info->LicValue == LicValue)
            {
                struct tm t_tm;
                time_t EndTimeTicks = 0;
                Find = true;

                // 比较当前时间
                time_t NowTimeTicks;
                time(&NowTimeTicks);

                t_tm.tm_sec = Info->EndTime.Sec;
                t_tm.tm_min = Info->EndTime.Min;
                t_tm.tm_hour = Info->EndTime.Hour;
                t_tm.tm_mday = Info->EndTime.Mday;
                t_tm.tm_mon = Info->EndTime.Mon - 1;
                t_tm.tm_year = Info->EndTime.Year - 1900;
                t_tm.tm_isdst = 0;
                EndTimeTicks = mktime(&t_tm);
                if (EndTimeTicks < NowTimeTicks)
                {
                    // WTLog::Instance().WriteLog(LOG_DEBUG, "license out of date.");
                    return false;
                }
                break;
            }
            Info++;
        }
        if (!Find)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
}

void SPCIUserParam::Reset_LiteResult(int start, int end)
{
    for (int i = start; i < end && i < LITE_ENUM::LITE_ENUM_MAX; i++)
    {
        for (int j = 0; j < MAX_DF_NUM; j++)
        {
            for (int m = 0; m < MAX_SEGMENT; m++)
            {
                m_litePoint[i][j][m].valid = false;
                m_litePoint[i][j][m].value.reset(nullptr);
                m_litePoint[i][j][m].value_len = 0;
            }
        }
    }
}

int SPCIUserParam::GetMoniPort(int MoniVsg)
{
    if (TesterLinkType == LINK_TYPE_NORMAL ||
        TesterLinkType == LINK_TYPE_SUB ||
        TesterLinkType == LINK_TYPE_FORCE)
    {
        return MoniVsg
                   ? (vsgParam.Freq2 > 0 ? vsgExtParam.VsgRfPort[0] : vsgParam.RfPort[0])
                   : (vsaParam.Freq2 > 0 ? vsaExtParam.VsaRfPort[0] : vsaParam.RfPort[0]);
    }
    return WT_PORT_OFF;
}

bool ScpiTimer::NeedRecord(std::string TimerName)
{
    if (m_Enable)
    {
        std::vector<std::string> ignor_cmd = {
            ":SYSTem:ERRor?",
            ":SENSe:CAPTure:STATe?",
            ":SOURce:CURRent:STATe?",
            ":UP:GRADe:FW:STATus?",
            ":SYSTem:CONNinfo?",
            "*CLS",
            "*OPC",
            ":TBTF:STATus?",
            ":TFTB:STATus?",
            "WT:SYSTem:CONFigure:SCPI:TIME:USED?",
        };
        for (int i = 0; i < ignor_cmd.size(); i++)
        {
            if (TimerName.find(ignor_cmd[i]) != string::npos)
            {
                return false;
            }
        }
        return true;
    }
    else
    {
        return false;
    }
}
void ScpiTimer::ClearTimer(std::string TimerName)
{
    if (NeedRecord(TimerName))
    {
        m_Timer.clear();
        m_text.clear();
    }
}

void ScpiTimer::StartTimer(std::string TimerName)
{
    if (NeedRecord(TimerName))
    {
        while (TimerName.back() == '\n' || TimerName.back() == '\t')
        {
            TimerName.pop_back();
        }
        struct timeval tpstart;
        gettimeofday(&tpstart, NULL);
        m_Timer[TimerName][0] = tpstart;
    }
}

void ScpiTimer::StopTimer(std::string TimerName)
{
    if (NeedRecord(TimerName))
    {
        while (TimerName.back() == '\n' || TimerName.back() == '\t')
        {
            TimerName.pop_back();
        }

        if (m_Timer.count(TimerName))
        {
            struct timeval tpend;
            gettimeofday(&tpend, NULL);
            m_Timer[TimerName][1] = tpend;
            std::string str;
            str += TimerName + ": ";
            str += std::to_string((int)((m_Timer[TimerName][1].tv_sec - m_Timer[TimerName][0].tv_sec) * 1e6 + (m_Timer[TimerName][1].tv_usec - m_Timer[TimerName][0].tv_usec)));
            str += "us,";
            m_text = str + m_text;
        }
    }
}

string ScpiTimer::GetCommandUsedTime()
{
    if (',' == m_text.back())
    {
        m_text.pop_back();
        m_text += '.';
    }
    return m_text;
}