#!/bin/bash

cmake_dir=../
currentDir=$(dirname $(readlink -f $0))

build_dir="${currentDir}/build"
matLibDir="Dependency DLLs/mat_linux_lib"

rm -rf ${build_dir}

#新建目标文件目录
if [ ! -d "${build_dir}/lib" ]; then
    mkdir -p ${build_dir}/lib	
fi

#编译链接时依赖matlib库，提前拷贝
chmod +x $currentDir/matlab.sh
bash $currentDir/matlab.sh "${currentDir}/${matLibDir}" "${build_dir}/lib"
if [ $? -ne 0 ]; then
    exit 1
fi
cd ${build_dir}

BuildFlag="-DCMAKE_BUILD_TYPE=Release"
if [[ "$@" == *DEBUG=1* ]]; then
	BuildFlag="-DCMAKE_BUILD_TYPE=Debug"
fi

if [[ "$@" == *SISO=1* ]]; then
	BuildFlag=${BuildFlag}" -DStreamType=SISO"
fi

if [[ "$@" == *DEVTYPE=WT448* ]]; then
    BuildFlag=${BuildFlag}" -DTesterType=WT448"
    echo "Build WT448 version"
elif [[ "$@" == *DEVTYPE=WT428C* ]]; then
    BuildFlag=${BuildFlag}" -DTesterType=WT428C"
    echo "Build WT428C version"
elif [[ "$@" == *DEVTYPE=WT428H* ]]; then
    BuildFlag=${BuildFlag}" -DTesterType=WT428H"
    echo "Build WT428H version"
elif [[ "$@" == *DEVTYPE=WT428* ]]; then
	BuildFlag=${BuildFlag}" -DTesterType=WT428"
    echo "Build WT428 version"
elif [[ "$@" == *DEVTYPE=WT328CE* ]]; then
    BuildFlag=${BuildFlag}" -DTesterType=WT328CE"
    echo "Build WT328CE version"
else
    echo "error dev type! exit..."
    exit -1
fi

if [[ "$@" == *"mod=Common"* ]]; then
    BuildFlag=${BuildFlag}" -DModule=Common"
    echo "Build with Common module"
elif [[ "$@" == *"mod=IOControl"* ]]; then
    BuildFlag=${BuildFlag}" -DModule=IOControl"
    echo "Build with IOControl module"
elif [[ "$@" == *"mod=WT4XXWrapper"* ]]; then
    BuildFlag=${BuildFlag}" -DModule=WT4XXWrapper"
    echo "Build with WT4XXWrapper module"
elif [[ "$@" == *"mod=API"* ]]; then
    BuildFlag=${BuildFlag}" -DModule=API"
    echo "Build with API module"
elif [[ "$@" == *"mod=Encryption"* ]]; then
    BuildFlag=${BuildFlag}" -DModule=Encryption"
    echo "Build with Encryption module"
elif [[ "$@" == *"mod=PNFileProcess"* ]]; then
    BuildFlag=${BuildFlag}" -DModule=PNFileProcess"
    echo "Build with PNFileProcess module"
else
    BuildFlag=${BuildFlag}" -DModule="
    echo "Build All"
fi


cmake ${BuildFlag} ${cmake_dir}

if [ $? -eq 0 ]; then
    make
    if [ $? -ne 0 ]; then
        echo "****************************************"
        echo "make error happend exit now !!!"
        echo "****************************************"
        exit 1;
    fi
else
    echo "cmake build error!!!"
    exit -1
fi

echo "Build ${build_dir} OK !!!"
