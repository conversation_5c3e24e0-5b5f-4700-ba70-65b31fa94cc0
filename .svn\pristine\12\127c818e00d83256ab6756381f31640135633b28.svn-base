#ifndef __DOCKER_UTILS_H__
#define __DOCKER_UTILS_H__

#include <jsoncpp/json/json.h>
#include <vector>

using namespace std;

enum DOCKER_RUN_STATUS
{
    STATUS_EXITED = 0,  // 已经退出
    STATUS_UP,          // 正在运行
    STATUS_OTHER,       // 其他未知状态
    STATUS_NOT_EXIST,   // 应用不存在
};

struct docker_app_info
{
    char name[16];  // 应用的名称, 小写, 不能有空格等特色符号
    int status_cfg; // 配置状态(1启动,0不启动)
    int status_run; // 实际的运行状态，见DOCKER_RUN_STATUS
    char resv[40];  // 保留备用
};

class DockerMgr
{
public:
    // 单例
    static DockerMgr &Instance();
    // 查询已经安装的应用列表
    // int QueryAppListEntry(vector<docker_app_info> &arr);
    int QueryAppListEntry(int resv, vector<docker_app_info> &arr);
    // 设置应用启用或禁用
    int SetAppStatusEntry(void *Data);
    // 删除一个应用
    int RemoveAppEntry(void *Data);

private:
    DockerMgr();

    int ResetConfig();
    int LoadConfig();
    int SaveConfig();
    int CheckDockerEnvironment();
    int SetAppStatus(const char *name, int status);
    int RemoveApp(const char *name);
    Json::Value m_JsonRoot;
};
#endif