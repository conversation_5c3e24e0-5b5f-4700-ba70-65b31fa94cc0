#ifndef ALG_3GPP_VSGDEF_NBIOT_H_
#define ALG_3GPP_VSGDEF_NBIOT_H_

#include "alg_3gpp_apidef.h"

/* NBIOT Channel Type */
#define ALG_NBIOT_NPUSCH                0
#define ALG_NBIOT_NPRACH                1
#define ALG_NBIOT_NPDSCH                2
#define ALG_NBIOT_NPBCH                 3
#define ALG_NBIOT_NBSIB1                4
#define ALG_NBIOT_NPDCCH                5
#define ALG_NBIOT_NPSS                  6
#define ALG_NBIOT_NSSS                  7

/* Operating Mode */
#define ALG_NBIOT_STANDALONE            0
#define ALG_NBIOT_GUARDBAND             1
#define ALG_NBIOT_INBAND                2

/* Carrier Type */
#define ALG_NBIOT_ANCHOR                0
#define ALG_NBIOT_NONANCHOR             1

/**************************************************************************************************/
/*                                   UL Configurate Start                                         */
/**************************************************************************************************/
typedef struct {
    int OperationMode; /* 1:In-band; 2:Guard band; 3:Standalone */
    int ChannelBW;
    int RBIdx;

    int NBCellID;

    /* # NDMRS */
    int GrpHopping;
    int TTCShift;
    int STCShift;
    int BaseSeqMode;
    int TTBaseSeq;
    int STBaseSeq;
    int TWBaseSeq;
    int DeltaSeqShift;

    int Reserved[64];
} Alg_NBIOT_ULCellType;

typedef struct {
    int UeID;

    /* NPUSCH parameter */
    int Scrambling;
    int DataType;
    int Initialization;
    int ChanCodingState;

    int Reserved[65];
} Alg_NBIOT_ULUeType;

typedef struct {
    /* # NPUSCH */
    int Format;
    int SCSpacing; /* 15000(15kHz), 3750(3.75kHz) */
    int StartSubfrm;
    int Repetitions;
    int RUs;

    /* Format 1 */
    int UseIsc;
    int Isc; /* Subcarrier indication field */
    int SubcarrierNum;
    int StartSubcarrier;
    int Modulate;
    int UseMcs; /* 0: I_MCS; 1: I_TBS */
    int Mcs;
    int TBSIdx;
    int StartRVIdx; /* Start slot rv_idx: 0,2*/

    /* Format 2 */
    int UseACKResField;
    int ACKResField;
    int SubcarrierIdx;
    int HarqAckInfo; /* 1, 0*/

    int Reserved[64];
} Alg_NBIOT_NpuschType;

typedef struct {
    int Reserved[64];
} Alg_NBIOT_NprachType;

typedef struct {
    int ChanType;
    int Reserved[3];
    Alg_NBIOT_NpuschType Npusch;
    Alg_NBIOT_NprachType Nprach;
} Alg_NBIOT_ULScheduleType;

typedef struct {
    Alg_NBIOT_ULCellType Cell;

    Alg_NBIOT_ULUeType Ue;

    Alg_NBIOT_ULScheduleType Schedule;
} Alg_NBIOT_ULWaveGenType;

/**************************************************************************************************/
/*                                   UL Configurate End                                           */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                   DL Configurate Start                                         */
/**************************************************************************************************/
typedef struct {
    int Rmax;
    int AlignReserved; /* Reserved for memory align */
    double G;
    double Offset;
} Alg_NBIOT_SearchSpaceType;

typedef struct {
    int PhyCellID;
    int RARNTI;
    int LteAntennaNum;

    int LteREsFillState;
    int Modulation;
    int DataType;
    int Initialization;

    int Reserved[65];
} Alg_NBIOT_DLLteType;

typedef struct {
    int OperationMode; /* 0:Standalone; 1:Guard band; 2:In-band */
    int RBIdx;
    
    int CSSType1Rmax;
    int AlignReserved; /* Reserved for memory align */
    Alg_NBIOT_SearchSpaceType CSSType2;

    int Reserved[16];
} Alg_NBIOT_DLAhrCarrierType;

typedef struct {
    int Index;
    int State;
    int OperationMode; /* 0:Standalone; 1:Guard band; 2:In-band */
    int RBIdx;

    int Reserved[16];
} Alg_NBIOT_DLNonAhrCarrierType;

typedef struct {
    int ChannelBW;
    int NBCellID;
    int NBAntennaNum; /* 1 or 2 */
    int Reserved[15];

    Alg_NBIOT_DLLteType Lte;

    Alg_NBIOT_DLAhrCarrierType Anchor;
    Alg_NBIOT_DLNonAhrCarrierType NonAnchor[3];
} Alg_NBIOT_DLCellType;

typedef struct {
    int UeID;
    int UeCategory;

    Alg_NBIOT_SearchSpaceType USS;

    int DataType;
    int Initialization;

    int Reserved[16];
} Alg_NBIOT_DLUeType;

typedef struct {
    int Isc; /* Subcarrier Indication Field */
    int Iru; /* Resource assignment  */
    int Idelay; /* Scheduling delay  */
    int Imcs; /* Modulation and coding scheme */
    int RedunVer; /* Redundancy version */
    int Irep; /* Repetition number */
    int NewDataInd; /* New data indicator */
    int DciRepNum; /* DCI subframe repetition number */
    int Reserved[8];
} Alg_NBIOT_DciN0Type;

typedef struct {
    int OrderInd; /* NPDCCH order indicator */
    int NprachRep; /* Starting number of NPRACH repetitions  */
    int NprachSC; /* Subcarrier indication of NPRACH */

    int Idelay; /* Scheduling delay */
    int Isf; /* Resource assignment */
    int Imcs; /* Modulation and coding scheme */
    int Irep; /* Repetition number */
    int NewDataInd; /* New data indicator */
    int HarqAckRes; /* HARQ-ACK resource */
    int DciRepNum; /* DCI subframe repetition number */

    int DistanceType; /* Standard, Minimum, Zero */
    int Reserved[9];
} Alg_NBIOT_DciN1Type;

typedef struct {
    int PagFlg; /* Flag for paging/direct indication differentiation  */
    
    /* Direct Indication Information - 8bits */
    int SysInfoModifEDRX; /* systemInfoModification-eDRX by TS36.331 */
    int SysInfoModif; /* systemInfoModification by TS36.331 */

    int Isf;
    int Imcs;
    int Irep;
    int DciRepNum;
    int Reserved[9];
} Alg_NBIOT_DciN2Type;

typedef struct {
    int User;
    int DciFormat; /* 0: N0; 1: N1; 2: N2 */
    int SearchSpace;
    int AlignReserved; /* Reserved for memory align */

    union {
        Alg_NBIOT_DciN0Type N0;
        Alg_NBIOT_DciN1Type N1;
        Alg_NBIOT_DciN2Type N2;
    };

    int StartSubfrm;
    int NpdcchFormat; /* 0: Format0(NCCE0); 1: Format1(NCCE0/1)*/
    int NcceIdx; /* 0 or 1 */
    int Reserved[17];
} Alg_NBIOT_DciType;

typedef struct {
    int SFN;
    int HyperSFN;
    int SchedInfoSIB1;
    int SysInfoValueTag;
    char ABEnabled;
    char OptModeInfo[7];
    char SpareBit[11];
    char ReservedAlign[5];
} Alg_NBIOT_MIBType;

typedef struct {
    int Precoding;
    int Scrambling;
    int ChanCodingState;
    int UseMIB; /* 0: OFF; 1: ON */
    Alg_NBIOT_MIBType MIBInfo;
    int Reserved[32];
} Alg_NBIOT_NpbchType;

typedef struct {
    int Precoding;
    int Scrambling;
    int ChanCodingState;
    int TBSIdx;
    int Reserved[8];
} Alg_NBIOT_SIB1Type;

typedef struct {
    int StartSymb;
    int Scrambling;
    int Precoding;

    int Reserved[9];
} Alg_NBIOT_NpdcchType;

typedef struct {
    int StartSymb;
    int Scrambling;
    int Precoding;
    int ChanCodingState;
    int Reserved[8];
} Alg_NBIOT_NpdschType;

typedef struct {
    Alg_NBIOT_DciType Dci;

    Alg_NBIOT_NpbchType Npbch;
    Alg_NBIOT_SIB1Type SIB1;
    Alg_NBIOT_NpdcchType Npdcch;
    Alg_NBIOT_NpdschType Npdsch;

    int Reserved[32];
} Alg_NBIOT_AhrScheduleType;

typedef struct {
    int ConfigType; /* Standard; Dummy */
    int AlignReserved; /* Reserved for memory align */

    Alg_NBIOT_DciType Dci;

    Alg_NBIOT_NpdcchType Npdcch;
    Alg_NBIOT_NpdschType Npdsch;

    int Reserved[32];
} Alg_NBIOT_NonAhrScheduleType;

typedef struct {
    Alg_NBIOT_DLCellType Cell;

    Alg_NBIOT_DLUeType Ue;

    Alg_NBIOT_AhrScheduleType AhrSchedule;
    Alg_NBIOT_NonAhrScheduleType NonAhrSchedule[3];
} Alg_NBIOT_DLWaveGenType;

/**************************************************************************************************/
/*                                   DL Configurate End                                           */
/**************************************************************************************************/
typedef struct {
    /* Filter Type: ALG_3GPP_FILTER_NBIOT */
    int Type;
    int Fs; /* Fixed: 3.84MHz */

    /* # FIR filter parameter */
    int MaxOrder;
    int AlignReserved;
    double FpassFactor;
    double FstopFactor;
    double PassRipple;
    double StopAtten;

    /* # RC filter */
    double RollOffFactor;
    double CutOffFreqShift;

    /* # WOLA filter parameter */
    double WindowLenFactor;

    int Reserved[16];
} Alg_NBIOT_Filter;


/**************************************************************************************************/
/*                                General Configurate Start                                       */
/**************************************************************************************************/

typedef struct {
    /* # Filter */
    Alg_NBIOT_Filter Filter;
    int Reserved[64];
} Alg_NBIOT_GeneralInfo;

/**************************************************************************************************/
/*                                 General Configurate End                                        */
/**************************************************************************************************/

typedef struct {
    /* Link Direct: 0(ALG_3GPP_UL), 1(ALG_3GPP_DL)*/
    int LinkDirect;
    int Reserved[3];

    union {
        Alg_NBIOT_ULWaveGenType UL;
        Alg_NBIOT_DLWaveGenType DL;
    };

    /* Filter, Clipping, Marker, ... */
    Alg_NBIOT_GeneralInfo General;
} Alg_NBIOT_WaveGenType;

/******************** NB-IOT Start ********************/
typedef struct {
    int Format;
    int SCSpacing; /* 15000(15kHz), 3750(3.75kHz) */
    int Repetitions;
    int RUs;
    int SubcarrierNum;
    int StartSubcarrier;

    int CyclicShift;
    int GrpHopping;
    int DeltaSeqShift;

    int Modulate;

    int ChanCodingState;
    int Scrambling;
    int StartSubfrm;
    int TBSIdx;
    int StartRVIdx; /* Start slot rv_idx: 0,2*/
    int UeID;

    int Reversed[64];
} Alg_3GPP_AlzNpuschNBIOT;

typedef struct {
    int Reversed[64];
} Alg_3GPP_AlzNprachNBIOT;

typedef struct {
    int Duplexing;

    int OperationMode; /* 1:In-band; 2:Guard band; 3:Standalone */
    int ChannelBW;
    int RBIdx;

    int NBCellID;
    int Reversed[64];

    int ChanType; /* 0: NPUSCH; 1: NPRACH */
    Alg_3GPP_AlzNpuschNBIOT Npusch;
    Alg_3GPP_AlzNprachNBIOT Nprach;
} Alg_3GPP_AlzULInNBIOT;

typedef struct {
    int NSF; /* number of subframes */
    int Repetitions; /* repetition number */
    int MCS;

    int StartSymb;
    int StartSubfrm;

    int Precoding;
    int ChanCodingState;
    int Scrambling;
    int UeID;

    int AlignReserved; /* Reserved for memory align */

    double Power; /* Power factor */

    int Reversed[64];
} Alg_3GPP_AlzNpdschNBIOT;

typedef struct {
    int Duplexing; /* Duplexing: 0(ALG_3GPP_FDD), 1(ALG_3GPP_TDD) */

    int OperationMode; /* 0:Standalone; 1:In-band; 2:Guard band;  */
    int CarrierType;   /* 0:Anchor; 1:Non-Anchor */

    int ChannelBW;

    int RBIdx; /* Configured for In-band or Guard band */

    int NBCellID;

    int LTECellID; /* Configured for In-band or Guard band */

    int LTEAntennaNum; /* 1 or 2 or 4*/
    int NBAntennaNum; /* 1 or 2 */
    int SIB1Switch; /* 0:DISABLE; 1:ENABLE */
    int SchedulingInfoSIB1; /* 0-11 */
    int AlignReserved; /* Reserved for memory align */

    double NPssPower; /* NPSS Power factor */
    double NSssPower; /* NSSS Power factor */

    int Reversed[65];

    int ChanType; /* 2: NPDSCH */
    Alg_3GPP_AlzNpdschNBIOT Npdsch;
} Alg_3GPP_AlzDLInNBIOT;

typedef struct {
    int StatisticAvgFlg; /* 0: OFF; 1: ON */
    int StatisticCnt;

    int MeasureUnit; /* RU or Slot */

                     /* Constellation Grap */
    int ConstShowPilot; /* Enable or Disable */

    int Reserved[64];
} Alg_3GPP_AlzMeasureNBIOT;

typedef struct {
    int LinkDirect;
    int Reserved[3];

    union {
        Alg_3GPP_AlzULInNBIOT UL;
        Alg_3GPP_AlzDLInNBIOT DL;
    };

    Alg_3GPP_AlzMeasureNBIOT Measure;
} Alg_3GPP_AlzInNBIOT;
/********************* NB-IOT End *********************/

/******************** NB-IOT Start ********************/
typedef struct {
    int LinkDirect;
    int ChanType;
    int Format;
    int Modulate;
    int Scrambling;
    int ChannelCodingType;
    int Crc;
    int HarqAckInfo;
    int BitLen;
    char *BitSeq;
} Alg_3GPP_DecodeOutNBIOT;

typedef struct {
    int InfoFlg;

    double FreqErr; /* Hz */
    double IQOffset; /* dBc */
    double OBW; /* kHz */
    double TxPower; /* dBm */
    double PeakPower; /* dBm */
    double SCPower; /* dBm */
    double RSRP; /* dBm */
    double RSSI; /* dBm */
    double RSRQ; /* dB */
    double SNR; /* dB */

    double RmsEvmPCT; /* % */
    double PeakEvmPCT; /* % */
    double DmrsEvmPCT; /* % */
    double MagnErrRms; /* % */
    double MagnErrPeak; /* % */
    double MagnErrDmrs; /* % */
    double PhaseErrRms; /* deg */
    double PhaseErrPeak; /* deg */
    double PhaseErrDmrs; /* deg */
} Alg_3GPP_AlzSlotNBIOT;

typedef struct {
    int TxStreamNum;
    Alg_3GPP_OfdmOutInfo TxMeasure[2];

    Alg_3GPP_DecodeOutNBIOT DeInfo;

    /* Statistic Average Result */
    int StatisticCnt;
    Alg_3GPP_AlzSlotNBIOT *SlotOut;
} Alg_3GPP_AlzOutNBIOT;
/********************  NB-IOT End  ********************/

#endif /* ALG_3GPP_VSGDEF_NBIOT_H_ */