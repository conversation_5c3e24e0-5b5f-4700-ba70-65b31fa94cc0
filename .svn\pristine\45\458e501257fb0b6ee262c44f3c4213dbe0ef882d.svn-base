#include "scpi_3gpp_common.h"

int cellular::GetScpiCommandNumbers(scpi_t *context,
                                         std::vector<cellular::Command> &params)
{
    std::vector<int> command_numbers(params.size());
    if (!SCPI_CommandNumbers(context, command_numbers.data(), command_numbers.size()))
    {
        return WT_3GPP_GET_COMMAND_NUM_FAILED;
    }

    // 注: 此处max为实际可取的最大值+1
    for (int i = 0; i < command_numbers.size(); ++i)
    {
        int value = command_numbers[i];
        if (value < params[i].min_ || value >= params[i].max_)
        {
            return WT_3GPP_COMMAND_NUM_OUT_OF_RANGE;
        }
        params[i].value_ = value;
    }
    return WT_OK;
}

void cellular::GetVsaDefaultParamFrom3GPPCallback(const int& standard, const int& linkDirect, AlzParam3GPP &param)
{
    // 在栈上创建临时结构体
    Alg_3GPP_VsaInInfo tempVsaInfo;
    memset(&tempVsaInfo, 0, sizeof(tempVsaInfo));
    
    // 设置必要的前置参数
    tempVsaInfo.Standard = standard;
    switch (param.Standard) {
        case ALG_3GPP_STD_5G:
            tempVsaInfo.NR.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_4G:
            tempVsaInfo.LTE.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_NB_IOT:
            tempVsaInfo.NBIOT.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_WCDMA:
            tempVsaInfo.WCDMA.LinkDirect = linkDirect;
            break;
    }
    
    // 调用算法接口获取默认值
    Callback_3GPP_VsaParamInit(&tempVsaInfo);
    
    // 只复制需要的部分回到您的参数结构体
    // param.DcFreqCompensate = tempVsaInfo.DcFreqCompensate;
    // param.SpectrumRBW = tempVsaInfo.SpectrumRBW;
    // param.MeasPowerGraph = tempVsaInfo.MeasPowerGraph;
    // param.MeasSpectrum = tempVsaInfo.MeasSpectrum;
    // param.MeasCCDF = tempVsaInfo.MeasCCDF;
    
    // 复制协议相关的union部分
    switch (param.Standard) {
        case ALG_3GPP_STD_GSM:
            memcpy(&param.GSM, &tempVsaInfo.GSM, sizeof(param.GSM));
            break;
        case ALG_3GPP_STD_WCDMA:
            memcpy(&param.WCDMA, &tempVsaInfo.WCDMA, sizeof(param.WCDMA));
            break;
        case ALG_3GPP_STD_5G:
            memcpy(&param.NR, &tempVsaInfo.NR, sizeof(param.NR));
            break;
        case ALG_3GPP_STD_4G:
            memcpy(&param.LTE, &tempVsaInfo.LTE, sizeof(param.LTE));
            break;
        case ALG_3GPP_STD_NB_IOT:
            memcpy(&param.NBIOT, &tempVsaInfo.NBIOT, sizeof(param.NBIOT));
            break;
    }
}

void cellular::GetVsgDefaultParamFrom3GPPCallback(const int& standard, const int& linkDirect, Alg_3GPP_WaveGenType &param)
{
    // 在栈上创建临时结构体
    Alg_3GPP_WaveGenType tempVsgInfo;
    memset(&tempVsgInfo, 0, sizeof(tempVsgInfo));
    
    // 设置必要的前置参数
    // tempVsgInfo.Standard = standard;
    switch (standard) {
        case ALG_3GPP_STD_5G:
            tempVsgInfo.NR.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_4G:
            tempVsgInfo.LTE.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_NB_IOT:
            tempVsgInfo.NBIOT.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_WCDMA:
            tempVsgInfo.WCDMA.LinkDirect = linkDirect;
            break;
        case ALG_3GPP_STD_GSM:
            // tempVsgInfo.GSM.LinkDirect = linkDirect;
            break;
    }
    
    // 调用算法接口获取默认值
    Callback_3GPP_VsgParamInit(&tempVsgInfo);
    
    // 只复制需要的部分回到您的参数结构体
    // param.DcFreqCompensate = tempVsgInfo.DcFreqCompensate;
    // param.SpectrumRBW = tempVsgInfo.SpectrumRBW;
    // param.MeasPowerGraph = tempVsgInfo.MeasPowerGraph;
    // param.MeasSpectrum = tempVsgInfo.MeasSpectrum;
    // param.MeasCCDF = tempVsgInfo.MeasCCDF;
    
    // 复制协议相关的union部分
    switch (standard) {
        case ALG_3GPP_STD_GSM:
            memcpy(&param.GSM, &tempVsgInfo.GSM, sizeof(param.GSM));
            break;
        case ALG_3GPP_STD_WCDMA:
            memcpy(&param.WCDMA, &tempVsgInfo.WCDMA, sizeof(param.WCDMA));
            break;
        case ALG_3GPP_STD_5G:
            memcpy(&param.NR, &tempVsgInfo.NR, sizeof(param.NR));
            break;
        case ALG_3GPP_STD_4G:
            memcpy(&param.LTE, &tempVsgInfo.LTE, sizeof(param.LTE));
            break;
        case ALG_3GPP_STD_NB_IOT:
            memcpy(&param.NBIOT, &tempVsgInfo.NBIOT, sizeof(param.NBIOT));
            break;
    }
}

void cellular::SetCellularWaveGenCommonParam(int demod, PNSetBaseType &commonParam)
{
    memset(&commonParam, 0, sizeof(PNSetBaseType));
    commonParam.standard = demod; // 0x1000为5G,0x1001为4G
    commonParam.subType = 0;
    commonParam.bandwidth = 0; // 未明确
    commonParam.samplingRate = 30720000;
    commonParam.NSS = 1;       // 未明确
    commonParam.segment = 1;   // 未明确
    commonParam.FreqErr = 0;
    commonParam.IQImbalanceAmp = 0;
    commonParam.IQImbalancePhase = 0;
    commonParam.DCOffset_I = 0;
    commonParam.DCOffset_Q = 0;
    commonParam.ClockErr = 0;
    commonParam.Snr = 200;
    commonParam.Gap = 0;              // 未明确
    commonParam.FlatFactor = 0;
    commonParam.ReducePARA = 0;        // 未明确
    commonParam.PhaseNoiseFlg = 0;
    commonParam.PhaseNoiseFactor[0] = -84;
    commonParam.PhaseNoiseFactor[1] = -100;
    commonParam.PhaseNoiseFactor[2] = -96;
    commonParam.PhaseNoiseFactor[3] = -109;
    commonParam.PhaseNoiseFactor[4] = -122;
    commonParam.PhaseNoiseFactor[5] = -200;

    switch (demod)
    {
    case ALG_3GPP_STD_GSM:
        commonParam.samplingRate = 30 * MHz;
        break;
    case ALG_3GPP_STD_WCDMA:
    case ALG_3GPP_STD_4G:
        commonParam.samplingRate = 30.72 * MHz;
        break;
    case ALG_3GPP_STD_5G:
        commonParam.samplingRate = 122.88 * MHz;
        break;
    case ALG_3GPP_STD_NB_IOT:
        commonParam.samplingRate = 15.36 * MHz;
        break;
    default:
        break;
    }
}
