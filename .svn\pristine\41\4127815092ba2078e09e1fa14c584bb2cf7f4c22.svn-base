#include "connection_monitor.h"
#include <cerrno>
#include <sys/socket.h>
#include <unistd.h>

ConnectionMonitor::ConnectionMonitor(vxi::SessionManager& manager)
    : m_session_manager(manager)
    , m_running(false)
{
}

ConnectionMonitor::~ConnectionMonitor()
{
    Stop();
}

void ConnectionMonitor::Start()
{
    if (!m_running.exchange(true)) {
        m_monitor_thread = std::thread(&ConnectionMonitor::MonitorLoop, this);
    }
}

void ConnectionMonitor::Stop()
{
    if (m_running.exchange(false)) {
        if (m_monitor_thread.joinable()) {
            m_monitor_thread.join();
        }
    }
}

void ConnectionMonitor::MonitorLoop()
{
    while (m_running) {
        CheckConnections();
        std::this_thread::sleep_for(std::chrono::seconds(kCheckInterval));
    }
}

void ConnectionMonitor::CheckConnections()
{
    auto check_client = [this](const vxi::VxiClientInfo& client) {
        if (send(client.sockfd, "", 0, 0) < 0) {
            int err = errno;
            if (err != EINTR && err != EAGAIN) {
                printf("vxi link lost, linkid %d, socket: %d\n", client.link_id, client.sockfd);
                m_session_manager.RemoveSession(client.link_id);
            }
        }
    };

    m_session_manager.ForEachClient(check_client);
}
