//*****************************************************************************
//  File: scpi_config.h
//  SCPI端口定义
//  Data: 2019.10.21
//*****************************************************************************
#ifndef __SCPI_CONFIG_H__
#define __SCPI_CONFIG_H__

#include "conf.h"

#include "wtlog.h"

//业务处理类
class ScpiConfig
{
public:
    ScpiConfig()
    {
        WTConf wtconf(WTConf::GetDir() + "/scpi.conf");

        wtconf.GetItemVal("Port", NormalLinkPort);
        wtconf.GetItemVal("MaxUserNum", NormalUserNum);

        wtconf.GetItemVal("ManagerPort", ManagerLinkPort);
        wtconf.GetItemVal("MaxManagerUserNum", ManagerUserNum);

        wtconf.GetItemVal("QueryPort", QueryLinkPort);
        wtconf.GetItemVal("MaxQueryUserNum", QueryUserNum);

        wtconf.GetItemVal("InnerPort", InnerLinkPort);
        wtconf.GetItemVal("MaxForceUserNum", ForceUserNum);
        wtconf.GetItemVal("MaxSubUserNum", SubUserNum);
        wtconf.GetItemVal("MaxDiagnosisUserNum", DiagnosisUserNum);
        wtconf.GetItemVal("MaxMonitorUserNum", MonitorUserNum);

        wtconf.GetItemVal("VxiPortStart", VxiPortStart);
        wtconf.GetItemVal("VxiPortEnd", VxiPortEnd);
        wtconf.GetItemVal("MaxVxiUserNum", MaxVxiUserNum);
        printf("vxi port start: %d, end: %d, max user num: %d\n", VxiPortStart, VxiPortEnd, MaxVxiUserNum);
    };
    ~ScpiConfig() { WTLog::Instance().WriteLog(LOG_DEBUG, "ScpiConfig destruct\n"); }

    static ScpiConfig &Instance()
    {
        static ScpiConfig obj;
        return obj;
    }

    int GetInnerUserNum() { return (ForceUserNum + SubUserNum + DiagnosisUserNum + MonitorUserNum); }

    int NormalLinkPort;
    int NormalUserNum;

    int ManagerLinkPort;
    int ManagerUserNum;

    int QueryLinkPort;
    int QueryUserNum;

    int InnerLinkPort;
    int ForceUserNum;
    int SubUserNum;
    int DiagnosisUserNum;
    int MonitorUserNum;

    int VxiPortStart;
    int VxiPortEnd;
    int MaxVxiUserNum;
};

#endif //__SCPI_CONFIG_H__
