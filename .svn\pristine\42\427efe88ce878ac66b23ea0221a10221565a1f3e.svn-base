/*
 * @Description: 3GPP：LTE生成信号相关命令
 * @Autor: MaYongFeng
 * @Date: 2022-11-1 12:30:16
 */
#ifndef SCPI_3GPP_GEN_LTE_H_
#define SCPI_3GPP_GEN_LTE_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif
    scpi_result_t SCPI_LTE_QueryLTEVersion(scpi_t *context);

    // 上下行
    scpi_result_t SCPI_LTE_SetLinkDirect(scpi_t *context);

    //**************************************************************************************************
    // General
    //**************************************************************************************************
    scpi_result_t SCPI_LTE_SetGenWaveMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetGenWaveNo(scpi_t *context);
    scpi_result_t SCPI_LTE_SetGenWaveSequenceLen(scpi_t *context);
    //**************************************************************************************************
    // Filter
    //**************************************************************************************************
    scpi_result_t SCPI_LTE_SetFilterType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterOverFs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterMaxOrder(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterFpassFactor(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterFstopFactor(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterPassRipple(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterStopAtten(scpi_t *context);    
    scpi_result_t SCPI_LTE_SetFilterOptimization(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterRollOffFactor(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterCutOffFrqFactor(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterCutOffFreqShift(scpi_t *context);
    scpi_result_t SCPI_LTE_SetFilterSmoothFactor(scpi_t *context);

    //**************************************************************************************************
    // CELL
    //**************************************************************************************************
    //COMMOM
    scpi_result_t SCPI_LTE_SetCarrierAggregation(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellIndex(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellCid(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellBW(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDuolexing(scpi_t *context);
    scpi_result_t SCPI_LTE_SetULDLConfig(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSpecialSubframeConfig(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCyclicPrefix(scpi_t *context);
    //UL
    scpi_result_t SCPI_LTE_SetN1Dmrs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetULGroupHop(scpi_t *context);
    scpi_result_t SCPI_LTE_SetULSequenceHop(scpi_t *context);
    scpi_result_t SCPI_LTE_SetULDeltaSeqShift(scpi_t *context);
    //DL
    scpi_result_t SCPI_LTE_SetDLPdschScheduling(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellDLPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellDLPdschStart(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellDLPhichResource(scpi_t *context);
    scpi_result_t SCPI_LTE_SetCellDLPhichDuration(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSyncTxAntenna(scpi_t *context);
    scpi_result_t SCPI_LTE_SetPsyncPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSsyncPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetRefSignalPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetPdschPB(scpi_t *context);  
    scpi_result_t SCPI_LTE_SetPbchRatioRho(scpi_t *context);  
    scpi_result_t SCPI_LTE_SetPdcchRatioRho(scpi_t *context);     
    scpi_result_t SCPI_LTE_SetTxAntennaNum(scpi_t *context);              

    //**************************************************************************************************
    // UE
    //**************************************************************************************************
    //COMMOM
    scpi_result_t SCPI_LTE_SetUserID(scpi_t *context);
    //UL
    scpi_result_t SCPI_LTE_SetUserPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserPushData(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserPushPattern(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserPushTxMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserPushNAPort(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserScramblingState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserCCodingState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserCCodingMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserEnable256QAM(scpi_t *context);
    //DL
    scpi_result_t SCPI_LTE_SetUserScramble(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserChanCodingState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserAPMapping(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserUECategory(scpi_t *context);    
    scpi_result_t SCPI_LTE_SetUserCodebookIndex(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserDataType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserInitialization(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserPdschPA(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserMcsTable(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserTransmitMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetUserTBSTalternativeIndex(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSchedulePattern(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSchedulePdsch(scpi_t *context);
    scpi_result_t SCPI_LTE_SetScheduleHarqProcNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetScheduleNewDataIndic(scpi_t *context);
    scpi_result_t SCPI_LTE_SetScheduleMcs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedPusch(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedNrPdcchSymbNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedNrDLDciMcs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLNrDLDciPdcchFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLNrDLDciCceIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedNrULDciPdcchFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedNrULDciCceIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedSpPdcchSymbNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedSpDLDciMcs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSpDLDciPdcchFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSpDLDciCceIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedSpULDciPdcchFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetDLSchedSpULDciCceIdx(scpi_t *context);

    //**************************************************************************************************
    // Frame
    //**************************************************************************************************
    scpi_result_t SCPI_LTE_SetSubfUserPucchConfCount(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschConfCount(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPucchState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschRBSETnum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschRBCount(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschVRBoffset(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschFhopState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschPrecodingScheme(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschPrecodingNOLayers(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschPrecodingNAPused(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschCBINdex(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschDrsCYCShift(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschCodewords(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserPuschMcsMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserCwPuschMcs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserCwPuschModulation(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserCwPuschCCodingTBsize(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfUserCwPuschCCodingRVINdex(scpi_t *context);

    //DL
    scpi_result_t SCPI_LTE_SetSubfrmCfgNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmOcngFlag(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmDummyModulate(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmDummyDataType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetOCNGFlag(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPbchState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPbchScrambling(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPbchPrecoding(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPbchSNFOffset(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPbchSpareBit(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPcfichState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPcfichScrambling(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPcfichPrecoding(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPcfichPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPcfichPDCCHSymNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPhichPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPhichACKInfo(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDummyCCEType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDataType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchPower(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchAutoSchedDLDCI(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchSymbNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLUser(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLSearchSpace(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ResAllocateHeader(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ResBlkAssign(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1MCS(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1HarqProcNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1NewDataInd(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1RvIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1TPCCommand(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1DLAssignment(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AVRBAssignment(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AResBlkConfig(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARBNumber(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARBOffset(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AResBlkAssign(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AMCS(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AHarqProcNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ANewDataInd(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARvIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ATPCCommand(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ADLAssignment(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLPDCCHFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLCCEIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULUser(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULDCIFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULSearchSpace(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0FreqHop(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0ResBlkAssign(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0MCS(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0NewDataInd(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0TPCCommand(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0CyclicShiftForDMRS(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0ULIndex(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0DAI(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0CSIResquest(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0ResAllocateType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULPDCCHFormat(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULCCEIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschState(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschResAllocateType(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschVRBAssignment(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschRBGBitmap(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschRBNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschAutoOffset(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschRBOffset(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschSymbOffset(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschPrecoding(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschLayerNum(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCyclicDelayDiversity(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCodebookIndex(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwIRConfigMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCodeword(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschMCSConfigMode(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwMcs(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwModulate(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwPayloadSize(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwRedunVerIdx(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwNIR(scpi_t *context);
    scpi_result_t SCPI_LTE_SetSubfrmPdschCwSoftChanBit(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif