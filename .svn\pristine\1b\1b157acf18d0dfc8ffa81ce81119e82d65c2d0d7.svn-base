#message(STATUS "PROJECT_SOURCE_DIR: " ${PROJECT_SOURCE_DIR})
message(STATUS "CMAKE_CURRENT_SOURCE_DIR: " ${CMAKE_CURRENT_SOURCE_DIR})
message(STATUS "CMAKE_BUILD_TYPE: " ${CMAKE_BUILD_TYPE})

OPTION(TesterType "TesterType" WT448) # TesterType
set(SO_NAME "WT.Tester.API.MAC.Encryption")

add_definitions("-DLINUX" "-DWTTESTERAPI_MAC_ENCRYPTION_EXPORTS")

#execute_process(COMMAND sh ${CMAKE_CURRENT_SOURCE_DIR}/Includings.sh ${CMAKE_CURRENT_SOURCE_DIR})
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ../../extlib/include
	../../source/general
	../../source/filesecure
	../../source/server
	../../source/general/devlib
	../../source/server/analysis
)

if (CMAKE_BUILD_TYPE STREQUAL "")
    set(CMAKE_BUILD_TYPE "DEBUG")
else()
	string(TOUPPER ${CMAKE_BUILD_TYPE} CMAKE_BUILD_TYPE)
endif()


SET(CMAKE_CXX_FLAGS "$ENV{CXXFLAGS} -std=c++11 -MMD -MP")
if(StreamType STREQUAL "SISO")
    add_definitions("-DSISO_VER")
endif()

if(TesterType STREQUAL "WT448")
    add_definitions("-DWT448_FW")
elseif(TesterType STREQUAL "WT428")
    add_definitions("-DWT428_FW")
elseif(TesterType STREQUAL "WT328CE")
    add_definitions("-DWT418_FW")
elseif(TesterType STREQUAL "WT428C")
    add_definitions("-DWT418_FW")
elseif(TesterType STREQUAL "WT428H")
    add_definitions("-DWT428H_FW")
endif()

if(StreamType STREQUAL "SISO")
    add_definitions("-DSISO_VER")
endif()
if(TesterType STREQUAL "WT428")
    add_definitions("-DWT428_FW")
endif()
add_definitions(
    "-W"
    "-fPIC"
    "-Wall"
    "-Wno-comment"
    "-Wno-sign-compare"
    "-Wno-unused-function"
    "-Wno-unused-variable"
    "-Wno-unused-parameter"
    "-Wno-unused-but-set-variable"
	"-Wuninitialized"
	"-fvisibility=hidden"
)

if (CMAKE_BUILD_TYPE STREQUAL "RELEASE")
    add_definitions("-O2")
else()
    add_definitions("-g" "-D_DEBUG" "-ggdb")
endif()


set(EXTRA_LIBS 
    pthread 
    m 
)

file(GLOB_RECURSE SRC_LIST "./*.cpp")

link_directories(${PROJECT_BINARY_DIR}/lib) # 这行一定要在ADD_EXECUTABLE前面
add_library(${SO_NAME} SHARED ${SRC_LIST}) # lib的名字不能重复
target_link_libraries(${SO_NAME} ${EXTRA_LIBS})
set_target_properties(${SO_NAME} PROPERTIES OUTPUT_NAME ${SO_NAME})

# 设置动态库的版本号
#set_target_properties(${SO_NAME} PROPERTIES VERSION 1.2 SOVERSION 1)

set(LIBRARY_OUTPUT_PATH ${PROJECT_BINARY_DIR}/lib)

