﻿#ifndef ALG_3GPP_APIDEF_H_
#define ALG_3GPP_APIDEF_H_

/**************************************************************************************************/
/*                                    Common Area Start                                           */
/**************************************************************************************************/
/* WLAN standard from 0 to 0xFF. Cellular standard from 0x1000 */
typedef enum {
    ALG_3GPP_STD_5G = 0x1000,
    ALG_3GPP_STD_4G,
    ALG_3GPP_STD_NB_IOT,
    ALG_3GPP_STD_WCDMA,
    ALG_3GPP_STD_CDMA2000,
    ALG_3GPP_STD_TDSCDMA,
    ALG_3GPP_STD_GSM
} ALG_3GPP_STANDARD_TYPE;

#ifndef Complex_Def
#define Complex_Def
typedef double  Complex[2];
#endif

#define ALG_3GPP_MAX_STREAM         2

#define ALG_3GPP_UL                 0
#define ALG_3GPP_DL                 1

#define ALG_3GPP_FDD                0
#define ALG_3GPP_TDD                1

#define ALG_3GPP_NCP                0
#define ALG_3GPP_ECP                1

/* # Modulate type */
/* vsa auto detect modulate type */
#define ALG_3GPP_MOD_AUTO           0
#define ALG_3GPP_PID2_BPSK          1
/* BPSK also use 1bit, so define 0x11 */
#define ALG_3GPP_BPSK               0x11 /* 17 */
#define ALG_3GPP_QPSK               2
#define ALG_3GPP_PID4_QPSK          0x12 /* 18 */
#define ALG_3GPP_16QAM              4
#define ALG_3GPP_64QAM              6
#define ALG_3GPP_256QAM             8
#define ALG_3GPP_1024QAM            10

/* Payload data type */
#define ALG_3GPP_PSDU_PN9           0
#define ALG_3GPP_PSDU_PN11          1
#define ALG_3GPP_PSDU_PN15          2
#define ALG_3GPP_PSDU_PN23          3
#define ALG_3GPP_PSDU_ALL0          4
#define ALG_3GPP_PSDU_ALL1          5
#define ALG_3GPP_PSDU_1010          6

/* Generate wave length type */
#define ALG_3GPP_GW_FRAME           0
#define ALG_3GPP_GW_SUBFRM          1
#define ALG_3GPP_GW_SLOT            2

/* Filter Type */
#define ALG_3GPP_FILTER_NON         0
#define ALG_3GPP_FILTER_FIR         1
#define ALG_3GPP_FILTER_LTE         2
#define ALG_3GPP_FILTER_NR          2
#define ALG_3GPP_FILTER_RC          2
#define ALG_3GPP_FILTER_WOLA        3

#define ALG_3GPP_STANDARD           0
#define ALG_3GPP_MANUAL             1

#define ALG_3GPP_SEM_LIM_SET        12

/**************************************************************************************************/
/*                                    Common Area End                                             */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                   WaveGenerate Area Start                               */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                   WaveGenerate Area End                                 */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                  Analyze Area Start                                     */
/**************************************************************************************************/

typedef struct {
    void *capturedata;
    int capturecnt;
    int capturedata_format;

    /* Scale factor: % */
    double ScaleTo;
    /* IQ amplitude imbalance: dB */
    double IQImb_Amp;
    /* IQ phase imbalance: deg */
    double IQImb_Phase;
    /* I DC offset */
    double IDCOffset;
    /* Q DC offset */
    double QDCOffset;
    double TimeSkew;
    /* ADC sample frequency: Hz */
    int adc_freq;
    /* Internal RF gain: dB */
    double gain;
    /* External RF gain: dB */
    double extgain;
    /* RF carrier center frequency: Hz */
    double rf_center_freq;
    int rf_band;
    /* RF Channel number */
    int rf_channel;

    /* Refernece level */
    double referenceLevel;

    double *rf_response;
    int rf_response_len;

    double *bb_response;
    int bb_response_len;
} Alg_3GPP_RFInInfo;

typedef struct {
    int State;
    double StartFreq;
    double StopFreq;
    double LimitPower;
    int RBW;
} Alg_3GPP_SEMLimitType;

typedef struct {
    int InfoFlg;

    /* Constellatin result:
    * evm[0] is low, evm[1] is high. */
    int RefPointLen;
    Complex *RefPoint;
    int RxPointLen;
    Complex *RxPoint[2];
    /* Rx point type: 1(ALG_4G_SCH), 2(ALG_4G_DMRS)*/
    int *PointType;

    /* Each point EVM result for EVM vs. modulation symbols */
    int PointEvmLen;
    Complex *PointEvm;

    /* EVM vs. carrier result */
    int MaxCarrierLen;
    int CarrierLen;
    Complex *CarrierEvm;

    /* EVM vs. symbol result */
    int SymbLen;
    Complex *SymbEvm[2];
    /* Rx Symbol type: 1(ALG_4G_SCH), 2(ALG_4G_DMRS)*/
    int *SymbType;

    double EvmAllPCT[2];
    double RmsEvmPCT[2];
    double PeakEvmPCT[2];
    double DmrsEvmPCT[2];

    double EvmAlldB[2];
    double RmsEvmdB[2];
    double PeakEvmdB[2];
    double DmrsEvmdB[2];
} Alg_3GPP_EvmOutInfo;

typedef struct {
    int InfoFlg;

    int ErrLen;
    Complex *MagnErr[2];
    Complex *PhaseErr[2];

    double MagnErrRms[2];
    double MagnErrPeak[2];
    double MagnErrDmrs[2];

    double PhaseErrRms[2];
    double PhaseErrPeak[2];
    double PhaseErrDmrs[2];
} Alg_3GPP_MPErrInfo;

typedef struct {
    int InfoFlg;

    /* Current value */
    int RBEmisLen;
    Complex *RBEmis;
    /* Reference value */
    int EmisRefLen;
    Complex *EmisRef;
    Complex *EmisRefSeg;

    double MarginMin;
    int MarginMinIdx;

    int InEmisResult;
} Alg_3GPP_InEmisInfo;

typedef struct {
    int InfoFlg;
    int MaxFlatNum;
    int FlatLen;
    /* Flatness result: { carrier, flatness }*/
    Complex *FlatRes;
    Complex *FlatMaskUp;
    Complex *FlatMaskDown;
    Complex *RangeType;
    double Ripple1;
    double Ripple2;
    double MaxR1SubMinR2;
    double MaxR2SubMinR1;

    int FlatResult;
} Alg_3GPP_FlatnessInfo;

typedef struct {
    double FreqErr;
    double IqImbaAmp;
    double IqImbaPhs;
    double IqOffset;
    double CarrierLeakage; /* dBc */
    double PhaseErr; /* dB */
    double RSRP; /* dBm */
    double RSSI; /* dBm */
    double RSRQ; /* dB */
    double SNR; /* dB */

    Alg_3GPP_EvmOutInfo Evm;
    Alg_3GPP_MPErrInfo MPErr;
    /* Inband emission result */
    Alg_3GPP_InEmisInfo InEmis;
    Alg_3GPP_FlatnessInfo Flatness;
} Alg_3GPP_OfdmOutInfo;

/**************************************************************************************************/
/*                                  Analyze Area End                                       */
/**************************************************************************************************/

#endif