//*****************************************************************************
//File: rf.h
//Describe:射频板器件相关
//Author：wangzhenglong
//Date: 2016.10.1
//*****************************************************************************

#ifndef __RF_H__
#define __RF_H__
#define MHz             (1000*1000)
#define KHz             (1000)

#define RF_FREQ_MAX     RF_6G_MAX
#define RF_USE_MIX      RF_5G_MIN
#define RF_FREQ_MIN     RF_1G_MIN
#define LO_MIX_SW       (5800)

#define RF_6G_MAX       (8000 * 1e6)
#define RF_6G_MIN       (6000 * 1e6)

#define RF_5G_MAX       (6000 * 1e6)
#define RF_5G_MIN       (4900 * 1e6)

#define RF_4G_MAX       (4900 * 1e6)
#define RF_4G_MIN       (3800 * 1e6)

#define RF_3G_MAX       (3800 * 1e6)
#define RF_3G_MIN       (2500 * 1e6)

#define RF_2_4G_MAX     (2500 * 1e6)
#define RF_2_4G_MIN     (2400 * 1e6)

#define RF_2G_MAX       (2400 * 1e6)
#define RF_2G_MIN       (1000 * 1e6)

#define RF_1G_MAX       (1000 * 1e6)
#define RF_1G_MIN       (400  * 1e6)

#define NEED_ORIGIN_DATA_TEMP_STATE (0x60)

//RX/TX状态枚举类型
enum WT_RX_TX_STATUS
{
    WT_RX_TX_STATUS_DONE,
    WT_RX_TX_STATUS_RUNNING,
    WT_RX_TX_STATUS_STOP,
    WT_RX_TX_STATUS_DOWN,
    WT_RX_TX_STATUS_TIMEOUT,
    WT_RX_TX_STATUS_ERR_DONE,
    WT_RX_TX_STATUS_WAITING,    //等待Token
};

//仪器RX/TX状态定义
enum WT_RX_TX_STATE_E
{
    WT_RX_TX_STATE_DONE,
    WT_RX_TX_STATE_RUNNING,
    WT_RX_TX_STATE_TIMEOUT,
    WT_RX_TX_STATE_ERR_DONE,
    WT_RX_TX_STATE_NEED_FREE,
    WT_RX_TX_STATUS_WAIT_START,
    WT_RX_TX_STATUS_SEQ_SEG_DONE,
};

// 448调制BAND
enum WT_RF_MOD_BAND_E
{
    WT_RF_MOD_BAND_1,
    WT_RF_MOD_BAND_2,
    WT_RF_MOD_BAND_3,
    WT_RF_MOD_BAND_4,
    WT_RF_MOD_BAND_5,
    WT_RF_MOD_BAND_6,
    WT_RF_MOD_BAND_COUNT,
};

// 448混频BAND
enum WT_RF_MIX_BAND_E
{
    WT_RF_MIX_BAND_1,
    WT_RF_MIX_BAND_2,
    WT_RF_MIX_BAND_3,
    WT_RF_MIX_BAND_4,
    WT_RF_MIX_BAND_5,
    WT_RF_MIX_BAND_6,
    WT_RF_MIX_BAND_7,
    WT_RF_MIX_BAND_8,
    WT_RF_MIX_BAND_9,
    WT_RF_MIX_BAND_COUNT,
};

enum WT_RF_LO1_BAND_E
{
    LO1_BAND_1,
    LO1_BAND_2,
    LO1_BAND_3,
    LO1_BAND_4,
    LO1_BAND_5,
    LO1_BAND_6,
    LO1_BAND_COUNT,
};

enum WT_RF_LO2_BAND_E
{
    LO2_BAND_1,
    LO2_BAND_2,
    LO2_BAND_COUNT,
};

enum WT_MIX_LO_SWITCH_E
{
    MIX_LO_SWITCH_LOW,
    MIX_LO_SWITCH_HIGHT,
};

//RX/TX波段选择
enum WT_RF_BAND_MODE
{
    WT_RF_BAND_1G,             // 400M-1G
    WT_RF_BAND_2G,             // 1G-2.4G
    WT_RF_BAND_2_4G,           // 2.4G-2.5G
    WT_RF_BAND_3G,             // 2.5G-3.8G
    WT_RF_BAND_4G,             // 3.8G-4.9G
    WT_RF_5G_SUB_BAND_1,       //VSA:[4900,5060]MHz  VSG:[4900,5215]MHz
    WT_RF_5G_SUB_BAND_2,       //VSA:[5060,5450]MHz  VSG:[5215,5450]MHz
    WT_RF_5G_SUB_BAND_3,       //VSA:[5450,5685]MHz  VSG:[5450,5685]MHz
    WT_RF_5G_SUB_BAND_4,       //VSA:[5685,6000]MHz  VSG:[5685,6000]MHz
    WT_RF_MAX_BAND
};

//射频板开关通道工作类型
enum WT_RF_CHANNEL_WORK_TYPE_E
{
    AC_NOT_80_80,
    AC_80_80_A_MASTER,
    AC_80_80_B_MASTER,
    AC_WORK_TYPE_MAX,
};

//RF端口功能定义
enum WT_RF_STATUS_E
{
    WT_RF_OFF_STATUS,
    WT_RF_RX_STATUS,
    WT_RF_TX_STATUS
};

//RF端口定义1
enum WT_RF_PORT_E
{
    WT_RF_PORT_OFF,               //SB RF port off
    WT_RF_PORT_RF1,               //SB RF port 1
    WT_RF_PORT_RF2,               //SB RF port 2
    WT_RF_PORT_RF3,               //SB RF port 3
    WT_RF_PORT_RF4,               //SB RF port 4
    WT_RF_PORT_RF5,               //SB RF port 5
    WT_RF_PORT_RF6,               //SB RF port 6
    WT_RF_PORT_RF7,               //SB RF port 7
    WT_RF_PORT_RF8,               //SB RF port 8
    WT_RF_PORT_MAX,

    WT_PART_RF_PORT_A1 = WT_RF_PORT_RF1,           //SB RF PART A1
    WT_PART_RF_PORT_A2 = WT_RF_PORT_RF2,           //SB RF PART A2
    WT_PART_RF_PORT_A3 = WT_RF_PORT_RF3,           //SB RF PART A3
    WT_PART_RF_PORT_A4 = WT_RF_PORT_RF4,           //SB RF PART A4
    WT_PART_RF_PORT_B1 = WT_RF_PORT_RF5,           //SB RF PART B1
    WT_PART_RF_PORT_B2 = WT_RF_PORT_RF6,           //SB RF PART B2
    WT_PART_RF_PORT_B3 = WT_RF_PORT_RF7,           //SB RF PART B3
    WT_PART_RF_PORT_B4 = WT_RF_PORT_RF8,           //SB RF PART B4
};

// 射频板ATT, 4个主路ATT, 2个本振支路个1个ATT
enum WT_RF_ATT_ID_E
{
    RF_ATT0,
    RF_ATT1,
    RF_ATT2,
    RF_ATT3,
    RF_ATT4,
    RF_ATT5,
    RF_ATT_SW,
    RF_ATT_MAX,
    RF_LO_MOD_ATT = RF_ATT4,
    RF_LO_MIX_ATT = RF_ATT5,
};

//业务板Flash选择
enum WT_RF_FLASH_E
{
    BASE_BOARD_ROM,
    RF_BOARD_ROM
};

#define TEMP_SAMPLE_MAX 10
//射频板温度值采样结构体类型
struct TemperatureType
{
    int CurrentIndex;                    //当前所采样的温度值下标
    double Temperature[TEMP_SAMPLE_MAX]; //记录过去采样的10个温度值
};

#define RX_ATT_MAX              6   // 射频链路控制ATT数量
#define TX_ATT_MAX              6   // 射频链路控制ATT数量
#if RX_ATT_MAX > TX_ATT_MAX
#define ATT_MAX (RX_ATT_MAX)
#else
#define ATT_MAX (6)
#endif

#define ATT_CODE_MAX            63

#endif