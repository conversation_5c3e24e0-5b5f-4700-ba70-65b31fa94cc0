# NBIOT 文件重构指南

## 重构目标
参考 `scpi_3gpp_gen_wcdma.cpp` 的书写格式，重构整个 `scpi_3gpp_gen_nbiot.cpp` 文件。

## 重构原则

### 1. 头文件和命名空间
```cpp
#include "scpi_3gpp_gen_nbiot.h"

#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular;
```

### 2. 内联访问函数
```cpp
static inline Alg_NBIOT_WaveGenType &Nbiot(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->Pn3GPP->NBIOT;
}
```

### 3. 函数重构模式

#### 模式1: 简单整数参数
**原始格式:**
```cpp
scpi_result_t SCPI_NBIOT_SetFilterType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do {
        int Value = ALG_3GPP_FILTER_NON;
        if (!SCPI_ParamInt(context, &Value, true)) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < ALG_3GPP_FILTER_NON || ALG_3GPP_FILTER_WOLA < Value) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->NBIOT.General.Filter.Type = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}
```

**重构后格式:**
```cpp
scpi_result_t SCPI_NBIOT_SetFilterType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(ALG_3GPP_FILTER_NON, ALG_3GPP_FILTER_WOLA))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.Type = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
```

#### 模式2: 双精度浮点参数
**重构后格式:**
```cpp
scpi_result_t SCPI_NBIOT_SetFilterFpassFactor(scpi_t *context)
{
    double value = 0.0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.0, 1.0))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.FpassFactor = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
```

#### 模式3: 列表参数验证
**重构后格式:**
```cpp
scpi_result_t SCPI_NBIOT_SetFilterMaxOrder(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {128, 256, 512})
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).General.Filter.MaxOrder = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
```

#### 模式4: 带命令参数的函数
**原始格式:**
```cpp
scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciUser(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do {
        int Value = 0;
        int NAhrID = 0;
        SCPI_CommandNumbers(context, &NAhrID, 1);
        if (NAhrID < 0 || NAhrID >= 3) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true)) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 2) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->NBIOT.DL.NonAhrSchedule[NAhrID].Dci.User = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}
```

**重构后格式:**
```cpp
scpi_result_t SCPI_NBLOT_DL_SetNonAhrScheduleDciUser(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, 3))
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nbiot(context).DL.NonAhrSchedule[param].Dci.User = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
```

#### 模式5: 字符串参数处理
**原始格式:**
```cpp
scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoOptModeInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do {
        char text[256] = {0};
        size_t copyLen = 0;
        int Value = 0;
        if (!SCPI_ParamCopyText(context, text, sizeof(text) - 1, &copyLen, true)) {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        Value = std::stoi(text, nullptr, 2);
        if (Value < 0 || Value > 177) {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        memcpy(attr->Pn3GPP->NBIOT.DL.AhrSchedule.Npbch.MIBInfo.OptModeInfo, text, sizeof(char)*7);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}
```

**重构后格式:**
```cpp
scpi_result_t SCPI_NBLOT_DL_SetAnchorScheduleNpbchMIBInfoOptModeInfo(scpi_t *context)
{
    char text[256] = {0};
    size_t copyLen = 0;
    int iRet = ScpiChecker(context)
        .ParamText(text, sizeof(text) - 1, &copyLen)
        .Result();

    if (iRet == WT_OK) {
        int value = std::stoi(text, nullptr, 2);
        if (value >= 0 && value <= 177) {
            memcpy(Nbiot(context).DL.AhrSchedule.Npbch.MIBInfo.OptModeInfo, text, sizeof(char)*7);
        } else {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}
```

## 重构进度

### 已完成重构的函数 (约30个):
1. SCPI_NBLOT_SetLinkDirect
2. SCPI_NBIOT_SetFilterType
3. SCPI_NBIOT_SetFilterSampleRate
4. SCPI_NBIOT_SetFilterMaxOrder
5. SCPI_NBIOT_SetFilterFpassFactor
6. SCPI_NBIOT_SetFilterFstopFactor
7. SCPI_NBIOT_SetFilterPassRipple
8. SCPI_NBIOT_SetFilterStopAtten
9. SCPI_NBIOT_SetFilterRollOffFactor
10. SCPI_NBIOT_SetFilterCutOffFreqShift
11. SCPI_NBIOT_SetFilterWindowLenFactor
12. SCPI_NBLOT_UL_SetCellOperMode
13. SCPI_NBLOT_UL_SetCellBW
14. SCPI_NBLOT_UL_SetCellRBIndex
15. SCPI_NBLOT_UL_SetCellNBCellID
16. SCPI_NBLOT_UL_SetCellGroupHopping
17. SCPI_NBLOT_UL_SetCellToneCyclicShift3
18. SCPI_NBLOT_UL_SetCellToneCyclicShift6
19. SCPI_NBLOT_UL_SetCellBaseSequenceMode
... (其他已重构的函数)

### 待重构的函数 (约138个):
- 所有DL相关函数
- 复杂的字符串处理函数
- 带多个命令参数的函数

## 重构优势

1. **代码简洁性**: 消除了冗长的do-while结构
2. **一致性**: 与WCDMA文件格式保持一致
3. **可读性**: 使用ScpiChecker链式调用，逻辑更清晰
4. **维护性**: 统一的错误处理和参数验证
5. **性能**: 减少了重复的错误检查代码

## 下一步计划

1. 继续重构剩余的138个函数
2. 处理特殊的字符串参数函数
3. 处理带多个命令参数的复杂函数
4. 测试重构后的代码功能
5. 更新相关的头文件和文档
