TARGET = libfilesecure.so

CFLAGS_INCLUDE = -I./ -I$(TOPDIR)/general -I$(TOPDIR)/filesecure 

CFLAGS := $(filter-out -g, $(CFLAGS))
CFLAGS += -I./  -I$(TOPDIR)/general/ -I$(TOPDIR)/filesecure/ -fPIC -fvisibility=hidden
LDFLAGS += -shared   

SRC_DIR = $(shell pwd)
OBJ_DIR = objs
SRCS = $(wildcard $(shell find $(SRC_DIR) -name "*.cpp" -not -name "filetest.cpp"))
OBJS = $(patsubst $(SRC_DIR)/%.cpp, $(OBJ_DIR)/%.o, $(SRCS))
DEPS = $(patsubst %.o, %.d, $(OBJS))

SRC_DIR2 = $(shell pwd)/../general
SRCS2 = $(SRC_DIR2)/basefun.cpp $(SRC_DIR2)/secure.cpp
OBJS2 = $(patsubst $(SRC_DIR2)/%.cpp, $(OBJ_DIR)/%.o, $(SRCS2))

DEPS += $(patsubst %.o, %.d, $(OBJS2))
-include $(DEPS)

$(OBJ_DIR)/%.o: $(SRC_DIR2)/%.cpp
	@[ ! -e $(dir $@) ] &  mkdir -p $(dir $@)
	$(CXX) $(CFLAGS) -MF "$(@:%.o=%.d)" -MT "$@" -c $< -o $@

$(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp
	@[ ! -e $(dir $@) ] &  mkdir -p $(dir $@)
	$(CXX) $(CFLAGS) -MF "$(@:%.o=%.d)" -MT "$@" -c $< -o $@

$(OUTDIR)/$(TARGET): $(OBJS) $(OBJS2)
	$(CXX) -o $@ $^ $(DATEOBJ) $(LDFLAGS)

ifneq ($(RELEASE), 1)
all: $(OUTDIR)/$(TARGET)
	strip $(OUTDIR)/$(TARGET)
#	$(CXX) -std=c++11 -g -o $(OUTDIR)/ftest.o  $(SRC_DIR)/filetest.cpp -I./ -I$(SRC_DIR2) -L$(OUTDIR) -lfilesecure
else
all: $(OUTDIR)/$(TARGET)
	objcopy --only-keep-debug $(OUTDIR)/$(TARGET) $(OUTDIR)/$(TARGET).debug
	objcopy --strip-debug $(OUTDIR)/$(TARGET)
endif

clean:
	-@rm $(OBJ_DIR) -r -f

.PHONY: all clean