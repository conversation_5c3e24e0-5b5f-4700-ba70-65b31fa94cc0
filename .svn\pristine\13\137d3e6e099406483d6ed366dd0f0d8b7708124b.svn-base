/*
 * commonhandler.cpp
 *
 * 公共部分的callback
 *  Created on: 2019-3-14
 *      Author: Administrator
 */
#include "commonhandler.h"
#include <iostream>
#include <unistd.h>     //time
#include <memory>       //unique_ptr
#include <sys/socket.h> //send
#include <arpa/inet.h>
#include <vector>
#include <random>
#include <cmath>
#include <sys/time.h>
#include "basehead.h"
#include "tester.h"
#include "TesterCommon.h"
#include "internal.h"
#include "tester_admin.h"
#include "tester_mt.h"
#include "tester_cal.h"
#include "wlan_encryption_api.h"
#include "conf.h"
#include "server/scpi_conninfo.h"
#include "scpi_monitor.h"
#include "basetools.h"
#include "wtlog.h"
//#include "alg/includeAll.h"
/* *******************************************************************
 * scpi输出类callback函数
 * *******************************************************************/
using namespace std;

#define METER_INFO_FILE      "/meter/MeterInfo.json"

scpi_result_t SCPI_ResultOK(scpi_t *context, int isOK)
{
    if (context->cmd_ack)
    {
        if (WT_ERR_CODE_OK != isOK)
        {
            SCPI_ErrorPush(context, isOK);
            return SCPI_RES_ERR;
        }
        SCPI_ResultInt(context, WT_ERR_CODE_OK);
        return SCPI_RES_OK;
    }
    else
    {
        if (WT_ERR_CODE_OK != isOK)
        {
            SCPI_ErrorPush(context, isOK);
            return SCPI_RES_ERR;
        }
        return SCPI_RES_OK;
    }
}

scpi_result_t SCPI_ReponseEnable(scpi_t *context)
{
    int enable = 0;
    if (!SCPI_ParamInt(context, &enable, true))
    {
        return SCPI_RES_ERR;
    }
    context->cmd_ack = enable;
    return SCPI_ResultOK(context);
}

scpi_result_t Queryfilelength(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;
    char fileName[256] = {0};
    size_t copyLen = 0;
    int Exist = 0;

    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "check WaveName=" << fileName << std::endl;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_QueryTesterWaveFileOrRefFile(attr->ConnID, fileName, &Exist);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "iRet = " << iRet << ",File exist = " << Exist << endl;
    if (Exist == 0) // 文件不存在
    {
        iRet = WT_ERR_CODE_TESTER_NO_WAVE;
    }
    else
    {
        struct stat statbuf;

        std::string fullfileName = SCPI_WaveDir();
        fullfileName += fileName;
        if (stat(fullfileName.c_str(), &statbuf) == 0)
        {

            iRet = WT_OK;
            SCPI_ResultInt(context, statbuf.st_size);
            // WTLog::Instance().WriteLog(LOG_DEBUG, "statbuf.st_size = %ld\n\n\n", statbuf.st_size);
        }
    }

    return SCPI_ResultOK(context, iRet);
}
scpi_result_t QueryfileMD5code(scpi_t *context)
{
    std::string cmd;
    char fileName[256] = {0};
    size_t copyLen = 0;
    int bufSize = 1024 * 1024;
    int Exist = 0;
    int iRet = SCPI_RES_ERR;
    unique_ptr<char[]> cmd_ack = make_unique<char[]>(bufSize);

    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "check WaveName=" << fileName << std::endl;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_QueryTesterWaveFileOrRefFile(attr->ConnID, fileName, &Exist);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "iRet = " << iRet << ",File exist = " << Exist << endl;
    if (Exist == 0) // 文件不存在
    {
        iRet = WT_ERR_CODE_TESTER_NO_WAVE;
    }
    else
    {

        std::string tempcmd1 = "md5sum  ";
        std::string tempcmd2 = SCPI_WaveDir();
        std::string ch = "\"";

        cmd += tempcmd1;
        cmd += ch;
        cmd += tempcmd2;
        cmd += fileName;
        cmd += ch;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "cmd = " << cmd << "fileName = " << fileName << std::endl;
        iRet = WT_ExecShellCmd(
            attr->ConnID,
            cmd.c_str(),
            cmd_ack.get(),
            bufSize);
        char delims[] = " ";
        char *md5sumcode = strtok(cmd_ack.get(), delims);
        SCPI_ResultText(context, md5sumcode);
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_UpLoadFile(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    do
    {
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }
        string dirName(fileName);
        int pos = dirName.find_last_of('/');
        if (pos != string::npos)
        {
            string cmd = string("mkdir -p ") + dirName.substr(0, pos);
            auto _ = system(cmd.c_str());
            (void)_;
        }
        iRet = WriteFile(fileName, data, len);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DownLoadFile(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    unique_ptr<char[]> data = nullptr;
    int len = 0;
    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }
    iRet = ReadFile(fileName, data, len);
    IF_ERR_RETURN(iRet);

    SCPI_ResultArbitraryBlock(context, (char *)(data.get()), len);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_UpLoadFileRelativePath(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    do
    {
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }

        string dirName(WTConf::GetDir() + string("/"));
        dirName += fileName;
        int pos = dirName.find_last_of('/');
        if (pos != string::npos)
        {
            string cmd = string("mkdir -p ") + dirName.substr(0, pos);
            do_system_cmd(cmd);
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "path = " << dirName << std::endl;
        iRet = WriteFile(dirName.c_str(), data, len);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DownLoadFileRelativePath(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    unique_ptr<char[]> data = nullptr;
    int len = 0;
    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }
    string dirName(WTConf::GetDir() + string("/"));
    dirName += fileName;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "path = " << dirName << std::endl;
    iRet = ReadFile(dirName.c_str(), data, len);
    IF_ERR_RETURN(iRet);

    SCPI_ResultArbitraryBlock(context, (char *)(data.get()), len);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_ExeCmd(scpi_t *context)
{
    char cmd[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    int bufSize = 1024 * 1024;
    unique_ptr<char[]> cmd_ack = make_unique<char[]>(bufSize);

    if (!SCPI_ParamCopyText(context, cmd, sizeof(cmd) - 1, &copyLen, true))
    {
        return SCPI_RES_ERR;
    }

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_ExecShellCmd(
        attr->ConnID,
        cmd,
        cmd_ack.get(),
        bufSize);

    IF_ERR_RETURN(iRet);
    SCPI_ResultText(context, cmd_ack.get());
    return SCPI_RES_OK;
}

bool validStreamSegmentID(int &streamID, int &segmentID)
{
    if (0 > streamID || 0 > segmentID)
    {
        return false;
    }
    if (streamID > 8 || segmentID > 2)
    {
        return false;
    }
    return true;
}

bool validateIpAddress(const string &ipAddress)
{
    struct sockaddr_in sa;
    int result = inet_pton(AF_INET, ipAddress.c_str(), &(sa.sin_addr));
    return result != 0;
}

static int Send(int m_Fd, const char *Buf, int Len)
{
    int Pos = 0;
    while (Pos < Len)
    {
        errno = 0;
        int Ret = send(m_Fd, (char *)Buf + Pos, Len - Pos, 0);
        int err = errno;

        if (Ret > 0)
        {
            Pos += Ret;
        }
        else
        {
            if ((EINTR == err) || (EWOULDBLOCK == err) || (EAGAIN == err)) //重试
            {
                usleep(10);
                continue;
            }

            WTLog::Instance().WriteLog(LOG_ERROR,  "**ERROR: socket send: Ret=%d, errno(%d)\r\n", Ret, err);
            Pos = -1;
            break;
        }
    }

    return Pos;
}

#define MAX_PKT_SIZE 1024
static int Send(int fd, const void *Data, int Len, int &TxLen)
{
    int Ret = SCPI_RES_OK;

    (void)Len; // 所有数据总字节数; 也表示剩余需要发送字节数;
    TxLen = 0; // 已经发送的字节数
    while (Len > 0)
    {
        // 本次(一次)发送字节数
        int OnceCount = (Len >= MAX_PKT_SIZE) ? MAX_PKT_SIZE : Len;

        int RetCount = Send(fd, (char *)Data + TxLen, OnceCount);
        if (RetCount >= 0)
        {
            TxLen += RetCount;
            Len -= RetCount;
        }
        else
        {
            Ret = -1;
            break;
        }
    }

    return Ret;
}

size_t SCPI_Write(scpi_t *context, const char *data, size_t len)
{
    if (context->user_context != NULL)
    {
        int ClientFd = ((SPCIUserParam *)(context->user_context))->ClientFd;
        if (ClientFd >= 0)
        {
            int SendLen = 0;
            return Send(ClientFd, data, (int)len, SendLen);
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t SCPI_Flush(scpi_t *context)
{
    (void)context;
    return SCPI_RES_OK;
}

int SCPI_Error(scpi_t *context, int_fast32_t err)
{
    if (err)
    {
        WTLog::Instance().WriteLog(LOG_ERROR,  "**ERROR: %d, \"%s\"\r\n", (int32_t)err, SCPI_ErrorTranslate(err));
    }

    do
    {
        // ACK设置等于0的时候，非查询命令不再主动返回错误信息
        if (0 == context->cmd_ack && SCPI_SEQUENTIAL == context->cmd_type)
        {
            break;
        }

        if (nullptr != context->user_context)
        {
            SPCIUserParam *obj = static_cast<SPCIUserParam *>(context->user_context);
            int ClientFd = obj->ClientFd;
            if (ClientFd >= 0 && 0 != err)
            {
                char data[256] = {0};
                // check API connect status
                if (WT_ERR_CODE_OK != WT_CheckConnectStatus(obj->ConnID))
                {
                    err = WT_ERR_CODE_CONNECT_FAIL;
                    sprintf(data, "%d,\"%s\"\r\n", (int32_t)err, SCPI_ErrorTranslate(err));
                    int SendLen = 0;
                    Send(ClientFd, data, strlen(data), SendLen);
                    close(ClientFd);
                }
                else
                {
                    sprintf(data, "%d,\"%s\"\r\n", (int32_t)err, SCPI_ErrorTranslate(err));
                    int SendLen = 0;
                    Send(ClientFd, data, strlen(data), SendLen);
                }
            }
        }
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_Control(scpi_t *context, scpi_ctrl_name_t ctrl, scpi_reg_val_t val)
{
    char b[16] = {0};

    if (context->user_context != NULL)
    {
        int ClientFd = ((SPCIUserParam *)(context->user_context))->ClientFd;
        if (ClientFd >= 0)
        {
            snprintf(b, sizeof(b), "SRQ_%d=%d\r\n", ctrl, val);
            int SendLen = 0;
            return Send(ClientFd, b, strlen(b), SendLen) > 0 ? SCPI_RES_OK : SCPI_RES_ERR;
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t SCPI_Reset(scpi_t *context)
{
    SCPI_ErrorClear(context);
    SetClearData(context);
    return SCPI_ResultOK(context); //操作完成，返回ok，0
}

/* ************************************************************************
 *  基础cmd回调函数
 * ************************************************************************/
//*****************************************************************************
// 清空状态：清除仪器vsa，vsg配置，平均次数，以及vsa已抓取到的状态结果
// 参数: context：scpi输入结构
// 返回值: 无
//*****************************************************************************
scpi_result_t SetClearData(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->Reset();
    //清空软件的vsa抓取状态结果
    return SCPI_RES_OK;
}

//*****************************************************************************
// 清空状态：获取仪器标识，及其相关信息
// 参数: context：scpi输入结构
// 返回值: 无
//*****************************************************************************
scpi_result_t GetDeviceIDN(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    char Buf[2048] = {0};
    memset(&info, 0, sizeof(info));
    if (WT_GetTesterInfo(ConnID, &info) == WT_OK)
    {
        // sprintf(Buf, "DevName:%s,DevType:%d,SN:%s,FW Version:%s", info.Name, info.TesterType, info.SN, info.FwVersion);
        //仪器标识
        // iTest Technologies,WT-328,WT328-100100,1.3.5.6
        //公司，仪器类型，序号，固件版本
        char TesterType[256] = {0};
        switch (info.TesterType)
        {
        case TEST_TYPE_ENUM_WT448:
            strcpy(TesterType, "WT-448");
            break;
        case TEST_TYPE_ENUM_WT428:
            strcpy(TesterType, "WT-428");
            break;
        case TEST_TYPE_ENUM_WT328CE:
            strcpy(TesterType, "WT-328CE");
            break;
        case TEST_TYPE_ENUM_WT428C:
            strcpy(TesterType, "WT-428C");
            break;
        case TEST_TYPE_ENUM_WT428H:
            strcpy(TesterType, "WT-428H");
            break;
        default:
            strcpy(TesterType, "Unknown tester type");
            break;
        }

#if _SISO_MODE
#define MIMO_TYPE "SISO"
#else
#define MIMO_TYPE ""
#endif

        sprintf(Buf, "iTest Technologies %s,%s,%s,%s", MIMO_TYPE, TesterType, info.SN, info.FwVersion);
        SCPI_ResultText(context, Buf);
        return SCPI_RES_OK;
    }
    return SCPI_RES_ERR;
}

scpi_result_t GetTesterAlgVersionInfo(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;

    char info[512] = {0};
    Json::Reader reader;
    Json::Value root;

    int iRet = WT_QueryTesterVersionInfo(attr->ConnID, info);
    IF_ERR_RETURN(iRet);

    if (!reader.parse(info, root))
    {
        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    IF_ERR_RETURN(iRet);

    std::string vsaVersion = root["VsaAlgLibVer"].asString();
    std::string vsgVersion = root["VsgAlgLibVer"].asString();
    SCPI_ResultText(context, vsaVersion.c_str());
    SCPI_ResultText(context, vsgVersion.c_str());

    return SCPI_RES_OK;
}

scpi_result_t GetTester3GPPAlgVersionInfo(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;

    char info[512] = {0};
    Json::Reader reader;
    Json::Value root;

    int iRet = WT_QueryTesterVersionInfo(attr->ConnID, info);
    IF_ERR_RETURN(iRet);

    if (!reader.parse(info, root))
    {
        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    IF_ERR_RETURN(iRet);

    std::string vsaVersion = root["3GPPVsaAlgLibVer"].asString();
    std::string vsgVersion = root["3GPPVsgAlgLibVer"].asString();
    SCPI_ResultText(context, vsaVersion.c_str());
    SCPI_ResultText(context, vsgVersion.c_str());

    return SCPI_RES_OK;
}

scpi_result_t GetTesterCalVersionInfo(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;

    char info[512] = {0};
    Json::Reader reader;
    Json::Value root;

    int iRet = WT_QueryTesterVersionInfo(attr->ConnID, info);
    IF_ERR_RETURN(iRet);

    if (!reader.parse(info, root))
    {
        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    IF_ERR_RETURN(iRet);

    std::string Version = root["CalibrationLibVer"].asString();
    SCPI_ResultText(context, Version.c_str());

    return SCPI_RES_OK;
}

scpi_result_t GetTesterMeterVersionInfo(scpi_t *context)
{
    std::string Version = "0.0.0.0";
    std::string MeterInfoFile(WTConf::GetDir() + std::string("/") + METER_INFO_FILE);
    if (WT_OK == GetJsonItemData(MeterInfoFile, "MeterVersion", Version))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get Meter Version = " << Version << endl;
    }
    SCPI_ResultText(context, Version.c_str());

    return SCPI_RES_OK;
}

scpi_result_t GetTesterFt4222Info(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;

    char info[512] = {0};
    Json::Reader reader;
    Json::Value root;

    int iRet = WT_QueryTesterVersionInfo(attr->ConnID, info);
    IF_ERR_RETURN(iRet);

    if (!reader.parse(info, root))
    {
        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    IF_ERR_RETURN(iRet);

    std::string Version = root["Ft4222Info"].asString();
    SCPI_ResultText(context, Version.c_str());

    return SCPI_RES_OK;
}

scpi_result_t GetTesterNameInfo(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    TesterInfo info;

    char Buf[1024] = {0};
    memset(&info, 0, sizeof(info));
    int iRet = WT_GetTesterInfo(attr->ConnID, &info);
    IF_ERR_RETURN(iRet);

    int index = 0;
    char TesterType[256] = {0};
    switch (info.TesterType)
    {
    case TEST_TYPE_ENUM_WT448:
        strcpy(TesterType, "WT-448");
        break;
    case TEST_TYPE_ENUM_WT428:
        strcpy(TesterType, "WT-428");
        break;
    case TEST_TYPE_ENUM_WT328CE:
        strcpy(TesterType, "WT-328CE");
        break;
    case TEST_TYPE_ENUM_WT428C:
        strcpy(TesterType, "WT-428C");
        break;
    case TEST_TYPE_ENUM_WT428H:
        strcpy(TesterType, "WT-428H");
        break;
    default:
        strcpy(TesterType, "Unknown tester type");
        break;
    }
    index += sprintf(Buf + index, "Tester type:%s,", TesterType);
    index += sprintf(Buf + index, "Tester name:%s", info.Name);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetTesterIPInfo(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    char Buf[2048] = {0};
    memset(&info, 0, sizeof(info));
    int iRet = WT_GetTesterInfo(ConnID, &info);
    IF_ERR_RETURN(iRet);

    int index = 0;
    index += sprintf(Buf + index, "IP:%s,", info.IP);
    index += sprintf(Buf + index, "Net mask:%s,", info.SubMask);
    index += sprintf(Buf + index, "Gateway:%s,", info.GateWay);
    index += sprintf(Buf + index, "MAC:%s", info.Mac);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetTesterIPAddressType(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    bool IsDhcp = false;
    int Ret = WT_GetTesterIpAddressType(ConnID, &IsDhcp);
    IF_ERR_RETURN(Ret);

    if (IsDhcp == true)
    {
        SCPI_ResultText(context, "Dynamic");
    }
    else
    {
         SCPI_ResultText(context, "Static");
    }

    return SCPI_RES_OK;
}

scpi_result_t GetTesterModuleCnt(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    memset(&info, 0, sizeof(info));
    int iRet = WT_GetTesterInfo(ConnID, &info);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, info.BusiBoardCount);
    return SCPI_RES_OK;
}

static scpi_result_t GetTesterModuleInformation(scpi_t *context, string moduleType = "VSA")
{
    int ModuleID = 0;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;

    int32_t numbers[1] = {0};
    SCPI_CommandNumbers(context, numbers, 1);
    int iRet = WT_GetTesterInfo(ConnID, &info);
    IF_ERR_RETURN(iRet);
    ModuleID = numbers[0];

    if (ModuleID > info.BusiBoardCount)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModuleID = " << ModuleID << " > info.BusiBoardCount = " << info.BusiBoardCount << std::endl;
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    char Buf[2048] = {0};
    int index = 0;
    int count = 0;
    bool isFind = false;
    for (int i = 0; i < WT_MODULES_MAX_NUM; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BusiBoardInfo[" << i << "] type = " << info.BusiBoardInfo[i].Type << std::endl;
        if (info.TesterType < TEST_TYPE_ENUM_WT448 && moduleType == string((const char *)info.BusiBoardInfo[i].Type))
        {
            if (ModuleID == count)
            {
                ModuleID = i;
                isFind = true;
                break;
            }
            count++;
        }
        else if (info.TesterType >= TEST_TYPE_ENUM_WT448)
        {
            if (ModuleID == count)
            {
                ModuleID = i;
                isFind = true;
                break;
            }
            count++;
        }
    }
    if (false == isFind || ModuleID > info.BusiBoardCount)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Find no information. ModuleID = " << ModuleID << ",info.BusiBoardCount = " << info.BusiBoardCount << std::endl;
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    index += sprintf(Buf + index, "SN:%s,", info.BusiBoardInfo[ModuleID].SN);
    index += sprintf(Buf + index, "FPGA version:%s,", info.BusiBoardInfo[ModuleID].FPGAVersion);
    index += sprintf(Buf + index, "BB version:%s,", info.BusiBoardInfo[ModuleID].BBHWVersion);
    index += sprintf(Buf + index, "RF version:%s", info.BusiBoardInfo[ModuleID].RFHWVersion);

    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetTesterModuleVSA(scpi_t *context)
{
    return GetTesterModuleInformation(context, "VSA");
}

scpi_result_t GetTesterModuleVSG(scpi_t *context)
{
    return GetTesterModuleInformation(context, "VSG");
}

scpi_result_t GetTesterModuleBP(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    char Buf[2048] = {0};
    memset(&info, 0, sizeof(info));
    int iRet = WT_GetTesterInfo(ConnID, &info);
    IF_ERR_RETURN(iRet);

    int index = 0;
    index += sprintf(Buf + index, "FPGA version:%s,", info.BPInfo.FPGAVersion);
    index += sprintf(Buf + index, "BB version:%s,", info.BPInfo.BPHWVersion);
    index += sprintf(Buf + index, "SW version:%s", info.BPInfo.SwitchHWVersion);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetTesterTimeInfo(scpi_t *context)
{
    ExternedTesterOverview info;
    int iRet = WT_QuerySpecTester_V2("127.0.0.1", &info);
    IF_ERR_RETURN(iRet);
    int index = 0;
    char Buf[1024] = {0};
    index += sprintf(Buf + index, "Boot time:%s,", info.BootTime);
    index += sprintf(Buf + index, "Boot elapsed time:%s", info.BootElapsedTime);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetTesterMemorySize(scpi_t *context)
{
    std::string MemSize = "0";
    std::string cmd = "awk \'($1 == \"MemTotal:\"){print $2/1048576}\' /proc/meminfo";
    int iRet = do_system_cmd(cmd, MemSize);
    if (0 == iRet)
    {
        SCPI_ResultText(context, MemSize.c_str());
        return SCPI_RES_OK;
    }
    return SCPI_ResultOK(context, iRet);
}
//*****************************************************************************
// 清空状态：等待当前操作完成，server仅提供同步模式，固定返回“1”
// 参数: context：scpi输入结构
// 返回值: 无
//*****************************************************************************
scpi_result_t GetOPC(scpi_t *context)
{
    int32_t val = 1;
    SCPI_ResultInt(context, val);
    return SCPI_RES_OK;
}

//*****************************************************************************
// 清空状态：将仪器恢复出厂默认设置
// 参数: context：scpi输入结构
// 返回值: 无
//*****************************************************************************
scpi_result_t SetRestoreDevtoFactory(scpi_t *context)
{
    int Ret = WT_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    Ret = WT_FirmwareRestore(ConnID, WT_RESTORE_ORIGINAL);
    if (Ret == WT_OK)
    {
        Ret = WT_FactoryReset(ConnID);
    }
    if (Ret == WT_OK)
    {
        SCPI_ResultInt(context, WT_ERR_CODE_OK);
        return SCPI_RES_OK;
    }
    else
    {
        return SCPI_RES_ERR;
    }
}

//*****************************************************************************
// 等待完成所有待处理操作然后sleep等待1ms
// 参数: context：scpi输入结构
// 返回值: 无
//*****************************************************************************
scpi_result_t SetSleep(scpi_t *context)
{
    (void)context;
    // 1、判断所有下发操作是否完成
    // 2、等待1ms
    usleep(1000);
    return SCPI_ResultOK(context);
}

scpi_result_t SetSubNet(scpi_t *context)
{
    char buf[256] = {0};
    size_t copyLen = 0;

    ILLEGAL_PARAM_RETURN(context->parser_state.numberOfParameters != MAX_SUB_NET_COUNT + 1);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(WT_GetNetInfo(attr->ConnID, &attr->SubNetConfig));

    for (int i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        memset(buf, 0, sizeof(buf));
        if (!SCPI_ParamCopyText(context, buf, sizeof(buf), &copyLen, true))
        {
            return SCPI_RES_ERR;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << i << "] = " << buf << endl;
        ILLEGAL_PARAM_RETURN(false == validateIpAddress(string(buf)));

        if (0 == i)
        {
            strcpy(attr->SubNetConfig.DutAddr, buf);
        }
        else
        {
            strcpy(attr->SubNetConfig.VirAddr[i - 1], buf);
        }
    }
    return SCPI_ResultOK(context, WT_SetNetInfo(attr->ConnID, &attr->SubNetConfig));
}

scpi_result_t GetSubNet(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(WT_GetNetInfo(attr->ConnID, &attr->SubNetConfig));
    SCPI_ResultText(context, (const char *)attr->SubNetConfig.DutAddr);
    for (int i = 0; i < MAX_SUB_NET_COUNT; i++)
    {
        SCPI_ResultText(context, (const char *)attr->SubNetConfig.VirAddr[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetSubNetLink(scpi_t *context)
{
    bool linkStatus[MAX_SUB_NET_COUNT] = {false}; //false表示没有link，ture表示已经link
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int subNetIndex = 0;
    TesterInfo info;

    memset(&info, 0, sizeof(info));
    IF_ERR_RETURN(WT_GetTesterInfo(attr->ConnID, &info));
    IF_ERR_RETURN(WT_GetNetInfo(attr->ConnID, &attr->SubNetConfig));
    IF_ERR_RETURN(WT_GetNetLink(attr->ConnID, linkStatus));
    SCPI_ParamInt(context, &subNetIndex, false);

    if (info.TesterType == TEST_TYPE_ENUM_WT448) {
        SCPI_ResultText(context, "NoSupport");
        
        return SCPI_RES_OK;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "GetSubNetLink subNetIndex is:%d\n", subNetIndex);
    if (subNetIndex > MAX_SUB_NET_COUNT) {
       return SCPI_RES_ERR;
    }

    for (int i = 0; i < MAX_SUB_NET_COUNT; i++)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "subnet%d:ip is %s\n", i, (const char *)attr->SubNetConfig.VirAddr[i]);
        if (subNetIndex > 0) { //查询单个指定子网口link信息
            if (i + 1 == subNetIndex) {
                if (strlen((const char *)attr->SubNetConfig.VirAddr[i]) == 0) {//未配置子网口ip，则默认认为该端口没有link
                    SCPI_ResultText(context, "NoConfig");
                } else {
                    WTLog::Instance().WriteLog(LOG_DEBUG, "subnetLink%d:%s\n", i, (linkStatus[i] == false) ? "NoLink" : "Linked");
                    SCPI_ResultText(context, (linkStatus[i] == false) ? "NoLink" : "Linked");
                }
                break;
            }
        } else {

            if (strlen((const char *)attr->SubNetConfig.VirAddr[i]) == 0) {//未配置子网口ip，则默认认为该端口没有link
                SCPI_ResultText(context, "NoConfig");
            } else {
                WTLog::Instance().WriteLog(LOG_DEBUG, "subnetLink%d:%s\n", i, (linkStatus[i] == false) ? "NoLink" : "Linked");
                SCPI_ResultText(context, (linkStatus[i] == false) ? "NoLink" : "Linked");
            }
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t SetSubNetTftp(scpi_t *context)
{
    char buf[256] = {0};
    size_t copyLen = 0;
    const int tftpIPCnt = 5;

    ILLEGAL_PARAM_RETURN(tftpIPCnt != context->parser_state.numberOfParameters);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(WT_GetNetInfo(attr->ConnID, &attr->SubNetConfig));

    for (int i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        memset(buf, 0, sizeof(buf));
        if (!SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, true))
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "line%d, SetSubNetTftp SCPI_RES_ERR\n", __LINE__);
            return SCPI_RES_ERR;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << i << "] = " << buf << endl;
        if (false == validateIpAddress(string(buf)))
        {
            //不是合法IP时清空字符串
            buf[0] = 0;
        }

        switch (i)
        {
        case 0:
            strcpy(attr->SubNetConfig.DutTftpServerAddr, buf);
            break;
        case 1:
            strcpy(attr->SubNetConfig.TftpServerAddr, buf);
            break;
        case 2:
            strcpy(attr->SubNetConfig.TftpClientAddr, buf);
            break;
        case 3:
            strcpy(attr->SubNetConfig.TftpServerPCAddr, buf);
            break;
        default:
            strcpy(attr->SubNetConfig.TftpServerPCAddr2, buf);
            break;
        }
    }

    //有效性判断交由server处理
    return SCPI_ResultOK(context, WT_SetNetInfo(attr->ConnID, &attr->SubNetConfig));
}

scpi_result_t GetSubNetTftp(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(WT_GetNetInfo(attr->ConnID, &attr->SubNetConfig));
    SCPI_ResultText(context, (const char *)attr->SubNetConfig.DutTftpServerAddr);
    SCPI_ResultText(context, (const char *)attr->SubNetConfig.TftpServerAddr);
    SCPI_ResultText(context, (const char *)attr->SubNetConfig.TftpClientAddr);
    SCPI_ResultText(context, (const char *)attr->SubNetConfig.TftpServerPCAddr);
    SCPI_ResultText(context, (const char *)attr->SubNetConfig.TftpServerPCAddr2);
    return SCPI_RES_OK;
}

scpi_result_t SetSubNetAutoNeg(scpi_t *context)
{
    int Ret = WT_OK;
    int ParamVal;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            Ret = SCPI_RES_ERR;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_SetSubNetAutoNeg(attr->ConnID, ParamVal);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetSubNetAutoNeg = " << ParamVal << endl;
    } while (0);

    return SCPI_ResultOK(context, Ret);
}

scpi_result_t GetSubNetAutoNeg(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_GetSubNetAutoNeg(attr->ConnID, &ParamVal);
    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetSubNetAutoNeg=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t GetWaveNameList(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    const int len = 8 * 1024 * 1024;
    std::unique_ptr<char[]> buf(new char[len]);
    unsigned int Cnt = 0;
    const char *path = "./";

    int iRet = WT_GetTesterAllWaveFileOrRefFileNames(attr->ConnID, path, buf.get(), len, &Cnt);
    if (WT_ERR_CODE_OK == iRet)
    {
        for (int i = 0; i < Cnt; i++)
        {
            char fileName[256] = {0};
            memcpy(fileName, buf.get() + i * sizeof(fileName), sizeof(fileName));
            if (fileName[0] != '\0')
            {
                SCPI_ResultText(context, (const char *)fileName);
            }
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t GetMACAddr(scpi_t *context)
{
    string cmd;
    string result;
    string DevName = "eth10";
    int ParamVal;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Ret = WT_OK;
    IF_ERR_RETURN(WT_GetNetInfo(attr->ConnID, &attr->SubNetConfig));
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 1 || ParamVal > 8); // 判断范围
        if (false == validateIpAddress(string(attr->SubNetConfig.DutAddr)))
        {
            Ret = WT_IP_ERROR;
            break;
        }
        switch (ParamVal)
        {
        case 1:
            DevName = "eth10";
            break;
        case 2:
            DevName = "eth11";
            break;
        case 3:
            DevName = "eth12";
            break;
        case 4:
            DevName = "eth13";
            break;
        case 5:
            DevName = "eth14";
            break;
        case 6:
            DevName = "eth15";
            break;
        case 7:
            DevName = "eth16";
            break;
        case 8:
            DevName = "eth17";
            break;
        // case 18: DevName = "eth18";break;
        // case 0: DevName = "eth0";break;
        default:;
        }

        cmd = "ping -c 1 -w 1 -I "+ DevName + " " + string(attr->SubNetConfig.DutAddr) + "|grep received| sed -n '1{s/.* \\(.*\\) received.*/\\1/;p}'";
        do_system_cmd(cmd, result);
        if (atoi(result.c_str()) > 0)
        {
            cmd = "arp -a " + string(attr->SubNetConfig.DutAddr) + " |grep " + DevName + "| sed -n '1{s/.*at \\(.*\\) \\[.*/\\1/;p}'";
            do_system_cmd(cmd, result);
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "result = " << result << std::endl;
            if (result != "")
            {
                SCPI_ResultText(context, (const char *)result.c_str());
            }
            else
            {
                Ret = WT_IP_ERROR;
                break;
            }
        }
        else
        {
            Ret = WT_IP_ERROR;
            break;
        }
    } while (0);
    IF_ERR_RETURN(Ret);
    return SCPI_RES_OK;
}

scpi_result_t AddRemoteTester(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    //添加从机前先判断mimo lic
    if(attr->CheckBusinessLic(WT_WIFI_MIMO_API) != true)
    {
        SCPI_ErrorPush(context, WT_ERR_CODE_LICENSE_ERROR);
        return SCPI_RES_ERR;
    }

    EMPTY_PARAM_ERROR(context);

    char ip[WT_SUB_TESTER_INDEX_MAX][16];
    size_t len;
    memset(ip, 0, sizeof(ip));
    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamCopyText(context, ip[i], sizeof(ip[i]), &len, true))
        {
            return SCPI_RES_ERR;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "MIMO[" << i << "] IP = " << ip[i] << endl;
        ILLEGAL_PARAM_RETURN(false == validateIpAddress(ip[i]));
    }

    int iRet = WT_ERR_CODE_OK;
    do
    {
        iRet = WT_SwitchMode(attr->ConnID, TEST_MULTI_MIMO_API);
        if (iRet)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_SwitchMode error: " << iRet << std::endl;
            break;
        }

        for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
        {
            ConnectedUnit unit;
            memset(&unit, 0, sizeof(unit));
            strcpy(unit.Ip, ip[i]);
            unit.SubTesterIndex = WT_SUB_TESTER_INDEX0;

            iRet = WT_AddMimoTester(attr->ConnID, unit);
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_AddMimoTester[" << (i + 1) << "] result: " << iRet << std::endl;
            usleep(100 * 1000);

            if (iRet)
            {
                //连接失败时断开所有从机
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_RemoveMimoTester" << std::endl;
                WT_RemoveMimoTester(attr->ConnID);
                break;
            }
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t DelRemoteTester(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    WT_RemoveMimoTester(attr->ConnID);
    return SCPI_ResultOK(context);
}

scpi_result_t DumpMemoryParam(scpi_t *context)
{
    (void)context;
    WT_MemDump();
    return SCPI_RES_OK;
}

static int GetCalIntParam(scpi_t *context, int *Mode, int *Value)
{
    int iRet = WT_ERR_CODE_OK;
    int32_t numbers[1] = {0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 1);
        if (Value)
        {
            if (!SCPI_ParamInt(context, Value, true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        *Mode = numbers[0];
    } while (0);

    return iRet;
}
scpi_result_t SCPI_SetCalibrationMode(scpi_t *context)
{
    int mode = 0;
    int enable = 0;
    int iRet = GetCalIntParam(context, &mode, &enable);
    IF_ERR_RETURN(iRet);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->m_CalSetting.mode[mode] = enable;
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetCalibrationMode(scpi_t *context)
{
    int mode = 0;
    int iRet = GetCalIntParam(context, &mode, nullptr);
    IF_ERR_RETURN(iRet);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultInt(context, attr->m_CalSetting.mode[mode]);
    SCPI_ResultInt(context, attr->m_CalSetting.mode_enable[mode]);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_ReLoadCalibrationData(scpi_t *context)
{
    (void)context;
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetCalibrationFlatness(scpi_t *context)
{
    int value = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_CalSetting.flatnessEnable = value;

    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetSlaveTesterNameInfo(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    TesterInfo info;
    int iRet = WT_ERR_CODE_OK;
    char Buf[1024] = {0};
    int index = 0;
    char TesterType[256] = {0};
    int slaveID = 1;

    memset(&info, 0, sizeof(info));
    SCPI_ParamInt(context, &slaveID, false);
    iRet = WT_GetSlaveTesterInfo(attr->ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);

    switch (info.TesterType)
    {
    case TEST_TYPE_ENUM_WT448:
        strcpy(TesterType, "WT-448");
        break;
    case TEST_TYPE_ENUM_WT428:
        strcpy(TesterType, "WT-428");
        break;
    case TEST_TYPE_ENUM_WT328CE:
        strcpy(TesterType, "WT-328CE");
        break;
    case TEST_TYPE_ENUM_WT428C:
        strcpy(TesterType, "WT-428C");
        break;
    case TEST_TYPE_ENUM_WT428H:
        strcpy(TesterType, "WT-428H");
        break;
    default:
        strcpy(TesterType, "Unknown tester type");
        break;
    }

    index += sprintf(Buf + index, "Tester type:%s,", TesterType);
    index += sprintf(Buf + index, "Tester name:%s", info.Name);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetSlaveTesterAlgVersionInfo(scpi_t *context)
{
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    TesterInfo info;
    int slaveID = 1;
    int iRet = WT_ERR_CODE_OK;

    memset(&info, 0, sizeof(info));
    SCPI_ParamInt(context, &slaveID, false);
    iRet = WT_GetSlaveTesterInfo(attr->ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);

    SCPI_ResultText(context, info.ALGVersion);
    return SCPI_RES_OK;
}

scpi_result_t GetSlaveTesterIPInfo(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    int slaveID = 1;
    int index = 0;
    char Buf[2048] = {0};
    int iRet = WT_ERR_CODE_OK;

    memset(&info, 0, sizeof(info));
    SCPI_ParamInt(context, &slaveID, false);
    iRet = WT_GetSlaveTesterInfo(ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);

    index += sprintf(Buf + index, "IP:%s,", info.IP);
    index += sprintf(Buf + index, "Net mask:%s,", info.SubMask);
    index += sprintf(Buf + index, "Gateway:%s,", info.GateWay);
    index += sprintf(Buf + index, "MAC:%s", info.Mac);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetSlaveTesterModuleCnt(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    int slaveID = 1;
    int iRet = WT_ERR_CODE_OK;

    memset(&info, 0, sizeof(info));
    SCPI_ParamInt(context, &slaveID, false);
    iRet = WT_GetSlaveTesterInfo(ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, info.BusiBoardCount);
    return SCPI_RES_OK;
}

static scpi_result_t GetSlaveTesterModuleInformation(scpi_t *context, string moduleType = "VSA")
{
    int ModuleID = 0;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;

    int32_t numbers[1] = {0};
    SCPI_CommandNumbers(context, numbers, 1);

    int slaveID = 1;
    SCPI_ParamInt(context, &slaveID, false);

    int iRet = WT_GetSlaveTesterInfo(ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);
    ModuleID = numbers[0];
    if (ModuleID > info.BusiBoardCount)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModuleID = " << ModuleID << ",info.BusiBoardCount = " << info.BusiBoardCount << std::endl;
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    char Buf[2048] = {0};
    int index = 0;
    int count = 0;
    bool isFind = false;
    for (int i = 0; i < WT_MODULES_MAX_NUM; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "BusiBoardInfo[" << i << "] type = " << info.BusiBoardInfo[i].Type << std::endl;
        if (info.TesterType < TEST_TYPE_ENUM_WT448 && moduleType == string((const char *)info.BusiBoardInfo[i].Type))
        {
            if (ModuleID == count)
            {
                ModuleID = i;
                isFind = true;
                break;
            }
            count++;
        }
        else if (info.TesterType >= TEST_TYPE_ENUM_WT448)
        {
            if (ModuleID == count)
            {
                ModuleID = i;
                isFind = true;
                break;
            }
            count++;
        }
    }
    if (false == isFind || ModuleID > info.BusiBoardCount)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Find no information. ModuleID = " << ModuleID << ",info.BusiBoardCount = " << info.BusiBoardCount << std::endl;
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(iRet);

    index += sprintf(Buf + index, "SN:%s,", info.BusiBoardInfo[ModuleID].SN);
    index += sprintf(Buf + index, "FPGA version:%s,", info.BusiBoardInfo[ModuleID].FPGAVersion);
    index += sprintf(Buf + index, "BB version:%s,", info.BusiBoardInfo[ModuleID].BBHWVersion);
    index += sprintf(Buf + index, "RF version:%s", info.BusiBoardInfo[ModuleID].RFHWVersion);

    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetSlaveTesterModuleVSA(scpi_t *context)
{
    return GetSlaveTesterModuleInformation(context, "VSA");
}

scpi_result_t GetSlaveTesterModuleVSG(scpi_t *context)
{
    return GetSlaveTesterModuleInformation(context, "VSG");
}

scpi_result_t GetSlaveTesterModuleBP(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    int slaveID = 1;
    int index = 0;
    char Buf[2048] = {0};
    int iRet = WT_ERR_CODE_OK;

    memset(&info, 0, sizeof(info));
    SCPI_ParamInt(context, &slaveID, false);
    iRet = WT_GetSlaveTesterInfo(ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);

    index += sprintf(Buf + index, "FPGA version:%s,", info.BPInfo.FPGAVersion);
    index += sprintf(Buf + index, "BB version:%s,", info.BPInfo.BPHWVersion);
    index += sprintf(Buf + index, "SW version:%s", info.BPInfo.SwitchHWVersion);
    SCPI_ResultText(context, Buf);
    return SCPI_RES_OK;
}

scpi_result_t GetSlaveTesterInfoARB(scpi_t *context)
{
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    TesterInfo info;
    int slaveID = 1;
    int iRet = WT_ERR_CODE_OK;

    memset(&info, 0, sizeof(info));
    SCPI_ParamInt(context, &slaveID, false);
    iRet = WT_GetSlaveTesterInfo(ConnID, slaveID, &info);
    IF_ERR_RETURN(iRet);

    SCPI_ResultArbitraryBlock(context, (char *)&info, sizeof(TesterInfo));
    return SCPI_RES_OK;
}

scpi_result_t SCPI_ForceConnect(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    if (attr->TesterLinkType != LINK_TYPE_INVALID)
    {
        return SCPI_ResultOK(context, iRet);
    }

    do
    {
        iRet = attr->Connect(LINK_TYPE_FORCE);
        IF_BREAK(iRet);

        char IP[16] = {0};
        int Port = 0;
        GetSockPeerInfo(attr->ClientFd, IP, Port);

        iRet = ScpiConnInfo::Instance().AddLink(IP, Port, attr->TesterLinkType, attr->ConnID);
        IF_BREAK(iRet);
    } while (0);

    if (iRet != WT_OK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WT_DisConnect ConnID = %d, Ret=%#x\n", attr->ConnID, iRet);
        WT_DisConnect(attr->ConnID);
        attr->TesterLinkType = LINK_TYPE_INVALID;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_SubConnect(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;
    char IP[16] = {0};
    int Port = 0;
    char FatherIP[16] = {0};
    int FatherPort = 0;
    size_t copyLen = 0;
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;

    if (attr->TesterLinkType != LINK_TYPE_INVALID)
    {
        return SCPI_ResultOK(context, iRet);
    }

    do
    {
        if (!SCPI_ParamCopyText(context, FatherIP, sizeof(FatherIP) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamInt(context, &FatherPort, false))
        {
            break;
        }

        GetSockPeerInfo(attr->ClientFd, IP, Port);

        //检查父连接是否存在
        int Exist = false;
        iRet = ScpiConnInfo::Instance().CheckFatherLinkExist(FatherIP, FatherPort, Exist);
        IF_BREAK(iRet);
        if (Exist == false)
        {
            iRet = WT_FIND_LINK_FAILED;
            break;
        }

        iRet = attr->Connect(LINK_TYPE_SUB);
        IF_BREAK(iRet);

        iRet = ScpiConnInfo::Instance().AddLink(IP, Port, attr->TesterLinkType, attr->ConnID, FatherIP, FatherPort);
        IF_BREAK(iRet);

        std::vector<int> ConnId;
        iRet = ScpiConnInfo::Instance().GetFamilyMember(FatherIP, FatherPort, ConnId);
        IF_BREAK(iRet);

        iRet = WT_AckConnectInfo(attr->ConnID, ConnId.data(), ConnId.size());
        IF_BREAK(iRet);
    } while (0);

    if (iRet != WT_OK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WT_DisConnect ConnID = %d, Ret=%#x\n", attr->ConnID, iRet);
        WT_DisConnect(attr->ConnID);
        attr->TesterLinkType = LINK_TYPE_INVALID;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_MonitorConnect(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    if (attr->TesterLinkType != LINK_TYPE_INVALID)
    {
        return SCPI_ResultOK(context, iRet);
    }

    do
    {
        //连接到固件的连接保留，监视可以通过本身获取仪器的信息，和lic信息等
        iRet = attr->Connect(LINK_TYPE_MONITOR);
        IF_BREAK(iRet);

        MonitorMgr::Instance().AddMonitor(context); //监听连接成功，添加到监听列表
        char IP[16] = {0};
        int Port = 0;
        GetSockPeerInfo(attr->ClientFd, IP, Port);

        iRet = ScpiConnInfo::Instance().AddLink(IP, Port, attr->TesterLinkType, attr->ConnID);
        IF_BREAK(iRet);
    } while (0);

    if (iRet != WT_OK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WT_DisConnect ConnID = %d, Ret=%#x\n", attr->ConnID, iRet);
        WT_DisConnect(attr->ConnID);
        attr->TesterLinkType = LINK_TYPE_INVALID;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DiagnosisConnect(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    if (attr->TesterLinkType != LINK_TYPE_INVALID)
    {
        return SCPI_ResultOK(context, iRet);
    }

    do
    {
        //还是需要连接到fw，需要通过命令获取仪器的设备信息，lic等
        iRet = attr->Connect(LINK_TYPE_DIAGNOSIS);
        IF_BREAK(iRet);

        char IP[16] = {0};
        int Port = 0;
        GetSockPeerInfo(attr->ClientFd, IP, Port);

        iRet = ScpiConnInfo::Instance().AddLink(IP, Port, attr->TesterLinkType, attr->ConnID);
        IF_BREAK(iRet);
    } while (0);

    if (iRet != WT_OK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WT_DisConnect ConnID = %d, Ret=%#x\n", attr->ConnID, iRet);
        WT_DisConnect(attr->ConnID);
        attr->TesterLinkType = LINK_TYPE_INVALID;
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_SetMonitorRfPort(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;

    do
    {
        int MoniPort = 0;
        if (!SCPI_ParamInt(context, &MoniPort, false))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (MoniPort < WT_PORT_RF1 || MoniPort > WT_PORT_RF8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
        shared_ptr<Monitor> MonitorObj = MonitorMgr::Instance().GetMonitorByClientFd(attr->ClientFd);
        if (MonitorObj)
        {
            iRet = MonitorObj->SetMonObj(MoniPort);
            IF_BREAK(iRet);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_SetMonitorAction(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;

    do
    {
        int Action = 0;
        if (!SCPI_ParamInt(context, &Action, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Action < 0 || Action > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int DataType = -1;
        if (!SCPI_ParamInt(context, &DataType, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (DataType < 0 || DataType >= WT_MONITOR_MAX)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        char DataString[256] = {0};
        std::vector<std::string> VsaResult;
        if (DataType == MON_VSA_RESULT) // vsa analyze result
        {
            size_t copyLen = 0;
            if (!SCPI_ParamCopyText(context, DataString, sizeof(DataString) - 1, &copyLen, true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }
            // WTLog::Instance().WriteLog(LOG_DEBUG, "Set Monitor: Item = %s##################\n",DataString);
            VsaResult.push_back(string(DataString));
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        shared_ptr<Monitor> MonitorObj = MonitorMgr::Instance().GetMonitorByClientFd(attr->ClientFd);
        if (MonitorObj)
        {
            iRet = MonitorObj->SetMonData(Action, DataType, VsaResult);
            IF_BREAK(iRet);
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

static int get_random_int(int min = 1, int max = 100000)
{
    // Seed with a real random value, if available
    std::random_device r;
    // Choose a random mean between 1 and 6
    std::default_random_engine e1(r());
    std::uniform_int_distribution<int> uniform_dist(min, max);
    return uniform_dist(e1);
}

std::string get_random_name()
{
    struct timeval now;
    double timeUs = 0.0;
    char tmpName[256] = {0};
    gettimeofday(&now, nullptr);
    timeUs = now.tv_sec * 1e6 + now.tv_usec;
    sprintf(tmpName, "%f_%d", timeUs, get_random_int());
    return std::string(tmpName);
}

int check_waveform_exist_v2(scpi_t *context, char *waveName, std::vector<std::string> &low_name_list, bool create_low_file, bool is_vsg)
{
    int iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
    std::string tmpWave(waveName);
    std::string wavePath(SCPI_WaveDir());
    std::string low_name;
    SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    struct stat FileStat;

    do
    {
        if (tmpWave.at(0) == '/')
        {
            tmpWave = string(".") + tmpWave;
        }
        wavePath += tmpWave;

        /* 检查是否存在bwv/csv原始文件 */
        if (0 != access(wavePath.c_str(), F_OK))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Find " << wavePath << " fail" << std::endl;
            break;
        }
        else if (0 != stat(wavePath.c_str(), &FileStat))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "stat " << wavePath << " fail" << std::endl;
            break;
        }

        size_t pos_e = tmpWave.find_last_of(".");
        if (0 == strncmp(&tmpWave[pos_e], ".bwv2", 5) || 0 == strncmp(&tmpWave[pos_e], ".csv2", 5))
        {
            int PnCount = 0;
            int Deleteflag = false;
            int PnOrder[65];//多PN信号的信号数量上限64
            iRet = WT_GetPnCount(attr->ConnID, wavePath.c_str(), PnCount, PnOrder);
            if (iRet)
            {
                break;
            }
            if (PnCount <= 0)
            {
                iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
                break;
            }
            for(int i = 0; i < PnCount;i++)
            {
                low_name.clear();
                low_name = tmpWave.substr(0, pos_e);
                low_name += "_Fd" + to_string(attr->ClientFd) +
                    "_ConId" + to_string(attr->ConnID) +
                    "_" + to_string(FileStat.st_mtime) +
                    ".PN" + to_string(PnOrder[i])+
                    ".low";
                std::string wavePath_low(SCPI_LowWaveDir());
                wavePath_low += low_name;

                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "wave["<<i<<"] path = " << wavePath << std::endl;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Low wave["<<i<<"] path = " << wavePath_low << std::endl;
                /* 检查是否存在补偿后的low文件 */
                if (create_low_file && 0 != access(wavePath_low.c_str(), F_OK))
                {
                    if(!Deleteflag)
                    {
                        //删除之前保存的文件
                        remove_low_file(tmpWave, attr->ClientFd, attr->ConnID);
                        Deleteflag = true;
                    }
                    //创建新的文件
                    make_file_dir(SCPI_LowWaveDir(), low_name);
                    iRet = WT_CreateLowWave(attr->ConnID, is_vsg, wavePath.c_str(), low_name.c_str());
                    if (iRet)
                    {
                        break;
                    }
                }
                else
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "not create wave"<< std::endl;
                }
                low_name_list.push_back(low_name);
            }
        }
        else
        {
            std::string wavePath_low(SCPI_LowWaveDir());
            low_name = tmpWave.substr(0, pos_e);
            low_name +="_pre_save.low";
            string tmpstr = find_pre_save_low_file(low_name);

            if(tmpstr.length() > 0)
            {
                low_name = tmpstr;
                wavePath_low += low_name;
            }
            else
            {
                low_name = tmpWave.substr(0, pos_e);
                low_name += "_Fd" + to_string(attr->ClientFd) +
                "_ConId" + to_string(attr->ConnID) +
                "_" + to_string(FileStat.st_mtime) +
                ".low";
                wavePath_low += low_name;
                if (create_low_file && 0 != access(wavePath_low.c_str(), F_OK))
                {
                    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "recreate wave"<< std::endl;
                    //删除之前保存的文件
                    remove_low_file(tmpWave, attr->ClientFd, attr->ConnID);
                    //创建新的文件
                    make_file_dir(SCPI_LowWaveDir(), low_name);
                    iRet = WT_CreateLowWave(attr->ConnID, is_vsg, wavePath.c_str(), low_name.c_str());
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_CreateLowWave = " << iRet << std::endl;
                    if (iRet)
                    {
                        break;
                    }
                }
                else
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "not create wave"<< std::endl;
                }
            }

            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "wave path = " << wavePath << std::endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Low wave path = " << wavePath_low << std::endl;
            //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Low wave name = " << low_name << std::endl;

            /* 检查是否存在补偿后的low文件 */
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "create_low_file = " << create_low_file << std::endl;

            low_name_list.push_back(low_name);
        }

        iRet = WT_ERR_CODE_OK;
    } while (0);

    return iRet;
}

scpi_result_t SCPI_CreateJSONFile(scpi_t *context)
{
    char FileName[1024] = {0};
    char Buf[1024] = POWER_CORRECTION_TABLE;
    size_t FileNameLen = 0;
    Json::Value FileBufJson;
    double value;

    if (!SCPI_ParamCopyText(context, FileName, sizeof(FileName) - 1, &FileNameLen, true))
    {
        return SCPI_RES_ERR;
    }

    for (int i = 0; i < context->parser_state.numberOfParameters - 1; ++i)
    {
        if (!SCPI_ParamDouble(context, &value, true))
        {
            return SCPI_RES_ERR;
        }

        if (i % 2 == 0)
        {
            FileBufJson[i / 2]["Freq"] = value;
        }
        else
        {
            FileBufJson[i / 2]["Correction"] = value;
        }
    }

    strcat(Buf, FileName);
    WriteJson(Buf, FileBufJson);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_ParseJSONFile(scpi_t *context)
{
    char Buf[1024] = POWER_CORRECTION_TABLE;
    char FileName[1024] = {0};
    size_t FileNameLen = 0;
    std::ifstream Ifstream;
    Json::Value FileBufJson;
    Json::Reader JsonReader; // Json解析器对象
    int iRet = WT_OK;

    do
    {
        if (!SCPI_ParamCopyText(context, FileName, sizeof(FileName) - 1, &FileNameLen, true))
        {
            return SCPI_RES_ERR;
        }
        strcat(Buf, FileName);
        Ifstream.open(Buf);

        if (!Ifstream.is_open())
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "=== Ifstream open failed!" << std::endl;
            iRet = WT_CONF_FILE_ERROR;
            break;
        }

        //解析JSon文本
        if (!JsonReader.parse(Ifstream, FileBufJson))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "=== Ifstream parse failed!" << std::endl;
            iRet = WT_JSON_PARSE_FAILED;
            break;
        }

        if(iRet != WT_OK)
        {
            SCPI_ResultOK(context, iRet);
            break;
        }

        SCPI_ResultText(context, FileName);

        if (FileBufJson.isArray() && FileBufJson.size() > 0)
        {
            for (unsigned j = 0; j < FileBufJson.size(); j++)
            {
                SCPI_ResultDouble(context, FileBufJson[j]["Freq"].asDouble());
                SCPI_ResultDouble(context, FileBufJson[j]["Correction"].asDouble());
            }
        }
    } while (0);

    if (Ifstream.is_open())
    {
        Ifstream.close();
    }
    return SCPI_RES_OK;
}

scpi_result_t SCPI_UploadJSONFile(scpi_t *context)
{
    Json::Value FileBufJson;
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    do
    {
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }
        string dirName(fileName);
        int pos = dirName.find_last_of('/');
        if (pos != string::npos)
        {
            string cmd = string("mkdir -p ") + dirName.substr(0, pos);
            auto _ = system(cmd.c_str());
            (void)_;
        }
        iRet = WriteFile(fileName, data, len);

        if (iRet != WT_OK)
        {
            break;
        }

        //检查
        iRet = ParseJSONFile(fileName, FileBufJson);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

void remove_low_file(std::string filename, int ClientFd, int ConnID)
{
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << filename << std::endl;
    std::string path_pre = SCPI_LowWaveDir();
    std::string path = filename;
    std::string cmd;
    auto pos = path.find(path_pre);
    if (std::string::npos == pos)// without /tmp/low_wave/, add it
    {
        path = path_pre + path;
    }

    auto pos2 = path.find_last_of(".");
    if (std::string::npos != pos2)
    {
        path = path.substr(0, pos2);
    }

    cmd = "rm -rf ";
    cmd += "\"";
    cmd += path + "_Fd" + to_string(ClientFd) + "_ConId" + to_string(ConnID);
    cmd += "\"";
    cmd += "*.low";
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
    do_system_cmd(cmd);
}

std::string find_pre_save_low_file(std::string low_name)
{
    std::string cmd;
    std::string result;

    cmd = "find /tmp/low_wave/ -name ";
    cmd += low_name;
    cmd += " | head -n 1 | awk -F / '{print $NF}'";
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
    do_system_cmd(cmd, result);
    if(result.length() == 0)
    {
        cmd = "find /home/<USER>/bin/wave/ /tmp/wave/ -name ";
        cmd += low_name;
        cmd += " | head -n 1";
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
        do_system_cmd(cmd, result);
        if(result.length() > 0)
        {
            cmd = "cp ";
            cmd += result;
            cmd += " /tmp/low_wave/";
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
            do_system_cmd(cmd);
            size_t pos = result.find_last_of("/");
            if (pos != std::string::npos)
            {
                result = result.substr(pos + 1);
            }
        }
    }

    return result;
}

void remove_all_vsg_low(void *param)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(param);
    std::string result;
    std::string cmd;

    cmd = "find /tmp/low_wave/ -type f -name ";
    cmd += "*";
    cmd += "\"";
    cmd += +"_Fd" + to_string(attr->ClientFd) + "_ConId" + to_string(attr->ConnID);
    cmd += "\"";
    cmd += "*.low";
    cmd += " -exec rm -f {} \\;";
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << cmd << std::endl;
    do_system_cmd(cmd, result);
}

scpi_result_t GetVoltAgeArb(scpi_t *context)
{
    return GetVoltAgeInfo(context,true);
}

scpi_result_t GetVoltAge(scpi_t *context)
{
    return GetVoltAgeInfo(context);
}
scpi_result_t SetTestMode(scpi_t *context)
{
    int Ret = WT_OK;
    int ParamVal;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            Ret = SCPI_RES_ERR;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_TestMode = ParamVal;
        if (attr->m_TestMode == TESTMODE_STANDARD)
        {
            ParamVal = 0;
        }
        else if (attr->m_TestMode == TESTMODE_RD)
        {
            ParamVal = 2;
            attr->vsaAlzParam.analyzeParamWifi.ICISuppression = 2;
        }
        attr->vsaAlzParam.analyzeParamWifi.ICISuppression = ParamVal;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_TestMode = " << ParamVal << endl;
    } while (0);

    return SCPI_ResultOK(context, Ret);
}

scpi_result_t GetVoltAgeInfo(scpi_t *context, bool arb)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int VoltageCnt = 0;
    int Count = 0;
    SCPI_ParamInt(context, &VoltageCnt, false);
#define ACTUAL_VOLT_COUNT 100
    if (arb || 0 == VoltageCnt)
    {
        VoltageCnt = ACTUAL_VOLT_COUNT;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VoltageCnt=" << VoltageCnt << endl;

    std::unique_ptr<char[]> ResultBuf = nullptr;
    ResultBuf.reset(new (std::nothrow) char[(sizeof(DeviceVoltage))*VoltageCnt]);
    iRet = WT_GetVoltage(attr->ConnID, (DeviceVoltage *)ResultBuf.get(), VoltageCnt, &Count);

    IF_ERR_RETURN(iRet);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Count=" << Count << endl;

    if (!arb) //以字符串格式返回
    {

        SCPI_ResultInt(context, Count);
        for (int i = 0; i < Count; i++)
        {
            char VoltInfobuf[sizeof(DeviceVoltage)];
            memset(VoltInfobuf, 0, sizeof(DeviceVoltage));

            DeviceVoltage *TmpVoltInfo = (DeviceVoltage *)ResultBuf.get();
            DeviceVoltage VoltInfo = *(DeviceVoltage *)(TmpVoltInfo + i);
            //最后需要把\n去掉,暂时测试查看结果添加方便查看
            sprintf(VoltInfobuf, "%d,%d,%s,%s,%lf,%lf,%lf", VoltInfo.VoltChannel,
                                                            VoltInfo.MultVolt,
                                                            VoltInfo.Board,
                                                            VoltInfo.VoltChannelInfo,
                                                            VoltInfo.VoltValue,
                                                            VoltInfo.LowLimit,
                                                            VoltInfo.HighLimit);
            SCPI_ResultText(context, VoltInfobuf);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), (sizeof(DeviceVoltage))*Count);
    }

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetAuthenticationTK(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ParamVal = 0;
    const int max_cnt = 128;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->m_authentication_tk.clear();
    attr->m_authentication_tk.assign(max_cnt, 0);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < max_cnt; i++)
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        attr->m_authentication_tk[i] = (u8)(ParamVal & 0xFF);
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GetAuthenticationEncryptData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ParamVal = 0;
    const int max_cnt = 32;
    std::vector<u8>plaintext;
    std::vector<u8>MIC;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        plaintext.assign(max_cnt, 0);
        MIC.assign(max_cnt, 0);

        for (int i = 0; i < context->parser_state.numberOfParameters && i < max_cnt; i++)
        {
            if (!SCPI_ParamInt(context, &ParamVal, true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }
            plaintext[i] = (u8)(ParamVal & 0xFF);
        }

        IF_ERR_RETURN(iRet);

        iRet = WT_Authentication_with_tag(&attr->m_authentication_tk[0],
                    attr->m_authentication_tk.size(),
                    &plaintext[0],
                    plaintext.size(),
                    &MIC[0]);

        IF_ERR_RETURN(iRet);

        for (int i = 0; i < plaintext.size(); i++)
        {
            SCPI_ResultInt(context, (plaintext[i] & 0xFF));
        }

        for (int i = 0; i < MIC.size(); i++)
        {
            SCPI_ResultInt(context, (MIC[i] & 0xFF));
        }

    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t SetTimerEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Enable = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Enable, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_WTTimer.SetTimerEnable(Enable);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetCommandUsedTime(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        std::string str = attr->m_WTTimer.GetCommandUsedTime();
        SCPI_ResultText(context, str.c_str());
    } while (0);
    return SCPI_RES_OK;
}

scpi_result_t SetFullDuplexEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int FullDuplexEnable = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    SCPI_ParamInt(context, &FullDuplexEnable, false);

    Ret = WT_SetDuplexEnable(attr->ConnID, FullDuplexEnable);
    IF_ERR_RETURN(Ret);
    attr->m_fullDuplexEnable = FullDuplexEnable;

    return SCPI_ResultOK(context);
}

scpi_result_t GetFullDuplexEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int FullDuplexEnable = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    Ret = WT_GetDuplexEnable(attr->ConnID, &FullDuplexEnable);
    IF_ERR_RETURN(Ret);
    attr->m_fullDuplexEnable = FullDuplexEnable;

    SCPI_ResultInt(context, FullDuplexEnable);
    return SCPI_RES_OK;
}

scpi_result_t SetFullDuplexVsaNoiseCompFlag(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int Flag = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    SCPI_ParamInt(context, &Flag, false);

    Ret = WT_SetDuplexVsaNoiseCompFlag(attr->ConnID, Flag);
    IF_ERR_RETURN(Ret);

    return SCPI_ResultOK(context);
}

scpi_result_t GetFullDuplexVsaNoiseCompFlag(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int Flag = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    Ret = WT_GetDuplexVsaNoiseCompFlag(attr->ConnID, &Flag);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, Flag);
    return SCPI_RES_OK;
}
