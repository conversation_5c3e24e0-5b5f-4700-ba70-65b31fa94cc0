/*
 * scpi_monitor.h
 *
 *  Created on: 2021-12-22
 *      Author: Administrator
 */

#ifndef SCPI_MONITOR_H_
#define SCPI_MONITOR_H_

#include <list>
#include <mutex>
#include <string>
#include <vector>
#include <queue>
#include <set>
#include <memory>
#include <condition_variable>
#include "TesterCommon.h"
#include "internal.h"
#include "scpi/types.h"
#include "scpi/scpi.h"
#include "basehead.h"

class Monitor
{
public:
    Monitor(scpi_t *Context);
    ~Monitor();

    //*****************************************************************************
    // 设置监视的对象
    // 参数[IN]: Obj : 监视对象（监视的rf端口）
    // 返回值: 成功或错误码
    //*******************************************l**********************************
    int SetMonObj(int Obj);

    //*****************************************************************************
    // 设置监视机需要监视的数据
    // 参数[IN]: Action: 0-删除监视项 1: 添加监视项
    //           Type : 监视类型，取值见WT_MONITOR_TYPE
    //       VsaResult: 监视机需要获取的结果命令列表，监视类型为VSA结果时有效
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetMonData(int Action, int Type, const std::vector<std::string> &VsaResult);

    //*****************************************************************************
    // 是否监视指定的RF端口
    // 参数[IN]: PortId : RF端口
    // 返回值: true or false
    //*****************************************************************************
    bool IsMonPort(int PortId);

    //*****************************************************************************
    // 获取监视机所需要的VSA结果列表
    // 参数: 无
    // 返回值: 结果列表
    //*****************************************************************************
    std::set<std::string> &GetVsaResult(void);

    //解析监视机的scpi命令，并返回分析结果给监视机，返回内容：int Type类型+ cmd + scpi的返回结果
    void ParseMonitorCmd(int ConnId);

    //*****************************************************************************
    // 发送配置参数到监视机（vsa配置、vsg配置等）
    // 参数[IN]: Param : 配置参数
    //             Len : 参数长度
    //            Type : 结果类型，见枚举WT_MONITOR_TYPE
    // 返回值: 无
    //*****************************************************************************
    void SendParam(const void *Param, int Len, int Type);

    //*****************************************************************************
    // 发送VSG的PN配置信息到监视机
    // 参数[IN]: PnHeadInfo : PN头数据
    // 参数[IN]: PnInfo     : PN项数据
    // 返回值: 无
    //*****************************************************************************
    void SendPnParam(std::vector<VsgPattern> &PnPattern, std::vector<PnItemHead_API> &PnHead);
    
    //*****************************************************************************
    // 发送VSA的分析参数到监视机
    // 参数[IN]: Param : 分析参数
    //             Len : 参数长度
    // 返回值: 无
    //*****************************************************************************
    void SendVsaAlzParam(int AlzType, const void *CommonAlzParam, int ComonParamLen, const void *TechAlzParam, int AlzParamLen);

    //获取监听连接对外的fd
    int GetMonClientFd();

    void SendScpiCmd(const char *Data, int DataLen);
private:
    int SCPIParseCmd(const char *Data, int DataLen);//监听解析监听命令项处理

    void SendDataThread(); //发送监听命令线程
private:
    int m_Running;                       // 是否正在运行,用于析构时线程退出
    int m_RFPort = 0;                       //监视的RF端口号
    std::mutex m_Mutex;                  // 用于向客户端发送数据
    std::mutex m_VsaResultMutex;         // 用于禁止多线程调用ParseMonitorCmd
    std::mutex m_ReciveMutex;            // 用于操作m_VsaResult
    std::mutex m_ScpiCmdMutex;           // 用于操作m_CmdQueue
    int m_Types[WT_MONITOR_MAX];         // 监视的数据类型
    std::set<std::string> m_VsaResult;   // 监视机需要的VSA结果
    std::mutex m_CmdMutex;               // 用于线程唤醒
    std::condition_variable m_CmdConVar; // 用于线程唤醒
    std::queue<std::string> m_CmdQueue;  // 待发送CMD队列

    SPCIUserParam m_UserParam; // 用户输入param，主要是传入api使用
    std::unique_ptr<char[]> m_scpi_input_buffer;
    scpi_reg_val_t m_scpi_regs[SCPI_REG_COUNT];
    char m_scpi_error_queue[4096];
    scpi_t m_ScpiContextObj;
    scpi_t *m_ScpiContext;
};

//监视连接管理类
class MonitorMgr
{
public:
    static MonitorMgr &Instance()
    {
        static MonitorMgr Mgr;
        return Mgr;
    }

    //*****************************************************************************
    // 获取监视机
    // 参数[IN]: Port:监听的端口
    // 返回值: 监视机指针或空指针
    //*****************************************************************************
    std::shared_ptr<Monitor> GetMonitorByPort(int Port);

    //*****************************************************************************
    // 获取监视机
    // 参数[IN]: Fd:客户端socket的描述符
    // 返回值: 监视机指针或空指针
    //*****************************************************************************
    std::shared_ptr<Monitor> GetMonitorByClientFd(int Fd);

    //*****************************************************************************
    // 添加新的监视机连接
    // 参数[IN]: Context : scpi_t对象，配置监听项和发送监听项会同时存在，监听context和主连接的不能是同一个，要不然有共用，此处仅拷贝
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AddMonitor(scpi_t *Context);

    //*****************************************************************************
    // 删除监视机
    // 参数[IN]: fd : 监视机的scpi对象
    // 返回值: 无
    //*****************************************************************************
    void DelMonitor(int Fd);

    //*****************************************************************************
    // SCPI命令监听过滤器
    // 参数[IN]: Data：SCPI命令地址， DataLen：SCPI命令地址
    // 返回值: 0:保留 1:忽略
    //*****************************************************************************
    int ScpiCmdIgnor(const std::string &ScpiCmd);

    // delete copy and move constructors and assign operators
    MonitorMgr(MonitorMgr const&) = delete;             // Copy construct
    MonitorMgr(MonitorMgr&&) = delete;                  // Move construct
    MonitorMgr& operator=(MonitorMgr const&) = delete;  // Copy assign
    MonitorMgr& operator=(MonitorMgr &&) = delete;      // Move assign

private:
    MonitorMgr();
    ~MonitorMgr() {}

private:
    std::mutex m_Mutex;
    std::list<std::shared_ptr<Monitor>> m_Monitors;
    std::vector<std::string> m_MoniCmdIgnor;
};
#endif /* SCPI_MONITOR_H_ */
