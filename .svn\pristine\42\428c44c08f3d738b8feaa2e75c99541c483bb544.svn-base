TARGET = WT-Scpi-Server

APIDIR = $(TOPDIR)/../api
MATLABDIR = "$(APIDIR)/Dependency DLLs/mat_linux_lib"
APISODIR = $(APIDIR)/build/lib

API_INCLUDE = $(APIDIR)/Include
INTERNAL_INCLUDE = $(APIDIR)/Include/internal
ENC_INCLUDE = $(APIDIR)/WT.Tester.API.MAC.Encryption

CFLAGS_INCLUDE = -I./ -I$(TOPDIR)/general -I$(TOPDIR)/filesecure -I$(TOPDIR)/../extlib/include -I./include
CFLAGS_INCLUDE += -I$(API_INCLUDE) -I$(INTERNAL_INCLUDE) -I$(ENC_INCLUDE)

CFLAGS += -DLINUX -DLINUX_SCPI
CFLAGS += -I./ -I$(TOPDIR)/general -I$(TOPDIR)/filesecure -I$(TOPDIR)/../extlib/include -I./include 
CFLAGS += -I$(API_INCLUDE) -I$(INTERNAL_INCLUDE) -I$(ENC_INCLUDE)

LDFLAGS += -L$(TOPDIR)/bin -lgeneral -lfilesecure -lev -pthread \
	-L./lib -lSCPI \
	-L$(APISODIR) \
	-lWT.Tester.API.Common \
	-lWT.Tester.API.IOControl \
	-lWT.Tester.API.PNFileProcess \
	-lWT.Tester.API.WT4XXWrapper \
	-lWT.Tester.API \
	-lWT.Tester.API.MAC.Encryption \
	-L$(MATLABDIR) -lm -Wl,-rpath,-L$(APISODIR):$(MATLABDIR)

include $(TOPDIR)/base.mk