SRC_DIR = $(shell pwd)
OBJ_DIR = objs

SRCS = $(wildcard $(shell find $(SRC_DIR) -name "*.cpp"))
OBJS = $(patsubst $(SRC_DIR)/%.cpp, $(OBJ_DIR)/%.o, $(SRCS))
DEPS = $(patsubst %.o, %.d, $(OBJS))

-include $(DEPS)

$(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp
	@[ ! -e $(dir $@) ] &  mkdir -p $(dir $@)
	$(CXX) $(CFLAGS) -MF "$(@:%.o=%.d)" -MT "$@" -c $< -o $@

$(OUTDIR)/$(TARGET): $(OBJS)
	$(CXX) -o $@ $^ $(DATEOBJ) $(LDFLAGS)

ifneq ($(RELEASE), 1)
all: $(OUTDIR)/$(TARGET)
else
all: $(OUTDIR)/$(TARGET)
	objcopy --only-keep-debug $(OUTDIR)/$(TARGET) $(OUTDIR)/$(TARGET).debug
	objcopy --strip-debug $(OUTDIR)/$(TARGET)
endif

clean:
	-@rm $(OBJ_DIR) -r -f

check:
	#echo $(CFLAGS_INCLUDE)
	@for path in $(SRCS); do \
		#echo $(CFLAGS_INCLUDE); \
		#echo $$path; \
		#echo ${PWD}; \
		echo cppcheck --std=c++11 --platform=native --enable=warning --force -UEV_H -DDEBUG -DWT300 -DCOVERAGE -I$${PWD} $(CFLAGS_INCLUDE) $$path; \
		cppcheck --std=c++11 --platform=native --enable=warning --force -UEV_H -DDEBUG -DWT300 -DCOVERAGE -I$${PWD} $(CFLAGS_INCLUDE) $$path; \
	done

.PHONY: all clean check
