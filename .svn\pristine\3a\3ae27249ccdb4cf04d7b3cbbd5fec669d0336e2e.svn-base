//*****************************************************************************
//  File: wtlog.cpp
//  日志模块
//  Data: 2025.2.25
// 1、错误日志所使用的表名称为 "error"，表结构项如下：
// utc | time | file name | line | level | error code | content
// utc为UTC时间，精确到us, time是时间字符串格式为：yy-mm-dd hh-mm-ss
//*****************************************************************************
#include "wtlog.h"
#include <sys/time.h>
#include <sys/inotify.h>
#include <sstream>
#include <iostream>
#include <ctime>
#include <cstring>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <sys/types.h>
#include <stdarg.h>
#include <future>

#include "wterror.h"
#include "conf.h"
#include "basefun.h"

using namespace std;

std::string WTLog::m_LogDbName;
std::string WTLog::m_LogProcessName;

namespace level {
    Config config; // 实际存储配置数据
}

// sqlite3优化解决方案:
// 1) 启动时后，尝试删除旧数据, 如果有删除动作，则压缩一次数据库(比较耗时, 待定)
// 2) 删除动作包括2种, a: 根据条目数, 保留最近100000条; b: 根据, 保留1个月的记录。
// 3) 启动删除数据后, 会查询数据表的记录, 并保存在内存中, 数据表每插入一条数据，计数器加1，达到门限值(待定), 则触发异步事件
// 4) 异步事件激活后, 回调检查所有数据表条目计数器, 超过门限值表, 就根据条目清理数据, 并重新开始计数(数据表条目数)
// 5) 以上优化性能已经满足, 其他优化暂不考虑.
//    事务: BEGIN, COMMIT ;
//    缓存大小 PRAGMA cache_size;
//    磁盘同步 PRAGMA synchronous = OFF;

// 依据条目删除方案两种:
// a) 已知条目数, 删除多余的条码, 比如101000条(已知)删除1000条 大约0.16s
// delete from operator where utc in (select utc from operator order by utc limit 1000)
// b) 未知条码数, 保留指定条目删除其它, 比如101000条(此数未知) 保留10000条, 其他删除大约0.43s.
// delete from operator where utc not in (select utc from operator order by utc desc limit 100000)
// 考虑到效率问题, 选用方案a. 这种方案要对数据表记录数实时计数.

// 临时打开的数据库类
class DbGard
{
public:
    DbGard(const string &File, sqlite3 *Default) : m_Db(Default), m_Default(Default)
    {
        if (!File.empty())
        {
            if (sqlite3_open(File.c_str(), &m_Db) != SQLITE_OK)
            {
                m_Db = m_Default;
            }
        }
    }

    ~DbGard()
    {
        if (m_Db != m_Default)
        {
            sqlite3_close_v2(m_Db);
            // m_Db = nullptr;
        }
    }

    sqlite3 *operator()() {return m_Db;}

private:
    sqlite3 *m_Db, *m_Default;
};

DBManager::DBManager(const std::string &dbPath)
{
    if (dbPath.empty()) return;

    static std::once_flag init_flag;
    std::call_once(init_flag, [&]() {
        m_DbPath = dbPath;
        InitDB();
    });
}

DBManager::~DBManager()
{
    if (m_IsOpen)
    {
        // 确保最后的批次被写入
        ExecuteBatch();
        
        if (m_InsertStmt) {
            sqlite3_finalize(m_InsertStmt);
        }

        if (m_Db) {
            sqlite3_close_v2(m_Db);
            m_Db = nullptr;
        }

        m_IsOpen = false;
    }
}

void DBManager::InitDB()
{
    if (m_DbPath.empty()) {
        return;
    }

    // 预分配批处理缓冲区
    m_BatchRecords.reserve(level::Config::batch_size);

    if (!OpenDatabase()) {
        return;
    }

    if (!ConfigureDatabase()) {
        return;
    }

    if (!InitializeTable()) {
        return;
    }

    if (!PrepareStatements()) {
        return;
    }

    m_IsOpen = true;
    printf("database[%s] init success, log count[%d]\n", m_DbPath.c_str(), m_LogCount);
}

bool DBManager::OpenDatabase()
{
    std::lock_guard<std::mutex> lock(m_dbMutex);
    if (sqlite3_open_v2(m_DbPath.c_str(), &m_Db, 
        SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE, NULL) != SQLITE_OK) {
        printf("Open database[%s] failed\n", m_DbPath.c_str());
        return false;
    }
    return true;
}

bool DBManager::ConfigureDatabase()
{
    // SQLite优化配置
    static const struct {
        const char* pragma;
        const char* description;
    } pragmas[] = {
        {"PRAGMA synchronous = NORMAL",     "折衷方案：每个事务写盘但不fsync"},
        {"PRAGMA journal_mode = WAL",       "使用WAL模式"},
        {"PRAGMA cache_size = 10000",       "增加缓存大小到10MB"},
        {"PRAGMA wal_autocheckpoint = 100", "每100页自动触发检查点"},
        {"PRAGMA busy_timeout = 5000",      "避免EXCLUSIVE锁阻塞"}
    };
    
    char* errmsg = nullptr;
    for (const auto& pragma : pragmas) {
        if (sqlite3_exec(m_Db, pragma.pragma, nullptr, nullptr, &errmsg) != SQLITE_OK) {
            printf("Failed to set %s: %s\n", pragma.pragma, errmsg);
            sqlite3_free(errmsg);
            return false;
        }
    }
    return true;
}

bool DBManager::InitializeTable()
{
    if (!IsTableExist(level::Config::log_tbl)) {
        if (!CreateTable(level::Config::log_tbl)) {
            return false;
        }
    } else {
        CleanLogByDate(level::Config::log_tbl);
    }

    // 更新日志计数并清理过期记录
    m_LogCount = GetLogCount(level::Config::log_tbl);
    if (m_LogCount > level::Config::keep_records) {
        CleanLogByCount(level::Config::log_tbl, 
            (m_LogCount - level::Config::keep_margin * 0.9));
        m_LogCount = GetLogCount(level::Config::log_tbl);
    }
    
    return true;
}

bool DBManager::PrepareStatements()
{
    std::string sql = std::string("INSERT INTO ") + level::Config::log_tbl +
                     " VALUES (?, ?, ?, ?, ?, ?, ?);";
                     
    if (sqlite3_prepare_v2(m_Db, sql.c_str(), -1, &m_InsertStmt, NULL) != SQLITE_OK) {
        printf("Failed to prepare insert statement: %s\n", sqlite3_errmsg(m_Db));
        return false;
    }
    return true;
}

bool DBManager::IsTableExist(const std::string& tableName)
{
    string Sql = "SELECT COUNT(*) FROM sqlite_master WHERE TYPE='table' AND NAME='" + tableName + "';";
    sqlite3_stmt *stm;
    sqlite3_prepare(m_Db, Sql.c_str(), Sql.length(), &stm, NULL);
    sqlite3_step(stm);

    int count = sqlite3_column_int(stm, 0);
    sqlite3_finalize(stm);

    return count > 0;
}

// 表结构: utc | time | file name | line | level | error code | content
bool DBManager::CreateTable(const std::string& tableName)
{
    if (m_Db == nullptr)
    {
        return false;
    }

    std::string sql = "CREATE TABLE IF NOT EXISTS " + tableName \
        + " (utc INTEGER, time TEXT, file TEXT, line INTEGER, level TEXT, error_code INTEGER, content TEXT);";
    char *Errmsg = nullptr;
    sqlite3_exec(m_Db, sql.c_str(), NULL, NULL, &Errmsg);
    if (Errmsg != nullptr)
    {
        printf("CreateTable[%s] failed, errmsg[%s]\n", tableName.c_str(), Errmsg);
        sqlite3_free(Errmsg);
        return false;
    }
    return true;
}

// 表结构: utc | time | file name | line | level | error code | content
void DBManager::WriteToDB(const LogRecord& record)
{
    if (!IsOpen() || record.content.empty())
    {
        return;
    }

    m_BatchRecords.emplace_back(std::move(record));
    
    // 当积累足够多的记录时执行批量写入
    if (m_BatchRecords.size() >= level::Config::batch_size)
    {
        ExecuteBatch();
    }
}

void DBManager::ExecuteBatch()
{
    if (m_BatchRecords.empty()) return;

    std::lock_guard<std::mutex> lock(m_dbMutex);
    
    // 开始事务
    char *errmsg = nullptr;
    if (sqlite3_exec(m_Db, "BEGIN TRANSACTION", nullptr, nullptr, &errmsg) != SQLITE_OK)
    {
        printf("Failed to begin transaction: %s\n", errmsg);
        sqlite3_free(errmsg);
        return;
    }

    struct timeval tv;
    bool success = true;
    
    for (const auto& record : m_BatchRecords)
    {
        // 每次绑定前先清除之前的绑定
        sqlite3_clear_bindings(m_InsertStmt);
        sqlite3_reset(m_InsertStmt);

        gettimeofday(&tv, NULL);

        // 格式化时间
        struct tm timeinfo;
        localtime_r(&record.time, &timeinfo);
        char timeStr[32];
        strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &timeinfo);

        int rc = SQLITE_OK;
        rc = sqlite3_bind_int64(m_InsertStmt, 1, tv.tv_sec * 1000000 + tv.tv_usec);
        rc |= sqlite3_bind_text(m_InsertStmt, 2, timeStr, -1, SQLITE_TRANSIENT);
        rc |= sqlite3_bind_text(m_InsertStmt, 3, record.filename, -1, SQLITE_TRANSIENT);
        rc |= sqlite3_bind_int(m_InsertStmt, 4, record.line);
        rc |= sqlite3_bind_text(m_InsertStmt, 5, level::ToString(record.level), -1, SQLITE_TRANSIENT);
        rc |= sqlite3_bind_int(m_InsertStmt, 6, record.errcode);
        rc |= sqlite3_bind_text(m_InsertStmt, 7, record.content.c_str(), -1, SQLITE_TRANSIENT);

        if (rc != SQLITE_OK)
        {
            printf("Binding failed: %s\n", sqlite3_errmsg(m_Db));
            success = false;
            break;
        }

        rc = sqlite3_step(m_InsertStmt);
        if (rc != SQLITE_DONE)
        {
            printf("WriteToDB[%s] failed in batch, rc=%d, errmsg[%s]\n",
                   level::Config::log_tbl, rc, sqlite3_errmsg(m_Db));
            success = false;
            break;
        }

        m_LogCount++;
    }

    // 最后一次重置语句
    sqlite3_reset(m_InsertStmt);

    // 提交或回滚事务
    if (success)
    {
        if (sqlite3_exec(m_Db, "COMMIT", nullptr, nullptr, &errmsg) != SQLITE_OK)
        {
            printf("Failed to commit transaction: %s\n", errmsg);
            sqlite3_free(errmsg);
            // 如果提交失败，尝试回滚
            sqlite3_exec(m_Db, "ROLLBACK", nullptr, nullptr, nullptr);
            success = false;
        }
    }
    else
    {
        if (sqlite3_exec(m_Db, "ROLLBACK", nullptr, nullptr, &errmsg) != SQLITE_OK)
        {
            printf("Failed to rollback: %s\n", errmsg);
            sqlite3_free(errmsg);
        }
    }

    // 如果事务成功，清空批处理记录
    if (success)
    {
        m_BatchRecords.clear();

        // 检查是否需要清理日志
        if (m_LogCount > level::config.keep_records)
        {
            CleanLogByCount(level::Config::log_tbl, m_LogCount - level::Config::keep_margin * 0.9);
            m_LogCount = GetLogCount(level::Config::log_tbl);
        }
    }
    else
    {
        // 事务失败，重置计数
        m_LogCount = GetLogCount(level::Config::log_tbl);
        printf("ExecuteBatch[%s] failed, log count[%d]\n", level::Config::log_tbl, m_LogCount);
    }
    m_BatchRecords.clear();
}

void DBManager::CleanLogByCount(const std::string& tableName, int count)
{
    if (m_Db == nullptr || count <= 0)
    {
        return;
    }

    std::string sql = "DELETE FROM " + tableName +
                      " WHERE utc IN (SELECT utc FROM " + tableName +
                      " ORDER BY utc ASC LIMIT " + std::to_string(count) + ");";

    char *Errmsg = nullptr;
    if (sqlite3_exec(m_Db, sql.c_str(), NULL, NULL, &Errmsg) != SQLITE_OK)
    {
        printf("CleanLogByCount[%s] failed, errmsg[%s]\n", tableName.c_str(), Errmsg);
        sqlite3_free(Errmsg);
    }
}

void DBManager::CleanLogByDate(const std::string& tableName)
{
    std::string sql = "DELETE FROM " + tableName +
                      " WHERE utc < CAST(strftime('%s', date('now', 'start of day', '-1 month')) AS INTEGER) * 1000000;";

    char *Errmsg = nullptr;
    if (sqlite3_exec(m_Db, sql.c_str(), NULL, NULL, &Errmsg) != SQLITE_OK)
    {
        printf("CleanLogByDate[%s] failed, errmsg[%s]\n", tableName.c_str(), Errmsg);
        sqlite3_free(Errmsg);
    }
}

int DBManager::GetLogCount(const std::string& tableName)
{
    if (m_Db == nullptr) return 0;

    std::string sql = "SELECT COUNT(*) FROM " + tableName + ";";
    sqlite3_stmt* stmt = nullptr;
    int count = 0;

    if (sqlite3_prepare_v2(m_Db, sql.c_str(), -1, &stmt, nullptr) == SQLITE_OK)
    {
        if (sqlite3_step(stmt) == SQLITE_ROW)
        {
            count = sqlite3_column_int(stmt, 0);
        }
        sqlite3_finalize(stmt);
    }
    else
    {
        printf("GetLogCount[%s] failed: %s\n", tableName.c_str(), sqlite3_errmsg(m_Db));
    }

    return count;
}

WTLogStream WTLog::GettmpLogStream(int LogType)
{
    return WTLogStream(this, LogType);
}

WTLog::WTLog(void)
{
    m_confPath = WTConf::GetDir() + "/base.conf"; // 存储配置文件路径
    ReloadConfig();
    StartInotifyWatcher();

    m_Isrunning = true;
    if (!m_LogDbName.empty())
    {
        m_DbManager.reset(new DBManager(m_LogDbName));
    }
    m_Logthread = std::thread(&WTLog::LogConsumerThread, this);
}

WTLog::~WTLog()
{
    m_Isrunning = false;
    m_Logqueue.enqueue(LogRecord{static_cast<int>(level::Type::shutdown)});

    if (m_Logthread.joinable())
    {
        m_Logthread.join();
    }
}

void WTLog::LogConsumerThread(void)
{
    constexpr size_t BATCH_SIZE = 100;
    LogRecord records[BATCH_SIZE];
    
    while (true)
    {
        // 优先检查运行状态
        if (!m_Isrunning) {
            ProcessRemainLog();
            break;
        }

        // 带超时的等待（100ms）
        size_t count = m_Logqueue.wait_dequeue_bulk_timed(records, BATCH_SIZE, 
                                                         static_cast<std::int64_t>(100000));
        
        // 处理日志记录
        if (count > 0) {
            ProcessRecords(records, count);
        }
    }
}

void WTLog::ProcessRecords(LogRecord* records, size_t count)
{
    static thread_local char buffer[256 * 1024];  // 256KB缓冲区
    static thread_local char timeStr[32];
    int total_len = 0;

    for (size_t i = 0; i < count; ++i)
    {
        if (records[i].level == static_cast<int>(level::Type::shutdown))
        {
            m_Isrunning = false; // 立即终止
            return;
        }

        if (!level::IsPrintable(records[i].level))
        {
            continue;
        }

        // 格式化时间
        struct tm timeinfo;
        localtime_r(&records[i].time, &timeinfo);
        strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &timeinfo);

        // 预估日志长度
        size_t estimated_len = strlen(timeStr) + m_LogProcessName.length() +
                               strlen(level::ToString(records[i].level)) +
                               records[i].content.length() + 50; // 50是格式化字符的预估长度

        if (records[i].errcode != 0)
        {
            estimated_len += strlen(records[i].filename) + 20; // 额外的错误信息长度
        }

        // 如果缓冲区不够，先写入日志
        if (total_len + estimated_len >= sizeof(buffer) - 128)
        {
            if (total_len > 0)
            {
                ssize_t written = write(STDOUT_FILENO, buffer, total_len);
                if (written < 0)
                {
                    fprintf(stderr, "Error writing to stdout: %s\n", strerror(errno));
                }
                else if (written < total_len)
                {
                    // 处理部分写入的情况
                    memmove(buffer, buffer + written, total_len - written);
                    total_len -= written;
                }
                else
                {
                    total_len = 0;
                }
            }
        }

        // 格式化日志
        int len = 0;
        if (records[i].errcode != 0)
        {
            len = snprintf(buffer + total_len, sizeof(buffer) - total_len,
                           "[%s] [%s] [%s] %s:%d error code: %d %s\n",
                           timeStr,
                           m_LogProcessName.c_str(),
                           level::ToString(records[i].level),
                           records[i].filename,
                           records[i].line,
                           records[i].errcode,
                           records[i].content.c_str());
        }
        else
        {
            len = snprintf(buffer + total_len, sizeof(buffer) - total_len,
                           "[%s] [%s] [%s] %s\n",
                           timeStr,
                           m_LogProcessName.c_str(),
                           level::ToString(records[i].level),
                           records[i].content.c_str());
        }

        // 如果格式化失败或缓冲区不够，直接写入日志
        if (len < 0 || total_len + len >= static_cast<int>(sizeof(buffer)))
        {
            if (total_len > 0)
            {
                ssize_t written = write(STDOUT_FILENO, buffer, total_len);
                if (written < 0)
                {
                    fprintf(stderr, "Error writing to stdout: %s\n", strerror(errno));
                }
                else if (written < total_len)
                {
                    // 处理部分写入的情况
                    memmove(buffer, buffer + written, total_len - written);
                    total_len -= written;
                }
                else
                {
                    total_len = 0;
                }
            }
            ssize_t written = write(STDOUT_FILENO, buffer, len);
            if (written < 0)
            {
                fprintf(stderr, "Error writing to stdout: %s\n", strerror(errno));
            }
        }
        else
        {
            total_len += len;
        }
    }

    // 刷新剩余的日志
    if (total_len > 0)
    {
        ssize_t written = write(STDOUT_FILENO, buffer, total_len);
        if (written < 0)
        {
            fprintf(stderr, "Error writing to stdout: %s\n", strerror(errno));
        }
        else if (written < total_len)
        {
            // 处理部分写入的情况
            memmove(buffer, buffer + written, total_len - written);
            total_len -= written;
        }
        else
        {
            total_len = 0;
        }
    }

    // 写入数据库
    if (m_DbManager)
    {
        for (size_t i = 0; i < count; ++i)
        {
            if (level::IsSavable(records[i].level))
            {
                m_DbManager->WriteToDB(records[i]);
            }
        }
    }
}

void WTLog::ProcessRemainLog()
{
    constexpr size_t BATCH_SIZE = 100;
    LogRecord records[BATCH_SIZE];

    while (true) {
        // 先尝试批量取出
        size_t count = m_Logqueue.try_dequeue_bulk(records, BATCH_SIZE);
        if (count > 0) {
            for (size_t i = 0; i < count; ++i) {
                if (level::IsPrintable(records[i].level)) {
                    PrintLog(records[i]);
                }

                if (level::IsSavable(records[i].level) && m_DbManager) {
                    m_DbManager->WriteToDB(records[i]);
                }
            }
            continue;
        }
        
        // 确实没有日志了才退出
        break;
    }
}

void WTLog::PrintLog(const LogRecord &record)
{
    std::ostringstream oss;
    char timeStr[32];
    struct tm *timeinfo = localtime(&record.time);
    strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", timeinfo);
    oss << "[" << timeStr << "]";
    oss << " [" << m_LogProcessName << "]";
    oss << " [" << level::ToString(record.level) << "] ";
    if (record.errcode != 0)
    {
        oss << record.filename << ":" << std::dec << record.line << " ";
        oss << "ErrCode: " << record.errcode << " ";
    }
    oss << record.content;
    printf("%s\n", oss.str().c_str());
}

void WTLog::ReloadConfig()
{
    WTConf Conf(m_confPath);
    const auto old_print = level::config.print;
    const auto old_save = level::config.save;

    Conf.GetItemVal(level::Config::print_log_level, level::config.print);
    Conf.GetItemVal(level::Config::save_log_level, level::config.save);

    if (old_print != level::config.print || old_save != level::config.save)
    {
        using level::Type;
        printf("[%s] log level updated: Print[%s]->[%s], Save[%s]->[%s]\n",
               m_LogProcessName.c_str(),
               level::ToString(old_print),
               level::ToString(level::config.print),
               level::ToString(old_save),
               level::ToString(level::config.save));
    }
}

void WTLog::StartInotifyWatcher()
{
    // 创建非阻塞的 inotify 实例
    int inotify_fd = inotify_init1(IN_NONBLOCK);
    if (inotify_fd < 0)
    {
        wtlog::error(SOURCE_LOCATION, errno, "inotify_init1 failed");
        return;
    }
    // 添加监控 watch，监控配置文件的修改和写关闭事件
    int wd = inotify_add_watch(inotify_fd, m_confPath.c_str(), IN_MODIFY | IN_CLOSE_WRITE | IN_MOVED_TO);
    if (wd < 0)
    {
        wtlog::error(SOURCE_LOCATION, errno, "inotify_add_watch failed for %s", m_confPath.c_str());
        close(inotify_fd);
        return;
    }

    // 启动一个线程专门处理 inotify 事件
    m_Isrunning = true;
    std::thread([this, inotify_fd, wd]()
                {
        constexpr size_t event_size = sizeof(struct inotify_event);
        constexpr size_t buf_len = 1024 * (event_size + 16);
        char buffer[buf_len];

        static auto last_reload = std::chrono::steady_clock::now() - std::chrono::milliseconds(600);

        while (m_Isrunning) {
            fd_set fds;
            struct timeval tv;

            FD_ZERO(&fds);
            FD_SET(inotify_fd, &fds);
            tv.tv_sec = 1;  // 1秒超时
            tv.tv_usec = 0;

            int ret = select(inotify_fd + 1, &fds, NULL, NULL, &tv);
            if (ret < 0) {
                if (errno == EINTR) continue;
                wtlog::error(SOURCE_LOCATION, errno, "select error");
                break;
            }

            if (ret == 0) continue; // 超时

            ssize_t len = read(inotify_fd, buffer, buf_len);
            if (len < 0) {
                if (errno == EAGAIN) continue;
                wtlog::error(SOURCE_LOCATION, errno, "read error");
                break;
            }

            for (char *ptr = buffer; ptr < buffer + len; ) {
                struct inotify_event *event = reinterpret_cast<struct inotify_event *>(ptr);
                if (event->mask & (IN_MODIFY | IN_CLOSE_WRITE | IN_MOVED_TO)) {
                    auto now = std::chrono::steady_clock::now();
                    
                    if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_reload).count() > 500) {
                        std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 确保写入完成
                        ReloadConfig();
                        last_reload = now;
                    }
                }
                ptr += event_size + event->len;
            }
        }

        // 清理 watch 和 inotify_fd
        inotify_rm_watch(inotify_fd, wd);
        close(inotify_fd); })
        .detach();
}

int WTLog::Query(const string &File, const std::string SqlStr, std::vector<std::string> &Content)
{
    // not working for now
    if (m_Db == nullptr)
    {
        return WT_ERROR;
    }

    DbGard Db(File, m_Db);

    unique_lock<mutex> Lock(m_Mutex);

    char *Errmsg = nullptr;
    char **Data = nullptr;
    int Row, Column;
    int Ret = sqlite3_get_table(Db(), SqlStr.c_str(), &Data, &Row, &Column, &Errmsg);
    if (Ret != SQLITE_OK)
    {
        if(Errmsg != nullptr)
        {
            cout << Errmsg << endl;
            sqlite3_free(Errmsg);
        }
        return Ret;
    }

    Lock.unlock();

    string tmp;
    int Index = Column;  //第一行为表头，需要跳过
    for (int i = 0; i < Row; i++)
    {
        tmp.clear();
        Index++;  //第一列为utc值，跳过

        for (int j = 1; j < Column; j++)
        {
            tmp += Data[Index++];
            if (j != Column - 1)
            {
                tmp += ",";
            }
        }
        tmp += "\n\r";

        Content.push_back(tmp);
    }

    if(Data != nullptr)
    {
        sqlite3_free_table(Data);
    }
    return WT_OK;
}

int WTLog::Query(long BeginTime, long EndTime, vector<string> &Content, const string &File)
{
    // not working for now
    if (m_Db == nullptr)
    {
        return WT_ERROR;
    }

    DbGard Db(File, m_Db);

    ostringstream Strs;
    Strs << "SELECT * FROM " << level::Config::log_tbl << " WHERE utc >= " << BeginTime << " AND utc <= " << EndTime << ";";

    unique_lock<mutex> Lock(m_Mutex);

    char *Errmsg = nullptr;
    char **Data = nullptr;
    int Row, Column;
    int Ret = sqlite3_get_table(Db(), Strs.str().c_str(), &Data, &Row, &Column, &Errmsg);
    if (Ret != SQLITE_OK)
    {
        if(Errmsg != nullptr)
        {
            cout << Errmsg << endl;
            sqlite3_free(Errmsg);
        }
        return Ret;
    }

    Lock.unlock();

    string tmp;
    int Index = Column;  //第一行为表头，需要跳过
    for (int i = 0; i < Row; i++)
    {
        tmp.clear();
        Index++;  //第一列为utc值，跳过

        for (int j = 1; j < Column; j++)
        {
            tmp += Data[Index++];
            if (j != Column - 1)
            {
                tmp += ",";
            }
        }
        tmp += "\n\r";

        Content.push_back(tmp);
    }

    if(Data != nullptr)
    {
        sqlite3_free_table(Data);
    }
    return WT_OK;
}

int WtPrint(int type, const char *Str)
{
    WTLog::Instance().WriteLog(type, Str);
    return WT_OK;
}

