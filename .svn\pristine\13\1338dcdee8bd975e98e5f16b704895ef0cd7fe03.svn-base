/*
 * basefunc.cpp
 *
 *  Created on: 2019-8-1
 *      Author: Administrator
 */
#include <iostream>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "../general/conf.h"
#include "scpi_admintool.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "tester_admin.h"
#include "server/scpi_config.h"
#include <sys/types.h>
#include <dirent.h>
#include "commonhandler.h"
#include "conf.h"
#include "wtlog.h"
#include "listmod_sequence.h"
#include "cellular_analyze/scpi_3gpp_alz_default_param.h"
#include "scpi_3gpp_base.h"

TimeTick::TimeTick(std::string msg)
{
    m_info = msg;
    gettimeofday(&m_start, nullptr);
}

TimeTick::~TimeTick()
{
    gettimeofday(&m_ends, nullptr);
    struct timeval res;
    timersub(&m_ends, &m_start, &res);
    int time_ms = (res.tv_sec * 1000000 + res.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << m_info << " use time = " << time_ms << " us" << std::endl;
}

UserDefindPSDU::UserDefindPSDU()
{
    reset();
}

void UserDefindPSDU::reset()
{
    SegmentID = 0;
    RUID = 0;
    UserID = 0;
    DataLen = 0;
    Data.reset(nullptr);
}

UserPSDU::UserPSDU()
{
    for (int i = 0; i < this->GetMaxSegment(); i++)
    {
        for (int j = 0; j < BE_RU_COUNT; j++)
        {
            for (int k = 0; k < MUMIMO_8_USER; k++)
            {
                WIFI_PSDU *dest = &PSDU[i][j][k];
                memset(dest, 0, sizeof(WIFI_PSDU));
                dest->psduLen = 128;
                dest->psduType = PSDUType_RANDOM;
                dest->scrambler = 12;
                dest->CRCCheckEnable = 1;
                dest->MacHeaderEnable = 1;
                dest->FrameControl[0] = 0x08;
                dest->FrameControl[1] = 0x00;
                for (int m = 0; m < 6; m++)
                {
                    dest->MacAddress1[m] = 0xFF;
                }
                dest->MPDUCount = 0;      // 默认等于0。API层会把MPDU纠正，避免没有配置MPDU count而导致PSDU长度不一致问题
                dest->EofPaddingType = 1; // 默认为1，使用Eof padding 子帧类型计算psdu 长度
                dest->MPDULength[0] = dest->psduLen;
            }
        }
    }
}

OFDMA_RU::OFDMA_RU()
{
    for (int i = 0; i < ARRAYSIZE(RUIndex); i++)
    {
        RUIndex[i] = -1;
    }
    ToneUserCnt.clear();
}

void CalibrationStruct::Reset()
{
    for (int i = 0; i < MAX_CALIBRTION_MODE; i++)
    {
        mode[i] = i;
        mode_enable[i] = 0;
        reload = 0;
        flatnessEnable = 1;
    }
}

SCPI_AlzParam::SCPI_AlzParam()
{
    Reset();
}

void SCPI_AlzParam::Reset()
{
    timeOut = 60.0;
    Reset_AlzParam(&commonAnalyzeParam);
    Reset_AlzParam(&analyzeParamWifi);
    Reset_AlzParam(&analyzeParamBt);
    Reset_AlzParam(&analyzeParamZigBee);
    Reset_AlzParam(&analyzeParamFft);
    Reset_AlzParam(analyzeParam3GPP);
    Reset_AlzParam(&analyzeParamWiSun);
    Reset_AlzParam(&analyzeParamZWave);

}

void SCPI_AlzParam::Reset_AlzParam(AlzParamWifi *param)
{
    memset(param, 0, sizeof(AlzParamWifi));

    param->Demode = WT_DEMOD_11AG;
    param->AutoDetect = WT_USER_DEFINED;
    param->Method11b = WT_11B_STANDARD_TX_ACC;
    param->DCRemoval = WT_DC_REMOVAL_OFF;
    param->EqTaps = WT_EQ_OFF;
    param->PhsCorrMode11B = WT_PH_CORR_11b_OFF;

    param->PhsCorrMode = WT_PH_CORR_SYM_BY_SYM;
    param->ChEstimate = WT_CH_EST_RAW;
    param->SynTimeCorr = WT_SYM_TIM_ON;
    param->FreqSyncMode = WT_FREQ_SYNC_AUTO;
    param->AmplTrack = WT_AMPL_TRACK_OFF;
    param->OfdmDemodOn = 1;
    param->MIMOAnalysisMode = 0;
    param->MimoMaxPowerDiff = 30;
    param->ClockRate = 1;
    param->ICISuppression = 2;
    param->SpectrumMaskVersion = 1;
    param->EqualizerSmoothing = 2;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamBT *param)
{
    memset(param, 0, sizeof(AlzParamBT));
    param->BTDataRate = WT_BT_DATARATE_Auto;
    param->BTPktType = WT_BT_PACKETTYPE_NULL;
    param->ACPSweepTimes = 1;
    param->ACPViewRangeType = 2;   ////默认采样率的一半；acp明文返回时固定返回中间的11点，arb返回就是真实数据控制点数
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamZigBee *param)
{
    memset(param, 0, sizeof(AlzParamZigBee));
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamFFT *param)
{
    memset(param, 0, sizeof(AlzParamFFT));
    param->Rbw = 100 * KHz_API;
    param->WindowType = 4;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamComm *param)
{
    memset(param, 0, sizeof(AlzParamComm));
    param->IQSwap = WT_IQ_SWAP_DISABLED;
    param->IQReversion = WT_IQ_IQReversion_DISABLED;
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamSparkLink *param)
{
    memset(param, 0, sizeof(AlzParamSparkLink));
}

void SCPI_AlzParam::Reset_AlzParam(AlzParam3GPP &Param, const int standard, const int linkdirect)
{
    using namespace cellular::alz;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Reset Cellular Param: Standard=%d, LinkDirect=%d\n", Param.Standard, linkdirect);
    if (IsAlg3GPPStandardType(standard))
    {
        // 公共部分除了analyzegroup,其他都是给算法用的,在协议之前下发的,不能重置
        // memset(&Param, 0, sizeof(AlzParam3GPP));
        Param.analyzeGroup = 0;
        // DcFreqCompensate 和 SpectrumRBW 是固件提供默认值, 不是从算法生成的
        // 但是要受分析参数控制, 公共分析参数会在重置前下发
        for(int i = 0; i < ARRAYSIZE(Param.rf_band); ++i)
        {
            Param.rf_band[i] = 1;
        }

        Param.Standard = standard;
        bool spectrum_rbw_valid = false;
        if (Param.SpectrumRBW < 1000 || Param.SpectrumRBW > 500000 || Param.SpectrumRBW % 10 != 0)
        {
            spectrum_rbw_valid = true;
            Param.DcFreqCompensate = 1;
            Param.SpectrumRBW = spectrum_rbw_valid ? 100 * KHz : Param.SpectrumRBW;
        }
        switch (standard)
        {
        case ALG_3GPP_STD_GSM:
            reset_gsm_alz_param(Param.GSM);
            break;
        case ALG_3GPP_STD_WCDMA:
            reset_wcdma_alz_param(Param.WCDMA, linkdirect);
            break;
        case ALG_3GPP_STD_5G:
            Param.SpectrumRBW = spectrum_rbw_valid ? 120 * KHz : Param.SpectrumRBW;
            reset_nr_alz_param(Param.NR, linkdirect);
            break;
        case ALG_3GPP_STD_NB_IOT:
            Param.SpectrumRBW = spectrum_rbw_valid ? 30 * KHz : Param.SpectrumRBW;
            reset_nbiot_alz_param(Param.NBIOT, linkdirect);
            break;
        case ALG_3GPP_STD_4G:
        default:
            reset_lte_alz_param(Param.LTE, linkdirect);
            break;
        }
    }
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamWiSun *param)
{
    memset(param, 0, sizeof(AlzParamWiSun));
}

void SCPI_AlzParam::Reset_AlzParam(AlzParamZwave *param)
{
    memset(param, 0, sizeof(AlzParamZwave));
}

void SCPI_AlzParam::Close3GPPViews()
{
    // 蜂窝vsa默认关闭以下视图以提高性能
    analyzeParam3GPP.MeasPowerGraph = 0; // Power
    analyzeParam3GPP.MeasSpectrum = 0;   // Spectrum
    analyzeParam3GPP.MeasCCDF = 0;       // IQ Signal
}

SPCIUserParam::SPCIUserParam()
{
    ConnID = -1;
    IsMonObj = false;
    m_List = nullptr;
    Reset();
    DevConf::Instance().GetItemVal("SaveRawDataFlag", m_save_avg_wave.enable);
    DevConf::Instance().GetItemVal("TemporaryFilesNum", m_save_avg_wave.file_count);
    DevConf::Instance().GetItemVal("FileType", m_save_avg_wave.file_type);
}

SPCIUserParam::~SPCIUserParam()
{
    if (!IsMonObj)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "WT_DisConnect ConnID = %d\n", this->ConnID);
        WT_DisConnect(this->ConnID);
        remove_all_vsg_low(this);
    }

    string str;
    for (int i = 0; i < m_MutiPNFilesName.size(); ++i)
    {
        str = string("rm -rf ") + "/tmp/tmpwave/" + m_MutiPNFilesName[i];
        do_system_cmd(str.c_str());
    }
    for (int i = 0; i < m_MutiPNDstFileName.size(); ++i)
    {
        str = string("rm -rf ") + m_MutiPNDstFileName[i];
        do_system_cmd(str.c_str());
    }
}

int SPCIUserParam::Connect(int Type)
{
    // 调用内置api连接仪器
    int Ret = WT_ERR_CODE_OK;
    int mode = TESTER_RUN_NOMAL;
    int unitCount = 1;
    double maxSampleRate_Digit_IQ = 640 * MHz_API;
    double maxSampleRate_RF = MAX_SMAPLE_RATE_API;
    ConnectedUnit ConnUnit;
    string MasterIp("127.0.0.1");

    std::string testjson(WTConf::GetDir() + std::string("/") + TESTER_CONFIG_FILE);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Start Connect server\n");
    WTConf wtconf(WTConf::GetDir() + "/scpi.conf");
    if (WT_OK == wtconf.GetItemVal("TesterIP", MasterIp))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's ip from scpi.conf " << MasterIp << endl;
    }
    else
    {
        MasterIp = "127.0.0.1";
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's ip from scpi.conf " << MasterIp << endl;

    if (WT_OK == GetJsonItemData(testjson, "RunMode", mode))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's run mode from teser.json = " << mode << endl;
    }

    memset(&ConnUnit, 0, sizeof(ConnUnit));
    memcpy(ConnUnit.Ip, MasterIp.c_str(), MasterIp.length());
    ConnUnit.SubTesterIndex = WT_SUB_TESTER_INDEX_AUTO;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Conecting %s,SubTesterIndex %d, ClientFd:%d...\n", ConnUnit.Ip, ConnUnit.SubTesterIndex, ClientFd);

    switch (Type)
    {
    case LINK_TYPE_FORCE:
        Ret = WT_ForceConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_NORMAL:
        Ret = WT_Connect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_SUB:
        Ret = WT_SubConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_MANAGER:
        Ret = WT_ManageConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_MONITOR:
        Ret = WT_MoniConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    case LINK_TYPE_DIAGNOSIS:
        Ret = WT_DialogConnect(&ConnUnit, unitCount, &this->ConnID);
        break;
    default:
        Ret = WT_ERR_CODE_CONNECT_FAIL;
        break;
    }

    if (WT_OK == Ret)
    {
        this->TesterLinkType = Type;
        this->TesterLinkMode = TESTER_AS_NOMAL_LINK;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Connect Tester "
             << " OK, ID=" << this->ConnID << endl;

        if (Type == LINK_TYPE_FORCE || Type == LINK_TYPE_NORMAL || Type == LINK_TYPE_SUB)
        {
            vsgPattern.resize(1);
            WT_GetDefaultParameter(
                this->ConnID,
                &vsaParam,
                &avgParam,
                &vsgParam,
                &waveParam,
                &vsgPattern[0]);
            NeedSetVSGPattern = true;
            switch (mode)
            {
            case TESTER_RUN_DIGIT_IQ:
                this->TesterMajorMode = mode;
                WT_SetDigtalIQMode(this->ConnID, TESTER_RUN_DIGIT_IQ);
                WT_SetMaxSampleRate(this->ConnID, maxSampleRate_Digit_IQ);
                break;
            default:
                this->TesterMajorMode = TESTER_RUN_NOMAL;
                WT_SetDigtalIQMode(this->ConnID, TESTER_RUN_NOMAL);
                WT_SetMaxSampleRate(this->ConnID, maxSampleRate_RF);
                break;
            }

            if (this->CheckBusinessLic(WT_WAVE_DECRYPT_API) != true)
            {
                WT_SetWaveEncrypted(1);
            }
            else
            {
                WT_SetWaveEncrypted(0);
            }
        }
        TesterInfo Info;
        WT_GetTesterInfo(this->ConnID, &Info);
        WT_SetWaveSNAndFW(Info.SN, Info.FwVersion);
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Connect Tester "
             << " Fail,Ret =" << Ret << endl;
    }

    return Ret;
}

int SPCIUserParam::CheckConnectStatus(bool TestConnect)
{
#define CHECK_CONNECT_INTERVAL 1 // 最多每秒测试一次连接状态
    int Ret = WT_ERR_CODE_OK;
    auto CheckAndSetTime = [&]() -> bool
    {
        struct timeval NowTime;
        gettimeofday(&NowTime, NULL);
        if ((NowTime.tv_sec - m_ConnetTestTime.tv_sec > CHECK_CONNECT_INTERVAL + 1) ||
            (NowTime.tv_sec - m_ConnetTestTime.tv_sec == CHECK_CONNECT_INTERVAL && NowTime.tv_usec > m_ConnetTestTime.tv_usec))
        {
            m_ConnetTestTime.tv_sec = NowTime.tv_sec;
            m_ConnetTestTime.tv_usec = NowTime.tv_usec;
            return TestConnect ? true : false;
        }
        else
        {
            return false;
        }
    };

    switch (TesterLinkType)
    {
    case LINK_TYPE_NORMAL:
    case LINK_TYPE_FORCE:
    case LINK_TYPE_SUB:
        if (CheckAndSetTime())
        {
            Ret = WT_TestConnectStatus(this->ConnID);
        }

        if (WT_ERR_CODE_OK == Ret)
        {
            Ret = WT_CheckConnectStatus(this->ConnID);
        }
        break;
    case LINK_TYPE_QUERY:
        Ret = WT_ERR_CODE_OK;
        break;
    default:
        Ret = WT_ERR_CODE_OK;
        break;
    }
    return Ret;
}

void SPCIUserParam::InitMem()
{
    PnRU.reset(new (std::nothrow) OFDMA_RU);
    PnPSDU.reset(new (std::nothrow) UserPSDU);

    PnWifi.reset(new (std::nothrow) GenWaveWifiStruct_API);
    PnBt.reset(new (std::nothrow) GenWaveBtStructV2);
    PnCW.reset(new (std::nothrow) GenWaveCwStruct);
    PnGLE.reset(new (std::nothrow) GenWaveGleStruct());
    Pn3GPP.reset(new (std::nothrow) Alg_3GPP_WaveGenType);
    PnWiSun.reset(new (std::nothrow) GenWaveWisunStruct());

    memset(PnWifi.get(), 0, sizeof(GenWaveWifiStruct_API));
    memset(PnBt.get(), 0, sizeof(GenWaveBtStructV2));
    memset(PnCW.get(), 0, sizeof(GenWaveCwStruct));
    memset(Pn3GPP.get(), 0, sizeof(Alg_3GPP_WaveGenType));

    WaveUserDefineExtendParam.clear();
}

void SPCIUserParam::Reset()
{
    Reset_VsaVsgParam();
    ResetWaveGenParam();
    m_CalSetting.Reset();
    Reset_LiteResult();
}

void SPCIUserParam::Reset_VsaVsgParam()
{
    if (-1 != ConnID)
    {
        vsgPattern.resize(1);
        m_FrameDelay.resize(1);
        m_FrameDelay[0] = 0;
        WT_SetMaxSampleRate(this->ConnID, MAX_SMAPLE_RATE_API);
        WT_GetDefaultParameter(ConnID, &vsaParam, &avgParam, &vsgParam, &waveParam, &vsgPattern[0]);
        NeedSetVSGPattern = true;
    }

    if (true)
    {
        memset(&vsaExtParam, 0, sizeof(vsaExtParam));
        memset(&vsgExtParam, 0, sizeof(vsgExtParam));
        memset(&m_InterParam, 0, sizeof(m_InterParam));
        memset(&tbAnanlyzeParam, 0, sizeof(tbAnanlyzeParam));
        memset(&mPacAttribute, 0, sizeof(mPacAttribute));
        memset(&mPacParam, 0, sizeof(mPacParam));
        memset(&SubNetConfig, 0, sizeof(SubNetConfig));
        memset(&m_Devm, 0, sizeof(m_Devm));
        memset(&m_AzAlyParam, 0, sizeof(m_AzAlyParam));

        // 配置默认分析参数
        vsaAlzParam.Reset();
        vsgAlzParam.Reset();
        vsaAlzParam.analyzeParam3GPP.MeasPowerGraph = 0; // Power
        vsaAlzParam.analyzeParam3GPP.MeasSpectrum = 0;   // Spectrum
        vsaAlzParam.analyzeParam3GPP.MeasCCDF = 0;       // CCDF
        vsgAlzParam.analyzeParam3GPP.MeasPowerGraph = 1;
        vsgAlzParam.analyzeParam3GPP.MeasSpectrum = 1;
        vsgAlzParam.analyzeParam3GPP.MeasCCDF = 1;

        m_TestMode = TESTMODE_RD;

        AlzFrameID = 1;
        VsaSampleRateMode = RATE_DEFAULT_API; // 见枚举WT_SAMPLE_RATE_MODE
        VsgSampleRateMode = RATE_DEFAULT_API; // 见枚举WT_SAMPLE_RATE_MODE
        VsgDemode = WT_DEMOD_11AG;

        mSpectrumWideMode = false;
        mPacFreqList.clear();
        memset(m_CMIMORefFile, 0, sizeof(m_CMIMORefFile));

        memset(&m_DigtalIQParam, 0, sizeof(m_DigtalIQParam));
        m_DigtalIQParam.VsaActionMask = ~0;
        m_DigtalIQParam.VsgActionMask = ~0;
        m_DigtalIQParam.VsaMaxBitCnt = m_DigtalIQParam.VsgMaxBitCnt = 13;
        memset(m_DigtalIQParam.VSAChannelIdList, -1, sizeof(m_DigtalIQParam.VSAChannelIdList));
        memset(m_DigtalIQParam.VSGChannelIdList, -1, sizeof(m_DigtalIQParam.VSGChannelIdList));

        memset(&m_DigtalIQTestFixture, 0, sizeof(m_DigtalIQTestFixture));
        m_DigtalIQTestFixture.SendTimeout = 0.01;
        m_DigtalIQTestFixture.SendPeriod = 250;
        m_DigtalIQTestFixture.SendGap = 0;
        m_DigtalIQTestFixture.SendCnt = 1;

        m_VsaThreadStatus = THREAD_IDEL;
        // WT-4xx default 8080 two port
        vsaExtParam.WIFI8080DulPortMode = 1;
        vsgExtParam.WIFI8080DulPortMode = 1;

        vsaTrigParam.Edge = WT_TRIG_DEGE_POSITIVE_API;
        vsaTrigParam.GapTime = 6e-6;
    }
}

void SPCIUserParam::ResetWaveGenParam()
{
    InitMem();

    WaveGenDemod = WT_DEMOD_11AG;
    if (-1 != ConnID)
    {
        WT_GetDefaultWaveParameterWifi(ConnID, WT_DEMOD_11AG, 0, PnWifi.get());
        WT_GetDefaultWaveParameterBTV2(ConnID, PnBt.get());
        WT_GetDefaultWaveParameterCW(ConnID, PnCW.get());
        WT_GetDefaultWaveParameterGLE(ConnID, PnGLE.get());
        WT_GetDefaultWaveParameter3GPP(ConnID, ALG_3GPP_STD_4G, 0, Pn3GPP.get(), sizeof(Alg_3GPP_WaveGenType));
        WT_GetDefaultWaveParameterWiSun(ConnID, PnWiSun.get());
    }
}

bool SPCIUserParam::CheckBusinessLic(WT_PROT_E_API LicValue)
{
    int iRet = WT_ERR_CODE_OK;
    if (LicInfos == nullptr)
    {
        int maxLicenseCnt = 256;
        int realCnt = 0;

        unique_ptr<LicItemInfo_API[]> TmpInfo(new LicItemInfo_API[maxLicenseCnt]);
        memset(TmpInfo.get(), 0, sizeof(LicItemInfo_API) * maxLicenseCnt);

        iRet = WT_GetLicense(ConnID, (LicItemInfo_API *)TmpInfo.get(), maxLicenseCnt, &realCnt);
        if (iRet != WT_ERR_CODE_OK)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "WT_GetLicense ret= %x", iRet);
            return false;
        }

        LicInfos.reset(new char[sizeof(LicItemInfo_API) * realCnt]);
        memcpy((char *)LicInfos.get(), (char *)TmpInfo.get(), realCnt * sizeof(LicItemInfo_API));
        LicItemCnt = realCnt;
    }

    // WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI Get lic cnt = %d\n", LicItemCnt);
    if (LicItemCnt == 0)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "get no lic~");
        return false;
    }
    else
    {
        TesterInfo DevInfo;
        memset(&DevInfo, 0, sizeof(TesterInfo));
        iRet = WT_GetTesterInfo(ConnID, &DevInfo);
        // WTLog::Instance().WriteLog(LOG_DEBUG, "WT_GetTesterInfo ret = %d\n", iRet);
        if (iRet == WT_ERR_CODE_OK)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "DevInfo.TesterType = %d\n",DevInfo.TesterType);
            if (TEST_TYPE_ENUM_WT428 == DevInfo.TesterType)
            {
                if (LicValue == WT_MAC_INTER_AC_API ||
                    LicValue == WT_MAC_INTER_AX_API ||
                    LicValue == WT_MAC_INTER_BE_API)
                {
                    LicValue = WT_INTER_API;
                }
            }
        }

        LicItemInfo_API *Info = (LicItemInfo_API *)(LicInfos.get());
        bool Find = false;

        for (int i = 0; i < LicItemCnt; i++)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "i = %d, Info->LicType = %d,Info->LicValue=%d,LicValue=%d\n",i,Info->LicType,Info->LicValue , LicValue);
            if (Info->LicType == WT_PROT_TYPE_API && Info->LicValue == LicValue)
            {
                struct tm t_tm;
                time_t EndTimeTicks = 0;
                Find = true;

                // 比较当前时间
                time_t NowTimeTicks;
                time(&NowTimeTicks);

                t_tm.tm_sec = Info->EndTime.Sec;
                t_tm.tm_min = Info->EndTime.Min;
                t_tm.tm_hour = Info->EndTime.Hour;
                t_tm.tm_mday = Info->EndTime.Mday;
                t_tm.tm_mon = Info->EndTime.Mon - 1;
                t_tm.tm_year = Info->EndTime.Year - 1900;
                t_tm.tm_isdst = 0;
                EndTimeTicks = mktime(&t_tm);
                if (EndTimeTicks < NowTimeTicks)
                {
                    // WTLog::Instance().WriteLog(LOG_DEBUG, "license out of date.");
                    return false;
                }
                break;
            }
            Info++;
        }
        if (!Find)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
}

void SPCIUserParam::Reset_LiteResult(int start, int end)
{
    for (int i = start; i < end && i < LITE_ENUM::LITE_ENUM_MAX; i++)
    {
        for (int j = 0; j < MAX_DF_NUM; j++)
        {
            for (int m = 0; m < MAX_SEGMENT; m++)
            {
                m_litePoint[i][j][m].valid = false;
                m_litePoint[i][j][m].value.reset(nullptr);
                m_litePoint[i][j][m].value_len = 0;
            }
        }
    }
}

int SPCIUserParam::GetMoniPort(int MoniVsg)
{
    if (TesterLinkType == LINK_TYPE_NORMAL ||
        TesterLinkType == LINK_TYPE_SUB ||
        TesterLinkType == LINK_TYPE_FORCE)
    {
        return MoniVsg
                   ? (vsgParam.Freq2 > 0 ? vsgExtParam.VsgRfPort[0] : vsgParam.RfPort[0])
                   : (vsaParam.Freq2 > 0 ? vsaExtParam.VsaRfPort[0] : vsaParam.RfPort[0]);
    }
    return WT_PORT_OFF;
}

bool ScpiTimer::NeedRecord(std::string TimerName)
{
    if (m_Enable)
    {
        std::vector<std::string> ignor_cmd = {
            ":SYSTem:ERRor?",
            ":SENSe:CAPTure:STATe?",
            ":SOURce:CURRent:STATe?",
            ":UP:GRADe:FW:STATus?",
            ":SYSTem:CONNinfo?",
            "*CLS",
            "*OPC",
            ":TBTF:STATus?",
            ":TFTB:STATus?",
            "WT:SYSTem:CONFigure:SCPI:TIME:USED?",
        };
        for (int i = 0; i < ignor_cmd.size(); i++)
        {
            if (TimerName.find(ignor_cmd[i]) != string::npos)
            {
                return false;
            }
        }
        return true;
    }
    else
    {
        return false;
    }
}
void ScpiTimer::ClearTimer(std::string TimerName)
{
    if (NeedRecord(TimerName))
    {
        m_Timer.clear();
        m_text.clear();
    }
}

void ScpiTimer::StartTimer(std::string TimerName)
{
    if (NeedRecord(TimerName))
    {
        while (TimerName.back() == '\n' || TimerName.back() == '\t')
        {
            TimerName.pop_back();
        }
        struct timeval tpstart;
        gettimeofday(&tpstart, NULL);
        m_Timer[TimerName][0] = tpstart;
    }
}

void ScpiTimer::StopTimer(std::string TimerName)
{
    if (NeedRecord(TimerName))
    {
        while (TimerName.back() == '\n' || TimerName.back() == '\t')
        {
            TimerName.pop_back();
        }

        if (m_Timer.count(TimerName))
        {
            struct timeval tpend;
            gettimeofday(&tpend, NULL);
            m_Timer[TimerName][1] = tpend;
            std::string str;
            str += TimerName + ": ";
            str += std::to_string((int)((m_Timer[TimerName][1].tv_sec - m_Timer[TimerName][0].tv_sec) * 1e6 + (m_Timer[TimerName][1].tv_usec - m_Timer[TimerName][0].tv_usec)));
            str += "us,";
            m_text = str + m_text;
        }
    }
}

string ScpiTimer::GetCommandUsedTime()
{
    if (',' == m_text.back())
    {
        m_text.pop_back();
        m_text += '.';
    }
    return m_text;
}