/*
 * delimiter_crc_calculate.cpp
 *
 *  Created on: 2021-10-9
 *      Author: Administrator
 */

#include "delimiter_crc_calculate.h"
#include <iostream>
#include <stdio.h>
#include <cmath>
#include "wterrorAll.h"
#include "wtlog.h"

using namespace std;

//CRC part
/// <summary>
/// 循环冗余检验：CRC-32-IEEE 802.3查表法
///CRC-32 IEEE-802.3 x32 + x26 + x23 + x22 + x16 + x12 + x11 + x10 + x8 + x7 + x5 + x4 + x2 + x + 1
/// </summary>

static unsigned int CRC32TABLE[] =
{
    0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA, 0x076DC419, 0x706AF48F, 0xE963A535, 0x9E6495A3,
    0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988, 0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91,
    0x1DB71064, 0x6AB020F2, 0xF3B97148, 0x84BE41DE, 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
    0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC, 0x14015C4F, 0x63066CD9, 0xFA0F3D63, 0x8D080DF5,
    0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172, 0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B,
    0x35B5A8FA, 0x42B2986C, 0xDBBBC9D6, 0xACBCF940, 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
    0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116, 0x21B4F4B5, 0x56B3C423, 0xCFBA9599, 0xB8BDA50F,
    0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924, 0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D,
    0x76DC4190, 0x01DB7106, 0x98D220BC, 0xEFD5102A, 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
    0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818, 0x7F6A0DBB, 0x086D3D2D, 0x91646C97, 0xE6635C01,
    0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E, 0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457,
    0x65B0D9C6, 0x12B7E950, 0x8BBEB8EA, 0xFCB9887C, 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
    0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2, 0x4ADFA541, 0x3DD895D7, 0xA4D1C46D, 0xD3D6F4FB,
    0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0, 0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9,
    0x5005713C, 0x270241AA, 0xBE0B1010, 0xC90C2086, 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
    0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4, 0x59B33D17, 0x2EB40D81, 0xB7BD5C3B, 0xC0BA6CAD,
    0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A, 0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683,
    0xE3630B12, 0x94643B84, 0x0D6D6A3E, 0x7A6A5AA8, 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
    0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE, 0xF762575D, 0x806567CB, 0x196C3671, 0x6E6B06E7,
    0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC, 0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5,
    0xD6D6A3E8, 0xA1D1937E, 0x38D8C2C4, 0x4FDFF252, 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
    0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60, 0xDF60EFC3, 0xA867DF55, 0x316E8EEF, 0x4669BE79,
    0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236, 0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F,
    0xC5BA3BBE, 0xB2BD0B28, 0x2BB45A92, 0x5CB36A04, 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
    0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A, 0x9C0906A9, 0xEB0E363F, 0x72076785, 0x05005713,
    0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38, 0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21,
    0x86D3D2D4, 0xF1D4E242, 0x68DDB3F8, 0x1FDA836E, 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
    0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C, 0x8F659EFF, 0xF862AE69, 0x616BFFD3, 0x166CCF45,
    0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2, 0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB,
    0xAED16A4A, 0xD9D65ADC, 0x40DF0B66, 0x37D83BF0, 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
    0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6, 0xBAD03605, 0xCDD70693, 0x54DE5729, 0x23D967BF,
    0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94, 0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D
};

//#define CRC32_INIT_VALUE        0xffffffffL
//#define CRC32_XOR_VALUE         0xffffffffL
//static unsigned long crctable[256];
/*  Generate a table for a byte-wise 32-bit CRC calculation on the polynomial:
x^32+x^26+x^23+x^22+x^16+x^12+x^11+x^10+x^8+x^7+x^5+x^4+x^2+x^1+x^0.
Polynomials over GF(2) are represented in binary, one bit per coefficient,  with
the lowest powers in the most significant bit.  Then adding polynomials  is just
exclusive-or, and multiplying a polynomial by x is a right shift by  one.  If we
call the above polynomial p, and represent a byte as the  polynomial q, also with
the lowest power in the most significant bit (so the  byte 0xb1 is the polynomial
x^7+x^3+x^1+x^0), then the CRC is (q*x^32) mod p,  where a mod b means the remainder
after dividing a by b.    This calculation is done using the shift-register method
of multiplying and  taking the remainder.  The register is initialized to zero, and
for each  incoming bit, x^32 is added mod p to the register if the bit is a one
(where  x^32 mod p is p+x^32 = x^26+...+x^0), and the register is multiplied mod p
by  x (which is shifting right by one and adding x^32 mod p if the bit shifted  out is a one).
We start with the highest power (least significant bit) of  q and repeat for all eight bits of q.
The table is simply the CRC of all possible eight bit values. This is all  the information needed to
generate CRC's on data a byte at a time for all  combinations of CRC register values and incoming bytes.  */
//void make_crc_table(void)
//{
//    int i, j;
//    unsigned long c, poly;          /* polynomial exclusive-or pattern */
//                                    /* terms of polynomial defining this crc (except x^32): */
//    static const char p[] = { 0, 1, 2, 4, 5, 7, 8, 10, 11, 12, 16, 22, 23, 26 };
//    /* make exclusive-or pattern from polynomial (0xedb88320L) */
//    poly = 0L;
//    for (i = 0; i < sizeof(p) / sizeof(char); i++)
//    {
//        poly |= 1L << (31 - p[i]);
//    }
//    for (i = 0; i < 256; i++)
//    {
//        c = (unsigned long)i;
//        for (j = 0; j < 8; j++)
//        {
//            c = (c & 1) ? poly ^ (c >> 1) : (c >> 1);
//        }
//        crctable[i] = c;
//    }
//}

/// <summary>
/// 计算给定长度数据的32位CRC
/// </summary>
/// <param name="data">要计算CRC的字节数组</param>
/// <returns>CRC值</returns>
unsigned int GetCrc32(const char *data, int len)
{
    int i;
    unsigned int crcValue = 0xFFFFFFFF;
    if (data != NULL)
    {
        for (i = 0; i < len; i++)
        {
            crcValue = (crcValue >> 8) ^ CRC32TABLE[(crcValue ^ data[i]) & 0xFF];
        }
    }

    return ~crcValue;
}

//unsigned int GetCrc32_V(std::vector<u8>& data)
//{
//    unsigned int crcValue = 0xFFFFFFFF;
//    if (data.size() >0)
//    {
//        for (auto &item : data)
//        {
//            crcValue = (crcValue >> 8) ^ CRC32TABLE[(crcValue ^ item) & 0xFF];
//        }
//    }
//
//    return ~crcValue;
//}
//
//int Generator_FCS(std::vector<u8>& data, u8* checksum)
//{
//    u32 checkvalue = GetCrc32_V(data);
//
//    checksum[0] = (checkvalue & 0xff);
//    checksum[1] = ((checkvalue & 0xff00)>>8);
//    checksum[2] = ((checkvalue & 0xff0000) >> 16);
//    checksum[3] = ((checkvalue & 0xff000000) >> 24);
//    return WT_ERR_CODE_OK;
//}

int calculate_FCS(const char *data, int len, u8* checksum)
{
    u32 checkvalue = GetCrc32(data, len);

    checksum[0] = (checkvalue & 0xff);
    checksum[1] = ((checkvalue & 0xff00)>>8);
    checksum[2] = ((checkvalue & 0xff0000) >> 16);
    checksum[3] = ((checkvalue & 0xff000000) >> 24);
    return WT_ERR_CODE_OK;
}

//delimiter part
#define BIT(d, b) (((d) >> (b)) & 0x01)
#define Mod2ADD(a, b) (((a) ^ (b)) & 0x01)


static u8 const RBIT[256] =
{
        0x00, 0x80, 0x40, 0xC0, 0x20, 0xA0, 0x60, 0xE0, 0x10, 0x90, 0x50, 0xD0, 0x30,
        0xB0, 0x70, 0xF0, 0x08, 0x88, 0x48, 0xC8, 0x28, 0xA8, 0x68, 0xE8, 0x18, 0x98,
        0x58, 0xD8, 0x38, 0xB8, 0x78, 0xF8, 0x04, 0x84, 0x44, 0xC4, 0x24, 0xA4, 0x64,
        0xE4, 0x14, 0x94, 0x54, 0xD4, 0x34, 0xB4, 0x74, 0xF4, 0x0C, 0x8C, 0x4C, 0xCC,
        0x2C, 0xAC, 0x6C, 0xEC, 0x1C, 0x9C, 0x5C, 0xDC, 0x3C, 0xBC, 0x7C, 0xFC, 0x02,
        0x82, 0x42, 0xC2, 0x22, 0xA2, 0x62, 0xE2, 0x12, 0x92, 0x52, 0xD2, 0x32, 0xB2,
        0x72, 0xF2, 0x0A, 0x8A, 0x4A, 0xCA, 0x2A, 0xAA, 0x6A, 0xEA, 0x1A, 0x9A, 0x5A,
        0xDA, 0x3A, 0xBA, 0x7A, 0xFA, 0x06, 0x86, 0x46, 0xC6, 0x26, 0xA6, 0x66, 0xE6,
        0x16, 0x96, 0x56, 0xD6, 0x36, 0xB6, 0x76, 0xF6, 0x0E, 0x8E, 0x4E, 0xCE, 0x2E,
        0xAE, 0x6E, 0xEE, 0x1E, 0x9E, 0x5E, 0xDE, 0x3E, 0xBE, 0x7E, 0xFE, 0x01, 0x81,
        0x41, 0xC1, 0x21, 0xA1, 0x61, 0xE1, 0x11, 0x91, 0x51, 0xD1, 0x31, 0xB1, 0x71,
        0xF1, 0x09, 0x89, 0x49, 0xC9, 0x29, 0xA9, 0x69, 0xE9, 0x19, 0x99, 0x59, 0xD9,
        0x39, 0xB9, 0x79, 0xF9, 0x05, 0x85, 0x45, 0xC5, 0x25, 0xA5, 0x65, 0xE5, 0x15,
        0x95, 0x55, 0xD5, 0x35, 0xB5, 0x75, 0xF5, 0x0D, 0x8D, 0x4D, 0xCD, 0x2D, 0xAD,
        0x6D, 0xED, 0x1D, 0x9D, 0x5D, 0xDD, 0x3D, 0xBD, 0x7D, 0xFD, 0x03, 0x83, 0x43,
        0xC3, 0x23, 0xA3, 0x63, 0xE3, 0x13, 0x93, 0x53, 0xD3, 0x33, 0xB3, 0x73, 0xF3,
        0x0B, 0x8B, 0x4B, 0xCB, 0x2B, 0xAB, 0x6B, 0xEB, 0x1B, 0x9B, 0x5B, 0xDB, 0x3B,
        0xBB, 0x7B, 0xFB, 0x07, 0x87, 0x47, 0xC7, 0x27, 0xA7, 0x67, 0xE7, 0x17, 0x97,
        0x57, 0xD7, 0x37, 0xB7, 0x77, 0xF7, 0x0F, 0x8F, 0x4F, 0xCF, 0x2F, 0xAF, 0x6F,
        0xEF, 0x1F, 0x9F, 0x5F, 0xDF, 0x3F, 0xBF, 0x7F, 0xFF
};

unsigned int GetCrc8(u8 *indat, s32 inlen, u8 *outreg)
{
    s32 bytes = 0;
    s32 cnt = 0;
    s8 b = 0;
    u8 reg = 0;

    reg = 0xFF;
    bytes = 0;
    cnt = 0;
    do
    {
        for (b = 0; b < 8; b++)
        {
            u8 c7, c1, c0;
            u8 inb;
            u8 m;
            c7 = BIT(reg, 7);
            c1 = BIT(reg, 1);
            c0 = BIT(reg, 0);
            inb = BIT(indat[bytes], b);

            m = Mod2ADD(inb, c7);
            reg <<= 1;
            reg &= (~0x07);
            reg |= m;
            reg |= (Mod2ADD(m, c0) << 1);
            reg |= (Mod2ADD(m, c1) << 2);

            if (++cnt >= inlen)
            {
                reg = ~reg;
                *outreg = reg;
                return WT_ERR_CODE_OK;
            }
        }
        bytes++;
    } while (1);

    return WT_ERR_CODE_GENERAL_ERROR;
}

void calculate_mpdu_delimiter(int mpduLen, u8* mpdu_delimiter)
{

    if (true)
    {
        //10000010 01100000 10010011 01110010
        //1000 001001100000 10010011 01110010
        // rsv    len=100B     crc      4e
        u16 tmp1 = 0;
        u8 crc8 = 0;

        mpduLen += 4;

        if (true)
        {
            tmp1 = 0x0001;
        }
        else
        {
            mpduLen = (s32)ceil((double)mpduLen / 4) * 4;
        }

        tmp1 |= (((mpduLen - 4) / 4096) << 2);
        tmp1 |= (((mpduLen - 4) % 4096) << 4);
        GetCrc8((u8 *)&tmp1, 16, &crc8);

        mpdu_delimiter[0] = (u8)(tmp1 & 0x00FF);
        mpdu_delimiter[1] = (u8)((tmp1 & 0xFF00) >> 8);
        mpdu_delimiter[2] = RBIT[crc8];
        mpdu_delimiter[3] = 0x4E;

        WTLog::Instance().WriteLog(LOG_DEBUG, "mpdu len = %d, mpdu=0x%.2x,0x%.2x,0x%.2x,0x%.2x, crc8=0x%.2x\r\n",
          mpduLen,
          mpdu_delimiter[0],
          mpdu_delimiter[1],
          mpdu_delimiter[2],
          mpdu_delimiter[3],
          crc8);
    }
}

void calculate_mpdu_delimiter_crc(int mpduLen, u8 *mpdu_delimiter_crc)
{
    u16 tmp1 = 0;
    u8 crc8 = 0;

    mpduLen += 4;

    if (true)
    {
        tmp1 = 0x0001;
    }
    else
    {
        mpduLen = (s32)ceil((double)mpduLen / 4) * 4;
    }

    tmp1 |= (((mpduLen - 4) / 4096) << 2);
    tmp1 |= (((mpduLen - 4) % 4096) << 4);
    GetCrc8((u8 *)&tmp1, 16, &crc8);
    *mpdu_delimiter_crc = RBIT[crc8];
}
