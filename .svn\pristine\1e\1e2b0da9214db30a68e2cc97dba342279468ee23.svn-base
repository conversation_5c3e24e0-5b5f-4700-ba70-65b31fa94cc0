/***************************************************************************************************
 * This confidential and proprietary software may be only used as authorized by a licensing 
 * agreement from iTest Technology Co., Ltd.
 * (C) COPYRIGHT 2020 iTest Technology Co., Ltd. ALL RIGHTS RESERVED
 *--------------------------------------------------------------------------------------------------
 * Filename             : algvsa_3gpp_api.h
 * Author               : Linden
 * Data                 : 2022-09
 * Description          :       
 * Modification History :
 * Data            By          Version         Change Description
 *--------------------------------------------------------------------------------------------------
 * Reference to the source file.
 **************************************************************************************************/

#ifndef ALGVSA_3GPP_API_H_
#define ALGVSA_3GPP_API_H_

/**************************************************************************************************/
/*                                          include                                               */
/**************************************************************************************************/
#include "algvsa_3gpp_export.h"

/**************************************************************************************************/
/*                                 Macro or Debug Configuration                                   */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                      Enum Definition                                           */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                    Struct Definition                                           */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                  Extern Variable Statement                                     */
/**************************************************************************************************/


/**************************************************************************************************/
/*                                  Extern Function Statement                                     */
/**************************************************************************************************/
ALGVSA_3GPP_API void Callback_3GPP_VsaDLLInit();

ALGVSA_3GPP_API void Callback_3GPP_VsaDLLDeinit();

ALGVSA_3GPP_API void Callback_3GPP_VsaInit();

ALGVSA_3GPP_API void Callback_3GPP_VsaDeinit();

ALGVSA_3GPP_API char *Callback_3GPP_VsaDLLVersion(void);

ALGVSA_3GPP_API int Callback_3GPP_VsaReqMemSize(
    Alg_3GPP_VsaInInfo *pIndat);

/* 1. Initialize VSA input parameter.
 * 2. Following parameter shall be set by FW firstly,
 *      pIndat->Standard;
 *      5G      : pIndat->NR.LinkDirect;
 *      4G      : pIndat->LTE.LinkDirect;
 *      NBIOT   : pIndat->NBIOT.LinkDirect;
 *      WCDMA   : pIndat->WCDMA.LinkDirect;
 *      GSM     : NA;
 *      Sidelink: TODO.
 * 3. Following parameter shall be initialized:
 *      pIndat->DcFreqCompensate = ON,
 *      pIndat->SpectrumRBW = 120000,
 *      pIndat->PkgAlzMode = 0,
 *      pIndat->PkgAlzOffset = 0,
 *      pIndat->MeasPowerGraph = OFF,
 *      pIndat->MeasSpectrum = OFF,
 *      pIndat->MeasCCDF = OFF,
 *      pIndat->LTE/NR/NBIOT/WCDMA/GSM/SL
 */
ALGVSA_3GPP_API void Callback_3GPP_VsaParamInit(Alg_3GPP_VsaInInfo *pInInfo);

ALGVSA_3GPP_API int Callback_3GPP_VsaMain(
    Alg_3GPP_VsaInInfo *pIndat,
    Alg_3GPP_VsaOutInfo *pOutdat);

#endif /* ALGVSA_3GPP_API_H_ */
