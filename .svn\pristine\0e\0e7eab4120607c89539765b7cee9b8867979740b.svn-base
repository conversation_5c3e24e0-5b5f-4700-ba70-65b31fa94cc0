
#include <iostream>
#include <future>
#include <sys/time.h>
#include <math.h>
#include <unistd.h>
#include "basehead.h"
#include "scpi_admintool.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "tester_admin.h"
#include "tester_cal.h"
#include "commonhandler.h"
#include "shmkeyword.h"
#include "../general/conf.h"
#include "wtlog.h"

#define FW_TMP_DIR "/tmp/FW/"

enum UPGRADE_STEP_E
{
    UPGRADE_STEP_NOT_START = 0, //未开始升级(SCPI写入)
    UPGRADE_STEP_ACCTEPT_CMD,   //收到升级命令，准备开始升级
    UPGRADE_STEP_WR,            //将升级包存到临时文件，或者从本地文件读取升级数据
    UPGRADE_STEP_DECRYPT,       //解密升级包
    UPGRADE_STEP_ANALYSE,       //分析升级包是否合法
    UPGRADE_STEP_BAK,           //备份当前升级包（可选）
    UPGRADE_STEP_UNZIP,         //解压升级包文件到固件目录
    UPGRADE_STEP_DEB_SETUP,     //安装需要的软件包（可选）
    UPGRADE_STEP_SYS_CONFIG,    //替换系统配置文件（可选）
    UPGRADE_STEP_BACK_FPGA,     //升级背板FPGA
    UPGRADE_STEP_BASE_FPGA,     //升级基带板FPGA
    UPGRADE_STEP_CLEAR_RESTORE, //清理文件和重置文件
    UPGRADE_STEP_FINISH,        //升级完成
    UPGRADE_STEP_MAX,           //无效
};


using UpgradeStep = struct
{
    int CurStep;
    time_t Timep[UPGRADE_STEP_MAX];
};

static atomic_int AdminThreadStatus(THREAD_IDEL);
static std::future<int> AdminThread;
static int AdminThreadResult = WT_ERR_CODE_OK;
static volatile int AdminThreadPercent = 0;

static void *AttachShmAddr()
{
    void *addr = nullptr;
    do
    {
        int shmid = shmget(UPGRADE_KEY_SHARE_MEM, sizeof(UpgradeStep), IPC_CREAT | 0666);
        if (shmid == -1)
        {
            break;
        }

        void *shm_buf = shmat(shmid, NULL, SHM_RND);
        if (shm_buf == (void *)-1)
        {
            break;
        }

        addr = shm_buf;
    }while(0);

    return addr;
}

static void DetachShm(void *addr)
{
    shmdt(addr);
}

static int MangerConnect(int &ID)
{
    //调用内置api连接仪器
    WTLog::Instance().WriteLog(LOG_DEBUG, "Start Connect manger server\n");
    ConnectedUnit ConnUnit;
    memset(&ConnUnit, 0, sizeof(ConnUnit));
    string MasterIp("127.0.0.1");
    WTConf wtconf(WTConf::GetDir() + "/scpi.conf");

    if (WT_OK == wtconf.GetItemVal("TesterIP", MasterIp))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's ip from scpi.conf " << MasterIp << endl;
    }
    else
    {
        MasterIp = "127.0.0.1";
    }

    memcpy(ConnUnit.Ip, MasterIp.c_str(), MasterIp.length());
    ConnUnit.SubTesterIndex = WT_SUB_TESTER_INDEX_AUTO;
    int unitCount = 1;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Conecting %s,SubTesterIndex %d...\n", ConnUnit.Ip, ConnUnit.SubTesterIndex);
    int Ret = WT_ManageConnect(&ConnUnit, unitCount, &ID);
    if (WT_OK == Ret)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Manger Connect Tester OK, ID=" << ID << endl;

    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Manger Connect Tester Fail,Ret =" << Ret << endl;
    }

    return Ret;
}

int SwitchMangeMode(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    iRet = WT_DisConnect(attr->ConnID);
    iRet = MangerConnect(attr->ConnID);
    if (WT_ERR_CODE_OK == iRet)
    {
        attr->TesterLinkType = LINK_TYPE_MANAGER;
    }
    return iRet;
}


static int FW_UpgradeThread(std::string fileName, int ID, atomic_int* status, int *result)
{
    int iRet = WT_ERR_CODE_OK;
    TimeTick tick("WT_FirmwareUpdate");

    *status = THREAD_RUNNING;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FW_UpgradeThread filename = " << fileName << std::endl;

    iRet = WT_FirmwareUpdate(ID, fileName.c_str(), nullptr, 0);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_FirmwareUpdate result = " << iRet << std::endl;

    *status = THREAD_STOPPED;
    *result = iRet;

    return iRet;
}

static int FW_RestoreThread(int mode, int ID, atomic_int *status, int *result)
{
    int iRet = WT_ERR_CODE_OK;
    TimeTick tick("WT_FirmwareRestore");

    *status = THREAD_RUNNING;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_FirmwareRestore mode = " << mode << std::endl;

    iRet = WT_FirmwareRestore(ID, mode);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_FirmwareRestore result = " << iRet << std::endl;

    if (iRet == WT_ERR_CODE_OK)
    {
        if (WT_RESTORE_ORIGINAL == mode)
        {
            iRet = WT_FactoryReset(ID);
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_FactoryReset result = " << iRet << std::endl;
        }
    }
    *status = THREAD_STOPPED;
    *result = iRet;
    return iRet;
}

static int FW_FactoryResetThread(int ID, atomic_int *status, int *result)
{
    int iRet = WT_ERR_CODE_OK;
    TimeTick tick("WT_FactoryReset");

    *status = THREAD_RUNNING;

    iRet = WT_FactoryReset(ID);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_FactoryReset result = " << iRet << std::endl;

    *status = THREAD_STOPPED;
    *result = iRet;

    return iRet;
}

static int GetUpgradePercent()
{
    void *shm_buf = AttachShmAddr();
    UpgradeStep *proc = reinterpret_cast<UpgradeStep *>(shm_buf);
    int percent = 0;
    do
    {
        if(nullptr == proc)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get upgrade shm buffer fail" << std::endl;
            break;
        }

        switch(proc->CurStep)
        {
        case UPGRADE_STEP_NOT_START:
            percent = 10;
            break;
        case UPGRADE_STEP_ACCTEPT_CMD:
        case UPGRADE_STEP_WR:
            percent = 20;
            break;
        case UPGRADE_STEP_DECRYPT:
        case UPGRADE_STEP_ANALYSE:
        case UPGRADE_STEP_BAK:
            percent = 30;
            break;
        case UPGRADE_STEP_UNZIP:
            percent = 40;
            break;
        case UPGRADE_STEP_DEB_SETUP:
        case UPGRADE_STEP_SYS_CONFIG:
            percent = 50;
            break;
        case UPGRADE_STEP_BACK_FPGA:
            percent = 70;
            break;
        case UPGRADE_STEP_BASE_FPGA:
            percent = 80;
            break;
        case UPGRADE_STEP_CLEAR_RESTORE:
            percent = 90;
            break;
        case UPGRADE_STEP_FINISH:
            percent = 100;
            break;
        case UPGRADE_STEP_MAX:
            break;
        default:
            break;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Upgrade current step = " << proc->CurStep << ", percent = " << percent << std::endl;

        DetachShm(shm_buf);

    }while(0);
    return percent;
}

scpi_result_t SCPI_MangerLink(scpi_t *context)
{
    int iRet = SCPI_RES_ERR;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = SwitchMangeMode(attr);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_SetTesterRunMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int mode = 0;
    std::string testjson(WTConf::GetDir() + std::string("/") + TESTER_CONFIG_FILE);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        //manager连接的时候才能设置
        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if(!SCPI_ParamInt(context, &mode, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        //开启数字iq模式，需要先判断license
        if(0 != mode)
        {
            if(attr->CheckBusinessLic(WT_DIQ_AC_API) != true)
            {
                SCPI_ErrorPush(context, WT_ERR_CODE_LICENSE_ERROR);
                return SCPI_RES_ERR;
            }
        }

        TesterInfo info;
        iRet = WT_GetTesterInfo(attr->ConnID, &info);
        IF_BREAK(iRet);

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "mode = " << mode << ",TesterType=" << info.TesterType << std::endl;
        if (TEST_TYPE_ENUM_WT428 == info.TesterType && 0 != mode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->TesterMajorMode = (0 == mode ? TESTER_RUN_NOMAL : TESTER_RUN_DIGIT_IQ);
        SaveJsonItemData(testjson, "RunMode", attr->TesterMajorMode);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GetTesterRunMode(scpi_t *context)
{
    //SPCIUserParam *attr = (SPCIUserParam *)context->user_context;
    int mode = 0;
    std::string testjson(WTConf::GetDir() + std::string("/") + TESTER_CONFIG_FILE);
    if (WT_OK == GetJsonItemData(testjson, "RunMode", mode))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's run mode from teser.json = " << mode << endl;
    }
    SCPI_ResultInt(context, mode);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetTesterDigMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int mode = 0;
    std::string testjson(WTConf::GetDir() + std::string("/") + TESTER_CONFIG_FILE);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        //manager连接的时候才能设置
        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if(!SCPI_ParamInt(context, &mode, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DigMode = " << mode << std::endl;

        // attr->TesterDigMode = (0 == mode ? TESTER_DIG_NOMAL : TESTER_DIG_CHANNEL);
        SaveJsonItemData(testjson, "DigChannelMode", mode);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GetTesterDigMode(scpi_t *context)
{
    int mode = 0;
    std::string testjson(WTConf::GetDir() + std::string("/") + TESTER_CONFIG_FILE);
    if (WT_OK == GetJsonItemData(testjson, "DigChannelMode", mode))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get tester's Dig mode from teser.json = " << mode << endl;
    }
    SCPI_ResultInt(context, mode);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_RebootTester(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_RebootTester(attr->ConnID);
    if (WT_ERR_CODE_OK != iRet)
    {
        do_system_cmd("sync");
        sleep(1);
        do_system_cmd("reboot -f");
    }
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_FWThreadStatus(scpi_t *context)
{
    int status = 0;
    int iRet = WT_ERR_CODE_OK;
    switch(AdminThreadStatus)
    {
    case THREAD_RUNNING:
        status = 1;
        AdminThreadPercent = GetUpgradePercent();
        break;
    case THREAD_STOPPED:
        AdminThreadPercent = 100;
        iRet = AdminThreadResult;
        break;
    default:
        break;
    }
    //出错时把错误码Push进去，给查询用
    if(iRet)
    {
        SCPI_ErrorPush(context, iRet);
    }

    SCPI_ResultInt(context, status);
    SCPI_ResultInt(context, AdminThreadPercent);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_FirmwareUpLoad(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    do
    {
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }
        string dirName = string(FW_TMP_DIR) + string(fileName);
        int pos = dirName.find_last_of('/');
        if (pos != string::npos)
        {
            string cmd = string("mkdir -p ") + dirName.substr(0, pos);
            do_system_cmd(cmd);
        }
        iRet = WriteFile(dirName, data, len);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_FirmwareUpdate(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        string PathName = string(FW_TMP_DIR) + string(fileName);
        if(THREAD_RUNNING != AdminThreadStatus)
        {
            if(AdminThread.valid())
            {
                AdminThreadStatus = THREAD_IDEL;
                AdminThreadPercent = 0;
                AdminThread.get();
            }
            //strcpy(adminToolPath, PathName.c_str());
            AdminThread = std::async(std::launch::async,
                                     &FW_UpgradeThread,
                                     PathName,
                                     attr->ConnID,
                                     &AdminThreadStatus,
                                     &AdminThreadResult);

            iRet = CheckThreadStart(AdminThread, &AdminThreadStatus);
        }

    }while(0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_FirmwareRestore(scpi_t *context)
{
    int mode = WT_RESTORE_LASTED;
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {

        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        SCPI_ParamInt(context, &mode, false);

        if(WT_RESTORE_ORIGINAL != mode && WT_RESTORE_LASTED != mode)
        {
            mode = WT_RESTORE_LASTED;
        }

        if (THREAD_RUNNING != AdminThreadStatus)
        {
            if (AdminThread.valid())
            {
                AdminThreadStatus = THREAD_IDEL;
                AdminThreadPercent = 0;
                AdminThread.get();
            }
            AdminThread = std::async(std::launch::async,
                                     &FW_RestoreThread,
                                     mode,
                                     attr->ConnID,
                                     &AdminThreadStatus,
                                     &AdminThreadResult);

            iRet = CheckThreadStart(AdminThread, &AdminThreadStatus);
        }
    }while(0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_FactoryReset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (attr)
        {
            if (LINK_TYPE_MANAGER != attr->TesterLinkType)
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            if (THREAD_RUNNING != AdminThreadStatus)
            {
                if (AdminThread.valid())
                {
                    AdminThreadStatus = THREAD_IDEL;
                    AdminThreadPercent = 0;
                    AdminThread.get();
                }
                AdminThread = std::async(std::launch::async,
                                         &FW_FactoryResetThread,
                                         attr->ConnID,
                                         &AdminThreadStatus,
                                         &AdminThreadResult);

                iRet = CheckThreadStart(AdminThread, &AdminThreadStatus);
            }
        }
    }while(0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DeleteAllLicense(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (attr)
        {
            if (LINK_TYPE_MANAGER != attr->TesterLinkType)
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            iRet = WT_DeleteAllLicense(attr->ConnID);
        }
    }while(0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GetLicenseInfo(scpi_t *context)
{
    const int maxLicenseCnt = 256;
    int realCnt = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        unique_ptr<LicItemInfo_API[]>info(new LicItemInfo_API[maxLicenseCnt]);
        memset(info.get(), 0, sizeof(LicItemInfo_API) * maxLicenseCnt);

        int iRet = WT_GetLicense(attr->ConnID, info.get(), maxLicenseCnt, &realCnt);
        IF_ERR_RETURN(iRet);

        SCPI_ResultArbitraryBlock(context, (char *)info.get(), realCnt * sizeof(LicItemInfo_API));
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetLicenseInfoStr(scpi_t *context)
{
    const int maxLicenseCnt = 256;
    int realCnt = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        unique_ptr<LicItemInfo_API[]> info(new LicItemInfo_API[maxLicenseCnt]);
        memset(info.get(), 0, sizeof(LicItemInfo_API) * maxLicenseCnt);

        int iRet = WT_GetLicense(attr->ConnID, info.get(), maxLicenseCnt, &realCnt);
        IF_ERR_RETURN(iRet);

        SCPI_ResultInt(context, realCnt);
        for (int i = 0; i < realCnt; i++)
        {
            char TmpStr[100] = {0};
            if (info[i].LicType == WT_HW_TYPE_API)
            {
                sprintf(TmpStr, "%s:%d,startime:%d-%d-%d,endtime:%d-%d-%d",
                        info[i].LicName, info[i].ResourceNum,
                        info[i].StartTime.Year, info[i].StartTime.Mon, info[i].StartTime.Mday,
                        info[i].EndTime.Year, info[i].EndTime.Mon, info[i].EndTime.Mday);
            }
            else
            {
                sprintf(TmpStr, "%s,startime:%d-%d-%d,endtime:%d-%d-%d",
                        info[i].LicName,
                        info[i].StartTime.Year, info[i].StartTime.Mon, info[i].StartTime.Mday,
                        info[i].EndTime.Year, info[i].EndTime.Mon, info[i].EndTime.Mday);
            }
            SCPI_ResultText(context, TmpStr);
        }
    } while (0);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetSlaveLicenseInfo(scpi_t *context)
{
    const int maxLicenseCnt = 256;
    int realCnt = 0;
    int slaveID = 1;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        SCPI_ParamInt(context, &slaveID, false);
        unique_ptr<LicItemInfo_API[]> info(new LicItemInfo_API[maxLicenseCnt]);
        memset(info.get(), 0, sizeof(LicItemInfo_API) * maxLicenseCnt);

        int iRet = WT_GetSlaveTesterLicense(attr->ConnID, slaveID, info.get(), maxLicenseCnt, &realCnt);
        IF_ERR_RETURN(iRet);

        SCPI_ResultArbitraryBlock(context, (char *)info.get(), realCnt * sizeof(LicItemInfo_API));
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetLicensePack(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    const char *data = nullptr;
    size_t len = 0;
    unique_ptr<LicItemInfo_API[]>Info = nullptr;
    const int maxCnt = 256;
    unsigned int realCnt = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        Info.reset(new LicItemInfo_API[maxCnt]);
        memset(Info.get(), 0, maxCnt * sizeof(LicItemInfo_API));

        iRet = WT_LicensePack(attr->ConnID, (char *)data, len, Info.get(), &realCnt);
        if(iRet)
        {
            break;
        }

    } while (0);
    IF_ERR_RETURN(iRet);

    SCPI_ResultArbitraryBlock(context, (char *)Info.get(), realCnt * sizeof(LicItemInfo_API));
    return SCPI_RES_OK;
}

scpi_result_t SCPI_LicenseUpdate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    unique_ptr<char[]> Info = nullptr;
    const int maxCnt = 1024*100;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        Info.reset(new char[maxCnt]);
        memset(Info.get(), 0, maxCnt);

        iRet = WT_LicPackUpdate(attr->ConnID, (char *)Info.get());
        if (iRet)
        {
            break;
        }

    } while (0);

    if (WT_ERR_CODE_OK == iRet)
    {
        strcpy(Info.get(),  "0,\"No error\"\r\n");
    }

    if (strlen(Info.get()) > 0)
    {
        SCPI_ResultArbitraryBlock(context, (char *)Info.get(), strlen(Info.get()));
    }
    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetTesterNetInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    char buf[256] = {0};
    size_t copyLen = 0;
    char name[256] = {0};
    char ipAddr[256] = {0};
    char subMask[256] = {0};
    char netGate[256] = {0};
    char *pDest = nullptr;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    /*"<Name>", "<IP>", "<subnet mask>", "<gateway>"*/
    /*"<Name>", "0.0.0.0"  #DHCP MODE*/

    ILLEGAL_PARAM_RETURN(context->parser_state.numberOfParameters != 4 &&
                         context->parser_state.numberOfParameters != 2);

    do
    {
        for (int i = 0; i < context->parser_state.numberOfParameters; i++)
        {
            memset(buf, 0, sizeof(buf));
            if (!SCPI_ParamCopyText(context, buf, sizeof(buf), &copyLen, true))
            {
                return SCPI_RES_ERR;
            }
            switch(i)
            {
            case 0:
                pDest = name;
                break;
            case 1:
                pDest = ipAddr;
                break;
            case 2:
                pDest = subMask;
                break;
            case 3:
                pDest = netGate;
                break;
            }
            strcpy(pDest, buf);
        }

        if (context->parser_state.numberOfParameters == 2)
        {
            if (!strncmp(ipAddr, "0.0.0.0", strlen("0.0.0.0")))
            {
                strcpy(subMask, "0.0.0.0");
                strcpy(netGate, "0.0.0.0");
            }
            else
            {
                SCPI_ErrorPush(context, SCPI_ERROR_ILLEGAL_PARAMETER_VALUE);
                return SCPI_RES_ERR;
            }
        }
        else if (context->parser_state.numberOfParameters == 4)
        {
            if (!strncmp(ipAddr, "0.0.0.0", strlen("0.0.0.0")))
            {
                SCPI_ErrorPush(context, SCPI_ERROR_ILLEGAL_PARAMETER_VALUE);
                return SCPI_RES_ERR;
            }
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Tester name = " << name << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Tester ipAddr = " << ipAddr << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Tester subMask = " << subMask << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Tester netGate = " << netGate << std::endl;

        iRet = WT_SetTesterInfo(attr->ConnID, name, ipAddr, subMask, netGate);

    }while(0);

    IF_ERR_RETURN(iRet);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetDockerAppInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    using docker_app_info = struct
    {
        char name[16];  // 应用的名称, 小写, 不能有空格等特色符号
        int status_cfg; // 配置状态(1启动,0不启动)
        int status_run; // 实际的运行状态，见DOCKER_RUN_STATUS
        char resv[40];  // 保留备用
    };

    unique_ptr<docker_app_info[]> Info = nullptr;
    const int maxCnt = 128;
    docker_app_info* appList = nullptr;
    int appCnt = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (LINK_TYPE_MANAGER != attr->TesterLinkType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        Info.reset(new docker_app_info[maxCnt]);
        appList = Info.get();
        memset(appList, 0, maxCnt * sizeof(docker_app_info));

        iRet = WT_GetDockerAppList(attr->ConnID, (char *)appList, maxCnt * sizeof(docker_app_info), &appCnt);
        if (iRet)
        {
            break;
        }

        SCPI_ResultInt(context, appCnt);
        for (int i = 0; i < appCnt; i++)
        {
            SCPI_ResultText(context, appList[i].name);
            SCPI_ResultInt(context, appList[i].status_cfg);
            SCPI_ResultInt(context, appList[i].status_run);
        }

    } while (0);

    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetDockerAppInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    char Name[32] = {0};
    int flag = 0;
    size_t copyLen = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (attr)
        {
            if (LINK_TYPE_MANAGER != attr->TesterLinkType)
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            if (!SCPI_ParamCopyText(context, Name, sizeof(Name) - 1, &copyLen, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            if (!SCPI_ParamInt(context, &flag, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            iRet = WT_SetDockerAppEnable(attr->ConnID, Name, flag);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DelDockerApp(scpi_t *context)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    char Name[32] = { 0 };
    size_t copyLen = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (attr)
        {
            if (LINK_TYPE_MANAGER != attr->TesterLinkType)
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            if (!SCPI_ParamCopyText(context, Name, sizeof(Name) - 1, &copyLen, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }

            iRet = WT_DelDockerApp(attr->ConnID, Name);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GetTesterEthAutoneg(scpi_t *context)
{
    int iRet = SCPI_RES_OK;
    string str;
    int EthId = 1;
    unique_ptr<char[]> cmd_ack = make_unique<char[]>(4);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        string strcmd = "ethtool eth"+to_string(EthId)+" |grep 'Advertised auto-negotiation:'|rev|cut -c 1-3|rev";
        iRet = WT_ExecShellCmd(attr->ConnID,strcmd.c_str(),cmd_ack.get(),4);
        if (iRet)
        {
            break;
        }
        str.assign(cmd_ack.get(),3);
        if (str == "Yes")
        {
            str = "auto-negotiation: " + str;
        }
        else
        {
            int bufSize = 1024 * 1024;
            cmd_ack.reset(new char[bufSize]);
            strcmd = "ethtool eth"+to_string(EthId)+" |sed -n -e '/Advertised link modes/='";
            iRet = WT_ExecShellCmd(attr->ConnID,strcmd.c_str(),cmd_ack.get(),bufSize);
            if (iRet)
            {
                break;
            }
            string line1 = std::to_string(atoi(cmd_ack.get()));
            strcmd = "ethtool eth"+to_string(EthId)+" |sed -n -e '/Advertised pause frame use/='";
            iRet = WT_ExecShellCmd(attr->ConnID,strcmd.c_str(),cmd_ack.get(),bufSize);
            if (iRet)
            {
                break;
            }
            string line2 = std::to_string(atoi(cmd_ack.get())-1);
            strcmd = "ethtool eth"+to_string(EthId)+" |sed -n -e '" + line1 + "," + line2 + "p'|sed ':label;N;s/\\n/,/;b label'|sed -e 's/[[:space:]]//g'";
            iRet = WT_ExecShellCmd(attr->ConnID,strcmd.c_str(),cmd_ack.get(),bufSize);
            str.assign(cmd_ack.get());
            str.pop_back();
        }
        SCPI_ResultText(context, str.c_str());
    } while (0);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetTesterEthAutoneg(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int EthId = 1;
    int AutoNeg = 1;
    int Speed = 0;
    string strcmd;
    do
    {
    if (!SCPI_ParamInt(context, &AutoNeg, true))
    {
        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
        break;
    }
    if(AutoNeg)
    {
        strcmd = "ethtool -s eth"+to_string(EthId)+" autoneg on";
    }
    else
    {
        if (!SCPI_ParamInt(context, &Speed, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        strcmd = "ethtool -s eth"+to_string(EthId)+" autoneg off"+" speed "+to_string(Speed)+" duplex full";
    }
    int bufSize = 10;
    unique_ptr<char[]> cmd_ack = make_unique<char[]>(bufSize);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = WT_ExecShellCmd(
    attr->ConnID,
    strcmd.c_str(),
    cmd_ack.get(),
    bufSize);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}