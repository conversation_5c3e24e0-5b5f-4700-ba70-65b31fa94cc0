#include "scpi_3gpp_gen_gsm.h"

#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular;

static inline Alg_GSM_WaveGenType &Gsm(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->Pn3GPP->GSM;
}

scpi_result_t SCPI_GSM_SetSequenceMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SequenceMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSymbolRateMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SymbRateMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSequenceLength(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).GenSequenceLen = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotTimeMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SlotTimeMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetFilterParam(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.15, 2.5))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Filter.FilterParameter = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetPowerRampTime(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0.3, 16))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).PowerRamp.RampTime = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetPowerRampRiseDelay(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-9, 9))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).PowerRamp.RiseDelay = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetPowerRampFallDelay(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-9, 9))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).PowerRamp.FallDelay = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotBurstType(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 4))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].BurstType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotLevel(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].SlotLevel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotAttenuation(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, DOUBLE_RANGE(0, 70))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].SlotAttenuation = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotReptition(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].SlotRepition = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotRepSlotNum(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 8))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].RepSlotNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBModulate(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].ModType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBRateType(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].RateType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBDataType(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].DataType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBUseStealFlag(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].UseStealFlag = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBFlagValue(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].FlagValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBTscSet(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(1, 2))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].TscSet = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotNBTsc(scpi_t *context)
{
    int params[2] = {0};
    int value = 0;

    SCPI_CommandNumbers(context, params, 2);
    int iRet = ScpiChecker(context)
        .CommandParam(params[0], INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .CommandParam(params[1], INT_RANGE(0, arraySize(Gsm(context).Slot[0].NB) - 1))
        .Param(value, INT_RANGE(0, 7))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[params[0]].NB[params[1]].Tsc = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotABDataType(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].AB.DataType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotABSync(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].AB.Synch = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotSBDataType(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 6))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].SB.DataType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotSBEtsc(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].SB.Etsc = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_GSM_SetSlotFCBFixed(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).Slot) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).Slot[param].FCB.Fixed = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
