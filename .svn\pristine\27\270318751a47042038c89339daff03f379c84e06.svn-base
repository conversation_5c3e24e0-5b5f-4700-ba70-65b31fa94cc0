#ifndef __WT_DEV_VSA_H__
#define __WT_DEV_VSA_H__

#include "devbusiness.h"
#include "conf.h"
#include "wt-calibration.h"

//VSA单元
class DevVsa : public DevBusiness
{
public:
    DevVsa(int HwVersion, Json::Value &ConfJson) : DevBusiness(DEV_TYPE_VSA, HwVersion, ConfJson, WT_RX_ATT_MODE)
    {
        //修改ATT0时的额外延时
        int Value = 2000;
        if (DevConf::Instance().GetItemVal("VsaAtt0ConfigDelay", Value) == WT_OK)
        {
            m_Att0ExtDelay = Value;
        }

        for (int addr = 0; addr < sizeof(m_LTC5594Code) / sizeof(m_LTC5594Code[0]); ++addr)
        {
            m_LTC5594Code[addr] = -1;
        }
    }

    virtual ~DevVsa() {};

    virtual int SetRFPowerStatus(WT_SWITCH_STATUS Status);

    int GetGainParam(Rx_Gain_Parm &GainParm);

    int SetmGain(Rx_Gain_Parm GainParm);

    int SetParam(const VSAConfigType &VSAConfig, Rx_Parm &RXParm, int WorkPointMode = false, bool needTrigFlag = true);
    //ListMode配置多个参数
    int SetParamList(std::vector<VSAConfigType> &VSAConfigList, std::vector<Rx_Parm> &RXParmList, std::vector<SeqTimeType> &SeqTime);

    int SetWorkPointParam(const VSAConfigType &VSAConfig, Rx_Parm &RXParm);

    virtual int Start(int Mode = WT_START_MODE_NORMAL);
    //ListMode 只配置VSA_START
    virtual int StartList(int Mode  = WT_START_MODE_NORMAL);

    virtual int Stop();

    virtual int GetStatus();

    virtual int Down();

    int Finish();

    int SetCaptureOriDataSample(VSAConfigType &VsaConfig);

    int CaptureOriData(void *pBuf, int Size, int ExtraSmpOffset);

    int CaptureData(void *pBuf, int Size, int ListModeOffset=0);

    virtual int SaveData(int Index);

    int SetCalConfig(const Rx_Parm &RXParm);

    int SetFreqAndGain(const VSAConfigType &VSAConfig, Rx_Parm &RXParm);

    // 射频板解码器
    int SetRfDemod(Rx_Parm &RXParm);

    int SetFreq(double RXFreq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode);

    // 设置频率并且检查设置结果
    int SetFreqWithConfirm(double RXFreq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode);

    int SetGain(const Rx_Gain_Parm &RXGainParm);

    virtual int SetBand(double ModFreq, double MixFreq, WT_RF_MOD_BAND_E BandMod, WT_RF_MIX_BAND_E BandMIX);

    int CalRXTrigLevelDigital(double TrigLevel);

    int SetDCOffset(int Icode, int Qcode, bool record);

    int SetRXTrigLevelDigital(double TrigLevel);

    int SetADCPowerDown(int PowerDown);

    int GetTrigAddrStart(int *StartAddr);

    int WriteADCReg(int Addr, int Data);

    int ReadADCReg(int Addr, int &Data);

    int SetAGCEnable(int Enable);

    int SetAGCGateValue(int GateValue);

    int TBTApModeStart(int VsgUnitSel);

    int SetTBTStaParam(int Delay);

    void SetCalibrationFlat(int Enable) { m_calibration_flat = Enable;}

    int NotifyVsaFinish();

    int SetDebugAtt(int Index, int Data);

    int GetDebugAtt(int Index, int &Data);

    // VSA PA 开关
    int SetLNAStatus(int Status);

    int VSAGetIQCode(int &GainCode);

    int GetResultSIFS(std::vector<double> &SIFS);

    int GetFpgaCapturePower(long long &Power);

    //模拟IQ信号内/外链路切换开关
    int SetAnalogIQSW(int &AnalogIQSW);
    int GetAnalogIQSW(int &AnalogIQSW);
    //初始化设置IQ模式
    int IQModeInit();

    int VSASetTrig2(const std::vector<VSATriggerType> &TrigParam);

    int SetVsaCwFlag(bool CwFlag);
    int SetVSACaptureDataDeque(std::vector<int>LenVetcor, bool CwFlag = false);
    int CaptureData2(void *pBuf, int Size, int Offset);
    int GetCompleteCnt(int *CompleteCnt);
    int GetVsaSeqCompleteStat(unsigned int *CompleteStat);
    int GetDuplexVsgRunConfig(DuplexVsgRunConfig &config);

protected:
    int GetTrigCongfig(const VSAConfigType &VSAConfig, VSATriggerType &VSATriggerParam ,Rx_DC_Offset_Parm &OffsetParam);
    int GetTrigCongfig2(const VSAConfigType &VSAConfig, const SeqTimeType &SeqTimeParam, VSATriggerType &VSATriggerParam, Rx_DC_Offset_Parm &OffsetParam);
private:
    virtual int InitBaseBandMod();
    int ADCInit();
    int SetIqImbConfig(double GainComp, double PhaseError, double TimeSkew);
    //ListMode 结束后复位仪器状态
    int ResetRundataFromCache();
    int GetParamDmaBuf(int &offset);
private:
    Rx_Gain_Parm m_GainParm;                                //ATT预设置的参数。
    Rx_Gain_Parm m_GainParmDebug;                           //ATT预设置的参数,用于器件生效时间检查DEBUG。
    int m_calibration_flat = false;                         //校准模式开关
    int m_Att0ExtDelay = 2000;                              //修改ATT0时的额外延时，可配置，默认2000us
    int m_LTC5594Code[RX_DEMOD_CAL_ADDR_COUNT];             //Rx解码器配置表
    VSAConfigType m_VSAConfigCache;                         //缓存VSA配置
    unsigned int m_xdmatimeout = 5;                         //xdma超时时间
};
#endif
