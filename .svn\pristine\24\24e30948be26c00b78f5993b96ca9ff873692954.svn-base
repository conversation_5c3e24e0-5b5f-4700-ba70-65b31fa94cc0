/*******************************************************************************
 *
 * file name: wt-calibration.h
 * descript:
 * create by: liweidong 2016.10.17
 *
 *******************************************************************************/
#ifndef __WT_CALIBRATIION_H__
#define __WT_CALIBRATIION_H__

#ifdef __cplusplus
extern "C"
{
#define WT_CALIBRATION_EXTERN extern "C"
#else
#define WT_CALIBRATION_EXTERN
#endif

#define IN_CAL_APPLY_SELF_PORT (0)
#define IN_CAL_APPLY_OTHER_PORT (1)

#define CAL_UNIT_MODE_SISO (0)
#define CAL_UNIT_MODE_MASTER (1)
#define CAL_UNIT_MODE_SLAVE (2)
#define CAL_NOISE_DISABLE (0)     // 正常RF模式
#define CAL_NOISE_DATA_ENABLE (1) // 使用NOISE数据
#define CAL_NOISE_CAL_DATA (2)    // 校准NOISE数据参数请求
#define CAL_RF_PORT_MAX (8)
#define TX_ATT_COUNT (6)    // TX链路ATT器件数量，实际不一定用满
#define RX_ATT_COUNT (6)    // RX链路ATT器件数量，实际不一定用满
#define SW_ATT_COUNT (3)    // 开关板ATT器件数量，新仪器中存在至多3个att，旧仪器中只有1个att

#define IQ_IMAGE_FREQ_OFFSET_COUNT (64) // iq 镜像 频率偏移的个数

// 只用addr0-addr0x16
#define RX_DEMOD_CAL_ADDR_COUNT (0x17)

    /// 内部参考时钟OCXO参数
    typedef struct OCXO_Parameters
    {
        int ocxo_code;
        // int ocxo_code2;
    } Ocxo_Parm;

    typedef struct RF_Configuration_Parameters
    {
        double dac_margin;
        double cal_port_temp_avg;
    } Rf_Config_Parm;

    /// TX DC Offset参数
    typedef struct TX_DC_Offset_Parameters
    {
        int i_code;
        int q_code;
    } Tx_DC_Offset_Parm;

    /// RX DC Offset参数
    typedef struct RX_DC_Offset_Parameters
    {
        int i_code;
        int q_code;
    } Rx_DC_Offset_Parm;

    /// TX I/Q 不平衡参数
    typedef struct TX_IQ_Imbalance_Parameters
    {
        double gain_imb;
        double quad_err;
        double timeskew;
    } Tx_Iq_Imb_Parm;

    /// RX I/Q 不平衡参数
    typedef struct RX_IQ_Imbalance_Parameters
    {
        double gain_imb;
        double quad_err;
        double timeskew;
    } Rx_Iq_Imb_Parm;

    /// TX IQ 镜像参数
    typedef struct TX_IQ_Image_Parameters
    {
        int freq_Iqimb_len;                               // 频域iq不平衡实际长度
        double freq_gain_imb[IQ_IMAGE_FREQ_OFFSET_COUNT]; // 频域iq幅度不平衡值
        double freq_quad_err[IQ_IMAGE_FREQ_OFFSET_COUNT]; // 频域iq相位不平衡值
    }Tx_Iq_Image_Parm;

    /// RX IQ 镜像参数
    typedef struct RX_IQ_Image_Parameters
    {
        int freq_Iqimb_len;                               // 频域iq不平衡实际长度
        double freq_gain_imb[IQ_IMAGE_FREQ_OFFSET_COUNT]; // 频域iq幅度不平衡值
        double freq_quad_err[IQ_IMAGE_FREQ_OFFSET_COUNT]; // 频域iq相位不平衡值
    }Rx_Iq_Image_Parm;

    // TX链路开关板参数
    typedef struct TX_SW_Gain_Parameters
    {
        int sw_tx_link_state;
        int sw_tx_att_code[SW_ATT_COUNT];
        double actual_mpl;
    }Tx_SW_Gain_Parm;

    /// TX链路功率配置参数
    typedef struct TX_Gain_Parameters
    {
        bool is_pa_on;
        int att_code[TX_ATT_COUNT];
        double dac_gain;
        Tx_SW_Gain_Parm tx_sw_gain;
    } Tx_Gain_Parm;

    /// 广播TX链路功率配置参数
    typedef struct TX_BC_RF_Gain_Parameters
    {
        bool is_pa_on;
        int att_code[TX_ATT_COUNT];
        double dac_gain;
    } Tx_BC_RF_Gain_Parm;

    typedef struct TX_BC_SW_Gain_Parameters
    {
        int sw_tx_link_state;
        int sw_tx_att_code;
        double actual_mpl;
    } Tx_BC_SW_Gain_Parm;

    // RX链路开关板参数
    typedef struct RX_SW_Gain_Parameters
    {
        int sw_rx_att_code[SW_ATT_COUNT];
        int sw_rx_link_state;
        double actual_mpl;
        double final_gain;
    }Rx_SW_Gain_Parm;

    /// RX链路功率配置参数
    typedef struct RX_Gain_Parameters
    {
        bool is_lna_on;
        int att_code[RX_ATT_COUNT];
        Rx_SW_Gain_Parm rx_sw_gain;
    } Rx_Gain_Parm;

    /// RX链路功率配置参数
    typedef struct RX_Trigger_Parameters
    {
        int dac_code;
    } Rx_Trig_Parm;

    /// RX链路功率配置参数
    typedef struct RX_Demod_Parameters
    {
        unsigned char data[RX_DEMOD_CAL_ADDR_COUNT];
    } Rx_Demod_Parm;

/// 不同的采样带宽所对应的数量也不同
#define TX_SPECTRUM_SUBCHAN_COUNT (384 * 2 + 1)
    /// TX频谱平坦度补偿参数
    typedef struct TX_Spectrum_Flatness_Compensate_Parameters
    {
        int bb_comp_len;
        int rf_comp_len;
        double bb_comp_gain[TX_SPECTRUM_SUBCHAN_COUNT];
        double rf_comp_gain[TX_SPECTRUM_SUBCHAN_COUNT];
    } Tx_Spec_Flat_Comp_Parm;

/// 不同的采样带宽所对应的数量也不同
/// 240/0.3125 = 768
#define RX_SPECTRUM_SUBCHAN_COUNT (384 * 2 + 1)
    /// TX频谱平坦度补偿参数
    typedef struct RX_Spectrum_Flatness_Compensate_Parameters
    {
        int bb_comp_len;
        int rf_comp_len;
        int ns_comp_len;
        double bb_comp_gain[RX_SPECTRUM_SUBCHAN_COUNT];
        double rf_comp_gain[RX_SPECTRUM_SUBCHAN_COUNT];
        double ns_comp_gain[RX_SPECTRUM_SUBCHAN_COUNT];
    } Rx_Spec_Flat_Comp_Parm;

/// TX链路本振器件数量
#define LO_COUNT (2)

    /// 本振配置参数
    typedef struct LO_Parameters
    {
        int lo;
        int power_level;
        int band;
        int subband;
        double freq;
    } Lo_Parm;

    /// 频率配置参数
    typedef struct Freq_Parameters
    {
        Lo_Parm LoParm[LO_COUNT];
    } Freq_Parm;

    /// TX链路综合参数
    typedef struct TX_Parameters
    {
        int unit;              // 单元编号，表示使用哪一个单元
        int unit_mode;         // siso, 8080主, 8080从
        int rf_port;           // 射频端口
        // int tx_link_sw_state;  // 开关板状态
        double freq;           // 载波频率，单位为Hz
        double power;          // 输出电平，单位为dBm
        double temperature;    // 板上温度，单位为摄氏度
        double sw_temperature; // 开关板上温度，单位为摄氏度
        double sample_freq;    // 硬件重采样频率
        int ex_iq_mode;        // 1 模拟IO模式, 0 rf模式
        int share_mode;        // 共本振模式(0为普通, 非0为供本振)
        double resv[5];        // 保留

        Tx_Iq_Imb_Parm tx_iq_imb_parm_320m;
        Freq_Parm freq_parm;
        Tx_DC_Offset_Parm tx_dc_offset_parm;
        Tx_Iq_Imb_Parm tx_iq_imb_parm;
        Tx_Iq_Imb_Parm tx_iq_imb_parm_160m;
        Tx_Iq_Image_Parm tx_iq_image_parm;      // IQ镜像参数
        Tx_Spec_Flat_Comp_Parm tx_spec_flat_comp_parm;
        Tx_Gain_Parm tx_gain_parm;
    } Tx_Parm;

    /// TX广播链路参数定义
    typedef struct TX_BC_Parameters
    {
        int unit;                      // 单元编号
        int rf_port;                   // 端口号
        double freq;                   // 载波频率，单位为Hz
        double power[CAL_RF_PORT_MAX]; // 输出电平，单位为dBm
        double sample_freq;            // 硬件重采样频率
        double resv[16];               // 保留

        Freq_Parm freq_parm;
        Tx_BC_RF_Gain_Parm tx_rf_gain_parm;
        Tx_BC_SW_Gain_Parm tx_sw_gain_parm[CAL_RF_PORT_MAX];
        Tx_DC_Offset_Parm tx_dc_offset_parm;
        Tx_Iq_Imb_Parm tx_iq_imb_parm;
        Tx_Iq_Imb_Parm tx_iq_imb_parm_160m;
        Tx_Iq_Imb_Parm tx_iq_imb_parm_320m;
        Tx_Spec_Flat_Comp_Parm tx_spec_flat_comp_parm;
    } Tx_BC_Parm;

    /// TX链路传入参数
    typedef struct TX_Handles
    {
        int unit;           // 单元编号，表示使用哪一个单元
        int rf_port;        // 射频端口
        double freq;        // 载波频率，单位为Hz
        double power;       // 输出电平，单位为dBm
        double temperature; // 板上温度，单位为摄氏度
        double dac_gain;
    } TX_Handle;

    /// RX链路综合参数
    typedef struct RX_Parameters
    {
        int unit;              // 单元编号，表示使用哪一个单元
        int unit_mode;         // siso, 8080主, 8080从
        int rf_port;           // 射频端口
        // int rx_link_sw_state;  // 开关板状态
        double freq;           // 载波频率，单位为Hz
        double ref_power;      // 参考电平，单位为dBm
        double temperature;    // 板上温度，单位为摄氏度
        double sw_temperature; // 开关板上温度，单位为摄氏度
        double sample_freq;    // 硬件重采样频率
        int share_mode;        // 共本振模式(0为普通, 非0为供本振)
        int ex_iq_mode;        // 1 模拟IO模式, 0 rf模式
        int noise_flag;        // 是否请求noise数据, 参考CAL_NOISE_XXXX宏定义
        int res;               // 保留
        double resv[7];        // 保留

        Freq_Parm freq_parm;
        Rx_DC_Offset_Parm rx_dc_offset_parm;
        Rx_Iq_Imb_Parm rx_iq_imb_parm;
        Rx_Iq_Imb_Parm rx_iq_imb_parm_160m;
        Rx_Iq_Imb_Parm rx_iq_imb_parm_320m;
        Rx_Iq_Image_Parm rx_iq_image_parm;      // IQ不平衡镜像参数
        Rx_Spec_Flat_Comp_Parm rx_spec_flat_comp_parm;
        Rx_Trig_Parm rx_trig_parm;
        Rx_Demod_Parm rx_demod_parm;
        Rx_Gain_Parm rx_gain_parm;
    } Rx_Parm;

    /// RX链路传入参数
    typedef struct RX_Handles
    {
        int unit;             // 单元编号，表示使用哪一个单元
        int rf_port;          // 射频端口
        double freq;          // 载波频率，单位为Hz
        double ref_power;     // 参考电平，单位为dBm
        bool pd_enable;       // 启用功率检测trigger模式
        double trigger_level; // 触发电平
        double temperature;   // 板上温度，单位为摄氏度
    } RX_Handle;

    /// 自校准
    typedef struct In_Cal_Nodes
    {
        int freq;
        double tx_code_paon;
        double tx_code_paoff;
        double rx_power;
        double temp;
    } In_Cal_Node;

    /// code查询功率值
    typedef struct Pow_Check_Parameters
    {
        int rf_port;        // 射频端口
        int dev_id;         // 哪一个设备, 0 vsg端口, 1 vsa端口, 2内部.
        double freq;        // 当前频率
        double check_code;  // 检测code
        double check_temp;  // 检测时的温度
        double check_power; // 转换后的功率值
    } Pow_Check_Parm;

    /// 自校准运行时的频点配置
    typedef struct In_Cal_Run_Parameters
    {
        int freq;
        int sw_state_det_on;
        int sw_state_det_off;
        int sw_state_det_loop;
        double tx_power_on;
        double tx_power_off;
        double tx_power_loop;
        double rx_ref_power;
    } In_Cal_Freq_Conf;	//原本是In_Cal_Run_Parm;

    /// 噪声校准结构定义
    struct Noise_Param
    {
        int avg;           // 至少 1次 或 多次
        int rbw;           // 10k, 100k, 1k
        double smp_time;   // 单位秒
        double smp_rate;   // 单位HZ 480000000 or 240000000;
        int spec_start;    // 单位M 相对中心点 开始位置 -200
        int spec_end;      // 单位M 相对中心点 结束位置 200
        int spec_avg_span; // 单位M 频谱平均的宽度 5
    };

    struct Freq_Level
    {
        int freq;           // 频率MHz
        int link_idx;       // 链路编号 校准库内部使用
        int level_sub_idx;  // 链路内校准参考电平子编号
        int level_all_idx;  // 所有校准参考电平编号
        double power_level; // 参考电平
    };

    struct Freq_Level_List
    {
        int count; // array有效个数
        Freq_Level *array;
    };

    struct Freq_Level_Data_List
    {
        int freq;           // 频率MHz
        int link_idx;       // 链路编号 校准库内部使用
        int level_sub_idx;  // 链路内校准参考电平子编号
        int level_all_idx;  // 所有校准参考电平编号
        double power_level; // 设置的参考电平
        double real_plevel; // 实际的参考电平
        double resv[8];     // 保留数据位置
        double data[481];   // 传递给校准库的频谱噪声数据
    };

    enum CAL_NOISE_DATA_STATE
    {
        COMP_FILE_NO_EXISTS = 0, // 数据文件不存在
        COMP_DATA_INVALID = 1,   // 数据失效
        COMP_DATA_VALID = 2,     // 数据有效
        COMP_DATA_NOT_CALC = 3,  // 未计算该端口状态
    };

    /////////////////////////////////////////////////////////////////////////////////
    //
    // 以下是正常业务时调用的接口
    //
    ////////////////////////////////////////////////////////////////////////////////

    /// 校准模块版本号
    WT_CALIBRATION_EXTERN const char *wt_calibration_version();

    /// 校准模块初始化
    // unit_count 当前配置总的单元数
    // unit_map_rf_count 每个单元映射端口数
    WT_CALIBRATION_EXTERN int wt_calibration_initial(int unit_count, int unit_map_rf_count);

    /// 与校准相关的器件初始化配置: 本振、ADC、DAC、DAC Margin、ADC期望值等
    WT_CALIBRATION_EXTERN int wt_calibration_get_configuration(Rf_Config_Parm *rf_config_parm);

    /// 系统初始化时获取OCXO code并设置AD5611
    WT_CALIBRATION_EXTERN int wt_calibration_get_ocxo_setting(Ocxo_Parm *ocxo_parm);

    /// 获取TX链路配置参数
    WT_CALIBRATION_EXTERN int wt_calibration_get_tx_setting(Tx_Parm *tx_parm);

    // 获取广播模式TX链路配置参数
    WT_CALIBRATION_EXTERN int wt_calibration_get_tx_bc_setting(Tx_BC_Parm *tx_bc_parm);

    /// 获取RX链路配置参数
    WT_CALIBRATION_EXTERN int wt_calibration_get_rx_setting(Rx_Parm *rx_parm);

    /// 获取TX单元的本镇, share_lo=0非共本振
    WT_CALIBRATION_EXTERN int wt_calibration_get_tx_lo_setting(int unit, double dFreq, Freq_Parm *freq_parm, int share_lo);

    /// 获取RX单元的本振, share_lo=0非共本振
    WT_CALIBRATION_EXTERN int wt_calibration_get_rx_lo_setting(int unit, double dFreq, Freq_Parm *freq_parm, int share_lo);

    ////////////////////////////////////////////////////////////////////////////////
    //
    // 自校准相关
    //
    ////////////////////////////////////////////////////////////////////////////////
    /// 获取TX链路工作点配置参数
    WT_CALIBRATION_EXTERN int wt_calibration_get_tx_work_point_setting(Tx_Parm *tx_parm);

    /// 获取RX链路工作点配置参数
    WT_CALIBRATION_EXTERN int wt_calibration_get_rx_work_point_setting(Rx_Parm *rx_parm);

    // 获取Freq列表
    //WT_CALIBRATION_EXTERN int wt_calibration_get_in_cal_freqs(int rf, int arr[], int *pLen);
	
	// 获取Freq配置列表 port=0表示端口1
    WT_CALIBRATION_EXTERN int wt_calibration_get_in_cal_freqs(int port, In_Cal_Freq_Conf **pArray, int *pLen);

	

    // update Freq 数据
    //WT_CALIBRATION_EXTERN int wt_calibration_set_in_cal_datas(int rf, In_Cal_Node arr[], int *pLen, int cal_mode);
    // update Freq 数据 port=0表示端口1
    WT_CALIBRATION_EXTERN int wt_calibration_set_in_cal_datas(int port, In_Cal_Node arr[], int *pLen, int cal_mode);

    // 获取power值
    WT_CALIBRATION_EXTERN int wt_calibration_get_pow_check_data(Pow_Check_Parm *data);

    // 获取运行参数
    // WT_CALIBRATION_EXTERN int wt_calibration_get_in_cal_run_config(In_Cal_Freq_Conf *data);
    // 设置是否启用内校准的数据
    // WT_CALIBRATION_EXTERN int wt_calibration_set_enable_in_cal(int state);

    ////////////////////////////////////////////////////////////////////////////////
    //
    // 模拟IQ接口
    //
    ////////////////////////////////////////////////////////////////////////////////
    // 获取TX 模拟IQ输出最大值.
    WT_CALIBRATION_EXTERN int wt_calibration_get_tx_ex_iq_max_power(int modid, double *data);

    ////////////////////////////////////////////////////////////////////////////////
    //
    // 镜像IQ接口
    //
    ////////////////////////////////////////////////////////////////////////////////
    // 获取tx iq image的数据
    WT_CALIBRATION_EXTERN int wt_calibration_get_tx_iq_image_data(Tx_Parm *tx_parm);

    // 获取rx iq image的数据
    WT_CALIBRATION_EXTERN int wt_calibration_get_rx_iq_image_data(Rx_Parm *rx_parm);

    ////////////////////////////////////////////////////////////////////////////////
    //
    // NOISE 校准接口
    //
    ////////////////////////////////////////////////////////////////////////////////
    // 获取校准列表所有端口 数组位置0表示端口1
    WT_CALIBRATION_EXTERN int wt_calibration_get_noise_cal_list(int port_list[8], Freq_Level_List port_cal_list[8], Noise_Param *noise_info);

    // 获取NOISE补偿数据状态 数组位置0表示端口1
    WT_CALIBRATION_EXTERN int wt_calibration_get_noise_data_status(int port_list[8], CAL_NOISE_DATA_STATE status_list[8]);

    // 更新端口的校准数据 port=0表示端口1
    WT_CALIBRATION_EXTERN int wt_calibration_update_noise_data(int port, Freq_Level_Data_List *data, int count);

    ////////////////////////////////////////////////////////////////////////////////
    //
    // 以下是校准和调试时调用的接口
    //
    ////////////////////////////////////////////////////////////////////////////////
    /// 从仪器获取校准数据（文件）
    WT_CALIBRATION_EXTERN int wt_calibration_get_calibration_data(char *filename, char *buf, int bufsize, int len);

    /// 向仪器写入校准数据（文件）
    WT_CALIBRATION_EXTERN int wt_calibration_set_calibration_data(char *filename, char *buf, int len);

    /// 从仪器获取校准配置数据（文件）
    WT_CALIBRATION_EXTERN int wt_calibration_get_calibration_configuration_data(char *filename, char *buf, int bufsize, int len);

    /// 向仪器写入校准配置数据（文件）
    WT_CALIBRATION_EXTERN int wt_calibration_set_calibration_configuration_data(char *filename, char *buf, int len);

    /// 使能开关
    typedef struct Calibration_Option
    {
        int tx_cal_mode_flag;       // rx校准模式开关
        int rx_cal_mode_flag;       // tx校准模式开关
        int tx_link_gain_comp_flag; // TX 链路增益补偿
        int rx_link_gain_comp_flag; // RX 链路增益补偿
        int tx_temp_comp_flag;      // TX温补使能
        int rx_temp_comp_flag;      // RX温补使能
        int tx_iq_imb_flag;         // TX I/Q不平衡补偿使能
        int rx_iq_imb_flag;         // RX I/Q不平衡补偿使能
        int tx_spec_flat_flag;      // TX频谱平坦度补偿使能
        int rx_spec_flat_flag;      // RX频谱平坦度补偿使能
    } Cal_Opt;

    /// 设置使能开关，包括温补、I/Q不平衡补偿、频谱平坦度补偿
    WT_CALIBRATION_EXTERN int wt_calibration_set_calibration_option(int buf_size, void *cal_opt);
    /// 获取使能开关，包括温补、I/Q不平衡补偿、频谱平坦度补偿
    WT_CALIBRATION_EXTERN int wt_calibration_get_calibration_option(int len, void *cal_opt);
    /// 设置是否输出打印日志
    WT_CALIBRATION_EXTERN int wt_calibration_set_log_option(int flag);

#ifdef __cplusplus
}
#endif

#endif /* WT_CALIBRATIION_H */
