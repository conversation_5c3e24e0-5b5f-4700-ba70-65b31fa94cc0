#pragma once
#include <vector>
#include <string>
#include <memory>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <fstream>
#include <iostream>
#include <windows.h>
#include <io.h>
#include "global.h"
#include "wterrorAll.h"
#include "UITypeDef.h"


using namespace std;
void add_mpdu_delimiter(int &mpduLen, vector<unsigned char> &mpdu_delimiter, bool FCS = true);
void add_mpdu_delimiter(int &mpduLen, u8* mpdu_delimiter);
void get_mpdu_delimiter_crc8(int mpduLen, u8& crc8);
void add_mpdu_delimiter_v2(int &mpduLen, u8* mpdu_delimiter,bool isEOF=false);//按协议实现
void add_mpdu_delimiter_v3(int &mpduLen, u8* mpdu_delimiter, bool isEOF = false);//只修改EOF
void add_mpdu_delimiter_v4(int &mpduLen, u8* mpdu_delimiter, bool isEOF = false);//还原