#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include <memory>
#include <string.h>
#include "smooth.h"
using namespace std;

#ifndef FALSE
#define FALSE 0
#endif

#ifndef TRUE
#define TRUE 1
#endif

static long min(long x, long y)
{
    return ((x < y) ? x : y);
}

static long max(long x, long y)
{
    return ((x > y) ? x : y);
}

static double pow2(double x)
{
    return (x * x);
}

static double pow3(double x)
{
    return (x * x * x);
}

static int compar(const void *aa, const void *bb)
{
    const double *a = (double *)aa;
    const double *b = (double *)bb;

    if (*a < *b)
    {
        return (-1);
    }
    else if (*a > *b)
    {
        return (1);
    }
    else
    {
        return (0);
    }
}

static void Correctedlowest(
    double *x,
    double *y,
    size_t n,
    double xs,
    double *ys,
    long nleft,
    long nright,
    double *w,
    int userw,
    double *rw,
    int *ok)
{
    double range = 0.0, h = 0.0, h1 = 0.0, h9 = 0.0;
    double a = 0.0, b = 0.0, c = 0.0, r = 0.0;
    long j = 0, nrt = 0;

    range = x[n - 1] - x[0];
    h = fmax(xs - x[nleft], x[nright] - xs);
    h9 = .999 * h;
    h1 = .001 * h;

    a = 0.0;
    for (j = nleft; j < n; j++)
    {
        w[j] = 0.0;
        r = fabs(x[j] - xs);
        if (r <= h9)
        {
            if (r > h1)
            {
                w[j] = pow3(1.0 - pow3(r / h));
            }
            else
            {
                w[j] = 1.0;
            }

            if (userw)
            {
                w[j] = rw[j] * w[j];
            }
            a += w[j];
        }
        else if (x[j] > xs)
        {
            break;
        }
    }
    nrt = j - 1;
    if (a <= 0.0)
    {
        *ok = FALSE;
    }
    else
    {
        *ok = TRUE;

        for (j = nleft; j <= nrt; j++)
        {
            w[j] = w[j] / a;
        }

        if (h > 0.0)
        {
            for (j = nleft, a = 0.0; j <= nrt; j++)
            {
                a += w[j] * x[j];
            }

            b = xs - a;
            for (j = nleft, c = 0.0; j <= nrt; j++)
            {
                c += w[j] * (x[j] - a) * (x[j] - a);
            }

            if (sqrt(c) > .001 * range)
            {
                b = b / c;
                for (j = nleft; j <= nrt; j++)
                {
                    w[j] = w[j] * (1.0 + b * (x[j] - a));
                }
            }
        }

        for (j = nleft, *ys = 0.0; j <= nrt; j++)
        {
            *ys += w[j] * y[j];
        }
    }
}

static void sort(double *x, size_t n)
{
    qsort(x, n, sizeof(double), compar);
}

static int CorrectedData(
    double *x,
    double *y,
    size_t n,
    double f,
    size_t nsteps,
    double delta,
    double *ys,
    double *rw,
    double *res)
{
    int iter = 0, ok = 0;
    long i = 0, j = 0;
    long last = 0, m1 = 0, m2 = 0;
    long nleft = 0, nright = 0, ns = 0;
    double d1 = 0.0, d2 = 0.0;
    double denom = 0.0, alpha = 0.0;
    double cut = 0.0, cmad = 0.0;
    double c9 = 0.0, c1 = 0.0, r = 0.0;

    if (n < 2)
    {
        ys[0] = y[0];
        return (1);
    }

    ns = max(min((long)(f * n), n), 2);
    for (iter = 1; iter <= nsteps + 1; iter++)
    {
        nleft = 0;
        nright = ns - 1;
        last = -1;
        i = 0;
        do
        {
            while (nright < n - 1)
            {
                d1 = x[i] - x[nleft];
                d2 = x[nright + 1] - x[i];
                if (d1 <= d2)
                {
                    break;
                }
                nleft++;
                nright++;
            }
            Correctedlowest(
                x,
                y,
                n,
                x[i],
                &ys[i],
                nleft,
                nright,
                res,
                (iter > 1),
                rw,
                &ok);

            if (!ok)
            {
                ys[i] = y[i];
            }

            if (last < i - 1)
            {
                denom = x[i] - x[last];
                for (j = last + 1; j < i; j = j + 1)
                {
                    alpha = (x[j] - x[last]) / denom;
                    ys[j] = alpha * ys[i] + (1.0 - alpha) * ys[last];
                }
            }
            last = i;
            cut = x[last] + delta;
            for (i = last + 1; i < n; i++)
            {
                if (x[i] > cut)
                {
                    break;
                }

                if (x[i] == x[last])
                {
                    ys[i] = ys[last];
                    last = i;
                }
            }
            i = max(last + 1, i - 1);

        } while (last < n - 1);

        for (i = 0; i < n; i++)
        {
            res[i] = y[i] - ys[i];
        }
        if (iter > nsteps)
        {
            break;
        }
        for (i = 0; i < n; i++)
        {
            rw[i] = fabs(res[i]);
        }
        sort(rw, n);
        m1 = 1 + n / 2;
        m2 = n - m1 + 1;
        cmad = 3.0 * (rw[m1] + rw[m2]);
        c9 = .999 * cmad;
        c1 = .001 * cmad;
        for (i = 0; i < n; i++)
        {
            r = fabs(res[i]);
            if (r <= c1)
            {
                rw[i] = 1.0;
            }
            else if (r > c9)
            {
                rw[i] = 0.0;
            }
            else
            {
                rw[i] = pow2(1.0 - pow2(r / cmad));
            }
        }
    }
    return 0;
}

void FittingData(double *result, size_t n, double fittingFactor)
{
    const double f = fittingFactor; //控制拟合力度
    const size_t nsteps = 3;
    const double delta = 0.3;

    unique_ptr<double[]> ys(new double[n]);
    unique_ptr<double[]> rw(new double[n]);
    unique_ptr<double[]> res(new double[n]);
    unique_ptr<double[]> x(new double[n]);

    double *y = result;
    for (int i = 0; i < n; i++)
    {
        x.get()[i] = i;
    }

    CorrectedData(x.get(), y, n, f, nsteps, delta, ys.get(), rw.get(), res.get());

    memcpy(result, ys.get(), n*sizeof(double));
}