#ifndef _SCPI_GEN_11BE_TB_H_
#define _SCPI_GEN_11BE_TB_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif

    scpi_result_t Set11BE_TB_MUMIMO_NumLTFSymbols(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_STBC(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_SpatialReuse(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_GLTFSize(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_TXOP(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_BSSColor(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_Doppler(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_MidamblePeriodicity(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_PE(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_LTFMode(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_LDPCExtra(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_AFactor(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_PEDisambiguity(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_SIG1Dis(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_SIG2Dis(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_B2Valid(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_320MCenterFrequencyNumber(scpi_t *context);

    scpi_result_t Set11BE_TB_MUMIMO_NDP_Mode(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_NDP_StartingAID(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_NDP_Multiplexing(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_NDP_FeedbackStatus(scpi_t *context);

    scpi_result_t Set11BE_TB_MUMIMO_RUCnt(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUIndex(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserCnt(scpi_t *context);

    scpi_result_t Set11BE_TB_MUMIMO_RUQMat(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUQMatNtx(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUQMatType(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUQMatDelay(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUQMatMap(scpi_t *context);

    scpi_result_t Set11BE_TB_MUMIMO_RUUserAID(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserPowerFactor(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserMCS(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserCoding(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserNSS(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserDCM(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserNSSStart(scpi_t *context);

    scpi_result_t Set11BE_TB_MUMIMO_UserBuiltUpMode(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserCSDTime(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserFreqOffset(scpi_t *context);
    scpi_result_t Set11BE_TB_MUMIMO_RUUserPowerScale(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif