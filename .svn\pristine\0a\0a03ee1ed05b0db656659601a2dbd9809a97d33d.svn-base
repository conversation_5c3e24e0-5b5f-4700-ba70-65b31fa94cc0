//包含对外的接口

#ifndef __PN_Define__
#define __PN_Define__

#include "TesterWave.h"

#define WT_MAX_RFPORT_NUM 8

#define WT_MAX_ANT_NUM 8

typedef struct
{
    int FreqCount;          //频点个数
    double *Response;
} stResponseBlockInfo;

// 整个PN文件数据的结构,输出结果
typedef struct
{
    s32 initializedFlag;                    //是否已初始化
    char *descreption;                      //PN描述
    s32 desLen;                             //PN描述长度

    s32 u32DatCnt;                          //频率插补前，数据区中保存的数据条数
    s32 u32InterpCnt;                       //插补后的内存空间大小,在每次重新加载过文件之后需修改

    double SampleFreq;                      //采样频率,插补后的值
    double Argument;                        //将该类型转化为0dBm的数据，所需乘以的参数

    stPNDat *data;                          //PN数据
    s32 SampleCount;                        //有效PN数据数量

    s32 pnAddr;                             // 起始地址

    s32 pnIndex;                            // PN文件ID

    s32 DataType;                           // 数据类型，64位浮点型、16位整形
    s32 FrameStart;                         // 帧起始位置
    s32 FrameEnd;                           // 帧结束位置
    double RFGain;                          // 数据保存时的射频功率
    double dCenterFreq;                     // 中心频率
    double IQGainImb;                       // IQ幅度不平衡
    double IQPhaseImb;                      // IQ相位不平衡
    double DC_Offset_I;                     // DC offset I
    double DC_Offset_Q;                     // DC offset Q
    double timeSkew;                        // timeSkew

    s32 ExtAttEnable;                       //是否启用外部衰减
    s32 ModType;                            //分析类型，校准使用
    double ExtAtt;                          //外部衰减，与IQV兼容

    stResponseBlockInfo rf_response;
    stResponseBlockInfo bb_response;

    double triggerLevel;
    double vsaAmpl;
    double freqOffset;

    s32 vsaSourceFalg;                      //是否为vsa保存信号的标志位

    s32 sceneMode;                          //MIMO场景标志
    s32 flag8080;                           //8080文件标识
    s32 clockRate;                          //clock rate
    stResponseBlockInfo ns_response;        //噪声补偿数据
    double ExtGain;                         //VSA外部线衰
    MutiPNExtendInfo *MutiPNExInfo;
    double PnIfg;                             //生成的PN中间Gap 
    double PnHead;                          //生成的PN前后Gap
    double PnTail;                          //生成的PN前后Gap
    s32 Repeat;                             //生成的PNPN的重复次数
    s32 PnIndex;                            //生成的PN发送顺序
    s32 reserv[114];                        //保留项 
} stPNFileInfo;

#define strNameLen     256
#define MaxLineBuffer   1024
#define MaxDescLen  (6 * MaxLineBuffer)
#define MaxHeaderLine  100

enum
{
    PN_VSA_SCENE_MODE = (1 << 0),
    PN_VSG_SCENE_MODE = (1 << 1),
    PN_RESAMPLE_ENABLE = (1 << 2),
};

typedef struct
{
    int valid;
    char description[MaxLineBuffer];
    char modType[MaxLineBuffer];
    double dataRate;
    CmimoReferenceHeaderInfo cmimoReferenceHeaderInfo;
    int streamIndex;
    int psduLen;
    int maxDataCacheCount;
    stPNDat *data; //数据
    int dataCount;
    stPNDat *singleDataTmp[WT_MAX_ANT_NUM];
    int singleDataCountTmp[WT_MAX_ANT_NUM];
} stCmimoRefInfo;


#ifndef LINUX

#ifdef PN_EXPORTS
#define WT_PN_API __declspec(dllexport)
#else
#define WT_PN_API __declspec(dllimport)
#endif

#else

#ifdef PN_EXPORTS
#define WT_PN_API	__attribute ((visibility("default")))
#else
#define WT_PN_API
#endif

#endif

#ifdef __cplusplus
extern "C" {
#endif
    //////////////////////////////////////////////////////////////////////////////
    /// 初始化最大采样点数
    /// @return void
    /// @param[in]  s32 maxSampleCount：最大采样点数
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API void InitPNFileMaxSampleCount(s32 maxSampleCount);
    //////////////////////////////////////////////////////////////////////////////
    /// 释放PN内存
    /// @return void
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API void TermPNFileMem();
    //////////////////////////////////////////////////////////////////////////////
    /// 初始化PN信息实例，向指定实例中申请内存；之后对该实例进行操作时不再进行内存分配
    /// @param[out] pResult: 需初始化的对应实例
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitPNFileInfoInstance(stPNFileInfo *pResult);

    //////////////////////////////////////////////////////////////////////////////
    /// 释放PN信息实例
    /// @param[in] pstPNinfo: 需释放的对应PN实例
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 DisposePNFileInfo(stPNFileInfo *pstPNinfo);

    //////////////////////////////////////////////////////////////////////////////
    /// 从指定的文件中读取PN信息，自身根据不同的文件扩展名选择合适的解析方式，默认采用.csv形式
    /// @param[in]  fileName: 指定的文件名称
    /// @param[in]  freq: 需转换成的数据频率，单位MHz；
    /// @param[in]  StreamIndex：流ID号
    /// @param[in]  PnIndex：PN Id号（用于Csv2或Bwv2）
    /// @param[in]  pnMode：模式，例如是否重采样，VSA模式，VSG模式 enum PN_VSA_SCENE_MODE
    /// @param[out] pResult：读取成功后的结果数据
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetPNFileInfoFromFileMIMO(const char *fileName, int freq, int StreamIndex, int PnIndex, stPNFileInfo *pResult, int pnMode);
    //////////////////////////////////////////////////////////////////////////////
    /// 从指定的文件中读取PN配置信息
    /// @param[in]  fileName: 指定的文件名称
    /// @param[in]  data: 存储内存；
    /// @param[in]  len：内存大小，单位byte
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetPNFileStructData(const char *fileName, void *data, int len);
    //////////////////////////////////////////////////////////////////////////////
    /// 从指定的文件中读取PN配置信息字节大小
    /// @param[in]  fileName: 指定的文件名称
    /// @param[out]  len：配置结构体大小，单位byte
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetPNFileStructSize(const char *fileName, int *len);
    //////////////////////////////////////////////////////////////////////////////
    /// 读取CMIMO参考文件
    /// @param[in]  fileName: 指定的文件名称
    /// @param[out]  refVector：
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetCMIMORefFile(const char *fileName, stCmimoRefInfo *refVector);

    //////////////////////////////////////////////////////////////////////////////
    /// 生成CMIMO参考文件
    /// @param[in]  fileName: 指定的文件名称
    /// @param[out]  refVector：
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 CreateCMIMORefFile(const char *fileName, stCmimoRefInfo *refVector);

    WT_PN_API s32 CatenateMutiPnfiles(const MutiPNCatenateInfo *catenateInfo);

    //////////////////////////////////////////////////////////////////////////////
    /// 生成信号文件
    /// @param[in]  pstPNinfo: PN数据内存
    /// @param[in]  fileName: 信号文件名字
    /// @param[in]  streamIndex：流ID号
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 CreatFileByPNFileInfo(stPNFileInfo *pstPNinfo, const char *fileName, s32 streamIndex);
    //////////////////////////////////////////////////////////////////////////////
    /// 清空PN内存数据
    /// @param[in]  pstPNinfo: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API void ClearPNInfor(stPNFileInfo *pstPNinfo);

    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator WIFI时的PN配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_WIFI(GenWaveWifiStruct_API *Set);
    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator BT时的PN配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_BT(GenWaveBtStruct_API *Set);
    WT_PN_API s32 InitWaveGenerator_BTV2(GenWaveBtStructV2 *Set);
    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator CW时的PN配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_CW(GenWaveCwStruct *Set);
	//////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator GLE时的PN配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_GLE(GenWaveGleStruct* Set);
    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator 3GPP时的PN配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_3GPP(void *Set);

    //////////////////////////////////////////////////////////////////////////////
   /// 初始化wave generator WiSun时的PN配置信息，把配置信息写入信号文件
   /// @param[in]  Set: PN数据内存
   /// @return  0:操作成功，非0：失败
   //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_WiSun(GenWaveWisunStruct* Set);
    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator RU 子载波配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_WIFI_RUCarrier(RUCarrierInfo *Set, int Cnt);

    //////////////////////////////////////////////////////////////////////////////
    /// 从指定的文件中读取RU carrier配置信息
    /// @param[in]  fileName: 指定的文件名称
    /// @param[in]  data: 存储内存；
    /// @param[in]  len：内存大小，单位byte
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetRUCarrierStructData(const char *fileName, void *data, int len);

    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator 额外配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_ExternSetting(char *Set, int len);

    //////////////////////////////////////////////////////////////////////////////
    /// GetPNFileExternSetingSize额外配置信息大小，单位字节
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetPNFileExternSetingSize(const char *fileName, int *len);

    //////////////////////////////////////////////////////////////////////////////
    /// GetPNFileExternSetingData额外配置信息
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetPNFileExternSetingData(const char *fileName, void *data, int len);

    //////////////////////////////////////////////////////////////////////////////
    /// 初始化wave generator RU 子载波配置信息，把配置信息写入信号文件
    /// @param[in]  Set: PN数据内存
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 InitWaveGenerator_WIFI_ChannelMode(double pathLoss);

    //////////////////////////////////////////////////////////////////////////////
    /// 初始化保存信号文件时是否加密，根据实际license判断。默认加密
    /// @param[in]  set: 0 = 不加密, 1 = 加密
    /// @return  void
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API void SetWaveEncrypted(int set);

	//////////////////////////////////////////////////////////////////////////////
    /// 初始化保存信号文件时是否加密，根据实际license判断。默认加密
    /// @param[in]  SN: SN信息
    /// @param[in]  FwVersion: FwVersion信息
    /// @return  void
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API void SetWaveSNAndFW(const char *SN, const char *FwVersion);

	//////////////////////////////////////////////////////////////////////////////
    /// 从指定的文件中读取PN信息，自身根据不同的文件扩展名选择合适的解析方式，默认采用.csv形式
    /// @param[in]  fileName: 指定的文件名称
    /// @param[out] RetPnCount：读取读取多PN信号的PN数量
    /// @return  0:操作成功，非0：失败
    //////////////////////////////////////////////////////////////////////////////
    WT_PN_API s32 GetPNCountFromFile(const char * fileName, int &RetPnCount, int *PnOrder);
#ifdef __cplusplus
}
#endif

#endif
