//*****************************************************************************
//  File: wt_protocol.h
//  私有协议处理
//  Data: 2016.8.27
//*****************************************************************************
#ifndef __WT_INTER_PROT_H__
#define __WT_INTER_PROT_H__

#include <vector>
#include "protocol.h"
#include "socket.h"
#include "devtype.h"
#include "errorlib.h"

struct MonVsaResult
{
    char Name[VSA_RESULT_NAME_LEN];
    int StreamID;
    int SegmentID;
    int DataLen;
    int DataType;
    void *Data;
};

// 外部下发的PN配置
struct ExtPnItem
{
    unsigned int LoopCnt;         //循环次数//仅限数字IQ和交互
    unsigned int StartDelay;      // Tx起始延时，单位采样点数//仅限数字IQ和交互
    unsigned int IFG;             //循环间隔，单位采样点数//仅限数字IQ和交互
    double RandomWaveGapMax;      //随机间隔的范围上限，单位：second(秒)//仅限数字IQ和交互
    double RandomWaveGapMin;      //随机间隔的范围下限，单位：second(秒)//仅限数字IQ和交互
    int WaveSource;               // 信号数据来源
    int WavePreset;               // 信号文件编号
    int IFGRamdomMode;            // IFG随机模式
    int WaveType;                 // 信号类型
    char WaveName[MAX_NAME_SIZE]; // 信号文件名
    int Extend;                   //listmod arb信号发送扩展的点数，表示0.xxx个arb信号,listmod模式下，持续发送时，该参数不生效
};

struct PnItem
{
    unsigned long Addr; //起始地址
    u32 Len;            // Tx数据长度
    u32 TBTStartVsaLen;
};

struct VsaTrigParam
{
    double GapTime = 1e-6; ///< gap time
    int Edge = 0;          ///< 触发沿
    double FrameTime = 1e-6;       ///< 最小有效信号长度
    int GenTriggerType = 3;     ///< list mod vsa同步vsg的触发类型，0：不触发，1：在测量开始时同步vsg；2；在测量结束时同步，3：既在测量开始时同步，又在结束时同步
    double Reserved[10];   ///< 保留
};

//下发三个server连接的客户端信息
struct UserConnInfo
{
    char IP[16] = {0};
    int port = 0;
};

//内部协议解析类
class InterProt : public WTProtocol
{
public:
    static InterProt &Instance()
    {
        static InterProt Prot;
        return Prot;
    }

    //*****************************************************************************
    // 发送命令执行结果响应
    // 参数[IN]: Sock : 发送用的socket
    //          Header：响应对应的命令头，如果为空则说明之前的命令无法识别，在回应时需要自己构造响应头
    //           Ret：  命令执行结果
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int Response(WRSocket &Sock, const ReqHeader *Header, int Result);

    //*****************************************************************************
    // 发送命令执行结果响应
    // 参数[IN]: Sock : 发送用的socket
    //          Header：响应对应的命令头
    //           Data ：回应数据
    //           Len  ：数据长度
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int Response(WRSocket &Sock, const ReqHeader *Header, const void *Data, int Len);

    //*****************************************************************************
    // 转发请求对应的命令到指定的socket，用于MIMO情况下将发给主机的命令转发给从机。
    // 本接口存在使用限制，调用此接口的函数需要保证未修改任何原来传递过去的参数
    // 参数[IN]: Sock : 发送用的socket
    //          Header：请求头
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int TransmitCmd(WRSocket &Sock, const ReqHeader *Header);

    //*****************************************************************************
    // 发送参数给从机
    // 参数[IN]: Sock : 发送用的socket
    //        ModType : 类型VSA/VSG
    //         Param  ：参数
    //           Len  ：参数长度
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendModParam(WRSocket &Sock, int ModType, const void *Param, int Len);

    //*****************************************************************************
    // 发送获取VSA数据及校准补偿参数的命令
    // 参数[IN]: Sock : 发送用的socket
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int GetVsaData(WRSocket &Sock);

    //发送VSA启动命令
    int StartSlaveVsa(WRSocket &Sock);

    //发送模块停止命令
    int StopSlaveMod(WRSocket &Sock, int ModType, int Code);

    //同步主机的IFG状态到从机
    int SetVsgSlaveIfg(WRSocket &Sock, int Enable);

    //*****************************************************************************
    // 发送autorange结果，内容为autorange后的VSA配置参数
    // 参数[IN]: Sock : 发送用的socket
    //          Header：请求头
    //          DevNum: MIMO数量，SISO时此值为1
    //          Data  : 结果数据
    //           Len  : 结果长度
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendAutoRangeResult(WRSocket &Sock, const ReqHeader *Header, int DevNum, const void *Data, int Len);

    //*****************************************************************************
    // 发送数据给监视机
    // 参数[IN]: Sock : 发送用的socket
    //           Type : 数据类型
    //           Buf  ：数据buffer
    //           Len  ：buffer的长度
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendMonitorData(WRSocket &Sock, int Type, const void *Buf, int Len);

    //*****************************************************************************
    // 发送VSA数据给监视机
    // 参数[IN]: Sock : 发送用的socket
    //           Type : 数据类型
    //        Result  ：结果数据
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendMonVsaData(WRSocket &Sock, int Type, const std::vector<MonVsaResult> &Result);

    //*****************************************************************************
    // 发送pn配置信息给监视机SendMonPnParam(m_Sock, MON_VSG_PN_STATUS, PnHeadInfo, PnInfo)
    // 参数[IN]: Sock : 发送用的socket
    //           Type : 数据类型
    //     PnHeadInfo ：Pn头数据
    //         PnInfo : Pn项数据
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendMonPnParam(WRSocket &Sock, int Type, const std::vector<PnItemHead> &PnHeadInfo, const std::vector<ExtPnItem> &PnInfo);

    //*****************************************************************************
    // 发送与从机连接的发起端主机的IP、Port信息，主要为申请资源停止服务
    // 参数[IN]: Sock : 发送用的socket
    //           Buf : 数据内容，IP+Port ...
    //           Len ：数据长度
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendConnInfoToSalve(WRSocket &Sock, const void *Buf, int Len);

    //*****************************************************************************
    // 开启正负240M频谱时，发送获取从机VSA三次采集的数据命令
    // 参数[IN]: Sock : 发送用的socket
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int GetSlaveThreeVsaCapData(WRSocket &Sock);

    //*****************************************************************************
    // mimo时，如果加载仪器中的文件而从机没有时，下发信号文件到从机
    // 参数[IN]: Sock : 发送用的socket
    //           Name : 文件名
    //           FileBuf : 信号文件数据
    //           FileLen ：数据长度
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendVsgFileToSalve(WRSocket &Sock, const std::string &Name, const void *FileBuf, int FileLen);

    //*****************************************************************************
    // mimo时，如果加载仪器中的文件而从机没有时，下发信号文件到从机后，重新配置pn
    // 参数[IN]: Sock : 发送用的socket
    //           CurRequest : 对外的协议
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SetPnMasterToSlave(WRSocket &Sock, char *CurRequest);

    //*****************************************************************************
    // mimo时，主机主动下发命令到从机获取从机的license
    // 参数[IN]: Sock : 发送用的socket
    //           CurRequest : 对外的协议
    // 返回值: 命令发送结果，失败表示socket出错
    //*****************************************************************************
    int SendGetLicInfoToSalve(WRSocket &Sock);

private:
    InterProt();
    ~InterProt() {}

    struct VsaResult
    {
        int DataLen;
        int DataType;
        void *Data;
    };

    //发送VSA结果数据
    int SendVsaResult(WRSocket &Sock, AckHeader *Header, const std::vector<VsaResult> &Result);

    //注册协议处理处理函数
    void RegCmdPorc(void);

    //设置监视机监听对象
    int SetMonObj(CmdHeader *Header, void *Data, void *Arg);

    //设置监视机需要获取的数据
    int SetMonData(CmdHeader *Header, void *Data, void *Arg);

    //获取监视机信息列表
    int GetMonitorsInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    //设备信息相关命令处理
    //*****************************************************************************
    //写入校准数据
    int SetCalData(CmdHeader *Header, void *Data, void *Arg);

    //获取校准数据
    int GetCalData(CmdHeader *Header, void *Data, void *Arg);

    //连接接MIMO从机
    int AddMimoDev(CmdHeader *Header, void *Data, void *Arg);

    //断开MIMO从机
    int DelMimoDev(CmdHeader *Header, void *Data, void *Arg);

    //配置MIMO参数
    int SetMimoParam(CmdHeader *Header, void *Data, void *Arg);

    //设置校准参数
    int SetCalParam(CmdHeader *Header, void *Data, void *Arg);

    //获取校准参数
    int GetCalParam(CmdHeader *Header, void *Data, void *Arg);

    //重新加载校准数据文件
    int ReloadCalFile(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    //VSA相关命令处理
    //*****************************************************************************
    //设置VSA参数
    int SetVsaParam(CmdHeader *Header, void *Data, void *Arg);

    //设置VSA分析参数
    int SetVsaAlzParam(CmdHeader *Header, void *Data, void *Arg);

    //获取VSA分析参数
    int GetVsaAlzParam(CmdHeader *Header, void *Data, void *Arg);

    //VSA AutoRange
    int VsaAutoRange(CmdHeader *Header, void *Data, void *Arg);

    //启动VSA采集
    int StartVsa(CmdHeader *Header, void *Data, void *Arg);

    //暂停VSA
    int PauseVsa(CmdHeader *Header, void *Data, void *Arg);

    //停止VSA
    int StopVsa(CmdHeader *Header, void *Data, void *Arg);

    //查询VSA状态
    int GetVsaStatus(CmdHeader *Header, void *Data, void *Arg);

    //手动停止时vsa清空平均数据
    int ClrAvgData(CmdHeader *Header, void *Data, void *Arg);

    //加载VSA信号文件进行分析
    int LoadSigFile(CmdHeader *Header, void *Data, void *Arg);

    //分析VSA数据
    int Analyze(CmdHeader *Header, void *Data, void *Arg);

    //获取VSA数据
    int GetAlzData(CmdHeader *Header, void *Data, void *Arg);

    //当分析11ax是，获取指定用户的结果项
    int Get11axUserAlzData(CmdHeader *Header, void *Data, void *Arg);

    //获取VSA平均结果参数
    int GetAvgData(CmdHeader *Header, void *Data, void *Arg);

    //mimo时获取多路VSA平均结果的平均
    int GetAvgDataComposite(CmdHeader *Header, void *Data, void *Arg);

    //VSA配置查询
    int GetVsaParam(CmdHeader *Header, void *Data, void *Arg);

    //VSA record
    int VsaRecord(CmdHeader *Header, void *Data, void *Arg);

    //获取VSA record数据
    int GetVsaRecordData(CmdHeader *Header, void *Data, void *Arg);

    //获取采集到的原始数据及补偿参数
    int GetVsaRawData(CmdHeader *Header, void *Data, void *Arg);

    //设置VSA平均参数
    int SetVsaAvgParam(CmdHeader *Header, void *Data, void *Arg);

    //获取当前信号的分析结果的信息
    int GetVsaAlzResult(CmdHeader *Header, void *Data, void *Arg);

    //保持采集到的信号到文件
    int SaveSignal(CmdHeader *Header, void *Data, void *Arg);

    //获取参考电平范围
    int GetRefLevelRange(CmdHeader *Header, void *Data, void *Arg);

    //获取当前已平均的次数
    int GetCurAvgCnt(CmdHeader *Header, void *Data, void *Arg);

    //获取当前分析数据的校准补偿参数
    int GetCurVsaCalParam(CmdHeader *Header, void *Data, void *Arg);

    //获取配置VSA后，校准库计算的增益数据
    int GetVsaGainParam(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    //VSG相关命令处理
    //*****************************************************************************
    //设置VSG参数
    int SetVsgParam(CmdHeader *Header, void *Data, void *Arg);

    //下发VSG信号文件
    int VsgSigFile(CmdHeader *Header, void *Data, void *Arg);

    //启动VSG采集
    int StartVsg(CmdHeader *Header, void *Data, void *Arg);

    //暂停VSG
    int PauseVsg(CmdHeader *Header, void *Data, void *Arg);

    //停止VSG
    int StopVsg(CmdHeader *Header, void *Data, void *Arg);

    //查询VSG状态
    int GetVsgStatus(CmdHeader *Header, void *Data, void *Arg);

    //VSG配置查询
    int GetVsgParam(CmdHeader *Header, void *Data, void *Arg);

    //获取配置VSG后，校准库计算的增益数据
    int GetVsgGainParam(CmdHeader *Header, void *Data, void *Arg);

    //设置IFG控制使能状态
    int SetVsgIfgEnable(CmdHeader *Header, void *Data, void *Arg);

    //设置IFG控制使能状态
    int GetVsgIfgEnable(CmdHeader *Header, void *Data, void *Arg);
    //*****************************************************************************
    //信号文件处理
    //*****************************************************************************
    //信号文件下发
    int SendSigFile(CmdHeader *Header, void *Data, void *Arg);

    //查询信号文件是否存在
    int SigFileExist(CmdHeader *Header, void *Data, void *Arg);

    //删除信号文件
    int DelSigFile(CmdHeader *Header, void *Data, void *Arg);

    //获取信号文件列表
    int GetSigFileList(CmdHeader *Header, void *Data, void *Arg);

    //获取信号文件
    int GetSigFile(CmdHeader *Header, void *Data, void *Arg);

    //生成信号文件
    int GenerateSigFile(CmdHeader *Header, void *Data, void *Arg);

    //新的生成信号文件，第二版本信号生成接口
    //int GenerateSigFileV2(CmdHeader *Header, void *Data, void *Arg);
    
    int GenerateSigFileCW(CmdHeader *Header, void *Data, void *Arg);
    int GenerateSigFileBlueTooth(CmdHeader *Header, void *Data, void *Arg);
    int GenerateSigFile3GPP(CmdHeader *Header, void *Data, void *Arg);
    int GenerateSigFileWiSun(CmdHeader *Header, void *Data, void *Arg);

    std::string GetLowWaveDir() { return "/tmp/low_wave/"; }
    std::string GetWaveDir() { return "/tmp/wave/"; }
    
    enum GEN_WAVE_TYPE  //生成时，生成的信号类型
    {
        GEN_CW_WAVE,
        GEN_BT_WAVE,
        GEN_WIFI_WAVE,
        GEN_SLE_WAVE,
        GEN_3GPP_WAVE,
        GEN_WI_SUN,
    };
    //*****************************************************************************
    // 函数: NewGenerateSigFile()
    // 功能: 新的信号生成函数
    // 参数 [IN]：Type：要生成的信号类型，1：siso等；2：CW； 3：Bluetooth。
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int NewGenerateSigFile(int Type, CmdHeader *Header, void *Data, void *Arg);

    //第三版信号文件生成接口
    int GenerateSigFileWifi(CmdHeader *Header, void *Data, void *Arg);

    int GenerateSigFileSLE(CmdHeader *Header, void *Data, void *Arg);

    int GenerateSigSLESynSeq(CmdHeader *Header, void *Data, void *Arg);

    //获取VSG record数据
    int VsgRecord(CmdHeader *Header, void *Data, void *Arg);

    //获取VSG record数据
    int GetVsgRecordData(CmdHeader *Header, void *Data, void *Arg);

    //设置PN参数
    int SetPNCfg(CmdHeader *Header, void *Data, void *Arg);

    //获取PN参数
    int GetPnCfg(CmdHeader *Header, void *Data, void *Arg);

    //获取默认的VSG信号生成参数
    int GetDefaultGenParam(CmdHeader *Header, void *Data, void *Arg);

    //获取发送功率范围
    int GetVsgPowerRange(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    //器件相关命令处理
    //*****************************************************************************


    int SetSubnet(CmdHeader *Header, void *Data, void *Arg);               //子网口配置
    int GetSubnet(CmdHeader *Header, void *Data, void *Arg);               //获取子网口配置信息
    int GetSubnetLink(CmdHeader *Header, void *Data, void *Arg);           //获取子网口link信息
    int GetIpAddressType(CmdHeader *Header, void *Data, void *Arg);           //获取主网口ip地址类型
    int GetGUIVersion(CmdHeader *Header, void *Data, void *Arg);           //获取GUI文件的版本
    int GetGUIFile(CmdHeader *Header, void *Data, void *Arg);              //GUI界面文件获取
    int GetDeviceTemperature(CmdHeader *Header, void *Data, void *Arg);    //获取设备的温度
    int GetHistoryTemperature(CmdHeader *Header, void *Data, void *Arg);   //获取历史温度信息
    int GetVoltInfo(CmdHeader *Header, void *Data, void *Arg);             //获取仪器电压信息
    int GetFanSpeedParam(CmdHeader *Header, void *Data, void *Arg);        //获取风扇的转速
    int SetFanSpeedParam(CmdHeader *Header, void *Data, void *Arg);        //设置风扇的转速
    int SetComponentParam(CmdHeader *Header, void *Data, void *Arg);       //配置器件参数
    int GetComponentParam(CmdHeader *Header, void *Data, void *Arg);       //查询器件参数
    int GetCurSubDeviceCfg(CmdHeader *Header, void *Data, void *Arg);      //获取当前子仪器的资源划分信息

    //*****************************************************************************
    // 函数: GetDeviceInfo()
    // 功能: 获取设备详细信息
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetDeviceInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetDeviceVersionInfo()
    // 功能: 获取设备相关内容的版本信息，算法，校准等
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetDeviceVersionInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetLicenseInfo()
    // 功能: 获取license的相关信息,主要包括license类型，有效开始时间，有效结束时间
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLicenseInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 分析当前数据最优的IQ不平衡
    //*****************************************************************************
    int CalcIQImp(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 设置固定的IQ不平衡补偿参数
    //*****************************************************************************
    int SetStaticIQImb(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 清除固定的IQ不平衡补偿参数
    //*****************************************************************************
    int ClrStaticIQImb(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 设置固定的IQ不平衡补偿参数
    //*****************************************************************************
    int SetVsgStaticIQImb(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 清除固定的IQ不平衡补偿参数
    //*****************************************************************************
    int ClrVsgStaticIQImb(CmdHeader *Header, void *Data, void *Arg);

    //设置是否打开温度补偿功能
    int SetTempCal(CmdHeader *Header, void *Data, void *Arg);

    //设置是否打开平坦度补偿功能
    int SetFlatnessCal(CmdHeader *Header, void *Data, void *Arg);

    //保存线衰文件
    int SetPathLossFile(CmdHeader *Header, void *Data, void *Arg);

    //读取线衰文件
    int GetPathLossFile(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetHardErrInfo()
    // 功能: 获取硬件错误信息
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetHardErrInfo(CmdHeader *Header, void *Data, void *Arg);

    // 函数: DelSubNetSetting()
    // 功能: 删除子网口配置
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int DelSubNetSetting(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: ShutDownDevice()
    // 功能: 仪器关机
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int ShutDownDevice(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SendMeterSettingtoMons()
    // 功能: 发送meter界面配置信息，主要是给监视机发送使用
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SendMeterSettingtoMons(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: AnalyzeBeamformingCalibrationChannelEstDutTx()
    // 功能: Beamforming 在Calibration时，控制DUT发送，WT-200接收，调用此函数估算出Hab 1x3
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int AnalyzeBeamformingCalibrationChannelEstDutTx(CmdHeader *Header, void *Data, void *Arg);
    //*****************************************************************************
    // 函数: AnalyzeBeamformingCalibrationChannelEstDutRx()
    // 功能: Beamforming 在Calibration时，控制WT-300发送，DUT接收，调用此函数估算出Hba 3x1
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int AnalyzeBeamformingCalibrationChannelEstDutRx(CmdHeader *Header, void *Data, void *Arg);
    //*****************************************************************************
    // 函数: AnalyzeBeamformingCalibrationResult()
    // 功能: Beamforming 在Calibration时，在获取Hab 1x3和Hba 3x1后，通过此函数获取相位
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int AnalyzeBeamformingCalibrationResult(CmdHeader *Header, void *Data, void *Arg);
    //*****************************************************************************
    // 函数: AnalyzeBeamformingVerification()
    // 功能: Beamforming 在Verification时，通过配置DUT进入相应状态并输出Beamforming信号，WT-300接收
    //            并解析DUT信号，计算Beamforming带来的增益。
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int AnalyzeBeamformingVerification(CmdHeader *Header, void *Data, void *Arg);
    //*****************************************************************************
    // 函数: AnalyzeBeamformingCalculateChannelProfile()
    // 功能: Beamforming 在Calibration时，通过此函数获取DUT发送信号的幅度和相位，此函数适用于MTK方案
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int AnalyzeBeamformingCalculateChannelProfile(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: AnalyzeBeamformingCalculateChannelAmplitudeAngleBCM()
    // 功能: Beamforming 在Calibration时，AnalyzeBeamformingCalibrationChannelEstDutTx后
    // 通过此函数获取DUT发送信号的幅度和相位，有效空间流数，此函数适用于BCM方案
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int AnalyzeBeamformingCalculateChannelAmplitudeAngleBCM(CmdHeader *Header, void *Data, void *Arg);


    //*****************************************************************************
    // 函数: GetLog()
    // 功能: 通过指定的条件来获取相应的日志记录
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetLog(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetSaveLogFlagSetting()
    // 功能: 获取仪器中不同日志类型是否保存的控制标识
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetSaveLogFlagSetting(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetSaveLogFlag()
    // 功能: 设置日志保存控制标识
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetSaveLogFlag(CmdHeader *Header, void *Data, void *Arg);
    //*****************************************************************************
    //日志诊断相关处理
    //*****************************************************************************

    //*****************************************************************************
    // 函数: SetStopSlave()
    // 功能: 停止从机
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetStopSlave(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SendCustomizeFile()
    // 功能: 下发用户文件
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SendCustomizeFile(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetCustomizeFile()
    // 功能: 获取用户文件内容
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetCustomizeFile(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: ShellExecute()
    // 功能: 执行shell命令处理
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int ShellExecute(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: AnalyzePerResult()
    // 功能: Per分析
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int AnalyzePerResult(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetExtralAlzParam()
    // 功能: 配置附加扩展的分析参数
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int SetExtralAlzParam(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetAnalyseGroupBaseResultStr()
    // 功能: 分析前，根据结果字符串配置传给算法的配置AnalyseGroup，达到控制算法只分析某部分的功能
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int SetAnalyseGroupBaseResultStr(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SendUserConnInfo()
    // 功能: 下发连接用户的连接信息（一个用户三个连接对应的IP+port信息）,为取消连接资源等待使用，内部使用的协议
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int SendUserConnInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsaResultFilter()
    // 功能: 配置帧过滤的条件
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int SetVsaResultFilter(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: Set240MSpectOnFlag()
    // 功能: 配置是否使能正负240M频谱的功能
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int Set240MSpectOnFlag(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsaTrigParam
    // 功能: 配置VSA触发动作参数
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int SetVsaTrigParam(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetThreeVsaRawData()
    // 功能: 当正负240M频谱功能开启时，mimo下主机到从机获取正负240M采集的三组数据
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //****************************************************************************
    int GetThreeVsaRawData(CmdHeader *Header, void *Data, void *Arg);

    //启动Tb模式测试
    int StartTBTAp(CmdHeader *Header, void *Data, void *Arg);

    //停止Tb模式测试
    int StopTBTAp(CmdHeader *Header, void *Data, void *Arg);

    //查询Tb模式测试状态
    int GetTBTApStatus(CmdHeader *Header, void *Data, void *Arg);

    //Tb AutoRange
    int VsaAutoRangeTBTAp(CmdHeader *Header, void *Data, void *Arg);

    //启动Tb模式测试
    int StartTBTSta(CmdHeader *Header, void *Data, void *Arg);

    //停止Tb模式测试
    int StopTBTSta(CmdHeader *Header, void *Data, void *Arg);

    //查询Tb模式测试状态
    int GetTBTStaStatus(CmdHeader *Header, void *Data, void *Arg);

    //设置内校开关
    int SetInCalConfig(CmdHeader *Header, void *Data, void *Arg);

    int StartInCal(CmdHeader *Header, void *Data, void *Arg);
    int StopInCal(CmdHeader *Header, void *Data, void *Arg);
    int QueryInCalProcess(CmdHeader *Header, void *Data, void *Arg);

    //mimo下，主机把本机的指定信号文件发送到从机
    int SaveVsgFileMastertoSlave(CmdHeader *Header, void *Data, void *Arg);  //mimo主机给从机发信号文件

    //获取wave生成后才确定的生成参数
    int GetFinalParamAfterGenerate(CmdHeader *Header, void *Data, void *Arg);

    //获取wave生成后经过算法返回的生成参数
    int GetFinalGenWaveWifiParamAfterGenerate(CmdHeader *Header, void *Data, void *Arg);

    //获取wave生成后指定算法返回的ofdma数据
    int GetReturnDataAfterGenerate(CmdHeader *Header, void *Data, void *Arg);

    //从api下发ofdma的ru carrier信息，vsg加载文件时，用于ofdma的补偿
    int SetRuCarrierInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetSlaveLicInfo()
    // 功能: 主机获取从机的全部license信息
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetSlaveLicInfo(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsgFlatnessCal()
    // 功能: 配置vsg是否开启硬件补偿，平坦度补偿
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetVsgFlatnessCal(CmdHeader *Header, void *Data, void *Arg);
    int GetVsgFlatnessCal(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsgIQImbCal()
    // 功能: 配置vsg是否开启IQ不平衡补偿
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetVsgIQImbCal(CmdHeader *Header, void *Data, void *Arg);
    int GetVsgIQImbCal(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsaFlatnessCal()
    // 功能: 配置vsa是否开启硬件补偿，平坦度补偿
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetVsaFlatnessCal(CmdHeader *Header, void *Data, void *Arg);
    int GetVsaFlatnessCal(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsaIQImbCal()
    // 功能: 配置vsa是否开启IQ不平衡补偿
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetVsaIQImbCal(CmdHeader *Header, void *Data, void *Arg);
    int GetVsaIQImbCal(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsaDuplexNoiseFlag()
    // 功能: 双工补偿开关
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetVsaDuplexNoiseFlag(CmdHeader *Header, void *Data, void *Arg);
    int GetVsaDuplexNoiseFlag(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetDevRunMode()
    // 功能: 设置仪器运行状态
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetDevRunMode(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetDigParam()
    // 功能: 设置数字IQ包目标MAC
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetDigParam(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetDigDutMode()
    // 功能: 设置数字IQ治具模式
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetDigDutMode(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: GetVsgSendCnt()
    // 功能: 获取VSG已经发送的次数
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetVsgSendCnt(CmdHeader *Header, void *Data, void *Arg);

    //*****************************************************************************
    // 函数: SetVsgFemParam()
    // 功能: 配置vsg fem模式及参数
    // 参数 [IN]：head：协议头
    // 参数 [IN]：Data：除去协议头外的，数据内容部分
    // 参数 [IN]：Arg：额外的参数
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int SetVsgFemParam(CmdHeader *Header, void *Data, void *Arg);

    //启动ATT校准子功能
    int ATTCalStart(CmdHeader *Header, void *Data, void *Arg);
    //停止ATT校准子功能
    int ATTCalStop(CmdHeader *Header, void *Data, void *Arg);
    //设置业务板本振模式
    int SetDevLOMode(CmdHeader *Header, void *Data, void *Arg);
    //获取业务板本振模式
    int GetDevLOMode(CmdHeader *Header, void *Data, void *Arg);

    int TestConnectStatus(CmdHeader *Header, void *Data, void *Arg);

    //设置模拟IQ信号内/外链路切换开关
    int SetDevAnalogIQMode(CmdHeader *Header, void *Data, void *Arg);
    int GetDevAnalogIQMode(CmdHeader *Header, void *Data, void *Arg);

    int SetSubNetAutoNeg(CmdHeader *Header, void *Data, void *Arg);
    int GetSubNetAutoNeg(CmdHeader *Header, void *Data, void *Arg);
    
    //获取频谱指定频点的功率
    int GetSpectrumPointPower(CmdHeader *Header, void *Data, void *Arg);

    // 子命令处理
    int SubCmdHandle(CmdHeader *Header, void *Data, void *Arg);
    
    int SetExtendEVMStatus(CmdHeader *Header, void *Data, void *Arg);
    int SetVsaIterativeEVMStatus(CmdHeader *Header, void *Data, void *Arg);
    int SetVsaSncEVMStatus(CmdHeader *Header, void *Data, void *Arg);
    int SetVsaCcEVMStatus(CmdHeader *Header, void *Data, void *Arg);
    int NoiseCalStart(CmdHeader *Header, void *Data, void *Arg);
    int NoiseCalStop(CmdHeader *Header, void *Data, void *Arg);
    int GetNoiseCalStatus(CmdHeader *Header, void *Data, void *Arg);
    int GetNoiseCalValid(CmdHeader *Header, void *Data, void *Arg);

	//读写广播模式
    int SetBroadcastEnable(CmdHeader *Header, void *Data, void *Arg);
    int GetBroadcastEnable(CmdHeader *Header, void *Data, void *Arg);
    //广播DEBUG模式
    int SetBroadcastDebugEnable(CmdHeader *Header, void *Data, void *Arg);
    // 获取当前广播VSG运行状态
    int GetBroadcastRunStatus(CmdHeader *Header, void *Data, void *Arg);

    //listmod
    //使能listmod
    int SetlistEnable(CmdHeader *Header, void *Data, void *Arg);
    //去使能listmod
    int SetlistDisable(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的分析参数
    int SetlistSegVsaAlzParam(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的抓取参数
    int SetlistSegVsaCapParam(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的trig参数
    int SetlistSegVsaTrigParam(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的common trig参数
    int SetlistSegTrigCommParam(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的时间参数
    int SetlistSegTimeParam(CmdHeader *Header, void *Data, void *Arg);
    //启动TX seq
    int SetlistSegVsaSeqStart(CmdHeader *Header, void *Data, void *Arg);
    //启动RX seq
    int SetlistSegVsgSeqStart(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的vsg参数
    int SetlistSegVsgParam(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的vsg同步参数
    int SetlistSegVsgSyncParam(CmdHeader *Header, void *Data, void *Arg);
    //设置list seg的vsg参数
    int SetlistSegVsgWaveParam(CmdHeader *Header, void *Data, void *Arg);
    //启动TXRX seq
    int SetlistSegVsaVsgSeqStart(CmdHeader *Header, void *Data, void *Arg);
    //停止TX seq
    int SetlistSegVsaSeqStop(CmdHeader *Header, void *Data, void *Arg);
    //停止RX seq
    int SetlistSegVsgSeqStop(CmdHeader *Header, void *Data, void *Arg);
    //停止TXRX seq
    int SetlistSegVsaVsgSeqStop(CmdHeader *Header, void *Data, void *Arg);
    //获取seq状态
    int GetlistSegVsaVsgSeqState(CmdHeader *Header, void *Data, void *Arg);
    //获取tx seq抓取状态
    int GetListTxSeqAllCapState(CmdHeader *Header, void *Data, void *Arg);
    //获取tx seq分析状态
    int GetListTxSeqAllAnalyState(CmdHeader *Header, void *Data, void *Arg);
    //获取rx seq抓取状态
    int GetListRxSeqAllTransState(CmdHeader *Header, void *Data, void *Arg);
    //获取rx seq功率结果
    int GetListTxSeqAllPowerResult(CmdHeader *Header, void *Data, void *Arg);
    //清空上次的配置
    int SetListTxSeqVsaClear(CmdHeader *Header, void *Data, void *Arg);
    //获取lte tx各seg的状态
    int GetListLteTxSeqAllSegStat(CmdHeader *Header, void *Data, void *Arg);

    int DuplexSetState(CmdHeader *Header, void *Data, void *Arg);
    int DuplexGetState(CmdHeader *Header, void *Data, void *Arg);
};

#endif
