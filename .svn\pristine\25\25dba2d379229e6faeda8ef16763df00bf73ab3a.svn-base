//*****************************************************************************
//  File: monitor.h
//  监控系统运行状态
//  Data: 2016.7.11
//*****************************************************************************

#include "monitor.h"
#include <cstdio>
#include <sys/eventfd.h>
#include <stdint.h> /*Definition of uint64_t*/
#include <fcntl.h>
#include <iostream> //cout namespace
#include <unistd.h> //read so on
#include <sstream>
#include <cerrno>

#include "wterror.h"
#include "launch.h"
#include "devlib.h"
#include "wtlog.h"
#include "device.h"
#include "license.h"
#include "conf.h"
#include "basefun.h"
using namespace std;

const int UPGRADE_FINISH_REBOOT_WAIT = 5;

// 注册定时器回掉函数并启动定时器开始监控
int Monitor::Run(void)
{
    // WT-Server监控间隔为1s
    const int SRV_TIMER_INTERVAL = 1;
    m_EvTimer.set<Monitor, &Monitor::SrvTimerCb>(this);
    m_EvTimer.start(SRV_TIMER_INTERVAL + 30, SRV_TIMER_INTERVAL);

    // 仪器时间是否被篡改监控定时器,间隔为1小时监测一次
    const int DEV_TIME_CHECK_TIMER_INTERVAL = 1 * 60 * 60;
    m_DevTimeEvTimer.set<Monitor, &Monitor::DevTimeCheckTimerCb>(this);
    m_DevTimeEvTimer.start(30, DEV_TIME_CHECK_TIMER_INTERVAL);

    // 监听仪器系统状态，内存硬盘占用等，每分钟记录一次
    const int SYS_MONITOR_TIMER_INTERVAL = 60;
    m_DevTimeEvTimer.set<Monitor, &Monitor::SysMonitorTimerCb>(this);
    m_DevTimeEvTimer.start(10, SYS_MONITOR_TIMER_INTERVAL);

    m_UpgradeTimer.set<Monitor, &Monitor::UpgradeTimerCb>(this);
    m_UpgradeRebootWait = false;
    return WT_OK;
}

void Monitor::RegisterMonitorInstance(std::shared_ptr<MonitorInstance> Obj, int Efd)
{
    Obj->Efd = Efd;
    fcntl(Efd, F_SETFL, fcntl(Efd, F_GETFL, 0) | O_NONBLOCK); // 设置sock为非阻塞模式
    Obj->IOWatcher.set_userdata(Obj.get());
    Obj->IOWatcher.set<Monitor, &Monitor::MonitorIOWatcherCb>(this);
    Obj->IOWatcher.start(Obj->Efd, EV_READ);
    Obj->DataNoneChangeCnt = 0;
    printf("RegisterMonitorInstance ServerId=%d, Obj= %p\n", Obj->ServerId, Obj.get());
}

// 从laucher中获取WT-Server/SCPI/Vxi信息，然后将WT-Server/SCPI/Vxi加入到监控列表
int Monitor::RegisterMonitor(void)
{
    int Ret = 0;
    int Efd;

    // 监控WT-Server
    if (m_ServerStat != WT_FORK_SERVER_FAILED)
    {
        int ServerCnt = 0;
        Ret = m_Launcher->GetServerCnt(ServerCnt);
        if (Ret == WT_OK)
        {
            for (int i = 0; i < ServerCnt; i++)
            {
                m_Launcher->GetServerEventfd(i, Efd);
                std::shared_ptr<MonitorInstance> pInstance = make_shared<MonitorInstance>(m_EvLoop, i);
                RegisterMonitorInstance(pInstance, Efd);
                m_MonitorInsVector.emplace_back(pInstance);
            }
        }
    }

    // 监控WT-SCPI
    m_Launcher->GetScpiEventfd(Efd);
    std::shared_ptr<MonitorInstance> pScpiInstance = make_shared<MonitorInstance>(m_EvLoop, -1);
    RegisterMonitorInstance(pScpiInstance, Efd);
    m_MonitorInsVector.emplace_back(pScpiInstance);

    // 监控WT-Vxi
    int vxi_fd = -1;
    m_Launcher->GetVxiEventfd(vxi_fd);
    // fcntl(vxi_fd, F_SETFL, fcntl(vxi_fd, F_GETFL, 0) | O_NONBLOCK); //设置sock为非阻塞模式
    std::shared_ptr<MonitorInstance> pVxiInstance = make_shared<MonitorInstance>(m_EvLoop, -2);
    RegisterMonitorInstance(pVxiInstance, vxi_fd);
    m_MonitorInsVector.emplace_back(pVxiInstance);
    return WT_OK;
}

// 从laucher中获取WT-Server信息，然后将WT-Server加入到监控列表
int Monitor::ReRegisterServer(int ServerId)
{
    if (m_ServerStat != WT_FORK_SERVER_FAILED)
    {
        int Efd = -1;
        m_Launcher->GetServerEventfd(ServerId, Efd);
        RegisterMonitorInstance(m_MonitorInsVector[ServerId], Efd);
    }
    return WT_OK;
}

void Monitor::MonitorIOWatcherCb(wtev::io &watcher, int revents)
{
    if (revents & EV_READ)
    {
        MonitorInstance *pInstance = (MonitorInstance *)watcher.userdata;
        uint64_t Value = 0;
        ssize_t s = read(pInstance->Efd, &Value, sizeof(uint64_t));
        // 仪器处于升级状态不执行server监控内容
        if (WTDeviceInfo::Instance().GetDevUpgradeState() != UPGRADE_IDLE_STATE)
        {
            return;
        }

        if ((s != sizeof(uint64_t)) || (Value == 0))
        {
            stringstream Log;
            Log << "MonitorIOWatcherCb " << Pout(pInstance->ServerId) << Pout(pInstance->DataNoneChangeCnt) << Pout(s) << Pout(Value)
                << Pout(errno) << Pout(EAGAIN) << Pout(EINVAL);
#if DEBUG
            cout << Log.str() << endl;
#endif
            WTLog::Instance().LOGERR(WT_ERROR, Log.str());
            pInstance->DataNoneChangeCnt++;
        }
        else if (Value >= ErrorEvent)
        {
            stringstream Log;
            Log << "MonitorIOWatcherCb " << Pout(pInstance->ServerId) << Pout(Value) << ", ReStartProcess";
#if DEBUG
            cout << Log.str() << endl;
#endif
            WTLog::Instance().LOGERR(WT_ERROR, Log.str());
            ReStartProcess(pInstance);
        }
        else
        {
            pInstance->DataNoneChangeCnt = 0;
        }
    }
}

void Monitor::MonitoringProcess(void)
{
    for (auto &MonitorIns : m_MonitorInsVector)
    {
#if DEBUG
        if (MonitorIns->DataNoneChangeCnt > 0)
        {
            struct timeval tpstart;
            gettimeofday(&tpstart, NULL);
            std::cout << "MonitoringProcess " << Pout(MonitorIns->ServerId) << ", Timeout at " << tpstart.tv_sec << "." << tpstart.tv_usec << std::endl;

            stringstream Log;
            Log << "MonitoringProcess " << Pout(MonitorIns->ServerId) << Pout(MonitorIns->DataNoneChangeCnt) << "eventfd timeout";
            WTLog::Instance().LOGERR(WT_ERROR, Log.str());
        }
#endif
        if (++MonitorIns->DataNoneChangeCnt > 4)
        {
            ReStartProcess(MonitorIns.get());
        }
    }
}

void Monitor::Getserverinfo(void)
{
    char Buf[1024] = {0};
    int Cnt = readlink("/proc/self/exe", Buf, 1024);
    while (Cnt--)
    {
        if (Buf[Cnt] == '/')
        {
            Buf[Cnt] = '\0';
            break;
        }
    }

    char DirName[2048] = {0};
    sprintf(DirName, "%s/ServerINFO/", Buf);

    if (-1 == access(DirName, F_OK))
    {
        if (-1 == mkdir(DirName, 0755)) // 建立目录
        {
            printf("Create test get server info dir failed!\n");
            return;
        }
    }

    char DataBuf[512] = {0};
    struct tm *Mtm;
    time_t Now;
    time(&Now);
    Mtm = localtime(&Now);
    sprintf(DataBuf, "Sever_monitor_info_%d-%d-%d_%d-%d-%d_%p.csv", 1900 + Mtm->tm_year, Mtm->tm_mon + 1, Mtm->tm_mday, Mtm->tm_hour, Mtm->tm_min, Mtm->tm_sec, this);

    char DataFileName[4096] = {0};
    sprintf(DataFileName, "%s%s", DirName, DataBuf);

    char cmd[256] = {0};
    string File = " ";
    File = WTConf::GetDir() + "/gstack_2.sh >";
    sprintf(cmd, "%s%s", File.c_str(), DataFileName);

    // printf("cmd = %s%s\n\n", File.c_str(), DataFileName);
    //  printf("result = %s\n",Basefun::shell_exec("ps -A | grep WT-").c_str());
    // printf("result = %s\n",Basefun::shell_exec("echo 666 ").c_str());

    Basefun::LinuxSystem(cmd);
    // Basefun::LinuxSystem(sync);
    // system(cmd);
}

void Monitor::ReStartProcess(MonitorInstance *pInstance)
{
    if (pInstance->ServerId >= 0)
    {
        if (m_ServerStat != WT_FORK_SERVER_FAILED)
        {
            LinkMsg Msg;
            Msg.Cmd = LINK_CMD_CLOSE_ALL;
            Msg.Type = SRV_NORMAL_LINK;
            Msg.SID = pInstance->ServerId + 1; // SID为业务进程的ServerID，外部ServerID从1开始
            m_WRSocket.SendFdMsg(-1, &Msg, sizeof(Msg));
            pInstance->IOWatcher.stop();
            Getserverinfo();
            m_Launcher->RestartServer(pInstance->ServerId);
            pInstance->DataNoneChangeCnt = -30; // 30s等待SERVER重启完成
        }
    }
    else
    {
        WTLog::Instance().LOGOPERATE("RestartScpi\n");
        m_Launcher->RestartScpi();
        pInstance->DataNoneChangeCnt = -3; // 3s等待SERVER重启完成
    }
}

void Monitor::SrvTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;

    //升级状态监控
    UpgradeProcess();

    // 仪器处于升级状态不执行server监控内容
    if (WTDeviceInfo::Instance().GetDevUpgradeState() != UPGRADE_IDLE_STATE)
    {
        return;
    }

    // 监控SCPI/Server状态，没有定时喂狗则重启
    MonitoringProcess();

    // lic循环检测失效项
    License::Instance().CheckLicExpTime();
    // License::Instance().CheckDevTime();

    // 监控设备温度，根据温度变化对风扇转速调整，监控温度过高时，点亮设备故障error led。
    MonitoringAndCtlDeviceTemperature();
}

void Monitor::DevTimeCheckTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;

    License::Instance().CheckDevTime(); // 监测仪器系统时间
    // License::Instance().CheckLicStatus();
}

void Monitor::SysMonitorTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    Basefun::LinuxSystem((WTConf::GetDir() + "/SystemMonitor.sh").c_str());
    // system((WTConf::GetDir() + "/SystemMonitor.sh").c_str());
    ifstream SysLog(WTConf::GetDir() + "/SysMonitor.log");
    ostringstream sin;
    // 把文件流中的字符输入到字符串流中
    sin << SysLog.rdbuf();
    // 获取字符串流中的字符串
    string str = sin.str();
    WTLog::Instance().LOGOPERATE(str);
    SysLog.close();

    // 记录 /home/<USER>/bin/csv目录大小， 若该目录大于500M，删除该目录并检测算法LOG是否打开，
    // 若打开，且config.txt文件修改时间至今大于3天，关闭算法LOG
    string CsvSize = string("0");
    try
    {
        CsvSize = Basefun::shell_exec(("du -kd 0 " + WTConf::GetDir() + "/csv | awk \'{print $1}\'").c_str());
    }
    catch (const std::exception &e)
    {
        std::cerr << __FUNCTION__ << __LINE__ << e.what() << '\n';
        return;
    }

    if (CsvSize.length() > 0 && CsvSize[0] > '0' && CsvSize[0] < '9')
    {
        int CsvSizeInt = 0;
        try
        {
            CsvSizeInt = std::stol(CsvSize);
        }
        catch (const std::exception &e)
        {
            std::cerr << __FUNCTION__ << __LINE__ << e.what() << '\n';
        }

        if (CsvSizeInt > 1024 * 500)
        {
            try
            {
                Basefun::shell_exec(("rm -rf " + WTConf::GetDir() + "/csv ").c_str());
                Basefun::shell_exec(("mkdir " + WTConf::GetDir() + "/csv ").c_str());
            }
            catch (const std::exception &e)
            {
                std::cerr << __FUNCTION__ << __LINE__ << e.what() << '\n';
            }

            struct stat ConfigStat;
            string ConfFile = WTConf::GetDir() + "/config.txt";
            if (stat(ConfFile.c_str(), &ConfigStat) == 0)
            {
                time_t now = time(NULL);
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "now:" << now << ",m_time:" << ConfigStat.st_mtime << endl;
                if (now > ConfigStat.st_mtime && (now - ConfigStat.st_mtime > 3 * 24 * 60 * 60))
                {
                    // 修改config.txt配置文件,关闭算法LOG
                    stringstream NewStrs;
                    string Line;
                    string::size_type Begin;

                    ifstream Infile(ConfFile, fstream::in);
                    if (!Infile.is_open())
                    {
                        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "Config file not exit");
                        return;
                    }
                    Infile.seekg(0);

                    // 丢弃原配置
                    while (getline(Infile, Line))
                    {
                        // 空行和注释行
                        Begin = Line.find_first_not_of(' ');
                        if (Line.empty() || Line[Begin] == '#')
                        {
                            NewStrs << Line << endl;
                            continue;
                        }
                        else if (Line.find("LOG_Save_Config", Begin) != string::npos)
                        {
                            NewStrs << "LOG_Save_Config = " << 0 << endl;
                            continue;
                        }
                        else if (Line.find("output_result", Begin) != string::npos)
                        {
                            NewStrs << "output_result = " << 0 << endl;
                            continue;
                        }
                        else if (Line.find("LOG_Save_Capturedata", Begin) != string::npos)
                        {
                            NewStrs << "LOG_Save_Capturedata = " << 0 << endl;
                            continue;
                        }
                        NewStrs << Line << endl;
                    }
                    Infile.close();

                    ofstream OutFile(ConfFile, fstream::out | fstream::trunc);
                    OutFile << NewStrs.str();
                    OutFile.close();
                }
            }
        }
    }
}

void Monitor::UpgradeProcess(void)
{
    if (WTDeviceInfo::Instance().GetDevUpgradeState() == UPGRADE_ERROR_STATE ||
        WTDeviceInfo::Instance().GetDevUpgradeState() == UPGRADE_FINISH_STATE)
    {
        if (m_UpgradeRebootWait == false)
        {
            m_UpgradeRebootWait = true;
            m_UpgradeTimer.start(UPGRADE_FINISH_REBOOT_WAIT);

            stringstream Log;
            Log << "DevUpgradeState=" << WTDeviceInfo::Instance().GetDevUpgradeState() << ", ready to reboot(5s)";
#if DEBUG
            cout << Log.str() << endl;
#endif
            WTLog::Instance().LOGERR(WT_OK, Log.str());
        }
    }
}

void Monitor::UpgradeTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    if (m_UpgradeRebootWait &&
        (WTDeviceInfo::Instance().GetDevUpgradeState() == UPGRADE_ERROR_STATE ||
         WTDeviceInfo::Instance().GetDevUpgradeState() == UPGRADE_FINISH_STATE))
    {
        DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_OFF);
        Basefun::LinuxSystem("sync");
        usleep(1000);
        Basefun::LinuxSystem("reboot -f");
    }
}

void Monitor::MonitoringAndCtlDeviceTemperature(void)
{
    // TODO 风扇转速和温度的对应关系;仪器的温度范围
    // 1、获取仪器温度
    // 2、根据当前温度设置风扇的转速
    // 3、判断当前温度是否过高，是则点亮故障Led等
    // DevLib::Instance().SetErrorLed(LED_STATUS_ON);//error led
}
