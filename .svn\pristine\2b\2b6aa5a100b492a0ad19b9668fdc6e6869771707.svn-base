//*****************************************************************************
//  File: basefun.cpp
//  公共基础功能函数集合
//  Data: 2016.9.1
//*****************************************************************************

#ifndef __BASEFUN_H__
#define __BASEFUN_H__

typedef signed char int8;
typedef short int16;
typedef int int32;
//typedef long int64;

typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;
typedef unsigned long u64;
//typedef unsigned long long u64;

static const int LINE_BUFF_LEN = 3000;
static const long long ErrorEvent = 0x80000000;

// 去除c++编译器爆-Werror=stringop-truncation的问题
#define STRNCPY_USER(dst_arry, src)                   \
    do                                                \
    {                                                 \
        strncpy(dst_arry, src, sizeof(dst_arry) - 1); \
        dst_arry[sizeof(dst_arry) - 1] = 0;           \
    } while (0)

//时间类型
typedef struct
{
    u16 sec;                   // Seconds.     [0-60] (1 leap second)
    u16 min;                   // Minutes.     [0-59]
    u16 hour;                  // Hours.       [0-23]
    u16 mday;                  // Day.         [1-31]
    u16 mon;                   // Month.       [0-11]
    u16 year;                  // Year - 1900.
} TIME_TYPE;

class Basefun
{
public:
    Basefun() {};
    ~Basefun() {};

    //*******************************************************************************
    //得到系统时间
    //参数[OUT]:  tm_type:系统时间
    //返回值:成功或错误码
    //*******************************************************************************
    static int GetSysTime(TIME_TYPE *tm_type);

    //*******************************************************************************
    //将tm_type时间转换为自1970年1月1日以来持续时间的秒数
    //参数[IN]:  tm_type:系统时间
    //返回值:秒数
    //*******************************************************************************
    static u64 TimeType2Seconds(const TIME_TYPE *tm_type);

    //*******************************************************************************
    //把从1970-1-1零点零分到当前时间系统所偏移的秒数时间转换为本地时间tm_type
    //参数[IN]:  seconds:偏移的秒数
    //参数[OUT]:  tm_type:系统时间
    //返回值:成功或错误码
    //*******************************************************************************
    static int Seconds2TimeType(u64 Seconds, TIME_TYPE *tm_type);

    //*******************************************************************************
    //将数据写到指定的文件中，写入数据的大小由size指定
    //参数[IN]:  fileName: (输入)文件名  buf: (输入)内容缓冲区  size: 写入的数据长度
    //返回值: 实际写入的数据长度
    //*******************************************************************************
    static int WriteFile(const char *FileName, u8 *Buf, int Size);

    //*******************************************************************************
    //读取文件，将指定的文件内容读到buf中，读取的大小由size指定
    //参数[IN]:  fileName: (输入)文件名   size: 写入的数据长度
    //参数[OUT]:  buf: (输出)内容缓冲区
    //返回值: 实际读取的数据长度
    //*******************************************************************************
    static int ReadFile(const char *FileName, u8 *Buf, int Size);

    //*******************************************************************************
    //本函数正常运行的前提是，pbuf中的内容以'\0'结尾从ppbuf所指向的buffer中读取一行，以'\n'或'\r'为分隔符，放在line中
    //参数[IN]:  line: 行缓冲区    len:行缓冲区所能容纳的最大字节数  ppbuf: 字符串缓冲区
    //返回值:  存在行返回0，否则返回-1
    //*******************************************************************************
    static int GetLine(char *Line, u32 Len, char **ppBuf);

    //*****************************************************************************
    //比较两个 double型数据的大小,精度为：0.0001
    //参数 [IN]：dA: 数据A  dB: 数据B
    //返回值：0：相等   1：dA>dB   -1: dA<dB
    //*****************************************************************************
    static int CompareDouble(double dA, double dB,  double dEpsilon = 0.0001);
    
    //*****************************************************************************
    //给定一个整数A，计算一个整数最接近整数B的倍数的整数
    //参数 [IN]：dA: 数据A  dB: 数据B
    //返回值：最接近整数B的倍数的整数
    //*****************************************************************************
    static int Near(int A, int B);

    /*******************************************************************************
    函数: string_to_upper()
    功能: 把输入的字符串中小写转换为小写，数字和大写的不转换
    参数:
    str: 需要转换的字符串
    返回:
    None
    *******************************************************************************/
    static void string_to_upper(char *str);

    /*******************************************************************************
    函数: shell_exec()
    功能: 执行shell命令并返回操作结果
    参数:
    cmd: shell命令
    返回:
    std::string：shell命令的操作结果
    *******************************************************************************/
    static std::string shell_exec(const char* cmd);

    //*******************************************************************************
    // 函数: HexDump()
    // 功能: 显示内存中的数据
    // 参数 [IN]：Desc: 描述HexDump目的的字符串  
    //            Addr: HexDump内存地址
    //            Len: HexDump的字节数
    // 返回: 无
    //*******************************************************************************
    static void HexDump(const char *Desc, const void *Addr, int Len);

    //*******************************************************************************
    // 函数: GetSockPeerInfo()
    // 功能: 获取socket描述符的peer端的IP, 端口信息
    // 参数 [IN]：Fd: socket描述符 
    // 参数 [out]:Ip: peer端的IP地址
    //            Port: peer端的端口
    // 返回: 无
    //*******************************************************************************
    static void GetSockPeerInfo(int Fd, char Ip[16], int &Port);

    //*******************************************************************************
    // 函数: GetSockInfo()
    // 功能: 获取socket描述符的发起端（local）使用的IP和端口信息
    // 参数 [IN]：Fd: socket描述符
    // 参数 [out]:Ip: local端的IP地址
    //            Port: local端使用的端口
    // 返回: 无
    //*******************************************************************************
    static void GetSockInfo(int Fd, char Ip[16], int &Port);

    //*******************************************************************************
    // 函数: CompareDoubleAccuracy1K()
    // 功能: 比较两个double数据的大小，精度1K。
    // 参数  [IN]：dA dB: 要比较的两个数。
    // 返回: 比较结果，dA等于dB返回0，dA大于dB返回1，dA小于dB返回-1， 
    //*******************************************************************************
    static int CompareDoubleAccuracy1K(double dA, double dB);

    //*******************************************************************************
    // 函数: LinuxSystem()
    // 功能: 封装linux的system函数
    // 参数  [IN]：要执行的shell命令。
    // 返回: 无
    //*******************************************************************************
    static void LinuxSystem(const char *cmd);
    
    //*******************************************************************************
    // 函数: GetFreqIndex()
    // 功能: 获取目标再数组里的index
    // 参数  
    // 返回: index
    //*******************************************************************************
    static int GetFreqIndex(int Value, const int *Array, int Size);

    //*****************************************************************************
    // 获取指定项目的值字符串
    // 参数[IN]: Item : 配置项
    //           pos : 从文件指定位置开始查找
    // 参数[OUT]: Val : 配置值
    // 返回值: 成功或错误码
    //*****************************************************************************
    static int GetItemVal(const std::string &Line, std::string &Val, int Index = 0);

    //*****************************************************************************
    // 获取指定项目的值
    // 参数[IN]: Item : 配置项
    //           pos : 从文件指定位置开始查找
    // 参数[OUT]: Val : 配置值
    // 返回值: 成功或错误码
    //*****************************************************************************
    static int GetItemVal(const std::string &Line, int &Val, int Index = 0);

    // 创建一个路径
    static int CreateDir(const char *szPathName);

    static int IsFolderExist(const char* path);

    static int IsFileExist(const char* path);

    static int RemoveDir(const char *dir);

    // 自定义格式化函数
    static int csprintf(char *buf, const char *format, ...);

    // 自定义格式化输出函数
    static int cfprintf(int fd, const char *format, ...);
};

#endif/*__BASEFUN_H__*/
