#ifndef __INCLUDE__
#define __INCLUDE__

#include     <string.h>     //��׼�����������
#include     <math.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "AlgTesterMacro.h"
#include  "TypeDef.h"
#include  "DataDef.h"
#include  "AnalyseDef.h"
#include  "StructuresDef.h"

#include  "WTBeamforming.h"
#include  "Algorithm.h"
#include  "MIMO_Algorithm.h"

#include  "vsgDefine.h"
#include  "VsgExport.h"
#include  "alg_3gpp_vsgdef.h"
#include  "algvsg_3gpp_api.h"
#include  "alg_3gpp_vsadef.h"
#include  "algvsa_3gpp_api.h"
#include  "alg_3gpp_listdef.h"
#include  "alg_3gpp_list_api.h"
#ifdef __cplusplus
}
#endif

// 统一命名风格，方便搜索
#define WT_Algorithm_PER_Result PER_Result
#define WT_Algorithm_BF_MTK_Calibration_channelEst WTBF_MTK_Calibration_channelEst
#define WT_Algorithm_BF_BCM4360_Calibration_channelEst WTBF_BCM4360_Calibration_channelEst
#define WT_Algorithm_BF_BCM4360_Calibration_Result WTBF_BCM4360_Calibration_Result
#define WT_Algorithm_BF_BCM4360_Verification_Result WTBF_BCM4360_Verification_Result
#define WT_Algorithm_SetTimeskewParam WT_SetTimeskewParam
#define WT_Algorithm_GetAlgMaxCaptureDataNum WT_GetAlgMaxCaptureDataNum

#define WT_Algorithm_VSGSignalGenerate VSGSignalGenerate
#define WT_Algorithm_GenerateWaveWifi Alg_GenerateWaveWifi
#define WT_Algorithm_GenerateWaveCw Alg_GenerateWaveCw
#define WT_Algorithm_GenerateWaveBt Alg_GenerateWaveBt
#define WT_Algorithm_GenerateWaveGle Alg_GenerateWaveGle
#define WT_Algorithm_GenerateWisun Alg_GenerateWisun

#endif
