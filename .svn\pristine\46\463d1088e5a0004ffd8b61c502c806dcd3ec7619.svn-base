#ifndef __PN_Head_Define__
#define __PN_Head_Define__

typedef struct stStringIndex_Tag
{
	s32 index;          // 字符串索引
	const char *strDesc;      // 字符串头指针
} stStringIndex;

#define MAX_BB_RESPONSE_SIZE   (6000 - 4900)
#define MAX_RF_RESPONSE_SIZE   (6000 - 4900 + 1 + 120)
#define MAX_NS_RESPONSE_SIZE   (481)
#define MAX_IQ_IMAGE_SIZE      (64 * 2)
#define MHz     (1e6)
#define MaxDescCount 1024

#define strCMIMORefFileInfo "CMIMORefFileInformation"
#define strFileInfo         "FileInformation"
#define strStreamListInfo   "StreamListInformation"
#define strStartData        "StartData"
#define strDescription      "Description"
#define strSampleCount      "Sample Count"
#define strSampleFreq       "Frequency"
#define strDataType         "Data Type"
#define strRFGain           "RF Gain"
#define strCopyRight        "Copyright"
#define strCenterFreq       "Center Freq"
#define strIQGainImb        "IQGainImb"
#define strIQPhaseImb       "IQPhaseImb"
#define strDCOffset_I       "DC_Offset_I"
#define strDCOffset_Q       "DC_Offset_Q"
#define strTime_skew        "Time Skew"
#define strExtAttEnable     "ExternalAttEnable"
#define strModType          "ModType"
#define strExtAtt           "ExternalAtt"
#define strExtGain          "ExtGain"
#define strTriggerLevel     "TriggerLevel"
#define strVsaAmpl          "VsaAmpl"
#define strFreqOffset       "FreqOffset"
#define strStreamNum        "StreamNum"
#define strSymbolCnt        "Symbol Count"
#define strPsduLen          "Psdu Length"
#define strDataCnt          "Data Count"
#define strDataRate         "Data Rate"
#define strDataLength       "Data Length(Octets)"
#define strFlag8080         "Flag8080"
#define strScene            "Secne"
#define strSegmentCnt       "Segment"
#define strSubType       	"SubType"

#define strStandard         "Standard"
#define strBandwidth        "Bandwidth"
#define strMcs              "ModulationCodingScheme"
#define strCoding           "Coding"
#define strPatternsCnt      "NumPatterns"
#define strNss              "NumSpatialStreams"
#define strNsts             "NumSpaceTimeStreams"
#define strNumSymbols       "NumSymbols"
#define strNumTones         "NumTones"
#define strSkipSymbols      "SkipSymbols"
#define strPointsCnt        "NumPoints"
#define strLeftBracket      "<"
#define strLeftSlashBracket "</"
#define strRightBracket     ">"

#define strBBResponse       "BasebandResponse"
#define strRfResponse       "RfResponse"
#define strBBFreqStart      "bb_freqStart"
#define strRfFreqStart      "rf_freqStart"
#define strNSResponse    	"NSResponse"
#define strNSFreqStart      "ns_freqStart"
#define strIQImage          "IQImage"
#define strIQImageStart  	"IQImageStart"

#define str11acStandard     "AC"
#define str11nStandard      "N"
#define strBCCCoding        "BCC"
#define strLDPCCoding       "LDPC"

#define strInt16            "int16"
#define strDouble           "double"
#define striTest            "iTest"
#define strSampleFreqUnit   "MHz"
#define strCenterFreqUnit   "Hz"
#define strGainUint         "dB"
#define strPhaseUint        "deg"
#define strDataRateUint     "Mbps"
#define strClockRate        "ClockRate"
#define strGap              "Gap"
#define strPNStructData         "PNStructData"
#define strPNStructVer          "PNStructVer"
#define strPNStructSize         "PNStructSize"
#define strPNStruct             "PNStruct"

#define strRUCarrierStructVer          "RUCarrierStructVer"
#define strRUCarrierStructData         "RUCarrierStructData"
#define strRUCarrierStructSize         "RUCarrierStructSize"
#define strRUCarrierStruct             "RUCarrierStruct"


#define strExternSettingStructVer          "ExternSettingStructVer"
#define strExternSettingStructData         "ExternSettingStructData"
#define strExternSettingStructSize         "ExternSettingStructSize"
#define strExternSettingStruct             "ExternSettingStruct"

#define strChannelModePathLoss              "Channel mode path loss"
#define strEncodingTag                      "EncodingTag"
#define strSN								"Create by"
#define strFwVersion						"Fw"
#define strIFG								"Ifg"
#define strRepeat							"Repeat"
#define strPnHead							"Pn_head"
#define strPnTail							"Pn_tail"
#define strPnIndex							"PnIndex"
#define strPnStreamCount					"PnStreamCount"
#define strPnCount							"PnCount"

enum
{
	eumKw_Desc,
	eumKw_DataCnt,
	eumKw_SampleFreq,
	enumKw_DataType,
	enumKw_RFGain,
	enumKw_CenterFreq,
	enumKw_IQGainImb,
	enumKw_IQPhaseImb,
	enumKw_DC_Offset_I,
	enumKw_DC_Offset_Q,
	enumKw_Time_Skew,

	enumKw_ExtAttEnable,
	enumKw_ModType,
	enumKw_ExtAtt,
	enumKw_TriggerLevel,
	enumKw_VsaAmpl,
	enumKw_FreqOffset,
	enumKw_Flag8080,
	enumKw_Scene,
    enumKw_ClockRate,
    enumKw_PNStructSize,
    enumKw_PNStructVer,
    enumKw_PNStructData,
    enumKw_Gap,
    enumKw_Encoding,
	enumKw_IFG,
	enumKw_Repeat,
	enumKw_PnHead,
	enumKw_PnTail,
	enumKw_PnIndex,
	enumKw_IQImage,
};

#pragma region WIFI_PSDU
#define strFrameCtrl            "FrameCtrl User"
#define strMACAddress1          "MAC Address1"
#define strMACAddress2          "MAC Address2"
#define strMACAddress3          "MAC Address3"
#define strMACAddress4          "MAC Address4"
#define strMacHeaderEnable      "MAC header enable"
#define strTrue                 "true"
#define strFalse                "false"
#define strON                   "ON"
#define strOFF                  "OFF"
#define strUserDefinedPsdu      "User defined PSDU"
#pragma endregion


#pragma region BlueToothDescription
#define strLTAdd            "LT_ADDR"
#define strLAP              "LAP(HEX)"
#define strUAP              "UAP(HEX)"
#define strNAP              "NAP(HEX)"
#define strDataLen          "Payload Length(Octets)"
#define strPayLoad          "PayLoad"
#define strPRBS9            "PRBS9"
#define strPRBS15           "PRBS15"
#define str11110000         "11110000"
#define str10101010         "10101010"
#define str11111111         "11111111"
#define str00000000         "00000000"
#define str00001111         "00001111"
#define str01010101         "01010101"
#define strRandom           "Random"
#define strDataWhitEnable   "data whitening enabled"
#define strDataWhitDisable  "data whitening disabled"
#define strPRBS9INIT        "PRBS9 Init"
#define strModulate         "Modulation Type"
#define strNSS                "Max NSS"
#define strRU                "RU NUM"
#define strGILTFSize                "GILTFSize"

#define str_1Mbps           "1Mbps"
#define str_2Mbps           "2Mbps"
#define str_3Mbps           "3Mbps"

#define str_DM1             "DM1"
#define str_DH1             "1-DH1"
#define str_DM3             "DM3"
#define str_DH3             "1-DH3"
#define str_DM5             "1-DM5"
#define str_DH5             "1-DH5"
#define str_AUX1            "AUX1"
#define str_2_DH1           "2-DH1"
#define str_2_DH3           "2-DH3"
#define str_2_DH5           "2-DH5"
#define str_3_DH1           "3-DH1"
#define str_3_DH3           "3-DH3"
#define str_3_DH5           "3-DH5"
#define str_HV1             "HV1"
#define str_HV2             "HV2"
#define str_HV3             "HV3"
#define str_DV              "DV"
#define str_EV3             "1-EV3"
#define str_EV4             "1-EV4"
#define str_EV5             "1-EV5"
#define str_2_EV3           "2-EV3"
#define str_2_EV5           "2-EV5"
#define str_3_EV3           "3-EV3"
#define str_3_EV5           "3-EV5"
#define str_BlE             "LE Test"
#define str_BlE_1M          "LE 1M Test"
#define str_BlE_2M          "LE 2M Test"
#define str_BlE_Coded       "LE Coded Test"


#pragma region BTEnum

enum
{
	//LCP
	enPacketType_ID,
	enPacketType_NULL,
	enPacketType_POLL,
	enPacketType_FHS,
	//ACL(1M):
	//  enPacketType_NULL,
	//  enPacketType_POLL,
	//  enPacketType_FHS,
	enPacketType_DM1,
	enPacketType_DH1,
	enPacketType_DM3,
	enPacketType_DH3,
	enPacketType_DM5,
	enPacketType_DH5,
	enPacketType_AUX1,
	//ACL(2-3M):
	//  enPacketType_NULL,
	//  enPacketType_POLL,
	//  enPacketType_FHS,
	//  enPacketType_DM1,
	enPacketType_2_DH1,
	enPacketType_2_DH3,
	enPacketType_2_DH5,
	enPacketType_3_DH1,
	enPacketType_3_DH3,
	enPacketType_3_DH5,
	//SCO(1M):
	//  enPacketType_NULL,
	//  enPacketType_POLL,
	//  enPacketType_FHS,
	//  enPacketType_DM1,
	enPacketType_HV1,
	enPacketType_HV2,
	enPacketType_HV3,
	enPacketType_DV,
	//Extended SCO(1M):
	//  enPacketType_NULL,
	//  enPacketType_POLL,
	enPacketType_EV3,
	enPacketType_EV4,
	enPacketType_EV5,
	//Extended SCO(2-3M):
	//  enPacketType_NULL,
	//  enPacketType_POLL,
	enPacketType_2_EV3,
	enPacketType_2_EV5,
	enPacketType_3_EV3,
	enPacketType_3_EV5,

	enPacketType_LE_Test,
	enPacketType_LE_1M_Test = enPacketType_LE_Test,
	enPacketType_LE_2M_Test,
	enPacketType_LE_Coded_Test,
	enPacketType_Cnt
};

enum
{

	enBR_1M = 1,
	enEDR_2M = 2,
	enEDR_3M = 3,
	enLE_Test = 4,

	enRate_Cnt
};

enum
{

	enBT_Psdu_Type_PRBS9 = 0,
	enBT_Psdu_Type_11110000 = 1,
	enBT_Psdu_Type_10101010 = 2,
	enBT_Psdu_Type_PRBS15 = 3,
	enBT_Psdu_Type_11111111 = 4,
	enBT_Psdu_Type_00000000 = 5,
	enBT_Psdu_Type_00001111 = 6,
	enBT_Psdu_Type_01010101 = 7,
	enBT_Psdu_Type_Random = 8,

	enBT_Psdu_Type_END
};

enum
{
    enBLE_1M = 0,
    enBLE_2M = 1,
    enBLE_Coded = 2,

    enBLE_Coded_Cnt
};

enum
{
    enBT_sub_Type_BR = 0,
    enBT_sub_Type_EDR = enBT_sub_Type_BR,
    enBT_sub_Type_BLE,

    enBT_sub_Type_END
};

#pragma endregion BTEnum

#pragma endregion BlueToothDescription


#endif