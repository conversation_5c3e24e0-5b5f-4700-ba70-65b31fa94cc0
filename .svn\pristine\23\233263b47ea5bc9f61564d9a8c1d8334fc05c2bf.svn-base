#pragma once

class PrintParam
{
public:
    PrintParam();
    virtual ~PrintParam();
    virtual int RUN() { return 0; };
    void SetFileName(string fileName) { m_fileName = fileName; };
    void SetTitle(string title) { m_title = title; };
    void SetEqulStr(string str) { m_EqualStr = str; };
    virtual string Blank(int n);
    virtual void PrintAdd(string msg, bool isAdd = true);
    virtual void PrintHeader(string info = "");
protected:
    string m_title;
    string m_newLine;
    FILE *m_fp;
    string m_fileName;
    string m_EqualStr;
};

class PrintParam_VSA : public PrintParam
{
public:
    PrintParam_VSA(VsaParameter *obj, string tileName = "VsaParameter", string fileName = "");
    virtual ~PrintParam_VSA();
    virtual int RUN();
private:
    VsaParameter *m_VsaParameter;
};

class PrintParam_VSA_Ext : public PrintParam
{
public:
    PrintParam_VSA_Ext(ExtendVsaParameter *obj, string tileName = "ExtendVsaParameter", string fileName = "");
    virtual ~PrintParam_VSA_Ext();
    virtual int RUN();
private:
    ExtendVsaParameter *m_VsaParameter;
};

class PrintParam_VSG : public PrintParam
{
public:
    PrintParam_VSG(VsgParameter *obj, string tileName = "VsgParameter", string fileName = "");
    virtual ~PrintParam_VSG();
    virtual int RUN();

private:
    VsgParameter *m_VsgParameter;
};

class PrintParam_VSG_Ext : public PrintParam
{
public:
    PrintParam_VSG_Ext(ExtendVsgParameter *obj, string tileName = "ExtendVsgParameter", string fileName = "");
    virtual ~PrintParam_VSG_Ext();
    virtual int RUN();
private:
    ExtendVsgParameter *m_VsgParameter;
};

class PrintParam_VsgWaveParameter : public PrintParam
{
public:
    PrintParam_VsgWaveParameter(VsgWaveParameter *obj, string tileName = "VsgWaveParameter", string fileName = "");
    virtual ~PrintParam_VsgWaveParameter();
    virtual int RUN();

private:
    VsgWaveParameter *m_VsgWaveParameter;
};

class PrintParam_PnItem : public PrintParam
{
public:
    PrintParam_PnItem(vector<PnItemHead_API> *ItemHead, vector<ExtPnItem> *ExtItem, string tileName = "PnPartten", string fileName = "");
    virtual ~PrintParam_PnItem();
    virtual int RUN();

private:
    vector<PnItemHead_API> *m_PnItemHead;
    vector<ExtPnItem> *m_PnItem;
};

class PrintParam_AnalyzeParam : public PrintParam
{
public:
    PrintParam_AnalyzeParam(AnalyzeParam *param, int demod, string tileName = "AnalyzeParam", string fileName = "");
    virtual ~PrintParam_AnalyzeParam() {};
    virtual int RUN();

private:
    AnalyzeParam *m_AnalyzeParam;
    int m_demod;
};


class PrintParam_PN : public PrintParam
{
public:
    PrintParam_PN(GenWaveWifiStruct_API *obj, string tileName = "GenWaveWifiStruct", string fileName = "pnParameters.txt");
    virtual ~PrintParam_PN();
    virtual int RUN();

protected:
    void PrintCommonParam();
    void PrintEnd();
    void PrintALL();
    void Print_11AG();
    void Print_11B();
    void Print_11N();
    void Print_11AC();
    void Print_11AX();
    void Print_11AX_SU();
    void Print_11AX_MU();
    void Print_11AX_TB();
    void Print_11AX_TF(int psduType);
    void Print_11BE();
    void Print_11BE_MU();
    void Print_11BE_TB();
    void Print_TB_NDP(TB_NDP & NDP);
    string PrintPSDU(WIFI_PSDU *psdu, int n = 0);
    void Print_RU(MUMIMO_RU *RU, int RUCnt);
    void Print_RU(int segment, TB_MUMIMO_RU *RU);
    void PrintQMat(stringstream &msg, MUMIMO_QMat &Qmat, int blankOffset);
    void SaveStructBin();
private:
    GenWaveWifiStruct_API *m_pnParameters;
};
