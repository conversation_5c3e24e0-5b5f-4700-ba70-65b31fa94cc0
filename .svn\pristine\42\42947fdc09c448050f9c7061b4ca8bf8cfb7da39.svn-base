#重置算法内存配置文件

#最小复位间隔
MinResetGap = 60          #单位 s

#超过Warnning, 找个空闲的时间复位
WarnningEnbale = 1        #使能标志
MemWarnning = 5500000     #单位 KiB
SwapWarnning = 0          #单位 KiB
IdleTime = 60             #单位 s
AlgUsedWarnning = 3500000 #单位 KiB

#超过Error, 卡住新的算法调用,当前算法调用完成后复位
ErrorEnbale = 1         #使能标志
MemError = 7000000      #单位 KiB
SwapError = 0           #单位 KiB
AlgUsedError = 5000000  #单位 KiB

#总内存，用于计算百分比，适配增加内存的仪器
MemTotal = 7754360      #单位 KiB
SwapTotal = 3631100     #单位 KiB,当前不使用

