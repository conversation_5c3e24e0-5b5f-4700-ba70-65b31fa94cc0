//*****************************************************************************
//  File: wtdevice.c
//  WT300设备驱动程序
//  Data: 2016.7.18
//*****************************************************************************
#include <linux/types.h>
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/fs.h>
#include <linux/init.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/fcntl.h>
#include <linux/signal.h>
#include <linux/sched.h>
#include <asm/current.h>
#include <linux/pci.h>
#include <linux/err.h>
#include <linux/dma-direct.h>
#include <linux/interrupt.h>
#include <linux/spinlock.h>
#include <asm/atomic.h>
#include <linux/jiffies.h>
#include <linux/mm.h> //remap_pfn_range
#include <linux/interrupt.h>
#include <linux/time.h>

#include "hwlib.h"
#include "rfswitch.h"
#include "wtbackdefine.h"
#include "wtbacklib.h"
#include "wtswitch.h"
#include "wtbusilib_418.h"

#define DEV_MAJOR 233

#define BACK_PLANE_REG_BAR 2 // 背板寄存器所使用的bar
#define BUSI_BOARD_REG_BAR 1 // 业务板寄存器所使用的bar

#define CLASS_NAME "wtdev"
#define DRV_NAME "wt_drv"

struct workqueue_struct *wt_queue = NULL;
struct dev_unit *pBPdev = NULL;
int tf_delay_compensate = 350;

wait_queue_head_t busi_queue[DEV_BOARD_NUM];

int xdma_probe_one(struct pci_dev *pdev, const struct pci_device_id *id, void **pxdma);
void xdma_remove_one(struct pci_dev *pdev);
int xdma_user_isr_register(void *dev_hndl, unsigned int mask, irq_handler_t handler, void *dev);

// 设备所有硬件资源
struct wtdev_t
{
    int unit_num;                           // 业务单元数量
    struct dev_unit units[MAX_UNIT_NUM];    // 业务单元板
    struct dev_unit back;                   // 背板
    spinlock_t bplock;                      // 背板所用的spinlock
    spinlock_t lock[DEV_BOARD_NUM];         // VSA/VSG板所用的spinlock，每块业务板一个
    struct rw_semaphore bpsem;              // 背板信号量
    struct rw_semaphore sem[DEV_BOARD_NUM]; // 防止DMA期间读写寄存器用的信号量
    struct class *pclass;                   // 驱动所属class
    void *pxdma;                            // XDMA所使用对象
};

static struct wtdev_t wtdev;
static int g_devnum = 0;
static int g_vsanum = 0;
static int g_vsgnum = 0;
static int g_busiboardnum = 0;

static void set_vsa_done(struct work_struct *done)
{
    struct dev_unit *pdev = container_of(done, struct dev_unit, done);
    spin_lock_bh(pdev->lock);
    //down_write(pdev->sem);
    pdev->complete = WT_RX_TX_STATUS_DONE;
    //pdev->vsadonecnt++;

    if (pdev->testertype == HW_WT418)
    {
        // 双工缓存状态信息
        pdev->vsg_stat.vsg_run_flag = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_STARTING);

        if (pdev->vsg_stat.vsg_run_flag)
        {
            pdev->vsg_stat.vsg_dac_reg = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_DAC_REG);
            pdev->vsg_stat.vsg_shift_reg[0] = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_SHIFT_0);
            pdev->vsg_stat.vsg_shift_reg[1] = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_SHIFT_1);

            pdev->vsg_stat.sw_shift_reg[0] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_0_HI);
            pdev->vsg_stat.sw_shift_reg[1] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_0_LO);
            pdev->vsg_stat.sw_shift_reg[2] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_1);
            pdev->vsg_stat.sw_shift_reg[3] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_2);
            pdev->vsg_stat.sw_shift_reg[4] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_3);
        }
    }
    else
    {
        pdev->vsg_stat.vsg_run_flag = 0;
    }

    spin_unlock_bh(pdev->lock);
    //up_write(pdev->sem);

    if (pdev->async_queue)
    {
        kill_fasync(&pdev->async_queue, SIGIO, POLL_IN);
    }
    return;
}

static void set_vsa_donev2(unsigned long data)
{
    struct dev_unit *pdev = container_of((struct tasklet_struct *)data, struct dev_unit, tasklet);
    spin_lock_bh(pdev->lock);
    //down_write(pdev->sem);
    pdev->complete = WT_RX_TX_STATUS_DONE;
    //pdev->vsadonecnt++;

    if (pdev->testertype == HW_WT418)
    {
        // 双工缓存状态信息
        pdev->vsg_stat.vsg_run_flag = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_STARTING);

        if (pdev->vsg_stat.vsg_run_flag)
        {
            pdev->vsg_stat.vsg_dac_reg = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_DAC_REG);
            pdev->vsg_stat.vsg_shift_reg[0] = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_SHIFT_0);
            pdev->vsg_stat.vsg_shift_reg[1] = wt_read_direct_reg(pdev, BUSI_DUPLEX_VSG_BK_STAT_SHIFT_1);

            pdev->vsg_stat.sw_shift_reg[0] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_0_HI);
            pdev->vsg_stat.sw_shift_reg[1] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_0_LO);
            pdev->vsg_stat.sw_shift_reg[2] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_1);
            pdev->vsg_stat.sw_shift_reg[3] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_2);
            pdev->vsg_stat.sw_shift_reg[4] = wt_read_direct_reg(pdev, BUSI_DUPLEX_SW_BK_STAT_SHIFT_3);
        }
    }
    else
    {
        pdev->vsg_stat.vsg_run_flag = 0;
    }

    spin_unlock_bh(pdev->lock);
    //up_write(pdev->sem);

    pdev->setdonecnt++;
    dbg_print("set_vsa_donev2 vsadonecnt%d, pdev->setdoncnt%d\n", atomic_read(&pdev->vsadonecnt), pdev->setdonecnt);
    if (pdev->async_queue)
    {
        kill_fasync(&pdev->async_queue, SIGIO, POLL_IN);
    }
    return;
}

static void set_vsg_done(struct work_struct *done)
{
    struct dev_unit *pdev = container_of(done, struct dev_unit, done);
    spin_lock_bh(pdev->lock);
    //down_write(pdev->sem);
    pdev->complete = WT_RX_TX_STATUS_DONE;
    //up_write(pdev->sem);
    spin_unlock_bh(pdev->lock);
    if (pdev->async_queue)
    {
        kill_fasync(&pdev->async_queue, SIGIO, POLL_IN);
    }
    return;
}

static void set_vsg_donev2(unsigned long data)
{
    struct dev_unit *pdev = container_of((struct tasklet_struct *)data, struct dev_unit, tasklet);
    spin_lock_bh(pdev->lock);

    pdev->complete = WT_RX_TX_STATUS_DONE;

    spin_unlock_bh(pdev->lock);

    if (pdev->async_queue)
    {
        kill_fasync(&pdev->async_queue, SIGIO, POLL_IN);
    }
    return;
}


static void  set_vsa_vsg_trigtimeout(struct work_struct *trigtimeout)
{
    struct dev_unit *pdev = container_of(trigtimeout, struct dev_unit, trigtimeout);
    spin_lock_bh(pdev->lock);
    //down_write(pdev->sem);
    pdev->complete = WT_RX_TX_STATUS_TIMEOUT;
    //pdev->vsadonecnt++;
    spin_unlock_bh(pdev->lock);
    //up_write(pdev->sem);
    dbg_print("set_vsa_vsg_trigtimeout complete=%d\n",pdev->complete);
    if (pdev->async_queue)
    {
        kill_fasync(&pdev->async_queue, SIGIO, POLL_IN);
    }
    return;
}

static void  set_vsa_vsg_trigtimeoutv2(unsigned long data)
{
    struct dev_unit *pdev = container_of((struct tasklet_struct *)data, struct dev_unit, trigtasklet);
    spin_lock_bh(pdev->lock);
    //down_write(pdev->sem);
    pdev->complete = WT_RX_TX_STATUS_TIMEOUT;
    //pdev->vsadonecnt++;
    spin_unlock_bh(pdev->lock);
    //up_write(pdev->sem);
    dbg_print("set_vsa_vsg_trigtimeout complete=%d\n", pdev->complete);
    if (pdev->async_queue)
    {
        kill_fasync(&pdev->async_queue, SIGIO, POLL_IN);
    }
    return;
}


// done中断中不能操作寄存器，因为此时DMA可能正在运行
static irqreturn_t wt_done_irq_handler(int irq, void *arg)
{
    struct dev_unit *pdev = (struct dev_unit *)arg;

    atomic_inc(&pdev->vsadonecnt);
    pdev->doncntdebug++;
    dbg_print("%s %d done interrupt irq=%d, doncntdebug=%d\n",
              (pdev->type == DEV_TYPE_VSA ? "vsa" : "vsg"), pdev->id, irq, pdev->doncntdebug);

    //queue_work(wt_queue, &pdev->done);
    tasklet_schedule(&pdev->tasklet);

    return IRQ_HANDLED;
}

// trigger timeout中断中不能操作寄存器，因为此时DMA可能正在运行
static irqreturn_t wt_trigtimeout_irq_handler(int irq, void *arg)
{
    struct dev_unit *pdev = (struct dev_unit *)arg;

    atomic_inc(&pdev->vsadonecnt);
    dbg_print("%s %d trigtimeout interrupt irq=%d\n",
              (pdev->type == DEV_TYPE_VSA ? "vsa" : "vsg"), pdev->id, irq);
    //queue_work(wt_queue, &pdev->trigtimeout);
    tasklet_schedule(&pdev->trigtasklet);

    return IRQ_HANDLED;
}


static int wtdev_open(struct inode *node, struct file *flip)
{
    int FPGAVersion, FPGADate;
    struct dev_unit *pdev = container_of(node->i_cdev, struct dev_unit, cdev);
    flip->private_data = pdev;

    // 不能正常读取FPGA的版本信息，不允许打开设备文件，以免死机。
    FPGAVersion = wt_read_direct_reg(pdev, FPGA_VERSION);
    FPGADate = wt_read_direct_reg(pdev, FPGA_COMPILE_DATE);

    if (FPGAVersion == 0 || ((FPGAVersion & 0xFFF) == 0xFFF) || FPGADate < 20000101 || ((FPGADate & 0xFFF) == 0xFFF))
    {
        dbg_print("BusiType%d Board%d FPGAVersion=0x%x FPGADate=0x%x, open error!\n", pdev->type, pdev->id, FPGAVersion, FPGADate);
        pdev->is_open_ok = 0;
    }
    else
    {
        dbg_print("BusiType%d Board%d FPGAVersion=0x%x FPGADate=0x%x, open success\n", pdev->type, pdev->id, FPGAVersion, FPGADate);
        pdev->is_open_ok = 1;
    }

    atomic_inc_return(&pdev->open_cnt);
    return 0;
}

static long wtdev_ioctl(struct file *flip, unsigned int cmd, unsigned long arg)
{
    int FuncId = IOCTL_CMD_FUNC(cmd);
    int DataLength = IOCTL_CMD_DATALENGTH(cmd);
    int Ret = WT_OK;

    struct dev_unit *pdev = flip->private_data;

    if (unlikely(FuncId <= IOCTL_CMD_BEGIN || FuncId >= IOCTL_CMD_END))
    {
        dbg_print("wt_driver cmd FuncId error!\n");
        return WT_CMD_FUNID_ERROR;
    }

    if (unlikely(pdev->is_open_ok == 0 && FuncId != WRITE_DIRECT_REG && FuncId != READ_DIRECT_REG))
    {
        dbg_print("wtdev_ioctl FuncId=%d, DataLen=%d, wt_driver not ok!\n", FuncId, DataLength);
        return WT_UNITBOARD_NOT_EXIST;
    }

    if (unlikely((FuncId >= CRYPTO_MEM_INIT && FuncId <= CRYPTO_MEM_GET_CODE)))
    {
        dbg_print("wtdev_ioctl FuncId=%d, DataLen=%d!\n", FuncId, DataLength);
    }

    if (pdev->testertype == HW_WT418)
    {
        if (down_write_trylock(pdev->sem) == 0)
        {
            if (unlikely(wait_event_timeout(*pdev->wait_head,
                                            down_write_trylock(pdev->sem), msecs_to_jiffies(2000)) == 0))
            {
                dbg_print("kernel working functionid = %d! type:%d\n", pdev->functionid, pdev->type);
                if (pdev->functionid == WRITE_BASE_FPGA_EARSE ||
                    pdev->functionid == WRITE_BASE_FPGA_UPGRADE ||
                    pdev->functionid == WRITE_BASE_FPGA_WRITEONEPAGE ||
                    pdev->functionid == FPGA_RELOAD ||
                    pdev->functionid == SET_FLASH_4BYTE_MODE)
                {
                    dbg_print("busi board upgrade working!\n");
                    return WT_UPGRADE_HOLD_KERNEL;
                }

                // DMA出错导致信号量未释放时，增加自旋锁检测，避免多线程去复位VSA DMA。
                if (spin_trylock(pdev->lock))
                {
                    // 等待超时复位VSA
                    dbg_print("===wt448 reset busi board\n");
                    down_write(pdev->sem); // 避免在未持锁的情况下释放信号量
                    up_write(pdev->sem);           // 释放被占用的信号量
                    wake_up(pdev->wait_head);
                    spin_unlock(pdev->lock);
                    //dbg_print("up_write:pdev->sem\n");
                }
                return WT_WAIT_SEM_TIMEOUT;
            }
        }
    }
    else
    {
        if (unlikely(pdev->type == DEV_TYPE_BACK || FuncId == SET_SWITCH_VSG_CTL3 ||
                     FuncId == SET_RX_PORT || FuncId == SET_TX_PORT))
        {
            spin_lock(pBPdev->lock);
        }
        else if (likely(pdev->type != DEV_TYPE_BACK))
        {
            if (down_write_trylock(pdev->sem) == 0)
            {
                if (unlikely(wait_event_timeout(*pdev->wait_head,
                                                down_write_trylock(pdev->sem), msecs_to_jiffies(2000)) == 0))
                {
                    dbg_print("kernel working functionid = %d!\n", pdev->functionid);
                    if (pdev->functionid == WRITE_BASE_FPGA_EARSE ||
                        pdev->functionid == WRITE_BASE_FPGA_UPGRADE ||
                        pdev->functionid == WRITE_BASE_FPGA_WRITEONEPAGE ||
                        pdev->functionid == FPGA_RELOAD ||
                        pdev->functionid == SET_FLASH_4BYTE_MODE)
                    {
                        dbg_print("busi board upgrade working!\n");
                        return WT_UPGRADE_HOLD_KERNEL;
                    }

                    // DMA出错导致信号量未释放时，增加自旋锁检测，避免多线程去复位VSA DMA。
                    if (spin_trylock(pdev->lock))
                    {
                        spin_lock(pBPdev->lock);
                        // 等待超时复位VSA
                        dbg_print("wt448 reset busi board\n");
                        down_write_trylock(pdev->sem); // 避免在未持锁的情况下释放信号量
                        up_write(pdev->sem);           // 释放被占用的信号量
                        wake_up(pdev->wait_head);
                        spin_unlock(pBPdev->lock);
                        spin_unlock(pdev->lock);
                    }
                    return WT_WAIT_SEM_TIMEOUT;
                }
            }
        }
    }

    spin_lock_bh(pdev->lock);
    if (pdev->testertype == HW_WT418 && pdev->type == DEV_TYPE_BACK && IsBusiBoardComponent(FuncId))
    {
        pdev->functionid = FuncId;
        Ret = pFuncArrayWT418[FuncId](DataLength, (void *)arg, pdev);
        pdev->functionid = -1;
    }
    else if (pFuncArray[FuncId] != 0)
    {
        pdev->functionid = FuncId;
        Ret = pFuncArray[FuncId](DataLength, (void *)arg, pdev);
        pdev->functionid = -1;
    }
    else
    {
        dbg_print("wt_driver cmd FuncId error!\n");
    }

    if (pdev->testertype == HW_WT418)
    {
        up_write(pdev->sem);
        wake_up(pdev->wait_head);
    }
    else
    {
        if (unlikely(pdev->type == DEV_TYPE_BACK || FuncId == SET_SWITCH_VSG_CTL3 ||
                     FuncId == SET_RX_PORT || FuncId == SET_TX_PORT))
        {
            spin_unlock(pBPdev->lock);
        }
        else if (likely(pdev->type != DEV_TYPE_BACK))
        {
            up_write(pdev->sem);
            wake_up(pdev->wait_head);
        }
    }
    spin_unlock_bh(pdev->lock);
    return Ret;
}

static int wtdev_fasync(int fd, struct file *flip, int mode)
{
    struct dev_unit *pdev = flip->private_data;
    return fasync_helper(fd, flip, mode, &pdev->async_queue);
}

static int wtdev_close(struct inode *node, struct file *flip)
{
    struct dev_unit *pdev = flip->private_data;

    if (atomic_dec_and_test(&pdev->open_cnt))
    {
        // 如果是背板则下位机服务停止时，power亮橙灯
        if (pdev->testertype == HW_WT418)
        {
            // 下位机服务停止时，power亮橙灯。
            wt418_WriteLedIOExtBit((int)LED_R_POWER_RED, OFF, pdev);
            dbg_print("----------Set power led to orange!\n");
        }
        else if (pdev->type == DEV_TYPE_BACK)
        {
            // 下位机服务停止时，power亮橙灯。
            wt_WriteLedIOExtBit((int)LED_R_POWER_RED, OFF, pdev);
            dbg_print("----------Set power led to orange!\n");
        }
    }
    return 0;
}

static int wtdev_mmap(struct file *flip, struct vm_area_struct *vma)
{
    struct dev_unit *pdev = flip->private_data;
    unsigned long offset = vma->vm_pgoff << PAGE_SHIFT;
    unsigned long pfn_start = (virt_to_phys(pdev->HwParamBuf.pBuf) >> PAGE_SHIFT) + vma->vm_pgoff;
    unsigned long virt_start = (unsigned long)pdev->HwParamBuf.pBuf + offset;
    unsigned long size = vma->vm_end - vma->vm_start;
    int ret = 0;
    dbg_print("phy: 0x%lx, offset: 0x%lx, size: 0x%lx\n", pfn_start << PAGE_SHIFT, offset, size);
    ret = remap_pfn_range(vma, vma->vm_start, pfn_start, size, vma->vm_page_prot);
    if (ret)
        dbg_print("%s: remap_pfn_range failed at [0x%lx  0x%lx]\n", __func__, vma->vm_start, vma->vm_end);
    else
        dbg_print("%s: map 0x%lx to 0x%lx, size: 0x%lx\n", __func__, virt_start,vma->vm_start, size);
    return ret;
}

static struct file_operations wtdev_ops =
    {
        .open = wtdev_open,
        .unlocked_ioctl = wtdev_ioctl,
        .fasync = wtdev_fasync,
        .release = wtdev_close,
        .owner = THIS_MODULE,
        .mmap = wtdev_mmap,
};

static struct pci_device_id wt_dev_tbl[] =
    {
        {PCI_DEVICE(WT_VENDOR_ID, WT_BACK_DEV_ID)},
        {PCI_DEVICE(WT_VENDOR_ID, WT_BUSI_DEV_ID)},
        {PCI_DEVICE(WT_VENDOR_ID, WT418_BUSI_DEV_ID)},
        {
            0,
        },
};

static int add_device(struct cdev *pcdev, struct device **pdev, int type)
{
    int ret = WT_OK;
    dev_t dev;
    char name[16];

    if (type == DEV_TYPE_VSA)
    {
        snprintf(name, 16, "wtvsa%d", g_vsanum++);
    }
    else if (type == DEV_TYPE_VSG)
    {
        snprintf(name, 16, "wtvsg%d", g_vsgnum++);
    }
    else
    {
        snprintf(name, 16, "wtback");
    }

    dev = MKDEV(DEV_MAJOR, g_devnum++);

    ret = register_chrdev_region(dev, 1, name);
    if (ret < 0)
    {
        dbg_print("alloc_chrdev_region failed\n");
        goto fail_alloc_chrdev;
    }

    cdev_init(pcdev, &wtdev_ops);

    ret = cdev_add(pcdev, dev, 1);
    if (ret < 0)
    {
        dbg_print("add cdev failed\n");
        goto fail_add_cdev;
    }

    *pdev = device_create(wtdev.pclass, NULL, dev, NULL, name);
    if (IS_ERR(*pdev))
    {
        dbg_print("device create failed\n");
        ret = PTR_ERR(*pdev);
        goto fail_create_device;
    }
    return 0;

fail_create_device:
    cdev_del(pcdev); // 删除字符设备文件
fail_add_cdev:
    unregister_chrdev_region(dev, 1); // 释放设备号
fail_alloc_chrdev:
    return ret;
}

static int get_solt(struct dev_unit *punit, struct pci_dev *pdev)
{
    dbg_print("pdev->bus->parent->number = %d\n pdev->bus->number = %d", pdev->bus->parent->number, pdev->bus->number);
    if (pdev->bus->parent->number == WT448_PARENT_BUS_NUMBER_NORMAL)
    {
        switch (pdev->bus->number)
        {
        case WT448_BUS_NUMBER_BUSI0:
            punit->slot = WT_BUSI_BOARD_SLOT_0;
            break;
        case WT448_BUS_NUMBER_BUSI1:
            punit->slot = WT_BUSI_BOARD_SLOT_1;
            break;
        case WT448_BUS_NUMBER_BUSI2:
            punit->slot = WT_BUSI_BOARD_SLOT_2;
            break;
        case WT448_BUS_NUMBER_BUSI3:
            punit->slot = WT_BUSI_BOARD_SLOT_3;
            break;
        default:
            dbg("pdev->pcidev->bus->number = %d\n", pdev->bus->number);
            punit->slot = WT_BUSI_BOARD_SLOT_UNKOWN;
            break;
        }
    }
    else if (pdev->bus->parent->number == WT418_PARENT_BUS_NUMBER_NORMAL && pdev->bus->number == WT418_BUS_NUMBER_BUSI0)
    {
        punit->slot = WT_BUSI_BOARD_SLOT_0;
    }
    else
    {
        switch (pdev->bus->number)
        {
        case WT448_FORWARD_BUS_NUMBER_BUSI0:
            punit->slot = WT_BUSI_BOARD_SLOT_0;
            break;
        case WT448_FORWARD_BUS_NUMBER_BUSI1:
            punit->slot = WT_BUSI_BOARD_SLOT_1;
            break;
        case WT448_FORWARD_BUS_NUMBER_BUSI2:
            punit->slot = WT_BUSI_BOARD_SLOT_2;
            break;
        case WT448_FORWARD_BUS_NUMBER_BUSI3:
            punit->slot = WT_BUSI_BOARD_SLOT_3;
            break;
        default:
            dbg("pdev->pcidev->bus->number = %d\n", pdev->bus->number);
            punit->slot = WT_BUSI_BOARD_SLOT_UNKOWN;
            break;
        }
    }
    return WT_OK;
}

static int wt_probe(struct pci_dev *pdev, const struct pci_device_id *id)
{
    struct dev_unit *punit;
    void *iobase;
    int i;
    int ret = WT_OK;
    u8 revision;
    dbg_print("wt_probe,LINE = %d\n", __LINE__);
    ret = pci_enable_device(pdev);
    if (ret != 0)
    {
        dbg_print("enable pci device failed\n");
        return ret;
    }

    pci_set_master(pdev);

    if (pci_set_dma_mask(pdev, DMA_BIT_MASK(32)))
    {
        pci_disable_device(pdev);
        dbg_print("set dma mask failed\n");
        return -1;
    }

    if (pci_request_regions(pdev, DRV_NAME))
    {
        pci_disable_device(pdev);
        dbg_print("pci_request_regions failed\n");
        return -1;
    }

    if (pdev->device == WT418_BUSI_DEV_ID)
    {
        iobase = pci_ioremap_bar(pdev, BUSI_BOARD_REG_BAR);
    }
    else if (pdev->device != WT_BACK_DEV_ID)
    {
        iobase = pci_ioremap_bar(pdev, BUSI_BOARD_REG_BAR);
    }
    else
    {
        iobase = pci_ioremap_bar(pdev, BACK_PLANE_REG_BAR);
    }

    dbg_print("device=%#x pci_ioremap_bar = %p\n", id->device, iobase);

    if (!iobase)
    {
        pci_disable_device(pdev);
        dbg_print("pci_ioremap_bar failed\n");
        return -1;
    }

    if (pci_read_config_byte(pdev, PCI_REVISION_ID, &revision))
    {
        pci_disable_device(pdev);
        dbg_print("pci_read_config_byte PCI_REVISION_ID failed");
        return -1;
    }

    dbg_print("pci_bus_number=%d, parent pci_bus_number=%d\n", pdev->bus->number, pdev->bus->parent->number);
    if (pdev->device == WT_BACK_DEV_ID)
    {
        punit = &wtdev.back;
        pBPdev = punit;
        punit->type = DEV_TYPE_BACK;
        punit->pcidev = pdev;
        punit->iobase = iobase;
        punit->revision = revision;
        punit->version = VERSION_B;
        punit->lock = &wtdev.bplock;
        punit->sem = &wtdev.bpsem;
        punit->read_delay = 0;
        punit->write_delay = 500;
        init_rwsem(punit->sem);
        spin_lock_init(punit->lock);
        pci_set_drvdata(pdev, punit);
        add_device(&punit->cdev, &punit->pdev, punit->type);
        // 获取背板硬件版本号
        wt_GetUnitBoardHWVersion(punit);
        // 射频板真值表初始化
        rf_switch_table_init(punit);
        // //开关板真值表初始化
        // WT_InitSwitch(punit);
        wt_write_direct_reg(punit, BACK_VSA_MASTER_SLOT, SISO_MODE);
        wt_write_direct_reg(punit, BACK_VSG_MASTER_SLOT, SISO_MODE);
        wt_write_direct_reg(punit, BACK_TRIG_SELELT, TRIGGER_PORT_DIG);
    }
    else if (pdev->device == WT_BUSI_DEV_ID)
    {
        punit = &wtdev.units[wtdev.unit_num];
        pci_set_drvdata(pdev, punit);
        spin_lock_init(&wtdev.lock[wtdev.unit_num / BOARD_UNIT_NUM]);
        init_rwsem(&wtdev.sem[wtdev.unit_num / BOARD_UNIT_NUM]);
        xdma_probe_one(pdev, id, &wtdev.pxdma);
        dbg_print("xdma_probe_one xdma=%p\n", wtdev.pxdma);
        for (i = 0; i < BOARD_UNIT_NUM; i++, punit++)
        {
            punit->type = i;
            punit->id = wtdev.unit_num / BOARD_UNIT_NUM;
            punit->pcidev = pdev;
            punit->done_irq = (punit->type == DEV_TYPE_VSA) ? 1 : 0;
            punit->trigtimeout_irq = (punit->type == DEV_TYPE_VSA) ? 3 : 4 ;
            punit->read_delay = 0;
            punit->write_delay = 500;
            xdma_user_isr_register(wtdev.pxdma, 0x1u << punit->done_irq, wt_done_irq_handler, punit);
            xdma_user_isr_register(wtdev.pxdma, 0x1u << punit->trigtimeout_irq, wt_trigtimeout_irq_handler, punit);
            punit->iobase = iobase;
            punit->revision = revision;
            punit->version = VERSION_B;
            punit->ifgstatus = 0;
            punit->lock = &wtdev.lock[wtdev.unit_num / BOARD_UNIT_NUM];
            punit->sem = &wtdev.sem[wtdev.unit_num / BOARD_UNIT_NUM];
            punit->neighbor = i == 0 ? punit + 1 : punit - 1;
            punit->wait_head = &busi_queue[wtdev.unit_num / BOARD_UNIT_NUM];
            punit->HwParamBuf.pBuf = NULL;
            punit->HwParamBuf.offset = 0;
            punit->HwParamBuf.size = 0;
            punit->HwParamBuf.pBuf = kmalloc(HW_PARAM_SIZE, GFP_KERNEL);
            dbg_print("(long long)HwParamBuf = %#llx , size = %d\n", (long long)punit->HwParamBuf.pBuf, HW_PARAM_SIZE);
            if (!punit->HwParamBuf.pBuf)
            {
                dbg_print("vmalloc failed\n");
                return -1;
            }
            punit->HwParamBuf.size = HW_PARAM_SIZE;
            INIT_WORK(&punit->trigtimeout, set_vsa_vsg_trigtimeout);
            tasklet_init(&punit->trigtasklet, set_vsa_vsg_trigtimeoutv2, &punit->trigtasklet);
            if (punit->type == DEV_TYPE_VSG)
            {
                INIT_WORK(&punit->done, set_vsg_done);
                tasklet_init(&punit->tasklet, set_vsg_donev2, &punit->tasklet);
                punit->number = g_vsgnum;
            }
            else
            {
                INIT_WORK(&punit->done, set_vsa_done);
                tasklet_init(&punit->tasklet, set_vsa_donev2, &punit->tasklet);
                punit->number = g_vsanum;
            }
            get_solt(punit, pdev);
            add_device(&punit->cdev, &punit->pdev, punit->type);
        }
        g_busiboardnum++;
        wtdev.unit_num += BOARD_UNIT_NUM;
    }
    else if (pdev->device == WT418_BUSI_DEV_ID)
    {
        // FENG:418只有一块业务板，需要在dev层充当devback的入口
        {
            punit = &wtdev.back;
            pci_set_drvdata(pdev, punit);
            spin_lock_init(&wtdev.bplock);
            init_rwsem(&wtdev.bpsem);

            pBPdev = punit;
            punit->type = DEV_TYPE_BACK;
            punit->pcidev = pdev;
            punit->iobase = iobase;
            punit->revision = revision;
            punit->version = VERSION_A;
            punit->lock = &wtdev.bplock;
            punit->sem = &wtdev.bpsem;
            punit->slot = WT_BUSI_BOARD_SLOT_0;
            punit->wait_head = &busi_queue[wtdev.unit_num / BOARD_UNIT_NUM];
            punit->doncntdebug = 0;
            punit->setdonecnt = 0;
            punit->dev_virtual_addr.VirtualAddrMode = 0;

            add_device(&punit->cdev, &punit->pdev, punit->type);
            // 获取背板硬件版本号
            wt_GetUnitBoardHWVersion(punit);
            // 射频板真值表初始化
            rf_switch_table_init(punit);
            // 开关板真值表初始化
            // WT_InitSwitch(punit);
        }

        punit = &wtdev.units[wtdev.unit_num];
        // pci_set_drvdata(pdev, punit);
        xdma_probe_one(pdev, id, &wtdev.pxdma);
        dbg_print("xdma_probe_one xdma=%p\n", wtdev.pxdma);
        for (i = 0; i < BOARD_UNIT_NUM; i++, punit++)
        {
            punit->type = i;
            punit->id = wtdev.unit_num / BOARD_UNIT_NUM;
            punit->pcidev = pdev;
            punit->done_irq = (punit->type == DEV_TYPE_VSA) ? 1 : 0;
            punit->trigtimeout_irq = (punit->type == DEV_TYPE_VSA) ? 3 : 4;
            xdma_user_isr_register(wtdev.pxdma, 0x1u << punit->done_irq, wt_done_irq_handler, punit);
            xdma_user_isr_register(wtdev.pxdma, 0x1u << punit->trigtimeout_irq, wt_trigtimeout_irq_handler, punit);
            punit->iobase = iobase;
            punit->revision = revision;
            punit->version = VERSION_A;
            punit->ifgstatus = 0;
            punit->lock = &wtdev.bplock;
            punit->sem = &wtdev.bpsem;
            punit->neighbor = i == 0 ? punit + 1 : punit - 1;
            punit->wait_head = &busi_queue[wtdev.unit_num / BOARD_UNIT_NUM];
			punit->dev_virtual_addr.VirtualAddrMode = 0;
            punit->HwParamBuf.pBuf = NULL;
            punit->HwParamBuf.offset = 0;
            punit->HwParamBuf.size = 0;
            punit->HwParamBuf.pBuf = kmalloc(HW_PARAM_SIZE, GFP_KERNEL);
            dbg_print("(long long)HwParamBuf = %#llx , size = %d\n", (long long)punit->HwParamBuf.pBuf, HW_PARAM_SIZE);
            if (!punit->HwParamBuf.pBuf)
            {
                dbg_print("vmalloc failed\n");
                return -1;
            }
            punit->HwParamBuf.size = HW_PARAM_SIZE;
            punit->doncntdebug = 0;
            punit->setdonecnt = 0;
            INIT_WORK(&punit->trigtimeout, set_vsa_vsg_trigtimeout);
            tasklet_init(&punit->trigtasklet, set_vsa_vsg_trigtimeoutv2, &punit->trigtasklet);
            if (punit->type == DEV_TYPE_VSG)
            {
                INIT_WORK(&punit->done, set_vsg_done);
                tasklet_init(&punit->tasklet, set_vsg_donev2, &punit->tasklet);
                punit->number = g_vsgnum;
            }
            else
            {
                INIT_WORK(&punit->done, set_vsa_done);
                tasklet_init(&punit->tasklet, set_vsa_donev2, &punit->tasklet);
                punit->number = g_vsanum;
            }
            get_solt(punit, pdev);
            add_device(&punit->cdev, &punit->pdev, punit->type);
			wt_GetUnitBoardHWVersion(punit);
        }
        wtdev.unit_num += BOARD_UNIT_NUM;
    }
    if (pBPdev)
    {
        pBPdev->busi_num = g_busiboardnum;
    }
    return 0;
}

static void free_dev(struct dev_unit *pdev)
{
    if (pdev->pdev)
    {
        device_destroy(wtdev.pclass, pdev->cdev.dev);
        cdev_del(&pdev->cdev);
        unregister_chrdev_region(pdev->cdev.dev, 1);
        pdev->pdev = NULL;
    }
}

static void wt_remove(struct pci_dev *pdev)
{
    if (pdev->device != WT_BACK_DEV_ID)
    {
        pci_disable_msi(pdev);
        xdma_remove_one(pdev);
    }
    pci_release_regions(pdev);
    pci_disable_device(pdev);
}

static struct pci_driver wt_pci_drv =
    {
        .name = DRV_NAME,
        .id_table = wt_dev_tbl,
        .probe = wt_probe,
        .remove = wt_remove,
};

static int __init wtdev_init(void)
{
    int i = 0;
    memset(&wtdev, 0, sizeof(wtdev));

    wt_queue = create_workqueue("wt_queue");
    if (!wt_queue)
    {
        dbg_print("create workqueue failed");
        return -1;
    }

    for (i = 0; i < DEV_BOARD_NUM; i++)
    {
        init_waitqueue_head(&busi_queue[i]);
    }

    wtdev.pclass = class_create(THIS_MODULE, CLASS_NAME);
    if (IS_ERR(wtdev.pclass))
    {
        destroy_workqueue(wt_queue);
        dbg_print("class_create failed\n");
        return PTR_ERR(wtdev.pclass);
    }

    if (pci_register_driver(&wt_pci_drv))
    {
        dbg_print("pci_register_driver failed\n");
        destroy_workqueue(wt_queue);
        class_destroy(wtdev.pclass);
        return -1;
    }

    return 0;
}

static void __exit wtdev_exit(void)
{
    int i;

    if (wtdev.back.pdev)
    {
        free_dev(&wtdev.back);
    }

    for (i = 0; i < wtdev.unit_num; i++)
    {
        if (wtdev.units[i].pdev)
        {
            if (wtdev.units[i].HwParamBuf.size > 0)
            {
                dbg_print("kfree(pdev->HwParamBuf.pBuf)\n");
                kfree(wtdev.units[i].HwParamBuf.pBuf);
                wtdev.units[i].HwParamBuf.size = 0;
                wtdev.units[i].HwParamBuf.offset = 0;
            }
            free_dev(&wtdev.units[i]);
        }
    }

    class_destroy(wtdev.pclass);
    pci_unregister_driver(&wt_pci_drv);
    lo_loop_filter_table_release();
    destroy_workqueue(wt_queue);
}

// static int __init __get_free_pages_init(void)
// {
//     unsigned long addr = __get_free_pages(GFP_KERNEL, 6); // 分配 64 个物理页
//     if (!addr)
//     {
//         return -ENOMEM;
//     }
//     else
//     {
//         printk("__get_free_pages Successfully! , addr = 0x%lx\n", addr);
//         return 0;
//     }
// }
// static void __exit __get_free_pages_exit(void)
// {
//     if (addr)
//     {
//         free_pages(addr, 6); // 释放所分配的物理页
//         printk("free_pages ok!\n");
//     }
//     printk("exit!\n");
// }
// module_init(__get_free_pages_init);
// module_exit(__get_free_pages_exit);

module_init(wtdev_init);
module_exit(wtdev_exit);
// EXPORT_SYMBOL_GPL(wtdev_init);
// EXPORT_SYMBOL_GPL(wtdev_exit);
MODULE_LICENSE("GPL");
