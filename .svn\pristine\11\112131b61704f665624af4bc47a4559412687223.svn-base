/*
 * @Description: 3GPP：NR5G配置分析参数相关命令
 * @Autor: 
 * @Date: 20231103
 */
#ifndef SCPI_3GPP_ALZ_NR5G_H_
#define SCPI_3GPP_ALZ_NR5G_H_

#include "basehead.h"
#include "scpi/scpi.h"
#include "scpi_3gpp_common.h"

#ifdef __cplusplus
extern "C"
{
#endif
    //**************************************************************************************************
    // UL
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_UL_SetAlzRFPhaseCompensation(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzDuplexing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSlotPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSpecialSlotIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzNSValue(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellNum(scpi_t *context);

    //UL Cell
    scpi_result_t SCPI_NR5G_UL_SetAlzCellIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBW(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellPhysicalID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellDmrsTypeAPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWMaxRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWK0U(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpCyclicPrefix(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpTransformPrecoder(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpResourceAllocationType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpFreqHoppingMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSMaxLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSAddPosInd(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSUseSixteenDmrs(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSScramblingId(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSNpuschID(scpi_t *context);

    // scpi_result_t SCPI_NR5G_UL_SetAlzUEID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzChannelType(scpi_t *context);
    //UL PUSCH
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschCellIdex(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschMappingType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBDetectMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschLayerNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschAntennaNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschCDMGroupsWoData(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsAntennaPort(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSybolLength(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenInit(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenNscid(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschGroupOrSequenceHopping(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschModulate(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschChannelCodeingState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschScrambling(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschMCS(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschUEID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRVIndex(scpi_t *context);

    //**************************************************************************************************
    // DL
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_DL_SetAlzDuplexing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSlotPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSpecialSlotIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzRFPhaseCompensation(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellNum(scpi_t *context);
    
    //DL Cell
    scpi_result_t SCPI_NR5G_DL_SetAlzCellIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBW(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellPhysicalID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellDmrsTypeAPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWMaxRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWK0U(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCyclicPrefix(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschVrbToPrbInterLeaver(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMcsTable(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschResourceAlloc(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschRBGSizeType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschConfigType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMaxLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschAdditionalPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschUseSixteenDmrs(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymbolOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetFDRes(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetBitMap(scpi_t *context);

    //DL Scheduled Slot Allocation
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotCellIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchOffSetRefType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchCase(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchLength(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchBurstSetPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchHalfFrameIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdcchState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMapType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBDetMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschBitMap(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschLayerNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschAntennaNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCDMGroupsWoData(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsSymbLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsAntPort(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsInitType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschNSCID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCodewords(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschModulate(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschChanCodingState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschScrambling(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUsePdschScrambleID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDataScrambleID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUeID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMCS(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRvIdx(scpi_t *context);

    scpi_result_t SCPI_NR5G_DL_SetAlzChannelType(scpi_t *context);

    //**************************************************************************************************
    // Measure Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_Measure_SetAlzSubFrameIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSlotIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzDmrsConsState(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzEvmDeleteDcFlag(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzEVMSubcarrier(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSymbolIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzWindowPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzExcAbnormalSymbol(scpi_t *context);
    
#ifdef __cplusplus
}
#endif

namespace cellular {
namespace alz {
namespace nr {

// nr5g limits
scpi_result_t SetVsaNrLimitMode(scpi_t *context);
scpi_result_t SetVsaNrLimitModEvmRmsState(scpi_t *context);
scpi_result_t SetVsaNrLimitModEvmRmsLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModEvmPeakState(scpi_t *context);
scpi_result_t SetVsaNrLimitModEvmPeakLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModMerrRmsState(scpi_t *context);
scpi_result_t SetVsaNrLimitModMerrRmsLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModMerrPeakState(scpi_t *context);
scpi_result_t SetVsaNrLimitModMerrPeakLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModPherrRmsState(scpi_t *context);
scpi_result_t SetVsaNrLimitModPherrRmsLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModPherrPeakState(scpi_t *context);
scpi_result_t SetVsaNrLimitModPherrPeakLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModFreqErrState(scpi_t *context);
scpi_result_t SetVsaNrLimitModFreqErrLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitModIqOffsetState(scpi_t *context);
scpi_result_t SetVsaNrLimitModIqOffsetValue(scpi_t *context);
scpi_result_t SetVsaNrLimitModIBEState(scpi_t *context);
scpi_result_t SetVsaNrLimitModIBEGenMin(scpi_t *context);
scpi_result_t SetVsaNrLimitModIBEGenEvm(scpi_t *context);
scpi_result_t SetVsaNrLimitModIBEGenPwr(scpi_t *context);
scpi_result_t SetVsaNrLimitModIBEIqImag(scpi_t *context);
scpi_result_t SetVsaNrLimitModIBEIqOffset(scpi_t *context);
scpi_result_t SetVsaNrLimitModSpecFlatState(scpi_t *context);
scpi_result_t SetVsaNrLimitModSpecFlatRange(scpi_t *context);
scpi_result_t SetVsaNrLimitModSpecFlatMax(scpi_t *context);
scpi_result_t SetVsaNrLimitModSpecFlatEdgeDist(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecObwState(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecObwLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecSemState(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecSemStartFreq(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecSemStopFreq(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecSemLimitPwr(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecSemRbw(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecUtraRelState(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecUtraRelLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecUtraAbsState(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecUtraAbsPwr(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecNrRelState(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecNrRelLimit(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecNrAbsState(scpi_t *context);
scpi_result_t SetVsaNrLimitSpecNrAbsPwr(scpi_t *context);
scpi_result_t SetVsaNrLimitSemTestTol(scpi_t *context);
scpi_result_t SetVsaNrLimitAclrTestTol(scpi_t *context);
scpi_result_t SetVsaNrLimitPwrState(scpi_t *context);
scpi_result_t SetVsaNrLimitPwrOff(scpi_t *context);
scpi_result_t SetVsaNrLimitPwrTestTol(scpi_t *context);

} // namespace nr
} // namespace alz
} // namespace cellular


namespace {

/**
 * @brief 判断当前vsa的协议类型是否为Nr
 * 
 * @param context 
 * @return int 
 */
inline int IsAlzNr(scpi_t *context)
{
    int iRet = WT_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->vsaAlzParam.analyzeParam3GPP.Standard != ALG_3GPP_STD_5G)
    {
        iRet = WT_3GPP_STANDARD_MISMATCH;
    }

    return iRet;
}

/**
 * @brief 获取SCPI分析参数的Nr
 * 
 * @param context 
 * @return Alg_3GPP_AlzIn5g& 
 */
inline Alg_3GPP_AlzIn5g &GetNr(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->vsaAlzParam.analyzeParam3GPP.NR;
}

/**
 * @brief Get the Nr Vsa Command Numbers from SCPI
 * 
 * @param context 
 * @param params &value, min, max
 * @return int 
 */
template <typename T>
int GetNrScpiCommandNumbers(scpi_t *context, std::vector<std::tuple<T, T, T>> &params)
{
    int iRet = IsAlzNr(context);
    if (iRet != WT_ERR_CODE_OK) {
        return iRet;
    }

    return cellular::alz::GetScpiCommandNumbers(context, params);
}

/**
 * @brief Set the Nr Vsa Value In Range object
 * 
 * @param context 
 * @param field 要赋值的字段
 * @param min 最小值
 * @param max 最大值
 * @return int 
 */
template <typename FieldType, typename MinType, typename MaxType>
int SetNrVsaValueInRange(scpi_t *context, FieldType &field, MinType min, MaxType max)
{
    int iRet = IsAlzNr(context);
    if (iRet != WT_ERR_CODE_OK) {
        return iRet;
    }

    return cellular::alz::SetVsaValueInRange(context, field, min, max);
}

/**
 * @brief Set the Nr Vsa Value From Specified Values object
 * 
 * @param context 
 * @param field 要赋值的字段
 * @param validValues 可变长度的有效值列表
 * @return int 
 */
template <typename FieldType>
int SetNrVsaValueFromSpecified(scpi_t *context, FieldType &field, std::initializer_list<FieldType> validValues)
{
    int iRet = IsAlzNr(context);
    if (iRet != WT_ERR_CODE_OK) {
        return iRet;
    }

    return cellular::alz::SetVsaValueFromSpecified(context, field, validValues);
}

} // namespace

#endif
