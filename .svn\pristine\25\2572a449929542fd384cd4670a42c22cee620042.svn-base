#include <vector>
#include <thread>
#include <map>
#include <set>
#include <atomic>

#include "sncmanager.h"
#include "wtypes.h"
#include "wtlog.h"
#include "devmgr.h"
#include "vsa.h"
#include "wtev++.h"
#include "../general/devlib/devlib.h"

#define DEV_BUSY_WAITTING_CHECK_INTERVAL (1) //仪器资源忙等待检测间隔
#define DEV_BUSY_MAX_RETYR_CNT (5) //仪器资源忙等待最大重试次数

SncCalMgr &SncCalMgr::Instance(void)
{
    static SncCalMgr SncCalMgr;
    return SncCalMgr;
}

SncCalMgr::SncCalMgr()
{
    // 单元关联端口数量
    DevPortMap PortMap;
    PortMap.ModId = -1;
    PortMap.PortMask = 0;
    for (int UnitMapRfport = WT_RF_1, unit = 0; UnitMapRfport < WT_RF_MAX; ++UnitMapRfport)
    {
        DevLib::Instance().GetModId(DEV_TYPE_VSA, UnitMapRfport, unit);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(UnitMapRfport) << Pout(unit) << Pout(PortMap.ModId) << Pout(PortMap.PortMask) << endl;
        if (PortMap.ModId < 0)
        {
            PortMap.ModId = unit;
            PortMap.PortMask |= 1 << (UnitMapRfport - WT_RF_1);
        }
        else if (PortMap.ModId == unit)
        {
            PortMap.PortMask |= 1 << (UnitMapRfport - WT_RF_1);
        }
        else
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "SncCalMgr Mod%d PortMask=%#x\n", PortMap.ModId, PortMap.PortMask);
            m_DevPortMap.push_back(PortMap);
            std::unique_ptr<SncCalTask> Task(new (std::nothrow) SncCalTask(m_EvLoop, PortMap.PortMask));
            m_SncCalTask.push_back(std::move(Task));
            PortMap.ModId = unit;
            PortMap.PortMask = 1 << (UnitMapRfport - WT_RF_1);
        }
    }
    // 最后一组多加一次
    WTLog::Instance().WriteLog(LOG_DEBUG, "SncCalMgr Mod%d PortMask=%#x\n", PortMap.ModId, PortMap.PortMask);
    m_DevPortMap.push_back(PortMap);
    std::unique_ptr<SncCalTask> Task(new (std::nothrow) SncCalTask(m_EvLoop, PortMap.PortMask));
    m_SncCalTask.push_back(std::move(Task));
}

SncCalMgr::~SncCalMgr()
{

}

void SncCalMgr::Run()
{
    m_EvLoop.run();
}

int SncCalMgr::Start(int Force)
{
    if (!GetNoiseCalValid() || Force)
    {
        std::unique_lock<std::mutex> ErrLock(Mutex);
        for (auto &CalTask : m_SncCalTask)
        {
            if (CalTask->GetSncStatus() != WT_NOISE_CAL_STATE_RUNNING)
            {
                CalTask->NoiseCalStart();
            }
        }
        WTLog::Instance().LOGOPERATE("SncCalMgr Start Force=" + to_string(Force));
    }
    return WT_OK;
}

int SncCalMgr::Query(void)
{
    int Status = WT_NOISE_CAL_STATE_DONE;
    for (auto &CalTask : m_SncCalTask)
    {
        Status = CalTask->GetSncStatus();
        if (Status == WT_NOISE_CAL_STATE_ERR_DONE)
        {
            break;
        }
    }
    return Status;
}

int SncCalMgr::Stop(void)
{
    std::unique_lock<std::mutex> ErrLock(Mutex);
    for (auto &CalTask : m_SncCalTask)
    {
        CalTask->NoiseCalStop();
    }
    WTLog::Instance().LOGOPERATE("SncCalMgr Stop");
    return WT_OK;
}

int SncCalMgr::GetNoiseCalValid(void)
{
    int Status = true;
    int PortList[8] = {1, 1, 1, 1, 1, 1, 1, 1};
    CAL_NOISE_DATA_STATE SncStatus[8] = {COMP_FILE_NO_EXISTS};
    wt_calibration_get_noise_data_status(PortList, SncStatus);
    for (auto State : SncStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "GetNoiseCalValid Port State=%d\n", State);
        if (State != COMP_DATA_VALID)
        {
            Status = false;
        }
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetNoiseCalValid Status=%d\n", Status);
    return Status;
}

SncCalTask::SncCalTask(const wtev::loop_ref &Loop, int PortMask)
    : WTBase(Loop, DEV_RES_VSA), m_TimerEv(Loop)
{
    m_TimerEv.set<SncCalTask, &SncCalTask::SrvTimerCb>(this);
    m_TimerEv.set(DEV_BUSY_WAITTING_CHECK_INTERVAL, DEV_BUSY_WAITTING_CHECK_INTERVAL);

    for (int i = 0; i < sizeof(m_PortLlist) / sizeof(m_PortLlist[0]); i++)
    {
        if (GetBit(PortMask, i))
        {
            m_PortLlist[i] = true;
            if (m_VsgNeedModId < 0)
            {
                DevLib::Instance().GetModId(DEV_TYPE_VSG, i + WT_RF_1, m_VsgNeedModId);
            }
            if (m_NeedModId < 0)
            {
                DevLib::Instance().GetModId(DEV_TYPE_VSA, i + WT_RF_1, m_NeedModId);
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "m_PortLlist[%d] = %d, m_VsgNeedModId=%d, m_NeedModId=%d\n", i, m_PortLlist[i], m_VsgNeedModId, m_NeedModId);
        }
    }
    m_PortMask = PortMask;
    MakeSncCalParam();
    m_RunData.SncStatus = SNC_TASK_INIT;

    // 重新绑定回调函数
    m_Notify = std::bind(&SncCalTask::Complete, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
    m_FinishEv.stop();
    m_FinishEv.set<SncCalTask, &SncCalTask::HwOpFin>(this);
    m_FinishEv.start();
}

SncCalTask::~SncCalTask()
{
    FreeModPair();
    // 释放已经申请的内存
    if (m_NoiseCalStatus.power_ready_to_avg != nullptr)
    {
        for (int i = 0; i < ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span) + 1; i++)
        {
            delete[] m_NoiseCalStatus.power_ready_to_avg[i];
            m_NoiseCalStatus.power_ready_to_avg[i] = nullptr;
        }
        delete[] m_NoiseCalStatus.power_ready_to_avg;
        m_NoiseCalStatus.power_ready_to_avg = nullptr;
    }
    char pBuf[512];
    sprintf(pBuf, "SncCalTask distructor PortList=%#x\n", m_PortMask);
    WTLog::Instance().LOGOPERATE(pBuf);
}

// 硬件完成回调
void SncCalTask::Complete(int Type, int DevId, int Status)
{
    if (Type == m_ModType) // 只有正在运行状态的模块才能通知，防止重复通知
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "SncCalTask Complete Type%d, DevId%d, Status%d\n", Type, DevId, Status);
        m_RunData.ModStatus = Status;
        if (Status == WT_RX_TX_STATE_DONE)
        {
            m_RunData.SncStatus = SNC_TASK_HW_IQR;
        }
        else
        {
            m_RunData.SncStatus = SNC_TASK_ERROR;
        }
        m_FinishEv.send();
    }
}

void SncCalTask::HwOpFin(wtev::async &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    m_TimerEv.stop();   //计时器停止
    NoiseCalProc();
}

void SncCalTask::SrvTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    m_TimerEv.stop();   //计时器停止
    m_RunData.SncStatus = SNC_TASK_RETRY;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SrvTimerCb PortList=%#x\n", m_PortMask);
    m_FinishEv.send();
}

int SncCalTask::GetCaptureData()
{
    DataBufInfo &DataInfo = m_DataInfo;
    int DateSize = m_VsaParam.GetSigLen();

    // 如果信号buffer长度小于信号数据大小则重新申请内存
    if (DataInfo.BufLen < DateSize || DataInfo.Buf == nullptr)
    {
        DataInfo.Buf.reset(new (std::nothrow) char[DateSize]);
        if (DataInfo.Buf == nullptr)
        {
            DataInfo.BufLen = 0;
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
            return WT_ALLOC_FAILED;
        }

        DataInfo.BufLen = DateSize;
    }

    int Ret = DevLib::Instance().VSACaptureData(m_NeedModId, DataInfo.Buf.get(), DateSize);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get capture data failed");
        return Ret;
    }

    DataInfo.DataLen = DateSize;

    return WT_OK;
}

int SncCalTask::SetAlzParam()
{
    int Ret = WT_OK;
    do
    {
        AlzParamComm commonAnalyzeParam;
        Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_COMMON, &commonAnalyzeParam, sizeof(AlzParamComm));
        CheckBreak(Ret);
        AlzParamFFT FFTAnalyzeParam;
        FFTAnalyzeParam.Rbw = m_NoiseCalStatus.noise_info.rbw;
        Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_FFT, &FFTAnalyzeParam, sizeof(AlzParamFFT));
        CheckBreak(Ret);
    } while (0);
    return Ret;
}

int SncCalTask::AlzSingleData()
{
    int Ret = WT_OK;
    m_Alg.Clear();
    ExtendEVMStu ExtendEVM;
    m_Alg.SetAnalyzeMode(0);
    m_Alg.SetData(m_DataInfo, 0, m_VsaParam, m_CalParam, ExtendEVM);

    Ret = m_Alg.AlzFrameData(0, false);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "analysis failed");
    }

    return Ret;
}

int SncCalTask::AllocModPair(void)
{
    // time_t Now;
    // time(&Now);
    // tm *T = localtime(&Now);
    int Ret = WT_OK;

    // alloc vsg
    m_VsgAllocModId = -1;
    Ret = DevMgr::Instance().AllocMod(DEV_TYPE_VSG, m_Notify, m_VsaParam.AllocTimeout, 1121, m_VsgAllocModId, m_VsgNeedModId);
    if (Ret != WT_OK || m_VsgAllocModId < 0)
    {
        m_VsgAllocModId = -1;
        // WTLog::Instance().WriteLog(LOG_DEBUG, "[WTBase]AllocMod Fail, m_ModType=%d m_NeedModId=%d Ret=%d[0X%X]\n", m_ModType, m_NeedModId, Ret, Ret);
        WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SncCalTask]AllocMod Fail, m_ModType=%d m_NeedModId=%d Ret=%d[0X%X]\n",
                                   m_ModType, m_NeedModId, Ret, Ret);
    }
    if (Ret != WT_OK)
    {
        return Ret;
    }

    // alloc vsa
    m_AllocModId = -1;
    Ret = AllocMod(m_Notify, m_VsaParam.AllocTimeout, 1121);
    if (Ret != WT_OK || m_AllocModId < 0)
    {
        // VSG已经申请到了, 释放掉
        DevMgr::Instance().FreeMod(DEV_TYPE_VSG, m_VsgAllocModId);
        m_VsgAllocModId = -1;
        m_AllocModId = -1;
        return Ret;
    }
    return Ret;
}

int SncCalTask::FreeModPair(void)
{
    // free vsg
    WTLog::Instance().WriteLog(LOG_DEBUG, "FreeModPair m_VsgAllocModId=%d, m_AllocModId=%d\n", m_VsgAllocModId, m_AllocModId);
    if (m_VsgAllocModId >= 0)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FreeMod VSG");
        DevMgr::Instance().FreeMod(DEV_TYPE_VSG, m_VsgAllocModId);
        m_VsgAllocModId = -1;
    }
    // free vsa
    FreeMod();
    return WT_OK;
}

int SncCalTask::SetMod(double power)
{
    (void)power;
    m_VsaParam.RFPort = m_NoiseCalStatus.CurPort + 1;
    m_VsaParam.Freq = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].freq * 1e6;
    m_VsaParam.Ampl = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].power_level;
    m_VsaParam.SamplingTime = m_NoiseCalStatus.noise_info.smp_time;
    m_VsaParam.SamplingFreq = m_NoiseCalStatus.noise_info.smp_rate;

    VSAConfigType Config;
    Config.FreqOffsetHz = 0.0;
    Config.DeviceMode = DEVICE_MODE_SISO;
    Config.RFPortState = WT_RF_STATE_PI;
    Config.TrigType = WT_TRIG_TYPE_FREE_RUN;
    Config.NoiseCompensation = CAL_NOISE_CAL_DATA; // 噪声补偿
    Config.RFPort = m_VsaParam.RFPort;
    Config.SamplingTime = m_VsaParam.SamplingTime;
    Config.SamplingFreq = m_VsaParam.SamplingFreq;
    Config.Freq = m_VsaParam.Freq;
    Config.Ampl = m_VsaParam.Ampl;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SncCalTask::SetMod " << Pout(Config.RFPort) << Pout(Config.Freq) << Pout(Config.Ampl) << endl;
    memset(&m_CalParam, 0, sizeof(m_CalParam));
    int Ret = DevLib::Instance().VSASetConfig(m_AllocModId, Config, m_CalParam);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set vsa param to mod failed");
        return Ret;
    }

    double VsaAmpl = m_CalParam.rx_gain_parm.rx_sw_gain.actual_mpl + m_VsaParam.ExtGain;
    if (abs(VsaAmpl - m_VsaParam.Ampl) >= 1)
    {
        WTLog::Instance().LOGERR(WT_VSA_SETCONFIG_FAILED, "SncCalTask set vsa Ampl over range");
    }

    m_RunData.IsConfig = true;
    return Ret;
}

int SncCalTask::Start()
{
    int Ret = WT_OK;
    do
    {
        if (!m_RunData.IsConfig)
        {
            WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Mod is not configured");
            return WT_MOD_NUM_ERROR;
        }

        m_RunData.ModStatus = WT_RX_TX_STATE_RUNNING;
        m_RunData.CurDataNoAlzFlag = true;
        Ret = DevLib::Instance().VSAStart(m_AllocModId);
        if (Ret != WT_OK)
        {
            m_RunData.ModStatus = WT_RX_TX_STATE_ERR_DONE;
            WTLog::Instance().LOGERR(Ret, "start module failed");
            break;
        }
        usleep(2);
    } while (0);

    return Ret;
}

int SncCalTask::Stop()
{
    int Ret = WT_OK;
    if (m_RunData.ModStatus == WT_RX_TX_STATE_RUNNING && m_AllocModId > 0)
    {
        DevLib::Instance().VSAStop(m_AllocModId);
    }
    SetModRunState(false);
    m_RunData.ModStatus = WT_RX_TX_STATE_DONE;
    m_RunData.IsConfig = false;
    usleep(2);
    WTLog::Instance().WriteLog(LOG_DEBUG, "SncCalTask Stop PortList=%#x, VSA VSG AllocModId = %d,%d\n", m_PortMask, m_AllocModId, m_VsgAllocModId);
    return Ret;
}

void SncCalTask::NoiseCalProc(void)
{
    int Ret = WT_OK;
    bool LoopDown = false;
    do
    {
        if (!m_NoiseCalStatus.NoiseCalFlag) // 中途停止
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "SncCalTask NoiseCalFlag false PortMask=%#x\n", m_PortMask);
            m_RunData.SncStatus = SNC_TASK_ERROR;
            m_RetryCnt = 0;
            break;
        }
        else if(m_RunData.SncStatus == SNC_TASK_RETRY)
        {
            do
            {
                Ret = AllocModPair(); // 申请模块
                CheckBreak(Ret);
                Ret = Stop(); // 停止正在运行的VSG
                CheckBreak(Ret);
                Ret = SetMod(0);
                CheckBreak(Ret);
                Ret = Start();
                CheckBreak(Ret);
            } while (0);

            if (Ret == WT_ALLOC_MOD_TIMEOUT)
            {
                if (++m_RetryCnt < DEV_BUSY_MAX_RETYR_CNT)
                {
                    WTLog::Instance().WriteLog(LOG_DEBUG, "SncCalTask AllocModPair failed, VSA VSG AllocModId = %d,%d\n", m_AllocModId, m_VsgAllocModId);
                    LoopDown = false;
                    Ret = WT_OK;
                    m_TimerEv.again();
                }
            }
            else
            {
                LoopDown = false;
            }
        }
        else if (m_RunData.SncStatus == SNC_TASK_HW_IQR) // 采集完成
        {
            // 分析
            m_NoiseCalStatus.Capture_count++;
            Ret = GetCaptureData();
            CheckBreak(Ret);
            Ret = AlzSingleData();
            CheckBreak(Ret);

            // 取结果数据
            void *DataBuf = nullptr;
            int DataSize;
            int DataType;
            // 取y轴功率值(double *)DataBuf WT_RES_SPECTRUM_Y
            Ret = m_Alg.GetVsaResult(WT_RES_SPECTRUM_Y, 0, 0, &DataBuf, DataSize, DataType);
            CheckBreak(Ret);

            double *Datadouble = (double *)DataBuf;
            // 取频谱点
            int Scale = (DataSize / DataType) / (m_NoiseCalStatus.noise_info.smp_rate / 1e6);
            int SpectrumPointIndex = m_NoiseCalStatus.noise_info.spec_start;
            int DataPointIndex = 0;
            int TotalPoint = ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span);
            // for (int i = 0; i < DataSize / DataType; i++)
            // {
            //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************************"
            //               << "Datadouble [" << i << "] = " << Datadouble[i]
            //               << "****************************"
            //               << std::endl;
            // }
            // 取数据
            for (; SpectrumPointIndex < DataSize / DataType && SpectrumPointIndex < m_NoiseCalStatus.noise_info.spec_end; DataPointIndex++)
            {
                int j = 0;
                double sum = 0.0;
                for (; j < m_NoiseCalStatus.noise_info.spec_avg_span && SpectrumPointIndex < DataSize / DataType && SpectrumPointIndex < m_NoiseCalStatus.noise_info.spec_end; j++, SpectrumPointIndex++)
                {
                    sum += Datadouble[SpectrumPointIndex * Scale + (DataSize / DataType) / 2];
                }
                if (DataPointIndex < TotalPoint) // 已采集足够数据
                {
                    m_NoiseCalStatus.power_ready_to_avg[DataPointIndex][m_NoiseCalStatus.Capture_count] = sum / j;
                    // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************************"
                    //           << "m_NoiseCalStatus.power_ready_to_avg [" << DataPointIndex << "][" << m_NoiseCalStatus.Capture_count << "] = " << (sum / j)
                    //           << "****************************"
                    //           << std::endl;
                }
            }
            // 取平均
            if (m_NoiseCalStatus.Capture_count >= m_NoiseCalStatus.noise_info.avg)
            {
                // 进行平均
                Freq_Level_Data_List temp;
                memset(&temp, 0, sizeof(temp));
                temp.freq = m_VsaParam.Freq / 1e6;
                temp.link_idx = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].link_idx;
                temp.level_sub_idx = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].level_sub_idx;
                temp.level_all_idx = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].level_all_idx;
                temp.power_level = m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[m_NoiseCalStatus.CurCount].power_level;
                temp.real_plevel = m_VsaParam.Ampl;
                for (int j = 0; j < TotalPoint; j++)
                {
                    double sum = 0.0;
                    for (int k = 1; k <= m_NoiseCalStatus.noise_info.avg; k++)
                    {
                        sum += m_NoiseCalStatus.power_ready_to_avg[j][k];
                    }
                    temp.data[j] = sum / (m_NoiseCalStatus.noise_info.avg);
                    // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "============================"
                    //           << "m_NoiseCalStatus.Data [" << j << "] = " << (sum / (m_NoiseCalStatus.noise_info.avg))
                    //           << "============================"
                    //           << std::endl;
                }

                // for (int j = 0; j < ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span); j++)
                // {
                //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
                //               << "Port[" << m_NoiseCalStatus.CurPort << "].PowerLevel [" << temp.power_level << "](" << temp.freq * 1e6 << ").Spec[" << j << "] = " << temp.data[j]
                //               << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
                //               << std::endl;
                // }
                m_NoiseCalStatus.Data.push_back(temp);
                m_NoiseCalStatus.CurCount++;
                // 判断循环是否结束
                if (m_NoiseCalStatus.CurCount < m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].count)
                {
                    m_NoiseCalStatus.Capture_count = 0;
                }
                else
                {
                    // for (int i = 0; i < m_NoiseCalStatus.CurCount; i++)
                    // {
                    //     for (int j = 0; j < ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span); j++)
                    //     {
                    //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
                    //                   << "Port[" << m_NoiseCalStatus.CurPort << "].PowerLevel [" << i << "](" << m_NoiseCalStatus.port_cal_list[m_NoiseCalStatus.CurPort].array[i].freq * 1e6 << ").Spec[" << j << "] = " << m_NoiseCalStatus.Data[i].data[j]
                    //                   << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
                    //                   << std::endl;
                    //     }
                    // }
                    Ret = wt_calibration_update_noise_data(m_NoiseCalStatus.CurPort, &(m_NoiseCalStatus.Data[0]), m_NoiseCalStatus.CurCount);
                    CheckBreak(Ret);
                    do
                    {
                        m_NoiseCalStatus.CurPort++;
                    } while (m_NoiseCalStatus.CurPort < 8 && !m_NoiseCalStatus.port_list[m_NoiseCalStatus.CurPort]);
                    if (m_NoiseCalStatus.CurPort >= 8)
                    {
                        LoopDown = true;
                        break;
                    }
                    else
                    {
                        LoopDown = false;
                        m_NoiseCalStatus.CurCount = 0;
                        m_NoiseCalStatus.Capture_count = 0;
                        m_NoiseCalStatus.Data.clear();
                    }
                }
            }

            if (!LoopDown && m_NoiseCalStatus.NoiseCalFlag)
            {
                if (DevMgr::Instance().IsAllocating(DEV_TYPE_VSA, m_AllocModId) || DevMgr::Instance().IsAllocating(DEV_TYPE_VSG, m_VsgAllocModId))
                {
                    WTLog::Instance().WriteLog(LOG_DEBUG, "Find DevMgr IsAllocating, free...\n");
                    FreeModPair();
                    m_TimerEv.again();
                }
                else
                {
                    Ret = SetMod(0);
                    CheckBreak(Ret);
                    Ret = Start();
                    CheckBreak(Ret);
                    m_RetryCnt = 0;
                }
            }
        }
        else
        {
            WTLog::Instance().LOGERR(Ret, (string("NoiseCalProc ModStatus = ") + to_string(m_RunData.ModStatus)).c_str());
            Ret = WT_VSA_CAPTURE_FAILED;
            CheckBreak(Ret);
        }
    } while (0);

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, (string("NoiseCalProc Ret = ") + to_string(Ret)).c_str());
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_ERR_DONE;
        m_NoiseCalStatus.ErrorCode = Ret;
        NoiseCalStop();
        m_RunData.SncStatus = SNC_TASK_ERROR;
    }
    else if (LoopDown)
    {
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_DONE;
        NoiseCalStop();
        m_RunData.SncStatus = SNC_TASK_DONE;
        char pBuf[512];
        sprintf(pBuf, "SncCalTask SNC_TASK_DONE PortMask=%#x\n", m_PortMask);
        WTLog::Instance().LOGOPERATE(pBuf);
    }
    else
    {
        m_RunData.SncStatus = SNC_TASK_RUNNING;
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_RUNNING;
    }
}

int SncCalTask::NoiseCalStart()
{
    int Ret = WT_OK;
    bool LoopDown = false;
    do
    {
        for (int i = 0; i < 8; i++)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "PortList [" << i << "] = " << m_PortLlist[i] << std::endl;
            m_NoiseCalStatus.port_list[i] = m_PortLlist[i];
        }

        // 获取Cal参数
        Ret = wt_calibration_get_noise_cal_list(m_PortLlist, m_NoiseCalStatus.port_cal_list, &m_NoiseCalStatus.noise_info);
        CheckBreak(Ret);
        WTLog::Instance().WriteLog(LOG_DEBUG, "****************************CalParam****************************\n");
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_PortLlist=%d,%d,%d,%d,%d,%d,%d,%d\n", m_PortLlist[0], m_PortLlist[1], m_PortLlist[2], m_PortLlist[3],
               m_PortLlist[4], m_PortLlist[5], m_PortLlist[6], m_PortLlist[7]);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.avg = %d\n", m_NoiseCalStatus.noise_info.avg);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.rbw = %d\n", m_NoiseCalStatus.noise_info.rbw);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.smp_time = %d\n", m_NoiseCalStatus.noise_info.smp_time);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.smp_rate = %d\n", m_NoiseCalStatus.noise_info.smp_rate);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.spec_start = %d\n", m_NoiseCalStatus.noise_info.spec_start);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.spec_end = %d\n", m_NoiseCalStatus.noise_info.spec_end);
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_NoiseCalStatus.noise_info.spec_avg_span = %d\n", m_NoiseCalStatus.noise_info.spec_avg_span);
        WTLog::Instance().WriteLog(LOG_DEBUG, "****************************CalParam****************************\n");
        // 初始化Cal参数
        m_NoiseCalStatus.CurPort = 0;
        for (int i = 0; i < 8; i++)
        {
            if (m_NoiseCalStatus.port_list[i])
            {
                m_NoiseCalStatus.CurPort = i;
                break;
            }
        }
        if (!m_NoiseCalStatus.port_list[m_NoiseCalStatus.CurPort])
        {
            LoopDown = true;
            break;
        }
        m_NoiseCalStatus.NoiseCalFlag = true;
        m_NoiseCalStatus.CurCount = 0;
        m_NoiseCalStatus.Capture_count = 0;
        m_NoiseCalStatus.ErrorCode = WT_OK;
        m_NoiseCalStatus.Data.clear();

        // 申请平均所用内存
        int FreqCnt = ((m_NoiseCalStatus.noise_info.spec_end - m_NoiseCalStatus.noise_info.spec_start) / m_NoiseCalStatus.noise_info.spec_avg_span) + 1;
        if (m_NoiseCalStatus.power_ready_to_avg == nullptr)
        {
            m_NoiseCalStatus.power_ready_to_avg = new (std::nothrow) double *[FreqCnt];
            if (m_NoiseCalStatus.power_ready_to_avg == nullptr)
            {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when NoiseCalStart");
                Ret = WT_ALLOC_FAILED;
                CheckBreak(Ret);
            }
            for (int i = 0; i < FreqCnt; i++)
            {
                m_NoiseCalStatus.power_ready_to_avg[i] = new (std::nothrow) double[m_NoiseCalStatus.noise_info.avg + 1]();
                if (m_NoiseCalStatus.power_ready_to_avg[i] == nullptr)
                {
                    WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when NoiseCalStart");
                    Ret = WT_ALLOC_FAILED;
                    CheckBreak(Ret);
                }
            }
        }

        CheckBreak(Ret);
        Ret = AllocModPair(); // 申请模块
        CheckBreak(Ret);
        Ret = Stop(); // 停止正在运行的VSG
        CheckBreak(Ret);
        Ret = SetAlzParam();
        CheckBreak(Ret);
        Ret = SetMod(0);
        CheckBreak(Ret);
        Ret = Start();
        CheckBreak(Ret);
        
    } while (0);

    if (Ret == WT_OK)
    {
        if (LoopDown)
        {
            m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_DONE;
        }
        else
        {
            
            m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_RUNNING;
        }
        m_RetryCnt = 0;
        m_RunData.SncStatus = SNC_TASK_RUNNING;
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "NoiseCalStart error");
        m_NoiseCalStatus.Status = WT_NOISE_CAL_STATE_ERR_DONE;
        NoiseCalStop();
        m_RunData.SncStatus = SNC_TASK_ERROR;
    }

    char pBuf[512];
    sprintf(pBuf, "SncCalTask Start PortList=%#x\n", m_PortMask);
    WTLog::Instance().LOGOPERATE(pBuf);
    return Ret;
}

int SncCalTask::NoiseCalStop()
{
    Stop(); // 停止正在运行的VSA
    m_NoiseCalStatus.NoiseCalFlag = false;
    m_TimerEv.stop();    //计时器停止
    FreeModPair();
    usleep(2);
    char pBuf[512];
    sprintf(pBuf, "SncCalTask NoiseCalStop PortList=%#x, VSA VSG AllocModId = %d,%d\n", m_PortMask, m_AllocModId, m_VsgAllocModId);
    WTLog::Instance().LOGOPERATE(pBuf);
    return WT_OK;
}

int SncCalTask::GetSncStatus()
{
    int Status = WT_NOISE_CAL_STATE_DONE;
    switch (m_RunData.SncStatus)
    {
    case SncCalTask::SNC_TASK_ERROR:
        Status = WT_NOISE_CAL_STATE_ERR_DONE;
        break;
    case SncCalTask::SNC_TASK_RUNNING:
    case SncCalTask::SNC_TASK_RETRY:
    case SncCalTask::SNC_TASK_HW_IQR:
        Status = WT_NOISE_CAL_STATE_RUNNING;
        break;
    default:
        break;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetSncStatus Status=%d, m_RunData.SncStatus=%d\n", Status, m_RunData.SncStatus);
    return Status;
}

int SncCalTask::MakeSncCalParam()
{
    int Ret = WT_OK;
    do
    {
        memset(&m_VsaParam, 0, sizeof(VsaParam));

        m_VsaParam.Freq2 = 0.0;
        m_VsaParam.FreqOffset = 0.0;
        m_VsaParam.Type = TEST_SISO;
        m_VsaParam.MasterMode = 0;
        m_VsaParam.SignalId = 0;
        m_VsaParam.VsaMask = 0;
        m_VsaParam.TrigType = WT_TRIG_TYPE_FREE_RUN;
        m_VsaParam.AllocTimeout = 1.0;
        m_VsaParam.Is160M = 0;
        m_VsaParam.ExtGain = 0;
        m_VsaParam.TrigPreTime = 20 * Us;
        m_VsaParam.TrigTimeout = 0.2;
        m_VsaParam.TrigLevel = -31.0;
        m_VsaParam.MaxIFG = 0.1;
        m_VsaParam.RFPort2 = 0;
        m_VsaParam.DulPortMode = 0;
        m_VsaParam.Ampl2 = 0.0;
        m_VsaParam.TrigLevel2 = 0.0;
        m_VsaParam.Demode = WT_DEMOD_CW | 0xA5B70000;
        m_VsaParam.DCOffsetI = 0;
        m_VsaParam.DCOffsetQ = 0;

        m_VsaParam.SamplingTime = 100 * Us;
        m_VsaParam.SamplingFreq = DEFAULT_SMAPLE_RATE;
        m_VsaParam.Freq = 245000000000.0;
        m_VsaParam.RFPort = WT_RF_1;

        AlzParamComm commonAnalyzeParam;
        Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_COMMON, &commonAnalyzeParam, sizeof(AlzParamComm));
        CheckBreak(Ret);
        AlzParamFFT FFTAnalyzeParam;
        FFTAnalyzeParam.Rbw = m_NoiseCalStatus.noise_info.rbw;
        Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_FFT, &FFTAnalyzeParam, sizeof(AlzParamFFT));
        CheckBreak(Ret);
        m_VsaParam.IsAgumentLegal();
    } while (0);
    return Ret;
}
