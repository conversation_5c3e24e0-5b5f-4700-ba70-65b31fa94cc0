#include "upgradefpga.h"

#include <unistd.h>
#include "wterror.h"
#include "epcs.h"
#include "conf.h"
#include "basefun.h"
#include "iostream"
#include "ft4222/ftd2xx.h"
#include "../wtlog.h"
#include <memory>
#include "wtlog.h"
using namespace libft4222;
using namespace std;

#define SLAVE_SELECT(x) (1 << (x))

//FT4222Dev类都使用libft4222.h定义的错误码
//UpgradeFpga使用448自定义的错误码
template <typename Type>
inline int ERROR_FT4222(Type &Ret)
{
    if (Ret != FT4222_OK)
    {
        Ret = static_cast<Type>(static_cast<int>(Ret) + WT_HW_UPGRADE_FT4222_OK);
    }
    return static_cast<int>(Ret);
}

#define RetBreak(Ret) \
    if (Ret != WT_OK) \
    {                 \
        break;        \
    }

//检查错误状态
#define CheckError(Ret, Status)                                              \
    if (Ret != static_cast<int>(WT_OK) && Status != static_cast<int>(WT_OK)) \
    {                                                                        \
        Ret = static_cast<int>(Status);                                       \
    }

FT4222Dev &FT4222Dev::Instance(void)
{
    static FT4222Dev ObjDeviceInfo;
    return ObjDeviceInfo;
}

FT4222Dev::FT4222Dev()
{
    ListFtUsbDevices();
}

void FT4222Dev::ListFtUsbDevices()
{
    FT_STATUS ftStatus = 0;

    DWORD numOfDevices = 0;
    ftStatus = FT_CreateDeviceInfoList(&numOfDevices);
    for (DWORD iDev = 0; iDev < numOfDevices; ++iDev)
    {
        FT_DEVICE_LIST_INFO_NODE devInfo;
        memset(&devInfo, 0, sizeof(devInfo));

        ftStatus = FT_GetDeviceInfoDetail(iDev, &devInfo.Flags, &devInfo.Type,
                                          &devInfo.ID, &devInfo.LocId,
                                          devInfo.SerialNumber,
                                          devInfo.Description,
                                          &devInfo.ftHandle);

        //当前FT4222芯片选择模式0
        if (FT4222_OK == ftStatus && devInfo.Type == FT_DEVICE_4222H_0)
        {
            const std::string desc = devInfo.Description;
            //FT4222芯片模式0时，一个可FT4222芯片查找到A B两个USB设备
            if (desc == "FT4222 A" || desc == "FT4222 B")
            {
                m_FT4222DevList.push_back(devInfo);
            }
        }
    }
}

inline void FT4222Dev::DeviceFlagToString(DWORD flags, char *msg)
{
    strcat(msg, (flags & 0x1) ? "DEVICE_OPEN" : "DEVICE_CLOSED");
    strcat(msg, ",");
    strcat(msg, (flags & 0x2) ? "High-speed USB" : "Full-speed USB");
}

void FT4222Dev::PrintAllDevice(void)
{
    char msgTemp[256];
    WTLog::Instance().WriteLog(LOG_DEBUG, "\nFT4222 Device Info:\n================\n");
    for (auto &Iter : m_FT4222DevList)
    {
        memset(msgTemp, 0, sizeof(msgTemp));
        DeviceFlagToString(Iter.Flags, msgTemp);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Flags= 0x%x, (%s)\n", Iter.Flags, msgTemp);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Type= 0x%x\n", Iter.Type);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ID= 0x%x\n", Iter.ID);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: LocId= 0x%x\n", Iter.LocId);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: SerialNumber= %s\n", Iter.SerialNumber);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Description= %s\n", Iter.Description);
        WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ftHandle= 0x%p\n", Iter.ftHandle);
        WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");
    }
}

string FT4222Dev::GetFt4222INfo(void)
{
    string info;
    for (auto &Iter : m_FT4222DevList)
    {
        info += "Type=0x" + to_string(Iter.Type) + " " + Iter.Description + ";";
    }
    return info;
}

int FT4222Dev::GetBackPlaneDevice(FT4222Mode0 &FT4222Dev)
{
    if (m_FT4222DevList.size() >= 2)
    {
        FT4222Dev.Ft4222A = m_FT4222DevList[0];
        FT4222Dev.Ft4222B = m_FT4222DevList[1];
        return WT_OK;
    }
    else
    {
        return FT4222_DEVICE_NOT_FOUND;
    }
}

int FT4222Dev::GetBaseBoardDevice(FT4222Mode0 &FT4222Dev)
{
    if (m_FT4222DevList.size() >= 2)
    {
        FT4222Dev.Ft4222A = m_FT4222DevList[2];
        FT4222Dev.Ft4222B = m_FT4222DevList[3];
        return WT_OK;
    }
    else
    {
        return FT4222_DEVICE_NOT_FOUND;
    }
}

UpgradeFpga::~UpgradeFpga()
{
    if (m_FStream.is_open())
    {
        m_FStream.close();
    }
}

void UpgradeFpga::OpenFile(const std::string &CfgFile)
{
    if (m_FStream.is_open())
    {
        m_FStream.close();
    }
    m_FStream.open(CfgFile, std::ios_base::binary | std::ios_base::in);
    m_File = CfgFile;
}

int UpgradeFpga::BrunFpgaData(UPGRADE_FPGA_TYPE Type, int NotProgram)
{
    int Ret = WT_OK;
    FT4222Mode0 FT4222Dev;
    do
    {
        if (Type == UPGRADE_FPGA_BACKPLANE)
        {
            Ret = FT4222Dev::Instance().GetBackPlaneDevice(FT4222Dev);
            ERROR_FT4222(Ret);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret , "Can not find back FT4222 device");
                break;
            }
            Ret = BrunBackPlane(FT4222Dev, NotProgram);
        }
        else
        {
            Ret = FT4222Dev::Instance().GetBaseBoardDevice(FT4222Dev);
            ERROR_FT4222(Ret);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret , "Can not find base FT4222 device");
                break;
            }
            Ret = BrunBaseBoard(FT4222Dev, NotProgram);
        }
    } while (0);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Brun fpga data!");
    }
    return Ret;
}

int UpgradeFpga::ReadFpgaData(UPGRADE_FPGA_TYPE Type, const std::string &SavePath)
{
    int Ret = WT_OK;
    FT4222Mode0 FT4222Dev;
    if (Type == UPGRADE_FPGA_BACKPLANE)
    {
        Ret = FT4222Dev::Instance().GetBackPlaneDevice(FT4222Dev);
        ERROR_FT4222(Ret);
    }
    else
    {
        Ret = FT4222Dev::Instance().GetBaseBoardDevice(FT4222Dev);
        ERROR_FT4222(Ret);
    }

    if (Ret == WT_OK)
    {
        Ret = ReadData(FT4222Dev, SavePath);
    }
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "ReadFpgaData Type%d Can not find FT4222Dev\n", Type);
    }
    return Ret;
}

int UpgradeFpga::SelfTest(UPGRADE_FPGA_TYPE Type)
{
    int Ret = WT_OK;
    string Path = WTConf::GetDir();
    string BrunData = Path + (Type == UPGRADE_FPGA_BACKPLANE ? BACK_FPGA_CMD_FILE : BASE_FPGA_CMD_FILE);
    string ReadData = Path + (Type == UPGRADE_FPGA_BACKPLANE ? BACK_FPGA_READ_BACK_CMD_FILE : BASE_FPGA_READ_BACK_CMD_FILE);
    string BakData = Path + BAK_CMD_FILE;
    OpenFile(BrunData);
    Ret = BrunFpgaData(Type);
    sleep(2);
    Ret |= ReadFpgaData(Type, ReadData);
    string Cmd = string("cmp ") + ReadData + " " + BakData;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SelfTest:" << Cmd << endl;
    string Result = Basefun::shell_exec(Cmd.c_str());
    string RePrint;
    if (Result.length() < 10 || Result.find("EOF on") != string::npos)
    {
        RePrint = string("SelfTest no error:") + Result;
        Ret = WT_OK;
    }
    else if (Result.find("differ") != string::npos)
    {
        RePrint = string("SelfTest data error:") + Result;
        Ret = WT_ARG_ERROR;
    }
    else if (Result.find("No such file or directory") != string::npos)
    {
        RePrint = string("SelfTest no file error:") + Result;
        Ret = WT_FILE_OPEN_ERROR;
    }
    else
    {
        RePrint = string("SelfTest other error:") + Result;
        Ret = WT_ARG_ERROR;
    }
    ofstream ofs(HW_UPGRADE_TEST_RESULT, std::fstream::out|std::fstream::app);
    ofs << RePrint << endl;
    ofs.close();
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << RePrint << endl;
    return Ret;
}

int UpgradeFpga::BrunBackPlane(const FT4222Mode0 &FT4222Dev, int NotProgram)
{
    int Ret = WT_OK;

    if (!m_FStream.is_open())
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "BrunBackPlane WT_FILE_OPEN_ERROR, File=%s\n", m_File.c_str());
        return WT_FILE_OPEN_ERROR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Type= 0x%x\n", FT4222Dev.Ft4222A.Type);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ID= 0x%x\n", FT4222Dev.Ft4222A.ID);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: LocId= 0x%x\n", FT4222Dev.Ft4222A.LocId);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: SerialNumber= %s\n", FT4222Dev.Ft4222A.SerialNumber);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Description= %s\n", FT4222Dev.Ft4222A.Description);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ftHandle= 0x%p\n", FT4222Dev.Ft4222A.ftHandle);
    WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: Type= 0x%x\n", FT4222Dev.Ft4222B.Type);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: ID= 0x%x\n", FT4222Dev.Ft4222B.ID);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: LocId= 0x%x\n", FT4222Dev.Ft4222B.LocId);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: SerialNumber= %s\n", FT4222Dev.Ft4222B.SerialNumber);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: Description= %s\n", FT4222Dev.Ft4222B.Description);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: ftHandle= 0x%p\n", FT4222Dev.Ft4222B.ftHandle);
    WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");

    FT_HANDLE ftHandleSPI = NULL;
    FT_HANDLE ftHandleGPIO = NULL;
    FT_STATUS ftStatus = FT4222_OK;
    short unsigned int MaxSize = 1024;

    ftStatus = FT_OpenEx(reinterpret_cast<PVOID>(FT4222Dev.Ft4222A.LocId), FT_OPEN_BY_LOCATION, &ftHandleSPI);
    if (FT4222_OK != ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Open a FT4222 SPI device failed!\n");
        return ERROR_FT4222(ftStatus);
    }
    ftStatus = FT_OpenEx(reinterpret_cast<PVOID>(FT4222Dev.Ft4222B.LocId), FT_OPEN_BY_LOCATION, &ftHandleGPIO);
    if (FT4222_OK != ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Open a FT4222 GPIO device failed!\n");

        //ftHandleSPI已成功打开，退出前需关闭
        FT4222_UnInitialize(ftHandleSPI);
        FT_Close(ftHandleSPI);
        return ERROR_FT4222(ftStatus);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Open FT4222 device success!\n");

    do
    {

        ftStatus = FT4222_SetSuspendOut(ftHandleGPIO, false);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_SetWakeUpInterrupt(ftHandleGPIO, false);
        CheckError(Ret, ERROR_FT4222(ftStatus));

        GPIO_Dir gpioDir[4];
        gpioDir[FPGA_CONFIG] = GPIO_OUTPUT;
        gpioDir[FPGA_CONFIG_DONE] = GPIO_INPUT;
        gpioDir[FPGA_CE] = GPIO_OUTPUT;
        gpioDir[FPGA_CE_VC] = GPIO_OUTPUT;
        ftStatus = FT4222_GPIO_Init(ftHandleGPIO, gpioDir);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CONFIG), GPIO_OUTPUT_LOW);
        ftStatus |= FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE), GPIO_OUTPUT_HIGH);
        ftStatus |= FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE_VC), GPIO_OUTPUT_HIGH);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Init GPIO\n");
        do
        {
            // Configure the FT4222 as an SPI Master.
            ftStatus = FT4222_SPIMaster_Init(
                ftHandleSPI,
                SPI_IO_SINGLE,    // 1 channel
                CLK_DIV_4,        // (60 MHz / 2 == 30) MHz
                CLK_IDLE_LOW,     // clock idles at logic 0
                CLK_LEADING,      // data captured on rising edge
                SLAVE_SELECT(0)); // Use SS0O for slave-select
            WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Init SPI ftStatus = %d\n", ftStatus);
            CheckError(Ret, ERROR_FT4222(ftStatus));
            RetBreak(Ret);

            if (WT_OK == Ret)
            {
                ftStatus = FT4222_GetMaxTransferSize(ftHandleSPI, &MaxSize);
                CheckError(Ret, ERROR_FT4222(ftStatus));
                RetBreak(Ret);
                WTLog::Instance().WriteLog(LOG_DEBUG, "Get FT4222 MaxTransferSize =%d!\n", MaxSize);

                int OnceReadSize = SPI_FLASH_MAX_WRITE_SIZE;
                std::unique_ptr<char[]>Readbuf(new (std::nothrow) char[OnceReadSize]);
                int ReadCount = 0;
                int Addr = 0;
                m_FStream.seekg(0, m_FStream.end);
                int FileLenth = m_FStream.tellg();
                m_FStream.seekg(0, m_FStream.beg);
                do
                {
                    char SiliconId = 0;
                    if ((Ret = ReadSiliconId(ftHandleSPI, SiliconId) != WT_OK) || SiliconId != CHIP_SILICON_ID)
                    {
                        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadSiliconId %#x failed!\n", SiliconId);
                        RetBreak(Ret);
                    }

                    Ret = WriteEnableCmd(ftHandleSPI); /* 关闭写保护 */
                    RetBreak(Ret);
                    Ret = EarseFlashChip(ftHandleSPI); /* 擦除全部 */
                    RetBreak(Ret);
                    if (NotProgram)
                    {
                        WTLog::Instance().WriteLog(LOG_DEBUG, "Flash Earse Finish...Exit\n");
                        break;
                    }
                    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Promming...\n");

                    while (Addr < FileLenth)
                    {
                        if (Addr < FileLenth - SPI_FLASH_MAX_WRITE_SIZE)
                        {
                            ReadCount = SPI_FLASH_MAX_WRITE_SIZE;
                        }
                        else
                        {
                            ReadCount = FileLenth - Addr;
                        }

                        m_FStream.read(Readbuf.get(), ReadCount);
                        Ret = PageProgramCmd(ftHandleSPI, Addr, reinterpret_cast<unsigned char *>(Readbuf.get()), ReadCount);
                        RetBreak(Ret);
                        Addr += ReadCount;
                    }
                    WaitForFlashReady(ftHandleSPI);
                    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Program ftStatus=%d, Ret=%#x, Lenght= %#x\n", ftStatus, Ret, Addr);
                } while (0);
            }
        } while (0);
        //释放FPGA芯片，启动FPGA程序
        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CONFIG), GPIO_OUTPUT_HIGH);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE), GPIO_OUTPUT_LOW);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE_VC), GPIO_OUTPUT_LOW);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        WTLog::Instance().WriteLog(LOG_DEBUG, "Release FPGA Config and CE\n");
    } while (0);

    GPIO_Dir gpioDir[4];
    gpioDir[FPGA_CONFIG] = GPIO_INPUT;
    gpioDir[FPGA_CONFIG_DONE] = GPIO_INPUT;
    gpioDir[FPGA_CE] = GPIO_INPUT;
    gpioDir[FPGA_CE_VC] = GPIO_INPUT;
    ftStatus = FT4222_GPIO_Init(ftHandleGPIO, gpioDir);
    CheckError(Ret, ERROR_FT4222(ftStatus));

    FT4222_UnInitialize(ftHandleSPI);
    FT4222_UnInitialize(ftHandleGPIO);
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Release GPIO\n");

    FT_Close(ftHandleGPIO);
    FT_Close(ftHandleSPI);
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Close\n");

    return Ret;
}

int UpgradeFpga::BrunBaseBoard(const FT4222Mode0 &FT4222Dev, int NotProgram)
{
    (void)NotProgram;
    int Ret = WT_OK;

    if (!m_FStream.is_open())
    {
        return WT_FILE_OPEN_ERROR;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Type= 0x%x\n", FT4222Dev.Ft4222A.Type);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ID= 0x%x\n", FT4222Dev.Ft4222A.ID);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: LocId= 0x%x\n", FT4222Dev.Ft4222A.LocId);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: SerialNumber= %s\n", FT4222Dev.Ft4222A.SerialNumber);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Description= %s\n", FT4222Dev.Ft4222A.Description);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ftHandle= 0x%p\n", FT4222Dev.Ft4222A.ftHandle);
    WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: Type= 0x%x\n", FT4222Dev.Ft4222B.Type);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: ID= 0x%x\n", FT4222Dev.Ft4222B.ID);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: LocId= 0x%x\n", FT4222Dev.Ft4222B.LocId);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: SerialNumber= %s\n", FT4222Dev.Ft4222B.SerialNumber);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: Description= %s\n", FT4222Dev.Ft4222B.Description);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: ftHandle= 0x%p\n", FT4222Dev.Ft4222B.ftHandle);
    WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");

    FT_HANDLE ftHandleSPI = NULL;
    FT_HANDLE ftHandleGPIO = NULL;
    FT_STATUS ftStatus = FT4222_OK;
    short unsigned int MaxSize = 1024;
    const int WAIT_FPGA_READ_TIMES = 5000; /*ms*/

    ftStatus = FT_OpenEx(reinterpret_cast<PVOID>(FT4222Dev.Ft4222A.LocId), FT_OPEN_BY_LOCATION, &ftHandleSPI);
    if (FT4222_OK != ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Open a FT4222 SPI device failed!\n");
        return ERROR_FT4222(ftStatus);
    }
    ftStatus = FT_OpenEx(reinterpret_cast<PVOID>(FT4222Dev.Ft4222B.LocId), FT_OPEN_BY_LOCATION, &ftHandleGPIO);
    if (FT4222_OK != ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Open a FT4222 GPIO device failed!\n");

        //ftHandleSPI已成功打开，退出前需关闭
        FT4222_UnInitialize(ftHandleSPI);
        FT_Close(ftHandleSPI);
        return ERROR_FT4222(ftStatus);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Open FT4222 device success!\n");

    do
    {
        // Configure the FT4222 as an SPI Master.
        ftStatus = FT4222_SPIMaster_Init(
            ftHandleSPI,
            SPI_IO_SINGLE,    // 1 channel
            CLK_DIV_32,       // (60 MHz / 2 == 30) MHz
            CLK_IDLE_LOW,     // clock idles at logic 0
            CLK_LEADING,      // data captured on rising edge
            SLAVE_SELECT(0)); // Use SS0O for slave-select
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Init SPI Ret = %d\n", ftStatus);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        ftStatus = FT4222_GetMaxTransferSize(ftHandleSPI, &MaxSize);
        WTLog::Instance().WriteLog(LOG_DEBUG, "Get FT4222 MaxTransferSize = %d!, ftStatus = %#x\n", MaxSize, ftStatus);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        ftStatus = FT4222_SetSuspendOut(ftHandleGPIO, false);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_SetWakeUpInterrupt(ftHandleGPIO, false);
        CheckError(Ret, ERROR_FT4222(ftStatus));

        GPIO_Dir gpioDir[4];
        gpioDir[BASE_FPGA_INIT] = GPIO_INPUT;
        gpioDir[BASE_FPGA_PROGRAM] = GPIO_OUTPUT;
        gpioDir[BASE_FPGA_DONE] = GPIO_INPUT;
        gpioDir[BASE_FPGA_NOUSE2] = GPIO_INPUT;
        ftStatus = FT4222_GPIO_Init(ftHandleGPIO, gpioDir);
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Init GPIO\n");
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(BASE_FPGA_PROGRAM), GPIO_OUTPUT_LOW);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        WTLog::Instance().WriteLog(LOG_DEBUG, "Waiting BASE_FPGA_INIT LOW\n");
        if ((ftStatus = WaitPort(ftHandleGPIO,
                                 static_cast<GPIO_Port>(BASE_FPGA_INIT),
                                 GPIO_OUTPUT_LOW, WAIT_FPGA_READ_TIMES) != WT_OK))
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Wait base fpga init pin high timeout(%d)!\n", WAIT_FPGA_READ_TIMES);
        }
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(BASE_FPGA_PROGRAM), GPIO_OUTPUT_HIGH);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        WTLog::Instance().WriteLog(LOG_DEBUG, "Waiting BASE_FPGA_INIT HIGH\n");
        if ((ftStatus = WaitPort(ftHandleGPIO,
                                 static_cast<GPIO_Port>(BASE_FPGA_INIT),
                                 GPIO_OUTPUT_HIGH, WAIT_FPGA_READ_TIMES) != WT_OK))
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Wait base fpga init pin high timeout(%d)!\n", WAIT_FPGA_READ_TIMES);
        }
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        int OnceReadSize = SPI_FLASH_MAX_WRITE_SIZE;
        std::unique_ptr<char[]>Readbuf(new (std::nothrow) char[OnceReadSize]);
        int ReadCount = 0;
        int Addr = 0;
        uint16 sizeTransferred;
        BOOL FPGA_Done = 0;
        m_FStream.seekg(0, m_FStream.end);
        int FileLenth = m_FStream.tellg();
        m_FStream.seekg(0, m_FStream.beg);
        WTLog::Instance().WriteLog(LOG_DEBUG, "Programming...\n");
        do
        {
            while (Addr < FileLenth)
            {
                if (Addr < FileLenth - SPI_FLASH_MAX_WRITE_SIZE)
                {
                    ReadCount = SPI_FLASH_MAX_WRITE_SIZE;
                }
                else
                {
                    ReadCount = FileLenth - Addr;
                }

                m_FStream.read(Readbuf.get(), ReadCount);
                ftStatus = FT4222_SPIMaster_SingleWrite(ftHandleSPI, (unsigned char *)Readbuf.get(), ReadCount, &sizeTransferred, false);
                if (sizeTransferred != ReadCount)
                {
                    ftStatus = FT4222_OTHER_ERROR;
                }
                CheckError(Ret, ERROR_FT4222(ftStatus));
                RetBreak(Ret);
                Addr += ReadCount;

                //烧录完成时done信号拉高
                ftStatus = FT4222_GPIO_Read(ftHandleGPIO, static_cast<GPIO_Port>(BASE_FPGA_DONE), &FPGA_Done);
                CheckError(Ret, ERROR_FT4222(ftStatus));
                if (FPGA_Done == (bool)GPIO_OUTPUT_HIGH)
                {
                    break;
                }
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Program ftStatus=%d, Lenght= %#x\n", ftStatus, Addr);
        } while (0);

        WTLog::Instance().WriteLog(LOG_DEBUG, "Waiting BASE_FPGA_DONE HIGH\n");
        //烧录完成时done信号拉高
        if ((Ret = WaitPort(ftHandleGPIO,
                                 static_cast<GPIO_Port>(BASE_FPGA_DONE),
                                 GPIO_OUTPUT_HIGH, WAIT_FPGA_READ_TIMES) != WT_OK))
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Wait base fpga done pin high timeout(%d)!\n", WAIT_FPGA_READ_TIMES);
        }
    } while (0);

    FT4222_UnInitialize(ftHandleSPI);
    FT4222_UnInitialize(ftHandleGPIO);
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Release GPIO\n");

    FT_Close(ftHandleGPIO);
    FT_Close(ftHandleSPI);
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Close\n");

    return Ret;
}

int UpgradeFpga::ReadData(const FT4222Mode0 &FT4222Dev, const std::string &SavePath)
{
    int Ret = WT_OK;
    FT_HANDLE ftHandleSPI = NULL;
    FT_HANDLE ftHandleGPIO = NULL;
    FT_STATUS ftStatus = FT4222_OK;
    short unsigned int MaxSize = 1024;

    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Type= 0x%x\n", FT4222Dev.Ft4222A.Type);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ID= 0x%x\n", FT4222Dev.Ft4222A.ID);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: LocId= 0x%x\n", FT4222Dev.Ft4222A.LocId);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: SerialNumber= %s\n", FT4222Dev.Ft4222A.SerialNumber);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: Description= %s\n", FT4222Dev.Ft4222A.Description);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV A: ftHandle= 0x%p\n", FT4222Dev.Ft4222A.ftHandle);
    WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: Type= 0x%x\n", FT4222Dev.Ft4222B.Type);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: ID= 0x%x\n", FT4222Dev.Ft4222B.ID);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: LocId= 0x%x\n", FT4222Dev.Ft4222B.LocId);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: SerialNumber= %s\n", FT4222Dev.Ft4222B.SerialNumber);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: Description= %s\n", FT4222Dev.Ft4222B.Description);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DEV B: ftHandle= 0x%p\n", FT4222Dev.Ft4222B.ftHandle);
    WTLog::Instance().WriteLog(LOG_DEBUG, "--------\n");

    ftStatus = FT_OpenEx(reinterpret_cast<PVOID>(FT4222Dev.Ft4222A.LocId), FT_OPEN_BY_LOCATION, &ftHandleSPI);
    if (FT4222_OK != ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Open a FT4222 SPI device failed!\n");
        return ERROR_FT4222(ftStatus);
    }
    ftStatus = FT_OpenEx(reinterpret_cast<PVOID>(FT4222Dev.Ft4222B.LocId), FT_OPEN_BY_LOCATION, &ftHandleGPIO);
    if (FT4222_OK != ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Open a FT4222 GPIO device failed!\n");

        //ftHandleSPI已成功打开，退出前需关闭
        FT4222_UnInitialize(ftHandleSPI);
        FT_Close(ftHandleSPI);
        return ERROR_FT4222(ftStatus);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Open FT4222 device success!\n");

    do
    {
        ftStatus = FT4222_SetSuspendOut(ftHandleGPIO, false);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_SetWakeUpInterrupt(ftHandleGPIO, false);
        CheckError(Ret, ERROR_FT4222(ftStatus));

        GPIO_Dir gpioDir[4];
        gpioDir[FPGA_CONFIG] = GPIO_OUTPUT;
        gpioDir[FPGA_CONFIG_DONE] = GPIO_INPUT;
        gpioDir[FPGA_CE] = GPIO_OUTPUT;
        gpioDir[FPGA_CE_VC] = GPIO_OUTPUT;
        ftStatus = FT4222_GPIO_Init(ftHandleGPIO, gpioDir);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);

        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CONFIG), GPIO_OUTPUT_LOW);
        ftStatus |= FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE), GPIO_OUTPUT_HIGH);
        ftStatus |= FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE_VC), GPIO_OUTPUT_HIGH);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        RetBreak(Ret);
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Init GPIO\n");

        do
        {
            // Configure the FT4222 as an SPI Master.
            ftStatus = FT4222_SPIMaster_Init(
                ftHandleSPI,
                SPI_IO_SINGLE,    // 1 channel
                CLK_DIV_4,        // (60 MHz / 2 == 30) MHz
                CLK_IDLE_LOW,     // clock idles at logic 0
                CLK_LEADING,      // data captured on rising edge
                SLAVE_SELECT(0)); // Use SS0O for slave-select
            WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Init SPI Ret = %d\n", ftStatus);
            CheckError(Ret, ERROR_FT4222(ftStatus));
            RetBreak(Ret);

            if (WT_OK == Ret)
            {
                ftStatus = FT4222_GetMaxTransferSize(ftHandleSPI, &MaxSize);
                WTLog::Instance().WriteLog(LOG_DEBUG, "Get FT4222 MaxTransferSize =%d!\n", MaxSize);
                CheckError(Ret, ERROR_FT4222(ftStatus));
                RetBreak(Ret);

                std::unique_ptr<char[]>Readbuf(new (std::nothrow) char[EPCS64_SIZE]);
                int ReadCount = EPCS64_SIZE;
                int AddrOffset = 0;
                do
                {
                    char SiliconId = 0;
                    if ((Ret = ReadSiliconId(ftHandleSPI, SiliconId) != WT_OK) || SiliconId != CHIP_SILICON_ID)
                    {
                        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadSiliconId %#x failed!\n", SiliconId);
                        RetBreak(Ret);
                    }
                    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Reading...\n");
                    do
                    {
                        Ret = ReadDataBytesCmd(ftHandleSPI, AddrOffset, reinterpret_cast<unsigned char *>(Readbuf.get()) + AddrOffset, EPCS_BLOCK_SIZE);
                        RetBreak(Ret);
                        AddrOffset += EPCS_BLOCK_SIZE;
                    } while (AddrOffset < EPCS64_SIZE);
                } while (0);
                WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadData Lenght= %#x\n", AddrOffset);

                do
                {
                    if (AddrOffset != 0)
                    {
                        std::ofstream Ofs(SavePath, std::ios_base::binary | std::ios_base::out);
                        if (!Ofs.is_open())
                        {
                            WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 open file %s failed\n", SavePath.c_str());
                            Ret = WT_FILE_OPEN_ERROR;
                            break;
                        }
                        for (int i = EPCS64_SIZE - 1; i > 0; i--)
                        {
                            if (((unsigned char)Readbuf.get()[i]) != 0xff)
                            {
                                ReadCount = i + 1;
                                break;
                            }
                        }
                        Ofs.write(Readbuf.get(), ReadCount);
                        Ofs.flush();
                        Ofs.close();
                        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadData write File Lenght= %d\n", ReadCount);
                    }
                } while (0);
            }
        } while (0);
        //释放FPGA芯片，启动FPGA程序
        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CONFIG), GPIO_OUTPUT_HIGH);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE), GPIO_OUTPUT_LOW);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        ftStatus = FT4222_GPIO_Write(ftHandleGPIO, static_cast<GPIO_Port>(FPGA_CE_VC), GPIO_OUTPUT_LOW);
        CheckError(Ret, ERROR_FT4222(ftStatus));
        WTLog::Instance().WriteLog(LOG_DEBUG, "Release FPGA Config and CE\n");
    } while (0);

    GPIO_Dir gpioDir[4];
    gpioDir[FPGA_CONFIG] = GPIO_INPUT;
    gpioDir[FPGA_CONFIG_DONE] = GPIO_INPUT;
    gpioDir[FPGA_CE] = GPIO_INPUT;
    gpioDir[FPGA_CE_VC] = GPIO_INPUT;
    ftStatus = FT4222_GPIO_Init(ftHandleGPIO, gpioDir);
    CheckError(Ret, ERROR_FT4222(ftStatus));

    FT4222_UnInitialize(ftHandleSPI);
    FT4222_UnInitialize(ftHandleGPIO);
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Release GPIO\n");

    FT_Close(ftHandleGPIO);
    FT_Close(ftHandleSPI);
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 Close\n");

    return Ret;
}

int UpgradeFpga::WaitPort(FT_HANDLE ftHandle, GPIO_Port Port, GPIO_Output Level, int Timerout /*ms*/)
{
    for (int i = 0; i < Timerout; ++i)
    {
        FT4222_STATUS ftStatus;
        BOOL RData;
        ftStatus = FT4222_GPIO_Read(ftHandle, Port, &RData);
        if (ftStatus != FT4222_OK)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 WaitForFlashReady failed, ftstatus=%d\n", ftStatus);
            ERROR_FT4222(ftStatus);
            return ftStatus;
        }
        else if (RData == (bool)Level)
        {
            return WT_OK;
        }
        usleep(1000);
    }
    return WT_WAIT_TIMEOUT;
}

inline FT4222_STATUS UpgradeFpga::WaitForFlashReady(FT_HANDLE ftHandle)
{
    const int WAIT_FALSH_READY_TIMES = 1000;

    for (int i = 0; i < WAIT_FALSH_READY_TIMES; ++i)
    {
        std::vector<unsigned char> tmp;
        tmp.push_back(EPCS_READ_STATUS);
        tmp.push_back(0xFF);

        std::vector<unsigned char> recvData;
        recvData.resize(2);

        uint16 sizeTransferred;
        FT4222_STATUS ftStatus;

        ftStatus = FT4222_SPIMaster_SingleReadWrite(ftHandle, recvData.data(), &tmp[0], tmp.size(), &sizeTransferred, true);
        if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 WaitForFlashReady failed\n");
            return ftStatus;
        }

        if ((recvData[1] & 0x01) == 0x00) // not in write operation
        {
            return FT4222_OK;
        }

        usleep(1000);
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 WaitForFlashReady timeout\n");
    return FT4222_OTHER_ERROR;
}

int UpgradeFpga::WriteEnableCmd(FT_HANDLE ftHandle)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint8 outBuffer = EPCS_WRITE_ENABLE;
    uint16 sizeTransferred;
    ftStatus = FT4222_SPIMaster_SingleWrite(ftHandle, &outBuffer, 1, &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != 1))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 WriteEnableCmd failed\n");
        return ERROR_FT4222(ftStatus);
    }
    return WT_OK;
}

int UpgradeFpga::ReadStatusRegister(FT_HANDLE ftHandle, char &StatusRegister)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;

    tmp.push_back(EPCS_READ_STATUS);
    tmp.push_back(0);

    std::vector<unsigned char> recvData;
    recvData.resize(tmp.size());

    ftStatus = FT4222_SPIMaster_SingleReadWrite(ftHandle, recvData.data(), &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadStatusRegister failed\n");
        return ERROR_FT4222(ftStatus);
    }

    StatusRegister = recvData[tmp.size() - 1];
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadStatusRegister = %#x\n", StatusRegister);
    return WT_OK;
}

int UpgradeFpga::WriteStatusRegister(FT_HANDLE ftHandle, char Val)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;

    tmp.push_back(EPCS_WRITE_STATUS);
    tmp.push_back(Val);

    ftStatus = FT4222_SPIMaster_SingleWrite(ftHandle, &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 WriteStatusRegister failed\n");
        return ERROR_FT4222(ftStatus);
    }
    return WT_OK;
}

int UpgradeFpga::ReadSiliconId(FT_HANDLE ftHandle, char &SiliconId)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadSiliconId WaitForFlashReady timeout\n");
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;

    tmp.push_back(EPCS_READ_SILICON_ID);
    tmp.push_back(0);
    tmp.push_back(0);
    tmp.push_back(0);
    tmp.push_back(0);

    std::vector<unsigned char> recvData;
    recvData.resize(tmp.size());

    ftStatus = FT4222_SPIMaster_SingleReadWrite(ftHandle, recvData.data(), &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadSiliconId failed\n");
        return ERROR_FT4222(ftStatus);
    }

    SiliconId = recvData[tmp.size() - 1];
    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadSiliconId = %#x\n", SiliconId);
    return WT_OK;
}

int UpgradeFpga::ReadJedecId(FT_HANDLE ftHandle)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;

    tmp.push_back(EPCS_READ_DEVICE_ID);
    tmp.push_back(0xFF);
    tmp.push_back(0xFF);
    tmp.push_back(0xFF);

    std::vector<unsigned char> recvData;
    recvData.resize(4);

    ftStatus = FT4222_SPIMaster_SingleReadWrite(ftHandle, recvData.data(), &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadJedecId failed\n");
        return ERROR_FT4222(ftStatus);
    }
    return WT_OK;
}

int UpgradeFpga::BlockEraseCmd(FT_HANDLE ftHandle, uint32 _addr)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;

    tmp.push_back(EPCS_ERASE_SECTOR);
    tmp.push_back((unsigned char)((_addr & 0xFF0000) >> 16));
    tmp.push_back((unsigned char)((_addr & 0x00FF00) >> 8));
    tmp.push_back((unsigned char)(_addr & 0x0000FF));

    ftStatus = FT4222_SPIMaster_SingleWrite(ftHandle, &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 BlockEraseCmd failed\n");
        return ERROR_FT4222(ftStatus);
    }

    return WT_OK;
}

int UpgradeFpga::EarseFlashChip(FT_HANDLE ftHandle)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;
    tmp.push_back(EPCS_ERASE_CHIP);

    ftStatus = FT4222_SPIMaster_SingleWrite(ftHandle, &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 EarseFlashChip failed\n");
        return ERROR_FT4222(ftStatus);
    }

    ftStatus = FT4222_OTHER_ERROR;
    for (int i = 0; i < 100 && ftStatus != FT4222_OK; i++)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Waiting Earse Flash Chip...\n");
        ftStatus = WaitForFlashReady(ftHandle);
    }
    if (ftStatus)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 EarseFlashChip timeout\n");
        return ERROR_FT4222(ftStatus);
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 EarseFlashChip fisnish\n");
    return WT_OK;
}

int UpgradeFpga::PageProgramCmd(FT_HANDLE ftHandle, uint32 _addr, unsigned char *pData, uint16 size)
{
    WriteEnableCmd(ftHandle);

    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }
    if (!(_addr & 0xffff))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Programming Flash Addr %#x ...\n", _addr);
    }
    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;

    tmp.push_back(EPCS_WRITE_BYTES);
    tmp.push_back((unsigned char)((_addr & 0xFF0000) >> 16));
    tmp.push_back((unsigned char)((_addr & 0x00FF00) >> 8));
    tmp.push_back((unsigned char)(_addr & 0x0000FF));

    tmp.insert(tmp.end(), pData, (pData + size));

    ftStatus = FT4222_SPIMaster_SingleWrite(ftHandle, &tmp[0], tmp.size(), &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 PageProgramCmd failed\n");
        return ERROR_FT4222(ftStatus);
    }
    return WT_OK;
}

int UpgradeFpga::ReadDataBytesCmd(FT_HANDLE ftHandle, uint32 _addr, unsigned char *pData, uint16 size)
{
    FT4222_STATUS ftStatus;
    ftStatus = WaitForFlashReady(ftHandle);
    if (ftStatus)
    {
        return ERROR_FT4222(ftStatus);
    }

    uint16 sizeTransferred;
    std::vector<unsigned char> tmp;
    tmp.push_back(EPCS_FAST_READ);
    tmp.push_back((unsigned char)((_addr & 0xFF0000) >> 16));
    tmp.push_back((unsigned char)((_addr & 0x00FF00) >> 8));
    tmp.push_back((unsigned char)(_addr & 0x0000FF));
    tmp.push_back(0xFF);

    ftStatus = FT4222_SPIMaster_SingleWrite(ftHandle, &tmp[0], tmp.size(), &sizeTransferred, false);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != tmp.size()))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadDataBytesCmd FT4222_SPIMaster_SingleWrite failed\n");
        return ERROR_FT4222(ftStatus);
    }

    ftStatus = FT4222_SPIMaster_SingleRead(ftHandle, pData, size, &sizeTransferred, true);
    if ((ftStatus != FT4222_OK) || (sizeTransferred != size))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "FT4222 ReadDataBytesCmd FT4222_SPIMaster_SingleRead failed\n");
        return ERROR_FT4222(ftStatus);
    }
    return WT_OK;
}