//*****************************************************************************
//  File: result.h
//  结果信息处理
//  Data: 2016.8.26
//*****************************************************************************
#ifndef __WT_RESULT_H__
#define __WT_RESULT_H__

#include <unordered_map>
#include <string>
#include <vector>
#include <deque>
#include <functional>
#include <libgen.h>
#include "alg/includeAll.h"
#include "wterror.h"
#include "wtspec.h"


#define DOUBLE_VAL_ZERO                (1e-6)
#define UNVALID_DOUBLE_VAL            (-999.99)
#define UNVALID_INT_VAL                (-999)
#define _FUNCTION_LINE_   "[" << __FUNCTION__ << "," << __LINE__ << "]"
#define _FILE_LINE_   "[" << basename((char*)__FILE__) << "," << __LINE__ << "]"

#define CMIMO_MAX_STREAM 8

enum PSDU_FORMAT
{
    HE_SU = 1,          //HE-SU
    HE_MU = 2,          //HE-MU
    HE_ERSU = 3,        //HE-ERSU
    HE_TriggerBase = 4, //HE-TriggerBase
};

enum  
{
    OFDM = 0,
    MF_11N = 1,
    GF_11N = 2,
};//11n字段   type_frm

enum PPDU_11BE_ENUM
{
    EHT_MU_PPDU = 1,
    EHT_TB_PPDU = 2
};

enum
{
    TYPE_F1_RAW_DATA,
    TYPE_F2_RAW_DATA,
};

// 算法基本结果
struct VsaBaseResult
{
    double PowerFrame = UNVALID_DOUBLE_VAL;
    double PowerAll = UNVALID_DOUBLE_VAL;
    double PowerPeak = UNVALID_DOUBLE_VAL;

    double EvmAll = UNVALID_DOUBLE_VAL;
    double EvmPeak = UNVALID_DOUBLE_VAL;
    double EvmData = UNVALID_DOUBLE_VAL;
    double EvmPilot = UNVALID_DOUBLE_VAL;
    double EvmPsdu = UNVALID_DOUBLE_VAL;
    double EvmShrPhr = UNVALID_DOUBLE_VAL;

    double FreqOffset = 0;
    double CarrierLeakage = UNVALID_DOUBLE_VAL;
    double SymClkErr = UNVALID_DOUBLE_VAL;
    double PhaseErr = UNVALID_DOUBLE_VAL;
    double IQImbAmp = UNVALID_DOUBLE_VAL;
    double IQImbPhase = UNVALID_DOUBLE_VAL;

    void Reset(void)
    {
        PowerFrame = 0;
        PowerAll = 0;
        PowerPeak = 0;

        EvmAll = 0;
        EvmPeak = 0;
        EvmData = 0;
        EvmPilot = 0;
        EvmPsdu = 0;
        EvmShrPhr = 0;

        FreqOffset = 0;
        CarrierLeakage = 0;
        SymClkErr = 0;
        PhaseErr = 0;
        IQImbAmp = 0;
        IQImbPhase = 0;
    }

    VsaBaseResult &operator=(const VsaBaseResult &o)
    {
        PowerFrame = o.PowerFrame;
        PowerAll = o.PowerAll;
        PowerPeak = o.PowerPeak;

        EvmAll = o.EvmAll;
        EvmPeak = o.EvmPeak;
        EvmData = o.EvmData;
        EvmPilot = o.EvmPilot;
        EvmPsdu = o.EvmPsdu;
        EvmShrPhr = o.EvmShrPhr;

        FreqOffset = o.FreqOffset;
        CarrierLeakage = o.CarrierLeakage;
        SymClkErr = o.SymClkErr;
        PhaseErr = o.PhaseErr;
        IQImbAmp = o.IQImbAmp;
        IQImbPhase = o.IQImbPhase;

        return *this;
    }
};

#define MAX_SPECT_POINT_NUM  12000  // 频谱图的最多点数，120M/10k(ah)（采样率/RBW）1G/100k,宽屏(不包含ah)

// WIFI平均结果结构体，存放所有需要平均的结果
struct VsaCommResult : public VsaBaseResult
{
    int SpectNum = 0;                                           //频谱点数
    int ChannelNum = 0;                                         //子通道数
    int SpectMarginNum = 0;                                     //频谱margin的点数
    double Spectrum[MAX_SPECT_POINT_NUM] = {0};                 //频谱图数据点值
    Complex SpectFlatnessData[DF_Max_ChannalNum_11ax320] = {0}; //频谱平坦度数据值
	Complex margin_freq_vs_pwr[MAX_SPECT_MARGIN_LEN] = {0};     //频谱Margin，这个不做平均，只是做平均频谱结果重新计算的margin值
};

struct VsaSleCommResult :public VsaBaseResult
{
    double Max_Freq_Drift = UNVALID_DOUBLE_VAL;
    double Freq_Drift_Rate = UNVALID_DOUBLE_VAL;
    double EvmAvg = UNVALID_DOUBLE_VAL;
    double Evm99PCT = UNVALID_DOUBLE_VAL;
    double CtlInfo_EvmAvg = UNVALID_DOUBLE_VAL;
    double CtlInfo_EvmPeak = UNVALID_DOUBLE_VAL;
    double CtlInfo_Evm99PCT = UNVALID_DOUBLE_VAL;
    double Delta_Fd1_Avg = UNVALID_DOUBLE_VAL;
    double Delta_Fd1_Max = UNVALID_DOUBLE_VAL;
    double Delta_Fd1_Min = UNVALID_DOUBLE_VAL;
    double Delta_Fd2_Avg = UNVALID_DOUBLE_VAL;
    double Delta_Fd2_Min = UNVALID_DOUBLE_VAL;
    double EvmPeak = UNVALID_DOUBLE_VAL;
    int DeltaF2Len = UNVALID_INT_VAL;
    int Res = UNVALID_INT_VAL;
    double SLE_Delta_F2_99p9PCT = UNVALID_DOUBLE_VAL;
    double Reserved[125];
};

struct VsaSleCommResult2 : public VsaSleCommResult
{
    double *DeltaF2Raw;
};

struct  VsaBTCommResult: public VsaBaseResult
{
    double Init_Freq_Error = UNVALID_DOUBLE_VAL;

    double BR_Maxmum_Freq_Drift = UNVALID_DOUBLE_VAL;
    double BR_Freq_Drift_Rate = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F1_Max = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F1_Avg = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F1_Min = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F2_Max = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F2_Avg = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F2_Min = UNVALID_DOUBLE_VAL;
    double BR_BLE_Delta_F2_99p9PCT = UNVALID_DOUBLE_VAL;

    double EDR_Omega_i = UNVALID_DOUBLE_VAL;
    double EDR_Max_Omega_io = UNVALID_DOUBLE_VAL;
    double EDR_Max_Omega_o = UNVALID_DOUBLE_VAL;
    double EDR_DEVM_Avg = UNVALID_DOUBLE_VAL;
    double EDR_DEVM_Peak = UNVALID_DOUBLE_VAL;
    double EDR_Diff_Power = UNVALID_DOUBLE_VAL;
    double EDR_Max_FreqVar = UNVALID_DOUBLE_VAL;
    double EDR_DEVM_99PCT = UNVALID_DOUBLE_VAL;
    double EDR_GuardTime = UNVALID_DOUBLE_VAL;

    double BLE_FnMax = UNVALID_DOUBLE_VAL;
    double BLE_F0FnMax = UNVALID_DOUBLE_VAL;
    double BLE_F0FnAvg = UNVALID_DOUBLE_VAL;
    double BLE_F0Fn5_Max = UNVALID_DOUBLE_VAL;
    double BLE_Delta_F1F0 = UNVALID_DOUBLE_VAL;
    double BLE_Delta_F0F3 = UNVALID_DOUBLE_VAL;
    double BLE_Delta_F0Fn3 = UNVALID_DOUBLE_VAL;
    double BLE_Freq_offset_sync = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Pwr_Avg = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Pwr_Peak = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Pwr_Peak_sub_Avg = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Fsi_Max = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Fsi_Min = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Fs1_sub_Fp = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Fsi_sub_F0_Max = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Fsi_sub_Fsi3_Max = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Pwr_Ref_Avg = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Pwr_Ref_DevDivAvg = UNVALID_DOUBLE_VAL;
    double BLE_CTE_Pwr_Pn_DevDivAvg_Max = UNVALID_DOUBLE_VAL;
    
    double BLE_Delta_F1_99p9PCT = UNVALID_DOUBLE_VAL;

    int DeltaF1Len = UNVALID_INT_VAL;
    int DeltaF2Len = UNVALID_INT_VAL;
    
    double EDR_GFSK_Power = UNVALID_DOUBLE_VAL;
    double EDR_GFSK_Power_Peak = UNVALID_DOUBLE_VAL;
    double EDR_DPSK_Power = UNVALID_DOUBLE_VAL;
    double EDR_DPSK_Power_Peak = UNVALID_DOUBLE_VAL;
    
    double Reserved[124];
};

struct VsaBTCommResult2 : public VsaBTCommResult
{
    double *DeltaF1Raw;
    double *DeltaF2Raw;
};

struct Vsa3GPPCommResult
{
    int Standard;//信号类型 <typedef enum>
    Alg_3GPP_AlzSlotNBIOT NBIOT;
};

class AlgAvgResult
{
public:
    //*****************************************************************************
    // 初始化结果
    // 参数[IN]: Stream : 指定流
    //           Result : 初始结果
    // 返回值: 无
    //*****************************************************************************
    void Init(int Stream, const VsaCommResult &Result)
    {
        m_AvgResult[Stream] = Result;
        m_MaxResult[Stream] = Result;
        m_MinResult[Stream] = Result;
    }

    void InitSLE(int Stream, const VsaSleCommResult &Result)
    {
        m_AvgSLEResult[Stream] = Result;
        m_MaxSLEResult[Stream] = Result;
        m_MinSLEResult[Stream] = Result;
    }

    void InitBT(int Stream, VsaBTCommResult &Result)
    {
        m_AvgBTResult[Stream] = Result;
        m_MaxBTResult[Stream] = Result;
        m_MinBTResult[Stream] = Result;
    }

    void Init3GPP(int Stream, const Vsa3GPPCommResult &Result)
    {
        m_Avg3GPPResult[Stream] = Result;
        m_Max3GPPResult[Stream] = Result;
        m_Min3GPPResult[Stream] = Result;
    }

    const VsaCommResult &GetAvg(int Stream) const
    {
        return m_AvgResult[Stream];
    }

    const VsaCommResult &GetMax(int Stream) const
    {
        return m_MaxResult[Stream];
    }

    const VsaCommResult &GetMin(int Stream) const
    {
        return m_MinResult[Stream];
    }

    const VsaSleCommResult &GetSLEAvg(int Stream) const
    {
        return m_AvgSLEResult[Stream];
    }

    const VsaSleCommResult &GetSLEMax(int Stream) const
    {
        return m_MaxSLEResult[Stream];
    }

    const VsaSleCommResult &GetSLEMin(int Stream) const
    {
        return m_MinSLEResult[Stream];
    }

    const VsaBTCommResult &GetBTAvg(int Stream) const
    {
        return m_AvgBTResult[Stream];
    }

    const VsaBTCommResult &GetBTMax(int Stream) const
    {
        return m_MaxBTResult[Stream];
    }

    const VsaBTCommResult &GetBTMin(int Stream) const
    {
        return m_MinBTResult[Stream];
    }

    const Vsa3GPPCommResult &Get3GPPAvg(int Stream) const
    {
        return m_Avg3GPPResult[Stream];
    }

    const Vsa3GPPCommResult &Get3GPPMax(int Stream) const
    {
        return m_Max3GPPResult[Stream];
    }

    const Vsa3GPPCommResult &Get3GPPMin(int Stream) const
    {
        return m_Min3GPPResult[Stream];
    }
    //*****************************************************************************
    // 将当前结果与指定结果平均(增加一次结果并进行平均)
    // 参数[IN]: Stream : 指定流
    //           Result : 指定的待平均的结果
    //           AvgCnt : 当前结果已经平均的次数
    // 返回值: 无
    //*****************************************************************************
    void Average(int Stream, const VsaCommResult &Result, int AvgCnt);

    void SLEAverage(int Stream, const VsaSleCommResult &Result, int AvgCnt);

    void BTAverage(int Stream, VsaBTCommResult &Result1, int AvgCnt);

    void Alg3GPPAverage(int Stream,const Vsa3GPPCommResult &Result, int AvgCnt);

    //*****************************************************************************
    // 与指定结果进行对比，将每一项结果中比较小的保存为当前结果
    // 参数[IN]: Stream : 指定流
    //          Result : 指定的需要剥离的结果
    // 返回值: 无
    //*****************************************************************************
    void Min(int Stream, const VsaCommResult &Result);

    //*****************************************************************************
    // 与指定结果进行对比，将每一项结果中比较大的保存为当前结果
    // 参数[IN]: Stream : 指定流
    //           Result : 指定的需要剥离的结果
    // 返回值: 无
    //*****************************************************************************
    void Max(int Stream, const VsaCommResult &Result);

    //*****************************************************************************
    // 多次平均时，获取多次结果中的最小值
    // 参数[IN]: Stream : 指定流
    //           MultiRst : 多次的结果
    // 返回值: 无
    //*****************************************************************************
    void MultiMin(int Stream, const std::vector<VsaCommResult> MultiRst);

    void SLEMultiMin(int Stream, const std::vector<VsaSleCommResult> MultiRst);

    void BTMultiMin(int Stream, const std::vector<VsaBTCommResult> MultiRst);

    void Alg3GPPMultiMin(int Stream, const std::deque<Vsa3GPPCommResult> MultiRst);

    //*****************************************************************************
    // 多次平均时，获取多次结果中的最大值
    // 参数[IN]: Stream : 指定流
    //           MultiRst : 多次的结果
    // 返回值: 无
    //*****************************************************************************
    void MultiMax(int Stream, const std::vector<VsaCommResult> MultiRst);

    void SLEMultiMax(int Stream, const std::vector<VsaSleCommResult> MultiRst);

    void BTMultiMax(int Stream, const std::vector<VsaBTCommResult> MultiRst);

    void Alg3GPPMultiMax(int Stream, const std::deque<Vsa3GPPCommResult> MultiRst);

    //*****************************************************************************
    // 从当前的结果中将指定的值从结果数据中剥离(删除一次结果并进行平均)
    // 参数[IN]: Stream : 指定流
    //           Result : 指定的需要剥离的结果
    //           AvgCnt : 当前结果已经平均的次数
    // 返回值: 无
    //*****************************************************************************
    void Strip(int Stream, const VsaCommResult &Result, int AvgCnt);

    void SLEStrip(int Stream, const VsaSleCommResult &Result, int AvgCnt);

    void BTStrip(int Stream, const VsaBTCommResult &Result1, int AvgCnt);

    void Alg3GPPStrip(int Stream, const Vsa3GPPCommResult &Result, int AvgCnt);

    void BTStatistic(VsaBTCommResult2 &Result);

    void SLEStatistic(VsaSleCommResult2 &Result);

    int GetLastResultDemo(void)
    {
        return LastResultDemo;
    }

    void SetLastResultDemo(int Demo)
    {
        LastResultDemo = Demo;
    }

private:
    VsaCommResult m_AvgResult[MAX_NUM_OF_CHNNEL]; // 总的平均结果
    VsaCommResult m_MaxResult[MAX_NUM_OF_CHNNEL]; // 各项结果的最大值
    VsaCommResult m_MinResult[MAX_NUM_OF_CHNNEL]; // 各项结果的最小值
    int LastResultDemo = UNVALID_INT_VAL;         // 记录上一次的的解码方式

    VsaSleCommResult m_AvgSLEResult[MAX_NUM_OF_CHNNEL]; // 总的平均结果
    VsaSleCommResult m_MaxSLEResult[MAX_NUM_OF_CHNNEL]; // 各项结果的最大值
    VsaSleCommResult m_MinSLEResult[MAX_NUM_OF_CHNNEL]; // 各项结果的最小值

    VsaBTCommResult m_AvgBTResult[MAX_NUM_OF_CHNNEL]; // 总的平均结果
    VsaBTCommResult m_MaxBTResult[MAX_NUM_OF_CHNNEL]; // 各项结果的最大值
    VsaBTCommResult m_MinBTResult[MAX_NUM_OF_CHNNEL]; // 各项结果的最小值

    Vsa3GPPCommResult m_Avg3GPPResult[ALG_3GPP_MAX_STREAM]; // 总的平均结果
    Vsa3GPPCommResult m_Max3GPPResult[ALG_3GPP_MAX_STREAM]; // 各项结果的最大值
    Vsa3GPPCommResult m_Min3GPPResult[ALG_3GPP_MAX_STREAM]; // 各项结果的最小值
};

struct DataBurstFields
{
	int validflag = UNVALID_INT_VAL;
	int Mod = UNVALID_INT_VAL;
	int Len = UNVALID_INT_VAL; 
	int Res = UNVALID_INT_VAL;
	double EVM = UNVALID_DOUBLE_VAL;
    double Power = UNVALID_DOUBLE_VAL;

	int Reserved[16];
};

enum DataBurstEnum
 {
	ENUM_FIELD_LSTF = 0,
	ENUM_FIELD_LLTF,
	ENUM_FIELD_LSIG,
	ENUM_FIELD_RLSIG,
	ENUM_FIELD_USIG,
	ENUM_FIELD_EHTSIG,
	ENUM_FIELD_HTSIG,
	ENUM_FIELD_HESIGA,
	ENUM_FIELD_HESIGB,
	ENUM_FIELD_GFSTF,
	ENUM_FIELD_VHTSIGA,
	ENUM_FIELD_VHTSIGB,
	ENUM_FIELD_DataSTF,
	ENUM_FIELD_DataLTF,
	ENUM_FIELD_DataNSTF,
	ENUM_FIELD_Data,
    ENUM_FIELD_S1GLTFN,
    ENUM_FIELD_S1GSIG,
    ENUM_FIELD_S1GSIGA,
    ENUM_FIELD_S1GSIGB,
    ENUM_FIELD_PHR,
    ENUM_FIELD_MAX = 32,    //以后扩展枚举，这个值就要注意够不够了~后续再考虑直接放开
};

struct DataBurstFieldsInfo
{
    DataBurstFields Fieldtype[ENUM_FIELD_MAX];
};

struct DataInfo11ag
{
    double DataRate = UNVALID_DOUBLE_VAL;
    int SymbolCnt = UNVALID_INT_VAL;
    int PsduLen = UNVALID_INT_VAL;
    int CodeingRate = UNVALID_INT_VAL;
    int Modulation = UNVALID_INT_VAL;
};

struct DataInfo11agPlus
{
    int Dulpicate = UNVALID_INT_VAL;
    int DupBW = UNVALID_INT_VAL;
    int LSig_BitLen = UNVALID_INT_VAL;
    char LSig_Bit[24] = {0};
    int PuncFlag = UNVALID_INT_VAL;
    int Punc20Len = UNVALID_INT_VAL;
    int Punc20Flag[16] = {0};
    int Reserved[103];
};


struct DataInfo11b
{
    double DataRate = UNVALID_DOUBLE_VAL;
    int Length = UNVALID_INT_VAL;
    int PsduLen = UNVALID_INT_VAL;
    int PreambleType = UNVALID_INT_VAL;
    int SfdPass = UNVALID_INT_VAL;
    int HeaderPass = UNVALID_INT_VAL;
    int pad = UNVALID_INT_VAL;
};

struct DataInfo11n
{
    double DataRate = UNVALID_DOUBLE_VAL;
    int SymbolCnt = UNVALID_INT_VAL;
    int CodeingRate = UNVALID_INT_VAL;
    int PsduLen = UNVALID_INT_VAL;
    int Modulation = UNVALID_INT_VAL;

    int HTSigValid = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int Cbw = UNVALID_INT_VAL;
    int HTLen = UNVALID_INT_VAL;
    int Smooth = UNVALID_INT_VAL;
    int NotSnd = UNVALID_INT_VAL;
    int Aggreg = UNVALID_INT_VAL;
    int STBC = UNVALID_INT_VAL;
    int FecCode = UNVALID_INT_VAL;
    int ShortGi = UNVALID_INT_VAL;
    int ExtSStrms = UNVALID_INT_VAL;
    int Crc = UNVALID_INT_VAL;
    int Tail = UNVALID_INT_VAL;

    int LsigValid = UNVALID_INT_VAL;
    int Rate = UNVALID_INT_VAL;
    int Len = UNVALID_INT_VAL;
    int Parity = UNVALID_INT_VAL;
    int LsigTail = UNVALID_INT_VAL;
};

struct DataInfo11nPlus
{
    int LSig_BitLen = UNVALID_INT_VAL;
    char LSig_Bit[24] = {0};	////?0   1
    int HTSig_BitLen = UNVALID_INT_VAL;
    char HTSig_Bit[48] = {0};   ///0  1

    int Reserved[128];
};

struct DataInfo11ac
{
    double DataRate = UNVALID_DOUBLE_VAL;
    int SymbolCnt = UNVALID_INT_VAL;
    int CodeingRate = UNVALID_INT_VAL;
    int PsduLen = UNVALID_INT_VAL;
    int Modulation = UNVALID_INT_VAL;

    int VHTSigAValid = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int Bw = UNVALID_INT_VAL;
    int STBC = UNVALID_INT_VAL;
    int FecCode = UNVALID_INT_VAL;
    int Crc = UNVALID_INT_VAL;
    int ShortGi = UNVALID_INT_VAL;
    int Nvhtltf = UNVALID_INT_VAL;
    int Nsts = UNVALID_INT_VAL;
    int Nss = UNVALID_INT_VAL;
    int GroupId = UNVALID_INT_VAL;

    int VHTSigBValid = UNVALID_INT_VAL;
    int McsB = UNVALID_INT_VAL;
    int Len = UNVALID_INT_VAL;

    int LsigValid = UNVALID_INT_VAL;
    int Rate = UNVALID_INT_VAL;
    int LsigLen = UNVALID_INT_VAL;
    int pad = UNVALID_INT_VAL;
};

struct DataInfo11acPlus
{
    int PartialAID = UNVALID_INT_VAL;
    int TXOP_PS_NOT_ALLOWED = UNVALID_INT_VAL;
    int Beamformed = UNVALID_INT_VAL;
    int NotSnd = UNVALID_INT_VAL;
    int LDPC_Extra_Symbol = UNVALID_INT_VAL;
    int ShortGI_NsymDisamb = UNVALID_INT_VAL;

    int LSig_BitLen = UNVALID_INT_VAL;
    char LSig_Bit[24] = {0};
    int VHTSigA_BitLen = UNVALID_INT_VAL;
    char VHTSigA_Bit[48] = {0};
    int VHTSigB_BitLen[4] = {0};
    char VHTSigB_Bit[4][29] = {{0}};

    int Reserved[128];
};

#define MAX_AC_USER_NUM 4
#define MAX_DF_NUM 8

struct DataInfoAcMuMimo
{
    int MuMimoFlag = UNVALID_INT_VAL;
    int MuMimoUserNum = UNVALID_INT_VAL;
    int BW = UNVALID_INT_VAL;
    int SymbolCnt = UNVALID_INT_VAL;
    int STBC = UNVALID_INT_VAL;
    int Nvhtltf = UNVALID_INT_VAL;
    int ShortGi = UNVALID_INT_VAL;
    int GroupId = UNVALID_INT_VAL;
    int Reserved[66] = {0};

    struct {
        double DataRate = UNVALID_DOUBLE_VAL;
        int PsduLen = UNVALID_INT_VAL;
        int Crc = UNVALID_INT_VAL;
        int Mcs = UNVALID_INT_VAL;
        int Modulation = UNVALID_INT_VAL;
        int FecCode = UNVALID_INT_VAL;
        int CodingRate = UNVALID_INT_VAL;
        int Nsts = UNVALID_INT_VAL;
        int Nss = UNVALID_INT_VAL;
        double AllEvm = UNVALID_DOUBLE_VAL;
        double DataEvm = UNVALID_DOUBLE_VAL;
        double PilotEvm = UNVALID_DOUBLE_VAL;
        double UserPower = UNVALID_DOUBLE_VAL;
        int UserNstsFlag[MAX_DF_NUM] = {UNVALID_INT_VAL};
        double UserPowerNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double PilotEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double DateEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double AllEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        int vthbLength = UNVALID_INT_VAL;
        int Reserved1 = UNVALID_INT_VAL;
        double Reserved[63] = {0};
    }UserInfo[MAX_AC_USER_NUM];
};

#define COMMON8BIT_MAX_NUM 66
#define MAX_USER_NUM 74

struct DataInfo11ax
{
    int HeSigAValid = UNVALID_INT_VAL;
    int Bw = UNVALID_INT_VAL;
    int PsduFormat = UNVALID_INT_VAL;
    int UserNum = UNVALID_INT_VAL;
    int SymbolCnt = UNVALID_INT_VAL;
    int HeltfNum = UNVALID_INT_VAL;
    int HeltType = UNVALID_INT_VAL;
    int Reserved = UNVALID_INT_VAL;       // 预留字段，主要为了字节对齐
    double DataRate = UNVALID_DOUBLE_VAL;
    double HeltfLen = UNVALID_DOUBLE_VAL;
    double GILen = UNVALID_DOUBLE_VAL;
    double HeDataSymLen = UNVALID_DOUBLE_VAL;
    double FrameLen = UNVALID_DOUBLE_VAL;

    int HeSigBValid  = UNVALID_INT_VAL;
    int SigBDcm = UNVALID_INT_VAL;
    int SigBMcs = UNVALID_INT_VAL;
    int SigBSymbol = UNVALID_INT_VAL;
    int Common8BitLen = UNVALID_INT_VAL;
    int Common8Bit[COMMON8BIT_MAX_NUM] = {0};

    int PreTxBF = UNVALID_INT_VAL;
    int LDPCExtra = UNVALID_INT_VAL;
    int PE = UNVALID_INT_VAL;
    int PeLen = UNVALID_INT_VAL;
    int PreFEC = UNVALID_INT_VAL;
    int Doppler = UNVALID_INT_VAL;
    int Midamble_Periodicity = UNVALID_INT_VAL;
    int MuMimoFlag = UNVALID_INT_VAL;            //1采用新结构读取，0保持原来模式不变，按原方式读取
    int SIGBCompression = UNVALID_INT_VAL;
    int RealUserNum = UNVALID_INT_VAL;

    int TXOP = UNVALID_INT_VAL;
    int SpatialReuseNum = UNVALID_INT_VAL;      //控制Spatial Reuse输出数量，TB 4个
    int SpatialReuse[4] = {0};                  //非TB时1个，TB时4个
    int TFReserved[9] = {0};                    //TB特有的，其余格式为0
    int SoundingNDP = UNVALID_INT_VAL;
    int PuncturingMode = UNVALID_INT_VAL;

    //Lsig
    int LsigValid = UNVALID_INT_VAL;
    int LsigRate = UNVALID_INT_VAL;
    int LsigLen = UNVALID_INT_VAL;

    int Reserved1[105]= {0};       // 预留字段，主要为了字节对齐


    struct {
        int Valid = UNVALID_INT_VAL;
        int ULDL = UNVALID_INT_VAL;
        int UserId = UNVALID_INT_VAL;
        int ToneWide = UNVALID_INT_VAL;
        int ToneIdx = UNVALID_INT_VAL;
        int Isegment = UNVALID_INT_VAL;
        int Mcs = UNVALID_INT_VAL;
        int Modulation = UNVALID_INT_VAL;
        int LDPC = UNVALID_INT_VAL;
        int CodingRate = UNVALID_INT_VAL;
        int STBC = UNVALID_INT_VAL;
        int DCM = UNVALID_INT_VAL;
        int Nsts = UNVALID_INT_VAL;
        int PsduCrc = UNVALID_INT_VAL;
        int PsduLen = UNVALID_INT_VAL;
        int UserAID = UNVALID_INT_VAL;
        double PowerFactor = UNVALID_DOUBLE_VAL;
        double UserPower = UNVALID_DOUBLE_VAL;
        double UserRate = UNVALID_DOUBLE_VAL;
        double PilotEvm = UNVALID_DOUBLE_VAL;
        double DataEvm = UNVALID_DOUBLE_VAL;
        double AllEvm = UNVALID_DOUBLE_VAL;
        int UserNstsFlag[MAX_DF_NUM] = {UNVALID_INT_VAL};
        double UserPowerNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double PilotEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double DateEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double AllEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        int Beamformed = UNVALID_INT_VAL;
        int NSS = UNVALID_INT_VAL;
        double Reserved[27]= {0};       // 预留字段
    } UserInfo[MAX_USER_NUM];
};

struct UserInfo11ax
{
    //int Valid = UNVALID_INT_VAL;
    int ULDL = UNVALID_INT_VAL;
    int UserId = UNVALID_INT_VAL;
    int ToneWide = UNVALID_INT_VAL;
    int ToneIdx = UNVALID_INT_VAL;
    int Isegment = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int Modulation = UNVALID_INT_VAL;
    int LDPC = UNVALID_INT_VAL;
    int CodingRate = UNVALID_INT_VAL;
    int STBC = UNVALID_INT_VAL;
    int DCM = UNVALID_INT_VAL;
    int Nsts = UNVALID_INT_VAL;
    int PsduCrc = UNVALID_INT_VAL;
    int PsduLen = UNVALID_INT_VAL;
    int UserAID = UNVALID_INT_VAL;
    int Reserved1 = UNVALID_INT_VAL; //对齐，预留字段
    double PowerFactor = UNVALID_DOUBLE_VAL;
    double UserPower = UNVALID_DOUBLE_VAL;
    double UserRate = UNVALID_DOUBLE_VAL;
    double PilotEvm = UNVALID_DOUBLE_VAL;
    double DataEvm = UNVALID_DOUBLE_VAL;
    double AllEvm = UNVALID_DOUBLE_VAL;
    int UserNstsFlag[MAX_DF_NUM] = {UNVALID_INT_VAL};
    double UserPowerNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
    double PilotEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
    double DateEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
    double AllEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
    int Beamformed = UNVALID_INT_VAL;
    int NSS = UNVALID_INT_VAL;
    double Reserved[31]= {0};       // 预留字段
};

//mu-mimo 单个ru的ofdma结果结构体
struct RuOfdmaInfo11ax
{
    int ValidFlag = 0;  //Ru有效性
    int Ue_Num = 0;     //Ru内的用户数
    int RuNsts = 0;     //Ru的总流数
    int Reserved1 = 0;  //对齐，预留字段
    double q_matrix_r[DF_Max_AnaNum][DF_Max_AnaNum];
    double q_matrix_i[DF_Max_AnaNum][DF_Max_AnaNum];
    UserInfo11ax User[DF_Max_Ru_UeNum_11ax];
    double Reserved[64]= {0};       // 预留字段
};

typedef struct
{
    int ValidFlag = 0;  //有效性	  	
    int AntNum = 0;
    int StsNum = 0;
    int Reserved1 = 0;  //对齐，预留字段
    Complex QMatrix[DF_Max_AnaNum][DF_Max_LTFNum];
    Complex QMatrixInv[DF_Max_AnaNum][DF_Max_LTFNum];
    double Reserved[64] = { 0 };       // 预留字段
}QMatrixInformation;

struct EHT_COMMON_INFO
{
    int PPDU = UNVALID_INT_VAL;
    int RUCnt = UNVALID_INT_VAL;
    int UserCnt = UNVALID_INT_VAL;
    int ActiveUserCnt = UNVALID_INT_VAL;
    int NSTS = UNVALID_INT_VAL;
    int SymbolCnt = UNVALID_INT_VAL;
    int MUMIMO = UNVALID_INT_VAL;
    int Reserved_0 = UNVALID_INT_VAL;
    double DataRate = UNVALID_DOUBLE_VAL;
    double PELen = UNVALID_DOUBLE_VAL;
    double GILen = UNVALID_DOUBLE_VAL;
    double LTFLen = UNVALID_DOUBLE_VAL;
    double SymbolLen = UNVALID_DOUBLE_VAL;
    double PreambleLen = UNVALID_DOUBLE_VAL;
    double DataLen = UNVALID_DOUBLE_VAL;
    double FrameLen = UNVALID_DOUBLE_VAL;
    double Reserved[32] = {0};
};

struct EHT_USIG_INFO
{
    int CRC = UNVALID_INT_VAL;
    int PhyVersion = UNVALID_INT_VAL;
    int BW = UNVALID_INT_VAL;
    int ULDL = UNVALID_INT_VAL;
    int BSSColor = UNVALID_INT_VAL;
    int TXOP = UNVALID_INT_VAL;
    int Disregard = UNVALID_INT_VAL;
    int B25Valid = UNVALID_INT_VAL;
    int CompressionMode = UNVALID_INT_VAL;
    int U2B2Valid = UNVALID_INT_VAL;
    int PuncBitLen = UNVALID_INT_VAL;
    int PuncBit[20];
    int U2B8Valid = UNVALID_INT_VAL;
    int SIGMCS = UNVALID_INT_VAL;
    int SIGSymbol = UNVALID_INT_VAL;
    int SoundingNDP = UNVALID_INT_VAL;
    int TB_ValidB2 = UNVALID_INT_VAL;
    int TB_SIG1_Disregard = UNVALID_INT_VAL;
    int TB_SIG2_Disregard = UNVALID_INT_VAL;
    int TB_SpaReuse[2];
    double Reserved[29] = {0}; // 预留字段
};

struct EHT_SIG_INFO
{
    int CRC = UNVALID_INT_VAL;
    int SpatialReuseLen = UNVALID_INT_VAL;
    int SpatialReuse[4];
    int GILTFSize = UNVALID_INT_VAL;
    int LTFSymbol = UNVALID_INT_VAL;
    int LDPCExtra = UNVALID_INT_VAL;
    int PreFEC = UNVALID_INT_VAL;
    int PEDisambiguity = UNVALID_INT_VAL;
    int Disregard = UNVALID_INT_VAL; //字节对齐
    int Common9BitNum = UNVALID_INT_VAL;
    int EHTSIG = UNVALID_INT_VAL;
    int Common9Bit[16];
    int EhtLtfType = UNVALID_INT_VAL;
    int ReservedInt = 0;       // 预留字段
    double Reserved[31] = {0}; // 预留字段
};

struct EHT_LSIG
{
    int CRC = UNVALID_INT_VAL;
    int PSDULen = UNVALID_INT_VAL;
    double DataRate = UNVALID_DOUBLE_VAL;
    int ParityBit = UNVALID_INT_VAL;
    int ParityCheck = UNVALID_INT_VAL;
    int Reserved[32] = {0}; // 预留字段
};

struct DataInfo11Be
{
    EHT_COMMON_INFO common;
    EHT_USIG_INFO usig;
    EHT_SIG_INFO sig;
    EHT_LSIG lsig;
};

///EHT User的结构体
struct UserInfo11Be
{
    int UserID = UNVALID_INT_VAL;
    int AID = UNVALID_INT_VAL;
    int MCS = UNVALID_INT_VAL;
    int NSS = UNVALID_INT_VAL;
    int NSTS = UNVALID_INT_VAL;
    int Beamformed = UNVALID_INT_VAL;
    int CodingType = UNVALID_INT_VAL;
    int DCM = UNVALID_INT_VAL;
    int SpatialConfig = UNVALID_INT_VAL;
    int CodingRate = UNVALID_INT_VAL;
    int Modulation = UNVALID_INT_VAL;
    int PSDUCRC = UNVALID_INT_VAL;
    int PSDULength = UNVALID_INT_VAL;
    int ampduValid = UNVALID_INT_VAL;
    int ampduNum = UNVALID_INT_VAL;

    double Power = UNVALID_DOUBLE_VAL;
    double Rate = UNVALID_DOUBLE_VAL;
    double PilotEvm = UNVALID_DOUBLE_VAL;
    double DataEvm = UNVALID_DOUBLE_VAL;
    double AllEvm = UNVALID_DOUBLE_VAL;
    int NstsFlag[MAX_DF_NUM];
    double PowerNsts[MAX_DF_NUM];
    double PilotEvmNsts[MAX_DF_NUM];
    double DataEvmNsts[MAX_DF_NUM];
    double AllEvmNsts[MAX_DF_NUM];
    int ToneWide = UNVALID_INT_VAL;
    int ToneIdx = UNVALID_INT_VAL;
    double Reserved[31]; // 预留字段
};

//EHT RU的结构体
struct EHT_RU
{
    int ValidFlag = UNVALID_INT_VAL; //RU有效性，0为无效，0时其他内容都显示--
    int UeNum = UNVALID_INT_VAL;     //RU内的用户数
    int RUNsts = UNVALID_INT_VAL;    //RU的总流数
    int ToneWide = UNVALID_INT_VAL;
    int ToneIndex = UNVALID_INT_VAL;
    int NSD = UNVALID_INT_VAL;
    int NSP = UNVALID_INT_VAL;
    int Reserved1; //对齐，预留字段

    double PilotEVM = UNVALID_DOUBLE_VAL;
    double DataEVM = UNVALID_DOUBLE_VAL;
    double AllEVM = UNVALID_DOUBLE_VAL;
    double Power = UNVALID_DOUBLE_VAL;
    int NstsFlag[MAX_DF_NUM];
    double PowerNsts[MAX_DF_NUM];
    double PilotEvmNsts[MAX_DF_NUM];
    double DataEvmNsts[MAX_DF_NUM];
    double AllEvmNsts[MAX_DF_NUM];

    UserInfo11Be User[8];
    double Reserved[64]; // 预留字段
};

struct DataInfoBT
{
    double InitFreqErr = UNVALID_DOUBLE_VAL;
    double OmegaI = UNVALID_DOUBLE_VAL;
    double OmegaIO = UNVALID_DOUBLE_VAL;
    double OmegaO = UNVALID_DOUBLE_VAL;
    double DevmAvg = UNVALID_DOUBLE_VAL;
    double DevmPeak = UNVALID_DOUBLE_VAL;
    double DiffPower = UNVALID_DOUBLE_VAL;
    double MaxFreqVar = UNVALID_DOUBLE_VAL;
    double Devm = UNVALID_DOUBLE_VAL;
    double FreqDrift = UNVALID_DOUBLE_VAL;
    double FreqDriftRate = UNVALID_DOUBLE_VAL;
    double F1Max = UNVALID_DOUBLE_VAL;
    double F1Avg = UNVALID_DOUBLE_VAL;
    double F2Max = UNVALID_DOUBLE_VAL;
    double F2Avg = UNVALID_DOUBLE_VAL;
    double FnMax = UNVALID_DOUBLE_VAL;
    double F0FnMax = UNVALID_DOUBLE_VAL;
    double F1F0 = UNVALID_DOUBLE_VAL;
    double FnFn5Max = UNVALID_DOUBLE_VAL;
    double F0F3 = UNVALID_DOUBLE_VAL;
    double FnFn3 = UNVALID_DOUBLE_VAL;
    int DataRate = UNVALID_INT_VAL;
    int PktType = UNVALID_INT_VAL;
    int PktLen = UNVALID_INT_VAL;
    int PayloadHeader = UNVALID_INT_VAL;
    int CrcStatus = UNVALID_INT_VAL;
};

#define Slot_MAX_CTENum 74
struct DataInfoBTPlus
{
    int LAP = UNVALID_INT_VAL;
    int UAP = UNVALID_INT_VAL;
    int CTEInfo = UNVALID_INT_VAL;
    int CTEType = UNVALID_INT_VAL;
    int CTE_DurationT = UNVALID_INT_VAL;
    int EDR_SynSeq_ErrBit_Num = UNVALID_INT_VAL;
    int EDR_Trailer_ErrBit_Num = UNVALID_INT_VAL;
    int LT_ADDR = UNVALID_INT_VAL;
    int Flow = UNVALID_INT_VAL;
    int ARQN = UNVALID_INT_VAL;
    int SEQN = UNVALID_INT_VAL;
    int LLID = UNVALID_INT_VAL;
    int mFlow = UNVALID_INT_VAL;
    int PayLoadSize = UNVALID_INT_VAL;
    int Payload_EIR = UNVALID_INT_VAL;
    int Payload_SR = UNVALID_INT_VAL;
    int Payload_ClassofDevice = UNVALID_INT_VAL;
    int Payload_LTAddr = UNVALID_INT_VAL;
    int Payload_CLK27b2 = UNVALID_INT_VAL;
    int BLEMapperS = UNVALID_INT_VAL;
    double F0FnAvg = UNVALID_DOUBLE_VAL;
    double Freq_offset_sync = UNVALID_DOUBLE_VAL;
    double EDR_GuardTime = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Avg = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Peak = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Peak_sub_Avg = UNVALID_DOUBLE_VAL;
    double CTE_Fsi_Max = UNVALID_DOUBLE_VAL;
    double CTE_Fsi_Min = UNVALID_DOUBLE_VAL;
    double CTE_Fs1_sub_Fp = UNVALID_DOUBLE_VAL;
    double CTE_Fsi_sub_F0_Max = UNVALID_DOUBLE_VAL;
    double CTE_Fsi_sub_Fsi3_Max = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Ref_Avg = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Ref_DevDivAvg = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Pn_DevDivAvg_Max = UNVALID_DOUBLE_VAL;
    double CTE_Pwr_Avg_Slot[Slot_MAX_CTENum] = {-999.99};
    double Delta_F1_Min = UNVALID_DOUBLE_VAL;
    double Delta_F2_Min = UNVALID_DOUBLE_VAL;
    int Header_bin[54] = {-999};
    int VoiceField[10] = {-999};
    double Delta_F1_99p9PCT = UNVALID_DOUBLE_VAL;
    double Delta_F2_99p9PCT = UNVALID_DOUBLE_VAL;
    double EDR_GFSKPower = UNVALID_DOUBLE_VAL;
    double EDR_DPSKPower = UNVALID_DOUBLE_VAL;
    int Pattern = UNVALID_INT_VAL;
    int EnhancedMode = UNVALID_INT_VAL;
    int Payload_header_len = UNVALID_INT_VAL;
    int Res = UNVALID_INT_VAL;
    double Reserved[109] = {0};
};

struct DataInfoZwave    //zwave info
{
    double InitFreqErr = UNVALID_DOUBLE_VAL;
    double FreqDeviRMS = UNVALID_DOUBLE_VAL;
    double FreqDeviMAX = UNVALID_DOUBLE_VAL;
    double FreqDeviMIN = UNVALID_DOUBLE_VAL;
    double ZeroErrRMS = UNVALID_DOUBLE_VAL;
    double ZeroErrPeak = UNVALID_DOUBLE_VAL;
    double SymbolClockErr = UNVALID_DOUBLE_VAL;
    double SymbolClockJitter = UNVALID_DOUBLE_VAL;
    double DataRate = UNVALID_DOUBLE_VAL;
    int PsduLen = UNVALID_INT_VAL;
    int PsdeCrcPass = UNVALID_INT_VAL;
    int SymbolCount = UNVALID_INT_VAL;
    int Reserved[63] = {0};
};

struct DataInfo11ba
{
    int BW = UNVALID_INT_VAL;   //MHz, 20,40,80
    int LSigParityPassed = UNVALID_INT_VAL;//0:fail; 1:pass
    int LSigRate = UNVALID_INT_VAL;
    int LSigLength = UNVALID_INT_VAL;
    int SubChanNum = UNVALID_INT_VAL;
    int PunctureFlag[MAX_SEGMENT_11BA_NUM] = {0};//0:NO;1:Yes
    int DataRateMode[MAX_SEGMENT_11BA_NUM] = {0};//0:LDR; 1:HDR
    int PsduLength[MAX_SEGMENT_11BA_NUM] = {0};//0-22
    int PsduCRC[MAX_SEGMENT_11BA_NUM] = {0};//0:fail; 1:pass
    double Reserved[128] = {0}; //保留字段
};

struct DataInfo11az
{
    int HeSigAValid = UNVALID_INT_VAL;
    int PuncturingMode = UNVALID_INT_VAL;
    int Bw = UNVALID_INT_VAL;
    int PsduFormat = UNVALID_INT_VAL;
    int RuNum = UNVALID_INT_VAL;
    int UserNum = UNVALID_INT_VAL;
    int HeltfNum = UNVALID_INT_VAL;
    int HeltType = UNVALID_INT_VAL;
    double HeltfLen = UNVALID_DOUBLE_VAL;
    double GILen = UNVALID_DOUBLE_VAL;
    double FrameLen = UNVALID_DOUBLE_VAL;
    double PeLen = UNVALID_INT_VAL;
    int BSSColor = UNVALID_INT_VAL;
    int PreTxBF = UNVALID_INT_VAL;
    int LDPCExtra = UNVALID_INT_VAL;
    int PE = UNVALID_INT_VAL;
    int PreFEC = UNVALID_INT_VAL;
    int Doppler = UNVALID_INT_VAL;
    int Midamble_Periodicity = UNVALID_INT_VAL;
    int TXOP = UNVALID_INT_VAL;
    int SpatialReuseNum = UNVALID_INT_VAL;      //控制Spatial Reuse输出数量，TB 4个
    int SpatialReuse[4] = {0};                  //非TB时1个，TB时4个
    int SigABitLen = UNVALID_INT_VAL;
    char SigABit[52] = {0};
    //Lsig
    int LsigValid = UNVALID_INT_VAL;
    int LsigRate = UNVALID_INT_VAL;
    int LsigLen = UNVALID_INT_VAL;              //bytes
    int LsigBitLen = UNVALID_INT_VAL;           //bits
    char LsigBit[24] = {0};
    int Reserved[128] = {0};                    // 预留字段
};

struct WURPHYInfo
{
    int SubChanNum = UNVALID_INT_VAL;
    int Reserved1 = UNVALID_INT_VAL; //对齐字节
    double SyncSymbPwrRatio[MAX_SEGMENT_11BA_NUM] = {0};
    double DataSymbPwrRatioMax[MAX_SEGMENT_11BA_NUM] = {0};
    double DataSymbPwrRatioAvg[MAX_SEGMENT_11BA_NUM] = {0};
    double DataSymbPwrRatioMin[MAX_SEGMENT_11BA_NUM] = {0};

    double CorrMetrMax = UNVALID_DOUBLE_VAL;
    double CorrMetrAvg = UNVALID_DOUBLE_VAL;
    double Reserved[128] = {0};//保留字段
};

//11ah
#define MAX_AH_USER_NUM 4
struct  AH_S1G_Short_Preamble_Info
{
    int ValidFlag = UNVALID_INT_VAL;            //有效标志
    int ReservedB0  = UNVALID_INT_VAL;          //保留位
    int BW = UNVALID_INT_VAL;                   //带宽
    int STBC = UNVALID_INT_VAL;                 //是否空时块编码
    int ULDL = UNVALID_INT_VAL;                 //上行链路指示
    int NSS = UNVALID_INT_VAL;
    int Nsts = UNVALID_INT_VAL;
    int ID = UNVALID_INT_VAL;
    int ShortGi = UNVALID_INT_VAL;              //是否是短GI
    int CodingType = UNVALID_INT_VAL;           //编码方式 0：BCC；1：LDPC
    int LDPCExtra = UNVALID_INT_VAL;
    int MCS = UNVALID_INT_VAL;
    int Smoothing = UNVALID_INT_VAL;
    int IsAggregation = UNVALID_INT_VAL;        //是否聚合
    int Length = UNVALID_INT_VAL;
    int ResponseIndication = UNVALID_INT_VAL;
    int TravelingPilots = UNVALID_INT_VAL;
    int NDPIndication = UNVALID_INT_VAL;        //NDP指示
    int CRC = UNVALID_INT_VAL;
    int Tail = UNVALID_INT_VAL;                 //尾比特
    int SigBitLen = UNVALID_INT_VAL;
    int SigBit[48] = {0};
    int Reserved[20] = {0};
};

struct AH_S1G_Long_Preamble_SU_Info
{
    int ValidFlag = UNVALID_INT_VAL;            //有效标志
    int BW = UNVALID_INT_VAL;                   //带宽
    int STBC = UNVALID_INT_VAL;                 //是否空时块编码
    int ULDL = UNVALID_INT_VAL;                 //上行链路指示
    int NSS = UNVALID_INT_VAL;
    int Nsts = UNVALID_INT_VAL;
    int ID = UNVALID_INT_VAL;
    int ShortGi = UNVALID_INT_VAL;              //是否是短GI
    int CodingType = UNVALID_INT_VAL;           //编码方式 0：BCC；1：LDPC
    int LDPCExtra = UNVALID_INT_VAL;
    int MCS = UNVALID_INT_VAL;
    int BeamChange = UNVALID_INT_VAL;
    int Smoothing = UNVALID_INT_VAL;
    int IsAggregation = UNVALID_INT_VAL;        //是否聚合
    int Length = UNVALID_INT_VAL;
    int ResponseIndication = UNVALID_INT_VAL;
    int TravelingPilots = UNVALID_INT_VAL;
    int ReservedA2B12 = UNVALID_INT_VAL;
    int CRC = UNVALID_INT_VAL;
    int Tail = UNVALID_INT_VAL;                 //尾比特
    int SigABitLen = UNVALID_INT_VAL;
    int SigABit[48] = {0};
    int Reserved[20] = {0};
};

struct AH_S1G_Long_Preamble_MU_Info
{
    int ValidFlag = UNVALID_INT_VAL;            //有效标志
    int BW = UNVALID_INT_VAL;                   //带宽
    int STBC = UNVALID_INT_VAL;                 //是否空时块编码
    int ReservedA1B2 = UNVALID_INT_VAL;
    int NSS = UNVALID_INT_VAL;
    int Nsts = UNVALID_INT_VAL;
    int GroupID = UNVALID_INT_VAL;
    int CodingTypeI = UNVALID_INT_VAL;              //是否是短GI
    int CodingTypeII = UNVALID_INT_VAL;           //编码方式 0：BCC；1：LDPC
    int ReservedA2B1 = UNVALID_INT_VAL;
    int Length = UNVALID_INT_VAL;
    int ResponseIndication = UNVALID_INT_VAL;
    int TravelingPilots = UNVALID_INT_VAL;
    int CRC = UNVALID_INT_VAL;
    int Tail = UNVALID_INT_VAL;                 //尾比特
    int SigABitLen = UNVALID_INT_VAL;
    int SigABit[48] = {0};
    int Reserved[20] = {0};
};

struct AH_S1G_S1M_Preamble_Info
{
    int ValidFlag = UNVALID_INT_VAL;            //有效标志
    int BW = UNVALID_INT_VAL;                   //带宽
    int STBC = UNVALID_INT_VAL;                 //是否空时块编码
    int NSS = UNVALID_INT_VAL;
    int Nsts = UNVALID_INT_VAL;
    int ShortGi = UNVALID_INT_VAL;              //是否是短GI
    int CodingType = UNVALID_INT_VAL;           //编码方式 0：BCC；1：LDPC
    int LDPCExtra = UNVALID_INT_VAL;
    int ReservedB6 = UNVALID_INT_VAL;
    int MCS = UNVALID_INT_VAL;
    int IsAggregation = UNVALID_INT_VAL;        //是否聚合
    int Length = UNVALID_INT_VAL;
    int ResponseIndication = UNVALID_INT_VAL;
    int TravelingPilots = UNVALID_INT_VAL;
    int Smoothing = UNVALID_INT_VAL;
    int NDPIndication = UNVALID_INT_VAL;
    int CRC = UNVALID_INT_VAL;
    int Tail = UNVALID_INT_VAL;                 //尾比特
    int SigBitLen = UNVALID_INT_VAL;
    int SigBit[48] = {0};
    int Reserved[20] = {0};
};

struct DataInfo11ah
{
    int PreambleType = UNVALID_INT_VAL;         //前导类型,类型见枚举AH_PREAMBLE_TYPE
    int UserNumber = UNVALID_INT_VAL;           //用户总数
    int Reserved[20] = {0};

    AH_S1G_Short_Preamble_Info S1GShortInfo;
    AH_S1G_Long_Preamble_SU_Info S1GLongSuInfo;
    AH_S1G_Long_Preamble_MU_Info S1GLongMuInfo;
    AH_S1G_S1M_Preamble_Info S1GS1MInfo;

    struct
    {
        double DataRate = UNVALID_DOUBLE_VAL;
        int PsduLen = UNVALID_INT_VAL;
        int Crc = UNVALID_INT_VAL;
        int Mcs = UNVALID_INT_VAL;
        int Modulation = UNVALID_INT_VAL;
        int FecCode = UNVALID_INT_VAL;
        int SigBMCS = UNVALID_INT_VAL;
        int SigBCRC = UNVALID_INT_VAL;
        int SigBTail = UNVALID_INT_VAL;
        int SigBBitLen = UNVALID_INT_VAL;
        char SigBBit[32] = {0};             //最多29bit，剩余的字节对齐
        int CodingRate = UNVALID_INT_VAL;
        int Nsts = UNVALID_INT_VAL;
        int Nss = UNVALID_INT_VAL;
        double AllEvm = UNVALID_DOUBLE_VAL;
        double DataEvm = UNVALID_DOUBLE_VAL;
        double PilotEvm = UNVALID_DOUBLE_VAL;
        double UserPower = UNVALID_DOUBLE_VAL;
        int UserNstsFlag[MAX_DF_NUM] = {UNVALID_INT_VAL};
        double UserPowerNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double PilotEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double DateEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double AllEvmNsts[MAX_DF_NUM] = {UNVALID_DOUBLE_VAL};
        double Reserved[56] = {0};
    }UserInfo[MAX_AH_USER_NUM];
};
//end 11ah

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int BroadType = UNVALID_INT_VAL;
    int PacketType = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[8];
} SparkLinkCtrlInfoTypeA1;

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int PacketType = UNVALID_INT_VAL;
    int EmptyPacketInd = UNVALID_INT_VAL;
    int SndSN = UNVALID_INT_VAL;
    int RevSN = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int RevSysFrmInd = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[4];
} SparkLinkCtrlInfoTypeA2;

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int PacketType = UNVALID_INT_VAL;
    int EmptyPacketInd = UNVALID_INT_VAL;
    int SndSN = UNVALID_INT_VAL;
    int RevSN = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int ScheduleInd = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[4];
} SparkLinkCtrlInfoTypeA3;

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int PacketType = UNVALID_INT_VAL;
    int EmptyPacketInd = UNVALID_INT_VAL;
    int SndSN = UNVALID_INT_VAL;
    int RevSN = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int ScheduleInd = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[4];
} SparkLinkCtrlInfoTypeA4;

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int PacketType = UNVALID_INT_VAL;
    int EmptyPacketInd = UNVALID_INT_VAL;
    int SndSN = UNVALID_INT_VAL;
    int RevSN = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int ScheduleInd = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[4];
} SparkLinkCtrlInfoTypeA5;

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int PacketType = UNVALID_INT_VAL;
    int DataPacketSN = UNVALID_INT_VAL;
    int DataPacketGrp = UNVALID_INT_VAL;
    int EndInd = UNVALID_INT_VAL;
    int RevSysFrmInd = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[5];
} SparkLinkCtrlInfoTypeA6;

typedef struct
{
    int Mcs = UNVALID_INT_VAL;
    int DataPacketSN = UNVALID_INT_VAL;
    int DataPacketGrp = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[8];
} SparkLinkCtrlInfoTypeA7;

typedef struct
{
    int FrmFormatInd = UNVALID_INT_VAL;
    int HarqFeedback = UNVALID_INT_VAL;
    int DataPacketSN = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int UpperLinkInd = UNVALID_INT_VAL;
    int Reversed[5];
} SparkLinkCtrlInfoTypeB1;

typedef struct
{
    int HarqFeedback = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int UpperLinkInd = UNVALID_INT_VAL;
    int Reversed[9];
} SparkLinkCtrlInfoTypeB2;

typedef struct
{
    int DataPacketGrp = UNVALID_INT_VAL;
    int DataPacketSN = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int MaxDataPacketSNInd = UNVALID_INT_VAL;
    int Reversed[6];
} SparkLinkCtrlInfoTypeB3;

typedef struct
{
    int BrdcastSetFlag = UNVALID_INT_VAL;
    int BrdcastSetUpdateInd = UNVALID_INT_VAL;
    int DataPacketSN = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int FlowCtrlInd = UNVALID_INT_VAL;
    int MaxDataPacketSNInd = UNVALID_INT_VAL;
    int Reversed[5];
} SparkLinkCtrlInfoTypeB4;

typedef struct
{
    int MsgTypeInd = UNVALID_INT_VAL;
    int ConnectInd = UNVALID_INT_VAL;
    int DiscoveryInd = UNVALID_INT_VAL;
    int DirectInd = UNVALID_INT_VAL;
    int NonDirectInd = UNVALID_INT_VAL;
    int DataUpdateInd = UNVALID_INT_VAL;
    int Mcs = UNVALID_INT_VAL;
    int DataLength = UNVALID_INT_VAL;
    int Reversed[4];
} SparkLinkCtrlInfoTypeB5;


struct DataInfoSparkLink
{
    int FrmType = UNVALID_INT_VAL;
    int Bandwidth = UNVALID_INT_VAL;
    int PID = UNVALID_INT_VAL;
    int PayloadLen = UNVALID_INT_VAL;
    int PayloadCrcType = UNVALID_INT_VAL;
    int PayloadCrc = UNVALID_INT_VAL;
    int CtrlInfoType = UNVALID_INT_VAL;
    int CtrlInfoCrc = UNVALID_INT_VAL;
    double Reserved1 = UNVALID_DOUBLE_VAL;
    double Delta_fd1_Avg = UNVALID_DOUBLE_VAL;
    double Delta_fd1_Max = UNVALID_DOUBLE_VAL;
    double Delta_fd1_Min = UNVALID_DOUBLE_VAL;
    double Delta_fd2_Avg = UNVALID_DOUBLE_VAL;
    double Delta_fd2_Min = UNVALID_DOUBLE_VAL;
    double EvmAvg = UNVALID_DOUBLE_VAL;
    double EvmPeak = UNVALID_DOUBLE_VAL;
    double Evm99PCT = UNVALID_DOUBLE_VAL;
    double Init_Freq_Error = UNVALID_DOUBLE_VAL;
    double Max_Freq_Drift = UNVALID_DOUBLE_VAL;
    double Freq_Drift_Rate = UNVALID_DOUBLE_VAL;
    double CtrlInfoEvmAvg = UNVALID_DOUBLE_VAL;
    double CtrlInfoEvmPeak = UNVALID_DOUBLE_VAL;
    double CtrlInfoEvm99PCT = UNVALID_DOUBLE_VAL;
    double ZeroCrossingErr = UNVALID_DOUBLE_VAL;
    double SymClkErr = UNVALID_DOUBLE_VAL;
    double MaxTimeDev = UNVALID_DOUBLE_VAL;
    double Delta_fd2_99PCT = UNVALID_DOUBLE_VAL;
    double Reserved[120]; //保留位
};

struct CtrlInfoCfg
{
    SparkLinkCtrlInfoTypeA1 CtrlInfoTypeA1;
    SparkLinkCtrlInfoTypeA2 CtrlInfoTypeA2;
    SparkLinkCtrlInfoTypeA3 CtrlInfoTypeA3;
    SparkLinkCtrlInfoTypeA4 CtrlInfoTypeA4;
    SparkLinkCtrlInfoTypeA5 CtrlInfoTypeA5;
    SparkLinkCtrlInfoTypeA6 CtrlInfoTypeA6;
    SparkLinkCtrlInfoTypeA7 CtrlInfoTypeA7;
    SparkLinkCtrlInfoTypeB1 CtrlInfoTypeB1;
    SparkLinkCtrlInfoTypeB2 CtrlInfoTypeB2;
    SparkLinkCtrlInfoTypeB3 CtrlInfoTypeB3;
    SparkLinkCtrlInfoTypeB4 CtrlInfoTypeB4;
    SparkLinkCtrlInfoTypeB5 CtrlInfoTypeB5;
};

struct DataInfoWiSun
{
    int PHR_Flag = UNVALID_INT_VAL;
    int Rate_MCS = UNVALID_INT_VAL;
    int Frame_Len = UNVALID_INT_VAL;
    int Scrambler = UNVALID_INT_VAL;
    int Symbol_Cnt = UNVALID_INT_VAL;
    int Coding_rate = UNVALID_INT_VAL;
    int Modulation = UNVALID_INT_VAL;
    int PSDU_Cnt = UNVALID_INT_VAL;
    int PSDU_CRC = UNVALID_INT_VAL;

    double Date_Rate = UNVALID_DOUBLE_VAL;
    double BW = UNVALID_DOUBLE_VAL;
    
    int PHR_Bit[36];
    int CRC_Bit[8];

    int Spreading_Mode = UNVALID_INT_VAL;
    int Rate_Mode = UNVALID_INT_VAL;
    int Chip_Rate = UNVALID_INT_VAL;
    int PhyFSKPreambleLength = UNVALID_INT_VAL;

    int PhySunFskSfd = UNVALID_INT_VAL;
    int PhyFcsFecEnabled = UNVALID_INT_VAL;
    int PhyFSkFecScheme = UNVALID_INT_VAL;
    int PhyFskFecInterleavingRsc = UNVALID_INT_VAL;
    int ModeSwitch = UNVALID_INT_VAL;
    int FCSType = UNVALID_INT_VAL;
    int DataWhitening = UNVALID_INT_VAL;
    int CRC = UNVALID_INT_VAL;

    double Fdev_Min_2fsk = UNVALID_DOUBLE_VAL;
    double Fdev_Min_2fsk_Value = UNVALID_DOUBLE_VAL;
    double Fdev_Max_2fsk = UNVALID_DOUBLE_VAL;
    double Fdev_Max_2fsk_Value = UNVALID_DOUBLE_VAL;
    double Fedv_Rms_2fsk = UNVALID_DOUBLE_VAL;
    double Zero_Cross_Tolerance_Min = UNVALID_DOUBLE_VAL;
    double Zero_Cross_Tolerance_Max = UNVALID_DOUBLE_VAL;
    double Zero_Cross_Tolerance_Rms = UNVALID_DOUBLE_VAL;

    double Reserved[114]; //保留位
};

#ifndef BE_RU_COUNT
#define BE_RU_COUNT 144
#endif

struct TbUnusedToneError
{
    int valid_flag;             //界面画图有效性，0不生效，1生效
    int total_ru26_num;         //RU26个数，横坐标长度
    int mask_valid_flag[BE_RU_COUNT];    //模板有效性，在1时候才画模板值
    int mask_value[BE_RU_COUNT];         //模板值，当有效性为1时才显示
    double unused_tone_error[BE_RU_COUNT];//Tone Error值，整体是连续的
    double Reserved[128];       //保留位
};

//A-MPDU information
struct AmpduInfo
{
    int ampdu_ind;    //ampdu子帧序号
    int ampdu_length; //mpdu长度
    int crc;          //crc
    int Reserved[32];
};

//power table info / per signal
struct PowerTableInfo
{
    double PowerFrame;
    double PowerPeak;
    char ValidFig[DF_Max_AnaNum];
    double StrmPwr[DF_Max_AnaNum];
};

//psdu decomposed info
struct DecomposeAmpduInfo
{
    s32 MpduID;
    s32 MpduDelimiterValid;
    s32 Eof;
    s32 Reserved;
    s32 MpduLength;
    s32 DelimiterCRC;
    u8  DeCRC0x;
    u8  DeSignature0x;
    s32 PayloadBitsLength;
    s32 PbitStart;
    s32 PbitEnd;
    u8  BodyFCS[4];
    s32 PaddingLength;
    s8  PaddingBit[24];
    s32 PreEmptyDelimiterNum;
    s32 PreErrorDelimiterNum;
};

// 结果项处理
class ResultItemHandler
{
public:
    using ResultFunc = std::function<void*(StOutInfo *, int, void *, int&)>;
    using Result8080Func = std::function<void*(Merge80And80Rslt *, int, void *, int&)>;
    using AxUserResultFunc = std::function<void*(StOutInfo *, int, int, void *, int&)>;
    using AxUser8080Func = std::function<void*(Merge80And80Rslt *, int, int, void *, int&)>;
    using CheckFunc = std::function<bool(StOutInfo *)>;
    using Result3GPPFunc = std::function<void*(Alg_3GPP_VsaOutInfo *, int, int, void *, int&)>;

    // 普通数据结果的构造函数
    ResultItemHandler(bool IsChain, int Offset, int Size, CheckFunc Func = nullptr)
        : m_IsChainData(IsChain), m_Offset(Offset), m_Size(Size), m_ValSize(Size), m_Check(Func)
    {
    }

    // vector结果数据的构造函数
    ResultItemHandler(bool IsChain, bool IsVector, int Offset, int CntValPos, int ValSize, CheckFunc Func = nullptr)
        : m_IsChainData(IsChain), m_IsVecData(IsVector), m_Offset(Offset), m_CntValPos(CntValPos), m_ValSize(ValSize), m_Check(Func)
    {
    }

    // 使用函数从out结构体中获取结果数据的构造函数
    ResultItemHandler(bool IsChain, ResultFunc Func, int MbrSize, bool IsVector = false)
        : m_IsChainData(IsChain), m_IsVecData(IsVector), m_Size(MbrSize), m_ValSize(MbrSize), m_Func(Func)
    {
    }

    // 使用函数从out结构体中获取结果数据的构造函数
    ResultItemHandler(Result8080Func Func, int MbrSize)
        : m_ValSize(MbrSize), m_8080Func(Func)
    {
    }

    // 使用函数从out结构体中获取11ax指定用户的结果数据的构造函数
    ResultItemHandler(bool IsChain, AxUserResultFunc Func, int MbrSize, bool IsVector = false)
        : m_IsChainData(IsChain), m_IsVecData(IsVector), m_ValSize(MbrSize), m_UserFunc(Func)
    {
    }

    ResultItemHandler(AxUser8080Func Func, int MbrSize) : m_ValSize(MbrSize), m_User8080Func(Func)
    {
    }

    // 3GPP 蜂窝 使用函数从out结构体中获取结果数据的构造函数
    ResultItemHandler(int MbrSize, Result3GPPFunc Func)
        : m_ValSize(MbrSize), m_3GPPFunc(Func)
    { 
    }

    //*****************************************************************************
    // 获取结果数据地址及长度
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果，如果获取的是通道数据则表示通道ID
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度
    //         DataType: 数据类型，返回的实际是数据类型的长度，比如int就返回sizeof(int)
    // 返回值: 结果地址
    //*****************************************************************************
    void *GetResult(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len, int &DataType, bool IsHMatrix = false);

    void *Get11axUserResult(StOutInfo *pRxOut, int Stream, int UserID, void *pBuf, int &Len, int &DataType);

    //*****************************************************************************
    // 获取8080结果数据地址及长度
    // 参数[IN]: pResult : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果，如果获取的是通道数据则表示通道ID
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度
    //         DataType: 数据类型，返回的实际是数据类型的长度，比如int就返回sizeof(int)
    // 返回值: 结果地址
    //*****************************************************************************
    void *GetResult(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len, int &DataType);

    void *GetResult(Alg_3GPP_VsaOutInfo *pResult, int Stream, int Segment, void *pBuf, int &Len, int &DataType);

    void *Get11axUserResult(Merge80And80Rslt *pResult, int Stream, int UserID, void *pBuf, int &Len, int &DataType);

private:
    void *GetResultInfo(void *BaseAddr, int &Len);

    void *GetDefaultResult(void *pBuf, int &Len);

private:
    bool m_IsChainData = false; // 是否为通道数据
    bool m_IsVecData = false;   // 数据是否为vector
    int m_Offset;               // 数据在结构体中的偏移
    int m_Size;                 // 数据长度
    int m_CntValPos;            // 当数据为vector时，记录vector数据成员个数的变量一般为结构体中的成员，此值表示这个成员在结构体中的偏移
    int m_ValSize;              // 如果内容是vector，此值表示vector内每个数据成员的大小

    ResultFunc m_Func = nullptr;                // 使用函数来获取结果
    Result8080Func m_8080Func = nullptr;        // 使用函数来获取8080合并结果
    AxUserResultFunc m_UserFunc = nullptr;      // 使用函数来获取11ax指定用户的结果
    AxUser8080Func m_User8080Func = nullptr;    // 使用函数来获取11ax指定用户的8080合并结果
    CheckFunc m_Check = nullptr;                // 检查结果是否有效
    Result3GPPFunc m_3GPPFunc = nullptr;        // 蜂窝结果获取
};

enum EvmRsultType
{
    EVM_PERCENT,    // evm_percent
    EVM_DB,         // evm_db
};


// 算法结果类
class AlgResult
{
public:

    struct FrmStruct
    {
        stEVM *pEvm = nullptr;
        stMisc *pMisc = nullptr;
        stEVM *p11axSigbEvm = nullptr;
        stMisc *p11axMisc = nullptr;
        st11B_DB *p11BFrm = nullptr;
        stBT_DB *pBTFrm = nullptr;
        stZIGBEE_DB *pZFrm = nullptr;
        stEVM_OFMDA *p11axUserEvm = nullptr;
        stZWAVE_DB *pZwaveFrm = nullptr;
        stGle_DB *pGleFrm = nullptr;
        stLRWPAN_OFDM_DB *pWiSunOFDMFrm = nullptr;
        stLRWPAN_FSK_DB *pWiSunFSKFrm = nullptr;
        stLRWPAN_OQPSK_DB *pWiSunQPSKFrm = nullptr;
        int i11axUserNum = 0;
        int RealStream = 0;     //真正取得算法的流ID结果~
    };

    static AlgResult &Instance()
    {
        static AlgResult Alg;
        return Alg;
    }

    //*****************************************************************************
    // 获取保存结果信息的对象
    // 参数[IN]: Type : 结果类型
    // 返回值: 保存信息的对象的指针
    //*****************************************************************************
    ResultItemHandler *GetResultItemHandler(const std::string &Type)
    {
        auto iter = m_RsltHdlrTbl.find(Type);
        if (iter == m_RsltHdlrTbl.end())
        {
            return nullptr;
        }

        return &iter->second;
    }

    //*****************************************************************************
    // 获取保存8080结果信息的对象
    // 参数[IN]: Type : 结果类型
    // 返回值: 保存信息的对象的指针
    //*****************************************************************************
    ResultItemHandler *Get8080ResultItemHandler(const std::string &Type)
    {
        auto iter = m_8080RsltHdlrTbl.find(Type);
        if (iter == m_8080RsltHdlrTbl.end())
        {
            return nullptr;
        }

        return &iter->second;
    }

    //*****************************************************************************
    // 获取保存蜂窝的结果信息的对象
    // 参数[IN]: Type : 结果类型
    // 返回值: 保存信息的对象的指针
    //*****************************************************************************
    ResultItemHandler *Get3GPPResultItemHandler(const std::string &Type)
    {
        auto iter = m_3GPPRsltHdlrTbl.find(Type);
        if (iter == m_3GPPRsltHdlrTbl.end())
        {
            return nullptr;
        }

        return &iter->second;
    }

    // delete copy and move constructors and assign operators
    AlgResult(AlgResult const&) = delete;             // Copy construct
    AlgResult(AlgResult&&) = delete;                  // Move construct
    AlgResult& operator=(AlgResult const&) = delete;  // Copy assign
    AlgResult& operator=(AlgResult &&) = delete;      // Move assign

    static void Get8080DataInfo(Merge80And80Rslt *pResult, void *pBuf);

    //*****************************************************************************
    // 获取spectrum margin data
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetSpectMarginData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // EVM无效时特定场景下的错误码 EVM_ERROR_CODE
    static void *GetEvmErrorCode(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    //*****************************************************************************
    // 获取EVM ALL值
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetEvmAll(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //*****************************************************************************
    // 获取EVM ALL percent
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetEvmAllPercent(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmPeak(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmPeakPercent(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetFreqErr(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetSignalLTFSNR(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetSignalPSDUSNR(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetSymbolClkErr(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetIQMatchAmpDb(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetIQMatchPhase(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetOfdmPhaseErr(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetOfdmDataRate(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetRampOnTime(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetRampDownTime(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetOfdmSymbolNum(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmDataDb(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmPilotDb(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetOfdmSpectFlatnessPassed(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static int GetChannelResponseSection(int Demode, FrmStruct &FrmInfo, int Stream, void *Margin, void *Value, int &SectionNum);

    static void *GetOfdmSpectFlatnessSectionMargin(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetOfdmSpectFlatnessSectionValue(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetSpectFlatnessData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void SetFittingFlatnessData(StOutInfo *pRxOut);

    static void *GetSpectFlatnessMaskUp(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetSpectFlatnessMaskDown(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetPsduLength(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetAPEPLength(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //信道相位响应,return point,complex vector
    static void *GetChannelPhaseResponse(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //信道幅度响应,return point,complex vector
    static void *GetChannelAmplitudeResponse(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //phase error vs. symbol,double vector,phase error data
    static void *GetPhaseErrorvsSymbol(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //amplitude vs. symbol,double vector,amplitude data
    static void *GetAmplitudevsSymbol(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *Get8080ChannelPhaseResponse(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *Get8080ChannelAmplitudeResponse(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *Get8080PhaseErrorvsSymbol(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *Get8080AmplitudevsSymbol(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    //*****************************************************************************
    // 获取星座图参考点
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetConstRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetCtrInfoConstRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //*****************************************************************************
    // 获取星座图数据
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetConstData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetConstPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetCtrInfoConstData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetCtrInfoConstPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //*****************************************************************************
    // 获取EVM VS symbol数据
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetEvmSym(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS symbol导频数据
    static void *GetEvmSymPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS symbol平均数据
    static void *GetEvmSymAvg(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS Carrier数据
    static void *GetEvmCarrier(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmCarrierPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //*****************************************************************************
    // 获取EVM VS Carrier平均数据
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetEvmCarrierAvg(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmCarrierPilotAvg(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetDataInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetDataInfoPlus(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetDataBurstFieldInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetBaseResult(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetSparkCtrlInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //*****************************************************************************
    // mimo时，获取多路数据结果的平均
    // 参数[IN]: pRxOut : 总的结果数据
    //           Stream : 流ID，为0表示获取总的分析结果
    // 参数[OUT]: pBuf : 结果数据保存地址
    //            Len  : 结果数据长度：
    // 返回值: 结果地址
    //*****************************************************************************
    static void *GetBaseResultComposite(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //Mimo下获取每流实际对应的signalID
    static void *GetStreamRealNstsIndex(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetCommResult(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetCommResult(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    // 检查ZigBee帧是否有效
    static bool CheckZigbee(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_ZIGBEE) && (pRxOut->SISOFrm->DbZigbee.evm.validflag == 1);
    }

    static bool CheckWiSUNOQPSK(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_LRWPAN_OQPSK);
    }

    static bool CheckWiSUNFSK(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_LRWPAN_FSK);
    }

    static bool IsGLE(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_GLE);
    }

    static bool IsBT(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT);
    }

    static bool IsBr(StOutInfo *pRxOut)
    {
        return pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT
               && pRxOut->SISOFrm->DbBT.DataRate == enBT_DATARATE_1M;
    }

    static bool IsEdr(StOutInfo *pRxOut)
    {
        return pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT
               && pRxOut->SISOFrm->DbBT.DataRate != enBT_DATARATE_1M
               && pRxOut->SISOFrm->DbBT.DataRate != enBT_DATARATE_BLE_1M
               && pRxOut->SISOFrm->DbBT.DataRate != enBT_DATARATE_BLE_2M
               && pRxOut->SISOFrm->DbBT.DataRate != enBT_DATARATE_BLE_125K
               && pRxOut->SISOFrm->DbBT.DataRate != enBT_DATARATE_BLE_500K;
    }

    static bool IsNotEdr(StOutInfo *pRxOut)
    {
        return pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT
               && (pRxOut->SISOFrm->DbBT.DataRate == enBT_DATARATE_1M
                   || pRxOut->SISOFrm->DbBT.DataRate == enBT_DATARATE_BLE_1M
                   || pRxOut->SISOFrm->DbBT.DataRate == enBT_DATARATE_BLE_2M
                   || pRxOut->SISOFrm->DbBT.DataRate == enBT_DATARATE_BLE_125K
                   || pRxOut->SISOFrm->DbBT.DataRate == enBT_DATARATE_BLE_500K);
    }

    static bool IsBle(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT) && (pRxOut->SISOFrm->DbBT.BLE_TestValid == 1);
    }

    static bool IsNotBle(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT) && (pRxOut->SISOFrm->DbBT.BLE_TestValid == 0);
    }

    static bool IsBLECoded(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT) && (pRxOut->SISOFrm->DbBT.BLE_TestValid == 1)&&
                ((enBT_DATARATE_BLE_125K == pRxOut->SISOFrm->DbBT.DataRate) || (enBT_DATARATE_BLE_500K == pRxOut->SISOFrm->DbBT.DataRate));
    }

    static bool IsNotBLECoded(StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_BT) && (pRxOut->SISOFrm->DbBT.BLE_TestValid == 1)&&
                (enBT_DATARATE_BLE_125K != pRxOut->SISOFrm->DbBT.DataRate) && (enBT_DATARATE_BLE_500K != pRxOut->SISOFrm->DbBT.DataRate);
    }
    
    static bool IsWiSUN (StOutInfo *pRxOut)
    {
        return (pRxOut->aStCh[0].demod_mode == WT_DEMOD_LRWPAN_OFDM || pRxOut->aStCh[0].demod_mode == WT_DEMOD_LRWPAN_FSK || pRxOut->aStCh[0].demod_mode == WT_DEMOD_LRWPAN_OQPSK);
    }

    static bool IsZwaveRate(double Datarate)
    {
        if(fabs(Datarate - 9.6) < 1e-6
                || fabs(Datarate - 40) < 1e-6
                || fabs(Datarate - 100) < 1e-6)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    static bool IsSpectralValid(StOutInfo *pRxOut)
    {
        //3GPP要用到类型转换来通过Check函数参数检查，不影响原来的Check函数使用。
        Alg_3GPP_VsaOutInfo *pResult = (Alg_3GPP_VsaOutInfo *)pRxOut;
        if(pResult->RFOut->Spectral.SpectFlg)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    static bool IsACLRValid(StOutInfo *pRxOut)
    {
        //3GPP要用到类型转换来通过Check函数参数检查，不影响原来的Check函数使用。
        Alg_3GPP_VsaOutInfo *pResult = (Alg_3GPP_VsaOutInfo *)pRxOut;
        if(pResult->RFOut->ACLR.AclrFlg)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    static bool IsACLREutraValid(StOutInfo *pRxOut)
    {
        //3GPP要用到类型转换来通过Check函数参数检查，不影响原来的Check函数使用。
        Alg_3GPP_VsaOutInfo *pResult = (Alg_3GPP_VsaOutInfo *)pRxOut;
        if(pResult->RFOut->ACLR.AclrFlg)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // 11ax sigb 获取星座图数据
    static void *GetSigbConstData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax sigb 获取星座图参考点
    static void *GetSigbConstRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax sigb 获取星座导频
    static void *GetSigbConstPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax sigb 获取EVM VS symbol数据
    static void *GetSigbEvmSym(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax sigb 获取EVM VS Carrier数据
    static void *GetSigbEvmCarrier(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax sigb evm all db
    static void *GetSigbEvmAll(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax evm_pilot_db
    static void *GetSigbEvmPilotDb(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax evm_data_db
    static void *GetSigbEvmDataDb(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax sigb evm all db composite
    static void *GetSigbEvmAllComposite(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax evm_pilot_db composite
    static void *GetSigbEvmPilotDbComposite(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax evm_data_db composite
    static void *GetSigbEvmDataDbComposite(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS symbol平均数据 db
    static void *GetEvmSymAvgDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS symbol数据 db
    static void *GetEvmSymDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS symbol导频数据 db
    static void *GetEvmSymPilotDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS Carrier数据 db
    static void *GetEvmCarrierDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmCarrierPilotDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM VS Carrier平均数据 db
    static void *GetEvmCarrierAvgDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetEvmCarrierPilotAvgDB(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取所有用户的星座图数据
    static void *Get11axAllUsersConstData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取指定用户的星座图数据
    static void *Get11axUserConstDatabyID(StOutInfo *pRxOut, int Stream, int UserID, void *pBuf, int &Len);

    // 11ax 获取所有用户的星座图参考点数据
    static void *Get11axAllUsersConstRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取指定用户的星座图参考点数据
    static void *Get11axUserConstRefbyID(StOutInfo *pRxOut, int Stream, int UserID, void *pBuf, int &Len);

    // 11ax 获取所有用户星座图的导频数据
    static void *Get11axAllUsersConstPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取指定用户的星座图的导频数据
    static void *Get11axUserConstPolitbyID(StOutInfo *pRxOut, int Stream, int UserID, void *pBuf, int &Len);

    // 11be 获取解析出来的PPDU format帧格式，区分是su还是mu，su：1；mu：2
    static void *Get11BePPDUFormat(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取解析出来的PPDU format帧格式，区分是su还是mu，su：1；mu：2
    static void *Get11axPPDUFormat(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取SigA中的BSS Color bit的结果
    static void *Get11axSigABSSColor(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取L-sig bit的结果
    static void *Get11axLSigBit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取SigA bit的结果
    static void *Get11axSigABit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取sigB bit1的结果
    static void *Get11axSigBBit1(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取sigB bit2的结果
    static void *Get11axSigBBit2(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11ax 获取Tb unused tone error的结果，返回结构stTbUnusedToneError的内容
    static void *GetTbUnusedToneError(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取Trigger frame的信息结果，返回结构TriggerFrameSetting的内容
    static void *GetTriggerFrameInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // mimo 下获取实际流数，相对vsg实际发送流数来讲
    static void *GetMimoVsgStreamCount(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取Psdu的扰码序列，eg：“1111000”
    static void *GetPsduScrambler(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取信号中dut mac信息
    static void *GetMacInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetServiceFieldInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *GetAx8080ServiceFieldInfo(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);   //仅在ax 8080时使用

    // 获取11be 每个用户信号中dut mac信息
    static void *Get11BeUserMacInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取11ax 每个用户信号中dut mac信息
    static void *Get11axUserMacInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取11be 每个用户的Psdu的扰码序列，eg：“1111000”，一次性返回所有用户的
    static void *Get11BeUserPsduScrambler(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取11ax 每个用户的Psdu的扰码序列，eg：“1111000”，一次性返回所有用户的
    static void *Get11axUserPsduScrambler(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取11ax mu-mimo 的ofdma 信息视图结果
    static void *Get11BeMuMimoOfdmaInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取11ax mu-mimo 的ofdma 信息视图结果
    static void *Get11axMuMimoOfdmaInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *Get11BeRUQMatInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *Get11axRUQMatInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    //获取psdu crc 结果
    static void *GetPsduCrcResult(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取wifi中的MCS模式
    static void *GetMCSType(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取EVM 余量 db值,单个double
    static void *GetFrmEvmMargin(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取LO 余量 db值,单个double
    static void *GetFrmLeagkageMargin(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取频偏余量ppm值,单个double
    static void *GetFrmFreqerrMargin(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 获取采样偏余量 ppm值,单个double
    static void *GetFrmClockErrMargin(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //LO参考量dB,单个double
    static void *GetLeagkageRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //频偏参考量ppm,单个double
    static void *GetRreqerrRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //采样参考量ppm,单个double
    static void *GetClockerrRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //全译码ok时，导出psdu 二进制bit流
    static void *GetPsduBit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 8080获取频谱margin
    static void *Get8080SpectMargin(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080BaseResult(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080SigbEvmPilotDb(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080SigbEvmAll(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080SigbEvmDataDb(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080BaseResultComposite(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080SigbEvmPilotDbComposite(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080SigbEvmAllComposite(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080SigbEvmDataDbComposite(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *GetAx8080UserMacInfo(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *GetAx8080PsduScrambler(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    //mu-mimo
    static void *GetMuMimoAllUsersConstData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *GetMuMimoAllUsersConstRef(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *GetMuMimoAllUsersConstPilot(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *GetAx8080MuMimoOfdmaInfo(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *GetAx8080MuMimoAllUsersConstData(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *GetAx8080MuMimoAllUsersConstRef(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *GetAx8080MuMimoAllUsersConstPilot(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *Get8080AmpduInfo(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    static void *Get8080MimoPowerTable(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);
    //8080 全译码ok时，导出psdu 二进制bit流
    static void *GetAx8080PsduBit(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *GetAcMuMimoDataInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetAmpduInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //根据clockrate重新计算datainfo中返回的bw
    static void CalculateBW(int Demode, int ClockRate, void *pBuf);

    static void *GetMimoPowerTable(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11be 获取L-sig bit的结果
    static void *Get11beLSigBit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11be 获取U-sig bit的结果
    static void *Get11beUSigBit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11be 获取sig ctx1 bit的结果
    static void *Get11beSigCtx1Bit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    // 11be 获取sig ctx2 bit的结果
    static void *Get11beSigCtx2Bit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //获取psdu bit中mpdu 分解出来的详细信息
    static void *GetPsduDecomposeInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *GetAx8080PsduDecomposeInfo(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);   //仅在ax 8080时使用

    //获取psdu bit中A-MPDU的eofpadding内容
    static void *GetPsduAmpduEofPaddingInfo(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *GetAx8080AmpduEofPaddingInfo(Merge80And80Rslt *pResult, int Stream, void *pBuf, int &Len);

    static void *GetPsduLdpcErrorCorrectFlag(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //11ba result
    //获取WUR-Data部分的频谱数据
    static void *Get11baWURDataSpectrum(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11baWURDataSpectrumMask(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    static void *Get11baSymbolPowerTatioTestResult(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11baCorrelationTestResult(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11baLSigBit(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11baWUR_PHY_Info(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);

    //11az result 区分user repeatcnt，返回内容格式都为：
    //int usernum + int user1RepeatCnt + int user1Repeat1Pass + int user1Repeat2Pass + ... + int user2RepeatCnt + ...
    static void *Get11azUsersSpectFlatnessPassed(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    //int usernum + int user1RepeatCnt + int user1Repeat1DataLen + n bytes user1Repeat1Data + int user1Repeat2DataLen + ... + int user2RepeatCnt + ...
    static void *Get11azUsersSpectFlatnessData(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11azUsersSpectFlatnessMaskUp(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11azUsersSpectFlatnessMaskDown(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11azUsersSpectFlatnessSectionValue(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11azUsersSpectFlatnessSectionMargin(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11azUsersChannelPhaseResponse(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    static void *Get11azUsersChannelAmplitudeResponse(StOutInfo *pRxOut, int Stream, void *pBuf, int &Len);
    //end 11az result
private:
    AlgResult(void);

    ~AlgResult(void)
    {
    }

    static void Set11nSigData(DataInfo11n *DataInfo, const st11N_HTSig *HTSig, const st11AG_LSig *LSig);

    static void Set11acSigData(DataInfo11ac *DataInfo, const st11ac_VHTSig_A *VHTSigA,
                               const st11ac_VHTSig_B *VHTSigB, const st11AG_LSig *LSig);

    static void Set11acSigDataPlus(DataInfo11acPlus *DataInfo, const st11ac_VHTSig_A *VHTSigA,
                               const st11ac_VHTSig_B *VHTSigB, const st11AG_LSig *LSig);
    template <typename T>
    static void Set11BeSigData(DataInfo11Be *DataInfo, T *FrmDb);

    template<typename T>
    static void Set11axSigData(DataInfo11ax *DataInfo, T *FrmDb);

    template<typename T>
    static void Get11axUserMacInfoData(T *FrmDb, stMacInfo *MacInfo, int &DataLen);

    template <typename T>
    static void Get11BeUserMacInfoData(T *FrmDb, stMacInfo *MacInfo, int &DataLen);

    static void SetBTInfo(DataInfoBT *DataInfo, const stBT_DB *pBTFrm);

    static void SetBTInfoPlus(DataInfoBTPlus *DataInfoPlus, const stBT_DB *pBTFrm);

    // 获取EVM结构体
    static FrmStruct GetFrmStruct(StOutInfo *pRxOut, int Stream);

    static void GetResponseData(stChannelResponse *Response, int ChannelNum, void *pBuf, int &Len);

    static void GetFlatnessResult(stChannelResponse *Response, int ChannelNum, stChannelResponse *MaskUp,
                                  stChannelResponse *MaskDown, void *pBuf, int &Len);

    static void GetResponseSectionData(int Demode, stChannelResponse *ChannelResponse,
                                       stChannelResponse *MaskUp, stChannelResponse *MaskDown,
                                       void *Margin, void *Value, int &SectionNum);

    static void GetAllUserConstData(int UserNum, stEVM_OFMDA *UserEvm, void *pBuf, int &Len);

    static void GetAllUserConstRef(int UserNum, stEVM_OFMDA *UserEvm, void *pBuf, int &Len);

    static void GetAllUserConstPilot(int UserNum, stEVM_OFMDA *UserEvm, void *pBuf, int &Len);

    static void Set11baData(DataInfo11ba *DataInfo, stCrossChba *FrmDb);

    static void Set11azData(DataInfo11az *DataInfo, stCrossChaz *FrmDb);

    static void Set11ahData(DataInfo11ah *DataInfo, stCrossChah *FrmDb);

private:
    std::unordered_map<std::string, ResultItemHandler> m_RsltHdlrTbl;      // 算法结果标识和数据信息关联表
    std::unordered_map<std::string, ResultItemHandler> m_8080RsltHdlrTbl;  // 8080算法结果标识和数据信息关联表
    std::unordered_map<std::string, ResultItemHandler> m_3GPPRsltHdlrTbl;  // 蜂窝结果获取
public:
    std::deque<std::vector<double>> m_BTRawBuf;        // BT Delta F1 存储原始数据
    std::deque<std::vector<double>> m_BTRawBufF2;        // BT Delta F2 存储原始数据
    std::deque<std::vector<double>> m_SLERawBufF2;        // BT Delta F2 存储原始数据
};

#endif
