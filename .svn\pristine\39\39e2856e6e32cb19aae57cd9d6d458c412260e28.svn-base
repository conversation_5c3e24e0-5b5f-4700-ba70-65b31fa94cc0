//*****************************************************************************
//  File: scpi_service.h
//  业务处理相关
//  Data: 2019.03.10
//*****************************************************************************
#ifndef __SCPI_SERVICE_H__
#define __SCPI_SERVICE_H__

#include <list>
#include <memory>
#include <queue>
#include <jsoncpp/json/json.h>
#include <string>
#include <unordered_map>
#include <functional>

#include "wtev++.h"
#include "socket.h"
#include "scpiapiwrapper/wrapper.h"
#include "wtlog.h"

struct VxiLinkInfo
{
    std::shared_ptr<wtev::io> link_io;
    std::shared_ptr<WRSocket> wr_socket;

    VxiLinkInfo(std::shared_ptr<wtev::io> link_io, std::shared_ptr<WRSocket> wr_socket)
        : link_io(link_io), wr_socket(wr_socket) {}
};

// VXI连接断开回调函数类型
using VxiLinkCloseCallback = std::function<void(int fd)>;

//业务处理类
class ScpiService
{
  public:
    ScpiService(const int &Fd, Json::Value &ConfJson, int Port);
    ScpiService(const int &Fd, Json::Value &ConfJson, int Port, bool is_vxi);
    ~ScpiService() { WTLog::Instance().WriteLog(LOG_DEBUG, "ScpiService destruct\n"); }

    //*****************************************************************************
    // 运行service
    // 参数: 无
    // 返回值: 无
    //*****************************************************************************
    void Run();

    //*****************************************************************************
    // 获取socket PeerPort
    // 参数 : 无
    // 返回值: PeerPort
    //*****************************************************************************
    unsigned short GetPeerPort(void) const { return m_WrSock.GetPeerPort(); }

    //*****************************************************************************
    // 获取Fd peer Ip
    // 参数 : 无
    // 返回值: Ip
    //*****************************************************************************
    const char *GetPeerIp(void) const { return m_WrSock.GetPeerIp(); }

    //停止服务
    void Stop();

    //获取SOCK描述符
    int GetFd() { return m_Fd; }

    // 添加Fd, 处理来自VXI客户端的SCPI命令
    void AddFd(int fd);

    // 添加本地Fd, 处理来自SCPI服务器的响应数据
    void AddLocalFd(int fd);

    //*****************************************************************************
    // 设置VXI连接断开回调函数
    // 参数[IN]: callback - 当VXI连接断开时的回调函数
    // 返回值: 无
    //*****************************************************************************
    void SetVxiLinkCloseCallback(const VxiLinkCloseCallback& callback);

private:
    //连接关闭处理
    void CloseLink();

    void CloseVxiLink(int Port);

    //内置api对仪器server连接的心跳检测
    void SrvTimerCb(wtev::timer &watcher, int revents);

    //连接消息处理接口
    void LinkHandler(wtev::io &watcher, int revents);

    // 接收vxi客户端的SCPI命令
    void VxiLinkHandler(wtev::io &watcher, int revents);

    // 发送scpi服务器的响应数据
    void ScpiReplyCallback(wtev::io &watcher, int revents);

    //扩展WRSocket接收数据的BUF空间
    void ExpandBuf();

    void ExpandClientBuf(std::shared_ptr<WRSocket> client_socket);

    //停止消息的回掉函数
    void StopCb(wtev::async &watcher, int revents);

  private:
    std::mutex m_Mutex; 
    wtev::dynamic_loop m_EvLoop; //ev loop
    wtev::async m_StopEv;         //停止service的异步事件
    int m_ThreadId;
    int m_Port;
    int m_Fd;                    //连接信息
    std::vector<int> m_fd_vec;     //连接信息
    WRSocket m_WrSock;           //当前连接要用的通讯socket
    wtev::io m_LinkIO;           //连接io watcher
    wtev::timer m_EvTimer;       //监控定时器，用于监控内部api对下位机Server的连接状态，断开时，能主动断开对外连接
    WTScpiApiWrapper Wrapper;
    
    bool m_is_vxi;
    std::queue<int> m_vxi_cmd_fd_queue; // vxi发送查询命令的fd队列
    std::unordered_map<int, std::shared_ptr<VxiLinkInfo>> m_vxi_linkio_map; // fd 和 linkio 的映射
    VxiLinkCloseCallback m_vxi_link_close_callback;  // VXI连接断开回调
};
#endif //__SCPI_SERVICE_H__
