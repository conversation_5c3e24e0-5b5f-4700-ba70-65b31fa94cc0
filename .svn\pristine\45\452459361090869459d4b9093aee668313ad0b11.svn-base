TOPDIR = $(shell pwd)
OUTDIR = $(TOPDIR)/bin
DATE = $(shell date +%F_%R:%S)
SVN = $(shell svnversion -c $(TOPDIR)/../ |sed 's/^.*://' |sed 's/[A-Z]*$$//')
ALG_FILES=$(shell find $(TOPDIR)/../extlib/lib/libAlg*.so | awk -F '/' '{print $$NF}' | sed 's/lib/-l/' | sed 's/.so//')

CC = gcc
CXX = g++
LD = ld
CFLAGS = -std=c++11 -MMD -MP -Wall -Werror -Wno-sign-compare -Wno-format-overflow -Wno-noexcept-type
LDFLAGS = -ljsoncpp -L$(TOPDIR)/../extlib/lib -lwt-calibration -lfftw3 -lgsl -lgslcblas -lwt-crypto -lft4222 -lgcov
DRV_CFLAGS =

LDFLAGS += -L$(TOPDIR)/../extlib/lib $(ALG_FILES) -lWT-Debug-Mem

ifeq ($(CXX), clang++)
CFLAGS += -Wno-unused-private-field
endif

ifeq ($(DEBUG), 1)
CFLAGS += -g --coverage -DDEBUG -DCOVERAGE
LDFLAGS += --coverage -rdynamic
DRV_CFLAGS = -DDEBUG=1
else ifeq ($(RELEASE), 1)
CFLAGS += -g -O2
DRV_CFLAGS = -DDEBUG=1
else
CFLAGS += -g -O2 -DDEBUG
DRV_CFLAGS = -DDEBUG=1
endif

ifeq ($(CRYPTOMEMORY), 1)
CFLAGS += -DCRYPTOMEMORY
endif

ifeq ($(DISABLE_SECURE), 1)
CFLAGS += -DSECURE_DISABLE=1
endif

ifeq ($(DEVTYPE), WT328CE)

else ifeq ($(SISO), 1)
	CFLAGS += -D_SISO_MODE
else ifeq ($(DEVTYPE), WT428)
	CFLAGS += -D_WT428
endif

ifeq ($(DEVTYPE), WT448)
CFLAGS += -DWT448_FW
VersionDef=WT448_FW_VERSION
else ifeq ($(DEVTYPE), WT428)
CFLAGS += -DWT428_FW
VersionDef=WT428_FW_VERSION
else ifeq ($(DEVTYPE), WT328CE)
CFLAGS += -DWT418_FW
VersionDef=WT418_FW_VERSION
endif

define get_fw_versionobj
	@echo $(shell grep -m 1 "$(VersionDef)" $(TOPDIR)/general/version.cpp | grep -oP '\d+\.\d+\.\d+\.\d+')
endef

Now := $(shell date +%N)
DATESRC = /tmp/build_date_$(Now).cpp
DATEOBJ = /tmp/build_date_$(Now).o
define generate_dateobj
	@echo "extern \"C\" const char *GetBuildDate() { return \"$(DATE), revision: $(SVN)\"; }" > $(DATESRC)
	@$(CXX) -fPIC -c $(DATESRC) -o $(DATEOBJ)
endef

SUBDIR = filesecure general manager link server scpi xdma driver 

define record_compile_type
	echo $(DEVTYPE) > $(OUTDIR)/last_compile_type.txt
endef

export CC CXX CFLAGS DRV_CFLAGS LDFLAGS OUTDIR TOPDIR DATEOBJ

ifeq ($(mod),)
all:
	$(call generate_dateobj)
	$(call get_fw_versionobj)
	@[ ! -e $(OUTDIR) ] &  mkdir -p $(OUTDIR)
	@echo "mod not specified, make all"
	for dir in $(SUBDIR); do \
		$(MAKE) -C $$dir all || exit 1;   \
	done
	$(call record_compile_type)
	rm -f $(DATESRC)
	rm -f $(DATEOBJ)

ifeq ($(PACKAGE), 1)
	echo "packaging~"
	bash ./pack.sh $(DEVTYPE) GENERATOR=$(GENERATOR) DISABLE_SECURE=$(DISABLE_SECURE)
	echo "end package~"
endif

ifeq ($(FACTORY), 1)
	echo "factorying~"
	bash ./factory.sh $(DEVTYPE) GENERATOR=$(GENERATOR) DISABLE_SECURE=$(DISABLE_SECURE)
	echo "end factory~"
endif

clean:
	-rm $(OUTDIR) -r -f
	for dir in $(SUBDIR); do  \
		$(MAKE) -C $$dir clean;  \
	done

else
all:
	$(call generate_dateobj)
	$(call get_fw_versionobj)
	@[ ! -e $(OUTDIR) ] &  mkdir -p $(OUTDIR)
	@echo "mod is $(mod), make $(mod)"
	$(MAKE) -C $(mod) all
	$(call record_compile_type)
	rm -f $(DATESRC)
	rm -f $(DATEOBJ)

clean:
	$(MAKE) -C $(mod) clean

endif

check:
	for dir in $(SUBDIR); do \
		$(MAKE) -C $$dir check || exit 1;   \
	done

.PHONY: all clean check
