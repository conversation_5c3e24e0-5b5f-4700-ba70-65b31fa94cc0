#ifndef SCPI_3GPP_ALZ_JSON_FORMAT_H_
#define SCPI_3GPP_ALZ_JSON_FORMAT_H_

// ENV_API定义值指示了使用哪套库实现json的格式化
// API格式化json, 上位机可能没有jsoncpp库, 所以使用parson库
// SCPI格式化json使用jsoncpp库
#define ENV_API 0

#ifdef ENV_API
#if (ENV_API == 1)

// parson库

#include "includeall.h"
#include "parson.h"

typedef JSON_Value *WT_JSON_TYPE;
typedef JSON_Value *WT_JSON_DEFINE_OBJ_TYPE;

#else

// jsoncpp库

#include "basehead.h"
#include <jsoncpp/json/json.h>

typedef Json::Value &WT_JSON_TYPE;
typedef Json::Value WT_JSON_DEFINE_OBJ_TYPE;

#endif

#else

#error "Please define macro: ENV_API"

#endif

void Format_3GPP_ALZ_Json(WT_JSON_TYPE object, AlzParam3GPP &Param);

#endif