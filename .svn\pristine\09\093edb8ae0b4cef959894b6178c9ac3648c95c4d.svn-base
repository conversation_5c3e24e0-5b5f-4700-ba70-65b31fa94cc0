//*****************************************************************************
//  File: sysmonitor.h
//  仪器系统资源监控
//  Data: 2020.05.13
//*****************************************************************************
#ifndef __WT_SYS_MON_H__
#define __WT_SYS_MON_H__

#include "wtev++.h"
#include "wtev++.h"

#include "basefun.h"

using namespace std;
//系统资源监测类
class SysMonitor
{
public:
    //*****************************************************************************
    // 获取SysMonitor对象
    // 参数: 无
    // 返回值: DevMgr对象
    //*****************************************************************************
    static SysMonitor &Instance();

    //*****************************************************************************
    // 设置硬件操作完成后的信号回掉函数
    // 参数[IN]: loop : 信号所属的ev loop
    // 返回值: 无
    //*****************************************************************************
    void SetHwCb(const wtev::loop_ref &loop);  

    //*****************************************************************************
    // 是否禁止继续保存信号文件
    // 返回值: 是或否
    //*****************************************************************************
    bool IsDisableSaveSignal();  
private:
    SysMonitor();
    ~SysMonitor();

    //*****************************************************************************
    // 监控仪器系统信息
    // 参数[IN]：watcher : 定时器监控器
    //          revents : 时间掩码
    // 返回值：无
    //*****************************************************************************
    void SysMonitorCb(wtev::timer &watcher, int revents);

private:
    struct WaveSize                 //直接将三个硬盘容量监控的设定为一个结构体，方便进行共享内存
    {
        std::size_t m_TmpWaveSize;
        std::size_t m_BinWaveSize;
        int m_DiskUsed;
    };
    // std::size_t m_TmpWaveSize;
    // std::size_t m_BinWaveSize;
    // int m_DiskUsed;

    WaveSize *m_WaveSize;                            //保存硬盘容量监视的共享内存地址
    wtev::timer m_MonitorEv;                         //LED定时器
};

#endif
