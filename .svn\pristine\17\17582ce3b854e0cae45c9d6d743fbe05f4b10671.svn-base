#pragma once
#ifndef _CRYPTOLOGY_WT2XX_H__
#define _CRYPTOLOGY_WT2XX_H__
#include "Cryptology.h"
#include <string>

using namespace std;
#define TX_BUFF_LEN 2048U

#define TYPE_CMD 0x434D44 // 命令类型为下发命令
#define TYPE_ACK 0x41434B // 命令类型为命令响应

// 协议命令
enum CMD_CODE_E
{
    CMD_UNVALID = -1,                // 非法命令
    CMD_RESET_CFG = 0x0,             // 恢复出厂设置
    CMD_SET_DEVIP = 0x1,             // 设备IP配置
    CMD_SETUP_SUBDEV = 0x2,          // 子仪器划分配置
    CMD_GET_SUBDEV_INFO = 0x3,       // 子仪器配置查询
    CMD_GET_DEV_INFO = 0x4,          // 设备基本信息查询
    CMD_GET_HW_INFO = 0x5,           // 硬件详细信息查询
    CMD_GET_LIC = 0x6,               // License查询
    CMD_SET_SUB_ETH = 0x7,           // 子网口配置
    CMD_RELOAD_CAL_FILE = 0x8,       // 重新加载校准文件数据
    CMD_SET_EXT_GAIN = 0x9,          // 端口外部增益配置
    CMD_SET_CAL_FILE = 0xA,          // 校准文件写入
    CMD_GET_CAL_FILE = 0xB,          // 校准文件读取
    CMD_ADD_MIMO_DEV = 0xC,          // 增加MIMO仪器
    CMD_DEL_MIMO_DEV = 0xD,          // 删除MIMO仪器
    CMD_SET_MIMO_PARAM = 0xE,        // 多机MIMO参数配置
    CMD_GET_GUI = 0xF,               // GUI文件获取
    CMD_SET_MON_OBJ = 0x10,          // 监视机监视对象配置
    CMD_SET_MON_PARAM = 0x11,        // 监视机需要的数据项配置
    CMD_TX_MON_DATA = 0x12,          // 监视结果发送
    CMD_GET_GUI_VERSION = 0x13,      // 获取指定协议类型的GUI文件的版本号
    CMD_GET_SUB_ETH = 0x14,          // 获取子网口配置信息
    CMD_GET_DEVICE_CUR_TIME = 0x15,  // 获取仪器当前系统时间
    CMD_GET_ALL_GUI_VERSIONS = 0x16, // 获取全部GUI文件的对应版本号信息
    CMD_GET_CUR_DEV_RES_CONF = 0x17, // 获取当前server/子仪器的资源划分情况信息

    CMD_SIG_FILE_TX = 0x18,       // 信号文件下发
    CMD_SIG_FILE_EXIST = 0x19,    // 信号文件是否存在
    CMD_DEL_SIG_FILE = 0x1A,      // 删除信号文件
    CMD_GET_SIG_FILE_LIST = 0x1B, // 获取指定目录信号文件列表
    CMD_GET_SIG_FILE = 0x1C,      // 获取信号文件

    CMD_SET_CAL_PARAM = 0x1D,         // 设置校准参数
    CMD_GET_MONITOR_LIST = 0x1E,      // 获取当前仪器的监听机的列表信息
    CMD_SET_TEMP_CAL = 0x1F,          // 设置温度补偿开关
    CMD_SET_FLATNESS_CAL = 0x20,      // 设置平坦度补偿开关
    CMD_SET_PATH_LOSS_FILE = 0x21,    // 写入线衰文件
    CMD_GET_PATH_LOSS_FILE = 0x22,    // 读取线衰文件
    CMD_GET_CAL_PARAM = 0x24,         // 获取校准参数
    CMD_SET_CUSTOMIZE_FILE = 0x25,    // 下发文件
    CMD_GET_CUSTOMIZE_FILE = 0x26,    // 获取文件内容
    CMD_SET_SHELL_CMD = 0x27,         // 执行shell命令
    CMD_GET_DEV_VER_INFO = 0x28,      // 设备相关版本信息查询，算法、校准等
    CMD_GET_MON_RAW_DATA = 0x29,      // monitor模式下获取VSA采集的原始IQ数据
    CMD_GET_MON_VSA_DATA = 0x30,      // monitor模式下获取VSA采集的补偿后的IQ数据
    CMD_GET_MON_VSA_CAL_PARAM = 0x31, // monitor模式下获取VSA采集的校准数据
    CMD_GET_DISK_USE_INFO = 0x32,     // 查询仪器磁盘使用情况
    CMD_SET_CLEAN_WAVE_DIR = 0x33,    // 清空仪器信号文件磁盘
    CMD_SET_SUB_NET_AUTO_NEG = 0x34,  // 设置仪器子网口网速自动协商开关
    CMD_GET_SUB_ETH_2 = 0x35,         // 双pcserver获取子网口配置信息(不实现，保留ID号)
    CMD_GET_APP_LIST = 0x36,          // 获取第三方应用列表
    CMD_SET_APP_STATUS = 0x37,        // 启用禁用第三方应用列表
    CMD_SET_APP_DELETE = 0x38,        // 删除第三方应用列表
    CMD_GET_SUB_NET_AUTO_NEG = 0x39,  // 获取仪器子网口网速自动协商开关状态
    CMD_GET_SUB_ETH_LINK = 0x3a,            //获取子网口link信息
    CMD_GET_IP_ADDRESS_TYPE = 0x3b,          //获取主网口ip地址类型

    CMD_START_IN_CAL = 0x40,         // 立即开始自校准，如果正在自校准，直接返回OK
    CMD_STOP_IN_CAL = 0x41,          // 立即停止当前的自校准
    CMD_QUERY_IN_CAL_PROCESS = 0x42, // 查询自校准的进度
    CMD_SET_IN_CAL_CONFIG = 0x43,    // 设置仪器不要启动内环自动校准
    CMD_DEV_RUN_MODE = 0X45,         // 仪器运行模式
    CMD_SET_DEV_LO_MODE = 0X46,      // 本振模式
    CMD_GET_DEV_LO_MODE = 0X47,      // 本振模式
    CMD_SET_DEV_ANALOG_IQ_MODE,      // 设置模拟IQ信号内/外链路切换开关
    CMD_GET_DEV_ANALOG_IQ_MODE,      // 设置模拟IQ信号内/外链路切换开关

    CMD_SET_VSA_PARAM = 0X101,            // VSA参数配置
    CMD_SET_VSA_ALZ_PARAM = 0X102,        // VSA分析参数配置
    CMD_VSA_AUTO_RANGE = 0X103,           // VSA autorange
    CMD_START_VSA = 0X104,                // VSA启动
    CMD_PAUSE_VSA = 0X105,                // VSA暂停抓取数据
    CMD_STOP_VSA = 0X106,                 // VSA停止
    CMD_GET_VSA_STATUS = 0X107,           // VSA状态查询
    CMD_EXT_VSA_FILE = 0X108,             // 外部VSA信号文件下发
    CMD_VSA_ALZ = 0X10A,                  // 分析VSA信号
    CMD_GET_VSA_DATA = 0X10B,             // 获取VSA结果数据
    CMD_GET_SPEC_VSA_DATA = 0X10C,        // 获取平均中特定某一次的指定项结果数据
    CMD_GET_VSA_PARAM = 0X10D,            // VSA配置查询
    CMD_RECORD_VSA = 0X10E,               // VSA操作和数据录制
    CMD_GET_VSA_RECORD = 0X10F,           // VSA录制数据获取
    CMD_GET_VSA_RAW_DATA = 0X110,         // 获取VSA采集到的数据及补偿参数
    CMD_SET_AVG_PARAM = 0X111,            // 设置VSA平均参数
    CMD_GET_VSA_RESULT = 0x113,           // 获取VSA结果数据
    CMD_SAVE_SIGNAL = 0X117,              // 保存采集到的信号
    CMD_GET_REF_RANGE = 0X118,            // 获取参考电平范围
    CMD_GET_VSA_CAL_PARAM = 0X120,        // 获取校准补偿参数,配合保存vsa信号文件获取iq数据时使用
    CMD_GET_CLR_AVG_DATA = 0x121,         // 清除平均数据，meter vsa stop时使用
    CMD_GET_AVG_DATA_COMPOSITE = 0x122,   // MIMO平均时获取composite结果
    CMD_GET_VSA_GAIN_PARAM = 0x123,       // 获取校准增益数据
    CMD_SET_FREAM_FILTER = 0x124,         // 设置结果过滤条件
    CMD_SET_IS_204M_SPECT_ON = 0x125,     // 设置是否打开频谱显示正负240M的功能
    CMD_SET_PAC_PARAMETER = 0x126,        // 配置Pac 参数
    CMD_GET_PAC_MODE_POWER_PHASE = 0x127, // Pac mode下获取有
    CMD_SET_VSA_TRIG_PARAM = 0x128,       // 配置VSA TRIG参数
    CMD_SET_EXTEND_EVM_STATUS = 0x129,        //设置VSA 额外EVM分析参数

    CMD_CALC_IQ_IMB = 0x130,                    // 计算IQ不平衡，分别返回ImbAmp,ImbPhase,timeskew
    CMD_SET_STATIC_IQ_IMB = 0x131,              // 设置固定的IQ不平衡参数，分别为ImbAmp,ImbPhase,timeskew
    CMD_CLR_STATIC_IQ_IMB = 0x132,              // 清除固定的IQ不平衡参数
    CMD_BEAMFORMING_CLR_ALZ_DUTTX = 0x133,      // Beamforming Calibration DUT TX BCM
    CMD_BEAMFORMING_CLR_ALZ_DUTRX = 0x134,      // Beamforming Calibration DUT RX BCM
    CMD_BEAMFORMING_CLR_RST = 0x135,            // Beamforming获取相位结果
    CMD_BEAMFORMING_CLR_VERIFICATION = 0x136,   // Beamfoming 获取差异值
    CMD_BEAMFORMING_CAL_PROFILE = 0x137,        // Beamfoming MTK 分析
    CMD_PER_ANALYZE = 0x138,                    // PER 分析
    CMD_SET_EXTRAL_ALZ_PARAM = 0x139,           // 配置11ax triggger Base的分析参数，配合设置分析参数一起使用
    CMD_SET_ALZ_GROUP_BY_RESULT_STRING = 0x140, // 通过需要获取的来判断分析组类型
    CMD_SET_VSA_FLATNESS_CAL_ENABLE = 0x141,    // 配置vsa 平坦度补偿使能开关
    CMD_GET_VSA_FLATNESS_CAL_ENABLE = 0x142,    // vsa 平坦度补偿使能状态查询
    CMD_SET_VSA_IQIMB_CAL_ENABLE = 0x143,       // 配置vsa IQ不平衡补偿使能开关
    CMD_GET_VSA_IQIMB_CAL_ENABLE = 0x144,       // vsa IQ不平衡补偿使能状态查询
    CMD_GET_VSA_SPECTRUM_POINT_POWER = 0x145,   // 获取频谱指定频点的功率
    CMD_BEAMFORMING_CAL_BCM_AMP_ANGLE = 0x146,  //Beamfoming BCM获取相位幅度等结果内容
    CMD_SET_VSA_ITERATIVE_EVM_STATUS = 0x147,   //设置VSA迭代优化开关
    CMD_SET_VSA_SNC_EVM_STATUS = 0x148,         //设置VSA SNC_EVM噪声补偿开关
    CMD_SET_VSA_CC_EVM_STATUS = 0x149,          //设置VSA CC_EVM开关

    CMD_SET_VSG_PARAM = 0X201,               // VSG参数配置
    CMD_TX_VSG_FILE = 0X202,                 // VSG信号文件下发
    CMD_START_VSG = 0X203,                   // VSG启动
    CMD_PAUSE_VSG = 0X204,                   // VSG暂停
    CMD_STOP_VSG = 0X205,                    // VSG停止
    CMD_GET_VSG_STATUS = 0X206,              // VSG状态查询
    CMD_GET_VSG_PARAM = 0X207,               // VSG配置查询
    CMD_GEN_VSG_FILE = 0X20C,                // VSG信号生成
    CMD_RECORD_VSG = 0X20D,                  // VSG操作和数据录制
    CMD_GET_VSG_RECORD = 0X20E,              // VSG录制数据获取
    CMD_SET_PN_PARAM = 0X20F,                // 多PN配置
    CMD_SET_PN_DATA = 0X210,                 // 多PN数据下发
    CMD_GET_PN = 0X211,                      // 多PN配置查询
    CMD_GET_GEN_PARAM = 0X212,               // 获取信号生成默认参数
    CMD_GET_POWER_RANGE = 0X213,             // 获取发送功率范围
    CMD_GET_CUR_AVG_CNT = 0X214,             // 获取当前已平均的次数
    CMD_GET_VSG_GAIN_PARAM = 0x215,          // 获取校准增益数据
    CMD_SET_FPGA_IFG_PARAM = 0x216,          // 设置FPGA IFG的使能开关
    CMD_GET_FPGA_IFG_PARAM = 0x217,          // 获取FPGA IFG的使能开关
    CMD_GEN_VSG_FILE_V2 = 0X218,             // VSG信号生成
    CMD_GEN_VSG_FILE_CW = 0X219,             // CW VSG信号生成
    CMD_GEN_VSG_FILE_BLUETOOTH = 0X220,      // Bluetooth VSG信号生成
    CMD_GET_GEN_FINAL_PARAM = 0x221,         // 获取生成信号时，生成完才能确定的参数
    CMD_GEN_VSG_FILE_WIFI = 0x222,           // Vsg Wifi 信号生成，第三版生成信号函数
    CMD_SET_VSG_FLATNESS_CAL_ENABLE = 0x223, // 配置vsg 平坦度补偿使能开关
    CMD_SET_VSG_FEM_MODE = 0x224,            // 配置vsg FEM功能使能开关
    CMD_GET_VSG_SEND_CNT = 0X226,            // vsg已发送次数
    CMD_GET_VSG_GEN_WIFI_PARAM = 0x227,      // wave generator 获取配置参数
    CMD_GET_VSG_GEN_RETURN_DATA = 0x228,     // 获取生成ofmda信号后，算法计算出来的ru carrier相关信息
    CMD_SET_OFDMA_RU_CARRIER_INFO = 0x229,   // 加载文件前，下发配置到算法in，用于做ofdma功率补偿
    CMD_GET_VSG_FLATNESS_CAL_ENABLE = 0x230, // vsg 平坦度补偿使能状态查询
    CMD_SET_VSG_IQIMB_CAL_ENABLE = 0x231,    // 配置vsg IQ不平衡补偿使能开关
    CMD_GET_VSG_IQIMB_CAL_ENABLE = 0x232,    // vsg IQ不平衡补偿使能状态查询
    CMD_SET_VSG_STATIC_IQ_IMB_CAL = 0x233,   // 设置固定的IQ不平衡参数
    CMD_CLR_VSG_STATIC_IQ_IMB_CAL = 0x234,   // 清除固定的IQ不平衡参数
    CMD_GEN_VSG_FILE_SLE = 0x235,            // SLE VSG信号生成
    CMD_GEN_VSG_GEN_SLE_SYNSQR = 0x236,      // 获取生成SLE信号后 生成的同步序列
    CMD_GEN_VSG_FILE_WI_SUN = 0x237,              // WiSun  VSG信号文件生成
    CMD_GEN_VSG_FILE_SLB = 0x237,                 // SLB    VSG信号文件生成

    CMD_GEN_VSG_FILE_3GPP =0x240,            // 3GPP协议 VSG信号生成

    CMD_SENT_METER_SETTING = 0X250,    // 下发meter主机界面配置，主要是为发送给监视机使用
    CMD_SENT_SOCK_INFO = 0X251,        // 下发meter主机界面配置，主要是为发送给监视机使用
    CMD_REQUEST_CALC_PAC_LIST = 0x260, // PAC 下发请求计算的数据

    CMD_SET_VSG_BROADCAST_ENABLE = 0x299,       // 广播模式
    CMD_GET_VSG_BROADCAST_ENABLE = 0x29A,       // 广播模式
    CMD_SET_VSG_BROADCAST_DEBUG_ENABLE = 0X29B, // 广播debug模式
    CMD_GET_VSG_BROADCAST_RUN_STATUS = 0X29C, // 获取当前广播VSG运行状态

    CMD_GET_TEMP = 0X301,           // 设备温度查询
    CMD_GET_FAN_SPEED = 0X302,      // 风扇转速查询
    CMD_SET_FAN_SPEED = 0X303,      // 风扇转速配置
    CMD_SET_COMPONENT_DATA = 0X304, // 器件参数配置
    CMD_GET_COMPONENT_DATA = 0X305, // 器件参数查询
    CMD_GET_TEMP_HISTORY = 0X307,   // 查询设备历史温度信息
    CMD_GET_VOLTAGE = 0X308,        // 获取设备电压信息
    CMD_SUB_CMD = 0X309,            // CMD

    CMD_GET_LOG = 0X401,            // 日志查询
    CMD_SELF_DIAG = 0X402,          // 启动自诊断
    CMD_GET_DIAG_DATA = 0X403,      // 获取自诊断详细数据
    CMD_GET_HARD_ERR_INFO = 0X404,  // 获取硬件错误信息
    CMD_TEST_GET_LOG_INFO = 0X405,  // 获取仪器log配置信息
    CMD_TEST_SET_LOG_INFO = 0X406,  // 设置仪器log配置信息
    CMD_START_FAST_ATT_CAL = 0X407, // 启动快速校准ATT
    CMD_STOP_FAST_ATT_CAL = 0X408,  // 停止快速校准ATT
    CMD_NOISE_CAL_START = 0X409,           // NOISE_CAL
    CMD_NOISE_CAL_STOP = 0X40A,            // NOISE_CAL
    CMD_NOISE_CAL_STATUS = 0X40B,          // NOISE_CAL
    CMD_NOISE_CAL_VAILD = 0X40C,           // NOISE_CAL

    CMD_UPDATA_FIRM = 0X501,            // 固件升级
    CMD_ROLLBACK_FIRM = 0X502,          // 固件回退
    CMD_UPDATE_LIC = 0X503,             // License升级
    CMD_RESTART_DEV = 0X504,            // 重启设备
    CMD_LIC_UPDATA_PACK = 0X505,        // Licenses升级包下发
    CMD_LIC_UPDATA = 0X506,             // License包升级
    CMD_SHUT_DOWN_DEV = 0X507,          // 关机功能
    CMD_DELETE_ALL_LIC_FILES = 0X508,   // 删除仪器的所有license
    CMD_DELETE_SUB_NET_SETTING = 0X509, // 删除子网口配置

    CMD_START_TB = 0X601,          // TB-TF测试，仪器属于AP
    CMD_STOP_TB = 0X602,           // TB-TF测试，仪器属于AP
    CMD_GET_TB_STATUS = 0X603,     // TB-TF测试，仪器属于AP
    CMD_VSA_AUTO_RANGE_TB = 0X604, // TB-TF测试，仪器属于AP

    CMD_START_TBT_STA = 0x605,  // TB-TF测试，仪器属于STA
    CMD_STOP_TBT_STA = 0x606,   // TB-TF测试，仪器属于STA
    CMD_GET_TBT_STATUS = 0X607, // TB-TF测试，仪器属于STA

    CMD_DIQ_PARAM = 0X608,    // 数字IQ 目标MAC地址
    CMD_DIQ_DUT_MODE = 0X609, // 数字IQ 治具模式

    CMD_SET_LIST_ENABLE = 0x701,                     //listmod使能
    CMD_SET_LIST_DISABLE = 0x702,                    //listmod去使能
    CMD_SET_LIST_SEG_VSA_ALZ_PARAM = 0x703,          //listmod seg分析参数设置
    CMD_SET_LIST_SEG_VSA_CAP_PARAM = 0x704,          //listmod seg抓取参数设置
    CMD_SET_LIST_SEG_VSA_TIME_PARAM = 0x705,         //listmod seg时间参数设置
    CMD_SET_LIST_SEG_VSA_START_SEQ = 0x706,          //listmod tx seq启动
    CMD_SET_LIST_SEG_VSG_START_SEQ = 0x707,          //listmod rx seq启动
    CMD_SET_LIST_SEG_VSG_PARAM = 0x708,              //listmod seg vsg参数设置
    CMD_SET_LIST_SEG_VSG_WAVE_PARAM = 0x709,         //listmod seg vsg波形参数设置
    CMD_SET_LIST_SEG_VSAVSG_START_SEQ = 0x70a,       //listmod txrx seq启动
    CMD_SET_LIST_SEG_VSA_STOP_SEQ = 0x70b,           //listmod tx seq停止
    CMD_SET_LIST_SEG_VSG_STOP_SEQ = 0x70c,           //listmod rx seq停止
    CMD_SET_LIST_SEG_VSAVSG_STOP_SEQ = 0x70d,        //listmod txrx seq停止
    CMD_GET_LIST_SEQ_VSAVSG_STATE = 0x70e,           //获取listmod txrx seq状态
    CMD_SET_LIST_SEG_VSA_CAP_STATE = 0x70f,          //获取listmod tx seq抓取状态
    CMD_SET_LIST_SEG_VSA_ANNALY_STATE = 0x710,       //获取listmod tx seq分析状态
    CMD_GET_LIST_SEQ_VSG_TRANS_STATE = 0x711,        //获取listmod rx seq发送状态
    CMD_SET_LIST_SEG_VSA_POWER_RESULT = 0x712,       //获取listmod tx seq功率结果
    CMD_SET_LIST_SEG_VSA_CLEAR = 0x713,              //listmod配置清空
    CMD_SET_LIST_SEG_VSA_LTE_TX_SEG_STAT = 0x714,    //listmod lte tx 各seg状态
    CMD_SET_LIST_SEG_VSG_SYNC_PARAM = 0x715,         //listmod seg vsg同步参数设置
    CMD_SET_LIST_SEG_VSA_TRIG_PARAM = 0x716,         //listmod seg trig参数设置
    CMD_SET_LIST_SEG_VSA_TRIG_COMMON_PARAM = 0x717,  //listmod seg common trig参数设置
    CMD_SET_LIST_SEG_ALL_TIME_PARAM = 0x718,         //listmod seq所有seg时间参数设置
    CMD_SET_LIST_SEG_ALLVSA_CAP_PARAM = 0x719,        //listmod seq所有seg抓取参数设置
    CMD_SET_LIST_SEG_VSA_ALLTRIG_PARAM = 0x71a,      //listmod 所有seg trig参数设置
    CMD_SET_LIST_SEG_VSA_ALLTRIG_COMMON_PARAM = 0x71b, //listmod 所有seg common trig参数设置
    CMD_SET_LIST_SEG_VSA_ALLALZ_COMMON_PARAM = 0x71c,  //listmod 所有seg common分析参数设置
    CMD_SET_LIST_SEG_VSA_ALLALZ_PRO_PARAM = 0x71d,     //listmod 所有seg 具体业务分析参数设置
    CMD_SET_LIST_SEG_ALLVSG_PARAM = 0x71e,             //listmod 所有seg vsg参数设置
    CMD_SET_LIST_SEG_VSG_ALLSYNC_PARAM = 0x71f,        //listmod 所有seg vsg同步参数设置
    CMD_SET_LIST_SEG_VSG_ALLWAVE_PARAM = 0x720,        //listmod 所有seg vsg波形参数设置
    CMD_GET_LIST_ALL_VSA_SEG_DATA = 0x721,             //listmod 一次性获取所有seg的相关结果

    // 全双工
    CMD_DUPLEX_SET_STATE = 0X755,               // 设置全双工状态
    CMD_DUPLEX_GET_STATE = 0X756,               // 获取全双工状态
    CMD_SET_DUPLEX_NOISE_FLAG  = 0X757,         // 设置全双噪底补偿
    CMD_GET_DUPLEX_NOISE_FLAG  = 0X758,         // 获取全双噪底补偿

    CMD_GET_MODU_VSA_DATA = 0X800, // 获取统计平均Modulation 的平均值

    CMD_CHECK_CONNECT_STATUS = 0X1005 // API查询连接状态,不ACK
};

enum CMD_STATUS_E
{
    CMD_STATUS_OK,    // 查找到完整命令
    CMD_STATUS_UNCMP, // 已查找到命令，但命令数据不完整; 或者数据太少无法判断是否是命令
    CMD_STATUS_NO_CMD // 没有查找到命令
};

enum NET_PRO_HEAD_REPLY_RESULT
{
    NET_REPLY_OK = 0x0000,
    NET_FUN_MISMATCH = 0x0001,
    NET_RESULT_FAIL = 0x0002,
    NET_LENGTH_TOO_SHORT = 0x0004,
    NET_NO_REPLY_DATA = 0x0008,
    NET_ID_TOO_LARGE = 0x0010,
    NET_ID_TOO_SMALL = 0x020,
    NET_RESULT_IS_FAIL = 0x040
};

enum WT_VIRTUAL_IP_CFG_TYPE
{
    IP_INVALID = 0,
    IP_VIP0 = 1,
    IP_VIP1 = 2,
    IP_VIP3 = 3,
    IP_VIP4 = 4,
    IP_VIP5 = 5,
    IP_VIP6 = 6,
    IP_VIP7 = 7,
    IP_VIP8 = 8,
    IP_DUTIP = 9,
    IP_TFTP_SVR_IP = 10,
    IP_TFTP_DUT_IP = 11,
    IP_TFTP_PC_IP = 12

};
enum RESTORE_OPTION
{
    RESTORE_LAST_VERSION,
    RESTORE_FACTORY_VERSION
};

enum IP_PORT_ENUM
{
    PORT_HOST = 8600, /* 主端口 */
};
enum VSA_AVERAGE_TYPE
{

    CAPTURE_AVERAGE, // 多次抓取取平均
    SEGMENT_AVERAGE  // 多帧取平均
};

enum VSA_AVERAGE_METHOD
{
    ARITHMETIC_AVERAGE, // 算术平均
    MOVING_AVERAGE      // 滑动平均
};

enum MIMO_TYPE
{
    TRUE_MIMO,
    SWITCH_MIMO
};

typedef struct
{
    int ipType;
    char ipAddr[16];
} VirtualIpCfg;

// 请求头结构体
struct ReqHeader
{
    int Type;               // 命令类型 CMD或者ACK
    unsigned int SerialNum; // 命令序列号
    int Code;               // 功能码
};

// 命令包括命令头和命令内容，从数据长度字段之后的部分为命令内容，之前的为命令头
// 协议命令结构，不包含命令数据
struct CmdHeader : ReqHeader
{
    int Length; // 命令内容长度

    CmdHeader() {}
    CmdHeader(const ReqHeader *Cmd, int Len)
        : ReqHeader(*Cmd), Length(Len)
    {
        Type = TYPE_CMD;
    }
};

// 命令响应结构体
struct AckHeader : CmdHeader
{
    int Result; // 结果

    AckHeader() {}
    AckHeader(const ReqHeader *Cmd, int Len, int Result)
        : CmdHeader(Cmd, Len), Result(Result)
    {
        Type = TYPE_ACK;
    }
};

struct stProReqWT4xx
{
    CmdHeader cmdHead;
    char *data;
};

struct stProAckWT4xx
{
    AckHeader ackHead;
    char *data;
};

// 子网口信息结构体
struct SystemIPInfo
{
    char SubIP[MAX_SUB_NET_COUNT][16];
    char DutIP[MAX_SUB_NET_COUNT][16];
    char PCServerIP[16];
    char TftpServerIP[16];
    char TftpClientIP[16];
    char IPno8[16];
};

// VSA采样参数
struct VsaCapParam
{
    double Freq;         // 信号中心频率, 单位HZ
    double Freq2;        // 信号中心频率2，Type为80+80 AC时有效
    double FreqOffset;   // 频偏
    int RFPort;          // RF端口号，取值见WT_PORT_ENUM
    int Type;            // 测试类型，取值见WT_TEST_TYPE
    int MasterMode;      // 80+80时mater选择，0表示第一个模块为Master, 1表示第二个模块为master
    int SignalId;        // 信号流ID
    int VsaMask;         // 使用指定的VSA单元，用掩码表示，0表示自动分配
    int TrigType;        // Trig类型，取值见WT_TRIG_TYPE
    int AllocTimeout;    // 申请资源超时时间
    int Is160M;          // 是否是160M信号   0表示不是160M非连接信号  1表示160M非连续信号
    double ExtPathLoss;  // 外部衰减
    double Ampl;         // 信号参考电平，单位dBm
    double ExtPathLoss2; // 外部衰减2
    double SamplingTime; // 采样时长, 单位秒
    double SamplingFreq; // 采用频率, 单位HZ
    double TrigPreTime;  // Trig到数据后指定时间长度的数据不计算在总的时长中, 最终这部分长度数据被丢弃, 单位秒
    double TrigTimeout;  // Trig超时时间，单位秒
    double TrigLevel;    // Trig电平，单位dBm
    double MaxIFG;       // 最大帧间隔，单位秒
    int RFPort2;         // RF端口号，取值见WT_PORT_ENUM 80+80 双端口模式专用
    int DulPortMode;     // 80+80是否采用双端口模式。
    double Ampl2;        // 信号参考电平，单位dBm，       80+80是针对freq2的
    double TrigLevel2;   // Trig电平，单位dBm             80+80 双端口模式专用
    int Demod;           // VSA DEMOD
    int Resv;            // 保留
    double DCOffsetI;    // I路 DC offset: -1.0 to +1.0      模拟IQ模式专用
    double DCOffsetQ;    // Q路 DC offset: -1.0 to +1.0      模拟IQ模式专用
    double Reserved[3];  // 保留

    VsaCapParam()
    {
        Freq = 2412 * MHz_API;
        Freq2 = 0;
        FreqOffset = 0;
        RFPort = WT_PORT_RF1 - WT_PORT_OFF;
        Type = 1;
        MasterMode = 0;
        SignalId = 0;
        VsaMask = 0;
        TrigType = WT_TRIG_TYPE_FREE_RUN_API;
        AllocTimeout = 8;
        Is160M = 0;
        ExtPathLoss = 0;
        Ampl = 30;
        ExtPathLoss2 = 0;
        SamplingTime = 0.002;
        SamplingFreq = MAX_SMAPLE_RATE_API;
        TrigPreTime = 10 * Us;
        TrigTimeout = 0.5;
        TrigLevel = -31;
        MaxIFG = 0.2;

        RFPort2 = 0;
        DulPortMode = 0;

        memset(Reserved, 0, sizeof(Reserved));
    }
    // 获取测试类型
    int GetTestType(void) const { return Type; }

    // 获取使用的硬件模块掩码
    int GetModMask(void) const { return VsaMask; }

    // 获取配置参数对应的采集到的信号长度
    int GetSigLen(void) const { return (int)(long)(SamplingTime * SamplingFreq * sizeof(short) * 2); }
};

// VSA分析参数
struct VsaAlzParam
{
    int Demode;          // 信号格式
    int ChainNum;        // 天线数量，CMIMO时需要填写，其他模式忽略此参数
    int FrameIndex;      // 分析哪一帧数据
    int AutoDetect;      // WIFI 带宽自动检测
    int BTDataRate;      // 信号速率，主要用于BT分析
    int BTPktType;       // BT包类型，取值见enBT_PACKETTYPE
    int PhsCorrMode;     // 相位修正模式，取值见WT_PH_CORR_ENUM
    int ChEstimate;      // 通道估计方法，取值见WT_CH_EST_ENUM
    int SynTimeCorr;     // Symbol Timing Correction. 取值见WT_SYM_TIM_ENUM
    int FreqSyncMode;    // 频率同步模式，取值见WT_FREQ_SYNC_ENU
    int EqTaps;          // 802.11b Number of equalizer tap取值见WT_EQ_ENUM
    int DCRemoval;       // CCK分析EVM前移除DC分量，取值见WT_DC_REMOVAL_ENU
    int AmplTrack;       // 幅度跟踪，取值见WT_AMPL_TRACK_ENUM
    int Method11b;       // 802.11b EVM分析方式，取值见WT_11B_METHOD_ENUM
    int IQSwap;          // IQ交换，取值见WT_IQ_SWAP_ENUM
    int IQReversion;     // IQ极性反转
    int OfdmDemodOn;     // OFDM Demodulation, 1:on, 0:off
    int OfdmMode;        // OFDM模式，取值见WT_OFDM_MODE_ENUM
    int OfdmEvmMethod;   // 取值见WT_OFDM_EVM_METHODS
    int ManualPktStart;  // 指定数据包在多少个采样点后为起始
    double FreqOffset;   // 输入信号频率偏移
    int FilterPktByTime; // 短包过滤，低于此时间长度的包会被过滤
    int FilterPktByType; // 包类型过滤
    double Reserved[10];

    // FFT分析参数
    double RBW;          // resolution bandwidth
    double VideoBw;      // Video bandwidth
    int VideoAvMethod;   // 详见WT_VIDEO_AV_METHOD_ENUM
    int WindowType;      // 详见WT_WINDOW_TYPE_ENU
    double SamplingFreq; // 采样频率
    double NFFT;         // FFT点数
    double NFFTOver;     // Minimum oversampling factor
    double FreqStart;    // 起始频率
    double FreqStop;     // 结束频率
    double FreqDelta;    // 频率间隔
    double Reserved1[10];

    VsaAlzParam()
    {
        Demode = WT_DEMOD_11AC_20M;
        ChainNum = 0;
        FrameIndex = 0;
        AutoDetect = WT_BW_AUTO_DETECT;
        BTDataRate = WT_BT_DATARATE_Auto;
        BTPktType = WT_BT_PACKETTYPE_NULL;
        PhsCorrMode = WT_PH_CORR_SYM_BY_SYM;
        ChEstimate = WT_CH_EST_RAW;
        SynTimeCorr = WT_SYM_TIM_ON;
        FreqSyncMode = WT_FREQ_SYNC_AUTO;
        EqTaps = WT_EQ_OFF;
        DCRemoval = WT_DC_REMOVAL_OFF;
        AmplTrack = WT_AMPL_TRACK_OFF;
        Method11b = WT_11B_STANDARD_TX_ACC;
        IQSwap = WT_IQ_SWAP_DISABLED;
        IQReversion = WT_IQ_IQReversion_DISABLED;

        OfdmDemodOn = 0;
        OfdmMode = 0;
        OfdmEvmMethod = 0;
        ManualPktStart = 0;
        FreqOffset = 0;
        FilterPktByTime = 0;
        FilterPktByType = 0;
        memset(Reserved, 0, sizeof(Reserved));

        // FFT分析参数
        RBW = 0;
        VideoBw = 0;
        VideoAvMethod = 0;
        WindowType = 0;
        SamplingFreq = 0;
        NFFT = 0;
        NFFTOver = 0;
        FreqStart = 0;
        FreqStop = 0;
        FreqDelta = 0;
        memset(Reserved1, 0, sizeof(Reserved1));
    }
};

// 仪器信息
typedef struct
{
    char IP[16];      // IP地址
    char SubMask[16]; // 子网掩码
    char GateWay[16]; // 网关

    char SN[80];                        // 仪器SN码
    char Name[40];                      // 别名  "ITEST  Tester"
    char Mac[18];                       // MAC 地址 "DC-A4-B5-C7-E1-F8"
    char FwVersion[40];                 // 固件版本
    char ALGVersion[40];                // 算法版本
    int BusiBoardCount;                 // 业务板数量
    BackPlaneInfo_API BPInfo;               // 背板信息
    BusinessBoardInfo_API BusiBoardInfo[8]; // 业务板信息
    char ProduceDate[20];               // 生产日期 "2012-05-04  18:36:56"
} CrypTesterInfo;

// VSG配置参数
struct VsgParam
{
    double Freq;         // 信号中心频率, 单位HZ
    double Freq2;        // 信号中心频率2，Type为80+80 AC时有效
    double FreqOffset;   // 频偏
    int RFPort;          // RF端口号，取值见WT_PORT_ENUM
    int Type;            // 测试类型，取值见WT_TEST_TYPE
    int MasterMode;      // 80+80时mater选择，0表示第一个模块为Master, 1表示第二个模块为master
    int VsgMask;         // 使用指定的VSG单元，用掩码表示，0表示自动分配
    double Power;        // 发送功率 dBm
    double ExtPathLoss;  // 外部衰减
    int SignalId;        // 信号流ID
    int AllocTimeout;    // 申请资源超时时间，单位秒
    double SamplingRate; // 采样率
    double ExtPathLoss2; // 外部衰减2
    int Is160M;          // 是否是160M信号  0表示不是160M非连接信号  1表示160M非连续信号
    int RFPort2;         // RF端口号，取值见WT_PORT_ENUM 80+80 双端口模式专用
    int DulPortMode;     // 80+80是否采用双端口模式。
    int LoopCnt;                                    //循环次数
    double Power2;       // 发送功率 dBm   80+80 双端口模式port2专用
    double TBTStaDelay;  // TB-TF 仪器做STA时的delay时间，单位S
    double DCOffsetI;    // I路 DC offset: -1.0 to +1.0      模拟IQ模式专用
    double DCOffsetQ;    // Q路 DC offset: -1.0 to +1.0      模拟IQ模式专用
    double CommModeVolt; // 共模电压 0.0V-3.3V                 模拟IQ模式专用
    double IFG;                                     //循环间隔，单位采样点数
    double RandomWaveGapMax;                        //随机间隔的范围上限，单位：second(秒)
    double RandomWaveGapMin;                        //随机间隔的范围下限，单位：second(秒)
    int IFGRamdomMode;                              //IFG随机模式

    VsgParam()
    {
        Freq = 2412 * MHz_API; // 信号中心频率, 单位HZ
        Freq2 = 0;         // 信号中心频率2，Type为80+80 AC时有效
        FreqOffset = 0;
        RFPort = WT_PORT_RF1 - WT_PORT_OFF; // RF端口号，取值见WT_PORT_ENUM
        VsgMask = 0;                        // 使用指定的VSG单元，用掩码表示，0表示自动分配
        Power = -10;                        // 发送功率 dBm
        Type = 0;                           // 测试类型，取值见WT_TEST_TYPE
        MasterMode = 0;                     // 信号流ID
        ExtPathLoss = 0;                    // 衰减
        ExtPathLoss2 = 0;                   // 衰减2
        Is160M = 0;                         // 是否是160M信号
        // Gain = 0;                                 //数据增益 db
        SignalId = 0;
        AllocTimeout = 8;
        SamplingRate = MAX_SMAPLE_RATE_API; // 采样率

        RFPort2 = 0;
        DulPortMode = 0;
        IFG = 0;
        RandomWaveGapMax = 0;
        RandomWaveGapMin = 0;
        IFGRamdomMode = 0;
        TBTStaDelay = 0.0;
    }
    // 获取测试类型
    int GetTestType(void) const { return Type; }

    // 获取使用的硬件模块掩码
    int GetModMask(void) const { return VsgMask; }
};

enum
{
    FIXED_IFG_MODE,
    RANDOM_IFG_MODE,
};

// 外部下发的PN配置
struct ExtPnItem
{
    unsigned int LoopCnt;         //循环次数//仅限数字IQ和交互
    unsigned int StartDelay;      // Tx起始延时，单位采样点数
    unsigned int IFG;             //循环间隔，单位采样点数//仅限数字IQ和交互
    double RandomWaveGapMax;      //随机间隔的范围上限，单位：second(秒)//仅限数字IQ和交互
    double RandomWaveGapMin;      //随机间隔的范围下限，单位：second(秒)//仅限数字IQ和交互
    int WaveSource;               // 信号数据来源
    int WavePreset;               // 信号文件编号
    int IFGRamdomMode;            // IFG随机模式
    int WaveType;                 // 信号类型
    char WaveName[MAX_NAME_SIZE]; // 信号文件名
    int Extend;                   //listmod arb信号发送扩展的点数，表示0.xxx个arb信号,listmod模式下，持续发送时，该参数不生效
};

struct ImbalanceParam
{
    double ImbAmp;
    double ImbPhase;
    double TimeSkew;
};

struct ResponseBlockInfo
{
    int FreqCount; // 频点个数
    int Reserve;
    double *Response;
#if (defined _WIN32) && !(defined _WIN64)
    int pad; // 补齐
#endif
};

struct SpectrumOffset
{
    int Valid; // 是否有效
    int PointCnt;
    double *First;
#if (defined _WIN32) && !(defined _WIN64)
    int pad1; // 补齐
#endif
    double *Last;
#if (defined _WIN32) && !(defined _WIN64)
    int pad2; // 补齐
#endif
};

struct SigDataHeader_2409
{
    void *Data; // 信号数据
#if (defined _WIN32) && !(defined _WIN64)
    int pad2; // 补齐
#endif
    int SampleCount;  // 数据个数
    int DataType;     // 数据类型，64位浮点型、16位整形
    int ModType;      // 分析类型，校准使用
    int ExtAttEnable; // 是否启用外部衰减，VSA
    int clockRate;
    int pad3;
    double ExtAtt;                // 外部衰减，与IQV兼容，VSA
    double SamplingRate;          // 采样率
    double TriggerLevel;          // 触发电平，VSA
    double RefLevel;              // 参考电平，VSA
    double FreqOffset;            // 频率偏移
    double RFGain;                // 数据保存时的射频功率, VSA
    double CenterFreq;            // 中心频率, VSA
    double IQGainImb;             // IQ幅度不平衡
    double IQPhaseImb;            // IQ相位不平衡
    double DCOffsetI;             // DC offset I
    double DCOffsetQ;             // DC offset Q
    double TimeSkew;              // timeSkew
    double PnIfg;                             //生成的PN中间Gap 
    double PnHead;                          //生成的PN前后Gap
    double PnTail;                          //生成的PN前后Gap
    int Repeat;                             //生成的PNPN的重复次数
    int reserved;                            //
    double reserved2[26];                   //
    ResponseBlockInfo NSResponse;                   //NOISE响应补偿
    ResponseBlockInfo RfResponse; // RF响应补偿
    ResponseBlockInfo BBResponse; // BB响应补偿
    SpectrumOffset SpectOffset;   // 频谱边带补偿数据, VSA
    Iq_Image_Parm IQImage;
};
enum FILE_TYPE
{
    FILE_TYPE_NORMAL = 0, // 普通信号文件
    FILE_TYPE_8080,       // 8080信号文件
    FILE_TYPE_160         // 8080组成的160M信号文件
};

// 信号文件头
struct SigFileHeader
{
    int Flag;     // 文件标识
    int ChainNum; // 文件数据通道数量
    int FileType; // 信号文件类型，见enum FILE_TYPE
    int MimoType; // MIMO类型，SwitchedMIMO or TrueMIMO
};

#define CRYPTOGRAM_LEN 4

class CryptologyWT4xx
    : public Cryptology
{
public:
    CryptologyWT4xx();
    virtual ~CryptologyWT4xx();

public:
    virtual unsigned int GetProAckHeadSize();
    virtual unsigned int GetProAckStartFlagSize();
    virtual unsigned int GetProAckSize();
    virtual unsigned int GetProAckNoStartFlagSize();

    virtual unsigned int GetProCmdHeadSize();
    virtual unsigned int GetProCmdStartFlagSize();
    virtual unsigned int GetProCmdSize();
    virtual unsigned int GetProCmdNoStartFlagSize();
    virtual int GetFunFromCmdHead(char *head_buff);

    virtual unsigned int GetDataLength(char *srcProHead);
    virtual unsigned int GetCmdDataLength(char *srcProHead);

    virtual const char *GetProAckStartFlag(char *package, unsigned int packageSize);
    virtual const char *GetProCmdStartFlag(char *package, unsigned int packageSize);
    virtual int GetComuPort();
    virtual const char *GetPlainText(int deviceType, int connType);
    virtual const char *GetAuthText();
    virtual int VerifyProAckHeadExceptStartFlag(char *ackHead, char *cmdHead, int *errCodeFromFir);
    virtual int WrapUpPackage(int mode, int fun, ExchangeBuff *sendBuff, unsigned int sendCnt, char *package);
    // 加密
    int Encrypt(int *text, int textLen, int *random, int randomLen);
    // 解密
    int Decrypt(int *text, int textLen, int *random, int randomLen);

protected:
    int SetCryParameters(int *cryptogram, int cryptogramLen, unsigned int delta);
    int tea_encrypt(int *v, unsigned int n, int *k);
    int tea_decrypt(int *v, int n, int *k);
    int mx(unsigned int z, unsigned int y, unsigned int sum, unsigned int e, int *k, int p);
    virtual unsigned int GetSerialNum();

private:
    unsigned int m_serialNum;
    unsigned int m_cDelta;
    int m_cCryptogram[CRYPTOGRAM_LEN];
};
#endif
