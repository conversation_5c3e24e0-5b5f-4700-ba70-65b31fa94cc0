/**
 * @file scpi_3gpp_alz_default_param.cpp
 * @brief scpi蜂窝默认vsa分析设置
 * @version 0.1
 * @date 2024-09-21
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#include "scpi_3gpp_alz_default_param.h"
#include "scpi_3gpp_common.h"

using namespace cellular::method;

void cellular::alz::ResetGsmAlzParam(AlzParam3GPP &Param)
{
    memset(&Param.GSM, 0, sizeof(Param.GSM));
    Param.Standard = ALG_3GPP_STD_GSM;
    Param.GSM.NumbOfSlot = 1;

    for (int i = 0; i < 11; i++)
    {
        Param.GSM.SpectMod.OffsetState[i] = 1;
    }
    Param.GSM.SpectMod.FreqOffset[0] = 0.1;
    Param.GSM.SpectMod.FreqOffset[1] = 0.2;
    Param.GSM.SpectMod.FreqOffset[2] = 0.25;
    Param.GSM.SpectMod.FreqOffset[3] = 0.4;
    Param.GSM.SpectMod.FreqOffset[4] = 0.6;
    Param.GSM.SpectMod.FreqOffset[5] = 0.8;
    Param.GSM.SpectMod.FreqOffset[6] = 1.0;
    Param.GSM.SpectMod.FreqOffset[7] = 1.2;
    Param.GSM.SpectMod.FreqOffset[8] = 1.4;
    Param.GSM.SpectMod.FreqOffset[9] = 1.6;
    Param.GSM.SpectMod.FreqOffset[10] = 1.8;
    Param.GSM.SpectMod.FreqOffset[11] = 1.9;
    Param.GSM.SpectMod.FreqOffset[12] = 1.9;
    Param.GSM.SpectMod.FreqOffset[13] = 1.9;
    Param.GSM.SpectMod.FreqOffset[14] = 1.9;
    Param.GSM.SpectMod.FreqOffset[15] = 1.9;
    Param.GSM.SpectMod.FreqOffset[16] = 1.9;
    Param.GSM.SpectMod.FreqOffset[17] = 1.9;
    Param.GSM.SpectMod.FreqOffset[18] = 1.9;
    Param.GSM.SpectMod.FreqOffset[19] = 1.9;

    for (int i = 0; i < 4; i++)
    {
        Param.GSM.SpectSwt.OffsetState[i] = 1;
    }
    Param.GSM.SpectSwt.FreqOffset[0] = 0.4;
    Param.GSM.SpectSwt.FreqOffset[1] = 0.6;
    Param.GSM.SpectSwt.FreqOffset[2] = 1.2;
    Param.GSM.SpectSwt.FreqOffset[3] = 1.8;
    Param.GSM.SpectSwt.FreqOffset[4] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[5] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[6] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[7] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[8] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[9] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[10] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[11] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[12] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[13] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[14] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[15] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[16] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[17] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[18] = 1.9;
    Param.GSM.SpectSwt.FreqOffset[19] = 1.9;

    // LimitInfo.ModLimit
    Param.GSM.LimitInfo.ModLimit[0].EvmRms.LimitValue = 10;
    Param.GSM.LimitInfo.ModLimit[0].EvmPeak.LimitValue = 35;
    Param.GSM.LimitInfo.ModLimit[0].Evm95Percent.Limit = 20;
    Param.GSM.LimitInfo.ModLimit[0].MErrRms.LimitValue = 10;
    Param.GSM.LimitInfo.ModLimit[0].MErrPeak.LimitValue = 35;
    Param.GSM.LimitInfo.ModLimit[0].MErr95Percent.Limit = 20;
    Param.GSM.LimitInfo.ModLimit[0].PhErrRms.LimitValue = 5;
    Param.GSM.LimitInfo.ModLimit[0].PhErrRms.Current = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErrRms.Average = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErrRms.Max = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErrPeak.LimitValue = 20;
    Param.GSM.LimitInfo.ModLimit[0].PhErrPeak.Current = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErrPeak.Average = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErrPeak.Max = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErr95Percent.Limit = 10;
    Param.GSM.LimitInfo.ModLimit[0].IQOffset.LimitValue = -30;
    Param.GSM.LimitInfo.ModLimit[0].IQImbalance.LimitValue = -30;
    Param.GSM.LimitInfo.ModLimit[0].FreError.LimitValue = 90;
    Param.GSM.LimitInfo.ModLimit[0].FreError.Current = 1;
    Param.GSM.LimitInfo.ModLimit[0].FreError.Average = 1;
    Param.GSM.LimitInfo.ModLimit[0].FreError.Max = 1;
    Param.GSM.LimitInfo.ModLimit[0].PhErrRms.LimitValue = 10;

    Param.GSM.LimitInfo.ModLimit[1].EvmRms.LimitValue = 9;
    Param.GSM.LimitInfo.ModLimit[1].EvmRms.Current = 1;
    Param.GSM.LimitInfo.ModLimit[1].EvmRms.Average = 1;
    Param.GSM.LimitInfo.ModLimit[1].EvmRms.Max = 1;
    Param.GSM.LimitInfo.ModLimit[1].EvmPeak.LimitValue = 30;
    Param.GSM.LimitInfo.ModLimit[1].EvmPeak.Current = 1;
    Param.GSM.LimitInfo.ModLimit[1].EvmPeak.Average = 1;
    Param.GSM.LimitInfo.ModLimit[1].EvmPeak.Max = 1;
    Param.GSM.LimitInfo.ModLimit[1].Evm95Percent.Limit = 15;
    Param.GSM.LimitInfo.ModLimit[1].Evm95Percent.State = 1;
    Param.GSM.LimitInfo.ModLimit[1].MErrRms.LimitValue = 9;
    Param.GSM.LimitInfo.ModLimit[1].MErrRms.Current = 1;
    Param.GSM.LimitInfo.ModLimit[1].MErrRms.Average = 1;
    Param.GSM.LimitInfo.ModLimit[1].MErrRms.Max = 1;
    Param.GSM.LimitInfo.ModLimit[1].MErrPeak.LimitValue = 30;
    Param.GSM.LimitInfo.ModLimit[1].MErr95Percent.Limit = 15;
    Param.GSM.LimitInfo.ModLimit[1].PhErrRms.LimitValue = 5;
    Param.GSM.LimitInfo.ModLimit[1].PhErrPeak.LimitValue = 20;
    Param.GSM.LimitInfo.ModLimit[1].PhErr95Percent.Limit = 10;
    Param.GSM.LimitInfo.ModLimit[1].IQOffset.LimitValue = -30;
    Param.GSM.LimitInfo.ModLimit[1].IQOffset.Average = 1;
    Param.GSM.LimitInfo.ModLimit[1].IQImbalance.LimitValue = -30;
    Param.GSM.LimitInfo.ModLimit[1].FreError.LimitValue = 90;
    Param.GSM.LimitInfo.ModLimit[1].FreError.Current = 1;
    Param.GSM.LimitInfo.ModLimit[1].FreError.Average = 1;
    Param.GSM.LimitInfo.ModLimit[1].FreError.Max = 1;
    Param.GSM.LimitInfo.ModLimit[1].PhErrRms.LimitValue = 10;

    Param.GSM.LimitInfo.ModLimit[2].EvmRms.LimitValue = 7;
    Param.GSM.LimitInfo.ModLimit[2].EvmRms.Current = 1;
    Param.GSM.LimitInfo.ModLimit[2].EvmRms.Average = 1;
    Param.GSM.LimitInfo.ModLimit[2].EvmRms.Max = 1;
    Param.GSM.LimitInfo.ModLimit[2].EvmPeak.LimitValue = 30;
    Param.GSM.LimitInfo.ModLimit[2].EvmPeak.Current = 1;
    Param.GSM.LimitInfo.ModLimit[2].EvmPeak.Average = 1;
    Param.GSM.LimitInfo.ModLimit[2].EvmPeak.Max = 1;
    Param.GSM.LimitInfo.ModLimit[2].Evm95Percent.Limit = 15;
    Param.GSM.LimitInfo.ModLimit[2].Evm95Percent.State = 1;
    Param.GSM.LimitInfo.ModLimit[2].MErrRms.LimitValue = 7;
    Param.GSM.LimitInfo.ModLimit[2].MErrRms.Current = 1;
    Param.GSM.LimitInfo.ModLimit[2].MErrRms.Average = 1;
    Param.GSM.LimitInfo.ModLimit[2].MErrRms.Max = 1;
    Param.GSM.LimitInfo.ModLimit[2].MErrPeak.LimitValue = 30;
    Param.GSM.LimitInfo.ModLimit[2].MErr95Percent.Limit = 15;
    Param.GSM.LimitInfo.ModLimit[2].PhErrRms.LimitValue = 5;
    Param.GSM.LimitInfo.ModLimit[2].PhErrPeak.LimitValue = 20;
    Param.GSM.LimitInfo.ModLimit[2].PhErr95Percent.Limit = 10;
    Param.GSM.LimitInfo.ModLimit[2].IQOffset.LimitValue = -30;
    Param.GSM.LimitInfo.ModLimit[2].IQOffset.Average = 1;
    Param.GSM.LimitInfo.ModLimit[2].IQImbalance.LimitValue = -30;
    Param.GSM.LimitInfo.ModLimit[2].FreError.LimitValue = 90;
    Param.GSM.LimitInfo.ModLimit[2].FreError.Current = 1;
    Param.GSM.LimitInfo.ModLimit[2].FreError.Average = 1;
    Param.GSM.LimitInfo.ModLimit[2].FreError.Max = 1;
    Param.GSM.LimitInfo.ModLimit[2].PhErrRms.LimitValue = 10;

    // LimitInfo.PVTLimit
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[0].State = 1;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[0].FromPCL = 5;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[0].ToPCL = 5;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[0].Lower = -2;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[0].Upper = 2;

    Param.GSM.LimitInfo.PVTLimit.AvgLimit[1].State = 1;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[1].FromPCL = 0;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[1].ToPCL = 2;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[1].Lower = -2;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[1].Upper = 2;

    Param.GSM.LimitInfo.PVTLimit.AvgLimit[2].State = 1;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[2].FromPCL = 3;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[2].ToPCL = 15;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[2].Lower = -3;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[2].Upper = 3;

    Param.GSM.LimitInfo.PVTLimit.AvgLimit[3].State = 1;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[3].FromPCL = 16;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[3].ToPCL = 31;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[3].Lower = -5;
    Param.GSM.LimitInfo.PVTLimit.AvgLimit[3].Upper = 5;

    Param.GSM.LimitInfo.PVTLimit.GuardPeriod.State = 1;
    Param.GSM.LimitInfo.PVTLimit.GuardPeriod.Limit = 3;

    for (int i = 0; i < arraySize(Param.GSM.LimitInfo.PVTLimit.UpperTemLimit); i++)
    {
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.Time = -38;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.LevelRel = -59;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.LevelAbs.Limit = -36;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.Time = -28;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.LevelRel = -3;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.LevelAbs.Limit = -48;
        
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.Time = -28;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.LevelRel = -30;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.LevelAbs.Limit = -17;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.Time = -18;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.LevelRel = -30;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.LevelAbs.Limit = -17;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Start.Time = -18;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Start.LevelRel = -6;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Stop.Time = -10;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Stop.LevelRel = -6;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].StaticLimt.Start.Time = -10;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].StaticLimt.Start.LevelRel = 4;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].StaticLimt.Stop.LevelRel = 4;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].StaticLimt.Start.LevelRel = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].StaticLimt.Stop.Time = 542.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].StaticLimt.Stop.LevelRel = 1;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Start.Time = 542.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Start.LevelRel = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Stop.Time = 552.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Stop.LevelRel = 1;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Start.Time = 552.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Start.LevelRel = -6;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Stop.Time = 560.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Stop.LevelRel = -6;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.Time = 560.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.LevelRel = -30;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.LevelAbs.Limit = -17;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.Time = 570.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.LevelRel = -30;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.LevelAbs.Limit = -17;

        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.Time = 570.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.LevelRel = -59;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.LevelAbs.Limit = -54;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.Time = 580.8;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.LevelRel = -59;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.LevelAbs.State = 1;
        Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.LevelAbs.Limit = -54;

        Param.GSM.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].State = 1;
        Param.GSM.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].StaticLimt.Start.LevelRel = -1;
        Param.GSM.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].StaticLimt.Stop.Time = 542.8;
        Param.GSM.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].StaticLimt.Stop.LevelRel = -1;
    }

    Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[2].RiseEdgeLimit[3].StaticLimt.Start.LevelRel = 6.5;
    Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[2].RiseEdgeLimit[3].StaticLimt.Stop.LevelRel = 6.5;
    Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[2].FallEdgeLimit[0].StaticLimt.Start.LevelRel = 6.5;
    Param.GSM.LimitInfo.PVTLimit.UpperTemLimit[2].FallEdgeLimit[0].StaticLimt.Stop.LevelRel = 6.5;

    // LimitInfo.SpecModLimit
    Param.GSM.LimitInfo.SpecModLimit[0].RefPwrLimit.LowPwr = 33;
    Param.GSM.LimitInfo.SpecModLimit[0].RefPwrLimit.HighPwr = 39;
    for (int i = 0; i < 10; i++)
    {
        Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[i].State = 1;
    }
    for (int i = 0; i < arraySize(Param.GSM.LimitInfo.SpecModLimit); i++)
    {
        for (int j = 4; j < arraySize(Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit); j++)
        {
            Param.GSM.LimitInfo.SpecModLimit[i].FreOffsetLimit[j].LowPwrRel = -60;
            Param.GSM.LimitInfo.SpecModLimit[i].FreOffsetLimit[j].HighPwrRel = -66;
            Param.GSM.LimitInfo.SpecModLimit[i].FreOffsetLimit[j].AbsPwr = -51;
        }
    }
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[0].LowPwrRel = 0.5;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[1].LowPwrRel = -30;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[2].LowPwrRel = -33;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[3].LowPwrRel = -54;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[0].HighPwrRel = 0.5;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[1].HighPwrRel = -30;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[2].HighPwrRel = -33;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[3].HighPwrRel = -54;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[0].AbsPwr = -36;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[1].AbsPwr = -36;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[2].AbsPwr = -36;
    Param.GSM.LimitInfo.SpecModLimit[0].FreOffsetLimit[3].AbsPwr = -36;

    Param.GSM.LimitInfo.SpecModLimit[1].RefPwrLimit.LowPwr = 33;
    Param.GSM.LimitInfo.SpecModLimit[1].RefPwrLimit.HighPwr = 34;
    Param.GSM.LimitInfo.SpecModLimit[1].FreOffsetLimit[3].LowPwrRel = -54;
    Param.GSM.LimitInfo.SpecModLimit[1].FreOffsetLimit[3].HighPwrRel = -54;
    Param.GSM.LimitInfo.SpecModLimit[2].RefPwrLimit.LowPwr = 33;
    Param.GSM.LimitInfo.SpecModLimit[2].RefPwrLimit.HighPwr = 34;
    Param.GSM.LimitInfo.SpecModLimit[2].FreOffsetLimit[3].LowPwrRel = -54;
    Param.GSM.LimitInfo.SpecModLimit[2].FreOffsetLimit[3].HighPwrRel = -54;

    // LimitInfo.SpecSwiLimit
    for (int i = 0; i < arraySize(Param.GSM.LimitInfo.SpecSwiLimit); i++)
    {
        for (int j = 0; j < arraySize(Param.GSM.LimitInfo.SpecSwiLimit[0].RefPower); j++)
        {
            Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[j].State = 1;
        }

        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[0].Limit = 39;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[1].Limit = 37;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[2].Limit = 35;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[3].Limit = 33;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[4].Limit = 31;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[5].Limit = 29;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[6].Limit = 27;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[7].Limit = 25;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[8].Limit = 23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].RefPower[9].Limit = 21;

        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].State = 1;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[0] = -13;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[1] = -15;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[2] = -17;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[3] = -19;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[4] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[5] = -23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[6] = -23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[7] = -23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[8] = -23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[9] = -23;

        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].State = 1;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[0] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[1] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[2] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[3] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[4] = -23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[5] = -25;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[6] = -26;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[7] = -26;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[8] = -26;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[9] = -26;

        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].State = 1;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[0] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[1] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[2] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[3] = -21;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[4] = -23;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[5] = -25;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[6] = -27;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[7] = -29;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[8] = -31;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[9] = -32;

        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].State = 1;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[0] = -24;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[1] = -24;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[2] = -24;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[3] = -24;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[4] = -26;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[5] = -28;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[6] = -30;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[7] = -32;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[8] = -34;
        Param.GSM.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[9] = -36;
    }
}
