#include "scpi_3gpp_common.h"

int cellular::common::GetScpiCommandNumbers(scpi_t *context,
                                         std::vector<cellular::common::Command> &params)
{
    std::vector<int> command_numbers(params.size());
    if (!SCPI_CommandNumbers(context, command_numbers.data(), command_numbers.size()))
    {
        return WT_3GPP_GET_COMMAND_NUM_FAILED;
    }

    // 注: 此处max为实际可取的最大值+1
    for (int i = 0; i < command_numbers.size(); ++i)
    {
        int value = command_numbers[i];
        if (value < params[i].min_ || value >= params[i].max_)
        {
            return WT_3GPP_COMMAND_NUM_OUT_OF_RANGE;
        }
        params[i].value_ = value;
    }
    return WT_OK;
}