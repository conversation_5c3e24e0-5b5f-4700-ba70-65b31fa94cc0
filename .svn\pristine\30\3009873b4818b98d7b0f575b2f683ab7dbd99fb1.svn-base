#include "scpi_3gpp_alz_NBIOT.h"
#include <iostream>
#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_base.h"

static int IsNBIOT(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->vsaAlzParam.analyzeParam3GPP.Standard;

        if (demod != ALG_3GPP_STD_NB_IOT)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

static int IsNBIOTDownLink(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->vsaAlzParam.analyzeParam3GPP.Standard;

        if (demod != ALG_3GPP_STD_NB_IOT || ALG_3GPP_DL != attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}
//scpi_result_t SCPI_NBIOT_SetAnalyzeLinkDirect(scpi_t *context)
//{
//    int iRet = WT_ERR_CODE_OK;
//
//    do
//    {
//        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
//        printf("attr->vsaAlzParam.analyzeParam3GPP.Standard=%d\n", attr->vsaAlzParam.analyzeParam3GPP.Standard);
//        iRet = IsNBIOT(attr);
//        IF_BREAK(iRet);

//        int Value = 0;
//        if (!SCPI_ParamInt(context, &Value, true))
//        {
//            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
//            break;
//        }

//        if (Value != ALG_3GPP_UL && Value != ALG_3GPP_DL)
//        {
//            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
//            break;
//        }
//        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect = Value;
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
//    } while (0);
//    
//    return SCPI_ResultOK(context, iRet);
//}

//UL
scpi_result_t SCPI_NBIOT_SetAnalyzeDuplexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Duplexing = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeOperationMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.OperationMode = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeChnnelBandWidth(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (200 * KHz_API) && 
            Value != (3 * MHz_API) && 
            Value != (5 * MHz_API) && 
            Value != (10 * MHz_API) && 
            Value != (15 * MHz_API) && 
            Value != (20 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.ChannelBW = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeResourceBlockIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -47 || Value > 134)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.RBIdx = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeCellID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.NBCellID = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeChannelType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.Reset_3GPPAlzNBIOTChanParam(attr->vsaAlzParam.analyzeParam3GPP.NBIOT, Value);
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.ChanType = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.Format = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000 && Value != 3750)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.SCSpacing = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschRepetTimes(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && 
            Value != 2 &&
            Value != 4 &&
            Value != 8 &&
            Value != 16 &&
            Value != 32 &&
            Value != 64 &&
            Value != 128)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.Repetitions = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschResourceUnitsNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && 
            Value != 2 &&
            Value != 3 &&
            Value != 4 &&
            Value != 5 &&
            Value != 6 &&
            Value != 8 &&
            Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.RUs = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschSubCarrierNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && 
            Value != 3 &&
            Value != 6 &&
            Value != 12)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.SubcarrierNum = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschSubCarrierStart(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 47)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.StartSubcarrier = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschCyclicShift(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.CyclicShift = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschGroupHopping(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.GrpHopping = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschDeltaSquenceShift(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 29)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.DeltaSeqShift = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_PID2_BPSK &&
            Value != ALG_3GPP_QPSK &&
            Value != ALG_3GPP_PID4_QPSK)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.Modulate = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschChannelCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.ChanCodingState = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.Scrambling = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.UeID = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschStartSubFrame(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.StartSubfrm = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschTransportBlockSizeIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.TBSIdx = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschRVStartIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
  
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.StartRVIdx = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeMeasureModulationConstShowPilot(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.Measure.ConstShowPilot = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAlzMeasureUnit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOT(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.Measure.MeasureUnit = Value;
        std::cout << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLDuplexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Duplexing = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLOperationMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.OperationMode = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLCarrierType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.CarrierType = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLChnnelBandWidth(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (200 * KHz_API) && 
            Value != (3 * MHz_API) && 
            Value != (5 * MHz_API) && 
            Value != (10 * MHz_API) && 
            Value != (15 * MHz_API) && 
            Value != (20 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.ChannelBW = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLResourceBlockIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -47 || Value > 134)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.RBIdx = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLCellID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.NBCellID = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLLTECellID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.LTECellID = Value;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDL_LTEAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.LTEAntennaNum = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDL_NBAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.NBAntennaNum = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLSIB1Switch(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.SIB1Switch = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLSIB1SchedulingInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.SchedulingInfoSIB1 = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNPSSPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        double Value = 0.0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.NPssPower = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNSSSPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        double Value = 0.0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.NSssPower = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLChannelType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.Reset_3GPPAlzNBIOTChanParam(attr->vsaAlzParam.analyzeParam3GPP.NBIOT, Value);
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.ChanType = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdschSubFrameNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        std::vector<int> RefValueVec{1, 2, 3, 4, 5, 6, 8, 10};
        std::vector<int>::iterator Iter = find(RefValueVec.begin(), RefValueVec.end(), Value); 
        if (Iter == RefValueVec.end())
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.NSF = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdschRepetNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        std::vector<int> RefValueVec{1, 2, 4, 8, 16, 32, 64, 128, 192, 256, 384, 512, 768, 1024, 1536, 2048};
        std::vector<int>::iterator Iter = find(RefValueVec.begin(), RefValueVec.end(), Value); 
        if (Iter == RefValueVec.end())
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.Repetitions = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdschMCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.MCS= Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscStartSymbol(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.StartSymb = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscStartSubFrame(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 6;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.StartSubfrm = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.Precoding = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.ChanCodingState = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.Scrambling = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 && Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.UeID = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNBIOTDownLink(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 && Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NBIOT.DL.Npdsch.Power = Value;
        //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << std::endl;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}