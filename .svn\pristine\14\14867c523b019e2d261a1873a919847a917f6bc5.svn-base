#include "vsa_11ah.h"
#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "commonhandler.h"
#include "vsa_data_info.h"
#include "tester.h"
#include "internal.h"
#include "tester_mt.h"
#include "wtlog.h"

/*****************************set analyze param**********************************/
scpi_result_t SetVsaAlzAhFrameType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int ParamValue = 0;

    if (!SCPI_ParamInt(context, &ParamValue, true))
    {
        iRet = SCPI_RES_ERR;
        IF_ERR_RETURN(iRet);
    }

    if (ParamValue < S1G || ParamValue > S1G_DUP_2M)
    {
        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        IF_ERR_RETURN(iRet);
    }

    attr->vsaAlzParam.analyzeParamWifi.FrameType11AH = ParamValue;
    return SCPI_ResultOK(context, iRet);
}

/*******************************get vsa result***********************************/
static int Is11AH(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11AH_1M, WT_DEMOD_11AH_16M);
}

#define Resq11ahDataInfo(name, isDouble)                                                    \
do                                                                                          \
{                                                                                           \
    DataInfo11ah DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = Is11AH(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)

#define Resq11ahDataInfoBitInfo(name, lenName)                                              \
do                                                                                          \
{                                                                                           \
    DataInfo11ah DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = Is11AH(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    int BitLen = (int)DataInfo.lenName;                                                     \
    SCPI_ResultInt(context, BitLen);                                                        \
    if(BitLen > 0)                                                                          \
    {                                                                                       \
        unique_ptr<char[]> tmpBuf(new char[BitLen + 50]);                                   \
        int tmpLen = 0;                                                                     \
        for (int i = 0; i < BitLen; i++)                                                    \
        {                                                                                   \
            tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", DataInfo.name[i]);               \
        }                                                                                   \
        SCPI_ResultText(context, (const char *)tmpBuf.get());                               \
    }                                                                                       \
} while (0)

//datainfo detail result
scpi_result_t GetVsaRst_11ah_PreambleType(scpi_t *context)
{
    Resq11ahDataInfo(PreambleType, false);
    return SCPI_RES_OK;
}
scpi_result_t GetVsaRst_11ah_UserNumber(scpi_t *context)
{
    Resq11ahDataInfo(UserNumber, false);
    return SCPI_RES_OK;
}

// ah S1G short preamble
scpi_result_t GetVsaRst_11ah_short_ValidFlag(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.ValidFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_ReservedB0(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.ReservedB0, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_BW(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.BW, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_STBC(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.STBC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_ULDL(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.ULDL, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_NSS(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.NSS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_NSTS(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.Nsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_ID(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.ID, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_GI(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.ShortGi, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_CodingType(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.CodingType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_LDPCExtra(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.LDPCExtra, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_MCS(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.MCS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_Smoothing(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.Smoothing, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_Aggregation(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.IsAggregation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_Length(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.Length, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_RespIndication(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.ResponseIndication, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_TravelingPilots(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.TravelingPilots, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_NDPIndication(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.NDPIndication, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_CRC(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.CRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_Tail(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.Tail, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_SigBitLength(scpi_t *context)
{
    Resq11ahDataInfo(S1GShortInfo.SigBitLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_short_SigBit(scpi_t *context)
{
    Resq11ahDataInfoBitInfo(S1GShortInfo.SigBit, S1GShortInfo.SigBitLen);
    return SCPI_RES_OK;
}

//S1G Long Preamble SU
scpi_result_t GetVsaRst_11ah_long_SU_ValidFlag(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.ValidFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_BW(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.BW, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_STBC(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.STBC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_ULDL(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.ULDL, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_NSS(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.NSS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_NSTS(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.Nsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_ID(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.ID, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_GI(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.ShortGi, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_CodingType(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.CodingType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_LDPCExtRa(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.LDPCExtra, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_MCS(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.MCS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_BeamChange(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.BeamChange, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_Smoothing(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.Smoothing, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_Aggregation(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.IsAggregation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_Length(scpi_t *context){
    Resq11ahDataInfo(S1GLongSuInfo.Length, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_RespIndication(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.ResponseIndication, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_TravelingPilots(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.TravelingPilots, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_ReservedA2B12(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.ReservedA2B12, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_CRC(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.CRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_Tail(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.Tail, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_SigABitLength(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongSuInfo.SigABitLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_SU_SigABit(scpi_t *context)
{
    Resq11ahDataInfoBitInfo(S1GLongSuInfo.SigABit, S1GLongSuInfo.SigABitLen);
    return SCPI_RES_OK;
}

// S1G long preamble MU
scpi_result_t GetVsaRst_11ah_long_MU_ValidFlag(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.ValidFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_BW(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.BW, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_STBC(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.STBC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_ReservedA1B2(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.ReservedA1B2, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_NSS(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.NSS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_NSTS(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.Nsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_GroupID(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.GroupID, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_CodingTypeI(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.CodingTypeI, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_CodingTypeII(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.CodingTypeII, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_ReservedA2B1(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.ReservedA2B1, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_Length(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.Length, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_RespIndication(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.ResponseIndication, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_TravelingPilots(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.TravelingPilots, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_CRC(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.CRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_Tail(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.Tail, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_SigABitLength(scpi_t *context)
{
    Resq11ahDataInfo(S1GLongMuInfo.SigABitLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_long_MU_SigABit(scpi_t *context)
{
    Resq11ahDataInfoBitInfo(S1GLongMuInfo.SigABit, S1GLongMuInfo.SigABitLen);
    return SCPI_RES_OK;
}

// S1G S1M preamble
scpi_result_t GetVsaRst_11ah_S1M_ValidFlag(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.ValidFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_BW(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.BW, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_STBC(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.STBC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_NSS(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.NSS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_NSTS(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.Nsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_GI(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.ShortGi, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_CodingType(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.CodingType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_LDPCExtra(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.LDPCExtra, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_ReservedB6(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.ReservedB6, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_MCS(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.MCS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_Aggregation(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.IsAggregation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_Length(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.Length, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_RespIndication(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.ResponseIndication, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_Smoothing(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.Smoothing, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_TravelingPilots(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.TravelingPilots, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_NDPIndication(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.NDPIndication, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_CRC(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.CRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_Tail(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.Tail, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_SigBitLength(scpi_t *context)
{
    Resq11ahDataInfo(S1GS1MInfo.SigBitLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ah_S1M_SigABit(scpi_t *context)
{
    Resq11ahDataInfoBitInfo(S1GS1MInfo.SigBit, S1GS1MInfo.SigBitLen);
    return SCPI_RES_OK;
}

static int GetUserID_NSSID(scpi_t * context, int &UserID, int &NSS_ID)
{
    int iRet = WT_ERR_CODE_OK;
    int Number[2] = { 0 , 0 };
    UserID = 1;
    NSS_ID = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, 2))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        //RUID从1开始
        if (Number[0] < 1)
        {
            Number[0] = 1;
        }

        if (Number[1] < 0)
        {
            Number[1] = 0;
        }
        UserID = Number[0];
        NSS_ID = Number[1];

        if (UserID > MAX_AH_USER_NUM || NSS_ID > MAX_DF_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);
    return iRet;
}

#define Rsq_11ah_MUMIMO_UserInfo(name, isDouble)                                                               \
do                                                                                                          \
{                                                                                                           \
    int iRet = Is11AH(context);                                                                             \
    IF_ERR_RETURN(iRet);                                                                                    \
    int UserID = 1;                                                                                         \
    int NSSID = 0;                                                                                          \
    iRet = GetUserID_NSSID(context, UserID, NSSID);                                                         \
    IF_ERR_RETURN(iRet);                                                                                    \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);                              \
    DataInfo11ah DataInfo;                                                                              \
    iRet = WT_GetVectorResult(attr->ConnID,                                                                 \
        WT_RES_DATA_INFO,                                                                      \
        &DataInfo,                                                                                          \
        sizeof(DataInfo11ah));                                                                          \
    IF_ERR_RETURN(iRet);                                                                                    \
    if(false == isDouble) SCPI_ResultInt(context, (int)DataInfo.UserInfo[UserID - 1].name);             \
    else SCPI_ResultDouble(context, (double)DataInfo.UserInfo[UserID - 1].name);                        \
} while (0)

static void ResqInvalidValue(scpi_t *context, bool isDouble)
{
    if (false == isDouble)
    {
        SCPI_ResultInt(context, -999);
    }
    else
    {
        SCPI_ResultDouble(context, -999.999);
    }
}

scpi_result_t GetVsaRst_AH_MUMIMO_AllUserInformation(scpi_t *context)
{
    int iRet = Is11AH(context);
    IF_ERR_RETURN(iRet);

    int UserID = 1;
    int NSSID = 0;
    iRet = GetUserID_NSSID(context, UserID, NSSID);
    IF_ERR_RETURN(iRet);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    DataInfo11ah DataInfo;
    iRet = WT_GetVectorResult(attr->ConnID,
        WT_RES_DATA_INFO,
        &DataInfo,
        sizeof(DataInfo11ah));
    IF_ERR_RETURN(iRet);

    int UserNum = DataInfo.UserNumber;
    SCPI_ResultInt(context, UserNum);  //usernumber
    for(int i = 0; i < UserNum; i++)
    {
        SCPI_ResultDouble(context, DataInfo.UserInfo[i].UserPower);
        SCPI_ResultDouble(context, DataInfo.UserInfo[i].AllEvm);
        SCPI_ResultDouble(context, DataInfo.UserInfo[i].DataEvm);
        SCPI_ResultDouble(context, DataInfo.UserInfo[i].PilotEvm);
        SCPI_ResultDouble(context, DataInfo.UserInfo[i].DataRate);

        SCPI_ResultInt(context, DataInfo.UserInfo[i].PsduLen);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].Crc);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].Mcs);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].FecCode);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].SigBMCS);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].SigBCRC);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].SigBTail);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].SigBBitLen);

        //sig bit
        stringstream msg;
        for (int i = 0; i <  DataInfo.UserInfo[i].SigBBitLen; i++)
        {
            msg <<  DataInfo.UserInfo[i].SigBBit;
        }
        SCPI_ResultText(context, msg.str().c_str());


        SCPI_ResultInt(context, DataInfo.UserInfo[i].CodingRate);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].Nsts);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].Nss);
        SCPI_ResultInt(context, DataInfo.UserInfo[i].Modulation);

        const int MaxStreamCnt = 8;
        for(int j = 0; j < MaxStreamCnt; j++)
        {
            int UserNstsFlag = DataInfo.UserInfo[i].UserNstsFlag[j];
            SCPI_ResultInt(context, UserNstsFlag);
            if(UserNstsFlag != -999 && UserNstsFlag != 0)
            {
                SCPI_ResultDouble(context, DataInfo.UserInfo[i].UserPowerNsts[j]);
                SCPI_ResultDouble(context, DataInfo.UserInfo[i].AllEvmNsts[j]);
                SCPI_ResultDouble(context, DataInfo.UserInfo[i].DateEvmNsts[j]);
                SCPI_ResultDouble(context, DataInfo.UserInfo[i].PilotEvmNsts[j]);
            }
            else
            {
                ResqInvalidValue(context, true);
                ResqInvalidValue(context, true);
                ResqInvalidValue(context, true);
                ResqInvalidValue(context, true);
            }
        }
    }
    return SCPI_RES_OK;
}
scpi_result_t GetVsaRst_AH_MUMIMO_UserDataRate(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(DataRate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserPSDULen(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(PsduLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserPSDUCRC(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(Crc, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserMCS(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(Mcs, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserCodingType(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(FecCode, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserModulation(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(Modulation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserCodingRate(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(CodingRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserSIGBInfos(scpi_t *context)
{
    int iRet = Is11AH(context);
    IF_ERR_RETURN(iRet);

    int UserID = 1;
    int NSSID = 0;
    iRet = GetUserID_NSSID(context, UserID, NSSID);
    IF_ERR_RETURN(iRet);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    DataInfo11ah DataInfo;
    iRet = WT_GetVectorResult(attr->ConnID,
        WT_RES_DATA_INFO,
        &DataInfo,
        sizeof(DataInfo11ah));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.UserInfo[UserID-1].SigBMCS);
    SCPI_ResultInt(context, DataInfo.UserInfo[UserID-1].SigBCRC);
    SCPI_ResultInt(context, DataInfo.UserInfo[UserID-1].SigBTail);
    SCPI_ResultInt(context, DataInfo.UserInfo[UserID-1].SigBBitLen);

    //sig bit
    stringstream msg;
    for (int i = 0; i <  DataInfo.UserInfo[UserID-1].SigBBitLen; i++)
    {
        msg <<  DataInfo.UserInfo[UserID-1].SigBBit;
    }
    SCPI_ResultText(context, msg.str().c_str());
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserSIGBMCS(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(SigBMCS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserSIGBCRC(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(SigBCRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserSIGBTail(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(SigBTail, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserSigBBitLen(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(SigBBitLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserSigBBit(scpi_t *context)
{
    int iRet = Is11AH(context);
    IF_ERR_RETURN(iRet);

    int UserID = 1;
    int NSSID = 0;
    iRet = GetUserID_NSSID(context, UserID, NSSID);
    IF_ERR_RETURN(iRet);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    DataInfo11ah DataInfo;
    iRet = WT_GetVectorResult(attr->ConnID,
        WT_RES_DATA_INFO,
        &DataInfo,
        sizeof(DataInfo11ah));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.UserInfo[UserID-1].SigBBitLen);

    //sig bit
    stringstream msg;
    for (int i = 0; i <  DataInfo.UserInfo[UserID-1].SigBBitLen; i++)
    {
        msg <<  DataInfo.UserInfo[UserID-1].SigBBit;
    }
    SCPI_ResultText(context, msg.str().c_str());
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserNSTS(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(Nsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserNSS(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserInfo(Nss, false);
    return SCPI_RES_OK;
}

#define Rsq_11ah_MUMIMO_UserNSSInfo(composit_name, nss_name, isDouble)                 \
do                                                                                  \
{                                                                                   \
    int iRet = is11AC(context);                                                     \
    IF_ERR_RETURN(iRet);                                                            \
    int UserID = 1;                                                                 \
    int NSSID = 0;                                                                  \
    iRet = GetUserID_NSSID(context, UserID, NSSID);                                 \
    IF_ERR_RETURN(iRet);                                                            \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);      \
    DataInfo11ah DataInfo;                                                      \
    iRet = WT_GetVectorResult(attr->ConnID,                                         \
        WT_RES_DATA_INFO,                                              \
        &DataInfo,                                                                  \
        sizeof(DataInfo11ah));                                                  \
    IF_ERR_RETURN(iRet);                                                            \
    if(0 == NSSID)                                                                  \
    {                                                                               \
        if(false == isDouble) SCPI_ResultInt(context, (int)DataInfo.UserInfo[UserID - 1].composit_name);    \
        else SCPI_ResultDouble(context, (double)DataInfo.UserInfo[UserID - 1].composit_name);               \
    }                                                                                                       \
    else                                                                                                    \
    {                                                                                                       \
        if(0 == DataInfo.UserInfo[UserID - 1].UserNstsFlag[NSSID - 1])                                      \
        {                                                                                                   \
            ResqInvalidValue(context, isDouble);                                                            \
        }                                                                                                   \
        else                                                                                                \
        {                                                                                                   \
            if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.UserInfo[UserID - 1].nss_name[NSSID - 1]); \
            else SCPI_ResultDouble(context, (double)DataInfo.UserInfo[UserID - 1].nss_name[NSSID - 1]);             \
        }                                                                                                           \
    }                                                                                                               \
} while (0)

scpi_result_t GetVsaRst_AH_MUMIMO_UserPower(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserNSSInfo(UserPower, UserPowerNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserEVMALL(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserNSSInfo(AllEvm, AllEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserEVMData(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserNSSInfo(DataEvm, DateEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_AH_MUMIMO_UserEVMPilot(scpi_t *context)
{
    Rsq_11ah_MUMIMO_UserNSSInfo(PilotEvm, PilotEvmNsts, true);
    return SCPI_RES_OK;
}
