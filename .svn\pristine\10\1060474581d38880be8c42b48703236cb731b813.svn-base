#ifndef SCPI_3GPP_RESULT_GSM_H_
#define SCPI_3GPP_RESULT_GSM_H_

#include "scpi/scpi.h"

// 获取gsm分析结果
scpi_result_t SCPI_3GPP_GetVsaRstSummaryGSM(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerSymbol(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerPower(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasureBurstNum(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstUpMask(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstDownMask(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstXMark(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstAveragePower(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstTscType(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerBurstType(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasurePartMin(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasurePartMax(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstPowerMeasureSV(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationFrequency(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationPower(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationMask(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationSymbol(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumModulationTime(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchFrequency(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchPower(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchMask(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchSymbol(scpi_t *context);
scpi_result_t SCPI_3GPP_GetVsaRstSpectrumSwitchTime(scpi_t *context);

#endif  // SCPI_3GPP_RESULT_GSM_H_
