#include "cellular_wavegen_default_param.h"

namespace
{
    template <typename T, size_t N>
    inline constexpr size_t arraySize(const T (&)[N]) noexcept
    {
        return N;
    }

    // C++11 兼容的 create_unique 实现
    template<typename T, typename... Args>
    std::unique_ptr<T> create_unique(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }
}

// 公共接口实现
void cellular::wavegen::SetCellularWaveGenDefaultParam(int standard, int linkDirect, Alg_3GPP_WaveGenType* param) {
    if (param == nullptr) {
        return;
    }
    
    std::unique_ptr<WaveGenBase> waveGenPtr;
    switch (standard) {
        case ALG_3GPP_STD_GSM:
            waveGenPtr = create_unique<GsmWaveGenFactory>()->CreateWaveGen();
            break;
        case ALG_3GPP_STD_WCDMA:
            waveGenPtr = create_unique<WcdmaWaveGenFactory>()->CreateWaveGen();
            break;
        case ALG_3GPP_STD_4G:
            waveGenPtr = create_unique<LteWaveGenFactory>()->CreateWaveGen();
            break;
        case ALG_3GPP_STD_5G:
            waveGenPtr = create_unique<NrWaveGenFactory>()->CreateWaveGen();
            break;
        case ALG_3GPP_STD_NB_IOT:
            waveGenPtr = create_unique<NbiotWaveGenFactory>()->CreateWaveGen();
            break;
        default:
            return;
    }
    
    if (waveGenPtr) {
        waveGenPtr->SetDefaultParams(linkDirect, param);
    }
}

//*****************************************************************************
// 公共接口
//*****************************************************************************
void cellular::wavegen::SetCellularWaveGenCommonParam(int demod, PNSetBaseType &commonParam)
{
    memset(&commonParam, 0, sizeof(PNSetBaseType));
    commonParam.standard = demod; // 0x1000为5G,0x1001为4G
    commonParam.subType = 0;
    commonParam.bandwidth = 0; // 未明确
    commonParam.samplingRate = 30720000;
    commonParam.NSS = 1;       // 未明确
    commonParam.segment = 1;   // 未明确
    commonParam.FreqErr = 0;
    commonParam.IQImbalanceAmp = 0;
    commonParam.IQImbalancePhase = 0;
    commonParam.DCOffset_I = 0;
    commonParam.DCOffset_Q = 0;
    commonParam.ClockErr = 0;
    commonParam.Snr = 200;
    commonParam.Gap = 0;              // 未明确
    commonParam.FlatFactor = 0;
    commonParam.ReducePARA = 0;        // 未明确
    commonParam.PhaseNoiseFlg = 0;
    commonParam.PhaseNoiseFactor[0] = -84;
    commonParam.PhaseNoiseFactor[1] = -100;
    commonParam.PhaseNoiseFactor[2] = -96;
    commonParam.PhaseNoiseFactor[3] = -109;
    commonParam.PhaseNoiseFactor[4] = -122;
    commonParam.PhaseNoiseFactor[5] = -200;

    switch (demod)
    {
    case ALG_3GPP_STD_GSM:
        commonParam.samplingRate = 30 * MHz_API;
        break;
    case ALG_3GPP_STD_WCDMA:
    case ALG_3GPP_STD_4G:
        commonParam.samplingRate = 30.72 * MHz_API;
        break;
    case ALG_3GPP_STD_5G:
        commonParam.samplingRate = 122.88 * MHz_API;
        break;
    case ALG_3GPP_STD_NB_IOT:
        commonParam.samplingRate = 15.36 * MHz_API;
        break;
    default:
        break;
    }
}

//*****************************************************************************
// 内部接口
//*****************************************************************************
void cellular::wavegen::GsmWaveGen::SetDefaultParams(int linkDirect, Alg_3GPP_WaveGenType *param)
{
    memset(&(param->GSM), 0, sizeof(Alg_GSM_WaveGenType));
    param->GSM.SequenceMode = 1;
    param->GSM.GenSequenceLen = 1;
    param->GSM.Filter.FilterParameter = 0.3;
    param->GSM.PowerRamp.RampTime = 2.0;
    for (int i = 0; i < arraySize(param->GSM.Slot); i++)
    {
        param->GSM.Slot[i].SlotLevel = 2;
        param->GSM.Slot[i].RepSlotNum = 1;
        for (int j = 0; j < arraySize(param->GSM.Slot[i].NB); j++)
        {
            param->GSM.Slot[i].NB[j].TscSet = 1;
        }
    }
}

void cellular::wavegen::WcdmaWaveGen::SetDefaultParams(int linkDirect, Alg_3GPP_WaveGenType *param)
{
    memset(&(param->WCDMA), 0, sizeof(Alg_WCDMA_WaveGenType));
    param->WCDMA.LinkDirect = linkDirect;
    param->WCDMA.General.GenFrmLen = 1;

    if (linkDirect == ALG_3GPP_UL)
    {
        SetWcdmaULParams(param->WCDMA.UL);
    }
    else
    {
        SetWcdmaDLParams(param->WCDMA.DL);
    }
}

void cellular::wavegen::LteWaveGen::SetDefaultParams(int linkDirect, Alg_3GPP_WaveGenType *param)
{
    memset(&(param->LTE), 0, sizeof(Alg_4G_WaveGenType));
    param->LTE.LinkDirect = linkDirect;
    param->LTE.General.GenSequenceLen = 1;
    param->LTE.General.Filter.Type = 2;
    param->LTE.General.Filter.Fs = 30720000;
    param->LTE.General.Filter.MaxOrder = 512;
    param->LTE.General.Filter.FpassFactor = 0.6;
    param->LTE.General.Filter.FstopFactor = 0.65;
    param->LTE.General.Filter.PassRipple = 0.05;
    param->LTE.General.Filter.StopAtten = 45;
    param->LTE.General.Filter.RollOffFactor = 0.1;
    param->LTE.General.Filter.CutOffFrqFactor = 0.11;
    param->LTE.General.Filter.SmoothFactor = 1;
    
    if (linkDirect == ALG_3GPP_UL)
    {
        SetLteULParams(param->LTE.UL);
    }
    else
    {
        SetLteDLParams(param->LTE.DL);
    }
}

void cellular::wavegen::NrWaveGen::SetDefaultParams(int linkDirect, Alg_3GPP_WaveGenType *param)
{
    memset(&(param->NR), 0, sizeof(Alg_5G_WaveGenType));
    param->NR.LinkDirect = linkDirect;
    param->NR.Version = 17;
    param->NR.General.Filter.Type = 2;
    param->NR.General.Filter.Fs = 122880000;
    param->NR.General.Filter.MaxOrder = 512;
    param->NR.General.Filter.FpassFactor = 0.8138;
    param->NR.General.Filter.FstopFactor = 0.85;
    param->NR.General.Filter.PassRipple = 0.05;
    param->NR.General.Filter.StopAtten = 45;
    param->NR.General.Filter.FilterMode = 1;
    param->NR.General.Filter.SmoothFactor = 1;
    param->NR.General.Filter.CutOffFrqFactor = 0.1;

    if (linkDirect == ALG_3GPP_UL)
    {
        SetNrULParams(param->NR.UL);
    }
    else
    {
        SetNrDLParams(param->NR.DL);
    }
}

void cellular::wavegen::NbiotWaveGen::SetDefaultParams(int linkDirect, Alg_3GPP_WaveGenType *param)
{
    memset(&(param->NBIOT), 0, sizeof(Alg_NBIOT_WaveGenType));
    param->NBIOT.LinkDirect = linkDirect;
    param->NBIOT.General.Filter.Type = 3;
    param->NBIOT.General.Filter.MaxOrder = 256;
    param->NBIOT.General.Filter.FpassFactor = 0.25;
    param->NBIOT.General.Filter.FstopFactor = 0.28;
    param->NBIOT.General.Filter.PassRipple = 0.05;
    param->NBIOT.General.Filter.StopAtten = 45;
    param->NBIOT.General.Filter.RollOffFactor = 0.2;
    param->NBIOT.General.Filter.CutOffFreqShift = 0.5;
    param->NBIOT.General.Filter.WindowLenFactor = 0.5;

    if (linkDirect == ALG_3GPP_UL)
    {
        SetNbiotULParams(param->NBIOT.UL);
        param->NBIOT.General.Filter.Fs = 3840000;
    }
    else
    {
        SetNbiotDLParams(param->NBIOT.DL);
        param->NBIOT.General.Filter.Fs = 30720000;
    }
}

//*****************************************************************************
// WCDMA
//*****************************************************************************
void cellular::wavegen::WcdmaWaveGen::SetWcdmaULParams(Alg_WCDMA_ULWaveGenType &param)
{
    param.ScramblingMode = 1;
    param.DPCCH.TpcDataType = 4;

    // DPDCH
    param.DPDCH.SymbRate = 15000;
    param.DPDCH.DCH.Interleaver2Stat = 1;
    for (int i = 0; i < arraySize(param.DPDCH.DCH.DTCH); i++)
    {
        param.DPDCH.DCH.DTCH[i].Initialization = 1;
        param.DPDCH.DCH.DTCH[i].TTI = 10; // 未明确
        param.DPDCH.DCH.DTCH[i].TbCount = 1;
        param.DPDCH.DCH.DTCH[i].TbSize = 100;
        param.DPDCH.DCH.DTCH[i].Crc = 16;
        param.DPDCH.DCH.DTCH[i].RmAttribute = 1;
        param.DPDCH.DCH.DTCH[i].EProtection = 3;
        param.DPDCH.DCH.DTCH[i].InterleaverStat = 1;
        param.DPDCH.Initialization[i] = 1;
    }
    param.DPDCH.DCH.DCCH.Initialization = 1;
    param.DPDCH.DCH.DCCH.TTI = 10; // 未明确
    param.DPDCH.DCH.DCCH.TbCount = 1;
    param.DPDCH.DCH.DCCH.TbSize = 100;
    param.DPDCH.DCH.DCCH.Crc = 16;
    param.DPDCH.DCH.DCCH.RmAttribute = 1;
    param.DPDCH.DCH.DCCH.EProtection = 3;
    param.DPDCH.DCH.DCCH.InterleaverStat = 1;

    // HS-DPCCH
    param.HSDPCCH.StartDelay = 101;
    param.HSDPCCH.InterTTIDist = 5;
}

void cellular::wavegen::WcdmaWaveGen::SetWcdmaDLParams(Alg_WCDMA_DLWaveGenType &param)
{
    param.PCPICH.State = 1;
    param.PCPICH.SymbRate = 15000;

    param.PSCH.State = 1;
    param.PSCH.SymbRate = 15000;
    
    param.PCCPCH.State = 1;
    param.PCCPCH.SymbRate = 15000;
    param.PCCPCH.ChanCode = 1;
    param.PCCPCH.Initialization = 1;

    param.SSCH.State = 1;
    param.SSCH.SymbRate = 15000;

    param.SCCPCH.State = 1;
    param.SCCPCH.SymbRate = 15000;
    param.SCCPCH.ChanCode = 2;
    param.SCCPCH.Initialization = 1;

    param.DPCH[0].DCH.Interleaver2Stat = 1;
    for (int j = 0; j < arraySize(param.DPCH[0].DCH.DTCH); j++)
    {
        param.DPCH[0].DCH.DTCH[j].Initialization = 1;
        param.DPCH[0].DCH.DTCH[j].TTI = 10;
        param.DPCH[0].DCH.DTCH[j].TbCount = 1;
        param.DPCH[0].DCH.DTCH[j].TbSize = 100;
        param.DPCH[0].DCH.DTCH[j].Crc = 16;
        param.DPCH[0].DCH.DTCH[j].RmAttribute = 1;
        param.DPCH[0].DCH.DTCH[j].EProtection = 3;
        param.DPCH[0].DCH.DTCH[j].InterleaverStat = 1;
    }
    param.DPCH[0].DCH.DCCH.Initialization = 1;
    param.DPCH[0].DCH.DCCH.TTI = 10;
    param.DPCH[0].DCH.DCCH.TbCount = 1;
    param.DPCH[0].DCH.DCCH.TbSize = 100;
    param.DPCH[0].DCH.DCCH.Crc = 16;
    param.DPCH[0].DCH.DCCH.RmAttribute = 1;
    param.DPCH[0].DCH.DCCH.EProtection = 3;
    param.DPCH[0].DCH.DCCH.InterleaverStat = 1;
    
    param.DPCH[0].Initialization = 1;
    param.DPCH[0].TpcDataType = 4;
}

//*****************************************************************************
// LTE
//*****************************************************************************
void cellular::wavegen::LteWaveGen::SetLteULParams(Alg_4G_ULWaveGenType &param)
{
    param.CchSfCfgNum = 1;
    param.SchSfCfgNum = 1;
    param.MultiCell.Cell[0].State = 1;
    for (int i = 0; i < arraySize(param.MultiCell.Cell); i++)
    {
        param.MultiCell.Cell[i].ChannelBW = 20 * MHz;
    }
    for (int i = 0; i < arraySize(param.Ue.Pusch); i++)
    {
        param.Ue.Pusch[i].Initialization = 1;
        param.Ue.Pusch[i].MaxNumAP = 1;
        param.Ue.Pusch[i].Scramble = 1;
        param.Ue.Pusch[i].ChanCodingState = 1;
    }
    
    param.Chan[0].State = 1;
    param.Chan[0].Pusch[0].State = 1;
    for (int i = 0; i < arraySize(param.Chan); i++)
    {
        for (int j = 0; j < arraySize(param.Chan[i].Pusch); j++)
        {
            param.Chan[i].Pusch[j].RBSetNum = 1;
            for (int k = 0; k < arraySize(param.Chan[i].Pusch[j].RBNum); k++)
            {
                param.Chan[i].Pusch[j].RBNum[k] = 100;
            }
            param.Chan[i].Pusch[j].LayerNum = 1;
            param.Chan[i].Pusch[j].AntennaNum = 1;
            param.Chan[i].Pusch[j].Codeword = 1;
            for (int k = 0; k < arraySize(param.Chan[i].Pusch[j].Encode.Modulate); k++)
            {
                param.Chan[i].Pusch[j].Encode.Modulate[k] = 2;
            }
            for (int k = 0; k < arraySize(param.Chan[i].Pusch[j].Encode.PayloadSize); k++)
            {
                param.Chan[i].Pusch[j].Encode.PayloadSize[k] = 1500;
            }
        }
    }
}

void cellular::wavegen::LteWaveGen::SetLteDLParams(Alg_4G_DLWaveGenType &param)
{
    param.Schedule.SchedPattern = 1;
    param.SubfrmCfgNum = 1;
    param.OCNGFlag = 1;
    param.DummyModulate = 2;
    param.MultiCell.TxAntennaNum = 1;
    param.MultiCell.Cell[0].State = 1;
    for (int i = 0; i < arraySize(param.MultiCell.Cell); i++)
    {
        param.MultiCell.Cell[i].ChannelBW = 10 * MHz;
        param.MultiCell.Cell[i].PdschStart = 1;
    }

    param.Ue.Scramble = 1;
    param.Ue.ChanCodingState = 1;
    param.Ue.ApMapping = 1;
    param.Ue.UECategory = 1;
    param.Ue.Initialization = 1;
    param.Ue.TxMode = 1;
    param.Ue.McsTable = 1;

    for (int i = 0; i < arraySize(param.Chan); i++)
    {
        for (int j = 0; j < arraySize(param.Chan[i]); j++)
        {
            param.Chan[i][j].PBCH.State = 1;
            param.Chan[i][j].PBCH.Scrambling = 1;

            param.Chan[i][j].PDCCH.Format = -2;
            param.Chan[i][j].PDCCH.DummyCCEType = 1;
            param.Chan[i][j].PDCCH.PDCCHNum = 1;
            param.Chan[i][j].PDCCH.DCI[0].DCIFormat = 1;
            param.Chan[i][j].PDCCH.DCI[0].SearchSpace = 1;
            param.Chan[i][j].PDCCH.DCI[0].Format1.ResBlkAssign = 1;
            param.Chan[i][j].PDCCH.DCI[0].Format1.NewDataInd = 1;
            param.Chan[i][j].PDCCH.DCI[0].Format1.RvIdx = -1;
            param.Chan[i][j].PDCCH.DCI[0].Format1A.ResBlkAssign = 1;
            param.Chan[i][j].PDCCH.DCI[0].Format1A.NewDataInd = 1;
            param.Chan[i][j].PDCCH.DCI[1].State = 1;
            param.Chan[i][j].PDCCH.DCI[1].SearchSpace = 1;
            param.Chan[i][j].PDCCH.DCI[1].CCEIdx = 1;

            param.Chan[i][j].PDSCH.State = 1;
            param.Chan[i][j].PDSCH.ResAllocateType = 2;
            memset(param.Chan[i][j].PDSCH.RBGBitmap, 1, sizeof(char) * 32);
            param.Chan[i][j].PDSCH.RBNum = 50;
            param.Chan[i][j].PDSCH.AutoOffset = 1;
            param.Chan[i][j].PDSCH.LayerNum = 1;
            param.Chan[i][j].PDSCH.Codeword = 1;
            for (int k = 0; k < arraySize(param.Chan[i][j].PDSCH.Modulate); k++)
            {
                param.Chan[i][j].PDSCH.Modulate[k] = 2;
            }
            for (int k = 0; k < arraySize(param.Chan[i][j].PDSCH.PayloadSize); k++)
            {
                param.Chan[i][j].PDSCH.PayloadSize[k] = 1500;
            }
            for (int k = 0; k < arraySize(param.Chan[i][j].PDSCH.NIR); k++)
            {
                param.Chan[i][j].PDSCH.NIR[k] = 3667200;
            }
            for (int k = 0; k < arraySize(param.Chan[i][j].PDSCH.SoftChanBit); k++)
            {
                param.Chan[i][j].PDSCH.SoftChanBit[k] = 58675200;
            }

            param.Chan[i][j].PCFICH.State = 1;
            param.Chan[i][j].PCFICH.Scrambling = 1;
            param.Chan[i][j].PCFICH.PDCCHSymNum = 1;

            param.Chan[i][j].PHICH.Power = -3.010;
        }
    }
}

//*****************************************************************************
// NR
//*****************************************************************************
void cellular::wavegen::NrWaveGen::SetNrULParams(Alg_5G_ULWaveGenType &param)
{
    param.SlotPeriod = 5;
    param.ULSlotNumber = 1;
    param.CellNum = 1;

    param.Cell[0].State = 1;
    for (int i = 0; i < arraySize(param.Cell); i++)
    {
        param.Cell[i].Frequency = 1.95; // 单位: GHz
        param.Cell[i].ChannelBW = 100 * MHz; // 100MHz
        param.Cell[i].DmrsTypeAPos = 2;
        param.Cell[i].TxBW[0].SCSpacing = 15 * KHz;
        param.Cell[i].TxBW[1].SCSpacing = 30 * KHz;
        param.Cell[i].TxBW[2].SCSpacing = 60 * KHz;

        param.Cell[i].TxBW[1].State = 1;

        param.Cell[i].TxBW[0].MaxRBNumb = 270;
        param.Cell[i].TxBW[1].MaxRBNumb = 273;
        param.Cell[i].TxBW[2].MaxRBNumb = 135;
    }

    param.Ue.Scramble = 1;
    param.Ue.Initialization = 1;
    for (int i = 0; i < arraySize(param.Ue.Bwp); i++)
    {
        param.Ue.Bwp[i].SCSpacing = 30000;
        param.Ue.Bwp[i].RBNum = 273;
        param.Ue.Bwp[i].Pusch.Dmrs.ConfigType = 1;
        param.Ue.Bwp[i].Pusch.Dmrs.MaxLength = 1;
    }

    for (int i = 0; i < arraySize(param.Subfrm); i++)
    {
        for (int j = 0; j < arraySize(param.Subfrm[i]); j++)
        {
            param.Subfrm[i][j].SlotCfgNum = 1;
            for (int k = 0; k < 4; k++)
            {
                param.Subfrm[i][j].Slot[k].State = 1;
                param.Subfrm[i][j].Slot[k].Pusch.SymbNum = 14;
                param.Subfrm[i][j].Slot[k].Pusch.RBNum = param.Ue.Bwp[i].RBNum;
                param.Subfrm[i][j].Slot[k].Pusch.LayerNum = 1;
                param.Subfrm[i][j].Slot[k].Pusch.AntennaNum = 1;
                param.Subfrm[i][j].Slot[k].Pusch.Modulate = 2;
                param.Subfrm[i][j].Slot[k].Pusch.CDMGrpWOData = 1;
                param.Subfrm[i][j].Slot[k].Pusch.DmrsSymbLen = 1;
                param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[0] = 0;
                param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[1] = 1;
                param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[2] = 2;
                param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[3] = 3;
            }
        }
    }
}

void cellular::wavegen::NrWaveGen::SetNrDLParams(Alg_5G_DLWaveGenType &param)
{
    param.SlotPeriod = 10;
    param.DLSlotNumber = 1;
    param.CellNum = 1;
    param.RestrictSS = 1;

    param.Cell[0].State = 1;
    for (int i = 0; i < arraySize(param.Cell); i++)
    {
        param.Cell[i].Frequency = 1.95; // 单位: GHz
        param.Cell[i].ChannelBW = 100 * MHz;
        param.Cell[i].DmrsTypeAPos = 2;
        param.Cell[i].TxBW[0].SCSpacing = 15 * KHz;
        param.Cell[i].TxBW[1].SCSpacing = 30 * KHz;
        param.Cell[i].TxBW[2].SCSpacing = 60 * KHz;

        param.Cell[i].TxBW[1].State = 1;

        param.Cell[i].TxBW[0].MaxRBNumb = 270;
        param.Cell[i].TxBW[1].MaxRBNumb = 273;
        param.Cell[i].TxBW[2].MaxRBNumb = 135;

        param.Cell[i].Pbch.OffsetRefType = 1;
        param.Cell[i].Pbch.State = 1;
        param.Cell[i].Pbch.SCSpacing = 30000;
        param.Cell[i].Pbch.RBOffset = 126;
        param.Cell[i].Pbch.SCOffset = 6;
        param.Cell[i].Pbch.PbchCase = 1;
        param.Cell[i].Pbch.Length = 4;
        param.Cell[i].Pbch.Position[2] = 1;
        param.Cell[i].Pbch.Position[3] = 1;
        param.Cell[i].Pbch.BurstSetPeriod = 10;
        param.Cell[i].Pbch.MIB.SCOffset = 1;
        param.Cell[i].Pbch.MIB.CoresetZero = 1;
        param.Cell[i].Pbch.MIB.SSZero = 1;
        param.Cell[i].Pbch.MIB.CellBarre = 1;
        param.Cell[i].Pbch.MIB.InFreqResel = 1;
        param.Cell[i].Pbch.MIB.AutoSCOffset = 1;
        param.Cell[i].OCNGMode= 1;
        param.Cell[i].OCNGModulation= 2;
    }

    param.Ue.Scrambling = 1;
    param.Ue.Initialization = 1;
    param.Ue.UeID = 14;
    for (int i = 0; i < arraySize(param.Ue.Bwp); i++)
    {
        param.Ue.Bwp[i].SCSpacing = 30 * KHz;
        param.Ue.Bwp[i].RBNum = 273;

        param.Ue.Bwp[i].Pdsch.ResourceAllocation = 1;
        param.Ue.Bwp[i].Pdsch.Dmrs.ConfigType = 1;
        param.Ue.Bwp[i].Pdsch.Dmrs.MaxLength = 2;
        param.Ue.Bwp[i].Pdsch.MaxCWNumPerDCI = 1;
        param.Ue.Bwp[i].Pdsch.ResourceAllocation = 1;
        param.Ue.Bwp[i].Pdsch.RBGSizeType = 1;
        param.Ue.Bwp[i].Pdsch.HProcNumSize = 4;

        param.Ue.Bwp[i].Coreset.State = 1;
        param.Ue.Bwp[i].Coreset.SymbNum = 1;
        param.Ue.Bwp[i].Coreset.RBNum = 6;
        memset(param.Ue.Bwp[i].Coreset.BitMap, 1, sizeof(char) * 48);
        param.Ue.Bwp[i].Coreset.CoresetID = 1;
        param.Ue.Bwp[i].Coreset.BundleSize = 6;
        param.Ue.Bwp[i].Coreset.InterleaverSize = 3;
        memset(param.Ue.Bwp[i].Coreset.MaxCandi, 1, sizeof(int) * 5);
    }

    for (int i = 0; i < arraySize(param.Ue.Bwp); i++)
    {
        param.Ue.BwpUL[i].SCSpacing = 30 * KHz;
        param.Ue.BwpUL[i].RBNum = 273;
        param.Ue.BwpUL[i].RBOffset = 0;
        param.Ue.BwpUL[i].Pusch.ResourceAllocation = 1;
        param.Ue.BwpUL[i].Pusch.Dmrs.ConfigType = 1;
        param.Ue.BwpUL[i].Pusch.Dmrs.MaxLength = 1;
        param.Ue.BwpUL[i].Pusch.MaxRank = 1;
        param.Ue.BwpUL[i].Pusch.HarqProcNumSize = 4;
        param.Ue.BwpUL[i].Pucch.TimMapNum = 1;
        param.Ue.BwpUL[i].SRS.ResNum = 1;
        param.Ue.BwpUL[i].SRS.PortNum = 1;
    }

    for (int i = 0; i < arraySize(param.Sched); i++)
    {
        param.Sched[i].SubfrmCfgNum = 1;
        param.Sched[i].SlotCfgNum = 1;
        for (int j = 0; j < arraySize(param.Sched[i].Slot); j++)
        {
            for (int k = 0; k < arraySize(param.Sched[i].Slot[j]); k++)
            {
                param.Sched[i].Slot[j][k].Pdcch.State = 1;
                param.Sched[i].Slot[j][k].Pdcch.UnusedCCEs = 1;
                param.Sched[i].Slot[j][k].Pdcch.Initialization = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[0].State = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[0].DCIFormat = 10;
                param.Sched[i].Slot[j][k].Pdcch.DCI[0].AggregationLevel = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[0].TDAllocTabType = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[0].DF10Crnti.FDResAssign = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[0].DF11Crnti.FDResAssign = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[1].AggregationLevel = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[1].DF00Crnti.FDResAssign = 1;
                param.Sched[i].Slot[j][k].Pdcch.DCI[1].DF01Crnti.FDResAssign = 1;

                param.Sched[i].Slot[j][k].Pdsch.State = 1;
                param.Sched[i].Slot[j][k].Pdsch.SymbNum = 13;
                param.Sched[i].Slot[j][k].Pdsch.SymbOffset = 1;
                param.Sched[i].Slot[j][k].Pdsch.ResourceAllocation = 1;
                param.Sched[i].Slot[j][k].Pdsch.RBNum = 273;
                param.Sched[i].Slot[j][k].Pdsch.Codewords = 1;
                param.Sched[i].Slot[j][k].Pdsch.LayerNum = 1;
                param.Sched[i].Slot[j][k].Pdsch.AntennaNum = 1;
                param.Sched[i].Slot[j][k].Pdsch.CDMGrpWOData = 1;
                param.Sched[i].Slot[j][k].Pdsch.DmrsSymbLen = 1;

                for (int l = 0; l < arraySize(param.Sched[i].Slot[j][k].Pdsch.DmrsAntPort); l++)
                {
                    param.Sched[i].Slot[j][k].Pdsch.DmrsAntPort[l] = 1000;

                }
                
                for (int l = 0; l < arraySize(param.Sched[i].Slot[j][k].Pdsch.Modulate); l++)
                {
                    param.Sched[i].Slot[j][k].Pdsch.Modulate[l] = 2;
                }
            }
        }
    }
}

//*****************************************************************************
// NBIOT
//*****************************************************************************
void cellular::wavegen::NbiotWaveGen::SetNbiotULParams(Alg_NBIOT_ULWaveGenType &param)
{
    param.Cell.ChannelBW = 200000;
    param.Ue.Scrambling = 1;
    param.Ue.Initialization = 1;
    param.Ue.ChanCodingState = 1;
    param.Schedule.Npusch.Format = 1;
    param.Schedule.Npusch.SCSpacing = 15000;
    param.Schedule.Npusch.Repetitions = 1;
    param.Schedule.Npusch.RUs = 1;

    /* Format 1 */
    param.Schedule.Npusch.UseIsc = 1;
    param.Schedule.Npusch.SubcarrierNum = 1;
    param.Schedule.Npusch.Modulate = 1;
    param.Schedule.Npusch.UseMcs = 1;

    /* Format 2 */
    param.Schedule.Npusch.Modulate = 1;
    param.Schedule.Npusch.UseACKResField = 1;
}

void cellular::wavegen::NbiotWaveGen::SetNbiotDLParams(Alg_NBIOT_DLWaveGenType &param)
{
    // cell
    param.Cell.ChannelBW = 200000;
    param.Cell.Lte.RARNTI = 1;
    param.Cell.Lte.LteAntennaNum = 1;
    param.Cell.Lte.LteREsFillState = 1;
    param.Cell.Lte.Modulation = 2;
    param.Cell.Lte.Initialization = 1;
    param.Cell.NBAntennaNum = 1;
    param.Cell.Anchor.CSSType1Rmax = 1;
    param.Cell.Anchor.CSSType2.Rmax = 1;
    param.Cell.Anchor.CSSType2.G = 4;
    param.Cell.Anchor.CSSType2.Offset = 0.25;
    // UE
    param.Ue.UeCategory = 1;
    param.Ue.USS.Rmax = 1;
    param.Ue.USS.G = 4;
    param.Ue.USS.Offset = 0.25;
    param.Ue.Initialization = 1;
    // AhrScheduleType
    param.AhrSchedule.Dci.N2.PagFlg = 1;
    param.AhrSchedule.Npbch.ChanCodingState = 1;
    param.AhrSchedule.Npbch.UseMIB = 1;
    param.AhrSchedule.Npbch.MIBInfo.ABEnabled = 1;
    param.AhrSchedule.SIB1.ChanCodingState = 1;
    param.AhrSchedule.Npdsch.ChanCodingState = 1;

    // NonAhrScheduleType
    for (int i = 0; i < arraySize(param.NonAhrSchedule); i++)
    {
        param.NonAhrSchedule[i].ConfigType = 1;
        memcpy(&param.NonAhrSchedule[i].Dci, &param.AhrSchedule.Dci, sizeof(param.NonAhrSchedule[i].Dci));
        memcpy(&param.NonAhrSchedule[i].Npdcch, &param.AhrSchedule.Npdcch, sizeof(param.NonAhrSchedule[i].Npdcch));
        memcpy(&param.NonAhrSchedule[i].Npdsch, &param.AhrSchedule.Npdsch, sizeof(param.NonAhrSchedule[i].Npdsch));
    }
}
