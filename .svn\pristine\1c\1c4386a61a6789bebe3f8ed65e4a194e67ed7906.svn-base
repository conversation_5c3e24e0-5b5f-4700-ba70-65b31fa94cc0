#define _CRT_SECURE_NO_DEPRECATE

#include "includeall.h"
#include <algorithm>
#include <fstream>

using namespace std;

static stStringIndex StrIndex_array[] =
{
    { eumKw_Desc,           strDescription },
    { eumKw_DataCnt,        strSampleCount },
    { eumKw_SampleFreq,     strSampleFreq },
    { enumKw_DataType,      strDataType },
    { enumKw_RFGain,        strRFGain },
    { enumKw_CenterFreq,    strCenterFreq },
    { enumKw_IQGainImb,     strIQGainImb },
    { enumKw_IQPhaseImb,    strIQPhaseImb },
    { enumKw_DC_Offset_I,   strDCOffset_I },
    { enumKw_DC_Offset_Q,   strDCOffset_Q },
    { enumKw_Time_Skew,     strTime_skew },

    { enumKw_ExtAttEnable,  strExtAttEnable },
    { enumKw_ModType,       strModType },
    { enumKw_ExtAtt,        strExtAtt },
    { enumKw_TriggerLevel,  strTriggerLevel },
    { enumKw_VsaAmpl,       strVsaAmpl },
    { enumKw_FreqOffset,    strFreqOffset },
    { enumKw_Flag8080,      strFlag8080 },
    { enumKw_Scene,         strScene },
    { enumKw_ClockRate ,    strClockRate},
    { enumKw_PNStructSize , strPNStructSize },
    { enumKw_PNStructVer ,  strPNStructVer },
    { enumKw_PNStructData , strPNStructData },
    { enumKw_Gap,           strGap},
    { enumKw_Encoding,      strEncodingTag },
    { enumKw_IFG,           strIFG },
    { enumKw_Repeat,        strRepeat },
    { enumKw_PnHead,        strPnHead },
    { enumKw_PnTail,        strPnTail },
    { enumKw_PnIndex,       strPnIndex }
};

s32 WaveForm_CSV::GetPNInfoFromFileMIMO(const char *fileName, s32 index, stPNFileInfo *pResult, int pnMode)
{
    char u8Buf[MaxLineBuffer] = { 0 };
    FILE *pFile = nullptr;
    s32 u32LineNum = 0;
    s32 u32DatNum = 0;
    s32 responseNum = 0;
	s32 ResponseStarted = 0;

    s32     mindex = 0;
    s32     FlagStart = 0;
    s32     FlagEnd = 0;
    double tmpResponse = 0;
    const char *newLine = "\r\n";
    int iRet = WT_ERR_CODE_OK;
    int PnStructSize = 0;
    bool insertDataFlag = false;
    int Encoding = 0;
    dword IQ[2];
    pResult->Repeat = 1; //默认值。
    pResult->PnTail = 0.0; //默认值。
    pResult->PnHead = 0.0; //默认值。
    pResult->PnIfg = 0.0;
    do
    {
        pFile = fopen(fileName, "rb");
        if (nullptr == pFile)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        while (fgets(u8Buf, MaxLineBuffer, pFile))
        {
            u32LineNum++;
            if (u32LineNum > 3)
            {
                break;
            }
        }

        if ((0 == u32LineNum))
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        //
        fseek(pFile, 0L, SEEK_SET);
        pResult->freqOffset = 0;
        pResult->vsaSourceFalg = FALSE;
        while (nullptr != fgets(u8Buf, MaxLineBuffer, pFile))       // fgets获取的字符串一定以'\0'结束
        {
            s32 i = 0;

            if (FlagStart == 0)
            {
                if (memcmp(u8Buf, strFileInfo, strlen(strFileInfo)))
                {
                    continue;
                }
                if (index > mindex)
                {
                    mindex++;
                    continue;
                }
                FlagStart = 1;
                continue;
            }
            else if (FlagEnd == 0)
            {
                if (memcmp(u8Buf, strFileInfo, strlen(strFileInfo)) == 0)
                {
                    FlagEnd = 1;
                    break;
                }
            }
            if (0 == Encoding && 2 == sscanf(u8Buf, "%lg,%lg", &pResult->data[u32DatNum].dReal, &pResult->data[u32DatNum].dImag))
            {
                u32DatNum++;
                if (pResult->u32DatCnt != 0)
                {
                    if (pResult->u32DatCnt <= u32DatNum)
                    {
                        break;
                    }
                }
                else if (u32DatNum >= WaveFileCommon::Instance().MaxSampleCount())
                {
                    break;
                }
            }
            else if (1 == Encoding &&
                     4 == sscanf(u8Buf, "%d,%d,%d,%d",
                                 &IQ[0].value_32[0], &IQ[0].value_32[1],
                                 &IQ[1].value_32[0], &IQ[1].value_32[1]))
            {
                // If double is NaN or Infinite, a direct equal will cause loss of precision. So we use the memcpy method
                memcpy(&pResult->data[u32DatNum].dReal, &IQ[0].value_64, sizeof(double));
                memcpy(&pResult->data[u32DatNum].dImag, &IQ[1].value_64, sizeof(double));

                u32DatNum++;
                if (pResult->u32DatCnt != 0)
                {
                    if (pResult->u32DatCnt <= u32DatNum)
                    {
                        break;
                    }
                }
                else if (u32DatNum >= WaveFileCommon::Instance().MaxSampleCount())
                {
                    break;
                }
            }
            else if (1 == sscanf(u8Buf, "%lf", &tmpResponse))
            {
			    if (ResponseStarted == 0)
                {
                    if (responseNum >= MAX_BB_RESPONSE_SIZE)
                    {
                        break;
                    }
                    pResult->bb_response.Response[responseNum] = tmpResponse;
                    pResult->bb_response.FreqCount = responseNum + 1;
                }
			    else if(ResponseStarted == 1)
                {
                    if (responseNum >= MAX_RF_RESPONSE_SIZE)
                    {
                        break;
                    }
                    pResult->rf_response.Response[responseNum] = tmpResponse;
                    pResult->rf_response.FreqCount = responseNum + 1;
                }
				else if (ResponseStarted == 2)
				{
				    if (responseNum >= MAX_NS_RESPONSE_SIZE)
				    {
					    break;
				    }
				    pResult->ns_response.Response[responseNum] = tmpResponse;
				    pResult->ns_response.FreqCount = responseNum + 1;
				}
                responseNum++;
            }
            else
            {
                char cTmp[MaxLineBuffer] = { 0 };
                char *pCh = nullptr;
                int j = 0;

                if (0 == strlen(u8Buf))
                {
                    break;
                }
                for (j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }// for
                j = strlen(u8Buf);

                // 统一以','结尾，除非 (j == MaxLineBuffer - 1)
                if (j > 0 && j < MaxLineBuffer - 1)
                {
                    u8Buf[j] = ',';
                }
                else
                {
                    continue;
                }

                pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(u8Buf, "%[^,]", cTmp);    // 把第一个','之前的数据装在cTmp里面
                for (i = 0; i < sizeof(StrIndex_array) / sizeof(stStringIndex); i++)
                {
                    u32 bGetKeyWord = 1;
                    if (0 == strcmp(StrIndex_array[i].strDesc, cTmp))
                    {
                        switch (i)
                        {
                        case eumKw_DataCnt:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 s32DatLen = 0;
                                if (1 == sscanf(pCh, "%d,", &s32DatLen))
                                {
                                    if (s32DatLen > WaveFileCommon::Instance().MaxSampleCount())
                                    {
                                        s32DatLen = WaveFileCommon::Instance().MaxSampleCount();
                                    }
                                    pResult->u32DatCnt = s32DatLen;
                                }
                            }
                            break;

                        case eumKw_SampleFreq:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                FP64 f64Freq = 80;
                                string tmpCh = pCh;
                                transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::toupper);
                                bool isMHz = true;
                                if (tmpCh.find("MHZ") == string::npos)
                                {
                                    isMHz = false;
                                }
                                if (1 == sscanf(pCh, "%lf,", &f64Freq))
                                {
                                    pResult->SampleFreq = f64Freq;
                                    if (!isMHz || pResult->SampleFreq > (1*MHz_API + 0.001))
                                    {
                                        pResult->SampleFreq /= MHz_API;
                                    }
                                }
                                // 把频率数与单位MHz之间的','改为' '
                                pCh = strchr(pCh, ',');
                                if (nullptr != pCh)
                                {
                                    *pCh = ' ';
                                }
                            }
                            break;

                        case enumKw_DataType:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                string tmpCh = pCh;
                                transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::tolower);

                                pResult->DataType = enDataFormat_Float64;
                                if (0 == strncmp(tmpCh.c_str(), strInt16, strlen(strInt16)))
                                {
                                    pResult->DataType = enDataFormat_Int16;
                                }
                            }
                            break;

                        case enumKw_RFGain:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double gain = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &gain))
                                {
                                    pResult->RFGain = gain;
                                }
                                // 把增益值与单位之间的','转变为' '
                                pCh = strchr(pCh, ',');
                                if (nullptr != pCh)
                                {
                                    *pCh = ' ';
                                }
                                //以信号文件有误RF Gain信息来确定是否为VSA保存的信号
                                pResult->vsaSourceFalg = TRUE;
                            }
                            break;

                        case enumKw_CenterFreq:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double dFreq = DefaultCenterFreq;

                                if (1 == sscanf(pCh, "%lf,", &dFreq))
                                {
                                    pResult->dCenterFreq = dFreq;
                                }
                            }
                            break;

                        case enumKw_FreqOffset:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double freqOffset = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &freqOffset))
                                {
                                    pResult->freqOffset = freqOffset;
                                }
                            }
                            break;

                        case enumKw_IQGainImb:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double dImb = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &dImb))
                                {
                                    pResult->IQGainImb = dImb;
                                }
                            }
                            break;
                        case enumKw_IQPhaseImb:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double dImb = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &dImb))
                                {
                                    pResult->IQPhaseImb = dImb;
                                }
                            }
                            break;

                        case enumKw_DC_Offset_I:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double offset_I = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &offset_I))
                                {
                                    pResult->DC_Offset_I = offset_I;
                                }
                            }
                            break;

                        case enumKw_DC_Offset_Q:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double offset_Q = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &offset_Q))
                                {
                                    pResult->DC_Offset_Q = offset_Q;
                                }
                            }
                            break;
                        case enumKw_Time_Skew:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double timeSkew = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &timeSkew))
                                {
                                    pResult->timeSkew = timeSkew;
                                }
                            }
                            break;
                        case enumKw_ExtAttEnable:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 attEnable = 0;

                                if (1 == sscanf(pCh, "%d,", &attEnable))
                                {
                                    pResult->ExtAttEnable = attEnable;
                                }
                            }
                            break;
                        case enumKw_ModType:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 modeType = 0;

                                if (1 == sscanf(pCh, "%d,", &modeType))
                                {
                                    pResult->ModType = modeType;
                                }
                            }
                            break;
                        case enumKw_TriggerLevel:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double triggerlevel = 0;

                                if (1 == sscanf(pCh, "%lf,", &triggerlevel))
                                {
                                    pResult->triggerLevel = triggerlevel;
                                }
                            }
                            break;
                        case enumKw_Flag8080:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 flag8080 = 0;

                                if (1 == sscanf(pCh, "%d,", &flag8080))
                                {
                                    pResult->flag8080 = flag8080;
                                }
                            }
                            break;
                        case enumKw_Scene:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 scene = 0;

                                if (1 == sscanf(pCh, "%d,", &scene))
                                {
                                    pResult->sceneMode = scene;
                                }
                            }
                            break;
                        case enumKw_ExtAtt:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double extAtt = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &extAtt))
                                {
                                    pResult->ExtAtt = extAtt;
                                }
                            }
                            break;
                        case enumKw_ClockRate:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                int clockRate = 1;
                                if (1 == sscanf(pCh, "%d,", &clockRate))
                                {
                                    pResult->clockRate = clockRate;
                                }
                            }
                            break;
                        case enumKw_PNStructSize:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                sscanf(pCh, "%d,", &PnStructSize);
                            }
                            break;
                        case enumKw_PNStructVer:
                            break;
                        case enumKw_PNStructData:
                            if (PnStructSize > 0)
                            {
                                fseek(pFile, PnStructSize + strlen(newLine), SEEK_CUR);
                            }
                            break;
                        case enumKw_Gap:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double gap = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &gap))
                                {
                                    if (fabs(gap) < 1e-6)
                                    {
                                        insertDataFlag = true;
                                    }
                                }
                            }
                            break;
                        case enumKw_Encoding:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                sscanf(pCh, "%d,", &Encoding);
                            }
                            break;
                        default:
                            break;
                        }// switch
                    }//  if
                    else
                    {
                        bGetKeyWord = 0;
                    }
                    if (bGetKeyWord != 0)
                    {
                        break;
                    }
                } // for

                if (0 == strncmp(strRfFreqStart, u8Buf, strlen(strRfFreqStart)))
                {
                    responseNum = 0;
				    ResponseStarted = 1;
                    continue;
                }
				else if (0 == strncmp(strNSFreqStart, u8Buf, strlen(strNSFreqStart)))
				{
				    responseNum = 0;
					ResponseStarted = 2;
				    continue;
				}

                if ((0 == strncmp(strFileInfo, u8Buf, strlen(strFileInfo))) ||
                    (0 == strncmp(strStartData, u8Buf, strlen(strStartData))) ||
                    (0 == strncmp(strPNStructVer, u8Buf, strlen(strPNStructVer))) ||
                    (0 == strncmp(strPNStructSize, u8Buf, strlen(strPNStructSize))) ||
                    (0 == strncmp(strPNStructData, u8Buf, strlen(strPNStructData))))
                {
                    // 不显示 strFileInfo 和 strStartData
                    continue;
                }

                for (i = 0; i < sizeof(StrIndex_array) / sizeof(stStringIndex); i++)
                {
                    if (enumKw_DataType == StrIndex_array[i].index)
                    {
                        break;
                    }
                }
                if (0 == strncmp(StrIndex_array[i].strDesc, u8Buf, strlen(StrIndex_array[i].strDesc)))
                {
                    // 不显示 enumKw_Type
                    continue;
                }

                // 去掉尾部','并在尾部添加"\r\n"
                if (',' == u8Buf[strlen(u8Buf) - 1])
                {
                    u8Buf[strlen(u8Buf) - 1] = 0;
                }

                if (strlen(u8Buf) + strlen(newLine) < MaxLineBuffer)
                {
                    strcat(u8Buf, newLine);
                }
                else
                {
                    u8Buf[MaxLineBuffer - 3] = '\r';
                    u8Buf[MaxLineBuffer - 2] = '\n';
                    u8Buf[MaxLineBuffer - 1] = 0;
                }


                // 将首个','替换为':'
                pCh = strchr(u8Buf, ',');
                if (nullptr != pCh)
                {
                    *pCh = ':';
                }

                if (pResult->descreption)
                {
                    if ((strlen(u8Buf) + 1) < (MaxDescLen - strlen(pResult->descreption)))
                    {
                        strcat(pResult->descreption, u8Buf);
                    }
                }
            }// else
            memset(u8Buf, 0, sizeof(u8Buf));
        } // while

        if (0 == u32DatNum)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }
        //处理奇偶数，matlab格式的在读取回来后在归一化时会处理
        if ((u32DatNum & 1) != 0)
        {
            u32DatNum -= 1;
        }

        if (pResult->u32DatCnt == 0)
        {
            pResult->u32DatCnt = u32DatNum;
        }
        pResult->SampleCount = u32DatNum;
        pResult->u32InterpCnt = u32DatNum * sizeof(stPNDat);
        pResult->desLen = strlen(pResult->descreption);
        pResult->FrameStart = 0;
        pResult->FrameEnd = pResult->SampleCount;

        if (Encoding)
        {
            WaveFileCommon::Instance().ScramblerData(pResult, false);
        }

        if (insertDataFlag && (pnMode & PN_VSA_SCENE_MODE))
        {
            WaveFileCommon::Instance().InsertGapData(pResult);
        }
        iRet = WT_ERR_CODE_OK;
    } while (0);

    if (pFile)
    {
        fclose(pFile);
    }

    return iRet;
}

WaveForm_CSV::WaveForm_CSV()
{
}

WaveForm_CSV::~WaveForm_CSV()
{
}

s32 WaveForm_CSV::GetPnStructData(const char * fileName, void *data, int len)
{
    char u8Buf[MaxLineBuffer] = { 0 };
    FILE *pFile = nullptr;
    s32 u32LineNum = 0;
    int PnStructSize = 0;

    const char *newLine = "\r\n";
    int iRet = WT_ERR_CODE_OK;
    do
    {
        pFile = fopen(fileName, "rb");
        if (nullptr == pFile)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        while (fgets(u8Buf, MaxLineBuffer, pFile))
        {
            u32LineNum++;
            if (u32LineNum > 3)
            {
                break;
            }
        }

        if ((0 == u32LineNum))
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        //
        fseek(pFile, 0L, SEEK_SET);

        while (nullptr != fgets(u8Buf, MaxLineBuffer, pFile))       // fgets获取的字符串一定以'\0'结束
        {
            if (0 == strncmp(u8Buf, strPNStructSize, strlen(strPNStructSize)))
            {
                for (int j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }

                if (strlen(u8Buf) < 1)
                {
                    continue;
                }

                char *pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(pCh, "%d", &PnStructSize);
            }
            else if (0 == strncmp(u8Buf, strPNStructData, strlen(strPNStructData)))
            {
                for (int j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }

                fread(data, 1, (len > PnStructSize ? PnStructSize : len), pFile);
                iRet = WT_ERR_CODE_OK;

                break;
            }
            memset(u8Buf, 0, sizeof(u8Buf));
        } // while
    } while (0);

    if (pFile)
    {
        fclose(pFile);
    }

    return iRet;
}

s32 WaveForm_CSV::GetPnStructSize(const char * fileName, int *len)
{
    char u8Buf[MaxLineBuffer] = { 0 };
    FILE *pFile = nullptr;
    s32 u32LineNum = 0;

    const char *newLine = "\r\n";
    int iRet = WT_ERR_CODE_OK;
    do
    {
        pFile = fopen(fileName, "rb");
        if (nullptr == pFile)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        while (fgets(u8Buf, MaxLineBuffer, pFile))
        {
            u32LineNum++;
            if (u32LineNum > 3)
            {
                break;
            }
        }

        if ((0 == u32LineNum))
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        //
        fseek(pFile, 0L, SEEK_SET);

        while (nullptr != fgets(u8Buf, MaxLineBuffer, pFile))       // fgets获取的字符串一定以'\0'结束
        {
            if(0 == strncmp(u8Buf, strPNStructSize, strlen(strPNStructSize)))
            {
                for (int j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }

                if (strlen(u8Buf) < 1)
                {
                    continue;
                }

                char *pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(pCh, "%d", len);
                iRet = WT_ERR_CODE_OK;
                break;
            }
            memset(u8Buf, 0, sizeof(u8Buf));
        } // while
    } while (0);

    if (pFile)
    {
        fclose(pFile);
    }

    return iRet;
}

s32 WaveForm_CSV::ConvertFrom_CSVMIMO(const char *fileName, s32 freq, s32 index, stPNFileInfo *pResult, int pnMode)
{
    s32 result = GetPNInfoFromFileMIMO(fileName, index, pResult, pnMode);

    return result;
}

s32 WaveForm_CSV::CatenateMutiPnCsvFiles(const MutiPNCatenateInfo *catenateInfo)
{
    int i = 0;
    ofstream fout(string(catenateInfo->fileName), ios::out | ios::binary);
    if (!fout.is_open())
    {
        return WT_ERR_CODE_FILE_OPERATE_FAIL;
    }

    ifstream fin;
    string tmp;
    stringstream mutiPnHead;
    size_t pos;
    const char *sperator = ",";
    mutiPnHead << strStreamListInfo << nextLine;
    mutiPnHead << strPnCount << sperator << catenateInfo->srcFilesNum << nextLine;
    
    for (i = 0; i < catenateInfo->srcFilesNum; i++)
    {
        mutiPnHead << strPnStreamCount << sperator << catenateInfo->streamCnt[catenateInfo->index[i]] << nextLine;
    }
    fout << mutiPnHead.str() << nextLine;

    string str;
    for (i = 0; i < catenateInfo->srcFilesNum; i++)
    {
        str = "/tmp/tmpwave/" + string(catenateInfo->srcPnFiles[catenateInfo->index[i]]);
        fin.open(str, ios::in | ios::binary);
        if (!fin.is_open())
        {
            fout.close();
            return WT_ERR_CODE_FILE_OPERATE_FAIL;
        }

        while (getline(fin, tmp))
        {
            pos = tmp.find_last_not_of('\r');
            if (pos != string::npos)
            {
                tmp = tmp.substr(0, pos + 1);
            }
            if (strstr(tmp.c_str(), strPnIndex))
            {
                pos = tmp.find_last_of(",");
                if (pos != string::npos)
                {
                    tmp = tmp.substr(0, pos + 1) + to_string(i + 1);
                }
            }
            tmp += nextLine;
            fout << tmp;
        }
        fout << nextLine;
        fin.close();
    }

    fout.close();
    return WT_ERR_CODE_OK;
}

s32 WaveForm_CSV::ConvertFrom_CSVMIMO(const char *fileName, s32 freq, s32 StreamIndex, s32 PnIndex, stPNFileInfo *pResult, int pnMode)
{
    s32 result = GetPNInfoFromFileMIMO(fileName, StreamIndex, PnIndex, pResult, pnMode);

    return result;
}

s32 WaveForm_CSV::GetPNCountFromFile(const char *fileName, s32 &RetPnCount, int *PnOrder)
{
    FILE *pFile = nullptr;
    int iRet = WT_ERR_CODE_OK;
    char u8Buf[MaxLineBuffer] = { 0 };
    s32 u32LineNum = 0;
    s32 FlagListInfo = 0;
    RetPnCount = 0;

    do
    {
        pFile = fopen(fileName, "rb");
        if (nullptr == pFile)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }
        
        while (fgets(u8Buf, MaxLineBuffer, pFile))
        {
            u32LineNum++;
            if (u32LineNum > 3)
            {
                break;
            }
        }
        if ((0 == u32LineNum))
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }
        fseek(pFile, 0L, SEEK_SET);

        while (nullptr != fgets(u8Buf, MaxLineBuffer, pFile))       // fgets获取的字符串一定以'\0'结束
        {
            if (FlagListInfo == 0)
            {
                if (memcmp(u8Buf, strStreamListInfo, strlen(strStreamListInfo)))
                {
                    continue;
                }
                FlagListInfo = 1;
                continue;
            }
            else
            {
                char cTmp[MaxLineBuffer] = { 0 };
                char *pCh = nullptr;
                int j = 0;

                if (0 == strlen(u8Buf))
                {
                    break;
                }
                for (j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }// for
                j = strlen(u8Buf);

                // 统一以','结尾，除非 (j == MaxLineBuffer - 1)
                if (j > 0 && j < MaxLineBuffer - 1)
                {
                    u8Buf[j] = ',';
                }
                else
                {
                    continue;
                }

                pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(u8Buf, "%[^,]", cTmp);    // 把第一个','之前的数据装在cTmp里面
                if (0 == strcmp(strPnCount, cTmp) && 0 != *pCh)
                {
                    s32 PnCount = 0;
                    if (1 == sscanf(pCh, "%d,", &PnCount))
                    {
                        RetPnCount = PnCount;
                        break;
                    }
                }
                else
                {
                    continue;
                }
            }
        }
        memset(PnOrder, 0, RetPnCount);
        for(int i = 0;i < RetPnCount; i++)
        {
            PnOrder[i] = i;
            s32 CurPnIndex = 0;
            s32 CurStreamIndex = 0;
            s32 FlagStart = 0;
            s32 mindex = 0;
            fseek(pFile, 0L, SEEK_SET);
            while (nullptr != fgets(u8Buf, MaxLineBuffer, pFile))       // fgets获取的字符串一定以'\0'结束
            {
                char cTmp[MaxLineBuffer] = { 0 };
                char *pCh = nullptr;
                int j = 0;

                if (0 == strlen(u8Buf))
                {
                    break;
                }
                for (j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }// for
                j = strlen(u8Buf);

                // 统一以','结尾，除非 (j == MaxLineBuffer - 1)
                if (j > 0 && j < MaxLineBuffer - 1)
                {
                    u8Buf[j] = ',';
                }
                else
                {
                    continue;
                }

                pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(u8Buf, "%[^,]", cTmp);    // 把第一个','之前的数据装在cTmp里面

                if (CurPnIndex < i)
                {
                    if(0 == strcmp(strPnStreamCount, cTmp) && 0 != *pCh)
                        {
                        s32 PnStreamCount = 0;
                        if (1 == sscanf(pCh, "%d,", &PnStreamCount))
                        {
                            CurStreamIndex += PnStreamCount;
                            CurPnIndex++;
                        }
                        }
                    else
                    {
                        continue;
                    }
                }
                else if (FlagStart == 0)
                {
                    if (memcmp(u8Buf, strFileInfo, strlen(strFileInfo)))
                    {
                        continue;
                    }
                    if (CurStreamIndex > mindex)
                    {
                        mindex++;
                        continue;
                    }
                    FlagStart = 1;
                    continue;
                }
                else
                {
                    if(0 == strcmp(strPnIndex, cTmp) && 0 != *pCh)
                        {
                            s32 PnIndex = 1;
                            if (1 == sscanf(pCh, "%d,", &PnIndex))
                            {
                                PnOrder[i]= PnIndex;
                                break;
                            }
                        }
                    else
                    {
                        continue;
                    }
                }
            }
        }
    }while (0);
    if (pFile)
    {
        fclose(pFile);
    }
    return iRet;
}

s32 WaveForm_CSV::SaveCsvFile(stPNFileInfo *pResult, const char *fileName, s32 streamIndex)
{
    FILE *wfp_WT = nullptr;
    const int bufSize = 1024*32;//32K
    const int tail = 256;
    const int totalSize = bufSize + tail;
    unique_ptr<char[]> tmpBuf(new char[totalSize]);
    int index = 0;

    memset(tmpBuf.get(), 0, totalSize);

    if ((nullptr == pResult)
        || (nullptr == pResult->data))
    {
        return WT_ERR_CODE_FILE_OPERATE_FAIL;
    }

    if (0 == streamIndex)
    {
        wfp_WT = fopen(fileName, "wb");
    }
    else
    {
        wfp_WT = fopen(fileName, "ab");
    }

    if (nullptr == wfp_WT)
    {
        return WT_ERR_CODE_FILE_OPERATE_FAIL;
    }

    WaveFileCommon::Instance().ScramblerData(pResult, true);

    stringstream msg;
    const char *sperator = ",";
    string str("");
    WaveFileCommon::Instance().WriteDescriptionTo(str, sperator, pResult);
    //写入数据
    msg << str;
    fprintf(wfp_WT, "%s", msg.str().c_str());

    if (0 == streamIndex)
    {
        if (0)
        {
            void *data = nullptr;
            int len = 0;
            WaveFileCommon::Instance().WritePnStruct(pResult, &data, &len);

            if (len > 0)
            {
                msg.str("");
                msg << strPNStructVer << sperator << 1 << nextLine;
                msg << strPNStructSize << sperator << len << nextLine;
                msg << strPNStructData << nextLine;
                fprintf(wfp_WT, "%s", msg.str().c_str());
                fwrite(data, 1, len, wfp_WT);
                fprintf(wfp_WT, "%s", nextLine);
            }
        }

    }

    fprintf(wfp_WT, "%s%s", strStartData, nextLine);

    if (WaveFileCommon::Instance().Encoding())
    {
        dword IQ[2];
        for (int i = 0; i < pResult->SampleCount; i++)
        {
			// If double is NaN or Infinite, a direct equal will cause loss of precision. So we use the memcpy method
            memcpy(&IQ[0].value_64, &pResult->data[i].dReal, sizeof(double));
            memcpy(&IQ[1].value_64, &pResult->data[i].dImag, sizeof(double));

            index += sprintf(tmpBuf.get() + index, "%d%s%d%s%d%s%d%s",
                    IQ[0].value_32[0], sperator,
                    IQ[0].value_32[1], sperator,
                    IQ[1].value_32[0], sperator,
                    IQ[1].value_32[1], nextLine);
            if (index >= (bufSize - tail) || (i + 1) == pResult->SampleCount)
            {
                fwrite(tmpBuf.get(), index, 1, wfp_WT);
                memset(tmpBuf.get(), 0, sizeof(tmpBuf));
                index = 0;
            }
        }
    }
    else
    {
        if (enDataFormat_Int16 == pResult->DataType)
        {
            for (int i = 0; i < pResult->SampleCount; i++)
            {
                index += sprintf(tmpBuf.get() + index, "%d%s%d%s", (s16)pResult->data[i].dReal, sperator, (s16)pResult->data[i].dImag, nextLine);
                if (index >= (bufSize - tail) || (i + 1) == pResult->SampleCount)
                {
                    fwrite(tmpBuf.get(), index, 1, wfp_WT);
                    memset(tmpBuf.get(), 0, sizeof(tmpBuf));
                    index = 0;
                }
            }
        }
        else
        {
            for (int i = 0; i < pResult->SampleCount; i++)
            {
                index += sprintf(tmpBuf.get() + index, "%lg%s%lg%s", pResult->data[i].dReal, sperator, pResult->data[i].dImag, nextLine);
                if (index >= (bufSize - tail) || (i + 1) == pResult->SampleCount)
                {
                    fwrite(tmpBuf.get(), index, 1, wfp_WT);
                    memset(tmpBuf.get(), 0, totalSize);
                    index = 0;
                }
            }
        }
    }

    fclose(wfp_WT);
    return WT_ERR_CODE_OK;
}

s32 WaveForm_CSV::GetPNInfoFromFileMIMO(const char *fileName, s32 StreamIndex, s32 PnIndex, stPNFileInfo *pResult, int pnMode)
{
    FILE *pFile = nullptr;
    int iRet = WT_ERR_CODE_OK;
    char u8Buf[MaxLineBuffer] = { 0 };
    s32 u32LineNum = 0;
    s32 u32DatNum = 0;
    s32 responseNum = 0;
    s32 ResponseStarted = 0;
    
    s32     FlagListInfo = 0;
    s32     CurStreamIndex = StreamIndex;
    s32     CurPnIndex = PnIndex;
    s32     FlagStart = 0;
    s32     mindex = 0;
    s32     FlagEnd = 0;
    double tmpResponse = 0;
    const char *newLine = "\r\n";
    int Encoding = 0;
    int PnStructSize = 0;
    bool insertDataFlag = false;
    dword IQ[2];
    pResult->Repeat = 1; //默认值。
    pResult->PnTail = 0.0; //默认值。
    pResult->PnHead = 0.0; //默认值。
    pResult->PnIfg = 0.0;
    do
    {
        pFile = fopen(fileName, "rb");
        if (nullptr == pFile)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }
        
        while (fgets(u8Buf, MaxLineBuffer, pFile))
        {
            u32LineNum++;
            if (u32LineNum > 3)
            {
                break;
            }
        }
        if ((0 == u32LineNum))
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }
        fseek(pFile, 0L, SEEK_SET);

        while (nullptr != fgets(u8Buf, MaxLineBuffer, pFile))       // fgets获取的字符串一定以'\0'结束
        {
            s32 i = 0;
            if (FlagListInfo == 0)
            {
                if (memcmp(u8Buf, strStreamListInfo, strlen(strStreamListInfo)))
                {
                    continue;
                }
                FlagListInfo = 1;
                continue;
            }
            else if (CurPnIndex > 1)//PN排序范围1~100
            {
                char cTmp[MaxLineBuffer] = { 0 };
                char *pCh = nullptr;
                int j = 0;

                if (0 == strlen(u8Buf))
                {
                    break;
                }
                for (j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }// for
                j = strlen(u8Buf);

                // 统一以','结尾，除非 (j == MaxLineBuffer - 1)
                if (j > 0 && j < MaxLineBuffer - 1)
                {
                    u8Buf[j] = ',';
                }
                else
                {
                    continue;
                }

                pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(u8Buf, "%[^,]", cTmp);    // 把第一个','之前的数据装在cTmp里面
                if(0 == strcmp(strPnStreamCount, cTmp) && 0 != *pCh)
                    {
                    s32 PnStreamCount = 0;
                    if (1 == sscanf(pCh, "%d,", &PnStreamCount))
                    {
                        CurStreamIndex += PnStreamCount;
                        CurPnIndex--;
                    }
                    }
                else
                {
                    continue;
                }
            }
            else if (CurPnIndex == 1)//PN排序范围1~100
            {
                char cTmp[MaxLineBuffer] = { 0 };
                char *pCh = nullptr;
                int j = 0;

                if (0 == strlen(u8Buf))
                {
                    break;
                }
                for (j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }// for
                j = strlen(u8Buf);

                // 统一以','结尾，除非 (j == MaxLineBuffer - 1)
                if (j > 0 && j < MaxLineBuffer - 1)
                {
                    u8Buf[j] = ',';
                }
                else
                {
                    continue;
                }

                pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(u8Buf, "%[^,]", cTmp);    // 把第一个','之前的数据装在cTmp里面
                if(0 == strcmp(strPnStreamCount, cTmp) && 0 != *pCh)
                    {
                    s32 PnStreamCount = 0;
                        if (1 == sscanf(pCh, "%d,", &PnStreamCount))
                        {
                            if(StreamIndex + 1 > PnStreamCount)
                            {
                                break;
                            }
                            else
                            {
                                CurPnIndex = 0;
                            }
                        }
                    }
                else
                {
                    continue;
                }
            }
            else if (FlagStart == 0)
            {
                if (memcmp(u8Buf, strFileInfo, strlen(strFileInfo)))
                {
                    continue;
                }
                if (CurStreamIndex > mindex)
                {
                    mindex++;
                    continue;
                }
                FlagStart = 1;
                continue;
            }
            else if (FlagEnd == 0)
            {
                if (memcmp(u8Buf, strFileInfo, strlen(strFileInfo)) == 0)
                {
                    FlagEnd = 1;
                    break;
                }
            }
            if (0 == Encoding && 2 == sscanf(u8Buf, "%lg,%lg", &pResult->data[u32DatNum].dReal, &pResult->data[u32DatNum].dImag))
            {
                u32DatNum++;
                if (pResult->u32DatCnt != 0)
                {
                    if (pResult->u32DatCnt <= u32DatNum)
                    {
                        break;
                    }
                }
                else if (u32DatNum >= WaveFileCommon::Instance().MaxSampleCount())
                {
                    break;
                }
            }
            else if (1 == Encoding &&
                     4 == sscanf(u8Buf, "%d,%d,%d,%d",
                                 &IQ[0].value_32[0], &IQ[0].value_32[1],
                                 &IQ[1].value_32[0], &IQ[1].value_32[1]))
            {
                // If double is NaN or Infinite, a direct equal will cause loss of precision. So we use the memcpy method
                memcpy(&pResult->data[u32DatNum].dReal, &IQ[0].value_64, sizeof(double));
                memcpy(&pResult->data[u32DatNum].dImag, &IQ[1].value_64, sizeof(double));

                u32DatNum++;
                if (pResult->u32DatCnt != 0)
                {
                    if (pResult->u32DatCnt <= u32DatNum)
                    {
                        break;
                    }
                }
                else if (u32DatNum >= WaveFileCommon::Instance().MaxSampleCount())
                {
                    break;
                }
            }
            else if (1 == sscanf(u8Buf, "%lf", &tmpResponse))
            {
			    if (ResponseStarted == 0)
                {
                    if (responseNum >= MAX_BB_RESPONSE_SIZE)
                    {
                        break;
                    }
                    pResult->bb_response.Response[responseNum] = tmpResponse;
                    pResult->bb_response.FreqCount = responseNum + 1;
                }
			    else if(ResponseStarted == 1)
                {
                    if (responseNum >= MAX_RF_RESPONSE_SIZE)
                    {
                        break;
                    }
                    pResult->rf_response.Response[responseNum] = tmpResponse;
                    pResult->rf_response.FreqCount = responseNum + 1;
                }
				else if (ResponseStarted == 2)
				{
				    if (responseNum >= MAX_NS_RESPONSE_SIZE)
				    {
					    break;
				    }
				    pResult->ns_response.Response[responseNum] = tmpResponse;
				    pResult->ns_response.FreqCount = responseNum + 1;
				}
                responseNum++;
            }
            else
            {
                char cTmp[MaxLineBuffer] = { 0 };
                char *pCh = nullptr;
                int j = 0;

                if (0 == strlen(u8Buf))
                {
                    break;
                }
                for (j = strlen(u8Buf) - 1; j >= 0; j--)
                {
                    if (('\n' == u8Buf[j]) || ('\r' == u8Buf[j]) || ('\t' == u8Buf[j]) || (',' == u8Buf[j]))
                    {
                        u8Buf[j] = 0;
                    }
                    else
                    {
                        break;
                    }
                }// for
                j = strlen(u8Buf);

                // 统一以','结尾，除非 (j == MaxLineBuffer - 1)
                if (j > 0 && j < MaxLineBuffer - 1)
                {
                    u8Buf[j] = ',';
                }
                else
                {
                    continue;
                }

                pCh = strchr(u8Buf, ',');       // 把指针移动到第一个','之后的数据
                if (nullptr == pCh)
                {
                    // u8Buf中没有',',忽略
                    continue;
                }
                pCh++;
                sscanf(u8Buf, "%[^,]", cTmp);    // 把第一个','之前的数据装在cTmp里面
                for (i = 0; i < sizeof(StrIndex_array) / sizeof(stStringIndex); i++)
                {
                    u32 bGetKeyWord = 1;
                    if (0 == strcmp(StrIndex_array[i].strDesc, cTmp))
                    {
                        switch (i)
                        {
                        case eumKw_DataCnt:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 s32DatLen = 0;
                                if (1 == sscanf(pCh, "%d,", &s32DatLen))
                                {
                                    if (s32DatLen > WaveFileCommon::Instance().MaxSampleCount())
                                    {
                                        s32DatLen = WaveFileCommon::Instance().MaxSampleCount();
                                    }
                                    pResult->u32DatCnt = s32DatLen;
                                }
                            }
                            break;

                        case eumKw_SampleFreq:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                FP64 f64Freq = 80;
                                string tmpCh = pCh;
                                transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::toupper);
                                bool isMHz = true;
                                if (tmpCh.find("MHZ") == string::npos)
                                {
                                    isMHz = false;
                                }
                                if (1 == sscanf(pCh, "%lf,", &f64Freq))
                                {
                                    pResult->SampleFreq = f64Freq;
                                    if (!isMHz || pResult->SampleFreq > (1*MHz_API + 0.001))
                                    {
                                        pResult->SampleFreq /= MHz_API;
                                    }
                                }
                                // 把频率数与单位MHz之间的','改为' '
                                pCh = strchr(pCh, ',');
                                if (nullptr != pCh)
                                {
                                    *pCh = ' ';
                                }
                            }
                            break;

                        case enumKw_DataType:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                string tmpCh = pCh;
                                transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::tolower);

                                pResult->DataType = enDataFormat_Float64;
                                if (0 == strncmp(tmpCh.c_str(), strInt16, strlen(strInt16)))
                                {
                                    pResult->DataType = enDataFormat_Int16;
                                }
                            }
                            break;

                        case enumKw_RFGain:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double gain = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &gain))
                                {
                                    pResult->RFGain = gain;
                                }
                                // 把增益值与单位之间的','转变为' '
                                pCh = strchr(pCh, ',');
                                if (nullptr != pCh)
                                {
                                    *pCh = ' ';
                                }
                                //以信号文件有误RF Gain信息来确定是否为VSA保存的信号
                                pResult->vsaSourceFalg = TRUE;
                            }
                            break;

                        case enumKw_CenterFreq:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double dFreq = DefaultCenterFreq;

                                if (1 == sscanf(pCh, "%lf,", &dFreq))
                                {
                                    pResult->dCenterFreq = dFreq;
                                }
                            }
                            break;

                        case enumKw_FreqOffset:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double freqOffset = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &freqOffset))
                                {
                                    pResult->freqOffset = freqOffset;
                                }
                            }
                            break;

                        case enumKw_IQGainImb:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double dImb = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &dImb))
                                {
                                    pResult->IQGainImb = dImb;
                                }
                            }
                            break;
                        case enumKw_IQPhaseImb:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double dImb = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &dImb))
                                {
                                    pResult->IQPhaseImb = dImb;
                                }
                            }
                            break;

                        case enumKw_DC_Offset_I:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double offset_I = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &offset_I))
                                {
                                    pResult->DC_Offset_I = offset_I;
                                }
                            }
                            break;

                        case enumKw_DC_Offset_Q:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double offset_Q = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &offset_Q))
                                {
                                    pResult->DC_Offset_Q = offset_Q;
                                }
                            }
                            break;
                        case enumKw_Time_Skew:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double timeSkew = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &timeSkew))
                                {
                                    pResult->timeSkew = timeSkew;
                                }
                            }
                            break;
                        case enumKw_ExtAttEnable:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 attEnable = 0;

                                if (1 == sscanf(pCh, "%d,", &attEnable))
                                {
                                    pResult->ExtAttEnable = attEnable;
                                }
                            }
                            break;
                        case enumKw_ModType:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 modeType = 0;

                                if (1 == sscanf(pCh, "%d,", &modeType))
                                {
                                    pResult->ModType = modeType;
                                }
                            }
                            break;
                        case enumKw_TriggerLevel:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double triggerlevel = 0;

                                if (1 == sscanf(pCh, "%lf,", &triggerlevel))
                                {
                                    pResult->triggerLevel = triggerlevel;
                                }
                            }
                            break;
                        case enumKw_Flag8080:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 flag8080 = 0;

                                if (1 == sscanf(pCh, "%d,", &flag8080))
                                {
                                    pResult->flag8080 = flag8080;
                                }
                            }
                            break;
                        case enumKw_Scene:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 scene = 0;

                                if (1 == sscanf(pCh, "%d,", &scene))
                                {
                                    pResult->sceneMode = scene;
                                }
                            }
                            break;
                        case enumKw_ExtAtt:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double extAtt = 0.0;

                                if (1 == sscanf(pCh, "%lf,", &extAtt))
                                {
                                    pResult->ExtAtt = extAtt;
                                }
                            }
                            break;
                        case enumKw_ClockRate:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                int clockRate = 1;
                                if (1 == sscanf(pCh, "%d,", &clockRate))
                                {
                                    pResult->clockRate = clockRate;
                                }
                            }
                            break;
                        case enumKw_PNStructSize:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                sscanf(pCh, "%d,", &PnStructSize);
                            }
                            break;
                        case enumKw_PNStructVer:
                            break;
                        case enumKw_PNStructData:
                            if (PnStructSize > 0)
                            {
                                fseek(pFile, PnStructSize + strlen(newLine), SEEK_CUR);
                            }
                            break;
                        case enumKw_Gap:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double gap = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &gap))
                                {
                                    if (fabs(gap) < 1e-6)
                                    {
                                        insertDataFlag = true;
                                    }
                                }
                            }
                            break;
                        case enumKw_Encoding:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                sscanf(pCh, "%d,", &Encoding);
                            }
                            break;
                        case enumKw_IFG:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double PnIfg = 0.0;
                                if (1 == sscanf(pCh, "%lf,", &PnIfg))
                                {
                                    pResult->PnIfg = PnIfg;
                                }
                            }
                            break;
                        case enumKw_Repeat:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                s32 Repeat = 0;
                                if (1 == sscanf(pCh, "%d,", &Repeat))
                                {
                                    pResult->Repeat = Repeat;
                                }
                            }
                            break;
                        case enumKw_PnHead:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double PnHead = 0;
                                if (1 == sscanf(pCh, "%lf,", &PnHead))
                                {
                                    pResult->PnHead = PnHead;
                                }
                            }
                            break;
                        case enumKw_PnTail:
                            if (0 == *pCh)
                            {
                                //字符串结尾
                                break;
                            }
                            else
                            {
                                double PnTail = 0;
                                if (1 == sscanf(pCh, "%lf,", &PnTail))
                                {
                                    pResult->PnTail = PnTail;
                                }
                            }
                            break;                          
                        default:
                            break;
                        }// switch
                    }//  if
                    else
                    {
                        bGetKeyWord = 0;
                    }
                    if (bGetKeyWord != 0)
                    {
                        break;
                    }
                } // for

                if (0 == strncmp(strRfFreqStart, u8Buf, strlen(strRfFreqStart)))
                {
                    responseNum = 0;
				    ResponseStarted = 1;
                    continue;
                }
				else if (0 == strncmp(strNSFreqStart, u8Buf, strlen(strNSFreqStart)))
				{
				    responseNum = 0;
					ResponseStarted = 2;
				    continue;
				}

                if ((0 == strncmp(strFileInfo, u8Buf, strlen(strFileInfo))) ||
                    (0 == strncmp(strStartData, u8Buf, strlen(strStartData))) ||
                    (0 == strncmp(strPNStructVer, u8Buf, strlen(strPNStructVer))) ||
                    (0 == strncmp(strPNStructSize, u8Buf, strlen(strPNStructSize))) ||
                    (0 == strncmp(strPNStructData, u8Buf, strlen(strPNStructData))))
                {
                    // 不显示 strFileInfo 和 strStartData
                    continue;
                }

                for (i = 0; i < sizeof(StrIndex_array) / sizeof(stStringIndex); i++)
                {
                    if (enumKw_DataType == StrIndex_array[i].index)
                    {
                        break;
                    }
                }
                if (0 == strncmp(StrIndex_array[i].strDesc, u8Buf, strlen(StrIndex_array[i].strDesc)))
                {
                    // 不显示 enumKw_Type
                    continue;
                }

                // 去掉尾部','并在尾部添加"\r\n"
                if (',' == u8Buf[strlen(u8Buf) - 1])
                {
                    u8Buf[strlen(u8Buf) - 1] = 0;
                }

                if (strlen(u8Buf) + strlen(newLine) < MaxLineBuffer)
                {
                    strcat(u8Buf, newLine);
                }
                else
                {
                    u8Buf[MaxLineBuffer - 3] = '\r';
                    u8Buf[MaxLineBuffer - 2] = '\n';
                    u8Buf[MaxLineBuffer - 1] = 0;
                }


                // 将首个','替换为':'
                pCh = strchr(u8Buf, ',');
                if (nullptr != pCh)
                {
                    *pCh = ':';
                }

                if (pResult->descreption)
                {
                    if ((strlen(u8Buf) + 1) < (MaxDescLen - strlen(pResult->descreption)))
                    {
                        strcat(pResult->descreption, u8Buf);
                    }
                }
            }// else
            memset(u8Buf, 0, sizeof(u8Buf));
        }//while
        if (0 == u32DatNum)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }
        //处理奇偶数，matlab格式的在读取回来后在归一化时会处理
        if ((u32DatNum & 1) != 0)
        {
            u32DatNum -= 1;
        }

        if (pResult->u32DatCnt == 0)
        {
            pResult->u32DatCnt = u32DatNum;
        }
        pResult->SampleCount = u32DatNum;
        pResult->u32InterpCnt = u32DatNum * sizeof(stPNDat);
        pResult->desLen = strlen(pResult->descreption);
        pResult->FrameStart = 0;
        pResult->FrameEnd = pResult->SampleCount;

        if (Encoding)
        {
            WaveFileCommon::Instance().ScramblerData(pResult, false);
        }

        if (insertDataFlag && (pnMode & PN_VSA_SCENE_MODE))
        {
            WaveFileCommon::Instance().InsertGapData(pResult);
        }
        iRet = WT_ERR_CODE_OK;
    } while (0);
    if (pFile)
    {
        fclose(pFile);
    }
    return iRet;
}