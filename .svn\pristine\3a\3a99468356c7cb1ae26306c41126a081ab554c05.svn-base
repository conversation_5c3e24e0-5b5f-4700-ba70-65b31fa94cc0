#include "scpi_listmod.h"
#include "basehead.h"
#include "commonhandler.h"
#include "listmod_sequence.h"
#include "tester.h"
#include "wtlog.h"
#include "wterror.h"
#include "vsahandler.h"

static inline int ListModeCheck(SPCIUserParam *attr)
{
    if (attr->m_List == nullptr)
    {
        attr->m_List.reset(new ListSeq());
    }

    return WT_ERR_CODE_OK;
}

scpi_result_t SCPI_SetTxListModEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    //EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->m_List == nullptr)
    {
        attr->m_List.reset(new ListSeq());
    }

    Ret = WT_SetTxListModeEnable(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetTxListModEnable();
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetTxListModDisable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    //attr->m_List.reset(nullptr);
    //attr->m_List = nullptr;

    if (attr->m_List == nullptr)
    {
        attr->m_List.reset(new ListSeq());
    }
    Ret = WT_SetTxListModeDisable(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetTxListModDisable();
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetRxListModEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    //EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->m_List == nullptr)
    {
        attr->m_List.reset(new ListSeq());
    }

    Ret = WT_SetRxListModeEnable(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetRxListModEnable();
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetRxListModDisable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    //attr->m_List.reset(nullptr);
    //attr->m_List = nullptr;

    if (attr->m_List == nullptr)
    {
        attr->m_List.reset(new ListSeq());
    }
    Ret = WT_SetRxListModeDisable(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetRxListModDisable();
    return SCPI_ResultOK(context);
}


scpi_result_t SCPI_SetListTxSeqStart(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int SegNo = 0;
    int ParamsSize = 0;
    int AlzType = WT_ALZ_PARAM_FFT;
    AnalyzeParam *AlzParam = nullptr;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    if (attr->m_List->GetListModScen() != LISTSCEN_NONECOMB)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart ListModScen=" << attr->m_List->GetListModScen() << std::endl;
        Ret = WT_LIST_TXSEQ_START_SCEN_NOT_MATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListModEnable() == false || attr->m_List->GetListModSeqSize(SEQUENCETX) == 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart TxSeqSize=" << attr->m_List->GetListModSeqSize(SEQUENCETX) << " ListmodEnable=" << attr->m_List->GetListModEnable() << std::endl;
        Ret = WT_LIST_TXSEQ_START_LISTNOENABLE_OR_TXSEQSIZE_IS_ZERO;
    }
    IF_ERR_RETURN(Ret);

    Ret = WT_SetSegVsaClear(attr->ConnID);
    IF_ERR_RETURN(Ret);

    //下发scpi缓存的相关seq配置到server
    auto BeginIt = attr->m_List->GetSegBeginIter(SEQUENCETX);
    auto EndIt = attr->m_List->GetSegEndIter(SEQUENCETX);
    while (BeginIt != EndIt)
    {
        if (BeginIt->get() == nullptr)
        {
            continue;
        }
        if (BeginIt->get()->TxFlag != true)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart is not tx Seg  SegNo=" << SegNo << " TxFlag=" << BeginIt->get()->TxFlag << std::endl;
            Ret = WT_LIST_TXSEQ_START_LIST_SEG_ISNOT_TX;
        }
        IF_ERR_RETURN(Ret);

        //当协议为lte时，测量时长需要根据ModStatNum、SEMStatNum、ACLRStatNum、PowerStatNum确定
        if (BeginIt->get()->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
        {
            double maxVal = 0.0;
            //const double slot = 0.5 * Ms;
            //const double subframe = 1.0 * Ms; //lte分支合并主线，先屏蔽
            const double LeftOffset = 300 * Us;
            const double RightOffset = 300 * Us;
            /*if (BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.EvmEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.MErrEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PErrEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.IBEEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ESFlatEnable == 1)
            {
                maxVal = (((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModStatNum) * slot > maxVal) ? 
                    ((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModStatNum) * slot : maxVal;
            }

            if (BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SpectEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.OBWEnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMEnable == 1)
            {
                maxVal = (((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMStatNum) * slot > maxVal) ? 
                    ((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMStatNum) * slot : maxVal;
            }

            if (BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLREnable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.UTRA1Enable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.UTRA2Enable == 1
                || BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.EUTRAEnable == 1)
            {
                maxVal = (((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLRStatNum) * slot > maxVal) ? 
                    ((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLRStatNum) * slot : maxVal;
            }

            if (BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerEnable == 1)
            {
                maxVal = (((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerStatNum) * subframe > maxVal) ? 
                    ((double)BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerStatNum) * subframe : maxVal;
            }*/ //lte分支合并主线，先屏蔽
            BeginIt->get()->tx_seg.SegTimeParam.Meadura = maxVal + LeftOffset + RightOffset;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart SegNo=" << SegNo << " SegTimeParam.Meadura=" << BeginIt->get()->tx_seg.SegTimeParam.Meadura << std::endl;
        }


        //发送每个seg的时间参数到server
        Ret = WT_SetSeqTxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.SegTimeParam), sizeof(SeqTimeParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的抓取参数到server
        Ret = WT_SetSegVsaCapParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的triger参数到server
        Ret = WT_SetSegVsaTrigParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaTrigParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的triger common参数到server
        Ret = WT_SetSegVsaTrigCommonParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.SegTigComParam), sizeof(TriggerCommonParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的分析参数到server
        Ret = WT_SetSegVsaAlzCommParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaAlzParam.commonAnalyzeParam));
        IF_ERR_RETURN(Ret);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart SegNo=" << SegNo << " attr->vsaParam.Demode=" << BeginIt->get()->tx_seg.vsaParam.Demode << std::endl;
        switch (BeginIt->get()->tx_seg.vsaParam.Demode)
        {
        case WT_DEMOD_CW:
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft;
            break;
        case WT_DEMOD_BT:
            AlzType = WT_ALZ_PARAM_BT;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt;
            break;
        case WT_DEMOD_ZIGBEE:
            AlzType = WT_ALZ_PARAM_ZIGBEE;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee;
            break;
        case WT_DEMOD_GLE:
            AlzType = WT_ALZ_PARAM_GLE;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink;
            break;
        case ALG_3GPP_STD_GSM:
        case ALG_3GPP_STD_WCDMA:
        case ALG_3GPP_STD_5G:
        case ALG_3GPP_STD_4G:
        case ALG_3GPP_STD_NB_IOT:
            AlzType = WT_ALZ_PARAM_3GPP;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            AlzType = WT_ALZ_PARAM_WSUN;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun;
            break;
        case WT_DEMOD_ZWAVE:
            AlzType = WT_ALZ_PARAM_ZWAVE;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave;
            break;
        default:
            AlzType = WT_ALZ_PARAM_WIFI;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi;
            break;
        }
        Ret = WT_SetSegVsaAlzProtoParam(attr->ConnID, SegNo, AlzType, AlzParam, ParamsSize);
        IF_ERR_RETURN(Ret);

        BeginIt++;
        SegNo++;
    }

    if (attr->m_fullDuplexEnable)
    {
        VsgParameter *para = nullptr;
        if (attr->m_List->GetListModSeqSize(SEQUENCERX) > 0)
        {
            para = (VsgParameter *)(attr->m_List->GetBaseParam(SEQUENCERX, 0));
        }

        if (attr->NeedSetVSGPattern)
        {
            Ret = WT_SetVSGPattern(attr->ConnID, attr->vsgPattern.data(), attr->vsgPattern.size());
            attr->NeedSetVSGPattern = !Ret ? false : attr->NeedSetVSGPattern;
        }
        
        if (para == nullptr)
        {
            Ret = WT_SetVSGParameter_V2(attr->ConnID, &attr->vsgParam, &attr->vsgExtParam);
        }
        else
        {
            Ret = WT_SetVSGParameter_V2(attr->ConnID, para, &attr->vsgExtParam);
        }
    }

    //启动tx seq
    Ret = WT_SetListTxSeqStart(attr->ConnID, attr->m_List->GetListTxSeqTrigerOffset(SEQUENCETX));
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETX, SEQUENCERUNNING);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqStart(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int SegNo = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    if (attr->m_List->GetListModScen() != LISTSCEN_NONECOMB)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqStart ListModScen=" << attr->m_List->GetListModScen() << std::endl;
        Ret = WT_LIST_RXSEQ_START_SCEN_NOT_MATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListModEnable() == false || attr->m_List->GetListModSeqSize(SEQUENCERX) == 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqStart RxSeqSize=" << attr->m_List->GetListModSeqSize(SEQUENCERX) << " ListmodEnable=" << attr->m_List->GetListModEnable() << std::endl;
        Ret = WT_LIST_RXSEQ_START_LISTNOENABLE_OR_RXSEQSIZE_IS_ZERO;
    }
    IF_ERR_RETURN(Ret);

    //下发scpi缓存的相关seq配置到server
    auto BeginIt = attr->m_List->GetSegBeginIter(SEQUENCERX);
    auto EndIt = attr->m_List->GetSegEndIter(SEQUENCERX);
    while (BeginIt != EndIt)
    {
        if (BeginIt->get() == nullptr)
        {
            continue;
        }
        if (BeginIt->get()->TxFlag == true)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqStart is not rx Seg  SegNo=" << SegNo << " TxFlag=" << BeginIt->get()->TxFlag << std::endl;
            Ret = WT_LIST_RXSEQ_START_LIST_SEG_ISNOT_RX;
        }
        IF_ERR_RETURN(Ret);

        //发送每个seg的时间参数到server
        Ret = WT_SetSeqRxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.SegTimeParam), sizeof(SeqTimeParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的vsg参数到server
        Ret = WT_SetSegVsgParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.vsgParam));

        //发送每个seg的triger common参数到server
        Ret = WT_SetSegVsgTrigCommonParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.SegTigComParam), sizeof(TriggerCommonParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的wave参数到server
        Ret = WT_SetSegVsgWaveParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.waveParam));
        IF_ERR_RETURN(Ret);

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT_SetSegVsgWaveParam:{ " << std::endl \
                                                     << "    WaveName: " << BeginIt->get()->rx_seg.waveParam.WaveName << std::endl \
                                                     << "    WaveType: " << BeginIt->get()->rx_seg.waveParam.WaveType << std::endl \
                                                     << "}" << std::endl;
        //发送每个seg的vsg参数到server
        Ret = WT_SetSegVsgSyncParam(attr->ConnID, SegNo, BeginIt->get()->rx_seg.vsgSyncParam);
        IF_ERR_RETURN(Ret);

        BeginIt++;
        SegNo++;
    }

    if (attr->m_fullDuplexEnable)
    {
        VsaParameter *para = nullptr;
        if (attr->m_List->GetListModSeqSize(SEQUENCETX) > 0)
        {
            para = (VsaParameter *)(attr->m_List->GetBaseParam(SEQUENCETX, 0));
        }

        if (para == nullptr)
        {
            Ret = WT_SetVSA_V2(attr->ConnID, &(attr->vsaParam), &attr->vsaExtParam);
        }
        else
        {
            Ret = WT_SetVSA_V2(attr->ConnID, para, &attr->vsaExtParam);
        }
    }

    //启动tx seq
    Ret = WT_SetListRxSeqStart(attr->ConnID, attr->m_List->GetListSeqRepet(SEQUENCERX),
        attr->m_List->GetListSeqEnableFlag(SEQUENCERX), attr->m_List->GetListSeqIncrementFlag(SEQUENCERX), attr->m_List->GetListSeqCellMod(SEQUENCERX));
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCERX, SEQUENCERUNNING);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxRxSeqStart(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int SegNo = 0;
    int ParamsSize = 0;
    int AlzType = WT_ALZ_PARAM_FFT;
    AnalyzeParam *AlzParam = nullptr;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    if (attr->m_List->GetListModScen() != LISTSCEN_COMB)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxRxSeqStart ListModScen=" << attr->m_List->GetListModScen() << std::endl;
        Ret = WT_LIST_TXRXSEQ_START_SCEN_NOT_MATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListModEnable() == false || attr->m_List->GetListModSeqSize(SEQUENCETXRX) == 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxRxSeqStart TxRxSeqSize=" << attr->m_List->GetListModSeqSize(SEQUENCETXRX) << " ListmodEnable=" << attr->m_List->GetListModEnable() << std::endl;
        Ret = WT_LIST_TXRXSEQ_START_LISTNOENABLE_OR_RXSEQSIZE_IS_ZERO;
    }
    IF_ERR_RETURN(Ret);

    //下发scpi缓存的相关seq配置到server
    auto BeginIt = attr->m_List->GetSegBeginIter(SEQUENCETXRX);
    auto EndIt = attr->m_List->GetSegEndIter(SEQUENCETXRX);

    while (BeginIt != EndIt)
    {
        if (BeginIt->get() == nullptr)
        {
            continue;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxRxSeqStart SegNo=" << SegNo << " TxFlag=" << BeginIt->get()->TxFlag << std::endl;
        if (BeginIt->get()->TxFlag == true)
        {
            //发送每个seg的时间参数到server
            Ret = WT_SetSeqTxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.SegTimeParam), sizeof(SeqTimeParam));
            IF_ERR_RETURN(Ret);

            //发送每个seg的抓取参数到server
            Ret = WT_SetSegVsaCapParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaParam));
            IF_ERR_RETURN(Ret);

             //发送每个seg的triger参数到server
             Ret = WT_SetSegVsaTrigParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaTrigParam));
             IF_ERR_RETURN(Ret);

             //发送每个seg的triger common参数到server
             Ret = WT_SetSegVsaTrigCommonParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.SegTigComParam), sizeof(TriggerCommonParam));
             IF_ERR_RETURN(Ret);

            //发送每个seg的分析参数到server
            Ret = WT_SetSegVsaAlzCommParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaAlzParam.commonAnalyzeParam));
            IF_ERR_RETURN(Ret);
            switch (attr->vsaParam.Demode)
            {
            case WT_DEMOD_CW:
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft;
                break;
            case WT_DEMOD_BT:
                AlzType = WT_ALZ_PARAM_BT;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt;
                break;
            case WT_DEMOD_ZIGBEE:
                AlzType = WT_ALZ_PARAM_ZIGBEE;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee;
                break;
            case WT_DEMOD_GLE:
                AlzType = WT_ALZ_PARAM_GLE;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink;
                break;
            case ALG_3GPP_STD_GSM:
            case ALG_3GPP_STD_WCDMA:
            case ALG_3GPP_STD_5G:
            case ALG_3GPP_STD_4G:
            case ALG_3GPP_STD_NB_IOT:
                AlzType = WT_ALZ_PARAM_3GPP;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP;
                break;
            case WT_DEMOD_LRWPAN_FSK:
            case WT_DEMOD_LRWPAN_OQPSK:
            case WT_DEMOD_LRWPAN_OFDM:
                AlzType = WT_ALZ_PARAM_WSUN;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun;
                break;
            case WT_DEMOD_ZWAVE:
                AlzType = WT_ALZ_PARAM_ZWAVE;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave;
                break;
            default:
                AlzType = WT_ALZ_PARAM_WIFI;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi;
                break;
            }
            Ret = WT_SetSegVsaAlzProtoParam(attr->ConnID, SegNo, AlzType, AlzParam, ParamsSize);
            IF_ERR_RETURN(Ret);
        }
        else
        {
            //发送每个seg的时间参数到server
            Ret = WT_SetSeqRxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.SegTimeParam), sizeof(SeqTimeParam));
            IF_ERR_RETURN(Ret);

            //发送每个seg的wave参数到server
            Ret = WT_SetSegVsgWaveParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.waveParam));
            IF_ERR_RETURN(Ret);

            //发送每个seg的vsg参数到server
            Ret = WT_SetSegVsgParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.vsgParam));
            IF_ERR_RETURN(Ret);

            //发送每个seg的triger common参数到server
            Ret = WT_SetSegVsgTrigCommonParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.SegTigComParam), sizeof(TriggerCommonParam));
            IF_ERR_RETURN(Ret);
        }

        BeginIt++;
        SegNo++;
    }

    //启动tx seq
    Ret = WT_SetListTxRxSeqStart(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETXRX, SEQUENCERUNNING);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqStop(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_SetListTxSeqStop(attr->ConnID);
    IF_ERR_RETURN(Ret);

     attr->m_List->SetListSeqStat(SEQUENCETX, SEQUENCEOFF);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqStop(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_SetListRxSeqStop(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCERX, SEQUENCEOFF);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxRxSeqStop(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_SetListTxRxSeqStop(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETXRX, SEQUENCEOFF);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListSeqMod(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SeqScene = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &SeqScene, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (SeqScene != LISTSCEN_NONECOMB || SeqScene != LISTSCEN_COMB)
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCETX) == SEQUENCERUNNING || attr->m_List->GetListSeqStat(SEQUENCERX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListSeqMod when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListModScen((LISTSCEN)SeqScene);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTrigerOffset(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double TrigerOffset = 0;
    IF_ERR_RETURN(ListModeCheck(attr));


    if (!SCPI_ParamDouble(context, &TrigerOffset, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListTxSeqTrigerOffset(SEQUENCETX, TrigerOffset);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqSize(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Size = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &Size, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCETX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListTxSeqSize when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMaxSize(SEQUENCETX, Size);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSize(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Size = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &Size, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCERX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListRxSeqSize when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMaxSize(SEQUENCERX, Size);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxRxSeqSize(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Size = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &Size, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCETX) == SEQUENCERUNNING || attr->m_List->GetListSeqStat(SEQUENCERX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListTxRxSeqSize when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMaxSize(SEQUENCETXRX, Size);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqFreq(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Freq = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Freq, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            ILLEGAL_PARAM_RETURN(Freq > 8000 * MHz_API || Freq < 0)

            attr->m_List->SetListSeqFreq(SEQUENCETX, SegNum, Freq);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqFreqAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Freq = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Freq, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqFreqAll(SEQUENCETX, Freq);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqPower(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Power = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Power, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqPower(SEQUENCETX, SegNum, Power);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqPowerAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Power = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Power, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqPowerAll(SEQUENCETX, Power);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRfport(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Port = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Port, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

            attr->m_List->SetListSeqPort(SEQUENCETX, SegNum, Port);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRfportAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Port = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Port, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

    attr->m_List->SetListSeqPortAll(SEQUENCETX, Port);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqSampleRate(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double SampleRate = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &SampleRate, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

            attr->m_List->SetListSeqSampleRate(SEQUENCETX, SegNum, SampleRate);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqSampleRateAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double SampleRate = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &SampleRate, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

    attr->m_List->SetListSeqSampleRateAll(SEQUENCETX, SampleRate);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqExtGain(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double ExtGain = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &ExtGain, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqExtGain(SEQUENCETX, SegNum, ExtGain);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqExtGainAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double ExtGain = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &ExtGain, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqExtGainAll(SEQUENCETX, ExtGain);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerType(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int TrigType = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqTriggerType ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqTriggerType ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &TrigType, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListTxSeqTriggerType(SEQUENCETX, SegNum, TrigType);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerTypeAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int TrigType = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &TrigType, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListTxSeqTriggerTypeAll(SEQUENCETX, TrigType);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerLevel(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double TrigLevel = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqTriggerLevel ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqTriggerLevel ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &TrigLevel, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListTxSeqTriggerLevel(SEQUENCETX, SegNum, TrigLevel);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerLevelAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double TrigLevel = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &TrigLevel, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListTxSeqTriggerLevelAll(SEQUENCETX, TrigLevel);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerGaptime(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Gaptime = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqTriggerGaptime ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqTriggerGaptime ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Gaptime, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListTxSeqTriggerGaptime(SEQUENCETX, SegNum, Gaptime);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerGaptimeAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Gaptime = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Gaptime, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListTxSeqTriggerGaptimeAll(SEQUENCETX, Gaptime);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerFrametime(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Frametime = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListTxSeqTriggerFrametime ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SetListTxSeqTriggerFrametime ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Frametime, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListTxSeqTriggerFrametime(SEQUENCETX, SegNum, Frametime);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerFrametimeAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Frametime = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Frametime, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListTxSeqTriggerFrametimeAll(SEQUENCETX, Frametime);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerTimeout(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double TriggerTimeout = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListTxSeqTriggerTimeout ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SetListTxSeqTriggerTimeout ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &TriggerTimeout, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqTriggerTimeout(SEQUENCETX, SegNum, TriggerTimeout);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqTriggerTimeoutAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double TriggerTimeout = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &TriggerTimeout, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqTriggerTimeoutAll(SEQUENCETX, TriggerTimeout);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqGenTriggerType(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int GenTriggerType = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListTxSeqGenTriggerType ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SetListTxSeqGenTriggerType ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &GenTriggerType, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListTxSeqGenTriggerType(SEQUENCETX, SegNum, GenTriggerType);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqGenTriggerTypeAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int GenTriggerType = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &GenTriggerType, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListTxSeqGenTriggerTypeAll(SEQUENCETX, GenTriggerType);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqFreq(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Freq = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Freq, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            ILLEGAL_PARAM_RETURN(Freq > 8000 * MHz_API || Freq < 0)

            attr->m_List->SetListSeqFreq(SEQUENCERX, SegNum, Freq);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqFreqAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Freq = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Freq, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqFreqAll(SEQUENCERX, Freq);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqPower(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Power = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Power, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqPower(SEQUENCERX, SegNum, Power);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqPowerAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Power = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Power, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqPowerAll(SEQUENCERX, Power);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRfport(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Port = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Port, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

            attr->m_List->SetListSeqPort(SEQUENCERX, SegNum, Port);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRfportAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Port = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Port, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

    attr->m_List->SetListSeqPortAll(SEQUENCERX, Port);

    return SCPI_ResultOK(context);
}


scpi_result_t SCPI_SetListRxSeqSync(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int Sync = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqSync ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqSync ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &Sync, true))

            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(Sync != 0 && Sync != 1)

            attr->m_List->SetListSeqSync(SEQUENCERX, SegNum, Sync);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSyncAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Sync = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &Sync, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(Sync != 0 && Sync != 1)

    attr->m_List->SetListSeqSyncAll(SEQUENCERX, Sync);

    return SCPI_ResultOK(context);

}

scpi_result_t SCPI_SetListRxSeqArbRepet(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int Repeat = 0;
    int SegNo = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqArbRepet ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqArbRepet ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNo, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &Repeat, true))

            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListRxSeqArbRepet(SegNo, Repeat);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqArbRepetAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Repeat = 0;

    if (!SCPI_ParamInt(context, &Repeat, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListRxSeqArbRepetAll(Repeat);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqArbExtend(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int Extend = 0;
    int SegNo = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqArbExtend ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqArbExtend ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNo, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &Extend, true))

            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListRxSeqArbExtend(SegNo, Extend);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqArbExtendAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Extend = 0;

    if (!SCPI_ParamInt(context, &Extend, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListRxSeqArbExtendAll(Extend);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRepet(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Repet = 0;

    if (!SCPI_ParamInt(context, &Repet, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqRepet(SEQUENCERX, Repet);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Enable = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &Enable, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqEnableFlag(SEQUENCERX, Enable);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqCellMod(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int CellMod = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &CellMod, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListRxSeqCellMod(SEQUENCERX, CellMod);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqIncre(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Incre = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &Incre, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqIncrementFlag(SEQUENCERX, Incre);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqTriggerTimeoutAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double TriggerTimeout = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &TriggerTimeout, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqTriggerTimeoutAll(SEQUENCERX, TriggerTimeout);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSampleRate(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double SampleRate = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &SampleRate, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

            attr->m_List->SetListSeqSampleRate(SEQUENCERX, SegNum, SampleRate);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSampleRateAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double SampleRate = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &SampleRate, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

    attr->m_List->SetListSeqSampleRateAll(SEQUENCERX, SampleRate);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqExtGain(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double ExtGain = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &ExtGain, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqExtGain(SEQUENCERX, SegNum, ExtGain);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqExtGainAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double ExtGain = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &ExtGain, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqExtGainAll(SEQUENCERX, ExtGain);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqWave(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    char fileName[256] = {0};
    int SegNum = 0;
    size_t copyLen = 0;
    std::string Tmp;

    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqWave ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqWave ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            std::vector<std::string> low_name_list;
            memset(fileName, 0, sizeof(fileName));
            if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            if (strcmp(fileName, "CW") == 0)
            {
                Tmp.clear();
                attr->m_List->SetListRxSeqWave(SegNum, Tmp);
            }
            else
            {
                Ret = check_waveform_exist_v2(context, fileName, low_name_list);
                IF_ERR_RETURN(Ret);

                attr->m_List->SetListRxSeqWave(SegNum, low_name_list[0]);
            }
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqWaveAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    char fileName[256] = {0};
     size_t copyLen = 0;
    std::vector<std::string> low_name_list;
    std::string Tmp;

    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (strcmp(fileName, "CW") == 0)
    {
        Tmp.clear();
        attr->m_List->SetListRxSeqWaveAll(Tmp);
    }
    else
    {
        Ret = check_waveform_exist_v2(context, fileName, low_name_list);
        IF_ERR_RETURN(Ret);

        attr->m_List->SetListRxSeqWaveAll(low_name_list[0]);
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqDuration(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Duration = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Duration, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqDuration(SEQUENCETX, SegNum, Duration);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqDurationAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Duration = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Duration, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqDurationAll(SEQUENCETX, Duration);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqDuration(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Duration = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Duration, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqDuration(SEQUENCERX, SegNum, Duration);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqDurationAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Duration = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Duration, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqDurationAll(SEQUENCERX, Duration);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaoffset(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Meaoffset = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqMeaoffset ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqMeaoffset ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Meaoffset, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqMeaoffset(SEQUENCETX,SegNum, Meaoffset);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaoffsetAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Meaoffset = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Meaoffset, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMeaoffsetAll(SEQUENCETX, Meaoffset);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqMeaoffset(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Meaoffset = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqGap ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqGap ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Meaoffset, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqMeaoffset(SEQUENCERX,SegNum, max(Meaoffset, LIST_MODE_MIN_VSG_MEAOFFSET));
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqMeaoffsetAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Meaoffset = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Meaoffset, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMeaoffsetAll(SEQUENCERX, max(Meaoffset, LIST_MODE_MIN_VSG_MEAOFFSET));

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaDur(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double MeaDur = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqMeaDur ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqMeaDur ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &MeaDur, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqMeaDur(SEQUENCETX,SegNum, MeaDur);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaDurAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double MeaDur = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &MeaDur, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMeaDurAll(SEQUENCETX, MeaDur);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRepeat(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Repeat = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Repeat, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqRepeat(SEQUENCETX,SegNum, Repeat);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRepeatAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Repeat = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Repeat, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqRepeatAll(SEQUENCETX, Repeat);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRepeat(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Repeat = 0.0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Repeat, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqRepeat(SEQUENCERX,SegNum, Repeat);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRepeatAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Repeat = 0.0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamDouble(context, &Repeat, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqRepeatAll(SEQUENCERX, Repeat);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqAnalDemod(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int AnalDemod = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqAnalDemod ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqAnalDemod ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &AnalDemod, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqAnalDemod(SEQUENCERX,SegNum, AnalDemod);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqAnalDemodAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int AnalDemod = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (!SCPI_ParamInt(context, &AnalDemod, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqAnalDemodAll(SEQUENCETX, AnalDemod);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DeleteListTxSeqSeg(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_DeleteListTxSeqSeg ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &SegNum, true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
        attr->m_List->DeleteListSeqSeg(SEQUENCETX, SegNum);
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DeleteListTxSeqSegAll(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    attr->m_List->DeleteListSeqSegAll(SEQUENCETX);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DeleteListRxSeqSeg(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int SegNum = 0;
    IF_ERR_RETURN(ListModeCheck(attr));

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_DeleteListRxSeqSeg ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &SegNum, true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
        attr->m_List->DeleteListSeqSeg(SEQUENCERX,SegNum);
    }

    return SCPI_ResultOK(context);

}

scpi_result_t SCPI_DeleteListRxSeqSegAll(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    IF_ERR_RETURN(ListModeCheck(attr));

    attr->m_List->DeleteListSeqSegAll(SEQUENCETX);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SEQUENCESTATE Stat = SEQUENCEOFF;
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_GetListTxSeqAllState(attr->ConnID, (int *)&Stat);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETX, (SEQUENCESTATE)Stat);
    if (Stat == SEQUENCEREADY)
    {
        SCPI_ResultText(context, "Ready");
    }
    else if (Stat == SEQUENCERUNNING)
    {
        SCPI_ResultText(context, "Running");
    }
    else
    {
        SCPI_ResultText(context, "Failed");
    }

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListRxSeqState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListRxSeqAllState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SEQUENCESTATE Stat = SEQUENCEOFF;
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_GetListRxSeqAllState(attr->ConnID, (int *)&Stat);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCERX, (SEQUENCESTATE)Stat);
    if (Stat == SEQUENCEREADY)
    {
        SCPI_ResultText(context, "Ready");
    }
    else if (Stat == SEQUENCERUNNING)
    {
        SCPI_ResultText(context, "Running");
    }
    else
    {
        SCPI_ResultText(context, "Failed");
    }

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListTxRxSeqState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxRxSeqAllState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqCapState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllCapState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNum[2] = {0};
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_GetListTxSeqAllCapState(attr->ConnID, SegNum);
    IF_ERR_RETURN(Ret);

     SCPI_ResultInt(context, SegNum[0]);
     SCPI_ResultInt(context, SegNum[1]);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListRxSeqTransState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListRxSeqAllTransState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNum[2] = {0};
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_GetListRxSeqAllTransState(attr->ConnID, SegNum);
    IF_ERR_RETURN(Ret);

     SCPI_ResultInt(context, SegNum[0]);
     SCPI_ResultInt(context, SegNum[1]);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListTxSeqAnalyState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllAnalyState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNum[2] = {0};
    IF_ERR_RETURN(ListModeCheck(attr));

    Ret = WT_GetListTxSeqAllAnalyState(attr->ConnID, SegNum);
    IF_ERR_RETURN(Ret);

     SCPI_ResultInt(context, SegNum[0]);
     SCPI_ResultInt(context, SegNum[1]);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListTxSeqPowerResult(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllPowerResult(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    std::unique_ptr<double[]> Power;
    int SegNum = 0;
    double *PowerResult = nullptr;
    int i;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (attr->m_List == nullptr)
    {
        IF_ERR_RETURN(WT_ERR_CODE_GENERAL_ERROR);
    }

    SegNum = attr->m_List->GetListModRealSeqSize(SEQUENCETX);
    if (SegNum <= 0)
    {
        IF_ERR_RETURN(WT_ERR_CODE_GENERAL_ERROR);
    }

    Power.reset(new double[SegNum]);
    PowerResult = Power.get();

    Ret = WT_GetListTxSeqAllPowerResult(attr->ConnID, PowerResult, SegNum);
    IF_ERR_RETURN(Ret);

    for (i = 0; i < SegNum; i++)
    {
        SCPI_ResultDouble(context, PowerResult[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqParam(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 10;
    int i;
    double TmpPara[ParaNum] = {0};
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqParam ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamDouble(context, &TmpPara[i], true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
    }
    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqParam SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqParam(SEQUENCETX, SegNo, TmpPara);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqTdd(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 2;
    int UpDownLink;
    int SpecilSubFrame;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqTdd ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (!SCPI_ParamInt(context, &UpDownLink, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (!SCPI_ParamInt(context, &SpecilSubFrame, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqTdd SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqTdd(SEQUENCETX, SegNo, UpDownLink, SpecilSubFrame);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqRbAllocation(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 3;
    int Auto;
    int NoRb;
    int Offset;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqRbAllocation ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (!SCPI_ParamInt(context, &Auto, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (!SCPI_ParamInt(context, &NoRb, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (!SCPI_ParamInt(context, &Offset, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqRbAllocation SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqRbAllocation(SEQUENCETX, SegNo, Auto, NoRb, Offset);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqModulation(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 8;
    int TmpPara[ParaNum] = {0};
    int i;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqModulation ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &TmpPara[i], true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
    }

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqModulation SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqModulation(SEQUENCETX, SegNo, TmpPara);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqSemask(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 4;
    int TmpPara[ParaNum] = {0};
    int i;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqSemask ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &TmpPara[i], true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
    }

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqSemask SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqSemask(SEQUENCETX, SegNo, TmpPara);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqAclr(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 5;
    int TmpPara[ParaNum] = {0};
    int i;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqAclr ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &TmpPara[i], true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
    }

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqAclr SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqAclr(SEQUENCETX, SegNo, TmpPara);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqPmonitor(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 1;
    int PowMonEnab;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqPmonitor ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (!SCPI_ParamInt(context, &PowMonEnab, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqPmonitor SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqPmonitor(SEQUENCETX, SegNo, PowMonEnab);
    return SCPI_RES_OK;
}

scpi_result_t SCPI_SetListLteTxSeqPower(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNo = 0;
    const int ParaNum = 2;
    int TmpPara[ParaNum] = {0};
    int i;
    IF_ERR_RETURN(ListModeCheck(attr));

    if (context->parser_state.numberOfParameters != ParaNum)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not correctr when SCPI_SetListLteTxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &TmpPara[i], true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
    }

    SCPI_CommandNumbers(context, &SegNo, 1);
    if (attr->m_List->GetListModSeqSize(SEQUENCETX) <= SegNo)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The SegNo is not correctr when SCPI_SetListLteTxSeqPower SegNo= " << SegNo << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListLteTxSeqPower(SEQUENCETX, SegNo, TmpPara);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqAllSegState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    std::unique_ptr<int[]> LteSegStat;
    int SegNum = 0;
    int *LteSegStatResult = nullptr;
    int i;

    if (attr->m_List == nullptr)
    {
        IF_ERR_RETURN(WT_ERR_CODE_GENERAL_ERROR);
    }

    SegNum = attr->m_List->GetListModRealSeqSize(SEQUENCETX);
    if (SegNum <= 0)
    {
        IF_ERR_RETURN(WT_ERR_CODE_GENERAL_ERROR);
    }

    LteSegStat.reset(new int[SegNum]);
    LteSegStatResult = LteSegStat.get();

    Ret = WT_GetListLteTxSeqAllSegState(attr->ConnID, LteSegStatResult, SegNum);
    IF_ERR_RETURN(Ret);

    for (i = 0; i < SegNum; i++)
    {
        SCPI_ResultInt(context, LteSegStatResult[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb = false, int SegNo = -1);
scpi_result_t SCPI_GetListLteTxSeqSegModCurrent(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_MODU_CURRENT, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegModAverage(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_MODU_AVERAGE, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegModSdeviation(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_MODU_SDTEVIAION, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegModExtreme(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_MODU_EXTREME, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginCurrent(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_IEMISSION_MARGIN_CURRENT, false, SegNo);;
}

scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginAverage(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_IEMISSION_MARGIN_AVERAGE, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginExtreme(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_IEMISSION_MARGIN_EXTREME, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginSdeviation(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_IEMISSION_MARGIN_SDTEVIAION, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginCurrentRbindex(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_IEMISSION_MARGIN_CURRENT_RBINDEX, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginExtremeRbindex(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_IEMISSION_MARGIN_EXTREME_RBINDEX, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesCurrent(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ESFLATNESS_CURRENT, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesAverage(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ESFLATNESS_AVERAGE, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesExtreme(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ESFLATNESS_EXTREME, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesSdeviation(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ESFLATNESS_SDTEVIAION, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesCurrentScindex(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ESFLATNESS_CURRENT_SCINDEX, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskCurrent(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_SEMASK_CURRENT, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskAverage(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_SEMASK_AVERAGE, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskSdeviation(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_SEMASK_SDTEVIAION, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskExtreme(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_SEMASK_EXTREME, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskMargin(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_SEMASK_MARGIN, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginCurrentNegativ(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginCurrentPositiv(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginAverageNegativ(scpi_t *context)
{
    return SCPI_RES_OK;
}
scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginAveragePositiv(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginMinimumNegativ(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginMinimumPositiv(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegAclrCurrent(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ACLR_CURRENT, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegAclrAverage(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_ACLR_AVERAGE, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegPmonitorRms(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegPmonitorPeak(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegPowerCurrent(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegPowerAverage(scpi_t *context)
{
    int SegNo;

    SCPI_CommandNumbers(context, &SegNo, 1);

    return GetRstDoubleVectorData(context, WT_RES_3GPP_LIST_POWER_AVERAGE, false, SegNo);
}

scpi_result_t SCPI_GetListLteTxSeqSegPowerMinimum(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegPowerMaximum(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegPowerSdeviation(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskDallocation(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegAclrDallocation(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegModulationDallocation(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegModulationDModu(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegSemaskDchtype(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegAclrDchtype(scpi_t *context)
{
    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListLteTxSeqSegModulationDchtype(scpi_t *context)
{
    return SCPI_RES_OK;
}

