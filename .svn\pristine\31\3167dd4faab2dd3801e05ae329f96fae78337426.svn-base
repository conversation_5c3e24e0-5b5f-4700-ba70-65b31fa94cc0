//*****************************************************************************
//File: wtBusidefine.h
//Describe:业务板硬件相关定义
//Author：yuanyongchun
//Date: 2021.01.15
//*****************************************************************************
#ifndef _WT_BUSI_DEFINE_H_
#define _WT_BUSI_DEFINE_H_

//SPI ADDRESS DEFINE
enum SpiAddrDefine
{
    BUSI_DEVM_SPI_TX = 0,                     //写入到器件的数据
    BUSI_DEVM_SPI_RX = 4,                     //从器件读出的数据
    BUSI_DEVM_SPI_CR1 = 8,                    //控制寄存器1
    BUSI_DEVM_SPI_CR2 = 12,                   //控制寄存器2
    BUSI_DEVM_SPI_STATUS = BUSI_DEVM_SPI_CR1, //器件SPI总线状态
};

//工作模式
enum WT_WORKMODE_E
{
    WT_WORKMODE_SISO,
    WT_WORKMODE_MASTER,
    WT_WORKMODE_SLAVE,
};

//VSG工作模式
enum WT_VSG_WORKMODE_E
{
    VSG_WORKMODE_SISO,
    VSG_WORKMODE_NO_USE,
    VSG_WORKMODE_MIMO_MASTER,
    VSG_WORKMODE_MIMO_SLAVE,
};

#define BUSI_VSA_VSG_STOP             (0x0005  << 2)         //
#define VSA_STOP_BIT                  12
#define VSG_STOP_BIT                  11
#define VSA_SPI_CFG_RESET_DMA_BIT     1
#define VSG_SPI_CFG_RESET_DMA_BIT     0


#define VSA_TRIG_READY_BIT    0
#define VSG_TRIG_READY_BIT    1

#define BASE_BAND_SWITCH              (0x00E0  << 2)         //

#define BUSI_FPGA_FLASH_EARSE_TARG           0x7ff0000   //
#define BUSI_FPGA_FLASH_WRITE_START          0x4000000   //

#define BUSI_FPGA_FLASH_EARSE           (0x0089 << 2)   //
#define BUSI_FPGA_FLASH_ADDR            (0x0088 << 2)   //
#define BUSI_FPGA_FLASH_DATA            (0x008A << 2)   //
#define BUSI_FPGA_FLASH_WRITE           (0x0083 << 2)   //
#define BUSI_FPGA_FLASH_STATUS          (0x008C << 2)   //
#define BUSI_FPGA_FINISH_RESET          (0x008D << 2)   //
#define BUSI_FPGA_FLASH_MODE            (0x008E << 2)   //

//AD5611晶振
#define BUSI_AD5611_SEL               (0x000E  << 2)          //AD5611片选
//VA
#define BUSI_AD5611_SPI               (0x0010  << 2)          //写入到AD5611的数据
//VB
#define BUSI_VB_AD5611_1_SPI          (0x0010  << 2)          //AD5611 SPI基地址
#define BUSI_VB_AD5611_2_SPI          (0x0014  << 2)          //AD5611 SPI基地址

enum BusiAd5611Sel
{
    BUSI_AD5611_TXBB,
    BUSI_AD5611_MODE,
};

//AD5611晶振 VB基带板
#define BUSI_AD5611_1_SPI             (0x0010  << 2)          //写入到AD5611的数据
#define BUSI_AD5611_2_SPI             (0x0014  << 2)          //写入到AD5611的数据
#define BUSI_AD5611_3_SPI             (0x000A  << 2)          //写入到AD5611的数据


//AD7682晶振
#define BUSI_RX_AD7682_CH_SEL         (0x0090  << 2)          //写入到AD5611的数据
#define BUSI_RX_AD7682_CTRL1          (0x0092  << 2)          //写入到AD5611的数据
#define BUSI_RX_AD7682_RX             (0x0091  << 2)          //从AD5611读出的数据
#define BUSI_RX_AD7682_STATUS         BUSI_RX_AD7682_CTRL1    //控制寄存器1
#define BUSI_RX_AD7682_CH_EXT_SEL     (0x0094  << 2)          //控制寄存器2

#define BUSI_TX_AD7682_CH_SEL         (0x0095  << 2)          //写入到AD5611的数据
#define BUSI_TX_AD7682_CTRL1          (0x0097  << 2)          //写入到AD5611的数据
#define BUSI_TX_AD7682_RX             (0x0096  << 2)          //从AD5611读出的数据
#define BUSI_TX_AD7682_STATUS         BUSI_TX_AD7682_CTRL1    //控制寄存器1
#define BUSI_TX_AD7682_CH_EXT_SEL     (0x0099  << 2)          //控制寄存器2

//AD7689
#define BUSI_SW_AD7689_CH_SEL         (0x0090  << 2)          //写入到AD7689的数据
#define BUSI_SW_AD7689_CTRL1          (0x0092  << 2)          //写入到AD7689的数据
#define BUSI_SW_AD7689_RX             (0x0091  << 2)          //从AD7689读出的数据

//ADF4106
#define BUSI_ADF4106_SPI            (0x0045 << 2)           //ADF4106 SPI基地址

//HM7044
#define BUSI_HM7044_SPI             (0x0050 << 2)           //HM7044 SPI基地址

//LTC5594
#define BUSI_LTC5594_SPI            (0x000A << 2)           //LTC5594 SPI基地址



//FPGA通用类
#define BUSI_FPGA_VERSION           (0x0000 << 2)           //单元板FPGA的版本号
#define BUSI_FPGA_COMPILE_DATE      (0x0001 << 2)           //单元板FPGA的版本编译日期(年月日)
#define BUSI_FPGA_COMPILE_TIME      (0x0002 << 2)           //单元板FPGA的版本编译时间(时分秒)
#define BUSI_FPGA_MODULE_VERSION    (0x0003 << 2)           //单元板信息
#define BUSI_FPGA_PLL_LOCK          (0x0004 << 2)           //PLL锁定
#define BUSI_FPGA_SYM_RESET         (0x0005 << 2)           //SYM RST
#define BUSI_FPGA_LO_LOCK_DET       (0x0006 << 2)           //LO锁定

enum BusiPlaneSymReset
{
    BUSI_RESET_AD5611,
    BUSI_RESET_RX_LMX2594,
    BUSI_RESET_TX_LMX2594,
    BUSI_RESET_RX_DDS,
    BUSI_RESET_TX_DDS,
    BUSI_RESET_TX_4106,
    BUSI_RESET_HMC7044,
    BUSI_RESET_AD7091,
    BUSI_RESET_ADC,
    BUSI_RESET_DAC,
};

enum BusiPlaneLoLockDet
{
    BUSI_LO_LOCK_RX_HMC3716_LD,
    BUSI_LO_LOCK_TX_HMC3716_LD,
    BUSI_LO_LOCK_HMC1031_DET,
};

//LMX2594
#define BUSI_RX_LMX2594_SPI         (0x002A << 2)            //LMX2594_1 SPI基地址
#define BUSI_TX_LMX2594_SPI         (0x0030 << 2)            //LMX2594_2 SPI基地址

#define BUSI_RX_LMX2594_SPI_418         (0x00DC << 2)            //LMX2594_1 SPI基地址
#define BUSI_TX_LMX2594_SPI_418         (0x00D4 << 2)            //LMX2594_2 SPI基地址

//HMC833
#define BUSI_RX_HMC833_SPI         (0x00D8 << 2)            //LMX2594_1 SPI基地址
#define BUSI_TX_HMC833_SPI         (0x00D0 << 2)            //LMX2594_2 SPI基地址

//FLASH
#define BUSI_FLASH_WD_START         (0x0083 << 2)            //BUSI FLASH
#define BUSI_FLASH_WD_DATA          (0x008A << 2)            //BUSI FLASH
#define BUSI_FLASH_WD_ADDR          (0x0084 << 2)            //BUSI FLASH
#define BUSI_FLASH_RD_START         (0x0086 << 2)            //BUSI FLASH
#define BUSI_FLASH_RD_LEN           (0x0087 << 2)            //BUSI FLASH
#define BUSI_FLASH_RD_DATA          (0x0085 << 2)            //BUSI FLASH
#define BUSI_FLASH_RD_ADDR          (0x0088 << 2)            //BUSI FLASH
#define BUSI_FLASH_FULL             (0x0080 << 2)            //BUSI FLASH
#define BUSI_FLASH_EMTPY            (0x0081 << 2)            //BUSI FLASH
#define BUSI_FLASH_ERASE            (0x0089 << 2)            //BUSI FLASH
#define BUSI_FLASH_DONE             (0x0082 << 2)            //BUSI FLASH

//AD7091
#define BUSI_AD7091_SPI             (0x004A << 2)            //AD7091 SPI基地址
#define BUSI_AD7091_CONVST          (0x004E << 2)            //AD7091 SPI基地址

//PAC
#define WT418_SW_PAC_CTL3         (0X000E << 2)
#define WT418_SW_LOOP_CTL5        (0X000F << 2)
enum BusiVoltChannel
{
    BUSI_BB_VOLT_FPGA_MON_3V3,
    BUSI_BB_VOLT_VCCINT_0V85,
    BUSI_BB_VOLT_FPGA_MON_1V1,
    BUSI_BB_VOLT_VDD_0V9,
    BUSI_BB_VOLT_FPGA_MON_2V5,
    BUSI_BB_VOLT_FPGA_1V8,
    BUSI_BB_VOLT_SLOT1_VDD_4V2,
    BUSI_BB_VOLT_SLOT1_VDD_5V5,
};

// 射频板移位寄存器
enum BusiSwitchShiftReg
{
    RF_SHIFT_REG_0 = 0,
    RF_SHIFT_REG_1,
    RF_SHIFT_REG_2,
    RF_SHIFT_REG_COUNT,
};

// 射频板移位寄存器控制位置定义
enum BusiSwitchShiftRegCtrlType
{
    RF_SW_CTRL_TYPE_MOD_RF = 0,
    RF_SW_CTRL_TYPE_MIX_RF,
    RF_SW_CTRL_TYPE_ALL_RF, // 射频BAND
    RF_SW_CTRL_TYPE_MOD_LO,
    RF_SW_CTRL_TYPE_MIX_LO,
    RF_SW_CTRL_TYPE_ALL_LO, // 射频板LO band设置
    RF_SW_CTRL_TYPE_COUNT,
};



//DDS
#define BUSI_RX_DDS_SPI             (0x0035 << 2)            //DDS_1 SPI基地址
#define BUSI_TX_DDS_SPI             (0x003A << 2)            //DDS_2 SPI基地址

//射频板移位寄存器，同时控制开关和ATT
#define BUSI_RX_SHIFT_0_SPI     (0x0060 << 2)            //ATT SPI基地址
#define BUSI_RX_SHIFT_1_SPI     (0x0065 << 2)            //ATT SPI基地址
#define BUSI_RX_SHIFT_2_SPI     (0x006A << 2)            //ATT SPI基地址

#define BUSI_TX_SHIFT_0_SPI     (0x0070 << 2)            //ATT SPI基地址
#define BUSI_TX_SHIFT_1_SPI     (0x0075 << 2)            //ATT SPI基地址
#define BUSI_TX_SHIFT_2_SPI     (0x007A << 2)            //ATT SPI基地址

//LO SHIFT
#define BUSI_RX_LO_SHIFT_TX_8_39    (0x001A  << 2)          //写入到LO SHIFT的数据
#define BUSI_RX_LO_SHIFT_TX_0_7     (0x001B  << 2)          //写入到LO SHIFT的数据
#define BUSI_RX_LO_SHIFT_RX_8_39    (0x001C  << 2)          //从LO SHIFT读出的数据
#define BUSI_RX_LO_SHIFT_RX_0_7     (0x001D  << 2)          //从LO SHIFT读出的数据
#define BUSI_RX_LO_SHIFT_CR1        (0x001E  << 2)          //控制寄存器1
#define BUSI_RX_LO_SHIFT_CR2        (0x001F  << 2)          //控制寄存器2
#define BUSI_RX_LO_SHIFT_STATUS     BUSI_RX_LO_SHIFT_CR1    //状态

#define BUSI_TX_LO_SHIFT_TX_8_39    (0x0020  << 2)          //写入到LO SHIFT的数据
#define BUSI_TX_LO_SHIFT_TX_0_7     (0x0021  << 2)          //写入到LO SHIFT的数据
#define BUSI_TX_LO_SHIFT_RX_8_39    (0x0022  << 2)          //从LO SHIFT读出的数据
#define BUSI_TX_LO_SHIFT_RX_0_7     (0x0023  << 2)          //从LO SHIFT读出的数据
#define BUSI_TX_LO_SHIFT_CR1        (0x0024  << 2)          //控制寄存器1
#define BUSI_TX_LO_SHIFT_CR2        (0x0025  << 2)          //控制寄存器2
#define BUSI_TX_LO_SHIFT_STATUS     BUSI_RX_LO_SHIFT_CR1    //状态

//AD9684 ADC
#define BUSI_AD9684_ADC_SPI         (0x0055 << 2)            //AD9684_ADC SPI基地址

//AD9142 DAC
#define BUSI_AD9142_DAC_SPI         (0x005A << 2)            //AD9142_DAC SPI基地址

//LMX2820
#define BUSI_TX_LMX2820_1_SPI         (0x00D0 << 2)            //LMX2820_1 SPI基地址
#define BUSI_TX_LMX2820_2_SPI         (0x00D4 << 2)            //LMX2820_2 SPI基地址
#define BUSI_RX_LMX2820_1_SPI         (0x00D8 << 2)            //LMX2820_1 SPI基地址
#define BUSI_RX_LMX2820_2_SPI         (0x00DC << 2)            //LMX2820_2 SPI基地址

//ADF4002
#define BUSI_BB_REF_PLL_1_TX                 (0x0035 << 2)            //写入到ADF4002的数据
#define BUSI_BB_REF_PLL_1_RX                 (0x0036 << 2)            //从ADF4002读出的数据
#define BUSI_BB_REF_PLL_1_CR1                (0x0037 << 2)            //控制寄存器1
#define BUSI_BB_REF_PLL_1_CR2                (0x0038 << 2)            //控制寄存器2

#define BUSI_RF_REF_PLL_2_TX                 (0x003a << 2)            //写入到ADF4002的数据
#define BUSI_RF_REF_PLL_2_RX                 (0x003b << 2)            //从ADF4002读出的数据
#define BUSI_RF_REF_PLL_2_CR1                (0x003c << 2)            //控制寄存器1
#define BUSI_RF_REF_PLL_2_CR2                (0x003d << 2)            //控制寄存器2

//AT88加密芯片
#define BUSI_AT88_IIC_OUT_EN           (0x003E << 2)        //加密芯片
#define BUSI_AT88_IIC_OUT_CLK          (0x003F << 2)        //加密芯片
#define BUSI_AT88_IIC_OUT_TX           (0x0040 << 2)        //加密芯片
#define BUSI_AT88_IIC_OUT_RX           (0x0041 << 2)        //加密芯片

//ADT7475 FAN控制芯片
#define BUSI_ADT7475_FAN_SMB_1_SLV_ADDR    (0x0043 << 2)            //写入到AD7091的数据
#define BUSI_ADT7475_FAN_SMB_1_COM_REG     (0x0044 << 2)            //从AD7091读出的数据
#define BUSI_ADT7475_FAN_SMB_1_TX          (0x0045 << 2)            //控制寄存器1
#define BUSI_ADT7475_FAN_SMB_1_RX          (0x0046 << 2)            //控制寄存器2
#define BUSI_ADT7475_FAN_SMB_1_CR1         (0x0047 << 2)            //控制寄存器1
#define BUSI_ADT7475_FAN_SMB_1_CR2         (0x0048 << 2)            //控制寄存器2

//LED
#define BUSI_LED_SHIFT_TX           (0x0030 << 2)            //写入到shift0的数据
#define BUSI_LED_SHIFT_RX           (0x0031 << 2)            //从shift0读出的数据
#define BUSI_LED_SHIFT_CR1          (0x0032 << 2)            //控制寄存器1
#define BUSI_LED_SHIFT_CR2          (0x0033 << 2)            //控制寄存器2
#define BUSI_LED_SHIFT_CLT          (0x0034 << 2)            //bit0:Led green; bit1:Led red 

//射频板版本号
#define BUSI_HW_VER_SEL           (0x002E << 2)            //读取版本号，片选
#define BUSI_HW_VER               (0x002F << 2)            //读取版本号，数值

#define BUSI_VSG_GAP_POWER_PORT    (0x007f  << 2)          //设置Gap Power ON/OFF
#define BUSI_VSG_GAP_POWER_MODE    (0x007e  << 2)      //设置Gap Power 控制方式

//ListMode
//VSA
#define BUSI_LISTMODE_RX_2594_TX_LIST                   (0xe4 << 2)
#define BUSI_LISTMODE_RX_2594_CFG_NUM_LIST              (0xe5 << 2)
#define BUSI_LISTMODE_RX_833_TX_LIST                    (0xe6 << 2)
#define BUSI_LISTMODE_RX_833_CFG_NUM_LIST               (0xe7 << 2)
#define BUSI_LISTMODE_RX_RF_SHIFT0_LIST                 (0xe8 << 2)
#define BUSI_LISTMODE_RX_RF_SHIFT1_LIST                 (0xe9 << 2)
#define BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSA           (0xea << 2)
#define BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_MASK_VSA      (0xeb << 2)
#define BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_VSA           (0xec << 2)
#define BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_MASK_VSA      (0xed << 2)
#define BUSI_LISTMODE_SW_SHIFT1_TX_LIST_VSA             (0xee << 2)
#define BUSI_LISTMODE_SW_SHIFT1_TX_LIST_MASK_VSA        (0xef << 2)
#define BUSI_LISTMODE_SW_SHIFT2_TX_LIST_VSA             (0xf1 << 2)
#define BUSI_LISTMODE_SW_SHIFT2_TX_LIST_MASK_VSA        (0xf2 << 2)
#define BUSI_LISTMODE_SW_SHIFT3_TX_LIST_VSA             (0xf3 << 2)
#define BUSI_LISTMODE_SW_SHIFT3_TX_LIST_MASK_VSA        (0xf4 << 2)
#define BUSI_LISTMODE_RESAMPLE_SEL_LIST_VSA             (0xf5 << 2)
#define BUSI_LISTMODE_O_LED_TX_LIST_VSA                 (0xf6 << 2)
#define BUSI_LISTMODE_S_SEGMENT_CNT_VSA                 (0xf7 << 2)
#define BUSI_LISTMODE_SEGMENT_INDEX_VSA                 (0xf9 << 2)
#define BUSI_LISTMODE_SEGMENT_OFFSET_VSA                (0xfc << 2)
#define BUSI_LISTMODE_SEGMENT_SAMPLE_VSA                (0xfd << 2)
#define BUSI_LISTMODE_SEGMENT_DURATION_VSA              (0xfe << 2)
#define BUSI_LISTMODE_SEGMENT_DC_OFFSET_VSA             (0xff << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_TYPE_VSA             (0x128 << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_LEVEL_VSA            (0x129 << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_VSA          (0x135 << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_OUTTRIGLEN_VSA  (0x136 << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG    (0x140 << 2)

//VSG
#define BUSI_LISTMODE_TX_2594_TX_LIST                   (0x100 << 2)
#define BUSI_LISTMODE_TX_2594_CFG_NUM_LIST              (0x101 << 2)
#define BUSI_LISTMODE_TX_833_TX_LIST                    (0x102 << 2)
#define BUSI_LISTMODE_TX_833_CFG_NUM_LIST               (0x103 << 2)
#define BUSI_LISTMODE_TX_RF_SHIFT0_LIST                 (0x104 << 2)
#define BUSI_LISTMODE_TX_RF_SHIFT1_LIST                 (0x105 << 2)
#define BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG           (0x106 << 2)
#define BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_MASK_VSG      (0x107 << 2)
#define BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_VSG           (0x108 << 2)
#define BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_MASK_VSG      (0x109 << 2)
#define BUSI_LISTMODE_SW_SHIFT1_TX_LIST_VSG             (0x10a << 2)
#define BUSI_LISTMODE_SW_SHIFT1_TX_LIST_MASK_VSG        (0x10b << 2)
#define BUSI_LISTMODE_SW_SHIFT2_TX_LIST_VSG             (0x10c << 2)
#define BUSI_LISTMODE_SW_SHIFT2_TX_LIST_MASK_VSG        (0x10d << 2)
#define BUSI_LISTMODE_SW_SHIFT3_TX_LIST_VSG             (0x10e << 2)
#define BUSI_LISTMODE_SW_SHIFT3_TX_LIST_MASK_VSG        (0x10f << 2)
#define BUSI_LISTMODE_DAC_9142_TX_LIST                  (0x110 << 2)
#define BUSI_LISTMODE_DAC_9142_CFG_NUM_LIST             (0x111 << 2)
#define BUSI_LISTMODE_O_T_CTL5_CTL3_LIST                (0x112 << 2)
#define BUSI_LISTMODE_O_LED_TX_LIST_VSG                 (0x113 << 2)
#define BUSI_LISTMODE_S_SEGMENT_CNT_VSG                 (0x115 << 2)
#define BUSI_LISTMODE_SEGMENT_INDEX_VSG                 (0x117 << 2)
#define BUSI_LISTMODE_REAMPLE_SEL_LIST_VSG              (0x119 << 2)
#define BUSI_LISTMODE_GAP_POWER_MODE_LIST_VSG           (0x11a << 2)
#define BUSI_LISTMODE_LIST_VSG_CELL_MOD                 (0xe2 << 2)

// #define BUSI_LISTMODE_CW_MODE_VSG                       (0x130 << 2)
// #define BUSI_LISTMODE_FIRST_SEGMENT_SYNC_MODE_VSG       (0x133 << 2)
// #define BUSI_LISTMODE_SEGMENT_SYNC_MODE_VSG             (0x134 << 2)
// #define BUSI_LISTMODE_SEGMENT_ENDLESS_VSG               (0x135 << 2)
// #define BUSI_LISTMODE_SEGMENT_PN_INDEX_VSG              (0x136 << 2)
// #define BUSI_LISTMODE_SEGMENT_PN_INDEX_ENABLE_VSG       (0x137 << 2)
// #define BUSI_LISTMODE_SEGMENT_REPEAT_VSG                (0x138 << 2)
// #define BUSI_LISTMODE_SEGMENT_TAIL_GAP_VSG              (0x139 << 2)
// #define BUSI_LISTMODE_SEGMENT_SEND_LEN_VSG              (0x140 << 2)

#define BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_VSG              (0x141 << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_OUTTRIGLEN_VSG   (0x142 << 2)
#define BUSI_LISTMODE_SEGMENT_TRIG_REPEAT_NUM_VSG           (0x143 << 2)
#define BUSI_LISTMODE_DMA_FIFO_RESET_VSG                    (0x118 << 2)

// listmode下, VSA采集数据时, 同单元板VSG和开关板的状态值
#define BUSI_DUPLEX_VSG_BK_STAT_DAC_REG                 (0x157<<2)  // DAC gain I路 Code
#define BUSI_DUPLEX_VSG_BK_STAT_SHIFT_0                 (0x158<<2)  // 射频板移位寄存器0
#define BUSI_DUPLEX_VSG_BK_STAT_SHIFT_1                 (0x159<<2)  // 射频板移位寄存器1
#define BUSI_DUPLEX_SW_BK_STAT_SHIFT_0_HI               (0x160<<2)  // 开关板移位寄存器0高32bit
#define BUSI_DUPLEX_SW_BK_STAT_SHIFT_0_LO               (0x161<<2)  // 开关板移位寄存器0低8bit
#define BUSI_DUPLEX_SW_BK_STAT_SHIFT_1                  (0x162<<2)  // 开关板移位寄存器1
#define BUSI_DUPLEX_SW_BK_STAT_SHIFT_2                  (0x163<<2)  // 开关板移位寄存器2
#define BUSI_DUPLEX_SW_BK_STAT_SHIFT_3                  (0x164<<2)  // 开关板移位寄存器3
#define BUSI_DUPLEX_VSG_BK_STAT_STARTING                (0x165<<2)  // VSG 基带开启状态


enum ChanlSel
{
    HW_BB_VERSION,
    HW_RF_VERSION,
    RF_ADF4002_CONFIG,
    RF_AD5611_CONFIG,
    ChanlSel_MAX,
};

enum Ad7689ChannelSel
{
    AD7689_CHANNEL_Port4,
    AD7689_CHANNEL_Port3,
    AD7689_CHANNEL_Port2,
    AD7689_CHANNEL_Port1,
    AD7689_CHANNEL_Port8,
    AD7689_CHANNEL_Port7,
    AD7689_CHANNEL_Port6,
    AD7689_CHANNEL_Port5,
};

#endif
