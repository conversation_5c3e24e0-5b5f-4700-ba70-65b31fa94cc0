/*
 * This file is part of the Xilinx DMA IP Core driver tools for Linux
 *
 * Copyright (c) 2016-present,  Xilinx, Inc.
 * All rights reserved.
 *
 * This source code is licensed under BSD-style license (found in the
 * LICENSE file in the root directory of this source tree)
 */

#include <assert.h>
#include <fcntl.h>
#include <getopt.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>
#include <stdio.h>
#include <stdint.h>
#include <unistd.h>
#include <time.h>
#include <errno.h>

#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>

#include "wtxdma.h"
#include "wtlog.h"

#define RW_MAX_SIZE 0x7ffff000
//#define RW_MAX_SIZE 0x200000

Xdmafun &Xdmafun::Instance(void)
{
	static Xdmafun XdmafunInstance;
	return XdmafunInstance;
}

int Xdmafun::TestDmaFromDevice(const char *devname, uint64_t addr, uint64_t aperture,
								  uint64_t size, uint64_t offset, uint64_t count,
								  const char *ofname, const char *cmpfname, float &timeuse)
{
	WTLog::Instance().WriteLog(LOG_DEBUG, "dmafromdevice:devname=%s,addr=%#lx,aperture=%#lx,size=%#lx,offset=%#lx,count=%#lx,ofname=%s,cmpfname=%s\n",
		   devname, addr, aperture, size, offset, count, ofname, cmpfname);
	ssize_t rc = 0;
	size_t out_offset = 0;
	size_t bytes_done = 0;
	uint64_t i;
	uint64_t apt_loop = aperture ? (size + aperture - 1) / aperture : 0;
	char *buffer = NULL;
	char *buffer2 = NULL;
	char *allocated = NULL;
	struct timespec ts_start, ts_end;
	int out_fd = -1;
	int cmp_fd = -1;
	int fpga_fd;
	long total_time = 0;
	float result;
	float avg_time = 0;
	int underflow = 0;
	int Ret = 0;

	/*
	 * use O_TRUNC to indicate to the driver to flush the data up based on
	 * EOP (end-of-packet), streaming mode only
	 */
	if (m_EopFlush)
		fpga_fd = open(devname, O_RDWR | O_TRUNC);
	else
		fpga_fd = open(devname, O_RDWR);

	if (fpga_fd < 0)
	{
		WTLog::Instance().WriteLog(LOG_ERROR, "unable to open device %s, %d.\n",
				devname, fpga_fd);
		perror("open device");
		return -EINVAL;
	}

	/* create file to write data to */
	if (ofname)
	{
		out_fd = open(ofname, O_RDWR | O_CREAT | O_TRUNC | O_SYNC,
					  0666);
		if (out_fd < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "unable to open output file %s, %d.\n",
					ofname, out_fd);
			perror("open output file");
			rc = -EINVAL;
			goto readout;
		}
	}

	if(cmpfname!=NULL)
	{
		cmp_fd = open(cmpfname, O_RDONLY);
		if (cmp_fd < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "unable to open output file %s, %d.\n",
					cmpfname, cmp_fd);
			perror("open output file");
			rc = -EINVAL;
			goto readout;
		}
		Ret = posix_memalign((void **)&buffer2, 4096 /*alignment */, size + 4096);
		if (!buffer2 || Ret != 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "OOM %lu.\n", size + 4096);
			rc = -ENOMEM;
			goto readout;
		}
		rc = ReadToBuffer(cmpfname, cmp_fd, buffer2, size, 0);
		if (rc < 0 || rc < size)
			goto readout;
	}

	Ret = posix_memalign((void **)&allocated, 4096 /*alignment */, size + 4096);
	if (!allocated || Ret != 0)
	{
		WTLog::Instance().WriteLog(LOG_ERROR,  "OOM %lu.\n", size + 4096);
		rc = -ENOMEM;
		goto readout;
	}

	buffer = allocated + offset;
	if (m_Verbose)
		fprintf(stdout, "host buffer 0x%lx, %p.\n", size + 4096, buffer);
	for (i = 0; i < count; i++)
	{
		rc = clock_gettime(CLOCK_MONOTONIC, &ts_start);
		if (apt_loop)
		{
			uint64_t j;
			uint64_t len = size;
			char *buf = buffer;

			bytes_done = 0;
			for (j = 0; j < apt_loop; j++, len -= aperture, buf += aperture)
			{
				uint64_t bytes = (len > aperture) ? aperture : len,
						 rc = ReadToBuffer(devname, fpga_fd, buf,
											 bytes, addr);
				if (rc <= 0)
					goto readout;

				if (!underflow && rc < bytes)
					underflow = 1;
				bytes_done += rc;
			}
		}
		else
		{
			rc = ReadToBuffer(devname, fpga_fd, buffer, size, addr);
			if (rc < 0)
				goto readout;
			bytes_done = rc;

			if (!underflow && bytes_done < size)
				underflow = 1;
		}
		clock_gettime(CLOCK_MONOTONIC, &ts_end);

		/* subtract the start time from the end time */
		TimespecSub(&ts_end, &ts_start);
		total_time += ts_end.tv_nsec;
		/* a bit less accurate but side-effects are accounted for */
		if (m_Verbose)
			fprintf(stdout,
					"#%lu: CLOCK_MONOTONIC %ld.%09ld sec. read %ld/%ld bytes\n",
					i, ts_end.tv_sec, ts_end.tv_nsec, bytes_done, size);

		if(buffer2 != NULL && memcmp(buffer, buffer2, bytes_done) != 0)
		{
			WTLog::Instance().WriteLog(LOG_DEBUG, "memcmp(buffer, buffer2, bytes_done) != 0\n");
			underflow = 1;
		}
	}
	/* file argument given? */
	if (out_fd >= 0)
	{
		rc = WriteFromBuffer(ofname, out_fd, buffer,
								bytes_done, out_offset);
		if (rc < 0 || rc < bytes_done)
			goto readout;
		out_offset += bytes_done;
	}

	if (!underflow)
	{
		avg_time = (float)total_time / (float)count;
		result = ((float)size) * 1000 / avg_time;
		if (m_Verbose)
			WTLog::Instance().WriteLog(LOG_DEBUG, "** Avg time device %s, total time %ld nsec, avg_time = %f, size = %lu, BW = %f \n",
				   devname, total_time, avg_time, size, result);
		WTLog::Instance().WriteLog(LOG_DEBUG, "%s ** Average BW = %lu, %f\n", devname, size, result);
		rc = 0;
	}
	else if (m_EopFlush)
	{
		/* allow underflow with -e option */
		rc = 0;
	}
	else
		rc = -EIO;
	timeuse = total_time;
readout:
	close(fpga_fd);
	if (out_fd >= 0)
		close(out_fd);
	if (cmp_fd >= 0)
		close(cmp_fd);
	free(allocated);
	free(buffer2);
	return rc;
}

int Xdmafun::TestDmaToDevice(const char *devname, uint64_t addr, uint64_t aperture,
								uint64_t size, uint64_t offset, uint64_t count,
								const char *infname, const char *ofname, const char *cmpfname, float &timeuse)
{
	WTLog::Instance().WriteLog(LOG_DEBUG, "dmatodevice:devname=%s,addr=%#lx,aperture=%#lx,size=%#lx,offset=%#lx,count=%#lx,infname=%s,ofname=%s,cmpfname=%s\n",
		   devname, addr, aperture, size, offset, count, infname, ofname, cmpfname);
	uint64_t i;
	ssize_t rc = 0;
	size_t bytes_done = 0;
	size_t out_offset = 0;
	uint64_t apt_loop = aperture ? (size + aperture - 1) / aperture : 0;
	char *buffer = NULL;
	char *buffer2 = NULL;
	char *allocated = NULL;
	struct timespec ts_start, ts_end;
	int infile_fd = -1;
	int outfile_fd = -1;
	int cmp_fd = -1;
	int fpga_fd = open(devname, O_RDWR);
	long total_time = 0;
	float result;
	float avg_time = 0;
	int underflow = 0;
	int Ret = 0;

	if (fpga_fd < 0)
	{
		WTLog::Instance().WriteLog(LOG_ERROR,  "unable to open device %s, %d.\n",
				devname, fpga_fd);
		perror("open device");
		return -EINVAL;
	}

	if (infname)
	{
		infile_fd = open(infname, O_RDONLY);
		if (infile_fd < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "unable to open input file %s, %d.\n",
					infname, infile_fd);
			perror("open input file");
			rc = -EINVAL;
			goto writeout;
		}
	}

	if (ofname)
	{
		outfile_fd =
			open(ofname, O_RDWR | O_CREAT | O_TRUNC | O_SYNC,
				 0666);
		if (outfile_fd < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "unable to open output file %s, %d.\n",
					ofname, outfile_fd);
			perror("open output file");
			rc = -EINVAL;
			goto writeout;
		}
	}

	if(cmpfname!=NULL)
	{
		cmp_fd = open(cmpfname, O_RDONLY);
		if (cmp_fd < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "unable to open output file %s, %d.\n",
					cmpfname, cmp_fd);
			perror("open output file");
			rc = -EINVAL;
			goto writeout;
		}
		Ret = posix_memalign((void **)&buffer2, 4096 /*alignment */, size + 4096);
		if (!buffer2 || Ret != 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "OOM %lu.\n", size + 4096);
			rc = -ENOMEM;
			goto writeout;
		}
		rc = ReadToBuffer(cmpfname, cmp_fd, buffer2, size, 0);
		if (rc < 0 || rc < size)
			goto writeout;
	}

	Ret = posix_memalign((void **)&allocated, 4096 /*alignment */, size + 4096);
	if (!allocated || Ret != 0)
	{
		WTLog::Instance().WriteLog(LOG_ERROR,  "OOM %lu.\n", size + 4096);
		rc = -ENOMEM;
		goto writeout;
	}
	buffer = allocated + offset;
	if (m_Verbose)
		fprintf(stdout, "host buffer 0x%lx = %p\n",
				size + 4096, buffer);

	if (infile_fd >= 0)
	{
		rc = ReadToBuffer(infname, infile_fd, buffer, size, 0);
		if (rc < 0 || rc < size)
			goto writeout;
	}

	for (i = 0; i < count; i++)
	{
		/* write buffer to AXI MM address using SGDMA */
		rc = clock_gettime(CLOCK_MONOTONIC, &ts_start);

		if (apt_loop)
		{
			uint64_t j;
			uint64_t len = size;
			char *buf = buffer;

			bytes_done = 0;
			for (j = 0; j < apt_loop; j++, len -= aperture,
				buf += aperture)
			{
				uint64_t bytes = (len > aperture) ? aperture : len,
						 rc = WriteFromBuffer(devname, fpga_fd, buf,
												bytes, addr);
				if (rc <= 0)
					goto writeout;

				bytes_done += rc;
				if (!underflow && rc < bytes)
					underflow = 1;
			}
		}
		else
		{
			rc = WriteFromBuffer(devname, fpga_fd, buffer, size,
								   addr);
			if (rc < 0)
				goto writeout;

			bytes_done = rc;
			if (!underflow && bytes_done < size)
				underflow = 1;
		}

		rc = clock_gettime(CLOCK_MONOTONIC, &ts_end);
		/* subtract the start time from the end time */
		TimespecSub(&ts_end, &ts_start);
		total_time += ts_end.tv_nsec;
		/* a bit less accurate but side-effects are accounted for */
		if (m_Verbose)
			fprintf(stdout,
					"#%lu: CLOCK_MONOTONIC %ld.%09ld sec. write %ld bytes\n",
					i, ts_end.tv_sec, ts_end.tv_nsec, size);
		if(buffer2 != NULL && memcmp(buffer, buffer2, bytes_done) != 0)
		{
			WTLog::Instance().WriteLog(LOG_DEBUG, "memcmp(buffer, buffer2, bytes_done) != 0\n");
			underflow = 1;
		}
	}
	if (outfile_fd >= 0)
	{
		rc = WriteFromBuffer(ofname, outfile_fd, buffer,
								bytes_done, out_offset);
		if (rc < 0 || rc < bytes_done)
			goto writeout;
		out_offset += bytes_done;
	}

	if (!underflow)
	{
		avg_time = (float)total_time / (float)count;
		result = ((float)size) * 1000 / avg_time;
		if (m_Verbose)
			WTLog::Instance().WriteLog(LOG_DEBUG, "** Avg time device %s, total time %ld nsec, avg_time = %f, size = %lu, BW = %f \n",
				   devname, total_time, avg_time, size, result);
		WTLog::Instance().WriteLog(LOG_DEBUG, "%s ** Average BW = %lu, %f\n", devname, size, result);
	}
	timeuse = total_time;
writeout:
	close(fpga_fd);
	if (infile_fd >= 0)
		close(infile_fd);
	if (outfile_fd >= 0)
		close(outfile_fd);
	if (cmp_fd >= 0)
		close(cmp_fd);
	free(allocated);
	free(buffer2);
	if (rc < 0)
		return rc;
	/* treat underflow as error */
	return underflow ? -EIO : 0;
}

ssize_t Xdmafun::ReadToBuffer(const char *fname, int fd, char *buffer, uint64_t size,
								uint64_t base)
{
	ssize_t rc;
	uint64_t count = 0;
	char *buf = buffer;
	off_t offset = base;
	int loop = 0;

	if (size > 10240 || base)
	{
		// 小数据不打印
		WTLog::Instance().WriteLog(LOG_DEBUG,  "ReadToBuffer %s, size = 0x%lx, base = 0x%lx.\n", fname, size, base);
	}

    //size = size + 128 * 4096;
	while (count < size)
	{
		uint64_t bytes = size - count;

		if (bytes > RW_MAX_SIZE)
			bytes = RW_MAX_SIZE;

		if (offset)
		{
			rc = lseek(fd, offset, SEEK_SET);
			if (rc != offset)
			{
				WTLog::Instance().WriteLog(LOG_ERROR,  "%s, seek off 0x%lx != 0x%lx.\n",
						fname, rc, offset);
				perror("seek file");
				return -EIO;
			}
		}

		/* read data from file into memory buffer */
		rc = read(fd, buf, bytes);
		//WTLog::Instance().WriteLog(LOG_ERROR,  "read %s, bytes = 0x%lx\n", fname, bytes);
		if (rc < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "%s, read 0x%lx @ 0x%lx failed %ld.\n",
					fname, bytes, offset, rc);
			perror("read file");
			return -EIO;
		}

		count += rc;
		if (rc != bytes)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "%s, read underflow 0x%lx/0x%lx @ 0x%lx.\n",
					fname, rc, bytes, offset);
			break;
		}

		buf += bytes;
		offset += bytes;
		loop++;
	}

	if (count != size && loop)
		WTLog::Instance().WriteLog(LOG_ERROR,  "%s, read underflow 0x%lx/0x%lx.\n",
				fname, count, size);
	return count;
}

ssize_t Xdmafun::WriteFromBuffer(const char *fname, int fd, char *buffer, uint64_t size,
								   uint64_t base)
{
	ssize_t rc;
	uint64_t count = 0;
	char *buf = buffer;
	off_t offset = base;
	int loop = 0;

	if (size > 10240 || base)
	{
		// 小数据不打印
		WTLog::Instance().WriteLog(LOG_ERROR,  "WriteFromBuffer %s, size = 0x%lx, base = 0x%lx.\n", fname, size, base);
	}

	while (count < size)
	{
		uint64_t bytes = size - count;

		if (bytes > RW_MAX_SIZE)
			bytes = RW_MAX_SIZE;

		if (offset)
		{
			rc = lseek(fd, offset, SEEK_SET);
			if (rc != offset)
			{
				WTLog::Instance().WriteLog(LOG_ERROR,  "%s, seek off 0x%lx != 0x%lx.\n",
						fname, rc, offset);
				perror("seek file");
				return -EIO;
			}
		}

		/* write data to file from memory buffer */
		rc = write(fd, buf, bytes);
		//WTLog::Instance().WriteLog(LOG_ERROR,  "write %s, bytes = 0x%lx\n", fname, bytes);
		if (rc < 0)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "%s, write 0x%lx @ 0x%lx failed %ld.\n",
					fname, bytes, offset, rc);
			perror("write file");
			return -EIO;
		}

		count += rc;
		if (rc != bytes)
		{
			WTLog::Instance().WriteLog(LOG_ERROR,  "%s, write underflow 0x%lx/0x%lx @ 0x%lx.\n",
					fname, rc, bytes, offset);
			break;
		}
		buf += bytes;
		offset += bytes;

		loop++;
	}

	if (count != size && loop)
		WTLog::Instance().WriteLog(LOG_ERROR,  "%s, write underflow 0x%lx/0x%lx.\n",
				fname, count, size);

	return count;
}

/* Subtract timespec t2 from t1
 *
 * Both t1 and t2 must already be normalized
 * i.e. 0 <= nsec < 1000000000
 */
int Xdmafun::TimespecCheck(struct timespec *t)
{
	if ((t->tv_nsec < 0) || (t->tv_nsec >= 1000000000))
		return -1;
	return 0;
}

void Xdmafun::TimespecSub(struct timespec *t1, struct timespec *t2)
{
	if (TimespecCheck(t1) < 0)
	{
		WTLog::Instance().WriteLog(LOG_ERROR,  "invalid time #1: %lld.%.9ld.\n",
				(long long)t1->tv_sec, t1->tv_nsec);
		return;
	}
	if (TimespecCheck(t2) < 0)
	{
		WTLog::Instance().WriteLog(LOG_ERROR,  "invalid time #2: %lld.%.9ld.\n",
				(long long)t2->tv_sec, t2->tv_nsec);
		return;
	}
	t1->tv_sec -= t2->tv_sec;
	t1->tv_nsec -= t2->tv_nsec;
	if (t1->tv_nsec >= 1000000000)
	{
		t1->tv_sec++;
		t1->tv_nsec -= 1000000000;
	}
	else if (t1->tv_nsec < 0)
	{
		t1->tv_sec--;
		t1->tv_nsec += 1000000000;
	}
}