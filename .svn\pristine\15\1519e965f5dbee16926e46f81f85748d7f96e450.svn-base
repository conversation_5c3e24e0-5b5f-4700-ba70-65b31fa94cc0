/*===========================================================================
algdef.h

algorithm result define
algorithm parameter define
===========================================================================*/
#ifndef __ALG_DEF_H__
#define __ALG_DEF_H__
#include "TypeDef.h"
#include "alg/includeAll.h"

//*************************************************************************
//
//                         algorithm parameter Enumerations
//
//*************************************************************************

#define COMMON8BIT_MAX_NUM 66
#define MAX_USER_NUM 74
#define MAX_DF_NUM 8
#ifndef BE_RU_COUNT
#define BE_RU_COUNT 144
#endif
//_ALG_3GPP_APIDEF_中的参数VSA ALZ和 VSG GEN都会使用，此处避免冲突。更新自alg\alg_3gpp_apidef.h的定义
#ifndef _ALG_3GPP_APIDEF_
#define _ALG_3GPP_APIDEF_


/**************************************************************************************************/
/*                                    Common Configurate Start                                    */
/**************************************************************************************************/

#ifndef Complex_Def
#define Complex_Def
typedef double  Complex[2];
#endif

// from alg_3gpp_vsadef.h
#define ALG_VSA_ALZ_NORMAL  0
/* Only analyze power for AGC mode */
#define ALG_VSA_ALZ_POWER   1
#define ALG_VSA_ALZ_ATE     2

/**************************************************************************************************/
/*                                    Common Configurate End                                      */
/**************************************************************************************************/

#define ALG_3GPP_MAX_STREAM         2

#define ALG_3GPP_UL                 0
#define ALG_3GPP_DL                 1

#define ALG_3GPP_FDD                0
#define ALG_3GPP_TDD                1

#define ALG_3GPP_NCP                0
#define ALG_3GPP_ECP                1

/* # Modulate type */
/* vsa auto detect modulate type */
#define ALG_3GPP_MOD_AUTO           0
#define ALG_3GPP_PID2_BPSK          1
/* BPSK also use 1bit, so define 0x11 */
#define ALG_3GPP_BPSK               0x11 /* 17 */
#define ALG_3GPP_QPSK               2
#define ALG_3GPP_PID4_QPSK          0x12 /* 18 */
#define ALG_3GPP_16QAM              4
#define ALG_3GPP_64QAM              6
#define ALG_3GPP_256QAM             8
#define ALG_3GPP_1024QAM            10

/* Payload data type */
#define ALG_3GPP_PSDU_PN9           0
#define ALG_3GPP_PSDU_PN11          1
#define ALG_3GPP_PSDU_PN15          2
#define ALG_3GPP_PSDU_PN23          3
#define ALG_3GPP_PSDU_ALL0          4
#define ALG_3GPP_PSDU_ALL1          5
#define ALG_3GPP_PSDU_1010          6

/* Generate wave length type */
#define ALG_3GPP_GW_FRAME           0
#define ALG_3GPP_GW_SUBFRM          1
#define ALG_3GPP_GW_SLOT            2

/* Filter Type */
#define ALG_3GPP_FILTER_NON         0
#define ALG_3GPP_FILTER_FIR         1
#define ALG_3GPP_FILTER_LTE         2
#define ALG_3GPP_FILTER_NR          2
#define ALG_3GPP_FILTER_RC          2
#define ALG_3GPP_FILTER_WOLA        3

#define ALG_3GPP_STANDARD           0
#define ALG_3GPP_MANUAL             1

/**************************************************************************************************/
/*                                      4G Configurate Start                                      */
/**************************************************************************************************/
#define ALG_4G_MAX_CELL_NUM         5
#define ALG_4G_MAX_UE_NUM           1
#define ALG_4G_MAX_SUBFRAME_NUM     10
#define ALG_4G_MAX_DL_SUBFRAME_NUM  80

#define ALG_4G_PUSCH                0
#define ALG_4G_PUCCH                1
#define ALG_4G_PDSCH                2
#define ALG_4G_PDCCH                3
#define ALG_4G_PBCH                 4
#define ALG_4G_PCFICH               5
#define ALG_4G_PHICH                6

/* Precoding Type */
#define ALG_4G_SINGLE_AP            0
#define ALG_4G_SPATIAL_MUL          1
#define ALG_4G_TRANSMIT_DIVE        2

#define ALG_4G_TBS_AUTO             0
#define ALG_4G_TBS_MANUAL           1

/**************************************************************************************************/
/*                                       4G Configurate End                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                      5G Configurate Start                                      */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                       5G Configurate End                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                       NB-IOT Area Start                                        */
/**************************************************************************************************/
/* NBIOT Channel Type */
#define ALG_NBIOT_NPUSCH                0
#define ALG_NBIOT_NPRACH                1
#define ALG_NBIOT_NPDSCH                2
#define ALG_NBIOT_NPBCH                 3
#define ALG_NBIOT_NBSIB1                4
#define ALG_NBIOT_NPDCCH                5
#define ALG_NBIOT_NPSS                  6
#define ALG_NBIOT_NSSS                  7

/* Operating Mode */
#define ALG_NBIOT_STANDALONE            0
#define ALG_NBIOT_GUARDBAND             1
#define ALG_NBIOT_INBAND                2

/**************************************************************************************************/
/*                                        NB-IOT Area End                                         */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        WCDMA Area Start                                        */
/**************************************************************************************************/

/* WCDMA Scrambling Type */
#define ALG_WCDMA_SCRAMBLING_OFF            0
#define ALG_WCDMA_SCRAMBLING_LONG           1
#define ALG_WCDMA_SCRAMBLING_SHORT          2

/**************************************************************************************************/
/*                                        WCDMA Area End                                          */
/**************************************************************************************************/

#endif

enum PSDU_FORMAT
{
    HE_SU = 1,          // HE-SU
    HE_MU = 2,          // HE-MU
    HE_ERSU = 3,        // HE-ERSU
    HE_TriggerBase = 4, // HE-TriggerBase
};

// enum for trigger base
enum GILTF_SIZE
{
    GILTFSize0,
    GILTFSize1,
    GILTFSize2,
};
// end enum for trigger base

enum HELTF_TYPE
{
    LTFSize_1x = 1, // 1x LTFSize
    LTFSize_2x = 2, // 2x LTFSize
    LTFSize_4x = 4, // 4x LTFSize
};

enum CODE_RATE
{
    Rate_1_2 = 12, // 1/2
    Rate_2_3 = 23, // 2/3
    Rate_3_4 = 34, // 3/4
    Rate_5_6 = 56, // 5/6
};

// coding rate
enum WT_CODING_RATE
{
    WT_RATE_12, // 1/2
    WT_RATE_23, // 2/3
    WT_RATE_34, // 3/4
    WT_RATE_56,  // 5/6

    WT_BT_PACKETTYPE_BLE_TEST = 30,
    WT_BT_PACKETTYPE_BLE_AVD = 31, //advertising
};

//纠错码
enum WT_ERROR_CORRECTING_CODE
{
    WT_BCC,  // BCC
    WT_LDPC, // LDPC
};

enum WT_ULDL
{
    WT_DL,
    WT_UL
};

enum WT_PREAMBLE_TYPE
{
    WT_PREAMBLE_LONG,
    WT_PREAMBLE_SHORT,
};

/*================ 结果结构体定义 ========================================*/
#define UNVALID_VAL -999.99
#define MAX_SPECT_POINT_NUM 12000 //频谱图的最多点数，120M/10k(ah)（采样率/RBW）1G/100k,宽屏(不包含ah)
#define DF_Max_ChannalNum_11ax 2048
#define DF_Max_ChannalNum_11ax320 4096
#define MAX_SPECT_MARGIN_LEN         32

//算法基本结果
struct VsaBaseResult
{
    double PowerFrame;
    double PowerAll;
    double PowerPeak;

    double EvmAll;
    double EvmPeak;
    double EvmData;
    double EvmPilot;
    double EvmPsdu;
    double EvmShrPhr;

    double FreqOffset;
    double CarrierLeakage;
    double SymClkErr;
    double PhaseErr;
    double IQImbAmp;
    double IQImbPhase;
};

// 11ax triggerbase unused tone error
struct TbUnusedToneError
{
    int valid_flag;                        //界面画图有效性，0不生效，1生效
    int total_ru26_num;                    // RU26个数，横坐标长度
    int mask_valid_flag[BE_RU_COUNT];      //模板有效性，在1时候才画模板值
    int mask_value[BE_RU_COUNT];           //模板值，当有效性为1时才显示
    double unused_tone_error[BE_RU_COUNT]; // Tone Error值，整体是连续的
    double Reserved[128];                  //保留位
};

// WIFI平均结果结构体，存放所有需要平均
typedef struct
{
    struct VsaBaseResult BaseResult;                   //基本结果
    int SpectNum;                                      //频谱点数
    int ChannelNum;                                    //子通道数
    int SpectMarginNum;                            //频谱margin的点数
    double Spectrum[MAX_SPECT_POINT_NUM];              //频谱图数据点值
    Complex SpectFlatnessData[DF_Max_ChannalNum_11ax320]; //频谱平坦度数据值
    Complex margin_freq_vs_pwr[MAX_SPECT_MARGIN_LEN];   //频谱Margin，这个不做平均，只是做平均频谱结果重新计算的margin值
} VsaAverageResult;

typedef struct  
{
    struct VsaBaseResult BaseResult;   
    double Init_Freq_Error;
    double BR_Maxmum_Freq_Drift;
    double BR_Freq_Drift_Rate;
    double BR_BLE_Delta_F1_Max;
    double BR_BLE_Delta_F1_Avg;
    double BR_BLE_Delta_F1_Min;
    double BR_BLE_Delta_F2_Max;
    double BR_BLE_Delta_F2_Avg;
    double BR_BLE_Delta_F2_Min;
    double BR_BLE_Delta_F2_99p9PCT;

    double EDR_Omega_i;
    double EDR_Max_Omega_io;
    double EDR_Max_Omega_o;
    double EDR_DEVM_Avg;
    double EDR_DEVM_Peak;
    double EDR_Diff_Power;
    double EDR_Max_FreqVar;
    double EDR_DEVM_99PCT;
    double EDR_GuardTime;

    double BLE_FnMax;
    double BLE_F0FnMax;
    double BLE_F0FnAvg;
    double BLE_F0Fn5_Max;
    double BLE_Delta_F1F0;
    double BLE_Delta_F0F3;
    double BLE_Delta_F0Fn3;
    double BLE_Freq_offset_sync;
    double BLE_CTE_Pwr_Avg;
    double BLE_CTE_Pwr_Peak;
    double BLE_CTE_Pwr_Peak_sub_Avg;
    double BLE_CTE_Fsi_Max;
    double BLE_CTE_Fsi_Min;
    double BLE_CTE_Fs1_sub_Fp;
    double BLE_CTE_Fsi_sub_F0_Max;
    double BLE_CTE_Fsi_sub_Fsi3_Max;
    double BLE_CTE_Pwr_Ref_Avg;
    double BLE_CTE_Pwr_Ref_DevDivAvg;
    double BLE_CTE_Pwr_Pn_DevDivAvg_Max;
    
    double BLE_Delta_F1_99p9PCT;
   
    int DeltaF1Len;
    int DeltaF2Len;
	
	double EDR_GFSK_Power;
    double EDR_GFSK_Power_Peak;
    double EDR_DPSK_Power;
    double EDR_DPSK_Power_Peak;
	double EDR_DEVM_99PCT_RS;

    double Reserved[123];
}VsaBTCommResult;

typedef struct
{
	struct VsaBaseResult BaseResult;
	double Max_Freq_Drift;
	double Freq_Drift_Rate;
	double EvmAvg;
	double Evm99PCT;
	double CtlInfo_EvmAvg;
	double CtlInfo_EvmPeak;
	double CtlInfo_Evm99PCT;
	double Delta_Fd1_Avg;
	double Delta_Fd1_Max;
	double Delta_Fd1_Min;
	double Delta_Fd2_Avg;
	double Delta_Fd2_Min;
    double EvmPeak;
    int DeltaF2Len;
	int Res;
	double SLE_Delta_F2_99p9PCT;
	double Reserved[125];
}VsaSLECommResult;

struct DataBurstFields
{
    int validflag; ///< 0 = invalid, 1 = valid
    int Mod;       ///< 调制模式
    int Len;       ///< Symbol 数量
    int Res;       ///< 保留位
    double EVM;    ///< EVM，单位dB
    double Power;  ///< 功率，单位dBm

    int Reserved[16]; ///< 保留位
};

enum DataBurstEnum
{
    ENUM_FIELD_LSTF = 0,
    ENUM_FIELD_LLTF,
    ENUM_FIELD_LSIG,
    ENUM_FIELD_RLSIG,
    ENUM_FIELD_USIG,
    ENUM_FIELD_EHTSIG,
    ENUM_FIELD_HTSIG,
    ENUM_FIELD_HESIGA,
    ENUM_FIELD_HESIGB,
    ENUM_FIELD_GFSTF,
    ENUM_FIELD_VHTSIGA,
    ENUM_FIELD_VHTSIGB,
    ENUM_FIELD_DataSTF,
    ENUM_FIELD_DataLTF,
    ENUM_FIELD_DataNSTF,
    ENUM_FIELD_Data,
    ENUM_FIELD_S1GLTFN,
    ENUM_FIELD_S1GSIG,
    ENUM_FIELD_S1GSIGA,
    ENUM_FIELD_S1GSIGB,
    ENUM_FIELD_PHR,
    ENUM_FIELD_MAX = 32,
};

struct DataBurstFieldsInfo
{
    DataBurstFields Fieldtype[ENUM_FIELD_MAX];
};

struct DataInfo11ag
{
    double DataRate;
    int SymbolCnt;
    int PsduLen;
    int CodeingRate;
    int Modulation;
};

struct DataInfo11agPlus
{
    int Dulpicate; ///< 0 = Not dulplicate, 1 = dulplicate
    int DupBW;     ///< dulplicate bandwidth
    int LSig_BitLen;
    char LSig_Bit[24]; ////?0   1
    int PuncFlag;
    int Punc20Len;
    int Punc20Flag[16];
    int Reserved[103];
};

struct DataInfo11b
{
    double DataRate;
    int Length;
    int PsduLen;
    int PreambleType;
    int SfdPass;
    int HeaderPass;
    int pad;
};

struct DataInfo11n
{
    double DataRate;
    int SymbolCnt;
    int CodeingRate;
    int PsduLen;
    int Modulation;

    int HTSigValid;
    int Mcs;
    int Cbw;
    int HTLen;
    int Smooth;
    int NotSnd;
    int Aggreg;
    int STBC;
    int FecCode;
    int ShortGi;
    int ExtSStrms;
    int Crc;
    int Tail;

    int LsigValid;
    int Rate;
    int Len;
    int Parity;
    int LsigTail;
};

struct DataInfo11nPlus
{
    int LSig_BitLen;    ///< L-SIG bit 长度
    char LSig_Bit[24];  ///< L-SIG bit 数据
    int HTSig_BitLen;   ///< HT-SIG bit 长度
    char HTSig_Bit[48]; ///< HT-SIG bit 数据

    int Reserved[128]; ///< 保留位
};

struct DataInfo11ac
{
    double DataRate;
    int SymbolCnt;
    int CodeingRate;
    int PsduLen;
    int Modulation;

    int VHTSigAValid;
    int Mcs;
    int Bw;
    int STBC;
    int FecCode;
    int Crc;
    int ShortGi;
    int Nvhtltf;
    int Nsts;
    int Nss;
    int GroupId;

    int VHTSigBValid;
    int McsB;
    int Len;

    int LsigValid;
    int Rate;
    int LsigLen;
    int pad;
};

struct DataInfo11acPlus
{
    int PartialAID; ///< Partial AID
    int TXOP_PS_NOT_ALLOWED;
    int Beamformed;         ///< 0 = not beamformed, 1 = beamformed
    int SoundingNDP;        ///< sounding NDP
    int LDPC_Extra_Symbol;  ///< LDPC extra symbol number
    int ShortGI_NsymDisamb; ///< Short GI NSYM disambiguity

    int LSig_BitLen;         ///< L-SIG bit length
    char LSig_Bit[24];       ///< L-SIG bit data
    int VHTSigA_BitLen;      ///< SIG-A bit length
    char VHTSigA_Bit[48];    ///< SIG-A bit data
    int VHTSigB_BitLen[4];   ///< SIG-B bit length for users
    char VHTSigB_Bit[4][29]; ///< SIG-B bit data for users

    int Reserved[128];
};

// ac mu mimo information
#define MAX_AC_USER_NUM 4
struct DataInfoAcMuMimo
{
    int MuMimoFlag;
    int MuMimoUserNum;
    int BW;
    int SymbolCnt;
    int STBC;
    int Nvhtltf;
    int ShortGi;
    int GroupId;
    int Reserved[66];

    struct
    {
        double DataRate;
        int PsduLen;
        int Crc;
        int Mcs;
        int Modulation;
        int FecCode;
        int CodingRate;
        int Nsts;
        int Nss;
        double AllEvm;
        double DataEvm;
        double PilotEvm;
        double UserPower;
        int UserNstsFlag[MAX_DF_NUM];
        double UserPowerNsts[MAX_DF_NUM];
        double PilotEvmNsts[MAX_DF_NUM];
        double DateEvmNsts[MAX_DF_NUM];
        double AllEvmNsts[MAX_DF_NUM];
        int vthbLength;
        int Reserved1;
        double Reserved[63];
    } UserInfo[MAX_AC_USER_NUM];
};

struct DataInfo11ax
{
    int HeSigAValid;
    int Bw;
    int PsduFormat;
    int UserNum;
    int SymbolCnt;
    int HeltfNum;
    int HeltType;
    int Reserved; //预留字段，主要为了字节对齐
    double DataRate;
    double HeltfLen;
    double GILen;
    double HeDataSymLen;
    double FrameLen;

    int HeSigBValid;
    int SigBDcm;
    int SigBMcs;
    int SigBSymbol;
    int Common8BitLen;
    int Common8Bit[COMMON8BIT_MAX_NUM];

    int PreTxBF;
    int LDPCExtra;
    int PE;
    int PeLen;
    int PreFEC;
    int Doppler;
    int Midamble_Periodicity;
    int MuMimoFlag;      // 1采用mu-mimo新结构读取，0保持原来模式不变，按原方式读取
    int SIGBCompression; // mu-mimo下是表示1为全带宽，0为部分带宽
    int RealUserNum;     //有效的用户数

    int TXOP;
    int SpatialReuseNum; //控制Spatial Reuse输出数量，TB 4个
    int SpatialReuse[4]; //非TB时1个，TB时4个
    int TFReserved[9];   // TB特有的，其余格式为0
    int SoundingNDP;     ///< 0=not NDP, 1 = NDP
    int PuncturingMode;  ///< 0 = not puncturing, 1 = puncturing

    //Lsig
    int LsigValid;
    int LsigRate;
    int LsigLen;

    int Reserved1[105];  // 预留字段，主要为了字节对齐

    struct
    {
        int Valid;
        int ULDL;
        int UserId;
        int ToneWide;
        int ToneIdx;
        int Isegment;
        int Mcs;
        int Modulation;
        int LDPC;
        int CodingRate;
        int STBC;
        int DCM;
        int Nsts;
        int PsduCrc;
        int PsduLen;
        int UserAID;
        double PowerFactor;
        double UserPower;
        double UserRate;
        double PilotEvm;
        double DataEvm;
        double AllEvm;
        int UserNstsFlag[MAX_DF_NUM];
        double UserPowerNsts[MAX_DF_NUM];
        double PilotEvmNsts[MAX_DF_NUM];
        double DateEvmNsts[MAX_DF_NUM];
        double AllEvmNsts[MAX_DF_NUM];
        int Beamformed;
        int NSS;
        double Reserved[27]; // 预留字段
    } UserInfo[MAX_USER_NUM];
};

struct UserInfo11ax
{
    int ULDL;
    int UserId;
    int ToneWide;
    int ToneIdx;
    int Isegment;
    int Mcs;
    int Modulation;
    int LDPC;
    int CodingRate;
    int STBC;
    int DCM;
    int Nsts;
    int PsduCrc;
    int PsduLen;
    int UserAID;
    int Reserved1; //对齐，预留字段
    double PowerFactor;
    double UserPower;
    double UserRate;
    double PilotEvm;
    double DataEvm;
    double AllEvm;
    int UserNstsFlag[MAX_DF_NUM];
    double UserPowerNsts[MAX_DF_NUM];
    double PilotEvmNsts[MAX_DF_NUM];
    double DateEvmNsts[MAX_DF_NUM];
    double AllEvmNsts[MAX_DF_NUM];
    int Beamformed;
    int NSS;
    double Reserved[31]; // 预留字段
};

// mu-mimo 单个ru的结构体
struct RuOfdmaInfo11ax
{
    int ValidFlag; // Ru有效性，0为无效，0时其他内容都显示--
    int Ue_Num;    // Ru内的用户数
    int RuNsts;    // Ru的总流数
    int Reserved1; //对齐，预留字段
    double q_matrix_r[8][8];
    double q_matrix_i[8][8];
    UserInfo11ax User[8];
    double Reserved[64]; // 预留字段
};

typedef struct
{
    int validflag; //有效性
    int AntNum;
    int StsNum;
    Complex QMatrix[MAX_DF_NUM][MAX_DF_NUM];
    Complex QMatrixInv[MAX_DF_NUM][MAX_DF_NUM];
    double Reserved[64]; // 预留字段
} QMatrixInformation;

struct EHT_COMMON_INFO
{
    int PPDU;
    int RUCnt;
    int UserCnt;
    int ActiveUserCnt;
    int NSTS;
    int SymbolCnt;
    int MUMIMO;
    int Reserved_0;
    double DataRate;
    double PELen;
    double GILen;
    double LTFLen;
    double SymbolLen;
    double PreambleLen;
    double DataLen;
    double FrameLen;
    double Reserved[32];
};

struct EHT_USIG_INFO
{
    int CRC;
    int PhyVersion;
    int BW;
    int ULDL;
    int BSSColor;
    int TXOP;
    int Disregard;
    int B25Valid;
    int CompressionMode;
    int U2B2Valid;
    int PuncBitLen;
    int PuncBit[20];
    int U2B8Valid;
    int SIGMCS;
    int SIGSymbol;
    int SoundingNDP;
    int TB_ValidB2;
    int TB_SIG1_Disregard;
    int TB_SIG2_Disregard;
    int TB_SpaReuse[2];
    double Reserved[29]; // 预留字段
};

struct EHT_SIG_INFO
{
    int CRC;
    int SpatialReuseLen;
    int SpatialReuse[4];
    int GILTFSize;
    int LTFSymbol;
    int LDPCExtra;
    int PreFEC;
    int PEDisambiguity;
    int Disregard; //字节对齐
    int Common9BitNum;
    int EHTSIG;
    int Common9Bit[16];
    int EhtLtfType;
    int ReservedInt;     // 预留字段
    double Reserved[31]; // 预留字段
};

struct EHT_LSIG
{
    int CRC;
    int PSDULen;
    double DataRate;
    int ParityBit;
    int ParityCheck;
    int Reserved[32]; // 预留字段
};

struct DataInfo11Be
{
    EHT_COMMON_INFO common;
    EHT_USIG_INFO usig;
    EHT_SIG_INFO sig;
    EHT_LSIG lsig;
};

/// EHT User的结构体
struct UserInfo11Be
{
    int UserID;
    int AID;
    int MCS;
    int NSS;
    int NSTS;
    int Beamformed;
    int CodingType;
    int DCM;
    int SpatialConfig;
    int CodingRate;
    int Modulation;
    int PSDUCRC;
    int PSDULength;
    int ampduValid;
    int ampduNum;

    double Power;
    double Rate;
    double PilotEvm;
    double DataEvm;
    double AllEvm;
    int NstsFlag[MAX_DF_NUM];
    double PowerNsts[MAX_DF_NUM];
    double PilotEvmNsts[MAX_DF_NUM];
    double DataEvmNsts[MAX_DF_NUM];
    double AllEvmNsts[MAX_DF_NUM];
    int ToneWide;
    int ToneIdx;
    double Reserved[31]; // 预留字段
};

// EHT RU的结构体
struct EHT_RU
{
    int ValidFlag; // RU有效性，0为无效，0时其他内容都显示--
    int UeNum;     // RU内的用户数
    int RUNsts;    // RU的总流数
    int ToneWide;
    int ToneIndex;
    int NSD;
    int NSP;
    int Reserved1; //对齐，预留字段

    double PilotEVM; // Compsite value
    double DataEVM;  // Compsite value
    double AllEVM;   // Compsite value
    double Power;    // Compsite value
    int NstsFlag[MAX_DF_NUM];
    double PowerNsts[MAX_DF_NUM];
    double PilotEvmNsts[MAX_DF_NUM];
    double DataEvmNsts[MAX_DF_NUM];
    double AllEvmNsts[MAX_DF_NUM];

    UserInfo11Be User[8];
    double Reserved[64]; // 预留字段
};

struct DataInfoBT
{
    double InitFreqErr;
    double OmegaI;
    double OmegaIO;
    double OmegaO;
    double DevmAvg;
    double DevmPeak;
    double DiffPower;
    double MaxFreqVar;
    double Devm;
    double FreqDrift;
    double FreqDriftRate;
    double F1Max;
    double F1Avg;
    double F2Max;
    double F2Avg;
    double FnMax;
    double F0FnMax;
    double F1F0;
    double FnFn5Max;
    double F0F3;
    double FnFn3;
    int DataRate;
    int PktType;
    int PktLen;
    int PayloadHeader;
    int CrcStatus;
};

#define Slot_MAX_CTENum 74
struct DataInfoBTPlus
{
    int LAP;
    int UAP;
    int CTEInfo;
    int CTEType;
    int CTE_DurationT;
    int EDR_SynSeq_ErrBit_Num;
    int EDR_Trailer_ErrBit_Num;
    int LT_ADDR;
    int Flow;
    int ARQN;
    int SEQN;
    int LLID;
    int mFlow;
    int PayLoadSize;
    int Payload_EIR;
    int Payload_SR;
    int Payload_ClassofDevice;
    int Payload_LTAddr;
    int Payload_CLK27b2;
    int BLEMapperS;
    double F0FnAvg;
    double Freq_offset_sync;
    double EDR_GuardTime;
    double CTE_Pwr_Avg;
    double CTE_Pwr_Peak;
    double CTE_Pwr_Peak_sub_Avg;
    double CTE_Fsi_Max;
    double CTE_Fsi_Min;
    double CTE_Fs1_sub_Fp;
    double CTE_Fsi_sub_F0_Max;
    double CTE_Fsi_sub_Fsi3_Max;
    double CTE_Pwr_Ref_Avg;
    double CTE_Pwr_Ref_DevDivAvg;
    double CTE_Pwr_Pn_DevDivAvg_Max;
    double CTE_Pwr_Avg_Slot[Slot_MAX_CTENum];
    double Delta_F1_Min;
    double Delta_F2_Min;
    int Header_bin[54];
    int VoiceField[10];
    double Delta_F1_99p9PCT;
    double Delta_F2_99p9PCT;
    double EDR_GFSKPower;
    double EDR_DPSKPower;
    int Pattern;
    int EnhancedMode;
	int Payload_header_len;
	int Res;
    double Reserved[109];
};

struct DataInfoZwave // zwave info
{
    double InitFreqErr;
    double FreqDeviRMS;
    double FreqDeviMAX;
    double FreqDeviMIN;
    double ZeroErrRMS;
    double ZeroErrPeak;
    double SymbolClockErr;
    double SymbolClockJitter;
    double DataRate;
    int PsduLen;
    int PsdeCrcPass;
    int SymbolCount;
    int Reserved[63];
};

typedef struct
{
    int Mcs;
    int BroadType;
    int PacketType;
    int DataLength;
    int Reversed[8];
} SLECtrlInfoTypeA1;

typedef struct
{
    int Mcs;
    int PacketType;
    int EmptyPacketInd;
    int SndSN;
    int RevSN;
    int FlowCtrlInd;
    int RevSysFrmInd;
    int DataLength;
    int Reversed[4];
} SLECtrlInfoTypeA2;

typedef struct
{
    int Mcs;
    int PacketType;
    int EmptyPacketInd ;
    int SndSN;
    int RevSN;
    int FlowCtrlInd;
    int ScheduleInd;
    int DataLength;
    int Reversed[4];
} SLECtrlInfoTypeA3;

typedef struct
{
    int Mcs;
    int PacketType;
    int EmptyPacketInd;
    int SndSN;
    int RevSN;
    int FlowCtrlInd;
    int ScheduleInd;
    int DataLength;
    int Reversed[4];
} SLECtrlInfoTypeA4;

typedef struct
{
    int Mcs;
    int PacketType;
    int EmptyPacketInd;
    int SndSN;
    int RevSN;
    int FlowCtrlInd;
    int ScheduleInd;
    int DataLength;
    int Reversed[4];
} SLECtrlInfoTypeA5;

typedef struct
{
    int Mcs;
    int PacketType;
    int DataPacketSN;
    int DataPacketGrp;
    int EndInd;
    int RevSysFrmInd;
    int DataLength;
    int Reversed[5];
} SLECtrlInfoTypeA6;

typedef struct
{
    int Mcs;
    int DataPacketSN;
    int DataPacketGrp;
    int DataLength;
    int Reversed[8];
} SLECtrlInfoTypeA7;

typedef struct
{
    int FrmFormatInd;
    int HarqFeedback;
    int DataPacketSN;
    int Mcs;
    int DataLength;
    int FlowCtrlInd;
    int UpperLinkInd;
    int Reversed[5];
} SLECtrlInfoTypeB1;

typedef struct
{
    int HarqFeedback;
    int FlowCtrlInd;
    int UpperLinkInd;
    int Reversed[9];
} SLECtrlInfoTypeB2;

typedef struct
{
    int DataPacketGrp;
    int DataPacketSN;
    int Mcs;
    int DataLength;
    int FlowCtrlInd;
    int MaxDataPacketSNInd;
    int Reversed[6];
} SLECtrlInfoTypeB3;

typedef struct
{
    int BrdcastSetFlag;
    int BrdcastSetUpdateInd;
    int DataPacketSN;
    int Mcs;
    int DataLength;
    int FlowCtrlInd;
    int MaxDataPacketSNInd;
    int Reversed[5];
} SLECtrlInfoTypeB4;

typedef struct
{
    int MsgTypeInd;
    int ConnectInd;
    int DiscoveryInd;
    int DirectInd;
    int NonDirectInd;
    int DataUpdateInd;
    int Mcs;
    int DataLength;
    int Reversed[4];
} SLECtrlInfoTypeB5;

struct DataInfoSparkLink
{
    int FrmType;
    int Bandwidth;
    int PID;
    int PayloadLen;
    int PayloadCrcType;
    int PayloadCrc;
    int CtrlInfoType;
    int CtrlInfoCrc;
    double Reserved1;
    double Delta_fd1_Avg;
    double Delta_fd1_Max;
    double Delta_fd1_Min;
    double Delta_fd2_Avg;
    double Delta_fd2_Min;
    double EvmAvg;
    double EvmPeak;
    double Evm99PCT;
    double Init_Freq_Error;
    double Max_Freq_Drift;
    double Freq_Drift_Rate;
    double CtrlInfoEvmAvg;
    double CtrlInfoEvmPeak;
    double CtrlInfoEvm99PCT;
    double ZeroCrossingErr;
    double SymClkErr;
    double MaxTimeDev;
    double Delta_fd2_99PCT;
    double Reserved[120]; //保留位
};

struct DataInfoWiSun
{
    int PHR_Flag;
    int Rate_MCS;
    int Frame_Len;
    int Scrambler;
    int Symbol_Cnt;
    int Coding_rate;
    int Modulation;
    int PSDU_Cnt;
    int PSDU_CRC;
    double Date_Rate;
    double BW;
    int PHR_Bit[36];
    int CRC_Bit[8];
	int Spreading_Mode;
    int Rate_Mode;
    int Chip_Rate;
    int PhyFSKPreambleLength;
	
	int PhySunFskSfd;
    int PhyFcsFecEnabled;
    int PhyFSkFecScheme;
    int PhyFskFecInterleavingRsc;
    int ModeSwitch;
    int FCSType;
    int DataWhitening;
    int CRC;
	
	double Fdev_Min_2fsk;
    double Fdev_Min_2fsk_Value;
    double Fdev_Max_2fsk;
    double Fdev_Max_2fsk_Value;
    double Fedv_Rms_2fsk;
    double Zero_Cross_Tolerance_Min;
    double Zero_Cross_Tolerance_Max;
    double Zero_Cross_Tolerance_Rms;
	
    double Reserved[114]; //保留位
};

struct CtrlInfoCfg
{
    SLECtrlInfoTypeA1 CtrlInfoTypeA1;
    SLECtrlInfoTypeA2 CtrlInfoTypeA2;
    SLECtrlInfoTypeA3 CtrlInfoTypeA3;
    SLECtrlInfoTypeA4 CtrlInfoTypeA4;
    SLECtrlInfoTypeA5 CtrlInfoTypeA5;
    SLECtrlInfoTypeA6 CtrlInfoTypeA6;
    SLECtrlInfoTypeA7 CtrlInfoTypeA7;
    SLECtrlInfoTypeB1 CtrlInfoTypeB1;
    SLECtrlInfoTypeB2 CtrlInfoTypeB2;
    SLECtrlInfoTypeB3 CtrlInfoTypeB3;
    SLECtrlInfoTypeB4 CtrlInfoTypeB4;
    SLECtrlInfoTypeB5 CtrlInfoTypeB5;
};

#ifndef MAX_SEGMENT_11BA_NUM
#define MAX_SEGMENT_11BA_NUM 4
#endif
struct DataInfo11ba
{
    int BW;   //MHz, 20,40,80
    int LSigParityPassed;//0:fail; 1:pass
    int LSigRate;
    int LSigLength;
    int SubChanNum;
    int PunctureFlag[MAX_SEGMENT_11BA_NUM];//0:NO;1:Yes
    int DataRateMode[MAX_SEGMENT_11BA_NUM];//0:LDR; 1:HDR
    int PsduLength[MAX_SEGMENT_11BA_NUM];//0-22
    int PsduCRC[MAX_SEGMENT_11BA_NUM];//0:fail; 1:pass
    double Reserved[128]; //保留字段
};

struct DataInfo11az
{
    int HeSigAValid;
    int PuncturingMode;
    int Bw;
    int PsduFormat;
    int RuNum;
    int UserNum;
    int HeltfNum;
    int HeltType;
    double HeltfLen;
    double GILen;
    double FrameLen;
    double PeLen;
    int BSSColor;
    int PreTxBF;
    int LDPCExtra;
    int PE;
    int PreFEC;
    int Doppler;
    int Midamble_Periodicity;
    int TXOP;
    int SpatialReuseNum;      //控制Spatial Reuse输出数量，TB 4个
    int SpatialReuse[4];                  //非TB时1个，TB时4个
    int SigABitLen;
    char SigABit[52];
    //Lsig
    int LsigValid;
    int LsigRate;
    int LsigLen;              //bytes
    int LsigBitLen;           //bits
    char LsigBit[24];
    int Reserved[128];                    // 预留字段
};

struct WURPHYInfo
{
    int SubChanNum;
    int Reserved1; //对齐字节
    double SyncSymbPwrRatio[MAX_SEGMENT_11BA_NUM];
    double DataSymbPwrRatioMax[MAX_SEGMENT_11BA_NUM];
    double DataSymbPwrRatioAvg[MAX_SEGMENT_11BA_NUM];
    double DataSymbPwrRatioMin[MAX_SEGMENT_11BA_NUM];

    double CorrMetrMax;
    double CorrMetrAvg;
    double Reserved[128];//保留字段
};

struct  AH_S1G_Short_Preamble_Info
{
    int ValidFlag;            //有效标志
    int ReservedB0;          //保留位
    int BW;                   //带宽
    int STBC;                 //是否空时块编码
    int ULDL;                 //上行链路指示
    int NSS;
    int Nsts;
    int ID;
    int ShortGi;              //是否是短GI
    int CodingType;           //编码方式 0：BCC；1：LDPC
    int LDPCExtra;
    int MCS;
    int Smoothing;
    int IsAggregation;        //是否聚合
    int Length;
    int ResponseIndication;
    int TravelingPilots;
    int NDPIndication;        //NDP指示
    int CRC;
    int Tail;                 //尾比特
    int SigBitLen;
    int SigBit[48];
    int Reserved[20];
};

struct AH_S1G_Long_Preamble_SU_Info
{
    int ValidFlag;            //有效标志
    int BW;                   //带宽
    int STBC;                 //是否空时块编码
    int ULDL;                 //上行链路指示
    int NSS;
    int Nsts;
    int ID;
    int ShortGi;              //是否是短GI
    int CodingType;           //编码方式 0：BCC；1：LDPC
    int LDPCExtra;
    int MCS;
    int BeamChange;
    int Smoothing;
    int IsAggregation;        //是否聚合
    int Length;
    int ResponseIndication;
    int TravelingPilots;
    int ReservedA2B12;
    int CRC;
    int Tail;                 //尾比特
    int SigABitLen;
    int SigABit[48];
    int Reserved[20];
};

struct AH_S1G_Long_Preamble_MU_Info
{
    int ValidFlag;            //有效标志
    int BW;                   //带宽
    int STBC;                 //是否空时块编码
    int ReservedA1B2;
    int NSS;
    int Nsts;
    int GroupID;
    int CodingTypeI;              //是否是短GI
    int CodingTypeII;           //编码方式 0：BCC；1：LDPC
    int ReservedA2B1;
    int Length;
    int ResponseIndication;
    int TravelingPilots;
    int CRC;
    int Tail;                 //尾比特
    int SigABitLen;
    int SigABit[48];
    int Reserved[20];
};

struct AH_S1G_S1M_Preamble_Info
{
    int ValidFlag;            //有效标志
    int BW;                   //带宽
    int STBC;                 //是否空时块编码
    int NSS;
    int Nsts;
    int ShortGi;              //是否是短GI
    int CodingType;           //编码方式 0：BCC；1：LDPC
    int LDPCExtra;
    int ReservedB6;
    int MCS;
    int IsAggregation;        //是否聚合
    int Length;
    int ResponseIndication;
    int TravelingPilots;
    int Smoothing;
    int NDPIndication;
    int CRC;
    int Tail;                 //尾比特
    int SigBitLen;
    int SigBit[48];
    int Reserved[20];
};

#define MAX_AH_USER_NUM 4
struct DataInfo11ah
{
    int PreambleType;         //前导类型,类型见枚举AH_PREAMBLE_TYPE
    int UserNumber;           //用户总数
    int Reserved[20];

    AH_S1G_Short_Preamble_Info S1GShortInfo;
    AH_S1G_Long_Preamble_SU_Info S1GLongSuInfo;
    AH_S1G_Long_Preamble_MU_Info S1GLongMuInfo;
    AH_S1G_S1M_Preamble_Info S1GS1MInfo;

    struct
    {
        double DataRate;
        int PsduLen;
        int Crc;
        int Mcs;
        int Modulation;
        int FecCode;
        int SigBMCS;
        int SigBCRC;
        int SigBTail;
        int SigBBitLen;
        char SigBBit[32];             //最多29bit，剩余的字节对齐
        int CodingRate;
        int Nsts;
        int Nss;
        double AllEvm;
        double DataEvm;
        double PilotEvm;
        double UserPower;
        int UserNstsFlag[MAX_DF_NUM];
        double UserPowerNsts[MAX_DF_NUM];
        double PilotEvmNsts[MAX_DF_NUM];
        double DateEvmNsts[MAX_DF_NUM];
        double AllEvmNsts[MAX_DF_NUM];
        double Reserved[56];
    }UserInfo[MAX_AH_USER_NUM];
};
//end 11ah

typedef struct
{
    int evm_chip_start; // EVM计算开始chip号
    int evm_chip_count; // EVM计算的chip个数
} st11BChip;

enum WT_ALZ_PARAM_TYPE
{
    WT_ALZ_PARAM_COMMON,
    WT_ALZ_PARAM_FFT,
    WT_ALZ_PARAM_WIFI,
    WT_ALZ_PARAM_BT,
    WT_ALZ_PARAM_ZIGBEE,
    WT_ALZ_PARAM_ZWAVE,
    WT_ALZ_PARAM_GLE,
    WT_ALZ_PARAM_3GPP,
    WT_ALZ_PARAM_WSUN,
};

// WIFI分析参数
typedef struct
{
    int Demode;     //分析模式,WT_DEMOD_ENUM,默认值WT_DEMOD_UNKNOW
    int AutoDetect; //信号带宽自动检测，详见WT_BANDWIDTH_DETECT_ENUM,默认值WT_BW_AUTO_DETECT

    int Method11b;      // 802.11 b EVM方式.  WT_11B_METHOD_ENUM,默认值WT_11B_STANDARD_TX_ACC
    int DCRemoval;      // 802.11 b 直流去除. WT_DC_REMOVAL_ENUM,默认值WT_DC_REMOVAL_OFF
    int EqTaps;         // 802.11 b 均衡类型. WT_EQ_ENUM,默认值WT_EQ_OFF
    int PhsCorrMode11B; // 802.11 b 相位跟踪. WT_PH_CORR_11b_ENUM,默认值WT_PH_CORR_11b_ON

    int PhsCorrMode;  // 802.11 a/b/g/n/ac 相位跟踪. WT_PH_CORR_ENUM,默认值WT_PH_CORR_SYM_BY_SYM
    int ChEstimate;   // 802.11 a/g/n/ac 通道估计. WT_CH_EST_ENUM,默认值WT_CH_EST_RAW
    int SynTimeCorr;  // 802.11 a/g/n/ac 时序跟踪. WT_SYM_TIM_ENUM,默认值WT_SYM_TIM_ENUM
    int FreqSyncMode; // 802.11 a/g/n/ac 频率同步. WT_FREQ_SYNC_ENUM,默认值WT_FREQ_SYNC_ENUM
    int AmplTrack;    // 802.11 a/g/n/ac 幅度跟踪. WT_AMPL_TRACK_ENUM,默认值WT_AMPL_TRACK_ENUM
    int OfdmDemodOn;  // OFDM Demodulation, 1:on, 0:off,默认值1

    int MIMOAnalysisMode;    // MIMO分析模式,默认值0
    int MimoMaxPowerDiff;    // MIMO不同signal之间的最大功率差异，差异大于该值较低功率的会被当成干扰信号,默认值30
    int SpectrumMaskVersion; // SpecturmMask Version ，0：Ieee2009；1：Ieee2012,只在11n选择两种不同模板时使用~
    int ClockRate;           //压缩子载波间隔,赋值详见枚举
    short NonHTDupBW;        //分析nonHT Duplicate打孔时需要指定带宽,可取值：0,20,40,80,160,320；默认值为0
    char FullCRCFlag;        //全译码开关，0：FCS CRC； 1：FEC CRC
    char IQCompensation;

    char PreambleAverage;
    char EqualizerSmoothing;
    char LdpcDecodeIterationTimes;
    char SfoCompensation;         //sfo优化

    char OBWCalcFlag;
    char HardwareDecodeFlag;        ////是否开启硬件译码，0-软件译码，1-硬件译码
    char FrameType11AH;             //11ah的分析帧类型，0：S1G（默认值），1：S1G_DUP_1M,2:S1G_DUP_2M
    char ICISuppression;

    char SpecialAnalyzeStream;      //当MIMOAnalysisMode为1，single signal analysis时，指定分析指定流ID，范围：0~7
    char ReservedC[2];              //对齐保留位
    char EnbaleEmbeddedBSSID;       // 0/1
	int EmbeddedBSSID[4];           // EmbeddedBSSID[*]: use 0 ~ 15bit

    int Reserved[251];
} AlzParamWifi;

// BT分析参数
typedef struct
{
    int BTDataRate;  // Bluetooth速率, WT_BT_DATARATE,默认值WT_BT_DATARATE_Auto
    int BTPktType;   // Bluetooth包类型, WT_BT_PACKETTYPE(默认设置为WT_BT_PACKETTYPE_NULL即可)
    int BTBleEnhance;                   //增强模式0:OFF Standard Rate; 1:ON Twice Standard Rate
    char BTBlePDUPktype;                //0:Test; 1:Advertising
    char BTBleSyncMode;                 //0:Preamble; 1:AccessAddress,当且仅当BTBlePDUPktype为1，Advertising时，该值有效
    unsigned char BTBleChannelIndex;    //信道索引，当且仅当BTBlePDUPktype为1，Advertising时，该值有效
    char ACPViewRangeType;              //ACP显示宽度
    unsigned int BTBleAccessAddress;
    unsigned int ACPSweepTimes;         //按SLE计算方式时，ACP扫描计算的次数，默认1，范围：1~10
    char ReservedC[3];                  //保留
} AlzParamBT;

// ZigBee分析参数
typedef struct
{
    int Optimize; //是否开启优化，0 关闭， 1 开启，默认值0
} AlzParamZigBee;

//通用分析参数
typedef struct
{
    int IQSwap;          // IQ交换(频谱反转). WT_IQ_SWAP_ENUM,默认值WT_IQSWAP_DISABLED
    int IQReversion;     // IQ极性反转.WT_IQ_Reversion_ENUM,默认值WT_IQ_IQReversion_DISABLED
    int ManualPktStart;  //指定数据包在多少个采样点后为起始,默认值0
    int FilterPktByTime; //短包过滤，低于此时间长度的包会被过滤,默认值0
    int FilterPktByType; //包类型过滤,默认值0
    double FreqOffset;   //输入信号频率偏移,默认值0
    int SpectrumFlag;
    int CcdfFlag;
    int AvgTimes;       //算法平均次数(采集次数低于此则不平均)
    int HmatrixEnable;                        //H矩阵使能开关，0：OFF； 1：ON
    int HmatRxAntennaNum;                     //H矩阵 Rx 天线数
    int HmatTxAntennaNum;                     //H矩阵 Tx 天线数
    Complex HMatValue[8][8];                      //H矩阵数据，Complex，包含实部虚部
    int Reserved[5];  //保留
} AlzParamComm;

typedef struct
{
    // FFT分析参数
    int WindowType;      // WT_WINDOW_TYPE_ENUM,默认值WT_WINDOW_TYPE_Hann
    double Rbw;          // resolution bandwidth, 默认值100000
    double Reserved[10]; //保留
} AlzParamFFT;

enum WT_ZWAVE_DATARATE
{
    WT_ZWAVE_DATARATE_R1 = 1,
    WT_ZWAVE_DATARATE_R2 = 2,
    WT_ZWAVE_DATARATE_R3 = 3,
};

// Zwave的分析参数
struct AlzParamZwave
{
    int ZwaveRate; //速率设置，见枚举WT_ZWAVE_DATARATE，取值1=R1，2=R2，3=R3
    int Reserved[32];
};

struct AlzParamSparkLink
{
    int FrmType;                                   /* Frame Type: 0-4 (auto, type1, type2, type3, type4) */
    int Bandwidth;                                 /* Bandwidth: 0(auto); 1(1M); 2(2M); 3(4M) */
    int CtrlInfoType;                              /* Control Information Type, reference Alg_GleCtrlInfoType */ 
    int PayloadCrcType;                            /* Payload CRC Type: 1(CRC24); 2(CRC32) */
    unsigned int PayloadCrcSeed;                   /* Payload CRC Seed */
    int SlotIndex;                                 /* Slot Index */
    int PilotDens;                                 /* Pilot Density: 4, 8, 16 */
    int BoardIndex;
    int PolarEncodePathNum;
    int PID;
    int Scramble;
    int ChannelType;
    int FreqRange;
    int MSeqNo;
    int SyncSource;
    int SyncSeq[64];
    int PayloadAnalyzeMode;                          //分析模式 取值：1 User Defined（默认值）；0 Standard
    int MCS;                                           //调制编码方式索引 取值范围0-11,默认值为0
    int RaisedRootCosineFilter;                     //根升余弦滤波器 取值：0:OFF；1:ON（默认值） 
    int Reserved[57];
};

//3GPP的分析参数
/******************** 4G LTE Start ********************/

/******************** 4G LTE End ********************/

/******************** 5G NR Start ********************/

/******************** 5G NR End ********************/

/******************** NB-IOT Start ********************/

/********************* NB-IOT End *********************/

/******************** WCDMA Start ********************/

/******************** WCDMA End ********************/

struct AlzParam3GPP
{
    int analyzeGroup;
    int DcFreqCompensate;
    int Standard;
    int SpectrumRBW;
    int PkgAlzOffset;
    int MeasPowerGraph;
    int MeasSpectrum;
    int MeasCCDF;
    int ErrorCode;
    int rf_band[ALG_3GPP_MAX_STREAM];
    int rf_channel[ALG_3GPP_MAX_STREAM];
    int TriggerType; /* Free Run; External; Signal */
    double PreTime;
    int Reserved1[16];
    union {
        Alg_3GPP_AlzIn4g LTE;
        Alg_3GPP_AlzIn5g NR;
        Alg_3GPP_AlzInNBIOT NBIOT;
        Alg_3GPP_AlzInWCDMA WCDMA;
        Alg_3GPP_AlzInGSM GSM;
        Alg_4G_ListInType LTELIST; //lte分支合并主线，先屏蔽

        char MaxReserved[1024 * 500]; // 500K
    };
};

// 参考文件
union AlzParam3GPPAlign
{
    Alg_3GPP_AlzInGSM GSM;
    Alg_3GPP_AlzInWCDMA WCDMA;
    Alg_3GPP_AlzIn4g LTE;
    Alg_3GPP_AlzIn5g NR;
    Alg_3GPP_AlzInNBIOT NBIOT;
    char Align[500 * 1024];  // 占位
};

// METER\SCPI使用, 注意这个结构体要与meter对齐
struct AlzParam3GPP_SCPI
{
    int Version;    // 1,2,3, 目前只支持1, 后续结构体变大后，版本号依次累加
    int analyzeGroup;
    int DcFreqCompensate;
    int Standard;
    int SpectrumRBW;
    int rf_band[ALG_3GPP_MAX_STREAM];
    int rf_channel[ALG_3GPP_MAX_STREAM];
    int Reserved1[31];
    AlzParam3GPPAlign Param;
};

struct AlzParamWiSun
{
    int Mr_OFDM_Option;
    int Phy_OFDM_Interleaving;
	int PhsCorrMode;
    int ChEstimate;
    int SynTimeCorr;
    int FreqSyncMode;
    int AmplTrack;
    int SfoCompensation;
    int ClockRate;
    int IQCompensation;
	int FreqBand;
	int Demode; 
	int DataRate;
	int AcpCalMode;
	double ModulationIndex;
    int ChannelSpacing;
    int Reserved[25];
};

typedef union
{
    AlzParamWifi analyzeParamWifi;
    AlzParamBT analyzeParamBt;
    AlzParamZigBee analyzeParamZigBee;
    AlzParamFFT analyzeParamFft;
	AlzParamSparkLink analyzeParamSparkLink;
    AlzParam3GPP analyzeParam3GPP;
	AlzParamWiSun analyzeParamWiSun;
    AlzParamZwave analyzeParamZWave;

} AnalyzeParam;

struct Vsa3GPPCommResult
{
    int Standard;//信号类型 <typedef enum>
    Alg_3GPP_AlzSlotNBIOT NBIOT;
};
// 11ax/11be Trigger Base分析参数
struct AlzParamAxTriggerBase
{
    int TBFlag;    //是否必须按TriggerBase模式来解
    int UserNum;   //总User数
    int UserID;    //当前分析User
    int GILTFSize; //帧的GILTF:GILTFSize0, 1:GILTFSize1, 2:GILTFSize2
    int NumLTF;    //帧的取值1,2,4,6,8
    int LDPCSym;   //取值0,1
    int PEDisamb;  //取值0,1
    int AFactor;   //取值1,2,3,4
    int STBC;      // STBC值
    int Doppler;
    int Midamble_Periodicity;

    int Stream[BE_RU_COUNT];  //取值1~8
    int MCS[BE_RU_COUNT];     //取值0~11
    int Segment[BE_RU_COUNT]; //取值0,1
    int RUIndex[BE_RU_COUNT]; //
    int Conding[BE_RU_COUNT]; //取值0:BCC、1:LDPC
    int DCM[BE_RU_COUNT];     //取值
    int AID[BE_RU_COUNT];
    int TBMUMIMOFlag;          // TB MU-MIMO时为1，否则为0
    int NSSStart[BE_RU_COUNT]; //取值1-7，常规TB都为1
    int Reserved[107];
};

//11az analyze param
typedef struct {
    int Nsts;
    int LtfRep;
    int LtfKey[16];
    int LtfIv[16];
    int Reserved[128];
} AzUser;

#define AZ_RU_COUNT AX_RU_COUNT
struct AlzParam11az
{
    /*Secure mode 0: OFF(默认值), 1: ON*/
    u32 SecureMode;
    /*Frequency domian transmitter window 0: OFF(默认值), 1: ON*/
    u32 TxWinFlg;
    /*User number*/
    u32 UserNum;
    AzUser User[ALG_11AZ_MAX_USER_NUM];
    int Reserved[256];
};

// wave生成文件后，才确定的参数变量值获取返回，目前主要是tb相关的内容
typedef struct
{
    int LDPCSym;
    int PEDisamb;
    int AFactor;
    int Doppler;
    int Midamble_Periodicity;
    int Reserved[256];
} AxTbVariableParameter;

// A-MPDU information
struct AmpduInfo
{
    int ampdu_ind;    // ampdu子帧序号
    int ampdu_length; // mpdu长度
    int crc;          // crc
    int Reserved[32];
};

typedef struct
{
    s32 validflag;                          //有效性
    s32 ScrambleLen;
    char ScrambleSeq[12];
    char ServField[16];
    s32 Reserved[16];
}ServiceFieldInfo;

//power table info / per signal
struct PowerTableInfo
{
    double PowerFrame;
    double PowerPeak;
    char ValidFig[MAX_DF_NUM];
    double StrmPwr[MAX_DF_NUM];
};
/*=========================================================================
                                result define
==========================================================================*/

/* IQ data. Complex vector is returned. */
#define WT_RES_IQ "IQ.data"

/* IQ data mv. Complex vector is returned. */
#define WT_RES_IQ_MV "IQ.data.mv"

/* sampling frequency. Int Value */
#define WT_RES_SMP_FREQ "sampling.freq"

/* frame data info. for meter */
#define WT_RES_DATA_INFO "data.info"

/* frame data information plus. for meter */
#define WT_RES_DATA_INFOPLUS "data.infoplus"

/* data burst information. for meter */
#define WT_RES_DATA_BURST_FIELD_INFO "data.burst.field.info"

/* base result */
#define WT_RES_BASE_RESULT "result.base"

/*mimo base result*/
#define WT_RES_BASE_RESULT_COMPOSITE "result.base.composite"

/*mimo stream real nstsIndex*/
#define WT_RES_STREAM_NSTS_INDEX    "stream.real.nsts.index"

/* demode. Int Value */
#define WT_RES_DEMODE "demode"

/* signal segment number. Int Value  */
#define WT_RES_SEGMENT_NUM "segment.num"

/* rf gain. double Value  */
#define WT_RES_RF_GAIN "gain"

/* external gain. double Value  */
#define WT_RES_EXT_GAIN "ext_gain"

/* reference power level. double Value  */
#define WT_RES_REF_LEVEL "ref.level"

/* trigger power level. double Value  */
#define WT_RES_TRIG_LEVEL "trig.level"

/* SparkLink CtrlInfo. for meter*/
#define WT_RES_SPARK_CTRLINFO      "spark.ctrlinfo"

/* SparkLink SyncSigBin. char* */  
#define WT_RES_SPARK_SYNC          "spark.SyncSigBin"

/* SparkLink CtrlBin. char* */      
#define WT_RES_SPARK_CTRL          "spark.CtrlBin"

/* SparkLink PayloadBin. char*  */      
#define WT_RES_SPARK_PAYLOAD        "spark.PayloadBin"

/*===========================================================================
  CCDF Result
  ===========================================================================*/

/* Real vector containing CCDF probability values (Y-axis of CCDF plot) */
#define WT_RES_CCDF_PROB "CCDF.prob"

/* Real vector containing CCDF power relative to average power in dB values
(X-axis of CCDF plot) */
#define WT_RES_CCDF_POWER_REL_DB "CCDF.power_rel_dB"

/* ccdf start relative power. double Value dBm */
#define WT_RES_CCDF_START "CCDF.start"

/* ccdf scale value dBm. double value */
#define WT_RES_CCDF_SCALE "CCDF.scale"

/* Result for CCDF %10 %1 %0.1 %0.01 percentage‘s power. double vector Value is returned. */
#define WT_RES_CCDF_PERCENT_POWER "CCDF.percent.power"
/*===========================================================================
  spectrum result
  ===========================================================================*/

/*Carrier leakage in dB.Value is returned*/
#define WT_RES_SPECTRUM_CARRIER_LEAKAGE "Carrier_leakage"

/*OBW in Hz.Value is returned*/
#define WT_RES_SPECTRUM_OBW "Obw"

/*OBW start Freq,int value*/
#define WT_RES_SPECTRUM_OBW_START_FREQ "Obw_start_freq"

/*OBW end Freq,int value*/
#define WT_RES_SPECTRUM_OBW_END_FREQ "Obw_end_freq"

// 8080 第二段的obw，8080专用
/*OBW in Hz.Value is returned*/
#define WT_RES_SPECTRUM_OBW1 "Obw1"

/*OBW start Freq in Hz,int value*/
#define WT_RES_SPECTRUM_OBW_START_FREQ1 "Obw_start_freq1"

/*OBW end Freq in Hz,int value*/
#define WT_RES_SPECTRUM_OBW_END_FREQ1 "Obw_end_freq1"
// end 8080 obw

/*Spectrum mask error point in %.Value is returned*/
#define WT_RES_SPECTRUM_MASK_ERR "Spec_mask_err"

/*Frequency of the max power in Hz.Value is returned*/
#define WT_RES_SPECTRUM_PEAK_FREQ "Spec_peak_freq"

/*power of the max power in dBm.Value is returned*/
#define WT_RES_SPECTRUM_PEAK_POWER "Spec_peak_power"

/* Y-axis values, power in dBm. Vector is returned. */
#define WT_RES_SPECTRUM_Y "y"

/* spectrum maskm, power in dBm. Vector is returned */
#define WT_RES_SPECTRUM_MASK "spectrum.mask"

/* spectrum rbw, value in HZ. Int Value */
#define WT_RES_SPECTRUM_RBW "spectrum.rbw"

/* Resolution bandwidth used in calculations. Int Value is returned. */
#define WT_RES_SPECTRUM_RES_BW "res_bw"

/*Center Frequency in Hz.Value is returned*/
#define WT_RES_SPECTRUM_FREQ_CENTER "Freq_center"

/*Frequency span in Hz. Int Value is returned*/
#define WT_RES_SPECTRUM_FREQ_SPAN "Freq_span"

/*Spectrum margin data. Complex Vector is returned*/
#define WT_RES_SPECTRUM_MARGIN_DATA "Spec_margin_data"

/* 500M spectrum Y-axis values, power in dBm. Vector is returned. */
#define WT_RES_SPECTRUM_Y_500M "y_500M"

/* 500M spectrum maskm, power in dBm. Vector is returned */
#define WT_RES_SPECTRUM_MASK_500M "spectrum.mask_500M"
/*===========================================================================
  power result
  ===========================================================================*/

/* RMS Power in dB, no gap. */
#define WT_RES_RMS_DB_NO_GAP "rms_db_nogap"

/* RMS Power in dB. */
#define WT_RES_RMS_DB "rms_db"

/* Frame Count by Power Detectoring. Int Value is returned. */
#define WT_RES_POWER_FRAME_COUNT "pow.frame.count"

/* Power Peak. Vector is returned. */
#define WT_RES_POWER_PEAK "pow.peak"

/* points power. Vector is returned */
#define WT_RES_POINTS_POWER "points.power"

/* window avg power. Vector is returned */
#define WT_RES_WIN_AVG_POWER "avg.power"

/*===========================================================================
  OFDM & 11B result
  ===========================================================================*/
/* Error code while analyzing EVM invalid. int Value is returned. */
#define WT_RES_EVM_INVALID_ERROR_CODE "evm.error_code"

/* EVM for entire frame. Value is returned. */
#define WT_RES_EVM_ALL "evm.all"

/* EVM for entire frame(%). Value is returned. */
#define WT_RES_EVM_ALL_PERCENT "evm.all(%)"

/* EVM peak value. Value is returned. */
#define WT_RES_EVM_PEAK "evm.pk"

/* EVM peak value(%). Value is returned. */
#define WT_RES_EVM_PEAK_PERCENT "evm.pk(%)"

/* Frequency Error in Hz. Value is returned. */
#define WT_RES_FREQ_ERR_HZ "signal.freqerr_hz"

/* Symbol Clock Error in ppm. Value is returned. */
#define WT_RES_SYMBOL_CLOCK_ERR "signal.symclockerr"

/* IQ Match Amplitude Error in dB. Value is returned. */
#define WT_RES_IQ_MATCH_AMP_DB "iqmatch.amp_dB"

/* IQ Match Phase Error in deg. Value is returned. */
#define WT_RES_IQ_MATCH_PHASE "iqmatch.phase"

/* IQ Chanel DC Offset in dB. Value is returned. */
#define WT_RES_IQ_DC_OFFSET "iq.offset"

/* const data in dB. Complex Vector is returned */
#define WT_RES_CONST_DATA "const.data"

/* pilot const data in dB. Complex Vector is returned */
#define WT_RES_CONST_PILOT "const.pilot"

/* reference const data in dB. Complex Vector is returned */
#define WT_RES_CONST_REF "const.ref"

/* symbol evm avg. Vector is returned */
#define WT_RES_EVM_SYM_AVG "evm.symbol.avg"

/* symbol evm. Vector is returned */
#define WT_RES_EVM_SYM "evm.symbol"

/* symbol evm pilot. Complex Vector is returned */
#define WT_RES_EVM_SYM_PILOT "evm.symbol.pilot"

/* carrier evm avg. Vector is returned */
#define WT_RES_EVM_CARRIER_AVG "evm.carrier.avg"

/* pilot carrier evm avg. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT_AVG "evm.carrier.pilot.avg"

/* carrier evm. Vector is returned */
#define WT_RES_EVM_CARRIER "evm.carrier"

/* pilot carrier evm. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT "evm.carrier.pilot"

/* symbol evm avg db. Vector is returned */
#define WT_RES_EVM_SYM_AVG_DB "evm.symbol.avg.db"

/* symbol evm db. Vector is returned */
#define WT_RES_EVM_SYM_DB "evm.symbol.db"

/* symbol evm pilot db. Vector is returned */
#define WT_RES_EVM_SYM_PILOT_DB "evm.symbol.pilot.db"

/* carrier evm avg db. Vector is returned */
#define WT_RES_EVM_CARRIER_AVG_DB "evm.carrier.avg.db"

/* pilot carrier evm avg db. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT_AVG_DB "evm.carrier.pilot.avg.db"

/* carrier evm db. Vector is returned */
#define WT_RES_EVM_CARRIER_DB "evm.carrier.db"

/* pilot carrier evm db. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT_DB "evm.carrier.pilot.db"

#define WT_RES_LTF_SNR_DB         "signal.LTF.SNR_dB"
#define WT_RES_PSDU_SNR_DB         "signal.PSDU_SNR_dB"
/* 11ax ltf type, int value is returned */
#define WT_RES_LTF_SIZE "LTF.size"
/*========================================================================
 * 11ax SigB result
 * =======================================================================*/
/* symbol const data in dB. Complex Vector is returned */
#define WT_RES_SIGB_CONST_DATA "sigb.const.data"

/* symbol const pilot in dB. Complex Vector is returned */
#define WT_RES_SIGB_CONST_PILOT "sigb.const.pilot"

/* symbol reference const data in dB. Complex Vector is returned */
#define WT_RES_SIGB_CONST_REF "sigb.const.ref"

/*sigb symbol evm . Vector is returned */
#define WT_RES_SIGB_EVM_SYM "sigb.evm.symbol"

/*sigb carrier evm. Vector is returned */
#define WT_RES_SIGB_EVM_CARRIER "sigb.evm.carrier"

/* Single element double Value */
/*sigb all evm*/
#define WT_RES_SIGB_EVM_ALL "sigb.evm.all"

/* Single element double Value */
/*sigb pilot evm*/
#define WT_RES_SIGB_EVM_PILOT_DB "sigb.evm.pilot"

/* Single element double Value */
/*sigb data evm*/
#define WT_RES_SIGB_EVM_DATA_DB "sigb.evm.data"

/* double Value */
#define WT_RES_SIGB_EVM_ALL_COMPOSITE "sigb.evm.all.composite"

/* double Value */
#define WT_RES_SIGB_EVM_PILOT_DB_COMPOSITE "sigb.evm.pilot.composite"

/* double Value */
#define WT_RES_SIGB_EVM_DATA_DB_COMPOSITE "sigb.evm.data.composite"

/*===========================================================================
  11ax user result
  ===========================================================================*/
/*all user's const data*/
#define WT_RES_ALL_USER_CONST_DATA "all.user.const.data"

/*all user's const ref*/
#define WT_RES_ALL_USER_CONST_REF "all.user.const.ref"

/*all user's const pilot*/
#define WT_RES_ALL_USER_CONST_PILOT "all.user.const.pilot"

/*specific user's const. data*/
#define WT_RES_11AX_USER_CONST_DATA "user.const.data"

/*specific user's const ref*/
#define WT_RES_11AX_USER_CONST_REF "user.const.ref"

/*specific user's const.pilot*/
#define WT_RES_11AX_USER_CONST_PILOT "user.const.pilot"

/*11ax psdu format*/
#define WT_RES_11AX_PSDU_FORMAT "ax.psdu.format"

/*========================================================================
 * 11ax SigA result
 * =======================================================================*/
/*siga color bit,int Value*/
#define WT_RES_11AX_SIGA_BSS_COLOR "siga.bss.color"

/*L-sig bit*/
#define WT_RES_11AX_L_SIG_BIT "ax.L-sig.bit"

/*HE-Sig-A Bit*/
#define WT_RES_11AX_SIG_A_BIT "ax.siga.bit"

/*========================================================================
 * 11ax SigB result
 * =======================================================================*/
/*HE-Sig-B bit 1,only mu ppdu have*/
#define WT_RES_11AX_SIG_B_BIT1 "ax.sigb.bit1"

/*HE-Sig-B bit 2,only mu ppdu have*/
#define WT_RES_11AX_SIG_B_BIT2 "ax.sigb.bit2"

/*========================================================================
 * 11be SigA result
 * =======================================================================*/
/*L-sig bit*/
#define WT_RES_11BE_L_SIG_BIT "be.L-sig.bit"

/*BE-U-Sig Bit*/
#define WT_RES_11BE_U_SIG_BIT "be.U-sig.bit"

/*========================================================================
 * 11be SigB result
 * =======================================================================*/
/*BE-Sig-ctx1 bit 1,only mu ppdu have*/
#define WT_RES_11BE_SIG_CTX1_BIT "be.sig1.bit"

/*BE-Sig-ctx2 bit 2,only mu ppdu have*/
#define WT_RES_11BE_SIG_CTX2_BIT "be.sigb2.bit"

/*========================================================================
 * 11ax TB result
 * =======================================================================*/
/*trigger base unused tone error,struct stTbUnusedToneError result is return*/
#define WT_RES_TB_UNUSED_TONE_ERROR "tb.unused_tone_error"

/*trigger frame info, struct TF11ax is return*/
#define WT_RES_TRIGGER_FRAME_INFO "trigger_frame.info"
/*===========================================================================
  11a 11ac 11n preamble freqErr
  ===========================================================================*/
/* 160 points data, vector is returned, double value*/
#define WT_RES_PREAMBLE_FREQ_ERR_HZ "preamble.freqErr_hz"

/* 160 points data, vector is returned, int value*/
#define WT_RES_PREAMBLE_FREQ_ERR_FLAG "preamble.freqErr.validFlag"

/*===========================================================================
  11B result
  ===========================================================================*/
/* 11b LO(DC) Leakage rbw. Int Value is returned. */
#define WT_RES_LO_LEAKAGE_RBW "spectrum.lo.leakage.rbw"

/* 11b LO(DC) Leakage span. Int Value is returned. */
#define WT_RES_LO_LEAKAGE_SPAN "spectrum.lo.leakage.span"

/* 11b LO(DC) Leakage .Vector is returned*/
#define WT_RES_LO_LEAKAGE "spectrum.lo.leakage"

/* Real vector is returned */
/* Time vector for the data points in WT_RES_11B_FREQ_ERR */
#define WT_RES_11B_FREQ_ERR_TIME "b11.freqErrTimeVect"

/* Real vector is returned */
/* 11b Preamble Frequency Error in Hz,Vector is returned. */
#define WT_RES_11B_FREQ_ERR "b11.freqErr_hz"

/* Ramp on time. Value is returned. */
#define WT_RES_RAMP_ON_TIME "ramp.on_time"

/* Ramp off time. Value is returned. */
#define WT_RES_RAMP_OFF_TIME "ramp.off_time"

/* Single element double Value */
/* Phase Error */
#define WT_RES_11B_PHASE_ERR "b11.phase_err"

/* Single element double Value */
/* Bit Rate, see 802.11b standard */
#define WT_RES_11B_BIT_RATE "b11.PLCP_info.bit_rate"

/* Single element double Value */
/* Carrier Suppression */
#define WT_RES_11B_CARR_SUPPRESSION "b11.carrier_suppression"

/* 11b eye data. st11B_eye Vector */
#define WT_RES_11B_EYE "b11.eye"

/*11b 频偏余量ppm, double value is return */
#define WT_RES_11B_FREQ_ERR_MARGIN           "b11_freqerr_margin"

/*11b 采样偏余量 ppm, double value is return */
#define WT_RES_11B_CLOCK_ERR_MARGIN           "b11_clockerr_margin"

/* power on ramp data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_INST "ramp.on_power_inst"

/* power on ramp peak data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_PEAK "ramp.on_power_peak"

/* power on ramp mask1 data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_MARK1 "ramp.on_power_mask1"

/* power on ramp mask2 data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_MARK2 "ramp.on_power_mask2"

/* power down ramp data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_INST "ramp.off_power_inst"

/* power down ramp peak data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_PEAK "ramp.off_power_peak"

/* power down ramp mask1 data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_MARK1 "ramp.off_power_mask1"

/* power down ramp mask2 data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_MARK2 "ramp.off_power_mask2"

/* evm vs time data. double vector */
#define WT_RES_EVM_TIME "evm.time"

/* evm vs time avg data. double vector */
#define WT_RES_EVM_TIME_AVG "evm.time.avg"

/* evm vs time chip info. st11BChip data */
#define WT_RES_EVM_TIME_CHIP "evm.chip"

/*===========================================================================
  OFDM result
  ===========================================================================*/

/* Single element double Value */
/* Phase Error */
#define WT_RES_OFDM_PHASE_ERR "ofdm.phase_err"

/* Number of Bytes in PSDU. Int Value */
#define WT_RES_PSDU_LENGTH "psdu_length"

/* Single element double Value */
/* Data rate in Mbps */
#define WT_RES_OFDM_DATA_RATE_MBPS "ofdm.PLCP.Data_rate_Mbps"

/* Number of symbols. Int Value */
#define WT_RES_OFDM_NUMBER_SYMBOLS "ofdm.PLCP.Nspp"

/* EVM for data part of frame.dB. Value is returned. */
#define WT_RES_EVM_DATA_DB "evm.data"

/* EVM for pilot part of frame.dB. Value is returned. */
#define WT_RES_EVM_PILOT_DB "evm.pilot"

/*Spectrum Flatness Passed Or Failed. Int Value is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED "Spec_flatness_passed"

/*Spectrum Flatness Data. Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_DATA "Spec_flatness_data"

/*Spectrum Flatness Data. Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA "Spec_flatness_maskup_data"

/*Spectrum Flatness Data. Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA "Spec_flatness_maskdown_data"

/*Spectrum Flatness Section Value. Complex Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE "Spec_flatness_section_value"

/*Spectrum Flatness Section Margin. Complex Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN "Spec_flatness_section_margin"

#define WT_RES_OFDM_CHANNEL_PHASE_RESPONSE  "Channel_phase_response"
#define WT_RES_OFDM_CHANNEL_AMPLITUDE_RESP  "Channel_amplitude_response"
#define WT_RES_OFDM_SYMBOL_PHASE_ERROR      "Symbol.PhaseError"
#define WT_RES_OFDM_SYMBOL_AMPLITUDE        "Symbol.Amplitude"

/* APEP length. struct Value return ,support by ac ax be*/
#define WT_RES_APEP_LENGTH          "APEP_length"
/*===========================================================================
  ZigBee result
  ===========================================================================*/

/* Single element double vector */
/* Phase Error */
#define WT_RES_ZIGBEE_PHASE_ERR "zigbee.phase_err"
#define WT_RES_ZIGBEE_EVM_PSDU "zigbee.evm(psdu)"
#define WT_RES_ZIGBEE_EVM_PSDU_PERCENT "zigbee.evm(psdu)_percent"
#define WT_RES_ZIGBEE_EVM_SHRPHR "zigbee.evm(shr+phr)"
#define WT_RES_ZIGBEE_EVM_SHRPHR_PERCENT "zigbee.evm(shr+phr)_percent"
#define WT_RES_ZIGBEE_EVM_OFFSET_DB "zigbee.evm_offset"
#define WT_RES_ZIGBEE_EVM_OFFSET_PERCENT "zigbee.evm_offset_percent"
/* st11Zigbee_eye vector is returned*/
#define WT_RES_ZIGBEE_EYE_REAL "zigbee.eye_real"
/* st11Zigbee_eye vector is returned*/
#define WT_RES_ZIGBEE_EYE_IMAG "zigbee.eye_imag"
/* double vector is returned, phase_err vs chip*/
#define WT_RES_ZIGBEE_PHASE_ERR_VS_CHIP "zigbee.phase_err.chip"

/*===========================================================================
 Bluetooth result
 ===========================================================================*/

/* freq carrier offset of each burst detected, in Hz. */
/* stBT_FreqErr vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_BUF "freq_est buffer"

/* Initial freq carrier drift of each burst detected, in Hz. */
/* Real vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_DRIFT "freq_drift"

/* Initial freq carrier drift of each burst detected, in Hz. */
/* Real vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_DRIFT_RATE "freq_drift_rate"

/* Initial freq offset of each burst detected, in Hz. */
/* Real vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_TOL "freq_est"

/* The measurement result for deltaF1Avg as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2
Requires 00001111 data pattern. Result in Hz */
#define WT_RES_BT_DELTA_F1_AVG "deltaF1Average"

/* WT_RES_BT_DELTA_F1_AVG is valid or not. Int Value
1 - valid
0 - invalid */
#define WT_RES_BT_DELTA_F1_VALID "deltaF1Valid"

/* The measurement result for deltaF2Max as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2
Requires alternating data pattern. Result in Hz */
#define WT_RES_BT_DELTA_F2_MAX "deltaF2Max"

/* The measurement result for deltaF2Avg as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2
Requires alternating data pattern. Result in Hz */
#define WT_RES_BT_DELTA_F2_AVG "deltaF2Average"

/* WT_RES_BT_DELTA_F2_MAX(WT_RES_BT_DELTA_F2_AVG) is valid or not. Int Value
1 - valid
0 - invalid */
#define WT_RES_BT_DELTA_F2_VALID "deltaF2Valid"

/* Indicates validity of WT_RES_BT_RMS_DEVM. Int Value */
#define WT_RES_BT_RMS_DEVM_VALID "EdrEVMvalid"

/* RMS Differential EVM value (EDR only). */
/* See BlueTooth Testing Documentation. */
/* Single element real vector is returned. */
#define WT_RES_BT_RMS_DEVM "EdrEVMAv"

/* Pk Differential EVM value (EDR only). */
/* See BlueTooth Testing Documentation. */
/* Single element real vector is returned. */
#define WT_RES_BT_PK_DEVM "EdrEVMpk"

/* Relative power of EDR section to FM section of packet, in dB. */
#define WT_RES_BT_EDR_REL_PWR "EdrPowDiffdB"

/* The percentage of symbols with EVM below the threshold. 取DEVM数组中索引99%的值作为结果 ,EEDR 99% DEVM*/
#define WT_RES_BT_EDR_PROB_99_EVM_PASS "EdrprobEVM99pass"

/* The percentage of symbols with EVM below the threshold. Threshold
for 2 Mbps is 0.3 for 3 Mbps is 0.2 ，EDR DEVM < 30%(pi/4-DQPSK),EDR DEVM < 20%(8DPSK)*/
#define WT_RES_BT_EDR_PROB_99_EVM_PASS_PERCENT "EdrprobEVMpassPct"


/* NEW: In release 1.2 of the Bluetooth Software:
Max DEVM Average as specified in: BLUETOOTH TEST SPECIFICATION
Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2. stBT_DEVM vector returned*/
#define WT_RES_BT_MAX_DEVM_AVG "EdrEVMvsTime"

/* Omega_i value (EDR only). */
/* Single element real vector is returned. */
#define WT_RES_BT_EDR_OMEAG_I "Omega_i"

/* Omega_o value (EDR only). */
/* Single element real vector is returned. */
#define WT_RES_BT_EDR_OMEAG_O "Omega_o"

/* Omega_i0 value (EDR only). */
/* Single element real vector is returned. */
#define WT_RES_BT_EDR_OMEAG_IO "Omega_io"

/* Bandwidth-20dB Passed Or Failed. Int Value */
/* A value of 1 indicates BW20dB passed. */
/* A value of 0 indicates BW20dB doesn't passed, and thus empty. */
/* Single element vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_BANDWIDTH_20DB_Passed "BW20dB_Passed"

/* 20 dB bandwidth value Hz. See BlueTooth Testing Documentation. */
/* stBT_BW20dB vector is returned. */
#define WT_RES_BT_BANDWIDTH_20DB "bandwidth20dB"

/* Bandwidth-20dB rbw HZ. double value */
#define WT_RES_BT_BANDWIDTH_20DB_RBW "bandwidth20dBRbw"

/* Bandwidth-20dB obw HZ. double value */
#define WT_RES_BT_BANDWIDTH_20DB_OBW "bandwidth20dBObw"

/* Bandwidth-20dB start freqency that meets the threshold, double valude is return*/
#define WT_RES_BT_BANDWIDTH_20DB_FREQ_LOW   "bandwidth20dB.freq_low"

/* Bandwidth-20dB end freqency that meets the threshold, double valude is return*/
#define WT_RES_BT_BANDWIDTH_20DB_FREQ_HIGH   "bandwidth20dB.freq_high"

// BT BLE FnMax,Hz
#define WT_RES_BT_BLE_FnMax "FnMax"

// BT BLE F0FnMax,Hz
#define WT_RES_BT_BLE_F0FnMax "F0FnMax"

// BT BLE Delta_F1F0,Hz
#define WT_RES_BT_BLE_Delta_F1F0 "Delta_F1F0"

// BT BLE F0Fn5_Max,Hz
#define WT_RES_BT_BLE_F0Fn5_Max "FnFn5_Max"

// BT BLE Delta_F1_MAX
#define WT_RES_BT_DELTA_F1_MAX "deltaF1Max"

// BT BLE Delta_F0F3,Hz
#define WT_RES_BT_BLE_Delta_F0F3 "Delta_F0F3"

// BT BLE Delta_F0Fn3,Hz
#define WT_RES_BT_BLE_Delta_F0FN3 "Delta_F0Fn3"

// BT BLE Freq Drift Detail Valid. Int Value
#define WT_RES_BT_BLE_DRIFT_DETAIL_VALID "BLE_drift_detail_valid"

// BT BR/BLE Spectrum adjacent Channel Power,dBm
#define WT_RES_BT_SPEC_ACP "Spectrum_Acp"

// BT BR/BLE Spectrum adjacent Channel Power mask, Complex vector
#define WT_RES_BT_SPEC_ACP_MASK "Spectrum_Acp_mask"

// BT BR/BLE名称：DetaF2Max Pass Rate，单位：%（保留两位小数）
#define WT_RES_BT_DELTA_F2_PASS_PERCENT "Delta_F2_pass_percent"

// BT BR/BDR Freq offset header
#define WT_RES_BT_FREQ_OFFSET_HEADER "freq_offset_header"

// BT BLE Freq offset sync
#define WT_RES_BT_FREQ_OFFSET_SYNC "freq_offset_sync"
/* Similar to the measurement result for deltaF1Avg as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2.
BT BR/EDR Result measured from Header data. Result in Hz.*/
#define WT_RES_BT_RES_FREQ_DEV "freq_deviation"

/* BT BR/EDR Peak to Peak Frequency Deviation, in Hz during header*/
#define WT_RES_BT_RES_FREQ_DEV_PK_TO_PK "freq_deviationpktopk"

/*BT BR/EDR Delta av access*/
#define WT_RES_BT_DELTA_F2_AVG_ACCESS "Delta_F2_Av_Access"

/*BT BR/EDR Delta max access*/
#define WT_RES_BT_DELTA_F2_MAX_ACCESS "Delta_F2_Max_Access"

/*BT PSDU CRC result, int*/
#define WT_RES_BT_PSDU_CRC                  "BT_PSDU_CRC_Status"

#define WT_RES_BT_DELTA_F1_MIN                "deltaF1Min"

#define WT_RES_BT_DELTA_F2_MIN                "deltaF2Min"

#define WT_RES_BT_EDR_MAX_FREQ_VAR            "Max_FreqVar"

/*BT BT BLE F0FnAvg,Hz*/
#define WT_RES_BT_F0FN_AVG                   "BT_F0FnAvg"

/*BT PSDU BIN result, char*/
#define WT_RES_BT_PSDU_BIN                   "BT_PSDU_BIN"

#define WT_RES_BT_PACKET_TYPE                   "BT_Packet_type"

#define WT_RES_BT_PACKET_LENGTH                 "BT_Packet_length"

#define WT_RES_BT_PACKET_DATA_RATE              "BT_Packet_Data_Rate"

#define WT_RES_BT_PACKET_INIT_FREQ_ERR          "BT_Packet_Init_Frequency_Err"

#define WT_RES_BT_DELTA_F1_99p9_PRECENT         "Delta_F1_99.9%"

#define WT_RES_BT_DELTA_F2_99p9_PRECENT         "Delta_F2_99.9%"

#define WT_RES_BT_PACKET_PAYLOAD_HEADER_BITS    "Packet_Payload_Header_bits"

//BT5.2 result

/* BT BR/EDR result, int */
#define WT_RES_BR_EDR_LAP                     "BR_EDR_LAP"

/* BT BR/EDR result, int */
#define WT_RES_BR_EDR_UAP                     "BR_EDR_UAP"

/* BT BLE_Uncoded result, int */
#define WT_RES_BLE_CTEINFO                    "BLE_CTEInfo"

/* BT BLE_Uncoded && CTEInfo=1 result, int */
#define WT_RES_BLE_CTEType                     "BLE_CTEType"

/* BT BLE_Uncoded && CTEInfo=1 result, int */
#define WT_RES_BLE_CTE_DURATIONT                "BLE_CTE_DurationT"

/* BT EDR result, int */
#define WT_RES_BT_EDR_SYNSEQ_ERR_BIT_NUM        "EDR_SynSeq_ErrBit_Num"

/* BT EDR result(2-EV3、2-EV5、3-EV3 and 3-EV5 no such result), int */
#define WT_RES_BT_EDR_TRAILER_ERR_BIT_NUM       "EDR_Trailer_ErrBit_Num"

/* int */
#define WT_RES_BT_LT_ADDR                        "FrmInfo.LT_ADDR"

/* int */
#define WT_RES_BT_FLOW_CTRL                      "FrmInfo.Flow"

/* int */
#define WT_RES_BT_ACK_INDICATION                 "FrmInfo.ARQN"

/* int */
#define WT_RES_BT_SEQ_NUM                        "FrmInfo.SEQN"

/* int */
#define WT_RES_BT_LLID                           "FrmInfo.LLID"

/* int */
#define WT_RES_BT_FLOW                            "FrmInfo.mFlow"

/* int */
#define WT_RES_BT_PAYLOADSIZE                     "FrmInfo.PayLoadSize"

/* int */
#define WT_RES_BT_PAYLOAD_EIR                             "FrmInfo.PayLoadEIR"

/* int */
#define WT_RES_BT_PAYLOAD_SR                              "FrmInfo.PayLoadSR"

/* int */
#define WT_RES_BT_PAYLOAD_CLASS_OF_DEVICE                 "FrmInfo.PayLoadClassofDevice"

/* int */
#define WT_RES_BT_PAYLOAD_LTADDR                          "FrmInfo.PayLoadLTAddr"

/* int */
#define WT_RES_BT_PAYLOAD_CLK27B2                         "FrmInfo.PayLoadCLK27b2"

/* int */
#define WT_RES_BT_BLEMAPPERS                      "FrmInfo.BLEMapperS"

/* VoiceField[10],int */
#define WT_RES_BT_VOICE_FIELD                     "FrmInfo.VoiceField"

#define WT_RES_BT_PAYLOAD_HEADER                  "FrmInfo.PayLoadHeader"

/* int */
#define WT_RES_BT_PATTERN                       "BT_Pattern"

/* BT EDR result, double */
#define WT_RES_EDR_GFSK_POWER                   "EDR_GFSK_power"

/* BT EDR result, double */
#define WT_RES_EDR_DPSK_POWER                   "EDR_DPSK_power"

#define WT_RES_EDR_GFSK_POWER_PEAK                           "EDR_GFSK_Power_Peak"

#define WT_RES_EDR_DPSK_POWER_PEAK                           "EDR_DPSK_Power_Peak"

/* BT EDR result, double */
#define WT_RES_EDR_GUARD_TIME                    "EDR_GuardTime"

#define WT_RES_EDR_GFSK_POWER_PEAK               "EDR_GFSK_Power_Peak"
#define WT_RES_EDR_DPSK_POWER_PEAK                "EDR_DPSK_Power_Peak"
/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_PWR_AVG                "BLE_CTE_Pwr_Avg"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_PWR_PEAK               "BLE_CTE_Pwr_Peak"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_PWR_SUB_AVG            "BLE_CTE_Pwr_Sub_Avg"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_MAX                "BLE_CTE_Fsi_Max"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_MIN                 "BLE_CTE_Fsi_Min"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FS1_SUB_FP              "BLE_CTE_Fs1_Sub_Fp"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_SUB_F0_MAX              "BLE_CTE_Fsi_Sub_F0_Max"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_SUB_FSI3_MAX              "BLE_CTE_Fsi_Sub_Fsi3_Max"

/* BT BLE_Uncoded && CTEInfo=1 && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_REF_AVG                    "BLE_CTE_Pwr_Ref_Avg"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_REF_Dev                    "BLE_CTE_Pwr_Ref_DevDivAvg"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_REF_Dev_MAX                 "BLE_CTE_Pwr_Ref_DevDivAvg_Max"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_AVG_SLOT                    "BLE_CTE_Pwr_Avg_Slot"

/* BT BR/EDR  result Header_bin[54], int */
#define WT_RES_BR_EDR_HEADERBIN                         "BR_EDR_Header_Bin"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result CTE_Pwr_Avg_Slot[74], double */
#define WT_RES_BLE_CTE_PWR_AVG_SLOT                      "BLE_CTE_Pwr_Avg_Slot"

/* BT BLE Enhanced mode, int value is return*/
#define WT_RES_BLE_ENHANCED_MODE                          "BLE_Enhanced_Mode"

/* BT BLE Advertising result, Delta F2 Avg / Delta F1 Avg, 0.8<= res <=1,double value is return*/
#define WT_RES_BLE_DELTAF2AVG_DIV_DELTAF1AVG                "BLE.DelF2Avg_Div_DelF1Avg"

/* BT Leakage Power,unit is dBm, double value is return */
#define WT_RES_BT_LEAKAGE_POWER                             "BT.leakage_power"

/* BT Difference between Power Peak and Power Average ,unit is dBm,double value is return*/
#define WT_RES_BT_DIFF_POWER_PEAK_VS_AVG                       "BT.PowerPeakMinusPowerAvg"

/* BT EDR Phase difference view result,complex vector point data is return */
#define WT_RES_EDR_PHASE_DIFFERENCE                         "EDR.Phase_difference"

/* BT BR or BLE Freq Deviation view result,complex vector point data is return */
#define WT_RES_BR_BLE_FREQ_DEVIATION                       "BR_BLE.Freq.Deviation"

/* BT BLE advertising access address or BLE test syncWord ,unsigned int is return*/
#define WT_RES_BlE_DECODE_ACCESS_ADDR                       "BLE.Decode.Access_addr"

//====================================================================
// TBT SIFS, unit:S.
#define WT_RES_TBT_SIFS             "tbt.sifs"

// Frame start and end place is return
#define WT_RES_FRAME_LOCATION "Power.Frame.Location"

// Evm Specification db
#define WT_RES_EVM_SPECIFICATION_DB "evm.specification.db"

// Mimo real stream count,according to real vsg stream
#define WT_RES_STREAM_COUNT "mimo.stream.count"

/*PSDU Scramble sequence result,u8 vetor is return,result eg:0100101,ax的不用这个获取*/
#define WT_RES_PSDU_SCRAMBLE "psdu_scramble"

/*DUT Mac Info， struct stMacInfo is return,support 11ag 11n, 11ac*/
#define WT_RES_MAC_INFO "Mac_info"

/*PSDU CRC result ,int value is return,只获取11a, n, ac的，ax的在ofdma info中*/
#define WT_RES_PSDU_CRC "PSDU_CRC"

#define WT_RES_SERVICE_FIELD_INFO        "Service.field.info"

#define WT_RES_MPDU_EOF_PADDING_INFO    "AMPDU.EOF.Padding"

/*LDPC 是否纠正错误bit的int flag，目前只获取11n,ax,ac,be的，区分用户*/
#define WT_RES_PSDU_LDPC_CORRECT_FLAG   "LDPC.Correct_flag"
/*===========================================================================
 special for 11ax result
 ===========================================================================*/
/*11ax per user Mac Info，vector struct stMacInfo is return*/
#define WT_RES_11AX_USER_MAC_INFO "Ax11_User_Mac_info"

/*11ax per user PSDU Scramble sequence result,u8 vetor is return,result eg:0100101，只是ax的*/
#define WT_RES_11AX_PSDU_SCRAMBLE "Ax11_psdu_scramble"

/*11ax mu mimo, ofdma into,vector struct RuOfdmaInfo11ax is return*/
#define WT_RES_11AX_MU_MIMO_OFDMA_INFO "Ax11_Mu_Mimo.Ofdma_info"

/*11ax mu mimo, all user's const data*/
#define WT_RES_MU_MIMO_ALL_USER_CONST_DATA "mumimo.all.user.const.data"

/*11ax mu mimo, all user's const ref*/
#define WT_RES_MU_MIMO_ALL_USER_CONST_REF "mumimo.all.user.const.ref"

/*11ax mu mimo, all user's const pilot*/
#define WT_RES_MU_MIMO_ALL_USER_CONST_PILOT "mumimo.all.user.const.pilot"

/*11ax A-mpdu信息,8080要取组合结果，所以和其他协议分开*/
#define WT_RES_11AX_A_MPDU "Ax11_ampdu.info"

/*11ax get RU Q-matrix information, vector struct QMatrixInformation return*/
#define WT_RES_11AX_RU_QMAT "Ax11_QMat.info"
/*===========================================================================
 special for 11be result
 ===========================================================================*/
/*specific user's const. data*/
#define WT_RES_11BE_USER_CONST_DATA "11be.user.const.data"

/*specific user's const ref*/
#define WT_RES_11BE_USER_CONST_REF "11be.user.const.ref"

/*specific user's const.pilot*/
#define WT_RES_11BE_USER_CONST_PILOT "11be.user.const.pilot"

/*11be psdu format*/
#define WT_RES_11BE_PSDU_FORMAT "11be.psdu.format"

/*11be per user Mac Info，vector struct stMacInfo is return*/
#define WT_RES_11BE_USER_MAC_INFO "11be.user.MAC.info"

/*11be per user PSDU Scramble sequence result,u8 vetor is return,result eg:0100101 */
#define WT_RES_11BE_PSDU_SCRAMBLE "11be.psdu.scrambler"

/*11be mu mimo, ofdma into,vector struct RuOfdmaInfo11ax is return*/
#define WT_RES_11BE_MU_MIMO_OFDMA_INFO "11be.mu-mimo.ofdma.info"

/*11be mu mimo, all user's const data*/
#define WT_RES_11BE_MU_MIMO_ALL_USER_CONST_DATA "mu-mimo.all.user.const.data"

/*11be mu mimo, all user's const ref*/
#define WT_RES_11BE_MU_MIMO_ALL_USER_CONST_REF "mu-mimo.all.user.const.ref"

/*11be mu mimo, all user's const pilot*/
#define WT_RES_11BE_MU_MIMO_ALL_USER_CONST_PILOT "mu-mimo.all.user.const.pilot"

/*11be A-mpdu信息,8080要取组合结果，所以和其他协议分开*/
#define WT_RES_11BE_A_MPDU "11be.ampdu.info"

/*11be get RU Q-matrix information, vector struct QMatrixInformation return*/
#define WT_RES_11BE_RU_QMAT "11be.QMat.info"
/*===========================================================================
 Ofdm result
 ===========================================================================*/
/*ac mu mimo, all user's const data，ac8080也是按普通的取所以和ax分开*/
#define WT_RES_AC_MU_MIMO_ALL_USER_CONST_DATA "ac_mumimo.all.user.const.data"

/*ac mu mimo, all user's const ref*/
#define WT_RES_AC_MU_MIMO_ALL_USER_CONST_REF "ac_mumimo.all.user.const.ref"

/*ac mu mimo, all user's const pilot*/
#define WT_RES_AC_MU_MIMO_ALL_USER_CONST_PILOT "ac_mumimo.all.user.const.pilot"

/*11ac mu mimo 返回data info信息*/
#define WT_RES_11AC_MU_MIMO_DATA_INFO "mumimo.data.info"

/*A-mpdu信息，n，ac*/
#define WT_RES_A_MPDU "ampdu.info"

/*EVM 余量 db, double value is return */
#define WT_RES_EVM_MARGIN "evm_margin"

/* LO 余量 db, double value is return */
#define WT_RES_LEAGKAGE_MARGIN "Leagkage_margin"

/*频偏余量ppm, double value is return */
#define WT_RES_FREQ_ERR_MARGIN "freqerr_margin"

/*采样偏余量 ppm, double value is return */
#define WT_RES_CLOCK_ERR_MARGIN "clockerr_margin"

/*LO参考量dB, double value is return */
#define WT_RES_LEAGKAGE_SPECIFICATION_DB "LO.specification.db"

/*频偏参考量ppm, double value is return */
#define WT_RES_FREQ_ERR_SPECIFICATION_PPM "Freqerr.specification.ppm"

/*采样参考量ppm, double value is return */
#define WT_RES_CLOCK_ERR_SPECIFICATION_PPM "Clockerr.specification.ppm"

/*除了ax外的export PSDU data char vector value is return*/
#define WT_RES_EXPORT_PSDU_BIT "Psdu_bit"

/*11ax psdu bit信息,8080要取组合结果，所以和其他协议分开*/
#define WT_RES_AX_EXPORT_PSDU_BIT "Ax11_Psdu_bit"

/*mimo power table result ,struct vector value is return*/
#define WT_RES_POWER_TABLE                  "power.table"

/*psdu decomposde info*/
#define WT_RES_PSDU_DECOMPOSED_INFO         "PSDU_Decomposed_Info"
/*===========================================================================
 zwave result
 ===========================================================================*/
/* zwave eye data. stZWave_eye Vector */
#define WT_RES_ZWAVE_EYE "Zwave.eye"

/*===========================================================================
 11ba result
 ===========================================================================*/
/* Spectrum Y-axis values, power in dBm. Vector is returned. */
#define WT_RES_11BA_WUR_SPECTRUM_Y              "11ba_WUR_y"

/* spectrum mask, power in dBm. Vector is returned */
#define WT_RES_11BA_WUR_SPECTRUM_MASK           "11ba_WUR_spectrum.mask"

/*Spectrum mask error point in %.Value is returned*/
#define WT_RES_11BA_WUR_SPECTRUM_MASK_ERR       "11ba_WUR_Spec_mask_err"

/*OBW in Hz.Value is returned*/
#define WT_RES_11BA_WUR_SPECTRUM_OBW            "11ba_WUR_Obw"

/*OBW start Freq,int value*/
#define WT_RES_11BA_WUR_SPECTRUM_OBW_START_FREQ "11ba_WUR_Obw_start_freq"

/*OBW end Freq,int value*/
#define WT_RES_11BA_WUR_SPECTRUM_OBW_END_FREQ   "11ba_WUR_Obw_end_freq"

/* spectrum rbw, value in HZ. Int Value */
#define WT_RES_11BA_WUR_SPECTRUM_RBW            "11ba_WUR_spectrum.rbw"

/*Frequency span in Hz. Int Value is returned*/
#define WT_RES_11BA_WUR_SPECTRUM_FREQ_SPAN      "11ba_WUR_spectrum.Freq_span"

/*发送ON/OFF符号功率比测试结果.0：pass; 1 fail,Value is returned*/
#define WT_RES_SYMBOLS_POWER_RATIO              "symbols.power.tatio.result"

/*相关性测试结果.0：pass; 1 fail,Value is returned*/
#define WT_RES_CORRELATION_TEST                 "Correlation.test.result"

//11ba WUR PHY层相关信息，struct WURPHYInfo is returned*/
#define WT_RES_11BA_WUR_PHY_INFO                "11ba_WUR_PHY_Info"

#define WT_RES_11BA_L_SIG_BIT                   "11ba.L_Sig.bit"

/*===========================================================================
 11az result
 ===========================================================================*/
 //spectral flatness
/*Spectrum Flatness Passed Or Failed. Vector Value is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_PASSED             "AZ.Users.Spec_flatness_passed"

/*Spectrum Flatness Data. Vector is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_DATA               "AZ.Users.Spec_flatness_data"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA       "AZ.Users.Spec_flatness_maskup"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA     "AZ.Users.Spec_Flatness_maskdown"

/*Spectrum Flatness Section Value. Complex Vector is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE      "AZ.Users.SpecFlat_section_value"

/*Spectrum Flatness Section Margin. Complex Vector is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN     "AZ.Users.SpeFlat_section_margin"

 //channel amplitude response
#define WT_RES_11AZ_ALL_USERS_OFDM_CHANNEL_PHASE_RESPONSE               "AZ.Users.Ch_phase_response"

 //channel phase response
#define WT_RES_11AZ_ALL_USERS_OFDM_CHANNEL_AMPLITUDE_RESP               "AZ.Users.Ch_amplitude_response"

/*===========================================================================
  H-Matrix initial power result
  ===========================================================================*/
/* RMS Power in dB, no gap. power frame*/
#define WT_RES_HMAT_INIT_RMS_DB_NO_GAP "Hmat.init.rms_db_nogap"

/* RMS Power in dB. power all*/
#define WT_RES_HMAT_INIT_RMS_DB "Hmat.init.rms_db"

/* Power Peak. Vector is returned. */
#define WT_RES_HMAT_INIT_POWER_PEAK "Hmat.init.pow.peak"

/* Mimo real stream count,according to real vsg stream */
#define WT_RES_HMAT_INIT_STREAM_COUNT "Hmat.init.mimo.stream.count"

/*===========================================================================
 SparkLink(GLE) result
 ===========================================================================*/
/* Contrl Info evm vs time data. double vector */
#define WT_RES_CTRINFO_EVM_TIME            "ctrinfo.evm.time"

/* Contrl Info const data in dB. Complex Vector is returned */
#define WT_RES_CTRINFO_CONST_DATA          "ctrinfo.const.data"

/* Contrl Info pilot const data in dB. Complex Vector is returned */
#define WT_RES_CTRINFO_CONST_PILOT          "ctrinfo.const.pilot"

/* reference const data in dB. Complex Vector is returned */
#define WT_RES_CTRINFO_CONST_REF            "ctrinfo.const.ref"

/* gle Eye Diagram  */
#define WT_RES_GLE_EYE                       "gle.eye"

/* gle Spectrum adjacent Channel Power,dBm */
#define WT_RES_GLE_SPEC_ACP                  "gle.Spectrum_Acp"

/* gle Spectrum adjacent Channel Power mask, Complex vector */
#define WT_RES_GLE_SPEC_ACP_MASK             "gle.Spectrum_Acp_mask"

/* gle FrmType, int */
#define WT_RES_GLE_FRAME_TYPE                 "gle.FrmType"

/* gle Bandwidth, int */
#define WT_RES_GLE_BAND_WIDTH                 "gle.Bandwidth"

/* gle PID, int */
#define WT_RES_GLE_PID                         "gle.PID"

/* gle PayloadLen, int */
#define WT_RES_GLE_PAYLOAD_LEN                 "gle.Payload_Len"

/* gle PayloadCRCType, int */
#define WT_RES_GLE_PAYLOAD_CRC_TYPE            "gle.Payload_CRC_Type"

/* gle PayloadCRC, int */
#define WT_RES_GLE_PAYLOAD_CRC                 "gle.Payload_CRC"

/* gle CtrlinfoType, int */
#define WT_RES_GLE_CTRL_INFO_TYPE              "gle.Ctrl_Info_Type"

/* gle CtrlinfoCRC, int */
#define WT_RES_GLE_CTRL_INFO_CRC               "gle.Ctrl_Info_CRC"

/* gle Min_Freq_Err, double */
//#define WT_RES_GLE_MIN_FREQ_ERR                "gle.Min_Freq_Err"

/* gle Delta_fd1_Avg, double */
#define WT_RES_GLE_DELTA_FD1_AVG               "gle.Delta_fd1_Avg"

/* gle Delta_fd1_Max, double */
#define WT_RES_GLE_DELTA_FD1_MAX                "gle.Delta_fd1_Max"

/* gle Delta_fd1_Min, double */
#define WT_RES_GLE_DELTA_FD1_MIN                "gle.Delta_fd1_Min"

/* gle Delta_fd2_Avg, double */
#define WT_RES_GLE_DELTA_FD2_AVG                "gle.Delta_fd2_Avg"

/* gle Delta_fd2_Min, double */
#define WT_RES_GLE_DELTA_FD2_MIN                 "gle.Delta_fd2_Min"

/* gle EvmAvg, double */
#define WT_RES_GLE_EVM_AVG                       "gle.Evm_Avg"

/* gle EvmPeak, double */
#define WT_RES_GLE_EVM_PEAK                      "gle.Evm_Peak"

/* gle Evm99PCT, double */
#define WT_RES_GLE_EVM_99PCT                     "gle.Evm_99PCT"

/* gle Init_Freq_Error, double */
#define WT_RES_GLE_INIT_FREQ_ERR                 "gle.Init_Freq_Error"

/* gle Max_Freq_Drift, double */
#define WT_RES_GLE_MAX_FREQ_DRIFT                "gle.Max_Freq_Drift"

/* gle Freq_Drift_Rate, double */
#define WT_RES_GLE_FREQ_DRIFT_RATE               "gle.Freq_Drift_Rate"

/* gle CtrlInfoEvmAvg, double */
#define WT_RES_GLE_CTRL_INFO_EVM_AVG             "gle.Ctrl_Info_Evm_Avg"

/* gle CtrlInfoEvmPeak, double */
#define WT_RES_GLE_CTRL_INFO_EVM_PEAK            "gle.Ctrl_Info_Evm_Peak"

/* gle CtrlInfoEvm99PCT, double */
#define WT_RES_GLE_CTRL_INFO_EVM_99PCT           "gle.Ctrl_Info_Evm_99PCT"

/* gle ZeroCrossingErr, double */
#define WT_RES_GLE_ZERO_CROSSING_ERR           "gle.Zero_Crossing_Err"

/* gle SymClkErr, double */
#define WT_RES_GLE_SYM_CLK_ERR                   "gle.Sym_Clk_Err"

/* gle MaxTimeDev, double */
#define WT_RES_GLE_MAX_TIME_DEV                  "gle.Max_Time_Dev"

/* gle Delta_fd2_99PCT, double */
#define WT_RES_GLE_DELTA_FD2_99PCT                  "gle.Delta_fd2_99PCT"

/*===========================================================================
 3GPP result
 ===========================================================================*/
/*
<IQ signal>
*/
/* IQ data. Complex vector is returned. */
// #define WT_RES_IQ "IQ.data"

/* IQ data mv. Complex vector is returned. */
// #define WT_RES_IQ_MV "IQ.data.mv"

/*
<Spectrum>
*/
/* spectrum rbw, value in HZ. Int Value */
//#define WT_RES_SPECTRUM_RBW "spectrum.rbw"

/*Center Frequency in Hz.Value is returned*/
// #define WT_RES_SPECTRUM_FREQ_CENTER "Freq_center"

/*Frequency span in Hz. Int Value is returned*/
// #define WT_RES_SPECTRUM_FREQ_SPAN "Freq_span"

/* Y-axis values, power in dBm. Vector is returned. */
// #define WT_RES_SPECTRUM_Y "y"
/*start Freq in Hz. double is returned. */
#define WT_RES_SPECTRUM_FREQ_START "freq_start"

/*end Freq in Hz. double is returned. */
#define WT_RES_SPECTRUM_FREQ_END "freq_end"

/* Complex[0] is freq in Hz, Complex[1] is power in dBm. Vector<Complex> is returned. */
#define WT_RES_3GPP_SPECTRAL_RXSPECTEMIS "3GPP.Spectral.RxSpectEmis"

/* Complex[0] is freq in Hz, Complex[1] is power in dBm. Vector<Complex> is returned. */
#define WT_RES_3GPP_SPECTRAL_SPECTEMISMASK "3GPP.Spectral.SpectEmisMask"

/* Mask segment, Vector<int> is returned. */
#define WT_RES_3GPP_SPECTRAL_EMISMASKSEG "3GPP.Spectral.EmisMaskSeg"

/* Additional Spectrum Emission, Vector<Complex> is returned. */
#define WT_RES_3GPP_SPECTRAL_ADDSPECTEMIS "3GPP.Spectral.AddSpectEmis"

/* Additional Spectrum Emission Segment, Vector<int> is returned. */
#define WT_RES_3GPP_SPECTRAL_ADDSPECTEMISSEG "3GPP.Spectral.AddSpectEmisSeg"

/* Sem Margin Position Low, Vector<double> is returned. */
#define WT_RES_3GPP_MARGINPOSLOW "3GPP.Spectral.SemMargPosLow"

/* Sem Margin Position High, Vector<double> is returned. */
#define WT_RES_3GPP_MARGINPOSHIGH "3GPP.Spectral.SemMargPosHigh"

/* Sem Margin Neg Low, Vector<double> is returned. */
#define WT_RES_3GPP_MARGINNEGLOW "3GPP.Spectral.SemMargNegLow"

/* Sem Margin Neg High, Vector<double> is returned. */
#define WT_RES_3GPP_MARGINNEGHIGH "3GPP.Spectral.SemMargNegHigh"

/* badpointcnt, int is returned. */
#define WT_RES_3GPP_SPECTRAL_BADPOINTCNT "3GPP.Spectral.badpointcnt"

/* freq in MHz Vector<double> is returned. */
#define WT_RES_3GPP_SPECTRAL_SEMMARGNEG_X "3GPP.Spectral.SemMargNegX"

/* power in dBm. Vector<double> is returned. */
#define WT_RES_3GPP_SPECTRAL_SEMMARGNEG_Y "3GPP.Spectral.SemMargNegY"

/* freq in MHz Vector<double> is returned. */
#define WT_RES_3GPP_SPECTRAL_SEMMARGPOS_X "3GPP.Spectral.SemMargPosX"

/* power in dBm. Vector<double> is returned. */
#define WT_RES_3GPP_SPECTRAL_SEMMARGPOS_Y "3GPP.Spectral.SemMargPosY"

/*OBW in Hz.Value is returned*/
// #define WT_RES_SPECTRUM_OBW "Obw"

/*OBW start Freq,int value*/
// #define WT_RES_SPECTRUM_OBW_START_FREQ "Obw_start_freq"

/*OBW end Freq,int value*/
//#define WT_RES_SPECTRUM_OBW_END_FREQ "Obw_end_freq"

/*
<Summary>
*/
/* RmsEvm in %, double is return. */
#define WT_RES_3GPP_EVM_RMSEVM "3GPP.Evm.RmsEvm"

/* PeakEvm in %, double is return. */
#define WT_RES_3GPP_EVM_PEAKEVM "3GPP.Evm.PeakEvm"

/* DmrsEvm in %, double is return. */
#define WT_RES_3GPP_EVM_DMRSEVM "3GPP.Evm.DmrsEvm"

/* EVM 95 Percentile in %, double is return. */
#define WT_RES_3GPP_EVM_95TH_EVM "3GPP.Evm.Evm95th"

/* MErr 95 Percentile in %, double is return. */
#define WT_RES_3GPP_EVM_MAGNERR95TH "3GPP.Evm.MagnErr95th"

/* PhErr 95 Percentile in %, double is return. */
#define WT_RES_3GPP_EVM_PHASEERR95th "3GPP.Evm.PhaseErr95th"

/* Burst Power in %, double is return. */
#define WT_RES_3GPP_POWER_MEASBURSTPWR "3GPP.Power.MeasBurstPwr"

/* Power vs. Time Test in %, int is return. */
#define WT_RES_3GPP_POWER_TESTRESULT "3GPP.Power.TestResult"

/* Spectrum Modulation Freq. Test in %, int is return. */
#define WT_RES_3GPP_SPECTMOD_TESTRESULT "3GPP.SpectMod.TestResult"

/* Spectrum Switching Freq Test in %, int is return. */
#define WT_RES_3GPP_SPECTSWT_TESTRESULT "3GPP.SpectSwt.TestResult"

/* Frequency Error in Hz. Value is returned. */
// #define WT_RES_FREQ_ERR_HZ "signal.freqerr_hz"

/* Frames average power in dBm, double is return. */
#define WT_RES_3GPP_FRMAVGPWRDBM "3GPP.FrmAvgPwrdBm"

/* Frames peak power in dBm, double is return. */
#define WT_RES_3GPP_FRMPEAKPWRDBM "3GPP.FrmPeakPwrdBm"

/* NR-UTRA Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_NRUTRARESULT "3GPP.ACLR.NrUtraResult"

/* E-UTRA Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_EUTRARESULT "3GPP.ACLR.EutraResult"

/* UTRA Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_UTRARESULT "3GPP.ACLR.UtraResult"

/* ACLR Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_RESULT "3GPP.ACLR.AclrResult"

/* Spectrum Emission Mask Test  is pass or fail , int is return. */
#define WT_RES_3GPP_SPECTRAL_SEMRESULT "3GPP.Spectral.SEMResult"

/* Inband Emission Test  is pass or fail , int is return. */
#define WT_RES_3GPP_INEMIS_INEMISRESULT "3GPP.InEmis.InEmisResult"

/* IQ Match Amplitude Error in dB. Value is returned. */
//#define WT_RES_IQ_MATCH_AMP_DB      "iqmatch.amp_dB"

/* IQ Match Phase Error in deg. Value is returned. */
//#define WT_RES_IQ_MATCH_PHASE      "iqmatch.phase"

/*Spectrum Flatness Passed Or Failed. Int Value is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED "Spec_flatness_passed"

/*Carrier leakage in dB.Value is returned*/
// #define WT_RES_SPECTRUM_CARRIER_LEAKAGE "Carrier_leakage"

/*Subcarrier Power in dBm.double is returned*/
#define WT_RES_3GPP_SCAVGPWRDBM "3GPP.SCAvgPwrdBm"

/*Reference Signal Receiving Power in dBm.double is returned*/
#define WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_POWER "3GPP.Reference_Signal_Receiving_Power"

/*Received Signal Strength Indication in dBm.double is returned*/
#define WT_RES_3GPP_RECEIVED_SIGNAL_STRENGTH_INDICATION "3GPP.Received_Signal_Strength_Indication"

/*Reference Signal Receiving Quality in dB.double is returned*/
#define WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_QUALITY "3GPP.Reference_Signal_Receiving_Quality"

/*Signal to Noise Ratio in dB.double is returned*/
#define WT_RES_3GPP_SIGNAL_TO_NOISE_RATIO "3GPP.Signal_to_Noise_Ratio"

/*
<Data Info>
*/
/*LinkDirect, int is returned*/
#define WT_RES_3GPP_INFO_LINKDIRECT "3GPP.Info.LinkDirect"

/*Channel, int is returned*/
#define WT_RES_3GPP_INFO_CHANNEL "3GPP.Info.Channel"

/*Codeword, int is returned*/
#define WT_RES_3GPP_INFO_CODEWORD "3GPP.Info.Codeword"

/*Slot, int is returned*/
#define WT_RES_3GPP_INFO_SLOT "3GPP.Info.SlotIdx"

/*Codeword Modulate, int is returned*/
#define WT_RES_3GPP_INFO_CW_MODULATE "3GPP.Info.Codeword_Modulate"

/*Codeword Scrambling, int is returned*/
#define WT_RES_3GPP_INFO_CW_SCRAMBLING "3GPP.Info.Codeword_Scrambling"

/*TrChOnNum, vector<int> is returned*/
#define WT_RES_3GPP_DEINFO_TRCHONNUM "3GPP.Info.TrChOnNum"

/*Codeword Channel Coding Type, int is returned*/
#define WT_RES_3GPP_INFO_CW_CHANNELCODINGTYPE "3GPP.Info.Codeword_Channel_Coding_Type"

/*Codeword CRC, int is returned*/
#define WT_RES_3GPP_INFO_CW_CRC "3GPP.Info.Codeword_CRC"

/*Format, int is returned*/
#define WT_RES_3GPP_INFO_FORMAT "3GPP.Info.Format"

/*HARQ-ACK, int is returned*/
#define WT_RES_3GPP_INFO_HARQ_ACK "3GPP.Info.HarqAckInfo"

/*
<PHY Bit Information>
*/
/*Bit sequence of Codeword 0, Vector<char*> is returned*/
#define WT_RES_3GPP_INFO_CW_BITSEQ_0 "3GPP.Info.Cw.BitSeq_0"

/*Bit sequence of Codeword 1, Vector<char*> is returned*/
#define WT_RES_3GPP_INFO_CW_BITSEQ_1 "3GPP.Info.Cw.BitSeq_1"

/*Bit Length, Vector<int> is returned*/
#define WT_RES_3GPP_INFO_CW_BITLEN "3GPP.Info.Cw.BitLen"

/*
<EVM by symbol>
*/
/*Symbol Evm low Data, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_LOW_DATA "3GPP.Evm.SymbEvm.Low_Data"

/*Symbol Evm low DMRS, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_LOW_PILOT "3GPP.Evm.SymbEvm.Low_DMRS"

/*Symbol Evm high Data, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_HIGH_DATA "3GPP.Evm.SymbEvm.High_Data"

/*Symbol Evm high DMRS, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_HIGH_PILOT "3GPP.Evm.SymbEvm.High_DMRS"

/*
<EVM by Carrier>
*/
/*Max Carrier Len, X-axis Range. int is returned*/
#define WT_RES_3GPP_EVM_MAXCARRIERLEN "3GPP.Evm.MaxCarrierLen"

/*CarrierEvm, Complex[0] is Carrier ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_CARRIEREVM "3GPP.Evm.CarrierEvm"

/*
<Spectrum Flatness>
*/
/*Max Flat Number, X-axis Range. int is returned*/
#define WT_RES_3GPP_FLATNESS_MAXFLATNUM "3GPP.Flatness.MaxFlatNum"

/*Spectrum Flatness Data. Vector is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_DATA "Spec_flatness_data"

/*Spectrum Flatness Data. Vector is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA "Spec_flatness_maskup_data"

/*Spectrum Flatness Data. Vector is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA "Spec_flatness_maskdown_data"

/*Range Type, Complex[0] is Subcarrier ID, Complex[1] is Range Type. Vector<Complex> is returned*/
#define WT_RES_3GPP_FLATNESS_RANGETYPE "3GPP.Flatness.RangeType"

/*Ripple1 = max(range1)-min(rang1), double is returned*/
#define WT_RES_3GPP_FLATNESS_RIPPLE1 "3GPP.Flatness.Ripple1"

/*Ripple2 = max(range2)-min(rang2), double is returned*/
#define WT_RES_3GPP_FLATNESS_RIPPLE2 "3GPP.Flatness.Ripple2"

/*MaxR2SubMinR1 = max(range2)-min(rang1), double is returned*/
#define WT_RES_3GPP_FLATNESS_MAXR2SUBMINR1 "3GPP.Flatness.MaxR2SubMinR1"

/*MaxR1SubMinR2 = max(range1)-min(rang2), double is returned*/
#define WT_RES_3GPP_FLATNESS_MAXR1SUBMINR2 "3GPP.Flatness.MaxR1SubMinR2"

/*
<Power>
*/
/* points power. Vector is returned */
// #define WT_RES_POINTS_POWER "points.power"

/* window avg power. Vector is returned */
// #define WT_RES_WIN_AVG_POWER "avg.power"

// Frame start and end place is return
// #define WT_RES_FRAME_LOCATION "Power.Frame.Location"

/* Package average power in dBm, double is return. */
#define WT_RES_3GPP_PKGAVGPWRDBM "3GPP.PkgAvgPwrdBm"

/* Package average power in dBm, double is return. */
#define WT_RES_3GPP_PKGPEAKPWRDBM "3GPP.PkgPeakPwrdBm"

/*
<CCDF>
*/
/* Real vector containing CCDF probability values (Y-axis of CCDF plot) */
// #define WT_RES_CCDF_PROB "CCDF.prob"

/* Real vector containing CCDF power relative to average power in dB values
(X-axis of CCDF plot) */
// #define WT_RES_CCDF_POWER_REL_DB "CCDF.power_rel_dB"

/* Result for CCDF %10 %1 %0.1 %0.01 percentage‘s power. double vector Value is returned. */
// #define WT_RES_CCDF_PERCENT_POWER "CCDF.percent.power"

/* ccdf start relative power. double Value dBm */
// #define WT_RES_CCDF_START "CCDF.start"

/* ccdf scale value dBm. double value */
// #define WT_RES_CCDF_SCALE "CCDF.scale"

/*
<Constellation>
*/
/*RxPoint low Data, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_LOW_DATA "3GPP.Evm.RxPoint.Low_Data"

/*RxPoint low DMRS, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_LOW_PILOT "3GPP.Evm.RxPoint.Low_DMRS"

/*RxPoint high Data, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_HIGH_DATA "3GPP.Evm.RxPoint.High_Data"

/*RxPoint high DMRS, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_HIGH_PILOT "3GPP.Evm.RxPoint.High_DMRS"

/* reference const data in dB. Complex Vector is returned */
// #define WT_RES_CONST_REF "const.ref"

/*
<Inband Emission>
*/
/*RBEmis data, Complex[0] is RB number, Complex[1] is Power in dB.Vector<Complex> is returned*/
#define WT_RES_3GPP_INEMIS_RBEMIS "3GPP.InEmis.RBEmis"

/*RBEmis reference, Complex[0] is RB number, Complex[1] is Power in dB.Vector<Complex> is returned*/
#define WT_RES_3GPP_INEMIS_EMISREF "3GPP.InEmis.EmisRef"

/*RBEmis reference segment, Complex[0] is RB number, Complex[1] is segment number.Vector<Complex> is returned*/
#define WT_RES_3GPP_INEMIS_EMISREFSEG "3GPP.InEmis.EmisRefSeg"

/*MarginMin, double is returned*/
#define WT_RES_3GPP_INEMIS_MARGINMIN "3GPP.InEmis.MarginMin"

/*RB Index of MarginMin, int is returned*/
#define WT_RES_3GPP_INEMIS_MARGINMINIDX "3GPP.InEmis.MarginMinIdx"

/*
<Spectrum ACLR E-UTRA>
*/
/*ACLR E-URTA输出结果, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_POWER "3GPP.ACLR_EUtra.Power"

/*ACLR E-URTA输出参考, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_MASK "3GPP.ACLR_EUtra.Mask"

/*
E-UTRA2(△f < 0),
E-UTRA1(△f < 0),
Carrier,
E-UTRA1(△f > 0),
E-UTRA2(△f > 0),
Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_ACLRATIO "3GPP.ACLR_EUtra.AclRatio"

/*ACLR序列对应频率范围, Complex[0] is start freq in MHz, Complex[1] is end freq in MHz.Vector<Complex> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_FREQ "3GPP.ACLR_EUtra.Freq"

/*
<Spectrum ACLR NR-UTRA>
*/
/*ACLR NR-URTA输出结果, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_POWER "3GPP.ACLR_NrUtra.Power"

/*ACLR NR-URTA输出参考, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_MASK "3GPP.ACLR_NrUtra.Mask"

/*
NR-UTRA2(△f < 0),
NR-UTRA1(△f < 0),
Carrier,
NR-UTRA1(△f > 0),
NR-UTRA2(△f > 0),
Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_ACLRATIO "3GPP.ACLR_NrUtra.AclRatio"

/*ACLR序列对应频率范围, Complex[0] is start freq in MHz, Complex[1] is end freq in MHz.Vector<Complex> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_FREQ "3GPP.ACLR_NrUtra.Freq"

/*
<Spectrum ACLR UTRA>
*/
/*ACLR URTA输出结果, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_POWER "3GPP.ACLR_Utra.Power"

/*ACLR URTA输出参考, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_MASK "3GPP.ACLR_Utra.Mask"

/*
UTRA2(△f < 0),
UTRA1(△f < 0),
Carrier,
UTRA1(△f > 0),
UTRA2(△f > 0),
Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_ACLRATIO "3GPP.ACLR_Utra.AclRatio"

/*ACLR序列对应频率范围, Complex[0] is start freq in MHz, Complex[1] is end freq in MHz.Vector<Complex> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_FREQ "3GPP.ACLR_Utra.Freq"

/*
<Phase Error>
*/
/*Phase Error low, Complex[0] is symbol number, Complex[1] is Phase Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERR_LOW "3GPP.MPErr.PhaseErr.Low"

/*Phase Error high, Complex[0] is symbol number, Complex[1] is Phase Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERR_HIGH "3GPP.MPErr.PhaseErr.High"

/*Phase Error low and high PhaseErrRms, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRRMS "3GPP.MPErr.PhaseErrRms"

/*Phase Error low and high PhaseErrPeak, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRPEAK "3GPP.MPErr.PhaseErrPeak"

/*Phase Error low and high PhaseErrDmrs, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRDMRS "3GPP.MPErr.PhaseErrDmrs"

/*Phase Error Limit, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRLIMIT "3GPP.MPErr.PhaseErrLimit"

/*Phase Error Limit, Complex vector is returned*/
#define WT_RES_3GPP_MPERR_PHASEERROUT "3GPP.MPErr.PhaseErrOut"

/*
<Magnitude Error>
*/
/*Magnitude Error low, Complex[0] is symbol number, Complex[1] is Magnitude Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERR_LOW "3GPP.MPErr.MagnErr.Low"

/*Magnitude Error high, Complex[0] is symbol number, Complex[1] is Magnitude Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERR_HIGH "3GPP.MPErr.MagnErr.High"

/*Magnitude Error Out MagnErrRms, Complex vector is returned*/
#define WT_RES_3GPP_MPERR_MAGNERROUT "3GPP.MPErr.MagnErrOut"

/*Magnitude Error Limit MagnErrLimit, double is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRLIMIT "3GPP.MPErr.MagnErrLimit"

/*Magnitude Error low and high MagnErrRms, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRRMS "3GPP.MPErr.MagnErrRms"

/*Magnitude Error low and high MagnErrPeak, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRPEAK "3GPP.MPErr.MagnErrPeak"

/*Magnitude Error low and high MagnErrDmrs, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRDMRS "3GPP.MPErr.MagnErrDmrs"

/*SyncSlotId, int is returned*/
#define WT_RES_3GPP_SYNC_SLOTID "3GPP.SyncSlotId"

/*
<UE Power>
*/
/*UE Power Out, Vector<Complex> is returned*/
#define WT_RES_3GPP_UEPOWER_POWEROUT "3GPP.UEPower.PwrOut"

/*
<Phase Discontinuity>
*/
/*Phase Discontinuity Out, Vector<Complex> is returned*/
#define WT_RES_3GPP_PHASEDIS_DIFFOUT "3GPP.PhaseDis.PhaseDiffOut"

/*Phase Discontinuity Dynamic Limit, Vector<double> is returned*/
#define WT_RES_3GPP_PHASEDIS_DYNAMICLIMIT "3GPP.PhaseDis.DynamicLimit"

/*Phase Discontinuity Dynamic Limit, Vector<double> is returned*/
#define WT_RES_3GPP_PHASEDIS_UPPERLIMIT "3GPP.PhaseDis.UpperLimit"

/*Phase Discontinuity Max PhaseDis, double is returned*/
#define WT_RES_3GPP_PHASEDIS_MAXPHASEDIS "3GPP.PhaseDis.MaxPhaseDis"

/*Phase Discontinuity Min SlotDis, int is returned*/
#define WT_RES_3GPP_PHASEDIS_MINSLOTDIS "3GPP.PhaseDis.MinSlotDis"

/*Phase Discontinuity Exceed Upper Limit Num, int is returned*/
#define WT_RES_3GPP_PHASEDIS_EXCEEDUPPERLIMITNUM "3GPP.PhaseDis.ExceedUpperLimitNum"

/*Phase Discontinuity Exceed Dynamic Limit Num, int is returned*/
#define WT_RES_3GPP_PHASEDIS_EXCEEDDYNAMICLIMITNUM "3GPP.PhaseDis.ExceedDynamicLimitNum"

/*
<Frequency Error>
*/
/*Frequency Error Out, Vector<Complex> is returned*/
#define WT_RES_3GPP_FREQERR_FREQERROUT "3GPP.FreqErr.FreqErrOut"

/*Frequency Error Limit, Vector<double> is returned*/
#define WT_RES_3GPP_FREQERR_FREQERRLIMIT "3GPP.FreqErr.FreqErrLimit"

/*Frequency Error, Vector<double> is returned*/
#define WT_RES_3GPP_FREQERR_FREQERR "3GPP.FreqErr.FreqErr"

/*
<Power vs Time>
*/
/*Power Symbol, Vector<double> is returned*/
#define WT_RES_3GPP_POWER_SYMBOL "3GPP.Power.Symbol"

/*Power Power, Vector<double> is returned*/
#define WT_RES_3GPP_POWER_POWER "3GPP.Power.Power"

/*Power Measure Burst Num, int is returned*/
#define WT_RES_3GPP_POWER_MEASURE_BURSTNUM "3GPP.Power.MeasBurstNum"

/*Power Burst Up Mask, Vector<Complex> is returned*/
#define WT_RES_3GPP_POWER_BURST_UPMASK "3GPP.Power.Burst.UpMask"

/*Power Burst Down Mask, Vector<Complex> is returned*/
#define WT_RES_3GPP_POWER_BURST_DOWNMASK "3GPP.Power.Burst.DownMask"

/*Power Burst XMask, Vector<double> is returned*/
#define WT_RES_3GPP_POWER_BURST_XMARK "3GPP.Power.Burst.XMark"

/*Power Burst Average Power, double is returned*/
#define WT_RES_3GPP_POWER_BURST_AVERAGE_POWER "3GPP.Power.Burst.AveragePower"

/*Power Burst Tsc Type, int is returned*/
#define WT_RES_3GPP_POWER_BURST_TSC_TYPE "3GPP.Power.Burst.TscType"

/*Power Burst Type, int is returned*/
#define WT_RES_3GPP_POWER_BURST_TYPE "3GPP.Power.Burst.Type"

/*Power Measure Part Min, int is returned*/
#define WT_RES_3GPP_POWER_BURST_MEASURE_PART_MIN "3GPP.Power.Burst.MeasurePartMin"

/*Power Measure Part Max, int is returned*/
#define WT_RES_3GPP_POWER_BURST_MEASURE_PART_MAX "3GPP.Power.Burst.MeasurePartMax"

/*Power Measure SV, int is returned*/
#define WT_RES_3GPP_POWER_BURST_MEASURE_SV "3GPP.Power.Burst.MeasureSV"

/*
<Spectrum Modulation>
*/
/*Spectrum Modulation Frequency, Vector<Complex> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_FREQ "3GPP.SpecMod.Freq"

/*Spectrum Modulation Power, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_POWER "3GPP.SpecMod.Power"

/*Spectrum Modulation Mask, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_MASK "3GPP.SpecMod.Mask"

/*Spectrum Modulation Symbol, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_SYMBOL "3GPP.SpecMod.Symbol"

/*Spectrum Modulation Time, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_TIME "3GPP.SpecMod.Time"

/*
<Spectrum Switching>
*/
/*Spectrum Switching Frequency, Vector<Complex> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_FREQ "3GPP.SpecSwt.Freq"

/*Spectrum Switching Power, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_POWER "3GPP.SpecSwt.Power"

/*Spectrum Switching Mask, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_MASK "3GPP.SpecSwt.Mask"

/*Spectrum Switching Symbol, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_SYMBOL "3GPP.SpecSwt.Symbol"

/*Spectrum Switching Time, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_TIME "3GPP.SpecSwt.Time"

/*
<base result>
*/
// #define WT_RES_BASE_RESULT "result.base"

/*
<Evm vs. Modulation Symbols>
*/
/*每个调制符号的EVM值, Complex[0] is Modulation Symbols number, Complex[1] is Evm in dB.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_POINTEVM "3GPP.Evm.PointEvm"

/*
<Magnitude Error vs chip>
*/
#define WT_RES_3GPP_MPERR_CHIP "3GPP.MPErr.MagnErrChip"
#define WT_RES_3GPP_MPERR_CHIPLIMIT "3GPP.MPErr.MagnErrChipLimit"

/*
<Phase Error vs chip>
*/
#define WT_RES_3GPP_MPERR_PHASECHIP "3GPP.MPErr.PhaseErrChip"
#define WT_RES_3GPP_MPERR_PHASECHIPLIMIT "3GPP.MPErr.PhaseErrChipLimit"

/*
<Error Vector Magnitude>
*/
#define WT_RES_3GPP_EVM_EVMOUT "3GPP.Evm.EvmOut"
#define WT_RES_3GPP_EVM_LIMIT "3GPP.Evm.EvmLimit"
#define WT_RES_3GPP_EVM_CHIP "3GPP.Evm.EvmChip"
#define WT_RES_3GPP_EVM_CHIPLIMIT "3GPP.Evm.EvmChipLimit"

/*
<CDP vs Slot>
*/
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPCCHPWROUT "3GPP.CodeDomain.CDP.DPCCHPwrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPDCHPWROUT "3GPP.CodeDomain.CDP.DPDCHPwrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDP_HSDPCCHPWROUT "3GPP.CodeDomain.CDP.HSDPCCHPwrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPCCHPWR "3GPP.CodeDomain.CDP.DPCCHPwr"
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPDCHPWR "3GPP.CodeDomain.CDP.DPDCHPwr"
#define WT_RES_3GPP_CODEDOMAIN_CDP_HSDPCCHPWR "3GPP.CodeDomain.CDP.HSDPCCHPwr"

/*
<Relative CDE>
*/
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHERROUT "3GPP.CodeDomain.CDE.DPCCHErrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHERROUT "3GPP.CodeDomain.CDE.DPDCHErrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHERROUT "3GPP.CodeDomain.CDE.HSDPCCHErrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHERR "3GPP.CodeDomain.CDE.DPCCHErr"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHERR "3GPP.CodeDomain.CDE.DPDCHErr"
#define WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHERR "3GPP.CodeDomain.CDE.HSDPCCHErr"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHSF "3GPP.CodeDomain.CDE.DPCCHSf"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHSF "3GPP.CodeDomain.CDE.DPDCHSf"
#define WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHSF "3GPP.CodeDomain.CDE.HSDPCCHSf"

/*
<CDP/CDE IQ-Signal>
*/
#define WT_RES_3GPP_CDM_CODENUM "3GPP.CDM.CodeNum"
#define WT_RES_3GPP_CDM_CDP_IMONITORVAL "3GPP.CDM.CDP.IMonitorVal"
#define WT_RES_3GPP_CDM_CDP_QMONITORVAL "3GPP.CDM.CDP.QMonitorVal"
#define WT_RES_3GPP_CDM_CDE_IMONITORVAL "3GPP.CDM.CDE.IMonitorVal"
#define WT_RES_3GPP_CDM_CDE_QMONITORVAL "3GPP.CDM.CDE.QMonitorVal"

#define WT_RES_3GPP_UEPOWER_PWR "3GPP.UEPower.Pwr"
#define WT_RES_3GPP_IQOFFSET "3GPP.IQOffset"
#define WT_RES_3GPP_IQIMBA "3GPP.IQImba"
#define WT_RES_3GPP_PHASEDIS_PHASEDIFF "3GPP.PhaseDis.PhaseDiff"

/*
<Power Dynamics>
*/
#define WT_RES_3GPP_POWERDYN_NUM "3GPP.PowerDyn.Num"
#define WT_RES_3GPP_POWERDYN_TRACE_TIME "3GPP.PowerDyn.TraceTime"
#define WT_RES_3GPP_POWERDYN_TRACE_POWER "3GPP.PowerDyn.TracePower"
#define WT_RES_3GPP_POWERDYN_OUTER_POWER "3GPP.PowerDyn.OuterPower"
#define WT_RES_3GPP_EVM_SYMBOL_CARRIER_LENGTH "3GPP.Evm.SymbolCarrierLength"
#define WT_RES_3GPP_EVM_SYMBOL_CARRIER_NUM "3GPP.Evm.SymbolCarrierNum"
#define WT_RES_3GPP_EVM_SYMBOL_CARRIER_EVM "3GPP.Evm.SymbolCarrierEvm"

/*
<PBCH info>
*/
#define WT_RES_3GPP_SSB_NUM "3GPP.SSBNum"
#define WT_RES_3GPP_SSB_INDEX "3GPP.SSBInfo.SSBIdx"
#define WT_RES_3GPP_SSB_STATE "3GPP.SSBInfo.State"
#define WT_RES_3GPP_SSB_PSS_EVM "3GPP.SSBInfo.PSSEvm"
#define WT_RES_3GPP_SSB_SSS_EVM "3GPP.SSBInfo.SSSEvm"
#define WT_RES_3GPP_SSB_PBCH_EVM "3GPP.SSBInfo.PBCHEVM"
#define WT_RES_3GPP_SSB_CRC "3GPP.SSBInfo.Crc"
#define WT_RES_3GPP_SSB_HALF_FRAME "3GPP.SSBInfo.HalfFrm"
#define WT_RES_3GPP_SSB_SYSTEM_FRAME_NUM "3GPP.SSBInfo.SFN"
#define WT_RES_3GPP_SSB_DMRS_POS "3GPP.SSBInfo.DmrsTypeAPos"
#define WT_RES_3GPP_SSB_COMMON_SUB_SPACING "3GPP.SSBInfo.SCSCommon"
#define WT_RES_3GPP_SSB_SUB_OFFSET "3GPP.SSBInfo.SCOffset"
#define WT_RES_3GPP_SSB_CORESET_ZERO "3GPP.SSBInfo.CoresetZero"
#define WT_RES_3GPP_SSB_SEARCH_SPACE_ZERO "3GPP.SSBInfo.SSZero"
#define WT_RES_3GPP_SSB_CELL_BARRED "3GPP.SSBInfo.CellBarre"
#define WT_RES_3GPP_SSB_INTRA_FREQ_RESEL "3GPP.SSBInfo.InFreqResel"

/*===========================================================================
 listmod 3GPP result
 ===========================================================================*/
#define WT_RES_3GPP_LIST_MODU_CURRENT "3GPP.LIST.MODU.CURRENT"
#define WT_RES_3GPP_LIST_MODU_AVERAGE "3GPP.LIST.MODU.AVERAGE"
#define WT_RES_3GPP_LIST_MODU_SDTEVIAION "3GPP.LIST.MODU.SDTEVIAION"
#define WT_RES_3GPP_LIST_MODU_EXTREME "3GPP.LIST.MODU.EXTREME"
#define WT_RES_3GPP_LIST_IEMISSION_MARGIN_CURRENT "3GPP.LIST.IEMISSION.MARGIN.CURRENT"
#define WT_RES_3GPP_LIST_IEMISSION_MARGIN_AVERAGE "3GPP.LIST.IEMISSION.MARGIN.AVERAGE"
#define WT_RES_3GPP_LIST_IEMISSION_MARGIN_SDTEVIAION "3GPP.LIST.IEMISSION.MARGIN.SDTEVIAION"
#define WT_RES_3GPP_LIST_IEMISSION_MARGIN_EXTREME "3GPP.LIST.IEMISSION.MARGIN.EXTREME"
#define WT_RES_3GPP_LIST_IEMISSION_MARGIN_CURRENT_RBINDEX "3GPP.LIST.IEMISSION.MARGIN.CURRENT.RBINDEX"
#define WT_RES_3GPP_LIST_IEMISSION_MARGIN_EXTREME_RBINDEX "3GPP.LIST.IEMISSION.MARGIN.CURRENT.RBINDEX"
#define WT_RES_3GPP_LIST_ESFLATNESS_CURRENT "3GPP.LIST.ESFLATNESS.CURRENT"
#define WT_RES_3GPP_LIST_ESFLATNESS_AVERAGE "3GPP.LIST.ESFLATNESS.AVERAGE"
#define WT_RES_3GPP_LIST_ESFLATNESS_SDTEVIAION "3GPP.LIST.ESFLATNESS.SDTEVIAION"
#define WT_RES_3GPP_LIST_ESFLATNESS_EXTREME "3GPP.LIST.ESFLATNESS.EXTREME"
#define WT_RES_3GPP_LIST_ESFLATNESS_CURRENT_SCINDEX "3GPP.LIST.ESFLATNESS.CURRENT.SCINDEX"
#define WT_RES_3GPP_LIST_SEMASK_CURRENT "3GPP.LIST.SEMASKE.CURRENT"
#define WT_RES_3GPP_LIST_SEMASK_AVERAGE "3GPP.LIST.SEMASKE.AVERAGE"
#define WT_RES_3GPP_LIST_SEMASK_SDTEVIAION "3GPP.LIST.SEMASKE.SDTEVIAION"
#define WT_RES_3GPP_LIST_SEMASK_EXTREME "3GPP.LIST.SEMASKE.EXTREME"
#define WT_RES_3GPP_LIST_SEMASK_MARGIN "3GPP.LIST.SEMASKE.MARGIN"
#define WT_RES_3GPP_LIST_ACLR_CURRENT "3GPP.LIST.ACLR.CURRENT"
#define WT_RES_3GPP_LIST_ACLR_AVERAGE "3GPP.LIST.ACLR.AVERAGE"
#define WT_RES_3GPP_LIST_POWER_AVERAGE "3GPP.LIST.POWER.AVERAGE"

/*===========================================================================
 3GPP result End
 ===========================================================================*/
/*===========================================================================
 WISUN result
 ===========================================================================*/
/*WISUN PHR CRC Check, int*/
#define WT_RES_WISUN_PHR_CRC_CHECK                  "WiSun.phr_crc_cheak"

/*WISUN PHR Rate Field, int*/
#define WT_RES_WISUN_PHR_MCS                         "WiSun.mcs"

/*WISUN PHR Frame Len, int*/
#define WT_RES_WISUN_PHR_FRAME_LENGTH                "WiSun.phr_frame_length"

/*WISUN PHR Scrambler, int*/
#define WT_RES_WISUN_PHR_SCRAMBLER                    "WiSun.phr_scrambler"

/*WISUN Data Rate, double*/
#define WT_RES_WISUN_DATA_RATE                        "WiSun.data_rate"

/*WISUN BW, double*/
#define WT_RES_WISUN_BW                                "WiSun.bw"

/*WISUN Symbol count, int*/
#define WT_RES_WISUN_SYMBOL_COUNT                      "WiSun.symbol_count"

/*WISUN Coding rate, int*/
#define WT_RES_WISUN_CODING_RATE                       "WiSun.coding_rate"

/*WISUN Modulation type, int*/
#define WT_RES_WISUN_MODULATION_TYPE                   "WiSun.modulation"

/*WISUN PSDU length, int*/
#define WT_RES_WISUN_PSDU_LENGTH                       "WiSun.psdu_length"

/*WISUN PSDU CRC, int*/
#define WT_RES_WISUN_PSDU_CRC                          "WiSun.psdu_crc"

/*WISUN PHR Bit, int[36]*/
#define WT_RES_WISUN_PHR_BIT                           "WiSun.phr_bit"

/*WISUN CRC Bit, int[8]*/
#define WT_RES_WISUN_PHR_CRC_BIT                       "WiSun.phr_crc_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_SHR_SFD_BIT                    "WiSun.fsk_shr_sfd_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_PHR_BIT                         "WiSun.fsk_phr_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_PHY_PAYLOAD_BIT                  "WiSun.fsk_phy_payload_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_CRC_BIT                           "WiSun.fsk_crc_bit"


#define WT_RES_WISUN_EVM_OFFSET_DB            "wisun.evm_offset"

/* double vector is returned, phase_err vs chip*/
#define WT_RES_WISUN_PHASE_ERR_VS_CHIP        "wisun.phase_err.chip"

/* stLRWPAN_OQPSK_eye vector is returned*/
#define WT_RES_WISUN_EYE_REAL                 "wisun.eye_real"

/* stLRWPAN_OQPSK_eye vector is returned*/
#define WT_RES_WISUN_EYE_IMAG                 "wisun.eye_imag"

/* stLRWPAN_FSK_eye vector is returned  */
#define WT_RES_WISUN_FSK_EYE                   "wisun.fsk_eye"

#define WT_RES_WSUN_FSK_SPEC_ACP_MASK          "wisun.fsk_acp_mask"

#define WT_RES_WSUN_FSK_SPEC_ACP_Y              "wisun.fsk_acp_y"

#define WT_RES_WSUN_FSK_SPEC_ACP_X               "wisun.fsk_acp_x"

#define WT_RES_WSUN_FSK_SPEC_ACP_WIDTH          "wisun.fsk_acp_width"

#define WT_RES_WSUN_FSK_SPEC_ACP_VALID_NUM          "wisun.fsk_acp_Valid_num"

#endif
