
#include "scpi_digital_IQ.h"
#include "commonhandler.h"
#include <iostream>
#include <future>
#include "basehead.h"
#include "../../general/protocolsub.h"
#include "tester.h"
#include "internal.h"
#include "tester_mt.h"
#include <sys/time.h>
#include <math.h>
#include <unistd.h>
#include "wtlog.h"
#define MAX_CHANNEL_COUNT 8
scpi_result_t SCPI_DigIQ_SetTriggerTimeOut(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double timeOut = 0.0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamDouble(context, &timeOut, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.TriggerTimeout = timeOut;

    }while(0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetRecvTimeOut(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double timeOut = 0.0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamDouble(context, &timeOut, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.RecvTimeout = timeOut;
        attr->vsaParam.TrigTimeout = timeOut;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetDestMac(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    char mac[256] = {0};
    size_t copyLen = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamCopyText(context, mac, sizeof(mac) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if(strlen(mac) != 12)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        memcpy(attr->m_DigtalIQParam.DstMac, mac, copyLen);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int mode = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &mode, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        mode = (0 == mode ? 0 : 1);
        attr->m_DigtalIQTestFixture.Enbale = mode;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureTriggerCnt(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int cnt = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &cnt, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        cnt = (1 > cnt ? 1 : cnt);

        attr->m_DigtalIQTestFixture.SendCnt = cnt;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureTriggerGap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int gap = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &gap, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        gap = (0 > gap ? 0 : gap);
        attr->m_DigtalIQTestFixture.SendGap = gap;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureTriggerPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &value, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        value = (0 > value ? 0 : value);
        attr->m_DigtalIQTestFixture.SendPeriod = value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureSendTimeOut(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double value = 0.0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamDouble(context, &value, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQTestFixture.SendTimeout = value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureDelayPerPacket(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double value = 0.0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamDouble(context, &value, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQTestFixture.VsgFixtureDelay = value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetApply(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        iRet = WT_SetDigtalIQTestFixture(attr->ConnID, &attr->m_DigtalIQTestFixture);
        iRet |= WT_SetDigtalIQParam(attr->ConnID, &attr->m_DigtalIQParam);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetFixtureApply(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        iRet = WT_SetDigtalIQTestFixture(attr->ConnID, &attr->m_DigtalIQTestFixture);
        iRet |= WT_SetDigtalIQParam(attr->ConnID, &attr->m_DigtalIQParam);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetVSGPacketTimeOut(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double timeOut = 0.0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamDouble(context, &timeOut, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.VSGISTimeout = timeOut;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetVSAPacketTimeOut(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double timeOut = 0.0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamDouble(context, &timeOut, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.VSAISTimeout = timeOut;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetInterVsaAction(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        char buf[256] = {0};
        size_t copyLen = 0;
        if (!SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        int ActionMask = 0;
        for (int i = 0; i < copyLen; i++)
        {
            //先标记无效的动作
            if (buf[i] == '0')
            {
                ActionMask |= 0x1u << i;
            }
        }
        //取反，得到有效动作掩码
        attr->m_DigtalIQParam.VsaActionMask = ~ActionMask;
        WTLog::Instance().WriteLog(LOG_DEBUG, "VsaActionMask=%#x\n", attr->m_DigtalIQParam.VsaActionMask);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetInterVsgAction(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        char buf[256] = {0};
        size_t copyLen = 0;
        if (!SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        int ActionMask = 0;
        for (int i = 0; i < copyLen; i++)
        {
            //先标记无效的动作
            if (buf[i] == '0')
            {
                ActionMask |= 0x1u << i;
            }
        }
        //取反，得到有效动作掩码
        attr->m_DigtalIQParam.VsgActionMask = ~ActionMask;
        WTLog::Instance().WriteLog(LOG_DEBUG, "VsgActionMask=%#x\n", attr->m_DigtalIQParam.VsgActionMask);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetVsaMaxBitCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int MaxBits = 13;

        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &MaxBits, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.VsaMaxBitCnt = MaxBits;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_SetVsgMaxBitCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int MaxBits = 13;

        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &MaxBits, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.VsgMaxBitCnt = MaxBits;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_GetVsaPacketCnt(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_GET_VSA_DIG_PACKET_CNT;
        SubCmd.SendBuf = nullptr;
        SubCmd.SendDataLen = 0;
        SubCmd.RecvBuf = reinterpret_cast<char*>(&ParamVal);
        SubCmd.RecvBufLen = sizeof(ParamVal);
        Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsaPacketCnt=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DigIQ_GetVsgPacketCnt(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_GET_VSG_DIG_PACKET_CNT;
        SubCmd.SendBuf = nullptr;
        SubCmd.SendDataLen = 0;
        SubCmd.RecvBuf = reinterpret_cast<char*>(&ParamVal);
        SubCmd.RecvBufLen = sizeof(ParamVal);
        Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsgPacketCnt=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DigIQ_SetVSACellChannelList(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int ChannelIdCount = 0;
        int ChannelId = 0;

        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &ChannelIdCount, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        
        for (int i = 0; i < ChannelIdCount; i++)
        {
            if (!SCPI_ParamInt(context, &ChannelId, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            if (ChannelId < 0 || ChannelId >= MAX_CHANNEL_COUNT)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;            
            }
            else
            {
                attr->m_DigtalIQParam.VSAChannelIdList[i] = (char)ChannelId;
            }
        }

        while (ChannelIdCount < MAX_CHANNEL_COUNT)
        {
            attr->m_DigtalIQParam.VSAChannelIdList[ChannelIdCount] = -1;
            ChannelIdCount++;
        }

        // if (TF_TB_TESTER_AS_STA == attr->TesterLinkMode || TF_TB_TESTER_AS_AP == attr->TesterLinkMode)
        // {
        //     DigtalIQParam &Param = attr->m_DigtalIQParam;
        //     memcpy(Param.VSGChannelIdList, Param.VSAChannelIdList, sizeof(Param.VSAChannelIdList));
        // }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_GetVSACellChannelList(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for(int i = 0; attr->m_DigtalIQParam.VSAChannelIdList[i] >=0 && i<MAX_CHANNEL_COUNT;i++)
    {
        SCPI_ResultInt(context, attr->m_DigtalIQParam.VSAChannelIdList[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t SCPI_DigIQ_SetVSGCellChannelList(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int ChannelIdCount = 0;
        int ChannelId = 0;

        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &ChannelIdCount, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        
        for (int i = 0; i < ChannelIdCount; i++)
        {
            if (!SCPI_ParamInt(context, &ChannelId, true))
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            if (ChannelId < 0 || ChannelId >= MAX_CHANNEL_COUNT)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;            
            }
            else
            {
                attr->m_DigtalIQParam.VSGChannelIdList[i] = (char)ChannelId;
            }
        }

        while(ChannelIdCount<MAX_CHANNEL_COUNT)
        {
            attr->m_DigtalIQParam.VSGChannelIdList[ChannelIdCount] = -1;
            ChannelIdCount++;
        }

        // if (TF_TB_TESTER_AS_STA == attr->TesterLinkMode || TF_TB_TESTER_AS_AP == attr->TesterLinkMode)
        // {
        //     DigtalIQParam &Param = attr->m_DigtalIQParam;
        //     memcpy(Param.VSAChannelIdList, Param.VSGChannelIdList, sizeof(Param.VSGChannelIdList));
        // }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_DigIQ_GetVSGCellChannelList(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for(int i = 0; attr->m_DigtalIQParam.VSGChannelIdList[i] >=0 && i<MAX_CHANNEL_COUNT;i++)
    {
        SCPI_ResultInt(context, attr->m_DigtalIQParam.VSGChannelIdList[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t SCPI_DigIQ_SetVSGWaveRatio(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;

        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        attr->m_DigtalIQParam.VSGPnRatioMode = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}
