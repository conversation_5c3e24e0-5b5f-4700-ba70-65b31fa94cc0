#ifndef _MAC_ENCRYTION_EXPORT_H_
#define _MAC_ENCRYTION_EXPORT_H_

#ifndef LINUX
#ifdef WTTESTERAPI_MAC_ENCRYPTION_EXPORTS
#define WTTESTER_MAC_ENCRYPTION_API __declspec(dllexport)
#else
#define WTTESTER_MAC_ENCRYPTION_API __declspec(dllimport)
#endif
#else
#ifdef WTTESTERAPI_MAC_ENCRYPTION_EXPORTS
#define WTTESTER_MAC_ENCRYPTION_API __attribute((visibility("default")))
#else
#define WTTESTER_MAC_ENCRYPTION_API
#endif
#endif


#ifdef __cplusplus

#define WT_MAC_ENCRYPT_NONE         "NONE"
#define WT_MAC_ENCRYPT_WEP40        "WEP-40"
#define WT_MAC_ENCRYPT_WEP104       "WEP-104"
#define WT_MAC_ENCRYPT_WEP128       "WEP-128"
#define WT_MAC_ENCRYPT_TKIP         "TKIP"
#define WT_MAC_ENCRYPT_CCMP128      "CCMP-128"
#define WT_MAC_ENCRYPT_CCMP256      "CCMP-256"
#define WT_MAC_ENCRYPT_GCMP128      "GCMP-128"
#define WT_MAC_ENCRYPT_GCMP256      "GCMP-256"
#define WT_MAC_ENCRYPT_SM4_OFB      "SM4-OFB"
#define WT_MAC_ENCRYPT_SM4_GCM      "SM4-GCM"

#define LEN_TK_NONE         0
#define LEN_TK_WEP40        5
#define LEN_TK_WEP104       13
#define LEN_TK_WEP128       16
#define LEN_TK_TKIP         16
#define LEN_TK_CCMP_128     16
#define LEN_TK_CCMP_256     32
#define LEN_TK_GCMP_128     16
#define LEN_TK_GCMP_256     32
#define LEN_TK_SMS4_OFB     16
#define LEN_TK_SMS4_GCM     16

enum WT_ENCRY_ERR_ENUM
{
    WT_ENCRY_ERR_CODE_OK,               // OK
    WT_ENCRY_ERR_CODE_Fail,             // Fail
    WT_ENCRY_ERR_CODE_NOT_SUPPORT,      // Not support
    WT_ENCRY_ERR_CODE_NOT_ENCRYPT,       // Data not encryption
    WT_ENCRY_ERR_CODE_CRC_Fail,
    WT_ENCRY_ERR_CODE_Data_Too_Short,
    WT_ENCRY_ERR_CODE_DATA_ENCRYPTED,   // not plaintext msdu data
    WT_ENCRY_ERR_CODE_NOT_DATA_FRAME,   // not data frame
};

enum LLC_TYPE_OR_LENGTH_ENUM
{
    ETHERNET_TYPE_ENUM, // ethernet type
    LLC_8023_LENGTH_ENUM // 802.3 llc
};

#pragma pack(1)
typedef	struct
{
    unsigned char InitVector[3];
    unsigned char Pad : 6;
    unsigned char KeyID : 2;
}WEP_IV;

typedef	struct
{
    union
    {
        struct
        {
            unsigned char TSC1;
            unsigned char WEPSeed;
            unsigned char TSC0;
            union
            {
                struct
                {
                    unsigned char Rsvd : 5;
                    unsigned char ExtIV : 1;
                    unsigned char KeyID : 2;
                }field;
                unsigned char Byte;
            }CONTROL;
        }field;
        unsigned int word;
    }IV16;
    union
    {
        struct
        {
            unsigned char TSC2;
            unsigned char TSC3;
            unsigned char TSC4;
            unsigned char TSC5;
        }field;
        unsigned int word;
    }IV32;
}TKIP_IV;


typedef	struct
{
    unsigned char PN0;
    unsigned char PN1;
    unsigned char Rsvd;
    union
    {
        struct
        {
            unsigned char Rsvd : 5;
            unsigned char ExtIV : 1;
            unsigned char KeyID : 2;
        }field;
        unsigned char Byte;
    }CONTROL;
    unsigned char PN2;
    unsigned char PN3;
    unsigned char PN4;
    unsigned char PN5;
}CCMP_Header;

typedef	struct
{
    unsigned char PN0;
    unsigned char PN1;
    unsigned char Rsvd;
    union
    {
        struct
        {
            unsigned char Rsvd : 5;
            unsigned char ExtIV : 1;
            unsigned char KeyID : 2;
        }field;
        unsigned char Byte;
    }CONTROL;
    unsigned char PN2;
    unsigned char PN3;
    unsigned char PN4;
    unsigned char PN5;
}GCMP_Header;

typedef struct
{
    unsigned char KeyID;
    unsigned char Reserved;
    unsigned char PN[16];
} WAPI_Header;


typedef struct {
    unsigned char da[6];
    unsigned char sa[6];
    unsigned short type;
    char type_name[256];
}EthernetDataType;

typedef struct {
    unsigned char DSAP;
    unsigned char SSAP;
    unsigned char Control;
    unsigned char VendorCode[3];
    unsigned char type[2];
}LLC_Header;

typedef struct {
    unsigned char da[6];
    unsigned char sa[6];
    unsigned short length;
    LLC_Header llc_header;
}LLC_802_3_header;

#pragma pack()

typedef struct
{
    char passphrase[64];
    unsigned int passphrase_len;
    char ssid[32];
    unsigned int ssid_len;
}PSK_KeyParam;

typedef struct
{
    unsigned char psk[32];
    unsigned char snonce[32];
    unsigned char anonce[32];
    unsigned char saddr[6];
    unsigned char aaddr[6];
}TK_KeyParam;


typedef struct
{
    unsigned char bk[16];       // BK
    unsigned char n1[32];       // AE nonce(AP)
    unsigned char n2[32];       // ASUE nonce(STA)
    unsigned char aaddr[6];     // AE MAC(AP)
    unsigned char saddr[6];     // ASUE MAC(STA)
}WAPI_TKParam;

typedef struct
{
    int aad_mode;       // AAD 构造方式：2006或者2020；SMS4-OFB默认2006版， SMS4-GCM 默认2020版
    int fc_htc;         // 0 = 使用framecontrol里面的htc值，1 = 强制framecontrol里面的htc值等于0。默认值0
    int amsdu_capab;    // 仅在AAD 2020版时有效。0 = 表示STA or SPP A-MSDU Capab = 0， 1 = 表示STA and SPP A-MSDU Capab = 1
} WAPI_ExtInfo;


extern "C"
{
#endif
    /**
     * @brief 获取加解密方式
     *
     * @param type :加解密类型，参考define WT_MAC_ENCRYPT_xxx
     * @return 成功返回非nullptr handle，错误返回nullptr
     */
    WTTESTER_MAC_ENCRYPTION_API void* WT_MAC_Encryption_Open(const char* type);

    /**
     * @brief 释放加密资源，与WT_MAC_Encryption_Open 配对使用.
     *
     * @param ptr ：WT_MAC_Encryption_Open返回的地址指针
     * @return :void
     */
    WTTESTER_MAC_ENCRYPTION_API void WT_MAC_Encryption_Close(void *ptr);

    /**
     * @brief TKIP、CCMP、GCMP加密时生成PSK，不支持WEP
     *
     * @param handle :WT_MAC_Encryption_Open 返回的handle
     * @param param:秘钥和SSID配置
     * @param PSK :PSK结果
     * @param PSKLen: PSK结果长度
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_EncryptDerivePSK(void *handle, PSK_KeyParam* param, unsigned char *PSK, unsigned int *PSKLen);

    /**
     * @brief TKIP、CCMP、GCMP加密时生成TK，不支持WEP
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param param: TK生成的输入参数
     * @param TK：TK结果
     * @param TKLen：TK结果长度
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_EncryptDeriveTK(void *handle, TK_KeyParam* param, unsigned char *TK, unsigned int *TKLen);

    /**
     * @brief WEP加密数据
     *
     * @param handle:handle:WT_MAC_Encryption_Open 返回的handle
     * @param pInData:待加密的明文数据(plaintext data)
     * @param len: 待加密的明文数据长度(plaintext data length)
     * @param iv:配置的IV
     * @param pOutData: 加密后的数据内存(IV + cipher data + ICV)
     * @param outLen: 加密后的数据长度(IV length + cipher data length + ICV length)
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_WEP_EncryptData(void *handle, unsigned char *pInData, unsigned int len, WEP_IV* iv, unsigned char **pOutData, unsigned int *outLen);

    /**
     * @brief WEP、TKIP、CCMP、GCMP加密时设置key。每种加解密协议的TK长度严格按照协议规则，否则报错
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param TK: 密码，只支持16进制，非ASCII码
     * @param TKLen: 密码长度。不同加解密协议有不同的长度，见define LEN_TK_xxx
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_EncryptSetKey(void *handle, unsigned char *TK, unsigned int TKLen);

    /**
     * @brief TKIP加密数据
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param pInData:待加密的明文数据(MAC header + plaintext data, not include FCS)
     * @param len: 待加密的明文数据长度(MAC header length + plaintext data length, not include FCS)
     * @param IV: 4bytes KeyID + 4bytes Extended IV
     * @param pOutData: 加密后的数据内存(MAC header + IV + cipher data + MIC + ICV)
     * @param outLen: 加密后的数据长度(MAC header length + IV length +  cipher data length + MIC length +  ICV length)
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_TKIP_EncryptData(void *handle, unsigned char *pInData, unsigned int len, TKIP_IV *IV, unsigned char **pOutData, unsigned int *outLen);

    /**
     * @brief CCMP加密数据
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param pInData:待加密的明文数据(MAC header + plaintext data, not include FCS)
     * @param len: 待加密的明文数据长度(MAC header length + plaintext data length, not include FCS)
     * @param header: CCMP 头信息
     * @param pOutData: 加密后的数据内存(MAC header + CCMP header + cipher data + MIC)
     * @param outLen: 加密后的数据长度(MAC header length + CCMP header length +  cipher data length +  MIC length)
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_CCMP_EncryptData(void *handle, unsigned char *pInData, unsigned int len, CCMP_Header *header, unsigned char **pOutData, unsigned int *outLen);

    /**
     * @brief GCMP加密数据
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param pInData:待加密的明文数据(MAC header + plaintext data, not include FCS)
     * @param len: 待加密的明文数据长度(MAC header length + plaintext data length, not include FCS)
     * @param header: CCMP 头信息
     * @param pOutData: 加密后的数据内存(MAC header + GCMP header + cipher data + MIC)
     * @param outLen: 加密后的数据长度(MAC header length + GCMP header length +  cipher data length +  MIC length)
     * @return WTTESTER_MAC_ENCRYPTION_API
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_GCMP_EncryptData(void *handle, unsigned char *pInData, unsigned int len, GCMP_Header *header, unsigned char **pOutData, unsigned int *outLen);

    /**
     * @brief WAPI加密数据
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param pInData:待加密的明文数据(MAC header + plaintext data, not include FCS)
     * @param len: 待加密的明文数据长度(MAC header length + plaintext data length, not include FCS)
     * @param header: WAPI 头信息
     * @param pOutData: 加密后的数据内存(MAC header + WAPI header + cipher data + MIC)
     * @param outLen: 加密后的数据长度(MAC header length + WAPI header length +  cipher data length +  MIC length)
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_WAPI_EncryptData(void *handle, unsigned char *pInData, unsigned int len, WAPI_Header *header, unsigned char **pOutData, unsigned int *outLen);

    /**
     * @brief 解密数据
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param pInData:待解密的明文数据
     * @param len: 待解密的明文数据长度
     * @param pOutData: 解密后的数据内存，完整帧数据(MAC header + plaintext data + FCS)
     * @param outLen: 解密后的数据长度，完整帧数据长度(MAC header length + plaintext data length + FCS length)
     * @param plaintext_pos: 解密后的数据在pOutData中的起始位置
     * @param plaintext_len: 解密后的数据长度(plaintext data length)
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_DecryptData(
        void *handle,
        unsigned char *pInData,
        unsigned int len,
        unsigned char **pOutData,
        unsigned int *outLen,
        unsigned int *plaintext_pos,
        unsigned int *plaintext_len
    );

    /**
     * @brief CCMP、GCMP加密/解密构建AAD nonce时A-MSDU Capable fields的值
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param value：0 or 1
     * @return void
     */
    WTTESTER_MAC_ENCRYPTION_API void WT_MAC_AAD_AMSDU_Capable(void *handle, int value);

    /**
     * @brief 获取解密数据过程中的一些描述详细信息
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param err_msg:解密过程中的错误信息，无错误返回"no error"
     * @param data:解密过程中的中间数据信息，重复下面的格式：title(32bytes) + data length(4bytes) + data(data length)
     * @param cnt:解密过程中的中间数据信息总长度
     * @return 0 = ok, other = fail
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_MAC_GetInProcessData(void *handle, char *err_msg, char **data, int *cnt);

    /**
     * @brief 获取解密后的数据或者未加密的frame data里面的MSDU位置
     *
     * @param pInData: MAC header + frame body + FCS
     * @param len:MAC header length + frame body length + FCS length
     * @param msdu_pos: MSDU start position start from pIndata
     * @param msdu_len: MSDU length
     * @param have_mesh: 1 = have mesh, 0 = no mesh
     * @return 成功返回0，否则失败
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_MAC_GetMSDUData(unsigned char *pInData, unsigned int len, unsigned int *msdu_pos, unsigned int *msdu_len, unsigned int *have_mesh = 0);

    /**
     * @brief 获取帧数据里面的AP地址和STA地址
     *
     * @param pInData: MAC header
     * @param len: MAC header length
     * @param sa: STA地址，6字节
     * @param aa: AP地址，6字节
     * @return 成功返回0，否则失败
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_MAC_GetAddr2TK(unsigned char *pInData, unsigned int len, unsigned char *sa, unsigned char *aa);

    /**
     * @brief WAPI加密时生成BK，从预共享密钥导出体系
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param presharekey: 预共享密钥
     * @param keylen: 预共享密钥长度
     * @param BK: 密码
     * @param BKLen: 密码长度
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_EncryptDerive_WAPIBK(void *handle, unsigned char* presharekey, unsigned int keylen, unsigned char *BK, unsigned int *BKLen);

    /**
     * @brief WAPI加密时生成TK
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param param: TK配置参数
     * @param TK: TK结果
     * @param TKLen: TK结果长度
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_EncryptDerive_WAPITK(void *handle, WAPI_TKParam *param, unsigned char *TK, unsigned int *TKLen);

    /**
     * @brief 配置WAPI加解密时额外的设置信息
     *
     * @param handle:WT_MAC_Encryption_Open 返回的handle
     * @param param：配置参数
     * @return 0 = ok，other = fail
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_WAPI_SetExtInfo(void *handle, WAPI_ExtInfo *param);

    /**
     * @brief 解析非A-MSDU的Ethernet II/IEEE802.3 结构
     *
     * @param pInData:MAC帧完整数据(MAC header + payload + FCS)
     * @param len: MAC帧完整数据长度
     * @param payload_pos: payload地址偏移，先对于pInData
     * @param payload_len: payload数据长度
     * @param result_type: 见LLC_TYPE_OR_LENGTH_ENUM
     * @param result_ethernet: Ethernet II 数据结果
     * @param result_8023: IEEE802.3数据结果
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_MAC_get_msdu_ethernet_data(
        unsigned char *pInData,
        unsigned int len,
        unsigned int *payload_pos,
        unsigned int *payload_len,
        unsigned int *result_type,
        EthernetDataType *result_ethernet,
        LLC_802_3_header *result_8023);

    /**
     * @brief 解析A-MSDU的Ethernet II结构
     *
     * @param pInData:MAC帧完整数据(MAC header + payload + FCS)
     * @param len: MAC帧完整数据长度
     * @param amsdu_data: A-MSDU data地址
     * @param amsdu_data_len：A-MSDU data长度
     * @param ethernet_payload_pos: ethernet payload地址偏移，先对于A-MSDU data
     * @param ethernet_payload_len: ethernet payload数据长度
     * @param result_type: 见LLC_TYPE_OR_LENGTH_ENUM
     * @param result_ethernet: Ethernet II 数据结果
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_MAC_get_amsdu_ethernet_data(
        unsigned char *pInData,
        unsigned int len,
        unsigned char *amsdu_data,
        unsigned int amsdu_data_len,
        unsigned int *ethernet_payload_pos,
        unsigned int *ethernet_payload_len,
        unsigned int *result_type,
        EthernetDataType *result_ethernet);

    /**
    * @brief CCMP、GCMP加密/解密时A-MSDU capablity值
    *
    * @param handle:WT_MAC_Encryption_Open 返回的handle
    * @param Capab: SPP A-MSDU Capab 值，默认等于0
    * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
    */
    WTTESTER_MAC_ENCRYPTION_API int WT_Set_SPP_AMSDU_Capab(void *handle, unsigned int Capab);

    /**
     * @brief 数据简单加密
     *
     * @param key : 秘钥
     * @param key_len ：秘钥长度
     * @param plain ：明文数据
     * @param plain_len ：明文数据长度
     * @return 成功返回 @ref WT_ENCRY_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
     */
    WTTESTER_MAC_ENCRYPTION_API int WT_IQ_Data_Encrypt(const unsigned char *key, unsigned int key_len, unsigned char *plain, unsigned int plain_len);

#ifdef __cplusplus
}
#endif


#endif
