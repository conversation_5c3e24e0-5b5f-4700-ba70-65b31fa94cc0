/*
 * vsa_11ba.h
 *
 *  Created on: 2022-5-12
 *      Author: Administrator
 */

#ifndef VSA11BA_H_
#define VSA11BA_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif
    //set
    scpi_result_t SetVsaAlzBaEmbeddedBSSID(scpi_t *context);

    //get
    scpi_result_t GetVsaRst_11Ba_WURDataSpectrumDataArb(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_WURDataSpectrumMaskArb(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_WURDataSpectrumOBW(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_WURDataSpectrumRBW(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_WURDataSpectrumSpan(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_SyncSymPowerRatio(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_DataSymPowerRatioMax(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_DataSymPowerRatioAvg(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_DataSymPowerRatioMin(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_CorrelationMetricMax(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_CorrelationMetricAvg(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_WURDataSpectrumMaskErrPercent(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_SymPowerRatioTestResult(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_CorrelationTestResult(scpi_t *context);
#ifdef __cplusplus
}
#endif

#endif /* VSA11BA_H_ */
