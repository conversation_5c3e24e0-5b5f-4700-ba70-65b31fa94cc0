#ifndef __DIG_ETHERNET_H__
#define __DIG_ETHERNET_H__

#include <unistd.h>
#include <string>
#include <sys/types.h>
#include <sys/time.h>
#include <memory>
#include <functional>

#include <mutex>
#include <future>
#include <atomic>

#include <vector>
#include <list>
#include <queue>
#include <set>
#include <map>

#include "conf.h"
#include "wtev++.h"
#include "ethtype.h"
#include "digstruct.h"
#include "devtype.h"
#include "../threadpool.h"

#define DIG_KEY(ThreadId, Code, ChanId) (((ThreadId & 0xFFFF) << 16) + ((Code & 0xFF) << 8) + (ChanId))
#define DIG_CMD_THREADID(ZoneId) (((ZoneId)) >> 16)
#define DIG_CMD_CODE(ZoneId) (((ZoneId) >> 8) & 0xFF)
#define DIG_CMD_CHANID(ZoneId) (((ZoneId)) & 0xFF)

using SendPnList = std::map<int, std::vector<PacketFormat>>;                   // Send
using ChannelPnList = std::map<int, std::list<std::unique_ptr<PacketFormat>>>; // Recv

// 接收结果
struct RecvResultType
{
    int Status;               // 状态
    uint32_t RecvPacketCnt;   // 实际接收数据包总个数
    struct timeval StartTime; // 开始收包时间
    struct timeval EndTime;   // 结束收包时间
    ChannelPnList PnMap;      // Pn数据
};

// Vsa Dig配置结构体类型
struct RecvZoneType
{
    double ISTimeout;              // 包间超时，接收首包数据后，未接收到后续数据，超时退出（认为接收完成）的时间，单位秒
    uint32_t SampleCnt;            // 单个通道采集点数
    int IsFirstPacket;             // 是否是第一包
    std::promise<int> FirstPacket; // 接收到首包
    std::promise<int> RecvFinish;  // 接收完成状态
    std::vector<int> ChannelList;  // 要接收的通道集合, ChanId
};

// 发送状态
struct SendResultType
{
    int IsFirstPacket;      // 是否是第一包
    struct timeval EndTime; // 结束时间
};

// Vsg Dig配置结构体类型
struct SendZoneType
{
    double FirstTimeout;          // 首包超时
    double ISTimeout;             // 包间超时
    std::vector<int> ChannelList; // 要接收的通道集合, ChanId
    SendPnList *PnData;           // 数据
};

// 数字IQ控制
class EthernetLib
{
public:
    //*****************************************************************************
    // 获取EthernetLib对象
    // 参数[IN] : 无
    // 返回值: EthernetLib对象指针
    //*****************************************************************************
    static EthernetLib &Instance(void);

    //*****************************************************************************
    // 检测网口状态，是否连接网线
    // 参数[IN] : 无
    // 返回值: 成功或错误码
    //*****************************************************************************
    int CheckEthStatus(int Wait = true);

    //*****************************************************************************
    // 设置治具运行模式
    // 参数[IN] : DigDutMode:治具模式参数
    // 返回值: 无
    //*****************************************************************************
    void SetDigTestMode(const DigDutModeType &DigDutMode);

    // 状态控制
    void Stop(int Type, const std::vector<int> &ChannelList, int &SetFlag);
    void GetSrcMac(uint8_t *MacArray);
    bool IsDutMode() { return m_DutModeParam.Enbale == DIG_DUT_MODE; }
    bool IsLightEth() { return m_EthType == ETH_TYPE_LIGHT; }

    // 发送
    int SendPacket(int Index, int Ch);
    void GetSendResult(const std::vector<int> &ChannelList, SendResultType &SendResult);
    int SetSendZone(const SendZoneType &Config, int &SetFlag);
    int ResetSendZone(const std::vector<int> &ChannelList);
    void ClearSendZone(const std::vector<int> &ChannelList, int &SetFlag);

    // 接收
    int SetRecvZone(RecvZoneType &Config, int &SetFlag);
    void ClearRecvZone(const std::vector<int> &ChannelList, int &SetFlag);
    int CaptureData(const std::vector<int> &ChannelList, RecvResultType &Result);
    int GetRecvPacketCnt(const std::vector<int> &ChannelList);

    std::unique_ptr<ThreadPool>& GetThreadPool(void)
    {
        return m_TaskPool;
    }
private:
    EthernetLib();
    ~EthernetLib();

    //*****************************************************************************
    // Socket套接字初始化
    // 参数[IN] : 无
    // 返回值: 成功或错误码
    //*****************************************************************************
    void SocketInit();

    //*****************************************************************************
    // 发送一包数据
    // 参数[IN] : DigData：数据指针，SendStatus：当前包所属通道的状态
    // 返回值: 无
    //*****************************************************************************
    void EthSend(PacketFormat *PnData, int &Status);

    //*****************************************************************************
    // 分配一包数据
    // 参数[IN] : PacketBuf：数据包地址
    // 返回值: 无
    //*****************************************************************************
    bool AssignPacket(std::unique_ptr<PacketFormat> &&PacketBuf);

    //*****************************************************************************
    // 接收一包数据
    // 参数[IN] : PacketBuf：数据包地址
    // 返回值: 无
    //*****************************************************************************
    int RecvOnePacket(PacketFormat *PacketBuf);

    //*****************************************************************************
    // 通过网口接收PN数据，新线程执行，空闲时等待
    // 参数[IN] : 无
    // 返回值: 成功或错误码
    //*****************************************************************************
    void RecvDataThread();

    //*****************************************************************************
    // 通过网口接收PN数据，新线程执行，空闲时等待
    // 参数[IN] : 无
    // 返回值: 成功或错误码
    //*****************************************************************************
    void RecvOptThread();

    //*****************************************************************************
    // 新线程执行，执行发送动作，空闲时等待
    // 参数[IN] : 无
    // 返回值: 无
    //*****************************************************************************
    void SendDataThread();

private:
    enum
    {
        ETH_TYPE_ElEC,  // 网口硬件
        ETH_TYPE_LIGHT, // 光口硬件
    };

    struct SendPnData
    {
        int IsFirstPacket;         // 是否是第一包
        std::vector<int> ChanList; // 发送通道集合, ChanId
        SendPnList *PnData;        // 数据
        int Status;                // 发送操作状态
        uint32_t SendPacketCnt;    // 实际发送数据包总个数
        double FirstTimeout;       // 首包超时
        double ISTimeout;          // 包间超时
        struct timeval EndTime;    // 结束时间
    };

    struct SendOptType
    {
        std::mutex ThreadMutex;         // 发送线程锁，仅用于等待
        std::condition_variable ConVar; // 发送条件变量
        std::mutex OptMutex;            // 发送操作锁，取得锁才允许修改接收行为
        std::timed_mutex PacketMutex;   // 发包锁，用于多用户发送数据包排队
        std::atomic<int> IsHasTrig;     // 当前是否拥有TRIG
        std::atomic<int> TriggerCnt;    // 当前拥有的TRIG数，测试仪模式，每拥有一个TRIG允许发送一包
        int TriggerMulti = 1;           // 一个触发信号发送多少个数据包
        unsigned int TriggerTotal;      // 总计接收的TRIG数
        unsigned int TriggerLastTotal;  // 上一次读取的总计接收的TRIG数
        struct timeval LastTriggerTime; // 上一次持有TRIG的时间
        int Status;
    };

    struct RecvPnData
    {
        ChannelPnList ChanPnMap;       // Pn数据,<ChanId, ChanPnData>
        uint32_t NeedPacketCnt;        // 单个通道需采集包数
        uint32_t RecvPacketCnt;        // 实际接收数据包总个数
        int StartRecv;                 // 收到首CHID的Packet后，才开始接收其他CHID的Packet
        std::vector<int> ChanList;     // 要接收的通道集合, ChanId
        int Status;                    // 接收操作状态
        std::mutex Mutex;              // 小区操作锁
        struct timeval StartTime;      // 收到第一包数据的时间
        struct timeval EndTime;        // 接收所有数据的时间
        std::promise<int> FirstPacket; // 接收到首包
        std::promise<int> RecvFinish;  // 接收完成状态
    };

    struct RecvOptType
    {
        std::mutex ThreadMutex;          // 接收线程锁，仅用于等待
        std::condition_variable ConVar;  // 接收条件变量
        std::mutex OptMutex;             // 接收操作锁，取得锁才允许修改接收行为
        volatile int Status;             // 收包结果
        volatile int TriggerCnt = 0;     // 已发送的TRIG数，治具模式下，每接收一包前，发送一个TIRG
        int TriggerTotal;                // 总计发送的TRIG数，用于调试
        int ClearTrigger;                // 统计因没有收到数据包重复发送的TRIG数，用于调试
        int RecvPacketCnt;               // 本次启动收到的包数，用于调试
        int TriggerMulti = 1;            // 一个触发信号发送多少个数据包
        volatile int RecvSuccess = true; // 接收成功
        double ISTimeout;                // 包间超时,多个小区必须配置一样的包间超时
        struct timeval LastRecvTime;     // 上一次接收数据包的时间
        struct timeval LastTrigTime;     // 上一次发送Trig的时间
    };

    // 公共成员
    int m_Running;                 // 是否正在运行,用于析构时线程退出
    uint8_t m_SrcMac[6];           // 发送数据源MAC地址
    int m_EthType = ETH_TYPE_ElEC; // 网口类型，电口/光口
    int m_LightDutSendDelay = 1;   // 光口发包延时
    int m_SockRawFd = -1;          // 网口设备描述符
    std::string m_Eth;             // 网卡名称
    DigDutModeType m_DutModeParam; // 治具模式发送TRIGGER
    int m_RecvTrigDebug = 0;
    std::unique_ptr<ThreadPool> m_TaskPool;    // 线程池

    // 发送
    std::map<int, std::shared_ptr<SendPnData>> m_SendZone; // 正在接收的小区空间, <ChanId, ZonePnData>
    SendOptType m_SendOpt;                                 // 发送操作状态

    // 接收
    std::map<int, std::shared_ptr<RecvPnData>> m_RecvPnBuf; // 正在接收的小区集合, <ChanId, ZonePnData>
    RecvOptType m_RecvOpt;                                  // 接收操作状态
};
#endif //__DIG_ETHERNET_H__