//*****************************************************************************
//File: ioctlcmd.h
//Describe:硬件操作控制命令
//Author：wangzhenglong
//Date: 2016.8.4
//*****************************************************************************

#ifndef _IOCTLCMD_H_
#define _IOCTLCMD_H_

#include "rf.h"
#include "defines.h"
#include "devtype.h"
#include "backplane.h"

/***********************************
cmd命令格式：
数据长度字段(Byte/K Byte)+预留+业务命令字段
(        高23位      )（ 1位 ）(   低8位  )
***********************************/
#define IOCTL_CMD(ioctlcmd,DataLength)      (((DataLength) << 9) + (ioctlcmd))
#define IOCTL_CMD_FUNC(cmd)                 ((cmd) & 0xFF)
#define IOCTL_CMD_DATALENGTH(cmd)           ((cmd) >> 9)

#define TYPE_STRING(type)           (type == DEV_TYPE_VSA ? "vsa" : "vsg")

#define MRC_ADC_VFS         (1250.0)        //ADC的电压最大最绝对值，单位为mV
#define MRC_ADC_BITS        16              //ADC位数，单位为Bit
#define MRC_ADC_RESISTOR    (50000.0)       //ADC电阻，单位为mΩ
#define VOLT_CHANNEL_COUNT   8              //AD7091电压通道数量

#define CHECK_DITECT_REG_COUNT  200          //单次寄存器测试次数
#define PN_ITEM_MAX (1000)
#define SEGMENT_ITEM_MAX (2000)
#define HW_PARAM_SIZE (4 * 1024)
#define TRIG_PARAM_SIZE (32)

//启动类型
enum WT_START_MODE_E
{
    WT_START_MODE_NORMAL,                   //Normal
    WT_START_MODE_ATT_CAL,                  //ATT校准
};

//单元板电压通道地址定义
enum WT_UB_VOLT_CHANNEL_E
{
    UB_VOLT_CHANNEL_00,
    UB_VOLT_CHANNEL_01,
    UB_VOLT_CHANNEL_02,
    UB_VOLT_CHANNEL_03,
    UB_VOLT_CHANNEL_04,
    UB_VOLT_CHANNEL_05,
    UB_VOLT_CHANNEL_06,
    UB_VOLT_CHANNEL_07,

    UB_VOLT_CHANNEL_10,
    UB_VOLT_CHANNEL_11,
    UB_VOLT_CHANNEL_12,
    UB_VOLT_CHANNEL_13,
    UB_VOLT_CHANNEL_14,
    UB_VOLT_CHANNEL_15,
    UB_VOLT_CHANNEL_16,
    UB_VOLT_CHANNEL_17,

    UB_VOLT_CHANNEL_20,
    UB_VOLT_CHANNEL_21,
    UB_VOLT_CHANNEL_22,
    UB_VOLT_CHANNEL_23,
    UB_VOLT_CHANNEL_24,
    UB_VOLT_CHANNEL_25,
    UB_VOLT_CHANNEL_26,
    UB_VOLT_CHANNEL_27
};

//电平状态
enum WT_VOLTAGE_STATUS
{
    LOW,
    HIGH
};

//寄存器结构体类型
struct RegType
{
    unsigned int  Addr;         //寄存器地址
    int  Data;                  //寄存器数据
};

//64bit寄存器结构体类型
struct Reg64Type
{
    int Addr;
    long long Data;
};

//片选器件结构体类型
struct DevValueType
{
    unsigned int  DevId;        //器件编号
    int  Data;                  //器件数据
};

//多器件参数结构体类型
struct DeviceConfig
{
    int DeviceId;                //器件ID
    struct RegType RegTypeData;  //寄存器参数
};

//位控制结构体类型
struct BitSetting
{
    int Index;                   //IO编号
    int Status;                  //IO状态
};

//多字节数据传输结构体类型
struct DataBufType
{
    void *pDataBuf;   //数据缓冲区地址
    unsigned int Len; //数据长度
    unsigned int pFlashAddr;  //Flash地址
};

// 开关板RF设置结构体类型
struct SwitchRFPortSetType
{
    int SwitchId;
    int SubPort;                    // 输出Port
    int SBConfigType;            // 开关板配置类型(WT_SB_CONFIG_TYPE_E) / * 328用，保留先 */
    int Mode;                    // SISO或8080, 见WT_PORT_STATE_MODE_E
    int State;                   // 端口的状态见WT_PORT_STATE_E
};

// 开关板RF设置结构体类型
struct SwitchPortSetType
{
    int Type;                    // 单元类型, vsa,vsg
    int SwitchId;
    int SubPort;
    int Mode;                    // SISO或8080, 见WT_PORT_STATE_MODE_E
    int State;                   // 状态
};

struct SwitchIfgCtrlMode
{
    int Port;
    int Status;
};

// 开关板移位寄存器
struct ShiftRegData
{
    int SwitchId;   // 开关板ID, A, B
    int RegId;      // 移位寄存器ID
    int RegValue;   // 寄存器的值
};

//单元板器件状态结构体类型
struct DeviceStatus
{
    int DeviceId;
    int Status;
};

//波段设置结构体类型
struct FreqBandType
{
    int LoModBand;    // Mod调制频率
    int LoMixBand;    // MIX混频中频频率
    enum WT_RF_MOD_BAND_E BandMod;
    enum WT_RF_MIX_BAND_E BandMIX;
};

//基带环回类型
enum WT_BB_LOOP_E
{
    LOOP_TO_RF,
    LOOP_TO_BB
};

//RX直流偏移结构体类型
struct RXDCOffsetType
{
    int Icode;
    int Qcode;
};

//衰减器code值结构体类型
struct ATTCodeType
{
    int AttId;
    int Code;
};

//开关数据配置
struct SwitchCfgType
{
    int Type;
    int Size;
    char *Addr;
    int SpecSwitchFlag;
};

//开关板VSG IFG数据配置
struct SwitchVsgIfgType
{
    unsigned int Status;
    unsigned int Solt;
    unsigned int Port;
};

//VSG IFG数据配置
struct VsgIfgCtrlType
{
    unsigned int Status;
    unsigned int Param1;
    unsigned int Param2;
    unsigned int Param3;
};

//开关板数据配置类型
enum WT_SWITCH_CFG_TYPE
{
    SWITCH_MAP,
    SWITCH_INIT,
};

//加密芯片SN码信息结构体类型
struct CryptoAT88WRType
{
    int Cmd;
    int Addr;
    int Len;
    int Data[8];
};

//加密芯片SN码信息结构体类型
struct CryptoMemSNInfoType
{
    char InnerSN[32];  //内部SN码
    char DevType[32];  //设备类型
};

//加密芯片初始化信息结构体类型
struct  CryptoMemInfoType
{
    char InnerSN[32];  //内部SN码
    char Code[32];     //Code码
    char DevType[32];  //设备类型
};

//Flash位置选择定义
enum WT_ROM_CHIP_ID_E
{
    SWITCH_1_FLASH,
    SWITCH_2_FLASH,
};

//多字节数据传输结构体类型
struct  RomDataInfoType
{
    u32 BaseAddr;       //FLASH基地址
    u32 *pData;         //数据缓冲区地址
    enum WT_ROM_CHIP_ID_E ChipID;        //=0:基带板或者背板，=1:射频板, =2:开关板
};

//单元板FPGA信息
struct UnitFPGAInfo
{
    int FPGAVersion;                        //单元板FPGA逻辑版本号
    int FPGADate;                           //单元板FPGA编译日期(年月日)
    int FPGATime;                           //单元板FPGA编译时间(时分秒)
    int BoardInfo;                          //单元板FPGA信息
};

//单元板FPGA信息
struct IqImbConfigType
{
    int PhaseA;
    int PhaseB;
    int AmpComp;
    int TimeSkew[24];
    int TimeSkewNum;
};

struct LdpcParamType
{
    char *InData;
    char *OutData;
    int InDataLen;
    int OutDataLen;
    int Mode;
    int LLRLen;
    int Iteration;
};

struct BccParamType
{
    char *InData;
    char *OutData;
    int InDataLen;
    int OutDataLen;
};

struct Seq_Seg_ParamType
{
    int SegmentPort;
    int SegmentIndex;
};

struct Seq_Seg_TimeParamType
{
    int DurationCnt;    // VSA 的Segment总长度,对于VSG而言，该参数乘以DurationLoopCnt*Duration+DurationExtendCnt 为实际长度（点数
    int MeaoffsetCnt;   // VSA 的Segment偏移长度
    int MeadurationCnt; // VSA 的Segment有效数据长度
};

struct VSGParamBitType
{
    unsigned int TxShift0 : 32;
    unsigned int TxShift1 : 32;
    unsigned int SwShift00 : 32;
    unsigned int SwShift01 : 8;
    unsigned int SwShift00Mask : 32;
    unsigned int SwShift01Mask : 8;
    unsigned int SwShift1 : 32;
    unsigned int SwShift1Mask : 32;
    unsigned int SwShift2 : 32;
    unsigned int SwShift2Mask : 32;
    unsigned int SwShift3 : 32;
    unsigned int SwShift3Mask : 32;
    unsigned int reserved1 : 16;
    unsigned int reserved2 : 32;
};

//Stop Mode
enum WT_STOP_MODE_E
{
    STOP_MODE_NORMAL,   //正常停止
    STOP_MODE_RESET,    //仅复位FPGA
};

//应用层与驱动层的命令枚举类定义
enum IOCTL_CMD_E
{
    IOCTL_CMD_BEGIN,                    //仅作开始标示用

    /*-------------加密芯片控制接口，-----------*/
    //需与外部程序配置，尽量不要修改CRYPTO_MEM的IOCTL_CMD，否则外部烧录程序也需修改。
    CRYPTO_MEM_INIT,                    //加密芯片初始化
    CRYPTO_MEM_GET_INFO,                //获取加密芯片信息
    CRYPTO_MEM_GET_SN,                  //获取加密芯片SN码
    CRYPTO_MEM_GET_CODE,                //获取加密芯片特征码

    /*-----单元板(背板/VSA、VSG业务板)公共接口----*/
    //单元板硬件信息
    GET_UNIT_BOARD_HW_VERSION,          //获取单元板硬件版本
    GET_UNIT_BOARD_FPGA_INFO,           //获取单元板板FPGA信息
    GET_UNIT_BOARD_REVISION_ID,         //获取单元板修订版本

    //寄存器读写
    WRITE_DIRECT_REG,                   //直接写寄存器数据
    READ_DIRECT_REG,                    //直接读寄存器数据
    CHECK_DIRECT_REG,                   //直接检查寄存器数据

    WRITE_DIRECT_MULTI_REG,             //直接写寄存器组数据
    READ_DIRECT_MULTI_REG,              //直接读寄存器组数据
    CHECK_DIRECT_MULTI_REG,             //直接检查寄存器组数据
    //寄存器读写延时
    SET_REG_WR_DELAY,                   //配置PCI读写延时
    //FLASH
    WRITE_ROM,                          //写单元板Flash(背板/基带板/射频板)
    READ_ROM,                           //读单元板Flash(背板/基带板/射频板)
    //温度、电压
    READ_UB_VOLT_CHANNEL_VALUE,         //获取单元板FPGA电压数据
    READ_UNIT_BOARD_TEMPERATURE,        //读取单元板FPGA温度数据
    /*--------业务单元板(VSA/VSG)通用接口--------*/
    GET_BB_BOARD_INFO,                  //获取基带板FPGA信息
    GET_BUSI_BOARD_SLOT,                //获取业务板槽位号
    GET_COMPLETE_CLR_STATUS,            //获取业务单元模块当前工作状态与完成状态并清零
    SET_DEVICE_WORK_MODE,               //设置业务板当前工作模式（SISO/MIMO）
    CLEAR_WORK_MODE,                    //清除驱动里缓存的工作模式
    WRITE_BB_AD5611,                    //基带板锁相环
    READ_BB_AD5611,                     //基带板锁相环
    GET_BB_AD7682_CHANNEL,              //获取电压值
    GET_SW_AD7689_CHANNEL,              //获取电压值
    WRITE_BB_ADF4106,                   //ADF4106
    READ_BB_ADF4106,                    //ADF4106
    WRITE_BB_HM7044,                    //写HMC7044基带板时钟分频芯片
    READ_BB_HM7044,                     //读HMC7044基带板时钟分频芯片
    WRITE_BB_LTC5594,                   //LTC5594
    READ_BB_LTC5594,                    //LTC5594
    WRITE_BB_LMX2594,                   //LMX2594
    READ_BB_LMX2594,                    //LMX2594
    WRITE_BB_LMX2820,                   //LMX2820
    READ_BB_LMX2820,                    //LMX2820
    WRITE_BB_HMC833,                    //HMC833
    READ_BB_HMC833,                     //HMC833
    WRITE_BB_FLASH,                     //FLASH
    READ_BB_FLASH,                      //FLASH
    WRITE_BB_AD7091,                    //AD7091
    READ_BB_AD7091,                     //AD7091
    GET_BB_VOLTAGE,                     //获取电压
    WRITE_LO_DDS,                       //DDS Reg
    READ_LO_DDS,                        //DDS Reg
    SET_LO_DDS_FREQ_CHANNEL,            //DDS Freq
    SET_LO_DDS_CURRENT,                 //DDS Current
    WRITE_RF_ATT_SHIFT,                 //ATT SHIFT
    READ_RF_ATT_SHIFT,                  //ATT SHIFT
    WRITE_BB_LO_SHIFT,                  //LO SHIFT
    READ_BB_LO_SHIFT,                   //LO SHIFT
    SET_LO_SHIFT_HMC705,                //LO SHIFT HMC705
    SET_LO_SHIFT_LOOP_FILTER,           //LO SHIFT LOOP_FILTER
    SET_LO_SHIFT_LOOP_FILTER_INIT,      //LO SHIFT LOOP_FILTER
    SET_LO_SHIFT_FREQ_CHANNEL,          //LO SHIFT LOOP_FILTER
    WRITE_RF_ADC_DAC,                   //ADC Or Dac
    READ_RF_ADC_DAC,                    //ADC Or Dac
    SET_IQ_SWITCH,                      //设置IQ交换
    SET_RX_PORT,                        //设置RX的RF端口
    SET_TX_PORT,                        //设置TX的RF端口
    SET_RX_BAND,                        //设置RX的RF端口
    SET_TX_BAND,                        //设置TX的RF端口
    WRITE_ATT,                          //写ATT code值
    READ_ATT,                           //读取ATT code值
    SET_RF_PA,                          //设置RF PA状态
    SET_TB_MODE,
    GET_XDMA_STATUS,                    //查询XMDA完成状态
    WRITE_BASE_FPGA_EARSE,              //基带板升级FLASH擦除
    WRITE_BASE_FPGA_UPGRADE,            //基带板FPGA升级
    WRITE_BASE_FPGA_WRITEONEPAGE,       //基带板FPGA写一页Flash
    FPGA_RELOAD,                        //FPGA重加载
    SET_FLASH_4BYTE_MODE,               //配置Flash 4Byte模式
    SET_ATT_CAL_CONFIG,                 //配置ATT CAL模式
    Set_LO_COM_Mode,                    //配置共本振模式
    Get_LO_COM_Mode,                    //获取共本振模式
    SET_ANALOGIQ_SWITCH,                //设置模拟IQ信号内/外链路切换开关
    GET_ANALOGIQ_SWITCH,                //读取模拟IQ信号内/外链路切换开关
    SET_MIX_LO_SWITCH,                  //设置MIX LO链路
    INIT_BUSI_CRYPTO_AT88,              //上电初始化加密芯片
    WRITE_BUSI_CRYPTO_AT88,             //烧录加密芯片
    READ_BUSI_CRYPTO_AT88,              //读取加密芯片
    READ_HW_VERSION,                    //硬件版本号获取
    SET_LIST_MODE,                      //设置LIST模式
    INIT_VIRTUAL_ADDR,                  //初始化寄存器列表为当前器件值
    SET_VIRTUAL_ADDR_RECORD,            //设置虚拟地址记录listMode的配置。
    SET_VIRTUAL_ADDR_CONFIG,            //设置虚拟地址下发listMode的配置。
    SET_SEQUENCE_SEGMENT_TIME,          //设置Sequence:Sgement的时间参数
    SET_SEQUENCE_TRIG_COM_PARAM,        //设置trig公共参数
    SET_SEQUENCE_TRIG_LOOP_PARAM,       //设置非Listmode大循环次数
    SET_SWITCH_MODE,                    //设置开关板的配置状态，防止开关链路配置冲突，用于listmode

    //*******************VSG业务命令***************************
    VSG_START,                          //VSG开启命令
    VSG_STOP,                           //VSG停止命令
    VSG_GET_STATUS,                     //VSG状态获取命令
    VSG_SET_PN_LOOP_IFG,                //设置PN大循环IFG
    VSG_SET_PN_HEAD,                    //设置PN头部
    VSG_GET_PN_HEAD,                    //获取PN头部
    VSG_SET_IFG_STATUS,                 //IFG控制使能
    SET_EXT_MODE,                       //设置Fem模式参数
    VSG_START_TBT_MIMO,                 //TBT_STA MIMO VSG开启命令
    VSG_SET_TBT_STA_PARAM,              //设置VSG TBT_STA参数
    VSG_CLEAR_TBT_STA_MODE,             //清除TBT STA模式
    VSG_GET_PARAM_DMA_BUF,              //获取配置硬件参数的DmaBuf
    VSG_RESET_DMA_FIFO,                 //清除DMA的FIFO通道数据
    VSG_SET_LIST_CELL_MOD,              //设置vsg list 模式
    //*******************VSA业务命令***************************
    VSA_START,                          //VSA开启命令
    VSA_STOP,                           //VSA停止命令
    VSA_GET_STATUS,                     //VSA状态获取命令
    GET_TRIG_ADDR_START,                //获取前置时间后的trigger数据起点
    WRITE_ADC_REG,                      //写ADC寄存器
    READ_ADC_REG,                       //读ADC寄存器
    SET_RX_TRIG_LEVEL_DIGITAL,          //设置RX数字触发的触发电平
    SET_AGC_ENABLE,                     //设置AGC使能
    SET_AGC_GATE_VALUE,                 //设置AGC门限
    SET_ADC_POWERDOWN,                  //设置ADC低功耗模式
    SET_TBT_AP_MODE,                    //设置TBT_AP模式参数
    VSA_SET_TBT_STA_PARAM,              //设置VSG TBT_STA参数
    SET_RF_LNA,                         //设置VSA LNA模式
    SET_IQ_IMB_COMP,                    //设置VSA IQ不平衡补偿
    GET_ATT_CAL_RESULT,                 //获取ATT校准结果
    GET_FPGA_CAPTURE_POWER,             //获取FPGA计算的功率
    VSA_GET_PARAM_DMA_BUF,              //获取配置硬件参数的DmaBuf
    SET_CW_FLAG,                        //设置CW标志，在为true时，不需要提前下发描述符
    GET_COMPLETE_CNT,
    GET_SEQ_COMPLETE_STAT,              //获取seq完成状态
    GET_DUPLEX_VSG_RUN_CONFIG,          //双工VSA采集数据时VSG的状态配置值

    //*******************背板业务命令***************************
    GET_BACK_FPGA_INFO,                 //获取背板FPGA信息
    GET_DEV_CLK_STATE,                  //仪器时钟状态
    WRITE_OCXO_CODE,                    //写晶振code值
    READ_OCXO_CODE,                     //读取晶振code值
    WRITE_SWITCH_AD9228,                //写开关板AD9228寄存器
    READ_SWITCH_AD9228,                 //读开关板AD9228寄存器
    GET_SWITCH_PORT_POWER,              //读开关板端口功率
    GET_SWITCH_INNER_POWER,             //读开关板内部功率
    WRITE_HM7043_CODE,                  //写时钟HM7043寄存器
    READ_HM7043_CODE,                   //读时钟HM7043寄存器
    READ_RF_VERSION,                    //读取射频板版本号
    INIT_BACK_CRYPTO_AT88,              //上电初始化加密芯片
    WRITE_BACK_CRYPTO_AT88,             //烧录加密芯片
    READ_BACK_CRYPTO_AT88,              //读取加密芯片
    WRITE_BACK_VOLTAGE_REG,             //写单元板AD7091电压检测器件
    READ_BACK_VOLTAGE_REG,              //读单元板AD7091电压检测器件
    READ_BACK_CHANNEL_VALUE,            //获取单元板AD7091电压数据
    SET_FAN_REG,                        //风扇速度设置命令
    GET_FAN_REG,                        //风扇速度获取命令
    SET_SWB_ATT_CODE,                   //设置背板ATT CODE
    GET_SWB_ATT_CODE,                   //获取背板ATT CODE
    WRITE_LED_IO_EXT_REG,               //写LED IO扩展
    READ_LED_IO_EXT_REG,                //读LED IO扩展
    WRITE_LED_IO_EXT_BIT,               //以Index写LED IO扩展
    READ_LED_IO_EXT_BIT,                //以Index读LED IO扩展
    WRITE_SWITCH_PA,                    //设置开关板PA
    READ_SWITCH_PA,                     //获取开关板PA状态
    WRITE_SWITCH_42553,                 //设置开关板42553
    READ_SWITCH_42553,                  //获取开关板42553状态
    WRITE_REF_PLL_ADF4002,              //设置时钟板ADF4002
    READ_REF_PLL_ADF4002,               //读取时钟板ADF4002

    SET_SWITCH_BOARD_CFG,               //设置开关板的开关配置
    SET_SWITCH_STATE,                   //开关设置PI/PA状态等
    SET_SWITCH_VSG_CTL3,                //开关设置VSG CTL3状态等
    SWITCH_SET_VSG_IFG_STATUS,          //IFG控制打开时的端口选择
    GET_SWITCH_VALUE_BAK,               //获取缓存的开关板真值
    SET_SWITCH_SHIFT_REG,               //开关板移位寄存器
    GET_SWITCH_SHIFT_REG,               //开关板移位寄存器
    GET_DEV_CLK_SEL,                    //获取仪器时钟选择(外部\内部)
    SWITCH_SET_VSG_IFG_CTRL_MODE,   //设置VSG CTL3状态是PI/PA以控制IFG

    //*******************其他业务命令************************
    IOCTL_CMD_END                       //仅作结束标示用，该CMD枚举值最大为2^8=256
};
#endif