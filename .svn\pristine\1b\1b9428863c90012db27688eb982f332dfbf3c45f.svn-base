#include "wlan_ccmp.h"
#include "aes.h"
#include "wpa_debug.h"
#include "wpa_common.h"

void wlan_ccmp::ccmp_aad_nonce(HEADER_802_11 *hdr, const u8 *data, u8 *aad, size_t *aad_len, u8 *nonce)
{
    u16 fc, stype, seq;
    int qos = 0, addr4 = 0;
    u8 *pos;

    nonce[0] = 0;

    memcpy(&fc, &hdr->FC, sizeof(fc));
    stype = WLAN_FC_GET_STYPE(fc);
    if ((fc & (WLAN_FC_TODS | WLAN_FC_FROMDS)) ==
        (WLAN_FC_TODS | WLAN_FC_FROMDS))
    {
        addr4 = 1;
    }

    if (WLAN_FC_GET_TYPE(fc) == WLAN_FC_TYPE_DATA)
    {
        fc &= ~0x0070; /* Mask subtype bits */
        if (stype & 0x08)
        {
            const u8 *qc;
            qos = 1;
            fc &= ~WLAN_FC_HTC;
            qc = (const u8 *)(hdr + 1);
            if (addr4)
            {
                qc += ETH_ALEN;
            }
            nonce[0] = qc[0] & 0x0f;
        }
    }
    else if (WLAN_FC_GET_TYPE(fc) == WLAN_FC_TYPE_MGMT)
    {
        nonce[0] |= 0x10; /* Management */
    }

    fc &= ~(WLAN_FC_RETRY | WLAN_FC_PWRMGT | WLAN_FC_MOREDATA);
    fc |= WLAN_FC_ISWEP;
    WPA_PUT_LE16(aad, fc);
    pos = aad + 2;
    memcpy(pos, hdr->Addr1, 3 * ETH_ALEN);
    pos += 3 * ETH_ALEN;
    seq = (hdr->Sequence << 4) | hdr->Frag;/* Sequence Control field 2 bytes */
    seq &= ~0xfff0; /* Mask Seq#; do not modify Frag# */
    WPA_PUT_LE16(pos, seq);
    pos += 2;
    /* A4 and QC */
    memcpy(pos, hdr + 1, addr4 * ETH_ALEN + qos * 2);
    pos += addr4 * ETH_ALEN;
    if (qos)
    {
        /**
         * The QC TID is used in the construction of the AAD.
         * When in a non-DMG BSS and both the STA and its peer have their
         * SPP A-MSDU Capable fields equal to 1, bit 7 (the A-MSDU Present field) is used in the
         * construction of the AAD. The remaining QC fields are masked to 0 for the AAD calculation
         * (bits 4 to 6, bits 8 to 15, and bit 7 when either the STA or its peer has the SPP A-MSDU
         * Capable field equal to 0).
         * TID = BIT(QOS, 0~3)
         */
        if (0 == m_amsdu_capab) /* either device has SPP A-MSDU Capab = 0, only TID */
        {
            pos[0] &= ~0x70; /* bits 4~6 masked to 0 */
            pos[0] &= ~0x80; /* bit 7 masked to 0 */
        }
        else /* all device has SPP A-MSDU Capab = 1, TID + bit7 */
        {
            pos[0] &= 0x8F; /* use bit7 */
        }
        pos++;
        *pos++ = 0x00; /* bit 8~15 */
    }

    *aad_len = pos - aad;

    memcpy(nonce + 1, hdr->Addr2, ETH_ALEN);
    nonce[7] = data[7]; /* PN5 */
    nonce[8] = data[6]; /* PN4 */
    nonce[9] = data[5]; /* PN3 */
    nonce[10] = data[4]; /* PN2 */
    nonce[11] = data[1]; /* PN1 */
    nonce[12] = data[0]; /* PN0 */
}

u8* wlan_ccmp::ccmp_decrypt(const u8 *tk, HEADER_802_11 *hdr, const u8 *data, size_t data_len, size_t *decrypted_len)
{
    u8 aad[LEN_CCMP_AAD_MAX] = { 0 };
    u8 nonce[LEN_CCMP_NONCE] = { 0 };
    size_t aad_len = 0;
    size_t mlen = 0;
    u8* plain = nullptr;

    if (data_len < LEN_CCMP_HDR + LEN_CCMP_128_MIC)
    {
        wep_printf(MSG_ERROR, "Drop RX frame data length too short(%d) < CCMP header length + MIC length", data_len);
        return nullptr;
    }

    m_decrypt_buf.reset(new u8[data_len + AES_BLOCK_SIZE]);
    plain = m_decrypt_buf.get();
    if (plain == nullptr)
    {
        wep_printf(MSG_ERROR, "System memory allocation fail");
        return nullptr;
    }
    wep_hexdump(MSG_INFO, "CCMP Header", data, LEN_CCMP_HDR);

    mlen = data_len - LEN_CCMP_HDR - LEN_CCMP_128_MIC;

    memset(aad, 0, sizeof(aad));
    ccmp_aad_nonce(hdr, data, aad, &aad_len, nonce);
    wep_hexdump(MSG_INFO, "CCMP-128 AAD", aad, aad_len);
    wep_hexdump(MSG_INFO, "CCMP-128 nonce", nonce, LEN_CCMP_NONCE);

    if (aes_ccm_ad(tk, LEN_CCMP_128_KEY, nonce, LEN_CCMP_128_MIC, data + LEN_CCMP_HDR, mlen, aad, aad_len, data + LEN_CCMP_HDR + mlen, plain) < 0)
    {
        s8 tmpbuf_1[128] = { 0 };
        wpa_snprintf_hex(tmpbuf_1, sizeof(tmpbuf_1) - 1, data + LEN_CCMP_HDR + mlen, LEN_CCMP_128_MIC);
        wep_printf(MSG_ERROR, "Invalid CCMP-128 MIC:0x%s", tmpbuf_1);
        return nullptr;
    }
    wep_hexdump(MSG_INFO, "CCMP-128 MIC", data + LEN_CCMP_HDR + mlen, LEN_CCMP_128_MIC);

    *decrypted_len = mlen;
    return plain;
}

u8* wlan_ccmp::ccmp_encrypt(const u8 *tk, u8 *frame, size_t len, size_t hdrlen, CCMP_Header* header, size_t *encrypted_len)
{
    u8 aad[LEN_CCMP_AAD_MAX] = { 0 };
    u8 nonce[LEN_CCMP_NONCE] = { 0 };
    size_t aad_len = 0;
    size_t plen = 0;
    u8 *crypt = nullptr;
    u8 *pos = nullptr;
    u16 *fc = nullptr;
    HEADER_802_11 *hdr = nullptr;

    if (len < hdrlen || hdrlen < LENGTH_802_11)
    {
        return nullptr;
    }
    plen = len - hdrlen;

    m_crypt_buf.reset(new u8[hdrlen + LEN_CCMP_HDR + plen + LEN_CCMP_128_MIC + AES_BLOCK_SIZE]);
    crypt = m_crypt_buf.get();
    if (crypt == nullptr)
    {
        return nullptr;
    }

    memcpy(crypt, frame, hdrlen);
    hdr = (HEADER_802_11 *)crypt;
    fc = (u16*)&hdr->FC;
    *fc |= WLAN_FC_ISWEP; /* Protected Frame */

    pos = crypt + hdrlen;
    *pos++ = header->PN0; /* PN0 */
    *pos++ = header->PN1; /* PN1 */
    *pos++ = header->Rsvd; /* Rsvd */
    *pos++ = header->CONTROL.Byte; /* KeyID,ExtIV,Rsvd */
    *pos++ = header->PN2; /* PN2 */
    *pos++ = header->PN3; /* PN3 */
    *pos++ = header->PN4; /* PN4 */
    *pos++ = header->PN5; /* PN5 */

    memset(aad, 0, sizeof(aad));
    ccmp_aad_nonce(hdr, crypt + hdrlen, aad, &aad_len, nonce);
    wpa_hexdump(MSG_INFO, "CCMP-128 AAD", aad, aad_len);
    wpa_hexdump(MSG_INFO, "CCMP-128 nonce", nonce, sizeof(nonce));

    if (aes_ccm_ae(tk, LEN_CCMP_128_KEY, nonce, LEN_CCMP_128_MIC, frame + hdrlen, plen, aad, aad_len, pos, pos + plen) < 0)
    {
        return nullptr;
    }
    wpa_hexdump(MSG_INFO, "CCMP-128 MIC", pos + plen, LEN_CCMP_128_MIC);

    wpa_hexdump(MSG_INFO, "CCMP-128 encrypted", crypt + hdrlen + LEN_CCMP_HDR, plen);

    *encrypted_len = hdrlen + LEN_CCMP_HDR + plen + LEN_CCMP_128_MIC;
    wpa_hexdump(MSG_INFO, "Cipher data(MAC header + CCMP header + cipher data + MIC)", crypt, *encrypted_len);
    return crypt;
}

u8* wlan_ccmp::ccmp_256_decrypt(const u8 *tk, HEADER_802_11 *hdr, const u8 *data, size_t data_len, size_t *decrypted_len)
{
    u8 aad[LEN_CCMP_AAD_MAX] = { 0 };
    u8 nonce[LEN_CCMP_NONCE] = { 0 };
    size_t aad_len = 0;
    size_t mlen = 0;
    u8 *plain = nullptr;

    if (data_len < LEN_CCMP_HDR + LEN_CCMP_256_MIC)
    {
        wep_printf(MSG_ERROR, "Drop RX frame data length too short(%d) < CCMP header length + MIC length", data_len);
        return nullptr;
    }
    m_decrypt_buf.reset(new u8[data_len + AES_BLOCK_SIZE]);
    plain = m_decrypt_buf.get();
    if (plain == nullptr)
    {
        wep_printf(MSG_ERROR, "System memory allocation fail");
        return nullptr;
    }
    wep_hexdump(MSG_INFO, "CCMP Header", data, LEN_CCMP_HDR);

    mlen = data_len - LEN_CCMP_HDR - LEN_CCMP_256_MIC;

    memset(aad, 0, sizeof(aad));
    ccmp_aad_nonce(hdr, data, aad, &aad_len, nonce);
    wep_hexdump(MSG_INFO, "CCMP-256 AAD", aad, aad_len);
    wep_hexdump(MSG_INFO, "CCMP-256 nonce", nonce, LEN_CCMP_NONCE);

    if (aes_ccm_ad(tk, LEN_CCMP_256_KEY, nonce, LEN_CCMP_256_MIC, data + LEN_CCMP_HDR, mlen, aad, aad_len, data + LEN_CCMP_HDR + mlen, plain) < 0)
    {
        s8 tmpbuf_1[128] = { 0 };
        wpa_snprintf_hex(tmpbuf_1, sizeof(tmpbuf_1) - 1, data + LEN_CCMP_HDR + mlen, LEN_CCMP_256_MIC);
        wep_printf(MSG_ERROR, "Invalid CCMP-256 MIC:0x%s", tmpbuf_1);
        return nullptr;
    }
    wep_hexdump(MSG_INFO, "CCMP-256 MIC", data + LEN_CCMP_HDR + mlen, LEN_CCMP_256_MIC);

    *decrypted_len = mlen;
    return plain;
}

u8* wlan_ccmp::ccmp_256_encrypt(const u8 *tk, u8 *frame, size_t len, size_t hdrlen, CCMP_Header* header, size_t *encrypted_len)
{
    u8 aad[LEN_CCMP_AAD_MAX] = { 0 };
    u8 nonce[LEN_CCMP_NONCE] = { 0 };
    size_t aad_len = 0;
    size_t plen = 0;
    u8 *crypt = nullptr;
    u8 *pos = nullptr;
    HEADER_802_11 *hdr = nullptr;
    u16 *fc = nullptr;
    if (len < hdrlen || hdrlen < LENGTH_802_11)
    {
        return nullptr;
    }

    plen = len - hdrlen;

    m_crypt_buf.reset(new u8[hdrlen + LEN_CCMP_HDR + plen + LEN_CCMP_256_MIC + AES_BLOCK_SIZE]);
    crypt = m_crypt_buf.get();
    if (crypt == nullptr)
    {
        return nullptr;
    }

    memcpy(crypt, frame, hdrlen);
    hdr = (HEADER_802_11 *)crypt;
    fc = (u16*)&hdr->FC;
    *fc |= WLAN_FC_ISWEP; /* Protected Frame */
    pos = crypt + hdrlen;

    *pos++ = header->PN0; /* PN0 */
    *pos++ = header->PN1; /* PN1 */
    *pos++ = header->Rsvd; /* Rsvd */
    *pos++ = header->CONTROL.Byte; /* KeyID,ExtIV,Rsvd */
    *pos++ = header->PN2; /* PN2 */
    *pos++ = header->PN3; /* PN3 */
    *pos++ = header->PN4; /* PN4 */
    *pos++ = header->PN5; /* PN5 */

    memset(aad, 0, sizeof(aad));
    ccmp_aad_nonce(hdr, crypt + hdrlen, aad, &aad_len, nonce);
    wpa_hexdump(MSG_INFO, "CCMP-256 AAD", aad, aad_len);
    wpa_hexdump(MSG_INFO, "CCMP-256 nonce", nonce, LEN_CCMP_NONCE);

    if (aes_ccm_ae(tk, LEN_CCMP_256_KEY, nonce, LEN_CCMP_256_MIC, frame + hdrlen, plen, aad, aad_len, pos, pos + plen) < 0)
    {
        return nullptr;
    }
    wpa_hexdump(MSG_INFO, "CCMP-256 MIC", pos + plen, LEN_CCMP_256_MIC);

    wpa_hexdump(MSG_INFO, "CCMP-256 encrypted", crypt + hdrlen + LEN_CCMP_HDR, plen);

    *encrypted_len = hdrlen + LEN_CCMP_HDR + plen + LEN_CCMP_256_MIC;

    wpa_hexdump(MSG_INFO, "Cipher data(MAC header + IV + cipher data + MIC + ICV)", crypt, *encrypted_len);
    return crypt;
}

wlan_ccmp::wlan_ccmp()
{
}

wlan_ccmp::~wlan_ccmp()
{
}

s32 wlan_ccmp::decrypt_frame_data(u8 * pInData, u32 len, u8 ** pOutData, u32 * outLen, u32 * plaintext_pos, u32 * data_len)
{
    s32 iRet = WT_ENCRY_ERR_CODE_OK;
    u8 *decrypted_ptr = nullptr;
    size_t decrypted_len = 0;
    u8 *tk = get_cipher()->Key;
    u32 tk_len = get_cipher()->KeyLen;
    u32 cipherType = get_cipher_type();
    do
    {
        iRet = parsing_frame(pInData, len);
        /* if frame data not encrypted, copy it to outdata*/
        if (WT_ENCRY_ERR_CODE_NOT_ENCRYPT == iRet)
        {
            copy_not_encrypted_data(pInData, len, pOutData, outLen, plaintext_pos, data_len, decrypted_ptr, decrypted_len);
            break;
        }

        if (iRet)
        {
            break;
        }

        if (WPA_CIPHER_CCMP_128 == cipherType)
        {
            decrypted_ptr = ccmp_decrypt(tk, m_hdrInfo.hdr, m_hdrInfo.data, m_hdrInfo.len, &decrypted_len);
        }
        else
        {
            decrypted_ptr = ccmp_256_decrypt(tk, m_hdrInfo.hdr, m_hdrInfo.data, m_hdrInfo.len, &decrypted_len);
        }

        if (!decrypted_ptr)
        {
            iRet = WT_ENCRY_ERR_CODE_Fail;
            break;
        }

        reassemble_frame(pInData, len, pOutData, outLen, plaintext_pos, data_len, decrypted_ptr, decrypted_len);

    } while (0);

    return iRet;
}

s32 wlan_ccmp::encrypt_data(u8 * pInData, u32 len, CCMP_Header* header, u8 ** pOutData, u32 * outLen)
{
    s32 iRet = WT_ENCRY_ERR_CODE_OK;
    u8* encr = nullptr;
    size_t encrypted_len = 0;
    u8 *tk = get_cipher()->Key;
    u32 tk_len = get_cipher()->KeyLen;
    size_t hdrlen = get_hdrlen(pInData, len);
    u32 cipherType = get_cipher_type();

    do
    {
        if (hdrlen < LENGTH_802_11)
        {
            iRet = WT_ENCRY_ERR_CODE_Data_Too_Short;
            break;
        }

        if (WPA_CIPHER_CCMP_128 == cipherType)
        {
            encr = ccmp_encrypt(tk, pInData, len, hdrlen, header, &encrypted_len);
        }
        else
        {
            encr = ccmp_256_encrypt(tk, pInData, len, hdrlen, header, &encrypted_len);
        }

        if (!encr)
        {
            iRet = WT_ENCRY_ERR_CODE_Fail;
            break;
        }
        copy_encrypted_data(encr, encrypted_len, pOutData, outLen);

    } while (0);

    return iRet;
}
