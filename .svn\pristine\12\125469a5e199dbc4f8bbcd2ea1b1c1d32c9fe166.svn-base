//*****************************************************************************
//  File: monitor.h
//  监视连接处理
//  Data: 2016.9.22
//*****************************************************************************
#ifndef __WT_MON_H__
#define __WT_MON_H__

#include <list>
#include <mutex>
#include <memory>
#include <string>
#include <vector>
#include "wtev++.h"
#include "socket.h"
#include "business.h"
#include "connector.h"

enum WT_MONITOR_TYPE   
{
    MON_VSA_STATUS,             //监视VSA状态，包括配置的参数
    MON_VSA_RESULT,             //VSA结果数据

    MON_VSG_STATUS,             //VSG状态，包括配置参数
    MON_VSG_PN_STATUS,          //PN配置参数
    MON_VSA_ANALYZE_STATUS,     //VSA分析参数
    MON_METER_SETTING,          //meter的控制配置参数
};

class Monitor;

class MonConnector : public WTConnector
{
public:
    using WTConnector::WTConnector;

    void SetMon(Monitor *Mon) { m_Monitor = Mon; }

    Monitor *GetMon() { return m_Monitor; }

private:
    Monitor *m_Monitor;
};

class Monitor
{
public:
    Monitor(const wtev::loop_ref &Loop, int fd);
    ~Monitor();

    //*****************************************************************************
    // 设置监视的对象
    // 参数[IN]: Obj : 监视对象
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetMonObj(int Obj);

    //*****************************************************************************
    // 设置监视机需要监视的数据
    // 参数[IN]: Action: 0-删除监视项 1: 添加监视项
    //           Type : 监视类型，取值见WT_MONITOR_TYPE
    //       VsaResult: 监视机需要获取的VSA结果列表，监视类型为VSA结果时有效
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetMonData(int Action, int Type, const std::vector<std::string> &VsaResult);

    //*****************************************************************************
    // 发送VSA配置参数到监视机
    // 参数[IN]: Param : 配置参数
    //             Len : 参数长度
    // 返回值: 无
    //*****************************************************************************
    void SendVsaParam(const void *Param, int Len);

    //*****************************************************************************
    // 发送VSG配置参数到监视机
    // 参数[IN]: Param : 配置参数
    //             Len : 参数长度
    // 返回值: 无
    //*****************************************************************************
    void SendVsgParam(const void *Param, int Len);

    //*****************************************************************************
    // 发送VSA的分析参数到监视机
    // 参数[IN]: Param : 分析参数
    //             Len : 参数长度
    // 返回值: 无
    //*****************************************************************************
    void SendVsaAlzParam(const void *Param, int Len);

    //*****************************************************************************
    // 发送VSG的PN配置信息到监视机
    // 参数[IN]: PnHeadInfo : PN头数据
    // 参数[IN]: PnInfo     : PN项数据
    // 返回值: 无
    //*****************************************************************************
    void SendPnParam(const std::vector<PnItemHead> &PnHeadInfo, const std::vector<ExtPnItem> &PnInfo);

    //*****************************************************************************
    // 发送VSA结果数据到监视机
    // 参数[IN]: Result : vsa结果
    // 返回值: 无
    //*****************************************************************************
    void SendVsaResult(const std::vector<MonVsaResult> &Result);

    //*****************************************************************************
    // 是否监视指定的RF端口
    // 参数[IN]: PortId : RF端口
    // 返回值: true or false
    //*****************************************************************************
    bool IsMonPort(int PortId);

    //*****************************************************************************
    // 获取监视机所需要的VSA结果列表
    // 参数: 无
    // 返回值: 结果列表
    //*****************************************************************************
    std::list<std::string> GetVsaResult(void);

    // socket收到数据后的回掉函数，处理上位机下发的监视命令
    int ExecMonCmd(void);

    //*****************************************************************************
    // 是否监视机的ip信息
    // 参数[OUT]: Info：IP信息
    // 返回值: 成功或者错误码
    //*****************************************************************************
    int GetMonInfo(std::string &Info);

    //*****************************************************************************
    // 转发meter界面控制参数到监视机
    // 参数[IN]: SettingInfo : 配置信息
    // 参数[IN]: Len : 长度
    // 返回值: 无
    //*****************************************************************************
    void SendMeterInfo(const void *SettingInfo, int Len);

private:
    std::mutex m_Mutex;
    std::mutex m_ReciveMutex;
    std::shared_ptr<MonConnector> m_Conn; //外部连接
    std::unique_ptr<Business> m_Bsn;

    int m_RFPort = 0;    //监视的RF端口号
    std::vector<int> m_Types;    //监视的数据类型
    std::list<std::string> m_VsaResult;  //监视机需要的VSA结果
};

// 监视连接管理类
class MonitorMgr
{
public:
    static MonitorMgr &Instance()
    {
        static MonitorMgr Mgr;
        return Mgr;
    }

    //*****************************************************************************
    // 获取监视机列表
    // 参数[OUT]: Mons : 监视机列表
    // 返回值: 监视机列表
    //*****************************************************************************
    void GetMonitors(std::list<std::shared_ptr<Monitor>> &Mons);

    //*****************************************************************************
    // 添加新的监视机连接
    // 参数[IN]: Loop : ev loop
    //            fd : 连接fd
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AddMonitor(const wtev::loop_ref &Loop, int fd);

    //*****************************************************************************
    // 获取监视机信息列表
    // 参数[OUT]: Mons : 监视机信息列表
    // 返回值: 监视机列表
    //*****************************************************************************
    int GetAllMonitorsInfo(std::vector<std::string> &MonsInfo);

    //*****************************************************************************
    // 管理连接时断开所有的监视连接~
    // 返回值: 操作结果
    //*****************************************************************************
    int DisConnectAllMonitor();

    // delete copy and move constructors and assign operators
    MonitorMgr(MonitorMgr const&) = delete;             // Copy construct
    MonitorMgr(MonitorMgr&&) = delete;                  // Move construct
    MonitorMgr& operator=(MonitorMgr const&) = delete;  // Copy assign
    MonitorMgr& operator=(MonitorMgr &&) = delete;      // Move assign

private:
    MonitorMgr() {}
    ~MonitorMgr() {}

    //socket收到数据后的回掉函数，处理上位机下发的监视命令
    void MonCmdCb(wtev::io &watcher, int revents);

    //*****************************************************************************
    // 删除监视机
    // 参数[IN]: Mon : 需要删除的monitor
    // 返回值: 无
    //*****************************************************************************
    void DelMonitor(Monitor *Mon);

private:
    std::mutex m_Mutex;
    std::list<std::shared_ptr<Monitor>> m_Monitors;
    std::list<std::unique_ptr<wtev::io>> m_Watchers;
};

#endif
