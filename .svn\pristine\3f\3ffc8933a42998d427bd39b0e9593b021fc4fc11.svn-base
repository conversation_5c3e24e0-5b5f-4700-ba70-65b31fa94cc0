#include "scpi_3gpp_alz_NR5G.h"
#include <iostream>
#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_base.h"

static int IsAlzNR5G(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (attr->vsaAlzParam.analyzeParam3GPP.Standard != ALG_3GPP_STD_5G)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

//**************************************************************************************************
// UL
//**************************************************************************************************
scpi_result_t SCPI_NR5G_UL_SetAlzRFPhaseCompensation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.RfPhaseCompen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzDuplexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = ALG_3GPP_FDD;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Duplexing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzSlotPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 10;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 5 && Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.SlotPeriod = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzSlotNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.SlotPeriod == 5)
        {
            if (Value < 1 || Value > 4)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        else if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.SlotPeriod == 10)
        {
            if (Value < 1 || Value > 9)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        else
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.ULSlotnumber = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzSpecialSlotIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 55)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.SpecialSlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.CellNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // 0:OFF; 1:ON
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellFrequency(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Frequency = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_UL_SetAlzCellBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 100 * MHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (5 * MHz_API) && Value != (10 * MHz_API) && Value != (15 * MHz_API) && Value != (20 * MHz_API) && Value != (25 * MHz_API) && Value != (30 * MHz_API) &&
            Value != (40 * MHz_API) && Value != (50 * MHz_API) && Value != (60 * MHz_API) && Value != (70 * MHz_API) && Value != (80 * MHz_API) && Value != (90 * MHz_API) && Value != (100 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].ChannelBW = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellPhysicalID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].PhyCellID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellDmrsTypeAPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 2 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].DmrsTypeAPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 15 * KHz_API;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[TxBWID].SCSpacing = Value;

        // 该值固定，只读，强制赋值
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[0].SCSpacing = 15 * KHz_API;
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[1].SCSpacing = 30 * KHz_API;
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[2].SCSpacing = 60 * KHz_API;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (Value == 1) 
        {
            // Reset
            attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[0].State = 0;
            attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[1].State = 0;
            attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[2].State = 0;
        }
        
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[TxBWID].State = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWMaxRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0 && Value != -999) || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[TxBWID].MaxRBNumb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[TxBWID].Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWK0U(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].TxBW[TxBWID].k0u = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 30 * KHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.CyclicPrefix = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 273;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 0 || Value > 273)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpTransformPrecoder(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.TransformPrecoder = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpResourceAllocationType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.ResourceAllocation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpFreqHoppingMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.FreqHopMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.Dmrs.ConfigType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSMaxLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.Dmrs.MaxLength = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSAddPosInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.Dmrs.AdditionalPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSScramblingId(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value[2] = {0};
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < 2; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < 0 || Value[i] > 65535)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.TransformPrecoder != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.Dmrs.ScramblingID0 = Value[0];
        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.Dmrs.ScramblingID1 = Value[1];
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSNpuschID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.TransformPrecoder != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.NPuschID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzChannelType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.ChanType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschCellIdex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschMappingType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].MappingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].SymNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].SymbOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBDetectMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].RBDetMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (Value < 1 || Value > attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[CellID].Bwp.RBNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschLayerNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].LayerNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschAntennaNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].AntennaNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschCDMGroupsWoData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].CDMGrpWOData = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsAntennaPort(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;
    int i = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsAlzNR5G(attr);
    if (iRet != WT_ERR_CODE_OK)
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            return SCPI_ResultOK(context, iRet);
        }

        int LayerNum = attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].LayerNum;
        if (LayerNum < 1 || LayerNum > 4)
        {
            return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
        }

        int Value[LayerNum];
        for (i = 0; i < LayerNum; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < 0 || Value[i] > 11)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        IF_BREAK(iRet);

        memset(attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].DmrsAntPort, 0, sizeof(attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].DmrsAntPort));
        memcpy(attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].DmrsAntPort, Value, sizeof(int) * LayerNum);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSybolLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].DmrsSymbLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].DmrsInitType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].DmrsID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenNscid(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].NSCID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschGroupOrSequenceHopping(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].GrporSeqHopType= Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].Modulate= Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschChannelCodeingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].ChanCodingState= Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].Scramble= Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschMCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].ChanCodingState != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        switch (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].Modulate)
        {
            case 1: // π/2-BPSK
                if (Value < 0 || Value > 1)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            case 2: // QPSK
                if (Value < 0 || Value > 9)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            case 4: // 16QAM
                if (Value < 10 || Value > 16)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            case 6: // 64QAM
                if (Value < 17 || Value > 28)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            case 8: // 256QAM
                if (Value < 20 || Value > 27)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            default:
                if (Value < 0 || Value > 28)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
        }
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].MCS= Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].UeID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetAlzPuschRVIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Pusch[CellID].RvIdx= Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// DL
//**************************************************************************************************
scpi_result_t SCPI_NR5G_DL_SetAlzDuplexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = ALG_3GPP_FDD;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Duplexing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzSlotPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 10;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 5 && Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.SlotPeriod = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzSlotNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (attr->vsaAlzParam.analyzeParam3GPP.NR.DL.SlotPeriod == 5)
        {
            if (Value < 1 || Value > 4)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        else if (attr->vsaAlzParam.analyzeParam3GPP.NR.DL.SlotPeriod == 10)
        {
            if (Value < 1 || Value > 9)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        else
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.DLSlotNumber = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzSpecialSlotIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 55)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);
        
        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.SpecialSlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzRFPhaseCompensation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.RfPhaseCompen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.CellNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // 0:OFF; 1:ON
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].State = Value;

        // Cell[0].State强制为ON
        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[0].State = 1;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellFrequency(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Frequency = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzCellBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 100 * MHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (5 * MHz_API) && Value != (10 * MHz_API) && Value != (15 * MHz_API) && Value != (20 * MHz_API) && Value != (25 * MHz_API) && Value != (30 * MHz_API) &&
            Value != (40 * MHz_API) && Value != (50 * MHz_API) && Value != (60 * MHz_API) && Value != (70 * MHz_API) && Value != (80 * MHz_API) && Value != (90 * MHz_API) && Value != (100 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].ChannelBW = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellPhysicalID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].PhyCellID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellDmrsTypeAPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 2 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].DmrsTypeAPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 15 * KHz_API;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[TxBWID].SCSpacing = Value;

        // 该值固定，只读，强制赋值
        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[0].SCSpacing = 15 * KHz_API;
        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[1].SCSpacing = 30 * KHz_API;
        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[2].SCSpacing = 60 * KHz_API;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        if (Value == 1) 
        {
            // Reset
            attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[0].State = 0;
            attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[1].State = 0;
            attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[2].State = 0;
        }
        
        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[TxBWID].State = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWMaxRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0 && Value != -999) || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[TxBWID].MaxRBNumb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[TxBWID].Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWK0U(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].TxBW[TxBWID].k0u = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 30 * KHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.CyclicPrefix = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 273;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 0 || Value > 273)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschVrbToPrbInterLeaver(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.VrbToPrbInterleaver = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMcsTable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.McsTab = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschResourceAlloc(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.ResourceAllocation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschRBGSizeType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.RBGSizeType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschConfigType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.ConfigType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMaxLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.MaxLength = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschAdditionalPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Pdsch.AdditionalPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.SymbNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymbolOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.SymbOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetFDRes(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.FDResUseBitmap = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*if (Value < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }*/

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetBitMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int CellID = 0;

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int bufLen = sizeof(attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.BitMap) + 1;
        char buf[bufLen];
        size_t copyLen = 0;
        memset(buf, 0, bufLen);

        if (!SCPI_ParamCopyText(context, buf, bufLen, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < copyLen; i++)
        {
            buf[i] -= '0';
            if (buf[i] < 0 || buf[i] > 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        IF_BREAK(iRet);

        for (int i = copyLen - 1; i >= 0; i--)
        {
            attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.BitMap[i] = buf[copyLen - 1 - i];
        }
        //memcpy(attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Cell[CellID].Bwp.Coreset.BitMap, buf, copyLen);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzChannelType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.ChanType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotCellIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000 && Value != 30000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.SCOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchCase(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.PbchCase = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 4 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.Length = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        int CellID = 0;
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int bufLen = sizeof(attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.Position) + 1;
        char buf[bufLen];
        size_t copyLen = 0;
        memset(buf, 0, bufLen);

        if (!SCPI_ParamCopyText(context, buf, bufLen, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < copyLen; i++)
        {
            buf[i] -= '0';
            if (buf[i] < 0 || buf[i] > 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        IF_BREAK(iRet);

        for (int i = copyLen - 1; i >= 0; i--)
        {
            attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.Position[i] = buf[copyLen - 1 - i];
        }
        //memcpy(attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.Position, buf, copyLen);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchBurstSetPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 5 && Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.BurstSetPeriod = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchHalfFrameIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pbch.HalfFrmIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdcchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdcch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMapType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.MappingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.SymbNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.SymbOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBDetMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        // attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.RBDetMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
            
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsAlzNR5G(attr);
    if (iRet != WT_ERR_CODE_OK)
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }
    
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsAlzNR5G(attr);
    if (iRet != WT_ERR_CODE_OK)
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschBitMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsAlzNR5G(attr);
    if (iRet != WT_ERR_CODE_OK)
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int bufLen = sizeof(attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.Bitmap) + 1;
        char buf[bufLen];
        size_t copyLen = 0;

        if (!SCPI_ParamCopyText(context, buf, bufLen, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < copyLen; i++)
        {
            buf[i] -= '0';
            if (buf[i] < 0 || buf[i] > 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        IF_BREAK(iRet);

        for (int i = copyLen - 1; i >= 0; i--)
        {
            attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.Bitmap[i] = buf[copyLen - 1 - i];
        }
        //memcpy(attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.Bitmap, buf, copyLen);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschLayerNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.LayerNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.AntennaNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCDMGroupsWoData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.CDMGrpWOData = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsSymbLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.DmrsSymbLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsAntPort(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Number[2] = {0, 0};
    int CellID = 0;
    int AntPort = 0;
    int Value = 0;

    do
    {
        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        AntPort = Number[1];
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            return SCPI_ResultOK(context, iRet);
        }
        
        if (AntPort < 0 || AntPort >= ALG_5G_MAX_ANTENNA)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            return SCPI_ResultOK(context, iRet);
        }


        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1000 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.DmrsAntPort[AntPort] = Value;
        
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsInitType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.DmrsInitType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.DmrsID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschNSCID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.NSCID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCodewords(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.Codewords = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[2] = {0, 0};
        int CellID = 0;
        int Modulate = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        Modulate = Number[1];
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.Modulate[Modulate] = Value;
    
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.ChanCodingState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.Scrambling = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUsePdschScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.UsePdschScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDataScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1023)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.DataScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUeID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.UeID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int Idx = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        Idx = Number[1];
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 28)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.MCS[Idx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRvIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int Idx = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        Idx = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.DL.Channel[CellID].Pdsch.RvIdx[Idx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// Measure
//**************************************************************************************************
scpi_result_t SCPI_NR5G_Measure_SetAlzSubFrameIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.MeasSubfrmIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_Measure_SetAlzSlotIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 39)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.MeasSlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_Measure_SetAlzDmrsConsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.DmrsConsState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_Measure_SetAlzEVMSubcarrier(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.EvmSubcarrierState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_Measure_SetAlzSymbolIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.EvmSymbIndx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_Measure_SetAlzWindowPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzNR5G(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.EvmSymbPosType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}