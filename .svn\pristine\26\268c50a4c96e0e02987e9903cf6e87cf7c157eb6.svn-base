//*****************************************************************************
//  File: socket.h
//  提供常见的socket操作接口
//  Data: 2016.7.12
//*****************************************************************************
#ifndef __WT_SOCKET_H__
#define __WT_SOCKET_H__

#include <memory>
#include <string>

// 连接类型枚举定义
enum WT_LINK_TYPE
{
    MGR_LINK,          // 管理连接
    SRV_EXCLUDE_LINK,  // server独占连接
    SRV_NORMAL_LINK,   // server普通连接
    SRV_SUB_LINK,      // server子连接
    MON_LINK,          // 监视连接
    DIAGNOSIS_LINK,    // server诊断连接（查询日志log，cmd操作等）
};

enum WT_LINK_CMD
{
    LINK_CMD_ERR,        // 不存在的命令
    LINK_CMD_NEW,        // 新的连接
    LINK_CMD_CLOSE,      // 关闭指定连接
    LINK_CMD_CLOSE_ALL   // 断开进程的所有连接
};

// WT-Link与其他进程通信所用消息结构体
struct LinkMsg
{
    LinkMsg(int Cmd, int Type, int SID)
        : Cmd(Cmd), Type(Type), SID(SID)
    {
    }

    LinkMsg(void)
        : Cmd(LINK_CMD_ERR)
    {
    }

    int Cmd;           // 命令字，见WT_LINK_CMD
    int Type;          // 连接类型，见WT_LINK_TYPE
    int SID;           // 连接序列号
};

// buffer信息
struct BufInfo
{
    void *Data;
    int Len;
};

// 封装的用于非阻塞socket读写类，提供数据收发接口
class WRSocket
{
public:
    //*****************************************************************************
    // 构造函数
    // 参数[IN]: Fd : 要操作的FD
    //       BufSize: 接收buf的长度
    // 返回值: 无
    //*****************************************************************************
    WRSocket(int Fd, int BufSize = 2048);

    // 不应该调用这个接口，它时不安全的
    WRSocket(const WRSocket& sock);

    //*****************************************************************************
    // 连接指定的TCP socket服务器
    // 参数[IN]: IP : 服务器IP地址
    //         Port : 服务器端口
    // 返回值: socket fd
    //*****************************************************************************
    static int ConnectAddr(const char *IP, int Port);

    //*****************************************************************************
    // 检查socket是否可写，以此来检查socket是否创建成功
    // 参数[IN]: Fd : 待设置的fd
    //          timeout_ms : 超时时间
    // 返回值: 成功或者错误码
    //*****************************************************************************
    static int IOWriteWait(int Fd, int timeout_ms);

    //*****************************************************************************
    // 设置文件属性为NONBLOCK
    // 参数[IN]: Fd : 待设置的fd
    // 返回值: 成功或者错误码
    //*****************************************************************************
    static int SetNonblock(int Fd, int on = 1);

    //*****************************************************************************
    // 设置socket的keepalive参数
    // 参数[IN]: Fd : 待设置的fd
    //         Enable: 是否开启keepalive功能
    //       KeepIdle: 空闲多长时间后开始发送检测包，单位秒
    //     KeepIntval: 没有收到响应时间隔多长时间再发送一次，单位秒
    //        KeepCnt: 连续多少次没有收到响应认为连接已断开
    //        Timeout: 等待ACK超时时间，单位秒
    // 返回值: 成功或者错误码
    //*****************************************************************************
    static int SetKeepAlive(int Fd, bool Enable, int KeepIdle, int KeepIntval, int KeepCnt, unsigned int Timeout);

    //*****************************************************************************
    // 通过unix socket发送文件描述符到另一端，发送描述符的同时哈能附带发送少量数据。
    // 参数[IN]: Fd     : 待发送的描述符，小于0表示此FD无需发送
    //           Buf    : 需要附带发送的buf，附带buf只能是很少的数据。
    //           BufLen : 附带Buf的长度
    // 返回值: 成功或者错误码，返回错误码表示连接异常需要关闭
    //*****************************************************************************
    int SendFdMsg(int Fd, const void *Buf, int BufLen);

    //*****************************************************************************
    // 通过unix socket接收文件描述符，同时接收少量的附带数据
    // 参数[IN]: Buf    : 保存附带数据使用的buf，附带buf只能是很少的数据。
    //           BufLen : Buf的长度
    // 参数[OUT]: Fd     : 接收到的文件描述符，小于0表示没有描述符
    // 返回值: 成功或者错误码，返回错误码表示连接异常需要关闭
    //*****************************************************************************
    int RecvFdMsg(int &Fd, void *Buf, int BufLen);

    //*****************************************************************************
    // 接受数据，接收到的数据保存在内部buf中
    // 参数[IN]: Restart : 是否从头开始接收数据
    //                     true ：接收的数据从接收buf开头处保存
    //                     false: 接收的数据放在上一次接收位置的后面
    // 参数[OUT]: Len : 接收Buf的数据总长度
    // 返回值: 成功或错误码，返回错误码表示连接异常需要关闭
    //*****************************************************************************
    int Recv(int &Len, bool Restart = true);

    //*****************************************************************************
    // 接收数据到指定Buf
    // 参数[IN]: Buf : 存放数据的buf
    //         RxLen : 最大接收长度
    // 参数[OUT]: Len : 实际接收的数据长度
    // 返回值: 成功或错误码，返回错误码表示连接异常需要关闭
    //*****************************************************************************
    int Recv(void *Buf, int RxLen, int &Len);

    //*****************************************************************************
    // 接收数据到指定Buf
    // 参数[IN]: Buf : 存放数据的buf
    //           RxLen : 最大接收长度
    //           TimeoutUs: 超时时间微秒
    // 参数[OUT]: Len : 实际接收的数据长度
    // 返回值: WT_OK, WT_SOCKET_CLOSED, TIMEOUT
    //*****************************************************************************
    int Recv(void *Buf, int RxLen, int &Len, long TimeoutUs);

    //*****************************************************************************
    // 发送数据
    // 参数[IN]: Data : 待发送的数据
    //           Len : 数据总长度
    // 参数[OUT]: TxLen : 实际发送的长度
    // 返回值: 成功或错误码，返回错误码表示连接异常需要关闭
    //*****************************************************************************
    int Send(const void *Data, int Len, int &TxLen);

    //*****************************************************************************
    // 发送多块数据
    // 参数[IN]: Vec : 数据信息数组
    //           Cnt : 数组成员个数
    // 参数[OUT]: TxLen : 实际发送的长度
    // 返回值: 成功或错误码，返回错误码表示连接异常需要关闭
    //*****************************************************************************
    int Send(const BufInfo *Vec, int Cnt, int &TxLen);

    //*****************************************************************************
    // 扩展buffer的大小，并将原有buffer的数据复制到新的buffer中
    // 参数[IN]: Size : Buffer大小
    // 返回值: 成功或错误码
    //*****************************************************************************
    int ExpandBuf(int Size);

    //*****************************************************************************
    // 清除buffer的使用记录，与Recv接口Restart = true参数效果相同
    // 参数 : 无
    // 返回值: 无
    //*****************************************************************************
    void ResetBuf()
    {
        m_BufPos = 0;
    }

    //*****************************************************************************
    // 清除命令使用部分的buff
    // 参数 : CmdLen, 命令长度
    // 返回值: 无
    //*****************************************************************************
    void ResetCmdBuf(int CmdLen);

    //*****************************************************************************
    // 重新设置使用的fd
    // 参数[IN] : fd : 新的socket fd
    // 返回值: 无
    //*****************************************************************************
    void ResetFd(int fd);

    //*****************************************************************************
    // 获取接收数据的buf
    // 参数 : 无
    // 返回值: buf
    //*****************************************************************************
    char *GetRxBuf(void) const
    {
        return m_RecvBuf.get();
    }

    //*****************************************************************************
    // 获取接收buffer大小
    // 参数 : 无
    // 返回值: buf大小值
    //*****************************************************************************
    int GetBufSize(void) const
    {
        return m_BufSize;
    }

    //*****************************************************************************
    // 获取当前buffer中可用数据大小
    // 参数 : 无
    // 返回值: 字节数
    //*****************************************************************************
    int GetBufUsedSize(void) const
    {
        return m_BufPos;
    }

    //*****************************************************************************
    // 获取socket fd
    // 参数 : 无
    // 返回值: fd
    //*****************************************************************************
    int GetFd(void) const
    {
        return m_Fd;
    }

    //*****************************************************************************
    // 获取Fd peer Ip
    // 参数 : 无
    // 返回值: Ip
    //*****************************************************************************
    const char* GetPeerIp(void) const
    {
        return m_Ip;
    }

    //*****************************************************************************
    // 获取Fd peer Port
    // 参数 : 无
    // 返回值: Port
    //*****************************************************************************
    short GetPeerPort(void) const
    {
        return m_Port;
    }

    const char *GetLastErrorStr(void)
    {
        return m_LastErrorStr.c_str();
    }

    bool operator==(const WRSocket &Sock)
    {
        return (m_Fd == Sock.m_Fd);
    }

protected:
    int Recv(char *Buf, int Len);
    int Send(char *Buf, int Len);

private:
    int m_Fd;
    int m_BufSize;
    int m_BufPos;
    std::string m_LastErrorStr;
    std::unique_ptr<char[]> m_RecvBuf;
    char m_Ip[16] = {0};    // peer Ip
    int m_Port = 0;         // peer port

};

#endif
