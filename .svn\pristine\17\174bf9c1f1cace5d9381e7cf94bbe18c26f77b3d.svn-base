{"COMMAND": [{"name": "UB_PCI_REG", "DeviceId": "0", "Description": "Write and read pci register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "UB_HARDWARE_VERSION", "DeviceId": "6", "Description": "Read hardware version", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "UB_BUSI_COUNT", "DeviceId": "9", "Description": "Read busi board count", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "TESTER_HW_TYPE", "DeviceId": "10", "Description": "Read tester hw type", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": "0:WT448, 1:WT428"}]}, {"name": "SW_AD7682_POWER_DETECT", "DeviceId": "101", "Description": "Get inner power device ad7682 code, Please turn Gap Power off!", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Port", "Addr": [{"Value": "1", "Name": "PORT1", "Destination": ""}, {"Value": "2", "Name": "PORT2", "Destination": ""}, {"Value": "3", "Name": "PORT3", "Destination": ""}, {"Value": "4", "Name": "PORT4", "Destination": ""}, {"Value": "5", "Name": "PORT5", "Destination": ""}, {"Value": "6", "Name": "PORT6", "Destination": ""}, {"Value": "7", "Name": "PORT7", "Destination": ""}, {"Value": "8", "Name": "PORT8", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_BOARD_INFO", "DeviceId": "106", "Description": "Get Busi Board info", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "0x0", "Name": "FPGA_VERSION", "Destination": ""}, {"Value": "0x4", "Name": "COMPILE_DATE", "Destination": ""}, {"Value": "0x8", "Name": "COMPILE_TIME", "Destination": ""}, {"Value": "0xC", "Name": "MODULE_VERSION", "Destination": ""}, {"Value": "0x10", "Name": "FPGA_PLL_LOCK", "Destination": ""}, {"Value": "0xB8", "Name": "BUSI_VERSION_SEL", "Destination": ""}, {"Value": "0xBC", "Name": "BUSI_VERSION", "Destination": ""}], "ValueName": "Value", "Value": [{"Value": "0x0", "Name": "BB_HW_VERSION", "Destination": ""}, {"Value": "0x1", "Name": "SW_HW_VERSION", "Destination": ""}], "Result": [{"Type": "int", "Name": "Value", "Destination": ""}]}, {"name": "BB_AT88_CLK_LOOP", "DeviceId": "107", "Description": "AT88SC0104 CLK Loop", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "LoopCnt", "Value": [], "Result": [{"Type": "int", "Name": "Value", "Destination": ""}]}, {"name": "BB_AT88_WR", "DeviceId": "108", "Description": "Write and read AT88SC0104", "Type": "WR", "ChipSelName": "Command", "ChipSel": [], "AddrName": "Addr&Len", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "string", "Name": "Value", "Destination": ""}]}, {"name": "BB_FLASH", "DeviceId": "109", "Description": "Write and read flash", "Type": "WR", "ChipSelName": "ChipSel", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Value", "Destination": ""}]}, {"name": "BB_FAN_ADT7475", "DeviceId": "112", "Description": "Write and read adt7475 register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_FAN_SPEED", "DeviceId": "113", "Description": "Set and get fan speed (set pwm percentage)", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "FanId", "Addr": [{"Value": "1", "Name": "Fan1", "Destination": ""}, {"Value": "2", "Name": "Fan2", "Destination": ""}, {"Value": "3", "Name": "Fan3", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Pwm_percentage", "Destination": ""}, {"Type": "int", "Name": "Rotating_speed ", "Destination": ""}]}, {"name": "SW_SWITCH_ATT", "DeviceId": "115", "Description": "Write and read swb port att code", "Type": "WR", "ChipSelName": "ChipSel", "ChipSel": [{"Value": "0", "Name": "SWITCH_ATT_VSA", "Destination": ""}, {"Value": "1", "Name": "SWITCH_ATT_VSG", "Destination": ""}], "AddrName": "Port", "Addr": [{"Value": "1", "Name": "Port1", "Destination": ""}, {"Value": "2", "Name": "Port2", "Destination": ""}, {"Value": "3", "Name": "Port3", "Destination": ""}, {"Value": "4", "Name": "Port4", "Destination": ""}, {"Value": "5", "Name": "Port5", "Destination": ""}, {"Value": "6", "Name": "Port6", "Destination": ""}, {"Value": "7", "Name": "Port7", "Destination": ""}, {"Value": "8", "Name": "Port8", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_LED_BIT", "DeviceId": "116", "Description": "Write and read LED stauts", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "0", "Name": "LED_R1", "Destination": ""}, {"Value": "1", "Name": "LED_G1", "Destination": ""}, {"Value": "2", "Name": "LED_R2", "Destination": ""}, {"Value": "3", "Name": "LED_G2", "Destination": ""}, {"Value": "4", "Name": "LED_R3", "Destination": ""}, {"Value": "5", "Name": "LED_G3", "Destination": ""}, {"Value": "6", "Name": "LED_R4", "Destination": ""}, {"Value": "7", "Name": "LED_G4", "Destination": ""}, {"Value": "8", "Name": "LED_R5", "Destination": ""}, {"Value": "9", "Name": "LED_G5", "Destination": ""}, {"Value": "10", "Name": "LED_R6", "Destination": ""}, {"Value": "11", "Name": "LED_G6", "Destination": ""}, {"Value": "12", "Name": "LED_R7", "Destination": ""}, {"Value": "13", "Name": "LED_G7", "Destination": ""}, {"Value": "14", "Name": "LED_R8", "Destination": ""}, {"Value": "15", "Name": "LED_G8", "Destination": ""}, {"Value": "16", "Name": "LED_LINK", "Destination": ""}, {"Value": "17", "Name": "LED_ERROR", "Destination": ""}, {"Value": "18", "Name": "LED_POWER_GREED", "Destination": ""}, {"Value": "19", "Name": "LED_POWER_RED", "Destination": ""}, {"Value": "20", "Name": "ALL", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_PLL_ADF4002", "DeviceId": "119", "Description": "Write and read adf4002 register", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "0", "Name": "ADF4002_1", "Destination": ""}, {"Value": "1", "Name": "ADF4002_2", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_GET_MOD_ID", "DeviceId": "120", "Description": "Get the port banding ModId", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "1", "Name": "Port1", "Destination": ""}, {"Value": "2", "Name": "Port2", "Destination": ""}, {"Value": "3", "Name": "Port3", "Destination": ""}, {"Value": "4", "Name": "Port4", "Destination": ""}, {"Value": "5", "Name": "Port5", "Destination": ""}, {"Value": "6", "Name": "Port6", "Destination": ""}, {"Value": "7", "Name": "Port7", "Destination": ""}, {"Value": "8", "Name": "Port8", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "SW_SET_PORT_STATE", "DeviceId": "121", "Description": "Set Port State", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Port", "Addr": [{"Value": "1", "Name": "Port1", "Destination": ""}, {"Value": "2", "Name": "Port2", "Destination": ""}, {"Value": "3", "Name": "Port3", "Destination": ""}, {"Value": "4", "Name": "Port4", "Destination": ""}, {"Value": "5", "Name": "Port5", "Destination": ""}, {"Value": "6", "Name": "Port6", "Destination": ""}, {"Value": "7", "Name": "Port7", "Destination": ""}, {"Value": "8", "Name": "Port8", "Destination": ""}], "ValueName": "State", "Value": [{"Value": "0", "Name": "SISO_INIT", "Destination": ""}, {"Value": "1", "Name": "SISO_OFF", "Destination": ""}, {"Value": "2", "Name": "SISO_PI", "Destination": ""}, {"Value": "3", "Name": "SISO_PA", "Destination": ""}, {"Value": "5", "Name": "SISO_DET_PI", "Destination": ""}, {"Value": "8", "Name": "SISO_LOOP_PI", "Destination": ""}], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_SWITCH_SHIFT", "DeviceId": "122", "Description": "write and read swb shift data State", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "ShitfId", "Addr": [{"Value": "0", "Name": "Shift0", "Destination": ""}, {"Value": "1", "Name": "Shift1", "Destination": ""}, {"Value": "2", "Name": "Shift2", "Destination": ""}, {"Value": "3", "Name": "Shift3", "Destination": ""}, {"Value": "4", "Name": "Shift4", "Destination": ""}, {"Value": "5", "Name": "Shift5", "Destination": ""}, {"Value": "6", "Name": "Shift6", "Destination": ""}, {"Value": "7", "Name": "Shift7", "Destination": ""}, {"Value": "8", "Name": "Shift8", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_LED_REG", "DeviceId": "123", "Description": "write and read led reg", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "SW_PORT_TEMPERATURE", "DeviceId": "124", "Description": "Get sw Board temperature value", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "1", "Name": "Port1", "Destination": ""}, {"Value": "2", "Name": "Port2", "Destination": ""}, {"Value": "3", "Name": "Port3", "Destination": ""}, {"Value": "4", "Name": "Port4", "Destination": ""}, {"Value": "5", "Name": "Port5", "Destination": ""}, {"Value": "6", "Name": "Port6", "Destination": ""}, {"Value": "7", "Name": "Port7", "Destination": ""}, {"Value": "8", "Name": "Port8", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "double", "Name": "Value", "Destination": ""}]}, {"name": "BB_AD5611", "DeviceId": "200", "Description": "Write and read AD5611 register, Vout = (CODE / 1024) * 4.096 = CODE * 0.004, CODE = Vout * 250", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "0", "Name": "AD5611_RF", "Destination": ""}, {"Value": "1", "Name": "AD5611_BB", "Destination": ""}], "ValueName": "CODE", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": "CODE = Vout * 250"}]}, {"name": "BB_BUSI_BOARD_INFO", "DeviceId": "207", "Description": "Get busi board base info", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [{"Value": "0x0", "Name": "FPGA_VERSION", "Destination": ""}, {"Value": "0x4", "Name": "COMPILE_DATE", "Destination": ""}, {"Value": "0x8", "Name": "COMPILE_TIME", "Destination": ""}, {"Value": "0xC", "Name": "MODULE_VERSION", "Destination": ""}, {"Value": "0x10", "Name": "PLL_LOCK", "Destination": ""}, {"Value": "0x14", "Name": "BB_SYM_RST", "Destination": ""}, {"Value": "0x18", "Name": "LO_LOCK_DET", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "LO_LMX2594", "DeviceId": "208", "Description": "Write and read LMX2594 register", "Version": "A+", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "LO_LMX2594_FREQ", "DeviceId": "209", "Description": "Set LMX2594 Freq", "Version": "A+", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Freq(MHz)", "Addr": [], "ValueName": "PowerLever", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_AD7091", "DeviceId": "211", "Description": "Write and read AD7091 register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_AD7091_VOLT", "DeviceId": "212", "Description": "Read AD7091 Channel Volt", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Channel", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_ADC9643", "DeviceId": "217", "Description": "Write and read ADC9643 register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "ChipAddr", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_DAC9142", "DeviceId": "218", "Description": "Write and read DAC9142 register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "ChipAddr", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_BUSI_BOARD_FREQ", "DeviceId": "228", "Description": "Set Busi board FREQ", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Frequency(KHz)", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_BUSI_START", "DeviceId": "229", "Description": "Set vsa/vsg start", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "NULL", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_BUSI_STOP", "DeviceId": "230", "Description": "Set vsa/vsg stop", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "NULL", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "LO_LMX2820", "DeviceId": "231", "Description": "Write and read LMX2820 register", "Versiom": "B+", "Type": "WR", "ChipSelName": "LoId", "ChipSel": [{"Value": "0", "Name": "LoMod", "Destination": ""}, {"Value": "1", "Name": "LoMix", "Destination": ""}], "AddrName": "Addr", "Addr": [], "ValueName": "Data", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_BUSI_DOWN", "DeviceId": "237", "Description": "Set vsa/vsg down", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "NULL", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_XDMA_WRITE", "DeviceId": "503", "Description": "write data in bin/xdma/DmaToDevice(0,1,2,3....).bin", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Channel", "Addr": [{"Value": "0", "Name": "Channel0", "Destination": ""}, {"Value": "1", "Name": "Channel1", "Destination": ""}], "ValueName": "Lenght(byte)", "Value": [], "Result": []}, {"name": "BB_XDMA_READ", "DeviceId": "504", "Description": "Save data in bin/xdma/DmaFromDevice(0,1,2,3....).bin", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Channel", "Addr": [{"Value": "0", "Name": "Channel0", "Destination": ""}, {"Value": "1", "Name": "Channel1", "Destination": ""}], "ValueName": "Lenght(byte)", "Value": [], "Result": []}, {"name": "BB_XDMA_TEST", "DeviceId": "509", "Description": "bin/xdma/DmaToDevice0.bin, bin/xdma/DmaFromDevice0.bin, Write&Read Test", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Count", "Addr": [], "ValueName": "Lenght(byte)", "Value": [], "Result": []}, {"name": "BB_HMC7044", "DeviceId": "205", "Description": "Write and read hmc7044 register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "RF_ATT", "DeviceId": "219", "Description": "ATT", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "AttID", "Addr": [{"Value": "0", "Name": "ATT1", "Destination": ""}, {"Value": "1", "Name": "ATT2", "Destination": ""}, {"Value": "2", "Name": "ATT3", "Destination": ""}, {"Value": "3", "Name": "ATT4", "Destination": ""}, {"Value": "4", "Name": "ATT5_MOD", "Destination": ""}, {"Value": "5", "Name": "ATT6_MIX", "Destination": ""}], "ValueName": "AttCode", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "RF_SHIFT_REG", "DeviceId": "214", "Description": "ATT", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "ShiftRegID", "Addr": [{"Value": "0", "Name": "Shift0", "Destination": ""}, {"Value": "1", "Name": "Shift1", "Destination": ""}, {"Value": "2", "Name": "Shift2", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "RF_SET_BAND", "DeviceId": "221", "Description": "RF BAND", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "FreqMerge", "Addr": [], "ValueName": "BandMerge", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "LO_MOD_FREQ", "DeviceId": "227", "Description": "Set MIX Freq", "Type": "W", "Version": "A+", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Freq(KHz)", "Addr": [], "ValueName": "PowerLever", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "LO_MIX_FREQ", "DeviceId": "233", "Description": "Set MIX Freq", "Type": "W", "Version": "A+", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Freq(MHz)", "Addr": [], "ValueName": "PowerLever", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_LO_COMMON_MODE", "DeviceId": "239", "Description": "Write and Read ALL MOD BB_LO_COMMON_MODE", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "NULL", "Value": [{"Value": "0", "Name": "LO COMMON MODE", "Destination": ""}, {"Value": "1", "Name": "LO DEFAULT MODE", "Destination": ""}], "Result": [{"Type": "int", "Name": "MASK", "Destination": ""}]}, {"name": "SW_AD7689_CHANNEL", "DeviceId": "241", "Description": "Read AD7689 channel code", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Channel", "Addr": [{"Value": "00", "Name": "Port4", "Destination": ""}, {"Value": "01", "Name": "Port3", "Destination": ""}, {"Value": "02", "Name": "Port2", "Destination": ""}, {"Value": "03", "Name": "Port1", "Destination": ""}, {"Value": "04", "Name": "Port8", "Destination": ""}, {"Value": "05", "Name": "Port7", "Destination": ""}, {"Value": "06", "Name": "Port6", "Destination": ""}, {"Value": "07", "Name": "Port5", "Destination": ""}], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "SW_AD7689_CHANNEL_VOLT", "DeviceId": "243", "Description": "Read AD7689 channel VOLT", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Channel", "Addr": [{"Value": "00", "Name": "Port4", "Destination": ""}, {"Value": "01", "Name": "Port3", "Destination": ""}, {"Value": "02", "Name": "Port2", "Destination": ""}, {"Value": "03", "Name": "Port1", "Destination": ""}, {"Value": "04", "Name": "Port8", "Destination": ""}, {"Value": "05", "Name": "Port7", "Destination": ""}, {"Value": "06", "Name": "Port6", "Destination": ""}, {"Value": "07", "Name": "Port5", "Destination": ""}], "ValueName": "VOLT", "Value": [], "Result": [{"Type": "double", "Name": "Data", "Destination": ""}]}, {"name": "LO_HMC833_REG", "DeviceId": "242", "Description": "Write and Read HMC833 Reg", "Type": "WR", "Version": "A+", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Address", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_LMX2582", "DeviceId": "246", "Description": "Write and read LTC2582 register", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Addr", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "BB_VSG_IFG_DEBUG", "DeviceId": "401", "Description": "Set Vsg Gap Power ctrl debug", "Type": "W", "Version": "A+", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Debug Enable", "Value": [{"Value": "-1", "Name": "NOT DEBUG", "Destination": ""}, {"Value": "0", "Name": "DEBUG_IFG_SET_OFF", "Destination": ""}, {"Value": "1", "Name": "DEBUG_IFG_SET_ON", "Destination": ""}], "Result": [{"Type": "int", "Name": "Enable", "Destination": ""}]}, {"name": "DEV_CMD_VSG_BROADCAST_PORT_POWER", "DeviceId": "408", "Description": "Set Vsg Broadcast mode per port power", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [{"Value": "1", "Name": "Port1", "Destination": ""}, {"Value": "2", "Name": "Port2", "Destination": ""}, {"Value": "3", "Name": "Port3", "Destination": ""}, {"Value": "4", "Name": "Port4", "Destination": ""}, {"Value": "5", "Name": "Port5", "Destination": ""}, {"Value": "6", "Name": "Port6", "Destination": ""}, {"Value": "7", "Name": "Port7", "Destination": ""}, {"Value": "8", "Name": "Port8", "Destination": ""}], "ValueName": "Power", "Value": [], "Result": [{"Type": "double", "Name": "power", "Destination": ""}]}, {"name": "WT_BRUN_FPGA", "DeviceId": "505", "Description": "./bin/fpgadata/backfpga.rpd \nor ./bin/fpgadata/basefpga.bin", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "ACTIVE", "Value": [{"Value": "0", "Name": "Erase And Program", "Destination": ""}, {"Value": "1", "Name": "Only Erase", "Destination": ""}], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "WT_READ_BACK_FPGA", "DeviceId": "506", "Description": "./bin/fpgadata/backreadback.bin \nor ./bin/fpgadata/basereadback.bin", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "NULL", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "TEST_SAVE_XDMA_DATA", "DeviceId": "600", "Description": "./bin/Vsa?_CaptureData_(Index).csv \nor ./bin/Vsg?_PnData_(Index).csv", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "Index", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "TEST_UNIT_STRESS_TEST", "DeviceId": "603", "Description": "", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "TEST CHOOSE", "Addr": [{"Value": "0", "Name": "pci reg continuous", "Destination": ""}, {"Value": "1", "Name": "pci reg alternate", "Destination": ""}, {"Value": "2", "Name": "start flow led", "Destination": ""}, {"Value": "3", "Name": "pci reg read", "Destination": ""}, {"Value": "4", "Name": "multi thread check pci reg", "Destination": ""}], "ValueName": "Test Count", "Value": [], "Result": [{"Type": "int", "Name": "Status", "Destination": "0:success;1:error;2:running"}, {"Type": "int", "Name": "TotalCnt", "Destination": "Test total count"}, {"Type": "int", "Name": "FailedCnt", "Destination": "Failed count"}, {"Type": "int", "Name": "OpcCntPerLoop", "Destination": "operate count per loop"}, {"Type": "double", "Name": "Usetime", "Destination": "unit:s"}]}, {"name": "TEST_BACK_STRESS_TEST", "DeviceId": "604", "Description": "", "Type": "WR", "ChipSelName": "ChipId", "ChipSel": [], "AddrName": "TEST CHOOSE", "Addr": [{"Value": "0", "Name": "AD5611", "Destination": ""}, {"Value": "1", "Name": "AD7682_CHANNEL", "Destination": ""}, {"Value": "2", "Name": "AD9228_REG", "Destination": ""}, {"Value": "3", "Name": "AD9228_CHANNEL", "Destination": ""}, {"Value": "4", "Name": "HMC7043", "Destination": ""}, {"Value": "5", "Name": "CRYPTO_BRUN", "Destination": ""}, {"Value": "6", "Name": "CRYPTO_READ", "Destination": ""}, {"Value": "7", "Name": "FLASH", "Destination": ""}, {"Value": "8", "Name": "AD7091_REG", "Destination": ""}, {"Value": "9", "Name": "AD7091_CONV", "Destination": ""}, {"Value": "10", "Name": "FAN_REG", "Destination": ""}, {"Value": "11", "Name": "SW_SHIFT", "Destination": ""}, {"Value": "12", "Name": "LED_REG", "Destination": ""}, {"Value": "13", "Name": "PA_42553", "Destination": ""}, {"Value": "14", "Name": "ADF4002", "Destination": ""}, {"Value": "15", "Name": "SW_PORT_TEMP", "Destination": ""}], "ValueName": "Test Count", "Value": [], "Result": [{"Type": "int", "Name": "Status", "Destination": "0:success;1:error;2:running"}, {"Type": "int", "Name": "TotalCnt", "Destination": "Test total count"}, {"Type": "int", "Name": "FailedCnt", "Destination": "Failed count"}, {"Type": "int", "Name": "OpcCntPerLoop", "Destination": "operate count per loop"}, {"Type": "double", "Name": "Usetime", "Destination": "unit:s"}]}, {"name": "TEST_BUSI_STRESS_TEST", "DeviceId": "605", "Description": "", "Type": "WR", "ChipSelName": "ChipId", "ChipSel": [], "AddrName": "TEST CHOOSE", "Addr": [{"Value": "0", "Name": "RF_AD5611", "Destination": ""}, {"Value": "1", "Name": "RF_AD7682_VOLT_CH", "Destination": ""}, {"Value": "2", "Name": "RF_AD7682_SINGLE_CH", "Destination": ""}, {"Value": "3", "Name": "RF_ADF4106", "Destination": ""}, {"Value": "4", "Name": "BB_HM7044", "Destination": ""}, {"Value": "5", "Name": "RF_LTC5594", "Destination": ""}, {"Value": "6", "Name": "LO_LMX2594_REG", "Destination": ""}, {"Value": "7", "Name": "RF_FLASH", "Destination": ""}, {"Value": "8", "Name": "BB_AD7091_REG", "Destination": ""}, {"Value": "9", "Name": "BB_AD7091_CONV", "Destination": ""}, {"Value": "10", "Name": "LO_DDS", "Destination": ""}, {"Value": "11", "Name": "BB_ATT_SHIFT", "Destination": ""}, {"Value": "12", "Name": "BB_ATT_CODE", "Destination": ""}, {"Value": "13", "Name": "BB_LO_SHIFT", "Destination": ""}, {"Value": "14", "Name": "BB_DAC", "Destination": ""}, {"Value": "15", "Name": "BB_ADC", "Destination": ""}, {"Value": "16", "Name": "BB_XDMA_READ", "Destination": ""}, {"Value": "17", "Name": "BB_XDMA_WRITE", "Destination": ""}, {"Value": "18", "Name": "BB_XDMA_WRITE_READ_VERIFY", "Destination": ""}, {"Value": "19", "Name": "LO_MOD_FREQ", "Destination": ""}, {"Value": "20", "Name": "LO_MIX_FREQ", "Destination": ""}, {"Value": "21", "Name": "LO_BOARD_FREQ", "Destination": ""}, {"Value": "22", "Name": "LO_LMX2820_REG", "Destination": ""}, {"Value": "23", "Name": "RF_AD7689_VOLT_CH", "Destination": ""}, {"Value": "24", "Name": "RF_AD7689_SINGLE_CH", "Destination": ""}, {"Value": "25", "Name": "LO_HMC833_REG", "Destination": ""}], "ValueName": "Test Count", "Value": [], "Result": [{"Type": "int", "Name": "Status", "Destination": "0:success;1:error;2:running"}, {"Type": "int", "Name": "TotalCnt", "Destination": "Test total count"}, {"Type": "int", "Name": "FailedCnt", "Destination": "Failed count"}, {"Type": "int", "Name": "OpcCntPerLoop", "Destination": "operate count per loop"}, {"Type": "double", "Name": "Usetime", "Destination": "unit:s"}]}, {"name": "DEV_RESPONSE_DEBUG", "DeviceId": "606", "Description": "dev response time debug", "Type": "WR", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Index", "Addr": [{"Value": "0", "Name": "MODE_ENABLE", "Destination": ""}, {"Value": "1", "Name": "SYNC_TESTER_CONFIG", "Destination": ""}, {"Value": "2", "Name": "SWITCH_ATT", "Destination": ""}, {"Value": "3", "Name": "VSG_PA_ON_OFF", "Destination": ""}, {"Value": "10", "Name": "RF_ATT0", "Destination": ""}, {"Value": "11", "Name": "RF_ATT1", "Destination": ""}, {"Value": "12", "Name": "RF_ATT2", "Destination": ""}, {"Value": "13", "Name": "RF_ATT3", "Destination": ""}, {"Value": "14", "Name": "RF_ATT4", "Destination": ""}, {"Value": "15", "Name": "RF_ATT5", "Destination": ""}], "ValueName": "NULL", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "CAL_OCCUPY_DEV", "DeviceId": "507", "Description": "if bit is setted, After dev done, FW will not release dev", "Type": "W", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "NULL", "Addr": [], "ValueName": "NULL", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}, {"name": "LO_CHECK", "DeviceId": "244", "Description": "Check LO Lock", "Type": "R", "ChipSelName": "NULL", "ChipSel": [], "AddrName": "Mod(0)/MIX(1)", "Addr": [], "ValueName": "Value", "Value": [], "Result": [{"Type": "int", "Name": "Data", "Destination": ""}]}]}