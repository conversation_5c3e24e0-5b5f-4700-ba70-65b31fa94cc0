#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <cmath>
#include <stdio.h>
#include <cstring>
#include <iomanip>
#include <fcntl.h>

#include "devvsg.h"
#include "wterror.h"
#include "wtlog.h"
#include "errorlib.h"
#include "devdef.h"
#include "templib.h"
#include "wtxdma.h"
#include "wtcal.h"
#include "devlib.h"
#include "fpgadefine.h"
using namespace std;

int DevVsg::InitBaseBandMod()
{
    //初始化DAC
    int Ret = DACInit();
    RetWarnning(Ret, "VSG DACInit failed!");
    ErrorLib::Instance().CheckErrCode(Ret, Ret);

    //初始化VSG IQ反转
    Json::Value Reg = m_JsonRoot["IQSwap"]["VSGSwapValue"];
    if (!Reg.isNull())
    {
        m_IQSwitch = std::strtol(Reg.asString().c_str(), 0, 0);
        Ret = SetIQSwitch(m_IQSwitch);
        ErrorLib::Instance().CheckErrCode(Ret, WT_BUSI_BASE_IQ_SWITCH_FAILED);
        RetWarnning(Ret, "SetIQSwitch failed!");
    }

    m_VsgGapPowerDebug = GAP_POWER_NOT_DEBUG;
    Json::Value IfgReg = m_JsonRoot["VsgGapPowerParam"];
    if (!IfgReg.isNull())
    {
        m_ExtHwParam.IfgParam[0] = std::strtol(IfgReg["Param1"].asString().c_str(), 0, 0);
        m_ExtHwParam.IfgParam[1] = std::strtol(IfgReg["Param2"].asString().c_str(), 0, 0);
        m_ExtHwParam.IfgParam[2] = std::strtol(IfgReg["Param3"].asString().c_str(), 0, 0);
    }
    else
    {
        memset(m_ExtHwParam.IfgParam, 0, sizeof(m_ExtHwParam.IfgParam));
    }
    Json::Value DevmReg = (m_TesterType == HW_WT428)
                              ? m_JsonRoot["VsgDevmDelay"]["WT428"]
                              : m_JsonRoot["VsgDevmDelay"]["WT448"];
    if (DevmReg.isArray() && DevmReg.size() > m_ModId)
    {
        m_ExtHwParam.DevmDelay = std::strtol(DevmReg[m_ModId]["Delay"].asString().c_str(), 0, 0);
    }
    else
    {
        m_ExtHwParam.DevmDelay = 0;
    }

    if(m_TesterType == HW_WT418 && m_HwVersion >= VERSION_B)
    {
        IQModeInit();
    }

    return Ret;
}

int DevVsg::CheckDacStatus()
{
    int Flag;
    int Ret = ReadDACReg(DAC_INTE_FLAG, Flag);
    if (Ret == WT_OK && GetBit(Flag, 6))
    {
        // char pBuf[256];
        // snprintf(pBuf, sizeof(pBuf), "ModId%d ReadDACReg DAC_INTE_FLAG = %#x, Ret = %#x", m_ModId, Flag, Ret);
        // WTLog::Instance().LOGERR(Ret, pBuf);
        m_DacNeedReset = true;
        //ResetDacStatus();
    }
    return WT_OK;
}

int DevVsg::ResetDacStatus()
{
    if (m_DacNeedReset)
    {
        m_DacNeedReset = false;
        char pBuf[256];
        snprintf(pBuf, sizeof(pBuf), "ModId%d ResetDacStatus", m_ModId);
        WTLog::Instance().LOGERR(WT_OK, pBuf);

        WriteDACReg(DAC_RESET, 0);
        WriteDACReg(DAC_RESET, 1);
        WriteDACReg(DAC_RESET, 0);

        usleep(1000);
    }
    return WT_OK;
}

int DevVsg::SetRFPowerStatus(WT_SWITCH_STATUS Status)
{
    (void)Status;
    return WT_OK;
}

int DevVsg::GetGainParam(Tx_Gain_Parm &GainParm)
{
    GainParm = m_GainParm;
    return WT_OK;
}

int DevVsg::SetParam(const VSGConfigType &VSGConfig, Tx_Parm &TXParm, int WorkPointMode)
{
    int Ret = WT_OK;
    int RFPort = VSGConfig.RFPort;
    int RFPortState = VSGConfig.RFPortState;
    int RFPortMode = WT_SW_STATE_MODE_SISO;
    TXParm.rf_port = VSGConfig.RFPort;
    TXParm.freq = VSGConfig.Freq + VSGConfig.FreqOffsetHz;
    TXParm.power = VSGConfig.Power;
    TXParm.ex_iq_mode = 0;
    TXParm.pac_mode = 0;

    if(m_LOComMode == LO_IS_COM_MODE)
    {
        TXParm.share_mode = true;
    }
    else
    {
        TXParm.share_mode = false;
    }

    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        TXParm.ex_iq_mode = true;
    }
    else
    {
        TXParm.ex_iq_mode = false;
    }
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif
    if(m_TesterType < HW_WT418)
    {
        //设置工作模式
        Ret = SetUnitModWorkMode(static_cast<WT_DEVICE_MODE>(VSGConfig.DeviceMode));
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetUnitModWorkMode failed!");
    }

    bool Is400M = false;
    if (!Basefun::CompareDoubleAccuracy1K(TXParm.freq, 400 * MHz))
    {
        Is400M = true;
        TXParm.freq += MHz;
    }

    TXParm.unit = m_ModId;
    if (Is8080Master(VSGConfig.DeviceMode))
    {
        TXParm.unit_mode = CAL_UNIT_MODE_MASTER;
        RFPortMode = WT_SW_STATE_MODE_8080;
    }
    else if (Is8080Slave(VSGConfig.DeviceMode))
    {
        TXParm.unit_mode = CAL_UNIT_MODE_SLAVE;
        RFPort = WT_RF_PORT_OFF;
        RFPortMode = WT_SW_STATE_MODE_8080;
    }
    else if(VSGConfig.BroadcastEnable)
    {
        TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        RFPortMode = WT_SW_STATE_MODE_BROADCAST;
    }
    else
    {
        TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        RFPortMode = WT_SW_STATE_MODE_SISO;
    }

    TXParm.sample_freq = VSGConfig.SamplingRate;
    SetListModeStatus(true);
    InitVirtualAddr();
    RecordlistSegConfig(RFPort);
    if (WorkPointMode == false && RFPortMode == WT_SW_STATE_MODE_BROADCAST)
    {
        //获取TX链路综合校准数据
        Tx_BC_Parm TXBroadcastParm;
        TXBroadcastParm.unit = TXParm.unit;
        TXBroadcastParm.rf_port = TXParm.rf_port;
        TXBroadcastParm.freq = TXParm.freq;
        TXBroadcastParm.freq = TXParm.freq;
        TXBroadcastParm.sample_freq = TXParm.sample_freq;
        for (int i = WT_RF_1; i < WT_RF_MAX; ++i)
        {
            TXBroadcastParm.power[i - WT_RF_1] = VSGConfig.BroadcastPower[i];
        }
        Ret = wt_calibration_get_tx_bc_setting(&TXBroadcastParm);
        RetWarnning(Ret, "wt_calibration_get_tx_bc_setting failed!");
        memcpy(&TXParm.freq_parm, &TXBroadcastParm.freq_parm, sizeof(TXParm.freq_parm));
        memcpy(&TXParm.tx_dc_offset_parm, &TXBroadcastParm.tx_dc_offset_parm, sizeof(TXParm.tx_dc_offset_parm));
        memcpy(&TXParm.tx_iq_imb_parm, &TXBroadcastParm.tx_iq_imb_parm, sizeof(TXParm.tx_iq_imb_parm));
        memcpy(&TXParm.tx_iq_imb_parm_160m, &TXBroadcastParm.tx_iq_imb_parm_160m, sizeof(TXParm.tx_iq_imb_parm_160m));
        memcpy(&TXParm.tx_iq_imb_parm_320m, &TXBroadcastParm.tx_iq_imb_parm_320m, sizeof(TXParm.tx_iq_imb_parm_320m));
        memcpy(&TXParm.tx_spec_flat_comp_parm, &TXBroadcastParm.tx_spec_flat_comp_parm, sizeof(TXParm.tx_spec_flat_comp_parm));
        if (RFPort > WT_RF_OFF && RFPort < WT_RF_MAX)
        {
            TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state;
            TXParm.tx_gain_parm.tx_sw_gain.sw_tx_att_code[0] = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_att_code;
            TXParm.tx_gain_parm.tx_sw_gain.actual_mpl = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].actual_mpl;
            TXParm.tx_gain_parm.is_pa_on = TXBroadcastParm.tx_rf_gain_parm.is_pa_on;
            memcpy(TXParm.tx_gain_parm.att_code, TXBroadcastParm.tx_rf_gain_parm.att_code, sizeof(int) * TX_ATT_COUNT);
            TXParm.tx_gain_parm.dac_gain = TXBroadcastParm.tx_rf_gain_parm.dac_gain;
        }
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;

        for (int RFPort = WT_RF_1; RFPort < WT_RF_MAX && ((RFPort - WT_RF_1) < CAL_RF_PORT_MAX); ++RFPort)
        {
            /**************************开关板配置************************************/
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", TXPort RF = " << RFPort
                      << ", State=" << TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state << ", Mode=" << RFPortMode << std::endl;
#endif
            // 校查开关板在位情况与该单元是否为80+80从机模式
            if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
            {
#if DEVLIB_DEBUG
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                Ret = SetRFPort(RFPort, RFPortMode, TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "VSG SetRFPort failed");

                m_RunData.RFBroadcastPortState[RFPort] = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state;//记录广播端口的链路状态，用于SetSwitchVsgCTL3判断依据
            }

            Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_att_code);
            RetAssert(Ret, "SetGain SetSwbAttCode failed!");
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Port" << m_RunData.RFPort << " BPAttCode =" << TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_att_code << std::endl;
        }
    }
    else if(WorkPointMode == false)
    {
        //获取TX链路综合校准数据
        Ret = wt_calibration_get_tx_setting(&TXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_setting failed!");
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
    }
    else
    {
        // 工作点不取温度
        TXParm.sw_temperature = 30.0;
        TXParm.temperature = 30.0;

        //获取TX链路综合校准数据
        Ret = wt_calibration_get_tx_work_point_setting(&TXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_work_point_setting failed!");
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
    }

    if (Is400M)
    {
        TXParm.freq -= MHz;
        TXParm.freq_parm.LoParm[LoMod].freq = TXParm.freq_parm.LoParm[LoMod].freq > 0
                                                  ? TXParm.freq_parm.LoParm[LoMod].freq - 1
                                                  : TXParm.freq_parm.LoParm[LoMod].freq + 1;
    }

    if(m_AnalogIQSW != ANALOGIQ_MODE)
    {

    /**************************开关板配置************************************/

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", TXPort RF = " << RFPort
              << ", State=" << RFPortState << ", Mode=" << RFPortMode << std::endl;
#endif
    //校查开关板在位情况与该单元是否为80+80从机模式
    if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
    {
        //当RF端口改变或端口功能改变时重新设置RF端口
        if ((m_RunData.RFPortState != RFPortState && m_RunData.RFPort != WT_RF_OFF) ||
            m_RunData.RFPortMode != RFPortMode ||
            m_RunData.RFPort != RFPort ||
            m_BackBoard->GetPortStatus(RFPort) != WT_RF_TX_STATUS)
        {
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
            Ret = SetRFPort(RFPort, RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "VSG SetRFPort failed");
        }
    }

    /**************************射频板配置************************************/

    //配置TX链路上的增益、本振、波段开关
    Ret = SetFreqAndGain(VSGConfig, TXParm);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "SetTXFreqAndGain failed!");
    }

    /**************************基带板配置************************************/
    //ATT步进误差补偿
    Ret = SetDacGain(TXParm.tx_gain_parm.dac_gain);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TXParm.tx_gain_parm.dac_gain=" << TXParm.tx_gain_parm.dac_gain << std::endl;
    RetAssert(Ret, "SetDacGain failed!");

    //设置TX DC IQ offset
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(VSGConfig.DcOffsetI) << Pout(TXParm.tx_dc_offset_parm.i_code)
              << Pout(VSGConfig.DcOffsetQ) << Pout(TXParm.tx_dc_offset_parm.q_code) << std::endl;
    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        Ret = SetTXDCOffset(CheckIQOffset(VSGConfig.DcOffsetI + TXParm.tx_dc_offset_parm.i_code),
                            CheckIQOffset(VSGConfig.DcOffsetQ + TXParm.tx_dc_offset_parm.q_code));
    }
    else
    {
        Ret = SetTXDCOffset(CheckIQOffset(TXParm.tx_dc_offset_parm.i_code),
                            CheckIQOffset(TXParm.tx_dc_offset_parm.q_code));
    }

    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "SetTXDCOffset failed!");

#if WT_BOOST_MANUAL
    // 设置boost开关
    Ret = SetBoostStatus(TXParm.tx_gain_parm.is_pa_on);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
#endif
    Tx_Gain_Parm &GainParm = (m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
                                    ? m_GainParmDebug
                                    : m_GainParm;
    if (m_AttConfigMode == WT_ATT_CONFIG_MODE_START || m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
    {

        // 设置TX链路增益
        Ret = SetGain(GainParm);
        RetAssert(Ret, "SetGain failed!");
    }
    m_RunData.Power = VSGConfig.Power;

    switch (m_RunData.RFPortState)
    {
    case WT_RF_STATE_PI:
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
    case WT_RF_STATE_RF_PI:
    case WT_RF_STATE_RF_PA_1:
    case WT_RF_STATE_RF_PA_2:
        Ret = SetSwitchVsgCTL3(m_RunData.RFPort, m_RunData.RFPortMode, m_RunData.RFPortState,
                            static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetSwitchVsgCTL3 set ifg failed!");
        break;
    default:
        break;
    }
    m_RunData.IfgStatus = (m_VsgGapPowerDebug == GAP_POWER_DEBUG_ON || m_VsgGapPowerDebug == GAP_POWER_DEBUG_OFF)
                              ? m_VsgGapPowerDebug
                              : VSGConfig.VsgIfgStatus;
    if(m_TesterType == HW_WT418 && m_RunData.IfgStatus)
    {
        switch (m_RunData.RFPortState)
        {
        case WT_RF_STATE_PI:
        case WT_RF_STATE_RF_PI:
        case WT_RF_STATE_FULL_DUPLEX_PI:
            Ret = SetIfgCtrlMode(m_RunData.RFPort,true);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetIfgCtrlMode failed!");
            break;
        case WT_RF_STATE_PA_1:
        case WT_RF_STATE_PA_2:
        case WT_RF_STATE_RF_PA_1:
        case WT_RF_STATE_RF_PA_2:
            Ret = SetIfgCtrlMode(m_RunData.RFPort,false);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetIfgCtrlMode failed!");
        }
    }
    GetParamDmaBuf(m_HwParamDmaDataOffset);
    memcpy(m_TempHwParamDmaBuf, m_HwParamDmaBuf, m_HwParamDmaDataOffset);//使用非mmap内存地址传输dma数据
    m_TempHwParamDmaDataOffset = m_HwParamDmaDataOffset;
    //TODO:仿真数据
    ////////////////////////////////////////////////////////////////////////
    SaveDmaData(DMA_CHANNEL_ID, H2C_DATA_TYPE_VSG_HW_PARAM, m_TempHwParamDmaBuf, m_TempHwParamDmaDataOffset);
    ////////////////////////////////////////////////////////////////////////
    Ret = WriteDmaData(DMA_CHANNEL_ID, m_TempHwParamDmaBuf, m_TempHwParamDmaDataOffset);
    SetListModeStatus(false);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "WriteDmaData failed!");
    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        //设置TX CmVolt
        Ret = SetCmVolt(VSGConfig.CmVolt);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetWarnning(Ret, "SetCmVolt failed!");
    }
    m_RunData.Power = VSGConfig.Power;

    if (VSGConfig.WaveBw == WT_BW_160M)
    {
        memcpy(&TXParm.tx_iq_imb_parm, &TXParm.tx_iq_imb_parm_160m, sizeof(Tx_Iq_Imb_Parm));
    }
    else if (VSGConfig.WaveBw == WT_BW_320M)
    {
        memcpy(&TXParm.tx_iq_imb_parm, &TXParm.tx_iq_imb_parm_320m, sizeof(Tx_Iq_Imb_Parm));
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG WorkMode=" << m_RunData.CurrentWorkMode << std::setprecision(4)
              << " WaveBw =" << VSGConfig.WaveBw
              << " gain_imb=" << TXParm.tx_iq_imb_parm.gain_imb
              << " quad_err=" << TXParm.tx_iq_imb_parm.quad_err
              << " timeskew=" << TXParm.tx_iq_imb_parm.timeskew
              << std::endl;
#endif

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSGSetConfig Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::SetParam2(const VSGConfigType &VSGConfig, Tx_Parm &TXParm, int WorkPointMode)
{
    int Ret = WT_OK;
    int RFPort = VSGConfig.RFPort;
    int RFPortState = VSGConfig.RFPortState;
    int RFPortMode = WT_SW_STATE_MODE_SISO;
    TXParm.rf_port = VSGConfig.RFPort;
    TXParm.freq = VSGConfig.Freq + VSGConfig.FreqOffsetHz;
    TXParm.power = VSGConfig.Power;
    TXParm.ex_iq_mode = 0;

    if(m_LOComMode == LO_IS_COM_MODE)
    {
        TXParm.share_mode = true;
    }
    else
    {
        TXParm.share_mode = false;
    }

    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        TXParm.ex_iq_mode = true;
    }
    else
    {
        TXParm.ex_iq_mode = false;
    }
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif
    if(m_TesterType < HW_WT418)
    {
        //设置工作模式
        Ret = SetUnitModWorkMode(static_cast<WT_DEVICE_MODE>(VSGConfig.DeviceMode));
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetUnitModWorkMode failed!");
    }

    bool Is400M = false;
    if (!Basefun::CompareDoubleAccuracy1K(TXParm.freq, 400 * MHz))
    {
        Is400M = true;
        TXParm.freq += MHz;
    }

    TXParm.unit = m_ModId;
    if (Is8080Master(VSGConfig.DeviceMode))
    {
        TXParm.unit_mode = CAL_UNIT_MODE_MASTER;
        RFPortMode = WT_SW_STATE_MODE_8080;
    }
    else if (Is8080Slave(VSGConfig.DeviceMode))
    {
        TXParm.unit_mode = CAL_UNIT_MODE_SLAVE;
        RFPort = WT_RF_PORT_OFF;
        RFPortMode = WT_SW_STATE_MODE_8080;
    }
    else if(VSGConfig.BroadcastEnable)
    {
        TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        RFPortMode = WT_SW_STATE_MODE_BROADCAST;
    }
    else
    {
        TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        RFPortMode = WT_SW_STATE_MODE_SISO;
    }

    TXParm.sample_freq = VSGConfig.SamplingRate;
    if (WorkPointMode == false && RFPortMode == WT_SW_STATE_MODE_BROADCAST)
    {
        //获取TX链路综合校准数据
        Tx_BC_Parm TXBroadcastParm;
        TXBroadcastParm.unit = TXParm.unit;
        TXBroadcastParm.rf_port = TXParm.rf_port;
        TXBroadcastParm.freq = TXParm.freq;
        TXBroadcastParm.freq = TXParm.freq;
        TXBroadcastParm.sample_freq = TXParm.sample_freq;
        for (int i = WT_RF_1; i < WT_RF_MAX; ++i)
        {
            TXBroadcastParm.power[i - WT_RF_1] = VSGConfig.BroadcastPower[i];
        }
        Ret = wt_calibration_get_tx_bc_setting(&TXBroadcastParm);
        RetWarnning(Ret, "wt_calibration_get_tx_bc_setting failed!");
        memcpy(&TXParm.freq_parm, &TXBroadcastParm.freq_parm, sizeof(TXParm.freq_parm));
        memcpy(&TXParm.tx_dc_offset_parm, &TXBroadcastParm.tx_dc_offset_parm, sizeof(TXParm.tx_dc_offset_parm));
        memcpy(&TXParm.tx_iq_imb_parm, &TXBroadcastParm.tx_iq_imb_parm, sizeof(TXParm.tx_iq_imb_parm));
        memcpy(&TXParm.tx_iq_imb_parm_160m, &TXBroadcastParm.tx_iq_imb_parm_160m, sizeof(TXParm.tx_iq_imb_parm_160m));
        memcpy(&TXParm.tx_iq_imb_parm_320m, &TXBroadcastParm.tx_iq_imb_parm_320m, sizeof(TXParm.tx_iq_imb_parm_320m));
        memcpy(&TXParm.tx_spec_flat_comp_parm, &TXBroadcastParm.tx_spec_flat_comp_parm, sizeof(TXParm.tx_spec_flat_comp_parm));
        if (RFPort > WT_RF_OFF && RFPort < WT_RF_MAX)
        {
            TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state;
            TXParm.tx_gain_parm.tx_sw_gain.sw_tx_att_code[0] = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_att_code;
            TXParm.tx_gain_parm.tx_sw_gain.actual_mpl = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].actual_mpl;
            TXParm.tx_gain_parm.is_pa_on = TXBroadcastParm.tx_rf_gain_parm.is_pa_on;
            memcpy(TXParm.tx_gain_parm.att_code, TXBroadcastParm.tx_rf_gain_parm.att_code, sizeof(int) * TX_ATT_COUNT);
            TXParm.tx_gain_parm.dac_gain = TXBroadcastParm.tx_rf_gain_parm.dac_gain;
        }
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;

        for (int RFPort = WT_RF_1; RFPort < WT_RF_MAX && ((RFPort - WT_RF_1) < CAL_RF_PORT_MAX); ++RFPort)
        {
            /**************************开关板配置************************************/
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", TXPort RF = " << RFPort
                      << ", State=" << TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state << ", Mode=" << RFPortMode << std::endl;
#endif
            // 校查开关板在位情况与该单元是否为80+80从机模式
            if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
            {
#if DEVLIB_DEBUG
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                Ret = SetRFPort(RFPort, RFPortMode, TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "VSG SetRFPort failed");

                m_RunData.RFBroadcastPortState[RFPort] = TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_link_state;//记录广播端口的链路状态，用于SetSwitchVsgCTL3判断依据
            }

            Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_att_code);
            RetAssert(Ret, "SetGain SetSwbAttCode failed!");
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Port" << m_RunData.RFPort << " BPAttCode =" << TXBroadcastParm.tx_sw_gain_parm[RFPort - WT_RF_1].sw_tx_att_code << std::endl;
        }
    }
    else if(WorkPointMode == false)
    {
        //获取TX链路综合校准数据
        Ret = wt_calibration_get_tx_setting(&TXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_setting failed!");
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
    }
    else
    {
        // 工作点不取温度
        TXParm.sw_temperature = 30.0;
        TXParm.temperature = 30.0;

        //获取TX链路综合校准数据
        Ret = wt_calibration_get_tx_work_point_setting(&TXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_work_point_setting failed!");
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
    }

    Ret = wt_calibration_get_tx_iq_image_data(&TXParm);
    if (Ret != WT_OK)
    {
        Ret += WT_CAL_BASE_ERROR;
    }
    RetAssert(Ret, "wt_calibration_get_tx_iq_image_data failed!");

    if (Is400M)
    {
        TXParm.freq -= MHz;
        TXParm.freq_parm.LoParm[LoMod].freq = TXParm.freq_parm.LoParm[LoMod].freq > 0
                                                  ? TXParm.freq_parm.LoParm[LoMod].freq - 1
                                                  : TXParm.freq_parm.LoParm[LoMod].freq + 1;
    }

    if(m_AnalogIQSW != ANALOGIQ_MODE)
    {

    /**************************开关板配置************************************/

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", TXPort RF = " << RFPort
              << ", State=" << RFPortState << ", Mode=" << RFPortMode << std::endl;
#endif
    //校查开关板在位情况与该单元是否为80+80从机模式
    if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
    {
        //当RF端口改变或端口功能改变时重新设置RF端口
        if ((m_RunData.RFPortState != RFPortState && m_RunData.RFPort != WT_RF_OFF) ||
            m_RunData.RFPortMode != RFPortMode ||
            m_RunData.RFPort != RFPort ||
            m_BackBoard->GetPortStatus(RFPort) != WT_RF_TX_STATUS)
        {
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
            Ret = SetRFPort(RFPort, RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "VSG SetRFPort failed");
        }
    }

    /**************************射频板配置************************************/

    //配置TX链路上的增益、本振、波段开关
    Ret = SetFreqAndGain(VSGConfig, TXParm);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "SetTXFreqAndGain failed!");
    }

    /**************************基带板配置************************************/
    //ATT步进误差补偿
    Ret = SetDacGain(TXParm.tx_gain_parm.dac_gain);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TXParm.tx_gain_parm.dac_gain=" << TXParm.tx_gain_parm.dac_gain << std::endl;
    RetAssert(Ret, "SetDacGain failed!");

    //设置TX DC IQ offset
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(VSGConfig.DcOffsetI) << Pout(TXParm.tx_dc_offset_parm.i_code)
              << Pout(VSGConfig.DcOffsetQ) << Pout(TXParm.tx_dc_offset_parm.q_code) << std::endl;
    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        Ret = SetTXDCOffset(CheckIQOffset(VSGConfig.DcOffsetI + TXParm.tx_dc_offset_parm.i_code),
                            CheckIQOffset(VSGConfig.DcOffsetQ + TXParm.tx_dc_offset_parm.q_code));
    }
    else
    {
        Ret = SetTXDCOffset(CheckIQOffset(TXParm.tx_dc_offset_parm.i_code),
                            CheckIQOffset(TXParm.tx_dc_offset_parm.q_code));
    }

    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "SetTXDCOffset failed!");

    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        //设置TX CmVolt
        Ret = SetCmVolt(VSGConfig.CmVolt);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetWarnning(Ret, "SetCmVolt failed!");
    }


    m_RunData.IfgStatus = (m_VsgGapPowerDebug == GAP_POWER_DEBUG_ON || m_VsgGapPowerDebug == GAP_POWER_DEBUG_OFF)
                              ? m_VsgGapPowerDebug
                              : VSGConfig.VsgIfgStatus;
    m_RunData.Power = VSGConfig.Power;

    if (VSGConfig.WaveBw == WT_BW_160M)
    {
        memcpy(&TXParm.tx_iq_imb_parm, &TXParm.tx_iq_imb_parm_160m, sizeof(Tx_Iq_Imb_Parm));
    }
    else if (VSGConfig.WaveBw == WT_BW_320M)
    {
        memcpy(&TXParm.tx_iq_imb_parm, &TXParm.tx_iq_imb_parm_320m, sizeof(Tx_Iq_Imb_Parm));
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG WorkMode=" << m_RunData.CurrentWorkMode << std::setprecision(4)
              << " WaveBw =" << VSGConfig.WaveBw
              << " gain_imb=" << TXParm.tx_iq_imb_parm.gain_imb
              << " quad_err=" << TXParm.tx_iq_imb_parm.quad_err
              << " timeskew=" << TXParm.tx_iq_imb_parm.timeskew
              << std::endl;
#endif

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSGSetConfig Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::SetWorkPointParam(const VSGConfigType &VSGConfig, Tx_Parm &TXParm)
{
    return SetParam(VSGConfig, TXParm, true);
}

int DevVsg::Start(int Mode)
{
    int Ret = WT_OK;
    int CellMod = 0;

#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    ResetDacStatus();

    if (m_IsRfExist)
    {
        if(m_RunData.RFPortMode == WT_SW_STATE_MODE_BROADCAST)
        {
            for (int RFPort = WT_RF_1;RFPort < WT_RF_MAX; ++RFPort)
            {
                switch (m_RunData.RFBroadcastPortState[RFPort])
                {
                case WT_RF_STATE_PI:
                case WT_RF_STATE_PA_1:
                case WT_RF_STATE_PA_2:
                case WT_RF_STATE_RF_PI:
                case WT_RF_STATE_RF_PA_1:
                case WT_RF_STATE_RF_PA_2:
                    Ret = SetSwitchVsgCTL3(RFPort, m_RunData.RFPortMode, m_RunData.RFBroadcastPortState[RFPort],
                                        static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "SetSwitchVsgCTL3 set ifg failed!");
                    break;
                default:
                    break;
                }
            }
        }
        else
        {
            switch (m_RunData.RFPortState)
            {
            case WT_RF_STATE_PI:
            case WT_RF_STATE_PA_1:
            case WT_RF_STATE_PA_2:
            case WT_RF_STATE_RF_PI:
            case WT_RF_STATE_RF_PA_1:
            case WT_RF_STATE_RF_PA_2:
                Ret = SetSwitchVsgCTL3(m_RunData.RFPort, m_RunData.RFPortMode, m_RunData.RFPortState,
                                    static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "SetSwitchVsgCTL3 set ifg failed!");
                break;
            default:
                break;
            }
        }
        Tx_Gain_Parm &GainParm = (m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
                                     ? m_GainParmDebug
                                     : m_GainParm;

#if WT_BOOST_MANUAL
        //设置boost开关

        Ret = SetBoostStatus(GainParm.is_pa_on);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
#endif

        Ret = SetIfgCtrlStatus(m_RunData.IfgStatus);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetIfgCtrlStatus set ifg failed!");
        if(m_TesterType == HW_WT418 && m_RunData.IfgStatus)
        {
            if(m_RunData.RFPortMode == WT_SW_STATE_MODE_BROADCAST)
            {
                for (int RFPort = WT_RF_1;RFPort < WT_RF_MAX; ++RFPort)
                {
                    switch (m_RunData.RFBroadcastPortState[RFPort])
                    {
                    case WT_RF_STATE_PI:
                    case WT_RF_STATE_RF_PI:
                    case WT_RF_STATE_FULL_DUPLEX_PI:
                        Ret = SetIfgCtrlMode(RFPort, true);
                        ErrorLib::Instance().CheckErrCode(Ret, Ret);
                        RetAssert(Ret, "SetIfgCtrlMode failed!");
                        break;
                    case WT_RF_STATE_PA_1:
                    case WT_RF_STATE_PA_2:
                    case WT_RF_STATE_RF_PA_1:
                    case WT_RF_STATE_RF_PA_2:
                        Ret = SetIfgCtrlMode(RFPort, false);
                        ErrorLib::Instance().CheckErrCode(Ret, Ret);
                        RetAssert(Ret, "SetIfgCtrlMode failed!");
                        break;
                    default:
                        break;
                    }
                }
            }
            else
            {
                switch (m_RunData.RFPortState)
                {
                case WT_RF_STATE_PI:
                case WT_RF_STATE_RF_PI:
                case WT_RF_STATE_FULL_DUPLEX_PI:
                    Ret = SetIfgCtrlMode(m_RunData.RFPort,true);
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "SetIfgCtrlMode failed!");
                    break;
                case WT_RF_STATE_PA_1:
                case WT_RF_STATE_PA_2:
                case WT_RF_STATE_RF_PA_1:
                case WT_RF_STATE_RF_PA_2:
                    Ret = SetIfgCtrlMode(m_RunData.RFPort,false);
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "SetIfgCtrlMode failed!");
                }
            }
        }

        if (m_AttConfigMode == WT_ATT_CONFIG_MODE_START || m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
        {
            //设置TX链路增益
            Ret = SetGain(GainParm);
            RetAssert(Ret, "SetGain failed!");
        }
    }

    if ((Ret = DrvCmd(VSG_SET_LIST_CELL_MOD, sizeof(int), &CellMod)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_START ioctl VSG_SET_CELL_MOD error");
        return Ret;
    }

    if ((Ret = DrvCmd(VSG_START, sizeof(int), &Mode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_START ioctl error");
        return Ret;
    }


    if (Mode == WT_START_MODE_NORMAL)
    {
        m_RunData.XdmaStatus = WT_XDMA_INIT;
        m_RunData.Status = WT_RX_TX_STATUS_RUNNING;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSG" << m_ModId << "  Start!--------" << std::endl;
#endif

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSGStart Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::Stop()
{
    int Ret = WT_OK;
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    Finish();
    if (!DevLib::Instance().GetFullDuplexEnable())
    {
    if (m_RunData.RFPortMode == WT_SW_STATE_MODE_BROADCAST)
    {
        for (int RFPort = WT_RF_1;RFPort < WT_RF_MAX; ++RFPort)
        {
            // 开关板ATT
            Ret = m_BackBoard->SetSwbAttCode(RFPort, BP_ATT_CODE_MAX);
            RetWarnning(Ret, "VSGStop SetSwbAttCode failed!");
        }
    }
    else
    {
        // 开关板ATT
        Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, BP_ATT_CODE_MAX);
        RetWarnning(Ret, "VSGStop SetSwbAttCode failed!");
    }
    }

    m_RunData.Freq = 0;

    //驱动层对FPGA软件复位寄存器进行复位操作
    int StopType = STOP_MODE_NORMAL;
    if ((Ret = DrvCmd(VSG_STOP, sizeof(int), &StopType)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_STOP ioctl error");
        return Ret;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSG" << m_ModId << "  Stop!--------" << std::endl;
#endif

    //增加30（改为35）毫秒延时，等待FPGA软复位完成
    //按逻辑计算的时间应该延时27ms,之前延时30ms,但校准时发现延时过短，导致发出的信号有误/发不出来信号，故改为35ms.
    // usleep(35000);

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSGStop Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::Finish()
{
    int Ret = WT_OK;
    int CellMod = 0;

    ResetDacStatus();
    ResetDmaFIFO();
    //驱动层对FPGA软件复位寄存器进行复位操作
    int StopType = STOP_MODE_NORMAL;

    if ((Ret = DrvCmd(VSG_SET_LIST_CELL_MOD, sizeof(int), &CellMod)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Finish ioctl VSG_SET_CELL_MOD error");
        return Ret;
    }

    if ((Ret = DrvCmd(VSG_STOP, sizeof(int), &StopType)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_STOP ioctl error");
        return Ret;
    }

    if(m_ListModeNeedReset)
    {
        m_ListModeNeedReset = 0;
        Ret = ResetRundataFromCache();
        if (Ret)
        {
            return Ret;
        }
    }
    //驱动层对FPGA软件复位寄存器进行复位操作
    if ((Ret = DrvCmd(VSG_CLEAR_TBT_STA_MODE, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_CLEAR_TBT_STA_MODE ioctl error");
        return Ret;
    }

    if (m_IsRfExist)
    {
        if (m_AttConfigMode == WT_ATT_CONFIG_MODE_START)
        {
            //设置衰减器code值为最大值
            for (int i = 0; i < TX_ATT_MAX; i++)
            {
                Ret = SetATTCode(i, ATT_CODE_MAX);
                RetContinue(Ret, "VSGStop SetATTCode failed!");
                m_RunData.ATTCurData[i] = ATT_CODE_MAX;
            }
        }

        Ret = SetIfgCtrlStatus(OFF);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetIfgCtrlStatus set ifg failed!");

#if WT_BOOST_MANUAL
        //设置boost开关
        Ret = SetBoostStatus(false);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetBoostStatus failed!");
#endif

        if(m_RunData.RFPortMode == WT_SW_STATE_MODE_BROADCAST)
        {
            for (int RFPort = WT_RF_1;RFPort < WT_RF_MAX; ++RFPort)
            {
                switch (m_RunData.RFBroadcastPortState[RFPort])
                {
                case WT_RF_STATE_PI:
                case WT_RF_STATE_PA_1:
                case WT_RF_STATE_PA_2:
                case WT_RF_STATE_RF_PI:
                case WT_RF_STATE_RF_PA_1:
                case WT_RF_STATE_RF_PA_2:
                    Ret = SetSwitchVsgCTL3(RFPort, m_RunData.RFPortMode, WT_RF_STATE_OFF,
                                        static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "SetSwitchVsgCTL3 set ifg failed!");
                    break;
                default:
                    break;
                }
            }
        }
        else
        {
            switch (m_RunData.RFPortState)
            {
            case WT_RF_STATE_PI:
            case WT_RF_STATE_PA_1:
            case WT_RF_STATE_PA_2:
            case WT_RF_STATE_RF_PI:
            case WT_RF_STATE_RF_PA_1:
            case WT_RF_STATE_RF_PA_2:
                Ret = SetSwitchVsgCTL3(m_RunData.RFPort, m_RunData.RFPortMode, WT_RF_STATE_OFF,
                                    static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "SetSwitchVsgCTL3 set ifg failed!");
                break;
            default:
                break;
            }
        }
    }

    m_RunData.XdmaStatus = WT_XDMA_INIT;
    m_RunData.Status = WT_RX_TX_STATUS_STOP;

    return Ret;
}


int DevVsg::GetStatus()
{
    int Status = 0;

    if (DrvCmd(VSG_GET_STATUS, sizeof(int), &Status) != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_RX_TX_STATUS_ERR_DONE, "VSG_GET_STATUS ioctl error");
        Status = WT_RX_TX_STATUS_ERR_DONE;
    }

    return Status;
}

int DevVsg::Down()
{
    int Ret = WT_OK;
    if (m_IsLoExist)
    {
        if (m_LOComMode == LO_IS_COM_MODE && m_BoardType != m_ShareLOBoardType && m_RunData.Status2 == WT_RX_TX_STATUS_DOWN)
        {
            Ret = DevLib::Instance().VSADown(m_ModId);
        }
        else if (m_TesterType == HW_WT418)
        {
            Ret = SetHMC833FreqPower(0, 0);
            RetWarnning(Ret, "VSADown SetHMC833FreqPower failed!");
            (Ret == WT_OK) ? Ret = WriteLMX2594(0, LMX2594POWERDOWN) : WriteLMX2594(0, LMX2594POWERDOWN);
        }
        else if (m_HwInfo.LoHwVersion >= VERSION_B)
        {
            Ret = WriteLMX2820(LoMix, 0, LMX2820POWERDOWN);
            Ret |= WriteLMX2820(LoMod, 0, LMX2820POWERDOWN);
        }
        else
        {
            Ret = SetMixFreqPower(0, 0);
            Ret |= SetModFreqPower(0, 0);
        }
        m_RunData.Freq = 0;
    }
    m_RunData.Status2 = WT_RX_TX_STATUS_DOWN;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSG" << m_ModId << "  Down!--------" << std::endl;
#endif
    return Ret;
}

int DevVsg::SetCalConfig(const Tx_Parm &TXParm)
{
    (void)TXParm;
    return WT_OK;
}

int DevVsg::SetBoostStatus(int Status)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "DevVsg::SetBoostStatus Status=%d\n", Status);
    int Ret = WT_OK;

    if (m_RunData.PaStatus != Status)
    {
        m_RunData.PaStatus = -1;
        Ret = DrvCmd(SET_RF_PA, sizeof(Status), &Status);
        RetAssert(Ret, "SetBoostStatus failed!");
        m_RunData.PaStatus = Status;
    }

    return WT_OK;
}

int DevVsg::SetFreqAndGain(const VSGConfigType &VSGConfig, Tx_Parm &TXParm)
{
    int Ret = WT_OK;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\nVSG Freq= " << VSGConfig.Freq << "\nFreqOffsetHz=" << VSGConfig.FreqOffsetHz << std::endl;
#endif
    // 设置RX链路频率并确认
    double Freq = VSGConfig.Freq + VSGConfig.FreqOffsetHz;
    Ret = SetFreqWithConfirm(Freq, &TXParm.freq_parm, static_cast<WT_DEVICE_MODE>(VSGConfig.DeviceMode));
    RetAssert(Ret, "SetFreqWithConfirm failed!");
    if (m_LOComMode == LO_IS_COM_MODE)
    {
        Freq_Parm freq_parm;
        Ret = wt_calibration_get_rx_lo_setting(m_ModId, Freq, &freq_parm, TXParm.share_mode);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_lo_setting failed!");
        //在共本振模式下,同时设置本振
        Ret = DevLib::Instance().SetFreqWithConfirm(m_ModId, DEV_TYPE_VSA, Freq, &freq_parm, static_cast<WT_DEVICE_MODE>(VSGConfig.DeviceMode));
        RetAssert(Ret, "SetFreqWithConfirm failed!");
    }
    if (m_IsRfExist)
    {
        m_GainParm = TXParm.tx_gain_parm;
        if (m_AttConfigMode == WT_ATT_CONFIG_MODE_CONFIG ||
            (m_AttConfigMode == WT_ATT_CONFIG_MODE_START && m_RunData.Status == WT_RX_TX_STATUS_RUNNING))
        {
            //设置TX链路增益
            Ret = SetGain(TXParm.tx_gain_parm);
            RetAssert(Ret, "SetTXGain failed!");
        }
    }
    return WT_OK;
}

int DevVsg::SetmGain(Tx_Gain_Parm TxGainParm)
{
    int Ret = WT_OK;

    m_GainParm = TxGainParm;

    return Ret;
}

int DevVsg::SetFreq(const double TXFreq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode)
{
    int Ret = WT_OK;
    int LoModDuplicateSet = 0;
    double FreqLoMix = 0;
    double FreqLoMod = 0;
    WT_RF_MOD_BAND_E BandMod = static_cast<WT_RF_MOD_BAND_E>(FreqParm->LoParm[LoMod].band);
    WT_RF_MIX_BAND_E BandMix = static_cast<WT_RF_MIX_BAND_E>(FreqParm->LoParm[LoMix].band);
    int IQSwap = 0;

    if (Basefun::CompareDouble(TXFreq, 0) == 0)
    {
        FreqLoMod = 0;
        FreqLoMix = 0;
    }
    else
    {
        FreqLoMod = fabs(FreqParm->LoParm[LoMod].freq);
        FreqLoMix = fabs(FreqParm->LoParm[LoMix].freq);
        IQSwap = (FreqParm->LoParm[LoMod].freq < 0) ? true : false;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setiosflags(std::ios::fixed) << std::setprecision(9);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX Freq=" << TXFreq << " WorkMode="<< WorkMode << " IQSwap=" << IQSwap << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMod Freq=" << FreqLoMod << " BandMod=" << BandMod << " power_level=" << FreqParm->LoParm[LoMod].power_level << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix Freq=" << FreqLoMix << " BandMix=" << BandMix << " power_level=" << FreqParm->LoParm[LoMix].power_level << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setiosflags(std::ios::fixed) << std::setprecision(4);
#endif

    if (m_IsRfExist)
    {
#if WT_TX_BAND_ON
        if ((Basefun::CompareDouble(m_RunData.Freq, TXFreq, 1e-12) != 0) ||
            m_RunData.CurrentBandMod != BandMod ||
            m_RunData.CurrentBandMix != BandMix ||
            m_RunData.CurrentWorkMode != WorkMode)
        {
            Ret = SetBand(FreqLoMod, FreqLoMix, BandMod, BandMix);
            RetAssert(Ret, "SetTXBand WT_RF_TX_BAND failed!");
            m_RunData.Freq = TXFreq;
            m_RunData.CurrentBandMod = BandMod;
            m_RunData.CurrentBandMix = BandMix;
            m_RunData.CurrentWorkMode = WorkMode;
        }
#endif
    }

    if (m_IsLoExist)
    {
#if WT_TX_FREQ_MANUAL
        if (m_BoardType != m_ShareLOBoardType && m_RunData.Status2 == WT_RX_TX_STATUS_DOWN && m_LOComMode == LO_IS_NOT_COM_MODE)
        {
            LoModInit();
            LoMixInit();
            m_RunData.Status2 = WT_RX_TX_STATUS_RUNNING;
        }

        if ((Basefun::CompareDouble(m_RunData.LOCurData[LoMix].Freq, FreqLoMix, 1e-12) != 0) ||
            (m_RunData.LOCurData[LoMix].PowerLevel != FreqParm->LoParm[LoMix].power_level))
        {
            Ret = SetMixFreqPower(FreqLoMix, FreqParm->LoParm[LoMix].power_level);
            RetAssert(Ret, "SetMixFreqPower LoMix failed!");
            LoModDuplicateSet = 1;
        }

        if (Basefun::CompareDouble(m_RunData.LOCurData[LoMod].Freq, FreqLoMod, 1e-12) != 0 ||
            m_RunData.LOCurData[LoMod].PowerLevel != FreqParm->LoParm[LoMod].power_level)
        {
            Ret = SetModFreqPower(FreqLoMod, FreqParm->LoParm[LoMod].power_level, IQSwap);
            RetAssert(Ret, "SetModFreqPower LoMod failed!");
        }

        LOConfigSave(LoMix, true, BandMix, FreqLoMix, FreqParm->LoParm[LoMix].power_level);
        LOConfigSave(LoMod, true, BandMod, FreqLoMod, FreqParm->LoParm[LoMod].power_level);

#if 0
        //先取消第二次配置LMX2594, 看看有没有问题，后续有问题再继续加上
        if (LoModDuplicateSet)
        {
            Ret = SetMixFreqPower(m_RunData.LOCurData[LoMix].Freq, m_RunData.LOCurData[LoMix].PowerLevel);
            RetAssert(Ret, "SetMixFreqPower LoMix failed!");
        }
#else
        (void)LoModDuplicateSet;
#endif
#endif
    }
    return Ret;
}

int DevVsg::SetFreqWithConfirm(const double Freq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode)
{
    std::unique_lock<std::mutex>  Lock(m_SetFreqMutex);
    int count = 0;
    LO_ID_E LOId;
    // 设置TX链路频率
    int Ret = SetFreq(Freq, FreqParm, WorkMode);
    RetWarnning(Ret, "SetTXFreq1 failed!");
    if (m_BoardType != m_ShareLOBoardType && m_LOComMode == LO_IS_COM_MODE)
    {
        return Ret; //共本振模式,非共享本振掉电。
    }
    else
    {
        while ((Ret = CheckLOStatus(Freq)) != WT_OK)
        {
            LOId = (Ret == WT_MIX_FREQ_IS_UNLOCKED ? LoMix : LoMod);
            if (count % 2 == 1) //掉电后重置LO
            {
                usleep(6000);
                Ret = ResetLO(LOId);
                RetWarnning(Ret, "ResetLO failed!");
                usleep(2000);
            }

            if (count++ == 3)
            {
                LoMixInit();
                usleep(2000);
                if (m_TesterType == HW_WT418 || m_HwInfo.LoHwVersion >= VERSION_B)
                {
                    LoModInit();
                }
                usleep(2000);
                Ret = SetFreq(Freq, FreqParm, WorkMode);
                RetWarnning(Ret, "SetTXFreq2 failed!");

                if ((Ret = CheckLOStatus(Freq)) != WT_OK)
                {
#if DEVLIB_DEBUG
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\n\n\n\n############SetTXFreq ResetLO count=" << count << "##################\n\n\n\n"
                        << std::endl;
#endif
                    return Ret;
                }
                break;
            }

            //重新设置TX链路频率
            Ret = SetFreq(Freq, FreqParm, WorkMode);
            RetWarnning(Ret, "SetTXFreq2 failed!");
        }
    }

    return Ret;
}

int DevVsg::SetBand(double ModFreq, double MixFreq, WT_RF_MOD_BAND_E BandMod, WT_RF_MIX_BAND_E BandMIX)
{
    int Ret = WT_OK;
    FreqBandType FreqBandTypeTemp;

    int LoMod = static_cast<int>(ModFreq + 0.5);
    int LoMix = static_cast<int>(MixFreq + 0.5);

    FreqBandTypeTemp.LoModBand = 0;
    FreqBandTypeTemp.LoMixBand = 0;
    FreqBandTypeTemp.BandMod = BandMod;
    FreqBandTypeTemp.BandMIX = BandMIX;

    int Channel = 0;
    for (auto &iter : m_RfModLoFreqChannel)
    {
        if (LoMod <= iter.EndFreq)
        {
            Channel = iter.Index;
            break;
        }
    }
    FreqBandTypeTemp.LoModBand = Channel;

    Channel = 0;
    for (auto &iter : m_RfMixLoFreqChannel)
    {
        if (LoMix <= iter.EndFreq)
        {
            Channel = iter.Index;
            break;
        }
    }
    FreqBandTypeTemp.LoMixBand = Channel;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetTXBand LoModBand=" << FreqBandTypeTemp.LoModBand << " LoMixBand=" << FreqBandTypeTemp.LoMixBand << " BandMod=" << BandMod << " BandMIX=" << BandMIX << std::endl;
#endif

    if ((Ret = DrvCmd(SET_TX_BAND, sizeof(FreqBandType), &FreqBandTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_TX_BAND ioctl error");
        return Ret;
    }

    return WT_OK;
}

// 设置输出功率，适用于VSG开启后调整功率与频率
int DevVsg::SetFreqOutputPower(int Freq, int Power)
{
    int Ret = WT_OK;

    Tx_Parm TXParm;
    //TXParm.unit = m_RunData.CurrentWorkMode;
    TXParm.rf_port = m_RunData.RFPort;
    TXParm.power = (double)Power / 100.0;   // 功率值转换
    TXParm.freq = (double)Freq * MHz;

    TXParm.unit = m_ModId % MOD_TYPE_COUNT;
    if (Is8080Master(m_RunData.CurrentWorkMode))
    {
        if (m_ModId % MOD_TYPE_COUNT == 0)
        {
            TXParm.unit = 2;    //单元ID1为主机，单元ID2为从机===>单元ID1校准数据
        }
        else
        {
            TXParm.unit = 5;    //单元ID1为从机，单元ID2为主机===>单元ID2校准数据
        }
    }
    if (Is8080Slave(m_RunData.CurrentWorkMode))
    {
        if (m_ModId % MOD_TYPE_COUNT == 1)
        {
            TXParm.unit = 3;    //单元ID1为主机，单元ID2为从机===>单元ID2校准数据
        }
        else
        {
            TXParm.unit = 4;    //单元ID1为从机，单元ID2为主机===>单元ID1校准数据
        }
      //  RFPort = WT_RF_PORT_OFF;
    }

    TXParm.unit = TXParm.unit + (m_ModId / MOD_TYPE_COUNT) * 6;

    // 校准8318斜率时，并不一定需要非常准确的精度，所以温度可以暂时给一个接近值.
    TXParm.sw_temperature = 30.0;
    TXParm.temperature = 30.0;
    TXParm.sample_freq = 240*MHz;
    TXParm.pac_mode = 0;
    Ret = wt_calibration_get_tx_setting(&TXParm);
    if (WT_OK != Ret)
    {
        return Ret;
    }

    VSGConfigType VSGConfig;
    VSGConfig.DeviceMode = m_RunData.CurrentWorkMode;
    VSGConfig.FreqOffsetHz = 0;
    VSGConfig.Power = TXParm.power;
    VSGConfig.Freq = TXParm.freq;

    Ret = SetFreqAndGain(VSGConfig, TXParm);
    if (WT_OK != Ret)
    {
        return Ret;
    }

    // 放大器
    Ret = SetBoostStatus(TXParm.tx_gain_parm.is_pa_on);
    if (WT_OK != Ret)
    {
        return Ret;
    }

    Ret = SetDacGain(TXParm.tx_gain_parm.dac_gain);
    if (WT_OK != Ret)
    {
        return Ret;
    }

    return Ret;
}

int DevVsg::SetGain(const Tx_Gain_Parm &TXGainParm)
{
    int Ret = WT_OK;
    // 开关板ATT设置
    if (m_BackBoard->GetSwbAttCacheCode(m_RunData.RFPort) != TXGainParm.tx_sw_gain.sw_tx_att_code[0])
    {
        Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, TXGainParm.tx_sw_gain.sw_tx_att_code[0]);
        RetAssert(Ret, "SetGain SetSwbAttCode failed!");
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Port" << m_RunData.RFPort << " BPAttCode =" << TXGainParm.tx_sw_gain.sw_tx_att_code[0] << std::endl;
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Port" << m_RunData.RFPort << " BPAttCode =" << TXGainParm.tx_sw_gain.sw_tx_att_code[0] << ",  Same as last. " << std::endl;
    }

    //使用校准数据设置衰减器code值
    for (unsigned i = 0; i < TX_ATT_MAX; i++)
    {
        if (m_RunData.ATTCurData[i] != TXGainParm.att_code[i])
        {
            Ret = SetATTCode(i, TXGainParm.att_code[i]);
            RetAssert(Ret, "SetTXGain SetATTCode failed!");
            m_RunData.ATTCurData[i] = TXGainParm.att_code[i];
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TXGainParm.ATT[" << i << "]=" << TXGainParm.att_code[i] << std::endl;
#endif
        }
        else
        {
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TXGainParm.ATT[" << i << "]=" << TXGainParm.att_code[i] << ",  Same as last. " << std::endl;
#endif
        }
    }
    return Ret;
}

int DevVsg::SaveData(int Index)
{
    std::ofstream PnData(WTConf::GetDir() + "/Vsg" + std::to_string(m_ModId) + "_PnData_" + std::to_string(Index) + ".csv",
                        std::fstream::out | std::fstream::trunc);
    short *Code = (short *)m_MapDmaBuf;
    for (int j = 0; j < (m_DmaDataSize / 4); j++)
    {
        PnData << Code[j * 2] << "," << Code[j * 2 + 1] << std::endl;
    }
    PnData.close();
    return WT_OK;
}

int DevVsg::SetPNHead(PnItemHead &PNHead)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(VSG_SET_PN_HEAD, sizeof(PnItemHead), &PNHead)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSGSetPNHead ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevVsg::GetPNHead(PnItemHead &PNHead)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(VSG_GET_PN_HEAD, sizeof(PnItemHead), &PNHead)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_GET_PN_HEAD ioctl error");
        return Ret;
    }

    return WT_OK;
}

//DAC模数转换器
int DevVsg::WriteDACReg(int Addr, const int Data)
{
    int Ret = WT_OK;
    RegType RegTypeTemp;
    RegTypeTemp.Addr = Addr;
    RegTypeTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_RF_ADC_DAC, sizeof(RegType), &RegTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_RF_ADC_DAC ioctl error");
        return Ret;
    }

    return WT_OK;
}

int DevVsg::ReadDACReg(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTypeTemp;
    RegTypeTemp.Addr = Addr;
    RegTypeTemp.Data = 0;

    if ((Ret = DrvCmd(READ_RF_ADC_DAC, sizeof(RegType), &RegTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_RF_ADC_DAC ioctl error");
        return Ret;
    }

    Data = RegTypeTemp.Data;

    return WT_OK;
}

int DevVsg::DACInit()
{
    int Ret = WT_OK;
    int RegData;
    int RegAddr;
    Json::Value Reg = m_JsonRoot["VSG_DAC_Init"];

    if (Reg.isArray() && Reg.size() > 0)
    {
        for (unsigned i = 0; i < Reg.size(); i++)
        {
            RegAddr = std::strtol(Reg[i]["Addr"].asString().c_str(), 0, 0);
            RegData = std::strtol(Reg[i]["Data"].asString().c_str(), 0, 0);

            Ret = WriteDACReg(RegAddr, RegData);
            RetAssert(Ret, "DACInit WriteDACReg failed!");

            if (RegAddr == 0x25 && RegData == 0x1)
            {
                int ReadData = 0;
                int ReadCnt = 0;
                do
                {
                    usleep(1);
                    ReadDACReg(0x25, ReadData);
                    ReadCnt++;
                } while ((ReadData & 0x2) == 0 && ReadCnt < 1000);
                WTLog::Instance().WriteLog(LOG_DEBUG, "Init Dac ModId=%d, Addr=%#x, Data=%#x, times=%d\n", m_ModId, 0x25, ReadData, ReadCnt);

                ReadCnt = 0;
                do
                {
                    usleep(1);
                    ReadDACReg(0x24, ReadData);
                    ReadCnt++;
                } while (ReadData != 0x33 && ReadData != 0x40 && ReadData != 0x41 && ReadCnt < 1000);
                WTLog::Instance().WriteLog(LOG_DEBUG, "Init Dac ModId=%d, Addr=%#x, Data=%#x, times=%d\n", m_ModId, 0x24, ReadData, ReadCnt);
            }
        }
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG ModId=" << m_ModId << " DACInit success!" << std::endl;
#endif
    }

    return WT_OK;
}

int DevVsg::GetDACVersion(int &DACVersion)
{
    return ReadDACReg(DAC_VERSION, DACVersion);
}

int DevVsg::SetITuneGain(int ICode)
{
    int Ret = WT_OK;
    int High;
    int Low;

    if (ICode > 1023)
    {
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetITuneGain ICode=" << ICode << std::endl;
#endif
        return WT_DATA_OVER_RANGE;
    }
    High = ICode / 256;
    Low = ICode % 256;

    Ret = WriteDACReg(IDAC_FS_ADJ1, High);
    RetAssert(Ret, "WriteDACReg failed!");
    Ret = WriteDACReg(IDAC_FS_ADJ0, Low);
    RetAssert(Ret, "WriteDACReg failed!");

    return WT_OK;
}

int DevVsg::SetQTuneGain(int QCode)
{
    int Ret = WT_OK;
    int High;
    int Low;

    if (QCode > 1023)
    {
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetQTuneGain ICode=" << QCode << std::endl;
#endif
        return WT_DATA_OVER_RANGE;
    }
    High = QCode / 256;
    Low = QCode % 256;

    Ret = WriteDACReg(QDAC_FS_ADJ1, High);
    RetAssert(Ret, "WriteDACReg failed!");
    Ret = WriteDACReg(QDAC_FS_ADJ0, Low);
    RetAssert(Ret, "WriteDACReg failed!");

    return WT_OK;
}

int DevVsg::SetDacGain(double &Remain)
{
    int Ret = WT_OK;
    int Code = 0;
    double CurrentOut = 0.0;
    double DCode = 0.0;
    double GCurrentI = 20.0025;              // TX I 通道电流的默认值
    double Ratio = 0.0;

    if (Remain > 2.0 || Remain < -2.0)
    {
        WTLog::Instance().LOGERR(WT_DAC_GAIN_OVER_RANGE, "SetDacGain Remain over range");
        return WT_DAC_GAIN_OVER_RANGE;
    }

    Remain = (Remain > 2.0) ? 2.0 : ((Remain < -2.0) ? -2.0 : Remain);

    Ratio = pow(10.0, Remain / -20.0);
    CurrentOut = GCurrentI / Ratio;
    DCode = (((double)10 * CurrentOut) / 1.2 - 72) * 16 / 3;
    Code = (int)(DCode + 0.5);
#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetDacGain Remain=" << Remain << "    Code=" << Code << std::endl;
#endif
    // code反推db增益
    // Remain = math.log10((20.0025 * 10) / (((3*(hi*256+lo))/16 + 72) * 1.2)) * -20.0
    // Remain in [-2, 2] ~ Code in [322, 735]
    // Remain in [-0.5, 0.5] ~ Code in [455, 558]
    // code 不能小于-384，math.log10 参数会为负异常

    Ret = SetITuneGain(Code);
    RetAssert(Ret, "SetDacGain SetITuneGain failed");

    Ret = SetQTuneGain(Code);
    RetAssert(Ret, "SetDacGain SetQTuneGain failed");

    return WT_OK;
}

int DevVsg::GetDacGain(double &Remain)
{
    int Ret = WT_OK;
    int High = 0;
    int Low = 0;

    // 假定,I路code 与 Q路code是一样的
    Ret = ReadDACReg(IDAC_FS_ADJ1, High);
    RetAssert(Ret, "ReadDACReg IDAC_FS_ADJ1 failed");
    Ret = ReadDACReg(IDAC_FS_ADJ0, Low);
    RetAssert(Ret, "ReadDACReg IDAC_FS_ADJ0 failed");

    double code = ((High * 256) | Low);
    double Ratio = (20.0025 * 10.0) / (((3.0 * code) / 16.0 + 72.0) * 1.2);
    Remain = log10(Ratio) * -20.0;

    return Ret;
}

int DevVsg::SetDacIQGainCode(int ICode, int QCode)
{
    int Ret = WT_OK;

    Ret = SetITuneGain(ICode);
    RetAssert(Ret, "SetDacIQGainCode I code failed!");

    Ret = SetQTuneGain(QCode);
    RetAssert(Ret, "SetDacIQGainCode Q code failed!");

    return WT_OK;
}

int DevVsg::SetIOffset(int IOffset)
{
    int MSB = 0;
    int LSB = 0;
    int MSBRead = 0;
    int LSBRead = 0;
    int Ret = WT_OK;

    if ((IOffset < -((1 << 15) - 1)) || (IOffset >((1 << 15) - 1)))
    {
        WTLog::Instance().LOGERR(WT_DATA_OVER_RANGE, "SetIOffset value over range!");
        return WT_DATA_OVER_RANGE;
    }

    MSB = IOffset >> 8;
    LSB = IOffset;

    Ret = WriteDACReg(IDAC_DC_OFFSET1, MSB);
    RetAssert(Ret, "WriteDACReg IDAC_DC_OFFSET MSB failed!");
    if(!m_ListVirtualCfgMode)
    {
        Ret = ReadDACReg(IDAC_DC_OFFSET1, MSBRead);
        RetWarnning(Ret, "ReadDACReg IDAC_DC_OFFSET MSB failed!");
        if ((MSB & 0xFF) != (MSBRead & 0xFF))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetIOffset error MSB=" << MSB << "  MSBRead=" << MSBRead << std::endl;
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "WriteDACReg IDAC_DC_OFFSET Read Back error");
            Ret = WriteDACReg(IDAC_DC_OFFSET1, MSB);
            RetAssert(Ret, "WriteDACReg IDAC_DC_OFFSET MSB failed!");
        }
    }

    Ret = WriteDACReg(IDAC_DC_OFFSET0, LSB);
    RetAssert(Ret, "WriteDACReg IDAC_DC_OFFSET LSB failed!");
    if(!m_ListVirtualCfgMode)
    {
        Ret = ReadDACReg(IDAC_DC_OFFSET0, LSBRead);
        RetWarnning(Ret, "ReadDACReg IDAC_DC_OFFSET LSB failed!");
        if ((LSB & 0xFF) != (LSBRead & 0xFF))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetIOffset error LSB=" << LSB << "  LSBRead=" << LSBRead << std::endl;
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "WriteDACReg IDAC_DC_OFFSET Read Back error");
            Ret = WriteDACReg(IDAC_DC_OFFSET0, LSB);
            RetAssert(Ret, "WriteDACReg IDAC_DC_OFFSET LSB failed!");
        }
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetIOffset IOffset=" << IOffset << " MSB=" << MSB << ",   LSB=" << LSB << std::endl;
#endif

    return WT_OK;
}

int DevVsg::SetQOffset(int QOffset)
{
    int MSB = 0;
    int LSB = 0;
    int MSBRead = 0;
    int LSBRead = 0;
    int Ret = WT_OK;

    if ((QOffset < -((1 << 15) - 1)) || (QOffset >((1 << 15) - 1)))
    {
        WTLog::Instance().LOGERR(WT_DATA_OVER_RANGE, "SetQOffset value over range!");
        return WT_DATA_OVER_RANGE;
    }

    MSB = QOffset >> 8;
    LSB = QOffset;

    Ret = WriteDACReg(QDAC_DC_OFFSET1, MSB);
    RetAssert(Ret, "WriteDACReg QDAC_DC_OFFSET MSB failed!");
    if(!m_ListVirtualCfgMode)
    {
        Ret = ReadDACReg(QDAC_DC_OFFSET1, MSBRead);
        RetWarnning(Ret, "ReadDACReg QDAC_DC_OFFSET MSB failed!");
        if ((MSB & 0xFF) != (MSBRead & 0xFF))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetQOffset error MSB=" << MSB << "  MSBRead=" << MSBRead << std::endl;
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "WriteDACReg QDAC_DC_OFFSET Read Back error");
            Ret = WriteDACReg(QDAC_DC_OFFSET1, MSB);
            RetAssert(Ret, "WriteDACReg QDAC_DC_OFFSET MSB failed!");
        }
    }

    Ret = WriteDACReg(QDAC_DC_OFFSET0, LSB);
    RetAssert(Ret, "WriteDACReg QDAC_DC_OFFSET LSB failed!");
    if(!m_ListVirtualCfgMode)
    {
        Ret = ReadDACReg(QDAC_DC_OFFSET0, LSBRead);
        RetWarnning(Ret, "ReadDACReg QDAC_DC_OFFSET LSB failed!");
        if ((LSB & 0xFF) != (LSBRead & 0xFF))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetQOffset error LSB=" << LSB << "  LSBRead=" << LSBRead << std::endl;
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "WriteDACReg QDAC_DC_OFFSET Read Back error");
            Ret = WriteDACReg(QDAC_DC_OFFSET0, LSB);
            RetAssert(Ret, "WriteDACReg QDAC_DC_OFFSET LSB failed!");
        }
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetQOffset  QOffset=" << QOffset << "  MSB=" << MSB << ",   LSB=" << LSB << std::endl;
#endif

    return WT_OK;
}

int DevVsg::SetTXDCOffset(int IOffset, int QOffset)
{
    int Ret = WT_OK;

    if (m_RunData.DcOffsetI != IOffset || m_RunData.DcOffsetI == CODE_NO_INIT)
    {
        m_RunData.DcOffsetI = CODE_NO_INIT;
    Ret = SetIOffset(IOffset);
    RetAssert(Ret, "SetTXDCOffset I Offset failed!");
        m_RunData.DcOffsetI = IOffset;
    }
    // else
    // {
    //     WTLog::Instance().WriteLog(LOG_DEBUG, "SetTXDCOffset IOffset=%d,  Same as last\n", IOffset);
    // }

    if (m_RunData.DcOffsetQ != QOffset || m_RunData.DcOffsetQ == CODE_NO_INIT)
    {
        m_RunData.DcOffsetQ = CODE_NO_INIT;
    Ret = SetQOffset(QOffset);
    RetAssert(Ret, "SetTXDCOffset Q Offset failed!");
        m_RunData.DcOffsetQ = QOffset;
    }
    // else
    // {
    //     WTLog::Instance().WriteLog(LOG_DEBUG, "SetTXDCOffset QOffset=%d,  Same as last\n", QOffset);
    // }

    return WT_OK;
}

int DevVsg::SetIfgCtrlEnable(int Enable)
{
    m_VsgGapPowerDebug = Enable;
    return WT_OK;
}

int DevVsg::SetIfgCtrlStatus(int Status)
{
    int Ret = WT_OK;
    VsgIfgCtrlType IfgCtrl;
    IfgCtrl.Status = Status;
    IfgCtrl.Param1 = m_ExtHwParam.IfgParam[0];
    IfgCtrl.Param2 = m_ExtHwParam.IfgParam[1];
    IfgCtrl.Param3 = m_ExtHwParam.IfgParam[2];

    if ((Ret = DrvCmd(VSG_SET_IFG_STATUS, sizeof(VsgIfgCtrlType), &IfgCtrl)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_SET_IFG_STATUS ioctl error");
        return Ret;
    }
    if(m_RunData.RFPortMode == WT_SW_STATE_MODE_BROADCAST)
    {
        for (int RFPort = WT_RF_1;RFPort < WT_RF_MAX; ++RFPort)
        {
            Ret = m_BackBoard->SwitchSetVsgIfgStatus(Status, m_Slot, RFPort);
            WTLog::Instance().WriteLog(LOG_DEBUG, "SetIfgCtrlStatus Port=%d, Status=%d\n", RFPort, Status);
        }
    }
    else
    {
        Ret = m_BackBoard->SwitchSetVsgIfgStatus(Status, m_Slot, m_RunData.RFPort);
        WTLog::Instance().WriteLog(LOG_DEBUG, "SetIfgCtrlStatus Port=%d, Status=%d\n", m_RunData.RFPort, Status);
    }
    return Ret;
}

int DevVsg::TBTStaStart(int DevMode)
{
    int Ret = WT_OK;

    if (IsSingleMode(DevMode))
    {
        if ((Ret = DrvCmd(VSG_START, 0, nullptr)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "VSG_START ioctl error");
            return Ret;
        }
    }
    else if (IsMasterMode(DevMode))
    {
        if ((Ret = DrvCmd(VSG_START_TBT_MIMO, 0, nullptr)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "VSG_START_TBT_MIMO ioctl error");
            return Ret;
        }
    }

    m_RunData.Status = WT_RX_TX_STATUS_RUNNING;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSG" << m_ModId << "  TBTSta Start!--------" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::SetTBTStaParam(TBTStaParamType StaConfig)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(VSG_SET_TBT_STA_PARAM, sizeof(TBTStaParamType), &StaConfig)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG_SET_TBT_STA_PARAM ioctl error");
        return Ret;
    }
    return Ret;
}

int DevVsg::SetDebugAtt(int Index, int Data)
{
    int Ret = WT_OK;
    if (Index == WT_DEV_RESPONSE_SW_ATT && Data >= 0 && Data <= ATT_CODE_MAX)
    {
        m_GainParmDebug.tx_sw_gain.sw_tx_att_code[0] = Data;
    }
    else if(Index == WT_DEV_RESPONSE_SYNC)
    {
        m_GainParmDebug = m_GainParm;
    }
    else if (Index == WT_DEV_RESPONSE_PA)
    {
        m_GainParmDebug.is_pa_on = Data ? true : false;
    }
    else if ((Index >= WT_DEV_RESPONSE_RF_ATT0 && Index < (WT_DEV_RESPONSE_RF_ATT0 + TX_ATT_COUNT)) &&
             Data >= 0 &&
             Data <= ATT_CODE_MAX)
    {
        m_GainParmDebug.att_code[Index - WT_DEV_RESPONSE_RF_ATT0] = Data;
    }
    else if ((Index >= WT_ATT_CAL_RF_ATT0 && Index < (WT_ATT_CAL_RF_ATT0 + TX_ATT_COUNT)) &&
             Data >= 0 &&
             Data <= ATT_CODE_MAX)
    {
        int ATTId = Index - WT_ATT_CAL_RF_ATT0;
        m_GainParm.att_code[ATTId] = Data;
        if (m_RunData.ATTCurData[ATTId] != Data)
        {
            Ret = SetATTCode(ATTId, Data);
            RetAssert(Ret, "SetDebugAtt SetATTCode failed!");
            m_RunData.ATTCurData[ATTId] = Data;
        }
    }
    else if (Index == WT_ATT_CAL_SW_ATT && Data >= 0 && Data <= BP_ATT_CODE_MAX)
    {
        m_GainParm.tx_sw_gain.sw_tx_att_code[0] = Data;
        if (m_BackBoard->GetSwbAttCacheCode(m_RunData.RFPort) != Data)
        {
            Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, Data);
            RetAssert(Ret, "SetDebugAtt SetSwbAttCode failed!");
        }
    }
    else
    {
        Ret = WT_ARG_ERROR;
    }
    return Ret;
}

int DevVsg::GetDebugAtt(int Index, int &Data)
{
    int Ret = WT_OK;
    if (Index == WT_DEV_RESPONSE_SW_ATT)
    {
        Data = m_GainParmDebug.tx_sw_gain.sw_tx_att_code[0];
    }
    else if (Index == WT_DEV_RESPONSE_PA)
    {
        Data = m_GainParmDebug.is_pa_on;
    }
    else if (Index >= WT_DEV_RESPONSE_RF_ATT0 && Index < (WT_DEV_RESPONSE_RF_ATT0 + TX_ATT_COUNT))
    {
        Data = m_GainParmDebug.att_code[Index - WT_DEV_RESPONSE_RF_ATT0];
    }
    else
    {
        Ret = WT_ARG_ERROR;
    }
    return Ret;
}

int DevVsg::SetSwitchVsgCTL3(int RFPort, int Mode, int State, WT_SB_CONFIG_TYPE_E SBConfigType)
{
    int Ret = WT_OK;
    int SwitchId = 0;
    int SubPort = 0;

    m_BackBoard->GetSwitchMap(m_ModId, RFPort, SwitchId, SubPort);
    State = (RFPort != WT_RF_OFF ? State : WT_RF_STATE_OFF);

    SwitchRFPortSetType RFPortSetTemp;
    RFPortSetTemp.SwitchId = SwitchId;
    RFPortSetTemp.SubPort = SubPort;
    RFPortSetTemp.SBConfigType = SBConfigType;
    RFPortSetTemp.Mode = Mode;
    RFPortSetTemp.State = State;

    if ((Ret = DrvCmd(SET_SWITCH_VSG_CTL3, sizeof(SwitchRFPortSetType), &RFPortSetTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_SWITCH_VSG_CTL3 ioctl error");
        return Ret;
    }

    return Ret;
}
int DevVsg::SetExtMode(ExtModeType ExtMode)
{
    int Ret = WT_OK;
    if (ExtMode.Mode == WT_VSG_MODE_DEVM)
    {
        ExtMode.Param.Devm.DebugDelay = m_ExtHwParam.DevmDelay;
    }

    if ((Ret = DrvCmd(SET_EXT_MODE, sizeof(ExtModeType), &ExtMode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetExtMode ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevVsg::SetAnalogIQSW(int &AnalogIQSW)
{
    int Ret = WT_OK;
    m_AnalogIQSW = AnalogIQSW;
    return Ret;
}

int DevVsg::GetAnalogIQSW(int &AnalogIQSW)
{
    int Ret = WT_OK;
    AnalogIQSW = m_AnalogIQSW;
    return Ret;
}

int DevVsg::SetCmVolt(double CmVolt)
{
    int Ret = WT_OK;
    if (CmVolt > 3.3||CmVolt<0)
    {
        return WT_AD5611_CODE_OVER_RANGE;
    }
    int Code = (int)((CmVolt / 3.3) * pow(2, 10));
    Ret = WriteAD5611(1, Code);  //片选： 1=基带

    return Ret;
}

int DevVsg::IQModeInit()
{
    int Ret = WT_OK;
    //初始化共本振模式
    Json::Value AnalogIQMode = m_JsonRoot["AnalogIQ"];
    int AnalogIQSW = AnalogIQMode["IQMode"].asUInt() == static_cast<int>(ANALOGIQ_MODE)
                      ? static_cast<int>(ANALOGIQ_MODE)
                      : static_cast<int>(RFIQ_MODE);
    Ret = SetAnalogIQSW(AnalogIQSW);
    return Ret;
}

int DevVsg::SetIfgCtrlMode(int Port, int Status)
{
    int Ret = WT_OK;
    SwitchIfgCtrlMode IfgCtrlMode;
    IfgCtrlMode.Port = Port;
    IfgCtrlMode.Status = Status;
    if ((Ret = DrvCmd(SWITCH_SET_VSG_IFG_CTRL_MODE, sizeof(IfgCtrlMode), &IfgCtrlMode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetExtMode ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevVsg::SetfastVsgFreq(const double Freq)
{
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    int Ret = WT_OK;
    Tx_Parm TXParm;

    if (m_RunData.Status != WT_RX_TX_STATUS_RUNNING)
    {
        return WT_OK;
    }

    else if(Basefun::CompareDouble(Freq, m_RunData.Freq) == 0)
    {
        return WT_OK;
    }
    else 
    {
        TXParm.rf_port = m_RunData.RFPort;
        TXParm.freq = Freq;
        TXParm.power = m_RunData.Power;
        TXParm.ex_iq_mode = false;
        TXParm.unit = m_ModId;
        TXParm.sample_freq = m_RunData.SamplingFreq;
        TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        TXParm.pac_mode = 0;

        // 获取TX链路综合校准数据
        Ret = wt_calibration_get_tx_setting(&TXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetfastVsgFreq freq = " << Freq << " TXParm.freq = " << TXParm.freq << " TXParm.power=" << TXParm.power << std::endl;
        int RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
        if (m_AnalogIQSW != ANALOGIQ_MODE)
        {
            /**************************开关板配置************************************/

#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetfastVsgFreq m_ModId = " << m_ModId << ", TXPort RF = " << m_RunData.RFPort
                  << ", State=" << RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
            // 校查开关板在位情况与该单元是否为80+80从机模式
            if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(m_RunData.RFPort, m_ModId)))
            {
                // 当RF端口改变或端口功能改变时重新设置RF端口
                if (m_RunData.RFPortState != RFPortState)
                {
#if DEVLIB_DEBUG
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetfastVsgFreq TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                    Ret = SetRFPort(m_RunData.RFPort, m_RunData.RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "SetfastVsgFreq VSG SetRFPort failed");
                }
            }
        }

        VSGConfigType VSGConfig;
        VSGConfig.DeviceMode = m_RunData.CurrentWorkMode;
        VSGConfig.FreqOffsetHz = 0;
        VSGConfig.Power = TXParm.power;
        VSGConfig.Freq = TXParm.freq;

        Ret = SetFreqAndGain(VSGConfig, TXParm);
        if (WT_OK != Ret)
        {
            return Ret;
        }

         // 放大器
        Ret = SetBoostStatus(TXParm.tx_gain_parm.is_pa_on);
        if (WT_OK != Ret)
        {
            return Ret;
        }

#if 0
        Ret = SetIfgCtrlStatus(m_RunData.IfgStatus);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetfastVsgFreq SetIfgCtrlStatus set ifg failed!");
#endif

        Ret = SetDacGain(TXParm.tx_gain_parm.dac_gain);
        if (WT_OK != Ret)
        {
            return Ret;
        }

    }

    m_RunData.Freq = Freq;
#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetfastVsgFreq fast vsg Freq Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::SetfastVsgPower(const double Power)
{
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    int Ret = WT_OK;
    Tx_Parm TXParm;

    if (m_RunData.Status != WT_RX_TX_STATUS_RUNNING)
    {
        return WT_OK;
    }
    else if(Basefun::CompareDouble(Power, m_RunData.Power) == 0)
    {
        return WT_OK;
    }
    else
    {
        TXParm.rf_port = m_RunData.RFPort;
        TXParm.freq = m_RunData.Freq;
        TXParm.power = Power;
        TXParm.ex_iq_mode = false;
        TXParm.pac_mode = false;
        TXParm.unit = m_ModId;
        TXParm.sample_freq = m_RunData.SamplingFreq;

        if (Is8080Master(m_RunData.CurrentWorkMode))
        {
            TXParm.unit_mode = CAL_UNIT_MODE_MASTER;
        }
        else if (Is8080Slave(m_RunData.CurrentWorkMode))
        {
            TXParm.unit_mode = CAL_UNIT_MODE_SLAVE;
        }
        else
        {
            TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        }

        // 获取TX链路综合校准数据
        if (DevLib::Instance().GetFullDuplexEnable())
        {
            wt_calibration_set_duplex_state(DUPLEX_GET_TX_PARAM);
        Ret = wt_calibration_get_tx_setting(&TXParm);
            wt_calibration_set_duplex_state(DUPLEX_CLOSE);
        }
        else
        {
            Ret = wt_calibration_get_tx_setting(&TXParm);
        }

        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_setting failed!");
        int RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;

        if (m_AnalogIQSW != ANALOGIQ_MODE)
        {
            /**************************开关板配置************************************/

#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", TXPort RF = " << m_RunData.RFPort
                      << ", State=" << RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
            // 校查开关板在位情况与该单元是否为80+80从机模式
            if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(m_RunData.RFPort, m_ModId)))
            {
                // 当RF端口改变或端口功能改变时重新设置RF端口
                if (m_RunData.RFPortState != RFPortState)
                {
#if DEVLIB_DEBUG
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                    Ret = SetRFPort(m_RunData.RFPort, m_RunData.RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "VSG SetRFPort failed");
                }
            }

            /**************************射频板配置************************************/
            // 配置TX链路上的增益、本振、波段开关
            // Ret = SetFreqAndGain(VSGConfig, TXParm);
            m_GainParm = TXParm.tx_gain_parm;
            if (m_IsRfExist)
            {
                // 设置TX链路增益
                Ret = SetGain(TXParm.tx_gain_parm);
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "SetTXGain failed!");
            }

            Ret = SetBoostStatus(m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK
                                     ? m_GainParmDebug.is_pa_on
                                     : m_GainParm.is_pa_on);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetBoostStatus failed!");

            Ret = SetIfgCtrlStatus(m_RunData.IfgStatus);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetIfgCtrlStatus set ifg failed!");
        }

        /**************************基带板配置************************************/
        // ATT步进误差补偿
        Ret = SetDacGain(TXParm.tx_gain_parm.dac_gain);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TXParm.tx_gain_parm.dac_gain=" << TXParm.tx_gain_parm.dac_gain << std::endl;
        RetAssert(Ret, "SetDacGain failed!");
    }

    m_RunData.Power = Power;
#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "fast vsg Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsg::StartList(int Mode, int CellMod)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(VSG_SET_LIST_CELL_MOD, sizeof(int), &CellMod)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "StartList ioctl VSG_SET_CELL_MOD error");
        return Ret;
    }

    if ((Ret = DrvCmd(VSG_START, sizeof(int), &Mode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "StartList ioctl error");
        return Ret;
    }

    if(m_ListMode)
    {
        m_ListModeNeedReset = 1;
    }
    if (Mode == WT_START_MODE_NORMAL)
    {
        m_RunData.XdmaStatus = WT_XDMA_INIT;
        m_RunData.Status = WT_RX_TX_STATUS_RUNNING;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSG" << m_ModId << "  Start!--------" << std::endl;
#endif
    return WT_OK;
}

// PN配置
int DevVsg::SetPNItem(std::vector<RfPnItem> &PnItemVector, std::vector<SegBehaviorType> SegRfPnItemVector)
{
    int Ret = WT_OK;

    if (m_MapDmaBuf == NULL)
    {
        return WT_ALLOC_FAILED;
    }

    if (SegRfPnItemVector.size() > SEGMENT_ITEM_MAX)
    {
        WTLog::Instance().LOGERR(WT_LIST_SEG_ITEM_COUNT_ERROR, "SetPNItemList param  SegBehaviorType count error");
        return WT_LIST_SEG_ITEM_COUNT_ERROR;
    }

#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    std::string DeviceName = Xdmafun::GetH2CDeviceName(m_ModId, DMA_CHANNEL_ID);
    int FpgaFd = open(DeviceName.c_str(), O_RDWR);
    if (FpgaFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }

    m_DmaDataSize = 0;
    do
    {
        ssize_t rc = 0;
        unsigned int BitOffset = 0;
        for (unsigned int i = 0; i < PnItemVector.size(); i++)
        {
            rc = 0;
            unsigned long &DDROffset = PnItemVector[i].DDRaddr;
            DDROffset = i * DMA_BUF_SIZE;
            unsigned int LenInBeat = (PnItemVector[i].Len / BEAT_LEN_UNIT_BYTE + ((PnItemVector[i].Len % BEAT_LEN_UNIT_BYTE) ? 1 : 0));
            unsigned int LastBeatValidPoint = (PnItemVector[i].Len % BEAT_LEN_UNIT_BYTE);
            //bit[0:0]:最后一拍(128bit)的第一个32bit有效，即最后一拍只有一个点是有效点(data beat[31:0])。last_beat_valid_point == 0001
            LastBeatValidPoint = ((LastBeatValidPoint == 0) ? 0x10 : LastBeatValidPoint);
            LastBeatValidPoint = GetBitMask(LastBeatValidPoint / 4);
            m_DmaDataSize = 0;
            BitOffset = 0;
            BeatInsert(m_MapDmaBuf + m_DmaDataSize, 0, BEAT_LEN_UNIT_BYTE, H2C_DATA_TYPE_VSG_ARB, 8, &BitOffset);
            BeatInsert(m_MapDmaBuf + m_DmaDataSize, 0, BEAT_LEN_UNIT_BYTE, i, 8, &BitOffset);
            BeatInsert(m_MapDmaBuf + m_DmaDataSize, 0, BEAT_LEN_UNIT_BYTE, DDROffset, 29, &BitOffset);
            BeatInsert(m_MapDmaBuf + m_DmaDataSize, 0, BEAT_LEN_UNIT_BYTE, LenInBeat, 28, &BitOffset);
            BeatInsert(m_MapDmaBuf + m_DmaDataSize, 0, BEAT_LEN_UNIT_BYTE, LastBeatValidPoint, 4, &BitOffset);
            m_DmaDataSize += BEAT_LEN_UNIT_BYTE;

            if(m_DmaDataSize + PnItemVector[i].Len <= DMA_BUF_SIZE)
            {
                memcpy((char *)m_MapDmaBuf + m_DmaDataSize, (void *)PnItemVector[i].Addr, (int)PnItemVector[i].Len);
                m_DmaDataSize += (int)PnItemVector[i].Len;
            }
            else
            {
                memcpy((char *)m_MapDmaBuf + m_DmaDataSize, (void *)PnItemVector[i].Addr, DMA_BUF_SIZE - m_DmaDataSize);
                m_DmaDataSize = DMA_BUF_SIZE;
            }
            //TODO:仿真数据
            ////////////////////////////////////////////////////////////////////////
            SaveDmaData(DMA_CHANNEL_ID, H2C_DATA_TYPE_VSG_ARB, m_MapDmaBuf, m_DmaDataSize);
            ////////////////////////////////////////////////////////////////////////
            rc += Xdmafun::Instance().WriteFromBuffer(DeviceName.c_str(), FpgaFd, m_MapDmaBuf, m_DmaDataSize, 0);
            WTLog::Instance().WriteLog(LOG_DEBUG, "VSG%d XDMA write size=%d, actual size=%ld\n", m_ModId, m_DmaDataSize, rc);
            DDROffset += m_DmaDataSize;
            if (rc < 0 || rc != m_DmaDataSize)
            {
                Ret = WT_XMDA_WR_ERROR;
                RetBreak(Ret, "SetPNItem write Xdma error");
            }
        }

        m_TrigParamDmaDataOffset = 0;
        BitOffset = 0;
        BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, H2C_DATA_TYPE_VSG_BEHAVIOR, 8, &BitOffset);
        m_TrigParamDmaDataOffset += BEAT_LEN_UNIT_BYTE;

        for (int k = 0; k < SegRfPnItemVector.size(); k++)
        {
            BitOffset = 0;
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, SegRfPnItemVector[k].PnIndex, 12, &BitOffset);
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, SegRfPnItemVector[k].PnRepeat ? 1 : 0, 16, &BitOffset);
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, (int)ceil((SegRfPnItemVector[k].PnDataSendLenCnt) / 4.0), 28, &BitOffset);//FPGA要求 点数/4
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, GetFPGAResampleIndex(SegRfPnItemVector[k].ReSampleRate), 4, &BitOffset);

            m_TrigParamDmaDataOffset += BEAT_LEN_UNIT_BYTE;
        }
        rc = 0;
        //TODO:仿真数据
        ////////////////////////////////////////////////////////////////////////
        SaveDmaData(DMA_CHANNEL_ID, H2C_DATA_TYPE_VSG_BEHAVIOR, m_TrigParamDmaBuf, m_TrigParamDmaDataOffset);
        ////////////////////////////////////////////////////////////////////////
        rc += Xdmafun::Instance().WriteFromBuffer(DeviceName.c_str(), FpgaFd, m_TrigParamDmaBuf, m_TrigParamDmaDataOffset, 0);
        WTLog::Instance().WriteLog(LOG_DEBUG, "VSG%d XDMA write size=%d, actual size=%ld\n", m_ModId, m_TrigParamDmaDataOffset, rc);

        if (rc < 0 || rc != m_TrigParamDmaDataOffset)
        {
            Ret = WT_XMDA_WR_ERROR;
            RetBreak(Ret, "SetPNItem write Xdma error");
        }
        unsigned int Temp = (SegRfPnItemVector[0].IFGCntLen / BEAT_LEN_UNIT_BYTE);
        if ((Ret = DrvCmd(VSG_SET_PN_LOOP_IFG, sizeof(Temp), &Temp)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "VSG_SET_PN_LOOP_IFG ioctl error");
            RetBreak(Ret, "SetPNItem write Xdma error");
        }
    } while (0);
    m_RunData.XdmaStatus = WT_XDMA_WR_FINISH;
    close(FpgaFd);
    // free(pPnItem);

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetPNItem Used Time:" << timeuse << "us" << std::endl;
#endif

    return Ret;
}

int DevVsg::ResetRundataFromCache()
{
    int Ret = WT_OK;
    Tx_Parm TXParm_Tmp;
    ModRunData ModRunData_Tmp;
    memcpy(&m_RunData, &ModRunData_Tmp, sizeof(ModRunData_Tmp));
    int code = 0;
    m_BackBoard->GetSwbAttCode(m_VSGConfigCache.RFPort, code, true);
    printf("ResetRundataFromCache GetSwbAttCode m_VSGConfigCache.RFPort=%d code=%d\n", m_VSGConfigCache.RFPort, code);

    if (DevLib::Instance().GetFullDuplexEnable())
    {
        wt_calibration_set_duplex_state(DUPLEX_GET_TX_PARAM);
    }

    Ret = SetParam2(m_VSGConfigCache, TXParm_Tmp);

    if (DevLib::Instance().GetFullDuplexEnable())
    {
        wt_calibration_set_duplex_state(DUPLEX_CLOSE);
    }

    return Ret;
}

int DevVsg::GetParamDmaBuf(int &offset)
{
    int Ret = WT_OK;
    m_ListVirtualCfgMode = false;
    if ((Ret = DrvCmd(VSG_GET_PARAM_DMA_BUF, sizeof(offset), &offset)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GetVSGParamDmaBuf ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevVsg::SetParamList(std::vector<VSGConfigType> &VSGConfigList, std::vector<Tx_Parm> TXParmList)
{
    int Ret = WT_OK;
    int RFPort = 0;
    int RFPortState = 0;
    int RFPortMode = WT_SW_STATE_MODE_SISO;
    unsigned int BitOffset = 31;
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif
    memcpy(&m_VSGConfigCache, &(VSGConfigList[0]), sizeof(VSGConfigType)); // 缓存临时数据
    m_TempHwParamDmaDataOffset = 0;
    InitVirtualAddr();
    for (int i = 0; i < VSGConfigList.size(); i++)
    {
        VSGConfigType &VSGConfig = VSGConfigList[i];
        Tx_Parm &TXParm = TXParmList[i];
        RFPort = VSGConfig.RFPort;
        RFPortState = VSGConfig.RFPortState;
        TXParm.rf_port = VSGConfig.RFPort;
        TXParm.freq = VSGConfig.Freq + VSGConfig.FreqOffsetHz;
        TXParm.power = VSGConfig.Power;
        TXParm.ex_iq_mode = false;
        TXParm.pac_mode = 0;
        TXParm.unit = m_ModId;
        TXParm.unit_mode = CAL_UNIT_MODE_SISO;
        TXParm.sample_freq = VSGConfig.SamplingRate;
        if (m_LOComMode == LO_IS_COM_MODE)
        {
            TXParm.share_mode = true;
        }
        else
        {
            TXParm.share_mode = false;
        }
        bool Is400M = false;
        if (!Basefun::CompareDoubleAccuracy1K(TXParm.freq, 400 * MHz))
        {
            Is400M = true;
            TXParm.freq += MHz;
        }

        // 获取TX链路综合校准数据
        Ret = wt_calibration_get_tx_setting(&TXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_setting failed!");
        RFPortState = TXParm.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
        //TODO:临时修改，listmode的信号文件不进行平坦度和IQ补偿。
        if(1)
        {
            TXParm.tx_iq_imb_parm.timeskew = 0;
            TXParm.tx_iq_imb_parm.gain_imb = 0;
            TXParm.tx_iq_imb_parm.quad_err = 0;
            TXParm.tx_spec_flat_comp_parm.bb_comp_len = 0;
            TXParm.tx_spec_flat_comp_parm.rf_comp_len = 0;
            TXParm.tx_iq_imb_parm_160m.timeskew = 0;
            TXParm.tx_iq_imb_parm_160m.gain_imb = 0;
            TXParm.tx_iq_imb_parm_160m.quad_err = 0;
            TXParm.tx_iq_imb_parm_320m.timeskew = 0;
            TXParm.tx_iq_imb_parm_320m.gain_imb = 0;
            TXParm.tx_iq_imb_parm_320m.quad_err = 0;
        }
        if (Is400M)
        {
            TXParm.freq -= MHz;
            TXParm.freq_parm.LoParm[LoMod].freq = TXParm.freq_parm.LoParm[LoMod].freq > 0
                                                      ? TXParm.freq_parm.LoParm[LoMod].freq - 1
                                                      : TXParm.freq_parm.LoParm[LoMod].freq + 1;
        }
        RecordlistSegConfig(RFPort);
        /**************************开关板配置************************************/
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", TXPort RF = " << RFPort
                                                     << ", State=" << RFPortState << ", Mode=" << RFPortMode << std::endl;
#endif
        // 校查开关板在位情况与该单元是否为80+80从机模式
        if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
        {
            // 当RF端口改变或端口功能改变时重新设置RF端口
            if ((m_RunData.RFPortState != RFPortState && m_RunData.RFPort != WT_RF_OFF) ||
                m_RunData.RFPortMode != RFPortMode ||
                m_RunData.RFPort != RFPort ||
                m_BackBoard->GetPortStatus(RFPort) != WT_RF_TX_STATUS)
            {
#if DEVLIB_DEBUG
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                Ret = SetRFPort(RFPort, RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "VSG SetRFPort failed");
            }
        }
        /**************************射频板配置************************************/
        // 配置TX链路上的增益、本振、波段开关
        Ret = SetFreqAndGain(VSGConfig, TXParm);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetTXFreqAndGain failed!");
        /**************************基带板配置************************************/
        // ATT步进误差补偿
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TXParm.tx_gain_parm.dac_gain=" << TXParm.tx_gain_parm.dac_gain << std::endl;
        Ret = SetDacGain(TXParm.tx_gain_parm.dac_gain);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetDacGain failed!");
        // 设置TX DC IQ offset
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(VSGConfig.DcOffsetI) << Pout(TXParm.tx_dc_offset_parm.i_code)
                                                     << Pout(VSGConfig.DcOffsetQ) << Pout(TXParm.tx_dc_offset_parm.q_code) << std::endl;
        Ret = SetTXDCOffset(CheckIQOffset(TXParm.tx_dc_offset_parm.i_code),
                            CheckIQOffset(TXParm.tx_dc_offset_parm.q_code));
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetTXDCOffset failed!");
#if WT_BOOST_MANUAL
        // 设置boost开关
        Ret = SetBoostStatus(TXParm.tx_gain_parm.is_pa_on);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
#endif
        Tx_Gain_Parm &GainParm = (m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
                                     ? m_GainParmDebug
                                     : m_GainParm;
        if (m_AttConfigMode == WT_ATT_CONFIG_MODE_START || m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
        {

            // 设置TX链路增益
            Ret = SetGain(GainParm);
            RetAssert(Ret, "SetGain failed!");
        }
        m_RunData.Power = VSGConfig.Power;
        switch (m_RunData.RFPortState)
        {
        case WT_RF_STATE_PI:
        case WT_RF_STATE_PA_1:
        case WT_RF_STATE_PA_2:
        case WT_RF_STATE_RF_PI:
        case WT_RF_STATE_RF_PA_1:
        case WT_RF_STATE_RF_PA_2:
            Ret = SetSwitchVsgCTL3(m_RunData.RFPort, m_RunData.RFPortMode, m_RunData.RFPortState,
                                static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetSwitchVsgCTL3 set ifg failed!");
            break;
        default:
            break;
        }
        m_RunData.IfgStatus = (m_VsgGapPowerDebug == GAP_POWER_DEBUG_ON || m_VsgGapPowerDebug == GAP_POWER_DEBUG_OFF)
                            ? m_VsgGapPowerDebug
                            : VSGConfig.VsgIfgStatus;
        if (m_TesterType == HW_WT418 && m_RunData.IfgStatus)
        {
            switch (m_RunData.RFPortState)
            {
            case WT_RF_STATE_PI:
            case WT_RF_STATE_RF_PI:
            case WT_RF_STATE_FULL_DUPLEX_PI:
                Ret = SetIfgCtrlMode(RFPort, true);
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "SetIfgCtrlMode failed!");
                break;
            case WT_RF_STATE_PA_1:
            case WT_RF_STATE_PA_2:
            case WT_RF_STATE_RF_PA_1:
            case WT_RF_STATE_RF_PA_2:
                Ret = SetIfgCtrlMode(RFPort, false);
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "SetIfgCtrlMode failed!");
            }
        }
        //暂时先一个seg，一个seg的dma搬运参数，后续调好后，需要把整个seq的所有seg组成一个大包，发给fpga
        GetParamDmaBuf(m_HwParamDmaDataOffset);
        if (i == (VSGConfigList.size()- 1))
        {
            //设置 seq lastseg flag为1
            BitOffset = 31;
            BeatInsert(m_HwParamDmaBuf, 0, BEAT_LEN_UNIT_BYTE, 1, 1, &BitOffset);
        }
        else
        {
            //设置seq lastseg flag为0
            BitOffset = 31;
            BeatInsert(m_HwParamDmaBuf, 0, BEAT_LEN_UNIT_BYTE, 1, 0, &BitOffset);
        }
        memcpy(m_TempHwParamDmaBuf + m_TempHwParamDmaDataOffset, m_HwParamDmaBuf, m_HwParamDmaDataOffset);//使用非mmap内存地址传输dma数据
        m_TempHwParamDmaDataOffset += m_HwParamDmaDataOffset;

        if (VSGConfig.WaveBw == WT_BW_160M)
        {
            memcpy(&TXParm.tx_iq_imb_parm, &TXParm.tx_iq_imb_parm_160m, sizeof(Tx_Iq_Imb_Parm));
        }
        else if (VSGConfig.WaveBw == WT_BW_320M)
        {
            memcpy(&TXParm.tx_iq_imb_parm, &TXParm.tx_iq_imb_parm_320m, sizeof(Tx_Iq_Imb_Parm));
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setprecision(4)
                                                        << " WaveBw =" << VSGConfig.WaveBw
                                                        << " gain_imb=" << TXParm.tx_iq_imb_parm.gain_imb
                                                        << " quad_err=" << TXParm.tx_iq_imb_parm.quad_err
                                                        << " timeskew=" << TXParm.tx_iq_imb_parm.timeskew
                                                        << std::endl;
        m_RunData.IfgStatus = (m_VsgGapPowerDebug == GAP_POWER_DEBUG_ON || m_VsgGapPowerDebug == GAP_POWER_DEBUG_OFF)
                            ? m_VsgGapPowerDebug
                            : VSGConfig.VsgIfgStatus;
    }
    ////////////////////////////////////////////////////////////////////////
    SaveDmaData(DMA_CHANNEL_ID, H2C_DATA_TYPE_VSG_HW_PARAM, m_TempHwParamDmaBuf, m_TempHwParamDmaDataOffset);
    ////////////////////////////////////////////////////////////////////////
    Ret = WriteDmaData(DMA_CHANNEL_ID, m_TempHwParamDmaBuf, m_TempHwParamDmaDataOffset);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "WriteDmaData failed!");

    Ret = SetIfgCtrlStatus(m_RunData.IfgStatus);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "SetIfgCtrlStatus set ifg failed!");

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSGSetConfig Used Time:" << timeuse << "us" << std::endl;
#endif
    return WT_OK;
}

int DevVsg::VSGSetTrig(const std::vector<VSGTriggerType> &TrigParam)
{
    int Ret = WT_OK;

    if (m_TrigParamDmaBuf == NULL)
    {
        return WT_ALLOC_FAILED;
    }

    std::string DeviceName = Xdmafun::GetH2CDeviceName(m_ModId, DMA_CHANNEL_ID);
    int FpgaFd = open(DeviceName.c_str(), O_RDWR);
    if (FpgaFd < 0)
    {
        // free(pPnItem);
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }
    do
    {
        unsigned int BitOffset = 0;
        memset(m_TrigParamDmaBuf, 0, m_TrigParamDmaDataOffset);
        m_TrigParamDmaDataOffset = 0;
        BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, H2C_DATA_TYPE_VSG_TRIGGER, 8, &BitOffset);
        m_TrigParamDmaDataOffset += BEAT_LEN_UNIT_BYTE;

        for(int i = 0; i < TrigParam.size(); i++)
        {
            BitOffset = 0;
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, TrigParam[i].TrigType, 4, &BitOffset);
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, (TrigParam[i].TrigOffsetCnt & GetBitMask(25)/*最高位为符号位,VSG固定为0*/), 25, &BitOffset);
            BeatInsert(m_TrigParamDmaBuf + m_TrigParamDmaDataOffset, 0, BEAT_LEN_UNIT_BYTE, TrigParam[i].SegmentDurationCnt, 28, &BitOffset);
            m_TrigParamDmaDataOffset += BEAT_LEN_UNIT_BYTE;
        }
        ssize_t rc = 0;
        //TODO:仿真数据
        ////////////////////////////////////////////////////////////////////////
        SaveDmaData(DMA_CHANNEL_ID, H2C_DATA_TYPE_VSG_TRIGGER, m_TrigParamDmaBuf, m_TrigParamDmaDataOffset);
        ////////////////////////////////////////////////////////////////////////
        rc += Xdmafun::Instance().WriteFromBuffer(DeviceName.c_str(), FpgaFd, m_TrigParamDmaBuf, m_TrigParamDmaDataOffset, 0);
        WTLog::Instance().WriteLog(LOG_DEBUG, "VSG%d XDMA write size=%d, actual size=%ld\n", m_ModId, m_TrigParamDmaDataOffset, rc);

        if (rc < 0 || rc != m_TrigParamDmaDataOffset)
        {
            Ret = WT_XMDA_WR_ERROR;
            RetBreak(Ret, "SetPNItem write Xdma error");
        }
        //修正RX Trigger模式,从IQV的模式枚举映射到硬件中的模式配置
        m_RunData.TrigType = TrigParam.back().TrigType;
    } while (0);
    m_RunData.XdmaStatus = WT_XDMA_WR_FINISH;
    close(FpgaFd);
    return Ret;
}

int DevVsg::VSGSetPNParamWithoutFile(unsigned long Addr, int Len, double SampleRate, double IFG, int Loop)
{
    int Ret = WT_OK;
    RfPnItem RfPnItemParam;
    std::vector<RfPnItem> RfPnItemVector;
    memset(&RfPnItemParam, 0, sizeof(RfPnItemParam));
    RfPnItemParam.Addr = Addr;
    RfPnItemParam.Len = Len;
    RfPnItemVector.push_back(RfPnItemParam);
    std::vector<SegBehaviorType> SegRfPnItemVector;
    SegBehaviorType SegRfPnItemParam;
    memset(&SegRfPnItemParam, 0, sizeof(SegRfPnItemParam));
    SegRfPnItemParam.PnIndex = 0;
    SegRfPnItemParam.PnDataLen = Len;
    SegRfPnItemParam.ReSampleRate = SampleRate;
    SegRfPnItemParam.PnRepeat = 1;
    SegRfPnItemParam.PnIfg = 0;
    SegRfPnItemParam.PnHead = 0;
    SegRfPnItemParam.PnTail = 0;
    SegRfPnItemParam.PnIfgCnt = 0;
    SegRfPnItemParam.PnHeadCnt = 0;
    SegRfPnItemParam.PnTailCnt = 0;
    SegRfPnItemParam.PnDataRepeat = 1;
    SegRfPnItemParam.PnDataExtendCnt = 0;
    SegRfPnItemVector.push_back(SegRfPnItemParam);
    Ret = SetPNItem(RfPnItemVector, SegRfPnItemVector);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSG VSGSetTrig failed");

    std::vector<VSGTriggerType> VSGTriggerVector;
    VSGTriggerType VSGTriggerParam;
    VSGTriggerParam.TrigType = WT_VSA_VSG_TRIG_TYPE_FREE_RUN;
    VSGTriggerParam.TrigOffsetCnt = IFG * SampleRate;
    VSGTriggerParam.SegmentDurationCnt = (Len / SampleRate) + IFG * SampleRate;
    VSGTriggerVector.push_back(VSGTriggerParam);
    // 配置VSG Trigger
    Ret = VSGSetTrig(VSGTriggerVector);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSG VSGSetTrig failed");
    unsigned long long TempTimeoutCnt = round(3 * SampleRate);
    CommonTrigType TrigComParam = {TempTimeoutCnt, 0, Loop};
    Ret = SetTrigComParam(TrigComParam);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSG VSGSetTrig failed");
    return Ret;
}



//TODO:分配DDR内存
// void DevVsg::GetPnFileDDRIndex(char *FilePath, int &DDRIndex, unsigned long &DDROffset)
// {
//     return ;
// }
