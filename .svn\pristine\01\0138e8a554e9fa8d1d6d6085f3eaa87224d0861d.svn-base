#include "vsav2.h"
#include "wtlog.h"
#include "vsa.h"
#include <sys/time.h>

WTVsaV2::WTVsaV2(const wtev::loop_ref &Loop)
    : WTBase(Loop, DEV_RES_VSA)
{
}

void WTVsaV2::MakeCalParam(double power)
{
    m_VsaParam.Freq = GetFreqHz();
    m_VsaParam.Freq2 = 0;
    m_VsaParam.FreqOffset = 0;
    m_VsaParam.RFPort = GetRFPort(); // WT_RF_1
    m_VsaParam.Type = TEST_SISO;
    m_VsaParam.MasterMode = 0;
    m_VsaParam.SignalId = 0;
    m_VsaParam.VsaMask = 0;
    m_VsaParam.TrigType = WT_TRIG_TYPE_FREE_RUN;
    m_VsaParam.AllocTimeout = 1; // 超时时间
    m_VsaParam.Is160M = 0;
    m_VsaParam.ExtGain = 0;
    m_VsaParam.Ampl = power;    // 参考电平待定
    m_VsaParam.ExtGain2 = 0.0;
    m_VsaParam.SamplingTime = 0.0001; // 采样时间待定
    m_VsaParam.SamplingFreq = MAX_SMAPLE_RATE;
    m_VsaParam.TrigPreTime = 0.00000; // 前置时间
    m_VsaParam.TrigTimeout = 0.2;
    m_VsaParam.TrigLevel = -31.0;
    m_VsaParam.MaxIFG = 0.1;
    m_VsaParam.RFPort2 = 0;
    m_VsaParam.DulPortMode = 0;
    m_VsaParam.Ampl2 = 0.0;
    m_VsaParam.TrigLevel2 = 0.0;
    m_VsaParam.Demode = 0;
    m_VsaParam.Resv = 0;
    memset(m_VsaParam.Reserved, 0, sizeof(m_VsaParam.Reserved));
}

int WTVsaV2::SetMod(double power)
{
    MakeCalParam(power);

    memset(&m_RxCalParam, 0, sizeof(Rx_Parm));
    VSAConfigType Config = TransConfig(m_VsaParam);
    //配置读取的信号长度
    std::vector<int>DataLenVetcor;
    DataLenVetcor.push_back(m_VsaParam.GetSigLen());
    int Ret = DevLib::Instance().VSASetWorkPointConfig(m_AllocModId, Config, m_RxCalParam);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetVSACaptureDataDeque failed");
        return Ret;
    }

    return DevLib::Instance().SetVSACaptureDataDeque(m_AllocModId, DataLenVetcor);
}

VSAConfigType WTVsaV2::TransConfig(const VsaParam &Param)
{
    VSAConfigType Config;

    Config.FreqOffsetHz = Param.FreqOffset;
    Config.BaseBandGain = 0;
    Config.SamplingTime = Param.SamplingTime;
    Config.SamplingFreq = Param.SamplingFreq;
    Config.TrigPreTime = Param.TrigPreTime;
    Config.RFPortState = WT_RF_STATE_PI;

    Config.TrigTimeout = Param.TrigTimeout;
    Config.TrigLevel = Param.TrigLevel;
    Config.RFPort = Param.RFPort;
    Config.TrigType = Param.TrigType;

    Config.DeviceMode = DEVICE_MODE_SISO;

    Config.Ampl = Param.Ampl - Param.ExtGain;
    Config.Freq = Param.Freq;
    Config.FrameTime = 6e-6;
    Config.NoiseCompensation = 0;

    Config.NoiseCompensation = 0;

    return Config;
}

int WTVsaV2::GetLoopState()
{
	// ?
    int LoopState = WT_RF_STATE_LOOP_PA_1;
    if (m_RxCalParam.rx_gain_parm.rx_sw_gain.sw_rx_link_state == WT_RF_STATE_PI)
    {
        LoopState = WT_RF_STATE_LOOP_PI;
    }

    return LoopState;
}

int WTVsaV2::Start()
{
    int Ret = DevLib::Instance().VSAStart(m_AllocModId);
    if (Ret != WT_OK)
    {
        SetModRunState(false);
    }
    else
    {
        SetModRunState(true);
    }

    return Ret;
}

int WTVsaV2::Stop(bool ForceSetSwitch)
{
    int Ret = 0;
    if (m_AllocModId != -1)
    {
        if (m_ModHasRun)
        {
            Ret = DevLib::Instance().VSAStop(m_AllocModId);
            SetModRunState(false);
        }
        if (ForceSetSwitch)
        {
            SetRFLinkState(WT_RF_STATE_OFF);
        }
       
    }

    return Ret;
}

int WTVsaV2::GetCaptureData(int ModId, DataBufInfo &DataInfo)
{
    int DateSize = m_VsaParam.GetSigLen();

    // 如果信号buffer长度小于信号数据大小则重新申请内存
    if (DataInfo.BufLen < DateSize || DataInfo.Buf == nullptr)
    {
        DataInfo.Buf.reset(new (std::nothrow) char[DateSize]);
        if (DataInfo.Buf == nullptr)
        {
            DataInfo.BufLen = 0;
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc signal buffer failed");
            return WT_ALLOC_FAILED;
        }

        DataInfo.BufLen = DateSize;
    }

    int Ret = DevLib::Instance().VSACaptureData(ModId, DataInfo.Buf.get(), DateSize);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get capture data failed");
        return Ret;
    }

    DataInfo.DataLen = DateSize;

    return WT_OK;
}

int WTVsaV2::SetAlzParam()
{
    int Ret = WT_OK;
    AlzParamComm ParamComm;
    Ret = m_Alg.SetAlzParam(WT_ALZ_PARAM_COMMON, &ParamComm, sizeof(AlzParamComm));
    if (Ret != WT_OK)
    {
        return Ret;
    }

    AlzParamFFT ParamFFT;
    return m_Alg.SetAlzParam(WT_ALZ_PARAM_FFT, &ParamFFT, sizeof(AlzParamFFT));
}

int WTVsaV2::AlzInCalPower(double &Power)
{
    int Ret = WT_OK;
    Ret = GetCaptureData(m_AllocModId, m_DataInfo);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    m_Alg.Clear();
    ExtendEVMStu ExtendEVM;
    m_Alg.SetData(m_DataInfo, 0, m_VsaParam, m_RxCalParam, ExtendEVM);

    bool HasFrame = 0;
    //double Power = -1000;
    double PeakPower = -1000;
    double MinPower = -1000;
    Ret = m_Alg.AlzPower(0, HasFrame, Power, MinPower, PeakPower);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    WTLog::Instance().WriteLog(LOG_SUB_MGR, "[WTVsaV2] AlzInCalPower rf=%d Freq=%d Ret=%d, Frame=%d, Power=%f, Peak=%f\n\n",
                               GetRFPort(),
                               GetFreqMHz(),
                               Ret,
                               HasFrame,
                               Power,
                               PeakPower);

    return Ret;
}
