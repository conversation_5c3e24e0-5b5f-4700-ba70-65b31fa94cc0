#message(STATUS "PROJECT_SOURCE_DIR: " ${PROJECT_SOURCE_DIR})
message(STATUS "CMAKE_CURRENT_SOURCE_DIR: " ${CMAKE_CURRENT_SOURCE_DIR})
message(STATUS "CMAKE_BUILD_TYPE: " ${CMAKE_BUILD_TYPE})

OPTION(TesterType "TesterType" WT448) # TesterType
set(SO_NAME "WT.Tester.API")

add_definitions("-DLINUX" "-DWTTESTER_DLL_EXPORTS")

execute_process(COMMAND sh ${CMAKE_CURRENT_SOURCE_DIR}/Includings.sh ${CMAKE_CURRENT_SOURCE_DIR})
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ../../extlib/include
	../../source/general
	../../source/general/devlib
	../../source/filesecure
	../../source/server
	../../source/server/analysis
)

if (CMAKE_BUILD_TYPE STREQUAL "")
    set(CMAKE_BUILD_TYPE "DEBUG")
else()
	string(TOUPPER ${CMAKE_BUILD_TYPE} CMAKE_BUILD_TYPE)
endif()


SET(CMAKE_CXX_FLAGS "$ENV{CXXFLAGS} -std=c++11 -MMD -MP")
if(StreamType STREQUAL "SISO")
    add_definitions("-DSISO_VER")
endif()

if(TesterType STREQUAL "WT428")
    add_definitions("-DWT428_FW")
elseif(TesterType STREQUAL "WT328CE")
    add_definitions("-DWT418_FW")
elseif(TesterType STREQUAL "WT418")
    add_definitions("-DWT418_FW")
endif()

if(StreamType STREQUAL "SISO")
    add_definitions("-DSISO_VER")
endif()
if(TesterType STREQUAL "WT428")
    add_definitions("-DWT428_FW")
endif()
add_definitions(
    "-W"
    "-fPIC"
    "-Wall"
    "-Wno-comment"
    "-Wno-sign-compare"
    "-Wno-unused-function"
    "-Wno-unused-variable"
    "-Wno-unused-parameter"
    "-Wno-unused-but-set-variable"
	"-Wuninitialized"
	"-fvisibility=hidden"
)

if (CMAKE_BUILD_TYPE STREQUAL "RELEASE")
    add_definitions("-O2")
else()
    add_definitions("-g" "-D_DEBUG" "-ggdb")
endif()


set(EXTRA_LIBS 
    pthread 
    m 
    WT.Tester.API.Common
    WT.Tester.API.IOControl
    WT.Tester.API.PNFileProcess
    WT.Tester.API.WT4XXWrapper
)

set(SRC_LIST
    tester.cpp
    TesterManager.cpp
)
link_directories(${PROJECT_BINARY_DIR}/lib) # 这行一定要在add_executable前面
add_library(${SO_NAME} SHARED ${SRC_LIST}) # lib的名字不能重复
target_link_libraries(${SO_NAME} ${EXTRA_LIBS})
set_target_properties(${SO_NAME} PROPERTIES OUTPUT_NAME ${SO_NAME})

# 设置动态库的版本号
#set_target_properties(${SO_NAME} PROPERTIES VERSION 1.5.0.42 SOVERSION 1)

set(LIBRARY_OUTPUT_PATH ${PROJECT_BINARY_DIR}/lib)

