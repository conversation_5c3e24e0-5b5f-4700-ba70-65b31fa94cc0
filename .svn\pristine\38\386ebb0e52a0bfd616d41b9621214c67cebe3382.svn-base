#!/bin/bash 

lspci -v > /home/<USER>/pciinfo.txt

backnum=`lspci -d 3333:1111 -vv | grep -P  'Region \d: Memory at [a-f0-9]+' | wc -l`
basenum=`lspci -d 3333:2222 -vv | grep -P  'Region \d: Memory at [a-f0-9]+' | wc -l`
echo $backnum $basenum

#开机时检测PCI设备正常，不正常则重启 
if [ $backnum -lt 3 ] || [ $basenum -lt 4 ]; then
	echo "reboot $backnum $basenum" >> /home/<USER>/pcireboot.txt
	cnt=`cat /home/<USER>/pcireboot.txt | wc -l`
	if [ $cnt -lt 3 ];then
	   reboot
	fi
elif [ -e "/home/<USER>/pcireboot.txt" ]; then
	rm /home/<USER>/pcireboot.txt
fi
