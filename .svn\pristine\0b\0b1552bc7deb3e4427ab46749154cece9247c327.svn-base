#ifndef ALG_3GPP_VSADEF_H_
#define ALG_3GPP_VSADEF_H_

#include "alg_3gpp_apidef.h"
#include "alg_3gpp_apidef_4g.h"
#include "alg_3gpp_apidef_5g.h"
#include "alg_3gpp_apidef_nbiot.h"
#include "alg_3gpp_apidef_wcdma.h"
#include "alg_3gpp_apidef_gsm.h"
#include "alg_3gpp_apidef_sidelink.h"

#define ALG_VSA_DATA_FORMAT_FLOAT64     0
#define ALG_VSA_DATA_FORMAT_INT16       1

#define ALG_VSA_MAX_FRM_CNT     50

#define ALG_VSA_ALZ_NORMAL  0
/* Only analyze power for AGC mode */
#define ALG_VSA_ALZ_POWER   1
#define ALG_VSA_ALZ_ATE     2

#define ALG_VSA_PKG_ALZ_SINGLE          0
#define ALG_VSA_PKG_ALZ_REPEAT          1

/**************************************************************************************************/
/*                                  Algorithm VSA Input Start                                     */
/**************************************************************************************************/

typedef struct {
    float FreqOffset;
    float UserMargin;
    float MixerLvOffset;

    int Reserved[61];
} Alg_3GPP_RFSetInfo;

typedef struct {
    int RFInChanNum;
    /* RF data */
    Alg_3GPP_RFInInfo RFInfo[ALG_3GPP_MAX_STREAM];
    /* High RF data: center frequency same as RFInfo */
    Alg_3GPP_RFInInfo HRFInfo[ALG_3GPP_MAX_STREAM];
    /* High RF data: center frequency - offset */
    Alg_3GPP_RFInInfo LHRFInfo[ALG_3GPP_MAX_STREAM];
    /* High RF data: center frequency + offset */
    Alg_3GPP_RFInInfo RHRFInfo[ALG_3GPP_MAX_STREAM];

    /* Follow WLAN lib: WT_GROUP_ENUM */
    /* Follow ALG_VSA_ALZ_NORMAL/ALG_VSA_ALZ_POWER/ALG_VSA_ALZ_ATE */
    int analyzeGroup;

    /* # Allocate physical memory for algorithm */
    int PhyMemSize;
    char *PhyMemPtr;

    int DcFreqCompensate;
    int SpectrumRBW; /* Unit:kHz */

    int PkgAlzMode; /* ALG_VSA_PKG_ALZ_SINGLE / ALG_VSA_PKG_ALZ_REPEAT*/
    int PkgAlzOffset;

    char MeasPowerGraph;
    char MeasSpectrum;
    char MeasCCDF;
    char AlignReserved; /* Reserved for memory align */

    int Reserved[253];

    Alg_3GPP_RFSetInfo RFSet;

    /* RF standard, reference ALG_3GPP_STANDARD_TYPE */
    int Standard;
    /* In analyze parameter */
    union {
        Alg_3GPP_AlzIn4g LTE;
        Alg_3GPP_AlzIn5g NR;
        Alg_3GPP_AlzInNBIOT NBIOT;
        Alg_3GPP_AlzInWCDMA WCDMA;
        Alg_3GPP_AlzInGSM GSM;
        Alg_3GPP_AlzInSL SL;
    };
} Alg_3GPP_VsaInInfo;

/**************************************************************************************************/
/*                                  Algorithm VSA Input End                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                  Algorithm VSA Output Start                                    */
/**************************************************************************************************/
typedef struct {
    unsigned int MagicValue;
    int TotalSize;
    char *MemPtr;
    /* Design for ATE */
    int RgLen;
    Complex *InRg;
} Alg_3GPP_AlgMemInfo;

typedef struct {
    unsigned int MagicValue;
    int TotalSize;
    int UsedSize;
    char *MemPtr;
} Alg_3GPP_PhyMemInfo;

typedef struct {
    int InfoFlg;
    int ccdf_len;
    double *frm_ccdf;                      //CCDF distribution in y-axis
    double *ccdf_mask;                     //CCDF mask following gaussian distribution 
    double frm_ccdf_start;                 //CCDF start power(dBr) in x-axis
    double frm_ccdf_end;                   //CCDF end power(dBr) in x-axis
    double frm_ccdf_scale;                 //scale(dBr)
    double ccdf_power[4];                  //10%,1%,0.1%,0.01% mapping power value
} Alg_3GPP_CCDFInfo;

typedef struct {
    int SpectFlg;

    /* Spectrum data */
    int spectral_all_cnt;
    Complex *spectral_all_dbr;
    int spectral_rbw;
    double obw_startfreq;
    double obw_endfreq;
    int obw_bw;

    /* Spectrum emission data */
    int SpectEmisLen;
    Complex *RxSpectEmis;
    int AddSpectEmisLen;
    Complex *AddSpectEmis;
    int *AddSpectEmisSeg;
    int EmisMaskLen;
    Complex *SpectEmisMask;
    int *EmisMaskSeg;
    Complex SemMargNeg[ALG_3GPP_SEM_LIM_SET];
    Complex SemMargPos[ALG_3GPP_SEM_LIM_SET];
	int badpointcnt;
    int SEMResult;

    /* Common data */
    int spectral_span;
    double rf_center_freq;
    double rf_start_freq;
    double rf_end_freq;
} Alg_3GPP_SpectralInfo;

/* For LTE, UTRA  NR buffer: 
 * E-UTRA2[L], E-UTRA1[L], UTRA2[L], UTRA1[L], 
 * band, UTRA1[R], UTRA2[R], E-UTRA1[R], E-UTRA2[R] */
typedef struct {
    int UtraFlg;
    double Power;
    double Mask;
    double AclRatio;
    Complex Freq;
} Alg_3GPP_UtraInfo;

typedef struct {
    int AclrFlg;

    /* E-UTRA for 4G, NR-UTRA for 5G */
    union {
        Alg_3GPP_UtraInfo Eutra[5];
        Alg_3GPP_UtraInfo NrUtra[3];
    };

    Alg_3GPP_UtraInfo Utra[5];

    int EutraResult;
    int UtraResult;
    int NrUtraResult;
    int AclrResult;
} Alg_3GPP_ACLRInfo;

typedef struct {
    int adc_freq;
    int capturecnt;
    Complex *capturedata;
    Complex *capturedata_mv;

    int SyncFlg;
    int SubfrmStart;
    int SubfrmEnd;

    /* Power */
    double *PwrdBm;
    double *PwrWinAvgdBm;
    double PkgAvgPwrdBm;
    double PkgPeakPwrdBm;
    double FrmAvgPwrdBm;
    double FrmPeakPwrdBm;
    /* Subcarrier average power */
    double SCAvgPwrdBm;

    Alg_3GPP_SpectralInfo Spectral;
    Alg_3GPP_ACLRInfo ACLR;
    Alg_3GPP_CCDFInfo CCDF;
} Alg_3GPP_RFOutInfo;

typedef struct {
    int SampleRate;
    int Capturecnt;
    Complex *Capturedata;
    
    int SyncFlg;
    int SubfrmStart;
    int SubfrmEnd;
} Alg_3GPP_BasebandInfo;

typedef struct {
    /* # Algothrim data memory. Not use for FW. */
    Alg_3GPP_AlgMemInfo AlgMem;
    /* # Output data memory. Not use for FW. */
    Alg_3GPP_PhyMemInfo OutMem;

    int StreamNum;
    Alg_3GPP_RFOutInfo RFOut[ALG_3GPP_MAX_STREAM];
    Alg_3GPP_BasebandInfo Basedband[ALG_3GPP_MAX_STREAM];

    /* RF standard, reference ALG_3GPP_STANDARD_TYPE */
    int Standard;
    union {
        Alg_3GPP_AlzOut4g LTE;
        Alg_3GPP_AlzOut5g NR;
        Alg_3GPP_AlzOutNBIOT NBIOT;
        Alg_3GPP_AlzOutWCDMA WCDMA;
        Alg_3GPP_AlzOutGSM GSM;
        Alg_3GPP_AlzOutSL SL;
    };
} Alg_3GPP_VsaOutInfo;

/**************************************************************************************************/
/*                                  Algorithm VSA Output End                                      */
/**************************************************************************************************/

#endif /* ALG_3GPP_VSADEF_H_ */