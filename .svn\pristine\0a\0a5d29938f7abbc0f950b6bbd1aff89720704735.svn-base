/*
 * basehead.h
 *
 *  Created on: 2019-3-18
 *      Author: Administrator
 */

#ifndef BASEHEAD_H_
#define BASEHEAD_H_

#include "tester.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <memory>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <jsoncpp/json/json.h>
#include <thread>
#include <functional>
#include <future>
#include <sys/time.h>

#include "basetools.h"
#include "wtlog.h"

class ListSeq;


using namespace std;


#define IF_BREAK(x)     if(0 != x){\
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" <<__FUNCTION__ << "," << __LINE__ << "] error: " << x << endl; \
    break;\
}

#define EMPTY_PARAM_ERROR(x)    if(0 >= x->parser_state.numberOfParameters) {\
    SCPI_ErrorPush(x, SCPI_ERROR_MISSING_PARAMETER); \
    return SCPI_RES_ERR;\
}

#define ILLEGAL_PARAM_RETURN(x)	if(x){ \
	SCPI_ErrorPush(context, SCPI_ERROR_ILLEGAL_PARAMETER_VALUE);\
	return SCPI_RES_ERR;\
}

#define IF_ERR_RETURN(x)	if((x) != WT_ERR_CODE_OK)	{\
	SCPI_ErrorPush(context, (x));\
	return SCPI_RES_ERR;\
}

#define PoutN(n)                 #n<<"="<<n<<"\n"
#define Pout(n)                  #n<<"="<<n<<","

//采样率选项枚举
enum WT_SAMPLE_RATE_MODE
{
    RATE_DEFAULT_API,   //0根据具体业务设定固定值，eg:BT/zigbee 为120M，其他都为240M(TODO:待完善)
    RATE_7_5M_API,      //1, 7.5M
    RATE_15M_API,       //2, 15M
    RATE_30M_API,       //3, 30M
    RATE_60M_API,       //4, 60M
    RATE_120M_API,      //5, 120M
    RATE_240M_API,      //6, 240M
    RATE_480M_API,      //7, 480M
    RATE_640M_API,      //8, 640M
};


//TB-TF 类型
enum TESTER_RUN_MODE_TYPE
{
    TESTER_RUN_NOMAL = 0,     /*正常模式*/
    TESTER_RUN_DIGIT_IQ = 1,  /*数字IQ模式*/
};

enum TESTER_DIG_MODE_TYPE
{
    TESTER_DIG_NOMAL = 0,     /*正常模式*/
    TESTER_DIG_CHANNEL = 1,  /*数字IQ单小区多通道模式*/
};

enum TESTER_LINK_MODE
{
    TESTER_AS_NOMAL_LINK = 0,   /*正常模式*/
    TF_TB_TESTER_AS_AP = 1,     /*TB-TF模式，仪器作为AP*/
    TF_TB_TESTER_AS_STA = 2,    /*TB-TF模式，仪器作为STA*/
};

enum TESTER_BUSI_START_MODE
{
    WT_SYNC_START = 0,  /*同步启动模式*/
    WT_ASYNC_START = 1, /*异步启动模式*/
};

// SCPI和WT-LINK层的连接类型
enum TESTER_LINK_TYPE
{
    LINK_TYPE_INVALID = 0,  /*未建立连接*/
    LINK_TYPE_NORMAL,       /*普通连接*/
    LINK_TYPE_MANAGER,      /*管理连接*/
    LINK_TYPE_QUERY,        /*查询连接*/
    LINK_TYPE_FORCE,        /*强制连接*/
    LINK_TYPE_SUB,          /*子连接*/
    LINK_TYPE_MONITOR,      /*监听连接*/
    LINK_TYPE_DIAGNOSIS,    /*诊断连接*/
};
#define POWER_CORRECTION_TABLE  "/tmp/Table/"
#define TESTER_CONFIG_FILE      "tester.json"
enum THREAD_STATUS
{
    THREAD_IDEL,
    THREAD_RUNNING,
    THREAD_STOPPED
};
#define MAX_A_MPDU_CNT    512

class TimeTick
{
public:
    TimeTick(std::string msg = "");
    ~TimeTick();
private:
    std::string m_info;
    struct timeval m_start;
    struct timeval m_ends;
};


class UserDefindPSDU
{
public:
    UserDefindPSDU();
    void reset();
public:
    int SegmentID;                          ///< 段ID号
    int RUID;                               ///< RU ID号
    int UserID;                             ///< RU下的User ID号
    int DataLen;                            ///< 数据长度，单位bytes
    unique_ptr<char[]>Data;
};

class UserPSDU
{
public:
    UserPSDU();
    unsigned int GetMaxSegment() const { return BE_MAX_SEGMENT; }

public:
    WIFI_PSDU PSDU[BE_MAX_SEGMENT][BE_RU_COUNT][MUMIMO_8_USER];
    UserDefindPSDU PSDU_User[BE_MAX_SEGMENT][BE_RU_COUNT][MUMIMO_8_USER];
};

class UserDefinedDataClass
{
public:
    unique_ptr<char[]> UserDefineData = nullptr;
    unsigned int UserDefineDataLen = 0;
};

class OFDMA_RU
{
public:
    int RUIndex[BE_RU_COUNT];
    std::vector<int>ToneUserCnt;
public:
    OFDMA_RU();
};

#define MAX_CALIBRTION_MODE     4
class CalibrationStruct
{
public:
    int mode[MAX_CALIBRTION_MODE] = {0, 1, 2, 3};
    int mode_enable[MAX_CALIBRTION_MODE] = {0, 0, 0, 0};
    int reload = 0;
    int flatnessEnable = 1;
public:
    void Reset();
};

class SCPI_AlzParam
{
public:
    AlzParamComm commonAnalyzeParam;
    AlzParamWifi analyzeParamWifi;
    AlzParamBT analyzeParamBt;
    AlzParamZigBee analyzeParamZigBee;
    AlzParamFFT analyzeParamFft;
    AlzParamSparkLink analyzeParamSparkLink;
    AlzParam3GPP analyzeParam3GPP;
    AlzParamWiSun analyzeParamWiSun;
    AlzParamZwave analyzeParamZWave;
    double timeOut;
public:
    // 所有协议标准的默认构造函数
    SCPI_AlzParam();
    /**
     * @brief 重置所有协议标准的分析参数
     *  实例化了两个对象 vsgAlzParam 和 vsaAlzParam
     */
    void Reset();
    void Reset_AlzParam(AlzParamWifi *param);
    void Reset_AlzParam(AlzParamBT *param);
    void Reset_AlzParam(AlzParamZigBee *param);
    void Reset_AlzParam(AlzParamFFT *param);
    void Reset_AlzParam(AlzParamComm *param);
    void Reset_AlzParam(AlzParamSparkLink *param);
    void Reset_AlzParam(AlzParam3GPP &param,
                        const int standard = ALG_3GPP_STD_4G,
                        const int linkDirect = ALG_3GPP_UL);
    void Reset_AlzParam(AlzParamZwave *param);
    void Reset_AlzParam(AlzParamWiSun *param);
};

enum WEP_SECURITY_TYPE
{
    WEP_40,
    WEP_104,
    WEP_128,
};

enum SECURITY_FORMAT
{
    ASCII_FORMAT,
    HEX_FORMAT,
};

#define WT_MAC_ENCRYPT_KEY_WPA_PWD "WPA-PWD"
#define WT_MAC_ENCRYPT_KEY_WPA_PSK "WPA-PSK"
#define WT_MAC_ENCRYPT_KEY_WPA_TK "WPA-TK"

#define WT_MAC_ENCRYPT_KEY_WAPI_PWD "WAPI-PWD"
#define WT_MAC_ENCRYPT_KEY_WAPI_BK "WAPI-BK"
#define WT_MAC_ENCRYPT_KEY_WAPI_TK "WAPI-TK"

typedef struct {
    bool valid;
    std::unique_ptr<u8[]>value;
    u32 value_len; // byte length
}LitePointResult;

enum LITE_ENUM{
    LITE_ENUM_Power,
    LITE_ENUM_AvgPower,
    LITE_ENUM_IQ,

    LITE_VSG_ENUM_Power,
    LITE_VSG_ENUM_AvgPower,
    LITE_VSG_ENUM_IQ,

    LITE_ENUM_MAX
};
enum TESTMODE
{
    TESTMODE_STANDARD,
    TESTMODE_RD,
};

class ScpiTimer
{
public:
    void ClearTimer(std::string TimerName);
    void StartTimer(std::string TimerName);
    void StopTimer(std::string TimerName);
    void SetTimerEnable(bool Enable) { m_Enable = Enable; };
    string GetCommandUsedTime();

private:
    bool NeedRecord(std::string TimerName);
    std::map<std::string, timeval[2]> m_Timer;
    bool m_Enable = false;
    string m_text;
};

//频点线衰结构体
struct FreqPathLos {
    double Freq;
    double PathLos;
};

class SPCIUserParam
{
public:
    SPCIUserParam();
    ~SPCIUserParam();
    int Connect(int Type = LINK_TYPE_NORMAL);
    int CheckConnectStatus(bool TestConnect);
    void InitMem();
    void Reset();
    void Reset_VsaVsgParam();
    void ResetWaveGenParam();
    bool CheckBusinessLic(WT_PROT_E_API LicValue); //检验lic有效性，有效返回true，无效返回false
    void Reset_LiteResult(int start = 0, int end = LITE_ENUM::LITE_ENUM_MAX);
    int GetMoniPort(int MoniVsg = false);
public:
    int m_TestMode = TESTMODE_RD;                        //分为0:普通模式和 1:高级模式
    int ClientFd = -1;
    int ConnID = 0 ;
    int AlzFrameID = 1;
    int VsaSampleRateMode = RATE_DEFAULT_API;  //见枚举WT_SAMPLE_RATE_MODE
    int VsgSampleRateMode = RATE_DEFAULT_API;  //见枚举WT_SAMPLE_RATE_MODE
    int VsgDemode = WT_DEMOD_11AG;
    int mSpectrumWideMode = 0;     //WIFI spectrum wide mode,0:off;1:1G;2:auto
    int TesterLinkMode = TESTER_AS_NOMAL_LINK;
    int TesterLinkType = LINK_TYPE_INVALID;
    int TesterMajorMode = TESTER_RUN_NOMAL;
    char m_CMIMORefFile[256];
    std::unique_ptr<ListSeq> m_List; 
    VsaParameter vsaParam;
    SCPI_AlzParam vsaAlzParam; // vsa分析参数
    VsaAvgParameter avgParam;
    VsaTrigParam vsaTrigParam;
    int MinAvgCount = 1;

    int NeedSetVSGPattern = true;        //在上位机不更改PN数据时，不执行SetVSGPattern操作
    VsgParameter vsgParam;
    VsgWaveParameter waveParam;
    vector<VsgPattern> vsgPattern;         //改变vsgPattern时必须设置NeedSetVSGPattern为true,目前还没办法检测vsgPattern是否被重新赋值。
    SCPI_AlzParam vsgAlzParam; // vsg分析参数

    //子网口
    VirtualNetType SubNetConfig;
    //8080 vsa双端口
    ExtendVsaParameter vsaExtParam;
    //8080 vsg双端口
    ExtendVsgParameter vsgExtParam;
    //TB分析参数
    AlzParamAxTriggerBase tbAnanlyzeParam;
    //Az相关分析参数
    AlzParam11az m_AzAlyParam;

    //PAC
    PacAttribute mPacAttribute;
    PacParameter mPacParam;
    vector<double>mPacFreqList;
    //普通SCPI命令是否返回
    int m_ScpiResponse = 0;


    std::array<std::vector<FreqPathLos>, WT_SUB_TESTER_INDEX_MAX> m_VsaFreqPathLosTbl;
    std::array<std::vector<FreqPathLos>, WT_SUB_TESTER_INDEX_MAX> m_VsgFreqPathLosTbl;

    std::array<string, WT_SUB_TESTER_INDEX_MAX> m_vsaAutoPowerCorrectFileName;
    std::array<string, WT_SUB_TESTER_INDEX_MAX> m_vsgAutoPowerCorrectFileName;

    std::array<Json::Value, WT_SUB_TESTER_INDEX_MAX> m_VSAPowerCorrectionJson;
    std::array<Json::Value, WT_SUB_TESTER_INDEX_MAX> m_VSGPowerCorrectionJson;

    CalibrationStruct m_CalSetting;
    FemParameter m_Devm;

    //TF-TB
    InterBindParameter m_InterParam;
    vector<double> m_FrameDelay;

    //数字IQ
    DigtalIQParam m_DigtalIQParam;
    DigtalIQTestFixture m_DigtalIQTestFixture;
    int WaveGenDemod = WT_DEMOD_11AG;
    std::future<int> m_vsaThread;
    std::atomic_int m_VsaThreadStatus;
    std::atomic_int m_VsaStartMode;

    MutiPNExtendInfo m_MutiPNExtendInfo;
    vector<string> m_MutiPNFilesName;
    vector<string> m_MutiPNDstFileName;
    vector<int> m_MutiPNFileStreamCnt;
    vector<MutiPNUserGenParam> m_MutiPNUserGenParam;
    unique_ptr<GenWaveWifiStruct_API> PnWifi = nullptr;
    unique_ptr<OFDMA_RU>PnRU = nullptr;
    unique_ptr<UserPSDU>PnPSDU = nullptr;
    unique_ptr<TriggerFrameSetting>ptrTf = nullptr;
    unique_ptr<GenWaveBtStructV2> PnBt = nullptr;
    unique_ptr<GenWaveCwStruct> PnCW = nullptr;
    unique_ptr<GenWaveGleStruct> PnGLE = nullptr;
    unique_ptr<Alg_3GPP_WaveGenType> Pn3GPP = nullptr;
    unique_ptr<GenWaveWisunStruct> PnWiSun = nullptr;
    std::map<int, UserDefinedDataClass> WaveUserDefineExtendParam;

    LitePointResult m_litePoint[LITE_ENUM_MAX][MAX_DF_NUM][MAX_SEGMENT];

    const Json::Value *JsonRoot = nullptr;
    int IsMonObj = false;
    struct timeval m_ConnetTestTime;

    //wep 解密相关配置
    struct WEP_Setting
    {
        int SecutityFormat = ASCII_FORMAT;   //key的格式，ASCII或者16进制
        std::string PassPhrase = "";         //实际的key字符串，不同格式下长度不一样
    };

    //TKIP\CCMP\GCMP解密相关配置
    struct TK_Setting
    {
        std::string KeyTypeName = WT_MAC_ENCRYPT_KEY_WPA_PWD;    //密码类型，有三种：支持三种密钥格式：WPA-PWD，WPA-PSK，WPA-TK（the temporal key）
        //notice:这三种密钥有关联，可由WPA_PWD推导出WPA_PSK，WPA_PSK推导出WPA-TK，最终解密时都是用WPA-TK去解
        std::string SSID = "";      //1-32
        std::string PassPhrase = "";
        std::string ANonce = "";    //16进制的ASCII码字符串,最大32byte
        std::string SNonce = "";
        std::string Psk = "";
        std::string TemporalKey = "";
        int AmsduCapable = 0;    //取值范围：0~1，仅在CCMP/GCMP下有效
    };

    //WAPI解密相关配置
    struct WAPI_Setting
    {
        std::string KeyTypeName = WT_MAC_ENCRYPT_KEY_WAPI_PWD;    //密码类型，有三种：支持三种密钥格式：WAPI-PWD，WAPI-BK，WAPI-TK（the temporal key）
        std::string PassPhrase = "";
        std::string N1 = "";
        std::string N2 = "";
        std::string BK = "";
        std::string TemporalKey = "";
        int AADVersion = 2006;          //2006/2020
        int AADFrameCtlHTC = 0;         //0-1
        int AADAmsduCapableField = 0;   //0-1,仅在AADVersion为2020时该值有效
    };

    struct SecuritySetting
    {
        std::string SecurityAlgorithm = "NONE";      //加密方式，见枚举SECURITYMODE
        WEP_Setting WepSet;
        TK_Setting TkSet;
        WAPI_Setting WapiSet;
    };
    SecuritySetting MacDecryptionSetting;

    //开启平均功能时，辅助验证功能配置，用于保存多次采集的原始数据
    using TestSaveVsaAvgWave = struct
    {
        int enable = 0;                 //是否开启保存每次分析的原始数据
        int file_type = 0;              //保存数据文件类型，0，表示.bwv；1表示.csv
        int file_count = 10;            //保存文件的最大数量，默认给10
    };
    TestSaveVsaAvgWave m_save_avg_wave;
    std::vector<u8> m_authentication_tk;
    ScpiTimer m_WTTimer; //SCPI命令计时
    int m_fullDuplexEnable = 0;
private:
    unique_ptr<char[]> LicInfos = nullptr;//用于保存通过api获取的仪器lic项
    int LicItemCnt = 0;
};

#endif /* BASEHEAD_H_ */
