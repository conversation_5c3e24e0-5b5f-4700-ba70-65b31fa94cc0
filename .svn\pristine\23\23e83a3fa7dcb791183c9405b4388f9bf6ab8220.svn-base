#include "includeall.h"


bool InstrumentHandle::isSameVsgParam(VsgParameter *param)
{
    if (m_VsgParamUpdate)
    {
        m_VsgParamUpdate = false;
        return false;
    }
    return (
        (0 == memcmp(m_vsgParam.RfPort, param->RfPort, sizeof(m_vsgParam.RfPort)))
        && (0 == memcmp(m_vsgParam.VsgUnitMask, param->VsgUnitMask, sizeof(m_vsgParam.VsgUnitMask)))
        && (0 == memcmp(&m_vsgParam.ValidNum, &param->ValidNum, sizeof(m_vsgParam.ValidNum)))
        && (0 == memcmp(&m_vsgParam.Reserved, &param->Reserved, sizeof(m_vsgParam.Reserved)))

        && (0 == UsualKit::DoubleCompareVector(m_vsgParam.ExtPathLoss, param->ExtPathLoss, ARRAYSIZE(m_vsgParam.ExtPathLoss)))
        && (0 == UsualKit::DoubleCompareVector(m_vsgParam.ExtPathLoss2, param->ExtPathLoss2, ARRAYSIZE(m_vsgParam.ExtPathLoss)))
        && (0 == UsualKit::DoubleCompareVector(m_vsgParam.Power, param->Power, ARRAYSIZE(m_vsgParam.Power)))

        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.Freq, &param->Freq))
        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.Freq2, &param->Freq2))
        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.FreqOffset, &param->FreqOffset))
        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.SamplingFreq, &param->SamplingFreq))
        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.TimeoutWaiting, &param->TimeoutWaiting))

        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.Wave_gap, &param->Wave_gap))
        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.RandomWaveGapMax, &param->RandomWaveGapMax))
        && (0 == UsualKit::DoubleCompareVector(&m_vsgParam.RandomWaveGapMin, &param->RandomWaveGapMin))
        && (0 == memcmp(&m_vsgParam.Repeat, &param->Repeat, sizeof(m_vsgParam.Repeat)))
        && (0 == memcmp(&m_vsgParam.IFGRamdomMode, &param->IFGRamdomMode, sizeof(m_vsgParam.IFGRamdomMode)))
        );
}

int InstrumentHandle::SetVSGParam(
    VsgParameter *vsgParam,
    ExtendVsgParameter *extParam,
    double TBTDelay)
{
    A_ASSERT(vsgParam);
    //API在接收到PN配置后，VSG参数配置都需要往仪器发送，取消相同参数不下发的做法
    if (!m_vsgPnRefFlag && isSameVsgParam(vsgParam) && nullptr == extParam)
    {
        return m_LastSetVsgParamResult;
    }

    const int MaxBufSize = WT_SUB_TESTER_INDEX_MAX*MAX_BUFF_SIZE;//8*8时，避免内存不够
    std::unique_ptr<char[]> proBuff(new char[MaxBufSize]);
    char *curPos = proBuff.get();

    memcpy(curPos, &m_currSubTesterCount, sizeof(m_currSubTesterCount));
    curPos += sizeof(m_currSubTesterCount);
    u32 remainSize = MaxBufSize - sizeof(m_currSubTesterCount);
    int testerIndex = 0;
    for (; testerIndex < m_currSubTesterCount; testerIndex++)
    {
        VsgParam tVsgParam;
        tVsgParam.Freq = vsgParam->Freq;
        tVsgParam.Freq2 = vsgParam->Freq2;
        tVsgParam.Is160M = 0;
        if ((vsgParam->Freq2 < 1.0) && ((vsgParam->Reserved[0] == WT_DEMOD_11AC_80_80M) || (vsgParam->Reserved[0] == WT_DEMOD_11AX_80_80M)))
        {
            tVsgParam.Is160M = 1;
        }
        tVsgParam.FreqOffset = vsgParam->FreqOffset;
        tVsgParam.IFG = vsgParam->Wave_gap;
        tVsgParam.LoopCnt = vsgParam->Repeat;
        tVsgParam.RandomWaveGapMax = vsgParam->RandomWaveGapMax;
        tVsgParam.RandomWaveGapMin = vsgParam->RandomWaveGapMin;
        tVsgParam.IFGRamdomMode = vsgParam->IFGRamdomMode;
        //300与200端口枚举不一致
        tVsgParam.RFPort = vsgParam->RfPort[testerIndex] - 1;
        tVsgParam.Type = m_currTestMode;
        tVsgParam.MasterMode = m_vsgMasterMode;
        tVsgParam.VsgMask = vsgParam->VsgUnitMask[testerIndex];
        tVsgParam.Power = vsgParam->Power[testerIndex];
        tVsgParam.ExtPathLoss = vsgParam->ExtPathLoss[testerIndex];
        tVsgParam.ExtPathLoss2 = vsgParam->ExtPathLoss2[testerIndex];
        tVsgParam.SignalId = testerIndex;
        if (m_VsgSampRateFromFileFlag == true)
        {
            tVsgParam.SamplingRate = m_vsgParam.SamplingFreq;
        }
        else
        {
            tVsgParam.SamplingRate = vsgParam->SamplingFreq;
        }

        tVsgParam.TBTStaDelay = TBTDelay;
        if (vsgParam->SamplingFreq > m_maxSampleRate + 1.0)
        {
            return WT_ERR_CODE_SAMPLE_RATE_TOO_LARGE;
        }
        tVsgParam.AllocTimeout = (int)vsgParam->TimeoutWaiting;
        if (remainSize < sizeof(VsgParam))
        {
            break;
        }
        if (extParam)
        {
            if (fabs(vsgParam->Freq2) > 1.0)
            {
                tVsgParam.RFPort = extParam->VsgRfPort[testerIndex * 2] - 1;
                tVsgParam.RFPort2 = extParam->VsgRfPort[testerIndex * 2 + 1] - 1;
                tVsgParam.Power = extParam->VsgPower[testerIndex * 2];
                tVsgParam.Power2 = extParam->VsgPower[testerIndex * 2 + 1];
                tVsgParam.DulPortMode = 1;
            }
            tVsgParam.DCOffsetI = extParam->DCOffsetI;
            tVsgParam.DCOffsetQ = extParam->DCOffsetQ;
            tVsgParam.CommModeVolt = extParam->CommModeVolt;
        }
        memcpy(curPos, &tVsgParam, sizeof(VsgParam));
        curPos += sizeof(VsgParam);
        remainSize -= sizeof(VsgParam);
    }

    if (testerIndex < m_currSubTesterCount)
    {
        return WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    CmdHeader tmpHeader;
    ExchangeBuff pstSendBuff;
    u32 sendTimeOut_ms = 1000;
    u32 recvTimeOut_ms = static_cast<int>(vsgParam->TimeoutWaiting) * 1000 + 3000;

    pstSendBuff.chpHead = proBuff.get();
    pstSendBuff.buff_len = (sizeof(VsgParam) * m_currSubTesterCount) + sizeof(int);
    pstSendBuff.data_len = (sizeof(VsgParam) * m_currSubTesterCount) + sizeof(int);
    int err = Exchange(0, CMD_SET_VSG_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG, sendTimeOut_ms, recvTimeOut_ms, &tmpHeader);
    //不管是否配置成功，都保存当时的参数
    //if(err >= WT_ERR_CODE_OK)
    {
        memcpy(&m_VsgParameterBack, &m_vsgParam, sizeof(VsgParameter));
        memcpy(&m_vsgParam, vsgParam, sizeof(VsgParameter));
        if (extParam)
        {
            memcpy(&m_vsgExternParam, extParam, sizeof(ExtendVsgParameter));
        }
        else
        {
            memset(&m_vsgExternParam, 0, sizeof(m_vsgExternParam));
        }
        m_vsgPnRefFlag = false;
        m_VsgParamSerialNum = tmpHeader.SerialNum;
    }
    m_LastSetVsgParamResult = err;
    return err;
}

int InstrumentHandle::SetVSGWaveParam(VsgWaveParameter *vsgWaveParam)
{
    unique_ptr<char[]>tmpBuf = nullptr;
    A_ASSERT(vsgWaveParam);
    CmdHeader tmpHeader;
    //转换成linux下的目录格式
    char tmpSaveFileName[MAX_NAME_SIZE] = { 0 };
    strcpy(tmpSaveFileName, vsgWaveParam->SaveWaveName);
    UsualKit::WindirToLinuxDir(tmpSaveFileName, MAX_NAME_SIZE);
    const char *extName = m_WaveExtName.c_str();  //新的文件扩展名
    string lastName;
    char *pSendBuff = nullptr;
    u32 buffSize = 0;
    int err = WT_ERR_CODE_OK;
    size_t pos = ((string)tmpSaveFileName).find_last_of("."); //找到扩展名的位置
    if (pos == string::npos)
    {
        lastName = m_WaveExtName;
    }
    else
    {
        lastName = ((string)tmpSaveFileName).substr(pos + 1, strlen(tmpSaveFileName));
    }

    if (true)
    {
        //查看是否有关闭PN补偿配置文件，优先使用此文件配置
        int flag = 0;
        err = UsualKit::config_int_var("api_cfg.json", "WT_PN_COMPENSATE", &flag);
        if (WT_ERR_CODE_OK == err)
        {
            err = SetWaveCalDataCompensate(flag);
        }
    }

    if (true)
    {
        size_t actualBuffSize = 0;
        m_vsgAc8080Flag = (vsgWaveParam->Wave2Flag == 1);
        memcpy(tmpSaveFileName + pos + 1, extName, strlen(extName));          //更改上传文件的扩展名

        SetRUSubCarreir(vsgWaveParam->WaveName);

        if (TEST_SISO_API < m_currTestMode || m_currSubTesterCount > 1)
        {
            //MIMO时，不考虑多连接和效率。此处不再多余申请PN内存拷贝，避免申请内存失败问题
            //在网络发送过程中还是加锁PN内存，直到网络发送完成才解锁
            vector<memCollector>PnCollectorBuf;
            ENTER_LOCK(PN_OPERATE_LOCK);
            err = PnFileProcess(vsgWaveParam->WaveName, tmpSaveFileName, vsgWaveParam->Wave2Flag, IOCONTROL_VSG, PnCollectorBuf);
            if (WT_ERR_CODE_OK == err)
            {
                vector<ExchangeBuff> pstSendBuff;

                for (auto &item : PnCollectorBuf)
                {
                    ExchangeBuff tmpBuf;
                    tmpBuf.chpHead = (false == item.isNeedFree ? (char *)item.pData2 : (char *)item.pData.get());
                    tmpBuf.buff_len = item.DataSize;
                    tmpBuf.data_len = item.DataSize;
                    pstSendBuff.push_back(tmpBuf);
                }
                err = Exchange(0, CMD_TX_VSG_FILE, &pstSendBuff[0], pstSendBuff.size(), nullptr, 0, IOCONTROL_VSG, 3000, 10000, &tmpHeader);
            }
            EXIT_LOCK(PN_OPERATE_LOCK);
        }
        else
        {
            //SISO时，采用原来的PN内存拷贝，可加快多连接时PN处理速度
            //只是在读取PN数据时加锁，之后后拷贝到网络发送内存，避免网络发送过程中还在加锁状态
            unique_ptr<char[]>tmpBuf = nullptr;
            err = PnFileProcess(vsgWaveParam->WaveName, tmpSaveFileName, vsgWaveParam->Wave2Flag, IOCONTROL_VSG, tmpBuf, &actualBuffSize);
            if (WT_ERR_CODE_OK == err)
            {
                ExchangeBuff pstSendBuff;
                pstSendBuff.chpHead = tmpBuf.get();
                pstSendBuff.buff_len = actualBuffSize;
                pstSendBuff.data_len = actualBuffSize;
                err = Exchange(0, CMD_TX_VSG_FILE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG, 3000, 10000, &tmpHeader);
            }
        }
    }

    m_VsgWaveSerialNum = tmpHeader.SerialNum;
    memcpy(&m_VsgWaveParameterBack, &m_VsgWaveParameter, sizeof(m_VsgWaveParameter));
    memcpy(&m_VsgWaveParameter, vsgWaveParam, sizeof(m_VsgWaveParameter));
    return err;
}

int InstrumentHandle::SetVSGPattern(
    VsgPattern *vsgPattern,
    unsigned int vsgPnItemCount,
    PnItemHead_API *vsgPnHead,
    unsigned int vsgPnHeadCount)
{
    A_ASSERT(vsgPattern);

    unsigned int i = 0;
    int post = 0;
    char tmpSaveFileName[MAX_NAME_SIZE] = { 0 };
    string extName = m_WaveExtName;
    u32 recvTimeOut_ms = 3000;

    BackupPnItem();

    m_vecPnItem.clear();
    m_vecPnItemHead.clear();

    if (nullptr == vsgPnHead)
    {
        PnItemHead_API itemHead;
        itemHead.SendCnt = 1;
        itemHead.StartIdx = 0;
        m_vecPnItemHead.push_back(itemHead);
    }
    else
    {
        for (i = 0; i < vsgPnHeadCount; i++)
        {
            m_vecPnItemHead.push_back(vsgPnHead[i]);
        }
    }

    for (i = 0; i < vsgPnItemCount; i++)
    {
        ExtPnItem pnItem;

        pnItem.LoopCnt = vsgPattern[i].Repeat;
        //TODO IFG 120MHz or 240MHz ??
        //不固定240MHz采样率补点数，根据实际配置的采样率来设置
        pnItem.IFG = (unsigned int)(vsgPattern[i].Wave_gap * m_vsgParam.SamplingFreq);
        pnItem.StartDelay = (unsigned int)(vsgPattern[i].StartDelay * m_vsgParam.SamplingFreq);
        pnItem.IFGRamdomMode = (vsgPattern[i].Wave_gap >= 0 ? FIXED_IFG_MODE : RANDOM_IFG_MODE);
        pnItem.RandomWaveGapMax = vsgPattern[i].RandomWaveGapMax;
        pnItem.RandomWaveGapMin = vsgPattern[i].RandomWaveGapMin;
        if (SIG_USERFILE == vsgPattern[i].WaveType)
        {
            pnItem.WaveSource = WT_SOURCE_UNDEFINED;
        }
        else
        {
            pnItem.WaveSource = WT_SOURCE_WAVE;
        }
        pnItem.WavePreset = vsgPattern[i].WaveType;

        strcpy(tmpSaveFileName, vsgPattern[i].WaveName);

        post = ((string)tmpSaveFileName).find_last_of(".");
        if (post > 0)
        {
            strncpy(tmpSaveFileName + post + 1, extName.c_str(), extName.length());
            memcpy(pnItem.WaveName, tmpSaveFileName, MAX_NAME_SIZE);
        }
        memcpy(pnItem.WaveName, tmpSaveFileName, MAX_NAME_SIZE);
        //memcpy(pnItem.WaveName, vsgPattern->WaveName, MAX_NAME_SIZE);

        struct stat statbuf;
        std::string fullfileName = std::string("/tmp/low_wave/") + pnItem.WaveName;
        if (stat(fullfileName.c_str(), &statbuf) == 0)
        {
            recvTimeOut_ms += 3000 * (statbuf.st_size / (50 * 1e6));  //每5M增加300ms时间
        }
        m_vecPnItem.push_back(pnItem);
    }

    unique_ptr<char[]>buff = nullptr;
    buff.reset(new (std::nothrow) char[MAX_BUFF_SIZE * vsgPnItemCount]);
    unsigned int actualSize = 0;
    memcpy(buff.get(), &vsgPnHeadCount, sizeof(int));
    memcpy(buff.get() + sizeof(int), &vsgPnItemCount, sizeof(int));
    char *pos = buff.get() + 2 * sizeof(int);
    for (auto itPnHead = m_vecPnItemHead.begin(); itPnHead != m_vecPnItemHead.end(); itPnHead++)
    {
        memcpy(pos, &(*itPnHead), sizeof(PnItemHead_API));
        pos += sizeof(PnItemHead_API);
    }

    for (auto itPnItem = m_vecPnItem.begin(); itPnItem != m_vecPnItem.end(); itPnItem++)
    {
        memcpy(pos, &(*itPnItem), sizeof(ExtPnItem));
        pos += sizeof(ExtPnItem);
    }

    actualSize = pos - buff.get();
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = buff.get();
    pstSendBuff.buff_len = actualSize;
    pstSendBuff.data_len = actualSize;
    CmdHeader tmpHeader;
    int iret = Exchange(0, CMD_SET_PN_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG, 1000, recvTimeOut_ms, &tmpHeader);
    //只要配置过，就改变状态
    //if(iret == WT_ERR_CODE_OK)
    {
        m_vsgPnRefFlag = true;
        m_VsgWaveSerialNum = tmpHeader.SerialNum;
    }
    return iret;
}

int InstrumentHandle::GetVSGParam(
    VsgParameter* vsgParam,
    VsgWaveParameter* vsgWaveParam,
    VsgPattern* vsgPattern, 
    ExtendVsgParameter* extParam)
{
    if (nullptr == vsgParam)
    {
        return WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    int err = WT_ERR_CODE_OK;
    size_t memory_size = sizeof(VsgParam) * m_currSubTesterCount;
    unique_ptr<char[]> tmpBuf(new (std::nothrow) char[memory_size]);
    VsgParam* vsgBaseParam = (VsgParam*)tmpBuf.get();
    if (nullptr == vsgBaseParam)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }
    memset(vsgBaseParam, 0, memory_size);
    memset(vsgParam, 0, sizeof(VsgParameter));
    GetDefaultParameter(nullptr, nullptr, vsgParam, nullptr, nullptr);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char*)(vsgBaseParam);
    pstRecvBuff.buff_len = memory_size;
    pstRecvBuff.data_len = memory_size;
    err = Exchange(0, CMD_GET_VSG_PARAM, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }
    for (int testerIndex = 0; testerIndex < m_currSubTesterCount; testerIndex++)
    {
        vsgParam->Freq = vsgBaseParam[0].Freq;
        vsgParam->Freq2 = vsgBaseParam[0].Freq2;
        vsgParam->FreqOffset = vsgBaseParam[0].FreqOffset;
        vsgParam->SamplingFreq = vsgBaseParam[0].SamplingRate;
        vsgParam->RfPort[testerIndex] = vsgBaseParam[testerIndex].RFPort + 1;
        vsgParam->VsgUnitMask[testerIndex] = vsgBaseParam[testerIndex].VsgMask;
        vsgParam->Power[testerIndex] = vsgBaseParam[testerIndex].Power;
        vsgParam->ExtPathLoss[testerIndex] = vsgBaseParam[testerIndex].ExtPathLoss;
        vsgParam->TimeoutWaiting = vsgBaseParam[0].AllocTimeout;
        vsgParam->Wave_gap = vsgBaseParam[0].IFG;
        vsgParam->RandomWaveGapMax = vsgBaseParam[0].RandomWaveGapMax;
        vsgParam->RandomWaveGapMin = vsgBaseParam[0].RandomWaveGapMin;
        vsgParam->Repeat = vsgBaseParam[0].LoopCnt;
        vsgParam->IFGRamdomMode = vsgBaseParam[0].IFGRamdomMode;
        vsgParam->Reserved[0] = vsgBaseParam[0].Is160M;

        if (extParam)
        {
            extParam->WIFI8080DulPortMode = vsgBaseParam[0].DulPortMode;
            if (vsgBaseParam[0].DulPortMode)
            {
                extParam->VsgPower[testerIndex * 2] = vsgBaseParam[testerIndex].Power;
                extParam->VsgPower[testerIndex * 2 + 1] = vsgBaseParam[testerIndex].Power2;

                extParam->VsgRfPort[testerIndex * 2] = vsgBaseParam[testerIndex].RFPort + 1;
                extParam->VsgRfPort[testerIndex * 2 + 1] = vsgBaseParam[testerIndex].RFPort2 + 1;
            }
        }
        extParam->DCOffsetI = vsgBaseParam[0].DCOffsetI;
        extParam->DCOffsetQ = vsgBaseParam[0].DCOffsetQ;
        extParam->CommModeVolt = vsgBaseParam[0].CommModeVolt;
    }

    memcpy(&m_VsgParameterBack, &m_vsgParam, sizeof(VsgParameter));
    memcpy(&m_vsgParam, vsgParam, sizeof(m_vsgParam));
    m_VsgParamSerialNum = 0;

    if (vsgPattern)
    {
        GetVSGPattern(vsgPattern, 1, nullptr, 0, nullptr, nullptr);
    }

    if (vsgWaveParam)
    {
        memcpy(vsgWaveParam, &m_VsgWaveParameter, sizeof(VsgWaveParameter));
    }

func_exit:

    return err;
}

int InstrumentHandle::GetVSGPattern(
    VsgPattern *vsgPattern,
    unsigned int vsgPnItemCount,
    PnItemHead_API *vsgPnHead,
    unsigned int vsgPnHeadCount,
    unsigned int *actualPnItemCount,
    unsigned int *actualPnHeadCount)
{
    if (actualPnItemCount)
    {
        *actualPnItemCount = m_vecPnItem.size();
    }
    if (actualPnHeadCount)
    {
        *actualPnHeadCount = m_vecPnItemHead.size();
    }

    for (int i = 0; i < m_vecPnItem.size() && i < vsgPnItemCount; i++)
    {
        if (vsgPattern)
        {
            vsgPattern[i].Repeat = m_vecPnItem[i].LoopCnt;
            vsgPattern[i].Wave_gap = static_cast<double>(m_vecPnItem[i].IFG) / static_cast<double>(m_vsgParam.SamplingFreq);
            vsgPattern[i].RandomWaveGapMax = m_vecPnItem[i].RandomWaveGapMax;
            vsgPattern[i].RandomWaveGapMin = m_vecPnItem[i].RandomWaveGapMin;
            vsgPattern[i].StartDelay = static_cast<double>(m_vecPnItem[i].StartDelay) / static_cast<double>(m_vsgParam.SamplingFreq);
            vsgPattern[i].WaveType = m_vecPnItem[i].WavePreset;
            strcpy(vsgPattern[i].WaveName, m_vecPnItem[i].WaveName);
        }
    }

    for (int i = 0; i < m_vecPnItemHead.size() && i < vsgPnHeadCount; i++)
    {
        if (vsgPnHead)
        {
            memcpy(&vsgPnHead[i], &m_vecPnItemHead[i], sizeof(PnItemHead_API));
        }
    }

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::StartVSG()
{
    int vsgStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int err = WT_ERR_CODE_OK;
    u32 sendTimeOut_ms = 1000;
    u32 recvTimeOut_ms = 3000;
    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        recvTimeOut_ms += static_cast<int>(m_vsgParam.TimeoutWaiting) * 1000;
    }
    err = Exchange(0, CMD_START_VSG, nullptr, 0, nullptr, 0, IOCONTROL_VSG, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        return err;
    }
    while (true)
    {
        err = GetCurrVSGStatu(&vsgStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsgStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsgStatu)
        {
            err = WT_ERR_CODE_VSG_ERR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsgStatu)
        {
            Sleep(1);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }
    }
    return err;
}

int InstrumentHandle::AsynStartVSG()
{
    u32 sendTimeOut_ms = 1000;
    u32 recvTimeOut_ms = 3000;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        recvTimeOut_ms += static_cast<int>(m_vsgParam.TimeoutWaiting) * 1000;
    }
    int iRet = Exchange(0, CMD_START_VSG, nullptr, 0, nullptr, 0, IOCONTROL_VSG, sendTimeOut_ms, recvTimeOut_ms);
    return iRet;
}

int InstrumentHandle::GetCurrVSGStatu(int *statu)
{
    A_ASSERT(statu);
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)statu;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    return Exchange(0, CMD_GET_VSG_STATUS, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);
}

int InstrumentHandle::PauseVSG()
{
    return Exchange(0, CMD_PAUSE_VSG, nullptr, 0, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::StopVSG()
{
    //停止信号时,应该立即生效,尝试三次发送停止命令
    int cnt = 1;
    int err = WT_ERR_CODE_OK;
    while (cnt)
    {
        err = Exchange(0, CMD_STOP_VSG, nullptr, 0, nullptr, 0, IOCONTROL_VSG, 1000, 2000);
        if (WT_ERR_CODE_OK == err)
        {
            break;
        }
        cnt--;
    }
    return err;
}

int InstrumentHandle::AnalyzeVSGData(
    const char *waveName,
    int demodType,
    AnalyzeParam *analyzeParams,
    int paramsSize,
    int timeOut)
{
    int err = WT_ERR_CODE_OK;
    do
    {
        err = LoadDataAsCapture(nullptr, waveName, IOCONTROL_VSG, m_vsgAc8080Flag);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        err = SetAnalyzeParam(demodType, analyzeParams, paramsSize, IOCONTROL_VSG);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        m_vsgAnalyzeType = demodType;

        err = Analyze(0, nullptr, IOCONTROL_VSG, timeOut);

    } while (0);
    return err;
}

int InstrumentHandle::GetVSGDataResultElementSize(
    const char *anaParamString,
    unsigned int *elementSize,
    int signalID,
    int segmentID)
{
    unsigned int elementCount = 0;
    int err = GetVectorResultInfo(anaParamString, &elementCount, elementSize, IOCONTROL_VSG, signalID, segmentID);
    if (WT_ERR_CODE_BUFFER_TOO_SHORT == err)
    {
        err = WT_ERR_CODE_OK;
    }
    return err;
}

int InstrumentHandle::GetVSGDataResultElementCount(
    const char *anaParamString,
    unsigned int *elementCount,
    int signalID,
    int segmentID)
{
    unsigned int elementSize = 0;
    int err = GetVectorResultInfo(anaParamString, elementCount, &elementSize, IOCONTROL_VSG, signalID, segmentID);
    if (WT_ERR_CODE_BUFFER_TOO_SHORT == err)
    {
        err = WT_ERR_CODE_OK;
    }
    return err;
}

int InstrumentHandle::GetVSGDataVectorResult(
    const char *anaParamString,
    void *result,
    unsigned int resultSize,
    int signalID,
    int segmentID)
{
    A_ASSERT(result);
    unsigned int elementSize = 0;
    unsigned int elementCount = 0;
    return GetVectorResult(anaParamString, result, resultSize, &elementCount, &elementSize, IOCONTROL_VSG, signalID, segmentID);
}

int InstrumentHandle::GetTxPowerRange(
    double freq,
    int option,
    int *upperLimit,
    int *lowerLimit)
{
    A_ASSERT(upperLimit);
    A_ASSERT(lowerLimit);
    char pSendBuff[MAX_BUFF_SIZE] = { 0 };
    int offset = 0;

    memcpy(pSendBuff + offset, &option, sizeof(option));
    offset += sizeof(option);
    memcpy(pSendBuff + offset, &freq, sizeof(freq));
    offset += sizeof(freq);

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = pSendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    char pRecvBuff[MAX_BUFF_SIZE] = { 0 };
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = pRecvBuff;
    pstRecvBuff.buff_len = sizeof(pRecvBuff);
    pstRecvBuff.data_len = sizeof(pRecvBuff);

    int err = WT_ERR_CODE_OK;
    do
    {
        err = Exchange(0, CMD_GET_POWER_RANGE, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSG);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        memcpy(upperLimit, pRecvBuff, sizeof(int));
        memcpy(lowerLimit, pRecvBuff + sizeof(int), sizeof(int));
    } while (0);

    return err;
}

int InstrumentHandle::SetWaveCalDataCompensate(int ON_FF)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&ON_FF;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSG_FLATNESS_CAL_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}
int InstrumentHandle::SetVsgIQImbCompensate(int ON_FF)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&ON_FF;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSG_IQIMB_CAL_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::GetVsgFlatnessCalCompensate(int *ON_FF)
{
	ExchangeBuff pstRecvBuff;
	pstRecvBuff.chpHead = (char *)(ON_FF);
	pstRecvBuff.buff_len = sizeof(int);
	pstRecvBuff.data_len = sizeof(int);
    
	return Exchange(0, CMD_GET_VSG_FLATNESS_CAL_ENABLE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_MISC);
}
int InstrumentHandle::GetVsgIQImbCompensate(int *ON_FF)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)(ON_FF);
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    
	return Exchange(0, CMD_GET_VSG_IQIMB_CAL_ENABLE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_MISC);
}

int InstrumentHandle::SetVSGFemParamter(FemParameter *param)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (char *)param;
    pstSendBuff.buff_len = sizeof(FemParameter);
    pstSendBuff.data_len = sizeof(FemParameter);

    int iRet = Exchange(0, CMD_SET_VSG_FEM_MODE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
    return iRet;
}
// TB-TF测试，仪器属于STA，发送TB信号

#ifndef LINUX
int InstrumentHandle::TF_Start(int timeout_ms)
{
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    if (m_vecPnItem.size() > 10)
    {
        sendTimeOut_ms += 1000 * m_vecPnItem.size();
        recvTimeOut_ms += 1000 * m_vecPnItem.size();
    }

    err = Exchange(0, CMD_START_TBT_STA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    int vsaStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int infinity = 0;
    if (0 == UsualKit::DoubleCompare(timeout_ms, 0.0))
    {
        infinity = 1;
    }
    timeval stTimeStart, stTimeEnd;
    UsualKit::gettimeofday(&stTimeStart);
    while (true)
    {
        if (0 == infinity)
        {
            UsualKit::gettimeofday(&stTimeEnd);
            if (UsualKit::elapsed_time(&stTimeStart, &stTimeEnd) >= timeout_ms)
            {
                Exchange(0, CMD_STOP_TBT_STA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }
        err = TF_GetStatus(&vsaStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_CAPTURE_DATA_ERROR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsaStatu)
        {
            Sleep(1);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_NO_DATA_CAPTURED;
            break;
        }
    }
func_exit:
    return err;
}
#else
int InstrumentHandle::TF_Start(int timeout_ms)
{
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;

    int vsaStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int infinity = 0;
    if (0 == UsualKit::DoubleCompare(timeout_ms, 0.0))
    {
        infinity = 1;
    }
    timeval stTimeStart, stTimeEnd;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    if (m_vecPnItem.size() > 10)
    {
        sendTimeOut_ms += 1000 * m_vecPnItem.size();
        recvTimeOut_ms += 1000 * m_vecPnItem.size();
    }
    err = Exchange(0, CMD_START_TBT_STA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }
    gettimeofday(&stTimeStart, nullptr);
    while (true)
    {
        if (0 == infinity)
        {
            gettimeofday(&stTimeEnd, nullptr);
            if (UsualKit::elapsed_time(&stTimeStart, &stTimeEnd) >= timeout_ms)
            {
                Exchange(0, CMD_STOP_TBT_STA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }
        err = TF_GetStatus(&vsaStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_CAPTURE_DATA_ERROR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsaStatu)
        {
            usleep(1000);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_NO_DATA_CAPTURED;
            break;
        }
    }
func_exit:
    return err;
}
#endif

// TB-TF测试，仪器属于STA，发送TB信号
int InstrumentHandle::TF_Stop()
{
    int cnt = 1;
    int err = WT_ERR_CODE_OK;
    while (cnt)
    {
        err = Exchange(0, CMD_STOP_TBT_STA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 3000);
        if (WT_ERR_CODE_OK == err)
        {
            break;
        }
        cnt--;
    }
    return err;
}

// TB-TF测试，仪器属于STA，发送TB信号
int InstrumentHandle::TF_GetStatus(int *status)
{
    A_ASSERT(status);
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)status;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    return Exchange(0, CMD_GET_TBT_STATUS, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);
}

int InstrumentHandle::TF_SetParam(InterBindParameter *Param, int vsaTrigger)
{
    int iRet = WT_ERR_CODE_OK;
#ifdef LINUX
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Param->VsgPattNum:" << Param->VsgPattNum << std::endl;
#endif
    A_ASSERT(Param);
    A_ASSERT(Param->VsgPattNum && Param->VsgPatt);

    unique_ptr<VsaParameter> tmpVsaParameter(new VsaParameter);
    unique_ptr<VsgParameter>tmpVsgParameter(new VsgParameter);
    A_ASSERT(tmpVsaParameter && tmpVsgParameter);

    GetDefaultParameter(tmpVsaParameter.get(), nullptr, tmpVsgParameter.get(), nullptr, nullptr);

    //VSA
    tmpVsaParameter->Freq = Param->Freq;
    tmpVsaParameter->Freq2 = Param->Freq2;
    memcpy(&tmpVsaParameter->MaxPower[0], &Param->VsaMaxPower[0], sizeof(Param->VsaMaxPower));
    memcpy(&tmpVsaParameter->RfPort[0], &Param->VsaRfPort[0], sizeof(Param->VsaRfPort));
    memcpy(&tmpVsaParameter->ExtPathLoss[0], &Param->VsaExtPathLoss[0], sizeof(Param->VsaExtPathLoss));
    memcpy(&tmpVsaParameter->ExtPathLoss2[0], &Param->VsaExtPathLoss2[0], sizeof(Param->VsaExtPathLoss2));
    tmpVsaParameter->SmpTime = Param->VsaSmpTime;
    tmpVsaParameter->Demode = Param->VsaDemode;
    tmpVsaParameter->TrigType = vsaTrigger;
    tmpVsaParameter->TrigLevel = Param->TrigLevel;
    tmpVsaParameter->TrigTimeout = Param->TriggerTimeOut;
    tmpVsaParameter->TrigPretime = Param->VsaPreTrigger;
    tmpVsaParameter->SamplingFreq = Param->VsaSamplingFreq;

    //VSG
    tmpVsgParameter->Freq = Param->Freq;
    tmpVsgParameter->Freq2 = Param->Freq2;
    tmpVsgParameter->SamplingFreq = Param->VsgSamplingFreq;
    memcpy(&tmpVsgParameter->Power[0], &Param->VsgPower[0], sizeof(Param->VsgPower));
    memcpy(&tmpVsgParameter->RfPort[0], &Param->VsgRfPort[0], sizeof(Param->VsgRfPort));
    memcpy(&tmpVsgParameter->ExtPathLoss[0], &Param->VsgExtPathLoss[0], sizeof(Param->VsgExtPathLoss));
    memcpy(&tmpVsgParameter->ExtPathLoss2[0], &Param->VsgExtPathLoss2[0], sizeof(Param->VsgExtPathLoss2));

    do
    {
        if (Param->DigParam && TESTER_RUN_DIGIT_IQ == m_TesterRunMode)
        {
            SetDigtalIQParam(Param->DigParam);
            if (iRet)
            {
#ifdef LINUX
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetDigtalIQParam error = " << iRet << std::endl;
#endif
                break;
            }
        }

        iRet = SetVSAParam(tmpVsaParameter.get(), nullptr);
        if (iRet)
        {
#ifdef LINUX
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetVSAParam error = " << iRet << std::endl;
#endif
            break;
        }

        if ('\0' != Param->VsgPatt->WaveName[0])
        {
            if (Param->NeedSetVSGPattern)
            {
                iRet = SetVSGPattern(Param->VsgPatt, Param->VsgPattNum, nullptr, 1);
                if (iRet)
                {
#ifdef LINUX
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetVSGPattern error = " << iRet << std::endl;
#endif
                    break;
                }
            }
            iRet = SetVSGParam(tmpVsgParameter.get(), nullptr);
            if (iRet)
            {
#ifdef LINUX
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetVSGParam error = " << iRet << std::endl;
#endif
                break;
            }
        }
    } while (0);

    return iRet;
}

int InstrumentHandle::GetVSGSendPacketCnt(int *Cnt)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)(Cnt);
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    return Exchange(0, CMD_GET_VSG_SEND_CNT, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);
}

int InstrumentHandle::SetBroadcastEnable(int Status)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (char *)&Status;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSG_BROADCAST_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::GetBroadcastEnable(int &Status)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)&Status;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);

    return Exchange(0, CMD_GET_VSG_BROADCAST_ENABLE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);
}

int InstrumentHandle::SetBroadcastDebugEnable(int Status, double Power[WT_PORT_RF8 - WT_PORT_OFF])
{
    ExchangeBuff pstSendBuff;
    char tmpDataBuff[MAX_BUFF_SIZE] = { 0 };
    memcpy(tmpDataBuff, &Status, sizeof(Status));
    memcpy(tmpDataBuff + sizeof(Status), Power, sizeof(double)*(WT_PORT_RF8 - WT_PORT_OFF));

    pstSendBuff.chpHead = tmpDataBuff;
    pstSendBuff.buff_len = sizeof(int)+ sizeof(double)*(WT_PORT_RF8 - WT_PORT_OFF);
    pstSendBuff.data_len = sizeof(int)+ sizeof(double)*(WT_PORT_RF8 - WT_PORT_OFF);

    return Exchange(0, CMD_SET_VSG_BROADCAST_DEBUG_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::GetBroadcastRunStatus(int &Status)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)&Status;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);

    return Exchange(0, CMD_GET_VSG_BROADCAST_RUN_STATUS, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);
}

int InstrumentHandle::SetSegVsgParam(int Segno, VsgParameter *vsgParam)
{
    VsgParam *vsgParamTmp;
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize = sizeof(VsgParam) + sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &Segno, sizeof(int));

    vsgParamTmp = (VsgParam *)(proBuff + sizeof(int));
    memset(vsgParamTmp, 0, sizeof(VsgParam));
    vsgParamTmp->RFPort = vsgParam->RfPort[0] - 1;
    vsgParamTmp->Freq = vsgParam->Freq;
    vsgParamTmp->Freq2 = vsgParam->Freq2;
    vsgParamTmp->FreqOffset = vsgParam->FreqOffset;
    vsgParamTmp->IFG = vsgParam->Wave_gap;
    vsgParamTmp->Power = vsgParam->Power[0];
    vsgParamTmp->ExtPathLoss = vsgParam->ExtPathLoss[0];
    vsgParamTmp->ExtPathLoss2 = vsgParam->ExtPathLoss2[0];
    vsgParamTmp->LoopCnt = vsgParam->Repeat;
    vsgParamTmp->SamplingRate = vsgParam->SamplingFreq;
    vsgParamTmp->AllocTimeout = (int)vsgParam->TimeoutWaiting;

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(VsgParam) + sizeof(int);
    pstSendBuff.data_len = sizeof(VsgParam) + sizeof(int);
    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);

    return Err;
}

int InstrumentHandle::SetSegAllVsgParam(VsgParameter *AllvsgParam, int Size)
{
    VsgParam *AllvsgParamTmp;
    int Err = WT_ERR_CODE_OK;
    int MaxBufSize;
    int i;
    int Cnt = Size / sizeof(VsgParameter);

    MaxBufSize = sizeof(VsgParam) * Cnt;
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();

    AllvsgParamTmp = (VsgParam *)proBuff;
    for (i = 0; i < Cnt; i++)
    {
        AllvsgParamTmp[i].RFPort = AllvsgParam[i].RfPort[0] - 1;
        AllvsgParamTmp[i].Freq = AllvsgParam[i].Freq;
        AllvsgParamTmp[i].Freq2 = AllvsgParam[i].Freq2;
        AllvsgParamTmp[i].FreqOffset = AllvsgParam[i].FreqOffset;
        AllvsgParamTmp[i].IFG = AllvsgParam[i].Wave_gap;
        AllvsgParamTmp[i].Power = AllvsgParam[i].Power[0];
        AllvsgParamTmp[i].ExtPathLoss = AllvsgParam[i].ExtPathLoss[0];
        AllvsgParamTmp[i].ExtPathLoss2 = AllvsgParam[i].ExtPathLoss2[0];
        AllvsgParamTmp[i].LoopCnt = AllvsgParam[i].Repeat;
        AllvsgParamTmp[i].SamplingRate = AllvsgParam[i].SamplingFreq;
        AllvsgParamTmp[i].AllocTimeout = (int)AllvsgParam[i].TimeoutWaiting;
        AllvsgParamTmp[i].Type = 0;
        AllvsgParamTmp[i].SignalId = 0;
        AllvsgParamTmp[i].MasterMode = 0;
        AllvsgParamTmp[i].VsgMask = 0;
        AllvsgParamTmp[i].Is160M = 0;
        AllvsgParamTmp[i].RFPort2 = 0;
        AllvsgParamTmp[i].DulPortMode = 0;
        AllvsgParamTmp[i].DulPortMode = 0;
        AllvsgParamTmp[i].Power2 = 0;
        AllvsgParamTmp[i].TBTStaDelay = 0;
        AllvsgParamTmp[i].DCOffsetI = 0;
        AllvsgParamTmp[i].DCOffsetQ = 0;
        AllvsgParamTmp[i].CommModeVolt = 0;
        AllvsgParamTmp[i].RandomWaveGapMax = 0;
        AllvsgParamTmp[i].RandomWaveGapMin = 0;
    }

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = MaxBufSize;
    pstSendBuff.data_len = MaxBufSize;
    Err = Exchange(0, CMD_SET_LIST_SEG_ALLVSG_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::SetSegVsgSyncParam(int Segno, int Status)
{
    int Err = WT_ERR_CODE_OK;
    std::unique_ptr<char[]> TmpBuf(new (std::nothrow) char[sizeof(int) * 2]);

    char *proBuff = TmpBuf.get();
    *(int *)proBuff = Segno;
    *((int *)proBuff + 1) = Status;

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(int) * 2;
    pstSendBuff.data_len = sizeof(int) * 2;
    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_SYNC_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);

    return Err;
}

int InstrumentHandle::SetSegVsgAllSyncParam(int *AllSyncParam, int Size)
{
    int Err = WT_ERR_CODE_OK;

    char *proBuff = (char *)AllSyncParam;

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = Size;
    pstSendBuff.data_len = Size;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_ALLSYNC_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);

    return Err;
}

int InstrumentHandle::SetListRxSeqStart(int Repet, int EnableFlag, int IncrementFlag, int CellMod)
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 15000; // 暂时定10s
    u32 sendTimeOut_ms = 1000;
    const int MaxBufSize = sizeof(int) * 4;
    std::unique_ptr<char[]> TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &Repet, sizeof(int));
    memcpy(proBuff + sizeof(int), &EnableFlag, sizeof(int));
    memcpy(proBuff + sizeof(int) * 2, &IncrementFlag, sizeof(int));
    memcpy(proBuff + sizeof(int) * 3, &CellMod, sizeof(int));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = MaxBufSize ;
    pstSendBuff.data_len = MaxBufSize;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_START_SEQ, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::SetSegVsgWaveParam(int Segno, VsgPattern *vsgWaveParam)
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    ExtPnItem *pnItem;
    struct stat statbuf;
    const int MaxBufSize = sizeof(ExtPnItem) + sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &Segno, sizeof(int));

    pnItem = (ExtPnItem *)(proBuff + sizeof(int));
    memset(pnItem, 0, sizeof(ExtPnItem));
    pnItem->LoopCnt = vsgWaveParam->Repeat;
    pnItem->Extend = vsgWaveParam->Extend;
    pnItem->IFG = (unsigned int)(vsgWaveParam->Wave_gap * m_vsgParam.SamplingFreq);
    pnItem->StartDelay = (unsigned int)(vsgWaveParam->StartDelay * m_vsgParam.SamplingFreq);
    pnItem->IFGRamdomMode = (vsgWaveParam->Wave_gap >= 0 ? FIXED_IFG_MODE : RANDOM_IFG_MODE);
    pnItem->RandomWaveGapMax = vsgWaveParam->RandomWaveGapMax;
    pnItem->RandomWaveGapMin = vsgWaveParam->RandomWaveGapMin;
    pnItem->WaveSource = WT_SOURCE_UNDEFINED;
    pnItem->WaveType = vsgWaveParam->WaveType;
    strcpy(pnItem->WaveName, vsgWaveParam->WaveName);

    std::string fullfileName = std::string("/tmp/low_wave/") + pnItem->WaveName;
    if (stat(fullfileName.c_str(), &statbuf) == 0)
    {
        recvTimeOut_ms += 3000 * (statbuf.st_size / (50 * 1e6));  //每5M增加300ms时间
    }

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(ExtPnItem) + sizeof(int);
    pstSendBuff.data_len = sizeof(ExtPnItem) + sizeof(int);
    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_WAVE_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);

    return Err;
}

int InstrumentHandle::SetSegAllVsgWaveParam(VsgPattern *AllvsgWaveParam, int Size)
{
    ExtPnItem *AllvsgPnItem;
    int Err = WT_ERR_CODE_OK;
    int MaxBufSize;
    int i;
    int Cnt = Size / sizeof(VsgPattern);
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;
    struct stat statbuf;

    MaxBufSize = sizeof(ExtPnItem) * Cnt;
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();

    AllvsgPnItem = (ExtPnItem *)proBuff;
    for (i = 0; i < Cnt; i++)
    {
        AllvsgPnItem[i].LoopCnt = AllvsgWaveParam[i].Repeat;
        AllvsgPnItem[i].Extend = AllvsgWaveParam[i].Extend;
        AllvsgPnItem[i].IFG = (unsigned int)(AllvsgWaveParam[i].Wave_gap * m_vsgParam.SamplingFreq);
        AllvsgPnItem[i].StartDelay = (unsigned int)(AllvsgWaveParam[i].StartDelay * m_vsgParam.SamplingFreq);
        AllvsgPnItem[i].IFGRamdomMode = (AllvsgWaveParam[i].Wave_gap >= 0 ? FIXED_IFG_MODE : RANDOM_IFG_MODE);
        AllvsgPnItem[i].RandomWaveGapMax = AllvsgWaveParam[i].RandomWaveGapMax;
        AllvsgPnItem[i].RandomWaveGapMin = AllvsgWaveParam[i].RandomWaveGapMin;
        AllvsgPnItem[i].WaveSource = WT_SOURCE_UNDEFINED;
        AllvsgPnItem[i].WaveType = AllvsgWaveParam[i].WaveType;
        strcpy(AllvsgPnItem[i].WaveName, AllvsgWaveParam[i].WaveName);
        std::string fullfileName = std::string("/tmp/low_wave/") + AllvsgPnItem[i].WaveName;
        if (stat(fullfileName.c_str(), &statbuf) == 0)
        {
            recvTimeOut_ms += 3000 * (statbuf.st_size / (50 * 1e6));  //每5M增加300ms时间
        }
    }

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = MaxBufSize;
    pstSendBuff.data_len = MaxBufSize;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_ALLWAVE_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::SetListRxSeqStop()
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 10000;  //暂时定10s
    u32 sendTimeOut_ms = 1000;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSG_STOP_SEQ, nullptr, 0, nullptr, 0, IOCONTROL_VSG, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::GetListRxSeqAllTransState(int *SegNo)
{
    int Err = WT_ERR_CODE_OK;

    A_ASSERT(SegNo);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)SegNo;
    pstRecvBuff.buff_len = sizeof(int) * 2;
    pstRecvBuff.data_len = sizeof(int) * 2;

    Err = Exchange(0, CMD_GET_LIST_SEQ_VSG_TRANS_STATE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSG);

    return Err;
}