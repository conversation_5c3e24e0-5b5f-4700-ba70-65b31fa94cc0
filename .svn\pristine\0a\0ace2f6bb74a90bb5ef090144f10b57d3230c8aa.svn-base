#include <iostream>
#include <string>
#include <vector>
#include <tuple>

#include "scpi_3gpp_gen_NR5G.h"
#include "commonhandler.h"
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "TesterWave.h"
#include "math.h"
#define DEBUG_SHOW_NR (1)

static int IsNR5G(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (attr->Pn3GPP.get()->CommonParam.standard != ALG_3GPP_STD_5G)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t SCPI_NR5G_SetLinkDirect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = ALG_3GPP_DL;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_UL && Value != ALG_3GPP_DL)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.LinkDirect = Value;
        iRet = WT_GetDefaultWaveParameter3GPP(attr->ConnID, ALG_3GPP_STD_5G, Value, attr->Pn3GPP.get(), sizeof(Alg_3GPP_WaveGenType));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// General
//**************************************************************************************************
scpi_result_t SCPI_NR5G_SetGenWaveMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.General.GenWaveMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetGenSubFramePositonNo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.General.GenWaveSubfrmNo = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetGenSlotPositonNo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.General.GenWaveSlotNo = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// Filter
//**************************************************************************************************
scpi_result_t SCPI_NR5G_SetFilterType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FILTER_NON && Value != ALG_3GPP_FILTER_FIR && Value != ALG_3GPP_FILTER_NR)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.Type = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterSampleRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 122880000;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value != 30720000)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.Fs = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterMaxOrder(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 512;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 512 && Value != 1024 && Value != 2048)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.MaxOrder = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterFpassFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.6;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 1.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.FpassFactor = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterFstopFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.65;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 1.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        if (Value < attr->Pn3GPP->NR.General.Filter.FpassFactor)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.General.Filter.FstopFactor = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterPassRipple(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.05;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 0.3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.PassRipple = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterStopAtten(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 45;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.StopAtten = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 1;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.FilterMode = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterSmoothFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 1;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.1 || Value > 20)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.SmoothFactor = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_SetFilterCutOffFrqFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.11;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.02 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);
        attr->Pn3GPP->NR.General.Filter.CutOffFrqFactor = Value;
#if DEBUG_SHOW_NR
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// UL
//**************************************************************************************************
scpi_result_t SCPI_NR5G_UL_SetRFPhaseCompensation(scpi_t *context)
{
    enum WT_RF_PHASE_COMPEN_TYPE
    {
        OFF = 0,
        Manual,
    };
    /*0:OFF;1:Manual*/
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = OFF;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != OFF && Value != Manual)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.RfPhaseCompen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetDuplexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = ALG_3GPP_FDD;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Duplexing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSlotPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 10;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 5 && Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (attr->Pn3GPP->NR.UL.Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->NR.UL.SlotPeriod = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSlotNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (attr->Pn3GPP->NR.UL.Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->NR.UL.ULSlotNumber = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSpecialSlotIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 55)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (attr->Pn3GPP->NR.UL.Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->NR.UL.SpecialSlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.CellNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // 0:OFF; 1:ON
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellDeployment(scpi_t *context)
{
    enum WT_Deployment_type
    {
        FR1_LE_3GHz = 0, // FR1<=3GHz
        FR1_GT_3GHz,     // FR1>3GHz
    };

    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < FR1_LE_3GHz || Value > FR1_GT_3GHz)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].Deployment = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 100 * MHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (5 * MHz_API) && Value != (10 * MHz_API) && Value != (15 * MHz_API) && Value != (20 * MHz_API) && Value != (25 * MHz_API) && Value != (30 * MHz_API) &&
            Value != (40 * MHz_API) && Value != (50 * MHz_API) && Value != (60 * MHz_API) && Value != (70 * MHz_API) && Value != (80 * MHz_API) && Value != (90 * MHz_API) && Value != (100 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].ChannelBW = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellTxBWSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 15 * KHz_API;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[TxBWID].SCSpacing = Value;

        // 该值固定，只读，强制赋值
        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[0].SCSpacing = 15 * KHz_API;
        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[1].SCSpacing = 30 * KHz_API;
        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[2].SCSpacing = 60 * KHz_API;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellTxBWState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (Value == 1) 
        {
            // Reset
            attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[0].State = 0;
            attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[1].State = 0;
            attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[2].State = 0;
        }
        
        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[TxBWID].State = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellTxBWMaxRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0 && Value != -999) || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[TxBWID].MaxRBNumb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellTxBWOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[TxBWID].Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellTxBWK0U(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].TxBW[TxBWID].Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellPhysicalID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].PhyCellID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellDMRSTypeAPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 2;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 2 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].DmrsTypeAPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetCellFrequency(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Cell[CellID].Frequency = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.UeID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Scramble = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEChannelCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.ChanCodingState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 30 * KHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].CyclicPrefix = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 273;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // if (Value < 0 || Value > 273)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschTransformPrecoder(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.TransformPrecoder = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschTxConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.TxConfig = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschTPMI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 27)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.TPMI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPUSChDataScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Flag = 0;
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Flag, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Flag < 0 || Flag > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Flag == 1)
        {
            if (!SCPI_ParamInt(context, &Value, true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value < 0 || Value > 1023)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.UsePuschScrambleID = Flag;
        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.DataScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschMCSTable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.McsTab = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschFreqHoppingMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.FreqHopMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschResourceAllocationType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.ResourceAllocation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschDMRSType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.ConfigType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschDMRSMaxLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.MaxLength = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschDMRSAddPosInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.AdditionalPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschDMRSScramblingId(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value[2] = {0};
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < 2; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < 0 || Value[i] > 65535)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        // if (attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.TransformPrecoder != 0)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.ScramblingID0 = Value[0];
        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.ScramblingID1 = Value[1];
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetUEBwpPuschDMRSNpuschID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        // if (attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.TransformPrecoder != 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].Pusch.Dmrs.NPuschID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameTotalNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.SubfrmCfgNum[CellID] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int SubFrameID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        SubFrameID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].SubfrmIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[2] = {0, 0};
        int CellID = 0;
        int SubFrameID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        SubFrameID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].SlotCfgNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].SlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschCellID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        // 0-4
        if (Value < 0 || Value >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschMappingType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.MappingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschSymbolNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.SymbNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschSymbofOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.SymbOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (Value < 1 || Value > attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].RBNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (Value < 0 || Value > attr->Pn3GPP->NR.UL.Ue.Bwp[CellID].RBNum - 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschLayerNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.LayerNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.AntennaNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.Modulate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschCDMGroupsWithoutData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.CDMGrpWOData = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschDMRSSymbolLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.DmrsSymbLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschDMRSAntennaPort(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value[4] = {0, 1, 2, 3};
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int MaxLayerNum = 4;
        for (int i = 0; i < min(context->parser_state.numberOfParameters, MaxLayerNum); i++)
        {
            if (i == 0)
            {
                if (!SCPI_ParamInt(context, &Value[i], true))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }
            else
            {
                if (!SCPI_ParamInt(context, &Value[i], false))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }

            if (Value[i] < 0 || Value[i] > 11)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        IF_BREAK(iRet);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        memcpy(attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.DmrsAntPort, Value, sizeof(Value));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschNScrIDType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.NScrIDType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschNSCID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.NSCID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschNRSIDType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.NRSIDType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschGroupSequenceHopping(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.GrporSeqHopType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschMCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 28)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.MCS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschRVIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.RvIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_UL_SetSubFrameSlotPuschDMRSPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubFrameID = 0;
        int SlotID = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubFrameID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubFrameID < 0 || SubFrameID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.UL.Subfrm[CellID][SubFrameID].Slot[SlotID].Pusch.DmrsPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// DL
//**************************************************************************************************
scpi_result_t SCPI_NR5G_DL_SetPDSCHScheduling(scpi_t *context)
{
    enum WT_RF_PHASE_COMPEN_TYPE
    {
        Manual = 0,
        DCI,
    };
    /*0:Manual;1:DCI*/
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = Manual;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != Manual && Value != DCI)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.PdschScheduling = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetRestrictSS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.RestrictSS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetRFPhaseCompensation(scpi_t *context)
{
    enum WT_RF_PHASE_COMPEN_TYPE
    {
        OFF = 0,
        Manual,
    };
    /*0:OFF;1:Manual*/
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = OFF;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != OFF && Value != Manual)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.RfPhaseCompen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 1;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.CellNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // 0:OFF; 1:ON
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellDeployment(scpi_t *context)
{
    enum WT_Deployment_type
    {
        FR1_LE_3GHz = 0, // FR1<=3GHz
        FR1_GT_3GHz,     // FR1>3GHz
    };

    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < FR1_LE_3GHz || Value > FR1_GT_3GHz)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Deployment = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellFrequency(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Frequency = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellChannelBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 100 * MHz_API;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (5 * MHz_API) && Value != (10 * MHz_API) && Value != (15 * MHz_API) && Value != (20 * MHz_API) && Value != (25 * MHz_API) && Value != (30 * MHz_API) &&
            Value != (40 * MHz_API) && Value != (50 * MHz_API) && Value != (60 * MHz_API) && Value != (70 * MHz_API) && Value != (80 * MHz_API) && Value != (90 * MHz_API) && Value != (100 * MHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].ChannelBW = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPhysicalID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].PhyCellID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellDMRSTypeAPos(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 2;
        int CellID = 0;

       SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 2 && Value != 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].DmrsTypeAPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellSysFrameNumberOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

       SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1023)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].SysFNOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellOCNGState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int CellID = 0;
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].OCNGState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellOCNGModulation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int CellID = 0;
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].OCNGModulation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellOCNGDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int CellID = 0;
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].OCNGDataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellOCNGPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int CellID = 0;
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].OCNGPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellTxBWSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 15 * KHz_API;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[TxBWID].SCSpacing = Value;

        // 该值固定，只读，强制赋值
        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[0].SCSpacing = 15 * KHz_API;
        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[1].SCSpacing = 30 * KHz_API;
        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[2].SCSpacing = 60 * KHz_API;
    }while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellTxBWState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        if (Value == 1) 
        {
            // Reset
            attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[0].State = 0;
            attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[1].State = 0;
            attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[2].State = 0;
        }
        
        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[TxBWID].State = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);

}


scpi_result_t SCPI_NR5G_DL_SetCellTxBWMaxRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0 && Value != -999) || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[TxBWID].MaxRBNumb = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellTxBWOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[TxBWID].Offset = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellTxBWK0U(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Number[2] = {0, 0};
        int CellID = 0;
        int TxBWID = 0;

        SCPI_CommandNumbers(context, Number, 2);
        CellID = Number[0];
        TxBWID = Number[1];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (TxBWID < 0 || TxBWID > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].TxBW[TxBWID].k0u = Value;

    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

       SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 30 * KHz_API;
        int CellID = 0;

       SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15 * KHz_API && Value != 30 * KHz_API)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 253)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchSubCarrierOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 6;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.SCOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchCase(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.PbchCase = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 4;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 4 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.Length = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchPosition(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    if (iRet != WT_ERR_CODE_OK)
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        int Value[8] = {0};
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.Length; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] != 0 && Value[i] != 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        memcpy(attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.Position, Value, sizeof(Value));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchBurstSetPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 10;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 5 && Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.BurstSetPeriod = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchHalfFrameIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.HalfFrmIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchPBCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (-80  > Value || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.PbchPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchPSSPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (-80  > Value || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.PssPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchSSSPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (-80  > Value || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.SssPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBCommonSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((0 != Value) && (Value != 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.SCSCommon = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBSSBSubCarrierOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0) || (Value > 31))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.SCOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBCORESETZero(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0) || (Value > 15))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.CoresetZero = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBSearchSpaceZero(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < 0) || (Value > 15))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.SSZero = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBCellBarred(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value != 0) && (Value != 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.CellBarre = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}



scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBIntraFrequencyReselection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value != 0) && (Value != 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.InFreqResel = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellPbchOffsetRefType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.OffsetRefType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetCellPbchMIBAutoSCOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    
    do
    {
        int Value = 1;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Cell[CellID].Pbch.MIB.AutoSCOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetDuplexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1) // 0:FDD; 1:TDD
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Duplexing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSlotPeriod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    if (attr->Pn3GPP->NR.DL.Duplexing != 1) // 1:TDD
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 5 && Value != 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.SlotPeriod = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSlotNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    if (attr->Pn3GPP->NR.DL.Duplexing != 1) // 1:TDD
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 9 || (attr->Pn3GPP->NR.DL.SlotPeriod == 5 && Value > 4))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.DLSlotNumber = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSpecialSlotIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    if (attr->Pn3GPP->NR.DL.Duplexing != 1) // 1:TDD
    {
        return SCPI_ResultOK(context, WT_ERR_CODE_PARAMETER_MISMATCH);
    }

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.SpecialSlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.UeID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1) // 0:OFF; 1:ON
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Scrambling = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // 0:PN9; 1:PN11; 2:PN15; 3:PN23; 4:全0; 5:全1; 6:1010
        if (Value < 0 || Value > 6 || (attr->Pn3GPP->NR.DL.Ue.Scrambling == 0 && Value > 3))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int DataType = attr->Pn3GPP->NR.DL.Ue.DataType;
        if (Value < 1 || Value > 0x7FFFFE || (DataType == 0 && Value > 0x1FE) || (DataType == 1 && Value > 0x7FE) ||
            (DataType == 2 && Value > 0x7FFE) || (DataType == 3 && Value > 0x7FFFFE))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEChannelCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.ChanCodingState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1 || (attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].SCSpacing != 60 * KHz_API && Value == 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].CyclicPrefix = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpRBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }*/

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschUseScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1) // 0:OFF; 1:ON
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.UsePdschScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschDataScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1023)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.DataScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschVrbToPrbInterleaver(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.VrbToPrbInterleaver = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschMCSTable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.McsTab = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschResourseAllocation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.ResourceAllocation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschDmrsConfigType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.Dmrs.ConfigType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschDmrsMaxLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.Dmrs.MaxLength = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschDmrsAddPosIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.Dmrs.AdditionalPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschDmrsScramblingId0(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.Dmrs.ScramblingID0 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschDmrsScramblingId1(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.Dmrs.ScramblingID1 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschMaxCWNumPerDCI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.MaxCWNumPerDCI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschRBGSizeType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.RBGSizeType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpPdschMaxCBGPerTB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pdsch.MaxCBGPerTB = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1) // 0:OFF; 1:ON
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetSymbolNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.SymbNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetSymbolOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 13)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.SymbOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetFDResUseBitmap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.FDResUseBitmap = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetBitMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int bufLen = sizeof(attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.BitMap) + 1;
        char buf[bufLen];
        size_t copyLen = 0;
        memset(buf, 0, bufLen);

        if (!SCPI_ParamCopyText(context, buf, bufLen, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < copyLen; i++)
        {
            buf[i] -= '0';
            if (buf[i] < 0 || buf[i] > 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        IF_BREAK(iRet);

        for (int i = copyLen - 1; i >= 0; i--)
        {
            attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.BitMap[i] = buf[copyLen - 1 - i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetCoresetID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.CoresetID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetPreGranType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.PreGranType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetUseDmrsScramID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.UseDmrsScramID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetDmrsScramID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.DmrsScramID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetDmrsRefPoint(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.DmrsRefPoint = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetInterleaveState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.InterleaveState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetBundleSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 2 && Value != 3 && Value != 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.BundleSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetShfitIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0  || Value > 274)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.ShfitIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetInterleaverSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 2 && Value != 3 && Value != 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.InterleaverSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpCoresetMaxCandi(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        int Value[5] = {0};
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < 5; i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] != 1 && Value[i] != 2 && Value[i] != 3 && Value[i] != 4 && Value[i] != 5 && Value[i] != 6 && Value[i] != 8)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        memcpy(attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Coreset.MaxCandi, Value, sizeof(Value));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlSubCarrierSpacing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != (15 * KHz_API) && Value != (30 * KHz_API) && Value != (60 * KHz_API))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].SCSpacing = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1 || (attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].SCSpacing != 60 * KHz_API && Value == 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].CyclicPrefix = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*if (Value < 0 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }*/

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschTransformPrecoder(scpi_t *context)
{
        int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.TransformPrecoder = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschTxConfig(scpi_t *context)
{
        int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.TxConfig = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschTPMI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 27 || (attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.TxConfig != 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.TPMI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschUsePuschScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1) // 0:OFF; 1:ON
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.UsePuschScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDataScrambleID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1023)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.DataScrambleID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschMcsTab(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.McsTab = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschFreqHopMode(scpi_t *context)
{
        int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.FreqHopMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschResourceAllocation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.ResourceAllocation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDmrsConfigType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.Dmrs.ConfigType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDmrsMaxLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.Dmrs.MaxLength = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDmrsAdditionalPos(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.Dmrs.AdditionalPos = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDmrsScramblingID0(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.Dmrs.ScramblingID0 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDmrsScramblingID1(scpi_t *context)
{
        int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    iRet = IsNR5G(attr);
    IF_ERR_RETURN(iRet);

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.Dmrs.ScramblingID1 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetUEBwpUlPuschDmrsNPuschID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1007)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_BREAK(iRet);

        // if (attr->Pn3GPP->NR.DL.Ue.Bwp[CellID].Pusch.TransformPrecoder != 1)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }
        attr->Pn3GPP->NR.DL.Ue.BwpUL[CellID].Pusch.Dmrs.NPuschID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmCfgNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int CellID = 0;

        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].SubfrmCfgNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotCfgNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int CellID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, &CellID, 1);

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].SlotCfgNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmCellIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].SubfrmIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].SlotIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchUnusedCCEs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.UnusedCCEs = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchAutoDci(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.AutoSchedDLDCI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciUsage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].Usage = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 10 || Value > 11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DCIFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciSearchSpace(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].SearchSpace = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciAggregationLevel(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 1 && Value != 2 && Value != 4 && Value != 8 && Value != 16)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].AggregationLevel = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciCandidate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].Candidate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciCCEIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 134)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].CCEIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciTDAllocTabType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].TDAllocTabType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        double Value = 0.0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.PdcchPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchPdschPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        double Value = 0.0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].PdschPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_FreqDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.FDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_TimeDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.TDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_VRBtoPRBMapping(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.VRBtoPRBMap = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_ModulCodingScheme(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 28)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.MCS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_NewDataIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.NewDataInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_RedunVerIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.RVIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_HARQProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.Harq = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_DLAssignIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.DLAssignIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_TPCCmd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.TPCCom = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_PucchResIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.PucchResInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF10_PdschToHARQ(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF10Crnti.PdschToHarq = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_BWPartIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.BPWInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_FreqDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.FDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_TimeDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.TDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_VRBtoPRBMapping(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.VRBtoPRBMap = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_PRBBundSizeIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.PRBBundSizeInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_RateMatchIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.RateMatchInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_ZPCSIRSTrig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.ZPCSIRSTrigger = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_MCS_TB1(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 28)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.MCS[0] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_NewDataIndic_TB1(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.NewDataInd[0] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_RedunVerIdx_TB1(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.RVIdx[0] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_MCS_TB2(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 28)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.MCS[1] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_NewDataIndic_TB2(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.NewDataInd[1] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_RedunVerIdx_TB2(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.RVIdx[1] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_HARQProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.HarqProcNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_DLAssignIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.DLAssignIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_TPCCmdForPucch(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.TpcCmdForPucch = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_PucchResIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.PucchResInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_PdschToHarqIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.PdschToHardInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_AntennaPorts(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 31)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.AntennaPorts = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_TxConfigIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.TxConfigInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_SRSReq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.SRSRequest = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_CBGTI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.CBGTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_CBGFI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.CBGFI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_DMRSSeqInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -1 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.DMRSSeqInit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchDF11_MinAppSchedOffsetIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF11Crnti.MinAppSchedOffsetIndic = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleUsage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].Usage = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DCIFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleSearchSpace(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].SearchSpace = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleAggregationLevel(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 1 && Value != 2 && Value != 4 && Value != 8 && Value != 16)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].AggregationLevel = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleCandidate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].Candidate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmSlotPdcchDciScheduleCCEIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 134)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].CCEIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_FreqDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.FDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_TimeDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.TDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_FreqHopFlg(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.FreqHopFlg = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_ModulCodingScheme(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 31)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.MCS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_NewDataIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.NewDataInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_RedunVerIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.RVIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_HARQProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.HarqProcNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF00_TPCCmd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF00Crnti.TpcCmdForPusch = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_FreqDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.FDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_TimeDomainResAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.TDResAssign = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_ModulCodingScheme(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 31)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.MCS = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_NewDataIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.NewDataInd = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_RedunVerIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.RVIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_HARQProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.HarqProcNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_DLAssignIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[4] = {0, 0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Idx = 0;

        SCPI_CommandNumbers(context, Number, 4);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];
        Idx = Number[3];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM ||
            Idx < 0 || Idx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[0].DF01Crnti.DLAssignIdx[Idx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_TPCCmd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.TpcCmdForPusch = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_SRSReqIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.SRSResIndic = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_AntennaPorts(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.AntennaPorts = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_SRSReq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.SRSRequest = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_CSIReq(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.CSIRequest = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_CBGTI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.CBGTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_PTRSDmrsAssociate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.PTRSDmrsAssociate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_DmrsSeqInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.DMRSSeqInit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_ULSchIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.ULSCHIndic = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdcchScheduleDF01_MinAppSchdOffsetIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdcch.DCI[1].DF01Crnti.MinAppSchedOffsetIndic = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschMappingType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.MappingType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschSymbolNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.SymbNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschSymbolOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 12)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.SymbOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschResAlloc(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        SCPI_CommandNumbers(context, Number, 3);
        int CellID = Number[0];
        int SubfrmID = Number[1];
        int SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 12)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.ResourceAllocation = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschBitmap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        SCPI_CommandNumbers(context, Number, 3);
        int CellID = Number[0];
        int SubfrmID = Number[1];
        int SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        char text[256] = {0};
        char Buf[256] = {0};
        size_t copyLen = 0;
        if (!SCPI_ParamCopyText(context, text, sizeof(text) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        copyLen = min(copyLen, sizeof(Buf) - 1);

        for (int i = 0; i < copyLen; i++)
        {
            Buf[i] = text[copyLen - i - 1] - '0';
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);
        
        memcpy(attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.Bitmap, Buf, copyLen);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 273)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.RBNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 272)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.RBOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschCWNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.Codewords = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschLayerNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.LayerNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.AntennaNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschCmdGroupsWOData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.CDMGrpWOData = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschDmrsSymbolLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.DmrsSymbLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschDMRSAntennaPort(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[4] = {0, 0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int antIdx = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 4);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];
        antIdx = Number[3];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM ||
            antIdx < 0 || antIdx > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1000 || Value > 1005)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.DmrsAntPort[antIdx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschNscridType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.NScrIDType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschNscid(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.NSCID = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschDmrsPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        double Value = 0.0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.DmrsPower = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschTransBlockMod(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[4] = {0, 0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int codewordIdx = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 4);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];
        codewordIdx = Number[3];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM ||
            codewordIdx < 0 || codewordIdx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.Modulate[codewordIdx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschTransBlockMCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[4] = {0, 0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int codewordIdx = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 4);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];
        codewordIdx = Number[3];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM ||
            codewordIdx < 0 || codewordIdx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 28)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.MCS[codewordIdx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschTransBlockRedunVerIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[4] = {0, 0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int codewordIdx = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 4);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];
        codewordIdx = Number[3];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM ||
            codewordIdx < 0 || codewordIdx > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].Pdsch.RvIdx[codewordIdx] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschCSIRSCellIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        // attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].CSIRS.CellIdx = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NR5G_DL_SetSubfrmPdschCSIRSState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Number[3] = {0, 0, 0};
        int CellID = 0;
        int SubfrmID = 0;
        int SlotID = 0;
        int Value = 0;

        SCPI_CommandNumbers(context, Number, 3);
        CellID = Number[0];
        SubfrmID = Number[1];
        SlotID = Number[2];

        if (CellID < 0 || CellID >= ALG_5G_MAX_CELL_NUM ||
            SubfrmID < 0 || SubfrmID >= ALG_5G_MAX_SUBFRAME_NUM ||
            SlotID < 0 || SlotID >= ALG_5G_MAX_SLOT_IN_SUBFRM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsNR5G(attr);
        IF_ERR_RETURN(iRet);

        attr->Pn3GPP->NR.DL.Sched[CellID].Slot[SubfrmID][SlotID].CSIRS.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
