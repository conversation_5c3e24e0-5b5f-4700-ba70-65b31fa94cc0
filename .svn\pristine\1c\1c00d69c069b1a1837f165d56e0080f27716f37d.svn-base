﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DFA42F77-DCCD-4342-94AE-491B6572E418}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>WLANTesterAPIWT4XXWrapper</RootNamespace>
    <ProjectName>WT.Tester.API.WT4XXWrapper</ProjectName>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <EmbedManifest>true</EmbedManifest>
    <TargetName>$(ProjectName)</TargetName>
    <IncludePath>../../extlib/include;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <TargetExt>.dll</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <IncludePath>../../extlib/include;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;WT4XXWRAPPER_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(OutDir)</AdditionalLibraryDirectories>
      <AdditionalOptions>/verbose:lib %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <PreBuildEvent>
      <Command>Includings.bat</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;WT4XXWRAPPER_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(OutDir)</AdditionalLibraryDirectories>
    </Link>
    <PreBuildEvent>
      <Command>Includings.bat</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Pac.h" />
    <ClInclude Include="Cryptology.h" />
    <ClInclude Include="CryptologyWT4xx.h" />
    <ClInclude Include="includeall.h" />
    <ClInclude Include="InstrumentHandle.h" />
    <ClInclude Include="InstrumentHandle_V2.h" />
    <ClInclude Include="LoadLibrary.h" />
    <ClInclude Include="PrintParam.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="TesterWrapper.h" />
    <ClInclude Include="WaveGenerator.h" />
    <ClInclude Include="WaveGenerator_11BE.h" />
    <ClInclude Include="wt-calibration.h" />
    <ClInclude Include="WT4XXWrapper.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="InstrumentHandle_V2.cpp" />
    <ClCompile Include="Pac.cpp" />
    <ClCompile Include="CryptologyWT4xx.cpp" />
    <ClCompile Include="InstrumentHandle.cpp" />
    <ClCompile Include="MISC.cpp" />
    <ClCompile Include="PrintParam.cpp" />
    <ClCompile Include="VSA.cpp" />
    <ClCompile Include="VSG.cpp" />
    <ClCompile Include="WaveGenerator.cpp" />
    <ClCompile Include="WaveGenerator_11BE.cpp" />
    <ClCompile Include="WaveGenerator_V2.cpp" />
    <ClCompile Include="WT4XXWrapper.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Includings.bat" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="WT.Tester.API.WT4XXWrapper.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>