#include "includeall.h"
#define WIDE_MOD(x) ((WT_DEMOD_11AG <= x && WT_DEMOD_11AC_80_80M >= x) || \
                    (WT_DEMOD_11AX_20M <= x && WT_DEMOD_11AX_80_80M >= x) || (WT_DEMOD_11BE_20M <= x && WT_DEMOD_11BE_160_160M >= x))

int InstrumentHandle::BeamformingCalibrationChannelEstDutTX(int demod)
{
    ExchangeBuff pstSendBuff;
    memset(&m_Beaforming, 0, sizeof(m_Beaforming));
    m_Beaforming.demod = demod;
    m_Beaforming.DUT_Type = iBEAFORMING_BCM_STATE_1;

    pstSendBuff.chpHead = (char *)&m_Beaforming.demod;
    pstSendBuff.buff_len = sizeof(m_Beaforming.demod);
    pstSendBuff.data_len = sizeof(m_Beaforming.demod);
    return Exchange(0, CMD_BEAMFORMING_CLR_ALZ_DUTTX, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::BeamformingCalibrationChannelEstDutRX(
    double *dutChannelEst,
    int dutChannelEstLength)
{
    ExchangeBuff pstSendBuff;

    m_Beaforming.DUT_channelEst_Length = dutChannelEstLength;
    memset(m_Beaforming.DUT_channelEst_Data, 0, sizeof(m_Beaforming.DUT_channelEst_Data));
    memcpy(m_Beaforming.DUT_channelEst_Data, dutChannelEst, dutChannelEstLength * sizeof(double));
    m_Beaforming.DUT_mean_of_angle_deltas = 0;
    m_Beaforming.DUT_std_of_angle_deltas = 0;

    pstSendBuff.chpHead = (char *)m_Beaforming.DUT_channelEst_Data;
    pstSendBuff.buff_len = dutChannelEstLength * sizeof(double);
    pstSendBuff.data_len = dutChannelEstLength * sizeof(double);
    return Exchange(0, CMD_BEAMFORMING_CLR_ALZ_DUTRX, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::BeamformingCalibrationResult(
    double *resultAngle,
    int *resultAngleLength)
{
    ExchangeBuff pstRecvBuff;

    m_Beaforming.Result_Length = 0;
    memset(m_Beaforming.Result_angle, 0, sizeof(m_Beaforming.Result_angle));
    m_Beaforming.Result_Errorcode = 0;

    pstRecvBuff.chpHead = (char *)m_Beaforming.Result_angle;
    pstRecvBuff.buff_len = sizeof(m_Beaforming.Result_angle);
    pstRecvBuff.data_len = 0;

    int err = Exchange(0, CMD_BEAMFORMING_CLR_RST, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);

    if (WT_ERR_CODE_OK == err)
    {
        m_Beaforming.Result_Length = pstRecvBuff.data_len;
        *resultAngleLength = m_Beaforming.Result_Length / sizeof(double);
        memcpy((char *)resultAngle, (char *)m_Beaforming.Result_angle, m_Beaforming.Result_Length);
    }
    return err;
}

int InstrumentHandle::BeamformingVerification(double *diffPower)
{
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)diffPower;
    pstRecvBuff.buff_len = sizeof(double);
    pstRecvBuff.data_len = 0;

    int err = Exchange(0, CMD_BEAMFORMING_CLR_VERIFICATION, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);

    return err;
}

static void SetAlzParamWifiDefault(AlzParamWifi *analyzeParamWifi, int demod)
{
    analyzeParamWifi->AutoDetect = WT_BW_AUTO_DETECT;
    analyzeParamWifi->Method11b = WT_11B_STANDARD_TX_ACC;
    analyzeParamWifi->DCRemoval = WT_DC_REMOVAL_OFF;
    analyzeParamWifi->EqTaps = WT_DC_REMOVAL_OFF;
    analyzeParamWifi->PhsCorrMode11B = WT_PH_CORR_11b_ON;

    analyzeParamWifi->PhsCorrMode = WT_PH_CORR_SYM_BY_SYM;
    analyzeParamWifi->ChEstimate = WT_CH_EST_RAW;
    analyzeParamWifi->SynTimeCorr = WT_SYM_TIM_ON;
    analyzeParamWifi->FreqSyncMode = WT_FREQ_SYNC_AUTO;
    analyzeParamWifi->AmplTrack = WT_AMPL_TRACK_OFF;

    analyzeParamWifi->OfdmDemodOn = 1;
    analyzeParamWifi->MIMOAnalysisMode = 0;
    analyzeParamWifi->MimoMaxPowerDiff = 30;

    analyzeParamWifi->Demode = demod;
    analyzeParamWifi->AutoDetect = WT_USER_DEFINED;
    analyzeParamWifi->ClockRate = 1;
    analyzeParamWifi->FullCRCFlag = 0;
}
int InstrumentHandle::BeamformingCalculateChannelProfile(
    int demod,
    double *resultBuf,
    int *resultLength)
{
    ExchangeBuff pstSendBuff;

    memset(&m_Beaforming, 0, sizeof(m_Beaforming));
    m_Beaforming.demod = demod;
    m_Beaforming.DUT_Type = iBEAFORMING_MTK_STATE_1;

    pstSendBuff.chpHead = (char *)&m_Beaforming.demod;
    pstSendBuff.buff_len = sizeof(m_Beaforming.demod);
    pstSendBuff.data_len = sizeof(m_Beaforming.demod);


    ExchangeBuff pstRecvBuff;
    size_t bufSize = sizeof(m_Beaforming.WT_channelEst_amplitude) + sizeof(m_Beaforming.WT_channelEst_angle) + 1024;
    std::unique_ptr<char[]> bufPtr(new (std::nothrow) char[bufSize]);
    char *tmpBuf = bufPtr.get();

    if (!tmpBuf)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }
    pstRecvBuff.chpHead = (char *)tmpBuf;
    pstRecvBuff.buff_len = bufSize;
    pstRecvBuff.data_len = 0;

    int err = Exchange(0, CMD_BEAMFORMING_CAL_PROFILE, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);
    if (WT_ERR_CODE_OK == err)
    {
        *resultLength = min(*resultLength, (pstRecvBuff.data_len / sizeof(double)));
        double *ptrBuf = (double*)tmpBuf;
        for (int i = 0; i < *resultLength; i++)
        {
            resultBuf[i] = ptrBuf[i];//amplitude, phase
        }
    }
    memset(&m_Beaforming, 0, sizeof(m_Beaforming));

    return err;
}

int InstrumentHandle::BeamformingCalculateChannelAmplitudeandAngleBCM(int *ValidSpatialStreams, int *DataLen, double *resultBuf, int *resultLength)
{
    ExchangeBuff pstRecvBuff;
    size_t bufSize = sizeof(m_Beaforming.WT_channelEst_amplitude) + sizeof(m_Beaforming.WT_channelEst_angle) + 1024;
    std::unique_ptr<char[]> bufPtr(new (std::nothrow) char[bufSize]);
    char *tmpBuf = bufPtr.get();

    if (!tmpBuf)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }
    pstRecvBuff.chpHead = (char *)tmpBuf;
    pstRecvBuff.buff_len = bufSize;
    pstRecvBuff.data_len = 0;

    int err = Exchange(0, CMD_BEAMFORMING_CAL_BCM_AMP_ANGLE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);
    if (WT_ERR_CODE_OK == err)
    {
        *resultLength = min(*resultLength, (pstRecvBuff.data_len / sizeof(double) - 1));
        *ValidSpatialStreams = *(int *)tmpBuf;
        *DataLen = *(int *)(tmpBuf + sizeof(int));
        //printf("ValidSpatialStreams=%d, Datalen=%d,resultLength = %d\n",*ValidSpatialStreams, *DataLen, *resultLength);
        double *ptrBuf = (double*)(tmpBuf + sizeof(int) * 2);
        for (int i = 0; i < *resultLength; i++)
        {
            resultBuf[i] = ptrBuf[i];//amplitude, phase
        }
    }

    return err;
}

int InstrumentHandle::SetVSAParam(VsaParameter *vsaParam, ExtendVsaParameter *extParam)
{
    return SetVSAParam(vsaParam, extParam, IOCONTROL_VSA);
}

bool InstrumentHandle::isSameVsaParam(VsaParameter *param)
{
    if (m_VsaParamUpdate)
    {
        m_VsaParamUpdate = false;
        return false;
    }
    const double scaleSecond = 1e6;//1s = 1e6 us
    return (
        (0 == memcmp(&m_vsaParam.Demode, &param->Demode, sizeof(m_vsaParam.Demode)))
        && (0 == memcmp(m_vsaParam.RfPort, param->RfPort, sizeof(m_vsaParam.RfPort)))
        && (0 == memcmp(m_vsaParam.VsaUnitMask, param->VsaUnitMask, sizeof(m_vsaParam.VsaUnitMask)))
        && (0 == memcmp(&m_vsaParam.ValidNum, &param->ValidNum, sizeof(m_vsaParam.ValidNum)))
        && (0 == memcmp(&m_vsaParam.TrigType, &param->TrigType, sizeof(m_vsaParam.TrigType)))

        && (0 == UsualKit::DoubleCompareVector(m_vsaParam.ExtPathLoss, param->ExtPathLoss, ARRAYSIZE(m_vsaParam.ExtPathLoss)))
        && (0 == UsualKit::DoubleCompareVector(m_vsaParam.ExtPathLoss2, param->ExtPathLoss2, ARRAYSIZE(m_vsaParam.ExtPathLoss)))
        && (0 == UsualKit::DoubleCompareVector(m_vsaParam.MaxPower, param->MaxPower, ARRAYSIZE(m_vsaParam.MaxPower)))

        && (0 == UsualKit::DoubleCompareVector(&m_vsaParam.Freq, &param->Freq))
        && (0 == UsualKit::DoubleCompareVector(&m_vsaParam.Freq2, &param->Freq2))
        && (0 == UsualKit::DoubleCompareVector(&m_vsaParam.FreqOffset, &param->FreqOffset))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.SmpTime * scaleSecond, param->SmpTime * scaleSecond))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.SamplingFreq, param->SamplingFreq))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.TrigLevel, param->TrigLevel))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.TrigTimeout * scaleSecond, param->TrigTimeout * scaleSecond))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.TrigPretime * scaleSecond, param->TrigPretime * scaleSecond))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.MaxIFGGap * scaleSecond, param->MaxIFGGap * scaleSecond))
        && (0 == UsualKit::DoubleCompare(m_vsaParam.TimeoutWaiting * scaleSecond, param->TimeoutWaiting * scaleSecond))
        );
}


bool InstrumentHandle::isSameVsaParam(ExtendVsaParameter *param)
{
    if (m_VsaParamUpdate)
    {
        m_VsaParamUpdate = false;
        return false; 
    }
    const double scaleSecond = 1e6;//1s = 1e6 us
    return (
        (0 == memcmp(&m_vsaExternParam.WIFI8080DulPortMode, &param->WIFI8080DulPortMode, sizeof(m_vsaExternParam.WIFI8080DulPortMode)))
        && (0 == memcmp(m_vsaExternParam.VsaRfPort, param->VsaRfPort, sizeof(m_vsaExternParam.VsaRfPort)))

        && (0 == UsualKit::DoubleCompareVector(m_vsaExternParam.RfPort_MaxPower, param->RfPort_MaxPower, ARRAYSIZE(m_vsaExternParam.RfPort_MaxPower)))
        && (0 == UsualKit::DoubleCompareVector(m_vsaExternParam.RfPort_TrigLevel, param->RfPort_TrigLevel, ARRAYSIZE(m_vsaExternParam.RfPort_TrigLevel)))
        );
}

int InstrumentHandle::SetVSAParam(
    VsaParameter *vsaParam,
    ExtendVsaParameter *extParam,
    int controlType)
{
    A_ASSERT(vsaParam);
    int err = WT_ERR_CODE_OK;
    if (isSameVsaParam(vsaParam) && nullptr == extParam)
    {
        return m_LastSetVsaParamResult;
    }
#if 0
    if (isSameVsaParam(vsaParam) && nullptr != extParam && isSameVsaParam(extParam))
    {
        return m_LastSetVsaParamResult;
    }
#endif
    const int MaxBufSize = WT_SUB_TESTER_INDEX_MAX * MAX_BUFF_SIZE;//8*8时，避免内存不够
    char proBuff[MaxBufSize] = { 0 };
    char *curPos = proBuff;
    memcpy(curPos, &m_currSubTesterCount, sizeof(int));
    curPos += sizeof(int);
    unsigned int remainSize = MaxBufSize - sizeof(int);
    int testerIndex = 0;
    for (testerIndex = 0; testerIndex < m_currSubTesterCount; testerIndex++)
    {
        VsaCapParam vsaCapParam;
        memset(&vsaCapParam, 0, sizeof(vsaCapParam));

        vsaCapParam.Freq = vsaParam->Freq;
        vsaCapParam.Freq2 = vsaParam->Freq2;
        vsaCapParam.FreqOffset = vsaParam->FreqOffset;
        m_isvsa160mFlag = false;
        if ((vsaParam->Freq2 < 1.0) && ((vsaParam->Demode == WT_DEMOD_11AC_80_80M) || (vsaParam->Demode == WT_DEMOD_11AX_80_80M)))
        {
            vsaCapParam.Is160M = 1;
            m_isvsa160mFlag = true;
        }
        //300与200端口枚举不一致
        vsaCapParam.RFPort = vsaParam->RfPort[testerIndex] - 1;
        vsaCapParam.Type = m_currTestMode;
        if (UsualKit::DoubleCompare(vsaCapParam.Freq2, 0.0))
        {
            //vsaCapParam.Type = 1;
            m_vsaAc8080Flag = true;
        }
        else
        {
            //vsaCapParam.Type = 0;
            m_vsaAc8080Flag = false;
        }
        vsaCapParam.MasterMode = m_vsaMasterMode;
        vsaCapParam.SignalId = testerIndex;
        vsaCapParam.VsaMask = vsaParam->VsaUnitMask[testerIndex];
        vsaCapParam.TrigType = vsaParam->TrigType;
        vsaCapParam.Ampl = vsaParam->MaxPower[testerIndex];
        vsaCapParam.ExtPathLoss = vsaParam->ExtPathLoss[testerIndex];
        vsaCapParam.ExtPathLoss2 = vsaParam->ExtPathLoss2[testerIndex];
        //vsaCapParam不会用到
        //vsaCapParam.BaseBandGain=0;
        vsaCapParam.SamplingTime = vsaParam->SmpTime;
        vsaCapParam.SamplingFreq = vsaParam->SamplingFreq;
        //vsa最大采样点数
        const double max_sample_point = m_maxSampleRate * MAX_SAMPLEING_TIME;

        if (TESTER_RUN_DIGIT_IQ != m_TesterRunMode)
        {
            if (vsaCapParam.SamplingTime * vsaCapParam.SamplingFreq > max_sample_point)
            {
                return WT_ERR_CODE_UNKNOW_PARAMETER;
            }
        }

        if (vsaCapParam.SamplingFreq > m_maxSampleRate + 1.0)
        {
            return WT_ERR_CODE_SAMPLE_RATE_TOO_LARGE;
        }

        vsaCapParam.Demod = vsaParam->Demode;
        vsaCapParam.Demod |= 0xA5B70000;

        vsaCapParam.TrigPreTime = vsaParam->TrigPretime;

        if (TESTER_RUN_DIGIT_IQ != m_TesterRunMode)
        {
            //TrigPreTime的间隔限制在10us-300us之间
            const double min_TrigPretime = 0 * Us;
            const double max_TrigPreTime = 300 * Us;

            if (vsaParam->TrigPretime < min_TrigPretime)
            {
                vsaCapParam.TrigPreTime = min_TrigPretime;
            }
            // if (vsaParam->TrigPretime > max_TrigPreTime)
            // {
            //     vsaCapParam.TrigPreTime = max_TrigPreTime;
            // }
        }

        vsaCapParam.TrigTimeout = vsaParam->TrigTimeout;
        vsaCapParam.TrigLevel = vsaParam->TrigLevel;
        vsaCapParam.AllocTimeout = (int)vsaParam->TimeoutWaiting;
        vsaCapParam.MaxIFG = vsaParam->MaxIFGGap;
        //MaxIFG的间隔设置在10ms-2000ms之间
        const double min_MaxIFG = 10 * Ms;
        const double max_MaxIFG = 2000 * Ms;
        if (vsaParam->MaxIFGGap < min_MaxIFG)
        {
            vsaCapParam.MaxIFG = min_MaxIFG;
        }
        if (vsaParam->MaxIFGGap > max_MaxIFG)
        {
            vsaCapParam.MaxIFG = max_MaxIFG;
        }

        if (remainSize < sizeof(VsaCapParam))
        {
            break;
        }

        if (extParam)
        {
            if (fabs(vsaParam->Freq2) > 1.0)
            {
                vsaCapParam.DulPortMode = 1;
                vsaCapParam.RFPort = extParam->VsaRfPort[testerIndex * 2] - 1;
                vsaCapParam.RFPort2 = extParam->VsaRfPort[testerIndex * 2 + 1] - 1;

                vsaCapParam.Ampl = extParam->RfPort_MaxPower[testerIndex * 2];
                vsaCapParam.Ampl2 = extParam->RfPort_MaxPower[testerIndex * 2 + 1];
                vsaCapParam.TrigLevel = extParam->RfPort_TrigLevel[testerIndex * 2];
                vsaCapParam.TrigLevel2 = extParam->RfPort_TrigLevel[testerIndex * 2 + 1];
            }
            vsaCapParam.DCOffsetI = extParam->DCOffsetI;
            vsaCapParam.DCOffsetQ = extParam->DCOffsetQ;
        }

        memcpy(curPos, &vsaCapParam, sizeof(VsaCapParam));
        curPos += sizeof(VsaCapParam);
        remainSize -= sizeof(VsaCapParam);
    }
    if (testerIndex < m_currSubTesterCount)
    {
        err = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    else
    {
        size_t SendTimeout_ms = 1000;//1000ms
        size_t RecvTimeout_ms = 3000;//3000ms
        RecvTimeout_ms += static_cast<int>(vsaParam->TimeoutWaiting) * 1000;

        CmdHeader tmpHeader;
        ExchangeBuff pstSendBuff;
        pstSendBuff.chpHead = proBuff;
        pstSendBuff.buff_len = (sizeof(VsaCapParam) * m_currSubTesterCount) + sizeof(int);
        pstSendBuff.data_len = (sizeof(VsaCapParam) * m_currSubTesterCount) + sizeof(int);
        err = Exchange(0, CMD_SET_VSA_PARAM, &pstSendBuff, 1, nullptr, 0, controlType, SendTimeout_ms, RecvTimeout_ms, &tmpHeader);
        //不管是否配置成功，都保存VSA当时的参数
        //      if(WT_ERR_CODE_OK == err)
        {
            memcpy(&m_VsaParameterBack, &m_vsaParam, sizeof(VsaParameter));
            memcpy(&m_vsaParam, vsaParam, sizeof(VsaParameter));
            if (extParam)
            {
                memcpy(&m_vsaExternParam, extParam, sizeof(ExtendVsaParameter));
            }
            else
            {
                memset(&m_vsaExternParam, 0, sizeof(m_vsaExternParam));
            }
            m_VsaParamSerialNum = tmpHeader.SerialNum;
        }
    }
    m_LastSetVsaParamResult = err;
    if (WT_ERR_CODE_OK == err)
    {
        ProcWifiVsaSpecial();
    }
    return err;
}

int InstrumentHandle::SetVSAAverageParam(VsaAvgParameter *avgParam)
{
    //设置平均参数
    int averageType = CAPTURE_AVERAGE;
    int averageMethod = ARITHMETIC_AVERAGE;
    switch (avgParam->AvgType)
    {
    case WT_CAPTURE_ARITHMETIC_AVERAGE:
    {
        averageType = CAPTURE_AVERAGE;
        averageMethod = ARITHMETIC_AVERAGE;
        break;
    }
    case WT_CAPTURE_MOVING_AVERAGE:
    {
        averageType = CAPTURE_AVERAGE;
        averageMethod = MOVING_AVERAGE;
        break;
    }
    case WT_SEGMENT_ARITHMETIC_AVERAGE:
    {
        averageType = SEGMENT_AVERAGE;
        averageMethod = ARITHMETIC_AVERAGE;
        break;
    }
    case WT_SEGMENT_MOVING_AVERAGE:
    {
     averageType = SEGMENT_AVERAGE;
     averageMethod = MOVING_AVERAGE;
     break;
    }
    default:
        break;
    }
    int err = SetVSAAverageParam(averageType, averageMethod, avgParam->AvgCount);
    return err;
}


bool InstrumentHandle::CompareAnalyzeParam(
    int sigType,
    void *analyzeParam,
    int paramSize,
    int controlType)
{
    bool bRet = false;
    int index = 0;
    switch (controlType)
    {
    case IOCONTROL_VSG:
        index = IOCONTROL_VSG;
        break;
    default:
        index = IOCONTROL_VSA;
        break;
    }

    if (0 == memcmp(analyzeParam, m_AnalyzeParamInfo[index].analyzeParam, paramSize) &&
        paramSize == m_AnalyzeParamInfo[index].paramSize &&
        sigType == m_AnalyzeParamInfo[index].sigType)
    {
        bRet = true;
    }
    else
    {
        m_AnalyzeParamInfo[index].sigType = sigType;
        m_AnalyzeParamInfo[index].paramSize = paramSize;
        m_AnalyzeParamInfo[index].controlType = controlType;
        memcpy(m_AnalyzeParamInfo[index].analyzeParam, analyzeParam, paramSize);
    }

    if (IOCONTROL_VSA == index && WT_ALZ_PARAM_WIFI == m_AnalyzeParamInfo[index].sigType)
    {
        AlzParamWifi *wifi_param = (AlzParamWifi *)m_AnalyzeParamInfo[index].analyzeParam;
        m_VsaClockRate = CheckPnClockRate(wifi_param->ClockRate);
    }
    return bRet;
}

int InstrumentHandle::SetAnalyzeParam(
    int signalType,
    void *analyzeParam,
    int paramSize)
{
    return SetAnalyzeParam(signalType, analyzeParam, paramSize, IOCONTROL_VSA);
}

int InstrumentHandle::SetAnalyzeParam(
    int signalType,
    void *analyzeParam,
    int paramSize,
    int controlType)
{
    //比较分析参数配置信息，如果相同的，则直接返回OK。否则下发对应的配置信息给仪器
    int err = WT_ERR_CODE_OK;
    const int MaxBufSize = paramSize + MAX_BUFF_SIZE;
    do
    {
        if (CompareAnalyzeParam(signalType, analyzeParam, paramSize, controlType))
        {
            break;
        }
        std::unique_ptr<char[]>tmpBuf(new (std::nothrow) char[MaxBufSize]);
        char *proBuff = tmpBuf.get();

        memset(proBuff, 0, MaxBufSize);
        memcpy(proBuff, &signalType, sizeof(int));
        memcpy(proBuff + sizeof(int), analyzeParam, paramSize);

        ExchangeBuff pstSendBuff;
        pstSendBuff.chpHead = proBuff;
        pstSendBuff.buff_len = paramSize + sizeof(int);
        pstSendBuff.data_len = paramSize + sizeof(int);
        err = Exchange(0, CMD_SET_VSA_ALZ_PARAM, &pstSendBuff, 1, nullptr, 0, controlType);

    } while (0);

    return err;
}

int InstrumentHandle::SetExternAnalyzeParam(
    int demod,
    int ParamType,
    void *param,
    int paramSize)
{
    return SetExternAnalyzeParam(demod, ParamType, param, paramSize, IOCONTROL_VSA);
}

void InstrumentHandle::DefaultTbAlzParam(int demod, AlzParamAxTriggerBase *alzTB)
{
    if (WT_DEMOD_11AX_20M <= demod && demod <= WT_DEMOD_11AX_80_80M)
    {
        for (int i = 0; i < alzTB->UserNum; i++)
        {
            if (1 > alzTB->Stream[i] || alzTB->Stream[i] > 8)
            {
                alzTB->Stream[i] = 1;
            }
            if (0 > alzTB->MCS[i] || alzTB->MCS[i] > 13)
            {
                alzTB->MCS[i] = 7;
            }

            if (0 > alzTB->Segment[i] || alzTB->Segment[i] > 1)
            {
                alzTB->Segment[i] = 0;
            }

            if (0 > alzTB->RUIndex[i] || alzTB->RUIndex[i] > 68)
            {
                alzTB->RUIndex[i] = 0;
            }

            if (0 > alzTB->Conding[i] || alzTB->Conding[i] > 1)
            {
                alzTB->Conding[i] = 1;
            }

            if (0 > alzTB->DCM[i] || alzTB->DCM[i] > 1)
            {
                alzTB->DCM[i] = 0;
            }

            if (1 > alzTB->AID[i] || alzTB->AID[i] > 2007)
            {
                alzTB->AID[i] = 1;
            }
        }
    }
    else if (WT_DEMOD_11BE_20M <= demod && demod <= WT_DEMOD_11BE_160_160M)
    {
        for (int i = 0; i < alzTB->UserNum; i++)
        {
            if (1 > alzTB->Stream[i] || alzTB->Stream[i] > 8)
            {
                alzTB->Stream[i] = 1;
            }
            if (0 > alzTB->MCS[i] || alzTB->MCS[i] > 15)
            {
                alzTB->MCS[i] = 7;
            }

            if (0 > alzTB->Segment[i] || alzTB->Segment[i] > 4)
            {
                alzTB->Segment[i] = 0;
            }

            if (0 > alzTB->RUIndex[i] || alzTB->RUIndex[i] > 106)
            {
                alzTB->RUIndex[i] = 0;
            }

            if (0 > alzTB->Conding[i] || alzTB->Conding[i] > 1)
            {
                alzTB->Conding[i] = 1;
            }

            if (0 > alzTB->DCM[i] || alzTB->DCM[i] > 1)
            {
                alzTB->DCM[i] = 0;
            }

            if (1 > alzTB->AID[i] || alzTB->AID[i] > 2007)
            {
                alzTB->AID[i] = 1;
            }
        }
    }

}

static int TbMUMIMO2TbStruct(AlzParamAxTriggerBase &alzTB, void *param, int paramSize)
{
    int iRet = WT_ERR_CODE_OK;
    bool isHaveTbVar = false;

    Set11AX_TB *tbFile = static_cast<Set11AX_TB*>(param);
    //Wave generator与VSA分析之间的映射关系。
    const int NumLtfSymbols[][2] = { { 0, 1 },{ 1, 2 },{ 2, 4 },{ 3, 6 },{ 4, 8 } };
    int UserCnt = 0;
    do
    {
        if ((sizeof(Set11AX_TB) != paramSize) &&
            (sizeof(Set11AX_TB) + sizeof(AxTbVariableParameter) != paramSize))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (paramSize == sizeof(Set11AX_TB) + sizeof(AxTbVariableParameter))
        {
            isHaveTbVar = true;
        }

        alzTB.TBFlag = 1;
        alzTB.UserID = 1;
        alzTB.LDPCSym = 0;
        alzTB.AFactor = 1;
        alzTB.PEDisamb = 1;

        alzTB.UserNum = 0;//TODO
        alzTB.GILTFSize = tbFile->GILTFSize;
        alzTB.NumLTF = NumLtfSymbols[tbFile->NumLTFSymbols][1];

        alzTB.STBC = tbFile->STBC;
        alzTB.Doppler = tbFile->Doppler;
        alzTB.Midamble_Periodicity = tbFile->Midamble_Periodicity;
        alzTB.TBMUMIMOFlag = tbFile->TBMUMIMOFlag;

        for (int segmentID = 0; segmentID < MAX_SEGMENT; segmentID++)
        {
            for (int i = 0; i < tbFile->RUNum[segmentID]; i++)
            {
                for (int j = 0; j < tbFile->RU[segmentID][i].UserNum; j++)
                {
                    alzTB.Stream[UserCnt] = tbFile->RU[segmentID][i].User[j].NSS;
                    alzTB.MCS[UserCnt] = tbFile->RU[segmentID][i].User[j].MCS;
                    alzTB.Segment[UserCnt] = tbFile->RU[segmentID][i].User[j].SegmentIndex;
                    alzTB.RUIndex[UserCnt] = tbFile->RU[segmentID][i].User[j].RuIndex;
                    alzTB.Conding[UserCnt] = tbFile->RU[segmentID][i].User[j].CodingType;
                    alzTB.DCM[UserCnt] = tbFile->RU[segmentID][i].User[j].DCM;
                    alzTB.AID[UserCnt] = tbFile->RU[segmentID][i].User[j].AID;
                    alzTB.NSSStart[UserCnt] = tbFile->RU[segmentID][i].User[j].NSSStart;
                    UserCnt++;
                }
            }
        }
        alzTB.UserNum = UserCnt;

        if (isHaveTbVar)
        {
            char *tmpPtr = static_cast<char *>(param);
            AxTbVariableParameter *tbExtInfo = (AxTbVariableParameter *)(tmpPtr + sizeof(Set11AX_TB));
            alzTB.LDPCSym = tbExtInfo->LDPCSym;
            alzTB.AFactor = tbExtInfo->AFactor;
            alzTB.PEDisamb = tbExtInfo->PEDisamb;
            alzTB.Doppler = tbExtInfo->Doppler;
            alzTB.Midamble_Periodicity = tbExtInfo->Midamble_Periodicity;
        }

    } while (0);

    return iRet;
}

static int TbMUMIMO2TbStruct_11Be(AlzParamAxTriggerBase &alzTB, void *param, int paramSize)
{
    int iRet = WT_ERR_CODE_OK;
    bool isHaveTbVar = false;

    Set11BE_TB *tbFile = static_cast<Set11BE_TB*>(param);
    //Wave generator与VSA分析之间的映射关系。
    const int NumLtfSymbols[][2] = { { 0, 1 },{ 1, 2 },{ 2, 4 },{ 3, 6 },{ 4, 8 } };
    int UserCnt = 0;
    do
    {
        if ((sizeof(Set11BE_TB) != paramSize) &&
            (sizeof(Set11BE_TB) + sizeof(AxTbVariableParameter) != paramSize))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (paramSize == sizeof(Set11BE_TB) + sizeof(AxTbVariableParameter))
        {
            isHaveTbVar = true;
        }

        alzTB.TBFlag = 1;
        alzTB.UserID = 1;
        alzTB.LDPCSym = 0;
        alzTB.AFactor = 1;
        alzTB.PEDisamb = 1;

        alzTB.UserNum = 0;//TODO
        alzTB.GILTFSize = tbFile->GILTFSize;
        alzTB.NumLTF = NumLtfSymbols[tbFile->NumLTFSymbols][1];

        alzTB.STBC = tbFile->STBC;
        alzTB.Doppler = tbFile->Doppler;
        alzTB.Midamble_Periodicity = tbFile->Midamble_Periodicity;
        alzTB.TBMUMIMOFlag = tbFile->TBMUMIMOFlag;

        for (int segmentID = 0; segmentID < BE_MAX_SEGMENT; segmentID++)
        {
            for (int i = 0; i < tbFile->RUNum[segmentID]; i++)
            {
                for (int j = 0; j < tbFile->RU[segmentID][i].UserNum; j++)
                {
                    alzTB.Stream[UserCnt] = tbFile->RU[segmentID][i].User[j].NSS;
                    alzTB.MCS[UserCnt] = tbFile->RU[segmentID][i].User[j].MCS;
                    alzTB.Segment[UserCnt] = tbFile->RU[segmentID][i].User[j].SegmentIndex;
                    alzTB.RUIndex[UserCnt] = tbFile->RU[segmentID][i].User[j].RuIndex;
                    alzTB.Conding[UserCnt] = tbFile->RU[segmentID][i].User[j].CodingType;
                    alzTB.DCM[UserCnt] = tbFile->RU[segmentID][i].User[j].DCM;
                    alzTB.AID[UserCnt] = tbFile->RU[segmentID][i].User[j].AID;
                    alzTB.NSSStart[UserCnt] = tbFile->RU[segmentID][i].User[j].NSSStart;
                    UserCnt++;
                }
            }
        }
        alzTB.UserNum = UserCnt;

        if (isHaveTbVar)
        {
            char *tmpPtr = static_cast<char *>(param);
            AxTbVariableParameter *tbExtInfo = (AxTbVariableParameter *)(tmpPtr + sizeof(Set11BE_TB));
            alzTB.LDPCSym = tbExtInfo->LDPCSym;
            alzTB.AFactor = tbExtInfo->AFactor;
            alzTB.PEDisamb = tbExtInfo->PEDisamb;
            alzTB.Doppler = tbExtInfo->Doppler;
            alzTB.Midamble_Periodicity = tbExtInfo->Midamble_Periodicity;
        }

    } while (0);

    return iRet;
}

static int TbMUMIMO2TbStruct_11AZ(AlzParamAxTriggerBase &alzTB, void *param, int paramSize)
{
    int iRet = WT_ERR_CODE_OK;
    bool isHaveTbVar = false;

    Set11AZ_TB *tbFile = static_cast<Set11AZ_TB*>(param);
    //Wave generator与VSA分析之间的映射关系。
    const int NumLtfSymbols[][2] = { { 0, 1 },{ 1, 2 },{ 2, 4 },{ 3, 6 },{ 4, 8 } };
    int UserCnt = 0;
    do
    {
        if ((sizeof(Set11AZ_TB) != paramSize) &&
            (sizeof(Set11AZ_TB) + sizeof(AxTbVariableParameter) != paramSize))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (paramSize == sizeof(Set11AZ_TB) + sizeof(AxTbVariableParameter))
        {
            isHaveTbVar = true;
        }

        alzTB.TBFlag = 1;
        alzTB.UserID = 1;
        alzTB.LDPCSym = 0;
        alzTB.AFactor = 1;
        alzTB.PEDisamb = 1;

        alzTB.UserNum = 0;//TODO
        alzTB.GILTFSize = tbFile->GILTFSize;
        alzTB.NumLTF = NumLtfSymbols[tbFile->NumLTFSymbols][1];

        alzTB.STBC = tbFile->STBC;
        alzTB.Doppler = tbFile->Doppler;
        alzTB.Midamble_Periodicity = tbFile->Midamble_Periodicity;
        alzTB.TBMUMIMOFlag = tbFile->TBMUMIMOFlag;

        int segMaxCnt = sizeof(tbFile->RUNum)/sizeof(tbFile->RUNum[0]);
        for (int segmentID = 0; segmentID < segMaxCnt; segmentID++)
        {
            for (int i = 0; i < tbFile->RUNum[segmentID]; i++)
            {
                for (int j = 0; j < tbFile->RU[segmentID][i].UserNum; j++)
                {
                    alzTB.Stream[UserCnt] = tbFile->RU[segmentID][i].User[j].NSS;
                    alzTB.MCS[UserCnt] = tbFile->RU[segmentID][i].User[j].MCS;
                    alzTB.Segment[UserCnt] = tbFile->RU[segmentID][i].User[j].SegmentIndex;
                    alzTB.RUIndex[UserCnt] = tbFile->RU[segmentID][i].User[j].RuIndex;
                    alzTB.Conding[UserCnt] = tbFile->RU[segmentID][i].User[j].CodingType;
                    alzTB.DCM[UserCnt] = tbFile->RU[segmentID][i].User[j].DCM;
                    alzTB.AID[UserCnt] = tbFile->RU[segmentID][i].User[j].AID;
                    alzTB.NSSStart[UserCnt] = tbFile->RU[segmentID][i].User[j].NSSStart;
                    UserCnt++;
                }
            }
        }
        alzTB.UserNum = UserCnt;

        if (isHaveTbVar)
        {
            char *tmpPtr = static_cast<char *>(param);
            AxTbVariableParameter *tbExtInfo = (AxTbVariableParameter *)(tmpPtr + sizeof(Set11AZ_TB));
            alzTB.LDPCSym = tbExtInfo->LDPCSym;
            alzTB.AFactor = tbExtInfo->AFactor;
            alzTB.PEDisamb = tbExtInfo->PEDisamb;
            alzTB.Doppler = tbExtInfo->Doppler;
            alzTB.Midamble_Periodicity = tbExtInfo->Midamble_Periodicity;
        }

    } while (0);

    return iRet;
}

int InstrumentHandle::File2TBParam(int ParamType, void *src_param, int paramSize, AlzParamAxTriggerBase *dest, int demod)
{
    if (AX_TB_REF_FILE == ParamType)
    {
        int header[2] = { 0 };
        char *tmpPtr = static_cast<char *>(src_param);
        memcpy(header, src_param, sizeof(header));
        if (WAVE_CFG_BASE_HEAD == header[0] && WAVE_CFG_SUB_TB_MUMIMO == header[1])
        {
            tmpPtr += sizeof(header);
            paramSize -= sizeof(header);
            TbMUMIMO2TbStruct(*dest, tmpPtr, paramSize);
        }
        else if (WAVE_CFG_BASE_HEAD == header[0] && WAVE_CFG_SUB_EHT_TB == header[1])
        {
            tmpPtr += sizeof(header);
            paramSize -= sizeof(header);
            TbMUMIMO2TbStruct_11Be(*dest, tmpPtr, paramSize);
        }
        else if (WAVE_CFG_BASE_HEAD == header[0] && WAVE_CFG_SUB_AZ_HE_TB == header[1])
        {
            tmpPtr += sizeof(header);
            paramSize -= sizeof(header);
            TbMUMIMO2TbStruct_11AZ(*dest, tmpPtr, paramSize);
        }
        else
        {
            TbMUMIMO2TbStruct(*dest, tmpPtr, paramSize);
        }
    }
    else if (AX_TB_BASE_TYPE == ParamType && sizeof(AlzParamAxTriggerBase) == paramSize)
    {
        DefaultTbAlzParam(demod, static_cast<AlzParamAxTriggerBase*>(src_param));
        memcpy(dest, static_cast<AlzParamAxTriggerBase*>(src_param), sizeof(AlzParamAxTriggerBase));
    }

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::File2SLEParam(int ParamType, void* src_param, int paramSize, AlzParamSparkLink* dest)
{
	int iRet = WT_ERR_CODE_OK;
	GenWaveGleStruct* tmpPtr = static_cast<GenWaveGleStruct*>(src_param);
	dest->FrmType = tmpPtr->commonParam.subType;
	dest->Bandwidth = tmpPtr->commonParam.bandwidth;
	dest->Scramble = tmpPtr->GlePacketSet.Scramble;
	dest->BoardIndex = tmpPtr->GlePacketSet.ChannelNo;
	dest->PID = tmpPtr->GlePacketSet.PhyID;
	dest->SlotIndex = tmpPtr->GlePacketSet.SlotIndex;
	dest->PayloadCrcSeed = tmpPtr->GlePacketSet.CrcDataSeed;
	dest->MSeqNo = tmpPtr->GlePacketSet.MSeqNo;
	if (tmpPtr->GlePacketSet.CrcType == 0)
	{
		dest->PayloadCrcType = 1;

	}
	else if (tmpPtr->GlePacketSet.CrcType == 1)
	{
		dest->PayloadCrcType = 2;

	}
	if (tmpPtr->GlePacketSet.PilotDensity == 0)
	{
		dest->PilotDens = 4;
	}
	else if (tmpPtr->GlePacketSet.PilotDensity == 1)
	{
		dest->PilotDens = 8;

	}
	else if (tmpPtr->GlePacketSet.PilotDensity == 2)
	{
		dest->PilotDens = 16;
	}
	else if (tmpPtr->GlePacketSet.PilotDensity == 3)
	{
		dest->PilotDens = 0;
	}
	dest->CtrlInfoType = tmpPtr->GlePacketSet.CtrlInfoType;
	dest->SyncSource = tmpPtr->GlePacketSet.SyncSource;
	memcpy(dest->SyncSeq, tmpPtr->GlePacketSet.SyncSeq, sizeof(tmpPtr->GlePacketSet.SyncSeq));

	if (tmpPtr->GlePacketSet.CtrlInfoType == 0 || tmpPtr->GlePacketSet.CtrlInfoType == 11)
	{
		dest->ChannelType = 2;
	}
	else
	{
		dest->ChannelType = 1;
	}
    
    if (dest->CtrlInfoType == 0)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA1.Mcs;
	}
	else if (dest->CtrlInfoType == 1)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA2.Mcs;
	}
	else if (dest->CtrlInfoType == 2)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA3.Mcs;
	}
	else if (dest->CtrlInfoType == 3)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA4.Mcs;
	}
	else if (dest->CtrlInfoType == 4)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA5.Mcs;
	}
	else if (dest->CtrlInfoType == 5)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA6.Mcs;
	}
	else if (dest->CtrlInfoType == 6)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeA7.Mcs;
	}
	else if (dest->CtrlInfoType == 7)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeB1.Mcs;
	}
	else if (dest->CtrlInfoType == 9)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeB3.Mcs;
	}
	else if (dest->CtrlInfoType == 10)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeB4.Mcs;
	}
	else if (dest->CtrlInfoType == 11)
	{
		dest->MCS = tmpPtr->GlePacketSet.CtrlInfoTypeB5.Mcs;
	}
    
	return iRet;
}

void GetAlzParam4G(void *src_param, int paramSize, AlzParam3GPP &dest)
{
    //TODO:目前在SCPI层解析
    // Alg_3GPP_WaveGenType GenParam;
    // memcpy(&GenParam, static_cast<Alg_3GPP_WaveGenType*>(src_param), sizeof(Alg_3GPP_WaveGenType));
    // if(GenParam.LinkDirect == ALG_3GPP_UL)
    // {
    //     dest.LTE.CarrAggrState = GenParam.UL.MultiCell.CarrAggrState;
    //     for(int i=0;i<ALG_4G_MAX_CELL_NUM;++i)
    //     {
    //         dest.LTE.Cell[i].CellIdx = GenParam.UL.MultiCell.Cell[i].CellIdx;
    //         dest.LTE.Cell[i].State = GenParam.UL.MultiCell.Cell[i].State;
    //         dest.LTE.Cell[i].PhyCellID = GenParam.UL.MultiCell.Cell[i].PhyCellID;
    //         dest.LTE.Cell[i].ChannelBW = GenParam.UL.MultiCell.Cell[i].ChannelBW;
    //         dest.LTE.Cell[i].Duplexing = GenParam.UL.MultiCell.Cell[i].Duplexing;
    //         dest.LTE.Cell[i].ULDLConfig = GenParam.UL.MultiCell.Cell[i].ULDLConfig;
    //         dest.LTE.Cell[i].SpecialSubfrmConfig = GenParam.UL.MultiCell.Cell[i].SpecialSubfrmCfg;
    //     }
    //     dest.LTE.CyclicPrefix = GenParam.UL.MultiCell.CyclicPrefix;
    //     dest.LTE.UeID = GenParam.UL.Ue.UeID;
    //     dest.LTE.ChanType = ALG_4G_PUSCH;//目前只考虑PUSCH，后面增加新的子帧数据段可能要多取几次。
    //     for(int i=0;i<ALG_4G_MAX_CELL_NUM;++i)
    //     {
    //         dest.LTE.Pusch[i].CellIdx = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].CellIdx;
    //         dest.LTE.Pusch[i].State = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].State;
    //         dest.LTE.Pusch[i].RBNum = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].RBNum[0];
    //         dest.LTE.Pusch[i].RBOffset = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].RBOffset[0];
    //         dest.LTE.Pusch[i].Precoding = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Precoding;
    //         dest.LTE.Pusch[i].LayerNum = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].LayerNum;
    //         dest.LTE.Pusch[i].AntennaNum = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].AntennaNum;
    //         dest.LTE.Pusch[i].CodebookIdx = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].CodebookIdx;
    //         dest.LTE.Pusch[i].GroupHop = GenParam.UL.MultiCell.GroupHop;
    //         dest.LTE.Pusch[i].SequenceHop = GenParam.UL.MultiCell.SequenceHop;
    //         dest.LTE.Pusch[i].DeltaSeqShift = GenParam.UL.MultiCell.DeltaSeqShift;
    //         dest.LTE.Pusch[i].N1Dmrs = GenParam.UL.MultiCell.Cell[i].N1Dmrs;
    //         dest.LTE.Pusch[i].CyclicShiftField = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].CyclicShiftField;
    //         dest.LTE.Pusch[i].Codeword = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Codeword;
    //         dest.LTE.Pusch[i].Modulate[0] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.Modulate[0];
    //         dest.LTE.Pusch[i].Modulate[1] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.Modulate[1];
    //         dest.LTE.Pusch[i].ChanDecodeState = GenParam.UL.Ue.Pusch[i].ChanCodingState;
    //         dest.LTE.Pusch[i].Scramble = GenParam.UL.Ue.Pusch[i].Scramble;
    //         dest.LTE.Pusch[i].McsCfgMode = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.McsCfgMode;
    //         dest.LTE.Pusch[i].Mcs[0] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.Mcs[0];
    //         dest.LTE.Pusch[i].Mcs[1] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.Mcs[1];
    //         dest.LTE.Pusch[i].PayloadSize[0] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.PayloadSize[0];
    //         dest.LTE.Pusch[i].PayloadSize[1] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.PayloadSize[1];
    //         dest.LTE.Pusch[i].RedunVerIdx[0] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.RedunVerIdx[0];
    //         dest.LTE.Pusch[i].RedunVerIdx[1] = GenParam.UL.Chan[dest.LTE.MeasSubfrmIdx].Pusch[i].Encode.RedunVerIdx[1];
    //     }
    // }
    // else
    // {
    //     ;//下行信号的参数获取
    // }
    return;
}

int InstrumentHandle::FileRefParam(int ParamType, void *src_param, int paramSize, AnalyzeParam *dest)
{
    if (IsAlg3GPPStandardType(ParamType))
    {
        int header[2] = { 0 };
        char *tmpPtr = static_cast<char *>(src_param);
        memcpy(header, src_param, sizeof(header));

        if (WAVE_CFG_BASE_HEAD == header[0] && WAVE_CFG_SUB_LTE == header[1])
        {
            tmpPtr += sizeof(header);
            paramSize -= sizeof(header);
            GetAlzParam4G(tmpPtr, paramSize, dest->analyzeParam3GPP);
        }
    }
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SetExternAnalyzeParam(
    int demod,
    int ParamType,
    void *param,
    int paramSize,
    int controlType)
{
    int err = WT_ERR_CODE_OK;
    int offset = 0;
    AlzParamAxTriggerBase alzTB;
    char *proBuff = nullptr;
    int bufSize = 1024;
    unique_ptr<char[]> tmpBuf = nullptr;
    if (!param && AX_TB_ANALYZE_EXIT == ParamType)
    {
        paramSize = sizeof(AlzParamAxTriggerBase);
    }
    bufSize = paramSize + 1024;

    do
    {
        tmpBuf.reset(new (std::nothrow) char[bufSize]);
        proBuff = tmpBuf.get();
        if (!proBuff)
        {
            err = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
            break;
        }
        memset(&alzTB, 0, sizeof(alzTB));

        if ((AX_TB_REF_FILE == ParamType) ||
            (AX_TB_BASE_TYPE == ParamType && sizeof(AlzParamAxTriggerBase) == paramSize))
        {
            File2TBParam(ParamType, param, paramSize, &alzTB, demod);
            param = &alzTB;
            paramSize = sizeof(alzTB);
            ParamType = AX_TB_BASE_TYPE;//读取TB配置文件，转换成TB分析结构体
        }

        memset(proBuff, 0, bufSize);
        //int Demo + int ParamType + param
        memcpy(proBuff + offset, &demod, sizeof(int));
        offset += sizeof(int);
        memcpy(proBuff + offset, &ParamType, sizeof(int));
        offset += sizeof(int);
        if (param)
        {
            memcpy(proBuff + offset, param, paramSize);
        }
        offset += paramSize;

        ExchangeBuff pstSendBuff;
        pstSendBuff.chpHead = proBuff;
        pstSendBuff.buff_len = offset;
        pstSendBuff.data_len = offset;
        err = Exchange(0, CMD_SET_EXTRAL_ALZ_PARAM, &pstSendBuff, 1, nullptr, 0, controlType);
    } while (0);

    return err;
}

int InstrumentHandle::SetAnalyzeGroupParam(char *anaParamString)
{
    //CMD_SET_ALZ_GROUP_BY_RESULT_STRING
    A_ASSERT(anaParamString);
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    int offset = 0;
    memset(sendBuff, 0, sizeof(sendBuff));

    vector<string>paramVector = UsualKit::split(anaParamString, ";");
    for (size_t i = 0; i < paramVector.size(); i++)
    {
        memcpy(sendBuff + offset, paramVector[i].c_str(), strlen(paramVector[i].c_str()) * sizeof(char));
        offset += VSA_RESULT_NAME_LEN;
    }

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    return Exchange(0, CMD_SET_ALZ_GROUP_BY_RESULT_STRING, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::GetVSAParam(int demodType,
    VsaParameter *vsaParam,
    ExtendVsaParameter *extvsaParam)
{
    A_ASSERT(vsaParam);

    size_t memory_size = m_currSubTesterCount * sizeof(VsaCapParam);
    std::unique_ptr<char[]> tmpBuf(new (std::nothrow) char[memory_size]);
    VsaCapParam *vsaCapParam = (VsaCapParam *)tmpBuf.get();
    if (nullptr == vsaCapParam)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }
    memset(vsaCapParam, 0, memory_size);


    ExchangeBuff pstRecvBufff[1];
    pstRecvBufff[0].chpHead = (char *)(vsaCapParam);
    pstRecvBufff[0].buff_len = memory_size;
    pstRecvBufff[0].data_len = memory_size;

    int err = Exchange(0, CMD_GET_VSA_PARAM, nullptr, 0, pstRecvBufff, 1, IOCONTROL_VSA);

    if ((WT_ERR_CODE_OK != err)
        || (pstRecvBufff[0].buff_len != pstRecvBufff[0].data_len))
    {
        goto func_exit;
    }

    for (int testerIndex = 0; testerIndex < m_currSubTesterCount; testerIndex++)
    {
        //0 or testerIndex ??
        vsaParam->Freq = vsaCapParam[0].Freq;
        vsaParam->Freq2 = vsaCapParam[0].Freq2;
        vsaParam->FreqOffset = vsaCapParam[0].FreqOffset;
        vsaParam->VsaUnitMask[testerIndex] = vsaCapParam[testerIndex].VsaMask;
        vsaParam->MaxPower[testerIndex] = vsaCapParam[testerIndex].Ampl;
        vsaParam->SmpTime = vsaCapParam[0].SamplingTime;
        vsaParam->SamplingFreq = vsaCapParam[0].SamplingFreq;

        vsaParam->RfPort[testerIndex] = vsaCapParam[testerIndex].RFPort + 1;
        vsaParam->ExtPathLoss[testerIndex] = vsaCapParam[testerIndex].ExtPathLoss;
        vsaParam->TrigType = vsaCapParam[0].TrigType;
        vsaParam->TrigLevel = vsaCapParam[0].TrigLevel;
        vsaParam->TrigTimeout = vsaCapParam[0].TrigTimeout;
        vsaParam->TrigPretime = vsaCapParam[0].TrigPreTime;
        vsaParam->TimeoutWaiting = vsaCapParam[0].AllocTimeout;
        vsaParam->MaxIFGGap = vsaCapParam[0].MaxIFG;

        if (extvsaParam)
        {
            extvsaParam->WIFI8080DulPortMode = vsaCapParam[0].DulPortMode;
            if (vsaCapParam[0].DulPortMode)
            {
                extvsaParam->RfPort_MaxPower[testerIndex * 2] = vsaCapParam[testerIndex].Ampl;
                extvsaParam->RfPort_MaxPower[testerIndex * 2 + 1] = vsaCapParam[testerIndex].Ampl2;
                extvsaParam->RfPort_TrigLevel[testerIndex * 2] = vsaCapParam[testerIndex].TrigLevel;
                extvsaParam->RfPort_TrigLevel[testerIndex * 2 + 1] = vsaCapParam[testerIndex].TrigLevel2;

                extvsaParam->VsaRfPort[testerIndex * 2] = vsaCapParam[testerIndex].RFPort + 1;
                extvsaParam->VsaRfPort[testerIndex * 2 + 1] = vsaCapParam[testerIndex].RFPort2 + 1;
            }
            extvsaParam->DCOffsetI = vsaCapParam[0].DCOffsetI;
            extvsaParam->DCOffsetQ = vsaCapParam[0].DCOffsetQ;
        }
    }
    memcpy(&m_VsaParameterBack, &m_vsaParam, sizeof(VsaParameter));
    memcpy(&m_vsaParam, vsaParam, sizeof(VsaParameter));
    m_VsaParamSerialNum = 0;
func_exit:

    return err;
}

int InstrumentHandle::GetAnalyzeParam(
    AlzParamComm *commonAnalyzeParam,
    void *analyzeParam,
    unsigned int paramSize,
    int *signalType)
{
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SetVSAParamAutorange(
    VsaParameter *vsaParam,
    ExtendVsaParameter *extParam)
{
    A_ASSERT(vsaParam);

    int err = SetVSAParam(vsaParam, extParam, IOCONTROL_VSA);
    if (err != WT_ERR_CODE_OK)
    {
        return err;
    }
    size_t SendTimeout_ms = 1000;//1000ms
    size_t RecvTimeout_ms = 5000;//5000ms
    RecvTimeout_ms += (static_cast<int>(vsaParam->MaxIFGGap) * 1000) * 20;
    RecvTimeout_ms += (m_VsaClockRate - 1) * 1000;

    size_t memory_size = m_currSubTesterCount * sizeof(VsaCapParam);
    std::unique_ptr<char[]>tmpBuf(new (std::nothrow) char[memory_size]);
    VsaCapParam *vsaCapParam = (VsaCapParam *)tmpBuf.get();
    if (nullptr == vsaCapParam)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }
    memset(vsaCapParam, 0, memory_size);

    int signalCount = 0;
    CmdHeader tmpHeader;
    ExchangeBuff pstRecvBufff[2];
    pstRecvBufff[0].chpHead = (char *)(&signalCount);
    pstRecvBufff[0].buff_len = sizeof(int);
    pstRecvBufff[0].data_len = sizeof(int);

    pstRecvBufff[1].chpHead = (char *)(vsaCapParam);
    pstRecvBufff[1].buff_len = memory_size;
    pstRecvBufff[1].data_len = memory_size;


    err = Exchange(0, CMD_VSA_AUTO_RANGE, nullptr, 0, pstRecvBufff, 2, IOCONTROL_VSA, SendTimeout_ms, RecvTimeout_ms, &tmpHeader);

    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    if (signalCount != m_currSubTesterCount)
    {
        err = WT_ERR_CODE_GET_TESTERINFO_ERR;
        goto func_exit;
    }

    for (int testerIndex = 0; testerIndex < m_currSubTesterCount; testerIndex++)
    {
        vsaParam->ExtPathLoss[testerIndex] = vsaCapParam[testerIndex].ExtPathLoss;
        vsaParam->MaxPower[testerIndex] = vsaCapParam[testerIndex].Ampl;
        vsaParam->TrigLevel = vsaCapParam[0].TrigLevel;
        if (extParam)
        {
            extParam->RfPort_MaxPower[testerIndex * 2] = vsaCapParam[testerIndex].Ampl;
            extParam->RfPort_MaxPower[testerIndex * 2 + 1] = vsaCapParam[testerIndex].Ampl2;

            extParam->RfPort_TrigLevel[testerIndex * 2] = vsaCapParam[testerIndex].TrigLevel;
            extParam->RfPort_TrigLevel[testerIndex * 2 + 1] = vsaCapParam[testerIndex].TrigLevel2;
        }
    }
    memcpy(&m_VsaParameterBack, &m_vsaParam, sizeof(VsaParameter));
    memcpy(&m_vsaParam, vsaParam, sizeof(VsaParameter));
    if (extParam)
    {
        memcpy(&m_vsaExternParam, extParam, sizeof(ExtendVsaParameter));
    }
    else
    {
        memset(&m_vsaExternParam, 0, sizeof(m_vsaExternParam));
    }
    m_VsaParamSerialNum = tmpHeader.SerialNum;

    if (WT_ERR_CODE_OK == err)
    {
        ProcWifiVsaSpecial();
    }
func_exit:

    return err;
}
#ifndef LINUX
int InstrumentHandle::DataCapture()
{
    //TODO 暂不支持资源争夺的超时处理
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    err = Exchange(0, CMD_START_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    u32 timeout_ms = 3000;
    int vsaStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int infinity = 0;
    if (WT_TRIG_TYPE_FREE_RUN_API != m_vsaParam.TrigType)
    {
        int flag = 0;
        UsualKit::config_int_var("api_cfg.json", "WT_TRIGGER_TIMEOUT", &flag);
        if (flag > 0)
        {
            timeout_ms = flag;
        }
        //此处不能先把m_vsaParam.TrigTimeout转换成u32。避免精度丢失
        timeout_ms += static_cast<int>(m_vsaParam.TrigTimeout) * 1000;
        if (0 == UsualKit::DoubleCompare(m_vsaParam.TrigTimeout, 0.0))
        {
            infinity = 1;
        }
    }

    timeval stTimeStart, stTimeEnd;
    UsualKit::gettimeofday(&stTimeStart);
    while (true)
    {
        if (0 == infinity)
        {
            UsualKit::gettimeofday(&stTimeEnd);
            if (UsualKit::elapsed_time(&stTimeStart, &stTimeEnd) >= timeout_ms)
            {
                Exchange(0, CMD_STOP_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }
        err = GetCurrVSAStatu(&vsaStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_CAPTURE_DATA_ERROR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsaStatu)
        {
            Sleep(1);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_NO_DATA_CAPTURED;
            break;
        }
    }
func_exit:
    return err;
}
#else
int InstrumentHandle::DataCapture()
{
    //TODO 暂不支持资源争夺的超时处理
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;
    u32 timeout_ms = 300;
    int vsaStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int infinity = 0;
    timeval stTimeStart, stTimeEnd;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    err = Exchange(0, CMD_START_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    if (WT_TRIG_TYPE_FREE_RUN_API != m_vsaParam.TrigType)
    {
        //此处不能先把m_vsaParam.TrigTimeout转换成u32。避免精度丢失
        timeout_ms += static_cast<int>(m_vsaParam.TrigTimeout) * 1000;
        if (0 == UsualKit::DoubleCompare(m_vsaParam.TrigTimeout, 0.0))
        {
            infinity = 1;
        }
    }

    gettimeofday(&stTimeStart, nullptr);
    while (true)
    {
        if (0 == infinity)
        {
            gettimeofday(&stTimeEnd, nullptr);
            if (UsualKit::elapsed_time(&stTimeStart, &stTimeEnd) >= timeout_ms)
            {
                Exchange(0, CMD_STOP_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }
        err = GetCurrVSAStatu(&vsaStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_CAPTURE_DATA_ERROR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsaStatu)
        {
            usleep(1000);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_NO_DATA_CAPTURED;
            break;
        }
    }
func_exit:
    return err;
}
#endif


int InstrumentHandle::DataCaptureAsync()
{
    //TODO 暂不支持资源争夺的超时处理
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    return Exchange(0, CMD_START_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
}

int InstrumentHandle::PauseDataCapture()
{
    //停止采集信号时,应该立即生效,尝试三次发送停止命令
    int cnt = 1;
    int err = WT_ERR_CODE_OK;
    while (cnt)
    {
        err = Exchange(0, CMD_PAUSE_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
        if (WT_ERR_CODE_OK == err)
        {
            break;
        }
        cnt--;
    }
    return err;
}

int InstrumentHandle::StopDataCapture()
{
    //停止采集信号时,应该立即生效,尝试三次发送停止命令
    int cnt = 1;
    int err = WT_ERR_CODE_OK;
    while (cnt)
    {
        err = Exchange(0, CMD_STOP_VSA, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
        if (WT_ERR_CODE_OK == err)
        {
            break;
        }
        cnt--;
    }
    return err;
}

int InstrumentHandle::GetCurrVSAStatu(int *statu)
{
    A_ASSERT(statu);
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)statu;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    return Exchange(0, CMD_GET_VSA_STATUS, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);
}

int InstrumentHandle::LoadDataAsCapture(
    const char *fileName,
    const char *saveFileName,
    int acWave2)
{
    return LoadDataAsCapture(fileName, saveFileName, IOCONTROL_VSA, acWave2);

}

int InstrumentHandle::LoadDataAsCapture(
    const char *fileName,
    const char *saveFileName,
    int controlType,
    int acWave2)
{
    int err = WT_ERR_CODE_OK;
    size_t pos = 0;

    //转换成linux下的目录格式
    char tmpSaveFileName[MAX_NAME_SIZE] = { 0 };
    strcpy(tmpSaveFileName, saveFileName);
    UsualKit::WindirToLinuxDir(tmpSaveFileName, MAX_NAME_SIZE);
    const char *extName = m_WaveExtName.c_str();  //新的文件扩展名
    string lastName("");
    char *pSendBuff = nullptr;
    size_t memory_size = 0;
    if (nullptr != fileName)
    {
        pos = ((string)fileName).find_last_of("\\");
        if (pos != string::npos)
        {
            strncpy(tmpSaveFileName, fileName + pos + 1, strlen(fileName) - pos);
        }
        pos = ((string)tmpSaveFileName).find_last_of("."); //找到扩展名的位置
        if (pos == string::npos)
        {
            lastName = m_WaveExtName;
        }
        else
        {
            lastName = ((string)tmpSaveFileName).substr(pos + 1, strlen(fileName));
        }
    }
    if (true)
    {
        size_t actualBuffSize = 0;
        pos = ((string)tmpSaveFileName).find_last_of(".");
        memcpy(tmpSaveFileName + pos + 1, extName, strlen(extName));

        //MIMO
        if (TEST_SISO_API < m_currTestMode || m_currSubTesterCount > 1)
        {
            //MIMO时，不考虑多连接和效率。此处不再多余申请PN内存拷贝，避免申请内存失败问题
            //在网络发送过程中还是加锁PN内存，直到网络发送完成才解锁
            vector<memCollector>PnCollectorBuf;
            ENTER_LOCK(PN_OPERATE_LOCK);
            err = PnFileProcess(fileName, tmpSaveFileName, acWave2, controlType, PnCollectorBuf);
            if (WT_ERR_CODE_OK == err)
            {
                vector<ExchangeBuff> pstSendBuff;

                for (auto &item : PnCollectorBuf)
                {
                    ExchangeBuff tmpBuf;
                    tmpBuf.chpHead = (false == item.isNeedFree ? (char *)item.pData2 : (char *)item.pData.get());
                    tmpBuf.buff_len = item.DataSize;
                    tmpBuf.data_len = item.DataSize;
                    pstSendBuff.push_back(tmpBuf);
                }

                err = Exchange(0, CMD_EXT_VSA_FILE, &pstSendBuff[0], pstSendBuff.size(), nullptr, 0, controlType, 3000, 10000);

            }

            EXIT_LOCK(PN_OPERATE_LOCK);
        }
        else //SISO
        {
            //SISO时，采用原来的PN内存拷贝，可加快多连接时PN处理速度
            //只是在读取PN数据时加锁，之后后拷贝到网络发送内存，避免网络发送过程中还在加锁状态
            unique_ptr<char[]>tmpBuf = nullptr;
            err = PnFileProcess(fileName, tmpSaveFileName, acWave2, controlType, tmpBuf, &actualBuffSize);
            if (WT_ERR_CODE_OK == err)
            {
                ExchangeBuff pstSendBuff;
                pstSendBuff.chpHead = tmpBuf.get();
                pstSendBuff.buff_len = actualBuffSize;
                pstSendBuff.data_len = actualBuffSize;

                err = Exchange(0, CMD_EXT_VSA_FILE, &pstSendBuff, 1, nullptr, 0, controlType, 1000, 2000);
            }
        }
    }

    return err;
}

int InstrumentHandle::Analyze(
    int frameID,
    const char *refFileName,
    unsigned int timeoutMs)
{
    return Analyze(frameID, refFileName, IOCONTROL_VSA, timeoutMs);
}

int InstrumentHandle::Analyze(
    int frameID,
    const char *refFileName,
    int controlType,
    unsigned int timeoutMs)
{
    //A_ASSERT(pIoControl);
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    unsigned int actualSize = 0;

    frameID = frameID - 1;      //下位机分析帧是从第0帧开始的
    if (frameID <= 0)                   //避免出现负数的情况
    {
        frameID = 0;
    }
    memcpy(sendBuff, &frameID, sizeof(int));
    actualSize += sizeof(int);
    if (nullptr != refFileName)
    {
        memcpy(sendBuff + sizeof(int), refFileName, MAX_NAME_SIZE);
        actualSize += MAX_NAME_SIZE;
    }
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = actualSize;
    pstSendBuff.data_len = actualSize;
    //避免采样时间比较长时，分析超时太快，由5s超时变成15s超时
    int err = Exchange(0, CMD_VSA_ALZ, &pstSendBuff, 1, nullptr, 0, controlType, 1000, timeoutMs);
    return err;
}


int InstrumentHandle::GetResult(const char *anaParamString,
    double *result,
    int signalID,
    int segmentID)
{
    A_ASSERT(result && anaParamString);
    *result = 0;
    //  unsigned int sendBuffLen = 2 * sizeof(int) + VSA_RESULT_NAME_LEN;
    int offset = 0;
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    int SegNo = -1;
    memset(sendBuff, 0, sizeof(sendBuff));
    ValidSegmentSignal(segmentID, signalID);

    memcpy(sendBuff + offset, &signalID, sizeof(signalID));
    offset += sizeof(signalID);
    memcpy(sendBuff + offset, &segmentID, sizeof(segmentID));
    offset += sizeof(segmentID);
    memcpy(sendBuff + offset, &SegNo, sizeof(SegNo));
    offset += sizeof(SegNo);
    memcpy(sendBuff + offset, anaParamString, strlen(anaParamString) * sizeof(char));
    offset += VSA_RESULT_NAME_LEN;

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    char recvBuff[MAX_BUFF_SIZE] = { 0 };
    //memcpy(recvBuff,)
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = recvBuff;
    pstRecvBuff.buff_len = sizeof(recvBuff);
    pstRecvBuff.data_len = sizeof(recvBuff);
    int err = Exchange(0, CMD_GET_VSA_DATA, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);
    //unsigned int actualSize=(unsigned int)(*(int*)recvBuff);
    if (WT_ERR_CODE_OK == err)
    {
        unsigned int dataLen = *(unsigned int *)(recvBuff);
        if (dataLen <= sizeof(double))
        {
            //TODO int 确认
            if (dataLen == sizeof(int))
            {
                *result = *((int *)(recvBuff + 2 * sizeof(int)));
            }
            else
            {
                *result = *((double *)(recvBuff + 2 * sizeof(int)));
            }
        }
        else
        {
            err = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
    }
    return err;
}

int InstrumentHandle::GetVectorResultElementSize(
    const char *anaParamString,
    unsigned int *elementSize,
    int signalID,
    int segmentID, int SegNo)
{
    unsigned int elementCount = 0;
    int err = GetVectorResultInfo(anaParamString, nullptr, elementSize, IOCONTROL_VSA, signalID, segmentID, SegNo);
    if (WT_ERR_CODE_BUFFER_TOO_SHORT == err)
    {
        err = WT_ERR_CODE_OK;
    }
    return err;
}

int InstrumentHandle::GetVectorResultElementCount(
    const char *anaParamString,
    unsigned int *elementCount,
    int signalID,
    int segmentID, int SegNo)
{
    unsigned int elementSize = 0;
    int err = GetVectorResultInfo(anaParamString, elementCount, nullptr, IOCONTROL_VSA, signalID, segmentID, SegNo);
    if (WT_ERR_CODE_BUFFER_TOO_SHORT == err)
    {
        err = WT_ERR_CODE_OK;
    }
    return err;
}

int InstrumentHandle::GetVectorResult(
    const char *anaParamString,
    void *result,
    unsigned int resultSize,
    int signalID,
    int segmentID, int SegNo)
{
    A_ASSERT(result);
    unsigned int elementSize = 0;
    unsigned int elementCount = 0;
    return GetVectorResult(anaParamString, result, resultSize, nullptr, nullptr, IOCONTROL_VSA, signalID, segmentID, SegNo);
}

int InstrumentHandle::GetVectorResult(
    const char *anaParamString,
    void *result,
    unsigned int resultSize,
    unsigned int *elementCount,
    unsigned int *elementSize,
    int controlType,
    int signalID,
    int segmentID, int SegNo)
{
    A_ASSERT(anaParamString && result);

    //  unsigned int sendBuffSize = 2 * sizeof(int) + 32;
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    int offset = 0;
    int err = WT_ERR_CODE_OK;
    ValidSegmentSignal(segmentID, signalID);

    memset(sendBuff, 0, sizeof(sendBuff));

    memcpy(sendBuff + offset, &signalID, sizeof(signalID));
    offset += sizeof(signalID);
    memcpy(sendBuff + offset, &segmentID, sizeof(segmentID));
    offset += sizeof(segmentID);
    memcpy(sendBuff + offset, &SegNo, sizeof(SegNo));
    offset += sizeof(SegNo);

    vector<string>paramVector = UsualKit::split(anaParamString, ";");
    for (size_t i = 0; i < paramVector.size(); i++)
    {
        memcpy(sendBuff + offset, paramVector[i].c_str(), strlen(paramVector[i].c_str()) * sizeof(char));
        offset += VSA_RESULT_NAME_LEN;
    }

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    if (1 == paramVector.size())
    {
        char dataInfo[MAX_BUFF_SIZE] = { 0 };
        ExchangeBuff pstRecvBuff[2];
        pstRecvBuff[0].chpHead = dataInfo;
        pstRecvBuff[0].buff_len = 2 * sizeof(int);
        pstRecvBuff[0].data_len = 2 * sizeof(int);

        pstRecvBuff[1].chpHead = (char *)result;
        pstRecvBuff[1].buff_len = resultSize;
        pstRecvBuff[1].data_len = resultSize;

        err = Exchange(0, CMD_GET_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 2, controlType, 1000, 5000);
        unsigned int actualSize = 0;

        //获取size和count，协议完成后再改
        //内存不够的情况下，针对获取size或count，也得返回值
        if (WT_ERR_CODE_OK == err)
        {
            actualSize = pstRecvBuff[1].data_len;
            if (elementSize && elementCount)
            {
                memcpy(elementSize, dataInfo + sizeof(int), sizeof(int));
                if (*elementSize != 0)
                {
                    *elementCount = actualSize / (*elementSize);
                }
            }
        }
        else if (WT_ERR_CODE_BUFFER_TOO_SHORT == err)
        {
            actualSize = pstRecvBuff[0].data_len;
            if (elementSize && elementCount)
            {
                memcpy(elementSize, dataInfo + sizeof(int), sizeof(int));
                if (*elementSize != 0)
                {
                    *elementCount = actualSize / (*elementSize);
                }
            }
        }
    }
    else
    {
        ExchangeBuff pstRecvBuff;
        const u32 recvTimeOutMs = 5000 + paramVector.size() * 400;
        pstRecvBuff.chpHead = (char *)result;
        pstRecvBuff.buff_len = resultSize;
        pstRecvBuff.data_len = resultSize;
        err = Exchange(0, CMD_GET_VSA_DATA, &pstSendBuff, 1, &pstRecvBuff, 1, controlType, 1000, recvTimeOutMs);
    }
    return err;
}

int InstrumentHandle::GetVectorResultInfo(
    const char *anaParamString,
    unsigned int *elementCount,
    unsigned int *elementSize,
    int controlType,
    int signalID,
    int segmentID, int SegNo)
{
    A_ASSERT(anaParamString);
    //A_ASSERT(pIoControl);

    //unsigned int sendBuffSize = 2 * sizeof(int) + 32;
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    int offset = 0;
    memset(sendBuff, 0, sizeof(sendBuff));
    ValidSegmentSignal(segmentID, signalID);

    memcpy(sendBuff + offset, &signalID, sizeof(signalID));
    offset += sizeof(signalID);
    memcpy(sendBuff + offset, &segmentID, sizeof(segmentID));
    offset += sizeof(segmentID);
    memcpy(sendBuff + offset, &SegNo, sizeof(SegNo));
    offset += sizeof(SegNo);

    vector<string>paramVector = UsualKit::split(anaParamString, ";");
    for (int i = 0; i < paramVector.size(); i++)
    {
        memcpy(sendBuff + offset, paramVector[i].c_str(), strlen(paramVector[i].c_str()) * sizeof(char));
        offset += VSA_RESULT_NAME_LEN;
    }


    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;


    char dataInfo[MAX_BUFF_SIZE] = { 0 };
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = dataInfo;
    pstRecvBuff.buff_len = sizeof(dataInfo);
    pstRecvBuff.data_len = sizeof(dataInfo);

    int err = Exchange(0, CMD_GET_VSA_RESULT, &pstSendBuff, 1, &pstRecvBuff, 1, controlType);
    if (WT_ERR_CODE_OK != err)
    {
        return err;
    }


    int Count = pstRecvBuff.data_len / (2 * sizeof(int));
    int DataLen = 0;
    int DataType = 0;
    for (int i = 0, pos = 0; i < paramVector.size() && i < Count; i++)
    {
        memcpy(&DataLen, dataInfo + pos, sizeof(int));
        pos += sizeof(int);
        memcpy(&DataType, dataInfo + pos, sizeof(int));
        pos += sizeof(int);

        Logger::PrintDebug(__FUNCTION__, __LINE__, "DataType=%d, DataLen=%d(%d)\n", DataType, DataLen, (DataLen / DataType));

        if (nullptr != elementSize)
        {
            elementSize[i] = DataType;
        }

        if (nullptr != elementCount)
        {
            elementCount[i] = DataLen / DataType;
        }

    }
    //  unsigned int actualSize = *(unsigned int *)dataInfo;
    //  memcpy(elementSize, dataInfo + sizeof(int), sizeof(int));
    //  if(*elementSize != 0)
    //  {
    //      *elementCount = actualSize / (*elementSize);
    //  }
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetAverageResult(VsaAverageResult *result, VsaAverageResult *max, VsaAverageResult *min, int times, int signalID, int segmentID)
{
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    memcpy(sendBuff, &times, sizeof(int));
    memcpy(sendBuff + sizeof(int), &signalID, sizeof(int));
    memcpy(sendBuff + sizeof(int) * 2, &segmentID, sizeof(int));
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = 3 * sizeof(int);
    pstSendBuff.data_len = 3 * sizeof(int);

    ExchangeBuff pstRecvBuff[3];
    pstRecvBuff[0].chpHead = (char *)result;
    pstRecvBuff[0].buff_len = sizeof(VsaAverageResult);
    pstRecvBuff[0].data_len = sizeof(VsaAverageResult);

    pstRecvBuff[1].chpHead = (char *)max;
    pstRecvBuff[1].buff_len = sizeof(VsaAverageResult);
    pstRecvBuff[1].data_len = sizeof(VsaAverageResult);

    pstRecvBuff[2].chpHead = (char *)min;
    pstRecvBuff[2].buff_len = sizeof(VsaAverageResult);
    pstRecvBuff[2].data_len = sizeof(VsaAverageResult);

    if (0 != times)
    {
        int err = Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 1, IOCONTROL_VSA);
        if (WT_ERR_CODE_OK == err)
        {
            memcpy(max, result, sizeof(VsaAverageResult));
            memcpy(min, result, sizeof(VsaAverageResult));
        }
        return err;
    }
    else
    {
        return Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 3, IOCONTROL_VSA);
    }
}

int InstrumentHandle::GetBTAverageResult(VsaBTCommResult* result,
	VsaBTCommResult* max,
	VsaBTCommResult* min,
	int times,
	int signalID,
	int segmentID)
{
	char sendBuff[MAX_BUFF_SIZE] = { 0 };
	memcpy(sendBuff, &times, sizeof(int));
	memcpy(sendBuff + sizeof(int), &signalID, sizeof(int));
	memcpy(sendBuff + sizeof(int) * 2, &segmentID, sizeof(int));
	ExchangeBuff pstSendBuff;
	pstSendBuff.chpHead = sendBuff;
	pstSendBuff.buff_len = 3 * sizeof(int);
	pstSendBuff.data_len = 3 * sizeof(int);

	ExchangeBuff pstRecvBuff[3];
	pstRecvBuff[0].chpHead = (char*)result;
	pstRecvBuff[0].buff_len = sizeof(VsaBTCommResult);
	pstRecvBuff[0].data_len = sizeof(VsaBTCommResult);

	pstRecvBuff[1].chpHead = (char*)max;
	pstRecvBuff[1].buff_len = sizeof(VsaBTCommResult);
	pstRecvBuff[1].data_len = sizeof(VsaBTCommResult);

	pstRecvBuff[2].chpHead = (char*)min;
	pstRecvBuff[2].buff_len = sizeof(VsaBTCommResult);
	pstRecvBuff[2].data_len = sizeof(VsaBTCommResult);

	if (0 != times)
	{
		int err = Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 1, IOCONTROL_VSA);//当前次的结果
		if (WT_ERR_CODE_OK == err)
		{
			memcpy(max, result, sizeof(VsaBTCommResult));
			memcpy(min, result, sizeof(VsaBTCommResult));
		}
		return err;
	}
	else
	{
		return Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 3, IOCONTROL_VSA);//指定平均条件下的平均结果
	}
}

int InstrumentHandle::GetSLEAverageResult(VsaSLECommResult* result,
	VsaSLECommResult* max,
	VsaSLECommResult* min,
	int times,
	int signalID,
	int segmentID)
{
	A_ASSERT(result && max && min);

	char sendBuff[MAX_BUFF_SIZE] = { 0 };
	memcpy(sendBuff, &times, sizeof(int));
	memcpy(sendBuff + sizeof(int), &signalID, sizeof(int));
	memcpy(sendBuff + sizeof(int) * 2, &segmentID, sizeof(int));
	ExchangeBuff pstSendBuff;
	pstSendBuff.chpHead = sendBuff;
	pstSendBuff.buff_len = 3 * sizeof(int);
	pstSendBuff.data_len = 3 * sizeof(int);

	ExchangeBuff pstRecvBuff[3];
	pstRecvBuff[0].chpHead = (char*)result;
	pstRecvBuff[0].buff_len = sizeof(VsaSLECommResult);
	pstRecvBuff[0].data_len = sizeof(VsaSLECommResult);

	pstRecvBuff[1].chpHead = (char*)max;
	pstRecvBuff[1].buff_len = sizeof(VsaSLECommResult);
	pstRecvBuff[1].data_len = sizeof(VsaSLECommResult);

	pstRecvBuff[2].chpHead = (char*)min;
	pstRecvBuff[2].buff_len = sizeof(VsaSLECommResult);
	pstRecvBuff[2].data_len = sizeof(VsaSLECommResult);

	if (0 != times)
	{
		int err = Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 1, IOCONTROL_VSA);//当前次的结果
		if (WT_ERR_CODE_OK == err)
		{
			memcpy(max, result, sizeof(VsaSLECommResult));
			memcpy(min, result, sizeof(VsaSLECommResult));
		}
		return err;
	}
	else
	{
		return Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 3, IOCONTROL_VSA);//指定平均条件下的平均结果
	}
}

int InstrumentHandle::Get3GPPAverageResult(Vsa3GPPCommResult *result, Vsa3GPPCommResult *max, Vsa3GPPCommResult *min, int times, int signalID, int segmentID)
{
    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    memcpy(sendBuff, &times, sizeof(int));
    memcpy(sendBuff + sizeof(int), &signalID, sizeof(int));
    memcpy(sendBuff + sizeof(int) * 2, &segmentID, sizeof(int));
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = 3 * sizeof(int);
    pstSendBuff.data_len = 3 * sizeof(int);

    ExchangeBuff pstRecvBuff[3];
    pstRecvBuff[0].chpHead = (char *)result;
    pstRecvBuff[0].buff_len = sizeof(Vsa3GPPCommResult);
    pstRecvBuff[0].data_len = sizeof(Vsa3GPPCommResult);

    pstRecvBuff[1].chpHead = (char *)max;
    pstRecvBuff[1].buff_len = sizeof(Vsa3GPPCommResult);
    pstRecvBuff[1].data_len = sizeof(Vsa3GPPCommResult);

    pstRecvBuff[2].chpHead = (char *)min;
    pstRecvBuff[2].buff_len = sizeof(Vsa3GPPCommResult);
    pstRecvBuff[2].data_len = sizeof(Vsa3GPPCommResult);
    if (0 != times)
    {
        int err = Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 1, IOCONTROL_VSA);
        if (WT_ERR_CODE_OK == err)
        {
            memcpy(max, result, sizeof(Vsa3GPPCommResult));
            memcpy(min, result, sizeof(Vsa3GPPCommResult));
        }
        return err;
    }
    else
    {
        return Exchange(0, CMD_GET_SPEC_VSA_DATA, &pstSendBuff, 1, pstRecvBuff, 3, IOCONTROL_VSA);
    }
}

int InstrumentHandle::GetAvgBaseCompositeResult(
    VsaBaseResult *result,
    int segmentID)
{
    A_ASSERT(result);

    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    u32 offset = 0;

    memcpy(sendBuff + offset, &segmentID, sizeof(segmentID));
    offset += sizeof(segmentID);


    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)result;
    pstRecvBuff.buff_len = sizeof(VsaBaseResult);
    pstRecvBuff.data_len = sizeof(VsaBaseResult);
    int err = Exchange(0, CMD_GET_AVG_DATA_COMPOSITE, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);

    return err;
}


int InstrumentHandle::GetBaseResult(
    VsaBaseResult *result,
    int signalID,
    int segmentID)
{
    return GetBaseResult(result, signalID, IOCONTROL_VSA, segmentID);
}

int InstrumentHandle::GetBaseResult(
    VsaBaseResult *result,
    int signalID,
    int controlType,
    int segmentID)
{
    A_ASSERT(result);

    char sendBuff[MAX_BUFF_SIZE] = { 0 };
    u32 offset = 0;
    int SegNo = -1;
    ValidSegmentSignal(segmentID, signalID);

    memcpy(sendBuff + offset, &signalID, sizeof(signalID));
    offset += sizeof(signalID);
    memcpy(sendBuff + offset, &segmentID, sizeof(segmentID));
    offset += sizeof(segmentID);
    memcpy(sendBuff + offset, &SegNo, sizeof(SegNo));
    offset += sizeof(SegNo);
    memcpy(sendBuff + offset, WT_RES_BASE_RESULT, strlen(WT_RES_BASE_RESULT));
    offset += VSA_RESULT_NAME_LEN;


    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = sendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    char recvBuff[MAX_BUFF_SIZE] = { 0 };
    //memcpy(recvBuff,)
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = recvBuff;
    pstRecvBuff.buff_len = sizeof(recvBuff);
    pstRecvBuff.data_len = sizeof(recvBuff);
    int err = Exchange(0, CMD_GET_VSA_DATA, &pstSendBuff, 1, &pstRecvBuff, 1, controlType);
    unsigned int actualSize = (unsigned int)(*(int *)recvBuff);
    if (WT_ERR_CODE_OK == err)
    {
        memcpy(result, recvBuff + 2 * sizeof(int), actualSize);
    }
    return err;
}

int InstrumentHandle::SetVSAAverageParam(
    int averageType,
    int averageMethod,
    int averageCount)
{
    char proBuff[MAX_BUFF_SIZE] = { 0 };
    u32 offset = 0;

    memcpy(proBuff + offset, &averageType, sizeof(averageType));
    offset += sizeof(averageType);
    memcpy(proBuff + offset, &averageMethod, sizeof(averageMethod));
    offset += sizeof(averageMethod);
    memcpy(proBuff + offset, &averageCount, sizeof(averageCount));
    offset += sizeof(averageCount);
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    return Exchange(0, CMD_SET_AVG_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SaveSignal(const char *fileName,
    int saveOption,
    char *signalBuff,
    unsigned int signalBuffSize)
{
    char pSendBuff[MAX_BUFF_SIZE] = { 0 };
    memcpy(pSendBuff, &saveOption, sizeof(int));
    memcpy(pSendBuff + sizeof(int), fileName, strlen(fileName) * sizeof(char));
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = pSendBuff;
    pstSendBuff.buff_len = MAX_NAME_SIZE + sizeof(int);
    pstSendBuff.data_len = MAX_NAME_SIZE + sizeof(int);
    return Exchange(0, CMD_SAVE_SIGNAL, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::GetRefLvlRange(double freq,
    int option,
    int *upperLimit,
    int *lowerLimit)
{
    A_ASSERT(upperLimit);
    A_ASSERT(lowerLimit);
    char pSendBuff[MAX_BUFF_SIZE] = { 0 };
    int offset = 0;

    memcpy(pSendBuff + offset, &option, sizeof(option));
    offset += sizeof(option);
    memcpy(pSendBuff + offset, &freq, sizeof(freq));
    offset += sizeof(freq);

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = pSendBuff;
    pstSendBuff.buff_len = offset;
    pstSendBuff.data_len = offset;

    char pRecvBuff[MAX_BUFF_SIZE] = { 0 };
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = pRecvBuff;
    pstRecvBuff.buff_len = sizeof(pRecvBuff);
    pstRecvBuff.data_len = sizeof(pRecvBuff);

    int err = WT_ERR_CODE_OK;
    do
    {
        err = Exchange(0, CMD_GET_REF_RANGE, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }
        memcpy(upperLimit, pRecvBuff, sizeof(int));
        memcpy(lowerLimit, pRecvBuff + sizeof(int), sizeof(int));
    } while (0);
    return err;
}

int InstrumentHandle::SetGeneralAnalyzeParam(AlzParamComm *commonAnalyzeParam)
{
    int paramType = 0;
    return SetAnalyzeParam(paramType, commonAnalyzeParam, sizeof(AlzParamComm));
}

int InstrumentHandle::SaveOriginalIQDataToFile(const char *fileName, char *calParam, unsigned int calParamSize, int signalID)
{
    unsigned int dataSize = 0;
    int err = WT_ERR_CODE_OK;
    Rx_ParmStruct rxParam(m_FwVersion);
    char tmpRecvData[MAX_NAME_SIZE] = { 0 };
    unsigned int rawDataSize = 0;
    unsigned int paramSize = 0;
    int segmentCount = 0;
    unsigned int remainCalParamSize = calParamSize;
    int rcvLength = 0;
    int i = 0;
    int j = 0;
    stIQDat *iqData = nullptr;
    unique_ptr<char[]>tmpBuf = nullptr;

    int rxParmSize = rxParam.GetParaSize();
    int tmpStreamCnt = 0;
    int streamEnd = 1;
    double result = 0.0;
    do
    {
        err = GetResult(WT_RES_STREAM_COUNT, &result, 0, 0);
        if (err)
        {
            streamEnd = m_currSubTesterCount;
        }
        else
        {
            streamEnd = static_cast<int>(result);
        }
        for (size_t i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
        {
            ClearPNInfor(&m_pnInfos[i]);
        }

        for (int signalIndex = 0; signalIndex < streamEnd; signalIndex++)
        {
            if (signalID > 0)
            {
                signalIndex = signalID;
            }
            ExchangeBuff pstSendBuff;
            pstSendBuff.chpHead = (char *)&signalIndex;
            pstSendBuff.buff_len = sizeof(int);
            pstSendBuff.data_len = sizeof(int);

            err = ProGetSpeciallyAckDataSize(0, CMD_GET_VSA_RAW_DATA, &dataSize, &pstSendBuff, 1, IOCONTROL_VSA);
            if (WT_ERR_CODE_OK != err)
            {
                break;
            }
            rcvLength = ProRecv(IOCONTROL_VSA, tmpRecvData, sizeof(int) * 3, 5000);
            if (rcvLength != sizeof(int) * 3)
            {
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
            segmentCount = *((unsigned int *)tmpRecvData) + j;

            do
            {

                paramSize = *(unsigned int *)(tmpRecvData + sizeof(int));
                rawDataSize = *(unsigned int *)(tmpRecvData + 2 * sizeof(int));
                if (paramSize != rxParmSize)
                {
#ifdef LINUX
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "paramSize != rawDataSize error(" << paramSize << " != " << rxParmSize << ")" << std::endl;
#endif
                    //err = WT_ERR_CODE_GENERAL_ERROR;
                    //break;
                }

                rcvLength = ProRecv(IOCONTROL_VSA, rxParam.GetParamAddr(), paramSize, 5000);
                if (rcvLength != paramSize)
                {
                    err = WT_ERR_CODE_TIMEOUT;
                    break;
                }
                tmpBuf.reset(new (std::nothrow) char[rawDataSize]);
                iqData = (stIQDat *)tmpBuf.get();

                if (nullptr == iqData)
                {
                    err = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }

                rcvLength = ProRecv(IOCONTROL_VSA, (char *)iqData, rawDataSize, 5000);
                if (rcvLength != rawDataSize)
                {
                    err = WT_ERR_CODE_TIMEOUT;
                    break;
                }
                ClearPNInfor(&m_pnInfos[j]);
                m_pnInfos[j].DataType = enDataFormat_Int16;
                m_pnInfos[j].flag8080 = m_vsaParam.Freq2 > 1 ? 1 : 0; //TODO 还需确认该值

                if (m_isvsa160mFlag)
                {
                    if (1 == j % 2)
                    {
                        m_pnInfos[j].dCenterFreq = m_vsaParam.Freq + 40 * MHz_API;
                    }
                    else
                    {
                        m_pnInfos[j].dCenterFreq = m_vsaParam.Freq - 40 * MHz_API;
                    }
                    m_pnInfos[j].flag8080 = 1;
                }
                else
                {
                    if (1 == j % 2)
                    {
                        m_pnInfos[j].dCenterFreq = m_vsaParam.Freq2;
                    }
                    else
                    {
                        m_pnInfos[j].dCenterFreq = m_vsaParam.Freq;
                    }
                }

                m_pnInfos[j].ModType = m_vsaParam.Demode;
                m_pnInfos[j].SampleFreq = (m_vsaParam.SamplingFreq / MHz_API);
                m_pnInfos[j].SampleCount = rawDataSize / sizeof(int);;
                m_pnInfos[j].FrameEnd = rawDataSize / sizeof(int);;
                m_pnInfos[j].freqOffset = m_vsaParam.FreqOffset;
                m_pnInfos[j].triggerLevel = m_vsaParam.TrigLevel;
                m_pnInfos[j].sceneMode = m_currTestMode; //TODO 还需确认该值
                tmpStreamCnt++;
                if (WT_ALZ_PARAM_WIFI == m_AnalyzeParamInfo[IOCONTROL_VSA].sigType &&
                    m_AnalyzeParamInfo[IOCONTROL_VSA].paramSize == sizeof(AlzParamWifi))
                {
                    AlzParamWifi *alzParam = (AlzParamWifi*)m_AnalyzeParamInfo[IOCONTROL_VSA].analyzeParam;
                    m_pnInfos[j].clockRate = CheckPnClockRate(alzParam->ClockRate);//default clock rate
                }

                if (m_vsaAc8080Flag)
                {
                    m_pnInfos[j].vsaAmpl = m_vsaParam.MaxPower[j / 2];
                    if (1 == j % 2)
                    {
                        m_pnInfos[j].ExtGain = m_vsaParam.ExtPathLoss2[j / 2];
                        m_pnInfos[j].ExtAtt = m_vsaParam.ExtPathLoss2[j / 2];
                    }
                    else
                    {
                        m_pnInfos[j].ExtGain = m_vsaParam.ExtPathLoss[j];
                        m_pnInfos[j].ExtAtt = m_vsaParam.ExtPathLoss[j];
                    }
                }
                else
                {
                    m_pnInfos[j].vsaAmpl = m_vsaParam.MaxPower[j];
                    m_pnInfos[j].ExtGain = m_vsaParam.ExtPathLoss[j];
                    m_pnInfos[j].ExtAtt = m_vsaParam.ExtPathLoss[j];
                }

                if (segmentCount > 1 && m_vsaExternParam.WIFI8080DulPortMode)
                {
                    if (j < segmentCount)
                    {
                        m_pnInfos[j].vsaAmpl = m_vsaExternParam.RfPort_MaxPower[signalIndex * 2];
                        m_pnInfos[j].triggerLevel = m_vsaExternParam.RfPort_TrigLevel[signalIndex * 2];
                    }
                    else
                    {
                        m_pnInfos[j].vsaAmpl = m_vsaExternParam.RfPort_MaxPower[signalIndex * 2 + 1];
                        m_pnInfos[j].triggerLevel = m_vsaExternParam.RfPort_TrigLevel[signalIndex * 2 + 1];
                    }
                }


                m_pnInfos[j].vsaSourceFalg = 1;

                for (i = 0; i < m_pnInfos[j].SampleCount; i++)
                {
                    m_pnInfos[j].data[i].dReal = iqData[i].s16Real;
                    m_pnInfos[j].data[i].dImag = iqData[i].s16Imag;
                }

                m_pnInfos[j].RFGain = rxParam.GetRFGain();
                m_pnInfos[j].bb_response.FreqCount = static_cast<int>(m_pnInfos[j].SampleFreq) + 1;
                memcpy(m_pnInfos[j].bb_response.Response, rxParam.GetParamBBResponseGain(), m_pnInfos[j].bb_response.FreqCount * sizeof(double));
                m_pnInfos[j].rf_response.FreqCount = m_pnInfos[j].bb_response.FreqCount;
                memcpy(m_pnInfos[j].rf_response.Response, rxParam.GetParamRFResponseGain(), m_pnInfos[j].bb_response.FreqCount * sizeof(double));
                m_pnInfos[j].ns_response.FreqCount = rxParam.GetParamNSResponseGainSize();
                memcpy(m_pnInfos[j].ns_response.Response, rxParam.GetParamNSResponseGain(), m_pnInfos[j].ns_response.FreqCount * sizeof(double));
                m_pnInfos[j].iqImage.freq_Iqimb_len = rxParam.GetPramIQImage().freq_Iqimb_len;
                memcpy(m_pnInfos[j].iqImage.freq_gain_imb, rxParam.GetPramIQImage().freq_gain_imb, m_pnInfos[j].iqImage.freq_Iqimb_len * sizeof(double));
                memcpy(m_pnInfos[j].iqImage.freq_quad_err, rxParam.GetPramIQImage().freq_quad_err, m_pnInfos[j].iqImage.freq_Iqimb_len * sizeof(double));
                m_pnInfos[j].DC_Offset_I = 0;
                m_pnInfos[j].DC_Offset_Q = 0;
                m_pnInfos[j].IQGainImb = rxParam.GetIQGainImb();
                m_pnInfos[j].IQPhaseImb = rxParam.GetIQPhaseImb();
                m_pnInfos[j].timeSkew = rxParam.GetTimeSkew();

                //TODO 获取补偿参数，后续需将该参数也写到文件中过去
                if ((nullptr != calParam) && (remainCalParamSize >= rxParmSize))
                {
                    memcpy(calParam + rxParmSize * j, rxParam.GetParamAddr(), rxParmSize);
                    remainCalParamSize -= rxParmSize;
                }

                if (j == (segmentCount - 1))
                {
                    j++;
                    break;
                }
                else
                {
                    rcvLength = ProRecv(IOCONTROL_VSA, &tmpRecvData[sizeof(int)], sizeof(int) * 2, 3000);
                    if (rcvLength != sizeof(int) * 2)
                    {
                        err = WT_ERR_CODE_TIMEOUT;
                        break;
                    }
                    j++;
                }
            } while (j < segmentCount && WT_ERR_CODE_OK == err);

            if (WT_ERR_CODE_OK != err)
            {
                break;
            }
            if (-1 != signalID)
            {
                break;
            }
        }

        if (WT_ERR_CODE_OK == err)
        {
            err = PnDataParamValid(m_pnInfos, tmpStreamCnt);
        }

        if (WT_ERR_CODE_OK == err)
        {
            string tmpstr = ((string)fileName).c_str();
            int pos = tmpstr.find_last_of(".");
            if (pos != string::npos)
            {
                TruncatePnInfo(m_pnInfos);

                for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
                {
                    if (m_pnInfos[i].SampleCount <= 0)
                    {
                        break;
                    }
                    if (m_pnInfos[i].data != nullptr)
                    {
                        err = CreatFileByPNFileInfo(&m_pnInfos[i], fileName, i);
                        if (err != WT_ERR_CODE_OK)
                        {
                            break;
                        }
                    }
                }
            }
        }
    } while (0);
 

    return err;
}

int InstrumentHandle::SetSaveCurrIQDataCursor(int startUs, int endUs)
{
    if (startUs < 0 || endUs < 0)
    {
        return WT_ERR_CODE_UNKNOW_PARAMETER;
    }

    if (startUs >= endUs && startUs > 0)
    {
        return WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    m_SaveIQDataRange[0] = startUs;
    m_SaveIQDataRange[1] = endUs;
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::PnDataParamValid(stPNFileInfo *pn, int maxStream)
{
    vector<int>SampleCountList;
    vector<double>SampleRateList;
    vector<int>ClockRateList;
    vector<int>modTypeList;
    int iRet = WT_ERR_CODE_OK;

    for (int i = 0; i < maxStream; i++)
    {
        if (pn[i].SampleCount <= 0 || nullptr == pn[i].data)
        {
            break;
        }
        SampleCountList.push_back(pn[i].SampleCount);
        SampleRateList.push_back(pn[i].SampleFreq);
        ClockRateList.push_back(pn[i].clockRate);
        modTypeList.push_back(pn[i].ModType);
    }

    if (SampleCountList.size() > 1)
    {
        for (int i = 1; i < SampleCountList.size(); i++)
        {
            if (SampleCountList[i] - SampleCountList[0] != 0)
            {
                Logger::WriteLog(eumLogType_Error, "Waveform SampleCount[%d] != SampleCount[0] = [%d,%d]",
                    i,
                    SampleCountList[i],
                    SampleCountList[0]);
                iRet = WT_ERR_CODE_GENERATE_FAIL;
                break;
            }
            if (ClockRateList[i] - ClockRateList[0] != 0)
            {
                Logger::WriteLog(eumLogType_Error, "Waveform ClockRate[%d] != ClockRate[0] = [%d,%d]",
                    i,
                    ClockRateList[i],
                    ClockRateList[0]);
                iRet = WT_ERR_CODE_GENERATE_FAIL;
                break;
            }

            if (modTypeList[i] - modTypeList[0] != 0)
            {
                Logger::WriteLog(eumLogType_Error, "Waveform ModType[%d] != ModType[0] = [%d,%d]",
                    i,
                    modTypeList[i],
                    modTypeList[0]);
                iRet = WT_ERR_CODE_GENERATE_FAIL;
                break;
            }

            if (SampleRateList[i] - SampleRateList[0] > 0.02)
            {
                Logger::WriteLog(eumLogType_Error, "Waveform SampleFreq[%d] != SampleFreq[0] = [%f,%f]",
                    i,
                    SampleRateList[i],
                    SampleRateList[0]);
                iRet = WT_ERR_CODE_GENERATE_FAIL;
                break;
            }
        }
    }

    return iRet;
}

int InstrumentHandle::SaveCurrIQDataToFile(
    const char *fileName,
    int signalID)
{
    A_ASSERT(fileName);

    unsigned int elementSize = 0;
    unsigned int elementCount = 0;
    int err = 0;
    stPNFileInfo *pnInfoPtr = nullptr;
    unsigned int dataSize = 0;
    bool platFlag = true;

    char tmpRecvData[MAX_NAME_SIZE] = { 0 };
    unsigned int paramSize = 0;

    int recLength = 0;
    size_t pos = 0;

    int segmentCnt = 1;
    int streamStart = 0;
    int streamEnd = 1;
    int tmpStreamCnt = 0;

    double result = 0.0;
    err = GetResult(WT_RES_STREAM_COUNT, &result, 0, 0);
    if (err)
    {
        streamEnd = m_currSubTesterCount;
    }
    else
    {
        streamEnd = static_cast<int>(result);
    }


    if (m_vsaAc8080Flag || m_isvsa160mFlag)
    {
        segmentCnt = 2;
    }
        
    int rxParmSize = 0;
    std::map<int, Rx_ParmStruct> rxParamMap;
    for (int i = 1; i <= segmentCnt; i++)
    {
        rxParamMap.insert(std::make_pair(i, Rx_ParmStruct(m_FwVersion)));
        rxParmSize = rxParamMap.find(i)->second.GetParaSize();
    }

    if (m_currSubTesterCount > WT_SUB_TESTER_INDEX_MAX)
    {
        Logger::WriteLog(eumLogType_Error, "m_currSubTesterCount(%d) > (%d)\n", m_currSubTesterCount, WT_SUB_TESTER_INDEX_MAX);
        err = WT_ERR_CODE_PARAMETER_MISMATCH;
        goto func_exit;
    }

    if (signalID >= 0)
    {
        streamStart = signalID;
        streamEnd = streamStart + 1;
    }

    for (size_t i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        ClearPNInfor(&m_pnInfos[i]);
    }

    for (int streamID = streamStart; streamID < streamEnd; streamID++)
    {
                ExchangeBuff pstSendBuff;
                pstSendBuff.chpHead = (char *)&streamID;
                pstSendBuff.buff_len = sizeof(int);
                pstSendBuff.data_len = sizeof(int);
                err = ProGetSpeciallyAckDataSize(0, CMD_GET_VSA_CAL_PARAM, &dataSize, &pstSendBuff, 1, IOCONTROL_VSA);
                if (WT_ERR_CODE_OK != err)
                {
                    Logger::WriteLog(eumLogType_Error, "[%s,%d]CMD_GET_VSA_CAL_PARAM err(%d)\n", __FUNCTION__, __LINE__, err);
                    break;
                }
        platFlag = true;
        recLength = ProRecv(IOCONTROL_VSA, tmpRecvData, sizeof(int), 5000);
        if (recLength != sizeof(int))
                {
            err = WT_ERR_CODE_TIMEOUT;
            Logger::WriteLog(eumLogType_Error, "[%s,%d]ProRecv err(%d)\n", __FUNCTION__, __LINE__, err);
            break;
                }
        segmentCnt = min(*((unsigned int *)tmpRecvData), segmentCnt);

        for (int segmentID = 1; segmentID <= segmentCnt; segmentID++)
                {
            recLength = ProRecv(IOCONTROL_VSA, tmpRecvData, sizeof(int), 5000);
            if (recLength != sizeof(int))
                    {
                        err = WT_ERR_CODE_TIMEOUT;
                        Logger::WriteLog(eumLogType_Error, "[%s,%d]ProRecv err(%d)\n", __FUNCTION__, __LINE__, err);
                        break;
                    }
            paramSize = *(unsigned int *)(tmpRecvData);
                    if (paramSize != rxParmSize)
                    {
                Logger::WriteLog(eumLogType_Error, "[%s,%d]paramSize != sizeof(Rx_Parm) err(%d)\n", __FUNCTION__, __LINE__, err);
                break;
                    }
            recLength = ProRecv(IOCONTROL_VSA, rxParamMap.find(segmentID)->second.GetParamAddr(), paramSize, 5000);
                    if (recLength != paramSize)
                    {
                        err = WT_ERR_CODE_TIMEOUT;
                        Logger::WriteLog(eumLogType_Error, "[%s,%d]ProRecv err(%d)\n", __FUNCTION__, __LINE__, err);
                        break;
                    }
                }

        for (int segmentID = 1; segmentID <= segmentCnt; segmentID++)
        {
            if ((err = GetVectorResultElementSize(WT_RES_IQ_MV, &elementSize, streamID, segmentID)) != WT_ERR_CODE_OK)
            {
                Logger::WriteLog(eumLogType_Error, "[%s,%d]GetVectorResultElementSize err(%d)\n", __FUNCTION__, __LINE__, err);
                break;
            }
            if ((err = GetVectorResultElementCount(WT_RES_IQ_MV, &elementCount, streamID, segmentID)) != WT_ERR_CODE_OK)
            {
                Logger::WriteLog(eumLogType_Error, "[%s,%d]GetVectorResultElementCount err(%d)\n", __FUNCTION__, __LINE__, err);
                break;
            }
            int PosOffset = streamID*segmentCnt + (segmentID - 1);
            pnInfoPtr = m_pnInfos + PosOffset;

            ClearPNInfor(pnInfoPtr);
            if ((err = GetVectorResult(WT_RES_IQ_MV, pnInfoPtr->data, elementCount * elementSize, streamID, segmentID)) != WT_ERR_CODE_OK)
            {
                Logger::WriteLog(eumLogType_Error, "[%s,%d]GetVectorResult err(%d)\n", __FUNCTION__, __LINE__, err);
                break;
            }

            pnInfoPtr->DataType = enDataFormat_Float64;
            pnInfoPtr->dCenterFreq = m_vsaParam.Freq;

            if (segmentCnt > 1)
            {
                if (segmentID < segmentCnt)
                {
                    pnInfoPtr->dCenterFreq = m_vsaParam.Freq;
                }
                else
                {
                    pnInfoPtr->dCenterFreq = m_vsaParam.Freq2;
                }

                if (m_isvsa160mFlag)
                {
                    if (segmentID < segmentCnt)
                    {
                        pnInfoPtr->dCenterFreq = m_vsaParam.Freq - 40 * MHz_API;
                    }
                    else
                    {
                        pnInfoPtr->dCenterFreq = m_vsaParam.Freq + 40 * MHz_API;
                    }
                }
            }
            pnInfoPtr->ModType = m_vsaParam.Demode;
            pnInfoPtr->SampleFreq = (m_vsaParam.SamplingFreq / MHz_API);
            pnInfoPtr->SampleCount = elementCount;
            pnInfoPtr->FrameEnd = elementCount;
            pnInfoPtr->freqOffset = m_vsaParam.FreqOffset;
            pnInfoPtr->triggerLevel = m_vsaParam.TrigLevel;

            pnInfoPtr->flag8080 = (segmentCnt > 1 ? 1 : 0);
            pnInfoPtr->sceneMode = m_currTestMode; //TODO 还需确认该值
            pnInfoPtr->vsaAmpl = m_vsaParam.MaxPower[streamID];
            if (segmentCnt > 1 && m_vsaExternParam.WIFI8080DulPortMode)
            {
                if (segmentID < segmentCnt)
                {
                    pnInfoPtr->vsaAmpl = m_vsaExternParam.RfPort_MaxPower[streamID * 2];
                    pnInfoPtr->triggerLevel = m_vsaExternParam.RfPort_TrigLevel[streamID * 2];
                }
                else
                {
                    pnInfoPtr->vsaAmpl = m_vsaExternParam.RfPort_MaxPower[streamID * 2 + 1];
                    pnInfoPtr->triggerLevel = m_vsaExternParam.RfPort_TrigLevel[streamID * 2 + 1];
                }
            }
            pnInfoPtr->vsaSourceFalg = 1;
            tmpStreamCnt++;

            if (platFlag)
            {
                pnInfoPtr->bb_response.FreqCount = static_cast<int>(pnInfoPtr->SampleFreq) + 1;
                memcpy(pnInfoPtr->bb_response.Response, rxParamMap.find(segmentID)->second.GetParamBBResponseGain(), pnInfoPtr->bb_response.FreqCount * sizeof(double));
                pnInfoPtr->rf_response.FreqCount = pnInfoPtr->bb_response.FreqCount;
                memcpy(pnInfoPtr->rf_response.Response, rxParamMap.find(segmentID)->second.GetParamRFResponseGain(), pnInfoPtr->bb_response.FreqCount * sizeof(double));
                pnInfoPtr->ns_response.FreqCount = rxParamMap.find(segmentID)->second.GetParamNSResponseGainSize();
                memcpy(pnInfoPtr->ns_response.Response, rxParamMap.find(segmentID)->second.GetParamNSResponseGain(), pnInfoPtr->ns_response.FreqCount * sizeof(double));
                /*pnInfoPtr->iqImage.freq_Iqimb_len = rxParamMap.find(segmentID)->second.GetPramIQImage().freq_Iqimb_len;
                memcpy(pnInfoPtr->iqImage.freq_gain_imb, rxParamMap.find(segmentID)->second.GetPramIQImage().freq_gain_imb, pnInfoPtr->iqImage.freq_Iqimb_len * sizeof(double));
                memcpy(pnInfoPtr->iqImage.freq_quad_err, rxParamMap.find(segmentID)->second.GetPramIQImage().freq_quad_err, pnInfoPtr->iqImage.freq_Iqimb_len * sizeof(double));*/
                pnInfoPtr->iqImage.freq_Iqimb_len = 0;
            }

            if (WT_ALZ_PARAM_WIFI == m_AnalyzeParamInfo[IOCONTROL_VSA].sigType &&
                m_AnalyzeParamInfo[IOCONTROL_VSA].paramSize == sizeof(AlzParamWifi))
            {
                AlzParamWifi *alzParam = (AlzParamWifi*)m_AnalyzeParamInfo[IOCONTROL_VSA].analyzeParam;
                pnInfoPtr->clockRate = CheckPnClockRate(alzParam->ClockRate);//default clock rate
            }
        }
    }
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    err = SaveVsaWaveForm(fileName, tmpStreamCnt);
func_exit:
    return err;
}

void InstrumentHandle::TruncatePnInfo(stPNFileInfo *tmpPnFileInfo)
{
    //设置保存段
    if (m_SaveIQDataRange[0] >= 0 && m_SaveIQDataRange[1] > 0)
    {
        for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
        {
            if (tmpPnFileInfo[i].SampleCount <= 0)
            {
                break;
            }
            s32 startSavePos = m_SaveIQDataRange[0] * static_cast<int>(tmpPnFileInfo[i].SampleFreq);
            s32 endSavePos = m_SaveIQDataRange[1] * static_cast<int>(tmpPnFileInfo[i].SampleFreq);
            endSavePos = min(endSavePos, tmpPnFileInfo[i].SampleCount);
            if (startSavePos > endSavePos)
            {
                startSavePos = 0;
            }

            s32 elementCount = endSavePos - startSavePos;
            s32 elementSize = sizeof(stPNDat);
            tmpPnFileInfo[i].SampleCount = elementCount;
            tmpPnFileInfo[i].FrameEnd = elementCount;
            if (startSavePos > 0)
            {
                memmove(tmpPnFileInfo[i].data, &tmpPnFileInfo[i].data[startSavePos], elementCount * elementSize);
            }
        }
        //恢复
        memset(m_SaveIQDataRange, 0, sizeof(m_SaveIQDataRange));
    }
}

int InstrumentHandle::SaveCurrIQDataToFile(int saveType,
    const char *fileName,
    char *calParam,
    unsigned int calParamSize,
    int signalID)
{
    int err = WT_ERR_CODE_OK;
    ENTER_LOCK(PN_OPERATE_LOCK);
    switch (saveType)
    {
    case WT_SAVE_RAW_DATA:
    {
        err = SaveOriginalIQDataToFile(fileName, calParam, calParamSize, signalID);
        break;
    }
    case WT_SAVE_COMPENSATED_DATA:
    {
        err = SaveCurrIQDataToFile(fileName, signalID);
        break;
    }
    }
    EXIT_LOCK(PN_OPERATE_LOCK);
    return err;
}


int InstrumentHandle::MoniGetIQVectorResult(int cmd, int &streamNum, int &segmentCnt, vector<int> &elementdataBytes, vector<void *>&result, IOControl *pIoControl)
{
    char tmpRecvData[MAX_NAME_SIZE] = { 0 };
    unsigned int dataSize = 0;
    int recLength = 0;
    int iRet = WT_ERR_CODE_OK;
    int index = 0;

    do
    {
        iRet = ProGetSpeciallyAckDataSize_LockFree(0, cmd, &dataSize, nullptr, 0, pIoControl);
        if (iRet)
        {
            Logger::WriteLog(eumLogType_Error, "[%s,%d]%d err(%d)\n", __FUNCTION__, __LINE__, cmd, iRet);
            break;
        }
        recLength = ProRecv_LockFree(pIoControl, tmpRecvData, sizeof(int) * 2, 1000);
        if (recLength <= 0)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        streamNum = *(unsigned int *)(tmpRecvData);
        segmentCnt = *(unsigned int *)(tmpRecvData + sizeof(int));

        memset(tmpRecvData, 0, sizeof(tmpRecvData));
        if (0 == streamNum || 0 == segmentCnt)
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }
        for (int streamID = 0; streamID < streamNum; streamID++)
        {
            for (int segmentID = 1; segmentID <= segmentCnt; segmentID++)
            {
                recLength = ProRecv_LockFree(pIoControl, tmpRecvData, sizeof(int), 1000);
                if (recLength <= 0)
                {
                    iRet = WT_ERR_CODE_GENERAL_ERROR;
                    break;
                }

                int elementBytes = *(unsigned int *)(tmpRecvData);
                if (elementBytes < 0)
                {
                    iRet = WT_ERR_CODE_GENERAL_ERROR;
                    break;
                }

                if (elementBytes > 0)
                {
                    recLength = ProRecv_LockFree(pIoControl, (char *)result[index], elementBytes, 5000);
                    if (recLength != elementBytes)
                    {
                        iRet = WT_ERR_CODE_GENERAL_ERROR;
                        break;
                    }
                }
                elementdataBytes[index] = elementBytes;
                index++;
            }
        }

        index = 0;
        for (int streamID = 0; streamID < streamNum; streamID++)
        {
            for (int segmentID = 1; segmentID <= segmentCnt; segmentID++)
            {
                if (0 == elementdataBytes[index])
                {
                    iRet = WT_ERR_CODE_TIMEOUT;
                    break;
                }
            }
        }

    } while (0);

    return iRet;
}

int InstrumentHandle::MoniGetIQVectorResult(
    int cmd,
    int &streamNum,
    int &segmentCnt,
    vector<int> &IQBytes,
    vector<void *>&IQresult,
    vector<int> &CalBytes,
    vector<void *>&Calresult)
{
    int iRet = WT_ERR_CODE_OK;
    IOControl *pIoControl = GetUsableIOControler(IOCONTROL_MISC);
    do
    {

        if (nullptr == pIoControl)
        {
            iRet = WT_ERR_CODE_RESOURCE_BEING_OCCUPIED;
            break;
        }

        Sleep(200);//TODO? 不休眠容易出现获取数据不对，monitor连接单个socket

        iRet = MoniGetIQVectorResult(cmd, streamNum, segmentCnt, IQBytes, IQresult, pIoControl);
        if (iRet)
        {
            break;
        }

        if (segmentCnt <= 1)
        {
            iRet = MoniGetIQVectorResult(CMD_GET_MON_VSA_CAL_PARAM, streamNum, segmentCnt, CalBytes, Calresult, pIoControl);
        }
    } while (0);

    ResleaseCurrIOControler(IOCONTROL_MISC);
    return iRet;
}

int InstrumentHandle::MoniSaveOriginalIQDataToFile(const char *fileName)
{
    int iRet = WT_ERR_CODE_OK;
    int streamNum = 0;
    int segmentCnt = 0;

    stIQDat *iqData = nullptr;
    stPNFileInfo *pnInfoPtr = nullptr;
    bool platFlag = false;
    int tmpStreamCnt = 0;
    vector<void*>calResult;
    vector<void*>IQResult;
    vector<int> calBytes;
    vector<int> IQBytes;

    unique_ptr<Rx_ParmStruct[]> rxParamBuf(new Rx_ParmStruct[WT_SUB_TESTER_INDEX_MAX]);
    Rx_ParmStruct *rxParam = rxParamBuf.get();
    unique_ptr<char[]>iqBuf = nullptr;

    int index = 0;
    //返回数据：streamnum+segmentnum +vsadata_len1+vsadata1+vsadata+len2+....
    for (size_t i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        ClearPNInfor(&m_pnInfos[i]);
        IQResult.push_back(m_pnInfos[i].data);
        calResult.push_back(rxParam[i].GetParamAddr());

        calBytes.push_back(0);
        IQBytes.push_back(0);
    }

    do
    {
        iRet = MoniGetIQVectorResult(CMD_GET_MON_RAW_DATA, streamNum, segmentCnt, IQBytes, IQResult, calBytes, calResult);
        if (iRet)
        {
            break;
        }

        if (0 != calBytes[0])
        {
            platFlag = true;
        }

        index = 0;
        for (int streamID = 0; streamID < streamNum; streamID++)
        {
            for (int segmentID = 1; segmentID <= segmentCnt; segmentID++)
            {
                int PosOffset = streamID*segmentCnt + (segmentID - 1);
                pnInfoPtr = m_pnInfos + PosOffset;

                SetVsaPNStructInfo(
                    pnInfoPtr,
                    streamID,
                    segmentID,
                    segmentCnt,
                    IQBytes[index],
                    enDataFormat_Int16
                );

                SetVsaPNCalParam(
                    pnInfoPtr,
                    rxParam[index].GetParamBBResponseGain(),
                    rxParam[index].GetParamRFResponseGain(),
                    rxParam[index].GetParamNSResponseGain(),
                    rxParam[index].GetParamNSResponseGainSize(),
                    segmentCnt,
                    platFlag
                );

                if (nullptr == iqData)
                {
                    iqBuf.reset(new char[IQBytes[0]]);
                    iqData = (stIQDat*)iqBuf.get();
                }
                memcpy(iqData, pnInfoPtr->data, IQBytes[0]);

                for (int i = 0; i < pnInfoPtr->SampleCount; i++)
                {
                    pnInfoPtr->data[i].dReal = iqData[i].s16Real;
                    pnInfoPtr->data[i].dImag = iqData[i].s16Imag;
                }

                index++;
                tmpStreamCnt++;
            }
        }

        iRet = SaveVsaWaveForm(fileName, tmpStreamCnt);
    } while (0);

    return iRet;
}

int InstrumentHandle::MoniSaveCurrIQDataToFile(
    const char *fileName,
    int signalID)
{
    A_ASSERT(fileName);
    (void)signalID;

    stPNFileInfo *pnInfoPtr = nullptr;
    char tmpRecvData[MAX_NAME_SIZE] = { 0 };
    unsigned int paramSize = 0;
    bool platFlag = false;

    int tmpStreamCnt = 0;
    int iRet = WT_ERR_CODE_OK;
    int streamNum = 0;
    int segmentCnt = 0;
    vector<void*>calResult;
    vector<void*>IQResult;
    vector<int> calBytes;
    vector<int> IQBytes;

    unique_ptr<Rx_ParmStruct[]> rxParamBuf(new Rx_ParmStruct[WT_SUB_TESTER_INDEX_MAX]);
    Rx_ParmStruct *rxParam = rxParamBuf.get();
    int index = 0;

    //返回数据：streamnum+segmentnum +vsadata_len1+vsadata1+vsadata+len2+....
    for (size_t i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        ClearPNInfor(&m_pnInfos[i]);
        IQResult.push_back(m_pnInfos[i].data);

        calResult.push_back(rxParam[i].GetParamAddr());

        calBytes.push_back(0);
        IQBytes.push_back(0);
    }

    do
    {
        iRet = MoniGetIQVectorResult(CMD_GET_MON_VSA_DATA, streamNum, segmentCnt, IQBytes, IQResult, calBytes, calResult);
        if (iRet)
        {
            break;
        }

        if (0 != calBytes[0])
        {
            platFlag = true;
        }

        index = 0;
        for (int streamID = 0; streamID < streamNum; streamID++)
        {
            for (int segmentID = 1; segmentID <= segmentCnt; segmentID++)
            {
                int PosOffset = streamID*segmentCnt + (segmentID - 1);
                pnInfoPtr = m_pnInfos + PosOffset;
                SetVsaPNStructInfo(
                    pnInfoPtr,
                    streamID,
                    segmentID,
                    segmentCnt,
                    IQBytes[index],
                    enDataFormat_Float64
                );

                SetVsaPNCalParam(
                    pnInfoPtr,
                    rxParam[index].GetParamBBResponseGain(),
                    rxParam[index].GetParamRFResponseGain(),
                    rxParam[index].GetParamNSResponseGain(),
                    rxParam[index].GetParamNSResponseGainSize(),
                    segmentCnt,
                    platFlag
                );
                index++;
                tmpStreamCnt++;
            }
        }
        iRet = SaveVsaWaveForm(fileName, tmpStreamCnt);
    } while (0);

func_exit:
    delete[]rxParam;

    return iRet;
}

int InstrumentHandle::SetVsaPNCalParam(
    stPNFileInfo *pnInfoPtr,
    void *BB,
    void *RF,
    void *NS,
    int NsCount,
    int segmentCnt,
    bool platFlag)
{
    if (segmentCnt <= 1 && platFlag)
    {
        pnInfoPtr->bb_response.FreqCount = static_cast<int>(pnInfoPtr->SampleFreq) + 1;
        memcpy(pnInfoPtr->bb_response.Response, BB, pnInfoPtr->bb_response.FreqCount * sizeof(double));
        pnInfoPtr->rf_response.FreqCount = pnInfoPtr->bb_response.FreqCount;
        memcpy(pnInfoPtr->rf_response.Response, RF, pnInfoPtr->bb_response.FreqCount * sizeof(double));
        pnInfoPtr->ns_response.FreqCount = NsCount;
        memcpy(pnInfoPtr->ns_response.Response, RF, pnInfoPtr->ns_response.FreqCount * sizeof(double));
    }

    if (WT_ALZ_PARAM_WIFI == m_AnalyzeParamInfo[IOCONTROL_VSA].sigType &&
        m_AnalyzeParamInfo[IOCONTROL_VSA].paramSize == sizeof(AlzParamWifi))
    {
        AlzParamWifi *alzParam = (AlzParamWifi*)m_AnalyzeParamInfo[IOCONTROL_VSA].analyzeParam;
        pnInfoPtr->clockRate = CheckPnClockRate(alzParam->ClockRate);//default clock rate
    }

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SetVsaPNStructInfo(
    stPNFileInfo *pnInfoPtr,
    int streamID,
    int segmentID,
    int segmentCnt,
    int dataBytes,
    int dataType)
{
    pnInfoPtr->DataType = dataType;
    pnInfoPtr->dCenterFreq = m_vsaParam.Freq;

    if (segmentCnt > 1)
    {
        if (segmentID < segmentCnt)
        {
            pnInfoPtr->dCenterFreq = m_vsaParam.Freq;
        }
        else
        {
            pnInfoPtr->dCenterFreq = m_vsaParam.Freq2;
        }

        if (m_isvsa160mFlag)
        {
            if (segmentID < segmentCnt)
            {
                pnInfoPtr->dCenterFreq = m_vsaParam.Freq - 40 * MHz_API;
            }
            else
            {
                pnInfoPtr->dCenterFreq = m_vsaParam.Freq + 40 * MHz_API;
            }
        }
    }
    pnInfoPtr->ModType = m_vsaParam.Demode;
    pnInfoPtr->SampleFreq = (m_vsaParam.SamplingFreq / MHz_API);
    pnInfoPtr->SampleCount = dataBytes / sizeof(stPNDat);
    switch (dataType)
    {
    case enDataFormat_Int16:
        pnInfoPtr->SampleCount = dataBytes / sizeof(stIQDat);
        break;
    default:
        break;
    }
    pnInfoPtr->FrameEnd = pnInfoPtr->SampleCount;
    pnInfoPtr->freqOffset = m_vsaParam.FreqOffset;
    pnInfoPtr->triggerLevel = m_vsaParam.TrigLevel;

    pnInfoPtr->flag8080 = (segmentCnt > 1 ? 1 : 0);
    pnInfoPtr->sceneMode = m_currTestMode; //TODO 还需确认该值
    pnInfoPtr->vsaAmpl = m_vsaParam.MaxPower[streamID];
    if (segmentCnt > 1 && m_vsaExternParam.WIFI8080DulPortMode)
    {
        if (segmentID < segmentCnt)
        {
            pnInfoPtr->vsaAmpl = m_vsaExternParam.RfPort_MaxPower[streamID * 2];
            pnInfoPtr->triggerLevel = m_vsaExternParam.RfPort_TrigLevel[streamID * 2];
        }
        else
        {
            pnInfoPtr->vsaAmpl = m_vsaExternParam.RfPort_MaxPower[streamID * 2 + 1];
            pnInfoPtr->triggerLevel = m_vsaExternParam.RfPort_TrigLevel[streamID * 2 + 1];
        }
    }
    pnInfoPtr->vsaSourceFalg = 1;
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SaveVsaWaveForm(const char* fileName, int streamCnt)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;

    do
    {
        iRet = PnDataParamValid(m_pnInfos, streamCnt);
        if (iRet)
        {
            break;
        }

        size_t pos = ((string)fileName).find_last_of(".");
        if (pos != string::npos)
        {
            //设置保存段
            TruncatePnInfo(m_pnInfos);
            for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
            {
                if (m_pnInfos[i].SampleCount <= 0)
                {
                    break;
                }
                if (m_pnInfos[i].data != nullptr)
                {
                    iRet = CreatFileByPNFileInfo(&m_pnInfos[i], fileName, i);
                    if (iRet)
                    {
                        Logger::WriteLog(eumLogType_Error, "[%s,%d]CreatFileByPNFileInfo stream(%d) err(%d)\n", __FUNCTION__, __LINE__, i + 1, iRet);
                        break;
                    }
                }
            }
        }
    } while (0);

    return iRet;
}

int InstrumentHandle::MoniSaveCurrIQDataToFile(int saveType, const char *fileName, char *calParam, unsigned int calParamSize, int signalID)
{
    int err = WT_ERR_CODE_OK;

    ENTER_LOCK(PN_OPERATE_LOCK);
    switch (saveType)
    {
    case WT_SAVE_RAW_DATA:
    {
        err = MoniSaveOriginalIQDataToFile(fileName);
        break;
    }
    case WT_SAVE_COMPENSATED_DATA:
    {
        err = MoniSaveCurrIQDataToFile(fileName, signalID);
        break;
    }
    }
    EXIT_LOCK(PN_OPERATE_LOCK);
    return err;
}

int InstrumentHandle::SetWifiFrameFilter(ResultFilter *filter)
{
    ExchangeBuff pstSend;
    pstSend.chpHead = (char *)filter;
    pstSend.buff_len = sizeof(ResultFilter);
    pstSend.data_len = sizeof(ResultFilter);

    if (filter)
    {
        m_WifiFilterEnable = (filter->IsEnable > 0 ? true : false);
    }

    return Exchange(0, CMD_SET_FREAM_FILTER, &pstSend, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetWideSpectrumEnable(int onff)
{
    ExchangeBuff pstSend;
    pstSend.chpHead = (char *)&onff;
    pstSend.buff_len = sizeof(int);
    pstSend.data_len = sizeof(int);

    m_SpectrumWideEnable = (onff > 0 ? true : false);

    return Exchange(0, CMD_SET_IS_204M_SPECT_ON, &pstSend, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::ProcWifiVsaSpecial()
{
    int iRet = WT_ERR_CODE_OK;
    //自动关闭小于240MHz时的频谱WIFI Wide功能
    if (WT_ERR_CODE_OK == iRet && m_SpectrumWideEnable &&
        m_vsaParam.SamplingFreq < m_maxSampleRate)
    {
        iRet = SetWideSpectrumEnable(0);
    }

    if (WT_ERR_CODE_OK == iRet && m_WifiFilterEnable)
    {
        //非WIFI模式，关闭WIFI过滤功能
        if (!WIDE_MOD(m_vsaParam.Demode))
        {
            ResultFilter tmpFilter;
            memset(&tmpFilter, 0, sizeof(tmpFilter));
            iRet = SetWifiFrameFilter(&tmpFilter);
        }
    }

    return iRet;
}
#ifndef LINUX
int InstrumentHandle::TB_Start(int timeout_ms)
{
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    if (m_vecPnItem.size() > 10)
    {
        sendTimeOut_ms += 1000 * m_vecPnItem.size();
        recvTimeOut_ms += 1000 * m_vecPnItem.size();
    }
    err = Exchange(0, CMD_START_TB, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }

    int vsaStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int infinity = 0;
    if (0 == UsualKit::DoubleCompare(timeout_ms, 0.0))
    {
        infinity = 1;
    }
    timeval stTimeStart, stTimeEnd;
    UsualKit::gettimeofday(&stTimeStart);
    while (true)
    {
        if (0 == infinity)
        {
            UsualKit::gettimeofday(&stTimeEnd);
            if (UsualKit::elapsed_time(&stTimeStart, &stTimeEnd) >= timeout_ms)
            {
                Exchange(0, CMD_STOP_TB, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }
        err = TB_GetStatus(&vsaStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_CAPTURE_DATA_ERROR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsaStatu)
        {
            Sleep(1);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_NO_DATA_CAPTURED;
            break;
        }
    }
func_exit:
    return err;
}
#else
int InstrumentHandle::TB_Start(int timeout_ms)
{
    int err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 3000;
    u32 sendTimeOut_ms = 1000;

    int vsaStatu = WT_VSG_VSA_STATE_TIMEOUT;
    int infinity = 0;
    if (0 == UsualKit::DoubleCompare(timeout_ms, 0.0))
    {
        infinity = 1;
    }
    timeval stTimeStart, stTimeEnd;

    if (WT_CONNECT_TYPE_MultiUser == m_linkType || WT_CONNECT_TYPE_NORMAL == m_linkType)
    {
        u32 multiLinkTimeOut_ms = static_cast<int>(m_vsaParam.TimeoutWaiting) * 1000;
        recvTimeOut_ms += multiLinkTimeOut_ms;
    }
    if (m_vecPnItem.size() > 10)
    {
        sendTimeOut_ms += 1000 * m_vecPnItem.size();
        recvTimeOut_ms += 1000 * m_vecPnItem.size();
    }
    err = Exchange(0, CMD_START_TB, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);
    if (WT_ERR_CODE_OK != err)
    {
        goto func_exit;
    }
    gettimeofday(&stTimeStart, nullptr);
    while (true)
    {
        if (0 == infinity)
        {
            gettimeofday(&stTimeEnd, nullptr);
            if (UsualKit::elapsed_time(&stTimeStart, &stTimeEnd) >= timeout_ms)
            {
                Exchange(0, CMD_STOP_TB, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
                err = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }
        err = TB_GetStatus(&vsaStatu);
        if (WT_ERR_CODE_OK != err)
        {
            break;
        }

        if (WT_VSG_VSA_STATE_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_OK;
            break;
        }
        else if (WT_VSG_VSA_STATE_ERR_DONE == vsaStatu)
        {
            err = WT_ERR_CODE_CAPTURE_DATA_ERROR;
            break;
        }
        else if (WT_VSG_VSA_STATE_RUNNING == vsaStatu)
        {
            usleep(1000);
            continue;
        }
        else
        {
            err = WT_ERR_CODE_NO_DATA_CAPTURED;
            break;
        }
    }
func_exit:
    return err;
}
#endif
int InstrumentHandle::TB_Stop()
{
    int cnt = 1;
    int err = WT_ERR_CODE_OK;
    while (cnt)
    {
        err = Exchange(0, CMD_STOP_TB, nullptr, 0, nullptr, 0, IOCONTROL_VSA, 1000, 2000);
        if (WT_ERR_CODE_OK == err)
        {
            break;
        }
        cnt--;
    }
    return err;
}

int InstrumentHandle::TB_GetStatus(int *status)
{
    A_ASSERT(status);
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)status;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);
    return Exchange(0, CMD_GET_TB_STATUS, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);
}

int InstrumentHandle::TB_Init()
{
    SetVsaVsgLinkShared(HANDLE_SCENARIO_TB_TEST);
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::TB_Release()
{
    //主动退出TB分析模式，避免客户忘记配置导致正常WIFI分析失败
    SetExternAnalyzeParam(WT_DEMOD_11AX_20M, -1, nullptr, 0);
    SetVsaVsgLinkShared(HANDLE_SCENARIO_NORMAL);
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::TB_SetParam(InterBindParameter *Param, int vsaTrigger)
{
    int iRet = WT_ERR_CODE_OK;
#ifdef LINUX
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Param->VsgPattNum:" << Param->VsgPattNum << std::endl;
#endif
    A_ASSERT(Param);
    A_ASSERT(Param->VsgPattNum && Param->VsgPatt);

    std::unique_ptr<VsaParameter> tmpVsaParameter(new VsaParameter);
    std::unique_ptr<VsgParameter>tmpVsgParameter(new VsgParameter);
    A_ASSERT(tmpVsaParameter && tmpVsgParameter);

    GetDefaultParameter(tmpVsaParameter.get(), nullptr, tmpVsgParameter.get(), nullptr, nullptr);

    //VSA
    tmpVsaParameter->Freq = Param->Freq;
    tmpVsaParameter->Freq2 = Param->Freq2;
    memcpy(&tmpVsaParameter->MaxPower[0], &Param->VsaMaxPower[0], sizeof(Param->VsaMaxPower));
    memcpy(&tmpVsaParameter->RfPort[0], &Param->VsaRfPort[0], sizeof(Param->VsaRfPort));
    memcpy(&tmpVsaParameter->ExtPathLoss[0], &Param->VsaExtPathLoss[0], sizeof(Param->VsaExtPathLoss));
    memcpy(&tmpVsaParameter->ExtPathLoss2[0], &Param->VsaExtPathLoss2[0], sizeof(Param->VsaExtPathLoss2));
    tmpVsaParameter->SmpTime = Param->VsaSmpTime;
    tmpVsaParameter->Demode = Param->VsaDemode;
    tmpVsaParameter->TrigType = vsaTrigger;
    tmpVsaParameter->TrigLevel = Param->TrigLevel;
    tmpVsaParameter->TrigTimeout = Param->TriggerTimeOut;
    tmpVsaParameter->TrigPretime = Param->VsaPreTrigger;
    tmpVsaParameter->SamplingFreq = Param->VsaSamplingFreq;

    //VSG
    tmpVsgParameter->Freq = Param->Freq;
    tmpVsgParameter->Freq2 = Param->Freq2;
    tmpVsgParameter->SamplingFreq = Param->VsgSamplingFreq;
    memcpy(&tmpVsgParameter->Power[0], &Param->VsgPower[0], sizeof(Param->VsgPower));
    memcpy(&tmpVsgParameter->RfPort[0], &Param->VsgRfPort[0], sizeof(Param->VsgRfPort));
    memcpy(&tmpVsgParameter->ExtPathLoss[0], &Param->VsgExtPathLoss[0], sizeof(Param->VsgExtPathLoss));
    memcpy(&tmpVsgParameter->ExtPathLoss2[0], &Param->VsgExtPathLoss2[0], sizeof(Param->VsgExtPathLoss2));

    do
    {
        if (Param->DigParam && TESTER_RUN_DIGIT_IQ == m_TesterRunMode)
        {
            SetDigtalIQParam(Param->DigParam);
            if (iRet)
            {
#ifdef LINUX
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetDigtalIQParam error = " << iRet << std::endl;
#endif
                break;
            }
        }

        iRet = SetVSAParam(tmpVsaParameter.get(), nullptr);
        if (iRet)
        {
#ifdef LINUX
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetVSAParam error = " << iRet << std::endl;
#endif
            break;
        }

        if ('\0' != Param->VsgPatt->WaveName[0])
        {
            if (Param->NeedSetVSGPattern)
            {
                iRet = SetVSGPattern(Param->VsgPatt, Param->VsgPattNum, nullptr, 1);
                if (iRet)
                {
#ifdef LINUX
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetVSGPattern error = " << iRet << std::endl;
#endif
                    break;
                }
            }

            iRet = SetVSGParam(tmpVsgParameter.get(), nullptr);
            if (iRet)
            {
#ifdef LINUX
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "SetVSGParam error = " << iRet << std::endl;
#endif
                break;
            }
        }
    } while (0);

    return iRet;
}

int InstrumentHandle::TB_AutoRange(InterBindParameter *Param)
{
    int iRet = WT_ERR_CODE_OK;

    A_ASSERT(Param);
    A_ASSERT(Param->VsgPattNum && Param->VsgPatt);

    iRet = TB_SetParam(Param);
    if (iRet != WT_ERR_CODE_OK)
    {
        return iRet;
    }
    size_t SendTimeout_ms = 1000;//1000ms
    size_t RecvTimeout_ms = 5000;//5000ms
    RecvTimeout_ms += (static_cast<int>(Param->VsgPatt->Wave_gap) * 1000) * 20;

    size_t memory_size = m_currSubTesterCount * sizeof(VsaCapParam);
    std::unique_ptr<char[]> tmpBuf(new (std::nothrow) char[memory_size]);
    VsaCapParam *vsaCapParam = (VsaCapParam *)tmpBuf.get();
    if (nullptr == vsaCapParam)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }
    memset(vsaCapParam, 0, memory_size);

    int signalCount = 0;
    CmdHeader tmpHeader;
    ExchangeBuff pstRecvBufff[2];
    pstRecvBufff[0].chpHead = (char *)(&signalCount);
    pstRecvBufff[0].buff_len = sizeof(int);
    pstRecvBufff[0].data_len = sizeof(int);

    pstRecvBufff[1].chpHead = (char *)(vsaCapParam);
    pstRecvBufff[1].buff_len = memory_size;
    pstRecvBufff[1].data_len = memory_size;


    iRet = Exchange(0, CMD_VSA_AUTO_RANGE_TB, nullptr, 0, pstRecvBufff, 2, IOCONTROL_VSA, SendTimeout_ms, RecvTimeout_ms, &tmpHeader);

    if (WT_ERR_CODE_OK != iRet)
    {
        goto func_exit;
    }

    if (signalCount != m_currSubTesterCount)
    {
        iRet = WT_ERR_CODE_GET_TESTERINFO_ERR;
        goto func_exit;
    }

    for (int testerIndex = 0; testerIndex < m_currSubTesterCount; testerIndex++)
    {
        Param->TrigLevel = vsaCapParam[0].TrigLevel;
        Param->VsaMaxPower[testerIndex] = vsaCapParam[testerIndex].Ampl;
    }


    // 	memcpy(&m_VsaParameterBack, &m_vsaParam, sizeof(VsaParameter));
    // 	memcpy(&m_vsaParam, vsaParam, sizeof(VsaParameter));
    m_VsaParamSerialNum = tmpHeader.SerialNum;

    // 	if (WT_ERR_CODE_OK == iRet)
    // 	{
    // 		ProcWifiVsaSpecial();
    // 	}
func_exit:

    return iRet;

}

int InstrumentHandle::SetVsaCalDataCompensate(int ON_FF)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&ON_FF;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSA_FLATNESS_CAL_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::SetVsaIQImbCompensate(int ON_FF)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&ON_FF;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSA_IQIMB_CAL_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::GetVsaFlatnessCalCompensate(int *ON_FF)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&ON_FF;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_GET_VSA_FLATNESS_CAL_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::GetVsaIQImbCompensate(int *ON_FF)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&ON_FF;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_GET_VSA_IQIMB_CAL_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSG);
}

int InstrumentHandle::ClrVSAAvgData()
{
	return Exchange(0, CMD_GET_CLR_AVG_DATA, nullptr, 0, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetVSATrigParam(VsaTrigParam* Param)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8*)Param;
    pstSendBuff.buff_len = sizeof(VsaTrigParam);
    pstSendBuff.data_len = sizeof(VsaTrigParam);

    return Exchange(0, CMD_SET_VSA_TRIG_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::GetVSATrigParam(VsaTrigParam* Param)
{
    ExchangeBuff pstRecvBufff;
    pstRecvBufff.chpHead = (char*)Param;
    pstRecvBufff.buff_len = sizeof(VsaTrigParam);
    pstRecvBufff.data_len = sizeof(VsaTrigParam);

    return Exchange(0, CMD_SET_VSA_TRIG_PARAM, nullptr, 0, &pstRecvBufff, 1, IOCONTROL_VSA);
}

int InstrumentHandle::GetSpectrumPointPower(double Offset, double* Power, int signalID, int segmentID)
{
    struct
    {
        double Offset;
        int signalID;
        int segmentID;
    }Temp;
    Temp.Offset = Offset;
    Temp.signalID = signalID;
    Temp.segmentID = segmentID;
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (char*)&Temp;
    pstSendBuff.buff_len = sizeof(Temp);
    pstSendBuff.data_len = sizeof(Temp);

    ExchangeBuff pstRecvBufff;
    pstRecvBufff.chpHead = (char*)Power;
    pstRecvBufff.buff_len = sizeof(*Power);
    pstRecvBufff.data_len = sizeof(*Power);

    Exchange(0, CMD_GET_VSA_SPECTRUM_POINT_POWER, &pstSendBuff, 1, &pstRecvBufff, 1, IOCONTROL_VSA);
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SetVsaExtendEVMStatus(int Status)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&Status;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_EXTEND_EVM_STATUS, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetVsaIterativeEVMStatus(int Status)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&Status;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSA_ITERATIVE_EVM_STATUS, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetVsaSncEVMStatus(int Status)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&Status;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSA_SNC_EVM_STATUS, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetVsaCcEVMStatus(int Status)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (s8 *)&Status;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_VSA_CC_EVM_STATUS, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}


//listmod
int InstrumentHandle::SetListModeEnable(bool TxFlag)
{
    const int MaxBufSize = sizeof(bool);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &TxFlag, sizeof(bool));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(bool);
    pstSendBuff.data_len = sizeof(bool);

    return Exchange(0, CMD_SET_LIST_ENABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetListModeDisable(bool TxFlag)
{
    const int MaxBufSize = sizeof(bool);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &TxFlag, sizeof(bool));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(bool);
    pstSendBuff.data_len = sizeof(bool);

    return Exchange(0, CMD_SET_LIST_DISABLE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetSegVsaClear()
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = NULL;
    pstSendBuff.buff_len = 0;
    pstSendBuff.data_len = 0;

    return Exchange(0, CMD_SET_LIST_SEG_VSA_CLEAR, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetSegVsaAlzCommParam(int SegNo, AlzParamComm *commonAnalyzeParam)
{
    return SetSegVsaAnalyzeParam(0, SegNo, commonAnalyzeParam, sizeof(AlzParamComm));
}

int InstrumentHandle::SetSegVsaAnalyzeParam(int signalType, int SegNo, void *AnalyzeParam, int Size)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize = Size + sizeof(int) * 2;

    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);
    char *proBuff = TmpBuf.get();

    memcpy(proBuff, &signalType, sizeof(int));
    memcpy(proBuff + sizeof(int), &SegNo, sizeof(int));
    memcpy(proBuff + sizeof(int) + sizeof(int), AnalyzeParam, Size);

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = Size + sizeof(int) * 2;
    pstSendBuff.data_len = Size + sizeof(int) * 2;
    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_ALZ_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::SetSegVsaCapParam(int Segno, VsaParameter *vsaParam)
{
    VsaCapParam *vsaCapParam;
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize = sizeof(VsaCapParam) + sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &Segno, sizeof(int));

    vsaCapParam = (VsaCapParam *)(proBuff + sizeof(int));
    memset(vsaCapParam, 0, sizeof(vsaCapParam));
    vsaCapParam->RFPort = vsaParam->RfPort[0] - 1;
    vsaCapParam->Freq = vsaParam->Freq;
    vsaCapParam->Freq2 = vsaParam->Freq2;
    vsaCapParam->FreqOffset = vsaParam->FreqOffset;
    vsaCapParam->Type = 0;
    vsaCapParam->TrigType = vsaParam->TrigType;
    vsaCapParam->Ampl = vsaParam->MaxPower[0];
    vsaCapParam->ExtPathLoss = vsaParam->ExtPathLoss[0];
    vsaCapParam->ExtPathLoss2 = vsaParam->ExtPathLoss2[0];
    vsaCapParam->SamplingTime = vsaParam->SmpTime;
    vsaCapParam->SamplingFreq = vsaParam->SamplingFreq;

    vsaCapParam->Demod = vsaParam->Demode;
    //vsaCapParam->Demod |= 0xA5B70000;

    vsaCapParam->TrigPreTime = vsaParam->TrigPretime;
    vsaCapParam->TrigTimeout = vsaParam->TrigTimeout;
    vsaCapParam->TrigLevel = vsaParam->TrigLevel;
    vsaCapParam->AllocTimeout = (int)vsaParam->TimeoutWaiting;
    vsaCapParam->MaxIFG = vsaParam->MaxIFGGap;
    vsaCapParam->MasterMode = 0;
    vsaCapParam->SignalId = 0;
    vsaCapParam->VsaMask = 0;
    vsaCapParam->Is160M = 0;

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(VsaCapParam) + sizeof(int);
    pstSendBuff.data_len = sizeof(VsaCapParam) + sizeof(int);
    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_CAP_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::SetSegVsaTrigParam(int Segno, VsaTrigParam *vsatrigParam)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize = sizeof(VsaTrigParam) + sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);
    VsaTrigParam *TrigParamTmp = nullptr;

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &Segno, sizeof(int));

    TrigParamTmp = (VsaTrigParam *)(proBuff + sizeof(int));
    memcpy(TrigParamTmp, vsatrigParam, sizeof(VsaTrigParam));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = MaxBufSize;
    pstSendBuff.data_len = MaxBufSize;

    return Exchange(0, CMD_SET_LIST_SEG_VSA_TRIG_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);
}

int InstrumentHandle::SetSegTrigCommonParam(bool TxFlag, int Segno, void *SegTrigCommParam, int Size)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize = Size + sizeof(bool) + sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &TxFlag, sizeof(bool));
    memcpy(proBuff + sizeof(bool), &Segno, sizeof(int));
    memcpy(proBuff + sizeof(bool) + sizeof(int), SegTrigCommParam, Size);

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = MaxBufSize;
    pstSendBuff.data_len = MaxBufSize;
    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_TRIG_COMMON_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::SetSegVsaAlzProtoParam(int Segno, int AlzType, void *ProAnalyzeParam, int Size)
{
    return SetSegVsaAnalyzeParam(AlzType, Segno, ProAnalyzeParam, Size);
}

int InstrumentHandle::SetSegSeqTimeParam(bool TxFlag, int Segno, void *SegTimeParam, int Size)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize = Size + sizeof(bool) + sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &TxFlag, sizeof(bool));
    memcpy(proBuff + sizeof(bool), &Segno, sizeof(int));
    memcpy(proBuff + sizeof(bool) + sizeof(int), SegTimeParam, Size);

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = Size + sizeof(int) + sizeof(bool);
    pstSendBuff.data_len = Size + sizeof(int) + sizeof(bool);
    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_TIME_PARAM, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::SetListTxSeqStart(double TrigerOffset)
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 10000;  //暂时定10s
    u32 sendTimeOut_ms = 1000;
    const int MaxBufSize = sizeof(double);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &TrigerOffset, sizeof(double));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(double);
    pstSendBuff.data_len = sizeof(double);

    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_START_SEQ, &pstSendBuff, 1, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::SetListTxRxSeqStart()
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 10000;  //暂时定10s
    u32 sendTimeOut_ms = 1000;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSAVSG_START_SEQ, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::SetListTxSeqStop()
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 10000;  //暂时定10s
    u32 sendTimeOut_ms = 1000;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_STOP_SEQ, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::SetListTxRxSeqStop()
{
    int Err = WT_ERR_CODE_OK;
    u32 recvTimeOut_ms = 10000;  //暂时定10s
    u32 sendTimeOut_ms = 1000;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSAVSG_STOP_SEQ, nullptr, 0, nullptr, 0, IOCONTROL_VSA, sendTimeOut_ms, recvTimeOut_ms);

    return Err;
}

int InstrumentHandle::GetListSeqAllState(bool TxFlag, int *State)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize =sizeof(bool);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    A_ASSERT(State);
    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &TxFlag, sizeof(bool));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(bool);
    pstSendBuff.data_len = sizeof(bool);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)State;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);

    if(TxFlag)
    {
        Err = Exchange(0, CMD_GET_LIST_SEQ_VSAVSG_STATE, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);
    }
    else
    {
        Err = Exchange(0, CMD_GET_LIST_SEQ_VSAVSG_STATE, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSG);
    }

    return Err;
}

int InstrumentHandle::GetListTxSeqAllCapState(int *SegNo)
{
    int Err = WT_ERR_CODE_OK;

    A_ASSERT(SegNo);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)SegNo;
    pstRecvBuff.buff_len = sizeof(int) * 2;
    pstRecvBuff.data_len = sizeof(int) * 2;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_CAP_STATE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::GetListTxSeqAllAnalyState(int *SegNo)
{
    int Err = WT_ERR_CODE_OK;

    A_ASSERT(SegNo);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)SegNo;
    pstRecvBuff.buff_len = sizeof(int) * 2;
    pstRecvBuff.data_len = sizeof(int) * 2;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_ANNALY_STATE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::GetListTxSeqAllPowerResult(double *PowerResult, int SegNum)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize =sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    A_ASSERT(PowerResult);
    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &SegNum, sizeof(int));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)PowerResult;
    pstRecvBuff.buff_len = sizeof(double) * SegNum;
    pstRecvBuff.data_len = sizeof(double) * SegNum;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_POWER_RESULT, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::GetListLteTxSeqAllSegState(int *LteTxSegStat, int SegNum)
{
    int Err = WT_ERR_CODE_OK;
    const int MaxBufSize =sizeof(int);
    std::unique_ptr<char[]>TmpBuf(new (std::nothrow) char[MaxBufSize]);

    A_ASSERT(LteTxSegStat);
    char *proBuff = TmpBuf.get();
    memcpy(proBuff, &SegNum, sizeof(int));

    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = proBuff;
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)LteTxSegStat;
    pstRecvBuff.buff_len = sizeof(int) * SegNum;
    pstRecvBuff.data_len = sizeof(int) * SegNum;

    Err = Exchange(0, CMD_SET_LIST_SEG_VSA_LTE_TX_SEG_STAT, &pstSendBuff, 1, &pstRecvBuff, 1, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::DuplexSetEnable(int enable)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (char *)(&enable);
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_DUPLEX_SET_STATE, &pstSendBuff, 1, nullptr, 0, IOCONTROL_MISC);
}

int InstrumentHandle::DuplexGetEnable(int *enable)
{
    int Err = WT_ERR_CODE_OK;

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)enable;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);

    Err = Exchange(0, CMD_DUPLEX_GET_STATE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);

    return Err;
}

int InstrumentHandle::SetDuplexVsaNoiseCompFlag(int flag)
{
    ExchangeBuff pstSendBuff;
    pstSendBuff.chpHead = (char *)(&flag);
    pstSendBuff.buff_len = sizeof(int);
    pstSendBuff.data_len = sizeof(int);

    return Exchange(0, CMD_SET_DUPLEX_NOISE_FLAG, &pstSendBuff, 1, nullptr, 0, IOCONTROL_MISC);
}

int InstrumentHandle::GetDuplexVsaNoiseCompFlag(int *flag)
{
    int Err = WT_ERR_CODE_OK;

    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)flag;
    pstRecvBuff.buff_len = sizeof(int);
    pstRecvBuff.data_len = sizeof(int);

    Err = Exchange(0, CMD_GET_DUPLEX_NOISE_FLAG, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA);

    return Err;
}