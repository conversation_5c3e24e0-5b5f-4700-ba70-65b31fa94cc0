#include "commonhandler.h"
#include "vsghandler.h"
#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "tester.h"
#include "internal.h"
#include "tester_mt.h"
#include "vsa_data_info.h"
#include "vsa_eht.h"
#include "wtlog.h"
#define MAX_BE_RU_CNT 144

static bool GetNSSStartID_11BeMUMIMO(UserInfo11Be &User, int &NSS)
{
    bool isOK = false;
    for (int i = 0; i < MAX_DF_NUM; i++)
    {
        if (User.NstsFlag[i] > 0)
        {
            NSS = i;
            isOK = true;
            break;
        }
    }
    return isOK;
}

int is11BE(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11BE_20M, WT_DEMOD_11BE_160_160M);
}

int is11BE_MUMIMO(scpi_t *context, bool &MUFlag)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        MUFlag = false;

        iRet = is11BE(context);
        if (iRet)
        {
            break;
        }

        unique_ptr<DataInfo11Be> DataInfo = make_unique<DataInfo11Be>();
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, DataInfo.get(), sizeof(DataInfo11Be));
        if (iRet)
        {
            break;
        }

        if (1 == DataInfo.get()->common.MUMIMO)
        {
            MUFlag = true;
        }

    } while (0);

    return iRet;
}

static int GetRUID_NSSID(scpi_t *context, int &RUID, int &NSS_ID)
{
    int iRet = WT_ERR_CODE_OK;
    int Number[2] = {0, 0};
    RUID = 1;
    NSS_ID = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, 2))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        //RUID从1开始
        if (Number[0] < 1)
        {
            Number[0] = 1;
        }

        if (Number[1] < 0)
        {
            Number[1] = 0;
        }
        RUID = Number[0];
        NSS_ID = Number[1];

        if (RUID > MAX_BE_RU_CNT || NSS_ID > MAX_DF_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);
    return iRet;
}

static int GetRUID_User_NSSID(scpi_t *context, int &RUID, int &UserID, int &NSS_ID)
{
    int iRet = WT_ERR_CODE_OK;
    int Number[3] = {0, 0, 0};

    do
    {
        if (!SCPI_CommandNumbers(context, Number, 3))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        //RUID从1开始
        if (Number[0] < 1)
        {
            Number[0] = 1;
        }

        if (Number[1] < 1)
        {
            Number[1] = 1;
        }

        if (Number[2] < 0)
        {
            Number[2] = 0;
        }

        RUID = Number[0];
        UserID = Number[1];
        NSS_ID = Number[2];

        if (RUID > MAX_BE_RU_CNT || UserID > 8 || NSS_ID > MAX_DF_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

    } while (0);
    return iRet;
}

static void ResqInvalidValue(scpi_t *context, bool isDouble)
{
    if (false == isDouble)
    {
        SCPI_ResultInt(context, -999);
    }
    else
    {
        SCPI_ResultDouble(context, -999.999);
    }
}

#define Resq_11Be_DataInfo(name, field, isDouble)                                  \
    do                                                                             \
    {                                                                              \
        int iRet = WT_ERR_CODE_OK;                                                 \
        int RUID = 1;                                                              \
        int NSS = 0;                                                               \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context); \
        iRet = GetRUID_NSSID(context, RUID, NSS);                                  \
        IF_ERR_RETURN(iRet);                                                       \
        iRet = is11BE(context);                                                    \
        IF_ERR_RETURN(iRet);                                                       \
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);                    \
        DataInfo11Be *Info = DataInfoBuf.get();                                    \
        iRet = WT_GetVectorResult(attr->ConnID,                                    \
                                  WT_RES_DATA_INFO,                                \
                                  Info,                                            \
                                  sizeof(DataInfo11Be));                           \
        IF_ERR_RETURN(iRet);                                                       \
        if (false == isDouble)                                                     \
        {                                                                          \
            SCPI_ResultInt(context, Info->field.name);                             \
        }                                                                          \
        else                                                                       \
        {                                                                          \
            SCPI_ResultDouble(context, Info->field.name);                          \
        }                                                                          \
    } while (0)

#define Resq_11Be_DataInfo_array(name, field, isDouble)                            \
    do                                                                             \
    {                                                                              \
        int iRet = WT_ERR_CODE_OK;                                                 \
        int RUID = 1;                                                              \
        int NSS = 0;                                                               \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context); \
        iRet = GetRUID_NSSID(context, RUID, NSS);                                  \
        IF_ERR_RETURN(iRet);                                                       \
        iRet = is11BE(context);                                                    \
        IF_ERR_RETURN(iRet);                                                       \
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);                    \
        DataInfo11Be *Info = DataInfoBuf.get();                                    \
        iRet = WT_GetVectorResult(attr->ConnID,                                    \
                                  WT_RES_DATA_INFO,                                \
                                  Info,                                            \
                                  sizeof(DataInfo11Be));                           \
        IF_ERR_RETURN(iRet);                                                       \
        if (false == isDouble)                                                     \
        {                                                                          \
            for (int i = 0; i < ARRAYSIZE(Info->field.name); i++)                  \
            {                                                                      \
                SCPI_ResultInt(context, Info->field.name[i]);                      \
            }                                                                      \
        }                                                                          \
        else                                                                       \
        {                                                                          \
            for (int i = 0; i < ARRAYSIZE(Info->field.name); i++)                  \
            {                                                                      \
                SCPI_ResultDouble(context, Info->field.name[i]);                   \
            }                                                                      \
        }                                                                          \
    } while (0)

#define Resq_11Be_vector_DataInfo(name, len_name, field, isDouble)                 \
    do                                                                             \
    {                                                                              \
        int iRet = WT_ERR_CODE_OK;                                                 \
        int RUID = 1;                                                              \
        int NSS = 0;                                                               \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context); \
        iRet = GetRUID_NSSID(context, RUID, NSS);                                  \
        IF_ERR_RETURN(iRet);                                                       \
        iRet = is11BE(context);                                                    \
        IF_ERR_RETURN(iRet);                                                       \
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);                    \
        DataInfo11Be *Info = DataInfoBuf.get();                                    \
        iRet = WT_GetVectorResult(attr->ConnID,                                    \
                                  WT_RES_DATA_INFO,                                \
                                  Info,                                            \
                                  sizeof(DataInfo11Be));                           \
        IF_ERR_RETURN(iRet);                                                       \
        if (Info->field.len_name <= 0)                                             \
        {                                                                          \
            iRet = WT_RESULT_UNVALID;                                              \
        }                                                                          \
        IF_ERR_RETURN(iRet);                                                       \
        if (false == isDouble)                                                     \
        {                                                                          \
            for (int i = 0; i < Info->field.len_name; i++)                         \
            {                                                                      \
                SCPI_ResultInt(context, Info->field.name[i]);                      \
            }                                                                      \
        }                                                                          \
        else                                                                       \
        {                                                                          \
            for (int i = 0; i < Info->field.len_name; i++)                         \
            {                                                                      \
                SCPI_ResultDouble(context, Info->field.name[i]);                   \
            }                                                                      \
        }                                                                          \
    } while (0)

scpi_result_t GetVsaRstEhtodmaInfo(scpi_t *context, int RUID, int StreamID)
{
    //be非mu-mimo的用户ofdma 信息RUID为0，表示取全部RU的信息，否则指定某个用户的结果；stream为0取综合的power evm，否则返回指定流的
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
        DataInfo11Be *Info = DataInfoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_DATA_INFO,
                                  Info,
                                  sizeof(DataInfo11Be));
        IF_ERR_RETURN(iRet);
        ILLEGAL_PARAM_RETURN(RUID < 0 || RUID > Info->common.RUCnt);

        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,
                                  RU_MUMIMO,
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));
        IF_ERR_RETURN(iRet);
        if (0 == RUID)
        {
            SCPI_ResultInt(context, Info->common.RUCnt);
            for (int i = 0; i < Info->common.RUCnt; i++)
            {
                SCPI_ResultInt(context, RU_MUMIMO[i].ValidFlag);
            }

            for (int i = 0; i < Info->common.RUCnt; i++)
            {
                SCPI_ResultInt(context, RU_MUMIMO[i].ValidFlag);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].UserID);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].AID);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ToneWide);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ToneIdx);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].MCS);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].NSS);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].Beamformed);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].CodingType);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].DCM);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].SpatialConfig);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].CodingRate);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].Modulation);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].PSDUCRC);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].PSDULength);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ampduValid);
                SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ampduNum);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].Rate);
                if (0 == StreamID)
                {
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].Power);
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].PilotEvm);
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].DataEvm);
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].AllEvm);
                }
                else
                {
                    int index = StreamID - 1;
                    int tmpID = 0;
                    if (GetNSSStartID_11BeMUMIMO(RU_MUMIMO[i].User[0], tmpID))
                    {
                        index += tmpID;
                    }
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].PowerNsts[index]);
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].PilotEvmNsts[index]);
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].DataEvmNsts[index]);
                    SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].AllEvmNsts[index]);
                }
            }
        }
        else
        {
            int i = RUID - 1;
            SCPI_ResultInt(context, RU_MUMIMO[i].ValidFlag);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].UserID);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].AID);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ToneWide);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ToneIdx);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].MCS);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].NSS);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].Beamformed);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].CodingType);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].DCM);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].SpatialConfig);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].CodingRate);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].Modulation);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].PSDUCRC);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].PSDULength);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ampduValid);
            SCPI_ResultInt(context, RU_MUMIMO[i].User[0].ampduNum);
            SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].Rate);

            if (0 == StreamID)
            {
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].Power);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].PilotEvm);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].DataEvm);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].AllEvm);
            }
            else
            {
                int index = StreamID - 1;
                int tmpID = 0;
                if (GetNSSStartID_11BeMUMIMO(RU_MUMIMO[i].User[0], tmpID))
                {
                    index += tmpID;
                }
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].PowerNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].PilotEvmNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].DataEvmNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[i].User[0].AllEvmNsts[index]);
            }
        }
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_PPDU(scpi_t *context)
{
    Resq_11Be_DataInfo(PPDU, common, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_RUCnt(scpi_t *context)
{
    Resq_11Be_DataInfo(RUCnt, common, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_UserCnt(scpi_t *context)
{
    Resq_11Be_DataInfo(UserCnt, common, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_ActiveUserCnt(scpi_t *context)
{
    Resq_11Be_DataInfo(ActiveUserCnt, common, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SymbolCnt(scpi_t *context)
{
    Resq_11Be_DataInfo(SymbolCnt, common, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LTFLen(scpi_t *context)
{
    Resq_11Be_DataInfo(LTFLen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_GILen(scpi_t *context)
{
    Resq_11Be_DataInfo(GILen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_DataInfoDataRate(scpi_t *context)
{
    Resq_11Be_DataInfo(DataRate, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_PELen(scpi_t *context)
{
    Resq_11Be_DataInfo(PELen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_DataInfoNSTS(scpi_t *context)
{
    Resq_11Be_DataInfo(NSTS, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_DataLen(scpi_t *context)
{
    Resq_11Be_DataInfo(DataLen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_FrameLen(scpi_t *context)
{
    Resq_11Be_DataInfo(FrameLen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_PreambleLen(scpi_t *context)
{
    Resq_11Be_DataInfo(PreambleLen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_DataSymbolLen(scpi_t *context)
{
    Resq_11Be_DataInfo(SymbolLen, common, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_CRC(scpi_t *context)
{
    Resq_11Be_DataInfo(CRC, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_PHY(scpi_t *context)
{
    Resq_11Be_DataInfo(PhyVersion, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_BW(scpi_t *context)
{
    Resq_11Be_DataInfo(BW, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_ULDL(scpi_t *context)
{
    Resq_11Be_DataInfo(ULDL, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_BSSColor(scpi_t *context)
{
    Resq_11Be_DataInfo(BSSColor, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_TXOP(scpi_t *context)
{
    Resq_11Be_DataInfo(TXOP, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_Disregard(scpi_t *context)
{
    Resq_11Be_DataInfo(Disregard, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_B25(scpi_t *context)
{
    Resq_11Be_DataInfo(B25Valid, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_CompressionMode(scpi_t *context)
{
    Resq_11Be_DataInfo(CompressionMode, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_U2B2(scpi_t *context)
{
    Resq_11Be_DataInfo(U2B2Valid, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_U2B8(scpi_t *context)
{
    Resq_11Be_DataInfo(U2B8Valid, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_MCS(scpi_t *context)
{
    Resq_11Be_DataInfo(SIGMCS, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_Symbol(scpi_t *context)
{
    Resq_11Be_DataInfo(SIGSymbol, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_PunturingBit(scpi_t *context)
{
    Resq_11Be_vector_DataInfo(PuncBit, PuncBitLen, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_SoundingNDP(scpi_t *context)
{
    Resq_11Be_DataInfo(SoundingNDP, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_TBSIG1_Disregard(scpi_t *context)
{
    Resq_11Be_DataInfo(TB_SIG1_Disregard, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_TBSIG2_Disregard(scpi_t *context)
{
    Resq_11Be_DataInfo(TB_SIG2_Disregard, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_TBValid_B2(scpi_t *context)
{
    Resq_11Be_DataInfo(TB_ValidB2, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_USIG_TBSpaReuse(scpi_t *context)
{
    Resq_11Be_DataInfo_array(TB_SpaReuse, usig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_CRC(scpi_t *context)
{
    Resq_11Be_DataInfo(CRC, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_SpatialReuse(scpi_t *context)
{
//    int iRet = WT_ERR_CODE_OK;
//    do
//    {
//        int RUID = 1;
//        int NSS = 0;
//        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
//        iRet = GetRUID_NSSID(context, RUID, NSS);
//        IF_BREAK(iRet);
//        iRet = is11BE(context);
//        IF_BREAK(iRet);
//        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
//        DataInfo11Be *Info = DataInfoBuf.get();
//        iRet = WT_GetVectorResult(attr->ConnID,
//                                  WT_RES_DATA_INFO,
//                                  Info,
//                                  sizeof(DataInfo11Be));
//        IF_BREAK(iRet);
//
//        int Len = Info->sig.SpatialReuseLen;
//        SCPI_ResultInt(context, Len);
//        for(int  i = 0; i < Len; i++)
//        {
//            SCPI_ResultInt(context, Info->sig.SpatialReuse[i]);
//        }
//    } while (0);
//
//    return SCPI_ResultOK(context, iRet);
    //回退，直接返回数组四个内容~不考虑有效与否
    for (int i = 0; i < 4; i++)
    {
        Resq_11Be_DataInfo(SpatialReuse[i], sig, false);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_GILTFSize(scpi_t *context)
{
    Resq_11Be_DataInfo(GILTFSize, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_LTFSymobl(scpi_t *context)
{
    Resq_11Be_DataInfo(LTFSymbol, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_LDPCExtra(scpi_t *context)
{
    Resq_11Be_DataInfo(LDPCExtra, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_PreFecPadFactor(scpi_t *context)
{
    Resq_11Be_DataInfo(PreFEC, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_PEDisambiguity(scpi_t *context)
{
    Resq_11Be_DataInfo(PEDisambiguity, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_Disregard(scpi_t *context)
{
    Resq_11Be_DataInfo(Disregard, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_Common9Bit(scpi_t *context)
{
    Resq_11Be_vector_DataInfo(Common9Bit, Common9BitNum, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_AddEhtLtfSym(scpi_t *context)
{
    Resq_11Be_DataInfo(EHTSIG, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_SIG_LTFType(scpi_t *context)
{
    Resq_11Be_DataInfo(EhtLtfType, sig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LSIG_CRC(scpi_t *context)
{
    Resq_11Be_DataInfo(CRC, lsig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LSIG_PSDULength(scpi_t *context)
{
    Resq_11Be_DataInfo(PSDULen, lsig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LSIG_DataRate(scpi_t *context)
{
    Resq_11Be_DataInfo(DataRate, lsig, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LSIG_ParityCheck(scpi_t *context)
{
    Resq_11Be_DataInfo(ParityCheck, lsig, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LSIG_ParityBit(scpi_t *context)
{
    Resq_11Be_DataInfo(ParityBit, lsig, false);
    return SCPI_RES_OK;
}
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
#define Resq_11Be_RU_Value(name, isDouble)                                                    \
    do                                                                                        \
    {                                                                                         \
        int iRet = WT_ERR_CODE_OK;                                                            \
        int RUID = 1;                                                                         \
        int NSS = 0;                                                                          \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);            \
        iRet = GetRUID_NSSID(context, RUID, NSS);                                             \
        IF_ERR_RETURN(iRet);                                                                  \
        iRet = is11BE(context);                                                               \
        IF_ERR_RETURN(iRet);                                                                  \
        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);                            \
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();                                                  \
        iRet = WT_GetVectorResult(attr->ConnID,                                               \
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,                             \
                                  RU_MUMIMO,                                                  \
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));                            \
        IF_ERR_RETURN(iRet);                                                                  \
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);                               \
        DataInfo11Be *Info = DataInfoBuf.get();                                               \
        iRet = WT_GetVectorResult(attr->ConnID,                                               \
                                  WT_RES_DATA_INFO,                                           \
                                  Info,                                                       \
                                  sizeof(DataInfo11Be));                                      \
        IF_ERR_RETURN(iRet);                                                                  \
        iRet = (RUID > Info->common.RUCnt ? WT_ERR_CODE_PARAMETER_MISMATCH : WT_ERR_CODE_OK); \
        IF_ERR_RETURN(iRet);                                                                  \
        if (false == isDouble)                                                                \
        {                                                                                     \
            SCPI_ResultInt(context, (int)RU_MUMIMO[RUID - 1].name);                           \
        }                                                                                     \
        else                                                                                  \
        {                                                                                     \
            SCPI_ResultDouble(context, (double)RU_MUMIMO[RUID - 1].name);                     \
        }                                                                                     \
    } while (0)

#define Resq_11Be_RU_UserValue(name, isDouble)                                                \
    do                                                                                        \
    {                                                                                         \
        int iRet = WT_ERR_CODE_OK;                                                            \
        int RUID = 1;                                                                         \
        int NSS = 0;                                                                          \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);            \
        iRet = GetRUID_NSSID(context, RUID, NSS);                                             \
        IF_ERR_RETURN(iRet);                                                                  \
        iRet = is11BE(context);                                                               \
        IF_ERR_RETURN(iRet);                                                                  \
        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);                            \
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();                                                  \
        iRet = WT_GetVectorResult(attr->ConnID,                                               \
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,                             \
                                  RU_MUMIMO,                                                  \
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));                            \
        IF_ERR_RETURN(iRet);                                                                  \
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);                               \
        DataInfo11Be *Info = DataInfoBuf.get();                                               \
        iRet = WT_GetVectorResult(attr->ConnID,                                               \
                                  WT_RES_DATA_INFO,                                           \
                                  Info,                                                       \
                                  sizeof(DataInfo11Be));                                      \
        IF_ERR_RETURN(iRet);                                                                  \
        iRet = (RUID > Info->common.RUCnt ? WT_ERR_CODE_PARAMETER_MISMATCH : WT_ERR_CODE_OK); \
        IF_ERR_RETURN(iRet);                                                                  \
        RUID -= 1;                                                                            \
        if (false == isDouble)                                                                \
        {                                                                                     \
            SCPI_ResultInt(context, (int)RU_MUMIMO[RUID].User[0].name);                       \
        }                                                                                     \
        else                                                                                  \
        {                                                                                     \
            SCPI_ResultDouble(context, (double)RU_MUMIMO[RUID].User[0].name);                 \
        }                                                                                     \
    } while (0)

#define Resq_11Be_RU_UserNSSValue(composite_name, nss_name, isDouble)                         \
    do                                                                                        \
    {                                                                                         \
        int iRet = WT_ERR_CODE_OK;                                                            \
        int RUID = 1;                                                                         \
        int NSS = 0;                                                                          \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);            \
        iRet = GetRUID_NSSID(context, RUID, NSS);                                             \
        IF_ERR_RETURN(iRet);                                                                  \
        iRet = is11BE(context);                                                               \
        IF_ERR_RETURN(iRet);                                                                  \
        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);                            \
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();                                                  \
        iRet = WT_GetVectorResult(attr->ConnID,                                               \
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,                             \
                                  RU_MUMIMO,                                                  \
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));                            \
        IF_ERR_RETURN(iRet);                                                                  \
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);                               \
        DataInfo11Be *Info = DataInfoBuf.get();                                               \
        iRet = WT_GetVectorResult(attr->ConnID,                                               \
                                  WT_RES_DATA_INFO,                                           \
                                  Info,                                                       \
                                  sizeof(DataInfo11Be));                                      \
        IF_ERR_RETURN(iRet);                                                                  \
        iRet = (RUID > Info->common.RUCnt ? WT_ERR_CODE_PARAMETER_MISMATCH : WT_ERR_CODE_OK); \
        IF_ERR_RETURN(iRet);                                                                  \
        RUID -= 1;                                                                            \
        if (0 == NSS)                                                                         \
        {                                                                                     \
            if (isDouble)                                                                     \
            {                                                                                 \
                SCPI_ResultDouble(                                                            \
                    context,                                                                  \
                    (double)RU_MUMIMO[RUID].User[0].composite_name);                          \
            }                                                                                 \
            else                                                                              \
            {                                                                                 \
                SCPI_ResultInt(                                                               \
                    context,                                                                  \
                    (int)RU_MUMIMO[RUID].User[0].composite_name);                             \
            }                                                                                 \
        }                                                                                     \
        else                                                                                  \
        {                                                                                     \
            if (0 == RU_MUMIMO[RUID].User[0].NstsFlag[NSS - 1])                               \
            {                                                                                 \
                ResqInvalidValue(context, isDouble);                                          \
            }                                                                                 \
            else                                                                              \
            {                                                                                 \
                if (isDouble)                                                                 \
                {                                                                             \
                    SCPI_ResultDouble(                                                        \
                        context,                                                              \
                        (double)RU_MUMIMO[RUID].User[0].nss_name[NSS - 1]);                   \
                }                                                                             \
                else                                                                          \
                {                                                                             \
                    SCPI_ResultInt(                                                           \
                        context,                                                              \
                        (int)RU_MUMIMO[RUID].User[0].nss_name[NSS - 1]);                      \
                }                                                                             \
            }                                                                                 \
        }                                                                                     \
    } while (0)

scpi_result_t GetVsaRst_11Be_UserValid(scpi_t *context)
{
    Resq_11Be_RU_Value(ValidFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_ULDL(scpi_t *context)
{
    return GetVsaRst_11Be_USIG_ULDL(context);
}

scpi_result_t GetVsaRst_11Be_ToneWide(scpi_t *context)
{
    Resq_11Be_RU_Value(ToneWide, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_ToneIndex(scpi_t *context)
{
    Resq_11Be_RU_Value(ToneIndex, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_NSP(scpi_t *context)
{
    Resq_11Be_RU_Value(NSP, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_NSD(scpi_t *context)
{
    Resq_11Be_RU_Value(NSD, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MCS(scpi_t *context)
{
    Resq_11Be_RU_UserValue(MCS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_Modulation(scpi_t *context)
{
    Resq_11Be_RU_UserValue(Modulation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_CodingType(scpi_t *context)
{
    Resq_11Be_RU_UserValue(CodingType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_CodingRate(scpi_t *context)
{
    Resq_11Be_RU_UserValue(CodingRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_STBC(scpi_t *context)
{
    //Resq_11Be_RU_UserValue(STBC, false);
    (void)context;
    return SCPI_RES_ERR;
}

scpi_result_t GetVsaRst_11Be_DCM(scpi_t *context)
{
    Resq_11Be_RU_UserValue(DCM, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_NSTS(scpi_t *context)
{
    Resq_11Be_RU_UserValue(NSTS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_NSS(scpi_t *context)
{
    Resq_11Be_RU_UserValue(NSS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_PSDUCRC(scpi_t *context)
{
    Resq_11Be_RU_UserValue(PSDUCRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_PSDULen(scpi_t *context)
{
    Resq_11Be_RU_UserValue(PSDULength, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_AID(scpi_t *context)
{
    Resq_11Be_RU_UserValue(AID, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_Beamformed(scpi_t *context)
{
    Resq_11Be_RU_UserValue(Beamformed, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_PowerFactor(scpi_t *context)
{
    (void)context;
    return SCPI_RES_ERR;
}

scpi_result_t GetVsaRst_11Be_DataRate(scpi_t *context)
{
    Resq_11Be_RU_UserValue(Rate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_Power(scpi_t *context)
{
    Resq_11Be_RU_UserNSSValue(Power, PowerNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_EVMAll(scpi_t *context)
{
    Resq_11Be_RU_UserNSSValue(AllEvm, AllEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_EVMData(scpi_t *context)
{
    Resq_11Be_RU_UserNSSValue(DataEvm, DataEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_EVMPilot(scpi_t *context)
{
    Resq_11Be_RU_UserNSSValue(PilotEvm, PilotEvmNsts, true);
    return SCPI_RES_OK;
}

static int IsMUMIMO(scpi_t *context, bool &MUFlag)
{
    return is11BE_MUMIMO(context, MUFlag);
}

#define ResqMUMIMOUserValue(name, isDouble)                                            \
    do                                                                                 \
    {                                                                                  \
        int RUID = 1;                                                                  \
        int UserID = 1;                                                                \
        int NSS = 1;                                                                   \
        int iRet = WT_ERR_CODE_GENERAL_ERROR;                                          \
        bool MuFlag = false;                                                           \
        iRet = GetRUID_User_NSSID(context, RUID, UserID, NSS);                         \
        IF_ERR_RETURN(iRet);                                                           \
        iRet = IsMUMIMO(context, MuFlag);                                              \
        IF_ERR_RETURN(iRet);                                                           \
        if (MuFlag)                                                                    \
        {                                                                              \
            SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context); \
            unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);                 \
            EHT_RU *RU_MUMIMO = MuMimoBuf.get();                                       \
            memset(RU_MUMIMO, 0, sizeof(EHT_RU) * MAX_BE_RU_CNT);                      \
            iRet = WT_GetVectorResult(attr->ConnID,                                    \
                                      WT_RES_11BE_MU_MIMO_OFDMA_INFO,                  \
                                      RU_MUMIMO,                                       \
                                      sizeof(EHT_RU) * MAX_BE_RU_CNT);                 \
            IF_ERR_RETURN(iRet);                                                       \
            RUID -= 1;                                                                 \
            UserID -= 1;                                                               \
            if (UserID > RU_MUMIMO[RUID].UeNum)                                        \
            {                                                                          \
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;                                 \
                IF_ERR_RETURN(iRet);                                                   \
            }                                                                          \
            if (RU_MUMIMO[RUID].ValidFlag)                                             \
            {                                                                          \
                if (false == isDouble)                                                 \
                {                                                                      \
                    SCPI_ResultInt(                                                    \
                        context,                                                       \
                        (int)RU_MUMIMO[RUID].User[UserID].name);                       \
                }                                                                      \
                else                                                                   \
                {                                                                      \
                    SCPI_ResultDouble(                                                 \
                        context,                                                       \
                        (double)RU_MUMIMO[RUID].User[UserID].name);                    \
                }                                                                      \
            }                                                                          \
            else                                                                       \
            {                                                                          \
                ResqInvalidValue(context, isDouble);                                   \
            }                                                                          \
        }                                                                              \
        else                                                                           \
        {                                                                              \
            ResqInvalidValue(context, isDouble);                                       \
        }                                                                              \
    } while (0)

#define ResqMUMIMOUserNSSValue(composite_name, nss_name, isDouble)                                    \
    do                                                                                                \
    {                                                                                                 \
        int RUID = 1;                                                                                 \
        int UserID = 1;                                                                               \
        int NSS = 0;                                                                                  \
        int Tmp_NSS_ID = 0;                                                                           \
        int iRet = WT_ERR_CODE_GENERAL_ERROR;                                                         \
        bool MuFlag = false;                                                                          \
        iRet = GetRUID_User_NSSID(context, RUID, UserID, NSS);                                        \
        WTLog::Instance().WriteLog(LOG_DEBUG, "RUID=%d,UserID=%d,NSS=%d\n", RUID, UserID, NSS);                                      \
        IF_ERR_RETURN(iRet);                                                                          \
        iRet = IsMUMIMO(context, MuFlag);                                                             \
        IF_ERR_RETURN(iRet);                                                                          \
        if (MuFlag)                                                                                   \
        {                                                                                             \
            SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);                \
            unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);                                \
            EHT_RU *RU_MUMIMO = MuMimoBuf.get();                                                      \
            memset(RU_MUMIMO, 0, sizeof(EHT_RU) * MAX_BE_RU_CNT);                                     \
            iRet = WT_GetVectorResult(attr->ConnID,                                                   \
                                      WT_RES_11BE_MU_MIMO_OFDMA_INFO,                                 \
                                      RU_MUMIMO,                                                      \
                                      sizeof(EHT_RU) * MAX_BE_RU_CNT);                                \
            IF_ERR_RETURN(iRet);                                                                      \
            RUID -= 1;                                                                                \
            UserID -= 1;                                                                              \
            if (UserID > RU_MUMIMO[RUID].UeNum)                                                       \
            {                                                                                         \
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;                                                \
                IF_ERR_RETURN(iRet);                                                                  \
            }                                                                                         \
            if (RU_MUMIMO[RUID].ValidFlag)                                                            \
            {                                                                                         \
                if (false == isDouble)                                                                \
                {                                                                                     \
                    if (0 == NSS)                                                                     \
                    {                                                                                 \
                        SCPI_ResultInt(                                                               \
                            context,                                                                  \
                            (int)RU_MUMIMO[RUID].User[UserID].composite_name);                        \
                    }                                                                                 \
                    else                                                                              \
                    {                                                                                 \
                        if (GetNSSStartID_11BeMUMIMO(RU_MUMIMO[RUID].User[UserID], Tmp_NSS_ID))       \
                        {                                                                             \
                            SCPI_ResultInt(                                                           \
                                context,                                                              \
                                (int)RU_MUMIMO[RUID].User[UserID].nss_name[Tmp_NSS_ID + NSS - 1]);    \
                        }                                                                             \
                        else                                                                          \
                        {                                                                             \
                            ResqInvalidValue(context, isDouble);                                      \
                        }                                                                             \
                    }                                                                                 \
                }                                                                                     \
                else                                                                                  \
                {                                                                                     \
                    if (0 == NSS)                                                                     \
                    {                                                                                 \
                        SCPI_ResultDouble(                                                            \
                            context,                                                                  \
                            (double)RU_MUMIMO[RUID].User[UserID].composite_name);                     \
                    }                                                                                 \
                    else                                                                              \
                    {                                                                                 \
                        if (GetNSSStartID_11BeMUMIMO(RU_MUMIMO[RUID].User[UserID], Tmp_NSS_ID))       \
                        {                                                                             \
                            SCPI_ResultDouble(                                                        \
                                context,                                                              \
                                (double)RU_MUMIMO[RUID].User[UserID].nss_name[Tmp_NSS_ID + NSS - 1]); \
                        }                                                                             \
                        else                                                                          \
                        {                                                                             \
                            ResqInvalidValue(context, isDouble);                                      \
                        }                                                                             \
                    }                                                                                 \
                }                                                                                     \
            }                                                                                         \
            else                                                                                      \
            {                                                                                         \
                ResqInvalidValue(context, isDouble);                                                  \
            }                                                                                         \
        }                                                                                             \
        else                                                                                          \
        {                                                                                             \
            ResqInvalidValue(context, isDouble);                                                      \
        }                                                                                             \
    } while (0)

scpi_result_t GetVsaRst_11Be_MUMIMO(scpi_t *context)
{
    bool isMUMIMO = false;
    int iRet = IsMUMIMO(context, isMUMIMO);
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, (isMUMIMO == true ? 1 : 0));
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_RUValid(scpi_t *context)
{
    Resq_11Be_RU_Value(ValidFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_RU_NSTS(scpi_t *context)
{
    Resq_11Be_RU_Value(RUNsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_ULDL(scpi_t *context)
{
    return GetVsaRst_11Be_USIG_ULDL(context);
}

scpi_result_t GetVsaRst_11Be_MUMIMO_MCS(scpi_t *context)
{
    ResqMUMIMOUserValue(MCS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_ToneWide(scpi_t *context)
{
    Resq_11Be_RU_Value(ToneWide, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_Beamformed(scpi_t *context)
{
    ResqMUMIMOUserValue(Beamformed, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_ToneIndex(scpi_t *context)
{
    Resq_11Be_RU_Value(ToneIndex, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_NSP(scpi_t *context)
{
    Resq_11Be_RU_Value(NSP, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_NSD(scpi_t *context)
{
    Resq_11Be_RU_Value(NSD, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_Modulation(scpi_t *context)
{
    ResqMUMIMOUserValue(Modulation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_CodingType(scpi_t *context)
{
    ResqMUMIMOUserValue(CodingType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_CodingRate(scpi_t *context)
{
    ResqMUMIMOUserValue(CodingRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_STBC(scpi_t *context)
{
    //ResqMUMIMOUserValue(STBC, false);
    (void)context;
    return SCPI_RES_ERR;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_DCM(scpi_t *context)
{
    ResqMUMIMOUserValue(DCM, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_NSTS(scpi_t *context)
{
    ResqMUMIMOUserValue(NSTS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_NSS(scpi_t *context)
{
    ResqMUMIMOUserValue(NSS, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_PSDUCRC(scpi_t *context)
{
    ResqMUMIMOUserValue(PSDUCRC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_PSDULen(scpi_t *context)
{
    ResqMUMIMOUserValue(PSDULength, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_AID(scpi_t *context)
{
    ResqMUMIMOUserValue(AID, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_PowerFactor(scpi_t *context)
{
    //ResqMUMIMOUserValue(PowerFactor, true);
    (void)context;
    return SCPI_RES_ERR;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_DataRate(scpi_t *context)
{
    ResqMUMIMOUserValue(Rate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_Power(scpi_t *context)
{
    ResqMUMIMOUserNSSValue(Power, PowerNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_EVMAll(scpi_t *context)
{
    ResqMUMIMOUserNSSValue(AllEvm, AllEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_EVMData(scpi_t *context)
{
    ResqMUMIMOUserNSSValue(DataEvm, DataEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_EVMPilot(scpi_t *context)
{
    ResqMUMIMOUserNSSValue(PilotEvm, PilotEvmNsts, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_RUInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 1;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        SCPI_ParamInt(context, &RUID, false);

        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
        DataInfo11Be *Info = DataInfoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_DATA_INFO,
                                  Info,
                                  sizeof(DataInfo11Be));
        IF_ERR_RETURN(iRet);
        ILLEGAL_PARAM_RETURN(RUID < 0 || RUID > Info->common.RUCnt);

        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,
                                  RU_MUMIMO,
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));
        IF_ERR_RETURN(iRet);

        unique_ptr<QMatrixInformation[]> QMatBuf(new QMatrixInformation[MAX_BE_RU_CNT]);
        QMatrixInformation *QMat = QMatBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_RU_QMAT,
                                  QMat,
                                  MAX_BE_RU_CNT * sizeof(QMatrixInformation));
        IF_ERR_RETURN(iRet);
        if (true)
        {
            RUID -= 1;
            SCPI_ResultInt(context, RU_MUMIMO[RUID].ValidFlag);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].UeNum);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].RUNsts);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].ToneWide);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].ToneIndex);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].NSP);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].NSD);

            for (int i = 0; i < RU_MUMIMO[RUID].RUNsts; i++)
            {
                for (int j = 0; j < RU_MUMIMO[RUID].RUNsts; j++)
                {
                    SCPI_ResultDouble(context, QMat[RUID].QMatrix[i][j][0]);
                    SCPI_ResultDouble(context, QMat[RUID].QMatrix[i][j][1]);
                }
            }
        }
    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_RUCntInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
        DataInfo11Be *Info = DataInfoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_DATA_INFO,
                                  Info,
                                  sizeof(DataInfo11Be));
        IF_ERR_RETURN(iRet);

        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,
                                  RU_MUMIMO,
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));
        IF_ERR_RETURN(iRet);

        SCPI_ResultInt(context, Info->common.RUCnt);
        for (int i = 0; i < Info->common.RUCnt; i++)
        {
            SCPI_ResultInt(context, RU_MUMIMO[i].ValidFlag);
        }

    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_RU_UserInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 1;
    int UserID = 1;
    int StreamID = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        SCPI_ParamInt(context, &RUID, false);
        SCPI_ParamInt(context, &UserID, false);
        SCPI_ParamInt(context, &StreamID, false);
        WTLog::Instance().WriteLog(LOG_DEBUG, "RUID = %d, UserID = %d, StreamID = %d\n", RUID, UserID, StreamID);

        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
        DataInfo11Be *Info = DataInfoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_DATA_INFO,
                                  Info,
                                  sizeof(DataInfo11Be));
        IF_ERR_RETURN(iRet);

        ILLEGAL_PARAM_RETURN(RUID < 1 || RUID > Info->common.RUCnt);
        ILLEGAL_PARAM_RETURN(StreamID < 0 || StreamID > MAX_DF_NUM);

        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,
                                  RU_MUMIMO,
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));
        IF_ERR_RETURN(iRet);

        unique_ptr<QMatrixInformation[]> QMatBuf(new QMatrixInformation[MAX_BE_RU_CNT]);
        QMatrixInformation *QMat = QMatBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_RU_QMAT,
                                  QMat,
                                  MAX_BE_RU_CNT * sizeof(QMatrixInformation));
        IF_ERR_RETURN(iRet);

        ILLEGAL_PARAM_RETURN(UserID < 1 || UserID > MUMIMO_8_USER);

        RUID -= 1;
        UserID -= 1;
        SCPI_ResultInt(context, RU_MUMIMO[RUID].ValidFlag);
        SCPI_ResultInt(context, RU_MUMIMO[RUID].UeNum);

        {
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].UserID);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].AID);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ToneWide);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ToneIdx);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].MCS);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].NSS);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].Beamformed);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].CodingType);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].DCM);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].SpatialConfig);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].CodingRate);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].Modulation);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].PSDUCRC);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].PSDULength);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ampduValid);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ampduNum);
            SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].Rate);

            if (0 == StreamID)
            {
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].Power);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].PilotEvm);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].DataEvm);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].AllEvm);
            }
            else
            {
                int index = StreamID - 1;
                int tmpID = 0;
                if (GetNSSStartID_11BeMUMIMO(RU_MUMIMO[RUID].User[UserID], tmpID))
                {
                    index += tmpID;
                }
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].PowerNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].PilotEvmNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].DataEvmNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].AllEvmNsts[index]);
            }
        }

    } while (0);

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_MUMIMO_RU_UserInfo_V2(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int32_t numbers[3] = {0, 0, 0};
    int RUID = 1;
    int UserID = 1;
    int StreamID = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        RUID = numbers[0];
        UserID = numbers[1];
        StreamID = numbers[2];

        WTLog::Instance().WriteLog(LOG_DEBUG, "RUID = %d, UserID = %d, StreamID = %d\n", RUID, UserID, StreamID);

        unique_ptr<DataInfo11Be> DataInfoBuf(new DataInfo11Be);
        DataInfo11Be *Info = DataInfoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_DATA_INFO,
                                  Info,
                                  sizeof(DataInfo11Be));
        IF_ERR_RETURN(iRet);

        ILLEGAL_PARAM_RETURN(RUID < 1 || RUID > Info->common.RUCnt);
        ILLEGAL_PARAM_RETURN(StreamID < 0 || StreamID > MAX_DF_NUM);

        unique_ptr<EHT_RU[]> MuMimoBuf(new EHT_RU[MAX_BE_RU_CNT]);
        EHT_RU *RU_MUMIMO = MuMimoBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_MU_MIMO_OFDMA_INFO,
                                  RU_MUMIMO,
                                  MAX_BE_RU_CNT * sizeof(EHT_RU));
        IF_ERR_RETURN(iRet);

        unique_ptr<QMatrixInformation[]> QMatBuf(new QMatrixInformation[MAX_BE_RU_CNT]);
        QMatrixInformation *QMat = QMatBuf.get();
        iRet = WT_GetVectorResult(attr->ConnID,
                                  WT_RES_11BE_RU_QMAT,
                                  QMat,
                                  MAX_BE_RU_CNT * sizeof(QMatrixInformation));
        IF_ERR_RETURN(iRet);

        ILLEGAL_PARAM_RETURN(UserID < 1 || UserID > MUMIMO_8_USER);

        RUID -= 1;
        UserID -= 1;
        SCPI_ResultInt(context, RU_MUMIMO[RUID].ValidFlag);
        SCPI_ResultInt(context, RU_MUMIMO[RUID].UeNum);

        {
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].UserID);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].AID);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ToneWide);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ToneIdx);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].MCS);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].NSS);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].Beamformed);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].CodingType);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].DCM);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].SpatialConfig);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].CodingRate);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].Modulation);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].PSDUCRC);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].PSDULength);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ampduValid);
            SCPI_ResultInt(context, RU_MUMIMO[RUID].User[UserID].ampduNum);
            SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].Rate);

            if (0 == StreamID)
            {
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].Power);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].PilotEvm);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].DataEvm);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].AllEvm);
            }
            else
            {
                int index = StreamID - 1;
                int tmpID = 0;
                if (GetNSSStartID_11BeMUMIMO(RU_MUMIMO[RUID].User[UserID], tmpID))
                {
                    index += tmpID;
                }
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].PowerNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].PilotEvmNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].DataEvmNsts[index]);
                SCPI_ResultDouble(context, RU_MUMIMO[RUID].User[UserID].AllEvmNsts[index]);
            }
        }

    } while (0);

    return SCPI_RES_OK;
}
