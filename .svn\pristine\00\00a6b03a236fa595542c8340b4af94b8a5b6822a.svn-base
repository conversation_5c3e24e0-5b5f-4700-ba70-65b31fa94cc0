#include <string>
#include <iostream>

#include "scpi_3gpp_gen_wcdma.h"
#include "commonhandler.h"
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include "TesterWave.h"
#include "math.h"

static int IsWCDMA(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (attr->Pn3GPP->CommonParam.standard != ALG_3GPP_STD_WCDMA)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t SCPI_WCDMA_SetLinkDirect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = ALG_3GPP_UL;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_UL && Value != ALG_3GPP_DL)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->WCDMA.LinkDirect = Value;

        iRet = WT_GetDefaultWaveParameter3GPP(attr->ConnID, ALG_3GPP_STD_WCDMA, Value, attr->Pn3GPP.get(), sizeof(Alg_3GPP_WaveGenType));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenWaveMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.Mode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenScramblingMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.ScramblingMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenScramblingCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 16777215)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.ScramblingCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHSlotFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPCCH.SlotFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHTfci(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1023)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPCCH.Tfci = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHTpcDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 4 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPCCH.TpcDataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_UL_SetGenDPCCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPCCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchOverallSymbolRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 30000 && Value != 60000 && Value != 120000 && Value != 240000 && Value != 480000 && Value != 960000 &&
            Value != 2 * 960000 && Value != 3 * 960000 && Value != 4 * 960000 && Value != 5 * 960000 && Value != 6 * 960000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingInterleaverState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.Interleaver2Stat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7fffff)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportTimeInterval(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 10 && Value != 20 && Value != 40)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].TTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportBlocks(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].TbCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchTransportBlockSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4096)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].TbSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchSizeOfCRC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 8 && Value != 12 && Value != 16 && Value != 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].Crc = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchRateMatchingAttribute(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1024)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].RmAttribute = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchErrorProtection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].EProtection = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDtchInterleaverState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int DtchId = 0;

        SCPI_CommandNumbers(context, &DtchId, 1);
        if (DtchId < 0 || DtchId >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DTCH[DtchId].InterleaverStat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7fffff)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportTimeInterval(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 10 && Value != 20 && Value != 40)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.TTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportBlocks(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.TbCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchTransportBlockSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4096)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.TbSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchSizeOfCRC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 8 && Value != 12 && Value != 16 && Value != 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.Crc = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchRateMatchingAttribute(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1024)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.RmAttribute = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchErrorProtection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.EProtection = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelCodingDcchInterleaverState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DCH.DCCH.InterleaverStat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchPhyChannelConfigDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Id = 0;

        SCPI_CommandNumbers(context, &Id, 1);
        if (Id < 0 || Id >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.DataType[Id] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchPhyChannelConfigInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Id = 0;

        SCPI_CommandNumbers(context, &Id, 1);
        if (Id < 0 || Id >= 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.Initialization[Id] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULDpdchChannelPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0.0;

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        /*if (Basefun::CompareDouble(Value, -80.0) < 0 || Basefun::CompareDouble(Value, 0.0) > 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }*/

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.DPDCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULHsdpcchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.HSDPCCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULHsdpcchStartDelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 250)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.HSDPCCH.StartDelay = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULHsdpcchInterTTIDist(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 16)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.HSDPCCH.InterTTIDist = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULHsdpcchACKPattern(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Value = 0;
        int Id = 0;

        SCPI_CommandNumbers(context, &Id, 1);
        if (Id < 0 || Id >= 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.HSDPCCH.ACKPattern[Id] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULHsdpcchCQIPattern(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        int Id = 0;

        SCPI_CommandNumbers(context, &Id, 1);
        if (Id < 0 || Id >= 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -1 || Value > 30)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.HSDPCCH.CQIPattern[Id] = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULHsdpcchPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0.0;

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < -80.0 || Value > 10.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        // if (Basefun::CompareDouble(Value, -80.0) < 0 || Basefun::CompareDouble(Value, 0.0) > 0)
        // {
        //     iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        //     break;
        // }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.HSDPCCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULEdpcchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.EDPCCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetULEdpdchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.UL.EDPDCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLCommonScramblingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.ScramblingState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLCommonScramblingCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int MAXNum = pow(2, 13);
    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > (MAXNum - 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.ScramblingCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLCommonOCNSModeState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.OCNSModeState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLCommonOCNSMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.OCNSMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCPICHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCPICH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCPICHSymbRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCPICH.SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCPICHChanCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCPICH.ChanCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCPICHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCPICH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPSCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PSCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPSCHSymbRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PSCH.SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPSCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PSCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCCPCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCCPCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCCPCHSymbRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCCPCH.SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLPCCPCHChanCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCCPCH.ChanCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetPCCPCHDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCCPCH.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetPCCPCHInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCCPCH.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetPCCPCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.PCCPCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_SetDLDPCHNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 32)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCHNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHSlotFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 16)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].SlotFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHSymbolRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 7500 && Value != 15000 && Value != 30000 && Value != 60000 &&
            Value != 120000 && Value != 240000 && Value != 480000 && Value != 960000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value >= 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].ChanCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingInterleaver2State(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.Interleaver2Stat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].DataType = Value;
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransTimeInterval(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 10 && Value != 20 && Value != 40)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].TTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransBlockNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].TbCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHTransBlockSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4096)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].TbSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHCrcSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 8 && Value != 12 && Value != 16 && Value != 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].Crc = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHRateMatchAttribute(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1024)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].RmAttribute = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHErrorProtect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].EProtection = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDTCHInterleaverState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].InterleaverStat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransTimeInterval(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 10 && Value != 20 && Value != 40)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.TTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransBlockNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.TbCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHTransBlockSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4096)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.TbSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHCrcSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 8 && Value != 12 && Value != 16 && Value != 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.Crc = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHRateMatchAttribute(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1024)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.RmAttribute = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHErrorProtect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.EProtection = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHChanCodingDCCHInterleaverState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DCH.DCCH.InterleaverStat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHInit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHTpcDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 4 || Value > 5)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].TpcDataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLDPCHTimingOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 149)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.DPCH[Idx].TimingOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULGeneralFilterType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.General.Filter.Type = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetULGeneralSeqLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.General.GenFrmLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSSCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SSCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSSCHSymbolRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SSCH.SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSSCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SSCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHSlotFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 17)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.SlotFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHSymbolRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 15000 && Value != 30000 && Value != 60000 && Value != 120000 && Value != 240000 &&
            Value != 480000 && Value != 960000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHChannelCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.ChanCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        double Value = 0;

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.DataType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 0x7FFFFF)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.Initialization = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_SetDLSCCPCHTimeOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Value = 0;

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 149)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsWCDMA(attr);
        IF_BREAK(iRet);

        attr->Pn3GPP->WCDMA.DL.SCCPCH.TimingOffset = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}