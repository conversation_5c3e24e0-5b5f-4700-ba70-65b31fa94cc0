#pragma once

#include <map>
#ifndef LINUX
#ifdef WT4XXWRAPPER_EXPORTS
#define WT4XXWRAPPER_API __declspec(dllexport)
#else
#define WT4XXWRAPPER_API __declspec(dllimport)
#endif

#else
#ifdef WT4XXWRAPPER_EXPORTS
#define WT4XXWRAPPER_API __attribute ((visibility("default")))
#else
#define WT4XXWRAPPER_API
#endif
#endif

using namespace std;

class WT4XXWRAPPER_API WT4XXWrapper :
	public TesterWrapper
{
public:
	WT4XXWrapper(void);
	WT4XXWrapper(const char *fwVersion);

	virtual ~WT4XXWrapper(void);

    virtual void SetPnMem(void *pData);

	virtual int ConnectDevice(ConnectedUnit *connUnit, int unitCount, int connType);
	virtual int DisConnect();

	virtual int SwitchMode(int targetMode);

	virtual int AddMimoTester(ConnectedUnit connUnit);

	virtual int RemoveMimoTester();

	virtual int GetDefaultParameter(VsaParameter *vsaParam, VsaAvgParameter *avgParam, VsgParameter *vsgParam, VsgWaveParameter *waveParam, VsgPattern *vsgPattern);

    virtual int ClearSampRateFromFileFlag();

	virtual int SetVSA(VsaParameter *vsaParam, ExtendVsaParameter *extParam);

	virtual int SetVSAAverageParameter(VsaAvgParameter *avgParam);

	virtual int ClrVSAAvgData();

	virtual int SetVSATrigParam(VsaTrigParam *Param);

	virtual int GetVSATrigParam(VsaTrigParam* Param);

	virtual int SetVSAAutorange(VsaParameter *vsaParam, ExtendVsaParameter *extParam);

	virtual int GetVSAParameter(int demodType, VsaParameter *vsaParam, ExtendVsaParameter *extvsaParam);

	virtual int GetConnectStatus();
	virtual int DataCapture();
    virtual int DataCaptureAsync();
    virtual int GetCurrVSAStatus(int *status);
	virtual int PauseDataCapture();

	virtual int StopDataCapture();

	virtual int SetGeneralAnalyzeParam(AlzParamComm *commonAnalyzeParam);

	virtual int Analyze(int demodType, AnalyzeParam *analyzeParams, int paramsSize, const char *refFileName, int frameID, unsigned int timeoutMs);
    virtual int SetAlzParam(int analyzeParamType, AnalyzeParam *analyzeParams, int paramsSize);
	virtual int SetExternAnalyzeParam(int demod, int ParamType, void *param, int paramSize);

	virtual int CalculateImbalance(double *imbAmp, double *imbPhase, double *timeSkew, int segmentID);
	virtual int SetFixedImbalance(int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID);
    virtual int SetVsgFixedImbalance(int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID);

	virtual int GetResult(const char *anaParamString, double *result, int signalID, int segmentID);

	virtual int GetBaseResult(VsaBaseResult *result, int signalID, int segmentID);

	virtual int GetVectorResultElementSize(const char *anaParamString, unsigned int *elementSize, int signalID, int segmentID, int SegNo = -1);

	virtual int GetVectorResultElementCount(const char *anaParamString, unsigned int *elementCount, int signalID, int segmentID, int SegNo = -1);

	virtual int GetVectorResult(const char *anaParamString, void *result, unsigned int resultSize, int signalID, int segmentID, int SegNo = -1);

	virtual int GetAverageResult(VsaAverageResult *result, VsaAverageResult *max, VsaAverageResult *min, int times, int signalID, int segmentID);
	virtual int GetSLEAverageResult(VsaSLECommResult* result, VsaSLECommResult* max, VsaSLECommResult* min, int times, int signalID, int segmentID);
	virtual int Get3GPPAverageResult(Vsa3GPPCommResult *result, Vsa3GPPCommResult *max, Vsa3GPPCommResult *min, int times, int signalID, int segmentID);
	virtual int GetBTAverageResult(VsaBTCommResult* result, VsaBTCommResult* max, VsaBTCommResult* min, int times, int signalID, int segmentID);
	virtual int GetAvgBaseCompositeResult(VsaBaseResult *result, int segmentID);
	virtual int GetCurrAverageCount(int *count);
	virtual int SetAnalyzeGroupParam(char *anaParamString);
	virtual int SetVSGParameter(VsgParameter *vsgParam, ExtendVsgParameter *extParam);

	virtual int SetVSGWaveParameter(VsgWaveParameter *vsgWaveParam);
    virtual int GetVSGParam(VsgParameter *vsgParam, VsgWaveParameter *vsgWaveParam, VsgPattern *vsgPattern, ExtendVsgParameter* extParam);
	virtual int SetVSGPattern(VsgPattern *vsgPatternParam, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount);
	virtual int GetVSGPattern(VsgPattern *vsgPattern, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount, unsigned int *actualPnItemCount, unsigned int *actualPnHeadCount);
	virtual int AnalyzeVSGData(const char *waveName, int demodType, AnalyzeParam *analyzeParams, int paramsSize, int timeOutMs);
	virtual int GetVSGDataResultElementSize(const char *anaParamString, unsigned int *elementSize, int signalID, int segmentID);
	virtual int GetVSGDataResultElementCount(const char *anaParamString, unsigned int *elementCount, int signalID, int segmentID);
	virtual int GetVSGDataVectorResult(const char *anaParamString, void *result, unsigned int resultSize, int signalID, int segmentID);
    virtual int GetVSGSendPacketCnt(int *Cnt);
	virtual int StartVSG();
	virtual int GetWaveGenCBFReportField(CBFReport *ReportField);

	virtual int AsynStartVSG();

	virtual int GetCurrVSGStatu(int *statu);

	virtual int PauseVSG();

	virtual int StopVSG();

	virtual int DeleteTesterWaveFileOrRefFile(const char *fileName);
	virtual int AddTesterWaveFileOrRefFile(const char *fileName, const char *saveFileName, int acWave2);
	virtual int GetTesterAllWaveFileOrRefFileNames(const char *path, char *fileNameBuffer, int fileNameBuffSize, unsigned int *fileCount);
	virtual int QueryTesterWaveFileOrRefFile(const char *fileName, int *waveExists);
	virtual int GetTesterWaveFileOrRefFileSize(const char *fileName, unsigned int *fileSize);
	virtual int GetTesterWaveFileOrRefFile(const char *fileName, char *fileBuff, unsigned int fileBuffSize);

	virtual int BeamformingCalibrationChannelEstDutTX(int demod);
	virtual int BeamformingCalibrationChannelEstDutRX(double *dutChannelEst, int dutChannelEstLength);
	virtual int BeamformingCalibrationResult(double *resultAngle, int *resultAngleLength);
	virtual int BeamformingVerification(double *diffPower);
	virtual int BeamformingCalculateChannelProfile(int demod, double *resultBuf, int *resultLength);
    virtual int BeamformingCalculateChannelAmplitudeandAngleBCM(int *ValidSpatialStreams, int *DataLen, double *resultBuf, int *resultLength);

	virtual int SetExternalGain(double extGain);

	virtual int GetExternalGain(CableVerifyParameter *param, double *extGain);

	virtual int CablelossCal(CalTestingInAgrs *calParameter, void (__stdcall *pfunCallBack)(CalTestingCallbackArgs callbackArgs));

	virtual int GetFileSampleFreq(double *SampleFreq);

	virtual int SetCmimoRefFile(const char *fileName);

	virtual int StartPerTesting(PerMacParameter *MacParameter, PerActionParameter *perParameter, void(*pfunCallback)(PerResultParameter progress));
	virtual int StopPerTesting();
	//METER
	virtual int SetMoniObj(int obj);
	virtual int MoniConfig(int action, int dataType, const char *anaParamString);
	virtual int GetMoniResultSize(unsigned int *dataSize);
	virtual int GetMoniResult(char *resultBuff, unsigned int resultBuffSize, int *dataType);
	virtual int QueryMoniVSAParam(char *resultBuff, unsigned int resultBuffSize, VsaParameter *vsaParam);
	virtual int QueryMoniVSGParam(char *resultBuff, unsigned int resultBuffSize, VsgParameter *vsgParam);
	virtual int QueryMoniVSAResult(char *resultBuff, unsigned int resultBuffSize, const char *anaParamString,  int signalID, int segmentID, char **dataPtr, unsigned int *elementCount, unsigned int *elementSize);
	virtual int QueryMoniVSAAnalyzeParam(char *resultBuff, unsigned int resultBuffSize, AlzParamComm *commonAnalyzeParam, void *analyzeParam, unsigned int paramSize, int *signalType);
	virtual int QueryMoniVSGPattern(char *resultBuff, unsigned int resultBuffSize, VsgPattern *vsgPattern, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount, unsigned int *actualPnItemCount, unsigned int *actualPnHeadCount);
	virtual int SetMeterConfiguration(char *config, unsigned int size);
	virtual int QueryMeterConfiguration(char *resultBuff, unsigned int resultBuffSize, char *ConfigParam);
	virtual int StartRecord(bool isLocalSave);
	virtual int FinishRecord();
	virtual int GetTesterAllRecordNames(char *recordNameBuffer, int recordNameBufferSize, int *recordCount);
	virtual int ReadRecord(char *recordName, char *Data);
	virtual int GetGUIVersion(GUIVersion *guiVersion, unsigned int maxGuiVersionCnt, unsigned int *actualGuiVersionCnt);
	virtual int GetGUIFileVersion(const char *techName, GUIVersion *version);
	virtual int GetGUIFile(const char *techName, const char *saveDir);
	virtual int GetMonitorInfos(MonitorInfo *monitorInfo, unsigned int maxMonInfoCount, unsigned int *actualMonInfoCount);
	virtual int GetPnDescription(char *fileName, char *description, unsigned int size);

	virtual int LoadSignalAsCapture(const char *fileName, const char *saveFileName, int acWave2);

	virtual int SaveSignal(const char *fileName, int saveOption, char *signalBuff, unsigned int signalBuffSize);
	virtual int SaveCurrIQDataToFile(int saveType, const char *fileName, char *calParam, unsigned int calParamSize, int signalID);
	virtual int SaveOriginalIQDataToFile(const char *fileName, char *calParam, unsigned int calParamSize, int signalID);
	virtual int TruncateSaveSignal(int type, const char *fileName, int startUs, int endUs);

    virtual int MoniSaveCurrIQDataToFile(int saveType, const char *fileName, char *calParam, unsigned int calParamSize, int signalID);
    virtual int MoniTruncateSaveSignal(int type, const char *fileName, int startUs, int endUs);
	virtual int GetTesterErrorCode(int *errCode, char *errMsg, unsigned int errMsgSize);
	virtual char *GetErrString(int err);

	virtual int GetRefLvlRange(double freq, int option, int *upperLimit, int *lowerLimit);
	virtual int GetTxPowerRange(double freq, int option, int *upperLimit, int *lowerLimit);
	virtual int GetCurrSubTesterCfg(SubTesterCfg *testerCfg);
	virtual int GetSlaveTesterSubTesterCfg(int slaveTesterID, SubTesterCfg *testerCfg);

	virtual int SetPathLossFile(const char *fileName);
	virtual int GetPathLossFileSize(unsigned int *fileSize);
	virtual int GetPathLossFile(const char *fileName, unsigned int fileSize);

	virtual int GenSensetivityWave(PerMacParameter *MacParameter, char *WaveName);
	virtual int GetSensetivityResult(int demod, int *AckCnt);

	//Admin tool

	virtual int RebootTester();

	virtual int SetSubTesterCfg(SubTesterCfg *testerCfg, int testerCount);
	virtual int GetSubTesterCfg(SubTesterCfg testerCfg[WT_SUB_TESTER_INDEX_MAX], int *subTesterCount);

	virtual int FirmwareUpdate(const char *packageFile, char *upgradePackage, unsigned int packageSize);

	virtual int LicenseUpdate(char *licensePackage, unsigned int packageSize);

	virtual int LicensePack(char *licensePackage, unsigned int packageSize, LicItemInfo_API *licInfo, unsigned int *actualSize);

	virtual int LicPackUpdate(char *Message);

	virtual int SetTesterInfo(const char *name, const char *ipAddr, const char *subMask, const char *netGate);

	virtual int FirmwareRestore(int restoreOption);

	virtual int SetNetInfo(VirtualNetType *netInfo);

	virtual int GetNetInfo(VirtualNetType *netInfo);

    virtual int GetNetLink(bool *LinkStatus);

	virtual int GetLicense(LicItemInfo_API *testerLicense, int licenseMaxCount, int *licActualcount);

	virtual int GetSlaveTesterLicense(int slaveTesterID, LicItemInfo_API *licInfo, int licenseMaxCount, int *licActualcount);

	virtual int GetTesterInfo(TesterInfo *info);

    virtual int GetTesterIpAddressType(bool *IsDhcp);

	virtual int GetSlaveTesterInfo(int slaveTesterID, TesterInfo *testerInfo);

	virtual int GetTesterSystemTime(TIME_TYPE_API *sysTime);

	virtual int FactoryReset();

	virtual int ReadLog(LogFilter *filter, char *filterLog, int logMaxBuff);
	virtual int Diagnose(DiagnoseSetting setting, char *diaInfo, unsigned int infoSize);
	virtual int AnalyzeDiagnoseLog();
	virtual int GetHardErrorCodeSize(int *errorCodeAcount);
	virtual int GetSlaveHardErrorCodeSize(int SlaveTesterID, int *errorCodeAcount);
	virtual int GetHardErrorCode(int *hardErrCode, int errorCodeAcount);
	virtual int GetSlaveHardErrorCode(int *hardErrCode, int SlaveTesterID, int errorCodeAcount);

	virtual int GetFpgaConfig(FpgaConfig *config);
	virtual int GetOSStatus(OSStatu *osStatu);

	virtual int GetProcStatus(ProcStatu *procStatu);

	virtual int TesterLogSettingInfo(int type, int *log_cfg, unsigned int size);
	//virtual int RebootTester();

	virtual int SendCalFile(char *fileBuffer, unsigned int fileBuffSize, const char *fileName);

	virtual int GetCalFile(const char *fileName, char *fileBuffer, unsigned int fileBuffSize);
	virtual int SetCalData(char *data, unsigned int dataSize);
	virtual int GetCalData(char *data, unsigned int dataSize);
	virtual int SetBBGain(int gain);
	virtual int SetTempCal(int value);
	virtual int SetFlatnessCal(int value);

	virtual int GetTemperature(DevTemperature *temp);
	virtual int GetTemperatureHistory(int *Cnt, DevTempSave *info, unsigned int infoSize);
	virtual int GetVoltage(DeviceVoltage *voltage, int voltageCnt, int *count);

	virtual int GetFanSpeed(int *speed);
	virtual int GetRxGain(int ModuleID, char *buf, unsigned int bufSize);
	virtual int GetTxGain(int ModuleID, char *buf, unsigned int bufSize);
	virtual int WriteSN(const char *password, const char *SN);
	virtual int SetATT(double *att);
	virtual int GetATT(double *att);
	virtual int SetComponentValue(ComponentLocation componentLocation, int componentValue);
	virtual int GetComponentValue(ComponentLocation componentLocation, char *componentValue, int bufSize, int *dataLen);
	virtual int InitCalData();
	virtual int SetMasterMode(int vsaMasterMode, int vsgMasterMode);

	virtual int ShutDownDevice();
	virtual int DeleteAllLicense();
	virtual int ResetSubNetConfiguration();
	virtual int SetFPGAIFG(int onff);
	virtual int GetFPGAIFG(int *onff);
	virtual void memdump(const char *filename, int id);
	virtual int WriteRemoteFile(const char *filename, char *buffer, unsigned int buffer_size);
	virtual int ReadRemoteFile(const char *filename, unsigned int *filesize, char *buffer, unsigned int buffer_size);
	virtual int ExecShellCmd(const char *cmd, char *buffer, unsigned int buffer_size);
	virtual int QueryTesterVersionInfo(char *testerInfo);
	virtual int PacStartGetData(PacParameter *param, PacAttribute *attribute, void(*pfunCallback)(PacProgressParameter *progress));
	virtual int PacCalData(PacDataAvg *buf, int BufCnt, int *retCnt);
	virtual int PacDumpResult(int mode);
	virtual int PacStop();
    virtual int PacGetModeCalData(int mode, int *Cnt, PacDataInfo **data);

    virtual int SetWifiFrameFilter(ResultFilter *filter);
    virtual int SetWideSpectrumEnable(int onff);
    virtual int AxValidCommonBit(int demod, int *commonBit, int *userInRU, int *RUCnt, int *center26RU, int *RUPos);
    virtual int BeValidCommonBit(int demod, int fullBand, int *commonBits, int *Punctured, int *userInRU, int *TotalRU, int *RUPos);
    virtual int File2TBParam(int ParamType, void *src_param, int paramSize, AlzParamAxTriggerBase *dest);
	virtual int File2SLEParam(int ParamType, void* src_param, int paramSize, AlzParamSparkLink* dest);
	virtual int FileRefParam(int ParamType, void *src_param, int paramSize, AnalyzeParam *dest);
    virtual int TB_Init();
    virtual int TB_Release();
    virtual int TB_Start(int timeout_ms);
    virtual int TB_Stop();
    virtual int TB_Status(int *stauts);
    virtual int TB_SetParam(InterBindParameter *Param, int vsaTrigger = WT_TRIG_TYPE_FREE_RUN_API);
	virtual int TB_AutoRange(InterBindParameter *Param);

    virtual int TF_Start(int timeout_ms);
    virtual int TF_Stop();
    virtual int TF_GetStatus(int *status);
    virtual int TF_SetParam(InterBindParameter *Param, int vsaTrigger = WT_TRIG_TYPE_FREE_RUN_API);

    virtual int SelfCalibration(bool isStart = true);
    virtual int QuerySelfCalibrationPercent(int *percent);
    virtual int SelfCalibrationAutoRunning(int isAutoRun);

    virtual int PacSectionParameter(PacSectionParam *param);
    virtual int SetWaveCalDataCompensate(int ON_FF);
	virtual int SetVsgIQImbCompensate(int ON_FF);
	virtual int SetVsaCalDataCompensate(int ON_FF);
	virtual int SetVsaIQImbCompensate(int ON_FF);

	virtual int GetVsgFlatnessCalCompensate(int *ON_FF);
	virtual int GetVsgIQImbCompensate(int *ON_FF);
	virtual int GetVsaFlatnessCalCompensate(int *ON_FF);
	virtual int GetVsaIQImbCompensate(int *ON_FF);
    //Wave generator
    virtual int GetDefaultWaveParameterBT(GenWaveBtStruct_API *pnParameters);
    virtual int GetDefaultWaveParameterBTV2(GenWaveBtStructV2 *pnParameters);
    virtual int GetDefaultWaveParameterCW(GenWaveCwStruct *pnParameters);
    virtual int GetDefaultWaveParameterWifi(int demod, int ppdu, GenWaveWifiStruct_API *pnParameters);
	virtual int WaveGeneratorCatenateFiles(const MutiPNCatenateInfo *catenateInfo);
    virtual int WaveGeneratorWifi(const char *fileName, GenWaveWifiStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGeneratorBlueTooth(const char *fileName, GenWaveBtStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGeneratorBlueToothV2(const char *fileName, GenWaveBtStructV2 *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGeneratorCW(const char *fileName, GenWaveCwStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
	virtual int WaveGeneratorGLE(const char *fileName, GenWaveGleStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
    virtual int WaveGenerator3GPP(const char *fileName, void *pnParameters, int Paramlen, std::shared_ptr<AlzParam3GPP>& pAlz3gpp);
	virtual int WaveGeneratorWiSun(const char* fileName, GenWaveWisunStruct* pnParameters);

	virtual int GetDefaultWaveParameterGLE(GenWaveGleStruct* pnParameters);
	virtual int GetDefaultWaveParameterWiSun(GenWaveWisunStruct* pnParameters);


    virtual int SetVSGFemParamter(FemParameter *param);
    virtual int CleanTesterCustomerWave();
    virtual int QueryTesterDiskUseInfo(TesterDiskUsage *testerInfo);
    virtual int SetSubNetAutoNeg(int Enable);
	virtual int GetSubNetAutoNeg(int *ON_FF);
    virtual int SetDigtalIQParam(DigtalIQParam *param);
    virtual int SetDigtalIQTestFixture(DigtalIQTestFixture *param);
    virtual int SetDigtalIQMode(int mode);
	virtual int AckConnectInfo(char *data, int len);
	virtual int GetConnectDetail(char *buffer, int buffersize);
    virtual int StartFastAttCal(ATTCalCfg_API *config, ATTCalResult_API *result);
    virtual int StopFastAttCal();
    virtual int SetMaxSampleRate(double maxRate);
    virtual int SetPNFileExternSettingData(void *data, int len);
	virtual int SubCmdHandle(SubCmdType* SubCmd);
    virtual void UpdateVsgParam(VsgParameter *param, ExtendVsgParameter *extParam);
    virtual int CreateLowWave(int type, const char *filename, const char *savename);
	virtual int GetPnCount(const char * filename, int &RetPnCount, int *PnOrder);
	virtual int GetConnectInfo(char* Ip, int* Port);
	virtual int TestConnectStatus();
    virtual int GetDockerAppList(char *buffer, int buflen, int *AppCnt);
    virtual int SetDockerAppEnable(char *name, int onoff);
    virtual int DelDockerApp(char *name);
    virtual int SetLOMode(int mode,int ModId);
	virtual int GetLOMode(int *mode, int ModId);
	virtual int SetIQMode(int *mode , int len);
	virtual int GetIQMode(int *mode , int *len);
	virtual int GetSpectrumPointPower(double Offset, double* Power, int signalID, int segmentID);
	virtual int SetWaveGeneratorTimeout(int Time);
	virtual int SetVsaNoiseCalibrationStart(int PortList[8]);
	virtual int SetVsaNoiseCalibrationStop();
	virtual int GetVsaNoiseCalibrationStatus(int &Status);
	virtual int GetVsaNoiseCalibrationPortValid(int Status[8][8], int &TesterCount);
	virtual int SetVsaExtendEVMStatus(int Status);
	virtual int SetBroadcastEnable(int Status);
	virtual int GetBroadcastEnable(int &Status);
	virtual int SetBroadcastDebugEnable(int Status, double Power[WT_PORT_RF8 - WT_PORT_OFF]);
	virtual int GetBroadcastRunStatus(int &Status);
	virtual int SetVsaIterativeEVMStatus(int Status);
	virtual int SetVsaSncEVMStatus(int Status);
	virtual int SetVsaCcEVMStatus(int Status);
    virtual int SetListModeEnable(bool TxFlag);
    virtual int SetListModeDisable(bool TxFlag);
	virtual int SetSegVsaClear();
    virtual int SetSegVsaAlzCommParam(int SegNo, AlzParamComm *commonAnalyzeParam);
    virtual int SetSegVsaCapParam(int Segno, VsaParameter *vsaParam);
    virtual int SetSegVsaTrigParam(int Segno, VsaTrigParam *vsatrigParam);
    virtual int SetSegTrigCommonParam(bool TxFlag, int Segno, void *SegTrigCommParam, int Size);
    virtual int SetSegVsaAlzProtoParam(int Segno, int AlzType, void *ProAnalyzeParam, int Size);
    virtual int SetSegSeqTimeParam(bool TxFlag, int Segno, void *SegTimeParam, int Size);
    virtual int SetListTxSeqStart(double TrigerOffset);
    virtual int SetListRxSeqStart(int Repet, int EnableFlag, int IncrementFlag, int CellMod);
    virtual int SetSegVsgParam(int Segno, VsgParameter *vsgParam);
    virtual int SetSegVsgSyncParam(int Segno, int Status);
    virtual int SetSegVsgWaveParam(int Segno, VsgPattern *vsgWaveParam);
    virtual int SetListTxRxSeqStart();
    virtual int SetListTxSeqStop();
    virtual int SetListRxSeqStop();
    virtual int SetListTxRxSeqStop();
    virtual int GetListSeqAllState(bool TxFlag, int *State);
    virtual int GetListTxSeqAllCapState(int *SegNo);
    virtual int GetListRxSeqAllTransState(int *SegNo);
    virtual int GetListTxSeqAllAnalyState(int *SegNo);
    virtual int GetListTxSeqAllPowerResult(double *PowerResult, int SegNum);
    virtual int GetListLteTxSeqAllSegState(int *LteTxSegStat, int SegNum);
    virtual int DuplexSetEnable(int enable);
    virtual int DuplexGetEnable(int *enable);
    virtual int SetDuplexVsaNoiseCompFlag(int flag);
    virtual int GetDuplexVsaNoiseCompFlag(int *flag);
private:
    InstrumentHandle *m_pInstrument;
    std::unique_ptr<InstrumentHandle>m_InstrumentPtr;
};
