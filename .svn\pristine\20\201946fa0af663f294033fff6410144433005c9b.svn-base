//#ifndef LINUX
#include "includeall.h"
using namespace std;

#define DF_1024SZ 1024
#define DF_512SZ  512
#define VERSION_BWV     "1.1.1.5"

#ifndef LINUX
#ifdef _WIN64
#pragma comment (lib, "libmat_x64.lib")
#pragma comment (lib, "libmx_x64.lib")
#elif _WIN32
#pragma comment (lib, "libmat.lib")
#pragma comment (lib, "libmx.lib")
#endif // _WIN32
#endif

//
// WIFI 版本变最后一位，BT 版本变倒数第二位
// PNStructVersionNumer = 1.1.1.0, sizeof(GenWaveWifiStruct) = 4495344 bytes, add 11be
// PNStructVersionNumer = *******, sizeof(GenWaveWifiStruct) = 4497120 bytes, add wifi channel mode
//
#define PNStructVersionNumer    "*******"

#define WAVE_STREAM                 "wave"
#define BB_RESPONSE                 "BasebandResponse"
#define RF_RESPONSE                 "RfResponse"
#define NS_RESPONSE    	            "NSResponse"
#define RU_SUBCARRIER                 "RU_Subcarrier"

#define INFO2_FREQUNCY_TEXT         "fs"
#define INFO2_STREAMLISTINFO_TEXT   "StreamListInformation"
#define INFO2_DESCRIPTION_TEXT      "description"
#define INFO2_DATE_TEXT             "date"
#define INFO2_CREATEDBY_TEXT        "createdby"
#define Encrypted_IQ_Part2          "_part2"
// **************************************************************
//Name: ReadBool
//Func: 从指定mxArray中读取相关的逻辑信息
//Par:
//In:
//mparr:信息所在的mxArray(matlab)结构
//Ret:
//指定mxArray中的逻辑数据bool[]的起始地址
//Charge:
// ***************************************************************
bool *WaveForm_BWV::ReadBool(mxArray *mparr)
{
    mxLogical *pl = mxGetLogicals(mparr);
    return pl;
}

// **************************************************************
// Name: ReadComplex
// Func: 从指定mxArray中读取相关的Wave信息,返回值为错误时结果中的数据无效
// Par:
// In:
// mparr:信息所在的mxArray(matlab)结构
// Out:
// pResult：装载了指定mxArray中Wave数据的结果
// Ret:
// 返回出错代码
// ERR_OK:操作成功
// WT_ERR_OUT_OF_MEM:内存不足
// ERR_GENERAL_ERROR:其他错误
// Charge:
// ***************************************************************/
s32 WaveForm_BWV::ReadComplex(mxArray *mparr, stPNFileInfo *pResult, int Encoding)
{
    double *realData, *imagData;
    s32 i, length;

    //只读取第一列 行数如下
    length = mxGetM(mparr);

    if (length > WaveFileCommon::Instance().MaxSampleCount())
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }

    pResult->SampleCount = length;
    pResult->u32DatCnt = length;
    pResult->u32InterpCnt = sizeof(stPNDat) * length;

    //获取数据
    realData = mxGetPr(mparr);
    imagData = mxGetPi(mparr);

    if ((NULL != realData) && (NULL != imagData))
    {
        if (Encoding)
        {
            dword IQ[2];
            for (i = 0; i < length; i++)
            {
                IQ[0].value_32[0] = (s32)pResult->data[i].dReal;
                IQ[1].value_32[0] = (s32)pResult->data[i].dImag;

                IQ[0].value_32[1] = (s32)realData[i];
                IQ[1].value_32[1] = (s32)imagData[i];
				// If double is NaN or Infinite, a direct equal will cause loss of precision. So we use the memcpy method
                memcpy(&pResult->data[i].dReal, &IQ[0].value_64, sizeof(double));
                memcpy(&pResult->data[i].dImag, &IQ[1].value_64, sizeof(double));
            }
        }
        else
        {
            for (i = 0; i < length; i++)
            {
                pResult->data[i].dReal = realData[i];
                pResult->data[i].dImag = imagData[i];
            }
        }
    }
    else
    {
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    return WT_ERR_CODE_OK;
}

s32 WaveForm_BWV::ReadMatComplexIQ(MATFile *pmat, std::string name, stPNFileInfo *pResult, int Encoding)
{
    s32 iRet = WT_ERR_CODE_GENERAL_ERROR;
    mxArray *mparr = NULL;
    do
    {
        mparr = matGetVariable(pmat, name.c_str());
        if (NULL == mparr)
        {
            break;
        }

        if (mxIsDouble(mparr) && mxIsComplex(mparr))
        {
            iRet = ReadComplex(mparr, pResult, Encoding);
            if (iRet != WT_ERR_CODE_OK)
            {
                mxDestroyArray(mparr);
                break;
            }
        }
        mxDestroyArray(mparr);

        iRet = WT_ERR_CODE_OK;
    } while (0);

    return iRet;
}
// **************************************************************
// Name: ReadDouble
// Func: 从指定mxArray中读取相关的Double数据信息
// Par:
// In:
// mparr:信息所在的mxArray(matlab)结构
// Out:
// arrayLen：返回指针中的数据长度
// Ret:
// 读取到的double数组地址
// Charge:
// ***************************************************************/
double *WaveForm_BWV::ReadDouble(mxArray *mparr, s32 *arrayLen)
{
    double *mdarr;

    //只读取第一列 行数如下
    *arrayLen = mxGetN(mparr);

    //获取数据
    mdarr = (double *)mxGetData(mparr);

    return mdarr;
}

// **************************************************************
// Name: ReadStruct
// Func: 从指定mxArray中读取相关的PN信息
// Par:
// In:
// mparr:信息所在的mxArray(matlab)结构
// OUT：
// pResult：将读取结果装载在指定的结果数据中
// Ret:
// 返回出错代码
// ERR_OK:操作成功
// ERR_UNKNOWN_PARM:传入的参数有误，文件格式不对
// ERR_GENERAL_ERROR:其他错误
// Charge:
// ***************************************************************/
s32 WaveForm_BWV::ReadStruct(mxArray *mparr, stPNFileInfo *pResult, s32 index, s32 version)
{
    s32 nfields, NStructElems;
    s32 i;
    s32 result = WT_ERR_CODE_GENERAL_ERROR;
    double *tmpDouble;
    s32 tmpLen = 0;


    if (mxGetClassID(mparr) != mxSTRUCT_CLASS)
    {
        return result;
    }

    nfields = mxGetNumberOfFields(mparr);
    NStructElems = mxGetNumberOfElements(mparr);
    if (1 == version)
    {
        for (i = 0; i < nfields; i++)
        {
            mxArray *mmpa = NULL;
            const char *name = NULL;
            const char *frequncy_Text = m_field_Text[index * m_EachInfo2Size].c_str();
            const char *createdby_Text = m_field_Text[index * m_EachInfo2Size + 1].c_str();
            const char *date_Text = m_field_Text[index * m_EachInfo2Size + 2].c_str();
            const char *description_Text = m_field_Text[index * m_EachInfo2Size + 3].c_str();
            const char *bb_response_Text = m_field_Text[index * m_EachInfo2Size + 4].c_str();
            const char *rf_response_Text = m_field_Text[index * m_EachInfo2Size + 5].c_str();
            const char *ns_response_Text = m_field_Text[index * m_EachInfo2Size + 6].c_str();

            name = mxGetFieldNameByNumber(mparr, i);

            if ((0 != strcmp(name, frequncy_Text))
                && (0 != strcmp(name, createdby_Text))
                && (0 != strcmp(name, date_Text))
                && (0 != strcmp(name, description_Text))
                && (0 != strcmp(name, bb_response_Text))
                && (0 != strcmp(name, rf_response_Text))
                && (0 != strcmp(name, ns_response_Text)))
            {
                continue;
            }
            mmpa = mxGetFieldByNumber(mparr, 0, i);
            if (mmpa == NULL)
            {
                break;
            }
            if (mxIsDouble(mmpa))
            {
                if (mxIsComplex(mmpa))
                {
                    //ReadComplex(mmpa);
                }
                else
                {
                    tmpDouble = ReadDouble(mmpa, &tmpLen);

                    if (NULL != tmpDouble)
                    {
                        if (tmpLen > 0)
                        {
                            if (0 == strcmp(name, frequncy_Text))
                            {
                                if (tmpDouble[0] >= MHz_API - 1.0)
                                {
                                    pResult->SampleFreq = (s32)((tmpDouble[0]) / MHz_API);
                                }
                                else
                                {
                                    pResult->SampleFreq = (s32)tmpDouble[0];
                                }
                                result = WT_ERR_CODE_OK;
                            }
                            else if (0 == strcmp(name, rf_response_Text))
                            {
                                pResult->rf_response.FreqCount = tmpLen;
                                memcpy(pResult->rf_response.Response, tmpDouble, tmpLen * sizeof(double));
                            }
                            else if (0 == strcmp(name, bb_response_Text))
                            {
                                pResult->bb_response.FreqCount = tmpLen;
                                memcpy(pResult->bb_response.Response, tmpDouble, tmpLen * sizeof(double));
                            }
                            else if (0 == strcmp(name, ns_response_Text))
                            {
                                pResult->ns_response.FreqCount = tmpLen;
                                memcpy(pResult->ns_response.Response, tmpDouble, tmpLen * sizeof(double));
                            }
                        }

                        //mxFree(tmpDouble);//析构整个mparr时会跟随一起析构
                    }
                }
            }
            else if (mxIsChar(mmpa))
            {
                if (0 == strcmp(name, description_Text))
                {
                    mxGetString(mmpa, pResult->descreption, MaxDescLen);
                    pResult->desLen = strlen(pResult->descreption);
                }
            }
        }
    }
    else
    {
        for (i = 0; i < nfields; i++)
        {
            mxArray *mmpa;
            const char *name;
            mmpa = mxGetFieldByNumber(mparr, 0, i);
            if (mmpa == NULL)
            {
                break;
            }

            name = mxGetFieldNameByNumber(mparr, i);

            if ((0 != strcmp(name, INFO2_STREAMLISTINFO_TEXT))
                &&(0 != strcmp(name, INFO2_FREQUNCY_TEXT))
                && (0 != strcmp(name, INFO2_DESCRIPTION_TEXT))
                && (0 != strcmp(name, INFO2_DATE_TEXT))
                && (0 != strcmp(name, INFO2_CREATEDBY_TEXT))
                && (0 != strcmp(name, m_rfResponse_Text[index].c_str()))
                && (0 != strcmp(name, m_bbResponse_Text[index].c_str()))
                && (0 != strcmp(name, m_nsResponse_Text[index].c_str())))
            {
                continue;
            }

            if (mxIsDouble(mmpa))
            {
                if (mxIsComplex(mmpa))
                {
                    //ReadComplex(mmpa);
                }
                else
                {
                    tmpDouble = ReadDouble(mmpa, &tmpLen);

                    if (NULL != tmpDouble)
                    {
                        if (tmpLen > 0)
                        {
                            if (0 == strcmp(name, INFO2_FREQUNCY_TEXT))
                            {
                                if (tmpDouble[0] >= MHz_API - 1.0)
                                {
                                    pResult->SampleFreq = (s32)((tmpDouble[0]) / MHz_API);
                                }
                                else
                                {
                                    pResult->SampleFreq = (s32)tmpDouble[0];
                                }
                                result = WT_ERR_CODE_OK;
                            }
                            else if (0 == strcmp(name, m_rfResponse_Text[index].c_str()))
                            {
                                pResult->rf_response.FreqCount = tmpLen;
                                memcpy(pResult->rf_response.Response, tmpDouble, tmpLen * sizeof(double));
                            }
                            else if (0 == strcmp(name, m_bbResponse_Text[index].c_str()))
                            {
                                pResult->bb_response.FreqCount = tmpLen;
                                memcpy(pResult->bb_response.Response, tmpDouble, tmpLen * sizeof(double));
                            }
                            else if (0 == strcmp(name, m_nsResponse_Text[index].c_str()))
                            {
                                pResult->ns_response.FreqCount = tmpLen;
                                memcpy(pResult->ns_response.Response, tmpDouble, tmpLen * sizeof(double));
                            }
                        }

                        //mxFree(tmpDouble);//析构整个mparr时会跟随一起析构
                    }
                }
            }
            else if (mxIsChar(mmpa))
            {
                if (0 == strcmp(name, INFO2_DESCRIPTION_TEXT))
                {
                    mxGetString(mmpa, pResult->descreption, MaxDescLen);
                    pResult->desLen = strlen(pResult->descreption);
                }
                if (0 == strcmp(name, INFO2_STREAMLISTINFO_TEXT))
                {
                    mxGetString(mmpa, pResult->descreption, MaxDescLen);
                    pResult->desLen = strlen(pResult->descreption);
                }
            }
        }
    }
    return result;
}

s32 WaveForm_BWV::GetExternSettingStructSize(const char *fileName, int *len)
{
    MATFile *pMF = nullptr;
    mxArray *pStruct = nullptr;
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;

    do
    {
        pMF = matOpen(fileName, "r");
        if (nullptr == pMF)
        {
            break;
        }

        pStruct = matGetVariable(pMF, m_ExternSetting_Text.c_str());
        if (nullptr == pStruct)
        {
            break;
        }

        if (mxGetClassID(pStruct) != mxSTRUCT_CLASS)
        {
            break;
        }

        int nfields = mxGetNumberOfFields(pStruct);
        int NStructElems = mxGetNumberOfElements(pStruct);
        int dataSize = 0;

        int sizeIndex = mxGetFieldNumber(pStruct, strExternSettingStructSize);
        mxArray *pSize = mxGetFieldByNumber(pStruct, 0, sizeIndex);
        if (mxIsDouble(pSize))
        {
            dataSize = *(double *)mxGetData(pSize);
        }

        *len = dataSize;

        iRet = WT_ERR_CODE_OK;
    } while (0);

    if (pMF)
    {
        matClose(pMF);
    }

    return iRet;
}

s32 WaveForm_BWV::GetExternSettingStructData(const char *fileName, void *data, int len)
{
    MATFile *pMF = nullptr;
    mxArray *pStruct = nullptr;
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;

    do
    {
        pMF = matOpen(fileName, "r");
        if (nullptr == pMF)
        {
            break;
        }

        pStruct = matGetVariable(pMF, m_ExternSetting_Text.c_str());
        if (nullptr == pStruct)
        {
            break;
        }

        if (mxGetClassID(pStruct) != mxSTRUCT_CLASS)
        {
            break;
        }

        int nfields = mxGetNumberOfFields(pStruct);
        int NStructElems = mxGetNumberOfElements(pStruct);
        int dataSize = 0;

        int sizeIndex = mxGetFieldNumber(pStruct, strExternSettingStructSize);
        mxArray *pSize = mxGetFieldByNumber(pStruct, 0, sizeIndex);
        if (mxIsDouble(pSize))
        {
            dataSize = *(double *)mxGetData(pSize);
        }

        int DataIndex = mxGetFieldNumber(pStruct, strExternSettingStructData);
        mxArray *pData = mxGetFieldByNumber(pStruct, 0, DataIndex);
        if (pData && mxIsDouble(pData))
        {
            int  doubleLen = 0;
            double *tmpDouble = ReadDouble(pData, &doubleLen);
            doubleLen *= sizeof(double);

            if (tmpDouble && doubleLen > 0 && doubleLen >= dataSize)
            {
                memcpy(data, (void*)tmpDouble, (dataSize > len ? len : dataSize));
            }
        }
        iRet = WT_ERR_CODE_OK;
    } while (0);

    if (pMF)
    {
        matClose(pMF);
    }

    return iRet;
}

s32 WaveForm_BWV::GetRUCarrierStructData(const char * fileName, void * data, int len)
{
    MATFile *pMF = nullptr;
    mxArray *pStruct = nullptr;
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;

    do
    {
        pMF = matOpen(fileName, "r");
        if (nullptr == pMF)
        {
            break;
        }

        pStruct = matGetVariable(pMF, m_RUCarrier_Text.c_str());
        if (nullptr == pStruct)
        {
            break;
        }

        if (mxGetClassID(pStruct) != mxSTRUCT_CLASS)
        {
            break;
        }

        int nfields = mxGetNumberOfFields(pStruct);
        int NStructElems = mxGetNumberOfElements(pStruct);
        int dataSize = 0;

        int sizeIndex = mxGetFieldNumber(pStruct, strRUCarrierStructSize);
        mxArray *pSize = mxGetFieldByNumber(pStruct, 0, sizeIndex);
        if (mxIsDouble(pSize))
        {
            dataSize = *(double *)mxGetData(pSize);
        }

        int DataIndex = mxGetFieldNumber(pStruct, strRUCarrierStructData);
        mxArray *pData = mxGetFieldByNumber(pStruct, 0, DataIndex);
        if (pData && mxIsDouble(pData))
        {
            int  doubleLen = 0;
            double *tmpDouble = ReadDouble(pData, &doubleLen);
            doubleLen *= sizeof(double);

            if (tmpDouble && doubleLen > 0 && doubleLen >= dataSize)
            {
                memcpy(data, (void*)tmpDouble, (dataSize > len ? len : dataSize));
            }
        }
        iRet = WT_ERR_CODE_OK;
    } while (0);

    if (pMF)
    {
        matClose(pMF);
    }

    return iRet;
}

s32 WaveForm_BWV::GetPnStructData(const char * fileName, void * data, int len)
{
    MATFile *pMF = nullptr;
    mxArray *pStruct = nullptr;
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;

    do
    {
        pMF = matOpen(fileName, "r");
        if (nullptr == pMF)
        {
            break;
        }

        pStruct = matGetVariable(pMF, m_PNStruct_Text.c_str());
        if (nullptr == pStruct)
        {
            break;
        }

        if (mxGetClassID(pStruct) != mxSTRUCT_CLASS)
        {
            break;
        }

        int nfields = mxGetNumberOfFields(pStruct);
        int NStructElems = mxGetNumberOfElements(pStruct);
        int dataSize = 0;

        int sizeIndex = mxGetFieldNumber(pStruct, strPNStructSize);
        mxArray *pSize = mxGetFieldByNumber(pStruct, 0, sizeIndex);
        if (mxIsDouble(pSize))
        {
            dataSize = *(double *)mxGetData(pSize);
        }

        int DataIndex = mxGetFieldNumber(pStruct, strPNStructData);
        mxArray *pData = mxGetFieldByNumber(pStruct, 0, DataIndex);
        if (pData && mxIsDouble(pData))
        {
            int  doubleLen = 0;
            double *tmpDouble = ReadDouble(pData, &doubleLen);
            doubleLen *= sizeof(double);

            if (tmpDouble && doubleLen > 0 && doubleLen >= dataSize)
            {
                memcpy(data, (void*)tmpDouble, (dataSize > len ? len : dataSize));
            }
        }
        iRet = WT_ERR_CODE_OK;
    } while (0);

    if (pMF)
    {
        matClose(pMF);
    }

    return iRet;
}

s32 WaveForm_BWV::GetPnStructSize(const char * fileName, int *len)
{
    MATFile *pMF = nullptr;
    mxArray *pStruct = nullptr;
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;

    do
    {
        pMF = matOpen(fileName, "r");
        if (nullptr == pMF)
        {
            break;
        }

        pStruct = matGetVariable(pMF, m_PNStruct_Text.c_str());
        if (nullptr == pStruct)
        {
            break;
        }

        if (mxGetClassID(pStruct) != mxSTRUCT_CLASS)
        {
            break;
        }

        int nfields = mxGetNumberOfFields(pStruct);
        int NStructElems = mxGetNumberOfElements(pStruct);
        int dataSize = 0;

        int sizeIndex = mxGetFieldNumber(pStruct, strPNStructSize);
        mxArray *pSize = mxGetFieldByNumber(pStruct, 0, sizeIndex);
        if (mxIsDouble(pSize))
        {
            dataSize = *(double *)mxGetData(pSize);
        }

        int DataIndex = mxGetFieldNumber(pStruct, strPNStructData);
        mxArray *pData = mxGetFieldByNumber(pStruct, 0, DataIndex);
        if (pData && mxIsDouble(pData))
        {
            int  doubleLen = 0;
            double *tmpDouble = ReadDouble(pData, &doubleLen);
            doubleLen *= sizeof(double);

            if (tmpDouble && doubleLen > 0 && doubleLen >= dataSize)
            {
                *len = dataSize;
                iRet = WT_ERR_CODE_OK;;
            }
        }
    } while (0);

    if (pMF)
    {
        matClose(pMF);
    }

    return iRet;
}

s32 WaveForm_BWV::ConvertFrom_MatlabMIMO(const char *fileName, s32 freq, s32 index, stPNFileInfo *pResult, int pnMode)
{
    s32 result = WT_ERR_CODE_UNKNOW_PARAMETER;
    s32 resampleLen = 0;
    char *ptmp = NULL;
    char desBuff[1024] = { 0 };
    string descreption;
    s32 freqCnt[4] = { 0 };
    s32 freqTmp = 0;
    double responseTmp[WT_MAX_RFPORT_NUM] = { 0 };
    int Encoding = 0;
    MATFile *pmat = NULL;
    mxArray *mparr = NULL;
    mxArray *mVersion = NULL;
    s32 version = 0;
    bool insertDataFlag = false;
    pResult->SampleFreq = 80;//现有.mod文件默认频率
    pResult->Repeat = 1; //默认值。
    pResult->PnTail = 0.0; //默认值。
    pResult->PnHead = 0.0; //默认值。
    pResult->PnIfg = 0.0;
    pmat = matOpen(fileName, "r");
    if (NULL == pmat)
    {
        return result;
    }

    do
    {
        mparr = matGetVariable(pmat, m_info2_Text.c_str());
        if (NULL == mparr)
        {
            Logger::WriteLog(eumLogType_Error, "%s get bwv %s fail", __FUNCTION__, m_info2_Text.c_str());
            result = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (mxIsStruct(mparr))
        {
            mVersion = matGetVariable(pmat, m_version_Text.c_str());
            if (mVersion != NULL)
            {
                version = 1;
                
                if (mxIsStruct(mVersion))
                {
                    mxArray* bwv_version = mxGetField(mVersion, 0, m_version_Text.c_str());
                    if (mxIsChar(bwv_version))
                    {
                        char tmp_string[128] = { 0 };
                        mxGetString(bwv_version, tmp_string, sizeof(tmp_string) - 1);
                        if (tmp_string[0] != 0)
                        {
                            printf("bwv_version = %s\n", tmp_string);
                        }
                    }
                }
                mxDestroyArray(mVersion);
            }

            result = ReadStruct(mparr, pResult, index, version);
            if (result != WT_ERR_CODE_OK)
            {
                mxDestroyArray(mparr);
                break;
            }
            descreption = pResult->descreption;
            ptmp = strtok((char *)descreption.c_str(), "\r\n");
            pResult->vsaSourceFalg = FALSE;
            while (ptmp)
            {
                sscanf(ptmp, "%[^:]", desBuff);
                ptmp = ptmp + strlen(desBuff) + 1;      //将指针移到':'下一位
                if (!strcmp(strSampleFreq, desBuff))
                {
                    string tmpCh = ptmp;
                    transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::toupper);
                    bool isMHz = true;
                    if (tmpCh.find("MHZ") == string::npos)
                    {
                        isMHz = false;
                    }
                    sscanf(ptmp, "%lf", &pResult->SampleFreq);
                    if (!isMHz || pResult->SampleFreq >= MHz_API - 1.0)
                    {
                        pResult->SampleFreq /= MHz_API;
                    }
                }
                else if (!strcmp(strCenterFreq, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->dCenterFreq);
                }
                else if (!strcmp(strFreqOffset, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->freqOffset);
                }
                else if (!strcmp(strTriggerLevel, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->triggerLevel);
                }
                else if (!strcmp(strVsaAmpl, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->vsaAmpl);
                }
                else if (!strcmp(strDataType, desBuff))
                {
                    string tmpCh = ptmp;
                    transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::tolower);

                    pResult->DataType = enDataFormat_Float64;
                    if (0 == strncmp(tmpCh.c_str(), strInt16, strlen(strInt16)))
                    {
                        pResult->DataType = enDataFormat_Int16;
                    }
                }
                else if (!strcmp(strRFGain, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->RFGain);
                    //以信号文件有误RF Gain信息来确定是否为VSA保存的信号
                    pResult->vsaSourceFalg = TRUE;
                }
                else if (!strcmp(strIQGainImb, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->IQGainImb);
                }
                else if (!strcmp(strIQPhaseImb, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->IQPhaseImb);
                }
                else if (!strcmp(strDCOffset_I, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->DC_Offset_I);
                }
                else if (!strcmp(strDCOffset_Q, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->DC_Offset_Q);
                }
                else if (!strcmp(strTime_skew, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->timeSkew);
                }
                else if (!strcmp(strExtAttEnable, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->ExtAttEnable);
                }
                else if (!strcmp(strModType, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->ModType);
                }
                else if (!strcmp(strFlag8080, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->flag8080);
                }
                else if (!strcmp(strClockRate, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->clockRate);
                }
                else if (!strcmp(strScene, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->sceneMode);
                }
                else if (!strcmp(strExtAtt, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->ExtAtt);
                }
                else if (!strcmp(strEncodingTag, desBuff))
                {
                    sscanf(ptmp, "%d", &Encoding);
                }
                else if (!strcmp(strGap, desBuff))
                {
                    double gap = 0.0;
                    sscanf(ptmp, "%lf", &gap);
                    if (fabs(gap) < 1e-6)
                    {
                        insertDataFlag = true;
                    }
                }
                else if (!strcmp(strIFG, desBuff))
                {
                    if (pResult->MutiPNExInfo)
                    {
                        sscanf(ptmp, "%lf", &pResult->MutiPNExInfo->MutiPNIFG);
                    }
                }
                else if (!strcmp(strRepeat, desBuff))
                {
                    if (pResult->MutiPNExInfo)
                    {
                        sscanf(ptmp, "%d", &pResult->MutiPNExInfo->MutiPNRepeat);
                    }
                }
                else if (!strcmp(strPnHead, desBuff))
                {
                    if (pResult->MutiPNExInfo)
                    {
                        sscanf(ptmp, "%lf", &(pResult->MutiPNExInfo->MutiWaveGap[0]));
                    }
                }
                else if (!strcmp(strPnTail, desBuff))
                {
                    if (pResult->MutiPNExInfo)
                    {
                        sscanf(ptmp, "%lf", &(pResult->MutiPNExInfo->MutiWaveGap[1]));
                    }
                }
                else if (!strcmp(strPnIndex, desBuff))
                {
                    if (pResult->MutiPNExInfo)
                    {
                        sscanf(ptmp, "%d", &pResult->MutiPNExInfo->pnIndex);
                    }
                }
                else if (!strcmp(strPnStreamCount, desBuff))
                {
                    if (pResult->MutiPNExInfo)
                    {
                        sscanf(ptmp, "%d", &pResult->MutiPNExInfo->streamCnt);
                    }
                }
                ptmp = strtok(NULL, "\r\n");
            }
        }

        mxDestroyArray(mparr);

        mparr = matGetVariable(pmat, ((string)(strIQImageStart) + to_string(index)).c_str());
        if (mparr && mxIsDouble(mparr))
        {
            double *realData = mxGetPr(mparr);
            if (nullptr == realData)
            {
                break;
            }
            int len = mxGetM(mparr);
            pResult->iqImage.freq_Iqimb_len = len / 2;
            for (int i = 0; i < len; i++)
            {
                if (i < pResult->iqImage.freq_Iqimb_len)
                {
                    pResult->iqImage.freq_gain_imb[i] = realData[i];
                }
                else
                {
                    pResult->iqImage.freq_quad_err[i % pResult->iqImage.freq_Iqimb_len] = realData[i];
                }
            }
        }

        // Read IQ complex value
        result = ReadMatComplexIQ(pmat, m_wave_Text[index], pResult);
        if (result)
        {
            break;
        }

        if (Encoding)
        {
            result = ReadMatComplexIQ(pmat, m_wave_Text[index] + Encrypted_IQ_Part2, pResult, Encoding);
            if (result)
            {
                break;
            }
        }
        result = WT_ERR_CODE_OK;
    } while (0);

    matClose(pmat);

    if (WT_ERR_CODE_OK == result && Encoding)
    {
        WaveFileCommon::Instance().ScramblerData(pResult, false);
    }

    if (WT_ERR_CODE_OK == result && insertDataFlag && (pnMode & PN_VSA_SCENE_MODE))
    {
        WaveFileCommon::Instance().InsertGapData(pResult);
    }
    return result;
}

static s32 SaveMatComplex(MATFile *pMF, stPNFileInfo *pResult, s32 streamIndex, std::string name, s32 Encoding = 0, s32 part = 0)
{
    FP64 *mxReal = NULL;
    FP64 *mxImag = NULL;
    s32 iRet = WT_ERR_CODE_GENERAL_ERROR;
    do
    {
        //创建一个复矩阵
        mxArray *pStruct = mxCreateDoubleMatrix(pResult->SampleCount, 1, mxCOMPLEX);
        if (nullptr == pStruct)
        {
            break;
        }
        // 让指针Array指向这个矩阵的数据
        // 在VC里数组是按行排放的，在MATLAB里矩阵是按列排放的

        //得到矩阵的实部
        mxReal = mxGetPr(pStruct);

        //得到矩阵的虚部
        mxImag = mxGetPi(pStruct);

        if ((nullptr == mxReal) || (nullptr == mxImag))
        {
            break;
        }

        //写数据
        if (0 == Encoding)
        {
            for (int i = 0; i < pResult->SampleCount; i++)
            {
                mxReal[i] = pResult->data[i].dReal;
                mxImag[i] = pResult->data[i].dImag;
            }
        }
        else
        {
            dword IQ[2];
            if (0 == part)
            {
                for (int i = 0; i < pResult->SampleCount; i++)
                {
					// If double is NaN or Infinite, a direct equal will cause loss of precision. So we use the memcpy method
                    memcpy(&IQ[0].value_64, &pResult->data[i].dReal, sizeof(double));
                    memcpy(&IQ[1].value_64, &pResult->data[i].dImag, sizeof(double));
                    mxReal[i] = IQ[0].value_32[0];
                    mxImag[i] = IQ[1].value_32[0];
                }
            }
            else
            {
                for (int i = 0; i < pResult->SampleCount; i++)
                {
					// If double is NaN or Infinite, a direct equal will cause loss of precision. So we use the memcpy method
                    memcpy(&IQ[0].value_64, &pResult->data[i].dReal, sizeof(double));
                    memcpy(&IQ[1].value_64, &pResult->data[i].dImag, sizeof(double));

                    mxReal[i] = IQ[0].value_32[1];
                    mxImag[i] = IQ[1].value_32[1];
                }
                name += std::string(Encrypted_IQ_Part2);
            }
        }
        matPutVariable(pMF, name.c_str(), pStruct);
        // 释放矩阵空间
        mxDestroyArray(pStruct);

        iRet = WT_ERR_CODE_OK;
    } while (0);

    return iRet;
}

s32 WaveForm_BWV::CatenateMutiPnMatFiles(const MutiPNCatenateInfo *catenateInfo)
{
    int i = 0;
    int ret = WT_ERR_CODE_UNKNOW_PARAMETER;
    MATFile *pMF = matOpen(catenateInfo->fileName, "w");
    if (pMF == NULL)
    {
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    stPNFileInfo Result;
    memset(&Result, 0, sizeof(Result));
    MutiPNExtendInfo MutiPNInfo;
    Result.MutiPNExInfo = &MutiPNInfo;
    ret = InitPNFileInfoInstance(&Result);
    if (ret != WT_ERR_CODE_OK)
    {
        matClose(pMF);
        return ret;
    }
    stringstream mutiPnHead;
    const char *sperator = ":";
    mutiPnHead << strStreamListInfo << nextLine;
    mutiPnHead << strPnCount << sperator << catenateInfo->srcFilesNum << nextLine;
    
    for (i = 0; i < catenateInfo->srcFilesNum; i++)
    {
        mutiPnHead << strPnStreamCount << sperator << catenateInfo->streamCnt[catenateInfo->index[i]] << nextLine;
    }

    string str;
    /*WaveFileCommon::Instance().GeneratorFlag() = WaveGenTypeWIFI;
    InitWaveGenerator_WIFI()*/
    int standard = 0;
    for (i = 0; i < catenateInfo->srcFilesNum; i++)
    {
        standard = (catenateInfo->userParam[catenateInfo->index[i]]).wifiParam.commonParam.standard;
        switch (standard)
        {
        case WT_DEMOD_BT:
            InitWaveGenerator_BTV2(&catenateInfo->userParam[catenateInfo->index[i]].BtParam);
            break;
        case WT_DEMOD_CW:
            InitWaveGenerator_CW(&catenateInfo->userParam[catenateInfo->index[i]].CwParam);
            break;
        case WT_DEMOD_GLE:
            InitWaveGenerator_GLE(&catenateInfo->userParam[catenateInfo->index[i]].GleParam);
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM: 
            InitWaveGenerator_WiSun(&catenateInfo->userParam[catenateInfo->index[i]].WiSunParam);
            break;
        default:
            InitWaveGenerator_WIFI(&catenateInfo->userParam[catenateInfo->index[i]].wifiParam);
            break;
        }

        for (int j = 0; j < catenateInfo->streamCnt[catenateInfo->index[i]]; j++)
        {
            str = "/tmp/tmpwave/" + string(catenateInfo->srcPnFiles[catenateInfo->index[i]]);
            ret = ConvertFrom_MatlabMIMO(str.c_str(), 0, j, &Result, PN_VSG_SCENE_MODE);
            if (ret != WT_ERR_CODE_OK)
            {
                DisposePNFileInfo(&Result);
                matClose(pMF);
                return WT_ERR_CODE_GENERAL_ERROR;
            }

            Result.MutiPNExInfo->pnIndex = i + 1;
            Result.pnIndex = i + 1;

            ret = SaveBwv2File(&Result, pMF, mutiPnHead.str().c_str(), to_string(i), j);
            if (ret != WT_ERR_CODE_OK)
            {
                DisposePNFileInfo(&Result);
                matClose(pMF);
                return WT_ERR_CODE_GENERAL_ERROR;
            }
        }
    }

    WaveFileCommon::Instance().GeneratorFlag() = WaveGenTypeInit;
    DisposePNFileInfo(&Result);
    matClose(pMF);
    return ret;
}

s32 WaveForm_BWV::SaveBwv2File(stPNFileInfo *pResult, MATFile *pMF, const char *bwv2HeadInfo, string fileIdx, s32 streamIndex)
{
    s32 result = WT_ERR_CODE_GENERAL_ERROR;
    FP64 *mxReal = NULL;
    FP64 *mxImag = NULL;
    mxArray *pStruct = NULL;
    mxArray *tmp = NULL;
    int NioseCalCount = 481;
    s8 fileHeadInfo[DF_1024SZ] = { 0 };
    s32 i = 0;
    s32 Encoding = WaveFileCommon::Instance().Encoding();
    do
    {
        if ((pMF == NULL) || (pResult == NULL) || streamIndex < 0)
        {
            break;
        }

//#pragma region IQ数据结构体
        if (true)
        {
            WaveFileCommon::Instance().ScramblerData(pResult, true);
            SaveMatComplex(pMF, pResult, streamIndex, m_wave_Text[streamIndex] + "File" + fileIdx + "_" + to_string(streamIndex), Encoding);
            if (Encoding)
            {
                SaveMatComplex(pMF, pResult, streamIndex, m_wave_Text[streamIndex] + "File" + fileIdx + "_" + to_string(streamIndex), Encoding, 1);
            }
        }
//#pragma endregion IQ数据结构体

        if ("0" == fileIdx)
        {
//#pragma region 版本号结构体
            //BWV版本信息结构体
            if (true)
            {
                vector<const char *>tmpFieldPtr(1);
                tmpFieldPtr[0] = m_version_Text.c_str();
                pStruct = mxCreateStructMatrix(1, 1, tmpFieldPtr.size(), &tmpFieldPtr[0]);
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
                tmp = mxCreateString(VERSION_BWV);
                mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                matPutVariable(pMF, m_version_Text.c_str(), pStruct);
                mxDestroyArray(pStruct);
            }
//#pragma endregion 版本号结构体

            if (true)
            {
                //多Pn信号文件信息
                vector<const char *>tmpFieldPtr(1);
                tmpFieldPtr[0] = INFO2_STREAMLISTINFO_TEXT;
                pStruct = mxCreateStructMatrix(1, 1, tmpFieldPtr.size(), &tmpFieldPtr[0]);
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
                tmp = mxCreateString(bwv2HeadInfo);
                mxSetField(pStruct, 0, INFO2_STREAMLISTINFO_TEXT, tmp);
                matPutVariable(pMF, INFO2_STREAMLISTINFO_TEXT, pStruct);
                mxDestroyArray(pStruct);
            }
        }

        if (0 == streamIndex)
        {
//#pragma region WaveGenerator 结构体
            //Wave generator的PN结构体信息
            if (true)
            {
                void *data = nullptr;
                int len = 0;
                WaveFileCommon::Instance().WritePnStruct(pResult, &data, &len);
                if (len > 0)
                {
                    const char *tmpFieldPtr[3] = { strPNStructVer, strPNStructSize, strPNStructData };
                    //创建结构体，包含3个部分(版本号、数据长度、数据)
                    pStruct = mxCreateStructMatrix(1, 1, 3, &tmpFieldPtr[0]);

                    //版本号
                    tmp = mxCreateString(PNStructVersionNumer);
                    mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                    //数据长度
                    tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                    mxReal = mxGetPr(tmp);
                    *mxReal = len;
                    mxSetField(pStruct, 0, tmpFieldPtr[1], tmp);


                    //数据，用double存储。否则用char会申请不到内存
                    int roundLen = (len / sizeof(double));
                    int remain = (len % sizeof(double));
                    if (remain)
                    {
                        roundLen += 1;
                    }
                    tmp = mxCreateDoubleMatrix(1, roundLen, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy((void *)mxReal, data, len);
                    mxSetField(pStruct, 0, tmpFieldPtr[2], tmp);

                    matPutVariable(pMF, (m_PNStruct_Text + fileIdx).c_str(), pStruct);
                    mxDestroyArray(pStruct);
                }
            }
//#pragma endregion WaveGenerator 结构体

//#pragma region RUCarrier 结构体
            //Wave generator的PN结构体信息
            if (true)
            {
                void *data = nullptr;
                int len = 0;
                WaveFileCommon::Instance().WriteRUCarrierStruct(pResult, &data, &len);
                if (len > 0)
                {
                    const char *tmpFieldPtr[3] = { strRUCarrierStructVer, strRUCarrierStructSize, strRUCarrierStructData };
                    //创建结构体，包含3个部分(版本号、数据长度、数据)
                    pStruct = mxCreateStructMatrix(1, 1, 3, &tmpFieldPtr[0]);

                    //版本号
                    tmp = mxCreateString(PNStructVersionNumer);
                    mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                    //数据长度
                    tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                    mxReal = mxGetPr(tmp);
                    *mxReal = len;
                    mxSetField(pStruct, 0, tmpFieldPtr[1], tmp);


                    //数据，用double存储。否则用char会申请不到内存
                    int roundLen = (len / sizeof(double));
                    int remain = (len % sizeof(double));
                    if (remain)
                    {
                        roundLen += 1;
                    }
                    tmp = mxCreateDoubleMatrix(1, roundLen, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy((void *)mxReal, data, len);
                    mxSetField(pStruct, 0, tmpFieldPtr[2], tmp);

                    matPutVariable(pMF, (m_RUCarrier_Text + fileIdx).c_str(), pStruct);
                    mxDestroyArray(pStruct);
                }
            }
//#pragma endregion RUCarrier 结构体

//#pragma region 额外的配置信息结构体
            //Wave generator的PN结构体信息
            if (true)
            {
                void *data = nullptr;
                int len = 0;
                WaveFileCommon::Instance().WriteExternSetingStruct(pResult, &data, &len);
                if (len > 0)
                {
                    const char *tmpFieldPtr[3] = { strExternSettingStructVer, strExternSettingStructSize, strExternSettingStructData };
                    //创建结构体，包含3个部分(版本号、数据长度、数据)
                    pStruct = mxCreateStructMatrix(1, 1, 3, &tmpFieldPtr[0]);

                    //版本号
                    tmp = mxCreateString(PNStructVersionNumer);
                    mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                    //数据长度
                    tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                    mxReal = mxGetPr(tmp);
                    *mxReal = len;
                    mxSetField(pStruct, 0, tmpFieldPtr[1], tmp);


                    //数据，用double存储。否则用char会申请不到内存
                    int roundLen = (len / sizeof(double));
                    int remain = (len % sizeof(double));
                    if (remain)
                    {
                        roundLen += 1;
                    }
                    tmp = mxCreateDoubleMatrix(1, roundLen, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy((void *)mxReal, data, len);
                    mxSetField(pStruct, 0, tmpFieldPtr[2], tmp);

                    matPutVariable(pMF, (m_ExternSetting_Text + fileIdx).c_str(), pStruct);
                    mxDestroyArray(pStruct);
                }
            }
//#pragma endregion 额外的配置信息结构体

        }
//#pragma region info2结构体
        if (true)
        {
            //写INFO2结构体
            if (0 == streamIndex)
            {
                vector<const char *>tmpFieldPtr(m_field_Text.size());
                for (int i = 0; i < m_field_Text.size(); i++)
                {
                    tmpFieldPtr[i] = m_field_Text[i].c_str();
                }
                pStruct = mxCreateStructMatrix(1, 1, m_field_Text.size(), &tmpFieldPtr[0]);
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
            }
            else
            {
                pStruct = matGetVariable(pMF, (m_info2_Text + fileIdx).c_str());
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
            }

            if (pStruct)
            {
                tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                mxReal = mxGetPr(tmp);
                if (pResult->SampleFreq < MHz_API - 0.0001)
                {
                    *mxReal = pResult->SampleFreq * MHz_API;

                    pResult->SampleFreq *= MHz_API;
                }
                else
                {
                    *mxReal = pResult->SampleFreq;
                }
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size].c_str(), tmp);
                //mxFree(tmp);

                tmp = mxCreateString(m_author_Text.c_str());
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 1].c_str(), tmp);
                //mxFree(tmp);

                tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                mxReal = mxGetPr(tmp);
                *mxReal = 0;
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 2].c_str(), tmp);
                //mxFree(tmp);

                if (1)
                {
                    string str;
                    WaveFileCommon::Instance().WriteDescriptionTo(str, ":", pResult);
                    strcpy(fileHeadInfo, str.c_str());
                }

                tmp = mxCreateString(fileHeadInfo);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 3].c_str(), tmp);

                tmp = mxCreateDoubleMatrix(1, pResult->bb_response.FreqCount, mxREAL);
                mxReal = mxGetPr(tmp);
                memcpy(mxReal, pResult->bb_response.Response, sizeof(double) * pResult->bb_response.FreqCount);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 4].c_str(), tmp);
                //mxFree(tmp);

                tmp = mxCreateDoubleMatrix(1, pResult->rf_response.FreqCount, mxREAL);
                mxReal = mxGetPr(tmp);
                memcpy(mxReal, pResult->rf_response.Response, sizeof(double) * pResult->rf_response.FreqCount);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 5].c_str(), tmp);
                //mxFree(tmp);
                
                if(pResult->ns_response.FreqCount)
                {
                    tmp = mxCreateDoubleMatrix(1, pResult->ns_response.FreqCount, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy(mxReal, pResult->ns_response.Response, sizeof(double) * pResult->ns_response.FreqCount);
                }
                else
                {
                    tmp = mxCreateDoubleMatrix(1, NioseCalCount, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memset(mxReal, 0, sizeof(double) * NioseCalCount);
                }
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 6].c_str(), tmp);
                //mxFree(tmp);
            }

            matPutVariable(pMF, (m_info2_Text + fileIdx).c_str(), pStruct);
            mxDestroyArray(pStruct);
        }
//#pragma endregion info2结构体

        result = WT_ERR_CODE_OK;
    } while (0);

    return result;
}

// ****************************************************************************
// Name:      SaveMatFile
// Func:      将指定PN信息保存为mat格式文件
// Path:      SaveMatFile
// Access:    public
// Returns:   s32
// Parameter: const char * fileName
// Parameter: stPNFileInfo * pResult
// Parameter: s32 streamIndex
// ****************************************************************************
s32 WaveForm_BWV::SaveMatFile(stPNFileInfo *pResult, const char *fileName, s32 streamIndex)
{
    s32 result = WT_ERR_CODE_GENERAL_ERROR;
    MATFile *pMF = NULL;
    FP64 *mxReal = NULL;
    FP64 *mxImag = NULL;
    mxArray *pStruct = NULL;
    mxArray *tmp = NULL;
    s8 fileHeadInfo[DF_1024SZ] = { 0 };
    s32 i = 0;
    s32 Encoding = WaveFileCommon::Instance().Encoding();
    do
    {
        if ((fileName == NULL) || (pResult == NULL))
        {
            break;
        }

        //兼容MIMO情况，首次打开用写，后续只需更新

        if (streamIndex < 0)
        {
            result = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (streamIndex == 0)
        {
            pMF = matOpen(fileName, "w");
        }
        else
        {
            pMF = matOpen(fileName, "u");
        }

        if (pMF == NULL)
        {
            result = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }
//#pragma region IQ数据结构体
        if (true)
        {
            WaveFileCommon::Instance().ScramblerData(pResult, true);
            SaveMatComplex(pMF, pResult, streamIndex, m_wave_Text[streamIndex], Encoding);
            if (Encoding)
            {
                SaveMatComplex(pMF, pResult, streamIndex, m_wave_Text[streamIndex], Encoding, 1);
            }
        }
//#pragma endregion IQ数据结构体

        if (pResult->iqImage.freq_Iqimb_len)
        {
            string tmpFieldPtr = (string)(strIQImageStart) + to_string(streamIndex);
            int dataLen = pResult->iqImage.freq_Iqimb_len * 2;
            pStruct = mxCreateDoubleMatrix(dataLen, 1, mxREAL);
            if (nullptr == pStruct)
            {
                break;
            }
            mxReal = mxGetPr(pStruct);
            if (nullptr == mxReal)
            {
                break;
            }
            for (int i = 0; i < dataLen; i++)
            {
                if (i < pResult->iqImage.freq_Iqimb_len)
                {
                    mxReal[i] = pResult->iqImage.freq_gain_imb[i];
                }
                else
                {
                    mxReal[i] = pResult->iqImage.freq_quad_err[i % pResult->iqImage.freq_Iqimb_len];
                }
            }
            matPutVariable(pMF, tmpFieldPtr.c_str(), pStruct);
            mxDestroyArray(pStruct);
        }

        if (0 == streamIndex)
        {
//#pragma region 版本号结构体
            //BWV版本信息结构体
            if (true)
            {
                vector<const char *>tmpFieldPtr(1);
                tmpFieldPtr[0] = m_version_Text.c_str();
                pStruct = mxCreateStructMatrix(1, 1, tmpFieldPtr.size(), &tmpFieldPtr[0]);
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
                tmp = mxCreateString(VERSION_BWV);
                mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                matPutVariable(pMF, m_version_Text.c_str(), pStruct);
                mxDestroyArray(pStruct);
            }
//#pragma endregion 版本号结构体

//#pragma region WaveGenerator 结构体
            //Wave generator的PN结构体信息
            if (true)
            {
                void *data = nullptr;
                int len = 0;
                WaveFileCommon::Instance().WritePnStruct(pResult, &data, &len);
                if (len > 0)
                {
                    const char *tmpFieldPtr[3] = { strPNStructVer, strPNStructSize, strPNStructData };
                    //创建结构体，包含3个部分(版本号、数据长度、数据)
                    pStruct = mxCreateStructMatrix(1, 1, 3, &tmpFieldPtr[0]);

                    //版本号
                    tmp = mxCreateString(PNStructVersionNumer);
                    mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                    //数据长度
                    tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                    mxReal = mxGetPr(tmp);
                    *mxReal = len;
                    mxSetField(pStruct, 0, tmpFieldPtr[1], tmp);


                    //数据，用double存储。否则用char会申请不到内存
                    int roundLen = (len / sizeof(double));
                    int remain = (len % sizeof(double));
                    if (remain)
                    {
                        roundLen += 1;
                    }
                    tmp = mxCreateDoubleMatrix(1, roundLen, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy((void *)mxReal, data, len);
                    mxSetField(pStruct, 0, tmpFieldPtr[2], tmp);

                    matPutVariable(pMF, m_PNStruct_Text.c_str(), pStruct);
                    mxDestroyArray(pStruct);
                }
            }
//#pragma endregion WaveGenerator 结构体

//#pragma region RUCarrier 结构体
            //Wave generator的PN结构体信息
            if (true)
            {
                void *data = nullptr;
                int len = 0;
                WaveFileCommon::Instance().WriteRUCarrierStruct(pResult, &data, &len);
                if (len > 0)
                {
                    const char *tmpFieldPtr[3] = { strRUCarrierStructVer, strRUCarrierStructSize, strRUCarrierStructData };
                    //创建结构体，包含3个部分(版本号、数据长度、数据)
                    pStruct = mxCreateStructMatrix(1, 1, 3, &tmpFieldPtr[0]);

                    //版本号
                    tmp = mxCreateString(PNStructVersionNumer);
                    mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                    //数据长度
                    tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                    mxReal = mxGetPr(tmp);
                    *mxReal = len;
                    mxSetField(pStruct, 0, tmpFieldPtr[1], tmp);


                    //数据，用double存储。否则用char会申请不到内存
                    int roundLen = (len / sizeof(double));
                    int remain = (len % sizeof(double));
                    if (remain)
                    {
                        roundLen += 1;
                    }
                    tmp = mxCreateDoubleMatrix(1, roundLen, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy((void *)mxReal, data, len);
                    mxSetField(pStruct, 0, tmpFieldPtr[2], tmp);

                    matPutVariable(pMF, m_RUCarrier_Text.c_str(), pStruct);
                    mxDestroyArray(pStruct);
                }
            }
//#pragma endregion RUCarrier 结构体

//#pragma region 额外的配置信息结构体
            //Wave generator的PN结构体信息
            if (true)
            {
                void *data = nullptr;
                int len = 0;
                WaveFileCommon::Instance().WriteExternSetingStruct(pResult, &data, &len);
                if (len > 0)
                {
                    const char *tmpFieldPtr[3] = { strExternSettingStructVer, strExternSettingStructSize, strExternSettingStructData };
                    //创建结构体，包含3个部分(版本号、数据长度、数据)
                    pStruct = mxCreateStructMatrix(1, 1, 3, &tmpFieldPtr[0]);

                    //版本号
                    tmp = mxCreateString(PNStructVersionNumer);
                    mxSetField(pStruct, 0, tmpFieldPtr[0], tmp);

                    //数据长度
                    tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                    mxReal = mxGetPr(tmp);
                    *mxReal = len;
                    mxSetField(pStruct, 0, tmpFieldPtr[1], tmp);


                    //数据，用double存储。否则用char会申请不到内存
                    int roundLen = (len / sizeof(double));
                    int remain = (len % sizeof(double));
                    if (remain)
                    {
                        roundLen += 1;
                    }
                    tmp = mxCreateDoubleMatrix(1, roundLen, mxREAL);
                    mxReal = mxGetPr(tmp);
                    memcpy((void *)mxReal, data, len);
                    mxSetField(pStruct, 0, tmpFieldPtr[2], tmp);

                    matPutVariable(pMF, m_ExternSetting_Text.c_str(), pStruct);
                    mxDestroyArray(pStruct);
                }
            }
//#pragma endregion 额外的配置信息结构体

        }
//#pragma region info2结构体
        if (true)
        {
            //写INFO2结构体
            if (0 == streamIndex)
            {
                vector<const char *>tmpFieldPtr(m_field_Text.size());
                for (int i = 0; i < m_field_Text.size(); i++)
                {
                    tmpFieldPtr[i] = m_field_Text[i].c_str();
                }
                pStruct = mxCreateStructMatrix(1, 1, m_field_Text.size(), &tmpFieldPtr[0]);
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
            }
            else
            {
                pStruct = matGetVariable(pMF, m_info2_Text.c_str());
                if (nullptr == pStruct)
                {
                    result = WT_ERR_CODE_ALLOC_MEMORY_FAIL;
                    break;
                }
            }

            if (pStruct)
            {
                tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                mxReal = mxGetPr(tmp);
                if (pResult->SampleFreq < MHz_API - 0.0001)
                {
                    *mxReal = pResult->SampleFreq * MHz_API;

                    pResult->SampleFreq *= MHz_API;
                }
                else
                {
                    *mxReal = pResult->SampleFreq;
                }
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size].c_str(), tmp);
                //mxFree(tmp);

                tmp = mxCreateString(m_author_Text.c_str());
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 1].c_str(), tmp);
                //mxFree(tmp);

                tmp = mxCreateDoubleMatrix(1, 1, mxREAL);
                mxReal = mxGetPr(tmp);
                *mxReal = 0;
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 2].c_str(), tmp);
                //mxFree(tmp);

                if (1)
                {
                    string str;
                    WaveFileCommon::Instance().WriteDescriptionTo(str, ":", pResult);
                    strcpy(fileHeadInfo, str.c_str());
                }

                tmp = mxCreateString(fileHeadInfo);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 3].c_str(), tmp);

                tmp = mxCreateDoubleMatrix(1, pResult->bb_response.FreqCount, mxREAL);
                mxReal = mxGetPr(tmp);
                memcpy(mxReal, pResult->bb_response.Response, sizeof(double) * pResult->bb_response.FreqCount);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 4].c_str(), tmp);
                //mxFree(tmp);

                tmp = mxCreateDoubleMatrix(1, pResult->rf_response.FreqCount, mxREAL);
                mxReal = mxGetPr(tmp);
                memcpy(mxReal, pResult->rf_response.Response, sizeof(double) * pResult->rf_response.FreqCount);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 5].c_str(), tmp);
                //mxFree(tmp);
                
                tmp = mxCreateDoubleMatrix(1, pResult->ns_response.FreqCount, mxREAL);
                mxReal = mxGetPr(tmp);
                memcpy(mxReal, pResult->ns_response.Response, sizeof(double) * pResult->ns_response.FreqCount);
                mxSetField(pStruct, 0, m_field_Text[streamIndex * m_EachInfo2Size + 6].c_str(), tmp);
                //mxFree(tmp);
            }

            matPutVariable(pMF, m_info2_Text.c_str(), pStruct);
            mxDestroyArray(pStruct);
        }
//#pragma endregion info2结构体

        result = WT_ERR_CODE_OK;
    } while (0);

    // 关闭文件
    matClose(pMF);
    pMF = NULL;

    return result;
}

WaveForm_BWV::WaveForm_BWV()
{
    m_MaxNss = 8;
    m_author_Text = "iTest & Measurement";
    m_version_Text = "version";
    m_info2_Text = "info2";
    m_PNStruct_Text = strPNStruct;
    m_RUCarrier_Text = strRUCarrierStruct;
    m_ExternSetting_Text = strExternSettingStruct;

    for (int nss = 0; nss < m_MaxNss; nss++)
    {
        stringstream tmpstr;
        tmpstr << WAVE_STREAM;
        if (nss > 0)
        {
            tmpstr << (nss + 1);
        }
        m_wave_Text.push_back(tmpstr.str());
    }

    for (int nss = 0; nss < m_MaxNss; nss++)
    {
        stringstream tmpstr;
        tmpstr << BB_RESPONSE;
        if (nss > 0)
        {
            tmpstr << (nss + 1);
        }
        m_bbResponse_Text.push_back(tmpstr.str());
    }

    for (int nss = 0; nss < m_MaxNss; nss++)
    {
        stringstream tmpstr;
        tmpstr << RF_RESPONSE;
        if (nss > 0)
        {
            tmpstr << (nss + 1);
        }
        m_rfResponse_Text.push_back(tmpstr.str());
    }

    for (int nss = 0; nss < m_MaxNss; nss++)
    {
        stringstream tmpstr;
        tmpstr << NS_RESPONSE;
        if (nss > 0)
        {
            tmpstr << (nss + 1);
        }
        m_nsResponse_Text.push_back(tmpstr.str());
    }

    for (int nss = 0; nss < m_MaxNss; nss++)
    {
        stringstream freqStr;
        stringstream discriptionStr;
        stringstream dateStr;
        stringstream creatorStr;
        stringstream bbStr;
        stringstream rfStr;
        stringstream nsStr;

        m_EachInfo2Size = 7;

        freqStr << INFO2_FREQUNCY_TEXT;
        discriptionStr << INFO2_DESCRIPTION_TEXT;
        dateStr << INFO2_DATE_TEXT;
        creatorStr << INFO2_CREATEDBY_TEXT;
        bbStr << BB_RESPONSE;
        rfStr << RF_RESPONSE;
        nsStr << NS_RESPONSE;

        if (nss > 0)
        {
            freqStr << (nss + 1);
            discriptionStr << (nss + 1);
            dateStr << (nss + 1);
            creatorStr << (nss + 1);
            bbStr << (nss + 1);
            rfStr << (nss + 1);
            nsStr << (nss + 1);
        }
        m_field_Text.push_back(freqStr.str());
        m_field_Text.push_back(creatorStr.str());
        m_field_Text.push_back(dateStr.str());
        m_field_Text.push_back(discriptionStr.str());
        m_field_Text.push_back(bbStr.str());
        m_field_Text.push_back(rfStr.str());
        m_field_Text.push_back(nsStr.str());
    }
}

WaveForm_BWV::~WaveForm_BWV()
{
}

s32 WaveForm_BWV::ConvertFrom_MatlabMIMO(const char *fileName, s32 freq, s32 StreamIndex, s32 PnIndex, stPNFileInfo *pResult, int pnMode)
{
    s32 result = WT_ERR_CODE_UNKNOW_PARAMETER;
    MATFile *pmat = NULL;
    mxArray *mparr = NULL;
    mxArray *mVersion = NULL;
    mxArray *StreamListInfo = NULL;
    char bwv_StreamListInfostr[MaxDescLen];
    char *ptmp = NULL;
    s32 version = 0;
    char desBuff[1024] = { 0 };
    string descreption;
    bool insertDataFlag = false;
    int Encoding = 0;
    pResult->Repeat = 1; //默认值。
    pResult->PnTail = 0.0; //默认值。
    pResult->PnHead = 0.0; //默认值。
    pResult->PnIfg = 0.0;
    int PnOrder[65];//多PN信号的信号数量上限64
    int RetPnCount = 0;
    int Bwv_PnId = -1;
    result = GetPNCountFromFile(fileName, RetPnCount, PnOrder);
    if (result != WT_ERR_CODE_OK)
    {
        return result;
    }
    else
    {
        for (int i = 0; i < RetPnCount;i++)
        {
            if (PnOrder[i] == PnIndex)
            {
                Bwv_PnId = i;
                break;
            }
        }
        if(Bwv_PnId == -1)
        {
            result = WT_ERR_CODE_UNKNOW_PARAMETER;
            return result;
        }
    }
    pmat = matOpen(fileName, "r");
    if (NULL == pmat)
    {
        return result;
    }

    do
    {
        mparr = matGetVariable(pmat, (m_info2_Text + std::to_string(Bwv_PnId)).c_str());
        if (NULL == mparr)
        {
            Logger::WriteLog(eumLogType_Error, "%s get bwv %s fail", __FUNCTION__, (m_info2_Text + std::to_string(Bwv_PnId)).c_str());
            result = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        if (mxIsStruct(mparr))
        {
            mVersion = matGetVariable(pmat, m_version_Text.c_str());
            if (mVersion != NULL)
            {
                version = 1;
                
                if (mxIsStruct(mVersion))
                {
                    mxArray* bwv_version = mxGetField(mVersion, 0, m_version_Text.c_str());
                    if (mxIsChar(bwv_version))
                    {
                        char tmp_string[128] = { 0 };
                        mxGetString(bwv_version, tmp_string, sizeof(tmp_string) - 1);
                        if (tmp_string[0] != 0)
                        {
                            printf("bwv_version = %s\n", tmp_string);
                        }
                    }
                }
                mxDestroyArray(mVersion);
            }

            result = ReadStruct(mparr, pResult, StreamIndex, version);
            if (result != WT_ERR_CODE_OK)
            {
                mxDestroyArray(mparr);
                break;
            }
            descreption = pResult->descreption;
            ptmp = strtok((char *)descreption.c_str(), "\r\n");
            pResult->vsaSourceFalg = FALSE;
            while (ptmp)
            {
                sscanf(ptmp, "%[^:]", desBuff);
                ptmp = ptmp + strlen(desBuff) + 1;      //将指针移到':'下一位
                if (!strcmp(strSampleFreq, desBuff))
                {
                    string tmpCh = ptmp;
                    transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::toupper);
                    bool isMHz = true;
                    if (tmpCh.find("MHZ") == string::npos)
                    {
                        isMHz = false;
                    }
                    sscanf(ptmp, "%lf", &pResult->SampleFreq);
                    if (!isMHz || pResult->SampleFreq >= MHz_API - 1.0)
                    {
                        pResult->SampleFreq /= MHz_API;
                    }
                }
                else if (!strcmp(strCenterFreq, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->dCenterFreq);
                }
                else if (!strcmp(strFreqOffset, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->freqOffset);
                }
                else if (!strcmp(strTriggerLevel, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->triggerLevel);
                }
                else if (!strcmp(strVsaAmpl, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->vsaAmpl);
                }
                else if (!strcmp(strDataType, desBuff))
                {
                    string tmpCh = ptmp;
                    transform(tmpCh.begin(), tmpCh.end(), tmpCh.begin(), ::tolower);

                    pResult->DataType = enDataFormat_Float64;
                    if (0 == strncmp(tmpCh.c_str(), strInt16, strlen(strInt16)))
                    {
                        pResult->DataType = enDataFormat_Int16;
                    }
                }
                else if (!strcmp(strRFGain, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->RFGain);
                    //以信号文件有误RF Gain信息来确定是否为VSA保存的信号
                    pResult->vsaSourceFalg = TRUE;
                }
                else if (!strcmp(strIQGainImb, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->IQGainImb);
                }
                else if (!strcmp(strIQPhaseImb, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->IQPhaseImb);
                }
                else if (!strcmp(strDCOffset_I, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->DC_Offset_I);
                }
                else if (!strcmp(strDCOffset_Q, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->DC_Offset_Q);
                }
                else if (!strcmp(strTime_skew, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->timeSkew);
                }
                else if (!strcmp(strExtAttEnable, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->ExtAttEnable);
                }
                else if (!strcmp(strModType, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->ModType);
                }
                else if (!strcmp(strFlag8080, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->flag8080);
                }
                else if (!strcmp(strClockRate, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->clockRate);
                }
                else if (!strcmp(strScene, desBuff))
                {
                    sscanf(ptmp, "%d", &pResult->sceneMode);
                }
                else if (!strcmp(strExtAtt, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->ExtAtt);
                }
                else if (!strcmp(strEncodingTag, desBuff))
                {
                    sscanf(ptmp, "%d", &Encoding);
                }
                else if (!strcmp(strGap, desBuff))
                {
                    double gap = 0.0;
                    sscanf(ptmp, "%lf", &gap);
                    if (fabs(gap) < 1e-6)
                    {
                        insertDataFlag = true;
                    }
                }
                else if (!strcmp(strIFG, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->PnIfg);
                }
                else if (!strcmp(strRepeat, desBuff))   
                {
                    sscanf(ptmp, "%d", &pResult->Repeat);
                }
                else if (!strcmp(strPnHead, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->PnHead);
                }
                else if (!strcmp(strPnTail, desBuff))
                {
                    sscanf(ptmp, "%lf", &pResult->PnTail);
                }
                ptmp = strtok(NULL, "\r\n");
            }
        }
        mxDestroyArray(mparr);
        // Read IQ complex value
        result = ReadMatComplexIQ(pmat, m_wave_Text[StreamIndex] + "File" + std::to_string(Bwv_PnId) + "_" + to_string(StreamIndex), pResult);
        if (result)
        {
            break;
        }

        if (Encoding)
        {
            result = ReadMatComplexIQ(pmat, m_wave_Text[StreamIndex] + "File" + std::to_string(Bwv_PnId) + "_" + to_string(StreamIndex) + Encrypted_IQ_Part2, pResult, Encoding);
            if (result)
            {
                break;
            }
        }
        result = WT_ERR_CODE_OK;
    } while (0);

    matClose(pmat);

    if (WT_ERR_CODE_OK == result && Encoding)
    {
        WaveFileCommon::Instance().ScramblerData(pResult, false);
    }

    if (WT_ERR_CODE_OK == result && insertDataFlag && (pnMode & PN_VSA_SCENE_MODE))
    {
        WaveFileCommon::Instance().InsertGapData(pResult);
    }
    return result;
}

s32 WaveForm_BWV:: GetPNCountFromFile(const char *fileName, s32 &RetPnCount, int *PnOrder)
{
    s32 result = WT_ERR_CODE_UNKNOW_PARAMETER;
    MATFile *pmat = NULL;
    mxArray *mparr = NULL;
    mxArray *mVersion = NULL;
    char Tempstr[MaxDescLen] = { 0 };
    stPNFileInfo pResult;
    char *ptmp = NULL;
    s32 version = 0;
    char desBuff[1024] = { 0 };
    string descreption;

    pmat = matOpen(fileName, "r");
    if (NULL == pmat)
    {
        return result;
    }

    do
    {
        //读取多PN信号中的PN信息
        mxArray *StreamListInfo = matGetVariable(pmat, INFO2_STREAMLISTINFO_TEXT);   
        if (NULL == StreamListInfo)
        {
            Logger::WriteLog(eumLogType_Error, "%s get bwv %s fail", __FUNCTION__, INFO2_STREAMLISTINFO_TEXT);
            result = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        else if (mxIsStruct(StreamListInfo))
        {
            mxArray* bwv_StreamListInfo = mxGetField(StreamListInfo, 0, INFO2_STREAMLISTINFO_TEXT);
            if (mxIsChar(bwv_StreamListInfo))
            {
                mxGetString(bwv_StreamListInfo, Tempstr, MaxDescLen);
            }
            mxDestroyArray(StreamListInfo);
        }

        ptmp = strtok(Tempstr, "\r\n");
        while (ptmp)
        {
            sscanf(ptmp, "%[^:]", desBuff);
            ptmp = ptmp + strlen(desBuff) + 1;      //将指针移到':'下一位
            if (!strcmp(strPnCount, desBuff))
            {
                sscanf(ptmp, "%d", &RetPnCount);
            }
            ptmp = strtok(NULL, "\r\n");
        }

        memset((void *)PnOrder, 0, RetPnCount * sizeof(int));
        for(int i = 0;i < RetPnCount; i++)
        {
            mparr = matGetVariable(pmat, (m_info2_Text + std::to_string(i)).c_str());
            if (NULL == mparr)
            {
                Logger::WriteLog(eumLogType_Error, "%s get bwv %s fail", __FUNCTION__, (m_info2_Text + std::to_string(i)).c_str());
                result = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            else if (mxIsStruct(mparr))
            {
                mVersion = matGetVariable(pmat, m_version_Text.c_str());
                if (mVersion != NULL)
                {
                    version = 1;
                    
                    if (mxIsStruct(mVersion))
                    {
                        mxArray* bwv_version = mxGetField(mVersion, 0, m_version_Text.c_str());
                        if (mxIsChar(bwv_version))
                        {
                            char tmp_string[128] = { 0 };
                            mxGetString(bwv_version, tmp_string, sizeof(tmp_string) - 1);
                            if (tmp_string[0] != 0)
                            {
                                printf("bwv_version = %s\n", tmp_string);
                            }
                        }
                    }
                    mxDestroyArray(mVersion);
                }
                memset(Tempstr, 0, MaxDescLen);
                mxArray *mmpa;
                mmpa = mxGetField(mparr, 0, INFO2_DESCRIPTION_TEXT);  
                if (mxIsChar(mmpa))
                {
                    mxGetString(mmpa, Tempstr, MaxDescLen);
                }
                if (Tempstr[0] == 0)
                {
                    mxDestroyArray(mparr);
                    break;
                }

                ptmp = strtok(Tempstr, "\r\n");
                while (ptmp)
                {
                    sscanf(ptmp, "%[^:]", desBuff);
                    ptmp = ptmp + strlen(desBuff) + 1;      //将指针移到':'下一位
                    if (!strcmp(strPnIndex, desBuff))
                    {
                        sscanf(ptmp, "%d", &PnOrder[i]);
                    }
                    ptmp = strtok(NULL, "\r\n");
                }
            }
            mxDestroyArray(mparr);
        }
        result = WT_ERR_CODE_OK;
    } while (0);

    matClose(pmat);

    return result;
}
//#endif
