#include "devbusiness.h"

#include <sstream>
#include <iostream>
#include <cmath>
#include <iomanip>

#include <string.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/time.h>
#include <thread>

#include "wterror.h"
#include "wtlog.h"
#include "errorlib.h"
#include "basefun.h"
#include "devdef.h"
#include "ioctlcmd.h"
#include "templib.h"
#include "busiboard.h"
#include "devlib.h"
#include "wtxdma.h"

using namespace std;

#define DDSFsCurrentMin (8640)      //uA
#define DDSFsCurrentMax (31680)     //uA
#define DDSFsCurrentDefault (20000) //uA
#define ModFreqDefault (2412.0)     //MHz

// 无LIC的情况下多次reboot仪器出现环回测试频偏-15KHz，Power底噪呈现曲线变动的问题
// 在有LIC的情况下reboot仪器不会出现问题。WT328、WT318，VB、VF的仪器都存在这个问题。
// 有无LIC的区别在于，无LIC的情况下，仪器开机后不会启动SERVER进程，所以也不会初始化VSA/VSG业务板（主要是因为没初始化LMK），导致出现问题。
// 修改程序，无LIC的情况下启动，也先初始化所有业务板的LMK，连续重启仪器测试，问题解决。不初始化LMK连续重启出问题的原因，需要硬件来解释。
// 对于已经出现出现问题的仪器，关机再重启就可以恢复，不需要完全断电。
int DevBusiness::InitForManager()
{
    int Ret = WT_OK;
    return Ret;
}

int DevBusiness::Init(DevBack *BackBoard)
{
    int Ret = WT_OK;
    Json::Value InitList;
    WTLog::Instance().WriteLog(LOG_DEBUG, "========================DevBusiness::Init\n");
    m_BackBoard = BackBoard;

    BusiInfoInit();
    switch (m_TesterType)
    {
        case HW_WT448:
        case HW_WT428:
            InitList = m_JsonRoot["InitList"]["WT448_and_WT428"]["BUSI"];
            break;
        case HW_WT418:
            InitList = m_JsonRoot["InitList"]["WT418"]["BUSI"];
            break;
        default:;
    }

    if (InitList["UBVoltInfoInit"].asString().c_str()==string("Yes"))
    {
        Ret = UBVoltInfoInit();
    }
    if (InitList["RFVoltInfoInit"].asString().c_str()==string("Yes"))
    {
        Ret |= RFVoltInfoInit();
    }

    Ret |= InitBoard();
    if (!Ret)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (m_BoardType == DEV_TYPE_VSA ? "VSA " : "VSG ") << (m_ModId == 0 ? "MODID[0] " : "MODID[1] ") << "RF Init sucesses!" << std::endl;
    }
    return Ret;
}

int DevBusiness::Release()
{
    if (m_RunData.Status == WT_RX_TX_STATUS_RUNNING)
    {
        Stop();
    }

    Close();
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (m_BoardType == DEV_TYPE_VSA ? WTVSAFILEPATH : WTVSGFILEPATH)
              << m_ModId << " close success!" << std::endl;
    return WT_OK;
}

int DevBusiness::InitBoard()
{
    int Ret = 0;
    ostringstream Stream;
    if (m_BoardType == DEV_TYPE_VSA)
    {
        Stream << WT_VSA_INIT_FILEPATH << m_ModId;
    }
    else
    {
        Stream << WT_VSG_INIT_FILEPATH << m_ModId;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "========================DevBusiness::InitBoard, %d,%d\n", m_BoardType, m_ModId);
#if UB_INIT_ONCE
    //判断内存临时文件是否存在，避免重复初始化基带板
    if (access(Stream.str().c_str(), F_OK) != 0)
    {
        //生成内存临时文件
        int fd = open(Stream.str().c_str(), O_CREAT, 0644);
        close(fd);
        //初始化业务板基带板
        Ret = InitBaseBand();
        RetWarnning(Ret, "InitBaseBand failed!");

        if (m_BoardType == DEV_TYPE_VSG)
        {
            ErrorLib::Instance().CheckErrCode(Ret, WT_VSG_BOARD_BASE_INIT_FAILED);
        }
        else
        {
            ErrorLib::Instance().CheckErrCode(Ret, WT_VSA_BOARD_BASE_INIT_FAILED);
        }
    }
#else
    //初始化业务板基带板
    Ret = InitBaseBand();
#endif

    //启动时，先复位模块FPGA逻辑，防止固件不正常结束时逻辑异常
    Stop();
    if (m_IsLoExist)
    {
        //初始化业务板本振板
        Ret = InitLO();
    }

    if (m_IsRfExist)
    {
        //初始化业务板射频板
        Ret = InitRF();
    }

    SetListModeStatus(0);
    //开启保存Dma协议包的功能
    int Value = 0;
    DevConf::Instance().GetItemVal("SaveListModeDmaData", Value, 0);
    std::cout<<"SaveListModeDmaData = "<<Value<<std::endl;
    if(Value)
    {
        m_SaveDmaDataFlag = true;
    }
    else
    {
        m_SaveDmaDataFlag = false;
    }

    return Ret;
}

int DevBusiness::InitBaseBand()
{
    int Ret = WT_OK;
    Json::Value InitList;
    switch (m_TesterType)
    {
        case HW_WT448:
        case HW_WT428:
            InitList = m_JsonRoot["InitList"]["WT448_and_WT428"]["BUSI"];
            break;
        case HW_WT418:
            InitList = m_JsonRoot["InitList"]["WT418"]["BUSI"];
            break;
        default:;
    }

    if (m_BoardType == DEV_TYPE_VSA)
    {
        if (InitList["HMC7044Init"].asString().c_str()==string("Yes"))
        {
            //基带板时钟芯片
            WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitBaseBand HMC7044Init=%d,%d\n", m_BoardType, m_ModId);
            Ret = HMC7044Init();
            RetWarnning(Ret, "HMC7044Init failed!");
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
        }

        Ret = ResetFpga();
        RetWarnning(Ret, "ResetFpga failed!");
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
    }

#if WT_ADCONVST
    if (InitList["BusiAD7091Init"].asString().c_str()==string("Yes"))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitBaseBand BusiAD7091Init=%d,%d\n", m_BoardType, m_ModId);
        //初始化RF AD7091电压检测模块
        Ret = BusiAD7091Init();
        RetWarnning(Ret, "BusiAD7091Init failed!");
        ErrorLib::Instance().CheckErrCode(Ret, WT_RFBOARD_AD7091_INIT_FAILED);
    }

#endif

    if (InitList["InitBaseBandMod"].asString().c_str()==string("Yes"))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitBaseBand InitBaseBandMod=%d,%d\n", m_BoardType, m_ModId);
        Ret = InitBaseBandMod();
        RetWarnning(Ret, "InitBaseBandMod failed!");

    }
     ErrorLib::Instance().CheckErrCode(Ret, Ret);
    return Ret;
}

int DevBusiness::InitLO()
{
    int Ret = WT_OK;
    Json::Value InitList;
    switch (m_TesterType)
    {
        case HW_WT448:
        case HW_WT428:
            InitList = m_JsonRoot["InitList"]["WT448_and_WT428"]["BUSI"];
            break;
        case HW_WT418:
            InitList = m_JsonRoot["InitList"]["WT418"]["BUSI"];
            break;
        default:;
    }
    if (m_BoardType == DEV_TYPE_VSA)
    {
        if (InitList["ADF4106Init"].asString().c_str()==string("Yes"))
        {
            if (m_HwInfo.LoHwVersion >= VERSION_B)
            {
                ; //VERSION_B删除ADF4106
            }
            else
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitLO ADF4106Init=%d,%d\n", m_BoardType, m_ModId);
                Ret = ADF4106Init();
                RetWarnning(Ret, "ADF4106Init failed!");
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
            }
        }

        if (InitList["LOMODLOCKDETECT"].asString().c_str()==string("Yes"))
        {
            int Value;
            WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitLO LOMODLOCKDETECT=%d,%d\n", m_BoardType, m_ModId);
            ReadDirectReg(LOMODLOCKDETECT, Value);
            Ret = (Value & HMC1301_BIT) ? WT_OK : WT_HMC1031_IS_UNLOCKED;
            RetWarnning(Ret, "HMC1031 IS UNLOCKED!");
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
        }

    }
    if (InitList["DDSInit"].asString().c_str()==string("Yes"))
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitLO DDSInit=%d,%d\n", m_BoardType, m_ModId);
        Ret = DDSInit();
        RetWarnning(Ret, "DDSInit failed!");
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
    }

    if (InitList["LoModInit"].asString().c_str()==string("Yes"))
    {
        //初始化LoBoard
        WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitLO LoBoardInit=%d,%d\n", m_BoardType, m_ModId);
        Ret = LoModInit();
        RetWarnning(Ret, "RFBoardInit LoModInit failed!");
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
    }

    if (InitList["LoMixInit"].asString().c_str()==string("Yes"))
    {
        //初始化Mix本振
        WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitLO LoMixInit=%d,%d\n", m_BoardType, m_ModId);
        Ret = LoMixInit();
        RetWarnning(Ret, "RFBoardInit LoMixInit failed!");
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
    }

    if (InitList["LOComModeInit"].asString().c_str()==string("Yes"))
    {
        if (m_TesterType == HW_WT418||m_HwInfo.LoHwVersion >= VERSION_D)
        {
            //初始化共本振模式
            WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitLO ComMode=%d,%d\n", m_BoardType, m_ModId);
            Ret = LOComModeInit();
            RetWarnning(Ret, "RFBoardInit InitLOComMode failed!");
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
        }
    }

    return WT_OK;
}

int DevBusiness::InitRF()
{
    int Ret = WT_OK;
    WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitRF m_BoardType=%d, m_Mod=%d, Ret=%d\n", m_BoardType, m_ModId, Ret);
    Json::Value InitList;
    switch (m_TesterType)
    {
        case HW_WT448:
        case HW_WT428:
            InitList = m_JsonRoot["InitList"]["WT448_and_WT428"]["BUSI"];
            break;
        case HW_WT418:
            InitList = m_JsonRoot["InitList"]["WT418"]["BUSI"];
            break;
        default:;
    }
    if (InitList["RfModLoFreqList"].asString().c_str()==string("Yes"))
    {
        Json::Value &RfModLo = (m_FpgaInfo.TesterType == HW_WT418) ? m_JsonRoot["RfModLoFreq_Channel_418VA"] :\
        ((m_HwInfo.LoHwVersion >= VERSION_B) ? m_JsonRoot["RfModLoFreq_Channel"] : m_JsonRoot["RfModLoFreq_Channel_VA"]);
        Json::Value &RfModLoFreq = m_BoardType == DEV_TYPE_VSA ? RfModLo["VSA"] : RfModLo["VSG"];
        if (RfModLoFreq.isArray())
        {
            m_RfModLoFreqChannel.clear();
            FreqSection FreqS;
            for (unsigned i = 0; i < RfModLoFreq.size(); i++)
            {
                FreqS.StartFreq = std::strtol(RfModLoFreq[i]["StartFreq"].asString().c_str(), 0, 0);
                FreqS.EndFreq = std::strtol(RfModLoFreq[i]["EndFreq"].asString().c_str(), 0, 0);
                FreqS.Index = i;
                m_RfModLoFreqChannel.push_back(FreqS);
            }
        }
    }

    if (InitList["RfMixLoFreqList"].asString().c_str()==string("Yes"))
    {
        Json::Value &RfMixLo = (m_FpgaInfo.TesterType == HW_WT418) ? m_JsonRoot["RfMixLoFreq_Channel_418VA"] :\
        ((m_HwInfo.LoHwVersion >= VERSION_B) ? m_JsonRoot["RfMixLoFreq_Channel"] : m_JsonRoot["RfMixLoFreq_Channel_VA"]);
        Json::Value &RfMixLoFreq = m_BoardType == DEV_TYPE_VSA ? RfMixLo["VSA"] : RfMixLo["VSG"];
        if (RfMixLoFreq.isArray())
        {
            m_RfMixLoFreqChannel.clear();
            FreqSection FreqS;
            for (unsigned i = 0; i < RfMixLoFreq.size(); i++)
            {
                FreqS.StartFreq = std::strtol(RfMixLoFreq[i]["StartFreq"].asString().c_str(), 0, 0);
                FreqS.EndFreq = std::strtol(RfMixLoFreq[i]["EndFreq"].asString().c_str(), 0, 0);
                FreqS.Index = i;
                m_RfMixLoFreqChannel.push_back(FreqS);
            }
        }
    }

    if (InitList["SetATTCode"].asString().c_str()==string("Yes"))
    {
        Json::Value &RF_ATT_Init = m_BoardType == DEV_TYPE_VSA ? m_JsonRoot["RF_ATT_Init"][m_TesterType]["ATT_Init"]["VSA"] : m_JsonRoot["RF_ATT_Init"][m_TesterType]["ATT_Init"]["VSG"];
        //初始化射频板衰减器
        for (int i = 0; i < RX_ATT_MAX; i++)
        {
            Ret = SetATTCode(i, std::strtol(RF_ATT_Init[i]["Code"].asString().c_str(), 0, 0));
            RetWarnning(Ret, "InitRF SetATTCode failed!");
            m_RunData.ATTCurData[i] = (Ret == WT_OK) ? std::strtol(RF_ATT_Init[i]["Code"].asString().c_str(), 0, 0) : -1;
        }
    }

    if (InitList["GetAD7682ChannelCode"].asString().c_str()==string("Yes"))
    {
        // 7682 8318器件初始化，每个端口分别读几次CODE.
        int PowerCode = 0;
        for (int times = 0; times < AD7682_INIT_READ_CNT; ++times)
        {
            for (int Channel = 0; Channel < BUSI_AD7682_CHANNEL_MAX; Channel++)
            {
                Ret = GetAD7682ChannelCode(Channel, PowerCode);
                RetWarnning(Ret, "InitRF GetAD7682ChannelCode failed!");
            }
        }
    }

    if (InitList["GetAD7689ChannelCode"].asString().c_str()==string("Yes"))
    {
        // 7682 8318器件初始化，每个端口分别读几次CODE.
        int PowerCode = 0;
        for (int times = 0; times < AD7689_INIT_READ_CNT; ++times)
        {
            for (int Channel = 0; Channel < BUSI_AD7689_CHANL_SEL_MAX; Channel++)
            {
                Ret = GetAD7689ChannelCode(Channel, PowerCode);
                RetWarnning(Ret, "InitRF GetAD7689ChannelCode failed!");
            }
        }
    }

    m_RunData.PaStatus = -1;
    return WT_OK;
}

int DevBusiness::ResetFpga()
{
    //复位基带板
    WriteDirectReg(BASE_FPGA_SYM_RESET, SYM_RESET_MASK);
    WriteDirectReg(BASE_FPGA_SYM_RESET, 0);
    return WT_OK;
}

int DevBusiness::BusiInfoInit()
{
    int Ret = WT_OK;

    BusiBoardFpgaInfo FpgaInfoTemp;
    memset(&FpgaInfoTemp, 0, sizeof(BusiBoardFpgaInfo));
    if ((Ret = DrvCmd(GET_BB_BOARD_INFO, sizeof(BusiBoardFpgaInfo), &FpgaInfoTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_BB_BOARD_INFO ioctl error");
        return Ret;
    }
    m_FpgaInfo = FpgaInfoTemp;
    m_TesterType = m_FpgaInfo.TesterType;
    
    if ((Ret = DrvCmd(GET_BUSI_BOARD_SLOT, sizeof(m_Slot), &m_Slot)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_BB_BOARD_INFO ioctl error");
        return Ret;
    }

    m_HwInfo = m_BackBoard->GetBusiHwVersion(m_Slot);
    if(m_TesterType == HW_WT418)
    {
        m_IsRfExist = (m_HwInfo.RfHwVersion & 0x7) != 0x7;
        m_IsLoExist = m_IsRfExist;
    }
    else if (m_HwInfo.BBHwVersion >= VERSION_B)
    {
        m_IsRfExist = m_HwInfo.IsRfExist();
        m_IsLoExist = m_HwInfo.IsLoExist();
    }
    else
    {
#define LTC5594_CHIPID_ADDR 0X17
        int LTC5594ChipId = 0;
        ReadLTC5594(LTC5594_CHIPID_ADDR, LTC5594ChipId);
        if (LTC5594ChipId == 0x1)
        {
            m_IsLoExist = m_IsRfExist = true;
        }
        else
        {
            m_IsLoExist = m_IsRfExist = false;
        }
    }
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::hex
              << PoutN(FpgaInfoTemp.FpgaVersion) << PoutN(FpgaInfoTemp.CompileDate) << PoutN(FpgaInfoTemp.CompileTime)
              << PoutN(FpgaInfoTemp.ModuleVersion) << PoutN(FpgaInfoTemp.PllLockDet) << PoutN(FpgaInfoTemp.LoLockDet)
              << PoutN(m_HwInfo.BBHwVersion) << PoutN(m_HwInfo.RfHwVersion) << PoutN(m_HwInfo.LoHwVersion)
              << PoutN(m_IsRfExist)
              << PoutN(m_IsLoExist)
              << std::endl
              << std::dec;
    return Ret;
}

int DevBusiness::GetCompleteClrStatus(int &Status)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(GET_COMPLETE_CLR_STATUS, sizeof(int), &Status)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_COMPLETE_CLR_STATUS DrvCmd error");
        return Ret;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetCompleteClrStatus Type%d Mod%d = %#x\n", m_BoardType, m_ModId, Status);
    return WT_OK;
}

int DevBusiness::SetIQSwitch(int IQSwitch)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(SET_IQ_SWITCH, sizeof(int), &IQSwitch)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_IQ_SWITCH DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::BusiStart()
{
    return Start();
}

int DevBusiness::BusiStop()
{
    return Stop();
}

int DevBusiness::BusiDown()
{
    return Down();
}

//设置业务板工作模式（主从模式/SISO/MIMO）
int DevBusiness::SetUnitModWorkMode(WT_DEVICE_MODE WorkMode)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(SET_DEVICE_WORK_MODE, sizeof(WT_DEVICE_MODE), &WorkMode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_DEVICE_WORK_MODE DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

//AD5611
int DevBusiness::WriteAD5611(int DevId, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = DevId;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_AD5611, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_AD5611 DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadAD5611(int DevId, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = DevId;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_AD5611, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_AD5611 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::WriteAD7682(int Addr, int Data)
{
    (void)Addr;
    (void)Data;
    return WT_OK;
}

int DevBusiness::ReadAD7682(int Addr, int &Data)
{
    (void)Addr;
    (void)Data;
    return WT_OK;
}

int DevBusiness::GetAD7682ChannelCode(int Channel, int &Code)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Channel;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(GET_BB_AD7682_CHANNEL, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_BB_AD7682_CHANNEL DrvCmd error");
        return Ret;
    }
    Code = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::GetAD7682ChannelVolt(int Channel, double &VoltValue)
{
    int Value = 0;
    GetAD7682ChannelCode(Channel, Value);
    VoltValue = ((double)Value / 65535) * 2.5;
    return WT_OK;
}

int DevBusiness::GetAD7689ChannelCode(int Channel, int &Code)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Channel;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(GET_SW_AD7689_CHANNEL, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_BB_AD7682_CHANNEL DrvCmd error");
        return Ret;
    }
    Code = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::GetAD7689ChannelVolt(int Channel, double &VoltValue)
{
    int Value = 0;
    GetAD7689ChannelCode(Channel, Value);
    VoltValue = ((double)Value / 65535) * 2.5;
    return WT_OK;
}

int DevBusiness::GetAD7682ChannelTemperature(int Channel, double &Data)
{
    if (m_BoardType == DEV_TYPE_VSG &&
        Channel != BUSI_VB_TX_AD7682_MIX_LO_DET_TEMP &&
        Channel != BUSI_VB_TX_AD7682_MOD_LO_DET_TEMP)
    {
        return WT_ARG_ERROR;
    }

    if (m_BoardType == DEV_TYPE_VSA &&
        Channel != BUSI_VB_RX_AD7682_MIX_LO_DET_TEMP &&
        Channel != BUSI_VB_RX_AD7682_MOD_LO_DET_TEMP &&
        Channel != BUSI_VB_RX_AD7682_EXT_DEM_TEMP)
    {
        return WT_ARG_ERROR;
    }

    double Value = 0;
    GetAD7682ChannelVolt(Channel, Value);
    GetAD7682ChannelVolt(Channel, Value);
    GetAD7682ChannelVolt(Channel, Value);
    GetAD7682ChannelVolt(Channel, Value);

    if (Channel == BUSI_VB_RX_AD7682_EXT_DEM_TEMP)
    {
        Data = (Value * 1000 - 774) / (-1.52) + 25.0;
    }
    else
    {
        Data = (Value * 1000 - 600) / 2 + 27.0;
    }
    //WTLog::Instance().WriteLog(LOG_DEBUG, "GetAD7682ChannelTemperature volt=%lf, temp=%lf\n", Value, Data);
    return WT_OK;
}

int DevBusiness::GetRFAllVoltValue()
{
    for (auto &Iter : m_RFVoltInfoVec)
    {
        GetAD7682ChannelVolt(Iter.VoltChannel, Iter.VoltValue);
        GetAD7682ChannelVolt(Iter.VoltChannel, Iter.VoltValue);
        GetAD7682ChannelVolt(Iter.VoltChannel, Iter.VoltValue);
        GetAD7682ChannelVolt(Iter.VoltChannel, Iter.VoltValue);
    }
    return WT_OK;
}

//ADF4106 PLL晶振
int DevBusiness::ADF4106Init(void)
{
    int Ret = WT_OK;
    Json::Value JsonReg = m_JsonRoot["LO_ADF4106"];
    int Data = 0;

    if (JsonReg.isArray())
    {
        for (unsigned i = 0; i < JsonReg.size(); i++)
        {
            Data = std::strtol(JsonReg[i]["Data"].asString().c_str(), 0, 0);
            Ret = WriteADF4106(0, Data);
            RetAssert(Ret, "WriteADF4106 failed!");
        }
    }
    
    return Ret;
}

int DevBusiness::WriteADF4106(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_ADF4106, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_ADF4106 DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadADF4106(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_ADF4106, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_ADF4106 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

//HMC7044
int DevBusiness::HMC7044Init()
{
    int Ret = WT_OK;
    Json::Value DevId = m_JsonRoot["BB_HMC7044"];
    int Delay = 0;
    int Addr = 0;
    int Data = 0;

    if (DevId.isArray())
    {
        for (unsigned j = 0; j < DevId.size(); j++)
        {
            Addr = std::strtol(DevId[j]["Addr"].asString().c_str(), 0, 0);
            Data = std::strtol(DevId[j]["Data"].asString().c_str(), 0, 0);
            Delay = std::strtol(DevId[j]["Delay"].asString().c_str(), 0, 0);
            Ret = WriteHM7044(Addr, Data);
            RetAssert(Ret, "WriteHM7044!");
            // WTLog::Instance().WriteLog(LOG_DEBUG, "HMC7044Init WriteHM7044 Addr = %#x, Data=%#x\n", Addr, Data);
            if (Delay != 0)
            {
                usleep(Delay);
                WTLog::Instance().WriteLog(LOG_DEBUG, "HMC7044Init Delay %d us\n", Delay);
            }
        }
    }
    return Ret;
}

int DevBusiness::WriteHM7044(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_HM7044, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_HM7044 DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadHM7044(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_HM7044, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_HM7044 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::WriteLTC5594(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_LTC5594, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_LTC5594 DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadLTC5594(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_LTC5594, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_LTC5594 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}
int DevBusiness::CheckLMX2594(int Addr, int Data)
{
    if(Addr == 0x0)
    {
        return WT_OK;
    }
    RegType RegTemp1,RegTemp2,RegTemp;
    RegTemp1.Addr = 0x0;
    RegTemp1.Data = 0x2518;
    RegTemp.Addr = Addr;
    RegTemp.Data = -1;
    RegTemp2.Addr = 0x0;
    RegTemp2.Data = 0x251C;
    do
    {
        if (DrvCmd(WRITE_BB_LMX2594, sizeof(RegTemp1), &RegTemp1) != WT_OK)
        {
            break;
        }
        if (DrvCmd(READ_BB_LMX2594, sizeof(RegTemp), &RegTemp) != WT_OK)
        {
            break;
        }
        if (DrvCmd(WRITE_BB_LMX2594, sizeof(RegTemp2), &RegTemp2) != WT_OK)
        {
            break;
        }
        if(Data != RegTemp.Data)
        {
            break;
        }
        return WT_OK;
    } while (0);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Addr = %#x(%d),write Data = %#x(%d),Read Data = %#x(%d) \n",Addr,Addr,Data,Data,RegTemp.Data,RegTemp.Data);
    return WT_OK;
}

int DevBusiness::WriteLMX2594(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;
    //WTLog::Instance().WriteLog(LOG_DEBUG, "======================================%s WriteLMX2594 Addr = %#x(%d), Data=%#x======================================\n", (m_BoardType == DEV_TYPE_VSA ? "VSA" : "VSG"), Addr, Addr, Data);
    if ((Ret = DrvCmd(WRITE_BB_LMX2594, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_LMX2594 DrvCmd error");
        return Ret;
    }

#if 0
    //读写对比
    if ((Ret = CheckLMX2594(Addr,Data)) != WT_OK)
    {
        return Ret;
    };
#else
    usleep(10);
#endif
// TODO:FENG:临时添加，暂时不知道不锁定的原因
    if(RegTemp.Addr != 0)
    {
        RegTemp.Addr = 0x0;
        RegTemp.Data = 0x251C;
        DrvCmd(WRITE_BB_LMX2594, sizeof(RegTemp), &RegTemp);
    }
    return WT_OK;
}

int DevBusiness::ReadLMX2594(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_LMX2594, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_LMX2594 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::WriteLMX2820(LO_ID_E LOId, int Addr, int Data)
{
    int Ret = WT_OK;
    DeviceConfig DeviceConfigTemp;
    DeviceConfigTemp.DeviceId = LOId;
    DeviceConfigTemp.RegTypeData.Addr = Addr;
    DeviceConfigTemp.RegTypeData.Data = Data;
    //WTLog::Instance().WriteLog(LOG_DEBUG, "%s WriteLMX2820 Id = %d, Addr = %#x, Data=%#x\n", (m_BoardType == DEV_TYPE_VSA ? "VSA" : "VSG"),LOId, Addr, Data);

    if ((Ret = DrvCmd(WRITE_BB_LMX2820, sizeof(DeviceConfigTemp), &DeviceConfigTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_LMX2820 DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadLMX2820(LO_ID_E LOId, int Addr, int &Data)
{
    int Ret = WT_OK;
    DeviceConfig DeviceConfigTemp;
    DeviceConfigTemp.DeviceId = LOId;
    DeviceConfigTemp.RegTypeData.Addr = Addr;
    DeviceConfigTemp.RegTypeData.Data = 0;

    if ((Ret = DrvCmd(READ_BB_LMX2820, sizeof(DeviceConfigTemp), &DeviceConfigTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_LMX2820 DrvCmd error");
        return Ret;
    }
    Data = DeviceConfigTemp.RegTypeData.Data;
    return WT_OK;
}

int DevBusiness::CheckHMC833Reg(int Addr, int Data)
{
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = -1;
    do
    {
        if (DrvCmd(READ_BB_HMC833, sizeof(RegTemp), &RegTemp) != WT_OK)
        {
            break;
        }
        if (Data != RegTemp.Data)
        {
            break;
        }
        return WT_OK;
    } while (0);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Addr = %#x(%d),write Data = %#x(%d),Read Data = %#x(%d) \n", Addr, Addr, Data, Data, RegTemp.Data, RegTemp.Data);
    return WT_OK;
}

int DevBusiness::WriteHMC833Reg(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;
    // WTLog::Instance().WriteLog(LOG_DEBUG, "======================================%s WriteHMC833Reg Addr = %#x, Data=%#x======================================\n", (m_BoardType == DEV_TYPE_VSA ? "VSA" : "VSG"), Addr, Data);

    if ((Ret = DrvCmd(WRITE_BB_HMC833, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "DEV_CMD_BB_HMC833 DrvCmd error");
        return Ret;
    }
#if 0
    //读写对比
    if ((Ret = CheckHMC833Reg(Addr,Data)) != WT_OK)
    {
        return Ret;
    };
#else
    usleep(10);
#endif
    return WT_OK;
}

int DevBusiness::ReadHMC833Reg(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_HMC833, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "DEV_CMD_BB_HMC833 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::WriteFLASH(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_FLASH, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_FLASH DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadFLASH(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_FLASH, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_FLASH DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;

    return WT_OK;
}

//RF Board Volt
int DevBusiness::BusiAD7091Init()
{
    int Ret = WT_OK;
    int VoltData;
    for (int i = 0; i < 66; i++)
    {
        ReadAD7091(0x0, VoltData);
    }

    //初始化RF AD7091电压检测模块
    Ret = WriteAD7091(0x2, 0x2C0); //复位
    RetWarnning(Ret, "RFBoardInit WriteAD7091 Reset failed!");
    usleep(100);
    Ret |= WriteAD7091(0x2, 0x91); //初始化配置
    RetWarnning(Ret, "RFBoardInit WriteAD7091 Config failed!");

    return Ret;
}

int DevBusiness::WriteAD7091(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_AD7091, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_AD7091 DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadAD7091(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_AD7091, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_AD7091 DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;

    return WT_OK;
}

int DevBusiness::GetBBVoltValue(int Channel, double &VoltValue)
{
    int Ret = WT_OK;
    RegType RegTypeTemp;
    RegTypeTemp.Addr = Channel;
    RegTypeTemp.Data = 0;

    if ((Ret = DrvCmd(GET_BB_VOLTAGE, sizeof(RegType), &RegTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_RF_VOLTAGE_VALUE ioctl error");
        return Ret;
    }

    if ((RegTypeTemp.Data >> 13) != Channel)
    {
        Ret = WT_GET_VOLTAGE_ERR;
        std::stringstream ErrMsg;
        ErrMsg << "UB" << m_BoardType << ", ModId" << m_ModId
               << ", GetBBVoltValue channel=" << Channel
               << ", return channel=" << (RegTypeTemp.Data >> 13);
        WTLog::Instance().LOGERR(WT_GET_VOLTAGE_ERR, ErrMsg.str());
        VoltValue = -999.0;
    }
    else
    {
        VoltValue = ((RegTypeTemp.Data & 0xFFF) * 2.5) / 4096.0;
    }
    
    return Ret;
}

int DevBusiness::GetBBAllVoltValue()
{
    int Ret = WT_OK;
    int VoltData;

    Ret = WriteAD7091(0x1, 0xFF);
    RetAssert(Ret, "WriteAD7091 failed!");

    //去掉第一次读取的无效值
    ReadAD7091(0x0, VoltData);

    for (int i = 0; i < m_UBVoltInfoVec.size(); i++)
    {
        Ret = ReadAD7091(0, VoltData);
        if (Ret != WT_OK || (VoltData >> 13) != m_UBVoltInfoVec[i].VoltChannel)
        {
            Ret = WT_GET_VOLTAGE_ERR;
            std::stringstream ErrMsg;
            ErrMsg << "Busi ModId" << m_ModId
                   << ", ReadVoltValue channel=" << m_UBVoltInfoVec[i].VoltChannel
                   << ", return channel=" << (VoltData >> 13);
            WTLog::Instance().LOGERR(WT_GET_VOLTAGE_ERR, ErrMsg.str());
            m_UBVoltInfoVec[i].VoltValue = -999.0;
        }
        else
        {
            m_UBVoltInfoVec[i].VoltValue = ((VoltData & 0xFFF) * 2.5) / 4096.0;
        }
    }
    return Ret;
}

//DDS AD9912
int DevBusiness::DDSInit()
{
    int Ret = WT_OK;
    Json::Value TypeM = m_JsonRoot["LO_DDS_AD9912"]["TypeM"];
    Json::Value Reg = m_JsonRoot["LO_DDS_AD9912"]["Init"];
    int Addr = 0;
    int Data = 0;

    //先记录9912的所有TYPE M寄存器
    if (TypeM.isArray())
    {
        for (unsigned j = 0; j < TypeM.size(); j++)
        {
            Addr = std::strtol(TypeM[j]["Addr"].asString().c_str(), 0, 0);
            m_DDSAddrTypeM.push_back(Addr);
        }
    }

    if (Reg.isArray())
    {
        for (unsigned j = 0; j < Reg.size(); j++)
        {
            Addr = std::strtol(Reg[j]["Addr"].asString().c_str(), 0, 0);
            Data = std::strtol(Reg[j]["Data"].asString().c_str(), 0, 0);
            Ret = WriteDDS(Addr, Data);
            RetAssert(Ret, "WriteDDS!");
        }
    }
    return Ret;
}

int DevBusiness::WriteDDS(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_LO_DDS, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WriteDDS DrvCmd error");
        return Ret;
    }

    //写完地址类型为TYPE M的寄存器后，都要将05寄存器写1，以使TYPE M寄存器生效
    if (Addr != 0x5)
    {
        //遍历TYPE M寄存器时先排除05寄存器，以免陷入无限递归
        for (auto AddrM : m_DDSAddrTypeM)
        {
            if (AddrM == Addr)
            {
                WriteDDS(0x5, 1);
                break;
            }
        }
    }
    return WT_OK;
}

int DevBusiness::ReadDDS(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_LO_DDS, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_LO_DDS DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::SetDDSChannel(unsigned long long Data)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(SET_LO_DDS_FREQ_CHANNEL, sizeof(Data), &Data)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_LO_DDS_FREQ_CHANNEL DrvCmd error");
        return Ret;
    }
    return Ret;
}

int DevBusiness::SetDDSFreq(double Freq /*MHz*/)
{
    int Ret = WT_OK;
    int Fs = 1000;

    unsigned long long FreqInt = m_HwInfo.LoHwVersion >= VERSION_B
                                     ? m_RunData.ModLoRefSel
                                     : (unsigned long long)Freq * MHz;

    long long FTW = 0;
    switch (m_RunData.ModLoRefSel)
    {
    case REF_DDS_700M:
        Fs = 700;
        break;
    case REF_DDS_900M:
        Fs = 900;
        break;
    case REF_VA_DDS_1000M:
        Fs = 1000;
        break;  
    default:
        Fs = 0;
        break;
    }
    if (Fs != 0)
    {
        FTW = round(pow(2, 48) * ((double)Freq / Fs));
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetDDSFreq Freq=%lf MHz, FTW=%lld\n", Freq, FTW);
    
    Ret = WriteDDS(BUSI_DDS_FTW_BIT0_7, (FTW >> 0) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");
    Ret = WriteDDS(BUSI_DDS_FTW_BIT8_15, (FTW >> 8) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");
    Ret = WriteDDS(BUSI_DDS_FTW_BIT16_23, (FTW >> 16) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");
    Ret = WriteDDS(BUSI_DDS_FTW_BIT24_31, (FTW >> 24) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");
    Ret = WriteDDS(BUSI_DDS_FTW_BIT32_39, (FTW >> 32) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");
    Ret = WriteDDS(BUSI_DDS_FTW_BIT40_47, (FTW >> 40) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");

    return SetDDSChannel(FreqInt);
}

int DevBusiness::SetDDSFsCurrent(unsigned int DacFsCurrent)
{
    int Ret = WT_OK;

    if (DacFsCurrent < DDSFsCurrentMin || DacFsCurrent > DDSFsCurrentMax)
    {
        DacFsCurrent = DDSFsCurrentDefault;
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "DacFsCurrent out of range. Set the default value 20mA");
    }

    int DacRef = 120;
    int FSC = (DacFsCurrent / DacRef - 72) * 1024 / 192;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetDDSFsCurrent DacRef=%d, DacFs=%d, FSC=%d\n", DacRef, DacFsCurrent, FSC);

    WriteDDS(BUSI_DDS_FSC_BIT0_7, (FSC >> 0) & 0xFF);
    RetWarnning(Ret, "WriteDDS failed!");
    WriteDDS(BUSI_DDS_FSC_BIT8_9, (FSC >> 8) & 0x3);
    RetWarnning(Ret, "WriteDDS failed!");
    return Ret;
}

int DevBusiness::WriteATTAndShift(int ChipId, int Addr, int Data)
{
    int Ret = WT_OK;
    DeviceConfig DevTemp;
    DevTemp.DeviceId = ChipId;
    DevTemp.RegTypeData.Addr = Addr;
    DevTemp.RegTypeData.Data = Data;

    if ((Ret = DrvCmd(WRITE_RF_ATT_SHIFT, sizeof(DevTemp), &DevTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_RF_ATT_SHIFT DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadATTAndShift(int ChipId, int Addr, int &Data)
{
    int Ret = WT_OK;
    DeviceConfig DevTemp;
    DevTemp.DeviceId = ChipId;
    DevTemp.RegTypeData.Addr = Addr;
    DevTemp.RegTypeData.Data = 0;

    if ((Ret = DrvCmd(READ_RF_ATT_SHIFT, sizeof(DevTemp), &DevTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_RF_ATT_SHIFT DrvCmd error");
        return Ret;
    }
    Data = DevTemp.RegTypeData.Data;

    return WT_OK;
}

//Lo Shift
int DevBusiness::WriteLoShift(long long Data)
{
    int Ret = WT_OK;
    Reg64Type RegTemp;
    RegTemp.Addr = 0;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_BB_LO_SHIFT, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BB_LO_SHIFT DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadLoShift(long long &Data)
{
    int Ret = WT_OK;
    Reg64Type RegTemp;
    RegTemp.Addr = 0;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_BB_LO_SHIFT, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_BB_LO_SHIFT DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

//Lo HMC705
int DevBusiness::SetLoHMC705(int Data)
{
    int Ret = WT_OK;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetLoHMC705 DivRatioN=%d\n", Data);
    if ((Ret = DrvCmd(SET_LO_SHIFT_HMC705, sizeof(int), &Data)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_LO_SHIFT_HMC705 DrvCmd error");
        return Ret;
    }
    return WT_OK;
}

//Lo Loop Filter
int DevBusiness::SetLoLoopFilter(int Data)
{
    int Ret = WT_OK;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetLoLoopFilter=%d\n", Data);
    if ((Ret = DrvCmd(SET_LO_SHIFT_LOOP_FILTER, sizeof(int), &Data)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_LO_SHIFT_LOOP_FILTER DrvCmd error");
        return Ret;
    }
    return WT_OK;
}

//Lo Freq Channel
int DevBusiness::SetLoFreqChannel(int Data)
{
    int Ret = WT_OK;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetLoFreqChannel=%d\n", Data);
    if ((Ret = DrvCmd(SET_LO_SHIFT_FREQ_CHANNEL, sizeof(int), &Data)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_LO_SHIFT_FREQ_CHANNEL DrvCmd error");
        return Ret;
    }
    return WT_OK;
}

int DevBusiness::WriteAdcDac(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = Data;

    if ((Ret = DrvCmd(WRITE_RF_ADC_DAC, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_RF_ADC_DAC DrvCmd error");
        return Ret;
    }
    return WT_OK;
}

int DevBusiness::ReadAdcDac(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTemp;
    RegTemp.Addr = Addr;
    RegTemp.Data = 0;

    if ((Ret = DrvCmd(READ_RF_ADC_DAC, sizeof(RegTemp), &RegTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_RF_ADC_DAC DrvCmd error");
        return Ret;
    }
    Data = RegTemp.Data;
    return WT_OK;
}

int DevBusiness::GetRFTemperature(int TempId, double &TempValue)
{
    (void)TempId;
    TempValue = 30.0;
    return WT_OK;
}

int DevBusiness::GetRFTemperatureAverage(double &TempAverage)
{
    return GetRFTemperature(0, TempAverage);
}

int DevBusiness::GetRFAvgTemperature(double &TempValue)
{
    return GetRFTemperature(0, TempValue);
}

//射频板AD7682电压侦测
int DevBusiness::RFVoltInfoInit(void)
{
    VoltInfoType RFVoltInfoTemp;
    std::string Board = (m_BoardType == DEV_TYPE_VSA ? "RX RF" : "TX RF");
    Json::Value Reg;

    if (m_HwInfo.BBHwVersion >= VERSION_B)
    {
        Reg = m_BoardType == DEV_TYPE_VSA ? m_JsonRoot["RFVoltInfo_VB"]["VSA"] : m_JsonRoot["RFVoltInfo_VB"]["VSG"];
    }
    else
    {
        Reg = m_BoardType == DEV_TYPE_VSA ? m_JsonRoot["RFVoltInfo"]["VSA"] : m_JsonRoot["RFVoltInfo"]["VSG"];
    }

    if (Reg.isArray())
    {
        for (unsigned i = 0; i < Reg.size(); i++) //电压通道数量
        {
            RFVoltInfoTemp.VoltChannel = std::strtol(Reg[i]["VoltChannel"].asString().c_str(), 0, 0);
            //实际射频板电压通道从AD7682_EXT开始
            RFVoltInfoTemp.VoltChannel += (m_BoardType == DEV_TYPE_VSA) ? (int)BUSI_RX_AD7682_MAX : (int)BUSI_TX_AD7682_MAX;
            RFVoltInfoTemp.MultVolt = std::strtol(Reg[i]["MultVolt"].asString().c_str(), 0, 0);
            STRNCPY_USER(RFVoltInfoTemp.Board, Board.c_str());
            STRNCPY_USER(RFVoltInfoTemp.VoltChannelInfo, Reg[i]["VoltChannelInfo"].asString().c_str());
            RFVoltInfoTemp.VoltValue = -999.0;
            RFVoltInfoTemp.LowLimit = std::strtod(Reg[i]["LowLimit"].asString().c_str(), NULL);
            RFVoltInfoTemp.HighLimit = std::strtod(Reg[i]["HighLimit"].asString().c_str(), NULL);

            if (RFVoltInfoTemp.MultVolt != 0)
            {
                m_RFVoltInfoVec.push_back(RFVoltInfoTemp);
            }
        }
    }
    return WT_OK;
}

//CheckRfExist
bool DevBusiness::CheckRfExist()
{
    return true;
}

//开关板端口设置
int DevBusiness::SetRFPort(int RFPort, int Mode, int State, WT_SB_CONFIG_TYPE_E SBConfigType)
{
    int Ret = WT_OK;
    int Cmd = (m_BoardType == DEV_TYPE_VSA ? SET_RX_PORT : SET_TX_PORT);
    int SwitchId = 0;
    int SubPort = 0;

    m_BackBoard->GetSwitchMap(m_ModId, RFPort, SwitchId, SubPort);
    State = (RFPort != WT_RF_OFF ? State : WT_RF_STATE_OFF);

    SwitchRFPortSetType RFPortSetTemp;
    RFPortSetTemp.SwitchId = SwitchId;
    RFPortSetTemp.SubPort = SubPort;
    RFPortSetTemp.SBConfigType = SBConfigType;
    RFPortSetTemp.Mode = Mode;
    RFPortSetTemp.State = State;
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetRFPort RFPort%d(SwitchId%d, SubPort%d) to Mode%d, State%d\n", RFPort, SwitchId, SubPort, Mode, State);
    if ((Ret = DrvCmd(Cmd, sizeof(SwitchRFPortSetType), &RFPortSetTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_RF_PORT DrvCmd error");
        return Ret;
    }

    m_RunData.RFPort = RFPort;
    m_RunData.RFPortState = State;
    m_RunData.RFPortMode = Mode;
    m_BackBoard->SetPortStatus(RFPort, m_BoardType == DEV_TYPE_VSA ? WT_RF_RX_STATUS : WT_RF_TX_STATUS);
    return WT_OK;
}

int DevBusiness::LoMixInit()
{
    if(m_TesterType == HW_WT418)
    {
        return LMX2594Init(string("LMX2595_Init"));
    }
    else if (m_HwInfo.LoHwVersion >= VERSION_B)
    {
        return LMX2820Init(LoMix);
    }
    else
    {
        return LMX2594Init();
    }
}

int DevBusiness::LoModInit()
{
    int Ret = WT_OK;
    int RetTemp;

    //LoBoardInit不涉及硬件，初始化不同版本的，以备不同硬件版本混插
    RetTemp = LoBoardInit();
    RetWarnning(RetTemp, "LoModInit Error");
    Ret = (Ret == WT_OK ? RetTemp : Ret);

    if(m_TesterType == HW_WT418)
    {
        RetTemp = HMC833Init();
        RetWarnning(RetTemp, "RFBoardInit HMC833Init failed!");
        Ret = (Ret == WT_OK ? RetTemp : Ret);
    }
    else if (m_HwInfo.LoHwVersion >= VERSION_B)
    {
        RetTemp = LoBoardInit_VB();
        RetWarnning(RetTemp, "LoBoardInit_VB Error");
        Ret = (Ret == WT_OK ? RetTemp : Ret);

        RetTemp = LMX2820Init(LoMod);
        RetWarnning(RetTemp, "LMX2820Init Error");
        Ret = (Ret == WT_OK ? RetTemp : Ret);
    }
    else
    {
        RetTemp = SetModFreqPower(ModFreqDefault, DDSFsCurrentDefault);
        RetWarnning(RetTemp, "SetModFreqPower Error");
        Ret = (Ret == WT_OK ? RetTemp : Ret);
    }
    return Ret;
}

int DevBusiness::LoBoardInit()
{
    int Ret = WT_OK;

    m_RunData.ModLoRefSel = REF_VA_DDS_1000M;

    //HMC705 CHANNEL INIT
    Json::Value Reg = m_JsonRoot["HMC705_Div_Ratio"];
    if (Reg.isArray())
    {
        m_HMC705RatioN.clear();
        FreqSection FreqS;
        for (unsigned i = 0; i < Reg.size(); i++)
        {
            FreqS.StartFreq = std::strtol(Reg[i]["StartFreq"].asString().c_str(), 0, 0);
            FreqS.EndFreq = std::strtol(Reg[i]["EndFreq"].asString().c_str(), 0, 0);
            FreqS.Index = std::strtol(Reg[i]["DivRatioN"].asString().c_str(), 0, 0);
            m_HMC705RatioN.push_back(FreqS);
        }
    }

    //LOOP FILTER INIT
    do
    {
        const int LoTxLoopFilterBit[] = {LO_SHIFT_TX_TX_NU_IN1_CTL,
                                         LO_SHIFT_TX_TX_NU_IN2_CTL,
                                         LO_SHIFT_TX_TX_NU_IN3_CTL,
                                         LO_SHIFT_TX_TX_NU_IN4_CTL,
                                         LO_SHIFT_TX_TX_ND_IN1_CTL,
                                         LO_SHIFT_TX_TX_ND_IN2_CTL,
                                         LO_SHIFT_TX_TX_ND_IN3_CTL,
                                         LO_SHIFT_TX_TX_ND_IN4_CTL,
                                         LO_SHIFT_TX_TX_OP_IN1_CTL,
                                         LO_SHIFT_TX_TX_OP_IN2_CTL,
                                         LO_SHIFT_TX_TX_OP_IN3_CTL,
                                         LO_SHIFT_TX_TX_OP_IN4_CTL};

        const int LoRxLoopFilterBit[] = {LO_SHIFT_RX_RX_NU_IN1_CTL,
                                         LO_SHIFT_RX_RX_NU_IN2_CTL,
                                         LO_SHIFT_RX_RX_NU_IN3_CTL,
                                         LO_SHIFT_RX_RX_NU_IN4_CTL,
                                         LO_SHIFT_RX_RX_ND_IN1_CTL,
                                         LO_SHIFT_RX_RX_ND_IN2_CTL,
                                         LO_SHIFT_RX_RX_ND_IN3_CTL,
                                         LO_SHIFT_RX_RX_ND_IN4_CTL,
                                         LO_SHIFT_RX_RX_OP_IN1_CTL,
                                         LO_SHIFT_RX_RX_OP_IN2_CTL,
                                         LO_SHIFT_RX_RX_OP_IN3_CTL,
                                         LO_SHIFT_RX_RX_OP_IN4_CTL};

        long long TXLoShift[64] = {0};
        long long RXLoShift[64] = {0};

        do
        {
            WTFileSecure FileSec("configuration/LoopFilterConfig.csv");
            ifstream FStream(FileSec.GetDecryptName()); //文件流
            if (!FStream.is_open())
            {
                Ret = WT_CONF_FILE_ERROR;
                break;
            }

            string Line;
            int Start = false;
            FreqSection FreqS;
            m_LoopFilter.clear();
            while (getline(FStream, Line))
            {
                //去掉注释行
                if (Line.find("##", 0, 2) != string::npos || Line.find("//", 0, 2) != string::npos)
                {
                    continue;
                }
                else if (Start == false)
                {
                    if (Line.find("StatrFreq") != string::npos || Line.find("EndFreq") != string::npos)
                    {
                        Start = true;
                    }
                    continue;
                }

                int Pos = 0;
                Ret = Basefun::GetItemVal(Line, FreqS.StartFreq, Pos++);
                RetContinue(Ret, "GetItemVal Error");
                Ret = Basefun::GetItemVal(Line, FreqS.EndFreq, Pos++);
                RetContinue(Ret, "GetItemVal Error");

                FreqS.Index = m_LoopFilter.size();
                for (int i = 0; i < sizeof(LoTxLoopFilterBit) / sizeof(int); i++)
                {
                    int LoShiftBit = 0;
                    Basefun::GetItemVal(Line, LoShiftBit, Pos++);
                    if (LoShiftBit)
                    {
                        TXLoShift[FreqS.Index] |= 0x1ull << LoTxLoopFilterBit[i];
                        RXLoShift[FreqS.Index] |= 0x1ull << LoRxLoopFilterBit[i];
                    }
                }
                m_LoopFilter.push_back(FreqS);
            }
            FStream.close();
        } while (0);
        SwitchCfgType SwCfgTemp;

        ostringstream Stream;
        Stream << WT_LF_INIT_FILEPATH;
        //判断内存临时文件是否存在，避免重复初始化
        if (access(Stream.str().c_str(), F_OK) != 0)
        {
            //生成内存临时文件
            int fd = open(Stream.str().c_str(), O_CREAT, 0644);
            close(fd);

            WTLog::Instance().WriteLog(LOG_DEBUG, "========================InitBaseBand LoopFilter=%d,%d\n", m_BoardType, m_ModId);

            SwCfgTemp.Type = DEV_TYPE_VSG;
            SwCfgTemp.Size = m_LoopFilter.size();
            SwCfgTemp.Addr = (char *)&TXLoShift[0];
            if ((Ret = DrvCmd(SET_LO_SHIFT_LOOP_FILTER_INIT, sizeof(SwitchCfgType), &SwCfgTemp)) != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "SET_LO_SHIFT_LOOP_FILTER_INIT ioctl error");
            }

            SwCfgTemp.Type = DEV_TYPE_VSA;
            SwCfgTemp.Addr = (char *)&RXLoShift[0];
            if ((Ret = DrvCmd(SET_LO_SHIFT_LOOP_FILTER_INIT, sizeof(SwitchCfgType), &SwCfgTemp)) != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "SET_LO_SHIFT_LOOP_FILTER_INIT ioctl error");
            }
        }
    } while (0);

    return Ret;
}

int DevBusiness::LoBoardInit_VB()
{
    int Ret = WT_OK;

    //LO SETTING
    Json::Value RegSetting = m_JsonRoot["LO_SETTING_VB"];
    m_RunData.ModLoRefSel = RegSetting["FRef"].asUInt() < static_cast<int>(REF_VB_MAX)
                                ? RegSetting["FRef"].asUInt()
                                : static_cast<int>(REF_100M);
    m_RunData.ModMashOrder = RegSetting["ModMashOrder"].asUInt() < static_cast<int>(MASH_ORDER_MAX) 
                                 ? RegSetting["ModMashOrder"].asUInt() 
                                 : static_cast<int>(MASH_ORDER_THIRD);
    m_RunData.MixMashOrder = RegSetting["MixMashOrder"].asUInt() < static_cast<int>(MASH_ORDER_MAX)
                                 ? RegSetting["MixMashOrder"].asUInt()
                                 : static_cast<int>(MASH_ORDER_THIRD);

    Ret = SetDDSChannel(static_cast<unsigned long long>(m_RunData.ModLoRefSel));
    RetWarnning(Ret, "SetDDSChannel failed!");

    //MOD FREQ CHANNEL INIT
    Json::Value Reg = m_JsonRoot["ModFreq_Channel"];
    if (Reg.isArray())
    {
        m_ModFreqChannel.clear();
        FreqSection FreqS;
        for (unsigned i = 0; i < Reg.size(); i++)
        {
            FreqS.StartFreq = std::strtol(Reg[i]["StartFreq"].asString().c_str(), 0, 0);
            FreqS.EndFreq = std::strtol(Reg[i]["EndFreq"].asString().c_str(), 0, 0);
            FreqS.Index = i;
            m_ModFreqChannel.push_back(FreqS);
        }
    }
    Ret = SetLoFreqChannel(m_ModFreqChannel.begin()->Index);
    RetWarnning(Ret, "SetLoFreqChannel failed!");

    //MOD FREQ CHANNEL INIT
    Json::Value RegDDS = m_JsonRoot["DDS_VCO_SELECT"];
    if (RegDDS.isArray())
    {
        m_DDSRefFreqIndex.clear();
        FreqSection FreqS;
        for (unsigned i = 0; i < RegDDS.size(); i++)
        {
            FreqS.StartFreq = std::strtol(RegDDS[i]["StartFreq"].asString().c_str(), 0, 0);
            FreqS.EndFreq = std::strtol(RegDDS[i]["EndFreq"].asString().c_str(), 0, 0);
            FreqS.Index = std::strtol(RegDDS[i]["Index"].asString().c_str(), 0, 0);
            m_DDSRefFreqIndex.push_back(FreqS);
        }
    }
    return Ret;
}

int DevBusiness::LMX2594Init(string InitMap)
{
    int Ret = WT_OK;
    int RegData;
    int RegAddr;
    unsigned Count = 1;
    Json::Value Reg;
    Json::Value PLL_Den;
    if(InitMap == string("LMX2595_Init"))
    {
        if(m_BoardType == DEV_TYPE_VSG)
        {
            Reg = m_JsonRoot["LMX2595_Init"]["VSGReg"];
        }
        else
        {
            Reg = m_JsonRoot["LMX2595_Init"]["VSAReg"];
        }
        PLL_Den = m_JsonRoot["LMX2595_Init"]["Pll_Den"];
    }
    else
    {
        Reg = m_JsonRoot["LMX2594_Init"]["Reg"];
        PLL_Den = m_JsonRoot["LMX2594_Init"]["Pll_Den"];
    }

    Ret = WriteLMX2594( 0, LMX2594RESET);
    RetWarnning(Ret, "Reset LMX2594 failed!");
    usleep(2000);
    do
    {
        if (Reg.isArray())
        {
            for (unsigned i = 0; i < Reg.size(); i++)
            {
                RegAddr = std::strtol(Reg[i]["Addr"].asString().c_str(), 0, 0);
                RegData = std::strtol(Reg[i]["Data"].asString().c_str(), 0, 0);
                if (RegAddr == 75)
                {
                    m_RunData.LOCurData[LoMix].LastChDiv = (RegData >> 6) & 0x1F;
                }
                else if (RegAddr == 44)
                {
                    m_RunData.LOCurData[LoMix].R44 = RegData;
                }
                else if (RegAddr == 45)
                {
                    m_RunData.LOCurData[LoMix].R45 = RegData;
                }
                else if ((RegAddr == 39) && PLL_Den.isInt())
                {
                    RegData = PLL_Den.asInt() & 0xFFFF;
                }
                else if ((RegAddr == 38) && PLL_Den.isInt())
                {
                    RegData = (PLL_Den.asInt() >> 16) & 0xFFFF;
                }
                Ret = WriteLMX2594(RegAddr, RegData);
                RetAssert(Ret, "WriteLMX2594 failed!");
            }
            usleep(10000);
            if (CheckLOIsLock(LoMix))
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WriteLMX2594 is lock!" << std::endl;
                return WT_OK;
            }
            else
            {
                if (Count % 2 == 0)
                {
                    //复位本振
                    Ret = WriteLMX2594(0, LMX2594RESET);
                    RetWarnning(Ret, "Reset LoMix failed!");
                    usleep(1000);
                }
            }
        }
    } while (Count--);
    m_BackBoard->SetErrorLed(LED_STATUS_ON);

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix try lock 2 times failed!" << std::endl;
#endif

    return WT_MIX_FREQ_IS_UNLOCKED;
}

#define CHARGE_PUMP_DEFAULT  0x403264       //默认上拉电流寄存器值
int DevBusiness::HMC833Init()
{
    int Ret = WT_OK;
    int RegData = 0;
    int RegAddr = 0;
    unsigned Count = 1;
    int StartBitIndex;
    int BitsData;
    const Json::Value &Reg = m_JsonRoot["HMC833_Init"]["Reg"];
    const Json::Value &PumpValue = m_JsonRoot["HMC833_Init"]["Reg9PumpValue"];

    //复位本振
    Ret = WriteHMC833Reg(0, 0x20);
    RetWarnning(Ret, "Reset HMC833 failed!");
    usleep(3000);

    do
    {
        if (Reg.isArray())
        {

            for (unsigned i = 0; i < Reg.size(); i++)
            {
                RegAddr = std::strtol(Reg[i]["Addr"].asString().c_str(), 0, 0);
                RegData = std::strtol(Reg[i]["Data"].asString().c_str(), 0, 0);

                if ((RegAddr == 9) && PumpValue.isArray())
                {
                    for (unsigned j = 0; j < PumpValue.size(); j++)
                    {
                        StartBitIndex = std::strtol(PumpValue[j]["StartBitIndex"].asString().c_str(), 0, 0);
                        BitsData = std::strtol(PumpValue[j]["BitsData"].asString().c_str(), 0, 0);
                        RegData = RegData | (BitsData << StartBitIndex);
                    }
                }

                Ret = WriteHMC833Reg(RegAddr, RegData);
                RetBreak(Ret, "HMC833Init WriteLO failed!");
                usleep(1000);
            }

            if (CheckLOIsLock(LoMod))
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "HMC833 is lock!" << std::endl;
                return WT_OK;
            }
            else
            {
                if (Count % 2 == 0)
                {
                    //复位本振
                    Ret = WriteHMC833Reg(0, 0x20);
                    RetWarnning(Ret, "Reset HMC833 failed!");
                    usleep(4000);
                    continue;
                }
                WriteHMC833Reg(9, CHARGE_PUMP_DEFAULT);           //拉高上拉电流
            }
        }
    } while (Count++ <= 16);

    m_BackBoard->SetErrorLed(LED_STATUS_ON);
#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "HMC833 try lock 16 times failed!" << std::endl;
#endif
    return WT_HMC833_IS_UNLOCKED;
}

int DevBusiness::LMX2820Init(LO_ID_E LOId)
{
    int Ret = WT_OK;
    int RegData;
    int RegAddr;
    unsigned Count = 1;

    m_RunData.LOCurData[LOId].R0 = (0x6470 & ~LMX2820R0MASK);
    m_RunData.LOCurData[LOId].R2 = (0x81F4 & ~LMX2820R2MASK);
    m_RunData.LOCurData[LOId].R32 = (0x1001 & ~LMX2820R32MASK);
    m_RunData.LOCurData[LOId].R35 = (0x3100 & ~LMX2820R35MASK);
    m_RunData.LOCurData[LOId].R79 = (0x3100 & ~LMX2820R79MASK);
    m_RunData.LOCurData[LOId].R80 = (0x1C0 & ~LMX2820R80MASK);

    m_RunData.LOCurData[LOId].RefSel = -1;
    m_RunData.LOCurData[LOId].MashOrder = -1;
    m_RunData.LOCurData[LOId].ChDivIndex = -1;
    m_RunData.LOCurData[LOId].OutMux = -1;
    Json::Value LoJson;
    if(m_HwInfo.LoHwVersion >= VERSION_D)
    {
        LoJson = m_JsonRoot["LO_SETTING_VC"];
    }
    else
    {
        LoJson = m_JsonRoot["LO_SETTING_VB"];
    }

    //输出功率
    string RegName = string(m_BoardType == DEV_TYPE_VSG ? "Vsg" : "Vsa") + (LOId == LoMod ? "Mod" : "Mix") + "Pwr";
    m_RunData.LOCurData[LOId].OutPwr = LoJson[RegName.c_str()].isInt() ? LoJson[RegName.c_str()].asInt() : 7;

    RegName = string(m_BoardType == DEV_TYPE_VSG ? "Vsg" : "Vsa") + (LOId == LoMod ? "Mod" : "Mix") + "PwrB";
    m_RunData.LOCurData[LOId].OutPwrB = LoJson[RegName.c_str()].isInt() ? LoJson[RegName.c_str()].asInt() : 7;

    //鉴相频率与小数分母
    Json::Value Fpd_Json = m_JsonRoot["LMX2820_Init"];
    m_RunData.LOCurData[LOId].Fpd = Fpd_Json["FPD"].isInt() ? Fpd_Json["FPD"].asInt() : 200;
    m_RunData.LOCurData[LOId].PllDen = Fpd_Json["PLL_DEN"].isInt() ? Fpd_Json["PLL_DEN"].asInt() : 1000;

    //初始化寄存器
    Json::Value Reg = Fpd_Json["Reg"];

    Ret = WriteLMX2820(LOId, 0, LMX2820RESET);
    RetWarnning(Ret, "Reset LMX2820 failed!");
    usleep(2000);
    do
    {
        if (Reg.isArray())
        {
            for (unsigned i = 0; i < Reg.size(); i++)
            {
                RegAddr = std::strtol(Reg[i]["Addr"].asString().c_str(), 0, 0);
                RegData = std::strtol(Reg[i]["Data"].asString().c_str(), 0, 0);

                if (RegAddr == 32)
                {
                    m_RunData.LOCurData[LOId].R32 = (RegData & ~LMX2820R32MASK);
                }
                else if (RegAddr == 35)
                {
                    m_RunData.LOCurData[LOId].R35 = (RegData & ~LMX2820R35MASK);
                }
                else if (RegAddr == 79)
                {
                    m_RunData.LOCurData[LOId].R79 = (RegData & ~LMX2820R79MASK);
                    RegData = ((m_RunData.LOCurData[LOId].OutPwr << 1) & LMX2820R79MASK) | m_RunData.LOCurData[LOId].R79;
                }
                else if (RegAddr == 2)
                {
                    m_RunData.LOCurData[LOId].R2 = (RegData & ~LMX2820R2MASK);
                }
                else if (RegAddr == 0)
                {
                    m_RunData.LOCurData[LOId].R0 = (RegData & ~LMX2820R0MASK);
                }
                else if (RegAddr == 80)
                {
                    m_RunData.LOCurData[LOId].R80 = (RegData & ~LMX2820R80MASK);
                    RegData = ((m_RunData.LOCurData[LOId].OutPwrB << 6) & LMX2820R80MASK) | m_RunData.LOCurData[LOId].R80;
                }

                Ret = WriteLMX2820(LOId, RegAddr, RegData);
                RetAssert(Ret, "WriteLMX2820 failed!");
            }
            usleep(10000);
            if (CheckLOIsLock(LOId))
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WriteLMX2820 is lock!" << std::endl;
                return WT_OK;
            }
            else
            {
                if (Count % 2 == 0)
                {
                    //复位本振
                    Ret = WriteLMX2820(LOId, 0, LMX2820RESET);
                    RetWarnning(Ret, "Reset LMX2820 failed!");
                    usleep(1000);
                }
            }
        }
    } while (Count--);
    m_BackBoard->SetErrorLed(LED_STATUS_ON);

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LMX2820 try lock 2 times failed!" << std::endl;
#endif

    return LOId == LoMod ? WT_MOD_FREQ_IS_UNLOCKED : WT_MIX_FREQ_IS_UNLOCKED;
}

int DevBusiness::SetRfModLoSwitch(double ModLoFreq /*MHz*/)
{
    return SetBand(ModLoFreq, m_RunData.LOCurData[LoMix].Freq, m_RunData.CurrentBandMod, m_RunData.CurrentBandMix);
}

int DevBusiness::SetMixLoSwitch(double Freq /*MHz*/)
{
    int Ret = WT_OK;
    int MixSwtich = Basefun::CompareDouble(Freq, LO_MIX_SW) >= 0
                        ? MIX_LO_SWITCH_HIGHT
                        : MIX_LO_SWITCH_LOW;

    if ((Ret = DrvCmd(SET_MIX_LO_SWITCH, sizeof(int), &MixSwtich)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_MIX_LO_SWITCH DrvCmd error");
        return Ret;
        }
    return WT_OK;
        }

int DevBusiness::SetModFreqPower(double Freq /*MHz*/, int PowerLevel, int IQSwap)
{
    if(m_TesterType == HW_WT418)
    {
        return SetHMC833FreqPower(Freq, PowerLevel);
    }
    else if (m_HwInfo.LoHwVersion >= VERSION_B)
    {
        SetIQSwitch(IQSwap ? !m_IQSwitch : m_IQSwitch);
        return SetLoBoardFreqPower_VB(Freq, PowerLevel);
    }
    else
    {
        return SetLoBoardFreqPower(Freq, PowerLevel);
    }
}

int DevBusiness::SetMixFreqPower(double Freq /*MHz*/, int PowerLevel)
{
    if(m_TesterType == HW_WT418)
    {
        return SetLMX2594FreqPower(static_cast<int>(Freq), PowerLevel);
    }
    else if (m_HwInfo.LoHwVersion >= VERSION_B)
    {
        SetMixLoSwitch(Freq);
        return SetLMX2820FreqPower(LoMix, Freq, PowerLevel);
    }
    else
    {
        return SetLMX2594FreqPower(static_cast<int>(Freq), PowerLevel);
    }
}

int DevBusiness::SetLoBoardFreqPower(double Freq /*MHz*/, int PowerLevel)
{
    int Ret = WT_OK;
    double ChDiv = 1;
    int Index = 1;

    WTLog::Instance().WriteLog(LOG_DEBUG, "SetModFreqPower Freq=%lf, PowerLevel=%d\n", Freq, PowerLevel);

    //Freq Channel
    GetModFreqChannel(Freq, ChDiv, Index);
    Ret = SetLoFreqChannel(Index);
    RetAssert(Ret, "SetLoFreqChannel failed!");

    //FreqVco
    double FreqVco = Freq * ChDiv;
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetModFreqChannel=%d ChDiv=%lf, FreqVco=%lf(MHz)\n", Index, ChDiv, FreqVco);
    
    //Loop Filter
    if(m_LoopFilter.empty())
    {
        Index = BUSI_LO_LOOP_FILTER_1;
    }
    else
    {
        Index = m_LoopFilter.back().Index;
    }
    for (auto &iter : m_LoopFilter)
    {
        if (Basefun::CompareDouble(FreqVco, iter.EndFreq, 1e-12) <= 0)
        {
            Index = iter.Index;
            break;
        }
    }
    Ret = SetLoLoopFilter(Index);
    RetAssert(Ret, "SetLoLoopFilter failed!");

    //HMC705
    if(m_HMC705RatioN.empty())
    {
        Index = BUSI_LO_FREQ_CHANNEL_MUL_1_HITHT;
    }
    else
    {
        Index = m_HMC705RatioN.back().Index;
    }
    for (auto &iter : m_HMC705RatioN)
    {
        if (Basefun::CompareDouble(FreqVco, iter.EndFreq, 1e-12) <= 0)
        {
            Index = iter.Index;
            break;
        }
    }
    Ret = SetLoHMC705(Index);
    RetAssert(Ret, "SetLoHMC705 failed!");

    Ret = SetDDSFreq(FreqVco/Index);
    RetAssert(Ret, "SetDDSFreq failed!");
    Ret = SetDDSFsCurrent(PowerLevel);
    RetWarnning(Ret, "SetDDSFsCurrent failed!");

    return Ret;
}

int DevBusiness::SetLoBoardFreqPower_VB(double Freq /*MHz*/, int PowerLevel)
{
    int Ret = WT_OK;

    // FreqChannel
    int Channel = BUSI_VB_LO_FREQ_CHANNEL_300_520;
    if (m_ModFreqChannel.empty())
    {
        Channel = 0;
    }
    else
    {
        Channel = m_ModFreqChannel.back().Index;
    }
    for (auto &iter : m_ModFreqChannel)
    {
        if (Basefun::CompareDouble(Freq, iter.EndFreq, 1e-12) < 0)
        {
            Channel = iter.Index;
            break;
        }
    }

    Ret = SetLoFreqChannel(Channel);
    RetAssert(Ret, "SetLoFreqChannel failed!");

    Ret = SetLMX2820FreqPower(LoMod, Freq, PowerLevel);
    RetAssert(Ret, "SetLMX2820FreqPower failed!");

    return Ret;
}

int DevBusiness::GetModFreqChannel(double Freq, double &ChDiv, int &Index)
{
    double FreqVco;
    Index = BUSI_LO_FREQ_CHANNEL_DIV_8_LOW;
    ChDiv = ModChDivArray[sizeof(ModChDivArray) / sizeof(ModChDivArray[0]) - 1];
    for (int i = 0; i < sizeof(ModChDivArray) / sizeof(ModChDivArray[0]); i++)
    {
        FreqVco = Freq * ModChDivArray[i];
        if (Basefun::CompareDouble(FreqVco, (double)LO_DDS_VCO_MIN, 1e-12) <= 0)
        {
            continue;
        }
        else if (Basefun::CompareDouble(FreqVco, (double)LO_DDS_VCO_MAX, 1e-12) > 0)
        {
            return WT_ARG_ERROR;
        }
        else
        {
            ChDiv = ModChDivArray[i];
            if (Basefun::CompareDouble(FreqVco, (double)LO_DDS_VCO_THR, 1e-12) > 0)
            {
                Index = i * 2;
            }
            else
            {
                Index = i * 2 + 1;
            }
            return WT_OK;
        }
    }
    return WT_ARG_ERROR;
}

int DevBusiness::GetChDiv(double Freq, int &ChDiv, int &Index, int LoType)
{
    int FreqVco;

    //DEFAULT LMX2820
    const double *pChDivArray = Lmx2820ChDivArray;
    int FreqMax = LO_LMX2820_VCO_MAX;
    int FreqMin = LO_LMX2820_VCO_MIN;
    int Size = sizeof(Lmx2820ChDivArray) / sizeof(Lmx2820ChDivArray[0]);

    if (LoType == LO_LMX2594)
    {
        pChDivArray = Lmx2594ChDivArray;
        FreqMax = LO_LMX2594_VCO_MAX;
        FreqMin = LO_LMX2594_VCO_MIN;
        Size = sizeof(Lmx2594ChDivArray) / sizeof(Lmx2594ChDivArray[0]);
    }

    for (int i = 0; i < Size; i++)
    {
        FreqVco = Freq * pChDivArray[i];
        WTLog::Instance().WriteLog(LOG_DEBUG, "FreqVco = %d, FreqMin=%d, FreqMax=%d\n", FreqVco, FreqMin, FreqMax);
        if (FreqVco <= FreqMin)
        {
            continue;
        }
        else if (FreqVco > FreqMax)
        {
            return WT_ARG_ERROR;
        }
        else
        {
            ChDiv = pChDivArray[i];
            if (LoType == LO_LMX2594)
            {
                Index = i;
            }
            else
            {
                Index = ChDiv >= LO_LMX2820_MUX_INDEX ? i - LO_LMX2820_MUX_INDEX : -1;
            }
            return WT_OK;
        }
    }
    return WT_ARG_ERROR;
}

int DevBusiness::SetLMX2594FreqPower(int Freq, int PowerLevel)
{
    if (m_BoardType != m_ShareLOBoardType && m_LOComMode == LO_IS_COM_MODE)
    {
        return WT_OK;
    }
    int Ret = WT_OK;
    int ChDiv = 0;      //VCO分频值
    int ChDivIndex = -1;//ChDiv的Index,=-1时表示不需重新配置ChDiv
    double PLL;         //分频系数;
    int Pll_N;          //整数分频值
    int Pll_Mol;        //小数分频值的分子，分母默认为1000
    int Pll_Den = 1000;
    int Fpd = 200;

    Json::Value Fpd_Json = m_JsonRoot["LMX2594_Init"]["FPD"];
    Json::Value Pll_Denominator_Json = m_JsonRoot["LMX2594_Init"]["PLL_Den"];
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"SetLMX2594FreqPower: "<<" Freq = "<<Freq<<"PowerLevel = "<<PowerLevel<<std::endl;
    if (Fpd_Json.isInt() && Pll_Denominator_Json.isInt())
    {
        Fpd = Fpd_Json.asInt();
        Pll_Den = Pll_Denominator_Json.asInt();
    }

    Freq = Basefun::Near(Freq, 50);

    if (Freq)
    {
        Ret = GetChDiv(Freq, ChDiv, ChDivIndex, LO_LMX2594);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "LMX2594 VCO Freq Compute Error!");
        if (ChDiv != m_RunData.LOCurData[LoMix].LastChDiv)
        {
            m_RunData.LOCurData[LoMix].LastChDiv = ChDiv;
        }
        else
        {
            //不需重新设置ChDiv
            ChDivIndex = -1;
        }
        PLL = (double)(Freq * ChDiv) / Fpd;
        Pll_N = (int)(PLL);
        Pll_Mol = (int)((PLL - Pll_N)*Pll_Den);
    }
    else
    {
        Pll_N = Pll_Mol = 0;
    }


#ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Pll_N=" << Pll_N << ",    Pll_Mol=" << Pll_Mol << std::endl;
#endif

    // 0中频时，混频本振不清0, 规避再次启用混频时功率不稳定或生效慢的问题
    if (Pll_N == 0)
    {
        return WT_OK;
    }
    //PowerLevel范围是[0,63]

    if (m_LOComMode == LO_IS_COM_MODE)
    {
        Ret = WriteLMX2594( 44, ((m_RunData.LOCurData[LoMix].R44 & ~(0x3F00)) | (PowerLevel<<8)) & (~(0x1u<<6)));
        RetAssert(Ret, "WriteLMX2594 failed!");
        Ret = WriteLMX2594( 45, (m_RunData.LOCurData[LoMix].R45 & ~(0x3F)) | (PowerLevel));
        RetAssert(Ret, "WriteLMX2594 failed!");
    }
    else
    {
        if(!((m_RunData.LOCurData[LoMix].R44 >> 6) & 0x1u))
        {
            Ret = WriteLMX2594( 44, (m_RunData.LOCurData[LoMix].R44 & ~(0x3F00) ) | (PowerLevel<<8));
            RetAssert(Ret, "WriteLMX2594 failed!");
        }
        else if(!((m_RunData.LOCurData[LoMix].R44 >> 7) & 0x1u))
        {
            Ret = WriteLMX2594( 45, (m_RunData.LOCurData[LoMix].R45 & ~(0x3F) ) | (PowerLevel));
            RetAssert(Ret, "WriteLMX2594 failed!");
        }
    }
    if (ChDivIndex >= 0)
    {
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ReSet LMX2594  ChDiv =" << ChDiv << std::endl;
#endif
        Ret = WriteLMX2594( 75, (0x800 & (~0x1FU << 6)) | (ChDivIndex << 6));
        RetAssert(Ret, "WriteLMX2594 failed!");
    }
    Ret = WriteLMX2594( 36, Pll_N & 0xFFFF);
    RetAssert(Ret, "WriteLMX2594 failed!");
    Ret = WriteLMX2594( 34, (Pll_N >> 16) & 0x3);
    RetAssert(Ret, "WriteLMX2594 failed!");
    Ret = WriteLMX2594( 43, Pll_Mol & 0xFFFF);
    RetAssert(Ret, "WriteLMX2594 failed!");
    Ret = WriteLMX2594( 42, (Pll_Mol >> 16) & 0xFFFF);
    RetAssert(Ret, "WriteLMX2594 failed!");
    Ret = WriteLMX2594( 0, Pll_N ? LMX2594LOCK : LMX2594POWERDOWN);
    RetAssert(Ret, "WriteLMX2594 failed!");

    struct timeval tptime;
    gettimeofday(&tptime, NULL);
    return WT_OK;
}

int DevBusiness::LOConfigSave(int LOId, int ActiveStatus, int BandMode, double Freq, int PowerLevel)
{
    m_RunData.LOCurData[LOId].Activated = ActiveStatus;
    m_RunData.LOCurData[LOId].LastFreq = m_RunData.LOCurData[LOId].Freq;
    m_RunData.LOCurData[LOId].LastPowerLevel = m_RunData.LOCurData[LOId].PowerLevel;
    m_RunData.LOCurData[LOId].LastBandMode = m_RunData.LOCurData[LOId].BandMode;

    m_RunData.LOCurData[LOId].BandMode = BandMode;
    m_RunData.LOCurData[LOId].Freq = Freq;
    m_RunData.LOCurData[LOId].PowerLevel = PowerLevel;

    return WT_OK;
}

int DevBusiness::CheckLOIsLock(LO_ID_E LOId)
{
    int Value = 0;
    int Ret = WT_OK;

    //本振板不存在时直接返回锁定
    if (!m_IsLoExist || m_ListMode)
    {
        return true;
    }
    else if (LOId != LoMod && LOId != LoMix)
    {
        WTLog::Instance().LOGERR(WT_LO_ID_ERROR, "WT_LO_ID_ERROR error");
        return WT_LO_ID_ERROR;
    }
    if(m_TesterType == HW_WT418)
    {
        if(LOId == LoMod)
        {
            Ret = ReadHMC833Reg(0x12, Value);
            RetWarnning(Ret, "Check HMC833 Locked status failed!");
            return GetBit(Value, 1);
        }
        else
        {
            Ret = ReadLMX2594(0x0, Value);
            RetWarnning(Ret, "Check LMX2594 Locked status failed!");
            return (Value & 0xFFFF) == 0xFFFF ? true : false;
        }
    }
    else if (m_HwInfo.BBHwVersion >= VERSION_B)
    {
        bool lock = false;
        int TryCnt = 0;
        //最多等待250us
        while (++TryCnt < 25)
        {
            Ret = ReadLMX2820(LOId, LMX2820LOCKREG, Value);
            RetWarnning(Ret, "Check LMX2820 Locked status failed!");
            lock = (Value & LMX2820LOCKMASK) == LMX2820LOCKSTATUS;
            if (lock)
            {
                break;
            }
            usleep(10);
        }
        return lock;
    }
    else
    {
        if (LOId == LoMod)
        {
            ReadDirectReg(LOMODLOCKDETECT, Value);
            if (m_BoardType == DEV_TYPE_VSA)
            {
                return (Value & HMC3716_VSA_BIT) ? true : false;
            }
            else
            {
                return (Value & HMC3716_VSG_BIT) ? true : false;
            }
        }
        else
        {
            Ret = ReadLMX2594(0x0, Value);
            RetWarnning(Ret, "Check LoMix Locked status failed!");
            return (Value & 0xFFFF) == 0xFFFF ? true : false;
        }
    }
}

int DevBusiness::PowerDownLO(LO_ID_E LOId)
{
    int Ret = WT_OK;
    do
    {
        if (!m_IsLoExist)
        {
            Ret = WT_OK;
            break;
        }
        else if (LOId != LoMod && LOId != LoMix)
        {
            WTLog::Instance().LOGERR(WT_LO_ID_ERROR, "WT_LO_ID_ERROR error");
            Ret = WT_LO_ID_ERROR;
            break;
        }

        if(m_TesterType == HW_WT418)
        {
            if(LOId == LoMod)
            {
                Ret = WriteHMC833Reg(0x1, 0);
                RetBreak(Ret, "WriteHMC833Reg failed!");
            }
            else
            {
                Ret = WriteLMX2594(0x0, LMX2594POWERDOWN);
                RetBreak(Ret, "WriteLMX2594 failed!");
            }
        }
        else if (m_HwInfo.BBHwVersion >= VERSION_B)
        {
            if (LOId == LoMod)
            {
                Ret = WriteLMX2820(LoMod, 0, LMX2820POWERDOWN);
                RetBreak(Ret, "WriteLMX2820 failed!");
            }
            else
            {
                Ret = WriteLMX2820(LoMix, 0, LMX2820POWERDOWN);
                RetBreak(Ret, "WriteLMX2820 failed!");
            }
        }
        else
        {
            if(LOId == LoMod)
            {
                //4XX A板似乎没有LOMOD
            }
            else
            {
                Ret = WriteLMX2594(0x0, LMX2594POWERDOWN);
                RetBreak(Ret, "WriteLMX2594 failed!");
            }
        }
    } while (0);
 
    return Ret;
}

int DevBusiness::CheckLOStatus(double Freq)
{
    //判断MOD是否同时锁定
    if (!CheckLOIsLock(LoMod))
    {
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMoD is unlock!" << std::endl;
#endif

#if LO_SET_DEBUG
        m_RunData.LOCurData[LoMod].Freq = 0;
        return WT_MOD_FREQ_IS_UNLOCKED;
#else
        return WT_OK;
#endif
    }

    if (IsUseLo2(Freq))
    {
        //判断MIX是否同时锁定
        if (!CheckLOIsLock(LoMix))
        {
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix LO is unlock!" << std::endl;
#endif

#if LO_SET_DEBUG
            m_RunData.LOCurData[LoMix].Freq = 0;
            return WT_MIX_FREQ_IS_UNLOCKED;
#else
            return WT_OK;
#endif
        }
    }
    return WT_OK;
}

int DevBusiness::ResetLO(LO_ID_E LOId)
{
    int Ret = WT_OK;
    if (LOId == LoMix)
    {
        Ret = LoMixInit();
        RetAssert(Ret, "ResetLO LoMix Init failed!");

        Ret = SetMixFreqPower(m_RunData.LOCurData[LoMix].LastFreq,
                              m_RunData.LOCurData[LoMix].LastPowerLevel);
        RetAssert(Ret, "ResetLO SetMixFreqPower failed!");
    }
    else if (m_TesterType == HW_WT418 || m_HwInfo.LoHwVersion >= VERSION_B)
    {
        Ret = LoModInit();
        RetAssert(Ret, "ResetLO LoMod Init failed!");
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<< "m_RunData.LOCurData[LoMod].LastFreq = "<<m_RunData.LOCurData[LoMod].LastFreq<<std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG)<< "m_RunData.LOCurData[LoMod].LastPowerLevel = "<<m_RunData.LOCurData[LoMod].LastPowerLevel<<std::endl;
        Ret = SetModFreqPower(m_RunData.LOCurData[LoMod].LastFreq,
                              m_RunData.LOCurData[LoMod].LastPowerLevel);
        RetAssert(Ret, "ResetLO SetModFreqPower failed!");
    }
    return WT_OK;
}

//ATT
int DevBusiness::SetATTCode(int DevId, int Code)
{
    if (Code < 0 || Code > ATT_CODE_MAX)
    {
        WTLog::Instance().LOGERR(WT_ATTCODE_OVER_RANGE, "SetATTCode ATT Code error!");
        Code = ATT_CODE_MAX;
        return WT_ATTCODE_OVER_RANGE;
    }

    return WriteATT(DevId, Code);
}

int DevBusiness::GetATTCode(int DevId, int &Code)
{
    int Ret = WT_OK;
    Ret = ReadATT(DevId, Code);
    RetAssert(Ret, "ReadATT failed!");
    
    return WT_OK;
}

int DevBusiness::WriteATT(int AttId, int Code)
{
    int Ret = WT_OK;
    struct ATTCodeType ATTCodeTemp;
    ATTCodeTemp.AttId = AttId;
    ATTCodeTemp.Code = Code;
    if ((Ret = DrvCmd(WRITE_ATT, sizeof(ATTCodeType), &ATTCodeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WriteATT DrvCmd error");
        return Ret;
    }

    return WT_OK;
}

int DevBusiness::ReadATT(int AttId, int &Code)
{
    int Ret = WT_OK;
    struct ATTCodeType ATTCodeTemp;
    ATTCodeTemp.AttId = AttId;
    ATTCodeTemp.Code = 0;

    if ((Ret = DrvCmd(READ_ATT, sizeof(ATTCodeType), &ATTCodeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_ATT DrvCmd error");
        return Ret;
    }
    Code = ATTCodeTemp.Code;
    return WT_OK;
}

int DevBusiness::ShowRFInfo()
{
    double TempValue = 0.0;
    int Ret = WT_OK;

    Ret = GetRFAllVoltValue();
    RetWarnning(Ret, "GetRFAllVoltValue failed!");

    for (auto Iter : m_RFVoltInfoVec)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Iter.Board << " AD7091 Channel" << Iter.VoltChannel
                  << "  " << Iter.VoltChannelInfo << " =" << std::setiosflags(std::ios::fixed)
                  << std::setprecision(2) << Iter.VoltValue * Iter.MultVolt << "V" << std::endl;
    }

    Ret = GetRFTemperature(0, TempValue);
    RetWarnning(Ret, "GetRFTemperature failed!");

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << (m_BoardType == DEV_TYPE_VSA ? "VSA" : "VSG") << " ModId=" << m_ModId
              << " RF LM74 Current Temperature=" << std::setiosflags(std::ios::fixed)
              << std::setprecision(2) << TempValue << "℃" << std::endl;

    //本振锁定信息
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LO Info:\n"
              << (m_BoardType == DEV_TYPE_VSA ? "VSA" : "VSG") << " ModId=" << m_ModId << "  ";

    if (CheckLOIsLock(LoMix))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix:locked!" << std::endl;
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix:unlocked!" << std::endl;
    }

    if (CheckLOIsLock(LoMod))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMod:locked!" << std::endl;
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMod:unlocked!" << std::endl;
    }

    return WT_OK;
}

int DevBusiness::ClearWorkMode()
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(CLEAR_WORK_MODE, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "ClearWorkMode ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevBusiness::SetBusiBoardFreq(int Freq /*(KHz)*/)
{
    int Ret = WT_OK;
    Freq_Parm *FreqParam;
    double BusiFreq = double(Freq) * KHz;
    bool Is400M = false;
    if (!Basefun::CompareDoubleAccuracy1K(BusiFreq, 400 * MHz))
    {
        Is400M = true;
        BusiFreq += MHz;
    }

    if (m_BoardType == DEV_TYPE_VSA)
    {
        Rx_Parm RXParm;
        RXParm.unit = m_ModId;
        RXParm.freq = BusiFreq;
        RXParm.sample_freq = DEFAULT_SMAPLE_RATE;
        RXParm.share_mode = (m_LOComMode == LO_IS_COM_MODE) ? 1 : 0;
        Ret = wt_calibration_get_rx_setting(&RXParm);
        FreqParam = &RXParm.freq_parm;
    }
    else
    {
        Tx_Parm TXParm;
        TXParm.unit = m_ModId;
        TXParm.freq = BusiFreq;
        TXParm.sample_freq = DEFAULT_SMAPLE_RATE;
        TXParm.share_mode = (m_LOComMode == LO_IS_COM_MODE) ? 1 : 0;
        Ret = wt_calibration_get_tx_setting(&TXParm);
        FreqParam = &TXParm.freq_parm;
    }

    if (Is400M)
    {
        BusiFreq -= MHz;
        FreqParam->LoParm[LoMod].freq -= 1;
        FreqParam->LoParm[LoMod].freq = FreqParam->LoParm[LoMod].freq > 0
                                            ? FreqParam->LoParm[LoMod].freq - 1
                                            : FreqParam->LoParm[LoMod].freq + 1;
    }

    if (Ret != WT_OK)
    {
        // TODO ...
        return WT_CAL_BASE_ERROR + Ret;
    }

    return SetFreq(BusiFreq, FreqParam, m_RunData.CurrentWorkMode);
}

int DevBusiness::GetXdmaStatus(int &Status)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(GET_XDMA_STATUS, sizeof(int), &Status)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_XDMA_STATUS ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevBusiness::BaseFpgaUpgrade(unsigned char *pData, unsigned int Len, int NotProgram)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "DevBusiness::BaseFpgaUpgrade Started\n");
    int Ret = WT_OK;
    DataBufType DataBuf;
    DataBuf.pDataBuf = pData;
    DataBuf.Len = Len;
    DataBuf.pFlashAddr = 0;
    int PageNum;
    int LastPageLen;
    int CurrentPage;

    TempLib::Instance().TempEnable(false);
    //占用CPU太久，导致看门狗警告，分成两段处理
    //配置flash芯片4Byte模式
    WTLog::Instance().WriteLog(LOG_DEBUG, "Set Flash 4Byte Mode Started\n");
    if ((Ret = DrvCmd(SET_FLASH_4BYTE_MODE, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_FLASH_4BYTE_MODE ioctl error");
        return Ret;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Flash Earse Started\n");
    //向驱动发擦除命令
    if ((Ret = DrvCmd(WRITE_BASE_FPGA_EARSE, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BASE_FPGA_EARSE ioctl error");
        return Ret;
    }
    usleep(100);
    
    if (NotProgram)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Flash Earse Finish...Exit\n");
        return Ret;
    }

#if 0       
    //一次写完全部Flash页
    if ((Ret = DrvCmd(WRITE_BASE_FPGA_UPGRADE, sizeof(DataBufType), &DataBuf)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_BASE_FPGA_UPGRADE ioctl error");
        return Ret;
    }

#else
    LastPageLen = DataBuf.Len % FLASH_PAGE_SIZE; //计算最后一页长度
    if (LastPageLen == 0)
        PageNum = DataBuf.Len / FLASH_PAGE_SIZE; //计算要写多少页
    else
        PageNum = DataBuf.Len / FLASH_PAGE_SIZE + 1;
    WTLog::Instance().WriteLog(LOG_DEBUG, "Flash Write page by page Started\n");
    //应用层多次调用 每次写一页
    for (CurrentPage = 1; CurrentPage <= PageNum; CurrentPage++)
    {
        //判断这页是否为最后一页 需要写多少赋值给DataBuf.Len
        DataBuf.Len = (CurrentPage == PageNum) ? LastPageLen : FLASH_PAGE_SIZE;
        if ((Ret = DrvCmd(WRITE_BASE_FPGA_WRITEONEPAGE, sizeof(DataBufType), &DataBuf)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "WRITE_BASE_FPGA_WRITEONEPAGE ioctl error");
            return Ret;
        }
        DataBuf.pFlashAddr += FLASH_PAGE_SIZE;
        DataBuf.pDataBuf = (unsigned char *)DataBuf.pDataBuf + FLASH_PAGE_SIZE;
        std::this_thread::yield();
    }
#endif
    WTLog::Instance().WriteLog(LOG_DEBUG, "Fpga reload Started\n");
    //FPGA重加载
    if ((Ret = DrvCmd(FPGA_RELOAD, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "FPGA_RELOAD ioctl error");
        return Ret;
    }

    TempLib::Instance().TempEnable(true);
    return WT_OK;
}

int DevBusiness::SetAttConfigMode(int ConfigMode)
{
    m_AttConfigMode = ConfigMode;
    return WT_OK;
}

int DevBusiness::GetAttConfigMode(int &ConfigMode)
{
    ConfigMode = m_AttConfigMode;
    return WT_OK;
}

int DevBusiness::SetModLoRefSel(int Data)
{
    if (Data < REF_100M || Data > REF_VB_MAX)
    {
        return WT_ARG_ERROR;
    }
    m_RunData.ModLoRefSel = Data;
    return SetDDSChannel(static_cast<unsigned long long>(m_RunData.ModLoRefSel));
}

int DevBusiness::GetModLoRefSel(int &Data)
{
    Data = m_RunData.ModLoRefSel;
    return WT_OK;
}

int DevBusiness::SetATTCalConfig(ATTCalConfigType &Config)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(SET_ATT_CAL_CONFIG, sizeof(ATTCalConfigType), &Config)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_ATT_CAL_CONFIG ioctl error");
        return Ret;
    }
    return Ret;
}

int DevBusiness::LOComModeInit()
{
    int Ret = WT_OK;
    //初始化共本振模式
    m_ShareLOBoardType = DEV_TYPE_VSG;
    Ret = SetLOComModeToDevice(GetDefaultLoMode());
    return Ret;
}

int DevBusiness::SetLOComModeToDevice(int Data)
{
    int Ret = WT_OK;
    if (!(m_HwInfo.LoHwVersion >= VERSION_D) && m_TesterType != HW_WT418)
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        RetAssert(Ret, "SetLOComModeToDevice ERORR Tester Version donnot support LO common mode");
    }
    if ((Ret = DrvCmd(Set_LO_COM_Mode, sizeof(int), &Data)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetLOComMode error");
        return Ret;
    }
    Ret = GetLOComModeFromDevice(m_LOComMode);
    RetAssert(Ret, "GetLOComModeFromDevice failed!");
    if(m_TesterType == HW_WT418)
    {
        if (Data == LO_IS_COM_MODE && m_BoardType != m_ShareLOBoardType)
        {
            BusiDown();
        }
        else if (Data == LO_IS_NOT_COM_MODE && m_BoardType != m_ShareLOBoardType && m_RunData.Status2 == WT_RX_TX_STATUS_DOWN)
        {
            m_RunData.LOCurData[LoMod].LastFreq = m_RunData.LOCurData[LoMod].Freq;
            m_RunData.LOCurData[LoMod].LastPowerLevel = m_RunData.LOCurData[LoMod].PowerLevel;
            m_RunData.LOCurData[LoMix].LastFreq = m_RunData.LOCurData[LoMix].Freq;
            m_RunData.LOCurData[LoMix].LastPowerLevel = m_RunData.LOCurData[LoMix].PowerLevel;
            Ret = ResetLO(LoMod);
            RetWarnning(Ret, "ResetLO failed!");
            Ret = ResetLO(LoMix);
            RetWarnning(Ret, "ResetLO failed!");

            m_RunData.Status2 = WT_RX_TX_STATUS_RUNNING;
        }
        else if (Data == LO_IS_COM_MODE && m_BoardType == m_ShareLOBoardType)
        {
            m_RunData.LOCurData[LoMod].LastFreq = m_RunData.LOCurData[LoMod].Freq;
            m_RunData.LOCurData[LoMod].LastPowerLevel = m_RunData.LOCurData[LoMod].PowerLevel;
            m_RunData.LOCurData[LoMix].LastFreq = m_RunData.LOCurData[LoMix].Freq;
            m_RunData.LOCurData[LoMix].LastPowerLevel = m_RunData.LOCurData[LoMix].PowerLevel;
            Ret = ResetLO(LoMod);
            RetWarnning(Ret, "ResetLO failed!");
            Ret = ResetLO(LoMix);
            RetWarnning(Ret, "ResetLO failed!");
        }
        else if (Data == LO_IS_NOT_COM_MODE && m_BoardType == m_ShareLOBoardType)
        {  
            // 关闭RFOUTB
            Ret = WriteLMX2594( 44, m_RunData.LOCurData[LoMix].R44);
            RetWarnning(Ret, "WriteLMX2594 failed!");
        }
    }
    else if (m_TesterType == HW_WT448||m_TesterType == HW_WT428)
    {
        if (Data == LO_IS_COM_MODE && m_BoardType == DEV_TYPE_VSA)
        {
            BusiDown();
        }
        else if (Data == LO_IS_NOT_COM_MODE && m_BoardType == DEV_TYPE_VSA && m_RunData.Status2 == WT_RX_TX_STATUS_DOWN)
        {
            m_RunData.LOCurData[LoMod].LastFreq = m_RunData.LOCurData[LoMod].Freq;
            m_RunData.LOCurData[LoMod].LastPowerLevel = m_RunData.LOCurData[LoMod].PowerLevel;
            m_RunData.LOCurData[LoMix].LastFreq = m_RunData.LOCurData[LoMix].Freq;
            m_RunData.LOCurData[LoMix].LastPowerLevel = m_RunData.LOCurData[LoMix].PowerLevel;
            Ret = ResetLO(LoMod);
            RetWarnning(Ret, "ResetLO failed!");
            Ret = ResetLO(LoMix);
            RetWarnning(Ret, "ResetLO failed!");
            m_RunData.Status2 = WT_RX_TX_STATUS_RUNNING;
        }
        else if (Data == LO_IS_COM_MODE && m_BoardType == DEV_TYPE_VSG)
        {
            m_RunData.LOCurData[LoMod].LastFreq = m_RunData.LOCurData[LoMod].Freq;
            m_RunData.LOCurData[LoMod].LastPowerLevel = m_RunData.LOCurData[LoMod].PowerLevel;
            m_RunData.LOCurData[LoMix].LastFreq = m_RunData.LOCurData[LoMix].Freq;
            m_RunData.LOCurData[LoMix].LastPowerLevel = m_RunData.LOCurData[LoMix].PowerLevel;
            Ret = ResetLO(LoMod);
            RetWarnning(Ret, "ResetLO failed!");
            Ret = ResetLO(LoMix);
            RetWarnning(Ret, "ResetLO failed!");
        }
        else if (Data == LO_IS_NOT_COM_MODE && m_BoardType == DEV_TYPE_VSG)
        {  
            // 关闭RFOUTB
            Ret = WriteLMX2820(LoMix, 79, ((m_RunData.LOCurData[LoMix].OutPwr << 1) & LMX2820R79MASK) | m_RunData.LOCurData[LoMix].R79);
            RetWarnning(Ret, "WriteLMX2820 failed!");
            Ret = WriteLMX2820(LoMod, 79, ((m_RunData.LOCurData[LoMod].OutPwr << 1) & LMX2820R79MASK) | m_RunData.LOCurData[LoMod].R79);
            RetWarnning(Ret, "WriteLMX2820 failed!");
        }
    }
    return Ret;
}
int DevBusiness::GetLOComModeFromDevice(int &Data)
{
    int Ret = WT_OK;
    
    if (!(m_HwInfo.LoHwVersion >= VERSION_D)&&m_TesterType != HW_WT418)
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        RetAssert(Ret, "GetLOComModeFromDevice ERORR LO Version do not support LO common mode");
    }
    if ((Ret = DrvCmd(Get_LO_COM_Mode, sizeof(int), &Data)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_SET_TBT_STA_PARAM ioctl error");
    }
    return Ret;
}
int DevBusiness::SetLOComMode(int Data)
{
    //设置共本振模式的功能对非共本振硬件无效
    int Ret = WT_OK;
    if (Data != m_LOComMode)
    {
        Ret = SetLOComModeToDevice(Data);
    }
    return Ret;
}

int DevBusiness::GetLOComMode(int &Data)
{
    //设置共本振模式的功能对非共本振硬件无效
    int Ret = WT_OK;
    Ret = GetLOComModeFromDevice(Data);
    return Ret;
}

int DevBusiness::SetLMX2820FreqPower(LO_ID_E LOId, double Freq /*MHz*/, int PowerLevel)
{
    if (m_BoardType != m_ShareLOBoardType && m_LOComMode == LO_IS_COM_MODE)
    {
        return WT_OK;
    }
    int Ret = WT_OK;
    int ChDiv = 1;          // VCO倍频或分频系数
    int ChDivIndex = -1;    // ChDiv的Index,=-1时表示不需重新配置ChDiv
    double PLL;             //整数分频
    double FreqVco;         // VCO频率
    int OutA_MUX = 1;       // VCO倍频或分频模式
    int Pll_N;              //整数分频值
    uint32 Pll_Mol;         //小数分频值的分子
    uint32 Pll_Instcal_num; // 2^32 × (PLL_NUM / PLL_DEN)
    uint32 Pll_Den = 0;         //小数分频值的分母
    int FreqPd = m_RunData.LOCurData[LOId].Fpd;
    int FoscSel = 0;
    double FreqDDS = FreqPd;
    int RefSel = (LOId == LoMix) ? REF_100M : m_RunData.ModLoRefSel;

    if (Basefun::CompareDouble(Freq, 0) > 0)
    {
        Ret = GetChDiv(Freq, ChDiv, ChDivIndex, LO_LMX2820);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "LMX2820 VCO Freq Compute Error!");
        // OutA_MUX: 0x0 = Channel divider, 0x1 = VCO, 0x2 = VCO doubler, 0x3 = Reserved
        OutA_MUX = ChDiv < 1 ? 2 : (ChDiv > 1 ? 0 : 1);
        FreqVco = Freq * ChDiv;

        switch (RefSel)
        {
        case REF_DDS_700M:
        case REF_DDS_900M:
            Pll_Den = Pll_Instcal_num = Pll_Mol = 0;
            Pll_N = floor(FreqVco / FreqPd);
            for (auto &iter : m_DDSRefFreqIndex)
            {
                if (Basefun::CompareDouble(FreqVco, iter.EndFreq, 1e-12) < 0)
                {
                    Pll_N = iter.Index;
                    break;
                }
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "=====Freq = %lf, FreqVco=%lf, Pll_N = %d\n", Freq, FreqVco, Pll_N);
            FreqDDS = Pll_N ? FreqVco / Pll_N : 0;
            FoscSel = FreqDDS > 800 ? 3 : (FreqDDS > 400 ? 2 : (FreqDDS > 200 ? 1 : 0));
            break;
        default:
            FoscSel = 0;
            FreqDDS = FreqPd;
            PLL = FreqVco / FreqPd;
            Pll_N = floor(PLL);
            WTLog::Instance().WriteLog(LOG_DEBUG, "=====Freq = %lf, FreqVco=%lf, PLL = %.9lf, Pll_N = %d\n", Freq, FreqVco, PLL, Pll_N);
            uint64 Fpd = llround(FreqPd * MHz);
            uint64 FVco = llround(FreqVco * MHz);
            uint64 FGcd = Gcd(FVco, Fpd);
            if (Basefun::CompareDouble(ceil(Fpd / FGcd), pow(2, 32)) < 0)
            {
                Pll_Den = floor(Fpd / FGcd);
            }
            else
            {
                Pll_Den = m_RunData.LOCurData[LOId].PllDen;
            }
            Pll_Mol = round((PLL - Pll_N) * Pll_Den);
            Pll_Instcal_num = Pll_Mol;
            WTLog::Instance().WriteLog(LOG_DEBUG, "=====FVco = %lld, Fpd = %lld, FGcd=%lld, Pll_Mol=%d, Pll_Den = %d\n", FVco, Fpd, FGcd, Pll_Mol, Pll_Den);
            break;
        }
    }
    else
    {
        FreqVco = 0;
        Pll_N = Pll_Mol = Pll_Instcal_num = 0;
        OutA_MUX = 1;
    }

    int MashOrder = Pll_Mol
                        ? (LOId == LoMod ? m_RunData.ModMashOrder : m_RunData.MixMashOrder)
                        : MASH_ORDER_INTEGER;
#ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Freq=" << Freq << ", Pll_N=" << Pll_N << ", Pll_Mol=" << Pll_Mol << std::endl;
#endif

    if (LOId == LoMod)
    {
        Ret = SetDDSFreq(FreqDDS);
        RetAssert(Ret, "SetDDSFreq failed!");
        Ret = SetDDSFsCurrent(PowerLevel);
        RetWarnning(Ret, "SetDDSFsCurrent failed!");
    }

    // 0中频时，混频本振不下电，规避本振不锁的问题
    if (Pll_N == 0)
    {
        return WT_OK;
    }

    //增益采用默认配置，先不设置
    (void)PowerLevel;

    Ret = WriteLMX2820(LOId, 36, Pll_N & 0x7FFF);
    RetAssert(Ret, "WriteLMX2820 failed!");

    if (RefSel == REF_100M)
    {
        Ret = WriteLMX2820(LOId, 38, (Pll_Den >> 16) & 0xFFFF);
        RetAssert(Ret, "WriteLMX2820 failed!");
        Ret = WriteLMX2820(LOId, 39, Pll_Den & 0xFFFF);
        RetAssert(Ret, "WriteLMX2820 failed!");

        Ret = WriteLMX2820(LOId, 42, (Pll_Mol >> 16) & 0xFFFF);
        RetAssert(Ret, "WriteLMX2820 failed!");
        Ret = WriteLMX2820(LOId, 43, Pll_Mol & 0xFFFF);
        RetAssert(Ret, "WriteLMX2820 failed!");

        Ret = WriteLMX2820(LOId, 44, (Pll_Instcal_num >> 16) & 0xFFFF);
        RetAssert(Ret, "WriteLMX2820 failed!");
        Ret = WriteLMX2820(LOId, 45, Pll_Instcal_num & 0xFFFF);
        RetAssert(Ret, "WriteLMX2820 failed!");
        //参考输入倍频开启：R11 = 0x612
        Ret = WriteLMX2820(LOId, 11, 0x612);
        RetAssert(Ret, "WriteLMX2820 failed!");
    }
    else
    {   //参考输入倍频关闭：R11 = 0x602
        Ret = WriteLMX2820(LOId, 11, 0x602);
        RetAssert(Ret, "WriteLMX2820 failed!");
    }

    if (m_LOComMode == LO_IS_COM_MODE)
    {
        if (m_RunData.LOCurData[LOId].OutMux != OutA_MUX)
        {
            m_RunData.LOCurData[LOId].OutMux = OutA_MUX;
            Ret = WriteLMX2820(LOId, 78, m_RunData.LOCurData[LOId].OutMux);
            RetAssert(Ret, "WriteLMX2820 failed!");
            Ret = WriteLMX2820(LOId, 79, (m_RunData.LOCurData[LOId].OutMux << 4) | (m_RunData.LOCurData[LOId].OutPwr << 1));
            RetAssert(Ret, "WriteLMX2820 failed!");
        }
        if (ChDivIndex >= 0 && m_RunData.LOCurData[LOId].ChDivIndex != ChDivIndex)
        {
            m_RunData.LOCurData[LOId].ChDivIndex = ChDivIndex;
            Ret = WriteLMX2820(LOId, 32, (m_RunData.LOCurData[LOId].ChDivIndex << 6) | (m_RunData.LOCurData[LOId].ChDivIndex << 9) | m_RunData.LOCurData[LOId].R32);
            RetAssert(Ret, "WriteLMX2820 failed!");
        }
    }
    else
    {
        if (OutA_MUX != m_RunData.LOCurData[LOId].OutMux)
        {
            Ret = WriteLMX2820(LOId, 78, OutA_MUX);
            RetAssert(Ret, "WriteLMX2820 failed!");
            m_RunData.LOCurData[LOId].OutMux = OutA_MUX;
        }

        if (ChDivIndex >= 0 && ChDivIndex != m_RunData.LOCurData[LOId].ChDivIndex)
        {
            Ret = WriteLMX2820(LOId, 32, (ChDivIndex << 6) | m_RunData.LOCurData[LOId].R32);
            RetAssert(Ret, "WriteLMX2820 failed!");
            m_RunData.LOCurData[LOId].ChDivIndex = ChDivIndex;
        }
    }

    if (MashOrder != m_RunData.LOCurData[LOId].MashOrder)
    {
        Ret = WriteLMX2820(LOId, 35, (MashOrder << 7) | m_RunData.LOCurData[LOId].R35);
        RetAssert(Ret, "WriteLMX2820 failed!");
    }

    Ret = WriteLMX2820(LOId, 2, (FoscSel << 12) | m_RunData.LOCurData[LOId].R2);
    RetAssert(Ret, "WriteLMX2820 failed!");

    int FpdSel = FreqDDS > 200 ? 3 : (FreqDDS > 150 ? 2 : (FreqDDS > 100 ? 1 : 0));
    Ret = WriteLMX2820(LOId, 0, (FpdSel << 9) | m_RunData.LOCurData[LOId].R0);
    RetAssert(Ret, "WriteLMX2820 failed!");
    return WT_OK;
}

int DevBusiness::GetDefaultLoMode()
{
    //初始化共本振模式
    Json::Value RegSetting = (m_TesterType == HW_WT418)? m_JsonRoot["LO_SETTING_WT418_VA"]:m_JsonRoot["LO_SETTING_VC"];
    return RegSetting["LOComMode"].asUInt() < static_cast<int>(LO_COM_MODE_IS_UNKNOWED)
                      ? RegSetting["LOComMode"].asUInt()
                      : static_cast<int>(LO_IS_NOT_COM_MODE);
}

int DevBusiness::ReSetLoMode()
{    
    int Ret = WT_OK;
    if (m_HwInfo.LoHwVersion >= VERSION_D||m_TesterType == HW_WT418)
    {
        if(m_LOComMode != GetDefaultLoMode())
        {
            Ret = LOComModeInit();   
        }
    }
    return Ret;
}

int DevBusiness::CheckIQOffset(int Data)
{
    if (Data > MAX_DAC_CODE)
    {
        return MAX_DAC_CODE;
    }
    else if (Data < MIN_DAC_CODE)
    {
        return MIN_DAC_CODE;
    }
    else
    {
        return Data;
    }
}

#define LDPC_DEBUG 1
int DevBusiness::LdpcCompute(LdpcParamType *LdpcParam, int Count)
{
    unique_lock<std::mutex>  Lock(m_HwDecodeMutex);
#if LDPC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======LdpcCompute:: Count = %d\n", Count);
#endif
    if(Count != 1)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "LdpcCompute Count error");
        return WT_ARG_ERROR;
    }
#if LDPC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======LdpcCompute:: open WriteFd\n");
#endif
    std::string DeviceNameW = Xdmafun::GetH2CDeviceName(m_ModId, 1);
    int WriteFd = open(DeviceNameW.c_str(), O_RDWR);
    if (WriteFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma h2c device");
        return WT_FILE_OPEN_ERROR;
    }
#if LDPC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======LdpcCompute:: open ReadFd\n");
#endif
    std::string DeviceNameR = Xdmafun::GetC2HDeviceName(m_ModId, 1);
    int ReadFd = open(DeviceNameR.c_str(), O_RDWR);
    if (ReadFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }
#if LDPC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======LdpcCompute:: start\n");
#endif
    int Ret = WT_OK;
    for (int i = 0; i < Count; i++)
    {
#if LDPC_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(LdpcParam[i].Mode) << Pout(LdpcParam[i].LLRLen) << Pout(LdpcParam[i].Iteration) <<endl;
#endif
        WriteDirectReg(HW_DECODE_MODE, HW_DECODE_MODE_LDPC);
        WriteDirectReg(LDPC_MODE_INDEX, LdpcParam[i].Mode);
        WriteDirectReg(LDPC_LLR_LENGHT, LdpcParam[i].LLRLen);
        WriteDirectReg(LDPC_ITERATION, LdpcParam[i].Iteration);
        WriteDirectReg(LDPC_STATUS, 0x1);
        WriteDirectReg(LDPC_STATUS, 0x0);

        ssize_t rc = Xdmafun::Instance().WriteFromBuffer(DeviceNameW.c_str(), WriteFd,
                                                         LdpcParam[i].InData, LdpcParam[i].InDataLen, 0);
#if LDPC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "Mod%d XDMA write size=%d, actual size=%ld\n", m_ModId, LdpcParam[i].InDataLen, rc);
#endif
        if (rc < LdpcParam[i].InDataLen)
        {
            Ret = WT_XMDA_WR_ERROR;
            RetBreak(Ret, "LdpcCompute write Xdma error");
        }

        // 查询，等待发送完成
        int Finish = 0;
        int ReadCount = 0;
        do
        {
            if (ReadCount++ > 1000)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "ReadDirectReg %#x = %#x\n", LDPC_STATUS, Finish);
                WTLog::Instance().LOGERR(WT_WAIT_TIMEOUT, "ReadCount > 1000!");
                return WT_WAIT_TIMEOUT;
            }
            ReadDirectReg(LDPC_STATUS, Finish);
        } while ((Finish & 0x02) != 0x02);

        rc = Xdmafun::Instance().ReadToBuffer(DeviceNameR.c_str(), ReadFd,
                                              LdpcParam[i].OutData, LdpcParam[i].OutDataLen, 0);
#if LDPC_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "Mod%d XDMA read size=%d, actual size=%ld\n", m_ModId, LdpcParam[i].OutDataLen, rc);
#endif
        if (rc < LdpcParam[i].OutDataLen)
        {
            Ret = WT_XMDA_WR_ERROR;
            RetBreak(Ret, "LdpcCompute read Xdma error");
        }
    }

    close(WriteFd);
    close(ReadFd);
    return WT_OK;
}

int DevBusiness::SetHMC833FreqPower(double Freq, int PowerLevel)
{
    if (m_BoardType != m_ShareLOBoardType && m_LOComMode == LO_IS_COM_MODE && (Freq != 0 || PowerLevel != 0))
    {
        return WT_OK;
    }
    LOFreqAttr  LOFreqAttrTemp;
    int Ret = WT_OK;
    int SleepFlag = false;
    int Ref = 100;
    Json::Value Fxtal = m_JsonRoot["HMC833_PD_Config"]["Fxtal"];
    Json::Value FxtalR = m_JsonRoot["HMC833_PD_Config"]["FxtalR"];
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"SetHMC833FreqPower: "<<" Freq = "<<Freq<<"PowerLevel = "<<PowerLevel<<std::endl;
    LOFreqAttrTemp.R = 2;
    if (Fxtal.isInt() && FxtalR.isInt())
    {
        Ref = Fxtal.asInt();
        LOFreqAttrTemp.R = FxtalR.asInt();
    }

    LOFreqAttrTemp.fOut = Freq;
    if (LOFreqAttrTemp.fOut > 6000)
    {
        WTLog::Instance().LOGERR(WT_FREQ_OVER_RANGE, "WT_FREQ_OVER_RANGE Freq > 6000M!");
        return WT_FREQ_OVER_RANGE;
    }

    LOFreqAttrTemp.fPD = ((double)Ref) / ((double)LOFreqAttrTemp.R); // 鉴相器频率
    LOFreqAttrTemp.k = GetKValue(LOFreqAttrTemp.fOut);//?
    LOFreqAttrTemp.fVCO = LOFreqAttrTemp.fOut * LOFreqAttrTemp.k; // VCO频率
#if 1
    if (LOFreqAttrTemp.fVCO != 0)
    {
        uint64 Fpd = llround(LOFreqAttrTemp.fPD * MHz);
        uint64 FVco = llround(LOFreqAttrTemp.fVCO * MHz);
        uint64 FGcd = Gcd(FVco, Fpd);

        if (FGcd < ceil(Fpd / pow(2, 14)))
        {
            LOFreqAttrTemp.ChannleCnt = 0;
        }
        else
        {
            LOFreqAttrTemp.ChannleCnt = ceil((Fpd / FGcd));
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "frequency=%lf Fpd=%lld,FVco=%lld,k=%lf,FGcd=%lld, ChannleCnt=%d\n", Freq, Fpd, FVco, LOFreqAttrTemp.k, FGcd, LOFreqAttrTemp.ChannleCnt);
    }
    else
    {
        LOFreqAttrTemp.ChannleCnt = ceil(LOFreqAttrTemp.fPD / 0.01); /* 间隔为10kHz */
    }
#else
    LOFreqAttrTemp.ChannleCnt = ceil(LOFreqAttrTemp.fPD / 0.1);
#endif
    LOFreqAttrTemp.N = LOFreqAttrTemp.fVCO / LOFreqAttrTemp.fPD;
    LOFreqAttrTemp.Intg = (int)LOFreqAttrTemp.N; // 整数分频
    LOFreqAttrTemp.Frac = (int)ceil(((1 << 24) * (LOFreqAttrTemp.fVCO - ((int)LOFreqAttrTemp.fPD) * LOFreqAttrTemp.Intg)) / LOFreqAttrTemp.fPD);
    LOFreqAttrTemp.RfGain = PowerLevel;

    if(m_ListVirtualCfgMode)
    {
        Ret = SetHMC833FreqAttrListMode(LOFreqAttrTemp, SleepFlag);
    }
    else
    {
        Ret = SetHMC833FreqAttr(LOFreqAttrTemp, SleepFlag);
    }
    RetAssert(Ret, "SetLOFreqAttr failed!");

    return WT_OK;
}

int DevBusiness::SetHMC833FreqAttr(const LOFreqAttr &FreqAttr, int SleepFlag)
{
    int Ret = 0;
    int Value = 0;
    int VCOValue = 0;           //Enable doubler, Auto RFO mode
    int SleepTime = 10;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "set board=" << m_BoardType << ",  modid=" << m_ModId << ",  Powerlevel=" << FreqAttr.RfGain << std::endl;
#endif
    //Read VCO Switch Setting
    Ret = ReadHMC833Reg(0x10, Value);
    RetAssert(Ret, "SetHMC833FreqAttr ReadHMC833Reg 0x10 failed!");

    // VCO sub-band selection
    Ret = WriteHMC833VCOReg(0, (Value & 0x1FF) << 1);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833VCOReg failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(2, FreqAttr.R);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 2 failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(0xC, FreqAttr.ChannleCnt);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 0xc failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(3, FreqAttr.Intg);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 3 failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(4, FreqAttr.Frac);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 4 failed!");

    Value = FreqAttr.RfGain << 6;                  //RF output buffer gain control, 3(Max Gain)
    if((FreqAttr.k < 1) && (FreqAttr.k > 0))
    {
        Value |= 1;                                 //RF Divider Ratio
    }
    else
    {
        Value |= (int)(FreqAttr.k);                 //RF Divider Ratio
        VCOValue = (2 << 5) | (2 << 3) | 0x1;       //Enable fundamental mode,RF buffer bias,Spare
    }

    //配置VCO_Reg02h
    Ret = WriteHMC833VCOReg(2, Value);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833VCOReg 2 failed!");

    //配置VCO_Reg03h,Enable doubler, Auto RFO mode
    Ret = WriteHMC833VCOReg(3, VCOValue);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833VCOReg 3 failed!");

    return WT_OK;
}

int DevBusiness::SetHMC833FreqAttrListMode(const LOFreqAttr &FreqAttr, int SleepFlag)
{
    int Ret = 0;
    int Value = 0;
    int VCOValue = 0;           //Enable doubler, Auto RFO mode
    int SleepTime = 10;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "set board=" << m_BoardType << ",  modid=" << m_ModId << ",  Powerlevel=" << FreqAttr.RfGain << std::endl;
#endif

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(2, FreqAttr.R);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 2 failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(0xC, FreqAttr.ChannleCnt);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 0xc failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(3, FreqAttr.Intg);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 3 failed!");

    if (SleepFlag) usleep(SleepTime);
    Ret = WriteHMC833Reg(4, FreqAttr.Frac);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833Reg 4 failed!");

    Value = FreqAttr.RfGain << 6;                  //RF output buffer gain control, 3(Max Gain)
    if((FreqAttr.k < 1) && (FreqAttr.k > 0))
    {
        Value |= 1;                                 //RF Divider Ratio
    }
    else
    {
        Value |= (int)(FreqAttr.k);                 //RF Divider Ratio
        VCOValue = (2 << 5) | (2 << 3) | 0x1;       //Enable fundamental mode,RF buffer bias,Spare
    }

    //配置VCO_Reg02h
    Ret = WriteHMC833VCOReg(2, Value);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833VCOReg 2 failed!");

    //配置VCO_Reg03h,Enable doubler, Auto RFO mode
    Ret = WriteHMC833VCOReg(3, VCOValue);
    RetAssert(Ret, "SetHMC833FreqAttr WriteHMC833VCOReg 3 failed!");

    return WT_OK;
}

int DevBusiness::WriteHMC833VCOReg(int Addr, int Data)
{
    int Value  = 0;

    if(Addr < 0 || Addr > 6)
    {
        return WT_LO_VCO_ADDR_ERROR;
    }

    //配置VCO寄存器
    Value |= Addr << 3;         //VCO_REGADDR
    Value |= Data << 7;         //VCO_DATA

    return WriteHMC833Reg(5, Value);
}

#define BCC_DEBUG 0
int DevBusiness::BccCompute(const BccParamType &BccParam)
{
    unique_lock<std::mutex>  Lock(m_HwDecodeMutex);
#if BCC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======BccCompute:: open WriteFd\n");
#endif
    std::string DeviceNameW = Xdmafun::GetH2CDeviceName(m_ModId, 1);
    int WriteFd = open(DeviceNameW.c_str(), O_RDWR);
    if (WriteFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma h2c device");
        return WT_FILE_OPEN_ERROR;
    }
#if BCC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======BccCompute:: open ReadFd\n");
#endif
    std::string DeviceNameR = Xdmafun::GetC2HDeviceName(m_ModId, 1);
    int ReadFd = open(DeviceNameR.c_str(), O_RDWR);
    if (ReadFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }
#if BCC_DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "=======BccCompute:: start\n");
#endif
    int Ret = WT_OK;
    do
    {
        const int BccCompensate = 420;
        int InDataLen = (BccParam.InDataLen + BccCompensate + 15) & (~15); // dma传输要求,补偿196字节后对齐到16bytes

        std::unique_ptr<char[]> CompensateBuf(new (std::nothrow) char[InDataLen]());
        memcpy(CompensateBuf.get(), BccParam.InData, BccParam.InDataLen);

        auto WriteXdma = [&]() -> void
        {
            WriteDirectReg(HW_DECODE_MODE, HW_DECODE_MODE_BCC);
            ssize_t rc = Xdmafun::Instance().WriteFromBuffer(DeviceNameW.c_str(), WriteFd,
                                                             CompensateBuf.get(), InDataLen, 0);
#if BCC_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "Mod%d XDMA write size=%d, offset=%d, actual size=%ld\n",
                   m_ModId, InDataLen, 0, rc);
#endif
            if (rc < InDataLen)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "Mod%d XDMA write size=%d, actual size=%ld\n", m_ModId, InDataLen, rc);
                Ret = WT_XMDA_WR_ERROR;
                RetWarnning(Ret, "BccCompute write Xdma error");
            }
        };

        auto ReadXdma = [&]() -> void
        {
            ssize_t rc = Xdmafun::Instance().ReadToBuffer(DeviceNameR.c_str(), ReadFd,
                                                  BccParam.OutData, BccParam.OutDataLen, 0);
            WriteDirectReg(BCC_DECODE_FINISH, 0);
            WriteDirectReg(BCC_DECODE_FINISH, 1);
#if BCC_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "Mod%d XDMA read size=%d, actual size=%ld\n", m_ModId, BccParam.OutDataLen, rc);
#endif
            if (rc < BccParam.OutDataLen)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "Mod%d XDMA read size=%d, actual size=%ld\n", m_ModId, BccParam.OutDataLen, rc);
                Ret = WT_XMDA_WR_ERROR;
                RetWarnning(Ret, "BccCompute read Xdma error");
            }
        };

        std::thread ThreadWrite(WriteXdma);
        std::thread ThreadRead(ReadXdma);
        ThreadWrite.join();
        ThreadRead.join();
    } while (0);

    close(WriteFd);
    close(ReadFd);
    return Ret;
}

int DevBusiness::SetListModeStatus(int Status)
{
    int Ret = WT_OK;
    m_ListMode = Status;
    if(!m_ListMode)
    {
        m_ListVirtualCfgMode = false;
    }
    int SwitchListmode = ((m_BoardType << 1) | Status);
    if ((Ret = DrvCmd(SET_SWITCH_MODE, sizeof(int), &SwitchListmode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetListModeStatus ioctl error");
        return Ret;
    }
    if ((Ret = DrvCmd(SET_LIST_MODE, sizeof(int), &Status)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetListModeStatus ioctl error");
        return Ret;
    }

    return Ret;
}

int DevBusiness::InitVirtualAddr()
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(INIT_VIRTUAL_ADDR, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "InitlistSegConfig ioctl error");
        return Ret;
    }
    return Ret;
}

int DevBusiness::RecordlistSegConfig(int Port)
{
    int Ret = WT_OK;
    m_ListVirtualCfgMode = true;
    if ((Ret = DrvCmd(SET_VIRTUAL_ADDR_RECORD, sizeof(Port), &Port)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "RecordlistSegConfig ioctl error");
        return Ret;
    }

    return Ret;
}

int DevBusiness::SetlistSegConfig(int SegmentIndex, int SegmentPort)
{
    int Ret = WT_OK;
    struct Seq_Seg_ParamType Param;
    Param.SegmentPort = SegmentPort;
    Param.SegmentIndex = SegmentIndex;
    m_ListVirtualCfgMode = false;
    if ((Ret = DrvCmd(SET_VIRTUAL_ADDR_CONFIG, sizeof(Param), &Param)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetlistSegConfig ioctl error");
        return Ret;
    }

    return Ret;
}

int DevBusiness::SetTrigComParam(CommonTrigType &TrigComParam)
{
    int Ret = WT_OK;
    WTLog::Instance().WriteLog(LOG_DEBUG, "TrigComParam.TriggerTimeoutCnt = %llu\n", TrigComParam.TriggerTimeoutCnt);
    WTLog::Instance().WriteLog(LOG_DEBUG, "TrigComParam.OutsideTriggerValidLen = %d\n", TrigComParam.OutsideTriggerValidLen);
    WTLog::Instance().WriteLog(LOG_DEBUG, "TrigComParam.VsgSeqTrigRepeatRum = %d\n", TrigComParam.VsgSeqTrigRepeatRum);

    if ((Ret = DrvCmd(SET_SEQUENCE_TRIG_COM_PARAM, sizeof(CommonTrigType), &TrigComParam)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetTrigComParam ioctl error");
        return Ret;
    }

    return Ret;
}

int DevBusiness::SetTrigLoopParam(int Loop)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(SET_SEQUENCE_TRIG_LOOP_PARAM, sizeof(Loop), &Loop)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetTrigLoopParam ioctl error");
        return Ret;
    }

    return Ret;
}

int DevBusiness::SetSeqSegTime(SeqTimeType SeqTimeParam, double SamplingRate)
{
    int Ret = WT_OK;
    struct Seq_Seg_TimeParamType CntParam;
    CntParam.DurationCnt = round(SeqTimeParam.Duration * MAX_SMAPLE_RATE);
    CntParam.MeaoffsetCnt = round(SeqTimeParam.Meaoffset * SamplingRate);
    CntParam.MeadurationCnt = round(SeqTimeParam.Meadura * SamplingRate);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DevBusiness::SetSeqSegTime : " \
    << "Duration = " << SeqTimeParam.Duration \
    << "Meaoffset = " << SeqTimeParam.Meaoffset \
    << "Meadura = " << SeqTimeParam.Meadura \
    << std::endl;

    if ((Ret = DrvCmd(SET_SEQUENCE_SEGMENT_TIME, sizeof(CntParam), &CntParam)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetSeqSegTime ioctl error");
        return Ret;
    }

    return Ret;
}

int DevBusiness::WriteDmaData(int Channel, char* Buf, int Len)
{
    int Ret = WT_OK;
    if (m_TrigParamDmaBuf == NULL)
    {
        return WT_ALLOC_FAILED;
    }

    std::string DeviceName = Xdmafun::GetH2CDeviceName(m_ModId, Channel);
    int FpgaFd = open(DeviceName.c_str(), O_RDWR);
    if (FpgaFd < 0)
    {
        // free(pPnItem);
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }
    do
    {
        if(Len > DMA_BUF_SIZE)
        {
            Len = DMA_BUF_SIZE;
        }

        ssize_t rc = 0;
        rc += Xdmafun::Instance().WriteFromBuffer(DeviceName.c_str(), FpgaFd, Buf, Len, 0);
        WTLog::Instance().WriteLog(LOG_DEBUG, "WriteDmaData ModId%d XDMA write size=%d, actual size=%ld\n", m_ModId, Len, rc);

        if (Ret != WT_OK)
        {
            Ret = WT_XMDA_STATUS_TIMEOUT;
            RetBreak(Ret, "SetPNItem wait fpga xdma finish timeout");
        }
        else if (rc < 0 || rc != Len)
        {
            Ret = WT_XMDA_WR_ERROR;
            RetBreak(Ret, "SetPNItem write Xdma error");
        }
        m_RunData.XdmaStatus = WT_XDMA_WR_FINISH;
    } while (0);

    close(FpgaFd);
    return Ret;
}

int DevBusiness::GetFPGAResampleIndex(const double SampleRate)
{
    int ResampleIndex = 0;
    const static int ResampleMap[] = {480000000,240000000, 120000000, 60000000, 30000000, 15000000, 7500000, 122880000, 61440000, 30720000};
    //采样率
    for (int i = 0; i < sizeof(ResampleMap)/sizeof(int); i++)
    {
        if (SampleRate == ResampleMap[i])
        {
            ResampleIndex = i;
            break;
        }
    }
    return ResampleIndex;
}

void DevBusiness::SaveDmaData(int Channel, int ChannelType,void *Buf, int Len)
{
    if(!m_SaveDmaDataFlag)
    {
        return;
    }
    string filePath = WTConf::GetDir() + "/fpgadata/";    // 检查并创建目录
    struct stat FileStat;

    if ((stat(filePath.c_str(), &FileStat) != 0) || !S_ISDIR(FileStat.st_mode))
    {
        if (-1 == mkdir(filePath.c_str(), 0755)) // 建立目录
        {
            WTLog::Instance().LOGERR(WT_ERROR, "Create Dir Failed!");
        }
    }

    // 检查文件夹下的文件数量，如果达到指定数量MaxCnt，就先删除最旧的一个信号文件再保存~
    int Cnt = 0;
    std::string result = Basefun::shell_exec((std::string("ls -l ") + filePath + std::string(" | wc -l")).c_str());
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << PoutN(result);
    if (result.length() > 0 && std::isdigit(result.at(0)))
    {
        Cnt = std::stoi(result) - 1;
        if (Cnt >= 30)
        {
            int last = Cnt - 20;
            stringstream bash_cmd;
            bash_cmd << std::string("cd ");
            bash_cmd << filePath;
            bash_cmd << std::string(" && ");
            bash_cmd << std::string("ls -tr");
            bash_cmd << std::string(" | head -");
            bash_cmd << last;
            bash_cmd << std::string(" | xargs rm");
            Basefun::LinuxSystem(bash_cmd.str().c_str());
        }
    }

    struct timeval timetemp;
    gettimeofday(&timetemp, NULL);
    string DataType;
    switch (ChannelType)
    {
    case 0:
        DataType = "VSA_TRIGGER";
        break;
    case 1:
        DataType = "VSA_HW_PARAM";
        break;
    case 2:
        DataType = "VSG_ARB";
        break;
    case 3:
        DataType = "VSG_BEHAVIOR";
        break;
    case 4:
        DataType = "VSG_TRIGGER";
        break;
    case 5:
        DataType = "VSG_HW_PARAM";
        break;
    default:
        DataType = "?";
    }
    string SavePath = string(filePath) + "dma" + to_string(Channel) + "_" + DataType + "_data_" + to_string((long)(timetemp.tv_sec * 1e6 + timetemp.tv_usec)) + ".bin";
    FILE *fp = fopen(SavePath.c_str(), "wb");
    if (fp)
    {
        int pos = 0;
        while (pos < Len)
        {
            for (int i = 15; i >= 0; --i)
            {
                fwrite((unsigned char *)Buf + pos + i, 1, 1, fp);
            }
            pos += 16;
        }
        //fwrite(Buf, 1, Len, fp);
        WTLog::Instance().WriteLog(LOG_DEBUG, "Save DmaData %s len=%d\n", SavePath.c_str(), Len);
        fclose(fp);
    }
}

int DevBusiness::ResetDmaFIFO()
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(VSG_RESET_DMA_FIFO, 0, NULL)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "ResetDmaFIFO ioctl error");
        return Ret;
    }
    return WT_OK;
}