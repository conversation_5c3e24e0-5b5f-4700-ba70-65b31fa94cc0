#ifndef _VIRTUAL_ADDR_LIST_H_
#define _VIRTUAL_ADDR_LIST_H_

#define VIRTUAL_DRECT_REG_LIST_LENGTH 1024
#define VIRTUAL_HMC833_REG_LIST_LENGTH 100
#define VIRTUAL_LMX2594_REG_LIST_LENGTH 100
#define VIRTUAL_ADCORDAC_REG_LIST_LENGTH 100

struct dev_virtual_addr_struct
{
    spinlock_t dev_virtual_addr_lock;//读写dev_virtual_addr用的自旋锁
    int VirtualAddrMode;
    unsigned int VirtualDrectRegList[VIRTUAL_DRECT_REG_LIST_LENGTH];
    unsigned int HMC833ConfigList[VIRTUAL_HMC833_REG_LIST_LENGTH];
    unsigned int HMC833ConfigCount;
    unsigned int LMX2594ConfigList[VIRTUAL_LMX2594_REG_LIST_LENGTH];
    unsigned int LMX2594ConfigCount;
    unsigned int AdcOrDacConfigList[VIRTUAL_ADCORDAC_REG_LIST_LENGTH];
};

extern int wt_read_direct_reg_virtual(struct dev_virtual_addr_struct dev_virtual_addr, int RegAddr);

extern void wt_write_direct_reg_virtual(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value);

extern int wt_read_HMC833_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index);

extern int wt_get_HMC833_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr);

extern void wt_set_HMC833_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count);

extern void wt_write_HMC833_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value);

extern int wt_read_LMX2594_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index);

extern void wt_write_LMX2594_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value);

extern int wt_get_LMX2594_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr);

extern void wt_set_LMX2594_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count);

extern int wt_read_AdcOrDac_reg_virtual(struct dev_virtual_addr_struct dev_virtual_addr, int RegAddr);

extern void wt_write_AdcOrDac_reg_virtual(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value);

extern void SetVirtualAddrMode(struct dev_virtual_addr_struct *dev_virtual_addr, int mode);

extern int GetVirtualAddrMode(struct dev_virtual_addr_struct dev_virtual_addr);

extern int GetVirtualSwitchMask(int type, int port, int RegId);

extern void InitVirtualAddr(struct dev_virtual_addr_struct *dev_virtual_addr);


#endif //_VIRTUAL_ADDR_LIST_H_