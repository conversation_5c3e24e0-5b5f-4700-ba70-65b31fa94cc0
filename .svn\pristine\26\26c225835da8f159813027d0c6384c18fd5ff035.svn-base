#ifndef __DIGITAL_IQ_H__
#define __DIGITAL_IQ_H__

#include <string>

#include "digstruct.h"
#include "digtask.h"

// 数字IQ控制
class DigModeLib
{
public:
    enum TESTER_DATA_DIG_MODE
    {
        DIG_SINGEL_ZONE_MODE,
        DIG_MULTIPLE_ZONE_MODE,
    };

    static DigModeLib &Instance(void);

    bool IsDigMode(void);
    void SetRunMode(int Mode);
    void SetDigTestMode(DigDutModeType &DigDutMode);
    int GetChannelMode() { return m_ChannelMode; }
private:
    DigModeLib();
    ~DigModeLib() {}
    int GetJsonItemData(std::string fileName, std::string item, int &value) const;

private:
    int m_RunMode;
    int m_ChannelMode;
};

// 数字IQ控制
class DigitalLib
{
public:
    int VSGSetParam(VsgDigConfigType Config);
    int SetPNItem(const std::vector<DigPnItem> &PnItemVector, int ChannelId);
    int SetPNItemHardDisk(const std::vector<DigPnItem> &PnItemVector, int FrameId);
    void SetDstMac(uint8_t *MacArray);
    int GetVsgSendCnt(void);

    int VSASetParam(VsaDigConfigType Config);
    int CaptureData(void *pBuf, uint32_t Size, int ChannelId, int ChannelTotal);
    int VSAGetChannelDataLen(void);

    int Start(int Type);
    int Stop(int Type);
    int GetStatus(int Type);

    int GetTBTApSIFS(std::vector<double> &SIFS);
    int GetVsaMaxBitsCnt(void);
    int GetVsgMaxBitsCnt(void);
    int GetPacketCnt(int Type);
    
private:
    DigTaskLib m_DigTask;
};

#endif