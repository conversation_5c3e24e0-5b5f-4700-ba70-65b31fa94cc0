//*****************************************************************************
//  File: wtlog.h
//  日志模块
//  Data: 2016.7.29
//*****************************************************************************
#ifndef __WT_LOG_H__
#define __WT_LOG_H__

#include <string>
#include <vector>
#include <mutex>
#include <sqlite3.h>
#include <thread>
#include <queue>
#include <condition_variable>
#include "wtev++.h"
#include <sstream>
#include <iostream>

//定义日志查询类型枚举
enum WT_LOG_QUERY_TYPE
{
    QueryByServerID,
    QueryByHardware,
    QueryByTime,
    QueryByOperate,
    QueryAll,
    QueryBySQL,
};

//需要保存的日志类型
enum WT_LOG_TYPE
{
    SYS_LOG,       //系统操作日志
    OPERATE_LOG,   //操作日志
    ERR_LOG,       //错误日志
    EXCEPTION_LOG, //异常日志
};

// 数据库的表类型
enum LOG_TABLE_TYPE
{
    DB_ERR_TABLE,
    DB_DIAG_TABLE,
    DB_SYS_LOG_TABLE,
    DB_OPT_LOG_TABLE,
    DB_EXCEPT_LOG_TABLE,
    DB_MAX_TABLE,
};

// 日志类别开关的类型与数量, 如果有新的需求就在LOG_TYPE_END前添加一个类别
enum LOG_FUNC_TYPE
{
    LOG_DEFULAT, // 缺省类型
    LOG_ERROR,   // LOGERR
    LOG_DEBUG,   // WT_DEBUG
    LOG_SYSOPT, 
    LOG_OPERATE, // LOGOPERATE
    LOG_EXCEPTION,
    LOG_WT_LINK, // WT_LINK 日志

    // 5
    LOG_CMD_TRACE, // CMD / ACK 的显示;
    LOG_CAL_MOD,   // 校准模块
    LOG_AUTO_BAKING,  // 自动烤机
    LOG_SUB_MGR,   // 子任务管理器
    LOG_SNC_CAL,
    LOG_TYPE_END,  // 日志类型结束, 在此行前面加日志类别
};

#define DB_TBL_RECORD_REMAIN (100000) // 记录保留数
#define DB_TBL_RECORD_MARGIN (101000) // 记录门限值

extern char g_log_type_flag[];

#define MAX_LOG_DATA_SIZE (100)

#define MAX_LOG_RECORD_NOTIY_CNT (8)  // 日志记录队列记录条数中超过该数值就通知到异步日志线程

// 定义日志级别
enum LogLevel {
    TRACE_,
    DEBUG_,
    SYSOPT_,
    OPERATE_,
    DIAG_,
    INFO_,
    ERROR_,
    EXCEPTION_,
    FATAL_,
    ENDLEVEL_,
};

struct LogRecord {
    int Type;
    int Level;
    time_t  Time;
    int Threadid;
    std::string Filename;
    int FileLine;
    int ErrCode;
    std::string Content;
    char Data[MAX_LOG_DATA_SIZE];
    int Datalen;
};

#ifdef DEBUG
#include <time.h>
#include <iostream>

#define WT_DEBUG(ErrCode, Content)                                    \
    if (g_log_type_flag[LOG_DEBUG] != 0)                              \
    {                                                                 \
        std::cout << __FILE__ << ", " << std::dec << __LINE__ << ", " \
                  << "ErrCode: 0x" << std::hex << ErrCode             \
                  << std::dec << ", " << Content << std::endl;        \
    }
#else
#define WT_DEBUG(ErrCode, Content) (void)0
#endif

#define LOG_LEVEL(LEVEL, ErrCode, Content) ErrLog(__FILE__, __LINE__, LEVEL, ErrCode, Content)
#define LOGERR(ErrCode, Content) ErrLog(__FILE__, __LINE__, LOG_ERROR, ErrCode, Content)
#define LOGSYSOPT(Content) SysOptLog(__FILE__, __LINE__, LOG_SYSOPT, Content)
#define LOGOPERATE(Content) OperateLog(__FILE__, __LINE__, LOG_OPERATE, Content)
#define LOGEXCEPTION(ErrCode, Content) ExceptionLog(__FILE__, __LINE__, LOG_EXCEPTION, ErrCode, Content)

//检查返回值，不成功则跳出
#define CheckBreak(ErrCode) \
    if (ErrCode != WT_OK)   \
    break

//检查返回值，不成功则返回错误码
#define CheckRet(ErrCode) \
    if (ErrCode != WT_OK) \
    return ErrCode

class WTLogStream;

class WTLog
{
  public:
    //*****************************************************************************
    // 获取日志对象
    // 参数[IN] : 无
    // 返回值: 日志对象指针
    //*****************************************************************************
    static WTLog &Instance();

    //*****************************************************************************
    // 设置所使用的日志文件名，此接口需在日志类的所有接口之前被调用一次
    // 参数[IN] : 无
    // 返回值: 无
    //*****************************************************************************
    static void SetLogName(const std::string &Name);

    //*****************************************************************************
    // 设置LOG输出到控制台使能
    // 参数[IN] : LogEnableFlag:使能标志位
    // 返回值: 无
    //*****************************************************************************
    static void SetLogPrintEnalbe(int LogEnableFlag);

    //*****************************************************************************
    // 记录错误信息到日志文件
    // 参数[IN] : File : 代码文件名
    //            Line ： 代码行号
    //            Logtype : 日志类型
    //            ErrCode ：错误码
    //            Content : 详细信息
    // 返回值: 无
    //*****************************************************************************
    void ErrLog(const char *File, int Line, int Logtype, int ErrCode, const std::string &Content);

    //*****************************************************************************
    // 记录诊断信息到日志
    // 参数[IN] : Data : 要记录的数据
    //            Len  : 数据长度
    //            File : 代码文件名
    //            Line ： 代码行号
    //            Logtype : 日志类型
    // 返回值: 无
    //*****************************************************************************
    void DiagLog(const void *Data, int Len, const char *File, int Line, int Logtype);

    //*****************************************************************************
    // 记录系统（程序）日志信息到日志文件
    // 参数[IN] : File : 代码文件名
    //            Line ： 代码行号
    //            Logtype : 日志类型
    //            Content : 详细信息
    // 返回值: 无
    //*****************************************************************************
    void SysOptLog(const char *File, int Line, int Logtype, const std::string &Content);

    //*****************************************************************************
    // 记录操作日志信息到日志文件
    // 参数[IN] : File : 代码文件名
    //            Line ： 代码行号
    //            Logtype : 日志类型
    //            Content : 详细信息
    // 返回值: 无
    //*****************************************************************************
    void OperateLog(const char *File, int Line, int Logtype, const std::string &Content);

    //*****************************************************************************
    // 记录异常日志信息到日志文件
    // 参数[IN] : File : 代码文件名
    //            Line ： 代码行号
    //            Logtype : 日志类型
    //            ErrCode ：错误码
    //            Content : 详细信息
    // 返回值: 无
    //*****************************************************************************
    void ExceptionLog(const char *File, int Line, int Logtype, int ErrCode, const std::string &Content);
    //*****************************************************************************
    // 查询指定时间段内的日志信息
    // 参数[IN] : BeginTime ：UTC起始时间，单位为us
    //            EndTime : UTC结束时间，单位为us
    //            Db : 查询指定的数据库文件，如果不指定则是查询当前的数据库
    // 参数[OUT]：Content :
    // 返回值: 无
    //*****************************************************************************
    int Query(long BeginTime, long EndTime, std::vector<std::string> &Content, const std::string &File = "");

    //*****************************************************************************
    // 通过SQL语句来直接查询日志信息
    // 参数[IN] : File ：查询指定的数据库db文件，如果不指定则是查询当前的数据库
    //            SqlStr : 协议下发的完整的sql
    // 参数[OUT]：Content :日志查询结果
    // 返回值: 无
    //*****************************************************************************
    int Query(const std::string &File, const std::string SqlStr, std::vector<std::string> &Content);

    // 主要用于读取日志的配置情况.
    static void SetFiFoName(const std::string &Name);

    // 带类别开关的日志输出接口
    void WriteLog(LOG_FUNC_TYPE type, const char *format, ...);

    // delete copy and move constructors and assign operators
    WTLog(WTLog const &) = delete;            // Copy construct
    WTLog(WTLog &&) = delete;                 // Move construct
    WTLog &operator=(WTLog const &) = delete; // Copy assign
    WTLog &operator=(WTLog &&) = delete;      // Move assign

    WTLogStream GettmpLogStream(int LogType = LOG_DEBUG);

    static void SetLogPreName(const std::string &Name);

  private:
    WTLog();
    ~WTLog();

    // 判断表是否存在
    bool TableExist(const std::string &TableName);

    // 创建错误日志表\异常日志表
    void CreateTable(const std::string &TableName);

    // 创建诊断记录表
    void CreateDiagTable(const std::string &TableName);

    // 创建系统操作日志\操作日志表
    void CreateOperationsTable(const std::string &TableName);

    // 创建表触发器，插入时检查删除一个月前的记录
    void CreateTriggerForTable(const std::string &TableName, const std::string &TriggerName);

    // 删除某个触发器
    void DeleteTriggerForTable(const std::string &TableName, const std::string &TriggerName);

    // 查询一个数据表中的记录数量
    void QueryTblRecdCnt(const std::string TableName, int &Count);

    // 查询一个数据表中的记录数量
    void QueryTblRecdCnt2(const std::string TableName, int &Count);

    // 检查内存中的计数器,并触发异步事件
    inline void CheckRecordCountAndTrig(int &Count)
    {
        if (Count > DB_TBL_RECORD_MARGIN)
        {
            m_DbCheckEv.send();
        }
    }

    // 数据库表检查回调
    void DatabaseCheckCb(wtev::async &watcher, int revents);

    // 释放数据库占有的磁盘空间
    void FreeDiskSpace(void);

    // 清理数据表中的数据(根据utc排序, 删除前面的Count条记录)
    void CleanUpRecordByCount(const std::string TableName, int Count);

    // 通过日期清理数据
    void CleanUpRecordByDate(const std::string TableName);

    // 启动后第一次检查数据
    void CheckAndCleanUpTable(const std::string &TableName, const std::string &TriggerName, int &RecordCount, bool &IsClean);

    // 初始化FIFO文件
    void InitFiFoFile(void);

    // FIFO IO事件回调
    void FifoEventCb(wtev::io &watche, int revents);

  private:
    void m_LogThreadFun(void);
    int m_TransLeve2TblId(int LogLevel);
    void LognotifyTimerCb(wtev::timer &watcher, int revents);
    static char m_FileName[64];       // 日志文件名
    sqlite3 *m_Db = nullptr;          // 数据库句柄
    std::mutex m_Mutex;               // 互斥体
    int m_TblRecdCount[DB_MAX_TABLE]; // 数据表的记录计数器
    wtev::async m_DbCheckEv;          // 数据表检查异步事件
    static char m_FifoName[64];       // FIFO配置文件
    static char m_LogPreName[64];     //日志前綴名字
    wtev::io m_fifoEv;                // FIFO IO观察者
    bool m_Isrunning;  // 日志线程是否运行中
    std::queue<LogRecord> m_Logqueue;  // 待写入的日志记录队列
    std::mutex m_Condmutex;
    std::condition_variable m_logcond;  // 条件变量
    std::thread m_Logthread;  // 日志线程
    wtev::timer m_EvLogNotiyTimer; //监听定时器


};

class WTLogStream : public std::ostringstream
{
public:
    WTLogStream(WTLog *WtLog, int LogType) : m_WTLog(WtLog), m_LogType(LogType) {};
    WTLogStream(const WTLogStream &LogStream) : m_WTLog(LogStream.m_WTLog), m_LogType(LogStream.m_LogType) {};
    ~WTLogStream() {if (m_WTLog != nullptr) {m_WTLog->WriteLog((LOG_FUNC_TYPE)m_LogType, (std::move(str())).c_str());}}

private:
    WTLog *m_WTLog = nullptr;
    int m_LogType;
};

#endif
