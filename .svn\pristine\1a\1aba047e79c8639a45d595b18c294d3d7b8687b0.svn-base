#include "wtswitch_418va.h"
#include "wtswitchdefine.h"

#define SELECT_SWITCH_BIT(a, b) \
    do                          \
    {                           \
        switch (a)              \
        {                       \
        case 0:                 \
            _DR_SW_CLEARBIT(b); \
            break;              \
        case 1:                 \
            _DR_SW_SETBIT(b);   \
            break;              \
        default:                \
            break;              \
        }                       \
    } while (0)
// VSG接口
int Vsg_Select_Port(int Port, int PortState);
int Vsg_Select_Loop(int Port, int PortState);
int Vsg_Select_Pac(int Port, int PortState, int NextStep);
int Vsg_Select_TX_RX(int Port, int PortState);
int Vsg_Select_PAC_PI_PA(int Port, int PortState);

// VSA接口
int Vsa_Select_Port(int Port, int PortState);
int Vsa_Select_Loop(int Port, int PortState);
int Vsa_Select_Pac(int Port, int PortState);
int Vsa_Select_TX_RX(int Port, int PortState);
int Vsa_Select_PAC_PI_PA(int Port, int PortState);

//对外接口
// Port: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsa_Select_SW_418VA(int Port, int PortState)
{
    if (PortState >= WT_RF_STATE_SISO)
    {
        return 0;
    }
    return Vsa_Select_Port(Port, PortState);
}

// Port: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsg_Select_SW_418VA(int Port, int PortState)
{
    if (PortState >= WT_RF_STATE_SISO)
    {
        return 0;
    }
    return Vsg_Select_Port(Port, PortState);
}

int Vsg_Select_CTL3_418VA(int Port, int PortState)
{
    if (PortState >= WT_RF_STATE_SISO)
    {
        return 0;
    }
    return Vsg_Select_Pac(Port, PortState, false);
}
//寄存器位索引表
enum WT418SwitchBit
{
    // RX
    RX_S_CTL,
    RX_M1_CTL,
    RX_M2_CTL,
    RX_12_CTL,
    RX_34_CTL,
    RX_56_CTL,
    RX_78_CTL,

    P1_R_CTL10,
    P2_R_CTL10,
    P3_R_CTL10,
    P4_R_CTL10,
    P5_R_CTL10,
    P6_R_CTL10,
    P7_R_CTL10,
    P8_R_CTL10,

    P1_R_CTL8,
    P2_R_CTL8,
    P3_R_CTL8,
    P4_R_CTL8,
    P5_R_CTL8,
    P6_R_CTL8,
    P7_R_CTL8,
    P8_R_CTL8,

    P1_R_CTL6,
    P2_R_CTL6,
    P3_R_CTL6,
    P4_R_CTL6,
    P5_R_CTL6,
    P6_R_CTL6,
    P7_R_CTL6,
    P8_R_CTL6,

    P1_R_CTL4,
    P2_R_CTL4,
    P3_R_CTL4,
    P4_R_CTL4,
    P5_R_CTL4,
    P6_R_CTL4,
    P7_R_CTL4,
    P8_R_CTL4,
    //TX
    P1_T_CTL12,
    P2_T_CTL12,
    P3_T_CTL12,
    P4_T_CTL12,
    P5_T_CTL12,
    P6_T_CTL12,
    P7_T_CTL12,
    P8_T_CTL12,

    P1_T_CTL11,
    P2_T_CTL11,
    P3_T_CTL11,
    P4_T_CTL11,
    P5_T_CTL11,
    P6_T_CTL11,
    P7_T_CTL11,
    P8_T_CTL11,

    P1_T_CTL9,
    P2_T_CTL9,
    P3_T_CTL9,
    P4_T_CTL9,
    P5_T_CTL9,
    P6_T_CTL9,
    P7_T_CTL9,
    P8_T_CTL9,

    P1_T_CTL5,
    P2_T_CTL5,
    P3_T_CTL5,
    P4_T_CTL5,
    P5_T_CTL5,
    P6_T_CTL5,
    P7_T_CTL5,
    P8_T_CTL5,

    P1_T_CTL3,
    P2_T_CTL3,
    P3_T_CTL3,
    P4_T_CTL3,
    P5_T_CTL3,
    P6_T_CTL3,
    P7_T_CTL3,
    P8_T_CTL3,

    // BETWEEN
    P1_B_CTL7,
    P2_B_CTL7,
    P3_B_CTL7,
    P4_B_CTL7,
    P5_B_CTL7,
    P6_B_CTL7,
    P7_B_CTL7,
    P8_B_CTL7,

    P1_S1_CTL2,
    P2_S1_CTL2,
    P3_S1_CTL2,
    P4_S1_CTL2,
    P5_S1_CTL2,
    P6_S1_CTL2,
    P7_S1_CTL2,
    P8_S1_CTL2,

    P1_S_CTL1,
    P2_S_CTL1,
    P3_S_CTL1,
    P4_S_CTL1,
    P5_S_CTL1,
    P6_S_CTL1,
    P7_S_CTL1,
    P8_S_CTL1,
};

#if DEBUG_SHOW_SW_BIT_NAME
// 注意要与WT428SwitchBit名称一一对应
static char WT448SwitchBitNames[][20] = {
    // RX
    "RX_S_CTL",
    "RX_M1_CTL",
    "RX_M2_CTL",
    "RX_12_CTL",
    "RX_34_CTL",
    "RX_56_CTL",
    "RX_78_CTL",

    "P1_R_CTL10",
    "P2_R_CTL10",
    "P3_R_CTL10",
    "P4_R_CTL10",
    "P5_R_CTL10",
    "P6_R_CTL10",
    "P7_R_CTL10",
    "P8_R_CTL10",

    "P1_R_CTL8",
    "P2_R_CTL8",
    "P3_R_CTL8",
    "P4_R_CTL8",
    "P5_R_CTL8",
    "P6_R_CTL8",
    "P7_R_CTL8",
    "P8_R_CTL8",

    "P1_R_CTL6",
    "P2_R_CTL6",
    "P3_R_CTL6",
    "P4_R_CTL6",
    "P5_R_CTL6",
    "P6_R_CTL6",
    "P7_R_CTL6",
    "P8_R_CTL6",

    "P1_R_CTL4",
    "P2_R_CTL4",
    "P3_R_CTL4",
    "P4_R_CTL4",
    "P5_R_CTL4",
    "P6_R_CTL4",
    "P7_R_CTL4",
    "P8_R_CTL4",
    //TX
    "P1_T_CTL12",
    "P2_T_CTL12",
    "P3_T_CTL12",
    "P4_T_CTL12",
    "P5_T_CTL12",
    "P6_T_CTL12",
    "P7_T_CTL12",
    "P8_T_CTL12",

    "P1_T_CTL11",
    "P2_T_CTL11",
    "P3_T_CTL11",
    "P4_T_CTL11",
    "P5_T_CTL11",
    "P6_T_CTL11",
    "P7_T_CTL11",
    "P8_T_CTL11",

    "P1_T_CTL9",
    "P2_T_CTL9",
    "P3_T_CTL9",
    "P4_T_CTL9",
    "P5_T_CTL9",
    "P6_T_CTL9",
    "P7_T_CTL9",
    "P8_T_CTL9",

    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",
    "P5_T_CTL5",
    "P6_T_CTL5",
    "P7_T_CTL5",
    "P8_T_CTL5",

    "P1_T_CTL3",
    "P2_T_CTL3",
    "P3_T_CTL3",
    "P4_T_CTL3",
    "P5_T_CTL3",
    "P6_T_CTL3",
    "P7_T_CTL3",
    "P8_T_CTL3",

    // BETWEEN
    "P1_B_CTL7",
    "P2_B_CTL7",
    "P3_B_CTL7",
    "P4_B_CTL7",
    "P5_B_CTL7",
    "P6_B_CTL7",
    "P7_B_CTL7",
    "P8_B_CTL7",

    "P1_S1_CTL2",
    "P2_S1_CTL2",
    "P3_S1_CTL2",
    "P4_S1_CTL2",
    "P5_S1_CTL2",
    "P6_S1_CTL2",
    "P7_S1_CTL2",
    "P8_S1_CTL2",

    "P1_S_CTL1",
    "P2_S_CTL1",
    "P3_S_CTL1",
    "P4_S_CTL1",
    "P5_S_CTL1",
    "P6_S_CTL1",
    "P7_S_CTL1",
    "P8_S_CTL1",
};
#endif

int Vsa_Select_Port(int Port, int PortState)
{
    const int BitMask[7] = {
        RX_S_CTL, RX_M1_CTL, RX_M2_CTL, RX_12_CTL, RX_34_CTL, RX_56_CTL, RX_78_CTL};
    const int BitSelectInit[7] = {
        0, 1, 1, 0, 0, 0, 0};
    const int BitSelect[8][7] = {
        {0, 0, 2, 0, 2, 2, 2},
        {0, 0, 2, 1, 2, 2, 2},
        {0, 1, 2, 2, 0, 2, 2},
        {0, 1, 2, 2, 1, 2, 2},
        {1, 2, 0, 2, 2, 0, 2},
        {1, 2, 0, 2, 2, 1, 2},
        {1, 2, 1, 2, 2, 2, 0},
        {1, 2, 1, 2, 2, 2, 1},
    };
    int i = 0;
    for (i = 0; i < sizeof(BitMask) / sizeof(BitMask[0]); ++i)
    {
        if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
        {
            SELECT_SWITCH_BIT(BitSelectInit[i], BitMask[i]);
        }
        else if (PortState < WT_RF_STATE_MAX)
        {
            SELECT_SWITCH_BIT(BitSelect[Port][i], BitMask[i]);
        }
    }
    return Vsa_Select_Loop(Port, PortState);
}

int Vsa_Select_Loop(int Port, int PortState)
{
    const int BitMask[8] = {
        P1_R_CTL6, P2_R_CTL6, P3_R_CTL6, P4_R_CTL6, P5_R_CTL6, P6_R_CTL6, P7_R_CTL6, P8_R_CTL6};
    const int BitMask2[8] = {
        P1_B_CTL7, P2_B_CTL7, P3_B_CTL7, P4_B_CTL7, P5_B_CTL7, P6_B_CTL7, P7_B_CTL7, P8_B_CTL7};
    const int BitMask3[8] = {P1_R_CTL10, P2_R_CTL10, P3_R_CTL10, P4_R_CTL10, P5_R_CTL10, P6_R_CTL10, P7_R_CTL10, P8_R_CTL10};
    const int BitMask4[8] = {P1_R_CTL8, P2_R_CTL8, P3_R_CTL8, P4_R_CTL8, P5_R_CTL8, P6_R_CTL8, P7_R_CTL8, P8_R_CTL8};
    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask3[Port]);
        _DR_SW_SETBIT(BitMask4[Port]);
        break;
    default:
        _DR_SW_SETBIT(BitMask3[Port]);
        _DR_SW_CLEARBIT(BitMask4[Port]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask[Port]);
        break;
    default:
        _DR_SW_CLEARBIT(BitMask[Port]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_CLEARBIT(BitMask2[Port]);
        break;

    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask2[Port]);
        break;
    default:
        break;
    }
    return Vsa_Select_Pac(Port, PortState);
}

int Vsa_Select_Pac(int Port, int PortState)
{
    const int BitMaskRx[8] = {
        P1_R_CTL4, P2_R_CTL4, P3_R_CTL4, P4_R_CTL4, P5_R_CTL4, P6_R_CTL4, P7_R_CTL4, P8_R_CTL4};
    const int BitMaskTx[8] = {
        P1_T_CTL3, P2_T_CTL3, P3_T_CTL3, P4_T_CTL3, P5_T_CTL3, P6_T_CTL3, P7_T_CTL3, P8_T_CTL3}; 

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMaskRx[Port]);
        break;
    case WT_RF_STATE_PI:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_SETBIT(BitMaskRx[Port]);
        _DR_SW_SETBIT(BitMaskTx[Port]);
        break;
    case WT_RF_STATE_FULL_DUPLEX_PI:
        _DR_SW_SETBIT(BitMaskRx[Port]);
        _DR_SW_CLEARBIT(BitMaskTx[Port]);
    default:
        break;
    }

    return Vsa_Select_TX_RX(Port, PortState);
}

int Vsa_Select_TX_RX(int Port, int PortState)
{
    const int BitMask[8] = {
        P1_S1_CTL2, P2_S1_CTL2, P3_S1_CTL2, P4_S1_CTL2, P5_S1_CTL2, P6_S1_CTL2, P7_S1_CTL2, P8_S1_CTL2};

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
        _DR_SW_CLEARBIT(BitMask[Port]);
        break;
    default:
        _DR_SW_SETBIT(BitMask[Port]);
        break;
    }
    return Vsa_Select_PAC_PI_PA(Port, PortState);
}

int Vsa_Select_PAC_PI_PA(int Port, int PortState)
{
    const int BitMask[8] = {
        P1_S_CTL1, P2_S_CTL1, P3_S_CTL1, P4_S_CTL1, P5_S_CTL1, P6_S_CTL1, P7_S_CTL1, P8_S_CTL1};

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_PI:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
    case WT_RF_STATE_FULL_DUPLEX_PI:
        _DR_SW_SETBIT(BitMask[Port]);
        break;
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMask[Port]);
        break;
    default:
        break;
    }
    return 0;
}

int Vsg_Select_Port(int Port, int PortState)
{
    return Vsg_Select_Loop(Port, PortState);
}

int Vsg_Select_Loop(int Port, int PortState)
{
    const int BitMask[8] = {
        P1_T_CTL5, P2_T_CTL5, P3_T_CTL5, P4_T_CTL5, P5_T_CTL5, P6_T_CTL5, P7_T_CTL5, P8_T_CTL5};
    const int BitMask2[8] = {
        P1_B_CTL7, P2_B_CTL7, P3_B_CTL7, P4_B_CTL7, P5_B_CTL7, P6_B_CTL7, P7_B_CTL7, P8_B_CTL7};

    const int BitMask3[8] = {P1_T_CTL12, P2_T_CTL12, P3_T_CTL12, P4_T_CTL12, P5_T_CTL12, P6_T_CTL12, P7_T_CTL12, P8_T_CTL12};
    const int BitMask4[8] = {P1_T_CTL11, P2_T_CTL11, P3_T_CTL11, P4_T_CTL11, P5_T_CTL11, P6_T_CTL11, P7_T_CTL11, P8_T_CTL11};
    const int BitMask5[8] = {P1_T_CTL9, P2_T_CTL9, P3_T_CTL9, P4_T_CTL9, P5_T_CTL9, P6_T_CTL9, P7_T_CTL9, P8_T_CTL9};

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask3[Port]);
        _DR_SW_SETBIT(BitMask4[Port]);
        _DR_SW_CLEARBIT(BitMask5[Port]);
        break;
    default:
        _DR_SW_SETBIT(BitMask3[Port]);
        _DR_SW_CLEARBIT(BitMask4[Port]);
        _DR_SW_SETBIT(BitMask5[Port]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_CLEARBIT(BitMask[Port]);
        break;
    default:
        _DR_SW_SETBIT(BitMask[Port]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_CLEARBIT(BitMask2[Port]);
        break;

    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask2[Port]);
        break;
    default:
        break;
    }
    return Vsg_Select_Pac(Port, PortState, true);
}

int Vsg_Select_Pac(int Port, int PortState, int NextStep)
{
    const int BitMaskRx[8] = {
        P1_R_CTL4, P2_R_CTL4, P3_R_CTL4, P4_R_CTL4, P5_R_CTL4, P6_R_CTL4, P7_R_CTL4, P8_R_CTL4};
    const int BitMaskTx[8] = {
        P1_T_CTL3, P2_T_CTL3, P3_T_CTL3, P4_T_CTL3, P5_T_CTL3, P6_T_CTL3, P7_T_CTL3, P8_T_CTL3}; 

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_PI:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_CLEARBIT(BitMaskTx[Port]);
        _DR_SW_CLEARBIT(BitMaskRx[Port]);
        break;
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_SETBIT(BitMaskTx[Port]);
        break;
    case WT_RF_STATE_FULL_DUPLEX_PI:
        _DR_SW_CLEARBIT(BitMaskTx[Port]);
        _DR_SW_SETBIT(BitMaskRx[Port]);
        break;
    default:
        break;
    }

    if(NextStep)
    {
        return Vsg_Select_TX_RX(Port, PortState);
    }
    else
    {
        return 0;
    }
}

int Vsg_Select_TX_RX(int Port, int PortState)
{
    const int BitMask[8] = {
        P1_S1_CTL2, P2_S1_CTL2, P3_S1_CTL2, P4_S1_CTL2, P5_S1_CTL2, P6_S1_CTL2, P7_S1_CTL2, P8_S1_CTL2};

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
        _DR_SW_CLEARBIT(BitMask[Port]);
        break;
    default:
        _DR_SW_CLEARBIT(BitMask[Port]);
        break;
    }
    return Vsg_Select_PAC_PI_PA(Port, PortState);
}

int Vsg_Select_PAC_PI_PA(int Port, int PortState)
{
    return Vsa_Select_PAC_PI_PA(Port, PortState);
}