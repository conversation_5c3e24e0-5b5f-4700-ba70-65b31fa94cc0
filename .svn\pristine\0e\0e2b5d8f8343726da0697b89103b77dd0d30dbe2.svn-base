#ifndef ALG_3GPP_VSGDEF_4G_H_
#define ALG_3GPP_VSGDEF_4G_H_

#include "alg_3gpp_apidef.h"

#define ALG_4G_MAX_CELL_NUM         5
#define ALG_4G_MAX_UE_NUM           1
#define ALG_4G_MAX_SUBFRAME_NUM     10
#define ALG_4G_MAX_DL_FRM_CFG       8
#define ALG_4G_MAX_DL_FRAME_NUM     16

#define ALG_4G_PUSCH                0
#define ALG_4G_PUCCH                1
#define ALG_4G_PDSCH                2
#define ALG_4G_PDCCH                3
#define ALG_4G_PBCH                 4
#define ALG_4G_PCFICH               5
#define ALG_4G_PHICH                6

/* Precoding Type */
#define ALG_4G_SINGLE_AP            0
#define ALG_4G_SPATIAL_MUL          1
#define ALG_4G_TRANSMIT_DIVE        2

#define ALG_4G_TBS_AUTO             0
#define ALG_4G_TBS_MANUAL           1

/**************************************************************************************************/
/*                                WaveGenerate Configure Start                                    */
/**************************************************************************************************/

/*------------------------------ WaveGnerate General Configure ------------------------------*/
typedef struct {
    /* Filter Type: ALG_3GPP_FILTER_LTE */
    int Type;
    int Fs; /* Fixed: 30.72MHz */
    
    /* # FIR filter parameter */
    int MaxOrder;
    int AlignReserved1;  /* Reserved for memory align */
    double FpassFactor;
    double FstopFactor;
    double PassRipple;
    double StopAtten;

    /* # LTE filter */
    int Optimization; /* 0: Best ACP; 1: Best EVM; */
    int AlignReserved2; /* Reserved for memory align */
    /* LTE best ACP config. */
    double CutOffFrqFactor;
    /* LTE best EVM config. */
    double RollOffFactor;
    double CutOffFreqShift;
    
    /* LTE best ACP config. */
    double SmoothFactor;

    int Reserved[8];
} Alg_4G_Filter;

typedef struct {
    /* # Filter */
    Alg_4G_Filter Filter;

    int GenWaveMode; /* 0: Frame; 1: Subframe; */
    int GenWaveNo;

    int GenSequenceLen;

    /* # Power */
    int PowerRefType;

    /* # Clip */
    /* # Time Domain Windowing */
    /* # Marker */
    int Reserved[64];
} Alg_4G_GeneralInfo;

/*------------------------------ WaveGnerate UL Configure ------------------------------*/
typedef struct {
    /* Cell Index */
    int CellIdx;
    /* Cell State: STD_OFF/STD_ON */
    int State;

    int PhyCellID;
    /* Channel bandwidth: Hz */
    int ChannelBW;

    /* Duplexing: 0(ALG_3GPP_FDD), 1(ALG_3GPP_TDD) */
    int Duplexing;
    int ULDLConfig;
    int SpecialSubfrmCfg;
    int AlignReserved; /* Reserved for memory align */
    double Power;

    /* Cyclic Shift n(1)DMRS */
    int N1Dmrs;

    int Reserved[9];
} Alg_4G_ULCellType;

typedef struct {
    /* Carrier Aggregation: ON/OFF */
    int CarrAggrState;
    int AlignReserved; /* Reserved for memory align */
    Alg_4G_ULCellType Cell[ALG_4G_MAX_CELL_NUM];

    /* Cyclic prefix: 0(ALG_3GPP_NCP), 1(ALG_3GPP_ECP) */
    int CyclicPrefix;

    /* # Signal */
    int GroupHop;
    int SequenceHop;
    int DeltaSeqShift;
    /* n(1)DMRS confiurate in call set */

    int Reserved[128];
} Alg_4G_ULMultiCellType;

typedef struct {
    int DataType;
    int Initialization;
    int TxMode;
    int MaxNumAP;

    /* Scramble: 0(STD_OFF), 1(STD_ON)*/
    int Scramble;

    /* Channel Coding: 0(STD_OFF), 1(STD_ON)*/
    int ChanCodingState;
    int ChanCodingMode;
    int Enable256QAM;

    int Reserved[8];
} Alg_4G_UePuschType;

typedef struct {
    /* # Common */
    int UeID;
    /* Use mode: standard or PRACH mode */
    int UseMode;
    double UePower;

    /* # PUSCH */
    Alg_4G_UePuschType Pusch[ALG_4G_MAX_CELL_NUM];

    /* # PUCCH */
    /* # FRC */ 
    /* # DMRS */
    /* # SRS */
    /* # Sidelink */
    /* # Antenna Port Mapping */

    int Reserved[128];
} Alg_4G_ULUeType;

typedef struct {
    int State;
    int Formate;
    /* n_PUCCH */
    int NPucch;
    int AlignReserved; /* Reserved for memory align */
    double Power;
    int Reserved[16];
} Alg_4G_PucchType;

typedef struct {
    /* MCS configurate mode: 0(standard); 1(Manual) */
    int McsCfgMode;
    int Mcs[2];
    int Modulate[2];
    /* Payload size: bit */
    int PayloadSize[2];
    int RedunVerIdx[2];

    int Reserved[33];
} Alg_4G_PuschEncodeType;

typedef struct {
    int CellIdx;
    int State;

    int FreqHopState;

    int RBSetNum;
    int RBNum[2];
    int RBOffset[2];
    double Power;

    /* Precoding type: 0(ALG_4G_SINGLE_AP); 1(ALG_4G_SPATIAL_MUL) */
    int Precoding;
    int LayerNum;
    int AntennaNum;
    int CodebookIdx;

    /* DMRS */
    int CyclicShiftField;

    int Codeword;

    int Reserved[16];

    Alg_4G_PuschEncodeType Encode;
} Alg_4G_PuschType;

typedef struct {
    int SubfrmIdx;
    int State;
    int Reserved[8];
    
    Alg_4G_PucchType Pucch;

    Alg_4G_PuschType Pusch[ALG_4G_MAX_CELL_NUM];
} Alg_4G_ULChanType;

typedef struct {
    Alg_4G_ULMultiCellType MultiCell;

    Alg_4G_ULUeType Ue;

    /* This is subframe configurate number */
    int CchSfCfgNum;
    int SchSfCfgNum;
    Alg_4G_ULChanType Chan[ALG_4G_MAX_SUBFRAME_NUM];
} Alg_4G_ULWaveGenType;

/*------------------------------ WaveGnerate DL Configure ------------------------------*/
typedef struct {
    /* Cell Index */
    int CellIdx;
    int State;

    int PhyCellID;
    /* Channel bandwidth: Hz */
    int ChannelBW;

    /* Duplexing: 0(ALG_3GPP_FDD), 1(ALG_3GPP_TDD) */
    int Duplexing;
    int ULDLConfig;
    int SpecialSubfrmCfg;
    int AlignReserved; /* Reserved for memory align */
    double Power;
    int PdschStart; /* No use parameter */
    int PhichResource;
    int PhichDuration; /* 0:Normal; 1:Extended */

    int Reserved[9];
} Alg_4G_DLCellType;

typedef struct {
    int AlignReserved2; /* Reserved for memory align */
    /* Carrier Aggregation: ON/OFF */
    int CarrAggrState;
    Alg_4G_DLCellType Cell[ALG_4G_MAX_CELL_NUM];

    /* Cyclic prefix: 0(ALG_3GPP_NCP), 1(ALG_3GPP_ECP) */
    int CyclicPrefix;

    int SyncTxAntenna;
    double PsyncPower;
    double SsyncPower;
    double RefSignalPower;

    int PdschPB;
    int AlignReserved; /* Reserved for memory align */
    double  PbchRatioRho;
    double  PdcchRatioRho;
    /* # DL::Antenna Ports */
    int TxAntennaNum;/*CRS Antenna Port Num*/

    int Reserved[127];
} Alg_4G_DLMultiCellType;

typedef struct {
    int UeID;
    /* Scramble: 0(STD_OFF), 1(STD_ON)*/
    int Scramble;
    /* Channel Coding: 0(STD_OFF), 1(STD_ON)*/
    int ChanCodingState;
    int ApMapping;
    int UECategory;
    int CodebookIndex;
    int DataType;
    int Initialization;
    
    double PdschPA;/*PDSCH power factor*/
    int TxMode;
    int McsTable; /* 1-4 */
    int TbsIndexAlt;/* non(0); a26(1); a33(2); b33(3); a37(4) */

    int Reserved[127];
} Alg_4G_DLUeType;

typedef struct {
    int PDCCHSymNum;
    int DLFormat;
    int DLCCEIdx;
    int ULFormat;
    int ULCCEIdx;

    int Reserved[9];
} Alg_4G_DLSchedSubfrmType;

typedef struct {
    char SchedPdsch[ALG_4G_MAX_DL_FRAME_NUM][ALG_4G_MAX_SUBFRAME_NUM];
    char HarqProcNum[ALG_4G_MAX_DL_FRAME_NUM][ALG_4G_MAX_SUBFRAME_NUM];
    char NewDataInd[ALG_4G_MAX_DL_FRAME_NUM][ALG_4G_MAX_SUBFRAME_NUM];
    char MCS[ALG_4G_MAX_DL_FRAME_NUM][ALG_4G_MAX_SUBFRAME_NUM];

    char SchedPusch[ALG_4G_MAX_DL_FRAME_NUM][ALG_4G_MAX_SUBFRAME_NUM];

    int Reserved1[64];

    /* [0]: Noraml subframe; [1]: Special subframe */
    Alg_4G_DLSchedSubfrmType NrSubfrm;
    Alg_4G_DLSchedSubfrmType SpSubfrm;

    int Reserved2[64];
} Alg_4G_DLSchedType;

typedef struct {
    /* Transmit state: 0(STD_OFF), 1(STD_ON) */
    int State;
    /* Scrambling: 0(STD_OFF), 1(STD_ON) */
    int Scrambling;
    /* Precoding type: 0(ALG_4G_SINGLE_AP); 2(ALG_4G_TRANSMIT_DIVE) */
    int Precoding;

    int SFNOffset;
    char SpareBit[16];

    int Reserved[8];
} Alg_4G_PbchType;

typedef struct {
    int State;
    int Scrambling;
    int Precoding; /* Precoding Scheme */
    int AlignReserved; /* Reserved for memory align */
    double Power;
    int PDCCHSymNum;

    int Reserved[9];
} Alg_4G_PCFICHType;

typedef struct {
    int ACKInfo; /* 0: NACK; 1: ACK */
    int AlignReserved; /* Reserved for memory align */
    double Power;
    int Reserved[8];
} Alg_4G_PHICHType;

typedef struct {
    int FreqHop; /* Frequency hopping flag */
    int ResBlkAssign;
    int MCS;
    int NewDataInd;
    int TPCCommand;
    int CyclicShiftForDMRS;
    int ULIndex;
    int DAI;
    int CSIResquest;
    int ResAllocateType;
} Alg_4G_DCIFormat0Info;

typedef struct {
    int ResAllocateHeader; /* 0: type 0; 1: type 1 */
    int ResBlkAssign;
    int MCS;
    int HarqProcNum;
    int NewDataInd;
    int RvIdx;
    int TPCCommand;
    int DLAssignment;
} Alg_4G_DCIFormat1Info;

typedef struct {
    int Mode;
    int VRBAssignment; /* 0: Localized VRB; 1: Distributed VRB */
    int ResBlkConfig; /* 0: Standard; 1: Manual */
    int RBNumber;
    int RBOffset;
    int ResBlkAssign;
    int MCS;
    int HarqProcNum;
    int NewDataInd;
    int RvIdx;
    int TPCCommand;
    int DLAssignment;
} Alg_4G_DCIFormat1AInfo;

typedef struct {
    int State;
    int User; /* 0: UE; */
    int DCIFormat; /* 0: Format0; 1: Format1; 0x11: Format1A */
    int SearchSpace; /* 0: Common Search Space; 1: UE-Specific search space */

    union {
        Alg_4G_DCIFormat0Info Format0;
        Alg_4G_DCIFormat1Info Format1;
        Alg_4G_DCIFormat1AInfo Format1A;
        int UnionReservd[32];
    };

    int PDCCHFormat;
    int CCEIdx; /* -1: Auto; 0-N */

    int Reserved[8];
} Alg_4G_DCIInfo;

typedef struct {
    /* -2:Variable; -1; 0; 1; 2; 3 */
    int Format;
    int DummyCCEType;
    int DataType;
    int PDCCHNum;
    double Power;

    /* DCI[0]:DL DCI; DCI[1]:UL DCI */
    Alg_4G_DCIInfo DCI[2];

    int AutoSchedDLDCI;

    int Reserved[15];
} Alg_4G_PdcchType;

typedef struct {
    int State;
    int ResAllocateType; /* 0: Type 0; 2: Type 2 */
    int VRBAssignment; /* 0: Localized */
    char RBGBitmap[32];
    int RBNum;
    int AutoOffset;
    int RBOffset;
    int SymbOffset;
    int Precoding;
    int LayerNum;
    int CyclicDelayDiversity;
    int CodebookIdx;

    int Codeword;
    int MCSConfigMode; /* 0: Standard; 1: Manual */
    int Mcs[2];
    int Modulate[2];
    /* Payload size: bit */
    int PayloadSize[2];
    int RedunVerIdx[2];
    int IRConfigMode;
    int NIR[2];
    int SoftChanBit[2];

    int Reserved[16];
} Alg_4G_PdschType;

typedef struct {
    int FrameIdx;
    int SubfrmIdx;

    Alg_4G_PbchType PBCH;
    Alg_4G_PCFICHType PCFICH;
    Alg_4G_PHICHType PHICH;
    Alg_4G_PdcchType PDCCH;
    Alg_4G_PdschType PDSCH;

    int Reserved[32];
} Alg_4G_DLChanType;

typedef struct {
    Alg_4G_DLMultiCellType MultiCell;

    Alg_4G_DLUeType Ue;

    Alg_4G_DLSchedType Schedule;

    /* This is subframe configurate number for DL. */
    int SubfrmCfgNum;
    int OCNGFlag;
    int DummyModulate;
    int DummyDataType;
    /* 0:Manual; 1:DCI; 2:Auto Sequence */
    int PdschScheduling;
    int Reserved[15];
    Alg_4G_DLChanType Chan[ALG_4G_MAX_DL_FRM_CFG][ALG_4G_MAX_SUBFRAME_NUM];
} Alg_4G_DLWaveGenType;

/*------------------------------ WaveGnerate Configure ------------------------------*/
typedef struct {
    /* Link Direct: 0(ALG_3GPP_UL), 1(ALG_3GPP_DL)*/
    int LinkDirect;
    int Reserved[3];

    union {
        Alg_4G_ULWaveGenType UL;
        Alg_4G_DLWaveGenType DL;
        char UnionReserved[1024 * 1024];
    };

    /* Filter, Clipping, Marker, ... */
    Alg_4G_GeneralInfo General;
} Alg_4G_WaveGenType;

/**************************************************************************************************/
/*                                 WaveGenerate Configure End                                     */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                  VSA Input Configure Start                                     */
/**************************************************************************************************/
typedef struct {
    int CellIdx;
    int State;

    int RBNum;
    int RBOffset;

    /* # Configurate parameter */
    int Precoding;
    int LayerNum;
    int AntennaNum;
    int CodebookIdx;

    /* DMRS parameter */
    int GroupHop;
    int SequenceHop;
    int DeltaSeqShift;
    int N1Dmrs;
    int CyclicShiftField;

    int Codeword;
    int Modulate[2];

    /* Channel decode state: STD_ON/STD_OFF */
    int ChanCodingState;
    /* # Decode parameter */
    int Scramble;
    /* MCS configurate mode: 0(standard); 1(Manual) */
    int McsCfgMode;
    int Mcs[2];
    int PayloadSize[2];
    int RedunVerIdx[2];
    int Enable256QAM;

    /* RB Auto detect: 0(OFF), 1(ON) */
    int RBDetMode;
    int Reversed[63];
} Alg_3GPP_AlzPusch4g;

typedef struct {
    int SymbOffset;

    int ResAllocateType; /* 0: Type 0; 2: Type 2 */
    int VRBAssignment;
    char RBGBitmap[32];
    int RBNum;
    int RBOffset;

    int PbchState;

    int Precoding;
    int LayerNum;
    int AntennaNum;
    int CyclicDelayDiversity;
    int CodebookIdx;

    int Codeword;
    int Modulate[2];

    int ChanCodingState;
    int Scramble;
    int McsCfgMode;
    int Mcs[2];
    int PayloadSize[2];
    int RedunVerIdx[2];
    int SoftChanBit[2];
    int AlignReserved; /* Reserved for memory align */

    /* Power factor */
    double PA;
    int PB;

    int NIR[2];
    int IRConfigMode;
    int TxMode;
    int UECategory;
    int McsTable;
    int TbsIndexAlt; /* non(0); a26(1); a33(2); b33(3); a37(4) */

    int Reversed[62];
} Alg_3GPP_AlzPdsch4g;

typedef struct {
    int CellIdx;
    int State;

    int PhyCellID;

    /* Channel bandwidth: Hz */
    int ChannelBW;
    /* Duplexing: 0(ALG_3GPP_FDD), 1(ALG_3GPP_TDD) */
    int Duplexing;
    int ULDLConfig;
    int SpecialSubfrmConfig;

    int Reversed[17];
} Alg_3GPP_AlzCell4g;

typedef struct {
    char ModEnable;
    char EvmEnable;
    char MErrEnable;
    char PErrEnable;
    char EvmSubcarEnable;
    char IBEEnable;
    char ESFlatEnable;
    char IQConstelEnable;
    char TxMeasureEnable;

    int ModStatNum;

    int EvmWinNcp[6];
    int EvmWinEcp[6];

    int ExPeriodLeading;
    int ExPeriodLagging;

    int ExAbnSymbFlg; /* TRUE: exculde abnormal symbol in EVM; FALSE: do nothing */

    int Equalizer;

    int Reserved[16];
} Alg_4G_MeasureInModulation;

typedef struct {
    /* SEM parameter */
    char SpectEnable;
    char OBWEnable;
    char SEMEnable;
    char MeasFilter;
    int SEMStatNum;

    /* ACLR parameter */
    char ACLREnable;
    char UTRA1Enable;
    char UTRA2Enable;
    char EUTRA1Enable;
    char EUTRA2Enable;
    int ACLRStatNum;

    int Reserved[16];
} Alg_4G_MeasureInSpectrum;

typedef struct {
    char PMonitorEnable;

    char PowerEnable;

    int PowerStatNum;

    char PwrDynEnable;
    char TimeMaskType;
    int Leading;
    int Lagging;
    int HighDynmMode;

    int Reserved[16];
} Alg_4G_MeasureInPower;

typedef struct {
    /* # Measurement Subframe settings */
    int MeasSubfrmIdx;
    int MeasSubfrmCount;
    int MeasSubfrmOffset;
    int MeasSlotType; /* 0(slot0); 1(slot1); 2(all) */

    /* # EVM vs modulation symbol  */
    int EvmSymbEnable; /* 0: OFF, 1: ON */
    int EvmSymbIdx;
    int EvmSymbWinType; /* 0: Low; 1: High */

    int DmrsConsState; /* 0: OFF, 1: ON */

    int SyncMode;
    int MeasureUnit; /* 0: slot; 1: subframe */

    int DecodingEnable;

    int Reserved[32];

    Alg_4G_MeasureInModulation Modulate;
    
    Alg_4G_MeasureInSpectrum Spectrum;

    Alg_4G_MeasureInPower Power;
} Alg_3GPP_MeasureIn4g;

typedef struct {
    Alg_3GPP_BaseLimitType EvmRms;
    Alg_3GPP_BaseLimitType EvmPeak;
    Alg_3GPP_BaseLimitType MErrRms;
    Alg_3GPP_BaseLimitType MErrPeak;
    Alg_3GPP_BaseLimitType PhErrRms;
    Alg_3GPP_BaseLimitType PhErrPeak;
    Alg_3GPP_BaseLimitType FreqErr;
    Alg_3GPP_IQOffsetLimitType IQOffset;
    Alg_3GPP_IBELimitType IBE;
    Alg_3GPP_ESFlatLimitType SpectFlat;
} Alg_4G_ModulateLimitType;

typedef struct {
    Alg_3GPP_BaseLimitType OBWLimit;
    Alg_3GPP_SEMLimitType SEMLimit[6][ALG_3GPP_SEM_LIM_SET];
    Alg_3GPP_AclrLimitType UtraLimit[2];
    Alg_3GPP_AclrLimitType EUtraLimit[2];
} Alg_4G_SpectrumLimitType;

typedef struct {
    int State;
    double OnLimitUpper;
    double OnLimitLower;
    double OffLimit;
} Alg_4G_PowerLimitType;

typedef struct {
    Alg_4G_ModulateLimitType ModLimit[4];

    Alg_4G_SpectrumLimitType SpectLimit[6];
    double SEMAddTestTol[2];

    Alg_4G_PowerLimitType PwrLimit[6];
} Alg_3GPP_LimitIn4g;

typedef struct {
    /* Carrier Aggregation: ON/OFF */
    int CarrAggrState;
    int LinkDirect;
    Alg_3GPP_AlzCell4g Cell[ALG_4G_MAX_CELL_NUM];

    int CyclicPrefix;
    int UeID;
    int NSValue;
    int Reserved[4];

    int ChanType;
    union {
        Alg_3GPP_AlzPusch4g Pusch[ALG_4G_MAX_CELL_NUM];
        Alg_3GPP_AlzPdsch4g Pdsch;
    };

    Alg_3GPP_MeasureIn4g MeasInfo;

    Alg_3GPP_LimitIn4g LimitInfo;
} Alg_3GPP_AlzIn4g;

/**************************************************************************************************/
/*                                   VSA Input Configure End                                      */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                      VSA Output Start                                          */
/**************************************************************************************************/
typedef struct {
    int Modulate;
    int Scrambling;
    int ChannelCodingType; /* 0:Turbo; 1:TBCC */

    /* Transport block CRC check: 0(FAIL); 1(PASS) */
    int Crc;

    int BitLen;
    char *BitSeq;
}Alg_3GPP_OutInfoCw4g;

typedef struct {
    Alg_3GPP_BaseLimitType EvmRms;
    Alg_3GPP_BaseLimitType EvmPeak;
    Alg_3GPP_BaseLimitType MErrRms;
    Alg_3GPP_BaseLimitType MErrPeak;
    Alg_3GPP_BaseLimitType PhErrRms;
    Alg_3GPP_BaseLimitType PhErrPeak;
    Alg_3GPP_BaseLimitType FreqErr;
    Alg_3GPP_BaseLimitType IQOffset;
    Alg_3GPP_ESFlatLimitType SpectFlat;
    Alg_3GPP_BaseLimitType OBWLimit;
    char SEMLimitState[ALG_3GPP_SEM_LIM_SET];
    Alg_3GPP_AclrLimitType UTRALimit[2];
    Alg_3GPP_AclrLimitType EUTRALimit;
    Alg_4G_PowerLimitType PwrLimit;
} Alg_4G_MeasLimitOutType;

typedef struct {
    Alg_3GPP_OfdmSummyOutType ModOut;

    float IBEmisMargin;
    int IBEmisRBIdx;
    int IBEmisTest;

    Alg_3GPP_ESFlatSigOutType ESFlat;
    int SCIdxMax1;
    int SCIdxMin1;
    int SCIdxMax2;
    int SCIdxMin2;
} Alg_4G_ModOutSlot;

typedef struct {
    float TraceTime[2048]; /* X-Axis */
    float PwrTrace[2048]; /* Y-Axis */
    float OutPower[4];
} Alg_4G_PwrDynOutType;

typedef struct {
    /* # 1 Last slot measure result */
    int TxStreamNum;
    Alg_3GPP_OfdmOutInfo TxMeasure[ALG_3GPP_MAX_STREAM];

    int LinkDirect;
    int ChanType;

    int Codeword;
    Alg_3GPP_OutInfoCw4g CwInfo[2];

    /* # 2 Measure limit value */
    Alg_4G_MeasLimitOutType LimOut;

    /* # 3 Multi-slot statistic result */
    int ModStatNum;
    Alg_4G_ModOutSlot *SlotMod;
    int ModRBNum;
    int ModRBOffset;

    int SEMStatNum;
    Alg_3GPP_SEMaskSigOutType *SlotSEM;
    int SEMaskRBNum;
    int SEMaskRBOffset;
    
    int ACLRStatNum;
    Alg_3GPP_ACLRSigOutType *SlotACLR;
    int AclrRBNum;
    int AclrRBOffset;

    int PwrMonitorNum;
    float *PwrMoRmsSF;
    float *PwrMoPeakSF;
    
    int PwrStatNum;
    float *TxPwrSF;

    int PwrDynmNum;
    Alg_4G_PwrDynOutType *PwrDynm;
} Alg_3GPP_AlzOut4g;

/**************************************************************************************************/
/*                                        VSA Output End                                          */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                   List Input Configure Start                                   */
/**************************************************************************************************/
typedef struct {
    /* Measure statistical length */
    int ModStatNum;
    int SEMStatNum;
    int ACLRStatNum;
    int PowerStatNum;

    char ModEnable;
    char EvmEnable;
    char MErrEnable;
    char PErrEnable;
    char IBEEnable;
    char ESFlatEnable;

    char SpectEnable;
    char OBWEnable;
    char SEMEnable;

    char ACLREnable;
    char UTRA1Enable;
    char UTRA2Enable;
    char EUTRAEnable;

    char PMonitorEnable;

    char PowerEnable;
} Alg_4G_ListMeasureInType;

typedef struct {
    int ChannelBW;

    int Duplexing;
    int ULDLConfig;
    int SpecialSubfrmConfig;

    int CyclicPrefix;

    int ChanType;

    /* RB Auto detect: 0(OFF), 1(ON) */
    int RBAutoMode;
    int RBNum;
    int RBOffset;

    int Modulate;

    Alg_4G_ListMeasureInType Measure;
} Alg_4G_ListInType;

/**************************************************************************************************/
/*                                     List Input Configure End                                   */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                       List Output Start                                        */
/**************************************************************************************************/

typedef struct {
    int StatistNum;
    float OutOfTol;

    Alg_3GPP_OfdmSummyOutType ModResCur;
    Alg_3GPP_OfdmSummyOutType ModResAvg;
    Alg_3GPP_OfdmSummyOutType ModResSdv;

    Alg_3GPP_OfdmSummyTestType ModTstCur;
    Alg_3GPP_OfdmSummyTestType ModTstAvg;

    Alg_3GPP_OfdmSummyOutType ModResExt;
    Alg_3GPP_OfdmSummyTestType ModTstExt;
    float TxPowerMin;
    float TxPowerMax;
    float PeakPowerMin;
    float PeakPowerMax;
    float RBPowerMin;
    float RBPowerMax;
    char TxPowerMinTst;
    char TxPowerMaxTst;
    char PeakPowerMinTst;
    char PeakPowerMaxTst;
    char RBPowerMinTst;
    char RBPowerMaxTst;

    int RBNum;
    int RBOffset;
} Alg_4G_ListModOutType;

typedef struct {
    int StatistNum;
    float OutOfTol;

    float MarginCur;
    float MarginAvg;
    float MarginExt;
    float MarginSdv;

    int RBIdxCur;
    int RBIdxExt;
} Alg_4G_ListIEmisType;

typedef struct {
    int StatistNum;
    float OutOfTol;

    Alg_3GPP_ESFlatSigOutType FlatResCur;
    Alg_3GPP_ESFlatSigOutType FlatResAvg;
    Alg_3GPP_ESFlatSigOutType FlatResExt;
    Alg_3GPP_ESFlatSigOutType FlatResSdv;

    Alg_3GPP_ESFlatSigTstType FlatTstCur;
    Alg_3GPP_ESFlatSigTstType FlatTstAvg;
    Alg_3GPP_ESFlatSigTstType FlatTstExt;

    int SCIdxMax1;
    int SCIdxMin1;
    int SCIdxMax2;
    int SCIdxMin2;
} Alg_4G_ListESFlatType;

typedef struct {
    int StatistNum;
    float OutOfTol; /* SEM Test */

    Alg_3GPP_SEMaskSigOutType SEMResCur;
    Alg_3GPP_SEMaskSigOutType SEMResAvg;
    Alg_3GPP_SEMaskSigOutType SEMResExt;
    
    float OBWSdv;
    float TxPwrSdv;
    float TxPwrMin;
    float TxPwrMax;

    char OBWTstCur;
    char OBWTstAvg;
    char OBWTstExt;
    char TxPwrTstCur;
    char TxPwrTstAvg;
    char TxPwrTstMin;
    char TxPwrTstMax;

    int RBNum;
    int RBOffset;
} Alg_4G_ListSEMOutType;

typedef struct {
    int StatistNum;
    float OutOfTol;

    int RBNum;
    int RBOffset;

    Alg_3GPP_ACLRSigOutType ACLROutCur;
    Alg_3GPP_ACLRSigOutType ACLROutAvg;

    Alg_3GPP_ACLRSigTstType ACLRTstCur;
    Alg_3GPP_ACLRSigTstType ACLRTstAvg;
} Alg_4G_ListACLROutType;

typedef struct {
    int PMonitorNum;
    float PwrRMS[ALG_3GPP_LIST_PMONITOR_MAX];
    float PwrPeak[ALG_3GPP_LIST_PMONITOR_MAX];

    int StatistNum;
    float OutOfTol;
    float TxPwrCur;
    float TxPwrAvg;
    float TxPwrMin;
    float TxPwrMax;
    float TxPwrSdv;
} Alg_4G_ListPwrOutType;

typedef struct {
    /* # Modulation Measurement */
    Alg_4G_ListModOutType ModOut;

    /* # Inband Emission Measurement Result */
    Alg_4G_ListIEmisType IEmis;

    /* # Spectrum Flatness Measurement Result */
    Alg_4G_ListESFlatType ESFlat;

    /* # Spectrum Emission Mask Measurement Result */
    Alg_4G_ListSEMOutType SEM;
    
    /* # ACLR Measurement Result */
    Alg_4G_ListACLROutType ACLR;

    Alg_4G_ListPwrOutType Power;

    int Modulate;
    int ChanType;
} Alg_4G_ListOutType;

/**************************************************************************************************/
/*                                        List Output End                                         */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                   VSA Statistic Output Start                                   */
/**************************************************************************************************/

typedef struct {
    Alg_3GPP_BaseStatOutType EvmRmsL;
    Alg_3GPP_BaseStatOutType EvmRmsH;
    Alg_3GPP_BaseStatOutType EvmPeakL;
    Alg_3GPP_BaseStatOutType EvmPeakH;
    Alg_3GPP_BaseStatOutType EvmDmrsL;
    Alg_3GPP_BaseStatOutType EvmDmrsH;

    Alg_3GPP_BaseStatOutType MErrRmsL;
    Alg_3GPP_BaseStatOutType MErrRmsH;
    Alg_3GPP_BaseStatOutType MErrPeakL;
    Alg_3GPP_BaseStatOutType MErrPeakH;
    Alg_3GPP_BaseStatOutType MErrDmrsL;
    Alg_3GPP_BaseStatOutType MErrDmrsH;

    Alg_3GPP_BaseStatOutType PErrRmsL;
    Alg_3GPP_BaseStatOutType PErrRmsH;
    Alg_3GPP_BaseStatOutType PErrPeakL;
    Alg_3GPP_BaseStatOutType PErrPeakH;
    Alg_3GPP_BaseStatOutType PErrDmrsL;
    Alg_3GPP_BaseStatOutType PErrDmrsH;

    Alg_3GPP_BaseStatOutType IQOffset;
    Alg_3GPP_BaseStatOutType FreqErr;
    Alg_3GPP_BaseStatOutType TimingErr;
    Alg_3GPP_PwrStatOutType TxPower;
    Alg_3GPP_PwrStatOutType PeakPower;
    Alg_3GPP_PwrStatOutType RBPower;
} Alg_4G_StatOutModuInfo;

typedef struct {
    Alg_3GPP_PwrStatOutType TxPower;
    Alg_3GPP_BaseStatOutType OBW;
    Alg_3GPP_BaseStatOutType MarginNegX[12];
    Alg_3GPP_BaseStatOutType MarginNegY[12];
    Alg_3GPP_BaseStatOutType MarginPosX[12];
    Alg_3GPP_BaseStatOutType MarginPosY[12];
} Alg_4G_StatOutSEMInfo;

typedef struct {
    Alg_3GPP_ACLRSigOutType Cur;
    Alg_3GPP_ACLRSigOutType Avg;
} Alg_4G_StatOutACLRInfo;

/**************************************************************************************************/
/*                                    VSA Statistic Output End                                    */
/**************************************************************************************************/

#endif /* ALG_3GPP_VSGDEF_4G_H_ */