#ifndef _SCPI_PAC_H_
#define _SCPI_PAC_H_
#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif

    scpi_result_t PAC_SetVsaPort(scpi_t *context);
    scpi_result_t PAC_SetVsaRefPowerLevel(scpi_t *context);
    scpi_result_t PAC_SetVsaSmpTime(scpi_t *context);
    scpi_result_t PAC_SetVsgPort(scpi_t *context);
    scpi_result_t PAC_SetVsgPower(scpi_t *context);
    scpi_result_t PAC_SetMode(scpi_t *context);
    scpi_result_t PAC_SetAvgCnt(scpi_t *context);
    scpi_result_t PAC_SetFreqList(scpi_t *context);
    scpi_result_t PAC_SetStarGetData(scpi_t *context);
    scpi_result_t PAC_GetCalPathLoss(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif