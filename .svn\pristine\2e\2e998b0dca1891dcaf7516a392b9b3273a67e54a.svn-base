//*****************************************************************************
//  File: vsg.h
//  vsg业务处理
//  Data: 2016.8.11
//*****************************************************************************
#ifndef __WT_VSG_H__
#define __WT_VSG_H__

#include "wtbsn.h"
#include <vector>
#include "wt-calibration.h"
#include "analysis/compensate.h"
#include "analysis/analysis.h"
#include "broadcastvsg.h"
#include "wtlog.h"

#define PAC_VSG_WAVE_PATH "/configuration/Sin1MHz_240M.low"

class WTVsa;
class RxSequence;
struct SeqTimeParam;
struct TriggerCommonParam;

#define TB_START_VSA_POS  0xFFFFFFFF     //TB模式时,VSA的启动位置,默认给个大值以便于比较。
#define OLDSTRUCTSIZE 1984
#define NEWSTRUCTSIZE 1832

// VSG配置参数
struct VsgParam
{
    double Freq;         //信号中心频率, 单位HZ
    double Freq2;        //信号中心频率2，Type为80+80 AC时有效
    double FreqOffset;   //频偏
    int RFPort;          // RF端口号，取值见WT_PORT_ENUM
    int Type;            //测试类型，取值见WT_TEST_TYPE
    int MasterMode;      // 80+80时mater选择，0表示第一个模块为Master, 1表示第二个模块为master
    int VsgMask;         //使用指定的VSG单元，用掩码表示，0表示自动分配
    double Power;        //发送功率 dBm
    double ExtGain;      //外部增益
    int SignalId;        //信号流ID
    int AllocTimeout;    //申请资源超时时间，单位秒
    double SamplingFreq; //采样率
    double ExtGain2;     //外部增益, 80+80时对于freq2
    int Is160M;          //是否为160M信号
    int RFPort2;         // RF端口号，取值见WT_PORT_ENUM 80+80 双端口模式专用
    int DulPortMode;     // 80+80是否使用两份射频参数，包含双端口模式和双射频参数单端口模式。
    int LoopCnt;         //循环次数
    double Power2;       //发送功率 dBm   80+80 双端口模式port2专用
    double TBTStaDelay;  //TBTSta模式的延时，单位s;
    double DCOffsetI;    // I路 DC offset: -1.0 to +1.0      模拟IQ模式专用
    double DCOffsetQ;    // Q路 DC offset: -1.0 to +1.0      模拟IQ模式专用
    double CommModeVolt; //共模电压 0.0V-3.3V                 模拟IQ模式专用
    double IFG;          //循环间隔，单位采样点数
    double RandomWaveGapMax;  //随机间隔的范围上限，单位：second(秒)
    double RandomWaveGapMin;  //随机间隔的范围下限，单位：second(秒)
    int IFGRamdomMode;            //IFG随机模式
                         //编译器对一些变量的起始地址做了“对齐”处理。在默认情况下，规定各成员变量存放的起始地址相对于结构的起始地址的偏移量必须为该变量的类型所占用的字节数的倍数。

    //获取测试类型
    int GetTestType(void) const { return Type; }

    //获取使用的硬件模块掩码
    int GetModMask(void) const { return VsgMask; }

    //当前测试是否为80+80
    bool IsAC8080(void) const { return Freq2 > 1; }

    bool Is160(void) const { return Is160M == 1; }

    int GetMasterMode(void) const { return MasterMode; }

    bool IsAgumentLegal(void) const;

    //是否使用两份射频参数，包含双端口模式和双射频参数单端口模式。
    bool IsUseDualParam(void) const { return DulPortMode == 1 && Freq2 > 1; }

    //使用两份射频参数,且RFPort != RFPort2时认为是双端口模式。
    bool IsDualPortMode(void) const { return DulPortMode == 1 && Freq2 > 1 && (RFPort != RFPort2); }

    //使用两份射频参数,且RFPort == RFPort2时认为是双射频参数单端口模式。
    bool IsDualParamMode(void) const { return DulPortMode == 1 && Freq2 > 1 && RFPort == RFPort2; }

    void ParamTest();

    void testT()
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "=========VSG Power:%lf, CmVolt:%lf, DCOffsetI:%lf, DCOffsetQ:%lf\n", Power, CommModeVolt, DCOffsetI, DCOffsetQ);
    }
    bool operator==(const VsgParam &a){
    if( Basefun::CompareDouble(this->Freq, a.Freq)||
        Basefun::CompareDouble(this->Freq2, a.Freq2)||
        Basefun::CompareDouble(this->FreqOffset, a.FreqOffset)||
        this->RFPort != a.RFPort||
        this->Type != a.Type||
        this->MasterMode != a.MasterMode||
        this->VsgMask != a.VsgMask||
        Basefun::CompareDouble(this->Power, a.Power)||
        Basefun::CompareDouble(this->ExtGain, a.ExtGain)||
        this->SignalId != a.SignalId||
        this->AllocTimeout != a.AllocTimeout||
        Basefun::CompareDouble(this->SamplingFreq, a.SamplingFreq)||
        Basefun::CompareDouble(this->ExtGain2, a.ExtGain2)||
        this->Is160M != a.Is160M||
        this->RFPort2 != a.RFPort2||
        this->DulPortMode != a.DulPortMode||
        Basefun::CompareDouble(this->Power2, a.Power2)||
        Basefun::CompareDouble(this->TBTStaDelay, a.TBTStaDelay)||
        Basefun::CompareDouble(this->DCOffsetI, a.DCOffsetI)||
        Basefun::CompareDouble(this->DCOffsetQ, a.DCOffsetQ)||
        Basefun::CompareDouble(this->CommModeVolt, a.CommModeVolt)||
        Basefun::CompareDouble(this->IFG, a.IFG)||
        Basefun::CompareDouble(this->RandomWaveGapMax, a.RandomWaveGapMax)||
        Basefun::CompareDouble(this->RandomWaveGapMin, a.RandomWaveGapMin)||
        this->IFGRamdomMode != a.IFGRamdomMode||
        this->LoopCnt != a.LoopCnt)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};

struct FemParam
{
    int FemMode;        //FemMode使能，0=NormalMode，1=FemMode
    int DutyRatio;      //占空比，仅连续测试时有效，0-100
    double LeadTime;    //前置时间，发送RF前，TRIG电平提前拉高的时间，单位S
    double DelayTime;   //延时时间，发送RF后，TRIG电平继续保持高的时间。单位S
    double Reserved[3]; //保留 使用保留字段时，需要注意字节对齐的问题
    FemParam() : FemMode(0), DutyRatio(50), LeadTime(0), DelayTime(0) {}
};

struct VsgDataInfo
{
    std::unique_ptr<SigFile>  WaveFile;  //波形文件
    std::unique_ptr<char[]> Buf;   //已补偿数据buffer
    std::unique_ptr<char[]> Buf2;   //8080模式时，第二模块的已补偿的数据buffer
    SigFileInfo *FileInfo;     //波形文件头
    char *PnData;              //最终处理完的PN数据的存放地址
    int PointNum;              //数据点数
    int PnDataLen;             //PN数据长度
    int WaveSource;            //数据来源
};

//生成文件后，才确定的参数变量值获取返回，目前主要是tb相关的内容
typedef struct
{
    int LDPCSym = UNVALID_INT_VAL;
    int PEDisamb = UNVALID_INT_VAL;
    int AFactor = UNVALID_INT_VAL;
    int Doppler = UNVALID_INT_VAL;
    int Midamble_Periodicity = UNVALID_INT_VAL;
    int Reserved[256] = {0};
}AxTbVariableParameter;

enum WAVE_PPDU_ENUM
{
    //11ax wave PPDU type enum
    HE_SU_PPDU = 1,
    HE_MU_PPDU = 2,
    HE_TB_PPDU = 3,
    HE_EXTEND_PPDU = 4,
};

//兼容旧版本api,保留bt旧的生成结构体，然后配置时强制转新结构
typedef struct
{
    //standard和subType确定要生成那种信号类型
    int standard;            //信号标准. 参考：WT_STANDARD_ENUM
    int subType;             //subType: 类似ax的ppdu type
    int bandwidth;           //带宽, 单位：MHz (20:20M; 40:40M; 80:80M; 160:160M; 160:8080M)
    int samplingRate;        //采样速率, 单位：Hz
    int NSS;                 //stream数量
    int segment;             //segment数量，默认值等于1，80+80M信号时配置值等于2
    double FreqErr;          //频偏
    double IQImbalanceAmp;   //IQ不平衡幅度调整
    double IQImbalancePhase; //IQ不平衡相位调整
    double DCOffset_I;       //DC offset I调整
    double DCOffset_Q;       //DC offset Q调整
    double ClockRate;        //Clock rate
    double Snr;
    double Gap;              //Unit: Sec,default 10us, 0.00001s
    int SpatialExtension;
    int Duplicate;
    int ChannelMode;
    int DupPunc[16];
    int Reserved[233];       //保留
} OldPNSettingBase;

typedef struct
{
    //Header format
    int LT_ADDR;                    //  3bit  LT_ADDR：3bit，取值0~7
    int PackType;                   //  4bit  TYPE: 分组包类型：目前主要支持DH1，DH3，DH5,2DH1,2DH3,2DH5,3DH1,3DH3,3DH5等
    int Flow;                       //  1bit  FLOW: FLOW_Control：1bit，取值0~1
    int ARQN;                       //  1bit  ARQN: ACK Indication：1bit，取值0~1
    int SEQN;                       //  1bit  SEQN: Seq Number Inder：1bit，取值0~1

    //Payload
    int LLID;                       // Logical Link Identifier(LLID):取值0~3 :默认：1
    int DataWhitening;              // 数据是否需要加噪（加噪值为1，不加噪值为0，默认不加噪：0）
    int payLoadType;                //在BLE测试的时候指定负载的类型（PackType选在LE_TEST时候有效）
    int mFlow;                      //Flow取值：0,1

    //PHY Parameters
    double FreqDeviation;           // Freq Deviation：115kHz-300kHz，默认160kHz
    double ModuIdex;                //Modulation Index：取值：0.01~0.99(只支持两位小数)   由原来的FreqDeviation字段修改
    int BTProductIdex;              //Filter BT Product 取值：0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9
    int RolloffIdex;                //EDR Filter Rolloff  取值：0.3，0.35,0.40,0.45,0.50
    int GuardTime;                  //EDR Guard Time us;IQXEL（0~10000us，只能取整数）

    //Device Address Part
    unsigned int LAP;               // LAP(Lower Address Part)：24bit 默认：0x000000
    unsigned char  UAP;             // UAP(Upper Address Part)：8bit 默认：0x47
    unsigned int NAP;               // NAP(Non-significant Address Part)：16bit 默认：0x0000

    //Packet Ramp Time
    double PowerRampTime;           //us 默认：2us
    double PowerSettlingTime;       //us 默认：4us

    //LE packetType
    int LE_PayloadType;             //Payload type 0~7
    unsigned int LE_SyncWord;       //32bit 默认取值94826e8e(最左边为低位 如A=1010)

    int BTHeadZeroCount;            //BT信号头部添零
    int BTAddZeroCount;             //BT信号尾部添零
    int BLEMapperS;                 //只对enPacketType_LE_Coded_Test有效，取值2或者8

    int PayLoadSize;                // payload(PSDU)

    //PayloadBody for FHS
    char FHS_ParityBits[5];                 //34bit
    unsigned char  FHS_EIR;             //1bit
    unsigned char  FHS_SR;              //2bit
    unsigned char  FHS_SP;              //2bit ，set to 0b10
    int FHS_ClassofDevice;              //24bit
    int FHS_LT_ADDR;                    //3bit
    int FHS_CLK27_2;                    //26bit
    int FHS_PageScanMode;               //3bit，Mandatory scan mode 000

    // for DV voice Field 80bit
    int DV_VoiceField;

    //core5.1 BLE_1M和BLE_2M才可能有的Constant Tone Extension相关项
    int LE_CTEInfo;                     //1bit 1，表示存在CTEInfo field和CTE；  0，表示不存在，默认为0
    int LE_CTEtime;                     //5bit 范围2-20，对应16-160us；
    int LE_CTEInfo_RFU;                 //Reserve for Future Use
    int LE_CTEType;                     //2bit 0-AoA , 1-AOD 1us slot ,  2-AOD 2us slot , 3-reserved

    int Reserved[53];               //保留
} OldBlueToothPacketSet;

typedef struct
{
    OldPNSettingBase oldcommonParam;
    OldBlueToothPacketSet BtPacketSet;
    int Reserved[128];    //保留
}GenWaveBtOldStruct;

class WTVsg : public WTBsn<VsgParam, DEV_RES_VSG>
{
public:
    // using WTBsn::WTBsn;
    WTVsg(std::shared_ptr<Connector> ExtConn, bool Exclude, const wtev::loop_ref &Loop);
    ~WTVsg();

    //*****************************************************************************
    // 接收外部信号文件
    // 参数[IN]: Conn : 命令收发使用的连接
    //           Name : 文件名
    //           Data : 文件数据
    //           Len  : 文件长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int ExtSigFile(Connector *Conn, const std::string &Name, void *Data, int Len);

    //*****************************************************************************
    // 生成VSG信号文件
    // 参数[IN]: Name : 文件名
    //          Param : 生成参数
    //           Len  : 参数长度
    // 参数[OUT]:FileData：生成的信号文件内容
    //           FileLen: 生成的信号文件长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GenerateSig(const void *Param, int ParamLen, void **FileData, int *FileLen);

    //*****************************************************************************
    // 设置PN数据
    // 参数[IN]: Conn : 命令收发使用的连接
    //         PnHead : PN头
    //        HeadNum ：PN头数量
    //          Items ：PN项
    //        ItemNum ：PN项数量
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetPNCfg(Connector *Conn, const PnItemHead *PnHead, int HeadNum, const ExtPnItem *Items, int ItemNum);

    //*****************************************************************************
    // 获取PN配置
    // 参数[OUT]: PnHead : PN头
    //            Item  : PN数据
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetPNCfg(std::vector<PnItemHead> &PnHead, std::vector<ExtPnItem> &Item);

    //*****************************************************************************
    // 获取默认的信号生成参数
    // 参数[IN] : Demode : 信号调制模式
    // 参数[OUT]: Data : 参数
    //            Len : 数据长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetDefaultGenParam(int Demode, void **Data, int &Len);

    //*****************************************************************************
    // 获取发送功率的范围
    // 参数[IN]: Type: 范围类型, 0 : 标准范围, 1 - 极限范围
    //          Freq : 频率
    // 参数[OUT]: MaxPower: 最大功率
    //            MinPower: 最小功率
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetVsgPowerRange(int Type, double Freq, double &MaxPower, double &MinPower);

    //*****************************************************************************
    // 根据校准数据修正PN数据
    // 参数[IN]: Param : 补偿参数
    //           Chain : 链路Index
    // 参数[OUT]: SamplingRate: PN文件的采样率
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetVsgIfgEnable(Connector *Conn, int Enable);

    int GetVsgIfgEnable() { return m_GapPowerEnable;}

    //程序奔溃时保存vsg中算法m_Alg对象的数据
    void SaveStackData(void);

    //检测已下发的参数是否符合Cal测试标准
    virtual int CheckCalParam(int mode);

    //检测已下发的参数是否符合TB测试标准
    virtual int CheckTBTParam(int mode);

    //是否已经计算TB模式时，启动VSA的位置
    int IsConfigTbPnPos(void) { return m_PnItem[0].TBTStartVsaLen != TB_START_VSA_POS; }

    void SetAndBackupParamTb(void);

    void RestoreParam(void);

    virtual void SetStop();

    double GetVsgPeriod() { return (double)(m_ExtPnItem[0].IFG + m_PnItem[0].Len) / m_Param[0].SamplingFreq; }

    //send file form master to slave
    int SendVsgFileMaterToSlave(Connector *Conn, const std::string &Name); //主机下发文件到从机的协议命令
    int SaveSigFileMasterToSlave(Connector *Conn, const std::string &Name, void *Data, int Len);    //保存主从间传递信号文件处理函数
    int SetPnToSlave(Connector *Conn, char *CurRequest);    //再次主机配置pn到从机

    //TB模式VSA/VSG完成标志相关操作
    void SetTBTModeVsaFinish() { m_IsTBTFinish |= 0x2; }
    void SetTBTModeVsgFinish() { m_IsTBTFinish |= 0x1; }
    bool IsTBTModeFinish() { return ((m_IsTBTFinish & 0x3) == 0x3); }
    void ClearTBTModeFinish() { m_IsTBTFinish = 0; }

    //判断是否释放VSG资源
    virtual void ClearMod(int NeedFreeCnt);

    //20200413 第四版生成信号文件接口
    //*****************************************************************************
    // 生成VSG信号文件
    // 参数[IN]: Name : 文件名
    //          Param : 生成参数
    //           Len  : 参数长度
    // 参数[OUT]:FileData：生成的信号文件内容
    //           FileLen: 生成的信号文件长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GenerateSigWifiV4(const void *Param, int ParamLen, void **FileData, int *FileLen);
    int GenerateSigCWV4(const void *Param, int ParamLen, void **FileData, int *FileLen);
    int GenerateSigBlueToothV4(const void *Param, int ParamLen, void **FileData, int *FileLen);
    int GenerateSigSLE(const void *Param, int ParamLen, void **FileData, int *FileLen);
    int GenerateSig3GPPV4(const void *Param, int ParamLen, void **FileData, int *FileLen);
    int GetGenerateFinalParam(int Demod, void **Param, int &ParamLen);
    int GenerateSigWiSun(const void *Param, int ParamLen, void **FileData, int *FileLen);

    int GetGenerateFinalParam(void **Param, int &ParamLen);
    int GetGenerateFinalGenWaveWifiParam(void **Param, int &ParamLen);

    int GetGenerateReturnParam(int Type, void **Param, int &ParamLen); //type 见枚举VSG_OUT_EXT_DATA_ENUM

    int GetGenerateSynSeq(void **Param, int &ParamLen);

    int SetVsgFlatnessCal(int Enable);
    int SetVsgIQImbCal(int Enable);
    int SetVsgDomianIQForceEnable(int Enable);
    int GetVsgFlatnessCal();
    int GetVsgIQImbCal();

    //配置数字IQ参数
    int SetDigPn();
    int SetDig();

    //下发文件中ofdma的ru carrier信息,mimo时需要转下发给从机
    int SetVsgRuCarrierInfo(Connector *Conn, void * Data);

    void SetGapPowerStatus(int Enable) { m_GapPowerEnable = Enable; }
    int GetGapPowerStatus() { return m_GapPowerEnable; }

    int SetVsgFemParam(Connector *Conn, FemParam FemParamTemp);

    //旧的bt结构转新的结构 20220615~这个接口转的有问题，还没验证后续需要补上，比如packetype转换都是不对的
    int ConvertBTDataToNew(GenWaveBtOldStruct *OldParameters, GenWaveBtStruct &Parameters);

    //检查参数是否符合不停止模块情况下设置硬件的条件
    //int CopyFreqAndPower(VsgParam &Param1,VsgParam &Param2);

    int SetStaticIQImb(int Segment, double Ampl, double Phase, double TimeSkew);
    int ClrStaticIQImb(int Segment);

    VsgParam* GetVsgParam()
    {
        return m_Param.get();
    }

    int GetVsgParamNum()
    {
        return m_ParamNum;
    }

    int SetBroadcastMod(int Index);

    int SetPowerParamToMod(double Param);

    int SetFreqParamToMod(double NewParam);

    int SetPathLossParamToMod(double Param);

    //listmod
    int SetlistEnable();
    int SetlistDisable();
    int SetlistSegTimeParam(int SegNo, SeqTimeParam *Param);
    int SetlistSegVsgSeqStart(int Repet, int EnableFlag, int IncrementFlag, int CellMod);
    int SetlistSegVsgParam(int SegNo, VsgParam *Param);
    int SetlistSegVsgSyncParam(int SegNo, int Status);
    int SetlistSegVsgWaveParam(int SegNo, ExtPnItem *Param);
    int SetlistSegVsgSeqStop();
    int GetlistSegVsgSeqState(int &State);
    int GetListRxSeqAllTransState(int *SegNo);
    //配置ListMode回调
    bool SequenceProc(int Id);
    //ListMode启动模块
    int StartSequenceMod(int CellMod);
    int SetlistSegTrigCommParam(int SegNo, TriggerCommonParam *Param);

private:
    //VSG模块配置信息
    struct VsgInfo
    {
        int CalUnitId;
        int ModId;
        int RFPort;
        double Freq;
        double Power;
        double RFTemperature;
        double SWTemperature;
    };

    struct TX_Param;

    //修改实际的发送功率
    int SetActualAmpl(int Index, Tx_Parm &CalParam);
    int SetActualAmpl(int Index, VsgParam &vsgParam, Tx_Parm &CalParam);

    //将参数配置到硬件模块中
    virtual int SetMod(int Index = -1, int StartVsaXdma=1, int needtrigFlag=1);
    virtual int SetModList(int Repet, int EnableFlag, int IncrementFlag);

     virtual bool GetDmaReTranFlag();

    //配置参数给硬件模块
    virtual int SetExtMode(int ModId);

    //启动硬件开始业务处理
    virtual int StartModDig();

    //将参数配置到硬件模块中
    virtual int StartMod(int ModId);
    virtual int StartModList(int ModId, int CellMod);

    //Vsg连续启动
    int VsgRestartTBTStaMod(int ModId);

    // MIMO从机完成消息处理
    virtual int MimoStartFin();

    //数字IQ操作完成事件回掉函数
    virtual void ProcDigFin(int Status);

    //**************************************************************************
    // VSG发送完成后的回掉函数
    // 参数[IN] : Id : 硬件模块ID
    //           Cnt : 已完成的硬件个数，比如80+80需要两个都完成才算完成
    // 返回值：true : 表示已使用完毕，false：模块仍需继续使用
    //*************************************************************************
    virtual bool ProcModFin(int Id, int Cnt);

    //根据VSA参数生成配置设备的结构体
    VSGConfigType TransConfig(const VsgParam &Param, int Index, int &FileBw);


    //*****************************************************************************
    // 获取PN使用的波形文件
    // 参数[IN]: Pn : PN项
    // 返回值: 成功或错误码
    //*****************************************************************************
    std::string GetWaveFile(const ExtPnItem &Pn);

    //*****************************************************************************
    // 从PN波形文件中读取数据
    // 参数[IN]: WaveFile : 波形文件
    // 参数[OUT]: DataInfo: 数据信息
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetWaveData(const std::string &WaveFile, VsgDataInfo &DataInfo);

    //*****************************************************************************
    // 根据校准数据修正PN数据
    // 参数[IN]: Param : 补偿参数
    //           Chain : 链路Index
    // 参数[OUT]: SamplingRate: PN文件的采样率
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AmendWaveData(const Tx_Parm *Param, int Chain, double &SamplingRate);

    //*****************************************************************************
    // 根据校准数据修正PN数据
    // 参数[IN]: Param : 补偿参数
    //           Chain : 链路Index
    //           FileId : 待补偿信号文件数据id
    // 参数[OUT]: SamplingRate: PN文件的采样率
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AmendWaveData(const Tx_Parm *Param, int Chain, int FileId, double &SamplingRate);
    int AmendWaveDataSegment(const Tx_Parm *Param, int Chain, double &SamplingFreq, VsgParam &vsgParam, VsgDataInfo &vsgData);

    //发送参数到监视机
    void SendParamToMon(void);

    //将MIMO信号数据发送到从机
    int TransFileToSlave(Connector *Conn);

    //计算TB模式时，VSA的启动位置
    int TbStartVsaPosCompute(int Mode);

    //通过文件管理下发文件时，创建文件所保存的目录
    void CreatePath(const std::string &LocalDir, std::string &NeedCreatePath);

    //处理下发文件时，如果带有目录，则先判断和处理目录
    void DealFilePath(const std::string &Name);

    //*****************************************************************************
    // 函数: GetSigFile
    // 功能: 获取信号文件
    // 参数 [IN]：Name : 信号文件名
    // 参数 [OUT]：Data: 信号数据
    //             Len : 数据长度
    // 返回值：成功或者失败错误码
    //*****************************************************************************
    int GetSigFile(const std::string &Name, void **Data, int &Len);

    //新生成接口相关函数声明
    int GetSignalType(const int standard, const int bw, const int mcs, const int streams);
    int GetStandardType(const int standard);
    int CheckGenLicense(GenWaveWifiStruct *Param);
    bool IsTriggerFrame(GenWaveWifiStruct *Param);
    int CheckNotSupportPart(GenWaveWifiStruct *Param);

    //获取wave生成后才确定值的配置参数
    void GetAxTbVariableParameter(GenWaveWifiStruct *param); //mu-mimo时用,目前只有tb mu-mimo的有返回

    //生成文件后，拆分算法附加返回的数据信息，当前有ofdma ru carrier信息
    void SplitAlgExtOutData(const void *Data);
    /**
     * @brief 3GPP算法返回的数据转分析参数
     * 
     * @param Demode 
     * @param pOutExtData 
     * 输出:
     * int 协议标准类型
     * int 错误码
     * int 输出长度
     * void* 配置参数
     */
    void Get3GPPAlzParam(int Demode, void *pOutExtData);

    bool IsMultiPn(const string WaveName);

    int GetTrigCongfig(int TrigType, const SeqTimeType SeqTimeParam, double SamplingRate, VSGTriggerType &VSGTriggerParam);

    int VSGSetTrig(const std::vector<VSGTriggerType> &TrigParam);

    // vsg 是否补偿的判断因素
    // 不改变采样率、频点以及不重新下发信号文件，或者保持在合理的相同的校准功率段内（同功率段主要通过校准返回的tx_link_sw_state和is_pa_on来判断）的情况下不会重新补偿
    struct CompensationFactors
    {
        double Freq = UNVALID_DOUBLE_VAL;          // 保存上一次配置的频点
        double SamplingRate = UNVALID_DOUBLE_VAL;  // 上一次配置的采样率
        int Port = UNVALID_INT_VAL;                // 校准判断功率变化标识
        int Cal_tx_link_sw_stat = UNVALID_INT_VAL; // 校准判断功率变化标识
        int Cal_is_pa_on = UNVALID_INT_VAL;        // 校准判断功率变化标识
        double Power = UNVALID_INT_VAL;            //功率变化标识
        int ForceComp = false;                     //强制重新补偿
        double timeskew = UNVALID_DOUBLE_VAL;
        double gain_imb = UNVALID_DOUBLE_VAL;
        double quad_err = UNVALID_DOUBLE_VAL;
        std::vector<std::string> WaveName;         // 信号文件名
    };

public:
    //设置VSG对象指针
    void SetVsa(const std::shared_ptr<WTVsa> &Vsa) { m_Vsa = Vsa; }

    //获取VSA对象
    WTVsa &GetVsa() { return *(m_Vsa.lock().get()); }

    //获取VSG对象
    WTVsg &GetVsg() { return *this; }

    int GetTBTStaDelay(int FileId, double SamplingFreq);

    int ListModeCheck();//检测指针是否为空防止越界崩溃

    int DuplexLoadDefaultPnData();

    int VsgAllocMod()
    {
        int Mask = m_Param[0].GetModMask();
        return AllocMod(Mask);
    }

private:
    std::weak_ptr<WTVsa> m_Vsa;               // vsa对象指针，用于TBT模式（TBT STA模式需同时操作VSA/VSG）。
    std::unique_ptr<RxSequence> m_RxSeq;
    int m_IsTBTFinish;                         // BIT0:VSA FINISH; BIT1:VSG FINISH
    std::vector<VsgDataInfo> m_VsgData;       // VSG数据
    std::vector<PnItemHead> m_PnItemHead;     // PN头
    std::vector<PnItem>     m_PnItem;         // PN项
    std::vector<int> m_FileBw;                //当前信号文件贷款类型

    std::vector<ExtPnItem> m_ExtPnItem;       //保存外部下发的PN项，方便获取
    //int m_ExtPnLoopRepeat = 3; // 大循环repeat次数
    //int m_ExtPnLoopIfg = 50; // 大循环IFG us
    std::vector<VsgInfo> m_ModInfo;           //当前正在发送的VSG的信息
    std::unique_ptr<char[]> m_GenParam;       // VSG信号文件生成参数
    std::unique_ptr<Compensate> m_Compensate; // 数据补偿类
    Analysis m_Alg;                           // 算法分析模块，主要用于获取mimo或者8080文件中信号的最大功率，为判断信号是否为噪底使用
    double m_ChPower[MAX_SEGMENT_CNT * MAX_NUM_OF_CHNNEL] = {0};
    double m_MaxPower = UNVALID_DOUBLE_VAL;
    std::unique_ptr<char[]> m_CurRequest;                   //当前对外的协议命令，主要用于api下发一个命令后，仪器主从间要操作额外的名命令时返回使用
    std::unique_ptr<SigFile> m_SigFile;                   //信号文件读取缓存,主要是mimo主机给从机发送
    string m_FileName;                                    //要下发的文件名，主要是mimo主机给从机发送
    unsigned int m_UserIfg;                               //保存用户配置IFG
    int m_UserLoop;                                       //保存用户配置Loop
    unsigned int m_TestType;                              //保存用户配置测试模式
    AxTbVariableParameter m_FinalParam;                   // wave生成后才确定的参数
    GenWaveWifiStruct m_FinalGenWaveWifiParam;            // wave生成后保存生成结构，便于外层获取生成结果变量
    Alg_4G_WaveGenType m_FinalGenWaveLTEParam;            // wave生成后保存生成结构，便于外层获取生成结果变量
    AlzParam3GPP m_FinalGenWaveAlzParam;                  // wave生成返回的分析参数, 后面会逐步替代(m_FinalGenWaveLTEParam)
    RUCarrierInfo m_CarrierInfo[MAX_SEGMENT_CNT * MAX_NUM_OF_CHNNEL];                      //保存生成后返回用户的子载波位置信息，用于ofdma功率补偿使用,最多支持16流
    FemParam m_FemParam;                                  // Fem模式参数
    std::unique_ptr<char[]> m_AlgGenOutPutData = nullptr; // wave生成过后保存算法返回的参数内容，便于外层api获取算法返回
    int TotalOutPutData = 0;                              //对应m_AlgGenOutPutData的总长度
    int m_GapPowerEnable = 1;
    CompensationFactors m_AmendFactors[MAX_SEGMENT_CNT * MAX_NUM_OF_CHNNEL]; // 最多支持8080两个模块
    int m_synSeq[64];
    int m_synSeqLen;
    bool m_IqCompChangeFlag; // vsg IQ补偿参数是否改变(频域/时域)
};


#endif
