//************************************************************************
//  Copyright (c) 2016,Shenzhen iTest Communication Tech. Co., Ltd
//  All rights reserved.
//  File name: TesterWrapper.h
//  Description:
//
//  Current version: 1.1
//  Author: kong
//  Date:   2016/8/16
//
//  Replaced version: /
//  Original author:  /
//  Date: /
//************************************************************************
#pragma once
#ifndef _TESTER_WRAPPER_H__
#define _TESTER_WRAPPER_H__

#ifndef LINUX
#ifdef WT4XXWRAPPER_EXPORTS
#define WT4XXWRAPPER_API __declspec(dllexport)
#else
#define WT4XXWRAPPER_API __declspec(dllimport)
#endif

#else

#ifndef __stdcall
#define __stdcall
#endif // !__stdcall

#ifdef WT4XXWRAPPER_EXPORTS
#define WT4XXWRAPPER_API __attribute ((visibility("default")))
#else
#define WT4XXWRAPPER_API
#endif

#endif

class WT4XXWRAPPER_API TesterWrapper
{
public:
    TesterWrapper(){}
	virtual ~TesterWrapper(){};
public:

    virtual void SetPnMem(void *pData) = 0;

	virtual int ConnectDevice(ConnectedUnit *connUnit, int unitCount, int connType) = 0;
	virtual int DisConnect() = 0;

	// MEASURE
	virtual int SwitchMode(int targetMode) = 0;
	virtual int AddMimoTester(ConnectedUnit connUnit) = 0;
	virtual int RemoveMimoTester() = 0;
	virtual int SetNetInfo(VirtualNetType *netInfo) = 0;
	virtual int GetNetInfo(VirtualNetType *netInfo) = 0;
    virtual int GetNetLink(bool *LinkStatus) = 0;
	virtual int GetLicense(LicItemInfo_API *testerLicense, int licenseMaxCount, int *licActualcount) = 0;
	virtual int GetSlaveTesterLicense(int slaveTesterID, LicItemInfo_API *licInfo, int licenseMaxCount, int *licActualcount) = 0;
	virtual int GetTesterInfo(TesterInfo *info) = 0;
    virtual int GetTesterIpAddressType(bool *IsDhcp) = 0;
	virtual int GetSlaveTesterInfo(int slaveTesterID, TesterInfo *testerInfo) = 0;
	virtual int GetDefaultParameter(VsaParameter *vsaParam, VsaAvgParameter *avgParam, VsgParameter *vsgParam, VsgWaveParameter *waveParam, VsgPattern *vsgPattern) = 0;
    virtual int ClearSampRateFromFileFlag(void) = 0;
	virtual int SetVSA(VsaParameter *vsaParam, ExtendVsaParameter *extParam) = 0;
	virtual int SetVSAAverageParameter(VsaAvgParameter *vsaParam) = 0;
	virtual int ClrVSAAvgData() = 0;
	virtual int SetVSATrigParam(VsaTrigParam *Param) = 0;
	virtual int GetVSATrigParam(VsaTrigParam* Param) = 0;
	virtual int SetVSAAutorange(VsaParameter *vsaParam, ExtendVsaParameter *extParam) = 0;
	virtual int GetVSAParameter(int demodType, VsaParameter *vsaParam, ExtendVsaParameter *extvsaParam) = 0;
    virtual int GetCurrVSAStatus(int *status) = 0;
	virtual int DataCapture() = 0;
    virtual int DataCaptureAsync() = 0;
	virtual int PauseDataCapture() = 0;
	virtual int StopDataCapture() = 0;
	virtual int SetGeneralAnalyzeParam(AlzParamComm *commonAnalyzeParam) = 0;
	virtual int Analyze(int demodType, AnalyzeParam *analyzeParams, int paramsSize, const char *refFileName, int frameID, unsigned int timeoutMs) = 0;
    virtual int SetAlzParam(int analyzeParamType, AnalyzeParam *analyzeParams, int paramsSize) = 0;
	virtual int SetExternAnalyzeParam(int demod, int paramType, void *param, int paramSize) = 0;
	virtual int GetResult(const char *anaParamString, double *result, int signalID, int segmentID) = 0;
	virtual int GetBaseResult(VsaBaseResult *result, int signalID, int segmentID) = 0;
	virtual int GetVectorResultElementSize(const char *anaParamString, unsigned int *elementSize, int signalID, int segmentID, int SegNo = -1) = 0;
	virtual int GetVectorResultElementCount(const char *anaParamString, unsigned int *elementCount, int signalID, int segmentID, int SegNo = -1) = 0;
	virtual int GetVectorResult(const char *anaParamString, void *result, unsigned int resultSize, int signalID, int segmentID, int SegNo = -1) = 0;
    virtual int GetListAllsegVectorResult(const char *ParamString, void *Result, int ResultSize, int &RetResultSize) = 0;
	virtual int GetSLEAverageResult(VsaSLECommResult* result, VsaSLECommResult* max, VsaSLECommResult* min, int times, int signalID, int segmentID) = 0;
	virtual int Get3GPPAverageResult(Vsa3GPPCommResult *result, Vsa3GPPCommResult *max, Vsa3GPPCommResult *min, int times, int signalID, int segmentID) = 0;
	virtual int Get3GPPModulationAvgResult(Alg_NBIOT_StatOutModuInfo* result, int times, int signalID, int segmentID) = 0 ;
	virtual int GetBTAverageResult(VsaBTCommResult* result, VsaBTCommResult* max, VsaBTCommResult* min, int times, int signalID, int segmentID) = 0;
	virtual int GetAverageResult(VsaAverageResult *result, VsaAverageResult *max, VsaAverageResult *min, int times, int signalID, int segmentID) = 0;
	virtual int GetAvgBaseCompositeResult(VsaBaseResult *result, int segmentID) = 0;
	virtual int GetCurrAverageCount(int *count) = 0;
	virtual int SetVSGParameter(VsgParameter *vsgParam, ExtendVsgParameter *extParam) = 0;
	virtual int SetVSGWaveParameter(VsgWaveParameter *vsgWaveParam) = 0;
    virtual int GetVSGParam(VsgParameter *vsgParam, VsgWaveParameter *vsgWaveParam, VsgPattern *vsgPattern, ExtendVsgParameter* extParam) = 0;
	virtual int SetVSGPattern(VsgPattern *vsgPatternParam, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount) = 0;
	virtual int GetVSGPattern(VsgPattern *vsgPattern, unsigned int vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount, unsigned int *actualPnItemCount, unsigned int *actualPnHeadCount) = 0;
    virtual int GetVSGSendPacketCnt(int *Cnt) = 0;
	virtual int AnalyzeVSGData(const char *waveName, int demodType, AnalyzeParam *analyzeParams, int paramsSize, int timeOutMs) = 0;
	virtual int GetVSGDataResultElementSize(const char *anaParamString, unsigned int *elementSize, int signalID, int segmentID) = 0;
	virtual int GetVSGDataResultElementCount(const char *anaParamString, unsigned int *elementCount, int signalID, int segmentID) = 0;
	virtual int GetVSGDataVectorResult(const char *anaParamString, void *result, unsigned int resultSize, int signalID, int segmentID) = 0;
	virtual int SetAnalyzeGroupParam(char *anaParamString) = 0;
	virtual int StartVSG() = 0;
	virtual int AsynStartVSG() = 0;
	virtual int GetCurrVSGStatu(int *statu) = 0;
	virtual int PauseVSG() = 0;
	virtual int StopVSG() = 0;
	virtual int SetExternalGain(double extGain) = 0;
	virtual int GetExternalGain(CableVerifyParameter *param, double *extGain) = 0;
	virtual int CablelossCal(CalTestingInAgrs *calParameter, void (__stdcall *pfunCallBack)(CalTestingCallbackArgs callbackArgs)) = 0;
	virtual int GetConnectStatus() = 0;

	virtual int BeamformingCalibrationChannelEstDutTX(int demod) = 0;
	virtual int BeamformingCalibrationChannelEstDutRX(double *dutChannelEst, int dutChannelEstLength) = 0;
	virtual int BeamformingCalibrationResult(double *resultAngle, int *resultAngleLength) = 0;
	virtual int BeamformingVerification(double *diffPower) = 0;
	virtual int BeamformingCalculateChannelProfile(int demod, double *resultBuf, int *resultLength) = 0;
    virtual int BeamformingCalculateChannelAmplitudeandAngleBCM(int *ValidSpatialStreams, int *DataLen, double *resultBuf, int *resultLength) = 0;

	virtual int DeleteTesterWaveFileOrRefFile(const char *fileName) = 0;
	virtual int AddTesterWaveFileOrRefFile(const char *fileName, const char *saveFileName, int acWave2) = 0;
	virtual int GetTesterAllWaveFileOrRefFileNames(const char *path, char *fileNameBuffer, int fileNameBuffSize, unsigned int *fileCount) = 0;
	virtual int QueryTesterWaveFileOrRefFile(const char *fileName, int *waveExists) = 0;
	virtual int GetTesterWaveFileOrRefFileSize(const char *fileName, unsigned int *fileSize) = 0;
	virtual int GetTesterWaveFileOrRefFile(const char *fileName, char *fileBuff, unsigned int fileBuffSize) = 0;

	virtual int SetCmimoRefFile(const char *fileName) = 0;
	virtual int GetHardErrorCodeSize(int  *errorCodeAcount) = 0;
	virtual int GetSlaveHardErrorCodeSize(int slaveTesterID, int *errorCodeAcount) = 0;
	virtual int GetHardErrorCode(int *hardErrCode, int errorCodeAcount) = 0;
	virtual int GetSlaveHardErrorCode(int *hardErrCode, int slaveTesterID, int errorCodeAcount) = 0;
	virtual int StartPerTesting(PerMacParameter *MacParameter, PerActionParameter *perParameter, void(*pfunCallback)(PerResultParameter progress)) = 0;
	virtual int StopPerTesting() = 0;
	//METER
	virtual int SetMoniObj(int obj) = 0;
	virtual int MoniConfig(int action, int dataType, const char *anaParamString) = 0;
	virtual int GetMoniResultSize(unsigned int *dataSize) = 0;
	virtual int GetMoniResult(char *resultBuff, unsigned int resultBuffSize, int *dataType) = 0;
	virtual int QueryMoniVSAParam(char *resultBuff, unsigned int resultBuffSize, VsaParameter *vsaParam) = 0;
	virtual int QueryMoniVSGParam(char *resultBuff, unsigned int resultBuffSize, VsgParameter *vsgParam) = 0;
	virtual int QueryMoniVSAResult(char *resultBuff, unsigned int resultBuffSize, const char *anaParamString, int signalID, int segmentID, char **dataPtr, unsigned int *elementCount, unsigned int *elementSize) = 0;
	virtual int QueryMoniVSAAnalyzeParam(char *resultBuff, unsigned int resultBuffSize, AlzParamComm *commonAnalyzeParam, void *analyzeParam, unsigned int paramSize, int *signalType) = 0;
	virtual int QueryMoniVSGPattern(char *resultBuff, unsigned int resultBuffSize, VsgPattern *vsgPattern, unsigned int vsgPnItemCount,
									PnItemHead_API *vsgPnHead, unsigned int vsgPnHeadCount, unsigned int *actualPnItemCount, unsigned int *actualPnHeadCount) = 0;
	virtual int SetMeterConfiguration(char *config, unsigned int size) = 0;
	virtual int QueryMeterConfiguration(char *resultBuff, unsigned int resultBuffSize, char *ConfigParam) = 0;
	virtual int GetPnDescription(char *fileName, char *description, unsigned int size) = 0;
	virtual int StartRecord(bool isLocalSave) = 0;
	virtual int FinishRecord() = 0;
	virtual int GetTesterAllRecordNames(char *recordNameBuffer, int recordNameBufferSize, int *recordCount) = 0;
	virtual int ReadRecord(char *recordName, char *Data) = 0;
	virtual int GetGUIVersion(GUIVersion *guiVersion, unsigned int maxGuiVersionCnt, unsigned int *actualGuiVersionCnt) = 0;
	virtual int GetGUIFileVersion(const char *techName, GUIVersion *version) = 0;
	virtual int GetGUIFile(const char *techName, const char *saveDir) = 0;
	virtual int GetMonitorInfos(MonitorInfo *monitorInfo, unsigned int maxMonInfoCount, unsigned int *actualMonInfoCount) = 0;
	virtual int LoadSignalAsCapture(const char *fileName, const char *saveFileName, int acWave2) = 0;
	virtual int SaveSignal(const char *fileName, int saveOption, char *signalBuff, unsigned int signalBuffSize) = 0;
	virtual int SaveCurrIQDataToFile(int saveType, const char *fileName, char *calParam, unsigned int calParamSize, int signalID) = 0;
	virtual int SaveOriginalIQDataToFile(const char *fileName, char *calParam, unsigned int calParamSize, int signalID) = 0;
	virtual int TruncateSaveSignal(int type, const char *fileName, int startUs, int endUs) = 0;
    virtual int MoniTruncateSaveSignal(int type, const char *fileName, int startUs, int endUs) = 0;
	virtual char *GetErrString(int err) = 0;
	virtual int GetRefLvlRange(double freq, int option, int *upperLimit, int *lowerLimit) = 0;
	virtual int GetTxPowerRange(double freq, int option, int *upperLimit, int *lowerLimit) = 0;
	virtual int GetCurrSubTesterCfg(SubTesterCfg *testerCfg) = 0;
	virtual int GetSlaveTesterSubTesterCfg(int slaveTesterID, SubTesterCfg *testerCfg) = 0;
	virtual int GetTesterErrorCode(int *errCode, char *errMsg, unsigned int errMsgSize) = 0;
	virtual int SetMasterMode(int vsaMasterMode, int vsgMasterMode) = 0;
	virtual int GetFileSampleFreq(double *SampleFreq) = 0;

	virtual int GenSensetivityWave(PerMacParameter *MacParameter, char *WaveName) = 0;
	virtual int GetSensetivityResult(int demod, int *AckCnt) = 0;
	virtual int SetPathLossFile(const char *fileName) = 0;
	virtual int GetPathLossFileSize(unsigned int *fileSize) = 0;
	virtual int GetPathLossFile(const char *fileName, unsigned int fileSize) = 0;

	virtual int CalculateImbalance(double *imbAmp, double *imbPhase, double *timeSkew, int segmentID) = 0;
	virtual int SetFixedImbalance(int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID) = 0;
	virtual int SetVsgFixedImbalance(int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID) = 0;

	virtual int RebootTester() = 0;
	virtual int SetSubTesterCfg(SubTesterCfg *testerCfg, int testerCount) = 0;
	virtual int GetSubTesterCfg(SubTesterCfg testerCfg[WT_SUB_TESTER_INDEX_MAX], int *subTesterCount) = 0;
	virtual int FirmwareUpdate(const char *packageFile, char *upgradePackage, unsigned int packageSize) = 0;
	virtual int LicenseUpdate(char *licensePackage, unsigned int packageSize) = 0;
	virtual int LicensePack(char *licensePackage, unsigned int packageSize, LicItemInfo_API *licInfo, unsigned int *actualSize) = 0;
	virtual int LicPackUpdate(char *Message) = 0;

	virtual int SetTesterInfo(const char *name, const char *ipAddr, const char *subMask, const char *netGate) = 0;
	virtual int FirmwareRestore(int restoreOption) = 0;

	virtual int GetTesterSystemTime(TIME_TYPE_API *sysTime) = 0;
	virtual int FactoryReset() = 0;
	virtual int ReadLog(LogFilter *filter, char *filterLog, int logMaxBuff) = 0;
	virtual int Diagnose(DiagnoseSetting setting, char *diaInfo, unsigned int infoSize) = 0;
	virtual int AnalyzeDiagnoseLog() = 0;
	virtual int GetFpgaConfig(FpgaConfig *config) = 0;
	virtual int GetOSStatus(OSStatu *osStatu) = 0;

	virtual int GetProcStatus(ProcStatu *procStatu) = 0;
	virtual int TesterLogSettingInfo(int type, int *log_cfg, unsigned int size) = 0;

	virtual int SendCalFile(char *fileBuffer, unsigned int fileBuffSize, const char *fileName) = 0;
	virtual int GetCalFile(const char *fileName, char *fileBuffer, unsigned int fileBuffSize) = 0;
	virtual int SetCalData(char *data, unsigned int dataSize) = 0;
	virtual int GetCalData(char *data, unsigned int dataSize) = 0;
	virtual int SetBBGain(int gain) = 0;
	virtual int GetTemperature(DevTemperature *temp) = 0;
	virtual int GetTemperatureHistory(int *Cnt, DevTempSave *info, unsigned int infoSize) = 0;
	virtual int SetTempCal(int value) = 0;
	virtual int SetFlatnessCal(int value) = 0;
	virtual int GetVoltage(DeviceVoltage *voltage, int voltageCnt, int *count) = 0;
	virtual int GetFanSpeed(int *speed) = 0;
	virtual int WriteSN(const char *password, const char *SN) = 0;
	virtual int SetATT(double *att) = 0;
	virtual int GetATT(double *att) = 0;
	virtual int SetComponentValue(ComponentLocation componentLocation, int componentValue) = 0;
	virtual int GetComponentValue(ComponentLocation componentLocation, char *componentValue, int bufSize, int *dataLen) = 0;
	virtual int InitCalData() = 0;
	virtual int GetRxGain(int ModuleID, char *buf, unsigned int bufSize) = 0;
	virtual int GetTxGain(int ModuleID, char *buf, unsigned int bufSize) = 0;
	virtual int ShutDownDevice() = 0;
	virtual int DeleteAllLicense() = 0;
	virtual int ResetSubNetConfiguration() = 0;
	virtual int SetFPGAIFG(int onff) = 0;
	virtual int GetFPGAIFG(int *onff) = 0;
	virtual void memdump(const char *filename, int id) = 0;

	virtual int WriteRemoteFile(const char *filename, char *buffer, unsigned int buffer_size) = 0;
	virtual int ReadRemoteFile(const char *filename, unsigned int *filesize, char *buffer, unsigned int buffer_size) = 0;
	virtual int ExecShellCmd(const char *cmd, char *buffer, unsigned int buffer_size) = 0;
	virtual int QueryTesterVersionInfo(char *testerInfo) = 0;
	virtual int PacStartGetData(PacParameter *param, PacAttribute *attribute, void(*pfunCallback)(PacProgressParameter *progress)) = 0;
	virtual int PacCalData(PacDataAvg *buf, int BufCnt, int *retCnt) = 0;
	virtual int PacDumpResult(int mode) = 0;
	virtual int PacStop() = 0;
    virtual int PacGetModeCalData(int mode, int *Cnt, PacDataInfo **data) = 0;

    virtual int SetWifiFrameFilter(ResultFilter *filter) = 0;
    virtual int SetWideSpectrumEnable(int onff) = 0;

    virtual int TB_Init() = 0;
    virtual int TB_Release() = 0;
    virtual int TB_Start(int timeout_ms) = 0;
    virtual int TB_Stop() = 0;
    virtual int TB_Status(int *stauts) = 0;
    virtual int TB_SetParam(InterBindParameter *Param, int vsaTrigger = WT_TRIG_TYPE_FREE_RUN_API) = 0;
	virtual int TB_AutoRange(InterBindParameter *Param) = 0;

    virtual int TF_Start(int timeout_ms) = 0;
    virtual int TF_Stop() = 0;
    virtual int TF_GetStatus(int *status) = 0;
    virtual int TF_SetParam(InterBindParameter *Param, int vsaTrigger = WT_TRIG_TYPE_FREE_RUN_API) = 0;

    virtual int File2TBParam(int ParamType, void *src_param, int paramSize, AlzParamAxTriggerBase *dest) = 0;
	virtual int File2SLEParam(int ParamType, void* src_param, int paramSize, AlzParamSparkLink* dest) = 0;
	virtual int FileRefParam(int ParamType, void *src_param, int paramSize, AnalyzeParam *dest) = 0;
	
    virtual int SelfCalibration(bool isStart = true) = 0;
    virtual int QuerySelfCalibrationPercent(int *percent) = 0;
    virtual int SelfCalibrationAutoRunning(int isAutoRun) = 0;
    virtual int AxValidCommonBit(int demod, int *commonBit, int *userInRU, int *RUCnt, int *center26RU, int *RUPos) = 0;
    virtual int BeValidCommonBit(int demod, int fullBand, int *commonBits, int *Punctured, int *userInRU, int *TotalRU, int *RUPos) = 0;
    virtual int PacSectionParameter(PacSectionParam *param) = 0;
    virtual int SetWaveCalDataCompensate(int ON_FF) = 0;
	virtual int SetVsgIQImbCompensate(int ON_FF) = 0;
	virtual int SetVsaCalDataCompensate(int ON_FF) = 0;
	virtual int SetVsaIQImbCompensate(int ON_FF) = 0;
	virtual int GetVsgFlatnessCalCompensate(int *ON_FF) = 0;
	virtual int GetVsgIQImbCompensate(int *ON_FF) = 0;
	virtual int GetVsaFlatnessCalCompensate(int *ON_FF) = 0;
	virtual int GetVsaIQImbCompensate(int *ON_FF) = 0;
    //Wave generator
    virtual int GetDefaultWaveParameterBT(GenWaveBtStruct_API *pnParameters) = 0;
    virtual int GetDefaultWaveParameterBTV2(GenWaveBtStructV2 *pnParameters) = 0;
    virtual int GetDefaultWaveParameterCW(GenWaveCwStruct *pnParameters) = 0;
	virtual int WaveGeneratorCatenateFiles(const MutiPNCatenateInfo *catenateInfo) = 0;
    virtual int GetDefaultWaveParameterWifi(int demod, int ppdu, GenWaveWifiStruct_API *pnParameters) = 0;
	virtual int GetDefaultWaveParameterGLE(GenWaveGleStruct* pnParameters) = 0;
    virtual int WaveGeneratorWifi(const char *fileName, GenWaveWifiStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo) = 0;
    virtual int WaveGeneratorBlueTooth(const char *fileName, GenWaveBtStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo) = 0;
	virtual int GetDefaultWaveParameterWiSun(GenWaveWisunStruct* pnParameters) = 0;
    virtual int WaveGeneratorBlueToothV2(const char *fileName, GenWaveBtStructV2 *pnParameters, MutiPNExtendInfo *MutiPnExInfo) = 0;
    virtual int WaveGeneratorCW(const char *fileName, GenWaveCwStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo) = 0;
	virtual int WaveGeneratorGLE(const char *fileName, GenWaveGleStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo) = 0;
    virtual int WaveGenerator3GPP(const char *fileName, void *pnParameters, int Paramlen, std::shared_ptr<AlzParam3GPP>& pAlz3gpp) = 0;
	
	virtual int WaveGeneratorWiSun(const char* fileName, GenWaveWisunStruct* pnParameters) = 0;

    virtual int SetVSGFemParamter(FemParameter *param) = 0;
    virtual int CleanTesterCustomerWave() = 0;
    virtual int QueryTesterDiskUseInfo(TesterDiskUsage *testerInfo) = 0;
    virtual int SetSubNetAutoNeg(int Enable) = 0;
	virtual int GetSubNetAutoNeg(int *ON_FF) = 0;
    virtual int SetDigtalIQParam(DigtalIQParam *param) = 0;
    virtual int SetDigtalIQTestFixture(DigtalIQTestFixture *param) = 0;
    virtual int SetDigtalIQMode(int mode) = 0;
	virtual int AckConnectInfo(char *data, int len) = 0;
	virtual int GetConnectDetail(char *buffer, int buffersize) = 0;
    virtual int StartFastAttCal(ATTCalCfg_API *config, ATTCalResult_API *result) = 0;
    virtual int StopFastAttCal() = 0;
    virtual int SetMaxSampleRate(double maxRate) = 0;
    virtual int SetPNFileExternSettingData(void *data, int len) = 0;
	virtual int SubCmdHandle(SubCmdType* SubCmd) = 0;
    virtual void UpdateVsgParam(VsgParameter *param, ExtendVsgParameter *extParam) = 0;
    virtual int CreateLowWave(int type, const char *filename, const char *savename) = 0;
	virtual int GetPnCount(const char * filename, int &RetPnCount, int *PnOrder) = 0;
	virtual int GetConnectInfo(char* Ip, int* Port) = 0;
	virtual int TestConnectStatus() = 0;
    virtual int GetDockerAppList(char *buffer, int buflen, int *AppCnt) = 0;
    virtual int SetDockerAppEnable(char *name, int onoff) = 0;
    virtual int DelDockerApp(char *name) = 0;
    virtual int SetLOMode(int mode,int ModId) = 0;
	virtual int GetLOMode(int *mode, int ModId) = 0;
	virtual int SetIQMode(int *mode, int len) = 0;
	virtual int GetIQMode(int *mode, int *len) = 0;
	virtual int GetSpectrumPointPower(double Offset, double* Power, int signalID, int segmentID) = 0;
	virtual int SetWaveGeneratorTimeout(int Time) = 0;
	virtual int GetWaveGenCBFReportField(CBFReport *ReportField) = 0;
    virtual int SetVsaNoiseCalibrationStart(int PortList[8]) = 0;
	virtual int SetVsaNoiseCalibrationStop() = 0;
	virtual int GetVsaNoiseCalibrationStatus(int &Status) = 0;
	virtual int GetVsaNoiseCalibrationPortValid(int Status[8][8], int &TesterCount) = 0;
	virtual int SetVsaExtendEVMStatus(int Status) = 0;
	virtual int SetBroadcastEnable(int Status) = 0;
	virtual int GetBroadcastEnable(int &Status) = 0;
	virtual int SetBroadcastDebugEnable(int Status, double Power[WT_PORT_RF8 - WT_PORT_OFF]) = 0;
	virtual int GetBroadcastRunStatus(int &Status) = 0;
	virtual int SetVsaIterativeEVMStatus(int Status) = 0;
	virtual int SetVsaSncEVMStatus(int Status) = 0;
	virtual int SetVsaCcEVMStatus(int Status) = 0;
    virtual int SetListModeEnable(bool TxFlag) = 0;
    virtual int SetListModeDisable(bool TxFlag) = 0;
	virtual int SetSegVsaClear() = 0;
    virtual int SetSegVsaAlzCommParam(int SegNo, AlzParamComm *commonAnalyzeParam) = 0;
    virtual int SetSegVsaAllAlzCommParam(AlzParamComm *AllcommonAnalyzeParam, int Size) = 0;
    virtual int SetSegVsaCapParam(int Segno, VsaParameter *vsaParam) = 0;
    virtual int SetSegVsaAllCapParam(VsaParameter *AllvsaParam, int Size) = 0;
    virtual int SetSegAllVsgParam(VsgParameter *AllvsgParam, int Size) = 0;
    virtual int SetSegVsaTrigParam(int Segno, VsaTrigParam *vsatrigParam) = 0;
    virtual int SetSegVsaAllTrigParam(VsaTrigParam *vsaAlltrigParam, int Size) = 0;
    virtual int SetSegTrigCommonParam(bool TxFlag, int Segno, void *SegTrigCommParam, int Size) = 0;
    virtual int SetSegAllTrigCommonParam(bool TxFlag, void *AllSegTrigCommParam, int Size) = 0;
    virtual int SetSegVsaAlzProtoParam(int Segno, int AlzType, void *ProAnalyzeParam, int Size) = 0;
    virtual int SetSegVsaAllAlzProtoParam(void *AllProAnalyzeParam, int Size) = 0;
    virtual int SetSegSeqTimeParam(bool TxFlag, int Segno, void *SegTimeParam, int Size) = 0;
    virtual int SetSegSeqAllTimeParam(bool TxFlag, void *AllSegTimeParam, int Size) = 0;
    virtual int SetListTxSeqStart(double TrigerOffset) = 0;
    virtual int SetListRxSeqStart(int Repet, int EnableFlag, int IncrementFlag, int CellMod) = 0;
    virtual int SetListTxRxSeqStart() = 0;
    virtual int SetSegVsgParam(int Segno, VsgParameter *vsgParam) = 0;
    virtual int SetSegVsgSyncParam(int Segno, int Status) = 0;
    virtual int SetSegVsgAllSyncParam(int *AllSyncParam, int Size) = 0;
    virtual int SetSegVsgWaveParam(int Segno, VsgPattern *vsgWaveParam) = 0;
    virtual int SetSegAllVsgWaveParam(VsgPattern *AllvsgWaveParam, int Size) = 0;
    virtual int SetListTxSeqStop() = 0;
    virtual int SetListRxSeqStop() = 0;
    virtual int SetListTxRxSeqStop() = 0;
    virtual int GetListSeqAllState(bool TxFlag, int *State) = 0;
    virtual int GetListTxSeqAllCapState(int *SegNo) = 0;
    virtual int GetListRxSeqAllTransState(int *SegNo) = 0;
    virtual int GetListTxSeqAllAnalyState(int *SegNo) = 0;
    virtual int GetListTxSeqAllPowerResult(double *PowerResult, int SegNum) = 0;
    virtual int GetListLteTxSeqAllSegState(int *LteTxSegStat, int SegNum) = 0;
    virtual int DuplexSetEnable(int enable) = 0;
    virtual int DuplexGetEnable(int *enable) = 0;
    virtual int SetDuplexVsaNoiseCompFlag(int flag) = 0;
    virtual int GetDuplexVsaNoiseCompFlag(int *flag) = 0;
};
#endif
