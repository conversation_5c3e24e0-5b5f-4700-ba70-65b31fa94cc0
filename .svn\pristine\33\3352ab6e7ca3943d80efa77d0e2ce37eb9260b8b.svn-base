#ifndef _VSA_WIFI_DATA_INFO_H_
#define _VSA_WIFI_DATA_INFO_H_
#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif
    int StandardBetween(scpi_t *context, int minValue, int maxValue);
    int is11B(scpi_t *context);
    int is11AG(scpi_t *context);
    int is11N(scpi_t *context);
    int is11AC(scpi_t *context);
    int is11AC_MUMIMO(scpi_t *context, bool &MUFlag);
    int is11AX(scpi_t *context);
    int is11AX_MUMIMO(scpi_t *context, bool &MUFlag);
    int is11BE(scpi_t *context);
    int is11BE_MUMIMO(scpi_t *context, bool &MUFlag);
    int is11BA(scpi_t *context);
    
    scpi_result_t GetVsaRst_11ag_DataRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ag_SymbolCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ag_PSDULen(scpi_t *context);
    scpi_result_t GetVsaRst_11ag_CodingRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ag_Modulation(scpi_t *context);
    scpi_result_t GetVsaRst_11ag_duplicate(scpi_t *context);
    scpi_result_t GetVsaRst_NonHTDuplicatePuncturedInfo(scpi_t *context);
    scpi_result_t GetVsaRst_11ag_LSIG_Bit(scpi_t *context);
    /////////////////////////////////////////////////////////
    scpi_result_t GetVsaRst_11b_DataRate(scpi_t *context);
    scpi_result_t GetVsaRst_11b_DataLen(scpi_t *context);
    scpi_result_t GetVsaRst_11b_PSDULen(scpi_t *context);
    scpi_result_t GetVsaRst_11b_PreambleType(scpi_t *context);
    scpi_result_t GetVsaRst_11b_SDFPass(scpi_t *context);
    scpi_result_t GetVsaRst_11b_HeaderPass(scpi_t *context);
    ///////////////////////////////////////////////////////////
    scpi_result_t GetVsaRst_11n_DataRate(scpi_t *context);
    scpi_result_t GetVsaRst_11n_SymbolCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11n_PSDULen(scpi_t *context);
    scpi_result_t GetVsaRst_11n_CodingRate(scpi_t *context);
    scpi_result_t GetVsaRst_11n_Modulation(scpi_t *context);
    scpi_result_t GetVsaRst_11n_HTSIGCRC(scpi_t *context);
    scpi_result_t GetVsaRst_11n_MCS(scpi_t *context);
    scpi_result_t GetVsaRst_11n_BW(scpi_t *context);
    scpi_result_t GetVsaRst_11n_HTLength(scpi_t *context);
    scpi_result_t GetVsaRst_11n_Smoothing(scpi_t *context);
    scpi_result_t GetVsaRst_11n_Sounding(scpi_t *context);
    scpi_result_t GetVsaRst_11n_Aggregation(scpi_t *context);
    scpi_result_t GetVsaRst_11n_STBC(scpi_t *context);
    scpi_result_t GetVsaRst_11n_CodingType(scpi_t *context);
    scpi_result_t GetVsaRst_11n_GIType(scpi_t *context);
    scpi_result_t GetVsaRst_11n_SSTrms(scpi_t *context);
    scpi_result_t GetVsaRst_11n_Crc(scpi_t *context);
    scpi_result_t GetVsaRst_11n_HTSIGTail(scpi_t *context);
    scpi_result_t GetVsaRst_11n_LSIGValid(scpi_t *context);
    scpi_result_t GetVsaRst_11n_LSIGParity(scpi_t *context);
    scpi_result_t GetVsaRst_11n_LSIGRate(scpi_t *context);
    scpi_result_t GetVsaRst_11n_LSIGLen(scpi_t *context);
    scpi_result_t GetVsaRst_11n_LSIGTail(scpi_t *context);
    scpi_result_t GetVsaRst_11n_LSIGBit(scpi_t *context);
    scpi_result_t GetVsaRst_11n_HSIGBit(scpi_t *context);
    ////////////////////////////////////////////////
    scpi_result_t GetVsaRst_11ac_PPDU(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_DataRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SymbolCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_PSDULen(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_CodingRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_Modulation(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SIGACRC(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_MCS(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_BW(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_LTFCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_NSTS(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_NSS(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_STBC(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_CodingType(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_GIType(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_GroupID(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SIGBCRC(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SIGBMCS(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SIGBLen(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_LSIGParity(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_LSIGRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_LSIGLen(scpi_t *context);

    scpi_result_t GetVsaRst_11ac_PartialAID(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_TXOP_PS(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_Beamformed(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SoundingNDP(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_LDPCExtra(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_NsymDisambiguity(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_LSIG_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SIGA_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11ac_SIGB_Bit(scpi_t *context);
    ////////////////////////////////////////////////
    scpi_result_t GetVsaRst_11ax_PPDU(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGACRC(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_BW(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_RUCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_UserCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SymbolCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_LTFCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_LTFType(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_LTFLen(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_GILen(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_DataRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_DataSymbolLen(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_FrameLen(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_BSSColor(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_BeamChange(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_LDPCExtra(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_PEDisambiguity(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_PELen(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_PreFecPadFactor(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_Doppler(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_MidamblePeriodicity(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBCRC(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBDCM(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBMCS(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBSymbolCnt(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBCompression(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBEVMALL(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBEVMData(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBEVMPilot(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBEVMALL_Composite(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBEVMData_Composite(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGBEVMPilot_Composite(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_Common8Bit(scpi_t *context);

    scpi_result_t GetVsaRst_11ax_SoundingNDP(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_PunctureMode(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_TXOP(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SReuse(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_Beamformed(scpi_t *context);

    scpi_result_t GetVsaRst_11ax_LSIG_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGA_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGB_Bit_1(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_SIGB_Bit_2(scpi_t *context);

    //Lsig
    scpi_result_t GetVsaRst_11ax_LSIGParity(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_LSIGRate(scpi_t *context);
    scpi_result_t GetVsaRst_11ax_LSIGLen(scpi_t *context);

    /////////////////////////////
    scpi_result_t GetVsaRst_11Be_LSIG_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11Be_USIG_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11Be_SIG_CTX1_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11Be_SIG_CTX2_Bit(scpi_t *context);
    /////////////////////////////////////////////////////////
    scpi_result_t GetVsaRst_11Ba_BW(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_LSigParityPassed(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_LSigRate(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_LSigLength(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_LSig_Bit(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_SubChannelCount(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_PuncturedInfo(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_DataRateMode(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_PSUDLength(scpi_t *context);
    scpi_result_t GetVsaRst_11Ba_PSDU_CRC(scpi_t *context);

    scpi_result_t GetVsaRst_DataburstInfo(scpi_t *context);
#ifdef __cplusplus
}
#endif

#endif