//*****************************************************************************
//  File: protocol.h
//  内部协议解析
//  Data: 2016.7.14
//*****************************************************************************
#ifndef __WT_PROT_H__
#define __WT_PROT_H__

#include <functional>
#include <unordered_map>

enum CMD_CODE_E
{
    CMD_UNVALID = -1,                // 非法命令
    CMD_RESET_CFG = 0x0,             // 恢复出厂设置
    CMD_SET_DEVIP = 0x1,             // 设备IP配置
    CMD_SETUP_SUBDEV = 0x2,          // 子仪器划分配置
    CMD_GET_SUBDEV_INFO = 0x3,       // 子仪器配置查询
    CMD_GET_DEV_INFO = 0x4,          // 设备基本信息查询
    CMD_GET_HW_INFO = 0x5,           // 硬件详细信息查询
    CMD_GET_LIC = 0x6,               // License查询
    CMD_SET_SUB_ETH = 0x7,           // 子网口配置
    CMD_RELOAD_CAL_FILE = 0x8,       // 重新加载校准文件数据
    CMD_SET_EXT_GAIN = 0x9,          // 端口外部增益配置
    CMD_SET_CAL_FILE = 0xA,          // 校准文件写入
    CMD_GET_CAL_FILE = 0xB,          // 校准文件读取
    CMD_ADD_MIMO_DEV = 0xC,          // 增加MIMO仪器
    CMD_DEL_MIMO_DEV = 0xD,          // 删除MIMO仪器
    CMD_SET_MIMO_PARAM = 0xE,        // 多机MIMO参数配置
    CMD_GET_GUI = 0xF,               // GUI文件获取
    CMD_SET_MON_OBJ = 0x10,          // 监视机监视对象配置
    CMD_SET_MON_PARAM = 0x11,        // 监视机需要的数据项配置
    CMD_TX_MON_DATA = 0x12,          // 监视结果发送
    CMD_GET_GUI_VERSION = 0x13,      // 获取指定协议类型的GUI文件的版本号
    CMD_GET_SUB_ETH = 0x14,          // 获取子网口配置信息
    CMD_GET_DEVICE_CUR_TIME = 0x15,  // 获取仪器当前系统时间
    CMD_GET_ALL_GUI_VERSIONS = 0x16, // 获取全部GUI文件的对应版本号信息
    CMD_GET_CUR_DEV_RES_CONF = 0x17, // 获取当前server/子仪器的资源划分情况信息

    CMD_SIG_FILE_TX = 0x18,       // 信号文件下发
    CMD_SIG_FILE_EXIST = 0x19,    // 信号文件是否存在
    CMD_DEL_SIG_FILE = 0x1A,      // 删除信号文件
    CMD_GET_SIG_FILE_LIST = 0x1B, // 获取指定目录信号文件列表
    CMD_GET_SIG_FILE = 0x1C,      // 获取信号文件

    CMD_SET_CAL_PARAM = 0x1D,      // 设置校准参数
    CMD_GET_MONITOR_LIST = 0x1E,   // 获取当前仪器的监听机的列表信息
    CMD_SET_TEMP_CAL = 0x1F,       // 设置温度补偿开关
    CMD_SET_FLATNESS_CAL = 0x20,   // 设置平坦度补偿开关
    CMD_SET_PATH_LOSS_FILE = 0x21, // 写入线衰文件
    CMD_GET_PATH_LOSS_FILE = 0x22, // 读取线衰文件

    CMD_CRYPTOMEMORY_INIT = 0x23, // 初始化加密芯片
    CMD_GET_CAL_PARAM = 0x24,     // 获取校准参数

    CMD_SET_CUSTOMIZE_FILE = 0X25, // 下发文件
    CMD_GET_CUSTOMIZE_FILE = 0X26, // 上传文件
    CMD_SHELL_EXECUTE = 0X27,      // 执行SELL命令，并返回操作结果。
    CMD_GET_DEV_VER_INFO = 0x28,   // 设备相关版本信息查询，算法、校准等

    CMD_GET_MON_RAW_DATA = 0x29,      // 获取vsa原始数据（不含补偿数据），仅监视机下发有效
    CMD_GET_MON_VSA_DATA = 0x30,      // 获取vsa信号数据，仅监视机下发有效
    CMD_GET_MON_VSA_CAL_PARAM = 0x31, // 获取vsa校准补偿数据，仅监视机下发有效

    CMD_GET_DEV_DISK_USAGE = 0x32,   // 查询仪器硬盘使用情况
    CMD_CLEAR_USER_WAVE = 0X33,      // 清除仪器里用户下发的信号文件
    CMD_SET_SUB_NET_AUTO_NEG = 0X34, // 配置仪器子网口自动协商开关
    CMD_GET_SUB_ETH_2 = 0x35,        // 双pcserver获取子网口配置信息(不实现，保留ID号)
    CMD_GET_APP_LIST = 0x36,         // 获取第三方应用列表
    CMD_SET_APP_STATUS = 0x37,       // 启用禁用第三方应用列表
    CMD_SET_APP_DELETE = 0x38,       // 删除第三方应用列表
    CMD_GET_SUB_NET_AUTO_NEG = 0X39, // 获取仪器子网口自动协商开关状态
    CMD_GET_SUB_ETH_LINK = 0x3a,     //获取子网口link信息
    CMD_GET_IP_ADDRESS_TYPE = 0x3b,  //获取主网口ip地址类型

    // 自校
    CMD_START_IN_CAL = 0x40,         // 立即开始自校准，如果正在自校准，直接返回OK
    CMD_STOP_IN_CAL = 0x41,          // 立即停止当前的自校准
    CMD_QUERY_IN_CAL_PROCESS = 0x42, // 查询自校准的进度
    CMD_SET_IN_CAL_CONFIG = 0x43,    // 设置自校准参数配置(MANAGER进程不支持该协议)
    // CMD_GET_IN_CAL_CONFIG = 0x44,        // 获取自校准参数配置(MANAGER进程不支持该协议)

    // 仪器运行模式
    CMD_DEV_RUN_MODE = 0X45,           // 仪器运行模式
    CMD_SET_DEV_LO_MODE = 0X46,        // 本振模式
    CMD_GET_DEV_LO_MODE = 0X47,        // 本振模式
    CMD_SET_DEV_ANALOG_IQ_MODE = 0X48, // 设置模拟IQ信号内/外链路切换开关
    CMD_GET_DEV_ANALOG_IQ_MODE = 0X49, // 设置模拟IQ信号内/外链路切换开关

    CMD_SET_VSA_PARAM = 0X101,               // VSA参数配置
    CMD_SET_VSA_ALZ_PARAM = 0X102,           // VSA分析参数配置
    CMD_VSA_AUTO_RANGE = 0X103,              // VSA autorange
    CMD_START_VSA = 0X104,                   // VSA启动
    CMD_PAUSE_VSA = 0X105,                   // VSA暂停抓取数据
    CMD_STOP_VSA = 0X106,                    // VSA停止
    CMD_GET_VSA_STATUS = 0X107,              // VSA状态查询
    CMD_EXT_VSA_FILE = 0X108,                // 外部VSA信号文件下发
    CMD_VSA_ALZ = 0X10A,                     // 分析VSA信号
    CMD_GET_VSA_DATA = 0X10B,                // 获取VSA结果数据
    CMD_GET_SPEC_VSA_DATA = 0X10C,           // 获取平均中特定某一次的指定项结果数据
    CMD_GET_VSA_PARAM = 0X10D,               // VSA配置查询
    CMD_RECORD_VSA = 0X10E,                  // VSA操作和数据录制
    CMD_GET_VSA_RECORD = 0X10F,              // VSA录制数据获取
    CMD_GET_VSA_RAW_DATA = 0X110,            // 获取VSA采集到的数据及补偿参数
    CMD_SET_AVG_PARAM = 0X111,               // 设置VSA平均参数
    CMD_GET_VSA_ALZ_PARAM = 0X112,           // 获取VSA的当前使用的分析参数
    CMD_GET_VSA_ALZ_RESULT = 0X113,          // 获取当前信号的分析结果的信息,长度和类型，配合10b协议使用
    CMD_SAVE_SIGNAL = 0X117,                 // 保存采集到的信号
    CMD_GET_REF_RANGE = 0X118,               // 获取参考电平范围
    CMD_GET_11AX_USER_VSA_DATA = 0x119,      // ax解析时获取指定用户的分析结果
    CMD_GET_VSA_CAL_PARAM = 0x120,           // 获取校准补偿参数,配合保存vsa信号文件获取iq数据时使用
    CMD_GET_CLR_AVG_DATA = 0x121,            // 清除平均数据，meter vsa stop时使用
    CMD_GET_SPEC_VSA_DATA_COMPOSITE = 0X122, // mimo下，打开平均时，获取多路平均结果的平均
    CMD_GET_VSA_GAIN_PARAM = 0x123,          // 获取校准增益数据
    CMD_SET_FREAM_FILTER = 0x124,            // 设置结果过滤条件
    CMD_SET_IS_WIDE_SPECT_ON = 0x125,        // 设置是否打开频谱显示宽频谱的功能
    CMD_SET_VSA_TRIG_PARAM = 0x128,          // 配置VSA TRIG参数
    CMD_SET_EXTEND_EVM_STATUS = 0x129,        //设置VSA 额外EVM分析参数

    CMD_CALC_IQ_IMB = 0x130,       // 计算IQ不平衡
    CMD_SET_STATIC_IQ_IMB = 0x131, // 设置固定的IQ不平衡参数
    CMD_CLR_STATIC_IQ_IMB = 0x132, // 清除固定的IQ不平衡参数

    CMD_BEAMFORMING_CLR_ALZ_DUTTX = 0x133,    // Beamforming Calibration DUT TX BCM
    CMD_BEAMFORMING_CLR_ALZ_DUTRX = 0x134,    // Beamforming Calibration DUT RX BCM
    CMD_BEAMFORMING_CLR_RST = 0x135,          // Beamforming获取相位结果
    CMD_BEAMFORMING_CLR_VERIFICATION = 0x136, // Beamfoming 获取差异值
    CMD_BEAMFORMING_CAL_PROFILE = 0x137,      // Beamfoming MTK 分析

    CMD_PER_ANALYZE = 0x138,                    // PER 分析
    CMD_SET_EXTRAL_ALZ_PARAM = 0x139,           // 配置扩展的分析参数，配合设置分析参数一起使用
    CMD_SET_ALZ_GROUP_BY_RESULT_STRING = 0x140, // 通过结果字符串来判断并设置分析组类型
    CMD_SET_VSA_FLATNESS_CAL_ENABLE = 0x141,    // 配置vsa 平坦度补偿使能开关
    CMD_GET_VSA_FLATNESS_CAL_ENABLE = 0x142,    // vsa 平坦度补偿使能状态查询
    CMD_SET_VSA_IQIMB_CAL_ENABLE = 0x143,       // 配置vsa IQ不平衡补偿使能开关
    CMD_GET_VSA_IQIMB_CAL_ENABLE = 0x144,       // vsa IQ不平衡补偿使能状态查询
    CMD_GET_VSA_SPECTRUM_POINT_POWER = 0x145,   // 获取频谱指定频点的功率

    CMD_BEAMFORMING_CAL_BCM_AMP_ANGLE = 0x146,  //Beamfoming BCM获取相位幅度等结果内容
    CMD_SET_VSA_ITERATIVE_EVM_STATUS = 0x147,   //设置VSA迭代优化开关
    CMD_SET_VSA_SNC_EVM_STATUS = 0x148,         //设置VSA SNC_EVM噪声补偿开关
    CMD_SET_VSA_CC_EVM_STATUS = 0x149,          //设置VSA CC_EVM开关
    
    CMD_SET_VSG_11AX_FLATNESS_CAL_METHOD = 0x200, // VSG 11AX平坦度补偿模式设置，0默认，1加速
    CMD_SET_VSG_PARAM = 0X201,                    // VSG参数配置
    CMD_TX_VSG_FILE = 0X202,                      // VSG信号文件下发
    CMD_START_VSG = 0X203,                        // VSG启动
    CMD_PAUSE_VSG = 0X204,                        // VSG暂停
    CMD_STOP_VSG = 0X205,                         // VSG停止
    CMD_GET_VSG_STATUS = 0X206,                   // VSG状态查询
    CMD_GET_VSG_PARAM = 0X207,                    // VSG配置查询
    CMD_GEN_VSG_FILE = 0X20C,                     // VSG信号生成,第一版生成信号函数，已停用
    CMD_RECORD_VSG = 0X20D,                       // VSG操作和数据录制
    CMD_GET_VSG_RECORD = 0X20E,                   // VSG录制数据获取
    CMD_SET_PN_PARAM = 0X20F,                     // 多PN配置
    CMD_SET_PN_DATA = 0X210,                      // 多PN数据下发
    CMD_GET_PN = 0X211,                           // 多PN配置查询
    CMD_GET_GEN_PARAM = 0X212,                    // 获取信号生成默认参数
    CMD_GET_POWER_RANGE = 0X213,                  // 获取发送功率范围
    CMD_GET_CUR_AVG_CNT = 0X214,                  // 获取当前已平均的次数
    CMD_GET_VSG_GAIN_PARAM = 0x215,               // 获取校准增益数据
    CMD_SET_VSG_IFG_ENABLE = 0x216,               // IFG使能设置。
    CMD_GET_VSG_IFG_ENABLE = 0x217,               // IFG使能获取。
    CMD_GEN_VSG_FILE_V2 = 0X218,                  // VSG信号生成V2,第二版生成信号函数,已停用
    CMD_GEN_VSG_FILE_CW = 0X219,                  // CW VSG信号生成
    CMD_GEN_VSG_FILE_BLUETOOTH = 0X220,           // Bluetooth VSG信号生成
    CMD_GET_GEN_FINAL_PARAM = 0x221,              // 获取生成信号时，生成完才能确定的参数
    CMD_GEN_VSG_FILE_WIFI = 0x222,                // Vsg Wifi 信号生成
    CMD_SET_VSG_FLATNESS_CAL_ENABLE = 0x223,      // 配置vsg 平坦度补偿使能开关，默认是开,每个连接配置独立，该使能开关关闭，是vsg完全不调用算法的硬件补偿，不管有没有平坦度补偿数据，
                                                  //  0x223与0x20命令不一样，0x20只是不给补偿数据且同时控制vsa和vsg
    CMD_SET_VSG_FEM_MODE = 0x224,                 // 配置vsg FEM功能使能开关
    CMD_GET_PN_DESCRIPTION = 0x225,               // 获取PN信号文件的描述信息
    CMD_VSG_SEND_CNT = 0X226,                     // vsg已发送次数
    CMD_GET_VSG_GEN_WIFI_PARAM = 0x227,           // 获取生成信号后 的生成wifi配置参数，主要为了tftb使用
    CMD_GET_VSG_GEN_RETURN_DATA = 0x228,          // 获取生成信号后，从算法返回的内容，eg:ru carrier;信道配置返回的PathLoss
    CMD_SET_VSG_OFDMA_RU_CARRIER_INFO = 0x229,    // VSG加载文件前，下发ru carrier信息，用于做ofdma功率补偿
    CMD_GET_VSG_FLATNESS_CAL_ENABLE = 0x230,      // vsg 平坦度补偿使能状态查询
    CMD_SET_VSG_IQIMB_CAL_ENABLE = 0x231,         // 配置vsg IQ不平衡补偿使能开关
    CMD_GET_VSG_IQIMB_CAL_ENABLE = 0x232,         // vsg IQ不平衡补偿使能状态查询
    CMD_SET_VSG_STATIC_IQ_IMB_CAL = 0x233,        // 设置固定的IQ不平衡参数
    CMD_CLR_VSG_STATIC_IQ_IMB_CAL = 0x234,        // 清除固定的IQ不平衡参数
    CMD_GEN_VSG_FILE_SLE = 0x235,                 //SLE VSG信号生成
    CMD_GEN_VSG_GEN_SLE_SYNSQR = 0x236,           //获取生成SLE信号后 生成的同步序列
    CMD_GEN_VSG_FILE_WI_SUN = 0x237,              // WiSun  VSG信号文件生成
    CMD_GEN_VSG_FILE_SLB = 0x238,                 // SLB    VSG信号文件生成
    CMD_GEN_VSG_FILE_3GPP =0x240,                 //LTE - 4G VSG信号生成

    CMD_SEND_METER_SETTING = 0X250,             // 下发meter主机界面配置，主要是为发送给监视机使用
    CMD_SEND_USER_CONN_INFO = 0x251,            // api下发，或者主机给从机下发连接发起端的相关ip和port的信息，内部使用，不对客户开放
    CMD_SET_VSG_BROADCAST_ENABLE = 0x299,       // 广播模式
    CMD_GET_VSG_BROADCAST_ENABLE = 0x29A,       // 广播模式
    CMD_SET_VSG_BROADCAST_DEBUG_ENABLE = 0X29B, // 广播debug模式
    CMD_GET_VSG_BROADCAST_RUN_STATUS = 0X29C, // 获取当前广播VSG运行状态


    CMD_GET_TEMP = 0X301,           // 设备温度查询
    CMD_GET_FAN_SPEED = 0X302,      // 风扇转速查询
    CMD_SET_FAN_SPEED = 0X303,      // 风扇转速配置
    CMD_SET_COMPONENT_DATA = 0X304, // 器件参数配置
    CMD_GET_COMPONENT_DATA = 0X305, // 器件参数查询
    CMD_GET_HISTORY_TEMP = 0X307,   // 历史温度信息查询
    CMD_GET_VOLT_INFO = 0X308,      // 仪器电压信息查询
    CMD_SUB_CMD = 0X309,            // CMD

    CMD_GET_LOG = 0X401,                   // 日志查询
    CMD_SELF_DIAG = 0X402,                 // 启动自诊断
    CMD_GET_DIAG_DATA = 0X403,             // 获取自诊断详细数据
    CMD_GET_HARD_ERR_INFO = 0X404,         // 获取硬件错误信息
    CMD_GET_SAVE_LOG_FLAG_SETTING = 0X405, // 获取保存日志的控制标识值
    CMD_SET_SAVE_LOG_FLAG = 0X406,         // 设置保存日志的控制标识
    CMD_CAL_START = 0X407,                 // AUTO CAL
    CMD_CAL_STOP = 0X408,                  // AUTO CAL
    CMD_NOISE_CAL_START = 0X409,           // NOISE_CAL
    CMD_NOISE_CAL_STOP = 0X40A,            // NOISE_CAL
    CMD_NOISE_CAL_STATUS = 0X40B,          // NOISE_CAL
    CMD_NOISE_CAL_VAILD = 0X40C,           // NOISE_CAL

    CMD_UPDATA_FIRM = 0X501,            // 固件升级
    CMD_ROLLBACK_FIRM = 0X502,          // 固件回退
    CMD_UPDATE_LIC = 0X503,             // License升级
    CMD_RESTART_DEV = 0X504,            // 重启设备
    CMD_PRO_LIC_PACKAGE_TRAN = 0X505,   // license package 下发和解析
    CMD_UPDATE_LIC_PACKAGE = 0X506,     // license package升级
    CMD_SHUT_DOWN_DEV = 0X507,          // 关机功能
    CMD_DELETE_ALL_LIC_FILES = 0X508,   // 删除仪器的所有license
    CMD_DELETE_SUB_NET_SETTING = 0X509, // 删除子网口配置

    // TB
    CMD_START_TBT_AP = 0X601,          // TB 开启
    CMD_STOP_TBT_AP = 0X602,           // TB 停止
    CMD_GET_TBT_AP_STATUS = 0X603,     // TB 状态查询
    CMD_VSA_TBT_AP_AUTO_RANGE = 0X604, // TB autorange
    CMD_START_TBT_STA = 0X605,         // TBT_STA 开启
    CMD_STOP_TBT_STA = 0X606,          // TBT_STA 停止
    CMD_GET_TBT_STA_STATUS = 0X607,    // TBT_STA 状态查询

    // DIGITAL IQ
    CMD_DIQ_PARAM = 0X608,    // 数字IQ 目标MAC地址
    CMD_DIQ_DUT_MODE = 0X609, // 数字IQ 治具模式


    //list mod
    CMD_SET_LIST_ENABLE = 0x701,                     //listmod使能
    CMD_SET_LIST_DISABLE = 0x702,                    //listmod去使能
    CMD_SET_LIST_SEG_VSA_ALZ_PARAM = 0x703,          //listmod seg分析参数设置
    CMD_SET_LIST_SEG_VSA_CAP_PARAM = 0x704,          //listmod seg抓取参数设置
    CMD_SET_LIST_SEG_VSA_TIME_PARAM = 0x705,         //listmod seg时间参数设置
    CMD_SET_LIST_SEG_VSA_START_SEQ = 0x706,          //listmod tx seq启动
    CMD_SET_LIST_SEG_VSG_START_SEQ = 0x707,          //listmod rx seq启动
    CMD_SET_LIST_SEG_VSG_PARAM = 0x708,              //listmod seg vsg参数设置
    CMD_SET_LIST_SEG_VSG_WAVE_PARAM = 0x709,         //listmod seg vsg波形参数设置
    CMD_SET_LIST_SEG_VSAVSG_START_SEQ = 0x70a,       //listmod txrx seq启动
    CMD_SET_LIST_SEG_VSA_STOP_SEQ = 0x70b,           //listmod tx seq停止
    CMD_SET_LIST_SEG_VSG_STOP_SEQ = 0x70c,           //listmod rx seq停止
    CMD_SET_LIST_SEG_VSAVSG_STOP_SEQ = 0x70d,        //listmod txrx seq停止
    CMD_GET_LIST_SEQ_VSAVSG_STATE = 0x70e,           //获取listmod txrx seq状态
    CMD_SET_LIST_SEG_VSA_CAP_STATE = 0x70f,          //获取listmod tx seq抓取状态
    CMD_SET_LIST_SEG_VSA_ANNALY_STATE = 0x710,       //获取listmod tx seq分析状态
    CMD_GET_LIST_SEQ_VSG_TRANS_STATE = 0x711,        //获取listmod rx seq发送状态
    CMD_SET_LIST_SEG_VSA_POWER_RESULT = 0x712,       //获取listmod tx seq功率结果

    // 内部接口
    CMD_STOP_SLAVE = 0X1001,                    // 停止从机。仅固件内部用,不对外。
    CMD_GET_SLAVE_240M_THREE_RAW_DATA = 0x1002, // 开启正负500M频谱时，获取从机VSA三次采集到的数据。仅固件内部用,不对外。
    CMD_SEND_FILE_IN_MIMO = 0x1003,             // mimo 时，vsg选择主机有而从机没有的文件时，配置pn时给从机下发主机内的信号文件，然后再给从机配置pn,仅固件内部使用,不对外
    CMD_GET_SLAVE_LIC_INFO = 0x1004,            // 主机获取从机license信息，主要用于mimo时判断从机的业务的license，仅固件内部用，不对外
    CMD_TEST_CONNECT_STATUS = 0X1005            // API查询连接状态,不ACK
};

enum CMD_STATUS_E
{
    CMD_STATUS_OK,    // 查找到完整命令
    CMD_STATUS_UNCMP, // 已查找到命令，但命令数据不完整; 或者数据太少无法判断是否是命令
    CMD_STATUS_NO_CMD // 没有查找到命令
};

#define TYPE_CMD 0x434D44 // 命令类型为下发命令
#define TYPE_ACK 0x41434B // 命令类型为命令响应

#define MAX_NAME_SIZE 256      // 带有文件名的命令中文件名的最大长度
#define VSA_RESULT_NAME_LEN 64 // VSA结果标识名字长度

// 请求头结构体
struct ReqHeader
{
    int Type;               // 命令类型 CMD或者ACK
    unsigned int SerialNum; // 命令序列号
    int Code = CMD_UNVALID; // 功能码
};

// 命令包括命令头和命令内容，从数据长度字段之后的部分为命令内容，之前的为命令头
// 协议命令结构，不包含命令数据
struct CmdHeader : public ReqHeader
{
    int Length; // 命令内容长度

    CmdHeader() { Type = TYPE_CMD; }
    CmdHeader(const ReqHeader *Cmd, int Len)
        : ReqHeader(*Cmd), Length(Len)
    {
        Type = TYPE_CMD;
    }
};

// 命令响应结构体
struct AckHeader : public CmdHeader
{
    int Result; // 结果

    AckHeader() {}
    AckHeader(const ReqHeader *Cmd, int Len, int Result)
        : CmdHeader(Cmd, Len), Result(Result)
    {
        Type = TYPE_ACK;
    }
};

class WTProtocol
{
    using ProtFunctor = std::function<int(CmdHeader *, void *, void *)>;

public:
    //*****************************************************************************
    // 注册对外通信协议功能回调函数
    // 参数[IN]: Code : 功能码
    //           Functor : 功能码对应的处理函数，回调函数的参数：命令头，命令内容
    // 返回值: 成功或者错误码
    //*****************************************************************************
    void RegCmd(int Code, const ProtFunctor &Functor);
    void RegCmd(int Code, ProtFunctor &&Functor);

    //*****************************************************************************
    // 查找数据中是否有完整的命令
    // 参数[IN]:  Data : 待查找的数据
    //          Length : 数据长度
    // 返回值: 检查结果，见CMD_STATUS_E
    //*****************************************************************************
    int CheckCmd(const void *Data, int Length);

    //*****************************************************************************
    // 执行命令
    // 参数[IN]:  Data : 命令数据
    //          Length : 数据长度
    //             Arg : 传递给命令处理函数的参数
    // 返回值: 成功或错误码
    //*****************************************************************************
    int ExecCmd(void *Data, int Length, void *Arg);

    //*****************************************************************************
    // 获取命令长度，调用者需要保证数据长度不小于命令头的长度
    // 参数[IN]:  Data : 命令数据
    // 返回值: 命令总长度(包括命令内容和命令头)
    //*****************************************************************************
    int GetCmdLen(void *Data);

    // 析构函数声明为虚函数
    virtual ~WTProtocol(){};

private:
    std::unordered_map<int, ProtFunctor> m_CmdHandler; // 功能码和回调函数关联表
};

#endif // !__WT_PRTC_H__
