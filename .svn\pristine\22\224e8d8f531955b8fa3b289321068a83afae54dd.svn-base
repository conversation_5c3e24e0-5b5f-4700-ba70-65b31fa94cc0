#include "vsa_sample.h"
#include "../../../extlib/include/alg/alg_3gpp_apidef.h"
#include "../include/scpi/types.h"
#include "../../general/wtlog.h"

int VsaSampler::updateVsaSamplingTime(SPCIUserParam *attr) 
{
    int iRet = WT_ERR_CODE_OK;
    if (!attr->vsaParam.VsaAutoSamplingTimeMode) 
    {
        return iRet;
    }

    switch (attr->vsaAlzParam.analyzeParam3GPP.Standard)
    {
    case ALG_3GPP_STD_GSM:
        iRet = updateGsmSamplingTime(attr);
        break;
    case ALG_3GPP_STD_WCDMA:
        iRet = updateWcdmaSamplingTime(attr);
        break;
    case ALG_3GPP_STD_4G:
        iRet = updateLteSamplingTime(attr);
        break;
    case ALG_3GPP_STD_5G:
        iRet = updateNR5GSamplingTime(attr);
        break;
    case ALG_3GPP_STD_NB_IOT:
        iRet = updateNbIotSamplingTime(attr);
        break;
    
    default:
        break;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "updateVsaSamplingTime: %d ms\n", static_cast<int>(1000 * attr->vsaParam.SmpTime));

    return iRet;
}

void VsaSampler::updateVsaSamplingFrequency(SPCIUserParam *attr)
{
    if (!attr->vsaParam.VsaAutoSamplingRateMode) 
    {
        return;
    }

    switch (attr->vsaAlzParam.analyzeParam3GPP.Standard)
    {
    case ALG_3GPP_STD_GSM:
        updateGsmSamplingFrequency(attr);
        break;
    case ALG_3GPP_STD_WCDMA:
        updateWcdmaSamplingFrequency(attr);
        break;
    case ALG_3GPP_STD_4G:
        updateLteSamplingFrequency(attr);
        break;
    case ALG_3GPP_STD_5G:
        updateNR5GSamplingFrequency(attr);
        break;
    case ALG_3GPP_STD_NB_IOT:
        updateNbIotSamplingFrequency(attr);
        break;
    
    default:
        break;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "updateVsaSamplingFrequency: %0.2f MHz\n", attr->vsaParam.SamplingFreq / MHz);
}

void VsaSampler::updateVsaSampling(SPCIUserParam *attr)
{
    updateVsaSamplingTime(attr);
    updateVsaSamplingFrequency(attr);
}

int VsaSampler::updateGsmSamplingTime(SPCIUserParam *attr) 
{
    attr->vsaParam.SmpTime = 10 * Ms;

    return SCPI_RES_OK;
}

int VsaSampler::updateWcdmaSamplingTime(SPCIUserParam *attr) 
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "Wcdma.LinkDirect: %d\n", attr->vsaAlzParam.analyzeParam3GPP.WCDMA.LinkDirect);
    if (attr->vsaAlzParam.analyzeParam3GPP.WCDMA.LinkDirect == 0) // UL
    {
        int iMeasureLen = attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.MeasureLen;
        if (iMeasureLen == 1) 
        {
            attr->vsaParam.SmpTime = 2 * Ms;
        }
        else if (iMeasureLen > 1) 
        {
            attr->vsaParam.SmpTime = std::ceil((iMeasureLen + 2) * 0.66) * Ms;
        }
    } 
    else if (attr->vsaAlzParam.analyzeParam3GPP.WCDMA.LinkDirect == 1) // DL
    {
        attr->vsaParam.SmpTime = 20 * Ms;
    }

    return SCPI_RES_OK;
}

int VsaSampler::updateLteSamplingTime(SPCIUserParam *attr) 
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "LTE.ChanType: %d\n", attr->vsaAlzParam.analyzeParam3GPP.LTE.ChanType);
    if (attr->vsaAlzParam.analyzeParam3GPP.LTE.ChanType == 0) // UL
    {
        if (attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.Power.PwrDynEnable == 0) 
        {
            attr->vsaParam.SmpTime = (2 + attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasSubfrmCount) * Ms;
        }
        else 
        {
            attr->vsaParam.SmpTime = (3 + attr->vsaAlzParam.analyzeParam3GPP.LTE.MeasInfo.MeasSubfrmCount) * Ms;
        }
    } 
    else if (attr->vsaAlzParam.analyzeParam3GPP.LTE.ChanType == 2) // DL
    {
        attr->vsaParam.SmpTime = 12 * Ms;
    }

    return SCPI_RES_OK;
}

int VsaSampler::updateNR5GSamplingTime(SPCIUserParam *attr) 
{
    int linkDirect = attr->vsaAlzParam.analyzeParam3GPP.NR.LinkDirect;
    WTLog::Instance().WriteLog(LOG_DEBUG, "NR.LinkDirect: %d\n", linkDirect);
    
    if (linkDirect == ALG_3GPP_UL) // UL
    {
        // 1. 计算slotLength和slotNumInSF
        int slotLength = 1000;
        int slotNumInSF = 1;
        int scSpacing = attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[0].Bwp.SCSpacing;
        
        if (scSpacing == 15 * KHz) {
            slotLength = 1000 * Us;
            slotNumInSF = 1;
        } else if (scSpacing == 30 * KHz) {
            slotLength = 500 * Us;
            slotNumInSF = 2;
        } else if (scSpacing == 60 * KHz) {
            slotLength = 250 * Us;
            slotNumInSF = 4;
        }

        // 2. 计算sampleSlotNum
        int modStatNum = attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.ModStatNum;
        int semStatNum = attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.SEMStatNum;
        int aclrStatNum = attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.ACLRStatNum;
        int pwrDynStatNum = attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.PwrDynStatNum;
        int txPwrStatNum = attr->vsaAlzParam.analyzeParam3GPP.NR.Measure.TxPwrStatNum;

        int sampleSlotNum = std::max({modStatNum, semStatNum, aclrStatNum, txPwrStatNum, pwrDynStatNum * slotNumInSF});

        // 3. 计算采样长度
        int calSampleSlotLen = std::ceil((static_cast<double>(sampleSlotNum / slotNumInSF))  * slotNumInSF *  slotLength);

        // 4. 计算采样时间
        if (attr->vsaAlzParam.analyzeParam3GPP.NR.ViewSet.PwrDynEnable) {
            attr->vsaParam.SmpTime = std::max(static_cast<double>(calSampleSlotLen), 4 * Ms);
        } else {
            attr->vsaParam.SmpTime = std::max(static_cast<double>(calSampleSlotLen), 3 * Ms);
        }
    } 
    else if (linkDirect == ALG_3GPP_DL) // DL
    {
        attr->vsaParam.SmpTime = 15 * Ms;
    }

    return SCPI_RES_OK;
}

int VsaSampler::updateNbIotSamplingTime(SPCIUserParam *attr) 
{
    if (attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect == ALG_3GPP_UL) // UL
    {
        LookupParam param;
        param.format = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.Format;
        param.SCS = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.SCSpacing;
        param.SCNum = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.SubcarrierNum;
        param.RUNum = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.UL.Npusch.RUs;
        param.analyzeMode = attr->vsaAlzParam.analyzeParam3GPP.NBIOT.Measure.MeasureUnit;

        LookupTable table;
        initializeLookupTable(table, param.RUNum);
        double value = lookupValue(table, param);
        if (value == -1) 
        {
            std::cerr << "Error: Lookup value not found" << std::endl;
            return SCPI_RES_ERR;
        }
        attr->vsaParam.SmpTime = value;
    } 
    else if (attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect == ALG_3GPP_DL) // DL
    {
        attr->vsaParam.SmpTime = 40 * Ms;
    }

    return WT_ERR_CODE_OK;
}

void VsaSampler::updateGsmSamplingFrequency(SPCIUserParam *attr)
{
    attr->vsaParam.SamplingFreq = 13 * MHz;
}

void VsaSampler::updateWcdmaSamplingFrequency(SPCIUserParam *attr)
{
    int linkDirect = attr->vsaAlzParam.analyzeParam3GPP.WCDMA.LinkDirect;
    WTLog::Instance().WriteLog(LOG_DEBUG, "Wcdma.LinkDirect: %d\n", linkDirect);
    if (linkDirect == 0) // UL
    {
        attr->vsaParam.SamplingFreq = 122.88 * MHz;
    } 
    else if (linkDirect == 1) // DL
    {
        attr->vsaParam.SamplingFreq = 30.72 * MHz;
    }
}

void VsaSampler::updateLteSamplingFrequency(SPCIUserParam *attr)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "ChannelBW: %0.1f MHz\n", static_cast<double>(attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[0].ChannelBW) / MHz);
    switch (attr->vsaAlzParam.analyzeParam3GPP.LTE.Cell[0].ChannelBW)
    {
    case 1400000:
    case 3 * MHz:
    case 5 * MHz:
        attr->vsaParam.SamplingFreq = 30.72 * MHz;
        break;
    case 10 * MHz:
        attr->vsaParam.SamplingFreq = 61.44 * MHz;
        break;
    case 15 * MHz:
    case 20 * MHz:
        attr->vsaParam.SamplingFreq = 122.88 * MHz;
        break;
    default:
        break;
    }
}

void VsaSampler::updateNR5GSamplingFrequency(SPCIUserParam *attr)
{
    int linkDirect = attr->vsaAlzParam.analyzeParam3GPP.NR.LinkDirect;
    WTLog::Instance().WriteLog(LOG_DEBUG, "NR.LinkDirect: %d\n", linkDirect);
    if (linkDirect == ALG_3GPP_UL) // UL
    {
        switch (attr->vsaAlzParam.analyzeParam3GPP.NR.UL.Cell[0].ChannelBW)
        {
        case 5 * MHz:
        case 10 * MHz:
            attr->vsaParam.SamplingFreq = 61.44 * MHz;
            break;
        case 15 * MHz:
        case 20 * MHz:
        case 25 * MHz:
        case 30 * MHz:
            attr->vsaParam.SamplingFreq = 122.88 * MHz;
            break;
        case 35 * MHz:
        case 40 * MHz:
        case 45 * MHz:
        case 50 * MHz:
        case 60 * MHz:
            attr->vsaParam.SamplingFreq = 245.76 * MHz;
            break;
        case 70 * MHz:
        case 80 * MHz:
        case 90 * MHz:
        case 100 * MHz:
            attr->vsaParam.SamplingFreq = 491.52 * MHz;
            break;
        default:
            attr->vsaParam.SamplingFreq = 122.88 * MHz;
            break;
        }
    } 
    else if (linkDirect == ALG_3GPP_DL) // DL
    {
        attr->vsaParam.SamplingFreq = 122.88 * MHz;
    }
}

void VsaSampler::updateNbIotSamplingFrequency(SPCIUserParam *attr)
{
    if (attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect == ALG_3GPP_UL) // UL
    {
        attr->vsaParam.SamplingFreq = 15.36 * MHz;
        // LTENB分支：attr->vsaParam.SamplingFreq = 30.72 * MHz;
    }
    else if (attr->vsaAlzParam.analyzeParam3GPP.NBIOT.LinkDirect == ALG_3GPP_DL) // DL
    {
        attr->vsaParam.SamplingFreq = 30.72 * MHz;
    }
}

void VsaSampler::initializeLookupTable(LookupTable& table, int RUNum) 
{
    // format, SCS, SCNum, RUNum, analyzeMode = sampling time
    // RU
    table[std::make_tuple(1, 3.75 * KHz, 1,  1,     0)]  = 35 * Ms;
    table[std::make_tuple(1, 15   * KHz, 1,  RUNum, 0)]  = (9 * RUNum) * Ms;
    table[std::make_tuple(1, 15   * KHz, 3,  RUNum, 0)]  = (5 * RUNum) * Ms;
    table[std::make_tuple(1, 15   * KHz, 6,  RUNum, 0)]  = (3 * RUNum) * Ms;
    table[std::make_tuple(1, 15   * KHz, 12, RUNum, 0)]  = (2 * RUNum) * Ms;
    table[std::make_tuple(2, 3.75 * KHz, 1,  1,     0)]  = 11 * Ms;
    table[std::make_tuple(2, 15   * KHz, 1,  1,     0)]  = 3 * Ms;

    // Slot
    table[std::make_tuple(1, 3.75 * KHz, 1,  1,     1)]  = 20 * Ms;
    table[std::make_tuple(1, 15   * KHz, 1,  RUNum, 1)]  = 9 * Ms;
    table[std::make_tuple(1, 15   * KHz, 3,  RUNum, 1)]  = 5 * Ms;
    table[std::make_tuple(1, 15   * KHz, 6,  RUNum, 1)]  = 3 * Ms;
    table[std::make_tuple(1, 15   * KHz, 12, RUNum, 1)]  = 2 * Ms;
    table[std::make_tuple(2, 3.75 * KHz, 1,  1,     1)]  = 11 * Ms;
    table[std::make_tuple(2, 15   * KHz, 1,  1,     1)]  = 3 * Ms;
}

double VsaSampler::lookupValue(const LookupTable& table, const LookupParam& param) 
{
    Key key = std::make_tuple(param.format, param.SCS, param.SCNum, param.RUNum, param.analyzeMode);
    auto it = table.find(key);
    if (it != table.end()) 
    {
        return it->second;
    } 
    else 
    {
        std::cerr << "Key not found in lookup table!" << std::endl;
        return -1; // 可以选择其他适当的错误返回值或处理方式
    }
}
