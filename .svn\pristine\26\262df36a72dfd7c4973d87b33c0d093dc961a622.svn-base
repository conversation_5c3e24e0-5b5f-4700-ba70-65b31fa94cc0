#include "cellular_param_from_json.h"
#include "scpi_3gpp_common.h"
#include "wterror.h"
#include "wtlog.h"

using namespace cellular::method;
using json = nlohmann::json;

namespace {

int set_gsm_param_from_json(Alg_3GPP_AlzInGSM &GSM, const json &json_gsm);
int set_wcdma_param_from_json(Alg_3GPP_AlzInWCDMA &WCDMA, const json &json_wcdma);
int set_lte_param_from_json(Alg_3GPP_AlzIn4g &LTE, const json &json_lte);
int set_nr_param_from_json(Alg_3GPP_AlzIn5g &NR, const json &json_nr);
int set_nbiot_param_from_json(Alg_3GPP_AlzInNBIOT &NBIOT, const json &json_nbiot);

// WCDMA
void set_wcdma_ul_param_from_json(Alg_3GPP_AlzULInWCDMA &UL, const json &json_wcdma_ul);
void set_wcdma_dl_param_from_json(Alg_3GPP_AlzDLInWCDMA &DL, const json &json_wcdma_dl);
void set_wcdma_measure_param_from_json(Alg_3GPP_AlzMeasureWCDMA &Measure, const json &json_wcdma_measure);
// LTE
void set_lte_ul_param_from_json(Alg_3GPP_AlzPusch4g &Pusch, const json &json_lte_ul);
void set_lte_dl_param_from_json(Alg_3GPP_AlzPdsch4g &Pdsch, const json &json_lte_dl);
void set_lte_measure_param_from_json(Alg_3GPP_MeasureIn4g &Measure, const json &json_lte_measure);
void set_lte_limit_param_from_json(Alg_3GPP_LimitIn4g &Limit, const json &json_lte_limit);
// NR
void set_nr_ul_param_from_json(Alg_3GPP_AlzULIn5g &UL, const json &json_nr_ul);
void set_nr_dl_param_from_json(Alg_3GPP_AlzDLIn5g &DL, const json &json_nr_dl);
void set_nr_measure_param_from_json(Alg_3GPP_AlzMeasure5g &Measure, const json &json_meas);
void set_nr_limit_param_from_json(Alg_3GPP_LimitIn5g &Limit, const json &JsonLimit);
// NB-IOT
void set_nbiot_ul_param_from_json(Alg_3GPP_AlzULInNBIOT &UL, const json &json_nbiot_ul);
void set_nbiot_dl_param_from_json(Alg_3GPP_AlzDLInNBIOT &DL, const json &json_nbiot_dl);
void set_nbiot_measure_param_from_json(Alg_3GPP_AlzMeasureNBIOT &Measure, const json &json_nbiot_measure);
void set_nbiot_limit_param_from_json(Alg_3GPP_LimitInNBIOT &Limit, const json &JsonLimit);

} // namespace

int set_cellular_param_from_json(AlzParam3GPP &Param, const json &pRoot)
{
    int ret = WT_ERR_CODE_OK;

    do
    {
        if (Param.Standard != pRoot.at("Standard").get<int>())
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "%d %d\n", Param.Standard, pRoot.at("Standard").get<int>());
            ret = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (pRoot.at("ErrorCode").get<int>() != WT_ERR_CODE_OK)
        {
            ret = pRoot.at("ErrorCode").get<int>() + WT_ALG_3GPP_BASE_ERROR;
            break;
        }

        const char *Key = "LTE";
        switch (Param.Standard)
        {
        case ALG_3GPP_STD_GSM:
            Key = "GSM";
            break;
        case ALG_3GPP_STD_4G:
            break;
        case ALG_3GPP_STD_WCDMA:
            Key = "WCDMA";
            break;
        case ALG_3GPP_STD_5G:
            Key = "NR";
            break;
        case ALG_3GPP_STD_NB_IOT:
            Key = "NBIOT";
            break;

        default:
            WTLog::Instance().WriteLog(LOG_DEBUG, "123123\n");
            ret = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        if (ret != WT_ERR_CODE_OK)
        {
            break;
        }

        if (pRoot.contains(Key) && pRoot.at(Key).is_null())
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "%s %d %d\n", Key, pRoot.contains(Key), pRoot.at(Key).is_null());
            ret = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }

        switch (Param.Standard)
        {
        case ALG_3GPP_STD_GSM:
            ret = set_gsm_param_from_json(Param.GSM, pRoot.at("GSM"));
            break;
        case ALG_3GPP_STD_WCDMA:
            ret = set_wcdma_param_from_json(Param.WCDMA, pRoot.at("WCDMA"));
            break;

        case ALG_3GPP_STD_4G:
            ret = set_lte_param_from_json(Param.LTE, pRoot.at("LTE"));
            break;

        case ALG_3GPP_STD_5G:
            ret = set_nr_param_from_json(Param.NR, pRoot.at("NR"));
            break;

        case ALG_3GPP_STD_NB_IOT:
            ret = set_nbiot_param_from_json(Param.NBIOT, pRoot.at("NBIOT"));
            break;
        default:
            printf("Set_3GPP_ALZ_By_Json demode error, cur demode: %d\n", Param.Standard);
            ret = WT_ERR_CODE_GENERAL_ERROR;
            break;  
        }

    } while (0);

    if (ret)
    {
        return ret;
    }

    // Param.DcFreqCompensate = pRoot.at("DcFreqCompensate");
    // Param.SpectrumRBW = pRoot.at("SpectrumRBW");

    return ret;
}

namespace {

int set_gsm_param_from_json(Alg_3GPP_AlzInGSM &GSM, const json &json_gsm)
{
    int ret = WT_OK;

    GSM.SlotOffset = json_gsm.at("SlotOffset");
    GSM.NumbOfSlot = json_gsm.at("NumbOfSlot");
    GSM.MeasureSlot = json_gsm.at("MeasureSlot");
    GSM.PvTFilter = json_gsm.at("PvTFilter");

    if (json_gsm.contains("SpectMod"))
    {
        if (json_gsm["SpectMod"].at("OffsetState").is_array())
        {
            for (int i = 0; i < arraySize(GSM.SpectMod.OffsetState); i++)
            {
                GSM.SpectMod.OffsetState[i] = json_gsm["SpectMod"]["OffsetState"].at(i);
            }
        }

        if (json_gsm["SpectMod"].at("FreqOffset").is_array())
        {
            for (int i = 0; i < arraySize(GSM.SpectMod.FreqOffset); i++)
            {
                GSM.SpectMod.FreqOffset[i] = json_gsm["SpectMod"]["FreqOffset"].at(i);
            }
        }
    }

    if (json_gsm.contains("SpectSwt"))
    {
        if (json_gsm["SpectSwt"].at("OffsetState").is_array())
        {
            for (int i = 0; i < arraySize(GSM.SpectSwt.OffsetState); i++)
            {
                GSM.SpectSwt.OffsetState[i] = json_gsm["SpectSwt"]["OffsetState"].at(i);
            }
        }
            
        if (json_gsm["SpectSwt"].at("FreqOffset").is_array())
        {
            for (int i = 0; i < arraySize(GSM.SpectSwt.FreqOffset); i++)
            {
                GSM.SpectSwt.FreqOffset[i] = json_gsm["SpectSwt"]["FreqOffset"].at(i);
            }
        }
    }

    // limit info
    if (json_gsm["LimitInfo"].at("ModLimit").is_array())
    {
        for (int i = 0; i < arraySize(GSM.LimitInfo.ModLimit); i++)
        {
            // EvmRms
            GSM.LimitInfo.ModLimit[i].EvmRms.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmRms").at("Current");
            GSM.LimitInfo.ModLimit[i].EvmRms.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmRms").at("Average");
            GSM.LimitInfo.ModLimit[i].EvmRms.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmRms").at("Max");
            GSM.LimitInfo.ModLimit[i].EvmRms.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmRms").at("LimitValue");
            
            // EvmPeak
            GSM.LimitInfo.ModLimit[i].EvmPeak.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmPeak").at("Current");
            GSM.LimitInfo.ModLimit[i].EvmPeak.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmPeak").at("Average");
            GSM.LimitInfo.ModLimit[i].EvmPeak.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmPeak").at("Max");
            GSM.LimitInfo.ModLimit[i].EvmPeak.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("EvmPeak").at("LimitValue");

            // Evm95Percent
            GSM.LimitInfo.ModLimit[i].Evm95Percent.State = json_gsm["LimitInfo"]["ModLimit"][i].at("Evm95Percent").at("State");
            GSM.LimitInfo.ModLimit[i].Evm95Percent.Limit = json_gsm["LimitInfo"]["ModLimit"][i].at("Evm95Percent").at("Limit");

            // MErrRms
            GSM.LimitInfo.ModLimit[i].MErrRms.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrRms").at("Current");
            GSM.LimitInfo.ModLimit[i].MErrRms.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrRms").at("Average");
            GSM.LimitInfo.ModLimit[i].MErrRms.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrRms").at("Max");
            GSM.LimitInfo.ModLimit[i].MErrRms.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrRms").at("LimitValue");

            // MErrPeak
            GSM.LimitInfo.ModLimit[i].MErrPeak.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrPeak").at("Current");
            GSM.LimitInfo.ModLimit[i].MErrPeak.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrPeak").at("Average");
            GSM.LimitInfo.ModLimit[i].MErrPeak.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("MErrPeak").at("Max");
            GSM.LimitInfo.ModLimit[i].MErrPeak.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i]["MErrPeak"].at("LimitValue");

            // MErr95Percent
            GSM.LimitInfo.ModLimit[i].MErr95Percent.State = json_gsm["LimitInfo"]["ModLimit"][i].at("MErr95Percent").at("State");
            GSM.LimitInfo.ModLimit[i].MErr95Percent.Limit = json_gsm["LimitInfo"]["ModLimit"][i].at("MErr95Percent").at("Limit");

            // PhErrRms
            GSM.LimitInfo.ModLimit[i].PhErrRms.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("PhErrRms").at("Current");
            GSM.LimitInfo.ModLimit[i].PhErrRms.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("PhErrRms").at("Average");
            GSM.LimitInfo.ModLimit[i].PhErrRms.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("PhErrRms").at("Max");
            GSM.LimitInfo.ModLimit[i].PhErrRms.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("PhErrRms").at("LimitValue");

            // PhErrPeak
            GSM.LimitInfo.ModLimit[i].PhErrPeak.Current = json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"].at("Current");
            GSM.LimitInfo.ModLimit[i].PhErrPeak.Average = json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"].at("Average");
            GSM.LimitInfo.ModLimit[i].PhErrPeak.Max = json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"].at("Max");
            GSM.LimitInfo.ModLimit[i].PhErrPeak.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"].at("LimitValue");

            // PhErr95Percent
            GSM.LimitInfo.ModLimit[i].PhErr95Percent.State = json_gsm["LimitInfo"]["ModLimit"][i].at("PhErr95Percent").at("State");
            GSM.LimitInfo.ModLimit[i].PhErr95Percent.Limit = json_gsm["LimitInfo"]["ModLimit"][i].at("PhErr95Percent").at("Limit");

            // IQOffset
            GSM.LimitInfo.ModLimit[i].IQOffset.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("IQOffset").at("Current");
            GSM.LimitInfo.ModLimit[i].IQOffset.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("IQOffset").at("Average");
            GSM.LimitInfo.ModLimit[i].IQOffset.Max = json_gsm["LimitInfo"]["ModLimit"][i]["IQOffset"].at("Max");
            GSM.LimitInfo.ModLimit[i].IQOffset.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i]["IQOffset"].at("LimitValue");

            // IQImbalance
            GSM.LimitInfo.ModLimit[i].IQImbalance.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("IQImbalance").at("Current");
            GSM.LimitInfo.ModLimit[i].IQImbalance.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("IQImbalance").at("Average");
            GSM.LimitInfo.ModLimit[i].IQImbalance.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("IQImbalance").at("Max");
            GSM.LimitInfo.ModLimit[i].IQImbalance.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("IQImbalance").at("LimitValue");

            // FreError
            GSM.LimitInfo.ModLimit[i].FreError.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("FreError").at("Current");
            GSM.LimitInfo.ModLimit[i].FreError.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("FreError").at("Average");
            GSM.LimitInfo.ModLimit[i].FreError.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("FreError").at("Max");
            GSM.LimitInfo.ModLimit[i].FreError.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("FreError").at("LimitValue");

            // TimeError
            GSM.LimitInfo.ModLimit[i].TimeError.Current = json_gsm["LimitInfo"]["ModLimit"][i].at("TimeError").at("Current");
            GSM.LimitInfo.ModLimit[i].TimeError.Average = json_gsm["LimitInfo"]["ModLimit"][i].at("TimeError").at("Average");
            GSM.LimitInfo.ModLimit[i].TimeError.Max = json_gsm["LimitInfo"]["ModLimit"][i].at("TimeError").at("Max");
            GSM.LimitInfo.ModLimit[i].TimeError.LimitValue = json_gsm["LimitInfo"]["ModLimit"][i].at("TimeError").at("LimitValue");
        }
    }

    return ret;
}

int set_wcdma_param_from_json(Alg_3GPP_AlzInWCDMA &WCDMA, const json &json_wcdma)
{
    if (WCDMA.LinkDirect != json_wcdma.at("LinkDirect"))
    {
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    if (WCDMA.LinkDirect == ALG_3GPP_UL)
    {
        set_wcdma_ul_param_from_json(WCDMA.UL, json_wcdma.at("UL"));
    }
    else if (WCDMA.LinkDirect == ALG_3GPP_DL)
    {
        set_wcdma_dl_param_from_json(WCDMA.DL, json_wcdma.at("DL"));
    }

    set_wcdma_measure_param_from_json(WCDMA.Measure, json_wcdma.at("Measure"));

    return WT_OK;
}

void set_wcdma_ul_param_from_json(Alg_3GPP_AlzULInWCDMA &UL, const json &json_wcdma_ul)
{
    UL.ScramblingCode = json_wcdma_ul.at("ScramblingCode");
    UL.DPCCHSlotFormat = json_wcdma_ul.at("DPCCHSlotFormat");
    UL.ChannelType = json_wcdma_ul.at("ChannelType");
    UL.DPDCHAvailable = json_wcdma_ul.at("DPDCHAvailable");
    UL.MeasureLen = json_wcdma_ul.at("MeasureLen");
    UL.SlotNum = json_wcdma_ul.at("SlotNum");
    UL.CDPSpreadFactor = json_wcdma_ul.at("CDPSpreadFactor");
}

void set_wcdma_dl_param_from_json(Alg_3GPP_AlzDLInWCDMA &DL, const json &json_wcdma_dl)
{
    DL.ScramblingCode = json_wcdma_dl.at("ScramblingCode");
    DL.DPCHNum = json_wcdma_dl.at("DPCHNum");

    if (json_wcdma_dl.at("DPCH").is_array())
    {
        for (int i = 0; i < arraySize(DL.DPCH); i++)
        {
            DL.DPCH[i].State = json_wcdma_dl["DPCH"][i].at("State");
            DL.DPCH[i].SlotFormat = json_wcdma_dl["DPCH"][i].at("SlotFormat");
            DL.DPCH[i].SymbRate = json_wcdma_dl["DPCH"][i].at("SymbRate"); 
            DL.DPCH[i].ChanCode = json_wcdma_dl["DPCH"][i].at("ChanCode");
            DL.DPCH[i].TimingOffset = json_wcdma_dl["DPCH"][i].at("TimingOffset");
            DL.DPCH[i].TpcDataType = json_wcdma_dl["DPCH"][i].at("TpcDataType");
            DL.DPCH[i].DataType = json_wcdma_dl["DPCH"][i].at("DataType");
            DL.DPCH[i].Initialization = json_wcdma_dl["DPCH"][i].at("Initialization");
            DL.DPCH[i].Power = json_wcdma_dl["DPCH"][i].at("Power");
            if (json_wcdma_dl["DPCH"][i].contains("DCH"))
            {
                DL.DPCH[i].DCH.State = json_wcdma_dl["DPCH"][i]["DCH"].at("State");
                DL.DPCH[i].DCH.Interleaver2Stat = json_wcdma_dl["DPCH"][i]["DCH"].at("Interleaver2Stat");
                
                if (json_wcdma_dl["DPCH"][i]["DCH"].contains("DCCH"))
                {
                    DL.DPCH[i].DCH.DCCH.State = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("State");
                    DL.DPCH[i].DCH.DCCH.DataType = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("DataType");
                    DL.DPCH[i].DCH.DCCH.Initialization = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("Initialization");
                    DL.DPCH[i].DCH.DCCH.TTI = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("TTI");
                    DL.DPCH[i].DCH.DCCH.TbCount = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("TbCount");
                    DL.DPCH[i].DCH.DCCH.TbSize = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("TbSize");
                    DL.DPCH[i].DCH.DCCH.Crc = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("Crc");
                    DL.DPCH[i].DCH.DCCH.RmAttribute = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("RmAttribute");
                    DL.DPCH[i].DCH.DCCH.EProtection = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("EProtection");
                    DL.DPCH[i].DCH.DCCH.InterleaverStat = json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"].at("InterleaverStat");
                }
                
                if (json_wcdma_dl["DPCH"][i]["DCH"].at("DTCH").is_array())
                {
                    for (int j = 0; j < 6 && j < json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"].size(); ++j)
                    {
                        DL.DPCH[i].DCH.DTCH[j].State = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("State");
                        DL.DPCH[i].DCH.DTCH[j].DataType = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("DataType");
                        DL.DPCH[i].DCH.DTCH[j].Initialization = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("Initialization");
                        DL.DPCH[i].DCH.DTCH[j].TTI = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("TTI");
                        DL.DPCH[i].DCH.DTCH[j].TbCount = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("TbCount");
                        DL.DPCH[i].DCH.DTCH[j].TbSize = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("TbSize");
                        DL.DPCH[i].DCH.DTCH[j].Crc = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("Crc");
                        DL.DPCH[i].DCH.DTCH[j].RmAttribute = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("RmAttribute");
                        DL.DPCH[i].DCH.DTCH[j].EProtection = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("EProtection");
                        DL.DPCH[i].DCH.DTCH[j].InterleaverStat = json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j].at("InterleaverStat");
                    }
                }
            }
        }
    }

    if (json_wcdma_dl.contains("PSCH"))
    {
        DL.PSCH.State = json_wcdma_dl["PSCH"].at("State");
        DL.PSCH.SymbRate = json_wcdma_dl["PSCH"].at("SymbRate");
        DL.PSCH.Power = json_wcdma_dl["PSCH"].at("Power");
    }

    if (json_wcdma_dl.contains("SSCH"))
    {
        DL.SSCH.State = json_wcdma_dl["SSCH"].at("State");
        DL.SSCH.SymbRate = json_wcdma_dl["SSCH"].at("SymbRate");
        DL.SSCH.Power = json_wcdma_dl["SSCH"].at("Power");
    }

    if (json_wcdma_dl.contains("PCPICH"))
    {
        DL.PCPICH.State = json_wcdma_dl["PCPICH"].at("State");
        DL.PCPICH.SymbRate = json_wcdma_dl["PCPICH"].at("SymbRate"); 
        DL.PCPICH.ChanCode = json_wcdma_dl["PCPICH"].at("ChanCode");
        DL.PCPICH.Power = json_wcdma_dl["PCPICH"].at("Power");
    }
}

void set_wcdma_measure_param_from_json(Alg_3GPP_AlzMeasureWCDMA &Measure, const json &json_wcdma_measure)
{
    Measure.AclrLimit1Mode = json_wcdma_measure.at("AclrLimit1Mode");
    Measure.AclrLimit2Mode = json_wcdma_measure.at("AclrLimit2Mode");
    Measure.AclrAbsLimitMode = json_wcdma_measure.at("AclrAbsLimitMode");
    Measure.UtraLimit1 = json_wcdma_measure.at("UtraLimit1");
    Measure.UtraLimit2 = json_wcdma_measure.at("UtraLimit2");
    Measure.AbsLimit = json_wcdma_measure.at("AbsLimit");

    Measure.SEMLimitADMode = json_wcdma_measure.at("SEMLimitADMode");
    Measure.SEMLimitEFMode = json_wcdma_measure.at("SEMLimitEFMode");
    Measure.SEMAbsLimitGMode = json_wcdma_measure.at("SEMAbsLimitGMode");
    Measure.SEMAbsLimitHMode = json_wcdma_measure.at("SEMAbsLimitHMode");
    Measure.HMode = json_wcdma_measure.at("HMode");

    if (json_wcdma_measure.at("LimitAD").is_array()) {
        for(int i = 0; i < 4 && i < json_wcdma_measure.at("LimitAD").size(); i++) {
            Measure.LimitAD[i] = json_wcdma_measure.at("LimitAD")[i];
        }
    }
    if (json_wcdma_measure.at("LimitEF").is_array()) {
        for(int i = 0; i < 2 && i < json_wcdma_measure.at("LimitEF").size(); i++) {
            Measure.LimitEF[i] = json_wcdma_measure.at("LimitEF")[i];
        }
    }
    if (json_wcdma_measure.at("SEMAbsLimitG").is_array()) {
        for(int i = 0; i < 2 && i < json_wcdma_measure.at("SEMAbsLimitG").size(); i++) {
            Measure.SEMAbsLimitG[i] = json_wcdma_measure.at("SEMAbsLimitG")[i];
        }
    }
    if (json_wcdma_measure.at("SEMAbsLimitH").is_array()) {
        for(int i = 0; i < 2 && i < json_wcdma_measure.at("SEMAbsLimitH").size(); i++) {
            Measure.SEMAbsLimitH[i] = json_wcdma_measure.at("SEMAbsLimitH")[i];
        }
    }

    Measure.PeakMagnErrLimitMode = json_wcdma_measure.at("PeakMagnErrLimitMode");
    Measure.RmsMagnErrLimitMode = json_wcdma_measure.at("RmsMagnErrLimitMode");
    Measure.PeakMagnErrLimit = json_wcdma_measure.at("PeakMagnErrLimit");
    Measure.RmsMagnErrLimit = json_wcdma_measure.at("RmsMagnErrLimit");

    Measure.PeakEvmLimitMode = json_wcdma_measure.at("PeakEvmLimitMode");
    Measure.RmsEvmLimitMode = json_wcdma_measure.at("RmsEvmLimitMode");
    Measure.PeakEvmLimit = json_wcdma_measure.at("PeakEvmLimit");
    Measure.RmsEvmLimit = json_wcdma_measure.at("RmsEvmLimit");

    Measure.PeakPhaseErrLimitMode = json_wcdma_measure.at("PeakPhaseErrLimitMode");
    Measure.RmsPhaseErrLimitMode = json_wcdma_measure.at("RmsPhaseErrLimitMode");
    Measure.PeakPhaseErrLimit = json_wcdma_measure.at("PeakPhaseErrLimit");
    Measure.RmsPhaseErrLimit = json_wcdma_measure.at("RmsPhaseErrLimit");

    Measure.CFErrLimitMode = json_wcdma_measure.at("CFErrLimitMode");
    Measure.CFErrLimit = json_wcdma_measure.at("CFErrLimit");
    Measure.PhaseDisLimitMode = json_wcdma_measure.at("PhaseDisLimitMode");
    Measure.UpperLimit = json_wcdma_measure.at("UpperLimit");
    Measure.DynamicLimit = json_wcdma_measure.at("DynamicLimit");
    Measure.IQOffsetLimitMode = json_wcdma_measure.at("IQOffsetLimitMode");
    Measure.IQOffsetLimit = json_wcdma_measure.at("IQOffsetLimit");
    Measure.IQImabaLimitMode = json_wcdma_measure.at("IQImabaLimitMode");
    Measure.IQImabaLimit = json_wcdma_measure.at("IQImabaLimit");
}

int set_lte_param_from_json(Alg_3GPP_AlzIn4g &LTE, const json &json_lte)
{
    int ret = WT_OK;
    int ChanTypeSet = json_lte.at("ChanType");
    int ChanTypeCur = LTE.ChanType;
    if ((ChanTypeCur >= ALG_4G_PUSCH && ChanTypeCur <= ALG_4G_PUCCH) && (ChanTypeSet >= ALG_4G_PUSCH && ChanTypeSet <= ALG_4G_PUCCH))
    {
    }
    else if ((ChanTypeCur >= ALG_4G_PDSCH && ChanTypeCur <= ALG_4G_PHICH) && (ChanTypeSet >= ALG_4G_PDSCH && ChanTypeSet <= ALG_4G_PHICH))
    {
    }
    else
    {
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    LTE.CarrAggrState = json_lte.at("CarrAggrState");
    LTE.CyclicPrefix = json_lte.at("CyclicPrefix");
    LTE.UeID = json_lte.at("UeID");
    LTE.ChanType = json_lte.at("ChanType");

    if (json_lte.at("Cell").is_array()) {
        for (int i = 0; i < arraySize(LTE.Cell); i++) {
            LTE.Cell[i].CellIdx = i; //容错
            LTE.Cell[i].State = json_lte["Cell"][i].at("State");
            LTE.Cell[i].PhyCellID = json_lte["Cell"][i].at("PhyCellID");
            LTE.Cell[i].ChannelBW = json_lte["Cell"][i].at("ChannelBW");
            LTE.Cell[i].Duplexing = json_lte["Cell"][i].at("Duplexing");
            LTE.Cell[i].ULDLConfig = json_lte["Cell"][i].at("ULDLConfig");
            LTE.Cell[i].SpecialSubfrmConfig = json_lte["Cell"][i].at("SpecialSubfrmConfig");
        }
    }

    if (LTE.ChanType == ALG_4G_PUSCH) {
        if (json_lte.at("Pusch").is_array()) {
            for (int i = 0; i < arraySize(LTE.Pusch); i++) {
                set_lte_ul_param_from_json(LTE.Pusch[i], json_lte["Pusch"].at(i));
            }
        }
    } else if (LTE.ChanType == ALG_4G_PDSCH) {
        if (json_lte.contains("Pdsch")) {
            set_lte_dl_param_from_json(LTE.Pdsch, json_lte["Pdsch"]);
        }
    }

    set_lte_measure_param_from_json(LTE.MeasInfo, json_lte.at("MeasInfo"));
    set_lte_limit_param_from_json(LTE.LimitInfo, json_lte.at("LimitInfo"));

    return ret;
}

void set_lte_ul_param_from_json(Alg_3GPP_AlzPusch4g &Pusch, const json &json_lte_ul)
{
    Pusch.State = json_lte_ul.at("State");
    Pusch.RBNum = json_lte_ul.at("RBNum");
    Pusch.RBOffset = json_lte_ul.at("RBOffset");
    Pusch.Precoding = json_lte_ul.at("Precoding");
    Pusch.LayerNum = json_lte_ul.at("LayerNum");
    Pusch.AntennaNum = json_lte_ul.at("AntennaNum");
    Pusch.CodebookIdx = json_lte_ul.at("CodebookIdx");
    Pusch.GroupHop = json_lte_ul.at("GroupHop");
    Pusch.SequenceHop = json_lte_ul.at("SequenceHop");
    Pusch.DeltaSeqShift = json_lte_ul.at("DeltaSeqShift");
    Pusch.N1Dmrs = json_lte_ul.at("N1Dmrs");
    Pusch.CyclicShiftField = json_lte_ul.at("CyclicShiftField");
    Pusch.Codeword = json_lte_ul.at("Codeword");
    if (json_lte_ul.at("Modulate").is_array()) {
        for (int j = 0; j < arraySize(Pusch.Modulate); j++) {
            Pusch.Modulate[j] = json_lte_ul.at("Modulate").at(j);
        }
    }
    Pusch.ChanCodingState = json_lte_ul.at("ChanCodingState");
    Pusch.Scramble = json_lte_ul.at("Scramble");
    Pusch.McsCfgMode = json_lte_ul.at("McsCfgMode");
    if (json_lte_ul.at("Mcs").is_array()) {
        for (int j = 0; j < arraySize(Pusch.Mcs); j++) {
            Pusch.Mcs[j] = json_lte_ul.at("Mcs").at(j);
        }
    }
    if (json_lte_ul.at("PayloadSize").is_array()) {
        for (int j = 0; j < arraySize(Pusch.PayloadSize); j++) {
            Pusch.PayloadSize[j] = json_lte_ul.at("PayloadSize").at(j);
        }
    }
    if (json_lte_ul.at("RedunVerIdx").is_array()) {
        for (int j = 0; j < arraySize(Pusch.RedunVerIdx); j++) {
            Pusch.RedunVerIdx[j] = json_lte_ul.at("RedunVerIdx").at(j);
        }
    }
    Pusch.Enable256QAM = json_lte_ul.at("Enable256QAM");
    Pusch.RBDetMode = json_lte_ul.at("RBDetMode");
}

void set_lte_dl_param_from_json(Alg_3GPP_AlzPdsch4g &Pdsch, const json &json_lte_dl)
{
    Pdsch.SymbOffset = json_lte_dl.at("SymbOffset");
    Pdsch.ResAllocateType = json_lte_dl.at("ResAllocateType");
    Pdsch.VRBAssignment = json_lte_dl.at("VRBAssignment");
    
    if (json_lte_dl.at("RBGBitmap").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.RBGBitmap); i++) {
            Pdsch.RBGBitmap[i] = static_cast<char>(json_lte_dl.at("RBGBitmap").at(i).get<int>());
        }
    }
    
    Pdsch.RBNum = json_lte_dl.at("RBNum");
    Pdsch.RBOffset = json_lte_dl.at("RBOffset");
    Pdsch.PbchState = json_lte_dl.at("PbchState");
    
    Pdsch.Precoding = json_lte_dl.at("Precoding");
    Pdsch.LayerNum = json_lte_dl.at("LayerNum"); 
    Pdsch.AntennaNum = json_lte_dl.at("AntennaNum");
    Pdsch.CyclicDelayDiversity = json_lte_dl.at("CyclicDelayDiversity");
    Pdsch.CodebookIdx = json_lte_dl.at("CodebookIdx");
    
    Pdsch.Codeword = json_lte_dl.at("Codeword");
    if (json_lte_dl.at("Modulate").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.Modulate); i++) {
            Pdsch.Modulate[i] = json_lte_dl.at("Modulate").at(i);
        }
    }
    
    Pdsch.ChanCodingState = json_lte_dl.at("ChanCodingState");
    Pdsch.Scramble = json_lte_dl.at("Scramble");
    Pdsch.McsCfgMode = json_lte_dl.at("McsCfgMode");
    
    if (json_lte_dl.at("Mcs").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.Mcs); i++) {
            Pdsch.Mcs[i] = json_lte_dl.at("Mcs").at(i);
        }
    }
    
    if (json_lte_dl.at("PayloadSize").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.PayloadSize); i++) {
            Pdsch.PayloadSize[i] = json_lte_dl.at("PayloadSize").at(i);
        }
    }
    
    if (json_lte_dl.at("RedunVerIdx").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.RedunVerIdx); i++) {
            Pdsch.RedunVerIdx[i] = json_lte_dl.at("RedunVerIdx").at(i);
        }
    }
    
    if (json_lte_dl.at("SoftChanBit").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.SoftChanBit); i++) {
            Pdsch.SoftChanBit[i] = json_lte_dl.at("SoftChanBit").at(i);
        }
    }
    
    Pdsch.PA = json_lte_dl.at("PA");
    Pdsch.PB = json_lte_dl.at("PB");
    
    if (json_lte_dl.at("NIR").is_array()) {
        for (int i = 0; i < arraySize(Pdsch.NIR); i++) {
            Pdsch.NIR[i] = json_lte_dl.at("NIR").at(i);
        }
    }
    
    Pdsch.IRConfigMode = json_lte_dl.at("IRConfigMode");
    Pdsch.TxMode = json_lte_dl.at("TxMode");
    Pdsch.UECategory = json_lte_dl.at("UECategory");
    Pdsch.McsTable = json_lte_dl.at("McsTable");
    Pdsch.TbsIndexAlt = json_lte_dl.at("TbsIndexAlt");
}

void set_lte_measure_param_from_json(Alg_3GPP_MeasureIn4g &MeasInfo, const json &json_lte_measure)
{
    MeasInfo.MeasSubfrmIdx = json_lte_measure.at("MeasSubfrmIdx");
    MeasInfo.EvmSubcarrierState = json_lte_measure.at("EvmSubcarrierState"); 
    MeasInfo.EvmSymbIndx = json_lte_measure.at("EvmSymbIndx");
    MeasInfo.EvmSymbPosType = json_lte_measure.at("EvmSymbPosType");
    
    MeasInfo.MeasPwrState = json_lte_measure.at("MeasPwrState");
    MeasInfo.MeasAclrState = json_lte_measure.at("MeasAclrState");
    MeasInfo.MeasSEMState = json_lte_measure.at("MeasSEMState");
    MeasInfo.MeasEVMState = json_lte_measure.at("MeasEVMState");
    
    MeasInfo.DmrsConsState = json_lte_measure.at("DmrsConsState");
    MeasInfo.MeasureUnit = json_lte_measure.at("MeasureUnit");
    MeasInfo.ExAbnSymbFlg = json_lte_measure.at("ExAbnSymbFlg");

}

void set_lte_limit_param_from_json(Alg_3GPP_LimitIn4g &Limit, const json &json_lte_limit)
{
    Limit.ModLimitMode = json_lte_limit.at("ModLimitMode");
    Limit.SpectLimitMode = json_lte_limit.at("SpectLimitMode");

    if (json_lte_limit.at("SEMAddTestTol").is_array()) {
        for (int i = 0; i < arraySize(Limit.SEMAddTestTol); i++) {
            Limit.SEMAddTestTol[i] = json_lte_limit.at("SEMAddTestTol").at(i);
        }
    }

    if (json_lte_limit.at("ModLimit").is_array()) {
        for (int i = 0; i < arraySize(Limit.ModLimit); i++) {
            if (json_lte_limit["ModLimit"][i].contains("EvmRms")) {
                Limit.ModLimit[i].EvmRms.State = json_lte_limit["ModLimit"][i]["EvmRms"].at("State");
                Limit.ModLimit[i].EvmRms.Limit = json_lte_limit["ModLimit"][i]["EvmRms"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("EvmPeak")) {
                Limit.ModLimit[i].EvmPeak.State = json_lte_limit["ModLimit"][i]["EvmPeak"].at("State");
                Limit.ModLimit[i].EvmPeak.Limit = json_lte_limit["ModLimit"][i]["EvmPeak"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("MErrRms")) {
                Limit.ModLimit[i].MErrRms.State = json_lte_limit["ModLimit"][i]["MErrRms"].at("State");
                Limit.ModLimit[i].MErrRms.Limit = json_lte_limit["ModLimit"][i]["MErrRms"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("MErrPeak")) {
                Limit.ModLimit[i].MErrPeak.State = json_lte_limit["ModLimit"][i]["MErrPeak"].at("State");
                Limit.ModLimit[i].MErrPeak.Limit = json_lte_limit["ModLimit"][i]["MErrPeak"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("PhErrRms")) {
                Limit.ModLimit[i].PhErrRms.State = json_lte_limit["ModLimit"][i]["PhErrRms"].at("State");
                Limit.ModLimit[i].PhErrRms.Limit = json_lte_limit["ModLimit"][i]["PhErrRms"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("PhErrPeak")) {
                Limit.ModLimit[i].PhErrPeak.State = json_lte_limit["ModLimit"][i]["PhErrPeak"].at("State");
                Limit.ModLimit[i].PhErrPeak.Limit = json_lte_limit["ModLimit"][i]["PhErrPeak"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("FreqErr")) {
                Limit.ModLimit[i].FreqErr.State = json_lte_limit["ModLimit"][i]["FreqErr"].at("State");
                Limit.ModLimit[i].FreqErr.Limit = json_lte_limit["ModLimit"][i]["FreqErr"].at("Limit");
            }
            if (json_lte_limit["ModLimit"][i].contains("IQOffset")) {
                Limit.ModLimit[i].IQOffset.State = json_lte_limit["ModLimit"][i]["IQOffset"].at("State");
                if (json_lte_limit["ModLimit"][i]["IQOffset"].at("PwrLimit").is_array()) {
                    for (int j = 0; j < arraySize(Limit.ModLimit[i].IQOffset.PwrLimit); j++) {
                        Limit.ModLimit[i].IQOffset.PwrLimit[j] = json_lte_limit["ModLimit"][i]["IQOffset"]["PwrLimit"].at(j);
                    }
                }
            }
            if (json_lte_limit["ModLimit"][i].contains("IBE")) {
                Limit.ModLimit[i].IBE.State = json_lte_limit["ModLimit"][i]["IBE"].at("State");
                Limit.ModLimit[i].IBE.GenMin = json_lte_limit["ModLimit"][i]["IBE"].at("GenMin");
                Limit.ModLimit[i].IBE.GenEVM = json_lte_limit["ModLimit"][i]["IBE"].at("GenEVM");
                Limit.ModLimit[i].IBE.GenPwr = json_lte_limit["ModLimit"][i]["IBE"].at("GenPwr");
                if (json_lte_limit["ModLimit"][i]["IBE"].at("IQImage").is_array()) {
                    for (int j = 0; j < arraySize(Limit.ModLimit[i].IBE.IQImage); j++) {
                        Limit.ModLimit[i].IBE.IQImage[j] = json_lte_limit["ModLimit"][i]["IBE"]["IQImage"].at(j);
                    }
                }
                if (json_lte_limit["ModLimit"][i]["IBE"].at("IQOffsetPwr").is_array()) {
                    for (int j = 0; j < arraySize(Limit.ModLimit[i].IBE.IQOffsetPwr); j++) {
                        Limit.ModLimit[i].IBE.IQOffsetPwr[j] = json_lte_limit["ModLimit"][i]["IBE"]["IQOffsetPwr"].at(j);
                    }
                }
            }
            if (json_lte_limit["ModLimit"][i].contains("SpectFlat")) {
                Limit.ModLimit[i].SpectFlat.State = json_lte_limit["ModLimit"][i]["SpectFlat"].at("State");
                Limit.ModLimit[i].SpectFlat.Range1 = json_lte_limit["ModLimit"][i]["SpectFlat"].at("Range1");
                Limit.ModLimit[i].SpectFlat.Range2 = json_lte_limit["ModLimit"][i]["SpectFlat"].at("Range2");
                Limit.ModLimit[i].SpectFlat.Max1Min2 = json_lte_limit["ModLimit"][i]["SpectFlat"].at("Max1Min2");
                Limit.ModLimit[i].SpectFlat.Max2Min1 = json_lte_limit["ModLimit"][i]["SpectFlat"].at("Max2Min1");
                Limit.ModLimit[i].SpectFlat.EdgeFreq = json_lte_limit["ModLimit"][i]["SpectFlat"].at("EdgeFreq");
            }
        }
    }

    if (json_lte_limit.at("SpectLimit").is_array()) {
        for (int i = 0; i < arraySize(Limit.SpectLimit); i++) {
            if (json_lte_limit["SpectLimit"][i].contains("OBWLimit")) {
                Limit.SpectLimit[i].OBWLimit.State = json_lte_limit["SpectLimit"][i]["OBWLimit"].at("State");
                Limit.SpectLimit[i].OBWLimit.Limit = json_lte_limit["SpectLimit"][i]["OBWLimit"].at("Limit");
            }
            if (json_lte_limit["SpectLimit"][i].at("SEMLimit").is_array()) {
                for (int j = 0; j < arraySize(Limit.SpectLimit[i].SEMLimit); j++) {
                    Limit.SpectLimit[i].SEMLimit[j].State = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("State");
                    Limit.SpectLimit[i].SEMLimit[j].StartFreq = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("StartFreq");
                    Limit.SpectLimit[i].SEMLimit[j].StopFreq = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("StopFreq");
                    Limit.SpectLimit[i].SEMLimit[j].LimitPower = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("LimitPower");
                    Limit.SpectLimit[i].SEMLimit[j].StartPower = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("StartPower");
                    Limit.SpectLimit[i].SEMLimit[j].StopPower = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("StopPower");
                    Limit.SpectLimit[i].SEMLimit[j].RBW = json_lte_limit["SpectLimit"][i]["SEMLimit"][j].at("RBW");
                }
            }
            if (json_lte_limit["SpectLimit"][i].at("UtraLimit").is_array()) {
                for (int j = 0; j < arraySize(Limit.SpectLimit[i].UtraLimit); j++) {
                    Limit.SpectLimit[i].UtraLimit[j].RelState = json_lte_limit["SpectLimit"][i]["UtraLimit"][j].at("RelState");
                    Limit.SpectLimit[i].UtraLimit[j].AbsState = json_lte_limit["SpectLimit"][i]["UtraLimit"][j].at("AbsState");
                    Limit.SpectLimit[i].UtraLimit[j].RelLimit = json_lte_limit["SpectLimit"][i]["UtraLimit"][j].at("RelLimit");
                    Limit.SpectLimit[i].UtraLimit[j].AbsPwr = json_lte_limit["SpectLimit"][i]["UtraLimit"][j].at("AbsPwr");
                }
            }
            if (json_lte_limit["SpectLimit"][i].contains("EUtraLimit")) {
                Limit.SpectLimit[i].EUtraLimit.RelState = json_lte_limit["SpectLimit"][i]["EUtraLimit"].at("RelState");
                Limit.SpectLimit[i].EUtraLimit.AbsState = json_lte_limit["SpectLimit"][i]["EUtraLimit"].at("AbsState");
                Limit.SpectLimit[i].EUtraLimit.RelLimit = json_lte_limit["SpectLimit"][i]["EUtraLimit"].at("RelLimit");
                Limit.SpectLimit[i].EUtraLimit.AbsPwr = json_lte_limit["SpectLimit"][i]["EUtraLimit"].at("AbsPwr");
            }
        }
    }
}

int set_nr_param_from_json(Alg_3GPP_AlzIn5g &NR, const json &json_nr)
{
    int ret = WT_OK;

    if (NR.LinkDirect != json_nr.at("LinkDirect"))
    {
        return WT_3GPP_LINK_DIRECT_MISMATCH;
    }

    if (NR.LinkDirect == ALG_3GPP_UL)
    {
        set_nr_ul_param_from_json(NR.UL, json_nr.at("UL"));
    }
    else if (NR.LinkDirect == ALG_3GPP_DL)
    {
        set_nr_dl_param_from_json(NR.DL, json_nr.at("DL"));
    }
    
    set_nr_measure_param_from_json(NR.Measure, json_nr.at("Measure"));
    set_nr_limit_param_from_json(NR.LimitInfo, json_nr.at("LimitInfo"));

    return ret;
}

void set_nr_ul_param_from_json(Alg_3GPP_AlzULIn5g &UL, const json &json_nr_ul)
{
    UL.Duplexing = json_nr_ul.at("Duplexing");
    UL.SlotPeriod = json_nr_ul.at("SlotPeriod");
    UL.ULSlotnumber = json_nr_ul.at("ULSlotnumber");
    UL.SpecialSlotIdx = json_nr_ul.at("SpecialSlotIdx");
    UL.NSValue = json_nr_ul.at("NSValue");
    UL.CellNum = json_nr_ul.at("CellNum");
    UL.RfPhaseCompen = json_nr_ul.at("RfPhaseCompen");
    UL.ChanType = json_nr_ul.at("ChanType");

    if (json_nr_ul.at("Cell").is_array())
    {
        for (int i = 0; i < arraySize(UL.Cell); i++)
        {
            UL.Cell[i].CellIdx = i; //容错
            UL.Cell[i].State = json_nr_ul["Cell"][i].at("State");
            UL.Cell[i].Frequency = json_nr_ul["Cell"][i].at("Frequency");
            UL.Cell[i].ChannelBW = json_nr_ul["Cell"][i].at("ChannelBW");
            UL.Cell[i].PhyCellID = json_nr_ul["Cell"][i].at("PhyCellID");
            UL.Cell[i].DmrsTypeAPos = json_nr_ul["Cell"][i].at("DmrsTypeAPos");

            if (json_nr_ul["Cell"].at(i).at("TxBW").is_array())
            {
                for (int j = 0; j < arraySize(UL.Cell[j].TxBW); j++)
                {
                    UL.Cell[i].TxBW[j].SCSpacing = json_nr_ul["Cell"][i]["TxBW"][j].at("SCSpacing");
                    UL.Cell[i].TxBW[j].State = json_nr_ul["Cell"][i]["TxBW"][j].at("State");
                    UL.Cell[i].TxBW[j].MaxRBNumb = json_nr_ul["Cell"][i]["TxBW"][j].at("MaxRBNumb");
                    UL.Cell[i].TxBW[j].Offset = json_nr_ul["Cell"][i]["TxBW"][j].at("Offset");
                    UL.Cell[i].TxBW[j].k0u = json_nr_ul["Cell"][i]["TxBW"][j].at("k0u");
                }
            }

            if (json_nr_ul["Cell"].at(i).contains("Bwp"))
            {
                UL.Cell[i].Bwp.SCSpacing = json_nr_ul["Cell"][i]["Bwp"].at("SCSpacing");
                UL.Cell[i].Bwp.CyclicPrefix = json_nr_ul["Cell"][i]["Bwp"].at("CyclicPrefix");
                UL.Cell[i].Bwp.RBNum = json_nr_ul["Cell"][i]["Bwp"].at("RBNum");
                UL.Cell[i].Bwp.RBOffset = json_nr_ul["Cell"][i]["Bwp"].at("RBOffset");
                UL.Cell[i].Bwp.TransformPrecoder = json_nr_ul["Cell"][i]["Bwp"].at("TransformPrecoder");
                UL.Cell[i].Bwp.ResourceAllocation = json_nr_ul["Cell"][i]["Bwp"].at("ResourceAllocation");
                UL.Cell[i].Bwp.FreqHopMode = json_nr_ul["Cell"][i]["Bwp"].at("FreqHopMode");
            }

            if (json_nr_ul["Cell"].at(i).at("Bwp").contains("Dmrs"))
            {
                UL.Cell[i].Bwp.Dmrs.ConfigType = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("ConfigType");
                UL.Cell[i].Bwp.Dmrs.MaxLength = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("MaxLength");
                UL.Cell[i].Bwp.Dmrs.AdditionalPos = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("AdditionalPos");
                UL.Cell[i].Bwp.Dmrs.ScramblingID0 = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("ScramblingID0");
                UL.Cell[i].Bwp.Dmrs.ScramblingID1 = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("ScramblingID1");
                UL.Cell[i].Bwp.Dmrs.NPuschID = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("NPuschID");
                UL.Cell[i].Bwp.Dmrs.UseR16Dmrs = json_nr_ul["Cell"][i]["Bwp"]["Dmrs"].at("UseR16Dmrs");
            }
        }
    }

    if (json_nr_ul.at("Pusch").is_array())
    {
        for (int i = 0; i < arraySize(UL.Pusch); i++)
        {
            UL.Pusch[i].State = json_nr_ul["Pusch"].at(i).at("State");
            UL.Pusch[i].MappingType = json_nr_ul["Pusch"].at(i).at("MappingType");
            UL.Pusch[i].SymNum = json_nr_ul["Pusch"].at(i).at("SymNum");
            UL.Pusch[i].SymbOffset = json_nr_ul["Pusch"].at(i).at("SymbOffset");
            UL.Pusch[i].RBDetMode = json_nr_ul["Pusch"].at(i).at("RBDetMode");
            UL.Pusch[i].RBNum = json_nr_ul["Pusch"].at(i).at("RBNum");
            UL.Pusch[i].RBOffset = json_nr_ul["Pusch"].at(i).at("RBOffset");
            UL.Pusch[i].LayerNum = json_nr_ul["Pusch"].at(i).at("LayerNum");
            UL.Pusch[i].AntennaNum = json_nr_ul["Pusch"].at(i).at("AntennaNum");
            UL.Pusch[i].CDMGrpWOData = json_nr_ul["Pusch"].at(i).at("CDMGrpWOData");
            UL.Pusch[i].DmrsSymbLen = json_nr_ul["Pusch"].at(i).at("DmrsSymbLen");
            UL.Pusch[i].DmrsInitType = json_nr_ul["Pusch"].at(i).at("DmrsInitType");
            UL.Pusch[i].DmrsID = json_nr_ul["Pusch"].at(i).at("DmrsID");
            UL.Pusch[i].NSCID = json_nr_ul["Pusch"].at(i).at("NSCID");
            UL.Pusch[i].GrporSeqHopType = json_nr_ul["Pusch"].at(i).at("GrporSeqHopType");
            UL.Pusch[i].Modulate = json_nr_ul["Pusch"].at(i).at("Modulate");
            UL.Pusch[i].ChanCodingState = json_nr_ul["Pusch"].at(i).at("ChanCodingState");
            UL.Pusch[i].Scramble = json_nr_ul["Pusch"].at(i).at("Scramble");
            UL.Pusch[i].UeID = json_nr_ul["Pusch"].at(i).at("UeID");
            UL.Pusch[i].MCS = json_nr_ul["Pusch"].at(i).at("MCS");
            UL.Pusch[i].RvIdx = json_nr_ul["Pusch"].at(i).at("RvIdx");
            UL.Pusch[i].DmrsInitType = json_nr_ul["Pusch"].at(i).at("DmrsInitType");

            for (int j = 0; j < arraySize(UL.Pusch[i].DmrsAntPort); j++)
            {
                UL.Pusch[i].DmrsAntPort[j] = json_nr_ul["Pusch"].at(i).at("DmrsAntPort").at(j);
            }
        }
    }
}

void set_nr_dl_param_from_json(Alg_3GPP_AlzDLIn5g &DL, const json &json_nr_dl)
{
    DL.Duplexing = json_nr_dl.at("Duplexing");
    DL.SlotPeriod = json_nr_dl.at("SlotPeriod");
    DL.DLSlotNumber = json_nr_dl.at("DLSlotNumber");
    DL.SpecialSlotIdx = json_nr_dl.at("SpecialSlotIdx");
    DL.RfPhaseCompen = json_nr_dl.at("RfPhaseCompen");
    DL.CellNum = json_nr_dl.at("CellNum");

    if (json_nr_dl.at("Cell").is_array())
    {
        for (int i = 0; i < arraySize(DL.Cell); i++)
        {
            DL.Cell[i].CellIdx = i;
            DL.Cell[i].State = json_nr_dl["Cell"][i].at("State");
            DL.Cell[i].ChannelBW = json_nr_dl["Cell"][i].at("ChannelBW");
            DL.Cell[i].PhyCellID = json_nr_dl["Cell"][i].at("PhyCellID");
            DL.Cell[i].DmrsTypeAPos = json_nr_dl["Cell"][i].at("DmrsTypeAPos");
            DL.Cell[i].Deployment = json_nr_dl["Cell"][i].at("Deployment");
            DL.Cell[i].Frequency = json_nr_dl["Cell"][i].at("Frequency");

            if (json_nr_dl["Cell"][i].at("TxBW").is_array())
            {
                for (int j = 0; j < arraySize(DL.Cell[i].TxBW); j++)
                {
                    DL.Cell[i].TxBW[j].SCSpacing = json_nr_dl["Cell"][i]["TxBW"][j].at("SCSpacing");
                    DL.Cell[i].TxBW[j].State = json_nr_dl["Cell"][i]["TxBW"][j].at("State");
                    DL.Cell[i].TxBW[j].MaxRBNumb = json_nr_dl["Cell"][i]["TxBW"][j].at("MaxRBNumb");
                    DL.Cell[i].TxBW[j].Offset = json_nr_dl["Cell"][i]["TxBW"][j].at("Offset");
                    DL.Cell[i].TxBW[j].k0u = json_nr_dl["Cell"][i]["TxBW"][j].at("k0u");
                }
            }

            if (json_nr_dl["Cell"][i].contains("Bwp"))
            {
                DL.Cell[i].Bwp.SCSpacing = json_nr_dl["Cell"][i]["Bwp"].at("SCSpacing");
                DL.Cell[i].Bwp.CyclicPrefix = json_nr_dl["Cell"][i]["Bwp"].at("CyclicPrefix");
                DL.Cell[i].Bwp.RBNum = json_nr_dl["Cell"][i]["Bwp"].at("RBNum");
                DL.Cell[i].Bwp.RBOffset = json_nr_dl["Cell"][i]["Bwp"].at("RBOffset");

                if (json_nr_dl["Cell"][i]["Bwp"].contains("Pdsch"))
                {
                    DL.Cell[i].Bwp.Pdsch.VrbToPrbInterleaver = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("VrbToPrbInterleaver");
                    DL.Cell[i].Bwp.Pdsch.McsTab = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("McsTab");
                    DL.Cell[i].Bwp.Pdsch.ResourceAllocation = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("ResourceAllocation");
                    DL.Cell[i].Bwp.Pdsch.ConfigType = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("ConfigType");
                    DL.Cell[i].Bwp.Pdsch.MaxLength = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("MaxLength");
                    DL.Cell[i].Bwp.Pdsch.AdditionalPos = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("AdditionalPos");
                    DL.Cell[i].Bwp.Pdsch.RBGSizeType = json_nr_dl["Cell"][i]["Bwp"]["Pdsch"].at("RBGSizeType");
                }

                if (json_nr_dl["Cell"][i]["Bwp"].contains("Coreset"))
                {
                    DL.Cell[i].Bwp.Coreset.State = json_nr_dl["Cell"][i]["Bwp"]["Coreset"].at("State");
                    DL.Cell[i].Bwp.Coreset.SymbNum = json_nr_dl["Cell"][i]["Bwp"]["Coreset"].at("SymbNum");
                    DL.Cell[i].Bwp.Coreset.SymbOffset = json_nr_dl["Cell"][i]["Bwp"]["Coreset"].at("SymbOffset");
                    DL.Cell[i].Bwp.Coreset.FDResUseBitmap = json_nr_dl["Cell"][i]["Bwp"]["Coreset"].at("FDResUseBitmap");
                    DL.Cell[i].Bwp.Coreset.RBOffset = json_nr_dl["Cell"][i]["Bwp"]["Coreset"].at("RBOffset");
                    
                    if (json_nr_dl["Cell"][i]["Bwp"]["Coreset"].at("BitMap").is_array())
                    {
                        for (int j = 0; j < arraySize(DL.Cell[i].Bwp.Coreset.BitMap); j++)
                        {
                            DL.Cell[i].Bwp.Coreset.BitMap[j] = static_cast<char>(json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["BitMap"].at(j).get<int>());
                        }
                    }
                }
            }
        }
    }

    if (json_nr_dl.at("Channel").is_array())
    {
        for (int i = 0; i < arraySize(DL.Channel); i++)
        {
            DL.Channel[i].CellIdx = i; // 容错
            if (json_nr_dl["Channel"][i].contains("Pbch"))
            {
                DL.Channel[i].Pbch.State = json_nr_dl["Channel"][i]["Pbch"].at("State");
                DL.Channel[i].Pbch.SCSpacing = json_nr_dl["Channel"][i]["Pbch"].at("SCSpacing");
                DL.Channel[i].Pbch.RBOffset = json_nr_dl["Channel"][i]["Pbch"].at("RBOffset");
                DL.Channel[i].Pbch.SCOffset = json_nr_dl["Channel"][i]["Pbch"].at("SCOffset");
                DL.Channel[i].Pbch.PbchCase = json_nr_dl["Channel"][i]["Pbch"].at("PbchCase");
                DL.Channel[i].Pbch.Length = json_nr_dl["Channel"][i]["Pbch"].at("Length");
                DL.Channel[i].Pbch.BurstSetPeriod = json_nr_dl["Channel"][i]["Pbch"].at("BurstSetPeriod");
                DL.Channel[i].Pbch.HalfFrmIdx = json_nr_dl["Channel"][i]["Pbch"].at("HalfFrmIdx");
            }

            if (json_nr_dl["Channel"][i].contains("Pdcch"))
            {
                DL.Channel[i].Pdcch.State = json_nr_dl["Channel"][i]["Pdcch"].at("State");
            }

            if (json_nr_dl["Channel"][i].contains("Pdsch"))
            {
                DL.Channel[i].Pdsch.State = json_nr_dl["Channel"][i]["Pdsch"].at("State");
                DL.Channel[i].Pdsch.MappingType = json_nr_dl["Channel"][i]["Pdsch"].at("MappingType");
                DL.Channel[i].Pdsch.SymbNum = json_nr_dl["Channel"][i]["Pdsch"].at("SymbNum");
                DL.Channel[i].Pdsch.SymbOffset = json_nr_dl["Channel"][i]["Pdsch"].at("SymbOffset");
                DL.Channel[i].Pdsch.RBNum = json_nr_dl["Channel"][i]["Pdsch"].at("RBNum");
                DL.Channel[i].Pdsch.RBOffset = json_nr_dl["Channel"][i]["Pdsch"].at("RBOffset");
                DL.Channel[i].Pdsch.LayerNum = json_nr_dl["Channel"][i]["Pdsch"].at("LayerNum");
                DL.Channel[i].Pdsch.AntennaNum = json_nr_dl["Channel"][i]["Pdsch"].at("AntennaNum");
                DL.Channel[i].Pdsch.CDMGrpWOData = json_nr_dl["Channel"][i]["Pdsch"].at("CDMGrpWOData");
                DL.Channel[i].Pdsch.DmrsSymbLen = json_nr_dl["Channel"][i]["Pdsch"].at("DmrsSymbLen");
                DL.Channel[i].Pdsch.DmrsInitType = json_nr_dl["Channel"][i]["Pdsch"].at("DmrsInitType");
                DL.Channel[i].Pdsch.DmrsID = json_nr_dl["Channel"][i]["Pdsch"].at("DmrsID");
                DL.Channel[i].Pdsch.NSCID = json_nr_dl["Channel"][i]["Pdsch"].at("NSCID");
                DL.Channel[i].Pdsch.Codewords = json_nr_dl["Channel"][i]["Pdsch"].at("Codewords");
                DL.Channel[i].Pdsch.ChanCodingState = json_nr_dl["Channel"][i]["Pdsch"].at("ChanCodingState");
                DL.Channel[i].Pdsch.Scrambling = json_nr_dl["Channel"][i]["Pdsch"].at("Scrambling");
                DL.Channel[i].Pdsch.UsePdschScrambleID = json_nr_dl["Channel"][i]["Pdsch"].at("UsePdschScrambleID");
                DL.Channel[i].Pdsch.DataScrambleID = json_nr_dl["Channel"][i]["Pdsch"].at("DataScrambleID");
                DL.Channel[i].Pdsch.UeID = json_nr_dl["Channel"][i]["Pdsch"].at("UeID");

                if (json_nr_dl["Channel"][i]["Pdsch"].at("DmrsAntPort").is_array())
                {
                    for (int j = 0; j < arraySize(DL.Channel[i].Pdsch.DmrsAntPort); j++)
                    {
                        DL.Channel[i].Pdsch.DmrsAntPort[j] = json_nr_dl["Channel"][i]["Pdsch"]["DmrsAntPort"].at(j);
                    }
                }

                if (json_nr_dl["Channel"][i]["Pdsch"].at("Modulate").is_array())
                {
                    for (int j = 0; j < arraySize(DL.Channel[i].Pdsch.Modulate); j++)
                    {
                        DL.Channel[i].Pdsch.Modulate[j] = json_nr_dl["Channel"][i]["Pdsch"]["Modulate"].at(j);
                    }
                }

                if (json_nr_dl["Channel"][i]["Pdsch"].at("MCS").is_array())
                {
                    for (int j = 0; j < arraySize(DL.Channel[i].Pdsch.MCS); j++)
                    {
                        DL.Channel[i].Pdsch.MCS[j] = json_nr_dl["Channel"][i]["Pdsch"]["MCS"].at(j);
                    }
                }

                if (json_nr_dl["Channel"][i]["Pdsch"].at("RvIdx").is_array())
                {
                    for (int j = 0; j < arraySize(DL.Channel[i].Pdsch.RvIdx); j++)
                    {
                        DL.Channel[i].Pdsch.RvIdx[j] = json_nr_dl["Channel"][i]["Pdsch"]["RvIdx"].at(j);
                    }
                }

                if (json_nr_dl["Channel"][i]["Pdsch"].at("Bitmap").is_array()) {
                    for (int j = 0; j < arraySize(DL.Channel[i].Pdsch.Bitmap); ++j) {
                        DL.Channel[i].Pdsch.Bitmap[j] = static_cast<char>(json_nr_dl["Channel"][i]["Pdsch"]["Bitmap"].at(j).get<int>());
                    }
                }
            }
        }
    }
}

void set_nr_measure_param_from_json(Alg_3GPP_AlzMeasure5g &Measure, const json &json_meas)
{
    Measure.MeasSubfrmIdx = json_meas.at("MeasSubfrmIdx");
    Measure.MeasSlotIdx = json_meas.at("MeasSlotIdx");
    Measure.DmrsConsState = json_meas.at("DmrsConsState");
    Measure.EvmSubcarrierState = json_meas.at("EvmSubcarrierState");
    Measure.EvmSymbIndx = json_meas.at("EvmSymbIndx");
    Measure.EvmSymbPosType = json_meas.at("EvmSymbPosType");
    Measure.MeasPwrState = json_meas.at("MeasPwrState");
    Measure.MeasAclrState = json_meas.at("MeasAclrState");
    Measure.AclrLimitMode = json_meas.at("AclrLimitMode");

    if (json_meas.at("UtraLimit").is_array())
    {
        for (int i = 0; i < arraySize(Measure.UtraLimit); i++)
        {
            Measure.UtraLimit[i] = json_meas["UtraLimit"].at(i);
        }
    }
 
    Measure.NrUtraLimit = json_meas.at("NrUtraLimit");
    Measure.MeasSEMState = json_meas.at("MeasSEMState");
    Measure.SEMLimitMode = json_meas.at("SEMLimitMode");

    if (json_meas.at("SEMLimit").is_array())
    {
        for (int i = 0; i < arraySize(Measure.SEMLimit); i++)
        {
            Measure.SEMLimit[i] = json_meas["SEMLimit"].at(i);
        }
    }

    Measure.MeasEVMState = json_meas.at("MeasEVMState");
    Measure.EVMDelDCRBFlag = json_meas.at("EVMDelDCRBFlag");
    Measure.ExcAbnormSymbFlag = json_meas.at("ExcAbnormSymbFlag");
}

void set_nr_limit_param_from_json(Alg_3GPP_LimitIn5g &Limit, const json &JsonLimit)
{
    Limit.LimitMode = JsonLimit.at("LimitMode");

    // 设置 ModLimit 数组
    if (JsonLimit.at("ModLimit").is_array()) {
        for (int i = 0; i < arraySize(Limit.ModLimit); i++) {
            auto &modLimit = Limit.ModLimit[i];
            const auto &jsonModLimit = JsonLimit["ModLimit"][i];

            // 设置 EvmRms
            modLimit.EvmRms.State = jsonModLimit["EvmRms"]["State"];
            modLimit.EvmRms.Limit = jsonModLimit["EvmRms"]["Limit"];

            // 设置 EvmPeak
            modLimit.EvmPeak.State = jsonModLimit["EvmPeak"]["State"];
            modLimit.EvmPeak.Limit = jsonModLimit["EvmPeak"]["Limit"];

            // 设置 MErrRms
            modLimit.MErrRms.State = jsonModLimit["MErrRms"]["State"];
            modLimit.MErrRms.Limit = jsonModLimit["MErrRms"]["Limit"];

            // 设置 MErrPeak
            modLimit.MErrPeak.State = jsonModLimit["MErrPeak"]["State"];
            modLimit.MErrPeak.Limit = jsonModLimit["MErrPeak"]["Limit"];

            // 设置 PhErrRms
            modLimit.PhErrRms.State = jsonModLimit["PhErrRms"]["State"];
            modLimit.PhErrRms.Limit = jsonModLimit["PhErrRms"]["Limit"];

            // 设置 PhErrPeak
            modLimit.PhErrPeak.State = jsonModLimit["PhErrPeak"]["State"];
            modLimit.PhErrPeak.Limit = jsonModLimit["PhErrPeak"]["Limit"];

            // 设置 FreqErr
            modLimit.FreqErr.State = jsonModLimit["FreqErr"]["State"];
            modLimit.FreqErr.Limit = jsonModLimit["FreqErr"]["Limit"];

            // 设置 IQOffset
            modLimit.IQOffset.State = jsonModLimit["IQOffset"]["State"];
            if (jsonModLimit["IQOffset"]["PwrLimit"].is_array()) {
                for (int j = 0; j < arraySize(modLimit.IQOffset.PwrLimit); j++) {
                    modLimit.IQOffset.PwrLimit[j] = jsonModLimit["IQOffset"]["PwrLimit"][j];
                }
            }

            // 设置 IBE
            modLimit.IBE.State = jsonModLimit["IBE"]["State"];
            modLimit.IBE.GenMin = jsonModLimit["IBE"]["GenMin"];
            modLimit.IBE.GenEVM = jsonModLimit["IBE"]["GenEVM"];
            modLimit.IBE.GenPwr = jsonModLimit["IBE"]["GenPwr"];
            if (jsonModLimit["IBE"]["IQImage"].is_array()) {
                for (int j = 0; j < arraySize(modLimit.IBE.IQImage); j++) {
                    modLimit.IBE.IQImage[j] = jsonModLimit["IBE"]["IQImage"][j];
                }
            }
            if (jsonModLimit["IBE"]["IQOffsetPwr"].is_array()) {
                for (int j = 0; j < arraySize(modLimit.IBE.IQOffsetPwr); j++) {
                    modLimit.IBE.IQOffsetPwr[j] = jsonModLimit["IBE"]["IQOffsetPwr"][j];
                }
            }

            // 设置 SpectFlat
            modLimit.SpectFlat.State = jsonModLimit["SpectFlat"]["State"];
            modLimit.SpectFlat.Range1 = jsonModLimit["SpectFlat"]["Range1"];
            modLimit.SpectFlat.Range2 = jsonModLimit["SpectFlat"]["Range2"];
            modLimit.SpectFlat.Max1Min2 = jsonModLimit["SpectFlat"]["Max1Min2"];
            modLimit.SpectFlat.Max2Min1 = jsonModLimit["SpectFlat"]["Max2Min1"];
            modLimit.SpectFlat.EdgeFreq = jsonModLimit["SpectFlat"]["EdgeFreq"];
        }
    }

    // 设置 SpectLimit 数组
    if (JsonLimit.at("SpectLimit").is_array()) {
        for (int i = 0; i < arraySize(Limit.SpectLimit); i++) {
            auto &spectLimit = Limit.SpectLimit[i];
            const auto &jsonSpectLimit = JsonLimit["SpectLimit"][i];
            // 设置 OBWLimit
            spectLimit.OBWLimit.State = jsonSpectLimit["OBWLimit"]["State"];
            spectLimit.OBWLimit.Limit = jsonSpectLimit["OBWLimit"]["Limit"];

            // 设置 SEMLimit
            if (jsonSpectLimit["SEMLimit"].is_array()) {
                for (int j = 0; j < arraySize(spectLimit.SEMLimit); j++) {
                    for (int k = 0; k < ALG_3GPP_SEM_LIM_SET; k++) {
                        auto &semLimit = spectLimit.SEMLimit[j][k];
                        const auto &jsonSemLimit = jsonSpectLimit["SEMLimit"][j][k];
                        semLimit.State = jsonSemLimit["State"];
                        semLimit.StartFreq = jsonSemLimit["StartFreq"];
                        semLimit.StopFreq = jsonSemLimit["StopFreq"];
                        semLimit.LimitPower = jsonSemLimit["LimitPower"];
                        semLimit.StartPower = jsonSemLimit["StartPower"];
                        semLimit.StopPower = jsonSemLimit["StopPower"];
                        semLimit.RBW = jsonSemLimit["RBW"];
                    }
                }
            }

            // 设置 UtraLimit
            if (jsonSpectLimit["UtraLimit"].is_array()) {
                for (int j = 0; j < arraySize(spectLimit.UtraLimit); j++) {
                    auto &utraLimit = spectLimit.UtraLimit[j];
                    const auto &jsonUtraLimit = jsonSpectLimit["UtraLimit"][j];
                    utraLimit.RelState = jsonUtraLimit["RelState"];
                    utraLimit.AbsState = jsonUtraLimit["AbsState"];
                    utraLimit.RelLimit = jsonUtraLimit["RelLimit"];
                    utraLimit.AbsPwr = jsonUtraLimit["AbsPwr"];
                }
            }

            // 设置 NRLimit
            spectLimit.NRLimit.RelState = jsonSpectLimit["NRLimit"]["RelState"];
            spectLimit.NRLimit.AbsState = jsonSpectLimit["NRLimit"]["AbsState"];
            spectLimit.NRLimit.RelLimit = jsonSpectLimit["NRLimit"]["RelLimit"];
            spectLimit.NRLimit.AbsPwr = jsonSpectLimit["NRLimit"]["AbsPwr"];
        }
    }

    // 设置 SEMAddTestTol 数组
    if (JsonLimit.at("SEMAddTestTol").is_array()) {
        for (int i = 0; i < arraySize(Limit.SEMAddTestTol); i++) {
            Limit.SEMAddTestTol[i] = JsonLimit["SEMAddTestTol"][i];
        }
    }

    // 设置 ACLRAddTestTol 数组
    if (JsonLimit.at("ACLRAddTestTol").is_array()) {
        for (int i = 0; i < arraySize(Limit.ACLRAddTestTol); i++) {
            Limit.ACLRAddTestTol[i] = JsonLimit["ACLRAddTestTol"][i];
        }
    }

    // 设置 PowerLimit
    Limit.PowerLimit.State = JsonLimit["PowerLimit"]["State"];
    Limit.PowerLimit.OffPower = JsonLimit["PowerLimit"]["OffPower"];
    if (JsonLimit["PowerLimit"]["TestTol"].is_array()) {
        for (int i = 0; i < arraySize(Limit.PowerLimit.TestTol); i++) {
            Limit.PowerLimit.TestTol[i] = JsonLimit["PowerLimit"]["TestTol"][i];
        }
    }
}

int set_nbiot_param_from_json(Alg_3GPP_AlzInNBIOT &NBIOT, const json &json_nbiot)
{
    int ret = WT_OK;
    if (NBIOT.LinkDirect != json_nbiot.at("LinkDirect")) {
        return WT_ERR_CODE_GENERAL_ERROR;
    }

    if (NBIOT.LinkDirect == ALG_3GPP_UL) {
        set_nbiot_ul_param_from_json(NBIOT.UL, json_nbiot.at("UL"));
    } else if (NBIOT.LinkDirect == ALG_3GPP_DL) {
        set_nbiot_dl_param_from_json(NBIOT.DL, json_nbiot.at("DL"));
    }

    set_nbiot_measure_param_from_json(NBIOT.Measure, json_nbiot.at("Measure"));
    set_nbiot_limit_param_from_json(NBIOT.LimitInfo, json_nbiot.at("LimitInfo"));

    return ret;
}

void set_nbiot_ul_param_from_json(Alg_3GPP_AlzULInNBIOT &UL, const json &json_nbiot_ul)
{
    UL.Duplexing = json_nbiot_ul.at("Duplexing");
    UL.OperationMode = json_nbiot_ul.at("OperationMode"); 
    UL.ChannelBW = json_nbiot_ul.at("ChannelBW");
    UL.RBIdx = json_nbiot_ul.at("RBIdx");
    UL.NBCellID = json_nbiot_ul.at("NBCellID");
    UL.ChanType = json_nbiot_ul.at("ChanType");

    if (UL.ChanType == ALG_NBIOT_NPUSCH) {
        UL.Npusch.Format = json_nbiot_ul.at("Npusch").at("Format");
        UL.Npusch.SCSpacing = json_nbiot_ul.at("Npusch").at("SCSpacing");
        UL.Npusch.Repetitions = json_nbiot_ul.at("Npusch").at("Repetitions");
        UL.Npusch.RUs = json_nbiot_ul.at("Npusch").at("RUs");
        UL.Npusch.SubcarrierNum = json_nbiot_ul.at("Npusch").at("SubcarrierNum");
        UL.Npusch.StartSubcarrier = json_nbiot_ul.at("Npusch").at("StartSubcarrier");
        UL.Npusch.CyclicShift = json_nbiot_ul.at("Npusch").at("CyclicShift");
        UL.Npusch.GrpHopping = json_nbiot_ul.at("Npusch").at("GrpHopping");
        UL.Npusch.DeltaSeqShift = json_nbiot_ul.at("Npusch").at("DeltaSeqShift");
        UL.Npusch.Modulate = json_nbiot_ul.at("Npusch").at("Modulate");
        UL.Npusch.ChanCodingState = json_nbiot_ul.at("Npusch").at("ChanCodingState");
        UL.Npusch.Scrambling = json_nbiot_ul.at("Npusch").at("Scrambling");
        UL.Npusch.StartSubfrm = json_nbiot_ul.at("Npusch").at("StartSubfrm");
        UL.Npusch.TBSIdx = json_nbiot_ul.at("Npusch").at("TBSIdx");
        UL.Npusch.StartRVIdx = json_nbiot_ul.at("Npusch").at("StartRVIdx");
        UL.Npusch.UeID = json_nbiot_ul.at("Npusch").at("UeID");
    } else if (UL.ChanType == ALG_NBIOT_NPRACH) {
        // null
    }
}

void set_nbiot_dl_param_from_json(Alg_3GPP_AlzDLInNBIOT &DL, const json &json_nbiot_dl)
{
    DL.Duplexing = json_nbiot_dl.at("Duplexing");
    DL.OperationMode = json_nbiot_dl.at("OperationMode");
    DL.CarrierType = json_nbiot_dl.at("CarrierType");
    DL.ChannelBW = json_nbiot_dl.at("ChannelBW");
    DL.RBIdx = json_nbiot_dl.at("RBIdx");
    DL.NBCellID = json_nbiot_dl.at("NBCellID");
    DL.LTECellID = json_nbiot_dl.at("LTECellID");
    DL.LTEAntennaNum = json_nbiot_dl.at("LTEAntennaNum");
    DL.NBAntennaNum = json_nbiot_dl.at("NBAntennaNum");
    DL.SIB1Switch = json_nbiot_dl.at("SIB1Switch");
    DL.SchedulingInfoSIB1 = json_nbiot_dl.at("SchedulingInfoSIB1");
    DL.NPssPower = json_nbiot_dl.at("NPssPower");
    DL.NSssPower = json_nbiot_dl.at("NSssPower");
    DL.ChanType = json_nbiot_dl.at("ChanType");
    if (json_nbiot_dl.contains("Npdsch")) {
        DL.Npdsch.NSF = json_nbiot_dl.at("Npdsch").at("NSF");
        DL.Npdsch.Repetitions = json_nbiot_dl.at("Npdsch").at("Repetitions");
        DL.Npdsch.MCS = json_nbiot_dl.at("Npdsch").at("MCS");
        DL.Npdsch.StartSymb = json_nbiot_dl.at("Npdsch").at("StartSymb");
        DL.Npdsch.StartSubfrm = json_nbiot_dl.at("Npdsch").at("StartSubfrm");
        DL.Npdsch.Precoding = json_nbiot_dl.at("Npdsch").at("Precoding");
        DL.Npdsch.ChanCodingState = json_nbiot_dl.at("Npdsch").at("ChanCodingState");
        DL.Npdsch.Scrambling = json_nbiot_dl.at("Npdsch").at("Scrambling");
        DL.Npdsch.UeID = json_nbiot_dl.at("Npdsch").at("UeID");
        DL.Npdsch.Power = json_nbiot_dl.at("Npdsch").at("Power");
    }
}

void set_nbiot_measure_param_from_json(Alg_3GPP_AlzMeasureNBIOT &Measure, const json &json_nbiot_measure)
{
    Measure.StatisticAvgFlg = json_nbiot_measure.at("StatisticAvgFlg");
    Measure.StatisticCnt = json_nbiot_measure.at("StatisticCnt"); 
    Measure.MeasureUnit = json_nbiot_measure.at("MeasureUnit");
    Measure.ConstShowPilot = json_nbiot_measure.at("ConstShowPilot");
}

void set_nbiot_limit_param_from_json(Alg_3GPP_LimitInNBIOT &Limit, const json &JsonLimit)
{
    Limit.ModLimitMode = JsonLimit.at("ModLimitMode");
    
    Limit.EvmRms.State = JsonLimit.at("EvmRms").at("State");
    Limit.EvmRms.Limit = JsonLimit.at("EvmRms").at("Limit");
    
    Limit.EvmPeak.State = JsonLimit.at("EvmPeak").at("State");
    Limit.EvmPeak.Limit = JsonLimit.at("EvmPeak").at("Limit");
    
    Limit.MErrRms.State = JsonLimit.at("MErrRms").at("State");
    Limit.MErrRms.Limit = JsonLimit.at("MErrRms").at("Limit");
    
    Limit.MErrPeak.State = JsonLimit.at("MErrPeak").at("State");
    Limit.MErrPeak.Limit = JsonLimit.at("MErrPeak").at("Limit");
    
    Limit.PhErrRms.State = JsonLimit.at("PhErrRms").at("State");
    Limit.PhErrRms.Limit = JsonLimit.at("PhErrRms").at("Limit");
    
    Limit.PhErrPeak.State = JsonLimit.at("PhErrPeak").at("State");
    Limit.PhErrPeak.Limit = JsonLimit.at("PhErrPeak").at("Limit");
    
    Limit.FreqErrLow.State = JsonLimit.at("FreqErrLow").at("State");
    Limit.FreqErrLow.Limit = JsonLimit.at("FreqErrLow").at("Limit");
    
    Limit.FreqErrHigh.State = JsonLimit.at("FreqErrHigh").at("State");
    Limit.FreqErrHigh.Limit = JsonLimit.at("FreqErrHigh").at("Limit");
    
    Limit.IQOffset.State = JsonLimit.at("IQOffset").at("State");
    for (int i = 0; i < arraySize(Limit.IQOffset.PwrLimit); i++) {
        Limit.IQOffset.PwrLimit[i] = JsonLimit.at("IQOffset").at("PwrLimit").at(i);
    }
    
    Limit.IBE.State = JsonLimit.at("IBE").at("State");
    Limit.IBE.GenMin = JsonLimit.at("IBE").at("GenMin");
    Limit.IBE.GenEVM = JsonLimit.at("IBE").at("GenEVM");
    Limit.IBE.GenPwr = JsonLimit.at("IBE").at("GenPwr");
    for (int i = 0; i < arraySize(Limit.IBE.IQImage); i++) {
        Limit.IBE.IQImage[i] = JsonLimit.at("IBE").at("IQImage").at(i);
    }
    for (int i = 0; i < arraySize(Limit.IBE.IQOffsetPwr); i++) {
        Limit.IBE.IQOffsetPwr[i] = JsonLimit.at("IBE").at("IQOffsetPwr").at(i);
    }
    
    Limit.SpectLimitMode = JsonLimit.at("SpectLimitMode");
    
    Limit.OBWLimit.State = JsonLimit.at("OBWLimit").at("State");
    Limit.OBWLimit.Limit = JsonLimit.at("OBWLimit").at("Limit");
    
    for (int i = 0; i < arraySize(Limit.SEMLimit); i++) {
        Limit.SEMLimit[i].State = JsonLimit.at("SEMLimit").at(i).at("State");
        Limit.SEMLimit[i].StartFreq = JsonLimit.at("SEMLimit").at(i).at("StartFreq");
        Limit.SEMLimit[i].StopFreq = JsonLimit.at("SEMLimit").at(i).at("StopFreq");
        Limit.SEMLimit[i].LimitPower = JsonLimit.at("SEMLimit").at(i).at("LimitPower");
        Limit.SEMLimit[i].StartPower = JsonLimit.at("SEMLimit").at(i).at("StartPower");
        Limit.SEMLimit[i].StopPower = JsonLimit.at("SEMLimit").at(i).at("StopPower");
        Limit.SEMLimit[i].RBW = JsonLimit.at("SEMLimit").at(i).at("RBW");
    }
    
    Limit.GSM.RelState = JsonLimit.at("GSM").at("RelState");
    Limit.GSM.AbsState = JsonLimit.at("GSM").at("AbsState"); 
    Limit.GSM.RelLimit = JsonLimit.at("GSM").at("RelLimit");
    Limit.GSM.AbsPwr = JsonLimit.at("GSM").at("AbsPwr");
    
    Limit.UTRA.RelState = JsonLimit.at("UTRA").at("RelState");
    Limit.UTRA.AbsState = JsonLimit.at("UTRA").at("AbsState");
    Limit.UTRA.RelLimit = JsonLimit.at("UTRA").at("RelLimit");
    Limit.UTRA.AbsPwr = JsonLimit.at("UTRA").at("AbsPwr");
}

} // namespace
