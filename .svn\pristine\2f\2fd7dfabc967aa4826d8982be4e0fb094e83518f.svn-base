#include "errorlib.h"

#include <iostream>
#include <iomanip>
#include <mutex>
#include "wtlog.h"

ErrorLib &ErrorLib::Instance(void)
{
	static ErrorLib ErrorLibInstance;
	return ErrorLibInstance;
}

ErrorLib::ErrorLib()
{
}

ErrorLib::~ErrorLib()
{
}

void ErrorLib::AddErrCode(int ErrCode)
{
    std::unique_lock<std::mutex> ErrLock(Mutex);
	int Done = 0;
	for (auto &iter : Errorcode)
	{
		if (iter == ErrCode) 
		{
			Done = 1;
			break;
		}
	}
	if (!Done)
	{
		Errorcode.push_back(ErrCode);
	}
    ErrLock.unlock();
}

void ErrorLib::CheckErrCode(int Ret, int ErrCode)
{
	if (Ret) 
	{
		AddErrCode(ErrCode);
	}
}

void ErrorLib::DelErrCode(int ErrCode)
{
	int Done = 0, Id = 0;
    std::unique_lock<std::mutex> ErrLock(Mutex);
	for (auto &iter : Errorcode)
	{
		if (iter == ErrCode)
		{
			Done = 1;
			break;
		}
		Id++;
	}
	if (Done)
	{
		Errorcode.erase(Errorcode.begin() + Id);
	}
    ErrLock.unlock();
}

void ErrorLib::ShowErrCode(void)
{
    std::unique_lock<std::mutex> ErrLock(Mutex);
	WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Errorcode code = " << Errorcode.size() << std::hex << std::endl;
	for (auto &iter : Errorcode)
	{
		WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "0x" << iter << "\n";
	}
	WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::dec;
    ErrLock.unlock();
}

void ErrorLib::ClearErrCode(void)
{
    std::unique_lock<std::mutex> ErrLock(Mutex);
	Errorcode.clear();
    ErrLock.unlock();
}

int ErrorLib::GetErrCodeSize(void)
{
    int ret = WT_OK;
    std::unique_lock<std::mutex> ErrLock(Mutex);
    ret = Errorcode.size();
    ErrLock.unlock();
    return ret;
}

int ErrorLib::GetErrCode(int Id)
{
    int ret = WT_OK;
    std::unique_lock<std::mutex> ErrLock(Mutex);
    ret = Errorcode[Id];
    ErrLock.unlock();
    return ret;
}