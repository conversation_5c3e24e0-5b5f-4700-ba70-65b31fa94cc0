#ifndef _WLAN_GCMP_H_
#define _WLAN_GCMP_H_
#include "includes.h"
#include "common.h"
#include "crc32.h"
#include "wlan_wep.h"
#include "ieee80211_def.h"
#include "wlan_encryption_api.h"

class wlan_gcmp : public wlan_wep
{
public:
    wlan_gcmp();
    virtual ~wlan_gcmp();
    /**
     * @brief decrypt MAC frame
     *
     * @param pInData: encrypted MPDU data
     * @param len : encrypted MPDU data length
     * @param pOutData: dcrypted data
     * @param outLen: dcrypted data length
     * @param plaintext_pos: plaintext data postion at pOutData
     * @param data_len:plaintext data length
     * @return s32: 0 = OK, other = fail
     */
    virtual s32 decrypt_frame_data(u8 *pInData, u32 len, u8 **pOutData, u32 *outLen, u32 *plaintext_pos, u32 *data_len);
    /**
     * @brief encrypt data
     *
     * @param pInData : plaintext data
     * @param len : : plaintext data length
     * @param header : GCMP pn header
     * @param pOutData : encrpyted data(MAC header + GCMP header + cipher data + MIC)
     * @param outLen : encrypted data length
     * @return s32: 0 = OK, other = fail
     */
    virtual s32 encrypt_data(u8 *pInData, u32 len, GCMP_Header *header, u8 **pOutData, u32 *outLen);

private:
    /**
     * @brief GCMP aad nonce generator
     *
     * @param hdr : MAC header
     * @param data : frame data, not include GCMP header
     * @param aad : aad result
     * @param aad_len : aad result length
     * @param nonce : nonce result
     */
    void gcmp_aad_nonce(HEADER_802_11 *hdr, const u8 *data, u8 *aad, size_t *aad_len, u8 *nonce);
    u8 *gcmp_decrypt(const u8 *tk, size_t tk_len, HEADER_802_11 *hdr, const u8 *data, size_t data_len, size_t *decrypted_len);
    u8 *gcmp_encrypt(const u8 *tk, size_t tk_len, const u8 *frame, size_t len, size_t hdrlen, GCMP_Header *header, size_t *encrypted_len);
};

#endif // !_GCMP_H_
