#该文件主要保存，不可通过通讯协议修改的，可升级覆盖的内容，包含在升级包中

#设备TCP服务使用的端口
port = 8600

#设备主网口信息
interface = eth0
interface_wt428 = eth18

#设备主网口信息
DigitalLibInterface = eth1

#数字IQ数据翻转
DigitalConvert = 0
DigitalLightConvert = 1

#光口治具模式发包延时 us
DigitalLibLightDutSendDelay = 5

#光口仪器背板PCI寄存器读写延时
DigitalLibLightBackWriteDelay = 2500
DigitalLibLightBackReadDelay = 200

#VSG的Flatness(平坦度)补偿开关
VSGFlatnessComp = 1

#VSG的IQ_IMB补偿开关
VSGIqImbComp = 1

#VSG强制使用IQ时域补偿
VSGForceTimeDomainIqComp = 0

#VSA的Flatness(平坦度)补偿开关
VSAFlatnessComp = 1

#VSA的IQ_IMB补偿开关
VSAIqImbComp = 1

#VSA强制使用IQ时域补偿
VSAForceTimeDomainIqComp = 0

#模拟IQ VSG功率校准开关
AnalogIQCal = 0

#温度补偿间隔(s)
TempCalInterval = 5

#autorange参数配置
RefOffset = 0.5
RangeDiffMin = 0
RangeDiffMax = 1.5

#是否用80+80实现160
Trans160To8080 = 0

#是否保存仪器温度数据
IsSaveTemperatureLog = 0

#仪器温度变化幅度阈值
TempChangeThreshold = 5

#是否记录历史温度
IsSaveHistoryTemp = 1

#是否开启自动烤机任务
AutoBaking = 0

# 服务启动后开启自校准的时间
InCalAfterServer = 30
# 仪器运行后是否开启自动校准
InternalCal = 1
# 仪器运行后,温度触发自动校准阈值*100(50就是0.5度, 最低设置40即0.4度)
InCalTemp = 50

# 仪器运行后，每隔一段时间，校准所有端口一次，单位分钟
InCalAllInterval = 180
# 自校准单元端口选项，-1：按顺序校准；0：每次校准第一个端口；1：每次校准第二个端口...
InCalPortChoice = 0
# 自校准单元选单端口校准, 加速自校准
InCalOnePort = 1
# 自校准加速选择，当存在BusinessBoardFlag文件时候根据以下判断是否加速
InCalOnePortEx = 0
# 自校准相邻两次校准数据抖动范围
InCalPowerShakeRange = 0.2

# 自校准稳定后的间隔时间分钟
#InCalSpace = 15
InCalSpace_418 = 15
InCalSpace_428 = 15
InCalSpace_448 = 5

# 自校准是否开启多线程
InCalMulThread = 0

# 修改ATT0时的额外延时,单位us
VsaAtt0ConfigDelay = 2000

# Q路数据偏移
QDataShift = 0

# 分析保存分析的原始数据，辅助平均功能验证使用
# 使能开关
SaveRawDataFlag = 0
# 保存文件的最大数
TemporaryFilesNum = 100
# 保存文件类型, 0为csv, 1为bwv
FileType = 0

# 硬件解码调试
HwDecodeLogPrint = 0
# VSG发送累计数调试
PnConuterDebug = 0

# 保存VSA原始数据文件的最大数
VSASaveRawDataMaxNum = 10

