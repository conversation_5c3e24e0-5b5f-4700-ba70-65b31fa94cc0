/*
 * @Description: 3GPP：NR5G配置分析参数相关命令
 * @Autor: 
 * @Date: 20231103
 */
#ifndef SCPI_3GPP_ALZ_NR5G_H_
#define SCPI_3GPP_ALZ_NR5G_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif
    //**************************************************************************************************
    // UL
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_UL_SetAlzRFPhaseCompensation(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzDuplexing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSlotPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzSpecialSlotIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellNum(scpi_t *context);

    //UL Cell
    scpi_result_t SCPI_NR5G_UL_SetAlzCellIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBW(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellPhysicalID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellDmrsTypeAPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWMaxRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellTxBWK0U(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpCyclicPrefix(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpTransformPrecoder(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpResourceAllocationType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpFreqHoppingMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSMaxLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSAddPosInd(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSScramblingId(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzCellBwpDMRSNpuschID(scpi_t *context);

    // scpi_result_t SCPI_NR5G_UL_SetAlzUEID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzChannelType(scpi_t *context);
    //UL PUSCH
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschCellIdex(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschMappingType(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschSymbolOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBDetectMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschLayerNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschAntennaNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschCDMGroupsWoData(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsAntennaPort(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSybolLength(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenInit(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschDmrsSeqGenNscid(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschGroupOrSequenceHopping(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschModulate(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschChannelCodeingState(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschScrambling(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschMCS(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschUEID(scpi_t *context);
    scpi_result_t SCPI_NR5G_UL_SetAlzPuschRVIndex(scpi_t *context);

    //**************************************************************************************************
    // DL
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_DL_SetAlzDuplexing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSlotPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSlotNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzSpecialSlotIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzRFPhaseCompensation(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellNum(scpi_t *context);
    
    //DL Cell
    scpi_result_t SCPI_NR5G_DL_SetAlzCellIndex(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellFrequency(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBW(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellPhysicalID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellDmrsTypeAPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWMaxRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellTxBWK0U(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpSubCarrierSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCyclicPrefix(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBNumber(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschVrbToPrbInterLeaver(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMcsTable(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschResourceAlloc(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschRBGSizeType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschConfigType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschMaxLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpPdschAdditionalPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetSymbolOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetFDRes(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzCellBwpCoresetBitMap(scpi_t *context);

    //DL Scheduled Slot Allocation
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotCellIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubSpacing(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchSubOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchCase(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchLength(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchPosition(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchBurstSetPeriod(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPbchHalfFrameIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdcchState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMapType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschSymbOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBDetMode(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRBOffset(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschBitMap(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschLayerNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschAntennaNum(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCDMGroupsWoData(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsSymbLen(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsAntPort(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsInitType(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDmrsID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschNSCID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschCodewords(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschModulate(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschChanCodingState(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschScrambling(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUsePdschScrambleID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschDataScrambleID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschUeID(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschMCS(scpi_t *context);
    scpi_result_t SCPI_NR5G_DL_SetAlzScheSlotPdschRvIdx(scpi_t *context);


    scpi_result_t SCPI_NR5G_DL_SetAlzChannelType(scpi_t *context);





















    //**************************************************************************************************
    // Measure Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_NR5G_Measure_SetAlzSubFrameIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSlotIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzDmrsConsState(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzEvmDeleteDcFlag(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzEVMSubcarrier(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzSymbolIdx(scpi_t *context);
    scpi_result_t SCPI_NR5G_Measure_SetAlzWindowPosition(scpi_t *context);
    
#ifdef __cplusplus
}
#endif

#endif