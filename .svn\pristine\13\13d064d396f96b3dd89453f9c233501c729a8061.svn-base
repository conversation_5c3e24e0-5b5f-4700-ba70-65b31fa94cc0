/**
 * @file scpi_3gpp_common.h
 * @brief 蜂窝公共头文件
 * @version 0.1
 * @date 2024-09-20
 * @copyright Copyright (c) 2024
 */

#ifndef SCPI_3GPP_COMMON_H_
#define SCPI_3GPP_COMMON_H_

// ============================================================================
// 系统头文件
// ============================================================================
#include <algorithm>
#include <initializer_list>
#include <tuple>
#include <type_traits>
#include <vector>

// ============================================================================
// 项目头文件
// ============================================================================
#include "alg/includeAll.h"
#include "alzdef.h"
#include "scpi/scpi.h"
#include "wterror.h"

namespace cellular {

// ============================================================================
// 公共定义和工具
// ============================================================================

/**
 * @brief 命令参数结构体
 */
struct Command {
    int min_;
    int max_;
    int value_;
    
    Command(int min, int max) : min_(min), max_(max), value_(0) {}
};

#define INT_RANGE(min, max) ParamRange<int>(min, max)
#define DOUBLE_RANGE(min, max) ParamRange<double>(min, max)
#define FLOAT_RANGE(min, max) ParamRange<float>(min, max)

// 参数范围配置模板
template<typename T>
struct ParamRange {
    T min;
    T max;
    
    ParamRange(T min_val, T max_val) 
    : min(min_val), max(max_val) {}
};

// SCPI 参数检查器
class ScpiChecker {
private:
    scpi_t* context_;
    int result_;
    
public:
    explicit ScpiChecker(scpi_t* ctx) : context_(ctx), result_(WT_OK) {}
    
    ScpiChecker& CommandParam(int& value, const ParamRange<int>& range) {
        if (result_ == WT_OK) {
            result_ = ValidateCommandParam(value, range);
        }
        return *this;
    }
    
    template<typename T>
    ScpiChecker& Param(T& value, const ParamRange<T>& range) {
        if (result_ == WT_OK) {
            result_ = ValidateParam(value, range);
        }
        return *this;
    }

    template<typename T>
    ScpiChecker& ParamFromList(T& value, std::initializer_list<T> validValues) {
        if (result_ == WT_OK) {
            result_ = ValidateParamFromList(value, validValues);
        }
        return *this;
    }
    
    int Result() const { return result_; }
    
private:
    int ValidateCommandParam(int& value, const ParamRange<int>& range) {
        SCPI_CommandNumbers(context_, &value, 1);
        if (value < range.min || value > range.max) {
            return WT_3GPP_COMMAND_NUM_OUT_OF_RANGE;
        }
        return WT_OK;
    }

    template<typename T>
    int ValidateParam(T& value, const ParamRange<T>& range) {
        if (std::is_integral<T>::value) {
            int temp;
            if (!SCPI_ParamInt(context_, &temp, true)) {
                return WT_3GPP_GET_VALUE_FAILED;
            }
            value = static_cast<T>(temp);
        } else if (std::is_floating_point<T>::value) {
            double temp;
            if (!SCPI_ParamDouble(context_, &temp, true)) {
                return WT_3GPP_GET_VALUE_FAILED;
            }
            value = static_cast<T>(temp);
        }
        
        if (value < range.min || value > range.max) {
            return WT_3GPP_VALUE_OUT_OF_RANGE;
        }
        return WT_OK;
    }

    int ValidateStringParam(std::string& value) {
        const char* str;
        size_t len;
        if (!SCPI_ParamCharacters(context_, &str, &len, true)) {
            return WT_3GPP_GET_VALUE_FAILED;
        }
        value.assign(str, len);
        return WT_OK;
    }
    
    template<typename T>
    int ValidateParamFromList(T& value, std::initializer_list<T> validValues) {
        if (std::is_integral<T>::value) {
            int temp;
            if (!SCPI_ParamInt(context_, &temp, true)) {
                return WT_3GPP_GET_VALUE_FAILED;
            }
            value = static_cast<T>(temp);
        } else if (std::is_floating_point<T>::value) {
            double temp;
            if (!SCPI_ParamDouble(context_, &temp, true)) {
                return WT_3GPP_GET_VALUE_FAILED;
            }
            value = static_cast<T>(temp);
        }
        
        if (std::find(validValues.begin(), validValues.end(), value) == validValues.end()) {
            return WT_3GPP_VALUE_OUT_OF_RANGE;
        }
        return WT_OK;
    }
};

/**
 * @brief 通用SCPI值获取模板函数
 * @tparam T 值类型
 * @param context SCPI上下文
 * @param value 输出值指针
 * @param mandatory 是否必需参数
 * @return 是否成功获取
 */
template <typename T>
inline bool GetScpiValue(scpi_t* context, T* value, bool mandatory);

// int类型特化
template <>
inline bool GetScpiValue<int>(scpi_t* context, int* value, bool mandatory) {
    return SCPI_ParamInt(context, value, mandatory);
}

// double类型特化
template <>
inline bool GetScpiValue<double>(scpi_t* context, double* value, bool mandatory) {
    return SCPI_ParamDouble(context, value, mandatory);
}

// float类型特化
template <>
inline bool GetScpiValue<float>(scpi_t* context, float* value, bool mandatory) {
    double double_val;
    if (SCPI_ParamDouble(context, &double_val, mandatory)) {
        *value = static_cast<float>(double_val);
        return true;
    }
    return false;
}

// char类型特化
template <>
inline bool GetScpiValue<char>(scpi_t* context, char* value, bool mandatory) {
    int int_val;
    if (SCPI_ParamInt(context, &int_val, mandatory)) {
        *value = static_cast<char>(int_val);
        return true;
    }
    return false;
}

/**
 * @brief 获取SCPI命令参数数量
 * @param context SCPI上下文
 * @param params 参数向量
 * @return 参数数量
 */
int GetScpiCommandNumbers(scpi_t* context, std::vector<Command>& params);


// ============================================================================
// 通用模板函数
// ============================================================================

/**
 * @brief 检查范围并赋值的通用实现
 * @tparam FieldType 字段类型
 * @tparam MinType 最小值类型
 * @tparam MaxType 最大值类型
 * @param context SCPI上下文
 * @param field 目标字段引用
 * @param min 最小值
 * @param max 最大值
 * @return 错误码
 */
template <typename FieldType, typename MinType, typename MaxType>
int SetValueInRange(scpi_t* context, FieldType& field, MinType min, MaxType max) {
    FieldType value = 0;
    if (!GetScpiValue(context, &value, true)) {
        return WT_3GPP_GET_VALUE_FAILED;
    }

    if (value < static_cast<FieldType>(min) || value > static_cast<FieldType>(max)) {
        return WT_3GPP_VALUE_OUT_OF_RANGE;
    }

    field = value;
    return WT_OK;
}

/**
 * @brief 检查指定值并赋值的通用实现
 * @tparam FieldType 字段类型
 * @param context SCPI上下文
 * @param field 目标字段引用
 * @param validValues 有效值列表
 * @return 错误码
 */
template <typename FieldType>
int SetValueFromSpecified(scpi_t* context, FieldType& field, 
                         std::initializer_list<FieldType> validValues) {
    FieldType value = 0;
    if (!GetScpiValue(context, &value, true)) {
        return WT_3GPP_GET_VALUE_FAILED;
    }

    if (std::find(validValues.begin(), validValues.end(), value) == validValues.end()) {
        return WT_3GPP_VALUE_OUT_OF_RANGE;
    }

    field = value;
    return WT_OK;
}

// ============================================================================
// VSA相关功能
// ============================================================================

/**
 * @brief 检查范围并设置VSA值
 */
template <typename FieldType, typename MinType, typename MaxType>
int SetVsaValueInRange(scpi_t* context, FieldType& field, MinType min, MaxType max) {
    return SetValueInRange(context, field, min, max);
}

/**
 * @brief 从指定值列表设置VSA值
 */
template <typename FieldType>
int SetVsaValueFromSpecified(scpi_t* context, FieldType& field,
                            std::initializer_list<FieldType> validValues) {
    return SetValueFromSpecified(context, field, validValues);
}

/**
 * @brief 从3GPP回调获取VSA默认参数
 * @param standard 标准类型
 * @param linkDirect 链路方向
 * @param param 输出参数
 */
void GetVsaDefaultParamFrom3GPPCallback(const int& standard, const int& linkDirect, 
                                       AlzParam3GPP& param);

// ============================================================================
// 波形生成相关功能
// ============================================================================

/**
 * @brief 检查范围并设置波形生成值
 */
template <typename FieldType, typename MinType, typename MaxType>
int SetWavegenValueInRange(scpi_t* context, FieldType& field, MinType min, MaxType max) {
    return SetValueInRange(context, field, min, max);
}

/**
 * @brief 从指定值列表设置波形生成值
 */
template <typename FieldType>
int SetWavegenValueFromSpecified(scpi_t* context, FieldType& field,
                                std::initializer_list<FieldType> validValues) {
    return SetValueFromSpecified(context, field, validValues);
}

/**
 * @brief 设置蜂窝波形生成公共参数
 * @param demod 解调器ID
 * @param commonParam 公共参数
 */
void SetCellularWaveGenCommonParam(int demod, PNSetBaseType& commonParam);

/**
 * @brief 从3GPP回调获取VSG默认参数
 * @param standard 标准类型
 * @param linkDirect 链路方向
 * @param param 输出参数
 */
void GetVsgDefaultParamFrom3GPPCallback(const int& standard, const int& linkDirect, 
                                       Alg_3GPP_WaveGenType& param);

// ============================================================================
// 工具方法
// ============================================================================

/**
 * @brief 获取数组大小
 * @tparam T 数组元素类型
 * @tparam N 数组大小
 * @param 数组引用
 * @return 数组大小
 */
template <typename T, size_t N>
inline constexpr size_t arraySize(const T (&)[N]) noexcept {
    return N;
}

} // namespace cellular

#endif // SCPI_3GPP_COMMON_H_
