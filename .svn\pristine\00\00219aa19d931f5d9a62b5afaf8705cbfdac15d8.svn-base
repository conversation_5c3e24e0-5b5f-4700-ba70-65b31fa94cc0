//*****************************************************************************
//  File: wt_protocol.h
//  私有协议处理
//  Data: 2016.8.27
//****************************************************************************
#include "wt_protocol.h"
#include <new>
#include <cstring>
#include "wt-calibration.h"
#include "wtlog.h"
#include "wterror.h"
#include "business.h"
#include "connector.h"
#include "monitor.h"
#include "calconf.h"
#include "errorlib.h"
#include "subtaskmgr.h"
#include "sysmonitor.h"
#include "devlib.h"
#include "wtcal.h"
#include "digitallib.h"
#include "protocolsub.h"
#include "sncmanager.h"

#include "broadcastvsg.h"
#include "server_listmod_sequence.h"
using namespace std;
using namespace std::placeholders;

InterProt::InterProt() : WTProtocol()
{
    RegCmdPorc();
}

void InterProt::RegCmdPorc()
{
#define REG_CMD(code, fun) RegCmd(code, bind(&fun, this, _1, _2, _3))

    REG_CMD(CMD_SET_MON_OBJ, InterProt::SetMonObj);
    REG_CMD(CMD_SET_MON_PARAM, InterProt::SetMonData);
    REG_CMD(CMD_SET_CAL_FILE, InterProt::SetCalData);
    REG_CMD(CMD_GET_CAL_FILE, InterProt::GetCalData);
    REG_CMD(CMD_ADD_MIMO_DEV, InterProt::AddMimoDev);
    REG_CMD(CMD_DEL_MIMO_DEV, InterProt::DelMimoDev);
    REG_CMD(CMD_SET_CAL_PARAM, InterProt::SetCalParam);
    REG_CMD(CMD_GET_CAL_PARAM, InterProt::GetCalParam);
    REG_CMD(CMD_RELOAD_CAL_FILE, InterProt::ReloadCalFile);
    REG_CMD(CMD_CALC_IQ_IMB, InterProt::CalcIQImp);
    REG_CMD(CMD_SET_STATIC_IQ_IMB, InterProt::SetStaticIQImb);
    REG_CMD(CMD_CLR_STATIC_IQ_IMB, InterProt::ClrStaticIQImb);
    REG_CMD(CMD_SET_TEMP_CAL, InterProt::SetTempCal);
    REG_CMD(CMD_SET_FLATNESS_CAL, InterProt::SetFlatnessCal);
    REG_CMD(CMD_SET_SUB_NET_AUTO_NEG, InterProt::SetSubNetAutoNeg);
    REG_CMD(CMD_GET_SUB_NET_AUTO_NEG, InterProt::GetSubNetAutoNeg);

    // 自校准
    REG_CMD(CMD_START_IN_CAL, InterProt::StartInCal);
    REG_CMD(CMD_STOP_IN_CAL, InterProt::StopInCal);
    REG_CMD(CMD_QUERY_IN_CAL_PROCESS, InterProt::QueryInCalProcess);
    REG_CMD(CMD_SET_IN_CAL_CONFIG, InterProt::SetInCalConfig);

    //VSA
    REG_CMD(CMD_SET_VSA_PARAM, InterProt::SetVsaParam);
    REG_CMD(CMD_SET_VSA_ALZ_PARAM, InterProt::SetVsaAlzParam);
    REG_CMD(CMD_VSA_AUTO_RANGE, InterProt::VsaAutoRange);
    REG_CMD(CMD_START_VSA, InterProt::StartVsa);
    REG_CMD(CMD_PAUSE_VSA, InterProt::PauseVsa);
    REG_CMD(CMD_STOP_VSA, InterProt::StopVsa);
    REG_CMD(CMD_GET_VSA_STATUS, InterProt::GetVsaStatus);
    REG_CMD(CMD_EXT_VSA_FILE, InterProt::LoadSigFile);
    REG_CMD(CMD_VSA_ALZ, InterProt::Analyze);
    REG_CMD(CMD_GET_VSA_DATA, InterProt::GetAlzData);
    REG_CMD(CMD_GET_11AX_USER_VSA_DATA, InterProt::Get11axUserAlzData);
    REG_CMD(CMD_GET_SPEC_VSA_DATA, InterProt::GetAvgData);
    REG_CMD(CMD_GET_VSA_PARAM, InterProt::GetVsaParam);
    REG_CMD(CMD_RECORD_VSA, InterProt::VsaRecord);
    REG_CMD(CMD_GET_VSA_RECORD, InterProt::GetVsaRecordData);
    REG_CMD(CMD_GET_VSA_RAW_DATA, InterProt::GetVsaRawData);
    REG_CMD(CMD_SET_AVG_PARAM, InterProt::SetVsaAvgParam);
    REG_CMD(CMD_GET_VSA_ALZ_RESULT, InterProt::GetVsaAlzResult);
    REG_CMD(CMD_SAVE_SIGNAL, InterProt::SaveSignal);
    REG_CMD(CMD_GET_REF_RANGE, InterProt::GetRefLevelRange);
    REG_CMD(CMD_GET_CUR_AVG_CNT, InterProt::GetCurAvgCnt);
    REG_CMD(CMD_GET_VSA_CAL_PARAM, InterProt::GetCurVsaCalParam);
    REG_CMD(CMD_GET_CLR_AVG_DATA, InterProt::ClrAvgData);
    REG_CMD(CMD_GET_SPEC_VSA_DATA_COMPOSITE, InterProt::GetAvgDataComposite);
    REG_CMD(CMD_GET_VSA_GAIN_PARAM, InterProt::GetVsaGainParam);
    REG_CMD(CMD_SET_FREAM_FILTER, InterProt::SetVsaResultFilter);
    REG_CMD(CMD_SET_IS_WIDE_SPECT_ON, InterProt::Set240MSpectOnFlag);
    REG_CMD(CMD_SET_VSA_TRIG_PARAM, InterProt::SetVsaTrigParam);

    REG_CMD(CMD_SET_EXTEND_EVM_STATUS, InterProt::SetExtendEVMStatus);
    REG_CMD(CMD_SET_VSA_ITERATIVE_EVM_STATUS, InterProt::SetVsaIterativeEVMStatus);
    REG_CMD(CMD_SET_VSA_SNC_EVM_STATUS, InterProt::SetVsaSncEVMStatus);
    REG_CMD(CMD_SET_VSA_CC_EVM_STATUS, InterProt::SetVsaCcEVMStatus);
    REG_CMD(CMD_SET_VSA_FLATNESS_CAL_ENABLE, InterProt::SetVsaFlatnessCal);
    REG_CMD(CMD_GET_VSA_FLATNESS_CAL_ENABLE, InterProt::GetVsaFlatnessCal);
    REG_CMD(CMD_SET_VSA_IQIMB_CAL_ENABLE, InterProt::SetVsaIQImbCal);
    REG_CMD(CMD_GET_VSA_IQIMB_CAL_ENABLE, InterProt::GetVsaIQImbCal);
    REG_CMD(CMD_SET_VSG_STATIC_IQ_IMB_CAL, InterProt::SetVsgStaticIQImb);
    REG_CMD(CMD_CLR_VSG_STATIC_IQ_IMB_CAL, InterProt::ClrVsgStaticIQImb);
    REG_CMD(CMD_GET_VSA_SPECTRUM_POINT_POWER, InterProt::GetSpectrumPointPower);

    //Beamforming
    REG_CMD(CMD_BEAMFORMING_CLR_ALZ_DUTTX, InterProt::AnalyzeBeamformingCalibrationChannelEstDutTx);
    REG_CMD(CMD_BEAMFORMING_CLR_ALZ_DUTRX, InterProt::AnalyzeBeamformingCalibrationChannelEstDutRx);
    REG_CMD(CMD_BEAMFORMING_CLR_RST, InterProt::AnalyzeBeamformingCalibrationResult);
    REG_CMD(CMD_BEAMFORMING_CLR_VERIFICATION, InterProt::AnalyzeBeamformingVerification);
    REG_CMD(CMD_BEAMFORMING_CAL_PROFILE, InterProt::AnalyzeBeamformingCalculateChannelProfile);
    REG_CMD(CMD_BEAMFORMING_CAL_BCM_AMP_ANGLE, InterProt::AnalyzeBeamformingCalculateChannelAmplitudeAngleBCM);

    //Per
    REG_CMD(CMD_PER_ANALYZE, InterProt::AnalyzePerResult);

    //set Trigger Base Alz Param
    REG_CMD(CMD_SET_EXTRAL_ALZ_PARAM, InterProt::SetExtralAlzParam);
    REG_CMD(CMD_SET_ALZ_GROUP_BY_RESULT_STRING, InterProt::SetAnalyseGroupBaseResultStr);

    //VSG
    REG_CMD(CMD_SET_VSG_PARAM, InterProt::SetVsgParam);
    REG_CMD(CMD_TX_VSG_FILE, InterProt::VsgSigFile);
    REG_CMD(CMD_START_VSG, InterProt::StartVsg);
    REG_CMD(CMD_PAUSE_VSG, InterProt::PauseVsg);
    REG_CMD(CMD_STOP_VSG, InterProt::StopVsg);
    REG_CMD(CMD_GET_VSG_STATUS, InterProt::GetVsgStatus);
    REG_CMD(CMD_GET_VSG_PARAM, InterProt::GetVsgParam);
    REG_CMD(CMD_GEN_VSG_FILE, InterProt::GenerateSigFile);
    REG_CMD(CMD_RECORD_VSG, InterProt::VsgRecord);
    REG_CMD(CMD_GET_VSG_RECORD, InterProt::GetVsgRecordData);
    REG_CMD(CMD_SET_PN_PARAM, InterProt::SetPNCfg);
    REG_CMD(CMD_GET_PN, InterProt::GetPnCfg);
    REG_CMD(CMD_GET_GEN_PARAM, InterProt::GetDefaultGenParam);
    REG_CMD(CMD_GET_POWER_RANGE, InterProt::GetVsgPowerRange);
    REG_CMD(CMD_GET_VSG_GAIN_PARAM, InterProt::GetVsgGainParam);
    REG_CMD(CMD_SET_VSG_IFG_ENABLE, InterProt::SetVsgIfgEnable);
    REG_CMD(CMD_GET_VSG_IFG_ENABLE, InterProt::GetVsgIfgEnable);
    //REG_CMD(CMD_GEN_VSG_FILE_V2, InterProt::GenerateSigFileV2);
    REG_CMD(CMD_GEN_VSG_FILE_CW, InterProt::GenerateSigFileCW);
    REG_CMD(CMD_GEN_VSG_FILE_BLUETOOTH, InterProt::GenerateSigFileBlueTooth);
    REG_CMD(CMD_GET_GEN_FINAL_PARAM, InterProt::GetFinalParamAfterGenerate);
    REG_CMD(CMD_GEN_VSG_FILE_WIFI, InterProt::GenerateSigFileWifi);
    REG_CMD(CMD_SET_VSG_FLATNESS_CAL_ENABLE, InterProt::SetVsgFlatnessCal);
    REG_CMD(CMD_GET_VSG_FLATNESS_CAL_ENABLE, InterProt::GetVsgFlatnessCal);
    REG_CMD(CMD_SET_VSG_IQIMB_CAL_ENABLE, InterProt::SetVsgIQImbCal);
    REG_CMD(CMD_GET_VSG_IQIMB_CAL_ENABLE, InterProt::GetVsgIQImbCal);
    REG_CMD(CMD_SET_VSG_FEM_MODE, InterProt::SetVsgFemParam);
    REG_CMD(CMD_VSG_SEND_CNT, InterProt::GetVsgSendCnt);
    REG_CMD(CMD_GET_VSG_GEN_WIFI_PARAM, InterProt::GetFinalGenWaveWifiParamAfterGenerate);
    REG_CMD(CMD_GET_VSG_GEN_RETURN_DATA, InterProt::GetReturnDataAfterGenerate);
    REG_CMD(CMD_SET_VSG_OFDMA_RU_CARRIER_INFO, InterProt::SetRuCarrierInfo);
    REG_CMD(CMD_GEN_VSG_FILE_SLE, InterProt::GenerateSigFileSLE);
    REG_CMD(CMD_GEN_VSG_GEN_SLE_SYNSQR, InterProt::GenerateSigSLESynSeq);
    REG_CMD(CMD_SET_VSG_BROADCAST_ENABLE, InterProt::SetBroadcastEnable);
    REG_CMD(CMD_GET_VSG_BROADCAST_ENABLE, InterProt::GetBroadcastEnable);
    REG_CMD(CMD_SET_VSG_BROADCAST_DEBUG_ENABLE, InterProt::SetBroadcastDebugEnable);
    REG_CMD(CMD_GET_VSG_BROADCAST_RUN_STATUS, InterProt::GetBroadcastRunStatus);
    REG_CMD(CMD_GEN_VSG_FILE_WI_SUN, InterProt::GenerateSigFileWiSun);

    REG_CMD(CMD_GEN_VSG_FILE_3GPP, InterProt::GenerateSigFile3GPP);

    //TB
    REG_CMD(CMD_START_TBT_AP, InterProt::StartTBTAp);
    REG_CMD(CMD_STOP_TBT_AP, InterProt::StopTBTAp);
    REG_CMD(CMD_GET_TBT_AP_STATUS, InterProt::GetTBTApStatus);
    REG_CMD(CMD_VSA_TBT_AP_AUTO_RANGE, InterProt::VsaAutoRangeTBTAp);
    REG_CMD(CMD_START_TBT_STA, InterProt::StartTBTSta);
    REG_CMD(CMD_STOP_TBT_STA, InterProt::StopTBTSta);
    REG_CMD(CMD_GET_TBT_STA_STATUS, InterProt::GetTBTStaStatus);

    //硬件模式
    //硬件模式
    REG_CMD(CMD_DEV_RUN_MODE, InterProt::SetDevRunMode);
    REG_CMD(CMD_DIQ_PARAM, InterProt::SetDigParam);
    REG_CMD(CMD_DIQ_DUT_MODE, InterProt::SetDigDutMode);
    REG_CMD(CMD_SET_DEV_LO_MODE, InterProt::SetDevLOMode);
    REG_CMD(CMD_GET_DEV_LO_MODE, InterProt::GetDevLOMode);
    REG_CMD(CMD_SET_DEV_ANALOG_IQ_MODE, InterProt::SetDevAnalogIQMode);
    REG_CMD(CMD_GET_DEV_ANALOG_IQ_MODE, InterProt::GetDevAnalogIQMode);

    // 0x3xx
    REG_CMD(CMD_GET_TEMP, InterProt::GetDeviceTemperature);          // 设备温度查询
    REG_CMD(CMD_GET_FAN_SPEED, InterProt::GetFanSpeedParam);         // 风扇转速查询
    REG_CMD(CMD_SET_FAN_SPEED, InterProt::SetFanSpeedParam);         // 风扇转速配置
    REG_CMD(CMD_SET_COMPONENT_DATA, InterProt::SetComponentParam);   // 配置器件参数
    REG_CMD(CMD_GET_COMPONENT_DATA, InterProt::GetComponentParam);   // 查询器件参数
    REG_CMD(CMD_GET_HISTORY_TEMP, InterProt::GetHistoryTemperature); // 设备温度查询
    REG_CMD(CMD_GET_VOLT_INFO, InterProt::GetVoltInfo);              // 设备电压查询
    REG_CMD(CMD_SUB_CMD, InterProt::SubCmdHandle);                   // 子命令处理

    //0x4xx
    REG_CMD(CMD_CAL_START, InterProt::ATTCalStart);
    REG_CMD(CMD_CAL_STOP, InterProt::ATTCalStop);
    REG_CMD(CMD_SEND_METER_SETTING, InterProt::SendMeterSettingtoMons);     //meter调用下发当前的配置，然后转发给监视机
    REG_CMD(CMD_GET_LOG, InterProt::GetLog);                                //日志查询
    REG_CMD(CMD_GET_SAVE_LOG_FLAG_SETTING, InterProt::GetSaveLogFlagSetting);   //获取保存日志的标识
    REG_CMD(CMD_SET_SAVE_LOG_FLAG, InterProt::SetSaveLogFlag);                  //设置保存日志的标识
    REG_CMD(CMD_NOISE_CAL_START, InterProt::NoiseCalStart);                     // NOISE_CAL
    REG_CMD(CMD_NOISE_CAL_STOP, InterProt::NoiseCalStop);                       // NOISE_CAL
    REG_CMD(CMD_NOISE_CAL_STATUS, InterProt::GetNoiseCalStatus);                // NOISE_CAL
    REG_CMD(CMD_NOISE_CAL_VAILD, InterProt::GetNoiseCalValid);                  // NOISE_CAL

    //设备状态
    REG_CMD(CMD_GET_DEV_INFO, InterProt::GetDeviceInfo);                    //设备基本信息查询
    REG_CMD(CMD_GET_SUB_ETH, InterProt::GetSubnet);                         //获取子网口配置信息
    REG_CMD(CMD_GET_SUB_ETH_LINK, InterProt::GetSubnetLink);                //获取子网口link信息
    REG_CMD(CMD_GET_IP_ADDRESS_TYPE, InterProt::GetIpAddressType);          //获取主网口ip地址类型
    REG_CMD(CMD_SET_SUB_ETH, InterProt::SetSubnet);                         //子网口配置
    REG_CMD(CMD_GET_GUI_VERSION, InterProt::GetGUIVersion);                 //获取GUI文件版本
    REG_CMD(CMD_GET_GUI, InterProt::GetGUIFile);                            //GUI界面文件获取
    REG_CMD(CMD_GET_LIC, InterProt::GetLicenseInfo);                        //License查询
    REG_CMD(CMD_GET_CUR_DEV_RES_CONF, InterProt::GetCurSubDeviceCfg);       //获取当前server/子仪器的资源划分情况信息
    REG_CMD(CMD_GET_HARD_ERR_INFO, InterProt::GetHardErrInfo);              //获取硬件错误信息
    REG_CMD(CMD_GET_DEV_VER_INFO, InterProt::GetDeviceVersionInfo);         //设备各种版本信息查询

    //信号文件管理
    REG_CMD(CMD_SIG_FILE_TX, InterProt::VsgSigFile);                       //信号文件下发,SendSigFile
    REG_CMD(CMD_SIG_FILE_EXIST, InterProt::SigFileExist);                   //信号文件是否存在
    REG_CMD(CMD_DEL_SIG_FILE, InterProt::DelSigFile);                       //删除信号文件
    REG_CMD(CMD_GET_SIG_FILE_LIST, InterProt::GetSigFileList);              //获取信号文件列表
    REG_CMD(CMD_GET_SIG_FILE, InterProt::GetSigFile);                       //获取信号文件

    //下发随机文件
    REG_CMD(CMD_SET_CUSTOMIZE_FILE, InterProt::SendCustomizeFile);          //文件下发
    REG_CMD(CMD_GET_CUSTOMIZE_FILE, InterProt::GetCustomizeFile);           //获取文件

    //执行SHELL命令
    REG_CMD(CMD_SHELL_EXECUTE, InterProt::ShellExecute);                    //shell命令执行并返回结果

    //线衰文件操作
    REG_CMD(CMD_SET_PATH_LOSS_FILE, InterProt::SetPathLossFile);
    REG_CMD(CMD_GET_PATH_LOSS_FILE, InterProt::GetPathLossFile);

    REG_CMD(CMD_GET_MONITOR_LIST, InterProt::GetMonitorsInfo);              //获取监视机的信息
    REG_CMD(CMD_SHUT_DOWN_DEV, InterProt::ShutDownDevice);                  //关机
    REG_CMD(CMD_DELETE_SUB_NET_SETTING, InterProt::DelSubNetSetting);       //删除子网口信息

    REG_CMD(CMD_STOP_SLAVE, InterProt::SetStopSlave);                           //停止从机，仅固件内部使用
    REG_CMD(CMD_SEND_USER_CONN_INFO, InterProt::SendUserConnInfo);              //下发连接发起端的本地ip和port信息
    REG_CMD(CMD_GET_SLAVE_240M_THREE_RAW_DATA, InterProt::GetThreeVsaRawData);  //正负240M频谱时，获取从机三次采集的数据
    REG_CMD(CMD_SEND_FILE_IN_MIMO, InterProt::SaveVsgFileMastertoSlave);        //mimo下主机给从机发送信号文件

    REG_CMD(CMD_GET_SLAVE_LIC_INFO, InterProt::GetSlaveLicInfo);                //从机license的查询
    REG_CMD(CMD_TEST_CONNECT_STATUS, InterProt::TestConnectStatus);           //确认连接状态


    //listmod
    REG_CMD(CMD_SET_LIST_ENABLE, InterProt::SetlistEnable);                   //使能listmod
    REG_CMD(CMD_SET_LIST_DISABLE, InterProt::SetlistDisable);
    REG_CMD(CMD_SET_LIST_SEG_VSA_ALZ_PARAM, InterProt::SetlistSegVsaAlzParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_ALLALZ_COMMON_PARAM, InterProt::SetlistSegVsaAllAlzComParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_ALLALZ_PRO_PARAM, InterProt::SetlistSegVsaAllAlzProParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_CAP_PARAM, InterProt::SetlistSegVsaCapParam);
    REG_CMD(CMD_SET_LIST_SEG_ALLVSA_CAP_PARAM, InterProt::SetlistSegAllVsaCapParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_TRIG_PARAM, InterProt::SetlistSegVsaTrigParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_ALLTRIG_PARAM, InterProt::SetlistSegAllVsaTrigParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_TRIG_COMMON_PARAM, InterProt::SetlistSegTrigCommParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_ALLTRIG_COMMON_PARAM, InterProt::SetlistSegAllTrigCommParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_TIME_PARAM, InterProt::SetlistSegTimeParam);
    REG_CMD(CMD_SET_LIST_SEG_ALL_TIME_PARAM, InterProt::SetlistSegAllTimeParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_START_SEQ, InterProt::SetlistSegVsaSeqStart);
    REG_CMD(CMD_SET_LIST_SEG_VSG_START_SEQ, InterProt::SetlistSegVsgSeqStart);
    REG_CMD(CMD_SET_LIST_SEG_VSAVSG_START_SEQ, InterProt::SetlistSegVsaVsgSeqStart);
    REG_CMD(CMD_SET_LIST_SEG_VSG_PARAM, InterProt::SetlistSegVsgParam);
    REG_CMD(CMD_SET_LIST_SEG_ALLVSG_PARAM, InterProt::SetlistSegAllVsgParam);
    REG_CMD(CMD_SET_LIST_SEG_VSG_SYNC_PARAM, InterProt::SetlistSegVsgSyncParam);
    REG_CMD(CMD_SET_LIST_SEG_VSG_ALLSYNC_PARAM, InterProt::SetlistSegVsgAllSyncParam);
    REG_CMD(CMD_SET_LIST_SEG_VSG_WAVE_PARAM, InterProt::SetlistSegVsgWaveParam);
    REG_CMD(CMD_SET_LIST_SEG_VSG_ALLWAVE_PARAM, InterProt::SetlistSegAllVsgWaveParam);
    REG_CMD(CMD_SET_LIST_SEG_VSA_STOP_SEQ, InterProt::SetlistSegVsaSeqStop);
    REG_CMD(CMD_SET_LIST_SEG_VSG_STOP_SEQ, InterProt::SetlistSegVsgSeqStop);
    REG_CMD(CMD_SET_LIST_SEG_VSAVSG_STOP_SEQ, InterProt::SetlistSegVsaVsgSeqStart);
    REG_CMD(CMD_GET_LIST_SEQ_VSAVSG_STATE, InterProt::GetlistSegVsaVsgSeqState);
    REG_CMD(CMD_SET_LIST_SEG_VSA_CAP_STATE, InterProt::GetListTxSeqAllCapState);
    REG_CMD(CMD_SET_LIST_SEG_VSA_ANNALY_STATE, InterProt::GetListTxSeqAllAnalyState);
    REG_CMD(CMD_GET_LIST_SEQ_VSG_TRANS_STATE, InterProt::GetListRxSeqAllTransState);
    REG_CMD(CMD_SET_LIST_SEG_VSA_POWER_RESULT, InterProt::GetListTxSeqAllPowerResult);
    REG_CMD(CMD_SET_LIST_SEG_VSA_CLEAR, InterProt::SetListTxSeqVsaClear);
    REG_CMD(CMD_SET_LIST_SEG_VSA_LTE_TX_SEG_STAT, InterProt::GetListLteTxSeqAllSegStat);
    REG_CMD(CMD_DUPLEX_SET_STATE, InterProt::DuplexSetState);
    REG_CMD(CMD_DUPLEX_GET_STATE, InterProt::DuplexGetState);
    REG_CMD(CMD_SET_DUPLEX_NOISE_FLAG, InterProt::SetVsaDuplexNoiseFlag);
    REG_CMD(CMD_GET_DUPLEX_NOISE_FLAG, InterProt::GetVsaDuplexNoiseFlag);
   REG_CMD(CMD_GET_LIST_ALL_VSA_SEG_DATA, InterProt::GetListAllAlzData);

}

int InterProt::Response(WRSocket &Sock, const ReqHeader *Header, int Result)
{
    int Len;
    ReqHeader Req;

    //Header为空表示之前收到的命令无法识别，需要此处自己构造响应
    if (Header == nullptr)
    {
        Req.Code = 0xFFFFL;
        Header = &Req;
    }

    AckHeader Ack(Header, sizeof(Result), Result);

    Result = Sock.Send(&Ack, sizeof(AckHeader), Len);
    if (Result == WT_OK)
    {
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(Result, "socket error or closed");
        return WT_SOCKET_CLOSED;
    }
}

int InterProt::Response(WRSocket &Sock, const ReqHeader *Header, const void *Data, int Len)
{
    AckHeader Ack(Header, sizeof(int) + Len, WT_OK);

    BufInfo Vec[2] = {{&Ack, sizeof(Ack)}, {const_cast<void *>(Data), Len}};
    int Ret = Sock.Send(Vec, 2, Len);
    if (Ret == WT_OK)
    {
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        return WT_SOCKET_CLOSED;
    }
}

int InterProt::GetVsaData(WRSocket &Sock)
{
    int Len = 0;
    char Buf[sizeof(CmdHeader) + 4];
    CmdHeader *Cmd = new (Buf) CmdHeader;
    Cmd->Type = TYPE_CMD;
    Cmd->Code = CMD_GET_VSA_RAW_DATA;
    Cmd->Length = sizeof(int);
    *(int *)(Buf + sizeof(CmdHeader)) = 0;

    return Sock.Send(Buf, sizeof(Buf), Len);
}

int InterProt::StartSlaveVsa(WRSocket &Sock)
{
    int Len = 0;
    CmdHeader Cmd;
    Cmd.Code = CMD_START_VSA;
    Cmd.Length = 0;

    return Sock.Send(&Cmd, sizeof(Cmd), Len);
}

int InterProt::StopSlaveMod(WRSocket &Sock, int ModType ,int Code)
{
    int tmp = 0;

    CmdHeader Cmd;
    Cmd.Code = CMD_STOP_SLAVE;
    Cmd.Length = sizeof(int) * 2;

    BufInfo Vec[3] = {{&Cmd,sizeof(CmdHeader)}, {&ModType,sizeof(int)}, {&Code,sizeof(int)}};
    return Sock.Send(Vec, 3, tmp);
}

int InterProt::SetVsgSlaveIfg(WRSocket &Sock, int Enable)
{
    int tmp = 0;
    CmdHeader Cmd;
    Cmd.Code = CMD_SET_VSG_IFG_ENABLE;
    Cmd.Length = sizeof(int);

    BufInfo Vec[2] = { { &Cmd,sizeof(CmdHeader) },{ &Enable,sizeof(int) } };
    return Sock.Send(Vec, 2, tmp);
}


int InterProt::SendConnInfoToSalve(WRSocket &Sock, const void *Buf, int Len)
{
    CmdHeader Header;
    Header.Code = CMD_SEND_USER_CONN_INFO;
    Header.Length = Len;

    BufInfo Vec[2] = { { &Header,sizeof(CmdHeader) },{const_cast<void *>(Buf),Header.Length } };
    return Sock.Send(Vec, 2, Len);
}

int InterProt::TransmitCmd(WRSocket &Sock, const ReqHeader *Header)
{
    int TxLen = 0;
    const CmdHeader *Cmd = static_cast<const CmdHeader *>(Header);

    return Sock.Send(Header, Cmd->Length + sizeof(CmdHeader), TxLen);
}

int InterProt::SendModParam(WRSocket &Sock, int ModType, const void *Param, int Len)
{
    CmdHeader Header;
    Header.Code = ModType == DEV_TYPE_VSA ? CMD_SET_VSA_PARAM : CMD_SET_VSG_PARAM;
    Header.Length = sizeof(int) + Len;

    int tmp;
    int ParamNum = 1;

    BufInfo Vec[3] = {{&Header, sizeof(Header)}, {&ParamNum, sizeof(int)}, {const_cast<void *>(Param), Len}};
    return Sock.Send(Vec, 3, tmp);
}

int InterProt::SendAutoRangeResult(WRSocket &Sock, const ReqHeader *Header, int DevNum, const void *Data, int Len)
{
    AckHeader Ack(Header, sizeof(int) * 2 + Len, WT_OK);
    BufInfo Vec[3] = {{&Ack, sizeof(Ack)}, {&DevNum, sizeof(int)}, {const_cast<void *>(Data), Len}};

    int Ret = Sock.Send(Vec, 3, Len);
    if (Ret == WT_OK)
    {
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        return WT_SOCKET_CLOSED;
    }
}

int InterProt::SendVsaResult(WRSocket &Sock, AckHeader *Header, const vector<VsaResult> &Result)
{
    int TxLen = 0;
    int Ret = WT_OK;

    Ret = Sock.Send(Header, sizeof(*Header), TxLen);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    for (const auto &Item : Result)
    {
        Ret = Sock.Send(&Item, sizeof(int) * 2, TxLen);
        if (Ret != WT_OK)
        {
            return Ret;
        }

        if (Item.Data != NULL && Item.DataLen) //若数据不为空，才发送数据
        {
            Ret = Sock.Send(Item.Data, Item.DataLen, TxLen);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }
    }

    return WT_OK;
}

int InterProt::AddMimoDev(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < 24)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Chain = *static_cast<int *>(Data);
    char *IP = static_cast<char *>(Data) + sizeof(int);
    int SubId = *reinterpret_cast<int *>(IP + 16);
    int Ret = Bsn->ConnectMimoDev(Chain, IP, SubId);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::DelMimoDev(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Chain = *static_cast<int *>(Data);
    int Ret = Bsn->DisconnMimoDev(Chain);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetVsaParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int ParamNum = 1;
    if (Header->Type == TYPE_CMD)
    {
        ParamNum = *static_cast<int *>(Data);
        if (Header->Length < (signed)sizeof(int) + ParamNum * (signed)sizeof(VsaParam)) //lint !e84
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }

    int Ret = Bsn->GetVsa().SetParam(Conn, ParamNum, static_cast<int *>(Data) + 1);
    if(Ret == WT_OK)
    {
        Bsn->GetVsa().BackUpVsaParam();  //配置完成时备份一下采集参数，主要为500M恢复参数使用
    }

    if(Bsn->GetVsa().IsNeedClearAvgData())
    {
        Bsn->GetVsa().ClearAvgData();   //通过协议配置vsa参数时，强制清空平均数据
    }

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::SetVsaAlzParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Demode = *static_cast<int *>(Data);
     
    int Ret = Bsn->GetVsa().SetAlzParam(Demode, static_cast<int *>(Data) + 1, Header->Length - sizeof(int));

    if(Bsn->GetVsa().IsNeedClearAvgData())
    {
        Bsn->GetVsa().ClearAvgData(true);   //通过协议配置vsa分析参数时，强制清空平均数据
    }
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetVsaAlzParam(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    std::unique_ptr<char[]> AlzParam;
    int Len = 0;

    int Demode = Bsn->GetVsa().GetCurExtDemode();

    int Ret = Bsn->GetVsa().GetAlzParamByDemode(Demode, AlzParam, Len);

    if(Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    return Response(Conn->GetSock(), Header, AlzParam.get(), Len);
}

int InterProt::VsaAutoRange(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    if (!DevLib::Instance().GetFullDuplexEnable())
    {
        Ret = Bsn->GetVsa().AutoRange(Conn, Data);
    }
    else
    {
        Ret = Bsn->GetVsa().DuplexAutoRange(Conn, Data);
    }

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StartVsa(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Bsn->GetVsa().Clear500MCapData();//api 重新下发start vsa时清空下500M采集数据

    int Ret = WT_OK;
    if (!DevLib::Instance().GetFullDuplexEnable())
    {
        Ret = Bsn->GetVsa().Start(Conn);
    }
    else
    {
        Ret = Bsn->GetVsa().DuplexVsaStart(Conn);
    }

    Bsn->GetVsa().BackUpVsaParam();

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::PauseVsa(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().Pause(Conn);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StopVsa(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    if (!DevLib::Instance().GetFullDuplexEnable())
    {
        Ret = Bsn->GetVsa().Stop(Conn);
    }
    else
    {
        Ret = Bsn->GetVsa().DuplexVsaStop(Conn);
    }

    //stop时清空500M的采集等待超时和采集数据
    if(Ret == WT_OK)
    {
        Bsn->GetVsa().Clear500MWaitRigger();
    }

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::ClrAvgData(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsa().ClearAvgData();

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetVsaStatus(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int) * 2)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    //从数据中提取结果值
    int Status = *(static_cast<int *>(Data));
    int Ret = Bsn->GetVsa().QueryStatus(Conn, Status);

    if (Conn->NeedRsp())
    {
        return Ret == WT_OK ? Response(Conn->GetSock(), Header, &Status, sizeof(int))
               : Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::LoadSigFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length <= MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int NameLen = strlen((char *)Data);
    if (NameLen == 0 || NameLen > MAX_NAME_SIZE)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "filename length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Len = Header->Length - MAX_NAME_SIZE;
    int Ret = Bsn->GetVsa().LoadSigFile((char *)Data, (char *)Data + MAX_NAME_SIZE, Len);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::Analyze(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    string SigFile;

    if (Header->Length > MAX_NAME_SIZE + (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "sigfile name > MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int FrameId = *static_cast<int *>(Data);

    //长度大于4表示指定了信号文件名
    if (Header->Length > (signed)sizeof(int))
    {
        SigFile = static_cast<char *>(Data) + sizeof(int);
    }

#if TIME_DEBUG
    auto startTime = std::chrono::high_resolution_clock::now();
#endif
    int Ret = Bsn->GetVsa().Analyze(FrameId, SigFile);
#if TIME_DEBUG
    auto endTime = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::micro> time_us = endTime - startTime;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", ALG Analyze Used Time:" << time_us.count() << "us" << std::endl;
#endif
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetAlzData(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < VSA_RESULT_NAME_LEN + (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Stream = *(int *)Data;
    int Segment = *((int *)Data + 1);
    int SegNo = *((int *)Data + 2);

    if ((!Bsn->GetVsa().GetCmimoMode() && Stream >= MAX_NUM_OF_CHNNEL) ||
        (Bsn->GetVsa().GetCmimoMode() && Stream >= CMIMO_MAX_STREAM) ||
        Segment > 2)
    {
        WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "GetAlzData Stream or segment id error");
        return Response(Conn->GetSock(), Header, WT_STREAM_ID_ERROR);
    }

    vector<VsaResult> Result;
    void *Buf = nullptr;
    int Size;
    int DataType;
    int Total = 0;

    int Cnt = (Header->Length - 3 * sizeof(int)) / VSA_RESULT_NAME_LEN; //结果项数量

    //从命令数据中提取分析项并获取结果
    for (int i = 0; i < Cnt; i++)
    {
        string AlzType = (char *)Data + sizeof(int) * 3 + VSA_RESULT_NAME_LEN * i;
        Ret = Bsn->GetVsa().GetAlzData(AlzType, Stream, Segment, SegNo, &Buf, Size, DataType);
        if (Ret != WT_OK)
        {
            return Response(Conn->GetSock(), Header, Ret);
        }

        Total += sizeof(int) * 2 + Size;
        Result.push_back({Size, DataType, Buf});
    }

    AckHeader Ack(Header, sizeof(int) + Total, WT_OK);
    Ret = SendVsaResult(Conn->GetSock(), &Ack, Result);
    Bsn->GetVsa().FreeAlzData();

    return Ret;
}

int InterProt::Get11axUserAlzData(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < VSA_RESULT_NAME_LEN + (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Stream = *(int *)Data;
    int Segment = *((int *)Data + 1);
    if ((!Bsn->GetVsa().GetCmimoMode() && Stream >= MAX_NUM_OF_CHNNEL) ||
        (Bsn->GetVsa().GetCmimoMode() && Stream >= CMIMO_MAX_STREAM) ||
        Segment > 2)
    {
        WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "GetAlzData Stream or segment id error");
        return Response(Conn->GetSock(), Header, WT_STREAM_ID_ERROR);
    }

    int UserID = *((int *)Data + 2);

    vector<VsaResult> Result;
    void *Buf;
    int Size;
    int DataType;
    int Total = 0;

    int Cnt = (Header->Length - sizeof(int)*3) / VSA_RESULT_NAME_LEN; //结果项数量

    //从命令数据中提取分析项并获取结果
    for (int i = 0; i < Cnt; i++)
    {
        string AlzType = (char *)Data + sizeof(int) * 3 + VSA_RESULT_NAME_LEN * i;
        Ret = Bsn->GetVsa().Get11axUserAlzData(UserID, AlzType, Stream, Segment, &Buf, Size, DataType);
        if (Ret != WT_OK)
        {
            return Response(Conn->GetSock(), Header, Ret);
        }

        Total += sizeof(int) * 2 + Size;
        Result.push_back({Size, DataType, Buf});
    }

    AckHeader Ack(Header, sizeof(int) + Total, WT_OK);
    Ret = SendVsaResult(Conn->GetSock(), &Ack, Result);
    Bsn->GetVsa().FreeAlzData();

    return Ret;
}

int InterProt::GetVsaParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if (Header->Type == TYPE_CMD)
    {
        void *Result = nullptr;
        int Len = 0;

        Ret = Bsn->GetVsa().GetParam(Conn, &Result, Len);
        if (Ret == WT_OK && Conn->NeedRsp())
        {
            Ret = Response(Conn->GetSock(), Header, Result, Len);
        }
        else if (Ret != WT_OK)
        {
            Ret = Response(Conn->GetSock(), Header, Ret);
        }
    }
    else
    {
        Ret = Bsn->GetVsa().GetParam(Conn, &Data, Header->Length);
    }

    return Ret;
}

int InterProt::VsaRecord(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().StartRecord(*static_cast<int *>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetVsaRecordData(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    void *Record = nullptr;
    int Len = 0;
    int Ret = Bsn->GetVsa().GetRecordData(&Record, Len);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Record, Len) : Response(Conn->GetSock(), Header, Ret);
}

//lint -e429
int InterProt::GetVsaRawData(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (((Header->Type == TYPE_CMD) && Header->Length < (signed)sizeof(int))
            || ((Header->Type == TYPE_ACK) && Header->Length < (signed)sizeof(int)))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    BufInfo CalData[2], CapData[2];
    int SegNum = 0;

    if (Header->Type == TYPE_CMD)
    {
        int Stream = *static_cast<int *>(Data);
        int Ret = Bsn->GetVsa().GetRawData(Stream, SegNum, CalData, CapData);
        WTLog::Instance().WriteLog(LOG_DEBUG, "GetVsaRawData Stream = %d, SegNum = %d, Ret = %d\n", Stream, SegNum, Ret);
        if (Ret == WT_OK)
        {
            char Buf[128];
            int Len = sizeof(int) * 2;  //result and segnum

            for(int Seg = 0; Seg < SegNum; Seg++)
            {
                Len += sizeof(int) * 2;  //caldata len and capdata len
                Len += CalData[Seg].Len;
                Len += CapData[Seg].Len;
            }
            AckHeader *Ack = new (Buf) AckHeader(Header, Len, WT_OK);
            *(int *)(Buf + sizeof(AckHeader)) = SegNum;

            int Ret = Conn->GetSock().Send(Ack, sizeof(AckHeader) + sizeof(int), Len);

            if(Ret != WT_OK)
            {
                return Ret;
            }
            for(int Seg = 0; Seg < SegNum; Seg++)
            {
                if(CalData[Seg].Data != nullptr && CapData[Seg].Data != nullptr)
                {
                    BufInfo Vec[4] = {{&(CalData[Seg].Len), sizeof(int)}, {&(CapData[Seg].Len), sizeof(int)}, CalData[Seg], CapData[Seg]};
                    Ret = Conn->GetSock().Send(Vec, 4, Len);
                    if(Ret != WT_OK)
                    {
                        return Ret;
                    }
                }
            }
            return WT_OK;
        }
        else
        {
            return Response(Conn->GetSock(), Header, Ret);
        }
    }
    else
    {
        int Result = static_cast<AckHeader *>(Header)->Result;
        if (Result == WT_OK)
        {
            SegNum = *(int *)Data;
            CalData[0].Len = *(int *)((char *)Data + sizeof(int));
            CalData[0].Data = (char *)Data + sizeof(int) * 3;

            CapData[0].Len = *(int *)((char *)Data + sizeof(int) * 2);
            CapData[0].Data = (char *)Data + CalData[0].Len + sizeof(int) * 3;
            for(int Seg = 1; Seg < SegNum; Seg++)
            {
                CalData[Seg].Len = *(int *)((char *)(CapData[Seg - 1].Data) + CapData[Seg - 1].Len);
                CalData[Seg].Data = (char *)(CapData[Seg - 1].Data) + CapData[Seg - 1].Len + sizeof(int) * 2;

                CapData[Seg].Len = *(int *)((char *)(CapData[Seg - 1].Data) + CapData[Seg - 1].Len + sizeof(int));
                CapData[Seg].Data = (char *)(CapData[Seg - 1].Data) + CapData[Seg - 1].Len + sizeof(int) * 2 + CalData[Seg].Len;
            }
        }

        Bsn->GetVsa().RecvRawData(Conn, Result, SegNum, CalData, CapData);
        return WT_OK;
    }
}

int InterProt::SetVsaAvgParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int) * 3)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int AvgType = *static_cast<int *>(Data);
    int AvgMode = *(static_cast<int *>(Data) + 1);
    int AvgNum = *(static_cast<int *>(Data) + 2);

    int Ret = Bsn->GetVsa().SetAvgParam(AvgType, AvgMode, AvgNum);
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetAvgData(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int) * 3)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    BufInfo Result[4];
    for (int i = 0; i < 4; i++)
    {
        Result[i].Data = nullptr;
        Result[i].Len = 0;
    }

    int Len = 0;
    int Cnt = 2;
    int Idx = *static_cast<int *>(Data);
    int Stream = *(static_cast<int *>(Data) + 1);
    int Segment = *(static_cast<int *>(Data) + 2);

    int Ret = Bsn->GetVsa().GetAvgData(Idx, Stream, Segment, &Result[1]);
    if (Ret == WT_OK)
    {
        //要么只有一个数据，要么是三个平均数据
        Len = Result[1].Len;
        if (Result[2].Data != nullptr)
        {
            Cnt = 4;
            Len = Len + Result[2].Len + Result[3].Len;
        }

        AckHeader Ack(Header, sizeof(int) + Len, WT_OK);
        Result[0].Data = &Ack;
        Result[0].Len = sizeof(Ack);

        Ret = Conn->GetSock().Send(Result, Cnt, Len);
        Bsn->GetVsa().FreeAlzData();
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetAvgDataComposite(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    BufInfo Result[2];
    for (int i = 0; i < 2; i++)
    {
        Result[i].Data = nullptr;
        Result[i].Len = 0;
    }

    int Len = 0;
    int Cnt = 2;
    int Segment = *(static_cast<int *>(Data) + 1);

    int Ret = Bsn->GetVsa().GetAvgDataComposite(Segment, &Result[1]);
    if (Ret == WT_OK)
    {
        //要么只有一个数据，要么是三个平均数据
        Len = Result[1].Len;
        AckHeader Ack(Header, sizeof(int) + Len, WT_OK);
        Result[0].Data = &Ack;
        Result[0].Len = sizeof(Ack);

        Ret = Conn->GetSock().Send(Result, Cnt, Len);
        Bsn->GetVsa().FreeAlzData();
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetVsaGainParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Ret = WT_OK;
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Rx_Gain_Parm VSAGainParm;
    Ret = DevLib::Instance().VSAGetGainParameter(*static_cast<int *>(Data), VSAGainParm);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, &VSAGainParm, sizeof(Rx_Gain_Parm))
        : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetVsaAlzResult(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < VSA_RESULT_NAME_LEN + (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Stream = *(int *)Data;
    int Segment = *((int *)Data + 1);
    int SegNo = *((int *)Data + 2);

    if ((!Bsn->GetVsa().GetCmimoMode() && Stream >= MAX_NUM_OF_CHNNEL) ||
        (Bsn->GetVsa().GetCmimoMode() && Stream >= CMIMO_MAX_STREAM) ||
        Segment > 2)
    {
        WTLog::Instance().LOGERR(WT_STREAM_ID_ERROR, "GetAlzData Stream or segment id error");
        return Response(Conn->GetSock(), Header, WT_STREAM_ID_ERROR);
    }

    vector<VsaResult> Result;
    void *Buf;
    int Size;
    int DataType;
    int Total = 0;

    int Cnt = (Header->Length - 3 * sizeof(int)) / VSA_RESULT_NAME_LEN; //结果项数量

    //从命令数据中提取分析项并获取结果
    for (int i = 0; i < Cnt; i++)
    {
        string AlzType = (char *)Data + sizeof(int)* 3 + VSA_RESULT_NAME_LEN * i;
        Ret = Bsn->GetVsa().GetAlzData(AlzType, Stream, Segment, SegNo, &Buf, Size, DataType);
        if (Ret != WT_OK)
        {
            return Response(Conn->GetSock(), Header, Ret);
        }

        Total += sizeof(int)* 2 ;
        Result.push_back({ Size, DataType, NULL });
    }

    AckHeader Ack(Header, sizeof(int)+Total, WT_OK);
    Ret = SendVsaResult(Conn->GetSock(), &Ack, Result);
    Bsn->GetVsa().FreeAlzData();

    return Ret;
}

int InterProt::SaveSignal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE + (signed)sizeof(int)))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int DataType = *static_cast<char *>(Data);
    string FileName(static_cast<char *>(Data) + sizeof(int));

    int Ret = Bsn->GetVsa().SaveSignal(DataType, FileName);
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetRefLevelRange(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE + (signed)sizeof(int) + (signed)sizeof(double)))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Type = *(int *)Data;
    double Freq = *(double *)((char *)Data + sizeof(int));
    double Range[2];

    int Ret = Bsn->GetVsa().GetRefLevelRange(Type, Freq, Range[0], Range[1]);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Range, sizeof(Range))
           : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetCurAvgCnt(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Cnt = 0;
    int Ret = Bsn->GetVsa().GetCurAvgCnt(Cnt);
    return Ret == WT_OK ? Response(Conn->GetSock(), Header, &Cnt, sizeof(Cnt))
           : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetCurVsaCalParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_CMD) && Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    BufInfo CalData[2];
    Rx_Parm CalParm[2];
    CalData[0].Data = CalParm;
    CalData[1].Data = CalParm + 1;
    CalData[0].Len = CalData[1].Len = sizeof(Rx_Parm);

    int SegNum = 0;
    int Stream = *static_cast<int *>(Data);
    int Ret = Bsn->GetVsa().GetVsaCalParam(Stream, SegNum, CalParm);
    if (Ret == WT_OK)
    {
        char Buf[128];
        int Len = sizeof(int) * 2;  //result and segnum

        for(int Seg = 0; Seg < SegNum; Seg++)
        {
            Len += sizeof(int);  //caldata len
            Len += CalData[Seg].Len;
        }

        AckHeader *Ack = new (Buf) AckHeader(Header, Len, WT_OK);
        *(int *)(Buf + sizeof(AckHeader)) = SegNum;

        int Ret = Conn->GetSock().Send(Ack, sizeof(AckHeader) + sizeof(int), Len);

        if(Ret != WT_OK)
        {
            return Ret;
        }
        for(int Seg = 0; Seg < SegNum; Seg++)
        {
            if(CalData[Seg].Data != nullptr)
            {
                BufInfo Vec[2] = {{&(CalData[Seg].Len), sizeof(int)}, CalData[Seg]};
                Ret = Conn->GetSock().Send(Vec, 2, Len);
                if(Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
        return WT_OK;
    }
    else
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
}

int InterProt::SetVsgParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int ParamNum = 1;
    if (Header->Type == TYPE_CMD)
    {
        ParamNum = *static_cast<int *>(Data);
        if (Header->Length < (signed)sizeof(int) + ParamNum * (signed)sizeof(VsgParam)) // lint !e84
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }

    int Ret = Bsn->GetVsg().SetParam(Conn, ParamNum, static_cast<int *>(Data) + 1);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::VsgSigFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Len = 0;

    if (Header->Type == TYPE_CMD)
    {
        if (Header->Length < MAX_NAME_SIZE)
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }

        int NameLen = strlen((char *)Data);
        if (NameLen == 0 || NameLen > MAX_NAME_SIZE)
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "filename length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }

        Len = Header->Length - MAX_NAME_SIZE;

        if (SysMonitor::Instance().IsDisableSaveSignal() == true)
        {
            WTLog::Instance().LOGERR(WT_DISK_CAPACITY_ERROR, "Disk capacity is too less");
            return Response(Conn->GetSock(), Header, WT_DISK_CAPACITY_ERROR);
        }
    }
    else
    {
        if (Header->Length < (signed)sizeof(int))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < int");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }

    int Ret = WT_OK;
    if (0 == Len && MAX_NAME_SIZE == Header->Length)
    {
        std::unique_ptr<ReadFile> File;
        std::string filePath = GetLowWaveDir() + std::string((char *)Data);
        File.reset(new (std::nothrow) ReadFile(filePath));
        if (nullptr == File || nullptr == File->GetFileBuf())
        {
            File.reset(nullptr);
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Cannot open Wave file!");
            Ret = WT_OPEN_FILE_FAILED;
        }
        else
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get " << filePath << " OK, file size = " << File->GetFileSize() << std::endl;
            Ret = Bsn->GetVsg().ExtSigFile(Conn, (char *)Data, (char *)File->GetFileBuf(), File->GetFileSize());
        }
    }
    else
    {
        Ret = Bsn->GetVsg().ExtSigFile(Conn, (char *)Data, (char *)Data + MAX_NAME_SIZE, Len);
    }
//#if TIME_DEBUG
//    struct timeval tpstart, tpend;
//    gettimeofday(&tpstart, NULL);
//#endif
//    int Ret = Bsn->GetVsg().ExtSigFile(Conn, (char *)Data, (char *)Data + MAX_NAME_SIZE, Len);
//#if TIME_DEBUG
//    gettimeofday(&tpend, NULL);
//    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##Vsg ExtSigFile Used Time:" << timeuse << "us" << std::endl;
//#endif
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StartVsg(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

//#if TIME_DEBUG
//    struct timeval tpstart, tpend;
//    gettimeofday(&tpstart, NULL);
//#endif
    int Ret = WT_OK;
    if (!DevLib::Instance().GetFullDuplexEnable())
    {
        Ret = Bsn->GetVsg().Start(Conn);
    }
    else
    {
        Ret = Bsn->GetVsa().DuplexVsgStart(Conn);
    }
//#if TIME_DEBUG
//    gettimeofday(&tpend, NULL);
//    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##Vsg start Used Time:" << timeuse << "us" << std::endl;
//#endif
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::PauseVsg(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsg().Pause(Conn);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StopVsg(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    if (!DevLib::Instance().GetFullDuplexEnable())
    {
        Ret = Bsn->GetVsg().Stop(Conn);
    }
    else
    {
        Ret = Bsn->GetVsa().DuplexVsgStop(Conn);
    }
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetVsgStatus(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int) * 2)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    //从数据中提取结果值
    int Status = *(static_cast<int *>(Data));
    int Ret = Bsn->GetVsg().QueryStatus(Conn, Status);

    if (Conn->NeedRsp())
    {
        return Ret == WT_OK ? Response(Conn->GetSock(), Header, &Status, sizeof(int))
               : Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetVsgParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if (Header->Type == TYPE_CMD)
    {
        void *Result = nullptr;
        int Len = 0;

        Ret = Bsn->GetVsg().GetParam(Conn, &Result, Len);
        if (Ret == WT_OK && Conn->NeedRsp())
        {
            Ret = Response(Conn->GetSock(), Header, Result, Len);
        }
        else if (Ret != WT_OK)
        {
            Ret = Response(Conn->GetSock(), Header, Ret);
        }
    }
    else
    {
        Ret = Bsn->GetVsg().GetParam(Conn, &Data, Header->Length);
    }

    return Ret;
}

int InterProt::GenerateSigFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < MAX_NAME_SIZE)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    void *Param = static_cast<char *>(Data);
    void *FileData[MAX_NUM_OF_CHNNEL] = {nullptr};//FileData的内存，算法里只申请一次，程序结束时才释放
    int FileLen[MAX_NUM_OF_CHNNEL]={0};

    int Ret = Bsn->GetVsg().GenerateSig(Param, Header->Length, FileData, FileLen);
    if(Ret == WT_OK)
    {
        //正确生成后返回的内容为：协议头+总流数+流1长度+流1数据+流2长度+流2数据+...
        int TotalLen = 0;   //数据长度
        int StreamCnt = 0;
        TotalLen += sizeof(int);    //StreamCnt
        for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            if(FileLen[i] > 0)
            {
                StreamCnt++;
                TotalLen += sizeof(int);
                TotalLen += FileLen[i];
            }
        }

        AckHeader Ack(Header, sizeof(Ret)+TotalLen, WT_OK);
        int TxLen = 0;
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), TxLen);

        if (Ret == WT_OK)
        {
            //WTLog::Instance().WriteLog(LOG_DEBUG, "Get Stream Cnt = %d\n",StreamCnt);
            Ret = Conn->GetSock().Send(&StreamCnt, sizeof(int), TxLen);

            if (Ret == WT_OK)
            {
                for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
                {
                    if(FileLen[i] > 0)
                    {
                        BufInfo Vec[2] = {{&(FileLen[i]), sizeof(int)}, {FileData[i], FileLen[i]}};
                        Ret = Conn->GetSock().Send(Vec, 2, TxLen);
                        if (Ret != WT_OK)
                        {
                            break;
                        }
                    }
                }
            }
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::VsgRecord(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsg().StartRecord(*static_cast<int *>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetVsgRecordData(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    void *Record = nullptr;
    int Len = 0;
    int Ret = Bsn->GetVsg().GetRecordData(&Record, Len);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Record, Len) : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetPNCfg(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    PnItemHead *PnHead = nullptr;
    ExtPnItem *Item = nullptr;
    int PnHeadNum = 0;
    int PnNum = 0;

    if (Header->Type == TYPE_CMD)
    {
        if (Header->Length <= 8)
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }

        PnHeadNum = *static_cast<int *>(Data);
        PnNum = *(static_cast<int *>(Data) + 1);

        if (Header->Length - 8 < PnHeadNum * (signed)sizeof(PnItemHead) + PnNum * (signed)sizeof(ExtPnItem))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
        PnHead = reinterpret_cast<PnItemHead *>(static_cast<char *>(Data) + 8);
        Item = reinterpret_cast<ExtPnItem *>(static_cast<char *>(Data) + 8 + sizeof(PnItemHead) * PnHeadNum);
    }
    else
    {
        if (Header->Length < (signed)sizeof(int))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }

//#if TIME_DEBUG
//    struct timeval tpstart, tpend;
//    gettimeofday(&tpstart, NULL);
//#endif
    int Ret = Bsn->GetVsg().SetPNCfg(Conn, PnHead, PnHeadNum, Item, PnNum);
//#if TIME_DEBUG
//    gettimeofday(&tpend, NULL);
//    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "###SetPNCfg Used Time:" << timeuse << "us" << std::endl;
//#endif
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetPnCfg(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    vector<PnItemHead> PnHead;
    vector<ExtPnItem> Item;

    int Ret = Bsn->GetVsg().GetPNCfg(PnHead, Item);

    if(Ret == WT_OK)
    {
        int Num[2] = { 0 };
        Num[0] = (int)PnHead.size();
        Num[1] = (int)PnHead.size();

        AckHeader Ack(Header, sizeof(int) * 3 + sizeof(PnItemHead) * Num[0] + sizeof(ExtPnItem) * Num[1], WT_OK);

        int TxLen;
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), TxLen);
        if (Ret != WT_OK)
        {
            return Ret;
        }

        Ret = Conn->GetSock().Send(Num, sizeof(Num), TxLen);
        if (Ret != WT_OK)
        {
            return Ret;
        }

        for (const auto &iter : PnHead)
        {
            Ret = Conn->GetSock().Send(&iter, sizeof(iter), TxLen);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }

        for (const auto &iter : Item)
        {
            Ret = Conn->GetSock().Send(&iter, sizeof(iter), TxLen);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }

        return WT_OK;
    }
    else
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
}

int InterProt::GetDefaultGenParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Demode = *static_cast<int *>(Data);
    void *Param = nullptr;
    int Len = 0;

    int Ret = Bsn->GetVsg().GetDefaultGenParam(Demode, &Param, Len);
    if (Ret == WT_OK)
    {
        return Response(Conn->GetSock(), Header, Param, Len);
    }
    else
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
}

int InterProt::GetVsgPowerRange(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE + (signed)sizeof(int) + (signed)sizeof(double)))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Type = *(int *)Data;
    double Freq = *(double *)((char *)Data + sizeof(int));
    double Range[2];

    int Ret = Bsn->GetVsg().GetVsgPowerRange(Type, Freq, Range[0], Range[1]);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Range, sizeof(Range))
           : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetVsgGainParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Ret = WT_OK;
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Tx_Gain_Parm VSGGainParm;
    Ret = DevLib::Instance().VSGGetGainParameter(*static_cast<int *>(Data), VSGGainParm);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, &VSGGainParm, sizeof(Tx_Gain_Parm))
        : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetVsgIfgEnable(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    Ret = Bsn->GetVsg().SetVsgIfgEnable(Conn, *static_cast<int *>(Data));
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetVsgIfgEnable(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Enable = 0;
    Enable = Bsn->GetVsg().GetVsgIfgEnable();
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}


int InterProt::GetSubnet(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    char Buf[sizeof(SystemIPInfo)];
    int InfoSize = 0;
    if ((Ret = WTDeviceInfo::Instance().GetDeviceSubNetInfo(Buf, InfoSize)) == WT_OK)
    {
        Ret = Response(Conn->GetSock(), Header, Buf, InfoSize);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetSubnet Ret=%d, InfoSize=%d\n", Ret, InfoSize);
    return Ret;
}

int InterProt::GetSubnetLink(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    bool Buf[MAX_SUB_NET_NUM];
    int InfoSize = 0;
    if ((Ret = WTDeviceInfo::Instance().GetDeviceSubNetLink(Buf, InfoSize)) == WT_OK)
    {
        Ret = Response(Conn->GetSock(), Header, Buf, InfoSize);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetSubnetLink Ret=%d, InfoSize=%d\n", Ret, InfoSize);
    return Ret;
}

int InterProt::GetIpAddressType(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    bool Isdhcp = false;
    int InfoSize = 0;
    if ((Ret = WTDeviceInfo::Instance().GetDeviceIpAddressType(&Isdhcp, InfoSize)) == WT_OK)
    {
        Ret = Response(Conn->GetSock(), Header, &Isdhcp, InfoSize);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetIpAddressType Ret=%d, InfoSize=%d Isdhcp=%d\n", Ret, InfoSize, Isdhcp);
    return Ret;
}

int InterProt::SetSubnet(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    if (Header->Length / sizeof(IPType) >= sizeof(SystemIPInfo) / sizeof(IPInfo))
    {
        int Ret = WTDeviceInfo::Instance().SetSubNetInfo((IPType *)Data, Header->Length / sizeof(IPType));
        return Response(Conn->GetSock(), Header, Ret);
    }
    WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
    return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
}

int InterProt::GetGUIVersion(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    //通过协议类型来获取对应的GUI文件，并获取对应的文件版本
    GUIVersion GUIVersionInfo;
    memset(&GUIVersionInfo, 0, sizeof(GUIVersion));
    Ret = Bsn->GetGUIFileVersionByType((char *)Data, GUIVersionInfo);

    if(Ret == WT_OK)
    {
        Ret = Response(Conn->GetSock(), Header, &GUIVersionInfo, sizeof(GUIVersion));
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetGUIFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Len = 0;
    int Ret = WT_OK;

    if((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    //通过协议类型来获取对应的GUI文件
    std::vector<GUIFileInfo> GUIFile;
    Ret = Bsn->GetGUIFileByType((char *)Data, GUIFile, Len);

    if(Ret == WT_OK)
    {
        int TxLen = 0;
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), Len);

        if (Ret != WT_OK)
        {
            return Ret;
        }

        for (const auto &Item : GUIFile)
        {
            Ret = Conn->GetSock().Send(&Item, sizeof(int) + 256, TxLen);
            if (Ret != WT_OK)
            {
                return Ret;
            }

            Ret = Conn->GetSock().Send(Item.File->GetFileBuf(), Item.FileDataLen, TxLen);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }

        return WT_OK;
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetDeviceTemperature(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int DataLen = 0;
    std::unique_ptr<char[]>TemperBuf(new char[sizeof(DevTemperature)]);
    // TODO 获取仪器温度
    int Ret = Bsn->GetDeviceTemperatureHandler(Data, TemperBuf.get(), DataLen);
    if(Ret == WT_OK)
    {
        //send 返回操作结果+温度信息
        Ret = Response(Conn->GetSock(), Header, TemperBuf.get(), DataLen);
    }
    else
    {
        //send 返回错误操作结果
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetHistoryTemperature(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int DataLen = 0;
    std::unique_ptr<char[]>TemperBuf(new char[sizeof(int) + MAX_SAVE_TEMP * sizeof(DevTempSave)]);
    // TODO 获取仪器温度
    int Ret = Bsn->GetHistoryTemperatureHandler(Data, TemperBuf.get(), DataLen);
    if(Ret == WT_OK)
    {
        //send 返回操作结果+温度信息
        Ret = Response(Conn->GetSock(), Header, TemperBuf.get(), DataLen);
    }
    else
    {
        //send 返回错误操作结果
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetVoltInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int DataLen = 0;
    int VoltCnt = 0;
    int Ret = WT_OK;
    Ret = DevLib::Instance().GetDevVoltCnt(VoltCnt, DataLen);
    std::unique_ptr<char[]>VoltBuf(new char[sizeof(int) + DataLen]);
    // TODO 获取仪器电压
    Ret = DevLib::Instance().GetDevVoltInfo(Data, VoltBuf.get() + sizeof(int), VoltCnt, DataLen);
    *reinterpret_cast<int *>(VoltBuf.get()) = VoltCnt;
    if(Ret == WT_OK)
    {
        //send 返回操作结果+电压信息
        Ret = Response(Conn->GetSock(), Header, VoltBuf.get(), DataLen + sizeof(int));
    }
    else
    {
        //send 返回错误操作结果
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetFanSpeedParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    //获取仪器风扇转速,目前处理的是单个的，后续有变再作修改
    int Speed = 0;
    Ret = Bsn->GetFanSpeedHandler(Data, Speed);
    if(Ret == WT_OK)
    {
        //send 返回操作结果+风扇转速
        return Response(Conn->GetSock(), Header, &Speed, sizeof(int));
    }
    else
    {
        //send 返回错误操作结果
        return Response(Conn->GetSock(), Header, Ret);
    }
}

int InterProt::SetFanSpeedParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if(Header->Length == sizeof(int) * 2)
    {
        Ret = Bsn->SetFanSpeedHandler(Data);
    }
    else
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error");
        Ret = WT_CMD_ERROR;
    }

    //send 返回操作结果Ret
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetComponentParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if (Header->Length < sizeof(ComponentParam) + sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Ret = Bsn->SetComponentParamValue((ComponentParam *)Data,
                                      (char *)Data + sizeof(ComponentParam),
                                      Header->Length - sizeof(ComponentParam));

    //send 返回操作结果
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetComponentParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Len = 0;
    char Buf[2048] = {0};
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(ComponentParam))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    //返回操作结果+器件参数值
    int Ret = Bsn->GetComponentParamValue((ComponentParam *)Data, Buf, Len);

    if(Ret == WT_OK)
    {
        return Response(Conn->GetSock(), Header, Buf, Len);
    }
    else
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
}


int InterProt::SetMimoParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length <= (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length <= sizeof(int)");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Id = *static_cast<int *>(Data);
    int Ret = Bsn->SetMimoParam(Id, static_cast<int *>(Data) + 1);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetDeviceInfo(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    int Ret = WT_OK;

    //获取相应仪器的详细信息:设备名称，SN，固件版本号
    DeviceInfo DevInfo = WTDeviceInfo::Instance().GetDeviceDetailedInfo();

    Ret = Response(Conn->GetSock(), Header, &DevInfo, sizeof(DeviceInfo));

    WTLog::Instance().WriteLog(LOG_DEBUG, "InterProt::GetDeviceInfo\n");
    return Ret;
}

int InterProt::GetLicenseInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    (void)Data;

    //获取相应license的信息
    vector<LicItemInfo> LicItemsInfo;
    int Len = 0;
    Ret = Bsn->GetLicenseInfoHandler(LicItemsInfo, Len);
    if(Ret == WT_OK)
    {
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), Len);
        if(Ret == WT_OK)
        {
            //发送license信息
            for (int i = 0; i < (signed)LicItemsInfo.size(); i++)
            {
                Ret = Conn->GetSock().Send(&LicItemsInfo[i], sizeof(LicItemInfo), Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::GetCurSubDeviceCfg(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    (void)Data;

    //获取当前子仪器划分的信息
    WTConf::DevCfg Cfg;
    int Len = 0;

    Ret = Bsn->GetCurSubDeviceCfgHandler(Cfg);
    if(Ret == WT_OK)
    {
        AckHeader Ack(Header, sizeof(Ret) + sizeof(WTConf::DevCfg), Ret);
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), Len);
        if(Ret == WT_OK)
        {
            //发送当前子仪器资源分配的信息,即当前子仪器的支持的硬件lic
            Ret = Conn->GetSock().Send(&Cfg, sizeof(WTConf::DevCfg), Len);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}


int InterProt::GetHardErrInfo(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    int HardErrInfo[1024] = { 0 };
    License::Instance().GetLicStatus();
    int Len = ErrorLib::Instance().GetErrCodeSize();
    for (int Index = 0; Index < Len; Index++)
    {
        HardErrInfo[Index] = ErrorLib::Instance().GetErrCode(Index);
    }

    if (WT_OK == Ret)
    {
        Ret = Response(Conn->GetSock(), Header, HardErrInfo, sizeof(int) * Len);
        if(WT_OK == Ret)
        {
            ErrorLib::Instance().ClearErrCode();
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}


//---------------------信号文件管理操作 begin------------------------------------
//信号文件下发
int InterProt::SendSigFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int NameLen = strlen((char *)Data);
    if (NameLen == 0 || NameLen > MAX_NAME_SIZE)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "filename length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Len = Header->Length - MAX_NAME_SIZE;
    int Ret = Bsn->SigFileTx((char *)Data, (char *)Data + MAX_NAME_SIZE, Len);

    return Response(Conn->GetSock(), Header, Ret);
}

//信号文件是否存在
int InterProt::SigFileExist(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    string FileName(static_cast<char *>(Data));
    int Exist = 0;

    int Ret = Bsn->SigFileExist(FileName, Exist);

    return WT_OK == Ret ? Response(Conn->GetSock(), Header, &Exist, sizeof(Exist))
           : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::DelSigFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    string FileName(static_cast<char *>(Data));

    int Ret = Bsn->DelSigFile(FileName);
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetSigFileList(CmdHeader *Header, void *Data, void *Arg)
{
    vector<string> FileList;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    string Dire(static_cast<char *>(Data));
    int Ret = Bsn->GetSigFileList(FileList, Dire);
    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }

    int Len = MAX_NAME_SIZE * FileList.size();
    unique_ptr<char[]> Buffer(new(std::nothrow) char[Len]);
    if (Buffer == nullptr)
    {
        return Response(Conn->GetSock(), Header, WT_ALLOC_FAILED);
    }

    memset(Buffer.get(), 0, Len);

    for (int i = 0; i < (signed)FileList.size(); i++)
    {
        strcpy(Buffer.get() + i * MAX_NAME_SIZE, FileList[i].c_str());
    }

    return Response(Conn->GetSock(), Header, Buffer.get(), Len);
}

int InterProt::GetSigFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    string FileName(static_cast<char *>(Data));
    void *Result = nullptr;
    int Len = 0;

    int Ret = Bsn->GetSigFile(FileName, &Result, Len);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Result, Len)
           : Response(Conn->GetSock(), Header, Ret);
}
//---------------------信号文件管理操作 end------------------------------------

int InterProt::SetCalData(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length <= MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Len = Header->Length - MAX_NAME_SIZE;
    int Ret = Bsn->SetCalData((char *)Data, (char *)Data + MAX_NAME_SIZE, Len);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetCalData(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    const int BufLen = 1024 * 1024; //1M buffer长度
    unique_ptr<char[]> Buf(new(std::nothrow) char[BufLen]);
    if (Buf == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed");
        return Response(Conn->GetSock(), Header, WT_ALLOC_FAILED);
    }

    int DataLen = 0;
    int Ret = Bsn->GetCalData(static_cast<char *>(Data), Buf.get(), BufLen, DataLen);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Buf.get(), DataLen)
                        : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetCalParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    int Ret = wt_calibration_set_calibration_option(Header->Length, Data);
    Cal_Opt *pCal_Opt = static_cast<Cal_Opt *>(Data);
    DevLib::Instance().VSASetCalibrationFlat(pCal_Opt->rx_cal_mode_flag);
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetCalParam(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    Cal_Opt CalConfig;
    int Ret = wt_calibration_get_calibration_option(sizeof(Cal_Opt), &CalConfig);

    return Ret == sizeof(Cal_Opt) ? Response(Conn->GetSock(), Header, &CalConfig, sizeof(Cal_Opt))
        : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::ReloadCalFile(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    (void)Header;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    int Count = DevLib::Instance().GetUnitBoardModNum(DEV_TYPE_VSG);
    int UnitMapRfport = WT_RF_1;
    for (int unit = 0; UnitMapRfport < WT_RF_MAX; ++UnitMapRfport)
    {
        DevLib::Instance().GetModId(DEV_TYPE_VSG, UnitMapRfport, unit);
        if(unit != 0)
        {
            break;
        }
    }
    int Ret = wt_calibration_initial(Count, UnitMapRfport);
    if (Ret != WT_OK)
    {
        Ret += WT_CAL_BASE_ERROR;
        WTLog::Instance().LOGERR(Ret, "reload calibration file failed");
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetMonObj(CmdHeader *Header, void *Data, void *Arg)
{
    MonConnector *Conn = dynamic_cast<MonConnector *>(static_cast<WTConnector *>(Arg));
    if (Conn == nullptr)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "invalid monitor command");
        return Response(static_cast<WTConnector *>(Arg)->GetSock(), Header, WT_CMD_ERROR);
    }

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Conn->GetMon()->SetMonObj(*static_cast<int *>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetMonData(CmdHeader *Header, void *Data, void *Arg)
{
    MonConnector *Conn = dynamic_cast<MonConnector *>(static_cast<WTConnector *>(Arg));
    if (Conn == nullptr)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "invalid monitor command");
        return Response(static_cast<WTConnector *>(Arg)->GetSock(), Header, WT_CMD_ERROR);
    }

    if (Header->Length < 8)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    vector<string> VsaResult;
    int Action = *static_cast<int *>(Data);
    int Type = *(static_cast<int *>(Data) + 1);

    if (Type == MON_VSA_RESULT)
    {
        for (int i = 0; i + VSA_RESULT_NAME_LEN <= Header->Length - 8; i += VSA_RESULT_NAME_LEN)
        {
            VsaResult.push_back(string(static_cast<char *>(Data) + 8 + i));
        }
    }

    int Ret = Conn->GetMon()->SetMonData(Action, Type, VsaResult);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SendMonitorData(WRSocket &Sock, int Type, const void *Buf, int Len)
{
    int TotalLen = 8 + Len;
    char SendBuf[sizeof(CmdHeader) + 8];
    CmdHeader *Header = new (SendBuf) CmdHeader;
    Header->Type = TYPE_CMD;
    Header->Code = CMD_TX_MON_DATA;
    Header->Length = TotalLen;
    *(int *)(SendBuf + sizeof(CmdHeader)) = Type;
    *(int *)(SendBuf + sizeof(CmdHeader) + sizeof(int)) = Len;

    int tmp;

    BufInfo Vec[2] = {{SendBuf, sizeof(SendBuf)}, {const_cast<void *>(Buf), Len}};
    return Sock.Send(Vec, 2, tmp);
}

int InterProt::SendMonVsaData(WRSocket &Sock, int Type, const vector<MonVsaResult> &Result)
{
    int TotalLen = sizeof(Type);

    for (const auto &Item : Result)
    {
        TotalLen += sizeof(MonVsaResult) - 8 + Item.DataLen;
    }

    char SendBuf[sizeof(CmdHeader) + sizeof(int)];
    CmdHeader *Header = new (SendBuf) CmdHeader;
    Header->Type = TYPE_CMD;
    Header->Code = CMD_TX_MON_DATA;
    Header->Length = TotalLen;
    *(int *)(SendBuf + sizeof(CmdHeader)) = Type;

    int tmp;
    int Ret = Sock.Send(SendBuf, sizeof(SendBuf), tmp);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    for (const auto &Item : Result)
    {
        Ret = Sock.Send(&Item, sizeof(MonVsaResult) - 8, tmp);
        Ret |= Sock.Send(Item.Data, Item.DataLen, tmp);
        if (Ret != WT_OK)
        {
            break;
        }
    }
    return Ret;
}

int InterProt::SendMonPnParam(WRSocket &Sock, int Type, const std::vector<PnItemHead> &PnHeadInfo, const std::vector<ExtPnItem> &PnInfo)
{
    int Ret = WT_OK;
    int Num[2] = {(int)PnHeadInfo.size(), (int)PnInfo.size()};
    int TotalLen = sizeof(Type) + sizeof(int) * 2 + sizeof(PnItemHead) * Num[0] + sizeof(ExtPnItem) * Num[1];
    char SendBuf[sizeof(CmdHeader) + sizeof(int) * 3];
    CmdHeader *Header = new (SendBuf) CmdHeader;
    Header->Type = TYPE_CMD;
    Header->Code = CMD_TX_MON_DATA;
    Header->Length = TotalLen;
    *(int *)(SendBuf + sizeof(CmdHeader)) = Type;
    *(int *)(SendBuf + sizeof(CmdHeader) + 4) = Num[0];
    *(int *)(SendBuf + sizeof(CmdHeader) + 8) = Num[1];

    int Len = 0;
    Ret = Sock.Send(SendBuf, sizeof(SendBuf), Len);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    for (const auto &iter : PnHeadInfo)
    {
        Ret = Sock.Send(&iter, sizeof(iter), Len);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    for (const auto &iter : PnInfo)
    {
        Ret = Sock.Send(&iter, sizeof(iter), Len);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }
    return WT_OK;
}

int InterProt::GetMonitorsInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Ret = WT_OK;
    const int IpSize = 16;
    (void)Data;

    vector<string> MonsInfo;
    int Len = 0;
    Ret = MonitorMgr::Instance().GetAllMonitorsInfo(MonsInfo);
    if(Ret == WT_OK)
    {
        Len = IpSize * MonsInfo.size();
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);

        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), Len);
        if(Ret == WT_OK)
        {
            for (const auto &iter : MonsInfo)
            {
                char TmpIP[16] = {0};
                strncpy(TmpIP, iter.c_str(), iter.length());
                Ret = Conn->GetSock().Send(TmpIP, sizeof(TmpIP), Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

int InterProt::CalcIQImp(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    double Result[3];
    int Segment = 1;
    if (Header->Length >= sizeof(int))
    {
        Segment = *(int *)Data;
    }

    int Ret = Bsn->GetVsa().CalcIQImp(Segment, Result[0], Result[1], Result[2]);

    if (Ret == WT_OK)
    {
        Bsn->GetVsa().SetStaticIQImb(Segment, Result[0], Result[1], Result[2]);
        return Response(Conn->GetSock(), Header, Result, sizeof(Result));
    }
    else
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
}

int InterProt::SetStaticIQImb(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(double) * 3)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Segment = 1;
    if (Header->Length >= sizeof(double) * 3 + sizeof(int))
    {
        Segment = *(int *)((char *)Data + sizeof(double) * 3);
    }

    double *IQParam = static_cast<double *>(Data);
    int Ret = Bsn->GetVsa().SetStaticIQImb(Segment, IQParam[0], IQParam[1], IQParam[2]);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::ClrStaticIQImb(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Segment = 1;

    if (Header->Length >= sizeof(int))
    {
        Segment = *(int *)Data;
    }

    int Ret = Bsn->GetVsa().ClrStaticIQImb(Segment);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetVsgStaticIQImb(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(double) * 3)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Segment = 1;
    if (Header->Length >= sizeof(double) * 3 + sizeof(int))
    {
        Segment = *(int *)((char *)Data + sizeof(double) * 3);
    }

    double *IQParam = static_cast<double *>(Data);
    int Ret = Bsn->GetVsg().SetStaticIQImb(Segment, IQParam[0], IQParam[1], IQParam[2]);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::ClrVsgStaticIQImb(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Segment = 1;

    if (Header->Length >= sizeof(int))
    {
        Segment = *(int *)Data;
    }

    int Ret = Bsn->GetVsg().ClrStaticIQImb(Segment);

    return Response(Conn->GetSock(), Header, Ret);
}


//设置是否打开温度补偿功能
int InterProt::SetTempCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    CalConf::Instance().SetTempCal(*static_cast<int*>(Data));
    return Response(Conn->GetSock(), Header, WT_OK);
}

//设置是否打开平坦度补偿功能
int InterProt::SetFlatnessCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    CalConf::Instance().SetFlatnessCal(*static_cast<int*>(Data));
    Bsn->GetVsg().SetVsgFlatnessCal(*static_cast<int*>(Data));
    Bsn->GetVsa().SetVsaFlatnessCal(*static_cast<int*>(Data));
    return Response(Conn->GetSock(), Header, WT_OK);
}

int InterProt::SetVsgFlatnessCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsg().SetVsgFlatnessCal(*static_cast<int*>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}
int InterProt::GetVsgFlatnessCal(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Enable = 0;
    Enable = Bsn->GetVsg().GetVsgFlatnessCal();
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}

int InterProt::SetVsgIQImbCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsg().SetVsgIQImbCal(*static_cast<int*>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}
int InterProt::GetVsgIQImbCal(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Enable = 0;
    Enable = Bsn->GetVsg().GetVsgIQImbCal();
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}

int InterProt::SetVsaFlatnessCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().SetVsaFlatnessCal(*static_cast<int*>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}
int InterProt::GetVsaFlatnessCal(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Enable = 0;
    Enable = Bsn->GetVsa().GetVsaFlatnessCal();
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}

int InterProt::SetVsaIQImbCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().SetVsaIQImbCal(*static_cast<int*>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}
int InterProt::GetVsaIQImbCal(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Enable = 0;
    Enable = Bsn->GetVsa().GetVsaIQImbCal();
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}


int InterProt::SetVsaDuplexNoiseFlag(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().SetDuplexNoiseFlag(*static_cast<int*>(Data));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetVsaDuplexNoiseFlag(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Enable = 0;
    Enable = Bsn->GetVsa().GetDuplexNoiseFlag();
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}

int InterProt::SetVsgFemParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Type == TYPE_CMD)
    {
        if (Header->Length < sizeof(FemParam))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }
    else
    {
        if (Header->Length < (signed)sizeof(int))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < int");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }

    int Ret = Bsn->GetVsg().SetVsgFemParam(Conn, *(FemParam *)Data);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::SetPathLossFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Len = Header->Length;
    int Ret = WT_OK;

    Ret = Bsn->SetPathLossFile((char *)Data, Len);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetPathLossFile(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    void *Buff = nullptr;
    int Len = 0;
    int Ret = WT_OK;

    Ret = Bsn->GetPathLossFile(&Buff, Len);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Buff, Len)
                        : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::DelSubNetSetting(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    (void)Data;

    Ret = Bsn->DeleteSubNet();
    Ret = Response(Conn->GetSock(), Header, Ret);

    return Ret;
}

int InterProt::ShutDownDevice(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Ret = WT_OK;
    (void)Data;

    Ret = Response(Conn->GetSock(), Header, Ret);

    DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_OFF);
    Basefun::LinuxSystem("init 0");

    return Ret;
}

int InterProt::SendMeterSettingtoMons(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = WT_OK;
    int Len = Header->Length;

    Bsn->SendMeterParamtoMon((char *)Data, Len);

    Ret = Response(Conn->GetSock(), Header, Ret);

    return Ret;
}

int InterProt::AnalyzeBeamformingCalibrationChannelEstDutTx(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;

    if(Header->Length < sizeof(int))
    {
        Ret = WT_CMD_ERROR;
        WTLog::Instance().LOGERR(Ret, "cmd length error");
    }
    else
    {
        WTConnector *Conn = static_cast<WTConnector *>(Arg);
        Business *Bsn = Conn->GetBsn();
        int Demode = *static_cast<int *>(Data);
        Ret = Bsn->GetVsa().AlzBeamformingCalChEstTx(Demode);
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::AnalyzeBeamformingCalibrationChannelEstDutRx(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;

    if((Header->Length % sizeof(double)) != 0)
    {
        Ret = WT_CMD_ERROR;
        WTLog::Instance().LOGERR(Ret, "cmd length error");
    }
    else
    {
        WTConnector *Conn = static_cast<WTConnector *>(Arg);
        Business *Bsn = Conn->GetBsn();
        Ret = Bsn->GetVsa().BeamformingCalChEstRx(Data,Header->Length);
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::AnalyzeBeamformingCalibrationResult(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    void *RstBuf = nullptr;
    int Len = 0;
    Ret = Bsn->GetVsa().AlzBeamformingResult(&RstBuf, Len);
    return (Ret == WT_OK && RstBuf != nullptr) ? Response(Conn->GetSock(), Header, RstBuf, Len):Response(Conn->GetSock(), Header, Ret);
}

int InterProt::AnalyzeBeamformingVerification(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    double RstDiffPower = 0;
    Ret = Bsn->GetVsa().AlzBeamformingVerification(RstDiffPower);
    return Ret == WT_OK ? Response(Conn->GetSock(), Header, &RstDiffPower, sizeof(double)):Response(Conn->GetSock(), Header, Ret);
}

int InterProt::AnalyzeBeamformingCalculateChannelProfile(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;

    if(Header->Length < sizeof(int))
    {
        Ret = WT_CMD_ERROR;
        WTLog::Instance().LOGERR(Ret, "cmd length error");
    }
    else
    {
        WTConnector *Conn = static_cast<WTConnector *>(Arg);
        Business *Bsn = Conn->GetBsn();
        int Demode = *static_cast<int *>(Data);

        unique_ptr<double[]> RstBuf(new(std::nothrow) double[8192*2]);
        if (RstBuf == nullptr)
        {
            return Response(Conn->GetSock(), Header, WT_ALLOC_FAILED);
        }

        memset(RstBuf.get(), 0, 8192*2);

        int Len = 0;
        Ret = Bsn->GetVsa().AlzBeamformingCalChProfile(Demode, RstBuf.get(), Len);
        return (Ret == WT_OK && RstBuf != nullptr) ? Response(Conn->GetSock(), Header, RstBuf.get(), Len)
                :Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::AnalyzeBeamformingCalculateChannelAmplitudeAngleBCM(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    unique_ptr<double[]> RstBuf(new(std::nothrow) double[8192*2 + 1]);
    if (RstBuf == nullptr)
    {
        return Response(Conn->GetSock(), Header, WT_ALLOC_FAILED);
    }

    memset(RstBuf.get(), 0, 8192*2 + 1);

    int Len = 0;
    Ret = Bsn->GetVsa().AlzBeamformingCalChAmplitudeAngleBCM(RstBuf.get(), Len);
    return (Ret == WT_OK && RstBuf != nullptr) ? Response(Conn->GetSock(), Header, RstBuf.get(), Len)
            :Response(Conn->GetSock(), Header, Ret);

    return Ret;
}

int InterProt::GetLog(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if (Header->Length <  (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error!");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    int Len = 0;

    vector<string> Record;

    Ret = Bsn->GetLogHandler(Data, Header->Length, Record, Len);
    if(Ret == WT_OK)
    {
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), Len);

        if(Ret == WT_OK)
        {
            for (int i = 0; i < (signed)Record.size(); i++)
            {
                Ret = Conn->GetSock().Send(Record[i].c_str(), Record[i].length(), Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::GetSaveLogFlagSetting(CmdHeader *Header, void *Data, void *Arg)
{
	int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Len = 0;
    Ret = Bsn->GetLogFlag(Data, Len);

    return (Ret == WT_OK) ? Response(Conn->GetSock(), Header, Data, Len)
            :Response(Conn->GetSock(), Header, Ret);

}

int InterProt::SetSaveLogFlag(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if (Header->Length <  (signed)sizeof(int) * 2 || 0 != (Header->Length % (sizeof(int) *2)))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error!");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Ret = Bsn->SetLogFlag(Data, Header->Length);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetStopSlave(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if ((Header->Type == TYPE_CMD) && Header->Length != sizeof(int) * 2)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    else if((Header->Type == TYPE_ACK) && Header->Length != sizeof(int) * 3)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int ModType = *(int *)Data;
    int Code    = *((int *)Data + 1);

//    WTLog::Instance().WriteLog(LOG_DEBUG, "Type = %#x,ModType =%#x,Code=%#x\n", Header->Type, ModType, Code);
    //stop操作不考虑出错的情况。
    ModType == DEV_TYPE_VSA ? Bsn->GetVsa().SetStopSlave(Conn, Code) : Bsn->GetVsg().SetStopSlave(Conn, Code);

    if (Conn->NeedRsp())
    {
        int RetData[2] = { ModType, Code };
        //从机返回给主机，由于是内部命令，不存在主机返回给API的情况
        return Response(Conn->GetSock(), Header, RetData, sizeof(int) * 2);
    }
    else
    {
        return WT_OK;
    }
}

//信号文件下发
int InterProt::SendCustomizeFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int NameLen = strlen((char *)Data);
    if (NameLen == 0 || NameLen > MAX_NAME_SIZE)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "filename length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    if (SysMonitor::Instance().IsDisableSaveSignal() == true)
    {
        WTLog::Instance().LOGERR(WT_DISK_CAPACITY_ERROR, "Disk capacity is too less");
        return Response(Conn->GetSock(), Header, WT_DISK_CAPACITY_ERROR);
    }

    int Len = Header->Length - MAX_NAME_SIZE;
    int Ret = Bsn->CustomizeFileTx((char *)Data, (char *)Data + MAX_NAME_SIZE, Len);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetCustomizeFile(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length < MAX_NAME_SIZE) || (strlen((char *)Data) > MAX_NAME_SIZE))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    string FileName(static_cast<char *>(Data));
    void *Result = nullptr;
    int Len = 0;

    int Ret = Bsn->GetCustomizeFile(FileName, &Result, Len);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, Result, Len)
        : Response(Conn->GetSock(), Header, Ret);
}


int InterProt::ShellExecute(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    std::string Cmd;
    std::string Result;
    Cmd.append((char *)Data, Header->Length);

    WTLog::Instance().WriteLog(LOG_DEBUG, "cmd : %s\n", Cmd.c_str());
    try
    {
        Result = Basefun::shell_exec(Cmd.c_str());
    }
    catch (const std::exception &e)
    {
        Result = string(e.what());
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Result : %s\n", Result.c_str());

    return Response(Conn->GetSock(), Header, Result.c_str(), Result.size());
}

int InterProt::AnalyzePerResult(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int FrameResult = 0;
    Ret = Bsn->GetVsa().AlzPer(FrameResult);

    return Ret == WT_OK ? Response(Conn->GetSock(), Header, &FrameResult, sizeof(int))
        : Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetExtralAlzParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < 2 * (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    int Demode = *static_cast<int *>(Data);
    int Type = *(static_cast<int *>(Data) + 1);
    int Ret = Bsn->GetVsa().SetExtralAlzParam(Demode, Type, static_cast<int *>(Data) + 2, Header->Length - 2 * sizeof(int));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetAnalyseGroupBaseResultStr(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < VSA_RESULT_NAME_LEN || (Header->Length % VSA_RESULT_NAME_LEN) > 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    vector<string> AlzTypes;
    int Cnt = (Header->Length / VSA_RESULT_NAME_LEN); //需要分析的结果项个数

    //从协议命令数据中提取分析项
    for(int i = 0; i < Cnt; i++)
    {
        string AlzStr = (char *)Data + VSA_RESULT_NAME_LEN * i;
        AlzTypes.push_back(AlzStr);
    }
    Ret = Bsn->GetVsa().SetAnalyseGroup(AlzTypes);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SendUserConnInfo(CmdHeader *Header, void *Data, void *Arg)
{
    if(Header->Type == TYPE_ACK)    //该协议不需要处理ack，只是用于主机给从机发送信息内容使用
    {
        return WT_OK;
    }
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    WTLog::Instance().WriteLog(LOG_DEBUG, "SendUserConnInfo Header->Type=%d, Header->Length=%d\n", Header->Type, Header->Length);

    if (Header->Length < sizeof(UserConnInfo))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    Ret = Bsn->SendUserConnInfo(Data, Header->Length);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetDeviceVersionInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Ret = WT_OK;

    (void)Data;

    //获取仪器相关内容的版本信息，算法，校准等
    std::string VerInfo;
    WTDeviceInfo::Instance().GetDeviceVersionInfo(VerInfo);

    Ret = Response(Conn->GetSock(), Header, VerInfo.c_str(), (int)VerInfo.length());

    return Ret;
}

int InterProt::SetVsaResultFilter(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsa().SetResultFilterSetting(Data, Header->Length);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::Set240MSpectOnFlag(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Flag = *(int *)Data;
    int Ret = Bsn->GetVsa().Set240MSpectOnFlag(Conn, Flag);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::SetVsaTrigParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(VsaTrigParam))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    VsaTrigParam *TrigParam = static_cast<VsaTrigParam *>(Data);
    int Ret = Bsn->GetVsa().SetVsaTrigParam(Conn, TrigParam);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetSlaveThreeVsaCapData(WRSocket &Sock)
{
    int Len = 0;
    char Buf[sizeof(CmdHeader) + 4];
    CmdHeader *Cmd = new (Buf) CmdHeader;
    Cmd->Type = TYPE_CMD;
    Cmd->Code = CMD_GET_SLAVE_240M_THREE_RAW_DATA;
    Cmd->Length = sizeof(int);
    *(int *)(Buf + sizeof(CmdHeader)) = 0;//流ID，从机都是0

    return Sock.Send(Buf, sizeof(Buf), Len);
}

int InterProt::GetThreeVsaRawData(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    std::vector<BufInfo> CalData;
    std::vector<BufInfo> CapData;
    int SegNum = 0;

    if(Header->Type == TYPE_CMD)
    {
        int Stream = *static_cast<int *>(Data);

        int Ret = Bsn->GetVsa().GetThreeCapRawData(Stream, SegNum, CalData, CapData);
        if(Ret == WT_OK)
        {
            char Buf[128];
            int Len = sizeof(int) * 2;  //ret and segnum

            for(int Seg = 0; Seg < SegNum; Seg++)
            {
                Len += sizeof(int) * 2 * WIDE_BAND_MAX_CAP_CNT; //WIDE_BAND_MAX_CAP_CNT *（capdata len + caldata len）
                for(int i = 0; i < WIDE_BAND_MAX_CAP_CNT; i++)
                {
                    Len += CapData[i+WIDE_BAND_MAX_CAP_CNT*Seg].Len;
                    Len += CalData[i+WIDE_BAND_MAX_CAP_CNT*Seg].Len;
            }
            }
            AckHeader *Ack = new (Buf) AckHeader(Header, Len, WT_OK);
            *(int *)(Buf + sizeof(AckHeader)) = SegNum;

            int Ret = Conn->GetSock().Send(Ack, sizeof(AckHeader) + sizeof(int), Len);
            if(Ret != WT_OK)
            {
                return Ret;
            }
            for(int i = 0; i < CapData.size(); i++)
            {
                if(CapData[i].Data != nullptr)
                {
                    BufInfo Vec[4] = {{&(CapData[i].Len), sizeof(int)}, CapData[i], {&(CalData[i].Len), sizeof(int)}, CalData[i]};
                    Ret = Conn->GetSock().Send(Vec, 4, Len);
                    if(Ret != WT_OK)
                    {
                        return Ret;
                    }
                }
            }
            return WT_OK;
        }
        else
        {
            return Response(Conn->GetSock(), Header, Ret);
        }
    }
    else    //接收从机返回数据
    {
        //数据格式为：segnum+Capdata1.len+Capdata1+CalData1.len+CalData1+Capdata2.len......
        int Result = static_cast<AckHeader *>(Header)->Result;
        if (Result == WT_OK)
        {
            SegNum = *(int *)Data;
            char *pData = (char *)Data + sizeof(int);
            for(int i = 0; i < WIDE_BAND_MAX_CAP_CNT * SegNum; i++)
            {
                BufInfo TmpInfo;
                TmpInfo.Len = *(int *)pData;
                pData += sizeof(int);
                TmpInfo.Data = pData;
                CapData.push_back(TmpInfo);
                pData += TmpInfo.Len;

                BufInfo TmpCalInfo;
                TmpCalInfo.Len = *(int *)pData;
                pData += sizeof(int);
                TmpCalInfo.Data = pData;
                CalData.push_back(TmpCalInfo);
                pData += TmpCalInfo.Len;
            }
        }
        Bsn->GetVsa().RecvThreeCapRawData(Conn, Result, SegNum, CalData, CapData);
        return WT_OK;
    }
}

int InterProt::StartTBTAp(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().StartTBTAp(Conn);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StopTBTAp(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().StopTBTAp(Conn);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}


int InterProt::GetTBTApStatus(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int) * 2)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    //从数据中提取结果值
    int Status = *(static_cast<int *>(Data));
    int Ret = Bsn->GetVsa().GetTBTApStatus(Conn, Status);

    if (Conn->NeedRsp())
    {
        return Ret == WT_OK ? Response(Conn->GetSock(), Header, &Status, sizeof(int))
               : Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::VsaAutoRangeTBTAp(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().AutoRangeTBTAp(Conn, Data);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StartTBTSta(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().StartTBTSta(Conn);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::StopTBTSta(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().StopTBTSta(Conn);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}


int InterProt::GetTBTStaStatus(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int) * 2)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    //从数据中提取结果值
    int Status = *(static_cast<int *>(Data));
    int Ret = Bsn->GetVsa().GetTBTStaStatus(Conn, Status);

    if (Conn->NeedRsp())
    {
        return Ret == WT_OK ? Response(Conn->GetSock(), Header, &Status, sizeof(int))
               : Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

// 设置是否打开内校准功能
int InterProt::StartInCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    (void)Data;
    WTLog::Instance().WriteLog(LOG_DEBUG, "WT_server InterProt::StartInCal\n");
    SubTaskmgr::Instance().StartInCalAtOnce();

    return Response(Conn->GetSock(), Header, WT_OK);
}


int InterProt::StopInCal(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);

    (void)Data;
    WTLog::Instance().WriteLog(LOG_DEBUG, "WT_server InterProt::StopInCal\n");
    SubTaskmgr::Instance().StopInCalAtOnce();

    return Response(Conn->GetSock(), Header, WT_OK);
}


int InterProt::QueryInCalProcess(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    WTLog::Instance().WriteLog(LOG_DEBUG, "WT_server InterProt::QueryInCalProcess\n");
    (void)Data;
    int Process = 100;
    int Ret = SubTaskmgr::Instance().QueryInCalProcess(Process);

    WTLog::Instance().WriteLog(LOG_DEBUG, "WT_server InterProt::QueryInCalProcess Ret=%d\n", Ret);
    if (Ret != WT_OK)
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, &Process, 4);
    }

    return Ret;
}

// 设置是否打开内校准功能
int InterProt::SetInCalConfig(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int state = *(int*)Data;
    SubTaskmgr::Instance().SetInCalConfig(state);
    return Response(Conn->GetSock(), Header, WT_OK);
}

int InterProt::SendVsgFileToSalve(WRSocket &Sock, const std::string &Name, const void *FileBuf, int FileLen)
{
    CmdHeader Header;
    int Len = 0;
    Header.Code = CMD_SEND_FILE_IN_MIMO;    //主机下发信号文件给主机
    Header.Length = FileLen+MAX_NAME_SIZE;
    char FileName[MAX_NAME_SIZE] = {0};
    memcpy(FileName, Name.c_str(), Name.length());

    BufInfo Vec[3] = { { &Header,sizeof(CmdHeader) },{ FileName,MAX_NAME_SIZE },{const_cast<void *>(FileBuf),FileLen } };
    return Sock.Send(Vec, 3, Len);
}

int InterProt::SaveVsgFileMastertoSlave(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Len = 0;

    if (Header->Type == TYPE_CMD)
    {
        if (Header->Length <= MAX_NAME_SIZE)
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length <= MAX_NAME_SIZE");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }

        int NameLen = strlen((char *)Data);
        if (NameLen == 0 || NameLen > MAX_NAME_SIZE)
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "filename length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }

        Len = Header->Length - MAX_NAME_SIZE;
    }
    else
    {
        if (Header->Length < (signed)sizeof(int))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < int");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
    }

    int Ret = Bsn->GetVsg().SaveSigFileMasterToSlave(Conn, (char *)Data, (char *)Data + MAX_NAME_SIZE, Len);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::SetPnMasterToSlave(WRSocket &Sock, char *CurRequest)
{
    int TxLen = 0;
    const CmdHeader *Cmd = reinterpret_cast <CmdHeader *>(CurRequest);

    return Sock.Send(Cmd, Cmd->Length + sizeof(CmdHeader), TxLen);

}

int InterProt::NewGenerateSigFile(int Type, CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

//    if (Header->Length < MAX_NAME_SIZE)
//    {
//        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length error");
//        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
//    }

    void *Param = static_cast<char *>(Data);
    void *FileData[MAX_NUM_OF_CHNNEL] = {nullptr};//FileData的内存，算法里只申请一次，程序结束时才释放
    int FileLen[MAX_NUM_OF_CHNNEL]={0};
    int Ret = WT_CMD_ERROR;

    switch(Type)
    {
        case GEN_CW_WAVE:
            Ret = Bsn->GetVsg().GenerateSigCWV4(Param, Header->Length, FileData, FileLen);
            break;
        case GEN_BT_WAVE:
            Ret = Bsn->GetVsg().GenerateSigBlueToothV4(Param, Header->Length, FileData, FileLen);
            break;
        case GEN_WIFI_WAVE:
            Ret = Bsn->GetVsg().GenerateSigWifiV4(Param, Header->Length, FileData, FileLen);
            break;
        case GEN_SLE_WAVE:
            Ret = Bsn->GetVsg().GenerateSigSLE(Param, Header->Length, FileData, FileLen);
            break;
        case GEN_3GPP_WAVE:
            Ret = Bsn->GetVsg().GenerateSig3GPPV4(Param, Header->Length, FileData, FileLen);
            break;
        case GEN_WI_SUN:
            Ret = Bsn->GetVsg().GenerateSigWiSun(Param, Header->Length, FileData, FileLen);
            break;
        default:
            break;
    }

    if(Ret == WT_OK)
    {
        //正确生成后返回的内容为：协议头+总流数+流1长度+流1数据+流2长度+流2数据+...
        int TotalLen = 0;   //数据长度
        int StreamCnt = 0;
        TotalLen += sizeof(int);    //StreamCnt
        for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            if(FileLen[i] > 0)
            {
                StreamCnt++;
                TotalLen += sizeof(int);
                TotalLen += FileLen[i];
            }
        }

        AckHeader Ack(Header, sizeof(Ret)+TotalLen, WT_OK);
        int TxLen = 0;
        Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), TxLen);

        if (Ret == WT_OK)
        {
            //WTLog::Instance().WriteLog(LOG_DEBUG, "Get Stream Cnt = %d\n",StreamCnt);
            Ret = Conn->GetSock().Send(&StreamCnt, sizeof(int), TxLen);

            if (Ret == WT_OK)
            {
                for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
                {
                    if(FileLen[i] > 0)
                    {
                        BufInfo Vec[2] = {{&(FileLen[i]), sizeof(int)}, {FileData[i], FileLen[i]}};
                        Ret = Conn->GetSock().Send(Vec, 2, TxLen);
                        if (Ret != WT_OK)
                        {
                            break;
                        }
                    }
                }
            }
        }
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }

    return Ret;
}

//int InterProt::GenerateSigFileV2(CmdHeader *Header, void *Data, void *Arg)
//{
//    return NewGenerateSigFile(1, Header, Data, Arg);
//}

int InterProt::GenerateSigFileSLE(CmdHeader *Header, void *Data, void *Arg)
{
    return  NewGenerateSigFile(GEN_SLE_WAVE, Header, Data, Arg);
}

int InterProt::GenerateSigFileCW(CmdHeader *Header, void *Data, void *Arg)
{
    return NewGenerateSigFile(GEN_CW_WAVE, Header, Data, Arg);
}

int InterProt::GenerateSigFileBlueTooth(CmdHeader *Header, void *Data, void *Arg)
{
    return NewGenerateSigFile(GEN_BT_WAVE, Header, Data, Arg);
}

int InterProt::GenerateSigFileWifi(CmdHeader *Header, void *Data, void *Arg)
{
    return NewGenerateSigFile(GEN_WIFI_WAVE, Header, Data, Arg);
}

int InterProt::GenerateSigFileWiSun(CmdHeader *Header, void *Data, void *Arg)
{
    return NewGenerateSigFile(GEN_WI_SUN, Header, Data, Arg);
}

int InterProt::GenerateSigSLESynSeq(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    (void)Arg;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    void *Param = nullptr;
    int Len = 0;

    Ret = Bsn->GetVsg().GetGenerateSynSeq(&Param, Len);
    if(Ret == WT_OK)
    {
        Response(Conn->GetSock(), Header, Param, Len);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::GenerateSigFile3GPP(CmdHeader *Header, void *Data, void *Arg)
{
    return NewGenerateSigFile(GEN_3GPP_WAVE, Header, Data, Arg);
}

int InterProt::GetFinalParamAfterGenerate(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Arg;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    void *Param = nullptr;
    int Len = 0;

    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    
    Ret = Bsn->GetVsg().GetGenerateFinalParam(*(int*)Data ,&Param, Len);

    if(Ret == WT_OK)
    {
        Response(Conn->GetSock(), Header, Param, Len);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::GetFinalGenWaveWifiParamAfterGenerate(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    void *Param = nullptr;
    int Len = 0;

    Ret = Bsn->GetVsg().GetGenerateFinalGenWaveWifiParam(&Param, Len);

    if(Ret == WT_OK)
    {
        Response(Conn->GetSock(), Header, Param, Len);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::GetReturnDataAfterGenerate(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    (void)Data;
    int Ret = WT_OK;
    void *Param = nullptr;
    int Len = 0;
    int Type = -999;    //无效类型，直接取算法所有返回输出参数内容

    Ret = Bsn->GetVsg().GetGenerateReturnParam(Type, &Param, Len);

    if(Ret == WT_OK)
    {
        Response(Conn->GetSock(), Header, Param, Len);
    }
    else
    {
        Ret = Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::SetRuCarrierInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Ret =  Bsn->GetVsg().SetVsgRuCarrierInfo(Conn,Data);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::SendGetLicInfoToSalve(WRSocket &Sock)
{
    CmdHeader Header;
    int Len = 0;
    Header.Code = CMD_GET_SLAVE_LIC_INFO;    //主机下发信号文件给主机
    Header.Length = 0;

    return Sock.Send(&Header, sizeof(CmdHeader), Len);
}

int InterProt::GetSlaveLicInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    (void)Data;

    if(TYPE_CMD == Header->Type)    //只有主机给从机下发
    {
        //获取相应license的信息
        vector<LicItemInfo> LicItemsInfo;
        int Len = 0;
        Ret = Bsn->GetLicenseInfoHandler(LicItemsInfo, Len);
        if(Ret == WT_OK)
        {
            AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
            Ret = Conn->GetSock().Send(&Ack, sizeof(AckHeader), Len);
            if(Ret == WT_OK)
            {
                //发送license信息
                for (int i = 0; i < (signed)LicItemsInfo.size(); i++)
                {
                    Ret = Conn->GetSock().Send(&LicItemsInfo[i], sizeof(LicItemInfo), Len);
                    if (Ret != WT_OK)
                    {
                        return Ret;
                    }
                }
            }
        }
        else
        {
            Ret = Response(Conn->GetSock(), Header, Ret);
        }
    }
    else    //TYPE_ACK,从机给主机的回应,获取到从机的lic
    {
        int Result = static_cast<AckHeader *>(Header)->Result;
        if (Result == WT_OK)
        {
            int DataLen = static_cast<AckHeader *>(Header)->Length;
            Bsn->GetVsa().RecvSlaveLicInfo(Conn, Result, Data, DataLen);
        }
    }
    return Ret;
}

int InterProt::SetDevRunMode(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    int RunMode = *(int *)Data;
    if (RunMode > 0 && RunMode < TESTER_DIG_MODE_MAX)
    {
        DigModeLib::Instance().SetRunMode(RunMode);
    }
    else
    {
        Ret = WT_ARG_ERROR;
    }
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::SetDigParam(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    if (Header->Length < sizeof(DigParam))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    Conn->GetBsn()->GetVsa().SetDigParam(*(static_cast<DigParam *>(Data)));
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::SetDigDutMode(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    if (Header->Length < sizeof(DigDutModeType))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    DigModeLib::Instance().SetDigTestMode(*(static_cast<DigDutModeType *>(Data)));
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::GetVsgSendCnt(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    int SendCnt = 0;
    SendCnt = Bsn->GetVsa().GetDigLib().GetVsgSendCnt();
    Response(Conn->GetSock(), Header, &SendCnt, sizeof(int));
    return Ret;
}

int InterProt::ATTCalStart(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if (Header->Length < sizeof(ATTCalCfg))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    int Ret = Bsn->GetVsa().ATTCalStart(Conn, *(static_cast<ATTCalCfg *>(Data)));
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::ATTCalStop(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsa().ATTCalStop();
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetDevLOMode(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int LOMode = 0;
    int ModId = 0;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if ((Header->Type == TYPE_CMD))
    {
        if (Header->Length < sizeof(int) * 2)
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
        else
        {
            LOMode = (static_cast<int *>(Data))[0];
            ModId = (static_cast<int *>(Data))[1];
        }
    }
    else if((Header->Type == TYPE_ACK)&&Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Ret = Bsn->GetVsa().SetDevLOMode(Conn, ModId, LOMode);

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::GetDevLOMode(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    //从数据中提取结果值
    int LOMode =0;
    int ModId = *(int *)Data;
    int Ret = DevLib::Instance().GetLOComMode(ModId, LOMode);
    if (Conn->NeedRsp())
    {
        return Ret == WT_OK ? Response(Conn->GetSock(), Header, &LOMode, sizeof(int))
                            : Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::TestConnectStatus(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    return Response(static_cast<WTConnector *>(Arg)->GetSock(), Header, WT_OK);
}

int InterProt::SetSubNetAutoNeg(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    do
    {
        if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448)
        {
            WTLog::Instance().LOGERR(WT_ARG_UNKNOW_PARAMETER, "448 not support sub net");
            Ret = WT_ARG_UNKNOW_PARAMETER;
            break;
        }

        if (Header->Length < sizeof(int))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            Ret = WT_CMD_ERROR;
            break;
        }

        if (*(int *)Data)
        {
            Basefun::LinuxSystem((WTConf::GetDir() + "/SetSubNetSpeed.sh autoneg_on").c_str());
        }
        else
        {
            Basefun::LinuxSystem((WTConf::GetDir() + "/SetSubNetSpeed.sh autoneg_off").c_str());
        }
    } while (0);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetSubNetAutoNeg(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Enable = -1;
    do
    {
        if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448)
        {
            WTLog::Instance().LOGERR(WT_ARG_UNKNOW_PARAMETER, "448 not support sub net");
            break;
        }

        string Size = Basefun::shell_exec("ethtool eth10");
        if (Size.find("Auto-negotiation: on") != string::npos)
        {
            Enable = 1;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "autoneg on" << Enable << endl;
        }
        else
        {
            Enable = 0;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "autoneg off" << Enable << endl;
        }

    } while (0);
    return Response(Conn->GetSock(), Header, &Enable, sizeof(int));
}

int InterProt::SetDevAnalogIQMode(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int *AnalogIQMode= nullptr;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Length < sizeof(int) * MAX_BUSINESS_NUM)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    else
    {
        AnalogIQMode = (static_cast<int *>(Data));
    }

    Ret = Bsn->GetVsa().SetDevAnalogIQMode(AnalogIQMode, Header->Length / sizeof(int));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetDevAnalogIQMode(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if(Header->Type == TYPE_CMD)
    {
        // int *AnalogIQMode= nullptr;
        std::unique_ptr<int[]>AnalogIQMode(new (std::nothrow) int[MAX_BUSINESS_NUM]);
        if (nullptr == AnalogIQMode)
        {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "File alloc failed!");
                return WT_ALLOC_FAILED;
        }
        else
        {
            memset(AnalogIQMode.get(), -1, sizeof(int)*MAX_BUSINESS_NUM);
        }

        Ret = Bsn->GetVsa().GetDevAnalogIQMode(AnalogIQMode.get(), MAX_BUSINESS_NUM);

        return Ret == WT_OK ? Response(Conn->GetSock(), Header, AnalogIQMode.get(), sizeof(int)*MAX_BUSINESS_NUM)
                            : Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::GetSpectrumPointPower(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    double Power = 0;
    struct Temp
    {
        double Offset;
        int signalID;
        int segmentID;
    };
    do
    {
        if (Header->Length < sizeof(Temp))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
        }
        double Offset = ((Temp*)Data)->Offset;
        int signalID = ((Temp*)Data)->signalID;
        int segmentID = ((Temp*)Data)->segmentID;
        Ret = Bsn->GetVsa().GetSpectrumPointPower(Offset, Power, signalID, segmentID);
    } while (0);
    if(Ret == WT_OK)
    {
        return Response(Conn->GetSock(), Header, &Power, sizeof(double));
    }
    else
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
}

int InterProt::SubCmdHandle(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    if (Header->Length < sizeof(SubCmdHeader))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    SubCmdHeader *SubCmd = (static_cast<SubCmdHeader *>(Data));
    if (Header->Length - sizeof(SubCmdHeader) < SubCmd->SendDataLen)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    unique_ptr<char[]> RecvBuf(new (std::nothrow) char[SubCmd->RecvBufLen]);
    SubCmd->RecvBuf = RecvBuf.get();
    SubCmd->RecvDataLen = 0;
    SubCmd->SendBuf = reinterpret_cast<char *>(Data) + sizeof(SubCmdHeader);

    switch (SubCmd->Cmd)
    {
    case SUB_CMD_GET_VSA_DIG_PACKET_CNT:
        *reinterpret_cast<int *>(SubCmd->RecvBuf) = Bsn->GetVsa().GetDigPacketCnt();
        SubCmd->RecvDataLen = sizeof(int);
        break;
    case SUB_CMD_GET_VSG_DIG_PACKET_CNT:
        *reinterpret_cast<int *>(SubCmd->RecvBuf) = Bsn->GetVsg().GetDigPacketCnt();
        SubCmd->RecvDataLen = sizeof(int);
        break;
    case SUB_CMD_SET_SVAE_VSA_ADC_DATA:
        Ret = Bsn->GetVsa().SetSaveRawDataEnable(*reinterpret_cast<int *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_GET_VSA_ALZ_RESULT:
        *reinterpret_cast<int *>(SubCmd->RecvBuf) = Bsn->GetVsa().GetAnalyzeErrResult();
        SubCmd->RecvDataLen = sizeof(int);
        break;
    case SUB_CMD_SET_SNC_CAL_SELF_START:
        Ret = SncCalMgr::Instance().Start(*reinterpret_cast<int *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_SNC_CAL_SELF_STOP:
        Ret = SncCalMgr::Instance().Stop();
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_SNC_CAL_SELF_QUERY:
        *reinterpret_cast<int *>(SubCmd->RecvBuf) = SncCalMgr::Instance().Query();
        SubCmd->RecvDataLen = sizeof(int);
        break;
    case SUB_CMD_SET_VSG_FAST_ACTINON_POWER:
        Ret = Bsn->GetVsg().SetPowerParamToMod(*reinterpret_cast<double *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_VSG_FAST_ACTINON_PARHLOSS:
        Ret = Bsn->GetVsg().SetPathLossParamToMod(*reinterpret_cast<double *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_BROADCAST_TIMEOUT:
        BroadcastVsg::Instance().SetTimeout(*reinterpret_cast<int *>(SubCmd->SendBuf));
        Ret = WT_OK;
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_VSA_AGC_SAMPLING_TIME:
        Bsn->GetVsa().SetVsaAGCSamplingTime(*reinterpret_cast<double *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_GET_VSA_AGC_SAMPLING_TIME:
        *reinterpret_cast<double *>(SubCmd->RecvBuf) = Bsn->GetVsa().GetVsaAGCSamplingTime();
        SubCmd->RecvDataLen = sizeof(double);
        break;
    case SUB_CMD_SET_VSA_TIME_DOMIAN_IQ_FORCE_ENABLE:
        Ret = Bsn->GetVsa().SetVsaDomianIQForceEnable(*reinterpret_cast<int *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_VSG_TIME_DOMIAN_IQ_FORCE_ENABLE:
        Ret = Bsn->GetVsg().SetVsgDomianIQForceEnable(*reinterpret_cast<int *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    case SUB_CMD_SET_VSG_FAST_ACTINON_FREQ:
        Ret = Bsn->GetVsg().SetFreqParamToMod(*reinterpret_cast<double *>(SubCmd->SendBuf));
        SubCmd->RecvDataLen = 0;
        break;
    default:
        Ret = WT_CMD_NO_HANDLER;
        break;
    }

    if (Ret == WT_OK && SubCmd->RecvDataLen > 0 && Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, SubCmd->RecvBuf, SubCmd->RecvDataLen);
    }
    else if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }

    return WT_OK;
}

int InterProt::SetExtendEVMStatus(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    int Ret = Bsn->GetVsa().SetExtendEVMStatus(Conn, *(int *)(Data));
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::SetVsaIterativeEVMStatus(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    int Status = *(int *)(Data);
    Ret = Bsn->GetVsa().SetIterativeEVMStatus(Conn, Status);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::SetVsaSncEVMStatus(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    int Status = *(int *)(Data);
    Ret = Bsn->GetVsa().SetSncEVMStatus(Conn, Status);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::SetVsaCcEVMStatus(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    int Status = *(int *)(Data);
    Ret = Bsn->GetVsa().SetCcEVMStatus(Conn, Status);
    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    return Ret;
}

int InterProt::NoiseCalStart(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    if (Header->Length < sizeof(int)*8)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    Business *Bsn = Conn->GetBsn();
    int Ret = Bsn->GetVsa().NoiseCalStart((int *)(Data));
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::NoiseCalStop(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = Bsn->GetVsa().NoiseCalStop();
    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetNoiseCalStatus(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Status = 0;
    Business *Bsn = Conn->GetBsn();
    Bsn->GetVsa().GetNoiseCalStatus(Status);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetNoiseCalStatus" << Pout(Status) << endl;
    return Response(Conn->GetSock(), Header, &Status, sizeof(int));
}

int InterProt::GetNoiseCalValid(CmdHeader *Header, void *Data, void *Arg)
{
    int Status[8][8] = {0};
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (Header->Type == TYPE_ACK)
    {
        Ret = Bsn->GetVsa().GetNoiseCalValid(Conn, (int (*)[8])(Data));
    }
    else
    {
        Ret = Bsn->GetVsa().GetNoiseCalValid(Conn, Status);
        if (Conn->NeedRsp())
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"Status :"<<std::endl;
            for(int i = 0;i<8;i++)
            {
                for(int j =0;j<8;j++)
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"["<<Status[i][j]<<"] ";
                }
                WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<std::endl;
            }
            return Ret == WT_OK ? Response(Conn->GetSock(), Header, Status, sizeof(int) * 8 * 8)
                                : Response(Conn->GetSock(), Header, Ret);
        }
    }
    return Ret;
}

int InterProt::SetBroadcastEnable(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Ret = WT_OK;
    if (Header->Length < sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }
    if (*(int *)Data == 1 && !(BroadcastVsg::Instance().IsBroadcastUser()))
    {
        if(License::Instance().CheckBusinessLicItem(WT_BROADCAST) != WT_OK)
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(Ret, "WT_BROADCAST license not exist");
        }
        else
        {
            Ret = BroadcastVsg::Instance().AddVsgUser(Bsn->GetVsgPtr());
        }
    }
    else if (*(int *)Data == 0 && BroadcastVsg::Instance().IsBroadcastUser())
    {
        BroadcastVsg::Instance().DelVsgUser();
    }
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::GetBroadcastEnable(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Ret = WT_OK;
    int Status = 0;
    Status = BroadcastVsg::Instance().IsBroadcastUser();
    Ret = Response(Conn->GetSock(), Header, &Status, sizeof(int));
    return Ret;
}

int InterProt::SetBroadcastDebugEnable(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    BroadcastVsg::Instance().SetBroadcastDebugEnable(*(int *)Data, (double *)((char *)Data + sizeof(int)));
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::GetBroadcastRunStatus(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    int Status = BroadcastVsg::Instance().QueryStatus();
    Ret = Response(Conn->GetSock(), Header, &Status, sizeof(int));
    return Ret;
}


int InterProt::SetlistEnable(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    bool TxFlag;

    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    TxFlag = *static_cast<bool *>(Data);
    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "InterProt::SetlistEnable" << TxFlag << std::endl;
    if (TxFlag == true)
    {
        Ret = Bsn->GetVsa().SetlistEnable();
    }
    else
    {
        Ret = Bsn->GetVsg().SetlistEnable();
    }
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::SetlistDisable(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    bool TxFlag;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    TxFlag = *static_cast<bool *>(Data);
    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "InterProt::SetlistDisable" << TxFlag << std::endl;
    if (TxFlag == true)
    {
        Ret = Bsn->GetVsa().SetlistDisable();
    }
    else
    {
        Ret = Bsn->GetVsg().SetlistDisable();
    }
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::SetlistSegVsaAlzParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;
    int Type;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    Type = *static_cast<int *>(Data);
    SegNo = *(static_cast<int *>(Data) + 1);
    Ret = Bsn->GetVsa().SetlistSegAlzParam(Type, SegNo, static_cast<int *>(Data) + 2, Header->Length - sizeof(int) * 2);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaAllAlzComParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    AlzParamComm *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(AlzParamComm)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegVsaAllAlzComParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(AlzParamComm);
    Tmp = (AlzParamComm *)Data;

    for(SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsa().SetlistSegAlzParam(WT_ALZ_PARAM_COMMON, SegNo, &Tmp[SegNo], sizeof(AlzParamComm));
    }

    return Response(Conn->GetSock(), Header, Ret);
}


int InterProt::SetlistSegVsaAllAlzProParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    AnalyzeParam *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(AnalyzeParam)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegVsaAllAlzProParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(AnalyzeParam);
    Tmp = (AnalyzeParam *)Data;

    for(SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsa().SetlistSegAlzParam(Tmp[SegNo].AlzType, SegNo, &Tmp[SegNo], Tmp[SegNo].AlzSize);
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaCapParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    SegNo = *static_cast<int *>(Data);
    Ret = Bsn->GetVsa().SetlistSegCapParam(SegNo, (VsaParam *)(static_cast<int *>(Data) + 1));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegAllVsaCapParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    VsaParam *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(VsaParam)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegAllVsaCapParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(VsaParam);
    Tmp = (VsaParam *)Data;

    for(SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsa().SetlistSegCapParam(SegNo, &Tmp[SegNo]);
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaTrigParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    SegNo = *static_cast<int *>(Data);
    Ret = Bsn->GetVsa().SetlistSegTrigParam(SegNo, (VsaTrigParam *)(static_cast<int *>(Data) + 1));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegAllVsaTrigParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    VsaTrigParam *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(VsaTrigParam)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegAllVsaTrigParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(VsaTrigParam);
    Tmp = (VsaTrigParam *)Data;

    for (SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsa().SetlistSegTrigParam(SegNo, &Tmp[SegNo]);
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegTrigCommParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;
    bool TxFlag;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    TxFlag = *static_cast<bool *>(Data);
    SegNo = *((int *)(static_cast<bool *>(Data) + 1));

    if (TxFlag == true)
    {
        Ret = Bsn->GetVsa().SetlistSegTrigCommParam(SegNo, (TriggerCommonParam *)(static_cast<char *>(Data) + sizeof(bool) + sizeof(int)));
    }
    else
    {
        Ret = Bsn->GetVsg().SetlistSegTrigCommParam(SegNo, (TriggerCommonParam *)(static_cast<char *>(Data) + sizeof(bool) + sizeof(int)));
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegAllTrigCommParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int SegCnt;
    bool TxFlag;
    TriggerCommonParam *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (((Header->Length - (signed)sizeof(bool)) % sizeof(TriggerCommonParam)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegAllTrigCommParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    SegCnt = (Header->Length - (signed)sizeof(bool)) / sizeof(TriggerCommonParam);
    TxFlag = *static_cast<bool *>(Data);
    Tmp = (TriggerCommonParam *)(static_cast<char *>(Data) + sizeof(bool));

    if (TxFlag == true)
    {
        for (SegNo = 0; SegNo < SegCnt; SegNo++)
        {
            Ret = Bsn->GetVsa().SetlistSegTrigCommParam(SegNo, &Tmp[SegNo]);
        }
    }
    else
    {
        for (SegNo = 0; SegNo < SegCnt; SegNo++)
        {
            Ret = Bsn->GetVsg().SetlistSegTrigCommParam(SegNo, &Tmp[SegNo]);
        }
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegTimeParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;
    bool TxFlag;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    TxFlag = *static_cast<bool *>(Data);
    SegNo = *((int *)(static_cast<bool *>(Data) + 1));

    if (TxFlag == true)
    {
        Ret = Bsn->GetVsa().SetlistSegTimeParam(SegNo, (SeqTimeParam *)(static_cast<char *>(Data) + sizeof(bool) + sizeof(int)));
    }
    else
    {
        Ret = Bsn->GetVsg().SetlistSegTimeParam(SegNo, (SeqTimeParam *)(static_cast<char *>(Data) + sizeof(bool) + sizeof(int)));
    }

    return Response(Conn->GetSock(), Header, Ret);
}
int InterProt::SetlistSegAllTimeParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int SegCnt;
    bool TxFlag;
    SeqTimeParam *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if (((Header->Length - (signed)sizeof(bool)) % sizeof(SeqTimeParam)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegAllTimeParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    SegCnt = (Header->Length - (signed)sizeof(bool)) / sizeof(SeqTimeParam);
    TxFlag = *static_cast<bool *>(Data);
    Tmp = (SeqTimeParam *)(static_cast<char *>(Data) + sizeof(bool));

    if (TxFlag == true)
    {
        for (SegNo = 0; SegNo < SegCnt; SegNo++)
        {
            Ret = Bsn->GetVsa().SetlistSegTimeParam(SegNo, &Tmp[SegNo]);
        }
    }
    else
    {
        for (SegNo = 0; SegNo < SegCnt; SegNo++)
        {
            Ret = Bsn->GetVsg().SetlistSegTimeParam(SegNo, &Tmp[SegNo]);
        }
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaSeqStart(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    double TrigerOffset = 0;

    TrigerOffset = *static_cast<double *>(Data);

    int Ret = Bsn->GetVsa().SetlistSegVsaSeqStart(TrigerOffset);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsgSeqStart(CmdHeader *Header, void *Data, void *Arg)
{
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    int Repet = 0;
    int EnableFlag = 0;
    int IncrementFlag = 0;
    int CellMod = 0;

    Repet = *static_cast<int *>(Data);
    EnableFlag = *(static_cast<int *>(Data) + 1);
    IncrementFlag = *(static_cast<int *>(Data) + 2);
    CellMod = *(static_cast<int *>(Data) + 3);

    int Ret = Bsn->GetVsg().SetlistSegVsgSeqStart(Repet, EnableFlag, IncrementFlag, CellMod);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsgParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    SegNo = *static_cast<int *>(Data);
    Ret = Bsn->GetVsg().SetlistSegVsgParam(SegNo, (VsgParam *)(static_cast<int *>(Data) + 1));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegAllVsgParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    VsgParam *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(VsgParam)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegAllVsgParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(VsgParam);
    Tmp = (VsgParam *)Data;

    for(SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsg().SetlistSegVsgParam(SegNo, &Tmp[SegNo]);
    }

    return Response(Conn->GetSock(), Header, Ret);

}

int InterProt::SetlistSegVsgSyncParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;
    int Status;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    SegNo = *static_cast<int *>(Data);
    Status = *(static_cast<int *>(Data) + 1);
    Ret = Bsn->GetVsg().SetlistSegVsgSyncParam(SegNo, Status);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsgAllSyncParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    int *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(int)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegVsgAllSyncParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(int);
    Tmp = (int *)Data;

    for(SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsg().SetlistSegVsgSyncParam(SegNo, Tmp[SegNo]);
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsgWaveParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    int SegNo;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    SegNo = *static_cast<int *>(Data);
    Ret = Bsn->GetVsg().SetlistSegVsgWaveParam(SegNo, (ExtPnItem *)(static_cast<int *>(Data) + 1));

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegAllVsgWaveParam(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    int SegNo;
    int Cnt;
    ExtPnItem *Tmp;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Length % sizeof(ExtPnItem)) != 0)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "SetlistSegVsgAllSyncParam cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    Cnt = Header->Length / sizeof(ExtPnItem);
    Tmp = (ExtPnItem *)Data;

    for(SegNo = 0; SegNo < Cnt; SegNo++)
    {
        Ret = Bsn->GetVsg().SetlistSegVsgWaveParam(SegNo, &Tmp[SegNo]);
    }

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaVsgSeqStart(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsa().SetlistSegVsaSeqStart(0);
    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    Ret = Bsn->GetVsg().SetlistSegVsgSeqStart(0, 0, 0, 0);

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaSeqStop(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsa().SetlistSegVsaSeqStop();

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsgSeqStop(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsg().SetlistSegVsgSeqStop();

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::SetlistSegVsaVsgSeqStop(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    int Ret = Bsn->GetVsa().SetlistSegVsaSeqStop();
    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    Ret = Bsn->GetVsg().SetlistSegVsgSeqStop();

    return Response(Conn->GetSock(), Header, Ret);
}

int InterProt::GetlistSegVsaVsgSeqState(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    bool TxFlag;
    int State = 0;

    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    TxFlag = *static_cast<bool *>(Data);

    if (TxFlag == true)
    {
        Ret = Bsn->GetVsa().GetlistSegVsaSeqState(State);
    }
    else
    {
        Ret = Bsn->GetVsg().GetlistSegVsgSeqState(State);
    }

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, &State, sizeof(State));
    }
}

int InterProt::GetListTxSeqAllCapState(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret;
    int SegNo[2] = {0};
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    Ret = Bsn->GetVsa().GetListTxSeqAllCapState(SegNo);

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, SegNo, sizeof(SegNo));
    }
}

int InterProt::GetListTxSeqAllAnalyState(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret;
    int SegNo[2] = {0};
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    Ret = Bsn->GetVsa().GetListTxSeqAllAnalyState(SegNo);

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, SegNo, sizeof(SegNo));
    }
}

int InterProt::GetListRxSeqAllTransState(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret;
    int SegNo[2] = {0};
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    Ret = Bsn->GetVsg().GetListRxSeqAllTransState(SegNo);

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, SegNo, sizeof(SegNo));
    }
}

int InterProt::GetListTxSeqAllPowerResult(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    std::unique_ptr<double []> Power;
    int SegNum = 0;
    double *PowerResult = nullptr;

    SegNum = *static_cast<int *>(Data);

    Power.reset(new double[SegNum]);
    PowerResult = Power.get();

    Ret = Bsn->GetVsa().GetListTxSeqAllPowerResult(PowerResult, SegNum);

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, PowerResult, sizeof(double) * SegNum);
    }
}

int InterProt::SetListTxSeqVsaClear(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;

    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    Ret = Bsn->GetVsa().SetListTxSeqVsaClear();
    Ret = Response(Conn->GetSock(), Header, Ret);
    return Ret;
}

int InterProt::GetListLteTxSeqAllSegStat(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    std::unique_ptr<int []> SegStat;
    int SegNum = 0;
    int *SegStatResult = nullptr;

    SegNum = *static_cast<int *>(Data);

    SegStat.reset(new int[SegNum]);
    SegStatResult = SegStat.get();

    Ret = Bsn->GetVsa().GetListTxSeqAllSegStat(SegStatResult, SegNum);

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, SegStatResult, sizeof(int) * SegNum);
    }
}

int InterProt::DuplexSetState(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    if ((Header->Type == TYPE_ACK) && Header->Length != sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    int Ret = Bsn->GetVsa().DuplexSetState(Conn, *static_cast<int*>(Data));

    if (Conn->NeedRsp())
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return WT_OK;
    }
}

int InterProt::DuplexGetState(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret;
    int enable = 0;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();

    Ret = Bsn->GetVsa().DuplexGetState(Conn, &enable);

    if (Ret != WT_OK)
    {
        return Response(Conn->GetSock(), Header, Ret);
    }
    else
    {
        return Response(Conn->GetSock(), Header, (void *)&enable, sizeof(enable));
    }
}

int InterProt::SendVsaListResult(WRSocket &Sock, AckHeader *Header, const int RstSzie, const vector<VsaResult> &Result)
{
    int TxLen = 0;
    int Ret = WT_OK;

    Ret = Sock.Send(Header, sizeof(*Header), TxLen);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = Sock.Send(&RstSzie, sizeof(int), TxLen);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    for (const auto &Item : Result)
    {

        if (Item.Data != NULL && Item.DataLen) //若数据不为空，才发送数据
        {
            Ret = Sock.Send(Item.Data, Item.DataLen, TxLen);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }
    }

    return WT_OK;
}

int InterProt::GetListAllAlzData(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WTConnector *Conn = static_cast<WTConnector *>(Arg);
    Business *Bsn = Conn->GetBsn();
    vector<VsaResult> Result;
    void *Buf = nullptr;
    int Size;
    int DataType;
    int Total = 0;
    int SegNo;
    int SegCnt;

    if (Header->Length < VSA_RESULT_NAME_LEN)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "GetListAllAlzData cmd length error");
        return Response(Conn->GetSock(), Header, WT_CMD_ERROR);
    }

    string RstType = (char *)Data;
    SegCnt = Bsn->GetVsa().GetListSegNum();

    for (SegNo = 0; SegNo < SegCnt; SegNo++)
    {
        Ret = Bsn->GetVsa().GetAlzData(RstType, 0, 0, SegNo, &Buf, Size, DataType);
        if (Ret != WT_OK)
        {
            return Response(Conn->GetSock(), Header, Ret);
        }
        Total += Size;
        Result.push_back({Size, DataType, Buf});
    }

    AckHeader Ack(Header, sizeof(int) * 2 + Total, WT_OK);

    Ret = SendVsaListResult(Conn->GetSock(), &Ack, Total, Result);
    Bsn->GetVsa().FreeAlzData();

    return Ret;
}
