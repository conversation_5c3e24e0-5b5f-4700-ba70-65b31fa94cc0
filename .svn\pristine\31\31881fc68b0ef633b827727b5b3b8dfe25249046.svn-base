#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "vsahandler.h"
#include "vsghandler.h"
#include "commonhandler.h"
#include "internal.h"
#include "tester.h"
#include "tester_mt.h"
#include "scpi_monitor.h"
#include "diskfun.h"
#include "wterror.h"
#include "wtlog.h"
#include "scpi_3gpp_base.h"
#include "../../general/protocolsub.h"
#include "listmod_sequence.h"

using namespace std;

static scpi_result_t GetRstDoubleData(scpi_t *context, const char *ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, ParamStr, &Result, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, Result);

    return SCPI_RES_OK;
}

static scpi_result_t GetRstIntData(scpi_t *context, const char *ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, ParamStr, &Result, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, (int)Result);

    return SCPI_RES_OK;
}

static scpi_result_t GetVsgRstDoubleVectorDataLite(scpi_t *context, const char *ParamStr, int litetype)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);
    
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    if (0 == ResponseReadyLiteResult(context, litetype, streamID, segmentID))
    {
        return SCPI_RES_OK;
    }

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    iRet = WT_GetVSGDataResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVSGDataResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    ResultSize = ElementSize * ElementCount;
    ResultBuf.reset(new (std::nothrow) char[ResultSize]);
    iRet = WT_GetVSGDataVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    int smp = 0;
    iRet = WT_GetVSGDataVectorResult(attr->ConnID, WT_RES_SMP_FREQ, &smp, sizeof(smp), streamID, segmentID);
    IF_ERR_RETURN(iRet);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Get VSG sample rate = " << smp << std::endl;
    if (ElementSize > 8)
    {
        return RstComplexVectorLite(
            context,
            smp,
            (Complex *)ResultBuf.get(),
            ElementCount,
            streamID,
            segmentID,
            litetype);
    }
    return RstDoubleVectorLite(
        context,
        smp,
        (double *)ResultBuf.get(),
        ElementCount,
        streamID,
        segmentID,
        litetype);
}

static scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb = true)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    iRet = WT_GetVSGDataResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVSGDataResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    ResultSize = ElementSize * ElementCount;
    ResultBuf.reset(new (std::nothrow) char[ResultSize]);
    iRet = WT_GetVSGDataVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    if(arb)
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }
    else
    {
        int DoubleCnt = ResultSize / sizeof(double);
        for (int i = 0; i < DoubleCnt; i++)
        {
            double *TmpData = (double *)ResultBuf.get();
            double Value = *(double *)(TmpData + i);
            SCPI_ResultDouble(context, Value);
        }
    }
    return SCPI_RES_OK;
}

scpi_result_t SetVsgStartAsy(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->m_WTTimer.StartTimer("WT_AsynStartVSG");
    int iRet = WT_AsynStartVSG(attr->ConnID);
    attr->m_WTTimer.StopTimer("WT_AsynStartVSG");

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgStop(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->m_WTTimer.StartTimer("WT_StopVSG");
    int iRet = WT_StopVSG(attr->ConnID);
    attr->m_WTTimer.StopTimer("WT_StopVSG");

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgSampleRate(scpi_t * context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > MAX_SMAPLE_RATE_API || ParamVal.value < 0);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgParam.SamplingFreq = ParamVal.value;
        attr->NeedSetVSGPattern = true;
        WT_ClearSampRateFromFileFlag(attr->ConnID);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG SamplingFreq=" << attr->vsgParam.SamplingFreq / MHz_API << " MHz" << endl;

    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgFreq(scpi_t * context)
{
    scpi_number_t Freq[2];
    EMPTY_PARAM_ERROR(context);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < 2; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &Freq[i], true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(Freq[i].value > 8000 * MHz_API || Freq[i].value < 0) //频点设置大于8G或者小于0，视为超范围
    }

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    if (context->parser_state.numberOfParameters > 1)
    {
        attr->vsgParam.Freq = Freq[0].value;
        attr->vsgParam.Freq2 = Freq[1].value;
    }
    else
    {
        attr->vsgParam.Freq = Freq[0].value;
        attr->vsgParam.Freq2 = 0.0;
    }
    
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Freq=" << attr->vsgParam.Freq / MHz_API << "MHz," << "Freq2=" << attr->vsgParam.Freq2 / MHz_API << "MHz" << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgFreqFast(scpi_t * context)
{
    scpi_number_t Freq[2];
    EMPTY_PARAM_ERROR(context);
    int Ret = WT_ERR_CODE_OK;

    for (int i = 0; i < context->parser_state.numberOfParameters && i < 2; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &Freq[i], true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(Freq[i].value > 8000 * MHz_API || Freq[i].value < 0) //频点设置大于8G或者小于0，视为超范围
    }

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    if (context->parser_state.numberOfParameters > 1)
    {
        attr->vsgParam.Freq = Freq[0].value;
        attr->vsgParam.Freq2 = Freq[1].value;
    }
    else
    {
        attr->vsgParam.Freq = Freq[0].value;
        attr->vsgParam.Freq2 = 0.0;
    }

    SubCmdType SubCmd;
    SubCmd.Cmd = SUB_CMD_SET_VSG_FAST_ACTINON_FREQ;
    SubCmd.SendBuf = reinterpret_cast<char *>(&attr->vsgParam.Freq);
    SubCmd.SendDataLen = sizeof(attr->vsgParam.Freq);
    SubCmd.RecvBuf = nullptr;
    SubCmd.RecvBufLen = 0;
    Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetVsgFreqFast Freq=" << attr->vsgParam.Freq / MHz_API << "MHz" << endl;
    IF_ERR_RETURN(Ret);
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgPower(scpi_t * context)
{
    scpi_number_t power[WT_SUB_TESTER_INDEX_MAX];
    EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &power[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsgParam.Power[i] = power[i].value;
    }

    
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Power=" << attr->vsgParam.Power[0] << " dBm" << endl;

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgPowerFast(scpi_t * context)
{
    scpi_number_t power[WT_SUB_TESTER_INDEX_MAX];
    EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &power[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsgParam.Power[i] = power[i].value;
    }
    
    int Ret = WT_ERR_CODE_OK;
    SubCmdType SubCmd;
    SubCmd.Cmd = SUB_CMD_SET_VSG_FAST_ACTINON_POWER;
    SubCmd.SendBuf = reinterpret_cast<char *>(&attr->vsgParam.Power[0]);
    SubCmd.SendDataLen = sizeof(attr->vsgParam.Power[0]);
    SubCmd.RecvBuf = nullptr;
    SubCmd.RecvBufLen = 0;
    Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Power=" << attr->vsgParam.Power[0] << " dBm" << endl;
    IF_ERR_RETURN(Ret);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgPort(scpi_t * context)
{
    int port[WT_SUB_TESTER_INDEX_MAX] = { WT_PORT_RF1 };
    EMPTY_PARAM_ERROR(context);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamInt(context, &port[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsgParam.RfPort[i] = port[i];
        ILLEGAL_PARAM_RETURN(port[i] < WT_PORT_OFF || port[i] >= WT_PORT_MAX);
        if (attr->m_fullDuplexEnable)
        {
            attr->vsaParam.RfPort[i] = port[i];
        }
    }

    
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RfPort=" << port[0] << endl;

    return SCPI_ResultOK(context);
}

scpi_result_t SaveVsgWave(scpi_t *context)
{
    char fileName[256] = {0};
    size_t copyLen = 0;
    int iRet = SCPI_RES_ERR;
    const char *data = nullptr;
    size_t len = 0;
    int wave2Flag = 0;

    do
    {
        if(Diskfun::Instance().IsDisableSaveSignal() == true)       // 判断硬盘容量是否允许保存
        {
            WTLog::Instance().LOGERR(WT_DISK_CAPACITY_ERROR, "scpi_Disk capacity is too less");
            iRet = WT_DISK_CAPACITY_ERROR;
            return SCPI_ResultOK(context, iRet);
        } 

        // Name,wave2flag,file data
        if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
        {
            break;
        }

        if (!SCPI_ParamInt(context, &wave2Flag, true))
        {
            break;
        }

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            break;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ARB data len = " << len << endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WaveName=" << fileName << endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Wave2 Flag=" << wave2Flag << endl;

        if (len > 0)
        {
            string SaveName(fileName);
            string firstName(fileName);
            string extName(".bwv");
            const string dirName(SCPI_WaveDir());

            size_t pos = SaveName.find_last_of(".");
            struct timeval now;
            double timeUs = 0.0;
            if (string::npos != pos)
            {
                firstName = SaveName.substr(0, pos);
                extName = SaveName.substr(pos);
            }
            make_file_dir(SCPI_WaveDir(), fileName);
            // make_file_dir(SCPI_LowWaveDir(), fileName);

            gettimeofday(&now, nullptr);
            timeUs = now.tv_sec * 1e6 + now.tv_usec;

            sprintf(fileName, "%s%s_%f_%s", dirName.c_str(), firstName.c_str(), timeUs, extName.c_str());
            // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Save File Name = " << fileName << endl;
            iRet = WriteFile(fileName, data, len);

            // remove bwv/csv file
            if (0 == access(fileName, R_OK))
            {
                char cmd_char[1024] = {0};
                sprintf(cmd_char, "mv '%s' '%s%s%s'", fileName, dirName.c_str(), firstName.c_str(), extName.c_str());
                do_system_cmd(cmd_char);
            }
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgWaveName(scpi_t * context)
{
    char buf[256] = { 0 };
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    std::vector<std::string> low_name_list;

    for (int i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamCopyText(context, buf, sizeof(buf) - 1, &copyLen, true))
        {
            break;
        }

        iRet = check_waveform_exist_v2(context, buf, low_name_list);
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        VsgPattern vsgPatternTemp = attr->vsgPattern.front();
        if (i == 0)
        {
            // remove_all_vsg_low(attr);
            attr->vsgPattern.clear();
        }
        for(int j = 0; j < low_name_list.size(); j++)
        {
            memset(vsgPatternTemp.WaveName, 0, MAX_NAME_SIZE);
            strcpy(vsgPatternTemp.WaveName, low_name_list[j].c_str());
            vsgPatternTemp.WaveType = SIG_USERFILE;
            if (i < attr->m_FrameDelay.size())
            {
                vsgPatternTemp.StartDelay = attr->m_FrameDelay[i];
            }
            attr->vsgPattern.push_back(move(vsgPatternTemp));
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgPattern WaveName=" << buf << ", low name = " << low_name_list[j] << endl;
        }
        attr->NeedSetVSGPattern = true;
    }
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgRepeat(scpi_t * context)
{
    int repeatCnt = 1;
    do
    {
        if (!SCPI_ParamInt(context, &repeatCnt, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for (auto &Pattern : attr->vsgPattern)
        {
            Pattern.Repeat = repeatCnt;
        }
        attr->vsgParam.Repeat = repeatCnt;
        attr->NeedSetVSGPattern= true;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "repeatCnt=" << repeatCnt << endl;
    } while (0);

    return SCPI_ResultOK(context);
}


static void VsgAutoPowerCorrect(SPCIUserParam *attr)
{
    const double EPSILON = 1e-6;
    bool flag = false;
    bool flag2 = false;
    u32 j;

    for (int i = 0; attr->m_vsgAutoPowerCorrectFileName[i].length() > 0 && i < attr->m_vsgAutoPowerCorrectFileName.size(); ++i)
    {
        if (attr->m_VsgFreqPathLosTbl[i].size() > 0)
        {
            for (j = 0; j < attr->m_VsgFreqPathLosTbl[i].size() - 1; j++)
            {
                if (j == 0 && CompareDouble(attr->vsgParam.Freq, attr->m_VsgFreqPathLosTbl[i][j].Freq, EPSILON) == -1)
                {
                    attr->vsgParam.ExtPathLoss[i] = attr->m_VsgFreqPathLosTbl[i][j].PathLos;
                    flag = true;
                    if (flag2  == true)
                    {
                       break;
                    }
                }
                else if ((CompareDouble(attr->vsgParam.Freq, attr->m_VsgFreqPathLosTbl[i][j].Freq, EPSILON) == 1 || CompareDouble(attr->vsgParam.Freq, attr->m_VsgFreqPathLosTbl[i][j].Freq, EPSILON) == 0)
                    && (CompareDouble(attr->vsgParam.Freq, attr->m_VsgFreqPathLosTbl[i][j + 1].Freq, EPSILON) == -1 || CompareDouble(attr->vsgParam.Freq, attr->m_VsgFreqPathLosTbl[i][j + 1].Freq, EPSILON) == 0))
                {
                    if (CompareDouble(attr->m_VsgFreqPathLosTbl[i][j].Freq, attr->m_VsgFreqPathLosTbl[i][j + 1].Freq, EPSILON) != 0)
                    {
                        attr->vsgParam.ExtPathLoss[i] = attr->m_VsgFreqPathLosTbl[i][j].PathLos + (attr->vsgParam.Freq - attr->m_VsgFreqPathLosTbl[i][j].Freq)
                            * (attr->m_VsgFreqPathLosTbl[i][j + 1].PathLos - attr->m_VsgFreqPathLosTbl[i][j].PathLos) / (attr->m_VsgFreqPathLosTbl[i][j + 1].Freq - attr->m_VsgFreqPathLosTbl[i][j].Freq);
                    }
                    else
                    {
                        attr->vsgParam.ExtPathLoss[i] = (attr->m_VsgFreqPathLosTbl[i][j].PathLos + attr->m_VsgFreqPathLosTbl[i][j + 1].PathLos) / 2;
                    }
                    flag = true;
                    if (flag2  == true)
                    {
                       break;
                    }
                }

                if (j == 0 && CompareDouble(attr->vsgParam.Freq2, attr->m_VsgFreqPathLosTbl[i][j].Freq, EPSILON) == -1)
                {
                    attr->vsgParam.ExtPathLoss2[i] = attr->m_VsgFreqPathLosTbl[i][j].PathLos;
                    flag2 = true;
                    if (flag  == true)
                    {
                       break;
                    }
                }
                else if ((CompareDouble(attr->vsgParam.Freq2, attr->m_VsgFreqPathLosTbl[i][j].Freq, EPSILON) == 1 || CompareDouble(attr->vsgParam.Freq2, attr->m_VsgFreqPathLosTbl[i][j].Freq, EPSILON) == 0)
                    && (CompareDouble(attr->vsgParam.Freq2, attr->m_VsgFreqPathLosTbl[i][j + 1].Freq, EPSILON) == -1 || CompareDouble(attr->vsgParam.Freq2, attr->m_VsgFreqPathLosTbl[i][j + 1].Freq, EPSILON) == 0))
                {
                    if (CompareDouble(attr->m_VsgFreqPathLosTbl[i][j].Freq, attr->m_VsaFreqPathLosTbl[i][j + 1].Freq, EPSILON) != 0)
                    {
                        attr->vsgParam.ExtPathLoss2[i] = attr->m_VsgFreqPathLosTbl[i][j].PathLos + (attr->vsgParam.Freq - attr->m_VsgFreqPathLosTbl[i][j].Freq)
                            * (attr->m_VsgFreqPathLosTbl[i][j + 1].PathLos - attr->m_VsgFreqPathLosTbl[i][j].PathLos) / (attr->m_VsgFreqPathLosTbl[i][j + 1].Freq - attr->m_VsgFreqPathLosTbl[i][j].Freq);
                    }
                    else
                    {
                        attr->vsgParam.ExtPathLoss2[i] = (attr->m_VsgFreqPathLosTbl[i][j].PathLos + attr->m_VsgFreqPathLosTbl[i][j + 1].PathLos) / 2;
                    }
                    flag2 = true;
                    if (flag  == true)
                    {
                       break;
                    }
                }

            }
            if (j == attr->m_VsgFreqPathLosTbl[i].size() - 1)
            {
                if (flag == false) {
                    attr->vsgParam.ExtPathLoss[i] = attr->m_VsgFreqPathLosTbl[i][j].PathLos;
                }
                if (flag2 == false) {
                    attr->vsgParam.ExtPathLoss2[i] = attr->m_VsgFreqPathLosTbl[i][j].PathLos;
                }
            }
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsgAutoPowerCorrect ExtGain = " << attr->vsgParam.ExtPathLoss[i] << "VsgAutoPowerCorrect ExtGain2 = " << attr->vsgParam.ExtPathLoss2[i] << std::endl;
        }
    }
}

scpi_result_t SetVsgStart(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = WT_ERR_CODE_OK;
    int vsgStatu = WT_ERR_CODE_VSG_ERR;
    do
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgParam.SamplingFreq = " << attr->vsgParam.SamplingFreq <<endl;
        attr->m_WTTimer.StartTimer("WT_UpdateVsgParam");
        iRet = WT_UpdateVsgParam(attr->ConnID, &attr->vsgParam, nullptr);
        attr->m_WTTimer.StopTimer("WT_UpdateVsgParam");
        IF_BREAK(iRet);

        for (auto &Pn : attr->vsgPattern)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(Pn.WaveType) << Pout(Pn.WaveName) << endl;
        }
        attr->m_WTTimer.StartTimer("WT_SetVSGFemParameter");
        iRet = WT_SetVSGFemParameter(attr->ConnID, &attr->m_Devm);
        attr->m_WTTimer.StopTimer("WT_SetVSGFemParameter");
        IF_BREAK(iRet);
        if (attr->NeedSetVSGPattern)
        {
            attr->m_WTTimer.StartTimer("WT_SetVSGPattern");
            iRet = WT_SetVSGPattern(attr->ConnID, attr->vsgPattern.data(), attr->vsgPattern.size());
            attr->m_WTTimer.StopTimer("WT_SetVSGPattern");
            if (!attr->m_fullDuplexEnable)
            {
                IF_BREAK(iRet);
                attr->NeedSetVSGPattern = false;
            }
        }
        //自动线衰修正
        VsgAutoPowerCorrect(attr);

        if (attr->m_fullDuplexEnable)
        {
            VsaParameter *para = nullptr;
            if ((attr->m_List != nullptr) && (attr->m_List->GetListModSeqSize(SEQUENCETX) > 0))
            {
                para = (VsaParameter *)(attr->m_List->GetBaseParam(SEQUENCETX, 0));
            }

            attr->m_WTTimer.StartTimer("WT_SetVSA_V2");
            if (para == nullptr)
            {
                iRet = WT_SetVSA_V2(attr->ConnID, &(attr->vsaParam), &attr->vsaExtParam); // vsa在运行时server层会舍弃参数
            }
            else
            {
                iRet = WT_SetVSA_V2(attr->ConnID, para, &attr->vsaExtParam); // vsa在运行时server层会舍弃参数
            }

            attr->m_WTTimer.StopTimer("WT_SetVSA_V2");
            // IF_BREAK(iRet);
            // attr->m_WTTimer.StartTimer("WT_GetVSAParameter_V2");
            // iRet = WT_GetVSAParameter_V2(attr->ConnID, 0, &attr->vsaParam, &attr->vsaExtParam);
            // attr->m_WTTimer.StopTimer("WT_GetVSAParameter_V2");
            // IF_BREAK(iRet);
        }

        attr->m_WTTimer.StartTimer("WT_SetVSGParameter_V2");
        iRet = WT_SetVSGParameter_V2(attr->ConnID, &attr->vsgParam, &attr->vsgExtParam);
        attr->m_WTTimer.StopTimer("WT_SetVSGParameter_V2");
        IF_BREAK(iRet);
        attr->m_WTTimer.StartTimer("WT_GetVSGParameter_V2");
        iRet = WT_GetVSGParameter_V2(attr->ConnID, &attr->vsgParam, &attr->vsgExtParam);
        attr->m_WTTimer.StopTimer("WT_GetVSGParameter_V2");
        IF_BREAK(iRet);

        //发送vsg参数到监视机
        shared_ptr<Monitor> MonitorObj = MonitorMgr::Instance().GetMonitorByPort(attr->GetMoniPort(true));
        if (MonitorObj)
        {
            vector<PnItemHead_API> PnItemHead;
            //发送pn配置到监视机
            MonitorObj->SendPnParam(attr->vsgPattern, PnItemHead);
            //发送vsa参数到监视机
            MonitorObj->SendParam((char *)&(attr->vsgParam), sizeof(VsgParameter), MON_VSG_STATUS);
        }
        attr->m_WTTimer.StartTimer("WT_AsynStartVSG");
        iRet = WT_AsynStartVSG(attr->ConnID);
        attr->m_WTTimer.StopTimer("WT_AsynStartVSG");
        IF_BREAK(iRet);

        while (true)
        {
            attr->m_WTTimer.StartTimer("WT_GetCurrVSGStatu");
            iRet = WT_GetCurrVSGStatu(attr->ConnID, &vsgStatu);
            attr->m_WTTimer.StopTimer("WT_GetCurrVSGStatu");
            if (WT_ERR_CODE_OK != iRet)
            {
                break;
            }
            if (WT_VSG_VSA_STATE_DONE == vsgStatu || WT_VSG_VSA_STATE_RUNNING == vsgStatu)
            {
                iRet = WT_ERR_CODE_OK;
                break;
            }
            else if (WT_VSG_VSA_STATE_ERR_DONE == vsgStatu)
            {
                iRet = WT_ERR_CODE_VSG_ERR;
                break;
            }
            else
            {
                iRet = WT_ERR_CODE_GENERAL_ERROR;
                break;
            }
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgTimeOut(scpi_t * context)
{
    scpi_number_t timeOut;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &timeOut, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgParam.TimeoutWaiting = timeOut.value;

        
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "TimeoutWaiting=" << attr->vsgParam.TimeoutWaiting << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgWaveGap(scpi_t * context)
{
    scpi_number_t gap;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &gap, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for (auto &Pattern : attr->vsgPattern)
        {
            Pattern.Wave_gap = gap.value;
        }
        attr->vsgParam.Wave_gap = gap.value;
        attr->NeedSetVSGPattern= true;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Wave_gap=" << gap.value << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgWaveGapMode(scpi_t *context)
{
    scpi_number_t gapMode;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &gapMode, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        //数字IQ模式才允许配置随机IFG
        if (TESTER_RUN_DIGIT_IQ != attr->TesterMajorMode && gapMode.value == 1)
        {
            return SCPI_RES_ERR;
        }
        
        for (auto &Pattern : attr->vsgPattern)
        {
            Pattern.Wave_gap = (gapMode.value == 1 ? -1 : Pattern.Wave_gap);
        }
        attr->vsgParam.IFGRamdomMode = gapMode.value;
        attr->NeedSetVSGPattern = true;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Wave_gapMode=" << gapMode.value << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgRandomWaveGapMax(scpi_t * context)
{
    scpi_number_t gap;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &gap, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for (auto &Pattern : attr->vsgPattern)
        {
            Pattern.RandomWaveGapMax = gap.value;
        }
        attr->vsgParam.RandomWaveGapMax = gap.value;
        attr->NeedSetVSGPattern= true;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Wave_gap max=" << gap.value << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgRandomWaveGapMin(scpi_t *context)
{
    scpi_number_t gap;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &gap, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for (auto &Pattern : attr->vsgPattern)
        {
            Pattern.RandomWaveGapMin = gap.value;
        }
        attr->vsgParam.RandomWaveGapMin = gap.value;
        attr->NeedSetVSGPattern = true;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Wave_gap min=" << gap.value << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgExtGain(scpi_t *context)
{
    scpi_number_t gain[WT_SUB_TESTER_INDEX_MAX];
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    EMPTY_PARAM_ERROR(context);
    int32_t ID = 1;
    double *pathLoss = nullptr;

    SCPI_CommandNumbers(context, &ID, 1);
    if (ID > 2)
    {
        ID = 2;
    }
    if (ID < 1)
    {
        ID = 1;
    }
    pathLoss = (ID <= 1 ? attr->vsgParam.ExtPathLoss : attr->vsgParam.ExtPathLoss2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &gain[i], true))
        {
            return SCPI_RES_ERR;
        }

        pathLoss[i] = gain[i].value;
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ExtPathLoss" << ID << "=" << pathLoss[0] << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgExtGainFast(scpi_t *context)
{
    scpi_number_t gain[WT_SUB_TESTER_INDEX_MAX];
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    EMPTY_PARAM_ERROR(context);
    int32_t ID = 1;
    double *pathLoss = nullptr;

    SCPI_CommandNumbers(context, &ID, 1);
    if (ID > 2)
    {
        ID = 2;
    }
    if (ID < 1)
    {
        ID = 1;
    }
    pathLoss = (ID <= 1 ? attr->vsgParam.ExtPathLoss : attr->vsgParam.ExtPathLoss2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        if (!SCPI_ParamNumber(context, nullptr, &gain[i], true))
        {
            return SCPI_RES_ERR;
        }

        pathLoss[i] = gain[i].value;
    }

    int Ret = WT_ERR_CODE_OK;
    SubCmdType SubCmd;
    SubCmd.Cmd = SUB_CMD_SET_VSG_FAST_ACTINON_PARHLOSS;
    SubCmd.SendBuf = reinterpret_cast<char *>(&pathLoss[0]);
    SubCmd.SendDataLen = sizeof(pathLoss[0]);
    SubCmd.RecvBuf = nullptr;
    SubCmd.RecvBufLen = 0;
    Ret = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Ret = %d\n\n\n\n", Ret);
    IF_ERR_RETURN(Ret);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ExtPathLoss" << ID << "=" << pathLoss[0] << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgDemod(scpi_t * context)
{
    int demod = WT_DEMOD_11AG;
    if (!SCPI_ParamInt(context, &demod, true))
    {
        return SCPI_RES_ERR;
    }
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Demod=" << demod << endl;
    attr->VsgDemode = demod;
    if (IsAlg3GPPStandardType(demod))
    {
        attr->vsgAlzParam.analyzeParam3GPP.Standard = demod;
    }
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgSampleRateMode(scpi_t * context)
{
    int ParamVal = 0;
    if (!SCPI_ParamInt(context, &ParamVal, true))
    {
        return SCPI_RES_ERR;
    }
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    attr->VsgSampleRateMode = ParamVal;
    attr->vsgParam.SamplingFreq = GetRealSampleRate(ParamVal, attr->VsgDemode);
    attr->NeedSetVSGPattern = true;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "###attr->vsgParam.SamplingFreq = " << attr->vsgParam.SamplingFreq <<endl;
    return SCPI_ResultOK(context);
}

scpi_result_t GetVsgFreq(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    

    SCPI_ResultDouble(context, attr->vsgParam.Freq);
    SCPI_ResultDouble(context, attr->vsgParam.Freq2);
    return SCPI_RES_OK;
}

scpi_result_t GetVsgPower(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, attr->vsgParam.Power[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsgActualPower(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    VsgParameter VsgParam;

    int iRet = WT_GetVSGParameter_V2(attr->ConnID, &VsgParam, nullptr);
    IF_ERR_RETURN(iRet);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, VsgParam.Power[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsgPort(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultInt(context, attr->vsgParam.RfPort[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsgWaveGap(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SCPI_ResultDouble(context, attr->vsgParam.Wave_gap);
    return SCPI_RES_OK;
}

scpi_result_t GetVsgRepeat(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    if (attr->vsgPattern.size() > 0)
    {
        SCPI_ResultInt(context, attr->vsgPattern[0].Repeat);
    }
    else
    {
        SCPI_ResultInt(context, 0);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsgTimeOut(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    SCPI_ResultDouble(context, attr->vsgParam.TimeoutWaiting);
    return SCPI_RES_OK;
}

scpi_result_t GetVsgWaveName(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    for (auto &Pattern : attr->vsgPattern)
    {
        SCPI_ResultText(context, (const char *)Pattern.WaveName);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsgExtGain(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    int32_t ID = 1;
    double *pathLoss = nullptr;
    SCPI_CommandNumbers(context, &ID, 1);
    if (ID > 2)
    {
        ID = 2;
    }
    if (ID < 1)
    {
        ID = 1;
    }
    pathLoss = (ID <= 1 ? attr->vsgParam.ExtPathLoss : attr->vsgParam.ExtPathLoss2);
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, pathLoss[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsgStatus(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    //WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "[" << __FUNCTION__ << "," << __LINE__ << "]" << "ID=" << attr->ConnID << endl;
    unsigned int timeout_ms = 0;
    const unsigned int max_timeout_ms = (10 + attr->vsgParam.TimeoutWaiting)*1000;
    //TimeTick tick("GetVsgStatus");

    if (context->parser_state.numberOfParameters > 0)
    {
        SCPI_ParamInt(context, (int*)&timeout_ms, false);
    }

    if (timeout_ms > max_timeout_ms)
    {
        timeout_ms = max_timeout_ms;
    }

    if (timeout_ms > 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetVsgStatus timeout = " << timeout_ms << std::endl;
    }

    auto startTime = std::chrono::high_resolution_clock::now();
    while(true)
    {
        int vsgStatu = WT_VSG_VSA_STATE_DONE;
        int iRet = WT_GetCurrVSGStatu(attr->ConnID, &vsgStatu);
        IF_ERR_RETURN(iRet);

        if (timeout_ms > 0 &&
            (WT_VSG_VSA_STATE_RUNNING == vsgStatu || WT_VSG_VSA_STATE_WAITING == vsgStatu))
        {
            auto endTime = std::chrono::high_resolution_clock::now();
            std::chrono::duration<double, std::micro> time_us = endTime - startTime;
            if ((time_us.count()/1000) >= timeout_ms)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetVsgStatus timeout = " << time_us.count() << " us" << std::endl;
                SCPI_ResultInt(context, vsgStatu);
                break;
            }
            usleep(20);
        }
        else
        {
            if (vsgStatu == WT_VSG_VSA_STATE_ERR_DONE)
            {
                vsgStatu = WT_VSG_VSA_STATE_DONE;
            }
            SCPI_ResultInt(context, vsgStatu);
            break;
        }
    }
    //WTLog::Instance().WriteLog(LOG_DEBUG, "GetVsgStatus Ret = %d\n", vsgStatu);
    return SCPI_RES_OK;
}

scpi_result_t SetVsg8080Mode(scpi_t * context)
{
    int ParamVal = 0;
    if (!SCPI_ParamInt(context, &ParamVal, true))
    {
        return SCPI_RES_ERR;
    } 
    // attr->vsgExtParam.WIFI8080DulPortMode = ParamVal;
    return SCPI_ResultOK(context);
}


scpi_result_t SetVsg8080Port(scpi_t * context)
{
    int port[WT_SUB_TESTER_INDEX_MAX / 2 + 1] = { WT_PORT_RF1 };
    EMPTY_PARAM_ERROR(context);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    ILLEGAL_PARAM_RETURN(0 != context->parser_state.numberOfParameters % 2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        if (!SCPI_ParamInt(context, &port[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsgExtParam.VsgRfPort[i] = port[i];
        ILLEGAL_PARAM_RETURN(port[i] < WT_PORT_OFF || port[i] >= WT_PORT_MAX);
    }

    
    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgExtParam.VsgRfPort" << i << "=" << port[i] << endl;
    }
    

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsg8080Power(scpi_t * context)
{
    double power[WT_SUB_TESTER_INDEX_MAX / 2 + 1] = { 0 };
    EMPTY_PARAM_ERROR(context);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    ILLEGAL_PARAM_RETURN(0 != context->parser_state.numberOfParameters % 2);

    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        if (!SCPI_ParamDouble(context, &power[i], true))
        {
            return SCPI_RES_ERR;
        }
        attr->vsgExtParam.VsgPower[i] = power[i];
    }

    
    for (int i = 0; i < context->parser_state.numberOfParameters && i < WT_SUB_TESTER_INDEX_MAX / 2 + 1; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgExtParam.VsgPower" << i << " = "  << power[i] << endl;
    }

    return SCPI_ResultOK(context);
}


scpi_result_t GetVsg8080Mode(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    
    SCPI_ResultInt(context, attr->vsgExtParam.WIFI8080DulPortMode);

    return SCPI_RES_OK;
}


scpi_result_t GetVsg8080Port(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultInt(context, attr->vsgExtParam.VsgRfPort[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsg8080Power(scpi_t * context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    
    for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
    {
        SCPI_ResultDouble(context, attr->vsgExtParam.VsgPower[i]);
    }
    return SCPI_RES_OK;
}

scpi_result_t SetVsgFreqOffset(scpi_t * context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > 60 * MHz_API || ParamVal.value < -60 * MHz_API) //判断可设置范围

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgParam.FreqOffset = ParamVal.value;
        
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "FreqOffset=" << attr->vsgParam.FreqOffset / MHz_API << " Mhz" << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgWaveFlatnessCalCompensateEnable(scpi_t * context)
{
    int Enable = 1; //默认vsg校准硬件补偿开
    if (!SCPI_ParamInt(context, &Enable, true))
    {
        return SCPI_RES_ERR;
    }

    ILLEGAL_PARAM_RETURN(Enable != 0 && Enable != 1); //判断范围

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Enable=" << Enable << endl;

    int iRet = WT_ERR_CODE_OK;
    iRet = WT_SetWaveCalDataCompensate(attr->ConnID, Enable);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgIQImbCompensateEnable(scpi_t * context)
{
    int Enable = 1; //默认vsg IQ不平衡补偿开
    if (!SCPI_ParamInt(context, &Enable, true))
    {
        return SCPI_RES_ERR;
    }

    ILLEGAL_PARAM_RETURN(Enable != 0 && Enable != 1); //判断范围

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Enable=" << Enable << endl;

    int iRet = WT_ERR_CODE_OK;
    iRet = WT_SetVsgIQImbCompensate(attr->ConnID, Enable);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgDomainIQCompensateForceEnable(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int value = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        if (!SCPI_ParamInt(context, &value, true))
        {
            return SCPI_RES_ERR;
        }
        if (value != 1 && value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_VSG_TIME_DOMIAN_IQ_FORCE_ENABLE;
        SubCmd.SendBuf = reinterpret_cast<char *>(&value);
        SubCmd.SendDataLen = sizeof(value);
        SubCmd.RecvBuf = nullptr;
        SubCmd.RecvBufLen = 0;
        iRet = WT_SubCmdHandle(attr->ConnID, &SubCmd);
        IF_ERR_RETURN(iRet);

    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsgWaveFlatnessCalCompensate(scpi_t * context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_GetVsgFlatnessCalCompensate(attr->ConnID, &ParamVal);

    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsgFlatnessCalCompensate=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t GetVsgIQImbCompensate(scpi_t * context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_GetVsgIQImbCompensate(attr->ConnID, &ParamVal);

    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VsgIQImbCompensate=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t VsgAlzWaveForm(scpi_t *context)
{
    char waveName[256] = {0};
    size_t copyLen = 0;
    int alz_type = WT_ALZ_PARAM_WIFI;
    int iRet = WT_ERR_CODE_OK;
    int wave2Flag = 0;
    std::vector<std::string> low_name_list;
    do
    {
        //wave name
        if (!SCPI_ParamCopyText(context, waveName, sizeof(waveName) - 1, &copyLen, true))
        {
            return SCPI_RES_ERR;
        }

        //analyze type
        if (!SCPI_ParamInt(context, &alz_type, true))
        {
            return SCPI_RES_ERR;
        }

        /* 分析的是仪器内部信号文件，不是从PC导入的必须带第三个参数 */
        if (context->parser_state.numberOfParameters > 2)
        {
            if (!SCPI_ParamInt(context, &wave2Flag, true))
            {
                return SCPI_RES_ERR;
            }
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int alz_size = sizeof(attr->vsgAlzParam.analyzeParamWifi);
        AnalyzeParam *alzParam = nullptr;
        alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamWifi;
        switch (alz_type)
        {
        case WT_ALZ_PARAM_FFT:
            alz_size = sizeof(attr->vsgAlzParam.analyzeParamFft);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamFft;
            break;
        case WT_ALZ_PARAM_BT:
            alz_size = sizeof(attr->vsgAlzParam.analyzeParamBt);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamBt;
            break;
        case WT_ALZ_PARAM_ZIGBEE:
            alz_size = sizeof(attr->vsgAlzParam.analyzeParamZigBee);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamZigBee;
            break;
        case WT_ALZ_PARAM_GLE:
            alz_size = sizeof(attr->vsgAlzParam.analyzeParamSparkLink);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamSparkLink;
            break;
        case WT_ALZ_PARAM_3GPP:
            attr->vsgAlzParam.analyzeParam3GPP.analyzeGroup = ALG_VSA_ALZ_POWER;    // VSG 信号只做FFT分析
            alz_size = sizeof(attr->vsgAlzParam.analyzeParam3GPP);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParam3GPP;
            break;
        case WT_ALZ_PARAM_ZWAVE:
            alz_size = sizeof(attr->vsgAlzParam.analyzeParamZWave);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamZWave;
            break;
        case WT_ALZ_PARAM_WSUN:
            alz_size = sizeof(attr->vsgAlzParam.analyzeParamWiSun);
            alzParam = (AnalyzeParam *)&attr->vsgAlzParam.analyzeParamWiSun;
            break;
        default:
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSG analyzeParamWifi demod = " << attr->vsgAlzParam.analyzeParamWifi.Demode << std::endl;
            break;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "waveName = " << waveName << ", alz type = " << alz_type << ", alz size = " << alz_size << std::endl;

        iRet = check_waveform_exist_v2(context, waveName, low_name_list);
        if (iRet)
        {
            break;
        }
        
        if (low_name_list.size() <= 0)
        {
            iRet = WT_ERR_CODE_FILE_OPERATE_FAIL;
            break;
        }

        int timeOut = attr->vsgAlzParam.timeOut * 1000; //转换成毫秒
        iRet = WT_AnalyzeVSGData(
            attr->ConnID,
            low_name_list[0].c_str(),//TODO:FENG 多PN信号是否可以选择指定PN进行分析，目前只分析第一个PN
            alz_type,
            alzParam,
            alz_size,
            timeOut);

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "low name = " << low_name_list[0] << ", WT_AnalyzeVSGData result = " << iRet << std::endl;
        attr->Reset_LiteResult(LITE_ENUM::LITE_VSG_ENUM_Power, LITE_ENUM::LITE_ENUM_MAX);

    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzDemod(scpi_t *context)
{
    int ParamVal = WT_DEMOD_11AG;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (ParamVal == WT_DEMOD_LRWPAN_FSK || ParamVal == WT_DEMOD_LRWPAN_OQPSK || ParamVal == WT_DEMOD_LRWPAN_OFDM)
        {
            attr->vsgAlzParam.analyzeParamWiSun.Demode = ParamVal;
        }
        else
        {
            attr->vsgAlzParam.analyzeParamWifi.Demode = ParamVal;
        }
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgOFDMPhaseTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_PH_CORR_OFF || WT_PH_CORR_MOVING_AVG < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.PhsCorrMode = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgOFDMChannelEstimation(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_CH_EST_RAW || WT_CH_EST_RAW_FULL < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.ChEstimate = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgOFDMTimingTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_SYM_TIM_OFF || WT_SYM_TIM_ON < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.SynTimeCorr = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgOFDMFrequencySync(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_FREQ_SYNC_SHORT_TRAIN || WT_FREQ_SYNC_AUTO < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.FreqSyncMode = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgOFDMAmplitudeTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_AMPL_TRACK_OFF || WT_AMPL_TRACK_ON < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.AmplTrack = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgDSSSEvmMethod(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_11B_STANDARD_TX_ACC || WT_11B_STANDARD_2016_TX_ACC < ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.Method11b = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgDSSSDCRemove(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < WT_DC_REMOVAL_OFF || WT_DC_REMOVAL_ON < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.DCRemoval = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgDSSSEqualizerTypes(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(
            WT_EQ_OFF != ParamVal &&
            WT_EQ_5_TAPS != ParamVal &&
            WT_EQ_7_TAPS != ParamVal &&
            WT_EQ_9_TAPS != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.EqTaps = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgDSSSPhaseTracking(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_PH_CORR_11b_OFF != ParamVal && WT_PH_CORR_11b_ON != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.PhsCorrMode11B = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsg11nSpectrumMaskVersion(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(0 != ParamVal && 1 != ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.SpectrumMaskVersion = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgAlzClockRate(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_CLOCK_RATE_1 != ParamVal && WT_CLOCK_RATE_1_2 != ParamVal && WT_CLOCK_RATE_1_4 != ParamVal && WT_CLOCK_RATE_1_5 != ParamVal && WT_CLOCK_RATE_1_8 != ParamVal && WT_CLOCK_RATE_1_10 != ParamVal && WT_CLOCK_RATE_1_20 != ParamVal);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamWifi.ClockRate = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgAlzRBW(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal.value > 100 * KHz_API || ParamVal.value < 1 * KHz_API);

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamFft.Rbw = ParamVal.value;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgBTAlzRate(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(WT_BT_DATARATE_Auto > ParamVal && WT_BT_BLE_500K < ParamVal); //AUTO|1M|2M|3M|BLE_1M|BLE_2M|BLE_125K|BLE_500K
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamBt.BTDataRate = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgBTAlzPacketType(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        //0 ~26,NULL|POLL|FHS|DH1|DH3|DH5|DM1|DM3|DM5|HV1|HV2|HV3|DV|AUX1|EV3|EV4|EV5|2DH1|2DH3|2DH5|3DH1|3DH3|3DH5|2EV3|2EV5|3EV3|3EV5
        ILLEGAL_PARAM_RETURN(WT_BT_PACKETTYPE_NULL > ParamVal && WT_BT_PACKETTYPE_3_EV5 < ParamVal);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamBt.BTPktType = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgZigbeeAnalyzeOptimise(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParamZigBee.Optimize = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t GetVsgRstAlzAdcSMPFreq(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_SMP_FREQ);
}

scpi_result_t GetVsgRstSpectOBW99(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_OBW, false);
}

scpi_result_t GetVsgRstSpectMaskErrorPercent(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_MASK_ERR, false);
}

scpi_result_t GetVsgRstSpectPeakFrequency(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_PEAK_FREQ, false);
}

scpi_result_t GetVsgRstSpectrumData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_Y);
}

scpi_result_t GetVsgRstSpectrumMaskData(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_MASK);
}

scpi_result_t GetVsgRstSpectMargin(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_MARGIN_DATA);
}

scpi_result_t GetVsgRstSpectrumSpan(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_SPECTRUM_FREQ_SPAN);
}

scpi_result_t GetVsgRstSpectrumRBW(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_SPECTRUM_RBW);
}

scpi_result_t GetVsgRstPointPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_POINTS_POWER);
}

scpi_result_t GetVsgRstPointAvgPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_WIN_AVG_POWER);
}

scpi_result_t GetVsgRstPointIQ(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_IQ);
}

scpi_result_t GetWavePnStructData(scpi_t *context)
{
    char waveName[256] = {0};
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        //wave name
        if (!SCPI_ParamCopyText(context, waveName, sizeof(waveName) - 1, &copyLen, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int dataSize = 0;
        unique_ptr<char []>pData = nullptr;

        std::string fileName = SCPI_WaveDir() + std::string(waveName);
        iRet = WT_GetPNFileStructSize(attr->ConnID, fileName.c_str(), &dataSize);
        if(iRet)
        {
            break;
        }

        if(dataSize > 0)
        {
            pData.reset(new char[dataSize]);
            iRet = WT_GetPNFileStructData(attr->ConnID, fileName.c_str(), pData.get(), dataSize);
            if (iRet)
            {
                break;
            }
            SCPI_ResultArbitraryBlock(context, (const char *)pData.get(), dataSize);
        }
                
    } while (0);
    IF_ERR_RETURN(iRet);

    return SCPI_RES_OK;
}

scpi_result_t GetWaveDescription(scpi_t *context)
{
    char waveName[256] = {0};
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        //wave name
        if (!SCPI_ParamCopyText(context, waveName, sizeof(waveName) - 1, &copyLen, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        const unsigned int bufSize = 4096;
        std::unique_ptr<char[]> description(new char[bufSize]);
        memset(description.get(), 0, bufSize);
        std::string fileName = SCPI_WaveDir() + std::string(waveName);

        iRet = WT_GetPnDescription(attr->ConnID, (char *)fileName.c_str(), description.get(), bufSize);
        if (iRet || 0 == description.get()[0])
        {
            break;
        }
        SCPI_ResultArbitraryBlock(context, (const char *)description.get(), bufSize);
    }while(0);
    IF_ERR_RETURN(iRet);
    
    return SCPI_RES_OK;
}

scpi_result_t GetWaveExternSettingData(scpi_t *context)
{
    char waveName[256] = {0};
    size_t copyLen = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        // wave name
        if (!SCPI_ParamCopyText(context, waveName, sizeof(waveName) - 1, &copyLen, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        int dataSize = 0;
        unique_ptr<char[]> pData = nullptr;

        std::string fileName = SCPI_WaveDir() + std::string(waveName);
        iRet = WT_GetPNFileExternSettingSize(attr->ConnID, fileName.c_str(), &dataSize);
        if (iRet)
        {
            break;
        }

        if (dataSize > 0)
        {
            pData.reset(new char[dataSize]);
            iRet = WT_GetPNFileExternSettingData(attr->ConnID, fileName.c_str(), pData.get(), dataSize);
            if (iRet)
            {
                break;
            }
            SCPI_ResultArbitraryBlock(context, (const char *)pData.get(), dataSize);
        }

    } while (0);
    IF_ERR_RETURN(iRet);

    return SCPI_RES_OK;
}

scpi_result_t GetVsgRstPointPowerLite(scpi_t *context)
{
    return GetVsgRstDoubleVectorDataLite(context, WT_RES_POINTS_POWER, LITE_ENUM::LITE_VSG_ENUM_Power);
}

scpi_result_t GetVsgRstPointAvgPowerLite(scpi_t *context)
{
    return GetVsgRstDoubleVectorDataLite(context, WT_RES_WIN_AVG_POWER, LITE_ENUM::LITE_VSG_ENUM_AvgPower);
}

scpi_result_t GetVsgRstPointIQLite(scpi_t *context)
{
    return GetVsgRstDoubleVectorDataLite(context, WT_RES_IQ, LITE_ENUM::LITE_VSG_ENUM_IQ);
}

scpi_result_t VsgDevmDutDutyRadio(scpi_t *context)
{
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_Devm.DutyRatio = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t VsgDevmLeadingTime(scpi_t *context)
{
    double ParamVal = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_Devm.LeadTime = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t VsgDevmDelayTime(scpi_t *context)
{
    double ParamVal = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_Devm.DelayTime = ParamVal;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t VsgDevmEnable(scpi_t *context)
{
    int ParamVal = 0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->m_Devm.FEMMode = ParamVal;
        if (attr->m_TestMode == TESTMODE_RD && attr->m_Devm.FEMMode)
        {
            attr->vsaAlzParam.analyzeParamWifi.ICISuppression = 0;
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzTimeOut(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.timeOut = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgAlzParam timeout = " << attr->vsgAlzParam.timeOut << " S" << std::endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgGapPowerEnable(scpi_t *context)
{
    int ParamVal;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        WT_SetFpgaIFG(attr->ConnID, ParamVal);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetFpgaIFG=" << ParamVal << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t GetVsgGapPowerEnable(scpi_t *context)
{
    int ParamVal = 0;
    int Ret = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        Ret = WT_GetFpgaIFG(attr->ConnID, &ParamVal);

    } while (0);
    IF_ERR_RETURN(Ret);

    SCPI_ResultInt(context, ParamVal);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetFpgaIFG=" << ParamVal << endl;
    return SCPI_ResultOK(context);
}

scpi_result_t SetVSGAutoPowerCorrect(scpi_t *context)
{
    int Flag = 0;
    int iRet = WT_OK;
    char FileName[1024] = {0};
    size_t FileNameLen = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (!SCPI_ParamInt(context, &Flag, true))
    {
        return SCPI_RES_ERR;
    }

    for (int i = 0; i < attr->m_vsgAutoPowerCorrectFileName.size(); ++i)
    {
        attr->m_vsgAutoPowerCorrectFileName[i] = "";
        attr->m_VSGPowerCorrectionJson[i].clear();
        attr->m_VsgFreqPathLosTbl[i].clear();
    }

    if (Flag == true)
    {
        for (int i = 0; i < context->parser_state.numberOfParameters - 1 && i < attr->m_vsgAutoPowerCorrectFileName.max_size(); ++i)
        {
            if (!SCPI_ParamCopyText(context, FileName, sizeof(FileName) - 1, &FileNameLen, true))
            {
                return SCPI_RES_ERR;
            }
            attr->m_vsgAutoPowerCorrectFileName[i] = FileName;
            char Buf[1024] = POWER_CORRECTION_TABLE;
            strcat(Buf, FileName);
            iRet = ParseJSONFile(Buf, attr->m_VSGPowerCorrectionJson[i]);
            for (u32 j = 0; j < attr->m_VSGPowerCorrectionJson[i].size(); j++)
            {
                struct FreqPathLos Tmp;
                Tmp.Freq = attr->m_VSGPowerCorrectionJson[i][j]["Freq"].asDouble();
                Tmp.PathLos = attr->m_VSGPowerCorrectionJson[i][j]["Correction"].asDouble();
                attr->m_VsgFreqPathLosTbl[i].push_back(Tmp);
            }
            std::sort(attr->m_VsgFreqPathLosTbl[i].begin(), attr->m_VsgFreqPathLosTbl[i].end(),
                [](const struct FreqPathLos &A, const struct FreqPathLos &B) {return A.Freq < B.Freq;});
        }
    }

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgDCOffsetI(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgExtParam.DCOffsetI = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgExtParam.DCOffsetI=" << attr->vsgExtParam.DCOffsetI << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgDCOffsetQ(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgExtParam.DCOffsetQ = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgExtParam.DCOffsetQ=" << attr->vsgExtParam.DCOffsetQ << endl;
    } while (0);

    return SCPI_ResultOK(context);
}


scpi_result_t SetVsgCommModeVolt(scpi_t *context)
{
    scpi_number_t ParamVal;
    do
    {
        if (!SCPI_ParamNumber(context, nullptr, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgExtParam.CommModeVolt = ParamVal.value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "vsgExtParam.CommModeVolt=" << attr->vsgExtParam.CommModeVolt << endl;
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgImbalanceCal(scpi_t *context)
{
    int Number[1] = {0};
    int segmentID = 0;
    int enable = 0;
    double imbAmp = 0.0;
    double imbPhase = 0.0;
    double timeSkew = 0.0;
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &enable, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamDouble(context, &imbAmp, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamDouble(context, &imbPhase, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamDouble(context, &timeSkew, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

    } while (0);

    IF_ERR_RETURN(iRet);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    segmentID = (Number[0] <= 1 ? 1 : 2);

    iRet = WT_SetVsgFixedImbalance(attr->ConnID, enable, imbAmp, imbPhase, timeSkew, segmentID);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgBroadcastStatus(scpi_t *context)
{
    int iRet = WT_OK;
    int Status = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Status, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(Status != 0 && Status != 1);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_SetBroadcastEnable(attr->ConnID, Status);
        IF_ERR_RETURN(iRet);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetVsgBroadcastStatus(scpi_t *context)
{
    int iRet = WT_OK;
    int Status = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_GetBroadcastEnable(attr->ConnID, Status);
        IF_ERR_RETURN(iRet);
        SCPI_ResultInt(context, Status);
    } while (0);
    return SCPI_RES_OK;
}

scpi_result_t SetVsgBroadcastDebugPower(scpi_t *context)
{
    int iRet = WT_OK;
    int Status = 0;
    double Power[WT_PORT_RF8 - WT_PORT_OFF] = {0};
    do
    {
        if (!SCPI_ParamInt(context, &Status, true))
        {
            return SCPI_RES_ERR;
        }
        if(Status)
        {
            for (int i = 0; i < context->parser_state.numberOfParameters && i < (WT_PORT_RF8 - WT_PORT_OFF); i++)
            {
                if (!SCPI_ParamDouble(context, &Power[i], true))
                {
                    return SCPI_RES_ERR;
                }
            }
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_SetBroadcastDebugEnable(attr->ConnID, Status, Power);
        IF_ERR_RETURN(iRet);
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t GetBroadcastRunStatus(scpi_t *context)
{
    int iRet = WT_OK;
    int Status = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = WT_GetBroadcastRunStatus(attr->ConnID, Status);
        IF_ERR_RETURN(iRet);
        SCPI_ResultInt(context, Status);
    } while (0);
    return SCPI_RES_OK;
}


scpi_result_t SetVsgAlzLinkDirect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = ALG_3GPP_UL;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_UL && Value != ALG_3GPP_DL)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (IsAlg3GPPStandardType(attr->vsgAlzParam.analyzeParam3GPP.Standard))
        {
            attr->vsgAlzParam.Reset_AlzParam(attr->vsgAlzParam.analyzeParam3GPP, attr->vsgAlzParam.analyzeParam3GPP.Standard, Value, false);
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzRFBand(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int StreamID = 0;
    do
    {
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParam3GPP.rf_band[StreamID] = Value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "StreamID=" << StreamID << "; Value=" << Value << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzPowerGraphEnable(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParam3GPP.MeasPowerGraph = Value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzSpectrumEnable(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParam3GPP.MeasSpectrum = Value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzCCDFEnable(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->vsgAlzParam.analyzeParam3GPP.MeasCCDF = Value;

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgAlzRFChannel(scpi_t * context)
{
    extern BandChanItem LTE_ULBandChanMap[];
    extern BandChanFreqItem LTE_DLBandChanFreqMap[];
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int StreamID = 0;
    do
    {
        SCPI_CommandNumbers(context, &StreamID, 1);
        if (StreamID < 0 || StreamID >= ALG_3GPP_MAX_STREAM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->vsgAlzParam.analyzeParam3GPP.Standard != ALG_3GPP_STD_4G)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->vsgAlzParam.analyzeParam3GPP.rf_channel[StreamID] = Value;

        int LinkDirect = 0;
        switch (attr->vsgAlzParam.analyzeParam3GPP.Standard)
        {
            case ALG_3GPP_STD_WCDMA:
                LinkDirect = attr->vsgAlzParam.analyzeParam3GPP.WCDMA.LinkDirect;
                break;
            case ALG_3GPP_STD_4G:
                LinkDirect = attr->vsgAlzParam.analyzeParam3GPP.LTE.ChanType;
                break;
            case ALG_3GPP_STD_5G:
                LinkDirect = attr->vsgAlzParam.analyzeParam3GPP.NR.LinkDirect;
                break;
            case ALG_3GPP_STD_NB_IOT:
                LinkDirect = attr->vsgAlzParam.analyzeParam3GPP.NBIOT.LinkDirect;
                break;
            default:
                break;
        }

        if (LinkDirect == ALG_3GPP_UL)
        {
            for (int i = 0; i < BAND_CHANNEL_ITEM_MAX; i++)
            {
                if (Value > LTE_ULBandChanMap[i].ChannelMax)
                {
                    continue;
                }

                if (Value >= LTE_ULBandChanMap[i].ChannelMin)
                {
                    if (StreamID == 0)
                    {
                        attr->vsgParam.Freq = (Value + LTE_ULBandChanMap[i].Delta) / 10.0 * MHz_API;
                    }
                    else
                    {
                        attr->vsgParam.Freq2 = (Value + LTE_ULBandChanMap[i].Delta) / 10.0 * MHz_API;
                    }
                }
                else
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            }
            IF_BREAK(iRet);
        }
        else if (attr->vsgAlzParam.analyzeParam3GPP.Standard == ALG_3GPP_STD_4G && LinkDirect == ALG_4G_PDSCH)
        {
            for (int i = 0; i < BAND_CHANNEL_ITEM_MAX; i++)
            {
                if (Value > LTE_DLBandChanFreqMap[i].ChannelMax)
                {
                    continue;
                }

                if (Value >= LTE_DLBandChanFreqMap[i].N_ref)
                {
                    if (StreamID == 0)
                    {
                        attr->vsaParam.Freq = ((Value - LTE_DLBandChanFreqMap[i].N_ref) / 10.0 + LTE_DLBandChanFreqMap[i].F_ref) * MHz_API;
                    }
                    else
                    {
                        attr->vsaParam.Freq2 = ((Value - LTE_DLBandChanFreqMap[i].N_ref) / 10.0 + LTE_DLBandChanFreqMap[i].F_ref) * MHz_API;
                    }
                }
                else
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                }
                break;
            }
            IF_BREAK(iRet);
        }
        else
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "StreamID=" << StreamID << "; Value=" << Value << "; Freq1=" << attr->vsgParam.Freq << "; Freq2=" << attr->vsgParam.Freq2 << endl;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsgBroadcastTimeout(scpi_t *context)
{
    int iRet = WT_OK;
    int ParamVal = 0;
    do
    {
        if (!SCPI_ParamInt(context, &ParamVal, true))
        {
            return SCPI_RES_ERR;
        }
        ILLEGAL_PARAM_RETURN(ParamVal < 0 || ParamVal > 60);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SubCmdType SubCmd;
        SubCmd.Cmd = SUB_CMD_SET_BROADCAST_TIMEOUT;
        SubCmd.SendBuf = reinterpret_cast<char*>(&ParamVal);
        SubCmd.SendDataLen = sizeof(ParamVal);
        SubCmd.RecvBuf = nullptr;
        SubCmd.RecvBufLen = 0;
        iRet = WT_SubCmdHandle(attr->ConnID, &SubCmd);
    } while (0);
    IF_ERR_RETURN(iRet);
    return SCPI_ResultOK(context);
}

scpi_result_t SetVsgUnitMask(scpi_t * context)
{
    int Mask[WT_SUB_TESTER_INDEX_MAX] = {0};
    do
    {
        for (int i = 0; i < WT_SUB_TESTER_INDEX_MAX; ++i)
        {
            if (!SCPI_ParamInt(context, &Mask[i], true))
            {
                return SCPI_RES_ERR;
            }
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        memcpy(attr->vsgParam.VsgUnitMask, Mask, sizeof(Mask));
    } while (0);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetVSGPattern(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int ConnID = ((SPCIUserParam *)(context->user_context))->ConnID;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    do
    {
        iRet = WT_SetVSGPattern(ConnID, attr->vsgPattern.data(), attr->vsgPattern.size());
    } while (0);

    SCPI_ResultInt(context, iRet);
    return SCPI_ResultOK(context, iRet);
}
