/*
 * @Description: 3GPP：WCDMA配置分析参数相关命令
 * @Autor: <PERSON>
 * @Date: 20240130
 */
#ifndef SCPI_3GPP_ALZ_WCDMA_H_
#define SCPI_3GPP_ALZ_WCDMA_H_

#include "basehead.h"
#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif

    //**************************************************************************************************
    // UL General Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzScramblingCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzDPCCHSlotFormat(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzConfiguration(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzDPDCHAvailable(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzMeasurementLength(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzSynchronisation(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzSlotNumber(scpi_t *context);
    scpi_result_t SCPI_WCDMA_UL_General_SetAlzCDPSpreadingFactor(scpi_t *context);

    //**************************************************************************************************
    // Measure Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakMagnErrLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakMagnErrLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsMagnErrLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsMagnErrLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakEvmLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakEvmLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsEvmLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsEvmLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakPhaseErrLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakPhaseErrLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsPhaseErrLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsPhaseErrLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzCarrFreqErrLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzCarrFreqErrLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzPhaseDiscLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzUpperLimit(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzDynamicLimit(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureUnit(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrLimit1Mode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrUtraLimit1Value(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrLimit2Mode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrUtraLimit2Value(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitADMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitADValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitEFMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitEFValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureAclrAbsLimitMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureAbsLimitValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureSemLimitGMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureSemLimitGValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureSemLimitHMode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureSemLimitHValue(scpi_t *context);
    scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureHMode(scpi_t *context);
    
    //**************************************************************************************************
    // DL General Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_DL_General_SetAlzScramblingState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_General_SetAlzScramblingCode(scpi_t *context);

    //**************************************************************************************************
    // DL Channel Configuration
    //**************************************************************************************************
    scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPCPICHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPCPICHPower(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPSCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPSCHPower(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzSSCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzSSCHPower(scpi_t *context);

    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHNum(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHSlotFormat(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHSymbRate(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHChanCode(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHInterleaver2Statate(scpi_t *context);

    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTTI(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTbCount(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTbSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHCrc(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHRmAttribute(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHEProtection(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHInterleaverStat(scpi_t *context);

    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHState(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTTI(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTbCount(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTbSize(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHCrc(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHRmAttribute(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHEProtection(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHInterleaverStat(scpi_t *context);
    scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHPower(scpi_t *context);
#ifdef __cplusplus
}
#endif

#endif // SCPI_3GPP_ALZ_WCDMA_H_
