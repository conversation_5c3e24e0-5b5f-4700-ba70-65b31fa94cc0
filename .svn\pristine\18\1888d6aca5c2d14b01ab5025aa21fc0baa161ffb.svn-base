//*****************************************************************************
//  File: socket.cpp
//  提供常见的socket操作接口
//  Data: 2016.7.12
//*****************************************************************************
#include "socket.h"
#include <sys/types.h>
#include <sys/socket.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <new>
#include <cerrno>
#include <cstring>
#include <unistd.h>
#include <sys/time.h>
#include <poll.h>
#include "wterror.h"
#include "basefun.h"
#include "wtlog.h"

//*****************************************************************************
// 注意或说明:
// 1、在发送大量数据的时候, 如果缓冲区接收不了的情况下，Send会一直阻塞在Send方法里,
// 不停的睡眠然后尝试发送，这或许不是一种好方法;
// 2、在接收大量数据(缓冲区很大)的时候, 如果接收不到数据时，则返回已经接收的数据
// 而不是阻塞在Recv中一直尝试读取数据;
//*****************************************************************************


#define MAX_PKT_SIZE (1024)

WRSocket::WRSocket(int Fd, int BufSize)
    : m_Fd(Fd), m_BufSize(BufSize), m_BufPos(0)
{
    if (Fd != -1)
    {
        m_RecvBuf.reset(new char[BufSize]);
        Basefun::GetSockPeerInfo(Fd, m_Ip, m_Port);
    }
}

WRSocket::WRSocket(const WRSocket& sock)
{
    m_BufSize = 2048;
    m_BufPos = 0;
    m_Fd = sock.GetFd();
    if (m_Fd != -1)
    {
        m_RecvBuf.reset(new char[m_BufSize]);
        Basefun::GetSockPeerInfo(m_Fd, m_Ip, m_Port);
    }
}

int WRSocket::ConnectAddr(const char *IP, int Port)
{
    int fd = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (fd < 0)
    {
        return -errno;
    }

    struct sockaddr_in Addr;
    bzero(&Addr, sizeof(Addr));
    Addr.sin_family = AF_INET;
    Addr.sin_addr.s_addr = inet_addr(IP);
    Addr.sin_port = htons(Port);
    if ( SetNonblock(fd , 1) != 0)
    {
        close(fd);
        return -errno;
    }
    connect(fd, (struct sockaddr *)&Addr, sizeof(Addr));
    if ( IOWriteWait(fd, 2000) != 0)//(ConnectAddr timeout == AddMimoTester timeout) && (ConnectAddr timeout < TestConnectStatus timeout)
    {
        close(fd);
        return -errno;
    }

    if ( SetNonblock(fd , 0) != 0)
    {
        return -errno;
    }
    return fd;
}

int WRSocket::IOWriteWait(int Fd, int timeout_ms)
{
    struct pollfd fds;
    int delay = timeout_ms;
    fds.events = POLLOUT | POLLHUP | POLLERR;
    fds.fd = Fd;
    for (;;)
    {
        switch (poll(&fds, 1, delay))
        {
        case -1:
            if (errno == EINTR)
            {
                continue;
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "[%s, %d]Error,errno(%d):%s\n", __FUNCTION__, __LINE__, errno, strerror(errno));
            return -errno;
        case 0:
            errno = ETIMEDOUT;
            WTLog::Instance().WriteLog(LOG_DEBUG, "[%s, %d]Error,errno(%d):%s\n", __FUNCTION__, __LINE__, errno, strerror(errno));
            return errno;
        default:
            if ((fds.revents & (POLLHUP | POLLERR)))
            {
                errno = ECONNREFUSED;
                WTLog::Instance().WriteLog(LOG_DEBUG, "[%s, %d]Error,errno(%d):%s\n", __FUNCTION__, __LINE__, errno, strerror(errno));
                return -errno;
            }
            if (fds.revents & POLLOUT)
            {
                return WT_OK;
            }
            WTLog::Instance().WriteLog(LOG_DEBUG, "[%s, %d]Error,errno(%d):%s\n", __FUNCTION__, __LINE__, errno, strerror(errno));
            return -errno;
        }
    }
}

int WRSocket::SetNonblock(int Fd, int on)
{
    int Flag = fcntl(Fd, F_GETFL);
    if(Flag < 0)
    {
        return -errno;
    }

    if (fcntl(Fd, F_SETFL, Flag | O_NONBLOCK) < 0)
    {
        return -errno;
    }

    setsockopt(Fd, IPPROTO_TCP, TCP_NODELAY, &on, sizeof(on)); //关闭Nagle

    return WT_OK;
}

int WRSocket::SetKeepAlive(int Fd, bool Enable, int KeepIdle, int KeepIntvl, int KeepCnt, unsigned int Timeout)
{
    int Val = 0;
    if (Enable)
    {
        Val = 1;
        Timeout *= 1000; //秒转毫秒
        setsockopt(Fd, SOL_SOCKET, SO_KEEPALIVE, &Val, sizeof(Val));
        setsockopt(Fd, IPPROTO_TCP, TCP_KEEPIDLE, &KeepIdle, sizeof(KeepIdle));
        setsockopt(Fd, IPPROTO_TCP, TCP_KEEPINTVL, &KeepIntvl, sizeof(KeepIntvl));
        setsockopt(Fd, IPPROTO_TCP, TCP_KEEPCNT, &KeepCnt, sizeof(KeepCnt));
        setsockopt(Fd, IPPROTO_TCP, TCP_USER_TIMEOUT, &Timeout, sizeof(Timeout));
    }
    else
    {
        setsockopt(Fd, SOL_SOCKET, SO_KEEPALIVE, &Val, sizeof(Val));
    }

    return WT_OK;
}

int WRSocket::SendFdMsg(int Fd, const void *Buf, int BufLen)
{
    struct msghdr msg;
    struct iovec  iov[1];
    char cmsgbuf[CMSG_SPACE(sizeof(int))];

    iov[0].iov_base = (void *)Buf;
    iov[0].iov_len  = BufLen;
    msg.msg_iov = iov;
    msg.msg_iovlen = 1;
    msg.msg_name = NULL;
    msg.msg_namelen = 0;

    if (Fd >= 0)
    {
        struct cmsghdr *cmptr = (struct cmsghdr *)cmsgbuf;
        cmptr->cmsg_level = SOL_SOCKET;
        cmptr->cmsg_type  = SCM_RIGHTS;
        cmptr->cmsg_len   = CMSG_LEN(sizeof(int));
        *(int *)CMSG_DATA(cmptr) = Fd;
        msg.msg_control = cmptr;
        msg.msg_controllen = sizeof(cmsgbuf);
    }
    else
    {
        msg.msg_control = NULL;
        msg.msg_controllen = 0;
    }

    do
    {
        errno = 0;
        int ret = sendmsg(m_Fd, &msg, 0);
        if (ret < 0)
        {
            if (errno == EAGAIN || errno == EINTR)  //重试
            {
                continue;
            }
            else
            {
                return WT_SOCKET_CLOSED;
            }
        }
        else if (ret == 0)
        {
            return WT_SOCKET_CLOSED;
        }
    } while(0);

    return WT_OK;
}

int WRSocket::RecvFdMsg(int &Fd, void *Buf, int BufLen)
{
    struct iovec   iov[1];
    struct msghdr  msg;
    char cmsgbuf[CMSG_SPACE(sizeof(int))];

    iov[0].iov_base = Buf;
    iov[0].iov_len  = BufLen;
    msg.msg_iov     = iov;
    msg.msg_iovlen  = 1;
    msg.msg_name    = NULL;
    msg.msg_namelen = 0;
    msg.msg_control = cmsgbuf;
    msg.msg_controllen = sizeof(cmsgbuf);

    do
    {
        errno = 0;
        int ret = recvmsg(m_Fd, &msg, 0);
        if (ret < 0)
        {
            if (errno == EINTR)  //重试
            {
                continue;
            }
            else
            {
                Fd = -1;
                return errno == EAGAIN ? WT_OK : WT_SOCKET_CLOSED;
            }
        }
        else if (ret == 0) //连接已被断开
        {
            return WT_SOCKET_CLOSED;
        }
    } while(0);

    struct cmsghdr *cmptr = CMSG_FIRSTHDR(&msg);
    if ((cmptr != NULL) && (cmptr->cmsg_len == CMSG_LEN(sizeof(int)))
        && (cmptr->cmsg_level == SOL_SOCKET) && (cmptr->cmsg_type == SCM_RIGHTS))
    {
        // 理论上recvmsg接收的FD就是已经复制过的，不需要额外dup一次;
        // Fd = dup(*(int *)CMSG_DATA(cmptr));
        // close(*(int *)CMSG_DATA(cmptr));
        Fd = *(int *)CMSG_DATA(cmptr);
    }
    else
    {
        Fd = -1;
    }

    return WT_OK;
}

// 注意点:
// Len 返回当前缓冲区所有的数据总和, 而不是本次读取的数据长度.
int WRSocket::Recv(int &Len, bool Restart)
{
    int Pos = Restart ? 0 : m_BufPos;
    int Ret = Recv(m_RecvBuf.get() + Pos, m_BufSize - Pos, Len);

    if (Ret == WT_OK)
    {
        Len += Pos;
        m_BufPos = Len;
    }

    return Ret;
}

// 收包超时处理, 某些场景下需要收特定长度的数据包, 且也不适合分包
// RxLen <= 0 不做读操作, 直接返回WT_OK, 且 Len置0
// TimeoutUs <= 0 则会阻塞, 一直读取; TimeoutUs>0 则需要计算超时
int WRSocket::Recv(void *Buf, int RxLen, int &Len, long TimeoutUs)
{
    (void)RxLen; // 剩余需要读取的数据
    (void)Len;   // 已经读取的数据
    Len = 0;
    int Ret = WT_OK;    // 错误码

    // 0就不要收包了, 不管SOCK是否存在错误.
    if (RxLen <= 0)
    {
        return Ret;
    }

    struct timeval Start, End;
    gettimeofday(&Start, nullptr);

    while (RxLen > 0)
    {
        errno = 0;
        int RetSize = recv(m_Fd, (char *)Buf + Len, RxLen, 0);
        int err = errno;

        if (RetSize > 0)
        {
            Len += RetSize;
            RxLen -= RetSize;

            // 提前跳出, 不做超时计算
            if (RxLen <= 0)
            {
                break;
            }
        }
        else if (RetSize < 0)
        {
            // 中断或者无数据可读, 需要重试
            if ((EINTR == err) || (EWOULDBLOCK == err) || (EAGAIN == err))
            {
                // usleep(10);  // 睡眠放在超时计算后面
                // continue;    // 后面要做超时处理, 不能continue
            }
            else
            {
                if (true)
                {
                    char tmp[256] = { 0 };
                    sprintf(tmp, "[%s, %d]Ret=%d, errno(%d)\n", __FUNCTION__, __LINE__, RetSize, err);
                    m_LastErrorStr = tmp;
                }
                Ret = WT_SOCKET_CLOSED;
                break;
            }

        }
        else
        {
            if (true)
            {
                char tmp[256] = { 0 };
                sprintf(tmp, "[%s, %d] socket peer has performed an orderly shutdown\n", __FUNCTION__, __LINE__);
                m_LastErrorStr = tmp;
            }
            Ret = WT_SOCKET_CLOSED;
            break;
        }

        if (TimeoutUs > 0) // 启用超时功能
        {
            gettimeofday(&End, nullptr);
            long Spend = (End.tv_sec - Start.tv_sec) * 1000000 + (End.tv_usec - Start.tv_usec);
            if (Spend >= TimeoutUs)
            {
                Ret = WT_SOCKET_DATA_RECV_TIMEOUT;
                break;
            }
        }

        usleep(10);
    }

    return Ret;
}

// 注意点:
// Len <= 0 不做读操作, 直接返回0
// 无数据可收就返回, 不会阻塞等数据
// 读操作失败, 返回-1
int WRSocket::Recv(char *Buf, int Len)
{
    int Pos = 0;
    while (Pos < Len)
    {
        errno = 0;
        int Ret = recv(m_Fd, (char *)Buf + Pos, Len - Pos, 0);
        int err = errno;

        if (Ret > 0)
        {
            Pos += Ret;
        }
        else if (Ret < 0)
        {
            if ((EINTR == err)) // 读操作被中断, 需要重试
            {
                usleep(10);
                continue;
            }

            if ((EWOULDBLOCK == err) || (EAGAIN == err)) // 无数据可读
            {
                break;
            }

            if (true) // 其他情况
            {
                char tmp[256] = { 0 };
                sprintf(tmp, "[%s,%d]Ret=%d, errno(%d)\n", __FUNCTION__, __LINE__, Ret, err);
                m_LastErrorStr = tmp;
            }
            Pos = -1;
            break;
        }
        else // 对方关闭
        {
            if (true)
            {
                char tmp[256] = { 0 };
                sprintf(tmp, "[%s,%d] socket peer has performed an orderly shutdown\n", __FUNCTION__, __LINE__);
                m_LastErrorStr = tmp;
            }
            Pos = -1;
            break;
        }
    }

    return Pos;
}

int WRSocket::Recv(void *Buf, int RxLen, int &Len)
{
#if 1
    int Ret = WT_OK;

    (void)RxLen; // 剩下需要读取的数据
    (void)Len;   // 已经读取的数据
    Len = 0;

    while (RxLen > 0)
    {
        int OnceCount = (RxLen > MAX_PKT_SIZE) ? MAX_PKT_SIZE : RxLen;

        int RetCount = Recv((char *)Buf + Len, OnceCount);

        if (RetCount < 0)
        {
            Ret = WT_SOCKET_CLOSED;
            break;
        }
        else
        {
            RxLen -= RetCount;
            Len += RetCount;

            // 这里要谨慎处理, 我觉得如果读取不到就不要再读了, 等下一次IO事件吧.
            // RetCount == OnceCount, socket 大概率存在数据可读
            // RetCount < OnceCount socket 读到数据了, 再读一次大概率也是没有数据了
            // RetCount == 0 socket 没有读取到数据, 再读一次大概率也是没有数据了
            if (RetCount > 0)
            {
                continue;
            }
            else
            {
                break;
            }
        }
    }

    return Ret;

#else
    int Ret = WT_OK;
    int Pos = 0;

    while (Pos < RxLen)
    {
        errno = 0;
        Len = recv(m_Fd, (char *)Buf + Pos, RxLen - Pos, 0);
        if (Len > 0)
        {
            Pos += Len;
        }
        else if (Len < 0)
        {
            if (errno == EINTR)   //重试
            {
                continue;
            }

            if (errno == EFAULT || errno == EINVAL)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "recv param error, addr %p, len %d\n", Buf, RxLen);
                break;
            }

            Ret = (errno == EAGAIN) ? WT_OK : WT_SOCKET_CLOSED;
            break;
        }
        else
        {
            Ret = WT_SOCKET_CLOSED;
            break;
        }
    }

    Len = Pos;

    return Ret;
#endif
}

// 发送Buf开始的Len个字节, 直到发完为止，如果缓冲区满，则睡眠一段时间后继续发
// 换句话说，要么阻塞在这里一直发送; 要么发送所有完成返回; 要么出错返回;
int WRSocket::Send(char *Buf, int Len)
{
    int Pos = 0;
    while (Pos < Len)
    {
        errno = 0;
        int Ret = send(m_Fd, (char *)Buf + Pos, Len - Pos, 0);
        int err = errno;

        if (Ret > 0)
        {
            Pos += Ret;
        }
        else
        {
            if ((EINTR == err) || (EWOULDBLOCK == err) || (EAGAIN == err)) //重试
            {
                usleep(10);
                continue;
            }

            if (true)
            {
                char tmp[256] = { 0 };
                sprintf(tmp, "[%s,%d]Ret=%d, errno(%d)\n", __FUNCTION__, __LINE__, Ret, err);
                m_LastErrorStr = tmp;
            }
            Pos = -1;
            break;
        }
    }

    return Pos;
}

int WRSocket::Send(const void *Data, int Len, int &TxLen)
{
#if 1
    int Ret = WT_OK;

    (void)Len; // 所有数据总字节数; 也表示剩余需要发送字节数;
    TxLen = 0; // 已经发送的字节数
    while (Len > 0)
    {
        // 本次(一次)发送字节数
        int OnceCount = (Len >= MAX_PKT_SIZE) ? MAX_PKT_SIZE : Len;

        int RetCount = Send((char *)Data + TxLen, OnceCount);
        if (RetCount >= 0)
        {
            TxLen += RetCount;
            Len -= RetCount;
        }
        else
        {
            Ret = WT_SOCKET_CLOSED;
            break;
        }
    }

    return Ret;

#else
    int Ret = WT_OK;

    TxLen = 0;
    while (TxLen < Len)
    {
        errno = 0;
        Ret = send(m_Fd, (char *)Data + TxLen, Len - TxLen, 0);
        if (Ret > 0)
        {
            TxLen += Ret;
        }
        else
        {
            if ((errno == EINTR) || (errno == EAGAIN))  //重试
            {
                continue;
            }

            if (errno == EFAULT || errno == EINVAL)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "send param error, addr %p, len %d\n", Data, Len);
                break;
            }

            WTLog::Instance().WriteLog(LOG_DEBUG, "send len %d, socket send ret %d, errno %d\n", Len, Ret, errno);

            return WT_SOCKET_CLOSED;
        }
    }

    return WT_OK;
#endif
}

int WRSocket::Send(const BufInfo *Vec, int Cnt, int &TxLen)
{
    int Ret = WT_OK;
    int Len = 0;
    TxLen = 0;

    int Total = 0;
    for (int i = 0; i < Cnt; i++)
    {
        Total += Vec[i].Len;
    }

    if (Total <= MAX_PKT_SIZE)
    {
        Total = 0;
        char Buf[1024];
        for (int i = 0; i < Cnt; i++)
        {
            memcpy(Buf + Total, Vec[i].Data, Vec[i].Len);
            Total += Vec[i].Len;
        }

        Ret = Send(Buf, Total, TxLen);
    }
    else
    {
        for (int i = 0; i < Cnt; i++)
        {
            Ret = Send(Vec[i].Data, Vec[i].Len, Len);
            if (Ret != WT_OK)
            {
                return Ret;
            }

            TxLen += Len;
        }
    }

    return WT_OK;
}

// 申请新长度buffer，然后将原有buffer数据复制到新buffer中。为防止频繁申请，实际申请内存为所需的2倍
int WRSocket::ExpandBuf(int Size)
{
    std::unique_ptr<char[]>Buf(new(std::nothrow) char[Size * 2]);
    if (Buf == nullptr)
    {
        return WT_ALLOC_FAILED;
    }

    memcpy(Buf.get(), m_RecvBuf.get(), m_BufPos);
    m_RecvBuf = std::move(Buf);
    m_BufSize = Size * 2;

    return WT_OK;
}

void WRSocket::ResetCmdBuf(int CmdLen)
{
    if ( (m_BufPos<=0) || (CmdLen<=0) )
    {
        m_BufPos = 0;
        return;
    }

    if (CmdLen < m_BufPos)
    {
        char *buff = m_RecvBuf.get();
        memcpy(buff, buff+CmdLen, m_BufPos-CmdLen);
        m_BufPos -= CmdLen;
    }
    else
    {
        m_BufPos = 0;
    }
}

void WRSocket::ResetFd(int fd)
{
    if (m_Fd != -1)
    {
        close(m_Fd);
        m_Fd = -1;
    }

    if (fd != -1)
    {
        m_Fd = fd;
        if (m_RecvBuf == nullptr)
        {
            m_RecvBuf.reset(new char[m_BufSize]);
        }
        else
        {
            m_BufPos = 0;
        }
    }
}