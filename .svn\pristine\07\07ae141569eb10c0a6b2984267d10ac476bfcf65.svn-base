#include "../general/devlib/ioctlcmd.h"
#include "hwlib.h"
#include "wtdefine.h"
#ifndef _WT_BUSI_LIB_418_H
#define _WT_BUSI_LIB_418_H
typedef int (*pFunc)(int DataLength, void *arg, struct dev_unit *pdev);
extern const pFunc pFuncArrayWT418[];
bool IsBusiBoardComponent(int FuncId);

int wt418_WriteLedIOExtReg(int Addr, int Data, struct dev_unit *pdev);
int wt418_ReadLedIOExtReg(int Addr, int *Data, struct dev_unit *pdev);
int wt418_WriteLedIOExtBit(int Index, int Status, struct dev_unit *pdev);
#endif