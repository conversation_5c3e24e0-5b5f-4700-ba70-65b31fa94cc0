//*****************************************************************************
//  File: mempool.cpp
//  内存池管理
//  Data: 2016.8.13
//
// 实现原理：
// 内存池从系统申请一大块内存，然后将整块内存分割成多个小块，每个小块内存的头部为内存节点信息。
// 初始时整块内存作为一个大块放入空闲链表。每次用户申请内存时按需要的大小分割出一个小块来。
// 释放时将内存放入空闲链表的头部。如果申请时发现链表中的所有块大小都无法满足要求则合并空闲内存块。
//*****************************************************************************
#include "mempool.h"
#include <cstdio>
#include "wterror.h"

using namespace std;

//最小的内存块大小，如果内存分割后剩余小于此大小则无需分割
static const int MinMemSize = 4096;

//将申请的内存大小对齐到最小内存块
static inline unsigned int PadToMinSize(unsigned int Size)
{
    return (Size + MinMemSize - 1) & (~ (MinMemSize - 1));
}

// memnode结构体占用内存大小
#define MEM_NODE_SIZE (sizeof(MemNode) + sizeof(long))

//内存块结构: |---node---|-----memory-----|，节点与其对应的内存在一起
struct MemNode
{
    unsigned int MemSize;       //节点对应的内存大小
    bool Free = true;           //节点是否空闲
    bool End = false;           //是否是最后一块内存

    explicit MemNode(unsigned int Size) : MemSize(Size) {}

    //*****************************************************************************
    // 将当前内存卡分割为2块，将原内存块尾部的内存作为新分割出的内存块
    // 参数[IN]: Size ：被分割出来的内存块的大小
    // 返回值: 分割出来的内存块节点
    //*****************************************************************************
    inline MemNode *Split(unsigned int Size)
    {
        void *NewAddr = (char *)GetMem() + MemSize - (Size + MEM_NODE_SIZE);

        MemNode *Node = new (NewAddr)MemNode(Size);
        Node->End = End;

        MemSize -= (Size + MEM_NODE_SIZE);
        End = false;

        return Node;
    }

    //获取下一个节点
    inline MemNode *Next(void)
    {
        if (End)
        {
            return nullptr;
        }
        else
        {
            void *NextNode = (char *)GetMem() + MemSize;
            return static_cast<MemNode *>(NextNode);
        }
    }

    //获取节点对应的内存
    inline void *GetMem()
    {
        void *Addr = static_cast<void *>(this);
        return (char *)Addr + MEM_NODE_SIZE;
    }

} __attribute__((aligned(8)));  //8字节对齐

MemPool &MemPool::Instance()
{
    static MemPool Pool;
    return Pool;
}

int MemPool::Init(unsigned int PoolSize)
{
    m_MemSize = PoolSize;
    m_Pool.reset(new(std::nothrow) char[m_MemSize]);
    if (m_Pool == nullptr)
    {
        return WT_ALLOC_FAILED;
    }

    m_First = new (m_Pool.get()) MemNode(m_MemSize - MEM_NODE_SIZE);
    m_First->End = true;
    m_FreeList.push_back(m_First);

    return WT_OK;
}

// 如果申请不到内存合并内存，如果合并后仍然申请不到则从系统中申请
void *MemPool::Alloc(unsigned int Size)
{
    void *Mem = nullptr;

    Size = PadToMinSize(Size);

    if (Size < m_MemSize)
    {
        lock_guard<mutex> Lock(m_Mutex);

        Mem = GetFreeMem(Size);
        if (Mem == nullptr)
        {
            Merge();
            Mem = GetFreeMem(Size);
        }
    }

    if (Mem == nullptr)
    {
        Mem = malloc(Size);
    }

    return Mem;
}

void MemPool::Free(void *Mem)
{
    if ((ulong)Mem >= (ulong)m_Pool.get() && (ulong)Mem < (ulong)m_Pool.get() + m_MemSize)
    {
        lock_guard<mutex> Lock(m_Mutex);

        MemNode *Node = reinterpret_cast<MemNode *>((char *)Mem - MEM_NODE_SIZE);
        Node->Free = true;
        m_FreeList.push_front(Node);
    }
    else
    {
        free(Mem);
    }
}

void *MemPool::GetFreeMem(unsigned int Size)
{
    void *Mem = nullptr;

    for (auto &Node : m_FreeList)
    {
        if (Node->MemSize < Size)
        {
            continue;
        }

        if (Node->MemSize > Size + MinMemSize)
        {
            MemNode *Tmp = Node->Split(Size);
            Tmp->Free = false;
            Mem = Tmp->GetMem();
        }
        else
        {
            Node->Free = false;
            Mem = Node->GetMem();
            m_FreeList.remove(Node);
        }

        break;
    }

    return Mem;
}

//遍历所有内存块将相邻且空闲的内存块合并
void MemPool::Merge()
{
    MemNode *Node = m_First;
    MemNode *Next = nullptr;

    while (Node && !Node->End)
    {
        if (!Node->Free)
        {
            Node = Node->Next();
            continue;
        }

        Next = Node->Next();
        if (!Next->Free)
        {
            Node = Next->Next();
            continue;
        }

        Node->MemSize += (Next->MemSize + MEM_NODE_SIZE);
        Node->End = Next->End;
        m_FreeList.remove(Next);
    }
}
