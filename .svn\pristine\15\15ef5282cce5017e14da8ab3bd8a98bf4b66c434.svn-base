{"HwConfigVersion": {"Version": "1.0.1.1", "Author": "yuan", "Date": "2025410"}, "InitList": {"WT448_and_WT428": {"BACK": {"UBVoltInfoInit": "Yes", "SwitchBoardVoltInfoInit": "Yes", "SwitchMappingInit": "Yes", "OCXOInit": "Yes", "ADF4002Init": "Yes", "HMC7043Init": "Yes", "FanInit": "Yes", "SwitchBoardInit": "Yes", "BackAD7091Init": "Yes", "SwbPortPowerDetectInit": "Yes", "SwbInnerPowerDetectInit": "Yes", "SetSwbAttInit": "Yes", "Remarks": "HwName : Init or Not"}, "BUSI": {"UBVoltInfoInit": "Yes", "RFVoltInfoInit": "Yes", "HMC7044Init": "Yes", "BusiAD7091Init": "Yes", "InitBaseBandMod": "Yes", "ADF4106Init": "Yes", "LOMODLOCKDETECT": "Yes", "DDSInit": "Yes", "LoModInit": "Yes", "LoMixInit": "Yes", "LOComModeInit": "Yes", "RfModLoFreqList": "Yes", "RfMixLoFreqList": "Yes", "SetATTCode": "Yes", "GetAD7682ChannelCode": "Yes", "Remarks": "HwName : Init or Not"}}, "WT418": {"BACK": {"UBVoltInfoInit": "Yes", "SwitchBoardVoltInfoInit": "Yes", "SwitchMappingInit": "Yes", "OCXOInit": "Yes", "ADF4002Init": "Yes", "FanInit": "Yes", "SwitchBoardInit": "Yes", "SwbInnerPowerDetectInit": "Yes", "SetSwbAttInit": "Yes", "Remarks": "HwName : Init or Not"}, "BUSI": {"UBVoltInfoInit": "Yes", "RFVoltInfoInit": "Yes", "HMC7044Init": "Yes", "BusiAD7091Init": "Yes", "InitBaseBandMod": "Yes", "LoModInit": "Yes", "LoMixInit": "Yes", "LOComModeInit": "Yes", "RfModLoFreqList": "Yes", "RfMixLoFreqList": "Yes", "SetATTCode": "Yes", "GetAD7689ChannelCode": "Yes", "Remarks": "HwName : Init or Not"}}}, "PortMapping_VA": [{"Port": "1", "Modulate": "0 ", "SwitchId": "0", "SubPort": "4"}, {"Port": "2", "Modulate": "0 ", "SwitchId": "0", "SubPort": "3"}, {"Port": "3", "Modulate": "1 ", "SwitchId": "0", "SubPort": "2"}, {"Port": "4", "Modulate": "1 ", "SwitchId": "0", "SubPort": "1"}, {"Port": "5", "Modulate": "2 ", "SwitchId": "1", "SubPort": "4"}, {"Port": "6", "Modulate": "2 ", "SwitchId": "1", "SubPort": "3"}, {"Port": "7", "Modulate": "3 ", "SwitchId": "1", "SubPort": "2"}, {"Port": "8", "Modulate": "3 ", "SwitchId": "1", "SubPort": "1"}], "PortMapping_VB": [{"Port": "1", "Modulate": "0 ", "SwitchId": "0", "SubPort": "1"}, {"Port": "2", "Modulate": "0 ", "SwitchId": "0", "SubPort": "2"}, {"Port": "3", "Modulate": "1 ", "SwitchId": "0", "SubPort": "3"}, {"Port": "4", "Modulate": "1 ", "SwitchId": "0", "SubPort": "4"}, {"Port": "5", "Modulate": "2 ", "SwitchId": "1", "SubPort": "1"}, {"Port": "6", "Modulate": "2 ", "SwitchId": "1", "SubPort": "2"}, {"Port": "7", "Modulate": "3 ", "SwitchId": "1", "SubPort": "3"}, {"Port": "8", "Modulate": "3 ", "SwitchId": "1", "SubPort": "4"}], "PortMapping_WT428_VA": [{"Port": "1", "Modulate": "0 ", "SwitchId": "0", "SubPort": "1"}, {"Port": "2", "Modulate": "0 ", "SwitchId": "0", "SubPort": "2"}, {"Port": "3", "Modulate": "0 ", "SwitchId": "0", "SubPort": "3"}, {"Port": "4", "Modulate": "0 ", "SwitchId": "0", "SubPort": "4"}, {"Port": "5", "Modulate": "1 ", "SwitchId": "1", "SubPort": "1"}, {"Port": "6", "Modulate": "1 ", "SwitchId": "1", "SubPort": "2"}, {"Port": "7", "Modulate": "1 ", "SwitchId": "1", "SubPort": "3"}, {"Port": "8", "Modulate": "1 ", "SwitchId": "1", "SubPort": "4"}], "PortMapping_WT418_VA": [{"Port": "1", "Modulate": "0 ", "SwitchId": "0", "SubPort": "1"}, {"Port": "2", "Modulate": "0 ", "SwitchId": "0", "SubPort": "2"}, {"Port": "3", "Modulate": "0 ", "SwitchId": "0", "SubPort": "3"}, {"Port": "4", "Modulate": "0 ", "SwitchId": "0", "SubPort": "4"}, {"Port": "5", "Modulate": "0 ", "SwitchId": "0", "SubPort": "5"}, {"Port": "6", "Modulate": "0 ", "SwitchId": "0", "SubPort": "6"}, {"Port": "7", "Modulate": "0 ", "SwitchId": "0", "SubPort": "7"}, {"Port": "8", "Modulate": "0 ", "SwitchId": "0", "SubPort": "8"}], "SwitchBoardConfig": {"SBConfigType": 1, "Remarks": "value=0:not exist; 1:simple;"}, "VsgGapPowerParam": {"Param1": "0x300", "Param2": "0x0", "Param3": "0x340", "Remarks": "Param Data Type : int"}, "VsgDevmDelay": {"WT448": [{"Delay": "864", "Remarks": "Unit0"}, {"Delay": "864", "Remarks": "Unit1"}, {"Delay": "864", "Remarks": "Unit2"}, {"Delay": "864", "Remarks": "Unit3"}], "WT428": [{"Delay": "864", "Remarks": "Unit0"}, {"Delay": "864", "Remarks": "Unit1"}, {"Delay": "864", "Remarks": "Unit2"}, {"Delay": "864", "Remarks": "Unit3"}]}, "IQSwap": {"VSGSwapValue": "0", "VSASwapValue": "0", "Remarks": "Value=0:not swap; 1:swap"}, "AnalogIQ": {"IQMode": 0, "Remarks": "IQMode=0:RfMode; 1:AnalogMode"}, "BP_ADF4002": {"WT418": {"DevId1": [{"Data": "0X92", "Remarks": ""}, {"Data": "0X101", "Remarks": ""}, {"Data": "0x04", "Remarks": ""}], "DevId2": [{"Data": "0X92", "Remarks": ""}, {"Data": "0XA01", "Remarks": ""}, {"Data": "0x04", "Remarks": ""}]}, "WT448_and_WT428": {"DevId1": [{"Data": "0X92", "Remarks": ""}, {"Data": "0XA01", "Remarks": ""}, {"Data": "0x04", "Remarks": ""}], "DevId2": [{"Data": "0X92", "Remarks": ""}, {"Data": "0X101", "Remarks": ""}, {"Data": "0x04", "Remarks": ""}]}}, "LO_ADF4106": [{"Data": "0X1F8092", "Remarks": ""}, {"Data": "0XC11", "Remarks": ""}, {"Data": "0x04", "Remarks": ""}], "BP_HMC7043": [{"Addr": "0x0004", "Data": "0x7F  ", "Delay": "0", "Remarks": "Seven Pairs of 14-Channel Outputs Enable[6:0] "}, {"Addr": "0x000A", "Data": "0xF   ", "Delay": "0", "Remarks": "CLKIN0/CLKIN0  input buffer control"}, {"Addr": "0x000B", "Data": "0xF   ", "Delay": "0", "Remarks": "CLKIN1/CLKIN1  input buffer control Reserved "}, {"Addr": "0x0064", "Data": "0x0   ", "Delay": "0", "Remarks": "Divide by 1 on clock input  "}, {"Addr": "0x00C8", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch0 "}, {"Addr": "0x00C9", "Data": "0x3   ", "Delay": "0", "Remarks": "ch0 12-Bit Channel Divider[7:0] (LSB) SLOT2_REF_CLK 300M/3=100M"}, {"Addr": "0x00CA", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00CB", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Fine Analog Delay[4:0]"}, {"Addr": "0x00CC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Coarse Digital Delay[4:0]"}, {"Addr": "0x00CD", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00CE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00CF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Output Mux Selection[1:0]"}, {"Addr": "0x00D0", "Data": "0x8   ", "Delay": "0", "Remarks": "ch0 LVPECL mode resistor disable"}, {"Addr": "0x00D2", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch1 "}, {"Addr": "0x00D3", "Data": "0x3   ", "Delay": "0", "Remarks": "ch1 12-Bit Channel Divider[7:0] (LSB) SLOT1_REF_CLK 300M/3=100M"}, {"Addr": "0x00D4", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00D5", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 Fine Analog Delay[4:0]"}, {"Addr": "0x00D6", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 Coarse Digital Delay[4:0]"}, {"Addr": "0x00D7", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00D8", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00D9", "Data": "0x2   ", "Delay": "0", "Remarks": "ch1 Output Mux Selection[1:0] clk"}, {"Addr": "0x00DA", "Data": "0x8  ", "Delay": "0", "Remarks": "ch1 LVPECL mode resistor disable"}, {"Addr": "0x00DC", "Data": "0xF3   ", "Delay": "0", "Remarks": "ch2 "}, {"Addr": "0x00DD", "Data": "0x3   ", "Delay": "0", "Remarks": "ch2 12-Bit Channel Divider[7:0] (LSB)SLOT1_FPGA_CLK 300M/3=100M"}, {"Addr": "0x00DE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00DF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Fine Analog Delay[4:0]"}, {"Addr": "0x00E0", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Coarse Digital Delay[4:0]"}, {"Addr": "0x00E1", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00E2", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00E3", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Output Mux Selection[1:0]"}, {"Addr": "0x00E4", "Data": "0x30   ", "Delay": "0", "Remarks": "ch2 LVDS mode resistor disable"}, {"Addr": "0x00E6", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch3 "}, {"Addr": "0x00E7", "Data": "0x3   ", "Delay": "0", "Remarks": "ch3 12-Bit Channel Divider[7:0] (LSB)SLOT2_FPGA_CLK 300M/3=100M"}, {"Addr": "0x00E8", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00E9", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 Fine Analog Delay[4:0]"}, {"Addr": "0x00EA", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 Coarse Digital Delay[4:0]"}, {"Addr": "0x00EB", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00EC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00ED", "Data": "0x2   ", "Delay": "0", "Remarks": "ch3 Output Mux Selection[1:0] clk"}, {"Addr": "0x00EE", "Data": "0x30  ", "Delay": "0", "Remarks": "ch3 LVDS mode resistor disable"}, {"Addr": "0x00F0", "Data": "0xF3   ", "Delay": "0", "Remarks": "ch4 "}, {"Addr": "0x00F1", "Data": "0x3   ", "Delay": "0", "Remarks": "ch4 12-Bit Channel Divider[7:0] (LSB)SLOT3_FPGA_CLK 300M/3=100M"}, {"Addr": "0x00F2", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00F3", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Fine Analog Delay[4:0]"}, {"Addr": "0x00F4", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Coarse Digital Delay[4:0]"}, {"Addr": "0x00F5", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00F6", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00F7", "Data": "0x2   ", "Delay": "0", "Remarks": "ch4 Output Mux Selection[1:0]"}, {"Addr": "0x00F8", "Data": "0x8   ", "Delay": "0", "Remarks": "ch4 LVPECL mode resistor disable"}, {"Addr": "0x00FA", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch5 "}, {"Addr": "0x00FB", "Data": "0x3   ", "Delay": "0", "Remarks": "ch5 12-Bit Channel Divider[7:0] (LSB)SLOT4_REF_CLK 300M/3=100M"}, {"Addr": "0x00FC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00FD", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 Fine Analog Delay[4:0]"}, {"Addr": "0x00FE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 Coarse Digital Delay[4:0]"}, {"Addr": "0x00FF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0100", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0101", "Data": "0x2   ", "Delay": "0", "Remarks": "ch5 Output Mux Selection[1:0] clk"}, {"Addr": "0x0102", "Data": "0x8  ", "Delay": "0", "Remarks": "ch5 LVPECL mode resistor disable"}, {"Addr": "0x0104", "Data": "0xF3   ", "Delay": "0", "Remarks": "ch6 "}, {"Addr": "0x0105", "Data": "0x3   ", "Delay": "0", "Remarks": "ch6 12-Bit Channel Divider[7:0] (LSB)SLOT4_FPGA_CLK 300M/3=100M"}, {"Addr": "0x0106", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0107", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Fine Analog Delay[4:0]"}, {"Addr": "0x0108", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Coarse Digital Delay[4:0]"}, {"Addr": "0x0109", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x010A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x010B", "Data": "0x2   ", "Delay": "0", "Remarks": "ch6 Output Mux Selection[1:0]"}, {"Addr": "0x010C", "Data": "0x8   ", "Delay": "0", "Remarks": "ch6 LVPECL mode resistor disable"}, {"Addr": "0x010E", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch7 "}, {"Addr": "0x010F", "Data": "0x3   ", "Delay": "0", "Remarks": "ch7 12-Bit Channel Divider[7:0] (LSB) SLOT3_REF_CLK 300M/3=100M"}, {"Addr": "0x0110", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0111", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 Fine Analog Delay[4:0]"}, {"Addr": "0x0112", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 Coarse Digital Delay[4:0]"}, {"Addr": "0x0113", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0114", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0115", "Data": "0x2   ", "Delay": "0", "Remarks": "ch7 Output Mux Selection[1:0] clk"}, {"Addr": "0x0116", "Data": "0x8  ", "Delay": "0", "Remarks": "ch7 LVPECL mode resistor disable"}, {"Addr": "0x0118", "Data": "0xF2   ", "Delay": "0", "Remarks": "ch8 disable"}, {"Addr": "0x0119", "Data": "0x3   ", "Delay": "0", "Remarks": "ch8 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x011A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x011B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Fine Analog Delay[4:0]"}, {"Addr": "0x011C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Coarse Digital Delay[4:0]"}, {"Addr": "0x011D", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x011E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x011F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Output Mux Selection[1:0]"}, {"Addr": "0x0120", "Data": "0x30   ", "Delay": "0", "Remarks": "ch8 LVDS mode resistor disable"}, {"Addr": "0x0122", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch9 "}, {"Addr": "0x0123", "Data": "0xA   ", "Delay": "0", "Remarks": "ch9 12-Bit Channel Divider[7:0] (LSB) SW_CLK 300M/10=30MHZ"}, {"Addr": "0x0124", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0125", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Fine Analog Delay[4:0]"}, {"Addr": "0x0126", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Coarse Digital Delay[4:0]"}, {"Addr": "0x0127", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0128", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0129", "Data": "0x2   ", "Delay": "0", "Remarks": "ch9 Output Mux Selection[1:0] clk"}, {"Addr": "0x012A", "Data": "0x30  ", "Delay": "0", "Remarks": "ch9 LVDS mode resistor disable"}, {"Addr": "0x012C", "Data": "0xF3   ", "Delay": "0", "Remarks": "ch10 "}, {"Addr": "0x012D", "Data": "0x3   ", "Delay": "0", "Remarks": "ch10 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x012E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x012F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Fine Analog Delay[4:0]"}, {"Addr": "0x0130", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Coarse Digital Delay[4:0]"}, {"Addr": "0x0131", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0132", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0133", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Output Mux Selection[1:0]"}, {"Addr": "0x0134", "Data": "0x30   ", "Delay": "0", "Remarks": "ch10 LVDS mode resistor disable"}, {"Addr": "0x0136", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch11 disable"}, {"Addr": "0x0137", "Data": "0x3   ", "Delay": "0", "Remarks": "ch11 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0138", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0139", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 Fine Analog Delay[4:0]"}, {"Addr": "0x013A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 Coarse Digital Delay[4:0]"}, {"Addr": "0x013B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x013C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x013D", "Data": "0x2   ", "Delay": "0", "Remarks": "ch11 Output Mux Selection[1:0] clk--0x2 sclk--0x0"}, {"Addr": "0x013E", "Data": "0x30  ", "Delay": "0", "Remarks": "ch11 LVDS mode resistor disable"}, {"Addr": "0x0140", "Data": "0xF3   ", "Delay": "0", "Remarks": "ch12 channel "}, {"Addr": "0x0141", "Data": "0xA   ", "Delay": "0", "Remarks": "ch12 12-Bit Channel Divider[7:0] (LSB) SW_CLK 300M/10=30MHZ"}, {"Addr": "0x0142", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0143", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Fine Analog Delay[4:0]"}, {"Addr": "0x0144", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Coarse Digital Delay[4:0]"}, {"Addr": "0x0145", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0146", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0147", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Output Mux Selection[1:0]"}, {"Addr": "0x0148", "Data": "0x30   ", "Delay": "0", "Remarks": "ch12 LVDS mode resistor disable"}, {"Addr": "0x014A", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch13 "}, {"Addr": "0x014B", "Data": "0x3   ", "Delay": "0", "Remarks": "ch13 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x014C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x014D", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 Fine Analog Delay[4:0]"}, {"Addr": "0x014E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 Coarse Digital Delay[4:0]"}, {"Addr": "0x014F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0150", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0151", "Data": "0x2   ", "Delay": "0", "Remarks": "ch13 Output Mux Selection[1:0] clk"}, {"Addr": "0x0152", "Data": "0x30  ", "Delay": "0", "Remarks": "ch13 LVDS mode resistor disable"}, {"Addr": "0x0001", "Data": "0x02  ", "Delay": "100", "Remarks": "<PERSON><PERSON>"}, {"Addr": "0x0001", "Data": "0x00  ", "Delay": "0", "Remarks": "<PERSON><PERSON>"}, {"Addr": "0x0001", "Data": "0x80  ", "Delay": "100", "Remarks": "<PERSON><PERSON>"}, {"Addr": "0x0001", "Data": "0x00  ", "Delay": "0", "Remarks": "<PERSON><PERSON>"}], "BB_HMC7044": [{"Addr": "0x0000", "Data": "0x1  ", "Delay": "100", "Remarks": "reset"}, {"Addr": "0x0000", "Data": "0x0  ", "Delay": "100", "Remarks": "reset"}, {"Addr": "0x0004", "Data": "0x7F  ", "Delay": "0", "Remarks": "Seven Pairs of 14-Channel Outputs Enable[6:0] "}, {"Addr": "0x0005", "Data": "0x41   ", "Delay": "0", "Remarks": "SYNC Pin ModeSelection[7:6]PLL1 Reference PathEnable[3:0]"}, {"Addr": "0x000A", "Data": "0xF   ", "Delay": "0", "Remarks": "CLKIN0 Input Buffer Mode[3:0]Buffer enable[0] LVPECL|AC|Zin100|enable"}, {"Addr": "0x000B", "Data": "0x6   ", "Delay": "0", "Remarks": "CLKIN1 Input Buffer Mode[3:0]Buffer enable[0] AC|Zin100|enable"}, {"Addr": "0x000C", "Data": "0x2   ", "Delay": "0", "Remarks": "CLKIN2 Input Buffer Mode[3:0]Buffer enable[0] AC|disable"}, {"Addr": "0x000D", "Data": "0x0   ", "Delay": "0", "Remarks": "CLKIN3 Input Buffer Mode[3:0]Buffer enable[0] disable"}, {"Addr": "0x000E", "Data": "0x7   ", "Delay": "0", "Remarks": "OSCIN  Input Buffer Mode[3:0]Buffer enable[0] AC|Zin100|enable"}, {"Addr": "0x0003", "Data": "0x2F  ", "Delay": "0", "Remarks": "RF reseeder enable[5]PLL2 VCO Selection[4:3]SYSREF timer enable[2]PLL2 enable[1]PLL1 enable[0]"}, {"Addr": "0x0014", "Data": "0xE4  ", "Delay": "0", "Remarks": "PLL1 Reference Priority Control default"}, {"Addr": "0x001A", "Data": "0xF   ", "Delay": "0", "Remarks": "PLL1 CP Current"}, {"Addr": "0x001C", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN0"}, {"Addr": "0x001D", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN1"}, {"Addr": "0x001E", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN2"}, {"Addr": "0x001F", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN3"}, {"Addr": "0x0020", "Data": "0x8   ", "Delay": "0", "Remarks": "OSCIN Prescaler"}, {"Addr": "0x0021", "Data": "0x1   ", "Delay": "0", "Remarks": "PLL1 R1"}, {"Addr": "0x0026", "Data": "0xC   ", "Delay": "0", "Remarks": "PLL1 N1"}, {"Addr": "0x0028", "Data": "0x26   ", "Delay": "0", "Remarks": "PLL1 N1"}, {"Addr": "0x0032", "Data": "0x0   ", "Delay": "0", "Remarks": "PLL2 REF frequency doubler "}, {"Addr": "0x0033", "Data": "0x1   ", "Delay": "0", "Remarks": "PLL2 12-Bit R2 Divider[7:0](LSB)"}, {"Addr": "0x0034", "Data": "0x0   ", "Delay": "0", "Remarks": "PLL2 12-Bit R2 Divider[11:8](MSB)"}, {"Addr": "0x0035", "Data": "0xC   ", "Delay": "0", "Remarks": "PLL2 16-Bit N2 Divider[7:0] (LSB) set 12 "}, {"Addr": "0x0036", "Data": "0x0   ", "Delay": "0", "Remarks": "PLL2 16-Bit N2 Divider[15:8] (MSB)"}, {"Addr": "0x0037", "Data": "0xF   ", "Delay": "0", "Remarks": "PLL2 CPCurrent[3:0] 15MA"}, {"Addr": "0x0038", "Data": "0x18  ", "Delay": "0", "Remarks": "PLL2 PFD Control default"}, {"Addr": "0x0046", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO1"}, {"Addr": "0x0047", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO2"}, {"Addr": "0x0048", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO3"}, {"Addr": "0x0049", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO4"}, {"Addr": "0x0050", "Data": "0x1F  ", "Delay": "0", "Remarks": "GPO1 Lock detect signal from PLL1"}, {"Addr": "0x0052", "Data": "0x1F  ", "Delay": "0", "Remarks": "GPO1 Lock detect signal from PLL1"}, {"Addr": "0x0051", "Data": "0x2B  ", "Delay": "0", "Remarks": "GPO2 PLL2 lock detect signal from PLL2"}, {"Addr": "0x0064", "Data": "0x0   ", "Delay": "0", "Remarks": "Divide by 1 on clock input  "}, {"Addr": "0x0039", "Data": "0x01  ", "Delay": "0", "Remarks": "OSCOUTxpath enable[0] Enable"}, {"Addr": "0x003B", "Data": "0x03  ", "Delay": "0", "Remarks": "OSCOUT1 driver enable CML Mode internal 100 resistor "}, {"Addr": "0x00C8", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch0 "}, {"Addr": "0x00C9", "Data": "0x18  ", "Delay": "0", "Remarks": "ch0 12-Bit Channel Divider[7:0] (LSB)2880M/18=160MHZ DDR CLK"}, {"Addr": "0x00CA", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00CB", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Fine Analog Delay[4:0]"}, {"Addr": "0x00CC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Coarse Digital Delay[4:0]"}, {"Addr": "0x00CD", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00CE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00CF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Output Mux Selection[1:0]"}, {"Addr": "0x00D0", "Data": "0x30  ", "Delay": "0", "Remarks": "ch0 LVDS mode resistor disable"}, {"Addr": "0x00D2", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch1 "}, {"Addr": "0x00D3", "Data": "0x18  ", "Delay": "0", "Remarks": "ch1 12-Bit Channel Divider[7:0] (LSB)2880M/18=160MHZ DDR CLK"}, {"Addr": "0x00D4", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00D5", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 Fine Analog Delay[4:0]"}, {"Addr": "0x00D6", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 Coarse Digital Delay[4:0]"}, {"Addr": "0x00D7", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00D8", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00D9", "Data": "0x2   ", "Delay": "0", "Remarks": "ch1 Output Mux Selection[1:0]"}, {"Addr": "0x00DA", "Data": "0x30   ", "Delay": "0", "Remarks": "ch1 LVDS mode resistor disable"}, {"Addr": "0x00DC", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch2 "}, {"Addr": "0x00DD", "Data": "0x6   ", "Delay": "0", "Remarks": "ch2 12-Bit Channel Divider[7:0] (LSB) 2880M/3=960Mhz DACCLK"}, {"Addr": "0x00DE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00DF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Fine Analog Delay[4:0]"}, {"Addr": "0x00E0", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Coarse Digital Delay[4:0]"}, {"Addr": "0x00E1", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00E2", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00E3", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Output Mux Selection[1:0]"}, {"Addr": "0x00E4", "Data": "0x8   ", "Delay": "0", "Remarks": "ch2 LVPECL mode resistor disable"}, {"Addr": "0x00E6", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch3 disable"}, {"Addr": "0x00E7", "Data": "0x3   ", "Delay": "0", "Remarks": "ch3 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x00E8", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00E9", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 Fine Analog Delay[4:0]"}, {"Addr": "0x00EA", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 Coarse Digital Delay[4:0]"}, {"Addr": "0x00EB", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00EC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00ED", "Data": "0x2   ", "Delay": "0", "Remarks": "ch3 Output Mux Selection[1:0]"}, {"Addr": "0x00EE", "Data": "0x30  ", "Delay": "0", "Remarks": "ch3 LVDS mode resistor disable"}, {"Addr": "0x00F0", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch4 "}, {"Addr": "0x00F1", "Data": "0xc   ", "Delay": "0", "Remarks": "ch4 12-Bit Channel Divider[7:0] (LSB) 2880M/6=480MHZ ADCCLK"}, {"Addr": "0x00F2", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00F3", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Fine Analog Delay[4:0]"}, {"Addr": "0x00F4", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Coarse Digital Delay[4:0]"}, {"Addr": "0x00F5", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00F6", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00F7", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Output Mux Selection[1:0] clk"}, {"Addr": "0x00F8", "Data": "0x30   ", "Delay": "0", "Remarks": "ch4 LVPECL mode resistor disable"}, {"Addr": "0x00FA", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch5 disable"}, {"Addr": "0x00FB", "Data": "0x5   ", "Delay": "0", "Remarks": "ch5 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x00FC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00FD", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 Fine Analog Delay[4:0]"}, {"Addr": "0x00FE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 Coarse Digital Delay[4:0]"}, {"Addr": "0x00FF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0100", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0101", "Data": "0x2   ", "Delay": "0", "Remarks": "ch5 Output Mux Selection[1:0] clk"}, {"Addr": "0x0102", "Data": "0x8   ", "Delay": "0", "Remarks": "ch5 LVPECL mode resistor disable"}, {"Addr": "0x0104", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch6 "}, {"Addr": "0x0105", "Data": "0x18  ", "Delay": "0", "Remarks": "ch6 12-Bit Channel Divider[7:0] (LSB) 2880M/18=160MHZ FPGA CLK"}, {"Addr": "0x0106", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0107", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Fine Analog Delay[4:0]"}, {"Addr": "0x0108", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Coarse Digital Delay[4:0]"}, {"Addr": "0x0109", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x010A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x010B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Output Mux Selection[1:0] clk"}, {"Addr": "0x010C", "Data": "0x30  ", "Delay": "0", "Remarks": "ch6 LVDS mode resistor disable"}, {"Addr": "0x010E", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch7 "}, {"Addr": "0x010F", "Data": "0x3   ", "Delay": "0", "Remarks": "ch7 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0110", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0111", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 Fine Analog Delay[4:0]"}, {"Addr": "0x0112", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 Coarse Digital Delay[4:0]"}, {"Addr": "0x0113", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0114", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0115", "Data": "0x2   ", "Delay": "0", "Remarks": "ch7 Output Mux Selection[1:0] clk"}, {"Addr": "0x0116", "Data": "0x30  ", "Delay": "0", "Remarks": "ch7 LVDS mode resistor disable"}, {"Addr": "0x0118", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch8 "}, {"Addr": "0x0119", "Data": "0x20  ", "Delay": "0", "Remarks": "ch8 12-Bit Channel Divider[7:0] (LSB) 2880M/288=10MHZ RX_REF_CLK"}, {"Addr": "0x011A", "Data": "0x01  ", "Delay": "0", "Remarks": "ch8 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x011B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Fine Analog Delay[4:0]"}, {"Addr": "0x011C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Coarse Digital Delay[4:0]"}, {"Addr": "0x011D", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x011E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x011F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Output Mux Selection[1:0] clk"}, {"Addr": "0x0120", "Data": "0x08  ", "Delay": "0", "Remarks": "ch8 LVPECL mode resistor disable"}, {"Addr": "0x0122", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch9 disable"}, {"Addr": "0x0123", "Data": "0x3   ", "Delay": "0", "Remarks": "ch9 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0124", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0125", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Fine Analog Delay[4:0]"}, {"Addr": "0x0126", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Coarse Digital Delay[4:0]"}, {"Addr": "0x0127", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0128", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0129", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Output Mux Selection[1:0] 02|clk 00|sclk"}, {"Addr": "0x012A", "Data": "0x30  ", "Delay": "0", "Remarks": "ch9 LVDS mode resistor disable"}, {"Addr": "0x012C", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch10 "}, {"Addr": "0x012D", "Data": "0x20  ", "Delay": "0", "Remarks": "ch10 12-Bit Channel Divider[7:0] (LSB) 2880M/288=10MHZ TX_REF_CLK"}, {"Addr": "0x012E", "Data": "0x01  ", "Delay": "0", "Remarks": "ch10 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x012F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Fine Analog Delay[4:0]"}, {"Addr": "0x0130", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Coarse Digital Delay[4:0]"}, {"Addr": "0x0131", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0132", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0133", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Output Mux Selection[1:0]"}, {"Addr": "0x0134", "Data": "0x08  ", "Delay": "0", "Remarks": "ch10 LVPECL mode resistor disable"}, {"Addr": "0x0136", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch11 disable"}, {"Addr": "0x0137", "Data": "0x3   ", "Delay": "0", "Remarks": "ch11 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0138", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0139", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 Fine Analog Delay[4:0]"}, {"Addr": "0x013A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 Coarse Digital Delay[4:0]"}, {"Addr": "0x013B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x013C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x013D", "Data": "0x2   ", "Delay": "0", "Remarks": "ch11 Output Mux Selection[1:0] clk"}, {"Addr": "0x013E", "Data": "0x30  ", "Delay": "0", "Remarks": "ch11 LVDS mode resistor disable"}, {"Addr": "0x0140", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch12 channel disable"}, {"Addr": "0x0141", "Data": "0x3   ", "Delay": "0", "Remarks": "ch12 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0142", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0143", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Fine Analog Delay[4:0]"}, {"Addr": "0x0144", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Coarse Digital Delay[4:0]"}, {"Addr": "0x0145", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0146", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0147", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Output Mux Selection[1:0]"}, {"Addr": "0x0148", "Data": "0x30  ", "Delay": "0", "Remarks": "ch12 LVDS mode resistor disable"}, {"Addr": "0x014A", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch13 disable"}, {"Addr": "0x014B", "Data": "0x3   ", "Delay": "0", "Remarks": "ch13 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x014C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x014D", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 Fine Analog Delay[4:0]"}, {"Addr": "0x014E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 Coarse Digital Delay[4:0]"}, {"Addr": "0x014F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0150", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0151", "Data": "0x2   ", "Delay": "0", "Remarks": "ch13 Output Mux Selection[1:0] clk"}, {"Addr": "0x0152", "Data": "0x30  ", "Delay": "0", "Remarks": "ch13 LVDS mode resistor disable"}, {"Addr": "0x0001", "Data": "0x2  ", "Delay": "0", "Remarks": "Restart dividers/FSMs"}, {"Addr": "0x0001", "Data": "0x60  ", "Delay": "0", "Remarks": "phase noise High performance"}], "BB_HMC7044_428C": [{"Addr": "0x0000", "Data": "0x1  ", "Delay": "100", "Remarks": "reset"}, {"Addr": "0x0000", "Data": "0x0  ", "Delay": "100", "Remarks": "reset"}, {"Addr": "0x009F", "Data": "0x4D  ", "Delay": "0", "Remarks": " "}, {"Addr": "0x00A0", "Data": "0xDF  ", "Delay": "0", "Remarks": " "}, {"Addr": "0x00A5", "Data": "0x06  ", "Delay": "0", "Remarks": " "}, {"Addr": "0x00A8", "Data": "0x06  ", "Delay": "0", "Remarks": " "}, {"Addr": "0x00B0", "Data": "0x04  ", "Delay": "0", "Remarks": " "}, {"Addr": "0x0001", "Data": "0x40  ", "Delay": "0", "Remarks": "phase noise High performance"}, {"Addr": "0x0032", "Data": "0x0   ", "Delay": "0", "Remarks": "PLL2 REF frequency doubler "}, {"Addr": "0x0033", "Data": "0x1   ", "Delay": "0", "Remarks": "PLL2 12-Bit R2 Divider[7:0](LSB)"}, {"Addr": "0x0034", "Data": "0x0   ", "Delay": "0", "Remarks": "PLL2 12-Bit R2 Divider[11:8](MSB)"}, {"Addr": "0x0035", "Data": "0xC   ", "Delay": "0", "Remarks": "PLL2 16-Bit N2 Divider[7:0] (LSB) set 12 "}, {"Addr": "0x0036", "Data": "0x0   ", "Delay": "0", "Remarks": "PLL2 16-Bit N2 Divider[15:8] (MSB)"}, {"Addr": "0x0037", "Data": "0xF   ", "Delay": "0", "Remarks": "PLL2 CPCurrent[3:0] 15MA"}, {"Addr": "0x0038", "Data": "0x18  ", "Delay": "0", "Remarks": "PLL2 PFD Control default"}, {"Addr": "0x0021", "Data": "0x1   ", "Delay": "0", "Remarks": "PLL1 R1"}, {"Addr": "0x0026", "Data": "0xC   ", "Delay": "0", "Remarks": "PLL1 N1"}, {"Addr": "0x001A", "Data": "0xF   ", "Delay": "0", "Remarks": "PLL1 CP Current"}, {"Addr": "0x0003", "Data": "0x2F  ", "Delay": "0", "Remarks": "RF reseeder enable[5]PLL2 VCO Selection[4:3]SYSREF timer enable[2]PLL2 enable[1]PLL1 enable[0]"}, {"Addr": "0x001C", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN0"}, {"Addr": "0x001D", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN1"}, {"Addr": "0x001E", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN2"}, {"Addr": "0x001F", "Data": "0x1   ", "Delay": "0", "Remarks": "Prescaler divider CLKIN3"}, {"Addr": "0x0020", "Data": "0x8   ", "Delay": "0", "Remarks": "OSCIN Prescaler"}, {"Addr": "0x0005", "Data": "0x43   ", "Delay": "0", "Remarks": "SYNC Pin ModeSelection[7:6]PLL1 Reference PathEnable[3:0]"}, {"Addr": "0x000A", "Data": "0x5   ", "Delay": "0", "Remarks": "CLKIN0 Input Buffer Mode[3:0]Buffer enable[0] AC"}, {"Addr": "0x000B", "Data": "0x6   ", "Delay": "0", "Remarks": "CLKIN1 Input Buffer Mode[3:0]Buffer enable[0] AC|Zin100|enable"}, {"Addr": "0x000C", "Data": "0x2   ", "Delay": "0", "Remarks": "CLKIN2 Input Buffer Mode[3:0]Buffer enable[0] AC|disable"}, {"Addr": "0x000D", "Data": "0x0   ", "Delay": "0", "Remarks": "CLKIN3 Input Buffer Mode[3:0]Buffer enable[0] disable"}, {"Addr": "0x000E", "Data": "0x5   ", "Delay": "0", "Remarks": "OSCIN  Input Buffer Mode[3:0]Buffer enable[0] AC"}, {"Addr": "0x0028", "Data": "0x32  ", "Delay": "0", "Remarks": "PLL1 Lock detect ctl "}, {"Addr": "0x0029", "Data": "0x04  ", "Delay": "0", "Remarks": "PLL1 Reference swtich CLKIN0"}, {"Addr": "0x0046", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO1"}, {"Addr": "0x0047", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO2"}, {"Addr": "0x0048", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO3"}, {"Addr": "0x0049", "Data": "0x0   ", "Delay": "0", "Remarks": "GPIO4"}, {"Addr": "0x0050", "Data": "0x1F  ", "Delay": "0", "Remarks": "GPO1 Lock detect signal from PLL1"}, {"Addr": "0x0052", "Data": "0x1F  ", "Delay": "0", "Remarks": "GPO1 Lock detect signal from PLL1"}, {"Addr": "0x0051", "Data": "0x2B  ", "Delay": "0", "Remarks": "GPO2 PLL2 lock detect signal from PLL2"}, {"Addr": "0x0064", "Data": "0x0   ", "Delay": "0", "Remarks": "Divide by 1 on clock input  "}, {"Addr": "0x0039", "Data": "0x01  ", "Delay": "0", "Remarks": "OSCOUTxpath enable[0] Enable"}, {"Addr": "0x003B", "Data": "0x03  ", "Delay": "0", "Remarks": "OSCOUT1 driver enable CML Mode internal 100 resistor "}, {"Addr": "0x0004", "Data": "0x7F  ", "Delay": "0", "Remarks": "Seven Pairs of 14-Channel Outputs Enable[6:0] "}, {"Addr": "0x00C8", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch0 "}, {"Addr": "0x00C9", "Data": "0x18  ", "Delay": "0", "Remarks": "ch0 12-Bit Channel Divider[7:0] (LSB)2880M/24=120MHZ DDR CLK"}, {"Addr": "0x00CA", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00CB", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Fine Analog Delay[4:0]"}, {"Addr": "0x00CC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Coarse Digital Delay[4:0]"}, {"Addr": "0x00CD", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00CE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00CF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch0 Output Mux Selection[1:0]"}, {"Addr": "0x00D0", "Data": "0x30  ", "Delay": "0", "Remarks": "ch0 LVDS mode resistor disable"}, {"Addr": "0x00D2", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch1 "}, {"Addr": "0x00D3", "Data": "0x18  ", "Delay": "0", "Remarks": "ch1 12-Bit Channel Divider[7:0] (LSB)2880M/24=120MHZ DDR CLK"}, {"Addr": "0x00D4", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00D5", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 Fine Analog Delay[4:0]"}, {"Addr": "0x00D6", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 Coarse Digital Delay[4:0]"}, {"Addr": "0x00D7", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00D8", "Data": "0x0   ", "Delay": "0", "Remarks": "ch1 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00D9", "Data": "0x2   ", "Delay": "0", "Remarks": "ch1 Output Mux Selection[1:0]"}, {"Addr": "0x00DA", "Data": "0x30   ", "Delay": "0", "Remarks": "ch1 LVDS mode resistor disable"}, {"Addr": "0x00DC", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch2 "}, {"Addr": "0x00DD", "Data": "0x3   ", "Delay": "0", "Remarks": "ch2 12-Bit Channel Divider[7:0] (LSB) 2880M/3=960Mhz DACCLK"}, {"Addr": "0x00DE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00DF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Fine Analog Delay[4:0]"}, {"Addr": "0x00E0", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Coarse Digital Delay[4:0]"}, {"Addr": "0x00E1", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00E2", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00E3", "Data": "0x0   ", "Delay": "0", "Remarks": "ch2 Output Mux Selection[1:0]"}, {"Addr": "0x00E4", "Data": "0x8   ", "Delay": "0", "Remarks": "ch2 LVPECL mode resistor disable"}, {"Addr": "0x00E6", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch3 disable"}, {"Addr": "0x00E7", "Data": "0x3   ", "Delay": "0", "Remarks": "ch3 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x00E8", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00E9", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 Fine Analog Delay[4:0]"}, {"Addr": "0x00EA", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 Coarse Digital Delay[4:0]"}, {"Addr": "0x00EB", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00EC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch3 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00ED", "Data": "0x2   ", "Delay": "0", "Remarks": "ch3 Output Mux Selection[1:0]"}, {"Addr": "0x00EE", "Data": "0x30  ", "Delay": "0", "Remarks": "ch3 LVDS mode resistor disable"}, {"Addr": "0x00F0", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch4 "}, {"Addr": "0x00F1", "Data": "0x6   ", "Delay": "0", "Remarks": "ch4 12-Bit Channel Divider[7:0] (LSB) 2880M/6=480MHZ ADCCLK"}, {"Addr": "0x00F2", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00F3", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Fine Analog Delay[4:0]"}, {"Addr": "0x00F4", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Coarse Digital Delay[4:0]"}, {"Addr": "0x00F5", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x00F6", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x00F7", "Data": "0x0   ", "Delay": "0", "Remarks": "ch4 Output Mux Selection[1:0] clk"}, {"Addr": "0x00F8", "Data": "0x8   ", "Delay": "0", "Remarks": "ch4 LVPECL mode resistor disable"}, {"Addr": "0x00FA", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch5 disable"}, {"Addr": "0x00FB", "Data": "0x5   ", "Delay": "0", "Remarks": "ch5 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x00FC", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x00FD", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 Fine Analog Delay[4:0]"}, {"Addr": "0x00FE", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 Coarse Digital Delay[4:0]"}, {"Addr": "0x00FF", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0100", "Data": "0x0   ", "Delay": "0", "Remarks": "ch5 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0101", "Data": "0x2   ", "Delay": "0", "Remarks": "ch5 Output Mux Selection[1:0] clk"}, {"Addr": "0x0102", "Data": "0x8   ", "Delay": "0", "Remarks": "ch5 LVPECL mode resistor disable"}, {"Addr": "0x0104", "Data": "0xF3  ", "Delay": "0", "Remarks": "ch6 "}, {"Addr": "0x0105", "Data": "0x18  ", "Delay": "0", "Remarks": "ch6 12-Bit Channel Divider[7:0] (LSB) 2880M/24=120MHZ FPGA CLK"}, {"Addr": "0x0106", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0107", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Fine Analog Delay[4:0]"}, {"Addr": "0x0108", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Coarse Digital Delay[4:0]"}, {"Addr": "0x0109", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x010A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x010B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch6 Output Mux Selection[1:0] clk"}, {"Addr": "0x010C", "Data": "0x30  ", "Delay": "0", "Remarks": "ch6 LVDS mode resistor disable"}, {"Addr": "0x010E", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch7 "}, {"Addr": "0x010F", "Data": "0x3   ", "Delay": "0", "Remarks": "ch7 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0110", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0111", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 Fine Analog Delay[4:0]"}, {"Addr": "0x0112", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 Coarse Digital Delay[4:0]"}, {"Addr": "0x0113", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0114", "Data": "0x0   ", "Delay": "0", "Remarks": "ch7 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0115", "Data": "0x2   ", "Delay": "0", "Remarks": "ch7 Output Mux Selection[1:0] clk"}, {"Addr": "0x0116", "Data": "0x30  ", "Delay": "0", "Remarks": "ch7 LVDS mode resistor disable"}, {"Addr": "0x0118", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch8 "}, {"Addr": "0x0119", "Data": "0x20  ", "Delay": "0", "Remarks": "ch8 12-Bit Channel Divider[7:0] (LSB) 2880M/288=10MHZ RX_REF_CLK"}, {"Addr": "0x011A", "Data": "0x01  ", "Delay": "0", "Remarks": "ch8 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x011B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Fine Analog Delay[4:0]"}, {"Addr": "0x011C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Coarse Digital Delay[4:0]"}, {"Addr": "0x011D", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x011E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x011F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch8 Output Mux Selection[1:0] clk"}, {"Addr": "0x0120", "Data": "0x08  ", "Delay": "0", "Remarks": "ch8 LVPECL mode resistor disable"}, {"Addr": "0x0122", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch9 disable"}, {"Addr": "0x0123", "Data": "0x3   ", "Delay": "0", "Remarks": "ch9 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0124", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0125", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Fine Analog Delay[4:0]"}, {"Addr": "0x0126", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Coarse Digital Delay[4:0]"}, {"Addr": "0x0127", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0128", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0129", "Data": "0x0   ", "Delay": "0", "Remarks": "ch9 Output Mux Selection[1:0] 02|clk 00|sclk"}, {"Addr": "0x012A", "Data": "0x30  ", "Delay": "0", "Remarks": "ch9 LVDS mode resistor disable"}, {"Addr": "0x012C", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch10 "}, {"Addr": "0x012D", "Data": "0x20  ", "Delay": "0", "Remarks": "ch10 12-Bit Channel Divider[7:0] (LSB) 2880M/288=10MHZ TX_REF_CLK"}, {"Addr": "0x012E", "Data": "0x01  ", "Delay": "0", "Remarks": "ch10 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x012F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Fine Analog Delay[4:0]"}, {"Addr": "0x0130", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Coarse Digital Delay[4:0]"}, {"Addr": "0x0131", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0132", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0133", "Data": "0x0   ", "Delay": "0", "Remarks": "ch10 Output Mux Selection[1:0]"}, {"Addr": "0x0134", "Data": "0x08  ", "Delay": "0", "Remarks": "ch10 LVPECL mode resistor disable"}, {"Addr": "0x0136", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch11 disable"}, {"Addr": "0x0137", "Data": "0x3   ", "Delay": "0", "Remarks": "ch11 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0138", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0139", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 Fine Analog Delay[4:0]"}, {"Addr": "0x013A", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 Coarse Digital Delay[4:0]"}, {"Addr": "0x013B", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x013C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch11 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x013D", "Data": "0x2   ", "Delay": "0", "Remarks": "ch11 Output Mux Selection[1:0] clk"}, {"Addr": "0x013E", "Data": "0x30  ", "Delay": "0", "Remarks": "ch11 LVDS mode resistor disable"}, {"Addr": "0x0140", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch12 channel disable"}, {"Addr": "0x0141", "Data": "0x3   ", "Delay": "0", "Remarks": "ch12 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x0142", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x0143", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Fine Analog Delay[4:0]"}, {"Addr": "0x0144", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Coarse Digital Delay[4:0]"}, {"Addr": "0x0145", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0146", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0147", "Data": "0x0   ", "Delay": "0", "Remarks": "ch12 Output Mux Selection[1:0]"}, {"Addr": "0x0148", "Data": "0x30  ", "Delay": "0", "Remarks": "ch12 LVDS mode resistor disable"}, {"Addr": "0x014A", "Data": "0xF2  ", "Delay": "0", "Remarks": "ch13 disable"}, {"Addr": "0x014B", "Data": "0x3   ", "Delay": "0", "Remarks": "ch13 12-Bit Channel Divider[7:0] (LSB)"}, {"Addr": "0x014C", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Channel Divider[11:8] (MSB)"}, {"Addr": "0x014D", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 Fine Analog Delay[4:0]"}, {"Addr": "0x014E", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 Coarse Digital Delay[4:0]"}, {"Addr": "0x014F", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Multislip Digital Delay[7:0] (LSB) "}, {"Addr": "0x0150", "Data": "0x0   ", "Delay": "0", "Remarks": "ch13 12-Bit Multislip Digital Delay[11:8] (MSB) "}, {"Addr": "0x0151", "Data": "0x2   ", "Delay": "0", "Remarks": "ch13 Output Mux Selection[1:0] clk"}, {"Addr": "0x0152", "Data": "0x30  ", "Delay": "0", "Remarks": "ch13 LVDS mode resistor disable"}, {"Addr": "0x0001", "Data": "0x42  ", "Delay": "0", "Remarks": "Restart dividers/FSMs"}, {"Addr": "0x0001", "Data": "0x40  ", "Delay": "0", "Remarks": "phase noise High performance"}], "SWB_AD9228_Init": {"DevId1": [{"WriteOrRead": "0", "Addr": "0x05", "Data": "0x3f", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0x09", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0x14", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0x16", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0xff", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "1", "Addr": "0x16", "Data": "0x00", "Remarks": "read"}], "DevId2": [{"WriteOrRead": "0", "Addr": "0x05", "Data": "0x3f", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0x09", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0x14", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0x16", "Data": "0x04", "Remarks": ""}, {"WriteOrRead": "0", "Addr": "0xff", "Data": "0x01", "Remarks": ""}, {"WriteOrRead": "1", "Addr": "0x16", "Data": "0x00", "Remarks": "read"}]}, "VSA_IQSwap": {"Reg": [{"SwapValue": "0", "Remarks": "channel 0"}, {"SwapValue": "0", "Remarks": "channel 1"}]}, "VSG_IQSwap": {"Reg": [{"SwapValue": "0", "Remarks": ""}, {"SwapValue": "0", "Remarks": ""}]}, "VSA_ADC_Init": [{"Addr": "0x002", "Data": "0x03", "Remarks": ""}, {"Addr": "0x000", "Data": "0x00", "Remarks": ""}, {"Addr": "0x001", "Data": "0x00", "Remarks": ""}, {"Addr": "0x008", "Data": "0x03", "Remarks": ""}, {"Addr": "0x00A", "Data": "0x00", "Remarks": ""}, {"Addr": "0x015", "Data": "0x00", "Remarks": ""}, {"Addr": "0x016", "Data": "0x2c", "Remarks": ""}, {"Addr": "0x018", "Data": "0x20", "Remarks": ""}, {"Addr": "0x024", "Data": "0x00", "Remarks": ""}, {"Addr": "0x025", "Data": "0x0c", "Remarks": ""}, {"Addr": "0x028", "Data": "0x00", "Remarks": ""}, {"Addr": "0x030", "Data": "0x04", "Remarks": ""}, {"Addr": "0x03f", "Data": "0x80", "Remarks": ""}, {"Addr": "0x040", "Data": "0x3f", "Remarks": ""}, {"Addr": "0x10b", "Data": "0x00", "Remarks": ""}, {"Addr": "0x10c", "Data": "0x00", "Remarks": ""}, {"Addr": "0x10d", "Data": "0x00", "Remarks": ""}, {"Addr": "0x117", "Data": "0x00", "Remarks": ""}, {"Addr": "0x118", "Data": "0x00", "Remarks": ""}, {"Addr": "0x120", "Data": "0x00", "Remarks": ""}, {"Addr": "0x121", "Data": "0x00", "Remarks": ""}, {"Addr": "0x123", "Data": "0x00", "Remarks": ""}, {"Addr": "0x1ff", "Data": "0x00", "Remarks": ""}, {"Addr": "0x200", "Data": "0x00", "Remarks": ""}, {"Addr": "0x201", "Data": "0x00", "Remarks": ""}, {"Addr": "0x228", "Data": "0x00", "Remarks": ""}, {"Addr": "0x245", "Data": "0x00", "Remarks": ""}, {"Addr": "0x247", "Data": "0x00", "Remarks": ""}, {"Addr": "0x248", "Data": "0x00", "Remarks": ""}, {"Addr": "0x249", "Data": "0x00", "Remarks": ""}, {"Addr": "0x24a", "Data": "0x00", "Remarks": ""}, {"Addr": "0x24b", "Data": "0x00", "Remarks": ""}, {"Addr": "0x24c", "Data": "0x00", "Remarks": ""}, {"Addr": "0x26f", "Data": "0x00", "Remarks": ""}, {"Addr": "0x270", "Data": "0x00", "Remarks": ""}, {"Addr": "0x271", "Data": "0x80", "Remarks": ""}, {"Addr": "0x272", "Data": "0x00", "Remarks": ""}, {"Addr": "0x273", "Data": "0x00", "Remarks": ""}, {"Addr": "0x274", "Data": "0x01", "Remarks": ""}, {"Addr": "0x550", "Data": "0x00", "Remarks": ""}, {"Addr": "0x551", "Data": "0x00", "Remarks": ""}, {"Addr": "0x552", "Data": "0x00", "Remarks": ""}, {"Addr": "0x553", "Data": "0x00", "Remarks": ""}, {"Addr": "0x554", "Data": "0x00", "Remarks": ""}, {"Addr": "0x555", "Data": "0x00", "Remarks": ""}, {"Addr": "0x556", "Data": "0x00", "Remarks": ""}, {"Addr": "0x557", "Data": "0x00", "Remarks": ""}, {"Addr": "0x558", "Data": "0x00", "Remarks": ""}, {"Addr": "0x559", "Data": "0x00", "Remarks": ""}, {"Addr": "0x561", "Data": "0x01", "Remarks": ""}, {"Addr": "0x562", "Data": "0x00", "Remarks": ""}, {"Addr": "0x563", "Data": "0x00", "Remarks": ""}, {"Addr": "0x564", "Data": "0x00", "Remarks": ""}, {"Addr": "0x568", "Data": "0x01", "Remarks": ""}, {"Addr": "0x569", "Data": "0x01", "Remarks": ""}, {"Addr": "0x56a", "Data": "0x4c", "Remarks": ""}, {"Addr": "0x05b", "Data": "0x00", "Remarks": ""}, {"Addr": "0x002", "Data": "0x00", "Remarks": ""}], "VSA_ADC_Init_WT418": [{"Addr": "0x15", "Data": "0x1 ", "Remarks": ""}, {"Addr": "0xff", "Data": "0x1 ", "Remarks": ""}, {"Addr": "0x16", "Data": "0x0", "Remarks": ""}, {"Addr": "0xff", "Data": "0x1 ", "Remarks": ""}, {"Addr": "0x17", "Data": "0x8C", "Remarks": ""}, {"Addr": "0xff", "Data": "0x1 ", "Remarks": ""}], "VSG_DAC_Init": [{"Addr": "0x0 ", "Data": "0x20", "Remarks": ""}, {"Addr": "0x20", "Data": "0x1 ", "Remarks": ""}, {"Addr": "0x5e", "Data": "0xFE ", "Remarks": ""}, {"Addr": "0x5f", "Data": "0x67", "Remarks": ""}, {"Addr": "0xd ", "Data": "0x06", "Remarks": "DCI >350MHZ 0X06 <350MHZ 0X86"}, {"Addr": "0xa ", "Data": "0xC4", "Remarks": "Enable DLL and duty cycle correction. Set DLL phase offset to 4 "}, {"Addr": "0x26", "Data": "0x40", "Remarks": ""}, {"Addr": "0x28", "Data": "0x0", "Remarks": ""}, {"Addr": "0x25", "Data": "0x1", "Remarks": "fifo reset"}, {"Addr": "0x25", "Data": "0x0", "Remarks": "fifo recover"}, {"Addr": "0x3c", "Data": "0x0", "Remarks": ""}, {"Addr": "0x3e", "Data": "0x0", "Remarks": ""}, {"Addr": "0x27", "Data": "0x34", "Remarks": ""}, {"Addr": "0x1", "Data": "0x00", "Remarks": "Power up DAC outputs"}], "HMC705_Div_Ratio": [{"DivRatioN": "6", "StartFreq": "1600", "EndFreq": "1800", "Remarks": "266-300(MHz)"}, {"DivRatioN": "7", "StartFreq": "1800", "EndFreq": "2100", "Remarks": "257-300(MHz)"}, {"DivRatioN": "8", "StartFreq": "2100", "EndFreq": "2400", "Remarks": "262.5-300(MHz)"}, {"DivRatioN": "9", "StartFreq": "2400", "EndFreq": "2700", "Remarks": "266-300(MHz)"}, {"DivRatioN": "10", "StartFreq": "2700", "EndFreq": "3200", "Remarks": "270-320(MHz)"}], "LMX2594_Init": {"Reg": [{"Addr": "109", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "108", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "107", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "106", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "105", "Data": "0x0021       ", "Remarks": ""}, {"Addr": "104", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "103", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "102", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "101", "Data": "0x0011       ", "Remarks": ""}, {"Addr": "100", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "99 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "98 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "97 ", "Data": "0x0888       ", "Remarks": ""}, {"Addr": "96 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "95 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "94 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "93 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "92 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "91 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "90 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "89 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "88 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "87 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "86 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "85 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "84 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "83 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "82 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "81 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "80 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "79 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "78 ", "Data": "0x0003       ", "Remarks": ""}, {"Addr": "77 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "76 ", "Data": "0x000C       ", "Remarks": ""}, {"Addr": "75 ", "Data": "0x0800       ", "Remarks": ""}, {"Addr": "74 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "73 ", "Data": "0x003F       ", "Remarks": ""}, {"Addr": "72 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "71 ", "Data": "0x0081       ", "Remarks": ""}, {"Addr": "70 ", "Data": "0xC350       ", "Remarks": ""}, {"Addr": "69 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "68 ", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "67 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "66 ", "Data": "0x01F4       ", "Remarks": ""}, {"Addr": "65 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "64 ", "Data": "0x1388       ", "Remarks": ""}, {"Addr": "63 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "62 ", "Data": "0x0322       ", "Remarks": ""}, {"Addr": "61 ", "Data": "0x00A8       ", "Remarks": ""}, {"Addr": "60 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "59 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "58 ", "Data": "0x8001       ", "Remarks": ""}, {"Addr": "57 ", "Data": "0x0020       ", "Remarks": ""}, {"Addr": "56 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "55 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "54 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "53 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "52 ", "Data": "0x0820       ", "Remarks": ""}, {"Addr": "51 ", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "50 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "49 ", "Data": "0x4180       ", "Remarks": ""}, {"Addr": "48 ", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "47 ", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "46 ", "Data": "0x07FC       ", "Remarks": ""}, {"Addr": "45 ", "Data": "0xC8DF       ", "Remarks": ""}, {"Addr": "44 ", "Data": "0x1FA0       ", "Remarks": ""}, {"Addr": "43 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "42 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "41 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "40 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "39 ", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "38 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "37 ", "Data": "0x0304       ", "Remarks": ""}, {"Addr": "36 ", "Data": "0x002E       ", "Remarks": ""}, {"Addr": "35 ", "Data": "0x0004       ", "Remarks": ""}, {"Addr": "34 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "33 ", "Data": "0x1E21       ", "Remarks": ""}, {"Addr": "32 ", "Data": "0x0393       ", "Remarks": ""}, {"Addr": "31 ", "Data": "0x43EC       ", "Remarks": ""}, {"Addr": "30 ", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "29 ", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "28 ", "Data": "0x0488       ", "Remarks": ""}, {"Addr": "27 ", "Data": "0x0002       ", "Remarks": ""}, {"Addr": "26 ", "Data": "0x0DB0       ", "Remarks": ""}, {"Addr": "25 ", "Data": "0x0624       ", "Remarks": ""}, {"Addr": "24 ", "Data": "0x071A       ", "Remarks": ""}, {"Addr": "23 ", "Data": "0x007C       ", "Remarks": ""}, {"Addr": "22 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "21 ", "Data": "0x0401       ", "Remarks": ""}, {"Addr": "20 ", "Data": "0xF048       ", "Remarks": ""}, {"Addr": "19 ", "Data": "0x27B7       ", "Remarks": ""}, {"Addr": "18 ", "Data": "0x0064       ", "Remarks": ""}, {"Addr": "17 ", "Data": "0x012C       ", "Remarks": ""}, {"Addr": "16 ", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "15 ", "Data": "0x064F       ", "Remarks": ""}, {"Addr": "14 ", "Data": "0x1E70       ", "Remarks": ""}, {"Addr": "13 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "12 ", "Data": "0x5001       ", "Remarks": ""}, {"Addr": "11 ", "Data": "0x18         ", "Remarks": ""}, {"Addr": "10 ", "Data": "0x10D8       ", "Remarks": ""}, {"Addr": "9  ", "Data": "0x1604       ", "Remarks": ""}, {"Addr": "8  ", "Data": "0x2000       ", "Remarks": ""}, {"Addr": "7  ", "Data": "0x40B2       ", "Remarks": ""}, {"Addr": "6  ", "Data": "0xC802       ", "Remarks": ""}, {"Addr": "5  ", "Data": "0x00C8       ", "Remarks": ""}, {"Addr": "4  ", "Data": "0x0A43       ", "Remarks": ""}, {"Addr": "3  ", "Data": "0x642        ", "Remarks": ""}, {"Addr": "2  ", "Data": "0x500        ", "Remarks": ""}, {"Addr": "1  ", "Data": "0x808        ", "Remarks": ""}, {"Addr": "0  ", "Data": "0x251C       ", "Remarks": ""}], "FPD": 200, "Pll_Den": 1000}, "LMX2595_Init": {"VSGReg": [{"Addr": "112", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "111", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "110", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "109", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "108", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "107", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "106", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "105", "Data": "0x0021      ", "Remarks": ""}, {"Addr": "104", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "103", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "102", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "101", "Data": "0x0011       ", "Remarks": ""}, {"Addr": "100", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "99 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "98 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "97 ", "Data": "0x0888       ", "Remarks": ""}, {"Addr": "96 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "95 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "94 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "93 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "92 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "91 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "90 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "89 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "88 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "87 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "86 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "85 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "84 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "83 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "82 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "81 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "80 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "79 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "78 ", "Data": "0x0003       ", "Remarks": ""}, {"Addr": "77 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "76 ", "Data": "0x000C       ", "Remarks": ""}, {"Addr": "75 ", "Data": "0x0800       ", "Remarks": ""}, {"Addr": "74 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "73 ", "Data": "0x003F       ", "Remarks": ""}, {"Addr": "72 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "71 ", "Data": "0x0081       ", "Remarks": ""}, {"Addr": "70 ", "Data": "0xC350       ", "Remarks": ""}, {"Addr": "69 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "68 ", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "67 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "66 ", "Data": "0x01F4       ", "Remarks": ""}, {"Addr": "65 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "64 ", "Data": "0x1388       ", "Remarks": ""}, {"Addr": "63 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "62 ", "Data": "0x0322       ", "Remarks": ""}, {"Addr": "61 ", "Data": "0x00A8       ", "Remarks": ""}, {"Addr": "60 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "59 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "58 ", "Data": "0x9001       ", "Remarks": ""}, {"Addr": "57 ", "Data": "0x0020       ", "Remarks": ""}, {"Addr": "56 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "55 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "54 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "53 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "52 ", "Data": "0x0820       ", "Remarks": ""}, {"Addr": "51 ", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "50 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "49 ", "Data": "0x4180       ", "Remarks": ""}, {"Addr": "48 ", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "47 ", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "46 ", "Data": "0x07FC       ", "Remarks": ""}, {"Addr": "45 ", "Data": "0xC0DF       ", "Remarks": ""}, {"Addr": "44 ", "Data": "0x1F63       ", "Remarks": ""}, {"Addr": "43 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "42 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "41 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "40 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "39 ", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "38 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "37 ", "Data": "0x0304       ", "Remarks": ""}, {"Addr": "36 ", "Data": "0x002E       ", "Remarks": ""}, {"Addr": "35 ", "Data": "0x0004       ", "Remarks": ""}, {"Addr": "34 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "33 ", "Data": "0x1E21       ", "Remarks": ""}, {"Addr": "32 ", "Data": "0x0393       ", "Remarks": ""}, {"Addr": "31 ", "Data": "0x43EC       ", "Remarks": ""}, {"Addr": "30 ", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "29 ", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "28 ", "Data": "0x0488       ", "Remarks": ""}, {"Addr": "27 ", "Data": "0x0002       ", "Remarks": ""}, {"Addr": "26 ", "Data": "0x0DB0       ", "Remarks": ""}, {"Addr": "25 ", "Data": "0x0C2B       ", "Remarks": ""}, {"Addr": "24 ", "Data": "0x071A       ", "Remarks": ""}, {"Addr": "23 ", "Data": "0x007C       ", "Remarks": ""}, {"Addr": "22 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "21 ", "Data": "0x0401       ", "Remarks": ""}, {"Addr": "20 ", "Data": "0xE048       ", "Remarks": ""}, {"Addr": "19 ", "Data": "0x27B7       ", "Remarks": ""}, {"Addr": "18 ", "Data": "0x0064       ", "Remarks": ""}, {"Addr": "17 ", "Data": "0x012C       ", "Remarks": ""}, {"Addr": "16 ", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "15 ", "Data": "0x064F       ", "Remarks": ""}, {"Addr": "14 ", "Data": "0x1E70       ", "Remarks": ""}, {"Addr": "13 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "12 ", "Data": "0x5001       ", "Remarks": ""}, {"Addr": "11 ", "Data": "0x0018       ", "Remarks": ""}, {"Addr": "10 ", "Data": "0x10D8       ", "Remarks": ""}, {"Addr": "9  ", "Data": "0x1604       ", "Remarks": ""}, {"Addr": "8  ", "Data": "0x2000       ", "Remarks": ""}, {"Addr": "7  ", "Data": "0x40B2       ", "Remarks": ""}, {"Addr": "6  ", "Data": "0xC802       ", "Remarks": ""}, {"Addr": "5  ", "Data": "0x00C8       ", "Remarks": ""}, {"Addr": "4  ", "Data": "0x0A43       ", "Remarks": ""}, {"Addr": "3  ", "Data": "0x0642       ", "Remarks": ""}, {"Addr": "2  ", "Data": "0x0500       ", "Remarks": ""}, {"Addr": "1  ", "Data": "0x0808       ", "Remarks": ""}, {"Addr": "0  ", "Data": "0x251C       ", "Remarks": ""}], "VSAReg": [{"Addr": "112", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "111", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "110", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "109", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "108", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "107", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "106", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "105", "Data": "0x0021      ", "Remarks": ""}, {"Addr": "104", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "103", "Data": "0x0000      ", "Remarks": ""}, {"Addr": "102", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "101", "Data": "0x0011       ", "Remarks": ""}, {"Addr": "100", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "99 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "98 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "97 ", "Data": "0x0888       ", "Remarks": ""}, {"Addr": "96 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "95 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "94 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "93 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "92 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "91 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "90 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "89 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "88 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "87 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "86 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "85 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "84 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "83 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "82 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "81 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "80 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "79 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "78 ", "Data": "0x0003       ", "Remarks": ""}, {"Addr": "77 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "76 ", "Data": "0x000C       ", "Remarks": ""}, {"Addr": "75 ", "Data": "0x0800       ", "Remarks": ""}, {"Addr": "74 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "73 ", "Data": "0x003F       ", "Remarks": ""}, {"Addr": "72 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "71 ", "Data": "0x0081       ", "Remarks": ""}, {"Addr": "70 ", "Data": "0xC350       ", "Remarks": ""}, {"Addr": "69 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "68 ", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "67 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "66 ", "Data": "0x01F4       ", "Remarks": ""}, {"Addr": "65 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "64 ", "Data": "0x1388       ", "Remarks": ""}, {"Addr": "63 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "62 ", "Data": "0x0322       ", "Remarks": ""}, {"Addr": "61 ", "Data": "0x00A8       ", "Remarks": ""}, {"Addr": "60 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "59 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "58 ", "Data": "0x9001       ", "Remarks": ""}, {"Addr": "57 ", "Data": "0x0020       ", "Remarks": ""}, {"Addr": "56 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "55 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "54 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "53 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "52 ", "Data": "0x0820       ", "Remarks": ""}, {"Addr": "51 ", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "50 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "49 ", "Data": "0x4180       ", "Remarks": ""}, {"Addr": "48 ", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "47 ", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "46 ", "Data": "0x07FC       ", "Remarks": ""}, {"Addr": "45 ", "Data": "0xC0DF       ", "Remarks": ""}, {"Addr": "44 ", "Data": "0x1FA3       ", "Remarks": ""}, {"Addr": "43 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "42 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "41 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "40 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "39 ", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "38 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "37 ", "Data": "0x0304       ", "Remarks": ""}, {"Addr": "36 ", "Data": "0x002E       ", "Remarks": ""}, {"Addr": "35 ", "Data": "0x0004       ", "Remarks": ""}, {"Addr": "34 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "33 ", "Data": "0x1E21       ", "Remarks": ""}, {"Addr": "32 ", "Data": "0x0393       ", "Remarks": ""}, {"Addr": "31 ", "Data": "0x43EC       ", "Remarks": ""}, {"Addr": "30 ", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "29 ", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "28 ", "Data": "0x0488       ", "Remarks": ""}, {"Addr": "27 ", "Data": "0x0002       ", "Remarks": ""}, {"Addr": "26 ", "Data": "0x0DB0       ", "Remarks": ""}, {"Addr": "25 ", "Data": "0x0C2B       ", "Remarks": ""}, {"Addr": "24 ", "Data": "0x071A       ", "Remarks": ""}, {"Addr": "23 ", "Data": "0x007C       ", "Remarks": ""}, {"Addr": "22 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "21 ", "Data": "0x0401       ", "Remarks": ""}, {"Addr": "20 ", "Data": "0xE048       ", "Remarks": ""}, {"Addr": "19 ", "Data": "0x27B7       ", "Remarks": ""}, {"Addr": "18 ", "Data": "0x0064       ", "Remarks": ""}, {"Addr": "17 ", "Data": "0x012C       ", "Remarks": ""}, {"Addr": "16 ", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "15 ", "Data": "0x064F       ", "Remarks": ""}, {"Addr": "14 ", "Data": "0x1E70       ", "Remarks": ""}, {"Addr": "13 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "12 ", "Data": "0x5001       ", "Remarks": ""}, {"Addr": "11 ", "Data": "0x0018       ", "Remarks": ""}, {"Addr": "10 ", "Data": "0x10D8       ", "Remarks": ""}, {"Addr": "9  ", "Data": "0x1604       ", "Remarks": ""}, {"Addr": "8  ", "Data": "0x2000       ", "Remarks": ""}, {"Addr": "7  ", "Data": "0x40B2       ", "Remarks": ""}, {"Addr": "6  ", "Data": "0xC802       ", "Remarks": ""}, {"Addr": "5  ", "Data": "0x00C8       ", "Remarks": ""}, {"Addr": "4  ", "Data": "0x0A43       ", "Remarks": ""}, {"Addr": "3  ", "Data": "0x0642       ", "Remarks": ""}, {"Addr": "2  ", "Data": "0x0500       ", "Remarks": ""}, {"Addr": "1  ", "Data": "0x0808       ", "Remarks": ""}, {"Addr": "0  ", "Data": "0x251C       ", "Remarks": ""}], "FPD": 200, "Pll_Den": 1000}, "LMX2582_Init": {"VSGReg": [{"Addr": "64 ", "Data": "0x0377       ", "Remarks": ""}, {"Addr": "62 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "61 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "59 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "48 ", "Data": "0x03FD       ", "Remarks": ""}, {"Addr": "47 ", "Data": "0x08D8       ", "Remarks": ""}, {"Addr": "46 ", "Data": "0x1823       ", "Remarks": ""}, {"Addr": "45 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "44 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "43 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "42 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "41 ", "Data": "0x2800       ", "Remarks": ""}, {"Addr": "40 ", "Data": "0xEE6B       ", "Remarks": ""}, {"Addr": "39 ", "Data": "0x8204       ", "Remarks": ""}, {"Addr": "38 ", "Data": "0x0028       ", "Remarks": ""}, {"Addr": "37 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "36 ", "Data": "0x0021       ", "Remarks": ""}, {"Addr": "35 ", "Data": "0x089B       ", "Remarks": ""}, {"Addr": "34 ", "Data": "0xC3EA       ", "Remarks": ""}, {"Addr": "33 ", "Data": "0x2A0A       ", "Remarks": ""}, {"Addr": "32 ", "Data": "0x210A       ", "Remarks": ""}, {"Addr": "31 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "30 ", "Data": "0x0034       ", "Remarks": ""}, {"Addr": "29 ", "Data": "0x0084       ", "Remarks": ""}, {"Addr": "28 ", "Data": "0x2924       ", "Remarks": ""}, {"Addr": "25 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "24 ", "Data": "0x0509       ", "Remarks": ""}, {"Addr": "23 ", "Data": "0xD042       ", "Remarks": ""}, {"Addr": "22 ", "Data": "0x2377       ", "Remarks": ""}, {"Addr": "20 ", "Data": "0x012C       ", "Remarks": ""}, {"Addr": "19 ", "Data": "0x0965       ", "Remarks": ""}, {"Addr": "14 ", "Data": "0x0738       ", "Remarks": ""}, {"Addr": "13 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "12 ", "Data": "0x7001       ", "Remarks": ""}, {"Addr": "11 ", "Data": "0x0018       ", "Remarks": ""}, {"Addr": "10 ", "Data": "0x10D8       ", "Remarks": ""}, {"Addr": "9  ", "Data": "0x0302       ", "Remarks": ""}, {"Addr": "8  ", "Data": "0x1084       ", "Remarks": ""}, {"Addr": "7  ", "Data": "0x28B2       ", "Remarks": ""}, {"Addr": "4  ", "Data": "0x1943       ", "Remarks": ""}, {"Addr": "2  ", "Data": "0x0500       ", "Remarks": ""}, {"Addr": "1  ", "Data": "0x0808       ", "Remarks": ""}, {"Addr": "0  ", "Data": "0x221C       ", "Remarks": ""}], "VSAReg": [{"Addr": "64 ", "Data": "0x0377       ", "Remarks": ""}, {"Addr": "62 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "61 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "59 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "48 ", "Data": "0x03FD       ", "Remarks": ""}, {"Addr": "47 ", "Data": "0x08D8       ", "Remarks": ""}, {"Addr": "46 ", "Data": "0x1823       ", "Remarks": ""}, {"Addr": "45 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "44 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "43 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "42 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "41 ", "Data": "0x2800       ", "Remarks": ""}, {"Addr": "40 ", "Data": "0xEE6B       ", "Remarks": ""}, {"Addr": "39 ", "Data": "0x8204       ", "Remarks": ""}, {"Addr": "38 ", "Data": "0x0028       ", "Remarks": ""}, {"Addr": "37 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "36 ", "Data": "0x0021       ", "Remarks": ""}, {"Addr": "35 ", "Data": "0x089B       ", "Remarks": ""}, {"Addr": "34 ", "Data": "0xC3EA       ", "Remarks": ""}, {"Addr": "33 ", "Data": "0x2A0A       ", "Remarks": ""}, {"Addr": "32 ", "Data": "0x210A       ", "Remarks": ""}, {"Addr": "31 ", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "30 ", "Data": "0x0034       ", "Remarks": ""}, {"Addr": "29 ", "Data": "0x0084       ", "Remarks": ""}, {"Addr": "28 ", "Data": "0x2924       ", "Remarks": ""}, {"Addr": "25 ", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "24 ", "Data": "0x0509       ", "Remarks": ""}, {"Addr": "23 ", "Data": "0xD042       ", "Remarks": ""}, {"Addr": "22 ", "Data": "0x2377       ", "Remarks": ""}, {"Addr": "20 ", "Data": "0x012C       ", "Remarks": ""}, {"Addr": "19 ", "Data": "0x0965       ", "Remarks": ""}, {"Addr": "14 ", "Data": "0x0738       ", "Remarks": ""}, {"Addr": "13 ", "Data": "0x4000       ", "Remarks": ""}, {"Addr": "12 ", "Data": "0x7001       ", "Remarks": ""}, {"Addr": "11 ", "Data": "0x0018       ", "Remarks": ""}, {"Addr": "10 ", "Data": "0x10D8       ", "Remarks": ""}, {"Addr": "9  ", "Data": "0x0302       ", "Remarks": ""}, {"Addr": "8  ", "Data": "0x1084       ", "Remarks": ""}, {"Addr": "7  ", "Data": "0x28B2       ", "Remarks": ""}, {"Addr": "4  ", "Data": "0x1943       ", "Remarks": ""}, {"Addr": "2  ", "Data": "0x0500       ", "Remarks": ""}, {"Addr": "1  ", "Data": "0x0808       ", "Remarks": ""}, {"Addr": "0  ", "Data": "0x221C       ", "Remarks": ""}], "FPD": 100, "Pll_Den": "4000000000"}, "LMX2582_Channel_Divider_vs_VCO_Frequency": [{"OutFreqMin": "400", "OutFreqMax": "550", "ChdivSeg1": "3", "ChdivSeg2": "4", "ChdivSeg3": "1", "TotalDivision": "12"}, {"OutFreqMin": "550", "OutFreqMax": "800", "ChdivSeg1": "2", "ChdivSeg2": "4", "ChdivSeg3": "1", "TotalDivision": "8"}, {"OutFreqMin": "800", "OutFreqMax": "1180", "ChdivSeg1": "3", "ChdivSeg2": "2", "ChdivSeg3": "1", "TotalDivision": "6"}, {"OutFreqMin": "1180", "OutFreqMax": "1750", "ChdivSeg1": "2", "ChdivSeg2": "2", "ChdivSeg3": "1", "TotalDivision": "4"}, {"OutFreqMin": "1750", "OutFreqMax": "2330", "ChdivSeg1": "3", "ChdivSeg2": "1", "ChdivSeg3": "1", "TotalDivision": "3"}, {"OutFreqMin": "2330", "OutFreqMax": "3550", "ChdivSeg1": "2", "ChdivSeg2": "1", "ChdivSeg3": "1", "TotalDivision": "2"}, {"OutFreqMin": "20", "OutFreqMax": "28", "ChdivSeg1": "3", "ChdivSeg2": "8", "ChdivSeg3": "8", "TotalDivision": "192"}, {"OutFreqMin": "28", "OutFreqMax": "37", "ChdivSeg1": "2", "ChdivSeg2": "8", "ChdivSeg3": "8", "TotalDivision": "128"}, {"OutFreqMin": "37", "OutFreqMax": "56", "ChdivSeg1": "2", "ChdivSeg2": "8", "ChdivSeg3": "6", "TotalDivision": "96"}, {"OutFreqMin": "56", "OutFreqMax": "74", "ChdivSeg1": "2", "ChdivSeg2": "8", "ChdivSeg3": "4", "TotalDivision": "64"}, {"OutFreqMin": "74", "OutFreqMax": "99", "ChdivSeg1": "3", "ChdivSeg2": "8", "ChdivSeg3": "2", "TotalDivision": "48"}, {"OutFreqMin": "99", "OutFreqMax": "111", "ChdivSeg1": "3", "ChdivSeg2": "6", "ChdivSeg3": "2", "TotalDivision": "36"}, {"OutFreqMin": "111", "OutFreqMax": "148", "ChdivSeg1": "2", "ChdivSeg2": "8", "ChdivSeg3": "2", "TotalDivision": "32"}, {"OutFreqMin": "148", "OutFreqMax": "222", "ChdivSeg1": "3", "ChdivSeg2": "8", "ChdivSeg3": "1", "TotalDivision": "24"}, {"OutFreqMin": "222", "OutFreqMax": "296", "ChdivSeg1": "2", "ChdivSeg2": "8", "ChdivSeg3": "1", "TotalDivision": "16"}, {"OutFreqMin": "296", "OutFreqMax": "400", "ChdivSeg1": "2", "ChdivSeg2": "6", "ChdivSeg3": "1", "TotalDivision": "12"}], "LMX2582_VCO_SEL": [{"FreqMin": "3550", "FreqMax": "4000", "VcoSel": "1"}, {"FreqMin": "4001", "FreqMax": "4520", "VcoSel": "2"}, {"FreqMin": "4521", "FreqMax": "5000", "VcoSel": "3"}, {"FreqMin": "5001", "FreqMax": "5550", "VcoSel": "4"}, {"FreqMin": "5551", "FreqMax": "6050", "VcoSel": "5"}, {"FreqMin": "6051", "FreqMax": "6600", "VcoSel": "6"}, {"FreqMin": "6601", "FreqMax": "7100", "VcoSel": "7"}], "ModFreq_Channel": [{"StartFreq": "300", "EndFreq": "520", "Remarks": ""}, {"StartFreq": "520", "EndFreq": "750", "Remarks": ""}, {"StartFreq": "750", "EndFreq": "1100", "Remarks": ""}, {"StartFreq": "1100", "EndFreq": "1700", "Remarks": ""}, {"StartFreq": "1700", "EndFreq": "2250", "Remarks": ""}, {"StartFreq": "2250", "EndFreq": "3400", "Remarks": ""}, {"StartFreq": "3400", "EndFreq": "4800", "Remarks": ""}, {"StartFreq": "4800", "EndFreq": "8200", "Remarks": ""}], "RfModLoFreq_Channel_VA": {"VSG": [{"StartFreq": "0", "EndFreq": "500", "Remarks": ""}, {"StartFreq": "500", "EndFreq": "800", "Remarks": ""}, {"StartFreq": "800", "EndFreq": "1500", "Remarks": ""}, {"StartFreq": "1500", "EndFreq": "2500", "Remarks": ""}, {"StartFreq": "2500", "EndFreq": "3900", "Remarks": ""}, {"StartFreq": "3900", "EndFreq": "6400", "Remarks": ""}], "VSA": [{"StartFreq": "0", "EndFreq": "500", "Remarks": ""}, {"StartFreq": "500", "EndFreq": "800", "Remarks": ""}, {"StartFreq": "800", "EndFreq": "1500", "Remarks": ""}, {"StartFreq": "1500", "EndFreq": "2500", "Remarks": ""}, {"StartFreq": "2500", "EndFreq": "3900", "Remarks": ""}, {"StartFreq": "3900", "EndFreq": "6400", "Remarks": ""}]}, "RfMixLoFreq_Channel_VA": {"VSG": [{"StartFreq": "300", "EndFreq": "5500", "Remarks": ""}, {"StartFreq": "5500", "EndFreq": "8200", "Remarks": ""}], "VSA": [{"StartFreq": "300", "EndFreq": "5500", "Remarks": ""}, {"StartFreq": "5500", "EndFreq": "8200", "Remarks": ""}]}, "RfModLoFreq_Channel": {"VSG": [{"StartFreq": "300", "EndFreq": "500", "Remarks": ""}, {"StartFreq": "500", "EndFreq": "800", "Remarks": ""}, {"StartFreq": "800", "EndFreq": "1500", "Remarks": ""}, {"StartFreq": "1500", "EndFreq": "2250", "Remarks": ""}, {"StartFreq": "2250", "EndFreq": "3400", "Remarks": ""}, {"StartFreq": "3400", "EndFreq": "6400", "Remarks": ""}], "VSA": [{"StartFreq": "300", "EndFreq": "500", "Remarks": ""}, {"StartFreq": "500", "EndFreq": "800", "Remarks": ""}, {"StartFreq": "800", "EndFreq": "1500", "Remarks": ""}, {"StartFreq": "1500", "EndFreq": "2250", "Remarks": ""}, {"StartFreq": "2250", "EndFreq": "3400", "Remarks": ""}, {"StartFreq": "3400", "EndFreq": "6400", "Remarks": ""}]}, "RfMixLoFreq_Channel": {"VSG": [{"StartFreq": "300", "EndFreq": "6000", "Remarks": ""}, {"StartFreq": "6000", "EndFreq": "8200", "Remarks": ""}], "VSA": [{"StartFreq": "300", "EndFreq": "6000", "Remarks": ""}, {"StartFreq": "6000", "EndFreq": "8200", "Remarks": ""}]}, "RfModLoFreq_Channel_418VA": {"VSG": [{"StartFreq": "300", "EndFreq": "500", "Remarks": ""}, {"StartFreq": "500", "EndFreq": "800", "Remarks": ""}, {"StartFreq": "800", "EndFreq": "1500", "Remarks": ""}, {"StartFreq": "1500", "EndFreq": "2400", "Remarks": ""}, {"StartFreq": "2400", "EndFreq": "3600", "Remarks": ""}, {"StartFreq": "3600", "EndFreq": "6400", "Remarks": ""}], "VSA": [{"StartFreq": "300", "EndFreq": "500", "Remarks": ""}, {"StartFreq": "500", "EndFreq": "800", "Remarks": ""}, {"StartFreq": "800", "EndFreq": "1500", "Remarks": ""}, {"StartFreq": "1500", "EndFreq": "2400", "Remarks": ""}, {"StartFreq": "2400", "EndFreq": "3600", "Remarks": ""}, {"StartFreq": "3600", "EndFreq": "6400", "Remarks": ""}]}, "DDS_VCO_SELECT": [{"StartFreq": "5650", "EndFreq": "6550", "Index": "28", "Remarks": ""}, {"StartFreq": "6550", "EndFreq": "7550", "Index": "30", "Remarks": ""}], "LO_SETTING_VB": {"FRef": 0, "FRef_Remarks": "0:100M, 1:700M, 2:900M", "ModMashOrder": 3, "MixMashOrder": 2, "VsgModPwr": 7, "VsgMixPwr": 7, "VsaModPwr": 7, "VsaMixPwr": 7, "Pwr_Remarks": "0-7"}, "LO_SETTING_VC": {"FRef": 0, "FRef_Remarks": "0:100M, 1:700M, 2:900M", "ModMashOrder": 3, "MixMashOrder": 2, "VsgModPwr": 5, "VsgMixPwr": 7, "VsaModPwr": 5, "VsaMixPwr": 7, "VsgModPwrB": 5, "VsgMixPwrB": 7, "VsaModPwrB": 5, "VsaMixPwrB": 7, "Pwr_Remarks": "0-7", "LOComMode": 1, "LOComMode_Remarks": "LoComMode:0,default:1"}, "LO_SETTING_WT418_VA": {"FRef": 0, "FRef_Remarks": "0:100M, 1:700M, 2:900M", "ModMashOrder": 3, "MixMashOrder": 2, "VsgModPwr": 5, "VsgMixPwr": 7, "VsaModPwr": 5, "VsaMixPwr": 7, "VsgModPwrB": 5, "VsgMixPwrB": 7, "VsaModPwrB": 5, "VsaMixPwrB": 7, "Pwr_Remarks": "0-7", "LOComMode": 1, "LOComMode_Remarks": "LoComMode:0,default:1"}, "HMC833_Init": {"Reg": [{"Addr": "1  ", "Data": "0x2     ", "Remarks": ""}, {"Addr": "2  ", "Data": "0x2     ", "Remarks": ""}, {"Addr": "6  ", "Data": "0x200B4A", "Remarks": "0x200B4A:Fractional part mode; 0x2003CA:Integer mode"}, {"Addr": "7  ", "Data": "0x214D  ", "Remarks": ""}, {"Addr": "8  ", "Data": "0xC1BEFF", "Remarks": ""}, {"Addr": "9  ", "Data": "0x400000", "Remarks": ""}, {"Addr": "0xA", "Data": "0x2005  ", "Remarks": ""}, {"Addr": "0xB", "Data": "0x7C061 ", "Remarks": ""}, {"Addr": "0xC", "Data": "0x500   ", "Remarks": ""}, {"Addr": "0xF", "Data": "0x81    ", "Remarks": ""}, {"Addr": "5  ", "Data": "0xff00  ", "Remarks": ""}, {"Addr": "3  ", "Data": "0x34    ", "Remarks": ""}, {"Addr": "4  ", "Data": "0x0     ", "Remarks": ""}, {"Addr": "5  ", "Data": "0x4110  ", "Remarks": ""}, {"Addr": "5  ", "Data": "0x2898  ", "Remarks": ""}, {"Addr": "5  ", "Data": "0x0     ", "Remarks": ""}], "Reg9PumpValue": [{"StartBitIndex": "0", "EndBitIndex": "6", "BitsData": "0x7f", "Remarks": "Bit[0-6]:Charge Pump DN Gain Control/1.2mA"}, {"StartBitIndex": "7", "EndBitIndex": "13", "BitsData": "0x7f", "Remarks": "Bit[7-13]:Charge Pump UP Gain Control/1.2mA"}, {"StartBitIndex": "14", "EndBitIndex": "20", "BitsData": "0x30", "Remarks": "Bit[14-20]:Charge Pump Offset/0.095mA"}, {"StartBitIndex": "21", "EndBitIndex": "22", "BitsData": "0x0 ", "Remarks": "Bit[21]:CPSrcEn,Source Offset current);Bit[22]:CPSrcEn,Sink Offset current)"}]}, "HMC833_PD_Config": {"Fxtal": 100, "FxtalR": 2}, "LMX2820_Init": {"Reg": [{"Addr": "112", "Data": "0xFFFF       ", "Remarks": ""}, {"Addr": "111", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "110", "Data": "0x001F       ", "Remarks": ""}, {"Addr": "109", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "108", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "107", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "106", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "105", "Data": "0x000A       ", "Remarks": ""}, {"Addr": "104", "Data": "0x0014       ", "Remarks": ""}, {"Addr": "103", "Data": "0x0014       ", "Remarks": ""}, {"Addr": "102", "Data": "0x0028       ", "Remarks": ""}, {"Addr": "101", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "100", "Data": "0x0533       ", "Remarks": ""}, {"Addr": "99", "Data": "0x19B9       ", "Remarks": ""}, {"Addr": "98", "Data": "0x1C80       ", "Remarks": ""}, {"Addr": "97", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "96", "Data": "0x17F8       ", "Remarks": ""}, {"Addr": "95", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "94", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "93", "Data": "0x1000       ", "Remarks": ""}, {"Addr": "92", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "91", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "90", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "89", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "88", "Data": "0x03FF       ", "Remarks": ""}, {"Addr": "87", "Data": "0xFF00       ", "Remarks": ""}, {"Addr": "86", "Data": "0x0040       ", "Remarks": ""}, {"Addr": "85", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "84", "Data": "0x0040       ", "Remarks": ""}, {"Addr": "83", "Data": "0x0F00       ", "Remarks": ""}, {"Addr": "82", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "81", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "80", "Data": "0x01C0       ", "Remarks": ""}, {"Addr": "79", "Data": "0x011E       ", "Remarks": ""}, {"Addr": "78", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "77", "Data": "0x0608       ", "Remarks": ""}, {"Addr": "76", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "75", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "74", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "73", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "72", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "71", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "70", "Data": "0x000E       ", "Remarks": ""}, {"Addr": "69", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "68", "Data": "0x0020       ", "Remarks": ""}, {"Addr": "67", "Data": "0x1000       ", "Remarks": ""}, {"Addr": "66", "Data": "0x003F       ", "Remarks": ""}, {"Addr": "65", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "64", "Data": "0x4080       ", "Remarks": ""}, {"Addr": "63", "Data": "0xC350       ", "Remarks": ""}, {"Addr": "62", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "61", "Data": "0x03E8       ", "Remarks": ""}, {"Addr": "60", "Data": "0x01F4       ", "Remarks": ""}, {"Addr": "59", "Data": "0x1388       ", "Remarks": ""}, {"Addr": "58", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "57", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "56", "Data": "0x0001       ", "Remarks": ""}, {"Addr": "55", "Data": "0x0002       ", "Remarks": ""}, {"Addr": "54", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "53", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "52", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "51", "Data": "0x203F       ", "Remarks": ""}, {"Addr": "50", "Data": "0x0080       ", "Remarks": ""}, {"Addr": "49", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "48", "Data": "0x4180       ", "Remarks": ""}, {"Addr": "47", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "46", "Data": "0x0300       ", "Remarks": ""}, {"Addr": "45", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "44", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "43", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "42", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "41", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "40", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "39", "Data": "0x2710       ", "Remarks": ""}, {"Addr": "38", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "37", "Data": "0x0500       ", "Remarks": ""}, {"Addr": "36", "Data": "0x001E       ", "Remarks": ""}, {"Addr": "35", "Data": "0x3040       ", "Remarks": ""}, {"Addr": "34", "Data": "0x0010       ", "Remarks": ""}, {"Addr": "33", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "32", "Data": "0x1041       ", "Remarks": ""}, {"Addr": "31", "Data": "0x0401       ", "Remarks": ""}, {"Addr": "30", "Data": "0xB18C       ", "Remarks": ""}, {"Addr": "29", "Data": "0x318C       ", "Remarks": ""}, {"Addr": "28", "Data": "0x0639       ", "Remarks": ""}, {"Addr": "27", "Data": "0x8001       ", "Remarks": ""}, {"Addr": "26", "Data": "0x0DB0       ", "Remarks": ""}, {"Addr": "25", "Data": "0x0624       ", "Remarks": ""}, {"Addr": "24", "Data": "0x0E34       ", "Remarks": ""}, {"Addr": "23", "Data": "0x1102       ", "Remarks": ""}, {"Addr": "22", "Data": "0xE2BF       ", "Remarks": ""}, {"Addr": "21", "Data": "0x1C64       ", "Remarks": ""}, {"Addr": "20", "Data": "0x272C       ", "Remarks": ""}, {"Addr": "19", "Data": "0x2120       ", "Remarks": ""}, {"Addr": "18", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "17", "Data": "0x15C0       ", "Remarks": ""}, {"Addr": "16", "Data": "0x171C       ", "Remarks": ""}, {"Addr": "15", "Data": "0x2001       ", "Remarks": ""}, {"Addr": "14", "Data": "0x3001       ", "Remarks": ""}, {"Addr": "13", "Data": "0x0038       ", "Remarks": ""}, {"Addr": "12", "Data": "0x0408       ", "Remarks": ""}, {"Addr": "11", "Data": "0x0612       ", "Remarks": ""}, {"Addr": "10", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "9", "Data": "0x0005       ", "Remarks": ""}, {"Addr": "8", "Data": "0xC802       ", "Remarks": ""}, {"Addr": "7", "Data": "0x0000       ", "Remarks": ""}, {"Addr": "6", "Data": "0x0A43       ", "Remarks": ""}, {"Addr": "5", "Data": "0x0032       ", "Remarks": ""}, {"Addr": "4", "Data": "0x4202       ", "Remarks": ""}, {"Addr": "3", "Data": "0x0041       ", "Remarks": ""}, {"Addr": "2", "Data": "0x81F4       ", "Remarks": ""}, {"Addr": "1", "Data": "0x57A0       ", "Remarks": ""}, {"Addr": "0", "Data": "0x6470       ", "Remarks": ""}], "FPD": 200, "PLL_DEN": 1000}, "LO_DDS_AD9912": {"Init": [{"Addr": "0x0000", "Data": "0x18  ", "Remarks": ""}, {"Addr": "0x0002", "Data": "0x82  ", "Remarks": ""}, {"Addr": "0x0003", "Data": "0x19  ", "Remarks": ""}, {"Addr": "0x0004", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0005", "Data": "0x01  ", "Remarks": ""}, {"Addr": "0x0010", "Data": "0x90  ", "Remarks": ""}, {"Addr": "0x0020", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0022", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0104", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0105", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0106", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01A6", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01A7", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01A8", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01A9", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01AA", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01AB", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01AC", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x01AD", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0200", "Data": "0x01  ", "Remarks": ""}, {"Addr": "0x0201", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x040B", "Data": "0xFF  ", "Remarks": ""}, {"Addr": "0x040C", "Data": "0x03  ", "Remarks": ""}, {"Addr": "0x0500", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0501", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0503", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0504", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0505", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0506", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0508", "Data": "0x00  ", "Remarks": ""}, {"Addr": "0x0509", "Data": "0x00  ", "Remarks": ""}], "TypeM": [{"Addr": "0x0012", "Remarks": ""}, {"Addr": "0x0013", "Remarks": ""}, {"Addr": "0x01A6", "Remarks": ""}, {"Addr": "0x01A7", "Remarks": ""}, {"Addr": "0x01A8", "Remarks": ""}, {"Addr": "0x01A9", "Remarks": ""}, {"Addr": "0x01AA", "Remarks": ""}, {"Addr": "0x01AB", "Remarks": ""}, {"Addr": "0x01AC", "Remarks": ""}, {"Addr": "0x01AD", "Remarks": ""}, {"Addr": "0x0500", "Remarks": ""}, {"Addr": "0x0501", "Remarks": ""}, {"Addr": "0x0503", "Remarks": ""}, {"Addr": "0x0504", "Remarks": ""}, {"Addr": "0x0505", "Remarks": ""}, {"Addr": "0x0506", "Remarks": ""}, {"Addr": "0x0508", "Remarks": ""}, {"Addr": "0x0509", "Remarks": ""}]}, "Fan_Setting": [{"FanId": "1", "FanSpeed": "60 ", "Remarks": "Switch Board Fan"}, {"FanId": "2", "FanSpeed": "60 ", "Remarks": "Bussiness Board Fan"}, {"FanId": "3", "FanSpeed": "60 ", "Remarks": "Controler <PERSON>"}], "BPVoltInfo": [{"VoltChannel": "0 ", "MultVolt": "2   ", "VoltChannelInfo": "3V3_FPGA_MON ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "1 ", "MultVolt": "1   ", "VoltChannelInfo": "PEX_VDD_0V9A ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "2 ", "MultVolt": "1   ", "VoltChannelInfo": "1V1_FPGA_MON ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "3 ", "MultVolt": "1   ", "VoltChannelInfo": "PEX_VDD_0V9  ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "4 ", "MultVolt": "2   ", "VoltChannelInfo": "2V5_FPGA_MON ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "5 ", "MultVolt": "1   ", "VoltChannelInfo": "PEX_1V8      ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "6 ", "MultVolt": "2   ", "VoltChannelInfo": "SYS_CLK_3V3  ", "LowLimit": "0    ", "HighLimit": "0  "}, {"VoltChannel": "7 ", "MultVolt": "3.14", "VoltChannelInfo": "VDD_5V5      ", "LowLimit": "0    ", "HighLimit": "0  "}], "SWBVoltInfo": [{"VoltChannel": "0 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT1_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "1 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT2_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "2 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT3_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "3 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT4_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "4 ", "MultVolt": "3   ", "VoltChannelInfo": "SW1_TX_5V    ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "5 ", "MultVolt": "3   ", "VoltChannelInfo": "SW1_VCC_-6V  ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "6 ", "MultVolt": "3   ", "VoltChannelInfo": "SW1_VCC_3V3  ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "7 ", "MultVolt": "3   ", "VoltChannelInfo": "SW1_VCC_5V5  ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "8 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT5_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "9 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT6_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "10", "MultVolt": "0   ", "VoltChannelInfo": "PORT7_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "11", "MultVolt": "0   ", "VoltChannelInfo": "PORT8_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "12", "MultVolt": "3   ", "VoltChannelInfo": "SW2_TX_5V    ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "13", "MultVolt": "3   ", "VoltChannelInfo": "SW2_VCC_-6V  ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "14", "MultVolt": "3   ", "VoltChannelInfo": "SW2_VCC_3V3  ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "15", "MultVolt": "3   ", "VoltChannelInfo": "SW2_VCC_5V5  ", "LowLimit": "0    ", "HighLimit": "0      "}], "BBVoltInfo": [{"VoltChannel": "0", "MultVolt": "2    ", "VoltChannelInfo": "3V3_FPGA_MON   ", "LowLimit": "3.27", "HighLimit": "3.40  "}, {"VoltChannel": "1", "MultVolt": "1    ", "VoltChannelInfo": "VCCINT_0V85    ", "LowLimit": "0.825", "HighLimit": "0.876  "}, {"VoltChannel": "2", "MultVolt": "1    ", "VoltChannelInfo": "1V1_FPGA_MON   ", "LowLimit": "1.14", "HighLimit": "1.26   "}, {"VoltChannel": "3", "MultVolt": "1    ", "VoltChannelInfo": "VDD_0V9        ", "LowLimit": "0.873", "HighLimit": "0.927  "}, {"VoltChannel": "4", "MultVolt": "2    ", "VoltChannelInfo": "2V5_FPGA_MON   ", "LowLimit": "2.375", "HighLimit": "2.75  "}, {"VoltChannel": "5", "MultVolt": "1    ", "VoltChannelInfo": "1V8_FPGA       ", "LowLimit": "1.78", "HighLimit": "1.86  "}, {"VoltChannel": "6", "MultVolt": "2    ", "VoltChannelInfo": "SLOT1_VDD_4V2  ", "LowLimit": "4.15", "HighLimit": "4.30   "}, {"VoltChannel": "7", "MultVolt": "3.125", "VoltChannelInfo": "SLOT1_VDD_5V5  ", "LowLimit": "5.0 ", "HighLimit": "5.80    "}], "RFVoltInfo_VB": {"VSG": [{"VoltChannel": "4", "MultVolt": "3    ", "VoltChannelInfo": "VCC_5V5        ", "LowLimit": "5.35 ", "HighLimit": "5.70    "}, {"VoltChannel": "5", "MultVolt": "11   ", "VoltChannelInfo": "VCC_12V        ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "7", "MultVolt": "3    ", "VoltChannelInfo": "VCC_4V2        ", "LowLimit": "4.15", "HighLimit": "5.8   "}], "VSA": []}, "RFVoltInfo": {"VSG": [{"VoltChannel": "0", "MultVolt": "0    ", "VoltChannelInfo": "               ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "1", "MultVolt": "3    ", "VoltChannelInfo": "VCC_DAC_3V3    ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "2", "MultVolt": "3    ", "VoltChannelInfo": "VCC_DAC_1V8    ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "3", "MultVolt": "3    ", "VoltChannelInfo": "MOD_3V3        ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "4", "MultVolt": "3    ", "VoltChannelInfo": "LO_MOD_5V      ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "5", "MultVolt": "3    ", "VoltChannelInfo": "AMP3T_4T_5V    ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "6", "MultVolt": "3    ", "VoltChannelInfo": "TX_MIX_5V      ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "7", "MultVolt": "3    ", "VoltChannelInfo": "AMP1T_5V       ", "LowLimit": "0 ", "HighLimit": "0    "}], "VSA": [{"VoltChannel": "0", "MultVolt": "0    ", "VoltChannelInfo": "               ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "1", "MultVolt": "3    ", "VoltChannelInfo": "ADC_AVDD1_1V25 ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "2", "MultVolt": "3    ", "VoltChannelInfo": "ADC_DRVDD_1V25 ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "3", "MultVolt": "3    ", "VoltChannelInfo": "ADC_DVDD_1V25  ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "4", "MultVolt": "3    ", "VoltChannelInfo": "ADC_AVDD2_2V5  ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "5", "MultVolt": "3    ", "VoltChannelInfo": "ADC_AVDD3_3V3  ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "6", "MultVolt": "3    ", "VoltChannelInfo": "DEM_5V5        ", "LowLimit": "0 ", "HighLimit": "0    "}, {"VoltChannel": "7", "MultVolt": "3    ", "VoltChannelInfo": "DEM_3V3        ", "LowLimit": "0 ", "HighLimit": "0    "}]}, "BPVoltInfo_WT428_VA": [{"VoltChannel": "0", "MultVolt": "2    ", "VoltChannelInfo": "3V3_FPGA_MON   ", "LowLimit": "3.27", "HighLimit": "3.40  "}, {"VoltChannel": "1", "MultVolt": "1    ", "VoltChannelInfo": "PEX_VDD_0V9A   ", "LowLimit": "0.8", "HighLimit": "0.95  "}, {"VoltChannel": "2", "MultVolt": "1    ", "VoltChannelInfo": "1V1_FPGA_MON   ", "LowLimit": "1.07", "HighLimit": "1.18   "}, {"VoltChannel": "3", "MultVolt": "1    ", "VoltChannelInfo": "VDD_0V9        ", "LowLimit": "0.86", "HighLimit": "0.95  "}, {"VoltChannel": "4", "MultVolt": "2    ", "VoltChannelInfo": "2V5_FPGA_MON   ", "LowLimit": "2.48", "HighLimit": "2.56  "}, {"VoltChannel": "5", "MultVolt": "1    ", "VoltChannelInfo": "1V8_FPGA       ", "LowLimit": "1.78", "HighLimit": "1.86  "}, {"VoltChannel": "6", "MultVolt": "2    ", "VoltChannelInfo": "SYSCLK_3V3     ", "LowLimit": "3.20", "HighLimit": "3.50  "}, {"VoltChannel": "7", "MultVolt": "3.14 ", "VoltChannelInfo": "VDD_5V5        ", "LowLimit": "5.00", "HighLimit": "5.80    "}], "SWBVoltInfo_WT428_VA": [{"VoltChannel": "0 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT4_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "1 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT8_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "2 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT3_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "3 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT7_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "4 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT2_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "5 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT6_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "6 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT1_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "7 ", "MultVolt": "0   ", "VoltChannelInfo": "PORT5_8318_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "8 ", "MultVolt": "2   ", "VoltChannelInfo": "VCC_3V3       ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "9 ", "MultVolt": "2   ", "VoltChannelInfo": "2V5_PEX_SW    ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "10", "MultVolt": "3.14", "VoltChannelInfo": "ETH_5V        ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "11", "MultVolt": "1   ", "VoltChannelInfo": "1V0_PEX8615_MON", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "12", "MultVolt": "0   ", "VoltChannelInfo": "              ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "13", "MultVolt": "0   ", "VoltChannelInfo": "              ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "14", "MultVolt": "0   ", "VoltChannelInfo": "              ", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "15", "MultVolt": "0   ", "VoltChannelInfo": "              ", "LowLimit": "0    ", "HighLimit": "0      "}], "SWBVoltInfo_WT418_VA": [{"VoltChannel": "0 ", "MultVolt": "0   ", "VoltChannelInfo": "Port4_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "1 ", "MultVolt": "0   ", "VoltChannelInfo": "Port8_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "2 ", "MultVolt": "0   ", "VoltChannelInfo": "Port3_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "3 ", "MultVolt": "0   ", "VoltChannelInfo": "Port7_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "4 ", "MultVolt": "0   ", "VoltChannelInfo": "Port2_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "5 ", "MultVolt": "0   ", "VoltChannelInfo": "Port6_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "6 ", "MultVolt": "0   ", "VoltChannelInfo": "Port1_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}, {"VoltChannel": "7 ", "MultVolt": "0   ", "VoltChannelInfo": "Port5_BY_TEMP", "LowLimit": "0    ", "HighLimit": "0      "}], "RF_ATT_Init": [{"TesterType": "WT_448", "ATT_Init": {"VSG": [{"RfATTId": "0", "Code": "63"}, {"RfATTId": "1", "Code": "63"}, {"RfATTId": "2", "Code": "63"}, {"RfATTId": "3", "Code": "63"}, {"RfATTId": "4", "Code": "63"}, {"RfATTId": "5", "Code": "63"}], "VSA": [{"RfATTId": "0", "Code": "63"}, {"RfATTId": "1", "Code": "63"}, {"RfATTId": "2", "Code": "63"}, {"RfATTId": "3", "Code": "63"}, {"RfATTId": "4", "Code": "63"}, {"RfATTId": "5", "Code": "63"}]}}, {"TesterType": "WT_428", "ATT_Init": {"VSG": [{"RfATTId": "0", "Code": "63"}, {"RfATTId": "1", "Code": "63"}, {"RfATTId": "2", "Code": "63"}, {"RfATTId": "3", "Code": "63"}, {"RfATTId": "4", "Code": "63"}, {"RfATTId": "5", "Code": "63"}], "VSA": [{"RfATTId": "0", "Code": "63"}, {"RfATTId": "1", "Code": "63"}, {"RfATTId": "2", "Code": "63"}, {"RfATTId": "3", "Code": "63"}, {"RfATTId": "4", "Code": "63"}, {"RfATTId": "5", "Code": "63"}]}}, {"TesterType": "WT_418", "ATT_Init": {"VSG": [{"RfATTId": "0", "Code": "63"}, {"RfATTId": "1", "Code": "61"}, {"RfATTId": "2", "Code": "59"}, {"RfATTId": "3", "Code": "63"}, {"RfATTId": "4", "Code": "63"}, {"RfATTId": "5", "Code": "63"}], "VSA": [{"RfATTId": "0", "Code": "63"}, {"RfATTId": "1", "Code": "63"}, {"RfATTId": "2", "Code": "63"}, {"RfATTId": "3", "Code": "63"}, {"RfATTId": "4", "Code": "63"}, {"RfATTId": "5", "Code": "63"}]}}]}