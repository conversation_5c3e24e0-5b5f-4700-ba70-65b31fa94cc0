#include "wtswitch_428vb.h"
#include "wtswitchdefine.h"
// VSG接口
static int Vsg_Select_8080(int Port, int PortState);
static int Vsg_Select_Port(int Port, int PortState);
static int Vsg_Select_PI_PA(int Port, int PortState);
static int Vsg_Select_Loop(int Port, int PortState);
static int Vsg_Select_Pac(int Port, int PortState, int NextStep);
static int Vsg_Select_TX_RX(int Port, int PortState);
static int Vsg_Select_PAC_PI_PA(int Port, int PortState);

// VSA接口
static int Vsa_Select_8080(int Port, int PortState);
static int Vsa_Select_Port(int Port, int PortState);
static int Vsa_Select_PI_PA(int Port, int PortState);
static int Vsa_Select_Loop(int Port, int PortState);
static int Vsa_Select_Pac(int Port, int PortState);
static int Vsa_Select_TX_RX(int Port, int PortState);
static int Vsa_Select_PAC_PI_PA(int Port, int PortState);

// Port: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsa_Select_SW_428VB(int Port, int PortState)
{
    return Vsa_Select_8080(Port, PortState);
}

// Port: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsg_Select_SW_428VB(int Port, int PortState)
{
    return Vsg_Select_8080(Port, PortState);
}

// Port: 开关板内的port序号, 0,1,2,3
// PortState: WT_PORT_STATE_E, WT_RF_STATE_INIT,... WT_RF_STATE_8080
int Vsg_Select_CTL3_428VB(int Port, int PortState)
{
    return Vsg_Select_Pac(Port, PortState, false);
}

enum WT428SwitchBit
{//va_vb相同
    // shift3
    RF_RX1_CTL2,
    RF_RX1_CTL4,
    RF_RX1_CTL1,
    RF_RX_CTL5,
    RF_RX2_CTL8,
    RF_RX2_CTL9,
    RF_RX2_CTL6,
    RF_RX2_CTL7,
    Port1_A0,
    Port1_A1,
    Port1_A2,
    Port1_A3,
    Port1_A4,
    Port1_A5,
    P1_S_CTL1,
    RF_RX1_CTL3,
    P1_R_CTL4,
    TX1_P1_CTL1,
    P1_RF_CTL,
    P1_RX_CTL=P1_RF_CTL,//降成本版与广播2版共用
    P1_B_CTL7,
    RX1_CTL_P12,
    RX1_P1_CTL1,
    RX1_P1_CTL2,
    P1_R_CTL6,
    TX1_P1_CTL2,
    TX1_CTL_P12,
    RX1_P2_CTL2,
    P2_R_CTL6,
    P2_R_CTL4,
    P2_S_CTL1,
    P1_S1_CTL2,
    P1_T_CTL3,

    // shift4
    P3_R_CTL6,
    TX1_P3_CTL2,
    TX1_P3_CTL1,
    P3_RF_CTL,
    P3_RX_CTL=P3_RF_CTL,//降成本版与广播2版共用
    P3_B_CTL7,
    RX1_CTL_P34,
    RX1_P3_CTL1,
    RX1_P3_CTL2,
    Port4_A0,
    Port4_A1,
    Port4_A2,
    Port4_A3,
    Port4_A4,
    Port4_A5,
    P3_S1_CTL2,
    P3_T_CTL3,
    P4_S_CTL1,
    P4_RF_CTL,
    P3_TX_CTL=P4_RF_CTL,//降成本版与广播2版共用
    P4_B_CTL7,
    TX1_CTL_P34,
    RX1_P4_CTL1,
    RX1_P4_CTL2,
    P4_R_CTL6,
    P4_R_CTL4,
    TX1_P4_CTL1,
    P4_S1_CTL2,
    P4_T_CTL3,
    TX1_P4_CTL2,
    //Reserved，
    P4_RX_CTL,
    P2_RX_CTL,
    P4_TX_CTL,

    // shift5
    Port3_A0,
    Port3_A1,
    Port3_A2,
    Port3_A3,
    Port3_A4,
    Port3_A5,
    RF_TX1_CTL,
    P2_TX_CTL=RF_TX1_CTL,//降成本版与广播2版共用
    TX1_CTL_S,
    P2_B_CTL7,
    P3_R_CTL4,
    P3_S_CTL1,
    P2_S1_CTL2,
    P2_T_CTL3,
    TX1_P2_CTL2,
    TX1_P2_CTL1,
    P2_RF_CTL,
    P1_TX_CTL=P2_RF_CTL,//降成本版与广播2版共用
    Port2_A0,
    Port2_A1,
    Port2_A2,
    Port2_A3,
    Port2_A4,
    Port2_A5,
    RX1_S_CTL,
    RX1_P2_CTL1,

    // shift0
    RF_TX2_CTL8,
    RF_TX2_CTL7,
    RF_TX1_CTL2,
    RF_TX1_CTL1,
    RF_TX1_CTL4,
    RF_TX1_CTL3,
    RF_TX_CTL5,
    RF_TX2_CTL6,
    P8_B_CTL7,
    RF_TX2_CTL9,
    P8_TX_CTL,
    P8_S1_CTL2,
    P8_T_CTL3,
    TX2_P8_CTL2,
    P8_RF_CTL,
    P8_RX_CTL=P8_RF_CTL,//降成本版与广播2版共用
    TX2_P8_CTL1,
    P7_S1_CTL2,
    P7_TX_CTL,
    RX2_P8_CTL1,
    RX2_P8_CTL2,
    P8_R_CTL6,
    P8_R_CTL4,
    P8_S_CTL1,
    P7_T_CTL3,
    Port8_A0,
    Port8_A1,
    Port8_A2,
    Port8_A3,
    Port8_A4,
    Port8_A5,
    TX2_CTL_P78,
    RF_TX2_CTL,
    P7_RX_CTL=RF_TX2_CTL,//降成本版与广播2版共用

    // shift1
    P7_R_CTL6,
    P7_RF_CTL,
    TX2_P7_CTL2,
    TX2_P7_CTL1,
    P7_B_CTL7,
    RX2_CTL_P78,
    RX2_P7_CTL1,
    RX2_P7_CTL2,
    Port7_A0,
    Port7_A1,
    Port7_A2,
    Port7_A3,
    Port7_A4,
    Port7_A5,
    P7_R_CTL4,
    P7_S_CTL1,
    RX2_P6_CTL1,
    P6_S1_CTL2,
    P6_T_CTL3,
    TX2_P6_CTL2,
    TX2_P6_CTL1,
    P6_RF_CTL,
    P6_TX_CTL=P6_RF_CTL,//降成本版与广播2版共用
    P6_B_CTL7,
    RX2_S_CTL,
    P5_T_CTL3,
    RX2_P6_CTL2,
    P6_R_CTL6,
    P6_R_CTL4,
    P6_S_CTL1,
    TX2_CTL_S,
    TX2_CTL_P56,
    P5_S1_CTL2,

    // shift2
    Port6_A0,
    Port6_A1,
    Port6_A2,
    Port6_A3,
    Port6_A4,
    Port6_A5,
    P5_TX_CTL,
    P6_RX_CTL,
    P5_R_CTL6,
    TX2_P5_CTL2,
    TX2_P5_CTL1,
    P5_RF_CTL,
    P5_RX_CTL=P5_RF_CTL,//降成本版与广播2版共用
    P5_B_CTL7,
    RX2_CTL_P56,
    RX2_P5_CTL1,
    RX2_P5_CTL2,
    Port5_A0,
    Port5_A1,
    Port5_A2,
    Port5_A3,
    Port5_A4,
    Port5_A5,
    P5_R_CTL4,
    P5_S_CTL1,

    // 42553 SW1
    P1_T_CTL5,
    P2_T_CTL5,
    P3_T_CTL5,
    P4_T_CTL5,

    // 42553 SW2
    P5_T_CTL5,
    P6_T_CTL5,
    P7_T_CTL5,
    P8_T_CTL5,

    WT428Switch_BitDefineMax,
};

#if DEBUG_SHOW_SW_BIT_NAME
// 注意要与WT428SwitchBit名称一一对应
static char WT448SwitchBitNames[][20] =
{
    // shift3
    "RF_RX1_CTL2",
    "RF_RX1_CTL4",
    "RF_RX1_CTL1",
    "RF_RX_CTL5",
    "RF_RX2_CTL8",
    "RF_RX2_CTL9",
    "RF_RX2_CTL6",
    "RF_RX2_CTL7",
    "Port1_A0",
    "Port1_A1",
    "Port1_A2",
    "Port1_A3",
    "Port1_A4",
    "Port1_A5",
    "P1_S_CTL1",
    "RF_RX1_CTL3",
    "P1_R_CTL4",
    "TX1_P1_CTL1",
    "P1_RX_CTL",
    "P1_B_CTL7",
    "RX1_CTL_P12",
    "RX1_P1_CTL1",
    "RX1_P1_CTL2",
    "P1_R_CTL6",
    "TX1_P1_CTL2",
    "TX1_CTL_P12",
    "RX1_P2_CTL2",
    "P2_R_CTL6",
    "P2_R_CTL4",
    "P2_S_CTL1",
    "P1_S1_CTL2",
    "P1_T_CTL3",

    // shift4
    "P3_R_CTL6",
    "TX1_P3_CTL2",
    "TX1_P3_CTL1",
    "P3_RX_CTL",
    "P3_B_CTL7",
    "RX1_CTL_P34",
    "RX1_P3_CTL1",
    "RX1_P3_CTL2",
    "Port4_A0",
    "Port4_A1",
    "Port4_A2",
    "Port4_A3",
    "Port4_A4",
    "Port4_A5",
    "P3_S1_CTL2",
    "P3_T_CTL3",
    "P4_S_CTL1",
    "P3_TX_CTL",
    "P4_B_CTL7",
    "TX1_CTL_P34",
    "RX1_P4_CTL1",
    "RX1_P4_CTL2",
    "P4_R_CTL6",
    "P4_R_CTL4",
    "TX1_P4_CTL1",
    "P4_S1_CTL2",
    "P4_T_CTL3",
    "TX1_P4_CTL2",
    //"Reserved",
    "P4_RX_CTL",
    "P2_RX_CTL",
    "P4_TX_CTL",

    // shift5
    "Port3_A0",
    "Port3_A1",
    "Port3_A2",
    "Port3_A3",
    "Port3_A4",
    "Port3_A5",
    "P2_TX_CTL",
    "TX1_CTL_S",
    "P2_B_CTL7",
    "P3_R_CTL4",
    "P3_S_CTL1",
    "P2_S1_CTL2",
    "P2_T_CTL3",
    "TX1_P2_CTL2",
    "TX1_P2_CTL1",
    "P1_TX_CTL",
    "Port2_A0",
    "Port2_A1",
    "Port2_A2",
    "Port2_A3",
    "Port2_A4",
    "Port2_A5",
    "RX1_S_CTL",
    "RX1_P2_CTL1",

    // shift0
    "RF_TX2_CTL8",
    "RF_TX2_CTL7",
    "RF_TX1_CTL2",
    "RF_TX1_CTL1",
    "RF_TX1_CTL4",
    "RF_TX1_CTL3",
    "RF_TX_CTL5",
    "RF_TX2_CTL6",
    "P8_B_CTL7",
    "RF_TX2_CTL9",
    " P8_TX_CTL",
    "P8_S1_CTL2",
    "P8_T_CTL3",
    "TX2_P8_CTL2",
    "P8_RX_CTL",
    "TX2_P8_CTL1",
    "P7_S1_CTL2",
    "P7_TX_CTL",
    "RX2_P8_CTL1",
    "RX2_P8_CTL2",
    "P8_R_CTL6",
    "P8_R_CTL4",
    "P8_S_CTL1",
    "P7_T_CTL3",
    "Port8_A0",
    "Port8_A1",
    "Port8_A2",
    "Port8_A3",
    "Port8_A4",
    "Port8_A5",
    "TX2_CTL_P78",
    "P7_RX_CTL",

    // shift1
    "P7_R_CTL6",
    //"Reserved",
    "TX2_P7_CTL2",
    "TX2_P7_CTL1",
    "P7_B_CTL7",
    "RX2_CTL_P78",
    "RX2_P7_CTL1",
    "RX2_P7_CTL2",
    "Port7_A0",
    "Port7_A1",
    "Port7_A2",
    "Port7_A3",
    "Port7_A4",
    "Port7_A5",
    "P7_R_CTL4",
    "P7_S_CTL1",
    "RX2_P6_CTL1",
    "P6_S1_CTL2",
    "P6_T_CTL3",
    "TX2_P6_CTL2",
    "TX2_P6_CTL1",
    "P6_TX_CTL",
    "P6_B_CTL7",
    "RX2_S_CTL",
    "P5_T_CTL3",
    "RX2_P6_CTL2",
    "P6_R_CTL6",
    "P6_R_CTL4",
    "P6_S_CTL1",
    "TX2_CTL_S",
    "TX2_CTL_P56",
    "P5_S1_CTL2",

    // shift2
    "Port6_A0",
    "Port6_A1",
    "Port6_A2",
    "Port6_A3",
    "Port6_A4",
    "Port6_A5",
    "P5_TX_CTL",
    "P6_RX_CTL",
    "P5_R_CTL6",
    "TX2_P5_CTL2",
    "TX2_P5_CTL1",
    "P5_RX_CTL",
    "P5_B_CTL7",
    "RX2_CTL_P56",
    "RX2_P5_CTL1",
    "RX2_P5_CTL2",
    "Port5_A0",
    "Port5_A1",
    "Port5_A2",
    "Port5_A3",
    "Port5_A4",
    "Port5_A5",
    "P5_R_CTL4",
    "P5_S_CTL1",

    // 42553 SW1
    "P1_T_CTL5",
    "P2_T_CTL5",
    "P3_T_CTL5",
    "P4_T_CTL5",

    // 42553 SW2
    "P5_T_CTL5",
    "P6_T_CTL5",
    "P7_T_CTL5",
    "P8_T_CTL5",
};
#endif

#define SELECT_SWITCH_BIT(a, b) \
    do                          \
    {                           \
        switch (a)              \
        {                       \
        case 0:                 \
            _DR_SW_CLEARBIT(b); \
            break;              \
        case 1:                 \
            _DR_SW_SETBIT(b);   \
            break;              \
        default:                \
            break;              \
        }                       \
    } while (0)

int Vsa_Select_8080(int Port, int PortState)
{
    const int BitMask[9] = {
        RF_RX_CTL5,
        RF_RX1_CTL1,
        RF_RX2_CTL6,
        RF_RX1_CTL2,
        RF_RX2_CTL7,
        RF_RX1_CTL3,
        RF_RX2_CTL8,
        RF_RX1_CTL4,
        RF_RX2_CTL9};

    const int BitSelectInit[9] = {0, 1, 0, 0, 1, 1, 0, 0, 1};
    const int BitSelectSISO[9] = {0, 0, 1, 0, 1, 1, 0, 1, 0};
    const int BitSelect8080[2][9] = {
        {1, 1, 0, 1, 0, 0, 0, 0, 0},
        {0, 1, 0, 1, 0, 1, 1, 1, 1},
    };
    int i = 0;
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int Lenght = sizeof(BitMask) / sizeof(BitMask[0]);

    for (i = 0; i < Lenght; ++i)
    {
        if (PortState == WT_RF_STATE_INIT)
        {
            SELECT_SWITCH_BIT(BitSelectInit[i], BitMask[i]);
        }
        else if (PortState == WT_RF_STATE_SISO || PortState == WT_RF_STATE_OFF)
        {
            SELECT_SWITCH_BIT(BitSelectSISO[i], BitMask[i]);
        }
        else if (PortState == WT_RF_STATE_8080)
        {
            SELECT_SWITCH_BIT(BitSelect8080[MaskIndex][i], BitMask[i]);
        }
    }

    if (PortState >= WT_RF_STATE_SISO)
    {
        return 0;
    }
    return Vsa_Select_Port(Port, PortState);
}

int Vsa_Select_Port(int Port, int PortState)
{
    const int BitMask[2][3] = {
        {RX1_S_CTL, RX1_CTL_P12, RX1_CTL_P34},
        {RX2_S_CTL, RX2_CTL_P56, RX2_CTL_P78},
    };
    const int BitSelectInit[2][3] = {
        {0, 0, 1},
        {1, 0, 1},
    };
    const int BitSelect[4][3] = {
        {0, 0, 2},
        {0, 1, 2},
        {1, 2, 0},
        {1, 2, 1},
    };
    int i = 0;
    int PortIndex = (Port - SWITCH_PORT_1) % 4;
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int Lenght = sizeof(BitMask[MaskIndex]) / sizeof(BitMask[MaskIndex][0]);

    for (i = 0; i < Lenght; ++i)
    {
        if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
        {
            SELECT_SWITCH_BIT(BitSelectInit[MaskIndex][i], BitMask[MaskIndex][i]);
        }
        else if (PortState < WT_RF_STATE_MAX)
        {
            SELECT_SWITCH_BIT(BitSelect[PortIndex][i], BitMask[MaskIndex][i]);
        }
    }

    switch (PortState)
    {
    case WT_RF_STATE_RF_PI:
        PortState = WT_RF_STATE_PI;
        break;
    case WT_RF_STATE_RF_PA_1:
        PortState = WT_RF_STATE_PA_1;
        break;
    case WT_RF_STATE_RF_PA_2:
        PortState = WT_RF_STATE_PA_2;
        break;
    case WT_RF_STATE_RF_DET_PI:
        PortState = WT_RF_STATE_RF_DET_PI;
        break;
    case WT_RF_STATE_RF_DET_PA_1:
        PortState = WT_RF_STATE_RF_DET_PA_1;
        break;
    case WT_RF_STATE_RF_DET_PA_2:
        PortState = WT_RF_STATE_RF_DET_PA_2;
        break;
    default:
        break;
    }

    return Vsa_Select_PI_PA(Port, PortState);
}

int Vsa_Select_PI_PA(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {RX1_P1_CTL1, RX1_P2_CTL1, RX1_P3_CTL1, RX1_P4_CTL1},
        {RX2_P5_CTL1, RX2_P6_CTL1, RX2_P7_CTL1, RX2_P8_CTL1},
    };
    const int BitMask2[2][4] = {
        {RX1_P1_CTL2, RX1_P2_CTL2, RX1_P3_CTL2, RX1_P4_CTL2},
        {RX2_P5_CTL2, RX2_P6_CTL2, RX2_P7_CTL2, RX2_P8_CTL2},
    };
    const int BitMask3[2][4] = {
        { P1_RX_CTL, P2_RX_CTL, P3_RX_CTL, P4_RX_CTL},
        { P5_RX_CTL, P6_RX_CTL, P7_RX_CTL, P8_RX_CTL },
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_CLEARBIT(BitMask2[MaskIndex][PortIndex]);
		_DR_SW_SETBIT(BitMask3 [MaskIndex][PortIndex]);
        break;
    case WT_RF_STATE_PA_2:
    case WT_RF_STATE_DET_PA_2:
    case WT_RF_STATE_LOOP_PA_2:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_SETBIT(BitMask2[MaskIndex][PortIndex]);
		_DR_SW_CLEARBIT(BitMask3 [MaskIndex][PortIndex]);    
        break;
    case WT_RF_STATE_PI:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_PAC_PA_1:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_CLEARBIT(BitMask2[MaskIndex][PortIndex]);
		_DR_SW_CLEARBIT(BitMask3 [MaskIndex][PortIndex]);
        break;
    }

    return Vsa_Select_Loop(Port, PortState);
}

int Vsa_Select_Loop(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {P1_R_CTL6, P2_R_CTL6, P3_R_CTL6, P4_R_CTL6},
        {P5_R_CTL6, P6_R_CTL6, P7_R_CTL6, P8_R_CTL6},
    };
    const int BitMask2[2][4] = {
        {P1_B_CTL7, P2_B_CTL7, P3_B_CTL7, P4_B_CTL7},
        {P5_B_CTL7, P6_B_CTL7, P7_B_CTL7, P8_B_CTL7},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        break;
    default:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_CLEARBIT(BitMask2[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask2[MaskIndex][PortIndex]);
        break;
    default:
        break;
    }
    return Vsa_Select_Pac(Port, PortState);
}

int Vsa_Select_Pac(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {P1_R_CTL4, P2_R_CTL4, P3_R_CTL4, P4_R_CTL4},
        {P5_R_CTL4, P6_R_CTL4, P7_R_CTL4, P8_R_CTL4},
    };
    const int BitMaskTx[2][4] = {
        {P1_T_CTL3, P2_T_CTL3, P3_T_CTL3, P4_T_CTL3},
        {P5_T_CTL3, P6_T_CTL3, P7_T_CTL3, P8_T_CTL3},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PI:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_SETBIT(BitMaskTx[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;
    default:
        break;
    }

    return Vsa_Select_TX_RX(Port, PortState);
}

int Vsa_Select_TX_RX(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {P1_S1_CTL2, P2_S1_CTL2, P3_S1_CTL2, P4_S1_CTL2},
        {P5_S1_CTL2, P6_S1_CTL2, P7_S1_CTL2, P8_S1_CTL2},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;
    default:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        break;
    }
    return Vsa_Select_PAC_PI_PA(Port, PortState);
}

int Vsa_Select_PAC_PI_PA(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {P1_S_CTL1, P2_S_CTL1, P3_S_CTL1, P4_S_CTL1},
        {P5_S_CTL1, P6_S_CTL1, P7_S_CTL1, P8_S_CTL1},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
    case WT_RF_STATE_PI:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        break;
    default:
        break;
    }
    return 0;
}

int Vsg_Select_8080(int Port, int PortState)
{
    const int BitMask[9] = {
        RF_TX_CTL5,
        RF_TX1_CTL1,
        RF_TX2_CTL6,
        RF_TX1_CTL2,
        RF_TX2_CTL7,
        RF_TX1_CTL3,
        RF_TX2_CTL8,
        RF_TX1_CTL4,
        RF_TX2_CTL9,
    };

    const int BitSelectInit[9] = {1, 1, 0, 0, 1, 1, 0, 0, 1};
    const int BitSelectSISO[9] = {1, 0, 1, 0, 1, 1, 0, 1, 0};
    const int BitSelect8080[2][9] = {
        {1, 1, 0, 1, 0, 0, 0, 0, 0},
        {0, 1, 0, 1, 0, 1, 1, 1, 1},
    };
    int i = 0;
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int Lenght = sizeof(BitMask) / sizeof(BitMask[0]);

    for (i = 0; i < Lenght; ++i)
    {
        if (PortState == WT_RF_STATE_INIT)
        {
            SELECT_SWITCH_BIT(BitSelectInit[i], BitMask[i]);
        }
        else if (PortState == WT_RF_STATE_SISO || PortState == WT_RF_STATE_OFF)
        {
            SELECT_SWITCH_BIT(BitSelectSISO[i], BitMask[i]);
        }
        else if (PortState == WT_RF_STATE_8080)
        {
            SELECT_SWITCH_BIT(BitSelect8080[MaskIndex][i], BitMask[i]);
        }
    }

    if (PortState >= WT_RF_STATE_SISO)
    {
        return 0;
    }

    return Vsg_Select_Port(Port, PortState);
}

int Vsg_Select_Port(int Port, int PortState)
{
    const int BitMask[2][3] = {
        {TX1_CTL_S, TX1_CTL_P12, TX1_CTL_P34},
        {TX2_CTL_S, TX2_CTL_P56, TX2_CTL_P78},
    };
    const int BitSelectInit[2][3] = {
        {1, 1, 0},
        {0, 1, 0},
    };
    const int BitSelect[4][3] = {
        {1, 1, 2},
        {1, 0, 2},
        {0, 2, 1},
        {0, 2, 0},
    };
    int i = 0;
    int PortIndex = (Port - SWITCH_PORT_1) % 4;
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int Lenght = sizeof(BitMask[MaskIndex]) / sizeof(BitMask[MaskIndex][0]);

    for (i = 0; i < Lenght; ++i)
    {
        if (PortState == WT_RF_STATE_INIT || PortState == WT_RF_STATE_OFF)
        {
            SELECT_SWITCH_BIT(BitSelectInit[MaskIndex][i], BitMask[MaskIndex][i]);
        }
        else if (PortState < WT_RF_STATE_MAX)
        {
            SELECT_SWITCH_BIT(BitSelect[PortIndex][i], BitMask[MaskIndex][i]);
        }
    }

    switch (PortState)
    {
    case WT_RF_STATE_RF_PI:
        PortState = WT_RF_STATE_PI;
        break;
    case WT_RF_STATE_RF_PA_1:
        PortState = WT_RF_STATE_PA_1;
        break;
    case WT_RF_STATE_RF_PA_2:
        PortState = WT_RF_STATE_PA_2;
        break;
    case WT_RF_STATE_RF_DET_PI:
        PortState = WT_RF_STATE_RF_DET_PI;
        break;
    case WT_RF_STATE_RF_DET_PA_1:
        PortState = WT_RF_STATE_RF_DET_PA_1;
        break;
    case WT_RF_STATE_RF_DET_PA_2:
        PortState = WT_RF_STATE_RF_DET_PA_2;
        break;
    default:
        break;
    }
    return Vsg_Select_PI_PA(Port, PortState);
}

int Vsg_Select_PI_PA(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {TX1_P1_CTL1, TX1_P2_CTL1, TX1_P3_CTL1, TX1_P4_CTL1},
        {TX2_P5_CTL1, TX2_P6_CTL1, TX2_P7_CTL1, TX2_P8_CTL1},
    };
    const int BitMask2[2][4] = {
        {TX1_P1_CTL2, TX1_P2_CTL2, TX1_P3_CTL2, TX1_P4_CTL2},
        {TX2_P5_CTL2, TX2_P6_CTL2, TX2_P7_CTL2, TX2_P8_CTL2},
    };
    const int BitMask3[2][4] = {
        { P1_TX_CTL, P2_TX_CTL, P3_TX_CTL, P4_TX_CTL},
        { P5_TX_CTL, P6_TX_CTL, P7_TX_CTL, P8_TX_CTL},
    };

    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_CLEARBIT(BitMask2[MaskIndex][PortIndex]);
        _DR_SW_CLEARBIT(BitMask3[MaskIndex][PortIndex]);
        break;
    case WT_RF_STATE_PI:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PA_2:
    case WT_RF_STATE_DET_PA_2:
    case WT_RF_STATE_LOOP_PA_2:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_CLEARBIT(BitMask2[MaskIndex][PortIndex]);
        _DR_SW_SETBIT(BitMask3[MaskIndex][PortIndex]);
        break;
    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_PAC_PA_1:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_SETBIT(BitMask2[MaskIndex][PortIndex]);
        _DR_SW_SETBIT(BitMask3[MaskIndex][PortIndex]);
        break;
    default:
        break;
    }
    return Vsg_Select_Loop(Port, PortState);
}

int Vsg_Select_Loop(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {P1_T_CTL5, P2_T_CTL5, P3_T_CTL5, P4_T_CTL5},
        {P5_T_CTL5, P6_T_CTL5, P7_T_CTL5, P8_T_CTL5},
    };
    const int BitMask2[2][4] = {
        {P1_B_CTL7, P2_B_CTL7, P3_B_CTL7, P4_B_CTL7},
        {P5_B_CTL7, P6_B_CTL7, P7_B_CTL7, P8_B_CTL7},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;
    default:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        break;
    }

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
    case WT_RF_STATE_DET_PI:
    case WT_RF_STATE_DET_PA_1:
    case WT_RF_STATE_DET_PA_2:
        _DR_SW_CLEARBIT(BitMask2[MaskIndex][PortIndex]);
        break;
    case WT_RF_STATE_LOOP_PI:
    case WT_RF_STATE_LOOP_PA_1:
    case WT_RF_STATE_LOOP_PA_2:
        _DR_SW_SETBIT(BitMask2[MaskIndex][PortIndex]);
        break;
    default:
        break;
    }
    return Vsg_Select_Pac(Port, PortState, true);
}

int Vsg_Select_Pac(int Port, int PortState, int NextStep)
{
    const int BitMask[2][4] = {
        {P1_T_CTL3, P2_T_CTL3, P3_T_CTL3, P4_T_CTL3},
        {P5_T_CTL3, P6_T_CTL3, P7_T_CTL3, P8_T_CTL3},
    };
    const int BitMaskRx[2][4] = {
        {P1_R_CTL4, P2_R_CTL4, P3_R_CTL4, P4_R_CTL4},
        {P5_R_CTL4, P6_R_CTL4, P7_R_CTL4, P8_R_CTL4},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_OFF:
    case WT_RF_STATE_PI:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_CLEARBIT(BitMaskRx[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PAC_PI:
    case WT_RF_STATE_PAC_PA_1:
    case WT_RF_STATE_PAC_PA_2:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;

    case WT_RF_STATE_PA_1:
    case WT_RF_STATE_PA_2:
        _DR_SW_SETBIT(BitMask[MaskIndex][PortIndex]);
        _DR_SW_SETBIT(BitMaskRx[MaskIndex][PortIndex]);
        break;
    default:
        break;
    }

    if(NextStep)
    {
        return Vsg_Select_TX_RX(Port, PortState);
    }
    else
    {
        return 0;
    }
}

int Vsg_Select_TX_RX(int Port, int PortState)
{
    const int BitMask[2][4] = {
        {P1_S1_CTL2, P2_S1_CTL2, P3_S1_CTL2, P4_S1_CTL2},
        {P5_S1_CTL2, P6_S1_CTL2, P7_S1_CTL2, P8_S1_CTL2},
    };
    int MaskIndex = ((Port <= SWITCH_PORT_4) ? 0 : 1);
    int PortIndex = (Port - SWITCH_PORT_1) % 4;

    switch (PortState)
    {
    case WT_RF_STATE_INIT:
    case WT_RF_STATE_OFF:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;
    default:
        _DR_SW_CLEARBIT(BitMask[MaskIndex][PortIndex]);
        break;
    }
    return Vsg_Select_PAC_PI_PA(Port, PortState);
}

int Vsg_Select_PAC_PI_PA(int Port, int PortState)
{
    return Vsa_Select_PAC_PI_PA(Port, PortState);
}
