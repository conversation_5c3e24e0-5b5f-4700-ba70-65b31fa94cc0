//*****************************************************************************
//  File: link.h
//  WT-Server连接管理
//  Data: 2016.7.27
//*****************************************************************************
#ifndef __WT_SRV_LINK_H__
#define __WT_SRV_LINK_H__

#include <list>
#include <memory>
#include <mutex>
#include "wtev++.h"
#include "socket.h"
#include "threadpool.h"

class Service;
class Monitor;
class LinkMgr;

// 连接信息类
class LinkInfo
{
public:
    //*****************************************************************************
    // 构造函数
    // 参数[IN] : fd : 连接的socket描述符
    //           type : 连接类型，普通连接/独占连接/监视连接...
    //           SID  : 连接序列号
    // 返回值: 无
    //*****************************************************************************
    LinkInfo(int Fd, int Type, int SID)
        : m_Fd(Fd), m_Type(Type), m_SID(SID)
    {
    }

    //*****************************************************************************
    // 拷贝构造函数
    // 参数[IN] : Link : 连接信息
    // 返回值: 无
    //*****************************************************************************
    LinkInfo(const LinkInfo &Link) = default;

    //*****************************************************************************
    // 获取连接的socket fd
    // 参数: 无
    // 返回值: socket fd
    //*****************************************************************************
    int GetFd(void) const
    {
        return m_Fd;
    }

    //*****************************************************************************
    // 获取连接的类型
    // 参数: 无
    // 返回值: 连接类型
    //*****************************************************************************
    int GetType(void) const
    {
        return m_Type;
    }

    //*****************************************************************************
    // 获取连接的序列号
    // 参数: 无
    // 返回值: 序列号
    //*****************************************************************************
    int GetSID(void) const
    {
        return m_SID;
    }

private:
    int m_Fd;             // 连接描述符
    int m_Type;           // 连接类型，见WT_LINK_TYPE
    int m_SID;            // 连接的序列号
};

// 连接管理类，接收WT-Link发送过来的连接并给记录维护相应的连接信息
class LinkMgr
{
public:
    //*****************************************************************************
    // 连接管理类构造函数
    // 参数[IN] : SocketFd : 与WT-Link通信所用的socket
    //            loop : ev loop
    // 返回值: 无
    //*****************************************************************************
    LinkMgr(int SocketFd, wtev::loop_ref &loop);

    //*****************************************************************************
    // 激活link manager, 接受WT-Link消息
    // 参数: 无
    // 返回值: 成功或错误码
    //*****************************************************************************
    int Activate(void);

    //程序崩溃时，用于保存当次给算法的采集数据和参数配置csv
    void SaveStackData(void);

    std::unique_ptr<ThreadPool>& GetThreadPool(void)
    {
        return m_TaskPool;
    }

    // 是否存在独占链接
    bool HasExcludeLink()
    {
        return m_Exclude;
    }

    // 是否存在链接
    bool HasLink(void);

private:
    //*****************************************************************************
    // 与 WT-Link unix socket 连接的事件回掉函数
    // 参数[IN] : Watcher : libev 观察者
    //            revents : 事件类型
    // 返回值: 无
    //*****************************************************************************
    void LinkMsgCb(wtev::io &Watcher, int revents);

    //*****************************************************************************
    // 收到新的连接，进行相应的处理
    // 参数[IN] : fd : 连接的socket描述符
    //           type : 连接类型，普通连接/独占连接/监视连接...
    //           SID  : 连接序列号
    // 返回值: 无
    //*****************************************************************************
    void AcceptLink(int Fd, int Type, int SID);

    //*****************************************************************************
    // 业务连接线程的处理函数
    // 参数[IN] : LinkSrv : service对象
    // 返回值: 无
    //*****************************************************************************
    void LinkService(std::shared_ptr<Service> LinkSrv, void *Arg);

    //*****************************************************************************
    // 创建link service并加入到任务队列
    // 参数[IN] : fd : 连接的socket描述符
    //           type : 连接类型，普通连接/独占连接/监视连接...
    //           SID  : 连接序列号
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StartLinSrv(int Fd, int Type, int SID);

    //*****************************************************************************
    // 停止指定连接的服务并关闭连接
    // 参数[IN] : LinkSrv : service对象
    // 返回值: 无
    //*****************************************************************************
    void StopLinkSrv(std::shared_ptr<Service> &LinkSrv);

    //*****************************************************************************
    // 关闭连接，同时通知WT-Link
    // 参数[IN] : fd : 连接的socket描述符
    //           type : 连接类型，普通连接/独占连接/监视连接...
    //           SID  : 连接序列号
    // 返回值: 无
    //*****************************************************************************
    void CloseLink(int Fd, int Type, int SID);

    //*****************************************************************************
    // 停掉当前所有运行的Service业务, 除了DIAGNOSIS_LINK类型
    // 参数: 无
    // 返回值: 无
    //*****************************************************************************
    void StopAllService(void);

    //*****************************************************************************
    // 返回连接成功的响应给客户端
    // 参数[IN] : Fd, 连接的socket描述符
    // 返回值: 无
    //*****************************************************************************
    void RespSuccToClient(int Fd);

    // 自动运行 改名未自动烤机，有些接口未修改
    // 自动运行计时器回调
    void SrvTimerCb(wtev::timer &watcher, int revents);
    void SrvTimerThread();
    void SncCalThread();

    // 设置自动运行计时器
    int SetAutoRunTimer(void);

    // 自动运行任务的入口点函数
    void StartAutoTest(void *Arg);

    // 查询Manager开机持续时间
    bool QueryManagerBootSeconds();

    //所有用户连接都断开时，复位仪器一些状态。
    int RetSetHWConfig();

private:
    int m_SocketFd;             // 与WT-Link通信所用的Unix socket fd
    WRSocket m_WRSocket;        // 与WT-Link通信所用的Unix socket fd 的socket读写类
    std::mutex m_SocketMutex;   // 写WT-Link之间通信的socket用的锁

    wtev::loop_ref m_EvLoop;    // ev loop
    wtev::io m_IOWatcher;       // ev iowatcher 观察 与 WT-Link 通信FD IO事件
    std::unique_ptr<ThreadPool> m_TaskPool;   // 线程池

    bool m_Exclude = false;     // 是否存在独占连接
    int m_UserNum;              // 能接收的总的用户数
    int m_UserCnt = 0;          // 当前的用户数量，一个用户可对应多个连接。
                                // 比如Meter连接时有几个子连接，这几个子连接都属于同一个用户

    std::list<std::shared_ptr<Service>> m_SrvLink;  // 连接服务对象列表
    std::mutex m_LinkMutex;         // 管理连接对象列表使用的锁

    wtev::timer m_EvTimer;          // autorun定时器
    int m_RunCnt = 0;               // autorun定时器 运行次数

};

#endif
