#ifndef ALG_3GPP_ERRDEF_H_
#define ALG_3GPP_ERRDEF_H_

typedef struct {
    int ErrCode;
    const char *ErrMsg;
} Alg_3GPP_ErrType;

const Alg_3GPP_ErrType Alg_3GPP_ErrInfo[] = {
    /* #1 0x1-0xFF, Common Error */
    /* 0x1-0x3F, System Error */
    { 0x2, "Algorithm library memory critical error!" },
    { 0x4, "Access a null pointer" },
    { 0x5, "Undefined parameter check error!" },
    { 0x6, "Algorithm parameters aren't initialized." },
    { 0x7, "Algorithm access protect." },
    
    /* 0x40-0x7F, Base Library Error */
    /* 0x80-0xBF, Algorithm Error */
    { 0x80, "The VSA can't analyze this waveform file!"},
    { 0x81, "The NBIOT wave length more than 20ms, and VSA can't analyze it!" },
    { 0x82, "WCDMA PCPICH State, PSCH State and SSCH State must all be ON!" },

    /* #2 0x100-0x1FF, Common Input Parameter Error */
    { 0x100, "Standard type don't support!" },
    { 0x101, "Sample rate don't support!" },
    { 0x102, "Link direct parameter out of range!" },
    { 0x103, "Resource grid allocate collision!" },
    { 0x104, "Statistic average measurement type error!"},

    /* #3 0x200-3FF, LTE parameter error */
    /* #3.1 0x200-280, LTE common error */
    { 0x200, "Carrier aggregation state error!" },
    { 0x201, "Cyclic prefix out of range or not support!" },
    { 0x202, "Cell index error!" },
    { 0x203, "Cell state error!" },
    { 0x204, "Physical cell ID out of range!" },
    { 0x205, "Channel bandwidth out of range!" },
    { 0x206, "Duplexing out of range!" },
    { 0x207, "Uplink-downlink configurations out of range in TDD mode!" },
    { 0x208, "Special subframe configuration out of range in TDD mode!" },
    { 0x209, "Cell power parameter out of range!" },
    { 0x20A, "UE ID out of range!" },
    { 0x20B, "Subframe index error!" },
    { 0x20C, "Subframe state error!" },
    { 0x20D, "Generate wave mode error!" },
    { 0x20E, "Generate wave position number error!" },
    { 0x20F, "Filter type don't support!" },
    { 0x210, "FIR filter maximum order out of range!" },
    { 0x211, "FIR filter passband frequency factor error!" },
    { 0x212, "FIR filter stopband frequency factor error!" },
    { 0x213, "FIR filter passband ripple factor error!" },
    { 0x214, "FIR filter stopband attenuation factor error!" },
    { 0x215, "LTE filter optimization type don't support!" },
    { 0x216, "LTE best ACP optimization filter cutoff frequency factor error!" },
    { 0x217, "LTE best EVM optimization filter rolloff factor error!" },
    { 0x218, "LTE best EVM optimization filter cutoff frequency shift error!" },
    { 0x219, "WOLA Window Length Factor error!" },
    { 0x21A, "FIR filter passband frequency factor should be less than FIR filter stopband frequency factor !" },
    { 0x21B, "LTE generate sequence length out of range!" },

    /* 0x240-27F, LTE Measure error */
    { 0x240, "Statistical length for the measurement of modulation results in slots error!"},
    { 0x241, "Enable/disable switch error for the measurement of modulation results!"},
    { 0x242, "Enable/disable switch error for the measurement of EVM!"},
    { 0x243, "Enable/disable switch error for the measurement of magnitude error!"},
    { 0x244, "Enable/disable switch error for the measurement of phase error!"},
    { 0x245, "Enable/disable switch error for the measurement of EVM vs subcarrier!"},
    { 0x246, "Enable/disable switch error for the measurement of inband emissions!"},
    { 0x247, "Enable/disable switch error for the measurement of equalizer spectrum flatness!"},
    { 0x248, "Enable/disable switch error for the measurement of IQ constellation!"},
    { 0x249, "Enable/disable switch error for the measurement of TX measurement result!"},
    { 0x24A, "Exclude abnormal symbol in EVM measurement error!"},
    
    { 0x258, "Statistical length for the measurement of spectrum emission results!"},
    { 0x259, "Enable/disable switch error for the measurement of spectrum emission results!"},
    { 0x25A, "Enable/disable switch error for the measurement of occupied bandwidth!"},
    { 0x25B, "Enable/disable switch error for the measurement of spectrum emission trace and margin results!"},
    { 0x25C, "Statistical length for the measurement of ACLR results!"},
    { 0x25D, "Enable/disable switch error for the measurement of ACLR results!"},
    { 0x25E, "Enable/disable switch error for the evaluation of first adjacent UTRA channels!"},
    { 0x25F, "Enable/disable switch error for the evaluation of second adjacent UTRA channels!"},
    { 0x260, "Enable/disable switch error for the evaluation of first adjacent E-UTRA channels!"},
    { 0x261, "Enable/disable switch error for the evaluation of second adjacent E-UTRA channels!"},
    
    { 0x270, "Enable/disable switch error for the measurement of power monitor results !"},
    { 0x271, "Statistical length for the measurement of the total TX power!"},
    { 0x272, "Enable/disable switch error for the measurement of the total TX power!"},
    { 0x273, "Enable/disable switch error for power dynamics measurements!"},
    { 0x274, "Leading period for OFF power measurements error!"},
    { 0x275, "Lagging period for OFF power measurements error!"},

    /* #3.2 0x281-32F, LTE UL parameter error */
    /* UL Cell */
    { 0x281, "UL-SCH group hopping state is invalid!" },
    { 0x282, "UL-SCH sequence hopping state is invalid!" },
    { 0x283, "UL-SCH delta sequence-shift out of range!" },
    { 0x284, "UL-SCH n(1)DMRS value out of range!" },
    /* UL UE */
    { 0x2A0, "UL UE power out of range!" },
    { 0x2A1, "UL UE mode error!" },
    { 0x2A2, "UL UE-SCH data type out of range!" },
    { 0x2A3, "UL UE-SCH initialization value out of range!" },
    { 0x2A4, "UL UE-SCH transmit mode error!" },
    { 0x2A5, "UL UE-SCH maximum antenna port number error!" },
    { 0x2A6, "UL UE-SCH scramble error!" },
    { 0x2A7, "UL UE-SCH channel coding state error!" },
    { 0x2A8, "UL UE-SCH channel coding mode error!" },
    { 0x2A9, "UL UE-SCH enable 256QAM error!" },
    /* UL PUSCH */
    { 0x2C0, "UL PUSCH subframe configurate number error!" },
    { 0x2C1, "UL PUCCH subframe configurate number error!" },
    { 0x2D0, "UL PUCCH subframe state error!" },
    { 0x2E0, "UL PUSCH subframe cell index error!" },
    { 0x2E1, "UL PUSCH subframe state error!" },
    { 0x2E2, "UL PUSCH subframe RB set number error!" },
    { 0x2E3, "UL PUSCH subframe RB number or RB offset error!" },
    { 0x2E4, "UL PUSCH subframe power error!" },
    { 0x2E5, "UL PUSCH subframe frequency hopping state error!" },
    { 0x2E6, "UL PUSCH subframe precoding type error!" },
    { 0x2E7, "UL PUSCH subframe layer number error!" },
    { 0x2E8, "UL PUSCH subframe antenna number error!" },
    { 0x2E9, "UL PUSCH subframe codebook index error!" },
    { 0x2EA, "UL PUSCH subframe cyclic shift field error!" },
    { 0x2EB, "UL PUSCH subframe codeword error!" },
    { 0x2EC, "UL PUSCH subframe MCS configure mode error!" },
    { 0x2ED, "UL PUSCH subframe mcs error!" },
    { 0x2EE, "UL PUSCH subframe modulate error!" },
    { 0x2F0, "UL PUSCH subframe payload size error!" },
    { 0x2F1, "UL PUSCH subframe redundancy version index error!" },
    { 0x300, "UL PUSCH channel decode state error!" },
    { 0x301, "UL PUSCH descramble state error!" },

    /* #3.3 0x330-3FF, LTE DL parameter error */
    /* LTE:DL:Cell */
    { 0x330, "DL Cell SyncTxAntenna out of range!" },
    { 0x331, "DL Cell PsyncPower out of range!" },
    { 0x332, "DL Cell SsyncPower out of range!" },
    { 0x333, "DL Cell RefSignalPower out of range!" },
    { 0x334, "DL Cell PdschPB out of range!" },
    { 0x335, "DL Cell PbchRatioRho out of range!" },
    { 0x336, "DL Cell PdcchRatioRho out of range!" },
    { 0x337, "DL Cell Power out of range!" },
    { 0x338, "DL Cell PdschStart out of range!" },
    { 0x339, "DL Cell PHICH resource error!" },
    { 0x33A, "DL Cell PhichDuration is invalid!" },
    { 0x33B, "DL Cell PDSCH scheduling type invalid!" },
    { 0x33C, "DL Cell Tx antenna number error!" },
    /* LTE:DL:UE */
    { 0x350, "DL UE Scramble state error!" },
    { 0x351, "DL UE channel coding state error!" },
    { 0x352, "DL UE ApMapping error!" },
    { 0x353, "DL UE category value out of range!" },
    { 0x354, "DL UE CodebookIndex out of range!" },
    { 0x355, "DL UE Data Type out of range!" },
    { 0x356, "DL UE initialization value out of range!" },
    { 0x357, "DL UE PdschPA value out of range!" },
    { 0x358, "DL UE Transmit Mode value out of range!" },
    { 0x359, "DL UE PDSCH MCS table error!" },
    { 0x35A, "DL UE Alternative TBS Index error!" },
    /* LTE:DL:Schedule */
    { 0x368, "DL Schedule pattern out of range!" },
    { 0x369, "DL Schedule DL DCI state out of range!" },
    { 0x36A, "DL Schedule HARQ process number out of range!" },
    { 0x36B, "DL Schedule new data indicator out of range!" },
    { 0x36C, "DL Schedule UL DCI state out of range!" },
    /* LTE:DL:Channel */
    { 0x370, "DL subframe configurate number error!" },
    { 0x371, "DL OCNG state error!" },
    { 0x372, "DL OCNG modulate type error!" },
    { 0x373, "DL OCNG data type error!" },
    { 0x378, "DL PBCH state error!" },
    { 0x379, "DL PBCH scrambling state error!" },
    { 0x37A, "DL PBCH Precoding error!" },
    { 0x37B, "DL PBCH SFN offset value out of range!" },
    { 0x37C, "DL PBCH spare bit value out of range!" },
    { 0x380, "DL PCFICH state error!" },
    { 0x381, "DL PCFICH Scrambling state error!" },
    { 0x382, "DL PCFICH Precoding error!" },
    { 0x383, "DL PCFICH Power value out of range!" },
    { 0x384, "DL PDCCH symbol number out of range!" },
    { 0x388, "DL PHICH ACK/NACK value out of range!" },
    { 0x389, "DL PHICH Power value out of range!" },
    { 0x390, "DL PDCCH configuration format type error!" },
    { 0x391, "DL PDCCH dummy CCE type error!" },
    { 0x392, "DL PDCCH dummy data type error!" },
    { 0x393, "DL PDCCH power factor out of range!" },
    { 0x398, "DL PDCCH DCI state error!" },
    { 0x399, "DL PDCCH DCI user type error!" },
    { 0x39A, "DL PDCCH DCI format type error!" },
    { 0x39B, "DL PDCCH DCI search space error!" },
    { 0x39C, "DL PDCCH format type error!" },
    { 0x39D, "DL PDCCH CCE index error!" },
    { 0x39E, "DL PDCCH auto schedule DL DCI error!" },
    /* LTE:DL:Channel:PDCCH:DCI:Format */
    { 0x3A0, "DL PDCCH DCI format resource block assignment error!" },
    { 0x3A1, "DL PDCCH DCI format modulation and coding scheme error!" },
    { 0x3A2, "DL PDCCH DCI format HARQ process number error!" },
    { 0x3A3, "DL PDCCH DCI format new data indicator error!" },
    { 0x3A4, "DL PDCCH DCI format redundancy version error!" },
    { 0x3A5, "DL PDCCH DCI format TPC command for PUCCH error!" },
    { 0x3A6, "DL PDCCH DCI format downlink assignment index error!" },
    { 0x3B0, "DL PDCCH DCI format 0 frequency hopping error!" },
    { 0x3B1, "DL PDCCH DCI format 0 cyclic shift for DMRS error!" },
    { 0x3B2, "DL PDCCH DCI format 0 UL index error!" },
    { 0x3B3, "DL PDCCH DCI format 0 downlink assignment index error!" },
    { 0x3B4, "DL PDCCH DCI format 0 CSI request error!" },
    { 0x3B5, "DL PDCCH DCI format 0 resouce allocation type error!" },
    { 0x3B8, "DL PDCCH DCI format 1 resource allocation header error!" },
    { 0x3BA, "DL PDCCH DCI format 1A mode error!" },
    { 0x3BB, "DL PDCCH DCI format 1A VRB assignment type error!" },
    { 0x3BC, "DL PDCCH DCI format 1A PDSCH RB configuration type error!" },
    { 0x3BD, "DL PDCCH DCI format 1A PDSCH RB number/offset error!" },
    /* LTE:DL:Channel:PDSCH */
    { 0x3D0, "DL PDSCH resource allocation type error!" },
    { 0x3D1, "DL PDSCH VRB assignment type error!" },
    { 0x3D2, "DL PDSCH RBG bitmap error!" },
    { 0x3D3, "DL PDSCH RB number/offset error!" },
    { 0x3D4, "DL PDSCH auto offset error!!" },
    { 0x3D5, "DL PDSCH symbol offset error!" },
    { 0x3D6, "DL PDSCH precoding error!" },
    { 0x3D7, "DL PDSCH layer number error!" },
    { 0x3D8, "DL PDSCH codebook index error!" },
    { 0x3D9, "DL PDSCH cyclic delay diversity error!" },
    { 0x3DA, "DL PDSCH codeword out of range!" },
    { 0x3DB, "DL PDSCH MCS configure mode error!" },
    { 0x3DC, "DL PDSCH MCS out of range!" },
    { 0x3DD, "DL PDSCH modulate value out of range!" },
    { 0x3DE, "DL PDSCH payload size value out of range!" },
    { 0x3DF, "DL PDSCH redundancy version index error!" },
    { 0x3E0, "DL PDSCH IR configure mode error!" },
    { 0x3E1, "DL PDSCH IR soft buffer size error!" },
    { 0x3E2, "DL PDSCH NIR value out of range!" },
    
    /* #4 0x400-5FF, VSA parameter error */
    /* 0x400-0x47F VSA common parameter error */
    { 0x400, "RF channel number error!" },
    { 0x401, "RF capture data number error!" },
    { 0x402, "RF capture data format type error!" },
    { 0x403, "RF analyze mode error!" },
    { 0x404, "RF band error!" },
    { 0x405, "DC frequency compensate error!" },
    { 0x406, "Sepctrum RBW error!" },
    { 0x407, "Measure subframe index error!" },
    { 0x408, "Measure enable error for EVM vs modulation symbol!" },
    { 0x409, "Measure symbol index error for EVM vs modulation symbol!" },
    { 0x40A, "Measure EVM windows type error for EVM vs modulation symbol!" },
    { 0x40B, "Measure physical channel type error!" },
    { 0x40C, "Statistic average flag error!" },
    { 0x40D, "Statistic average counter out of range!" },
    { 0x40E, "The flag for showing pilot signal in constellation map is error!" },
    { 0x40F, "Measure unit out of range!"},
    { 0x410, "The length of the measured subframe range error!" },
    { 0x411, "Start offset of the measured subframe range relative to the trigger event error!" },
    { 0x412, "Measure power graph enable error!" },
    { 0x413, "Measure spectrum graph enable error!" },
    { 0x414, "Measure CCDF graph enable error!" },
    { 0x415, "Network signal value error!" },
    { 0x416, "Decode views enable switch error!" },
    { 0x417, "Reserved" },
    { 0x418, "Reserved" },
    { 0x419, "Reserved" },
    { 0x420, "Measure Parameter Error: RB auto detect switch error!" },
    { 0x421, "Measure Parameter Error: Network Signal Value" },
    /* 0x480-0x4FF VSA Limit Parameter */
    { 0x480, "Modulation limit mode error" },
    { 0x481, "EVM RMS limit state error" },
    { 0x482, "EVM RMS limit value error" },
    { 0x483, "EVM peak limit state error" },
    { 0x484, "EVM peak limit value error" },
    { 0x485, "Magnitude error RMS limit state error" },
    { 0x486, "Magnitude error RMS limit value error" },
    { 0x487, "Magnitude error peak limit state error" },
    { 0x488, "Magnitude error peak limit value error" },
    { 0x489, "Phase error RMS limit state error" },
    { 0x48A, "Phase error RMS limit value error" },
    { 0x48B, "Phase error peak limit state error" },
    { 0x48C, "Phase error peak limit value error" },
    { 0x48D, "Frequency error limit state error" },
    { 0x48E, "Frequency error limit value error" },
    { 0x48F, "IQ offset limit state error" },
    { 0x490, "IQ offset limit value error when output power > 10dBm" },
    { 0x491, "IQ offset limit value error when output power > 0dBm" },
    { 0x492, "IQ offset limit value error when output power > -30dBm" },
    { 0x493, "IQ offset limit value error when output power > -40dBm" },
    { 0x494, "Inbnad emission limit state error" },
    { 0x495, "The general minimum power limit value error for inband emission measurement" },
    { 0x496, "The general EVM limit value error for inband emission measurement" },
    { 0x497, "The general RB power limit value error for inband emission measurement" },
    { 0x498, "The IQ image limit value error for inband emission measurement" },
    { 0x499, "The IQ image limit value error when output power > 10dBm for inband emission measurement" },
    { 0x49A, "The IQ image limit value error when output power <= 10dBm for inband emission measurement" },
    { 0x49B, "The IQ offset limit value error when output power > 10dBm for inband emission measurement" },
    { 0x49C, "The IQ offset limit value error when output power > 0dBm for inband emission measurement" },
    { 0x49D, "The IQ offset limit value error when output power > -30dBm for inband emission measurement" },
    { 0x49E, "The IQ offset limit value error when output power > -40dBm for inband emission measurement" },
    { 0x49F, "Spectrum flatness limit state error" },
    { 0x4A0, "Spectrum flatness upper limit for max(range 1) - min(range 1) error" },
    { 0x4A1, "Spectrum flatness upper limit for max(range 2) - min(range 2) error" },
    { 0x4A2, "Spectrum flatness upper limit for max(range 1) - min(range 2) error" },
    { 0x4A3, "Spectrum flatness upper limit for max(range 2) - min(range 1) error" },
    { 0x4A4, "Spectrum flatness frequency band edge distance of border between range 1 and range 2 error" },
    { 0x4C0, "Spectrum limit mode error" },
    { 0x4C1, "OBW limit state error" },
    { 0x4C2, "OBW upper limit error" },
    { 0x4C3, "Spectrum emission mask limit state error" },
    { 0x4C4, "Spectrum emission mask limit start frequency error" },
    { 0x4C5, "Spectrum emission mask limit stop frequency error" },
    { 0x4C6, "Spectrum emission mask limit power error" },
    { 0x4C7, "Spectrum emission mask limit start power error" },
    { 0x4C8, "Spectrum emission mask limit stop power error" },
    { 0x4C9, "Spectrum emission mask limit RBW error" },
    { 0x4CA, "ACLR UTRA relative limit state error" },
    { 0x4CB, "ACLR UTRA relative limit level error" },
    { 0x4CC, "ACLR UTRA absolute  limit state error" },
    { 0x4CD, "ACLR UTRA absolute limit level error" },
    { 0x4CE, "ACLR E-UTRA relative limit state error" },
    { 0x4CF, "ACLR E-UTRA relative limit level error" },
    { 0x4D0, "ACLR E-UTRA absolute  limit state error" },
    { 0x4D1, "ACLR E-UTRA absolute limit level error" },
    { 0x4D2, "ACLR GSM relative limit state error" },
    { 0x4D3, "ACLR GSM relative limit level error" },
    { 0x4D4, "ACLR GSM absolute  limit state error" },
    { 0x4D5, "ACLR GSM absolute limit level error" },
    { 0x4D6, "ACLR NR-UTRA relative limit state error" },
    { 0x4D7, "ACLR NR-UTRA relative limit level error" },
    { 0x4D8, "ACLR NR-UTRA absolute  limit state error" },
    { 0x4D9, "ACLR NR-UTRA absolute limit level error" },
    { 0x4DA, "Test tolerance emission mask limit level error" },
    { 0x4DB, "Test tolerance ACLR limit level error" },
    { 0x4DC, "Power Dynamics limit state error" },
    { 0x4DD, "Power Dynamics off power limit level error" },
    { 0x4DE, "Power Dynamics test tolerance limit level error" },
    { 0x4E0, "Phase Discontinuity limit state error" },
    { 0x4E1, "Phase Discontinuity Upper limit level error" },
    { 0x4E2, "Phase Discontinuity Dynamic limit level error" },
    { 0x4E8, "Power Dynamics Upper limit for the ON power error" },
    { 0x4E9, "Power Dynamics Lower limit for the ON power error" },

    /* 0x500-0x5FF VSA analyze parameter error for each standard */
    /* 0x500-0x51F VSA analyze parameter error for 5G: eMBB, Redcap, V2X */
    { 0x500, "5G Carrier Parameter Error: Use Subcarrier Spacing in Tx Bandwidth!" },
    { 0x501, "5G Carrier Parameter Error: Offset To Carrier in Tx Bandwidth!" },
    { 0x502, "NR Dmrs init type error!" },
    { 0x503, "NR Dmrs ID out of range!" },
    { 0x504, "5G Measure Parameter Error: Redcap Enabled" },
    /* 0x520-0x53F VSA analyze parameter error for 4G: LTE, NBIOT, V2X */
    /* 0x540-0x55F Reserved */
    /* 0x560-0x57F Reserved */
    /* 0x580-0x59F Reserved */
    /* 0x5A0-0x5BF Reserved */
    /* 0x5C0-0x5DF Reserved */
    /* 0x5E0-0x5FF Reserved */

    /* #5 0x600-7FF, 5G parameter error */
    /* 0x600-0x62F: 5G common error */
    { 0x600, "Cell Number error!" },
    { 0x601, "Cyclic prefix out of range or not support!" },
    { 0x602, "5G Parameter Error: 3GPP Version!" },
    { 0x603, "Cell state error!" },
    { 0x604, "Physical cell ID out of range!" },
    { 0x605, "Channel bandwidth out of range!" },
    { 0x606, "5G Parameter Error: Duplexing!" },
    { 0x607, "5G Cell TDD Parameter Error: Number of UL Slots!" },
    { 0x608, "5G Cell TDD Parameter Error: Special Slot Index!" },
    { 0x609, "5G Cell TDD Parameter Error: Slot Period!" },
    { 0x60A, "5G Cell TDD Parameter Error: Reference Subcarrier Spacing!" },
    { 0x60B, "5G Cell TDD Parameter Error: Number of DL Slots!" },
    { 0x60C, "General ARB Parameter Error: Sequence Length" },
    { 0x60D, "5G Cell TDD Parameter Error: Number of UL Symbols in Special Slot!" },
    { 0x60E, "5G Cell TDD Parameter Error: Number of DL Symbols in Special Slot!" },
    { 0x60F, "Reserved" },
    { 0x610, "Filter type don't support!" },
    { 0x611, "Fs error!" },
    { 0x612, "Maximum Order error!" },
    { 0x613, "Fpass Factor out of range!" },
    { 0x614, "Fstop Factor out of range!" },
    { 0x615, "Passband Ripple error!" },
    { 0x616, "Stopband Attenuation error!" },
    { 0x617, "Filter Mode error!" },
    { 0x618, "Smooth Factor out of range!" },
    { 0x619, "Cut Off Frequency Factor out of range!" },
    { 0x61A, "RF Phase Compensation switch error!" },
    { 0x61B, "RF Phase Compensation Frequency out of range!" },
    { 0x61C, "Deployment error!" },
    { 0x61D, "DMRS TypeA Position out of range!" },
    { 0x61E, "PDSCH Scheduling state error!" },
    { 0x61F, "System Frame Number Offset out of range!" },
    { 0x620, "Cell TxBW Parameter Error: Subcarrier Spacing" },
    { 0x621, "Cell TxBW Parameter Error: State" },
    { 0x622, "Cell TxBW Parameter Error: Max RB Number" },
    { 0x623, "Cell TxBW Parameter Error: TxBW Offset" },
    { 0x624, "Cell TxBW Parameter Error: k0u" },
    { 0x625, "UE ID out of range!" },
    { 0x626, "UE Scrambling state error!" },
    { 0x627, "UE Data Type error!" },
    { 0x628, "UE Data Initialization error!" },
    { 0x629, "UE channel coding state error!" },
    { 0x62A, "Reserved" },
    { 0x62B, "UE BWP Subcarrier Spacing error!" },
    { 0x62C, "UE BWP RB Number out of range!" },
    { 0x62D, "UE BWP RB Offset out of range!" },
    { 0x62E, "DL restrict to search space parameter error"},
    { 0x62F, "5G Parameter Error: Category" },

    /* 5G:UL:UE:BWP:PUSCH */
    { 0x630, "UL PUSCH Transform Precoder state error!" },
    { 0x631, "UL PUSCH TxConfig error!" },
    { 0x632, "UL PUSCH TPMI out of range!" },
    { 0x633, "UL PUSCH Use Pusch Scramble ID state error!" },
    { 0x634, "UL PUSCH Data Scramble ID out of range!" },
    { 0x635, "UL PUSCH Mcs Table error!" },
    { 0x636, "UL PUSCH Frequency Hopping don't support!" },
    { 0x637, "UL PUSCH Resource Allocation don't support!" },
    { 0x638, "UL PUSCH Dmrs Config Type error!" },
    { 0x639, "UL PUSCH Dmrs Max Length error!" },
    { 0x63A, "UL PUSCH Dmrs Additional Position Index out of range!" },
    { 0x63B, "UL PUSCH Dmrs Scrambling ID 0 out of range!" },
    { 0x63C, "UL PUSCH Dmrs Scrambling ID 1 out of range!" },
    { 0x63D, "UL PUSCH Dmrs Npusch ID out of range!" },
    { 0x63E, "UL PUSCH Dmrs UseR16Dmrs out of range!" },
    { 0x63F, "UL BWP PUSCH Parameter Error: Max Rank" },

    /* #5G Frame::SubFrame::Slot */
    { 0x640, "Frame Parameter Error: Subframe Configure Number" },
    { 0x641, "Frame Parameter Error: Slot Configure Number" },
    { 0x642, "Reserved" },
    { 0x643, "Reserved" },
    { 0x644, "Reserved" },
    { 0x79C, "Reserved" },

    /* 0x650-0x67F UL PUSCH */
    { 0x650, "UL PUSCH Cell Index error!" },
    { 0x653, "UL PUSCH State error!" },
    { 0x654, "UL PUSCH Mapping Type error!" },
    { 0x655, "UL PUSCH Symbol Number error!" },
    { 0x656, "UL PUSCH Symbol Offset error!" },
    { 0x657, "UL PUSCH RB Number error!" },
    { 0x658, "UL PUSCH RB Offset error!" },
    { 0x659, "UL PUSCH Layer Number error!" },
    { 0x65A, "UL PUSCH Antenna Number error!" },
    { 0x65B, "UL PUSCH Modulate error!" },
    { 0x65C, "UL PUSCH CDM Groups Without Data error!" },
    { 0x65D, "UL PUSCH Dmrs Symbol Length error!" },
    { 0x65E, "UL PUSCH Dmrs Antenna Port error!" },
    { 0x65F, "UL PUSCH NScrID Type error!" },
    { 0x660, "UL PUSCH NSCID error!" },
    { 0x661, "UL PUSCH NRSID Type error!" },
    { 0x662, "UL PUSCH Group Or Sequence Hopping don't support!" },
    { 0x663, "UL PUSCH MCS error!" },
    { 0x665, "UL PUSCH RV Index error!" },
    { 0x666, "UL PUSCH Power out of range!" },
    { 0x667, "UL PUSCH Dmrs Power out of range!" },

    /* 0x680-0x69F: 5G Cell Common Error */
    { 0x680, "Cell Parameter Error: CIF Present"},
    { 0x681, "Cell Parameter Error: Shared Spectrum Access" },

    /* 0x6A0-0x6BF: 5G UE Common Error */
    { 0x6A0, "UL BWP PUSCH Parameter Error: Max Code Block Groups Per Transport Block" },
    { 0x6A1, "UL BWP PUSCH Parameter Error: Minimum Scheduling Offset K2" },
    { 0x6A2, "UL BWP PUSCH Parameter Error: Harq-ProcessNumberSize" },
    { 0x6A3, "UL BWP PUSCH Parameter Error: Priority Indicator" },
    { 0x6A4, "UL BWP PUSCH Parameter Error: Invalid Symbol Indicator" },
    { 0x6A5, "UL BWP PUSCH Parameter Error: olpc-ParameterSet" },
    { 0x6A6, "UL BWP PUSCH Parameter Error: p0-PUSCH-SetList" },
    { 0x6A7, "UL BWP PUSCH Parameter Error: ul-AccessConfigList" },
    { 0x6A8, "UL BWP PUSCH Parameter Error: Use 2nd TPC Command" },
    { 0x6A9, "UL BWP PUCCH Parameter Error: Use Interlace" },
    { 0x6AA, "UL BWP PUCCH Parameter Error: Number of Entries in PDSCH to HARQ Timing Map" },
    { 0x6AB, "UL BWP PUCCH Parameter Error: Number of Entries in Channel-Access-CPext" },
    { 0x6AC, "UL BWP PUCCH Parameter Error: Use 2nd TPC Command" },
    { 0x6AD, "UL BWP SRS Parameter Error: Resource Sets" },
    { 0x6AE, "UL BWP SRS Parameter Error: Resources in Control" },
    { 0x6AF, "UL BWP SRS Parameter Error: No. Parts in Control" },

    /* 0x6B0-6EF, NR Measure error */
    { 0x6B0, "Statistical length for the measurement of modulation results in slots error!" },
    { 0x6B1, "Enable/disable switch error for the measurement of modulation results!" },
    { 0x6B2, "Enable/disable switch error for the measurement of EVM!" },
    { 0x6B3, "Enable/disable switch error for the measurement of magnitude error!" },
    { 0x6B4, "Enable/disable switch error for the measurement of phase error!" },
    { 0x6B5, "Enable/disable switch error for the measurement of EVM vs subcarrier!" },
    { 0x6B6, "Enable/disable switch error for the measurement of inband emissions!" },
    { 0x6B7, "Enable/disable switch error for the measurement of equalizer spectrum flatness!" },
    { 0x6B8, "Enable/disable switch error for the measurement of IQ constellation!" },
    { 0x6B9, "Enable/disable switch error for the measurement of TX measurement result!" },
    { 0x6BA, "Exclude abnormal symbol in EVM measurement error!" },

    { 0x6C8, "Statistical length for the measurement of spectrum emission results!" },
    { 0x6C9, "Enable/disable switch error for the measurement of spectrum emission results!" },
    { 0x6CA, "Enable/disable switch error for the measurement of occupied bandwidth!" },
    { 0x6CB, "Enable/disable switch error for the measurement of spectrum emission trace and margin results!" },
    { 0x6CC, "Statistical length for the measurement of ACLR results!" },
    { 0x6CD, "Enable/disable switch error for the measurement of ACLR results!" },
    { 0x6CE, "Enable/disable switch error for the evaluation of first adjacent UTRA channels!" },
    { 0x6CF, "Enable/disable switch error for the evaluation of second adjacent UTRA channels!" },
    { 0x6D0, "Enable/disable switch error for the evaluation of adjacent NR channels!" },

    { 0x6E0, "Enable/disable switch error for the measurement of power monitor results !" },
    { 0x6E1, "Statistical length for the measurement of the total TX power!" },
    { 0x6E2, "Enable/disable switch error for the measurement of the total TX power!" },
    { 0x6E3, "Statistical length for power dynamics measurements!" },
    { 0x6E4, "Enable/disable switch error for power dynamics measurements!" },
    { 0x6E5, "Leading period for OFF power measurements error!" },
    { 0x6E6, "Lagging period for OFF power measurements error!" },

    { 0x6F0, "Enable/disable switch for Measure on Exception!" },
    { 0x6F1, "Measurement subframe length error!" },
    { 0x6F2, "Enable/disable switch for Measure all SLOT" },
    { 0x6F3, "Measurement SLOT number error!" },
    { 0x6F4, "Sending DC position offset error!" },
    { 0x6F5, "Enable/disable switch for DC compensation!" },
    { 0x6F6, "Enable/disable switch for DMRS Constellation!" },
    { 0x6F7, "Measurement Subframe Parameter Error: Measure Slot Index" },

    { 0x6F8, "Assign Views Parameter Error: Enable/disable switch for PDSCH Info!" },
    { 0x6F9, "Assign Views Parameter Error: Enable/disable switch for SSB Info!" },

    /* 5G DL, 0x700-7FF*/
    /* 5G:DL:Cell */
    { 0x700, "DL PBCH state error!" },
    { 0x701, "DL PBCH Subcarrier Spacing error!" },
    { 0x702, "DL PBCH RB Offset error!" },
    { 0x703, "DL PBCH Subcarrier Offset error!" },
    { 0x704, "DL PBCH Case error!" },
    { 0x705, "DL PBCH Length error!" },
    { 0x706, "DL PBCH Position error!" },
    { 0x707, "DL PBCH Burst Set Period error!" },
    { 0x708, "DL PBCH Half Frame Index error!" },
    { 0x709, "DL PBCH PBCH Power out of range!" },
    { 0x70A, "DL PBCH PSS Power out of range!" },
    { 0x70B, "DL PBCH SSS Powerout of range!" },
    { 0x70C, "DL PBCH MIB Common Subcarrier Spacing error!" },
    { 0x70D, "DL PBCH MIB SSB Subcarrier Offset out of range!" },
    { 0x70E, "DL PBCH MIB CORESET Zero of range!" },
    { 0x70F, "DL PBCH MIB Search Space Zero out of range!" },
    { 0x710, "DL PBCH MIB Cell Barred state error!" },
    { 0x711, "DL PBCH MIB Intra-Frequency Reselection state error!" },
    { 0x711, "DL PBCH MIB auto subcarrier offfset error!" },
    { 0x712, "DL PBCH Offset Relative to TxBW/PointA error!" },
    { 0x713, "DL Cell OCNG state error!" },
    { 0x714, "DL Cell OCNG mode type parameter error!" },
    { 0x715, "DL Cell OCNG modulation type error!" },
    { 0x716, "DL Cell OCNG data type error!" },
    { 0x717, "DL Cell OCNG power error!" },
    { 0x718, "DL Cell Parameter Error: PDSCH HARQ ACK Codebook" },
    { 0x719, "DL Cell Parameter Error: PDSCH HARQ ACK Codebook R16" },
    { 0x71A, "DL Cell Parameter Error: SUL" },

    /* 5G:DL:UE */
    { 0x720, "DL UE Control Parameter Error: SCell Groups Within Active Time" },
    { 0x721, "DL UE Control Parameter Error: PDSCH Harq-Ack-One-Shot-Feedback-R16" },
    { 0x722, "DL UE Control Parameter Error: Ack-Nack-Feedback-Mode" },
    { 0x723, "DL UE Control Parameter Error: NFI-TotalDAI-Included" },
    { 0x724, "DL UE Control Parameter Error: UL-TotalDai-Included" },
    { 0x725, "DL UE Control Parameter Error: Bits for Sidelink Assignment Index" },
    { 0x726, "DL UE Control Parameter Error: PUCCH-sSCellDyn in DCI 1_1" },
    { 0x727, "DL UE Control Parameter Error: HARQ-ACK Retransmission Indicator in DCI 1_1" },
    { 0x728, "Reserved" },
    { 0x729, "Reserved" },
    { 0x72A, "Reserved" },
    { 0x72B, "Reserved" },
    /* 5G:DL:BWP:PDSCH */
    { 0x72C, "DL BWP PDSCH Control Parameter Error: PRB Bundling Type"},
    { 0x72D, "DL BWP PDSCH Control Parameter Error: Number of Entries in Minimum Scheduling Offset Indicator K0 "},
    { 0x72E, "DL BWP PDSCH Control Parameter Error: Priority Indicator in DCI 1_1"},
    { 0x72F, "DL BWP PDSCH Control Parameter Error: Harq-ProcessNumberSize in DCI 1_1"},
    { 0x730, "DL BWP PDSCH Use Pdsch Scramble ID state error!" },
    { 0x731, "DL BWP PDSCH Data Scramble ID out of range!" },
    { 0x732, "DL BWP PDSCH max. number of codewords per DCI error!"},
    { 0x733, "DL BWP PDSCH VRB-to-PRB Interleaver don't support!" },
    { 0x734, "DL BWP PDSCH Mcs Table error!" },
    { 0x735, "DL BWP PDSCH Resource Allocation error!" },
    { 0x736, "DL BWP PDSCH resource block group size error!" },
    { 0x737, "DL BWP PDSCH max code block groups per transport block error!" },
    { 0x738, "DL BWP PDSCH DMRS Parameter Error: Config Type" },
    { 0x739, "DL BWP PDSCH DMRS Parameter Error: Max Length" },
    { 0x73A, "DL BWP PDSCH DMRS Parameter Error: Additional Position Index" },
    { 0x73B, "DL BWP PDSCH DMRS Parameter Error: Scrambling ID 0" },
    { 0x73C, "DL BWP PDSCH DMRS Parameter Error: Scrambling ID 1" },
    { 0x73D, "DL BWP PDSCH DMRS Parameter Error: Use R16 DMRS" },
    { 0x73E, "DL BWP PDSCH General Parameter Error: Code Block Group Flush Indicator"},
    { 0x73F, "DL BWP PDSCH General Parameter Error: Precoding"},
    /* 5G:DL:BWP:CORESET */
    { 0x740, "DL BWP CORESET state error!" },
    { 0x741, "DL BWP CORESET Symbol Number error!" },
    { 0x742, "DL BWP CORESET Symbol Offset error!" },
    { 0x743, "DL BWP CORESET Use Bitmap for Res. in FD error"},
    { 0x744, "DL BWP CORESET RB Number error!" },
    { 0x745, "DL BWP CORESET RB Offset error!" },
    { 0x746, "DL BWP CORESET Frequency Domain Resources error!" },
    { 0x747, "DL BWP CORESET ID error!" },
    { 0x748, "DL BWP CORESET Precoder Granularity error!" },
    { 0x749, "DL BWP CORESET Use DMRS scrambling ID error!" },
    { 0x74A, "DL BWP CORESET DMRS scrambling ID error!" },
    { 0x74B, "DL BWP CORESET DMRS Reference Point error!" },
    { 0x74C, "DL BWP CORESET Interleaving State error!" },
    { 0x74D, "DL BWP CORESET Bundle Size error!" },
    { 0x74E, "DL BWP CORESET Shift Index error!" },
    { 0x74F, "DL BWP CORESET Interleaver Size error!" },
    { 0x750, "DL BWP CORESET Max. Candidate error!" },
    /* 5G:DL:BWP:Control */
    { 0x751, "DL BWP Control Parameter Error: Report Trigger Size in DCI 0_1" },
    { 0x752, "DL BWP Control Parameter Error: Bits for PDCCH Monitoring Adaptation Indication in DCI 0_1" },
    { 0x753, "DL BWP Control Parameter Error: Bits for SRS Resource Set Indication in DCI 0_1" },
    { 0x754, "DL BWP Control Parameter Error: Bits for 2nd SRS Resource Indication in DCI 0_1" },
    { 0x755, "DL BWP Control Parameter Error: Bits for 2nd Precoding Information in DCI 0_1" },
    { 0x756, "DL BWP Control Parameter Error: Bits for SRS Offset Indicator in DCI 0_1" },
    { 0x757, "DL BWP Control Parameter Error: Max. Bits for 2nd Downlink Assignment Index in DCI 0_1" },
    { 0x758, "DL BWP Control Parameter Error: Bits for 3rd Downlink Assignment Index in DCI 0_1" },
    { 0x759, "DL BWP Control Parameter Error: Bits for PDCCH Monitoring Adaptation Indication in DCI 1_1" },
    { 0x75A, "DL BWP Control Parameter Error: Bits for Enhanced Type 3 Codebook Indicator in DCI 1_1" },
    { 0x75B, "DL BWP Control Parameter Error: Bits for SRS Offset Indicator in DCI 1_1" },
    /* 5G:DL:BWP:Rate Match */
    { 0x75C, "DL BWP Rate Match Parameter Error: Number of Groups" },
    { 0x75D, "Reserved" },
    { 0x75E, "Reserved" },
    { 0x75F, "Reserved" },

    /* 5G:DL:Scheduled:PDSCH */
    { 0x760, "DL PDSCH State Error!" },
    { 0x761, "DL PDSCH Mapping Type error!" },
    { 0x762, "DL PDSCH Symbol Number error!" },
    { 0x763, "DL PDSCH Symbol Offset error!" },
    { 0x764, "DL PDSCH Resource Allocation error!" },
    { 0x765, "DL PDSCH RBG Bitmap error!" },
    { 0x766, "DL PDSCH RB Number error!" },
    { 0x767, "DL PDSCH RB Offset error" },
    { 0x768, "DL PDSCH Codeword Number error!" },
    { 0x769, "DL PDSCH Layer Number error!" },
    { 0x76A, "DL PDSCH Antenna Number error!" },
    { 0x76B, "DL PDSCH CDM Groups Without Data error!" },
    { 0x76C, "DL PDSCH Dmrs Symbol Length error!" },
    { 0x76D, "DL PDSCH Dmrs Antenna Port error!" },
    { 0x76E, "DL PDSCH NScrID Type error" },
    { 0x76F, "DL PDSCH NSCID error!" },
    { 0x770, "DL PDSCH Dmrs Power out of range!" },
    { 0x771, "DL PDSCH Modulate error!" },
    { 0x772, "DL PDSCH MCS out of range!" },
    { 0x773, "DL PDSCH Redundancy Version Index error!" },
    { 0x774, "PDSCH Slot Parameter Error: Use RBG Bitmap more than maximum PRBs in Redcap Release 18" },
    { 0x775, "Reserved" },
    { 0x776, "Reserved" },
    { 0x777, "Reserved" },
    { 0x778, "Reserved" },
    { 0x779, "Reserved" },
    { 0x77A, "Reserved" },
    { 0x77B, "Reserved" },
    { 0x77C, "Reserved" },
    { 0x77D, "Reserved" },
    { 0x77E, "Reserved" },
    { 0x77F, "Reserved" },
    /* 5G:DL:Channel:PDCCH */
    { 0x780, "DL PDCCH state error!" },
    { 0x781, "DL PDCCH Unused CCEs error!" },
    { 0x782, "DL PDCCH Data Type error!" },
    { 0x783, "DL PDCCH Data Initialization error!" },
    { 0x784, "DL PDCCH Auto Scheduling DL DCI error!"},
    { 0x785, "DL PDCCH power error!"},
    { 0x786, "DL PDCCH DCI State error" },
    { 0x787, "DL PDCCH DCI Usage error" },
    { 0x788, "DL PDCCH DCI Format error!" },
    { 0x789, "DL PDCCH Search Space error!" },
    { 0x78A, "DL PDCCH Aggregation Level error!" },
    { 0x78B, "DL PDCCH Candidate error!" },
    { 0x78C, "DL PDCCH CCE Index error!" },
    { 0x78D, "DL PDCCH Time Domain Allocation Table Type error!" },
    { 0x78E, "DL PDCCH Power error!" },
    { 0x78F, "DL PDCCH Error: TCI Present In DCI" },
    { 0x790, "DL PDCCH DCI 1_0 Parameter Error: Frequency Domain Resource Assignment" },
    { 0x791, "DL PDCCH DCI 1_0 Parameter Error: Time Domain Resource Assignment" },
    { 0x792, "DL PDCCH DCI 1_0 Parameter Error: VRB-to-PRB Mapping" },
    { 0x793, "DL PDCCH DCI 1_0 Parameter Error: Modulation And Coding Scheme" },
    { 0x794, "DL PDCCH DCI 1_0 Parameter Error: New Data Indicator" },
    { 0x795, "DL PDCCH DCI 1_0 Parameter Error: Redundancy Version" },
    { 0x796, "DL PDCCH DCI 1_0 Parameter Error: HARQ Process Number" },
    { 0x797, "DL PDCCH DCI 1_0 Parameter Error: Downlink Assignment Index" },
    { 0x798, "DL PDCCH DCI 1_0 Parameter Error: TPC Command For Scheduled PUCCH" },
    { 0x799, "DL PDCCH DCI 1_0 Parameter Error: PUCCH Resource Indicator" },
    { 0x79A, "DL PDCCH DCI 1_0 Parameter Error: PDSCH-to-HARQ Feedback Timing Indicator" },
    { 0x79B, "DL PDCCH DCI 1_0 Parameter Error: Channel Access CPext" },
    { 0x79C, "Reserved" },
    { 0x79D, "Reserved" },
    { 0x79E, "Reserved" },
    { 0x79F, "Reserved" },
    { 0x7A0, "DL PDCCH DCI 1_1 Parameter Error: Carrier Indicator(CIF)" },
    { 0x7A1, "DL PDCCH DCI 1_1 Parameter Error: Bandwidth Part Indicator" },
    { 0x7A2, "DL PDCCH DCI 1_1 Parameter Error: Frequency Domain Resource Assignment" },
    { 0x7A3, "DL PDCCH DCI 1_1 Parameter Error: Time Domain Resource Assignment" },
    { 0x7A4, "DL PDCCH DCI 1_1 Parameter Error: VRB-to-PRB Mapping" },
    { 0x7A5, "DL PDCCH DCI 1_1 Parameter Error: PRB Bundling Size Indicator" },
    { 0x7A6, "DL PDCCH DCI 1_1 Parameter Error: Rate Matching Indicator" },
    { 0x7A7, "DL PDCCH DCI 1_1 Parameter Error: ZP CSI-RS trigger" },
    { 0x7A8, "DL PDCCH DCI 1_1 Parameter Error: Modulation And Coding Scheme(TB1)" },
    { 0x7A9, "DL PDCCH DCI 1_1 Parameter Error: New Data Indicator (TB1)" },
    { 0x7AA, "DL PDCCH DCI 1_1 Parameter Error: Redundancy Version Index(TB1)" },
    { 0x7AB, "DL PDCCH DCI 1_1 Parameter Error: Modulation And Coding Scheme(TB2)" },
    { 0x7AC, "DL PDCCH DCI 1_1 Parameter Error: New Data Indicator (TB2)" },
    { 0x7AD, "DL PDCCH DCI 1_1 Parameter Error: Redundancy Version Index(TB2)" },
    { 0x7AE, "DL PDCCH DCI 1_1 Parameter Error: HARQ Process Number" },
    { 0x7AF, "DL PDCCH DCI 1_1 Parameter Error: Downlink Assignment Index" },
    { 0x7B0, "DL PDCCH DCI 1_1 Parameter Error: TPC Command For Scheduled PUCCH" },
    { 0x7B1, "DL PDCCH DCI 1_1 Parameter Error: 2nd TPC command for scheduling PUCCH" },
    { 0x7B2, "DL PDCCH DCI 1_1 Parameter Error: PUCCH Resource Indicator" },
    { 0x7B3, "DL PDCCH DCI 1_1 Parameter Error: PDSCH-to-HARQ Feedback Timing Indicator" },
    { 0x7B4, "DL PDCCH DCI 1_1 Parameter Error: One-Shot HARQ-ACK Request" },
    { 0x7B5, "DL PDCCH DCI 1_1 Parameter Error: Enhanced Type 3 Codebook Indicator" },
    { 0x7B6, "DL PDCCH DCI 1_1 Parameter Error: PDSCH Group Index" },
    { 0x7B7, "DL PDCCH DCI 1_1 Parameter Error: New Feedback Indicator" },
    { 0x7B8, "DL PDCCH DCI 1_1 Parameter Error: Number of Requested PDSCH Groups" },
    { 0x7B9, "DL PDCCH DCI 1_1 Parameter Error: HARQ-ACK Retransmission Indicator" },
    { 0x7BA, "DL PDCCH DCI 1_1 Parameter Error: Antenna ports" },
    { 0x7BB, "DL PDCCH DCI 1_1 Parameter Error: Transmission Configuration Indication" },
    { 0x7BC, "DL PDCCH DCI 1_1 Parameter Error: SRS request" },
    { 0x7BD, "DL PDCCH DCI 1_1 Parameter Error: SRS Offset Indicator" },
    { 0x7BE, "DL PDCCH DCI 1_1 Parameter Error: CBG Transmission Information (CBGTI)" },
    { 0x7BF, "DL PDCCH DCI 1_1 Parameter Error: CBG Flushing Out Information (CBGFI)" },
    { 0x7C0, "DL PDCCH DCI 1_1 Parameter Error: DMRS Sequence Initialization" },
    { 0x7C1, "DL PDCCH DCI 1_1 Parameter Error: Priority indicator" },
    { 0x7C2, "DL PDCCH DCI 1_1 Parameter Error: Channel Access CPext" },
    { 0x7C3, "DL PDCCH DCI 1_1 Parameter Error: Minimum Applicable Shceduling Offset Indicator" },
    { 0x7C4, "DL PDCCH DCI 1_1 Parameter Error: SCell Dormancy Indication 1" },
    { 0x7C5, "DL PDCCH DCI 1_1 Parameter Error: PDCCH Monitoring Adaption Indication" },
    { 0x7C6, "DL PDCCH DCI 1_1 Parameter Error: PUCCH Cell Indicator" },
    { 0x7C8, "DL PDCCH DCI 0_0 Parameter Error: Frequency Domain Resource Assignment" },
    { 0x7C9, "DL PDCCH DCI 0_0 Parameter Error: Time Domain Resource Assignment" },
    { 0x7CA, "DL PDCCH DCI 0_0 Parameter Error: Frequency Hopping Flag" },
    { 0x7CB, "DL PDCCH DCI 0_0 Parameter Error: Modulation And Coding Scheme(TB1)" },
    { 0x7CC, "DL PDCCH DCI 0_0 Parameter Error: New Data Indicator (TB1)" },
    { 0x7CD, "DL PDCCH DCI 0_0 Parameter Error: Redundancy Version Index(TB1)" },
    { 0x7CE, "DL PDCCH DCI 0_0 Parameter Error: HARQ Process Number" },
    { 0x7CF, "DL PDCCH DCI 0_0 Parameter Error: TPC Command For Scheduled PUSCH" },
    { 0x7D0, "DL PDCCH DCI 0_0 Parameter Error: Channel Access CPext" },
    { 0x7D1, "DL PDCCH DCI 0_0 Parameter Error: UL/SUL Indicator" },
    { 0x7D8, "DL PDCCH DCI 0_1 Parameter Error: Carrier indicator(CIF)" },
    { 0x7D9, "DL PDCCH DCI 0_1 Parameter Error: Reserved Bits" },
    { 0x7DA, "DL PDCCH DCI 0_1 Parameter Error: UL/SUL Indicator" },
    { 0x7DB, "DL PDCCH DCI 0_1 Parameter Error: Frequency Domain Resource Assignment" },
    { 0x7DC, "DL PDCCH DCI 0_1 Parameter Error: Time Domain Resource Assignment" },
    { 0x7DD, "DL PDCCH DCI 0_1 Parameter Error: Modulation And Coding Scheme(TB1)" },
    { 0x7DE, "DL PDCCH DCI 0_1 Parameter Error: New Data Indicator (TB1)" },
    { 0x7DF, "DL PDCCH DCI 0_1 Parameter Error: Redundancy Version Index(TB1)" },
    { 0x7E0, "DL PDCCH DCI 0_1 Parameter Error: HARQ Process Number" },
    { 0x7E1, "DL PDCCH DCI 0_1 Parameter Error: (1st) Downlink Assignment Index" },
    { 0x7E2, "DL PDCCH DCI 0_1 Parameter Error: 2nd Downlink Assignment Index" },
    { 0x7E3, "DL PDCCH DCI 0_1 Parameter Error: 3rd Downlink Assignment Index" },
    { 0x7E4, "DL PDCCH DCI 0_1 Parameter Error: TPC Command For Scheduled PUSCH" },
    { 0x7E5, "DL PDCCH DCI 0_1 Parameter Error: 2nd TPC command for scheduling PUSCH" },
    { 0x7E6, "DL PDCCH DCI 0_1 Parameter Error: SRS resource set indicator" },
    { 0x7E7, "DL PDCCH DCI 0_1 Parameter Error: SRS Resource Indicator" },
    { 0x7E8, "DL PDCCH DCI 0_1 Parameter Error: 2nd SRS resource indicator" },
    { 0x7E9, "DL PDCCH DCI 0_1 Parameter Error: Precoding information and number of layers" },
    { 0x7EA, "DL PDCCH DCI 0_1 Parameter Error: 2nd Precoding information and number of layers" },
    { 0x7EB, "DL PDCCH DCI 0_1 Parameter Error: Antenna Ports" },
    { 0x7EC, "DL PDCCH DCI 0_1 Parameter Error: SRS Request" },
    { 0x7ED, "DL PDCCH DCI 0_1 Parameter Error: SRS Offset Indicator" },
    { 0x7EE, "DL PDCCH DCI 0_1 Parameter Error: CSI Request" },
    { 0x7EF, "DL PDCCH DCI 0_1 Parameter Error: CBG Transmission Information (CBGTI)" },
    { 0x7F0, "DL PDCCH DCI 0_1 Parameter Error: PTRS-DMRS Association" },
    { 0x7F1, "DL PDCCH DCI 0_1 Parameter Error: 2nd PTRS-DMRS Association" },
    { 0x7F2, "DL PDCCH DCI 0_1 Parameter Error: DMRS Sequence Initialization" },
    { 0x7F3, "DL PDCCH DCI 0_1 Parameter Error: UL-SCH Indicator" },
    { 0x7F4, "DL PDCCH DCI 0_1 Parameter Error: Channel Access CPext" },
    { 0x7F5, "DL PDCCH DCI 0_1 Parameter Error: Open-loop power control parameter indication" },
    { 0x7F6, "DL PDCCH DCI 0_1 Parameter Error: Priority indicator" },
    { 0x7F7, "DL PDCCH DCI 0_1 Parameter Error: Invalid symbol pattern indicator" },
    { 0x7F8, "DL PDCCH DCI 0_1 Parameter Error: Min. Applicable Scheduling Offset Indicator" },
    { 0x7F9, "DL PDCCH DCI 0_1 Parameter Error: PDCCH Monitoring Adaption Indication" },
    { 0x7FA, "Reserved" },
    { 0x7FB, "Reserved" },
    { 0x7FC, "Reserved" },
    { 0x7FD, "Reserved" },
    { 0x7FE, "Reserved" },
    { 0x7FF, "Reserved" },

    /* #6 0x800-9FF, NB-IOT parameter error */
    /* 0x800-0x8FF, NB-IOT UL */
    /* UL Cell */
    { 0x800, "UL Cell Operation Mode out of range!" },
    { 0x801, "UL Cell Channel BandWidth out of range!" },
    { 0x802, "UL Cell RB Index value out of range!" },
    { 0x803, "UL Cell NB Cell ID value out of range!" },
    { 0x804, "UL Cell Group Hopping state error!" },
    { 0x805, "UL Cell Three Tone Cyclic Shift value out of range!" },
    { 0x806, "UL Cell Six Tone Cyclic Shift value out of range!" },
    { 0x807, "UL Cell Base Sequence Mode state error!" },
    { 0x808, "UL Cell Three Tone Base Sequence value out of range!" },
    { 0x809, "UL Cell Six Tone Base Sequence value out of range!" },
    { 0x80A, "UL Cell Twelve Tone Base Sequence value out of range!" },
    { 0x80B, "UL Cell Delta Squence Shift value out of range!" },
    { 0x80C, "UL Cell Cyclic Shift value out of range!" },
    /* UL UE */
    { 0x820, "UL UE UE ID out of range!" },
    { 0x821, "UL UE Scrambling state error!" },
    { 0x822, "UL UE channel coding state error!" },
    /* UL Schedule */
    { 0x830, "UL Schedule Channel Type error!" },
    { 0x831, "UL Schedule Npusch Format error!" },
    { 0x832, "UL Schedule Npusch Subcarrier Spacing error!" },
    { 0x833, "UL Schedule Npusch Start Subframe error!" },
    { 0x834, "UL Schedule Npusch Number of repetitions out of range!" },
    { 0x835, "UL Schedule Npusch Number of resource units out of range!" },
    { 0x836, "UL Schedule Npusch Use Isc state error!" },
    { 0x837, "UL Schedule Npusch Isc Index out of range!" },
    { 0x838, "UL Schedule Npusch Subcarrier Number out of range!" },
    { 0x839, "UL Schedule Npusch Start Subcarrier out of range!" },
    { 0x83A, "UL Schedule Npusch Modulate out of range!" },
    { 0x83B, "UL Schedule Npusch Use Mcs state error!" },
    { 0x83C, "UL Schedule Npusch Mcs out of range!" },
    { 0x83D, "UL Schedule Npusch TBS Index out of range!" },
    { 0x83E, "UL Schedule Npusch Start rv_idx error!" },
    { 0x83F, "UL Schedule Npusch Use ACK/NACK  Resource Field state error!" },
    { 0x840, "UL Schedule Npusch ACK/NACK Resource Field out of range!" },
    { 0x841, "UL Schedule Npusch Subcarrier Index out of range!" },
    { 0x842, "UL Schedule Npusch HARQ-ACK Information error!" },

    /* 0x900-0x9FF, NB-IOT DL */
    /* DL Cell */
    { 0x900, "DL Cell Channel BandWidth out of range!" },
    { 0x901, "DL Cell Lte PhyCellID out of range!" },
    { 0x902, "DL Cell Lte RARNTI out of range!" },
    { 0x903, "DL Cell Lte LteAntennaNum is not one!" },
    { 0x904, "DL Cell Lte LteREsFillState error!" },
    { 0x905, "DL Cell Lte Modulation out of range!" },
    { 0x906, "DL Cell NBCellID out of range!" },
    { 0x907, "DL Cell NBAntennaNum is not one!" },
    { 0x908, "DL Cell Anchor OperationMode out of range!" },
    { 0x909, "DL Cell Anchor RBIdx out of range!" },
    { 0x90A, "DL Cell Anchor CSSType1Rmax out of range!" },
    { 0x90B, "DL Cell Anchor CSSType2Rmax out of range!" },
    { 0x90C, "DL Cell Anchor CSSType2G out of range!" },
    { 0x90D, "DL Cell Anchor CSSType2Offset out of range!" },
    /* DL Ue */
    { 0x920, "DL Ue UeID out of range!" },
    { 0x921, "DL Ue UeCategory out of range!" },
    { 0x922, "DL Ue USS Rmax out of range!" },
    { 0x923, "DL Ue USS G out of range!" },
    { 0x924, "DL Ue USS Offset out of range!" },
    /* DL Schedule */
    { 0x930, "DL Schedule Anchor Dci User out of range!" },
    { 0x931, "DL Schedule Anchor Dci DciFormat out of range!" },
    { 0x932, "DL Schedule Anchor Dci SearchSpace out of range!" },
    { 0x933, "DL Schedule Anchor Dci N0 Isc out of range!" },
    { 0x934, "DL Schedule Anchor Dci N0 Iru out of range!" },
    { 0x935, "DL Schedule Anchor Dci N0 Idelay out of range!" },
    { 0x936, "DL Schedule Anchor Dci N0 Imcs out of range!" },
    { 0x937, "DL Schedule Anchor Dci N0 RedunVer state error!" },
    { 0x938, "DL Schedule Anchor Dci N0 Irep out of range!" },
    { 0x939, "DL Schedule Anchor Dci N0 NewDataInd state error" },
    { 0x93A, "DL Schedule Anchor Dci N0 DciRepNum out of range!" },
    { 0x93B, "DL Schedule Anchor Dci N1 OrderInd state error!" },
    { 0x93C, "DL Schedule Anchor Dci N1 NprachRep out of range!" },
    { 0x93D, "DL Schedule Anchor Dci N1 NprachSC out of range!" },
    { 0x93E, "DL Schedule Anchor Dci N1 Idelay out of range!" },
    { 0x93F, "DL Schedule Anchor Dci N1 Isf range out of range!" },
    { 0x940, "DL Schedule Anchor Dci N1 Imcs out of range!" },
    { 0x941, "DL Schedule Anchor Dci N1 Irep out of range!" },
    { 0x942, "DL Schedule Anchor Dci N1 NewDataInd state error!" },
    { 0x943, "DL Schedule Anchor Dci N1 HarqAckRes out of range!" },
    { 0x944, "DL Schedule Anchor Dci N1 DciRepNum out of range!" },
    { 0x945, "DL Schedule Anchor Dci N1 DistanceType out of range!" },
    { 0x946, "DL Schedule Anchor Dci N2 PagFlg state error!" },
    { 0x947, "DL Schedule Anchor Dci N2 SysInfoModifEDRX state error!" },
    { 0x948, "DL Schedule Anchor Dci N2 SysInfoModif state error!" },
    { 0x949, "DL Schedule Anchor Dci N2 Isf out of range!" },
    { 0x94A, "DL Schedule Anchor Dci N2 Imcs out of range!" },
    { 0x94B, "DL Schedule Anchor Dci N2 Irep out of range!" },
    { 0x94C, "DL Schedule Anchor Dci N2 DciRepNum out of range!" },
    { 0x94D, "DL Schedule Anchor Dci StartSubfrm out of range!" },
    { 0x94E, "DL Schedule Anchor Dci NpdcchFormat state error!" },
    { 0x94F, "DL Schedule Anchor Dci NcceIdx state error!" },
    { 0x950, "DL Schedule Anchor Npbch Precoding state error!" },
    { 0x951, "DL Schedule Anchor Npbch Scrambling state error!" },
    { 0x952, "DL Schedule Anchor Npbch ChanCodingState state error!" },
    { 0x953, "DL Schedule Anchor Npbch UseMIB state error!" },
    { 0x954, "DL Schedule Anchor Npbch SFN out of range!" },
    { 0x955, "DL Schedule Anchor Npbch HyperSFN out of range!" },
    { 0x956, "DL Schedule Anchor Npbch SchedInfoSIB1 out of range!" },
    { 0x957, "DL Schedule Anchor Npbch SysInfoValueTag out of range!" },
    { 0x958, "DL Schedule Anchor Npbch ABEnabled state error!" },
    { 0x959, "DL Schedule Anchor SIB1 Precoding state error!" },
    { 0x95A, "DL Schedule Anchor SIB1 Scrambling state error!" },
    { 0x95B, "DL Schedule Anchor SIB1 ChanCodingState state error!" },
    { 0x95C, "DL Schedule Anchor SIB1 TBSIdx out of range!" },
    { 0x95D, "DL Schedule Anchor Npdcch StartSymb out of range!" },
    { 0x95E, "DL Schedule Anchor Npdcch Scrambling state error!" },
    { 0x95F, "DL Schedule Anchor Npdcch Precoding state error!" },
    { 0x960, "DL Schedule Anchor Npdsch StartSymb out of range!" },
    { 0x961, "DL Schedule Anchor Npdsch Scrambling state error!" },
    { 0x962, "DL Schedule Anchor Npdsch Precoding state error!" },
    { 0x963, "DL Schedule Anchor Npdsch ChanCodingState state error!" },

    /* #7 0xA00-CFF, 3G parameter error */
    /* General parameter error: 0xA00-A1F */
    { 0xA00, "Generate Frame Length out of range!" },
    { 0xA01, "Trch Number of State On is Zero When Channel Coding state is On!" },
    { 0xA02, "Frame Length is larger than specified after Rate Matching!" },

     /* WCDMA UL : 0xA20-A7F*/
    { 0xA20, "UL Channel Mode error!" },
    { 0xA21, "UL Scrambling Mode error!" },
    { 0xA22, "UL Scrambling Code out of range!" },
    { 0xA23, "UL DPCCH Slot Format out of range!" },
    { 0xA24, "UL DPCCH TFCI out of range!" },
    { 0xA25, "UL DPCCH TPC Data Source out of range!" },
    { 0xA26, "UL DPCCH Power parameter out of range!" },
    { 0xA27, "UL DPDCH state error!" },
    { 0xA28, "UL DPDCH Overall Symbol Rate error!" },
    { 0xA29, "UL DPDCH Power parameter out of range!" },
    { 0xA2A, "UL DPDCH Channel Coding state error!" },
    { 0xA2B, "UL DPDCH Second Interleaver state error!" },
    { 0xA2C, "UL DPDCH DTCH or DCCH state error!" },
    { 0xA2D, "UL DPDCH DTCH or DCCH Data Type out of range!" },
    { 0xA2E, "UL DPDCH DTCH or DCCH Initialization value out of range!" },
    { 0xA2F, "UL DPDCH DTCH or DCCH Transport Time Interval error!" },
    { 0xA30, "UL DPDCH DTCH or DCCH Transport Blocks out of range!" },
    { 0xA31, "UL DPDCH DTCH or DCCH Transport Block Size out of range!" },
    { 0xA32, "UL DPDCH DTCH or DCCH Crc value error!" },
    { 0xA33, "UL DPDCH DTCH or DCCH Rate Matching Attribute out of range!" },
    { 0xA34, "UL DPDCH DTCH or DCCH Error Protection out of range!" },
    { 0xA35, "UL DPDCH DTCH or DCCH First Interleaver state error!" },
    { 0xA36, "UL DPDCH Data Type out of range!" },
    { 0xA37, "UL DPDCH Initialization value out of range!" },
    { 0xA38, "UL HSDPCCH state error!" },
    { 0xA39, "UL EDPCCH state error!" },
    { 0xA3A, "UL EDPDCH state error!" },

    /* WCDMA DL : 0xA80-AFF*/
    { 0xA80, "DL Scrambling Code out of range!" },
    { 0xA81, "DL OCNS Mode State error!" },
    { 0xA82, "DL OCNS Mode error!" },
    { 0xA83, "DL Number Of DPCH out of range!" },
    { 0xA84, "DL PCPICH state error!" },
    { 0xA85, "DL PCPICH Symbol Rate error!" },
    { 0xA86, "DL PCPICH Channelization Code error!" },
    { 0xA87, "DL PCPICH Power parameter out of range!" },
    { 0xA88, "DL PSCH state error!" },
    { 0xA89, "DL PSCH Symbol Rate error!" },
    { 0xA8A, "DL PSCH Power parameter out of range!" },
    { 0xA8B, "DL SSCH state error!" },
    { 0xA8C, "DL SSCH Symbol Rate error!" },
    { 0xA8D, "DL SSCH Power parameter out of range!" },
    { 0xA8E, "DL PCCPCH state error!" },
    { 0xA8F, "DL PCCPCH Symbol Rate error!" },
    { 0xA90, "DL PCCPCH Channelization Code error!" },
    { 0xA91, "DL PCCPCH Power parameter out of range!" },
    { 0xA92, "DL PCCPCH Data Type out of range!" },
    { 0xA93, "DL PCCPCH Initialization value out of range!" },
    { 0xA94, "DL SCCPCH state error!" },
    { 0xA95, "DL SCCPCH Slot Format out of range!" },
    { 0xA96, "DL SCCPCH Symbol Rate error!" },
    { 0xA97, "DL SCCPCH Channelization Code out of range!" },
    { 0xA98, "DL SCCPCH Power parameter out of range!" },
    { 0xA99, "DL SCCPCH Timing Offset out of range!" },
    { 0xA9A, "DL SCCPCH Data Type out of range!" },
    { 0xA9B, "DL SCCPCH Initialization value out of range!" },

    /* DL DPCH : 0xAB0-AFF */
    { 0xAB0, "DL DPCH state error!" },
    { 0xAB1, "DL DPCH Slot Format out of range!" },
    { 0xAB2, "DL DPCH Symbol Rate error!" },
    { 0xAB3, "DL DPCH Channelization Code out of range!" },
    { 0xAB4, "DL DPCH Power parameter out of range!" },
    { 0xAB5, "DL DPCH Timing Offset out of range!" },
    { 0xAB6, "DL DPCH TPC Data Type out of range!" },
    { 0xAB7, "DL DPCH Channel Coding state error!" },
    { 0xAB8, "DL DPCH Channel Second Interleaver state error!" },
    { 0xAB9, "DL DPCH DTCH or DCCH state error!" },
    { 0xABA, "DL DPCH DTCH or DCCH Data Type out of range!" },
    { 0xABB, "DL DPCH DTCH or DCCH Initialization value out of range!" },
    { 0xABC, "DL DPCH DTCH or DCCH Transport Time Interval error!" },
    { 0xABD, "DL DPCH DTCH or DCCH Transport Blocks out of range!" },
    { 0xABE, "DL DPCH DTCH or DCCH Transport Block Size out of range!" },
    { 0xABF, "DL DPCH DTCH or DCCH Crc value error!" },
    { 0xAC0, "DL DPCH DTCH or DCCH Rate Matching Attribute out of range!" },
    { 0xAC1, "DL DPCH DTCH or DCCH Error Protection out of range!" },
    { 0xAC2, "DL DPCH DTCH or DCCH First Interleaver state error!" },
    { 0xAC3, "DL DPCH Data Type out of range!" },
    { 0xAC4, "DL DPCH Initialization value out of range!" },

     /* VSA UL : 0xB00-BFF */
    { 0xB00, "UL Channel Type error!" },
    { 0xB01, "UL DPDCH Available parameter error!" },
    { 0xB02, "UL Measurement Length out of range!" },
    { 0xB03, "UL Synchronisation Index out of range!" },
    { 0xB04, "UL Slot Number out of range!" },
    { 0xB05, "UL CDP Spreading Factor parameter error!" },

    /* 0xB00-BFF, CDMA2000 parameter error */
    /* 0xC00-CFF, TDSCDMA parameter error */

    /* #8 0xD00-DFF, 2G parameter error */
    { 0xD00, "Sequence Mode error!" },
    { 0xD01, "Symbol Rate Mode error!" },
    { 0xD02, "Sequence Length out of range!" },
    { 0xD03, "GMSK Filter Parameter out of range!" },
    { 0xD04, "Ramp Time out of range!" },
    { 0xD05, "Rise Delay out of range!" },
    { 0xD06, "Fall Delay out of range!" },
    { 0xD07, "Burst Type out of range!" },
    { 0xD08, "Slot Level state error!" },
    { 0xD09, "Slot Attenuation out of range!" },
    { 0xD0A, "Slot Repition state error!" },
    { 0xD0B, "Repition Slot Number out of range!" },
    { 0xD0C, "Data Type out of range!" },
    { 0xD0D, "Modulate out of range!" },
    { 0xD0E, "Rate Type out of range!" },
    { 0xD0F, "Flag To Data state error!" },
    { 0xD10, "Flag Value state error!" },
    { 0xD11, "TSC Set state error!" },
    { 0xD12, "TSC out of range!" },
    { 0xD13, "Sync state error!" },
    { 0xD14, "ESTC state error!" },
    { 0xD15, "Fixed state error!" },
    { 0xD20, "Slot Time Mode state error!" },
    /* GSM VSA Parameter Error */
    { 0xD50, "Measure slot offset error!" },
    { 0xD51, "Measure number of slots error!" },
    { 0xD52, "Measure slot error!" },
    { 0xD53, "PvT Filter error!" },
    { 0xD54, "Spectrum modulation offset state error!" },
    { 0xD55, "Spectrum modulation frequency offset error!" },
    { 0xD56, "Spectrum switching offset state error!" },
    { 0xD57, "Spectrum switching frequency offset error!" },
    /* 0xD80-0xDFF VSA Limit Parameter */
    { 0xD80, "Limit mode error" },
    { 0xD81, "EVM RMS limit value error" },
    { 0xD82, "EVM RMS limit current state error" },
    { 0xD83, "EVM RMS limit average state error" },
    { 0xD84, "EVM RMS limit max state error" },
    { 0xD85, "EVM Peak limit value error" },
    { 0xD86, "EVM Peak limit current state error" },
    { 0xD87, "EVM Peak limit average state error" },
    { 0xD88, "EVM Peak limit max state error" },
    { 0xD89, "EVM 95% limit value error" },
    { 0xD8A, "EVM 95% limit average state error" },
    { 0xD8B, "MErr RMS limit value error" },
    { 0xD8C, "MErr RMS limit current state error" },
    { 0xD8D, "MErr RMS limit average state error" },
    { 0xD8E, "MErr RMS limit max state error" },
    { 0xD8F, "MErr Peak limit value error" },
    { 0xD90, "MErr Peak limit current state error" },
    { 0xD91, "MErr Peak limit average state error" },
    { 0xD92, "MErr Peak limit max state error" },
    { 0xD93, "MErr 95% limit value error" },
    { 0xD94, "MErr 95% limit average state error" },
    { 0xD95, "PhErr RMS limit value error" },
    { 0xD96, "PhErr RMS limit current state error" },
    { 0xD97, "PhErr RMS limit average state error" },
    { 0xD98, "PhErr RMS limit max state error" },
    { 0xD99, "PhErr Peak limit value error" },
    { 0xD9A, "PhErr Peak limit current state error" },
    { 0xD9B, "PhErr Peak limit average state error" },
    { 0xD9C, "PhErr Peak limit max state error" },
    { 0xD9D, "PhErr 95% limit value error" },
    { 0xD9E, "PhErr 95% limit average state error" },
    { 0xD9F, "IQ offset limit value error" },
    { 0xDA0, "IQ offset limit current state error" },
    { 0xDA1, "IQ offset limit average state error" },
    { 0xDA2, "IQ offset limit max state error" },
    { 0xDA3, "IQ imbalance limit value error" },
    { 0xDA4, "IQ imbalance limit current state error" },
    { 0xDA5, "IQ imbalance limit average state error" },
    { 0xDA6, "IQ imbalance limit max state error" },
    { 0xDA7, "Frequency error limit value error" },
    { 0xDA8, "Frequency error limit current state error" },
    { 0xDA9, "Frequency error limit average state error" },
    { 0xDAA, "Frequency error limit max state error" },
    { 0xDAB, "Timing error limit value error" },
    { 0xDAC, "Timing error limit current state error" },
    { 0xDAD, "Timing error limit average state error" },
    { 0xDAE, "Timing error limit max state error" },
    { 0xDAF, "PVT Avg. Burst Pwr. state error" },
    { 0xDB0, "PVT Avg. Burst Pwr. from PCL error" },
    { 0xDB1, "PVT Avg. Burst Pwr. to PCL error" },
    { 0xDB2, "PVT Avg. Burst Pwr. lower error" },
    { 0xDB3, "PVT Avg. Burst Pwr. upper error" },
    { 0xDB4, "PVT guard period state error" },
    { 0xDB5, "PVT guard period limit error" },
    { 0xDB6, "PVT upper template rising edge state error" },
    { 0xDB7, "PVT upper template rising edge static start time error" },
    { 0xDB8, "PVT upper template rising edge static start Level Rel. error" },
    { 0xDB9, "PVT upper template rising edge static start Level Abs. State error" },
    { 0xDBA, "PVT upper template rising edge static start Level Abs. Limit error" },
    { 0xDBB, "PVT upper template rising edge static stop time error" },
    { 0xDBC, "PVT upper template rising edge static stop Level Rel. error" },
    { 0xDBD, "PVT upper template rising edge static stop Level Abs. State error" },
    { 0xDBE, "PVT upper template rising edge static stop Level Abs. Limit error" },
    { 0xDBF, "PVT upper template rising edge dynamic state error" },
    { 0xDC0, "PVT upper template rising edge dynamic start PCL error" },
    { 0xDC1, "PVT upper template rising edge dynamic end PCL error" },
    { 0xDC2, "PVT upper template rising edge dynamic correction error" },
    { 0xDC3, "PVT upper template useful part state error" },
    { 0xDC4, "PVT upper template useful part static start time error" },
    { 0xDC5, "PVT upper template useful part static start Level Rel. error" },
    { 0xDC6, "PVT upper template useful part static start Level Abs. State error" },
    { 0xDC7, "PVT upper template useful part static start Level Abs. Limit error" },
    { 0xDC8, "PVT upper template useful part static stop time error" },
    { 0xDC9, "PVT upper template useful part static stop Level Rel. error" },
    { 0xDCA, "PVT upper template useful part static stop Level Abs. State error" },
    { 0xDCB, "PVT upper template useful part static stop Level Abs. Limit error" },
    { 0xDCC, "PVT upper template useful part dynamic state error" },
    { 0xDCD, "PVT upper template useful part dynamic start PCL error" },
    { 0xDCE, "PVT upper template useful part dynamic end PCL error" },
    { 0xDCF, "PVT upper template useful part dynamic correction error" },
    { 0xDD0, "PVT upper template falling edge state error" },
    { 0xDD1, "PVT upper template falling edge static start time error" },
    { 0xDD2, "PVT upper template falling edge static start Level Rel. error" },
    { 0xDD3, "PVT upper template falling edge static start Level Abs. State error" },
    { 0xDD4, "PVT upper template falling edge static start Level Abs. Limit error" },
    { 0xDD5, "PVT upper template falling edge static stop time error" },
    { 0xDD6, "PVT upper template falling edge static stop Level Rel. error" },
    { 0xDD7, "PVT upper template falling edge static stop Level Abs. State error" },
    { 0xDD8, "PVT upper template falling edge static stop Level Abs. Limit error" },
    { 0xDD9, "PVT upper template falling edge dynamic state error" },
    { 0xDDA, "PVT upper template falling edge dynamic start PCL error" },
    { 0xDDB, "PVT upper template falling edge dynamic end PCL error" },
    { 0xDDC, "PVT upper template falling edge dynamic correction error" },
    { 0xDDD, "PVT lower template useful part state error" },
    { 0xDDE, "PVT lower template useful part static start time error" },
    { 0xDDF, "PVT lower template useful part static start Level Rel. error" },
    { 0xDE0, "PVT lower template useful part static start Level Abs. State error" },
    { 0xDE1, "PVT lower template useful part static start Level Abs. Limit error" },
    { 0xDE2, "PVT lower template useful part static stop time error" },
    { 0xDE3, "PVT lower template useful part static stop Level Rel. error" },
    { 0xDE4, "PVT lower template useful part static stop Level Abs. State error" },
    { 0xDE5, "PVT lower template useful part static stop Level Abs. Limit error" },
    { 0xDE6, "PVT lower template useful part dynamic state error" },
    { 0xDE7, "PVT lower template useful part dynamic start PCL error" },
    { 0xDE8, "PVT lower template useful part dynamic end PCL error" },
    { 0xDE9, "PVT lower template useful part dynamic correction error" },
    { 0xDEA, "Spectrum modulation reference power low power error" },
    { 0xDEB, "Spectrum modulation reference power high power error" },
    { 0xDEC, "Spectrum modulation Frequency Offset state error" },
    { 0xDED, "Spectrum modulation Frequency Offset low power Rel error" },
    { 0xDEE, "Spectrum modulation Frequency Offset high power Rel error" },
    { 0xDEF, "Spectrum modulation Frequency Offset power Abs error" },
    { 0xDF0, "Spectrum Switching reference power state error" },
    { 0xDF1, "Spectrum Switching reference power power level error" },
    { 0xDF2, "Spectrum Switching Frequency Offset state error" },
    { 0xDF3, "Spectrum Switching Frequency Offset relative power error" },

    /* #9 0xE00-EFF, LTE V2X parameter error */
    { 0xE00, "LTE-V2X or NR-V2X version error!" },
    { 0xE01, "R14 or R15 version error!" },
    { 0xE02, "Channel bandwidth out of range!" },
    { 0xE03, "Duplexing out of range!" },
    { 0xE04, "Uplink-downlink configurations out of range in TDD mode!" },
    { 0xE05, "Number of SCI out of range!" },
    { 0xE06, "V2X Communication mode error!" },
    { 0xE07, "PN data type out of range!" },
    { 0xE08, "PN data initialization value out of range!" },
    { 0xE09, "OCNG Flag error!" },
    { 0xE0A, "OCNG modulation out of range!" },
    { 0xE0B, "OCNG PN data initialization value out of range!" },
    { 0xE0C, "OCNG PN data type out of range!" },
    { 0xE0D, "GenSequenceLen out of range!" },

    /* resource pool and sync info error message */
    { 0xE10, "bitmap length error!" },
    { 0xE11, "bitmap value error or bitmap is all zero!" },
    { 0xE12, "SubFrmOffset is not zero!" },
    { 0xE13, "pscch and pssch adjacent is on!" },
    { 0xE14, "SubChanNum out of range!" },
    { 0xE15, "SubChanSize out of range!" },
    { 0xE16, "ResPool StartRb out of range!" },
    { 0xE17, "SyncState error!" },
    { 0xE18, "InCoverage error!" },
    { 0xE19, "SSId out of range!" },
    { 0xE1A, "Sync subfrm offset index out of range!" },
    { 0xE1B, "PSBCH Modulate is not QPSK!" },
    { 0xE1C, "PSBCH Scramble state error!" },
    { 0xE1D, "PSBCH Power out of range!" },

    /* Alg_SL_LteSubfrm error message */
    { 0xE20, "PSCCH State error!" },
    { 0xE21, "PSSCH State error!" },
    { 0xE22, "number of SCI don't match with PSCCH/PSSCH!" },
    { 0xE23, "StartSubfrmTab must be sorted!" },
    { 0xE24, "TxMode out of range!" },
    { 0xE25, "Start SF out of range!" },
    { 0xE26, "Priority out of range!" },
    { 0xE27, "ResReservation error!" },
    { 0xE28, "RetransIdx error!" },
    { 0xE29, "FreqResLocation(riv) out of range!" },
    { 0xE2A, "SubChan error!" },
    { 0xE2B, "TimeGap error!" },
    { 0xE2C, "MCS out of range!" },
    { 0xE2D, "TxFormat out of range!" },
    { 0xE2E, "PSCCH Modulate is not QPSK!" },
    { 0xE2F, "PSCCH CyclicShiftField out of range!" },
    { 0xE30, "PSCCH Scramble state error!" },
    { 0xE31, "PSCCH Power out of range!" },
    { 0xE32, "PSSCH Modulate out of range!" },
    { 0xE33, "PSSCH GroupHop error!" },
    { 0xE34, "PSSCH Scramble state error!" },
    { 0xE35, "PSSCH ChanCoding state error!" },
    { 0xE36, "PSSCH Power out of range!" },
    { 0xE37, "PSSCH modulation and coding scheme error!" },
    
    /* 0xE80-EB0, VSA parameter error */
    { 0xE8A, "Measure physical channel type error!" },
    { 0xE8B, "SL LTE PSSCH subframe RB number error!" },
    { 0xE8C, "SL LTE PSSCH subframe RB offset error!" },
    { 0xE8D, "SL LTE PSSCH and PSCCH RB offset error!" },

    /* 0xEB0-ECF, LTE V2X Measure error */
    { 0xEB0, "Statistical length for the measurement of modulation results in slots error!" },
    { 0xEB1, "Enable/disable switch error for the measurement of modulation results!" },
    { 0xEB2, "Enable/disable switch error for the measurement of EVM!" },
    { 0xEB3, "Enable/disable switch error for the measurement of magnitude error!" },
    { 0xEB4, "Enable/disable switch error for the measurement of phase error!" },
    { 0xEB5, "Enable/disable switch error for the measurement of inband emissions!" },
    { 0xEB6, "Enable/disable switch error for the measurement of equalizer spectrum flatness!" },

    { 0xEB7, "Statistical length for the measurement of spectrum emission results!" },
    { 0xEB8, "Enable/disable switch error for the measurement of spectrum emission results!" },
    { 0xEB9, "Enable/disable switch error for the measurement of occupied bandwidth!" },
    { 0xEBA, "Enable/disable switch error for the measurement of spectrum emission trace and margin results!" },

    { 0xEBB, "Statistical length for the measurement of ACLR results!" },
    { 0xEBC, "Enable/disable switch error for the measurement of ACLR results!" },
    { 0xEBD, "Enable/disable switch error for the evaluation of first adjacent UTRA channels!" },
    { 0xEBE, "Enable/disable switch error for the evaluation of second adjacent UTRA channels!" },
    { 0xEBF, "Enable/disable switch error for the evaluation of adjacent E-UTRA channels!" },

    { 0xEC0, "Enable/disable switch error for the measurement of power monitor results !" },

    { 0xEC1, "Statistical length for the measurement of the total TX power!" },
    { 0xEC2, "Enable/disable switch error for the measurement of the total TX power!" },


    /* #10 0xF00-FFF, ATE error message */
    { 0xF00, "ATE algorithm library initialization failure!" },
    { 0xF01, "ATE testcase ID out of range!" },
};

#endif /* ALG_3GPP_ERRDEF_H_ */

