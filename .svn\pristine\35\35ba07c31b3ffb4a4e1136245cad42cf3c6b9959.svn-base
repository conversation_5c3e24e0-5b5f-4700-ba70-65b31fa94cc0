#include "SCPI_Export.h"
#include "SCPI_IO.h"
#include "SCPI_IO_Manger.h"
#include <WinSock2.h>
#include "wterrorAll.h"
#include "Logger.h"
#include "Usual.h"

#pragma comment(lib,"WT.Tester.API.Common.lib")
#define STR_SCPI_WRITE      "SCPI write"
#define STR_SCPI_READ      "SCPI read"

static int WriteFile(const char *fileName, char *buf, int len)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    FILE *fp = fopen(fileName, "wb");
    if (fp)
    {
        fwrite(buf, 1, len, fp);
        fclose(fp);
        iRet = WT_ERR_CODE_OK;
    }
    return iRet;
}

WTTESTERSCPI_API int WT_SCPI_Connect(const char * IP, int port, unsigned int timeout)
{
    unique_ptr<scpi_io> io(new scpi_io(IP, port, timeout));
    int ID = io->Connect();
    if (INVALID_SOCKET != ID)
    {
        Logger::PrintDebug(__FUNCTION__, __LINE__, "Connect ok, ID = %d\r\n", ID);
        SCPI_IO_Manger::Instance().AddIO(ID, io);
    }
    return ID;
}

WTTESTERSCPI_API int WT_SCPI_GetDetail(int ID, char *data, unsigned int *len)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->GetIODetail(data, len);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_NetStatus(int ID)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->IOStatus();
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_DisConnect(int ID)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        Logger::PrintDebug(__FUNCTION__, __LINE__, "DisConnect ok, ID = %d\r\n", ID);
        return io->DisConnect();
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_Write(int ID, const char * data, unsigned int dataLength, unsigned int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        io->SetTimeOut(timeout);
        Logger::PrintDebug(STR_SCPI_WRITE, __LINE__, "ID = %d ==>> %s\r\n", ID, data);
        return io->Write(data, dataLength);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_ReadLine(int ID, char * buffer, unsigned int bufferSize, int *recvBytes, int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        memset(buffer, 0, bufferSize);
        int len = 0;
        int iRet = io->ReadLine(buffer, bufferSize - 1, len, timeout);
        if (len)
        {
            if (recvBytes)
            {
                *recvBytes = len;
            }
            Logger::PrintDebug(STR_SCPI_READ, __LINE__, "ID = %d <<== %s\r\n", ID, buffer);
        }
        return iRet;
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_SendARB(int ID, const char *pre_cmd, const char *data, unsigned int dataSize, int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        io->SetTimeOut(timeout);

        stringstream cmd;
        std::string str_debug("");
        cmd << pre_cmd;
        int iRet = io->SendArbBlock(cmd, (char *)data, dataSize, str_debug);
        if (str_debug.length() > 0)
        {
            Logger::PrintDebug(STR_SCPI_WRITE, __LINE__, "ID = %d ==>> %s\r\n", ID, str_debug.c_str());
        }
        return iRet;
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_SendARBFile(int ID, const char *pre_cmd, const char *fileName, int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        io->SetTimeOut(timeout);

        stringstream cmd;
        std::string str_debug("");
        cmd << pre_cmd;
        
        int iRet = io->SendArbBlock(cmd, fileName, str_debug);
        if (str_debug.length() > 0)
        {
            Logger::PrintDebug(STR_SCPI_WRITE, __LINE__, "ID = %d ==>> %s\r\n", ID, str_debug.c_str());
        }
        return iRet;
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_ReadARB(int ID, char *buffer, unsigned int bufferSize, int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        std::stringstream str_debug;
        int iRet = io->ReadArbBlock(buffer, bufferSize, str_debug, timeout);
        if (str_debug.str().length() > 0)
        {
            Logger::PrintDebug(STR_SCPI_READ, __LINE__, "ID = %d <<== %s\r\n", ID, str_debug.str().c_str());
        }
        return iRet;
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_GetARBDataSize(int ID, unsigned int *ARBSize, int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        std::stringstream str_debug;
        int iRet = io->GetArbBlockSize(ARBSize, str_debug, timeout);
        if (str_debug.str().length() > 0)
        {
            Logger::PrintDebug(STR_SCPI_READ, __LINE__, "ID = %d <<== %s\r\n", ID, str_debug.str().c_str());
        }
        return iRet;
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_GetARBData(int ID, char *buffer, unsigned int bufferSize, unsigned int datasize, int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        std::stringstream str_debug;
        int iRet = io->GetArbBlockData(buffer, bufferSize, datasize, str_debug, timeout);
        if (str_debug.str().length() > 0)
        {
            Logger::PrintDebug(STR_SCPI_READ, __LINE__, "ID = %d <<== %s\r\n", ID, str_debug.str().c_str());
        }
        return iRet;
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_CmdIsOK(int ID, char *cmd, unsigned int cmd_len, int expectRespCnt, int timeout)
{
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_CleanIOForce(int ID)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->CleanIOForce();
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int WT_SCPI_SaveWaveForm(const char *fileName, const char *data, const unsigned int data_len)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    int FileCnt = 0;
    int Offset = 0;
    char *Ptr = (char *)data;

    using FileSegmentInfo = struct
    {
        int Len;
        char extName[32];
    };
    std::string preFileName(fileName);
    size_t pos = preFileName.find_last_of(".");
    if (pos != string::npos)
    {
        preFileName = preFileName.substr(0, pos + 1);
    }
    memcpy(&FileCnt, Ptr + Offset, sizeof(int));
    Offset += sizeof(int);

    std::vector<FileSegmentInfo> fileInfo(FileCnt);

    for (int i = 0; i < FileCnt; i++)
    {
        memcpy(&fileInfo[i], Ptr + Offset, sizeof(FileSegmentInfo));
        Offset += sizeof(FileSegmentInfo);

        std::string tmpFileName = preFileName + fileInfo[i].extName;
        iRet = WriteFile(tmpFileName.c_str(), Ptr + Offset, fileInfo[i].Len);
        if (iRet)
        {
            break;
        }
        Offset += fileInfo[i].Len;

        std::stringstream msg;
        msg << "[" << i << "] file size = " << fileInfo[i].Len << ", file Name = " << tmpFileName << ", extension name = " << fileInfo[i].extName << std::endl;
        Logger::PrintDebug(STR_SCPI_READ, __LINE__, "<<== %s\r\n", msg.str().c_str());
    }

    return iRet;
}

//////////////////////////////////////////////
/////////////////////////////////////////////
#pragma region VISA_FUNCTION
WTTESTERSCPI_API int Initialize(void)
{
    return WT_ERR_CODE_OK;
}

WTTESTERSCPI_API int Terminate(void)
{
    return WT_ERR_CODE_OK;
}

WTTESTERSCPI_API int QueryTester(const char *IP, int port, const char *queryCmd, char *buffer, unsigned int bufff_size, unsigned int timeout)
{
    int iRet = WT_ERR_CODE_CONNECT_FAIL;
    int recvBytes = 0;
    int ID = INVALID_SOCKET;
    do
    {
        ID = WT_SCPI_Connect(IP, port, timeout);
        if (ID < 0)
        {
            break;
        }

        int Len = WT_SCPI_Write(ID, queryCmd, strlen(queryCmd), timeout);
        if (Len <= 0)
        {
            break;
        }


        iRet = WT_SCPI_ReadLine(ID, buffer, bufff_size, &recvBytes, timeout);
        if (recvBytes <= 0)
        {
            break;
        }

    } while (0);

    if (INVALID_SOCKET != ID)
    {
        WT_SCPI_DisConnect(ID);
    }
    return iRet;
}

WTTESTERSCPI_API int Connect(const char *IP, int port, unsigned int timeout)
{
    return WT_SCPI_Connect(IP, port, timeout);
}

WTTESTERSCPI_API int DisConnect(int ID)
{
    return WT_SCPI_DisConnect(ID);
}

static bool isASCII(const char *s)
{
    int len = strlen(s);
    for (int i = 0; i < len; i++)
    {
        if (static_cast<unsigned char>(s[i]) > 127)
        {
            return false;
        }
    }
    return true;
}

WTTESTERSCPI_API int Write(int ID, const char *data, unsigned int dataLength, unsigned int timeout)
{
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    do
    {
        if (!isASCII(data))
        {
            break;
        }

        iRet = WT_SCPI_Write(ID, data, dataLength, timeout);
        if (iRet <= 0)
        {
            break;
        }

        iRet = WT_ERR_CODE_OK;

    } while (0);
    
    return iRet;
}

WTTESTERSCPI_API int WriteLine(int ID, const char *data, unsigned int dataLength, unsigned int timeout)
{
    int iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
    do
    {
        if (!isASCII(data))
        {
            break;
        }

        iRet = WT_SCPI_Write(ID, data, dataLength, timeout);
        if (iRet <= 0)
        {
            break;
        }

        WT_SCPI_Write(ID, "\n", 1, timeout);

        iRet = WT_ERR_CODE_OK;

    } while (0);

    return iRet;
}

WTTESTERSCPI_API int WriteArbitrary(int ID, const char *data, unsigned int dataLength, unsigned int timeout)
{
    return WT_SCPI_Write(ID, data, dataLength, timeout);
}

WTTESTERSCPI_API int WriteFromFile(int ID, const char *fileName, unsigned int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        io->SetTimeOut(timeout);
        return io->SendFile(fileName);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int Read(int ID, char *buffer, unsigned int dataLength, unsigned int *recvBytes, unsigned int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->Read(buffer, dataLength, *recvBytes, timeout);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int ReadLine(int ID, char *buffer, unsigned int bufferSize, unsigned int *recvBytes, int timeout)
{
    return WT_SCPI_ReadLine(ID, buffer, bufferSize, (int*)recvBytes, timeout);
}

WTTESTERSCPI_API int ReadArbitrary(int ID, char *buffer, unsigned int dataLength, unsigned int *recvBytes, unsigned int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->Read(buffer, dataLength, *recvBytes, timeout, true);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int ReadToFile(int ID, const char *fileName, unsigned int byteCount, unsigned int *retCount, unsigned int timeout)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->ReadToFile(fileName, byteCount, *retCount, timeout);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int CleanBuffer(int ID)
{
    return WT_SCPI_CleanIOForce(ID);
}

WTTESTERSCPI_API int GetError(int ID, int *error)
{
    scpi_io *io = SCPI_IO_Manger::Instance().GetIO(ID);
    if (io)
    {
        return io->GetIOErrorCode(error);
    }
    return WT_ERR_CODE_CONNECT_FAIL;
}

WTTESTERSCPI_API int ConnStatus(int ID, int *status)
{
    *status = WT_SCPI_NetStatus(ID);
    return WT_ERR_CODE_OK;
}

#pragma endregion VISA_FUNCTION