//*****************************************************************************
//  File: main.cpp
//  WT-Scpi-Server程序入口文件，调用各模块初始化，创建主循环
//  Data: 2019.03.10
//*****************************************************************************
#include <signal.h>
#include <unistd.h>
#include <cstdlib>
#include <cstdio>
#include <cassert>
#include <iostream>
#include <execinfo.h>
#include "wtev++.h"
#include "scpi/scpi.h"
#include "tester.h"
#include "scpiapiwrapper/scpiutils.h"
#include "scpiapiwrapper/wrapper.h"
#include "scpi_service.h"
#include "scpi_socket.h"
#include "conf.h"
#include "wterror.h"
#include "wtlog.h"
#include "scpi_config.h"
#include "scpi_conninfo.h"

using namespace std;

#ifdef DEBUG
//保证程序CTRL+C退出时能正常记录coverage信息
extern "C" void __gcov_flush(void);
static void SignalCb(struct ev_loop *loop, struct ev_signal *w, int revents)
{
    (void)w;
    (void)revents;
    ev_break(loop);
    #ifdef COVERAGE
    __gcov_flush();
    #endif
}
#endif

/*
  struct siginfo_t
  {
   int si_signo;
   int si_errno;
   int si_code;
   int si_trapno;
   pid_t si_pid;
   uid_t si_uid;
   void* si_addr;
   int si_status;
   ......
  };
*/

//打印异常调用栈信息
static void Backtrace(int Sig, siginfo_t *Info, void *Arg)
{
    (void)Info;
    (void)Arg;
    if (Sig == SIGPIPE)
    {
        return;
    }

    printf("Backtrace sig:%d, errno:%d, code:%d, tid:%u\n",
           Info->si_signo, Info->si_errno, Info->si_code, gettid());

    void *Addrlist[50];
    size_t AddrLen;

    AddrLen = backtrace(Addrlist, 50);
    backtrace_symbols_fd(Addrlist, AddrLen, STDOUT_FILENO);

    _exit(0);
}

//注册异常回掉
static void RegExcpHandler(void)
{
    struct sigaction Action;
    Action.sa_sigaction = Backtrace;
    sigemptyset(&Action.sa_mask);

    Action.sa_flags = SA_RESTART | SA_SIGINFO;
    sigaction(SIGSEGV, &Action, NULL); //无效的内存引用
    sigaction(SIGABRT, &Action, NULL); //由ablort发出的退出指令
    sigaction(SIGPIPE, &Action, NULL); //管道破裂，写一个没有读端的管道
    sigaction(SIGFPE, &Action, NULL);  //错误的算术运算，如除以零
    sigaction(SIGBUS, &Action, NULL);  //总现错误，错误的内存访问
    sigaction(SIGILL, &Action, NULL);  //非法指令
}


// 通过定时往eventfd中写入数据上报进程心跳
static void HeartBeat(wtev::timer &watcher, int revents)
{
    (void)revents;
    //获取不到锁时不上报心跳，避免死锁
    if (ScpiConnInfo::Instance().GetLockStatus())
    {
        int Fd = (int)(long)watcher.data;
        uint64_t Val = 1;
        ssize_t s = write(Fd, &Val, sizeof(uint64_t));

        if (s != sizeof(uint64_t))
        {
            WTLog::Instance().LOGERR(WT_ERROR, "SCPI Server eventfd write error");
        }
    }
}

int main(int argc, char *argv[])
{
    RegExcpHandler();

#ifdef DEBUG
    cout << "SPCI-Server arg num " << argc << ": ";
    for (int i = 1; i < argc; i++)
    {
        cout << argv[i] << " ";
    }
    cout << endl;
#endif
    WTFileSecure::SetDynamicPasswd(DynamicPasswd::GetDynamicPasswd());

    // 日志类初始化
    WTLog::SetLogName(WTConf::GetDir() + "/scpi_server.db");
    WTLog::SetFiFoName(WTConf::GetDir() + "/fifo.scpi_server");
    WTLog::SetLogPreName("WT_SCPI_SERVER");
    WTLog::Instance();
    WTLog::Instance().SetLogPrintEnalbe((2 << LOG_TYPE_END) - 1); //日志输出都打开

    wtev::default_loop Loop;

#ifdef DEBUG
    wtev::sig SigEv(Loop);
    SigEv.set_(NULL, SignalCb);
    SigEv.start(SIGINT);
#endif
    int Ret = WT_OK;

    WTScpiSocket srvNor(Loop, ScpiConfig::Instance().NormalUserNum);
    Ret = srvNor.StartSocketSrv(ScpiConfig::Instance().NormalLinkPort); //启动连接服务
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "NormalLink Server start error!");
        exit(0);
    }

    WTScpiSocket srvAd(Loop, ScpiConfig::Instance().ManagerUserNum);
    Ret = srvAd.StartSocketSrv(ScpiConfig::Instance().ManagerLinkPort);
    cout << "Create SCPI Manager link Ret = " << Ret << endl;
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "ManagerLink Server start error!");
        exit(0);
    }

    WTScpiSocket srvQue(Loop, ScpiConfig::Instance().QueryUserNum);
    Ret = srvQue.StartSocketSrv(ScpiConfig::Instance().QueryLinkPort);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "QueryLink Server start error!");
        exit(0);
    }

    WTScpiSocket srvInn(Loop, ScpiConfig::Instance().GetInnerUserNum());
    Ret = srvInn.StartSocketSrv(ScpiConfig::Instance().InnerLinkPort);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "InnerLink Server start error!");
        exit(0);
    }

    //注册心跳定时器，每0.9秒上报一次
    wtev::timer ReportEv(Loop);
    ReportEv.set<HeartBeat>((void *)(long)atoi(argv[1]));
    ReportEv.start(int(1), 0.9);

    const unsigned int maxSampleCount = MAX_SMAPLE_RATE_API * MAX_SAMPLEING_TIME;
    WT_DLLInitialize_V2(0, maxSampleCount);
    Loop.run();
    WT_DLLTerminate();

    return 0;
}
