#ifndef SCPI_3GPP_BASE_H_
#define SCPI_3GPP_BASE_H_
#include "../../extlib/include/alg/alg_3gpp_apidef.h"
// 参考文件Header, 需要与API中的数据同步
enum WaveCfgHeadEnum
{
    WAVE_CFG_BASE_HEAD = 0x11223344,
    WAVE_CFG_SUB_TB = 0x11223301,
    WAVE_CFG_SUB_TB_MUMIMO = 0x11223302,
    WAVE_CFG_SUB_EHT_TB = 0x11223303,
    WAVE_CFG_SUB_LTE = 0x11223350,
    WAVE_CFG_SUB_NR = 0x11223351,
    WAVE_CFG_SUB_NB_IOT = 0x11223352,
    WAVE_CFG_SUB_WCDMA = 0x11223353,
};

#define BAND_CHANNEL_ITEM_MAX 100
typedef struct {
    int Band;
    int ChannelMin;
    int ChannelMax;
    int Delta; // Delta = FreqMin * 10 - ChannelMin; Freq = (Channel + Delta) / 10 MHz
} BandChanItem;

// 蜂窝 不对外 基础数据定义
extern int pusch_pb_1_4[6];
extern int pusch_pb_3[11];
extern int pusch_pb_5[16];
extern int pusch_pb_10[24];
extern int pusch_pb_15[29];
extern int pusch_pb_20[34];

// PDSCH RB Number
#define PDSCH_PB_MIN        1
#define PDSCH_PB_1_4_MAX    6
#define PDSCH_PB_3_MAX      15
#define PDSCH_PB_5_MAX      25
#define PDSCH_PB_10_MAX     50
#define PDSCH_PB_15_MAX     75
#define PDSCH_PB_20_MAX     100

// DCI Resource Block Assignment
#define DCI_ResBlkAssign_Format1_1_4_Max 63
#define DCI_ResBlkAssign_Format1_3_Max 255
#define DCI_ResBlkAssign_Format1_5_Max 8191
#define DCI_ResBlkAssign_Format1_10_Max 131071
#define DCI_ResBlkAssign_Format1_15_Max 524287
#define DCI_ResBlkAssign_Format1_20_Max 33554431
#define DCI_ResBlkAssign_Format1A_1_4_Max 20
#define DCI_ResBlkAssign_Format1A_3_Max 119
#define DCI_ResBlkAssign_Format1A_5_Max 324
#define DCI_ResBlkAssign_Format1A_10_Max 1274
#define DCI_ResBlkAssign_Format1A_15_Max 2849
#define DCI_ResBlkAssign_Format1A_20_Max 5049
#define DCI_ResBlkAssign_Format0_1_4_Max 31
#define DCI_ResBlkAssign_Format0_3_Max 127
#define DCI_ResBlkAssign_Format0_5_Max 511
#define DCI_ResBlkAssign_Format0_10_Max 2047
#define DCI_ResBlkAssign_Format0_15_Max 4095
#define DCI_ResBlkAssign_Format0_20_Max 8191

//RBGBitmap Length
#define PDSCH_RBGBitmap_1_4_Length 6
#define PDSCH_RBGBitmap_3_Length 8
#define PDSCH_RBGBitmap_5_Length 13
#define PDSCH_RBGBitmap_10_Length 17
#define PDSCH_RBGBitmap_15_Length 19
#define PDSCH_RBGBitmap_20_Length 25
extern int rb_num_array[34];

extern int mcs_tbs_paylaod_map[29][34];
extern double DL_UE_PDSCHPA_MAP[8];
extern int UL_DL_CONFIG_MAP[7][10];
extern int UL_MODULE_MCS_MAP[2][5][2];
extern int DL_MODULE_MCS_MAP[5][9][2];
extern int NR_BWP_RB_MAP[13][3];
extern int NR_SCS_MAP[3];
extern int NR_BANDWIDTH_MAP[13];

inline bool IsAlg3GPPStandardType(const int& Type)
{
    if (ALG_3GPP_STANDARD_TYPE::ALG_3GPP_STD_5G <= Type && 
        ALG_3GPP_STANDARD_TYPE::ALG_3GPP_STD_WCDMA >= Type)
    {
        return true;
    }
    return false;
}

#endif /* SCPI_3GPP_BASE_H_ */
