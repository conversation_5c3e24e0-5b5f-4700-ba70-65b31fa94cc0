#ifndef SCPI_PARSER_H
#define	SCPI_PARSER_H

#include <string.h>
#include "scpi/types.h"

#ifdef	__cplusplus
extern "C" {
#endif
	LIBSCPI_API void SCPI_Init(scpi_t * context);

	LIBSCPI_API int SCPI_Input(scpi_t * context, const char * data, int len);
	LIBSCPI_API int SCPI_Parse(scpi_t * context, char * data, int len);


	LIBSCPI_API size_t SCPI_ResultCharacters(scpi_t * context, const char * data, size_t len);
#define SCPI_ResultMnemonic(context, data) SCPI_ResultCharacters((context), (data), strlen(data))
	LIBSCPI_API size_t SCPI_ResultInt(scpi_t * context, int32_t val);
	LIBSCPI_API size_t SCPI_ResultIntBase(scpi_t * context, int32_t val, int8_t base);
	LIBSCPI_API size_t SCPI_ResultDouble(scpi_t * context, double val);
	LIBSCPI_API size_t SCPI_ResultText(scpi_t * context, const char * data);
	LIBSCPI_API size_t SCPI_ResultArbitraryBlock(scpi_t * context, const char * data, size_t len);
	LIBSCPI_API size_t SCPI_ResultBool(scpi_t * context, scpi_bool_t val);

	LIBSCPI_API scpi_bool_t SCPI_Parameter(scpi_t * context, scpi_parameter_t * parameter, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamIsNumber(scpi_parameter_t * parameter, scpi_bool_t suffixAllowed);
	LIBSCPI_API scpi_bool_t SCPI_ParamToInt(scpi_t * context, scpi_parameter_t * parameter, int32_t * value);
	LIBSCPI_API scpi_bool_t SCPI_ParamToUnsignedInt(scpi_t * context, scpi_parameter_t * parameter, uint32_t * value);
	LIBSCPI_API scpi_bool_t SCPI_ParamToDouble(scpi_t * context, scpi_parameter_t * parameter, double * value);
	LIBSCPI_API scpi_bool_t SCPI_ParamToChoice(scpi_t * context, scpi_parameter_t * parameter, const scpi_choice_def_t * options, int32_t * value);
	LIBSCPI_API scpi_bool_t SCPI_ChoiceToName(const scpi_choice_def_t * options, int32_t tag, const char ** text);


	LIBSCPI_API scpi_bool_t SCPI_ParamInt(scpi_t * context, int32_t * value, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamUnsignedInt(scpi_t * context, uint32_t * value, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamDouble(scpi_t * context, double * value, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamCharacters(scpi_t * context, const char ** value, size_t * len, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamArbitraryBlock(scpi_t * context, const char ** value, size_t * len, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamCopyText(scpi_t * context, char * buffer, size_t buffer_len, size_t * copy_len, scpi_bool_t mandatory);

	LIBSCPI_API scpi_bool_t SCPI_ParamBool(scpi_t * context, scpi_bool_t * value, scpi_bool_t mandatory);
	LIBSCPI_API scpi_bool_t SCPI_ParamChoice(scpi_t * context, const scpi_choice_def_t * options, int32_t * value, scpi_bool_t mandatory);

	LIBSCPI_API scpi_bool_t SCPI_IsCmd(scpi_t * context, const char * cmd);
	LIBSCPI_API int32_t SCPI_CmdTag(scpi_t * context);
	LIBSCPI_API scpi_bool_t SCPI_Match(const char * pattern, const char * value, size_t len);
	LIBSCPI_API scpi_bool_t SCPI_CommandNumbers(scpi_t * context, int32_t * numbers, size_t len);

#ifdef	__cplusplus
}
#endif

#endif