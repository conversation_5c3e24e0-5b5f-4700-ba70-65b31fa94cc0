//*****************************************************************************
//  File: main.cpp
//  WT-Link程序入口文件，调用各模块初始化，创建主循环
//  Data: 2016.7.12
//*****************************************************************************
#include <signal.h>
#include <unistd.h>
#include <cstdlib>
#include <cstdio>
#include <cassert>
#include <iostream>

#include "wtev++.h"
#include "link_manager.h"
#include "sock_server.h"
#include "conf.h"
#include "wterror.h"
#include "wtlog.h"
//#include "scpiapiwrapper/scpiutils.h"
//#include "scpiapiwrapper/wrapper.h"
//#include "server/scpi_service.h"
//#include "server/scpi_socket.h"
//#include "server/scpi_config.h"
//#include "server/scpi_conninfo.h"

using namespace std;

#ifdef DEBUG
//保证程序CTRL+C退出时能正常记录coverage信息
extern "C" void __gcov_flush(void);
static void SignalCb(struct ev_loop *loop, struct ev_signal *w, int revents)
{
    (void)w;
    (void)revents;
    ev_break(loop);
    #ifdef COVERAGE
    __gcov_flush();
    #endif
}
#endif

//函数参数列表如下：argv[1] :当前目录，argv[2] : WT-Manager socketpair, argv[3-n] : WT-Server 1-n socketpair
#include "license.h"

int main(int argc, char *argv[])
{
    int Port = 0; //tcp服务监听端口
    //获取设备文件路径
    int ServerCnt = 0;

    // 日志类初始化, 注意这里没有初始化数据库
    WTLog::SetFiFoName(WTConf::GetDir() + "/fifo.link");
    WTLog::SetLogPreName("WT_LINK");
    WTLog::Instance().SetLogPrintEnalbe((2 << LOG_TYPE_END) - 1); //日志输出都打开

#ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WT-Link arg num " << argc << ": ";
    for (int i = 1; i < argc; i++)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << argv[i] << " ";
    }
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << endl;
#endif

    WTFileSecure::SetDynamicPasswd(DynamicPasswd::GetDynamicPasswd());

    wtev::default_loop Loop;

#ifdef DEBUG
    wtev::sig SigEv(Loop);
    SigEv.set_(NULL, SignalCb);
    SigEv.start(SIGINT);
#endif

    std::vector<int> LinkSock;

    for (int i = 2; i < argc; i++)
    {
        if(argv[i] != NULL)
            LinkSock.push_back(atoi(argv[i]));
    }

    License::Instance().CheckLicense();
    WTConnect WTConn(Loop, LinkSock);

    int Ret = BaseConf::Instance().GetDevNum(ServerCnt);// 从配置文件中读取子仪器配置获取子仪器数量
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "get dev num failed");
        ServerCnt = 1;
    }

    DevConf::Instance().GetTcpPort(Port);//通过读取文件获取TCP监听端口  ,配置文件里面设置端口为8600
    WTLinkSockServer srv(Loop, ServerCnt);
    Ret = srv.StartSocketSrv(Port, &WTConn); //启动连接服务
    if(Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "manager Server start error!");
        exit(0);
    }

    Loop.run();

    return 0;
}
