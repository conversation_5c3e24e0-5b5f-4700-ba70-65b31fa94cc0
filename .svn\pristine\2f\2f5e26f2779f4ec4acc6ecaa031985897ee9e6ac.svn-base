#pragma once

#include "basehead.h"
#include "json.hpp"
#include <string>
#include <vector>
#include <algorithm>

using json = nlohmann::json;

template <typename T, size_t N>
static inline constexpr size_t arraySize(const T (&)[N]) noexcept {
    return N;
}

inline json to_json(const Alg_GSM_SpectModInType& obj) {
    json j;
    j["OffsetState"] = json::array();
    for (size_t i = 0; i < arraySize(obj.OffsetState); ++i) {
        j["OffsetState"].emplace_back(obj.OffsetState[i]);
    }
    j["FreqOffset"] = json::array();
    for (size_t i = 0; i < arraySize(obj.FreqOffset); ++i) {
        j["FreqOffset"].emplace_back(obj.FreqOffset[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectModInType& obj) {
    if (j.contains("OffsetState")) {
        const auto& arr = j["OffsetState"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.OffsetState)); ++i) {
            arr[i].get_to(obj.OffsetState[i]);
        }
    }
    if (j.contains("FreqOffset")) {
        const auto& arr = j["FreqOffset"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.FreqOffset)); ++i) {
            arr[i].get_to(obj.FreqOffset[i]);
        }
    }
}

inline json to_json(const Alg_GSM_SpectSwtInType& obj) {
    json j;
    j["OffsetState"] = json::array();
    for (size_t i = 0; i < arraySize(obj.OffsetState); ++i) {
        j["OffsetState"].emplace_back(obj.OffsetState[i]);
    }
    j["FreqOffset"] = json::array();
    for (size_t i = 0; i < arraySize(obj.FreqOffset); ++i) {
        j["FreqOffset"].emplace_back(obj.FreqOffset[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectSwtInType& obj) {
    if (j.contains("OffsetState")) {
        const auto& arr = j["OffsetState"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.OffsetState)); ++i) {
            arr[i].get_to(obj.OffsetState[i]);
        }
    }
    if (j.contains("FreqOffset")) {
        const auto& arr = j["FreqOffset"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.FreqOffset)); ++i) {
            arr[i].get_to(obj.FreqOffset[i]);
        }
    }
}

inline json to_json(const Alg_GSM_ModuAllType& obj) {
    json j;
    j["Current"] = obj.Current;
    j["Average"] = obj.Average;
    j["Max"] = obj.Max;
    j["LimitValue"] = obj.LimitValue;
    return j;
}

inline void from_json(const json& j, Alg_GSM_ModuAllType& obj) {
    if (j.contains("Current")) {
        j["Current"].get_to(obj.Current);
    }
    if (j.contains("Average")) {
        j["Average"].get_to(obj.Average);
    }
    if (j.contains("Max")) {
        j["Max"].get_to(obj.Max);
    }
    if (j.contains("LimitValue")) {
        j["LimitValue"].get_to(obj.LimitValue);
    }
}

inline json to_json(const Alg_3GPP_BaseLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["Limit"] = obj.Limit;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_BaseLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("Limit")) {
        j["Limit"].get_to(obj.Limit);
    }
}

inline json to_json(const Alg_GSM_ModulateLimitType& obj) {
    json j;
    j["EvmRms"] = to_json(obj.EvmRms);
    j["EvmPeak"] = to_json(obj.EvmPeak);
    j["Evm95Percent"] = to_json(obj.Evm95Percent);
    j["MErrRms"] = to_json(obj.MErrRms);
    j["MErrPeak"] = to_json(obj.MErrPeak);
    j["MErr95Percent"] = to_json(obj.MErr95Percent);
    j["PhErrRms"] = to_json(obj.PhErrRms);
    j["PhErrPeak"] = to_json(obj.PhErrPeak);
    j["PhErr95Percent"] = to_json(obj.PhErr95Percent);
    j["IQOffset"] = to_json(obj.IQOffset);
    j["IQImbalance"] = to_json(obj.IQImbalance);
    j["FreError"] = to_json(obj.FreError);
    j["TimeError"] = to_json(obj.TimeError);
    return j;
}

inline void from_json(const json& j, Alg_GSM_ModulateLimitType& obj) {
    if (j.contains("EvmRms")) {
        from_json(j["EvmRms"], obj.EvmRms);
    }
    if (j.contains("EvmPeak")) {
        from_json(j["EvmPeak"], obj.EvmPeak);
    }
    if (j.contains("Evm95Percent")) {
        from_json(j["Evm95Percent"], obj.Evm95Percent);
    }
    if (j.contains("MErrRms")) {
        from_json(j["MErrRms"], obj.MErrRms);
    }
    if (j.contains("MErrPeak")) {
        from_json(j["MErrPeak"], obj.MErrPeak);
    }
    if (j.contains("MErr95Percent")) {
        from_json(j["MErr95Percent"], obj.MErr95Percent);
    }
    if (j.contains("PhErrRms")) {
        from_json(j["PhErrRms"], obj.PhErrRms);
    }
    if (j.contains("PhErrPeak")) {
        from_json(j["PhErrPeak"], obj.PhErrPeak);
    }
    if (j.contains("PhErr95Percent")) {
        from_json(j["PhErr95Percent"], obj.PhErr95Percent);
    }
    if (j.contains("IQOffset")) {
        from_json(j["IQOffset"], obj.IQOffset);
    }
    if (j.contains("IQImbalance")) {
        from_json(j["IQImbalance"], obj.IQImbalance);
    }
    if (j.contains("FreError")) {
        from_json(j["FreError"], obj.FreError);
    }
    if (j.contains("TimeError")) {
        from_json(j["TimeError"], obj.TimeError);
    }
}

inline json to_json(const Alg_GSM_PvTAvgBurstPowerType& obj) {
    json j;
    j["State"] = obj.State;
    j["FromPCL"] = obj.FromPCL;
    j["ToPCL"] = obj.ToPCL;
    j["Lower"] = obj.Lower;
    j["Upper"] = obj.Upper;
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTAvgBurstPowerType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("FromPCL")) {
        j["FromPCL"].get_to(obj.FromPCL);
    }
    if (j.contains("ToPCL")) {
        j["ToPCL"].get_to(obj.ToPCL);
    }
    if (j.contains("Lower")) {
        j["Lower"].get_to(obj.Lower);
    }
    if (j.contains("Upper")) {
        j["Upper"].get_to(obj.Upper);
    }
}

inline json to_json(const Alg_GSM_PvTAvgStaticEdgeType& obj) {
    json j;
    j["Time"] = obj.Time;
    j["LevelRel"] = obj.LevelRel;
    j["LevelAbs"] = to_json(obj.LevelAbs);
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTAvgStaticEdgeType& obj) {
    if (j.contains("Time")) {
        j["Time"].get_to(obj.Time);
    }
    if (j.contains("LevelRel")) {
        j["LevelRel"].get_to(obj.LevelRel);
    }
    if (j.contains("LevelAbs")) {
        from_json(j["LevelAbs"], obj.LevelAbs);
    }
}

inline json to_json(const Alg_GSM_PvTAvgStaticType& obj) {
    json j;
    j["Start"] = to_json(obj.Start);
    j["Stop"] = to_json(obj.Stop);
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTAvgStaticType& obj) {
    if (j.contains("Start")) {
        from_json(j["Start"], obj.Start);
    }
    if (j.contains("Stop")) {
        from_json(j["Stop"], obj.Stop);
    }
}

inline json to_json(const Alg_GSM_PvTAvgDynamicType& obj) {
    json j;
    j["State"] = obj.State;
    j["StartPCL"] = obj.StartPCL;
    j["EndPCL"] = obj.EndPCL;
    j["Correction"] = obj.Correction;
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTAvgDynamicType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("StartPCL")) {
        j["StartPCL"].get_to(obj.StartPCL);
    }
    if (j.contains("EndPCL")) {
        j["EndPCL"].get_to(obj.EndPCL);
    }
    if (j.contains("Correction")) {
        j["Correction"].get_to(obj.Correction);
    }
}

inline json to_json(const Alg_GSM_PvTAvgAreType& obj) {
    json j;
    j["State"] = obj.State;
    j["StaticLimt"] = to_json(obj.StaticLimt);
    j["DynamicLimt"] = json::array();
    for (size_t i = 0; i < arraySize(obj.DynamicLimt); ++i) {
        j["DynamicLimt"].emplace_back(to_json(obj.DynamicLimt[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTAvgAreType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("StaticLimt")) {
        from_json(j["StaticLimt"], obj.StaticLimt);
    }
    if (j.contains("DynamicLimt")) {
        const auto& arr = j["DynamicLimt"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.DynamicLimt)); ++i) {
            from_json(arr[i], obj.DynamicLimt[i]);
        }
    }
}

inline json to_json(const Alg_GSM_PvTUpperTemType& obj) {
    json j;
    j["RiseEdgeLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.RiseEdgeLimit); ++i) {
        j["RiseEdgeLimit"].emplace_back(to_json(obj.RiseEdgeLimit[i]));
    }
    j["UsefulPartLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.UsefulPartLimit); ++i) {
        j["UsefulPartLimit"].emplace_back(to_json(obj.UsefulPartLimit[i]));
    }
    j["FallEdgeLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.FallEdgeLimit); ++i) {
        j["FallEdgeLimit"].emplace_back(to_json(obj.FallEdgeLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTUpperTemType& obj) {
    if (j.contains("RiseEdgeLimit")) {
        const auto& arr = j["RiseEdgeLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.RiseEdgeLimit)); ++i) {
            from_json(arr[i], obj.RiseEdgeLimit[i]);
        }
    }
    if (j.contains("UsefulPartLimit")) {
        const auto& arr = j["UsefulPartLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.UsefulPartLimit)); ++i) {
            from_json(arr[i], obj.UsefulPartLimit[i]);
        }
    }
    if (j.contains("FallEdgeLimit")) {
        const auto& arr = j["FallEdgeLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.FallEdgeLimit)); ++i) {
            from_json(arr[i], obj.FallEdgeLimit[i]);
        }
    }
}

inline json to_json(const Alg_GSM_PvTLowerTemType& obj) {
    json j;
    j["UsefulPartLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.UsefulPartLimit); ++i) {
        j["UsefulPartLimit"].emplace_back(to_json(obj.UsefulPartLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_PvTLowerTemType& obj) {
    if (j.contains("UsefulPartLimit")) {
        const auto& arr = j["UsefulPartLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.UsefulPartLimit)); ++i) {
            from_json(arr[i], obj.UsefulPartLimit[i]);
        }
    }
}

inline json to_json(const Alg_GSM_PowerVsTimeLimitType& obj) {
    json j;
    j["AvgLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.AvgLimit); ++i) {
        j["AvgLimit"].emplace_back(to_json(obj.AvgLimit[i]));
    }
    j["GuardPeriod"] = to_json(obj.GuardPeriod);
    j["UpperTemLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.UpperTemLimit); ++i) {
        j["UpperTemLimit"].emplace_back(to_json(obj.UpperTemLimit[i]));
    }
    j["LowerTemlimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.LowerTemlimit); ++i) {
        j["LowerTemlimit"].emplace_back(to_json(obj.LowerTemlimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_PowerVsTimeLimitType& obj) {
    if (j.contains("AvgLimit")) {
        const auto& arr = j["AvgLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.AvgLimit)); ++i) {
            from_json(arr[i], obj.AvgLimit[i]);
        }
    }
    if (j.contains("GuardPeriod")) {
        from_json(j["GuardPeriod"], obj.GuardPeriod);
    }
    if (j.contains("UpperTemLimit")) {
        const auto& arr = j["UpperTemLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.UpperTemLimit)); ++i) {
            from_json(arr[i], obj.UpperTemLimit[i]);
        }
    }
    if (j.contains("LowerTemlimit")) {
        const auto& arr = j["LowerTemlimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.LowerTemlimit)); ++i) {
            from_json(arr[i], obj.LowerTemlimit[i]);
        }
    }
}

inline json to_json(const Alg_GSM_SpectModRefPwrType& obj) {
    json j;
    j["LowPwr"] = obj.LowPwr;
    j["HighPwr"] = obj.HighPwr;
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectModRefPwrType& obj) {
    if (j.contains("LowPwr")) {
        j["LowPwr"].get_to(obj.LowPwr);
    }
    if (j.contains("HighPwr")) {
        j["HighPwr"].get_to(obj.HighPwr);
    }
}

inline json to_json(const Alg_GSM_SpectModFreOffsetType& obj) {
    json j;
    j["State"] = obj.State;
    j["LowPwrRel"] = obj.LowPwrRel;
    j["HighPwrRel"] = obj.HighPwrRel;
    j["AbsPwr"] = obj.AbsPwr;
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectModFreOffsetType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("LowPwrRel")) {
        j["LowPwrRel"].get_to(obj.LowPwrRel);
    }
    if (j.contains("HighPwrRel")) {
        j["HighPwrRel"].get_to(obj.HighPwrRel);
    }
    if (j.contains("AbsPwr")) {
        j["AbsPwr"].get_to(obj.AbsPwr);
    }
}

inline json to_json(const Alg_GSM_SpectModulationType& obj) {
    json j;
    j["RefPwrLimit"] = to_json(obj.RefPwrLimit);
    j["FreOffsetLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.FreOffsetLimit); ++i) {
        j["FreOffsetLimit"].emplace_back(to_json(obj.FreOffsetLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectModulationType& obj) {
    if (j.contains("RefPwrLimit")) {
        from_json(j["RefPwrLimit"], obj.RefPwrLimit);
    }
    if (j.contains("FreOffsetLimit")) {
        const auto& arr = j["FreOffsetLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.FreOffsetLimit)); ++i) {
            from_json(arr[i], obj.FreOffsetLimit[i]);
        }
    }
}

inline json to_json(const Alg_GSM_SpectSwiFreOffsetType& obj) {
    json j;
    j["State"] = obj.State;
    j["LimitValue"] = json::array();
    for (size_t i = 0; i < arraySize(obj.LimitValue); ++i) {
        j["LimitValue"].emplace_back(obj.LimitValue[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectSwiFreOffsetType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("LimitValue")) {
        const auto& arr = j["LimitValue"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.LimitValue)); ++i) {
            arr[i].get_to(obj.LimitValue[i]);
        }
    }
}

inline json to_json(const Alg_GSM_SpectSwitchType& obj) {
    json j;
    j["RefPower"] = json::array();
    for (size_t i = 0; i < arraySize(obj.RefPower); ++i) {
        j["RefPower"].emplace_back(to_json(obj.RefPower[i]));
    }
    j["FreOffsetLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.FreOffsetLimit); ++i) {
        j["FreOffsetLimit"].emplace_back(to_json(obj.FreOffsetLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_GSM_SpectSwitchType& obj) {
    if (j.contains("RefPower")) {
        const auto& arr = j["RefPower"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.RefPower)); ++i) {
            from_json(arr[i], obj.RefPower[i]);
        }
    }
    if (j.contains("FreOffsetLimit")) {
        const auto& arr = j["FreOffsetLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.FreOffsetLimit)); ++i) {
            from_json(arr[i], obj.FreOffsetLimit[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_LimitInGSM& obj) {
    json j;
    j["ModLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.ModLimit); ++i) {
        j["ModLimit"].emplace_back(to_json(obj.ModLimit[i]));
    }
    j["PVTLimit"] = to_json(obj.PVTLimit);
    j["SpecModLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SpecModLimit); ++i) {
        j["SpecModLimit"].emplace_back(to_json(obj.SpecModLimit[i]));
    }
    j["SpecSwiLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SpecSwiLimit); ++i) {
        j["SpecSwiLimit"].emplace_back(to_json(obj.SpecSwiLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_LimitInGSM& obj) {
    if (j.contains("ModLimit")) {
        const auto& arr = j["ModLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.ModLimit)); ++i) {
            from_json(arr[i], obj.ModLimit[i]);
        }
    }
    if (j.contains("PVTLimit")) {
        from_json(j["PVTLimit"], obj.PVTLimit);
    }
    if (j.contains("SpecModLimit")) {
        const auto& arr = j["SpecModLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SpecModLimit)); ++i) {
            from_json(arr[i], obj.SpecModLimit[i]);
        }
    }
    if (j.contains("SpecSwiLimit")) {
        const auto& arr = j["SpecSwiLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SpecSwiLimit)); ++i) {
            from_json(arr[i], obj.SpecSwiLimit[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzInGSM& obj) {
    json j;
    j["SlotOffset"] = obj.SlotOffset;
    j["NumbOfSlot"] = obj.NumbOfSlot;
    j["MeasureSlot"] = obj.MeasureSlot;
    j["PvTFilter"] = obj.PvTFilter;
    j["SpectMod"] = to_json(obj.SpectMod);
    j["SpectSwt"] = to_json(obj.SpectSwt);
    j["LimitInfo"] = to_json(obj.LimitInfo);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzInGSM& obj) {
    if (j.contains("SlotOffset")) {
        j["SlotOffset"].get_to(obj.SlotOffset);
    }
    if (j.contains("NumbOfSlot")) {
        j["NumbOfSlot"].get_to(obj.NumbOfSlot);
    }
    if (j.contains("MeasureSlot")) {
        j["MeasureSlot"].get_to(obj.MeasureSlot);
    }
    if (j.contains("PvTFilter")) {
        j["PvTFilter"].get_to(obj.PvTFilter);
    }
    if (j.contains("SpectMod")) {
        from_json(j["SpectMod"], obj.SpectMod);
    }
    if (j.contains("SpectSwt")) {
        from_json(j["SpectSwt"], obj.SpectSwt);
    }
    if (j.contains("LimitInfo")) {
        from_json(j["LimitInfo"], obj.LimitInfo);
    }
}

inline json to_json(const Alg_3GPP_AlzULInWCDMA& obj) {
    json j;
    j["ScramblingCode"] = obj.ScramblingCode;
    j["DPCCHSlotFormat"] = obj.DPCCHSlotFormat;
    j["ChannelType"] = obj.ChannelType;
    j["DPDCHAvailable"] = obj.DPDCHAvailable;
    j["MeasureLen"] = obj.MeasureLen;
    j["SyncSlotId"] = obj.SyncSlotId;
    j["SlotNum"] = obj.SlotNum;
    j["CDPSpreadFactor"] = obj.CDPSpreadFactor;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzULInWCDMA& obj) {
    if (j.contains("ScramblingCode")) {
        j["ScramblingCode"].get_to(obj.ScramblingCode);
    }
    if (j.contains("DPCCHSlotFormat")) {
        j["DPCCHSlotFormat"].get_to(obj.DPCCHSlotFormat);
    }
    if (j.contains("ChannelType")) {
        j["ChannelType"].get_to(obj.ChannelType);
    }
    if (j.contains("DPDCHAvailable")) {
        j["DPDCHAvailable"].get_to(obj.DPDCHAvailable);
    }
    if (j.contains("MeasureLen")) {
        j["MeasureLen"].get_to(obj.MeasureLen);
    }
    if (j.contains("SyncSlotId")) {
        j["SyncSlotId"].get_to(obj.SyncSlotId);
    }
    if (j.contains("SlotNum")) {
        j["SlotNum"].get_to(obj.SlotNum);
    }
    if (j.contains("CDPSpreadFactor")) {
        j["CDPSpreadFactor"].get_to(obj.CDPSpreadFactor);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_WCDMA_DLCHType& obj) {
    json j;
    j["State"] = obj.State;
    j["DataType"] = obj.DataType;
    j["Initialization"] = obj.Initialization;
    j["TTI"] = obj.TTI;
    j["TbCount"] = obj.TbCount;
    j["TbSize"] = obj.TbSize;
    j["Crc"] = obj.Crc;
    j["RmAttribute"] = obj.RmAttribute;
    j["EProtection"] = obj.EProtection;
    j["InterleaverStat"] = obj.InterleaverStat;
    return j;
}

inline void from_json(const json& j, Alg_WCDMA_DLCHType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("DataType")) {
        j["DataType"].get_to(obj.DataType);
    }
    if (j.contains("Initialization")) {
        j["Initialization"].get_to(obj.Initialization);
    }
    if (j.contains("TTI")) {
        j["TTI"].get_to(obj.TTI);
    }
    if (j.contains("TbCount")) {
        j["TbCount"].get_to(obj.TbCount);
    }
    if (j.contains("TbSize")) {
        j["TbSize"].get_to(obj.TbSize);
    }
    if (j.contains("Crc")) {
        j["Crc"].get_to(obj.Crc);
    }
    if (j.contains("RmAttribute")) {
        j["RmAttribute"].get_to(obj.RmAttribute);
    }
    if (j.contains("EProtection")) {
        j["EProtection"].get_to(obj.EProtection);
    }
    if (j.contains("InterleaverStat")) {
        j["InterleaverStat"].get_to(obj.InterleaverStat);
    }
}

inline json to_json(const Alg_WCDMA_DCHType& obj) {
    json j;
    j["State"] = obj.State;
    j["Interleaver2Stat"] = obj.Interleaver2Stat;
    j["DCCH"] = to_json(obj.DCCH);
    j["DTCH"] = json::array();
    for (size_t i = 0; i < arraySize(obj.DTCH); ++i) {
        j["DTCH"].emplace_back(to_json(obj.DTCH[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_WCDMA_DCHType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("Interleaver2Stat")) {
        j["Interleaver2Stat"].get_to(obj.Interleaver2Stat);
    }
    if (j.contains("DCCH")) {
        from_json(j["DCCH"], obj.DCCH);
    }
    if (j.contains("DTCH")) {
        const auto& arr = j["DTCH"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.DTCH)); ++i) {
            from_json(arr[i], obj.DTCH[i]);
        }
    }
}

inline json to_json(const Alg_WCDMA_DLDPCHType& obj) {
    json j;
    j["State"] = obj.State;
    j["SlotFormat"] = obj.SlotFormat;
    j["SymbRate"] = obj.SymbRate;
    j["ChanCode"] = obj.ChanCode;
    j["TimingOffset"] = obj.TimingOffset;
    j["TpcDataType"] = obj.TpcDataType;
    j["DataType"] = obj.DataType;
    j["Initialization"] = obj.Initialization;
    j["Power"] = obj.Power;
    j["DCH"] = to_json(obj.DCH);
    return j;
}

inline void from_json(const json& j, Alg_WCDMA_DLDPCHType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("SlotFormat")) {
        j["SlotFormat"].get_to(obj.SlotFormat);
    }
    if (j.contains("SymbRate")) {
        j["SymbRate"].get_to(obj.SymbRate);
    }
    if (j.contains("ChanCode")) {
        j["ChanCode"].get_to(obj.ChanCode);
    }
    if (j.contains("TimingOffset")) {
        j["TimingOffset"].get_to(obj.TimingOffset);
    }
    if (j.contains("TpcDataType")) {
        j["TpcDataType"].get_to(obj.TpcDataType);
    }
    if (j.contains("DataType")) {
        j["DataType"].get_to(obj.DataType);
    }
    if (j.contains("Initialization")) {
        j["Initialization"].get_to(obj.Initialization);
    }
    if (j.contains("Power")) {
        j["Power"].get_to(obj.Power);
    }
    if (j.contains("DCH")) {
        from_json(j["DCH"], obj.DCH);
    }
}

inline json to_json(const Alg_WCDMA_PSCHType& obj) {
    json j;
    j["State"] = obj.State;
    j["SymbRate"] = obj.SymbRate;
    j["Power"] = obj.Power;
    return j;
}

inline void from_json(const json& j, Alg_WCDMA_PSCHType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("SymbRate")) {
        j["SymbRate"].get_to(obj.SymbRate);
    }
    if (j.contains("Power")) {
        j["Power"].get_to(obj.Power);
    }
}

inline json to_json(const Alg_WCDMA_SSCHType& obj) {
    json j;
    j["State"] = obj.State;
    j["SymbRate"] = obj.SymbRate;
    j["Power"] = obj.Power;
    return j;
}

inline void from_json(const json& j, Alg_WCDMA_SSCHType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("SymbRate")) {
        j["SymbRate"].get_to(obj.SymbRate);
    }
    if (j.contains("Power")) {
        j["Power"].get_to(obj.Power);
    }
}

inline json to_json(const Alg_WCDMA_PCPICHType& obj) {
    json j;
    j["State"] = obj.State;
    j["SymbRate"] = obj.SymbRate;
    j["ChanCode"] = obj.ChanCode;
    j["Power"] = obj.Power;
    return j;
}

inline void from_json(const json& j, Alg_WCDMA_PCPICHType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("SymbRate")) {
        j["SymbRate"].get_to(obj.SymbRate);
    }
    if (j.contains("ChanCode")) {
        j["ChanCode"].get_to(obj.ChanCode);
    }
    if (j.contains("Power")) {
        j["Power"].get_to(obj.Power);
    }
}

inline json to_json(const Alg_3GPP_AlzDLInWCDMA& obj) {
    json j;
    j["ScramblingCode"] = obj.ScramblingCode;
    j["DPCHNum"] = obj.DPCHNum;
    j["DPCH"] = json::array();
    for (size_t i = 0; i < arraySize(obj.DPCH); ++i) {
        j["DPCH"].emplace_back(to_json(obj.DPCH[i]));
    }
    j["PSCH"] = to_json(obj.PSCH);
    j["SSCH"] = to_json(obj.SSCH);
    j["PCPICH"] = to_json(obj.PCPICH);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLInWCDMA& obj) {
    if (j.contains("ScramblingCode")) {
        j["ScramblingCode"].get_to(obj.ScramblingCode);
    }
    if (j.contains("DPCHNum")) {
        j["DPCHNum"].get_to(obj.DPCHNum);
    }
    if (j.contains("DPCH")) {
        const auto& arr = j["DPCH"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.DPCH)); ++i) {
            from_json(arr[i], obj.DPCH[i]);
        }
    }
    if (j.contains("PSCH")) {
        from_json(j["PSCH"], obj.PSCH);
    }
    if (j.contains("SSCH")) {
        from_json(j["SSCH"], obj.SSCH);
    }
    if (j.contains("PCPICH")) {
        from_json(j["PCPICH"], obj.PCPICH);
    }
}

inline json to_json(const Alg_3GPP_AlzMeasureWCDMA& obj) {
    json j;
    j["AclrLimit1Mode"] = obj.AclrLimit1Mode;
    j["AclrLimit2Mode"] = obj.AclrLimit2Mode;
    j["AclrAbsLimitMode"] = obj.AclrAbsLimitMode;
    j["UtraLimit1"] = obj.UtraLimit1;
    j["UtraLimit2"] = obj.UtraLimit2;
    j["AbsLimit"] = obj.AbsLimit;
    j["SEMLimitADMode"] = obj.SEMLimitADMode;
    j["SEMLimitEFMode"] = obj.SEMLimitEFMode;
    j["SEMAbsLimitGMode"] = obj.SEMAbsLimitGMode;
    j["SEMAbsLimitHMode"] = obj.SEMAbsLimitHMode;
    j["HMode"] = obj.HMode;
    j["LimitAD"] = json::array();
    for (size_t i = 0; i < arraySize(obj.LimitAD); ++i) {
        j["LimitAD"].emplace_back(obj.LimitAD[i]);
    }
    j["LimitEF"] = json::array();
    for (size_t i = 0; i < arraySize(obj.LimitEF); ++i) {
        j["LimitEF"].emplace_back(obj.LimitEF[i]);
    }
    j["SEMAbsLimitG"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMAbsLimitG); ++i) {
        j["SEMAbsLimitG"].emplace_back(obj.SEMAbsLimitG[i]);
    }
    j["SEMAbsLimitH"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMAbsLimitH); ++i) {
        j["SEMAbsLimitH"].emplace_back(obj.SEMAbsLimitH[i]);
    }
    j["PeakMagnErrLimitMode"] = obj.PeakMagnErrLimitMode;
    j["RmsMagnErrLimitMode"] = obj.RmsMagnErrLimitMode;
    j["PeakMagnErrLimit"] = obj.PeakMagnErrLimit;
    j["RmsMagnErrLimit"] = obj.RmsMagnErrLimit;
    j["PeakEvmLimitMode"] = obj.PeakEvmLimitMode;
    j["RmsEvmLimitMode"] = obj.RmsEvmLimitMode;
    j["PeakEvmLimit"] = obj.PeakEvmLimit;
    j["RmsEvmLimit"] = obj.RmsEvmLimit;
    j["PeakPhaseErrLimitMode"] = obj.PeakPhaseErrLimitMode;
    j["RmsPhaseErrLimitMode"] = obj.RmsPhaseErrLimitMode;
    j["PeakPhaseErrLimit"] = obj.PeakPhaseErrLimit;
    j["RmsPhaseErrLimit"] = obj.RmsPhaseErrLimit;
    j["CFErrLimitMode"] = obj.CFErrLimitMode;
    j["CFErrLimit"] = obj.CFErrLimit;
    j["PhaseDisLimitMode"] = obj.PhaseDisLimitMode;
    j["UpperLimit"] = obj.UpperLimit;
    j["DynamicLimit"] = obj.DynamicLimit;
    j["IQOffsetLimitMode"] = obj.IQOffsetLimitMode;
    j["IQOffsetLimit"] = obj.IQOffsetLimit;
    j["IQImabaLimitMode"] = obj.IQImabaLimitMode;
    j["IQImabaLimit"] = obj.IQImabaLimit;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzMeasureWCDMA& obj) {
    if (j.contains("AclrLimit1Mode")) {
        j["AclrLimit1Mode"].get_to(obj.AclrLimit1Mode);
    }
    if (j.contains("AclrLimit2Mode")) {
        j["AclrLimit2Mode"].get_to(obj.AclrLimit2Mode);
    }
    if (j.contains("AclrAbsLimitMode")) {
        j["AclrAbsLimitMode"].get_to(obj.AclrAbsLimitMode);
    }
    if (j.contains("UtraLimit1")) {
        j["UtraLimit1"].get_to(obj.UtraLimit1);
    }
    if (j.contains("UtraLimit2")) {
        j["UtraLimit2"].get_to(obj.UtraLimit2);
    }
    if (j.contains("AbsLimit")) {
        j["AbsLimit"].get_to(obj.AbsLimit);
    }
    if (j.contains("SEMLimitADMode")) {
        j["SEMLimitADMode"].get_to(obj.SEMLimitADMode);
    }
    if (j.contains("SEMLimitEFMode")) {
        j["SEMLimitEFMode"].get_to(obj.SEMLimitEFMode);
    }
    if (j.contains("SEMAbsLimitGMode")) {
        j["SEMAbsLimitGMode"].get_to(obj.SEMAbsLimitGMode);
    }
    if (j.contains("SEMAbsLimitHMode")) {
        j["SEMAbsLimitHMode"].get_to(obj.SEMAbsLimitHMode);
    }
    if (j.contains("HMode")) {
        j["HMode"].get_to(obj.HMode);
    }
    if (j.contains("LimitAD")) {
        const auto& arr = j["LimitAD"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.LimitAD)); ++i) {
            arr[i].get_to(obj.LimitAD[i]);
        }
    }
    if (j.contains("LimitEF")) {
        const auto& arr = j["LimitEF"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.LimitEF)); ++i) {
            arr[i].get_to(obj.LimitEF[i]);
        }
    }
    if (j.contains("SEMAbsLimitG")) {
        const auto& arr = j["SEMAbsLimitG"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMAbsLimitG)); ++i) {
            arr[i].get_to(obj.SEMAbsLimitG[i]);
        }
    }
    if (j.contains("SEMAbsLimitH")) {
        const auto& arr = j["SEMAbsLimitH"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMAbsLimitH)); ++i) {
            arr[i].get_to(obj.SEMAbsLimitH[i]);
        }
    }
    if (j.contains("PeakMagnErrLimitMode")) {
        j["PeakMagnErrLimitMode"].get_to(obj.PeakMagnErrLimitMode);
    }
    if (j.contains("RmsMagnErrLimitMode")) {
        j["RmsMagnErrLimitMode"].get_to(obj.RmsMagnErrLimitMode);
    }
    if (j.contains("PeakMagnErrLimit")) {
        j["PeakMagnErrLimit"].get_to(obj.PeakMagnErrLimit);
    }
    if (j.contains("RmsMagnErrLimit")) {
        j["RmsMagnErrLimit"].get_to(obj.RmsMagnErrLimit);
    }
    if (j.contains("PeakEvmLimitMode")) {
        j["PeakEvmLimitMode"].get_to(obj.PeakEvmLimitMode);
    }
    if (j.contains("RmsEvmLimitMode")) {
        j["RmsEvmLimitMode"].get_to(obj.RmsEvmLimitMode);
    }
    if (j.contains("PeakEvmLimit")) {
        j["PeakEvmLimit"].get_to(obj.PeakEvmLimit);
    }
    if (j.contains("RmsEvmLimit")) {
        j["RmsEvmLimit"].get_to(obj.RmsEvmLimit);
    }
    if (j.contains("PeakPhaseErrLimitMode")) {
        j["PeakPhaseErrLimitMode"].get_to(obj.PeakPhaseErrLimitMode);
    }
    if (j.contains("RmsPhaseErrLimitMode")) {
        j["RmsPhaseErrLimitMode"].get_to(obj.RmsPhaseErrLimitMode);
    }
    if (j.contains("PeakPhaseErrLimit")) {
        j["PeakPhaseErrLimit"].get_to(obj.PeakPhaseErrLimit);
    }
    if (j.contains("RmsPhaseErrLimit")) {
        j["RmsPhaseErrLimit"].get_to(obj.RmsPhaseErrLimit);
    }
    if (j.contains("CFErrLimitMode")) {
        j["CFErrLimitMode"].get_to(obj.CFErrLimitMode);
    }
    if (j.contains("CFErrLimit")) {
        j["CFErrLimit"].get_to(obj.CFErrLimit);
    }
    if (j.contains("PhaseDisLimitMode")) {
        j["PhaseDisLimitMode"].get_to(obj.PhaseDisLimitMode);
    }
    if (j.contains("UpperLimit")) {
        j["UpperLimit"].get_to(obj.UpperLimit);
    }
    if (j.contains("DynamicLimit")) {
        j["DynamicLimit"].get_to(obj.DynamicLimit);
    }
    if (j.contains("IQOffsetLimitMode")) {
        j["IQOffsetLimitMode"].get_to(obj.IQOffsetLimitMode);
    }
    if (j.contains("IQOffsetLimit")) {
        j["IQOffsetLimit"].get_to(obj.IQOffsetLimit);
    }
    if (j.contains("IQImabaLimitMode")) {
        j["IQImabaLimitMode"].get_to(obj.IQImabaLimitMode);
    }
    if (j.contains("IQImabaLimit")) {
        j["IQImabaLimit"].get_to(obj.IQImabaLimit);
    }
}

inline json to_json(const Alg_3GPP_AlzInWCDMA& obj) {
    json j;
    j["LinkDirect"] = obj.LinkDirect;
    j["UL"] = to_json(obj.UL);
    j["DL"] = to_json(obj.DL);
    j["Measure"] = to_json(obj.Measure);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzInWCDMA& obj) {
    if (j.contains("LinkDirect")) {
        j["LinkDirect"].get_to(obj.LinkDirect);
    }
    if (j.contains("UL")) {
        from_json(j["UL"], obj.UL);
    }
    if (j.contains("DL")) {
        from_json(j["DL"], obj.DL);
    }
    if (j.contains("Measure")) {
        from_json(j["Measure"], obj.Measure);
    }
}

inline json to_json(const Alg_3GPP_AlzCell4g& obj) {
    json j;
    j["CellIdx"] = obj.CellIdx;
    j["State"] = obj.State;
    j["PhyCellID"] = obj.PhyCellID;
    j["ChannelBW"] = obj.ChannelBW;
    j["Duplexing"] = obj.Duplexing;
    j["ULDLConfig"] = obj.ULDLConfig;
    j["SpecialSubfrmConfig"] = obj.SpecialSubfrmConfig;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzCell4g& obj) {
    if (j.contains("CellIdx")) {
        j["CellIdx"].get_to(obj.CellIdx);
    }
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("PhyCellID")) {
        j["PhyCellID"].get_to(obj.PhyCellID);
    }
    if (j.contains("ChannelBW")) {
        j["ChannelBW"].get_to(obj.ChannelBW);
    }
    if (j.contains("Duplexing")) {
        j["Duplexing"].get_to(obj.Duplexing);
    }
    if (j.contains("ULDLConfig")) {
        j["ULDLConfig"].get_to(obj.ULDLConfig);
    }
    if (j.contains("SpecialSubfrmConfig")) {
        j["SpecialSubfrmConfig"].get_to(obj.SpecialSubfrmConfig);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzPusch4g& obj) {
    json j;
    j["CellIdx"] = obj.CellIdx;
    j["State"] = obj.State;
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["Precoding"] = obj.Precoding;
    j["LayerNum"] = obj.LayerNum;
    j["AntennaNum"] = obj.AntennaNum;
    j["CodebookIdx"] = obj.CodebookIdx;
    j["GroupHop"] = obj.GroupHop;
    j["SequenceHop"] = obj.SequenceHop;
    j["DeltaSeqShift"] = obj.DeltaSeqShift;
    j["N1Dmrs"] = obj.N1Dmrs;
    j["CyclicShiftField"] = obj.CyclicShiftField;
    j["Codeword"] = obj.Codeword;
    j["Modulate"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Modulate); ++i) {
        j["Modulate"].emplace_back(obj.Modulate[i]);
    }
    j["ChanCodingState"] = obj.ChanCodingState;
    j["Scramble"] = obj.Scramble;
    j["McsCfgMode"] = obj.McsCfgMode;
    j["Mcs"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Mcs); ++i) {
        j["Mcs"].emplace_back(obj.Mcs[i]);
    }
    j["PayloadSize"] = json::array();
    for (size_t i = 0; i < arraySize(obj.PayloadSize); ++i) {
        j["PayloadSize"].emplace_back(obj.PayloadSize[i]);
    }
    j["RedunVerIdx"] = json::array();
    for (size_t i = 0; i < arraySize(obj.RedunVerIdx); ++i) {
        j["RedunVerIdx"].emplace_back(obj.RedunVerIdx[i]);
    }
    j["Enable256QAM"] = obj.Enable256QAM;
    j["RBDetMode"] = obj.RBDetMode;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzPusch4g& obj) {
    if (j.contains("CellIdx")) {
        j["CellIdx"].get_to(obj.CellIdx);
    }
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("Precoding")) {
        j["Precoding"].get_to(obj.Precoding);
    }
    if (j.contains("LayerNum")) {
        j["LayerNum"].get_to(obj.LayerNum);
    }
    if (j.contains("AntennaNum")) {
        j["AntennaNum"].get_to(obj.AntennaNum);
    }
    if (j.contains("CodebookIdx")) {
        j["CodebookIdx"].get_to(obj.CodebookIdx);
    }
    if (j.contains("GroupHop")) {
        j["GroupHop"].get_to(obj.GroupHop);
    }
    if (j.contains("SequenceHop")) {
        j["SequenceHop"].get_to(obj.SequenceHop);
    }
    if (j.contains("DeltaSeqShift")) {
        j["DeltaSeqShift"].get_to(obj.DeltaSeqShift);
    }
    if (j.contains("N1Dmrs")) {
        j["N1Dmrs"].get_to(obj.N1Dmrs);
    }
    if (j.contains("CyclicShiftField")) {
        j["CyclicShiftField"].get_to(obj.CyclicShiftField);
    }
    if (j.contains("Codeword")) {
        j["Codeword"].get_to(obj.Codeword);
    }
    if (j.contains("Modulate")) {
        const auto& arr = j["Modulate"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Modulate)); ++i) {
            arr[i].get_to(obj.Modulate[i]);
        }
    }
    if (j.contains("ChanCodingState")) {
        j["ChanCodingState"].get_to(obj.ChanCodingState);
    }
    if (j.contains("Scramble")) {
        j["Scramble"].get_to(obj.Scramble);
    }
    if (j.contains("McsCfgMode")) {
        j["McsCfgMode"].get_to(obj.McsCfgMode);
    }
    if (j.contains("Mcs")) {
        const auto& arr = j["Mcs"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Mcs)); ++i) {
            arr[i].get_to(obj.Mcs[i]);
        }
    }
    if (j.contains("PayloadSize")) {
        const auto& arr = j["PayloadSize"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.PayloadSize)); ++i) {
            arr[i].get_to(obj.PayloadSize[i]);
        }
    }
    if (j.contains("RedunVerIdx")) {
        const auto& arr = j["RedunVerIdx"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.RedunVerIdx)); ++i) {
            arr[i].get_to(obj.RedunVerIdx[i]);
        }
    }
    if (j.contains("Enable256QAM")) {
        j["Enable256QAM"].get_to(obj.Enable256QAM);
    }
    if (j.contains("RBDetMode")) {
        j["RBDetMode"].get_to(obj.RBDetMode);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzPdsch4g& obj) {
    json j;
    j["SymbOffset"] = obj.SymbOffset;
    j["ResAllocateType"] = obj.ResAllocateType;
    j["VRBAssignment"] = obj.VRBAssignment;
    j["RBGBitmap"] = json::array();
    for (size_t i = 0; i < arraySize(obj.RBGBitmap); ++i) {
        j["RBGBitmap"].emplace_back(obj.RBGBitmap[i]);
    }
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["PbchState"] = obj.PbchState;
    j["Precoding"] = obj.Precoding;
    j["LayerNum"] = obj.LayerNum;
    j["AntennaNum"] = obj.AntennaNum;
    j["CyclicDelayDiversity"] = obj.CyclicDelayDiversity;
    j["CodebookIdx"] = obj.CodebookIdx;
    j["Codeword"] = obj.Codeword;
    j["Modulate"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Modulate); ++i) {
        j["Modulate"].emplace_back(obj.Modulate[i]);
    }
    j["ChanCodingState"] = obj.ChanCodingState;
    j["Scramble"] = obj.Scramble;
    j["McsCfgMode"] = obj.McsCfgMode;
    j["Mcs"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Mcs); ++i) {
        j["Mcs"].emplace_back(obj.Mcs[i]);
    }
    j["PayloadSize"] = json::array();
    for (size_t i = 0; i < arraySize(obj.PayloadSize); ++i) {
        j["PayloadSize"].emplace_back(obj.PayloadSize[i]);
    }
    j["RedunVerIdx"] = json::array();
    for (size_t i = 0; i < arraySize(obj.RedunVerIdx); ++i) {
        j["RedunVerIdx"].emplace_back(obj.RedunVerIdx[i]);
    }
    j["SoftChanBit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SoftChanBit); ++i) {
        j["SoftChanBit"].emplace_back(obj.SoftChanBit[i]);
    }
    j["PA"] = obj.PA;
    j["PB"] = obj.PB;
    j["NIR"] = json::array();
    for (size_t i = 0; i < arraySize(obj.NIR); ++i) {
        j["NIR"].emplace_back(obj.NIR[i]);
    }
    j["IRConfigMode"] = obj.IRConfigMode;
    j["TxMode"] = obj.TxMode;
    j["UECategory"] = obj.UECategory;
    j["McsTable"] = obj.McsTable;
    j["TbsIndexAlt"] = obj.TbsIndexAlt;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzPdsch4g& obj) {
    if (j.contains("SymbOffset")) {
        j["SymbOffset"].get_to(obj.SymbOffset);
    }
    if (j.contains("ResAllocateType")) {
        j["ResAllocateType"].get_to(obj.ResAllocateType);
    }
    if (j.contains("VRBAssignment")) {
        j["VRBAssignment"].get_to(obj.VRBAssignment);
    }
    if (j.contains("RBGBitmap")) {
        const auto& arr = j["RBGBitmap"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.RBGBitmap)); ++i) {
            arr[i].get_to(obj.RBGBitmap[i]);
        }
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("PbchState")) {
        j["PbchState"].get_to(obj.PbchState);
    }
    if (j.contains("Precoding")) {
        j["Precoding"].get_to(obj.Precoding);
    }
    if (j.contains("LayerNum")) {
        j["LayerNum"].get_to(obj.LayerNum);
    }
    if (j.contains("AntennaNum")) {
        j["AntennaNum"].get_to(obj.AntennaNum);
    }
    if (j.contains("CyclicDelayDiversity")) {
        j["CyclicDelayDiversity"].get_to(obj.CyclicDelayDiversity);
    }
    if (j.contains("CodebookIdx")) {
        j["CodebookIdx"].get_to(obj.CodebookIdx);
    }
    if (j.contains("Codeword")) {
        j["Codeword"].get_to(obj.Codeword);
    }
    if (j.contains("Modulate")) {
        const auto& arr = j["Modulate"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Modulate)); ++i) {
            arr[i].get_to(obj.Modulate[i]);
        }
    }
    if (j.contains("ChanCodingState")) {
        j["ChanCodingState"].get_to(obj.ChanCodingState);
    }
    if (j.contains("Scramble")) {
        j["Scramble"].get_to(obj.Scramble);
    }
    if (j.contains("McsCfgMode")) {
        j["McsCfgMode"].get_to(obj.McsCfgMode);
    }
    if (j.contains("Mcs")) {
        const auto& arr = j["Mcs"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Mcs)); ++i) {
            arr[i].get_to(obj.Mcs[i]);
        }
    }
    if (j.contains("PayloadSize")) {
        const auto& arr = j["PayloadSize"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.PayloadSize)); ++i) {
            arr[i].get_to(obj.PayloadSize[i]);
        }
    }
    if (j.contains("RedunVerIdx")) {
        const auto& arr = j["RedunVerIdx"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.RedunVerIdx)); ++i) {
            arr[i].get_to(obj.RedunVerIdx[i]);
        }
    }
    if (j.contains("SoftChanBit")) {
        const auto& arr = j["SoftChanBit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SoftChanBit)); ++i) {
            arr[i].get_to(obj.SoftChanBit[i]);
        }
    }
    if (j.contains("PA")) {
        j["PA"].get_to(obj.PA);
    }
    if (j.contains("PB")) {
        j["PB"].get_to(obj.PB);
    }
    if (j.contains("NIR")) {
        const auto& arr = j["NIR"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.NIR)); ++i) {
            arr[i].get_to(obj.NIR[i]);
        }
    }
    if (j.contains("IRConfigMode")) {
        j["IRConfigMode"].get_to(obj.IRConfigMode);
    }
    if (j.contains("TxMode")) {
        j["TxMode"].get_to(obj.TxMode);
    }
    if (j.contains("UECategory")) {
        j["UECategory"].get_to(obj.UECategory);
    }
    if (j.contains("McsTable")) {
        j["McsTable"].get_to(obj.McsTable);
    }
    if (j.contains("TbsIndexAlt")) {
        j["TbsIndexAlt"].get_to(obj.TbsIndexAlt);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_4G_MeasureInModulation& obj) {
    json j;
    j["ModEnable"] = obj.ModEnable;
    j["EvmEnable"] = obj.EvmEnable;
    j["MErrEnable"] = obj.MErrEnable;
    j["PErrEnable"] = obj.PErrEnable;
    j["EvmSubcarEnable"] = obj.EvmSubcarEnable;
    j["IBEEnable"] = obj.IBEEnable;
    j["ESFlatEnable"] = obj.ESFlatEnable;
    j["IQConstelEnable"] = obj.IQConstelEnable;
    j["TxMeasureEnable"] = obj.TxMeasureEnable;
    j["ModStatNum"] = obj.ModStatNum;
    j["EvmWinNcp"] = json::array();
    for (size_t i = 0; i < arraySize(obj.EvmWinNcp); ++i) {
        j["EvmWinNcp"].emplace_back(obj.EvmWinNcp[i]);
    }
    j["EvmWinEcp"] = json::array();
    for (size_t i = 0; i < arraySize(obj.EvmWinEcp); ++i) {
        j["EvmWinEcp"].emplace_back(obj.EvmWinEcp[i]);
    }
    j["ExPeriodLeading"] = obj.ExPeriodLeading;
    j["ExPeriodLagging"] = obj.ExPeriodLagging;
    j["ExAbnSymbFlg"] = obj.ExAbnSymbFlg;
    j["Equalizer"] = obj.Equalizer;
    return j;
}

inline void from_json(const json& j, Alg_4G_MeasureInModulation& obj) {
    if (j.contains("ModEnable")) {
        j["ModEnable"].get_to(obj.ModEnable);
    }
    if (j.contains("EvmEnable")) {
        j["EvmEnable"].get_to(obj.EvmEnable);
    }
    if (j.contains("MErrEnable")) {
        j["MErrEnable"].get_to(obj.MErrEnable);
    }
    if (j.contains("PErrEnable")) {
        j["PErrEnable"].get_to(obj.PErrEnable);
    }
    if (j.contains("EvmSubcarEnable")) {
        j["EvmSubcarEnable"].get_to(obj.EvmSubcarEnable);
    }
    if (j.contains("IBEEnable")) {
        j["IBEEnable"].get_to(obj.IBEEnable);
    }
    if (j.contains("ESFlatEnable")) {
        j["ESFlatEnable"].get_to(obj.ESFlatEnable);
    }
    if (j.contains("IQConstelEnable")) {
        j["IQConstelEnable"].get_to(obj.IQConstelEnable);
    }
    if (j.contains("TxMeasureEnable")) {
        j["TxMeasureEnable"].get_to(obj.TxMeasureEnable);
    }
    if (j.contains("ModStatNum")) {
        j["ModStatNum"].get_to(obj.ModStatNum);
    }
    if (j.contains("EvmWinNcp")) {
        const auto& arr = j["EvmWinNcp"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.EvmWinNcp)); ++i) {
            arr[i].get_to(obj.EvmWinNcp[i]);
        }
    }
    if (j.contains("EvmWinEcp")) {
        const auto& arr = j["EvmWinEcp"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.EvmWinEcp)); ++i) {
            arr[i].get_to(obj.EvmWinEcp[i]);
        }
    }
    if (j.contains("ExPeriodLeading")) {
        j["ExPeriodLeading"].get_to(obj.ExPeriodLeading);
    }
    if (j.contains("ExPeriodLagging")) {
        j["ExPeriodLagging"].get_to(obj.ExPeriodLagging);
    }
    if (j.contains("ExAbnSymbFlg")) {
        j["ExAbnSymbFlg"].get_to(obj.ExAbnSymbFlg);
    }
    if (j.contains("Equalizer")) {
        j["Equalizer"].get_to(obj.Equalizer);
    }
}

inline json to_json(const Alg_4G_MeasureInSpectrum& obj) {
    json j;
    j["SpectEnable"] = obj.SpectEnable;
    j["OBWEnable"] = obj.OBWEnable;
    j["SEMEnable"] = obj.SEMEnable;
    j["MeasFilter"] = obj.MeasFilter;
    j["SEMStatNum"] = obj.SEMStatNum;
    j["ACLREnable"] = obj.ACLREnable;
    j["UTRA1Enable"] = obj.UTRA1Enable;
    j["UTRA2Enable"] = obj.UTRA2Enable;
    j["EUTRA1Enable"] = obj.EUTRA1Enable;
    j["EUTRA2Enable"] = obj.EUTRA2Enable;
    j["ACLRStatNum"] = obj.ACLRStatNum;
    return j;
}

inline void from_json(const json& j, Alg_4G_MeasureInSpectrum& obj) {
    if (j.contains("SpectEnable")) {
        j["SpectEnable"].get_to(obj.SpectEnable);
    }
    if (j.contains("OBWEnable")) {
        j["OBWEnable"].get_to(obj.OBWEnable);
    }
    if (j.contains("SEMEnable")) {
        j["SEMEnable"].get_to(obj.SEMEnable);
    }
    if (j.contains("MeasFilter")) {
        j["MeasFilter"].get_to(obj.MeasFilter);
    }
    if (j.contains("SEMStatNum")) {
        j["SEMStatNum"].get_to(obj.SEMStatNum);
    }
    if (j.contains("ACLREnable")) {
        j["ACLREnable"].get_to(obj.ACLREnable);
    }
    if (j.contains("UTRA1Enable")) {
        j["UTRA1Enable"].get_to(obj.UTRA1Enable);
    }
    if (j.contains("UTRA2Enable")) {
        j["UTRA2Enable"].get_to(obj.UTRA2Enable);
    }
    if (j.contains("EUTRA1Enable")) {
        j["EUTRA1Enable"].get_to(obj.EUTRA1Enable);
    }
    if (j.contains("EUTRA2Enable")) {
        j["EUTRA2Enable"].get_to(obj.EUTRA2Enable);
    }
    if (j.contains("ACLRStatNum")) {
        j["ACLRStatNum"].get_to(obj.ACLRStatNum);
    }
}

inline json to_json(const Alg_4G_MeasureInPower& obj) {
    json j;
    j["PMonitorEnable"] = obj.PMonitorEnable;
    j["PowerEnable"] = obj.PowerEnable;
    j["PowerStatNum"] = obj.PowerStatNum;
    j["PwrDynEnable"] = obj.PwrDynEnable;
    j["TimeMaskType"] = obj.TimeMaskType;
    j["Leading"] = obj.Leading;
    j["Lagging"] = obj.Lagging;
    j["HighDynmMode"] = obj.HighDynmMode;
    return j;
}

inline void from_json(const json& j, Alg_4G_MeasureInPower& obj) {
    if (j.contains("PMonitorEnable")) {
        j["PMonitorEnable"].get_to(obj.PMonitorEnable);
    }
    if (j.contains("PowerEnable")) {
        j["PowerEnable"].get_to(obj.PowerEnable);
    }
    if (j.contains("PowerStatNum")) {
        j["PowerStatNum"].get_to(obj.PowerStatNum);
    }
    if (j.contains("PwrDynEnable")) {
        j["PwrDynEnable"].get_to(obj.PwrDynEnable);
    }
    if (j.contains("TimeMaskType")) {
        j["TimeMaskType"].get_to(obj.TimeMaskType);
    }
    if (j.contains("Leading")) {
        j["Leading"].get_to(obj.Leading);
    }
    if (j.contains("Lagging")) {
        j["Lagging"].get_to(obj.Lagging);
    }
    if (j.contains("HighDynmMode")) {
        j["HighDynmMode"].get_to(obj.HighDynmMode);
    }
}

inline json to_json(const Alg_3GPP_MeasureIn4g& obj) {
    json j;
    j["MeasSubfrmIdx"] = obj.MeasSubfrmIdx;
    j["MeasSubfrmCount"] = obj.MeasSubfrmCount;
    j["MeasSubfrmOffset"] = obj.MeasSubfrmOffset;
    j["MeasSlotType"] = obj.MeasSlotType;
    j["EvmSymbEnable"] = obj.EvmSymbEnable;
    j["EvmSymbIdx"] = obj.EvmSymbIdx;
    j["EvmSymbWinType"] = obj.EvmSymbWinType;
    j["DmrsConsState"] = obj.DmrsConsState;
    j["SyncMode"] = obj.SyncMode;
    j["MeasureUnit"] = obj.MeasureUnit;
    j["DecodingEnable"] = obj.DecodingEnable;
    j["Modulate"] = to_json(obj.Modulate);
    j["Spectrum"] = to_json(obj.Spectrum);
    j["Power"] = to_json(obj.Power);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_MeasureIn4g& obj) {
    if (j.contains("MeasSubfrmIdx")) {
        j["MeasSubfrmIdx"].get_to(obj.MeasSubfrmIdx);
    }
    if (j.contains("MeasSubfrmCount")) {
        j["MeasSubfrmCount"].get_to(obj.MeasSubfrmCount);
    }
    if (j.contains("MeasSubfrmOffset")) {
        j["MeasSubfrmOffset"].get_to(obj.MeasSubfrmOffset);
    }
    if (j.contains("MeasSlotType")) {
        j["MeasSlotType"].get_to(obj.MeasSlotType);
    }
    if (j.contains("EvmSymbEnable")) {
        j["EvmSymbEnable"].get_to(obj.EvmSymbEnable);
    }
    if (j.contains("EvmSymbIdx")) {
        j["EvmSymbIdx"].get_to(obj.EvmSymbIdx);
    }
    if (j.contains("EvmSymbWinType")) {
        j["EvmSymbWinType"].get_to(obj.EvmSymbWinType);
    }
    if (j.contains("DmrsConsState")) {
        j["DmrsConsState"].get_to(obj.DmrsConsState);
    }
    if (j.contains("SyncMode")) {
        j["SyncMode"].get_to(obj.SyncMode);
    }
    if (j.contains("MeasureUnit")) {
        j["MeasureUnit"].get_to(obj.MeasureUnit);
    }
    if (j.contains("DecodingEnable")) {
        j["DecodingEnable"].get_to(obj.DecodingEnable);
    }
    if (j.contains("Modulate")) {
        from_json(j["Modulate"], obj.Modulate);
    }
    if (j.contains("Spectrum")) {
        from_json(j["Spectrum"], obj.Spectrum);
    }
    if (j.contains("Power")) {
        from_json(j["Power"], obj.Power);
    }
}

inline json to_json(const Alg_3GPP_IQOffsetLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["PwrLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.PwrLimit); ++i) {
        j["PwrLimit"].emplace_back(obj.PwrLimit[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_IQOffsetLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("PwrLimit")) {
        const auto& arr = j["PwrLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.PwrLimit)); ++i) {
            arr[i].get_to(obj.PwrLimit[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_IBELimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["GenMin"] = obj.GenMin;
    j["GenEVM"] = obj.GenEVM;
    j["GenPwr"] = obj.GenPwr;
    j["IQImage"] = json::array();
    for (size_t i = 0; i < arraySize(obj.IQImage); ++i) {
        j["IQImage"].emplace_back(obj.IQImage[i]);
    }
    j["IQOffsetPwr"] = json::array();
    for (size_t i = 0; i < arraySize(obj.IQOffsetPwr); ++i) {
        j["IQOffsetPwr"].emplace_back(obj.IQOffsetPwr[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_IBELimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("GenMin")) {
        j["GenMin"].get_to(obj.GenMin);
    }
    if (j.contains("GenEVM")) {
        j["GenEVM"].get_to(obj.GenEVM);
    }
    if (j.contains("GenPwr")) {
        j["GenPwr"].get_to(obj.GenPwr);
    }
    if (j.contains("IQImage")) {
        const auto& arr = j["IQImage"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.IQImage)); ++i) {
            arr[i].get_to(obj.IQImage[i]);
        }
    }
    if (j.contains("IQOffsetPwr")) {
        const auto& arr = j["IQOffsetPwr"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.IQOffsetPwr)); ++i) {
            arr[i].get_to(obj.IQOffsetPwr[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_ESFlatLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["Range1"] = obj.Range1;
    j["Range2"] = obj.Range2;
    j["Max1Min2"] = obj.Max1Min2;
    j["Max2Min1"] = obj.Max2Min1;
    j["EdgeFreq"] = obj.EdgeFreq;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_ESFlatLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("Range1")) {
        j["Range1"].get_to(obj.Range1);
    }
    if (j.contains("Range2")) {
        j["Range2"].get_to(obj.Range2);
    }
    if (j.contains("Max1Min2")) {
        j["Max1Min2"].get_to(obj.Max1Min2);
    }
    if (j.contains("Max2Min1")) {
        j["Max2Min1"].get_to(obj.Max2Min1);
    }
    if (j.contains("EdgeFreq")) {
        j["EdgeFreq"].get_to(obj.EdgeFreq);
    }
}

inline json to_json(const Alg_4G_ModulateLimitType& obj) {
    json j;
    j["EvmRms"] = to_json(obj.EvmRms);
    j["EvmPeak"] = to_json(obj.EvmPeak);
    j["MErrRms"] = to_json(obj.MErrRms);
    j["MErrPeak"] = to_json(obj.MErrPeak);
    j["PhErrRms"] = to_json(obj.PhErrRms);
    j["PhErrPeak"] = to_json(obj.PhErrPeak);
    j["FreqErr"] = to_json(obj.FreqErr);
    j["IQOffset"] = to_json(obj.IQOffset);
    j["IBE"] = to_json(obj.IBE);
    j["SpectFlat"] = to_json(obj.SpectFlat);
    return j;
}

inline void from_json(const json& j, Alg_4G_ModulateLimitType& obj) {
    if (j.contains("EvmRms")) {
        from_json(j["EvmRms"], obj.EvmRms);
    }
    if (j.contains("EvmPeak")) {
        from_json(j["EvmPeak"], obj.EvmPeak);
    }
    if (j.contains("MErrRms")) {
        from_json(j["MErrRms"], obj.MErrRms);
    }
    if (j.contains("MErrPeak")) {
        from_json(j["MErrPeak"], obj.MErrPeak);
    }
    if (j.contains("PhErrRms")) {
        from_json(j["PhErrRms"], obj.PhErrRms);
    }
    if (j.contains("PhErrPeak")) {
        from_json(j["PhErrPeak"], obj.PhErrPeak);
    }
    if (j.contains("FreqErr")) {
        from_json(j["FreqErr"], obj.FreqErr);
    }
    if (j.contains("IQOffset")) {
        from_json(j["IQOffset"], obj.IQOffset);
    }
    if (j.contains("IBE")) {
        from_json(j["IBE"], obj.IBE);
    }
    if (j.contains("SpectFlat")) {
        from_json(j["SpectFlat"], obj.SpectFlat);
    }
}

inline json to_json(const Alg_3GPP_SEMLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["StartFreq"] = obj.StartFreq;
    j["StopFreq"] = obj.StopFreq;
    j["LimitPower"] = obj.LimitPower;
    j["RBW"] = obj.RBW;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_SEMLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("StartFreq")) {
        j["StartFreq"].get_to(obj.StartFreq);
    }
    if (j.contains("StopFreq")) {
        j["StopFreq"].get_to(obj.StopFreq);
    }
    if (j.contains("LimitPower")) {
        j["LimitPower"].get_to(obj.LimitPower);
    }
    if (j.contains("RBW")) {
        j["RBW"].get_to(obj.RBW);
    }
}

inline json to_json(const Alg_3GPP_AclrLimitType& obj) {
    json j;
    j["RelState"] = obj.RelState;
    j["AbsState"] = obj.AbsState;
    j["RelLimit"] = obj.RelLimit;
    j["AbsPwr"] = obj.AbsPwr;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AclrLimitType& obj) {
    if (j.contains("RelState")) {
        j["RelState"].get_to(obj.RelState);
    }
    if (j.contains("AbsState")) {
        j["AbsState"].get_to(obj.AbsState);
    }
    if (j.contains("RelLimit")) {
        j["RelLimit"].get_to(obj.RelLimit);
    }
    if (j.contains("AbsPwr")) {
        j["AbsPwr"].get_to(obj.AbsPwr);
    }
}

inline json to_json(const Alg_4G_SpectrumLimitType& obj) {
    json j;
    j["OBWLimit"] = to_json(obj.OBWLimit);
    j["SEMLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMLimit); ++i) {
        json arr_i = json::array();
        for (size_t j = 0; j < arraySize(obj.SEMLimit[i]); ++j) {
            arr_i.emplace_back(to_json(obj.SEMLimit[i][j]));
        }
        j["SEMLimit"].emplace_back(arr_i);
    }
    j["UtraLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.UtraLimit); ++i) {
        j["UtraLimit"].emplace_back(to_json(obj.UtraLimit[i]));
    }
    j["EUtraLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.EUtraLimit); ++i) {
        j["EUtraLimit"].emplace_back(to_json(obj.EUtraLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_4G_SpectrumLimitType& obj) {
    if (j.contains("OBWLimit")) {
        from_json(j["OBWLimit"], obj.OBWLimit);
    }
    if (j.contains("SEMLimit")) {
        const auto& arr = j["SEMLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMLimit)); ++i) {
            const auto& arr_i = arr[i];
            for (size_t j = 0; j < std::min(arr_i.size(), arraySize(obj.SEMLimit[i])); ++j) {
                from_json(arr_i[j], obj.SEMLimit[i][j]);
            }
        }
    }
    if (j.contains("UtraLimit")) {
        const auto& arr = j["UtraLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.UtraLimit)); ++i) {
            from_json(arr[i], obj.UtraLimit[i]);
        }
    }
    if (j.contains("EUtraLimit")) {
        const auto& arr = j["EUtraLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.EUtraLimit)); ++i) {
            from_json(arr[i], obj.EUtraLimit[i]);
        }
    }
}

inline json to_json(const Alg_4G_PowerLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["OnLimitUpper"] = obj.OnLimitUpper;
    j["OnLimitLower"] = obj.OnLimitLower;
    j["OffLimit"] = obj.OffLimit;
    return j;
}

inline void from_json(const json& j, Alg_4G_PowerLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("OnLimitUpper")) {
        j["OnLimitUpper"].get_to(obj.OnLimitUpper);
    }
    if (j.contains("OnLimitLower")) {
        j["OnLimitLower"].get_to(obj.OnLimitLower);
    }
    if (j.contains("OffLimit")) {
        j["OffLimit"].get_to(obj.OffLimit);
    }
}

inline json to_json(const Alg_3GPP_LimitIn4g& obj) {
    json j;
    j["ModLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.ModLimit); ++i) {
        j["ModLimit"].emplace_back(to_json(obj.ModLimit[i]));
    }
    j["SpectLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SpectLimit); ++i) {
        j["SpectLimit"].emplace_back(to_json(obj.SpectLimit[i]));
    }
    j["SEMAddTestTol"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMAddTestTol); ++i) {
        j["SEMAddTestTol"].emplace_back(obj.SEMAddTestTol[i]);
    }
    j["PwrLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.PwrLimit); ++i) {
        j["PwrLimit"].emplace_back(to_json(obj.PwrLimit[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_LimitIn4g& obj) {
    if (j.contains("ModLimit")) {
        const auto& arr = j["ModLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.ModLimit)); ++i) {
            from_json(arr[i], obj.ModLimit[i]);
        }
    }
    if (j.contains("SpectLimit")) {
        const auto& arr = j["SpectLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SpectLimit)); ++i) {
            from_json(arr[i], obj.SpectLimit[i]);
        }
    }
    if (j.contains("SEMAddTestTol")) {
        const auto& arr = j["SEMAddTestTol"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMAddTestTol)); ++i) {
            arr[i].get_to(obj.SEMAddTestTol[i]);
        }
    }
    if (j.contains("PwrLimit")) {
        const auto& arr = j["PwrLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.PwrLimit)); ++i) {
            from_json(arr[i], obj.PwrLimit[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzIn4g& obj) {
    json j;
    j["CarrAggrState"] = obj.CarrAggrState;
    j["LinkDirect"] = obj.LinkDirect;
    j["Cell"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Cell); ++i) {
        j["Cell"].emplace_back(to_json(obj.Cell[i]));
    }
    j["CyclicPrefix"] = obj.CyclicPrefix;
    j["UeID"] = obj.UeID;
    j["NSValue"] = obj.NSValue;
    j["ChanType"] = obj.ChanType;
    j["Pusch"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Pusch); ++i) {
        j["Pusch"].emplace_back(to_json(obj.Pusch[i]));
    }
    j["Pdsch"] = to_json(obj.Pdsch);
    j["MeasInfo"] = to_json(obj.MeasInfo);
    j["LimitInfo"] = to_json(obj.LimitInfo);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzIn4g& obj) {
    if (j.contains("CarrAggrState")) {
        j["CarrAggrState"].get_to(obj.CarrAggrState);
    }
    if (j.contains("LinkDirect")) {
        j["LinkDirect"].get_to(obj.LinkDirect);
    }
    if (j.contains("Cell")) {
        const auto& arr = j["Cell"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Cell)); ++i) {
            from_json(arr[i], obj.Cell[i]);
        }
    }
    if (j.contains("CyclicPrefix")) {
        j["CyclicPrefix"].get_to(obj.CyclicPrefix);
    }
    if (j.contains("UeID")) {
        j["UeID"].get_to(obj.UeID);
    }
    if (j.contains("NSValue")) {
        j["NSValue"].get_to(obj.NSValue);
    }
    if (j.contains("ChanType")) {
        j["ChanType"].get_to(obj.ChanType);
    }
    if (j.contains("Pusch")) {
        const auto& arr = j["Pusch"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Pusch)); ++i) {
            from_json(arr[i], obj.Pusch[i]);
        }
    }
    if (j.contains("Pdsch")) {
        from_json(j["Pdsch"], obj.Pdsch);
    }
    if (j.contains("MeasInfo")) {
        from_json(j["MeasInfo"], obj.MeasInfo);
    }
    if (j.contains("LimitInfo")) {
        from_json(j["LimitInfo"], obj.LimitInfo);
    }
}

inline json to_json(const Alg_5G_VsaTDDPatType& obj) {
    json j;
    j["ULSlotNumber"] = obj.ULSlotNumber;
    j["DLSlotNumber"] = obj.DLSlotNumber;
    j["ULSymbNumber"] = obj.ULSymbNumber;
    j["DLSymbNumber"] = obj.DLSymbNumber;
    return j;
}

inline void from_json(const json& j, Alg_5G_VsaTDDPatType& obj) {
    if (j.contains("ULSlotNumber")) {
        j["ULSlotNumber"].get_to(obj.ULSlotNumber);
    }
    if (j.contains("DLSlotNumber")) {
        j["DLSlotNumber"].get_to(obj.DLSlotNumber);
    }
    if (j.contains("ULSymbNumber")) {
        j["ULSymbNumber"].get_to(obj.ULSymbNumber);
    }
    if (j.contains("DLSymbNumber")) {
        j["DLSymbNumber"].get_to(obj.DLSymbNumber);
    }
}

inline json to_json(const Alg_3GPP_AlzULBwpCfg& obj) {
    json j;
    j["SCSpacing"] = obj.SCSpacing;
    j["CyclicPrefix"] = obj.CyclicPrefix;
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["ConfigType"] = json::array();
    for (size_t i = 0; i < arraySize(obj.ConfigType); ++i) {
        j["ConfigType"].emplace_back(obj.ConfigType[i]);
    }
    j["MaxLength"] = json::array();
    for (size_t i = 0; i < arraySize(obj.MaxLength); ++i) {
        j["MaxLength"].emplace_back(obj.MaxLength[i]);
    }
    j["AdditionalPos"] = json::array();
    for (size_t i = 0; i < arraySize(obj.AdditionalPos); ++i) {
        j["AdditionalPos"].emplace_back(obj.AdditionalPos[i]);
    }
    j["UseR16Dmrs"] = obj.UseR16Dmrs;
    j["TransformPrecoder"] = obj.TransformPrecoder;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzULBwpCfg& obj) {
    if (j.contains("SCSpacing")) {
        j["SCSpacing"].get_to(obj.SCSpacing);
    }
    if (j.contains("CyclicPrefix")) {
        j["CyclicPrefix"].get_to(obj.CyclicPrefix);
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("ConfigType")) {
        const auto& arr = j["ConfigType"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.ConfigType)); ++i) {
            arr[i].get_to(obj.ConfigType[i]);
        }
    }
    if (j.contains("MaxLength")) {
        const auto& arr = j["MaxLength"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.MaxLength)); ++i) {
            arr[i].get_to(obj.MaxLength[i]);
        }
    }
    if (j.contains("AdditionalPos")) {
        const auto& arr = j["AdditionalPos"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.AdditionalPos)); ++i) {
            arr[i].get_to(obj.AdditionalPos[i]);
        }
    }
    if (j.contains("UseR16Dmrs")) {
        j["UseR16Dmrs"].get_to(obj.UseR16Dmrs);
    }
    if (j.contains("TransformPrecoder")) {
        j["TransformPrecoder"].get_to(obj.TransformPrecoder);
    }
}

inline json to_json(const Alg_3GPP_AlzULCell5g& obj) {
    json j;
    j["ChannelBW"] = obj.ChannelBW;
    j["PhyCellID"] = obj.PhyCellID;
    j["DmrsTypeAPos"] = obj.DmrsTypeAPos;
    j["UseSCSpacing"] = obj.UseSCSpacing;
    j["OffsetToCarrier"] = obj.OffsetToCarrier;
    j["Bwp"] = to_json(obj.Bwp);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzULCell5g& obj) {
    if (j.contains("ChannelBW")) {
        j["ChannelBW"].get_to(obj.ChannelBW);
    }
    if (j.contains("PhyCellID")) {
        j["PhyCellID"].get_to(obj.PhyCellID);
    }
    if (j.contains("DmrsTypeAPos")) {
        j["DmrsTypeAPos"].get_to(obj.DmrsTypeAPos);
    }
    if (j.contains("UseSCSpacing")) {
        j["UseSCSpacing"].get_to(obj.UseSCSpacing);
    }
    if (j.contains("OffsetToCarrier")) {
        j["OffsetToCarrier"].get_to(obj.OffsetToCarrier);
    }
    if (j.contains("Bwp")) {
        from_json(j["Bwp"], obj.Bwp);
    }
}

inline json to_json(const Alg_3GPP_AlzPusch5g& obj) {
    json j;
    j["MappingType"] = obj.MappingType;
    j["SymNum"] = obj.SymNum;
    j["SymbOffset"] = obj.SymbOffset;
    j["RBDetMode"] = obj.RBDetMode;
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["Modulate"] = obj.Modulate;
    j["CDMGrpWOData"] = obj.CDMGrpWOData;
    j["DmrsSymbLen"] = obj.DmrsSymbLen;
    j["DmrsAntPort"] = json::array();
    for (size_t i = 0; i < arraySize(obj.DmrsAntPort); ++i) {
        j["DmrsAntPort"].emplace_back(obj.DmrsAntPort[i]);
    }
    j["DmrsInitType"] = obj.DmrsInitType;
    j["DmrsID"] = obj.DmrsID;
    j["NSCID"] = obj.NSCID;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzPusch5g& obj) {
    if (j.contains("MappingType")) {
        j["MappingType"].get_to(obj.MappingType);
    }
    if (j.contains("SymNum")) {
        j["SymNum"].get_to(obj.SymNum);
    }
    if (j.contains("SymbOffset")) {
        j["SymbOffset"].get_to(obj.SymbOffset);
    }
    if (j.contains("RBDetMode")) {
        j["RBDetMode"].get_to(obj.RBDetMode);
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("Modulate")) {
        j["Modulate"].get_to(obj.Modulate);
    }
    if (j.contains("CDMGrpWOData")) {
        j["CDMGrpWOData"].get_to(obj.CDMGrpWOData);
    }
    if (j.contains("DmrsSymbLen")) {
        j["DmrsSymbLen"].get_to(obj.DmrsSymbLen);
    }
    if (j.contains("DmrsAntPort")) {
        const auto& arr = j["DmrsAntPort"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.DmrsAntPort)); ++i) {
            arr[i].get_to(obj.DmrsAntPort[i]);
        }
    }
    if (j.contains("DmrsInitType")) {
        j["DmrsInitType"].get_to(obj.DmrsInitType);
    }
    if (j.contains("DmrsID")) {
        j["DmrsID"].get_to(obj.DmrsID);
    }
    if (j.contains("NSCID")) {
        j["NSCID"].get_to(obj.NSCID);
    }
}

inline json to_json(const Alg_3GPP_AlzULIn5g& obj) {
    json j;
    j["RfPhaseCompen"] = obj.RfPhaseCompen;
    j["Frequency"] = obj.Frequency;
    j["NSValue"] = obj.NSValue;
    j["Duplexing"] = obj.Duplexing;
    j["SlotPeriod"] = obj.SlotPeriod;
    j["TDDPat"] = json::array();
    for (size_t i = 0; i < arraySize(obj.TDDPat); ++i) {
        j["TDDPat"].emplace_back(to_json(obj.TDDPat[i]));
    }
    j["CellNum"] = obj.CellNum;
    j["Cell"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Cell); ++i) {
        j["Cell"].emplace_back(to_json(obj.Cell[i]));
    }
    j["ChanType"] = obj.ChanType;
    j["Pusch"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Pusch); ++i) {
        j["Pusch"].emplace_back(to_json(obj.Pusch[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzULIn5g& obj) {
    if (j.contains("RfPhaseCompen")) {
        j["RfPhaseCompen"].get_to(obj.RfPhaseCompen);
    }
    if (j.contains("Frequency")) {
        j["Frequency"].get_to(obj.Frequency);
    }
    if (j.contains("NSValue")) {
        j["NSValue"].get_to(obj.NSValue);
    }
    if (j.contains("Duplexing")) {
        j["Duplexing"].get_to(obj.Duplexing);
    }
    if (j.contains("SlotPeriod")) {
        j["SlotPeriod"].get_to(obj.SlotPeriod);
    }
    if (j.contains("TDDPat")) {
        const auto& arr = j["TDDPat"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.TDDPat)); ++i) {
            from_json(arr[i], obj.TDDPat[i]);
        }
    }
    if (j.contains("CellNum")) {
        j["CellNum"].get_to(obj.CellNum);
    }
    if (j.contains("Cell")) {
        const auto& arr = j["Cell"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Cell)); ++i) {
            from_json(arr[i], obj.Cell[i]);
        }
    }
    if (j.contains("ChanType")) {
        j["ChanType"].get_to(obj.ChanType);
    }
    if (j.contains("Pusch")) {
        const auto& arr = j["Pusch"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Pusch)); ++i) {
            from_json(arr[i], obj.Pusch[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzDLBwpPdschCfg& obj) {
    json j;
    j["VrbToPrbInterleaver"] = obj.VrbToPrbInterleaver;
    j["McsTab"] = obj.McsTab;
    j["ResourceAllocation"] = obj.ResourceAllocation;
    j["ConfigType"] = obj.ConfigType;
    j["MaxLength"] = obj.MaxLength;
    j["AdditionalPos"] = obj.AdditionalPos;
    j["RBGSizeType"] = obj.RBGSizeType;
    j["UseR16Dmrs"] = obj.UseR16Dmrs;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLBwpPdschCfg& obj) {
    if (j.contains("VrbToPrbInterleaver")) {
        j["VrbToPrbInterleaver"].get_to(obj.VrbToPrbInterleaver);
    }
    if (j.contains("McsTab")) {
        j["McsTab"].get_to(obj.McsTab);
    }
    if (j.contains("ResourceAllocation")) {
        j["ResourceAllocation"].get_to(obj.ResourceAllocation);
    }
    if (j.contains("ConfigType")) {
        j["ConfigType"].get_to(obj.ConfigType);
    }
    if (j.contains("MaxLength")) {
        j["MaxLength"].get_to(obj.MaxLength);
    }
    if (j.contains("AdditionalPos")) {
        j["AdditionalPos"].get_to(obj.AdditionalPos);
    }
    if (j.contains("RBGSizeType")) {
        j["RBGSizeType"].get_to(obj.RBGSizeType);
    }
    if (j.contains("UseR16Dmrs")) {
        j["UseR16Dmrs"].get_to(obj.UseR16Dmrs);
    }
}

inline json to_json(const Alg_3GPP_AlzDLBwpCoresetCfg& obj) {
    json j;
    j["State"] = obj.State;
    j["SymbNum"] = obj.SymbNum;
    j["SymbOffset"] = obj.SymbOffset;
    j["FDResUseBitmap"] = obj.FDResUseBitmap;
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["BitMap"] = json::array();
    for (size_t i = 0; i < arraySize(obj.BitMap); ++i) {
        j["BitMap"].emplace_back(obj.BitMap[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLBwpCoresetCfg& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("SymbNum")) {
        j["SymbNum"].get_to(obj.SymbNum);
    }
    if (j.contains("SymbOffset")) {
        j["SymbOffset"].get_to(obj.SymbOffset);
    }
    if (j.contains("FDResUseBitmap")) {
        j["FDResUseBitmap"].get_to(obj.FDResUseBitmap);
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("BitMap")) {
        const auto& arr = j["BitMap"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.BitMap)); ++i) {
            arr[i].get_to(obj.BitMap[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzDLBwpCfg& obj) {
    json j;
    j["SCSpacing"] = obj.SCSpacing;
    j["CyclicPrefix"] = obj.CyclicPrefix;
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["Pdsch"] = to_json(obj.Pdsch);
    j["Coreset"] = to_json(obj.Coreset);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLBwpCfg& obj) {
    if (j.contains("SCSpacing")) {
        j["SCSpacing"].get_to(obj.SCSpacing);
    }
    if (j.contains("CyclicPrefix")) {
        j["CyclicPrefix"].get_to(obj.CyclicPrefix);
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("Pdsch")) {
        from_json(j["Pdsch"], obj.Pdsch);
    }
    if (j.contains("Coreset")) {
        from_json(j["Coreset"], obj.Coreset);
    }
}

inline json to_json(const Alg_3GPP_AlzDLCell5g& obj) {
    json j;
    j["ChannelBW"] = obj.ChannelBW;
    j["PhyCellID"] = obj.PhyCellID;
    j["DmrsTypeAPos"] = obj.DmrsTypeAPos;
    j["UseSCSpacing"] = obj.UseSCSpacing;
    j["OffsetToCarrier"] = obj.OffsetToCarrier;
    j["Bwp"] = to_json(obj.Bwp);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLCell5g& obj) {
    if (j.contains("ChannelBW")) {
        j["ChannelBW"].get_to(obj.ChannelBW);
    }
    if (j.contains("PhyCellID")) {
        j["PhyCellID"].get_to(obj.PhyCellID);
    }
    if (j.contains("DmrsTypeAPos")) {
        j["DmrsTypeAPos"].get_to(obj.DmrsTypeAPos);
    }
    if (j.contains("UseSCSpacing")) {
        j["UseSCSpacing"].get_to(obj.UseSCSpacing);
    }
    if (j.contains("OffsetToCarrier")) {
        j["OffsetToCarrier"].get_to(obj.OffsetToCarrier);
    }
    if (j.contains("Bwp")) {
        from_json(j["Bwp"], obj.Bwp);
    }
}

inline json to_json(const Alg_3GPP_AlzPbch5g& obj) {
    json j;
    j["State"] = obj.State;
    j["SCSpacing"] = obj.SCSpacing;
    j["RBOffset"] = obj.RBOffset;
    j["SCOffset"] = obj.SCOffset;
    j["PbchCase"] = obj.PbchCase;
    j["Length"] = obj.Length;
    j["Position"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Position); ++i) {
        j["Position"].emplace_back(obj.Position[i]);
    }
    j["BurstSetPeriod"] = obj.BurstSetPeriod;
    j["HalfFrmIdx"] = obj.HalfFrmIdx;
    j["OffsetRefType"] = obj.OffsetRefType;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzPbch5g& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("SCSpacing")) {
        j["SCSpacing"].get_to(obj.SCSpacing);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("SCOffset")) {
        j["SCOffset"].get_to(obj.SCOffset);
    }
    if (j.contains("PbchCase")) {
        j["PbchCase"].get_to(obj.PbchCase);
    }
    if (j.contains("Length")) {
        j["Length"].get_to(obj.Length);
    }
    if (j.contains("Position")) {
        const auto& arr = j["Position"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Position)); ++i) {
            arr[i].get_to(obj.Position[i]);
        }
    }
    if (j.contains("BurstSetPeriod")) {
        j["BurstSetPeriod"].get_to(obj.BurstSetPeriod);
    }
    if (j.contains("HalfFrmIdx")) {
        j["HalfFrmIdx"].get_to(obj.HalfFrmIdx);
    }
    if (j.contains("OffsetRefType")) {
        j["OffsetRefType"].get_to(obj.OffsetRefType);
    }
}

inline json to_json(const Alg_3GPP_AlzPdcch5g& obj) {
    json j;
    j["State"] = obj.State;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzPdcch5g& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
}

inline json to_json(const Alg_3GPP_AlzPdsch5g& obj) {
    json j;
    j["MappingType"] = obj.MappingType;
    j["SymbNum"] = obj.SymbNum;
    j["SymbOffset"] = obj.SymbOffset;
    j["RBNum"] = obj.RBNum;
    j["RBOffset"] = obj.RBOffset;
    j["LayerNum"] = obj.LayerNum;
    j["AntennaNum"] = obj.AntennaNum;
    j["CDMGrpWOData"] = obj.CDMGrpWOData;
    j["DmrsSymbLen"] = obj.DmrsSymbLen;
    j["DmrsAntPort"] = json::array();
    for (size_t i = 0; i < arraySize(obj.DmrsAntPort); ++i) {
        j["DmrsAntPort"].emplace_back(obj.DmrsAntPort[i]);
    }
    j["DmrsInitType"] = obj.DmrsInitType;
    j["DmrsID"] = obj.DmrsID;
    j["NSCID"] = obj.NSCID;
    j["Codewords"] = obj.Codewords;
    j["Modulate"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Modulate); ++i) {
        j["Modulate"].emplace_back(obj.Modulate[i]);
    }
    j["ChanCodingState"] = obj.ChanCodingState;
    j["Scrambling"] = obj.Scrambling;
    j["UsePdschScrambleID"] = obj.UsePdschScrambleID;
    j["DataScrambleID"] = obj.DataScrambleID;
    j["UeID"] = obj.UeID;
    j["MCS"] = json::array();
    for (size_t i = 0; i < arraySize(obj.MCS); ++i) {
        j["MCS"].emplace_back(obj.MCS[i]);
    }
    j["RvIdx"] = json::array();
    for (size_t i = 0; i < arraySize(obj.RvIdx); ++i) {
        j["RvIdx"].emplace_back(obj.RvIdx[i]);
    }
    j["Bitmap"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Bitmap); ++i) {
        j["Bitmap"].emplace_back(obj.Bitmap[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzPdsch5g& obj) {
    if (j.contains("MappingType")) {
        j["MappingType"].get_to(obj.MappingType);
    }
    if (j.contains("SymbNum")) {
        j["SymbNum"].get_to(obj.SymbNum);
    }
    if (j.contains("SymbOffset")) {
        j["SymbOffset"].get_to(obj.SymbOffset);
    }
    if (j.contains("RBNum")) {
        j["RBNum"].get_to(obj.RBNum);
    }
    if (j.contains("RBOffset")) {
        j["RBOffset"].get_to(obj.RBOffset);
    }
    if (j.contains("LayerNum")) {
        j["LayerNum"].get_to(obj.LayerNum);
    }
    if (j.contains("AntennaNum")) {
        j["AntennaNum"].get_to(obj.AntennaNum);
    }
    if (j.contains("CDMGrpWOData")) {
        j["CDMGrpWOData"].get_to(obj.CDMGrpWOData);
    }
    if (j.contains("DmrsSymbLen")) {
        j["DmrsSymbLen"].get_to(obj.DmrsSymbLen);
    }
    if (j.contains("DmrsAntPort")) {
        const auto& arr = j["DmrsAntPort"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.DmrsAntPort)); ++i) {
            arr[i].get_to(obj.DmrsAntPort[i]);
        }
    }
    if (j.contains("DmrsInitType")) {
        j["DmrsInitType"].get_to(obj.DmrsInitType);
    }
    if (j.contains("DmrsID")) {
        j["DmrsID"].get_to(obj.DmrsID);
    }
    if (j.contains("NSCID")) {
        j["NSCID"].get_to(obj.NSCID);
    }
    if (j.contains("Codewords")) {
        j["Codewords"].get_to(obj.Codewords);
    }
    if (j.contains("Modulate")) {
        const auto& arr = j["Modulate"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Modulate)); ++i) {
            arr[i].get_to(obj.Modulate[i]);
        }
    }
    if (j.contains("ChanCodingState")) {
        j["ChanCodingState"].get_to(obj.ChanCodingState);
    }
    if (j.contains("Scrambling")) {
        j["Scrambling"].get_to(obj.Scrambling);
    }
    if (j.contains("UsePdschScrambleID")) {
        j["UsePdschScrambleID"].get_to(obj.UsePdschScrambleID);
    }
    if (j.contains("DataScrambleID")) {
        j["DataScrambleID"].get_to(obj.DataScrambleID);
    }
    if (j.contains("UeID")) {
        j["UeID"].get_to(obj.UeID);
    }
    if (j.contains("MCS")) {
        const auto& arr = j["MCS"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.MCS)); ++i) {
            arr[i].get_to(obj.MCS[i]);
        }
    }
    if (j.contains("RvIdx")) {
        const auto& arr = j["RvIdx"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.RvIdx)); ++i) {
            arr[i].get_to(obj.RvIdx[i]);
        }
    }
    if (j.contains("Bitmap")) {
        const auto& arr = j["Bitmap"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Bitmap)); ++i) {
            arr[i].get_to(obj.Bitmap[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzChannel5g& obj) {
    json j;
    j["Pbch"] = to_json(obj.Pbch);
    j["Pdcch"] = to_json(obj.Pdcch);
    j["Pdsch"] = to_json(obj.Pdsch);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzChannel5g& obj) {
    if (j.contains("Pbch")) {
        from_json(j["Pbch"], obj.Pbch);
    }
    if (j.contains("Pdcch")) {
        from_json(j["Pdcch"], obj.Pdcch);
    }
    if (j.contains("Pdsch")) {
        from_json(j["Pdsch"], obj.Pdsch);
    }
}

inline json to_json(const Alg_3GPP_AlzDLIn5g& obj) {
    json j;
    j["RfPhaseCompen"] = obj.RfPhaseCompen;
    j["Frequency"] = obj.Frequency;
    j["Duplexing"] = obj.Duplexing;
    j["SlotPeriod"] = obj.SlotPeriod;
    j["TDDPat"] = json::array();
    for (size_t i = 0; i < arraySize(obj.TDDPat); ++i) {
        j["TDDPat"].emplace_back(to_json(obj.TDDPat[i]));
    }
    j["CellNum"] = obj.CellNum;
    j["Cell"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Cell); ++i) {
        j["Cell"].emplace_back(to_json(obj.Cell[i]));
    }
    j["ChanType"] = obj.ChanType;
    j["Channel"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Channel); ++i) {
        j["Channel"].emplace_back(to_json(obj.Channel[i]));
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLIn5g& obj) {
    if (j.contains("RfPhaseCompen")) {
        j["RfPhaseCompen"].get_to(obj.RfPhaseCompen);
    }
    if (j.contains("Frequency")) {
        j["Frequency"].get_to(obj.Frequency);
    }
    if (j.contains("Duplexing")) {
        j["Duplexing"].get_to(obj.Duplexing);
    }
    if (j.contains("SlotPeriod")) {
        j["SlotPeriod"].get_to(obj.SlotPeriod);
    }
    if (j.contains("TDDPat")) {
        const auto& arr = j["TDDPat"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.TDDPat)); ++i) {
            from_json(arr[i], obj.TDDPat[i]);
        }
    }
    if (j.contains("CellNum")) {
        j["CellNum"].get_to(obj.CellNum);
    }
    if (j.contains("Cell")) {
        const auto& arr = j["Cell"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Cell)); ++i) {
            from_json(arr[i], obj.Cell[i]);
        }
    }
    if (j.contains("ChanType")) {
        j["ChanType"].get_to(obj.ChanType);
    }
    if (j.contains("Channel")) {
        const auto& arr = j["Channel"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Channel)); ++i) {
            from_json(arr[i], obj.Channel[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_ViewSet5g& obj) {
    json j;
    j["ModEnable"] = obj.ModEnable;
    j["EvmEnable"] = obj.EvmEnable;
    j["MErrEnable"] = obj.MErrEnable;
    j["PErrEnable"] = obj.PErrEnable;
    j["EvmSubcarEnable"] = obj.EvmSubcarEnable;
    j["IBEEnable"] = obj.IBEEnable;
    j["ESFlatEnable"] = obj.ESFlatEnable;
    j["IQConstelEnable"] = obj.IQConstelEnable;
    j["TxMeasureEnable"] = obj.TxMeasureEnable;
    j["SpectEnable"] = obj.SpectEnable;
    j["OBWEnable"] = obj.OBWEnable;
    j["SEMEnable"] = obj.SEMEnable;
    j["ACLREnable"] = obj.ACLREnable;
    j["PwrDynEnable"] = obj.PwrDynEnable;
    j["TxPwrEnable"] = obj.TxPwrEnable;
    j["PMonitorEnable"] = obj.PMonitorEnable;
    j["SCHDecEnable"] = obj.SCHDecEnable;
    j["SSBDecEnable"] = obj.SSBDecEnable;
    j["DmrsConsState"] = obj.DmrsConsState;
    j["EvmSymbEnable"] = obj.EvmSymbEnable;
    j["EvmSymbIndx"] = obj.EvmSymbIndx;
    j["EvmSymbPosType"] = obj.EvmSymbPosType;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_ViewSet5g& obj) {
    if (j.contains("ModEnable")) {
        j["ModEnable"].get_to(obj.ModEnable);
    }
    if (j.contains("EvmEnable")) {
        j["EvmEnable"].get_to(obj.EvmEnable);
    }
    if (j.contains("MErrEnable")) {
        j["MErrEnable"].get_to(obj.MErrEnable);
    }
    if (j.contains("PErrEnable")) {
        j["PErrEnable"].get_to(obj.PErrEnable);
    }
    if (j.contains("EvmSubcarEnable")) {
        j["EvmSubcarEnable"].get_to(obj.EvmSubcarEnable);
    }
    if (j.contains("IBEEnable")) {
        j["IBEEnable"].get_to(obj.IBEEnable);
    }
    if (j.contains("ESFlatEnable")) {
        j["ESFlatEnable"].get_to(obj.ESFlatEnable);
    }
    if (j.contains("IQConstelEnable")) {
        j["IQConstelEnable"].get_to(obj.IQConstelEnable);
    }
    if (j.contains("TxMeasureEnable")) {
        j["TxMeasureEnable"].get_to(obj.TxMeasureEnable);
    }
    if (j.contains("SpectEnable")) {
        j["SpectEnable"].get_to(obj.SpectEnable);
    }
    if (j.contains("OBWEnable")) {
        j["OBWEnable"].get_to(obj.OBWEnable);
    }
    if (j.contains("SEMEnable")) {
        j["SEMEnable"].get_to(obj.SEMEnable);
    }
    if (j.contains("ACLREnable")) {
        j["ACLREnable"].get_to(obj.ACLREnable);
    }
    if (j.contains("PwrDynEnable")) {
        j["PwrDynEnable"].get_to(obj.PwrDynEnable);
    }
    if (j.contains("TxPwrEnable")) {
        j["TxPwrEnable"].get_to(obj.TxPwrEnable);
    }
    if (j.contains("PMonitorEnable")) {
        j["PMonitorEnable"].get_to(obj.PMonitorEnable);
    }
    if (j.contains("SCHDecEnable")) {
        j["SCHDecEnable"].get_to(obj.SCHDecEnable);
    }
    if (j.contains("SSBDecEnable")) {
        j["SSBDecEnable"].get_to(obj.SSBDecEnable);
    }
    if (j.contains("DmrsConsState")) {
        j["DmrsConsState"].get_to(obj.DmrsConsState);
    }
    if (j.contains("EvmSymbEnable")) {
        j["EvmSymbEnable"].get_to(obj.EvmSymbEnable);
    }
    if (j.contains("EvmSymbIndx")) {
        j["EvmSymbIndx"].get_to(obj.EvmSymbIndx);
    }
    if (j.contains("EvmSymbPosType")) {
        j["EvmSymbPosType"].get_to(obj.EvmSymbPosType);
    }
}

inline json to_json(const Alg_3GPP_AlzMeasure5g& obj) {
    json j;
    j["MeasureExpect"] = obj.MeasureExpect;
    j["VFilterEnable"] = obj.VFilterEnable;
    j["NRBViewFilter"] = obj.NRBViewFilter;
    j["MeasSFNum"] = obj.MeasSFNum;
    j["MeasAllEnable"] = obj.MeasAllEnable;
    j["MeasSlotIdx"] = obj.MeasSlotIdx;
    j["ModStatNum"] = obj.ModStatNum;
    j["PhaseTrack"] = obj.PhaseTrack;
    j["TimingTrack"] = obj.TimingTrack;
    j["LevelTrack"] = obj.LevelTrack;
    j["TxDCOffset"] = obj.TxDCOffset;
    j["EvmWinLen"] = json::array();
    for (size_t i = 0; i < arraySize(obj.EvmWinLen); ++i) {
        json arr_i = json::array();
        for (size_t j = 0; j < arraySize(obj.EvmWinLen[i]); ++j) {
            arr_i.emplace_back(obj.EvmWinLen[i][j]);
        }
        j["EvmWinLen"].emplace_back(arr_i);
    }
    j["SEMStatNum"] = obj.SEMStatNum;
    j["SEMMeasFilter"] = obj.SEMMeasFilter;
    j["ACLRStatNum"] = obj.ACLRStatNum;
    j["UTRAEnable"] = json::array();
    for (size_t i = 0; i < arraySize(obj.UTRAEnable); ++i) {
        j["UTRAEnable"].emplace_back(obj.UTRAEnable[i]);
    }
    j["PwrDynStatNum"] = obj.PwrDynStatNum;
    j["TxPwrStatNum"] = obj.TxPwrStatNum;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzMeasure5g& obj) {
    if (j.contains("MeasureExpect")) {
        j["MeasureExpect"].get_to(obj.MeasureExpect);
    }
    if (j.contains("VFilterEnable")) {
        j["VFilterEnable"].get_to(obj.VFilterEnable);
    }
    if (j.contains("NRBViewFilter")) {
        j["NRBViewFilter"].get_to(obj.NRBViewFilter);
    }
    if (j.contains("MeasSFNum")) {
        j["MeasSFNum"].get_to(obj.MeasSFNum);
    }
    if (j.contains("MeasAllEnable")) {
        j["MeasAllEnable"].get_to(obj.MeasAllEnable);
    }
    if (j.contains("MeasSlotIdx")) {
        j["MeasSlotIdx"].get_to(obj.MeasSlotIdx);
    }
    if (j.contains("ModStatNum")) {
        j["ModStatNum"].get_to(obj.ModStatNum);
    }
    if (j.contains("PhaseTrack")) {
        j["PhaseTrack"].get_to(obj.PhaseTrack);
    }
    if (j.contains("TimingTrack")) {
        j["TimingTrack"].get_to(obj.TimingTrack);
    }
    if (j.contains("LevelTrack")) {
        j["LevelTrack"].get_to(obj.LevelTrack);
    }
    if (j.contains("TxDCOffset")) {
        j["TxDCOffset"].get_to(obj.TxDCOffset);
    }
    if (j.contains("EvmWinLen")) {
        const auto& arr = j["EvmWinLen"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.EvmWinLen)); ++i) {
            const auto& arr_i = arr[i];
            for (size_t j = 0; j < std::min(arr_i.size(), arraySize(obj.EvmWinLen[i])); ++j) {
                arr_i[j].get_to(obj.EvmWinLen[i][j]);
            }
        }
    }
    if (j.contains("SEMStatNum")) {
        j["SEMStatNum"].get_to(obj.SEMStatNum);
    }
    if (j.contains("SEMMeasFilter")) {
        j["SEMMeasFilter"].get_to(obj.SEMMeasFilter);
    }
    if (j.contains("ACLRStatNum")) {
        j["ACLRStatNum"].get_to(obj.ACLRStatNum);
    }
    if (j.contains("UTRAEnable")) {
        const auto& arr = j["UTRAEnable"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.UTRAEnable)); ++i) {
            arr[i].get_to(obj.UTRAEnable[i]);
        }
    }
    if (j.contains("PwrDynStatNum")) {
        j["PwrDynStatNum"].get_to(obj.PwrDynStatNum);
    }
    if (j.contains("TxPwrStatNum")) {
        j["TxPwrStatNum"].get_to(obj.TxPwrStatNum);
    }
}

inline json to_json(const Alg_3GPP_VsaTrigger5g& obj) {
    json j;
    j["SyncMode"] = obj.SyncMode;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_VsaTrigger5g& obj) {
    if (j.contains("SyncMode")) {
        j["SyncMode"].get_to(obj.SyncMode);
    }
}

inline json to_json(const Alg_5G_ModulateLimitType& obj) {
    json j;
    j["EvmRms"] = to_json(obj.EvmRms);
    j["EvmPeak"] = to_json(obj.EvmPeak);
    j["MErrRms"] = to_json(obj.MErrRms);
    j["MErrPeak"] = to_json(obj.MErrPeak);
    j["PhErrRms"] = to_json(obj.PhErrRms);
    j["PhErrPeak"] = to_json(obj.PhErrPeak);
    j["FreqErr"] = to_json(obj.FreqErr);
    j["IQOffset"] = to_json(obj.IQOffset);
    j["IBE"] = to_json(obj.IBE);
    j["SpectFlat"] = to_json(obj.SpectFlat);
    return j;
}

inline void from_json(const json& j, Alg_5G_ModulateLimitType& obj) {
    if (j.contains("EvmRms")) {
        from_json(j["EvmRms"], obj.EvmRms);
    }
    if (j.contains("EvmPeak")) {
        from_json(j["EvmPeak"], obj.EvmPeak);
    }
    if (j.contains("MErrRms")) {
        from_json(j["MErrRms"], obj.MErrRms);
    }
    if (j.contains("MErrPeak")) {
        from_json(j["MErrPeak"], obj.MErrPeak);
    }
    if (j.contains("PhErrRms")) {
        from_json(j["PhErrRms"], obj.PhErrRms);
    }
    if (j.contains("PhErrPeak")) {
        from_json(j["PhErrPeak"], obj.PhErrPeak);
    }
    if (j.contains("FreqErr")) {
        from_json(j["FreqErr"], obj.FreqErr);
    }
    if (j.contains("IQOffset")) {
        from_json(j["IQOffset"], obj.IQOffset);
    }
    if (j.contains("IBE")) {
        from_json(j["IBE"], obj.IBE);
    }
    if (j.contains("SpectFlat")) {
        from_json(j["SpectFlat"], obj.SpectFlat);
    }
}

inline json to_json(const Alg_5G_SpectrumLimitType& obj) {
    json j;
    j["OBWLimit"] = to_json(obj.OBWLimit);
    j["SEMLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMLimit); ++i) {
        json arr_i = json::array();
        for (size_t j = 0; j < arraySize(obj.SEMLimit[i]); ++j) {
            arr_i.emplace_back(to_json(obj.SEMLimit[i][j]));
        }
        j["SEMLimit"].emplace_back(arr_i);
    }
    j["UtraLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.UtraLimit); ++i) {
        j["UtraLimit"].emplace_back(to_json(obj.UtraLimit[i]));
    }
    j["NRLimit"] = to_json(obj.NRLimit);
    return j;
}

inline void from_json(const json& j, Alg_5G_SpectrumLimitType& obj) {
    if (j.contains("OBWLimit")) {
        from_json(j["OBWLimit"], obj.OBWLimit);
    }
    if (j.contains("SEMLimit")) {
        const auto& arr = j["SEMLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMLimit)); ++i) {
            const auto& arr_i = arr[i];
            for (size_t j = 0; j < std::min(arr_i.size(), arraySize(obj.SEMLimit[i])); ++j) {
                from_json(arr_i[j], obj.SEMLimit[i][j]);
            }
        }
    }
    if (j.contains("UtraLimit")) {
        const auto& arr = j["UtraLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.UtraLimit)); ++i) {
            from_json(arr[i], obj.UtraLimit[i]);
        }
    }
    if (j.contains("NRLimit")) {
        from_json(j["NRLimit"], obj.NRLimit);
    }
}

inline json to_json(const Alg_5G_PowerLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["OffPower"] = obj.OffPower;
    j["TestTol"] = json::array();
    for (size_t i = 0; i < arraySize(obj.TestTol); ++i) {
        j["TestTol"].emplace_back(obj.TestTol[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_5G_PowerLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("OffPower")) {
        j["OffPower"].get_to(obj.OffPower);
    }
    if (j.contains("TestTol")) {
        const auto& arr = j["TestTol"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.TestTol)); ++i) {
            arr[i].get_to(obj.TestTol[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_LimitIn5g& obj) {
    json j;
    j["ModLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.ModLimit); ++i) {
        j["ModLimit"].emplace_back(to_json(obj.ModLimit[i]));
    }
    j["SpectLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SpectLimit); ++i) {
        j["SpectLimit"].emplace_back(to_json(obj.SpectLimit[i]));
    }
    j["SEMAddTestTol"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMAddTestTol); ++i) {
        j["SEMAddTestTol"].emplace_back(obj.SEMAddTestTol[i]);
    }
    j["ACLRAddTestTol"] = json::array();
    for (size_t i = 0; i < arraySize(obj.ACLRAddTestTol); ++i) {
        j["ACLRAddTestTol"].emplace_back(obj.ACLRAddTestTol[i]);
    }
    j["PowerLimit"] = to_json(obj.PowerLimit);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_LimitIn5g& obj) {
    if (j.contains("ModLimit")) {
        const auto& arr = j["ModLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.ModLimit)); ++i) {
            from_json(arr[i], obj.ModLimit[i]);
        }
    }
    if (j.contains("SpectLimit")) {
        const auto& arr = j["SpectLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SpectLimit)); ++i) {
            from_json(arr[i], obj.SpectLimit[i]);
        }
    }
    if (j.contains("SEMAddTestTol")) {
        const auto& arr = j["SEMAddTestTol"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMAddTestTol)); ++i) {
            arr[i].get_to(obj.SEMAddTestTol[i]);
        }
    }
    if (j.contains("ACLRAddTestTol")) {
        const auto& arr = j["ACLRAddTestTol"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.ACLRAddTestTol)); ++i) {
            arr[i].get_to(obj.ACLRAddTestTol[i]);
        }
    }
    if (j.contains("PowerLimit")) {
        from_json(j["PowerLimit"], obj.PowerLimit);
    }
}

inline json to_json(const Alg_3GPP_AlzIn5g& obj) {
    json j;
    j["LinkDirect"] = obj.LinkDirect;
    j["RedcapEnable"] = obj.RedcapEnable;
    j["Version"] = obj.Version;
    j["UL"] = to_json(obj.UL);
    j["DL"] = to_json(obj.DL);
    j["ViewSet"] = to_json(obj.ViewSet);
    j["Measure"] = to_json(obj.Measure);
    j["Trigger"] = to_json(obj.Trigger);
    j["LimitInfo"] = to_json(obj.LimitInfo);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzIn5g& obj) {
    if (j.contains("LinkDirect")) {
        j["LinkDirect"].get_to(obj.LinkDirect);
    }
    if (j.contains("RedcapEnable")) {
        j["RedcapEnable"].get_to(obj.RedcapEnable);
    }
    if (j.contains("Version")) {
        j["Version"].get_to(obj.Version);
    }
    if (j.contains("UL")) {
        from_json(j["UL"], obj.UL);
    }
    if (j.contains("DL")) {
        from_json(j["DL"], obj.DL);
    }
    if (j.contains("ViewSet")) {
        from_json(j["ViewSet"], obj.ViewSet);
    }
    if (j.contains("Measure")) {
        from_json(j["Measure"], obj.Measure);
    }
    if (j.contains("Trigger")) {
        from_json(j["Trigger"], obj.Trigger);
    }
    if (j.contains("LimitInfo")) {
        from_json(j["LimitInfo"], obj.LimitInfo);
    }
}

inline json to_json(const Alg_3GPP_AlzNpuschNBIOT& obj) {
    json j;
    j["Format"] = obj.Format;
    j["SCSpacing"] = obj.SCSpacing;
    j["Repetitions"] = obj.Repetitions;
    j["RUs"] = obj.RUs;
    j["SubcarrierNum"] = obj.SubcarrierNum;
    j["StartSubcarrier"] = obj.StartSubcarrier;
    j["CyclicShift"] = obj.CyclicShift;
    j["GrpHopping"] = obj.GrpHopping;
    j["DeltaSeqShift"] = obj.DeltaSeqShift;
    j["Modulate"] = obj.Modulate;
    j["ChanCodingState"] = obj.ChanCodingState;
    j["Scrambling"] = obj.Scrambling;
    j["StartSubfrm"] = obj.StartSubfrm;
    j["TBSIdx"] = obj.TBSIdx;
    j["StartRVIdx"] = obj.StartRVIdx;
    j["UeID"] = obj.UeID;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzNpuschNBIOT& obj) {
    if (j.contains("Format")) {
        j["Format"].get_to(obj.Format);
    }
    if (j.contains("SCSpacing")) {
        j["SCSpacing"].get_to(obj.SCSpacing);
    }
    if (j.contains("Repetitions")) {
        j["Repetitions"].get_to(obj.Repetitions);
    }
    if (j.contains("RUs")) {
        j["RUs"].get_to(obj.RUs);
    }
    if (j.contains("SubcarrierNum")) {
        j["SubcarrierNum"].get_to(obj.SubcarrierNum);
    }
    if (j.contains("StartSubcarrier")) {
        j["StartSubcarrier"].get_to(obj.StartSubcarrier);
    }
    if (j.contains("CyclicShift")) {
        j["CyclicShift"].get_to(obj.CyclicShift);
    }
    if (j.contains("GrpHopping")) {
        j["GrpHopping"].get_to(obj.GrpHopping);
    }
    if (j.contains("DeltaSeqShift")) {
        j["DeltaSeqShift"].get_to(obj.DeltaSeqShift);
    }
    if (j.contains("Modulate")) {
        j["Modulate"].get_to(obj.Modulate);
    }
    if (j.contains("ChanCodingState")) {
        j["ChanCodingState"].get_to(obj.ChanCodingState);
    }
    if (j.contains("Scrambling")) {
        j["Scrambling"].get_to(obj.Scrambling);
    }
    if (j.contains("StartSubfrm")) {
        j["StartSubfrm"].get_to(obj.StartSubfrm);
    }
    if (j.contains("TBSIdx")) {
        j["TBSIdx"].get_to(obj.TBSIdx);
    }
    if (j.contains("StartRVIdx")) {
        j["StartRVIdx"].get_to(obj.StartRVIdx);
    }
    if (j.contains("UeID")) {
        j["UeID"].get_to(obj.UeID);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzNprachNBIOT& obj) {
    json j;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzNprachNBIOT& obj) {
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzULInNBIOT& obj) {
    json j;
    j["Duplexing"] = obj.Duplexing;
    j["OperationMode"] = obj.OperationMode;
    j["ChannelBW"] = obj.ChannelBW;
    j["RBIdx"] = obj.RBIdx;
    j["NBCellID"] = obj.NBCellID;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    j["ChanType"] = obj.ChanType;
    j["Npusch"] = to_json(obj.Npusch);
    j["Nprach"] = to_json(obj.Nprach);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzULInNBIOT& obj) {
    if (j.contains("Duplexing")) {
        j["Duplexing"].get_to(obj.Duplexing);
    }
    if (j.contains("OperationMode")) {
        j["OperationMode"].get_to(obj.OperationMode);
    }
    if (j.contains("ChannelBW")) {
        j["ChannelBW"].get_to(obj.ChannelBW);
    }
    if (j.contains("RBIdx")) {
        j["RBIdx"].get_to(obj.RBIdx);
    }
    if (j.contains("NBCellID")) {
        j["NBCellID"].get_to(obj.NBCellID);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
    if (j.contains("ChanType")) {
        j["ChanType"].get_to(obj.ChanType);
    }
    if (j.contains("Npusch")) {
        from_json(j["Npusch"], obj.Npusch);
    }
    if (j.contains("Nprach")) {
        from_json(j["Nprach"], obj.Nprach);
    }
}

inline json to_json(const Alg_3GPP_AlzNpdschNBIOT& obj) {
    json j;
    j["NSF"] = obj.NSF;
    j["Repetitions"] = obj.Repetitions;
    j["MCS"] = obj.MCS;
    j["StartSymb"] = obj.StartSymb;
    j["StartSubfrm"] = obj.StartSubfrm;
    j["Precoding"] = obj.Precoding;
    j["ChanCodingState"] = obj.ChanCodingState;
    j["Scrambling"] = obj.Scrambling;
    j["UeID"] = obj.UeID;
    j["Power"] = obj.Power;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzNpdschNBIOT& obj) {
    if (j.contains("NSF")) {
        j["NSF"].get_to(obj.NSF);
    }
    if (j.contains("Repetitions")) {
        j["Repetitions"].get_to(obj.Repetitions);
    }
    if (j.contains("MCS")) {
        j["MCS"].get_to(obj.MCS);
    }
    if (j.contains("StartSymb")) {
        j["StartSymb"].get_to(obj.StartSymb);
    }
    if (j.contains("StartSubfrm")) {
        j["StartSubfrm"].get_to(obj.StartSubfrm);
    }
    if (j.contains("Precoding")) {
        j["Precoding"].get_to(obj.Precoding);
    }
    if (j.contains("ChanCodingState")) {
        j["ChanCodingState"].get_to(obj.ChanCodingState);
    }
    if (j.contains("Scrambling")) {
        j["Scrambling"].get_to(obj.Scrambling);
    }
    if (j.contains("UeID")) {
        j["UeID"].get_to(obj.UeID);
    }
    if (j.contains("Power")) {
        j["Power"].get_to(obj.Power);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
}

inline json to_json(const Alg_3GPP_AlzDLInNBIOT& obj) {
    json j;
    j["Duplexing"] = obj.Duplexing;
    j["OperationMode"] = obj.OperationMode;
    j["CarrierType"] = obj.CarrierType;
    j["ChannelBW"] = obj.ChannelBW;
    j["RBIdx"] = obj.RBIdx;
    j["NBCellID"] = obj.NBCellID;
    j["LTECellID"] = obj.LTECellID;
    j["LTEAntennaNum"] = obj.LTEAntennaNum;
    j["NBAntennaNum"] = obj.NBAntennaNum;
    j["SIB1Switch"] = obj.SIB1Switch;
    j["SchedulingInfoSIB1"] = obj.SchedulingInfoSIB1;
    j["NPssPower"] = obj.NPssPower;
    j["NSssPower"] = obj.NSssPower;
    j["Reversed"] = json::array();
    for (size_t i = 0; i < arraySize(obj.Reversed); ++i) {
        j["Reversed"].emplace_back(obj.Reversed[i]);
    }
    j["ChanType"] = obj.ChanType;
    j["Npdsch"] = to_json(obj.Npdsch);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzDLInNBIOT& obj) {
    if (j.contains("Duplexing")) {
        j["Duplexing"].get_to(obj.Duplexing);
    }
    if (j.contains("OperationMode")) {
        j["OperationMode"].get_to(obj.OperationMode);
    }
    if (j.contains("CarrierType")) {
        j["CarrierType"].get_to(obj.CarrierType);
    }
    if (j.contains("ChannelBW")) {
        j["ChannelBW"].get_to(obj.ChannelBW);
    }
    if (j.contains("RBIdx")) {
        j["RBIdx"].get_to(obj.RBIdx);
    }
    if (j.contains("NBCellID")) {
        j["NBCellID"].get_to(obj.NBCellID);
    }
    if (j.contains("LTECellID")) {
        j["LTECellID"].get_to(obj.LTECellID);
    }
    if (j.contains("LTEAntennaNum")) {
        j["LTEAntennaNum"].get_to(obj.LTEAntennaNum);
    }
    if (j.contains("NBAntennaNum")) {
        j["NBAntennaNum"].get_to(obj.NBAntennaNum);
    }
    if (j.contains("SIB1Switch")) {
        j["SIB1Switch"].get_to(obj.SIB1Switch);
    }
    if (j.contains("SchedulingInfoSIB1")) {
        j["SchedulingInfoSIB1"].get_to(obj.SchedulingInfoSIB1);
    }
    if (j.contains("NPssPower")) {
        j["NPssPower"].get_to(obj.NPssPower);
    }
    if (j.contains("NSssPower")) {
        j["NSssPower"].get_to(obj.NSssPower);
    }
    if (j.contains("Reversed")) {
        const auto& arr = j["Reversed"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.Reversed)); ++i) {
            arr[i].get_to(obj.Reversed[i]);
        }
    }
    if (j.contains("ChanType")) {
        j["ChanType"].get_to(obj.ChanType);
    }
    if (j.contains("Npdsch")) {
        from_json(j["Npdsch"], obj.Npdsch);
    }
}

inline json to_json(const Alg_3GPP_AlzMeasureNBIOT& obj) {
    json j;
    j["StatisticAvgFlg"] = obj.StatisticAvgFlg;
    j["StatisticCnt"] = obj.StatisticCnt;
    j["MeasureUnit"] = obj.MeasureUnit;
    j["ConstShowPilot"] = obj.ConstShowPilot;
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzMeasureNBIOT& obj) {
    if (j.contains("StatisticAvgFlg")) {
        j["StatisticAvgFlg"].get_to(obj.StatisticAvgFlg);
    }
    if (j.contains("StatisticCnt")) {
        j["StatisticCnt"].get_to(obj.StatisticCnt);
    }
    if (j.contains("MeasureUnit")) {
        j["MeasureUnit"].get_to(obj.MeasureUnit);
    }
    if (j.contains("ConstShowPilot")) {
        j["ConstShowPilot"].get_to(obj.ConstShowPilot);
    }
}

inline json to_json(const Alg_NBIOT_SEMLimitType& obj) {
    json j;
    j["State"] = obj.State;
    j["StartFreq"] = obj.StartFreq;
    j["StopFreq"] = obj.StopFreq;
    j["StartPower"] = obj.StartPower;
    j["StopPower"] = obj.StopPower;
    j["RBW"] = obj.RBW;
    return j;
}

inline void from_json(const json& j, Alg_NBIOT_SEMLimitType& obj) {
    if (j.contains("State")) {
        j["State"].get_to(obj.State);
    }
    if (j.contains("StartFreq")) {
        j["StartFreq"].get_to(obj.StartFreq);
    }
    if (j.contains("StopFreq")) {
        j["StopFreq"].get_to(obj.StopFreq);
    }
    if (j.contains("StartPower")) {
        j["StartPower"].get_to(obj.StartPower);
    }
    if (j.contains("StopPower")) {
        j["StopPower"].get_to(obj.StopPower);
    }
    if (j.contains("RBW")) {
        j["RBW"].get_to(obj.RBW);
    }
}

inline json to_json(const Alg_3GPP_LimitInNBIOT& obj) {
    json j;
    j["ModLimitMode"] = obj.ModLimitMode;
    j["EvmRms"] = to_json(obj.EvmRms);
    j["EvmPeak"] = to_json(obj.EvmPeak);
    j["MErrRms"] = to_json(obj.MErrRms);
    j["MErrPeak"] = to_json(obj.MErrPeak);
    j["PhErrRms"] = to_json(obj.PhErrRms);
    j["PhErrPeak"] = to_json(obj.PhErrPeak);
    j["FreqErrLow"] = to_json(obj.FreqErrLow);
    j["FreqErrHigh"] = to_json(obj.FreqErrHigh);
    j["IQOffset"] = to_json(obj.IQOffset);
    j["IBE"] = to_json(obj.IBE);
    j["SpectLimitMode"] = obj.SpectLimitMode;
    j["OBWLimit"] = to_json(obj.OBWLimit);
    j["SEMLimit"] = json::array();
    for (size_t i = 0; i < arraySize(obj.SEMLimit); ++i) {
        j["SEMLimit"].emplace_back(to_json(obj.SEMLimit[i]));
    }
    j["GSM"] = to_json(obj.GSM);
    j["UTRA"] = to_json(obj.UTRA);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_LimitInNBIOT& obj) {
    if (j.contains("ModLimitMode")) {
        j["ModLimitMode"].get_to(obj.ModLimitMode);
    }
    if (j.contains("EvmRms")) {
        from_json(j["EvmRms"], obj.EvmRms);
    }
    if (j.contains("EvmPeak")) {
        from_json(j["EvmPeak"], obj.EvmPeak);
    }
    if (j.contains("MErrRms")) {
        from_json(j["MErrRms"], obj.MErrRms);
    }
    if (j.contains("MErrPeak")) {
        from_json(j["MErrPeak"], obj.MErrPeak);
    }
    if (j.contains("PhErrRms")) {
        from_json(j["PhErrRms"], obj.PhErrRms);
    }
    if (j.contains("PhErrPeak")) {
        from_json(j["PhErrPeak"], obj.PhErrPeak);
    }
    if (j.contains("FreqErrLow")) {
        from_json(j["FreqErrLow"], obj.FreqErrLow);
    }
    if (j.contains("FreqErrHigh")) {
        from_json(j["FreqErrHigh"], obj.FreqErrHigh);
    }
    if (j.contains("IQOffset")) {
        from_json(j["IQOffset"], obj.IQOffset);
    }
    if (j.contains("IBE")) {
        from_json(j["IBE"], obj.IBE);
    }
    if (j.contains("SpectLimitMode")) {
        j["SpectLimitMode"].get_to(obj.SpectLimitMode);
    }
    if (j.contains("OBWLimit")) {
        from_json(j["OBWLimit"], obj.OBWLimit);
    }
    if (j.contains("SEMLimit")) {
        const auto& arr = j["SEMLimit"];
        for (size_t i = 0; i < std::min(arr.size(), arraySize(obj.SEMLimit)); ++i) {
            from_json(arr[i], obj.SEMLimit[i]);
        }
    }
    if (j.contains("GSM")) {
        from_json(j["GSM"], obj.GSM);
    }
    if (j.contains("UTRA")) {
        from_json(j["UTRA"], obj.UTRA);
    }
}

inline json to_json(const Alg_3GPP_AlzInNBIOT& obj) {
    json j;
    j["LinkDirect"] = obj.LinkDirect;
    j["UL"] = to_json(obj.UL);
    j["DL"] = to_json(obj.DL);
    j["Measure"] = to_json(obj.Measure);
    j["LimitInfo"] = to_json(obj.LimitInfo);
    return j;
}

inline void from_json(const json& j, Alg_3GPP_AlzInNBIOT& obj) {
    if (j.contains("LinkDirect")) {
        j["LinkDirect"].get_to(obj.LinkDirect);
    }
    if (j.contains("UL")) {
        from_json(j["UL"], obj.UL);
    }
    if (j.contains("DL")) {
        from_json(j["DL"], obj.DL);
    }
    if (j.contains("Measure")) {
        from_json(j["Measure"], obj.Measure);
    }
    if (j.contains("LimitInfo")) {
        from_json(j["LimitInfo"], obj.LimitInfo);
    }
}

