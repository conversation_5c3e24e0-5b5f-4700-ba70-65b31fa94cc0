#include "cellular_param_to_json.h"
#include "wterror.h"

using json = nlohmann::json;

namespace {

template<typename T, size_t N>
inline constexpr size_t arraySize(const T (&)[N]) noexcept {
    return N;
}

void gsm_param_to_json(json &json_gsm, const Alg_3GPP_AlzInGSM &gsm);
void wcdma_param_to_json(json &json_wcdma, const Alg_3GPP_AlzInWCDMA &wcdma);
void lte_param_to_json(json &json_lte, const Alg_3GPP_AlzIn4g &lte);
void nr_param_to_json(json &json_nr, const Alg_3GPP_AlzIn5g &nr);
void nbiot_param_to_json(json &json_nbiot, const Alg_3GPP_AlzInNBIOT &nbiot);

// WCDMA
void wcdma_ul_param_to_json(json &json_wcdma_ul, const Alg_3GPP_AlzULInWCDMA &wcdma_ul);
void wcdma_dl_param_to_json(json &json_wcdma_dl, const Alg_3GPP_AlzDLInWCDMA &wcdma_dl);
void wcdma_measure_param_to_json(json &json_wcdma_measure, const Alg_3GPP_AlzMeasureWCDMA &wcdma_measure);
// LTE
void lte_ul_param_to_json(json &json_lte_ul, const Alg_3GPP_AlzPusch4g &lte_pusch);
void lte_dl_param_to_json(json &json_lte_dl, const Alg_3GPP_AlzPdsch4g &lte_pdsch);
void lte_measure_param_to_json(json &json_lte_measure, const Alg_3GPP_MeasureIn4g &lte_measure);
void lte_limit_param_to_json(json &json_lte_limit, const Alg_3GPP_LimitIn4g &lte_limit);
// NR5G
void nr_ul_param_to_json(json &json_nr_ul, const Alg_3GPP_AlzULIn5g &nr_ul);
void nr_dl_param_to_json(json &json_nr_dl, const Alg_3GPP_AlzDLIn5g &nr_dl);
void nr_measure_param_to_json(json &json_nr_meas, const Alg_3GPP_AlzMeasure5g &nr_meas);
void nr_limit_param_to_json(json &json_nr_limit, const Alg_3GPP_LimitIn5g &nr_limit);
// NBIOT
void nbiot_ul_param_to_json(json &json_nbiot_ul, const Alg_3GPP_AlzULInNBIOT &nbiot_ul);
void nbiot_dl_param_to_json(json &json_nbiot_dl, const Alg_3GPP_AlzDLInNBIOT &nbiot_dl);
void nbiot_measure_param_to_json(json &json_nbiot_meas, const Alg_3GPP_AlzMeasureNBIOT &nbiot_meas);
void nbiot_limit_param_to_json(json &json_nbiot_limit, const Alg_3GPP_LimitInNBIOT &nbiot_limit);

} // namespace

void cellular_param_to_json(json &pRoot, const AlzParam3GPP &Param, bool is_wavegen)
{
    pRoot["Version"] = 1;
    pRoot["Standard"] = Param.Standard;

    if (!is_wavegen)
    {
        // WaveGen 时, DcFreqCompensate和SpectrumRBW用的的固件默认值, 不是从算法返回的
        pRoot["DcFreqCompensate"] = Param.DcFreqCompensate;
        pRoot["SpectrumRBW"] = Param.SpectrumRBW;
    }
    pRoot["ErrorCode"] = Param.ErrorCode;

    switch (Param.Standard)
    {
    case ALG_3GPP_STD_GSM:
    {
        gsm_param_to_json(pRoot["GSM"], Param.GSM);
        break;
    }
    case ALG_3GPP_STD_WCDMA:
    {
        wcdma_param_to_json(pRoot["WCDMA"], Param.WCDMA);
        break;
    }
    case ALG_3GPP_STD_4G:
    {
        lte_param_to_json(pRoot["LTE"], Param.LTE);
        break;
    }
    case ALG_3GPP_STD_5G:
    {
        nr_param_to_json(pRoot["NR"], Param.NR);
        break;
    }
    case ALG_3GPP_STD_NB_IOT:
    {
        nbiot_param_to_json(pRoot["NBIOT"], Param.NBIOT);
        break;
    }
    default:
        break;
    }
}

namespace {

void gsm_param_to_json(json &json_gsm, const Alg_3GPP_AlzInGSM &gsm)
{
    json_gsm["SlotOffset"] = gsm.SlotOffset;
    json_gsm["NumbOfSlot"] = gsm.NumbOfSlot;
    json_gsm["MeasureSlot"] = gsm.MeasureSlot;
    json_gsm["PvTFilter"] = gsm.PvTFilter;

    for (int i = 0; i < arraySize(gsm.SpectMod.OffsetState); ++i) {
        json_gsm["SpectMod"]["OffsetState"].emplace_back(gsm.SpectMod.OffsetState[i]);
    }

    for (int i = 0; i < arraySize(gsm.SpectMod.FreqOffset); ++i) {
        json_gsm["SpectMod"]["FreqOffset"].emplace_back(gsm.SpectMod.FreqOffset[i]);
    }

    for (int i = 0; i < arraySize(gsm.SpectSwt.OffsetState); ++i) {
        json_gsm["SpectSwt"]["OffsetState"].emplace_back(gsm.SpectSwt.OffsetState[i]);
    }

    for (int i = 0; i < arraySize(gsm.SpectSwt.FreqOffset); ++i) {
        json_gsm["SpectSwt"]["FreqOffset"].emplace_back(gsm.SpectSwt.FreqOffset[i]);
    }

    // limit info
    for (int i = 0; i < arraySize(gsm.LimitInfo.ModLimit); ++i)
    {
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmRms"]["Current"] = gsm.LimitInfo.ModLimit[i].EvmRms.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmRms"]["Average"] = gsm.LimitInfo.ModLimit[i].EvmRms.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmRms"]["Max"] = gsm.LimitInfo.ModLimit[i].EvmRms.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmRms"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].EvmRms.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["EvmPeak"]["Current"] = gsm.LimitInfo.ModLimit[i].EvmPeak.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmPeak"]["Average"] = gsm.LimitInfo.ModLimit[i].EvmPeak.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmPeak"]["Max"] = gsm.LimitInfo.ModLimit[i].EvmPeak.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["EvmPeak"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].EvmPeak.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["Evm95Percent"]["State"] = gsm.LimitInfo.ModLimit[i].Evm95Percent.State;
        json_gsm["LimitInfo"]["ModLimit"][i]["Evm95Percent"]["Limit"] = gsm.LimitInfo.ModLimit[i].Evm95Percent.Limit;

        json_gsm["LimitInfo"]["ModLimit"][i]["MErrRms"]["Current"] = gsm.LimitInfo.ModLimit[i].MErrRms.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErrRms"]["Average"] = gsm.LimitInfo.ModLimit[i].MErrRms.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErrRms"]["Max"] = gsm.LimitInfo.ModLimit[i].MErrRms.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErrRms"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].MErrRms.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["MErrPeak"]["Current"] = gsm.LimitInfo.ModLimit[i].MErrPeak.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErrPeak"]["Average"] = gsm.LimitInfo.ModLimit[i].MErrPeak.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErrPeak"]["Max"] = gsm.LimitInfo.ModLimit[i].MErrPeak.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErrPeak"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].MErrPeak.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["MErr95Percent"]["State"] = gsm.LimitInfo.ModLimit[i].MErr95Percent.State;
        json_gsm["LimitInfo"]["ModLimit"][i]["MErr95Percent"]["Limit"] = gsm.LimitInfo.ModLimit[i].MErr95Percent.Limit;

        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrRms"]["Current"] = gsm.LimitInfo.ModLimit[i].PhErrRms.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrRms"]["Average"] = gsm.LimitInfo.ModLimit[i].PhErrRms.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrRms"]["Max"] = gsm.LimitInfo.ModLimit[i].PhErrRms.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrRms"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].PhErrRms.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"]["Current"] = gsm.LimitInfo.ModLimit[i].PhErrPeak.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"]["Average"] = gsm.LimitInfo.ModLimit[i].PhErrPeak.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"]["Max"] = gsm.LimitInfo.ModLimit[i].PhErrPeak.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErrPeak"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].PhErrPeak.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["PhErr95Percent"]["State"] = gsm.LimitInfo.ModLimit[i].PhErr95Percent.State;
        json_gsm["LimitInfo"]["ModLimit"][i]["PhErr95Percent"]["Limit"] = gsm.LimitInfo.ModLimit[i].PhErr95Percent.Limit;

        json_gsm["LimitInfo"]["ModLimit"][i]["IQOffset"]["Current"] = gsm.LimitInfo.ModLimit[i].IQOffset.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["IQOffset"]["Average"] = gsm.LimitInfo.ModLimit[i].IQOffset.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["IQOffset"]["Max"] = gsm.LimitInfo.ModLimit[i].IQOffset.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["IQOffset"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].IQOffset.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["IQImbalance"]["Current"] = gsm.LimitInfo.ModLimit[i].IQImbalance.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["IQImbalance"]["Average"] = gsm.LimitInfo.ModLimit[i].IQImbalance.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["IQImbalance"]["Max"] = gsm.LimitInfo.ModLimit[i].IQImbalance.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["IQImbalance"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].IQImbalance.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["FreError"]["Current"] = gsm.LimitInfo.ModLimit[i].FreError.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["FreError"]["Average"] = gsm.LimitInfo.ModLimit[i].FreError.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["FreError"]["Max"] = gsm.LimitInfo.ModLimit[i].FreError.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["FreError"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].FreError.LimitValue;

        json_gsm["LimitInfo"]["ModLimit"][i]["TimeError"]["Current"] = gsm.LimitInfo.ModLimit[i].TimeError.Current;
        json_gsm["LimitInfo"]["ModLimit"][i]["TimeError"]["Average"] = gsm.LimitInfo.ModLimit[i].TimeError.Average;
        json_gsm["LimitInfo"]["ModLimit"][i]["TimeError"]["Max"] = gsm.LimitInfo.ModLimit[i].TimeError.Max;
        json_gsm["LimitInfo"]["ModLimit"][i]["TimeError"]["LimitValue"] = gsm.LimitInfo.ModLimit[i].TimeError.LimitValue;
    }

    for (int i = 0; i < arraySize(gsm.LimitInfo.PVTLimit.AvgLimit); ++i)
    {
        json_gsm["LimitInfo"]["PVTLimit"]["AvgLimit"][i]["State"] = gsm.LimitInfo.PVTLimit.AvgLimit[i].State;
        json_gsm["LimitInfo"]["PVTLimit"]["AvgLimit"][i]["FromPCL"] = gsm.LimitInfo.PVTLimit.AvgLimit[i].FromPCL;
        json_gsm["LimitInfo"]["PVTLimit"]["AvgLimit"][i]["ToPCL"] = gsm.LimitInfo.PVTLimit.AvgLimit[i].ToPCL;
        json_gsm["LimitInfo"]["PVTLimit"]["AvgLimit"][i]["Lower"] = gsm.LimitInfo.PVTLimit.AvgLimit[i].Lower;
        json_gsm["LimitInfo"]["PVTLimit"]["AvgLimit"][i]["Upper"] = gsm.LimitInfo.PVTLimit.AvgLimit[i].Upper;
    }

    json_gsm["LimitInfo"]["PVTLimit"]["GuardPeriod"]["State"] = gsm.LimitInfo.PVTLimit.GuardPeriod.State;
    json_gsm["LimitInfo"]["PVTLimit"]["GuardPeriod"]["Limit"] = gsm.LimitInfo.PVTLimit.GuardPeriod.Limit;

    for (int i = 0; i < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit); ++i)
    {
        for (int j = 0; j < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit); ++j)
        {
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].State;

            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Start"]["Time"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Start.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Start"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Start.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Start.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Start.LevelAbs.Limit;

            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Stop"]["Time"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Stop.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Stop"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Stop.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Stop.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].StaticLimt.Stop.LevelAbs.Limit;
            
            for (int k = 0; k < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt); ++k)
            {
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["DynamicLimt"][k]["State"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].DynamicLimt[k].State;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["DynamicLimt"][k]["StartPCL"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].DynamicLimt[k].StartPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["DynamicLimt"][k]["EndPCL"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].DynamicLimt[k].EndPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["RiseEdgeLimit"][j]["DynamicLimt"][k]["Correction"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[j].DynamicLimt[k].Correction;
            }
        }

        for (int j = 0; j < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit); ++j)
        {
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].State;

            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["Time"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Start.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Start.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Start.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Start.LevelAbs.Limit;

            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["Time"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Stop.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Stop.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Stop.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].StaticLimt.Stop.LevelAbs.Limit;
            
            for (int k = 0; k < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt); ++k)
            {
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["State"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].DynamicLimt[k].State;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["StartPCL"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].DynamicLimt[k].StartPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["EndPCL"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].DynamicLimt[k].EndPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["Correction"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[j].DynamicLimt[k].Correction;
            }
        }

        for (int j = 0; j < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit); ++j)
        {
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].State;
            
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Start"]["Time"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Start.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Start"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Start.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Start.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Start.LevelAbs.Limit;

            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Stop"]["Time"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Stop.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Stop"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Stop.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Stop.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].StaticLimt.Stop.LevelAbs.Limit;
            
            for (int k = 0; k < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt); ++k)
            {
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["DynamicLimt"][k]["State"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].DynamicLimt[k].State;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["DynamicLimt"][k]["StartPCL"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].DynamicLimt[k].StartPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["DynamicLimt"][k]["EndPCL"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].DynamicLimt[k].EndPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["UpperTemLimit"][i]["FallEdgeLimit"][j]["DynamicLimt"][k]["Correction"] =
                    gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[j].DynamicLimt[k].Correction;
            }
        }
    }

    for (int i = 0; i < arraySize(gsm.LimitInfo.PVTLimit.LowerTemlimit); ++i)
    {
        for (int j = 0; j < arraySize(gsm.LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit); ++j)
        {
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["State"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].State;

            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["Time"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Start.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Start.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Start.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Start"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Start.LevelAbs.Limit;

            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["Time"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Stop.Time;
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["LevelRel"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Stop.LevelRel;
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["State"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Stop.LevelAbs.State;
            json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["StaticLimt"]["Stop"]["LevelAbs"]["Limit"] =
                gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].StaticLimt.Stop.LevelAbs.Limit;
            
            for (int k = 0; k < arraySize(gsm.LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt); ++k)
            {
                json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["State"] =
                    gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].DynamicLimt[k].State;
                json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["StartPCL"] =
                    gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].DynamicLimt[k].StartPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["EndPCL"] =
                    gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].DynamicLimt[k].EndPCL;
                json_gsm["LimitInfo"]["PVTLimit"]["LowerTemlimit"][i]["UsefulPartLimit"][j]["DynamicLimt"][k]["Correction"] =
                    gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[j].DynamicLimt[k].Correction;
            }
        }
    }
}

void wcdma_param_to_json(json &json_wcdma, const Alg_3GPP_AlzInWCDMA &wcdma)
{
    json_wcdma["LinkDirect"] = wcdma.LinkDirect;

    if (wcdma.LinkDirect == ALG_3GPP_UL)
    {
        wcdma_ul_param_to_json(json_wcdma["UL"], wcdma.UL);
    }
    else
    {
        wcdma_dl_param_to_json(json_wcdma["DL"], wcdma.DL);
    }

    wcdma_measure_param_to_json(json_wcdma["Measure"], wcdma.Measure);
}

void wcdma_ul_param_to_json(json &json_wcdma_ul, const Alg_3GPP_AlzULInWCDMA &wcdma_ul)
{
    json_wcdma_ul["ScramblingCode"] = wcdma_ul.ScramblingCode;
    json_wcdma_ul["DPCCHSlotFormat"] = wcdma_ul.DPCCHSlotFormat;
    json_wcdma_ul["ChannelType"] = wcdma_ul.ChannelType;
    json_wcdma_ul["DPDCHAvailable"] = wcdma_ul.DPDCHAvailable;
    json_wcdma_ul["MeasureLen"] = wcdma_ul.MeasureLen;
    json_wcdma_ul["SyncSlotId"] = wcdma_ul.SyncSlotId;
    json_wcdma_ul["SlotNum"] = wcdma_ul.SlotNum;
    json_wcdma_ul["CDPSpreadFactor"] = wcdma_ul.CDPSpreadFactor;
}

void wcdma_dl_param_to_json(json &json_wcdma_dl, const Alg_3GPP_AlzDLInWCDMA &wcdma_dl)
{
    json_wcdma_dl["ScramblingCode"] = wcdma_dl.ScramblingCode;
    json_wcdma_dl["DPCHNum"] = wcdma_dl.DPCHNum;
    for (int i = 0; i < arraySize(wcdma_dl.DPCH); ++i)
    {
        json_wcdma_dl["DPCH"][i]["State"] = wcdma_dl.DPCH[i].State;
        json_wcdma_dl["DPCH"][i]["SlotFormat"] = wcdma_dl.DPCH[i].SlotFormat;
        json_wcdma_dl["DPCH"][i]["SymbRate"] = wcdma_dl.DPCH[i].SymbRate;
        json_wcdma_dl["DPCH"][i]["ChanCode"] = wcdma_dl.DPCH[i].ChanCode;
        json_wcdma_dl["DPCH"][i]["TimingOffset"] = wcdma_dl.DPCH[i].TimingOffset;
        json_wcdma_dl["DPCH"][i]["TpcDataType"] = wcdma_dl.DPCH[i].TpcDataType;
        json_wcdma_dl["DPCH"][i]["DataType"] = wcdma_dl.DPCH[i].DataType;
        json_wcdma_dl["DPCH"][i]["Initialization"] = wcdma_dl.DPCH[i].Initialization;
        json_wcdma_dl["DPCH"][i]["Power"] = wcdma_dl.DPCH[i].Power;

        json_wcdma_dl["DPCH"][i]["DCH"]["State"] = wcdma_dl.DPCH[i].DCH.State;
        json_wcdma_dl["DPCH"][i]["DCH"]["Interleaver2Stat"] = wcdma_dl.DPCH[i].DCH.Interleaver2Stat;

        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["State"] = wcdma_dl.DPCH[i].DCH.DCCH.State;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["DataType"] = wcdma_dl.DPCH[i].DCH.DCCH.DataType;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["Initialization"] = wcdma_dl.DPCH[i].DCH.DCCH.Initialization;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["TTI"] = wcdma_dl.DPCH[i].DCH.DCCH.TTI;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["TbCount"] = wcdma_dl.DPCH[i].DCH.DCCH.TbCount;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["TbSize"] = wcdma_dl.DPCH[i].DCH.DCCH.TbSize;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["Crc"] = wcdma_dl.DPCH[i].DCH.DCCH.Crc;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["RmAttribute"] = wcdma_dl.DPCH[i].DCH.DCCH.RmAttribute;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["EProtection"] = wcdma_dl.DPCH[i].DCH.DCCH.EProtection;
        json_wcdma_dl["DPCH"][i]["DCH"]["DCCH"]["InterleaverStat"] = wcdma_dl.DPCH[i].DCH.DCCH.InterleaverStat;

        for (int j = 0; j < arraySize(wcdma_dl.DPCH[i].DCH.DTCH); ++j)
        {
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["State"] = wcdma_dl.DPCH[i].DCH.DTCH[j].State;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["DataType"] = wcdma_dl.DPCH[i].DCH.DTCH[j].DataType;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["Initialization"] = wcdma_dl.DPCH[i].DCH.DTCH[j].Initialization;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["TTI"] = wcdma_dl.DPCH[i].DCH.DTCH[j].TTI;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["TbCount"] = wcdma_dl.DPCH[i].DCH.DTCH[j].TbCount;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["TbSize"] = wcdma_dl.DPCH[i].DCH.DTCH[j].TbSize;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["Crc"] = wcdma_dl.DPCH[i].DCH.DTCH[j].Crc;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["RmAttribute"] = wcdma_dl.DPCH[i].DCH.DTCH[j].RmAttribute;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["EProtection"] = wcdma_dl.DPCH[i].DCH.DTCH[j].EProtection;
            json_wcdma_dl["DPCH"][i]["DCH"]["DTCH"][j]["InterleaverStat"] = wcdma_dl.DPCH[i].DCH.DTCH[j].InterleaverStat;
        }
    }

    json_wcdma_dl["PSCH"]["State"] = wcdma_dl.PSCH.State;
    json_wcdma_dl["PSCH"]["SymbRate"] = wcdma_dl.PSCH.SymbRate;
    json_wcdma_dl["PSCH"]["Power"] = wcdma_dl.PSCH.Power;

    json_wcdma_dl["SSCH"]["State"] = wcdma_dl.SSCH.State;
    json_wcdma_dl["SSCH"]["SymbRate"] = wcdma_dl.SSCH.SymbRate;
    json_wcdma_dl["SSCH"]["Power"] = wcdma_dl.SSCH.Power;

    json_wcdma_dl["PCPICH"]["State"] = wcdma_dl.PCPICH.State;
    json_wcdma_dl["PCPICH"]["SymbRate"] = wcdma_dl.PCPICH.SymbRate;
    json_wcdma_dl["PCPICH"]["ChanCode"] = wcdma_dl.PCPICH.ChanCode;
    json_wcdma_dl["PCPICH"]["Power"] = wcdma_dl.PCPICH.Power;
}

void wcdma_measure_param_to_json(json &json_wcdma_measure, const Alg_3GPP_AlzMeasureWCDMA &wcdma_measure)
{
    json_wcdma_measure["AclrLimit1Mode"] = wcdma_measure.AclrLimit1Mode;
    json_wcdma_measure["AclrLimit2Mode"] = wcdma_measure.AclrLimit2Mode;
    json_wcdma_measure["AclrAbsLimitMode"] = wcdma_measure.AclrAbsLimitMode;
    json_wcdma_measure["UtraLimit1"] = wcdma_measure.UtraLimit1;
    json_wcdma_measure["UtraLimit2"] = wcdma_measure.UtraLimit2;
    json_wcdma_measure["AbsLimit"] = wcdma_measure.AbsLimit;

    json_wcdma_measure["SEMLimitADMode"] = wcdma_measure.SEMLimitADMode;
    json_wcdma_measure["SEMLimitEFMode"] = wcdma_measure.SEMLimitEFMode;
    json_wcdma_measure["SEMAbsLimitGMode"] = wcdma_measure.SEMAbsLimitGMode;
    json_wcdma_measure["SEMAbsLimitHMode"] = wcdma_measure.SEMAbsLimitHMode;
    json_wcdma_measure["HMode"] = wcdma_measure.HMode;

    for (int i = 0; i < arraySize(wcdma_measure.LimitAD); ++i) {
        json_wcdma_measure["LimitAD"].emplace_back(wcdma_measure.LimitAD[i]);
    }

    for (int i = 0; i < arraySize(wcdma_measure.LimitEF); ++i) {
        json_wcdma_measure["LimitEF"].emplace_back(wcdma_measure.LimitEF[i]);
    }

    for (int i = 0; i < arraySize(wcdma_measure.SEMAbsLimitG); ++i) {
        json_wcdma_measure["SEMAbsLimitG"].emplace_back(wcdma_measure.SEMAbsLimitG[i]);
    }

    for (int i = 0; i < arraySize(wcdma_measure.SEMAbsLimitH); ++i) {
        json_wcdma_measure["SEMAbsLimitH"].emplace_back(wcdma_measure.SEMAbsLimitH[i]);
    }

    json_wcdma_measure["PeakMagnErrLimitMode"] = wcdma_measure.PeakMagnErrLimitMode;
    json_wcdma_measure["RmsMagnErrLimitMode"] = wcdma_measure.RmsMagnErrLimitMode;
    json_wcdma_measure["PeakMagnErrLimit"] = wcdma_measure.PeakMagnErrLimit;
    json_wcdma_measure["RmsMagnErrLimit"] = wcdma_measure.RmsMagnErrLimit;

    json_wcdma_measure["PeakEvmLimitMode"] = wcdma_measure.PeakEvmLimitMode;
    json_wcdma_measure["RmsEvmLimitMode"] = wcdma_measure.RmsEvmLimitMode;
    json_wcdma_measure["PeakEvmLimit"] = wcdma_measure.PeakEvmLimit;
    json_wcdma_measure["RmsEvmLimit"] = wcdma_measure.RmsEvmLimit;

    json_wcdma_measure["PeakPhaseErrLimitMode"] = wcdma_measure.PeakPhaseErrLimitMode;
    json_wcdma_measure["RmsPhaseErrLimitMode"] = wcdma_measure.RmsPhaseErrLimitMode;
    json_wcdma_measure["PeakPhaseErrLimit"] = wcdma_measure.PeakPhaseErrLimit;
    json_wcdma_measure["RmsPhaseErrLimit"] = wcdma_measure.RmsPhaseErrLimit;

    json_wcdma_measure["CFErrLimitMode"] = wcdma_measure.CFErrLimitMode;
    json_wcdma_measure["CFErrLimit"] = wcdma_measure.CFErrLimit;

    json_wcdma_measure["PhaseDisLimitMode"] = wcdma_measure.PhaseDisLimitMode;
    json_wcdma_measure["UpperLimit"] = wcdma_measure.UpperLimit;
    json_wcdma_measure["DynamicLimit"] = wcdma_measure.DynamicLimit;

    json_wcdma_measure["IQOffsetLimitMode"] = wcdma_measure.IQOffsetLimitMode;
    json_wcdma_measure["IQOffsetLimit"] = wcdma_measure.IQOffsetLimit;

    json_wcdma_measure["IQImabaLimitMode"] = wcdma_measure.IQImabaLimitMode;
    json_wcdma_measure["IQImabaLimit"] = wcdma_measure.IQImabaLimit;
}

void lte_param_to_json(json &json_lte, const Alg_3GPP_AlzIn4g &lte)
{
    json_lte["CarrAggrState"] = lte.CarrAggrState;
    json_lte["LinkDirect"] = lte.LinkDirect;
    json_lte["CyclicPrefix"] = lte.CyclicPrefix;
    json_lte["UeID"] = lte.UeID;
    json_lte["NSValue"] = lte.NSValue;
    json_lte["ChanType"] = lte.ChanType;

    for (int i = 0; i < arraySize(lte.Cell); i++) {
        json_lte["Cell"][i]["CellIdx"] = lte.Cell[i].CellIdx;
        json_lte["Cell"][i]["State"] = lte.Cell[i].State;
        json_lte["Cell"][i]["PhyCellID"] = lte.Cell[i].PhyCellID;
        json_lte["Cell"][i]["ChannelBW"] = lte.Cell[i].ChannelBW;
        json_lte["Cell"][i]["Duplexing"] = lte.Cell[i].Duplexing;
        json_lte["Cell"][i]["ULDLConfig"] = lte.Cell[i].ULDLConfig;
        json_lte["Cell"][i]["SpecialSubfrmConfig"] = lte.Cell[i].SpecialSubfrmConfig;
    }

    if (lte.ChanType == ALG_4G_PUSCH)
    {
        for (int i = 0; i < arraySize(lte.Pusch); i++)
        {
            lte_ul_param_to_json(json_lte["Pusch"][i], lte.Pusch[i]);
        }
    }
    else
    {
        lte_dl_param_to_json(json_lte["Pdsch"], lte.Pdsch);
    }

    lte_measure_param_to_json(json_lte["MeasInfo"], lte.MeasInfo);
    lte_limit_param_to_json(json_lte["LimitInfo"], lte.LimitInfo);
}

void lte_ul_param_to_json(json &json_lte_pusch, const Alg_3GPP_AlzPusch4g &lte_pusch)
{
    json_lte_pusch["CellIdx"] = lte_pusch.CellIdx;
    json_lte_pusch["State"] = lte_pusch.State;
    json_lte_pusch["RBNum"] = lte_pusch.RBNum;
    json_lte_pusch["RBOffset"] = lte_pusch.RBOffset;
    json_lte_pusch["Precoding"] = lte_pusch.Precoding;
    json_lte_pusch["LayerNum"] = lte_pusch.LayerNum;
    json_lte_pusch["AntennaNum"] = lte_pusch.AntennaNum;
    json_lte_pusch["CodebookIdx"] = lte_pusch.CodebookIdx;
    json_lte_pusch["GroupHop"] = lte_pusch.GroupHop;
    json_lte_pusch["SequenceHop"] = lte_pusch.SequenceHop;
    json_lte_pusch["DeltaSeqShift"] = lte_pusch.DeltaSeqShift;
    json_lte_pusch["N1Dmrs"] = lte_pusch.N1Dmrs;
    json_lte_pusch["CyclicShiftField"] = lte_pusch.CyclicShiftField;
    json_lte_pusch["Codeword"] = lte_pusch.Codeword;
    for(int i = 0; i < arraySize(lte_pusch.Modulate); i++) {
        json_lte_pusch["Modulate"][i] = lte_pusch.Modulate[i];
    }
    json_lte_pusch["ChanCodingState"] = lte_pusch.ChanCodingState;
    json_lte_pusch["Scramble"] = lte_pusch.Scramble;
    json_lte_pusch["McsCfgMode"] = lte_pusch.McsCfgMode;
    for(int i = 0; i < arraySize(lte_pusch.Mcs); i++) {
        json_lte_pusch["Mcs"][i] = lte_pusch.Mcs[i];
    }
    for(int i = 0; i < arraySize(lte_pusch.PayloadSize); i++) {
        json_lte_pusch["PayloadSize"][i] = lte_pusch.PayloadSize[i];
    }
    for(int i = 0; i < arraySize(lte_pusch.RedunVerIdx); i++) {
        json_lte_pusch["RedunVerIdx"][i] = lte_pusch.RedunVerIdx[i];
    }
    json_lte_pusch["Enable256QAM"] = lte_pusch.Enable256QAM;
    json_lte_pusch["RBDetMode"] = lte_pusch.RBDetMode;
}

void lte_dl_param_to_json(json &json_lte_pdsch, const Alg_3GPP_AlzPdsch4g &lte_pdsch)
{
    json_lte_pdsch["SymbOffset"] = lte_pdsch.SymbOffset;
    json_lte_pdsch["ResAllocateType"] = lte_pdsch.ResAllocateType;
    json_lte_pdsch["VRBAssignment"] = lte_pdsch.VRBAssignment;
    for(int j = 0; j < arraySize(lte_pdsch.RBGBitmap); j++) {
        json_lte_pdsch["RBGBitmap"][j] = static_cast<int>(lte_pdsch.RBGBitmap[j]);
    }
    json_lte_pdsch["RBNum"] = lte_pdsch.RBNum;
    json_lte_pdsch["RBOffset"] = lte_pdsch.RBOffset;
    json_lte_pdsch["PbchState"] = lte_pdsch.PbchState;
    json_lte_pdsch["Precoding"] = lte_pdsch.Precoding;
    json_lte_pdsch["LayerNum"] = lte_pdsch.LayerNum;
    json_lte_pdsch["AntennaNum"] = lte_pdsch.AntennaNum;
    json_lte_pdsch["CyclicDelayDiversity"] = lte_pdsch.CyclicDelayDiversity;
    json_lte_pdsch["CodebookIdx"] = lte_pdsch.CodebookIdx;
    json_lte_pdsch["Codeword"] = lte_pdsch.Codeword;
    for(int j = 0; j < arraySize(lte_pdsch.Modulate); j++) {
        json_lte_pdsch["Modulate"][j] = lte_pdsch.Modulate[j];
    }
    json_lte_pdsch["ChanCodingState"] = lte_pdsch.ChanCodingState;
    json_lte_pdsch["Scramble"] = lte_pdsch.Scramble;
    json_lte_pdsch["McsCfgMode"] = lte_pdsch.McsCfgMode;
    for(int j = 0; j < arraySize(lte_pdsch.Mcs); j++) {
        json_lte_pdsch["Mcs"][j] = lte_pdsch.Mcs[j];
    }
    for(int j = 0; j < arraySize(lte_pdsch.PayloadSize); j++) {
        json_lte_pdsch["PayloadSize"][j] = lte_pdsch.PayloadSize[j];
    }
    for(int j = 0; j < arraySize(lte_pdsch.RedunVerIdx); j++) {
        json_lte_pdsch["RedunVerIdx"][j] = lte_pdsch.RedunVerIdx[j];
    }
    for(int j = 0; j < arraySize(lte_pdsch.SoftChanBit); j++) {
        json_lte_pdsch["SoftChanBit"][j] = lte_pdsch.SoftChanBit[j];
    }
    json_lte_pdsch["PA"] = lte_pdsch.PA;
    json_lte_pdsch["PB"] = lte_pdsch.PB;
    for(int j = 0; j < arraySize(lte_pdsch.NIR); j++) {
        json_lte_pdsch["NIR"][j] = lte_pdsch.NIR[j];
    }
    json_lte_pdsch["IRConfigMode"] = lte_pdsch.IRConfigMode;
    json_lte_pdsch["TxMode"] = lte_pdsch.TxMode;
    json_lte_pdsch["UECategory"] = lte_pdsch.UECategory;
    json_lte_pdsch["McsTable"] = lte_pdsch.McsTable;
    json_lte_pdsch["TbsIndexAlt"] = lte_pdsch.TbsIndexAlt;
}

void lte_measure_param_to_json(json &json_lte_measure, const Alg_3GPP_MeasureIn4g &lte_measure)
{
    // 测量子帧设置
    json_lte_measure["MeasSubfrmIdx"] = lte_measure.MeasSubfrmIdx;
    json_lte_measure["MeasSubfrmCount"] = lte_measure.MeasSubfrmCount;
    json_lte_measure["MeasSubfrmOffset"] = lte_measure.MeasSubfrmOffset;
    json_lte_measure["MeasSlotType"] = lte_measure.MeasSlotType;

    // EVM vs modulation symbol
    json_lte_measure["EvmSymbEnable"] = lte_measure.EvmSymbEnable;
    json_lte_measure["EvmSymbIdx"] = lte_measure.EvmSymbIdx;
    json_lte_measure["EvmSymbWinType"] = lte_measure.EvmSymbWinType;

    // DMRS
    json_lte_measure["DmrsConsState"] = lte_measure.DmrsConsState;

    // 同步和测量单位
    json_lte_measure["SyncMode"] = lte_measure.SyncMode;
    json_lte_measure["MeasureUnit"] = lte_measure.MeasureUnit;

    // 解码
    json_lte_measure["DecodingEnable"] = lte_measure.DecodingEnable;

    // 调制测量
    json_lte_measure["Modulate"]["ModEnable"] = lte_measure.Modulate.ModEnable;
    json_lte_measure["Modulate"]["EvmEnable"] = lte_measure.Modulate.EvmEnable;
    json_lte_measure["Modulate"]["MErrEnable"] = lte_measure.Modulate.MErrEnable;
    json_lte_measure["Modulate"]["PErrEnable"] = lte_measure.Modulate.PErrEnable;
    json_lte_measure["Modulate"]["EvmSubcarEnable"] = lte_measure.Modulate.EvmSubcarEnable;
    json_lte_measure["Modulate"]["IBEEnable"] = lte_measure.Modulate.IBEEnable;
    json_lte_measure["Modulate"]["ESFlatEnable"] = lte_measure.Modulate.ESFlatEnable;
    json_lte_measure["Modulate"]["IQConstelEnable"] = lte_measure.Modulate.IQConstelEnable;
    json_lte_measure["Modulate"]["TxMeasureEnable"] = lte_measure.Modulate.TxMeasureEnable;
    json_lte_measure["Modulate"]["ModStatNum"] = lte_measure.Modulate.ModStatNum;
    json_lte_measure["Modulate"]["ExPeriodLeading"] = lte_measure.Modulate.ExPeriodLeading;
    json_lte_measure["Modulate"]["ExPeriodLagging"] = lte_measure.Modulate.ExPeriodLagging;
    json_lte_measure["Modulate"]["ExAbnSymbFlg"] = lte_measure.Modulate.ExAbnSymbFlg;
    json_lte_measure["Modulate"]["Equalizer"] = lte_measure.Modulate.Equalizer;
    for (int i = 0; i < arraySize(lte_measure.Modulate.EvmWinNcp); i++) {
        json_lte_measure["Modulate"]["EvmWinNcp"][i] = lte_measure.Modulate.EvmWinNcp[i];
    }
    for (int i = 0; i < arraySize(lte_measure.Modulate.EvmWinEcp); i++) {
        json_lte_measure["Modulate"]["EvmWinEcp"][i] = lte_measure.Modulate.EvmWinEcp[i];
    }
    

    // 频谱测量
    json_lte_measure["Spectrum"]["SpectEnable"] = lte_measure.Spectrum.SpectEnable;
    json_lte_measure["Spectrum"]["OBWEnable"] = lte_measure.Spectrum.OBWEnable;
    json_lte_measure["Spectrum"]["SEMEnable"] = lte_measure.Spectrum.SEMEnable;
    json_lte_measure["Spectrum"]["MeasFilter"] = lte_measure.Spectrum.MeasFilter;
    json_lte_measure["Spectrum"]["SEMStatNum"] = lte_measure.Spectrum.SEMStatNum;
    json_lte_measure["Spectrum"]["ACLREnable"] = lte_measure.Spectrum.ACLREnable;
    json_lte_measure["Spectrum"]["UTRA1Enable"] = lte_measure.Spectrum.UTRA1Enable;
    json_lte_measure["Spectrum"]["UTRA2Enable"] = lte_measure.Spectrum.UTRA2Enable;
    json_lte_measure["Spectrum"]["EUTRA1Enable"] = lte_measure.Spectrum.EUTRA1Enable;
    json_lte_measure["Spectrum"]["EUTRA2Enable"] = lte_measure.Spectrum.EUTRA2Enable;
    json_lte_measure["Spectrum"]["ACLRStatNum"] = lte_measure.Spectrum.ACLRStatNum;

    // 功率测量
    json_lte_measure["Power"]["PMonitorEnable"] = lte_measure.Power.PMonitorEnable;
    json_lte_measure["Power"]["PowerEnable"] = lte_measure.Power.PowerEnable;
    json_lte_measure["Power"]["PowerStatNum"] = lte_measure.Power.PowerStatNum;
    json_lte_measure["Power"]["PwrDynEnable"] = lte_measure.Power.PwrDynEnable;
    json_lte_measure["Power"]["TimeMaskType"] = lte_measure.Power.TimeMaskType;
    json_lte_measure["Power"]["Leading"] = lte_measure.Power.Leading;
    json_lte_measure["Power"]["Lagging"] = lte_measure.Power.Lagging;
    json_lte_measure["Power"]["HighDynmMode"] = lte_measure.Power.HighDynmMode;
}

void lte_limit_param_to_json(json &json_lte_limit, const Alg_3GPP_LimitIn4g &lte_limit)
{
    // 转换调制限制参数
    for(int i = 0; i < arraySize(lte_limit.ModLimit); i++) {
        // EVM RMS
        json_lte_limit["ModLimit"][i]["EvmRms"]["State"] = lte_limit.ModLimit[i].EvmRms.State;
        json_lte_limit["ModLimit"][i]["EvmRms"]["Limit"] = lte_limit.ModLimit[i].EvmRms.Limit;
        
        // EVM Peak
        json_lte_limit["ModLimit"][i]["EvmPeak"]["State"] = lte_limit.ModLimit[i].EvmPeak.State;
        json_lte_limit["ModLimit"][i]["EvmPeak"]["Limit"] = lte_limit.ModLimit[i].EvmPeak.Limit;
        
        // Magnitude Error RMS
        json_lte_limit["ModLimit"][i]["MErrRms"]["State"] = lte_limit.ModLimit[i].MErrRms.State;
        json_lte_limit["ModLimit"][i]["MErrRms"]["Limit"] = lte_limit.ModLimit[i].MErrRms.Limit;
        
        // Magnitude Error Peak
        json_lte_limit["ModLimit"][i]["MErrPeak"]["State"] = lte_limit.ModLimit[i].MErrPeak.State;
        json_lte_limit["ModLimit"][i]["MErrPeak"]["Limit"] = lte_limit.ModLimit[i].MErrPeak.Limit;
        
        // Phase Error RMS
        json_lte_limit["ModLimit"][i]["PhErrRms"]["State"] = lte_limit.ModLimit[i].PhErrRms.State;
        json_lte_limit["ModLimit"][i]["PhErrRms"]["Limit"] = lte_limit.ModLimit[i].PhErrRms.Limit;
        
        // Phase Error Peak
        json_lte_limit["ModLimit"][i]["PhErrPeak"]["State"] = lte_limit.ModLimit[i].PhErrPeak.State;
        json_lte_limit["ModLimit"][i]["PhErrPeak"]["Limit"] = lte_limit.ModLimit[i].PhErrPeak.Limit;
        
        // Frequency Error
        json_lte_limit["ModLimit"][i]["FreqErr"]["State"] = lte_limit.ModLimit[i].FreqErr.State;
        json_lte_limit["ModLimit"][i]["FreqErr"]["Limit"] = lte_limit.ModLimit[i].FreqErr.Limit;
        
        // IQ Offset
        json_lte_limit["ModLimit"][i]["IQOffset"]["State"] = lte_limit.ModLimit[i].IQOffset.State;
        for(int j = 0; j < arraySize(lte_limit.ModLimit[i].IQOffset.PwrLimit); j++) {
            json_lte_limit["ModLimit"][i]["IQOffset"]["PwrLimit"].emplace_back(lte_limit.ModLimit[i].IQOffset.PwrLimit[j]);
        }
        
        // IBE
        json_lte_limit["ModLimit"][i]["IBE"]["State"] = lte_limit.ModLimit[i].IBE.State;
        json_lte_limit["ModLimit"][i]["IBE"]["GenMin"] = lte_limit.ModLimit[i].IBE.GenMin;
        json_lte_limit["ModLimit"][i]["IBE"]["GenEVM"] = lte_limit.ModLimit[i].IBE.GenEVM;
        json_lte_limit["ModLimit"][i]["IBE"]["GenPwr"] = lte_limit.ModLimit[i].IBE.GenPwr;
        for(int j = 0; j < arraySize(lte_limit.ModLimit[i].IBE.IQImage); j++) {
            json_lte_limit["ModLimit"][i]["IBE"]["IQImage"].emplace_back(lte_limit.ModLimit[i].IBE.IQImage[j]);
        }
        for(int j = 0; j < arraySize(lte_limit.ModLimit[i].IBE.IQOffsetPwr); j++) {
            json_lte_limit["ModLimit"][i]["IBE"]["IQOffsetPwr"].emplace_back(lte_limit.ModLimit[i].IBE.IQOffsetPwr[j]);
        }
        
        // Spectrum Flatness
        json_lte_limit["ModLimit"][i]["SpectFlat"]["State"] = lte_limit.ModLimit[i].SpectFlat.State;
        json_lte_limit["ModLimit"][i]["SpectFlat"]["Range1"] = lte_limit.ModLimit[i].SpectFlat.Range1;
        json_lte_limit["ModLimit"][i]["SpectFlat"]["Range2"] = lte_limit.ModLimit[i].SpectFlat.Range2;
        json_lte_limit["ModLimit"][i]["SpectFlat"]["Max1Min2"] = lte_limit.ModLimit[i].SpectFlat.Max1Min2;
        json_lte_limit["ModLimit"][i]["SpectFlat"]["Max2Min1"] = lte_limit.ModLimit[i].SpectFlat.Max2Min1;
        json_lte_limit["ModLimit"][i]["SpectFlat"]["EdgeFreq"] = lte_limit.ModLimit[i].SpectFlat.EdgeFreq;
    }
    
    // 转换频谱限制参数
    for(int i = 0; i < arraySize(lte_limit.SpectLimit); i++) {
        // OBW Limit
        json_lte_limit["SpectLimit"][i]["OBWLimit"]["State"] = lte_limit.SpectLimit[i].OBWLimit.State;
        json_lte_limit["SpectLimit"][i]["OBWLimit"]["Limit"] = lte_limit.SpectLimit[i].OBWLimit.Limit;
        
        // SEM Limit
        for(int j = 0; j < arraySize(lte_limit.SpectLimit[i].SEMLimit); j++) {
            for(int k = 0; k < arraySize(lte_limit.SpectLimit[i].SEMLimit[j]); k++) {
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["State"] = lte_limit.SpectLimit[i].SEMLimit[j][k].State;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["StartFreq"] = lte_limit.SpectLimit[i].SEMLimit[j][k].StartFreq;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["StopFreq"] = lte_limit.SpectLimit[i].SEMLimit[j][k].StopFreq;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["LimitPower"] = lte_limit.SpectLimit[i].SEMLimit[j][k].LimitPower;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["StartPower"] = lte_limit.SpectLimit[i].SEMLimit[j][k].StartPower;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["StopPower"] = lte_limit.SpectLimit[i].SEMLimit[j][k].StopPower;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["Slope"] = lte_limit.SpectLimit[i].SEMLimit[j][k].Slope;
                json_lte_limit["SpectLimit"][i]["SEMLimit"][j][k]["RBW"] = lte_limit.SpectLimit[i].SEMLimit[j][k].RBW;
            }
        }
        
        // UTRA Limit
        for(int j = 0; j < arraySize(lte_limit.SpectLimit[i].UtraLimit); j++) {
            json_lte_limit["SpectLimit"][i]["UtraLimit"][j]["RelState"] = lte_limit.SpectLimit[i].UtraLimit[j].RelState;
            json_lte_limit["SpectLimit"][i]["UtraLimit"][j]["AbsState"] = lte_limit.SpectLimit[i].UtraLimit[j].AbsState;
            json_lte_limit["SpectLimit"][i]["UtraLimit"][j]["RelLimit"] = lte_limit.SpectLimit[i].UtraLimit[j].RelLimit;
            json_lte_limit["SpectLimit"][i]["UtraLimit"][j]["AbsPwr"] = lte_limit.SpectLimit[i].UtraLimit[j].AbsPwr;
        }
        
        // EUTRA Limit
        for(int j = 0; j < arraySize(lte_limit.SpectLimit[i].EUtraLimit); j++) {
            json_lte_limit["SpectLimit"][i]["EUtraLimit"][j]["RelState"] = lte_limit.SpectLimit[i].EUtraLimit[j].RelState;
            json_lte_limit["SpectLimit"][i]["EUtraLimit"][j]["AbsState"] = lte_limit.SpectLimit[i].EUtraLimit[j].AbsState;
            json_lte_limit["SpectLimit"][i]["EUtraLimit"][j]["RelLimit"] = lte_limit.SpectLimit[i].EUtraLimit[j].RelLimit;
            json_lte_limit["SpectLimit"][i]["EUtraLimit"][j]["AbsPwr"] = lte_limit.SpectLimit[i].EUtraLimit[j].AbsPwr;
        }
    }
    
    // 转换SEM附加测试容限
    for(int i = 0; i < arraySize(lte_limit.SEMAddTestTol); i++) {
        json_lte_limit["SEMAddTestTol"][i] = lte_limit.SEMAddTestTol[i];
    }
    
    // 转换功率限制参数
    for(int i = 0; i < arraySize(lte_limit.PwrLimit); i++) {
        json_lte_limit["PwrLimit"][i]["State"] = lte_limit.PwrLimit[i].State;
        json_lte_limit["PwrLimit"][i]["OnLimitUpper"] = lte_limit.PwrLimit[i].OnLimitUpper;
        json_lte_limit["PwrLimit"][i]["OnLimitLower"] = lte_limit.PwrLimit[i].OnLimitLower;
        json_lte_limit["PwrLimit"][i]["OffLimit"] = lte_limit.PwrLimit[i].OffLimit;
    }
}

void nr_param_to_json(json &json_nr, const Alg_3GPP_AlzIn5g &nr)
{
    json_nr["LinkDirect"] = nr.LinkDirect;
    json_nr["Version"] = nr.Version;
    json_nr["RedcapEnable"] = nr.RedcapEnable;

    if (nr.LinkDirect == ALG_3GPP_UL)
    {
        nr_ul_param_to_json(json_nr["UL"], nr.UL);
    }
    else
    {
        nr_dl_param_to_json(json_nr["DL"], nr.DL);
    }

    nr_measure_param_to_json(json_nr["Measure"], nr.Measure);
    nr_limit_param_to_json(json_nr["LimitInfo"], nr.LimitInfo);
}

void nr_ul_param_to_json(json &json_nr_ul, const Alg_3GPP_AlzULIn5g &nr_ul)
{
    json_nr_ul["Duplexing"] = nr_ul.Duplexing;
    json_nr_ul["SlotPeriod"] = nr_ul.SlotPeriod;
    json_nr_ul["NSValue"] = nr_ul.NSValue;
    json_nr_ul["RfPhaseCompen"] = nr_ul.RfPhaseCompen;
    json_nr_ul["ChanType"] = nr_ul.ChanType;
    json_nr_ul["Frequency"] = nr_ul.Frequency;

    for (int i = 0; i < arraySize(nr_ul.TDDPat); ++i)
    {
        json_nr_ul["TDDPat"][i]["ULSlotNumber"] = nr_ul.TDDPat[i].ULSlotNumber;
        json_nr_ul["TDDPat"][i]["DLSlotNumber"] = nr_ul.TDDPat[i].DLSlotNumber;
        json_nr_ul["TDDPat"][i]["ULSymbNumber"] = nr_ul.TDDPat[i].ULSymbNumber;
        json_nr_ul["TDDPat"][i]["DLSymbNumber"] = nr_ul.TDDPat[i].DLSymbNumber;
    }

    json_nr_ul["CellNum"] = nr_ul.CellNum;
    for (int i = 0; i < arraySize(nr_ul.Cell); ++i)
    {
        json_nr_ul["Cell"][i]["ChannelBW"] = nr_ul.Cell[i].ChannelBW;
        json_nr_ul["Cell"][i]["PhyCellID"] = nr_ul.Cell[i].PhyCellID;
        json_nr_ul["Cell"][i]["DmrsTypeAPos"] = nr_ul.Cell[i].DmrsTypeAPos;
        json_nr_ul["Cell"][i]["UseSCSpacing"] = nr_ul.Cell[i].UseSCSpacing;
        json_nr_ul["Cell"][i]["OffsetToCarrier"] = nr_ul.Cell[i].OffsetToCarrier;

        json_nr_ul["Cell"][i]["Bwp"]["SCSpacing"] = nr_ul.Cell[i].Bwp.SCSpacing;
        json_nr_ul["Cell"][i]["Bwp"]["CyclicPrefix"] = nr_ul.Cell[i].Bwp.CyclicPrefix;
        json_nr_ul["Cell"][i]["Bwp"]["RBNum"] = nr_ul.Cell[i].Bwp.RBNum;
        json_nr_ul["Cell"][i]["Bwp"]["RBOffset"] = nr_ul.Cell[i].Bwp.RBOffset;
        json_nr_ul["Cell"][i]["Bwp"]["UseR16Dmrs"] = nr_ul.Cell[i].Bwp.UseR16Dmrs;
        json_nr_ul["Cell"][i]["Bwp"]["TransformPrecoder"] = nr_ul.Cell[i].Bwp.TransformPrecoder;
        for (int j = 0; j < arraySize(nr_ul.Cell[i].Bwp.ConfigType); ++j)
        {
            json_nr_ul["Cell"][i]["Bwp"]["ConfigType"][j] = nr_ul.Cell[i].Bwp.ConfigType[j];
        }
        for (int j = 0; j < arraySize(nr_ul.Cell[i].Bwp.MaxLength); ++j)
        {
            json_nr_ul["Cell"][i]["Bwp"]["MaxLength"][j] = nr_ul.Cell[i].Bwp.MaxLength[j];
        }
        for (int j = 0; j < arraySize(nr_ul.Cell[i].Bwp.AdditionalPos); ++j)
        {
            json_nr_ul["Cell"][i]["Bwp"]["AdditionalPos"][j] = nr_ul.Cell[i].Bwp.AdditionalPos[j];
        }
    }

    for (int i = 0; i < arraySize(nr_ul.Pusch); ++i)
    {
        json_nr_ul["Pusch"][i]["MappingType"] = nr_ul.Pusch[i].MappingType;
        json_nr_ul["Pusch"][i]["SymNum"] = nr_ul.Pusch[i].SymNum;
        json_nr_ul["Pusch"][i]["SymbOffset"] = nr_ul.Pusch[i].SymbOffset;
        json_nr_ul["Pusch"][i]["RBDetMode"] = nr_ul.Pusch[i].RBDetMode;
        json_nr_ul["Pusch"][i]["RBNum"] = nr_ul.Pusch[i].RBNum;
        json_nr_ul["Pusch"][i]["RBOffset"] = nr_ul.Pusch[i].RBOffset;
        json_nr_ul["Pusch"][i]["CDMGrpWOData"] = nr_ul.Pusch[i].CDMGrpWOData;
        for (int j = 0; j < arraySize(nr_ul.Pusch[i].DmrsAntPort); ++j)
        {
            json_nr_ul["Pusch"][i]["DmrsAntPort"][j] = nr_ul.Pusch[i].DmrsAntPort[j];
        }
        json_nr_ul["Pusch"][i]["DmrsSymbLen"] = nr_ul.Pusch[i].DmrsSymbLen;
        json_nr_ul["Pusch"][i]["DmrsInitType"] = nr_ul.Pusch[i].DmrsInitType;
        json_nr_ul["Pusch"][i]["DmrsID"] = nr_ul.Pusch[i].DmrsID;
        json_nr_ul["Pusch"][i]["NSCID"] = nr_ul.Pusch[i].NSCID;
        json_nr_ul["Pusch"][i]["Modulate"] = nr_ul.Pusch[i].Modulate;
    }
}

void nr_dl_param_to_json(json &json_nr_dl, const Alg_3GPP_AlzDLIn5g &nr_dl)
{
    json_nr_dl["Duplexing"] = nr_dl.Duplexing;
    json_nr_dl["SlotPeriod"] = nr_dl.SlotPeriod;
    json_nr_dl["RfPhaseCompen"] = nr_dl.RfPhaseCompen;
    json_nr_dl["Frequency"] = nr_dl.Frequency;
    json_nr_dl["CellNum"] = nr_dl.CellNum;
    json_nr_dl["ChanType"] = nr_dl.ChanType;

    for (int i = 0; i < arraySize(nr_dl.TDDPat); ++i) {
        json_nr_dl["TDDPat"][i]["ULSlotNumber"] = nr_dl.TDDPat[i].ULSlotNumber;
        json_nr_dl["TDDPat"][i]["DLSlotNumber"] = nr_dl.TDDPat[i].DLSlotNumber;
        json_nr_dl["TDDPat"][i]["ULSymbNumber"] = nr_dl.TDDPat[i].ULSymbNumber;
        json_nr_dl["TDDPat"][i]["DLSymbNumber"] = nr_dl.TDDPat[i].DLSymbNumber;
    }

    for (int i = 0; i < arraySize(nr_dl.Cell); ++i)
    {
        json_nr_dl["Cell"][i]["ChannelBW"] = nr_dl.Cell[i].ChannelBW;
        json_nr_dl["Cell"][i]["PhyCellID"] = nr_dl.Cell[i].PhyCellID;
        json_nr_dl["Cell"][i]["DmrsTypeAPos"] = nr_dl.Cell[i].DmrsTypeAPos;
        json_nr_dl["Cell"][i]["UseSCSpacing"] = nr_dl.Cell[i].UseSCSpacing;
        json_nr_dl["Cell"][i]["OffsetToCarrier"] = nr_dl.Cell[i].OffsetToCarrier;

        json_nr_dl["Cell"][i]["Bwp"]["SCSpacing"] = nr_dl.Cell[i].Bwp.SCSpacing;
        json_nr_dl["Cell"][i]["Bwp"]["CyclicPrefix"] = nr_dl.Cell[i].Bwp.CyclicPrefix;
        json_nr_dl["Cell"][i]["Bwp"]["RBNum"] = nr_dl.Cell[i].Bwp.RBNum;
        json_nr_dl["Cell"][i]["Bwp"]["RBOffset"] = nr_dl.Cell[i].Bwp.RBOffset;

        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["VrbToPrbInterleaver"] = nr_dl.Cell[i].Bwp.Pdsch.VrbToPrbInterleaver;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["McsTab"] = nr_dl.Cell[i].Bwp.Pdsch.McsTab;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["ResourceAllocation"] = nr_dl.Cell[i].Bwp.Pdsch.ResourceAllocation;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["ConfigType"] = nr_dl.Cell[i].Bwp.Pdsch.ConfigType;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["MaxLength"] = nr_dl.Cell[i].Bwp.Pdsch.MaxLength;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["AdditionalPos"] = nr_dl.Cell[i].Bwp.Pdsch.AdditionalPos;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["RBGSizeType"] = nr_dl.Cell[i].Bwp.Pdsch.RBGSizeType;
        json_nr_dl["Cell"][i]["Bwp"]["Pdsch"]["UseR16Dmrs"] = nr_dl.Cell[i].Bwp.Pdsch.UseR16Dmrs;
        
        json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["State"] = nr_dl.Cell[i].Bwp.Coreset.State;
        json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["SymbNum"] = nr_dl.Cell[i].Bwp.Coreset.SymbNum;
        json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["SymbOffset"] = nr_dl.Cell[i].Bwp.Coreset.SymbOffset;
        json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["FDResUseBitmap"] = nr_dl.Cell[i].Bwp.Coreset.FDResUseBitmap;
        json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["RBNum"] = nr_dl.Cell[i].Bwp.Coreset.RBNum;
        json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["RBOffset"] = nr_dl.Cell[i].Bwp.Coreset.RBOffset;
        for (int j = 0; j < arraySize(nr_dl.Cell[i].Bwp.Coreset.BitMap); ++j) {
            json_nr_dl["Cell"][i]["Bwp"]["Coreset"]["BitMap"].emplace_back(static_cast<int>(nr_dl.Cell[i].Bwp.Coreset.BitMap[j]));
        }
    }

    for (int i = 0; i < arraySize(nr_dl.Channel); ++i)
    {
        json_nr_dl["Channel"][i]["Pbch"]["State"] = nr_dl.Channel[i].Pbch.State;
        json_nr_dl["Channel"][i]["Pbch"]["SCSpacing"] = nr_dl.Channel[i].Pbch.SCSpacing;
        json_nr_dl["Channel"][i]["Pbch"]["RBOffset"] = nr_dl.Channel[i].Pbch.RBOffset;
        json_nr_dl["Channel"][i]["Pbch"]["SCOffset"] = nr_dl.Channel[i].Pbch.SCOffset;
        json_nr_dl["Channel"][i]["Pbch"]["PbchCase"] = nr_dl.Channel[i].Pbch.PbchCase;
        json_nr_dl["Channel"][i]["Pbch"]["Length"] = nr_dl.Channel[i].Pbch.Length;
        json_nr_dl["Channel"][i]["Pbch"]["BurstSetPeriod"] = nr_dl.Channel[i].Pbch.BurstSetPeriod;
        json_nr_dl["Channel"][i]["Pbch"]["HalfFrmIdx"] = nr_dl.Channel[i].Pbch.HalfFrmIdx;
        json_nr_dl["Channel"][i]["Pbch"]["OffsetRefType"] = nr_dl.Channel[i].Pbch.OffsetRefType;
        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pbch.Position); ++j) {
            json_nr_dl["Channel"][i]["Pbch"]["Position"].emplace_back(static_cast<int>(nr_dl.Channel[i].Pbch.Position[j]));
        }

        json_nr_dl["Channel"][i]["Pdcch"]["State"] = nr_dl.Channel[i].Pdcch.State;

        json_nr_dl["Channel"][i]["Pdsch"]["MappingType"] = nr_dl.Channel[i].Pdsch.MappingType;
        json_nr_dl["Channel"][i]["Pdsch"]["SymbNum"] = nr_dl.Channel[i].Pdsch.SymbNum;
        json_nr_dl["Channel"][i]["Pdsch"]["SymbOffset"] = nr_dl.Channel[i].Pdsch.SymbOffset;
        json_nr_dl["Channel"][i]["Pdsch"]["RBNum"] = nr_dl.Channel[i].Pdsch.RBNum;
        json_nr_dl["Channel"][i]["Pdsch"]["RBOffset"] = nr_dl.Channel[i].Pdsch.RBOffset;
        json_nr_dl["Channel"][i]["Pdsch"]["LayerNum"] = nr_dl.Channel[i].Pdsch.LayerNum;
        json_nr_dl["Channel"][i]["Pdsch"]["AntennaNum"] = nr_dl.Channel[i].Pdsch.AntennaNum;
        json_nr_dl["Channel"][i]["Pdsch"]["CDMGrpWOData"] = nr_dl.Channel[i].Pdsch.CDMGrpWOData;
        json_nr_dl["Channel"][i]["Pdsch"]["DmrsSymbLen"] = nr_dl.Channel[i].Pdsch.DmrsSymbLen;
        json_nr_dl["Channel"][i]["Pdsch"]["DmrsInitType"] = nr_dl.Channel[i].Pdsch.DmrsInitType;
        json_nr_dl["Channel"][i]["Pdsch"]["DmrsID"] = nr_dl.Channel[i].Pdsch.DmrsID;
        json_nr_dl["Channel"][i]["Pdsch"]["NSCID"] = nr_dl.Channel[i].Pdsch.NSCID;
        json_nr_dl["Channel"][i]["Pdsch"]["Codewords"] = nr_dl.Channel[i].Pdsch.Codewords;
        json_nr_dl["Channel"][i]["Pdsch"]["ChanCodingState"] = nr_dl.Channel[i].Pdsch.ChanCodingState;
        json_nr_dl["Channel"][i]["Pdsch"]["Scrambling"] = nr_dl.Channel[i].Pdsch.Scrambling;
        json_nr_dl["Channel"][i]["Pdsch"]["UsePdschScrambleID"] = nr_dl.Channel[i].Pdsch.UsePdschScrambleID;
        json_nr_dl["Channel"][i]["Pdsch"]["DataScrambleID"] = nr_dl.Channel[i].Pdsch.DataScrambleID;
        json_nr_dl["Channel"][i]["Pdsch"]["UeID"] = nr_dl.Channel[i].Pdsch.UeID;

        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pdsch.DmrsAntPort); ++j) {
            json_nr_dl["Channel"][i]["Pdsch"]["DmrsAntPort"].emplace_back(nr_dl.Channel[i].Pdsch.DmrsAntPort[j]);
        }
        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pdsch.Modulate); ++j) {
            json_nr_dl["Channel"][i]["Pdsch"]["Modulate"].emplace_back(nr_dl.Channel[i].Pdsch.Modulate[j]);
        }
        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pdsch.MCS); ++j) {
            json_nr_dl["Channel"][i]["Pdsch"]["MCS"].emplace_back(nr_dl.Channel[i].Pdsch.MCS[j]);
        }
        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pdsch.RvIdx); ++j) {
            json_nr_dl["Channel"][i]["Pdsch"]["RvIdx"].emplace_back(nr_dl.Channel[i].Pdsch.RvIdx[j]);
        }
        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pdsch.Bitmap); ++j) {
            json_nr_dl["Channel"][i]["Pdsch"]["Bitmap"].emplace_back(static_cast<int>(nr_dl.Channel[i].Pdsch.Bitmap[j]));
        }
    }
}

void nr_measure_param_to_json(json &json_nr_meas, const Alg_3GPP_AlzMeasure5g &nr_meas)
{
    json_nr_meas["MeasureExpect"] = nr_meas.MeasureExpect;
    json_nr_meas["VFilterEnable"] = nr_meas.VFilterEnable;
    json_nr_meas["NRBViewFilter"] = nr_meas.NRBViewFilter;
    json_nr_meas["MeasSFNum"] = nr_meas.MeasSFNum;
    json_nr_meas["MeasAllEnable"] = nr_meas.MeasAllEnable;
    json_nr_meas["MeasSlotIdx"] = nr_meas.MeasSlotIdx;
    json_nr_meas["ModStatNum"] = nr_meas.ModStatNum;
    json_nr_meas["PhaseTrack"] = nr_meas.PhaseTrack;
    json_nr_meas["TimingTrack"] = nr_meas.TimingTrack;
    json_nr_meas["LevelTrack"] = nr_meas.LevelTrack;
    json_nr_meas["TxDCOffset"] = nr_meas.TxDCOffset;
    json_nr_meas["SEMStatNum"] = nr_meas.SEMStatNum;
    json_nr_meas["SEMMeasFilter"] = nr_meas.SEMMeasFilter;
    json_nr_meas["ACLRStatNum"] = nr_meas.ACLRStatNum;
    json_nr_meas["PwrDynStatNum"] = nr_meas.PwrDynStatNum;
    json_nr_meas["TxPwrStatNum"] = nr_meas.TxPwrStatNum;
    for (int i = 0; i < arraySize(nr_meas.EvmWinLen); ++i) {
        for (int j = 0; j < arraySize(nr_meas.EvmWinLen[i]); ++j) {
            json_nr_meas["EvmWinLen"][i][j] = nr_meas.EvmWinLen[i][j];
        }
    }
    for (int i = 0; i < arraySize(nr_meas.UTRAEnable); ++i) {
        json_nr_meas["UTRAEnable"].emplace_back(nr_meas.UTRAEnable[i]);
    }
}

void nr_limit_param_to_json(json &json_nr_limit, const Alg_3GPP_LimitIn5g &nr_limit)
{
    for (int i = 0; i < arraySize(nr_limit.ModLimit); ++i) {
        json_nr_limit["ModLimit"][i]["EvmRms"]["Limit"] = nr_limit.ModLimit[i].EvmRms.Limit;
        json_nr_limit["ModLimit"][i]["EvmRms"]["State"] = nr_limit.ModLimit[i].EvmRms.State;
        json_nr_limit["ModLimit"][i]["EvmPeak"]["Limit"] = nr_limit.ModLimit[i].EvmPeak.Limit;
        json_nr_limit["ModLimit"][i]["EvmPeak"]["State"] = nr_limit.ModLimit[i].EvmPeak.State;
        json_nr_limit["ModLimit"][i]["MErrRms"]["Limit"] = nr_limit.ModLimit[i].MErrRms.Limit;
        json_nr_limit["ModLimit"][i]["MErrRms"]["State"] = nr_limit.ModLimit[i].MErrRms.State;
        json_nr_limit["ModLimit"][i]["MErrPeak"]["Limit"] = nr_limit.ModLimit[i].MErrPeak.Limit;
        json_nr_limit["ModLimit"][i]["MErrPeak"]["State"] = nr_limit.ModLimit[i].MErrPeak.State;
        json_nr_limit["ModLimit"][i]["PhErrRms"]["State"] = nr_limit.ModLimit[i].PhErrRms.State;
        json_nr_limit["ModLimit"][i]["PhErrRms"]["Limit"] = nr_limit.ModLimit[i].PhErrRms.Limit;
        json_nr_limit["ModLimit"][i]["PhErrPeak"]["State"] = nr_limit.ModLimit[i].PhErrPeak.State;
        json_nr_limit["ModLimit"][i]["PhErrPeak"]["Limit"] = nr_limit.ModLimit[i].PhErrPeak.Limit;
        json_nr_limit["ModLimit"][i]["FreqErr"]["State"] = nr_limit.ModLimit[i].FreqErr.State;
        json_nr_limit["ModLimit"][i]["FreqErr"]["Limit"] = nr_limit.ModLimit[i].FreqErr.Limit;
        json_nr_limit["ModLimit"][i]["IQOffset"]["State"] = nr_limit.ModLimit[i].IQOffset.State;

        for (int j = 0; j < arraySize(nr_limit.ModLimit[i].IQOffset.PwrLimit); ++j) {
            json_nr_limit["ModLimit"][i]["IQOffset"]["PwrLimit"].emplace_back(nr_limit.ModLimit[i].IQOffset.PwrLimit[j]);
        }

        json_nr_limit["ModLimit"][i]["IBE"]["State"] = nr_limit.ModLimit[i].IBE.State;
        json_nr_limit["ModLimit"][i]["IBE"]["GenMin"] = nr_limit.ModLimit[i].IBE.GenMin;
        json_nr_limit["ModLimit"][i]["IBE"]["GenEVM"] = nr_limit.ModLimit[i].IBE.GenEVM;
        json_nr_limit["ModLimit"][i]["IBE"]["GenPwr"] = nr_limit.ModLimit[i].IBE.GenPwr;
        for (int j = 0; j < arraySize(nr_limit.ModLimit[i].IBE.IQImage); ++j) {
            json_nr_limit["ModLimit"][i]["IBE"]["IQImage"].emplace_back(nr_limit.ModLimit[i].IBE.IQImage[j]);
        }
        for (int j = 0; j < arraySize(nr_limit.ModLimit[i].IBE.IQOffsetPwr); ++j) {
            json_nr_limit["ModLimit"][i]["IBE"]["IQOffsetPwr"].emplace_back(nr_limit.ModLimit[i].IBE.IQOffsetPwr[j]);
        }

        json_nr_limit["ModLimit"][i]["SpectFlat"]["State"] = nr_limit.ModLimit[i].SpectFlat.State;
        json_nr_limit["ModLimit"][i]["SpectFlat"]["Range1"] = nr_limit.ModLimit[i].SpectFlat.Range1;
        json_nr_limit["ModLimit"][i]["SpectFlat"]["Range2"] = nr_limit.ModLimit[i].SpectFlat.Range2;
        json_nr_limit["ModLimit"][i]["SpectFlat"]["Max1Min2"] = nr_limit.ModLimit[i].SpectFlat.Max1Min2;
        json_nr_limit["ModLimit"][i]["SpectFlat"]["Max2Min1"] = nr_limit.ModLimit[i].SpectFlat.Max2Min1;
        json_nr_limit["ModLimit"][i]["SpectFlat"]["EdgeFreq"] = nr_limit.ModLimit[i].SpectFlat.EdgeFreq;
    }

    for (int i = 0; i < arraySize(nr_limit.SpectLimit); ++i) {
        json_nr_limit["SpectLimit"][i]["OBWLimit"]["State"] = nr_limit.SpectLimit[i].OBWLimit.State;
        json_nr_limit["SpectLimit"][i]["OBWLimit"]["Limit"] = nr_limit.SpectLimit[i].OBWLimit.Limit;

        for (int j = 0; j < arraySize(nr_limit.SpectLimit[i].SEMLimit); ++j) {
            for (int k = 0; k < arraySize(nr_limit.SpectLimit[i].SEMLimit[j]); ++k) {
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["State"] = nr_limit.SpectLimit[i].SEMLimit[j][k].State;
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["StartFreq"] = nr_limit.SpectLimit[i].SEMLimit[j][k].StartFreq;
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["StopFreq"] = nr_limit.SpectLimit[i].SEMLimit[j][k].StopFreq;
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["LimitPower"] = nr_limit.SpectLimit[i].SEMLimit[j][k].LimitPower;
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["StartPower"] = nr_limit.SpectLimit[i].SEMLimit[j][k].StartPower;
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["StopPower"] = nr_limit.SpectLimit[i].SEMLimit[j][k].StopPower;
                json_nr_limit["SpectLimit"][i]["SEMLimit"][j][k]["RBW"] = nr_limit.SpectLimit[i].SEMLimit[j][k].RBW;
            }
        }

        for (int j = 0; j < arraySize(nr_limit.SpectLimit[i].UtraLimit); ++j) {
            json_nr_limit["SpectLimit"][i]["UtraLimit"][j]["RelState"] = nr_limit.SpectLimit[i].UtraLimit[j].RelState;
            json_nr_limit["SpectLimit"][i]["UtraLimit"][j]["AbsState"] = nr_limit.SpectLimit[i].UtraLimit[j].AbsState;
            json_nr_limit["SpectLimit"][i]["UtraLimit"][j]["RelLimit"] = nr_limit.SpectLimit[i].UtraLimit[j].RelLimit;
            json_nr_limit["SpectLimit"][i]["UtraLimit"][j]["AbsPwr"] = nr_limit.SpectLimit[i].UtraLimit[j].AbsPwr;
        }

        json_nr_limit["SpectLimit"][i]["NRLimit"]["RelState"] = nr_limit.SpectLimit[i].NRLimit.RelState;
        json_nr_limit["SpectLimit"][i]["NRLimit"]["AbsState"] = nr_limit.SpectLimit[i].NRLimit.AbsState;
        json_nr_limit["SpectLimit"][i]["NRLimit"]["RelLimit"] = nr_limit.SpectLimit[i].NRLimit.RelLimit;
        json_nr_limit["SpectLimit"][i]["NRLimit"]["AbsPwr"] = nr_limit.SpectLimit[i].NRLimit.AbsPwr;
    }

    for (int i = 0; i < arraySize(nr_limit.SEMAddTestTol); ++i) {
        json_nr_limit["SEMAddTestTol"].emplace_back(nr_limit.SEMAddTestTol[i]);
    }

    for (int i = 0; i < arraySize(nr_limit.ACLRAddTestTol); ++i) {
        json_nr_limit["ACLRAddTestTol"].emplace_back(nr_limit.ACLRAddTestTol[i]);
    }

    json_nr_limit["PowerLimit"]["State"] = nr_limit.PowerLimit.State;
    json_nr_limit["PowerLimit"]["OffPower"] = nr_limit.PowerLimit.OffPower;
    for (int i = 0; i < arraySize(nr_limit.PowerLimit.TestTol); ++i) {
        json_nr_limit["PowerLimit"]["TestTol"].emplace_back(nr_limit.PowerLimit.TestTol[i]);
    }
}

void nbiot_param_to_json(json &json_nbiot, const Alg_3GPP_AlzInNBIOT &nbiot)
{
    json_nbiot["LinkDirect"] = nbiot.LinkDirect;

    if (nbiot.LinkDirect == ALG_3GPP_UL) {
        nbiot_ul_param_to_json(json_nbiot["UL"], nbiot.UL);
    } else {
        nbiot_dl_param_to_json(json_nbiot["DL"], nbiot.DL);
    }

    nbiot_measure_param_to_json(json_nbiot["Measure"], nbiot.Measure);
    nbiot_limit_param_to_json(json_nbiot["LimitInfo"], nbiot.LimitInfo);
}

void nbiot_ul_param_to_json(json &json_nbiot_ul, const Alg_3GPP_AlzULInNBIOT &nbiot_ul)
{
    json_nbiot_ul["Duplexing"] = nbiot_ul.Duplexing;
    json_nbiot_ul["OperationMode"] = nbiot_ul.OperationMode;
    json_nbiot_ul["ChannelBW"] = nbiot_ul.ChannelBW;
    json_nbiot_ul["RBIdx"] = nbiot_ul.RBIdx;
    json_nbiot_ul["NBCellID"] = nbiot_ul.NBCellID;
    json_nbiot_ul["ChanType"] = nbiot_ul.ChanType;

    json_nbiot_ul["Npusch"]["Format"] = nbiot_ul.Npusch.Format;
    json_nbiot_ul["Npusch"]["SCSpacing"] = nbiot_ul.Npusch.SCSpacing;
    json_nbiot_ul["Npusch"]["Repetitions"] = nbiot_ul.Npusch.Repetitions;
    json_nbiot_ul["Npusch"]["RUs"] = nbiot_ul.Npusch.RUs;
    json_nbiot_ul["Npusch"]["SubcarrierNum"] = nbiot_ul.Npusch.SubcarrierNum;
    json_nbiot_ul["Npusch"]["StartSubcarrier"] = nbiot_ul.Npusch.StartSubcarrier;
    json_nbiot_ul["Npusch"]["CyclicShift"] = nbiot_ul.Npusch.CyclicShift;
    json_nbiot_ul["Npusch"]["GrpHopping"] = nbiot_ul.Npusch.GrpHopping;
    json_nbiot_ul["Npusch"]["DeltaSeqShift"] = nbiot_ul.Npusch.DeltaSeqShift;
    json_nbiot_ul["Npusch"]["Modulate"] = nbiot_ul.Npusch.Modulate;
    json_nbiot_ul["Npusch"]["ChanCodingState"] = nbiot_ul.Npusch.ChanCodingState;
    json_nbiot_ul["Npusch"]["Scrambling"] = nbiot_ul.Npusch.Scrambling;
    json_nbiot_ul["Npusch"]["StartSubfrm"] = nbiot_ul.Npusch.StartSubfrm;
    json_nbiot_ul["Npusch"]["TBSIdx"] = nbiot_ul.Npusch.TBSIdx;
    json_nbiot_ul["Npusch"]["StartRVIdx"] = nbiot_ul.Npusch.StartRVIdx;
    json_nbiot_ul["Npusch"]["UeID"] = nbiot_ul.Npusch.UeID;
}

void nbiot_dl_param_to_json(json &json_nbiot_dl, const Alg_3GPP_AlzDLInNBIOT &nbiot_dl)
{
    json_nbiot_dl["Duplexing"] = nbiot_dl.Duplexing;
    json_nbiot_dl["OperationMode"] = nbiot_dl.OperationMode;
    json_nbiot_dl["CarrierType"] = nbiot_dl.CarrierType;
    json_nbiot_dl["ChannelBW"] = nbiot_dl.ChannelBW;
    json_nbiot_dl["RBIdx"] = nbiot_dl.RBIdx;
    json_nbiot_dl["NBCellID"] = nbiot_dl.NBCellID;
    json_nbiot_dl["LTECellID"] = nbiot_dl.LTECellID;
    json_nbiot_dl["LTEAntennaNum"] = nbiot_dl.LTEAntennaNum;
    json_nbiot_dl["NBAntennaNum"] = nbiot_dl.NBAntennaNum;
    json_nbiot_dl["SIB1Switch"] = nbiot_dl.SIB1Switch;
    json_nbiot_dl["SchedulingInfoSIB1"] = nbiot_dl.SchedulingInfoSIB1;
    json_nbiot_dl["NPssPower"] = nbiot_dl.NPssPower;
    json_nbiot_dl["NSssPower"] = nbiot_dl.NSssPower;
    json_nbiot_dl["ChanType"] = nbiot_dl.ChanType;

    json_nbiot_dl["Npdsch"]["NSF"] = nbiot_dl.Npdsch.NSF;
    json_nbiot_dl["Npdsch"]["Repetitions"] = nbiot_dl.Npdsch.Repetitions;
    json_nbiot_dl["Npdsch"]["MCS"] = nbiot_dl.Npdsch.MCS;
    json_nbiot_dl["Npdsch"]["StartSymb"] = nbiot_dl.Npdsch.StartSymb;
    json_nbiot_dl["Npdsch"]["StartSubfrm"] = nbiot_dl.Npdsch.StartSubfrm;
    json_nbiot_dl["Npdsch"]["Precoding"] = nbiot_dl.Npdsch.Precoding;
    json_nbiot_dl["Npdsch"]["ChanCodingState"] = nbiot_dl.Npdsch.ChanCodingState;
    json_nbiot_dl["Npdsch"]["Scrambling"] = nbiot_dl.Npdsch.Scrambling;
    json_nbiot_dl["Npdsch"]["UeID"] = nbiot_dl.Npdsch.UeID;
    json_nbiot_dl["Npdsch"]["Power"] = nbiot_dl.Npdsch.Power;

}

void nbiot_measure_param_to_json(json &json_nbiot_meas, const Alg_3GPP_AlzMeasureNBIOT &nbiot_meas)
{
    json_nbiot_meas["StatisticAvgFlg"] = nbiot_meas.StatisticAvgFlg;
    json_nbiot_meas["StatisticCnt"] = nbiot_meas.StatisticCnt;
    json_nbiot_meas["MeasureUnit"] = nbiot_meas.MeasureUnit;
    json_nbiot_meas["ConstShowPilot"] = nbiot_meas.ConstShowPilot;
}

void nbiot_limit_param_to_json(json &json_nbiot_limit, const Alg_3GPP_LimitInNBIOT &nbiot_limit)
{
    json_nbiot_limit["ModLimitMode"] = nbiot_limit.ModLimitMode;

    json_nbiot_limit["EvmRms"]["State"] = nbiot_limit.EvmRms.State;
    json_nbiot_limit["EvmRms"]["Limit"] = nbiot_limit.EvmRms.Limit;

    json_nbiot_limit["EvmPeak"]["State"] = nbiot_limit.EvmPeak.State;
    json_nbiot_limit["EvmPeak"]["Limit"] = nbiot_limit.EvmPeak.Limit;

    json_nbiot_limit["MErrRms"]["State"] = nbiot_limit.MErrRms.State;
    json_nbiot_limit["MErrRms"]["Limit"] = nbiot_limit.MErrRms.Limit;

    json_nbiot_limit["MErrPeak"]["State"] = nbiot_limit.MErrPeak.State;
    json_nbiot_limit["MErrPeak"]["Limit"] = nbiot_limit.MErrPeak.Limit;

    json_nbiot_limit["PhErrRms"]["State"] = nbiot_limit.PhErrRms.State;
    json_nbiot_limit["PhErrRms"]["Limit"] = nbiot_limit.PhErrRms.Limit;

    json_nbiot_limit["PhErrPeak"]["State"] = nbiot_limit.PhErrPeak.State;
    json_nbiot_limit["PhErrPeak"]["Limit"] = nbiot_limit.PhErrPeak.Limit;

    json_nbiot_limit["FreqErrLow"]["State"] = nbiot_limit.FreqErrLow.State;
    json_nbiot_limit["FreqErrLow"]["Limit"] = nbiot_limit.FreqErrLow.Limit;

    json_nbiot_limit["FreqErrHigh"]["State"] = nbiot_limit.FreqErrHigh.State;
    json_nbiot_limit["FreqErrHigh"]["Limit"] = nbiot_limit.FreqErrHigh.Limit;

    json_nbiot_limit["IQOffset"]["State"] = nbiot_limit.IQOffset.State;
    for(int i = 0; i < arraySize(nbiot_limit.IQOffset.PwrLimit); i++) {
        json_nbiot_limit["IQOffset"]["PwrLimit"].emplace_back(nbiot_limit.IQOffset.PwrLimit[i]);
    }

    json_nbiot_limit["IBE"]["State"] = nbiot_limit.IBE.State;
    json_nbiot_limit["IBE"]["GenMin"] = nbiot_limit.IBE.GenMin;
    json_nbiot_limit["IBE"]["GenEVM"] = nbiot_limit.IBE.GenEVM;
    json_nbiot_limit["IBE"]["GenPwr"] = nbiot_limit.IBE.GenPwr;
    for(int i = 0; i < arraySize(nbiot_limit.IBE.IQImage); i++) {
        json_nbiot_limit["IBE"]["IQImage"].emplace_back(nbiot_limit.IBE.IQImage[i]);
    }
    for(int i = 0; i < arraySize(nbiot_limit.IBE.IQOffsetPwr); i++) {
        json_nbiot_limit["IBE"]["IQOffsetPwr"].emplace_back(nbiot_limit.IBE.IQOffsetPwr[i]);
    }

    json_nbiot_limit["SpectLimitMode"] = nbiot_limit.SpectLimitMode;

    json_nbiot_limit["OBWLimit"]["State"] = nbiot_limit.OBWLimit.State;
    json_nbiot_limit["OBWLimit"]["Limit"] = nbiot_limit.OBWLimit.Limit;

    for(int i = 0; i < arraySize(nbiot_limit.SEMLimit); i++) {
        json_nbiot_limit["SEMLimit"][i]["State"] = nbiot_limit.SEMLimit[i].State;
        json_nbiot_limit["SEMLimit"][i]["StartFreq"] = nbiot_limit.SEMLimit[i].StartFreq;
        json_nbiot_limit["SEMLimit"][i]["StopFreq"] = nbiot_limit.SEMLimit[i].StopFreq;
        json_nbiot_limit["SEMLimit"][i]["LimitPower"] = nbiot_limit.SEMLimit[i].LimitPower;
        json_nbiot_limit["SEMLimit"][i]["StartPower"] = nbiot_limit.SEMLimit[i].StartPower;
        json_nbiot_limit["SEMLimit"][i]["StopPower"] = nbiot_limit.SEMLimit[i].StopPower;
        json_nbiot_limit["SEMLimit"][i]["RBW"] = nbiot_limit.SEMLimit[i].RBW;
    }

    json_nbiot_limit["GSM"]["RelState"] = nbiot_limit.GSM.RelState;
    json_nbiot_limit["GSM"]["AbsState"] = nbiot_limit.GSM.AbsState;
    json_nbiot_limit["GSM"]["RelLimit"] = nbiot_limit.GSM.RelLimit;
    json_nbiot_limit["GSM"]["AbsPwr"] = nbiot_limit.GSM.AbsPwr;

    json_nbiot_limit["UTRA"]["RelState"] = nbiot_limit.UTRA.RelState;
    json_nbiot_limit["UTRA"]["AbsState"] = nbiot_limit.UTRA.AbsState;
    json_nbiot_limit["UTRA"]["RelLimit"] = nbiot_limit.UTRA.RelLimit;
    json_nbiot_limit["UTRA"]["AbsPwr"] = nbiot_limit.UTRA.AbsPwr;
}

} // namespace
