#!/usr/bin/env python3
"""
批量重构NBIOT文件的脚本
将原始的do-while结构转换为ScpiChecker格式
"""

import re
import sys

def refactor_simple_function(match):
    """重构简单的整数参数函数"""
    func_name = match.group(1)
    param_range = match.group(2)
    assignment = match.group(3)
    
    # 解析参数范围
    range_match = re.search(r'Value < (\d+) \|\| Value > (\d+)', param_range)
    if range_match:
        min_val = range_match.group(1)
        max_val = range_match.group(2)
        
        return f"""scpi_result_t {func_name}(scpi_t *context)
{{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE({min_val}, {max_val}))
        .Result();

    if (iRet == WT_OK) {{
        {assignment.replace('Value', 'value').replace('attr->Pn3GPP->NBIOT', 'Nbiot(context)')}
    }}
    
    return SCPI_ResultOK(context, iRet);
}}"""
    
    return match.group(0)  # 如果无法解析，返回原文

def refactor_command_param_function(match):
    """重构带命令参数的函数"""
    func_name = match.group(1)
    cmd_param = match.group(2)
    param_range = match.group(3)
    assignment = match.group(4)
    
    # 解析命令参数范围
    cmd_range_match = re.search(r'(\w+) < (\d+) \|\| \1 >= (\d+)', cmd_param)
    if cmd_range_match:
        param_name = cmd_range_match.group(1)
        min_val = cmd_range_match.group(2)
        max_val = str(int(cmd_range_match.group(3)) - 1)
        
        # 解析值参数范围
        val_range_match = re.search(r'Value < (\d+) \|\| Value > (\d+)', param_range)
        if val_range_match:
            val_min = val_range_match.group(1)
            val_max = val_range_match.group(2)
            
            return f"""scpi_result_t {func_name}(scpi_t *context)
{{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE({min_val}, {max_val}))
        .Param(value, INT_RANGE({val_min}, {val_max}))
        .Result();

    if (iRet == WT_OK) {{
        {assignment.replace('Value', 'value').replace(param_name, 'param').replace('attr->Pn3GPP->NBIOT', 'Nbiot(context)')}
    }}
    
    return SCPI_ResultOK(context, iRet);
}}"""
    
    return match.group(0)

def refactor_list_param_function(match):
    """重构列表参数函数"""
    func_name = match.group(1)
    switch_cases = match.group(2)
    assignment = match.group(3)
    
    # 提取case值
    case_values = re.findall(r'case (\d+):', switch_cases)
    if case_values:
        values_str = ', '.join(case_values)
        
        return f"""scpi_result_t {func_name}(scpi_t *context)
{{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {{{values_str}}})
        .Result();

    if (iRet == WT_OK) {{
        {assignment.replace('Value', 'value').replace('attr->Pn3GPP->NBIOT', 'Nbiot(context)')}
    }}
    
    return SCPI_ResultOK(context, iRet);
}}"""
    
    return match.group(0)

def main():
    if len(sys.argv) != 2:
        print("Usage: python refactor_nbiot_batch.py <input_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 简单整数参数函数的正则表达式
    simple_pattern = r'(scpi_result_t SCPI_\w+\(scpi_t \*context\))\s*\{\s*int iRet = WT_ERR_CODE_OK;\s*do\s*\{\s*int Value = 0;\s*if \(!SCPI_ParamInt\(context, &Value, true\)\)\s*\{\s*iRet = WT_ERR_CODE_PARAMETER_MISMATCH;\s*break;\s*\}\s*(if \(Value[^}]+\})\s*SPCIUserParam \*attr[^;]+;\s*(attr->Pn3GPP->NBIOT[^;]+;)\s*\} while \(0\);\s*return SCPI_ResultOK\(context, iRet\);\s*\}'
    
    # 带命令参数函数的正则表达式
    cmd_param_pattern = r'(scpi_result_t SCPI_\w+\(scpi_t \*context\))\s*\{\s*int iRet = WT_ERR_CODE_OK;\s*do\s*\{\s*int Value = 0;\s*int (\w+) = 0;\s*SCPI_CommandNumbers\(context, &\2, 1\);\s*(if \(\2[^}]+\})\s*if \(!SCPI_ParamInt\(context, &Value, true\)\)[^}]+\}\s*(if \(Value[^}]+\})\s*SPCIUserParam \*attr[^;]+;\s*(attr->Pn3GPP->NBIOT[^;]+;)\s*\} while \(0\);\s*return SCPI_ResultOK\(context, iRet\);\s*\}'
    
    # 应用重构
    content = re.sub(simple_pattern, refactor_simple_function, content, flags=re.DOTALL)
    content = re.sub(cmd_param_pattern, refactor_command_param_function, content, flags=re.DOTALL)
    
    # 输出重构后的内容
    output_file = input_file.replace('.cpp', '_refactored.cpp')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"重构完成，输出文件: {output_file}")

if __name__ == "__main__":
    main()
