#ifndef _WT_VSG_DEFINE_H_
#define _WT_VSG_DEFINE_H_

//VSG REG DEFINE
#define VSG_XDMA_START      (0x00C0 << 2)       //XDMA启动信号
#define VSG_START_REG       (0x00C1 << 2)       //VSA启动信号
#define VSG_IQ_SWITCH       (0x00C2 << 2)       //DAC IQ数据交换
#define VSG_XDMA_LEN        (0x00C5 << 2)       //XDMA传输长度
#define VSG_STATUS          (0x00CA << 2)       //VSG状态
#define VSA_SEQ_STATE       (0x0150 << 2)       //VSA SEQ状态
#define VSG_WLAN_MODE       (0x00C4 << 2)       //VSG工作模式
#define VSG_SIN1M_REG       (0x00C9 << 2)       //VSG工作模式

#define PN_TX_INDEX         (0x0128 << 2)       //多PN索引
#define PN_TX_TOTAL         (0x0129 << 2)       //发送PN次数

//#define GAP_POWER_CTL_ENABLE    (0X0007 << 2)
#define GAP_POWER_CTL_DELAY1    (0X0007 << 2)
#define GAP_POWER_CTL_DELAY2    (0X0008 << 2)
#define GAP_POWER_CTL_DELAY3    (0X0009 << 2)

//特殊模式选择
#define VSG_EXT_MODE        (0X00F0 << 2)

#define DEVM_MODE_T1        (0X00F3 << 2)
#define DEVM_MODE_T2        (0X00F4 << 2)
#define DEVM_MODE_GAP       (0X00F5 << 2)
#define DEVM_MODE_DELAY     (0X00F9 << 2)

#define TBT_MODE_RESET         (0X002A << 2)
#define TBT_MODE_VSG_DELAY     (0X002B << 2)
#define TBT_MODE_VSG_LENGHT    (0X002C << 2)
#define TBT_MODE_RESULT_SIFS   (0X002D << 2)

// RX 射频板移位寄存器
#define TX_SHIFT_0_TX (0x0070 << 2)
#define TX_SHIFT_0_RX (0x0071 << 2)
#define TX_SHIFT_0_CR1 (0x0072 << 2)
#define TX_SHIFT_0_CR2 (0x0073 << 2)
#define TX_SHIFT_0_STATUS (0x0072 << 2)
#define TX_SHIFT_1_TX (0x0075 << 2)
#define TX_SHIFT_1_RX (0x0076 << 2)
#define TX_SHIFT_1_CR1 (0x0077 << 2)
#define TX_SHIFT_1_CR2 (0x0078 << 2)
#define TX_SHIFT_1_STATUS (0x0077 << 2)
#define TX_SHIFT_2_TX (0x007A << 2)
#define TX_SHIFT_2_RX (0x007B << 2)
#define TX_SHIFT_2_CR1 (0x007C << 2)
#define TX_SHIFT_2_CR2 (0x007D << 2)
#define TX_SHIFT_2_STATUS (0x007C << 2)

/////////////////////////////////////////////////////////////////////////
// VA版 射频板移位寄存器管教定义
/////////////////////////////////////////////////////////////////////////
enum WT_TX_SHIFT_VA_BIT_E
{
    // shift0
    VA_TX_ATT1T_CTL0 = 24,
    VA_TX_ATT1T_CTL1 = 25,
    VA_TX_ATT1T_CTL2 = 26,
    VA_TX_ATT1T_CTL3 = 27,
    VA_TX_ATT1T_CTL4 = 28,
    VA_TX_ATT1T_CTL5 = 29,
    VA_TX_ATT2T_CTL0 = 8,
    VA_TX_ATT2T_CTL1 = 9,
    VA_TX_ATT2T_CTL2 = 10,
    VA_TX_ATT2T_CTL3 = 11,
    VA_TX_ATT2T_CTL4 = 12,
    VA_TX_ATT2T_CTL5 = 13,
    VA_TX_ATT3T_CTL0 = 0,
    VA_TX_ATT3T_CTL1 = 1,
    VA_TX_ATT3T_CTL2 = 2,
    VA_TX_ATT3T_CTL3 = 3,
    VA_TX_ATT3T_CTL4 = 4,
    VA_TX_ATT3T_CTL5 = 5,
    VA_TX_ATT4T_CTL0 = 16,
    VA_TX_ATT4T_CTL1 = 17,
    VA_TX_ATT4T_CTL2 = 18,
    VA_TX_ATT4T_CTL3 = 19,
    VA_TX_ATT4T_CTL4 = 20,
    VA_TX_ATT4T_CTL5 = 21,

    // shift1
    VA_TX_SW1LOT_CTL1 = 19,
    VA_TX_SW1LOT_CTL2 = 20,
    VA_TX_SW1LOT_CTL3 = 21,
    VA_TX_SW2LOT_CTL1 = 16,
    VA_TX_SW2LOT_CTL2 = 17,
    VA_TX_SW2LOT_CTL3 = 18,
    VA_TX_SW3LOT_CTL = 22,
    VA_TX_SW4LOT_CTL = 23,
    VA_TX_ATT5T_CTL0 = 0,
    VA_TX_ATT5T_CTL1 = 1,
    VA_TX_ATT5T_CTL2 = 2,
    VA_TX_ATT5T_CTL3 = 3,
    VA_TX_ATT5T_CTL4 = 4,
    VA_TX_ATT5T_CTL5 = 5,
    VA_TX_ATT6T_CTL0 = 8,
    VA_TX_ATT6T_CTL1 = 9,
    VA_TX_ATT6T_CTL2 = 10,
    VA_TX_ATT6T_CTL3 = 11,
    VA_TX_ATT6T_CTL4 = 12,
    VA_TX_ATT6T_CTL5 = 13,

    // shift2
    VA_TX_IQSW_TXI_CTL1 = 24,
    VA_TX_IQSW_TXI_CTL2 = 25,
    VA_TX_IQSW_TXQ_CTL1 = 26,
    VA_TX_IQSW_TXQ_CTL2 = 27,
    VA_TX_SW1FT_CTL1 = 8,
    VA_TX_SW1FT_CTL2 = 9,
    VA_TX_SW1IT_CTL1 = 3,
    VA_TX_SW1IT_CTL2 = 4,
    VA_TX_SW1IT_CTL3 = 5,
    VA_TX_SW1T_CTL = 6,
    VA_TX_SW2FT_CTL1 = 10,
    VA_TX_SW2FT_CTL2 = 11,
    VA_TX_SW2IT_CTL1 = 0,
    VA_TX_SW2IT_CTL2 = 1,
    VA_TX_SW2IT_CTL3 = 2,
    VA_TX_SW2T_CTL = 7,
    VA_TX_SW3FT_CTL = 12,
    VA_TX_SW3T_CTL = 20,
    VA_TX_SW4FT_CTL = 13,
    VA_TX_SW4T_CTL = 21,
    VA_TX_SW5FT_CTL = 14,
    VA_TX_SW6FT_CTL = 15,
    VA_TX_SW7FT_CTL = 16,
    VA_TX_SW8FT_CTL = 17,
    VA_TX_SW9FT_CTL = 18,
    VA_TX_SW10FT_CTL = 19,
    VA_TX_TX_PA_ON = 22,
    VA_TX_MOD_EN = 23,
};

/////////////////////////////////////////////////////////////////////////
// VB版 射频板移位寄存器管教定义
/////////////////////////////////////////////////////////////////////////
// 参考: WT448_RF_VB_interface_list_202010826.xlsx
enum WT_TX_SHIFT_VB_BIT_E
{
    // shift0
    VB_TX_SW1FT_CTL3 = 1,
    VB_TX_SW1FT_CTL2,
    VB_TX_SW1FT_CTL1,
    VB_TX_SW2FT_CTL3 = 5,
    VB_TX_SW2FT_CTL2,
    VB_TX_SW2FT_CTL1,
    VB_TX_TX_PA_ON,
    VB_TX_SW2T_CTL,
    VB_TX_ATT2T_CTL0,
    VB_TX_ATT2T_CTL1,
    VB_TX_ATT2T_CTL2,
    VB_TX_ATT2T_CTL3,
    VB_TX_ATT2T_CTL4,
    VB_TX_ATT2T_CTL5,
    VB_TX_SW4T_CTL,
    VB_TX_ATT3T_CTL0,
    VB_TX_ATT3T_CTL1,
    VB_TX_ATT3T_CTL2,
    VB_TX_ATT3T_CTL3,
    VB_TX_ATT3T_CTL4,
    VB_TX_ATT3T_CTL5,
    VB_TX_SW3T_CTL,

    // shift2
    VB_TX_ATT4T_CTL0 = 1,
    VB_TX_ATT4T_CTL1,
    VB_TX_ATT4T_CTL2,
    VB_TX_ATT4T_CTL3,
    VB_TX_ATT4T_CTL4,
    VB_TX_ATT4T_CTL5,
    VB_TX_SW1LOT_CTL3 = 9,
    VB_TX_SW1LOT_CTL2,
    VB_TX_SW1LOT_CTL1,
    VB_TX_SW2LOT_CTL3 = 13,
    VB_TX_SW2LOT_CTL2,
    VB_TX_SW2LOT_CTL1,
    VB_TX_SW4LOT_CTL,
    VB_TX_ATT5T_CTL5,
    VB_TX_ATT5T_CTL4,
    VB_TX_ATT5T_CTL3,
    VB_TX_ATT5T_CTL2,
    VB_TX_ATT5T_CTL1,
    VB_TX_ATT5T_CTL0,
    VB_TX_SW3LOT_CTL,

    // shift 1
    VB_TX_MOD_EN = 0,
    VB_TX_ATT1T_CTL0,
    VB_TX_ATT1T_CTL1,
    VB_TX_ATT1T_CTL2,
    VB_TX_ATT1T_CTL3,
    VB_TX_ATT1T_CTL4,
    VB_TX_ATT1T_CTL5,
    VB_TX_SW1T_CTL,
    VB_TX_SW2IT_CTL1 = 9,
    VB_TX_SW2IT_CTL2,
    VB_TX_SW2IT_CTL3,
    VB_TX_SW1IT_CTL1 = 13,
    VB_TX_SW1IT_CTL2,
    VB_TX_SW1IT_CTL3,
};

/////////////////////////////////////////////////////////////////////////
// 418VA版 射频板移位寄存器管教定义
/////////////////////////////////////////////////////////////////////////
// 参考: WT418射频开关板接口定义说明文档_add_bit_2022.11.11.xlsx
enum WT_TX_SHIFT_418_VA_BIT_E
{
    // shift0
    WT418_VA_TX_MOD_EN = 0,
    WT418_VA_TX_ATT1T_CTL0,
    WT418_VA_TX_ATT1T_CTL1,
    WT418_VA_TX_ATT1T_CTL2,
    WT418_VA_TX_ATT1T_CTL3,
    WT418_VA_TX_ATT1T_CTL4,
    WT418_VA_TX_ATT1T_CTL5,

    WT418_VA_TX_ATT2T_CTL0 = 10,
    WT418_VA_TX_ATT2T_CTL1,
    WT418_VA_TX_ATT2T_CTL2,
    WT418_VA_TX_ATT2T_CTL3,
    WT418_VA_TX_ATT2T_CTL4,
    WT418_VA_TX_ATT2T_CTL5,

    WT418_VA_TX_ATT3T_CTL0 = 17,
    WT418_VA_TX_ATT3T_CTL1,
    WT418_VA_TX_ATT3T_CTL2,
    WT418_VA_TX_ATT3T_CTL3,
    WT418_VA_TX_ATT3T_CTL4,
    WT418_VA_TX_ATT3T_CTL5,

    // shift1
    WT418_VA_TX_SW7LOT_CTL = 0,
    WT418_VA_TX_SW6LOT_CTL,
    WT418_VA_TX_SW2IT_CTL1,
    WT418_VA_TX_SW2IT_CTL2,
    WT418_VA_TX_SW2IT_CTL3,
    WT418_VA_TX_SW1IT_CTL1,
    WT418_VA_TX_SW1IT_CTL2,
    WT418_VA_TX_SW1IT_CTL3,

    WT418_VA_TX_SW1T_CTL = 8,
    WT418_VA_TX_SW1FT_CTL1,
    WT418_VA_TX_SW1FT_CTL2,
    WT418_VA_TX_SW2FT_CTL1,
    WT418_VA_TX_SW2FT_CTL2,
    WT418_VA_TX_SW2T_CTL,
    WT418_VA_TX_SW3T_CTL,
    WT418_VA_TX_SW4T_CTL,

    WT418_VA_TX_SW4LOT_CTL,
    WT418_VA_TX_SW5LOT_CTL,
    WT418_VA_TX_SW2LOT_CTL1,
    WT418_VA_TX_SW2LOT_CTL2,
    WT418_VA_TX_SW2LOT_CTL3,
    WT418_VA_TX_SW1LOT_CTL1,
    WT418_VA_TX_SW1LOT_CTL2,
    WT418_VA_TX_SW1LOT_CTL3,
};
#endif