/**
 * @file cellular_result_common_interface.h
 * @brief 蜂窝获取结果的公共头文件, 包含了公用的一些函数
 * @version 0.1
 * @date 2024-10-09
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#ifndef CELLULAR_RESULT_COMMON_INTERFACE_H_
#define CELLULAR_RESULT_COMMON_INTERFACE_H_

#include "basehead.h"
#include "commonhandler.h"
#include "scpi/scpi.h"

scpi_result_t GetRstIntData(scpi_t *context, const char *ParamStr);
scpi_result_t GetRstIntData(scpi_t *context, std::vector<const char *> &ParamStr);
scpi_result_t GetRstDoubleData(scpi_t *context, const char *ParamStr);

scpi_result_t GetRstIntVectorData(scpi_t *context, const char *ParamStr, bool arb);
scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb);

#endif  // CELLULAR_RESULT_COMMON_INTERFACE_H_
