//*****************************************************************************
//  File: compensate.cpp
//  算法功能封装，用于VSG数据补偿
//  Data: 2017.4.13
//*****************************************************************************
#include "compensate.h"
#include <fstream>
#include "wtlog.h"
#include "wterror.h"
#include "calconf.h"
#include "basefun.h"
#include "digitallib.h"
#include "wtlog.h"

using namespace std;

static const double DAC_VOL = 0.5;        // DAC电压

extern void SetSigDataToVsa(const SigDataHeader *<PERSON><PERSON><PERSON>ead<PERSON>, RX_InDat *pInData, stSpectrumOffset *SpectOffset, const ExtendEVMStu &ExtendEvm);

Compensate::Compensate(void)
{
    m_RxOut = nullptr;
    m_BufLen = 0;

    InitAlgInput();
    ResetVsgRuCarrierInfo(); //初始化下数据

    m_AlzIQImbEnable = CalConf::Instance().GetVSGIqImbComp();
    m_AlzFlatnessEnable = CalConf::Instance().GetVSGFlatnessComp();
    m_AnalogIQCalEnable = CalConf::Instance().GetAnalogIQCal();
    GET_CONF_DATA(DevConf::Instance(), "QDataShift", m_QDataShift, 0);
    DevConf::Instance().GetItemVal("PnConuterDebug", m_PnConuterDebug);
}

Compensate::~Compensate(void)
{
    WT_Algorithm_term(m_RxOut);
}

void Compensate::InitAlgInput(void)
{
    memset(&m_RxIn, 0, sizeof(m_RxIn));

    m_RxIn.demod_mode = WT_DEMOD_UNKNOW;
    m_RxIn.iqswap = WT_IQ_SWAP_DISABLED;
    m_RxIn.iqreversion = WT_IQ_IQReversion_DISABLED;

    m_RxIn.channelest_enable = WT_CH_EST_RAW_LONG;
    m_RxIn.amp_track_enable = WT_AMPL_TRACK_OFF;
    m_RxIn.phase_track_enable = WT_PH_CORR_SYM_BY_SYM;
    m_RxIn.sym_tim_corr = WT_SYM_TIM_ON;
    m_RxIn.freq_sync = WT_FREQ_SYNC_AUTO;

    m_RxIn.cck_dcremove_enable = WT_DC_REMOVAL_OFF;
    m_RxIn.cck_phase_track_enable = WT_PH_CORR_11b_ON;
    m_RxIn.cck_equalizertaps = WT_EQ_OFF;
    m_RxIn.cck_evm_method = WT_11B_STANDARD_TX_ACC;

    m_RxIn.frequencyOffsetHz = 0;
    m_RxIn.FrameAutoDetection = WT_USER_DEFINED;
    m_RxIn.BT_DataRate = 0;
    m_RxIn.BT_PacketType = 0;

    m_RxIn.scene_model = Normal;
    m_RxIn.capturedata_format = enDataFormat_Float64;
    m_RxIn.flatnessPowerFactor = 1;
}

void *Compensate::AllocBuf(int PointNum)
{
    if (m_RxOut == nullptr)
    {
        int Ret = WT_Algorithm_init(&m_RxOut);
        if (Ret != WT_OK)
        {
            m_RxOut = nullptr;
            WTLog::Instance().LOGERR(Ret, "WT_Algorithm_init failed");
            return nullptr;
        }
    }

    m_RxIn.scene_model = Normal;
    m_RxIn.capturecnt = PointNum;
    m_RxIn.capturedata_format = enDataFormat_Float64;
    int Len = WT_Algorithm_ReqMemSizePerChannel(&m_RxIn);
    if (m_BufLen < Len)
    {
        m_AlgBuf.reset(MemPool::Instance().Alloc(Len));
        if (m_AlgBuf == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc for vsg compensate failed");
            m_BufLen = 0;
            return nullptr;
        }
        if(Len < PointNum * sizeof(Complex))
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc for vsg compensate failed");
            m_BufLen = 0;
            return nullptr;
        }

        m_BufLen = Len;
    }

    //开头部分Buffer用于存放中间数据
    m_TmpBuf = m_AlgBuf.get();
    m_RxOut->local->ReserveMem = static_cast<s8*>(m_TmpBuf) + PointNum * sizeof(Complex);
    m_RxOut->local->ReserveMemSize = m_BufLen - PointNum * sizeof(Complex);
    m_RxOut->local->ReserveMemUseLen = 0;

    return m_TmpBuf;
}

int Compensate::AmendWaveData(SigDataHeader *SigHeader, double DacMargin, const Tx_Parm *CalParam, void *DacData)
{
    void *Data = SigHeader->Data;
    void *Buf = AllocBuf(SigHeader->SampleCount);
    if (Buf == nullptr)
    {
        return WT_ALLOC_FAILED;
    }

    //原始文件可能带有补偿数据，需要先补偿一次
//    if (SigHeader->NeedCompensate())
//    {
//        stSpectrumOffset SpectOffset;
//        SetSigDataToVsa(SigHeader, &m_RxIn, &SpectOffset);
//        m_RxIn.demod_mode = SigHeader->ModType;
//
//        int Ret = WT_Algorithm_CorrectionHardwareParameter(&m_RxIn, Buf, m_RxIn.capturecnt * sizeof(Complex));
//        if (Ret != WT_OK)
//        {
//            WTLog::Instance().LOGERR(WT_VSA_COMPENSATE_FAILED, "compensate data failed");
//            return WT_VSA_COMPENSATE_FAILED;
//        }
//
//        Data = Buf;
//    }

    InitAlgInput();
    m_RxIn.capturecnt = SigHeader->SampleCount;
    m_RxIn.capturedata = Data;
    m_RxIn.demod_mode = SigHeader->ModType;
    m_RxIn.adc_freq = (int)SigHeader->SamplingRate;
    m_RxIn.clockrate = SigHeader->ClockRate;
    m_RxIn.capturedata_format = SigHeader->DataType;
    //clockrate 做保护,如果不在合理范围内，强制给默认1
    if(m_RxIn.clockrate != WT_CLOCK_RATE_1 && m_RxIn.clockrate != WT_CLOCK_RATE_1_2
            && m_RxIn.clockrate != WT_CLOCK_RATE_1_4 && m_RxIn.clockrate != WT_CLOCK_RATE_1_5
            && m_RxIn.clockrate != WT_CLOCK_RATE_1_8 && m_RxIn.clockrate != WT_CLOCK_RATE_1_10
            && m_RxIn.clockrate != WT_CLOCK_RATE_1_20)
    {
        m_RxIn.clockrate = WT_CLOCK_RATE_1;
    }
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SigHeader->SampleCount = " << SigHeader->SampleCount << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_RxIn.capturecnt = " << m_RxIn.capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "m_RxIn.adc_freq = " << m_RxIn.adc_freq << std::endl;

    return AmendData(DacMargin, CalParam, DacData);
}

int Compensate::AmendWaveData(Complex *Data, int DataNum, int AdcFreq, double DacMargin, const Tx_Parm *CalParam, void *DacData)
{
    if (AllocBuf(DataNum) == nullptr)
    {
        return WT_ALLOC_FAILED;
    }

    InitAlgInput();
    m_RxIn.capturedata = Data;
    m_RxIn.capturecnt = DataNum;
    m_RxIn.adc_freq = AdcFreq;

    return AmendData(DacMargin, CalParam, DacData);
}

#define PN_COMP_DEBUG 0
int Compensate::AmendData(double DacMargin, const Tx_Parm *CalParam, void *DacData)
{
#if PN_COMP_DEBUG
    typedef short   ComplexShort[2];
    auto SaveAmendData = [&](void *DataBuf, string name, int format) -> void
    {
        char Buf[128];
        std::ofstream PnData(WTConf::GetDir() + name, fstream::out | fstream::trunc);
        if (format == enDataFormat_Int16)
        {
            ComplexShort *Data = (ComplexShort *)DataBuf;
            for (int j = 0; j < m_RxIn.capturecnt; j++)
            {
                sprintf(Buf, "%d,%d\n", Data[j][0], Data[j][1]);
                PnData << Buf;
            }
        }
        else
        {
            Complex *Data = (Complex *)DataBuf;
            for (int j = 0; j < m_RxIn.capturecnt; j++)
            {
                sprintf(Buf, "%f,%f\n", Data[j][0], Data[j][1]);
                PnData << Buf;
            }
        }
        PnData.close();
    };
    SaveAmendData(m_RxIn.capturedata, "/vsgdata_src.csv", m_RxIn.capturedata_format);
#endif

    if (!m_PnConuterDebug)
    {
    int Ret = WT_OK;
    Ret = CompensateCalParam(CalParam);
    if (Ret != WT_OK)
    {
        return Ret;
    }

#if PN_COMP_DEBUG
    SaveAmendData(m_TmpBuf, "/vsgdata_comp.csv", enDataFormat_Float64);
#endif

    if (!(DigModeLib::Instance().IsDigMode() && CalParam->power > PN_FIX_RATIO_POWER))
    {
        Ret = PowerNormalization();
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

#if PN_COMP_DEBUG
    SaveAmendData(m_TmpBuf, "/vsgdata_norm.csv", enDataFormat_Float64);
#endif

    // 448 RF反转IQ, DIG不反转
    // TODO:？？？？？？？？？？？？？？？？？
    if (CalParam->ex_iq_mode == 1)
    {
            ConvertDataToDacCodeBaseBand(false, CalParam->power, CalParam->unit, DacData);
    }
    else
    {
        ConvertDataToDacCode(DigModeLib::Instance().IsDigMode() ? 0 : 1, CalParam->power, DacMargin, DacData);
    }

#if PN_COMP_DEBUG
    SaveAmendData(DacData, "/vsgcode_dac.csv", enDataFormat_Int16);
#endif
    }
    else
    {
    typedef struct
    {
        s16 real;
        s16 imag;
    } ADC_Data;
    ADC_Data *CaptureData = (ADC_Data *)DacData;

    for (int i = 0; i < m_RxIn.capturecnt; i++)
    {
        CaptureData[i].real = i;
        CaptureData[i].imag = i;
    }
    }

    return WT_OK;
}

void Compensate::CompensateRuCarrierInfo(const RUCarrierInfo *CarrierInfo)
{
    memcpy(&m_RxIn.RUInfo, CarrierInfo, sizeof(RUCarrierInfo));
}

int Compensate::CompensateCalParam(const Tx_Parm *CalParam)
{
    if (m_AlzIQImbEnable)
    {
        if(m_StaticIQParam[0].Valid)//目前模拟IQ在使用
        {
            m_RxIn.TimeSkew = m_StaticIQParam[0].TimeSkew;
            m_RxIn.IQImb_Amp = m_StaticIQParam[0].IQAmpl;
            m_RxIn.IQImb_Phase = m_StaticIQParam[0].IQPhase;
        }
        else
        {
            m_RxIn.TimeSkew = CalParam->tx_iq_imb_parm.timeskew;
            m_RxIn.IQImb_Amp = CalParam->tx_iq_imb_parm.gain_imb;
            m_RxIn.IQImb_Phase = -CalParam->tx_iq_imb_parm.quad_err; //需要反转的原因待查明 TODO
        }
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "VSG TimeSkew=%lf, IQImb_Amp=%lf, IQImb_Phase=%lf\n", m_RxIn.TimeSkew, m_RxIn.IQImb_Amp, m_RxIn.IQImb_Phase);
    if (m_AlzFlatnessEnable)
    {
        //只有wifi需要平坦度补偿；bt、zigbee等不需要平坦度补偿；有新加协议时，注意是否增加处理处理~
        if (m_RxIn.demod_mode <= WT_DEMOD_11AC_80_80M ||
            (m_RxIn.demod_mode >= WT_DEMOD_11AX_20M && m_RxIn.demod_mode <= WT_DEMOD_11AX_160_160M) ||
            (m_RxIn.demod_mode >= WT_DEMOD_11BE_20M && m_RxIn.demod_mode <= WT_DEMOD_11BE_160_160M) ||
            (m_RxIn.demod_mode >= WT_DEMOD_11AH_1M && m_RxIn.demod_mode <= WT_DEMOD_11AH_16M) ||
            (m_RxIn.demod_mode >= WT_DEMOD_11BA_20M && m_RxIn.demod_mode <= WT_DEMOD_11BA_80M) ||
            (m_RxIn.demod_mode >= WT_DEMOD_11AZ_20M && m_RxIn.demod_mode <= WT_DEMOD_11AZ_160M) 
            )
        {
            m_RxIn.rf_response = const_cast<double*>(CalParam->tx_spec_flat_comp_parm.rf_comp_gain);
            m_RxIn.rf_response_len = CalParam->tx_spec_flat_comp_parm.rf_comp_len;
            m_RxIn.bb_response = const_cast<double*>(CalParam->tx_spec_flat_comp_parm.bb_comp_gain);
            m_RxIn.bb_response_len = CalParam->tx_spec_flat_comp_parm.bb_comp_len;
        }
    }

    if(m_RxIn.rf_response_len == 0 && m_RxIn.bb_response_len != 0)//算法在m_RxIn.rf_response_len == 0的时候就不会补m_RxIn.bb_response，临时处理
    {
        m_RxIn.rf_response_len = m_RxIn.bb_response_len;
        memset(m_RxIn.rf_response, 0, m_RxIn.rf_response_len);
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "VSG rf_response_len=%d, bb_response_len=%d\n", m_RxIn.rf_response_len, m_RxIn.bb_response_len);

//#if TIME_DEBUG
//    struct timeval tpstart, tpend;
//    gettimeofday(&tpstart, NULL);
//#endif
    int Ret = WT_OK;
    if (DigModeLib::Instance().IsDigMode() == false &&
        (m_AlzFlatnessEnable == 1 || m_AlzIQImbEnable == 1))
    {
        CompensateRuCarrierInfo(&m_CarrierInfo[m_CurStream]);
        Ret = WT_Algorithm_CorrectionHardwareParameter(&m_RxIn, m_TmpBuf, m_RxIn.capturecnt * sizeof(Complex));
    }
    else
    {
        Complex *CaptureData = NULL;
        CaptureData = (Complex *)m_TmpBuf;
        if(m_RxIn.capturedata_format == enDataFormat_Int16)
        {
            typedef struct
            {
                s16 real;
                s16 imag;
            } ADC_Data;
            ADC_Data *Dat;
            Dat = (ADC_Data *)m_RxIn.capturedata;
            for(int i = 0; i < m_RxIn.capturecnt; i++)
            {
                CaptureData[i][0] = Dat[i].real;
                CaptureData[i][1] = Dat[i].imag;
            }
        }
        else if(m_RxIn.capturedata_format == enDataFormat_Float64)
        {
            memcpy((char *)CaptureData, (char *)m_RxIn.capturedata, m_RxIn.capturecnt * sizeof(Complex));
        }
    }
//#if TIME_DEBUG
//    gettimeofday(&tpend, NULL);
//    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##CompensateCalParam-WT_Algorithm_CorrectionHardwareParameter Used Time:" << timeuse << "us" << std::endl;
//#endif

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_VSA_COMPENSATE_FAILED, "compensate data failed");
        return WT_VSA_COMPENSATE_FAILED;
    }
    return WT_OK;
}

int Compensate::PowerNormalization(void)
{
    double Scale;
    Complex *Data = static_cast<Complex*>(m_TmpBuf);

    //WTLog::Instance().WriteLog(LOG_DEBUG, "PowerNormalization clockrate = %d\n",m_RxIn.clockrate);
    int Ret = WT_Algorithm_NormalFrmPwr0dBm(m_RxOut, Data, m_RxIn.capturecnt, m_RxIn.adc_freq / 1000000,
                                            &Scale, &m_FrmBegin, &m_FrmEnd, m_RxIn.clockrate);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WT_Algorithm_NormalFrmPwr0dBm failed");
        return Ret;
    }

    for (int i = 0; i < m_RxIn.capturecnt; i++)
    {
        Data[i][0] *= Scale * m_RxIn.flatnessPowerFactor;
        Data[i][1] *= Scale * m_RxIn.flatnessPowerFactor;
    }

    return WT_OK;
}

// DacMargin 预留值
// 参考文档: W160功率计算说明.doc, 但请注意文档与WT328还是有差异的
void Compensate::ConvertDataToDacCode(bool Inversion, double Power, double DacMargin, void *DacData)
{
    int *Value = static_cast<int*>(DacData);
    Complex *Data = static_cast<Complex*>(m_TmpBuf);
    double Scale = 0;
    int QDataShift = 0;

    int DataI  = 0, DataQ = 1;
    if (Inversion)
    {
        DataQ = 0;
        DataI = 1;
    }

    if (DigModeLib::Instance().IsDigMode())
    {
        int MaxCode = pow(2, m_MaxBitCnt - 1) - 1;
        int MinCode = -pow(2, m_MaxBitCnt - 1);
        if (Power > PN_FIX_RATIO_POWER)
        {
            Scale = 1.0;
        }
        else
        {
            double StandPower = 20 * log10(MaxCode);
            Scale = pow(10, (StandPower + Power) / 20.0);
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(m_MaxBitCnt) << Pout(MaxCode) << Pout(MinCode) << Pout(Power) << Pout(Scale) << endl;

        for (int i = 0; i < m_RxIn.capturecnt; i++)
        {
            Value[i] = DoubleToDigCode(Data[i][DataQ] * Scale, MaxCode, MinCode) << 16 |
                       DoubleToDigCode(Data[i][DataI] * Scale, MaxCode, MinCode);
        }
    }
    else
    {
        // 注意WT328 DAC阻抗值为100欧, 所以计算公式是 50*2
        double DacOutputPower = 10 * log10(1000 * DAC_VOL * DAC_VOL / (50 * 2));
        double StandPower = 20 * log10(1000) - 10 * log10(1000 * 50) + 20 * log10(DAC_VOL / (MAX_DAC_CODE + 1));
        double DacExpectPower = DacOutputPower - DacMargin;
        double Gain = DacExpectPower - StandPower;
        Scale = pow(10, Gain / 20.0);
        QDataShift = m_QDataShift;

        if (QDataShift >= 0)
        {
            for (int i = 0; i < m_RxIn.capturecnt; i++)
            {
                if (i < QDataShift)
                {
                    Value[i] = DoubleToDacCode(Data[i][DataQ] * Scale) << 16 | DoubleToDacCode(0 * Scale);
                }
                else
                {
                    Value[i] = DoubleToDacCode(Data[i][DataQ] * Scale) << 16 | DoubleToDacCode(Data[i - QDataShift][DataI] * Scale);
                }
            }
        }
        else
        {
            QDataShift = -QDataShift;
            for (int i = 0; i < m_RxIn.capturecnt; i++)
            {
                if (i < QDataShift)
                {
                    Value[i] = DoubleToDacCode(0 * Scale) << 16 | DoubleToDacCode(Data[i][DataI] * Scale);
                }
                else
                {
                    Value[i] = DoubleToDacCode(Data[i - QDataShift][DataQ] * Scale) << 16 | DoubleToDacCode(Data[i][DataI] * Scale);
                }
            }
        }
    }
}

void Compensate::ConvertDataToDacCodeBaseBand(bool Inversion, double Power, int ModId, void *DacData)
{
    int *Value = static_cast<int *>(DacData);
    Complex *Data = static_cast<Complex *>(m_TmpBuf);

    if(m_AnalogIQCalEnable)
    {
        for (int i = 0; i < m_RxIn.capturecnt; i++)
        {
            Value[i] = DoubleToDacCode(MIN_DAC_CODE) << 16 | DoubleToDacCode(MAX_DAC_CODE);
        }
        return;
    }

    double IQMaxPower = 0;
    int Ret = WT_OK;
    if ((Ret = wt_calibration_get_tx_ex_iq_max_power(ModId, &IQMaxPower)) != WT_OK)
    {
        Ret += WT_CAL_BASE_ERROR;
        RetWarnning(Ret, "wt_calibration_get_tx_ex_iq_max_power failed!\n");
    }

    double DGain = IQMaxPower - Power;
    DGain = DGain > 0 ? DGain : 0;
    double Gain = 20 * log10(MAX_DAC_CODE + 1) - DGain;
    double Scale = pow(10, Gain / 20.0);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(Scale) << Pout(IQMaxPower) << Pout(Power) << Pout(Inversion) << endl;
    if (Inversion)
    {
        for (int i = 0; i < m_RxIn.capturecnt; i++)
        {
            Value[i] = DoubleToDacCode(Data[i][1] * Scale) << 16 | DoubleToDacCode(Data[i][0] * Scale);
        }
    }
    else
    {
        for (int i = 0; i < m_RxIn.capturecnt; i++)
        {
            Value[i] = DoubleToDacCode(Data[i][0] * Scale) << 16 | DoubleToDacCode(Data[i][1] * Scale);
        }
    }
}

int Compensate::NormalizationFile(void *FileData, int FileLen)
{
    SigFileInfo *FileInfo = nullptr;
    SigFile File(FileData, FileLen);

    if (File.GetContent(&FileInfo) != WT_OK)
    {
        return WT_SIG_FILE_ERROR;
    }

    //非标准格式的不做归一化，采集到的原始数据不做归一化
    if (FileInfo == nullptr || FileInfo->SigHeader[0].DataType == enDataFormat_Int16)
    {
        return WT_OK;
    }

    int FrmBegin, FrmEnd;
    double Scale;
    double Gain = sqrt(1000 * 50);

    for (int i = 0; i < FileInfo->GetSigNum(); i++)
    {
        if (AllocBuf(FileInfo->SigHeader[i].SampleCount) == nullptr)
        {
            return WT_ALLOC_FAILED;
        }

        Complex *Data = static_cast<Complex *>(FileInfo->SigHeader[i].Data);
//#if TIME_DEBUG
//    struct timeval tpstart, tpend;
//    gettimeofday(&tpstart, NULL);
//#endif
        //WTLog::Instance().WriteLog(LOG_DEBUG, "WT_Algorithm_NormalFrmPwr0dBm FileInfo->SigHeader[i].ClockRate = %d\n",FileInfo->SigHeader[i].ClockRate);
        int Ret = WT_Algorithm_NormalFrmPwr0dBm(m_RxOut, Data, FileInfo->SigHeader[i].SampleCount,
                                                round(FileInfo->SigHeader[i].SamplingRate / 1000000),
                                                &Scale, &FrmBegin, &FrmEnd, FileInfo->SigHeader[i].ClockRate);
//#if TIME_DEBUG
//    gettimeofday(&tpend, NULL);
//    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##111NormalizationFile-WT_Algorithm_NormalFrmPwr0dBm Used Time:" << timeuse << "us" << std::endl;
//#endif
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "NormalizationFile WT_Algorithm_NormalFrmPwr0dBm failed");
            return Ret;
        }

        for (int j = 0; j < FileInfo->SigHeader[i].SampleCount; j++)
        {
            Data[j][0] *= Scale * Gain;
            Data[j][1] *= Scale * Gain;
        }

        // WTLog::Instance().WriteLog(LOG_DEBUG, "======== frm start %d, end %d , adc point us %d =================\n", FrmBegin, FrmEnd, m_RxOut->local->adc_point_us);

        // std::ofstream PnData(WTConf::GetDir() + "/vsgdata1.csv", fstream::out | fstream::trunc);
        // char Buf[128];
        // for (int j = 0; j < FileInfo->SigHeader[i].SampleCount; j++)
        // {
        //     sWTLog::Instance().WriteLog(LOG_DEBUG, Buf, "%f,%f\n", Data[j][0], Data[j][1]);
        //     PnData <<  Buf;
        // }
    }

    return WT_OK;
}

int Compensate::NormalizationFile(void *FileData, const int FileLen, const string FileName)
{
    SigFileInfo *FileInfo = nullptr;
    SigFile File(FileData, FileLen);

    if (File.GetContent(&FileInfo) != WT_OK)
    {
        return WT_SIG_FILE_ERROR;
    }

    if (FileInfo == nullptr)
    {
        return WT_OK;
    }

    std::unique_ptr<char[]> DataBuf[FileInfo->GetSigNum()]; //临时buffer，用于保存data
    do
    {
        if (DigModeLib::Instance().IsDigMode() &&
            (FileInfo->GetSigNum() == 0 || FileInfo->SigHeader[0].DataType == enDataFormat_Int16))
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "not NormalizationFile\n");
            break;
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "FileInfo->SigHeader[0].DataType = %d\n", FileInfo->SigHeader[0].DataType);

        for (int i=0; i<FileInfo->GetSigNum(); i++)
        {
            DataBuf[i].reset(new(std::nothrow) char[FileInfo->SigHeader[i].SampleCount * sizeof(double) * 2]);
        }
        //vsg外部下发的文件补偿vsa IQ不平衡，平坦度等数据
        for (int i = 0; i < FileInfo->GetSigNum(); i++)
        {
            void *Buf = AllocBuf(FileInfo->SigHeader[i].SampleCount);

            if (Buf == nullptr)
            {
                return WT_ALLOC_FAILED;
            }

            // 原始文件可能带有补偿数据，需要先补偿一次
            if (FileInfo->SigHeader[i].NeedCompensate())
            {
                if (FileInfo->SigHeader[i].SampleCount == 0)
                {
                    continue;
                }
                stSpectrumOffset SpectOffset;
                ExtendEVMStu ExtendEVM;
                SetSigDataToVsa(&FileInfo->SigHeader[i], &m_RxIn, &SpectOffset, ExtendEVM);
                m_RxIn.demod_mode = FileInfo->SigHeader[i].ModType;

                // #if TIME_DEBUG
                //     struct timeval tpstart, tpend;
                //     gettimeofday(&tpstart, NULL);
                // #endif
                int Ret = WT_OK;
                if (m_AlzFlatnessEnable == 1)
                {
                    CompensateRuCarrierInfo(&m_CarrierInfo[i]);

                    Ret = WT_Algorithm_CorrectionHardwareParameter(&m_RxIn, Buf, m_RxIn.capturecnt * sizeof(Complex));
                }
                else
                {
                    Complex *CaptureData = NULL;
                    CaptureData = (Complex *)Buf;
                    if (m_RxIn.capturedata_format == enDataFormat_Int16)
                    {
                        typedef struct
                        {
                            s16 real;
                            s16 imag;
                        } ADC_Data;
                        ADC_Data *Dat;
                        Dat = (ADC_Data *)m_RxIn.capturedata;
                        for (int i = 0; i < m_RxIn.capturecnt; i++)
                        {
                            CaptureData[i][0] = Dat[i].real;
                            CaptureData[i][1] = Dat[i].imag;
                        }
                    }
                    else if (m_RxIn.capturedata_format == enDataFormat_Float64)
                    {
                        memcpy((char *)CaptureData, (char *)m_RxIn.capturedata, m_RxIn.capturecnt * sizeof(Complex));
                    }
                }
                // #if TIME_DEBUG
                //     gettimeofday(&tpend, NULL);
                //     int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
                //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##NormalizationFile-WT_Algorithm_CorrectionHardwareParameter Used Time:" << timeuse << "us" << std::endl;
                // #endif
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(WT_VSA_COMPENSATE_FAILED, "compensate data failed");
                    return WT_VSA_COMPENSATE_FAILED;
                }

                memcpy(DataBuf[i].get(), Buf, m_RxIn.capturecnt * sizeof(Complex));
                FileInfo->SigHeader[i].Data = DataBuf[i].get();

                //            if(FileInfo->SigHeader[i].DataType == enDataFormat_Int16)
                //            {
                //                for (int j = 0; j < FileInfo->SigHeader[i].SampleCount*2; j++)
                //                {
                //                    *((double *)(DataBuf[i].get())+j) = *((short *)FileInfo->SigHeader[i].Data+j);
                //                }
                //                FileInfo->SigHeader[i].Data = DataBuf[i].get();
                //            }

                FileInfo->SigHeader[i].IQGainImb = 0;
                FileInfo->SigHeader[i].IQPhaseImb = 0;
                FileInfo->SigHeader[i].DCOffsetI = 0;
                FileInfo->SigHeader[i].DCOffsetQ = 0;
                FileInfo->SigHeader[i].TimeSkew = 0;
                FileInfo->SigHeader[i].RfResponse.FreqCount = 0;
                FileInfo->SigHeader[i].RfResponse.Response = NULL;
                FileInfo->SigHeader[i].BBResponse.FreqCount = 0;
                FileInfo->SigHeader[i].BBResponse.Response = NULL;
                FileInfo->SigHeader[i].SpectOffset.Valid = 0;
                if (FileInfo->SigHeader[i].DataType == enDataFormat_Int16)
                {
                    FileInfo->SigHeader[i].DataType = enDataFormat_Float64;
                }
            }

            //        {
            //            std::ofstream PnData(WTConf::GetDir() + "/vsgdatanew.csv", fstream::out | fstream::trunc);
            //            char Buf1[128];
            //
            //            Complex *Data1 = static_cast<Complex *>(Buf);
            //            for (int j = 0; j < FileInfo->SigHeader[i].SampleCount; j++)
            //            {
            //                sprintf(Buf1, "%f,%f\n", Data1[j][0], Data1[j][1]);
            //                PnData <<  Buf1;
            //            }
            //        }
        }

        // 归一化,功率归零处理
        int FrmBegin, FrmEnd;
        double Scale[FileInfo->GetSigNum()];
        double Gain = sqrt(1000 * 50);
        double MaxPower = UNVALID_DOUBLE_VAL;
        double ChPower[FileInfo->GetSigNum()];

        for (int i = 0; i < FileInfo->GetSigNum(); i++)
        {
            if (FileInfo->SigHeader[i].SampleCount == 0)
            {
                continue;
            }
            // WTLog::Instance().WriteLog(LOG_DEBUG, "FileInfo->SigHeader[i].ClockRate = %d\n",FileInfo->SigHeader[i].ClockRate);
            Complex *Data = static_cast<Complex *>(FileInfo->SigHeader[i].Data);
            // #if TIME_DEBUG
            //     struct timeval tpstart, tpend;
            //     gettimeofday(&tpstart, NULL);
            // #endif
            int Ret = WT_Algorithm_NormalFrmPwr0dBm(m_RxOut, Data, FileInfo->SigHeader[i].SampleCount,
                                                    round(FileInfo->SigHeader[i].SamplingRate / 1000000),
                                                    &Scale[i], &FrmBegin, &FrmEnd, FileInfo->SigHeader[i].ClockRate);
            // #if TIME_DEBUG
            //     gettimeofday(&tpend, NULL);
            //     int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
            //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##222WT_Algorithm_NormalFrmPwr0dBm Used Time:" << timeuse << "us" << std::endl;
            // #endif
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "NormalizationFile WT_Algorithm_NormalFrmPwr0dBm failed");
                return Ret;
            }

            ChPower[i] = (m_RxOut->pkg.frmcnt > 0) ? m_RxOut->pwr.frm_pwravg_dbm[0]
                                                   : m_RxOut->pwr.pkg_pwravg_dbm;
            if (MaxPower < ChPower[i])
            {
                MaxPower = ChPower[i];
            }
        }

        for (int i = 0; i < FileInfo->GetSigNum(); i++)
        {
            if (FileInfo->SigHeader[i].SampleCount == 0)
            {
                continue;
            }
            Complex *Data = static_cast<Complex *>(FileInfo->SigHeader[i].Data);

            if (Basefun::CompareDouble(MaxPower, UNVALID_DOUBLE_VAL) != 0 && fabs(MaxPower - ChPower[i]) > MIMO_MAX_DIFF_POWER)
            {
                //            std::ofstream PnData(WTConf::GetDir() + "/vsgdata2.csv", fstream::out | fstream::trunc);
                //            char Buf[128];
                //            for (int j = 0; j < FileInfo->SigHeader[i].SampleCount; j++)
                //            {
                //                sprintf(Buf, "%f,%f\n", Data[j][0], Data[j][1]);
                //                PnData <<  Buf;
                //            }
                continue;
            }
            FileInfo->SigHeader[i].RFGain = 0;
            for (int j = 0; j < FileInfo->SigHeader[i].SampleCount; j++)
            {
                Data[j][0] *= Scale[i] * Gain;
                Data[j][1] *= Scale[i] * Gain;
            }

            //        std::ofstream PnData(WTConf::GetDir() + "/vsgdata1.csv", fstream::out | fstream::trunc);
            //        char Buf[128];
            //        for (int j = 0; j < FileInfo->SigHeader[i].SampleCount; j++)
            //        {
            //            sprintf(Buf, "%f,%f\n", Data[j][0], Data[j][1]);
            //            PnData <<  Buf;
            //        }
        }
    } while (0);

    // 保存到文件
    SigFile SaveSigFile(FileName.c_str(), SigFile::WRITE);
    SaveSigFile.Save(*FileInfo);

    return WT_OK;
}

int Compensate::SetVsgFlatnessCal(int Enable)
{
    m_AlzFlatnessEnable = Enable;
    return WT_OK;
}

int Compensate::SetVsgIQImbCal(int Enable)
{
    m_AlzIQImbEnable = Enable;
    return WT_OK;
}

int Compensate::SetVsgRuCarrierInfo(void *Data)
{
    memcpy((char *)m_CarrierInfo, (char *)Data, sizeof(m_CarrierInfo));
    return WT_OK;
}

void Compensate::ResetVsgRuCarrierInfo(void)
{
    memset(m_CarrierInfo, 0, sizeof(m_CarrierInfo));
}

void Compensate::SetCurrenAmendStream(int StreamIndex)
{
    m_CurStream = StreamIndex;
}

void Compensate::SetStaticIQParam(int Segment, double Ampl, double Phase, double TimeSkew)
{
    Segment--;
    m_StaticIQParam[Segment].IQAmpl = Ampl;
    m_StaticIQParam[Segment].IQPhase = Phase;
    m_StaticIQParam[Segment].TimeSkew = TimeSkew;
    m_StaticIQParam[Segment].Valid = true;
}

void Compensate::ClrStaticIQParam(int Segment)
{
    Segment--;
    m_StaticIQParam[Segment].Valid = false;
}

