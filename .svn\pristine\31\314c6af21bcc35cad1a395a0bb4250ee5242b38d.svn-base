//  File: wtspec.h
//  仪器资源规格定义
//  Data: 2016.7.16
//*****************************************************************************
#ifndef __WT_SPEC_H__
#define __WT_SPEC_H__

//最大单元板数量
#ifdef WT418_FW
#define MAX_UNIT_NUM            2
#else
#define MAX_UNIT_NUM            8
#endif

//默认采样率
#ifdef WT418_FW
#define MAX_SMAPLE_RATE_MHZ (240)
#define MAX_SMAPLE_RATE (MAX_SMAPLE_RATE_MHZ * MHz)
#else
#define MAX_SMAPLE_RATE_MHZ (480)
#define MAX_SMAPLE_RATE (MAX_SMAPLE_RATE_MHZ * MHz)
#endif

#ifndef MAX_SAMPLEING_TIME
#ifdef WT418_FW
#define MAX_SAMPLEING_TIME (100 * Ms)
#else
#define MAX_SAMPLEING_TIME (20 * Ms)
#endif
#endif

#define MAX_SEGMENT_CNT 2                   //最大segment段数，8080时最大为2
//最大DMA数据长度
#ifdef WT418_FW
#define DMA_BUF_SIZE        (128 << 20)  //DMA大小，128M
#else
#define DMA_BUF_SIZE        (128 << 20)  //DMA大小，128M
#endif

//SERVER预留内存池大小
#ifdef WT418_FW
#define MEMORY_POOL_SIZE        (1 << 30)  //DMA大小，1G
#else
#define MEMORY_POOL_SIZE        (2 << 30)  //DMA大小，2G
#endif

#endif //__WT_SPEC_H__