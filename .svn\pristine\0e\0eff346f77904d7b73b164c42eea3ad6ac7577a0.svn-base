/**
 * @file scpi_3gpp_result_general.cpp
 * @brief 获取蜂窝通用结果
 * @version 0.1
 * @date 2024-10-09
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#include "scpi_3gpp_result_general.h"

#include "basehead.h"
#include "commonhandler.h"
#include "cellular_result_common_interface.h"

using namespace cellular::result::common;

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstPkgAvgPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_PKGAVGPWRDBM);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstPkgPeakPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_PKGPEAKPWRDBM);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFrmAvgPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_FRMAVGPWRDBM);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFrmPeakPwrdBm(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_FRMPEAKPWRDBM);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumFreqStart(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_FREQ_START, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumFreqCenter(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_FREQ_CENTER, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumFreqEnd(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_SPECTRUM_FREQ_END, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumEmissionDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_RXSPECTEMIS, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstAddSpectrumEmissionDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_ADDSPECTEMIS, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstAddSpectrumEmissionSegDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_ADDSPECTEMISSEG, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumEmissionMaskDataARB(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_SPECTEMISMASK, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumEmissionMaskSegDataARB(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_SPECTRAL_EMISMASKSEG, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumSemNeg(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_SPECTRAL_SEMMARGNEG_Y: WT_RES_3GPP_SPECTRAL_SEMMARGNEG_X, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumSemPos(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_SPECTRAL_SEMMARGPOS_Y: WT_RES_3GPP_SPECTRAL_SEMMARGPOS_X, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumSemMarginPos(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MARGINPOSHIGH : WT_RES_3GPP_MARGINPOSLOW, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumSemMarginNeg(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MARGINNEGHIGH : WT_RES_3GPP_MARGINNEGLOW, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumSemMarginAll(scpi_t *context)
{
    int value = 0;
    scpi_result_t Ret;
    SCPI_ParamInt(context, &value, false);
    Ret = GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MARGINNEGHIGH : WT_RES_3GPP_MARGINNEGLOW, false);
    if (Ret != SCPI_RES_OK)
    {
        return Ret;
    }
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MARGINPOSHIGH : WT_RES_3GPP_MARGINPOSLOW, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumBadPointcnt(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_SPECTRAL_BADPOINTCNT, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRUtraPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_POWER, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRUtraMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_MASK, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRUtraFreq(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_FREQ, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRUtraAclRatio(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_ACLRATIO, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLREUtraPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_POWER, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLREUtraMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_MASK, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLREUtraFreq(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_FREQ, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLREUtraAclRatio(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_ACLRATIO, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRAclRatio(scpi_t *context)
{
    scpi_result_t Ret;

    Ret = GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_EUTRA_ACLRATIO, false);
    if (Ret != SCPI_RES_OK)
    {
        return Ret;
    }
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_UTRA_ACLRATIO, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraPower(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_POWER, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraMask(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_MASK, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraFreq(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_FREQ, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSpectrumACLRNrUtraAclRatio(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_ACLR_NRUTRA_ACLRATIO, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSymoblConstARB(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_RXPOINT_HIGH_DATA : WT_RES_3GPP_EVM_RXPOINT_LOW_DATA, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSymoblPilotConstARB(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_RXPOINT_HIGH_PILOT : WT_RES_3GPP_EVM_RXPOINT_LOW_PILOT, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstEvmCarrierEvm(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_EVM_CARRIEREVM, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstEvmMaxCarrierLen(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_EVM_MAXCARRIERLEN);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSymoblEVM(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_SYMBEVM_LOW_PILOT : WT_RES_3GPP_EVM_SYMBEVM_LOW_DATA, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstPilotSymoblEVM(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_EVM_SYMBEVM_HIGH_PILOT : WT_RES_3GPP_EVM_SYMBEVM_HIGH_DATA, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrMagnErr(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MPERR_MAGNERR_HIGH : WT_RES_3GPP_MPERR_MAGNERR_LOW, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrErrRms(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERRRMS, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrMagnErrPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERRPEAK, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrMagnErrDmrs(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_MAGNERRDMRS, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrPhaseErrs(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleVectorData(context, (value == 1) ? WT_RES_3GPP_MPERR_PHASEERR_HIGH : WT_RES_3GPP_MPERR_PHASEERR_LOW, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrPhaseErrRms(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRRMS, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrPhaseErrPeak(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRPEAK, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstMPErrPhaseErrDmrs(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_MPERR_PHASEERRDMRS, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFlatnessMaxFlatNum(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_FLATNESS_MAXFLATNUM);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFlatnessRange(scpi_t *context)
{
    return GetRstDoubleVectorData(context, WT_RES_3GPP_FLATNESS_RANGETYPE, true);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFlatnessRipple(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleData(context, (value == 1) ? WT_RES_3GPP_FLATNESS_RIPPLE1 : WT_RES_3GPP_FLATNESS_RIPPLE2);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFlatnessCrossRipple(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstDoubleData(context, (value == 1) ? WT_RES_3GPP_FLATNESS_MAXR2SUBMINR1 : WT_RES_3GPP_FLATNESS_MAXR1SUBMINR2);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstFlatnessAllRipple(scpi_t *context)
{
    scpi_result_t Ret;
    Ret = GetRstDoubleData(context, WT_RES_3GPP_FLATNESS_RIPPLE1);
    if (Ret != SCPI_RES_OK)
    {
        return Ret;
    }
    Ret = GetRstDoubleData(context, WT_RES_3GPP_FLATNESS_RIPPLE2);
    if (Ret != SCPI_RES_OK)
    {
        return Ret;
    }
    Ret = GetRstDoubleData(context, WT_RES_3GPP_FLATNESS_MAXR2SUBMINR1);
    if (Ret != SCPI_RES_OK)
    {
        return Ret;
    }
    return GetRstDoubleData(context, WT_RES_3GPP_FLATNESS_MAXR1SUBMINR2);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstUtraResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_UTRARESULT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstEutraResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_EUTRARESULT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstNrUtraResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_NRUTRARESULT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstACLRResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_ACLR_RESULT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSEMResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_SPECTRAL_SEMRESULT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstReferenceSignalReceivingPower(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_POWER);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstReceivedSignalStrengthIndication(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_RECEIVED_SIGNAL_STRENGTH_INDICATION);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstReferenceSignalReceivingQuality(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_QUALITY);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstSignaltoNoiseRatio(scpi_t *context)
{
    return GetRstDoubleData(context, WT_RES_3GPP_SIGNAL_TO_NOISE_RATIO);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstInEmisResult(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INEMIS_INEMISRESULT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstLinkDirect(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_LINKDIRECT);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstChannel(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_CHANNEL);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstCodeword(scpi_t *context)
{
    return GetRstIntData(context, WT_RES_3GPP_INFO_CODEWORD);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaCwModulate(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_MODULATE, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstCwScrambling(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_SCRAMBLING, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstCwChannelCodingType(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_CHANNELCODINGTYPE, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstCwCrc(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_CRC, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstBitLen(scpi_t *context)
{
    return GetRstIntVectorData(context, WT_RES_3GPP_INFO_CW_BITLEN, false);
}

scpi_result_t cellular::result::general::SCPI_3GPP_GetVsaRstBitSeq(scpi_t *context)
{
    int value = 0;
    SCPI_ParamInt(context, &value, false);
    return GetRstIntVectorData(context, (value == 1) ? WT_RES_3GPP_INFO_CW_BITSEQ_1 : WT_RES_3GPP_INFO_CW_BITSEQ_0, true);
}



















