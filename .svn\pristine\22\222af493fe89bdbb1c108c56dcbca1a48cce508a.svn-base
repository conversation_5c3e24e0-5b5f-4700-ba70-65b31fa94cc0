//*****************************************************************************
//  File: analysis.cpp
//  算法功能封装
//  Data: 2016.8.23
//*****************************************************************************
#include "analysis.h"
#include <fstream>
#include <new>
#include <cstdlib>
#include <cstring>
#include <cerrno>
#include <cstdio>
#include <malloc.h>
#include "wtlog.h"
#include "wterror.h"
#include "protocol.h"
#include "service/vsa.h"
#include "result.h"
#include "calconf.h"
#include "basefun.h"
#include "resultdef.h"
#include "algenv.h"
#include "digitallib.h"
#include "conf.h"
#include "spectrumMarginCal.h"
#include "demod.h"
#include "../../scpi/scpiapiwrapper/scpi_3gpp_base.h"
#include "algreset.h"

#define CMIMO_MIMO 1

using namespace std;

static const int SPECTRUM_SUBCHAN_NUM = 384 * 2;

void SetSigDataToVsa(const SigDataHeader *SigHeader, RX_InDat *pInData, stSpectrumOffset *SpectOffset, const ExtendEVMStu &ExtendEvm)
{
    pInData->capturedata = SigHeader->Data;
    pInData->capturecnt = SigHeader->SampleCount;
    pInData->capturedata_format = SigHeader->DataType;
    pInData->ScaleTo = AlgEnv::Instance().GetScaleVal(SigHeader->DataType);
    pInData->adc_freq = round(SigHeader->SamplingRate);
    pInData->referenceLevel = SigHeader->RefLevel;
    pInData->trig_pwr = SigHeader->TriggerLevel;
    pInData->frequencyOffsetHz = SigHeader->FreqOffset;

    pInData->gain = SigHeader->RFGain;
    pInData->extgain = SigHeader->ExtAtt;
    pInData->rf_center_freq = SigHeader->CenterFreq;
    pInData->IDCOffset = SigHeader->DCOffsetI;
    pInData->QDCOffset = SigHeader->DCOffsetQ;
    pInData->IQImb_Amp = SigHeader->IQGainImb;
    pInData->IQImb_Phase = SigHeader->IQPhaseImb;
    pInData->TimeSkew = SigHeader->TimeSkew;

    pInData->rf_response = SigHeader->RfResponse.Response;
    pInData->rf_response_len = SigHeader->RfResponse.FreqCount;
    pInData->bb_response = SigHeader->BBResponse.Response;
    pInData->bb_response_len = SigHeader->BBResponse.FreqCount;

    pInData->Extend_EVM = ExtendEvm.IterativeEVM == Iterative_EVM_ON ? Iterative_EVM_ON : Iterative_EVM_OFF;
    pInData->CC_EVM = ExtendEvm.CcEVM == CC_EVM_ON ? CC_EVM_ON : CC_EVM_OFF;
    pInData->SNC_EVM = ExtendEvm.SncEVM == SNC_EVM_ON ? SNC_EVM_ON : SNC_EVM_OFF;
    if(pInData->SNC_EVM == SNC_EVM_ON && SigHeader->NSResponse.FreqCount > 0)
    {
        pInData->noise_response = SigHeader->NSResponse.Response;
        pInData->noise_response_len = SigHeader->NSResponse.FreqCount;
    }
    else
    {
        pInData->noise_response = nullptr;
        pInData->noise_response_len = 0;
    }

    pInData->clockrate = SigHeader->ClockRate;
    //clockrate 做保护,如果不在合理范围内，强制给默认1
    if(pInData->clockrate != WT_CLOCK_RATE_1 && pInData->clockrate != WT_CLOCK_RATE_1_2
            && pInData->clockrate != WT_CLOCK_RATE_1_4 && pInData->clockrate != WT_CLOCK_RATE_1_5
            && pInData->clockrate != WT_CLOCK_RATE_1_8 && pInData->clockrate != WT_CLOCK_RATE_1_10
            && pInData->clockrate != WT_CLOCK_RATE_1_20)
    {
        pInData->clockrate = WT_CLOCK_RATE_1;
    }

    if (SigHeader->SpectOffset.Valid && SpectOffset)
    {
        SpectOffset->valid = SigHeader->SpectOffset.Valid;
        memcpy(SpectOffset->firstOffset, SigHeader->SpectOffset.First, sizeof(double) * OFFSET_POINT_COUNT);
        memcpy(SpectOffset->lastOffset, SigHeader->SpectOffset.Last, sizeof(double) * OFFSET_POINT_COUNT);
        pInData->spectOffset = SpectOffset;
    }
    else if (pInData->spectOffset)
    {
        pInData->spectOffset->valid = 0;
    }
}

static void ClrCalParam(RX_InDat *pInData)
{
    pInData->gain = 0;
    pInData->IQImb_Amp = 0;
    pInData->IQImb_Phase = 0;
    pInData->IDCOffset = 0;
    pInData->QDCOffset = 0;
    pInData->TimeSkew = 0;
    pInData->freq_IQImb_amp = nullptr;
    pInData->freq_IQImb_len = 0;
    pInData->freq_IQImb_phase = nullptr;

    pInData->rf_response = nullptr;
    pInData->rf_response_len = 0;
    pInData->bb_response = nullptr;
    pInData->bb_response_len = 0;
    pInData->spectOffset = nullptr;
    pInData->noise_response_len = 0;
    pInData->noise_response = nullptr;
}

Composite8080Result::~Composite8080Result()
{
    //Reset(); //8080算法使用的内容统一使用下位机申请的，不用再主动free
}

void Composite8080Result::Reset()
{
    for (int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
    {
//        free(m_Result[i].spect80And80.pSpectMask);
        m_Result[i].spect80And80.pSpectMask = nullptr;

//        free(m_Result[i].spect80And80.pSpectAllDbr);
        m_Result[i].spect80And80.pSpectAllDbr = nullptr;

//        free(m_Result[i].Evm.point);
        m_Result[i].Evm.point = nullptr;

//        free(m_Result[i].Evm.sym);
        m_Result[i].Evm.sym = nullptr;

//        free(m_Result[i].Evm.ch);
        m_Result[i].Evm.ch = nullptr;

//        free(m_Result[i].Evmsigb.point);
        m_Result[i].Evmsigb.point = nullptr;

//        free(m_Result[i].Evmsigb.sym);
        m_Result[i].Evmsigb.sym = nullptr;

//        free(m_Result[i].Evmsigb.ch);
        m_Result[i].Evmsigb.ch = nullptr;

        for (int j = 0; j < DF_Max_UeNum_11ax; j++)
        {
//            free(m_Result[i].EvmUe[j].point);
//            free(m_Result[i].EvmUe[j].sym);
//            free(m_Result[i].EvmUe[j].ch);
            m_Result[i].EvmUe[j].point = nullptr;
            m_Result[i].EvmUe[j].sym = nullptr;
            m_Result[i].EvmUe[j].ch = nullptr;
        }

        m_Result[i].UserNum = 0;
        m_Result[i].Evm.validflag = 0;
        m_Result[i].Evmsigb.validflag = 0;
    }
}

void Analysis::SetAlgVsaParam(RX_InDat *pInData, const VsaAlzParam &AlzParam)
{
    //80+80拼160时demod必须是8080
    if (m_Comb8080To160)
    {
        if (AlzParam.Demode >= WT_DEMOD_11AC_20M && AlzParam.Demode <= WT_DEMOD_11AC_80_80M)
        {
            pInData->demod_mode = WT_DEMOD_11AC_80_80M;
        }
        else if (AlzParam.Demode >= WT_DEMOD_11AX_20M && AlzParam.Demode <= WT_DEMOD_11AX_160_160M)
        {
            pInData->demod_mode = WT_DEMOD_11AX_80_80M;
            if(AlzParam.Demode == WT_DEMOD_11AX_160_160M)
            {
                pInData->demod_mode = WT_DEMOD_11AX_160_160M;
            }
        }
        else
        {
            pInData->demod_mode = AlzParam.Demode;
        }
    }
    else
    {
        pInData->demod_mode = AlzParam.Demode;
    }
    pInData->iqswap = AlzParam.CommParam.IQSwap;
    pInData->iqreversion = AlzParam.CommParam.IQReversion;
    pInData->FilterFromatTime = AlzParam.CommParam.FilterPktByTime;
    pInData->frequencyOffsetHz = AlzParam.CommParam.FreqOffset;
    pInData->cSpectrumFlag = AlzParam.CommParam.SpectrumFlag;
    pInData->cCcdfFlag = AlzParam.CommParam.CcdfFlag;

    if (pInData->Extend_EVM != Iterative_EVM_ON)
    {//容错
        pInData->AlzNumOfFrame = 0;
    }
    else
    {
        pInData->AlzNumOfFrame = AlzParam.CommParam.AvgTimes;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "AlzNumOfFrame = %d \n",pInData->AlzNumOfFrame);

    //H矩阵配置
    pInData->HMatrix.HmatrixEnable = AlzParam.CommParam.HmatrixEnable;
    pInData->HMatrix.RxAntennaNum = AlzParam.CommParam.HmatRxAntennaNum;
    pInData->HMatrix.TxAntennaNum = AlzParam.CommParam.HmatTxAntennaNum;
    for(int i = 0; i < 8; i++)
    {
        for(int j = 0; j < 8; j++)
        {
            pInData->HMatrix.MatrValue[i][j][0] = AlzParam.CommParam.HMatValue[i][j][0];
            pInData->HMatrix.MatrValue[i][j][1] = AlzParam.CommParam.HMatValue[i][j][1];
        }
    }

    pInData->FrameAutoDetection = AlzParam.WifiParam.AutoDetect;
    if (AlzParam.Demode == WT_DEMOD_LRWPAN_FSK || AlzParam.Demode == WT_DEMOD_LRWPAN_OQPSK ||AlzParam.Demode == WT_DEMOD_LRWPAN_OFDM)
    {
        pInData->FrameAutoDetection = WT_USER_DEFINED;
    }
    //规避处理，如果是8080时，强制配置智能模式为user define，规格暂时不支持8080开启自动识别功能
    if(pInData->vht80_80MEnable == 1)
    {
        pInData->FrameAutoDetection = WT_USER_DEFINED;
    }
    pInData->channelest_enable = AlzParam.WifiParam.ChEstimate;
    pInData->amp_track_enable = AlzParam.WifiParam.AmplTrack;
    pInData->phase_track_enable = AlzParam.WifiParam.PhsCorrMode;
    pInData->sym_tim_corr = AlzParam.WifiParam.SynTimeCorr;
    pInData->freq_sync = AlzParam.WifiParam.FreqSyncMode;

    pInData->cck_dcremove_enable = AlzParam.WifiParam.DCRemoval;
    pInData->cck_phase_track_enable = AlzParam.WifiParam.PhsCorrMode11B;
    pInData->cck_equalizertaps = AlzParam.WifiParam.EqTaps;
    pInData->cck_evm_method = AlzParam.WifiParam.Method11b;

    pInData->BT_DataRate = AlzParam.BTParam.BTDataRate;
    pInData->BT_PacketType = AlzParam.BTParam.BTPktType;
    pInData->BT_BleEnhancedMode = AlzParam.BTParam.BTBleEnhance;
    pInData->BT_BlePDUType = AlzParam.BTParam.BTBlePDUPktype;
    pInData->BT_BleSyncMode = AlzParam.BTParam.BTBleSyncMode;
    pInData->BT_BleAccessAddress = AlzParam.BTParam.BTBleAccessAddress;
    pInData->BT_BleChannelIndex = AlzParam.BTParam.BTBleChannelIndex;
    pInData->BT_ACPViewRange = AlzParam.BTParam.ACPViewRangeType;
    pInData->BT_ACPSweepTimes = AlzParam.BTParam.ACPSweepTimes;

    pInData->group_fft_rbw = AlzParam.FFTParam.Rbw;
    pInData->group_fft_vbw = AlzParam.FFTParam.Rbw;
    pInData->spectrumMaskVersion = AlzParam.WifiParam.SpectrumMaskVersion;
    //2022-12-21:stbc二合一下，控制导频无效的指令，3xx和4xx已经没搞stbc二合一了，都是拿mimo测的了
    pInData->MIMOAnalysisMode = 0;//强制赋值等于0

    pInData->MaxPwrDiffForMimo = AlzParam.WifiParam.MimoMaxPowerDiff;
    if (m_SigFile == nullptr)   //分析文件时，读取文件配置的clockrate
    {
        pInData->clockrate = AlzParam.WifiParam.ClockRate;

        //clockrate 做保护
        if(pInData->clockrate != WT_CLOCK_RATE_1 && pInData->clockrate != WT_CLOCK_RATE_1_2
                && pInData->clockrate != WT_CLOCK_RATE_1_4 && pInData->clockrate != WT_CLOCK_RATE_1_5
                && pInData->clockrate != WT_CLOCK_RATE_1_8 && pInData->clockrate != WT_CLOCK_RATE_1_10
                && pInData->clockrate != WT_CLOCK_RATE_1_20)
        {
            pInData->clockrate = WT_CLOCK_RATE_1;
        }
    }
    pInData->FullCRC_Flag = AlzParam.WifiParam.FullCRCFlag;
    //WTLog::Instance().WriteLog(LOG_DEBUG, "pInData->FullCRC_Flag = %d\n",pInData->FullCRC_Flag);

    pInData->nonHTDupBW = AlzParam.WifiParam.NonHTDupBW;
    pInData->IQCompensation = AlzParam.WifiParam.IQCompensation;
    pInData->PreambleAverage = AlzParam.WifiParam.PreambleAverage;
    pInData->EqualizerSmoothing = AlzParam.WifiParam.EqualizerSmoothing;
    pInData->ICISuppression = AlzParam.WifiParam.ICISuppression;
    WTLog::Instance().WriteLog(LOG_DEBUG, "EqualizerSmoothing = %d ICISuppression = %d \n",pInData->EqualizerSmoothing, pInData->ICISuppression);
    pInData->SfoCompensation = AlzParam.WifiParam.SfoCompensation;
    pInData->LdpcDecodeIterationTimes = AlzParam.WifiParam.LdpcDecodeIterationTimes;
    pInData->OBWCalcFlag = AlzParam.WifiParam.OBWCalcFlag;
    pInData->LdpcDecodeMethod = AlzParam.WifiParam.HardwareDecodeFlag;

    //ah
    pInData->FrameType11AH = AlzParam.WifiParam.FrameType11AH;

    //zigbee param
    pInData->ZBAnalysisOptimise = AlzParam.ZigBeeParam.Optimize;

    //zwave param
    pInData->ZwaveRate = AlzParam.ZwaveParam.ZwaveRate;

    //11ba param
    pInData->EnbaleEmbeddedBSSID = AlzParam.WifiParam.EnbaleEmbeddedBSSID;
    if(pInData->EnbaleEmbeddedBSSID == 1)
    {
        for(int i = 0; i < (sizeof(pInData->EmbeddedBSSID)/sizeof(pInData->EmbeddedBSSID[0])); i++)
        {
            pInData->EmbeddedBSSID[i] = AlzParam.WifiParam.EmbeddedBSSID[i];
        }
    }
    //WTLog::Instance().WriteLog(LOG_DEBUG, "BSSID =%d,%d,%d,%d,%d\n", pInData->EnbaleEmbeddedBSSID,pInData->EmbeddedBSSID[0],pInData->EmbeddedBSSID[1],pInData->EmbeddedBSSID[2],pInData->EmbeddedBSSID[3]);

    if (pInData->adc_freq == (int)MAX_SMAPLE_RATE)
    {
        if (pInData->channelest_enable == WT_CH_EST_RAW_FULL)
        {
            pInData->evmoffset = AlgEnv::Instance().m_EvmOffset240MFP;
        }
        else
        {
            pInData->evmoffset = AlgEnv::Instance().m_EvmOffset240M;
        }
    }
    else if (pInData->adc_freq == 120000000)
    {
        if (pInData->channelest_enable == WT_CH_EST_RAW_FULL)
        {
            pInData->evmoffset = AlgEnv::Instance().m_EvmOffset120MFP;
        }
        else
        {
            pInData->evmoffset = AlgEnv::Instance().m_EvmOffset120M;
        }
    }

    pInData->analyzeGroup = m_AnalyseGroup;
    if(AlzParam.Demode == WT_DEMOD_CW)
    {
        pInData->analyzeGroup = WT_GROUP_FFT;//WT_GROUP_FFT 1
    }

#ifdef DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "#####pInData->analyzeGroup = %d\n",pInData->analyzeGroup);
#endif

    WTLog::Instance().WriteLog(LOG_DEBUG, "###Set analyzeMode = %d\n", m_AnalyzeMode);
    pInData->analyzeMode = m_AnalyzeMode;

    m_FlatnessFitting = false;


    //特殊处理，如果开启了Non-HT Duplicate 带宽配置，分析打孔，则强制把总流数置为1.避免分析mimo信号得不到打孔的情况
    //目前打孔分析只支持siso，且模式必须为user defined,且demo要为11ag
    if (pInData->nonHTDupBW > 0)
    {
        m_RxInData[0].iNumOfChIn = 1;
        m_RxInData[1].iNumOfChIn = 1;
        //demo 为11ag，user defined
        pInData->demod_mode = WT_DEMOD_11AG;
        pInData->FrameAutoDetection = WT_USER_DEFINED;
    }
    //特殊处理，如果clockrate不为1，则强制转userdefined，不支持自动识别
    if(pInData->clockrate > 1)
    {
        pInData->FrameAutoDetection = WT_USER_DEFINED;
    }

    //GLE
    if (AlzParam.Demode == WT_DEMOD_GLE)
    {
        pInData->Gle.FrmType = AlzParam.AnalyzeParamSparkLink.FrmType;
        //pInData->Gle.Bandwidth = AlzParam.AnalyzeParamSparkLink.Bandwidth;
        pInData->Gle.CtrlInfoType = AlzParam.AnalyzeParamSparkLink.CtrlInfoType;
        pInData->Gle.PayloadCrcType = AlzParam.AnalyzeParamSparkLink.PayloadCrcType;
        pInData->Gle.PayloadCrcSeed = AlzParam.AnalyzeParamSparkLink.PayloadCrcSeed;
        pInData->Gle.SlotIndex = AlzParam.AnalyzeParamSparkLink.SlotIndex;
        pInData->Gle.PilotDens = AlzParam.AnalyzeParamSparkLink.PilotDens;
        pInData->Gle.BoardIndex = AlzParam.AnalyzeParamSparkLink.BoardIndex;
        pInData->Gle.PolarEncodePathNum = AlzParam.AnalyzeParamSparkLink.PolarEncodePathNum;
        pInData->Gle.PID = AlzParam.AnalyzeParamSparkLink.PID;
        pInData->Gle.Scramble = AlzParam.AnalyzeParamSparkLink.Scramble;
        //pInData->Gle.FreqRange = AlzParam.AnalyzeParamSparkLink.FreqRange;
        //pInData->Gle.ChannelType = AlzParam.AnalyzeParamSparkLink.ChannelType;
        pInData->Gle.MSeqNo = AlzParam.AnalyzeParamSparkLink.MSeqNo;
        pInData->Gle.SyncSource = AlzParam.AnalyzeParamSparkLink.SyncSource;
        memcpy(pInData->Gle.SyncSeq, AlzParam.AnalyzeParamSparkLink.SyncSeq, sizeof(AlzParam.AnalyzeParamSparkLink.SyncSeq));
        pInData->Gle.PayloadAlzMode = AlzParam.AnalyzeParamSparkLink.Payload_Analyze_Mode;
        pInData->Gle.MCS = AlzParam.AnalyzeParamSparkLink.MCS;
        //pInData->Gle.RRCFilter = AlzParam.AnalyzeParamSparkLink.Raised_Root_Cosine_Filter;
    }
    // WiSun
    if (AlzParam.Demode == WT_DEMOD_LRWPAN_OFDM || AlzParam.Demode == WT_DEMOD_LRWPAN_FSK || AlzParam.Demode == WT_DEMOD_LRWPAN_OQPSK)
    {
        pInData->mrOfdmOption = AlzParam.AnalyzeParamWiSun.Mr_OFDM_Option;
        pInData->phyOfdmInterleaving = AlzParam.AnalyzeParamWiSun.Phy_OFDM_Interleaving;

        pInData->phase_track_enable = AlzParam.AnalyzeParamWiSun.PhsCorrMode;
        pInData->channelest_enable = AlzParam.AnalyzeParamWiSun.ChEstimate;
        pInData->sym_tim_corr = AlzParam.AnalyzeParamWiSun.SynTimeCorr;
        pInData->freq_sync = AlzParam.AnalyzeParamWiSun.FreqSyncMode;
        pInData->amp_track_enable = AlzParam.AnalyzeParamWiSun.AmplTrack;
        pInData->SfoCompensation = AlzParam.AnalyzeParamWiSun.SfoCompensation;
        pInData->clockrate = AlzParam.AnalyzeParamWiSun.ClockRate;
        pInData->IQCompensation = AlzParam.AnalyzeParamWiSun.IQCompensation;
        pInData->freqBand = AlzParam.AnalyzeParamWiSun.FreqBand;
        pInData->mrFskDataRate = AlzParam.AnalyzeParamWiSun.DataRate;
        pInData->phyMrFskACPCalculationMode = AlzParam.AnalyzeParamWiSun.AcpCalMode;
        pInData->phyMrFskChannelSpacing = AlzParam.AnalyzeParamWiSun.ChannelSpacing;
        pInData->fskModulationIndex = AlzParam.AnalyzeParamWiSun.ModulationIndex;

        //WTLog::Instance().WriteLog(LOG_DEBUG, "【WISUN】mrOfdmOption = %d phyOfdmInterleaving = %d\n\n\n", pInData->mrOfdmOption, pInData->phyOfdmInterleaving);
    }
}

void Analysis::SetAlgMIMOAnalysisMode()
{
#define MIMO_ANALYSIS_DEBUG 0
    // 把WIFI分析参数中的MIMOAnalysisMode字段用于设置MIMO分析模式
    // 默认等于0=NxN MIMO analysis， 1 = single signal analysis
    if (m_AlzParam.WifiParam.MIMOAnalysisMode >= 1)
    {
        m_HistoryMIMOAnalysisMode = m_AlzParam.WifiParam.MIMOAnalysisMode;
        int AnalyzeStreamID = m_AlzParam.WifiParam.SpecialAnalyzeStream;

        if (AnalyzeStreamID >= m_RealChannelCount[0] || AnalyzeStreamID < 0)
        {
            AnalyzeStreamID = 0;
        }
        m_RxInData[0].iNumOfChIn = 1;
        m_RxInData[1].iNumOfChIn = 1;
#if MIMO_ANALYSIS_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "MIMOAnalysisMode >= 1 :m_RealChannelCount[0]=%d, m_AlzParam.WifiParam.SpecialAnalyzeStream=%d, m_AlzInDataCh0Bak=%d, AnalyzeStreamID=%d\n",
            m_RealChannelCount[0], m_AlzParam.WifiParam.SpecialAnalyzeStream, m_AlzInDataCh0Bak, AnalyzeStreamID); 
#endif
        if (false == m_AlzInDataCh0Bak && AnalyzeStreamID != 0)
        {
#if MIMO_ANALYSIS_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "copy Stream 0 to bak\n");
#endif
            m_AlzInDataCh0Bak = true;
            memcpy(&(m_AlzInDataCh0[0]), &(m_RxInData[0].aStChIn[0]), sizeof(RX_InDat));
            memcpy(&(m_AlzInDataCh0[1]), &(m_RxInData[1].aStChIn[0]), sizeof(RX_InDat));
        }

        if (AnalyzeStreamID != 0)
        {
#if MIMO_ANALYSIS_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "copy Stream %d to 0\n", AnalyzeStreamID);
#endif
            memcpy(&(m_RxInData[0].aStChIn[0]), &(m_RxInData[0].aStChIn[AnalyzeStreamID]), sizeof(RX_InDat));
            memcpy(&(m_RxInData[1].aStChIn[0]), &(m_RxInData[1].aStChIn[AnalyzeStreamID]), sizeof(RX_InDat));
        }
        else if (AnalyzeStreamID == 0 && true == m_AlzInDataCh0Bak)
        {
#if MIMO_ANALYSIS_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "copy Stream bak to 0\n");
#endif
            memcpy(&(m_RxInData[0].aStChIn[0]), &(m_AlzInDataCh0[0]), sizeof(RX_InDat));
            memcpy(&(m_RxInData[1].aStChIn[0]), &(m_AlzInDataCh0[1]), sizeof(RX_InDat));
        }
        m_RxInData[0].aStChIn[0].analyzeMode = 0; // 重置算法为第一次分析
        m_RxInData[1].aStChIn[0].analyzeMode = 0; // 重置算法为第一次分析
        SetIQImbReset();
    }
    else if (m_HistoryMIMOAnalysisMode == 1) // 开启过mimo做siso分析后，恢复mimo重复分析，需要恢复下相关数据
    {
#if MIMO_ANALYSIS_DEBUG
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_HistoryMIMOAnalysisMode == 1 :m_RealChannelCount[0]=%d, m_AlzInDataCh0Bak=%d\n",
            m_RealChannelCount[0], m_AlzInDataCh0Bak);
#endif
        m_HistoryMIMOAnalysisMode = 0;
        m_RxInData[0].iNumOfChIn = m_RealChannelCount[0];
        m_RxInData[1].iNumOfChIn = m_RealChannelCount[1];

        if (true == m_AlzInDataCh0Bak)
        {
#if MIMO_ANALYSIS_DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "copy Stream bak to 0\n");
#endif
            memcpy(&(m_RxInData[0].aStChIn[0]), &(m_AlzInDataCh0[0]), sizeof(RX_InDat));
            memcpy(&(m_RxInData[1].aStChIn[0]), &(m_AlzInDataCh0[1]), sizeof(RX_InDat));
        }
        m_RxInData[0].aStChIn[0].analyzeMode = 0; // 重置算法为第一次分析
        m_RxInData[1].aStChIn[0].analyzeMode = 0; // 重置算法为第一次分析
        SetIQImbReset();
    }
}

Analysis::Analysis()
{
    AlgReset::Instance().RegisterAnalysisHandle(this, bind(&Analysis::ResetAlgMemory, this));
    InitInData();
    m_AlzIQImbEnable = CalConf::Instance().GetVSAIqImbComp();
    DevConf::Instance().GetItemVal("VSAForceTimeDomainIqComp", m_ForceTimeDomainIq);
    m_AlzFlatnessEnable = CalConf::Instance().GetVSAFlatnessComp();

    for (int i = 0; i < MAX_SEGMENT_CNT; i++)
    {
        m_RxOutData[i] = nullptr;

        for (auto &Len : m_BufLen[i])
        {
            Len = 0;
        }
    }
}

Analysis::~Analysis()
{
    for (int i = 0; i < MAX_SEGMENT_CNT; i++)
    {
        if (m_RxOutData[i] != nullptr)
        {
            AlgReset::Instance().AlgGetReaderLockT();
            MIMO_WT_Algorithm_term(m_RxOutData[i], 1);
            AlgReset::Instance().AlgReleaseLockT();
            malloc_trim(0);
        }
    }
    AlgReset::Instance().DeleteAnalysisHandle(this);
}

void Analysis::InitInData()
{
    memset(m_RxInData, 0, sizeof(m_RxInData));
    m_ExtralAlzParam.Type = -1;
    for (int Seg = 0; Seg < MAX_SEGMENT_CNT; Seg++)
    {
        for (int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            m_RxInData[Seg].aStChIn[i].capturedata = nullptr;
            m_RxInData[Seg].aStChIn[i].IQImb_Amp   = 0;
            m_RxInData[Seg].aStChIn[i].IQImb_Phase = 0;
            m_RxInData[Seg].aStChIn[i].IDCOffset   = 0;
            m_RxInData[Seg].aStChIn[i].QDCOffset   = 0;
            m_RxInData[Seg].aStChIn[i].TimeSkew = 0;
            m_RxInData[Seg].aStChIn[i].AutoRangeFlag = FALSE;
            m_RxInData[Seg].aStChIn[i].freq_IQImb_amp = nullptr;
            m_RxInData[Seg].aStChIn[i].freq_IQImb_len = 0;
            m_RxInData[Seg].aStChIn[i].freq_IQImb_phase = nullptr;

            m_RxInData[Seg].aStChIn[i].adc_freq = MAX_SMAPLE_RATE;
            m_RxInData[Seg].aStChIn[i].gain = 0;
            m_RxInData[Seg].aStChIn[i].extgain = 0;
            m_RxInData[Seg].aStChIn[i].trig_pwr = 0;
            m_RxInData[Seg].aStChIn[i].trig_point = 0;
            m_RxInData[Seg].aStChIn[i].rf_center_freq = 2.412E9;
            m_RxInData[Seg].aStChIn[i].rf_channel = 1;
            m_RxInData[Seg].aStChIn[i].referenceLevel = 0;
            m_RxInData[Seg].aStChIn[i].powerOffset = NULL;
            memset(&m_RxInData[Seg].aStChIn[i].evmoffset, 0, sizeof(stEvmOffset));

            m_RxInData[Seg].aStChIn[i].demod_mode = WT_DEMOD_UNKNOW;
            m_RxInData[Seg].aStChIn[i].iqswap = WT_IQ_SWAP_DISABLED;
            m_RxInData[Seg].aStChIn[i].iqreversion = WT_IQ_IQReversion_DISABLED;

            m_RxInData[Seg].aStChIn[i].channelest_enable = WT_CH_EST_RAW_LONG;
            m_RxInData[Seg].aStChIn[i].amp_track_enable = WT_AMPL_TRACK_OFF;
            m_RxInData[Seg].aStChIn[i].phase_track_enable = WT_PH_CORR_SYM_BY_SYM;
            m_RxInData[Seg].aStChIn[i].sym_tim_corr = WT_SYM_TIM_ON;
            m_RxInData[Seg].aStChIn[i].freq_sync = WT_FREQ_SYNC_AUTO;

            m_RxInData[Seg].aStChIn[i].cck_dcremove_enable =  WT_DC_REMOVAL_OFF;
            m_RxInData[Seg].aStChIn[i].cck_phase_track_enable = WT_PH_CORR_11b_ON;
            m_RxInData[Seg].aStChIn[i].cck_equalizertaps = WT_EQ_OFF;
            m_RxInData[Seg].aStChIn[i].cck_evm_method = WT_11B_STANDARD_TX_ACC;

            m_RxInData[Seg].aStChIn[i].frequencyOffsetHz = 0;
            m_RxInData[Seg].aStChIn[i].FrameAutoDetection = WT_USER_DEFINED;
            m_RxInData[Seg].aStChIn[i].BT_DataRate = 0;
            m_RxInData[Seg].aStChIn[i].BT_PacketType = 0;
        }
    }
}

void Analysis::Clear(void)
{
    m_RxInData[0].iNumOfChIn = 0;
    m_RxInData[1].iNumOfChIn = 0;
    m_Alg_3GPP_VsaInInfo.RFInChanNum = 0;
    m_Alg_3GPP_List_VsaInInfo.RFInChanNum = 0;
    m_AnalyzeMode = 0;
    m_IsIQImbReset = IQ_IMB_NOT_NEED_RESET; // 默认不自动判断带宽
    m_AlzAll = false;
    m_BufPos = 0;
    m_AlzResult = WT_OK;                    //重新分析会重置分析结果状态
    m_SigFile.reset(nullptr);
}

void Analysis::SetAnalyzeMode(int AnalyzeMode)
{
    m_AnalyzeMode = AnalyzeMode;
}

void Analysis::SetIQImbReset(int IQImbReset)
{
    m_IsIQImbReset = IQImbReset;
}

int Analysis::IsAlzParamChanged(int Demode, void *Param, int Len)
{
    switch (Demode)
    {
    case WT_ALZ_PARAM_COMMON:
        if (Len == sizeof(AlzParamComm))
        {
            if (!(m_AlzParam.CommParam == *(AlzParamComm *)Param))
            {
                return true;
            }
        }

        break;

    case WT_ALZ_PARAM_FFT:
        if (Len == sizeof(AlzParamFFT))
        {
            if(!(m_AlzParam.FFTParam == *(AlzParamFFT *)Param) && m_AlzParam.Demode == WT_DEMOD_CW)
            {
                return true;
            }
        }

        break;

    case WT_ALZ_PARAM_WIFI:
        if (Len == sizeof(AlzParamWifi))
        {
            if(!(m_AlzParam.WifiParam == *(AlzParamWifi *)Param) && m_AlzParam.Demode == m_AlzParam.WifiParam.Demode)
            {
                return true;
            }
        }

        break;

    case WT_ALZ_PARAM_BT:
        if (Len == sizeof(AlzParamBT))
        {
            if(!(m_AlzParam.BTParam == *(AlzParamBT *)Param) && m_AlzParam.Demode == WT_DEMOD_BT)
            {
                return true;
            }
        }

        break;

    case WT_ALZ_PARAM_ZIGBEE:
        if (Len == sizeof(AlzParamZigBee))
        {
            if(!(m_AlzParam.ZigBeeParam == *(AlzParamZigBee *)Param) && m_AlzParam.Demode == WT_DEMOD_ZIGBEE)
            {
                return true;
            }
        }

        break;
    case WT_ALZ_PARAM_ZWAVE:
        if (Len == sizeof(AlzParamZwave))
        {
            if(!(m_AlzParam.ZwaveParam == *(AlzParamZwave *)Param) &&m_AlzParam.Demode == WT_DEMOD_ZWAVE)
            {
                return true;
            }
        }
        break;
    case WT_ALZ_PARAM_GLE:
        if (Len == sizeof(AlzParamSparkLink))
        {
            if(!(m_AlzParam.AnalyzeParamSparkLink == *(AlzParamSparkLink *)Param) && m_AlzParam.Demode == WT_DEMOD_GLE)
            {
                return true;
            }
        }
        break;
    case WT_ALZ_PARAM_3GPP:
        if (Len == sizeof(AlzParam3GPP))
        { 
            if(!(m_AlzParam.Alz3GPPParam == *(AlzParam3GPP *)Param) && IsAlg3GPPStandardType(m_AlzParam.Demode))
            {
                return true;
            }
        }
        break;
    default:
        return false;
    }
    return false;
}


int Analysis::SetAlzParam(int Demode, void *Param, int Len)
{
    int Ret = WT_ARG_ERROR;

    switch (Demode)
    {
    case WT_ALZ_PARAM_COMMON:
        if (Len == sizeof(AlzParamComm))
        {
            memcpy(&m_AlzParam.CommParam, Param, Len);
            Ret = WT_OK;
        }

        break;

    case WT_ALZ_PARAM_FFT:
        if (Len == sizeof(AlzParamFFT))
        {
            memcpy(&m_AlzParam.FFTParam, Param, Len);
            m_AlzParam.Demode = WT_DEMOD_CW;
            Ret = WT_OK;
        }

        break;

    case WT_ALZ_PARAM_WIFI:
        if (Len == sizeof(AlzParamWifi))
        {
            memcpy(&m_AlzParam.WifiParam, Param, Len);
            m_AlzParam.Demode = m_AlzParam.WifiParam.Demode;

#ifdef DEBUG
    WTLog::Instance().WriteLog(LOG_DEBUG, "#####Set AlzParam Demo = %d#####\n",m_AlzParam.Demode);
#endif
            Ret = WT_OK;
        }

        break;

    case WT_ALZ_PARAM_BT:
        if (Len == sizeof(AlzParamBT))
        {
            memcpy(&m_AlzParam.BTParam, Param, Len);
            m_AlzParam.Demode = WT_DEMOD_BT;
            Ret = WT_OK;
        }

        break;

    case WT_ALZ_PARAM_ZIGBEE:
        if (Len == sizeof(AlzParamZigBee))
        {
            memcpy(&m_AlzParam.ZigBeeParam, Param, Len);
            m_AlzParam.Demode = WT_DEMOD_ZIGBEE;
            Ret = WT_OK;
        }

        break;
    case WT_ALZ_PARAM_ZWAVE:
        if (Len == sizeof(AlzParamZwave))
        {
            memcpy(&m_AlzParam.ZwaveParam, Param, Len);
            m_AlzParam.Demode = WT_DEMOD_ZWAVE;
            Ret = WT_OK;
        }
        break;
    case WT_ALZ_PARAM_GLE:
        if (Len == sizeof(AlzParamSparkLink))
        {
            memcpy(&m_AlzParam.AnalyzeParamSparkLink, Param, Len);
            m_AlzParam.Demode = WT_DEMOD_GLE;
            Ret = WT_OK;
        }
        break;
    case WT_ALZ_PARAM_3GPP:
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_AlzParam.Standard = %d\n", (static_cast<AlzParam3GPP *>(Param))->Standard);
        if (Len == sizeof(AlzParam3GPP))
        {
            m_AlzParam.Alz3GPPParam = *static_cast<AlzParam3GPP *>(Param);
            m_AlzParam.Demode = m_AlzParam.Alz3GPPParam.Standard;
            WTLog::Instance().WriteLog(LOG_DEBUG, "m_AlzParam.Demode = %d\n", m_AlzParam.Demode);
            Ret = WT_OK;
        }
        break;
    case WT_ALZ_PARAM_WSUN:
        if (Len == sizeof(AlzParamWiSun))
        {
            m_AlzParam.AnalyzeParamWiSun = *static_cast<AlzParamWiSun*>(Param);
            m_AlzParam.Demode = m_AlzParam.AnalyzeParamWiSun.Demode;
            Ret = WT_OK;
        }
        break;
    default:
        break;
    }

    return Ret;
}

int Analysis::GetAlzParam(const void **Param, int &Len)
{
    *Param = &m_AlzParam;
    Len = sizeof(Len);

    return WT_OK;
}

int Analysis::SetCmimoRefFile(const string &File)
{
    m_RxInData[0].aStChIn[0].scene_model = CMIMOModel;

#ifdef _SISO_MODE
    (void)File;
    m_RxInData[0].aStChIn[0].cmimo_refDat = nullptr;
    m_RxInData[0].aStChIn[0].cmimo_refDat_size = 0;

    m_RxInData[1].aStChIn[0].cmimo_refDat = nullptr;
    m_RxInData[1].aStChIn[0].cmimo_refDat_size = 0;
#else
    if (m_RefFile == File && m_RefData.get() != nullptr)
    {
        return WT_OK;
    }

    m_RefData.reset(new (std::nothrow) ReadFile(File));
    if (m_RefData == nullptr || m_RefData->GetFileBuf() == nullptr)
    {
        m_RefData.reset(nullptr);
        return WT_OPEN_FILE_FAILED;
    }

    m_RxInData[0].aStChIn[0].cmimo_refDat = (Complex *)(m_RefData->GetFileBuf());
    m_RxInData[0].aStChIn[0].cmimo_refDat_size = m_RefData->GetFileSize() / sizeof(Complex);

    m_RxInData[1].aStChIn[0].cmimo_refDat = (Complex *)(m_RefData->GetFileBuf());
    m_RxInData[1].aStChIn[0].cmimo_refDat_size = m_RefData->GetFileSize() / sizeof(Complex);
#endif
    return WT_OK;
}

void Analysis::SetData(const DataBufInfo &DataBuf, int Segment, const VsaParam &Param, const Rx_Parm &CalParam, const ExtendEVMStu &ExtendEvm, const std::vector<DataBufInfo> &MultiData, int Spect500MFlag, int Capture500cnt, int Offset500M)
{
    //分析采样的数据时清除文件数据
    if (m_SigFile != nullptr)
    {
        m_SigFile.reset(nullptr);
    }

    int Chain = m_RxInData[Segment].iNumOfChIn++;
    RX_InDat *pInData = &m_RxInData[Segment].aStChIn[Chain];
    ClrCalParam(pInData);   //清除校准参数

    pInData->Extend_EVM = ExtendEvm.IterativeEVM == Iterative_EVM_ON ? Iterative_EVM_ON : Iterative_EVM_OFF;
    pInData->CC_EVM = ExtendEvm.CcEVM == CC_EVM_ON ? CC_EVM_ON : CC_EVM_OFF;
    pInData->SNC_EVM = ExtendEvm.SncEVM == SNC_EVM_ON ? SNC_EVM_ON : SNC_EVM_OFF;
    
    if(pInData->SNC_EVM == SNC_EVM_ON && CalParam.rx_spec_flat_comp_parm.ns_comp_len > 0)
    {
        pInData->noise_response = const_cast<double*>(CalParam.rx_spec_flat_comp_parm.ns_comp_gain);
        pInData->noise_response_len = CalParam.rx_spec_flat_comp_parm.ns_comp_len;
        WTLog::Instance().WriteLog(LOG_DEBUG, "pInData->noise_response_len = %d\n", pInData->noise_response_len);

    }
    else if(pInData->CC_EVM == CC_EVM_ON)
    {
        //CC_EVM模式下，VSA参数和分析参数与8080模式无异，改动在于数据放在MIMO(1,2)流，关闭8080算法分析使能。
        pInData = &m_RxInData[0].aStChIn[Segment];
        pInData->vht80_80MEnable = 0;
    }

    if (Param.Type == TEST_SWITCHED_MIMO)
    {
        pInData->scene_model = SwitchMIMO;
    }
    else
    {
        pInData->scene_model = Normal;
    }
    pInData->vht80_80MEnable = Param.IsAC8080() ? 1 : 0;

    pInData->capturedata = DataBuf.Buf.get();
    pInData->capturedata_format = enDataFormat_Int16;
    pInData->capturecnt = round(DataBuf.DataLen / sizeof(stIQDat));
    pInData->ScaleTo = AlgEnv::Instance().GetScaleVal(enDataFormat_Int16);
    pInData->adc_freq = round(Param.SamplingFreq);
    pInData->trig_pwr = Param.TrigLevel;
    pInData->referenceLevel = Param.Ampl;

    if (Param.IsAC8080() && Segment == 1)
    {
        if (Param.IsUseDualParam())
        {
            pInData->trig_pwr = Param.TrigLevel2;
            pInData->referenceLevel = Param.Ampl2;
        }
        pInData->rf_center_freq = Param.Freq2;
        //算法是当增益处理，参数里面实际是线衰，所以要取负数
        pInData->extgain = -Param.ExtGain2 + AlgEnv::Instance().m_VsaPwrBaseDelta;
        m_Comb8080To160 = Param.Is160();
    }
    else
    {
        m_Comb8080To160 = false;
        pInData->rf_center_freq = Param.Freq;
        //算法是当增益处理，参数里面实际是线衰，所以要取负数
        pInData->extgain = -Param.ExtGain + AlgEnv::Instance().m_VsaPwrBaseDelta;
    }

    pInData->vht80_80MEnable = (Param.IsAC8080() && Param.Type != TEST_SWITCHED_MIMO) ? 1 : 0;

    //规避处理，如果是8080时，强制配置智能模式为user define，规格暂时不支持8080开启自动识别功能
    if(pInData->vht80_80MEnable == 1)
    {
        pInData->FrameAutoDetection = WT_USER_DEFINED;
    }

    //判断80+80哪个中心频率大的，设置算法输入的segment为1；小的频率，设置输入的segment为0
    if(pInData->vht80_80MEnable)
    {
        double MaxFreq = 0;
        MaxFreq = max(Param.Freq, Param.Freq2);
        pInData->currentSeg80_80M = Basefun::CompareDouble(pInData->rf_center_freq, MaxFreq) >= 0 ? 1 : 0;
    }

    pInData->gain = CalParam.rx_gain_parm.rx_sw_gain.final_gain;

    if (m_AlzFlatnessEnable)
    {
        pInData->bb_response = const_cast<double*>(CalParam.rx_spec_flat_comp_parm.bb_comp_gain);
        pInData->bb_response_len = CalParam.rx_spec_flat_comp_parm.bb_comp_len;
        pInData->rf_response = const_cast<double*>(CalParam.rx_spec_flat_comp_parm.rf_comp_gain);
        pInData->rf_response_len = CalParam.rx_spec_flat_comp_parm.rf_comp_len;
    }

    if (m_AlzDuplexNoiseFlag)
    {
        // TODO WLAN 双工泄露补偿
    }

    //缓存两份IQ IMB数据
    m_IsIQImbReset = IQ_IMB_NOT_NEED_RESET;
    m_CalIQImbData[Segment][Chain].rx_iq_imb_parm = CalParam.rx_iq_imb_parm;
    m_CalIQImbData[Segment][Chain].rx_iq_imb_parm_160m = CalParam.rx_iq_imb_parm_160m;
    m_CalIQImbData[Segment][Chain].rx_iq_imb_parm_320m = CalParam.rx_iq_imb_parm_320m;
    m_CalIQImbData[Segment][Chain].rx_iq_image_parm = CalParam.rx_iq_image_parm;
    m_CalIQImbData[Segment][Chain].rx_iq_image_parm_back = CalParam.rx_iq_image_parm;
    if (m_StaticIQParam[Segment].Valid)
    {
        pInData->IQImb_Amp = m_StaticIQParam[Segment].IQAmpl;
        pInData->IQImb_Phase = m_StaticIQParam[Segment].IQPhase;
        pInData->TimeSkew = m_StaticIQParam[Segment].TimeSkew;
    }
    else if (m_AlzIQImbEnable)
    {
        int IQParamType = WT_IQ_IMB_PARAM_80M;
        if (DemodFun::IsWifiDemod(m_AlzParam.Demode))
        {
            if (m_AlzParam.WifiParam.AutoDetect == WT_USER_DEFINED)
            {
                int Bw = DemodFun::GetSigBw(m_AlzParam.Demode);
                IQParamType = Bw == WT_BW_160M
                                  ? WT_IQ_IMB_PARAM_160M
                                  : (Bw == WT_BW_320M
                                         ? WT_IQ_IMB_PARAM_320M
                                         : WT_IQ_IMB_PARAM_80M);
            }
            else
            {
                m_IsIQImbReset = IQ_IMB_NEED_RESET; //自动判断带宽
                IQParamType = m_CalIQImbData[Segment][Chain].CurIQParamType;
            }
        }
        if (IQParamType == WT_IQ_IMB_PARAM_80M)
        {
            m_CalIQImbData[Segment][Chain].CurIQParamType = WT_IQ_IMB_PARAM_80M;
            pInData->IQImb_Amp = CalParam.rx_iq_imb_parm.gain_imb;
            pInData->IQImb_Phase = CalParam.rx_iq_imb_parm.quad_err;
            pInData->TimeSkew = CalParam.rx_iq_imb_parm.timeskew;
        }
        else if (IQParamType == WT_IQ_IMB_PARAM_160M)
        {
            m_CalIQImbData[Segment][Chain].CurIQParamType = WT_IQ_IMB_PARAM_160M;
            pInData->IQImb_Amp = CalParam.rx_iq_imb_parm_160m.gain_imb;
            pInData->IQImb_Phase = CalParam.rx_iq_imb_parm_160m.quad_err;
            pInData->TimeSkew = CalParam.rx_iq_imb_parm_160m.timeskew;
        }
        else
        {
            m_CalIQImbData[Segment][Chain].CurIQParamType = WT_IQ_IMB_PARAM_320M;
            pInData->IQImb_Amp = CalParam.rx_iq_imb_parm_320m.gain_imb;
            pInData->IQImb_Phase = CalParam.rx_iq_imb_parm_320m.quad_err;
            pInData->TimeSkew = CalParam.rx_iq_imb_parm_320m.timeskew;
        }

        pInData->freq_IQImb_amp = m_CalIQImbData[Segment][Chain].rx_iq_image_parm.freq_gain_imb;
        pInData->freq_IQImb_phase = m_CalIQImbData[Segment][Chain].rx_iq_image_parm.freq_quad_err;
        pInData->freq_IQImb_len = m_ForceTimeDomainIq ? 0 : m_CalIQImbData[Segment][Chain].rx_iq_image_parm.freq_Iqimb_len;
    }
    else
    {
        pInData->IQImb_Amp = 0;
        pInData->IQImb_Phase = 0;
        pInData->TimeSkew = 0;
        pInData->freq_IQImb_len = 0;
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetData: IQType %d, pInData->IQImb_Amp%f, pInData->IQImb_Phase%f, pInData->TimeSkew%f\n",
        m_CalIQImbData[Segment][Chain].CurIQParamType, pInData->IQImb_Amp, pInData->IQImb_Phase, pInData->TimeSkew);

    //500M 频谱功能分析参数
    pInData->Spec500MFlag = Spect500MFlag;
    if(Spect500MFlag == 1)
    {
        pInData->capturedataleft = MultiData[1].Buf.get();
        pInData->capturedataright = MultiData[2].Buf.get();
        pInData->capturedataleft2 = MultiData[3].Buf.get();
        pInData->capturedataright2 = MultiData[4].Buf.get();
        pInData->capture500cnt = Capture500cnt;
        pInData->Offset500M = Offset500M;
    }

    //配合MIMOAnalysisMode为1功能使用
    m_RealChannelCount[Segment] = m_RxInData[Segment].iNumOfChIn;
    m_AlzInDataCh0Bak = false;
    m_HistoryMIMOAnalysisMode = false;
}

int Analysis::CheckBT51Lic(const std::string ResultType)
{
   int iRet = WT_OK;
   std::vector <string> Bt51ResultStringVec;

//    Bt51ResultStringVec.push_back(WT_RES_BT_BLEMAPPERS);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTEINFO);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTEType);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_DURATIONT);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_PWR_AVG);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_PWR_PEAK);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_PWR_SUB_AVG);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FSI_MAX);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FSI_MIN);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FS1_SUB_FP);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FSI_SUB_F0_MAX);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FSI_SUB_FSI3_MAX);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FWR_REF_AVG);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FWR_REF_Dev);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FWR_REF_Dev_MAX);
   Bt51ResultStringVec.push_back(WT_RES_BLE_CTE_FWR_AVG_SLOT);

//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F1_AVG);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F2_MAX);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F2_AVG);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F2_MAX);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F1_MIN);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F2_MIN);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F1_99p9_PRECENT);
//    Bt51ResultStringVec.push_back(WT_RES_BT_DELTA_F2_99p9_PRECENT);
//    Bt51ResultStringVec.push_back(WT_RES_BT_BLE_FnMax);
//    Bt51ResultStringVec.push_back(WT_RES_BT_BLE_F0FnMax);
//    Bt51ResultStringVec.push_back(WT_RES_BT_BLE_Delta_F1F0);
//    Bt51ResultStringVec.push_back(WT_RES_BT_BLE_F0Fn5_Max);
//    Bt51ResultStringVec.push_back(WT_RES_BT_BLE_Delta_F0F3);
//    Bt51ResultStringVec.push_back(WT_RES_BT_BLE_Delta_F0FN3);
//    Bt51ResultStringVec.push_back(WT_RES_BT_F0FN_AVG);

   for(auto &Item : Bt51ResultStringVec)
   {
       if(strcasecmp(Item.c_str(), ResultType.c_str()) == 0)
       {
           if(License::Instance().CheckBusinessLicItem(WT_BT5_1) != WT_OK)
           {
               iRet = WT_LIC_NOT_EXIST;
               WTLog::Instance().LOGERR(iRet, "BT5.1 license not exist");
           }
           break;
       }
   }
   return iRet;
}

int Analysis::CheckMacLic(StOutInfo *pRxOut)
{
    int iRet = WT_OK;
    int Demo = pRxOut->aStCh[0].local->demod_mode;
    do
    {
        //mac相关内容，需要则判断相关lic
        if(WT_DEMOD_11BE_20M <= Demo && Demo <= WT_DEMOD_11BE_160_160M)
        {
            if(License::Instance().CheckBusinessLicItem(WT_MAC_BE) != WT_OK)
            {
                iRet = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(iRet, "WT_MAC_BE license not exist");
                break;
            }
        }
        else if(WT_DEMOD_11AX_20M <= Demo && Demo <= WT_DEMOD_11AX_160_160M)
        {
            if(License::Instance().CheckBusinessLicItem(WT_MAC_AX) != WT_OK)
            {
                iRet = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(iRet, "WT_MAC_AX license not exist");
                break;
            }
        }
        else
        {
            if(License::Instance().CheckBusinessLicItem(WT_MAC_AC) != WT_OK)
            {
                iRet = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(iRet, "WT_MAC_AC license not exist");
                break;
            }
        }
    }while(0);

    return iRet;
}

int Analysis::CheckDigModeLic(StOutInfo *pRxOut)
{
    int iRet = WT_OK;
    if(DigModeLib::Instance().IsDigMode())  //是数字模式才判断
    {
        int Demode = pRxOut->aStCh[0].local->demod_mode;
        if(WT_DEMOD_11BE_20M <= Demode && Demode <= WT_DEMOD_11BE_160_160M)
        {
            if(License::Instance().CheckBusinessLicItem(WT_DIQ_BE) != WT_OK)
            {
                iRet = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(iRet, "WT_DIQ_BE license not exist");
            }
        }
        else if(WT_DEMOD_11AX_20M <= Demode && Demode <= WT_DEMOD_11AX_160_160M)
        {
            if(License::Instance().CheckBusinessLicItem(WT_DIQ_AX) != WT_OK)
            {
                iRet = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(iRet, "WT_DIQ_AX license not exist");
            }
        }
        else
        {
            if(License::Instance().CheckBusinessLicItem(WT_DIQ_AC) != WT_OK)
            {
                iRet = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(iRet, "WT_DIQ_AC license not exist");
            }
        }
    }
    return iRet;
}

void Analysis::CheckAlgDemode(StOutInfo *pRxOut)
{
    do
    {
        if (pRxOut->aStCh[0].local != nullptr)
        {
            //开启帧自动检测后需要判断检测出来的帧license是否支持，若不支持则将结果帧标注为不识别
            if (CheckDemodLic(pRxOut->aStCh[0].local->demod_mode))
            {
                if(CheckDigModeLic(pRxOut) != WT_OK)
                {
                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                    break;
                }

                if(pRxOut->frametype != WT_DEMOD_SISO && pRxOut->iNumOfCh > 1)  //如果不是siso，且流数大于1，需要判断mimo lic
                {
                    if(License::Instance().CheckBusinessLicItem(WT_WIFI_MIMO) != WT_OK)
                    {
                        WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_WIFI_MIMO license not exist");
                        pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                        break;
                    }

                    if(pRxOut->iNumOfCh > 8)
                    {
                        if(License::Instance().CheckBusinessLicItem(WT_WIFI_MAS_MIMO) != WT_OK)
                        {
                            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_WIFI_MAS_MIMO license not exist");
                            pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                            break;
                        }
                    }
                }

                pRxOut->aStCh[0].demod_mode = pRxOut->aStCh[0].local->demod_mode;
                //tb tf, (8*8以上)16*16 license的判断
                if (pRxOut->SISOFrm != nullptr)
                {
                    switch (pRxOut->aStCh[0].demod_mode)
                    {
                        case WT_DEMOD_11AG:
                        {
                            //WTLog::Instance().WriteLog(LOG_DEBUG, "11ag tf= %d\n",pRxOut->SISOFrm->Db11ag.TFvalidflag);
//                            if (pRxOut->SISOFrm->Db11ag.TFvalidflag == 1)
//                            {
//                                if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AC) != WT_OK)
//                                {
//                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "TF license not exist");
//                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
//                                }
//                            }
                            break;
                        }
                        case WT_DEMOD_11N_20M:
                        case WT_DEMOD_11N_40M:
                        {
                            //WTLog::Instance().WriteLog(LOG_DEBUG, "11n pRxOut->frametype=%d,tf=%d\n",pRxOut->frametype,pRxOut->SISOFrm->Db11N.TFvalidflag);
//                            if (pRxOut->frametype == WT_DEMOD_SISO && pRxOut->SISOFrm->Db11N.TFvalidflag == 1)
//                            {
//                                if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AC) != WT_OK)
//                                {
//                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "TF license not exist");
//                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
//                                }
//                            }
                            break;
                        }
                        case WT_DEMOD_11AC_20M:
                        case WT_DEMOD_11AC_40M:
                        case WT_DEMOD_11AC_80M:
                        case WT_DEMOD_11AC_160M:
                        case WT_DEMOD_11AC_80_80M:
                        {
                            if (pRxOut->frametype == WT_DEMOD_SISO)
                            {
//                                if(pRxOut->SISOFrm->Db11ac.TFvalidflag == 1)
//                                if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AC) != WT_OK)
//                                {
//                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "TF license not exist");
//                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
//                                }
                            }
                            else	//mimo
                            {
                                if(pRxOut->aStFrm11ac->CrossChac.MuMimoFlag == 1)
                                {
                                    if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                                    {
                                        WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MUMIMO license not exist");
                                        pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                    }

                                    if(pRxOut->iNumOfCh > 8)
                                    {
                                        if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                                        {
                                            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MAS_MUMIMO license not exist");
                                            pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                        }
                                    }
                                }
                            }

                            //WTLog::Instance().WriteLog(LOG_DEBUG, "11ac pRxOut->iNumOfCh=%d,pRxOut->frametype=%d,tfflag=%d\n",pRxOut->iNumOfCh,pRxOut->frametype,pRxOut->SISOFrm->Db11ac.TFvalidflag);
                            break;
                        }
                        case WT_DEMOD_11AX_20M:
                        case WT_DEMOD_11AX_40M:
                        case WT_DEMOD_11AX_80M:
                        case WT_DEMOD_11AX_160M:
                        case WT_DEMOD_11AX_80_80M:
                        case WT_DEMOD_11AX_160_160M:
                        {
                            int PPDUFormat = -1;

                            if (pRxOut->frametype == WT_DEMOD_SISO)
                            {
                                PPDUFormat = pRxOut->SISOFrm->Db11ax.PPDUFormat;
                            }
                            else
                            {
                                PPDUFormat = pRxOut->aStFrm11ax->CrossChax.PPDUFormat;
                            }
                            //WTLog::Instance().WriteLog(LOG_DEBUG, "##Ppduformat = %d,streamcnt=%d,frametype=%d,tfflag=%d\n", PPDUFormat,pRxOut->iNumOfCh, pRxOut->frametype, pRxOut->SISOFrm->Db11ax.he_sig_common.TFvalidflag);

                            if(HE_MU == PPDUFormat && pRxOut->frametype != WT_DEMOD_SISO && pRxOut->aStFrm11ax->CrossChax.he_sig_common.mu_mimo_flag == 1)
                            {
                                if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                                {
                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MUMIMO license not exist");
                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                }

                                if(pRxOut->iNumOfCh > 8)
                                {
                                    if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                                    {
                                        WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MAS_MUMIMO license not exist");
                                        pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                    }
                                }
                            }
//                            if(HE_TriggerBase == PPDUFormat)
//                            {
//                                if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AX) != WT_OK)
//                                {
//                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MAC_INTER_AX license not exist");
//                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
//                                }
//                            }
//                            if (pRxOut->frametype == WT_DEMOD_SISO && pRxOut->SISOFrm->Db11ax.he_sig_common.TFvalidflag == 1)
//                            {
//                                if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_AX) != WT_OK)
//                                {
//                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "TF license not exist");
//                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
//                                }
//                            }
                            break;
                        }
                        case WT_DEMOD_11BE_20M:
                        case WT_DEMOD_11BE_40M:
                        case WT_DEMOD_11BE_80M:
                        case WT_DEMOD_11BE_160M:
                        case WT_DEMOD_11BE_80_80M:
                        case WT_DEMOD_11BE_160_160M:
                        {
                            int PPDUFormat = pRxOut->aStFrm11be->CrossChbe.PPDUFormat;
                            if(pRxOut->aStFrm11be->CrossChbe.MuMimoFlag == 1)
                            {
                                if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                                {
                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MUMIMO license not exist");
                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                }

                                if(pRxOut->iNumOfCh > 8)
                                {
                                    if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                                    {
                                        WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MAS_MUMIMO license not exist");
                                        pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                    }
                                }
                            }
                            if(2 == PPDUFormat)     //tb
                            {
                                if(License::Instance().CheckBusinessLicItem(WT_MAC_INTER_BE) != WT_OK)
                                {
                                    WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "WT_MAC_INTER_BE license not exist");
                                    pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                                }
                            }
                            break;
                        }
                        default:
                            break;
                    }
//                    if(IsMuMimo(pRxOut))
//                    {
//                        if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
//                        {
//                            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "MU_mimo license not exist");
//                            pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
//                        }
//                    }
                }

            }
            else
            {
                pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
            }
        }
        else
        {
            pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
        }
    }while(0);

    //如果demo为WT_DEMOD_UNKNOW，证明lic无效，清空8080合并结果内容
    if(pRxOut->aStCh[0].demod_mode == WT_DEMOD_UNKNOW)
    {
        m_AlzResult = WT_LIC_NOT_EXIST; //记录下分析不出来的原因
        if(m_8080Rslt != nullptr)
        {
            for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
            {
                m_8080Rslt->m_Result[i].DemodMode = WT_DEMOD_UNKNOW;
                memset((char *)&m_8080Rslt->m_Result[i].Evm, 0, sizeof(m_8080Rslt->m_Result[i].Evm));
            }
        }
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "Check lic final pRxOut->aStCh[0].demod_mode =%d\n",pRxOut->aStCh[0].demod_mode);
}

int Analysis::AlzFrameData(int FrameId, int IsFilter)
{
    int Ret = WT_OK;

    AlgReset::ReaderLock AlgReaderLock; //分析过程不允许重置算法内存
    if (!CheckDemodLic(m_AlzParam.Demode))
    {
        WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "demode license not exist");
        return WT_LIC_NOT_EXIST;
    }

    int SegmentNum = GetSegmentNum();
    for (int Seg = 0; Seg < SegmentNum; Seg++)
    {
        int ChainNum = 0;
#ifdef CMIMO_MIMO
        if(m_RxInData[Seg].aStChIn[0].scene_model == CMIMOModel)
        {
            ChainNum = MAX_NUM_OF_CHNNEL;   //cmimo输入只有一流，输入要扩展结果为多流，目前最多有4流，先全部申请好内存
        }
        else
#endif
        {
            ChainNum = m_RxInData[Seg].iNumOfChIn;
        }
        for (int i = 0; i < ChainNum; i++)
        {
            SetAlgVsaParam(&m_RxInData[Seg].aStChIn[i], m_AlzParam);
            SetAlgExtralParam(&m_RxInData[Seg].aStChIn[i], m_ExtralAlzParam);
            m_RxInData[Seg].aStChIn[i].analyzeFrameIndex = FrameId;

            Ret = AllocBuf(Seg, i);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }
    }

    SetAlgMIMOAnalysisMode();
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", frame ID = " << FrameId << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", m_RxInData demod_mode = " << m_RxInData[0].aStChIn[0].demod_mode << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", m_RxInData adc freq = " << m_RxInData[0].aStChIn[0].adc_freq << endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", m_RxInData capturecnt = " << m_RxInData[0].aStChIn[0].capturecnt << endl;

    if (m_RxInData[0].aStChIn[0].capturecnt == 0)
    {
        WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "not capture data yet");
        return WT_NO_SIG_DATA;
    }

    if (SegmentNum == 2)        //8080分析
    {
        if (m_8080Rslt == nullptr)
        {
            m_8080Rslt.reset(new Composite8080Result);
        }
        if (m_8080Rslt != nullptr)
        {
            m_8080Rslt->Reset();
            AlgReset::Instance().AlgGetReaderLockT();
            Ret = MIMO_WT_Algorithm_8080_main(m_RxInData, m_RxOutData[0], m_RxOutData[1], m_8080Rslt->m_Result);
            AlgReset::Instance().AlgReleaseLockT();
        }
    }
    else                        //非8080
    {
        AlgReset::Instance().AlgGetReaderLockT();
        Ret = MIMO_WT_Algorithm_main(&m_RxInData[0], m_RxOutData[0]);
        AlgReset::Instance().AlgReleaseLockT();
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", m_RxOutData demod_mode = " << m_RxOutData[0]->aStCh[0].local->demod_mode << endl;

    if (Ret == WT_OK)
    {
        CheckAlgDemode(m_RxOutData[0]);
    }
    else
    {
        WTLog::Instance().LOGERR(WT_ALG_BASE_ERROR + Ret, "AlzData MIMO_WT_Algorithm_main failed");
        return WT_ALG_BASE_ERROR + Ret;
    }

    do
    {
        if (IsFilter)
        {
            Ret = FrameFilter();
            RetBreak(Ret, "AlzData FrameFilter failed");
        }

        //仅第一次重新分析时需要更新IQ IMB数据
        if (m_IsIQImbReset == IQ_IMB_NEED_RESET)
        {
            m_AlzAll = Ret == WT_OK; //判断OBW，需先配置为分析完成
            Ret = UpdateImbAndReAnalyze();
            RetBreak(Ret, "AlzData UpdateImbAndReAnalyze failed");
            m_IsIQImbReset = IQ_IMB_ALREAD_RESET;
        }
        else
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Do not need reset iq imb data and analyze\n");
        }
    } while (0);

    m_FrameId = FrameId;
    m_AlzAll = Ret == WT_OK;
    return Ret;
}

int Analysis::FrameFilter()
{
    int Ret = WT_OK;
    AlgReset::ReaderLock AlgReaderLock; //分析过程不允许重置算法内存
    if (m_ResultFilters.IsEnable) //帧过滤处理
    {
        int SegmentNum = GetSegmentNum();
        for (int Seg = 0; Seg < SegmentNum; Seg++)
        {
            int IsWifi = DemodFun::IsWifiDemod(m_RxOutData[Seg]->aStCh[0].demod_mode);
            if (IsWifi && m_RxInData[Seg].aStChIn[0].scene_model != CMIMOModel)
            {
                // cmimo输入只有一流，输入要扩展结果为多流，目前最多有4流，先全部申请好内存
#ifdef CMIMO_MIMO
                int ChainNum = m_RxInData[Seg].aStChIn[0].scene_model == CMIMOModel ? MAX_NUM_OF_CHNNEL : m_RxInData[Seg].iNumOfChIn;
#else
                int ChainNum = m_RxInData[Seg].iNumOfChIn;
#endif
                int FrameCnt = m_RxOutData[Seg]->aStCh[0].pkg.frmcnt;

                if(FrameCnt == 0)   //没有检测到帧时，直接返回没找到符合帧错误
                {
                    m_AlzResult = WT_ARG_ERROR;
                    break;
                }
                for (int CurAlzIndex = 0; CurAlzIndex < FrameCnt; CurAlzIndex++)
                {
                    for (int i = 0; i < ChainNum; i++)
                    {
                        m_RxInData[Seg].aStChIn[i].analyzeFrameIndex = CurAlzIndex;
                    }

                    if (SegmentNum == 2) // 8080分析
                    {
                        if (m_8080Rslt != nullptr)
                        {
                            m_8080Rslt->Reset();
                            AlgReset::Instance().AlgGetReaderLockT();
                            Ret = MIMO_WT_Algorithm_8080_main(m_RxInData, m_RxOutData[0], m_RxOutData[1], m_8080Rslt->m_Result);
                            AlgReset::Instance().AlgReleaseLockT();
                        }
                    }
                    else
                    {
                        AlgReset::Instance().AlgGetReaderLockT();
                        Ret = MIMO_WT_Algorithm_main(&m_RxInData[Seg], m_RxOutData[Seg]);
                        AlgReset::Instance().AlgReleaseLockT();
                    }

                    if (Ret == WT_OK)
                    {
                        CheckAlgDemode(m_RxOutData[Seg]);
                    }
                    else
                    {
                        WTLog::Instance().LOGERR(WT_ALG_BASE_ERROR + Ret, "AlzData MIMO_WT_Algorithm_main failed");
                        return WT_ALG_BASE_ERROR + Ret;
                    }

                    for (int i = 0; i < m_RxOutData[Seg]->iNumOfCh; i++)
                    {
                        m_AlzResult = FiltFrameResult(i, Seg);
                        if (m_AlzResult != WT_OK)
                        {
                            break;  //退出流循环
                        }
                    }
                    if (WT_OK == Ret && m_AlzResult == WT_OK)
                    {
                        break;  //退出帧数循环
                    }
                }
            }
        }
        if(m_AlzResult != WT_OK)    //帧过滤没找到符合的帧，把demo置为无效~
        {
            for (int Seg = 0; Seg < SegmentNum; Seg++)
            {
                if (m_RxOutData[Seg]->aStCh[0].local != nullptr)
                {
                    m_RxOutData[Seg]->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
                }
            }
        }
    }
    return Ret;
}

int Analysis::UpdateImbAndReAnalyze()
{
#define SPECTRUM_OBW_320M_THRESHOLD (300 * MHz) // 320判断阈值，依据《WT-448 320M imb调整 - 1.0.doc》
#define SPECTRUM_OBW_160M_THRESHOLD (150 * MHz) // 160判断阈值，依据《WT-448 imb 校准调整讨论》
#define DebugPrintEvm 0

    int NeedReAnalynze = false;

    // Lambda函数
    auto SetIQImbParam = [&](int Segment, int Chain, int IQParamType) -> int
    {
        CalIQImbData &CalParam = m_CalIQImbData[Segment][Chain];
        if (!m_StaticIQParam[Segment].Valid &&
            m_AlzIQImbEnable &&
            CalParam.CurIQParamType != IQParamType)
        {
            Rx_Iq_Imb_Parm &IQImbParm = (IQParamType == WT_IQ_IMB_PARAM_320M)
                                            ? CalParam.rx_iq_imb_parm_320m
                                            : ((IQParamType == WT_IQ_IMB_PARAM_160M)
                                                   ? CalParam.rx_iq_imb_parm_160m
                                                   : CalParam.rx_iq_imb_parm);

            Rx_Iq_Imb_Parm &CurIQImbParm = (CalParam.CurIQParamType == WT_IQ_IMB_PARAM_320M)
                                               ? CalParam.rx_iq_imb_parm_320m
                                               : ((CalParam.CurIQParamType == WT_IQ_IMB_PARAM_160M)
                                                      ? CalParam.rx_iq_imb_parm_160m
                                                      : CalParam.rx_iq_imb_parm);

            if (Basefun::CompareDouble(CurIQImbParm.gain_imb, IQImbParm.gain_imb) ||
                Basefun::CompareDouble(CurIQImbParm.quad_err, IQImbParm.quad_err) ||
                Basefun::CompareDouble(CurIQImbParm.timeskew, IQImbParm.timeskew))
            {
                CalParam.CurIQParamType = IQParamType;
                RX_InDat &pInData = m_AlzParam.WifiParam.MIMOAnalysisMode >= 1
                                        ? m_RxInData[Segment].aStChIn[0]
                                        : m_RxInData[Segment].aStChIn[Chain];
                pInData.IQImb_Amp = IQImbParm.gain_imb;
                pInData.IQImb_Phase = IQImbParm.quad_err;
                pInData.TimeSkew = IQImbParm.timeskew;
                pInData.freq_IQImb_amp = CalParam.rx_iq_image_parm_back.freq_gain_imb;
                pInData.freq_IQImb_phase = CalParam.rx_iq_image_parm_back.freq_quad_err;
                pInData.freq_IQImb_len = m_ForceTimeDomainIq ? 0 : CalParam.rx_iq_image_parm_back.freq_Iqimb_len;
                pInData.analyzeMode = 0;
                NeedReAnalynze = true;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetIQImbParam:" << Pout(IQParamType) << Pout(pInData.IQImb_Amp) << Pout(pInData.IQImb_Phase) << Pout(pInData.TimeSkew) << std::endl;
                return WT_OK;
            }
        }
        return WT_ARG_ERROR;
    };

    int Ret = WT_OK;
    int DataSize = 0;
    int DataType = 0;
    void *pData = nullptr;
    int Segment = GetSegmentNum();
    for (int Seg = 0; Seg < Segment; Seg++)
    {
        int AnalyzeStreamID = m_AlzParam.WifiParam.SpecialAnalyzeStream;
        if (AnalyzeStreamID >= m_RealChannelCount[Seg] || AnalyzeStreamID < 0)
        {
            AnalyzeStreamID = 0;
        }
        for (int i = 0; i < m_RxInData[0].iNumOfChIn; i++)
        {
            if (m_AlzParam.WifiParam.MIMOAnalysisMode >= 1 && i != AnalyzeStreamID)
            {
                continue;
            }

#if DebugPrintEvm
            if (GetVsaResult(WT_RES_EVM_ALL, i, Seg, &pData, DataSize, DataType) == WT_OK)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "GetVsaResult %s = %lf\n", WT_RES_EVM_ALL, *((double *)pData));
            }
#endif
            if (GetVsaResult(WT_RES_SPECTRUM_OBW, i, Seg, &pData, DataSize, DataType) == WT_OK)
            {
                double Obw = (*(double *)pData) / Segment;
#if DebugPrintEvm
                WTLog::Instance().WriteLog(LOG_DEBUG, "GetVsaResult %s = %lf, %f\n", WT_RES_SPECTRUM_OBW, *((double *)pData), Obw);
#endif
                if (Obw >= SPECTRUM_OBW_320M_THRESHOLD)
                {
                    DataType = WT_IQ_IMB_PARAM_320M;
                }
                else if (Obw >= SPECTRUM_OBW_160M_THRESHOLD)
                {
                    DataType = WT_IQ_IMB_PARAM_160M;
                }
                else
                {
                    DataType = WT_IQ_IMB_PARAM_80M;
                }
                SetIQImbParam(Seg, i, DataType);
            }
        }
    }
    if (NeedReAnalynze)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "Reset iq imb data and re analyze\n");
        if (Segment == 2) // 8080分析
        {
            if (m_8080Rslt != nullptr)
            {
                m_8080Rslt->Reset();
                AlgReset::Instance().AlgGetReaderLockT();
                Ret = MIMO_WT_Algorithm_8080_main(m_RxInData, m_RxOutData[0], m_RxOutData[1], m_8080Rslt->m_Result);
                AlgReset::Instance().AlgReleaseLockT();
            }
        }
        else
        {
            AlgReset::Instance().AlgGetReaderLockT();
            Ret = MIMO_WT_Algorithm_main(&m_RxInData[0], m_RxOutData[0]);
            AlgReset::Instance().AlgGetReaderLockT();
        }

        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(WT_ALG_BASE_ERROR + Ret, "AlzData MIMO_WT_Algorithm_main failed");
            return WT_ALG_BASE_ERROR + Ret;
        }

#if DebugPrintEvm
        for (int i = 0; i < m_RxInData[0].iNumOfChIn; i++)
        {
            if (GetVsaResult(WT_RES_EVM_ALL, i, 0, &pData, DataSize, DataType) == WT_OK)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "GetVsaResult %s = %lf\n", WT_RES_EVM_ALL, *((double *)pData));
            }
        }
#endif
    }

    return Ret;
}

int Analysis::GetIQImbParam(int Segment, int Chain, Rx_Parm &CalParam)
{
    CalIQImbData &CalIQImbData = m_CalIQImbData[Segment][Chain];
    if (CalIQImbData.CurIQParamType == WT_IQ_IMB_PARAM_320M)
    {
        memcpy(&CalParam.rx_iq_imb_parm, &CalIQImbData.rx_iq_imb_parm_320m, sizeof(CalParam.rx_iq_imb_parm));
    }
    else if (CalIQImbData.CurIQParamType == WT_IQ_IMB_PARAM_160M)
    {
        memcpy(&CalParam.rx_iq_imb_parm, &CalIQImbData.rx_iq_imb_parm_160m, sizeof(CalParam.rx_iq_imb_parm));
    }

    memcpy(&CalParam.rx_iq_image_parm, &CalIQImbData.rx_iq_image_parm_back, sizeof(CalParam.rx_iq_image_parm));
    return WT_OK;
}

int Analysis::AlzPower(int Segment, bool &Exist, double &Power, double &PeakPower, double &MinPower, int Demode)
{
    AlgReset::ReaderLock AlgReaderLock; //分析过程不允许重置算法内存
#if TIME_DEBUG
    auto startTime = std::chrono::high_resolution_clock::now();
#endif
    int Ret = AllocBuf(Segment, 0);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    //当主从路中心频率间隔小于等于160M时，主从路的信号会有相加，
    //按80+80业务分析时，分析到的峰值功率已经过滤掉了另一路信号的干扰，导致分析结果的峰值功率比实际峰值功率低。引起削顶
    //按80M业务分析时，分析功率时没过滤另一路的干扰，更接近实际峰值功率，不会引起削顶
    //int AnalyzeGroup = m_AnalyseGroup;

    int AnalyzeGroup = WT_GROUP_FFT;
    if (Demode == WT_DEMOD_11AX_80_80M)
    {
        Demode = WT_DEMOD_11AX_80M;
        AnalyzeGroup = WT_GROUP_FFT;
    }
    else if (Demode == WT_DEMOD_11AX_160_160M)
    {
        //实际测试发现，按160+160分析时，得到的峰值功率接近最佳峰值功率，不需转换成160M业务去分析
        //        Demode = WT_DEMOD_11AX_160M;
    }
    else if (Demode == WT_DEMOD_11AC_80_80M)
    {
        Demode = WT_DEMOD_11AC_80M;
        AnalyzeGroup = WT_GROUP_FFT;
    }
    else if (Demode == WT_DEMOD_11BE_160_160M)
    {
        Demode = WT_DEMOD_11BE_160M;
        AnalyzeGroup = WT_GROUP_FFT;
    }
    else if (Demode == WT_DEMOD_11BE_80_80M)
    {
        Demode = WT_DEMOD_11BE_80M;
        AnalyzeGroup = WT_GROUP_FFT;
    }

    SetAlgVsaParam(&m_RxInData[Segment].aStChIn[0], m_AlzParam);
    SetAlgExtralParam(&m_RxInData[Segment].aStChIn[0], m_ExtralAlzParam);

    m_RxInData[Segment].aStChIn[0].analyzeFrameIndex = 1;
    m_RxInData[Segment].aStChIn[0].analyzeGroup = AnalyzeGroup;
    m_RxInData[Segment].aStChIn[0].demod_mode = Demode;
    m_RxInData[Segment].aStChIn[0].FrameAutoDetection = WT_USER_DEFINED;
    WTLog::Instance().WriteLog(LOG_DEBUG, "AlzPower demod_mode = %d\n", m_RxInData[Segment].aStChIn[0].demod_mode);
    WTLog::Instance().WriteLog(LOG_DEBUG, "AlzPower analyzeGroup = %d\n", m_RxInData[Segment].aStChIn[0].analyzeGroup);
    AlgReset::Instance().AlgGetReaderLockT();
    Ret = MIMO_WT_Algorithm_main(&m_RxInData[Segment], m_RxOutData[Segment]);
    AlgReset::Instance().AlgReleaseLockT();
    if (Ret == WT_OK)
    {
        Exist = m_RxOutData[Segment]->aStCh[0].pkg.frmcnt > 0;
        Power = Exist ? m_RxOutData[Segment]->aStCh[0].pwr.frm_pwravg_dbm[0]
                      : m_RxOutData[Segment]->aStCh[0].pwr.pkg_pwravg_dbm;
        PeakPower = m_RxOutData[Segment]->aStCh[0].pwr.pkg_pwrmax_dbm;
        int Count = m_RxOutData[Segment]->aStCh[0].pkg.pwrwin_cnt;
        double *Power = m_RxOutData[Segment]->aStCh[0].pkg.pwrwin_avg_dbm;
        MinPower = 100;
        for (int i = 0; i < Count; i++)
        {
            if (Power[i] < MinPower)
            {
                MinPower = Power[i];
            }
        }
    }
    else
    {
        Ret += WT_ALG_BASE_ERROR;
    }
#if TIME_DEBUG
    auto endTime = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::micro> time_us = endTime - startTime;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << ", ALG AlzPower Used Time:" << time_us.count() << "us" << std::endl;
#endif
    return Ret;
}

int Analysis::AlzData(int FrameId, int FrameCnt)
{
    int Ret = WT_OK;
    m_AlzAll = WT_OK;

    ClearAvgData();

    for (int Cnt = 0; Cnt < FrameCnt; Cnt++)
    {
        Ret = AlzAvgData(FrameId + Cnt);
        if (Ret != WT_OK)
        {
            return Ret;
        }

        //如果帧数量比要分析的少则停止平均
        if (m_RxOutData[0]->aStCh[0].pkg.frmcnt <= FrameId + Cnt + 1)
        {
            break;
        }
    }

    return Ret;
}

int Analysis::AlzAvgData(int FrameId)
{
    m_AlzAll = false;

    int Ret = AlzFrameData(FrameId);

    if (m_AlgAvg[0] == nullptr)
    {
        m_AlgAvg[0].reset(new(std::nothrow) AlgAvgResult);
        if (m_AlgAvg[0] == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc memory for average result failed");
            return WT_ALLOC_FAILED;
        }
        m_AlgAvg[0]->SetLastResultDemo(UNVALID_INT_VAL);
    }

    //平均检测自动清空数据处理
    if(m_AvgCnt > 0)
    {
        int LastResultDemo = m_AlgAvg[0]->GetLastResultDemo();
        double DataRate = UNVALID_DOUBLE_VAL;
        int Len = 0;

        int FrameType = UNVALID_INT_VAL;//SLE
        int BandWidth = UNVALID_INT_VAL;//SLE
        int PayLoadLength = 0;//SLE
        int CtrlInfoType = UNVALID_INT_VAL;
        int MCS = UNVALID_INT_VAL;//SLE
        char PayLoadBin[256];//SLE

        int PayLoadSize = UNVALID_INT_VAL;//BT
        int PacketType = UNVALID_INT_VAL;//BT
        int Pattern = UNVALID_INT_VAL;//BT
        int DataRate1 = UNVALID_INT_VAL;//BT

        if(Ret != WT_OK)
        {
            ClearAvgData();
            ClrFlag = true;
        }
        else
        {
            bool ChangeFlag = false;

            if (m_AlzParam.Demode == WT_DEMOD_GLE)
            {
                if (m_RxOutData[0]->SISOFrm)
                {
                    FrameType = m_RxOutData[0]->SISOFrm->DbGle.FrmType;
                    BandWidth = m_RxOutData[0]->SISOFrm->DbGle.Bandwidth;
                    PayLoadLength = m_RxOutData[0]->SISOFrm->DbGle.PayloadLen;
                    CtrlInfoType = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.CtrlInfoType;
                    if (PayLoadLength < 0 || PayLoadLength > sizeof(PayLoadBin))
                    {
                        PayLoadLength = 0;
                    }
                    memcpy(PayLoadBin, m_RxOutData[0]->SISOFrm->DbGle.PayloadBin, PayLoadLength);

                    switch (CtrlInfoType)
                    {
                    case WT_SLE_CTRLINFO_TYPE_A1:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA1.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_A2:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA2.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_A3:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA3.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_A4:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA4.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_A5:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA5.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_A6:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA6.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_A7:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeA7.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_B1:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeB1.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_B3:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeB3.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_B4:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeB4.Mcs;
                        break;
                    case WT_SLE_CTRLINFO_TYPE_B5:
                        MCS = m_RxOutData[0]->SISOFrm->DbGle.CtrlInfo.TypeB5.Mcs;
                        break;
                    default:
                        break;
                    }
                }

                if ((FrameType != m_SLEFrameType) || (BandWidth != m_SLEBandWidth)
                    || (PayLoadLength != m_SLEPayLoadLength) || (CtrlInfoType != m_SLECtrlInfoType)
                    || (MCS != m_SLEMCS) || (memcmp(PayLoadBin, m_SLEPayLoadBin, PayLoadLength) != 0))
                {
                    ChangeFlag = true;
                }
            }
            else if (m_AlzParam.Demode == WT_DEMOD_BT)
            {
                VsaBTCommResult2 *Result;
                Ret = GetBTCommResult(0, 0, &Result);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            
                if (m_RxOutData[0]->SISOFrm)
                {
                    PayLoadSize = m_RxOutData[0]->SISOFrm->DbBT.FrmInfo.PayLoadSize;
                    PacketType = m_RxOutData[0]->SISOFrm->DbBT.PacketType;
                    Pattern = m_RxOutData[0]->SISOFrm->DbBT.Pattern;
                    DataRate1 = m_RxOutData[0]->SISOFrm->DbBT.DataRate;
                }
                
                if ((DataRate1 != m_BTDataRate) || (PayLoadSize != m_BTPayLoadSize) 
                   || (PacketType != m_BTPacketType) || (Pattern != m_BTPattern))
                {
                    ChangeFlag = true;
                }
            }
            else
            {
                const VsaCommResult *Result;
                Ret = GetCommResult(0, 0, &Result);
                if (Ret != WT_OK)
                {
                    return Ret;
                }

                // 有帧转无帧，无帧转有帧，或者demode改变的情况下都清空~,或者解析出来的Datainfo中的DataRate和上次的不一样时（如果还出现频谱渐变问题，增加DataInfo中的mcs判断）
                if (m_RxOutData[0]->aStCh[0].pkg.frmcnt > 0 && Ret == WT_OK && Result != nullptr && Basefun::CompareDouble(Result->EvmAll, UNVALID_DOUBLE_VAL) != 0)
                {
                    double *TmpRate = &DataRate;
                    AlgReset::ReaderLock AlgReaderLock;
                    TmpRate = (double *)AlgResult::GetOfdmDataRate(m_RxOutData[0], 0, TmpRate, Len);
                    DataRate = *TmpRate;
                    TmpRate = nullptr;
                }
                for (int i = 0; i < m_RxInData[0].iNumOfChIn; i++)
                {
                    double LastEvmAll = m_AlgAvg[0]->GetAvg(i).EvmAll;
                    // 平均时是否清空平均数据的判断条件，如果还有错误，需要重新系统考虑判断条件
                    // WTLog::Instance().WriteLog(LOG_DEBUG, "LastDataRate=%lf, DataRate=%lf\n",LastDataRate,DataRate);
                    // 平均时是否清空平均数据的判断条件，如果还有错误，需要重新系统考虑判断条件
                    if ((Basefun::CompareDouble(LastEvmAll, UNVALID_DOUBLE_VAL) != 0 && m_RxOutData[0]->aStCh[i].pkg.frmcnt == 0)
                    || (Basefun::CompareDouble(LastEvmAll, UNVALID_DOUBLE_VAL) == 0 && m_RxOutData[0]->aStCh[i].pkg.frmcnt > 0 && Basefun::CompareDouble(Result->EvmAll, UNVALID_DOUBLE_VAL) != 0)
                    || (Basefun::CompareDouble(LastEvmAll, UNVALID_DOUBLE_VAL) != 0 && m_RxOutData[0]->aStCh[i].pkg.frmcnt > 0 && LastResultDemo != m_RxOutData[0]->aStCh[i].local->demod_mode && LastResultDemo != UNVALID_INT_VAL)
                    || (LastEvmAll > 0)
                    || (Basefun::CompareDouble(LastDataRate, DataRate) != 0 && LastDataRate != UNVALID_DOUBLE_VAL))
                    {
                        ChangeFlag = true;
                        break;
                    }
                }
            }
            
            if(ChangeFlag)
            {
                m_AvgCnt = 1;
                ClrFlag = true;
                for (int j = 0; j < m_RxInData[0].iNumOfChIn; j++)
                {
                    if (m_AlzParam.Demode == WT_DEMOD_BT)
                    {
                        VsaBTCommResult *Result1;
                        VsaBTCommResult2 *Result;

                        Ret = GetBTCommResult(0, j, &Result);
                        Result1 = Result;
                        if (Ret == WT_OK && Result1 != nullptr)
                        {
                            m_AlgAvg[0]->InitBT(j, *Result1);
                            BTMultiAlgResult[j].clear(); //平均数据清空重新计算时，要清楚历史的结果数据，否则影响最大最小值结果计算
                            AlgResult::Instance().m_BTRawBuf.clear();
                            AlgResult::Instance().m_BTRawBufF2.clear();
                            BTMultiAlgResult[j].push_back(*Result1);
                        }
                    }
                    else if (m_AlzParam.Demode == WT_DEMOD_GLE)
                    {
                        VsaSleCommResult *Result;
                        VsaSleCommResult2 *Result1;
                        Ret = GetSleCommResult(0, j, &Result1);
                        Result = Result1;
                        if (Ret == WT_OK && Result != nullptr)
                        {
                            m_AlgAvg[0]->InitSLE(j, *Result);
                            SLEMultiAlgResult[j].clear(); // 平均数据清空重新计算时，要清楚历史的结果数据，否则影响最大最小值结果计算
                            AlgResult::Instance().m_SLERawBufF2.clear();
                            SLEMultiAlgResult[j].push_back(*Result);
                        }
                    }
                    else
                    {
                        const VsaCommResult *Result;
                        Ret = GetCommResult(0, j, &Result);
                        if (Ret == WT_OK && Result != nullptr)
                        {
                            m_AlgAvg[0]->Init(j, *Result);
                            MultiAlgResult[j].clear(); // 平均数据清空重新计算时，要清楚历史的结果数据，否则影响最大最小值结果计算
                            MultiAlgResult[j].push_back(*Result);
                        }
                    }
                }
            }
            else
            {
                ClrFlag = false;
            }
        }
        if (m_AlzParam.Demode == WT_DEMOD_GLE)
        {
            m_SLEFrameType = FrameType;
            m_SLEBandWidth = BandWidth;
            m_SLECtrlInfoType = CtrlInfoType;
            m_SLEPayLoadLength = PayLoadLength;
            m_SLEMCS = MCS;
            memcpy(m_SLEPayLoadBin, PayLoadBin, m_SLEPayLoadLength);
        }
        else if (m_AlzParam.Demode == WT_DEMOD_BT)
        {
            m_BTPacketType = PacketType;   // BT
            m_BTPayLoadSize = PayLoadSize; // BT
            m_BTPattern = Pattern;         // BT
            m_BTDataRate = DataRate1;      // BT
        }
        else
        {
            m_AlgAvg[0]->SetLastResultDemo(m_RxOutData[0]->aStCh[0].demod_mode);
            LastDataRate = DataRate;
        }

        if(ClrFlag == true)
        {
            return Ret;
        }
    }
    else
    {
        if (m_AlzParam.Demode == WT_DEMOD_GLE)
        {
            VsaSleCommResult2 *Result;
            Ret = GetSleCommResult(0, 0, &Result);
        }
        else if (m_AlzParam.Demode == WT_DEMOD_BT)
        {
            VsaBTCommResult2 *Result;
            Ret = GetBTCommResult(0, 0, &Result);

        }
        else
        {
            const VsaCommResult *Result;
            Ret = GetCommResult(0, 0, &Result);
        }
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    if (Ret != WT_OK)
    {
        return Ret;
    }

    //实际帧数比要分析的帧少则返回
    // if (m_RxOutData[0]->aStCh[0].pkg.frmcnt <= FrameId)
    // {
    //     return WT_OK;
    // }

    //平均第一次数据保存分析的Demod，datainfo，方便判断是否清空
    if (m_AvgCnt == 0)
    {
        m_AlgAvg[0]->SetLastResultDemo(m_RxOutData[0]->aStCh[0].demod_mode); //保存平均时前一次分析的Demod
        int Len = 0;
        if (m_RxInData[0].iNumOfChIn >= 1)
        {
            if (m_AlzParam.Demode == WT_DEMOD_BT)
            {
            }
            else if (m_AlzParam.Demode == WT_DEMOD_GLE)
            {
            }
            else
            {
                const VsaCommResult *Result;
                Ret = GetCommResult(0, 0, &Result);
                if (m_RxOutData[0]->aStCh[0].pkg.frmcnt > 0 && Ret == WT_OK && Result != nullptr && Basefun::CompareDouble(Result->EvmAll, UNVALID_DOUBLE_VAL) != 0)
                {
                    double *TmpRate = &LastDataRate;
                    AlgReset::ReaderLock AlgReaderLock;
                    TmpRate = (double *)AlgResult::GetOfdmDataRate(m_RxOutData[0], 0, TmpRate, Len);
                    LastDataRate = *TmpRate;
                    TmpRate = nullptr;
                }
            }
        }
    }

    for (int j = 0; j < m_RxInData[0].iNumOfChIn; j++)
    {
        if (m_AlzParam.Demode == WT_DEMOD_BT)
        {
            VsaBTCommResult *Result1;
            VsaBTCommResult2 *Result;
            Ret = GetBTCommResult(0, j, &Result);
            Result1 = Result;
            if (Ret != WT_OK)
            {
                return Ret;
            }

            if (m_AvgCnt == 0)
            {
                m_AlgAvg[0]->InitBT(j, *Result1);
                BTMultiAlgResult[j].clear(); //平均数据清空重新计算时，要清除历史的结果数据，否则影响最大最小值结果计算
                AlgResult::Instance().m_BTRawBuf.clear();
                AlgResult::Instance().m_BTRawBufF2.clear();
                BTMultiAlgResult[j].push_back(*Result1);

                if ((Result->DeltaF1Len != VAL_DAT) || (Result->DeltaF2Len != VAL_DAT))
                {
                    m_AlgAvg[0]->BTStatistic(*Result);
                }
            }
            else
            {
                m_AlgAvg[0]->BTAverage(j, *Result1, m_AvgCnt);
                BTMultiAlgResult[0].push_back(*Result1);
                m_AlgAvg[0]->BTMultiMax(j, BTMultiAlgResult[j]);
                m_AlgAvg[0]->BTMultiMin(j, BTMultiAlgResult[j]);

                if ((Result1->DeltaF1Len != VAL_DAT) || (Result1->DeltaF2Len != VAL_DAT))
                {
                    m_AlgAvg[0]->BTStatistic(*Result);
                }
            }
        }
        else if (m_AlzParam.Demode == WT_DEMOD_GLE)
        {
            VsaSleCommResult *Result;
            VsaSleCommResult2 *Result1;
            Ret = GetSleCommResult(0, j, &Result1);
            Result = Result1;
            if (Ret != WT_OK)
            {
                return Ret;
            }

            if (m_AvgCnt == 0)
            {
                m_AlgAvg[0]->InitSLE(j, *Result);
                SLEMultiAlgResult[j].clear(); //平均数据清空重新计算时，要清除历史的结果数据，否则影响最大最小值结果计算
                SLEMultiAlgResult[j].push_back(*Result);
                AlgResult::Instance().m_SLERawBufF2.clear();

                if (Result->DeltaF2Len != VAL_DAT)
                {
                    m_AlgAvg[0]->SLEStatistic(*Result1);
                }
            }
            else
            {
                m_AlgAvg[0]->SLEAverage(j, *Result, m_AvgCnt);
                SLEMultiAlgResult[j].push_back(*Result);
                m_AlgAvg[0]->SLEMultiMax(j, SLEMultiAlgResult[j]);
                m_AlgAvg[0]->SLEMultiMin(j, SLEMultiAlgResult[j]);
                if (Result->DeltaF2Len != VAL_DAT)
                {
                    m_AlgAvg[0]->SLEStatistic(*Result1);
                }
            }
        }
        else
        {
            const VsaCommResult *Result;
            Ret = GetCommResult(0, j, &Result);
            if (Ret != WT_OK)
            {
                return Ret;
            }

            if (m_AvgCnt == 0)
            {
                m_AlgAvg[0]->Init(j, *Result);
                MultiAlgResult[j].clear(); //平均数据清空重新计算时，要清除历史的结果数据，否则影响最大最小值结果计算
                MultiAlgResult[j].push_back(*Result);
            }
            else
            {
                m_AlgAvg[0]->Average(j, *Result, m_AvgCnt);
                MultiAlgResult[j].push_back(*Result);
                m_AlgAvg[0]->MultiMax(j, MultiAlgResult[j]);
                m_AlgAvg[0]->MultiMin(j, MultiAlgResult[j]);
            }
        }
    }

    m_AvgCnt++;

    return WT_OK;
}

int Analysis::StripAvgData(int FrameId)
{
    m_AlzAll = false;

    int Ret = AlzFrameData(FrameId);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    if (m_AlgAvg[0] == nullptr)
    {
        WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no avg result exist");
        return WT_NO_AVG_RESULT;
    }

    for (int j = 0; j < m_RxInData[0].iNumOfChIn; j++)
    {
        if (m_AlzParam.Demode == WT_DEMOD_BT)
        {
            VsaBTCommResult *Result1 = nullptr;
            VsaBTCommResult2 *Result = nullptr;
            GetBTCommResult(0, j, &Result);
            Result1 = Result;
            m_AlgAvg[0]->BTStrip(j, *Result1, m_AvgCnt);
            if (!BTMultiAlgResult[j].empty())
            {
                BTMultiAlgResult[j].erase(BTMultiAlgResult[j].begin());
            }
            if (Result1->DeltaF1Len != VAL_DAT)
            {
                AlgResult::Instance().m_BTRawBuf.pop_front();
            }
            if (Result1->DeltaF2Len != VAL_DAT)
            {
                AlgResult::Instance().m_BTRawBufF2.pop_front();
            }
        }
        else if (m_AlzParam.Demode == WT_DEMOD_GLE)
        {
            VsaSleCommResult *Result = nullptr;
            VsaSleCommResult2 *Result1 = nullptr;
            GetSleCommResult(0, j, &Result1);
            Result = Result1;
            m_AlgAvg[0]->SLEStrip(j, *Result, m_AvgCnt);
            if (!SLEMultiAlgResult[j].empty())
            {
                SLEMultiAlgResult[j].erase(SLEMultiAlgResult[j].begin());
            }
            if (Result->DeltaF2Len != VAL_DAT)
            {
                AlgResult::Instance().m_SLERawBufF2.pop_front();
            }
        }
        else
        {
            const VsaCommResult *Result = nullptr;
            GetCommResult(0, j, &Result);
            m_AlgAvg[0]->Strip(j, *Result, m_AvgCnt);
            if (!MultiAlgResult[j].empty())
            {
                MultiAlgResult[j].erase(MultiAlgResult[j].begin());
            }
        }
    }

    m_AvgCnt--;

    return WT_OK;
}

int Analysis::LoadSigFile(const string &File, const ExtendEVMStu &ExtendEvm)
{
    m_Comb8080To160 = false;
    m_IsIQImbReset = IQ_IMB_NOT_NEED_RESET;
    m_SigFile.reset(new(std::nothrow) SigFile(File, SigFile::READ));
    if (m_SigFile == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "open signal file failed");
        return WT_ALLOC_FAILED;
    }

    SigFileInfo *FileInfo = nullptr;
    int Ret = m_SigFile->GetContent(&FileInfo);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    if (FileInfo == nullptr)
    {
        RX_InDat *pInData = m_RxInData[0].aStChIn;
        m_RxInData[0].iNumOfChIn = 1;
        pInData->capturedata = const_cast<void*>(m_SigFile->GetFileBuf());
        pInData->capturecnt = m_SigFile->GetFileSize() / sizeof(Complex);
        pInData->capturedata_format = enDataFormat_Float64;
        pInData->vht80_80MEnable = 0;
        pInData->adc_freq = MAX_SMAPLE_RATE;
        pInData->scene_model = Normal;
        pInData->ScaleTo = AlgEnv::Instance().GetScaleVal(enDataFormat_Float64);
        pInData->Spec500MFlag = 0;  //加载文件时不能开启500M频谱功能
        ClrCalParam(pInData);
    }
    else
    {
        int SegNum = FileInfo->FileType ? 2 : 1;
        m_Comb8080To160 = FileInfo->FileType == FILE_TYPE_160;
        WTLog::Instance().WriteLog(LOG_DEBUG, "Get File FileType = %d\n", FileInfo->FileType);
        m_SpectOffset.reset(new(std::nothrow) stSpectrumOffset[FileInfo->ChainNum * SegNum]);
        if (m_SpectOffset == nullptr)
        {
            return WT_ALLOC_FAILED;
        }

        SigDataHeader *SigHeader = FileInfo->SigHeader;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SigHeader->ModType = " << SigHeader->ModType << std::endl;
        m_Alg_3GPP_VsaInInfo.RFInChanNum = 0;
        memset(&m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
        memset(&m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
        memset(&m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
        for (int i = 0; i < FileInfo->ChainNum && i < ALG_3GPP_MAX_STREAM; ++i)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##########4GLTE############# LoadSigFile, Channel:" << i << std::endl;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].capturedata = SigHeader->Data;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].capturecnt = SigHeader->SampleCount;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].capturedata_format = SigHeader->DataType;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].ScaleTo = AlgEnv::Instance().GetScaleVal(SigHeader->DataType);
            m_Alg_3GPP_VsaInInfo.RFInfo[i].IDCOffset = SigHeader->DCOffsetI;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].QDCOffset = SigHeader->DCOffsetQ;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].TimeSkew = SigHeader->TimeSkew;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].IQImb_Amp = SigHeader->IQGainImb;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].IQImb_Phase = SigHeader->IQPhaseImb;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].adc_freq = round(SigHeader->SamplingRate);
            m_Alg_3GPP_VsaInInfo.RFInfo[i].gain = SigHeader->RFGain;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].rf_band = 0; // 分析参数
            m_Alg_3GPP_VsaInInfo.RFInfo[i].rf_response = SigHeader->RfResponse.Response;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].rf_response_len = SigHeader->RfResponse.FreqCount;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].bb_response = SigHeader->BBResponse.Response;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].rf_response_len = SigHeader->BBResponse.FreqCount;

            //if (SigHeader->ExtAttEnable == true) // 未明确
            {
                m_Alg_3GPP_VsaInInfo.RFInfo[i].extgain = SigHeader->ExtAtt; // 未明确
            }
            // m_Alg_3GPP_VsaInInfo.RFInfo[i].trig_pwr = SigHeader->TriggerLevel; //未明确
            m_Alg_3GPP_VsaInInfo.RFInfo[i].rf_center_freq = SigHeader->CenterFreq;
            m_Alg_3GPP_VsaInInfo.RFInfo[i].rf_channel = 0; // 分析参数
            m_Alg_3GPP_VsaInInfo.RFInfo[i].referenceLevel = SigHeader->RefLevel;
            ++SigHeader;
            ++m_Alg_3GPP_VsaInInfo.RFInChanNum;
        }
        SigHeader = FileInfo->SigHeader;
        m_RxInData[0].iNumOfChIn = 0;
        m_RxInData[1].iNumOfChIn = 0;
        int Scene = (FileInfo->ChainNum > 1 && FileInfo->MimoType) ? SwitchMIMO : Normal;

        for (int i = 0; i < FileInfo->ChainNum; i++)
        {
            for (int Seg = 0; Seg < SegNum; Seg++)
            {
                CalIQImbData &CalParam = m_CalIQImbData[Seg][i];
                CalParam.rx_iq_image_parm.freq_Iqimb_len = SigHeader->iqImage.freq_Iqimb_len;
                CalParam.rx_iq_image_parm_back.freq_Iqimb_len = SigHeader->iqImage.freq_Iqimb_len;
                if (SigHeader->iqImage.freq_Iqimb_len)
                {
                    memcpy(CalParam.rx_iq_image_parm.freq_gain_imb, SigHeader->iqImage.freq_gain_imb, sizeof(double) * SigHeader->iqImage.freq_Iqimb_len);
                    memcpy(CalParam.rx_iq_image_parm.freq_quad_err, SigHeader->iqImage.freq_quad_err, sizeof(double) * SigHeader->iqImage.freq_Iqimb_len);
                    memcpy(CalParam.rx_iq_image_parm_back.freq_gain_imb, SigHeader->iqImage.freq_gain_imb, sizeof(double) * SigHeader->iqImage.freq_Iqimb_len);
                    memcpy(CalParam.rx_iq_image_parm_back.freq_quad_err, SigHeader->iqImage.freq_quad_err, sizeof(double) * SigHeader->iqImage.freq_Iqimb_len);
                }
                RX_InDat *pInData = &m_RxInData[Seg].aStChIn[i];
                pInData->freq_IQImb_amp = CalParam.rx_iq_image_parm.freq_gain_imb;
                pInData->freq_IQImb_phase = CalParam.rx_iq_image_parm.freq_quad_err;
                pInData->freq_IQImb_len = m_ForceTimeDomainIq ? 0 : CalParam.rx_iq_image_parm.freq_Iqimb_len;
                SetSigDataToVsa(SigHeader, pInData, nullptr, ExtendEvm);

                if (pInData->capturedata_format == enDataFormat_Int16)
                {
                    pInData->extgain = AlgEnv::Instance().m_VsaPwrBaseDelta;
                }
                else
                {
                    pInData->extgain = 0;
                }
                pInData->extgain = SigHeader->ExtAtt;

                pInData->vht80_80MEnable = FileInfo->FileType ? 1 : 0;
                // 规避处理，如果是8080时，强制把分析配置配置智能模式为user define，规格暂时不支持8080开启自动识别功能
                if (pInData->vht80_80MEnable == 1)
                {
                    pInData->FrameAutoDetection = WT_USER_DEFINED;
                }
                pInData->scene_model = Scene;
                pInData->Spec500MFlag = 0;                           // 加载文件时不能开启500M频谱功能
                pInData->clockrate = FileInfo->SigHeader->ClockRate; // 文件保存的压缩比
                m_RxInData[Seg].iNumOfChIn++;
                SigHeader++;

                // 配合MIMOAnalysisMode为1功能使用
                m_RealChannelCount[Seg] = m_RxInData[Seg].iNumOfChIn;
                m_AlzInDataCh0Bak = false;
                m_HistoryMIMOAnalysisMode = false;
            }

            // 防止8080文件没有频率参数或者为默认参数导致不能分析而填写默认频率参数
            if (SegNum == 2 && abs(m_RxInData[0].aStChIn[i].rf_center_freq - m_RxInData[1].aStChIn[i].rf_center_freq) < 0.1)
            {
                if (!m_Comb8080To160)
                {
                    m_RxInData[0].aStChIn[i].rf_center_freq = 5210 * 1e6;
                    m_RxInData[1].aStChIn[i].rf_center_freq = 5610 * 1e6;
                }
                else
                {
                    m_RxInData[0].aStChIn[i].rf_center_freq = 5210 * 1e6;
                    // 为保证160+160能正确分析，f1、f2间隔需大于等于160M
                    m_RxInData[1].aStChIn[i].rf_center_freq = m_RxInData[0].aStChIn[i].rf_center_freq + (160 * 1e6);
                }
            }

            // 判断80+80哪个中心频率大的，设置算法输入的segment为1；小的频率，设置输入的segment为0
            for (int Seg = 0; Seg < SegNum; Seg++)
            {
                RX_InDat *pInData = &m_RxInData[Seg].aStChIn[i];
                if (pInData->vht80_80MEnable)
                {
                    double MaxFreq = 0;
                    MaxFreq = max(m_RxInData[0].aStChIn[i].rf_center_freq, m_RxInData[1].aStChIn[i].rf_center_freq);
                    pInData->currentSeg80_80M = Basefun::CompareDouble(m_RxInData[Seg].aStChIn[i].rf_center_freq, MaxFreq) >= 0 ? 1 : 0;
                }
            }
        }
    }

    return WT_OK;
}

int Analysis::AllocBuf(int Segment, int Chain)
{
    if (m_RxOutData[Segment] == nullptr)
    {
        AlgReset::Instance().AlgGetReaderLockT();
        int Ret = MIMO_WT_Algorithm_init(&m_RxOutData[Segment]);
        AlgReset::Instance().AlgReleaseLockT();
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "MIMO_WT_Algorithm_init failed");
            m_RxOutData[Segment] = nullptr;
            return Ret;
        }
    }

    RxInfoMultIn &RxInData = m_RxInData[Segment];
    AlgReset::Instance().AlgGetReaderLockT();
    int BufLen = WT_Algorithm_ReqMemSizePerChannel(&RxInData.aStChIn[Chain]);
    AlgReset::Instance().AlgReleaseLockT();

    //当现有内存比需要内存小时才申请
    if (m_BufLen[Segment][Chain] < BufLen)
    {
        m_AlzBuf[Segment][Chain].reset(MemPool::Instance().Alloc(BufLen));
        if (m_AlzBuf[Segment][Chain] == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "AllocBuf failed");
            return WT_ALLOC_FAILED;
        }

        m_BufLen[Segment][Chain] = BufLen;
        //特殊处理，当开启矢量平均时，需要重新给内存，重新计算，需要把是否是第一次分析配置置回0,否则算法会报错
        RxInData.aStChIn[Chain].analyzeMode = 0;
    }

    RxInData.aStChIn[Chain].ReserveMem = static_cast<char*>(m_AlzBuf[Segment][Chain].get());
    RxInData.aStChIn[Chain].ReserveMemSize = m_BufLen[Segment][Chain];

    return WT_OK;
}

void Analysis::SetWifiFlatnessDataFitting(StOutInfo *pRxOut, const std::string &Type)
{
    if (WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED == Type ||
        WT_RES_OFDM_SPECTRUM_FLATNESS_DATA == Type ||
        WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN == Type ||
        WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE == Type ||
        WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA == Type ||
        WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA == Type)
    {
        if (false == m_FlatnessFitting)
        {
            AlgReset::ReaderLock AlgReaderLock;
            AlgResult::Instance().SetFittingFlatnessData(pRxOut);
            m_FlatnessFitting = true;
        }
    }
    return;
}

void Analysis::ResetAlgMemory(void)
{
    m_AlzAll = false;
    m_AnalyzeMode = 0;
#if ALG_RESET_DEBUG
    printf("Analysis::ResetAlgMemory\n");
#endif
    for (int Seg = 0; Seg < MAX_SEGMENT_CNT; Seg++)
    {
        for (int Ch = 0; Ch < MAX_NUM_OF_CHNNEL; Ch++)
        {
            if (m_AlzBuf[Seg][Ch] != nullptr)
            {
#if ALG_RESET_DEBUG
                printf("m_AlzBuf[%d][%d] = %p, size=%u\n", Seg, Ch, m_AlzBuf[Seg][Ch].get(), m_BufLen[Seg][Ch]);
#endif
                m_AlzBuf[Seg][Ch].reset();
                m_BufLen[Seg][Ch] = 0;
            }
        }
#if ALG_RESET_DEBUG
        printf("m_RxOutData[%d] = %p\n", Seg, m_RxOutData[Seg]);
#endif
        m_RxOutData[Seg] = nullptr;
    }
}

int Analysis::GetVsaResult(const string &Type, int Stream, int Segment, void **DataBuf, int &DataSize, int &DataType)
{
    AlgReset::ReaderLock AlgReaderLock;
    int Ret = WT_OK;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    if (m_ResultBuf == nullptr)
    {
         m_ResultBuf.reset(MemPool::Instance().Alloc(m_ResultBufLen));
         if (m_ResultBuf == nullptr)
         {
             WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc result buffer failed");
             return WT_ALLOC_FAILED;
         }
    }

    //WT_RES_EXPORT_PSDU_BIT == Type || WT_RES_AX_EXPORT_PSDU_BIT == Type || check mac lic, 目前就控制psdu bit，psdu decode，psdu compose，msdu info，tf info，这几个视图的结果输出
    //428直接放开tf info视图，448判断mac lic || WT_RES_PSDU_DECOMPOSED_INFO == Type
    if((WT_RES_TRIGGER_FRAME_INFO == Type && WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448))
    {
        if(WT_OK != CheckMacLic(m_RxOutData[0]))
        {
            return WT_RESULT_UNVALID;
        }
    }

    //获取bt5.1结果时检测bt5.1的lic，不存在返回无效结果
    if(WT_OK != CheckBT51Lic(Type))
    {
        return WT_RESULT_UNVALID;
    }

    if (Type == WT_RES_SEGMENT_NUM)
    {
        *DataBuf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;
        *(int *)(*DataBuf) = !m_Comb8080To160 ? GetSegmentNum() : 1;
        DataSize = sizeof(int);
        DataType = sizeof(int);
        m_BufPos += DataSize;
        return WT_OK;
    }
    else if(Type == WT_RES_TBT_SIFS)
    {
        *DataBuf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;
        *(double *)(*DataBuf) = m_FrameId < m_TBTSIFS.size()
                                    ? m_TBTSIFS[m_FrameId]
                                    : (m_TBTSIFS.size() > 1 ? m_TBTSIFS[0] : 0);
        DataSize = sizeof(double);
        DataType = sizeof(double);
        m_BufPos += DataSize;
        return WT_OK;
    }

    ResultItemHandler *pHandler = nullptr;
    StOutInfo *pRxOut = m_RxOutData[0];
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(pRxOut->InitNumOfCh) << endl;


    // 获取使能H矩阵前的power结果，不能判断iNumOfCh,要判断InitNumOfCh
    if (Type == WT_RES_HMAT_INIT_RMS_DB_NO_GAP || Type == WT_RES_HMAT_INIT_RMS_DB || Type == WT_RES_HMAT_INIT_POWER_PEAK || Type == WT_RES_HMAT_INIT_STREAM_COUNT)
    {
        if (Stream >= pRxOut->InitNumOfCh || Stream < 0)
        {
            return WT_STREAM_ID_ERROR;
        }
    }
    else
    {
        if (m_RxInData[0].aStChIn[0].scene_model == CMIMOModel)
        {
            if(Stream >= CMIMO_MAX_STREAM || Stream < 0)
            {
                return WT_STREAM_ID_ERROR;
            }
        }
        else if (Stream >= pRxOut->iNumOfCh || Stream < 0)
        {
            return WT_STREAM_ID_ERROR;
        }
    }

    //获取8080综合结果
    if(m_RxInData[0].aStChIn[0].vht80_80MEnable && (m_Comb8080To160 || Segment == 0) &&
        CheckIfNeedGetResultFrom8080CombRst(Type, pRxOut))
    {
        pHandler = AlgResult::Instance().Get8080ResultItemHandler(Type);
        if (pHandler == nullptr)   //先从8080结果中查询，不存在时从普通结果中查询
        {
            pHandler = AlgResult::Instance().GetResultItemHandler(Type);
            if (pHandler == nullptr)
            {
                WTLog::Instance().LOGERR(WT_RESULT_NOT_EXIST, "result type not exist");
                return WT_RESULT_NOT_EXIST;
            }

            if (Segment == 2)
            {
                pRxOut = m_RxOutData[1];
            }
            SetWifiFlatnessDataFitting(pRxOut, Type);

            *DataBuf = pHandler->GetResult(pRxOut, Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
        }
        else
        {
            if(Type == WT_RES_BASE_RESULT_COMPOSITE || Type == WT_RES_SIGB_EVM_ALL_COMPOSITE || \
                    Type == WT_RES_SIGB_EVM_PILOT_DB_COMPOSITE || Type == WT_RES_SIGB_EVM_DATA_DB_COMPOSITE)
            {
                SetWifiFlatnessDataFitting(pRxOut, Type);

                *DataBuf = pHandler->GetResult(m_8080Rslt->m_Result, Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos,
                                         DataSize, DataType);
            }
            else
            {
                SetWifiFlatnessDataFitting(pRxOut, Type);

                *DataBuf = pHandler->GetResult(&m_8080Rslt->m_Result[Stream], Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos,
                                         DataSize, DataType);
            }
        }
    }
    else
    {
        if (m_RxInData[0].aStChIn[0].vht80_80MEnable && (Segment == 2))
        {
            pRxOut = m_RxOutData[1];
        }

        pHandler = AlgResult::Instance().GetResultItemHandler(Type);
        if (pHandler == nullptr)
        {
            //WTLog::Instance().LOGERR(WT_RESULT_NOT_EXIST, "result type not exist");
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "result type not exist" << endl;
            return WT_RESULT_NOT_EXIST;
        }
        SetWifiFlatnessDataFitting(pRxOut, Type);

        if(Type == WT_RES_HMAT_INIT_RMS_DB_NO_GAP || Type == WT_RES_HMAT_INIT_RMS_DB || Type == WT_RES_HMAT_INIT_POWER_PEAK || Type ==WT_RES_HMAT_INIT_STREAM_COUNT)
        {
            *DataBuf = pHandler->GetResult(pRxOut, Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType, true);
        }
        else
        {
            *DataBuf = pHandler->GetResult(pRxOut, Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
        }
    }

    //8080 ax datainfo用户部分结果需要从合并后的结果中获取
    if (*DataBuf != nullptr && m_RxInData[0].aStChIn[0].vht80_80MEnable && Type == WT_RES_DATA_INFO)
    {
        AlgResult::Get8080DataInfo(&m_8080Rslt->m_Result[Stream], *DataBuf);
    }

    //仅ax 8080要取合并结果的，特殊处理下
    int Demo = pRxOut->aStCh[0].local->demod_mode;
    if (m_RxInData[0].aStChIn[0].vht80_80MEnable && WT_DEMOD_11AX_20M <= Demo && Demo <= WT_DEMOD_11AX_160_160M)
    {
        if(WT_RES_PSDU_DECOMPOSED_INFO == Type)
        {
            *DataBuf = AlgResult::GetAx8080PsduDecomposeInfo(&m_8080Rslt->m_Result[Stream], Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize);
        }else if(WT_RES_A_MPDU == Type)
        {
            *DataBuf = AlgResult::Get8080AmpduInfo(&m_8080Rslt->m_Result[Stream], Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize);
        }else if(WT_RES_SERVICE_FIELD_INFO == Type)
        {
            *DataBuf = AlgResult::GetAx8080ServiceFieldInfo(&m_8080Rslt->m_Result[Stream], Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize);
        }
        else if(WT_RES_MPDU_EOF_PADDING_INFO == Type)
        {
            *DataBuf = AlgResult::GetAx8080AmpduEofPaddingInfo(&m_8080Rslt->m_Result[Stream], Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize);
        }
    }

    //Datainfo 中如果包含bw的，要把bw除下配置的clockrate，带宽压缩结果
    if(*DataBuf != nullptr && Type == WT_RES_DATA_INFO)
    {
        int Demode = pRxOut->aStCh[0].demod_mode;
        //WTLog::Instance().WriteLog(LOG_DEBUG, "get vsa result Demode= %d\n",Demode);
        if(m_RxInData[0].aStChIn[0].clockrate == 0)
        {
            Ret = WT_RESULT_UNVALID;
            wtlog::error(SOURCE_LOCATION, Ret, "m_RxInData[0].aStChIn[0].clockrate == 0, You can't use 0 as a denominator");
        }
        else
        {
            AlgResult::CalculateBW(Demode, m_RxInData[0].aStChIn[0].clockrate, *DataBuf);
        }
    }

    //结果数据可能保存再结果buffer中也可能直接是rxout中的地址，如果是保存在结果buffer中则需要更新buffer位置
    if (*DataBuf == nullptr)
    {
        Ret = WT_RESULT_UNVALID;
        wtlog::error(SOURCE_LOCATION, Ret, "get VSA result failed");
    }
    else if ((ulong)(*DataBuf) >= (ulong)static_cast<char*>(m_ResultBuf.get())
             && (ulong)(*DataBuf) < (ulong)static_cast<char*>(m_ResultBuf.get()) + m_ResultBufLen)
    {
        if ((m_BufPos + DataSize) > m_ResultBufLen)
        {
            Ret = WT_ALLOC_FAILED;
            wtlog::error(SOURCE_LOCATION, Ret, "get VSA result data len out of result buf range");
        }
        else
        {
            m_BufPos += DataSize;
        }
    }
    return Ret;
}

bool Analysis::CheckIfNeedGetResultFrom8080CombRst(const string &Type, StOutInfo *pRxOut)
{
    int Demo = pRxOut->aStCh[0].local->demod_mode;

    if ((WT_DEMOD_11AC_20M <= Demo && Demo <= WT_DEMOD_11AC_80_80M) ||
        (WT_DEMOD_11BE_20M <= Demo && Demo <= WT_DEMOD_11BE_160_160M))
    {
        if (WT_RES_MU_MIMO_ALL_USER_CONST_DATA == Type || 
            WT_RES_MU_MIMO_ALL_USER_CONST_REF == Type || 
            WT_RES_MU_MIMO_ALL_USER_CONST_PILOT == Type)
        {
            return false;
        }
    }

    return true;
}

int Analysis::Get11axUserVsaResult(int UserID, const string &Type, int Stream, int Segment, void **DataBuf, int &DataSize, int &DataType)
{
    int Ret = WT_OK;
    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    if (m_ResultBuf == nullptr)
    {
         m_ResultBuf.reset(MemPool::Instance().Alloc(m_ResultBufLen));
         if (m_ResultBuf == nullptr)
         {
             WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc result buffer failed");
             return WT_ALLOC_FAILED;
         }
    }

    //WT_RES_EXPORT_PSDU_BIT == Type || WT_RES_AX_EXPORT_PSDU_BIT == Type || check mac lic目前就控制psdu bit，psdu decode，psdu compose，msdu info，tf info，这几个视图的结果输出
        //428直接放开tf info视图，448判断mac lic|| WT_RES_PSDU_DECOMPOSED_INFO == Type
    if((WT_RES_TRIGGER_FRAME_INFO == Type && WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448))
    {
        if(WT_OK != CheckMacLic(m_RxOutData[0]))
        {
            return WT_RESULT_UNVALID;
        }
    }

    if (Type == WT_RES_SEGMENT_NUM)
    {
        *DataBuf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;
        *(int *)(*DataBuf) = !m_Comb8080To160 ? GetSegmentNum() : 1;
        DataSize = sizeof(int);
        DataType = sizeof(int);
        m_BufPos += DataSize;
        return WT_OK;
    }
    else if (Type == WT_RES_TBT_SIFS)
    {
        *DataBuf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;
        *(double *)(*DataBuf) = m_FrameId < m_TBTSIFS.size()
                                    ? m_TBTSIFS[m_FrameId]
                                    : (m_TBTSIFS.size() > 1 ? m_TBTSIFS[0] : 0);
        DataSize = sizeof(double);
        DataType = sizeof(double);
        m_BufPos += DataSize;
        return WT_OK;
    }

    ResultItemHandler *pHandler = nullptr;
    StOutInfo *pRxOut = m_RxOutData[0];

    if (m_RxInData[0].aStChIn[0].scene_model == CMIMOModel)
    {
        if(Stream >= CMIMO_MAX_STREAM || Stream < 0)
        {
            return WT_STREAM_ID_ERROR;
        }
    }
    else if (Stream >= pRxOut->iNumOfCh || Stream < 0)
    {
        return WT_STREAM_ID_ERROR;
    }

    //获取8080综合结果
    if (m_RxInData[0].aStChIn[0].vht80_80MEnable && (m_Comb8080To160 || Segment == 0))
    {
        pHandler = AlgResult::Instance().Get8080ResultItemHandler(Type);
        if (pHandler == nullptr)   //先从8080结果中查询，不存在时从普通结果中查询
        {
            pHandler = AlgResult::Instance().GetResultItemHandler(Type);
            if (pHandler == nullptr)
            {
                WTLog::Instance().LOGERR(WT_RESULT_NOT_EXIST, "result type not exist");
                return WT_RESULT_NOT_EXIST;
            }

            if (Segment == 2)
            {
                pRxOut = m_RxOutData[1];
            }
            SetWifiFlatnessDataFitting(pRxOut, Type);

            *DataBuf = pHandler->Get11axUserResult(pRxOut, Stream, UserID, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
        }
        else
        {
            SetWifiFlatnessDataFitting(pRxOut, Type);

            *DataBuf = pHandler->Get11axUserResult(&m_8080Rslt->m_Result[Stream], Stream, UserID,
                                                static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
        }
    }
    else
    {
        if (m_RxInData[0].aStChIn[0].vht80_80MEnable && (Segment == 2))
        {
            pRxOut = m_RxOutData[1];
        }

        pHandler = AlgResult::Instance().GetResultItemHandler(Type);
        if (pHandler == nullptr)
        {
            WTLog::Instance().LOGERR(WT_RESULT_NOT_EXIST, "result type not exist");
            return WT_RESULT_NOT_EXIST;
        }
        SetWifiFlatnessDataFitting(pRxOut, Type);

        *DataBuf = pHandler->Get11axUserResult(pRxOut, Stream, UserID, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
    }

    //结果数据可能保存再结果buffer中也可能直接是rxout中的地址，如果是保存在结果buffer中则需要更新buffer位置
    if (*DataBuf == nullptr)
    {
        Ret = WT_RESULT_UNVALID;
        wtlog::error(SOURCE_LOCATION, Ret, "get VSA result failed");
    }
    else if ((ulong)(*DataBuf) >= (ulong)static_cast<char*>(m_ResultBuf.get())
             && (ulong)(*DataBuf) < (ulong)static_cast<char*>(m_ResultBuf.get()) + m_ResultBufLen)
    {
        if ((m_BufPos + DataSize) > m_ResultBufLen)
        {
            Ret = WT_ALLOC_FAILED;
            wtlog::error(SOURCE_LOCATION, Ret, "get VSA result data len out of result buf range");
        }
        else
        {
            m_BufPos += DataSize;
        }
    }
    return Ret;
}

int Analysis::GetCommResult(int Segment, int Stream, const VsaCommResult **Result)
{
    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    if (m_ResultBuf == nullptr)
    {
         m_ResultBuf.reset(MemPool::Instance().Alloc(m_ResultBufLen));
         if (m_ResultBuf == nullptr)
         {
             WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc result buffer failed");
            return WT_ALLOC_FAILED;
         }
    }

    int Len = 0;
    void *Addr = nullptr;
    char *Buf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;

    if (GetSegmentNum() == 1)
    {
        Segment = 1;
    }

    if (Segment > 0)
    {
        Addr = AlgResult::GetCommResult(m_RxOutData[Segment - 1], Stream, Buf, Len);
        m_Demod = m_RxOutData[Segment - 1]->aStCh[0].demod_mode;
    }
    else
    {
        Addr = AlgResult::GetCommResult(&m_8080Rslt->m_Result[Stream], Stream, Buf, Len);
        m_Demod = m_8080Rslt->m_Result[Stream].DemodMode;
    }

    if (Addr == nullptr)
    {
        WTLog::Instance().LOGERR(WT_RESULT_UNVALID, "get common result failed");
        return WT_RESULT_UNVALID;
    }

    m_BufPos += sizeof(VsaCommResult);
    *Result = (VsaCommResult *)Buf;

    return WT_OK;
}

int Analysis::GetSleCommResult(int Segment, int Stream, VsaSleCommResult2 **Result)
{
    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    if (m_ResultBuf == nullptr)
    {
        m_ResultBuf.reset(MemPool::Instance().Alloc(m_ResultBufLen));
        if (m_ResultBuf == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc result buffer failed");
            return WT_ALLOC_FAILED;
        }
    }

    int Len = 0;
    void *Addr = nullptr;
    char *Buf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;

    if (GetSegmentNum() == 1)
    {
        Segment = 1;
    }

    Addr = AlgResult::GetCommResult(m_RxOutData[Segment - 1], Stream, Buf, Len);
    m_Demod = m_RxOutData[Segment - 1]->aStCh[0].demod_mode;
    if (Addr == nullptr)
    {
        WTLog::Instance().LOGERR(WT_RESULT_UNVALID, "get common result failed");
        return WT_RESULT_UNVALID;
    }

    m_BufPos += sizeof(VsaSleCommResult2);
    *Result = (VsaSleCommResult2 *)Buf;

    return WT_OK;
}

int Analysis::GetBTCommResult(int Segment, int Stream, VsaBTCommResult2 **Result)
{
    if (m_ResultBuf == nullptr)
    {
        m_ResultBuf.reset(MemPool::Instance().Alloc(m_ResultBufLen));
        if (m_ResultBuf == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc result buffer failed");
            return WT_ALLOC_FAILED;
        }
    }

    int Len = 0;
    void *Addr = nullptr;
    char *Buf = static_cast<char*>(m_ResultBuf.get()) + m_BufPos;

    if (GetSegmentNum() == 1)
    {
        Segment = 1;
    }

    Addr = AlgResult::GetCommResult(m_RxOutData[Segment - 1], Stream, Buf, Len);
    m_Demod = m_RxOutData[Segment - 1]->aStCh[0].demod_mode;
    if (Addr == nullptr)
    {
        WTLog::Instance().LOGERR(WT_RESULT_UNVALID, "get common result failed");
        return WT_RESULT_UNVALID;
    }

    m_BufPos += sizeof(VsaBTCommResult2);
    *Result = (VsaBTCommResult2 *)Buf;

    return WT_OK;
}

void Analysis::CalculateAvgSpectMargin(int demodMode, int Stream, VsaCommResult *AvgResult)
{
    Complex     *pConstMask = nullptr;
	int         constMaskLen = 0;
    for (int i = 0; i < sizeof(stMaskMapping) / sizeof(stMaskMapping[0]); i++)
	{
		if (demodMode == stMaskMapping[i].demod_mode)
		{
			pConstMask = stMaskMapping[i].mask_data;
			constMaskLen = stMaskMapping[i].mask_cnt;
			break;
		}
	}

    if (nullptr == pConstMask)
	{
		return;
	}

    if(AvgResult != nullptr)
    {
        double          *pSpectData = AvgResult->Spectrum;
        int             spectSize = AvgResult->SpectNum;
        stSpectral      *pSpect = &m_RxOutData[0]->aStCh[Stream].spect;
        Complex         *pSpectMask = pSpect->spectral_mask;
        int             spectRbw = pSpect->spectral_rbw;
        if(spectRbw <= 0) return;
        int             halfSpectSpan = pSpect->spectral_span / 2;
        int adcFreq = pSpect->spectral_span;
        int sceneMode = m_RxOutData[0]->aStCh[Stream].scene_model;

        double      centerPwr = 0;
        int         cnt = 0;
        // int         posIndex = 0;
        // int         negIndex = 0;
        double      freq = 0;
        for(int i = 0; i < constMaskLen - 1; i++)
        {
            int flag = 0;
            double marginFreq = 0;
            double marginPwr = 99999;

            if((pConstMask[i][1] == centerPwr) && (pConstMask[i + 1][1] == centerPwr))
            {
                continue;
            }

            if(abs(pConstMask[i + 1][0] - pConstMask[i][0]) < (2 * KHz))
            {
                continue;
            }

            if((sceneMode != CMIMOModel)
                && ((demodMode == WT_DEMOD_11AC_40M) || (demodMode == WT_DEMOD_11N_40M)
                || (demodMode == WT_DEMOD_11AC_80M) || (demodMode == WT_DEMOD_11AC_80_80M))
                && ((0 == i) || (8 == i))
                && (120000000 == adcFreq))
            {
                //AvgResult->margin_vaild[cnt] = TRUE;
                marginFreq = (0 == i) ? pSpectMask[0][0] : pSpectMask[1199][0];
                marginPwr  = (0 == i) ? (pSpectMask[0][1] - pSpectData[0]) : (pSpectMask[1199][1] - pSpectData[1199]);
                // if(marginFreq > 0)
                // {
                //     posIndex++;
                //     pSpect->margin_index[cnt] = posIndex;
                // }
                // else
                // {
                //     negIndex++;
                //     pSpect->margin_index[cnt] = -negIndex;
                // }
                AvgResult->margin_freq_vs_pwr[cnt][0] = marginFreq;
                AvgResult->margin_freq_vs_pwr[cnt][1] = marginPwr;
                cnt ++;
            }

            for(int k = 0; k < spectSize; k++)
            {
                freq = pSpectMask[k][0];     //spectRbw*k - halfSpectSpan;
                if((freq > pConstMask[i][0]) && (freq <= pConstMask[i + 1][0]))
                {
                    if(freq < 0)
                    {
                        if((freq + halfSpectSpan) < 1)
                        {
                            continue;
                        }
                    }
                    flag = 1;
                    if(marginPwr > (pSpectMask[k][1] - pSpectData[k]))
                    {
                        marginFreq = freq;
                        marginPwr = (pSpectMask[k][1] - pSpectData[k]);
                    }
                }
            }
            if(flag)
            {
                //AvgResult->margin_vaild[cnt] = TRUE;
                // if(marginFreq > 0)
                // {
                //     posIndex++;
                //     pSpect->margin_index[cnt] = posIndex;
                // }
                // else
                // {
                //     negIndex++;
                //     pSpect->margin_index[cnt] = -negIndex;
                // }
                AvgResult->margin_freq_vs_pwr[cnt][0] = marginFreq;
                AvgResult->margin_freq_vs_pwr[cnt][1] = marginPwr;
                cnt ++;
            }
        }
        AvgResult->SpectMarginNum = cnt;
    }

    // for(int i  = 0; i < AvgResult->SpectMarginNum; i ++)
    // {
    //     WTLog::Instance().WriteLog(LOG_DEBUG, "margin=(%d, %lf, %lf)\n", i, AvgResult->margin_freq_vs_pwr[i][0], AvgResult->margin_freq_vs_pwr[i][1]);
    // }
}

int Analysis::GetAvgResult(int Segment, int Stream, const VsaCommResult **AvgResult,
                           const VsaCommResult **MaxResult, const VsaCommResult **MinResult)
{
    (void)Segment;

    if (m_AlgAvg[0] != nullptr && m_AvgCnt)
    {
        *AvgResult = &m_AlgAvg[0]->GetAvg(Stream);
        *MaxResult = &m_AlgAvg[0]->GetMax(Stream);
        *MinResult = &m_AlgAvg[0]->GetMin(Stream);
        //通过api获取平均结果时，计算下平均后的频谱的margin值，再获取结果~
        VsaCommResult *TmpAvgResult = const_cast<VsaCommResult *>(*AvgResult);
        CalculateAvgSpectMargin(m_RxOutData[0]->aStCh[Stream].demod_mode, Stream, TmpAvgResult);

        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no avg result exist");
        return WT_NO_AVG_RESULT;
    }
}

int Analysis::GetSleAvgResult(int Segment, int Stream, const VsaSleCommResult **AvgSleResult,
                     const VsaSleCommResult **MaxSleResult, const VsaSleCommResult **MinSleResult)
{
    (void)Segment;
    if (m_AlgAvg[0] != nullptr && m_AvgCnt)
    {
        *AvgSleResult = &m_AlgAvg[0]->GetSLEAvg(Stream);
        *MaxSleResult = &m_AlgAvg[0]->GetSLEMax(Stream);
        *MinSleResult = &m_AlgAvg[0]->GetSLEMin(Stream);
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no Sle avg result exist");
        return WT_NO_AVG_RESULT;
    }
}

int Analysis::GetBTAvgResult(int Segment, int Stream, const VsaBTCommResult **AvgBTResult,
                           const VsaBTCommResult **MaxBTResult, const VsaBTCommResult **MinBTResult)
{
    (void)Segment;
    if (m_AlgAvg[0] != nullptr && m_AvgCnt)
    {
        *AvgBTResult = &m_AlgAvg[0]->GetBTAvg(Stream);
        *MaxBTResult = &m_AlgAvg[0]->GetBTMax(Stream);
        *MinBTResult = &m_AlgAvg[0]->GetBTMin(Stream);
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no BT avg result exist");
        return WT_NO_AVG_RESULT;
    }
}

#include "basefun.h"
#define VAL_MIN -999.99 //注意以此对照的数据量级
int Analysis::GetAvgResultComposite(int Segment, const VsaBaseResult **AvgResultComposite)
{
    // TODO暂时不支持80+80时获取综合平均的功能
    if (Segment > 0)
    {
        Segment--;
    }

    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    if (m_AlgAvg[Segment] != nullptr && m_AvgCnt)
    {
        VsaBaseResult CompositeResult;
        VsaBaseResult CompositeCnt;
        VsaBaseResult Result;

        //memset(&CompositeCnt, 0, sizeof(VsaBaseResult));
        CompositeCnt.Reset();

#define AVERAGE(member)\
    if(Basefun::CompareDouble(Result.member, VAL_MIN) != 0){\
        if(Basefun::CompareDouble(CompositeResult.member, VAL_MIN) == 0){\
            CompositeResult.member = Result.member; }\
        else{\
            CompositeResult.member = (CompositeResult.member * CompositeCnt.member + Result.member) / (CompositeCnt.member + 1);\
        }\
        CompositeCnt.member ++;\
    }
#define AVERAGE_OFFSET(member)\
        if(CompositeCnt.member == 0){\
            CompositeResult.member = Result.member; }\
        else{\
            CompositeResult.member = (CompositeResult.member * CompositeCnt.member + Result.member) / (CompositeCnt.member + 1);\
        }\
        CompositeCnt.member ++;

#define SUM(member)\
        if(Basefun::CompareDouble(Result.member, VAL_MIN) != 0){\
            if(Basefun::CompareDouble(CompositeResult.member, VAL_MIN) == 0){\
                CompositeResult.member = Result.member; }\
            else{\
                CompositeResult.member = CompositeResult.member + Result.member;\
            }\
        }
#define ABS_MAX(member)\
        if(Basefun::CompareDouble(Result.member, VAL_MIN) != 0){\
            if(Basefun::CompareDouble(CompositeResult.member, VAL_MIN) == 0){\
                CompositeResult.member = Result.member; }\
            else{\
                CompositeResult.member = (abs(CompositeResult.member) > abs(Result.member))? CompositeResult.member : Result.member;\
            }\
        }
#define MAX(member)\
        if(Basefun::CompareDouble(Result.member, VAL_MIN) != 0){\
            if(Basefun::CompareDouble(CompositeResult.member, VAL_MIN) == 0){\
                CompositeResult.member = Result.member; }\
            else{\
                CompositeResult.member = (CompositeResult.member > Result.member) ? CompositeResult.member : Result.member;\
            }\
        }
        for(int i = 0; i < m_RxOutData[0]->iNumOfCh; i++)
        {
            //memcpy(&Result, &m_AlgAvg[Segment]->GetAvg(i), sizeof(VsaBaseResult));
            Result = m_AlgAvg[Segment]->GetAvg(i);

            if(Basefun::CompareDouble(Result.PowerFrame, VAL_MIN) != 0)
                Result.PowerFrame = pow(10,Result.PowerFrame/10);
            SUM(PowerFrame);
            if(Basefun::CompareDouble(Result.PowerAll, VAL_MIN) != 0)
                Result.PowerAll = pow(10,Result.PowerAll/10);
            SUM(PowerAll);
            MAX(PowerPeak);
            if(Basefun::CompareDouble(Result.EvmAll, VAL_MIN) != 0)
                Result.EvmAll = pow(10,Result.EvmAll/20);
            AVERAGE(EvmAll);
            MAX(EvmPeak);
            if(Basefun::CompareDouble(Result.EvmData, VAL_MIN) != 0)
                Result.EvmData = pow(10,Result.EvmData/20);
            AVERAGE(EvmData);
            if(Basefun::CompareDouble(Result.EvmPilot, VAL_MIN) != 0)
                Result.EvmPilot = pow(10,Result.EvmPilot/20);
            AVERAGE(EvmPilot);
            AVERAGE(EvmPsdu);
            AVERAGE(EvmShrPhr);
            AVERAGE_OFFSET(FreqOffset);
            MAX(CarrierLeakage);
            AVERAGE(SymClkErr);
            ABS_MAX(PhaseErr);
            ABS_MAX(IQImbAmp);
            ABS_MAX(IQImbPhase);
        }

        if(Basefun::CompareDouble(CompositeResult.PowerFrame, VAL_MIN) != 0)
            CompositeResult.PowerFrame = 10*log10(CompositeResult.PowerFrame);
        if(Basefun::CompareDouble(CompositeResult.PowerAll, VAL_MIN) != 0)
            CompositeResult.PowerAll = 10*log10(CompositeResult.PowerAll);

        if(Basefun::CompareDouble(CompositeResult.EvmAll, VAL_MIN) != 0)
            CompositeResult.EvmAll = 20*log10(CompositeResult.EvmAll);
        if(Basefun::CompareDouble(CompositeResult.EvmData, VAL_MIN) != 0)
            CompositeResult.EvmData = 20*log10(CompositeResult.EvmData);
        if(Basefun::CompareDouble(CompositeResult.EvmPilot, VAL_MIN) != 0)
            CompositeResult.EvmPilot = 20*log10(CompositeResult.EvmPilot);

        //memcpy(&AvgRstComposite, &CompositeResult, sizeof(VsaBaseResult));
        AvgRstComposite = CompositeResult;

        *AvgResultComposite = &AvgRstComposite;
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no avg result exist");
        return WT_NO_AVG_RESULT;
    }
}

//内存无需释放，只要将位置清零即可，节省时间
void Analysis::FreeVsaResult(void)
{
    m_ResultBuf.reset(nullptr);
    m_ResultBuf = nullptr;
    m_BufPos = 0;
}

int Analysis::SaveSignal(int DataType, const string &File)
{
    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    SigFile OutFile(WTConf::GetDir() + "/signal/" + File, SigFile::WRITE);
    SigFileInfo FileInfo;
    FileInfo.ChainNum = m_RxInData[0].iNumOfChIn;
    FileInfo.MimoType = m_RxInData[0].aStChIn[0].scene_model == SwitchMIMO ? 1 : 0;
    if (m_RxInData[0].aStChIn[0].vht80_80MEnable == 0)
    {
        FileInfo.FileType = FILE_TYPE_NORMAL;
    }
    else if (m_Comb8080To160)
    {
        FileInfo.FileType = FILE_TYPE_160;
    }
    else
    {
        FileInfo.FileType = FILE_TYPE_8080;
    }

    int Ret = FileInfo.CreateSigHeader(FileInfo.ChainNum, FileInfo.FileType);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "CreateSigHeader failed");
        return Ret;
    }

    int SegNum = FileInfo.FileType ? 2 : 1;
    SigDataHeader *SigHeader = FileInfo.SigHeader;

    for (int i = 0; i < FileInfo.ChainNum; i++)
    {
        for (int Seg = 0; Seg < SegNum; Seg++)
        {
            RX_InDat *pInData = &m_RxInData[Seg].aStChIn[i];
            RX_OutDat *pOutData = &m_RxOutData[Seg]->aStCh[i];

            if (DataType == WT_COMPESATE_DATA)
            {
                SigHeader->Data = pOutData->capturedata;
                SigHeader->SampleCount = pOutData->capturecnt;
                SigHeader->DataType = enDataFormat_Float64;
                SigHeader->ModType = pInData->demod_mode;
                SigHeader->SamplingRate = pInData->adc_freq;
            }
            else
            {
                SigHeader->Data = pInData->capturedata;
                SigHeader->SampleCount = pInData->capturecnt;
                SigHeader->DataType = pInData->capturedata_format;
                SigHeader->ModType = pInData->demod_mode;
                SigHeader->SamplingRate = pInData->adc_freq;
                SigHeader->RefLevel = pInData->referenceLevel;
                SigHeader->TriggerLevel = pInData->trig_pwr;
                SigHeader->FreqOffset = pInData->frequencyOffsetHz;

                SigHeader->RFGain = pInData->gain;
                SigHeader->CenterFreq = pInData->rf_center_freq;
                SigHeader->IQGainImb = pInData->IQImb_Amp;
                SigHeader->IQPhaseImb = pInData->IQImb_Phase;
                SigHeader->DCOffsetI = pInData->IDCOffset;
                SigHeader->DCOffsetQ = pInData->QDCOffset;
                SigHeader->TimeSkew = pInData->TimeSkew;

                SigHeader->RfResponse.Response = pInData->rf_response;
                SigHeader->RfResponse.FreqCount = pInData->rf_response_len;
                SigHeader->BBResponse.Response = pInData->bb_response;
                SigHeader->BBResponse.FreqCount = pInData->bb_response_len;
                SigHeader->NSResponse.Response = pInData->noise_response;
                SigHeader->NSResponse.FreqCount = pInData->noise_response_len;

                if (pInData->spectOffset && pInData->spectOffset->valid)
                {
                    SigHeader->SpectOffset.Valid = pInData->spectOffset->valid;
                    SigHeader->SpectOffset.PointCnt = OFFSET_POINT_COUNT;
                    SigHeader->SpectOffset.First = pInData->spectOffset->firstOffset;
                    SigHeader->SpectOffset.Last = pInData->spectOffset->lastOffset;
                }
                else
                {
                    SigHeader->SpectOffset.Valid = 0;
                }
            }

            SigHeader->ClockRate = pInData->clockrate;  //带宽压缩比
            SigHeader++;
        }
    }

    return OutFile.Save(FileInfo);
}

bool Analysis::CheckDemodLic(int Demode)
{
    switch(Demode)
    {
        case WT_DEMOD_11AG:
            Demode = WT_11G;
            break;

        case WT_DEMOD_11B:
            Demode = WT_11B;
            break;

        case WT_DEMOD_11N_20M:
        case WT_DEMOD_11N_40M:
            Demode = WT_11N;
            break;

        case WT_DEMOD_11AC_20M:
        case WT_DEMOD_11AC_40M:
        case WT_DEMOD_11AC_80M:
            Demode = WT_11AC;
            break;

        case WT_DEMOD_11AC_160M:
            Demode = WT_11AC;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK)
            {
                Demode = WT_160M;
            }
            else
            {
                return false;
            }
            break;

        case WT_DEMOD_11AC_80_80M:
            Demode = WT_11AC;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK)
            {
                Demode = WT_MULIT_SEG;
            }
            else
            {
                return false;
            }
            break;

        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
            Demode = WT_11AX;
            break;
        case WT_DEMOD_11AX_160M:
            Demode = WT_11AX;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK)
            {
                Demode = WT_160M;
            }
            else
            {
                return false;
            }
            break;


        case WT_DEMOD_11AX_80_80M:
            Demode = WT_11AX;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK && License::Instance().CheckBusinessLicItem(WT_160M) == WT_OK)
            {
                Demode = WT_MULIT_SEG;
            }
            else
            {
                return false;
            }
            break;
        case WT_DEMOD_11AX_160_160M:
            Demode = WT_11AX;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK && License::Instance().CheckBusinessLicItem(WT_320M) == WT_OK)
            {
                Demode = WT_MULIT_SEG;
            }
            else
            {
                return false;
            }
            break;

        case WT_DEMOD_11BE_20M:
        case WT_DEMOD_11BE_40M:
        case WT_DEMOD_11BE_80M:
            Demode = WT_11BE;
            break;
        case WT_DEMOD_11BE_160M:
            Demode = WT_11BE;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK)
            {
                Demode = WT_160M;
            }
            else
            {
                return false;
            }
            break;
        case WT_DEMOD_11BE_320M:
            Demode = WT_11BE;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK)
            {
                Demode = WT_320M;
            }
            else
            {
                return false;
            }
            break;
        case WT_DEMOD_11BE_80_80M:
            Demode = WT_11BE;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK && License::Instance().CheckBusinessLicItem(WT_160M) == WT_OK)
            {
                Demode = WT_MULIT_SEG;
            }
            else
            {
                return false;
            }
            break;
        case WT_DEMOD_11BE_160_160M:
            Demode = WT_11BE;
            if(License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK && License::Instance().CheckBusinessLicItem(WT_320M) == WT_OK)
            {
                Demode = WT_MULIT_SEG;
            }
            else
            {
                return false;
            }
            break;
        case WT_DEMOD_BT:
            Demode = WT_BT;
            break;

        case WT_DEMOD_ZIGBEE:
            Demode = WT_ZIGBEE;
            break;

        case WT_DEMOD_11AH_1M:
        case WT_DEMOD_11AH_2M:
        case WT_DEMOD_11AH_4M:
        case WT_DEMOD_11AH_8M:
        case WT_DEMOD_11AH_16M:
            Demode = WT_11AH;
            break;

        //case WT_DEMOD_CW:
        case WT_DEMOD_UNKNOW:
            Demode = WT_GPRF;
            break;

        case WT_DEMOD_ZWAVE:
            Demode = WT_ZWAVE;
            break;

        case WT_DEMOD_11BA_20M:
        case WT_DEMOD_11BA_40M:
        case WT_DEMOD_11BA_80M:
            Demode = WT_11BA;
            break;

        case WT_DEMOD_11AZ_20M:
        case WT_DEMOD_11AZ_40M:
        case WT_DEMOD_11AZ_80M:
        case WT_DEMOD_11AZ_160M:
            Demode = WT_11AZ;
            break;
        case WT_DEMOD_GLE:
            Demode = WT_SLE;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            Demode = WT_WISUN;
            break;
        default:
            Demode = WT_GPRF;
            break;
    }

    return License::Instance().CheckBusinessLicItem((WT_PROT_E)Demode) == WT_OK;
}

bool Analysis::IsTFFrame(StOutInfo *pRxOut)
{
    bool IsTF = false; //判断是否为tf

    if(pRxOut->aStCh[0].local != nullptr)
    {
        int Demode = pRxOut->aStCh[0].local->demod_mode;
        if (pRxOut->SISOFrm != nullptr)
        {
            switch (Demode)
            {
                case WT_DEMOD_11AG:
                {
                    if (pRxOut->SISOFrm->Db11ag.TFvalidflag == 1)
                    {
                        IsTF = true;
                    }
                    break;
                }
                case WT_DEMOD_11N_20M:
                case WT_DEMOD_11N_40M:
                {
                    if (pRxOut->frametype == WT_DEMOD_SISO && pRxOut->SISOFrm->Db11N.TFvalidflag == 1)
                    {
                        IsTF = true;
                    }
                    break;
                }
                case WT_DEMOD_11AC_20M:
                case WT_DEMOD_11AC_40M:
                case WT_DEMOD_11AC_80M:
                case WT_DEMOD_11AC_160M:
                case WT_DEMOD_11AC_80_80M:
                {
                    if (pRxOut->frametype == WT_DEMOD_SISO && pRxOut->SISOFrm->Db11ac.TFvalidflag == 1)
                    {
                        IsTF = true;
                    }
                    break;
                }
                case WT_DEMOD_11AX_20M:
                case WT_DEMOD_11AX_40M:
                case WT_DEMOD_11AX_80M:
                case WT_DEMOD_11AX_160M:
                case WT_DEMOD_11AX_80_80M:
                case WT_DEMOD_11AX_160_160M:
                {
                    if (pRxOut->frametype == WT_DEMOD_SISO && pRxOut->SISOFrm->Db11ax.he_sig_common.TFvalidflag == 1)
                    {
                        IsTF = true;
                    }
                    break;
                }
                default:
                    break;
            }
        }
    }
    return IsTF;
}

bool Analysis::IsMuMimo(StOutInfo *pRxOut)
{
    bool IsMuMimo = false;

    if (pRxOut->SISOFrm != nullptr)
    {
        if(pRxOut->frametype == WT_DEMOD_MIMO_11AX) //ax
        {
            if(pRxOut->aStFrm11ax->CrossChax.he_sig_common.mu_mimo_flag == 1)
            {
                IsMuMimo = true;
            }
        }
        else if(pRxOut->frametype == WT_DEMOD_MIMO_11AC)
        {
            if(pRxOut->aStFrm11ac->CrossChac.MuMimoFlag == 1)//mu mimo
            {
                IsMuMimo = true;
            }
        }
    }
    return IsMuMimo;
}

int Analysis::CheckSlaveDemodLic(std::vector<LicItemInfo> &SlaveLicInfo)
{
    int Ret = WT_OK;
    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    StOutInfo *pRxOut = m_RxOutData[0];
    if(pRxOut->aStCh[0].local == nullptr)
    {
        pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
        return Ret;
    }

    std::vector<int> DemoVec;
    int Demode = pRxOut->aStCh[0].local->demod_mode;
    //WTLog::Instance().WriteLog(LOG_DEBUG, "##Get result demo = %d\n",Demode);
    //基本业务lic的判断
    switch(Demode)
    {
        case WT_DEMOD_11AG:
            Demode = WT_11G;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11B:
            Demode = WT_11B;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11N_20M:
        case WT_DEMOD_11N_40M:
            Demode = WT_11N;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AC_20M:
        case WT_DEMOD_11AC_40M:
        case WT_DEMOD_11AC_80M:
            Demode = WT_11AC;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AC_160M:
            Demode = WT_11AC;
            DemoVec.push_back(Demode);

            Demode = WT_160M;
            DemoVec.push_back(Demode);
            break;
        case WT_DEMOD_11AC_80_80M:
            Demode = WT_11AC;
            DemoVec.push_back(Demode);

            Demode = WT_160M;
            DemoVec.push_back(Demode);

            Demode = WT_MULIT_SEG;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
            Demode = WT_11AX;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AX_160M:
            Demode = WT_11AX;
            DemoVec.push_back(Demode);

            Demode = WT_160M;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AX_80_80M:
            Demode = WT_11AX;
            DemoVec.push_back(Demode);

            Demode = WT_160M;
            DemoVec.push_back(Demode);

            Demode = WT_MULIT_SEG;
            DemoVec.push_back(Demode);
            break;
        case WT_DEMOD_11AX_160_160M:
            Demode = WT_11AX;
            DemoVec.push_back(Demode);

            Demode = WT_MULIT_SEG;
            DemoVec.push_back(Demode);

            Demode = WT_320M;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11BE_20M:
        case WT_DEMOD_11BE_40M:
        case WT_DEMOD_11BE_80M:
            Demode = WT_11BE;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11BE_160M:
            Demode = WT_11BE;
            DemoVec.push_back(Demode);

            Demode = WT_160M;
            DemoVec.push_back(Demode);
            break;
        case WT_DEMOD_11BE_80_80M:
            Demode = WT_11BE;
            DemoVec.push_back(Demode);

            Demode = WT_MULIT_SEG;
            DemoVec.push_back(Demode);

            Demode = WT_160M;
            DemoVec.push_back(Demode);
            break;
        case WT_DEMOD_11BE_320M:
            Demode = WT_11BE;
            DemoVec.push_back(Demode);

            Demode = WT_320M;
            DemoVec.push_back(Demode);
            break;
        case WT_DEMOD_11BE_160_160M:
            Demode = WT_11BE;
            DemoVec.push_back(Demode);

            Demode = WT_MULIT_SEG;
            DemoVec.push_back(Demode);

            Demode = WT_320M;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_BT:
            Demode = WT_BT;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_ZIGBEE:
            Demode = WT_ZIGBEE;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AH_1M:
        case WT_DEMOD_11AH_2M:
        case WT_DEMOD_11AH_4M:
        case WT_DEMOD_11AH_8M:
        case WT_DEMOD_11AH_16M:
            Demode = WT_11AH;
            DemoVec.push_back(Demode);
            break;

        //case WT_DEMOD_CW:
        case WT_DEMOD_UNKNOW:
            Demode = WT_GPRF;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_ZWAVE:
            Demode = WT_ZWAVE;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11BA_20M:
        case WT_DEMOD_11BA_40M:
        case WT_DEMOD_11BA_80M:
            Demode = WT_11BA;
            DemoVec.push_back(Demode);
            break;

        case WT_DEMOD_11AZ_20M:
        case WT_DEMOD_11AZ_40M:
        case WT_DEMOD_11AZ_80M:
        case WT_DEMOD_11AZ_160M:
            Demode = WT_11AZ;
            DemoVec.push_back(Demode);
            break;

        default:
            Demode = WT_GPRF;
            DemoVec.push_back(Demode);
            break;
    }

    //TF 判断
//    if(IsTFFrame(pRxOut))
//    {
//        Demode = WT_MAC_INTER_AC;
//        DemoVec.push_back(Demode);
//    }

    //tb 判断

    //mumimo 判断
//    if(IsMuMimo(pRxOut))
//    {
//        Demode = WT_MUMIMO;
//        DemoVec.push_back(Demode);
//    }

    Ret = License::Instance().CheckSlaveBusinessLicItem(DemoVec, SlaveLicInfo);
    //WTLog::Instance().WriteLog(LOG_DEBUG, "CheckSlaveBusinessLicItem result = %d \n",Ret);
    if(Ret != WT_OK)
    {
        pRxOut->aStCh[0].demod_mode = WT_DEMOD_UNKNOW;
        if(m_8080Rslt != nullptr)
        {
            for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
            {
                m_8080Rslt->m_Result[i].DemodMode = WT_DEMOD_UNKNOW;
                memset((char *)&m_8080Rslt->m_Result[i].Evm, 0, sizeof(m_8080Rslt->m_Result[i].Evm));
            }
        }
    }
    return Ret;
}

void Analysis::SetStaticIQParam(int Segment, double Ampl, double Phase, double TimeSkew)
{
    Segment--;
    m_StaticIQParam[Segment].IQAmpl = Ampl;
    m_StaticIQParam[Segment].IQPhase = Phase;
    m_StaticIQParam[Segment].TimeSkew = TimeSkew;
    m_StaticIQParam[Segment].Valid = true;
}

void Analysis::ClrStaticIQParam(int Segment)
{
    Segment--;
    m_StaticIQParam[Segment].Valid = false;
}

int Analysis::CalcIQImblance(int Segment, double &Ampl, double &Phase, double &TimeSkew)
{
    AlgReset::ReaderLock AlgReaderLock;
    int Ret = WT_OK;
    int Len = 0;
    double TmpData;
    double *Evm = nullptr;
    double BestEvm = 999;

    double IQImbAmp = 0;
    double IQImbPhase = 0;
    double IQTimeSkew = 0;
    double Center = 0;
    int TimeSkewLen = 48;

    double Step[] = {1, 0.1, 0.01, 0.001, 0.0001};
    double Range[] = {10, 1, 0.1, 0.01, 0.001};
    bool Is8080 = m_RxInData[0].aStChIn[0].vht80_80MEnable == 1;

    m_RxInData[0].aStChIn[0].vht80_80MEnable = 0;
    AlgReset::Instance().AlgGetReaderLockT();
    WT_SetTimeskewParam(TimeSkewLen);
    AlgReset::Instance().AlgReleaseLockT();
    Segment--;

    m_RxInData[Segment].aStChIn[0].TimeSkew = 0;
    m_RxInData[Segment].aStChIn[0].IQImb_Phase = 0;
    m_RxInData[Segment].aStChIn[0].IQImb_Amp = 0;

    AlzFrameData(0);

    for (int k = 0; k < 2; k++)
    {
        Center = 0;
        BestEvm = 999;

        for (int i = 0; i < 5; i++)
        {
            for (double Skew = Center - Range[i]; Skew <= Center + Range[i]; Skew += Step[i])
            {
                m_RxInData[Segment].aStChIn[0].TimeSkew = Skew;
                AlgReset::Instance().AlgGetReaderLockT();
                //Ret = AlzFrameData(0); //MIMO_WT_Algorithm_main(&m_RxInData[0], m_RxOutData[0]);
                Ret = MIMO_WT_Algorithm_main(&m_RxInData[Segment], m_RxOutData[Segment]);
                AlgReset::Instance().AlgReleaseLockT();
                if (Ret != WT_OK)
                {
                    return Ret;
                }

                Evm = &TmpData;
                Evm = (double *)AlgResult::GetEvmAll(m_RxOutData[Segment], 0, Evm, Len);
                if (!Evm || *Evm < -999)
                {
                    continue;
                }

                if (*Evm < BestEvm)
                {
                    BestEvm = *Evm;
                    IQTimeSkew = Skew;
                }
            }

            Center = IQTimeSkew;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "best evm " << BestEvm << " , timeskew len: " << TimeSkewLen << " , timeskew " << IQTimeSkew << endl;

        BestEvm = 999;
        Center = 0;
        m_RxInData[Segment].aStChIn[0].TimeSkew = IQTimeSkew;
        TimeSkew = IQTimeSkew;

        for (int i = 0; i < 4; i++)
        {
            for (double Phase = Center - Range[i]; Phase <= Center + Range[i]; Phase += Step[i])
            {
                m_RxInData[Segment].aStChIn[0].IQImb_Phase = Phase;
                //Ret = AlzFrameData(0); //MIMO_WT_Algorithm_main(&m_RxInData[0], m_RxOutData[0]);
                AlgReset::Instance().AlgGetReaderLockT();
                Ret = MIMO_WT_Algorithm_main(&m_RxInData[Segment], m_RxOutData[Segment]);
                AlgReset::Instance().AlgReleaseLockT();
                if (Ret != WT_OK)
                {
                    return Ret;
                }

                Evm = &TmpData;
                Evm = (double *)AlgResult::GetEvmAll(m_RxOutData[Segment], 0, Evm, Len);
                if (!Evm || *Evm < -999)
                {
                    continue;
                }

                if (*Evm < BestEvm)
                {
                    BestEvm = *Evm;
                    IQImbPhase = Phase;
                }
            }

            Center = IQImbPhase;
        }

        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "best evm " << BestEvm << " , IQImbPhase " << IQImbPhase << endl;

        BestEvm = 999;
        Center = 0;
        m_RxInData[Segment].aStChIn[0].IQImb_Phase = IQImbPhase;
        Phase = IQImbPhase;

        for (int i = 0; i < 4; i++)
        {
            for (double Amp = Center - Range[i]; Amp <= Center + Range[i]; Amp += Step[i])
            {
                m_RxInData[Segment].aStChIn[0].IQImb_Amp = Amp;
                AlgReset::Instance().AlgGetReaderLockT();
                //Ret = AlzFrameData(0); //MIMO_WT_Algorithm_main(&m_RxInData[0], m_RxOutData[0]);
                Ret = MIMO_WT_Algorithm_main(&m_RxInData[Segment], m_RxOutData[Segment]);
                AlgReset::Instance().AlgReleaseLockT();
                if (Ret != WT_OK)
                {
                    return Ret;
                }

                Evm = &TmpData;
                Evm = (double *)AlgResult::GetEvmAll(m_RxOutData[Segment], 0, Evm, Len);
                if (!Evm || *Evm < -999)
                {
                    continue;
                }

                if (*Evm < BestEvm)
                {
                    BestEvm = *Evm;
                    IQImbAmp = Amp;
                }
            }

            Center = IQImbAmp;
        }

        m_RxInData[Segment].aStChIn[0].IQImb_Amp = IQImbAmp;
        Ampl = IQImbAmp;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "best evm " << BestEvm << " , IQImbAmp " << IQImbAmp << endl;
    }

    m_RxInData[0].aStChIn[0].vht80_80MEnable = Is8080 ? 1 : 0;

    //使用最终计算出来的IQ值再分析出最终的结果
    AlzFrameData(0);

    return 0;
}

int Analysis::FindBestIQParam(const std::string &File, int Demode, int fullpacket)
{
    int Ret = WT_OK;
    m_AlzParam.Demode = Demode;

    if (fullpacket)
        m_AlzParam.WifiParam.ChEstimate = WT_CH_EST_RAW_FULL;

    ExtendEVMStu ExtendEVM;
    if ((Ret = LoadSigFile(File, ExtendEVM)) != WT_OK)
    {
        return Ret;
    }

    if (m_RxInData[0].aStChIn[0].capturedata_format != enDataFormat_Int16) {

        SigFileInfo *FileInfo = nullptr;
        Ret = m_SigFile->GetContent(&FileInfo);
        if (FileInfo == nullptr)
        {
            m_RxInData[0].aStChIn[0].capturecnt = m_SigFile->GetFileSize() / sizeof(int);
            m_RxInData[0].aStChIn[0].capturedata_format = enDataFormat_Int16;
        }
    }

    double Ampl, Phase, TimeSkew;

    return CalcIQImblance(1, Ampl, Phase, TimeSkew);
}

int Analysis::AlzBeamformingCalChEstDUTTx(int Demode)
{
    int Ret = WT_OK;
    if((Ret = License::Instance().CheckBusinessLicItem(WT_IBF)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Beamforming license is invalid");
        return Ret;
    }

    m_BeamfomingAlzParam.demod = Demode;
    m_BeamfomingAlzParam.mcs = 0;
    m_BeamfomingAlzParam.IQref_Length = 0;
    m_BeamfomingAlzParam.IQref = NULL;
    m_BeamfomingAlzParam.WT_channelEst_Length = 0;
    memset(m_BeamfomingAlzParam.WT_channelEst_amplitude, 0 , sizeof(m_BeamfomingAlzParam.WT_channelEst_amplitude));
    memset(m_BeamfomingAlzParam.WT_channelEst_angle, 0, sizeof(m_BeamfomingAlzParam.WT_channelEst_angle));
    m_BeamfomingAlzParam.WT_validSpatialStreams = 0;
    m_BeamfomingAlzParam.WT_dataRate = 0;
    m_BeamfomingAlzParam.WT_mean_of_angle_deltas = 0;
    m_BeamfomingAlzParam.WT_std_of_angle_deltas = 0;
    m_BeamfomingAlzParam.DUT_Type = iBEAFORMING_BCM_STATE_1;

    AlgReset::Instance().AlgGetReaderLockT();
    if(m_RxOutData[0] == nullptr)
    {
        Ret = AllocBuf(0, 0);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    Ret = WT_Algorithm_BF_BCM4360_Calibration_channelEst(&m_RxInData[0].aStChIn[0], &m_RxOutData[0]->aStCh[0], &m_BeamfomingAlzParam);
    AlgReset::Instance().AlgReleaseLockT();

    if (Ret != WT_OK)
    {
        Ret = WT_ALG_BASE_ERROR + Ret;
        WTLog::Instance().LOGERR(Ret, "AlzBeamformingCalChEstDUTTx failed");
    }

    return Ret;
}

int Analysis::BeamformingCalChEstDUTRx(void *Data, int Len)
{
    int Ret = WT_OK;
    if((Ret = License::Instance().CheckBusinessLicItem(WT_IBF)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Beamforming license is invalid");
        return Ret;
    }

    m_BeamfomingAlzParam.DUT_channelEst_Length = Len/sizeof(double);
    memset(m_BeamfomingAlzParam.DUT_channelEst_Data, 0, sizeof(m_BeamfomingAlzParam.DUT_channelEst_Data));
    memcpy(m_BeamfomingAlzParam.DUT_channelEst_Data, Data, Len);
    m_BeamfomingAlzParam.DUT_mean_of_angle_deltas = 0;
    m_BeamfomingAlzParam.DUT_std_of_angle_deltas = 0;
    return Ret;
}

int Analysis::AlzBeamformingResult(void **RstBuf, int &Len)
{
    int Ret = WT_OK;
    if((Ret = License::Instance().CheckBusinessLicItem(WT_IBF)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Beamforming license is invalid");
        return Ret;
    }

    //是否有数据~
    if (m_RxInData[0].aStChIn[0].capturedata == nullptr)
    {
        return WT_NOT_ANALYSIS;
    }

    m_BeamfomingAlzParam.Result_Length = 0;
    memset(m_BeamfomingAlzParam.Result_angle, 0, sizeof(m_BeamfomingAlzParam.Result_angle));
    m_BeamfomingAlzParam.Result_Errorcode = 0;
    
    AlgReset::Instance().AlgGetReaderLockT();
    if(m_RxOutData[0] == nullptr)
    {
        Ret = AllocBuf(0, 0);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    Ret = WT_Algorithm_BF_BCM4360_Calibration_Result(&m_BeamfomingAlzParam);
    AlgReset::Instance().AlgReleaseLockT();
    if (Ret != WT_OK)
    {
        Ret = WT_ALG_BASE_ERROR + Ret;
        WTLog::Instance().LOGERR(Ret, "AlzBeamformingResult failed");
    }
    else
    {
        Len = m_BeamfomingAlzParam.Result_Length * sizeof(double);
        *RstBuf = m_BeamfomingAlzParam.Result_angle;
    }
    return Ret;
}

int Analysis::AlzBeamformingVerification(double &RstDiffPower)
{
    int Ret = WT_OK;
    if((Ret = License::Instance().CheckBusinessLicItem(WT_IBF)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Beamforming license is invalid");
        return Ret;
    }

    //是否有数据~
    if (m_RxInData[0].aStChIn[0].capturedata == nullptr)
    {
        return WT_NOT_ANALYSIS;
    }
    
    AlgReset::Instance().AlgGetReaderLockT();
    if(m_RxOutData[0] == nullptr)
    {
        Ret = AllocBuf(0, 0);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    Ret = WT_Algorithm_BF_BCM4360_Verification_Result(&m_RxInData[0].aStChIn[0], &m_RxOutData[0]->aStCh[0], &RstDiffPower);
    AlgReset::Instance().AlgReleaseLockT();

    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_ALG_BASE_ERROR + Ret, "AlzBeamformingVerification failed");
        Ret = WT_ALG_BASE_ERROR + Ret;
    }
    return Ret;
}

int Analysis::AlzBeamformingCalChProfile(int Demode, void *RstBuf, int &Len)
{
    int Ret = WT_OK;
    if((Ret = License::Instance().CheckBusinessLicItem(WT_IBF)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Beamforming license is invalid");
        return Ret;
    }
    m_BeamfomingAlzParam.demod = Demode;
    m_BeamfomingAlzParam.mcs = 0;
    m_BeamfomingAlzParam.IQref_Length = 0;
    m_BeamfomingAlzParam.IQref = NULL;
    m_BeamfomingAlzParam.WT_channelEst_Length = 0;
    memset(m_BeamfomingAlzParam.WT_channelEst_amplitude, 0 , sizeof(m_BeamfomingAlzParam.WT_channelEst_amplitude));
    memset(m_BeamfomingAlzParam.WT_channelEst_angle, 0, sizeof(m_BeamfomingAlzParam.WT_channelEst_angle));
    m_BeamfomingAlzParam.WT_validSpatialStreams = 0;
    m_BeamfomingAlzParam.WT_dataRate = 0;
    m_BeamfomingAlzParam.WT_mean_of_angle_deltas = 0;
    m_BeamfomingAlzParam.WT_std_of_angle_deltas = 0;
    m_BeamfomingAlzParam.DUT_Type = iBEAFORMING_MTK_STATE_1;
    
    AlgReset::Instance().AlgGetReaderLockT();
    if(m_RxOutData[0] == nullptr)
    {
        Ret = AllocBuf(0, 0);
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }
    Ret = WT_Algorithm_BF_MTK_Calibration_channelEst(&m_RxInData[0].aStChIn[0], &m_RxOutData[0]->aStCh[0], &m_BeamfomingAlzParam);
    AlgReset::Instance().AlgReleaseLockT();

    if (Ret != WT_OK)
    {
        Ret = WT_ALG_BASE_ERROR + Ret;
        WTLog::Instance().LOGERR(Ret, "AlzBeamformingCalChEstDUTTx failed");
    }
    else
    {
        int StreamCnt = m_RxOutData[0]->iNumOfCh;
        Len = m_BeamfomingAlzParam.WT_channelEst_Length * sizeof(double) *2 * StreamCnt;
        //WTLog::Instance().WriteLog(LOG_DEBUG, "##Len=%d,m_BeamfomingAlzParam.WT_channelEst_Length=%d\n",Len,m_BeamfomingAlzParam.WT_channelEst_Length);
        int j = 0;
        int RstCnt = sizeof(m_BeamfomingAlzParam.WT_channelEst_amplitude) /sizeof(double);
        Len = RstCnt * sizeof(double) * 2;  //已经取了所有的数据-8912
        double *Rst = (double *)RstBuf;
        //WTLog::Instance().WriteLog(LOG_DEBUG, "final Len = %d\n",Len);
        for(int i= 0; i < RstCnt; i++)
        {
            //WTLog::Instance().WriteLog(LOG_DEBUG, "%lf,%lf\n",m_BeamfomingAlzParam.WT_channelEst_amplitude[i],m_BeamfomingAlzParam.WT_channelEst_angle[i]);
            Rst[j++] = m_BeamfomingAlzParam.WT_channelEst_amplitude[i];
            Rst[j++] = m_BeamfomingAlzParam.WT_channelEst_angle[i];
        }
    }
    return Ret;
}

int Analysis::AlzBeamformingCalChAmplitudeAngleBCM(void *RstBuf, int &Len)
{
    int Ret = WT_OK;
    if((Ret = License::Instance().CheckBusinessLicItem(WT_IBF)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Beamforming license is invalid");
        return Ret;
    }

    *(int *)RstBuf = m_BeamfomingAlzParam.WT_validSpatialStreams;
    Len += sizeof(int);

    *(int *)((char *)RstBuf + Len) = m_BeamfomingAlzParam.WT_channelEst_Length;
    Len += sizeof(int);

    double *Rst = (double *)((char *)RstBuf + Len);
    int DataLen = min(m_BeamfomingAlzParam.WT_channelEst_Length, (int)(sizeof(m_BeamfomingAlzParam.WT_channelEst_amplitude) /sizeof(double)));
    int j = 0;

    for(int i = 0; i < DataLen; i++)
    {
        Rst[j++] = m_BeamfomingAlzParam.WT_channelEst_amplitude[i];
        Len += sizeof(double);
    }
    for(int i = 0; i < DataLen; i++)
    {
        Rst[j++] = m_BeamfomingAlzParam.WT_channelEst_angle[i];
        Len += sizeof(double);
    }

    //WTLog::Instance().WriteLog(LOG_DEBUG, "\nWT_validSpatialStreams=%d, WT_channelEst_Length=%d, Len = %d\n",m_BeamfomingAlzParam.WT_validSpatialStreams, m_BeamfomingAlzParam.WT_channelEst_Length, Len);
    return Ret;
}

//获取文件信号最大功率，用于vsg比较多流数据时存在噪底数据场景使用，eg：2*2mimo sin0+0,8080数据 80M+0等
int Analysis::GetMimoFileMaxPower(const string &FileName, double &MaxPower, double *ChPower)
{
    AlgReset::ReaderLock AlgReaderLock; //分析过程不允许重置算法内存
    MaxPower = UNVALID_DOUBLE_VAL;
    if (FileName.empty())
    {
        WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "vsg sigfile not exist");
        return WT_NO_SIG_DATA;
    }

    int Ret = access(FileName.c_str(), F_OK) == 0 ? WT_OK : WT_OPEN_FILE_FAILED;
    if (Ret == WT_OK)
    {
        Clear();
        ExtendEVMStu ExtendEVM;
        Ret = LoadSigFile(FileName, ExtendEVM);
        MaxPower = UNVALID_DOUBLE_VAL;
        if (Ret == WT_OK)
        {
            int SegmentNum = GetSegmentNum();

            if(m_RxInData[0].iNumOfChIn <= 1 && SegmentNum != 2) //文件数据流数只有一流，即为siso文件，且不是8080的信号，则不用做功率比较
            {
                return WT_OK;   //此时返回，MaxPower应为-999.99
            }

            for (int Seg = 0; Seg < SegmentNum; Seg++)
            {
                for(int i = 0; i < m_RxInData[Seg].iNumOfChIn; i++)
                {
                    Ret = AllocBuf(Seg, i);
                    if (Ret != WT_OK)
                    {
                        return Ret;
                    }
                    SetAlgVsaParam(&m_RxInData[Seg].aStChIn[i], m_AlzParam);
                    SetAlgExtralParam(&m_RxInData[Seg].aStChIn[i], m_ExtralAlzParam);
                    m_RxInData[Seg].aStChIn[i].analyzeFrameIndex = 1;
                    m_RxInData[Seg].aStChIn[i].demod_mode = WT_DEMOD_CW;    // 都按CW分析
                    m_RxInData[Seg].aStChIn[i].analyzeGroup = WT_GROUP_FFT; // 都按FFT分析
                    m_RxInData[Seg].aStChIn[i].FrameAutoDetection = WT_USER_DEFINED;
                    m_RxInData[Seg].aStChIn[i].vht80_80MEnable = 0; // 强制把8080标识置零，不使能，目前算法改了MIMO_WT_Algorithm_main，如果开启8080enable,使用内存是不正确的
                }
                AlgReset::Instance().AlgGetReaderLockT();  
                Ret = MIMO_WT_Algorithm_main(&m_RxInData[Seg], m_RxOutData[Seg]);
                AlgReset::Instance().AlgReleaseLockT();
                if (Ret == WT_OK)
                {
                    for(int i = 0; i < m_RxInData[Seg].iNumOfChIn; i++)
                    {
                       int Exist = m_RxOutData[Seg]->aStCh[i].pkg.frmcnt > 0;

                       int index = 0;
                       if(SegmentNum == 2) //8080
                       {
                           index = i*2+Seg;
                       }
                       else
                       {
                           index = i;
                       }
                       ChPower[index] = Exist ? m_RxOutData[Seg]->aStCh[i].pwr.frm_pwravg_dbm[0]
                                     : m_RxOutData[Seg]->aStCh[i].pwr.pkg_pwravg_dbm;
                       if(MaxPower < ChPower[index])
                           MaxPower = ChPower[index];
                       //WTLog::Instance().WriteLog(LOG_DEBUG, "##########MaxPower = %lf, TmpPower = %lf,index=%d\n",MaxPower, ChPower[index],index);
                    }
                }
                else
                {
                    Ret += WT_ALG_BASE_ERROR;
                }
            }
        }
    }
    return Ret;
}

int Analysis::AlzPer(int &FrameResult)
{
    int Ret = WT_OK;
    AlgReset::ReaderLock AlgReaderLock; //分析过程不允许重置算法内存
    if((Ret = License::Instance().CheckBusinessLicItem(WT_PER)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Per function license is invalid");
        return Ret;
    }

    unsigned char LocalMac[16]={0};
    if(m_RxInData[0].aStChIn[0].capturedata==NULL || m_RxInData[0].aStChIn[0].capturedata==nullptr)
    {
        return WT_ERROR;
    }

    int SegmentNum = GetSegmentNum();
    for (int Seg = 0; Seg < SegmentNum; Seg++)
    {
        for (int i = 0; i < m_RxInData[Seg].iNumOfChIn; i++)
        {
            SetAlgVsaParam(&m_RxInData[Seg].aStChIn[i], m_AlzParam);
            SetAlgExtralParam(&m_RxInData[Seg].aStChIn[i], m_ExtralAlzParam);

            Ret = AllocBuf(Seg, i);
            if (Ret != WT_OK)
            {
                return Ret;
            }
        }
    }
    AlgReset::Instance().AlgGetReaderLockT();
    Ret = WT_Algorithm_PER_Result(&m_RxInData[0].aStChIn[0], &m_RxOutData[0]->aStCh[0], LocalMac, &FrameResult);
    AlgReset::Instance().AlgReleaseLockT();
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_ALG_BASE_ERROR + Ret, "Alz per failed");
        Ret = WT_ALG_BASE_ERROR + Ret;
    }
    return Ret;
}

int Analysis::SetExtralAlzParam(int Demode, int Type, void *Param, int Len)
{
    int Ret = WT_ARG_ERROR;
    m_ExtralAlzParam.Demode = Demode;

    if( WT_DEMOD_11AZ_20M <= Demode && Demode <= WT_DEMOD_11AZ_160M && Type == 3)
    {
        if (Len == sizeof(AlzParam11az))
        {
            m_ExtralAlzParam.AzAlzParam = *static_cast<AlzParam11az *>(Param);
            Ret = WT_OK;
        }
        else
        {
            WTLog::Instance().LOGERR(Ret, "SetExtralAlzParam az paramter Len Error");
        }
        return Ret;
    }

    //WTLog::Instance().WriteLog(LOG_DEBUG, "SetExtralAlzParam Demo=%d, Type=%d, Len=%d,sizeof(AlzParamAxTriggerBase)=%d,sizeof(TriggerFrameSetting)=%d\n",
    //        Demode,Type,Len,(int)sizeof(AlzParamAxTriggerBase),(int)sizeof(TriggerFrameSetting));
    m_ExtralAlzParam.Type = Type;

    if(Type == -1)  //type为-1时，认为要设置tb配置无效，开关使能，主要为了清空tb的内容
    {
        return WT_OK;
    }

    switch (Demode)
    {
        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
        case WT_DEMOD_11AX_160M:
        case WT_DEMOD_11AX_80_80M:
        case WT_DEMOD_11AX_160_160M:
        case WT_DEMOD_11BE_20M:
        case WT_DEMOD_11BE_40M:
        case WT_DEMOD_11BE_80M:
        case WT_DEMOD_11BE_160M:
        case WT_DEMOD_11BE_320M:
        case WT_DEMOD_11BE_80_80M:
        case WT_DEMOD_11BE_160_160M:
        case WT_DEMOD_11AZ_20M:
        case WT_DEMOD_11AZ_40M:
        case WT_DEMOD_11AZ_80M:
        case WT_DEMOD_11AZ_160M:
            if (Type == 0)
            {
                if (Len == sizeof(AlzParamAxTriggerBase))
                {
                    m_ExtralAlzParam.AxTrigBaseParam = *static_cast<AlzParamAxTriggerBase *>(Param);
                    WTLog::Instance().WriteLog(LOG_DEBUG, "#####SetExtralAlzParam set tb flag = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.TBFlag);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####UserNum = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.UserNum);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####UserID = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.UserID);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####GILTFSize = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.GILTFSize);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####NumLTF = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.NumLTF);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####LDPCSym = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.LDPCSym);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####PEDisamb = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.PEDisamb);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####AFactor = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.AFactor);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####STBC = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.STBC);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####Doppler = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.Doppler);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####Midamble_Periodicity = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.Midamble_Periodicity);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####Stream = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.Stream[0], m_ExtralAlzParam.AxTrigBaseParam.Stream[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####MCS = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.MCS[0], m_ExtralAlzParam.AxTrigBaseParam.MCS[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####Segment = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.Segment[0], m_ExtralAlzParam.AxTrigBaseParam.Segment[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####RUIndex = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.RUIndex[0], m_ExtralAlzParam.AxTrigBaseParam.RUIndex[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####Conding = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.Conding[0], m_ExtralAlzParam.AxTrigBaseParam.Conding[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####DCM = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.DCM[0], m_ExtralAlzParam.AxTrigBaseParam.DCM[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####AID = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.AID[0], m_ExtralAlzParam.AxTrigBaseParam.AID[1]);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####TBMUMIMOFlag = %d####\n", m_ExtralAlzParam.AxTrigBaseParam.TBMUMIMOFlag);
                   WTLog::Instance().WriteLog(LOG_DEBUG, "#####NSSStart = %d,%d####\n", m_ExtralAlzParam.AxTrigBaseParam.NSSStart[0], m_ExtralAlzParam.AxTrigBaseParam.NSSStart[1]);
                    Ret = WT_OK;
                }
                else
                {
                    WTLog::Instance().LOGERR(Ret, "SetExtralAlzParam Type 0  triggerbase Len Error");
                }
            }
            else if(Type == 1)
            {
                if (Len == sizeof(TriggerFrameSetting))
                {
                    m_ExtralAlzParam.AxTrigFrameParam = *static_cast<TriggerFrameSetting *>(Param);
                    Ret = WT_OK;
                }
                else
                {
                    WTLog::Instance().LOGERR(Ret, "SetExtralAlzParam Type 1 trigger Frame Len Error");
                }
            }

            m_ExtralAlzParam.Type = Type;
            break;

        default:
            WTLog::Instance().LOGERR(Ret, "SetExtralAlzParam Demode Error");
            break;
    }

    return Ret;
}

void Analysis::SetAlgExtralParam(RX_InDat *pInData, ExtralVsaAlzParam &Param)
{
    //配置az的通用分析参数
    if(WT_DEMOD_11AZ_20M <= Param.Demode && Param.Demode <= WT_DEMOD_11AZ_160M)
    {
        pInData->Wifi11AzInCfg.SecureMode = Param.AzAlzParam.SecureMode;
        pInData->Wifi11AzInCfg.TxWinFlg = Param.AzAlzParam.TxWinFlg;
        pInData->Wifi11AzInCfg.UserNum = Param.AzAlzParam.UserNum;
        memcpy(pInData->Wifi11AzInCfg.User, Param.AzAlzParam.User, sizeof(Param.AzAlzParam.User));
    }
    //set 11ax trigger base alg analyze param
    if (Param.Type == 0)
    {
        pInData->TrigBase.TBFlag = Param.AxTrigBaseParam.TBFlag;
        pInData->TrigBase.UserNum = Param.AxTrigBaseParam.UserNum;
        pInData->TrigBase.UserID = Param.AxTrigBaseParam.UserID;
        pInData->TrigBase.GILTFSize = Param.AxTrigBaseParam.GILTFSize;
        pInData->TrigBase.NumLTF = Param.AxTrigBaseParam.NumLTF;
        pInData->TrigBase.LDPCSym = Param.AxTrigBaseParam.LDPCSym;
        pInData->TrigBase.PEDisamb = Param.AxTrigBaseParam.PEDisamb;
        pInData->TrigBase.AFactor = Param.AxTrigBaseParam.AFactor;
        pInData->TrigBase.STBC = Param.AxTrigBaseParam.STBC;
        pInData->TrigBase.Doppler = Param.AxTrigBaseParam.Doppler;
        pInData->TrigBase.Midamble_Periodicity = Param.AxTrigBaseParam.Midamble_Periodicity;
        memcpy(pInData->TrigBase.Stream, Param.AxTrigBaseParam.Stream, sizeof(Param.AxTrigBaseParam.Stream));
        memcpy(pInData->TrigBase.MCS, Param.AxTrigBaseParam.MCS, sizeof(Param.AxTrigBaseParam.MCS));
        memcpy(pInData->TrigBase.Segment, Param.AxTrigBaseParam.Segment, sizeof(Param.AxTrigBaseParam.Segment));
        memcpy(pInData->TrigBase.RUIndex, Param.AxTrigBaseParam.RUIndex, sizeof(Param.AxTrigBaseParam.RUIndex));
        memcpy(pInData->TrigBase.Conding, Param.AxTrigBaseParam.Conding, sizeof(Param.AxTrigBaseParam.Conding));
        memcpy(pInData->TrigBase.DCM, Param.AxTrigBaseParam.DCM, sizeof(Param.AxTrigBaseParam.DCM));
        pInData->TrigBase.TBMUMIMOFlag = Param.AxTrigBaseParam.TBMUMIMOFlag;
        memcpy(pInData->TrigBase.NSSStart, Param.AxTrigBaseParam.NSSStart, sizeof(Param.AxTrigBaseParam.NSSStart));
        //WTLog::Instance().WriteLog(LOG_DEBUG, "AID=%d,%d,%d,%d\n",Param.AxTrigBaseParam.AID[0],Param.AxTrigBaseParam.AID[1],Param.AxTrigBaseParam.AID[2],Param.AxTrigBaseParam.AID[3]);
        memcpy(pInData->TrigBase.AID, Param.AxTrigBaseParam.AID, sizeof(Param.AxTrigBaseParam.AID));
        pInData->TrigFrameSet = nullptr;
    }
    else if (Param.Type == 1)
    {
        memset(&m_TriggerFrame, 0, sizeof(m_TriggerFrame));
        pInData->TrigFrameSet = &m_TriggerFrame;
        pInData->TrigFrameSet->TriggerType = Param.AxTrigFrameParam.TriggerType;
        pInData->TrigFrameSet->TBLength = Param.AxTrigFrameParam.TBLength;
        pInData->TrigFrameSet->MoreTF = Param.AxTrigFrameParam.MoreTF;
        pInData->TrigFrameSet->CSRequired = Param.AxTrigFrameParam.CSRequired;
        pInData->TrigFrameSet->TBULBW = Param.AxTrigFrameParam.TBULBW;
        pInData->TrigFrameSet->TBGILTF = Param.AxTrigFrameParam.TBGILTF;
        pInData->TrigFrameSet->TBMMMode = Param.AxTrigFrameParam.TBMMMode;
        pInData->TrigFrameSet->TBLTFSym = Param.AxTrigFrameParam.TBLTFSym;
        pInData->TrigFrameSet->TBSTBC = Param.AxTrigFrameParam.TBSTBC;
        pInData->TrigFrameSet->TBLDPCExtra = Param.AxTrigFrameParam.TBLDPCExtra;
        pInData->TrigFrameSet->APTxPower = Param.AxTrigFrameParam.APTxPower;
        pInData->TrigFrameSet->TBAfactor = Param.AxTrigFrameParam.TBAfactor;
        pInData->TrigFrameSet->TBPE = Param.AxTrigFrameParam.TBPE;
        pInData->TrigFrameSet->mPad = Param.AxTrigFrameParam.mPad;
        memcpy(pInData->TrigFrameSet->TBSR, Param.AxTrigFrameParam.TBSR, sizeof(Param.AxTrigFrameParam.TBSR));
        pInData->TrigFrameSet->TBDoppler = Param.AxTrigFrameParam.TBDoppler;
        pInData->TrigFrameSet->TBUserNum = Param.AxTrigFrameParam.TBUserNum;
        memcpy(pInData->TrigFrameSet->TBAID, Param.AxTrigFrameParam.TBAID, sizeof(Param.AxTrigFrameParam.TBAID));
        memcpy(pInData->TrigFrameSet->TBRUIndex, Param.AxTrigFrameParam.TBRUIndex, sizeof(Param.AxTrigFrameParam.TBRUIndex));
        memcpy(pInData->TrigFrameSet->TBSegment, Param.AxTrigFrameParam.TBSegment, sizeof(Param.AxTrigFrameParam.TBSegment));
        memcpy(pInData->TrigFrameSet->TBCoding, Param.AxTrigFrameParam.TBCoding, sizeof(Param.AxTrigFrameParam.TBCoding));
        memcpy(pInData->TrigFrameSet->TBMCS, Param.AxTrigFrameParam.TBMCS, sizeof(Param.AxTrigFrameParam.TBMCS));
        memcpy(pInData->TrigFrameSet->TBDCM, Param.AxTrigFrameParam.TBDCM, sizeof(Param.AxTrigFrameParam.TBDCM));
        memcpy(pInData->TrigFrameSet->TBSSStart, Param.AxTrigFrameParam.TBSSStart, sizeof(Param.AxTrigFrameParam.TBSSStart));
        memcpy(pInData->TrigFrameSet->TBSSCount, Param.AxTrigFrameParam.TBSSCount, sizeof(Param.AxTrigFrameParam.TBSSCount));
        memcpy(pInData->TrigFrameSet->TBRSSI, Param.AxTrigFrameParam.TBRSSI, sizeof(Param.AxTrigFrameParam.TBRSSI));
        memcpy(pInData->TrigFrameSet->TBSpacingFactor, Param.AxTrigFrameParam.TBSpacingFactor, sizeof(Param.AxTrigFrameParam.TBSpacingFactor));
        memcpy(pInData->TrigFrameSet->TBAggLimit, Param.AxTrigFrameParam.TBAggLimit, sizeof(Param.AxTrigFrameParam.TBAggLimit));
        memcpy(pInData->TrigFrameSet->TBPreAC, Param.AxTrigFrameParam.TBPreAC, sizeof(Param.AxTrigFrameParam.TBPreAC));
        memcpy(pInData->TrigFrameSet->TBMultiplexing, Param.AxTrigFrameParam.TBMultiplexing, sizeof(Param.AxTrigFrameParam.TBMultiplexing));
        memcpy(pInData->TrigFrameSet->TBRxTxMap, Param.AxTrigFrameParam.TBRxTxMap, sizeof(Param.AxTrigFrameParam.TBRxTxMap));
        pInData->TrigFrameSet->TBBARInfoLen = Param.AxTrigFrameParam.TBBARInfoLen;
        memcpy(pInData->TrigFrameSet->TBBARControl, Param.AxTrigFrameParam.TBBARControl, sizeof(Param.AxTrigFrameParam.TBBARControl));
        memcpy(pInData->TrigFrameSet->TBBARInfo, Param.AxTrigFrameParam.TBBARInfo, sizeof(Param.AxTrigFrameParam.TBBARInfo));
        pInData->TrigFrameSet->TBMidamble_Periodicity = Param.AxTrigFrameParam.TBMidamble_Periodicity;
        pInData->TrigFrameSet->PE_Disambiguity = Param.AxTrigFrameParam.PE_Disambiguity;
    }
    else
    {
        pInData->TrigBase.TBFlag = 0;
        pInData->TrigBase.UserNum = 0;
        pInData->TrigBase.UserID = 0;
        pInData->TrigBase.GILTFSize = 0;
        pInData->TrigBase.NumLTF = 0;
        pInData->TrigBase.LDPCSym = 0;
        pInData->TrigBase.PEDisamb = 0;
        pInData->TrigBase.AFactor = 0;
        pInData->TrigBase.STBC = 0;
        pInData->TrigBase.Doppler = 0;
        pInData->TrigBase.Midamble_Periodicity = 0;
        memset(pInData->TrigBase.Stream, 0, sizeof(pInData->TrigBase.Stream));
        memset(pInData->TrigBase.MCS, 0, sizeof(pInData->TrigBase.MCS));
        memset(pInData->TrigBase.Segment, 0, sizeof(pInData->TrigBase.Segment));
        memset(pInData->TrigBase.RUIndex, 0, sizeof(pInData->TrigBase.RUIndex));
        memset(pInData->TrigBase.Conding, 0, sizeof(pInData->TrigBase.Conding));
        memset(pInData->TrigBase.DCM, 0, sizeof(pInData->TrigBase.DCM));
        pInData->TrigFrameSet = nullptr;
    }
    //WTLog::Instance().WriteLog(LOG_DEBUG, "========= set TrigBase.TBFlag=%d, TrigFrameSet=%p\n", pInData->TrigBase.TBFlag, pInData->TrigFrameSet);
}

int Analysis::SetAlzGroup(std::vector<std::string> &AlzTypes)
{
    int FlagArr[WT_MAX_GROUP_TYPE_NUM] = {0};
    int CurIndex = 0;
    for(auto &Item : AlzTypes)
    {
        //Spectrum
        if(\
                (Item == WT_RES_SPECTRUM_CARRIER_LEAKAGE) || \
                (Item == WT_RES_SPECTRUM_OBW) || \
                (Item == WT_RES_SPECTRUM_MASK_ERR) || \
                (Item == WT_RES_SPECTRUM_PEAK_FREQ) || \
                (Item == WT_RES_SPECTRUM_Y) || \
                (Item == WT_RES_SPECTRUM_MARGIN_DATA) || \
                (Item == WT_RES_IQ) /*IQ*/
                )
        {
            CurIndex = WT_GROUP_POWER_ANALYSIS;
        }
        //Power
        else if(\
                (Item == WT_RES_POWER_FRAME_COUNT) ||\
                (Item == WT_RES_POWER_PEAK) || \
                (Item == WT_RES_RMS_DB_NO_GAP) || \
                (Item == WT_RES_RMS_DB) || \
                (Item == WT_RES_POINTS_POWER) || \
                (Item == WT_RES_WIN_AVG_POWER)
        )
        {
            CurIndex = WT_GROUP_POWER_ANALYSIS;
        }
        //ccdf
        else if(\
                //(Item == WT_MAX_CCDF_ELEMENTS) ||
                (Item == WT_RES_CCDF_PROB) || \
                (Item == WT_RES_CCDF_POWER_REL_DB) || \
                (Item == WT_RES_CCDF_START) || \
                (Item == WT_RES_CCDF_SCALE) || \
                (Item == WT_RES_CCDF_PERCENT_POWER)
                )
        {
            CurIndex = WT_GROUP_CCDF_ANALYSIS;
        }

        else
        {
            //Frame
            int Demode = m_RxInData[0].aStChIn[0].demod_mode;
            WTLog::Instance().WriteLog(LOG_DEBUG, "cur Demode = %d\n",Demode);
            switch(Demode)
            {
                case WT_DEMOD_11AG:     //11a/g
                case WT_DEMOD_11N_20M:  //11n 20M
                case WT_DEMOD_11N_40M:  //11n 40M
                case WT_DEMOD_11AC_20M: //11ac 20M
                case WT_DEMOD_11AC_40M: //11ac 40M
                case WT_DEMOD_11AC_80M: //11ac 80M
                case WT_DEMOD_11AC_160M:    //11ac 160M
                case WT_DEMOD_11AC_80_80M:  //11ac 80 + 80M
                case WT_DEMOD_11B:
                case WT_DEMOD_BT:
                case WT_DEMOD_ZIGBEE:
                case WT_DEMOD_LORA_125K:
                case WT_DEMOD_LORA_250K:
                case WT_DEMOD_LORA_500K:
                    CurIndex = WT_GROUP_OFDM_ANALYSIS;
                    break;
                case WT_DEMOD_CW:
                    CurIndex = WT_GROUP_FFT;
                    break;
                default:
                    break;
            }
        }
        FlagArr[CurIndex] = 1;
    }

    if (FlagArr[WT_GROUP_FFT] == 1)
    {
        m_AnalyseGroup = WT_GROUP_FFT;
    }
    else if (FlagArr[WT_GROUP_OFDM_ANALYSIS] == 1)
    {
        if (FlagArr[WT_GROUP_CCDF_ANALYSIS] == 1)
        {
            m_AnalyseGroup = WT_GROUP_ALL;
        }
        else
        {
            m_AnalyseGroup = WT_GROUP_OFDM_ANALYSIS;
        }
    }
    else if (FlagArr[WT_GROUP_CCDF_ANALYSIS] == 1)
    {
        m_AnalyseGroup = WT_GROUP_CCDF_ANALYSIS;
    }
    else if (FlagArr[WT_GROUP_POWER_ANALYSIS] == 1)
    {
        m_AnalyseGroup = WT_GROUP_POWER_ANALYSIS;
    }
    else
    {
        m_AnalyseGroup = WT_GROUP_ALL;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "Get AnalyseGroup=%d\n",m_AnalyseGroup);

    return WT_OK;
}

void Analysis::ResetAnalyzeGroup()
{
    m_AnalyseGroup = WT_GROUP_ALL;    //默认算法全部内容分析
};

template <typename T>
static int GetCompareRst(T ValueA, T ValueB, int CompareType)
{
    int Ret = WT_ARG_ERROR;
    if(sizeof(T) == sizeof(int))
    {
        switch(CompareType)
        {
            case GE:
            {
                if(ValueA >= ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case LE:
            {
                if(ValueA <= ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case GT:
            {
                if(ValueA > ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case LT:
            {
                if(ValueA < ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case EQ:
            {
                if(ValueA == ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case NE:
            {
                if(ValueA != ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            default:
                break;
        }
    }
    else    //double 类型比较
    {
        const double dEpsilon = 1e-6;
        switch(CompareType)
        {
            case GE:
            {
                if(ValueA > (ValueB - dEpsilon))
                {
                    Ret = WT_OK;
                }
                break;
            }
            case LE:
            {
                if(ValueA < (ValueB + dEpsilon))
                {
                    Ret = WT_OK;
                }
                break;
            }
            case GT:
            {
                if(ValueA > ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case LT:
            {
                if(ValueA < ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case EQ:
            {
                if(fabs(ValueA - ValueB) < dEpsilon)
                {
                    Ret = WT_OK;
                }
                break;
            }
            case NE:
            {
                if(ValueA != ValueB)
                {
                    Ret = WT_OK;
                }
                break;
            }
            default:
                break;
        }
    }
    return Ret;
}

int Analysis::FiltFrameResult(int Stream, int Seg)
{
    //帧功率通过power等级或者psdu长度或者速率
    //目前只用于wifi
    AlgReset::ReaderLock AlgReaderLock;
    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    int Ret = WT_ARG_ERROR;
    StOutInfo *pRxOut = m_RxOutData[Seg];
    if(m_ResultFilters.IsEnable == 1)
    {
        switch(m_ResultFilters.FilterType)
        {
        case FILT_BY_PSDULENGTH:
        {
            int CurPsduLength = 0;
            int *TmpAddr = nullptr;
            TmpAddr = &CurPsduLength;
            int Len = 0;
            TmpAddr = (int *)AlgResult::GetPsduLength(pRxOut, Stream, &CurPsduLength, Len);

            if(TmpAddr != nullptr)
            {
                CurPsduLength = *(TmpAddr);
                if(CurPsduLength != UNVALID_INT_VAL)
                {
                    Ret = GetCompareRst<int>(CurPsduLength, m_ResultFilters.FiltPsduLength, m_ResultFilters.CompareType);
                }
            }
            break;
        }
        case FILT_BY_DATARATE:
        {
            double CurDataRate = 0;
            double *TmpAddr = nullptr;
            TmpAddr = &CurDataRate;
            int Len = 0;
            TmpAddr = (double *)AlgResult::GetOfdmDataRate(pRxOut, Stream, TmpAddr, Len);
            if(TmpAddr != nullptr)
            {
                CurDataRate = *TmpAddr;
                if(Basefun::CompareDouble(CurDataRate, UNVALID_DOUBLE_VAL) != 0)
                {
                    //WTLog::Instance().WriteLog(LOG_DEBUG, "###CurDataRate=%lf,m_ResultFilters.FiltDataRate = %lf,Compare=%d\n",CurDataRate, m_ResultFilters.FiltDataRate, m_ResultFilters.CompareType);
                    Ret = GetCompareRst<double>(CurDataRate, m_ResultFilters.FiltDataRate, m_ResultFilters.CompareType);
                }
            }
            break;
        }
        case FILT_BY_POWER:
        {
            int Exist = pRxOut->aStCh[0].pkg.frmcnt > 0;

            const VsaCommResult *Result;
            Ret = GetCommResult(0, Stream, &Result);
            if(Ret == WT_OK)
            {
                double CurPower = Exist ? Result->PowerFrame
                              : Result->PowerAll;

                //WTLog::Instance().WriteLog(LOG_DEBUG, "###CurPower=%lf,m_ResultFilters.FiltPower = %lf\n",CurPower, m_ResultFilters.FiltPower);
                Ret = GetCompareRst<double>(CurPower, m_ResultFilters.FiltPower, m_ResultFilters.CompareType);
            }
            break;
        }
        default:
            break;
        }
    }
    else
    {
        Ret =WT_OK;
    }

    return Ret;
}

int Analysis::SetResultFilterSetting(void *Param, int Len)
{
    int Ret = WT_ARG_ERROR;
    if(Len == sizeof(ResultFilter))
    {
        m_ResultFilters = *static_cast<ResultFilter*>(Param);
        Ret = WT_OK;
    }
    return Ret;
}

void Analysis::SaveStackDataFile(void)
{
    bool HasStackData = false;

    //两个segment的数据
    for(int Stream = 0; Stream < m_RxInData[0].iNumOfChIn; Stream++)
    {
        for(int Seg = 0; Seg < 2; Seg++)
        {
            RX_InDat *pInData = &m_RxInData[Seg].aStChIn[Stream];
            if(pInData->capturedata != nullptr)
            {
                HasStackData = true;
                break;
            }
        }
        if(HasStackData == true)
        {
            break;
        }
    }

    if(!HasStackData)
    {
        return;
    }

    char Buf[1024]={0};
    int Cnt = readlink("/proc/self/exe", Buf, 1024);
    while (Cnt--)
    {
        if (Buf[Cnt] == '/')
        {
            Buf[Cnt] = '\0';
            break;
        }
    }

    char DirName[2048] = {0};
    Basefun::csprintf(DirName, "%s/Stack/", Buf);

    if (-1 == access(DirName, F_OK))
    {
        if (-1 == mkdir(DirName, 0755))  //建立目录
        {
            Basefun::cfprintf(STDERR_FILENO, "Create stack data dir failed!\n");
            return;
        }
    }

    char DataBuf[512] = {0};
    char ParamBuf[512] = {0};
    struct tm *Mtm;
    time_t Now;
    time(&Now);
    Mtm = localtime(&Now);
    Basefun::csprintf(DataBuf, "Stack_Data_%d-%d-%d_%d-%d-%d_%p.csv", 1900 + Mtm->tm_year, Mtm->tm_mon + 1, Mtm->tm_mday, Mtm->tm_hour, Mtm->tm_min, Mtm->tm_sec, this);
    Basefun::csprintf(ParamBuf, "Stack_Config_%d-%d-%d_%d-%d-%d_%p.csv", 1900 + Mtm->tm_year, Mtm->tm_mon + 1, Mtm->tm_mday, Mtm->tm_hour, Mtm->tm_min, Mtm->tm_sec, this);

    char DataFileName[4096] = {0};
    Basefun::csprintf(DataFileName,"%s%s",DirName,DataBuf);
    SaveStackData(DataFileName);

    char ParamFileName[4096] = {0};
    Basefun::csprintf(ParamFileName,"%s%s",DirName,ParamBuf);
    SaveStackAlzParam(ParamFileName);
}

void Analysis::SaveStack3GppData(const char *FileName)
{
    Basefun::cfprintf(STDOUT_FILENO, "SaveStack3GppData ##FileName=%s\n", FileName);
    FILE *fp = NULL;

    if ((fp = fopen(FileName, "w+e")) != NULL)
    {
        int fd = fileno(fp);
        if (m_ListEnable == false)
        {
            for(int Stream = 0; Stream < m_Alg_3GPP_VsaInInfo.RFInChanNum; Stream++)
            {
                if (m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturedata != nullptr)
                {
                    Basefun::cfprintf(fd, "FileInformation\n");
                    Basefun::cfprintf(fd, "Description,NULL\n");
                    Basefun::cfprintf(fd, "Sample Count,%d\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturecnt);
                    Basefun::cfprintf(fd, "ScaleTo,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].ScaleTo);
                    Basefun::cfprintf(fd, "Frequency,%lf,MHz\n", (double)m_Alg_3GPP_VsaInInfo.RFInfo[Stream].adc_freq /(double)MHz);
                    Basefun::cfprintf(fd, "Center Freq,%lf,Hz\n",m_Alg_3GPP_VsaInInfo.RFInfo[Stream].rf_center_freq);
                    if(m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturedata_format == enDataFormat_Int16)
                    {
                        Basefun::cfprintf(fd, "Data Type,int16\n");
                    }
                    else
                    {
                        Basefun::cfprintf(fd, "Data Type,double\n");
                    }
                    Basefun::cfprintf(fd, "referenceLevel,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].referenceLevel);
                    Basefun::cfprintf(fd, "ExternalAtt,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].extgain);
                    //Basefun::cfprintf(fd, "ClockRate,%d\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].clockrate);
                    Basefun::cfprintf(fd, "RF Gain,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].gain);
                    Basefun::cfprintf(fd, "IQGainImb,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].IQImb_Amp);
                    Basefun::cfprintf(fd, "IQPhaseImb,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].IQImb_Phase);
                    Basefun::cfprintf(fd, "Time Skew,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].TimeSkew);
                    Basefun::cfprintf(fd, "DC_Offset_I,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].IDCOffset);
                    Basefun::cfprintf(fd, "DC_Offset_Q,%lf\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].QDCOffset);
                    Basefun::cfprintf(fd, "ModType,%d\n", m_Alg_3GPP_VsaInInfo.Standard);
                    Basefun::cfprintf(fd, "Creat Time\n");
                    Basefun::cfprintf(fd, "StartData\n");
                    if(m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturedata_format == enDataFormat_Int16)
                    {
                        typedef struct
                        {
                            s16 real;
                            s16 imag;
                        } ADC_Data;
                        ADC_Data *Dat;
                        Dat = (ADC_Data *)m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturedata;
                        for(int i = 0; i < m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturecnt; i++)
                        {
                            Basefun::cfprintf(fd, "%d,%d\n",Dat[i].real, Dat[i].imag);
                        }
                    }
                    else if(m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturedata_format == enDataFormat_Float64)
                    {
                        Complex *Dat;
                        Dat = (Complex *)m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturedata;
                        for(int i = 0; i < m_Alg_3GPP_VsaInInfo.RFInfo[Stream].capturecnt; i++)
                        {
                            Basefun::cfprintf(fd, "%lf,%lf\n", Dat[i][0], Dat[i][1]);
                        }
                    }
                }
            }
        }
        else
        {
            for(int Stream = 0; Stream < m_Alg_3GPP_List_VsaInInfo.RFInChanNum; Stream++)
            {
                if (m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturedata != nullptr)
                {
                    Basefun::cfprintf(fd, "FileInformation\n");
                    Basefun::cfprintf(fd, "Description,NULL\n");
                    Basefun::cfprintf(fd, "Sample Count,%d\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturecnt);
                    Basefun::cfprintf(fd, "ScaleTo,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].ScaleTo);
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SaveStack3GppData adc_freq" << (double)m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].adc_freq /(double)MHz << std::endl;
                    Basefun::cfprintf(fd, "Frequency,%lf,MHz\n", (double)m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].adc_freq /(double)MHz);
                    Basefun::cfprintf(fd, "Center Freq,%lf,Hz\n",m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].rf_center_freq);
                    if(m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturedata_format == enDataFormat_Int16)
                    {
                        Basefun::cfprintf(fd, "Data Type,int16\n");
                    }
                    else
                    {
                        Basefun::cfprintf(fd, "Data Type,double\n");
                    }
                    Basefun::cfprintf(fd, "referenceLevel,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].referenceLevel);
                    Basefun::cfprintf(fd, "ExternalAtt,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].extgain);
                    //Basefun::cfprintf(fd, "ClockRate,%d\n", m_Alg_3GPP_VsaInInfo.RFInfo[Stream].clockrate);
                    Basefun::cfprintf(fd, "RF Gain,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].gain);
                    Basefun::cfprintf(fd, "IQGainImb,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].IQImb_Amp);
                    Basefun::cfprintf(fd, "IQPhaseImb,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].IQImb_Phase);
                    Basefun::cfprintf(fd, "Time Skew,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].TimeSkew);
                    Basefun::cfprintf(fd, "DC_Offset_I,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].IDCOffset);
                    Basefun::cfprintf(fd, "DC_Offset_Q,%lf\n", m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].QDCOffset);
                    Basefun::cfprintf(fd, "ModType,%d\n", m_Alg_3GPP_List_VsaInInfo.Standard);
                    Basefun::cfprintf(fd, "Creat Time\n");
                    Basefun::cfprintf(fd, "StartData\n");
                    if(m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturedata_format == enDataFormat_Int16)
                    {
                        typedef struct
                        {
                            s16 real;
                            s16 imag;
                        } ADC_Data;
                        ADC_Data *Dat;
                        Dat = (ADC_Data *)m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturedata;
                        for(int i = 0; i < m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturecnt; i++)
                        {
                            Basefun::cfprintf(fd, "%d,%d\n",Dat[i].real, Dat[i].imag);
                        }
                    }
                    else if(m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturedata_format == enDataFormat_Float64)
                    {
                        Complex *Dat;
                        Dat = (Complex *)m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturedata;
                        for(int i = 0; i < m_Alg_3GPP_List_VsaInInfo.RFInfo[Stream].capturecnt; i++)
                        {
                            Basefun::cfprintf(fd, "%lf,%lf\n", Dat[i][0], Dat[i][1]);
                        }
                    }
                }
            }
        }
    }
    fclose(fp);
}

void Analysis::SaveStackData(const char *FileName)
{
    Basefun::cfprintf(STDOUT_FILENO, "##FileName=%s\n", FileName);
    FILE *fp = NULL;
    if ((fp = fopen(FileName, "w+e")) != NULL)
    {
        int fd = fileno(fp);

        //两个segment的数据
        for(int Stream = 0; Stream < m_RxInData[0].iNumOfChIn; Stream++)
        {
            for(int Seg = 0; Seg < 2; Seg++)
            {
                RX_InDat *pInData = &m_RxInData[Seg].aStChIn[Stream];
                if(pInData->capturedata != nullptr)
                {
                    Basefun::cfprintf(fd, "FileInformation\n");
                    Basefun::cfprintf(fd, "Description,NULL\n");
                    Basefun::cfprintf(fd, "Sample Count,%d\n", pInData->capturecnt);
                    Basefun::cfprintf(fd, "ScaleTo,%lf\n", pInData->ScaleTo);
                    Basefun::cfprintf(fd, "Frequency,%d,MHz\n", pInData->adc_freq /MHz);
                    Basefun::cfprintf(fd, "Center Freq,%lf,Hz\n",pInData->rf_center_freq);
                    if(pInData->vht80_80MEnable == 1)
                    {
                        Basefun::cfprintf(fd, "Flag8080,%d\n", pInData->vht80_80MEnable);
                    }
                    if(pInData->capturedata_format == enDataFormat_Int16)
                    {
                        Basefun::cfprintf(fd, "Data Type,int16\n");
                    }
                    else
                    {
                        Basefun::cfprintf(fd, "Data Type,double\n");
                    }
                    Basefun::cfprintf(fd, "referenceLevel,%lf\n", pInData->referenceLevel);
                    Basefun::cfprintf(fd, "ExternalAtt,%lf\n", pInData->extgain);
                    Basefun::cfprintf(fd, "ClockRate,%d\n", pInData->clockrate);
                    Basefun::cfprintf(fd, "RF Gain,%lf\n", pInData->gain);
                    Basefun::cfprintf(fd, "IQGainImb,%lf\n", pInData->IQImb_Amp);
                    Basefun::cfprintf(fd, "IQPhaseImb,%lf\n", pInData->IQImb_Phase);
                    Basefun::cfprintf(fd, "Time Skew,%lf\n", pInData->TimeSkew);
                    Basefun::cfprintf(fd, "DC_Offset_I,%lf\n", pInData->IDCOffset);
                    Basefun::cfprintf(fd, "DC_Offset_Q,%lf\n", pInData->QDCOffset);
                    Basefun::cfprintf(fd, "ModType,%d\n", pInData->demod_mode);
                    Basefun::cfprintf(fd, "Creat Time\n");
                    Basefun::cfprintf(fd, "StartData\n");
                    if(pInData->capturedata_format == enDataFormat_Int16)
                    {
                        typedef struct
                        {
                            s16 real;
                            s16 imag;
                        } ADC_Data;
                        ADC_Data *Dat;
                        Dat = (ADC_Data *)pInData->capturedata;
                        for(int i = 0; i < pInData->capturecnt; i++)
                        {
                            Basefun::cfprintf(fd, "%d,%d\n",Dat[i].real, Dat[i].imag);
                        }
                    }
                    else if(pInData->capturedata_format == enDataFormat_Float64)
                    {
                        Complex *Dat;
                        Dat = (Complex *)pInData->capturedata;
                        for(int i = 0; i < pInData->capturecnt; i++)
                        {
                            Basefun::cfprintf(fd, "%lf,%lf\n", Dat[i][0], Dat[i][1]);
                        }
                    }
                }
            }
        }
    }
    fclose(fp);
}

void Analysis::SaveStackAlzParam(char *FileName)
{
#define PSave(n)  Basefun::cfprintf(fd, #n",%d\n", pInData->n);
    WTLog::Instance().WriteLog(LOG_DEBUG, "##FileName=%s\n",FileName);
    FILE *fp = NULL;
    if ((fp = fopen(FileName, "w+e")) != NULL)
    {
        int fd = fileno(fp);
        //两个segment的数据
        for(int Stream = 0; Stream < m_RxInData[0].iNumOfChIn; Stream++)
        {
            for(int Seg = 0; Seg < 2; Seg++)
            {
                RX_InDat *pInData = &m_RxInData[Seg].aStChIn[Stream];
                if(pInData->capturedata != nullptr)
                {
                    Basefun::cfprintf(fd, "======================================\n");
                    Basefun::cfprintf(fd, "Seg,%d\n",Seg);
                    Basefun::cfprintf(fd, "Stream,%d\n",Stream);
                    Basefun::cfprintf(fd, "iNumOfChIn,%d\n",m_RxInData[0].iNumOfChIn);
                    Basefun::cfprintf(fd, "capturecnt,%d\n",pInData->capturecnt);
#ifdef WT300
                    PSave(Spec500MFlag);
                    PSave(capture500cnt);
                    PSave(Offset500M);
#endif
                    Basefun::cfprintf(fd, "capturedata_format,%d\n",pInData->capturedata_format);
                    Basefun::cfprintf(fd, "enDataFormat_Float64,\n");
                    Basefun::cfprintf(fd, "enDataFormat_Int16,\n");
                    Basefun::cfprintf(fd, "enDataFormat_None,\n");
                    Basefun::cfprintf(fd, "ScaleTo,%lf\n",pInData->ScaleTo);
                    Basefun::cfprintf(fd, "IQImb_Amp,%lf\n",pInData->IQImb_Amp);
                    Basefun::cfprintf(fd, "IQImb_Phase,%lf\n",pInData->IQImb_Phase);
                    Basefun::cfprintf(fd, "IDCOffset,%lf\n",pInData->IDCOffset);
                    Basefun::cfprintf(fd, "QDCOffset,%lf\n",pInData->QDCOffset);
                    Basefun::cfprintf(fd, "TimeSkew,%lf\n",pInData->TimeSkew);
                    Basefun::cfprintf(fd, "AutoRangeFlag,%lf\n",pInData->AutoRangeFlag);

                    Basefun::cfprintf(fd, "adc_freq,%d\n",pInData->adc_freq);
                    Basefun::cfprintf(fd, "gain,%lf\n",pInData->gain);
                    Basefun::cfprintf(fd, "extgain,%lf\n",pInData->extgain);
                    Basefun::cfprintf(fd, "trig_pwr,%lf\n",pInData->trig_pwr);
                    Basefun::cfprintf(fd, "trig_point,%lf\n",pInData->trig_point);
                    Basefun::cfprintf(fd, "rf_center_freq,%lf\n",pInData->rf_center_freq);
                    Basefun::cfprintf(fd, "rf_channel,%lf\n",pInData->rf_channel);
                    Basefun::cfprintf(fd, "referenceLevel,%lf\n",pInData->referenceLevel);
                    Basefun::cfprintf(fd, "powerOffset,%p\n",pInData->powerOffset);
                    Basefun::cfprintf(fd, "clockrate,%d\n",pInData->clockrate);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11AG,%lf\n",pInData->evmoffset.Offset_11AG);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11B,%lf\n",pInData->evmoffset.Offset_11B);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11N_20M,%lf\n",pInData->evmoffset.Offset_11N_20M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11N_40M,%lf\n",pInData->evmoffset.Offset_11N_40M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11AC_20M,%lf\n",pInData->evmoffset.Offset_11AC_20M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11AC_40M,%lf\n",pInData->evmoffset.Offset_11AC_40M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11AC_80M,%lf\n",pInData->evmoffset.Offset_11AC_80M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11AC_160M,%lf\n",pInData->evmoffset.Offset_11AC_160M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_11AC_80_80M,%lf\n",pInData->evmoffset.Offset_11AC_80_80M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_BT_2M,%lf\n",pInData->evmoffset.Offset_BT_2M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_BT_3M,%lf\n",pInData->evmoffset.Offset_BT_3M);
                    Basefun::cfprintf(fd, "evmoffset.Offset_ZigBee_2G,%lf\n",pInData->evmoffset.Offset_ZigBee_2G);
                    
                    Basefun::cfprintf(fd, "demod_mode,%d\n",pInData->demod_mode);
                    Basefun::cfprintf(fd, "iqswap,%d\n",pInData->iqswap);
                    Basefun::cfprintf(fd, "WT_IQ_SWAP_DISABLED,\n");
                    Basefun::cfprintf(fd, "WT_IQ_SWAP_ENABLED,\n");
                    Basefun::cfprintf(fd, "iqreversion,%d\n",pInData->iqreversion);
                    Basefun::cfprintf(fd, "WT_IQ_IQReversion_DISABLED,\n");
                    Basefun::cfprintf(fd, "WT_IQ_IReversion_ENABLED,\n");
                    Basefun::cfprintf(fd, "WT_IQ_QReversion_ENABLED,\n");
                    Basefun::cfprintf(fd, "WT_IQ_IQReversion_ENABLED,\n");

                    Basefun::cfprintf(fd, "channelest_enable,%d\n",pInData->channelest_enable);
                    Basefun::cfprintf(fd, "WT_CH_EST_RAW       = 1,\n");
                    Basefun::cfprintf(fd, "WT_CH_EST_RAW_LONG  = WT_CH_EST_RAW,\n");
                    Basefun::cfprintf(fd, "WT_CH_EST_2ND_ORDER,\n");
                    Basefun::cfprintf(fd, "WT_CH_EST_RAW_FULL,\n");
                    Basefun::cfprintf(fd, "amp_track_enable,%d\n",pInData->amp_track_enable);
                    Basefun::cfprintf(fd, "WT_AMPL_TRACK_OFF = 1,\n");
                    Basefun::cfprintf(fd, "WT_AMPL_TRACK_ON  = 2,\n");
                    Basefun::cfprintf(fd, "phase_track_enable,%d\n",pInData->phase_track_enable);
                    Basefun::cfprintf(fd, "WT_PH_CORR_OFF        = 1,\n");
                    Basefun::cfprintf(fd, "WT_PH_CORR_SYM_BY_SYM = 2,\n");
                    Basefun::cfprintf(fd, "WT_PH_CORR_MOVING_AVG = 3,\n");
                    Basefun::cfprintf(fd, "sym_tim_corr,%d\n",pInData->sym_tim_corr);
                    Basefun::cfprintf(fd, "WT_SYM_TIM_OFF = 1,\n");
                    Basefun::cfprintf(fd, "WT_SYM_TIM_ON  = 2,\n");
                    Basefun::cfprintf(fd, "freq_sync,%d\n",pInData->freq_sync);
                    Basefun::cfprintf(fd, "WT_FREQ_SYNC_SHORT_TRAIN = 1,\n");
                    Basefun::cfprintf(fd, "WT_FREQ_SYNC_LONG_TRAIN  = 2,\n");
                    Basefun::cfprintf(fd, "WT_FREQ_SYNC_FULL_PACKET = 3,\n");

                    Basefun::cfprintf(fd, "cck_dcremove_enable,%d\n",pInData->cck_dcremove_enable);
                    Basefun::cfprintf(fd, "WT_DC_REMOVAL_OFF = 0,\n");
                    Basefun::cfprintf(fd, "WT_DC_REMOVAL_ON  = 1,\n");
                    Basefun::cfprintf(fd, "cck_phase_track_enable,%d\n",pInData->cck_phase_track_enable);
                    Basefun::cfprintf(fd, "cck_equalizertaps,%d\n",pInData->cck_equalizertaps);
                    Basefun::cfprintf(fd, "WT_EQ_OFF = 1,\n");
                    Basefun::cfprintf(fd, "WT_EQ_5_TAPS = 5,\n");
                    Basefun::cfprintf(fd, "WT_EQ_7_TAPS = 7,\n");
                    Basefun::cfprintf(fd, "WT_EQ_9_TAPS = 9,\n");
                    Basefun::cfprintf(fd, "cck_evm_method,%d\n",pInData->cck_evm_method);
                    Basefun::cfprintf(fd, "WT_11B_STANDARD_TX_ACC  = 1,\n");
                    Basefun::cfprintf(fd, "WT_11B_RMS_ERROR_VECTOR = 2,\n");
                    Basefun::cfprintf(fd, "WT_11B_STANDARD_2007_TX_ACC = 3,\n");

                    Basefun::cfprintf(fd, "frequencyOffsetHz,%lf\n",pInData->frequencyOffsetHz);
                    Basefun::cfprintf(fd, "FrameAutoDetection,%d\n",pInData->FrameAutoDetection);
                    Basefun::cfprintf(fd, "MIMOAnalysisMode,%d\n",pInData->MIMOAnalysisMode);
                    Basefun::cfprintf(fd, "BT_DataRate,%d\n",pInData->BT_DataRate);
                    Basefun::cfprintf(fd, "BT_PacketType,%d\n",pInData->BT_PacketType);
                    Basefun::cfprintf(fd, "BT_BleEnhancedMode,%d\n",pInData->BT_BleEnhancedMode);
                    Basefun::cfprintf(fd, "BLE_Normal_Rate = 0,\n");
                    Basefun::cfprintf(fd, "BLE_Double_Rate = 1,\n");
                    Basefun::cfprintf(fd, "BT_BlePDUType,%d\n",pInData->BT_BlePDUType);
                    Basefun::cfprintf(fd, "TEST = 0,\n");
                    Basefun::cfprintf(fd, "ADV = 1,\n");
                    Basefun::cfprintf(fd, "BT_BleSyncMode,%d\n",pInData->BT_BleSyncMode);
                    Basefun::cfprintf(fd, "Preamble = 0,\n");
                    Basefun::cfprintf(fd, "AccessAddress = 1,\n");
                    Basefun::cfprintf(fd, "BT_BleAccessAddress,%d\n",pInData->BT_BleAccessAddress);
                    Basefun::cfprintf(fd, "BT_BleChannelIndex,%d\n",pInData->BT_BleChannelIndex);
                    Basefun::cfprintf(fd, "BT_ACPSweepTimes,%d\n",pInData->BT_ACPSweepTimes);
                    Basefun::cfprintf(fd, "BT_ACPViewRange,%d\n",pInData->BT_ACPViewRange);

                    Basefun::cfprintf(fd, "FilterFromatTime,%d\n",pInData->FilterFromatTime);
                    Basefun::cfprintf(fd, "rf_response,%p\n",pInData->rf_response);
                    Basefun::cfprintf(fd, "rf_response_len,%d\n",pInData->rf_response_len);
                    Basefun::cfprintf(fd, "bb_response,%p\n",pInData->bb_response);
                    Basefun::cfprintf(fd, "bb_response_len,%d\n",pInData->bb_response_len);
                    Basefun::cfprintf(fd, "noise_response,%p\n",pInData->noise_response);
                    Basefun::cfprintf(fd, "noise_response_len,%d\n",pInData->noise_response_len);
                    // Basefun::cfprintf(fd, "spectOffset,%p\n",pInData->spectOffset);
//TODO:
                    Basefun::cfprintf(fd, "group_fft_frm,%d\n",pInData->group_fft_frm);
                    Basefun::cfprintf(fd, "group_fft_rbw,%d\n",pInData->group_fft_rbw);
                    Basefun::cfprintf(fd, "group_fft_vbw,%d\n",pInData->group_fft_vbw);
                    Basefun::cfprintf(fd, "scene_model,%d\n",pInData->scene_model);
                    Basefun::cfprintf(fd, "cmimo_refDat,%d\n",pInData->cmimo_refDat);
                    Basefun::cfprintf(fd, "cmimo_refDat_size,%d\n",pInData->cmimo_refDat_size);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.standard,%d\n",pInData->cmimoReferenceInfo.standard);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.bandwidth,%d\n",pInData->cmimoReferenceInfo.bandwidth);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.modulationCodingScheme,%d\n",pInData->cmimoReferenceInfo.modulationCodingScheme);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.coding,%d\n",pInData->cmimoReferenceInfo.coding);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.numPattens,%d\n",pInData->cmimoReferenceInfo.numPattens);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.numSpatialStreams,%d\n",pInData->cmimoReferenceInfo.numSpatialStreams);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.numSpaceTimeStreams,%d\n",pInData->cmimoReferenceInfo.numSpaceTimeStreams);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.numSymbols,%d\n",pInData->cmimoReferenceInfo.numSymbols);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.numTones,%d\n",pInData->cmimoReferenceInfo.numTones);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.skipSymbols,%d\n",pInData->cmimoReferenceInfo.skipSymbols);
                    Basefun::cfprintf(fd, "cmimoReferenceInfo.validSkipSymbolsCount,%d\n",pInData->cmimoReferenceInfo.validSkipSymbolsCount);
                    Basefun::cfprintf(fd, "analyzeFrameIndex,%d\n",pInData->analyzeFrameIndex);
                    Basefun::cfprintf(fd, "vht80_80MEnable,%d\n",pInData->vht80_80MEnable);
                    Basefun::cfprintf(fd, "currentSeg80_80M,%d\n",pInData->currentSeg80_80M);
                    Basefun::cfprintf(fd, "ZBAnalysisOptimise,%d\n",pInData->ZBAnalysisOptimise);
                    Basefun::cfprintf(fd, "IEEE802_11p_SpectrumMaskMode,%d\n",pInData->IEEE802_11p_SpectrumMaskMode);
                    Basefun::cfprintf(fd, "spectMaskErrRange.enable,%d\n",pInData->spectMaskErrRange.enable);
                    Basefun::cfprintf(fd, "spectMaskErrRange.spectMaskStartFreq,%d\n",pInData->spectMaskErrRange.spectMaskStartFreq);
                    Basefun::cfprintf(fd, "spectMaskErrRange.spectMaskEndFreq,%d\n",pInData->spectMaskErrRange.spectMaskEndFreq);
                    Basefun::cfprintf(fd, "spectrumMaskVersion,%d\n",pInData->spectrumMaskVersion);
                    Basefun::cfprintf(fd, "analyzeGroup,%d\n",pInData->analyzeGroup);
                    
                    PSave(cSpectrumFlag);
                    PSave(cCcdfFlag);
                    PSave(FullCRC_Flag);
                    PSave(analyzeMode);
                    PSave(Vsg11axCompOptFlag);
                    PSave(IQCompensation);
                    PSave(PreambleAverage);
                    PSave(EqualizerSmoothing);
                    PSave(ICISuppression);
                    PSave(RemovePhaseRamp);
                    PSave(NoiseSuppression);
                    PSave(SfoCompensation);
                    
                    PSave(Extend_EVM);
                    PSave(AlzNumOfFrame);
                    PSave(SNC_EVM);
                    PSave(CC_EVM);
                    PSave(noise_response_len);
                    PSave(freq_IQImb_len);
                    PSave(FrameType11AH);
                    PSave(EnbaleEmbeddedBSSID);
                    PSave(EmbeddedBSSID[0]);
                    PSave(EmbeddedBSSID[1]);
                    PSave(EmbeddedBSSID[2]);
                    PSave(EmbeddedBSSID[3]);
                    PSave(freqBand);
                    PSave(DevmMode);
                    Basefun::cfprintf(fd, "ReserveMem,%p\n",pInData->ReserveMem);
                    Basefun::cfprintf(fd, "ReserveMemSize,%d\n",pInData->ReserveMemSize);

                    Basefun::cfprintf(fd, "mrFskType,%p\n",pInData->mrFskType);
                    Basefun::cfprintf(fd, "mrFskDataRate,%d\n",pInData->mrFskDataRate);
                    Basefun::cfprintf(fd, "mrFskSfdEnable,%p\n",pInData->mrFskSfdEnable);
                    Basefun::cfprintf(fd, "phyMrFskFecEnable,%d\n",pInData->phyMrFskFecEnable);
                    Basefun::cfprintf(fd, "phyMrFskSfdValue,%p\n",pInData->phyMrFskSfdValue);
                    Basefun::cfprintf(fd, "fskModulationIndex,%d\n",pInData->fskModulationIndex);
                    Basefun::cfprintf(fd, "phyMrFskACPCalculationMode,%p\n",pInData->phyMrFskACPCalculationMode);
                    Basefun::cfprintf(fd, "phyMrFskChannelSpacing,%d\n",pInData->phyMrFskChannelSpacing);
                    Basefun::cfprintf(fd, "ZwaveRate,%p\n",pInData->ZwaveRate);
                    Basefun::cfprintf(fd, "clockrate,%d\n",pInData->clockrate);
                    // Basefun::cfprintf(fd, "NpuschInputInfo,%p\n",pInData->NpuschInputInfo);
//TODO:
                    // Basefun::cfprintf(fd, "NprachInputInfo,%d\n",pInData->NprachInputInfo);
//TODO:
                    Basefun::cfprintf(fd, "deciFreqCompFlag,%p\n",pInData->deciFreqCompFlag);
                    // Basefun::cfprintf(fd, "TrigBase,%d\n",pInData->TrigBase);
//TODO:
                    // Basefun::cfprintf(fd, "TrigFrameSet,%d\n",pInData->TrigFrameSet);
//TODO:
                    Basefun::cfprintf(fd, "cSpectrumFlag,%d\n",pInData->cSpectrumFlag);
                    Basefun::cfprintf(fd, "cCcdfFlag,%d\n",pInData->cCcdfFlag);
                    Basefun::cfprintf(fd, "FullCRC_Flag,%d\n",pInData->FullCRC_Flag);
                    Basefun::cfprintf(fd, "analyzeMode,%d\n",pInData->analyzeMode);
                    Basefun::cfprintf(fd, "Vsg11axCompOptFlag,%d\n",pInData->Vsg11axCompOptFlag);
                    // Basefun::cfprintf(fd, "RUInfo,%d\n",pInData->RUInfo);
//TODO:
                    Basefun::cfprintf(fd, "IQCompensation,%d\n",pInData->IQCompensation);
                    Basefun::cfprintf(fd, "PreambleAverage,%d\n",pInData->PreambleAverage);
                    Basefun::cfprintf(fd, "EqualizerSmoothing,%d\n",pInData->EqualizerSmoothing);
                    Basefun::cfprintf(fd, "ICISuppression,%d\n",pInData->ICISuppression);
                    Basefun::cfprintf(fd, "RemovePhaseRamp,%d\n",pInData->RemovePhaseRamp);
                    Basefun::cfprintf(fd, "NoiseSuppression,%d\n",pInData->NoiseSuppression);
                    Basefun::cfprintf(fd, "SfoCompensation,%d\n",pInData->SfoCompensation);
                    // Basefun::cfprintf(fd, "Gle,%d\n",pInData->Gle);
//TODO:
                    Basefun::cfprintf(fd, "nonHTDupBW,%d\n",pInData->nonHTDupBW);
                    Basefun::cfprintf(fd, "LdpcDecodeIterationTimes,%d\n",pInData->LdpcDecodeIterationTimes);
                    // Basefun::cfprintf(fd, "Wifi11AzInCfg,%d\n",pInData->Wifi11AzInCfg);
//TODO:
                    Basefun::cfprintf(fd, "mrOfdmOption,%d\n",pInData->mrOfdmOption);
                    Basefun::cfprintf(fd, "phyOfdmInterleaving,%d\n",pInData->phyOfdmInterleaving);
                    Basefun::cfprintf(fd, "OBWCalcFlag,%d\n",pInData->OBWCalcFlag);
                    Basefun::cfprintf(fd, "LdpcDecodeMethod,%d\n",pInData->LdpcDecodeMethod);
                    Basefun::cfprintf(fd, "BtSDKCalcFlag,%d\n",pInData->BtSDKCalcFlag);
                    // Basefun::cfprintf(fd, "HMatrix,%d\n",pInData->HMatrix);
//TODO:
                    Basefun::cfprintf(fd, "Extend_EVM,%d\n",pInData->Extend_EVM);
                    Basefun::cfprintf(fd, "AlzNumOfFrame,%d\n",pInData->AlzNumOfFrame);
                    Basefun::cfprintf(fd, "SNC_EVM,%d\n",pInData->SNC_EVM);
                    Basefun::cfprintf(fd, "CC_EVM,%d\n",pInData->CC_EVM);
                    Basefun::cfprintf(fd, "freq_IQImb_amp,%d\n",pInData->freq_IQImb_amp);
                    Basefun::cfprintf(fd, "freq_IQImb_len,%d\n",pInData->freq_IQImb_len);
                    Basefun::cfprintf(fd, "freq_IQImb_phase,%d\n",pInData->freq_IQImb_phase);
                    Basefun::cfprintf(fd, "FrameType11AH,%d\n",pInData->FrameType11AH);
                    Basefun::cfprintf(fd, "EnbaleEmbeddedBSSID,%d\n",pInData->EnbaleEmbeddedBSSID);
                    Basefun::cfprintf(fd, "EmbeddedBSSID,%d\n",pInData->EmbeddedBSSID);
                    Basefun::cfprintf(fd, "freqBand,%d\n",pInData->freqBand);
                    Basefun::cfprintf(fd, "DevmMode,%d\n",pInData->DevmMode);
                }
            }
        }
    }
    fclose(fp);
}

int Analysis::SetVsaFlatnessCal(int Enable)
{
    m_AlzFlatnessEnable = Enable;
    return WT_OK;
}

int Analysis::SetVsaIQImbCal(int Enable)
{
    m_AlzIQImbEnable = Enable;
    return WT_OK;
}

int Analysis::SetDuplexNoiseFlag(int Flag)
{
    m_AlzDuplexNoiseFlag = Flag;
    return WT_OK;
}

int Analysis::AlzMinAvgPower(int Segment, double &MinPower)
{
    AlgReset::ReaderLock AlgReaderLock; //分析过程不允许重置算法内存
    int Ret = AllocBuf(Segment, 0);
    if (Ret != WT_OK)
    {
        return Ret;
    }

    SetAlgVsaParam(&m_RxInData[Segment].aStChIn[0], m_AlzParam);
    SetAlgExtralParam(&m_RxInData[Segment].aStChIn[0], m_ExtralAlzParam);
    m_RxInData[Segment].aStChIn[0].analyzeGroup = WT_GROUP_FFT; // 都按FFT分析
    m_RxInData[Segment].aStChIn[0].demod_mode = WT_DEMOD_UNKNOW;
    m_RxInData[Segment].aStChIn[0].FrameAutoDetection = WT_USER_DEFINED;
    
    AlgReset::Instance().AlgGetReaderLockT();
    Ret = MIMO_WT_Algorithm_main(&m_RxInData[Segment], m_RxOutData[Segment]);
    AlgReset::Instance().AlgReleaseLockT();
    if (Ret == WT_OK)
    {
        int Count = m_RxOutData[Segment]->aStCh[0].pkg.pwrwin_cnt;
        double *Power = m_RxOutData[Segment]->aStCh[0].pkg.pwrwin_avg_dbm;
        MinPower = 100;
        for (int i = 0; i < Count; i++)
        {
            if (Power[i] < MinPower)
            {
                MinPower = Power[i];
            }
        }
    }
    else
    {
        Ret += WT_ALG_BASE_ERROR;
    }

    return Ret;
}

void Analysis::ShowAlzParam()
{
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.Demode = "<<m_AlzParam.Demode<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.IQSwap = "<<m_AlzParam.CommParam.IQSwap<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.IQReversion = "<<m_AlzParam.CommParam.IQReversion<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.ManualPktStart = "<<m_AlzParam.CommParam.ManualPktStart<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.FilterPktByTime = "<<m_AlzParam.CommParam.FilterPktByTime<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.FilterPktByType = "<<m_AlzParam.CommParam.FilterPktByType<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.FreqOffset = "<<m_AlzParam.CommParam.FreqOffset<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.SpectrumFlag = "<<m_AlzParam.CommParam.SpectrumFlag<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.CcdfFlag = "<<m_AlzParam.CommParam.CcdfFlag<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.AvgTimes = "<<m_AlzParam.CommParam.AvgTimes<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.HmatrixEnable = "<<m_AlzParam.CommParam.HmatrixEnable<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.HmatRxAntennaNum = "<<m_AlzParam.CommParam.HmatRxAntennaNum<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.HmatTxAntennaNum = "<<m_AlzParam.CommParam.HmatTxAntennaNum<<std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.CommParam.HMatValue :"<<std::endl;
    for(int i = 0;i<8;i++)
    {
        for(int j = 0;j<8;j++)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "[%lf,%lf]",m_AlzParam.CommParam.HMatValue[i][j][0],m_AlzParam.CommParam.HMatValue[i][j][1]);
        }
        //WTLog::Instance().WriteLog(LOG_DEBUG, "\n");
    }
    switch(m_AlzParam.Demode)
    {
        case WT_DEMOD_CW:
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.FFTParam.WindowType = "<<m_AlzParam.FFTParam.WindowType<<std::endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG)<<"m_AlzParam.FFTParam.Rbw = "<<m_AlzParam.FFTParam.Rbw<<std::endl;
        }
        default:
        {

        }
    }

}


// 3GPP_START//////////////////////////////////////////////////////////////////////////////////////////////////////////////
void Analysis::SetAlgVsaParam3GPP(Alg_3GPP_VsaInInfo *pInData, const AlzParam3GPP &Alz3GPPParam)
{
    for (int i = 0; i < pInData->RFInChanNum && i < ALG_3GPP_MAX_STREAM; ++i)
    {
        pInData->RFInfo[i].rf_band = Alz3GPPParam.rf_band[i];
        pInData->RFInfo[i].rf_channel = Alz3GPPParam.rf_channel[i];
        pInData->RFInfo[i].TriggerType = Alz3GPPParam.TriggerType;
        pInData->RFInfo[i].PreTime = Alz3GPPParam.PreTime;
    }

    pInData->analyzeGroup = Alz3GPPParam.analyzeGroup;
    pInData->DcFreqCompensate = Alz3GPPParam.DcFreqCompensate;
    pInData->Standard = Alz3GPPParam.Standard;
    pInData->SpectrumRBW = Alz3GPPParam.SpectrumRBW;
    pInData->PkgAlzMode = m_AnalyzeMode;
    pInData->PkgAlzOffset = Alz3GPPParam.PkgAlzOffset;
    pInData->MeasPowerGraph = Alz3GPPParam.MeasPowerGraph;
    pInData->MeasSpectrum = Alz3GPPParam.MeasSpectrum;
    pInData->MeasCCDF = Alz3GPPParam.MeasCCDF;

    switch (pInData->Standard)
    {
    case ALG_3GPP_STD_GSM:
        memcpy(&pInData->GSM, &Alz3GPPParam.GSM, sizeof(pInData->GSM));
        break;
    case ALG_3GPP_STD_WCDMA:
        memcpy(&pInData->WCDMA, &Alz3GPPParam.WCDMA, sizeof(pInData->WCDMA));
        break;
    case ALG_3GPP_STD_5G:
        memcpy(&pInData->NR, &Alz3GPPParam.NR, sizeof(pInData->NR));
        break;
    case ALG_3GPP_STD_4G:
        memcpy(&pInData->LTE, &Alz3GPPParam.LTE, sizeof(pInData->LTE));
        break;
    case ALG_3GPP_STD_NB_IOT:
        memcpy(&pInData->NBIOT, &Alz3GPPParam.NBIOT, sizeof(pInData->NBIOT));
        // pInData->NBIOT.Measure.StatisticAvgFlg = (m_StatisticCnt > 1);
        // pInData->NBIOT.Measure.StatisticCnt = m_StatisticCnt;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "pInData->NBIOT.Measure.StatisticAvgFlg = "<<pInData->NBIOT.Measure.StatisticAvgFlg<<std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "pInData->NBIOT.Measure.StatisticCnt = "<<pInData->NBIOT.Measure.StatisticCnt<<std::endl; 
        break;
    default:
        WTLog::Instance().WriteLog(LOG_DEBUG, "SetAlgVsaParam3GPP : Unknow Demod!");
    }
}

void Analysis::Set3gppRfInfo(const DataBufInfo &DataBuf, const VsaParam &Param, const Rx_Parm &CalParam, int FreqOffset, Alg_3GPP_RFInInfo *pRFInInfo)
{
    if (pRFInInfo == nullptr)
    {
        return;
    }
    pRFInInfo->capturedata = DataBuf.Buf.get();
    pRFInInfo->capturedata_format = enDataFormat_Int16;
    pRFInInfo->capturecnt = round(DataBuf.DataLen / sizeof(stIQDat));
    pRFInInfo->ScaleTo = AlgEnv::Instance().GetScaleVal(enDataFormat_Int16);
    pRFInInfo->adc_freq = round(Param.SamplingFreq);
    // pRFInInfo->trig_pwr = Param.TrigLevel;
    pRFInInfo->referenceLevel = Param.Ampl;
    pRFInInfo->rf_center_freq = Param.Freq + FreqOffset;
    pRFInInfo->extgain = -Param.ExtGain;
    pRFInInfo->gain = CalParam.rx_gain_parm.rx_sw_gain.final_gain;
    if (m_AlzIQImbEnable)
    {
        pRFInInfo->IQImb_Amp = CalParam.rx_iq_imb_parm.gain_imb;
        pRFInInfo->IQImb_Phase = CalParam.rx_iq_imb_parm.quad_err;
    }
    else
    {
        pRFInInfo->IQImb_Amp = 0;
        pRFInInfo->IQImb_Phase = 0;
    }
    if (m_AlzFlatnessEnable)
    {
        pRFInInfo->bb_response = const_cast<double *>(CalParam.rx_spec_flat_comp_parm.bb_comp_gain);
        pRFInInfo->bb_response_len = CalParam.rx_spec_flat_comp_parm.bb_comp_len;
        pRFInInfo->rf_response = const_cast<double *>(CalParam.rx_spec_flat_comp_parm.rf_comp_gain);
        pRFInInfo->rf_response_len = CalParam.rx_spec_flat_comp_parm.rf_comp_len;
    }
    else
    {
        pRFInInfo->bb_response = 0;
        pRFInInfo->bb_response_len = 0;
        pRFInInfo->rf_response = 0;
        pRFInInfo->rf_response_len = 0;
    }

    if (m_AlzDuplexNoiseFlag)
    {
        pRFInInfo->duplex_noise_response = const_cast<double *>(CalParam.rx_spec_flat_comp_parm.duplex_ns_comp_gain);
        pRFInInfo->duplex_noise_response_len = CalParam.rx_spec_flat_comp_parm.duplex_ns_comp_len;
    }
    else
    {
        pRFInInfo->duplex_noise_response = 0;
        pRFInInfo->duplex_noise_response_len = 0;
    }

    // 清除校准参数
    pRFInInfo->IDCOffset = 0;
    pRFInInfo->QDCOffset = 0;
    pRFInInfo->TimeSkew = 0;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppRfInfo DataBuf= " << DataBuf.BufLen << "DataLen=" <<DataBuf.DataLen  << "pRFInInfo->capturecnt" << pRFInInfo->capturecnt << std::endl;
}

void Analysis::Set3gppNrWideBandData(const std::vector<DataBufInfo> &ReSampleData, const std::vector<DataBufInfo> &RawSampleData,
    const VsaParam &Param, const Rx_Parm &CalParam, int FreqOffset)
{
    //分析采样的数据时清除文件数据
    if (m_SigFile != nullptr)
    {
        m_SigFile.reset(nullptr);
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData ReSampleData[0].buflen= " << ReSampleData[0].BufLen << "DataLen=" << ReSampleData[0].DataLen << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData RawSampleData[0]= " << RawSampleData[0].BufLen << "DataLen=" << RawSampleData[0].DataLen << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData RawSampleData[1]= " << RawSampleData[1].BufLen << "DataLen=" << RawSampleData[1].DataLen << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData RawSampleData[2]= " << RawSampleData[2].BufLen << "DataLen=" << RawSampleData[2].DataLen << std::endl;
    Set3gppRfInfo(ReSampleData[0], Param, CalParam, 0.0, &m_Alg_3GPP_VsaInInfo.RFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]);

    Set3gppRfInfo(RawSampleData[0], Param, CalParam, 0.0, &m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]);
    m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].adc_freq = DEFAULT_SMAPLE_RATE;
    if (GetAlzParamNrAlzBand() <= 70*1e6)
    {
        memset(&m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
        memset(&m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData.RFInfo adc_freq= " << m_Alg_3GPP_VsaInInfo.RFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].adc_freq << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData.HRFInfo adc_freq= " << m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].adc_freq << std::endl;
        m_Alg_3GPP_VsaInInfo.RFInChanNum++;
        return;
    }

    Set3gppRfInfo(RawSampleData[1], Param, CalParam, 0 - FreqOffset, &m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]);
    m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].adc_freq = DEFAULT_SMAPLE_RATE;

    Set3gppRfInfo(RawSampleData[2], Param, CalParam, FreqOffset, &m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]);
    m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].adc_freq = DEFAULT_SMAPLE_RATE;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData.RFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.RFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData.HRFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData.LHRFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "Set3gppNrWideBandData.RHRFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum].capturecnt << std::endl;

    m_Alg_3GPP_VsaInInfo.RFInChanNum++;
}

void Analysis::SetData3GPP(const DataBufInfo &DataBuf, const VsaParam &Param, const Rx_Parm &CalParam)
{
    //分析采样的数据时清除文件数据
    if (m_SigFile != nullptr)
    {
        m_SigFile.reset(nullptr);
    }

    memset(&m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
    memset(&m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
    memset(&m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum], 0, sizeof(m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum]));
    Set3gppRfInfo(DataBuf, Param, CalParam, 0.0, &m_Alg_3GPP_VsaInInfo.RFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum++]);
}

void Analysis::SetData3GPPList(const DataBufInfo &DataBuf, const VsaParam &Param, const Rx_Parm &CalParam)
{
    //分析采样的数据时清除文件数据
    if (m_SigFile != nullptr)
    {
        m_SigFile.reset(nullptr);
    }

    Set3gppRfInfo(DataBuf, Param, CalParam, 0.0, &m_Alg_3GPP_List_VsaInInfo.RFInfo[m_Alg_3GPP_List_VsaInInfo.RFInChanNum++]);
}

void Analysis::SetAlgVsaParam3GPPList(Alg_3GPP_ListInType *pInData, const AlzParam3GPP &Alz3GPPParam)
{
    for (int i = 0; i < pInData->RFInChanNum && i < ALG_3GPP_MAX_STREAM; ++i)
    {
        pInData->RFInfo[i].rf_band = Alz3GPPParam.rf_band[i];
        pInData->RFInfo[i].rf_channel = Alz3GPPParam.rf_channel[i];
    }

    pInData->Standard = Alz3GPPParam.Standard;

    switch (pInData->Standard)
    {
    case ALG_3GPP_STD_4G:
    {
        memcpy(&pInData->LTE, &Alz3GPPParam.LTELIST, sizeof(pInData->LTE));
        break;
    }
    case ALG_3GPP_STD_5G:
    {
        memcpy(&pInData->NR, &Alz3GPPParam.NR5GLIST, sizeof(pInData->NR));
        break;
    }
    default:
        WTLog::Instance().WriteLog(LOG_DEBUG, "SetAlgVsaParam3GPPList : Unknow Demod!");
    }

    return;
}

int Analysis::AlzFrameData3GPPList()
{
    int Ret = WT_OK;

    if (m_AlzParam.Demode == ALG_3GPP_STD_NB_IOT || m_AlzParam.Demode == ALG_3GPP_STD_4G)
    {
        if(License::Instance().CheckBusinessLicItem(WT_LTE_IoT) != WT_OK)
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(Ret, "Lte Iot license not exist when AlzFrameData3GPPList");
            return Ret;
        }
    }

    for (int i = 0; i < m_Alg_3GPP_List_VsaInInfo.RFInChanNum; i++)
    {
        // 选择帧的参数无效，已于分析参数中指定。
        SetAlgVsaParam3GPPList(&m_Alg_3GPP_List_VsaInInfo, m_AlzParam.Alz3GPPParam);
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AlzFrameData3GPPList()......" << std::endl;
    auto &nr = m_Alg_3GPP_List_VsaInInfo.NR;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AlzFrameData3GPPList: {" << std::endl
                                                             << "    Standard:" << m_Alg_3GPP_List_VsaInInfo.Standard << std::endl
                                                             << "    adc_freq:" << m_Alg_3GPP_List_VsaInInfo.RFInfo[0].adc_freq << std::endl
                                                             << "    rf_center_freq:" << m_Alg_3GPP_List_VsaInInfo.RFInfo[0].rf_center_freq << std::endl
                                                             << "    capturecnt:" << m_Alg_3GPP_List_VsaInInfo.RFInfo[0].capturecnt << std::endl
                                                             << "    rf_band:" << m_Alg_3GPP_List_VsaInInfo.RFInfo[0].rf_band << std::endl
                                                             << "    RedcapEnable:" << nr.RedcapEnable << std::endl   
                                                             << "    Version:" << nr.Version << std::endl   
                                                             << "    ChannelBW:" << nr.ChannelBW << std::endl
                                                             << "    Duplexing:" << nr.Duplexing << std::endl   
                                                             << "    NSValue:" << nr.NSValue << std::endl   
                                                             << "    PhyCellID:" << nr.PhyCellID << std::endl   
                                                             << "    DmrsTypeAPos:" << nr.DmrsTypeAPos << std::endl   
                                                             << "    UseSCSpacing:" << nr.UseSCSpacing << std::endl   
                                                             << "    OffsetToCarrier:" << nr.OffsetToCarrier << std::endl   
                                                             << "    SCSpacing:" << nr.SCSpacing << std::endl   
                                                             << "    CyclicPrefix:" << nr.CyclicPrefix << std::endl
                                                             << "    BwpRBNum:" << nr.BwpRBNum << std::endl
                                                             << "    BwpRBOffset:" << nr.BwpRBOffset << std::endl
                                                             << "    ConfigType[0]:" << nr.ConfigType[0] << std::endl
                                                             << "    ConfigType[1]:" << nr.ConfigType[1] << std::endl
                                                             << "    MaxLength[0]:" << nr.MaxLength[0] << std::endl
                                                             << "    MaxLength[1]:" << nr.MaxLength[1] << std::endl
                                                             << "    AdditionalPos[0]:" << nr.AdditionalPos[0] << std::endl
                                                             << "    AdditionalPos[1]:" << nr.AdditionalPos[1] << std::endl
                                                             << "    TransformPrecoder:" << nr.TransformPrecoder << std::endl
                                                             << "    MappingType:" << nr.MappingType << std::endl
                                                             << "    SymNum:" << nr.SymNum << std::endl
                                                             << "    SymbOffset:" << nr.SymbOffset << std::endl
                                                             << "    RBAutoMode:" << nr.RBAutoMode << std::endl
                                                             << "    RBNum:" << nr.RBNum << std::endl
                                                             << "    RBOffset:" << nr.RBOffset << std::endl
                                                             << "    Modulate:" << nr.Modulate << std::endl
                                                             << "    DmrsSymbLen:" << nr.DmrsSymbLen << std::endl
                                                             << "    DmrsAntPort:" << nr.DmrsAntPort << std::endl
                                                             << "    DmrsInitType:" << nr.DmrsInitType << std::endl
                                                             << "    DmrsID:" << nr.DmrsID << std::endl
                                                             << "    NSCID:" << nr.NSCID << std::endl
                                                             << "    Measure.ModStatNum:" << nr.Measure.ModStatNum << std::endl
                                                             << "    Measure.ModEnable:" << (int)nr.Measure.ModEnable << std::endl
                                                             << "    Measure.EvmEnable:" << (int)nr.Measure.EvmEnable << std::endl
                                                             << "    Measure.MErrEnable:" << (int)nr.Measure.MErrEnable << std::endl
                                                             << "    Measure.PErrEnable:" << (int)nr.Measure.PErrEnable << std::endl
                                                             << "    Measure.IBEEnable:" << (int)nr.Measure.IBEEnable << std::endl
                                                             << "    Measure.ESFlatEnable:" << (int)nr.Measure.ESFlatEnable << std::endl
                                                             << "    Modulate:" << nr.Modulate << std::endl
                                                             << "    Measure.SEMStatNum:" << nr.Measure.SEMStatNum << std::endl
                                                             << "    Measure.SpectEnable:" << (int)nr.Measure.SpectEnable << std::endl
                                                             << "    Measure.OBWEnable:" << (int)nr.Measure.OBWEnable << std::endl
                                                             << "    Measure.SEMEnable:" << (int)nr.Measure.SEMEnable << std::endl
                                                             << "    Measure.ACLRStatNum:" << nr.Measure.ACLRStatNum << std::endl
                                                             << "    Measure.ACLREnable:" << (int)nr.Measure.ACLREnable << std::endl
                                                             << "    Measure.UTRA1Enable:" << (int)nr.Measure.UTRA1Enable << std::endl
                                                             << "    Measure.UTRA2Enable:" << (int)nr.Measure.UTRA2Enable << std::endl
                                                             << "    Measure.NREnable:" << (int)nr.Measure.NREnable << std::endl
                                                             << "    Measure.TxPwrStatNum:" << nr.Measure.TxPwrStatNum << std::endl
                                                             << "    Measure.TxPwrEnable:" << (int)nr.Measure.TxPwrEnable << std::endl
                                                             << "}" << std::endl;
    Ret = Callback_3GPP_ListMain(&m_Alg_3GPP_List_VsaInInfo, &m_Alg_3GPP_List_VsaOutInfo);
    m_AlzAll = (Ret == WT_OK);

    if (Ret != WT_OK)
    {
        return (Ret + WT_ALG_3GPP_BASE_ERROR);
    }
    else
    {
        return Ret;
    }
}

int Analysis::AlzFrameData3GPP(int FrameId)
{
    int Ret = WT_OK;

    if (m_AlzParam.Demode == ALG_3GPP_STD_NB_IOT || m_AlzParam.Demode == ALG_3GPP_STD_4G)
    {
        if(License::Instance().CheckBusinessLicItem(WT_LTE_IoT) != WT_OK)
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(Ret, "Lte Iot license not exist when AlzFrameData3GPP");
            return Ret;
        }
    }

    for (int i = 0; i < m_Alg_3GPP_VsaInInfo.RFInChanNum; i++)
    {
        // 选择帧的参数无效，已于分析参数中指定。
        SetAlgVsaParam3GPP(&m_Alg_3GPP_VsaInInfo, m_AlzParam.Alz3GPPParam);
        // 申请内存
        Ret = AllocBuf3GPP();
        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.m_Alg_3GPP_VsaInInfo.RFInChanNum = " << m_Alg_3GPP_VsaInInfo.RFInChanNum << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.analyzeGroup = " << m_Alg_3GPP_VsaInInfo.analyzeGroup << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.Standard = " << m_Alg_3GPP_VsaInInfo.Standard << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.RFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.RFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.HRFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.LHRFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.RHRFInfo capturecnt= " << m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].capturecnt << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.RFInfo adc_freq = " << m_Alg_3GPP_VsaInInfo.RFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].adc_freq << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.HRFInfo adc_freq= " << m_Alg_3GPP_VsaInInfo.HRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].adc_freq << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.LHRFInfo adc_freq= " << m_Alg_3GPP_VsaInInfo.LHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].adc_freq << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaInInfo.RHRFInfo adc_freq= " << m_Alg_3GPP_VsaInInfo.RHRFInfo[m_Alg_3GPP_VsaInInfo.RFInChanNum - 1].adc_freq << std::endl;
#if 0
    //调试打印
    WTLog::Instance().WriteLog(LOG_DEBUG, "RFInChanNum=%d\n", m_Alg_3GPP_VsaInInfo.RFInChanNum);
    WTLog::Instance().WriteLog(LOG_DEBUG, "analyzeGroup=%d\n", m_Alg_3GPP_VsaInInfo.analyzeGroup);
    WTLog::Instance().WriteLog(LOG_DEBUG, "PhyMemSize=%d\n", m_Alg_3GPP_VsaInInfo.PhyMemSize);
    WTLog::Instance().WriteLog(LOG_DEBUG, "PhyMemPtr=%p\n", m_Alg_3GPP_VsaInInfo.PhyMemPtr);
    WTLog::Instance().WriteLog(LOG_DEBUG, "DcFreqCompensate=%d\n", m_Alg_3GPP_VsaInInfo.DcFreqCompensate);
    WTLog::Instance().WriteLog(LOG_DEBUG, "SpectrumRBW=%d\n", m_Alg_3GPP_VsaInInfo.SpectrumRBW);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Standard=%d\n", m_Alg_3GPP_VsaInInfo.Standard);
#endif
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "AlzFrameData3GPP()......"  << gettid() << std::endl;
    Ret = Callback_3GPP_VsaMain(&m_Alg_3GPP_VsaInInfo, &m_Alg_3GPP_VsaOutInfo);

    m_FrameId = FrameId;
    m_AlzAll = (Ret == WT_OK);

    if (Ret != WT_OK)
    {
        return (Ret + WT_ALG_3GPP_BASE_ERROR);
    }
    else
    {
#if 0
        //调试打印
        WTLog::Instance().WriteLog(LOG_DEBUG, "PkgAvgPwrdBm=%f\n", m_Alg_3GPP_VsaOutInfo.RFOut[0].PkgAvgPwrdBm);
        WTLog::Instance().WriteLog(LOG_DEBUG, "PkgPeakPwrdBm=%f\n", m_Alg_3GPP_VsaOutInfo.RFOut[0].PkgPeakPwrdBm);
        WTLog::Instance().WriteLog(LOG_DEBUG, "FrmAvgPwrdBm=%f\n", m_Alg_3GPP_VsaOutInfo.RFOut[0].FrmAvgPwrdBm);
        WTLog::Instance().WriteLog(LOG_DEBUG, "FrmPeakPwrdBm=%f\n", m_Alg_3GPP_VsaOutInfo.RFOut[0].FrmPeakPwrdBm);
#endif
        return Ret;
    }
}

int Analysis::AllocBuf3GPP()
{
    int BufLen = Callback_3GPP_VsaReqMemSize(&m_Alg_3GPP_VsaInInfo);
    // 当现有内存比需要内存小时才申请
    if (m_BufLen[0][0] < BufLen)
    {
        m_AlzBuf[0][0].reset(MemPool::Instance().Alloc(BufLen));
        if (m_AlzBuf[0][0] == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "AllocBuf failed");
            return WT_ALLOC_FAILED;
        }

        m_BufLen[0][0] = BufLen;
    }

    /* # Allocate physical memory for algorithm */
    m_Alg_3GPP_VsaInInfo.PhyMemSize = m_BufLen[0][0];
    m_Alg_3GPP_VsaInInfo.PhyMemPtr = static_cast<char *>(m_AlzBuf[0][0].get());

    return WT_OK;
}

int Analysis::GetVsaResult3GPP(const string &Type, int Stream, int Segment, void **DataBuf, int &DataSize, int &DataType)
{
    int Ret = WT_OK;

    if (!m_AlzAll)
    {
        return WT_NOT_ANALYSIS;
    }

    if (m_ResultBuf == nullptr)
    {
        m_ResultBuf.reset(MemPool::Instance().Alloc(m_ResultBufLen));
        if (m_ResultBuf == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc result buffer failed");
            return WT_ALLOC_FAILED;
        }
    }
    if (m_ListEnable == false)
    {
       ResultItemHandler *pHandler = nullptr;
       if (Stream >= m_Alg_3GPP_VsaOutInfo.StreamNum || Stream < 0)
       {
           return WT_STREAM_ID_ERROR;
       }
       pHandler = AlgResult::Instance().Get3GPPResultItemHandler(Type);
       if (pHandler == nullptr)
       {
           WTLog::Instance().LOGERR(WT_RESULT_NOT_EXIST, "Get3GPPResultItemHandler result type not exist");
           return WT_RESULT_NOT_EXIST;
       }
       *DataBuf = pHandler->GetResult(&m_Alg_3GPP_VsaOutInfo, Stream, Segment, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
    }
    else
    {
        ResultItemHandler *pHandler = nullptr;
        pHandler = AlgResult::Instance().Get3GPPListResultItemHandler(Type);
       if (pHandler == nullptr)
       {
           WTLog::Instance().LOGERR(WT_RESULT_NOT_EXIST, "Get3GPPListResultItemHandler result type not exist");
           return WT_RESULT_NOT_EXIST;
       }
       WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetVsaResult3GPP Type = " << Type << std::endl;
       *DataBuf = pHandler->GetResult(&m_Alg_3GPP_List_VsaOutInfo, Stream, static_cast<char*>(m_ResultBuf.get()) + m_BufPos, DataSize, DataType);
    }
    // 结果数据可能保存再结果buffer中也可能直接是rxout中的地址，如果是保存在结果buffer中则需要更新buffer位置 ???
#if 1
    if ((*DataBuf != nullptr && DataSize == DataType) || (*DataBuf != nullptr && DataType == sizeof(double) * 2)) // 打印结果
    {
        switch (DataType)
        {
        case sizeof(int):
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetVsaResult3GPP Result Data = " << *(int *)(*DataBuf) << std::endl;
            break;
        case sizeof(double):
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetVsaResult3GPP Result Data = " << *(double *)(*DataBuf) << std::endl;
            break;
        case sizeof(double) * 2:
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetVsaResult3GPP Result Data = ( " << *(double *)(*DataBuf) << ", " << *((double *)(*DataBuf) + 1) << ")" << std::endl;
            break;
        default:
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DataType != 4 or 8" << *(double *)(*DataBuf) << std::endl;
        }
    }
#endif
    if (*DataBuf == nullptr)
    {
        Ret = WT_RESULT_UNVALID;
        wtlog::error(SOURCE_LOCATION, Ret, "get VSA result failed");
    }
    else if ((ulong)(*DataBuf) >= (ulong)static_cast<char*>(m_ResultBuf.get()) && (ulong)(*DataBuf) < (ulong)static_cast<char*>(m_ResultBuf.get()) + m_ResultBufLen)
    {
        m_BufPos += DataSize;
    }

    return Ret;
}

int Analysis::AlzAvgData3GPP(int FrameId, int FrameCnt, int AvgMode)
{
    int Ret = WT_OK;
    do
    {
        if (FrameCnt < 1)
        {
            ClearAvgData();
            break;
        }
        // 目前3GPP只有多帧(Frame/SubFrame/Slot)滑动平均
        if (AvgMode == SLIDE_AVG)
        {
            // 3GPP取多帧的平均数据只需分析一次
            Ret = AlzFrameData3GPP(FrameId);
            if (Ret != WT_OK)
            {
                break;
            }
            if (m_AvgCnt > 0)
            {
                Ret = StripAvgData3GPP(FrameCnt);
                if (Ret != WT_OK)
                {
                    break;
                }
            }
        }
        Ret = AlzAvgData3GPP(FrameCnt, AvgMode);

    } while (0);
    if (Ret != WT_OK)
    { // 分析失败则清空
        ClearAvgData();
    }
    return Ret;
}

int Analysis::AlzAvgData3GPP(int FrameCnt, int AvgMode)
{
    Vsa3GPPCommResult ResultTemp;
    (void)ResultTemp;
    (void)FrameCnt;
    int Ret = WT_OK;
    do
    {
        if (m_AlgAvg[0] == nullptr)
        {
            m_AlgAvg[0].reset(new (std::nothrow) AlgAvgResult);
            if (m_AlgAvg[0] == nullptr)
            {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc memory for average result failed");
                Ret = WT_ALLOC_FAILED;
                break;
            }
        }
        int AvgLinkDirect = UNVALID_INT_VAL;
        int AvgChanType = UNVALID_INT_VAL;
        int LastResultDemo = m_AlgAvg[0]->GetLastResultDemo();
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaOutInfo.NBIOT.StatisticCnt = " << m_Alg_3GPP_VsaOutInfo.NBIOT.StatisticCnt << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_AvgCnt = " << m_AvgCnt << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaOutInfo.Standard = " << m_Alg_3GPP_VsaOutInfo.Standard << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "AvgLinkDirect = " << AvgLinkDirect << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "AvgChanType = " << AvgChanType << std::endl;

        if (m_Alg_3GPP_VsaOutInfo.Standard == ALG_3GPP_STD_NB_IOT)
        {
            AvgLinkDirect = m_Alg_3GPP_VsaOutInfo.NBIOT.DeInfo.LinkDirect;
            AvgChanType = m_Alg_3GPP_VsaOutInfo.NBIOT.DeInfo.ChanType;
        }
        // 平均检测自动清空数据处理
        // if (m_Alg_3GPP_VsaOutInfo.NBIOT.StatisticCnt <= 0)
        // {
        //     ClearAvgData();
        //     m_3GPPAvgLinkDirect = UNVALID_INT_VAL;
        //     m_3GPPAvgChanType = UNVALID_INT_VAL;
        //     break;
        // }
        // else 
        if (m_AvgCnt <= 1)
        {
            ClearAvgData();
        }
        else if (LastResultDemo != m_Alg_3GPP_VsaOutInfo.Standard || m_3GPPAvgLinkDirect != AvgLinkDirect || m_3GPPAvgChanType != AvgChanType)
        {
            // 某些分析场景变化则清空
            ClearAvgData();
        }
        for (int j = 0; j < m_Alg_3GPP_VsaOutInfo.StreamNum; j++)
        {
            if (m_Alg_3GPP_VsaOutInfo.Standard == ALG_3GPP_STD_NB_IOT)
            {
                m_Demod = ALG_3GPP_STD_NB_IOT;
                if (AvgMode == SLIDE_AVG)
                {
                    // int i = m_Alg_3GPP_VsaOutInfo.NBIOT.StatisticCnt - (FrameCnt - m_AvgCnt);
                    // if (i < 0)
                    // {
                    //     i = 0;
                    // }
                    // while (i < m_Alg_3GPP_VsaOutInfo.NBIOT.StatisticCnt)
                    // {
                    //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << _FUNCTION_LINE_ << "m_Alg_3GPP_VsaOutInfo.NBIOT.SlotOut[" << i << "].InfoFlg = " << m_Alg_3GPP_VsaOutInfo.NBIOT.SlotOut[i].InfoFlg << std::endl;
                    //     if (m_Alg_3GPP_VsaOutInfo.NBIOT.SlotOut[i].InfoFlg)
                    //     {
                    //         // memcpy(&(ResultTemp.NBIOT), &(m_Alg_3GPP_VsaOutInfo.NBIOT.SlotOut[i]), sizeof(Alg_3GPP_AlzSlotNBIOT));
                    //         // ResultTemp.Standard = ALG_3GPP_STD_NB_IOT;
                    //         // m_Alg3GPPMultiAlgResult[j].push_back(ResultTemp);
                    //     }
                    //     else
                    //     {
                    //         ClearAvgData();
                    //         i++;
                    //         continue;
                    //     }
                    //     if (m_AvgCnt == 0)
                    //     {
                    //         m_AlgAvg[0]->Init3GPP(j, m_Alg3GPPMultiAlgResult[j][m_AvgCnt]);
                    //     }
                    //     else
                    //     {
                    //         m_AlgAvg[0]->Alg3GPPAverage(j, m_Alg3GPPMultiAlgResult[j][m_AvgCnt], m_AvgCnt);
                    //     }
                    //     i++;
                    //     m_AvgCnt++;
                    // }
                    // m_AlgAvg[0]->Alg3GPPMultiMax(j, m_Alg3GPPMultiAlgResult[j]);
                    // m_AlgAvg[0]->Alg3GPPMultiMin(j, m_Alg3GPPMultiAlgResult[j]);
                }
            }
        }
        if (m_AvgCnt > 0)
        {
            m_AlgAvg[0]->SetLastResultDemo(m_Alg_3GPP_VsaOutInfo.Standard);
            m_3GPPAvgLinkDirect = AvgChanType;
            m_3GPPAvgChanType = AvgChanType;
        }
    } while (0);

    return Ret;
}

int Analysis::StripAvgData3GPP(int FrameCnt)
{
    int Ret = WT_OK;

    for (int j = 0; j < m_Alg_3GPP_VsaOutInfo.StreamNum; j++)
    {
        if (m_Alg_3GPP_VsaOutInfo.Standard == ALG_3GPP_STD_NB_IOT)
        {
            // for (int i = 0; i < m_Alg_3GPP_VsaOutInfo.NBIOT.StatisticCnt; i++)
            // {
            //     if (m_AvgCnt >= 2)
            //     {
            //         if (m_AlgAvg[0] == nullptr)
            //         {
            //             WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no avg result exist");
            //             return WT_NO_AVG_RESULT;
            //         }
            //         m_AlgAvg[0]->Alg3GPPStrip(j, m_Alg3GPPMultiAlgResult[j].front(), m_AvgCnt);
            //         m_AvgCnt--;
            //     }
            //     else
            //     {
            //         m_AvgCnt = 0;
            //     }
            //     if (!m_Alg3GPPMultiAlgResult[j].empty())
            //     {
            //         m_Alg3GPPMultiAlgResult[j].pop_front();
            //     }
            // }
            while (m_AvgCnt > FrameCnt && m_AvgCnt >= 2)
            {
                if (m_AlgAvg[0] == nullptr)
                {
                    WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no avg result exist");
                    return WT_NO_AVG_RESULT;
                }
                m_AlgAvg[0]->Alg3GPPStrip(j, m_Alg3GPPMultiAlgResult[j].front(), m_AvgCnt);
                m_AvgCnt--;
                if (!m_Alg3GPPMultiAlgResult[j].empty())
                {
                    m_Alg3GPPMultiAlgResult[j].pop_front();
                }
            }
        }
    }
    return Ret;
}

int Analysis::Get3GPPAvgResult(int Segment, int Stream, const Vsa3GPPCommResult **Avg3GPPResult,
                               const Vsa3GPPCommResult **Max3GPPResult, const Vsa3GPPCommResult **Min3GPPResult)
{
    (void)Segment;
    if (m_AlgAvg[0] != nullptr && m_AvgCnt)
    {
        *Avg3GPPResult = &m_AlgAvg[0]->Get3GPPAvg(Stream);
        *Max3GPPResult = &m_AlgAvg[0]->Get3GPPMax(Stream);
        *Min3GPPResult = &m_AlgAvg[0]->Get3GPPMin(Stream);
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_NO_AVG_RESULT, "no 3GPP avg result exist");
        return WT_NO_AVG_RESULT;
    }
}

// 3GPP_END//////////////////////////////////////////////////////////////////////////////////////////////////////////////
