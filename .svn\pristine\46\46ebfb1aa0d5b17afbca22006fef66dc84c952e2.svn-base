//*****************************************************************************
//File: devtype.h
//Describe:devlib.h公有函数用到的类型定义,
//其他模块调用devlib.h公有函数时，在H文件包含devtype.h, 在CPP文件包含devlib.h，以减弱相互依赖关系。
//Author：yuanyongchun
//Date: 2020.01.29
//*****************************************************************************
#ifndef _DEVTYPE_H_
#define _DEVTYPE_H_

#include "defines.h"
#include "../wtspec.h"

//位操作
#define SetBit(x, y)            ((x) |= (unsigned long long)(0x1u) << (y))             //置位
#define ClearBit(x, y)          ((x) &= ~((unsigned long long)(0x1u) << (y)))          //清零
#define NegateBit(x, y)         ((x) ^= ((unsigned long long)(0x1u) << (y)))           //数x的y位取反
#define GetBit(x, y)            (((x) >> (y)) & 0x1u)            //获取第y位

#define DEFAULT_SMAPLE_RATE         MAX_SMAPLE_RATE
#define DEFAULT_SMAPLE_RATE_MHZ     MAX_SMAPLE_RATE_MHZ

//仪器硬件数量定义
#define MOD_TYPE_COUNT      2               //业务单元板模块数量
#define BUSI_UB_TYPE_COUNT  2               //业务单元板类型数量
#define UB_TYPE_COUNT       3               //业务类型数量

//空间大小定义
#define FLASH_PAGE_SIZE     256             //FLASH Page大小
#define EEPROM_PAGE_SIZE    32             //EEPROM Page大小
#define CM_ZONE_SIZE        32              //加密芯片用户区/配置区大小

/* maximum size of a single DMA transfer descriptor */
#define XDMA_DESC_BLEN_BITS	28
#define XDMA_DESC_BLEN_MAX	((1 << (XDMA_DESC_BLEN_BITS)) - 1)
//XDMA最大单次可传输XDMA_DESC_BLEN_MAX，但为了少申请内存，当前最大DMA数据长度为128M

#ifdef WT418_FW
#define DMA_BUF_SIZE        (128 << 20)  //DMA大小，128M
#else
#define DMA_BUF_SIZE        (128 << 20)  //DMA大小，128M
#endif

#define MAX_NAME_SIZE 256      // 带有文件名的命令中文件名的最大长度

#define DMA_DDR_PN_MAX (8*1024)/DMA_BUF_SIZE

#define TEMPERATURE_NO_INIT -256

#define CODE_NO_INIT  0x7FFFFFFF

//启动时读取AD7682次数
#define AD7682_INIT_READ_CNT 5
#define AD7689_INIT_READ_CNT 5
#define MAX_DAC_CODE 32767	  // DAC code最大值
#define MIN_DAC_CODE -32768   // DAC code最小值



//单元板类型
enum WT_DEV_TYPE
{
    DEV_TYPE_VSA,               //VSA单元板
    DEV_TYPE_VSG,               //VSG单元板
    DEV_TYPE_BACK,              //背板单元板
    DEV_TYPE_MAX,

    DEV_TYPE_BUSI = DEV_TYPE_VSA,
};

enum WT_BW_E
{
    WT_BW_CW,
    WT_BW_20M,
    WT_BW_40M,
    WT_BW_80M,
    WT_BW_160M,
    WT_BW_320M,
    WT_BW_NUM
};

//硬件版本
enum WT_UB_HARDWARE_VERSION
{
    VERSION_A = 0,
    VERSION_B,
    VERSION_C,
    VERSION_D,
    VERSION_E,
    VERSION_END
};

//测试仪版本(硬件)
enum WT_TESTER_HW_TYPE
{
    HW_WT448 = 0,
    HW_WT428,
    HW_WT418,
    HW_TYPE_END
};

//当前业务单元工作模式枚举类型
enum WT_DEVICE_MODE
{
    DEVICE_MODE_SISO,                       //SISO
    DEVICE_MODE_MIMO_MULTI_MASTER,          //多机MIMO MASTER
    DEVICE_MODE_MIMO_SINGLE_MASTER,         //单机MIMO MASTER
    DEVICE_MODE_MIMO_MULTI_SLAVE,           //多机MIMO SLAVE
    DEVICE_MODE_MIMO_SINGLE_SLAVE,          //单机MIMO SLAVE
    DEVICE_MODE_80_80_MASTER,               //AC80+80主
    DEVICE_MODE_80_80_SLAVE,                //AC80+80从
    DEVICE_MODE_MIMO_MULTI_MASTER_8080_MASTER,  //8080 MIMO主机部分的8080主
    DEVICE_MODE_MIMO_MULTI_MASTER_8080_SLAVE,   //8080 MIMO主机部分的8080从
    DEVICE_MODE_MIMO_MULTI_SLAVE_8080_MASTER,   //8080 MIMO中的从机部分的8080主
    DEVICE_MODE_MIMO_MULTI_SLAVE_8080_SLAVE,    //8080 MIMO中的从机部分的8080从
    DEVICE_MODE_DUL_RF_PARAM_AGC_MASTER,   //8080 双射频参数单端口模式AGC,主
    DEVICE_MODE_DUL_RF_PARAM_AGC_SLAVE,    //8080 双射频参数单端口模式AGC,从    
};
static inline int Is8080Master(int WorkMode)
{
    return WorkMode == DEVICE_MODE_80_80_MASTER
           || WorkMode == DEVICE_MODE_MIMO_MULTI_MASTER_8080_MASTER
           || WorkMode == DEVICE_MODE_MIMO_MULTI_SLAVE_8080_MASTER
           || WorkMode == DEVICE_MODE_DUL_RF_PARAM_AGC_MASTER;
}
static inline int Is8080Slave(int WorkMode)
{
    return WorkMode == DEVICE_MODE_80_80_SLAVE
           || WorkMode == DEVICE_MODE_MIMO_MULTI_MASTER_8080_SLAVE
           || WorkMode == DEVICE_MODE_MIMO_MULTI_SLAVE_8080_SLAVE
           || WorkMode == DEVICE_MODE_DUL_RF_PARAM_AGC_SLAVE;
}

static inline int Is8080(int WorkMode)
{
    return (Is8080Master(WorkMode) || Is8080Slave(WorkMode));
}

static inline int IsSlaveMode(int WorkMode)
{
    return WorkMode == DEVICE_MODE_MIMO_MULTI_SLAVE
           || WorkMode == DEVICE_MODE_MIMO_SINGLE_SLAVE
           || WorkMode == DEVICE_MODE_MIMO_MULTI_SLAVE_8080_MASTER
           || WorkMode == DEVICE_MODE_MIMO_MULTI_SLAVE_8080_SLAVE;
}

static inline int IsMasterMode(int WorkMode)
{
    return WorkMode == DEVICE_MODE_MIMO_MULTI_MASTER
           || WorkMode == DEVICE_MODE_MIMO_SINGLE_MASTER
           || WorkMode == DEVICE_MODE_80_80_MASTER
           || WorkMode == DEVICE_MODE_MIMO_MULTI_MASTER_8080_MASTER
           || WorkMode == DEVICE_MODE_MIMO_MULTI_MASTER_8080_SLAVE;
}

static inline int IsSingleMode(int WorkMode)
{
    return WorkMode == DEVICE_MODE_SISO
           || WorkMode == DEVICE_MODE_80_80_MASTER
           || WorkMode == DEVICE_MODE_80_80_SLAVE;
}

//触发类型
enum WT_TRIG_TYPE_E
{
    WT_TRIG_TYPE_FREE_RUN,                  // Free running ADC sampling.
    WT_TRIG_TYPE_EXT,                       // ADC External Trigger selected.
    WT_TRIG_TYPE_IF,                        // ADC IF Trigger selected - trigger calibration will be performed.
    WT_TRIG_TYPE_IF_NO_CAL
};

// trigger type
enum WT_TRIG_EDGE_ENUM
{
    WT_TRIG_DEGE_POSITIVE, // Rising edge signal detection initiates a trigger event.
    WT_TRIG_DEGE_NEGATIVE, // Falling edge signal detection initiates a trigger event
};

enum WT_VSG_GAP_POWER_TYPE
{
    GAP_POWER_NOT_DEBUG = 2,
    GAP_POWER_DEBUG_OFF = 0,
    GAP_POWER_DEBUG_ON = 1,
};
enum WT_ATT_CAL_ATT_E
{
    WT_ATT_CAL_RF_ATT0  = 100,
    WT_ATT_CAL_RF_ATT1,
    WT_ATT_CAL_RF_ATT2,
    WT_ATT_CAL_RF_ATT3,
    WT_ATT_CAL_RF_ATT4,
    WT_ATT_CAL_RF_ATT5,
    WT_ATT_CAL_SW_ATT,
    WT_ATT_CAL_ATT_MAX,
};

//触发配置结构体类型
struct TriggerCfg
{
    int Type;                               //触发类型，WT_TRIG_TYPE_ENUM类型
    int PreSmpCnt;                          //触发前预留的采样点数
    int SmpCnt;                             //总采样点数
    int DmaCnt;                             //dma传输的数据长度
    int TrigLevel;                          //触发门限
    int Timeout;                            //触发超时
    int TrigGapTime;
    int TrigEdge;
};

//触发配置结构体类型
struct CommonTrigType
{
    unsigned long long TriggerTimeoutCnt;
    int OutsideTriggerValidLen;
    int VsgSeqTrigRepeatRum;
};

//触发配置结构体类型
struct VSATriggerType
{
    int TrigType;
    int TrigOffset;
    int SegmentSampleCnt;
    int SegmentSampleRate;
    int SegmentTriggerLevel;
    int SegmentDuration;
    int DCValue;
    int LeastGapLen;
    int LeastFrameLen;
    int GenTriggerType;
    int PreTrig;
};

// VSA配置结构体类型
struct VSAConfigType
{
    double Freq;                //信号中心频率
    double Ampl;                //信号参考电平,单位：dBm
    double BaseBandGain;        //基带gain值，从基带采样时使用,单位：dBm
    double SamplingTime;        //采样时长,单位：s
    double SamplingFreq;        //采用频率,单位：HZ
    double FreqOffsetHz;        //频偏
    double TrigPreTime;         //Trig到数据后指定时间长度的数据不计算在总的时长中，最终这部分长度数据被丢弃
    double TrigTimeout;         //Trig超时时间
    double TrigLevel;           //Trig电平,单位：dBm
    double TrigGapTime;         //Trig gap time
    int TrigType;               //Trig类型，取值见WT_TRIG_TYPE_ENUM
    int TrigEdge;               //Trig边缘
    int RFPort;                 //RF端口号，取值见WT_PORT_ENUM
    int RFPortState;            //RF端口状态，取值见WT_PORT_VD_STATE_E
    int DeviceMode;             //当前的工作模式:SISO/MIMO主从机/80+80
    int DcOffsetI;              //模拟IQ模式：IQ偏移
    int DcOffsetQ;              //模拟IQ模式：IQ偏移
    int NoiseCompensation;      //噪声补偿

    double FrameTime;  // 最小有效信号长度
    int GenTriggerType;  // list mod vsa同步vsg的触发类型，0：不触发，1：在测量开始时同步vsg；2；在测量结束时同步，3：既在测量开始时同步，又在结束时同步
    double OutTriggerValidTime;  //外部触发信号有效长度
};

enum WT_VSG_MODE_TYPE
{
    WT_VSG_MODE_NORMAL = 0,
    WT_VSG_MODE_DEVM = 1,
    WT_VSG_MODE_VSA_VSG = 2,
    WT_VSG_MODE_VSG_VSA = 3,
};

enum WT_TRIG_TYPE_ENUM
{
	// Free running ADC sampling.
    WT_VSA_VSG_TRIG_TYPE_FREE_RUN,
    WT_VSA_TRIG_TYPE_SINGAL,
    WT_VSA_TRIG_TYPE_OUTSIDE,
	// FPGA内部触发 VSA->VSG，触发时机为VSA开始捕获信号
	WT_VSG_TRIG_TYPE_ENABLE,
	// FPGA内部触发 VSA->VSG，触发时机为VSA完成捕获信号
	WT_VSG_TRIG_TYPE_INCREMENT
};

// VSG配置结构体类型
struct VSGConfigType
{
    double Freq;         //信号中心频率
    double FreqOffsetHz; //频偏
    double Power;        //发送功率,单位：dBm
    int BroadcastEnable;
    double BroadcastPower[9];        //发送功率,单位：dBm
    double Gain;         //数据增益
    double SamplingRate; //采样率
    int RFPort;          // RF端口号，取值见WT_PORT_ENUM
    int RFPortState;     // RF端口状态，取值见WT_PORT_VD_STATE_E
    int DeviceMode;      //当前的工作模式:SISO/MIMO主从机/80+80
    int VsgIfgStatus;    //当前的工作模式:SISO/MIMO主从机/80+80
    int WaveBw;          //当前信号带宽
    int DcOffsetI;       //模拟IQ模式：IQ偏移
    int DcOffsetQ;       //模拟IQ模式：IQ偏移
    double CmVolt;       //共模电压

    int LoopCnt;         // 循环次数
    int IFGRamdomMode;   // IFG随机模式
    double IFG;        // 循环间隔，单位采样点数
    double RandomWaveGapMax; // 随机间隔的范围上限，单位：second(秒)
    double RandomWaveGapMin; // 随机间隔的范围下限，单位：second(秒)
};

struct VSGTriggerType
{
    int TrigType;
    int TrigOffsetCnt;
    int SegmentDurationCnt;
};

//PN全局配置
struct PnItemHead
{
    u32 StartIdx;               //第一个发送的PN编号
    u32 SendCnt;                //发送多少个PN
};

// PN控制条目
struct RfPnItem
{
    char FilePath[MAX_NAME_SIZE];
    unsigned long Addr; //起始地址
    unsigned int Len;   // Tx数据长度
    unsigned long DDRaddr;
};

//背板硬件信息
struct BackPlaneUnitInfo
{
    char SN[80];                            //背板SN码
    char FPGAVersion[40];                   //背板FPGA逻辑版本号
    char FPGADate[20];                      //背板FPGA编译日期(年月日)
    char FPGATime[20];                      //背板FPGA编译时间(时分秒)
    char BPHWVersion[40];                   //背板硬件版本
    char SwitchHWVersion[40];               //开关板硬件版本

    char RemarkInfo[40];                    //背板备注信息
};

//业务板硬件信息
struct BusinessBoardUnitInfo
{
    char Type[8];                           //业务板类型（VSA/VSG）
    char SN[80];                            //业务板SN码
    char HWVersion[40];                     //业务板硬件升级版本
    char FPGAVersion[40];                   //基带板FPGA版本
    char FPGADate[20];                      //基带板FPGA编译日期(年月日)
    char FPGATime[20];                      //基带板FPGA编译时间(时分秒)
    char RFHWVersion[40];                   //射频板硬件版本
    char RemarkInfo[40];                    //业务板备注信息
};


// 扩展模式配置
struct ExtModeType
{
    int Mode; //模式选择
    union
    {
        struct
        {
            int VsgDelay;
        } TBT; // TBT模式参数
        struct
        {
            int LeadTime;
            int DelayTime;
            int GapTime;
            int DebugDelay;
        } Devm; // DEVM模式参数
    } Param;
};


//Tb测试模式
enum WT_TB_MODE_TYPE_E
{
    WT_TB_MODE,      //频段类型
    WT_WLAN_MODE,    //硬件资源类型
};

//位控制结构体类型
struct TbModeType
{
    int Mode;                    //模式选择
    int VsgUnitSel;              //配对的VSG单元
};

struct TBTStaParamType
{
    int TBTStaMode;
    int TrigSel;
    int Repeat;
    int Delay;
};

struct ATTCalConfigType
{
    int Enable;
};

struct SeqTimeType
{
    double Duration;       // 该参数乘以DurationLoopCnt*Duration*SamplingFreq+DurationExtendCnt 为实际长度（点数）
    double Meaoffset;      // 对于Rx seg而言，该参数决定了seg之间的gap
    double Meadura;        // 对于Rx seg而言，该参数无效
};

// PN控制条目
struct SegBehaviorType
{
    int PnIndex;
    int PnDataLen;
    double ReSampleRate;
    int PnRepeat;
    double IFG;
    double PnIfg;
    double PnHead;
    double PnTail;
    int IFGCnt;
    int PnIfgCnt;
    int PnHeadCnt;
    int PnTailCnt;
    int IFGCntLen;
    int PnDataRepeat;
    int PnDataExtendCnt;
    int PnDataSendLenCnt;
};

struct DevBufType
{
    char* pBuf;        // Buf的结构体
    int offset;
    int size;
};
#endif