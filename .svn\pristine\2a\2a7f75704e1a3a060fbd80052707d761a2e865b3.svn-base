//*********************************************************************************
//  File: launch.h
//  创建unix socket和eventfd资源，然后启动WT-Link和WT-Server进程
//  Data: 2016.7.11
//*********************************************************************************

#ifndef __WT_LAUNCHER_H__
#define __WT_LAUNCHER_H__

#include <vector>
#include <string>
#include <functional>

using RegisterServerHandle = std::function<int(int ServerId)>;

class Launcher
{
public:
    Launcher();
    ~Launcher();

    static Launcher &Instance();

    //*****************************************************************************
    // 启动WT-Link和WT-Server进程
    // 参数：无
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int LauchProcess(void);

    //*****************************************************************************
    // 重启WT-Server进程，参数合法性由调用者保证
    // 参数[IN]：ServerId : 需要重启WT-Server ID
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int RestartServer(int ServerId);

    int ShutdownServers();

    //*****************************************************************************
    // 重启WT-Scpi-Server进程
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int RestartScpi();

    //*****************************************************************************
    // 获取服务进程数量，即子仪器数量
    // 参数 [OUT]: Cnt : 服务进程数量
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int GetServerCnt(int &Cnt);

    //*****************************************************************************
    // 获取WT-Server用来向WT-Manager上报状态的eventfd
    // 参数 [IN]：ServerId : WT-Server ID
    // 参数 [OUT]: Fd : 进程使用的fd
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int GetServerEventfd(int ServerId, int &Fd);

    //*****************************************************************************
    // 获取WT-Scpi-Server用来向WT-Manager上报状态的eventfd
    // 参数 [OUT]: Fd : 进程使用的fd
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int GetScpiEventfd(int &Fd);

    //*****************************************************************************
    // 获取WT-Server端使用的unix socket fd
    // 参数 [IN]：ServerId : WT-Server ID
    // 参数 [OUT]: Fd : 进程使用的socket fd
    // 返回值：成功或失败错误码
    //*****************************************************************************
    int GetServerSocket(int ServerId, int &Fd);

    //*****************************************************************************
    // 获取WT-Link端使用的unix socket fd
    // 参数: 无
    // 返回值: socket fd
    //*****************************************************************************
    int GetMgrSocket(void);

    //注册函数
    void SetReRegisterServerHandle(const RegisterServerHandle &&Handle) { m_ReRegisterServerHandle = Handle; }

private:
    int CreateSocketPairs(void);
    int CreateEventfds(void);
    int ReCreateEventfds(int Index);
    int LaunchServer(int ServerId);
    int LaunchLink(void);
    int LaunchScpi(void);

private:
    std::string m_Path;                     //当前程序所在的路径
    int m_MgrSocket;                        //与WT-Link通信所使用的socket

    int m_LinkPid;                          //WT-Link进程PID
    std::vector<int> m_LinkSocket;          //WT-Link与WT-Server和WT-Manager通信所使用的socket

    int m_ServerCnt;                        //WT-Server数量
    std::vector<int> m_ServerSocket;        //WT-Server与WT-Link通信所使用的socket
    std::vector<int> m_ServerEventfd;       //WT-Server向WT-Manager上报状态所使用的eventfd
    std::vector<int> m_ServerPid;           //WT-Server进程的PID
    int m_ScpiEventfd;                      //WT-Scpi-Server向WT-Manager上报状态所使用的eventfd
    RegisterServerHandle m_ReRegisterServerHandle = nullptr;
};

#endif
