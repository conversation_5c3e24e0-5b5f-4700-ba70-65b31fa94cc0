#ifndef _INTERNAL_H_
#define  _INTERNAL_H_
#include "TypeDef.h"
#include "StructuresDef.h"

#define MAX_BUFF_SIZE (1024)

#define SUP_MAX_BUFF_SIZE (1024*1024)

/// 最大硬件错误码大小
#define MAX_HARDERRCODE_SIZE (MAX_BUFF_SIZE * 5)

/// 线衰文件最大值
#define MAX_PATHLOSSFILE_SIZE (MAX_BUFF_SIZE * 200)


#define MAX_SPECT_POINT_NUM  12000  /// 频谱图的最多点数，120M/10k(ah)（采样率/RBW）1G/100k,宽屏(不包含ah)

#define DF_Max_ChannalNum_11ac       512
#define DF_Max_FlatnessNum          2048

#define  DF_Max_Device  8
enum
{
    enDataFormat_Float64,
    enDataFormat_Int16,
    enDataFormat_None
};

#define INFINITE_DAY 0XFFFFFFFF


enum WT_FIRM_RESTORE_OPTION
{
	WT_RESTORE_LASTED = 1,                              /// 回退到上一版本
	WT_RESTORE_ORIGINAL,                                /// 回退到原始版本

};

/// 上位机log类型
enum LOG_TYPE
{
	LOGTYPE_LOG,
	LOGTYPE_WARNING,
	LOGTYPE_ERROR,
	LOGTYPE_EXCEPTION
};

/// 下位机仪器log类型
enum TEST_LOG_TYPE
{
	LOGTYPE_TEST_SYS_LOG,
	LOGTYPE_TEST_OPERATE_LOG,
	LOGTYPE_TEST_ERROR,
	LOGTYPE_TEST_EXCEPTION
};

enum WT_MONITOR_TYPE
{
	MON_VSA_STATUS,                                     /// 监视VSA状态，包括配置的参数
	MON_VSA_RESULT,                                     /// VSA结果数据

	MON_VSG_STATUS,                                     /// VSG状态，包括配置参数
	MON_VSG_PN_STATUS,                                  /// PN配置参数
	MON_VSA_ANALYZE_STATUS,                             /// VSA分析参数
	MON_METER_SETTING,
	MON_SCPI_CMD,
	WT_MONITOR_MAX,
};

/// 背板信息
typedef struct
{
	char FPGAVersion[WT_COM_MAX_LEN];                   /// 背板FPGA逻辑版本号
	char FPGADate[WT_VER_DATA_LEN];                     /// 背板FPGA编译日期(年月日)
	char FPGATime[WT_VER_DATA_LEN];                     /// 背板FPGA编译时间(时分秒)
	char BPHWVersion[WT_COM_MAX_LEN];                   /// 背板硬件版本
	char SwitchHWVersion[WT_COM_MAX_LEN];               /// 开关板硬件版本
	char CalDate[WT_VER_DATA_LEN];                      /// 校准日期 "2012-05-04  18:36:56"
	char RemarkInfo[WT_COM_MAX_LEN];                    /// 背板备注信息
} BackPlaneInfo;

/// 业务板信息
typedef struct
{
	char Type[WT_MODULES_TYPE_LEN];                     /// 业务板类型（VSA/VSG）
	char SN[WT_SN_MAX_LEN];                             /// 业务板SN码
	char FPGAVersion[WT_COM_MAX_LEN];                   /// 业务板FPGA版本
	char FPGADate[WT_VER_DATA_LEN];                     /// 业务板FPGA编译日期(年月日)
	char FPGATime[WT_VER_DATA_LEN];                     /// 业务板FPGA编译时间(时分秒)
	char BBHWVersion[WT_COM_MAX_LEN];                   /// 业务板基带板版本
	char RFHWVersion[WT_COM_MAX_LEN];                   /// 业务板射频板版本
	char CalDate[WT_VER_DATA_LEN];                      /// 校准日期 "2012-05-04  18:36:56"
	char RemarkInfo[WT_COM_MAX_LEN];                    /// 业务板备注信息
} BusinessBoardInfo;

/// 仪器信息
typedef struct
{
	char IP[IP_MAX_LEN];                                    /// IP地址
	char SubMask[IP_MAX_LEN];                               /// 子网掩码
	char GateWay[IP_MAX_LEN];                               /// 网关

	int TesterType;                                         /// 仪器类型 WT_TESTER_TYPE
	char SN[WT_SN_MAX_LEN];                                 /// 仪器SN码
	char Name[WT_COM_MAX_LEN];                              /// 别名  "ITEST  Tester"
	char Mac[MAC_ADDR_MAX_LEN];                             /// MAC 地址 "DC-A4-B5-C7-E1-F8"
	char FwVersion[WT_COM_MAX_LEN];                         /// 固件版本
	char ALGVersion[WT_COM_MAX_LEN];                        /// 算法版本
	int  BusiBoardCount;                                    /// 业务板数量
	BackPlaneInfo BPInfo;                                   /// 背板信息
	BusinessBoardInfo BusiBoardInfo[WT_MODULES_MAX_NUM];    /// 业务板信息
	char ProduceDate[WT_VER_DATA_LEN];                      /// 生产日期 "2012-05-04  18:36:56"
} TesterInfo;

/// FPGA配置
typedef struct
{
} FpgaConfig;


/// 操作系统状态
typedef struct
{
	int CpuStatu;                                               /// CPU使用状态
	int MemoryStatu;                                            /// 内存使用状态
} OSStatu;

/// 下位机进程状态
typedef struct
{
	int CpuStatu;                                               /// CPU占用率
	int MemoryStatu;                                            /// 内存占用率
} ProcStatu;

/// 设备温度
typedef struct
{
	int Cpu;                                                    /// CPU温度
	int BB;                                                     /// 基带板温度
	int RF;                                                     /// 射频板温度
} DeviceTemp;

/// 设备电压
typedef struct
{
    /// 器件的实际电压值 = 电压值*倍压（ActualVoltValue = VoltValue * MultVolt）
    int VoltChannel;                                        /// 电压通道
    int MultVolt;                                           /// 倍压
    char Board[64];                                         /// 所在的单元板
    char VoltChannelInfo[256];                              /// 电压信息
    double VoltValue;                                       /// 实际电压值
    double LowLimit;                                        /// 电压下限值
    double HighLimit;                                       /// 电压上限值
} DeviceVoltage;

/// 时间类型
typedef struct
{
	unsigned short Sec;                                         ///  Seconds.     [0-60] (1 leap second)
	unsigned short Min;                                         ///  Minutes.     [0-59]
	unsigned short Hour;                                        ///  Hours.       [0-23]
	unsigned short Mday;                                        ///  Day.         [1-31]
	unsigned short Mon;                                         ///  Month.       [0-11]
	unsigned short Year;                                        ///  Year - 1900.
} TIME_TYPE;

/// license条目信息
typedef struct
{
	char LicTechName[WT_LICENSE_NAME_LEN];                      /// License Technology类型名称
	/// int         LicTechValue;                                 /// License Technology类型枚举值
	char LicName[WT_LICENSE_NAME_LEN];                          /// Lic名称
	int         LicType;                                        /// Lic类型，取值见WT_LIC_TYPE_E
	int         LicValue;                                       /// 具体Lic类型对应的业务值
	int         ResourceNum;                                    /// 硬件可用资源数，Lic类型为硬件类型时用，否则为0
	TIME_TYPE   StartTime;                                      /// License开始时间
	TIME_TYPE   EndTime;                                        /// License结束时间
	char        Remarks[16];                                    /// License备注信息
} LicItemInfo;


/// 日志筛选器
typedef struct
{
	unsigned int Logtype;                                       /// 日志类型
	tm       Starttime;                                         /// 开始时间
	tm       Endtime;                                           /// 结束时间
	char	SQL[MAX_BUFF_SIZE];									/// 自定义SQL查询
} LogFilter;

/// 日志筛选器
typedef struct
{
	unsigned int Logtype;											/// 日志类型
	char		Starttime[MAX_BUFF_SIZE];							/// 开始时间，格式YYYY-MMM-DD HH:MM:SS
	char		Endtime[MAX_BUFF_SIZE];								/// 结束时间，格式YYYY-MMM-DD HH:MM:SS
	char		SQL[MAX_BUFF_SIZE];									/// 自定义SQL查询
} DialogLogFilter;

typedef struct
{

} DiagnoseSetting;
/// ??
typedef struct
{
	int BoardID;		/// 单板编号
	int LinkID;			/// 链路编号
	int ComponentID;	/// 器件编号
	int ChipID;			/// 片选编号
	int Addr;			/// 地址
} ComponentLocation;

/// 除算法外WT300额外定义的分析结构
#define WT_RES_CONST_DATA                       "const.data"
#define WT_RES_CONST_PILOT                      "const.pilot"
#define WT_RES_CONST_REF                        "const.ref"
#define WT_RES_EVM_SYMBOL                       "evm.symbol"
#define WT_RES__EVM_CARRIER                     "evm.carrier"
#define WT_RES_EVM_CARRIER_PILOT                "evm.carrier.pilot"
#define WT_RES_POINT_POWER                      "points.power"
#define WT_RES_AVG_POWER                        "avg.power"
#define WT_RES_DATA_INFO                        "data.info"
#define WT_RES_BASE_RESULT                      "result.base"
#define WT_RES_BASE_RESULT_COMPOSITE            "result.base.composite"

typedef struct
{
	char LicTechName[WT_LICENSE_NAME_LEN];
	char Version[WT_COM_MAX_LEN];
} GUIVersion;

/// 监视机信息
typedef struct
{
	char IP[IP_MAX_LEN];
} MonitorInfo;


/// 校线参数
typedef struct
{
	double  Freq;                                       /// 中心频率,单位：Hz
	int     VsaPort;                                    /// 接收端口，WT_PORT_ENUM
	int     VsgPort;                                    /// 发送端口，WT_PORT_ENUM
	int     WaveType;                                   /// Wave类型，WT_SIGNAL_ENUM，仅当为SIG_USERFILE时才读取wave文件，WT-208仪器专用
	char    *Wave;                                      /// VSG数据文件,如果为null,只更新设备配置，不更新wave文件
	int     TimeoutWaiting;                             /// 在多连接情况下，等待的最大时间，单位sec
} CableVerifyParameter;

/// 校线选项
typedef struct
{
	int CalType;                                        /// 校线类型(普通校线or辅线校线)
	int CalOption;                                      /// 校线选项(2.4G , 5G , all)
} CalTestingInAgrs;



/// 校线打印选项
typedef struct
{
	double Frequency;
	double Attenuations[DF_MAX_ATT];
	int    AttValidCnt;                                 /// 有效衰减数量
	int    CalibrationBand;
	int    CalSpecialTip;                               /// 需接cable2提示,如果提示，则需暂停等待接线
	int    CurrentCalType;                              /// 当前校准类型,用于上层处理数据
} CalTestingCallbackArgs;

enum DIALOG_ENUM
{
	WT_CONNECT_TYPE_DIALOG = WT_CONNECT_TYPE_DEMO+1
};

/// 业务板类型
enum WT_DEV_TYPE
{
	DEV_TYPE_VSA,               /// VSA单元板
	DEV_TYPE_VSG,               /// VSG单元板
	DEV_TYPE_UNKNOWN
};
#define MAX_RF_TEMP_SNESEOR		2

typedef struct
{
	/// 背板温度,固定30℃
	double BackBoardTemp;

	/// 基带板温度,固定30℃
	/// m_BussiBaseBoardTemp[模块ID]
	double BussiBaseBoardTemp[WT_SUB_TESTER_INDEX_MAX];

	/// 开关板温度
	/// SwitchBoardTemp[开关板ID]
	double SwitchBoardTemp[WT_PORT_RF8];

	/// 业务板板振荡器温度
	/// BussiModLoDetTemp[业务板类型][模块ID]
	double BussiModLoDetTemp[DEV_TYPE_UNKNOWN][WT_SUB_TESTER_INDEX_MAX];
	double BussiMixLoDetTemp[DEV_TYPE_UNKNOWN][WT_SUB_TESTER_INDEX_MAX];

	/// 业务板VSA DEM温度
	/// BussiVsaDemTemp[模块ID]
	double BussiVsaDemTemp[WT_SUB_TESTER_INDEX_MAX];
} DevTemperature;

typedef struct
{
	int valid;
	TIME_TYPE time;
	DevTemperature temperature;
}DevTempSave;

typedef struct 
{
    int DiskUsageCur;   //仪器硬盘当前已使用百分比
    int DiskUsageMax;   //仪器硬盘最大可使用百分比
    int WaveUsageCur;   //WAVE文件夹当前已使用大小，单位Kb
    int WaveUsageMax;   //WAVE文件夹最大可使用大小，单位Kb
    int TmpUsageCur;    //TMP文件夹当前已使用大小，单位Kb
    int TmpUsageMax;    //TMP文件夹最大可使用大小，单位Kb
}TesterDiskUsage;


/// 帧过滤
enum WT_COMPARE_TYPE
{
    GE,             /// GREATER_THAN_OR_EQUAL大于等于 >=
    LE,             /// LESS_THAN_OR_EQUAL小于等于 <=
    GT,             /// GREATER_THAN大于 >
    LT,             /// LESS_THAN小于<
    EQ,             /// EQUAL等于 ==
    NE,             /// NOT_EQUAL不等于!=
    MAX_COMPARE_TYPE
};

enum WT_FILTER_TYPE
{
    FILT_BY_PSDULENGTH,         /// 根据Psdu长度过滤
    FILT_BY_DATARATE,           /// 根据速率过滤
    FILT_BY_POWER,              /// 根据功率过滤（有帧功率时取帧功率，没有帧功率则取power all）
    MAX_FILTER_TYPE
};

typedef struct
{
    int IsEnable;                               /// 是否使能过滤功能
    int FilterType;                             /// 过滤类型见枚举WT_FILTER_TYPE
    int CompareType;                            /// 比较类型见枚举WT_COMPARE_TYPE
    int FiltPsduLength;                         /// 指定的Psdu长度
    int FiltMcs;                                /// 指定的mcs模式
    double FiltDataRate;                        /// 指定的速率
    double FiltPower;                           /// 指定的Power值
    double Reserved[20];                        /// 扩展保留位
}ResultFilter;

#define ATT_COUNT_MAX   (6)
#define ATT_CODE_MAX    (63)

typedef struct
{
    int Type;
    int ATTId;
    int Direction;
    int StartCode;
    int EndCode;
    int SwAttInit;
    int ATTInitArray[ATT_COUNT_MAX];
    double Reserved[20];
}ATTCalCfg;

typedef struct
{
    double Gain[ATT_CODE_MAX_API + 1];
    double Reserved[20];
}ATTCalResult;

typedef struct
{
	int Cmd;
	char* SendBuf;
	int SendDataLen;
	int SendTimeOut;  //ms
	char* RecvBuf;
	int RecvBufLen;
	int RecvDataLen;
	int RecvTimeOut;  //ms
}SubCmdType;
typedef struct
{
	int Mode;
	int ModId;  //-1为全部，0~3对应模块ID
}LoModeParam;


#endif