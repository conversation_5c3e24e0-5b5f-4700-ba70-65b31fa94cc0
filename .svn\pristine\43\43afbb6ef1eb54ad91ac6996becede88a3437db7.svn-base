#pragma once
#ifndef _TESTER_COMMON_H_
#define _TESTER_COMMON_H_

#include "wterrorAll.h"
#include "alzdef.h"
#include <time.h>

#ifdef STD_CALL
#define CALL_MODE __stdcall 		///<stdcall方式
#else
#define CALL_MODE 					///<cdecl方式
#endif


#define MAX_VSA_UNIT_COUNT 4		///<最大VSA单元数

#define MAX_VSG_UNIT_COUNT 4		///<最大VSG单元数

#define MAX_NAME_SIZE      256		///<带有文件名的命令中文件名的最大长度

#define MAX_SUB_NET_COUNT 8			///<子网口最大数

#define DF_MAX_ATT 8				///<最大天线数

/// @brief 仪器类型枚举
//License Technology类型
enum WT_TECHNOLOGY_E_API
{
    LIC_GPRF_API,
    LIC_WIFI_SISO_API,
    LIC_WIFI_MIMO_API,
    LIC_BLUETOOTH_API,
    LIC_INTER_API,   //交互模式，自动添加VSA-VSG以及VSG-VSA; 取消原TB、TF模式的Technology
    LIC_DEVM_API,
    LIC_CELLULAR_API,
    LIC_OTHER_API,
    LIC_MODE_API,    //软件模式license(协议类型)
    LIC_MAX_TECH_API //无效，仅作结尾标识用
};

//License类型
enum WT_LIC_TYPE_E_API
{
    WT_HW_TYPE_API,             //硬件资源类型
    WT_FREQ_BAND_TYPE_API,      //频段类型
    WT_PROT_TYPE_API,           //业务协议类型
};

// license许可的硬件资源类型定义
enum WT_LIC_RESOURCE_E_API
{
    WT_RF_PORT_NUM_API,          // RF端口数量类型(普通端口)
    WT_ETH_PORT_NUM_API,         // 网口数类型
    WT_LINK_NUM_API,             // 连接数类型
    WT_VSG_UNIT_NUM_API,         // VSG硬件单元数量类型
    WT_VSA_UNIT_NUM_API,         // VSA硬件单元数量类型
    WT_SPEC_RF_PORT9_API,        // RF高功率port9，目前仅328有
    WT_USE_MODULES_PARALLET_API, //模块允许并行使用的license,318使用
    WT_DIGITAL_ETH_NUM_API,      //数字IQ网口数量
    WT_VSG_BROADCAST_API,        //VSG广播模式
    WT_MAX_RES_API               // 无效，仅作结尾标识用
};

//频段类型定义
enum WT_FREQ_BAND_E_API
{
    WT_BAND_1G_API,         // 400M-1G
    WT_BAND_2G_API,         // 1G-2.4G
    WT_BAND_2_4G_API,       // 2.4G-2.5G
    WT_BAND_3G_API,         // 2.5G-3.8G
    WT_BAND_4G_API,         // 3.8G-4.9G
    WT_BAND_5G_API,         // 4.9G-6G
    WT_BAND_6G_API,         // 6G-8G
    WT_MAX_BAND_API         //无效，仅作结尾标识用
};

// 业务协议定义
enum WT_PROT_E_API
{
    // GPRF
    WT_GPRF_API,

    // WIFI SISO
    WT_WIFI_SISO_API,
    WT_11A_API,
    WT_11B_API,
    WT_11G_API,
    WT_11N_API,
    WT_11P_API,
    WT_11AC_API,
    WT_11AX_API,
    WT_11BE_API,
    WT_11AH_API,
    WT_11AD_API,
    WT_160M_API,
    WT_320M_API,      //连续320M
    WT_MULIT_SEG_API, // 80+80、160+160、320+320等多单元频点组合测试
    WT_CMIMO_API,
    WT_MAC_AC_API, // 11a/b/g/n/ac等基础业务的MAC生成、解析控制开关；
               // MAC的基础开关（无该Licence时，UI不显示相关操作，仪器也不响应相关命令）,11a/b/g/n可以不进行细分
    WT_MAC_AX_API, // 11ax MAC生成解析；依赖于WT_MAC
               // Generator：在11ax业务下，根据是否存在该Licence决定是否处理进一步生成事宜
               // VSA：在非Auto Detect模式下，如果不存在该Licence，不处理11ax、11be的MAC分析及显示操作，在Auto Dect下，则不进行显示
    WT_MAC_BE_API, // 11be MAC生成解析；依赖于WT_MAC_ax
               //类似WT_MAC_ax

    // WIFI MIMO
    WT_WIFI_MIMO_API,     //直接到8x8 MIMO
    WT_MUMIMO_API,        //直接到8x8 MU-MIMO
    WT_WIFI_MAS_MIMO_API, // 8x8以上MIMO，(不包含8流)依赖于WT_WIFI_MIMO,eg(12*12),MAS(massive)
    WT_MAS_MUMIMO_API,    // 8x8以上MU-MIMO，(不包含8流)依赖于WT_MUMIMO，WT_WIFI_MAS_MIMO,eg(12*12),MAS(massive)
    WT_SWITCHED_MIMO_API,

    // ZigBee
    WT_ZIGBEE_API, //建议删除

    // Bluetooth
    WT_BT_API,

    // Navigation                   //建议删除定位相关Lic
    WT_GPS_API,
    WT_BEIDOU_API,
    WT_GALILEO_API,
    WT_GLONASS_API,

    // Cellular
    WT_CELL_2G_API,
    WT_CELL_3G_API,
    WT_CELL_LTE_API,
    WT_CELL_5G_API,

    // Z-mave
    WT_ZWAVE_API, //建议删除

    // DEVM
    WT_DEVM_API,

    // Interactive
    WT_MAC_INTER_AC_API,   // 11a/b/g/n/ac等基础业务的交互测试控制开关，依赖于WT_MAC_ac
    WT_MAC_INTER_AX_API,   // 11ax的交互测试控制开关，依赖于WT_MAC_ax、WT_MAC_Inter_ac
    WT_MAC_INTER_BE_API,   // 11be的交互测试控制开关，依赖于WT_MAC_be、WT_MAC_Inter_ax
    WT_MAC_ENCRYPTION_API, //加解密处理，依赖于WT_MAC_ac

    // Other 与其他业务组合，不单独成某一Technology
    WT_IBF_API,      // Implicit Beamforming，隐式Beamforming
    WT_SEQUENCE_API, // List mode， Sequence控制开关
    WT_DIQ_AC_API,   //数字IQ基础Licence，使能数字IQ的通讯能力，可以与MIMO、MAC组合拓宽测试范围;通过网口传输基带数据，不支持监视与子仪器
                 // VSA解析时，使能11a/b/g/n/ac等基础业务的帧/MAC解析
    WT_DIQ_AX_API,   // 11ax数字IQ解析开关，依赖于WT_DIQ_ac
    WT_DIQ_BE_API,   // 11be数字IQ解析开关，依赖于WT_DIQ_ax

    //模式license
    WT_SIGNALING_API,
    WT_BEAMFORMING_API, // Explicit （显示）Beamforming，依赖于mimo
    WT_PER_API,

    WT_INTER_API,          //使能RF模式下的交互测试
    WT_SUB_INSTRUMENT_API, //子仪器功能
    WT_AIQ_API,            //模拟IQ
    WT_BT5_1_API,          // BT5.1,BT5.2
    WT_SLE_API,            // GLE
    WT_11BA_API,             // 802.11BA
    WT_11AZ_API,             // 802.11AZ
    WT_PAC_API,

    WT_HWO_API,             //BaseBand
    WT_WISUN_API,
    WT_LORA_API,
    WT_EVMC_API,
    WT_CHANNEL_MODE_API,
    WT_CHANNEL_MATRIX_API,
    WT_DIQ_MULTI_CEL_APIL,
    WT_BT_HDR_API,
    WT_WAVE_DECRYPT_API,
    WT_MAX_PROT_API, //无效，仅作结尾标识用
};

/// @brief 仪器类型枚举
enum WT_TESTER_TYPE
{
    TEST_TYPE_ENUM_WT160,
    TEST_TYPE_ENUM_WT200,
    TEST_TYPE_ENUM_WT208 = 3,
    TEST_TYPE_ENUM_WT208C,
    TEST_TYPE_ENUM_WT300 = 14,
    TEST_TYPE_ENUM_WT328 = 15,
    TEST_TYPE_ENUM_WT318 = 16,
    TEST_TYPE_ENUM_WT328E = 17,
    TEST_TYPE_ENUM_WT328C = 18,
    TEST_TYPE_ENUM_WT448 = 30,
    TEST_TYPE_ENUM_WT428 = 31,
    TEST_TYPE_ENUM_WT418 = 32,
    TEST_TYPE_ENUM_WT328CE = 32,
    TEST_TYPE_ENUM_WT422 = 33,
    TEST_TYPE_ENUM_WT428C = 34,
    TEST_TYPE_ENUM_WT428H = 35,

    TEST_TYPE_ENUM_WTDEMO = 0xFFFF
};


/// @brief 子仪器索引
enum WT_SUB_TESTER_INDEX
{
    WT_SUB_TESTER_INDEX_AUTO = -1, ///< INDEX自动分配
    WT_SUB_TESTER_INDEX0 = 1,      ///< INDEX0
    WT_SUB_TESTER_INDEX1 = 2,      ///< INDEX1
    WT_SUB_TESTER_INDEX2 = 3,      ///< INDEX2
    WT_SUB_TESTER_INDEX3 = 4,      ///< INDEX3
    WT_SUB_TESTER_INDEX4 = 5,      ///< INDEX4
    WT_SUB_TESTER_INDEX5 = 6,      ///< INDEX5
    WT_SUB_TESTER_INDEX6 = 7,      ///< INDEX6
    WT_SUB_TESTER_INDEX7 = 8,      ///< INDEX7
#ifdef SISO_VER
    WT_SUB_TESTER_INDEX_MAX = 1,
#else
    WT_SUB_TESTER_INDEX_MAX = 8,
#endif
};

/// @brief 连接方式枚举
enum WT_CONNECT_TYPE
{
    WT_CONNECT_TYPE_NORMAL,                             ///<普通连接
    WT_CONNECT_TYPE_FORCE,                              ///<强制连接
    WT_CONNECT_TYPE_MONITOR,                            ///<监视连接
    WT_CONNECT_TYPE_MultiUser,                          ///<子连接
    WT_CONNECT_TYPE_MANAGE,                             ///<管理连接
    WT_CONNECT_TYPE_DEMO                                ///<DEMO连接
};

/// @brief VSG状态
enum WT_VSG_STATE
{
    WT_VSG_VSA_STATE_DONE,                              ///<VSA/VSG已完成
    WT_VSG_VSA_STATE_RUNNING,                           ///<VSA/VSG正在运行
    WT_VSG_VSA_STATE_TIMEOUT,                           ///<VSA/VSG超时
    WT_VSG_VSA_STATE_ERR_DONE,                          ///<VSA/VSG出错
    WT_VSG_VSA_STATE_WAITING
};

/// @brief 连接状态
enum WT_CONNECT_STATE
{
    WT_CONNECT_DEVICE_NOT_EXSIT = -1,                   ///<设备不存在
    WT_CONNECT_STATE_IDLE = 0,                          ///<设备空闲，未被连接
    WT_CONNECT_STATE_BUSY = 1,                          ///<设备已被连接
};
/// @brief 通信方式
enum WT_COMUNICATE_TYPE
{
    WT_COMU_TYPE_TCP,                                   ///<TCP通信方式
    WT_COMU_TYPE_UDP,                                   ///<UDP通信方式
    WT_COMU_TYPE_SCPI,                                  ///<SCPI通信
    WT_COMU_TYPE_VISA                                   ///<VISA通信
};
/// @brief 保存VSA信号文件
enum WT_SAVE_SIG_FILE_OPTION
{
    WT_SAVE_RAW_DATA,                                   ///<保存原始数据
    WT_SAVE_COMPENSATED_DATA                            ///<保存补偿后的数据,
};
/// @brief VSA结果平均
enum WT_VSA_AVERAGE_TYPE
{
    WT_CAPTURE_ARITHMETIC_AVERAGE,                      ///<多次抓取取算术平均
    WT_CAPTURE_MOVING_AVERAGE,                          ///<多次抓取取滑动平均
    WT_SEGMENT_ARITHMETIC_AVERAGE,                      ///<多帧取算术平均
    WT_SEGMENT_MOVING_AVERAGE                           ///<多帧取滑动平均
};
/// @brief 信号文件操作
enum FILE_OPTION
{
    GET_WAVE_FILE,                                      ///<获取波形文件
    DELETE_WAVE_FILE,                                   ///<删除波形文件
    ADD_WAVE_FILE,                                      ///<添加波形文件
    GET_REF_FILE,                                       ///<获取参考文件
    DELETE_REF_FILE,                                    ///<删除参考文件
    ADD_REF_FILE                                        ///<添加参考文件
};

/// @brief TB分析参数类型枚举
enum AX_TB_ANALYZE_ENUM
{
    AX_TB_ANALYZE_EXIT = -1,    ///< 退出TB分析模式    
    AX_TB_BASE_TYPE = 0,        ///< 配置AlzParamAxTriggerBase分析结构体
    AX_TB_REF_FILE = 2,         ///< 加载.tb文件
};

/// @brief trigger type
enum WT_TRIG_TYPE_ENUM_API
{
    /// @brief  Free running ADC sampling
    WT_TRIG_TYPE_FREE_RUN_API,
    /// @brief  ADC External Trigger selected
    WT_TRIG_TYPE_EXT_API,
    /// @brief  ADC IF Trigger selected - trigger calibration will be performed
    WT_TRIG_TYPE_IF_API,
    WT_TRIG_TYPE_IF_NO_CAL_API
};

/// @brief trigger type
enum WT_TRIG_EDGE_ENUM_API
{
    /// @brief  Rising edge signal detection initiates a trigger event.
    WT_TRIG_DEGE_POSITIVE_API,
    /// @brief  Falling edge signal detection initiates a trigger event
    WT_TRIG_DEGE_NEGATIVE_API,
};

enum WT_RANGE_GET_OPTION
{
    WT_STANDARD_RANGE,
    WT_ACTUAL_RANGE
};

/// @brief 测试类型定义
enum TEST_TYPE
{
    TEST_SISO_API,               ///< SISO
    TEST_MULTI_MIMO_API = 3,     ///< 多机MIMO
    TEST_SWITCHED_MIMO_API = 5,  ///< Switched MIMO
};

#define IP_MAX_LEN 16 									///<IP地址长度
#define MAC_ADDR_MAX_LEN 18 							///<MAC地址长度
#define WT_SN_MAX_LEN 80								///<SN长度
#define WT_COM_MAX_LEN 40   							///<通用信息长度
#define WT_VER_DATA_LEN 20  							///<日期信息长度
#define WT_MODULES_TYPE_LEN 8 							///<模块信息长度
#define WT_LICENSE_NAME_LEN 32 							///<license信息长度
#define WT_MODULES_MAX_NUM 8   							///<最大模块数量

///////////////////////////////////////////////////////////////
/// @brief VSA配置 \n
/// MIMO 模式下VSA配置：所有WT_SUB_TESTER_INDEX_MAX大小的成员数组都表示主从机配置 \n
/// 例如 \n
/// MaxPower[0]表示主机最大输入功率，MaxPower[1] 表示第一台从机最大输入功率、MaxPower[2]表示第二台从机最大输入功率；依次类推 \n
/// RfPort[0]表示主机VSA的RF口，RfPort[1]表示第一台从机VSA的RF口、RfPort[2]表示第二台从机VSA的RF口；依次类推 \n
///	ExtPathLoss[0]表示主机外部线衰，ExtPathLoss[1]表示第一台从机外部线衰、ExtPathLoss[2]表示第二台从机外部线衰；依次类推 \n
//////////////////////////////////////////////////////////////
typedef struct
{
    /// @brief VSA中心频率1，单位：Hz
    double  Freq;
    /// @brief VSA中心频率2，单位：Hz。仅80+80时使用
    double  Freq2;
    /// @brief 频率偏移，单位Hz。最小值10KHz，最小步进值1KHz
    double  FreqOffset;
    /// @brief 业务单元掩码。默认值等于0
    int     VsaUnitMask[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 输入的最大功率,单位dBm; (最好在DUT的目标功率基础上再加12~15dB，如果接收功率范围未知，建议事先Auto Range) \n
    /// MIMO模式下：MaxPower[0]表示主机最大输入功率，MaxPower[1] 表示第一台从机最大输入功率、MaxPower[2]表示第二台从机最大输入功率；依次类推
    double  MaxPower[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 指定VSA使用的RF端口 \n
    /// MIMO模式下：RfPort[0]表示主机VSA的RF口，RfPort[1]表示第一台从机VSA的RF口、RfPort[2]表示第二台从机VSA的RF口；依次类推
    int     RfPort[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 外部衰减1。对应VSA中心频率1的衰减值。单位：dB \n
    /// MIMO模式下：ExtPathLoss[0]表示主机外部线衰，ExtPathLoss[1]表示第一台从机外部线衰、ExtPathLoss[2]表示第二台从机外部线衰；依次类推
    double  ExtPathLoss[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 外部衰减2。对应VSA中心频率2的衰减值。仅80+80时候用；单位：dB
    double ExtPathLoss2[WT_SUB_TESTER_INDEX_MAX];
    /// @brief VsaUnitMask等的有效值，默认值等于1
    int     ValidNum;
    /// @brief 采样时间。单位：second(秒)
    double  SmpTime;
    /// @brief VSA采样带宽，单位Hz。默认240MHz
    double  SamplingFreq;
    /// @brief 信号demode
    int     Demode;
    /// @brief 触发模式,WT_TRIG_TYPE_ENUM
    int     TrigType;
    /// @brief 触发电平,与max_power的差距,单位：dB
    double  TrigLevel;
    /// @brief 触发等待超时，单位：second(秒)（该时间与TimeoutWaiting的时间相互独立）
    double  TrigTimeout;
    /// @brief 触发保留时间，单位：second(秒)。保留触发前若干时间内的数据，取值范围[10us-300us]
    double  TrigPretime;
    /// @brief 最大帧间隔，单位：second(秒)。取值范围[10ms-2000ms]
    double  MaxIFGGap;
    /// @brief 在多连接情况下，等待的最大时间，单位：second(秒)。默认值为4 second(秒)
    double  TimeoutWaiting;

    bool VsaAutoSamplingTimeMode = true;
    bool VsaAutoSamplingRateMode = true;
} VsaParameter;

/// @brief VSA平均配置
typedef struct
{
    int     AvgCount;                                           ///<平均次数
    int     AvgType;                                            ///<平均类型
} VsaAvgParameter;

/// @brief Vsg 平均配置参数
typedef struct
{
    double  Freq;                                               ///<VSG中心频率1，单位：Hz
    double  Freq2;                                              ///<VSG中心频率2，单位：Hz。仅80+80时使用
    double  FreqOffset;                                         ///<频率偏移，单位Hz。最小值10KHz，最小步进值1KHz
    double  SamplingFreq;                                       ///<VSG采样带宽，单位Hz。默认240MHz
    int     VsgUnitMask[WT_SUB_TESTER_INDEX_MAX];               ///<业务单元掩码，默认值等于0
    double  Power[WT_SUB_TESTER_INDEX_MAX];                     ///<VSG功率，单位：dBm
    int     RfPort[WT_SUB_TESTER_INDEX_MAX];                    ///<指定VSG使用的RF端口

    double  TimeoutWaiting;                                     ///<在多连接情况下，等待的最大时间，单位：second(秒)。默认值为8 second(秒)
} VsgAvgParameter;

///////////////////////////////////////////////////////////////
/// @brief VSG配置 \n
/// MIMO 模式下VSG配置：所有WT_SUB_TESTER_INDEX_MAX大小的成员数组都表示主从机配置 \n
/// 例如 \n
/// Power[0]表示主机最大输入功率，Power[1] 表示第一台从机最大输入功率、Power[2]表示第二台从机最大输入功率；依次类推 \n
/// RfPort[0]表示主机VSG的RF口，RfPort[1]表示第一台从机VSG的RF口、RfPort[2]表示第二台从机VSG的RF口；依次类推 \n
///	ExtPathLoss[0]表示主机外部线衰，ExtPathLoss[1]表示第一台从机外部线衰、ExtPathLoss[2]表示第二台从机外部线衰；依次类推 \n
//////////////////////////////////////////////////////////////
typedef struct
{
    /// @brief VSG中心频率1，单位：Hz
    double  Freq;
    /// @brief VSG中心频率2，单位：Hz。仅80+80时使用
    double  Freq2;
    /// @brief 频率偏移，单位Hz。最小值10KHz，最小步进值1KHz
    double  FreqOffset;
    /// @brief VSG采样带宽，单位Hz。默认240MHz
    double  SamplingFreq;
    /// @brief 业务单元掩码，默认值等于0
    int     VsgUnitMask[WT_SUB_TESTER_INDEX_MAX];
    /// @brief VSG功率，单位：dBm   \n
    /// MIMO模式下：Power[0]表示主机最大输入功率，Power[1] 表示第一台从机最大输入功率、Power[2]表示第二台从机最大输入功率；依次类推
    double  Power[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 指定VSG使用的RF端口    \n
    ///MIMO模式下：RfPort[0]表示主机VSG的RF口，RfPort[1]表示第一台从机VSG的RF口、RfPort[2]表示第二台从机VSG的RF口；依次类推
    int     RfPort[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 外部衰减1，对应VSG中心频率1的衰减值。单位：dB   \n
    ///MIMO模式下：ExtPathLoss[0]表示主机外部线衰，ExtPathLoss[1]表示第一台从机外部线衰、ExtPathLoss[2]表示第二台从机外部线衰；依次类推
    double  ExtPathLoss[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 外部衰减2，对应VSG中心频率2的衰减值。单位：dB。仅80+80时候用
    double  ExtPathLoss2[WT_SUB_TESTER_INDEX_MAX];
    /// @brief VsgUnitMask等的有效值。默认值等于1
    int ValidNum;
    /// @brief 在多连接情况下，等待的最大时间，单位：second(秒)。默认值为8 second(秒)
    double TimeoutWaiting;
    /// @brief <两次发送间隔,单位：second(秒)
    double Wave_gap;
    /// @brief <随机间隔的范围上限，单位：second(秒)
    double RandomWaveGapMax;
    /// @brief <随机间隔的范围下限，单位：second(秒)
    double RandomWaveGapMin;
    /// @brief <循环发送次数，0表示无限次发送
    int Repeat;
    /// @brief <IFG随机模式
    int IFGRamdomMode;
    /// @brief 保留位
    int Reserved[2];
} VsgParameter;

/// @brief 波形文件配置(兼容多PN)
typedef struct
{
    char WaveName[MAX_NAME_SIZE];                               ///<本地波形文件路径
    char SaveWaveName[MAX_NAME_SIZE];							///<信号文件保存到仪器后的信号文件名称。注意和WaveName区分
    int Wave2Flag;                                              ///<默认值等于0。仅80+80时设置为1
} VsgWaveParameter;

/// @brief VSG行为定义
typedef struct
{
    unsigned int Repeat;                                    ///<循环发送次数，0表示无限次发送//仅限数字IQ、交互以及listmod
    double       Wave_gap;                                      ///<两次发送间隔,单位：second(秒)//仅限数字IQ和交互
    double       StartDelay;                                    ///<发送信号的启动延时,单位：second(秒)//仅限数字IQ和交互
    // double       FreqOffset;                                 ///<频率偏移，单位：Hz
    // double       PowerOffset;                                ///<相对功率原点的功率偏差，单位：dB
    double       RandomWaveGapMax;                             ///<随机间隔的范围上限，单位：second(秒)//仅限数字IQ和交互
    double       RandomWaveGapMin;                             ///<随机间隔的范围下限，单位：second(秒)//仅限数字IQ和交互
    int          WaveType;                                     ///<Wave类型，SIG_USERFILE: 读取本地wave文件; SIG_TESTERFILE: 使用预先下载到仪器的wave文件;
    char         WaveName[MAX_NAME_SIZE];                      ///<如果信号文件从本地获取，需指定下载到仪器后的信号文件名称。
    int          Extend;                                   ///listmod arb信号发送扩展的点数，表示0.xxx个arb信号,listmod模式下，持续发送时，该参数不生效
} VsgPattern;


typedef struct
{
    unsigned int StartIdx;                                      ///<第一个发送的PN编号
    unsigned int SendCnt;                                       ///<发送多少个PN
} PnItemHead_API;

/// @brief 连接单元
typedef struct
{
    /// @brief 仪器IP地址
    char Ip[IP_MAX_LEN];
    /// @brief 子仪器编号。默认值等于WT_SUB_TESTER_INDEX_AUTO 最大值:WT_SUB_TESTER_INDEX_MAX  \n
    /// MIMO 模式下，主机配置为WT_SUB_TESTER_INDEX_AUTO；从机配置为WT_SUB_TESTER_INDEX0
    int SubTesterIndex;
} ConnectedUnit;

/// @brief 子仪器资源分配配置
typedef struct
{
    /************************************************************
    The ASCII decimal number specifying the active RF ports
    each binary bit represents a single physical RF port
    The least significant bit represents port 1 and so on for a maximum of 8 ports
    A bit set to 1 indicates that the port is active
    A bit set to 0 indicates that the port is inactive
    By default, port 1 is ON and all other ports are OFF
    *************************************************************/
    int RfPort;                                                 ///<RF口掩码(与网口一一对应，RF1对应子网口1，RF2对应子网口2，依次类推)
    /************************************************************
    The ASCII decimal number specifying the active VSA units
    each binary bit represents a single physical VSA units
    The least significant bit represents VSA 1 and so on for a maximum of 4 units
    A bit set to 1 indicates that the VSA unit is active
    A bit set to 0 indicates that the VSA unit is inactive
    By default, VSA unit 1 is ON and all other VSA unit are OFF
    *************************************************************/
    int VsaUnit;                                                ///<VSA单元掩码
    /************************************************************
    The ASCII decimal number specifying the active VSG units
    each binary bit represents a single physical VSG units
    The least significant bit represents VSG 1 and so on for a maximum of 4 units
    A bit set to 1 indicates that the VSG unit is active
    A bit set to 0 indicates that the VSG unit is inactive
    By default, VSG unit 1 is ON and all other VSG unit are OFF
    *************************************************************/
    int VsgUnit;                                                ///<VSG单元掩码
} SubTesterCfg;

/// @brief 虚拟网口配置
typedef struct
{
    //*****************************************************************************
    //                              基础配置，该部分必须配置
    //*****************************************************************************

    char VirAddr[MAX_SUB_NET_COUNT][IP_MAX_LEN];                ///< 虚拟IP,最多8个子网口，依次对应1~8子网口。不需要配置的子网口赋值为NULL
    char DutAddr[IP_MAX_LEN];                                   ///< DUT IP,必须与仪器IP不在同一网段

    //*****************************************************************************
    //                           DUT作为TFTP Server时
    //
    //        无TFTP需求时，以下IP都设置为空
    //
    //        需配置DUT在TFTP状态下指定的Server IP
    //*****************************************************************************
    char DutTftpServerAddr[IP_MAX_LEN];                         ///< DUT IP

    //*****************************************************************************
    //                         PC 作为FFTP Server时
    //
    //        无TFTP需求时，以下IP都设置为空
    //
    //        当PC作为TFTP Server时，需配置实际作为Server的PC IP
    //        还需配置DUT在TFTP状态下所使用的Client IP以及DUT所指定的Server IP
    //*****************************************************************************
    char TftpServerAddr[IP_MAX_LEN];                            ///< TFTP Server IP
    char TftpClientAddr[IP_MAX_LEN];                            ///< DUT IP
    char TftpServerPCAddr[IP_MAX_LEN];                          ///< PC IP,需确保该IP与DUT的虚拟IP在同一网络
    char TftpServerPCAddr2[IP_MAX_LEN];                         ///< PC IP,需确保该IP与DUT的虚拟IP在同一网络

} VirtualNetType;

typedef struct
{
    int DataLen;                                            ///<数据长度，单位：byte
    int DataTypeSize;                                       ///<数据类型长度，单位：byte
    char *Data;                                             ///<数据内容
} VsaResult;

/// @brief 仪器概况
typedef struct
{
    /************************************************************
    Tester IP address
    ************************************************************/
    char Ip[IP_MAX_LEN];                                ///< IP地址
    /************************************************************
    Tester type:WT-300 or WT-328 or other types
    ************************************************************/
    char TesterType[WT_COM_MAX_LEN];                    ///<仪器类型
    /************************************************************
    Tester SN
    ************************************************************/
    char SN[WT_SN_MAX_LEN];                             ///< SN码
    /************************************************************
    Tester alias
    ************************************************************/
    char Name[WT_COM_MAX_LEN];                          ///< 别名"iTest Tester"
    /************************************************************
    Tester firmware version
    ************************************************************/
    char FwVersion[WT_COM_MAX_LEN];                     ///<固件版本
} TesterOverview;
#ifndef LINUX_SCPI
/// @brief log查询枚举
enum LOG_QUERY_TYPE
{
    LOG_QUERY_SERVER_ID,
    LOG_QUERY_HW,
    LOG_QUERY_TIME,
    LOG_QUERY_OP,
    LOG_QUERY_All,
    LOG_QUERY_SQL,
};
#endif
/// @brief VSA扩展参数
typedef struct
{
    /// @brief 80+80是否采用双端口模式。1 = enable, 0 = disable
    int WIFI8080DulPortMode;
    /// @brief 8080 多端口模式 \n
    ///< SISO模式下: 配置数据VsaRfPort[0],VsaRfPort[1] \n
    ///< MIMO模式下：VsaRfPort[0],VsaRfPort[1]一组; VsaRfPort[2],VsaRfPort[3]一组，依次类推 \n
    ///< 注意模块冲突问题。RF1~RF4为一个模块，RF5~RF8一个模块 \n
    ///< 例如RF1 配置为8080的第一个VSA port，则第二个VSA口必须是RF5~RF8其中中的一个。否则配置VSA,VSG参数时提示失败
    int VsaRfPort[WT_SUB_TESTER_INDEX_MAX * 2];
    /// @brief 同上述VsaRfPort。
    ///< SISO模式下配置RfPort_MaxPower[0],RfPort_MaxPower[1] \n
    ///< MIMO模式每两个为一组。依次类推。如下说明 \n
    ///<      master:RfPort_MaxPower[0],RfPort_MaxPower[1] \n
    ///<      slave_1:RfPort_MaxPower[2],RfPort_MaxPower[3] \n
    ///<      slave_2:RfPort_MaxPower[4],RfPort_MaxPower[5]
    double	RfPort_MaxPower[WT_SUB_TESTER_INDEX_MAX * 2];
    /// @brief 同上述VsaRfPort \n
    ///< SISO模式下配置RfPort_TrigLevelr[0],RfPort_TrigLevel[1] \n
    ///< MIMO模式每两个为一组。依次类推。如下说明 \n
    ///<      master:RfPort_TrigLevel[0],RfPort_TrigLevel[1] \n
    ///<      slave_1:RfPort_TrigLevel[2],RfPort_TrigLevel[3] \n
    ///<      slave_2:RfPort_TrigLevel[4],RfPort_TrigLevel[5]
    double	RfPort_TrigLevel[WT_SUB_TESTER_INDEX_MAX * 2];
    /// @brief 在模拟IQ模式下，I路 DC offset: -1.0 to ****
    double  DCOffsetI;
    /// @brief 在模拟IQ模式下，Q路 DC offset: -1.0 to ****
    double  DCOffsetQ;
    int     Reserved[60];
}ExtendVsaParameter;

typedef struct
{
    /// @brief 80+80是否采用双端口模式。1 = enable, 0 = disable
    int		WIFI8080DulPortMode;
    /// @brief 8080 多端口模式 \n
    ///< SISO模式下: 配置数据VsgRfPort[0],VsgRfPort[1] \n
    ///< MIMO模式下：VsgRfPort[0],VsgRfPort[1]一组; VsgRfPort[2],VsgRfPort[3]一组，依次类推 \n
    ///< 注意模块冲突问题。RF1~RF4为一个模块，RF5~RF8一个模块 \n
    ///< 例如RF1 配置为8080的第一个VSG port，则第二个VSG口必须是RF5~RF8其中中的一个。否则配置VSA,VSG参数时提示失败
    int     VsgRfPort[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 8080 多端口模式，每个RF口的功率 \n
    ///< 同上述VsgRfPort \n
    ///< SISO模式下配置VsgPower[0],VsgPower[1] \n
    ///< MIMO模式每两个为一组。依次类推。如下说明 \n
    ///<      master:VsgPower[0],VsgPower[1] \n
    ///<      slave_1:VsgPower[2],VsgPower[3] \n
    ///<      slave_2:VsgPower[4],VsgPower[5]
    double	VsgPower[WT_SUB_TESTER_INDEX_MAX];
    /// @brief 在模拟IQ模式下，I路 DC offset: -1.0 to ****
    double  DCOffsetI;
    /// @brief 在模拟IQ模式下，Q路 DC offset: -1.0 to ****
    double  DCOffsetQ;
    /// @brief 在模拟IQ模式下，共模电压 0.0V-3.3V
    double CommModeVolt;
    int     Reserved[58];
}ExtendVsgParameter;

#define  AX_USER_COUNT 74

typedef struct
{
    TesterOverview overView;
    char BootTime[WT_COM_MAX_LEN];						//开机时间
    char BootElapsedTime[WT_COM_MAX_LEN];				//开机时长
    char Reserved[MAX_NAME_SIZE];						//保留位
}ExternedTesterOverview;

typedef struct
{
    double  TriggerTimeout;                 ///< 发送数据时，未接收到 TRIG信号，超时退出的时间，单位秒
    double  RecvTimeout;                    ///< 接收数据时，未接收到 数据包，超时退出的时间，单位秒
    char    DstMac[6];                      ///< 发送数据包的目标MAC地址
    char    Reserv[2];                      ///< 保留，对齐
    double  VSGISTimeout;                   ///< 包间超时，发送首包数据后，未接收到后续数据，超时退出（认为发送完成）的时间，单位秒
    double  VSAISTimeout;                   ///< 包间超时，接收首包数据后，未接收到后续数据，超时退出（认为接收完成）的时间，单位秒
    int     VsaActionMask;                  ///< VSA动作掩码
    int     VsgActionMask;                  ///< VSG动作掩码
    int     VsaMaxBitCnt;                   ///< VSA支持的最大bit位数
    int     VsgMaxBitCnt;                   ///< VSG支持的最大bit位数
    char    VSAChannelIdList[8];            ///< 数字IQ交互频道列表
    char    VSGChannelIdList[8];            ///< 数字IQ交互频道列表
    int     VSGPnRatioMode;                 ///< VSG信号放大比率
    int     Rese;                           ///< 保留
    double  Reserved[15];                   ///< 保留
}DigtalIQParam;

/// TB-TF测试时，仪器作为STA发送TB帧
typedef struct
{
    double  Freq;                                                   ///< VSA中心频率1，单位：Hz
    double  Freq2;                                                  ///< VSA中心频率2，单位：Hz。仅80+80时使用
    double  VsaMaxPower[WT_SUB_TESTER_INDEX_MAX];                   ///< 输入的最大功率,dBm; (最好在实际输出到仪器的功率基础上再加12dB，如果接收功率范围未知，建议事先Auto Range)
    double  TriggerTimeOut;                                         ///< Trigger 超时时间，单位S
    double  TrigLevel;                                              ///< 触发电平,与max_power的差距,单位：dBRange)
    int     VsaRfPort[WT_SUB_TESTER_INDEX_MAX];                     ///< 指定VSA使用的RF端口
    double  VsaExtPathLoss[WT_SUB_TESTER_INDEX_MAX];                ///< 外部衰减1。对应VSA中心频率1的衰减值。单位：dB
    double  VsaExtPathLoss2[WT_SUB_TESTER_INDEX_MAX];               ///< 外部衰减2。对应VSA中心频率2的衰减值。仅80+80时候用
    double  VsaSmpTime;                                             ///< 采样时间。单位：second(秒)
    double  VsaPreTrigger;                                          ///< 数字IQ时有效的pre-trigger时间，分析数据加gap。单位：second(秒)
    double  VsaSamplingFreq;
    int     VsaDemode;                                              ///< 信号demode
    int     NeedSetVSAParam;                                        ///< 需要重配VSA参数，保留，暂不使用

    double  VsgPower[WT_SUB_TESTER_INDEX_MAX];                      ///< VSG功率，单位：dBm
    int     VsgRfPort[WT_SUB_TESTER_INDEX_MAX];                     ///< 指定VSG使用的RF端口
    double  VsgExtPathLoss[WT_SUB_TESTER_INDEX_MAX];                ///< VSG外部衰减1，对应VSG中心频率1的衰减值。单位：dB
    double  VsgExtPathLoss2[WT_SUB_TESTER_INDEX_MAX];               ///< VSG外部衰减2，对应VSG中心频率2的衰减值。单位：dB。仅80+80时候用
    double  Delay;                                                  ///< TB STA 时延，单位S
    double  VsgSamplingFreq;
    int     NeedSetVSGParam;                                        ///< 需要重配VSG参数，保留，暂不使用

    VsgPattern* VsgPatt;
    int     VsgPattNum;
    int     NeedSetVSGPattern;                                      ///< 需要重配VSG信号文件

    DigtalIQParam* DigParam;
    int     NeedSetDigParam;                                        ///< 需要重配DIG参数
    int     Reserved[30];                                           ///< 保留位
} InterBindParameter;


enum WT_PAC_MODE_ENUM
{
    WT_PAC_SHORT_ENUM,
    WT_PAC_OPEN_ENUM,
    WT_PAC_LOAD_ENUM,
};
/// @brief PAC返回的线衰平均数据
typedef struct
{
    double Freq;		///< 频率(Hz)
    double S21_avg;		///< 线损(DB)
    double S21_liner;	///< 线性回归后的线损(DB)
    int Cnt;            ///< 平均次数
    int Reserved;       ///< 保留
}PacDataAvg;
typedef struct
{
    int vsaPort;		///< PAC VSA口
    double vsaSmpTime;	///< PAC VSA采样时间，单位秒(s)
    double vsaRefPower;	///< PAC VSA参考电平，单位dBm
    int vsgPort;		///< PAC VSG口
    double vsgPower;	///< PAC VSG功率，单位dBm
    char *vsgWaveName;	///< PAC VSG信号文件，推荐使用sin1MHz.bwv
}PacAttribute;
typedef struct
{
    int Mode;			///< 模式WT_PAC_MODE_ENUM
    int AvgCnt;			///< 平均次数
    double *FreqList;	///< 频点列表，单位Hz
    int FreqListCnt;	///< 频点列表个数
} PacParameter;
/// @brief PAC处理过程参数
typedef struct
{
    double Freq;
    int Valid;
    char Msg[256];
} PacProgressParameter;

typedef struct
{
    int Enable;             	///< 1=Enable, 0=disable
    double Section_start[2];	///< 起始时间，单位S
    double Section_end[2];  	///< 结束时间，单位S
    double Reserved[8];
}PacSectionParam;

typedef struct
{
    double power;
    double phase;
}PacResult;

typedef struct
{
    int FEMMode;                            ///< 0 = disable, 1 = enable
    int DutyRatio;                          ///< 占空比，仅连续采集时有效
    double LeadTime;                        ///< 前置时间，对应图中t1，单位S
    double DelayTime;                       ///< 延时时间，对应图中t2, 单位S
    double Reserved[3];
}FemParameter;

typedef struct
{
    int Enbale;                             ///< 0:测试仪模式， 1：治具模式
    int SendCnt;                            ///< 每次发送的TRIGGER数
    int SendGap;                            ///< 发送TRIGGER的间隔，单位ns
    int SendPeriod;                         ///< 发送TRIGGER的周期，单位ns
    double SendTimeout;                     ///< 接收端发送TRIGGER后，超时未收到数据，则重复发送TRIGGER
    double VsgFixtureDelay;                 ///< VSG自己模式发包延时
    double Reserved[19];                    ///< 保留
}DigtalIQTestFixture;


enum WTVSAGENTRIGGERTYPE
{
    VSA_GEN_NO_TRIGGER, //不发送同步事件
    VSA_GEN_MEA_START_TRIGGER, //测量开始时发送同步事件
    VSA_GEN_DUR_END_TRIGGER, //dur结束时发送同步事件
    VSA_GEN_MEASTART_DUREND_TRIGGER, //测量开始以及dur结束时发送同步事件
};


/// @brief VSG TRIG PARAM
typedef struct
{
    double GapTime;		    ///< gap time
    int Edge;		        ///< 触发沿
    double FrameTime;       ///< 最小有效信号长度
    int GenTriggerType;     ///< list mod vsa同步vsg的触发类型，0：不触发，1：在测量开始时同步vsg；2；在测量结束时同步，3：既在测量开始时同步，又在结束时同步
    double Reserved[10];    ///< 保留
}VsaTrigParam;

#include "TesterWave.h"

#endif
