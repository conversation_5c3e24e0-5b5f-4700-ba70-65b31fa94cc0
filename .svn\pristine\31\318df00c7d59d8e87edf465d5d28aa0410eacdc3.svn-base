#include "scpi_3gpp_alz_gsm.h"

#include <iostream>
#include <vector>
#include <tuple>

#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular;

static inline Alg_3GPP_AlzInGSM &Gsm(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->vsaAlzParam.analyzeParam3GPP.GSM;
}

scpi_result_t SetVsaGsmSlotOffset(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 7))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SlotOffset = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmNumbOfslot(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 8))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).NumbOfSlot = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmMeasureSlot(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 7))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).MeasureSlot = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmPvtFilter(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).PvTFilter = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmSpecModOffsetState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).SpectMod.OffsetState) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SpectMod.OffsetState[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmSpecModFreqOffset(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).SpectMod.FreqOffset) - 1))
        .Param(value, DOUBLE_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SpectMod.FreqOffset[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmSpecSwtOffsetState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).SpectSwt.OffsetState) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SpectSwt.OffsetState[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmSpecSwtFreqOffset(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).SpectSwt.FreqOffset) - 1))
        .Param(value, DOUBLE_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).SpectSwt.FreqOffset[param] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmRmsLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 50))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmRms.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmRmsCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmRms.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmRmsAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmRms.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmRmsMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmRms.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmPeakLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 50))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmPeak.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmPeakCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmPeak.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmPeakAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmPeak.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmPeakMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].EvmPeak.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmThreLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 50))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].Evm95Percent.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModEvmThreState(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].Evm95Percent.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrRmsLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrRms.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrRmsCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrRms.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrRmsAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrRms.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrRmsMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrRms.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrPeakLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrPeak.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrPeakCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrPeak.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrPeakAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrPeak.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrPeakMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErrPeak.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrThreLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErr95Percent.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModMerrThreState(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].MErr95Percent.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherRmsLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 180))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrRms.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherRmsCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrRms.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherRmsAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrRms.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherRmsMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrRms.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherPeakLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 180))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrPeak.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherPeakCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrPeak.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherPeakAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrPeak.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherPeakMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErrPeak.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherThreLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 180))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErr95Percent.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModPherThreState(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].PhErr95Percent.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqOffSetLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 0))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQOffset.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqOffSetCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQOffset.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqOffSetAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQOffset.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqOffSetMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQOffset.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqImbLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 0))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQImbalance.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqImbCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQImbalance.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqImbAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQImbalance.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModIqImbMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].IQImbalance.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModFreqErrLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 1000))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].FreError.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModFreqErrCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].FreError.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModFreqErrAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].FreError.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModFreqErrMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].FreError.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModTimeErrLimit(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, DOUBLE_RANGE(-1000, 1000))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].TimeError.LimitValue = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModTimeErrCurrent(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].TimeError.Current = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModTimeErrAverage(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].TimeError.Average = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitModTimeErrMax(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.ModLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.ModLimit[param].TimeError.Max = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtAvgState(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.AvgLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.AvgLimit[param].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtFpcl(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.AvgLimit) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.AvgLimit[param].FromPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtTpcl(scpi_t *context)
{
    int param = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.AvgLimit) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.AvgLimit[param].ToPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtLower(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.AvgLimit) - 1))
        .Param(value, DOUBLE_RANGE(-10, 0))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.AvgLimit[param].Lower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtUpper(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.AvgLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.AvgLimit[param].Upper = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtGState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.GuardPeriod.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPvtGLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.GuardPeriod.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseStartTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Start.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseStartRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Start.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Start.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Start.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseStopTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Stop.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseStopRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Stop.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseStopAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Stop.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseStopAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].StaticLimt.Stop.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseDynamicState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].DynamicLimt[param2].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseDynamicStartPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].DynamicLimt[param2].StartPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseDynamicEndPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].DynamicLimt[param2].EndPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPURiseDynamicCorrection(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].RiseEdgeLimit[param1].DynamicLimt[param2].Correction = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStartTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Start.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStartRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Start.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStartAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Start.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStartAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Start.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStopTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Stop.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStopRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Stop.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStopAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Stop.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseStopAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].StaticLimt.Stop.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseDynamicState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].DynamicLimt[param2].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseDynamicStartPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].DynamicLimt[param2].StartPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseDynamicEndPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].DynamicLimt[param2].EndPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUUseDynamicCorrection(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].UsefulPartLimit[param1].DynamicLimt[param2].Correction = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallStartTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Start.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallStartRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Start.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Start.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Start.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallStopTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Stop.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallStopRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Stop.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallStopAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Stop.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallStopAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].StaticLimt.Stop.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallDynamicState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].DynamicLimt[param2].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallDynamicStartPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].DynamicLimt[param2].StartPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallDynamicEndPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].DynamicLimt[param2].EndPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPUFallDynamicCorrection(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.UpperTemLimit[param].FallEdgeLimit[param1].DynamicLimt[param2].Correction = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStartTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Start.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStartRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Start.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStartAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Start.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStartAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Start.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStopTime(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-50, 600))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Stop.Time = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStopRelative(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Stop.LevelRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStopAbsState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Stop.LevelAbs.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseStopAbsLimit(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].StaticLimt.Stop.LevelAbs.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseDynamicState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].DynamicLimt[param2].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseDynamicStartPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].DynamicLimt[param2].StartPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseDynamicEndPcl(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, INT_RANGE(0, 31))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].DynamicLimt[param2].EndPCL = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitPLUseDynamicCorrection(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt) - 1))
        .Param(value, DOUBLE_RANGE(-100, 10))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.PVTLimit.LowerTemlimit[param].UsefulPartLimit[param1].DynamicLimt[param2].Correction = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecModRefLowPower(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 43))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecModLimit[param].RefPwrLimit.LowPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecModRefHighPower(scpi_t *context)
{
    int param = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit) - 1))
        .Param(value, DOUBLE_RANGE(0, 43))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecModLimit[param].RefPwrLimit.HighPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecModFreqOffsetState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit[0].FreOffsetLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecModLimit[param].FreOffsetLimit[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecModFreqOffsetLowPowerRel(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit[0].FreOffsetLimit) - 1))
        .Param(value, DOUBLE_RANGE(-120, 31.5))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecModLimit[param].FreOffsetLimit[param1].LowPwrRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecModFreqOffsetHighPowerRel(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit[0].FreOffsetLimit) - 1))
        .Param(value, DOUBLE_RANGE(-120, 31.5))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecModLimit[param].FreOffsetLimit[param1].HighPwrRel = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecModFreqOffsetPowerAbs(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecModLimit[0].FreOffsetLimit) - 1))
        .Param(value, DOUBLE_RANGE(-120, 31.5))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecModLimit[param].FreOffsetLimit[param1].AbsPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecSwitRefPower(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit[0].RefPower) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecSwiLimit[param].RefPower[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecSwitRefLevel(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit[0].RefPower) - 1))
        .Param(value, INT_RANGE(0, 39))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecSwiLimit[param].RefPower[param1].Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecSwitFreqOffsetState(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit[0].FreOffsetLimit) - 1))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecSwiLimit[param].FreOffsetLimit[param1].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetVsaGsmLimitSpecSwitFreqOffsetPowerRel(scpi_t *context)
{
    int param = 0;
    int param1 = 0;
    int param2 = 0;
    double value = 0;
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit) - 1))
        .CommandParam(param1, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit[0].FreOffsetLimit) - 1))
        .CommandParam(param2, INT_RANGE(0, arraySize(Gsm(context).LimitInfo.SpecSwiLimit[0].FreOffsetLimit[0].LimitValue) - 1))
        .Param(value, DOUBLE_RANGE(-60, 30))
        .Result();

    if (iRet == WT_OK) {
        Gsm(context).LimitInfo.SpecSwiLimit[param].FreOffsetLimit[param1].LimitValue[param2] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}
