#include "listmod_sequence.h"
#include "alg/alg_3gpp_apidef.h"
//#include "types.h"
#include "wtlog.h"
#include "scpi_3gpp_base.h"

void Sequence::ClearSeg()
{
    m_SeqType = SEQUENCETX;
    m_MaxSegSize = 0;
    SeqStat = SEQUENCEOFF;
    m_TrigerOffset = 0;
    //m_LowNam.clear();
    m_Repet = 1;

    m_Seg.clear();
}

int Sequence::GetSeqRealSegNum()
{
    int count = 0;
    for (auto &seg : m_Seg)
    {
        if (seg.get() == nullptr)
        {
            return 0;
        }

        if (seg.get()->TxFlag)
        {
            count += seg.get()->tx_seg.SegTimeParam.Repeat;
        }
        else
        {
            count += seg.get()->rx_seg.SegTimeParam.Repeat;
        }
    }

    return count;
}

void Sequence::SetSeqMaxSize(int Size)
{
    m_MaxSegSize = Size;
    m_Seg.resize(m_MaxSegSize);

    for(auto &Iter:m_Seg)
    {
        Iter.reset();
    }
}

std::vector<std::unique_ptr<Segment>>::iterator Sequence::GetSegBeginIter()
{
    return m_Seg.begin();
}

std::vector<std::unique_ptr<Segment>>::iterator Sequence::GetSegEndIter()
{
    return m_Seg.end();
}

void Sequence::ResetSeg(std::unique_ptr<Segment> &Seg, int SegNo)
{
    if (Seg->TxFlag == true)
    {
        //初始化采样参数
        Seg->tx_seg.vsaParam.Freq = 2412 * MHz_API;
        Seg->tx_seg.vsaParam.Freq2 = 0.0;
        Seg->tx_seg.vsaParam.FreqOffset = 0.0;
        Seg->tx_seg.vsaParam.VsaUnitMask[0] = 0;
        Seg->tx_seg.vsaParam.MaxPower[0] = 30.0;
        Seg->tx_seg.vsaParam.RfPort[0] = WT_PORT_RF1;
        Seg->tx_seg.vsaParam.ExtPathLoss[0] = 0.0;
        Seg->tx_seg.vsaParam.ExtPathLoss2[0] = 0.0;
        Seg->tx_seg.vsaParam.ValidNum = 1;
        Seg->tx_seg.vsaParam.Demode = WT_DEMOD_CW;
        if (SegNo == 0)
        {
            Seg->tx_seg.vsaParam.TrigType = WT_TRIG_TYPE_IF_API;
        }
        else
        {
            Seg->tx_seg.vsaParam.TrigType = WT_TRIG_TYPE_FREE_RUN_API;
        }
        Seg->tx_seg.vsaParam.TrigLevel = -31.0;
        Seg->tx_seg.vsaParam.TrigTimeout = 0.2;
        Seg->tx_seg.vsaParam.SamplingFreq = MAX_SMAPLE_RATE_API;
        Seg->tx_seg.vsaParam.SmpTime = 2 * Ms;
        Seg->tx_seg.vsaParam.TrigPretime = 20 * Us;
        Seg->tx_seg.vsaParam.MaxIFGGap = 0.1;
        Seg->tx_seg.vsaParam.TimeoutWaiting = 4.0;

        //初始化trig参数
        Seg->tx_seg.vsaTrigParam.Edge = WT_TRIG_DEGE_POSITIVE_API;
        Seg->tx_seg.vsaTrigParam.GapTime = 6e-6;

        //初始化分析参数
        Seg->tx_seg.vsaAlzParam.Reset();

        //初始化时间参数
        Seg->tx_seg.SegTimeParam.Duration = 0.005;
        Seg->tx_seg.SegTimeParam.Meaoffset = 0;
        Seg->tx_seg.SegTimeParam.Meadura = 0.001;
        Seg->tx_seg.SegTimeParam.Repeat = 1;
        Seg->tx_seg.SegTimeParam.Repeat = 1;
    }
    else
    {
        //初始化vsg参数
        memset(&Seg->rx_seg.vsgParam, 0, sizeof(VsgParameter));
        Seg->rx_seg.vsgParam.Freq = 2412 * MHz_API;
        Seg->rx_seg.vsgParam.Freq2 = 0.0;
        Seg->rx_seg.vsgParam.FreqOffset = 0.0;
        Seg->rx_seg.vsgParam.VsgUnitMask[0] = 0;
        Seg->rx_seg.vsgParam.Power[0] = -10.0;
        Seg->rx_seg.vsgParam.RfPort[0] = WT_PORT_RF1;
        Seg->rx_seg.vsgParam.ExtPathLoss[0] = 0.0;
        Seg->rx_seg.vsgParam.ExtPathLoss2[0] = 0.0;
        Seg->rx_seg.vsgParam.ValidNum = 1;
        Seg->rx_seg.vsgParam.SamplingFreq = MAX_SMAPLE_RATE_API;
        Seg->rx_seg.vsgParam.TimeoutWaiting = 4.0;

        //初始化wave参数
        memset(&Seg->rx_seg.waveParam, 0, sizeof(VsgPattern));
        if (m_LowNam.length() == 0)
        {
            Seg->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
        }
        else
        {
            Seg->rx_seg.waveParam.WaveType = SIG_USERFILE;
            strcpy(Seg->rx_seg.waveParam.WaveName, m_LowNam.c_str());
        }

        //初始化时间参数
        Seg->rx_seg.SegTimeParam.Duration = 0.005;
        Seg->rx_seg.SegTimeParam.Meaoffset = 0.000017;
        Seg->rx_seg.SegTimeParam.Meadura = 0;
        Seg->rx_seg.SegTimeParam.Repeat = 1;
        Seg->rx_seg.waveParam.Repeat = 1;
        Seg->rx_seg.waveParam.Extend = 0;
        Seg->rx_seg.vsgSyncParam = 0;

        Seg->rx_seg.vsgSyncParam = 0;
    }
}

void Sequence::CheckListSeqSegNo(int SegNo)
{
    int Size = m_Seg.size();

    if (Size <= SegNo)
    {
        m_MaxSegSize = SegNo + 1;
        for (; Size < m_MaxSegSize; Size++)
        {
            m_Seg.push_back(nullptr);
        }
    }
}

void Sequence::SetListSeqFreq(SEQUENCETYPE Type, int SegNo, double Freq)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.Freq = Freq;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.Freq = Freq;
    }
}

void Sequence::SetListSeqFreqAll(SEQUENCETYPE Type, double Freq)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.Freq = Freq;
        }
        else
        {
            Iter->rx_seg.vsgParam.Freq = Freq;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqPower(SEQUENCETYPE Type, int SegNo, double Power)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] = Power;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.Power[0] = Power;
    }
}

void Sequence::SetListSeqPowerAll(SEQUENCETYPE Type, double Power)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.MaxPower[0] = Power;
        }
        else
        {
            Iter->rx_seg.vsgParam.Power[0] = Power;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqPort(SEQUENCETYPE Type, int SegNo, double Port)
{
    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.RfPort[0] = Port;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.RfPort[0] = Port;
    }
}

void Sequence::SetListSeqPortAll(SEQUENCETYPE Type, double Port)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.RfPort[0] = Port;
        }
        else
        {
            Iter->rx_seg.vsgParam.RfPort[0] = Port;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqSync(SEQUENCETYPE Type, int SegNo, int Sync)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
       /*
        if(Sync)
        {
            m_Seg[SegNo]->tx_seg.vsaParam.TrigType = 2;
        }
        else
        {
            m_Seg[SegNo]->tx_seg.vsaParam.TrigType = 0;
        }*/

    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgSyncParam = Sync;
    }
}

void Sequence::SetListSeqSyncAll(SEQUENCETYPE Type, int Sync)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            /*if(Sync)
            {
                Iter->tx_seg.vsaParam.TrigType = 2;
            }
            else
            {
                Iter->tx_seg.vsaParam.TrigType = 0;
            }*/
        }
        else
        {
            Iter->rx_seg.vsgSyncParam = Sync;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqSampleRate(SEQUENCETYPE Type, int SegNo, double SampleRate)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.SamplingFreq = SampleRate;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.SamplingFreq = SampleRate;
    }
}

void Sequence::SetListSeqSampleRateAll(SEQUENCETYPE Type, double SampleRate)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.SamplingFreq = SampleRate;
        }
        else
        {
            Iter->rx_seg.vsgParam.SamplingFreq = SampleRate;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqExtGain(SEQUENCETYPE Type, int SegNo, double ExtGain)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.ExtPathLoss[0] = ExtGain;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.ExtPathLoss[0] = ExtGain;
    }
}

void Sequence::SetListSeqExtGainAll(SEQUENCETYPE Type, double ExtGain)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.ExtPathLoss[0] = ExtGain;
        }
        else
        {
            Iter->rx_seg.vsgParam.ExtPathLoss[0] = ExtGain;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqTriggerType(SEQUENCETYPE Type, int SegNo, int TrigType)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.TrigType = TrigType;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqTriggerTypeAll(SEQUENCETYPE Type, int TrigType)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.TrigType = TrigType;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqTriggerLevel(SEQUENCETYPE Type, int SegNo, double TrigLevel)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.TrigLevel = TrigLevel;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqTriggerLevelAll(SEQUENCETYPE Type, double TrigLevel)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.TrigLevel = TrigLevel;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListRxSeqWave(int SegNo, std::string &LowName)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->TxFlag = false;
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == false)
    {
        if (LowName.length() == 0)
        {
            m_Seg[SegNo]->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
        }
        else
        {
            m_Seg[SegNo]->rx_seg.waveParam.WaveType = SIG_USERFILE;
            strcpy(m_Seg[SegNo]->rx_seg.waveParam.WaveName, LowName.c_str());
        }
    }
}

void Sequence::SetListRxSeqWaveAll(std::string &LowName)
{
    int SegNo = 0;

    m_LowNam = LowName;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            if (LowName.length() == 0)
            {
                Iter->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
            }
            else
            {
                Iter->rx_seg.waveParam.WaveType = SIG_USERFILE;
                strcpy(Iter->rx_seg.waveParam.WaveName, LowName.c_str());
            }
         }
        SegNo++;
    }

}

void Sequence::SetListSeqDuration(SEQUENCETYPE Type, int SegNo, double Duration)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Duration = Duration;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Duration = Duration;
    }
}

void Sequence::SetListSeqDurationAll(SEQUENCETYPE Type, double Duration)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Duration = Duration;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Duration = Duration;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqMeaoffset(SEQUENCETYPE Type, int SegNo, double Meaoffset)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meaoffset = Meaoffset;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Meaoffset = Meaoffset;
    }
}

void Sequence::SetListSeqMeaoffsetAll(SEQUENCETYPE Type, double Meaoffset)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Meaoffset = Meaoffset;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Meaoffset = Meaoffset;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqMeaDur(SEQUENCETYPE Type, int SegNo, double Meadura)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meadura = Meadura;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Meadura = Meadura;
    }
}

void Sequence::SetListSeqMeaDurAll(SEQUENCETYPE Type, double Meadura)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Meadura = Meadura;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Meadura = Meadura;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqRepeat(SEQUENCETYPE Type, int SegNo, double Repeat)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Repeat = Repeat;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Repeat = Repeat;
    }
}

void Sequence::SetListSeqRepeatAll(SEQUENCETYPE Type, double Repeat)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Repeat = Repeat;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Repeat = Repeat;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqAnalDemod(SEQUENCETYPE Type, int SegNo, int AnalDemod)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.Demode = AnalDemod;
        if (IsAlg3GPPStandardType(AnalDemod))
        {
            m_Seg[SegNo]->tx_seg.vsaAlzParam.Reset_AlzParam(m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP, AnalDemod);
        }
        else if (AnalDemod == WT_DEMOD_LRWPAN_FSK || AnalDemod == WT_DEMOD_LRWPAN_OQPSK || AnalDemod == WT_DEMOD_LRWPAN_OFDM)
        {
            m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParamWiSun.Demode = AnalDemod;
        }
        else
        {
            m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParamWifi.Demode = AnalDemod;
        }
    }
    else
    {
        return;
    }
}

void Sequence::SetListSeqAnalDemodAll(SEQUENCETYPE Type, int AnalDemod)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.Demode = AnalDemod;
            if (IsAlg3GPPStandardType(AnalDemod))
            {
                Iter->tx_seg.vsaAlzParam.Reset_AlzParam(Iter->tx_seg.vsaAlzParam.analyzeParam3GPP, AnalDemod);
            }
            else if (AnalDemod == WT_DEMOD_LRWPAN_FSK || AnalDemod == WT_DEMOD_LRWPAN_OQPSK || AnalDemod == WT_DEMOD_LRWPAN_OFDM)
            {
                Iter->tx_seg.vsaAlzParam.analyzeParamWiSun.Demode = AnalDemod;
            }
            else
            {
                Iter->tx_seg.vsaAlzParam.analyzeParamWifi.Demode = AnalDemod;
            }
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::DeleteListSeqSeg(int SegNo)
{
    std::vector<std::unique_ptr<Segment>>::iterator Iter = m_Seg.begin() + SegNo;
    m_Seg.erase(Iter);
}

void Sequence::DeleteListSeqSegAll()
{
    m_SeqType = SEQUENCETX;
    m_MaxSegSize = 0;
    SeqStat = SEQUENCEOFF;
    m_Seg.clear();
}

void Sequence::SetListLteTxSeqParam(SEQUENCETYPE Type, int SegNo, double *LteTxPara)
{
    const double LeftOff = 300 * Us;
    const double Subframe = 1.0 * Ms;

    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Duration = LteTxPara[0] * Subframe;
        m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] = LteTxPara[1];
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Duplexing = LteTxPara[2];
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.rf_band[0] = LteTxPara[3];
        m_Seg[SegNo]->tx_seg.vsaParam.Freq = LteTxPara[4];
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.ChannelBW = LteTxPara[5];
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.CyclicPrefix = LteTxPara[6];
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.ChanType = LteTxPara[7]; //lte分支合并主线，先屏蔽
        m_Seg[SegNo]->tx_seg.vsaParam.TrigType = LteTxPara[8];
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meaoffset = (LteTxPara[9] * Subframe >= LeftOff) ? LteTxPara[9] * Subframe - LeftOff : LteTxPara[9] * Subframe;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqParam: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    LteTxPara[0]:" << LteTxPara[0] << "Duration" << m_Seg[SegNo]->tx_seg.SegTimeParam.Duration << std::endl
                                                             << "    LteTxPara[1]:" << LteTxPara[1] << "MaxPower" << m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] << std::endl
                                                             << "    LteTxPara[2] Duplexing:" << LteTxPara[2] << std::endl
                                                             << "    LteTxPara[3] rf_band:" << LteTxPara[3] << std::endl
                                                             << "    LteTxPara[4] Freq:" << LteTxPara[4] << std::endl
                                                             << "    LteTxPara[5] ChannelBW:" << LteTxPara[5] << std::endl
                                                             << "    LteTxPara[6] CyclicPrefix:" << LteTxPara[6] << std::endl
                                                             << "    LteTxPara[7] ChanType:" << LteTxPara[7] << std::endl
                                                             << "    LteTxPara[8] TrigType:" << LteTxPara[8] << std::endl
                                                             << "    LteTxPara[9] Meaoffset:" << LteTxPara[9] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqTdd(SEQUENCETYPE Type, int SegNo, int UpDownLink, int SpecSubframe)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.ULDLConfig = UpDownLink;
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.SpecialSubfrmConfig = SpecSubframe; //lte分支合并主线，先屏蔽
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqTdd: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ULDLConfig:" << UpDownLink << std::endl
                                                             << "    SpecialSubfrmConfig:" << SpecSubframe << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqRbAllocation(SEQUENCETYPE Type, int SegNo, int Auto, int NoRb, int Offset)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        /*m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.RBAutoMode = Auto;
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.RBNum = NoRb;
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.RBOffset = Offset;*/ //lte分支合并主线，先屏蔽
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqRbAllocation: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    RBAutoMode:" << Auto << std::endl
                                                             << "    RBNum:" << NoRb << std::endl
                                                             << "    RBOffset:" << Offset << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *LteTxModulation)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        /*m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModStatNum = LteTxModulation[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModEnable = LteTxModulation[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.EvmEnable = LteTxModulation[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.MErrEnable = LteTxModulation[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PErrEnable = LteTxModulation[4];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.IBEEnable = LteTxModulation[5];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ESFlatEnable = LteTxModulation[6];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Modulate = LteTxModulation[7];*/ //lte分支合并主线，先屏蔽
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqModulation: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ModStatNum:" << LteTxModulation[0] << std::endl
                                                             << "    ModEnable:" << LteTxModulation[1] << std::endl
                                                             << "    EvmEnable:" << LteTxModulation[2] << std::endl
                                                             << "    MErrEnable:" << LteTxModulation[3] << std::endl
                                                             << "    PErrEnable:" << LteTxModulation[4] << std::endl
                                                             << "    IBEEnable:" << LteTxModulation[5] << std::endl
                                                             << "    ESFlatEnable:" << LteTxModulation[6] << std::endl
                                                             << "    Modulate:" << LteTxModulation[7] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *LteTxSemask)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        /*m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMStatNum = LteTxSemask[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SpectEnable = LteTxSemask[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.OBWEnable = LteTxSemask[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMEnable = LteTxSemask[3];*/
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqSemask: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    SEMStatNum:" << LteTxSemask[0] << std::endl
                                                             << "    SpectEnable:" << LteTxSemask[1] << std::endl
                                                             << "    OBWEnable:" << LteTxSemask[2] << std::endl
                                                             << "    SEMEnable:" << LteTxSemask[3] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *LteTxAclr)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        /*m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLRStatNum = LteTxAclr[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLREnable = LteTxAclr[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.UTRA1Enable = LteTxAclr[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.UTRA2Enable = LteTxAclr[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.EUTRAEnable = LteTxAclr[4];*/ //lte分支合并主线，先屏蔽
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqAclr: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ACLRStatNum:" << LteTxAclr[0] << std::endl
                                                             << "    ACLREnable:" << LteTxAclr[1] << std::endl
                                                             << "    UTRA1Enable:" << LteTxAclr[2] << std::endl
                                                             << "    UTRA2Enable:" << LteTxAclr[3] << std::endl
                                                             << "    EUTRAEnable:" << LteTxAclr[4] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PMonitorEnable = PowMonEnab; //lte分支合并主线，先屏蔽
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqPower(SEQUENCETYPE Type, int SegNo, int *LteTxPower)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerStatNum = LteTxPower[0];
        //m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerEnable = LteTxPower[1]; //lte分支合并主线，先屏蔽
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqAclr: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    PowerStatNum:" << LteTxPower[0] << std::endl
                                                             << "    PowerEnable:" << LteTxPower[1] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListSeqRepet(int Repet)
{
    m_Repet = Repet;
}

void Sequence::SetListTxSeqTrigerOffset(double TrigerOffset)
{
    m_TrigerOffset = TrigerOffset;
}

int Sequence::GetListSeqRepet()
{
    return m_Repet;
}

double Sequence::GetListTxSeqTrigerOffset()
{
    return m_TrigerOffset;
}

void Sequence::SetListRxSeqArbRepet(int SegNo, int Repet)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->TxFlag = false;

        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == false)
    {
        m_Seg[SegNo]->rx_seg.waveParam.Repeat = Repet;
    }
}

void Sequence::SetListRxSeqArbRepetAll(int Repet)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            Iter->rx_seg.waveParam.Repeat = Repet;
        }
        SegNo++;
    }
}

void Sequence::SetListRxSeqArbExtend(int SegNo, int Extend)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->TxFlag = false;

        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == false)
    {
        m_Seg[SegNo]->rx_seg.waveParam.Extend = Extend;
    }
}

void Sequence::SetListRxSeqArbExtendAll(int Extend)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            Iter->rx_seg.waveParam.Extend = Extend;
        }
        SegNo++;
    }
}

void ListSeq::SetTxListModEnable()
{
    m_Seq[0].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = true;
}

void ListSeq::SetTxListModDisable()
{
    m_Seq[0].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = false;
}

void ListSeq::SetRxListModEnable()
{
    m_Seq[1].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = true;
}

void ListSeq::SetRxListModDisable()
{
    m_Seq[1].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = false;
}


int ListSeq::GetListModSeqSize(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        return m_Seq[0].GetSeqSegNum();
    }
    else
    {
        return m_Seq[1].GetSeqSegNum();
    }
}

int ListSeq::GetListModRealSeqSize(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        // printf("ListSeq::GetListModRealSeqSize(SEQUENCETYPE Type)\n");
        return m_Seq[0].GetSeqRealSegNum();
    }
    else
    {
        return m_Seq[1].GetSeqRealSegNum();
    }
}

void ListSeq::ClearListSeq()
{
    int i;

    for (i = 0; i < 2; i++)
    {
        m_Seq[i].ClearSeg();
    }
}

std::vector<std::unique_ptr<Segment>>::iterator ListSeq::GetSegBeginIter(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
       return m_Seq[0].GetSegBeginIter();
    }
    else
    {
       return m_Seq[1].GetSegBeginIter();
    }
}

std::vector<std::unique_ptr<Segment>>::iterator ListSeq::GetSegEndIter(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
       return m_Seq[0].GetSegEndIter();
    }
    else
    {
       return m_Seq[1].GetSegEndIter();
    }
}

void ListSeq::SetListSeqStat(SEQUENCETYPE Type, SEQUENCESTATE Stat)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        m_Seq[0].SetSeqStat(Stat);
    }
    else
    {
        m_Seq[1].SetSeqStat(Stat);
    }
}
SEQUENCESTATE ListSeq::GetListSeqStat(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        return m_Seq[0].GetSeqStat();
    }
    else
    {
        return m_Seq[1].GetSeqStat();
    }
}

void ListSeq::SetListSeqMaxSize(SEQUENCETYPE Type, int Sizes)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        m_Seq[0].SetSeqMaxSize(Sizes);
    }
    else
    {
        m_Seq[1].SetSeqMaxSize(Sizes);
    }
}

void ListSeq::SetListSeqFreq(SEQUENCETYPE Type, int SegNo, double Freq)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqFreq(Type, SegNo, Freq);
    }
    else
    {
        m_Seq[1].SetListSeqFreq(Type, SegNo, Freq);
    }
}

void ListSeq::SetListSeqFreqAll(SEQUENCETYPE Type, double Freq)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqFreqAll(Type,  Freq);
    }
    else
    {
        m_Seq[1].SetListSeqFreqAll(Type, Freq);
    }
}

void ListSeq::SetListSeqPower(SEQUENCETYPE Type, int SegNo, double Power)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPower(Type, SegNo, Power);
    }
    else
    {
        m_Seq[1].SetListSeqPower(Type, SegNo, Power);
    }
}

void ListSeq::SetListSeqPowerAll(SEQUENCETYPE Type, double Power)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPowerAll(Type,  Power);
    }
    else
    {
        m_Seq[1].SetListSeqPowerAll(Type, Power);
    }
}

void ListSeq::SetListSeqPort(SEQUENCETYPE Type, int SegNo, double Port)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPort(Type, SegNo, Port);
    }
    else
    {
        m_Seq[1].SetListSeqPort(Type, SegNo, Port);
    }
}

void ListSeq::SetListSeqPortAll(SEQUENCETYPE Type, double Port)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPortAll(Type,  Port);
    }
    else
    {
        m_Seq[1].SetListSeqPortAll(Type, Port);
    }
}

void ListSeq::SetListSeqSync(SEQUENCETYPE Type, int SegNo, int Sync)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSync(Type, SegNo, Sync);
    }
    else
    {
        m_Seq[1].SetListSeqSync(Type, SegNo, Sync);
    }
}

void ListSeq::SetListSeqSyncAll(SEQUENCETYPE Type, int Sync)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSyncAll(Type, Sync);
    }
    else
    {
        m_Seq[1].SetListSeqSyncAll(Type, Sync);
    }
}

void ListSeq::SetListSeqSampleRate(SEQUENCETYPE Type, int SegNo, double SampleRate)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSampleRate(Type, SegNo, SampleRate);
    }
    else
    {
        m_Seq[1].SetListSeqSampleRate(Type, SegNo, SampleRate);
    }
}

void ListSeq::SetListSeqSampleRateAll(SEQUENCETYPE Type, double SampleRate)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSampleRateAll(Type,  SampleRate);
    }
    else
    {
        m_Seq[1].SetListSeqSampleRateAll(Type, SampleRate);
    }
}

void ListSeq::SetListSeqExtGain(SEQUENCETYPE Type, int SegNo, double ExtGain)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqExtGain(Type, SegNo, ExtGain);
    }
    else
    {
        m_Seq[1].SetListSeqExtGain(Type, SegNo, ExtGain);
    }
}

void ListSeq::SetListSeqExtGainAll(SEQUENCETYPE Type, double ExtGain)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqExtGainAll(Type, ExtGain);
    }
    else
    {
        m_Seq[1].SetListSeqExtGainAll(Type, ExtGain);
    }
}

void ListSeq::SetListTxSeqTriggerType(SEQUENCETYPE Type, int SegNo, int TrigType)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerType(Type, SegNo, TrigType);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerType(Type, SegNo, TrigType);
    }
}

void ListSeq::SetListTxSeqTriggerTypeAll(SEQUENCETYPE Type, int TrigType)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerTypeAll(Type, TrigType);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerTypeAll(Type, TrigType);
    }
}

void ListSeq::SetListTxSeqTriggerLevel(SEQUENCETYPE Type, int SegNo, double TrigLevel)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerLevel(Type, SegNo, TrigLevel);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerLevel(Type, SegNo, TrigLevel);
    }
}

void ListSeq::SetListTxSeqTriggerLevelAll(SEQUENCETYPE Type, double TrigLevel)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerLevelAll(Type, TrigLevel);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerLevelAll(Type, TrigLevel);
    }
}

void ListSeq::SetListRxSeqWave(int SegNo, std::string &LowName)
{
    if (m_SeqScen == LISTSCEN_COMB)
    {
        m_Seq[0].SetListRxSeqWave(SegNo, LowName);
    }
    else
    {
        m_Seq[1].SetListRxSeqWave(SegNo, LowName);
    }
}

void ListSeq::SetListRxSeqWaveAll(std::string &LowName)
{
    if (m_SeqScen == LISTSCEN_COMB)
    {
        m_Seq[0].SetListRxSeqWaveAll(LowName);
    }
    else
    {
        m_Seq[1].SetListRxSeqWaveAll(LowName);
    }
}

void ListSeq::SetListSeqDuration(SEQUENCETYPE Type, int SegNo, double Duration)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqDuration(Type, SegNo, Duration);
    }
    else
    {
        m_Seq[1].SetListSeqDuration(Type, SegNo, Duration);
    }
}

void ListSeq::SetListSeqDurationAll(SEQUENCETYPE Type, double Duration)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqDurationAll(Type, Duration);
    }
    else
    {
        m_Seq[1].SetListSeqDurationAll(Type, Duration);
    }
}

void ListSeq::SetListSeqMeaoffset(SEQUENCETYPE Type, int SegNo, double Meaoffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaoffset(Type, SegNo, Meaoffset);
    }
    else
    {
        m_Seq[1].SetListSeqMeaoffset(Type, SegNo, Meaoffset);
    }
}

void ListSeq::SetListSeqMeaoffsetAll(SEQUENCETYPE Type, double Meaoffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaoffsetAll(Type, Meaoffset);
    }
    else
    {
        m_Seq[1].SetListSeqMeaoffsetAll(Type, Meaoffset);
    }
}
void ListSeq::SetListSeqMeaDur(SEQUENCETYPE Type, int SegNo, double Meadura)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaDur(Type, SegNo, Meadura);
    }
    else
    {
        m_Seq[1].SetListSeqMeaDur(Type, SegNo, Meadura);
    }
}

void ListSeq::SetListSeqMeaDurAll(SEQUENCETYPE Type, double Meadura)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaDurAll(Type, Meadura);
    }
    else
    {
        m_Seq[1].SetListSeqMeaDurAll(Type, Meadura);
    }
}

void ListSeq::SetListSeqRepeat(SEQUENCETYPE Type, int SegNo, double Repeat)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqRepeat(Type, SegNo, Repeat);
    }
    else
    {
        m_Seq[1].SetListSeqRepeat(Type, SegNo, Repeat);
    }
}

void ListSeq::SetListSeqRepeatAll(SEQUENCETYPE Type, double Repeat)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqRepeatAll(Type, Repeat);
    }
    else
    {
        m_Seq[1].SetListSeqRepeatAll(Type, Repeat);
    }
}

void ListSeq::SetListSeqAnalDemod(SEQUENCETYPE Type, int SegNo, int AnalDemod)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqAnalDemod(Type, SegNo, AnalDemod);
    }
    else
    {
        m_Seq[1].SetListSeqAnalDemod(Type, SegNo, AnalDemod);
    }
}

void ListSeq::SetListSeqAnalDemodAll(SEQUENCETYPE Type, int AnalDemod)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqAnalDemodAll(Type, AnalDemod);
    }
    else
    {
        m_Seq[1].SetListSeqAnalDemodAll(Type, AnalDemod);
    }
}

void ListSeq::DeleteListSeqSeg(SEQUENCETYPE Type, int SegNo)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].DeleteListSeqSeg(SegNo);
    }
    else
    {
        m_Seq[1].DeleteListSeqSeg(SegNo);
    }
}

void ListSeq::DeleteListSeqSegAll(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].DeleteListSeqSegAll();
    }
    else
    {
        m_Seq[1].DeleteListSeqSegAll();
    }
}

void ListSeq::SetListLteTxSeqParam(SEQUENCETYPE Type, int SegNo, double *LteTxPara)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqParam(Type, SegNo, LteTxPara);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqParam(Type, SegNo, LteTxPara);
    }
}

void ListSeq::SetListLteTxSeqTdd(SEQUENCETYPE Type, int SegNo, int UpDownLink, int SpecSubframe)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqTdd(Type, SegNo, UpDownLink, SpecSubframe);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqTdd(Type, SegNo, UpDownLink, SpecSubframe);
    }
}

void ListSeq::SetListLteTxSeqRbAllocation(SEQUENCETYPE Type, int SegNo, int Auto, int NoRb, int Offset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqRbAllocation(Type, SegNo, Auto, NoRb, Offset);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqRbAllocation(Type, SegNo, Auto, NoRb, Offset);
    }
}

void ListSeq::SetListLteTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *LteTxModulation)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqModulation(Type, SegNo, LteTxModulation);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqModulation(Type, SegNo, LteTxModulation);
    }
}

void ListSeq::SetListLteTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *LteTxSemask)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqSemask(Type, SegNo, LteTxSemask);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqSemask(Type, SegNo, LteTxSemask);
    }
}

void ListSeq::SetListLteTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *LteTxAclr)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqAclr(Type, SegNo, LteTxAclr);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqAclr(Type, SegNo, LteTxAclr);
    }
}

void ListSeq::SetListLteTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqPmonitor(Type, SegNo, PowMonEnab);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqPmonitor(Type, SegNo, PowMonEnab);
    }
}

void ListSeq::SetListLteTxSeqPower(SEQUENCETYPE Type, int SegNo, int *LteTxPower)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqPower(Type, SegNo, LteTxPower);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqPower(Type, SegNo, LteTxPower);
    }
}

void ListSeq::SetListSeqRepet(SEQUENCETYPE Type, int Repet)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqRepet(Repet);
    }
    else
    {
        m_Seq[1].SetListSeqRepet(Repet);
    }
}

void ListSeq::SetListTxSeqTrigerOffset(SEQUENCETYPE Type, double TrigerOffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTrigerOffset(TrigerOffset);
    }
    else
    {
        m_Seq[1].SetListTxSeqTrigerOffset(TrigerOffset);
    }
}

int ListSeq::GetListSeqRepet(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListSeqRepet();
    }
    else
    {
        return m_Seq[1].GetListSeqRepet();
    }
}

double ListSeq::GetListTxSeqTrigerOffset(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListTxSeqTrigerOffset();
    }
    else
    {
        return m_Seq[1].GetListTxSeqTrigerOffset();
    }
}


void ListSeq::SetListRxSeqArbRepet(int SegNo, int Repet)
{
    m_Seq[1].SetListRxSeqArbRepet(SegNo, Repet);
}

void ListSeq::SetListRxSeqArbRepetAll(int Repet)
{
    m_Seq[1].SetListRxSeqArbRepetAll(Repet);
}

void ListSeq::SetListRxSeqArbExtend(int SegNo, int Extend)
{
    m_Seq[1].SetListRxSeqArbExtend(SegNo, Extend);
}

void ListSeq::SetListRxSeqArbExtendAll(int Extend)
{
    m_Seq[1].SetListRxSeqArbExtendAll(Extend);
}

