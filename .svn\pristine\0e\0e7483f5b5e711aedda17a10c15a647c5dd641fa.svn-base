//*****************************************************************************
//  File: wtbsn.h
//  vsa/vsg 等业务处理的模板基类
//  Data: 2016.8.17
//*****************************************************************************
#ifndef __WT_BSN_H__
#define __WT_BSN_H__

#include <memory>
#include <list>
#include <functional>
#include <atomic>
#include <string>
#include <algorithm>
#include <fstream>
#include <unistd.h>
#include <dirent.h>
#include "wtev++.h"
#include "wtypes.h"
#include "wterror.h"
#include "wtlog.h"
#include "conf.h"
#include "devlib.h"
#include "license.h"
#include "wt_protocol.h"
#include "devmgr.h"
#include "connector.h"
#include "analysis/sigfile.h"
#include "digitallib.h"
#include "wtspec.h"
#include "broadcastvsg.h"
//#include "server_listmod_sequence.h"

using namespace std;

#define MOD_NOT_RUNNING    -2 //未开始运行
#define MOD_RUNNING        -1 //正在运行
#define MOD_RUN_FINISH     0  //运行完成

#define SET_PARAM_WHITOUT_PN    (-2)

#ifndef MHz
#define MHz (1e6)
#endif

#ifndef KHz
#define KHz (1e3)
#endif

#ifndef Ms
#define Ms (1e-3)
#endif

#ifndef Us
#define Us (1e-6)
#endif

struct ModInfo
{
    ModInfo(int Id, int Type) : ModId(Id), ModType(Type), Status(WT_RX_TX_STATE_DONE) {}
    ModInfo(ModInfo &&Mod)
    {
        Move(Mod);
    }

    ModInfo& operator=(ModInfo &Mod)
    {
        Move(Mod);
        return *this;
    }

    ModInfo& operator=(ModInfo &&Mod)
    {
        Move(Mod);
        return *this;
    }

    ~ModInfo()
    {
        if (NeedFree)
        {
            DevMgr::Instance().FreeMod(ModType, ModId);
        }
    }

    void Move(ModInfo &Mod)
    {
        if (ModId != -1 && NeedFree)
        {
            DevMgr::Instance().FreeMod(ModType, ModId);
        }

        RFPort = Mod.RFPort;
        ModId = Mod.ModId;
        ModType = Mod.ModType;
        Status = Mod.Status;
        IsConfig = Mod.IsConfig;
        NeedFree = Mod.NeedFree;
        Mod.NeedFree = false;
        IsMaster = Mod.IsMaster;
    }

    int RFPort = 0;          //模块对应的RF口
    int ModId = -1;          //模块ID
    int ModType;             //模块类型
    int Status;              //运行状态
    bool IsConfig = false;   //是否已经配置
    bool NeedFree = true;    //是否需要释放
    bool IsMaster = false;   //80+80是否为Master
};

struct TBTConfig
{
    int APFrameComputeMode;       //TBT AP模式计算帧结束位置。0:平坦度补偿前计算 1:平坦度补偿后计算
    int APIQDataThreshold;        //TBT AP模式计算帧结束位置时判断帧结束的IQ阈值
    int APMimoCompensate;         //TBT AP模式MIMO时的延时补偿（主从机都补偿）
    int APSlaveCompensate;        //TBT AP模式MIMO时Slave的额外延时补偿，在MimoCompensate基础上再次补偿
    int APSingleSlaveCompensate;  //TBT AP模式MIMO时本机Slave的额外延时补偿，在SlaveCompensate基础上再次补偿
    int SlaveTrigSel;             //MIMO时从机选择触发单元  0:Slave Unit; 1:Master Unit
    
    int STAHwCompensate;          //TBT STA模式硬件延时补偿
    int STAIFGCompensate;         //TBT STA模式IFG额外延时补偿
    int STA5GCompensate;          //TBT STA模式5G频段额外延时补偿
    int STAMimoCompensate;        //TBT STA模式MIMO时额外延时补偿
    int STASlaveCompensate;       //TBT STA模式MIMO时Slave的额外延时补偿
    int STASingleSlaveCompensate; //TBT STA模式MIMO时本机Slave的额外延时补偿

    TBTConfig()
    {
        WTConf m_Conf(WTConf::GetDir() + "/triggerbase.conf");
        GET_CONF_DATA(m_Conf, "FirstFrameEndCompute", APFrameComputeMode, 1);
        GET_CONF_DATA(m_Conf, "IQDataThreshold", APIQDataThreshold, 1000);
        GET_CONF_DATA(m_Conf, "MimoCompensate", APMimoCompensate, 0);
        GET_CONF_DATA(m_Conf, "SlaveCompensate", APSlaveCompensate, 0);
        GET_CONF_DATA(m_Conf, "SingleSlaveCompensate", APSingleSlaveCompensate, 0);
        GET_CONF_DATA(m_Conf, "IsUseVsgMasterTrig", SlaveTrigSel, 0);
        GET_CONF_DATA(m_Conf, "STAHwCompensate", STAHwCompensate, 0);
        GET_CONF_DATA(m_Conf, "STAIFGCompensate", STAIFGCompensate, 0);
        GET_CONF_DATA(m_Conf, "STA5GCompensate", STA5GCompensate, 0);
        GET_CONF_DATA(m_Conf, "STAMimoCompensate", STAMimoCompensate, 0);
        GET_CONF_DATA(m_Conf, "STASlaveCompensate", STASlaveCompensate, 0);
        GET_CONF_DATA(m_Conf, "STASingleSlaveCompensate", STASingleSlaveCompensate, 0);
    }
};

//停止模块运行
template<int ModType>
inline int StopDev(int ModId);

template <>
inline int StopDev<DEV_RES_VSA>(int ModId)
{
    return DevLib::Instance().VSAStop(ModId);
}

template<>
inline int StopDev<DEV_RES_VSG>(int ModId)
{
    return DevLib::Instance().VSGStop(ModId);
}

template<class P, int ModType>
class WTBsn
{
public:
    WTBsn(std::shared_ptr<Connector> ExtConn, bool Exclude, const wtev::loop_ref &Loop)
        : m_FinishEv(Loop), m_TempEv(Loop), m_ExtConn(ExtConn), m_Exclude(Exclude), m_FinMods(0)
    {
        m_FinishEv.set<WTBsn, &WTBsn::HwOpFin>(this);
        m_FinishEv.start();
        m_Notify = std::bind(&WTBsn::Complete, this,
                             std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
        m_CurDir = WTConf::GetDir();
        m_DigChanList.assign(1, 0);
        for (int i = 0; i < 4; i++)
        {
            m_ModStatus[i] = WT_RX_TX_STATE_DONE;
        }

        m_ListModeRun = false;
    }

    virtual ~WTBsn()
    {
        Terminate();
    }

    enum TBT_MODE_FLAG
    {
        TBT_MODE_VSG_VSA = 0,
        TBT_MODE_VSA_VSG,
        TBT_MODE_AGC,

        TBT_MODE_AP = TBT_MODE_VSG_VSA,
        TBT_MODE_STA = TBT_MODE_VSA_VSG,
    };

    //*****************************************************************************
    // 设置Dig业务对象
    // 参数[IN]: DigLib : Dig业务对象
    // 返回值: 无
    //*****************************************************************************
    void SetDigLib(const std::shared_ptr<DigitalLib> &DigLib) { m_DigLib = DigLib; }

    //*****************************************************************************
    // 获取Dig业务对象
    // 参数[IN]: 无
    // 返回值: Dig业务对象
    //*****************************************************************************
    DigitalLib &GetDigLib() { return *m_DigLib.get(); }

    //*****************************************************************************
    // 添加MIMO从机
    // 参数[IN]: Conn : 与MIMO从机连接
    // 返回值: 无
    //*****************************************************************************
    void AddMimoDev(std::shared_ptr<Connector> Conn)
    {
        m_SlaveConn.push_back(move(Conn));
        m_DevMask = (1 << m_SlaveConn.size()) - 1;
    }

    //*****************************************************************************
    // 删除MIMO从机
    // 参数[IN]: Conn : 与MIMO从机连接
    // 返回值: 无
    //*****************************************************************************
    void DelMimoDev(std::shared_ptr<Connector> Conn)
    {
        m_SlaveConn.remove(Conn);
        m_DevMask = (1 << m_SlaveConn.size()) - 1;
        m_DevStaMask &= m_DevMask;
    }

    //*****************************************************************************
    // 结束所有的业务
    // 参数: 无
    // 返回值: 无
    //*****************************************************************************
    void Terminate(void)
    {
        for (auto &Mod : m_Mods)
        {
            if (Mod.Status == WT_RX_TX_STATE_RUNNING)
            {
                StopMod(Mod);
            }
            DevLib::Instance().SetRFPort(Mod.ModId, static_cast<WT_DEV_TYPE>(Mod.ModType), Mod.RFPort, WT_RF_STATE_OFF);
        }
        m_Mods.clear();
        if(DigModeLib::Instance().IsDigMode())
        {
            GetDigLib().Stop(ModType);
            SetDigConfig(false);
        }
    }

    //*****************************************************************************
    // 设置VSA/VSG完成后的回掉处理函数
    // 参数[IN]: Func: 回掉函数
    // 返回值: 无
    //*****************************************************************************
    void SetFinAgent(const std::function<bool(int)> &Func) { m_FinAgent = Func; }

    //*****************************************************************************
    // 清除VSA/VSG完成后的回掉处理函数
    // 参数[IN]: 无
    // 返回值: 无
    //*****************************************************************************
    virtual void ClearFinAgent(void) { m_FinAgent = nullptr; }

    //*****************************************************************************
    // 停止从机（下发命令）
    // 参数[IN]: Result: MIMO操作的错误码（导致需停止从机的原因）。
    //           Code:   API命令的CMD号
    // 返回值: 成功或错误码
    //*****************************************************************************
    void StopSlave(int Result ,int Code)
    {
        if (m_StopSlave == 0)
        {
            for (auto iter = m_SlaveConn.begin(); iter != m_SlaveConn.end(); iter++)
            {
                int Ret = (*iter)->StopSlaveMod(ModType, Code);
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "stop mimo Slave device failed");
                }
            }
            m_StopSlave = 1;
            m_DevStaMaskStopSlave = 0;
            m_StopSlaveReason = Result;
        }
    }

    //*****************************************************************************
    // 停止从机（CMD及ACK响应接口）
    // 参数[IN]: Conn: 发送命令的连接
    //           Code: API命令的CMD号
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetStopSlave(Connector *Conn,int Code)
    {
        int Ret = WT_OK;

        if (!Conn->IsLinkToSlave())
        {
            //由于是内部命令，不存在API下发给主机的情况
            if (IsMIMOMaster())
            {
                WTLog::Instance().LOGERR(Ret, "SetStopSlave error");
            }

            SetStop();
            ClearFinAgent();
        }
        else
        {
            //如果从机的ACK和外部连接命令不匹配则不处理
            if (!m_ExtConn->IsMatch(Code))
            {
                return WT_OK;
            }
            auto iter = m_SlaveConn.begin();
            //如果为从机的响应则需要等所有的从机都响应后才能发送响应
            for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
            {
                if (*Conn == **iter)
                {
                    m_DevStaMaskStopSlave |= 1 << i;
                    break;
                }
            }
            if (m_DevMask == m_DevStaMaskStopSlave)
            {
                Ret = m_ExtConn->Response(m_StopSlaveReason);
            }
        }
        return Ret;
    }

    //*****************************************************************************
    // 配置数字IQ业务参数
    // 参数[IN]: Conn : 发送命令的连接
    //        ParamNum: 配置参数数量
    //           Param: 配置参数
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetParamDig(Connector *Conn, int ParamNum, void *Param)
    {
        int Ret = WT_OK;
        bool IsRunning;
        if (!Conn->IsLinkToSlave())
        {
            if (ParamNum <= 0)
            {
                WTLog::Instance().LOGERR(WT_ARG_ERROR, "param number <= 0");
                return WT_ARG_ERROR;
            }

            Ret = CheckModParam(static_cast<P*>(Param));
            if (Ret != WT_OK)
            {
                m_ParamNum = 0;
                return Ret;
            }

            Ret = StopRunningMod(IsRunning); //先停止正在运行的模块
            //保存参数
            if (m_MaxParamNum < ParamNum)
            {
                m_Param.reset(new (std::nothrow) P[ParamNum]);
                if (m_Param == nullptr)
                {
                    m_MaxParamNum = 0;
                    WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc switched mimo param buffer failed");
                    return WT_ALLOC_FAILED;
                }

                m_MaxParamNum = ParamNum;
            }

            memcpy(m_Param.get(), Param, sizeof(P) * ParamNum);
            m_ParamNum = ParamNum;

            //MIMOsignal id设置为0
            for (int i = 0; i < m_ParamNum; i++)
            {
                m_Param[i].SignalId = i;
            }

            // 独占模式下立即配置，非独占模式则无需现在配置，运行过程中更改配置也要即时配置生效.
            // MIMO主机模式下只配置不启动，非主机模式下则根据运行状态决定是否启动
            // 数字IQ模式没有自校准，独占模式仍可立即配置参数
            if (IsExclude() || IsRunning)
            {
                SetParamToMod(IsRunning);
            }
            else
            {
                SetDigConfig(false);
            }
        }
        return Ret;
    }

    //*****************************************************************************
    // 配置业务参数
    // 参数[IN]: Conn : 发送命令的连接
    //        ParamNum: 配置参数数量
    //           Param: 配置参数
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetParam(Connector *Conn, int ParamNum, void *Param)
    {
        if (DigModeLib::Instance().IsDigMode())
        {
            return SetParamDig(Conn, ParamNum, Param);
        }

        int Ret = WT_OK;
        if (!Conn->IsLinkToSlave())
        {
            ClearCurFlowResult();
            if (ParamNum <= 0)
            {
                WTLog::Instance().LOGERR(WT_ARG_ERROR, "param number <= 0");
                return WT_ARG_ERROR;
            }

            Ret = StopRunningMod(m_ModRunning); //先停止正在运行的模块
            if (Ret != WT_OK)
            {
                return Ret;
            }
#if 0
            ifstream Fs(WTConf::GetDir() + "/ParamTest.txt");
            int EnableTest = 0;
            Fs >> EnableTest;
            if (EnableTest)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "m_Param[0].ParamTest");
                static_cast<P*>(Param)->ParamTest();
            }
            Fs.close();
#endif
            Ret = CheckModParam(static_cast<P*>(Param));
            if (Ret != WT_OK)
            {
                m_ParamNum = 0;
                return Ret;
            }

            //保存参数
            if (m_MaxParamNum < ParamNum)
            {
                m_Param.reset(new (std::nothrow) P[ParamNum]());
                if (m_Param == nullptr)
                {
                    m_MaxParamNum = 0;
                    WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc switched mimo param buffer failed");
                    return WT_ALLOC_FAILED;
                }

                m_MaxParamNum = ParamNum;
            }

            if (ModType == DEV_TYPE_VSA)
            {
                P *TempPtr = m_Param.get();
                for (int i = 0; i < ParamNum; ++i)
                {
                    if (!(TempPtr[i] == ((P *)Param)[i]))
                    {
                        m_NeedClearAvgData = true;
                    }
                }
            }

            memcpy(m_Param.get(), Param, sizeof(P) * ParamNum);
            m_ParamNum = ParamNum;

            if(false == CheckParamValid())    //检查其他参数是否符合，目前监测了宽频谱下，设置的频点是否在范围内
            {
                return WT_WIDE_BAND_FREQ_ERROR;
            }

            //MIMO主机signal id需要设置为0，只有主机会收到多个参数
            if (ParamNum > 1)
            {
                m_Param[0].SignalId = 0;
            }

            // 独占模式使用资源后也立即释放，不再需要额外的释放处理
            // if (IsExclude() && ModType == DEV_TYPE_VSG && !IsSlaveMode(m_Param[0]) && !m_ModRunning)
            // {
            //     DevMgr::Instance().NotifyFreeResources(DEV_TYPE_VSG, m_Notify);
            // }

            // MIMO模式下需要将配置发送给从机，需要参数个数不少于MIMO数量
            if (IsMIMOMaster())
            {
                if (ParamNum >= 1 + m_SlaveConn.size())
                {
                    int i = 1;
                    for (auto iter = m_SlaveConn.begin(); iter != m_SlaveConn.end(); iter++, i++)
                    {
                        m_Param[i].SignalId = i;
                        Ret = (*iter)->SendModParam(ModType, &m_Param[i], sizeof(P));
                        if (Ret != WT_OK)
                        {
                            if (Ret == WT_SOCKET_CLOSED)
                            {
                                WTLog::Instance().WriteLog(LOG_DEBUG, "SendModParam Failed, SignalId[%d]\n", i);
                                Ret = WT_SOCKET_SLAVE_DATA_SEND_FAILED;
                            }
                            WTLog::Instance().LOGERR(Ret, "Send Param to mimo device failed");
                            return Ret;
                        }
                    }

                    Conn->SetRsp(false);   //需要等待从机返回后才能响应
                    m_DevStaMask = 0;
                    m_StopSlave = 0;
                }
                else
                {
                    WTLog::Instance().LOGERR(WT_PARAM_NUM_ERROR, "param count less than device");
                    return WT_PARAM_NUM_ERROR;
                }
            }

            // 独占模式下立即配置，非独占模式则无需现在配置，运行过程中更改配置也要即时配置生效.
            // MIMO主机模式下只配置不启动，非主机模式下则根据运行状态决定是否启动
            if (m_ModRunning)
            {
                Ret = !IsMIMOMaster() ? AllocSetMod(m_ModRunning) : AllocSetMod(false);
                if (Ret != WT_OK)
                {
                    SetCurFlowResult(Ret);
                    StopSlave(Ret, ModType == DEV_TYPE_VSA ? CMD_SET_VSA_PARAM : CMD_SET_VSG_PARAM);
                    ClearMod(m_Mods.size());
                    return Ret;
                }
            }
            else if (IsExclude() && !((BroadcastVsg::Instance().IsBroadcastUser() && ModType == DEV_RES_VSG)))
            {
                int Mask = m_Param[0].GetModMask();
                Ret = AllocMod(Mask);
                if (Ret == WT_OK)
                {
                    Ret = SetMod(SET_PARAM_WHITOUT_PN);
                    for (auto &Mod : m_Mods)
                    {
                        Mod.IsConfig = Ret == WT_OK;
                    }
                }
                if (Ret != WT_OK)
                {
                    SetCurFlowResult(Ret);
                    StopSlave(Ret, ModType == DEV_TYPE_VSA ? CMD_SET_VSA_PARAM : CMD_SET_VSG_PARAM);
                    return Ret;
                }
                ClearMod(m_Mods.size());
            }
        }
        else
        {
            Ret = Conn->GetCmdResult();
            if(Ret != WT_OK)
            {
                StopSlave(Ret, ModType == DEV_TYPE_VSA ? CMD_SET_VSA_PARAM : CMD_SET_VSG_PARAM);
                ClearMod(m_Mods.size());
            }
            else
            {
                ProcMimoAck(Conn, false);

                if (IsMimoDone() && GetCurFlowResult() == WT_OK)
                {
                    if (m_ModRunning)
                    {
                        Ret = StartAllMod();
                        if (Ret != WT_OK)
                        {
                            StopSlave(Ret, ModType == DEV_TYPE_VSA ? CMD_SET_VSA_PARAM : CMD_SET_VSG_PARAM);
                            ClearMod(m_Mods.size());
                            return Ret;
                        }
                    }
                    Ret = m_ExtConn->Response(Ret);
                }
            }
        }

        return Ret;
    }

    //*****************************************************************************
    // 获取配置参数
    // 参数[OUT]: Data : 参数地址
    //            Len  : 参数长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetParam(Connector *Conn, void **Data, int &Len)
    {
        (void)Len;
        if (m_Param == nullptr || m_ParamNum == 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "NO config parameter");
            return WT_ARG_ERROR;
        }

        int Ret = WT_OK;
        bool Response = false;

        if (!Conn->IsLinkToSlave())
        {
            if (!IsMIMOMaster())
            {
                Response = true;
            }
            else
            {
                Ret = TransmitMimoCmd();
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "start mimo device failed");
                    return Ret;
                }
            }

            Conn->SetRsp(false);
        }
        else
        {
            Ret = Conn->GetCmdResult();
            if (Ret == WT_OK)
            {
                auto iter = m_SlaveConn.begin();
                for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
                {
                    if (*Conn == **iter)
                    {
                        m_DevStaMask |= 1 << i;
                        memcpy(m_Param.get() + i + 1, *Data, sizeof(P));
                        break;
                    }
                }

                if (IsMimoDone())
                {
                    Response = true;
                }
            }
            else
            {
                Ret = m_ExtConn->Response(Ret);
            }
        }

        if (Response)
        {
            //如果是非连续160，需要将频率数据还原再配置回去
            bool Is160 = m_Param[0].Is160() && m_Param[0].IsAC8080();
            if (Is160)
            {
                m_Param[0].Freq += 40e6;
                m_Param[0].Freq2 = 0;
            }

            Ret = m_ExtConn->Response(m_Param.get(), sizeof(P) * m_ParamNum);

            if (Is160)
            {
                m_Param[0].Freq -= 40e6;
                m_Param[0].Freq2 = m_Param[0].Freq + 80e6;
            }
        }

        return Ret;
    }

    //*****************************************************************************
    // 启动VSA/VSG录制
    // 参数[IN]: 无
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StartRecord(int Start)
    {
        m_Record = (Start == 1);
        return WT_OK;
    }

    //*****************************************************************************
    // 获取录制的数据
    // 参数[OUT]: Data : 数据地址
    //            Len : 数据长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetRecordData(void **Data, int &Len)
    {
        // TODO待实现
        *Data = nullptr;
        Len = 0;

        return WT_OK;
    }

    //*****************************************************************************
    // 启动硬件开始进行VSA/VSG业务操作
    // 参数[IN]: Conn : 发送命令的连接
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StartDig(Connector *Conn)
    {
        int Ret = WT_OK;
        bool IsRunning = false;
        if (m_Param == nullptr || m_ParamNum == 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "NO config parameter");
            return WT_ARG_ERROR;
        }

        if (!Conn->IsLinkToSlave())
        {
            Ret = StopRunningMod(IsRunning); //先停止正在运行的模块
            if (GetDigConfig() == false)
            {
                SetMod();
            }
            //启动完成后立即返回响应
            Ret = StartAllMod();
        }
        return Ret;
    }

    //*****************************************************************************
    // 启动硬件开始进行VSA/VSG业务操作
    // 参数[IN]: Conn : 发送命令的连接
    // 返回值: 成功或错误码
    //*****************************************************************************
    int Start(Connector *Conn)
    {
        if (DigModeLib::Instance().IsDigMode())
        {
            return StartDig(Conn);
        }
        else if (BroadcastVsg::Instance().IsBroadcastUser() && ModType == DEV_RES_VSG)
        {
            return BroadcastVsg::Instance().Start();
        }

        int Ret = WT_OK;
        bool IsRunning = false;

        if (m_Param == nullptr || m_ParamNum == 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "NO config parameter");
            return WT_ARG_ERROR;
        }

        if (!Conn->IsLinkToSlave())
        {
            m_LocalStart = false;
            ClearCurFlowResult();
            //允许重新启动
            StopRunningMod(IsRunning);

            //独占连接、非独占连接都需要重新申请
            //主机先申请资源并配置参数
            Ret = AllocSetMod(false);
            if (Ret != WT_OK)
            {
                ClearMod(m_Mods.size());
                return Ret;
            }
            // MIMO模式下需要启动从机
            if (IsMIMOMaster())
            {
                Ret = TransmitMimoCmd();
                if (Ret != WT_OK)
                {
                    ClearMod(m_Mods.size());
                    WTLog::Instance().LOGERR(Ret, "start mimo device failed");
                    return Ret;
                }
                Conn->SetRsp(false);
                m_StopSlave = 0;
            }
            else // MIMO模式下主机需要等待从机启动完成后才能启动
            {
                //启动完成后立即返回响应
                Ret = StartAllMod();
            }
        }
        else
        {
            ProcMimoAck(Conn, false);
            Ret = Conn->GetCmdResult();
            if (Ret == WT_OK)
            {
                if (m_LocalStart) //本地启动时命令不match，不会更新m_DevStaMask，需要手动更新
                {
                    auto iter = m_SlaveConn.begin();
                    for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
                    {
                        if (*Conn == **iter)
                        {
                            m_DevStaMask |= 1 << i;
                            break;
                        }
                    }
                }

                //收到从机的启动响应后再启动主机
                if (IsMimoDone() && GetCurFlowResult() == WT_OK)
                {
                    if(ModType == CMD_START_VSG)
                    {
                        usleep(200);  //延时保证从机FPGA的运行完成 
                    }
                    else
                    {
                        usleep(500);  //延时保证从机FPGA的运行完成 
                                      //20200218修改：
                                      //出现MIMO启动后，从机FPGA中断丢失的情况，怀疑是延时不够，主机启动时从机仍未完成，延时由200us加到500us
                    }
                    
                    Ret = StartAllMod();
                    if(Ret != WT_OK)
                    {
                        StopSlave(Ret, ModType == DEV_TYPE_VSA ? CMD_START_VSA : CMD_START_VSG);
                        ClearMod(m_Mods.size());
                        return Ret;
                    }

                    if (!m_LocalStart)
                    {
                        Ret = m_ExtConn->Response(Ret);
                    }

                    if (Ret == WT_OK)
                    {
                        Ret = MimoStartFin();
                    }
                }
            }
            else
            {
                StopSlave(Ret, ModType == DEV_TYPE_VSA ? CMD_START_VSA : CMD_START_VSG);
                ClearMod(m_Mods.size());
            }
        }

        return Ret;
    }

    //*****************************************************************************
    // 暂停VSA/VSG业务，只停止硬件操作，软件状态保持不变
    // 参数[IN]: Conn : 发送命令的连接
    // 返回值: 成功或错误码
    //*****************************************************************************
    int Pause(Connector *Conn)
    {
        return Stop(Conn);
    }

    //*****************************************************************************
    // 停止VSA/VSG业务
    // 参数[IN]: Conn : 发送命令的连接
    // 返回值: 成功或错误码
    //*****************************************************************************
    int Stop(Connector *Conn)
    {
        int Ret = WT_OK;

        if (!Conn->IsLinkToSlave())
        {
            if (IsMIMOMaster())
            {
                Ret = TransmitMimoCmd();
                if (Ret == WT_OK)
                {
                    Conn->SetRsp(false);
                }
                else
                {
                    WTLog::Instance().LOGERR(Ret, "stop mimo device failed");
                }
            }
            SetStop();
            ClearFinAgent();
        }
        else
        {
            Ret = ProcMimoAck(Conn);
        }

        return Ret;
    }

    //*****************************************************************************
    // 查询硬件状态
    // 参数[IN]: Conn : 发送命令的连接
    //          Status: 从机返回的状态（连接是从机连接时）
    // 参数[OUT]: Status: 查询到的状态（连接是外部连接时）
    // 返回值: 成功或错误码
    //*****************************************************************************
    int QueryStatus(Connector *Conn, int &Status)
    {
        int Ret = WT_OK;
        if (DigModeLib::Instance().IsDigMode())
        {
            // DigitalLib处理多帧交互，主过程完成前，子过程可能存在处于完成状态
            // 所以内外层状态都为完成时，才确认完成状态，否则为ERROR或RUNNING
            Status = (m_RunStatus != MOD_RUNNING) ? GetDigLib().GetStatus(ModType) : WT_DIGTIAL_STATUS_RUNNING;
            if (Status == WT_DIGTIAL_STATUS_DONE)
            {
                Status = (m_RunStatus == MOD_RUN_FINISH) ? WT_DIGTIAL_STATUS_DONE
                                                         : ((m_RunStatus == MOD_RUNNING) ? WT_DIGTIAL_STATUS_RUNNING
                                                                                         : Status);
            }
            if (Status != WT_DIGTIAL_STATUS_DONE && Status != WT_DIGTIAL_STATUS_RUNNING && Status != WT_DIGTIAL_STATUS_STOP)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "+++QueryStatus Type=%d, m_RunStatus=%d, Status=%d\n", ModType, m_RunStatus, Status);
            }
            return WT_OK;
        }
        if (BroadcastVsg::Instance().IsBroadcastUser() && ModType == DEV_RES_VSG)
        {
            Status = BroadcastVsg::Instance().GetRunStatus();
            
            if(BroadcastVsg::Instance().QueryStatus() == WT_RX_TX_STATUS_WAIT_START)
            {
                Status = WT_RX_TX_STATE_RUNNING;

            }
        }
        else if (!Conn->IsLinkToSlave())
        {
            if (m_RunStatus != MOD_RUN_FINISH)
            {
                Status = GetRunStatus();
                return WT_OK;
            }

            // MIMO时需要查询从机
            if (IsMIMOMaster())
            {
                Ret = TransmitMimoCmd();
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "query mimo device failed");
                    return Ret;
                }

                Conn->SetRsp(false);
            }
            else
            {
                Status = WT_RX_TX_STATE_DONE;
            }
        }
        else
        {
            //有一个从机错误即可返回错误
            if (Status != WT_RX_TX_STATE_DONE)
            {
                Ret = m_ExtConn->Response(&Status, sizeof(int));
            }
            else
            {
                //所有从机正确时返回状态
                ProcMimoAck(Conn, false);
                Ret = Conn->GetCmdResult();
                if (Ret == WT_OK)
                {
                    if (IsMimoDone())
                    {
                        Ret = m_ExtConn->Response(&Status, sizeof(int));
                    }
                }
                else
                {
                    m_ExtConn->Response(Ret);
                }
            }
        }

        return Ret;
    }

    //获取当前业务对象已申请到的资源
    std::vector<ModInfo> *GetModInfo(void) { return &m_Mods; }

    //*****************************************************************************
    // 申请硬件单元并配置硬件参数
    // 参数[IN]: NeedStart : 是否需要启动
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AllocSetMod(bool NeedStart)
    {
        int Mask = m_Param[0].GetModMask();

        int Ret = AllocMod(Mask);
        if (Ret == WT_OK)
        {
            Ret = SetParamToMod(NeedStart);
        }

        return Ret;
    }

    //使用多个模块时第一个模块为主，其他为从，需要先启动从，所以反向迭代
    int StartAllMod(int Index = -1)
    {
        int Flag = DevLib::Instance().GetOccupyFlag();
        m_Exclude |= Flag;

        int Ret = WT_OK;

        if (DigModeLib::Instance().IsDigMode())
        {
            if (!GetDigConfig())
            {
                WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "dig is not configured");
                return WT_MOD_NUM_ERROR;
            }

            Ret = StartModDig();
            if (Ret == WT_OK)
            {
                m_RunStatus = MOD_RUNNING;
            }
            return Ret;
        }

        if (m_Param[0].IsAC8080() && m_Mods.size() < 2)
        {
            WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "80+80 mod num less than 2");
            return WT_MOD_NUM_ERROR;
        }

        if (Index == -1)
		{
		    //确认所有单元已成功配置
		    for (auto &Mod : m_Mods)
		    {
		        if (!Mod.IsConfig)
		        {
		            WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Mod is not configured");
		            return WT_MOD_NUM_ERROR;
		        }
		    }

            //使用多个模块时第一个模块为主，其他为从，需要先启动从，所以反向迭代
            for (auto iter = m_Mods.rbegin(); iter != m_Mods.rend(); iter++)
            {
                iter->Status = WT_RX_TX_STATE_RUNNING;

                Ret = StartMod(iter->ModId);
                if (Ret != WT_OK)
                {
                    iter->Status = WT_RX_TX_STATE_ERR_DONE;
                    WTLog::Instance().LOGERR(Ret, "start module failed");
                    break;
                }

		        //80+80双端口模式时，从机使用第二个端口
		        if (!m_Param[0].IsDualPortMode() || iter->IsMaster)
		        {
		            iter->RFPort = m_Param[0].RFPort;
		        }
		        else
		        {
		            iter->RFPort = m_Param[0].RFPort2;
		        }
		        DevMgr::Instance().PortLedOn(ModType, iter->RFPort);
		        usleep(2);
            }
        }
        else
        {
            do
            {
				if (!m_Mods[Index].IsConfig)
		        {
		            WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Mod is not configured");
		            return WT_MOD_NUM_ERROR;
		        }
			
                m_Mods[Index].Status = WT_RX_TX_STATE_RUNNING;
                Ret = StartMod(m_Mods[Index].ModId);
                if (Ret != WT_OK)
                {
                    m_Mods[Index].Status = WT_RX_TX_STATE_ERR_DONE;
                    WTLog::Instance().LOGERR(Ret, "start module failed");
                    break;
                }

	             //80+80双端口模式时，从机使用第二个端口
	            if (!m_Param[0].IsDualPortMode() || m_Mods[Index].IsMaster)
	            {
	                m_Mods[Index].RFPort = m_Param[0].RFPort;
	            }
	            else
	            {
	                m_Mods[Index].RFPort = m_Param[0].RFPort2;
	            }
	            DevMgr::Instance().PortLedOn(ModType, m_Mods[Index].RFPort);
                usleep(2);
            } while (0);
        }

        if (Ret == WT_OK)
        {
            m_RunStatus = MOD_RUNNING;
            m_FinCnt = 0;
        }

        return Ret;
    }    

    //*****************************************************************************
    // 停止正在运行的模块
    // 参数[OUT]: IsRunning : 模块是否正在运行
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StopRunningMod()
    {
        bool IsRunning;
        return StopRunningMod(IsRunning);
    }
    int StopRunningMod(bool &IsRunning)
    {
        int Ret = WT_OK;
        IsRunning = false;
        m_RunStatus = MOD_NOT_RUNNING;
        if (DigModeLib::Instance().IsDigMode())
        {
            if (GetDigLib().GetStatus(ModType) == WT_DIGTIAL_STATUS_RUNNING)
            {
                IsRunning = true;
            }
            Ret = GetDigLib().Stop(ModType);
        }
        else
        {
            for (auto &Mod : m_Mods)
            {
                if (Mod.Status == WT_RX_TX_STATE_RUNNING)
                {
                    IsRunning = true;
                    Ret = StopMod(Mod);
                    if (Ret != WT_OK)
                    {
                        WTLog::Instance().LOGERR(Ret, "stop module failed");
                        continue;
                    }
                }
            }
        }
        return Ret;
    }

    //*****************************************************************************
    // 将参数配置到硬件，并根据需要启动硬件
    // 参数[IN]: NeedStart : 配置完成后是否需要重新启动
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetParamToMod(bool NeedStart)
    {
        int Ret = SetMod();
        if(!DigModeLib::Instance().IsDigMode())
        {
            for (auto &Mod : m_Mods)
            {
                Mod.IsConfig = Ret == WT_OK;
            }
        }

        if (Ret == WT_OK && NeedStart)
        {
            Ret = StartAllMod();
        }

        return Ret;
    }

    //获取当前业务对象运行状态
    virtual int GetRunStatus(void)
    {
        return m_RunStatus > 0 || m_RunStatus == MOD_NOT_RUNNING
               ? WT_RX_TX_STATE_ERR_DONE : WT_RX_TX_STATE_RUNNING;
    }

    //设置当前业务对象运行状态标志
    int SetRunStatusFlag(int Status){return m_RunStatus = Status;}

    //返回当前业务对象运行状态标志
    int GetRunStatusFlag() { return m_RunStatus; }

    //设置TB模式测试标准
    void SetTBTMode(int Mode) { m_TBTMode |= 0x1u << Mode; }

    //清除TB模式测试标准
    void ClearTBTMode() { m_TBTMode = 0; }

    //是否正在进行TB模式测试
    int IsTBTMode()
    {
        return m_TBTMode ? true : false;
    }
    int IsTBTApMode()
    {
        return (m_TBTMode & (0x1u << TBT_MODE_AP)) ? true : false;
    }
    int IsTBTStaMode()
    {
        return (m_TBTMode & (0x1u << TBT_MODE_STA)) ? true : false;
    }

    //设置数字IQ模式参数配置状态
    void SetDigConfig(int Config) { m_IsDigConfig = Config; }

    // 设置数字IQ模式小区通道列表
    void SetChannelIdList(const char *List, int count)
    {
        m_DigChanList.clear();
        for (int i = 0; i < count; i++)
        {
            //WTLog::Instance().WriteLog(LOG_DEBUG, "ModType%d SetChannelIdList index%d = %d\n", ModType, i, List[i]);
            if (List[i] >= 0 && List[i] < count)
            {
                m_DigChanList.push_back(List[i]);
            }
        }
    }

    //获取数字IQ模式参数配置状态
    int GetDigConfig() { return m_IsDigConfig; }

    //停止当前运行的单元
    int StopMod(ModInfo &Mod)
    {
        // ListMode 运行时断链清除ListMode状态
        if (m_ListModeRun && ModType == DEV_TYPE_VSA)
        {
            DevLib::Instance().SetListModeStatus(Mod.ModId, DEV_TYPE_VSA, false);
        }
        if (ModType == DEV_TYPE_VSG)
        {
            DevLib::Instance().SetListModeStatus(Mod.ModId, DEV_TYPE_VSG, false);
        }

        int Ret = StopDev<ModType>(Mod.ModId);
        DevMgr::Instance().PortLedOff(Mod.RFPort, true);
        Mod.IsConfig = false;
        Mod.Status = WT_RX_TX_STATE_ERR_DONE;
        ClearTBTMode();
        return Ret;
    }

    void SetTBPairMod(int PairModId)
    {
        if ((IsTBTStaMode() || m_TBTConf.SlaveTrigSel) && (GetDevMode() == DEVICE_MODE_MIMO_SINGLE_SLAVE))
        {
            m_TBTPairModId = PairModId / 2 + ((PairModId % 2) ? 0 : 1);
        }
        else
        {
            m_TBTPairModId = PairModId;
        }
    }

    //检查其他参数是否符合，目前监测了vsa宽频谱下，设置的频点是否在范围内
    virtual int CheckParamValid(void)
    {
        return true;
    }

    int SetDevLOMode(Connector *Conn, int ModId, int LOMode)
    {
        int Ret = WT_OK;
        if (!Conn->IsLinkToSlave())
        {
            if (IsMIMOMaster())
            {
                Ret = TransmitMimoCmd();
                if (Ret == WT_OK)
                {
                    Conn->SetRsp(false);
                }
                else
                {
                    WTLog::Instance().LOGERR(Ret, "SetDevLOMode TransmitMimoCmd failed");
                }
            }
            Ret = DevLib::Instance().SetLOComMode(ModId, LOMode);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "DevLib::Instance().SetLOComMode failed");
                Conn->SetRsp(true);
            }
        }
        else
        {
            ProcMimoAck(Conn);
        }
        return Ret;
    }
    //是否需要清除平均数据
    bool IsNeedClearAvgData()
    {
        return m_NeedClearAvgData;
    }

    int TransDacCode(double Value)
    {
        if (Value >= 0)
        {
            Value = Value > 1.0 ? 1.0 : Value;
            return (int)(Value * MAX_DAC_CODE + 0.5);
        }
        else
        {
            Value = Value < -1.0 ? -1.0 : Value;
            return (int)(Value * MAX_DAC_CODE - 0.5);
        }
    }

    int GetDigPacketCnt()
    {
        return GetDigLib().GetPacketCnt(ModType);
    }
protected:

    //检测是否已配置的参数
    virtual int CheckTBTParam(int mode) = 0;

    //检测是否已配置的参数
    virtual int CheckCalParam(int mode) = 0;

    //配置参数给硬件模块
    virtual int SetMod(int Index = -1) = 0;


    //配置参数给硬件模块
    virtual int SetExtMode(int ModId) = 0;

    //启动硬件模块，需要VSA/VSG子类实现
    virtual int StartModDig() = 0;

    //启动硬件模块，需要VSA/VSG子类实现
    virtual int StartMod(int ModId) = 0;

    //MIMO从机操作完成后的处理，需要VSA/VSG子类实现
    virtual int MimoStartFin() = 0;

    std::string GetWaveDir(void) { return "/tmp/wave/"; }
    std::string GetLowWaveDir() { return "/tmp/low_wave/"; }
    
    inline int GetModIndex(int ModId)
    {
        int i = 0;
        for (i = 0; i < m_Mods.size(); i++)
        {
            if (m_Mods[i].ModId == ModId)
            {
                break;
            }
        }

        return i;
    }

    bool CheckTestTypeLic(int TestType)
    {
        int FreqFlag =0;
        int Ret = WT_OK;
        //非多机mimo或者是多机mimo主机时才检测测试类型；多机mimo时，从机不检测是否包含mimo license，主机一定要有mimo的license，从机可以没有也能辅助测试
        if(!(TEST_MULTI_MIMO != TestType || IsMIMOMaster()))
        {
            return 1;
        }
        switch (TestType)
        {
        case TEST_SELF_MIMO:
        case TEST_MULTI_MIMO:
            TestType = WT_WIFI_MIMO;
            break;

        case TEST_CMIMO:
            TestType = WT_CMIMO;
            break;

        case TEST_SWITCHED_MIMO:
            TestType = WT_SWITCHED_MIMO;
            break;

        case TEST_SISO:
            //TestType = WT_BAND_2_4G;  //频段license在CheckModParam中已判断，不需要单独在这里判断一遍
            //FreqFlag = 1;
            break;

        case TEST_DEVM:
            TestType = WT_DEVM;
            break;

        default:
            //TestType = WT_BAND_2_4G;  //频段license在CheckModParam中已判断，不需要单独在这里判断一遍
            //FreqFlag = 1;
            break;
        }

        
        if (FreqFlag == 1)
        {
            std::vector<WT_FREQ_BAND_E> FreqBandVertor;
            FreqBandVertor.push_back(WT_BAND_2_4G);
            Ret = License::Instance().CheckFreqBandLicItem(FreqBandVertor);
        }
        else
        {
            Ret = License::Instance().CheckBusinessLicItem((WT_PROT_E)TestType);
        }
        return Ret == WT_OK;
    }

    //**************************************************************************
    // VSA/VSG设备操作完成后的回掉函数，需要VSA/VSG子类实现
    // 参数[IN] : Id : 硬件模块ID
    //           Cnt : 已完成的硬件个数，比如80+80需要两个都完成才算完成
    // 返回值：true : 表示已使用完毕，false：模块仍需继续使用
    //**************************************************************************
    virtual bool ProcModFin(int Id, int Cnt) = 0;

    //因非独占连接TB模式下，需VSA/VSG都完成才能释放VSG资源，所以分别判断是否释放VSA/VSG资源。
    virtual void ClearMod(int NeedFreeCnt) = 0;

    // MIMO操作是否已经完成，即是否所有从机都已完成
    bool IsMimoDone(void) const { return m_DevMask == m_DevStaMask; }

    // 当前连接是否为独占连接
    bool IsExclude(void) const { return m_Exclude; }

    //当前设备是否为MIMO Master
    bool IsMIMOMaster(void) const { return !m_SlaveConn.empty(); }

    // 将收到的命令转发给MIMO从机
    int TransmitMimoCmd(void)
    {
        for (auto &Conn : m_SlaveConn)
        {
            Conn->ForwardCmd(m_ExtConn.get());
        }

        m_DevStaMask = 0;

        return WT_OK;
    }

    //*****************************************************************************
    // 收到从机响应后的处理，如果命令执行正确查看是否所有的从机都已完成，如果都完成则发送完成响应。如果有错误的则发送错误响应。
    // 参数[IN]: Conn : 命令连接
    //       Response : 当所有从机都正确返回时是否发送响应
    // 返回值: 成功或错误码
    //*****************************************************************************
    int ProcMimoAck(Connector *Conn, bool Response = true)
    {
        int Result = WT_OK;
        auto iter = m_SlaveConn.begin();

        //如果从机的ACK和外部连接命令不匹配则不处理
        if (!m_ExtConn->IsMatch(Conn))
        {
            return WT_OK;
        }

        //如果为从机的响应则需要等所有的从机都响应后才能发送响应
        for (int i = 0; i < m_SlaveConn.size(); i++, iter++)
        {
            if (*Conn == **iter)
            {
                if (Conn->GetCmdResult() == WT_OK)
                {
                    m_DevStaMask |= 1 << i;
                }
                else
                {
                    WTLog::Instance().LOGERR(Conn->GetCmdResult(), "set mimo failed");
                    if (Response)
                    {
                        Result = m_ExtConn->Response(Conn->GetCmdResult());
                    }
                }

                break;
            }
        }

        if (Response && IsMimoDone())
        {
            Result = m_ExtConn->Response(WT_OK);
        }

        return Result;
    }

    //获取判断设备是否作为从机运行
    int IsSlaveMode(const P &Param)
    {
        if (Param.Type == TEST_MULTI_MIMO)
        {
            if (IsMIMOMaster() || Param.SignalId == 0)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        return false;
    }

    int GetDevMode()
    {
        return GetDevMode(m_Param[0], 0);
    }

    //获取设备的运行模式,不包含8080转换
    int GetDevModeWithout8080Trans(const P &Param, int Index)
    {
        int Mode;

        if (Param.Type == TEST_MULTI_MIMO)
        {
            if (IsMIMOMaster())
            {
                if (!Param.IsAC8080())
                {
                    Mode = DEVICE_MODE_MIMO_MULTI_MASTER;
                }
                else
                {
                    Mode = m_Mods[Index].IsMaster ? DEVICE_MODE_MIMO_MULTI_MASTER_8080_MASTER
                        : DEVICE_MODE_MIMO_MULTI_MASTER_8080_SLAVE;
                }
            }
            else if (Param.SignalId == 0) //只有MIMO主机的signalid为0，如果没有从机则是1x1，设置为SISO模式
            {
                if (!Param.IsAC8080())
                {
                    Mode = DEVICE_MODE_SISO;
                }
                else
                {
                    Mode = m_Mods[Index].IsMaster ? DEVICE_MODE_80_80_MASTER : DEVICE_MODE_80_80_SLAVE;
                }
            }
            else if (!m_ExtConn->IsLocal()) //如果是本机内部的连接，则触发源为内部板间触发
            {
                if (!Param.IsAC8080())
                {
                    Mode = DEVICE_MODE_MIMO_MULTI_SLAVE;
                }
                else
                {
                    Mode = m_Mods[Index].IsMaster ? DEVICE_MODE_MIMO_MULTI_SLAVE_8080_MASTER
                        : DEVICE_MODE_MIMO_MULTI_SLAVE_8080_SLAVE;
                }
            }
            else
            {
                if (!Param.IsAC8080())
                {
                    Mode = DEVICE_MODE_MIMO_SINGLE_SLAVE;
                }
                else
                {
                    Mode = m_Mods[Index].IsMaster ? DEVICE_MODE_MIMO_MULTI_SLAVE_8080_MASTER
                        : DEVICE_MODE_MIMO_MULTI_SLAVE_8080_SLAVE;
                }
            }
        }
        else if (Param.Type == TEST_SELF_MIMO) //第一个为master，其他为slave
        {
            Mode = Index ? DEVICE_MODE_MIMO_SINGLE_SLAVE : DEVICE_MODE_MIMO_SINGLE_MASTER;
        }
        else if (Param.IsAC8080()) //80+80
        {
            if (m_Mods[Index].IsMaster)
            {
                Mode = DEVICE_MODE_80_80_MASTER;
            }
            else
            {
                Mode = DEVICE_MODE_80_80_SLAVE;
            }
        }
        else
        {
            Mode = DEVICE_MODE_SISO;
        }   
        return Mode;
    }

    //获取设备的运行模式
    int GetDevMode(const P &Param, int Index)
    {
        int Mode = GetDevModeWithout8080Trans(Param, Index);

        //双端口模式时，改为由80M MIMO来实现80+80
        if (Param.IsDualPortMode() && Is8080(Mode))
        {
            if (Param.Type == TEST_SELF_MIMO) //第一个为master，其他为slave
            {
                Mode = Index ? DEVICE_MODE_MIMO_SINGLE_SLAVE : DEVICE_MODE_MIMO_SINGLE_MASTER;
            }
            else if (Mode == DEVICE_MODE_80_80_MASTER || Mode == DEVICE_MODE_MIMO_MULTI_MASTER_8080_MASTER)
            {
                Mode = DEVICE_MODE_MIMO_MULTI_MASTER;
            }
            else
            {
                Mode = DEVICE_MODE_MIMO_MULTI_SLAVE;
            }
        }

        //80+80双端口模式或双射频参数模式AGC时,特殊处理。
        if (Param.Type == TEST_80_80M_AGC)
        {
            if(Param.IsDualPortMode())
            {
                Mode = DEVICE_MODE_SISO;  //80+80双端口模式,改为SISO，两个模式单独运行AGC，且按SISO模式取校准数据。
            }
            else if(Is8080Master(Mode))   //80+80双射频参数模式,两个模式单独运行AGC，但仍按80+80模式取校准数据。
            {
                Mode = DEVICE_MODE_DUL_RF_PARAM_AGC_MASTER;
            }
            else if(Is8080Slave(Mode))    //80+80双射频参数模式,两个模式单独运行AGC，但仍按80+80模式取校准数据。
            {
                Mode = DEVICE_MODE_DUL_RF_PARAM_AGC_SLAVE;
            }
        }        
        return Mode;
    }

    //创建路径
    void CreatePath(std::string &NeedCreatePath)
    {
        int iPos = 0;
        string Dir = "";
        string CurCreateDir = "";
        int Result;

        while (iPos >= 0)
        {
            iPos = NeedCreatePath.find('/');
            CurCreateDir = CurCreateDir + NeedCreatePath.substr(0, iPos);
            Dir = Dir + CurCreateDir;
            if (-1 == (Result = access(Dir.c_str(), 0)))   //该目录不存在
            {
                if (-1 == (Result = mkdir(Dir.c_str(), 0755)))     //创建目录
                {
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Create path error!" << endl;
                }
            }
            Dir = Dir + "/";
            NeedCreatePath = NeedCreatePath.substr(iPos + 1, NeedCreatePath.size());
            CurCreateDir = "";
        }
    }

    //创建文件，并将数据写入文件
    int SaveFile(const std::string &Name, const void *Data, int Len)
    {
        int Pos = Name.find_last_of("/");
        string DirPath = Name.substr(0,Pos);
        struct stat FileStat;
        std::ofstream Fs;

        if (!((stat(DirPath.c_str(), &FileStat) == 0) && S_ISDIR(FileStat.st_mode)))//若不存在相应目录
        {
            string NeedCreatePath = Name.substr(0, Pos);
            CreatePath(NeedCreatePath);         //没有相应目录则先创建目录
        }

        Fs.open(Name.c_str(), std::fstream::out | std::fstream::trunc);
        if (!Fs.is_open())
        {
            WTLog::Instance().LOGERR(WT_OPEN_FILE_FAILED, "Save Signal File Failed");
            return WT_OPEN_FILE_FAILED;
        }

        Fs.write((char *)Data, Len);    //文件写入数据内容
        Fs.close();

        return WT_OK;
    }

    //*****************************************************************************
    // 申请硬件单元
    // 参数[IN]: Mask : 申请指定的硬件单元掩码，0表示自动分配，否则分配mask中指定位的模块
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AllocMod(int Mask)
    {
        int Ret = WT_OK;

        if (0 == Mask)
        {
            Ret = ReSetModMask(Mask);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "Set Mod Mask Failed!");
                return Ret;
            }
        }

        //MIMO申请资源超时时间固定为1秒
        int TimeOutSec = m_Param[0].Type != TEST_MULTI_MIMO ? m_Param[0].AllocTimeout : 1;
        if (0 == Mask)  //RF为0，Mask还是为0，则需要动态分配
        {
            if (m_Param[0].Type != TEST_MULTI_MIMO)
            {
                Ret = DynamicAllocMod(TimeOutSec);
            }
            else
            {
                m_Mods.clear();     //mimo模式下设置rf为0，且mask为0时，清空资源列表
                Ret = WT_MODMASK_PORT_UNVALID;
            }
        }
        else
        {
            Ret = AllocSpecificMod(Mask, TimeOutSec);
        }

        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "Alloc Mod Failed!");
            return Ret;
        }

        if (m_Param[0].IsAC8080())
        {
            SetModMaster();  //80+80需要设置一个为主单元
        }

        return Ret;
    }

    //重新设置模块参数，如果模块正在运行需要先停止，然后配置再启动
    int ResetMod(void)
    {
        bool IsRunning = false;
        int Ret = StopRunningMod(IsRunning);
        if (Ret == WT_OK)
        {
            Ret = SetParamToMod(IsRunning);
        }

        return Ret;
    }

    // 当前操作流程的主从机结果存取接口
    void ClearCurFlowResult()
    {
        m_CurFlowResult = WT_OK;
    }

    void SetCurFlowResult(int ret)
    {
        if (m_CurFlowResult == WT_OK)
        {
            m_CurFlowResult = ret;
        }
    }

    int GetCurFlowResult(void)
    {
        return m_CurFlowResult;
    }

    //Port为0时，需要分配相应的单元
    int DynamicAllocMod(int TimeOutSec)
    {
        int Ret = WT_OK;
        int DevId = 0;

        if (m_Mods.empty())
        {
            Ret = DevMgr::Instance().AllocMod(ModType, m_Notify, TimeOutSec, m_ExtConn->GetExtConnKey(), DevId);
            if (Ret != WT_OK)
            {
                return Ret;
            }

            ModInfo Mod(DevId, ModType);
            m_Mods.push_back(std::move(Mod));
        }
        else
        {
            while (1 != m_Mods.size())    //若有多个元素，不断从尾到首删除直到一个
            {
                if (m_Mods.back().Status == WT_RX_TX_STATE_RUNNING)
                {
                    StopMod(m_Mods.back());
                }
                m_Mods.pop_back();
            }
        }

        return Ret;
    }

     //配置参数给硬件模块
    virtual void SetStop()
    {
        m_RunStatus = MOD_NOT_RUNNING;
        if (DigModeLib::Instance().IsDigMode())
        {
            GetDigLib().Stop(ModType);
            SetDigConfig(false);
        }
        else if (BroadcastVsg::Instance().IsBroadcastUser() && ModType == DEV_RES_VSG)
        {
            BroadcastVsg::Instance().Stop();
        }
        else
        {
            for (auto &Mod : m_Mods)
            {
                StopMod(Mod);
            }
            m_Mods.clear();
        }
    }
private:
    //*****************************************************************************
    // 硬件操作完成回掉函数，记录已完成的模块，然后触发完成事件
    // 参数[IN]: Type : 硬件类型
    //           DevId : VSA ID
    //           Status: 模块状态
    // 返回值: 无
    //*****************************************************************************
    void Complete(int Type, int DevId, int Status)
    {
        if(Status == WT_RX_TX_STATE_NEED_FREE)
        {
            SetStop();
            m_Mods.clear();
        }
        else if (Type == ModType)    //只有正在运行状态的模块才能通知，防止重复通知
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Complete Type%d, DevId%d, Status%d\n", Type, DevId, Status);
            m_ModStatus[DevId] = Status;
            m_FinMods |= (1 << DevId);
            m_FinishEv.send();
        }
    }

    //硬件操作完成异步事件回掉函数
    void HwOpFin(wtev::async &watcher, int revents)
    {
        (void)watcher;
        (void)revents;

        int Mask = 0;
        int DevId = 0;
        int NeedFreeCnt = 0;
       
        //此处需要循环处理，因为在处理过程可能中又有完成的模块
        while (m_FinMods)
        {
            DevId = 0;
            Mask = m_FinMods.exchange(0);  //从完成掩码中获取数据并将原数据清零
            while (Mask)
            {
                if (Mask & 1 << DevId)
                {
                    int Idx = GetModIndex(DevId);
                    if (m_Mods[Idx].Status != WT_RX_TX_STATE_RUNNING ||
                        Idx >= m_Mods.size())   //需确认Idx有效
                    {
                        Mask &= ~(1 << DevId);
                        continue;
                    }

                    m_Mods[Idx].Status = m_ModStatus[DevId];
                    int Port = m_Mods[Idx].RFPort; //理论上执行ProcModFin时可能会释放m_Mods，需提前获取端口
                    if (ProcModFin(DevId, ++m_FinCnt))
                    {
                        DevMgr::Instance().PortLedOff(Port);
                        NeedFreeCnt++;
                    }

                    Mask &= ~(1 << DevId);
                }

                DevId++;
            }
        }

        //因非独占连接TB模式下，需VSA/VSG都完成才能释放VSG资源，所以分别判断是否释放VSA/VSG资源。
        ClearMod(NeedFreeCnt);
    }

    int CheckModParam(P *ModParam)
    {
        //license检查是否支持指定的频率
        if (License::Instance().CheckFreq(ModParam->Freq + ModParam->FreqOffset) != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModParam->Freq=" << ModParam->Freq << std::endl;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "CheckFreq license not exist");
            return WT_LIC_NOT_EXIST;
        }

        //license检查是否支持指定的RF端口
        if (License::Instance().CheckRFPort(ModParam->RFPort) != WT_OK)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModParam->RFPort=" << ModParam->RFPort << std::endl;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "CheckRFPort license not exist");
            return WT_LIC_NOT_EXIST;
        }

        if (ModParam->IsDualPortMode())
        {
            //license检查是否支持指定的RF端口
            if (License::Instance().CheckRFPort(ModParam->RFPort2) != WT_OK)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModParam->RFPort2=" << ModParam->RFPort2 << std::endl;
                WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "CheckRFPort license not exist");
                return WT_LIC_NOT_EXIST;
            }
        }

        //license检查是否支持指定的测试类型
        if (!CheckTestTypeLic(ModParam->GetTestType()))
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ModParam->GetTestType()=" << ModParam->GetTestType() << std::endl;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "GetTestType license not exist");
            return WT_LIC_NOT_EXIST;
        }

        {
            int ModId = 0;
            int Version = VERSION_B;
            int TesterHwType = HW_WT428;
            DevLib::Instance().GetTesterHwType(TesterHwType);
            DevLib::Instance().GetModId(DEV_TYPE_VSA, ModParam->RFPort, ModId);
            DevLib::Instance().GetHardwareVersion(ModId, DEV_TYPE_VSA, Version);
            if (TesterHwType == HW_WT448 && Version == VERSION_A && !DigModeLib::Instance().IsDigMode())
            {
                ModParam->SamplingFreq = DEFAULT_SMAPLE_RATE;
            }
        }

        if (!ModParam->IsAgumentLegal()) //配置参数不合法
        {
            return WT_ARG_ERROR;
        }

        //160改为80+80实现
        if (ModParam->Is160() && !ModParam->IsAC8080())
        {
            if ((ModParam->Freq < RF_USE_MIX) || (ModParam->Freq > RF_FREQ_MAX))
            {
                return WT_ARG_ERROR;
            }

            ModParam->Freq = ModParam->Freq - 40 * 1e6;
            ModParam->Freq2 = ModParam->Freq + 80 * 1e6;
            ModParam->ExtGain2 = ModParam->ExtGain;
        }
        else
        {
            ModParam->Is160M = 0;
        }

        return WT_OK;
    }

    //mask=0时，重新设置Mask的值
    int ReSetModMask(int &Mask)
    {
        int ModNum = !m_Param[0].IsAC8080() ? 1 : 2;
        int ModId = 0;
        int ModId2 = 0;
        if (ModNum > DevMgr::Instance().GetModNum(ModType))
        {
            return WT_NO_ENOUGH_MODS;
        }
        if (1 == ModNum)
        {
            if (0 == m_Param[0].RFPort)
            {
                Mask = 0;
            }
            else
            {
                DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort, ModId);
                Mask = 1 << ModId;
            }
        }
        else    //80+80需要两个单元
        {
            if (0 == m_Param[0].RFPort)
            {
                if (m_Param[0].IsDualPortMode() && m_Param[0].RFPort2 != 0)
                {
                    //8080双端口模式:Port==0,RFPort2!=0时，先确定RFPort2的ModId,Port对应同单元板上的另一个MOD
                    DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort2, ModId);
                    Mask = 3 << ((ModId / MOD_TYPE_COUNT) * MOD_TYPE_COUNT);
                }
                else
                {
                    //8080双端口模式:Port==0,RFPort2==0时，强制分配模块0和模块1.
                    //非双端口模式:Port==0时，同样处理。
                    Mask = 3;                                                   
                }
            }
            else
            {
                if (m_Param[0].IsDualPortMode() && m_Param[0].RFPort2 != 0)
                {
                    //8080双端口模式:Port!=0,RFPort2!=0时，按各自端口分配ModId
                    DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort, ModId);
                    Mask = 1 << ModId;
                    DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort2, ModId2);
                    if (ModId2 == ModId)
                    {
                        WTLog::Instance().LOGERR(WT_ARG_ERROR, "80+80 Dul Mode cannt use same unit!");
                        return WT_ARG_ERROR;
                    }
                    Mask |= 1 << ModId2;
                }
                else                                                            
                {
                    //8080双端口模式:Port!=0,RFPort2==0时，先确定RFPort的ModId,Port2对应同单元板上的另一个MOD
                    //非双端口模式:Port!=0时，同样处理。
                    DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort, ModId);
                    Mask = 3 << ((ModId / MOD_TYPE_COUNT) * MOD_TYPE_COUNT);
                }
            }
        }
        return WT_OK;
    }

    //根据mask获取指定的单元
    int AllocSpecificMod(int Mask, int TimeOutSec)
    {
        //查找看那些已申请的则不用再申请，不需要的则释放掉
        for (auto iter = m_Mods.begin(); iter != m_Mods.end();)
        {
            if ((Mask & (1 << iter->ModId)) == 0)
            {
                if (iter->Status == WT_RX_TX_STATE_RUNNING)
                {
                    StopMod(*iter);
                }

                iter = m_Mods.erase(iter);
            }
            else
            {
                Mask &= ~(1 << iter->ModId);
                iter++;
            }
        }

        int i = 0;
        int DevId = 0;
        int Ret = WT_OK;
        while (Mask)
        {
            if (Mask & 1 << i)
            {
                Ret = DevMgr::Instance().AllocMod(ModType, m_Notify, TimeOutSec, m_ExtConn->GetExtConnKey(), DevId, i);
                if (Ret != WT_OK)
                {
                    break;
                }

                ModInfo Mod(DevId, ModType);
                m_Mods.push_back(std::move(Mod));
                Mask &= ~(1 << i);
            }

            i++;
        }

        //非独占连接模式下失败时需要释放已申请的模块
        if (Ret != WT_OK)
        {
            m_Mods.clear();
        }

        return Ret;
    }

    //申请的两个单元中，80+80时设置一个为主单元
    void SetModMaster()
    {
        int ModId = 0;
        //无论哪种情况，都以RFPort对应的单元为主。
        if (0 != m_Param[0].RFPort)
        {
            DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort, ModId);
        }
        else
        {
            if (m_Param[0].IsDualPortMode() && m_Param[0].RFPort2 != 0)
            {
                //当双端口模式且RFPort==0，RFPort2!=0时，需先确定Port2的ModId，Port对应同单元板上的另一个MOD
                DevLib::Instance().GetModId(static_cast<WT_DEV_TYPE>(ModType), m_Param[0].RFPort2, ModId);
                ModId = ModId % MOD_TYPE_COUNT ? ModId - 1 : ModId + 1;     //取得对应同单元板上的另一个MOD
            }
            else
            {
                //当双端口模式且RFPort==0，RFPort2==0时，根据MasterMode指定主MOD。
                ModId = m_Param[0].GetMasterMode() + (m_Mods[0].ModId / MOD_TYPE_COUNT) * MOD_TYPE_COUNT;
            }
        }

        for (int i = 0; i < m_Mods.size(); i++)
        {
            if (ModId == m_Mods[i].ModId)
            {
                m_Mods[i].IsMaster = true;
            }
            else
            {
                m_Mods[i].IsMaster = false;
            }
        }

        //确保第一个单元为主单元
        if (!m_Mods[0].IsMaster)
        {
            std::swap(m_Mods[0].ModId, m_Mods[1].ModId);
            std::swap(m_Mods[0].IsMaster, m_Mods[1].IsMaster);
        }
    }

private:
    wtev::async m_FinishEv;              //硬件操作完成异步事件
    wtev::timer m_TempEv;                //温度补偿定时器

protected:
    std::shared_ptr<Connector> m_ExtConn; //外部连接
    std::shared_ptr<DigitalLib> m_DigLib; //DIG业务单元
    int m_Exclude;                        //是否为独占连接
    bool m_Record = false;                //是否启动录制功能
    bool m_LocalStart = false;            //是否为本地启动，VSA多次采集时使用
    bool m_ModRunning = false;            //模块正在运行
    int m_RunStatus = MOD_NOT_RUNNING;    //运行状态，大于0表示运行错误码

    std::list<std::shared_ptr<Connector>> m_SlaveConn; //与MIMO从机连接的socket
    int m_DevMask = 0;                    //当前的MIMO从机掩码
    int m_DevStaMask = 0;                 //MIMO从机命令状态掩码，若对从机发送命令后收到正确回应则将相应的bit位置1
    int m_CurFlowResult = 0;              //记录MIMO主从机操作结果，成功时为WT_OK，任一设备(任何单元)返回失败后，记录其错误码.
                                          // 理论上一条新命令执行的时候设置为WT_OK, 命令执行完(命令发送ACK)后也应该设置为WT_OK

    int m_StopSlave = 0;                  //是否已进行过StopSlave操作
    int m_StopSlaveReason = WT_OK;        //导致StopSlave的原因
    int m_DevStaMaskStopSlave = 0;        //StopSlave的从机命令状态掩码。

    int m_ParamNum = 0;                   //当前的参数个数
    int m_MaxParamNum = 0;                //参数buffer区可大小
    std::unique_ptr<P[]> m_Param;         //当前参数
    std::vector<int> m_DigChanList;       //数字IQ小区channel id列表
    bool m_NeedClearAvgData = true;              //是否需要清除平均数据

    //TODO：只用于上下位机交流
    std::unique_ptr<int[][MAX_BUSINESS_NUM]> AnalogIQModeList;    //模拟IQ状态
    std::atomic<int> m_ModStatus[4];      //模块硬件状态
    std::vector<ModInfo> m_Mods;          //正在使用的模块
    std::atomic<int> m_FinMods;           //操作已完成的模块掩码

    NotifyFunc m_Notify;                  //硬件操作完成后的通知函数
    std::string m_CurDir;                 //当前目录
    std::unique_ptr<SigFile> m_SigFile;   //信号文件读取缓存

    std::function<bool(int)> m_FinAgent;  //VSA/VSG完成后的回掉函数
    int m_FinCnt = 0;                     //完成硬件操作的模块计数

    int m_TBTMode = 0;                    //当前是否正在进行TBT模式测试，仅在TBT模式启动测试后，完成或停止测试前，设置为1.
                                          //m_TBTMode BIT0:TBT_AP, BIT1:TBT_STA
    int m_TBTPairModId;                   //TBT模式配对的单元，VSG对象记录配对的VSA单元， 反之VSA对象记录配对的VSG单元
    TBTConfig m_TBTConf;                  //TBT模式相关的配置参数

    int m_IsDigConfig = false;            //数字IQ模式是否已经配置过
    bool m_ListModeRun = false;           //List Mode 启动运行标记
    bool m_ListEnable = false;            //List Mod使能标记
};

#endif
