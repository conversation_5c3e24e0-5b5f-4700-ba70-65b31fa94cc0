//*****************************************************************************
// File: virtual_addr_list.c
// Describe:以虚拟地址保存移位寄存器的值
// Author：mayongfeng
// Date: 2024.08.15
//*****************************************************************************
#include "wtdefine.h"

#include "virtual_addr_list.h"
#include <linux/printk.h>
#include <linux/string.h>
#include <linux/spinlock.h>

static int VirtualSwitchMask[2][9][5] =
    {
        {
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x0000FFFF, 0x000000FF, 0x00000000, 0x00000000, 0x00000000},
            {0xFFFF00FF, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x000000FF, 0x000000A4, 0xFFFF0000, 0x00000000, 0x00000000},
            {0x000000FF, 0x000000A4, 0x0000FFFF, 0x00000000, 0x00000000},
            {0x000000FF, 0x000000C8, 0x00000000, 0xFFFF0000, 0x00000000},
            {0x000000FF, 0x000000C8, 0x00000000, 0x0000FFFF, 0x00000000},
            {0x000000FF, 0x000000D0, 0x00000000, 0x00000000, 0x0000FFFF},
            {0x000000FF, 0x000000D0, 0x00000000, 0x00000000, 0xFFFF0000},
        },
        {
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x0000FF00, 0x000000FF, 0x00000000, 0x00000000, 0x00000000},
            {0xFFFF0000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x00000000, 0x00000000, 0xFFFF0000, 0x00000000, 0x00000000},
            {0x00000000, 0x00000000, 0x0000FFFF, 0x00000000, 0x00000000},
            {0x00000000, 0x00000000, 0x00000000, 0xFFFF0000, 0x00000000},
            {0x00000000, 0x00000000, 0x00000000, 0x0000FFFF, 0x00000000},
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x0000FFFF},
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFF0000},
        }};

//*****************************************************************************
// 功能: 直接读IO内存对应的virtual寄存器数据，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  RegAddr：寄存器地址
// 返回值：寄存器数据
//*****************************************************************************
int wt_read_direct_reg_virtual(struct dev_virtual_addr_struct dev_virtual_addr, int RegAddr)
{
    int value = 0;
    value = dev_virtual_addr.VirtualDrectRegList[RegAddr];
    //printk("wt_read_direct_reg_virtual 0x%x = %d\n", RegAddr, value);
    return value;
}

//*****************************************************************************
// 功能: 直接写IO内存对应的virtual寄存器数据，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  RegAddr：寄存器地址 Value：寄存器数据
// 返回值：成功为0  失败为-1
//*****************************************************************************
void wt_write_direct_reg_virtual(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value)
{
    dev_virtual_addr->VirtualDrectRegList[RegAddr] = Value;
    //printk("wt_write_direct_reg_virtual 0x%x = %d\n", RegAddr, Value);
}

int wt_read_HMC833_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index)
{
    int value = 0;
    value = dev_virtual_addr.HMC833ConfigList[Index];
    return value;
}

int wt_get_HMC833_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.HMC833ConfigCount;
}

void wt_set_HMC833_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count)
{
    dev_virtual_addr->HMC833ConfigCount = count;
}

void wt_write_HMC833_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value)
{
    // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
    dev_virtual_addr->HMC833ConfigList[dev_virtual_addr->HMC833ConfigCount] = ((RegAddr & 0x3F) << 25) + ((Value & 0xffffff) << 1);
    dev_virtual_addr->HMC833ConfigCount++;
}

int wt_read_LMX2594_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index)
{
    int value = 0;
    value = dev_virtual_addr.LMX2594ConfigList[Index];
    return value;
}

void wt_write_LMX2594_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value)
{
    //bit0_15:data, bit16_22:addr, bit23:0w,1r
    dev_virtual_addr->LMX2594ConfigList[dev_virtual_addr->LMX2594ConfigCount] = ((RegAddr & 0x7f) << 16) + (Value & 0xffff);;
    dev_virtual_addr->LMX2594ConfigCount++;
}

int wt_get_LMX2594_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.LMX2594ConfigCount;
}
void wt_set_LMX2594_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count)
{
    dev_virtual_addr->LMX2594ConfigCount = count;
}

int wt_read_AdcOrDac_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index)
{
    int value = 0;
    value = dev_virtual_addr.AdcOrDacConfigList[Index];
    return value;
}

void wt_write_AdcOrDac_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value)
{    //bit0_7:data, bit8_22:addr, bit23:0w,1r
    dev_virtual_addr->AdcOrDacConfigList[dev_virtual_addr->AdcOrDacConfigCount] = ((RegAddr & 0x7fff) << 8) + (Value & 0xff);
    dev_virtual_addr->AdcOrDacConfigCount++;
}

int wt_get_AdcOrDac_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.AdcOrDacConfigCount;
}

void wt_set_AdcOrDac_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count)
{
    dev_virtual_addr->AdcOrDacConfigCount = count;
}

void SetVirtualAddrMode(struct dev_virtual_addr_struct *dev_virtual_addr, int mode)
{
    if(mode && dev_virtual_addr->VirtualAddrMode == 0)
    {
        spin_lock(&(dev_virtual_addr->dev_virtual_addr_lock));
    }
    else if (mode == 0 && dev_virtual_addr->VirtualAddrMode)
    {
        spin_unlock(&(dev_virtual_addr->dev_virtual_addr_lock));
    }
    dev_virtual_addr->VirtualAddrMode = mode;
}

int GetVirtualAddrMode(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.VirtualAddrMode;
}

void SetVirtualAddrSwPort(struct dev_virtual_addr_struct *dev_virtual_addr, int Port)
{
    dev_virtual_addr->Port = Port;
}

int GetVirtualAddrSwPort(struct dev_virtual_addr_struct dev_virtual_addr)

{
    return dev_virtual_addr.Port;
}

int GetVirtualSwitchMask(int type, int port, int RegId)
{
    return VirtualSwitchMask[type][port][RegId];
}

void InitVirtualAddr(struct dev_virtual_addr_struct *dev_virtual_addr)
{
    memset(dev_virtual_addr->VirtualDrectRegList, -1, sizeof(dev_virtual_addr->VirtualDrectRegList[0]) * VIRTUAL_DRECT_REG_LIST_LENGTH);
    memset(dev_virtual_addr->AdcOrDacConfigList, 0, sizeof(dev_virtual_addr->AdcOrDacConfigList[0]) * VIRTUAL_ADCORDAC_REG_LIST_LENGTH);
    memset(dev_virtual_addr->HMC833ConfigList, 0, sizeof(dev_virtual_addr->HMC833ConfigList[0]) * VIRTUAL_HMC833_REG_LIST_LENGTH);
    memset(dev_virtual_addr->LMX2594ConfigList, 0, sizeof(dev_virtual_addr->LMX2594ConfigList[0]) * VIRTUAL_LMX2594_REG_LIST_LENGTH);
    dev_virtual_addr->HMC833ConfigCount = 0;
    dev_virtual_addr->LMX2594ConfigCount = 0;
    dev_virtual_addr->AdcOrDacConfigCount = 0;
    dev_virtual_addr->Port = 0;
}

