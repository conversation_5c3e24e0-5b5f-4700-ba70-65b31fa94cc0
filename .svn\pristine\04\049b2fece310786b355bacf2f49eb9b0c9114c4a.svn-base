#ifndef __WT_DEV_VSG_H__
#define __WT_DEV_VSG_H__

#include "devbusiness.h"
#include "wt-calibration.h"

//VSG单元
class DevVsg : public DevBusiness
{
public:
    DevVsg(int HwVersion, Json::Value &ConfJson) : DevBusiness(DEV_TYPE_VSG, HwVersion, ConfJson, WT_TX_ATT_MODE) {}
    virtual ~DevVsg() {};

    int CheckDacStatus();

    int ResetDacStatus();

    virtual int SetRFPowerStatus(WT_SWITCH_STATUS Status);

    virtual int Start(int Mode = WT_START_MODE_NORMAL);
    //ListMode 只配置VSG_START
    virtual int StartList(int Mode = WT_START_MODE_NORMAL);    //ListMode配置多个参数
    int SetParamList(const std::vector<VSAConfigType> VSGConfigList,
                     std::vector<Tx_Parm> TXParmList, 
                     const std::vector<SeqTimeType> SeqTime, 
                     const std::vector<int> SyncParam, 
                     int Repet);

    virtual int Stop();

    virtual int GetStatus(void);

    virtual int Down(void);

    int GetGainParam(Tx_Gain_Parm &GainParm);

    int Finish(void);

    int SetParam(const VSGConfigType &VSGConfig, Tx_Parm &TXParm, int WorkPointMode = false);
    //ListMode配置多个参数
    int SetParamList(const std::vector<VSGConfigType> VSGConfigList,
                     std::vector<Tx_Parm> TXParmList, 
                     const std::vector<SeqTimeType> SeqTime, 
                     const std::vector<int> SyncParam, 
                     int Repet);

    int SetfastVsgPower(const double Power);

    int SetfastVsgFreq(const double Freq);

    int SetWorkPointParam(const VSGConfigType &VSGConfig, Tx_Parm &TXParm);

    int SetCalConfig(const Tx_Parm &TXParm);

    int VSGStartSendPN(void);

    int SetPNItem(const std::vector<RfPnItem> &PnItemVector, double ResamppleFreq = DEFAULT_SMAPLE_RATE);
    //ListMode配置多个参数
    int SetPNItemList(const std::vector<RfPnItem> &PnItemVector, const std::vector<double> ResamppleFreq, std::vector<SegRfPnItem> SegRfPnItemVector);

    int GetPNItem(std::vector<RfPnItem> &PnItemVector);

    int SetPNHead(PnItemHead &PNHead);

    int GetPNHead(PnItemHead &PNHead);

    virtual int SaveData(int Index);

    int SetGain(const Tx_Gain_Parm &TXGainParm);

    virtual int SetBand(double ModFreq, double MixFreq, WT_RF_MOD_BAND_E BandMod, WT_RF_MIX_BAND_E BandMIX);

    int SetFreqOutputPower(int Freq, int Power);

    int SetFreqAndGain(const VSGConfigType &VSGConfig, Tx_Parm &TXParm);

    int SetmGain(Tx_Gain_Parm TxGainParm);

    int SetFreq(double TXFreq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode);

    // 设置频率并且检查设置结果
    int SetFreqWithConfirm(double TXFreq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode);

    int SetBoostStatus(int Status);

    int WriteDACReg(int Addr, const int Data);

    int ReadDACReg(int Addr, int &Data);

    int DACInit(void);

    int GetDACVersion(int &DACVersion);

    int SetITuneGain(int ICode);

    int SetQTuneGain(int QCode);

    int SetDacGain(double &Remain);

    int SetDacIQGainCode(int ICode, int QCode);

    int SetIOffset(int IOffset);

    int SetQOffset(int QOffset);

    int SetTXDCOffset(int IOffset, int QOffset);

    int SetIfgCtrlEnable(int Enable);

    int SetIfgCtrlStatus(int Status);

    //VSG TBT_STA模式启动
    int TBTStaStart(int DevMode);

    int SetTBTStaParam(TBTStaParamType StaConfig);

    int SetDebugAtt(int Index, int Data);

    int GetDebugAtt(int Index, int &Data);

    int SetSwitchVsgCTL3(int RFPort, int Mode, int State, WT_SB_CONFIG_TYPE_E SBConfigType);
    int SetIfgCtrlMode(int Port, int State);

    int SetExtMode(ExtModeType ExtMode);
    //模拟IQ信号内/外链路切换开关
    int SetAnalogIQSW(int &AnalogIQSW);
    int GetAnalogIQSW(int &AnalogIQSW);
    //设置共模电压
    int SetCmVolt(double CmVolt);
    //初始化设置IQ模式
    int IQModeInit();

    int SetPNLoopParam(unsigned int Repeat, unsigned int IFG);
    //ListMode 获取Sequence Segment的进度
    int GetSeqProgress(int &Progress);
private:
    virtual int InitBaseBandMod();
    //ListMode 结束后复位仪器状态
    int ResetRundataFromCache();

    struct ExtHwParamType
    {
        int IfgParam[3];
        int DevmDelay;
    };
private:
    Tx_Gain_Parm m_GainParm;                                //ATT预设置的参数。
    Tx_Gain_Parm m_GainParmDebug;                           //ATT预设置的参数,用于器件生效时间检查DEBUG。
    int          m_VsgGapPowerDebug = GAP_POWER_NOT_DEBUG;  //Vsg Gap Power Ctrl调试
    ExtHwParamType m_ExtHwParam;
    int          m_DacNeedReset = false;;
    VSGConfigType m_VSGConfigCache;                         //缓存VSG配置
};

#endif
