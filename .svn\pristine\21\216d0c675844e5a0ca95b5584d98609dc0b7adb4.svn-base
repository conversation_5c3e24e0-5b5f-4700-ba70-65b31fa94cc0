//*****************************************************************************
//  File: wt_protocol.h
//  私有协议处理
//  Data: 2016.8.27
//****************************************************************************
#include <iostream>
#include <cstring>
#include <sys/time.h>

#include "wtprotocol.h"
#include "devtype.h"
#include "wtlog.h"
#include "wterror.h"
#include "license.h"
#include "conf.h"
#include "devlib.h"
#include "crypto.h"
#include "digitallib.h"
#include "dockerutils.h"

#include <vector>

using namespace std;
using namespace std::placeholders;

InterProt::InterProt() : WTProtocol()
{
    RegCmdPorc();
}

#define REG_CMD(code, fun) RegCmd(code, bind(&fun, this, _1, _2, _3))
void InterProt::RegCmdPorc()
{
    REG_CMD(CMD_RESET_CFG, InterProt::RestoreFactorySettings);              //恢复出厂设置
    REG_CMD(CMD_SET_DEVIP, InterProt::SetDeviceIP);                         //设备IP配置
    REG_CMD(CMD_SETUP_SUBDEV, InterProt::SetSubDeviceCfg);                  //子仪器划分配置
    REG_CMD(CMD_GET_SUBDEV_INFO, InterProt::GetSubDeviceCfg);               //子仪器配置查询
    REG_CMD(CMD_GET_DEV_INFO, InterProt::GetDeviceInfo);                    //设备基本信息查询
    REG_CMD(CMD_GET_HW_INFO, InterProt::GetHardwareInfo);                   //硬件详细信息查询
    REG_CMD(CMD_GET_LIC, InterProt::GetLicenseInfo);                        //License查询
    REG_CMD(CMD_GET_DEVICE_CUR_TIME, InterProt::GetDeviceCurTime);          //获取仪器当前系统时间
    REG_CMD(CMD_GET_LOG, InterProt::GetLog);                                //日志查询
    REG_CMD(CMD_UPDATA_FIRM, InterProt::UpdateFirmware);                    //固件升级
    REG_CMD(CMD_ROLLBACK_FIRM, InterProt::RollbackFirmware);                //固件回退
    REG_CMD(CMD_PRO_LIC_PACKAGE_TRAN, InterProt::ProLicPackageTran);        //解析下发的lic升级包
    REG_CMD(CMD_UPDATE_LIC_PACKAGE, InterProt::UpdateLicPackage);           //操作升级license包
    REG_CMD(CMD_UPDATE_LIC, InterProt::UpdateLicense);                      //License升级，已停用
    REG_CMD(CMD_RESTART_DEV, InterProt::RestartDevice);                     //重启设备
    REG_CMD(CMD_GET_SUB_ETH, InterProt::GetSubnet);                         //获取子网口配置信息
    REG_CMD(CMD_GET_SUB_ETH_LINK, InterProt::GetSubnetLink);                //获取子网口link信息
    REG_CMD(CMD_GET_IP_ADDRESS_TYPE, InterProt::GetIpAddressType);          //获取主网口ip地址类型
    REG_CMD(CMD_SET_SUB_ETH, InterProt::SetSubnet);                         //子网口配置
    REG_CMD(CMD_GET_ALL_GUI_VERSIONS, InterProt::GetAllGUIFilesVersions);   //获取全部GUI文件的版本信息
    REG_CMD(CMD_CRYPTOMEMORY_INIT, InterProt::CryptoMemoryInit);            //初始化加密芯片
    REG_CMD(CMD_SHUT_DOWN_DEV, InterProt::ShutDownDevice);                  //关机功能
    REG_CMD(CMD_DELETE_ALL_LIC_FILES, InterProt::DelAllLicenses);           //删除仪器的所有license
    REG_CMD(CMD_DELETE_SUB_NET_SETTING, InterProt::DelSubNetSetting);       //删除子网口配置
    REG_CMD(CMD_GET_DEV_VER_INFO, InterProt::GetDeviceVersionInfo);         //设备各种版本信息查询
    REG_CMD(CMD_GET_VOLT_INFO, InterProt::GetVoltInfo);                     //设备电压查询
    REG_CMD(CMD_DEV_RUN_MODE, InterProt::SetDevRunMode);                    //设备电压查询
    REG_CMD(CMD_GET_APP_LIST, InterProt::GetDockerAppList);                 //获取容器APP列表
    REG_CMD(CMD_SET_APP_STATUS, InterProt::SetDockerAppStatus);             //设置APP运行状态
    REG_CMD(CMD_SET_APP_DELETE, InterProt::RemoveDockerApp);                //删除一个APP
    REG_CMD(CMD_SET_SUB_NET_AUTO_NEG, InterProt::SetSubNetAutoNeg);         //配置子网口速率
    REG_CMD(CMD_GET_SUB_NET_AUTO_NEG, InterProt::GetSubNetAutoNeg);         //获取子网口速率开关状态

    // 自校准
    REG_CMD(CMD_START_IN_CAL, InterProt::StartInCal);
    REG_CMD(CMD_STOP_IN_CAL, InterProt::StopInCal);
    REG_CMD(CMD_QUERY_IN_CAL_PROCESS, InterProt::QueryInCalProcess);

    REG_CMD(CMD_TEST_CONNECT_STATUS, InterProt::TestConnectStatus);           //确认连接状态
}

int InterProt::SendAck(WRSocket &Sock, const CmdHeader *Header, int Ret)
{
    int Len;
    AckHeader Ack(Header, sizeof(Ret), Ret);

    Ret = Sock.Send(&Ack, sizeof(AckHeader), Len);
    if (Ret == WT_OK)
    {
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        return WT_SOCKET_CLOSED;
    }
}

int InterProt::SendAck(WRSocket &Sock, const CmdHeader *Header, int Ret, const void *Data, int Len)
{
    AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
    BufInfo Vec[2] = {{&Ack, sizeof(Ack)}, {const_cast<void *>(Data), Len}};

    Ret = Sock.Send(Vec, 2, Len);
    if (Ret == WT_OK)
    {
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        return WT_SOCKET_CLOSED;
    }
}

#define CmdDataLen (64*1024)
int InterProt::RestoreFactorySettings(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    (void)Data;

    WRSocket *pSock = static_cast<WRSocket *>(Arg);

    Ret = WTDeviceInfo::Instance().RestoreDeviceSetting();
    Ret = SendAck(*pSock, Header, Ret);
    return Ret;
}

int InterProt::SetDeviceIP(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    if (Header->Length <  (signed)(sizeof(char) * 16 * 3))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error!");
        return SendAck(*pSock, Header, WT_CMD_ERROR);
    }

    int Ret = m_BnsHandlerOj.SetDeviceInfoHandler(Data, Header->Length);
    return SendAck(*pSock, Header, Ret);
}

int InterProt::SetSubDeviceCfg(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    if (0 != (Header->Length % (signed)sizeof(WTConf::DevCfg)))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Size Error");
        return SendAck(*pSock, Header, WT_CMD_ERROR);
    }

    int Ret = m_BnsHandlerOj.SetSubDeviceCfgHandler(Data, Header->Length);
    return SendAck(*pSock, Header, Ret);
}

int InterProt::GetSubDeviceCfg(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    std::unique_ptr<char[]>Buff(new(std::nothrow) char[CmdDataLen]);
    (void)Data;

    if(Buff == NULL)
    {
        SendAck(*pSock, Header, WT_ALLOC_FAILED);
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "Buff alloc failed!");
        return WT_ALLOC_FAILED;
    }

    int Len = 0;
    int Ret = m_BnsHandlerOj.GetSubDeviceCfgHandler(Buff.get(), Len);
    if(Ret == WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret, Buff.get(), Len);
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }

    return Ret;
}

int InterProt::GetDeviceInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    (void)Data;

    //获取相应仪器的详细信息:设备名称，SN，固件版本号
    DeviceInfo DevInfo = WTDeviceInfo::Instance().GetDeviceDetailedInfo();
    Ret = SendAck(*pSock, Header, Ret, &DevInfo, sizeof(DeviceInfo));

    return Ret;
}

int InterProt::GetHardwareInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    char *Buff = new(std::nothrow) char[CmdDataLen];
    (void)Data;

    if(Buff == NULL)
    {
        SendAck(*pSock, Header, WT_ALLOC_FAILED);
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "Buff alloc failed!");
        return WT_ALLOC_FAILED;
    }

    int Len = 0;
    int Ret = m_BnsHandlerOj.GetHardwareInfoHandler(Buff, Len);
    Ret = SendAck(*pSock, Header, Ret, Buff, sizeof(Buff));
    if(Buff != NULL)
    {
        delete []Buff;
        Buff = NULL;
    }
    return Ret;
}

int InterProt::GetLicenseInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    (void)Data;

    //获取相应license的信息，保存在Data中
    vector<LicItemInfo> LicItemsInfo;
    int Len = 0;
    Ret = m_BnsHandlerOj.GetLicenseInfoHandler(LicItemsInfo, Len);
    if(Ret == WT_OK)
    {
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
        Ret = pSock->Send(&Ack, sizeof(AckHeader), Len);
        if(Ret == WT_OK)
        {
            for (int i = 0; i < (signed)LicItemsInfo.size(); i++)
            {
                Ret = pSock->Send(&LicItemsInfo[i], sizeof(LicItemInfo), Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        SendAck(*pSock, Header, Ret);
    }

    return Ret;
}

int InterProt::GetDeviceCurTime(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    TIME_TYPE NowTime;
    (void)Data;
    (void)Arg;

    Ret = Basefun::GetSysTime(&NowTime);
    if(Ret == WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret, &NowTime, sizeof(TIME_TYPE));
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }

    return Ret;
}

int InterProt::GetLog(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    if (Header->Length <  (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error!");
        return SendAck(*pSock, Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
    int Len = 0;

    vector<string> Record;

    Ret = m_BnsHandlerOj.GetLogHandler(Data, Record, Len);
    if(Ret == WT_OK)
    {
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
        Ret = pSock->Send(&Ack, sizeof(AckHeader), Len);

        if(Ret == WT_OK)
        {
            for (int i = 0; i < (signed)Record.size(); i++)
            {
                Ret = pSock->Send(Record[i].c_str(), Record[i].length(), Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }
    return Ret;
}

int InterProt::UpdateFirmware(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    if (Header->Length < MAX_NAME_SIZE)
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Header->Length < MAX_NAME_SIZE");
        return SendAck(*pSock, Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;

    // 尝试关闭掉server的自校准功能, 只是尝试
    //Ret = TunnelMgr::Instance().EnableRunInCal(Header, 0);

    //升级固件处理
#if DEBUG
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
    //先判断升级文件是否是小模块升级包，如果是，按小模块模式升级，否则按正常模式升级
    if ((Ret = Upgrade::Instance().ModuleUpdateHandler(Data, Header->Length)) == WT_UPGRADE_MODULE_CHECK_ERROE)
    {
        Ret = Upgrade::Instance().UpdateFirmwareHandler(Data, Header->Length);
    }
#if DEBUG
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "UpdateFirmware use time us: " << (End - start) << endl;
#endif
    Ret = SendAck(*pSock, Header, Ret);

    //  if(Ret == WT_OK)
    //  {
    //      Basefun::LinuxSystem("reboot");
    //  }
    return Ret;
}

int InterProt::RollbackFirmware(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    if (Header->Length < (signed)sizeof(int))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "Cmd Data Error!");
        return SendAck(*pSock, Header, WT_CMD_ERROR);
    }

    int Ret = WT_OK;
#if DEBUG
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
    Ret = m_BnsHandlerOj.RollbackFirmwareHandler(Data);
#if DEBUG
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RollBackFirmware use time us: " << (End - start) << endl;
#endif
    Ret = SendAck(*pSock, Header, Ret);
    //  if(Ret == WT_OK)
    //  {
    //      Basefun::LinuxSystem("reboot");
    //  }

    return Ret;
}

int InterProt::ProLicPackageTran(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    //下发license升级包并返回升级包中的license信息
#if DEBUG
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
    vector<LicItemInfo> LicItemsInfo;   //用于保存升级包中lic的信息
    Ret = Upgrade::Instance().ProLicensePackageHandler(Data, Header->Length, LicItemsInfo);
    if(Ret == WT_OK)
    {
        if(LicItemsInfo.size()!= 0)
        {
            int Len = sizeof(LicItemInfo) * LicItemsInfo.size();
            AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
            Ret = pSock->Send(&Ack, sizeof(AckHeader), Len);
            if(Ret == WT_OK)
            {
                for (int i = 0; i < (signed)LicItemsInfo.size(); i++)
                {
                    Ret = pSock->Send(&LicItemsInfo[i], sizeof(LicItemInfo), Len);
                    if (Ret != WT_OK)
                    {
                        return Ret;
                    }
                }
            }
        }

    }
#if DEBUG
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Updatelicense use time us: " << (End - start) << endl;
#endif

    return SendAck(*pSock, Header, Ret);
}

int InterProt::UpdateLicPackage(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    //升级lic升级包中的lic文件，操作错误时，返回详细的错误提示
#if DEBUG
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif

    char ErrorInfo[3000] = {0};//用于保存操作错误时返回的错误提示信息
    int InfoLen = 0;
    int Len = 0;
    Ret = Upgrade::Instance().UpdateLicPackageHandler(ErrorInfo, InfoLen);

    WTLog::Instance().WriteLog(LOG_DEBUG, "###Updatelic Ret=%x####\n",Ret);
    if(Ret != WT_OK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "########InfoLen >0###########################\n");
        if(InfoLen > 0)
        {
            AckHeader Ack(Header, sizeof(Ret) + InfoLen, Ret);
            Ret = pSock->Send(&Ack, sizeof(AckHeader), Len);
            if(Ret == WT_OK)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "######ErrorInfo =%s, InfoLen = %d###########\n",ErrorInfo, InfoLen);
                Ret = pSock->Send(ErrorInfo, InfoLen, Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        SendAck(*pSock, Header, Ret);
    }
#if DEBUG
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "UpdatelicensePackage use time us: " << (End - start) << endl;
#endif

    return Ret;
}

int InterProt::UpdateLicense(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    //升级license
#if DEBUG
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
    Ret = Upgrade::Instance().UpdateLicenseHandler(Data, Header->Length);
#if DEBUG
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Updatelicense use time us: " << (End - start) << endl;
#endif

    return SendAck(*pSock, Header, Ret);
}

int InterProt::RestartDevice(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    (void)Data;

    Ret = SendAck(*pSock, Header, Ret);

    DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_OFF);
    Basefun::LinuxSystem("sync");
    usleep(1000);
    Basefun::LinuxSystem("reboot -f");

    return Ret;
}

int InterProt::GetSubnet(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    char Buf[sizeof(SystemIPInfo)];
    int InfoSize = 0;
    if ((Ret = WTDeviceInfo::Instance().GetDeviceSubNetInfo(Buf, InfoSize)) == WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret, Buf, InfoSize);
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }
    return Ret;
}

int InterProt::GetSubnetLink(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    bool Buf[MAX_SUB_NET_NUM];
    int InfoSize = 0;
    if ((Ret = WTDeviceInfo::Instance().GetDeviceSubNetLink(Buf, InfoSize)) == WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret, Buf, InfoSize);
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }
    return Ret;
}

int InterProt::GetIpAddressType(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    bool IsDhcp = false;
    int InfoSize = 0;
    if ((Ret = WTDeviceInfo::Instance().GetDeviceIpAddressType(&IsDhcp, InfoSize)) == WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret, &IsDhcp, InfoSize);
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }
    return Ret;
}

int InterProt::SetSubnet(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    if (Header->Length / sizeof(IPType) >= sizeof(SystemIPInfo) / sizeof(IPInfo))
    {
        if(License::Instance().CheckBusinessLicItem(WT_SUB_NET) != WT_OK)
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(Ret, "WT_SUB_NET license not exist");
        }
        else
        {
            Ret = WTDeviceInfo::Instance().SetSubNetInfo((IPType *)Data, Header->Length / sizeof(IPType));
        }
        return SendAck(*pSock, Header, Ret);
    }
    WTLog::Instance().LOGERR(WT_CMD_ERROR, "Data Error");
    return SendAck(*pSock, Header, WT_CMD_ERROR);
}

int InterProt::GetAllGUIFilesVersions(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    (void)Data;

    //获取相应license的信息，保存在Data中
    vector<GUIVersion> GUIVersionInfo;
    int Len = 0;
    Ret = m_BnsHandlerOj.GetGUIVersionHander(GUIVersionInfo, Len);
    if(Ret == WT_OK)
    {
        AckHeader Ack(Header, sizeof(Ret) + Len, Ret);
        Ret = pSock->Send(&Ack, sizeof(AckHeader), Len);
        if(Ret == WT_OK)
        {
            for (int i = 0; i < (signed)GUIVersionInfo.size(); i++)
            {
                Ret = pSock->Send(&GUIVersionInfo[i], sizeof(GUIVersion), Len);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
        }
    }
    else
    {
        SendAck(*pSock, Header, Ret);
    }

    return Ret;
}

int InterProt::CryptoMemoryInit(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);

    if (Header->Length >= sizeof(CryptoMemInfoType) + 2 * sizeof(int))
    {
        int Ret = CryptoLib::Instance().CryptoMemInit(*((int *)Data), (WT_DEV_TYPE)(*((int *)Data + 1)), (CryptoMemInfoType*)((int *)Data + 2));
        return SendAck(*pSock, Header, Ret);
    }
    WTLog::Instance().LOGERR(WT_CMD_ERROR, "Data Error");
    return SendAck(*pSock, Header, WT_CMD_ERROR);
}

int InterProt::DelSubNetSetting(CmdHeader *Header, void *Data, void *Arg)
{
	WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    (void)Data;

	Ret = m_BnsHandlerOj.DeleteSubNet();
    Ret = SendAck(*pSock, Header, Ret);

    return Ret;
}

int InterProt::ShutDownDevice(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    (void)Data;

    Ret = SendAck(*pSock, Header, Ret);

    DevLib::Instance().SetLedStatus(LED_ALL, LED_STATUS_OFF);
    Basefun::LinuxSystem("init 0");

    return Ret;
}

int InterProt::DelAllLicenses(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    (void)Data;

    Ret = m_BnsHandlerOj.DeleteAllLicenses();
    if(Ret == WT_OK)
    {
        DevLib::Instance().SetErrorLed(LED_STATUS_ON);
    }
    Ret = SendAck(*pSock, Header, Ret);

    return Ret;
}

int InterProt::GetDeviceVersionInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    (void)Data;

    //获取仪器相关内容的版本信息，算法，校准等
    std::string VerInfo;
    WTDeviceInfo::Instance().GetDeviceVersionInfo(VerInfo);

    Ret = SendAck(*pSock, Header, Ret, VerInfo.c_str(), (int)VerInfo.length());

    return Ret;
}

int InterProt::StartInCal(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    (void)Data;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Manager InterProt::StartIncal\n");
    Ret = TunnelMgr::Instance().StartInCal(Header);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Manager InterProt::StartIncal end, Ret=%d\n", Ret);
    Ret = SendAck(*pSock, Header, Ret);

    return Ret;
}

int InterProt::StopInCal(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    (void)Data;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Manager InterProt::StopIncal\n");
    Ret = TunnelMgr::Instance().StopInCal(Header);
    WTLog::Instance().WriteLog(LOG_DEBUG, "Manager InterProt::StopIncal end, Ret=%d\n", Ret);
    Ret = SendAck(*pSock, Header, Ret);
    return Ret;
}


int InterProt::QueryInCalProcess(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    (void)Data;

    WTLog::Instance().WriteLog(LOG_DEBUG, "Manager InterProt::StopIncal\n");
    int Process = 0;
    //int Data[2] = {0};
    Ret = TunnelMgr::Instance().QueryInCalProcess(Header, Process);

    if (Ret != WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret);
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret, &Process, 4);
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "Manager InterProt::StopIncal end, Ret=%d Process=%d\n", Ret, Process);
    return Ret;
}

int InterProt::GetVoltInfo(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int DataLen = 0;
    int VoltCnt = 0;
    int Ret = WT_OK;
    Ret = DevLib::Instance().GetDevVoltCnt(VoltCnt, DataLen);
    std::unique_ptr<char[]>VoltBuf(new char[sizeof(int) + DataLen]);
    // TODO 获取仪器电压
    Ret = DevLib::Instance().GetDevVoltInfo(Data, VoltBuf.get() + sizeof(int), VoltCnt, DataLen);
    *reinterpret_cast<int *>(VoltBuf.get()) = VoltCnt;
    if(Ret == WT_OK)
    {
        //send 返回操作结果+电压信息
        Ret = SendAck(*pSock, Header, WT_OK, VoltBuf.get(), DataLen + sizeof(int));
    }
    else
    {
        //send 返回错误操作结果
        Ret = SendAck(*pSock, Header, Ret);
    }

    return Ret;
}

int InterProt::SetDevRunMode(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    int RunMode = *(int *)Data;
    if (RunMode > 0 && RunMode < TESTER_DIG_MODE_MAX)
    {
        DigModeLib::Instance().SetRunMode(RunMode);
    }
    else
    {
        Ret = WT_ARG_ERROR;
    }
    //send 返回错误操作结果
    Ret = SendAck(*pSock, Header, Ret);
    return Ret;
}

int InterProt::GetDockerAppList(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;

    vector<docker_app_info> arr;
    int resv = *(int *)Data;
    Ret = DockerMgr::Instance().QueryAppListEntry(resv, arr);
    if (Ret == WT_OK)
    {
        Ret = SendAck(*pSock, Header, Ret, arr.data(), sizeof(docker_app_info) * arr.size());
    }
    else
    {
        Ret = SendAck(*pSock, Header, Ret);
    }
    return Ret;
}

int InterProt::SetDockerAppStatus(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    Ret = DockerMgr::Instance().SetAppStatusEntry(Data);
    Ret = SendAck(*pSock, Header, Ret);
    return Ret;
}

int InterProt::RemoveDockerApp(CmdHeader *Header, void *Data, void *Arg)
{
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Ret = WT_OK;
    Ret = DockerMgr::Instance().RemoveAppEntry(Data);
    Ret = SendAck(*pSock, Header, Ret);
    return Ret;
}

int InterProt::TestConnectStatus(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    return SendAck(*static_cast<WRSocket *>(Arg), Header, WT_OK);
}

int InterProt::SetSubNetAutoNeg(CmdHeader *Header, void *Data, void *Arg)
{
    int Ret = WT_OK;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    do
    {
        if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448)
        {
            WTLog::Instance().LOGERR(WT_ARG_UNKNOW_PARAMETER, "448 not support sub net");
            Ret = WT_ARG_UNKNOW_PARAMETER;
            break;
        }

        if (Header->Length < sizeof(int))
        {
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            Ret = WT_CMD_ERROR;
            break;
        }

        if (*(int *)Data)
        {
            Basefun::LinuxSystem((WTConf::GetDir() + "/SetSubNetSpeed.sh autoneg_on").c_str());
        }
        else
        {
            Basefun::LinuxSystem((WTConf::GetDir() + "/SetSubNetSpeed.sh autoneg_off").c_str());
        }
    } while (0);
    return SendAck(*pSock, Header, Ret);
}

int InterProt::GetSubNetAutoNeg(CmdHeader *Header, void *Data, void *Arg)
{
    (void)Data;
    int Ret = WT_OK;
    WRSocket *pSock = static_cast<WRSocket *>(Arg);
    int Enable = 0;
    do
    {
        if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448)
        {
            WTLog::Instance().LOGERR(WT_ARG_UNKNOW_PARAMETER, "448 not support sub net");
            Ret = WT_ARG_UNKNOW_PARAMETER;
            break;
        }

        string Size = Basefun::shell_exec("ethtool eth10");
        if (Size.find("Auto-negotiation: on") != string::npos)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "autoneg on" << endl;
            Enable = 1;
        }
        else
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "autoneg off" << endl;
            Enable = 0;
        }
    } while (0);
    return SendAck(*pSock, Header, Ret, &Enable, 4);
}
