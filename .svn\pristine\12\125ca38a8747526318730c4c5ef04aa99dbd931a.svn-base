#!/usr/bin/expect

if {![file exists /usr/bin/expect]} {
	send_user "please:\"sudo apt install expect\"\n"
	exit 0
} elseif {$argc > 3 || $argc < 1} {
	send_user "Usage: $argv0 remote_ip \[path\] \[all\].\n\n"
	exit 0
}

set remote_ip [lindex $argv 0]
set password  "wt4@rd28\r"

if {$argc == 2 && [lindex $argv $argc-1] == "all"} {
	set path ""
} elseif {$argc >= 2} {
	set path /[lindex $argv 1]
} else {
	set path ""
}

if {[lindex $argv $argc-1] == "all"} {
	spawn rm -rf bin/fpga
	set file_list "bin run.sh"
	set remote_path $path
} else {
	set file_list "bin/WT-* bin/xdma.ko bin/libgeneral.so bin/libfilesecure.so"
	set remote_path $path/bin
}

set timeout 3
spawn bash -c "scp -r $file_list wt400@192.168.$remote_ip:/home/<USER>"
expect {
"*yes/no*" {send "yes\r";exp_continue}
"*password*" {send "wt4@rd28\r"}
}
set timeout 100
expect eof