#ifndef __FIFOLIB_H__
#define __FIFOLIB_H__

#include <fstream>
#include <string>
#include <memory>
#include <sstream>
#include <unistd.h>

#include "wterror.h"
#include "wtlog.h"

#define WT_ATTRIBUTE 0x654123

struct HeadData
{
    int m_Attribute;
    int m_DataUnitSize;
    int m_FifoSize;
    int m_CurPos;
    int m_Cnt;

    HeadData(const int &Size = 0) : m_FifoSize(Size)
    {
        m_Attribute = WT_ATTRIBUTE;
        m_DataUnitSize = 0;
        m_CurPos = 0;
        m_Cnt = 0;
    }
};

template <class SData>
class FifoLib
{
  public:
    FifoLib(const int &Size, const std::string &SaveFile) : m_File(SaveFile)
    {
        if (Size == 0 || sizeof(SData) == 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "Input param failed!");
            return;
        }

        if (access(m_File.c_str(), F_OK) != 0)
        {
            //如果文件不存在，则生成文件
            m_FStream.open(m_File, std::ios_base::out | std::fstream::binary);
            if (!m_FStream.is_open())
            {
                WTLog::Instance().LOGERR(WT_CONF_FILE_ERROR, "Open file failed!");
                return;
            }
            m_FStream.close();
        }
        else
        {
            //如果存在文件，则读取文件头。
            m_FStream.open(m_File, std::ios_base::in | std::fstream::binary);
            if (!m_FStream.is_open())
            {
                WTLog::Instance().LOGERR(WT_CONF_FILE_ERROR, "Open file failed!");
                return;
            }
            m_FStream.seekg(0, std::ios_base::beg);
            m_FStream.read(reinterpret_cast<char *>(&m_HeadData), sizeof(m_HeadData));
            m_FStream.close();
        }

        //判断旧数据合法性
        if (m_HeadData.m_FifoSize != Size || m_HeadData.m_DataUnitSize != sizeof(SData) || m_HeadData.m_Attribute != WT_ATTRIBUTE)
        {
            m_HeadData.m_DataUnitSize = sizeof(SData);
            m_HeadData.m_FifoSize = Size;
            m_HeadData.m_Attribute = WT_ATTRIBUTE;
            m_HeadData.m_CurPos = 0;
            m_HeadData.m_Cnt = 0;

            //重新初始化存储文件
            m_FStream.open(m_File, std::ios_base::out | std::fstream::binary);
            if (!m_FStream.is_open())
            {
                WTLog::Instance().LOGERR(WT_CONF_FILE_ERROR, "Open file failed!");
                return;
            }
            m_FStream.seekp(0, std::ios_base::beg);
            m_FStream.write(reinterpret_cast<char *>(&m_HeadData), sizeof(m_HeadData));
            m_FStream.close();
        }

        //读取之前保存的数据
        m_SData.reset(new (std::nothrow) SData[m_HeadData.m_FifoSize]);
        m_FStream.open(m_File, std::ios_base::in | std::fstream::binary);
        if (!m_FStream.is_open())
        {
            WTLog::Instance().LOGERR(WT_CONF_FILE_ERROR, "Open file failed!");
            return;
        }
        m_FStream.seekg(sizeof(m_HeadData), std::ios_base::beg);
        m_FStream.read(reinterpret_cast<char *>(m_SData.get()), m_HeadData.m_FifoSize * m_HeadData.m_DataUnitSize);
        m_FStream.close();
    }

    int Push(const SData &Data)
    {
        if (m_HeadData.m_FifoSize == 0 || m_HeadData.m_DataUnitSize == 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "Input param failed!");
            return WT_ARG_ERROR;
        }

        char *pAddr = reinterpret_cast<char *>(&m_SData[m_HeadData.m_CurPos]);
        memcpy(pAddr, &Data, m_HeadData.m_DataUnitSize); //更新数据到内存

        //保存数据到文件
        m_FStream.open(m_File, std::fstream::in | std::fstream::out | std::fstream::binary);
        if (!m_FStream.is_open())
        {
            WTLog::Instance().LOGERR(WT_CONF_FILE_ERROR, "Open file failed!");
            return WT_CONF_FILE_ERROR;
        }

        m_FStream.seekp(sizeof(m_HeadData) + m_HeadData.m_CurPos * m_HeadData.m_DataUnitSize, std::ios_base::beg);
        m_FStream.write(pAddr, m_HeadData.m_DataUnitSize);

        if (++m_HeadData.m_CurPos >= m_HeadData.m_FifoSize)
        {
            m_HeadData.m_CurPos = 0;
        }
        if (++m_HeadData.m_Cnt >= m_HeadData.m_FifoSize)
        {
            m_HeadData.m_Cnt = m_HeadData.m_FifoSize;
        }
        m_FStream.seekp(0, std::ios_base::beg);
        m_FStream.write(reinterpret_cast<char *>(&m_HeadData), sizeof(m_HeadData));

        m_FStream.flush();
        m_FStream.close();
        return WT_OK;
    }

    int GetCnt()
    {
        return m_HeadData.m_Cnt;
    }

    SData GetData(const int &Cnt)
    {
        int pos = m_HeadData.m_CurPos - (Cnt % m_HeadData.m_FifoSize) - 1;
        pos = pos < 0 ? pos + m_HeadData.m_FifoSize : pos;
        return m_SData[pos];
    }

    int GetAllData(char *Addr)
    {
        if (m_HeadData.m_CurPos >= m_HeadData.m_Cnt)
        {
            memcpy(Addr, &m_SData[m_HeadData.m_CurPos - m_HeadData.m_Cnt], m_HeadData.m_DataUnitSize * m_HeadData.m_Cnt);
        }
        else
        {
            memcpy(Addr, &m_SData[m_HeadData.m_FifoSize - m_HeadData.m_Cnt + m_HeadData.m_CurPos], m_HeadData.m_DataUnitSize * (m_HeadData.m_Cnt - m_HeadData.m_CurPos));
            if (m_HeadData.m_CurPos)
            {
                memcpy(Addr  + m_HeadData.m_DataUnitSize * (m_HeadData.m_Cnt - m_HeadData.m_CurPos) , &m_SData[0], m_HeadData.m_DataUnitSize * m_HeadData.m_CurPos);
            }
        }
        return m_HeadData.m_Cnt;
    }

  private:
  protected:
    HeadData m_HeadData;              //数据头信息
    std::unique_ptr<SData[]> m_SData; //数据指针

    std::fstream m_FStream; //文件流
    std::string m_File;     //保存的文件
};

#endif //__FIFOLIB_H__