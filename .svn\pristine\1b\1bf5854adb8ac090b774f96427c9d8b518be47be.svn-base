//*****************************************************************************
//  File: conf.h
//  配置文件解析
//  Data: 2016.7.21
//*****************************************************************************
#ifndef __WT_READ_SWITCH_H__
#define __WT_READ_SWITCH_H__

#include <fstream>
#include <string>
#include <sstream>
#include <functional>

#include "../wtypes.h"
#include "devtype.h"
#include "backplane.h"
using namespace std;

class SwitchCfg
{
public:
    // 根据版本好初始化与版本相关的数据
    void InitSwbCfg(int TesterType, int Version, int SpecSwitchFlag);

    //*****************************************************************************
    //获取SwitchCfg对象
    //参数[IN] : 无
    //返回值: SwitchCfg对象指针
    //*****************************************************************************
    static SwitchCfg &Instance(void);

    //*****************************************************************************
    //获取开关板Bit映射表的长度
    //参数[IN] : 无
    //返回值: SwitchCfg配置信息的长度
    //*****************************************************************************
    int GetSwitchBitMapSize(void)
    {
        return WT_SWITCH_REG_CTRL_BIT_COUNT_MAX * SWITCH_BIT_MAP_MAX * sizeof(unsigned int);
    }

    //*****************************************************************************
    //获取SwitchCfgSet地址
    //参数[IN] : 无
    //返回值: SwitchCfgSet地址
    //*****************************************************************************
    char *GetSwitchBitMapAddr(void)
    {
        return (char *)m_SwitchBitMap;
    }

private:
    //*****************************************************************************
    // 获取当前行对应的BIT INDEX
    // 参数[IN]: Line : 当前行
    // 返回值: 当前行对应的BIT INDEX
    //*****************************************************************************
    int GetSwitchBitIndex(const string &Line, int &RegIndex, int &BitIndex, const string (*SwitchBitName)[WT_SWITCH_REG_BIT_LENGTH]);

    SwitchCfg();

    ~SwitchCfg()
    {
    }

private:
    const string (*m_SwitchBitName)[WT_SWITCH_REG_BIT_LENGTH];

    unsigned int m_SwitchBitMap[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX][SWITCH_BIT_MAP_MAX] = {{{0}}};

    static const string WT448SwitchBitName_VA[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX];
    static const string SwitchBitName_VA[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH];

    static const string WT448SwitchBitName_VB[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX];
    static const string SwitchBitName_VB[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH];

    static const string WT428SwitchBitName_428VA_VB[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX];
    static const string SwitchBitName_428VA_VB[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH];

    static const string WT448SwitchBitName_VD[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX];
    static const string SwitchBitName_VD[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH];
    
    static const string WT418SwitchBitName_VA[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX];
    static const string WT428CSwitchBitName_VA[WT_SWITCH_REG_CTRL_BIT_COUNT_MAX];
    static const string SwitchBitName_418_VA[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH];
    static const string SwitchBitName_428C_VD[SWITCH_SW_CTRL_REG_MAX][WT_SWITCH_REG_BIT_LENGTH];
};

#endif
