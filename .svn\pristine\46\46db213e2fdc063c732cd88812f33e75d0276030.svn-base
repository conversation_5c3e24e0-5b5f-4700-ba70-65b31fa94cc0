#!/bin/bash

dir=$(dirname $(readlink -f $0))

function get_tester_type()
{
	SelectbuildType=0
	Makeclean=0
	DevType=" WT428 "
	echo "Select tester type ?"
	echo -e "\tQ|q: quit build"
	echo -e "\t1: build wt448"
	echo -e "\t2: build wt428"
	echo -e "\tc: make clean"
	echo -e "\tnull: build wt328ce"
	read -p "please enter the number you want to build:" index 
	case "$index" in
		1)
			TesterType=" DEVTYPE=WT448 DISABLE_SECURE=1"
			DevType=" WT448 "
			echo "Build WT448 disable file secure"
		;;
		2)
			TesterType=" DEVTYPE=WT428"
			DevType=" WT428 "
			echo "Build WT428 file secure"
		;;
		c)
			Makeclean=1
			echo "make clean"
		;;
		q|Q)
			exit 0
		;;
		*)
			TesterType=" DEVTYPE=WT328CE"
			DevType=" WT328CE "
			echo "Build WT328CE"
		;;
	esac
}

BuildTwice=0
function get_428_version_type()
{
	StreamType=" "
	SecureType=" "
	echo "Build debug or release ?"
	echo -e "\tQ|q: quit build"
	echo -e "\t1: build 428 mimo && (secure and dissecure)"
	echo -e "\t2: build 428 siso && secure"
	echo -e "\t3: build 428 siso && dissecure"
	echo -e "\t4: build 428 mimo && secure"
	echo -e "\t5: build 428 mimo && dissecure"
	echo -e "\tnull: build 428 siso && (secure and dissecure)"
	read -p "please enter the number you want to build:" index
	case "$index" in
		1)
			BuildTwice=1
			echo "Build 428 mimo && && (secure and dissecure)"
		;;
		2)
			StreamType=" SISO=1 "
			echo "Build 428 siso && secure"
		;;
		3)
			StreamType=" SISO=1 "
			SecureType=" DISABLE_SECURE=1 "
			echo "Build 428 siso && dissecure"
		;;
		4)
			echo "Build 428 mimo && secure"
		;;
		5)
			SecureType=" DISABLE_SECURE=1 "
			echo "Build 428 mimo && dissecure"
		;;
		q|Q)
			exit 0
		;;
		*)
			StreamType=" SISO=1 "
			BuildTwice=1
			echo "Build 428 siso && && (secure and dissecure)"
		;;
	esac
}

function get_build_type()
{
	BuildType=" "
	echo "Build debug or release ?"
	echo -e "\tQ|q: quit build"
	echo -e "\t1: build release"
	echo -e "\t2: build debug"
	echo -e "\tnull: build default"
	read -p "please enter the number you want to build:" index
	case "$index" in
		1)
			BuildType=" RELEASE=1 "
			echo "Build release version"
		;;
		2)
			BuildType=" DEBUG=1 "
			echo "Build debug version"
		;;
		q|Q)
			exit 0
		;;
		*)
			echo "Build default version"
		;;
	esac
}

function get_mod_type()
{
	BuildApi=0
	BuildFw=0
	echo "Build api or fw ?"
	echo -e "\tQ|q: quit build"
	echo -e "\t1: build api"
	echo -e "\t2: build fw"
	echo -e "\t3: not build, only package"
	echo -e "\tnull: build api and fw"
	read -p "please enter the number you want to build:" index
	case "$index" in
		1)
			BuildApi=1
			echo "Only Build api"
		;;
		2)
			BuildFw=1
			echo "Only Build fw"
		;;
		3)
			echo "Not build, only package"
		;;
		q|Q)
			exit 0
		;;
		*)
			BuildApi=1
			BuildFw=1
			echo "Build api && fw"
		;;
	esac
}

function get_action_type()
{
	ActionType=" "
	Package=0
	Generator=0
	Factory=0
	ReleaseVer=0
    ExportFile="EXPORT_FILE=1"
	echo "Select tester type ?"
	echo -e "\tQ|q: quit build"
	echo -e "\t1: Development: build and pack"
	echo -e "\t2: Development: build and factory"
	echo -e "\t3: Development: build and pack and generator"
	echo -e "\t4: Release regular version"
	echo -e "\tnull: Development:only build"
	read -p "please enter the number you want to build:" index
	case "$index" in
		1)
			Package=1
            ExportFile="EXPORT_FILE=0"
			echo "build and pack"
		;;
		2)
			Factory=1
			echo "build and factory"
		;;
		3)
			ActionType=" GENERATOR=1"
			BuildApi=1
			BuildFw=1
			Package=1
			Generator=1
			echo "build and generator"
		;;
		4)
			ActionType=" GENERATOR=1"
			BuildType=" RELEASE=1 "
			BuildApi=1
			BuildFw=1
			Package=1
			Generator=1
			ReleaseVer=1
			echo "Release regular version"
		;;
		q|Q)
			exit 0
		;;
		*)
			ActionType=" "
			echo "only build"
		;;
	esac
}

if [[ $@ = *clean* ]]; then
	make $@
	exit 0
fi

if [[ $@ = *DEVTYPE=* ]]; then
	BuildApi=1
	BuildFw=1
	Package=1
	Generator=1
	ReleaseVer=1
	svn_url=$1
	TesterType="$2 $3 $4 $5 $6 $7 $8 $9"
    StreamType="$2 $3 $4 $5 $6 $7 $8 $9"
    # BuildType=" RELEASE=1 "
	all=""
else
	get_tester_type
	if [[ $Makeclean = 1 ]]; then
		make clean
		exit 0
	elif [[ $TesterType = *DEVTYPE=WT428* ]]; then
		get_428_version_type
	fi

	get_build_type
	get_mod_type
	get_action_type
	all=$@
fi

if [[ $ReleaseVer = 1 ]]; then
	release_dir=$dir/release
	rm -rf $release_dir
	if [[ -z  $svn_url ]]; then
		svn_url=$(svn info --username softwarepackage --password s5695816923 --show-item url ../ | sed 's/rdserver/192.168.10.6/g')
	fi
	echo "svn_url="$svn_url

	svn_args="--no-auth-cache --non-interactive "
	svn_args+='--trust-server-cert-failures="unknown-ca, cn-mismatch, expired, not-yet-valid, other"'
	while true; do
		cmd="svn export \"$svn_url\" $release_dir --username softwarepackage --password s5695816923 $svn_args"
		eval $cmd
		if [ $? -ne 0 ]; then
			rm -rf $config
			exit 1
		fi
		break
	done

	fw_dir=$release_dir/source
	api_dir=$release_dir/api
	pack_dir=$release_dir/source/bin
else 
	fw_dir=$dir
	api_dir=$dir/../api
	pack_dir=$dir/bin
	if [[ $Generator = 1 ]]; then
		rm -rf $pack_dir
	fi
fi

#配置执行权限
chmod 777 -R ../*

if [[ $BuildApi = 1 ]]; then
	echo "build api shell=\"api_make.sh $TesterType $StreamType $BuildType $ActionType $all \""
	bash $api_dir/api_make.sh $TesterType $StreamType $BuildType $ActionType $all
	if [ $? -ne 0 ]; then
		exit 1
	fi
	cd $fw_dir
fi


function build_fw_fun()
{
	if [[ $BuildFw = 1 ]]; then
		cd $fw_dir
		if [[ $Generator = 1 ]]; then
			make clean
		else
			make clean mod=filesecure
			$(touch $dir/general/version.cpp)
		fi
		echo "making......shell=\"make $TesterType $StreamType $SecureType $BuildType $@ \""
		make $TesterType $StreamType $SecureType $BuildType $@ -j$(nproc)
		if [ $? -ne 0 ]; then
			exit 1
		fi
	fi

	if [[ $Package = 1 || $Factory = 1 ]]; then
		if [[ $Package = 1 ]]; then
			echo "packaging......shell=\"bash $api_dir/api_pack.sh $pack_dir \""
			cd $fw_dir
			bash $api_dir/api_pack.sh $pack_dir
			if [ $? -ne 0 ]; then
				exit 1
			fi
			echo "packaging......shell=\"bash $fw_dir/pack.sh $TesterType $StreamType $SecureType $ActionType \""
			bash $fw_dir/pack.sh $dir $TesterType $StreamType $SecureType $ActionType $ExportFile
			if [ $? -ne 0 ]; then
				exit 1
			fi
			echo "end package~"
		fi

		if [[ $Factory = 1 ]]; then
			echo "factorying......shell=\"bash $fw_dir/factory.sh $TesterType $StreamType $SecureType $ActionType \""
			cd $fw_dir
			bash $fw_dir/factory.sh $TesterType $StreamType $SecureType $ActionType $ExportFile
			if [ $? -ne 0 ]; then
				exit 1
			fi
			echo "end factory~"
		fi
	fi
}

if [[ $BuildTwice = 0 ]]; then
	build_fw_fun $all
else
	SecureType=" "
	build_fw_fun $all
	if [ $? -ne 0 ]; then
		exit 1
	fi

	SecureType=" DISABLE_SECURE=1 "
	build_fw_fun $all
fi
