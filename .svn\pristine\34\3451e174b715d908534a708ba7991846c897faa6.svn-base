#include "commonhandler.h"
#include "vsghandler.h"
#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include <sstream>
#include <sys/time.h>
#include <unistd.h>
#include "basehead.h"
#include "tester.h"
#include "internal.h"
#include "tester_mt.h"
#include "vsa_data_info.h"

int StandardBetween(scpi_t *context, int minValue, int maxValue)
{
    double Demo = -1;
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demo);
        if (iRet)
        {
            break;
        }
        if ((int)Demo < minValue || (int)Demo > maxValue)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        iRet = WT_ERR_CODE_OK;
    } while (0);

    return iRet;
}
int is11AG(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11AG, WT_DEMOD_11AG);
}

#define Resq11AGDataInfo(name, isDouble)                                                     \
do                                                                                          \
{                                                                                           \
    DataInfo11ag DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = is11AG(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)

scpi_result_t GetVsaRst_11ag_DataRate(scpi_t *context)
{
    Resq11AGDataInfo(DataRate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ag_SymbolCnt(scpi_t *context)
{
    Resq11AGDataInfo(SymbolCnt, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ag_PSDULen(scpi_t *context)
{
    Resq11AGDataInfo(PsduLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ag_CodingRate(scpi_t *context)
{
    Resq11AGDataInfo(CodeingRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ag_Modulation(scpi_t *context)
{
    Resq11AGDataInfo(Modulation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ag_duplicate(scpi_t *context)
{
    DataInfo11agPlus DataInfo;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = is11AG(context);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.Dulpicate);
    SCPI_ResultInt(context, DataInfo.DupBW);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_NonHTDuplicatePuncturedInfo(scpi_t *context)
{
    DataInfo11agPlus DataInfo;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = is11AG(context);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.PuncFlag);
    SCPI_ResultInt(context, DataInfo.Punc20Len);
    for(int i = 0; i < DataInfo.Punc20Len; i++)
    {
        SCPI_ResultInt(context, DataInfo.Punc20Flag[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ag_LSIG_Bit(scpi_t *context)
{
    DataInfo11agPlus DataInfo;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = is11AG(context);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    int BitLen = DataInfo.LSig_BitLen;
    SCPI_ResultInt(context, BitLen);
    if (BitLen > 0)
    {
        unique_ptr<char[]> tmpBuf(new char[BitLen + 128]);
        int tmpLen = 0;
        for (int i = 0; i < BitLen; i++)
        {
            tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", DataInfo.LSig_Bit[i]);
        }
        SCPI_ResultText(context, (const char *)tmpBuf.get());
    }
    return SCPI_RES_OK;
}

/////////////////////////////////////////////////////////////////////////////////////////
int is11B(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11B, WT_DEMOD_11B);
}

#define Resq11BDataInfo(name, isDouble)                                                     \
do                                                                                          \
{                                                                                           \
    DataInfo11b DataInfo;                                                                   \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = is11B(context);                                                              \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)


scpi_result_t GetVsaRst_11b_DataRate(scpi_t *context)
{
    Resq11BDataInfo(DataRate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11b_DataLen(scpi_t *context)
{
    Resq11BDataInfo(Length, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11b_PSDULen(scpi_t *context)
{
    Resq11BDataInfo(PsduLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11b_PreambleType(scpi_t *context)
{
    Resq11BDataInfo(PreambleType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11b_SDFPass(scpi_t *context)
{
    Resq11BDataInfo(SfdPass, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11b_HeaderPass(scpi_t *context)
{
    Resq11BDataInfo(HeaderPass, false);
    return SCPI_RES_OK;
}


/////////////////////////////////////////////////////////////////////////////////////////

int is11N(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11N_20M, WT_DEMOD_11N_40M);
}


#define Resq11NDataInfo(name, isDouble)                                                     \
do                                                                                          \
{                                                                                           \
    DataInfo11n DataInfo;                                                                   \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = is11N(context);                                                              \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)

scpi_result_t GetVsaRst_11n_DataRate(scpi_t *context)
{
    Resq11NDataInfo(DataRate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_SymbolCnt(scpi_t *context)
{
    Resq11NDataInfo(SymbolCnt, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_PSDULen(scpi_t *context)
{
    Resq11NDataInfo(PsduLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_CodingRate(scpi_t *context)
{
    Resq11NDataInfo(CodeingRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_Modulation(scpi_t *context)
{
    Resq11NDataInfo(Modulation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_HTSIGCRC(scpi_t *context)
{
    Resq11NDataInfo(HTSigValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_MCS(scpi_t *context)
{
    Resq11NDataInfo(Mcs, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_BW(scpi_t *context)
{
    Resq11NDataInfo(Cbw, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_HTLength(scpi_t *context)
{
    Resq11NDataInfo(HTLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_Smoothing(scpi_t *context)
{
    Resq11NDataInfo(Smooth, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_Sounding(scpi_t *context)
{
    Resq11NDataInfo(NotSnd, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_Aggregation(scpi_t *context)
{
    Resq11NDataInfo(Aggreg, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_STBC(scpi_t *context)
{
    Resq11NDataInfo(STBC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_CodingType(scpi_t *context)
{
    Resq11NDataInfo(FecCode, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_GIType(scpi_t *context)
{
    Resq11NDataInfo(ShortGi, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_SSTrms(scpi_t *context)
{
    Resq11NDataInfo(ExtSStrms, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_Crc(scpi_t *context)
{
    Resq11NDataInfo(Crc, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_HTSIGTail(scpi_t *context)
{
    Resq11NDataInfo(Tail, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_LSIGValid(scpi_t *context)
{
    Resq11NDataInfo(LsigValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_LSIGParity(scpi_t *context)
{
    Resq11NDataInfo(Parity, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_LSIGRate(scpi_t *context)
{
    Resq11NDataInfo(Rate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_LSIGLen(scpi_t *context)
{
    Resq11NDataInfo(Len, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_LSIGTail(scpi_t *context)
{
    Resq11NDataInfo(LsigTail, false);
    return SCPI_RES_OK;
}


scpi_result_t GetVsaRst_11n_LSIGBit(scpi_t *context)
{
    DataInfo11nPlus DataInfo;                                                                   
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              
    int iRet = is11N(context);                                                              
    IF_ERR_RETURN(iRet);                                                                    
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo)); 
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.LSig_BitLen);
    if (DataInfo.LSig_BitLen > 0)
    {
        char tmpBuf[1024] = {0};
        int len = 0;
        for (int i = 0; i < DataInfo.LSig_BitLen; i++)
        {
            len += sprintf(tmpBuf + len, "%d", DataInfo.LSig_Bit[i]);
        }
        SCPI_ResultText(context, (const char *)tmpBuf);
    }
    
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11n_HSIGBit(scpi_t *context)
{
    DataInfo11nPlus DataInfo;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = is11N(context);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.HTSig_BitLen);
    if (DataInfo.HTSig_BitLen > 0)
    {
        char tmpBuf[1024] = {0};
        int len = 0;
        for (int i = 0; i < DataInfo.HTSig_BitLen; i++)
        {
            len += sprintf(tmpBuf + len, "%d", DataInfo.HTSig_Bit[i]);
        }
        SCPI_ResultText(context, (const char *)tmpBuf);
    }

    return SCPI_RES_OK;
}

////////////////////////////////////////////////////////////
int is11AC(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11AC_20M, WT_DEMOD_11AC_80_80M);
}

int is11AC_MUMIMO(scpi_t *context, bool &MUFlag)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        MUFlag = false;

        iRet = is11AC(context);
        if (iRet)
        {
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        DataInfoAcMuMimo DataInfo;

        iRet = WT_GetVectorResult(attr->ConnID,
            WT_RES_11AC_MU_MIMO_DATA_INFO,
            &DataInfo,
            sizeof(DataInfoAcMuMimo));
        if (iRet)
        {
            break;
        }
        if (DataInfo.MuMimoFlag && DataInfo.MuMimoUserNum > 0)
        {
            MUFlag = true;
        }
    } while (0);

    return iRet;
}

#define Resq11acDataInfo(name, isDouble)                                                    \
do                                                                                          \
{                                                                                           \
    DataInfo11ac DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    bool MUFlag = false;                                                                    \
    int iRet = is11AC_MUMIMO(context, MUFlag);                                              \
    IF_ERR_RETURN(iRet);                                                                    \
    if(MUFlag){ iRet = WT_ERR_CODE_PARAMETER_MISMATCH; IF_ERR_RETURN(iRet); }               \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)

scpi_result_t GetVsaRst_11ac_PPDU(scpi_t *context)
{
    bool MUFlag = false;
    int iRet = is11AC_MUMIMO(context, MUFlag);
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, (true == MUFlag ? 1 : 0));
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_DataRate(scpi_t *context)
{
    Resq11acDataInfo(DataRate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SymbolCnt(scpi_t *context)
{
    Resq11acDataInfo(SymbolCnt, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_PSDULen(scpi_t *context)
{
    Resq11acDataInfo(PsduLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_CodingRate(scpi_t *context)
{
    Resq11acDataInfo(CodeingRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_Modulation(scpi_t *context)
{
    Resq11acDataInfo(Modulation, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SIGACRC(scpi_t *context)
{
    Resq11acDataInfo(VHTSigAValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_MCS(scpi_t *context)
{
    Resq11acDataInfo(Mcs, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_BW(scpi_t *context)
{
    Resq11acDataInfo(Bw, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_LTFCnt(scpi_t *context)
{
    Resq11acDataInfo(Nvhtltf, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_NSTS(scpi_t *context)
{
    Resq11acDataInfo(Nsts, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_NSS(scpi_t *context)
{
    Resq11acDataInfo(Nss, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_STBC(scpi_t *context)
{
    Resq11acDataInfo(STBC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_CodingType(scpi_t *context)
{
    Resq11acDataInfo(FecCode, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_GIType(scpi_t *context)
{
    Resq11acDataInfo(ShortGi, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_GroupID(scpi_t *context)
{
    Resq11acDataInfo(GroupId, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SIGBCRC(scpi_t *context)
{
    Resq11acDataInfo(VHTSigBValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SIGBMCS(scpi_t *context)
{
    Resq11acDataInfo(McsB, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SIGBLen(scpi_t *context)
{
    Resq11acDataInfo(Len, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_LSIGParity(scpi_t *context)
{
    Resq11acDataInfo(LsigValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_LSIGRate(scpi_t *context)
{
    Resq11acDataInfo(Rate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_LSIGLen(scpi_t *context)
{
    Resq11acDataInfo(LsigLen, false);
    return SCPI_RES_OK;
}

#define Resq11acDataInfoPlus(name, isDouble)                                                        \
    do                                                                                              \
    {                                                                                               \
        DataInfo11acPlus DataInfo;                                                                  \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);                  \
        int iRet = is11AC(context);                                                                 \
        IF_ERR_RETURN(iRet);                                                                        \
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo)); \
        IF_ERR_RETURN(iRet);                                                                        \
        if (false == isDouble)                                                                      \
            SCPI_ResultInt(context, (int)DataInfo.name);                                            \
        else                                                                                        \
            SCPI_ResultDouble(context, (double)DataInfo.name);                                      \
    } while (0)

#define Resq11acDataInfoPlusBitStream(BitLen, BitData)                                              \
    do                                                                                              \
    {                                                                                               \
        DataInfo11acPlus DataInfo;                                                                  \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);                  \
        int iRet = is11AC(context);                                                                 \
        IF_ERR_RETURN(iRet);                                                                        \
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo)); \
        IF_ERR_RETURN(iRet);                                                                        \
        SCPI_ResultInt(context, (int)DataInfo.BitLen);                                              \
        if ((int)DataInfo.BitLen > 0)                                                               \
        {                                                                                           \
            unique_ptr<char[]> tmpBuf(new char[(int)DataInfo.BitLen + 128]);                        \
            int tmpLen = 0;                                                                         \
            for (int i = 0; i < (int)DataInfo.BitLen; i++)                                          \
            {                                                                                       \
                tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", DataInfo.BitData[i]);                \
            }                                                                                       \
            SCPI_ResultText(context, (const char *)tmpBuf.get());                                   \
        }                                                                                           \
    } while (0)

scpi_result_t GetVsaRst_11ac_PartialAID(scpi_t *context)
{
    Resq11acDataInfoPlus(PartialAID, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_TXOP_PS(scpi_t *context)
{
    Resq11acDataInfoPlus(TXOP_PS_NOT_ALLOWED, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_Beamformed(scpi_t *context)
{
    Resq11acDataInfoPlus(Beamformed, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SoundingNDP(scpi_t *context)
{
    Resq11acDataInfoPlus(SoundingNDP, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_LDPCExtra(scpi_t *context)
{
    Resq11acDataInfoPlus(LDPC_Extra_Symbol, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_NsymDisambiguity(scpi_t *context)
{
    Resq11acDataInfoPlus(ShortGI_NsymDisamb, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_LSIG_Bit(scpi_t *context)
{
    Resq11acDataInfoPlusBitStream(LSig_BitLen, LSig_Bit);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SIGA_Bit(scpi_t *context)
{
    Resq11acDataInfoPlusBitStream(VHTSigA_BitLen, VHTSigA_Bit);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ac_SIGB_Bit(scpi_t *context)
{
    DataInfo11acPlus DataInfo;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = is11AC(context);
    IF_ERR_RETURN(iRet);
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFOPLUS, &DataInfo, sizeof(DataInfo));
    IF_ERR_RETURN(iRet);
    for (int m = 0; m < 4; m++)
    {
        SCPI_ResultInt(context, (int)DataInfo.VHTSigB_BitLen[m]);
        if ((int)DataInfo.VHTSigB_BitLen[m] > 0)
        {
            unique_ptr<char[]> tmpBuf(new char[(int)DataInfo.VHTSigB_BitLen[m] + 128]);
            int tmpLen = 0;
            for (int i = 0; i < (int)DataInfo.VHTSigB_BitLen[m]; i++)
            {
                tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", DataInfo.VHTSigB_Bit[m][i]);
            }
            SCPI_ResultText(context, (const char *)tmpBuf.get());
        }    
    }
    return SCPI_RES_OK;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////
int is11AX(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11AX_20M, WT_DEMOD_11AX_80_80M);
}

int is11AX_MUMIMO(scpi_t *context, bool &MUFlag)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        MUFlag = false;

        iRet = is11AX(context);
        if (iRet)
        {
            break;
        }

        unique_ptr<DataInfo11ax> DataInfo = make_unique<DataInfo11ax>();
        iRet = WT_GetVectorResult(
            attr->ConnID,
            WT_RES_DATA_INFO,
            DataInfo.get(),
            sizeof(DataInfo11ax));
        if (iRet)
        {
            break;
        }

        if (DataInfo.get()->HeSigAValid &&
            1 == DataInfo.get()->MuMimoFlag)
        {
            MUFlag = true;
        }

    } while (0);

    return iRet;
}

#define Resq11axDataInfo(name, isDouble)                                           \
    do                                                                             \
    {                                                                              \
        int iRet = WT_ERR_CODE_OK;                                                 \
        iRet = is11AX(context);                                                    \
        IF_ERR_RETURN(iRet);                                                       \
        unique_ptr<DataInfo11ax> DataInfo = make_unique<DataInfo11ax>();           \
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context); \
        iRet = WT_GetVectorResult(                                                 \
            attr->ConnID,                                                          \
            WT_RES_DATA_INFO,                                                      \
            DataInfo.get(),                                                        \
            sizeof(DataInfo11ax));                                                 \
        IF_ERR_RETURN(iRet);                                                       \
        if (false == isDouble)                                                     \
        {                                                                          \
            SCPI_ResultInt(context, DataInfo.get()->name);                         \
        }                                                                          \
        else                                                                       \
        {                                                                          \
            SCPI_ResultDouble(context, DataInfo.get()->name);                      \
        }                                                                          \
    } while (0)

scpi_result_t GetVsaRst_11ax_PPDU(scpi_t *context)
{
    Resq11axDataInfo(PsduFormat, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGACRC(scpi_t *context)
{
    Resq11axDataInfo(HeSigAValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_BW(scpi_t *context)
{
    Resq11axDataInfo(Bw, false);
    return SCPI_RES_OK;
}

static int GetRUCnt_UserCnt(scpi_t *context, int &RUCnt, int &UserCnt)
{
    int iRet = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        RUCnt = 0;
        UserCnt = 0;

        iRet = is11AX(context);
        if (iRet)
        {
            break;
        }

        unique_ptr<DataInfo11ax> DataInfo = make_unique<DataInfo11ax>();
        iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, DataInfo.get(), sizeof(DataInfo11ax));
        if (iRet)
        {
            break;
        }

        if (DataInfo.get()->HeSigAValid && 1 == DataInfo.get()->MuMimoFlag && HE_MU == DataInfo.get()->PsduFormat)
        {
            unique_ptr<RuOfdmaInfo11ax[]>MuMimoBuf(new RuOfdmaInfo11ax[MAX_USER_NUM]);
            RuOfdmaInfo11ax *RU_MUMIMO = MuMimoBuf.get();
            memset(RU_MUMIMO, 0, sizeof(RuOfdmaInfo11ax)*MAX_USER_NUM);
            iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_MU_MIMO_OFDMA_INFO, RU_MUMIMO, sizeof(RuOfdmaInfo11ax)*MAX_USER_NUM);
            if (iRet)
            {
                break;
            }
            for (int i = 0; i < MAX_USER_NUM; i++)
            {
                if (RU_MUMIMO[i].ValidFlag)
                {
                    UserCnt += RU_MUMIMO[i].Ue_Num;
                    RUCnt++;
                }
            }
        }
        else
        {
            RUCnt = DataInfo.get()->UserNum;
            UserCnt = DataInfo.get()->RealUserNum;
        }
        iRet = WT_ERR_CODE_OK;
       
    } while (0);

    return iRet;
}

scpi_result_t GetVsaRst_11ax_RUCnt(scpi_t *context)
{
    int RUCnt = 0;
    int UserCnt = 0;
    int iRet = GetRUCnt_UserCnt(context, RUCnt, UserCnt);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, RUCnt);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_UserCnt(scpi_t *context)
{
    int RUCnt = 0;
    int UserCnt = 0;
    int iRet = GetRUCnt_UserCnt(context, RUCnt, UserCnt);
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, UserCnt);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SymbolCnt(scpi_t *context)
{
    Resq11axDataInfo(SymbolCnt, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LTFCnt(scpi_t *context)
{
    Resq11axDataInfo(HeltfNum, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LTFType(scpi_t *context)
{
    Resq11axDataInfo(HeltType, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LTFLen(scpi_t *context)
{
    Resq11axDataInfo(HeltfLen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_GILen(scpi_t *context)
{
    Resq11axDataInfo(GILen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_DataRate(scpi_t *context)
{
    Resq11axDataInfo(DataRate, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_DataSymbolLen(scpi_t *context)
{
    Resq11axDataInfo(HeDataSymLen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_FrameLen(scpi_t *context)
{
    Resq11axDataInfo(FrameLen, true);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_BSSColor(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int BSS_Color = 63;
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_11AX_SIGA_BSS_COLOR, &BSS_Color, sizeof(BSS_Color));
    IF_ERR_RETURN(iRet);
    SCPI_ResultInt(context, BSS_Color);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_BeamChange(scpi_t *context)
{
    Resq11axDataInfo(PreTxBF, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LDPCExtra(scpi_t *context)
{
    Resq11axDataInfo(LDPCExtra, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_PEDisambiguity(scpi_t *context)
{
    Resq11axDataInfo(PE, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_PELen(scpi_t *context)
{
    Resq11axDataInfo(PeLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_PreFecPadFactor(scpi_t *context)
{
    Resq11axDataInfo(PreFEC, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_Doppler(scpi_t *context)
{
    Resq11axDataInfo(Doppler, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_MidamblePeriodicity(scpi_t *context)
{
    Resq11axDataInfo(Midamble_Periodicity, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBCRC(scpi_t *context)
{
    Resq11axDataInfo(HeSigBValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBDCM(scpi_t *context)
{
    Resq11axDataInfo(SigBDcm, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBMCS(scpi_t *context)
{
    Resq11axDataInfo(SigBMcs, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBSymbolCnt(scpi_t *context)
{
    Resq11axDataInfo(SigBSymbol, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBCompression(scpi_t *context)
{
    Resq11axDataInfo(SIGBCompression, false);
    return SCPI_RES_OK;
}

static int GetVsaRst_11ax_SIGBEVM(scpi_t *context, const char *analyze_str, bool isComposite = false)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double value = -999.99;
    int streamID = 0;

    if (false == isComposite)
    {
       SCPI_ParamInt(context, &streamID, false); 
    }

//    std::cout << "streamID = " << streamID << std::endl;

    int iRet = WT_GetVectorResult(attr->ConnID, analyze_str, &value, sizeof(value), streamID);
    if (WT_ERR_CODE_OK == iRet)
    {
        SCPI_ResultDouble(context, value);
    }
    return iRet;
}

scpi_result_t GetVsaRst_11ax_SIGBEVMALL(scpi_t *context)
{
    int iRet = GetVsaRst_11ax_SIGBEVM(context, WT_RES_SIGB_EVM_ALL);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBEVMData(scpi_t *context)
{
    int iRet = GetVsaRst_11ax_SIGBEVM(context, WT_RES_SIGB_EVM_DATA_DB);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBEVMPilot(scpi_t *context)
{
    int iRet = GetVsaRst_11ax_SIGBEVM(context, WT_RES_SIGB_EVM_PILOT_DB);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBEVMALL_Composite(scpi_t *context)
{
    int iRet = GetVsaRst_11ax_SIGBEVM(context, WT_RES_SIGB_EVM_ALL_COMPOSITE, true);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBEVMData_Composite(scpi_t *context)
{
    int iRet = GetVsaRst_11ax_SIGBEVM(context, WT_RES_SIGB_EVM_DATA_DB_COMPOSITE, true);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SIGBEVMPilot_Composite(scpi_t *context)
{
    int iRet = GetVsaRst_11ax_SIGBEVM(context, WT_RES_SIGB_EVM_PILOT_DB_COMPOSITE, true);
    IF_ERR_RETURN(iRet);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_Common8Bit(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    unique_ptr<DataInfo11ax> DataInfo = make_unique<DataInfo11ax>();
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, DataInfo.get(), sizeof(DataInfo11ax));
    IF_ERR_RETURN(iRet);

    if (HE_MU != DataInfo.get()->PsduFormat || 1 != DataInfo.get()->HeSigBValid)
    {
        iRet = WT_ERR_CODE_GENERAL_ERROR;
        IF_ERR_RETURN(iRet);
    }

    stringstream msg;
    for (int i = 0; i < DataInfo.get()->Common8BitLen; i++)
    {
        msg << DataInfo.get()->Common8Bit[i];
    }
    SCPI_ResultText(context, msg.str().c_str());
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SoundingNDP(scpi_t *context)
{
    Resq11axDataInfo(SoundingNDP, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_PunctureMode(scpi_t *context)
{
    Resq11axDataInfo(PuncturingMode, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_TXOP(scpi_t *context)
{
    Resq11axDataInfo(TXOP, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_SReuse(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    unique_ptr<DataInfo11ax> DataInfo = make_unique<DataInfo11ax>();
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, DataInfo.get(), sizeof(DataInfo11ax));
    IF_ERR_RETURN(iRet);

    for (int i = 0; i < 4; i++)
    {
        SCPI_ResultInt(context, DataInfo.get()->SpatialReuse[i]);
    }

    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_Beamformed(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    unique_ptr<DataInfo11ax> DataInfo = make_unique<DataInfo11ax>();
    int iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, DataInfo.get(), sizeof(DataInfo11ax));
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, DataInfo.get()->UserInfo[0].Beamformed);

    return SCPI_RES_OK;
}

static scpi_result_t GetRstBitTextVectorData(scpi_t *context, const char *ParamStr, bool VecFlag = false)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }
    IF_ERR_RETURN(iRet);

    if(VecFlag)		//返回多个bit结果内容的，int len1 + bitdata1 + int len2 + bitdata2 + ...
    {
    	int offset = 0;
    	char *TmpRstBuf = ResultBuf.get();
    	//printf("Get ResultSize = %d\n",ResultSize);
    	while(offset < ResultSize)
    	{
    		int BitLen = *(int *)(TmpRstBuf + offset);
    		SCPI_ResultInt(context, BitLen);
    		offset += sizeof(int);

    		if(BitLen > 0)
    		{
    			unique_ptr<char[]> tmpBuf(new char[BitLen + 128]);
    			int tmpLen = 0;
    			char *BitData = TmpRstBuf + offset;
    			for (int i = 0; i < BitLen; i++)
    			{
    				tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", BitData[i]);
    			}
    			SCPI_ResultText(context, (const char *)tmpBuf.get());
    			offset += BitLen;
    		}
    	}
    }
    else	//只有一个bit 结果的
    {
		SCPI_ResultInt(context, ResultSize);
		if(ResultSize > 0)
		{
			unique_ptr<char[]> tmpBuf(new char[ResultSize + 128]);
			int tmpLen = 0;
			char *BitData = (char *)ResultBuf.get();
			for (int i = 0; i < ResultSize; i++)
			{
				tmpLen += sprintf(tmpBuf.get() + tmpLen, "%d", BitData[i]);
			}
			SCPI_ResultText(context, (const char *)tmpBuf.get());
		}
    }
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LSIG_Bit(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11AX_L_SIG_BIT);
}

scpi_result_t GetVsaRst_11ax_SIGA_Bit(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11AX_SIG_A_BIT);
}

scpi_result_t GetVsaRst_11ax_SIGB_Bit_1(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11AX_SIG_B_BIT1);
}

scpi_result_t GetVsaRst_11ax_SIGB_Bit_2(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11AX_SIG_B_BIT2);
}

scpi_result_t GetVsaRst_11ax_LSIGParity(scpi_t *context)
{
    Resq11axDataInfo(LsigValid, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LSIGRate(scpi_t *context)
{
    Resq11axDataInfo(LsigRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11ax_LSIGLen(scpi_t *context)
{
    Resq11axDataInfo(LsigLen, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Be_LSIG_Bit(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11BE_L_SIG_BIT);
}

scpi_result_t GetVsaRst_11Be_USIG_Bit(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11BE_U_SIG_BIT);
}

scpi_result_t GetVsaRst_11Be_SIG_CTX1_Bit(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11BE_SIG_CTX1_BIT, true);
}

scpi_result_t GetVsaRst_11Be_SIG_CTX2_Bit(scpi_t *context)
{
	return GetRstBitTextVectorData(context, WT_RES_11BE_SIG_CTX2_BIT, true);
}

static void ResqDataBurstInfo(scpi_t *context, DataBurstFieldsInfo *result, std::vector<int> &ResultIndex)
{
    for (auto &item : ResultIndex)
    {
        SCPI_ResultInt(context, result->Fieldtype[item].validflag);
        SCPI_ResultInt(context, result->Fieldtype[item].Mod);
        SCPI_ResultInt(context, result->Fieldtype[item].Len);
        SCPI_ResultDouble(context, result->Fieldtype[item].EVM);
        SCPI_ResultDouble(context, result->Fieldtype[item].Power);
    }
}

scpi_result_t GetVsaRst_DataburstInfo(scpi_t *context)
{
    unique_ptr<DataBurstFieldsInfo> DataInfo(new DataBurstFieldsInfo);
    std::vector<int> item_11ag = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_LSIG,
        ENUM_FIELD_Data,
    };

    std::vector<int> item_11n = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_LSIG,
        ENUM_FIELD_HTSIG,
        ENUM_FIELD_DataSTF,
        ENUM_FIELD_DataLTF,
        ENUM_FIELD_Data,
        ENUM_FIELD_GFSTF,
    };

    std::vector<int> item_11ac = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_LSIG,
        ENUM_FIELD_VHTSIGA,
        ENUM_FIELD_VHTSIGB,
        ENUM_FIELD_DataSTF,
        ENUM_FIELD_DataLTF,
        ENUM_FIELD_Data,
    };

    std::vector<int> item_11ax = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_LSIG,
        ENUM_FIELD_RLSIG,
        ENUM_FIELD_HESIGA,
        ENUM_FIELD_DataSTF,
        ENUM_FIELD_DataLTF,
        ENUM_FIELD_Data,
        ENUM_FIELD_DataNSTF,
        ENUM_FIELD_HESIGB,
    };

    std::vector<int> item_11be = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_LSIG,
        ENUM_FIELD_RLSIG,
        ENUM_FIELD_USIG,
        ENUM_FIELD_EHTSIG,
        ENUM_FIELD_DataLTF,
        ENUM_FIELD_Data,
        ENUM_FIELD_DataSTF,
        ENUM_FIELD_DataNSTF,
    };

    std::vector<int> item_11az = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_LSIG,
        ENUM_FIELD_RLSIG,
        ENUM_FIELD_HESIGA,
        ENUM_FIELD_DataSTF,
        ENUM_FIELD_DataNSTF,
        ENUM_FIELD_DataLTF,
    };

    std::vector<int> item_11ah = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_S1GLTFN,
        ENUM_FIELD_S1GSIG,
        ENUM_FIELD_S1GSIGA,
        ENUM_FIELD_DataSTF,
        ENUM_FIELD_DataLTF,
        ENUM_FIELD_S1GSIGB,
        ENUM_FIELD_Data,
    };

    std::vector<int> item_WiSUN = {
        ENUM_FIELD_LSTF,
        ENUM_FIELD_LLTF,
        ENUM_FIELD_PHR,
        ENUM_FIELD_Data,
    };

    double Demod = -1;
    int iRet = WT_ERR_CODE_OK;
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    do
    {
        iRet = WT_GetResult(attr->ConnID, WT_RES_DEMODE, &Demod);
        if (iRet)
        {
            break;
        }

        iRet = WT_GetVectorResult(
            attr->ConnID,
            WT_RES_DATA_BURST_FIELD_INFO,
            DataInfo.get(),
            sizeof(DataBurstFieldsInfo),
            streamID,
            segmentID);
            
        if (iRet)
        {
            break;
        }

        switch ((int)Demod)
        {
        case WT_DEMOD_11AG:
        case WT_DEMOD_11P_5M:
        case WT_DEMOD_11P_10M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11ag);
            break;
        case WT_DEMOD_11N_20M:
        case WT_DEMOD_11N_40M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11n);
            break;
        case WT_DEMOD_11AC_20M:
        case WT_DEMOD_11AC_40M:
        case WT_DEMOD_11AC_80M:
        case WT_DEMOD_11AC_160M:
        case WT_DEMOD_11AC_80_80M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11ac);
            break;
        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
        case WT_DEMOD_11AX_160M:
        case WT_DEMOD_11AX_80_80M:
        case WT_DEMOD_11AX_160_160M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11ax);
            break;
        case WT_DEMOD_11BE_20M:
        case WT_DEMOD_11BE_40M:
        case WT_DEMOD_11BE_80M:
        case WT_DEMOD_11BE_160M:
        case WT_DEMOD_11BE_320M:
        case WT_DEMOD_11BE_80_80M:
        case WT_DEMOD_11BE_160_160M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11be);
            break;
        case WT_DEMOD_11AZ_20M:
        case WT_DEMOD_11AZ_40M:
        case WT_DEMOD_11AZ_80M:
        case WT_DEMOD_11AZ_160M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11az);
            break;
        case WT_DEMOD_11AH_1M:
        case WT_DEMOD_11AH_2M:
        case WT_DEMOD_11AH_4M:
        case WT_DEMOD_11AH_8M:
        case WT_DEMOD_11AH_16M:
            ResqDataBurstInfo(context, DataInfo.get(), item_11ah);
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            ResqDataBurstInfo(context, DataInfo.get(), item_WiSUN);
            break;
        default:
            break;
        }

    } while (0);

    IF_ERR_RETURN(iRet);

    return SCPI_RES_OK;
}

int is11BA(scpi_t *context)
{
    return StandardBetween(context, WT_DEMOD_11BA_20M, WT_DEMOD_11BA_80M);
}

#define Resq11BaDataInfo(name, isDouble)                                                    \
do                                                                                          \
{                                                                                           \
    DataInfo11ba DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = is11BA(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    if (false == isDouble) SCPI_ResultInt(context, (int)DataInfo.name);                     \
    else SCPI_ResultDouble(context, (double)DataInfo.name);                                 \
} while (0)

#define Resq11BaSubChannelDataInfo(name, isDouble)                                          \
do                                                                                          \
{                                                                                           \
    DataInfo11ba DataInfo;                                                                  \
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);              \
    int iRet = is11BA(context);                                                             \
    IF_ERR_RETURN(iRet);                                                                    \
    iRet = WT_GetVectorResult(attr->ConnID, WT_RES_DATA_INFO, &DataInfo, sizeof(DataInfo)); \
    IF_ERR_RETURN(iRet);                                                                    \
    SCPI_ResultInt(context, DataInfo.SubChanNum);                                           \
    if (false == isDouble)                                                                  \
    {                                                                                       \
        for(int i = 0; i < DataInfo.SubChanNum; i++)                                        \
        {                                                                                   \
            SCPI_ResultInt(context, (int)DataInfo.name[i]);                                 \
        }                                                                                   \
    }                                                                                       \
    else                                                                                    \
    {                                                                                       \
        for(int i = 0; i < DataInfo.SubChanNum; i++)                                        \
        {                                                                                   \
            SCPI_ResultDouble(context, (double)DataInfo.name[i]);                           \
        }                                                                                   \
    }                                                                                       \
} while (0)

scpi_result_t GetVsaRst_11Ba_BW(scpi_t *context)
{
    Resq11BaDataInfo(BW, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_LSigParityPassed(scpi_t *context)
{
    Resq11BaDataInfo(LSigParityPassed, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_LSigRate(scpi_t *context)
{
    Resq11BaDataInfo(LSigRate, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_LSigLength(scpi_t *context)
{
    Resq11BaDataInfo(LSigLength, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_LSig_Bit(scpi_t *context)
{
    return GetRstBitTextVectorData(context, WT_RES_11BA_L_SIG_BIT);
}

scpi_result_t GetVsaRst_11Ba_SubChannelCount(scpi_t *context)
{
    Resq11BaDataInfo(SubChanNum, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_PuncturedInfo(scpi_t *context)
{
    Resq11BaSubChannelDataInfo(PunctureFlag, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_DataRateMode(scpi_t *context)
{
    Resq11BaSubChannelDataInfo(DataRateMode, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_PSUDLength(scpi_t *context)
{
    Resq11BaSubChannelDataInfo(PsduLength, false);
    return SCPI_RES_OK;
}

scpi_result_t GetVsaRst_11Ba_PSDU_CRC(scpi_t *context)
{
    Resq11BaSubChannelDataInfo(PsduCRC, false);
    return SCPI_RES_OK;
}

