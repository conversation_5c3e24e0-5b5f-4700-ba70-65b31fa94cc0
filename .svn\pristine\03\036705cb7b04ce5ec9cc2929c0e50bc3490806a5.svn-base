#include "devvsa.h"

#include <iostream>
#include <sstream>
#include <cmath>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <fcntl.h>
#include <iomanip>

#include "devdef.h"
#include "wterror.h"
#include "wtlog.h"
#include "errorlib.h"
#include "basefun.h"
#include "wt-calibration.h"
#include "templib.h"
#include "wtxdma.h"
#include "wtcal.h"
#include "devlib.h"

#define USE_DEFAULT_SAMPLINGFREQ 0

int DevVsa::InitBaseBandMod()
{
    //初始化ADC
    int Ret = ADCInit();
    RetWarnning(Ret, "VSA ADCInit failed!");
    ErrorLib::Instance().CheckErrCode(Ret, Ret);

    //初始化VSA IQ反转
    Json::Value Reg = m_JsonRoot["IQSwap"]["VSASwapValue"];
    if (!Reg.isNull())
    {
        m_IQSwitch = std::strtol(Reg.asString().c_str(), 0, 0);
        Ret = SetIQSwitch(m_IQSwitch);
        ErrorLib::Instance().CheckErrCode(Ret, WT_BUSI_BASE_IQ_SWITCH_FAILED);
        RetWarnning(Ret, "SetIQSwitch failed!");
    }

    if(m_TesterType == HW_WT418 && m_HwVersion >= VERSION_B)
    {
        IQModeInit();
    }

    return Ret;
}

int DevVsa::SetRFPowerStatus(WT_SWITCH_STATUS Status)
{
    (void)Status;
    return WT_OK;
}

int DevVsa::GetGainParam(Rx_Gain_Parm &GainParm)
{
    GainParm = m_GainParm;
    return WT_OK;
}

int DevVsa::SetmGain(Rx_Gain_Parm GainParm)
{
    m_GainParm = GainParm;
    return WT_OK;
}

int DevVsa::GetTrigCongfig(const VSAConfigType &VSAConfig, TriggerCfg &TrigCfg)
{
    int Ret = WT_OK;
#if USE_DEFAULT_SAMPLINGFREQ
    double TransSamplingFreq = DEFAULT_SMAPLE_RATE;
#else
    double TransSamplingFreq = VSAConfig.SamplingFreq;
#endif
    // HW_WT418的122.88M  61.44M  30.72M 15.36MHz 属重采样，用 240M 采样率来算

    if (m_TesterType == HW_WT418 && \
        (Basefun::CompareDouble(VSAConfig.SamplingFreq, 122.88 * MHz, 0.1) == 0 || \
        Basefun::CompareDouble(VSAConfig.SamplingFreq, 61.44 * MHz, 0.1)  == 0 || \
        Basefun::CompareDouble(VSAConfig.SamplingFreq, 30.72 * MHz, 0.1) == 0 || \
        Basefun::CompareDouble(VSAConfig.SamplingFreq, 15.36 * MHz, 0.1) == 0 ))
    {
        TransSamplingFreq = DEFAULT_SMAPLE_RATE;
    }

    //设置采样时间，通过采样时间转换为采样点数
    if (m_TesterType == HW_WT418 || m_HwInfo.BBHwVersion >= VERSION_B)
    {
        int Offset = 0;
        //采集的数据要偏移，所以要额外采集更多数据点
        if ((Ret = DrvCmd(GET_CAPTURE_OFFSET, sizeof(int), &Offset)) != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "GET_CAPTURE_OFFSET ioctl error");
            return Ret;
        }
        int ExtSamlpeCnt = Offset * floor(TransSamplingFreq / VSAConfig.SamplingFreq);
        WTLog::Instance().WriteLog(LOG_DEBUG, "GetTrigCongfig ExtSamlpeCnt=%d, Offset=%d\n", ExtSamlpeCnt, Offset);
        TrigCfg.SmpCnt = TransSamplingFreq * VSAConfig.SamplingTime + ExtSamlpeCnt;
    }
    else
    {
        TrigCfg.SmpCnt = TransSamplingFreq * VSAConfig.SamplingTime;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "Get TrigCfg.SmpCnt = %d\n", TrigCfg.SmpCnt);
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetTrigCongfig TrigType=%d\n", VSAConfig.TrigType);
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetTransSamplingFreq=%lf\n", TransSamplingFreq);
    WTLog::Instance().WriteLog(LOG_DEBUG, "GetSamplingTime=%lf\n", VSAConfig.SamplingTime);
    //修正RX Trigger模式,从IQV的模式枚举映射到硬件中的模式配置
    m_RunData.TrigType = TrigCfg.Type = VSAConfig.TrigType;

    //设置触发预留采样点数
    TrigCfg.PreSmpCnt = TransSamplingFreq * VSAConfig.TrigPreTime;
    TrigCfg.TrigGapTime = DEFAULT_SMAPLE_RATE * VSAConfig.TrigGapTime;
    TrigCfg.TrigEdge = VSAConfig.TrigEdge;
    if (TrigCfg.TrigEdge == WT_TRIG_DEGE_NEGATIVE)
    {
        TrigCfg.PreSmpCnt += TrigCfg.TrigGapTime;
    }

    // 确保采集个数为偶数
    if(TrigCfg.SmpCnt & 1)
    {
        TrigCfg.SmpCnt++;
    }

    //设置触发超时
    TrigCfg.Timeout = VSAConfig.TrigTimeout * 1000;         //毫秒单位，FPGA不用

    return WT_OK;
}

int DevVsa::SetParam(const VSAConfigType &VSAConfig, Rx_Parm &RXParm, int WorkPointMode)
{
    int Ret = WT_OK;
    int RFPort = VSAConfig.RFPort;
    int RFPortState = VSAConfig.RFPortState;
    int RFPortMode = WT_SW_STATE_MODE_SISO;

    TriggerCfg TriggerCfgTemp;
    memset(&TriggerCfgTemp, 0, sizeof(TriggerCfgTemp));

    RXParm.rf_port = VSAConfig.RFPort;
    RXParm.freq = VSAConfig.Freq + VSAConfig.FreqOffsetHz;
    RXParm.ref_power = VSAConfig.Ampl;
	RXParm.ex_iq_mode = 0;
 
    if(m_LOComMode == LO_IS_COM_MODE)
    {
        RXParm.share_mode = true;
    }
    else
    {
        RXParm.share_mode = false; 
    }

    if(m_AnalogIQSW == ANALOGIQ_MODE)
    {
        RXParm.ex_iq_mode = true;
    }
    else
    {
        RXParm.ex_iq_mode = false;
    }
    RXParm.noise_flag = VSAConfig.NoiseCompensation;

    //RXParm.pd_enable = false;
    //RXParm.trigger_level = VSAConfig.TrigLevel;

#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    if(m_TesterType < HW_WT418)
    {
        //设置工作模式
        Ret = SetUnitModWorkMode(static_cast<WT_DEVICE_MODE>(VSAConfig.DeviceMode)); \
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "SetUnitModWorkMode failed");
    }

    bool Is400M = false;
    if (!Basefun::CompareDoubleAccuracy1K(RXParm.freq, 400 * MHz))
    {
        Is400M = true;
        RXParm.freq += MHz;
    }
    RXParm.unit = m_ModId;
    if (Is8080Master(VSAConfig.DeviceMode))
    {
        RXParm.unit_mode = CAL_UNIT_MODE_MASTER;
        RFPortMode = WT_SW_STATE_MODE_8080;
    }
    else if (Is8080Slave(VSAConfig.DeviceMode))
    {
        RXParm.unit_mode = CAL_UNIT_MODE_SLAVE;
        RFPort = WT_RF_PORT_OFF;
        RFPortMode = WT_SW_STATE_MODE_8080;
    }
    else
    {
        RXParm.unit_mode = CAL_UNIT_MODE_SISO;
        RFPortMode = WT_SW_STATE_MODE_SISO;
    }

    RXParm.sample_freq = VSAConfig.SamplingFreq;
    if (WorkPointMode == false)
    {
        //获取RX链路综合校准数据
        Ret = wt_calibration_get_rx_setting(&RXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_rx_setting failed!");
        RFPortState = RXParm.rx_gain_parm.rx_sw_gain.sw_rx_link_state;
    }
    else
    {
        // 工作点设置不取温度
        RXParm.temperature = 30.0;
        RXParm.sw_temperature = 30.0;
        //获取RX链路综合校准数据
        Ret = wt_calibration_get_rx_work_point_setting(&RXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_rx_work_point_setting failed!");
        RFPortState = RXParm.rx_gain_parm.rx_sw_gain.sw_rx_link_state;;
    }

    Ret = wt_calibration_get_rx_iq_image_data(&RXParm);
    if (Ret != WT_OK)
    {
        Ret += WT_CAL_BASE_ERROR;
    }
    RetAssert(Ret, "wt_calibration_get_rx_iq_image_data failed!");

    if (Is400M)
    {
        RXParm.freq -= MHz;
        RXParm.freq_parm.LoParm[LoMod].freq = RXParm.freq_parm.LoParm[LoMod].freq > 0
                                                  ? RXParm.freq_parm.LoParm[LoMod].freq - 1
                                                  : RXParm.freq_parm.LoParm[LoMod].freq + 1;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSAConfig.ref power = " << VSAConfig.Ampl << std::endl;
#endif
    if (m_AnalogIQSW != ANALOGIQ_MODE)
    {
        /**************************开关板板配置************************************/

#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", RXPort RF = " << RFPort
                  << ", State=" << RFPortState << ", Mode=" << RFPortMode << std::endl;
#endif
        // 校查开关板在位情况与该单元是否为80+80从机模式
        if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
        {
            // 当RF端口改变或端口功能改变时重新设置RF端口
            if ((m_RunData.RFPortState != RFPortState && m_RunData.RFPort != WT_RF_OFF) ||
                m_RunData.RFPortMode != RFPortMode ||
                m_RunData.RFPort != RFPort ||
                m_BackBoard->GetPortStatus(RFPort) != WT_RF_RX_STATUS)
            {
                // 校准状态不修改
                if (m_RunData.RFPortState >= WT_RF_STATE_DET_PI && m_RunData.RFPortState <= WT_RF_STATE_PAC_PA_2)
                {
                    WTLog::Instance().WriteLog(LOG_DEBUG, "User set m_RunData.RFPortState=%d, skip setting\n", m_RunData.RFPortState);
                }
                else
                {
#if DEVLIB_DEBUG
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                    Ret = SetRFPort(RFPort, RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                    ErrorLib::Instance().CheckErrCode(Ret, Ret);
                    RetAssert(Ret, "VSA SetRXPort failed");
                }
            }
        }

        /**************************射频板配置************************************/

        // 配置RX链路上的增益、本振、波段开关
        Ret = SetFreqAndGain(VSAConfig, RXParm);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "VSA SetRXFreqAndGain failed");
    }
    /**************************基带板配置************************************/
    //配置重采样率
    Ret = SetResample(VSAConfig.SamplingFreq);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSA SetResample failed");

    //配置RX IQ IMB COMP
    // Ret = SetIqImbConfig(RXParm.rx_iq_imb_parm.gain_imb, RXParm.rx_iq_imb_parm.quad_err, RXParm.rx_iq_imb_parm.timeskew);
    // ErrorLib::Instance().CheckErrCode(Ret, Ret);
    // RetAssert(Ret, "VSA SetIqImbConfig failed");

    //配置RX DC offset
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(VSAConfig.DcOffsetI) << Pout(RXParm.rx_dc_offset_parm.i_code)
              << Pout(VSAConfig.DcOffsetQ) << Pout(RXParm.rx_dc_offset_parm.q_code) << std::endl;
    if (m_AnalogIQSW == ANALOGIQ_MODE)
    {
        Ret = SetDCOffset(CheckIQOffset(VSAConfig.DcOffsetI + RXParm.rx_dc_offset_parm.i_code),
                          CheckIQOffset(VSAConfig.DcOffsetQ + RXParm.rx_dc_offset_parm.q_code),
                          false);
    }
    else
    {
        Ret = SetDCOffset(RXParm.rx_dc_offset_parm.i_code, RXParm.rx_dc_offset_parm.q_code, false);
    }
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSA SetDCOffset failed");


    //获取Trigger配置信息
    GetTrigCongfig(VSAConfig, TriggerCfgTemp);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);

    //配置VSA Trigger
    Ret = VSASetTrig(TriggerCfgTemp);
    ErrorLib::Instance().CheckErrCode(Ret, Ret);
    RetAssert(Ret, "VSA VSASetTrig failed");

    if (VSAConfig.TrigType != WT_TRIG_TYPE_FREE_RUN)
    {
        //设置数字触发的触发电平
        Ret = SetRXTrigLevelDigital(VSAConfig.TrigLevel);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "VSA SetRXTrigLevelDigital failed");
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA WorkMode=" << m_RunData.CurrentWorkMode << std::setprecision(4)
              << " gain_imb=" << RXParm.rx_iq_imb_parm.gain_imb
              << " quad_err=" << RXParm.rx_iq_imb_parm.quad_err
              << " timeskew=" << RXParm.rx_iq_imb_parm.timeskew
              << "\n"
              << " gain_imb_160m=" << RXParm.rx_iq_imb_parm_160m.gain_imb
              << " quad_err_160m=" << RXParm.rx_iq_imb_parm_160m.quad_err
              << " timeskew_160m=" << RXParm.rx_iq_imb_parm_160m.timeskew
              << "\n"
              << " gain_imb_320m=" << RXParm.rx_iq_imb_parm_320m.gain_imb
              << " quad_err_320m=" << RXParm.rx_iq_imb_parm_320m.quad_err
              << " timeskew_320m=" << RXParm.rx_iq_imb_parm_320m.timeskew << std::endl;
#endif

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSASetConfig Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsa::SetWorkPointParam(const VSAConfigType &VSAConfig, Rx_Parm &RXParm)
{
    return SetParam(VSAConfig, RXParm, true);
}

int DevVsa::SetCalConfig(const Rx_Parm &RXParm)
{
    int Ret = WT_OK;
    (void)Ret;
    (void)RXParm;
    return WT_OK;
}

int DevVsa::Start(int Mode)
{
    int Ret = WT_OK;

#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    //特殊版本，为规避VSA ATT0相应时间过长的问题，改在配置时就配置ATT，而不是启动前
    //处理VF仪器切换ATT时，导致VSA功率不稳定的问题，两次修改两个ATT共增加耗时约80us
    if (m_IsRfExist && m_calibration_flat == false)
    {
        Rx_Gain_Parm &GainParm = (m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
                                     ? m_GainParmDebug
                                     : m_GainParm;

        if (m_AttConfigMode != WT_ATT_CONFIG_MODE_CONFIG)
        {
            //设置RX链路增益
            Ret = SetGain(GainParm);
            RetAssert(Ret, "SetGain failed!");

#if WT_BOOST_MANUAL
            //设置boost开关
            Ret = SetLNAStatus(GainParm.is_lna_on);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
#endif
        }
    }

    if ((Ret = DrvCmd(VSA_START, sizeof(int), &Mode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_START ioctl error");
        return Ret;
    }

    m_RunData.XdmaStatus = WT_XDMA_INIT;
    m_RunData.Status = WT_RX_TX_STATUS_RUNNING;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSA" << m_ModId << "  Start!--------" << std::endl;
#endif

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSAStart Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsa::Stop()
{
    int Ret = WT_OK;
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    int state = m_RunData.Status;

    Finish();
    Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, BP_ATT_CODE_MAX);
    RetWarnning(Ret, "VSAStop SetSwbAttCode failed!");
    m_RunData.Freq = 0;

    //驱动层对FPGA软件复位寄存器进行复位操作
    if ((Ret = DrvCmd(VSA_STOP, 0, nullptr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_STOP ioctl error");
        return Ret;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSA" << m_ModId << "  Stop!--------" << std::endl;
#endif

    if (state == WT_RX_TX_STATUS_RUNNING && m_RunData.TrigType == WT_TRIG_TYPE_FREE_RUN)
    {
        //增加40毫秒延时，等待FPGA软复位完成
        usleep(40000);
    }

    if(m_ListModeNeedReset)
    {
        Ret = ResetRundataFromCache();
        m_ListModeNeedReset = 0;
        if (Ret)
        {
            return Ret;
        }
    }
#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSAStop Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsa::Finish()
{
    int Ret = WT_OK;

    //特殊版本，为规避VSA ATT0相应时间过长的问题，改在配置时就配置ATT，而不是启动前
    if (m_IsRfExist && m_calibration_flat == false)
    {
        if (m_AttConfigMode == WT_ATT_CONFIG_MODE_START)
        {
            //设置衰减器code值为最大值
            for (int i = 0; i < RX_ATT_MAX; i++)
            {
                Ret = SetATTCode(i, ATT_CODE_MAX);
                RetContinue(Ret, "VSAStop SetATTCode failed!");
                m_RunData.ATTCurData[i] = ATT_CODE_MAX;
            }

#if WT_BOOST_MANUAL
            //设置boost开关
            Ret = SetLNAStatus(false);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetLNAStatus failed!");
#endif
        }
    }

    m_RunData.Status = WT_RX_TX_STATUS_STOP;
    return Ret;
}

int DevVsa::SetRfDemod(Rx_Parm &RXParm)
{
    int Ret = WT_OK;
    for (int addr = 0; addr < RX_DEMOD_CAL_ADDR_COUNT; ++addr)
    {
        if (m_LTC5594Code[addr] != RXParm.rx_demod_parm.data[addr])
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "SetRfDemod WriteLTC5594 0x%x=0x%x\n", addr, RXParm.rx_demod_parm.data[addr]);
            Ret = WriteLTC5594(addr, RXParm.rx_demod_parm.data[addr]);
            if (Ret != WT_OK)
            {
                m_LTC5594Code[addr] = -1;
            }
            else
            {
                m_LTC5594Code[addr] = RXParm.rx_demod_parm.data[addr];
            }
            RetAssert(Ret, "WriteLTC5594 failed!");
        }
    }

    return Ret;
}

int DevVsa::SetFreqAndGain(const VSAConfigType &VSAConfig, Rx_Parm &RXParm)
{
    int Ret = WT_OK;

    if (m_IsRfExist)
    {
        m_GainParm = RXParm.rx_gain_parm;
        if (m_AttConfigMode == WT_ATT_CONFIG_MODE_CONFIG ||
            (m_AttConfigMode == WT_ATT_CONFIG_MODE_START && m_RunData.Status == WT_RX_TX_STATUS_RUNNING))
        {
            //特殊版本，为规避VSA ATT0相应时间过长的问题，改在配置时就配置ATT，而不是启动前
            //校准模式时，仍在配置参数是配置ATT，完成及开启前不修改ATT
            //设置RX链路增益
            Ret = SetGain(RXParm.rx_gain_parm);
            RetAssert(Ret, "SetGain failed!");

            //设置boost开关
            Ret = SetLNAStatus(RXParm.rx_gain_parm.is_lna_on);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "SetLNAStatus failed!");
        }

        if (m_TesterType != HW_WT418)
        {
            Ret = SetRfDemod(RXParm);
            RetAssert(Ret, "VSA SetRfDemod failed");
        }
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA Freq= " << VSAConfig.Freq
              << ", FreqOffsetHz=" << VSAConfig.FreqOffsetHz
              << ", m_LOComMode=" << m_LOComMode << std::endl;
#endif
    // 设置RX链路频率并确认
    double Freq = VSAConfig.Freq + VSAConfig.FreqOffsetHz;
    Ret = SetFreqWithConfirm(Freq, &RXParm.freq_parm, static_cast<WT_DEVICE_MODE>(VSAConfig.DeviceMode));
    RetAssert(Ret, "SetFreqWithConfirm failed!");
    if (m_LOComMode == LO_IS_COM_MODE)
    {
        Freq_Parm freq_parm;
        Ret = wt_calibration_get_tx_lo_setting(m_ModId, Freq, &freq_parm, RXParm.share_mode);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_tx_lo_setting failed!");
        // 在共本振模式下,同时设置本振
        Ret = DevLib::Instance().SetFreqWithConfirm(m_ModId, DEV_TYPE_VSG, Freq, &freq_parm, static_cast<WT_DEVICE_MODE>(VSAConfig.DeviceMode));
        RetAssert(Ret, "SetFreqWithConfirm failed!");
    }

    return WT_OK;
}

int DevVsa::SetFreq(double RXFreq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode)
{
    int Ret = WT_OK;
    double FreqLoMix = 0;
    double FreqLoMod = 0;
    int IQSwap = 0;

    WT_RF_MOD_BAND_E BandMod = static_cast<WT_RF_MOD_BAND_E>(FreqParm->LoParm[LoMod].band);
    WT_RF_MIX_BAND_E BandMix = static_cast<WT_RF_MIX_BAND_E>(FreqParm->LoParm[LoMix].band);
    int LoModDuplicateSet = 0;

    if (Basefun::CompareDouble(RXFreq, 0) == 0)
    {
        FreqLoMix = 0;
        FreqLoMod = 0;
    }
    else
    {
        FreqLoMix = fabs(FreqParm->LoParm[LoMix].freq);
        FreqLoMod = fabs(FreqParm->LoParm[LoMod].freq);
        IQSwap = (FreqParm->LoParm[LoMod].freq < 0) ? true : false;
    }

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setiosflags(std::ios::fixed) << std::setprecision(9);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RX Freq=" << RXFreq << " WorkMode=" << WorkMode << " IQSwap=" << IQSwap << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMod Freq=" << FreqLoMod << " BandMod=" << BandMod << " power_level=" << FreqParm->LoParm[LoMod].power_level << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "LoMix Freq=" << FreqLoMix << " BandMix=" << BandMix << " power_level=" << FreqParm->LoParm[LoMix].power_level << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setiosflags(std::ios::fixed) << std::setprecision(4);
#endif

    if (m_IsRfExist)
    {
#if WT_RX_BAND_ON
        if ((Basefun::CompareDouble(m_RunData.Freq, RXFreq, 1e-12) != 0) ||
            m_RunData.CurrentBandMod != BandMod ||
            m_RunData.CurrentBandMix != BandMix ||
            m_RunData.CurrentWorkMode != WorkMode)
        {
            Ret = SetBand(FreqLoMod, FreqLoMix, BandMod, BandMix);
            RetAssert(Ret, "SetRXBand failed!");
            m_RunData.Freq = RXFreq;
            m_RunData.CurrentBandMod = BandMod;
            m_RunData.CurrentBandMix = BandMix;
            m_RunData.CurrentWorkMode = WorkMode;
        }
#else
        (void)WorkMode;
#endif
    }

    if (m_IsLoExist)
    {
#if WT_RX_FREQ_MANUAL
        //若当前为校准模式，直接复位本振，将本振从静音模式唤醒
        if (m_BoardType != m_ShareLOBoardType && m_RunData.Status2 == WT_RX_TX_STATUS_DOWN && m_LOComMode == LO_IS_NOT_COM_MODE)
        {
            LoModInit();
            LoMixInit();
            m_RunData.Status2 = WT_RX_TX_STATUS_RUNNING;
        }

        if (Basefun::CompareDouble(m_RunData.LOCurData[LoMix].Freq, FreqLoMix, 1e-12) != 0 ||
            m_RunData.LOCurData[LoMix].PowerLevel != FreqParm->LoParm[LoMix].power_level)
        {
            Ret = SetMixFreqPower(FreqLoMix, FreqParm->LoParm[LoMix].power_level);
            RetAssert(Ret, "SetMixFreqPower LoMix failed!");
            LoModDuplicateSet = 1;
        }

        if (Basefun::CompareDouble(m_RunData.LOCurData[LoMod].Freq, FreqLoMod, 1e-12) != 0 ||
            m_RunData.LOCurData[LoMod].PowerLevel != FreqParm->LoParm[LoMod].power_level)
        {
            Ret = SetModFreqPower(FreqLoMod, FreqParm->LoParm[LoMod].power_level, IQSwap);
            RetAssert(Ret, "SetModFreqPower LoMod failed!");
        }

        LOConfigSave(LoMix, true, BandMix, FreqLoMix, FreqParm->LoParm[LoMix].power_level);
        LOConfigSave(LoMod, true, BandMod, FreqLoMod, FreqParm->LoParm[LoMod].power_level);
#if 0
        //先取消第二次配置LMX2594, 看看有没有问题，后续有问题再继续加上
        if (LoModDuplicateSet)
        {
            Ret = SetMixFreqPower(m_RunData.LOCurData[LoMix].Freq, m_RunData.LOCurData[LoMix].PowerLevel);
            RetAssert(Ret, "SetMixFreqPower LoMix failed!");
        }
#else
        (void)LoModDuplicateSet;
#endif
#endif
    }
    return Ret;
}

int DevVsa::SetFreqWithConfirm(double Freq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode)
{
    std::unique_lock<std::mutex>  Lock(m_SetFreqMutex);

    int count = 0;
    LO_ID_E LOId;
    //设置RX链路频率
    int Ret = SetFreq(Freq, FreqParm, WorkMode);
    RetWarnning(Ret, "SetRXFreq1 failed!");
    if (m_BoardType != m_ShareLOBoardType && m_LOComMode == LO_IS_COM_MODE)
    {
        return Ret; //共本振模式,非共享本振掉电。
    }
    else
    {
        while ((Ret = CheckLOStatus(Freq)) != WT_OK)
        {
            LOId = (Ret == WT_MIX_FREQ_IS_UNLOCKED ? LoMix : LoMod);
            if (count % 2 == 1) //掉电后重置LO
            {
                usleep(6000);
                Ret = ResetLO(LOId);
                RetWarnning(Ret, "ResetLO failed!");
                usleep(2000);
            }

            if (count++ == 3)
            {
                LoMixInit();
                usleep(2000);
                if (m_TesterType == HW_WT418 || m_HwInfo.LoHwVersion >= VERSION_B)
                {
                    LoModInit();
                }
                usleep(2000);
                Ret = SetFreq(Freq, FreqParm, WorkMode);
                RetWarnning(Ret, "SetTXFreq2 failed!");

                if ((Ret = CheckLOStatus(Freq)) != WT_OK)
                {
#if DEVLIB_DEBUG
                    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "\n\n\n\n###############SetRXFreq ResetLO count=" << count << "##################\n\n\n\n"
                              << std::endl;
#endif
                    return Ret;
                }
                break;
            }

            //重新设置RX链路频率
            Ret = SetFreq(Freq, FreqParm, WorkMode);
            RetWarnning(Ret, "SetRXFreq failed!");
        }
    }

    return Ret;
}

// double FreqLo1, double FreqLo2, MHz为单位
int DevVsa::SetBand(double ModFreq, double MixFreq, WT_RF_MOD_BAND_E BandMod, WT_RF_MIX_BAND_E BandMIX)
{
    int Ret = WT_OK;
    FreqBandType FreqBandTypeTemp;

    int LoMod = static_cast<int>(ModFreq + 0.5);
    int LoMix = static_cast<int>(MixFreq + 0.5);

    FreqBandTypeTemp.LoModBand = 0;
    FreqBandTypeTemp.LoMixBand = 0; 
    FreqBandTypeTemp.BandMod = BandMod;
    FreqBandTypeTemp.BandMIX = BandMIX;

    int Channel = 0;
    for (auto &iter : m_RfModLoFreqChannel)
    {
        if (LoMod <= iter.EndFreq)
        {
            Channel = iter.Index;
            break;
        }
    }
    FreqBandTypeTemp.LoModBand = Channel;

    Channel = 0;
    for (auto &iter : m_RfMixLoFreqChannel)
    {
        if (LoMix <= iter.EndFreq)
        {
            Channel = iter.Index;
            break;
        }
    }
    FreqBandTypeTemp.LoMixBand = Channel;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetRXBand LoModBand=" << FreqBandTypeTemp.LoModBand << " LoMixBand=" << FreqBandTypeTemp.LoMixBand << " BandMod=" << BandMod << " BandMIX=" << BandMIX << std::endl;
#endif

    if((Ret = DrvCmd(SET_RX_BAND, sizeof(FreqBandType), &FreqBandTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_RX_BAND ioctl error");
        return Ret;
    }

    return WT_OK;
}

int DevVsa::SetGain(const Rx_Gain_Parm &RXGainParm)
{
    int Ret = WT_OK;

    if (m_BackBoard->GetSwbAttCacheCode(m_RunData.RFPort) != RXGainParm.rx_sw_gain.sw_rx_att_code[0])
    {
        Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, RXGainParm.rx_sw_gain.sw_rx_att_code[0]);
        RetAssert(Ret, "SetGain SetSwbAttCode failed!");
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Port" << m_RunData.RFPort << " BPAttCode =" << RXGainParm.rx_sw_gain.sw_rx_att_code[0] << std::endl;
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set Port" << m_RunData.RFPort << " BPAttCode =" << RXGainParm.rx_sw_gain.sw_rx_att_code[0] << ",  Same as last. " << std::endl;
    }

    //使用校准数据设置衰减器code值
    for (unsigned i = 0; i < RX_ATT_MAX; i++)
    {
        if (m_RunData.ATTCurData[i] != RXGainParm.att_code[i])
        {
            Ret = SetATTCode(i, RXGainParm.att_code[i]);
            RetAssert(Ret, "SetGain SetATTCode failed!");
            m_RunData.ATTCurData[i] = RXGainParm.att_code[i];

            if(i == 0)
            {
                usleep(m_Att0ExtDelay);
                if ((m_RunData.ATTCurData[i] - RXGainParm.att_code[i]) > 20)
                {
                    //调整幅度比较大时，额外延时
                    usleep((m_RunData.ATTCurData[i] - RXGainParm.att_code[i] - 20) * 50);
                }
            }
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RXGainParm.ATT[" << i << "]=" << RXGainParm.att_code[i] << std::endl;
#endif
        }
        else
        {
#if DEVLIB_DEBUG
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RXGainParm.ATT[" << i << "]=" << RXGainParm.att_code[i] << ",  Same as last. " << std::endl;
#endif
        }
    }
    return Ret;
}

#ifdef WT418_FW
int DevVsa::SetCaptureOriDataSample(VSAConfigType &VsaConfig)
{
    //配置重采样率
    int Ret = SetResample(VsaConfig.SamplingFreq);
    RetAssert(Ret, "VSA SetResample failed");

    TriggerCfg TriggerCfgTemp;
    memset(&TriggerCfgTemp, 0, sizeof(TriggerCfgTemp));
    //获取Trigger配置信息
    GetTrigCongfig(VsaConfig, TriggerCfgTemp);
    //配置VSA Trigger
    Ret = VSASetTrig(TriggerCfgTemp);
    RetAssert(Ret, "VSA VSASetTrig failed");

    return Ret;
}

int DevVsa::CaptureOriData(void *pBuf, int Size, int ExtraSmpOffset)
{
    int Ret = WT_OK;
    int DataOffset = 0;

    if (m_MapDmaBuf == NULL)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, " Allow continuous memory failed(posix_memalign)");
        return WT_ALLOC_FAILED;
    }

#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    if(Size > DMA_BUF_SIZE)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "CaptureData size more then max 128M");
        return WT_ARG_ERROR;
    }

    /*if (m_RunData.XdmaStatus == WT_XDMA_WR_FINISH)
    {
        memcpy(pBuf, m_MapDmaBuf + m_DmaDataOffset, Size);
        return WT_OK;
    }*/

    std::string DeviceName = Xdmafun::GetC2HDeviceName(m_ModId, 0);
    int FpgaFd = open(DeviceName.c_str(), O_RDWR);
    if (FpgaFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }

    do
    {
        if ((Ret = DrvCmd(VSA_CAPTURE_DATA, sizeof(int), &DataOffset)) != WT_OK)
        {
            Ret = WT_XMDA_WR_ERROR;
            WTLog::Instance().LOGERR(Ret, "VSA_CAPTURE_DATA failed");
            break;
        }

        m_DmaDataOffset = (DataOffset / 4) * 4 + ExtraSmpOffset;
        m_DmaDataSize = Size;
        m_RunData.XdmaStatus = WT_XDMA_WR_FINISH;
        int ReadSize = Size;

        if (m_HwInfo.BBHwVersion >= VERSION_B)
        {
            ReadSize = (Size + m_DmaDataOffset + 15) & (~15); //对齐到16byte
        }
        else
        {
            ReadSize = Size;
        }

        char *buffer = m_MapDmaBuf;
        ssize_t rc = Xdmafun::Instance().ReadToBuffer(DeviceName.c_str(), FpgaFd, buffer, ReadSize, 0);
        WTLog::Instance().WriteLog(LOG_DEBUG, "VSA%d XDMA read size=%d, actual size=%ld, Offset=%d\n", m_ModId, ReadSize, rc, m_DmaDataOffset);

        int Status = 0;
        int WaitCnt = 0; //等待计数，最多等待1S
        do
        {
            usleep(10);
            if ((Ret = DrvCmd(GET_XDMA_STATUS, sizeof(Status), &Status)) != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "GET_XDMA_STATUS failed");
                break;
            }
        } while (Status == WT_RX_TX_STATE_RUNNING && ++WaitCnt < 100000);

        if (Ret != WT_OK || Status != WT_RX_TX_STATE_DONE)
        {
            Ret = WT_XMDA_STATUS_TIMEOUT;
            RetBreak(Ret, "CaptureData wait fpga xdma finish timeout");
        }
        else if (rc < 0 || rc != ReadSize)
        {
            Ret = WT_XMDA_WR_ERROR;
            RetBreak(Ret, "CaptureData read xdma error");
        }
        else
        {
            memcpy(pBuf, buffer + m_DmaDataOffset, Size);
        }
    } while (0);

    close(FpgaFd);

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA" << m_ModId << " CaptureData Used Time:" << timeuse << "us" << std::endl;
#endif

    return Ret;
}
#endif

int DevVsa::CaptureData(void *pBuf, int Size, int ListModeOffset)
{
    (void)ListModeOffset;
    int Ret = WT_OK;
    int DataOffset = 0;
    int base = 0;

    if (m_MapDmaBuf == NULL)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, " Allow continuous memory failed(posix_memalign)");
        return WT_ALLOC_FAILED;
    }

#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif

    if(Size > DMA_BUF_SIZE)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "CaptureData size more then max 128M");
        return WT_ARG_ERROR;
    }

    if ((m_ListMode == 0) && (m_RunData.XdmaStatus == WT_XDMA_WR_FINISH))
    {
        memcpy(pBuf, m_MapDmaBuf + m_DmaDataOffset, Size);
        return WT_OK;
    }

    std::string DeviceName = Xdmafun::GetC2HDeviceName(m_ModId, 0);
    int FpgaFd = open(DeviceName.c_str(), O_RDWR);
    if (FpgaFd < 0)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
        return WT_FILE_OPEN_ERROR;
    }

    do
    {
        if ((Ret = DrvCmd(VSA_CAPTURE_DATA, sizeof(int), &DataOffset)) != WT_OK)
        {
            Ret = WT_XMDA_WR_ERROR;
            WTLog::Instance().LOGERR(Ret, "VSA_CAPTURE_DATA failed");
            break;
        }
		
        int ReadSize = Size;

		if (m_ListMode == 0)
		{
            m_RunData.XdmaStatus = WT_XDMA_WR_FINISH;
            m_DmaDataOffset = (DataOffset / 4) * 4;
            m_DmaDataSize = Size;

            if (m_HwInfo.BBHwVersion >= VERSION_B)
            {
                ReadSize = (Size + m_DmaDataOffset + 15) & (~15); //对齐到16byte
            }
            else
            {
                ReadSize = Size;
            }
		}
        else
        {
            m_DmaDataOffset = 0;
            m_DmaDataSize = Size;
            ReadSize = Size;
            base = ListModeOffset;
        }

        char *buffer = m_MapDmaBuf;
        ssize_t rc = Xdmafun::Instance().ReadToBuffer(DeviceName.c_str(), FpgaFd, buffer, ReadSize, base);
        WTLog::Instance().WriteLog(LOG_DEBUG, "VSA%d XDMA read size=%d, actual size=%ld, Offset=%d base=%d\n", m_ModId, ReadSize, rc, m_DmaDataOffset, base);

        int IfNeedOriDataState = 0;
#ifdef WT418_FW
        if (ReadDirectReg(VSA_ORIGIN_DATA_ENABLE_REG, IfNeedOriDataState) != WT_OK)
        {
            Ret = WT_REG_WR_ERROR;
            RetBreak(Ret, "CaptureData wait fpga xdma finish timeout");
        }
#endif

		if (m_ListMode == 0)
		{
	        int Status = 0;
	        int ExpectStatus = 0;
	        int WaitCnt = 0; //等待计数，最多等待1S
	        if (IfNeedOriDataState)
	        {
	            ExpectStatus = NEED_ORIGIN_DATA_TEMP_STATE;
	            do
	            {
	                usleep(10);
	                if ((Ret = DrvCmd(GET_XDMA_STATUS, sizeof(Status), &Status)) != WT_OK)
	                {
	                    WTLog::Instance().LOGERR(Ret, "GET_XDMA_STATUS failed");
	                    break;
	                }
	            } while (Status != ExpectStatus && ++WaitCnt < 100000);
	        }
	        else
	        {
	            ExpectStatus = WT_RX_TX_STATUS_DONE;
	            do
	            {
	                usleep(10);
	                if ((Ret = DrvCmd(GET_XDMA_STATUS, sizeof(Status), &Status)) != WT_OK)
	                {
	                    WTLog::Instance().LOGERR(Ret, "GET_XDMA_STATUS failed");
	                    break;
	                }
	            } while (Status == WT_RX_TX_STATE_RUNNING && ++WaitCnt < 100000);
	        }
			
	        if (Ret != WT_OK || Status != ExpectStatus)
	        {
	            Ret = WT_XMDA_STATUS_TIMEOUT;
	            RetBreak(Ret, "CaptureData wait fpga xdma finish timeout");
	        }
	        else if (rc < 0 || rc != ReadSize)
	        {
	            Ret = WT_XMDA_WR_ERROR;
	            RetBreak(Ret, "CaptureData read xdma error");
	        }
	        else
	        {
	            memcpy(pBuf, buffer + m_DmaDataOffset, Size);
	        }
		}
		else
		{
            if (rc < 0 || rc != ReadSize)
	        {
	            Ret = WT_XMDA_WR_ERROR;
	            RetBreak(Ret, "CaptureData read xdma error");
	        }
	        else
	        {
	            memcpy(pBuf, buffer + m_DmaDataOffset, Size);
	        }
		}

    } while (0);

    close(FpgaFd);

#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA" << m_ModId << " CaptureData Used Time:" << timeuse << "us" << std::endl;
#endif

    return Ret;
}

int DevVsa::SaveData(int Index)
{
    std::ofstream PnData(WTConf::GetDir() + "/Vsa" + std::to_string(m_ModId) + "_CaptureData_" + std::to_string(Index) + ".csv",
                        std::fstream::out | std::fstream::trunc);
    short *Code = (short *)((char *)m_MapDmaBuf + m_DmaDataOffset);
    for (int j = 0; j < (m_DmaDataSize / 4); j++)
    {
        PnData << Code[j * 2] << "," << Code[j * 2 + 1] << std::endl;
    }
    PnData.close();
    return WT_OK;
}

int DevVsa::GetStatus()
{
    int Status = 0;

    if (DrvCmd(VSA_GET_STATUS, sizeof(int), &Status) != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_RX_TX_STATUS_ERR_DONE, "VSA_GET_STATUS ioctl error");
        Status = WT_RX_TX_STATUS_ERR_DONE;
    }

    return Status;
}

int DevVsa::Down()
{
    int Ret = WT_OK;

    if (m_IsLoExist)
    {
        if (m_LOComMode == LO_IS_COM_MODE && m_BoardType != m_ShareLOBoardType && m_RunData.Status2 == WT_RX_TX_STATUS_DOWN)
        {
            Ret = DevLib::Instance().VSGDown(m_ModId);
        }
        else if (m_TesterType == HW_WT418)
        {
            Ret = SetHMC833FreqPower(0, 0);
            RetWarnning(Ret, "VSADown SetHMC833FreqPower failed!");
            (Ret == WT_OK) ? Ret = WriteLMX2594(0, LMX2594POWERDOWN) : WriteLMX2594(0, LMX2594POWERDOWN);
        }
        else if (m_HwInfo.LoHwVersion >= VERSION_B)
        {
            Ret = WriteLMX2820(LoMix, 0, LMX2820POWERDOWN);
            Ret |= WriteLMX2820(LoMod, 0, LMX2820POWERDOWN);
        }
        else
        {
            Ret = SetMixFreqPower(0, 0);
            Ret |= SetModFreqPower(0, 0);
        }
        m_RunData.Freq = 0;
    }
    m_RunData.Status2 = WT_RX_TX_STATUS_DOWN;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSA" << m_ModId << "  Down!--------" << std::endl;
#endif

    return Ret;
}

int DevVsa::VSASetTrig(const TriggerCfg &TrigCfg)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(VSA_SET_TRIGGER, sizeof(TriggerCfg), const_cast<TriggerCfg*>(&TrigCfg))) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_SET_TRIGGER ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevVsa::SetADCPowerDown(int PowerDown)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(SET_ADC_POWERDOWN, sizeof(int), &PowerDown)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_ADC_POWERDOWN ioctl error");
        return Ret;
    }

    return WT_OK;
}

int DevVsa::GetTrigAddrStart(int *StartAddr)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(GET_TRIG_ADDR_START, sizeof(int), StartAddr)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GET_TRIG_ADDR_START ioctl error");
        return Ret;
    }

    return WT_OK;
}

//ADC模数转换器
int DevVsa::WriteADCReg(int Addr, int Data)
{
    int Ret = WT_OK;
    RegType RegTypeTemp;
    RegTypeTemp.Addr = Addr;
    RegTypeTemp.Data = Data;
    //WTLog::Instance().WriteLog(LOG_DEBUG, "WriteADCReg Addr = %#x, Data = %#x\n", Addr, Data);
    if ((Ret = DrvCmd(WRITE_RF_ADC_DAC, sizeof(RegType), &RegTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "WRITE_RF_ADC_DAC ioctl error");
        return Ret;
    }
    return WT_OK;
}

int DevVsa::ReadADCReg(int Addr, int &Data)
{
    int Ret = WT_OK;
    RegType RegTypeTemp;

    RegTypeTemp.Addr = Addr;
    RegTypeTemp.Data = 0;
    
    if ((Ret = DrvCmd(READ_RF_ADC_DAC, sizeof(RegType), &RegTypeTemp)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "READ_RF_ADC_DAC ioctl error");
        return Ret;
    }

    Data = RegTypeTemp.Data;
    WTLog::Instance().WriteLog(LOG_DEBUG, "ReadADCReg Addr = %#x, Data = %#x\n", Addr, Data);
    return WT_OK;
}

int DevVsa::ADCInit()
{
    int Ret = WT_OK;
    int RegData;
    int RegAddr;
    Json::Value Reg = (m_TesterType == HW_WT418)?m_JsonRoot["VSA_ADC_Init_WT418"]:m_JsonRoot["VSA_ADC_Init"];

    if (Reg.isArray() && Reg.size() > 0)
    {
        for (unsigned i = 0; i < Reg.size(); i++)
        {
            RegAddr = std::strtol(Reg[i]["Addr"].asString().c_str(), 0, 0);
            RegData = std::strtol(Reg[i]["Data"].asString().c_str(), 0, 0);

            Ret = WriteADCReg(RegAddr, RegData);
            RetAssert(Ret, "ADCInit WriteADCReg failed!");
        }
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA ModId=" << m_ModId << "  ADCInit success!" << std::endl;
#endif
    }
    else
    {
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSA ModId=" << m_ModId << "  ADCInit failed!" << std::endl;
#endif
        return WT_ADC_INIT_FAILED;
    }
    return WT_OK;
}

int DevVsa::SetRXTrigLevelDigital(double TrigLevel)
{
    int Ret = WT_OK;
    double ss_iq = 0.0;                     //I、Q的平方和
    double v_p = 2.25 / 2.0;                //单位V
    double r = 50.0;                        //单位欧姆
    double scale = 2250.0 / 16384.0;
    double power_dac_max = 0.0;
    double power = 0.0;
    int TrigGate = 0;

    power_dac_max = 10 * log10(1000 * v_p * v_p / (r * 2));
    power = power_dac_max + TrigLevel;
    ss_iq = pow(10.0, power / 10) * 50000 / (scale * scale);
    TrigGate = ss_iq;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetRXTrigLevelDigital TrigGate = " << TrigGate << std::endl;
#endif

    if ((Ret = DrvCmd(SET_RX_TRIG_LEVEL_DIGITAL, sizeof(int), &TrigGate)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_RX_TRIG_LEVEL_DIGITAL ioctl error");
        return Ret;
    }
    return WT_OK;
}



// 重采样
int DevVsa::SetDCOffset(int Icode, int Qcode, bool cmd)
{
    if (m_RunData.DcOffsetI != Icode || m_RunData.DcOffsetQ != Qcode ||
        m_RunData.DcOffsetI == CODE_NO_INIT || m_RunData.DcOffsetQ == CODE_NO_INIT 
        || cmd)
    {
        int Ret = WT_OK;
        RXDCOffsetType RXDCOffsetTypeTemp;
        RXDCOffsetTypeTemp.Icode = Icode;
        RXDCOffsetTypeTemp.Qcode = Qcode;
        if ((Ret = DrvCmd(SET_RX_DC_OFFSET, sizeof(RXDCOffsetType), &RXDCOffsetTypeTemp)) != WT_OK)
        {
            m_RunData.DcOffsetI = CODE_NO_INIT;
            m_RunData.DcOffsetQ = CODE_NO_INIT;
            WTLog::Instance().LOGERR(Ret, "SET_RX_DC_OFFSET ioctl error");
            return Ret;
        }

        if (!cmd)
        {
            m_RunData.DcOffsetI = Icode;
            m_RunData.DcOffsetQ = Qcode;
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "SetDCOffset Icode=%d, Qcode=%d\n", Icode, Qcode);
    }
    // else
    // {
    //     WTLog::Instance().WriteLog(LOG_DEBUG, "SetDCOffset Icode=%d, Qcode=%d,  Same as last\n", Icode, Qcode);
    // }
    return WT_OK;
}

//快速AGC
int DevVsa::SetAGCEnable(int Enable)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(SET_AGC_ENABLE, sizeof(int), &Enable)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_AGC_ENABLE ioctl error");
        return Ret;
    }

    return WT_OK;
}

int DevVsa::SetAGCGateValue(int GateValue)
{
    int Ret = WT_OK;

    if ((Ret = DrvCmd(SET_AGC_GATE_VALUE, sizeof(int), &GateValue)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_AGC_GATE_VALUE ioctl error");
        return Ret;
    }

    return WT_OK;
}

int DevVsa::TBTApModeStart(int VsgUnitSel)
{
    int Ret = WT_OK;
    
    //设置TB模式及TRIG选择
    if ((Ret = DrvCmd(SET_TBT_AP_MODE, sizeof(int), &VsgUnitSel)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_TBT_AP_MODE ioctl error");
        return Ret;
    }
    return Ret;
}

int DevVsa::SetTBTStaParam(int Delay)
{
    int Ret = WT_OK;

    //设置TBT Sta模式参数
    if ((Ret = DrvCmd(VSA_SET_TBT_STA_PARAM, sizeof(int), &Delay)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_SET_TBT_STA_PARAM ioctl error");
        return Ret;
    }
    return Ret;
}

int DevVsa::SetDebugAtt(int Index, int Data)
{
    int Ret = WT_OK;
    if (Index == WT_DEV_RESPONSE_SW_ATT && Data >= 0 && Data <= BP_ATT_CODE_MAX)
    {
        m_GainParmDebug.rx_sw_gain.sw_rx_att_code[0] = Data;
    }
    else if(Index == WT_DEV_RESPONSE_SYNC)
    {
        m_GainParmDebug = m_GainParm;
    }
    else if ((Index >= WT_DEV_RESPONSE_RF_ATT0 && Index < (WT_DEV_RESPONSE_RF_ATT0 + RX_ATT_COUNT)) &&
             Data >= 0 &&
             Data <= ATT_CODE_MAX)
    {
        m_GainParmDebug.att_code[Index - WT_DEV_RESPONSE_RF_ATT0] = Data;
    }
    else if ((Index >= WT_ATT_CAL_RF_ATT0 && Index < (WT_ATT_CAL_RF_ATT0 + RX_ATT_COUNT)) &&
             Data >= 0 &&
             Data <= ATT_CODE_MAX)
    {
        int ATTId = Index - WT_ATT_CAL_RF_ATT0;
        m_GainParm.att_code[ATTId] = Data;

        if (m_RunData.ATTCurData[ATTId] != Data)
        {
            Ret = SetATTCode(ATTId, Data);
            RetAssert(Ret, "SetDebugAtt SetATTCode failed!");
            m_RunData.ATTCurData[ATTId] = Data;
        }
    }
    else if (Index == WT_ATT_CAL_SW_ATT && Data >= 0 && Data <= BP_ATT_CODE_MAX)
    {
        m_GainParm.rx_sw_gain.sw_rx_att_code[0] = Data;
        if (m_BackBoard->GetSwbAttCacheCode(m_RunData.RFPort) != Data)
        {
            Ret = m_BackBoard->SetSwbAttCode(m_RunData.RFPort, Data);
            RetAssert(Ret, "SetDebugAtt SetSwbAttCode failed!");
        }
    }
    else
    {
        Ret = WT_ARG_ERROR;
    }
    return Ret;
}

int DevVsa::GetDebugAtt(int Index, int &Data)
{
    int Ret = WT_OK;
    if (Index == WT_DEV_RESPONSE_SW_ATT)
    {
        Data = m_GainParmDebug.rx_sw_gain.sw_rx_att_code[0];
    }
    else if (Index >= WT_DEV_RESPONSE_RF_ATT0 && Index < (WT_DEV_RESPONSE_RF_ATT0 + TX_ATT_COUNT))
    {
        Data = m_GainParmDebug.att_code[Index - WT_DEV_RESPONSE_RF_ATT0];
    }
    else
    {
        Ret = WT_ARG_ERROR;
    }
    return Ret;
}

int DevVsa::SetLNAStatus(int Status)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "DevVsa::SetLNAStatus Status=%d\n", Status);
    int Ret = WT_OK;

    if (m_RunData.PaStatus != Status)
    {
        m_RunData.PaStatus = -1;
        Ret = DrvCmd(SET_RF_LNA, sizeof(Status), &Status);
        RetAssert(Ret, "SetLNAStatus failed!");
        m_RunData.PaStatus = Status;
    }

    return WT_OK;
}

int DevVsa::SetIqImbConfig(double GainComp, double PhaseError, double TimeSkew)
{
    int Ret = WT_OK;
    if (m_TesterType != HW_WT418 && m_HwInfo.BBHwVersion < VERSION_B)
    {
        return WT_OK;
    }

#define BUF_LEN 255
    //计算参数由FPGA提供
    int DataLen = 24;
    int FInterDelay = 12;
    double FRealDelay = FInterDelay;
    double FImagDelay = FInterDelay + TimeSkew * DEFAULT_SMAPLE_RATE / MHz;
    double FRealFilterCoef[BUF_LEN] = {0};
    double FImagFilterCoef[BUF_LEN] = {0};
    double CDataReg[BUF_LEN] = {0};
    (void)CDataReg;

    for (int i = 0; i < DataLen; i++)
    {
        FRealFilterCoef[i] = FImagFilterCoef[i] = 1.0;
        for (int j = 0; j < DataLen; j++)
        {
            if (i == j)
            {
                continue;
            }
            else
            {
                FRealFilterCoef[i] = FRealFilterCoef[i] * ((FRealDelay - j) / (i - j));
                FImagFilterCoef[i] = FImagFilterCoef[i] * ((FImagDelay - j) / (i - j));
            }
        }
    }

    // for (int i = 0; i < DataLen; i++)
    // {
    //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << PoutN(i) << PoutN(FRealFilterCoef[i]) << PoutN(FImagFilterCoef[i]) << "\n";
    // }

    IqImbConfigType IqImbConfig;
    memset(&IqImbConfig, 0, sizeof(IqImbConfigType));

    double PhaseDet = PhaseError * 3.1415926535897932384626 / 180;
    IqImbConfig.PhaseA = floor(pow(2, 13) * 1 / cos(PhaseDet));
    IqImbConfig.PhaseB = floor(pow(2, 13) * sin(PhaseDet) / cos(PhaseDet));
    IqImbConfig.AmpComp = floor(pow(2, 13) * 1 / pow(10, (GainComp / 20)));
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetIqImbConfig" << PoutN(GainComp) << PoutN(PhaseError) << PoutN(TimeSkew)
              << PoutN(IqImbConfig.AmpComp) << PoutN(PhaseDet) << PoutN(IqImbConfig.PhaseA) << PoutN(IqImbConfig.PhaseB) << std::endl;
    if((Ret = DrvCmd(SET_IQ_IMB_COMP, sizeof(IqImbConfigType), &IqImbConfig)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SET_IQ_IMB_COMP failed!");
        return Ret;
    }
    return WT_OK;
}

int DevVsa::VSAGetIQCode(int &GainCode)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(GET_ATT_CAL_RESULT, sizeof(int), &GainCode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_SET_TBT_STA_PARAM ioctl error");
        return Ret;
    }
    return Ret;
}

int DevVsa::GetResultSIFS(std::vector<double> &SIFS)
{
#define TBT_MODE_RESULT_SIFS (0X002D << 2)
    int Ret = WT_OK;

    int SifsHwComp = 0; //单位ns
    WTConf Conf(WTConf::GetDir() + "/triggerbase.conf");
    GET_CONF_DATA(Conf, "SifsHwCompensate", SifsHwComp, 0);

    SIFS.resize(32);
    int RData = 0;
    double HwSifs = 0;
    double Comp = SifsHwComp / 1e9;
    for (int i = 0; i < 32; i++)
    {
        Ret |= ReadDirectReg(TBT_MODE_RESULT_SIFS, RData);
        HwSifs = (double)RData / DEFAULT_SMAPLE_RATE;

        SIFS[i] = HwSifs + Comp;
        SIFS[i] = SIFS[i] < 0 ? 0 : SIFS[i];
        WTLog::Instance().WriteLog(LOG_DEBUG, "GetResultSIFS Mod[%d] SIFS[%d]=CODE:%d,HwSifs:%lf,Comp:%lf,Result:%lf\n",
               m_ModId, i, RData, HwSifs, Comp, SIFS[i]);
    }
    return Ret;
}

int DevVsa::GetFpgaCapturePower(int &Power)
{
    int Ret = WT_OK;
    Power = 0;
    if ((Ret = DrvCmd(GET_FPGA_CAPTURE_POWER, sizeof(int), &Power)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GetFpgaCapturePower ioctl error");
        return Ret;
    }

    return Ret;
}

int DevVsa::SetAnalogIQSW(int &AnalogIQSW)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(SET_ANALOGIQ_SWITCH, sizeof(int), &AnalogIQSW)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "SetAnalogIQSW ioctl error");
        return Ret;
    }
    m_AnalogIQSW = AnalogIQSW;
    return Ret;
}

int DevVsa::GetAnalogIQSW(int &AnalogIQSW)
{
    int Ret = WT_OK;
    AnalogIQSW = m_AnalogIQSW;
    // if ((Ret = DrvCmd(GET_ANALOGIQ_SWITCH, sizeof(int), &AnalogIQSW)) != WT_OK)
    // {
    //     WTLog::Instance().LOGERR(Ret, "GetAnalogIQSW ioctl error");
    //     return Ret;
    // }
    return Ret;
}

int DevVsa::IQModeInit()
{
    int Ret = WT_OK;
    //初始化共本振模式
    Json::Value AnalogIQMode = m_JsonRoot["AnalogIQ"];
    int AnalogIQSW = AnalogIQMode["IQMode"].asUInt() == static_cast<int>(ANALOGIQ_MODE)
                      ? static_cast<int>(ANALOGIQ_MODE)
                      : static_cast<int>(RFIQ_MODE);
    Ret = SetAnalogIQSW(AnalogIQSW);
    return Ret;
}

int DevVsa::SetParamList(std::vector<VSAConfigType> &VSAConfigList, std::vector<Rx_Parm> &RXParmList, std::vector<SeqTimeType> &SeqTime)
{
    int Ret = WT_OK;
    int RFPort = 0;
    int RFPortState = 0;
    int RFPortMode = WT_SW_STATE_MODE_SISO;
    TriggerCfg TriggerCfgTemp;
#if TIME_DEBUG
    struct timeval tpstart, tpend;
    gettimeofday(&tpstart, NULL);
#endif
    memcpy(&m_VSAConfigCache, &(VSAConfigList[0]), sizeof(VSAConfigType));//缓存临时数据

    InitlistSegConfig(VSAConfigList.size());

    // 强制设置第1个segment为信号触发
    // VSAConfigList[0].TrigType = WT_TRIG_TYPE_IF;

    for (int i = 0; i< VSAConfigList.size(); i++)
    {
        VSAConfigType &VSAConfig = VSAConfigList[i];
        Rx_Parm &RXParm = RXParmList[i];
        memset(&TriggerCfgTemp, 0, sizeof(TriggerCfgTemp));
        RFPort = VSAConfig.RFPort;
        RFPortState = VSAConfig.RFPortState;
        RXParm.rf_port = VSAConfig.RFPort;
        RXParm.freq = VSAConfig.Freq + VSAConfig.FreqOffsetHz;
        RXParm.ref_power = VSAConfig.Ampl;
        RXParm.ex_iq_mode = false;
        RXParm.noise_flag = VSAConfig.NoiseCompensation;
        RXParm.unit = m_ModId;
        RXParm.unit_mode = CAL_UNIT_MODE_SISO;
        RFPortMode = WT_SW_STATE_MODE_SISO;
        RXParm.sample_freq = VSAConfig.SamplingFreq;
        if(m_LOComMode == LO_IS_COM_MODE)
        {
            RXParm.share_mode = true;
        }
        else
        {
            RXParm.share_mode = false;
        }
        bool Is400M = false;
        if (!Basefun::CompareDoubleAccuracy1K(RXParm.freq, 400 * MHz))
        {
            Is400M = true;
            RXParm.freq += MHz;
        }
        //获取RX链路综合校准数据
        Ret = wt_calibration_get_rx_setting(&RXParm);
        if (Ret != WT_OK)
        {
            Ret += WT_CAL_BASE_ERROR;
        }
        RetAssert(Ret, "wt_calibration_get_rx_setting failed!");
        RFPortState = RXParm.rx_gain_parm.rx_sw_gain.sw_rx_link_state;
        if (Is400M)
        {
            RXParm.freq -= MHz;
            RXParm.freq_parm.LoParm[LoMod].freq = RXParm.freq_parm.LoParm[LoMod].freq > 0
                                                    ? RXParm.freq_parm.LoParm[LoMod].freq - 1
                                                    : RXParm.freq_parm.LoParm[LoMod].freq + 1;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSAConfig.ref power = " << VSAConfig.Ampl << std::endl;
        if(i == 1)
        {
            InitlistSegReglist();
        }
        if(i >= 1)
        {
            RecordlistSegConfig(i);
        }
        /**************************开关板板配置************************************/
#if DEVLIB_DEBUG
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Set  m_ModId = " << m_ModId << ", RXPort RF = " << RFPort
                  << ", State=" << RFPortState << ", Mode=" << RFPortMode << std::endl;
#endif
        // 校查开关板在位情况与该单元是否为80+80从机模式
        if (m_BackBoard->IsSwbExist(m_BackBoard->GetSwitchId(RFPort, m_ModId)))
        {
            // 当RF端口改变或端口功能改变时重新设置RF端口
            if ((m_RunData.RFPortState != RFPortState && m_RunData.RFPort != WT_RF_OFF) ||
                m_RunData.RFPortMode != RFPortMode ||
                m_RunData.RFPort != RFPort ||
                m_BackBoard->GetPortStatus(RFPort) != WT_RF_RX_STATUS)
            {
#if DEVLIB_DEBUG
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RX last set Port=" << m_RunData.RFPort << ", State=" << m_RunData.RFPortState << ", Mode=" << m_RunData.RFPortMode << std::endl;
#endif
                Ret = SetRFPort(RFPort, RFPortMode, RFPortState, static_cast<WT_SB_CONFIG_TYPE_E>(m_BackBoard->GetSBType()));
                ErrorLib::Instance().CheckErrCode(Ret, Ret);
                RetAssert(Ret, "VSA SetRXPort failed");
            }
        }
        /**************************射频板配置************************************/
        // 配置RX链路上的增益、本振、波段开关
        Ret = SetFreqAndGain(VSAConfig, RXParm);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "VSA SetRXFreqAndGain failed");
        /**************************基带板配置************************************/
        //配置重采样率
        Ret = SetResample(VSAConfig.SamplingFreq);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "VSA SetResample failed");

        // 下发时间参数
        Ret = SetSeqSegTime(SeqTime[i], VSAConfig.SamplingFreq);
        RetAssert(Ret, "SetSeqSegTime failed!");

        // 配置RX IQ IMB COMP
        // Ret = SetIqImbConfig(RXParm.rx_iq_imb_parm.gain_imb, RXParm.rx_iq_imb_parm.quad_err, RXParm.rx_iq_imb_parm.timeskew);
        // ErrorLib::Instance().CheckErrCode(Ret, Ret);
        // RetAssert(Ret, "VSA SetIqImbConfig failed");

        //配置RX DC offset
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Pout(VSAConfig.DcOffsetI) << Pout(RXParm.rx_dc_offset_parm.i_code)
                << Pout(VSAConfig.DcOffsetQ) << Pout(RXParm.rx_dc_offset_parm.q_code) << std::endl;
        Ret = SetDCOffset(RXParm.rx_dc_offset_parm.i_code, RXParm.rx_dc_offset_parm.q_code, false);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "VSA SetDCOffset failed");
        Rx_Gain_Parm &GainParm = (m_AttConfigMode == WT_ATT_CONFIG_MODE_CHECK)
                                    ? m_GainParmDebug
                                    : m_GainParm;

        if (m_AttConfigMode != WT_ATT_CONFIG_MODE_CONFIG)
        {
            //设置RX链路增益
            Ret = SetGain(GainParm);
            RetAssert(Ret, "SetGain failed!");
#if WT_BOOST_MANUAL
        //设置boost开关
        Ret = SetLNAStatus(GainParm.is_lna_on);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
#endif
        }

        //获取Trigger配置信息
        GetTrigCongfig(VSAConfig, TriggerCfgTemp);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        //配置VSA Trigger
        Ret = VSASetTrig(TriggerCfgTemp);
        ErrorLib::Instance().CheckErrCode(Ret, Ret);
        RetAssert(Ret, "VSA VSASetTrig failed");
        if (VSAConfig.TrigType != WT_TRIG_TYPE_FREE_RUN)
        {
            //设置数字触发的触发电平
            Ret = SetRXTrigLevelDigital(VSAConfig.TrigLevel);
            ErrorLib::Instance().CheckErrCode(Ret, Ret);
            RetAssert(Ret, "VSA SetRXTrigLevelDigital failed");
        }

        SetlistSegConfig(i, RFPort);

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << std::setprecision(4)
              << " gain_imb=" << RXParm.rx_iq_imb_parm.gain_imb
              << " quad_err=" << RXParm.rx_iq_imb_parm.quad_err
              << " timeskew=" << RXParm.rx_iq_imb_parm.timeskew
              << "\n"
              << " gain_imb_160m=" << RXParm.rx_iq_imb_parm_160m.gain_imb
              << " quad_err_160m=" << RXParm.rx_iq_imb_parm_160m.quad_err
              << " timeskew_160m=" << RXParm.rx_iq_imb_parm_160m.timeskew
              << "\n"
              << " gain_imb_320m=" << RXParm.rx_iq_imb_parm_320m.gain_imb
              << " quad_err_320m=" << RXParm.rx_iq_imb_parm_320m.quad_err
              << " timeskew_320m=" << RXParm.rx_iq_imb_parm_320m.timeskew << std::endl;
#endif
    }
#if TIME_DEBUG
    gettimeofday(&tpend, NULL);
    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "VSASetConfig Used Time:" << timeuse << "us" << std::endl;
#endif

    return WT_OK;
}

int DevVsa::StartList(int Mode)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(VSA_START, sizeof(int), &Mode)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSA_START ioctl error");
        return Ret;
    }

    if(m_ListMode)
    {
        m_ListModeNeedReset = 1;
    }

    m_RunData.XdmaStatus = WT_XDMA_INIT;
    m_RunData.Status = WT_RX_TX_STATUS_RUNNING;

#if DEVLIB_DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "--------VSA" << m_ModId << "  Start!--------" << std::endl;
#endif
    return WT_OK;
}

int DevVsa::ResetRundataFromCache()
{
    int Ret = WT_OK;
    Rx_Parm RXParm_Tmp;
    ModRunData ModRunData_Tmp;
    memcpy(&m_RunData, &ModRunData_Tmp, sizeof(ModRunData_Tmp));
    int code = 0;
    m_BackBoard->GetSwbAttCode(m_VSAConfigCache.RFPort, code, true);

    //printf("ResetRundataFromCache m_VSAConfigCache.RFPort=%d code=%d\n", m_VSAConfigCache.RFPort, code);

    Ret = SetParam(m_VSAConfigCache, RXParm_Tmp);
    return Ret;
}

int DevVsa::GetSeqProgress(int &Progress)
{
    int Ret = WT_OK;
    if ((Ret = DrvCmd(GET_SEQUENCE_SEGMENT_PROGRESS, sizeof(Progress), &Progress)) != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "GetSeqStat ioctl error");
        return Ret;
    }

    return WT_OK;
}

// int DevVsa::GetTrigCongfig2(const VSAConfigType &VSAConfig, const SeqTimeType SeqTimeParam, VSATriggerType &VSATriggerParam)
// {
//     int Ret = WT_OK;
// #if USE_DEFAULT_SAMPLINGFREQ
//     double TransSamplingFreq = DEFAULT_SMAPLE_RATE;
// #else
//     double TransSamplingFreq = VSAConfig.SamplingFreq;
// #endif

// #if 0
//     // HW_WT418的122.88M  61.44M  30.72MHz 属重采样，用 240M 采样率来算
//     if (m_TesterType == HW_WT418 &&
//         (Basefun::CompareDouble(VSAConfig.SamplingFreq, 122.88 * MHz, 0.1) == 0 ||
//         Basefun::CompareDouble(VSAConfig.SamplingFreq, 61.44 * MHz, 0.1)  == 0 ||
//         Basefun::CompareDouble(VSAConfig.SamplingFreq, 30.72 * MHz, 0.1) == 0))
//     {
//         TransSamplingFreq = DEFAULT_SMAPLE_RATE;
//     }
// #endif
//     //采样率
//     VSATriggerParam.SegmentSampleRate = TransSamplingFreq;
//     //设置采样时间，通过采样时间转换为采样点数
//     if (m_TesterType == HW_WT418 || m_HwInfo.BBHwVersion >= VERSION_B)
//     {
//         int Offset = 0;
//         //采集的数据要偏移，所以要额外采集更多数据点
//         if ((Ret = DrvCmd(GET_CAPTURE_OFFSET, sizeof(int), &Offset)) != WT_OK)
//         {
//             WTLog::Instance().LOGERR(Ret, "GET_CAPTURE_OFFSET ioctl error");
//             return Ret;
//         }
//         int ExtSamlpeCnt = Offset * floor(TransSamplingFreq / VSAConfig.SamplingFreq);
//         WTLog::Instance().WriteLog(LOG_DEBUG, "GetTrigCongfig ExtSamlpeCnt=%d, Offset=%d\n", ExtSamlpeCnt, Offset);
//         VSATriggerParam.SegmentSampleCnt = TransSamplingFreq * VSAConfig.SamplingTime + ExtSamlpeCnt;
//     }
//     else
//     {
//         VSATriggerParam.SegmentSampleCnt = TransSamplingFreq * VSAConfig.SamplingTime;
//     }

//     //修正RX Trigger模式,从IQV的模式枚举映射到硬件中的模式配置
//     m_RunData.TrigType = VSATriggerParam.TriggerType = VSAConfig.TrigType;

//     //设置触发预留采样点数
//     VSATriggerParam.TriggerOffset = TransSamplingFreq * VSAConfig.TrigPreTime;
//     VSATriggerParam.LeastGapLen = TransSamplingFreq * VSAConfig.TrigGapTime;

//     // 确保采集个数为偶数
//     if(VSATriggerParam.SegmentSampleCnt & 1)
//     {
//         VSATriggerParam.SegmentSampleCnt++;
//     }

//     VSATriggerParam.CommonTriggerParam.TriggerTimeout = VSAConfig.TrigTimeout * 1000;
//     VSATriggerParam.SegmentTriggerLevel = VSAConfig.TrigLevel;
//     VSATriggerParam.SegmentDuration = SeqTimeParam.Duration;
//     VSATriggerParam.DCValue = 0;//未定义
//     VSATriggerParam.LeastFrameLen = 0;//未定义
//     VSATriggerParam.CommonTriggerParam.VsaTriggerToVsgType;//未定义
//     VSATriggerParam.CommonTriggerParam.OutsideTriggerValidLen;//未定义

//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.TrigType=%d\n", VSATriggerParam.TriggerType);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.TriggerOffset = %d\n", VSATriggerParam.TriggerOffset);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.SegmentSampleCnt = %d\n", VSATriggerParam.SegmentSampleCnt);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.SegmentSampleRate = %d\n", VSATriggerParam.SegmentSampleRate);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.SegmentTriggerLevel = %d\n", VSATriggerParam.SegmentTriggerLevel);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.SegmentDuration = %d\n", VSATriggerParam.SegmentDuration);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.DCValue = %d\n", VSATriggerParam.DCValue);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.LeastGapLen = %d\n", VSATriggerParam.LeastGapLen);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.LeastFrameLen = %d\n", VSATriggerParam.LeastFrameLen);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.CommonTriggerParam.VsaTriggerToVsgType = %d\n", VSATriggerParam.CommonTriggerParam.VsaTriggerToVsgType);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.CommonTriggerParam.TriggerTimeout = %d\n", VSATriggerParam.CommonTriggerParam.TriggerTimeout);
//     WTLog::Instance().WriteLog(LOG_DEBUG, "Get VSATriggerParam.CommonTriggerParam.OutsideTriggerValidLen = %d\n", VSATriggerParam.CommonTriggerParam.OutsideTriggerValidLen);
//     return WT_OK;
// }

// inline unsigned int GetBitMask(unsigned int MaskLen)
// {
//     unsigned int mask = 0xFFFFFFFF;
//     mask >>=(32 - MaskLen);
//     return mask;
// }
// int DevVsa::VSASetTrig2(const std::vector<VSATriggerType> &TrigParam)
// {
//     int Ret = WT_OK;
//     char h2c_data_type = 0;//枚举类型

//     if (m_MapDmaBuf == NULL)
//     {
//         return WT_ALLOC_FAILED;
//     }

//     if (m_RunData.XdmaStatus == WT_XDMA_WR_FINISH)
//     {
//         // 驱动层对FPGA软件复位寄存器进行复位操作
//         int StopType = STOP_MODE_RESET;
//         if ((Ret = DrvCmd(VSA_STOP, sizeof(int), &StopType)) != WT_OK)
//         {
//             WTLog::Instance().LOGERR(Ret, "VSG_STOP ioctl error");
//             return Ret;
//         }
//     }

//     std::string DeviceName = Xdmafun::GetH2CDeviceName(m_ModId, 0);
//     int FpgaFd = open(DeviceName.c_str(), O_RDWR);
//     if (FpgaFd < 0)
//     {
//         // free(pPnItem);
//         WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "unable to open xdma c2h device");
//         return WT_FILE_OPEN_ERROR;
//     }
//     do
//     {
//         m_DmaDataSize = 0;
//         int tmpLen = 0;
//         DmaheaderBitType Headertemp;
//         Headertemp.h2cdatatype = 0 & GetBitMask(8);
//         Headertemp.reserved1 = 0 & GetBitMask(24);
//         Headertemp.reserved1 = 0 & GetBitMask(32);
//         Headertemp.reserved1 = 0 & GetBitMask(32);
//         Headertemp.reserved1 = 0 & GetBitMask(32);
//         tmpLen = (sizeof(Headertemp) + 15) & ~15;
//         if(m_DmaDataSize + tmpLen<= DMA_BUF_SIZE)
//         {
//             memcpy (&Headertemp, m_MapDmaBuf + m_DmaDataSize, tmpLen);
//             m_DmaDataSize += tmpLen;
//         }
//         else if (m_DmaDataSize != DMA_BUF_SIZE)
//         {
//             memcpy (&Headertemp, m_MapDmaBuf + m_DmaDataSize, DMA_BUF_SIZE - m_DmaDataSize);
//             m_DmaDataSize = DMA_BUF_SIZE;
//         }

//         VSATrigBitType VSATrigBittemp;
//         for(int i = 0; i < TrigParam.size(); i++)
//         {
//             VSATrigBittemp.TriggerType = TrigParam[i].TriggerType & GetBitMask(4);
//             VSATrigBittemp.TriggerOffset = TrigParam[i].TriggerOffset & GetBitMask(25);
//             VSATrigBittemp.SegmentSampleCnt = TrigParam[i].SegmentSampleCnt & GetBitMask(28);
//             VSATrigBittemp.SegmentSampleRate = TrigParam[i].SegmentSampleRate & GetBitMask(4);
//             VSATrigBittemp.SegmentTriggerLevel = TrigParam[i].SegmentTriggerLevel & GetBitMask(32);
//             VSATrigBittemp.SegmentDuration = TrigParam[i].SegmentDuration & GetBitMask(28);
//             VSATrigBittemp.DCValue = TrigParam[i].DCValue & GetBitMask(32);
//             VSATrigBittemp.LeastGapLen = TrigParam[i].LeastGapLen & GetBitMask(22);
//             VSATrigBittemp.LeastFrameLen = TrigParam[i].LeastFrameLen & GetBitMask(22);
//             VSATrigBittemp.reserved1 = TrigParam[i].reserved1 & GetBitMask(27);
//             VSATrigBittemp.reserved2 = TrigParam[i].reserved2 & GetBitMask(32);
//             tmpLen = (sizeof(VSATrigBittemp) + 15) & ~15;
//             if(m_DmaDataSize + tmpLen<= DMA_BUF_SIZE)
//             {
//                 memcpy (&VSATrigBittemp, m_MapDmaBuf + m_DmaDataSize, tmpLen);
//                 m_DmaDataSize += tmpLen;
//             }
//             else if (m_DmaDataSize != DMA_BUF_SIZE)
//             {
//                 memcpy (&VSATrigBittemp, m_MapDmaBuf + m_DmaDataSize, DMA_BUF_SIZE - m_DmaDataSize);
//                 m_DmaDataSize = DMA_BUF_SIZE;
//             }
//         }

//         // 下发XDMA传输的长度
//         if ((Ret = DrvCmd(VSG_SET_PN_ITEM, PnItemVector.size(), pPnItem.get())) != WT_OK)
//         {
//             WTLog::Instance().LOGERR(Ret, "VSG_SET_PN_ITEM ioctl error");
//             break;
//         }

//         ssize_t rc = 0;
//         rc += Xdmafun::Instance().WriteFromBuffer(DeviceName.c_str(), FpgaFd, m_MapDmaBuf, m_DmaDataSize, 0);
//         WTLog::Instance().WriteLog(LOG_DEBUG, "VSG%d XDMA write size=%d, actual size=%ld\n", m_ModId, m_DmaDataSize, rc);

//         int Status = 0;
//         int WaitCnt = 0;     //等待计数，最多等待1S
//         do
//         {
//             usleep(10);
//             if ((Ret = DrvCmd(GET_XDMA_STATUS, sizeof(Status), &Status)) != WT_OK)
//             {
//                 WTLog::Instance().LOGERR(Ret, "GET_XDMA_STATUS failed");
//                 break;
//             }
//         } while (Status == WT_RX_TX_STATE_RUNNING && ++WaitCnt < 100000);

//         if (Ret != WT_OK || Status != WT_RX_TX_STATE_DONE)
//         {
//             Ret = WT_XMDA_STATUS_TIMEOUT;
//             RetBreak(Ret, "SetPNItem wait fpga xdma finish timeout");
//         }
//         else if (rc < 0 || rc != m_DmaDataSize)
//         {
//             Ret = WT_XMDA_WR_ERROR;
//             RetBreak(Ret, "SetPNItem write Xdma error");
//         }
//     } while (0);
//     m_RunData.XdmaStatus = WT_XDMA_WR_FINISH;
//     close(FpgaFd);
//     return Ret;
// }
