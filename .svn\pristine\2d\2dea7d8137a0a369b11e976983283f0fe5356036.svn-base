#include "scpi_listmod.h"
#include "basehead.h"
#include "commonhandler.h"
#include "listmod_sequence.h"
#include "tester.h"
#include "wtlog.h"
#include "wterror.h"

scpi_result_t SCPI_SetListModEnable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    //EMPTY_PARAM_ERROR(context);
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->m_List == nullptr)
    {
        attr->m_List.reset(new ListSeq());
    }

    Ret = WT_SetListModeEnable(attr->ConnID);
    IF_ERR_RETURN(Ret);
 
    attr->m_List->SetListModEnable();
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListModDisable(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    attr->m_List.reset(nullptr);
    attr->m_List = nullptr;

    Ret = WT_SetListModeDisable(attr->ConnID);
    IF_ERR_RETURN(Ret);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqStart(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int SegNo = 0;
    int ParamsSize = 0;
    int AlzType = WT_ALZ_PARAM_FFT;
    AnalyzeParam *AlzParam = nullptr;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->m_List->GetListModScen() != LISTSCEN_NONECOMB)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart ListModScen=" << attr->m_List->GetListModScen() << std::endl;
        Ret = WT_LIST_TXSEQ_START_SCEN_NOT_MATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListModEnable() == false || attr->m_List->GetListModSeqSize(SEQUENCETX) == 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart TxSeqSize=" << attr->m_List->GetListModSeqSize(SEQUENCETX) << " ListmodEnable=" << attr->m_List->GetListModEnable() << std::endl;
        Ret = WT_LIST_TXSEQ_START_LISTNOENABLE_OR_TXSEQSIZE_IS_ZERO;
    }
    IF_ERR_RETURN(Ret);

    //下发scpi缓存的相关seq配置到server
    auto BeginIt = attr->m_List->GetSegBeginIter(SEQUENCETX);
    auto EndIt = attr->m_List->GetSegEndIter(SEQUENCETX);
    while (BeginIt != EndIt)
    {
        if (BeginIt->get() == nullptr)
        {
            continue;
        }
        if (BeginIt->get()->TxFlag != true)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart is not tx Seg  SegNo=" << SegNo << " TxFlag=" << BeginIt->get()->TxFlag << std::endl;
            Ret = WT_LIST_TXSEQ_START_LIST_SEG_ISNOT_TX;
        }
        IF_ERR_RETURN(Ret);

        //发送每个seg的抓取参数到server
        Ret = WT_SetSegVsaCapParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的分析参数到server
        Ret = WT_SetSegVsaAlzCommParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaAlzParam.commonAnalyzeParam));
        IF_ERR_RETURN(Ret);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqStart SegNo=" << SegNo << " attr->vsaParam.Demode=" << BeginIt->get()->tx_seg.vsaParam.Demode << std::endl;
        switch (BeginIt->get()->tx_seg.vsaParam.Demode)
        {
        case WT_DEMOD_CW:
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft;
            break;
        case WT_DEMOD_BT:
            AlzType = WT_ALZ_PARAM_BT;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt;
            break;
        case WT_DEMOD_ZIGBEE:
            AlzType = WT_ALZ_PARAM_ZIGBEE;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee;
            break;
        case WT_DEMOD_GLE:
            AlzType = WT_ALZ_PARAM_GLE;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink;
            break;
        case ALG_3GPP_STD_GSM:
        case ALG_3GPP_STD_WCDMA:
        case ALG_3GPP_STD_5G:
        case ALG_3GPP_STD_4G:
        case ALG_3GPP_STD_NB_IOT:
            AlzType = WT_ALZ_PARAM_3GPP;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP;
            break;
        case WT_DEMOD_LRWPAN_FSK:
        case WT_DEMOD_LRWPAN_OQPSK:
        case WT_DEMOD_LRWPAN_OFDM:
            AlzType = WT_ALZ_PARAM_WSUN;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun;
            break;
        case WT_DEMOD_ZWAVE:
            AlzType = WT_ALZ_PARAM_ZWAVE;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave;
            break;
        default:
            AlzType = WT_ALZ_PARAM_WIFI;
            ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi);
            AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi;
            break;
        }
        Ret = WT_SetSegVsaAlzProtoParam(attr->ConnID, SegNo, AlzType, AlzParam, ParamsSize);
        IF_ERR_RETURN(Ret);

        //发送每个seg的时间参数到server
        Ret = WT_SetSeqTxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.SegTimeParam), sizeof(SeqTimeParam));
        IF_ERR_RETURN(Ret);

        BeginIt++;
        SegNo++;
    }

    //启动tx seq
    Ret = WT_SetListTxSeqStart(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETX, SEQUENCERUNNING);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqStart(scpi_t *context)
{

    int Ret = WT_ERR_CODE_OK;
    int SegNo = 0;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->m_List->GetListModScen() != LISTSCEN_NONECOMB)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqStart ListModScen=" << attr->m_List->GetListModScen() << std::endl;
        Ret = WT_LIST_RXSEQ_START_SCEN_NOT_MATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListModEnable() == false || attr->m_List->GetListModSeqSize(SEQUENCERX) == 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqStart RxSeqSize=" << attr->m_List->GetListModSeqSize(SEQUENCERX) << " ListmodEnable=" << attr->m_List->GetListModEnable() << std::endl;
        Ret = WT_LIST_RXSEQ_START_LISTNOENABLE_OR_RXSEQSIZE_IS_ZERO;
    }
    IF_ERR_RETURN(Ret);

    //下发scpi缓存的相关seq配置到server
    auto BeginIt = attr->m_List->GetSegBeginIter(SEQUENCERX);
    auto EndIt = attr->m_List->GetSegEndIter(SEQUENCERX);
    while (BeginIt != EndIt)
    {
        if (BeginIt->get() == nullptr)
        {
            continue;
        }
        if (BeginIt->get()->TxFlag == true)
        {
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqStart is not rx Seg  SegNo=" << SegNo << " TxFlag=" << BeginIt->get()->TxFlag << std::endl;
            Ret = WT_LIST_RXSEQ_START_LIST_SEG_ISNOT_RX;
        }
        IF_ERR_RETURN(Ret);

        //发送每个seg的wave参数到server
        Ret = WT_SetSegVsgWaveParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.waveParam));
        IF_ERR_RETURN(Ret);

        //发送每个seg的vsg参数到server
        Ret = WT_SetSegVsgParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.vsgParam));
        IF_ERR_RETURN(Ret);


        //发送每个seg的时间参数到server
        Ret = WT_SetSeqRxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.SegTimeParam), sizeof(SeqTimeParam));
        IF_ERR_RETURN(Ret);

        BeginIt++;
        SegNo++;
    }

    //启动tx seq
    Ret = WT_SetListRxSeqStart(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCERX, SEQUENCERUNNING);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxRxSeqStart(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    int SegNo = 0;
    int ParamsSize = 0;
    int AlzType = WT_ALZ_PARAM_FFT;
    AnalyzeParam *AlzParam = nullptr;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    if (attr->m_List->GetListModScen() != LISTSCEN_COMB)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxRxSeqStart ListModScen=" << attr->m_List->GetListModScen() << std::endl;
        Ret = WT_LIST_TXRXSEQ_START_SCEN_NOT_MATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListModEnable() == false || attr->m_List->GetListModSeqSize(SEQUENCETXRX) == 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxRxSeqStart TxRxSeqSize=" << attr->m_List->GetListModSeqSize(SEQUENCETXRX) << " ListmodEnable=" << attr->m_List->GetListModEnable() << std::endl;
        Ret = WT_LIST_TXRXSEQ_START_LISTNOENABLE_OR_RXSEQSIZE_IS_ZERO;
    }
    IF_ERR_RETURN(Ret);

    //下发scpi缓存的相关seq配置到server
    auto BeginIt = attr->m_List->GetSegBeginIter(SEQUENCETXRX);
    auto EndIt = attr->m_List->GetSegEndIter(SEQUENCETXRX);
    while (BeginIt != EndIt)
    {
        if (BeginIt->get() == nullptr)
        {
            continue;
        }
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxRxSeqStart SegNo=" << SegNo << " TxFlag=" << BeginIt->get()->TxFlag << std::endl;
        if (BeginIt->get()->TxFlag == true)
        {
            //发送每个seg的抓取参数到server
            Ret = WT_SetSegVsaCapParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaParam));
            IF_ERR_RETURN(Ret);

            //发送每个seg的分析参数到server
            Ret = WT_SetSegVsaAlzCommParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.vsaAlzParam.commonAnalyzeParam));
            IF_ERR_RETURN(Ret);
            switch (attr->vsaParam.Demode)
            {
            case WT_DEMOD_CW:
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamFft;
                break;
            case WT_DEMOD_BT:
                AlzType = WT_ALZ_PARAM_BT;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamBt;
                break;
            case WT_DEMOD_ZIGBEE:
                AlzType = WT_ALZ_PARAM_ZIGBEE;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZigBee;
                break;
            case WT_DEMOD_GLE:
                AlzType = WT_ALZ_PARAM_GLE;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamSparkLink;
                break;
            case ALG_3GPP_STD_GSM:
            case ALG_3GPP_STD_WCDMA:
            case ALG_3GPP_STD_5G:
            case ALG_3GPP_STD_4G:
            case ALG_3GPP_STD_NB_IOT:
                AlzType = WT_ALZ_PARAM_3GPP;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParam3GPP;
                break;
            case WT_DEMOD_LRWPAN_FSK:
            case WT_DEMOD_LRWPAN_OQPSK:
            case WT_DEMOD_LRWPAN_OFDM:
                AlzType = WT_ALZ_PARAM_WSUN;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWiSun;
                break;
            case WT_DEMOD_ZWAVE:
                AlzType = WT_ALZ_PARAM_ZWAVE;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamZWave;
                break;
            default:
                AlzType = WT_ALZ_PARAM_WIFI;
                ParamsSize = sizeof(BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi);
                AlzParam = (AnalyzeParam *)&BeginIt->get()->tx_seg.vsaAlzParam.analyzeParamWifi;
                break;
            }
            Ret = WT_SetSegVsaAlzProtoParam(attr->ConnID, SegNo, AlzType, AlzParam, ParamsSize);
            IF_ERR_RETURN(Ret);
    
            //发送每个seg的时间参数到server
            Ret = WT_SetSeqTxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->tx_seg.SegTimeParam), sizeof(SeqTimeParam));
            IF_ERR_RETURN(Ret);
        }
        else
        {
            //发送每个seg的wave参数到server
            Ret = WT_SetSegVsgWaveParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.waveParam));
            IF_ERR_RETURN(Ret);
    
            //发送每个seg的vsg参数到server
            Ret = WT_SetSegVsgParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.vsgParam));
            IF_ERR_RETURN(Ret);

            //发送每个seg的时间参数到server
            Ret = WT_SetSeqRxSegTimeParam(attr->ConnID, SegNo, &(BeginIt->get()->rx_seg.SegTimeParam), sizeof(SeqTimeParam));
            IF_ERR_RETURN(Ret);
        }

        BeginIt++;
        SegNo++;
    }

    //启动tx seq
    Ret = WT_SetListTxRxSeqStart(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETXRX, SEQUENCERUNNING);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqStop(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    Ret = WT_SetListTxSeqStop(attr->ConnID);
    IF_ERR_RETURN(Ret);

     attr->m_List->SetListSeqStat(SEQUENCETX, SEQUENCEOFF);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqStop(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    Ret = WT_SetListRxSeqStop(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCERX, SEQUENCEOFF);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxRxSeqStop(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    Ret = WT_SetListTxRxSeqStop(attr->ConnID);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqStat(SEQUENCETXRX, SEQUENCEOFF);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListSeqMod(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SeqScene = 0;

    if (!SCPI_ParamInt(context, &SeqScene, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (SeqScene != LISTSCEN_NONECOMB || SeqScene != LISTSCEN_COMB)
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCETX) == SEQUENCERUNNING || attr->m_List->GetListSeqStat(SEQUENCERX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListSeqMod when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListModScen((LISTSCEN)SeqScene);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqSize(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Size = 0;

    if (!SCPI_ParamInt(context, &Size, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCETX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListTxSeqSize when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMaxSize(SEQUENCETX, Size);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSize(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Size = 0;

    if (!SCPI_ParamInt(context, &Size, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCERX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListRxSeqSize when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMaxSize(SEQUENCERX, Size);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxRxSeqSize(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Size = 0;

    if (!SCPI_ParamInt(context, &Size, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    if (attr->m_List->GetListSeqStat(SEQUENCETX) == SEQUENCERUNNING || attr->m_List->GetListSeqStat(SEQUENCERX) == SEQUENCERUNNING)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Can not SCPI_SetListTxRxSeqSize when the seq is in running state" << std::endl;
        Ret = WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMaxSize(SEQUENCETXRX, Size);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqFreq(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Freq = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Freq, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            ILLEGAL_PARAM_RETURN(Freq > 8000 * MHz_API || Freq < 0)

            attr->m_List->SetListSeqFreq(SEQUENCETX, SegNum, Freq);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqFreqAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Freq = 0.0;

    if (!SCPI_ParamDouble(context, &Freq, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqFreqAll(SEQUENCETX, Freq);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqPower(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Power = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Power, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqPower(SEQUENCETX, SegNum, Power);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqPowerAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Power = 0.0;

    if (!SCPI_ParamDouble(context, &Power, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqPowerAll(SEQUENCETX, Power);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRfport(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Port = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Port, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

            attr->m_List->SetListSeqPort(SEQUENCETX, SegNum, Port);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRfportAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Port = 0.0;

    if (!SCPI_ParamDouble(context, &Port, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

    attr->m_List->SetListSeqPortAll(SEQUENCETX, Port);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqSampleRate(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double SampleRate = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &SampleRate, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

            attr->m_List->SetListSeqSampleRate(SEQUENCETX, SegNum, SampleRate);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqSampleRateAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double SampleRate = 0.0;

    if (!SCPI_ParamDouble(context, &SampleRate, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

    attr->m_List->SetListSeqSampleRateAll(SEQUENCETX, SampleRate);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqExtGain(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double ExtGain = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &ExtGain, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqExtGain(SEQUENCETX, SegNum, ExtGain);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqExtGainAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double ExtGain = 0.0;

    if (!SCPI_ParamDouble(context, &ExtGain, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqExtGainAll(SEQUENCETX, ExtGain);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqFreq(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Freq = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqFreq ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Freq, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            ILLEGAL_PARAM_RETURN(Freq > 8000 * MHz_API || Freq < 0)

            attr->m_List->SetListSeqFreq(SEQUENCERX, SegNum, Freq);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqFreqAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Freq = 0.0;

    if (!SCPI_ParamDouble(context, &Freq, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqFreqAll(SEQUENCERX, Freq);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqPower(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Power = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqPower ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Power, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqPower(SEQUENCERX, SegNum, Power);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqPowerAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Power = 0.0;

    if (!SCPI_ParamDouble(context, &Power, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqPowerAll(SEQUENCERX, Power);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRfport(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Port = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqRfport ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Port, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

            attr->m_List->SetListSeqPort(SEQUENCERX, SegNum, Port);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRfportAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Port = 0.0;

    if (!SCPI_ParamDouble(context, &Port, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(Port < WT_PORT_OFF || Port >= WT_PORT_MAX)

    attr->m_List->SetListSeqPortAll(SEQUENCERX, Port);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSampleRate(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double SampleRate = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqSampleRate ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &SampleRate, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
            ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

            attr->m_List->SetListSeqSampleRate(SEQUENCERX, SegNum, SampleRate);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqSampleRateAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double SampleRate = 0.0;

    if (!SCPI_ParamDouble(context, &SampleRate, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);
    ILLEGAL_PARAM_RETURN(SampleRate > MAX_SMAPLE_RATE_API || SampleRate < 0)

    attr->m_List->SetListSeqSampleRateAll(SEQUENCERX, SampleRate);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqExtGain(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double ExtGain = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqExtGain ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &ExtGain, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqExtGain(SEQUENCERX, SegNum, ExtGain);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqExtGainAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double ExtGain = 0.0;

    if (!SCPI_ParamDouble(context, &ExtGain, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqExtGainAll(SEQUENCERX, ExtGain);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqWave(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    char fileName[256] = {0};
    int SegNum = 0;
    size_t copyLen = 0;
    std::vector<std::string> low_name_list;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqWave ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqWave ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            memset(fileName, 0, sizeof(fileName));
            if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            Ret = check_waveform_exist_v2(context, fileName, low_name_list);
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListRxSeqWave(SegNum, low_name_list[0]);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqWaveAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    char fileName[256] = {0};
     size_t copyLen = 0;
    std::vector<std::string> low_name_list;

    if (!SCPI_ParamCopyText(context, fileName, sizeof(fileName) - 1, &copyLen, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    Ret = check_waveform_exist_v2(context, fileName, low_name_list);
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListRxSeqWaveAll(low_name_list[0]);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqDuration(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Duration = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Duration, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqDuration(SEQUENCETX, SegNum, Duration);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqDurationAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Duration = 0.0;

    if (!SCPI_ParamDouble(context, &Duration, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqDurationAll(SEQUENCETX, Duration);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqDuration(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Duration = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqDuration ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Duration, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqDuration(SEQUENCERX, SegNum, Duration);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqDurationAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Duration = 0.0;

    if (!SCPI_ParamDouble(context, &Duration, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqDurationAll(SEQUENCERX, Duration);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaoffset(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Meaoffset = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqMeaoffset ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqMeaoffset ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Meaoffset, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqMeaoffset(SEQUENCETX,SegNum, Meaoffset);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaoffsetAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Meaoffset = 0.0;

    if (!SCPI_ParamDouble(context, &Meaoffset, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMeaoffsetAll(SEQUENCETX, Meaoffset);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqGap(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Meaoffset = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqGap ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqGap ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Meaoffset, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqMeaoffset(SEQUENCERX,SegNum, Meaoffset);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqGapAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Meaoffset = 0.0;

    if (!SCPI_ParamDouble(context, &Meaoffset, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMeaoffsetAll(SEQUENCERX, Meaoffset);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaDur(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double MeaDur = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqMeaDur ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqMeaDur ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &MeaDur, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqMeaDur(SEQUENCETX,SegNum, MeaDur);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqMeaDurAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double MeaDur = 0.0;

    if (!SCPI_ParamDouble(context, &MeaDur, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqMeaDurAll(SEQUENCETX, MeaDur);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRepeat(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Repeat = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Repeat, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqRepeat(SEQUENCETX,SegNum, Repeat);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqRepeatAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Repeat = 0.0;

    if (!SCPI_ParamDouble(context, &Repeat, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqRepeatAll(SEQUENCETX, Repeat);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRepeat(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    double Repeat = 0.0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListRxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListRxSeqRepeat ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamDouble(context, &Repeat, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqRepeat(SEQUENCERX,SegNum, Repeat);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListRxSeqRepeatAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    double Repeat = 0.0;

    if (!SCPI_ParamDouble(context, &Repeat, true))
    {
       Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqRepeatAll(SEQUENCERX, Repeat);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqAnalDemod(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int AnalDemod = 0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_SetListTxSeqAnalDemod ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    if (context->parser_state.numberOfParameters % 2 != 0)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "The param number is not even number when SCPI_SetListTxSeqAnalDemod ParamNuber= " << context->parser_state.numberOfParameters << std::endl;
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (i % 2 == 0)
        {
            if (!SCPI_ParamInt(context, &SegNum, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);
        }
        else
        {
            if (!SCPI_ParamInt(context, &AnalDemod, true))
            {
                Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            IF_ERR_RETURN(Ret);

            attr->m_List->SetListSeqAnalDemod(SEQUENCERX,SegNum, AnalDemod);
        }
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_SetListTxSeqAnalDemodAll(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int AnalDemod = 0;

    if (!SCPI_ParamInt(context, &AnalDemod, true))
    {
        Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
    }
    IF_ERR_RETURN(Ret);

    attr->m_List->SetListSeqAnalDemodAll(SEQUENCETX, AnalDemod);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DeleteListTxSeqSeg(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_DeleteListTxSeqSeg ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &SegNum, true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
        attr->m_List->DeleteListSeqSeg(SEQUENCETX, SegNum);
    }

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DeleteListTxSeqSegAll(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    attr->m_List->DeleteListSeqSegAll(SEQUENCETX);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_DeleteListRxSeqSeg(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int i = 0;
    int SegNum = 0;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SCPI_DeleteListRxSeqSeg ParamNuber= " << context->parser_state.numberOfParameters << std::endl;

    for (i = 0; i < context->parser_state.numberOfParameters; i++)
    {
        if (!SCPI_ParamInt(context, &SegNum, true))
        {
            Ret = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        IF_ERR_RETURN(Ret);
        attr->m_List->DeleteListSeqSeg(SEQUENCERX,SegNum);
    }

    return SCPI_ResultOK(context);

}

scpi_result_t SCPI_DeleteListRxSeqSegAll(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

    attr->m_List->DeleteListSeqSegAll(SEQUENCETX);

    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int Stat = SEQUENCEOFF;

    Ret = WT_GetListTxSeqAllState(attr->ConnID, (int *)&Stat);
    IF_ERR_RETURN(Ret);

    if ((SEQUENCESTATE)Stat == SEQUENCEREADY)
    {
        SCPI_ResultText(context, "Ready");
    }
    else
    {
        SCPI_ResultText(context, "Running");
    }

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListRxSeqState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListRxSeqAllState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    SEQUENCESTATE Stat = SEQUENCEOFF;

    Ret = WT_GetListRxSeqAllState(attr->ConnID, (int *)&Stat);
    IF_ERR_RETURN(Ret);

    if (Stat == SEQUENCEREADY)
    {
        SCPI_ResultText(context, "Ready");
    }
    else
    {
        SCPI_ResultText(context, "Running");
    }

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListTxRxSeqState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxRxSeqAllState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqCapState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllCapState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNum[2] = {0};

    Ret = WT_GetListTxSeqAllCapState(attr->ConnID, SegNum);
    IF_ERR_RETURN(Ret);

     SCPI_ResultInt(context, SegNum[0]);
     SCPI_ResultInt(context, SegNum[1]);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListRxSeqTransState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListRxSeqAllTransState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNum[2] = {0};

    Ret = WT_GetListRxSeqAllTransState(attr->ConnID, SegNum);
    IF_ERR_RETURN(Ret);

     SCPI_ResultInt(context, SegNum[0]);
     SCPI_ResultInt(context, SegNum[1]);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListTxSeqAnalyState(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllAnalyState(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int SegNum[2] = {0};

    Ret = WT_GetListTxSeqAllAnalyState(attr->ConnID, SegNum);
    IF_ERR_RETURN(Ret);

     SCPI_ResultInt(context, SegNum[0]);
     SCPI_ResultInt(context, SegNum[1]);

    return SCPI_RES_OK;
}

scpi_result_t SCPI_GetListTxSeqPowerResult(scpi_t *context)
{
    return SCPI_ResultOK(context);
}

scpi_result_t SCPI_GetListTxSeqAllPowerResult(scpi_t *context)
{
    int Ret = WT_ERR_CODE_OK;
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    std::unique_ptr<double []> Power;
    int SegNum = 0;
    double *PowerResult = nullptr;
    int i;

    SegNum = attr->m_List->GetListModSeqSize(SEQUENCETX);
    Power.reset(new double[SegNum]);
    PowerResult = Power.get();

    Ret = WT_GetListTxSeqAllPowerResult(attr->ConnID, PowerResult, SegNum);
    IF_ERR_RETURN(Ret);

    for (i = 0; i < SegNum; i++)
    {
         SCPI_ResultDouble(context, PowerResult[i]);
    }

    return SCPI_RES_OK;
}

