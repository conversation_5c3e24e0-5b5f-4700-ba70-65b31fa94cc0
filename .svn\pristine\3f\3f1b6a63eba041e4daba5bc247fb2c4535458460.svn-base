//*****************************************************************************
//  File: launch.h
//  创建unix socket和eventfd资源，然后启动WT-Link和WT-Server进程
//  Data: 2016.7.11
//*****************************************************************************

#include "launch.h"
#include <sys/types.h>
#include <sys/eventfd.h>
#include <sys/socket.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>
#include <unistd.h>
#include <cstdlib>
#include <cstdio>
#include <fstream>
#include <cerrno>

#include "wterror.h"
#include "conf.h"
#include "wtlog.h"
#include "license.h"
#include "tunnel.h"

using namespace std;

static const int MAX_SERVER_NUM = 16;  //最大子仪器数量

Launcher &Launcher::Instance()
{
    static Launcher obj;
    return obj;
}

Launcher::Launcher() : m_ServerCnt(0)
{
    m_Path = WTConf::GetDir();
}

Launcher::~Launcher()
{
    close(m_MgrSocket);

    kill(m_LinkPid, SIGINT);
    waitpid(m_LinkPid, NULL, WNOHANG);

    for (auto pid : m_ServerPid)
    {
        kill(pid, SIGINT);
        waitpid(pid, NULL, WNOHANG);
    }

    for (auto Sock : m_ServerSocket)
    {
        close(Sock);
    }

    close(m_ScpiEventfd);
}

int Launcher::LauchProcess(void)
{
    int Ret = WT_OK;
    int ServerCnt = 0;
    int LicNoMatchFlag = 0;

    signal(SIGCHLD, SIG_IGN);  //由内核处理子进程的回收，避免产生僵死进程

    //manager启动时自检测，包括检查license是否合法--和设备硬件是否匹配
    if(License::Instance().CheckLicense() != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, "License file illegal!");
        LicNoMatchFlag = 1;
    }
    GetServerCnt(ServerCnt);
    TunnelMgr::Instance().SetServerCnt(ServerCnt);

    Ret = CreateSocketPairs();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = LaunchLink();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = LaunchScpi();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = CreateEventfds();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    if(LicNoMatchFlag != 1) //license检测匹配后，启动server
    {
        m_ServerPid.assign(ServerCnt, 0);

        for (int i = 0; i < ServerCnt; i++)
        {
            Ret = LaunchServer(i);
            if (Ret != WT_OK)
            {
                Ret = WT_FORK_SERVER_FAILED;
                return Ret;
            }
        }
    }
    else                    //license illegal直接返回启动server失败，并不启动server
    {
        Ret = WT_FORK_SERVER_FAILED;
    }

    return Ret;
}

int Launcher::RestartServer(int ServerId)
{
    int Ret = kill(m_ServerPid[ServerId], SIGKILL);
    if (Ret < 0)
    {
        WTLog::Instance().LOGERR(Ret, "kill WT-Server failed");
    }
    Ret = ReCreateEventfds(ServerId);
    if (Ret < 0)
    {
        WTLog::Instance().LOGERR(Ret, "ReCreateEventfds failed");
    }

    Ret = m_ReRegisterServerHandle(ServerId);
    if (Ret < 0)
    {
        WTLog::Instance().LOGERR(Ret, "m_ReRegisterServerHandle failed");
    }

    return LaunchServer(ServerId);
}

int Launcher::ShutdownServers()
{
    for (auto pid : m_ServerPid)
    {
        kill(pid, SIGINT);
        waitpid(pid, NULL, WNOHANG);
    }
    return WT_OK;
}

int Launcher::RestartScpi()
{
    Basefun::LinuxSystem("pkill WT-Scpi-Server");
    char buf[256];
    snprintf(buf,256,"%s/WT-Scpi-Server %d &",m_Path.c_str(), m_ScpiEventfd);
    Basefun::LinuxSystem(buf);
    return WT_OK;
}

int Launcher::GetServerCnt(int &Cnt)
{
    if (m_ServerCnt > 0)
    {
        Cnt = m_ServerCnt;
    }
    else
    {
        // 从配置文件中读取子仪器配置获取子仪器数量
        int Ret = BaseConf::Instance().GetDevNum(m_ServerCnt);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "get dev num failed");
            m_ServerCnt = 1;
        }

        Cnt = m_ServerCnt;
    }

    return WT_OK;
}

int Launcher::GetServerSocket(int ServerId, int &Fd)
{
    Fd = m_ServerSocket[ServerId];

    return WT_OK;
}

int Launcher::GetServerEventfd(int ServerId, int &Fd)
{
    Fd = m_ServerEventfd[ServerId];

    return WT_OK;
}

int Launcher::GetScpiEventfd(int &Fd)
{
    Fd = m_ScpiEventfd;

    return WT_OK;
}

int Launcher::GetMgrSocket(void)
{
    return m_MgrSocket;
}

//启动WT-Link，fork之后子进程需要把自己不使用的socket和eventfd关闭，以免出现泄露和误操作
//WT-Link的参数顺序为：配置文件目录, manager socket, server 1 socket, server 2 socket, ...
int Launcher::LaunchLink(void)
{
    unsigned int i;
    string Path = m_Path + "/WT-Link";
    char buf[MAX_SERVER_NUM + 1][16];
    // ArgList[0] exe文件
    // ArgList[1] manager socket
    // ArgList[2] server 1 socket
    // ArgList[3] server 2 socket
    // ...
    // ArgList[n-1] server MAX_SERVER_NUM socket
    // ArgList[n] NULL;
#define ARG_SIZE ((MAX_SERVER_NUM) + 2 + 1)
    char *ArgList[ARG_SIZE];  

    pid_t pid = fork();
    if (pid < 0)
    {
        WTLog::Instance().LOGERR(errno, "LaunchLink fork failed");
        return WT_FORK_FAILED;
    }
    else if (pid == 0)
    {
        close(m_MgrSocket);

        for (auto Sock : m_ServerSocket)
        {
            close(Sock);
        }

        ArgList[0] = (char *)"WT-Link";
        ArgList[1] = const_cast<char *>(m_Path.c_str());

        for (i = 0; i < m_LinkSocket.size(); i++)
        {
            sprintf(buf[i], "%d", m_LinkSocket[i]);
            ArgList[i + 2] = buf[i];
        }

        ArgList[i + 2] = NULL;  // 注意for循环跳出前i已经自增了, 这里不能写成 i + 3

        execvp(Path.c_str(), ArgList);
        WTLog::Instance().LOGERR(errno, "LaunchLink execvp failed");
    }
    else
    {
        //父进程关闭不使用的socket，不关闭server socket的是因为后续重启server时需要使用
        for (auto Sock : m_LinkSocket)
        {
            close(Sock);
        }

        m_LinkPid = pid;
    }

    return WT_OK;
}

//启动WT-Server，fork之后子进程需要把自己不使用的socket和eventfd关闭，以免出现泄露和误操作
//WT-Server的参数顺序为：配置文件目录, id, socket, eventfd


int Launcher::LaunchServer(int ServerId)
{
    pid_t pid;
    char buf[3][16];
    string Path = m_Path + "/WT-Server";

    pid = fork();
    if (pid < 0)
    {
        WTLog::Instance().LOGERR(errno, "LaunchServer fork failed");
        return WT_FORK_FAILED;
    }
    else if (pid == 0)
    {
        close(m_MgrSocket);

        for (int j = 0; j < m_ServerCnt; j++)
        {
            if (ServerId != j)
            {
                close(m_ServerSocket[j]);
                close(m_ServerEventfd[j]);
            }
        }

        sprintf(buf[0], "%d", ServerId + 1);  //外部server编号从1开始
        sprintf(buf[1], "%d", m_ServerSocket[ServerId]);
        sprintf(buf[2], "%d", m_ServerEventfd[ServerId]);
        execlp(Path.c_str(), "WT-Server", m_Path.c_str(), buf[0], buf[1], buf[2], NULL);
        WTLog::Instance().LOGERR(errno, "exec WT-Server failed");
    }
    else
    {
        m_ServerPid[ServerId] = pid;
    }

    return WT_OK;
}

//创建unix socket pair用于WT-Link与WT-Manager, WT-Link与WT-Server进程通信
int Launcher::CreateSocketPairs(void)
{
    int SocketPairs[MAX_SERVER_NUM][2]={-1};

    for (int i = 0; i <= m_ServerCnt; i++)
    {
        if (socketpair(AF_UNIX, SOCK_STREAM, 0, SocketPairs[i]) < 0)
        {
            WTLog::Instance().LOGERR(errno, "socketpair failed");

            // 关闭i位置之前的所有socketpair(不包括i位置), 注意(i--)语法
            while (i--)
            {
                close(SocketPairs[i][0]);
                close(SocketPairs[i][1]);
            }

            return WT_CREATE_SOCKET_FAILED;
        }
    }

    m_MgrSocket = SocketPairs[0][0];
    m_LinkSocket.push_back(SocketPairs[0][1]);

    for (int i = 1; i <= m_ServerCnt; i++)
    {
        m_ServerSocket.push_back(SocketPairs[i][0]);
        m_LinkSocket.push_back(SocketPairs[i][1]);
    }

    return WT_OK;
}

//创建eventfd，用于WT-Server向WT-Manager上报状态
int Launcher::CreateEventfds(void)
{
    int Efds[MAX_SERVER_NUM];

    for (int i = 0; i < m_ServerCnt; i++)
    {
        Efds[i] = eventfd(0, 0);
        if (Efds[i] < 0)
        {
            WTLog::Instance().LOGERR(errno, "create eventfd failed");
            while (i--)
            {
                close(Efds[i]);
            }

            return WT_CREATE_EVENTFD_FAILED;
        }
    }

    for (int i = 0; i < m_ServerCnt; i++)
    {
        m_ServerEventfd.push_back(Efds[i]);
    }

    return WT_OK;
}

// 重新创建eventfd，用于WT-Server向WT-Manager上报状态
int Launcher::ReCreateEventfds(int Index)
{
    int Efds = eventfd(0, 0);
    if (Efds < 0)
    {
        WTLog::Instance().LOGERR(errno, "create eventfd failed");
        close(Efds);
        return WT_CREATE_EVENTFD_FAILED;
    }
    close(m_ServerEventfd[Index]);
    m_ServerEventfd[Index] = Efds;
    return WT_OK;
}

int Launcher::LaunchScpi(void)
{
    if((m_ScpiEventfd = eventfd(0, 0)) < 0)
    {
        WTLog::Instance().LOGERR(errno, "create eventfd failed");
        return WT_CREATE_EVENTFD_FAILED;
    }
    Basefun::LinuxSystem("pkill WT-Scpi-Server");
    char buf[256];
    snprintf(buf,256,"%s/WT-Scpi-Server %d &",m_Path.c_str(), m_ScpiEventfd);
    Basefun::LinuxSystem(buf);
    return WT_OK;
}
