//*****************************************************************************
//  File: wtlog.h
//  日志模块
//  Data: 2016.7.29
//*****************************************************************************
#ifndef __WT_LOG_H__
#define __WT_LOG_H__

#include <string>
#include <vector>
#include <mutex>
#include <sqlite3.h>
#include <thread>
#include "wtev++.h"
#include <sstream>
#include <iostream>
#include <unistd.h>
#include "concurrentqueue/blockingconcurrentqueue.h"

namespace level
{
    enum class Type : int
    {
        trace    = 0,
        debug    = 1,
        info     = 2,
        warn     = 3,
        err      = 4,
        critical = 5,
        operate  = 6,
        off      = 7,
        shutdown = 8,
        _count   = 9,
    };

    static constexpr std::array<const char*, static_cast<size_t>(Type::_count)> strings = {
        "trace", "debug", "info", "warn", "error", "critical", "operate", "off", "shutdown"
    };

    inline const char* ToString(int level) {
        return strings.at(level);
    }

    struct Config {
        int print = static_cast<int>(Type::err);
        int save = static_cast<int>(Type::err);
        static constexpr int keep_days    = 30;
        static constexpr int keep_records = 100000;
        static constexpr int keep_margin  = 101000;
        static constexpr int batch_size = 1000;
        static constexpr const char* log_tbl = "log";
        static constexpr const char* save_log_level = "SaveLogLevel";
        static constexpr const char* print_log_level = "PrintLogLevel";
    };

    extern Config config;

    inline bool IsPrintable(int level) noexcept {
        return level >= config.print;
    }

    inline bool IsSavable(int level) noexcept {
        return level >= config.save;
    }

    inline bool IsRecordable(int level) noexcept {
        return IsPrintable(level) || IsSavable(level);
    }
} // namespace level

struct LogRecord
{
    int Level;
    time_t  Time;
    int Threadid;
    const char* Filename;
    int FileLine;
    int ErrCode;
    std::string Content;
};

class DBManager
{
public:
    DBManager(const std::string& dbPath);
    ~DBManager();

    bool IsOpen() const { return m_IsOpen; }
    void WriteToDB(const LogRecord& record);

    DBManager() = delete;
    DBManager(const DBManager&) = delete;
    DBManager& operator=(const DBManager&) = delete;

private:
    void InitDB();
    bool TableExist(const std::string& tableName);
    void CreateTable(const std::string& tableName);
    void CleanLogByCount(const std::string& tableName, int count);
    void CleanLogByDate(const std::string& tableName);
    int GetLogCount(const std::string& tableName);
    void ExecuteBatch();

private:
    std::mutex m_dbMutex;
    std::string m_DbPath;
    bool m_IsOpen = false;
    int m_LogCount = 0;

    sqlite3 *m_Db = nullptr;
    sqlite3_stmt *m_InsertStmt = nullptr;
    std::vector<LogRecord> m_BatchRecords;
};

class WTLogStream;

class WTLog
{
public:
    static WTLog& Instance() {
        static WTLog instance;
        return instance;
    }

    static inline void SetLogDBName(const std::string &Name) {
        m_LogDbName = Name;
    }

    static inline void SetLogProcessName(const std::string &Name) {
        m_LogProcessName = Name;
    }

    inline void ErrLog(const char *File, int Line, int ErrCode, const std::string &Content)
    {
        if (level::IsRecordable(static_cast<int>(level::Type::err))) {
            m_Logqueue.enqueue(LogRecord{static_cast<int>(level::Type::err), 
                                       time(nullptr), 
                                       gettid(), 
                                       File, 
                                       Line, 
                                       ErrCode, 
                                       Content});
        }
    }

    inline void OperateLog(const char *File, int Line, const std::string &Content)
    {
        if (level::IsRecordable(static_cast<int>(level::Type::operate))) {
            m_Logqueue.enqueue(LogRecord{static_cast<int>(level::Type::operate), 
                                       time(nullptr), 
                                       gettid(), 
                                       File, 
                                       Line, 
                                       0, 
                                       Content});
        }
    }

    //*****************************************************************************
    // 查询指定时间段内的日志信息
    // 参数[IN] : BeginTime ：UTC起始时间，单位为us
    //            EndTime : UTC结束时间，单位为us
    //            Db : 查询指定的数据库文件，如果不指定则是查询当前的数据库
    // 参数[OUT]：Content :
    // 返回值: 无
    //*****************************************************************************
    int Query(long BeginTime, long EndTime, std::vector<std::string> &Content, const std::string &File = "");

    //*****************************************************************************
    // 通过SQL语句来直接查询日志信息
    // 参数[IN] : File ：查询指定的数据库db文件，如果不指定则是查询当前的数据库
    //            SqlStr : 协议下发的完整的sql
    // 参数[OUT]：Content :日志查询结果
    // 返回值: 无
    //*****************************************************************************
    int Query(const std::string &File, const std::string SqlStr, std::vector<std::string> &Content);

    template <typename... Args>
    void WriteLog(int type, const char *format, Args &&...args)
        __attribute__((format(printf, 3, 0)));

    template <typename... Args>
    void log(int level, const char *format, Args &&...args)
        __attribute__((format(printf, 3, 0)));

    template <typename... Args>
    void log(int level, int errcode, const char *file, 
            int line, const char *format, Args &&...args)
        __attribute__((format(printf, 6, 0)));

    // delete copy and move constructors and assign operators
    WTLog(WTLog const &) = delete;            // Copy construct
    WTLog(WTLog &&) = delete;                 // Move construct
    WTLog &operator=(WTLog const &) = delete; // Copy assign
    WTLog &operator=(WTLog &&) = delete;      // Move assign

    WTLogStream GettmpLogStream(int LogType = static_cast<int>(level::Type::debug));

private:
    WTLog();
    ~WTLog();

    void LogConsumerThread(void);
    void ProcessRecords(LogRecord* records, size_t count);
    void ProcessRemainLog();
    void PrintLog(const LogRecord &record);
    
    void StartInotifyWatcher();
    void ReloadConfig();
  
private:
    static std::string m_LogDbName;
    static std::string m_LogProcessName;
    std::unique_ptr<DBManager> m_DbManager;

    std::string m_confPath;

    bool m_Isrunning;
    moodycamel::BlockingConcurrentQueue<LogRecord> m_Logqueue;
    std::thread m_Logthread;
    
    // discard gradually
    sqlite3 *m_Db = nullptr;
    std::mutex m_Mutex;
};

class WTLogStream : public std::ostringstream
{
public:
    WTLogStream(WTLog *WtLog, int LogType)
        : m_WTLog(WtLog), 
          m_LogType(LogType)
    {
        str().reserve(256);
    };
    WTLogStream(const WTLogStream &LogStream)
        : m_WTLog(LogStream.m_WTLog), 
          m_LogType(LogStream.m_LogType) {};
    ~WTLogStream()
    {
        if (m_WTLog != nullptr && level::IsRecordable(m_LogType))
        {
            m_WTLog->WriteLog(m_LogType, (std::move(str())).c_str());
        }
    }

private:
    WTLog *m_WTLog = nullptr;
    int m_LogType;
};

template <typename... Args>
void WTLog::WriteLog(int type, const char *format, Args &&...args)
{
    if (level::IsRecordable(type))
    {
        switch (type)
        {
            case static_cast<int>(level::Type::err):
                log(static_cast<int>(level::Type::err), 
                    format, std::forward<Args>(args)...);
                break;
            case static_cast<int>(level::Type::operate):
                log(static_cast<int>(level::Type::operate), 
                    format, std::forward<Args>(args)...);
                break;
            default:
                log(static_cast<int>(level::Type::debug), 
                    format, std::forward<Args>(args)...);
        }
    }
}

template <typename... Args>
void WTLog::log(int level, const char *format, Args &&...args)
{
    const char *file = "";
    int line = 0;
    int errcode = 0;
    
    std::string content;
    if (sizeof...(Args) == 0) {
        content = format;
    } else {
        #pragma GCC diagnostic push
        #pragma GCC diagnostic ignored "-Wformat-security"
        const int size = snprintf(nullptr, 0, format, args...);
        content.resize(size + 1);
        snprintf(&content[0], content.size(), format, args...);
        #pragma GCC diagnostic pop
        content.resize(size);
    }
    m_Logqueue.enqueue(LogRecord{level, time(nullptr), gettid(), file, line, errcode, content});
}

template <typename... Args>
void WTLog::log(int level, int errcode, const char *file, 
        int line, const char *format, Args &&...args)
{
    std::string content;
    if (sizeof...(Args) == 0) {
        content = format;
    } else {
        #pragma GCC diagnostic push
        #pragma GCC diagnostic ignored "-Wformat-security"
        const int size = snprintf(nullptr, 0, format, args...);
        content.resize(size + 1);
        snprintf(&content[0], content.size(), format, args...);
        #pragma GCC diagnostic pop
        content.resize(size);
    }
    m_Logqueue.enqueue(LogRecord{level, time(nullptr), gettid(), file, line, errcode, content});
}

class SourceLocation
{
public:
    const char* file;
    int line;

    SourceLocation(const char* file, int line)
        : file(file), line(line) {}
};

#define SOURCE_LOCATION SourceLocation(__FILE__, __LINE__)

namespace wtlog {

template <typename... Args>
inline void trace(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::trace)))
        WTLog::Instance().log(static_cast<int>(level::Type::trace), 
                            format, std::forward<Args>(args)...);
}

template <typename... Args>
inline void debug(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::debug)))
        WTLog::Instance().log(static_cast<int>(level::Type::debug), 
                            format, std::forward<Args>(args)...);
}

template <typename... Args>
inline void info(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::info)))
        WTLog::Instance().log(static_cast<int>(level::Type::info), 
                            format, std::forward<Args>(args)...);
}

template <typename... Args>
inline void warn(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::warn)))
        WTLog::Instance().log(static_cast<int>(level::Type::warn), 
                            format, std::forward<Args>(args)...);
}

template <typename... Args>
inline void error(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::err)))
        WTLog::Instance().log(static_cast<int>(level::Type::err), 
                            format, std::forward<Args>(args)...);
}

template <typename... Args>
inline void error(const SourceLocation& loc, 
                  int errcode,
                  const char *format, 
                  Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::err)))
        WTLog::Instance().log(static_cast<int>(level::Type::err), 
                            errcode, 
                            loc.file, 
                            loc.line, 
                            format, 
                            std::forward<Args>(args)...);
}

template <typename... Args>
inline void critical(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::critical)))
        WTLog::Instance().log(static_cast<int>(level::Type::critical), 
                            format, std::forward<Args>(args)...);
}

template <typename... Args>
inline void critical(const SourceLocation& loc, 
                     int errcode,
                     const char *format, 
                     Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::critical)))
        WTLog::Instance().log(static_cast<int>(level::Type::critical), 
                            errcode, 
                            loc.file, 
                            loc.line, 
                            format, 
                            std::forward<Args>(args)...);
}

template <typename... Args>
inline void operate(const char *format, Args &&...args)
{
    if (level::IsRecordable(static_cast<int>(level::Type::operate)))
        WTLog::Instance().log(static_cast<int>(level::Type::operate), 
                            format, std::forward<Args>(args)...);
}

} // namespace wtlog

namespace { // old log macros, not recommended

#define LOGERR(ErrCode, Content) ErrLog(__FILE__, __LINE__, ErrCode, Content)
#define LOGOPERATE(Content) OperateLog(__FILE__, __LINE__, Content)

//检查返回值，不成功则跳出
#define CheckBreak(ErrCode) \
    if (ErrCode != WT_OK)   \
    break

//检查返回值，不成功则返回错误码
#define CheckRet(ErrCode) \
    if (ErrCode != WT_OK) \
    return ErrCode

// unused, discard gradually
enum WT_LOG_QUERY_TYPE
{
    QueryByServerID,
    QueryByHardware,
    QueryByTime,
    QueryByOperate,
    QueryAll,
    QueryBySQL,
};

// discard gradually
enum LOG_FUNC_TYPE
{
    LOG_DEBUG = 1,
    LOG_ERROR = 4,
    LOG_CMD_TRACE,   // CMD / ACK 的显示;
    LOG_AUTO_BAKING, // 自动烤机
    LOG_SUB_MGR,     // 子任务管理器
};

} // namespace

#endif
