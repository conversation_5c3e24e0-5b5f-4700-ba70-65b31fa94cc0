
#include "includes.h"
#include "common.h"
#include "wpa_debug.h"
#include "sms4.h"
#include "sms4_common.h"
#include "sms4_lcl.h"
#include "sms4_gcm.h"
#include "gcm.h"

static void * sms4_encrypt_init(const u8 *key, size_t len)
{
    sms4_key_t *sms4_key = (sms4_key_t*)malloc(sizeof(sms4_key_t));
    if (sms4_key == nullptr)
    {
        return nullptr;
    }

    memset(sms4_key, 0, sizeof(sms4_key_t));
    sms4_set_encrypt_key(sms4_key, key);
    wpa_hexdump(MSG_EXCESSIVE, "sms4 rk", sms4_key->rk, sizeof(sms4_key->rk));
    return sms4_key;
}


static void sms4_block_alg(u8* in, u8* out, void *ctx)
{
    sms4_encrypt(in, out, (const sms4_key_t*)ctx);
}

/**
* sms4_gcm_ae - GCM-AE_K(IV, P, A)
*/
int sms4_gcm_ae(const u8 *key, size_t key_len, const u8 *iv, size_t iv_len,
    const u8 *plain, size_t plain_len,
    const u8 *aad, size_t aad_len, u8 *crypt, u8 *tag)
{
    void *ctx = crypto_gcm_init(sms4_encrypt_init, key, key_len);
    if (ctx)
    {
        crypto_gcm_encrypt(ctx, sms4_block_alg, iv, iv_len, plain, plain_len, aad, aad_len, crypt, tag);
    }
    crypto_gcm_deinit(ctx);

    return 0;
}


/**
* sms4_gcm_ad - GCM-AD_K(IV, C, A, T)
*/
int sms4_gcm_ad(const u8 *key, size_t key_len, const u8 *iv, size_t iv_len,
    const u8 *crypt, size_t crypt_len,
    const u8 *aad, size_t aad_len, const u8 *tag, u8 *plain, u8* auth_tag)
{
    void *ctx = crypto_gcm_init(sms4_encrypt_init, key, key_len);
    int iRet = -1;
    if (ctx)
    {
        iRet = crypto_gcm_decrypt(ctx, sms4_block_alg, iv, iv_len, crypt, crypt_len, aad, aad_len, tag, plain, auth_tag);
    }
    crypto_gcm_deinit(ctx);

    return iRet;
}


int sms4_gmac(const u8 *key, size_t key_len, const u8 *iv, size_t iv_len,
    const u8 *aad, size_t aad_len, u8 *tag)
{
    return sms4_gcm_ae(key, key_len, iv, iv_len, NULL, 0, aad, aad_len, NULL,
        tag);
}

s32 sms4_gcm_authentication_encrypt(const u8 *tk, u32 tk_len, u8 *plain, u32 plain_len, u8 *crypt)
{
    sms4_key_t key;
    memcpy(key.rk, tk, sizeof(key.rk));
    return crypto_gcm_authentication_encrypt(&key, (u8*)tk, sms4_block_alg, plain, plain_len, crypt);
}