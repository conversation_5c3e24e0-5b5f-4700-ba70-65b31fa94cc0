#include "scpi_3gpp_alz_gsm.h"

#include <iostream>
#include <vector>
#include <tuple>

#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular::method;
using CommandRange = std::vector<std::tuple<int, int, int>>; // 命令参数, 最小值, 最大值

scpi_result_t cellular::alz::gsm::SetVsaGsmSlotOffset(scpi_t *context)
{
    int iRet = SetGsmVsaValueInRange(context, GetGSM(context).SlotOffset, 0, 7);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmNumbOfslot(scpi_t *context)
{
    int iRet = SetGsmVsaValueInRange(context, GetGSM(context).NumbOfSlot, 1, 8);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmMeasureSlot(scpi_t *context)
{
    int iRet = SetGsmVsaValueInRange(context, GetGSM(context).MeasureSlot, 0, 7);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmPvtFilter(scpi_t *context)
{
    int iRet = SetGsmVsaValueInRange(context, GetGSM(context).PvTFilter, 0, 1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmSpecModOffsetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).SpectMod.OffsetState))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).SpectMod.OffsetState[Idx], 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmSpecModFreqOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).SpectMod.FreqOffset))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).SpectMod.FreqOffset[Idx], 0.0, 3.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmSpecSwtOffsetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).SpectSwt.OffsetState))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).SpectSwt.OffsetState[Idx], 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmSpecSwtFreqOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).SpectSwt.FreqOffset))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).SpectSwt.FreqOffset[Idx], 0.0, 3.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmRmsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmRms.LimitValue, 0.0, 50.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmRmsCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmRms.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmRmsAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmRms.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmRmsMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmRms.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmPeakLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmPeak.LimitValue, 0.0, 50.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmPeakCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmPeak.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmPeakAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmPeak.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmPeakMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].EvmPeak.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmThreLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].Evm95Percent.Limit, 0.0, 50.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModEvmThreState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].Evm95Percent.State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrRmsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrRms.LimitValue, 0.0, 100.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrRmsCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrRms.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrRmsAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrRms.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrRmsMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrRms.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrPeakLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrPeak.LimitValue, 0, 100);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrPeakCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrPeak.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrPeakAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrPeak.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrPeakMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErrPeak.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrThreLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErr95Percent.Limit, 0, 100);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModMerrThreState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].MErr95Percent.State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherRmsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrRms.LimitValue, 0, 180);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherRmsCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrRms.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherRmsAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrRms.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherRmsMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrRms.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherPeakLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrPeak.LimitValue, 0, 180);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherPeakCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrPeak.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherPeakAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrPeak.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherPeakMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErrPeak.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherThreLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErr95Percent.Limit, 0, 180);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModPherThreState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].PhErr95Percent.State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqOffSetLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQOffset.LimitValue, -100, 0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqOffSetCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQOffset.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqOffSetAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQOffset.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqOffSetMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQOffset.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqImbLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQImbalance.LimitValue, -100, 0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqImbCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQImbalance.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqImbAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQImbalance.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModIqImbMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].IQImbalance.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModFreqErrLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].FreError.LimitValue, 0, 1000);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModFreqErrCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].FreError.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModFreqErrAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].FreError.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModFreqErrMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].FreError.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModTimeErrLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);
        
        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].TimeError.LimitValue, -1000, 1000);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModTimeErrCurrent(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].TimeError.Current, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModTimeErrAverage(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].TimeError.Average, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitModTimeErrMax(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.ModLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.ModLimit[Idx].TimeError.Max, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtAvgState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.AvgLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.AvgLimit[Idx].State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtFpcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.AvgLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.AvgLimit[Idx].FromPCL, 0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtTpcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.AvgLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.AvgLimit[Idx].ToPCL, 0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtLower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.AvgLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.AvgLimit[Idx].Lower, -10, 0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtUpper(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.AvgLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.AvgLimit[Idx].Upper, 0, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtGState(scpi_t *context)
{
    int iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.GuardPeriod.State, 0, 1);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPvtGLimit(scpi_t *context)
{
    int iRet = SetGsmVsaValueInRange(context, GetGSM(context).LimitInfo.PVTLimit.GuardPeriod.Limit, 0, 10);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseStartTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Start.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseStartRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Start.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Start.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Start.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseStopTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Stop.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseStopRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Stop.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseStopAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Stop.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseStopAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].StaticLimt.Stop.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseDynamicState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].DynamicLimt[Idx2].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseDynamicStartPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].DynamicLimt[Idx2].StartPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseDynamicEndPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].DynamicLimt[Idx2].EndPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPURiseDynamicCorrection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].RiseEdgeLimit[0].DynamicLimt))
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].RiseEdgeLimit[Idx1].DynamicLimt[Idx2].Correction,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStartTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStartRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStartAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStartAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStopTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStopRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStopAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseStopAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseDynamicState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseDynamicStartPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].StartPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseDynamicEndPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].EndPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUUseDynamicCorrection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].Correction,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallStartTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Start.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallStartRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Start.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Start.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Start.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallStopTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Stop.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallStopRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Stop.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallStopAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Stop.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallStopAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].StaticLimt.Stop.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallDynamicState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].DynamicLimt[Idx2].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallDynamicStartPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].DynamicLimt[Idx2].StartPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallDynamicEndPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].DynamicLimt[Idx2].EndPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPUFallDynamicCorrection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[0].FallEdgeLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.UpperTemLimit[Idx].FallEdgeLimit[Idx1].DynamicLimt[Idx2].Correction,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStartTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStartRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStartAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStartAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Start.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStopTime(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.Time,
            -50, 600);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStopRelative(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.LevelRel,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStopAbsState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.LevelAbs.State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseStopAbsLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].StaticLimt.Stop.LevelAbs.Limit,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseDynamicState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].State,
            0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseDynamicStartPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].StartPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseDynamicEndPcl(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].EndPCL,
            0, 31);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitPLUseDynamicCorrection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[0].UsefulPartLimit[0].DynamicLimt)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.PVTLimit.LowerTemlimit[Idx].UsefulPartLimit[Idx1].DynamicLimt[Idx2].Correction,
            -100, 10);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecModRefLowPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecModLimit[Idx].RefPwrLimit.LowPwr, 0, 43);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecModRefHighPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecModLimit[Idx].RefPwrLimit.HighPwr, 0, 43);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecModFreqOffsetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit[0].FreOffsetLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecModLimit[Idx].FreOffsetLimit[Idx1].State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecModFreqOffsetLowPowerRel(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit[0].FreOffsetLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecModLimit[Idx].FreOffsetLimit[Idx1].LowPwrRel, -120, 31.5);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecModFreqOffsetHighPowerRel(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit[0].FreOffsetLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecModLimit[Idx].FreOffsetLimit[Idx1].HighPwrRel, -120, 31.5);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecModFreqOffsetPowerAbs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecModLimit[0].FreOffsetLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecModLimit[Idx].FreOffsetLimit[Idx1].AbsPwr, -120, 31.5);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecSwitRefPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit[0].RefPower)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecSwiLimit[Idx].RefPower[Idx1].State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecSwitRefLevel(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit[0].RefPower)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecSwiLimit[Idx].RefPower[Idx1].Limit, 0, 39);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecSwitFreqOffsetState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit[0].FreOffsetLimit)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecSwiLimit[Idx].FreOffsetLimit[Idx1].State, 0, 1);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t cellular::alz::gsm::SetVsaGsmLimitSpecSwitFreqOffsetPowerRel(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        CommandRange params = {
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit[0].FreOffsetLimit)),
            std::make_tuple(0, 0, arraySize(GetGSM(context).LimitInfo.SpecSwiLimit[0].FreOffsetLimit[0].LimitValue)),
        };
        iRet = GetGsmScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int Idx = std::get<0>(params.at(0));
        int Idx1 = std::get<0>(params.at(1));
        int Idx2 = std::get<0>(params.at(2));
        iRet = SetGsmVsaValueInRange(context, 
            GetGSM(context).LimitInfo.SpecSwiLimit[Idx].FreOffsetLimit[Idx1].LimitValue[Idx2], -60, 30);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
