////////////////////////////////////////////////////////////////////////////
///  @date      2016/7/11 17:59
///  @file      tester
///  <AUTHOR>
////////////////////////////////////////////////////////////////////////////

#pragma once
#ifndef __TESTER_H__
#define __TESTER_H__

#include "TesterCommon.h"
#include <memory>
#ifndef LINUX
#ifndef _EXPORT
#define _EXPORT __declspec(dllexport)
#define _IMPORT __declspec(dllimport)
#endif

#else
#ifndef _EXPORT
#define _EXPORT __attribute ((visibility("default")))
#define _IMPORT
#endif
#endif


#ifdef WTTESTER_DLL_EXPORTS

#ifdef __cplusplus
#	define WTTESTER_DLL_API extern "C" _EXPORT
#else
#	define WTTESTER_DLL_API	_EXPORT
#endif

#else

#ifdef __cplusplus
#	define WTTESTER_DLL_API extern "C" _IMPORT
#else
#	define WTTESTER_DLL_API	_IMPORT
#endif
#endif

//////////////////////////////////////////////////////////////////////////////
/// WT_DLLInitialize
/// 初始化WTtester API处理，注意: 在调用该文档中其他API之前需调用该接口,且此函数只能调用一次.
/// @return   void
/// @param void
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API void CALL_MODE WT_DLLInitialize();
WTTESTER_DLL_API void CALL_MODE WT_DLLInitialize_V2(unsigned int maxMemSize, unsigned int maxSamplePoint);
//////////////////////////////////////////////////////////////////////////////
/// WT_DLLTerminate
/// 释放WTtester API处理
/// 注：强烈建议在所有操作结束之后才调用该接口，否则可能造成其他内存处理出错,且此函数只能调用一次.
///
/// @return   WTtester_DLL_API void
/// @param void
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API void CALL_MODE WT_DLLTerminate(void);

//////////////////////////////////////////////////////////////////////////////
/// 设置是否开启程序崩溃抓取并保存崩溃信息的功能
///
/// @return 操作完全正确返回WT_ERR_CODE_OK，失败返回其他值
/// @param[in] crashDumpMode：程序崩溃信息是否抓取, 0: 崩溃不抓取信息, 1: 崩溃抓取信息
//////////////////////////////////////////////////////////////////////////////
#ifndef LINUX
WTTESTER_DLL_API int CALL_MODE WT_SetCrashDumpIf(int crashDumpMode);
#endif
//////////////////////////////////////////////////////////////////////////////
/// 获取仪器的子仪器配置子仪器配置(如果关心子仪器配置，在连接之前调用该接口以便确定连接单元，如果不关心，可不调用)
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] ipAddr:仪器IP地址
/// @param[out] testerCfg:仪器的子仪器配置
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetSubTesterConfig(const char *ipAddr, SubTesterCfg testerCfg[WT_SUB_TESTER_INDEX_MAX], int *subTesterCount);

#ifndef GETTESTERCONNSTATU_API
#define GETTESTERCONNSTATU_API
//////////////////////////////////////////////////////////////////////////////
////  连接状态查询(心跳)
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connUnit:连接单元
/// @param[in]  bufferSize:buffer大小
/// @param[out] statu:连接状态
/// @param[out] buffer:描述连接单元连接状态的buff
/// @see ConnectedUnit
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetTesterConnStatus(ConnectedUnit *conUnit, int *statu, char *buffer, int bufferSize);
#endif
//////////////////////////////////////////////////////////////////////////////
/// 连接仪器
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connUnit:连接单元
/// @param[in]  unitCount:连接单元的数量，取值范围[1-4];默认值1；MIMO时，第一个连接单元为主机
/// @param[out] connID:返回的连接ID
/// @see ConnectedUnit
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_Connect(ConnectedUnit *connUnit, int unitCount, int *connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_ForceConnect
/// 强制连接
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connUnit:连接单元(如果不关心子仪器，请将connUnit中的subTesterIndex设置为 @ref WT_SUB_TESTER_INDEX_AUTO
/// @param[in]  subTesterIndex取值参见WT_SUB_TESTER_INDEX
/// @param[in]  unitCount:连接单元的数量，取值范围[1-4];默认值1；MIMO时，第一个连接单元为主机
/// @param[out] connID:返回的连接ID
/// @see WT_SUB_TESTER_INDEX
/// @see WT_SUB_TESTER_INDEX_AUTO
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ForceConnect(ConnectedUnit *connUnit, int unitCount, int *connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_DisConnect
/// 断开连接
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_DisConnect(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SwitchMode
/// 切换业务模式
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  targetMode:目标业务模式
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SwitchMode(int connID, int targetMode);

//////////////////////////////////////////////////////////////////////////////
/// WT_AddMimoTester
/// 添加MIMO从机
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  connUnit:连接单元
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_AddMimoTester(int connID, ConnectedUnit connUnit);

//////////////////////////////////////////////////////////////////////////////
/// WT_RemoveMimoTester
/// 删除Mimo从机
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_RemoveMimoTester(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetNetInfo
/// 配置子网口
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  netInfo:子网口配置
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetNetInfo(int connID, VirtualNetType *netInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetNetInfo
/// 获取子网口配置
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] netInfo:子网口配置，详见VirtualNetType
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetNetInfo(int connID, VirtualNetType *netInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetNetLink
/// 获取子网口配置
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] LinkStatus:子网口连接状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetNetLink(int connID, bool *LinkStatus);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetNetLink
/// 获取子网口配置
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] IsDhcp:主网口IP地址是否是DHCP动态获取的
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetTesterIpAddressType(int connID, bool *IsDhcp);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultParameter
/// 获取测试默认参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] vsaParam:Vsa参数
/// @param[out] vsgParam:Vsg参数
/// @param[out] waveParam:波形文件参数
/// @param[out] vsgPattern:发送行为配置
/// @note   如果只是想获取一项或几项的默认值，可将其它输入参数设置成NULL
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultParameter(int connID, VsaParameter *vsaParam, VsaAvgParameter *avgParam, VsgParameter *vsgParam, VsgWaveParameter *waveParam, VsgPattern *vsgPattern);

/////////////////////////////////////////////////////////////////////////////
/// WT_ClearSampRateFromFileFlag
/// 清除从文件获取的vsg信号采样率标志
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_ClearSampRateFromFileFlag(int connID);


//////////////////////////////////////////////////////////////////////////////
/// WT_GetTesterInfo_V2
/// 获取测试仪基本信息
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] info:仪器信息
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetTesterInfo_V2(int connID, TesterOverview *info);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsa
/// 设置Vsa参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  vsaParam:VSA参数
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_SetVSA(int connID, VsaParameter *vsaParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSAAverageParameter
/// 设置Vsa平均参数
/// @return   见WT_ERR_CODE_ENUM
/// @param[in]  vsaParam:VSA参数
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_SetVSAAverageParameter(int connID, VsaAvgParameter *avgParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaAutorange
/// 设置Vsa(AutoRange)
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] vsaParam:VSA参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSAAutorange(int connID, VsaParameter *vsaParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsa
/// 查询Vsa参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] vsaParam:VSA参数
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetVSAParameter(int connID, int demodType, VsaParameter *vsaParam, void *analyzeParam, int paramSize);

//////////////////////////////////////////////////////////////////////////////
/// WT_DataCapture
/// 信号抓取
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_DataCapture(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_DataCaptureAsync
/// 异步信号抓取
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_DataCaptureAsync(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_PauseDataCapture
/// 暂停数据抓取
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_PauseDataCapture(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_StopDataCapture
/// 结束信号抓取
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]   connID:连接ID
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_StopDataCapture(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetCurrVSAStatu
/// 获取当前的VSA状态
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] statu:当前VSG状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetCurrVSAStatu(int connID, int *statu);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetGeneralAnalyzeParam
/// 设置通用分析参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  commonAnalyzeParam:通用参数,详见AlzParamComm
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetGeneralAnalyzeParam(int connID, AlzParamComm *commonAnalyzeParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_Analyze
/// 数据分析
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  analyzeParamType:分析参数类型, 详细参见:WT_ALZ_PARAM_TYPE
/// @param[in]  analyzeParams:分析参数缓存，根据不同的分析业务，配置为union类型的AnalyzeParam.analyzeParamWifi或者AnalyzeParam.analyzeParamBt
/// @param[in]  paramsSize:分析参数缓存大小，根据analyzeParams业务类型，传入对应的sizeof(AnalyzeParam.analyzeParamWifi)或者sizeof(AnalyzeParam.analyzeParamBt)等
/// @param[in]  refFileName:分析使用的参考文件在下位机存储的文件名
/// @param[in]  frameID:帧ID，从1开始计数，默认分析第一帧
/// @par WIFI配置示例:
/// @code
///               AnalyzeParam param;
///               AnalyzeParam *analyzeParams = &param.analyzeParamWifi;
///               int paramsSize = sizeof(param.analyzeParamWifi);
/// @endcode
/// @par BT配置
/// @code
///               AnalyzeParam *analyzeParams = &param.analyzeParamBt;
///               int paramsSize = sizeof(param.analyzeParamBt);
/// @endcode
/// @par FFT配置
/// @code
///               AnalyzeParam *analyzeParams = &param.analyzeParamFft;
///               int paramsSize = sizeof(param.analyzeParamFft);
/// @endcode
/// @see WT_ALZ_PARAM_TYPE
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_Analyze(int connID, int analyzeParamType, AnalyzeParam *analyzeParams, int paramsSize, const char *refFileName = NULL, int frameID = 0, unsigned int timeoutMs = 15000);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetAlzParam:设置vsa分析参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  analyzeParamType:分析参数类型, 详细参见:WT_ALZ_PARAM_TYPE
/// @param[in]  analyzeParams:分析参数缓存
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetAlzParam(int connID, int analyzeParamType, AnalyzeParam *analyzeParams, int paramsSize);


//////////////////////////////////////////////////////////////////////////////
///  WT_GetResult
///   获取分析结果
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] anaParamString:要分析出的结果名称
/// @param[in] signalID:信号流ID，从0开始计数
/// @param[in] segmentID:8080段ID，0表示composite结果，1表示segment1,2表示segment2
/// @param[out] result:用于存放分析结果
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetResult(int connID, const char *anaParamString, double *result, int signalID = 0, int segmentID = 0);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetBaseResult
/// 获取常见的分析结果
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in] signalID:信号流ID，从0开始计数
/// @param[in] segmentID:8080段ID，0表示composite结果，1表示segment1,2表示segment2
/// @param[out] result:常用分析结果
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetBaseResult(int connID, VsaBaseResult *result, int signalID = 0, int segmentID = 0);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetVectorResultElementSize
/// 获取结果中每个数据单元的大小，单位:字节
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] signalID:信号流ID，从0开始计数
/// @param[in] segmentID:8080段ID，0表示composite结果，1表示segment1,2表示segment2
/// @param[in] anaParmString:分析参数
/// @param[out] elementSize:数据单元大小
/// @param[out] SegNo:listmod segment索引，从0开始，只在listmod下有效
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVectorResultElementSize(int connID, const char *anaParamString, unsigned *elementSize, int signalID = 0, int segmentID = 0, int SegNo = -1);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVectorResultElementCount
/// 获取整个结果的大小
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] signalID:信号流ID，从0开始计数
/// @param[in] segmentID:8080段ID，0表示composite结果，1表示segment1,2表示segment2
/// @param[in] anaParmString:分析参数
/// @param[out] elementCount:单元数量
/// @param[out] SegNo:listmod segment索引，从0开始，只在listmod下有效
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetVectorResultElementCount(int connID, const char *anaParamString, unsigned *elementCount, int signalID = 0, int segmentID = 0, int SegNo = -1);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVectorResult
/// 获取结果（适用于结果数据不止一组的情况）
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in] signalID:信号流ID，从0开始计数
/// @param[in] segmentID:8080段ID，0表示composite结果，1表示segment1,2表示segment2
/// @param[in]  anaParmString:
/// @param[in]  resultType:结果类型
/// @param[in]  resultSize:结果缓存区的大小
/// @param[out] result:分析结果
/// @param[out] SegNo:listmod segment索引，从0开始，只在listmod下有效
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVectorResult(int connID, const char *anaParamString, void *result, unsigned resultSize, int signalID = 0, int segmentID = 0, int SegNo = -1);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListAllsegVectorResult
/// 获取list所有seg结果（适用于结果数据不止一组的情况）
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ParmString: 结果字符串
/// @param[in]  resultSize:结果缓存区的大小
/// @param[out] result:分析结果
/// @param[out] RetResultSize:返回的实际结果大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListAllsegVectorResult(int connID, const char *ParamString, void *Result, int ResultSize, int &RetResultSize);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsgParameter
/// 设置Vsg基本参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsgParam:VSG参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSGParameter(int connID, VsgParameter *vsgParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsgWaveParameter
/// 设置波形参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsgWaveParam:波形参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSGWaveParameter(int connID, VsgWaveParameter *vsgWaveParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsgPattern
/// 设置发送行为
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsgWaveParam:VSG发送行为
/// @param[in]  vsgPnItemCount:PN项数量，单文件发送时不需要赋值
/// @param[in]  vsgPnHead:PN头，单文件发送时不需要赋值
/// @param[in]  vsgPnHeadCount:PN头数量，单文件发送时不需要赋值
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSGPattern(int connID, VsgPattern *vsgPatternParam, unsigned vsgPnItemCount = 1, PnItemHead_API *vsgPnHead = NULL, unsigned vsgPnHeadCount = 1);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVSGPattern
/// 获取发送行为
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] vsgPattern:VSG发送行为(数组)
/// @param[in] vsgPnItemCount:vsgPattern数组单元数量
/// @param[in] vsgPnHead:PN头(数组)
/// @param[in] vsgPnHeadCount:vsgPnHead数组单元数量
/// @param[in] actualPnItemCount:用于存储实际的vsgPattern数量
/// @param[in] actualPnHeadCount:用于存储实际的vsgPnHead数组单元数量
/// @param[out] vsgPattern:返回VSG发送行为(数组)
/// @param[out] vsgPnHead:返回PN头(数组)
/// @param[out] actualPnItemCount:返回实际的vsgPattern数量
/// @param[out] actualPnHeadCount:返回实际的vsgPnHead数组单元数量
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetVSGPattern(int connID, VsgPattern *vsgPattern, unsigned vsgPnItemCount, PnItemHead_API *vsgPnHead, unsigned vsgPnHeadCount, unsigned *actualPnItemCount, unsigned *actualPnHeadCount);

//////////////////////////////////////////////////////////////////////////////
/// WT_StartVsg
/// 同步Vsg开始
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_StartVSG(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_AsynStartVsg
/// 异步Vsg开始
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_AsynStartVSG(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetCurrVsgStatu
/// 获取当前的Vsg状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] statu:当前VSG状态
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetCurrVSGStatu(int connID, int *statu);

//////////////////////////////////////////////////////////////////////////////
/// WT_PauseVsg
/// 暂停Vsg
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_PauseVSG(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_StopVsg
/// 停止Vsg
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_StopVSG(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_DeleteTesterWaveFileOrRefFile
/// 删除仪器中的波形文件(参考文件)
/// @return  成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] fileName:下位机文件名称（需包含路径）
///
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_DeleteTesterWaveFileOrRefFile(int connID, const char *fileName);

//////////////////////////////////////////////////////////////////////////////
/// WT_AddTesterWaveFileOrRefFile
/// 添加波形文件（参考文件）到仪器
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] fileName:上位机文件名称（包含路径）
/// @param[in] saveFileName:须保存到下位机的文件名称（包含路径）。保存到仪器内部后，信号文件名后缀变成.low
/// @param[in] acWave2:是否为Load两条流的数据,目前仅当使用80+80时才启用该参数
///
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_AddTesterWaveFileOrRefFile(int connID, const char *fileName, const char *saveFileName, int acWave2);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetTesterAllWaveFileOrRefFileNames
/// 获取下位机指定目录波形文件列表，包括文件和文件夹
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] path:下位机文件目录
/// @param[in] fileNameBuffer:用于保存返回的文件列表，每个信号文件名占用0xff大小的内存块。信号文件名后缀为.low
/// @param[in] fileNameBuffSize:fileNameBuffer大小
/// @param[in] fileCount:信号文件个数
///
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetTesterAllWaveFileOrRefFileNames(int connID, const char *path, char *fileNameBuffer, int fileNameBuffSize, unsigned *fileCount);

//////////////////////////////////////////////////////////////////////////////
/// WT_QueryTesterWaveFileOrRefFile
/// 查询指定波形文件是否存在
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  fileName:下位机的文件名称（包含路径）
/// @param[in]  waveExists:文件是否存在，1表示存在，0表示不存在
///
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_QueryTesterWaveFileOrRefFile(int connID, const char *fileName, int *waveExists);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetCmimoRefFile
/// 设置Cmimo当前使用的参考文件
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  fileName:参考文件名称
/// @date      2017/2/22
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_SetCmimoRefFile(int connID, const char *fileName);

//////////////////////////////////////////////////////////////////////////////
/// WT_ShutDownDevice
/// 关闭仪器
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ShutDownDevice(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_ResetSubNetConfiguration
/// 清除子网口配置
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ResetSubNetConfiguration(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSubNetAutoNeg
/// 配置子网口网速自动协商
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in] Enable:配置信息；1表示开，0表示关。默认关闭
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSubNetAutoNeg(int connID, int Enable);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetSubNetAutoNeg
/// 获取子网口网速自动协商开关状态
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] ON_OFF:配置信息；1表示开，0表示关。默认关闭
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetSubNetAutoNeg(int connID, int *ON_FF);

//////////////////////////////////////////////////////////////////////////////
/// WT_QuerySpecTester
/// 局域网内查看仪器基本信息，包括IP地址，SN码
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  ipAddr:仪器IP
/// @param[out] testerInfo:仪器基本信息,详见 @ref TesterOverview
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_QuerySpecTester(const char *ipAddr, TesterOverview *testerInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_QuerySpecTester
/// 局域网内查看仪器基本信息，包括IP地址，SN码
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  ipAddr:仪器IP
/// @param[out] testerInfo:仪器基本信息,详见 @ref ExternedTesterOverview
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_QuerySpecTester_V2(const char *ipAddr, ExternedTesterOverview *testInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingCalibrationChannelEstDutTX
/// 在Calibration时，控制DUT发送，WT仪器接收，调用此函数估算出Hab 1x3
///
/// @return  成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  demod:枚举量802.11分析模式WT_DEMOD_ENUM
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalibrationChannelEstDutTX(int connID, int demod);

//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingCalibrationChannelEstDutRX
/// 在Calibration时，控制WT仪器发送，DUT接收，调用此函数估算出Hba 3x1
///
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  dutChannelEst:从DUT读取到的通道信息数组
/// @param[in]  dutChannelEstLength:从DUT读取到的通道信息数组长度
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalibrationChannelEstDutRX(int connID, double *dutChannelEst, int dutChannelEstLength);

//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingCalibrationResult
/// 在Calibration时，在获取Hab 1x3和Hba 3x1后，通过此函数获取相位
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[out] resultAngle:结果相位值, 返回为数组（返回最大长度8元素）, 由调用方提供数组内存
/// @param[out] resultAngleLength:结果相位值数组长度
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalibrationResult(int connID, double *resultAngle, int *resultAngleLength);

//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingVerification
/// 在Verification时，通过配置DUT进入相应状态并输出Beamforming信号，WT-200接收
/// 并解析DUT信号，计算Beamforming带来的增益。
///
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID.
/// @param[out] diffPower:返回结果，Beamforming增益dB
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingVerification(int connID, double *diffPower);

//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingCalculateChannelProfile
/// 在Calibration时，通过此函数获取DUT发送信号的幅度和相位，此函数适用于MTK方案
/// @return    成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] resultBuf:结果相位值, 返回为数组（幅度、相位）, 由调用方提供数组内存
/// @param[in] resultAngleLength:结果值数组长度，必须是2的倍数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalculateChannelProfile(int connID, int demod, double *resultBuf, int resultLength);
//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingCalculateChannelProfile_V2
/// 在Calibration时，通过此函数获取DUT发送信号的幅度和相位，此函数适用于MTK方案
/// @return    成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] resultBuf:结果相位值, 返回为数组（幅度、相位）, 由调用方提供数组内存
/// @param[in] resultAngleLength:结果值数组长度，必须是2的倍数。最终数组结果长度存储
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalculateChannelProfile_V2(int connID, int demod, double *resultBuf, int *resultLength);

//////////////////////////////////////////////////////////////////////////////
/// WT_BeamformingCalculateChannelAmplitudeandAngle_BCM
/// 在Calibration时，WT_BeamformingCalibrationChannelEstDutTX后通过此函数获取DUT发送信号的幅度和相位，此函数适用于BCM方案
/// @return    成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[out] ValidSpatialStreams:有效空间流数
/// @param[out] DataLen:幅度或者相位的数据长度
/// @param[out] resultBuf:结果相位值, 返回为数组（幅度、相位）, 由调用方提供数组内存
/// @param[in] resultLength:结果值数组长度，必须是2的倍数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalculateChannelAmplitudeandAngle_BCM(int connID, int *ValidSpatialStreams, int *DataLen, double *resultBuf, int *resultLength);

//////////////////////////////////////////////////////////////////////////////
/// WT_SaveSignal
/// 保存当前VSA信号到文件
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] fileName:文件名称
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SaveSignal(int connID, const char *fileName);

//////////////////////////////////////////////////////////////////////////////
/// WT_CheckConnectStatus
/// 查询当前连接仪器的网络状态
/// @return   网络正常返回返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_CheckConnectStatus(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSA_V2 版本2
/// 设置Vsa参数和VSA扩展参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  vsaParam:VSA参数
/// @param[in]  extParam:VSA扩展参数
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSA_V2(int connID, VsaParameter *vsaParam, ExtendVsaParameter *extParam = NULL);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSAAutorange_V2
/// 设置Vsa(AutoRange) 版本2
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] vsaParam:VSA参数
/// @param[out] extParam:VSA扩展参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSAAutorange_V2(int connID, VsaParameter *vsaParam, ExtendVsaParameter *extParam = NULL);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSGParameter_V2
/// 设置Vsg基本参数版本2
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsgParam:VSG参数
/// @param[in]  extParam:VSG扩展参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSGParameter_V2(int connID, VsgParameter *vsgParam, ExtendVsgParameter *extParam = NULL);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetExternAnalyzeParam
/// 配置扩展分析参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  demod: 分析信号的类型。同VSA参数里面的Demod意义一致
///	@param[in]  ParamType: 配置参数类型
///	@param[in]  param: 配置参数
///	@param[in]  paramSize: 配置参数结构体字节数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetExternAnalyzeParam(int connID, int demod, int ParamType, void *param, int paramSize);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetAnalyzeGroupParam
/// 配置需要分析结果的分析参数群组。
///	可加快分析速度，特别是只分析功率的情况下
///	必须在调用WT_Analyze之前使用
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  anaParamString: 同WT_GetResult的char *anaParamString
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetAnalyzeGroupParam(int connID, char *anaParamString);

//////////////////////////////////////////////////////////////////////////////
/// WT_SaveSignal_V2
/// 保存当前VSA信号到文件，需要如果type等于WT_SAVE_COMPENSATED_DATA时，需要WT_Analyze之后才能正确保存
/// 开始时间和结束时间都等于0时，默认保存所有采集到的信号数据
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  type: 保存原始数据：WT_SAVE_RAW_DATA；保存补偿后的VSA数据：WT_SAVE_COMPENSATED_DATA
/// @param[in]  fileName:文件名称
///	@param[in]  startUs: 开始时间，单位微秒(us)
///	@param[in]  endUs: 结束时间，单位微秒(us)
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SaveSignal_V2(int connID, int type, const char *fileName, int startUs = 0, int endUs = 0);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSA_V2 版本2
/// 设置Vsa参数和VSA扩展参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  vsaParam:VSA参数
/// @param[in]  extParam:VSA扩展参数
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVSAParameter_V2(int connID, int demodType, VsaParameter* vsaParam, ExtendVsaParameter* extParam = NULL);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVSGParam
/// 获取仪器以配置的VSG参数和PN参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  vsgParam: VSG参数结构体
///	@param[in]  vsgWaveParam: 信号文件参数
///	@param[in]  vsgPattern: PN参数

//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVSGParameter(int connID, VsgParameter *vsgParam, VsgWaveParameter *vsgWaveParam, VsgPattern *vsgPattern);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVSGParam
/// 获取仪器以配置的VSG参数和PN参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  vsgParam: VSG参数结构体
/// @param[in]  extParam: VSG扩展参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVSGParameter_V2(int connID, VsgParameter* vsgParam, ExtendVsgParameter* extParam = NULL);

//////////////////////////////////////////////////////////////////////////////
/// WT_TB_Init
/// 11ax TB测试前初始化，进入TB测试场景。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_Init(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_TB_Release
/// 11ax TB测试退出，退出TB测试场景。恢复正常测试。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_Release(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_TB_Start
/// 11ax TB测试开始。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_Start(int connID, int timeout_ms = 3000);
//////////////////////////////////////////////////////////////////////////////
/// WT_TB_Stop
/// 11ax TB测试停止。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_Stop(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_TB_Stauts
/// 11ax TB测试获取状态。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  status:TB测试状态信息
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_Status(int connID, int *status);
//////////////////////////////////////////////////////////////////////////////
/// WT_TB_SetParam
/// 11ax TB测试配置参数VSA,VSG。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Param:TB测试配置参数VSA,VSG
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_SetParam(int connID, InterBindParameter *Param);
//////////////////////////////////////////////////////////////////////////////
/// WT_TB_AutoRange
/// 11ax TB测试配置时AutoRange。仪器端模拟AP发送TF帧，DUT回应TB帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Param:TB测试Auto Range后的配置参数VSA,VSG
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TB_AutoRange(int connID, InterBindParameter *Param, int timeout_ms = 3000);

//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorCatenateFiles
/// 拼接多PN信号文件 bwv2/cvv2
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  catenateInfo:
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorCatenateFiles(int connID, const MutiPNCatenateInfo *catenateInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorWifi
/// 生成WIFI信号文件
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  fileName: 保存的信号文件名称
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorWifi(int connID, const char *fileName, GenWaveWifiStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorBT
/// 生成BT信号文件
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  fileName: 保存的信号文件名称
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorBT(int connID, const char *fileName, GenWaveBtStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo);
//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorBTV2
/// 生成BT信号文件新结构体
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  fileName: 保存的信号文件名称
/// @param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorBTV2(int connID, const char *fileName, GenWaveBtStructV2 *pnParameters, MutiPNExtendInfo *MutiPnExInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorCW
/// 生成CW信号文件
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  fileName: 保存的信号文件名称
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorCW(int connID, const char *fileName, GenWaveCwStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGenerator3GPP
/// 生成3GPP信号文件
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  fileName: 保存的信号文件名称
///	@param[in]  pnParameters: 默认参数结构体
///	@param[in]  pAlz3gpp: 蜂窝分析参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGenerator3GPP(int connID,
                                                    const char *fileName,
                                                    void *pnParameters,
                                                    int Paramlen,
                                                    std::shared_ptr<AlzParam3GPP> &pAlz3gpp);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultWaveParameterCW
/// 获取生成CW信号文件的默认参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultWaveParameterCW(int connID, GenWaveCwStruct *pnParameters);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultWaveParameterBT
/// 获取生成BT信号文件的默认参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultWaveParameterBT(int connID, GenWaveBtStruct_API *pnParameters);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultWaveParameterBTV2
/// 获取生成BT信号文件的默认参数,对应v2版结构体
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultWaveParameterBTV2(int connID, GenWaveBtStructV2 *pnParameters);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultWaveParameterWifi
/// 获取生成WIFI信号文件的默认参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  demod:WIFI信号的协议类型
/// @param[in]  ppdu:WIFI信号的PPDU类型
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultWaveParameterWifi(int connID, int demod, int ppdu, GenWaveWifiStruct_API *pnParameters);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetWaveCalDataCompensate
/// 配置VSG信号文件是否进行硬件参数补偿，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetWaveCalDataCompensate(int connID, int ON_FF);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsgIQImbCompensate
/// 配置VSG是否进行IQ不平衡参数补偿，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsgIQImbCompensate(int connID, int ON_FF);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaCalDataCompensate
/// 配置VSA是否进行硬件参数补偿，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaCalDataCompensate(int connID, int ON_FF);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaIQImbCompensate
/// 配置VSA是否进行IQ不平衡参数补偿，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaIQImbCompensate(int connID, int ON_FF);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsgFlatnessCalCompensate
/// 获取VSG平坦度补偿状态，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVsgFlatnessCalCompensate(int connID, int *ON_FF);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsgIQImbCompensate
/// 获取VSG IQ不平衡补偿状态，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVsgIQImbCompensate(int connID, int *ON_FF);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsaFlatnessCalCompensate
/// 获取VSA平坦度补偿状态，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVsaFlatnessCalCompensate(int connID, int *ON_FF);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsaIQImbCompensate
/// 获取VSA IQ不平衡补偿状态，默认是ON
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  ON_FF: 0=OFF, 1=ON
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVsaIQImbCompensate(int connID, int *ON_FF);

//////////////////////////////////////////////////////////////////////////////
/// WT_PacStartGetData
/// PAC值开始获取
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]connID:连接ID
/// @param[in]mode: WT_PAC_MODE_ENUM WT_PAC_SHORT_ENUM or WT_PAC_OPEN_ENUM
///	@param[in]AvgCnt: 数据平均次数
///	@param[in]freqList：频点列表
///	@param[in]freqListCnt：频点列表中的频点个数
///	@param[in]attribute：PAC的VSA，VSG属性
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_PacStartGetData(int connID, PacParameter *param, PacAttribute *attribute, void(*pfunCallback)(PacProgressParameter *progress));
//////////////////////////////////////////////////////////////////////////////
/// WT_PacStartGetData
/// PAC计算线衰值
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]connID:连接ID
///	@param[in]buf: 存储PAC线衰计算值内存
///	@param[in]BufCnt: 存储PAC线衰计算值内存的个数，有多少个PacDataAvg，注意不是byte大小
///	@param[in]retCnt：实际返回的PacDataAvg个数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_PacCalData(int connID, PacDataAvg *buf, int BufCnt, int *retCnt);
//////////////////////////////////////////////////////////////////////////////
/// WT_PacStartGetData
/// PAC计算停止
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_PacStop(int connID);



//////////////////////////////////////////////////////////////////////////////
/// WT_TF_Init
/// 11ax TF测试前初始化，进入TF测试场景。仪器端模拟STA回应TB帧，DUT发送TF帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TF_Init(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_TF_Release
/// 11ax TF测试退出，退出TF测试场景。恢复正常测试。仪器端模拟STA回应TB帧，DUT发送TF帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TF_Release(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_TF_Start
/// 11ax TF测试开始。仪器端模拟STA回应TB帧，DUT发送TF帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TF_Start(int connID, int timeout_ms = 3000);
//////////////////////////////////////////////////////////////////////////////
/// WT_TF_Stop
/// 11ax TF测试停止。仪器端模拟STA回应TB帧，DUT发送TF帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TF_Stop(int connID);
//////////////////////////////////////////////////////////////////////////////
/// WT_TF_Stauts
/// 11ax TF测试获取状态。仪器端模拟STA回应TB帧，DUT发送TF帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  status:TB测试状态信息
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TF_Status(int connID, int *status);
//////////////////////////////////////////////////////////////////////////////
/// WT_TF_SetParam
/// 11ax TF测试配置参数VSA,VSG。仪器端模拟STA回应TB帧，DUT发送TF帧场景
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Param:TB测试配置参数VSA,VSG
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TF_SetParam(int connID, InterBindParameter *Param);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetDigtalIQMode
/// 设置数字IQ的模式
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  mode:数字IQ参数， 1 = digtal IQ, 0 = RF mode，default RF mode
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetDigtalIQMode(int connID, int mode);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetDigtalIQParam
/// 设置数字IQ的参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Param:数字IQ参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetDigtalIQParam(int connID, DigtalIQParam *param);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetDigtalIQTestFixture
/// 设置数字IQ治具模式的参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Param:数字IQ治具参数
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetDigtalIQTestFixture(int connID, DigtalIQTestFixture *param);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetLoMode
/// 设置本振模式
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Mode:本振模式
/// @param[in]  ModId:模块Id
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetLoMode(int connID, int mode, int ModId);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetLoMode
/// 获取本振模式
/// @return 成功返回 @ref 模式（0为共本振，1为非共本振） ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] Mode:本振模式
/// @param[in]  ModId:模块Id
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetLoMode(int connID, int *mode, int ModId);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetIQMode
/// 设置模拟IQ模式
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  mode:模拟IQ模式(mode[]= {mod1,mod2,mod3,mod4,...})
/// @param[in]  modcount:模块数量
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetIQMode(int connID, int *mode, int modcount);
//////////////////////////////////////////////////////////////////////////////
/// WT_GetIQMode
/// 读取模拟IQ模式
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out] mode:模拟IQ模式(mode[]= {mod1,mod2,mod3,mod4,...})
/// @param[out] modcount:模块数量
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetIQMode(int connID, int *mode, int *modcount);

//////////////////////////////////////////////////////////////////////////////
/// WT_ClrVSAAvgData
/// 清除平均数据
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ClrVSAAvgData(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSATrigParam
/// 配置触发动作配置
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVSATrigParam(int connID, VsaTrigParam *Param);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVSATrigParam
/// 获取触发动作配置
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVSATrigParam(int connID, VsaTrigParam* Param);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetSpectrumPointPower
/// 获取频谱某一个点的功率
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Offset:频偏
/// @param[out]  Power:功率
/// @param[in]  signalID:MIMO流ID
/// @param[in]  segmentID:8080流ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetSpectrumPointPower(int connID, double Offset, double* Power, int signalID = 0, int segmentID = 0);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetWaveGeneratorTimeout
/// 设置WaveGen的超时时间
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Time:超时时间
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetWaveGeneratorTimeout(int connID, int Time);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetWaveGenCBFReportField
/// 生成完信号之后获取CFB compressed Beamforming report field数据内容
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[Out]  ReportField:CBF的SNR和量化的Angle数据内容
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetWaveGenCBFReportField(int connID, CBFReport *ReportField);

//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorSLE
/// 生成SLE信号文件
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  fileName: 保存的信号文件名称
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorSLE(int connID, const char *fileName, GenWaveGleStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetWaveEncrypted
/// 设置保存信号文件时是否加密，根据license设定。默认加密
/// @return 无
/// @param[in]  set: 0 = 明文，不加密。 1 = 加密
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API void WT_SetWaveEncrypted(int set);

//////////////////////////////////////////////////////////////////////////////
/// 初始化保存信号文件时的SN和FW版本信息
/// @param[in]  SN: SN信息
/// @param[in]  FwVersion: Fw版本信息
/// @return  void
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API void WT_SetWaveSNAndFW(const char *SN, const char *FwVersion);

//////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultWaveParameterGLE
/// 获取生成Gle信号文件的默认参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  demod:WIFI信号的协议类型
/// @param[in]  ppdu:WIFI信号的PPDU类型
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultWaveParameterGLE(int connID, GenWaveGleStruct* pnParameters);


//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaNoiseCalibrationStart
/// 启动噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  PortList:开启端口列表
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaNoiseCalibrationStart(int connID, int PortList[8]);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaNoiseCalibrationStop
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaNoiseCalibrationStop(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsaNoiseCalibrationStatus
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVsaNoiseCalibrationStatus(int connID, int &Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetVsaNoiseCalibrationPortValid
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:端口状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVsaNoiseCalibrationPortValid(int connID, int Status[8][8], int &TesterCount);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaExtendEVMStatus
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:端口状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaExtendEVMStatus(int connID, int Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetBroadcastEnable
/// 设置广播状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Status:状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetBroadcastEnable(int connID, int Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetBroadcastEnable
/// 获取广播状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetBroadcastEnable(int connID, int &Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetBroadcastDebugEnable
/// 设置广播DEBUG模式
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Status:是否开启DEBUG
/// @param[in]  Power:广播所有端口的功率
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetBroadcastDebugEnable(int connID, int Status, double Power[WT_PORT_RF8 - WT_PORT_OFF]);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetBroadcastRunStatus
/// 获取广播模块运行状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetBroadcastRunStatus(int connID, int &Status);

///////////////////////////////////////////////////////////////////////////
/// WT_FileRefParam
/// 把参考文件转换成AnalyzeParam结构体，转换结构存在dest中
/// @param[in] connID:连接ID
/// @param[in] ParamType: 参数类型
/// @param[in] src_param: 参考文件数据
/// @param[in] paramSize: 参考文件数据大小
/// @param[in] dest: 转换结果
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E   网络正常返回WT_ERR_CODE_OK，否则网络异常。见WT_ERR_CODE_ENUM
///////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_FileRefParam(int connID, int ParamType, void *src_param, int paramSize, AnalyzeParam *dest);


//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaIterativeEVMStatus
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:端口状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaIterativeEVMStatus(int connID, int Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaSncEVMStatus
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:端口状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaSncEVMStatus(int connID, int Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetVsaCcEVMStatus
/// 停止噪声校准
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  Status:端口状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetVsaCcEVMStatus(int connID, int Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetDefaultWaveParameterWiSun
/// 获取生成Gle信号文件的默认参数
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  demod:WIFI信号的协议类型
/// @param[in]  ppdu:WIFI信号的PPDU类型
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDefaultWaveParameterWiSun(int connID, GenWaveWisunStruct* pnParameters);

//////////////////////////////////////////////////////////////////////////////
/// WT_WaveGeneratorWiSun
/// 生成WiSUN信号文件
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in]  fileName: 保存的信号文件名称
///	@param[in]  pnParameters: 默认参数结构体
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorWiSun(int connID, const char* fileName, GenWaveWisunStruct *pnParameters);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetTxListModeEnable
/// 使能listmod
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetTxListModeEnable(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetTxListModeDisable
/// 去使能listmod
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetTxListModeDisable(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetRxListModeEnable
/// 使能listmod
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetRxListModeEnable(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetRxListModeDisable
/// 去使能listmod
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetRxListModeDisable(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaClear
/// 清除所有Vsa的Seg配置
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaClear(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAlzCommParam
/// 设置seg通用分析参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  commonAnalyzeParam:通用参数,详见AlzParamComm
/// @param[in]  Segno：segment索引
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAlzCommParam(int connID, int Segno, AlzParamComm *commonAnalyzeParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAllAlzCommParam
/// 设置seg所有通用分析参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllcommonAnalyzeParam:通用参数,详见AlzParamComm
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAllAlzCommParam(int connID, AlzParamComm *AllcommonAnalyzeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaCapParam
/// 设置Vsa抓取参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsaParam:VSA参数
/// @param[in]  Segno：segment索引
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaCapParam(int connID, int SegNo, VsaParameter *vsaParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAllCapParam
/// 设置seq所有抓取参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllvsaParam:所有VSA参数
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAllCapParam(int connID, VsaParameter *AllvsaParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegAllVsgParam
/// 设置seq所有vsg参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllvsgParam:所有VSG参数
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegAllVsgParam(int connID, VsgParameter *AllvsgParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAllTrigParam
/// 设置Vsa 所有trig参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllvsatrigParam:所有VSA trig参数
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAllTrigParam(int connID, VsaTrigParam *AllvsatrigParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaTrigParam
/// 设置Vsa trig参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsatrigParam:VSA trig参数
/// @param[in]  Segno：segment索引
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaTrigParam(int connID, int Segno, VsaTrigParam *vsatrigParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaTrigCommonParam
/// 设置Vsa common trig参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  SegTrigCommParam:VSA common trig参数
/// @param[in]  Segno：segment索引
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaTrigCommonParam(int connID, int Segno, void *SegTrigCommParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAllTrigCommonParam
/// 设置Vsa 所有 的common trig参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllSegTrigCommParam:VSA 所有common trig参数
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAllTrigCommonParam(int connID, void *AllSegTrigCommParam, int Size);
//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAllTrigCommonParam
/// 设置Vsa 所有 的common trig参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllSegTrigCommParam:VSA 所有common trig参数
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgAllTrigCommonParam(int connID, void *AllSegTrigCommParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaCapParam
/// 设置Vsg common trig参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsatrigParam:VSG trig参数
/// @param[in]  Segno：segment索引
/// @param[in]  Size:参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgTrigCommonParam(int connID, int Segno, void *SegTrigCommParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsaAlzProtoParam
/// 设置seg具体协议相关分析参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Segno：segment索引
/// @param[in]  ProAnalyzeParam: 协议相关分析参数
/// @param[in]  Size: 参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAlzProtoParam(int connID, int Segno, int AlzType, void *ProAnalyzeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// AllProAnalyzeParam
/// 设置seg所有具体协议相关分析参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllProAnalyzeParam: 所有协议相关分析参数
/// @param[in]  Size: 参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAllAlzProtoParam(int connID, void *AllProAnalyzeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSeqTxSegTimeParam
/// 设置seg时间相关参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Segno：segment索引
/// @param[in]  SegTimeParam: 时间参数
/// @param[in]  Size: 参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSeqTxSegTimeParam(int connID, int Segno, void *SegTimeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSeqTxSegAllTimeParam
/// 一把设置整个seq时间相关参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllSegTimeParam: 整个seq时间参数
/// @param[in]  Size: 参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSeqTxSegAllTimeParam(int connID, void *AllSegTimeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSeqRxSegAllTimeParam
/// 一把设置整个seq时间相关参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllSegTimeParam: 整个seq时间参数
/// @param[in]  Size: 参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSeqRxSegAllTimeParam(int connID, void *AllSegTimeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetListTxSeqStart
/// 启动Tx Seq
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]        connID:连接ID
/// @param[in]  TrigerOffset:整个seq triger 偏移，当前只有tx有效
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetListTxSeqStart(int ConnID, double TrigerOffset);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetListRxSeqStart
/// 启动Rx Seq
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Repet:整个seq重复模式，当前只有rx seq有效，0为重复模式
/// @param[in]  EnableFlag:整个seq同步参数
/// @param[in]  IncrementFlag:整个seq同步参数
/// @param[in]  CellMod:整个seq模式，0常规模式，1蜂窝模式
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetListRxSeqStart(int ConnID, int Repet, int EnableFlag, int IncrementFlag, int CellMod);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSeqRxSegTimeParam
/// 设置seg时间相关参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Segno：segment索引
/// @param[in]  SegTimeParam: 时间参数
/// @param[in]  Size: 参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSeqRxSegTimeParam(int connID, int Segno, void *SegTimeParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsgParam
/// 设置Vsg参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsgParam:VSG参数
/// @param[in]  Segno：segment索引
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgParam(int connID, int Segno, VsgParameter *vsgParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsgSyncParam
/// 设置Vsg参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  Status:VSG同步参数
/// @param[in]  Segno：segment索引
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgSyncParam(int connID, int Segno, int Status);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsgAllSyncParam
/// 设置Vsg所有seg同步参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllSyncParam:所有VSG同步参数
/// @param[in]  Size：参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgAllSyncParam(int connID, int *AllSyncParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegVsgWaveParam
/// 设置Vsg波形文件参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  vsgWaveParam:VSG信号文件参数
/// @param[in]  Segno：segment索引
//////////////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgWaveParam(int connID, int Segno, VsgPattern *vsgWaveParam);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetSegAllVsgWaveParam
/// 设置所有seg Vsg波形文件参数
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  AllvsgWaveParam:VSG信号文件参数
/// @param[in]  Size：参数大小
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetSegAllVsgWaveParam(int connID, VsgPattern *AllvsgWaveParam, int Size);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetListTxRxSeqStart
/// 启动TXRx Seq
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetListTxRxSeqStart(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetListTxSeqStop
/// 停止TX Seq
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetListTxSeqStop(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetListRxSeqStop
/// 停止RX Seq
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetListRxSeqStop(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetListTxRxSeqStop
/// 停止TXRX Seq
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetListTxRxSeqStop(int connID);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListTxSeqAllState
/// 获取Tx seq状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  State:seq状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllState(int connID, int *State);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListRxSeqAllState
/// 获取Rx seq状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  State:seq状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListRxSeqAllState(int connID, int *State);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListTxSeqAllCapState
/// 获取Tx seq抓取状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  State:Tx seq抓取状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllCapState(int connID, int *SegNo);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListRxSeqAllTransState
/// 获取Rx seq发送状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  State:Rx seq发送状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListRxSeqAllTransState(int connID, int *SegNo);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListTxSeqAllAnalyState
/// 获取Tx seq分析状态
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[out]  State:Tx seq分析状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllAnalyState(int connID, int *SegNo);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListTxSeqAllPowerResult
/// 获取Tx seq power结果
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  SegNum:seg总个数
/// @param[out]  PowerResult:Tx seq power结果
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllPowerResult(int connID, double *PowerResult, int SegNum);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetListLteTxSeqAllSegState
/// 获取lte Tx seq各seg状态 
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  SegNum:seg总个数
/// @param[out]  LteTxSegStat:Tx seq 各seg状态
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetListLteTxSeqAllSegState(int connID, int *LteTxSegStat, int SegNum);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetDuplexEnable
/// 设置全双工使能
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  enable: 1开启，0关闭
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetDuplexEnable(int connID, int enable);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetDuplexEnable
/// 获取全双工使能
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  enable: 1开启，0关闭
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDuplexEnable(int connID, int *enable);

//////////////////////////////////////////////////////////////////////////////
/// WT_SetDuplexVsaNoiseCompFlag
/// 设置全双工VSA泄露噪底补偿
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  flag: 1开启，0关闭
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetDuplexVsaNoiseCompFlag(int connID, int flag);

//////////////////////////////////////////////////////////////////////////////
/// WT_GetDuplexVsaNoiseCompFlag
/// 获取全双工VSA泄露噪底补偿
/// @return 成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
/// @param[in]  flag: 1开启，0关闭
//////////////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetDuplexVsaNoiseCompFlag(int connID, int *flag);

#endif
