//*****************************************************************************
//File: defines.h
//Describe:公共基础类型定义
//Date: 2016.09.25
//*****************************************************************************

#ifndef _DEFINES_H_
#define _DEFINES_H_

typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;

typedef short s16;
typedef int s32;
typedef float FP32;
typedef void *PVOID;

typedef  unsigned char          uint8;
typedef  unsigned short         uint16;
typedef  unsigned int           uint32;
typedef  unsigned long long     uint64;
typedef  signed char            int8;
typedef  signed short           int16;
typedef  signed int             int32;
typedef  signed long long     int64;

#ifndef SECURE_DISABLE
#define SECURE_DISABLE 0 // 文件加密开关
#endif

#define  LogOutput(Ret,Info)     WTLog::Instance().LOGERR(Ret,Info)

#define PoutN(n)                 #n<<"="<<n<<"\n"
#define Pout(n)                  #n<<"="<<n<<","

#define  RetAssert(Ret,Info)   \
    if(Ret != WT_OK) \
    {                   \
        LogOutput(Ret, Info);\
        return Ret;\
    }

#define  RetBreak(Ret,Info)   \
    if(Ret != WT_OK) \
    {                   \
        LogOutput(Ret, Info);\
        break;\
    }

#define  RetContinue(Ret,Info)   \
    if(Ret != WT_OK) \
    {                   \
        LogOutput(Ret, Info);\
        continue;\
    }

#define  RetWarnning(Ret,Info)   \
    if(Ret != WT_OK) \
    {                   \
        LogOutput(Ret, Info);\
    }

#endif
