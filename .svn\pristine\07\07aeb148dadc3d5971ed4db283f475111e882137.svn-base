/*
 * commonhandler.h
 *
 * 公共部分的callback
 *  Created on: 2019-3-14
 *      Author: Administrator
 */

#ifndef COMMONHANDLER_H_
#define COMMONHANDLER_H_

#include "scpi/scpi.h"
#include <string>
#include <vector>
#ifdef __cplusplus
extern "C"
{
#endif
  bool validStreamSegmentID(int &streamID, int &segmentID);
  scpi_result_t SCPI_ResultOK(scpi_t *context, int isOK = 0);
  /* *******************************************************************
   * scpi输出类callback函数
   * *******************************************************************/
  /****************************************************************
   * Write data to SCPI output
   * @param context
   * @param data
   * @param len - lenght of data to be written
   * @return number of bytes written
   ****************************************************************/
  size_t SCPI_Write(scpi_t *context, const char *data, size_t len);
  /****************************************************************
   * SCPI Error
   * @param context
   * @param err
   * @return error number
   ****************************************************************/
  int SCPI_Error(scpi_t *context, int_fast32_t err);
  /****************************************************************
   * Write Ctrl data to SCPI output
   * @param context
   * @param err
   * @return bool
   ****************************************************************/
  scpi_result_t SCPI_Control(scpi_t *context, scpi_ctrl_name_t ctrl, scpi_reg_val_t val);
  /****************************************************************
   * SCPI reset,reset all SCPI utils
   * @param context
   * @return bool
   ****************************************************************/
  scpi_result_t SCPI_Reset(scpi_t *context);
  /****************************************************************
   * Write cache data to SCPI output
   * @param context
   * @return bool
   ****************************************************************/
  scpi_result_t SCPI_Flush(scpi_t *context);

  /* ************************************************************************
   *  基础cmd回调函数
   * ************************************************************************/
  scpi_result_t SetClearData(scpi_t *context);
  scpi_result_t GetDeviceIDN(scpi_t *context);
  scpi_result_t GetTesterNameInfo(scpi_t *context);
  scpi_result_t GetTesterAlgVersionInfo(scpi_t *context);
    scpi_result_t GetTester3GPPAlgVersionInfo(scpi_t *context); 
  scpi_result_t GetTesterCalVersionInfo(scpi_t *context);
  scpi_result_t GetTesterMeterVersionInfo(scpi_t *context);
  scpi_result_t GetTesterFt4222Info(scpi_t *context);

  scpi_result_t GetTesterIPInfo(scpi_t *context);
  scpi_result_t GetTesterIPAddressType(scpi_t *context);
  scpi_result_t GetTesterModuleCnt(scpi_t *context);
  scpi_result_t GetTesterModuleVSA(scpi_t *context);
  scpi_result_t GetTesterModuleVSG(scpi_t *context);
  scpi_result_t GetTesterModuleBP(scpi_t *context);
  scpi_result_t GetTesterTimeInfo(scpi_t *context);
  scpi_result_t GetTesterMemorySize(scpi_t *context);

  scpi_result_t GetSlaveTesterNameInfo(scpi_t *context);
  scpi_result_t GetSlaveTesterAlgVersionInfo(scpi_t *context);
  scpi_result_t GetSlaveTesterIPInfo(scpi_t *context);
  scpi_result_t GetSlaveTesterModuleCnt(scpi_t *context);
  scpi_result_t GetSlaveTesterModuleVSA(scpi_t *context);
  scpi_result_t GetSlaveTesterModuleVSG(scpi_t *context);
  scpi_result_t GetSlaveTesterModuleBP(scpi_t *context);
  scpi_result_t GetSlaveTesterInfoARB(scpi_t *context);

  scpi_result_t GetOPC(scpi_t *context);
  scpi_result_t SetRestoreDevtoFactory(scpi_t *context);
  scpi_result_t SetSleep(scpi_t *context);

  scpi_result_t SetSubNet(scpi_t *context);
  scpi_result_t GetSubNet(scpi_t *context);
  scpi_result_t GetSubNetLink(scpi_t *context);
  scpi_result_t SetSubNetTftp(scpi_t *context);
  scpi_result_t GetSubNetTftp(scpi_t *context);
  scpi_result_t SetSubNetAutoNeg(scpi_t *context); // 配置子网口自动网速开关
  scpi_result_t GetSubNetAutoNeg(scpi_t *context); // 获取子网口自动网速开关状态
  scpi_result_t GetWaveNameList(scpi_t *context);
  scpi_result_t GetMACAddr(scpi_t *context);

  scpi_result_t AddRemoteTester(scpi_t *context);
  scpi_result_t DelRemoteTester(scpi_t *context);

  scpi_result_t QueryfileMD5code(scpi_t *context);
  scpi_result_t Queryfilelength(scpi_t *context);

  scpi_result_t DumpMemoryParam(scpi_t *context);
  scpi_result_t SCPI_ReponseEnable(scpi_t *context);
  scpi_result_t SCPI_UpLoadFile(scpi_t *context);
  scpi_result_t SCPI_DownLoadFile(scpi_t *context);
  scpi_result_t SCPI_UpLoadFileRelativePath(scpi_t *context);
  scpi_result_t SCPI_DownLoadFileRelativePath(scpi_t *context);
  scpi_result_t SCPI_ExeCmd(scpi_t *context);

  scpi_result_t SCPI_SetCalibrationMode(scpi_t *context);
  scpi_result_t SCPI_GetCalibrationMode(scpi_t *context);
  scpi_result_t SCPI_ReLoadCalibrationData(scpi_t *context);
  scpi_result_t SCPI_SetCalibrationFlatness(scpi_t *context);

  scpi_result_t SCPI_ForceConnect(scpi_t *context);
  scpi_result_t SCPI_SubConnect(scpi_t *context);
  scpi_result_t SCPI_MonitorConnect(scpi_t *context);
  scpi_result_t SCPI_DiagnosisConnect(scpi_t *context);

  // monitor
  scpi_result_t SCPI_SetMonitorRfPort(scpi_t *context);
  scpi_result_t SCPI_SetMonitorAction(scpi_t *context);

  scpi_result_t GetVoltAgeArb(scpi_t *context);
  scpi_result_t GetVoltAge(scpi_t *context);
  scpi_result_t GetVoltAgeInfo(scpi_t *context, bool arb = false);
  scpi_result_t SetTestMode(scpi_t *context);
  // 线衰修正文件
  scpi_result_t SCPI_CreateJSONFile(scpi_t *context);
  scpi_result_t SCPI_ParseJSONFile(scpi_t *context);
  scpi_result_t SCPI_UploadJSONFile(scpi_t *context);
  // 下位机计时
  scpi_result_t SetTimerEnable(scpi_t *context);
  scpi_result_t GetCommandUsedTime(scpi_t *context);

  scpi_result_t SetFullDuplexEnable(scpi_t *context);
  scpi_result_t GetFullDuplexEnable(scpi_t *context);

  /**
   * @brief 设置产测认证的随机秘钥
   *
   * @param context
   * @return scpi_result_t
   */
  scpi_result_t SCPI_SetAuthenticationTK(scpi_t *context);
  /**
   * @brief 获取产测认证的加密数据
   *
   * @param context
   * @return scpi_result_t
   */
  scpi_result_t SCPI_GetAuthenticationEncryptData(scpi_t *context);

  /**
   * @brief 检测bwv/csv信号文件是否存在，不存在则报错。再检测low文件是否存在，不存在则重新建立low文件
   *
   * @param context
   * @param waveName：/tmp/wave/路径下的bwv/csv信号文件名
   * @param low_name: /tmp/low_wave/下面的.low随机信号文件名
   * @param create_low_file: 是否创建.low文件
   * @param is_vsg: false 表示VSA信号文件， true 表示VSG信号文件
   * @return int ：0 = OK，否则fail
   */
  int check_waveform_exist_v2(scpi_t *context, char *waveName, std::vector<std::string> &low_name_list, bool create_low_file = true, bool is_vsg = true);
  /**
   * @brief 生成一个随机文件名
   *
   * @return std::string
   */
  std::string get_random_name();
  /**
   * @brief 删除.low文件
   *
   * @param filename : 带路径的文件名
   */
  void remove_low_file(std::string filename, int ClientFd, int ConnID);
  /**
   * @brief 删除所有VSG遗留下来的.low文件
   *
   * @param : SPCIUserParam 指针地址
   */
  void remove_all_vsg_low(void *param);
#ifdef __cplusplus
}
#endif

#endif // VSGHANDLER_H_
