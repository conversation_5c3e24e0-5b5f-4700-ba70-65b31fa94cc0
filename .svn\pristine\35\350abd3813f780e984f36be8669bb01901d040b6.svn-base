#include "scpi_3gpp_alz_nbiot.h"

#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_common.h"

using namespace cellular;

static inline Alg_3GPP_AlzInNBIOT &Nb(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    return attr->vsaAlzParam.analyzeParam3GPP.NBIOT;
}

scpi_result_t SCPI_NBIOT_SetAnalyzeEVMEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.EvmEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeMerrEnable(scpi_t *context)
{
    int value = 0;
    int iRet = Scpi<PERSON>hecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.MErrEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzePerrEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.PErrEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeIbeEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.IBEEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeIQConstelEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.IQConstelEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzePowerDynamicEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.PwrDynEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeSEMEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.SEMEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeACLREnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.ACLREnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeTxMeasureEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.TxMeasureEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDecodingEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).View.DecEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

//UL
scpi_result_t SCPI_NBIOT_SetAnalyzeDuplexing(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Duplexing = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeOperationMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.OperationMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeChnnelBandWidth(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {200 * KHz, 3 * MHz, 5 * MHz, 10 * MHz, 15 * MHz, 20 * MHz})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.ChannelBW = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeResourceBlockIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(-47, 134))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.RBIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeCellID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 503))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.NBCellID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeChannelType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.ChanType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULMeasureExpected(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.MeasureExpect = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULMeasureSlotNumber(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.MeasSlotNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULModulateType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 18})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Modu.Modulate = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULModulateStaticCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Modu.ModStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULSemStaticCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Spect.SEMStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULACLRStaticCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Spect.ACLRStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULUTRAEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Spect.UTRAEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULGSMEnable(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Spect.GSMEnable = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeULPowerStaticCount(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Power.PowerStatNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschFormat(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 2))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.Format = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschSubCarrierSpacing(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {15000, 3750})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.SCSpacing = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschRepetTimes(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4, 8, 16, 32, 64, 128})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.Repetitions = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschResourceUnitsNumber(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 3, 4, 5, 6, 8, 10})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.RUs = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschSubCarrierNumber(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 3, 6, 12})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.SubcarrierNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschSubCarrierStart(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 47))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.StartSubcarrier = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschCyclicShift(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.CyclicShift = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschGroupHopping(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.GrpHopping = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeNPuschDeltaSquenceShift(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 29))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Npusch.DeltaSeqShift = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

// UL Enhanced
scpi_result_t SCPI_NBIOT_SetAnalyzeENhancedChannelCodingState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {0, 1})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Enhanced.ChanCodingState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeENhancedScrambling(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {0, 1})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Enhanced.Scrambling = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeENhancedUEID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 65535))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Enhanced.UeID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeENhancedStartSubFrame(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1000))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Enhanced.StartSubfrm = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeENhancedTransportBlockSizeIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Enhanced.TBSIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeENhancedRVStartIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {0, 2})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.Enhanced.StartRVIdx= value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLDuplexing(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Duplexing = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLOperationMode(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 2))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.OperationMode = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLCarrierType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.CarrierType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLChnnelBandWidth(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {200 * KHz, 3 * MHz, 5 * MHz, 10 * MHz, 15 * MHz, 20 * MHz})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.ChannelBW = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLResourceBlockIndex(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(-47, 134))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.RBIdx = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLCellID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 503))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.NBCellID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLLTECellID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 503))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.LTECellID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDL_LTEAntennaNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.LTEAntennaNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDL_NBAntennaNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(1, 2))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.NBAntennaNum = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLSIB1Switch(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.SIB1Switch = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLSIB1SchedulingInfo(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 11))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.SchedulingInfoSIB1 = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNPSSPower(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-80, 10))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.NPssPower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNSSSPower(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-80, 10))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.NSssPower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLChannelType(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {2})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.ChanType = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdschSubFrameNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 3, 4, 5, 6, 8, 10})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.NSF = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdschRepetNum(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .ParamFromList(value, {1, 2, 4, 8, 16, 32, 64, 128, 192, 256, 384, 512, 768, 1024, 1536, 2048})
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.Repetitions = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdschMCS(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 13))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.MCS = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscStartSymbol(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 3))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.StartSymb = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscStartSubFrame(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.StartSubfrm = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscPrecoding(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.Precoding = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscChanCodingState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.ChanCodingState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscScrambling(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.Scrambling = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscUEID(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 65535))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.UeID = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetAnalyzeDLNpdscPower(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-80, 10))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).DL.Npdsch.Power = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitEvmRmsState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.EvmRms.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitEvmRmsLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.EvmRms.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitEvmPeakState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.EvmPeak.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitEvmPeakLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.EvmPeak.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitMerrRmsState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.MErrRms.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitMerrRmsLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.MErrRms.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitMerrPeakState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.MErrPeak.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitMerrPeakLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.MErrPeak.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitPherrRmsState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.PhErrRms.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitPherrRmsLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.PhErrRms.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitPherrPeakState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.PhErrPeak.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitPherrPeakLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 100))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.PhErrPeak.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitFreqErrlowState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.FreqErrLow.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitFreqErrlowLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.FreqErrLow.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitFreqErrhighState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.FreqErrHigh.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitFreqErrhighLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.FreqErrHigh.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIQOffsetState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.IQOffset.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIQOffsetPwrLimit(scpi_t *context)
{
    int param_num = context->parser_state.numberOfParameters;
    const int size = std::min(param_num, static_cast<int>(sizeof(Alg_3GPP_IQOffsetLimitType::PwrLimit) / sizeof(double)));
    std::vector<double> value(size, 0);
    int iRet = ScpiChecker(context)
        .ParamArray(value.data(), size, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        for (int i = 0; i < size; i++) {
            Nb(context).UL.LimitInfo.IQOffset.PwrLimit[i] = value[i];
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIbeState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.IBE.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIbeGenmin(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.IBE.GenMin = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIbeGenpwr(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.IBE.GenPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIbeIQImage(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.IBE.IQImage[0] = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitIbeIQOffsetPwrLimit(scpi_t *context)
{
    int param_num = context->parser_state.numberOfParameters;
    const int size = std::min(param_num, static_cast<int>(sizeof(Alg_3GPP_IBELimitType::IQOffsetPwr) / sizeof(double)));
    std::vector<double> value(size, 0);
    int iRet = ScpiChecker(context)
        .ParamArray(value.data(), size, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        for (int i = 0; i < size; i++) {
            Nb(context).UL.LimitInfo.IBE.IQOffsetPwr[i] = value[i];
        }
    }
    
    return SCPI_ResultOK(context, iRet);
}

//Spectrum
scpi_result_t SCPI_NBIOT_SetLimitOBWLimitState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.OBWLimit.State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitOBWLimitLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(0, 6000))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.OBWLimit.Limit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitSEMLimitState(scpi_t *context)
{
    int param = 0;
    int value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Nb(context).UL.LimitInfo.SEMLimit)))
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.SEMLimit[param].State = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitSEMLimitStartFreq(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Nb(context).UL.LimitInfo.SEMLimit)))
        .Param(value, DOUBLE_RANGE(0, 1.9))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.SEMLimit[param].StartFreq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitSEMLimitStopFreq(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Nb(context).UL.LimitInfo.SEMLimit)))
        .Param(value, DOUBLE_RANGE(0, 1.9))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.SEMLimit[param].StopFreq = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitSEMLimitStartPower(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Nb(context).UL.LimitInfo.SEMLimit)))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.SEMLimit[param].StartPower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitSEMLimitStopPower(scpi_t *context)
{
    int param = 0;
    double value = 0;

    SCPI_CommandNumbers(context, &param, 1);
    int iRet = ScpiChecker(context)
        .CommandParam(param, INT_RANGE(0, arraySize(Nb(context).UL.LimitInfo.SEMLimit)))
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.SEMLimit[param].StopPower = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitGSMRelState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.GSM.RelState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitGSMRelLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.GSM.RelLimit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitGSMRAbsState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.GSM.AbsState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitGSMRAbsPwr(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.GSM.AbsPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitUTRARelState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.UTRA.RelState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitUTRARelLimit(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.UTRA.RelLimit = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitUTRARAbsState(scpi_t *context)
{
    int value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, INT_RANGE(0, 1))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.UTRA.AbsState = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_NBIOT_SetLimitUTRARAbsPwr(scpi_t *context)
{
    double value = 0;
    int iRet = ScpiChecker(context)
        .Param(value, DOUBLE_RANGE(-256, 256))
        .Result();

    if (iRet == WT_OK) {
        Nb(context).UL.LimitInfo.UTRA.AbsPwr = value;
    }
    
    return SCPI_ResultOK(context, iRet);
}

// // 统计次数回调函数实现开始
// scpi_result_t SCPI_NBIOT_SetAnalyzeModulationModStatNum(scpi_t *context)
// {
//     int value = 0;
//     int iRet = ScpiChecker(context)
//                    .Param(value, INT_RANGE(1, 1000))
//                    .Result();

//     if (iRet == WT_OK)
//     {
//         Nb(context).UL.Modu.ModStatNum = value;
//     }

//     return SCPI_ResultOK(context, iRet);
// }

// scpi_result_t SCPI_NBIOT_SetAnalyzeSpectrumSEMStatNum(scpi_t *context)
// {
//     int value = 0;
//     int iRet = ScpiChecker(context)
//                    .Param(value, INT_RANGE(1, 1000))
//                    .Result();

//     if (iRet == WT_OK)
//     {
//         Nb(context).UL.Spect.SEMStatNum = value;
//     }

//     return SCPI_ResultOK(context, iRet);

// }

// scpi_result_t SCPI_NBIOT_SetAnalyzeSpectrumACLRStatNum(scpi_t *context)
// {
//     int value = 0;
//     int iRet = ScpiChecker(context)
//                    .Param(value, INT_RANGE(1, 1000))
//                    .Result();

//     if (iRet == WT_OK)
//     {
//         Nb(context).UL.Spect.ACLRStatNum = value;
//     }

//     return SCPI_ResultOK(context, iRet);
// };
// // 统计次数回调函数实现结束
