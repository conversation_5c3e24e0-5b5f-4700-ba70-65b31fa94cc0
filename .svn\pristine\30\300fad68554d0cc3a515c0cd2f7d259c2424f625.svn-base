#include "scpi_gen_wifi_psdu.h"
#include "scpi_gen_tf.h"
#include "commonhandler.h"
#include <iostream>
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include <sys/time.h>
#include <bits/stdc++.h>
#include "delimiter_crc_calculate.h"
#define DEBUG_PRINT_PSDU    0
/**
 * @brief push back extend parameter pointer
 *
 * @param pnParameters : wifi PN struct
 * @param item : extend parameter pointer
 */
static void push_back_user_extendParam(void **ExtendParam, void *item)
{
    ExtParamHdr *pHeader = (ExtParamHdr *)(*ExtendParam);
    if (pHeader)
    {
        while (pHeader->Field.Next)
        {
            pHeader = (ExtParamHdr *)pHeader->Field.Next;
        }
        pHeader->Field.Next = item;
    }
    else
    {
        *ExtendParam = item;
    }
}

/**
 * @brief add user defined extend parameter to tail, PSDU first
 *
 * @param attr : Link user parameter
 */
void add_wave_extend_param(SPCIUserParam *attr)
{
    int demod = attr->WaveGenDemod;
    void **pHeader = nullptr;
    if(demod == WT_DEMOD_BT)
    {
        pHeader = &(attr->PnBt.get()->ExtendParam);
    }
    else //wifi
    {
        pHeader = &(attr->PnWifi.get()->ExtendParam);
    }

    auto itor = attr->WaveUserDefineExtendParam.find(WT_EXT_PARAM_PSDU);
    if (itor != attr->WaveUserDefineExtendParam.end())
    {
        if (itor->second.UserDefineData != nullptr)
        {
            push_back_user_extendParam(pHeader, itor->second.UserDefineData.get());
        }
    }

    for (auto &item : attr->WaveUserDefineExtendParam)
    {
        if (item.first != WT_EXT_PARAM_PSDU && item.second.UserDefineData != nullptr)
        {
            push_back_user_extendParam(pHeader, item.second.UserDefineData.get());
        }
    }
}

static unsigned char HexChar2Byte(const char value)
{
    char tmp = toupper(value);
    return (tmp > '9' ? (tmp - 'A' + 10) : (tmp - '0'));
}

static int ConvertHexStringToBytes(unsigned char *Src, unsigned char *Dest, int length)
{
    unsigned char *srcTemp = Src;
    unsigned char c = 0;
    for (int i = 0; i < length; i++)
    {
        c = HexChar2Byte(*srcTemp++);
        if (c >= 16)
        {
            return -1;
        }
        Dest[i] = (c << 4);
        c = HexChar2Byte(*srcTemp++);
        if (c >= 16)
        {
            return -1;
        }
        Dest[i] |= c;
    }
    return 0;
}

static int GetUserDefinedPSDU_Len(WIFI_PSDU *psdu, UserDefindPSDU *userData, bool AGG_ON = false)
{
    int Len = 0;
    do
    {
        if (enPSDUType_USER != psdu->psduType)
        {
            if (userData->DataLen > 0 || nullptr != userData->Data)
            {
                userData->DataLen = 0;
                userData->reset();
            }
            break;
        }
        // sizeof(segmentID + RUID + UserID + userData->DataLen)
        Len += 4 * sizeof(int);
        Len += userData->DataLen;
        //同步把WIFI PSDU长度修改为User define的长度
        psdu->psduLen = userData->DataLen;
        if (AGG_ON)
        {
            //此处的MPDU length不包含MPDU delimter以及padding
            if (psdu->MPDUCount < 1)
            {
                psdu->MPDUCount = 1;
            }
            psdu->psduLen = 0;
            for (int j = 0; j < psdu->MPDUCount; j++)
            {
                if (psdu->MPDULength[j] > 0) //有数据
                {
                    if (j < psdu->MPDUCount - 1)
                    {
                        psdu->psduLen += ((psdu->MPDULength[j] + 4 + 3) / 4 * 4);
                    }
                    else
                    {
                        psdu->psduLen += (psdu->MPDULength[j] + 4);
                    }
                }
            }
        }
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "User defined PSDU = {" << std::endl;
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Blank(4) << "AGG_ON = " << (true == AGG_ON ? "true" : "false") << std::endl;
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Blank(4) << "MPDUCount = " << psdu->MPDUCount << std::endl;
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Blank(4) << "Total PSDU length(MPDU delimiter + MPDU data length + MPDU padding) = " << psdu->psduLen << std::endl;
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << Blank(4) << "User data length(ARB) = " << userData->DataLen << std::endl;
//        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "}" <<std::endl;

    } while (0);

//    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "sizeof(segment ID + RU ID + User ID + userData->DataLen) + userData->DataLen = " << Len << std::endl;
    return Len;
}

static int AllocUserDefinePsduHeader(SPCIUserParam *attr, int &TotalLen, int &offset)
{
	UserDefinedDataClass obj;
    ExtParamHdr hdr;
    memset(&hdr, 0, sizeof(hdr));

    hdr.Type = WT_EXT_PARAM_PSDU;
    hdr.Len = TotalLen;

    TotalLen += sizeof(hdr);

    obj.UserDefineData.reset(new char[TotalLen]);
    obj.UserDefineDataLen = TotalLen;

    memcpy(obj.UserDefineData.get(), &hdr, sizeof(hdr));
    offset += sizeof(hdr);

    attr->WaveUserDefineExtendParam[hdr.Type] = std::move(obj);

    return WT_ERR_CODE_OK;
}

static void CopyUserDefinedPSDU(
    SPCIUserParam *attr,
    int &offset,
    UserDefindPSDU *userData,
    int SegmentID,
    int RUID,
    int UserID)
{
    if (0 == userData->DataLen || nullptr == userData->Data)
    {
        return;
    }
    auto itor = attr->WaveUserDefineExtendParam.find(WT_EXT_PARAM_PSDU);
    if (itor == attr->WaveUserDefineExtendParam.end())
    {
        return;
    }
    char *pDest = itor->second.UserDefineData.get() + offset;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SegmentID = " << SegmentID << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RUID = " << RUID << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "UserID = " << UserID << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DataLen = " << userData->DataLen << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===================" << std::endl;

    int pos = 0;
    memcpy(pDest + pos, &SegmentID, sizeof(int));
    pos += sizeof(int);
    memcpy(pDest + pos, &RUID, sizeof(int));
    pos += sizeof(int);
    memcpy(pDest + pos, &UserID, sizeof(int));
    pos += sizeof(int);
    memcpy(pDest + pos, &userData->DataLen, sizeof(int));
    pos += sizeof(int);
    memcpy(pDest + pos, userData->Data.get(), userData->DataLen);
    pos += userData->DataLen;
    offset += pos;
}

static void ConvertPnPSDU_Legacy(SPCIUserParam *attr, WIFI_PSDU *psdu, bool AGG_ON = false)
{
    int userDefinePsduLen = 0;
    int offset = 0;

    memcpy(psdu, &attr->PnPSDU->PSDU[0][0][0], sizeof(WIFI_PSDU));

    userDefinePsduLen += GetUserDefinedPSDU_Len(
        psdu,
        &attr->PnPSDU->PSDU_User[0][0][0],
        AGG_ON);

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        CopyUserDefinedPSDU(
            attr,
            offset,
            &attr->PnPSDU->PSDU_User[0][0][0],
            0,
            0,
            0);
    }
}

static void ConvertPnPSDU_ACMUMIMO(SPCIUserParam *attr)
{
    int userDefinePsduLen = 0;
    int offset = 0;
    for (int i = 0; i < AC_MUMIMO_4_USER; i++)
    {
        if(i < attr->PnWifi->PN11ac_MUMIMO.UserNum)
        {
            memcpy(&attr->PnWifi->PN11ac_MUMIMO.User[i].psdu,
                   &attr->PnPSDU->PSDU[0][0][i],
                   sizeof(WIFI_PSDU));

            userDefinePsduLen += GetUserDefinedPSDU_Len(
                &attr->PnWifi->PN11ac_MUMIMO.User[i].psdu,
                &attr->PnPSDU->PSDU_User[0][0][i],
                true);
        }
    }

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        for (int i = 0; i < AC_MUMIMO_4_USER; i++)
        {
            if(i < attr->PnWifi->PN11ac_MUMIMO.UserNum)
            {
                CopyUserDefinedPSDU(
                    attr,
                    offset,
                    &attr->PnPSDU->PSDU_User[0][0][i],
                    0,
                    0,
                    i);
            }
        }
    }
}

static void ConvertPnPSDU_AHMUMIMO(SPCIUserParam *attr)
{
    int userDefinePsduLen = 0;
    int offset = 0;
    for (int i = 0; i < AH_MUMIMO_4_USER; i++)
    {
        if(i < attr->PnWifi->PN11ah_MUMIMO.UserNum)
        {
            memcpy(&attr->PnWifi->PN11ah_MUMIMO.User[i].psdu,
                   &attr->PnPSDU->PSDU[0][0][i],
                   sizeof(WIFI_PSDU));

            userDefinePsduLen += GetUserDefinedPSDU_Len(
                &attr->PnWifi->PN11ah_MUMIMO.User[i].psdu,
                &attr->PnPSDU->PSDU_User[0][0][i],
                true);
        }
    }

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        for (int i = 0; i < AH_MUMIMO_4_USER; i++)
        {
            if(i < attr->PnWifi->PN11ah_MUMIMO.UserNum)
            {
                CopyUserDefinedPSDU(
                    attr,
                    offset,
                    &attr->PnPSDU->PSDU_User[0][0][i],
                    0,
                    0,
                    i);
            }
        }
    }
}

static void ConvertPnPSDU_HEMUMIMO(SPCIUserParam *attr)
{
    int userDefinePsduLen = 0;
    int offset = 0;

    for (int i = 0; i < AX_RU_COUNT; i++)
    {
        if (attr->PnRU->RUIndex[i] >= 0)
        {
            int RUID = attr->PnRU->RUIndex[i];
            for (int j = 0; j < MUMIMO_8_USER; j++)
            {
                if(j < attr->PnWifi->PN11ax_MU.RU[RUID].UserNum)
                {
                    memcpy(&attr->PnWifi->PN11ax_MU.RU[RUID].User[j].psdu,
                        &attr->PnPSDU->PSDU[0][i][j],
                        sizeof(WIFI_PSDU));
                    userDefinePsduLen += GetUserDefinedPSDU_Len(
                        &attr->PnWifi->PN11ax_MU.RU[RUID].User[j].psdu,
                        &attr->PnPSDU->PSDU_User[0][i][j],
                        true);
                }
            }
        }
    }

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        for (int i = 0; i < AX_RU_COUNT; i++)
        {
            if (attr->PnRU->RUIndex[i] >= 0)
            {
                int RUID = attr->PnRU->RUIndex[i];
                for (int j = 0; j < MUMIMO_8_USER; j++)
                {
                    if (j < attr->PnWifi->PN11ax_MU.RU[RUID].UserNum)
                    {
                        CopyUserDefinedPSDU(
                            attr,
                            offset,
                            &attr->PnPSDU->PSDU_User[0][i][j],
                            0,
                            RUID,
                            j);

                    }
                }
            }
        }
    }
}

static void ConvertPnPSDU_EHT_MU(SPCIUserParam *attr)
{
    int userDefinePsduLen = 0;
    int offset = 0;

    for (int i = 0; i < BE_RU_COUNT; i++)
    {
        if (attr->PnRU->RUIndex[i] >= 0)
        {
            int RUID = attr->PnRU->RUIndex[i];
            for (int j = 0; j < MUMIMO_8_USER; j++)
            {
                if (j < attr->PnWifi->PN11be_MU.RU[RUID].UserNum)
                {
                    memcpy(&attr->PnWifi->PN11be_MU.RU[RUID].User[j].psdu,
                        &attr->PnPSDU->PSDU[0][i][j],
                        sizeof(WIFI_PSDU));
                    userDefinePsduLen += GetUserDefinedPSDU_Len(
                        &attr->PnWifi->PN11be_MU.RU[RUID].User[j].psdu,
                        &attr->PnPSDU->PSDU_User[0][i][j],
                        true);
                }
            }
        }
    }

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        for (int i = 0; i < BE_RU_COUNT; i++)
        {
            if (attr->PnRU->RUIndex[i] >= 0)
            {
                int RUID = attr->PnRU->RUIndex[i];
                for (int j = 0; j < MUMIMO_8_USER; j++)
                {
                    if (j < attr->PnWifi->PN11be_MU.RU[RUID].UserNum)
                    {
                        CopyUserDefinedPSDU(
                            attr,
                            offset,
                            &attr->PnPSDU->PSDU_User[0][i][j],
                            0,
                            RUID,
                            j);
                    }
                }
            }
        }
    }
}

static void ConvertPnPSDU_TBMUMIMO(SPCIUserParam *attr)
{
    int userDefinePsduLen = 0;
    int offset = 0;

    for (int SegmentID = 0; SegmentID < MAX_SEGMENT; SegmentID++)
    {
        for (int RUID = 0; RUID < AX_RU_COUNT; RUID++)
        {
            for (int UserID = 0; UserID < MUMIMO_8_USER; UserID++)
            {
                if (UserID < attr->PnWifi->PN11ax_TB.RU[SegmentID][RUID].UserNum)
                {
                    memcpy(
                        &attr->PnWifi->PN11ax_TB.RU[SegmentID][RUID].User[UserID].psdu,
                        &attr->PnPSDU->PSDU[SegmentID][RUID][UserID],
                        sizeof(WIFI_PSDU));
                    userDefinePsduLen += GetUserDefinedPSDU_Len(
                        &attr->PnWifi->PN11ax_TB.RU[SegmentID][RUID].User[UserID].psdu,
                        &attr->PnPSDU->PSDU_User[SegmentID][RUID][UserID],
                        true);
                }

            }
        }
    }

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        for (int SegmentID = 0; SegmentID < MAX_SEGMENT; SegmentID++)
        {
            for (int RUID = 0; RUID < AX_RU_COUNT; RUID++)
            {
                for (int UserID = 0; UserID < MUMIMO_8_USER; UserID++)
                {
                    if (UserID < attr->PnWifi->PN11ax_TB.RU[SegmentID][RUID].UserNum)
                    {
                        CopyUserDefinedPSDU(
                            attr,
                            offset,
                            &attr->PnPSDU->PSDU_User[SegmentID][RUID][UserID],
                            SegmentID,
                            RUID,
                            UserID);
                    }
                }
            }
        }
    }
}

static void ConvertPnPSDU_TBMUMIMO_11Be(SPCIUserParam *attr)
{
    int userDefinePsduLen = 0;
    int offset = 0;

    for (int SegmentID = 0; SegmentID < BE_MAX_SEGMENT; SegmentID++)
    {
        for (int RUID = 0; RUID < AX_RU_COUNT; RUID++)
        {
            for (int UserID = 0; UserID < MUMIMO_8_USER; UserID++)
            {
                if (UserID < attr->PnWifi->PN11be_TB.RU[SegmentID][RUID].UserNum)
                {
                    memcpy(
                        &attr->PnWifi->PN11be_TB.RU[SegmentID][RUID].User[UserID].psdu,
                        &attr->PnPSDU->PSDU[SegmentID][RUID][UserID],
                        sizeof(WIFI_PSDU));
                    userDefinePsduLen += GetUserDefinedPSDU_Len(
                        &attr->PnWifi->PN11be_TB.RU[SegmentID][RUID].User[UserID].psdu,
                        &attr->PnPSDU->PSDU_User[SegmentID][RUID][UserID],
                        true);
                }
            }
        }
    }

    if (userDefinePsduLen > 0)
    {
        AllocUserDefinePsduHeader(attr, userDefinePsduLen, offset);
        for (int SegmentID = 0; SegmentID < BE_MAX_SEGMENT; SegmentID++)
        {
            for (int RUID = 0; RUID < AX_RU_COUNT; RUID++)
            {
                for (int UserID = 0; UserID < MUMIMO_8_USER; UserID++)
                {
                    if (UserID < attr->PnWifi->PN11be_TB.RU[SegmentID][RUID].UserNum)
                    {
                        CopyUserDefinedPSDU(
                            attr,
                            offset,
                            &attr->PnPSDU->PSDU_User[SegmentID][RUID][UserID],
                            SegmentID,
                            RUID,
                            UserID);
                    }
                }
            }
        }
    }
}

#if DEBUG_PRINT_PSDU
static int print_SIG_data(const char *data, size_t len)
{
    int iRet = WT_ERR_CODE_OK;
    using DataHeader = struct
    {
        int ID;
        unsigned int DataLen;
    };
    DataHeader *pHeader = nullptr;

    int offset = 0;
    char tmpBuf[256] = {0};
    while (offset < len)
    {
        pHeader = (DataHeader *)(data + offset);
        sprintf(tmpBuf, "ID = %d, data", pHeader->ID);
        offset += sizeof(DataHeader);
        hexdump(tmpBuf, (const u8 *)data + offset, pHeader->DataLen);
        offset += pHeader->DataLen;
    }
    return iRet;
}

static void print_ExtParam(ExtParamHdr *hdr, int index)
{
    char *param = (char *)hdr;

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************"
              << "Index = " << index << "****************" << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Total len = " << sizeof(ExtParamHdr) + hdr->Len << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "hdr->Type = " << hdr->Type << std::endl;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "hdr->Len = " << hdr->Len << std::endl;

    int offset = sizeof(ExtParamHdr);
    int totalLen = sizeof(ExtParamHdr) + hdr->Len;
    if (WT_EXT_PARAM_PSDU == hdr->Type)
    {
        while (offset < totalLen)
        {
            UserDefinedPSDU *psdu = (UserDefinedPSDU *)(param + offset);
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "psdu->SegmentID = " << psdu->SegmentID << std::endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "psdu->RUID = " << psdu->RUID << std::endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "psdu->UserID = " << psdu->UserID << std::endl;
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "psdu->DataLen = " << psdu->DataLen << std::endl;
            offset += sizeof(psdu->SegmentID);
            offset += sizeof(psdu->RUID);
            offset += sizeof(psdu->UserID);
            offset += sizeof(psdu->DataLen);
            offset += psdu->DataLen; // data
        }
    }
    else if (WT_EXT_PARAM_PHY_SIGA == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is SIGA" << std::endl;
    }
    else if (WT_EXT_PARAM_PHY_HTSIG == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is HT-SIG" << std::endl;
    }
    else if (WT_EXT_PARAM_PHY_LSIG == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is LSIG" << std::endl;
    }
    else if (WT_EXT_PARAM_PHY_VHT_SIGB == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is VHT SIGB" << std::endl;
        print_SIG_data(param + offset, hdr->Len);
    }
    else if (WT_EXT_PARAM_PHY_HE_SIGB == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is HE SIGB" << std::endl;
        print_SIG_data(param + offset, hdr->Len);
    }
    else if (WT_EXT_PARAM_PHY_EHT_SIG == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is EHT SIG" << std::endl;
        print_SIG_data(param + offset, hdr->Len);
    }
    else if (WT_EXT_PARAM_PHY_EHT_USIG == hdr->Type)
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "This is EHT USIG" << std::endl;
        print_SIG_data(param + offset, hdr->Len);
    }
}

static void parserExtParam(SPCIUserParam *attr)
{
    GenWaveWifiStruct_API *pnParameters = attr->PnWifi.get();
    if (!pnParameters->ExtendParam)
    {
        return;
    }
    char *param = (char *)pnParameters->ExtendParam;
    int index = 0;
    ExtParamHdr *hdr = (ExtParamHdr *)param;
    print_ExtParam(hdr, index++);
    while (hdr->Field.Next)
    {
        hdr = (ExtParamHdr *)hdr->Field.Next;
        print_ExtParam(hdr, index++);
    }

}
#endif

void ConvertPNStruct(SPCIUserParam *attr)
{
    GenWaveWifiStruct_API *pnParameters = attr->PnWifi.get();
    WIFI_PSDU *psdu = nullptr;
    int demod = pnParameters->commonParam.standard;
    int PPDU = pnParameters->commonParam.subType;

    switch (demod)
    {
    case WT_DEMOD_11AG:
        psdu = &pnParameters->PN11a.psdu;
        break;
    case WT_DEMOD_11B:
        psdu = &pnParameters->PN11b.psdu;
        break;
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        psdu = &pnParameters->PN11n.psdu;
        break;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        if (VHT_MUMIMO_PPDU != PPDU)
        {
            psdu = &pnParameters->PN11ac.psdu;
        }
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        switch (PPDU)
        {
        case HE_SU_PPDU:
        case HE_EXTEND_PPDU:
            psdu = &pnParameters->PN11ax_SU.psdu;
            break;
        default:
            break;
        }
        break;

    case WT_DEMOD_11AH_1M:
    case WT_DEMOD_11AH_2M:
    case WT_DEMOD_11AH_4M:
    case WT_DEMOD_11AH_8M:
    case WT_DEMOD_11AH_16M:
        psdu = &pnParameters->PN11ah.psdu;
        break;
    default:
        break;
    }
    if (demod >= WT_DEMOD_11BE_20M && demod <= WT_DEMOD_11BE_160_160M)
    {
        switch (PPDU)
        {
        case EHT_MU_PPDU:
            ConvertPnPSDU_EHT_MU(attr);
            break;
        case EHT_TB_PPDU:
            ConvertPnPSDU_TBMUMIMO_11Be(attr);
            break;
        default:
            break;
        }
    }
    else if (demod >= WT_DEMOD_11AX_20M && demod <= WT_DEMOD_11AX_80_80M)
    {
        switch (PPDU)
        {
        case HE_MU_PPDU:
            ConvertPnPSDU_HEMUMIMO(attr);
            break;
        case HE_TB_PPDU:
            ConvertPnPSDU_TBMUMIMO(attr);
            break;
        default:
            if (psdu)
            {
                ConvertPnPSDU_Legacy(attr, psdu, true);
            }
            break;
        }
    }
    else if (demod >= WT_DEMOD_11AC_20M && demod <= WT_DEMOD_11AC_80_80M)
    {
        switch (PPDU)
        {
        case VHT_MUMIMO_PPDU:
            ConvertPnPSDU_ACMUMIMO(attr);
            break;
        default:
            ConvertPnPSDU_Legacy(attr, psdu, true);
            break;
        }
    }
    else if (demod >= WT_DEMOD_11N_20M && demod <= WT_DEMOD_11N_40M)
    {
        if (psdu)
        {
            ConvertPnPSDU_Legacy(attr, psdu, pnParameters->PN11n.isAggregation);
        }
    }
    else if(demod >= WT_DEMOD_11AH_1M && demod <= WT_DEMOD_11AH_16M)
    {
        switch (PPDU)
        {
        case SUB_1G_LONG_MU_MIMO:
            ConvertPnPSDU_AHMUMIMO(attr);
            break;
        default:
            if(psdu)
            {
                ConvertPnPSDU_Legacy(attr, psdu, pnParameters->PN11ah.Aggregation);
            }
            break;
        }
    }
    else
    {
        if (psdu)
        {
            ConvertPnPSDU_Legacy(attr, psdu);
        }
    }
    add_wave_extend_param(attr);
#if DEBUG_PRINT_PSDU
    parserExtParam(attr);
#endif
    return;
}

/////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////
static int GetPSDUIntParam(
    scpi_t *context,
    int *RUID,
    int *UserID,
    int *SegmentID,
    int *Value)
{
    int iRet = WT_ERR_CODE_OK;
    int32_t numbers[3] = {0, 0, 0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        if (!SCPI_ParamInt(context, Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        *RUID = numbers[0];
        *UserID = numbers[1];
        *SegmentID = numbers[2];
    } while (0);

    return iRet;
}

static int GetPSDUIntParam(
    scpi_t *context,
    int *RUID,
    int *UserID,
    int *SegmentID,
    int *Value,
    int *ValueCnt)
{
    int iRet = WT_ERR_CODE_OK;
    int paramCnt = 0;
    int32_t numbers[3] = {0, 0, 0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        for (int i = 0; i < context->parser_state.numberOfParameters && i < (*ValueCnt + 1); i++)
        {
            if (!SCPI_ParamInt(context, &Value[i], false))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
            paramCnt++;
        }
        *RUID = numbers[0];
        *UserID = numbers[1];
        *SegmentID = numbers[2];

        *ValueCnt = paramCnt;
    } while (0);

    return iRet;
}

static int GetPSDUStrParam(
    scpi_t *context,
    int *RUID,
    int *UserID,
    int *SegmentID,
    char *Value,
    int bufLen)
{
    int iRet = WT_ERR_CODE_OK;
    int32_t numbers[3] = {0, 0, 0};
    size_t copyLen = 0;
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        if (!SCPI_ParamCopyText(context, Value, bufLen - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        *RUID = numbers[0];
        *UserID = numbers[1];
        *SegmentID = numbers[2];
    } while (0);

    return iRet;
}

#define SetPSDUIntValue(attr, name, Value, SegmentID, RUID, UserID)                     \
    do                                                                                  \
    {                                                                                   \
        if (0 == SegmentID && 0 == RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                for (int j = 0; j < BE_RU_COUNT; j++)                                   \
                {                                                                       \
                    for (int k = 0; k < MUMIMO_8_USER; k++)                             \
                    {                                                                   \
                        WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][j][k];                 \
                        dest->name = Value;                                             \
                    }                                                                   \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (0 == SegmentID && RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                for (int k = 0; k < MUMIMO_8_USER; k++)                                 \
                {                                                                       \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][RUID - 1][k];              \
                    dest->name = Value;                                                 \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (0 == SegmentID && 0 == RUID && UserID)                                 \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                for (int j = 0; j < BE_RU_COUNT; j++)                                   \
                {                                                                       \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][j][UserID - 1];            \
                    dest->name = Value;                                                 \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (0 == SegmentID && RUID && UserID)                                      \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][RUID - 1][UserID - 1];         \
                dest->name = Value;                                                     \
            }                                                                           \
        }                                                                               \
        if (1 == SegmentID && 0 == RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int j = 0; j < BE_RU_COUNT; j++)                                       \
            {                                                                           \
                for (int k = 0; k < MUMIMO_8_USER; k++)                                 \
                {                                                                       \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][j][k];         \
                    dest->name = Value;                                                 \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (1 == SegmentID && RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int k = 0; k < MUMIMO_8_USER; k++)                                     \
            {                                                                           \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][RUID - 1][k];      \
                dest->name = Value;                                                     \
            }                                                                           \
        }                                                                               \
        else if (1 == SegmentID && 0 == RUID && UserID)                                 \
        {                                                                               \
            for (int j = 0; j < BE_RU_COUNT; j++)                                       \
            {                                                                           \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][j][UserID - 1];    \
                dest->name = Value;                                                     \
            }                                                                           \
        }                                                                               \
        else if (SegmentID && RUID && UserID)                                           \
        {                                                                               \
            WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][RUID - 1][UserID - 1]; \
            dest->name = Value;                                                         \
        }                                                                               \
    } while (0)

#define SetPSDUIntArrayValue(attr, name, index, Value, SegmentID, RUID, UserID)         \
    do                                                                                  \
    {                                                                                   \
        if (0 == SegmentID && 0 == RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                for (int j = 0; j < BE_RU_COUNT; j++)                                   \
                {                                                                       \
                    for (int k = 0; k < MUMIMO_8_USER; k++)                             \
                    {                                                                   \
                        WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][j][k];                 \
                        dest->name[index] = Value;                                      \
                    }                                                                   \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (0 == SegmentID && RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                for (int k = 0; k < MUMIMO_8_USER; k++)                                 \
                {                                                                       \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][RUID - 1][k];              \
                    dest->name[index] = Value;                                          \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (0 == SegmentID && 0 == RUID && UserID)                                 \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                for (int j = 0; j < BE_RU_COUNT; j++)                                   \
                {                                                                       \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][j][UserID - 1];            \
                    dest->name[index] = Value;                                          \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (0 == SegmentID && RUID && UserID)                                      \
        {                                                                               \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                     \
            {                                                                           \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][RUID - 1][UserID - 1];         \
                dest->name[index] = Value;                                              \
            }                                                                           \
        }                                                                               \
        if (1 == SegmentID && 0 == RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int j = 0; j < BE_RU_COUNT; j++)                                       \
            {                                                                           \
                for (int k = 0; k < MUMIMO_8_USER; k++)                                 \
                {                                                                       \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][j][k];         \
                    dest->name[index] = Value;                                          \
                }                                                                       \
            }                                                                           \
        }                                                                               \
        else if (1 == SegmentID && RUID && 0 == UserID)                                 \
        {                                                                               \
            for (int k = 0; k < MUMIMO_8_USER; k++)                                     \
            {                                                                           \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][RUID - 1][k];      \
                dest->name[index] = Value;                                              \
            }                                                                           \
        }                                                                               \
        else if (1 == SegmentID && 0 == RUID && UserID)                                 \
        {                                                                               \
            for (int j = 0; j < BE_RU_COUNT; j++)                                       \
            {                                                                           \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][j][UserID - 1];    \
                dest->name[index] = Value;                                              \
            }                                                                           \
        }                                                                               \
        else if (SegmentID && RUID && UserID)                                           \
        {                                                                               \
            WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][RUID - 1][UserID - 1]; \
            dest->name[index] = Value;                                                  \
        }                                                                               \
    } while (0)

#define SetPSDUStrValue(attr, name, Value, SegmentID, RUID, UserID)                                         \
    do                                                                                                      \
    {                                                                                                       \
        if (0 == SegmentID && 0 == RUID && 0 == UserID)                                                     \
        {                                                                                                   \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                                         \
            {                                                                                               \
                for (int j = 0; j < BE_RU_COUNT; j++)                                                       \
                {                                                                                           \
                    for (int k = 0; k < MUMIMO_8_USER; k++)                                                 \
                    {                                                                                       \
                        WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][j][k];                                     \
                        ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name)); \
                    }                                                                                       \
                }                                                                                           \
            }                                                                                               \
        }                                                                                                   \
        else if (0 == SegmentID && RUID && 0 == UserID)                                                     \
        {                                                                                                   \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                                         \
            {                                                                                               \
                for (int k = 0; k < MUMIMO_8_USER; k++)                                                     \
                {                                                                                           \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][RUID - 1][k];                                  \
                    ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));     \
                }                                                                                           \
            }                                                                                               \
        }                                                                                                   \
        else if (0 == SegmentID && 0 == RUID && UserID)                                                     \
        {                                                                                                   \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                                         \
            {                                                                                               \
                for (int j = 0; j < BE_RU_COUNT; j++)                                                       \
                {                                                                                           \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][j][UserID - 1];                                \
                    ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));     \
                }                                                                                           \
            }                                                                                               \
        }                                                                                                   \
        else if (0 == SegmentID && RUID && UserID)                                                          \
        {                                                                                                   \
            for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)                                         \
            {                                                                                               \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[i][RUID - 1][UserID - 1];                             \
                ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));         \
            }                                                                                               \
        }                                                                                                   \
        if (1 == SegmentID && 0 == RUID && 0 == UserID)                                                     \
        {                                                                                                   \
            for (int j = 0; j < BE_RU_COUNT; j++)                                                           \
            {                                                                                               \
                for (int k = 0; k < MUMIMO_8_USER; k++)                                                     \
                {                                                                                           \
                    WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][j][k];                             \
                    ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));     \
                }                                                                                           \
            }                                                                                               \
        }                                                                                                   \
        else if (1 == SegmentID && RUID && 0 == UserID)                                                     \
        {                                                                                                   \
            for (int k = 0; k < MUMIMO_8_USER; k++)                                                         \
            {                                                                                               \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][RUID - 1][k];                          \
                ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));         \
            }                                                                                               \
        }                                                                                                   \
        else if (1 == SegmentID && 0 == RUID && UserID)                                                     \
        {                                                                                                   \
            for (int j = 0; j < BE_RU_COUNT; j++)                                                           \
            {                                                                                               \
                WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][j][UserID - 1];                        \
                ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));         \
            }                                                                                               \
        }                                                                                                   \
        else if (SegmentID && RUID && UserID)                                                               \
        {                                                                                                   \
            WIFI_PSDU *dest = &attr->PnPSDU->PSDU[SegmentID - 1][RUID - 1][UserID - 1];                     \
            ConvertHexStringToBytes((unsigned char *)Value, dest->name, ARRAYSIZE(dest->name));             \
        }                                                                                                   \
    } while (0)

scpi_result_t SetWaveGen_PSDUPayloadType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, psduType, Value, SegmentID, RUID, UserID);

        InitTFMem(context);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUScrambler(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int RandomValue = 128;
        GenWaveWifiStruct_API *pnParameters = attr->PnWifi.get();
        int demod = pnParameters->commonParam.standard;
        if (demod >= WT_DEMOD_11BE_20M && demod <= WT_DEMOD_11BE_160_160M)
        {
            RandomValue = 1024;
        }

        if (Value > RandomValue)
        {
            Value = RandomValue;
        }

        if (Value == RandomValue)
        {
            std::random_device rd;
            mt19937 rand_generator(rd());
            uniform_int_distribution<int> dist(1, RandomValue - 1);
            Value = dist(rand_generator);
        }

        SetPSDUIntValue(attr, scrambler, Value, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUCRC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, CRCCheckEnable, Value, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDULength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        //        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "RUID = " << RUID << ", UserID =" << UserID << ", SegmentID =" << SegmentID << std::endl;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        //11b时，如果选择信号类型为1M和2M的时候，psdulen限制长度规则如下：采样率120M下限制长度不大于4095; 采样率480M下限制到1-1023，算法和界面控制报错
        SetPSDUIntValue(attr, psduLen, Value, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDU_BaseBandTest(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, BaseBandTestFlag, Value, SegmentID, RUID, UserID);
        if(Value == 1)//特殊处理，当开启BaseBandTestFlag时，强制mpdu count置空下0，恢复初始状态，好让配置给算法的len使用的是psdulen~
        {
            SetPSDUIntValue(attr, MPDUCount, 0, SegmentID, RUID, UserID);
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUMACEnable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, MacHeaderEnable, Value, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUMACFCtrlUser(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    char Value[256] = {0};
    do
    {
        iRet = GetPSDUStrParam(context, &RUID, &UserID, &SegmentID, Value, sizeof(Value));
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUStrValue(attr, FrameControl, Value, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUMACFCtrlDuration(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    char Value[256] = {0};
    do
    {
        iRet = GetPSDUStrParam(context, &RUID, &UserID, &SegmentID, Value, sizeof(Value));
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUStrValue(attr, DurationID, Value, SegmentID, RUID, UserID);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUMACFCtrlSequence(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    char Value[256] = {0};
    do
    {
        iRet = GetPSDUStrParam(context, &RUID, &UserID, &SegmentID, Value, sizeof(Value));
        if (iRet)
        {
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUStrValue(attr, SequenceControl, Value, SegmentID, RUID, UserID);

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUMACAddress(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int MACID = 0;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    char Value[256] = {0};

    int32_t numbers[4] = {1, 0, 0, 0};
    size_t copyLen = 0;
    do
    {
        SCPI_CommandNumbers(context, numbers, 4);
        if (!SCPI_ParamCopyText(context, Value, sizeof(Value) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        MACID = (0 == numbers[0] ? 1 : numbers[0]);
        RUID = numbers[1];
        UserID = numbers[2];
        SegmentID = numbers[3];

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (MACID)
        {
        case 1:
            SetPSDUStrValue(attr, MacAddress1, Value, SegmentID, RUID, UserID);
            break;
        case 2:
            SetPSDUStrValue(attr, MacAddress2, Value, SegmentID, RUID, UserID);
            break;
        case 3:
            SetPSDUStrValue(attr, MacAddress3, Value, SegmentID, RUID, UserID);
            break;
        case 4:
            SetPSDUStrValue(attr, MacAddress4, Value, SegmentID, RUID, UserID);
            break;
        default:
            SetPSDUStrValue(attr, MacAddress1, Value, SegmentID, RUID, UserID);
            break;
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDUMPDU(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value[MAX_MPDU_COUNT] = {0};
    int ValueCnt = MAX_MPDU_COUNT;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, Value, &ValueCnt);
        if (iRet)
        {
            break;
        }

        if (ValueCnt - 1 != Value[0])
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, MPDUCount, Value[0], SegmentID, RUID, UserID);
        for (int m = 1; m < ValueCnt; m++)
        {
            if (Value[m] < 0) //allow empty A-MPDU,only delimiter
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        if (iRet)
        {
            break;
        }

        for (int m = 1; m < ValueCnt; m++)
        {
            SetPSDUIntArrayValue(attr, MPDULength, (m - 1), Value[m], SegmentID, RUID, UserID);
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDURandomSeed(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        ILLEGAL_PARAM_RETURN(Value !=0 && Value != 1);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, FixedRandomSeed, Value, SegmentID, RUID, UserID);
        //printf("###Set RandomSeed %d\n", Value);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_PSDU_EOFPaddingType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;
    int Value = 0;
    do
    {
        iRet = GetPSDUIntParam(context, &RUID, &UserID, &SegmentID, &Value);
        if (iRet)
        {
            break;
        }
        ILLEGAL_PARAM_RETURN(Value !=0 && Value != 1);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        SetPSDUIntValue(attr, EofPaddingType, Value, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

static void PayLoadDataCopy(UserDefindPSDU *dest, char *srcData, int len)
{
    dest->DataLen = len;
    dest->Data.reset(new char[len]);
    memcpy(dest->Data.get(), srcData, len);
}

static int SetPayloadData(SPCIUserParam *attr, char *data, int len, int SegmentID, int RUID, int UserID)
{
    if (0 == SegmentID && 0 == RUID && 0 == UserID)
    {
        for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)
        {
            for (int j = 0; j < BE_RU_COUNT; j++)
            {
                for (int k = 0; k < MUMIMO_8_USER; k++)
                {
                    UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[i][j][k];
                    dest->SegmentID = i;
                    dest->RUID = j;
                    dest->UserID = k;
                    PayLoadDataCopy(dest, data, len);
                }
            }
        }
    }
    else if (0 == SegmentID && RUID && 0 == UserID)
    {
        for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)
        {
            for (int k = 0; k < MUMIMO_8_USER; k++)
            {
                UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[i][RUID - 1][k];
                dest->SegmentID = i;
                dest->RUID = RUID - 1;
                dest->UserID = k;
                PayLoadDataCopy(dest, data, len);
            }
        }
    }
    else if (0 == SegmentID && 0 == RUID && UserID)
    {
        for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)
        {
            for (int j = 0; j < BE_RU_COUNT; j++)
            {
                UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[i][j][UserID - 1];
                dest->SegmentID = i;
                dest->RUID = j;
                dest->UserID = UserID - 1;
                PayLoadDataCopy(dest, data, len);
            }
        }
    }
    else if (0 == SegmentID && RUID && UserID)
    {
        for (int i = 0; i < attr->PnPSDU->GetMaxSegment(); i++)
        {
            UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[i][RUID - 1][UserID - 1];
            dest->SegmentID = i;
            dest->RUID = RUID - 1;
            dest->UserID = UserID - 1;
            PayLoadDataCopy(dest, data, len);
        }
    }
    if (1 == SegmentID && 0 == RUID && 0 == UserID)
    {
        for (int j = 0; j < BE_RU_COUNT; j++)
        {
            for (int k = 0; k < MUMIMO_8_USER; k++)
            {
                UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[SegmentID - 1][j][k];
                dest->SegmentID = SegmentID - 1;
                dest->RUID = j;
                dest->UserID = k;
                PayLoadDataCopy(dest, data, len);
            }
        }
    }
    else if (1 == SegmentID && RUID && 0 == UserID)
    {
        for (int k = 0; k < MUMIMO_8_USER; k++)
        {
            UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[SegmentID - 1][RUID - 1][k];
            dest->SegmentID = SegmentID - 1;
            dest->RUID = RUID - 1;
            dest->UserID = k;
            PayLoadDataCopy(dest, data, len);
        }
    }
    else if (1 == SegmentID && 0 == RUID && UserID)
    {
        for (int j = 0; j < BE_RU_COUNT; j++)
        {
            UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[SegmentID - 1][j][UserID - 1];
            dest->SegmentID = SegmentID - 1;
            dest->RUID = j;
            dest->UserID = UserID - 1;
            PayLoadDataCopy(dest, data, len);
        }
    }
    else if (SegmentID && RUID && UserID)
    {
        UserDefindPSDU *dest = &attr->PnPSDU->PSDU_User[SegmentID - 1][RUID - 1][UserID - 1];
        dest->SegmentID = SegmentID - 1;
        dest->RUID = RUID - 1;
        dest->UserID = UserID - 1;
        PayLoadDataCopy(dest, data, len);
    }
    return WT_ERR_CODE_OK;
}

scpi_result_t SetWaveGen_PSDUPayloadData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;

    const char *data = nullptr;
    size_t len = 0;
    int32_t numbers[3] = {0, 0, 0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        RUID = numbers[0];
        UserID = numbers[1];
        SegmentID = numbers[2];

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (false)
        {
            bool isAggregation = false;
            if (attr->WaveGenDemod > WT_DEMOD_11N_40M)
            {
                isAggregation = true;
            }
            else
            {
                if (attr->WaveGenDemod == WT_DEMOD_11N_20M ||
                    attr->WaveGenDemod == WT_DEMOD_11N_40M)
                {
                    isAggregation = attr->PnWifi->PN11n.isAggregation;
                }
            }

            if (isAggregation)
            {
                hexdump("MPDU delimter", (const u8 *)data, 4);
                hexdump("MPDU data", (const u8 *)data + 4, len - 4);
            }
            else
            {
                hexdump("MPDU data", (const u8 *)data, len);
            }
        }
        SetPayloadData(attr, (char *)data, len, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

static int FillandCompositeDatawithDelimiterandCRC(SPCIUserParam *attr, const char *SrcData, int SrcDataLen, int SegID, \
        int RuID, int UserID, std::unique_ptr<char[]> &DestData, int &DestDataLen)
{
    WIFI_PSDU *DestPsdu = &attr->PnPSDU->PSDU[SegID][RuID][UserID];
    int MPDUCnt =DestPsdu->MPDUCount;
    int UserMpduTotalLen = 0;
    int FinalDataLen = 0;

    //优化处理，如果没下发WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MPDU，默认配置cnt为1，mpdu长度就为当前下发数据长度
    if(MPDUCnt < 1)
    {
        DestPsdu->MPDUCount = 1;
        MPDUCnt = 1;
        //DestPsdu->MPDULength[0] = DestPsdu->psduLen;
        if(DestPsdu->BaseBandTestFlag == 1)//开启Psdu Test Enable，强制给mpdulength默认值为1024
        {
            DestPsdu->MPDULength[0] = 1024;
        }
        else
        {
            DestPsdu->MPDULength[0] = SrcDataLen;
        }
        
    }

    int AlignSize = 4;  //每个mpdu的长度必须是4倍数，用4来向上取整，最后填充0补齐
    for(int i = 0; i < MPDUCnt; i++)
    {
        UserMpduTotalLen += DestPsdu->MPDULength[i];
        FinalDataLen += ((DestPsdu->MPDULength[i] + AlignSize - 1) / AlignSize * AlignSize);
    }

    if(UserMpduTotalLen != SrcDataLen)
    {
        return WT_ERR_CODE_PARAMETER_MISMATCH;
    }

    FinalDataLen += sizeof(u8) * 4 * 2 * MPDUCnt;//pudu 纯data基础上最多需要加MPDUcnt个（4byte的CRC和4byte的delimiter）
    DestDataLen = 0;
    DestData.reset(new(std::nothrow) char[FinalDataLen]);
    memset((char *)DestData.get(), 0, FinalDataLen);

    //根据协议判断是否要加delimiter
    int Demo = attr->WaveGenDemod;
    GenWaveWifiStruct_API *pnParameters = attr->PnWifi.get();
    bool NeedDelimiter = false;
    switch(Demo)
    {
        case WT_DEMOD_11N_20M:
        case WT_DEMOD_11N_40M:
            {
                if(pnParameters->PN11n.isAggregation) //on
                {
                    NeedDelimiter = true;
                }
            }
            break;
        case WT_DEMOD_11AC_20M:
        case WT_DEMOD_11AC_40M:
        case WT_DEMOD_11AC_80M:
        case WT_DEMOD_11AC_160M:
        case WT_DEMOD_11AC_80_80M:
        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
        case WT_DEMOD_11AX_160M:
        case WT_DEMOD_11AX_80_80M:
        case WT_DEMOD_11AX_160_160M:
        case WT_DEMOD_11BE_20M:
        case WT_DEMOD_11BE_40M:
        case WT_DEMOD_11BE_80M:
        case WT_DEMOD_11BE_160M:
        case WT_DEMOD_11BE_320M:
        case WT_DEMOD_11BE_80_80M:
        case WT_DEMOD_11BE_160_160M:
            {
                NeedDelimiter = true;
            }
            break;
        case WT_DEMOD_11AH_1M:
        case WT_DEMOD_11AH_2M:
        case WT_DEMOD_11AH_4M:
        case WT_DEMOD_11AH_8M:
        case WT_DEMOD_11AH_16M:
        {
            if(pnParameters->commonParam.subType == SUB_1G_LONG_MU_MIMO || pnParameters->PN11ah.Aggregation == 1)//MU或者非mu但开启聚合时
            {
                NeedDelimiter = true;
            }
        }
        default:
            break;
    }

    //先计算crc，然后再算delimiter，因为算delimiter时需要数据长度加上crc 4 byte长度来算;多个mpdu时，每一个mupdu都需要分别加上
    int CurReadSrcLen = 0;
    for(int i = 0; i < MPDUCnt; i++)
    {
        int CurMpduLen = DestPsdu->MPDULength[i];
        u8 Delimiter[4] = {0};
        u8 CRC[4] = {0};

        calculate_FCS(SrcData + CurReadSrcLen, CurMpduLen, CRC);
        //printf("###CRC= 0x%.2x,0x%.2x,0x%.2x,0x%.2x###\n",CRC[0],CRC[1],CRC[2],CRC[3]);

        //delimiter
        if(NeedDelimiter)
        {
            calculate_mpdu_delimiter(CurMpduLen + sizeof(u8)*4, Delimiter); //需要datalen + crclen来计算delimiter
            //printf("###Delimiter= 0x%.2x,0x%.2x,0x%.2x,0x%.2x###",Delimiter[0],Delimiter[1],Delimiter[2],Delimiter[3]);
            memcpy((char *)DestData.get() + DestDataLen, Delimiter, sizeof(Delimiter));
            DestDataLen += sizeof(u8)*4; //add 4 bytes delimiter to the head of the data.
        }

        //data
        memcpy((char *)DestData.get() + DestDataLen, SrcData + CurReadSrcLen, CurMpduLen);
        DestDataLen += CurMpduLen;
        CurReadSrcLen += CurMpduLen;

        //crc
        memcpy((char *)DestData.get() + DestDataLen, CRC, sizeof(CRC));
        DestDataLen += sizeof(u8)*4;     //add 4 bytes CRC to the end of the mpdu data absolutely.

        //每个mpdu长度必须为4的倍数，取4的倍数向上取整，挪出补了零的位置
        int NewSize = (DestPsdu->MPDULength[i] + AlignSize - 1) / AlignSize * AlignSize;
        DestDataLen += (NewSize - DestPsdu->MPDULength[i]);

        //改写每个mpdu的最终长度
        if(NeedDelimiter)
        {
            DestPsdu->MPDULength[i] = NewSize + sizeof(u8) * 4 * 2; //data + delimiter + crc + zero padding
        }
        else
        {
            DestPsdu->MPDULength[i] = NewSize + sizeof(u8) * 4;     //data + crc + zero padding
        }
    }
    //printf("###DestDataLen = %d\n####",DestDataLen);
    return WT_ERR_CODE_OK;
}

//处理不带聚合头和crc的psdu data数据，注意：
//1、11n时，如果开启聚合aggregation，并且psdu type为userdefine，
//必须先下发命令WT:WIFI:SOURce:CONFigure:WAVE:N:AGGregation 1,再下发命令
//WT:WIFI:SOURce:CONFigure:WAVE:PSDU:PAYload:DATA:NO:CRC:AND:DELImiter ，因为有用到agg参数判断(目前genertator工具后下发的aggregation，需要改正)；
//2、务必先下发命令WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MPDU配置mpdu个数和长度,
//再下发数据命令WT:WIFI:SOURce:CONFigure:WAVE:PSDU:PAYload:DATA:NO:CRC:AND:DELImiter
scpi_result_t SetWaveGen_PSDUPayloadDataWithoutCRCandDelimiter(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;

    const char *data = nullptr;
    size_t len = 0;
    int32_t numbers[3] = {0, 0, 0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        RUID = numbers[0];
        UserID = numbers[1];
        SegmentID = numbers[2];

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        std::unique_ptr<char[]> DestData = nullptr;
        int DestDataLen = 0;

        //不带delimiter聚合头和尾部crc的数据，按协议来补加头和crc;所有的数据都要带crc，11ag/b.11n AGG off时不带delimiter，其他都带delimiter（即支持A-MPDU）
        iRet = FillandCompositeDatawithDelimiterandCRC(attr, (char *)data, len, SegmentID, RUID, UserID, DestData, DestDataLen);
        if(WT_ERR_CODE_OK != iRet)
        {
            break;
        }

        SetPayloadData(attr, (char *)(DestData.get()), DestDataLen, SegmentID, RUID, UserID);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

static int check_SIG_data(
    int maxValue,
    int minValue,
    int diff,
    char *data,
    size_t len)
{
    int iRet = WT_ERR_CODE_OK;
    using DataHeader = struct
    {
        int ID;
        unsigned int DataLen;
    };
    DataHeader *pHeader = nullptr;

    int offset = 0;
    while(offset < len)
    {
        pHeader = (DataHeader*)(data + offset);
        if (pHeader->ID > maxValue || pHeader->ID < minValue)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (pHeader->DataLen < 1)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
        pHeader->ID -= diff;

        offset += sizeof(DataHeader);
        offset += pHeader->DataLen;
    }
    return iRet;
}

static scpi_result_t SetWaveGen_UserDefined_SIG_Field(scpi_t *context, int type)
{
    int iRet = WT_ERR_CODE_OK;
    const char *data = nullptr;
    size_t len = 0;
    do
    {
        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (WT_EXT_PARAM_PHY_VHT_SIGB == type)
        {
            iRet = check_SIG_data(4, 1, 1, (char *)data, len);
        }
        else if (WT_EXT_PARAM_PHY_HE_SIGB == type)
        {
            iRet = check_SIG_data(2, 1, 1, (char *)data, len);
        }
        else if (WT_EXT_PARAM_PHY_EHT_SIGB == type)
        {
            iRet = check_SIG_data(8, 1, 1, (char *)data, len);
        }
        else if (WT_EXT_PARAM_PHY_EHT_USIG == type)
        {
            iRet = check_SIG_data(4, 1, 1, (char *)data, len);
        }

        if (iRet)
        {
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        UserDefinedDataClass obj;

        int TotalLen = len;
        int offset = 0;
        ExtParamHdr hdr;
        memset(&hdr, 0, sizeof(hdr));

        hdr.Type = type;
        hdr.Len = TotalLen;

        TotalLen += sizeof(hdr);

        obj.UserDefineData.reset(new char[TotalLen]);
        obj.UserDefineDataLen = TotalLen;

        memcpy(obj.UserDefineData.get(), &hdr, sizeof(hdr));
        offset += sizeof(hdr);
        memcpy(obj.UserDefineData.get() + offset, data, hdr.Len);
        offset += hdr.Len;

        attr->WaveUserDefineExtendParam[hdr.Type] = std::move(obj);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_UserDefined_LSIG(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_LSIG);
}

scpi_result_t SetWaveGen_UserDefined_SIGA(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_SIGA);
}

scpi_result_t SetWaveGen_UserDefined_HT_SIG(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_HTSIG);
}

scpi_result_t SetWaveGen_UserDefined_VHT_SIGB(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_VHT_SIGB);
}

scpi_result_t SetWaveGen_UserDefined_HE_SIGB(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_HE_SIGB);
}

scpi_result_t SetWaveGen_UserDefined_EHT_SIG(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_EHT_SIGB);
}

scpi_result_t SetWaveGen_UserDefined_EHT_USIG(scpi_t *context)
{
    return SetWaveGen_UserDefined_SIG_Field(context, WT_EXT_PARAM_PHY_EHT_USIG);
}

scpi_result_t SetWaveGen_UserDefined_HE_PostFECPadding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;

    const char *data = nullptr;
    size_t len = 0;
    int32_t numbers[3] = {0, 0, 0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        RUID = numbers[0];
        UserID = numbers[1];
        SegmentID = numbers[2];

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        UserDefinedDataClass obj;

        // sizeof(int segmentID + int RUID + int UserID + int userData->DataLen + char n*UserData)
        int TotalLen = 4 * sizeof(int) + len;
        int offset = 0;
        ExtParamHdr hdr;
        memset(&hdr, 0, sizeof(hdr));

        hdr.Type = WT_EXT_PARAM_PHY_HE_POST_FEC_PADDING;
        hdr.Len = TotalLen;

        TotalLen += sizeof(hdr);

        obj.UserDefineData.reset(new char[TotalLen]);
        obj.UserDefineDataLen = TotalLen;

        memcpy(obj.UserDefineData.get(), &hdr, sizeof(hdr));
        offset += sizeof(hdr);

        memcpy(obj.UserDefineData.get() + offset, &SegmentID, sizeof(int));
        offset += sizeof(int);
        memcpy(obj.UserDefineData.get() + offset, &RUID, sizeof(int));
        offset += sizeof(int);
        memcpy(obj.UserDefineData.get() + offset, &UserID, sizeof(int));
        offset += sizeof(int);
        memcpy(obj.UserDefineData.get() + offset, &len, sizeof(int));
        offset += sizeof(int);

        memcpy(obj.UserDefineData.get() + offset, data, len);
        offset += len;

        attr->WaveUserDefineExtendParam[hdr.Type] = std::move(obj);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SetWaveGen_UserDefined_EHT_PostFECPadding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int RUID = 0;
    int UserID = 0;
    int SegmentID = 0;

    const char *data = nullptr;
    size_t len = 0;
    int32_t numbers[3] = {0, 0, 0};
    do
    {
        SCPI_CommandNumbers(context, numbers, 3);
        RUID = numbers[0];
        UserID = numbers[1];
        SegmentID = numbers[2];

        if (!SCPI_ParamArbitraryBlock(context, &data, &len, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        UserDefinedDataClass obj;

        // sizeof(int segmentID + int RUID + int UserID + int userData->DataLen + char n*UserData)
        int TotalLen = 4 * sizeof(int) + len;
        int offset = 0;
        ExtParamHdr hdr;
        memset(&hdr, 0, sizeof(hdr));

        hdr.Type = WT_EXT_PARAM_PHY_EHT_POST_FEC_PADDING;
        hdr.Len = TotalLen;

        TotalLen += sizeof(hdr);

        obj.UserDefineData.reset(new char[TotalLen]);
        obj.UserDefineDataLen = TotalLen;

        memcpy(obj.UserDefineData.get(), &hdr, sizeof(hdr));
        offset += sizeof(hdr);

        memcpy(obj.UserDefineData.get() + offset, &SegmentID, sizeof(int));
        offset += sizeof(int);
        memcpy(obj.UserDefineData.get() + offset, &RUID, sizeof(int));
        offset += sizeof(int);
        memcpy(obj.UserDefineData.get() + offset, &UserID, sizeof(int));
        offset += sizeof(int);
        memcpy(obj.UserDefineData.get() + offset, &len, sizeof(int));
        offset += sizeof(int);

        memcpy(obj.UserDefineData.get() + offset, data, len);
        offset += len;

        attr->WaveUserDefineExtendParam[hdr.Type] = std::move(obj);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}