#ifndef __WT_DEV_BNS_H__
#define __WT_DEV_BNS_H__

#include "devbase.h"
#include "devback.h"
#include "pll.h"
#include "rf.h"
#include "ioctlcmd.h"
#include "wtypes.h"
#include "basefun.h"
#include "busiboard.h"
#include <sys/mman.h>  
#include <mutex>
#include "pll.h"
#include "wtlog.h"
#include <sys/syscall.h>
#include <unistd.h>


//业务板
class DevBusiness : public DevBase
{
public:
    DevBusiness(int BoardType, int HwVersion, Json::Value &ConfJson, int AttConfigMode)
        : DevBase(BoardType, HwVersion, ConfJson), m_AttConfigMode(AttConfigMode)
    {
        int Ret = posix_memalign((void **)&m_MapDmaBuf, 4096 /*alignment */, DMA_BUF_SIZE + 4096);
        if (Ret != 0)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "====================== posix_memalign m_MapDmaBuf error\n");
            m_MapDmaBuf = NULL;
        }

        Ret = posix_memalign((void **)&m_TrigParamDmaBuf, 4096 /*alignment */, (SEGMENT_ITEM_MAX) * (TRIG_PARAM_SIZE));
        if (Ret != 0)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "====================== posix_memalign m_HwParamDmaBuf error\n");
            m_TrigParamDmaBuf = NULL;
        }
        m_TrigParamDmaDataSize = (SEGMENT_ITEM_MAX) * (TRIG_PARAM_SIZE);

        Ret = posix_memalign((void **)&m_TempHwParamDmaBuf, 4096 /*alignment */, HW_PARAM_SIZE);
        if (Ret != 0)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "====================== posix_memalign m_HwParamDmaBuf error\n");
            m_TempHwParamDmaBuf = NULL;
        }
        m_TempHwParamDmaDataSize = HW_PARAM_SIZE;
    }

    virtual ~DevBusiness()
    {
        if (m_MapDmaBuf != NULL)
        {
            free(m_MapDmaBuf);
        }
        if (m_TrigParamDmaBuf != NULL)
        {
            free(m_TrigParamDmaBuf);
        }
        if (m_TempHwParamDmaBuf != NULL)
        {
            free(m_TempHwParamDmaBuf);
        }
        if (m_HwParamDmaBuf != NULL)
        {
            munmap(m_HwParamDmaBuf, m_HwParamDmaDataSize);
        }

        if (m_FpgaFd > 0)
        {
            close(m_FpgaFd);
        }
    }

    virtual int InitForManager();

	virtual int Init(DevBack *BackBoard);

	virtual int SetRFPowerStatus(WT_SWITCH_STATUS Status) = 0;

    virtual int SetBand(double ModFreq, double MixFreq, WT_RF_MOD_BAND_E BandMod, WT_RF_MIX_BAND_E BandMIX) = 0;

	virtual int Stop() = 0;

    virtual int Start(int Mode = WT_START_MODE_NORMAL) = 0;

    virtual int GetStatus(void) = 0;

    virtual int Down(void) = 0;

	virtual int Release();

    virtual int SaveData(int Index) = 0;

    virtual int SetFreq(double Freq, Freq_Parm *FreqParm, WT_DEVICE_MODE WorkMode) = 0;

    const std::vector<VoltInfoType> &GetRFVoltInfo() { return m_RFVoltInfoVec; }

    BusiBoardFpgaInfo GetFpgaInfo() const { return m_FpgaInfo; }

    BusiBoardHwInfo GetHwInfo() const { return m_HwInfo; }

    int GetBusiFPGAVersion() { return m_FpgaInfo.FpgaVersion; }

    int GetRfExist() { return m_IsRfExist; }

    int GetLoExist() { return m_IsLoExist; }

    int GetRfHwVersion() { return m_HwInfo.RfHwVersion; }

    int GetLoHwVersion() { return m_HwInfo.LoHwVersion; }

    int BusiStart();

    int BusiStop();

    int BusiDown();

    int GetCompleteClrStatus(int &Status);

    int SetUnitModWorkMode(WT_DEVICE_MODE WorkMode);

	int SetIQSwitch(int IQSwitch);

    int WriteAD5611(int DevId, int Data);

    int ReadAD5611(int DevId, int &Data);

    int WriteAD7682(int Addr, int Data);

    int ReadAD7682(int Addr, int &Data);

    int GetAD7682ChannelCode(int Channel, int &VoltValue);

    int GetAD7682ChannelVolt(int Channel, double &VoltValue);

    int GetAD7682ChannelTemperature(int Channel, double &Data);

    int GetAD7689ChannelCode(int Channel, int &VoltValue);

    int GetAD7689ChannelVolt(int Channel, double &VoltValue);

    int GetRFAllVoltValue();

    int WriteADF4106(int Addr, int Data);

    int ReadADF4106(int Addr, int &Data);

    int WriteHM7044(int Addr, int Data);

    int ReadHM7044(int Addr, int &Data);

    int WriteLTC5594(int Addr, int Data);

    int ReadLTC5594(int Addr, int &Data);

    int WriteLMX2594(int Addr, int Data);

    int ReadLMX2594(int Addr, int &Data);

    int WriteLMX2820(LO_ID_E LOId, int Addr, int Data);

    int ReadLMX2820(LO_ID_E LOId, int Addr, int &Data);

    int WriteFLASH(int Addr, int Data);

    int ReadFLASH(int Addr, int &Data);

    //AD7091
    int WriteAD7091(int Addr, int Data);

    int ReadAD7091(int Addr, int &Data);

    int GetBBVoltValue(int Channel, double &VoltValue);

    int GetBBAllVoltValue();

    // LO DDS
    int WriteDDS(int Addr, int Data);

    int ReadDDS(int Addr, int &Data);

    int SetDDSChannel(unsigned long long Data);

    int SetDDSFreq(double Freq /*MHz*/);

    int SetDDSFsCurrent(unsigned int DacFsCurrent);

    //rf Shift
    int WriteATTAndShift(int ChipId, int Addr, int Data);

    int ReadATTAndShift(int ChipId, int Addr, int &Data);

    //lo shift
    int WriteLoShift(long long Data);

    int ReadLoShift(long long &Data);

    int SetLoHMC705(int Data);

    int SetLoLoopFilter(int Data);

    int SetLoFreqChannel(int Data);

    // 射频板移位寄存器
    int GetRfShiftReg(int RegId, int &Data);
    int SetRfShiftReg(int RegId, int Data);

    int WriteAdcDac(int Addr, int Data);

    int ReadAdcDac(int Addr, int &Data);

    //LM74
    int GetRFTemperatureAverage(double &TempAverage);

	int GetRFTemperature(int TempId, double &TempValue);

    //读取同一业务板上的两块射频板的平均温度
    int GetRFAvgTemperature(double &TempValue);

	int SetRFPort(int RFPort, int Mode, int State, WT_SB_CONFIG_TYPE_E SBConfigType);

    // 本振调频率, 随频率修改滤波器开关，校准调本振用
    int SetRfModLoSwitch(double ModLoFreq/*MHz*/);

    int SetMixLoSwitch(double Freq /*MHz*/);

    int SetModFreqPower(double Freq, int PowerLevel, int IQSwap = false);

    int SetMixFreqPower(double Freq, int PowerLevel);

    int SetLoBoardFreqPower(double Freq, int PowerLevel);

    int SetLoBoardFreqPower_VB(double Freq, int PowerLevel);

    int SetLMX2594FreqPower(int Freq, int PowerLevel);

    int SetLMX2820FreqPower(LO_ID_E LOId, double Freq, int PowerLevel);

    int SetHMC833FreqPower(double Freq, int PowerLevel);

    int SetHMC833FreqAttr(const LOFreqAttr &FreqAttr, int SleepFlag);

    int WriteHMC833VCOReg(int Addr, int Data);

   	int SetATTCode(int DevId, int Code);

    int GetATTCode(int DevId, int &Code);

	int WriteATT(int AttId, int Code);
 
    int ReadATT(int AttId, int &Code);

    int SetBusiBoardFreq(int Freq /*(KHz)*/);

    int CheckLOStatus(double Freq);
    
	int CheckLOIsLock(LO_ID_E LOId);
    
	int ResetLO(LO_ID_E LOId);

    int PowerDownLO(LO_ID_E LOId);

    int ShowRFInfo();

    int ClearWorkMode();

    int GetXdmaStatus(int &Status);

    int BaseFpgaUpgrade(unsigned char *pData, unsigned int Len, int NotProgram);

    int ResetFpga();

    int SetAttConfigMode(int ConfigMode);

    int GetAttConfigMode(int &ConfigMode);

    virtual int SetDebugAtt(int Index, int Data) = 0;

    virtual int GetDebugAtt(int Index, int &Data) = 0;

    int SetModLoRefSel(int Data);

    int GetModLoRefSel(int &Data);

    int SetATTCalConfig(ATTCalConfigType &Config);

    //共本振模式
    int SetLOComMode(int Data);

    int GetLOComMode(int &Data);

    int ReSetLoMode();

    int CheckIQOffset(int Data);

    int LdpcCompute(LdpcParamType *LdpcParam, int Count);

    int WriteHMC833Reg(int Addr, int Data);

    int BccCompute(const BccParamType &BccParam);

    int ReadHMC833Reg(int Addr, int &Data);

    //ListMode 开关ON/OFF
    int SetListModeStatus(int Status);
    //ListMode 初始化listmode寄存器列表。
    int InitVirtualAddr();
    //ListMode 下发SequenceSegment的时间参数到FPGA。
    int SetSeqSegTime(SeqTimeType SeqTimeParam, double SamplingRate);
    //ListMode 记录SequenceSegment的硬件配置列表到驱动。
    int RecordlistSegConfig(int Port);
    //ListMode 下发SequenceSegment的硬件配置列表到FPGA。
    int SetlistSegConfig(int SegmentIndex, int SegmentPort);
    //通过pcie 寄存配置trig公共参数
    int SetTrigComParam(CommonTrigType &TrigComParam);
    int SetTrigLoopParam(int Loop);
    //ListMode 下发SequenceSegment的同步开关到FPGA。
    int WriteDmaData(int Channel, char* Buf, int Len);
    void SetHwParamDmaBuf(void *Buf, int Len) { m_HwParamDmaBuf = (char *)Buf; m_HwParamDmaDataSize = Len;}
    int ResetDmaFIFO();
protected:
	virtual int InitBaseBandMod() = 0;

    //RF AD7091
    int BusiAD7091Init();

    int RFVoltInfoInit();

    int LoModInit();

    int LoBoardInit();

    int LoBoardInit_VB();

    int LoMixInit();

    int LMX2594Init(std::string InitMap = "LMX2594_Init");

    int HMC833Init();

    int LMX2820Init(LO_ID_E LOId);

    int DDSInit();

	int LOConfigSave(int LOId, int ActiveStatus, int BandMode, double Freq, int PowerLevel);

    //共本振模式
    int LOComModeInit();
    int GetDefaultLoMode();

    bool IsUseLo2(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_USE_MIX) >= 0);
    }

    //*****************************************************************************
    //判断频率是否属于6G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is6G(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_6G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_6G_MAX) <= 0) ? true : false;
    }

    //*****************************************************************************
    //判断频率是否属于5G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is5G(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_5G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_5G_MAX) <= 0) ? true : false;
    }

    //*****************************************************************************
    //判断频率是否属于4G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is4G(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_4G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_4G_MAX) < 0) ? true : false;
    }

    //*****************************************************************************
    //判断频率是否属于3G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is3G(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_3G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_3G_MAX) < 0) ? true : false;
    }

    //*****************************************************************************
    //判断频率是否属于2.4G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is2G4(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_2_4G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq , RF_2_4G_MAX) <= 0) ? true : false;
    }

    //*****************************************************************************
    //判断频率是否属于2G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is2G(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_2G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_2G_MAX) < 0) ? true : false;
    }

    //*****************************************************************************
    //判断频率是否属于1G频段
    //参数[IN] : Freq：频率
    //返回值: bool值
    //*****************************************************************************
    bool Is1G(double Freq)
    {
        return (Basefun::CompareDoubleAccuracy1K(Freq, RF_1G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_1G_MAX) < 0) ? true : false;
    }

    int GetModFreqChannel(double Freq, double &Div, int &Index);

    int GetChDiv(double Freq, int &Div, int &Index, int LoType);

    //共本振模式
    int SetLOComModeToDevice(int Data);

    int GetLOComModeFromDevice(int &Data);

    int GetFPGAResampleIndex(const double SampleRate);

    void SaveDmaData(int Channel, int ChannelType,void *Buf, int Len);
private:
	int InitBoard();

	int InitBaseBand();

    int InitLO();

	int InitRF();

    int BusiInfoInit();

    int ADF4106Init();

    int HMC7044Init();

	bool CheckRfExist();

	int RFTemperatureInit();

    int CheckLMX2594(int Addr, int Data);
    int CheckHMC833Reg(int Addr, int Data);
protected:
    //当前模块运行时的状态数据结构体
    struct FreqSection
    {
        int StartFreq;
        int EndFreq;
        int Index;
    };

    enum XDMA_STATUS_TYPE
    {
        WT_XDMA_INIT = 0,
        WT_XDMA_WR_FINISH,
    };


    enum WT_IQ_Mode
    {
        RFIQ_MODE,
        ANALOGIQ_MODE,
    };

    //当前模块运行时的状态数据结构体
    struct ModRunData
    {
        WT_RX_TX_STATUS Status = WT_RX_TX_STATUS_STOP;          // 当前链路运行状态
        WT_RX_TX_STATUS Status2 = WT_RX_TX_STATUS_STOP;         // 当前链路80+80运行状态,校准时用
        WT_DEVICE_MODE  CurrentWorkMode = DEVICE_MODE_SISO;     //当前链路工作模式
        WT_RF_MOD_BAND_E CurrentBandMod = WT_RF_MOD_BAND_COUNT; //当前链路波段类型
        WT_RF_MIX_BAND_E CurrentBandMix = WT_RF_MIX_BAND_COUNT; //当前链路波段类型
        int RFPort = WT_RF_OFF;                                 // 当前链路使用的RF端口号
        int RFPortMode = 0;                                     //当前端口模式
        int RFPortState = WT_RF_STATE_INIT;                     //当前端口的状态
        int RFBroadcastPortState[WT_RF_MAX] = {WT_RF_STATE_INIT}; //当前端口的状态
        double Freq = 0.0;                                      // 当前链路的频率
        double Power = 0.0;                                     // 当前VSG发送功率
        double SamplingFreq = 0.0;                              // 当前链路基带板采样频率
        TemperatureType RFTemp;                                 //当前链路射频板采样温度值
        LOCurrentStatus LOCurData[LO_NUM];                      //当前链路本振运行时参数
        int DcOffsetI = CODE_NO_INIT;                           //当前链路DC_OFFSET_I
        int DcOffsetQ = CODE_NO_INIT;                           //当前链路DC_OFFSET_Q
        int ATTCurData[ATT_MAX];                                //当前链路ATT运行时参数
        int PaStatus = -1;                                      //当前链路PA状态
        int IfgStatus = 0;                                      //当前VSG链路IFG状态
        int TrigType = 0;                                       //当前链路触发类型
        int XdmaStatus = 0;                                     //当前链路XDMA状态
        int ModLoRefSel = REF_100M;                             //调频参考时钟选择
        int ModMashOrder = MASH_ORDER_THIRD;                    //小数分频模式时的MASH order
        int MixMashOrder = MASH_ORDER_THIRD;                    //小数分频模式时的MASH order
        ModRunData()
        {
            LOCurData[LoMod].Activated = false;
            LOCurData[LoMod].BandMode = 0;
            LOCurData[LoMod].PowerLevel = 0;
            LOCurData[LoMod].Freq = 2000.0;
            LOCurData[LoMix].Activated = false;
            LOCurData[LoMix].BandMode = 0;
            LOCurData[LoMix].PowerLevel = 0;
            LOCurData[LoMix].Freq = 0.0;
        }
    };

    int m_Slot = 0;
    BusiBoardFpgaInfo m_FpgaInfo;
    BusiBoardHwInfo m_HwInfo;
    bool m_IsRfExist = false;                   //射频板是否存在
    bool m_IsLoExist = false;                   //本振板是否存在
    int m_LOComMode = LO_IS_NOT_COM_MODE;       //共本振模式 0:为共本振模式，1为非共本振模式
    int m_ShareLOBoardType = DEV_TYPE_VSG;
    int m_AnalogIQSW = RFIQ_MODE;                  //模拟IQ信号内/外链路切换开关
    std::vector<VoltInfoType> m_RFVoltInfoVec;  //射频板电压
    std::vector<int> m_DDSAddrTypeM;            //DDS TYPE M类型的寄存器
    std::vector<FreqSection> m_HMC705RatioN;    //HMC705 Div RatioN分段
    std::vector<FreqSection> m_LoopFilter;      //环路滤波器频率分段
    std::vector<FreqSection> m_ModFreqChannel;  //调频频率分段 VB
    std::vector<FreqSection> m_RfModLoFreqChannel;  // 射频板调制本振处分段
    std::vector<FreqSection> m_RfMixLoFreqChannel;  // 射频板混频本振处分段
    std::vector<FreqSection> m_DDSRefFreqIndex; //
    ModRunData m_RunData;                       //运行状态数据
    DevBack *m_BackBoard;
    char *m_MapDmaBuf = nullptr;                //DMA地址
    char *m_HwParamDmaBuf = nullptr;            //DMA地址
    char *m_TrigParamDmaBuf = nullptr;          //DMA地址
    char *m_TempHwParamDmaBuf = nullptr;        //DMA地址
    int m_DmaDataOffset = 0;
    int m_DmaDataSize = 0;
    int m_HwParamDmaDataOffset = 0;
    int m_HwParamDmaDataSize = 0;
    int m_TrigParamDmaDataOffset = 0;
    int m_TrigParamDmaDataSize = 0;
    int m_TempHwParamDmaDataOffset = 0;
    int m_TempHwParamDmaDataSize = 0;
    int m_AttConfigMode;
    int m_IQSwitch = 0;
    int m_ListMode = 0;
    int m_SaveDmaDataFlag = 0;
    int m_ListVirtualCfgMode = 0;
    int m_ListModeNeedReset = 0;
    std::mutex m_HwDecodeMutex;                    //硬解解码的锁
    std::mutex m_SetFreqMutex;                     //配置频率锁
    int m_FpgaFd = -1;
};

#endif
