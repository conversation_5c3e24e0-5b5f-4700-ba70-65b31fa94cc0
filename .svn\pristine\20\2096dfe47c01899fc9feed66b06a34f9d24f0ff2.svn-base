//*****************************************************************************
//File: wtbusilib.c
//Describe:业务板硬件驱动处理层
//Author：yuanyongchun
//Date: 2021.01.18
//*****************************************************************************
#include <linux/kernel.h>
#include <linux/uaccess.h>
#include <linux/delay.h>
#include <linux/time.h>

#include "wtdefine.h"
#include "hwlib.h"
#include "wtbusidefine.h"
#include "wtbusilib.h"
#include "wtvsadefine.h"
#include "wtvsgdefine.h"
#include "rfswitch.h"
#include "wtswitch.h"

#include "../general/devlib/busiboard.h"
#include "../general/wterror.h"
#include "../general/devlib/ioctlcmd.h"
#include "../general/devlib/pll.h"
#include "../general/devlib/fpgadefine.h"

#define BUSILIB_DEBUG 1
int cur_wlan_mode[BUSI_BOARD_TYPE_COUNT][MAX_UNIT_NUM] = {{WT_WORKMODE_SISO}};
int g_DCOffsetReg[MAX_UNIT_NUM][(QDAC_DC_OFFSET1 - IDAC_DC_OFFSET0 + 1)] = {{0}};
extern struct RF_ATT_T RfATTCtrlData[BUSI_UB_TYPE_COUNT][RF_ATT_MAX];

//读取业务板FPGA基本信息
int WT_GetBusiFpgaInfo(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct BusiBoardFpgaInfo FPGAInfo;

    //读取单元板FPGA信息
    FPGAInfo.TesterType = pdev->testertype;
    FPGAInfo.FpgaVersion = wt_read_direct_reg(pdev, BUSI_FPGA_VERSION);
    FPGAInfo.CompileDate = wt_read_direct_reg(pdev, BUSI_FPGA_COMPILE_DATE);
    FPGAInfo.CompileTime = wt_read_direct_reg(pdev, BUSI_FPGA_COMPILE_TIME);
    FPGAInfo.ModuleVersion = wt_read_direct_reg(pdev, BUSI_FPGA_MODULE_VERSION);
    FPGAInfo.PllLockDet = wt_read_direct_reg(pdev, BUSI_FPGA_PLL_LOCK);
    FPGAInfo.LoLockDet = wt_read_direct_reg(pdev, BUSI_FPGA_LO_LOCK_DET);

    if (copy_to_user(arg, &FPGAInfo, DataLength))
    {
        dbg("WT_GetBusiFpgaInfo info copy_to_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return WT_OK;
}

//获取业务板槽位号
int WT_GetBusiBoardSlot(int DataLength, void *arg, struct dev_unit *pdev)
{
    if (copy_to_user(arg, &pdev->slot, DataLength))
    {
        dbg("WT_GetBusiBoardSlot info copy_from_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

//读取业务板完成状态并清零
int WT_ReadCompleteClrStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Data = WT_RX_TX_STATUS_RUNNING;
    if (pdev->complete >= 0)
    {
        Data = 0x1u << 31;
        /*if (pdev->type == DEV_TYPE_VSG)
        {
            //获取VSG状态status信息
            status = wt_read_direct_reg(pdev, VSG_STATUS);
            status = status & 0xFF;
            // printk("Read VSG_STATUS=%#x, status=%#x\n", VSG_STATUS, status);
            if (status) //非0：未完成
            {
                status = WT_RX_TX_STATUS_RUNNING;
            }
            else //0：完成
            {
                status = WT_RX_TX_STATUS_DONE;
            }
        }
        else
        {
            //获取VSA状态status信息
            status = wt_read_direct_reg(pdev, VSA_STATUS);
            // printk("Read VSA_STATUS=%#x, status=%#x\n", VSA_STATUS, status);
            if (GetBit(status, 5) && GetBit(status, 6))
            {
                status = WT_RX_TX_STATUS_DONE;
            }
            else
            {
                status = WT_RX_TX_STATUS_RUNNING;
            }
        }*/
        Data |= pdev->complete;
    }

    pdev->complete = -1;
    dbg_print("WT_ReadCompleteClrStatus info 0x%08x \n", Data);
    if (copy_to_user(arg, &Data, DataLength))
    {
        dbg("WT_ReadCompleteClrStatus info copy_from_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_GetCompleteCnt(int DataLength, void *arg, struct dev_unit *pdev)
{
    int cnt = atomic_xchg(&pdev->vsadonecnt, 0);

    dbg("WT_GetCompleteCnt %d\n", pdev->vsadonecnt);

    if (copy_to_user(arg, &cnt, DataLength))
    {
        dbg("WT_GetCompleteCnt info copy_from_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    /*if (pdev->vsadonecnt > 0) {
        pdev->vsadonecnt--;
    }*/

    return WT_OK;
}

int WT_GetVsaSeqCompleteStat(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;

    RegData = wt_read_direct_reg(pdev, VSA_SEQ_STATE);

    dbg("WT_GetVsaSeqCompleteStat %#x\n", RegData);

    if (copy_to_user(arg, &RegData, DataLength))
    {
        dbg("WT_GetCompleteCnt info copy_from_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

void WT_SetWlanMode(struct dev_unit *pdev)
{
    int i;
    enum WT_WORKMODE_E wlan_mode = WT_WORKMODE_SISO;

    spin_lock(pBPdev->lock);
#if BUSILIB_DEBUG
    dbg_print("set wlan_mode to %d, cur cur_wlan_mode[%d][%d] = %d\n", pdev->wlan_mode, pdev->type, pdev->number, cur_wlan_mode[pdev->type][pdev->number]);
#endif
    if (pdev->wlan_mode == WT_WORKMODE_MASTER)
    {
        for (i = 0; i < MAX_UNIT_NUM; i++)
        {
            if (cur_wlan_mode[pdev->type][i] == WT_WORKMODE_MASTER)
            {
                cur_wlan_mode[pdev->type][i] = WT_WORKMODE_SISO; //只有一个VSG/VSA单元可以设置为主机
            }
        }
    }
    cur_wlan_mode[pdev->type][pdev->number] = pdev->wlan_mode; //缓存背板工作模式

    //是否有槽位为MASTER
    for (i = 0; i < MAX_UNIT_NUM; i++)
    {
        if (cur_wlan_mode[pdev->type][i] == WT_WORKMODE_MASTER)
        {
            wlan_mode = WT_WORKMODE_MASTER;
            break;
        }
    }

    if (wlan_mode != WT_WORKMODE_MASTER)
    {
        wt_write_direct_reg(pBPdev, pdev->type == DEV_TYPE_VSA ? BACK_VSA_MASTER_SLOT : BACK_VSG_MASTER_SLOT, SISO_MODE);
    }
    else if (pdev->wlan_mode == WT_WORKMODE_MASTER)
    {
        wt_write_direct_reg(pBPdev,
                            pdev->type == DEV_TYPE_VSA ? BACK_VSA_MASTER_SLOT : BACK_VSG_MASTER_SLOT,
                            pdev->slot + MIMO_MASTER_SLOT0);
    }
    spin_unlock(pBPdev->lock);
}

//设置链路工作模式（主从模式/SISO/MIMO）
int WT_SetUnitModWorkMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    enum WT_DEVICE_MODE WorkMode;

    if (copy_from_user(&WorkMode, arg, DataLength))
    {
        dbg("WT_SetDeviceWorkMode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    dbg_print("set Board%d  ModId%d  WorkMode to %d,\n", pdev->type, pdev->number, WorkMode);

    //设置仪器的工作模式（SISO/MIMO）
    //VSA的WLAN_MODE暂不配置，待配置触发模式时一起配置
    switch (WorkMode)
    {
    case DEVICE_MODE_SISO:
    case DEVICE_MODE_DUL_RF_PARAM_AGC_MASTER:
    case DEVICE_MODE_DUL_RF_PARAM_AGC_SLAVE:
        if (pdev->type == DEV_TYPE_VSG)
        {
            wt_write_direct_reg(pdev, VSG_WLAN_MODE, VSG_WORKMODE_SISO);
        }
        pdev->wlan_mode = WT_WORKMODE_SISO;
        break;

    case DEVICE_MODE_80_80_MASTER:
    case DEVICE_MODE_MIMO_MULTI_MASTER:
    case DEVICE_MODE_MIMO_SINGLE_MASTER:
    case DEVICE_MODE_MIMO_MULTI_MASTER_8080_MASTER:
        if (pdev->type == DEV_TYPE_VSG)
        {
            wt_write_direct_reg(pdev, VSG_WLAN_MODE, VSG_WORKMODE_MIMO_MASTER);
        }
        pdev->wlan_mode = WT_WORKMODE_MASTER;
        break;

    case DEVICE_MODE_80_80_SLAVE:
    case DEVICE_MODE_MIMO_MULTI_SLAVE:
    case DEVICE_MODE_MIMO_MULTI_SLAVE_8080_MASTER:
    case DEVICE_MODE_MIMO_MULTI_SLAVE_8080_SLAVE:
    case DEVICE_MODE_MIMO_SINGLE_SLAVE:
    case DEVICE_MODE_MIMO_MULTI_MASTER_8080_SLAVE:
        if (pdev->type == DEV_TYPE_VSG)
        {
            wt_write_direct_reg(pdev, VSG_WLAN_MODE, VSG_WORKMODE_MIMO_SLAVE);
        }
        pdev->wlan_mode = WT_WORKMODE_SLAVE;
        break;
    default:
        dbg("WT_SetDeviceWorkMode WorkMode error!\n");
        return WT_ARG_ERROR;
    }
    WT_SetWlanMode(pdev);
    return WT_OK;
}

int WT_ClearWorkMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    //printk("cur_wlan_mode Board%d  ModId%d\n", pdev->type, pdev->number);
    cur_wlan_mode[pdev->type][pdev->number] = WT_WORKMODE_SISO;
    return WT_OK;
}

int WT_WriteBusiOCXOCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    int SPIConfig = 0x5810;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    int SpiBaseAddr = 0;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteBusiOCXOCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if(pdev->testertype == HW_WT418)
    {
        SPIConfig = 0x1110;    //SPI配置参数
        SpiBaseAddr = RegTypeTemp.Addr == 0 ? BUSI_VB_AD5611_1_SPI : BUSI_AD5611_3_SPI;
        if(RegTypeTemp.Addr == BUSI_AD5611_3_SPI)
        {
            wt_write_direct_reg(pdev, (0x2e << 2), 3);
        }
    }
    else
    {
        SpiBaseAddr = RegTypeTemp.Addr == 0 ? BUSI_VB_AD5611_1_SPI : BUSI_VB_AD5611_2_SPI;
    }

    Reg[SPI_REG_TX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR2;

    //数据的低10位有效
    TXData = (RegTypeTemp.Data & 0x3ff) << 4;

    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

//背板初始化的操作移交给业务板的AD5611，此函数只用于兼容背板初始化OCXO的操作
int WT_ReadBusiOCXOCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    int SpiBaseAddr = 0;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadBusiOCXOCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->testertype == HW_WT418)
    {
        SpiBaseAddr = RegTypeTemp.Addr == 0 ? BUSI_VB_AD5611_1_SPI : BUSI_AD5611_3_SPI;
    }
    else
    {
        SpiBaseAddr = RegTypeTemp.Addr == 0 ? BUSI_VB_AD5611_1_SPI : BUSI_VB_AD5611_2_SPI;
    }

    Reg[SPI_REG_TX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR2;

    //回读code值
    RegTypeTemp.Data = wt_read_direct_reg(pdev, Reg[SPI_REG_RX_TYPE]);
    RegTypeTemp.Data = RegTypeTemp.Data >> 4;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadBusiOCXOCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//AD7682
int WT_ReadBusiAD7682Channel(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int ChannelSel = 0;         //通道选择
    int ChannelExtSel = 0;      //额外通道选择
    unsigned int Reg[4] = {0};  //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadBusiAD7682Channel info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]        = BUSI_RX_AD7682_CH_SEL;
        Reg[SPI_REG_RX_TYPE]        = BUSI_RX_AD7682_RX;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_RX_AD7682_CTRL1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_RX_AD7682_CH_EXT_SEL;

        if(RegTypeTemp.Addr < BUSI_RX_AD7682_MAX)
        {
            ChannelExtSel = 0;
            ChannelSel = RegTypeTemp.Addr;
        }
        else if (RegTypeTemp.Addr < BUSI_RX_AD7682_EXT_MAX)
        {
            ChannelExtSel = RegTypeTemp.Addr - BUSI_RX_AD7682_MAX;
            if (pdev->version >= VERSION_B)
            {
                ChannelSel = BUSI_VB_RX_AD7682_SEL_EXT;
            }
            else
            {
                ChannelSel = BUSI_RX_AD7682_SEL_EXT;
            }
        }
        else
        {
            dbg("ReadBusiAD7682 Channel=%d arg error!\n", RegTypeTemp.Addr);
            return WT_ARG_ERROR;
        }
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]        = BUSI_TX_AD7682_CH_SEL;
        Reg[SPI_REG_RX_TYPE]        = BUSI_TX_AD7682_RX;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_TX_AD7682_CTRL1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_TX_AD7682_CH_EXT_SEL;

        if(RegTypeTemp.Addr < BUSI_TX_AD7682_MAX)
        {
            ChannelExtSel = 0;
            ChannelSel = RegTypeTemp.Addr;
        }
        else if(RegTypeTemp.Addr < BUSI_TX_AD7682_EXT_MAX)
        {
            ChannelExtSel = RegTypeTemp.Addr - BUSI_TX_AD7682_MAX;
            if (pdev->version >= VERSION_B)
            {
                ChannelSel = BUSI_VB_TX_AD7682_SEL_EXT;
            }
            else
            {
                ChannelSel = BUSI_TX_AD7682_SEL_EXT;
            }
        }
        else
        {
            dbg("ReadBusiAD7682 Channel=%d arg error!\n", RegTypeTemp.Addr);
            return WT_ARG_ERROR;
        }
    }

    //读取7682通道
    ret = wt_bp_spi_direct_for_read(ChannelExtSel, ChannelSel, &RegTypeTemp.Data, Reg, pdev);

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("WT_ReadBusiAD7682Channel info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_ReadSwAD7689Channel(int DataLength, void *arg, struct dev_unit *pdev)
{
    {
        int ret = WT_OK;
        struct RegType RegTypeTemp;
        int ChannelSel = 0;         //通道选择
        unsigned int Reg[4] = {0};  //器件所映射的寄存器地址
        if (copy_from_user(&RegTypeTemp, arg, DataLength))
        {
            dbg("WT_ReadSwAD7689Channel info copy_from_user failed!\n");
            return WT_CPY_FROM_USR_FAILED;
        }
        // 没有SPI控制配置
        Reg[SPI_REG_TX_TYPE]        = BUSI_SW_AD7689_CH_SEL;
        Reg[SPI_REG_RX_TYPE]        = BUSI_SW_AD7689_RX;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_SW_AD7689_CTRL1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_SW_AD7689_CH_SEL;

        ChannelSel = RegTypeTemp.Addr;
        //读取7682通道
        ret = wt_bp_spi_direct_for_read(ChannelSel, ChannelSel, &RegTypeTemp.Data, Reg, pdev);

        if (copy_to_user(arg, &RegTypeTemp, DataLength))
        {
            dbg("WT_ReadSwAD7689Channel info copy_to_user failed!\n");
            return WT_CPY_TO_USR_FAILED;
        }
        return WT_OK;
    }
}

//ADF4106
int WT_WriteADF4106Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1C98;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ADF4106SetCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SPI_REG_TX_TYPE]    = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_CR2;

    // //数据的低10位有效
    // TXData = (RegTypeTemp.Data & 0xffffff) << 2 + (RegTypeTemp.Addr & 0x3);
    TXData = RegTypeTemp.Data;
    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadADF4106Code(int DataLength, void *arg, struct dev_unit *pdev)
{
#if 0   
    // int ret = WT_OK;
    // struct RegType RegTypeTemp;
    // int SPIConfig = 0x1C98;    //SPI配置参数
    // int TXData = 0;            //下发TX寄存器的数据
    // unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    // if (copy_from_user(&RegTypeTemp, arg, DataLength))
    // {
    //     dbg("WT_ReadADF4106Code info copy_from_user failed!\n");
    //     return WT_CPY_FROM_USR_FAILED;
    // }

    // Reg[SPI_REG_TX_TYPE]    = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_TX;
    // Reg[SPI_REG_RX_TYPE]    = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_RX;
    // Reg[SPI_REG_CTRL1_TYPE] = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_CR1;
    // Reg[SPI_REG_CTRL2_TYPE] = BUSI_ADF4106_SPI + BUSI_DEVM_SPI_CR2;

    // // //数据的低10位有效
    // // TXData = RegTypeTemp.Addr & 0x3;

    // //设置晶振的code值
    // ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);
    // retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    // RegTypeTemp.Data = RegTypeTemp.Data >> 2;

    // if (copy_to_user(arg, &RegTypeTemp, DataLength))
    // {
    //     dbg("wt_ReadADF4106Code info copy_to_user failed!\n");
    //     return WT_CPY_TO_USR_FAILED;
    // }
    // return WT_OK;
#else
    dbg("WT_ReadRefPllAdf4002 not support!\n");
    return WT_DEVICE_NOT_SUPPORT;
#endif
}

//HM7044
int WT_WriteHM7044Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if(pdev->testertype == HW_WT418)
    {
        SPIConfig = 0x1818;    //SPI配置参数
    }
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_HM7044SetCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SPI_REG_TX_TYPE]    = BUSI_HM7044_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_HM7044_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_HM7044_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_HM7044_SPI + BUSI_DEVM_SPI_CR2;

    //bit0_7:data, bit8_20:addr, bit23:0w,1r
    TXData = ((RegTypeTemp.Addr & 0x1fff) << 8) + (RegTypeTemp.Data & 0xff);
    
    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadHM7044Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if(pdev->testertype == HW_WT418)
    {
        SPIConfig = 0x1818;    //SPI配置参数
    }
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadHM7044Code info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SPI_REG_TX_TYPE]    = BUSI_HM7044_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_HM7044_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_HM7044_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_HM7044_SPI + BUSI_DEVM_SPI_CR2;

    //bit0_7:data, bit8_20:addr, bit23:0w,1r
    TXData = (0x1u << 23) + ((RegTypeTemp.Addr & 0x1fff) << 8);

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    RegTypeTemp.Data = RegTypeTemp.Data & 0xff;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadHM7044Code info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//LTC5594
int WT_WriteLTC5594Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    int ChipData = 0;
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_LTC5594SetCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SPI_REG_TX_TYPE]    = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_CR2;

    // 先读芯片数据
    TXData = (0x1u << 23) + ((RegTypeTemp.Addr & 0x7f) << 16);
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &ChipData, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    //bit0_7: data1 bit8_15:data0, bit16_22:addr, bit23:0w,1r
    TXData = ((RegTypeTemp.Addr & 0x7f) << 16) | ((RegTypeTemp.Data & 0xff) << 8) | (ChipData & 0xff);

    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadLTC5594Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadLTC5594Code info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SPI_REG_TX_TYPE]    = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_LTC5594_SPI + BUSI_DEVM_SPI_CR2;

    //bit0_15:data, bit16_22:addr, bit23:0w,1r
    TXData = (0x1u << 23) + ((RegTypeTemp.Addr & 0x7f) << 16);

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    //取bit8_15的数据
    RegTypeTemp.Data = (RegTypeTemp.Data & 0xff00) >> 8;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadLTC5594Code info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//LMX2594
int WT_WriteLMX2594Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    unsigned int RxAddr,TxAddr;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_LMX2594SetCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if(GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        if (pdev->type < 0 || pdev->type >= 2)
        {
            dbg("VirtualAddrMode WT_WriteLMX2594Code error!\n");
            return WT_OK;
        }
        wt_write_LMX2594_Cfg_List(&(pdev->dev_virtual_addr), RegTypeTemp.Addr, RegTypeTemp.Data);
        return WT_OK;
    }

    if(pdev->testertype == HW_WT418)
    {
        RxAddr = BUSI_RX_LMX2594_SPI_418;
        TxAddr = BUSI_TX_LMX2594_SPI_418;
    }
    else
    {
        RxAddr = BUSI_RX_LMX2594_SPI;
        TxAddr = BUSI_TX_LMX2594_SPI;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]    = RxAddr + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = RxAddr + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = RxAddr + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = RxAddr + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = TxAddr + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = TxAddr + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TxAddr + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TxAddr + BUSI_DEVM_SPI_CR2;
    }
    //bit0_15:data, bit16_22:addr, bit23:0w,1r
    TXData = ((RegTypeTemp.Addr & 0x7f) << 16) + (RegTypeTemp.Data & 0xffff);
    dbg_print("WT_WriteLMX2594Code Type=%d, RegTypeTemp.Addr=0x%x, RegTypeTemp.Data=0x%x, TxData=0x%x\n",
        pdev->type, RegTypeTemp.Addr, RegTypeTemp.Data, TXData);
    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadLMX2594Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    unsigned int RxAddr,TxAddr;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadLMX2594Code info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        RegTypeTemp.Data = 0;
        if (copy_to_user(arg, &RegTypeTemp, DataLength))
        {
            dbg("wt_ReadLMX2594Code info copy_to_user failed!\n");
            return WT_CPY_TO_USR_FAILED;
        }
        return WT_OK;
    }

    if(pdev->testertype == HW_WT418)
    {
        RxAddr = BUSI_RX_LMX2594_SPI_418;
        TxAddr = BUSI_TX_LMX2594_SPI_418;
    }
    else
    {
        RxAddr = BUSI_RX_LMX2594_SPI;
        TxAddr = BUSI_TX_LMX2594_SPI;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]    = RxAddr + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = RxAddr + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = RxAddr + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = RxAddr + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = TxAddr + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = TxAddr + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TxAddr + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TxAddr + BUSI_DEVM_SPI_CR2;
    }
    //bit0_15:data, bit16_22:addr, bit23:0w,1r
    TXData = (0x1u << 23) + ((RegTypeTemp.Addr & 0x7f) << 16);

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    RegTypeTemp.Data = RegTypeTemp.Data & 0xffff;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadLMX2594Code info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//LMX2820
int WT_WriteLMX2820Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct DeviceConfig DeviceConfigTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    int SpiBaseAddr = 0;

    if (copy_from_user(&DeviceConfigTemp, arg, DataLength))
    {
        dbg("WT_LMX2820SetCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if (pdev->type == DEV_TYPE_VSA)
    {
        SpiBaseAddr = DeviceConfigTemp.DeviceId == 0 ? BUSI_RX_LMX2820_1_SPI : BUSI_RX_LMX2820_2_SPI;
    }
    else
    {
        SpiBaseAddr = DeviceConfigTemp.DeviceId == 0 ? BUSI_TX_LMX2820_1_SPI : BUSI_TX_LMX2820_2_SPI;
    }
    Reg[SPI_REG_TX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR2;

    //bit0_15:data, bit16_22:addr, bit23:0w,1r
    TXData = ((DeviceConfigTemp.RegTypeData.Addr & 0x7f) << 16) + (DeviceConfigTemp.RegTypeData.Data & 0xffff);

    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadLMX2820Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct DeviceConfig DeviceConfigTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    int SpiBaseAddr = 0;

    if (copy_from_user(&DeviceConfigTemp, arg, DataLength))
    {
        dbg("WT_ReadLMX2820Code info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->type == DEV_TYPE_VSA)
    {
        SpiBaseAddr = DeviceConfigTemp.DeviceId == 0 ? BUSI_RX_LMX2820_1_SPI : BUSI_RX_LMX2820_2_SPI;
    }
    else
    {
        SpiBaseAddr = DeviceConfigTemp.DeviceId == 0 ? BUSI_TX_LMX2820_1_SPI : BUSI_TX_LMX2820_2_SPI;
    }
    Reg[SPI_REG_TX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = SpiBaseAddr + BUSI_DEVM_SPI_CR2;

    //bit0_15:data, bit16_22:addr, bit23:0w,1r
    TXData = (0x1u << 23) + ((DeviceConfigTemp.RegTypeData.Addr & 0x7f) << 16);

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &DeviceConfigTemp.RegTypeData.Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    // printk("WT_ReadLMX2820Code Type=%d, DevId=%d, Addr=%d, TxData=%d, Data=%#x\n",
    //        pdev->type, DeviceConfigTemp.DeviceId, DeviceConfigTemp.RegTypeData.Addr, TXData, DeviceConfigTemp.RegTypeData.Data);

    DeviceConfigTemp.RegTypeData.Data = DeviceConfigTemp.RegTypeData.Data & 0xffff;

    if (copy_to_user(arg, &DeviceConfigTemp, DataLength))
    {
        dbg("wt_ReadLMX2820Code info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_WriteHMC833Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1420;    // SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteHMC833Code info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if (GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        if (pdev->type < 0 || pdev->type >= 2)
        {
            dbg("VirtualAddrMode WT_WriteHMC833Code error!\n");
            return WT_OK;
        }
        wt_write_HMC833_Cfg_List(&(pdev->dev_virtual_addr), RegTypeTemp.Addr, RegTypeTemp.Data);
        return WT_OK;
    }

    if (pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_CR2;
    }
    // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
    TXData = ((RegTypeTemp.Addr & 0x3F) << 25) + ((RegTypeTemp.Data & 0xffffff)<<1);
    dbg_print("WT_WriteHMC833Code Type=%d, RegTypeTemp.Addr=0x%x, RegTypeTemp.Data=0x%x, TxData=0x%x\n",
        pdev->type, RegTypeTemp.Addr, RegTypeTemp.Data, TXData);
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadHMC833Code(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1420;    // SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadHMC833Code info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        RegTypeTemp.Data = 0;

        if (copy_to_user(arg, &RegTypeTemp, DataLength))
        {
            dbg("WT_ReadHMC833Code info copy_to_user failed!\n");
            return WT_CPY_TO_USR_FAILED;
        }
        return WT_OK;
    }

    if (pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_RX_HMC833_SPI + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_TX_HMC833_SPI + BUSI_DEVM_SPI_CR2;
    }

    // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
    TXData = (0x1u << 31) + ((RegTypeTemp.Addr & 0x3F) << 25);
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);

    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    RegTypeTemp.Data = RegTypeTemp.Data & 0xffffff;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("WT_ReadHMC833Code info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//AD7091
int wt_WriteBusiAD7091Reg(int Addr, int Data, struct dev_unit *pdev)
{
    int SPIConfig = 0x1810;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    Reg[SPI_REG_TX_TYPE]    = BUSI_AD7091_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_AD7091_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_AD7091_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_AD7091_SPI + BUSI_DEVM_SPI_CR2;

    if (Addr == 0 || Addr == 1)
    {
        wt_write_direct_reg(pdev, BUSI_AD7091_CONVST, 0);
        udelay(1);
        wt_write_direct_reg(pdev, BUSI_AD7091_CONVST, 1);
        udelay(10); //延时处理
    }

    //bit0_9:data, bit11_15:addr, bit10:1w,0r
    TXData = (0x1u << 10) + ((Addr & 0x1f) << 11) + (Data & 0x3ff);

    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_WriteBusiAD7091Reg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteBusiAD7091Reg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    ret = wt_WriteBusiAD7091Reg(RegTypeTemp.Addr, RegTypeTemp.Data, pdev);
    retWarnning(ret, "WT_WriteBusiAD7091Reg failed!\n");
    return ret;
}

int wt_ReadBusiAD7091Reg(int Addr, int *Data, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SPIConfig = 0x3810;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    Reg[SPI_REG_TX_TYPE]    = BUSI_AD7091_SPI + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE]    = BUSI_AD7091_SPI + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_AD7091_SPI + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_AD7091_SPI + BUSI_DEVM_SPI_CR2;

    if (Addr == 0)
    {
        wt_write_direct_reg(pdev, BUSI_AD7091_CONVST, 0);
        udelay(1);
        wt_write_direct_reg(pdev, BUSI_AD7091_CONVST, 1);
        udelay(10); //延时处理
    }

    //bit0_9:data, bit11_15:addr, bit10:1w,0r
    TXData = (Addr & 0x1f) << 11;

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");
    return ret;
}

int WT_ReadBusiAD7091Reg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadBusiAD7091Reg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    ret = wt_ReadBusiAD7091Reg(RegTypeTemp.Addr, &RegTypeTemp.Data, pdev);
    retWarnning(ret, "wt_ReadBusiAD7091Reg failed!\n");

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadBusiAD7091Reg info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_ReadBusiChannelVoltValue(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    struct RegType RegTypeTemp;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadBusiChannelVoltValue info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    ret = wt_WriteBusiAD7091Reg(0x1, (0x1 << RegTypeTemp.Addr), pdev); //读取电压通道的对应的bit位置为1
    retAssert(ret, "WT_WriteBusiAD7091Reg failed!\n");

    //去掉第一次无效数据
    ret = wt_ReadBusiAD7091Reg(0x0, &RegTypeTemp.Data, pdev);
    retAssert(ret, "wt_ReadBusiAD7091Reg 1 failed!");

    //重复操作一次
    ret = wt_ReadBusiAD7091Reg(0x0, &RegTypeTemp.Data, pdev);
    retAssert(ret, "wt_ReadBusiAD7091Reg 2 failed!");

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("WT_ReadBusiChannelVoltValue info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

//DDS AD9912
int WT_WriteDDSCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1218;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteDDSCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_CR2;
    }

    if (pdev->version >= VERSION_B)
    {
        SPIConfig = 0x1218;
    }
    else
    {
        SPIConfig = 0x1818;
    }

    //bit0_7:data, bit8_20:addr, bit23:0w,1r
    TXData = ((RegTypeTemp.Addr & 0x1fff) << 8) + (RegTypeTemp.Data & 0xff);

    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadDDSCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1218;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadDDSCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_RX_DDS_SPI + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_TX_DDS_SPI + BUSI_DEVM_SPI_CR2;
    }

    if (pdev->version >= VERSION_B)
    {
        SPIConfig = 0x1218;
    }
    else
    {
        SPIConfig = 0x1818;
    }

    //bit0_7:data, bit8_20:addr, bit23:0w,1r
    TXData = (0x1u << 23) + ((RegTypeTemp.Addr & 0x1fff) << 8);

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    RegTypeTemp.Data = RegTypeTemp.Data & 0xff;

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadDDSCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_SetDDSFreqChannel(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    int Reg[2] = {0};
    long long ShiftData;
    unsigned long long DDSFreq = 0;

    if (copy_from_user(&DDSFreq, arg, DataLength))
    {
        dbg("WT_SetDDSFreq info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Ret = wt_ReadLoBoardShitfCode(pdev, &ShiftData);
    retAssert(Ret, "WT_SetDDSFreq wt_ReadLoBoardShitfCode failed!");
    if (pdev->version >= VERSION_C)
    {
        if (pdev->type == DEV_TYPE_VSA)
        {
            switch (DDSFreq)
            {
            case REF_DDS_700M:
                ClearBit(ShiftData, LO_SHIFT_VB_RX_SWR_DE_CTL);
                break;
            case REF_100M:
                SetBit(ShiftData, LO_SHIFT_VB_RX_SWR_DE_CTL);
                break;
            }
        }
        else
        {
            switch (DDSFreq)
            {
            case REF_DDS_700M:
                SetBit(ShiftData, LO_SHIFT_VB_TX_SWT_MO_CTL);
                break;
            case REF_100M:
                ClearBit(ShiftData, LO_SHIFT_VB_TX_SWT_MO_CTL);
                break;
            }
        }
    }
    else if (pdev->version == VERSION_B)
    {
        if (pdev->type == DEV_TYPE_VSA)
        {
            switch (DDSFreq)
            {
            case REF_DDS_700M:
                SetBit(ShiftData, LO_SHIFT_VB_RX_SWR_DDS_CTL);
                ClearBit(ShiftData, LO_SHIFT_VB_RX_SWR_DE_CTL);
                break;
            case REF_DDS_900M:
                ClearBit(ShiftData, LO_SHIFT_VB_RX_SWR_DDS_CTL);
                ClearBit(ShiftData, LO_SHIFT_VB_RX_SWR_DE_CTL);
                break;
            case REF_100M:
                SetBit(ShiftData, LO_SHIFT_VB_RX_SWR_DDS_CTL);
                SetBit(ShiftData, LO_SHIFT_VB_RX_SWR_DE_CTL);
                break;
            }
        }
        else
        {
            switch (DDSFreq)
            {
            case REF_DDS_700M:
                ClearBit(ShiftData, LO_SHIFT_VB_TX_SWT_DDS_CTL);
                SetBit(ShiftData, LO_SHIFT_VB_TX_SWT_MO_CTL);
                break;
            case REF_DDS_900M:
                SetBit(ShiftData, LO_SHIFT_VB_TX_SWT_DDS_CTL);
                SetBit(ShiftData, LO_SHIFT_VB_TX_SWT_MO_CTL);
                break;
            case REF_100M:
                SetBit(ShiftData, LO_SHIFT_VB_TX_SWT_DDS_CTL);
                ClearBit(ShiftData, LO_SHIFT_VB_TX_SWT_MO_CTL);
                break;
            }
        }
    }
    else
    {
        if (pdev->type == DEV_TYPE_VSA)
        {
            Reg[0] = LO_SHIFT_RX_RX_DDS_SW1_CTL;
            Reg[1] = LO_SHIFT_RX_RX_DDS_SW2_CTL;
        }
        else
        {
            Reg[0] = LO_SHIFT_TX_TX_DDS_SW1_CTL;
            Reg[1] = LO_SHIFT_TX_TX_DDS_SW2_CTL;
        }
        if (DDSFreq <= 300 * MHz)
        {
            SetBit(ShiftData, Reg[0]);
            ClearBit(ShiftData, Reg[1]);
        }
        else if (DDSFreq <= 400 * MHz)
        {
            ClearBit(ShiftData, Reg[0]);
            SetBit(ShiftData, Reg[1]);
        }
        else
        {
            SetBit(ShiftData, Reg[0]);
            SetBit(ShiftData, Reg[1]);
        }
    }

    Ret = wt_WriteLoBoardShitfCode(pdev, ShiftData);
    retAssert(Ret, "WT_SetDDSFreq wt_WriteLoBoardShitfCode failed!");
    return WT_OK;
}

//AttAndShift
int WT_WriteAttAndShiftCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct DeviceConfig DevfgTemp;
    int SpiAddr = BUSI_RX_SHIFT_0_SPI;
    int SPIConfig = 0x1820;       // SPI配置参数
    int TXData = 0;               // 下发TX寄存器的数据
    unsigned int Reg[4] = {0};    // 器件所映射的寄存器地址

    const static unsigned int RfShiftRegConfig_VB[BUSI_BOARD_TYPE_COUNT][3] = {
        {0x1800 | 24, 0x1800 | 8, 0x1800 | 24},
        {0x1800 | 24, 0x1800 | 16, 0x1800 | 24}};

    const static unsigned int RfShiftRegConfig_VA[BUSI_BOARD_TYPE_COUNT][3] = {
        {0x1280 | 24, 0x1280 | 32, 0x1280 | 32},
        {0x1280 | 32, 0x1280 | 24, 0x1280 | 32}};

    const static unsigned int RfShiftRegConfig_418VA[BUSI_BOARD_TYPE_COUNT][2] = {
        {0x1810, 0x1818},
        {0x1818, 0x1818}};

    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_AttAndShiftSetCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (DevfgTemp.DeviceId == 0)
    {
        SpiAddr = (pdev->type == DEV_TYPE_VSA) ? BUSI_RX_SHIFT_0_SPI : BUSI_TX_SHIFT_0_SPI;
    }
    else if (DevfgTemp.DeviceId == 1)
    {
        SpiAddr = (pdev->type == DEV_TYPE_VSA) ? BUSI_RX_SHIFT_1_SPI : BUSI_TX_SHIFT_1_SPI;
    }
    else if (pdev->testertype != HW_WT418 && DevfgTemp.DeviceId == 2)
    {
        SpiAddr = (pdev->type == DEV_TYPE_VSA) ? BUSI_RX_SHIFT_2_SPI : BUSI_TX_SHIFT_2_SPI;
    }
    else
    {
        dbg("AttAndShift ChipId error, ChipId=%d", DevfgTemp.DeviceId);
        return WT_ARG_ERROR;
    }

    if(pdev->testertype == HW_WT418)
    {
        SPIConfig = RfShiftRegConfig_418VA[pdev->type][DevfgTemp.DeviceId];
    }
    else if (pdev->version >= VERSION_B)
    {
        SPIConfig = RfShiftRegConfig_VB[pdev->type][DevfgTemp.DeviceId];
    }
    else
    {
        SPIConfig = RfShiftRegConfig_VA[pdev->type][DevfgTemp.DeviceId];
    }

    Reg[SPI_REG_TX_TYPE] = SpiAddr + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE] = SpiAddr + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = SpiAddr + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = SpiAddr + BUSI_DEVM_SPI_CR2;

    TXData = DevfgTemp.RegTypeData.Data;
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int WT_ReadAttAndShiftCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct DeviceConfig DevfgTemp;
    int SpiAddr = BUSI_RX_SHIFT_0_SPI;
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_ReadAttAndShiftCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (DevfgTemp.DeviceId == 0)
    {
        SpiAddr = (pdev->type == DEV_TYPE_VSA) ? BUSI_RX_SHIFT_0_SPI : BUSI_TX_SHIFT_0_SPI;
    }
    else if (DevfgTemp.DeviceId == 1)
    {
        SpiAddr = (pdev->type == DEV_TYPE_VSA) ? BUSI_RX_SHIFT_1_SPI : BUSI_TX_SHIFT_1_SPI;
    }
    else if (pdev->testertype != HW_WT418 && DevfgTemp.DeviceId == 2)
    {
        SpiAddr = (pdev->type == DEV_TYPE_VSA) ? BUSI_RX_SHIFT_2_SPI : BUSI_TX_SHIFT_2_SPI;
    }
    else
    {
        dbg("AttAndShift ChipId error, ChipId=%d", DevfgTemp.DeviceId);
        return WT_ARG_ERROR;
    }

    Reg[SPI_REG_TX_TYPE] = SpiAddr + BUSI_DEVM_SPI_TX;
    Reg[SPI_REG_RX_TYPE] = SpiAddr + BUSI_DEVM_SPI_RX;
    Reg[SPI_REG_CTRL1_TYPE] = SpiAddr + BUSI_DEVM_SPI_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = SpiAddr + BUSI_DEVM_SPI_CR2;

#if RF_SHF_TX_ADDR_READ
    DevfgTemp.RegTypeData.Data = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
#else
    DevfgTemp.RegTypeData.Data = wt_read_direct_reg(pdev, Reg[SPI_REG_RX_TYPE]);
#endif

    if (copy_to_user(arg, &DevfgTemp, DataLength))
    {
        dbg("wt_ReadAttAndShiftCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//LoBoardShitf
int wt_WriteLoBoardShitfCode(struct dev_unit *pdev, long long Data)
{
    int SPIConfig = 0x1428;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[SPI_REG_TYPE_MAX] = {0}; //器件所映射的寄存器地址

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]        = BUSI_RX_LO_SHIFT_TX_0_7;
        Reg[SPI_REG_TX_TYPE_HIGHT]  = BUSI_RX_LO_SHIFT_TX_8_39;
        Reg[SPI_REG_RX_TYPE]        = BUSI_RX_LO_SHIFT_RX_0_7;
        Reg[SPI_REG_RX_TYPE_HIGHT]  = BUSI_RX_LO_SHIFT_RX_8_39;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_RX_LO_SHIFT_CR1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_RX_LO_SHIFT_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]        = BUSI_TX_LO_SHIFT_TX_0_7;
        Reg[SPI_REG_TX_TYPE_HIGHT]  = BUSI_TX_LO_SHIFT_TX_8_39;
        Reg[SPI_REG_RX_TYPE]        = BUSI_TX_LO_SHIFT_RX_0_7;
        Reg[SPI_REG_RX_TYPE_HIGHT]  = BUSI_TX_LO_SHIFT_RX_8_39;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_TX_LO_SHIFT_CR1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_TX_LO_SHIFT_CR2;
    }

    //低8位
    TXData = Data & 0xff;
    //高32位
    wt_write_direct_reg(pdev, Reg[SPI_REG_TX_TYPE_HIGHT], Data>> 8);

    //设置晶振的code值
    return wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
}

int wt_ReadLoBoardShitfCode(struct dev_unit *pdev, long long *Data)
{
    int RXDataH = 0;
    int RXDataL = 0;
    unsigned int Reg[SPI_REG_TYPE_MAX] = {0}; //器件所映射的寄存器地址

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]        = BUSI_RX_LO_SHIFT_TX_0_7;
        Reg[SPI_REG_TX_TYPE_HIGHT]  = BUSI_RX_LO_SHIFT_TX_8_39;
        Reg[SPI_REG_RX_TYPE]        = BUSI_RX_LO_SHIFT_RX_0_7;
        Reg[SPI_REG_RX_TYPE_HIGHT]  = BUSI_RX_LO_SHIFT_RX_8_39;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_RX_LO_SHIFT_CR1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_RX_LO_SHIFT_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]        = BUSI_TX_LO_SHIFT_TX_0_7;
        Reg[SPI_REG_TX_TYPE_HIGHT]  = BUSI_TX_LO_SHIFT_TX_8_39;
        Reg[SPI_REG_RX_TYPE]        = BUSI_TX_LO_SHIFT_RX_0_7;
        Reg[SPI_REG_RX_TYPE_HIGHT]  = BUSI_TX_LO_SHIFT_RX_8_39;
        Reg[SPI_REG_CTRL1_TYPE]     = BUSI_TX_LO_SHIFT_CR1;
        Reg[SPI_REG_CTRL2_TYPE]     = BUSI_TX_LO_SHIFT_CR2;
    }

    //设置晶振的code值
    RXDataL = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
    RXDataH = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE_HIGHT]);

    *Data = ((long long)RXDataH << 8) + (RXDataL & 0xff);
    return WT_OK;
}

int WT_WriteLoBoardShitfCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct Reg64Type RegTypeTemp;
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteLoBoardShitfCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    //设置本振板位移寄存器的code值
    return wt_WriteLoBoardShitfCode(pdev, RegTypeTemp.Data);
}

int WT_ReadLoBoardShitfCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct Reg64Type RegTypeTemp;
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadLoBoardShitfCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    ret = wt_ReadLoBoardShitfCode(pdev, &RegTypeTemp.Data);
    retAssert(ret, "wt_ReadLoBoardShitfCode failed");
    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("WT_ReadLoBoardShitfCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return ret;
}

int WT_SetLoBoardHMC705(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int DivRatioN;
    long long ShiftData;
    if (copy_from_user(&DivRatioN, arg, DataLength))
    {
        dbg("WT_SetLoBoardHMC705 info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(DivRatioN < BUSI_LO_HMC705_DIV_RATIO_1 || DivRatioN >= BUSI_LO_HMC705_DIV_RATIO_MAX)
    {
        dbg("WT_SetLoBoardHMC705 WT_ARG_ERROR DivRatioN=%d!\n", DivRatioN);
        return WT_ARG_ERROR;
    }

    ret = wt_ReadLoBoardShitfCode(pdev, &ShiftData);
    retAssert(ret, "wt_ReadLoBoardShitfCode faild");

    ret = wt_GetLoHMC705TableData(pdev, DivRatioN, &ShiftData);
    retAssert(ret, "wt_GetLoHMC705TableData faild");

    ret = wt_WriteLoBoardShitfCode(pdev, ShiftData);
    retAssert(ret, "wt_WriteLoBoardShitfCode faild");
    return ret;
}

int WT_SetLoBoardLoopFilter(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int LFIndex;
    long long ShiftData;
    if (copy_from_user(&LFIndex, arg, DataLength))
    {
        dbg("WT_SetLoBoardLoopFilter info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(LFIndex < BUSI_LO_LOOP_FILTER_1 || LFIndex >= BUSI_LO_LOOP_FILTER_MAX)
    {
        dbg("WT_SetLoBoardLoopFilter WT_ARG_ERROR LFIndex=%d!\n", LFIndex);
        return WT_ARG_ERROR;
    }

    ret = wt_ReadLoBoardShitfCode(pdev, &ShiftData);
    retAssert(ret, "wt_ReadLoBoardShitfCode faild");

    ret = wt_GetLoLoopFilterData(pdev, LFIndex, &ShiftData);
    retAssert(ret, "wt_GetLoLoopFilterData faild");

    ret = wt_WriteLoBoardShitfCode(pdev, ShiftData);
    retAssert(ret, "wt_WriteLoBoardShitfCode faild");
    return ret;
}

int WT_SetLoBoardFreqChannel(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int LFIndex;
    long long ShiftData;
    if (copy_from_user(&LFIndex, arg, DataLength))
    {
        dbg("WT_SetLoBoardFreqChannel info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->version >= VERSION_B &&
        (LFIndex < BUSI_VB_LO_FREQ_CHANNEL_300_520 || LFIndex >= BUSI_VB_LO_FREQ_CHANNEL_MAX))
    {
        dbg("WT_SetLoBoardFreqChannel WT_ARG_ERROR LFIndex=%d!\n", LFIndex);
        return WT_ARG_ERROR;
    }
    else if (LFIndex < BUSI_LO_FREQ_CHANNEL_MUL_2_HITHT || LFIndex >= BUSI_LO_FREQ_CHANNEL_MAX)
    {
        dbg("WT_SetLoBoardFreqChannel WT_ARG_ERROR LFIndex=%d!\n", LFIndex);
        return WT_ARG_ERROR;
    }

    ret = wt_ReadLoBoardShitfCode(pdev, &ShiftData);
    retAssert(ret, "wt_ReadLoBoardShitfCode faild");

    ret = wt_GetLoFreqChannelData(pdev, LFIndex, &ShiftData);
    retAssert(ret, "wt_GetLoLoopFilterData faild");

    ret = wt_WriteLoBoardShitfCode(pdev, ShiftData);
    retAssert(ret, "wt_WriteLoBoardShitfCode faild");
    return ret;
}

//ADC9684 or DAC9142 or AD9643
int WT_WriteAdcOrDacCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    int Index = 0;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteAdcOrDacCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        if(pdev->type<0 || pdev->type>=2)
        {
            dbg("VirtualAddrMode WT_WriteAdcOrDacCode error!\n");
            return WT_OK;
        }
        wt_write_AdcOrDac_Cfg_List(&(pdev->dev_virtual_addr), RegTypeTemp.Addr, RegTypeTemp.Data);
        return WT_OK;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_CR2;
    }

    //bit0_7:data, bit8_22:addr, bit23:0w,1r
    TXData = ((RegTypeTemp.Addr & 0x7fff) << 8) + (RegTypeTemp.Data & 0xff);

    if (pdev->type == DEV_TYPE_VSG)
    {
        if (RegTypeTemp.Addr >= IDAC_DC_OFFSET0 && RegTypeTemp.Addr <= QDAC_DC_OFFSET1)
        {
            Index = RegTypeTemp.Addr - IDAC_DC_OFFSET0;
            g_DCOffsetReg[pdev->id][Index] = RegTypeTemp.Data & 0xff;
            //printk("WT_WriteDacCode Type%d ModId%d Addr%#x, Data%#x\n", pdev->type, pdev->id, RegTypeTemp.Addr, RegTypeTemp.Data & 0xff);
        }
    }

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_write failed!\n");
    if(pdev->testertype == HW_WT418)
    {
        //HW_WT418的ADC器件是AD9643
        ret = wt_bp_spi_direct_for_write(SPIConfig, ((0xff & 0x7fff) << 8) + (0x1 & 0xff), Reg, pdev);
    }
    return ret;
}

int WT_ReadAdcOrDacCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;
    int SPIConfig = 0x1418;    //SPI配置参数
    int TXData = 0;            //下发TX寄存器的数据
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址
    int Index = 0;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadAdcOrDacCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->type == DEV_TYPE_VSA)
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_AD9684_ADC_SPI + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_AD9142_DAC_SPI + BUSI_DEVM_SPI_CR2;
    }

    //bit0_7:data, bit8_22:addr, bit23:0w,1r
    TXData = (0x1u << 23) + ((RegTypeTemp.Addr & 0x7fff) << 8);

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_read(SPIConfig, TXData, &RegTypeTemp.Data, Reg, pdev);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");

    RegTypeTemp.Data = RegTypeTemp.Data & 0xff;

    if (pdev->type == DEV_TYPE_VSG)
    {
        if (RegTypeTemp.Addr >= IDAC_DC_OFFSET0 && RegTypeTemp.Addr <= QDAC_DC_OFFSET1)
        {
            Index = RegTypeTemp.Addr - IDAC_DC_OFFSET0;
            if (g_DCOffsetReg[pdev->id][Index] != RegTypeTemp.Data)
            {
                dbg_print("warnning-------- WT_ReadAdcOrDacCode Type%d ModId%d Addr%#x, Data%#x, DataBak%#x\n",
                       pdev->type, pdev->id, RegTypeTemp.Addr, RegTypeTemp.Data, g_DCOffsetReg[pdev->id][Index]);
            }
        }
    }

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("wt_ReadAdcOrDacCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

// IQ交换
int WT_SetIQSwitch(int DataLength, void *arg, struct dev_unit *pdev)
{
    int IQSwitch; // IQ数据交换(0:保持不变  1：IQ翻转)

    if (copy_from_user(&IQSwitch, arg, DataLength))
    {
        dbg("WT_SetIQSwitch info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->testertype == HW_WT418 || pdev->version >= VERSION_B)
    {
        wt_write_direct_reg(pdev, pdev->type == DEV_TYPE_VSG ? VSG_IQ_SWITCH : VSA_IQ_SWITCH, IQSwitch & 0x1);
    }
    return WT_OK;
}

int WT_SetRFPA(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int Status = 0;
    int TXData = 0; // 先读FPGA
    int SPIConfig = 0x1800 | 24; // VB
    unsigned int Reg[4];

    (void)DataLength;

    if (copy_from_user(&Status, arg, sizeof(int)))
    {
        dbg("WT_SetRFPA info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if(pdev->testertype == HW_WT418)
    {
        Reg[SPI_REG_TX_TYPE]    = TX_SHIFT_1_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = TX_SHIFT_1_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_CR2;
    }
    else if (pdev->version >= VERSION_B)
    {
        Reg[SPI_REG_TX_TYPE]    = TX_SHIFT_0_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = TX_SHIFT_0_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TX_SHIFT_0_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TX_SHIFT_0_TX + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = TX_SHIFT_2_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = TX_SHIFT_2_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TX_SHIFT_2_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TX_SHIFT_2_TX + BUSI_DEVM_SPI_CR2;
    }

#if RF_SHF_TX_ADDR_READ
    TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
#else
    TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_RX_TYPE]);
#endif

    if(pdev->testertype == HW_WT418)
    {
        retWarnning(ret, "WT_SetRFPA:HW_WT418 Return\n");
        return ret;
    }
    else if (pdev->version >= VERSION_B)
    {
        // VB
        SPIConfig = 0x1800 | 24;
        if (Status)
        {
            ClearBit(TXData, VB_TX_SW3T_CTL);
            SetBit(TXData, VB_TX_SW4T_CTL);
            SetBit(TXData, VB_TX_TX_PA_ON);
        }
        else
        {
            SetBit(TXData, VB_TX_SW3T_CTL);
            ClearBit(TXData, VB_TX_SW4T_CTL);
            // ClearBit(TXData, VA_TX_PA_ON);  // 同328系列PA的处理
            SetBit(TXData, VB_TX_TX_PA_ON);
        }
    }
    else
    {
        SPIConfig = 0x1800 | 32;
        if (Status)
        {
            ClearBit(TXData, VA_TX_SW3T_CTL);
            SetBit(TXData, VA_TX_SW4T_CTL);
            SetBit(TXData, VA_TX_TX_PA_ON);
        }
        else
        {
            SetBit(TXData, VA_TX_SW3T_CTL);
            ClearBit(TXData, VA_TX_SW4T_CTL);
            // ClearBit(TXData, VA_TX_TX_PA_ON);  // 同328系列PA的处理
            SetBit(TXData, VA_TX_TX_PA_ON);
        }
    }

    ret = wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
    retWarnning(ret, "WT_SetRFPA set pa wt_bp_spi_direct_for_write failed!\n");

    return ret;
}

int WT_SetRFLNA(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int Status = 0;
    int TXData = 0;
    int SPIConfig = 0x1800 | 24; // VB
    unsigned int Reg[4]={};
    if(pdev->testertype == HW_WT418)
    {
        Reg[SPI_REG_TX_TYPE]    = RX_SHIFT_1_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = RX_SHIFT_1_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = RX_SHIFT_1_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = RX_SHIFT_1_TX + BUSI_DEVM_SPI_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE]    = RX_SHIFT_2_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE]    = RX_SHIFT_2_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = RX_SHIFT_2_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = RX_SHIFT_2_TX + BUSI_DEVM_SPI_CR2;
    }
    (void)DataLength;

    if (copy_from_user(&Status, arg, sizeof(int)))
    {
        dbg("WT_SetRFLNA info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if(pdev->testertype == HW_WT418)
    {
        TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
        SPIConfig = 0x1818;
        if (Status)
        {
            ClearBit(TXData, WT418_VA_RX_SW4R_CTL);
            SetBit(TXData, WT418_VA_RX_SW3R_CTL);
        }
        else
        {
            ClearBit(TXData, WT418_VA_RX_SW3R_CTL);
            SetBit(TXData, WT418_VA_RX_SW4R_CTL);
        }
    }
    else if (pdev->version >= VERSION_B)
    {
        SPIConfig = 0x1800 | 24;
#if RF_SHF_TX_ADDR_READ
        TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
#else
        TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_RX_TYPE1]);
#endif
        // VB
        if (Status)
        {
            ClearBit(TXData, VB_RX_SW3R_CTL);
            SetBit(TXData, VB_RX_SW4R_CTL);
        }
        else
        {
            SetBit(TXData, VB_RX_SW3R_CTL);
            ClearBit(TXData, VB_RX_SW4R_CTL);
        }
    }
    else
    {
        // VA 不支持
        return WT_OK;
    }
    ret = wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
    retWarnning(ret, "WT_SetRFLNA set pa wt_bp_spi_direct_for_write failed!\n");
    return ret;
}

int wt_SetRxLoBand(struct FreqBandType *pFreqBand, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SPIConfig_VA[3] = {0x1800 | 24, 0x1800 | 24, 0x1800 | 32};
    int SPIConfig[3] = {0x1800 | 24, 0x1800 | 8, 0x1800 | 24};
    const unsigned int Reg[3][4] = 
    {
        {RX_SHIFT_0_TX, RX_SHIFT_0_RX, RX_SHIFT_0_CR1, RX_SHIFT_0_CR2},
        {RX_SHIFT_1_TX, RX_SHIFT_1_RX, RX_SHIFT_1_CR1, RX_SHIFT_1_CR2},
        {RX_SHIFT_2_TX, RX_SHIFT_2_RX, RX_SHIFT_2_CR1, RX_SHIFT_2_CR2}
    };

    unsigned int SwitchTable[3] = {0};
    unsigned int TXData[3] = {0};

    wt_GetRxLoSwitchTableData(pFreqBand, SwitchTable);

    SwitchTable[0] &= RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][0];
    SwitchTable[1] &= RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][1];
    SwitchTable[2] &= RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][2];

//    printk("wt_SetRxLoBand RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][0]=0x%X SwitchTable[0]=0x%X\n", RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][0], SwitchTable[0]);
//    printk("wt_SetRxLoBand RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][1]=0x%X SwitchTable[1]=0x%X\n", RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][1], SwitchTable[1]);
//    printk("wt_SetRxLoBand RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][2]=0x%X SwitchTable[2]=0x%X\n", RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][2], SwitchTable[2]);

#if RF_SHF_TX_ADDR_READ
    TXData[0] = wt_read_direct_reg(pdev, RX_SHIFT_0_TX);
    TXData[1] = wt_read_direct_reg(pdev, RX_SHIFT_1_TX);
    TXData[2] = wt_read_direct_reg(pdev, RX_SHIFT_2_TX);
#else
    TXData[0] = wt_read_direct_reg(pdev, RX_SHIFT_0_RX);
    TXData[1] = wt_read_direct_reg(pdev, RX_SHIFT_1_RX);
    TXData[2] = wt_read_direct_reg(pdev, RX_SHIFT_2_RX);
#endif

    TXData[0] &= (~RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][0]);
    TXData[1] &= (~RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][1]);
    TXData[2] &= (~RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_LO][2]);

    TXData[0] |= SwitchTable[0];
    TXData[1] |= SwitchTable[1];
    TXData[2] |= SwitchTable[2];

    // 写移位寄存器
    if(pdev->testertype == HW_WT418)
    {
        //HW_WT418VA LoBand相关控制位都在RX_SHIFT_1_TX
        ret = wt_bp_spi_direct_for_write(0x1818, TXData[1], Reg[1], pdev);
    }
    else if (pdev->version >= VERSION_B)
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig[0], TXData[0], Reg[0], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[1], TXData[1], Reg[1], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[2], TXData[2], Reg[2], pdev);
    }
    else
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig_VA[0], TXData[0], Reg[0], pdev);
    }

    retWarnning(ret, "WT_SetTXBand set lo band wt_bp_spi_direct_for_write failed!\n");

    return WT_OK;
}

int WT_SetRXBand(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct FreqBandType FreqBandTypeTemp;
    int SPIConfig_VA[3] = {0x1800 | 24, 0x1800 | 24, 0x1800 | 32};
    int SPIConfig[3] = {0x1800 | 24, 0x1800 | 8, 0x1800 | 24};
    const unsigned int Reg[3][4] = 
    {
        {RX_SHIFT_0_TX, RX_SHIFT_0_RX, RX_SHIFT_0_CR1, RX_SHIFT_0_CR2},
        {RX_SHIFT_1_TX, RX_SHIFT_1_RX, RX_SHIFT_1_CR1, RX_SHIFT_1_CR2},
        {RX_SHIFT_2_TX, RX_SHIFT_2_RX, RX_SHIFT_2_CR1, RX_SHIFT_2_CR2}
    };

    unsigned int SwitchTable[3] = {0};
    unsigned int TXData[3] = {0};

    if (copy_from_user(&FreqBandTypeTemp, arg, DataLength))
    {
        dbg("WT_SetRXBand info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    // printk("WT_SetRXBand LoModBand=%d LoMixBand=%d BandMod=%d BandMIX=%d\n",
    //        FreqBandTypeTemp.LoModBand, FreqBandTypeTemp.LoMixBand, FreqBandTypeTemp.BandMod, FreqBandTypeTemp.BandMIX);

    wt_GetRxSwitchTableData(pdev, FreqBandTypeTemp, SwitchTable);

    SwitchTable[0] &= RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][0];
    SwitchTable[1] &= RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][1];
    SwitchTable[2] &= RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][2];

    // printk("WT_SetRXBand RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][0]=0x%X SwitchTable[0]=0x%X\n", RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][0], SwitchTable[0]);
    // printk("WT_SetRXBand RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][1]=0x%X SwitchTable[1]=0x%X\n", RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][1], SwitchTable[1]);
    // printk("WT_SetRXBand RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][2]=0x%X SwitchTable[2]=0x%X\n", RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][2], SwitchTable[2]);

#if RF_SHF_TX_ADDR_READ
    TXData[0] = wt_read_direct_reg(pdev, RX_SHIFT_0_TX);
    TXData[1] = wt_read_direct_reg(pdev, RX_SHIFT_1_TX);
    TXData[2] = wt_read_direct_reg(pdev, RX_SHIFT_2_TX);
#else
    TXData[0] = wt_read_direct_reg(pdev, RX_SHIFT_0_RX);
    TXData[1] = wt_read_direct_reg(pdev, RX_SHIFT_1_RX);
    TXData[2] = wt_read_direct_reg(pdev, RX_SHIFT_2_RX);
#endif


    TXData[0] &= (~RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][0]);
    TXData[1] &= (~RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][1]);
    TXData[2] &= (~RfSwicthMask[DEV_TYPE_VSA][RF_SW_CTRL_TYPE_ALL_RF][2]);

    TXData[0] |= SwitchTable[0];
    TXData[1] |= SwitchTable[1];
    TXData[2] |= SwitchTable[2];

    // 写移位寄存器
    if(pdev->testertype == HW_WT418)
    {
        //HW_WT418VA Band相关控制位都在RX_SHIFT_1_TX
        ret = wt_bp_spi_direct_for_write(0x1818, TXData[1], Reg[1], pdev);
    }
    else if (pdev->version >= VERSION_B)
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig[0], TXData[0], Reg[0], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[1], TXData[1], Reg[1], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[2], TXData[2], Reg[2], pdev);
    }
    else
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig_VA[1], TXData[1], Reg[1], pdev);
    }
    retWarnning(ret, "WT_SetRXBand set rf band wt_bp_spi_direct_for_write failed!\n");

    wt_SetRxLoBand(&FreqBandTypeTemp, pdev);

    return ret;
}

int wt_SetTxLoBand(struct FreqBandType *pFreqBand, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SPIConfig_VA[3] = {0x1800 | 32, 0x1800 | 24, 0x1800 | 32};
    int SPIConfig[3] = {0x1800 | 24, 0x1800 | 16, 0x1800 | 24};
    const unsigned int Reg[3][4] = 
    {
        {TX_SHIFT_0_TX, TX_SHIFT_0_RX, TX_SHIFT_0_CR1, TX_SHIFT_0_CR2},
        {TX_SHIFT_1_TX, TX_SHIFT_1_RX, TX_SHIFT_1_CR1, TX_SHIFT_1_CR2},
        {TX_SHIFT_2_TX, TX_SHIFT_2_RX, TX_SHIFT_2_CR1, TX_SHIFT_2_CR2}
    };

    unsigned int SwitchTable[3] = {0};
    unsigned int TXData[3] = {0};

    wt_GetTxLoSwitchTableData(pFreqBand, SwitchTable);

    SwitchTable[0] &= RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][0];
    SwitchTable[1] &= RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][1];
    SwitchTable[2] &= RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][2];

//    printk("wt_SetTxLoBand RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][0]=0x%X SwitchTable[0]=0x%X\n", RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][0], SwitchTable[0]);
//    printk("wt_SetTxLoBand RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][1]=0x%X SwitchTable[1]=0x%X\n", RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][1], SwitchTable[1]);
//    printk("wt_SetTxLoBand RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][2]=0x%X SwitchTable[2]=0x%X\n", RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][2], SwitchTable[2]);

#if RF_SHF_TX_ADDR_READ
    TXData[0] = wt_read_direct_reg(pdev, TX_SHIFT_0_TX);
    TXData[1] = wt_read_direct_reg(pdev, TX_SHIFT_1_TX);
    TXData[2] = wt_read_direct_reg(pdev, TX_SHIFT_2_TX);
#else
    TXData[0] = wt_read_direct_reg(pdev, TX_SHIFT_0_RX);
    TXData[1] = wt_read_direct_reg(pdev, TX_SHIFT_1_RX);
    TXData[2] = wt_read_direct_reg(pdev, TX_SHIFT_2_RX);
#endif

    TXData[0] &= (~RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][0]);
    TXData[1] &= (~RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][1]);
    TXData[2] &= (~RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_LO][2]);

    TXData[0] |= SwitchTable[0];
    TXData[1] |= SwitchTable[1];
    TXData[2] |= SwitchTable[2];

    // 写移位寄存器
    if(pdev->testertype == HW_WT418)
    {
        //HW_WT418VA LoBand相关控制位都在TX_SHIFT_1_TX
        ret = wt_bp_spi_direct_for_write(0x1818, TXData[1], Reg[1], pdev);
    }
    else if (pdev->version >= VERSION_B)
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig[0], TXData[0], Reg[0], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[1], TXData[1], Reg[1], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[2], TXData[2], Reg[2], pdev);
    }
    else
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig_VA[1], TXData[1], Reg[1], pdev);
    }

    retWarnning(ret, "WT_SetTXBand set lo band wt_bp_spi_direct_for_write failed!\n");
    return WT_OK;
}

int WT_SetTXBand(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct FreqBandType FreqBandTypeTemp;

    int SPIConfig_VA[3] = {0x1800 | 32, 0x1800 | 24, 0x1800 | 32};
    int SPIConfig[3] = {0x1800 | 24, 0x1800 | 16, 0x1800 | 24};
    const unsigned int Reg[3][4] = 
    {
        {TX_SHIFT_0_TX, TX_SHIFT_0_RX, TX_SHIFT_0_CR1, TX_SHIFT_0_CR2},
        {TX_SHIFT_1_TX, TX_SHIFT_1_RX, TX_SHIFT_1_CR1, TX_SHIFT_1_CR2},
        {TX_SHIFT_2_TX, TX_SHIFT_2_RX, TX_SHIFT_2_CR1, TX_SHIFT_2_CR2}
    };

    unsigned int SwitchTable[3] = {0};
    unsigned int TXData[3] = {0};

    if (copy_from_user(&FreqBandTypeTemp, arg, DataLength))
    {
        dbg("WT_SetTXBand info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    // printk("WT_SetTXBand LoModBand=%d LoMixBand=%d BandMod=%d BandMIX=%d\n",
    //        FreqBandTypeTemp.LoModBand, FreqBandTypeTemp.LoModBand, FreqBandTypeTemp.BandMod, FreqBandTypeTemp.BandMIX);

    wt_GetTxSwitchTableData(pdev, FreqBandTypeTemp, SwitchTable);

    SwitchTable[0] &= RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][0];
    SwitchTable[1] &= RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][1];
    SwitchTable[2] &= RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][2];

    // printk("WT_SetTXBand RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][0]=0x%X SwitchTable[0]=0x%X\n", RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][0], SwitchTable[0]);
    // printk("WT_SetTXBand RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][1]=0x%X SwitchTable[1]=0x%X\n", RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][1], SwitchTable[1]);
    // printk("WT_SetTXBand RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][2]=0x%X SwitchTable[2]=0x%X\n", RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][2], SwitchTable[2]);

#if RF_SHF_TX_ADDR_READ
    TXData[0] = wt_read_direct_reg(pdev, TX_SHIFT_0_TX);
    TXData[1] = wt_read_direct_reg(pdev, TX_SHIFT_1_TX);
    TXData[2] = wt_read_direct_reg(pdev, TX_SHIFT_2_TX);
#else
    TXData[0] = wt_read_direct_reg(pdev, TX_SHIFT_0_RX);
    TXData[1] = wt_read_direct_reg(pdev, TX_SHIFT_1_RX);
    TXData[2] = wt_read_direct_reg(pdev, TX_SHIFT_2_RX);
#endif

    TXData[0] &= (~RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][0]);
    TXData[1] &= (~RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][1]);
    TXData[2] &= (~RfSwicthMask[DEV_TYPE_VSG][RF_SW_CTRL_TYPE_ALL_RF][2]);

    TXData[0] |= SwitchTable[0];
    TXData[1] |= SwitchTable[1];
    TXData[2] |= SwitchTable[2];

    // 写移位寄存器
    if(pdev->testertype == HW_WT418)
    {
        //HW_WT418VA Band相关控制位都在TX_SHIFT_1_TX
        ret = wt_bp_spi_direct_for_write(0x1818, TXData[1], Reg[1], pdev);
    }
    else if (pdev->version >= VERSION_B)
    {
        // 写移位寄存器
        ret = wt_bp_spi_direct_for_write(SPIConfig[0], TXData[0], Reg[0], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[1], TXData[1], Reg[1], pdev);
        ret = wt_bp_spi_direct_for_write(SPIConfig[2], TXData[2], Reg[2], pdev);
    }
    else
    {
        ret = wt_bp_spi_direct_for_write(SPIConfig_VA[2], TXData[2], Reg[2], pdev);
    }
    retWarnning(ret, "WT_SetTXBand set rf band wt_bp_spi_direct_for_write failed!\n");
    wt_SetTxLoBand(&FreqBandTypeTemp, pdev);
    return ret;
}


//ATT
int wt_WriteATT(struct dev_unit *pdev, int ATTId, int ATTCode)
{
    //int ret = WT_OK;
    int temp;
    int i = 0;
    int SPIConfig_VA[2][3] =
    {
        {0x1800 | 24, 0x1800 | 24, 0x1800 | 32},
        {0x1800 | 32, 0x1800 | 24, 0x1800 | 32}
    };
    const int SPIConfig[2][3] =
    {
        {0x1800 | 24, 0x1800 | 8, 0x1800 | 24},  // VSA
        {0x1800 | 24, 0x1800 | 16, 0x1800 | 24}   // VSG
    };
    const int SPIConfig_418VA[2][2] =
    {
        {0x1810, 0x1818},  // VSA
        {0x1818, 0x1818}   // VSG
    };
    const unsigned int Reg[2][3][4] = {
        {
            {RX_SHIFT_0_TX, RX_SHIFT_0_RX, RX_SHIFT_0_CR1, RX_SHIFT_0_CR2},
            {RX_SHIFT_1_TX, RX_SHIFT_1_RX, RX_SHIFT_1_CR1, RX_SHIFT_1_CR2},
            {RX_SHIFT_2_TX, RX_SHIFT_2_RX, RX_SHIFT_2_CR1, RX_SHIFT_2_CR2}
        },
        {
            {TX_SHIFT_0_TX, TX_SHIFT_0_RX, TX_SHIFT_0_CR1, TX_SHIFT_0_CR2},
            {TX_SHIFT_1_TX, TX_SHIFT_1_RX, TX_SHIFT_1_CR1, TX_SHIFT_1_CR2},
            {TX_SHIFT_2_TX, TX_SHIFT_2_RX, TX_SHIFT_2_CR1, TX_SHIFT_2_CR2}
        }
    };

    int regid = RfATTCtrlData[pdev->type][ATTId].shift_reg;
    int mask = RfATTCtrlData[pdev->type][ATTId].shift_reg_mask;
    bool is_reverse = RfATTCtrlData[pdev->type][ATTId].is_reverse;
    int shift_bit = RfATTCtrlData[pdev->type][ATTId].shift_start_bit;
    int SPIConfigure = 0x1800 | 32;
    int TXData = 0;
    if(pdev->testertype == HW_WT418)
    {
        SPIConfigure=SPIConfig_418VA[pdev->type][regid];
    }
    else
    {
        if(pdev->version >= VERSION_B)
        {
            SPIConfigure = SPIConfig[pdev->type][regid];
        }
        else
        {
            SPIConfigure = SPIConfig_VA[pdev->type][regid];
        }
    }
    dbg_print("wt_WriteATT type:%d attid:%d, ATTCode:%d, regid:%d, mask:%#x, is_reverse:%d, shift_bit:%d, !high_low:%d, SPIConfigure:%#x\n",
           pdev->type, ATTId, ATTCode, regid, mask, is_reverse, shift_bit,
           (pdev->type == DEV_TYPE_VSG && ATTId == RF_LO_MIX_ATT), SPIConfigure);

    if (mask == 0)  // 该ATT不存在
    {
        return WT_OK;
    }

    if (pdev->type == DEV_TYPE_VSG && ATTId == RF_LO_MIX_ATT && pdev->testertype != HW_WT418)
    {
        // bit高低位取反
        temp = 0;
        for (i = 0; i < 6; i++)
        {
            temp <<= 1;
            temp += ATTCode & 0x1;
            ATTCode >>= 1;
        }
        ATTCode = temp;
    }

    if (is_reverse != 0)
    {
        ATTCode = 63 - ATTCode;
    }
#if RF_SHF_TX_ADDR_READ
    TXData = wt_read_direct_reg(pdev, Reg[pdev->type][regid][SPI_REG_TX_TYPE]);
#else
    TXData = wt_read_direct_reg(pdev, Reg[pdev->type][regid][SPI_REG_RX_TYPE]);
#endif

    TXData = (TXData & (~mask)) | (ATTCode << shift_bit);
    dbg_print("wt_WriteATT Data:%#x \n", TXData);
    return wt_bp_spi_direct_for_write(SPIConfigure, TXData, Reg[pdev->type][regid], pdev);
}

int wt_ReadATT(struct dev_unit *pdev, int ATTId, int *ATTCode)
{
    int ret = WT_OK;
    int temp;
    int i = 0;

    const unsigned int Reg[2][3][4] = {
        {
            {RX_SHIFT_0_TX, RX_SHIFT_0_RX, RX_SHIFT_0_CR1, RX_SHIFT_0_CR2},
            {RX_SHIFT_1_TX, RX_SHIFT_1_RX, RX_SHIFT_1_CR1, RX_SHIFT_1_CR2},
            {RX_SHIFT_2_TX, RX_SHIFT_2_RX, RX_SHIFT_2_CR1, RX_SHIFT_2_CR2}
        },
        {
            {TX_SHIFT_0_TX, TX_SHIFT_0_RX, TX_SHIFT_0_CR1, TX_SHIFT_0_CR2},
            {TX_SHIFT_1_TX, TX_SHIFT_1_RX, TX_SHIFT_1_CR1, TX_SHIFT_1_CR2},
            {TX_SHIFT_2_TX, TX_SHIFT_2_RX, TX_SHIFT_2_CR1, TX_SHIFT_2_CR2}
        }
    };

    int regid = RfATTCtrlData[pdev->type][ATTId].shift_reg;
    int mask = RfATTCtrlData[pdev->type][ATTId].shift_reg_mask;
    bool is_reverse = RfATTCtrlData[pdev->type][ATTId].is_reverse;
    int shift_bit = RfATTCtrlData[pdev->type][ATTId].shift_start_bit;
    int AttRxAddr = 0;
    int Code = 0;

    if (mask == 0)  // 该ATT不存在
    {
        return WT_OK;
    }

#if RF_SHF_TX_ADDR_READ
    AttRxAddr = Reg[pdev->type][regid][SPI_REG_TX_TYPE];
#else
    AttRxAddr = Reg[pdev->type][regid][SPI_REG_RX_TYPE];
#endif

    Code = wt_read_direct_reg(pdev, AttRxAddr);
    *ATTCode = (Code & mask) >> shift_bit;

    if (pdev->type== DEV_TYPE_VSG && ATTId == RF_LO_MIX_ATT && pdev->testertype != HW_WT418)
    {
        // bit高低位取反
        temp = 0;
        for (i = 0; i < 6; i++)
        {
            temp <<= 1;
            temp += *ATTCode & 0x1;
            *ATTCode >>= 1;
        }
        *ATTCode = temp;
    }

    if (is_reverse != 0)
    {
        *ATTCode = 63 - (*ATTCode);
    }
    return ret;
}

int wt_ReadAllATT(struct dev_unit *pdev, int *AttArr)
{
    int att = 0;
    int regid = 0;
    int mask = 0;
    bool is_reverse = 0;
    int shift_bit = 0;
    int value = 0;
    int ATTCode = 0;

    for (att = 0; att < 4; ++att)
    {
        AttArr[att] = 0;

        regid = RfATTCtrlData[DEV_TYPE_VSG][att].shift_reg;
        mask = RfATTCtrlData[DEV_TYPE_VSG][att].shift_reg_mask;
        is_reverse = RfATTCtrlData[DEV_TYPE_VSG][att].is_reverse;
        shift_bit = RfATTCtrlData[DEV_TYPE_VSG][att].shift_start_bit;
        // printk("att=%d regid=%d mask=0x%X is_reverse=%d shift_bit=%d\n", att, regid, mask, is_reverse, shift_bit);
        if (mask == 0)
        {
            continue;
        }

        value = pdev->vsg_stat.vsg_shift_reg[regid];
        ATTCode = (value & mask) >> shift_bit;

        if (is_reverse != 0)
        {
            ATTCode = 63 - (ATTCode);
        }

        AttArr[att] = ATTCode;
    }

    return WT_OK;
}

int WT_WriteATT(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct ATTCodeType ATTCodeTemp;
    if (copy_from_user(&ATTCodeTemp, arg, DataLength))
    {
        dbg("WT_SetATTCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return wt_WriteATT(pdev, ATTCodeTemp.AttId, ATTCodeTemp.Code);
}

int WT_ReadATT(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct ATTCodeType ATTCodeTemp;

    if (copy_from_user(&ATTCodeTemp, arg, DataLength))
    {
        dbg("WT_GetATTCode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    ret = wt_ReadATT(pdev, ATTCodeTemp.AttId, &ATTCodeTemp.Code);
    retAssert(ret, "wt_ReadATT failed!\n");

    if (copy_to_user(arg, &ATTCodeTemp, DataLength))
    {
        dbg("WT_GetATTCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_GetXdmaStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    //获取VSG状态status信息
    int status;

    if (pdev->type == DEV_TYPE_VSG)
    {
        status = wt_read_direct_reg(pdev, VSG_STATUS) & 0xFF;

        if (status != 0x03) //未完成
        {
            status = WT_RX_TX_STATUS_RUNNING;
        }
        else //完成
        {
            status = WT_RX_TX_STATUS_DONE;
        }
    }
    else
    {
        status = wt_read_direct_reg(pdev, VSA_STATUS) & 0xFF;

        if (status == NEED_ORIGIN_DATA_TEMP_STATE) // 蜂窝 如果需要采集原始数据, VSA Single下每次会采两包, 第一包采集完毕状态为0x60
        {
            if (copy_to_user(arg, &status, DataLength))
            {
                dbg("WT_GetXdmaStatus info copy_to_user failed!\n");
                return WT_CPY_TO_USR_FAILED;
            }
            return WT_OK;
        }
        else if (status != 0x00) //未完成
        {
            status = WT_RX_TX_STATUS_RUNNING;
        }
        else //完成
        {
            status = WT_RX_TX_STATUS_DONE;
        }
    }

    if (copy_to_user(arg, &status, DataLength))
    {
        dbg("WT_GetXdmaStatus info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_BaseFpgaEarse(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int ReadCount = 0;

    //配置擦除地址
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_ADDR, BUSI_FPGA_FLASH_EARSE_TARG);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_ADDR, BUSI_FPGA_FLASH_EARSE_TARG);

    //启动flash擦除
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_EARSE, 0);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_EARSE, 0);
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_EARSE, 1);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_EARSE, 1);
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_EARSE, 0);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_EARSE, 0);
    dbg("Base Fpga Upgrade Mod%d Start Earse!", pdev->id);

    //查询，等待发送完成
    ReadCount = 0;
    do
    {
        msleep(1);
        if (ReadCount++ > FLASH_WAITE_COUNT)
        {
            dbg("ReadCount > FLASH_WAITE_COUNT!\n");
            return WT_FLASH_STATUS_CHECK_OVERTIME;
        }
    } while ((wt_read_direct_reg(pdev, BUSI_FPGA_FLASH_STATUS) & 0x0F) != 0x0);
    dbg("Base Fpga Upgrade Mod%d Earse Finish!", pdev->id);
    return ret;
}

int WT_BaseFpgaUpgrade(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct DataBufType DataBuf;
    char *pData = NULL;
    char pBuf[FLASH_PAGE_SIZE];
    unsigned int PageAddr = 0;
    int wLen = FLASH_PAGE_SIZE;
    int i = 0;
    int ReadCount = 0;
    unsigned int FlashStartAddr = BUSI_FPGA_FLASH_WRITE_START;

    dbg("Base Fpga Upgrade Mod%d!\n", pdev->id);
    if (copy_from_user(&DataBuf, arg, DataLength))      //从应用层 传入DataBuf
    {
        dbg("WT_BaseFpgaUpgrade info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    pData = DataBuf.pDataBuf;

    //一页页写flash 直到写完DataBuf.Len
    for (PageAddr = FlashStartAddr; PageAddr < (DataBuf.Len + FlashStartAddr); PageAddr += FLASH_PAGE_SIZE, pData += FLASH_PAGE_SIZE)       
    {
        wLen = ((PageAddr + FLASH_PAGE_SIZE) < DataBuf.Len) ? FLASH_PAGE_SIZE : (DataBuf.Len - PageAddr);   //判断这页需要写多少 赋值为wLen
        if (PageAddr < (FLASH_PAGE_SIZE * 2) || !(PageAddr & 0xFFFFF))
        {
            dbg("Base Fpga Upgrade Mod%d wLen = %d, pData = %p!", pdev->id, wLen, pData);
        }
        if (copy_from_user(pBuf, pData, wLen))      //拷贝wLen长度的缓冲区数据到pBuf
        {
            dbg("WT_BaseFpgaUpgrade info copy_from_user failed!\n");
            return WT_CPY_FROM_USR_FAILED;
        }
        if (wLen < FLASH_PAGE_SIZE)     //最后一页
        {
            dbg("Base Fpga Upgrade Mod%d memset addr = %p, Len = %d!", pdev->id, pBuf + wLen, FLASH_PAGE_SIZE - wLen);
            memset(pBuf + wLen, 0xFF, FLASH_PAGE_SIZE - wLen);
        }

        wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_ADDR, PageAddr);
#if 1
        for (i = 0; i < FLASH_PAGE_SIZE; i += sizeof(int))      //一次写入 1 int 长度
        {
            if (PageAddr < (FLASH_PAGE_SIZE * 2))
            {
                dbg("Base Fpga Upgrade Mod%d write DATA = %#x!", pdev->id, *(int *)(pBuf + i));
            }
            wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_DATA, *(int *)(pBuf + i));
        }
#else
        for (i = 0; i < FLASH_PAGE_SIZE; i ++)
        {
            if (PageAddr < (FLASH_PAGE_SIZE * 2))
            {
                dbg("Base Fpga Upgrade Mod%d write DATA = %#x!", pdev->id, pBuf[i]);
            }
            wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_DATA, pBuf[i]);
        }
#endif
        //启动flash写
        wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
        wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
        wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 1);
        wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 1);
        wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
        wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
        if (PageAddr < (FLASH_PAGE_SIZE * 2) || !(PageAddr & 0xFFFFF))
        {
            dbg("Base Fpga Upgrade Mod%d Write Addr %#x!", pdev->id, PageAddr);
        }

        //查询，等待发送完成
        ReadCount = 0;
        do
        {
            udelay(10);
            if (ReadCount++ > FLASH_WAITE_COUNT)
            {
                dbg("ReadCount > FLASH_WAITE_COUNT!\n");
                return WT_FLASH_STATUS_CHECK_OVERTIME;
            }
        } while ((wt_read_direct_reg(pdev, BUSI_FPGA_FLASH_STATUS) & 0x0F) != 0x0);
    }

    return ret;
}

int WT_BaseFpgaWriteOnePage(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct DataBufType DataBuf;
    char *pData = NULL;
    char pBuf[FLASH_PAGE_SIZE];
    unsigned int PageAddr = 0;
    int wLen = FLASH_PAGE_SIZE; //这次要写的长度
    int i = 0;
    int ReadCount = 0;

    if (copy_from_user(&DataBuf, arg, DataLength)) //从应用层 传入DataBuf
    {
        dbg("WT_BaseFpgaUpgrade info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    pData = DataBuf.pDataBuf;
    PageAddr = DataBuf.pFlashAddr;
    wLen = DataBuf.Len;

    //写一页flash
    if (PageAddr < (FLASH_PAGE_SIZE * 2) || !(PageAddr & 0xFFFFF))
    {
        dbg("Base Fpga Upgrade Mod%d wLen = %d, PageAddr = %d, pData = %p!", pdev->id, wLen, PageAddr, pData);
    }
    if (copy_from_user(pBuf, pData, wLen)) //拷贝wLen长度的缓冲区数据到pBuf
    {
        dbg("WT_BaseFpgaUpgrade info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if (wLen < FLASH_PAGE_SIZE) //对应最后一页未满 补0xFF
    {
        dbg("Base Fpga Upgrade Mod%d memset addr = %p, Len = %d!\n", pdev->id, pBuf + wLen, FLASH_PAGE_SIZE - wLen);
        memset(pBuf + wLen, 0xFF, FLASH_PAGE_SIZE - wLen);
    }

    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_ADDR, PageAddr + BUSI_FPGA_FLASH_WRITE_START); //配置擦除地址
#if 1
    for (i = 0; i < FLASH_PAGE_SIZE; i += sizeof(int)) //一次写入 1 int 长度
    {
        if (PageAddr < (FLASH_PAGE_SIZE * 2))
        {
            dbg("Base Fpga Upgrade Mod%d write DATA = %#x!", pdev->id, *(int *)(pBuf + i));
        }
        if (wLen < FLASH_PAGE_SIZE)
        {
            dbg("Base Fpga Upgrade Mod%d Last Page write DATA = %#x!", pdev->id, *(int *)(pBuf + i));
        }
        wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_DATA, *(int *)(pBuf + i));
    }
#else
    for (i = 0; i < FLASH_PAGE_SIZE; i++)
    {
        if (PageAddr < (FLASH_PAGE_SIZE * 2))
        {
            dbg("Base Fpga Upgrade Mod%d write DATA = %#x!", pdev->id, pBuf[i]);
        }
        wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_DATA, pBuf[i]);
    }
#endif
    //启动flash写
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 1);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 1);
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_WRITE, 0);
    if (PageAddr < (FLASH_PAGE_SIZE * 2) || !(PageAddr & 0xFFFFF))
    {
        dbg("Base Fpga Upgrade Mod%d Write Addr %#x!", pdev->id, PageAddr);
    }

    //查询，等待发送完成
    ReadCount = 0;
    do
    {
        udelay(10);
        if (ReadCount++ > FLASH_WAITE_COUNT)
        {
            dbg("ReadCount > FLASH_WAITE_COUNT!\n");
            return WT_FLASH_STATUS_CHECK_OVERTIME;
        }
    } while ((wt_read_direct_reg(pdev, BUSI_FPGA_FLASH_STATUS) & 0x0F) != 0x0);

    return ret;
}

int WT_FPGAReload(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    wt_write_direct_reg(pdev, BUSI_FPGA_FINISH_RESET, 0);
    wt_write_direct_reg(pdev, BUSI_FPGA_FINISH_RESET, 1);
    wt_write_direct_reg(pdev, BUSI_FPGA_FINISH_RESET, 0);
    dbg("Base Fpga Upgrade Mod%d FPGAReload Write Addr Finish!\n", pdev->id);
    return ret;
}

int WT_SetFlash4ByteMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_MODE, 0);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_MODE, 0);
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_MODE, 1);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_MODE, 1);
    wt_write_direct_reg(pdev, BUSI_FPGA_FLASH_MODE, 0);
    wt_check_direct_reg(pdev, BUSI_FPGA_FLASH_MODE, 0);
    dbg("Base Fpga Upgrade Mod%d SetFlash4ByteMode Write Addr Finish!\n", pdev->id);
    return ret;
}

int WT_SetATTCalConfig(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct ATTCalConfigType Config;
    if (copy_from_user(&Config, arg, DataLength)) //从应用层 传入DataBuf
    {
        dbg("WT_SetATTCalConfig info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (Config.Enable)
    {
        wt_write_direct_reg(pdev, VSG_SIN1M_REG, 2);
    }
    else
    {
        wt_write_direct_reg(pdev, VSG_SIN1M_REG, 0);
    }
    return WT_OK;
}

//*******************VSA单元类型*******************
int WT_VSAStart(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;
    pdev->complete = -1;
    atomic_set(&pdev->vsadonecnt, 0);

    dbg_print("vsa %d start\n", pdev->id);

    // RegData = wt_read_direct_reg(pdev, BUSI_VSA_VSG_STOP);
    // ClearBit(RegData, VSA_STOP_BIT);
    // wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    // SetBit(RegData, VSA_STOP_BIT);
    // wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    // ClearBit(RegData, VSA_STOP_BIT);
    // wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);

    //使能通道
    wt_write_direct_reg(pdev, VSA_CHANNEL_EN, 1);

    //设置开启VSA
    RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG);
    ClearBit(RegData, VSA_TRIG_READY_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG, RegData);
    SetBit(RegData, VSA_TRIG_READY_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG, RegData);
    ClearBit(RegData, VSA_TRIG_READY_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG, RegData);

    return WT_OK;
}

int WT_VSAStop(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;

    atomic_set(&pdev->vsadonecnt, 0);
    dbg_print("vsa %d stop\n", pdev->id);
    cur_wlan_mode[pdev->type][pdev->number] = WT_WORKMODE_SISO;
    RegData = wt_read_direct_reg(pdev, BUSI_VSA_VSG_STOP);
    ClearBit(RegData, VSA_STOP_BIT);
    wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    SetBit(RegData, VSA_STOP_BIT);
    wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    ClearBit(RegData, VSA_STOP_BIT);
    wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);

    RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG);
    ClearBit(RegData, VSA_SPI_CFG_RESET_DMA_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG, RegData);
    SetBit(RegData, VSA_SPI_CFG_RESET_DMA_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG, RegData);
    ClearBit(RegData, VSA_SPI_CFG_RESET_DMA_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG, RegData);

    return WT_OK;
}



int WT_VSAGetStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    //获取VSA状态status信息
    int status = wt_read_direct_reg(pdev, VSA_STATUS);

    if (GetBit(status, 5) && GetBit(status, 6))
    {
        status = WT_RX_TX_STATUS_DONE;
    }
    else
    {
        status = WT_RX_TX_STATUS_RUNNING;
    }

    if (copy_to_user(arg, &status, DataLength))
    {
        dbg("WT_VSAGetStatus info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//数字TRIGGER
int WT_SetRXTrigLevelDigital(int DataLength, void *arg, struct dev_unit *pdev)
{
    int TrigGate;

    if (copy_from_user(&TrigGate, arg, DataLength))
    {
        dbg("WT_SetRXTrigLevelDigital info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    //TODO:FPGA Trig配置重构
    // //设置RX数字触发的触发电平
    // wt_write_direct_reg(pdev, VSA_TRIGGER_GATE, TrigGate);

    return WT_OK;
}

int WT_SetIqImbConfig(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct IqImbConfigType IqImbConfig;

    if (copy_from_user(&IqImbConfig, arg, DataLength))
    {
        dbg("WT_SetIqImbConfig info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    // wt_write_direct_reg(pdev, VSA_IQ_IMB_COMP_PHASE_A, IqImbConfig.PhaseA);
    // wt_write_direct_reg(pdev, VSA_IQ_IMB_COMP_PHASE_B, IqImbConfig.PhaseB);
    // wt_write_direct_reg(pdev, VSA_IQ_IMB_COMP_AMP, IqImbConfig.AmpComp);
    return WT_OK;
}

int WT_GetAttCalResult(int DataLength, void *arg, struct dev_unit *pdev)
{
    int IQCode = 0;
    IQCode = wt_read_direct_reg(pdev, ATT_CAL_IQ_CODE);
    wt_write_direct_reg(pdev, ATT_CAL_VSA_RESET, 0x1000);
    wt_write_direct_reg(pdev, ATT_CAL_VSA_RESET, 0);

    if (copy_to_user(arg, &IQCode, DataLength))
    {
        dbg("WT_GetAttCalResult info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_VSASetCwFlag(int DataLength, void *arg, struct dev_unit *pdev)
{
    bool CwFlag = false;
    int RegData;
    if (copy_from_user(&CwFlag, arg, DataLength)) //从应用层 传入DataBuf
    {
        dbg("WT_VSASetCwFlag info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    RegData = wt_read_direct_reg(pdev, VSA_CAPTURE_POWER_SIGNAL);
    if (CwFlag == true)
    {
        wt_write_direct_reg(pdev, VSA_CAPTURE_POWER_SIGNAL, SetBit(RegData, 2));
        dbg_print("WT_VSASetCwFlag true!\n");
    }
    else
    {
        wt_write_direct_reg(pdev, VSA_CAPTURE_POWER_SIGNAL, ClearBit(RegData, 2));
        dbg_print("WT_VSASetCwFlag false!\n");
    }

    return WT_OK;
}

int WT_GetFpgaCapturePower(int DataLength, void *arg, struct dev_unit *pdev)
{
    unsigned long long Data = 0;
    unsigned long long Data1 = 0;
    int tmp = 0;
    int RegData;
    if (pdev->testertype == HW_WT418 /* || pdev->version >= VERSION_B */)
    {
        tmp |= wt_read_direct_reg(pdev, VSA_CAPTURE_POWER_H32);
        //printk("WT_GetFpgaCapturePower FC Data=%#x\n", tmp);
        Data = (unsigned int)tmp;
        Data = Data << 23;
        tmp = wt_read_direct_reg(pdev, VSA_CAPTURE_POWER_L32);
        //printk("WT_GetFpgaCapturePower FB Data=%#x\n", tmp);
        Data1 = (unsigned int)tmp;
        Data |= ((Data1) >> 9);

       RegData = wt_read_direct_reg(pdev, VSA_CAPTURE_POWER_SIGNAL);
       wt_write_direct_reg(pdev, VSA_CAPTURE_POWER_SIGNAL, SetBit(RegData, 0));
       udelay(1);
       wt_write_direct_reg(pdev, VSA_CAPTURE_POWER_SIGNAL, ClearBit(RegData, 0));

        dbg_print("WT_GetFpgaCapturePower Data=%#llx\n", Data);
    }

    if (copy_to_user(arg, &Data, DataLength))
    {
        dbg("WT_GetFpgaCapturePower copy_to_user failed!");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_SetTBTApMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_VSASetTBTStaParam(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

//*******************VSG单元类型*******************

int WT_VSGStart(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Mode = 0;
    int RegData = 0;

    atomic_set(&pdev->vsadonecnt, 0);
    if (copy_from_user(&Mode, arg, DataLength))
    {
        dbg("WT_VSGStart info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    pdev->complete = -1;
    dbg_print("vsg %d start\n", pdev->id);

    if(Mode == WT_START_MODE_NORMAL) //暂时只管常规模式
    {
        RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG);
        ClearBit(RegData, VSG_TRIG_READY_BIT);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG, RegData);
        SetBit(RegData, VSG_TRIG_READY_BIT);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG, RegData);
        ClearBit(RegData, VSG_TRIG_READY_BIT);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG, RegData);
    }
    return WT_OK;
}

int WT_VSGSSetListCellMod(int DataLength, void *arg, struct dev_unit *pdev)
{
    int CellMode = 0;

    if (copy_from_user(&CellMode, arg, DataLength))
    {
        dbg("WT_VSGSSetListCellMod info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    pdev->complete = -1;
    dbg_print("vsg %d WT_VSGSSetListCellMod, CellMod %d\n", pdev->id, CellMode);

    wt_write_direct_reg(pdev, BUSI_LISTMODE_LIST_VSG_CELL_MOD, CellMode);

    return WT_OK;
}

int WT_VSGStop(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;
    int Mode = STOP_MODE_NORMAL;

    atomic_set(&pdev->vsadonecnt, 0);
    if (copy_from_user(&Mode, arg, DataLength))
    {
        dbg("WT_VSGStop info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    dbg_print("vsg %d stop\n", pdev->id);

    if (Mode == STOP_MODE_NORMAL)
    {
        cur_wlan_mode[pdev->type][pdev->number] = WT_WORKMODE_SISO;
        wt_ClearTBTStaMode(pdev);
    }

    RegData = wt_read_direct_reg(pdev, BUSI_VSA_VSG_STOP);
    ClearBit(RegData, VSG_STOP_BIT);
    wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    SetBit(RegData, VSG_STOP_BIT);
    wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    ClearBit(RegData, VSG_STOP_BIT);
    wt_write_direct_reg(pdev, BUSI_VSA_VSG_STOP, RegData);
    return WT_OK;
}

int WT_VSGGetStatus(int DataLength, void *arg, struct dev_unit *pdev)
{
    //获取VSG状态status信息
    int status = wt_read_direct_reg(pdev, VSG_STATUS);
    status = status & 0xFF;
    if (status) //非0：未完成
    {
        status = WT_RX_TX_STATUS_RUNNING;
    }
    else //0：完成
    {
        status = WT_RX_TX_STATUS_DONE;
    }

    if (copy_to_user(arg, &status, DataLength))
    {
        dbg("WT_VSGGetStatus info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_VSGSetPNHead(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct PnItemHead PnItemHeadTemp;

    if (copy_from_user(&PnItemHeadTemp, arg, DataLength))
    {
        dbg("WT_VSGSetPNHead info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    //写PnItem头部信息寄存器
    wt_write_direct_reg(pdev, PN_TX_INDEX, PnItemHeadTemp.StartIdx);
    wt_write_direct_reg(pdev, PN_TX_TOTAL, PnItemHeadTemp.SendCnt);

    return WT_OK;
}

int WT_VSGGetPNHead(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct PnItemHead PnItemHeadTemp;

    //读PnItem头部信息寄存器
    PnItemHeadTemp.StartIdx = wt_read_direct_reg(pdev, PN_TX_INDEX);
    PnItemHeadTemp.SendCnt = wt_read_direct_reg(pdev, PN_TX_TOTAL);

    if (copy_to_user(arg, &PnItemHeadTemp, DataLength))
    {
        dbg("WT_VSGGetPNHead info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

int WT_SetExtMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct ExtModeType ExtMode;
    if (copy_from_user(&ExtMode, arg, DataLength))
    {
        dbg("WT_SetExtMode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    pdev->ext_mode = ExtMode.Mode;

    wt_write_direct_reg(pdev, VSG_EXT_MODE, ExtMode.Mode);
    dbg_print("WT_SetExtMode ModId%d: Mode=%d\n", pdev->id, ExtMode.Mode);
    switch (ExtMode.Mode)
    {
    case WT_VSG_MODE_DEVM:
        wt_write_direct_reg(pdev, DEVM_MODE_T1, ExtMode.Param.Devm.LeadTime);
        wt_write_direct_reg(pdev, DEVM_MODE_T2, ExtMode.Param.Devm.DelayTime);
        wt_write_direct_reg(pdev, DEVM_MODE_GAP, ExtMode.Param.Devm.GapTime);
        wt_write_direct_reg(pdev, DEVM_MODE_DELAY, ExtMode.Param.Devm.DebugDelay);
        wt_write_direct_reg(pBPdev, BACK_TRIG_SELELT, TRIGGER_PORT_DEVM);
        dbg_print("Devm leadtime=%d, delaytime=%d, gaptime=%d, DebugDelay=%d\n",
               ExtMode.Param.Devm.LeadTime, ExtMode.Param.Devm.DelayTime,
               ExtMode.Param.Devm.GapTime, ExtMode.Param.Devm.DebugDelay);
        break;
    case WT_VSG_MODE_VSA_VSG:
    case WT_VSG_MODE_VSG_VSA:
        wt_write_direct_reg(pdev, TBT_MODE_RESET, 1);
        wt_check_direct_reg(pdev, TBT_MODE_RESET, 1);
        wt_write_direct_reg(pdev, TBT_MODE_RESET, 0);
        break;
    default:
        break;
    }
    return WT_OK;
}

int WT_VSGSetGapPowerEnable(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct VsgIfgCtrlType IfgCtrl;
    if (copy_from_user(&IfgCtrl, arg, DataLength))
    {
        dbg("WT_VSGSetGapPowerEnable info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    dbg_print("WT_VSGSetGapPowerEnable Param = %#x, %#x, %#x\n",
        IfgCtrl.Param1, IfgCtrl.Param2, IfgCtrl.Param3);
    //wt_write_direct_reg(pdev, GAP_POWER_CTL_ENABLE, IfgCtrl.Status ? 1 : 0);
    wt_write_direct_reg(pdev, GAP_POWER_CTL_DELAY1, IfgCtrl.Param1);
    wt_write_direct_reg(pdev, GAP_POWER_CTL_DELAY2, IfgCtrl.Param2);
    wt_write_direct_reg(pdev, GAP_POWER_CTL_DELAY3, IfgCtrl.Param3);
    return WT_OK;
}

int WT_VSGStartTBTMimo(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int wt_ClearTBTStaMode(struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_ClearTBTStaMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    return wt_ClearTBTStaMode(pdev);
}

int WT_VSGSetTBTStaParam(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_SetLOComMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    long long ShiftData;
    int Data;
    if(pdev->testertype == HW_WT418)
    {
        return WT_SetLOComMode_WT418va(DataLength, arg, pdev);
    }
    if (!(pdev->version >= VERSION_C))
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        retAssert(Ret, "WT_SetLOComMode ERORR Base Board Version donnot support LO common mode");
    }
    if (copy_from_user(&Data, arg, DataLength))
    {
        dbg("WT_SetLOComMode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    Ret = wt_ReadLoBoardShitfCode(pdev, &ShiftData);
    retAssert(Ret, "WT_SetLOComMode wt_ReadLoBoardShitfCode failed!");
    if (pdev->type == DEV_TYPE_VSA)
    {
        if (Data)
        {
            SetBit(ShiftData, LO_SHIFT_VC_RX_SWR_COM_CTL);
        }
        else
        {
            ClearBit(ShiftData, LO_SHIFT_VC_RX_SWR_COM_CTL);
        }
    }
    else
    {
        if (Data)
        {
            SetBit(ShiftData, LO_SHIFT_VC_TX_SWT_COM_CTL);
        }
        else
        {
            ClearBit(ShiftData, LO_SHIFT_VC_TX_SWT_COM_CTL);
        }
    }

    Ret = wt_WriteLoBoardShitfCode(pdev, ShiftData);
    retAssert(Ret, "WT_SetLOComMode wt_WriteLoBoardShitfCode failed!");
    return WT_OK;
}

int WT_SetLOComMode_WT418va(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    int TXData = 0;
    int SPIConfig = 0x1818;
    unsigned int Reg[4];
    int Status = 0;

    do
    {
        if (copy_from_user(&Status, arg, DataLength))
        {
            dbg("WT_SetLOComMode info copy_from_user failed!\n");
            return WT_CPY_FROM_USR_FAILED;
        }

        Reg[SPI_REG_TX_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_CR2;

        TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
        if (pdev->type == DEV_TYPE_VSA)
        {
            if (Status)
            {
                ClearBit(TXData, WT418_VA_TX_SW5LOT_CTL);
                ClearBit(TXData, WT418_VA_TX_SW4LOT_CTL);
            }
            else if(!Status)
            {
                SetBit(TXData, WT418_VA_TX_SW5LOT_CTL);
                SetBit(TXData, WT418_VA_TX_SW4LOT_CTL);
            }
        }
        else
        {
            if (Status)
            {
                ClearBit(TXData, WT418_VA_TX_SW7LOT_CTL);
                ClearBit(TXData, WT418_VA_TX_SW6LOT_CTL);
            }
            else if(!Status)
            {
                SetBit(TXData, WT418_VA_TX_SW7LOT_CTL);
                SetBit(TXData, WT418_VA_TX_SW6LOT_CTL);
            }
        }
        Ret = wt_bp_spi_direct_for_write(SPIConfig, TXData, Reg, pdev);
        retBreak(Ret, "WT_SetLOComMode wt_bp_spi_direct_for_write failed!");

    } while (0);

    return Ret;
}

int WT_GetLOComMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    long long ShiftData;
    int Data;
    if(pdev->testertype == HW_WT418)
    {
        return WT_GetLOComMode_WT418va(DataLength, arg, pdev);
    }
    Ret = wt_ReadLoBoardShitfCode(pdev, &ShiftData);
    retAssert(Ret, "WT_GetLOComMode wt_ReadLoBoardShitfCode failed!");

    if (pdev->type == DEV_TYPE_VSA)
    {
        Data = GetBit(ShiftData, LO_SHIFT_VC_RX_SWR_COM_CTL);
    }
    else
    {
        Data = GetBit(ShiftData, LO_SHIFT_VC_TX_SWT_COM_CTL);
    }

    if (copy_to_user(arg, &Data, DataLength))
    {
        dbg("WT_VSGGetStatus info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_GetLOComMode_WT418va(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int TXData = 0;
    unsigned int Reg[4];
    int Status = 0;

    do
    {
        Reg[SPI_REG_TX_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_TX;
        Reg[SPI_REG_RX_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_RX;
        Reg[SPI_REG_CTRL1_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = TX_SHIFT_1_TX + BUSI_DEVM_SPI_CR2;

        TXData = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);

        if (pdev->type == DEV_TYPE_VSA)
        {
            Status = !GetBit(TXData, WT418_VA_TX_SW4LOT_CTL);
        }
        else
        {
            Status = !GetBit(TXData, WT418_VA_TX_SW6LOT_CTL);
        }
        if (copy_to_user(arg, &Status, DataLength))
        {
            dbg("WT_VSGGetStatus info copy_to_user failed!\n");
            return WT_CPY_TO_USR_FAILED;
        }
    } while (0);

    return ret;
}


int WT_WriteIQ_Switch(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    int Addr = BASE_BAND_SWITCH; //模拟IQ信号内/外链路切换开关
    //int ShiftData;
    int Data;
    //模拟IQ基带板版本号
    if (!(pdev->testertype == HW_WT448 && pdev->version >= VERSION_B))
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        retAssert(Ret, "WT_WriteIQ_Switch ERORR Base Board Version don't support Analog IQ");
    }

    if (copy_from_user(&Data, arg, DataLength))
    {
        dbg("WT_WriteIQ_Switch info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    wt_write_direct_reg(pdev, Addr, Data ? 0x3 : 0);

    // //读寄存器组数据
    // ShiftData = wt_read_direct_reg(pdev, Addr);

    // if ((bool)Data)
    // {
    //     if((ShiftData & 0x3u) != 3)
    //     {
    //         ShiftData = 3;
    //         wt_write_direct_reg(pdev, Addr, ShiftData);
    //     }
    // }
    // else
    // {
    //     if((ShiftData & 0x3u) != 0)
    //     {
    //         ShiftData = 0;
    //         wt_write_direct_reg(pdev, Addr, ShiftData);
    //     }
    // }

    return WT_OK;
}

int WT_ReadIQ_Switch(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    int Addr = BASE_BAND_SWITCH; // 模拟IQ信号内/外链路切换开关
    int ShiftData;
    int Data;

    // 模拟IQ基带板版本号
    if (!(pdev->testertype == HW_WT448 && pdev->version >= VERSION_B))
    {
        Ret = WT_ARG_UNKNOW_PARAMETER;
        retAssert(Ret, "WT_WriteIQ_Switch ERORR Base Board Version don't support Analog IQ");
    }

    // 读寄存器组数据
    ShiftData = wt_read_direct_reg(pdev, Addr);

    if ((ShiftData & 0x3u) == 3)
    {
        Data = true;
    }
    else if ((ShiftData & 0x3u) == 0)
    {
        Data = false;
    }
    else
    {
        // 判断超出范围
        Ret = WT_REG_ARGS_ERROR;
        retAssert(Ret, "WT_WriteIQ_Switch ERORR ShiftData isn't neither AnalogIQMode nor RfIQMode.");
    }

    if (copy_to_user(arg, &Data, DataLength))
    {
        dbg("WT_ReadIQ_Switch info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int WT_Set_MixLo_Switch(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    int MixSwitch;
    long long Data;

    if (copy_from_user(&MixSwitch, arg, DataLength))
    {
        dbg("WT_Set_MixLo_Switch info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Ret = wt_ReadLoBoardShitfCode(pdev, &Data);
    retAssert(Ret, "wt_ReadLoBoardShitfCode failed!");

    if (MixSwitch == MIX_LO_SWITCH_HIGHT)
    {
        if (pdev->type == DEV_TYPE_VSA)
        {
            SetBit(Data, LO_SHIFT_VB_RX_MIX_SW1_2_CTL);
        }
        else
        {
            ClearBit(Data, LO_SHIFT_VB_TX_MIX_SW1_2_CTL);
        }
    }
    else
    {
        if (pdev->type == DEV_TYPE_VSA)
        {
            ClearBit(Data, LO_SHIFT_VB_RX_MIX_SW1_2_CTL);
        }
        else
        {
            SetBit(Data, LO_SHIFT_VB_TX_MIX_SW1_2_CTL);
        }
    }
    Ret = wt_WriteLoBoardShitfCode(pdev, Data);
    retAssert(Ret, "wt_WriteLoBoardShitfCode failed!");

    return WT_OK;
}

int WT_SetListMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Status;
    if (copy_from_user(&Status, arg, DataLength))
    {
        dbg("WT_SetListMode info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if (Status == 0)
    {
        SetVirtualAddrMode(&(pdev->dev_virtual_addr), false);
        SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), false);
    }
    dbg_print("WT_SetListMode type=%d ModId%d: Mode=%d\n", pdev->type, pdev->id, Status);
    return WT_OK;
}

int WT_InitVirtualAddr(int DataLength, void *arg, struct dev_unit *pdev)
{
    dbg_print("=======WT_InitVirtualAddr Start=======\n");
    if (pdev->type == DEV_TYPE_VSG)
    {
        InitVirtualAddr(&(pdev->dev_virtual_addr));
        Move_IORead_To_VirtualAddr(pBPdev, 0x0014 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x0015 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x001a << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x0020 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x0024 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, WT418_SW_PAC_CTL3);
        Move_IORead_To_VirtualAddr(pBPdev, WT418_SW_LOOP_CTL5);
        Move_IORead_To_VirtualAddr(pdev, BUSI_VSG_GAP_POWER_MODE);
        Move_IORead_To_VirtualAddr(pdev, BUSI_LED_SHIFT_CLT);
        Move_IORead_To_VirtualAddr(pdev, TX_SHIFT_0_TX);
        Move_IORead_To_VirtualAddr(pdev, TX_SHIFT_1_TX);
    }
    else
    {
        InitVirtualAddr(&(pdev->dev_virtual_addr));
        Move_IORead_To_VirtualAddr(pBPdev, 0x0014 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x0015 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x001a << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x0020 << 2);
        Move_IORead_To_VirtualAddr(pBPdev, 0x0024 << 2);
        Move_IORead_To_VirtualAddr(pdev, BUSI_LED_SHIFT_CLT);
        Move_IORead_To_VirtualAddr(pdev, RX_SHIFT_0_TX);
        Move_IORead_To_VirtualAddr(pdev, RX_SHIFT_1_TX);
    }
    dbg_print("=======WT_InitVirtualAddr End=======\n");
    return WT_OK;
}

int WT_SetVirtualAddrRecord(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Port;
    if (copy_from_user(&(Port), arg, sizeof(Port)))
    {
        dbg("WT_SetVirtualAddrRecord info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    SetVirtualAddrSwPort(&(pdev->dev_virtual_addr), Port);
    SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), true);
    SetVirtualAddrMode(&(pdev->dev_virtual_addr), true);
    dbg_print("WT_SetVirtualAddrMode true\n");
    return WT_OK;
}

int WT_SetVirtualAddrConfig(int DataLength, void *arg, struct dev_unit *pdev)
{
//     int RegData;
//     int ConfigCount;
//     int i = 0;
    struct Seq_Seg_ParamType SeqSegParam;
//     SetVirtualAddrMode(&(pdev->dev_virtual_addr), false);
//     SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), false);
//     printk("WT_SetVirtualAddrMode false\n");
//     printk("=======WT_SetVirtualAddrConfig Start=======\n");
    if (copy_from_user(&(SeqSegParam), arg, sizeof(SeqSegParam)))
    {
        dbg("WT_SetVirtualAddrConfig info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
//     if (pdev->type == DEV_TYPE_VSG)
//     {
//         // 将需要写入的配置写到FPGA指定地址。
//         RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, TX_SHIFT_0_TX);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_TX_RF_SHIFT0_LIST, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_TX_RF_SHIFT0_LIST, RegData);
//         RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, TX_SHIFT_1_TX);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_TX_RF_SHIFT1_LIST, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_TX_RF_SHIFT1_LIST, RegData);
//         RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0014 << 2);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr(0x%x):0x%x, Value:0x%x!\n", pdev->type, pdev->id, 0x0014 << 2, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG, RegData);
//         RegData = GetVirtualSwitchMask(pdev->type, SeqSegParam.SegmentPort, 0);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_MASK_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_MASK_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0015 << 2);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr(0x%x):0x%x, Value:0x%x!\n", pdev->type, pdev->id, 0x0015 << 2, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG, RegData);
//         RegData = GetVirtualSwitchMask(pdev->type, SeqSegParam.SegmentPort, 1);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_MASK_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_MASK_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x001a << 2);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr(0x%x):0x%x, Value:0x%x!\n", pdev->type, pdev->id, 0x001a << 2, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG, RegData);
//         RegData = GetVirtualSwitchMask(pdev->type, SeqSegParam.SegmentPort, 2);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_MASK_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_MASK_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0020 << 2);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr(0x%x):0x%x, Value:0x%x!\n", pdev->type, pdev->id, 0x0020 << 2, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG, RegData);
//         RegData = GetVirtualSwitchMask(pdev->type, SeqSegParam.SegmentPort, 3);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_MASK_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_MASK_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0024 << 2);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr(0x%x):0x%x, Value:0x%x!\n", pdev->type, pdev->id, 0x0024 << 2, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSG, RegData);
//         RegData = GetVirtualSwitchMask(pdev->type, SeqSegParam.SegmentPort, 4);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_MASK_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_MASK_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, WT418_SW_PAC_CTL3);
//         RegData |= wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, WT418_SW_LOOP_CTL5) << 8;
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_O_T_CTL5_CTL3_LIST, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_O_T_CTL5_CTL3_LIST, RegData);
//         RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, BUSI_VSG_GAP_POWER_MODE);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_GAP_POWER_MODE_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_GAP_POWER_MODE_LIST_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, VSG_RESAMPLE);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_REAMPLE_SEL_LIST_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_REAMPLE_SEL_LIST_VSG, RegData);
//         RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, BUSI_LISTMODE_FIRST_SEGMENT_SYNC_MODE_VSG);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_SYNC_MODE_VSG, RegData);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SEGMENT_SYNC_MODE_VSG, RegData);

//         ConfigCount = wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr);
//         for (i = 0; i < ConfigCount; i++)
//         {
//             // bit0_15:data, bit16_22:addr, bit23:0w,1r
//             RegData = wt_read_LMX2594_Cfg_List(pdev->dev_virtual_addr, i);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_TX_2594_TX_LIST, RegData);
//             dbg("Write ListMode List: LMX2594ConfigList Regaddr= 0x%x, data= 0x%x", ((RegData >> 16) & 0x7f), (RegData & 0xffff));
//         }
//         // 清空833 与 2594的ListMode配置
//         wt_set_LMX2594_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_TX_2594_CFG_NUM_LIST, ConfigCount);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!", pdev->type, pdev->id, BUSI_LISTMODE_TX_2594_CFG_NUM_LIST, ConfigCount);
//         ConfigCount = wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr);
//         for (i = 0; i < ConfigCount; i++)
//         {
//             // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
//             RegData = wt_read_HMC833_Cfg_List(pdev->dev_virtual_addr, i);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_TX_833_TX_LIST, RegData);
//             dbg("Write ListMode List: HMC833Config Regaddr= 0x%x, data= 0x%x", ((RegData >> 25) & 0x3F), ((RegData >> 1) & 0xffffff));
//         }
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_TX_833_CFG_NUM_LIST, ConfigCount);
//         // 清空833 与 2594的ListMode配置
//         wt_set_HMC833_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);
//         printk("Write ListMode List: Type%d, ModId%d, Adrr:0x%x, Value:0x%x!", pdev->type, pdev->id, BUSI_LISTMODE_TX_833_CFG_NUM_LIST, ConfigCount);
//         ConfigCount = wt_get_AdcOrDac_Cfg_List_Count(pdev->dev_virtual_addr);
//         for (i = 0; i < ConfigCount; i++)
//         {
//             // bit0_7:data, bit8_22:addr, bit23:0w,1r
//             RegData =  wt_read_AdcOrDac_Cfg_List(pdev->dev_virtual_addr, i);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_DAC_9142_TX_LIST, RegData);
//             dbg("Write ListMode List: AdcOrDacConfig Regaddr= 0x%x, data= 0x%x", ((RegData >> 8) & 0x7fff), (RegData & 0xff));
//         }
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_DAC_9142_CFG_NUM_LIST, ConfigCount);
//         printk("write ListMode List: List wt_write_direct_reg Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_DAC_9142_CFG_NUM_LIST, ConfigCount);
//         wt_set_AdcOrDac_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);
//         // 使能当前Segment的配置。
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_INDEX_VSG, SeqSegParam.SegmentIndex);
//         printk("write ListMode List: List wt_write_direct_reg Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_LISTMODE_SEGMENT_INDEX_VSG, ConfigCount);
//     }
//     else
//     {
// #define PRINTK_LIST_SEGENT(ty, id, idx, key, data) printk("WT_SetVirtualAddrConfig Type%d, ModId%d idx=%d, " #key " Addr:0x00%x/Addr:0x00%x, Value:0x%x!\n", ty, id, idx, key, key >> 2, data)
//         if (SeqSegParam.SegmentIndex > 0)
//         {
// #define SEG_PORT (SeqSegParam.SegmentPort)
//             // 将需要写入的配置写到FPGA指定地址。
//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, RX_SHIFT_0_TX);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_RX_RF_SHIFT0_LIST, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_RX_RF_SHIFT0_LIST, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, RX_SHIFT_1_TX);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_RX_RF_SHIFT1_LIST, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_RX_RF_SHIFT1_LIST, RegData);

//             RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0014 << 2);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_VSA, RegData);

//             RegData = GetVirtualSwitchMask(pdev->type, SEG_PORT, 0);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_MASK_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT0_0_TX_LIST_MASK_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0015 << 2);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_VSA, RegData);

//             RegData = GetVirtualSwitchMask(pdev->type, SEG_PORT, 1);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_MASK_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT0_1_TX_LIST_MASK_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x001a << 2);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_VSA, RegData);

//             RegData = GetVirtualSwitchMask(pdev->type, SEG_PORT, 2);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_MASK_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT1_TX_LIST_MASK_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0020 << 2);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_VSA, RegData);

//             RegData = GetVirtualSwitchMask(pdev->type, SEG_PORT, 3);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_MASK_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT2_TX_LIST_MASK_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0024 << 2);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_VSA, RegData);

//             RegData = GetVirtualSwitchMask(pdev->type, SEG_PORT, 4);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_MASK_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SW_SHIFT3_TX_LIST_MASK_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, VSA_TRIGGER_IQ_OFFSET);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_DC_OFFSET_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_DC_OFFSET_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, VSA_RESAMPLE);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_RESAMPLE_SEL_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_RESAMPLE_SEL_LIST_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, BUSI_LISTMODE_SEGMENT_OFFSET_VSA);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_OFFSET_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_OFFSET_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, BUSI_LISTMODE_SEGMENT_SAMPLE_VSA);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_SAMPLE_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_SAMPLE_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, BUSI_LISTMODE_SEGMENT_DURATION_VSA);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_DURATION_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_DURATION_VSA, RegData);

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, VSA_TRIG_SEL);
//             if (RegData == VSA_TRIGSEL_TRIGGER)
//             {
//                 wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_TYPE_VSA, 0);
//             }
//             else
//             {
//                 wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_TYPE_VSA, 1);
//             }
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_TRIG_TYPE_VSA, ((RegData == VSA_TRIGSEL_TRIGGER) ? 0 : 1));

//             RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, VSA_TRIGGER_GATE);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_LEVEL_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_TRIG_LEVEL_VSA, RegData);

//             ConfigCount = wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr);
//             for (i = 0; i < ConfigCount; i++)
//             {
//                 // bit0_15:data, bit16_22:addr, bit23:0w,1r
//                 RegData = wt_read_LMX2594_Cfg_List(pdev->dev_virtual_addr, i);
//                 wt_write_direct_reg(pdev, BUSI_LISTMODE_RX_2594_TX_LIST, RegData);
//                 dbg("Write ListMode List: LMX2594ConfigList Regaddr= 0x%x, data= 0x%x", ((RegData >> 16) & 0x7f), (RegData & 0xffff));
//             }
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_RX_2594_CFG_NUM_LIST, ConfigCount);
//             // 清空833 与 2594的ListMode配置
//             wt_set_LMX2594_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);
//             ConfigCount = wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr);
//             for (i = 0; i < ConfigCount; i++)
//             {
//                 // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
//                 RegData = wt_read_HMC833_Cfg_List(pdev->dev_virtual_addr, i);
//                 wt_write_direct_reg(pdev, BUSI_LISTMODE_RX_833_TX_LIST, RegData);
//                 dbg("Write ListMode List: HMC833Config Regaddr= 0x%x, data= 0x%x", ((RegData >> 25) & 0x3F), ((RegData >> 1) & 0xffffff));
//             }
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_RX_833_CFG_NUM_LIST, ConfigCount);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_RX_833_CFG_NUM_LIST, ConfigCount);

//             // 清空833 与 2594的ListMode配置
//             wt_set_HMC833_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);

// #undef SEG_PORT
//         }
//         else
//         {
//             RegData = wt_read_direct_reg(pdev, VSA_RESAMPLE);
//             wt_write_direct_reg(pdev, BUSI_LISTMODE_RESAMPLE_SEL_LIST_VSA, RegData);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_RESAMPLE_SEL_LIST_VSA, RegData);

//             RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_OFFSET_VSA);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_OFFSET_VSA, RegData);

//             RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_SAMPLE_VSA);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_SAMPLE_VSA, RegData);

//             RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_DURATION_VSA);
//             PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_DURATION_VSA, RegData);
//         }

//         // 使能当前Segment的配置。
//         wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_INDEX_VSA, SeqSegParam.SegmentIndex);
//         PRINTK_LIST_SEGENT(pdev->type, pdev->id, SeqSegParam.SegmentIndex, BUSI_LISTMODE_SEGMENT_INDEX_VSA, SeqSegParam.SegmentIndex);
// #undef PRINTK_LIST_SEGENT
//     }
//     printk("=======WT_SetVirtualAddrConfig End=======\n");
    return WT_OK;
}

int WT_SetSeqSegTimeParam(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct Seq_Seg_TimeParamType Param;

    if (copy_from_user(&(Param), arg, sizeof(Param)))
    {
        dbg("WT_SetVirtualAddrConfig info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    if (pdev->type == DEV_TYPE_VSG)
    {

    }
    else
    {
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_OFFSET_VSA, Param.MeaoffsetCnt);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_SAMPLE_VSA, Param.MeadurationCnt);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_DURATION_VSA, Param.DurationCnt);
    }
    return WT_OK;
}

int WT_SetSeqTrigComParam(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct CommonTrigType Param;
    int TrigTimeout1;
    int TrigTimeout2;

    if (copy_from_user(&(Param), arg, sizeof(Param)))
    {
        dbg("WT_SetSeqTrigComParam info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    TrigTimeout1 = (unsigned long long)(Param.TriggerTimeoutCnt) & 0xFFFFFFFF;
    TrigTimeout2 = ((unsigned long long)(Param.TriggerTimeoutCnt) >> 32) & 0x03;

    if (pdev->type == DEV_TYPE_VSA)
    {
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_VSA, TrigTimeout1);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_OUTTRIGLEN_VSA, TrigTimeout2 | ((Param.OutsideTriggerValidLen & 0xFFF) << 4));
        //wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG,
        //    1 | wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG));
    }
    else
    {
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_VSG, TrigTimeout1);
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_TIMEOUT_OUTTRIGLEN_VSG, TrigTimeout2 | ((Param.OutsideTriggerValidLen & 0xFFF) << 4));
        //wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG,
        //    2 | wt_read_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_GENERATE_RDY_VSA_VSG));
        wt_write_direct_reg(pdev, BUSI_LISTMODE_SEGMENT_TRIG_REPEAT_NUM_VSG, Param.VsgSeqTrigRepeatRum & 0xFFF);
    }
    return WT_OK;
}

int WT_VSAGetParamDmaBuf(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;
    int ConfigCount;
    int i = 0;
    int value = 0;
    int Port = 0;
    unsigned int BitOffset = 0;
    int SegSpiBeatCnt = 0;

    Port = GetVirtualAddrSwPort(pdev->dev_virtual_addr);
    pdev->HwParamBuf.offset = 0;

    BitOffset = 0;
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, H2C_DATA_TYPE_VSA_HW_PARAM, 8, &BitOffset);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr), 4, &BitOffset);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr), 4, &BitOffset);
    pdev->HwParamBuf.offset += BEAT_LEN_UNIT_BYTE;
    BitOffset = 0;
    SegSpiBeatCnt += 4;
    SegSpiBeatCnt += wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr) / 4 + (wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr) % 4 ? 1:0);
    SegSpiBeatCnt += wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr) / 4 + (wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr) % 4 ? 1:0);
    SegSpiBeatCnt = SegSpiBeatCnt << 24;
    BeatInsert(pdev->HwParamBuf.pBuf + (pdev->HwParamBuf.offset - 4), 0, BEAT_LEN_UNIT_BYTE, SegSpiBeatCnt, 32, &BitOffset);

    // dma模式
    BitOffset = 0;
    ConfigCount = wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr);
    for (i = 0; i < ConfigCount; i++)
    {
        // bit0_15:data, bit16_22:addr, bit23:0w,1r
        RegData = wt_read_LMX2594_Cfg_List(pdev->dev_virtual_addr, i);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, RegData, 32, &BitOffset);
        dbg("WT_VSAGetParamDmaBuf: LMX2594ConfigList Regaddr= 0x%x, data= 0x%x", ((RegData >> 16) & 0x7f), (RegData & 0xffff));
        dbg("WT_VSAGetParamDmaBuf: LMX2594Param= 0x%x", RegData);
    }
    pdev->HwParamBuf.offset += (ConfigCount * sizeof(int) + BEAT_LEN_UNIT_BYTE - 1) & ~(BEAT_LEN_UNIT_BYTE - 1) /*对齐到Beat*/;
    // 清空2594的ListMode配置
    wt_set_LMX2594_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);

    BitOffset = 0;
    ConfigCount = wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr);
    for (i = 0; i < ConfigCount; i++)
    {
        // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
        RegData = wt_read_HMC833_Cfg_List(pdev->dev_virtual_addr, i);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, RegData, 32, &BitOffset);
        dbg("WT_VSAGetParamDmaBuf: HMC833Config Regaddr= 0x%x, data= 0x%x", ((RegData >> 25) & 0x3F), ((RegData >> 1) & 0xffffff));
        dbg("WT_VSAGetParamDmaBuf: HMC833Param= 0x%x", RegData);
    }
    pdev->HwParamBuf.offset += (ConfigCount * sizeof(int) + BEAT_LEN_UNIT_BYTE - 1) & ~(BEAT_LEN_UNIT_BYTE - 1) /*对齐到Beat*/;
    // 清空833的ListMode配置
    wt_set_HMC833_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);

    //其他配置
    BitOffset = 0;
    RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, RX_SHIFT_0_TX);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, RegData, 32, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, RX_SHIFT_0_TX, RegData);
    RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, RX_SHIFT_1_TX);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, RegData, 32, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, RX_SHIFT_1_TX, RegData);
    //SW_shift0_list_vsg组成(40bit)
    RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0014 << 2);
    value = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0015 << 2);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), 32, &BitOffset);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, (RegData >> 24) & GetBitMask(8), 8, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, SW_shift0_list_vsg [31:0]= 0x%llx, SW_shift0_list_vsg [39:32]= 0x%llx!\n", pdev->type, pdev->id, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), (RegData >> 24) & GetBitMask(8));
    RegData = GetVirtualSwitchMask(pdev->type, Port, 0);
    value = GetVirtualSwitchMask(pdev->type, Port, 1);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), 32, &BitOffset);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, (RegData >> 24) & GetBitMask(8), 8, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, SW_shift0_list_vsg_mask [31:0]= 0x%llx, SW_shift0_list_vsg [39:32]= 0x%llx!\n", pdev->type, pdev->id, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), (RegData >> 24) & GetBitMask(8));
    RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x001a << 2);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
    value = GetVirtualSwitchMask(pdev->type, Port, 2);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, value, 32, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x, Mask:0x%x!\n", pdev->type, pdev->id, 0x001a << 2, RegData, value);
    RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0020 << 2);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
    value = GetVirtualSwitchMask(pdev->type, Port, 3);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, value, 32, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x, Mask:0x%x!\n", pdev->type, pdev->id, 0x0020 << 2, RegData, value);
    RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0024 << 2);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
    value = GetVirtualSwitchMask(pdev->type, Port, 4);
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, value, 32, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x, Mask:0x%x!\n", pdev->type, pdev->id, 0x0024 << 2, RegData, value);

    // 寄存器模式
    RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, WT418_SW_PAC_CTL3);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, WT418_SW_PAC_CTL3, RegData);
    value = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, WT418_SW_LOOP_CTL5);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, WT418_SW_LOOP_CTL5, value);
    RegData |= value << 8;
    BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 16, &BitOffset);
    dbg_print("WT_VSAGetParamDmaBuf: Type%d, ModId%d, (WT418_SW_LOOP_CTL5 << 8) | WT418_SW_PAC_CTL3 = 0x%x!\n", pdev->type, pdev->id, RegData);

    pdev->HwParamBuf.offset += BEAT_LEN_UNIT_BYTE * 3 /*对齐到Beat*/;
    dbg_print("=======WT_VSAGetParamDmaBuf End=======\n");
    if (copy_to_user(arg, &pdev->HwParamBuf.offset, DataLength))
    {
        dbg("WT_VSAGetParamDmaBuf info copy_to_user failed!\n");
        SetVirtualAddrMode(&(pdev->dev_virtual_addr), false);
        SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), false);
        return WT_CPY_TO_USR_FAILED;
    }

    SetVirtualAddrMode(&(pdev->dev_virtual_addr), false);
    SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), false);

    return WT_OK;
}

int WT_VSGGetParamDmaBuf(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;
    int ConfigCount;
    int i = 0;
    int value = 0;
    int Port = 0;
    unsigned int BitOffset = 0;
    int SegSpiBeatCnt = 0;

    pdev->HwParamBuf.offset = 0;
    Port = GetVirtualAddrSwPort(pdev->dev_virtual_addr);
    if (pdev->type == DEV_TYPE_VSG)
    {
        BitOffset = 0;
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, H2C_DATA_TYPE_VSG_HW_PARAM, 8, &BitOffset);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr), 4, &BitOffset);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr), 4, &BitOffset);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE, wt_get_AdcOrDac_Cfg_List_Count(pdev->dev_virtual_addr), 4, &BitOffset);
        pdev->HwParamBuf.offset += BEAT_LEN_UNIT_BYTE;
        BitOffset = 0;
        SegSpiBeatCnt += 4;
        SegSpiBeatCnt += wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr) / 4 + (wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr) % 4 ? 1 : 0);
        SegSpiBeatCnt += wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr) / 4 + (wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr) % 4 ? 1 : 0);
        SegSpiBeatCnt += wt_get_AdcOrDac_Cfg_List_Count(pdev->dev_virtual_addr) / 4 + (wt_get_AdcOrDac_Cfg_List_Count(pdev->dev_virtual_addr) % 4 ? 1 : 0);
        SegSpiBeatCnt = SegSpiBeatCnt << 24;
        BeatInsert(pdev->HwParamBuf.pBuf + (pdev->HwParamBuf.offset - 4), 0, BEAT_LEN_UNIT_BYTE, SegSpiBeatCnt, 32, &BitOffset);

        // dma模式
        BitOffset = 0;
        ConfigCount = wt_get_LMX2594_Cfg_List_Count(pdev->dev_virtual_addr);
        for (i = 0; i < ConfigCount; i++)
        {
            // bit0_15:data, bit16_22:addr, bit23:0w,1r
            RegData = wt_read_LMX2594_Cfg_List(pdev->dev_virtual_addr, i);
            BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 2, RegData, 32, &BitOffset);
            dbg_print("Write ListMode List: LMX2594ConfigList Regaddr= 0x%x, data= 0x%x", ((RegData >> 16) & 0x7f), (RegData & 0xffff));
            dbg_print("Write ListMode List: LMX2594Param= 0x%x", RegData);
        }
        pdev->HwParamBuf.offset += (ConfigCount * sizeof(int) + BEAT_LEN_UNIT_BYTE - 1) & ~(BEAT_LEN_UNIT_BYTE - 1) /*对齐到Beat*/;
        BitOffset = 0;
        // 清空2594的ListMode配置
        wt_set_LMX2594_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);

        ConfigCount = wt_get_HMC833_Cfg_List_Count(pdev->dev_virtual_addr);
        for (i = 0; i < ConfigCount; i++)
        {
            // bit0:0, bit1_24:data, bit25_30:addr, bit31:0w,1r
            RegData = wt_read_HMC833_Cfg_List(pdev->dev_virtual_addr, i);
            BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 2, RegData, 32, &BitOffset);
            dbg_print("Write ListMode List: HMC833Config Regaddr= 0x%x, data= 0x%x", ((RegData >> 25) & 0x3F), ((RegData >> 1) & 0xffffff));
            dbg_print("Write ListMode List: HMC833Param= 0x%x", RegData);
        }
        pdev->HwParamBuf.offset += (ConfigCount * sizeof(int) + BEAT_LEN_UNIT_BYTE - 1) & ~(BEAT_LEN_UNIT_BYTE - 1) /*对齐到Beat*/;
        BitOffset = 0;
        // 清空833的ListMode配置
        wt_set_HMC833_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);

        ConfigCount = wt_get_AdcOrDac_Cfg_List_Count(pdev->dev_virtual_addr);
        for (i = 0; i < ConfigCount; i++)
        {
            // bit0_7:data, bit8_22:addr, bit23:0w,1r
            RegData = wt_read_AdcOrDac_Cfg_List(pdev->dev_virtual_addr, i);
            BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 2, RegData, 32, &BitOffset);
            dbg_print("Write ListMode List: AdcOrDacConfig Regaddr= 0x%x, data= 0x%x", ((RegData >> 8) & 0x7fff), (RegData & 0xff));
            dbg_print("Write ListMode List: 9142Param= 0x%x", RegData);
        }
        pdev->HwParamBuf.offset += (ConfigCount * sizeof(int) + BEAT_LEN_UNIT_BYTE - 1) & ~(BEAT_LEN_UNIT_BYTE - 1) /*对齐到Beat*/;
        // 清空AdcOrDac的ListMode配置
        BitOffset = 0;
        wt_set_AdcOrDac_Cfg_List_Count(&(pdev->dev_virtual_addr), 0);
        RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, TX_SHIFT_0_TX);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, TX_SHIFT_0_TX, RegData);
        RegData = wt_read_direct_reg_virtual(pdev->dev_virtual_addr, TX_SHIFT_1_TX);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, TX_SHIFT_1_TX, RegData);
        //SW_shift0_list_vsg组成(40bit)
        RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0014 << 2);
        value = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0015 << 2);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), 32, &BitOffset);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, (RegData >> 24) & GetBitMask(8), 8, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, SW_shift0_list_vsg [31:0]= 0x%llx, SW_shift0_list_vsg [39:32]= 0x%llx!\n", pdev->type, pdev->id, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), (RegData >> 24) & GetBitMask(8));
        RegData = GetVirtualSwitchMask(pdev->type, Port, 0);
        value = GetVirtualSwitchMask(pdev->type, Port, 1);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), 32, &BitOffset);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, (RegData >> 24) & GetBitMask(8), 8, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, SW_shift0_list_vsg_mask [31:0]= 0x%llx, SW_shift0_list_vsg_mask [39:32]= 0x%llx!\n", pdev->type, pdev->id, ((RegData & GetBitMask(24)) << 8) | (value & GetBitMask(8)), (RegData >> 24) & GetBitMask(8));
        RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x001a << 2);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
        value = GetVirtualSwitchMask(pdev->type, Port, 2);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, value, 32, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x, Mask:0x%x!\n", pdev->type, pdev->id, 0x001a << 2, RegData, value);
        RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0020 << 2);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
        value = GetVirtualSwitchMask(pdev->type, Port, 3);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, value, 32, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x, Mask:0x%x!\n", pdev->type, pdev->id, 0x0020 << 2, RegData, value);
        RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, 0x0024 << 2);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
        value = GetVirtualSwitchMask(pdev->type, Port, 4);
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, value, 32, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x, Mask:0x%x!\n", pdev->type, pdev->id, 0x0024 << 2, RegData, value);
        // 寄存器模式
        RegData = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, WT418_SW_PAC_CTL3);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, WT418_SW_PAC_CTL3, RegData);
        value = wt_read_direct_reg_virtual(pBPdev->dev_virtual_addr, WT418_SW_LOOP_CTL5);
        dbg_print("Write ListMode List: Type%d, ModId%d, Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, WT418_SW_LOOP_CTL5, value);
        RegData |= value << 8;
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 16, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, (WT418_SW_LOOP_CTL5 << 8) | WT418_SW_PAC_CTL3 = 0x%x!\n", pdev->type, pdev->id, RegData);
        RegData = (wt_read_direct_reg_virtual(pdev->dev_virtual_addr, BUSI_VSG_GAP_POWER_MODE) >> (Port-WT_RF_PORT_RF1)) & 0x1u;
        BeatInsert(pdev->HwParamBuf.pBuf + pdev->HwParamBuf.offset, 0, BEAT_LEN_UNIT_BYTE * 3, RegData, 32, &BitOffset);
        dbg_print("Write ListMode List: Type%d, ModId%d, (Ctrl3_pi_en_list)Addr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, BUSI_VSG_GAP_POWER_MODE, RegData);
        pdev->HwParamBuf.offset += BEAT_LEN_UNIT_BYTE * 3 /*对齐到Beat*/;
    }
    dbg_print("=======WT_SetVirtualAddrConfig End=======\n");
    if (copy_to_user(arg, &pdev->HwParamBuf.offset, DataLength))
    {
        dbg("WT_VSGGetStatus info copy_to_user failed!\n");
        SetVirtualAddrMode(&(pdev->dev_virtual_addr), false);
        SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), false);
        return WT_CPY_TO_USR_FAILED;
    }

    SetVirtualAddrMode(&(pdev->dev_virtual_addr), false);
    SetVirtualAddrMode(&(pBPdev->dev_virtual_addr), false);
    return WT_OK;
}


int WT_VSGResetDmaFIFO(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RegData;

    RegData = wt_read_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG);
    ClearBit(RegData, VSG_SPI_CFG_RESET_DMA_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG, RegData);
    SetBit(RegData, VSG_SPI_CFG_RESET_DMA_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG, RegData);
    ClearBit(RegData, VSG_SPI_CFG_RESET_DMA_BIT);
    wt_write_direct_reg(pdev, BUSI_LISTMODE_DMA_FIFO_RESET_VSG, RegData);
    return WT_OK;
}

int WT_GetDuplexVsgRunConfig(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct DuplexVsgRunConfig VsgRunConfig;
    if (copy_from_user(&VsgRunConfig, arg, DataLength))
    {
        dbg("WT_GetDuplexVsgRunConfig info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->testertype == HW_WT418)
    {
        VsgRunConfig.VsgRunning = pdev->vsg_stat.vsg_run_flag;
        if (VsgRunConfig.VsgRunning)
        {
            VsgRunConfig.VsgDacCode = pdev->vsg_stat.vsg_dac_reg;

            // 射频板ATT
            wt_ReadAllATT(pdev, VsgRunConfig.VsgAtt);

            // 开关板ATT
            wt_ReadSwbAttCodeFromCache_418VA(VsgRunConfig.RfPort, VsgRunConfig.SwAtt, pdev->vsg_stat.sw_shift_reg);
        }
    }
    else
    {
        // 不支持其他类型的仪器
        VsgRunConfig.VsgRunning = 0;
    }

    if (copy_to_user(arg, &VsgRunConfig, DataLength))
    {
        dbg("WT_GetDuplexVsgRunConfig info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}
int WT_VSGSetPnLoopIfg(int DataLength, void *arg, struct dev_unit *pdev)
{
    unsigned long long GapTime;

    if (copy_from_user(&(GapTime), arg, sizeof(GapTime)))
    {
        dbg("WT_SetSeqTrigComParam info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->type == DEV_TYPE_VSG)
    {
        wt_write_direct_reg(pdev, VSG_LOOP_PN_IFG, GapTime);
    }

    return WT_OK;
}

int WT_SetSeqTrigLoopParam(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Loop;

    if (copy_from_user(&(Loop), arg, sizeof(Loop)))
    {
        dbg("WT_SetSeqTrigLoopParam info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->type == DEV_TYPE_VSG)
    {
        wt_write_direct_reg(pdev, VSG_LOOP_CNT, Loop);
    }

    return WT_OK;
}
