#!/bin/bash
proc_wave_dir() 
{
    waveName="$2"
    dirName="$1/$waveName"

    if [ ! -e "$dirName" ]; then
        mkdir $dirName
    fi

    if [ ! -e "$dirName/BT" ]; then
        mkdir $dirName/BT
    fi

    if [ ! -e "$dirName/User" ]; then
        mkdir $dirName/User
    fi

    if [ ! -e "$dirName/User/8080" ]; then
        mkdir $dirName/User/8080
    fi

    if [ ! -e "$dirName/Wifi" ]; then
        mkdir $dirName/Wifi
    fi

    if [ ! -e "$dirName/Wifi/8080" ]; then
        mkdir $dirName/Wifi/8080
    fi

    if [ ! -e "/tmp/Table" ]; then
        mkdir /tmp/Table
    fi

    if [ ! -e "/tmp/$waveName" ]; then
        mkdir /tmp/$waveName
    fi

    for d in `ls $dirName`; do
        ln -snf $dirName/$d /tmp/$waveName/$d
    done
}

memreserved=`cat /proc/iomem | grep -i reserved |grep 40000000-4fffffff`
if [ -z "$memreserved" ]; then
    sed -i '/^GRUB_CMDLINE_LINUX=/ s/"$/ memmap='"'"'256M\\$1G'"'"'"/' /etc/default/grub
    update-grub
    echo "add memmap to update cmdline and reboot..."
    reboot
else
    echo "memmap 40000000-4fffffff has reserved"
fi

runing=`ps -A | grep WT-`
if [ -z "$runing" ]; then

    dir=$(dirname $(readlink -f $0))/bin
    chmod 777 -R $dir
    rm -f /var/log/*.1.gz-*.backup
	
    $dir/eth/eth_rename.sh
	
    #若存在pcicheck.sh则执行pcicheck.sh，否则执行pcicheck_update.sh
    if [ -e "$dir/pcicheck.sh" ]; then
        $dir/pcicheck.sh
    elif [ -e "$dir/pcicheck_update.sh" ]; then
        $dir/pcicheck_update.sh
    fi

    if [ -z `echo $LD_LIBRARY_PATH | grep $dir` ]; then
        export LD_LIBRARY_PATH=$dir:$LD_LIBRARY_PATH
    fi

    if [[ "$@" == *nocal* ]]; then
        echo "run nocal"
        touch /tmp/notIntercal.txt
    else
        rm -f /tmp/notIntercal.txt
    fi

    #del the stack data file when rerun
    if [ -e "$dir/Stack" ]; then
        rm $dir/Stack -rf
    fi

    # 在脚本开始处清理超过3个月的日志
    find /var/log -name "kern.log.*" -type f -mtime +90 -delete
    find /var/log -name "syslog.*" -type f -mtime +90 -delete

    proc_wave_dir $dir "wave"


    #del the historical useless file
    if [ -d "$dir/low_wave" ]; then
        rm -rf $dir/low_wave
    fi

    mkdir -p /tmp/low_wave


    #强制删除历史遗留没用的文件内容
    rm $dir/GUIFile -r
    rm $dir/wave/Sin1MHz.low

    if [ ! -d "$dir/fpgadata" ]; then
        mkdir $dir/fpgadata
    fi

    if [ ! -d "$dir/digdata" ]; then
        mkdir $dir/digdata
    fi

    if [ ! -e "/tmp/wave" ]; then
        mkdir /tmp/wave
        for d in `ls $dir/wave`; do
            ln -s $dir/wave/$d /tmp/wave/$d
        done
    fi

    if [ ! -e "/dev/wtback" ]; then
        rm -rf /tmp/wt_back_inited
        insmod $dir/xdma.ko poll_mode=1
        insmod $dir/WT-Drv.ko
        chmod 777 /dev/wt*
    else
        echo "rmmod WT_Drv"
        rm -rf /tmp/wt_back_inited
        rmmod WT_Drv
        rmmod xdma
        insmod $dir/xdma.ko poll_mode=1
        insmod $dir/WT-Drv.ko
        chmod 777 /dev/wt*
    fi

    if [ ! -d "$dir/curfw" ]; then
        mkdir $dir/curfw
    fi

    if [ ! -d "/wttmp" ]; then
        mkdir -p /wttmp
        mount -t ramfs ramfs /wttmp
        chmod 660 -R /wttmp
    else
        mount -t ramfs ramfs /wttmp
        rm -rf /wttmp/d*
    fi


    if [ ! -d "$dir/HwDecode" ]; then
        mkdir $dir/HwDecode
    fi

    if [ ! -d "$dir/prev" ]; then
        mkdir $dir/prev
    fi

    if [ ! -d "$dir/eth" ]; then
        mkdir -p $dir/eth
    fi

    if [ ! -d "$dir/lic" ]; then
        mkdir -p $dir/lic
    fi

    rm -rf /tmp/wt_vsg_inited*
    rm -rf /tmp/wt_vsa_inited*
    rm -rf /tmp/wt_device_inited
    rm -rf /tmp/wt_lf_inited
    rm -rf /tmp/wt_subnet_inited
    #clear avg Raw files
    rm -rf /tmp/wave/rawwaveFiles

    $dir/WT-Manager $@
else
    echo "Err:program is running, please kill first!"
fi

