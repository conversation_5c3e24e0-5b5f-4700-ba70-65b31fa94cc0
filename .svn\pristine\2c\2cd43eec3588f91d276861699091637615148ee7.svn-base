#ifndef SCPI_3GPP_ALZ_JSON_SET_H_
#define SCPI_3GPP_ALZ_JSON_SET_H_

#include "basehead.h"
#include <jsoncpp/json/json.h>

// void Format_3GPP_ALZ_LTE(Json::Value &root, Alg_3GPP_AlzIn4g &LTE);

// void Format_3GPP_ALZ_NR(Json::Value &root, Alg_3GPP_AlzIn5g &NR);

// void Format_3GPP_ALZ_NBIot(Json::Value &root, Alg_3GPP_AlzInNBIOT &NBiot);

// 将分析参数格式化为json格式
// void Format_3GPP_ALZ_Json(Json::Value &root, AlzParam3GPP &Param);

// 将json格式数据转分析参数
int Set_3GPP_ALZ_By_Json(SCPI_AlzParam &vsaAlzParam, AlzParam3GPP &Param, Json::Value &root);

#endif