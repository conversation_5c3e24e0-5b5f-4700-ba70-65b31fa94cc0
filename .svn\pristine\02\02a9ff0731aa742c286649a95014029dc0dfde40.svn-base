/*
 * @Description: listmod相关对象以及结构体
 * @Autor: 
 * @Date: 20240703
 */

#include <vector>

#include "vsa.h"
#include "vsg.h"
#include "analysis/analysis.h"
#include "../../general/wtcal.h"

#ifndef __SERVER_LISTMOD_SEQUENCE_H__
#define __SERVER_LISTMOD_SEQUENCE_H__

#define LIST_MODE_REPEAT_MAX (50)

#define LIST_MODE_FW_CALC_CW_DATA (0)          // listmode 固件中是否计算CW信号功率, 便于数据对比， 缺省为0
#define LIST_MODE_DUMP_VSA_CAP_DATA_TO_CSV (0) // listmode 是否保存VSA caption data到CSV文件，便于对比分析，缺省为0, 慎用
#define LIST_MODE_CHECK_VSA_STATUS (0)         // listmode 中断检查VSA状态寄存器， 缺省为0
#define LIST_MODE_SEGMENT_FW_START (0)         // listmode 每个segment由固件主动发起start，缺省为0
#define LIST_MODE_DUPLEX_NOISE_COMP_MSG (1)    // 显示双工补偿日志

//seq类型
enum SEQUENCETYPE
{
    SEQUENCETX,
    SEQUENCERX,
    SEQUENCETXRX,
    SEQUENCETYPEEND,
};

//listmod场景：0非组合场景，1组合场景
enum LISTSCEN
{
    LISTSCEN_NONECOMB, //非组合场景
    LISTSCEN_COMB,     //组合场景
    LISTSCENEEND,
};

//seq状态
enum SEQUENCESTATE
{
    SEQUENCEOFF,         //停止状态, 这个时候可以去获取部分seg结果，要根据seg状态去获取
    SEQUENCERUNNING,     //运行状态，不能获取相关seg结果
    SEQUENCEFAILED,      //超时或者其他失败
    SEQUENCEREADY,       //seq处理完毕，理论上所有seg结果都可以获取
    SEQUENCESTATEEND,
};

//seg状态
enum SEGSTATE
{
    SEGNOREADY,   //相应seg还未处理
    SEGCAPTUREED, //抓取完成
    SEGREADY,     //相应seg已处理（对于Tx,代表抓取已分析完成，对于Rx代表发送完成），可以获取相关seg结果
    SEGFAILED,    //中间处理过程失败
    SEGSTATEEND,
};

//全局公共trigger参数，vsa/vsg共用
//虽然是全局参数，但考虑到后续有可能改成seg局参数，且该参数占有空间有限，所以scpi侧仍做成seg级的参数，在固件或驱动侧根据实际是否需要全局下发到fpga
struct TriggerCommonParam {
    double TriggerTimeout; //超时时间，单位s
    double  OutTriggerValidTime; //外部触发信号的有效长度,只在triggertype 为外部触发时有效，单位s
    int VsgSeqTrigRepeatRum; //只vsg有效，该参数跟vsg大循环次数相等
};

//时间参数结构体：单位秒
struct SeqTimeParam {
    double Duration;   //信号长度
    double Meaoffset;  //对于Rx seg而言，该参数决定了seg之间的gap
    double Meadura;    //对于Rx seg而言，该参数无效
    int Repeat;        //重复次数
};

struct TxSubSegment {
    ~TxSubSegment() {if (Alg != nullptr) {delete Alg;}}
    int SegSubId;
    SEGSTATE SegSta = SEGNOREADY;
    VsaCapData SegvsaCapData;  //抓取的数据
    double Power;              //从fpga获取到的功率
    Analysis *Alg = nullptr;              //分析对象
    u32 start = 0;
    DuplexVsgRunConfig DuplexVsgStat;
};

//tx Segment结构体：
struct TxSegment {
    ~TxSegment() { m_SubSeg.clear(); }
    int SegId;
    SEGSTATE SegSta = SEGNOREADY;
    VsaParam  vsaParam;        //抓取参数
    VsaTrigParam vsaTrigParam; //trig参数
    SeqTimeParam SegTimeParam; //时间参数
    TriggerCommonParam SegTigComParam; //公共trigger参数
    int Demode = WT_DEMOD_CW;

    std::vector<std::unique_ptr<TxSubSegment>>::iterator GetSubSegBeginIter() {return m_SubSeg.begin();}
    std::vector<std::unique_ptr<TxSubSegment>>::iterator GetSubSegEndIter() {return m_SubSeg.end();}
    std::vector<std::unique_ptr<TxSubSegment>>::iterator GetSubSegCapCurIter() {return m_SubSeg.begin() + m_CapOffset;}

    void CurCapOffsetShift() {m_CapOffset++;}
    std::vector<std::unique_ptr<TxSubSegment>> m_SubSeg; //Seg数组
    void PushbackSubSeg(std::unique_ptr<TxSubSegment> &Sub) {m_SubSeg.push_back(move(Sub));}

    int GetCaptureDataLen(){return round(SegTimeParam.Meadura * vsaParam.SamplingFreq * sizeof(short) * 2); }

    int m_CapOffset = 0;    // 当前的Segment

};


/// @brief 波形文件配置(兼容多PN)
typedef struct
{
    char WaveName[MAX_NAME_SIZE];      ///<本地波形文件路径
    char SaveWaveName[MAX_NAME_SIZE];  ///<信号文件保存到仪器后的信号文件名称。注意和WaveName区分
    int Wave2Flag;                     ///<默认值等于0。仅80+80时设置为1
} VsgWaveParameter;

//rx Segment结构体
struct RxSegment {
    int SegId;
    SEGSTATE SegSta;
    VsgParam vsgParam;         //vsg参数
    VsgDataInfo  VsgData;      //VSG信号
    PnItem Pn;                 //PN项
    ExtPnItem ExtPn;           //api下发的外部pn项
    SeqTimeParam SegTimeParam; //时间参数
    int vsgSyncParam;          //vsgListMode同步参数
    int PnIdex;                //在m_SeqPnList的索引
    TriggerCommonParam SegTigComParam; //公共trigger参数
};

//Segment结构体
struct Segment {
    bool TxFlag; //表明这个segment是tx seg还是rx seg，如果是tx seq，则seq下所有的seg都tx seg，如果是rx seq，则都是rx seg，对于组合场景，则可能是tx seg也可能是 rx seg
    SEGSTATE SegSta;
    union {
          TxSegment tx_seg;
          RxSegment rx_seg;
    };
};

//Segment Pn结构体
struct SegPnType
{
    int ExtendToDurationFlag;
    string FilePath;
    VsgDataInfo DataInfo;
};

class TxSequence {
public:
    TxSequence() {m_Seg.clear();}
    ~TxSequence() {}
    void SetListModEnable() {m_ListEnable = true;}
    void SetListModDisable() {m_ListEnable = false;}
    bool GetListModEnable() {return m_ListEnable;}
    void ClearListTxSeq();
    std::vector<std::unique_ptr<TxSegment>>::iterator GetSegBeginIter() {return m_Seg.begin();}
    std::vector<std::unique_ptr<TxSegment>>::iterator GetSegEndIter() {return m_Seg.end();}
    void PushbackSeg(std::unique_ptr<TxSegment> &Seg) {m_Seg.push_back(move(Seg));}
    void UpdateSeqStat(SEQUENCESTATE stat) {SeqStat = stat;}
    SEQUENCESTATE GetSeqStat(void) {return SeqStat;}
    void CurCapOffsetShift() {m_SegCapOffset++;}
    std::vector<std::unique_ptr<TxSegment>>::iterator GetSegCapCurIter() {return m_Seg.begin() + m_SegCapOffset;}
    int GetSegNum() {return m_Seg.size();}

private:
    int m_SegCapOffset = 0;
    bool m_ListEnable = false;
    unsigned int m_MaxSegSize = 0;
    SEQUENCESTATE SeqStat = SEQUENCEOFF;
    std::vector<std::unique_ptr<TxSegment>> m_Seg; //Seg数组
};

class RxSequence {
public:
    RxSequence() {}
    ~RxSequence() {}
    void SetListModEnable() {m_ListEnable = true;}
    void SetListModDisable() {m_ListEnable = false;}
    bool GetListModEnable() {return m_ListEnable;}
    void ClearListRxSeq();
    std::vector<std::unique_ptr<RxSegment>>::iterator GetSegBeginIter() {return m_Seg.begin();}
    std::vector<std::unique_ptr<RxSegment>>::iterator GetSegEndIter() {return m_Seg.end();}
    void PushbackSeg(std::unique_ptr<RxSegment> &Seg) {m_Seg.push_back(move(Seg));}
    bool CheckSeqPnList(SegPnType &SegPnParam, int &index);
    bool CheckSeqPnFileName(std::string filePath, int &index);
    void PushbackSeqPnList(SegPnType &SegPn){m_SegPnList.push_back(move(SegPn));}
    void ClearSeqPnList(){m_SegPnList.clear();}
    std::vector<SegPnType>& GetSegPnList(){return m_SegPnList;}

private:
    bool m_ListEnable = false;
    unsigned int m_MaxSegSize = 0;
    SEQUENCESTATE SeqStat = SEQUENCEOFF;
    std::vector<std::unique_ptr<RxSegment>> m_Seg; //Seg数组
    int m_Repet = 0;  //整个seq重复模式，当前只有rx seq有效, 0为单次模式， 1为重复模式
    std::vector<SegPnType>m_SegPnList;
};


#if 0
class ListSeq {
public:
    ListSeq();
    ~ListSeq();

private:
    LISTSCEN m_SeqScen;
    bool m_ListEnable;
    Sequence m_Seq[2];  //在组合场景下，只有一个seq有效，非组合场景两个都有效
};
#endif

#endif // __SEVER_LISTMOD_SEQUENCE_H__
