#ifndef _SM4_H_
#define _SM4_H_

#include <inttypes.h>
#include <string.h>
#include "common.h"

#define SMS4_KEY_LENGTH		16
#define SMS4_BLOCK_SIZE		16
#define SMS4_IV_LENGTH		(SMS4_BLOCK_SIZE)
#define SMS4_NUM_ROUNDS	32

#define LEN_WAPI_MIC    16
#define LEN_WAPI_PN     16
#define LEN_WAPI_HDR    (2 + LEN_WAPI_PN)   /* key ID + reserved + PN */

#define SMS4_ENCRYPT     1
#define SMS4_DECRYPT     0

typedef struct {
    unsigned int rk[SMS4_NUM_ROUNDS];
} sms4_key_t;

void sms4_set_encrypt_key(sms4_key_t *key, const u8 *user_key);
void sms4_encrypt(const u8 *in, u8 *out, const sms4_key_t *key);
#define sms4_decrypt(in,out,key)  sms4_encrypt(in,out,key)

void sms4_ecb_encrypt(const u8 *in, u8 *out,
    const sms4_key_t *key, int enc);
void sms4_cbc_encrypt(const u8 *in, u8 *out,
    size_t len, const sms4_key_t *key, u8 *iv, int enc);
void sms4_cfb128_encrypt(const u8 *in, u8 *out,
    size_t len, const sms4_key_t *key, u8 *iv, int *num, int enc);
void sms4_ofb128_encrypt(const u8 *in, u8 *out,
    size_t len, const sms4_key_t *key, u8 *iv, int *num);
void sms4_ctr128_encrypt(const u8 *in, u8 *out,
    size_t len, const sms4_key_t *key, u8 *iv,
    u8 ecount_buf[SMS4_BLOCK_SIZE], unsigned int *num);

int sms4_ofb_ae(const u8 *key, size_t key_len, u8 *iv, size_t iv_len,
    const u8 *plain, size_t plain_len, u8 *crypt);

void sms4_cbc_mac_mic(const u8 *aad, size_t aad_len,
    u8 *pdu, size_t pdu_len, u8 *key, u8 *iv);
#endif

