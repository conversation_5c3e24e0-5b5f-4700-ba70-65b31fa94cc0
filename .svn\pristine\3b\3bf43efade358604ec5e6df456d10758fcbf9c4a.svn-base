
#ifndef __StructuresDef_API_H__
#define __StructuresDef_API_H__

#include "TypeDef.h"

typedef struct
{
    int Demode;				/// 标准，同VSA参数中的一致
    int DataRate;			/// 速率, DATARATE_11A_ENUM,DATARATE_11B_ENUM,DataRate11n,DataRate11ac,DataRateMisc
    int DataLength;         /// 生成的数据
    char *MACAddr;          /// DUT MAC地址/字符串形式
} PerMacParameter;

typedef struct
{
    int BroadcastEnable;    /// DUT是否开启了广播SSID
    int AutoRange;			/// 初始时，是否用VSA的auto range查找最佳参考电平
    double Freq;			/// DUT中心频点
    double DutTargetPower;	/// DUT目标功率
    double VsaAtten;		/// VSA外部衰减
    double VsgAtten;		/// VSG外部衰减
    int packetCount;		/// 需发送的包数量
    int isFixedPowerLevel;	/// 是否为定值功率
    double FixedPower;      /// dBm ,定点功率值
    double StartLevel;		/// dBm，起始功率
    double StopLevel;		/// dBm，截至功率。StartLevel > StopLevel
    double LevelStep;		/// dB，功率步进
    double Threshold;		/// %，轮询测试
}PerActionParameter;

typedef struct
{
    double PowerLevel;
    unsigned int TotalPackets;
    unsigned int PacketsSent;
    unsigned int AcksReceived;
} PerResultParameter;

#include "Pac.h"
#endif  // __StructuresDef_H__
