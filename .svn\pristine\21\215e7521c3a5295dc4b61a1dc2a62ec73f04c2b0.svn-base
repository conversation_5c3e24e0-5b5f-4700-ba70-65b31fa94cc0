#ifndef __WT_DEV_BASE_H__
#define __WT_DEV_BASE_H__

#include <vector>
#include <string>
#include <jsoncpp/json/json.h>

#include "devdef.h"
#include "ioctlcmd.h"
#include "../errorlib.h"

//电压信息结构体
struct VoltInfoType
{
	//器件的实际电压值 = 电压值*倍压（ActualVoltValue = VoltValue * MultVolt）
	int VoltChannel;                                        //电压通道
	int MultVolt;                                           //倍压
    char Board[64];                                         //所在的单元板
	char VoltChannelInfo[256];                              //电压信息
	double VoltValue;                                       //实际电压值
	double LowLimit;                                        //电压下限值
	double HighLimit;                                       //电压上限值
};

//业务板硬件版本信息
struct BusiBoardHwInfo
{
    int BBHwVersion;
    int RfHwVersion;
    int LoHwVersion;

    int IsBBExist() { return (BBHwVersion & 0x7) != 0x7; }
    int IsRfExist() { return (RfHwVersion & 0x7) != 0x7; }
    int IsLoExist() { return (LoHwVersion & 0x7) != 0x7; }
};

class DevBase
{
public:
	DevBase(int BoardType, int HwVersion, Json::Value &ConfJson)
	       : m_BoardType(BoardType), m_HwVersion(HwVersion), m_JsonRoot(ConfJson) {}

    virtual ~DevBase() { Close(); }

    virtual int Release() = 0;

	void SetModFd(int ModId, int Fd)
	{
		m_ModId = ModId;
		m_Fd = Fd;
	}

    int SetDrvFasync();

	int DrvCmd(int Cmd, int CmdLen, void *CmdData);

	int GetVersion() const { return m_HwVersion; }

    int GetTesterType() const { return m_TesterType; }

    int GetRevisionId(int &RevisionId);

    const std::vector<VoltInfoType> &GetUBVoltInfo() const { return m_UBVoltInfoVec; }

    int UBVoltInfoInit(void);

    int GetUnitBoardFPGAInfo(UnitFPGAInfo &FPGAInfo);

    int GetUBVoltValue(double &VoltValue);

    int GetUBTempValue(double &TempValue);

    int SetUnitBoardPciDelay(int WriteDelay, int ReadDelay);

    int WriteDirectReg(int Addr, int Data);

    int ReadDirectReg(int Addr, int &Data);

    int CheckDirectReg(int Addr, int &Data);

    int WriteRomPage(u32 RomAddr, void *DataBuf, int ChipID);

    int ReadRomPage(u32 RomAddr, void *DataBuf, int ChipID);
protected:
    void Close();

	//*****************************************************************************
    //获取a,b的最大公约数
    //参数[IN] : a,b
    //返回值: 最大公约数
    //*****************************************************************************
    unsigned long long int Gcd(unsigned long long int ,unsigned long long int b);

	//*****************************************************************************
    //获取K值
    //参数[IN] : freq：频率
    //返回值: K值
    //*****************************************************************************
    double GetKValue(double freq);

protected:
    int m_TesterType;          //测试仪类型
	int m_BoardType;           //单元板类型
	int m_HwVersion;           //硬件版本号
	int m_Fd = -1;             //设备描述符
	int m_ModId = 0;           //模块ID
	Json::Value &m_JsonRoot;   //配置文件
	std::vector<VoltInfoType> m_UBVoltInfoVec;   //电压信息
};

#endif
