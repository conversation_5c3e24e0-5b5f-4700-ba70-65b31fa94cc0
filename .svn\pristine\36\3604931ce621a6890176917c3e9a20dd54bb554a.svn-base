//*****************************************************************************
//  File: wterror.h
//  错误码列表
//  Data: 2016.7.11
//*****************************************************************************

#ifndef __WT_ERROR_H__
#define __WT_ERROR_H__

//错误码列表
enum WT_ERROR_E
{
    /**************公共错误码************/
    WT_OK = 0,                                       //成功
    WT_ARG_ERROR = 1,                                //输入参数错误
    WT_ALLOC_FAILED = 2,                             //申请内存失败
    WT_VERSION_ERROR = 3,                            //版本不匹配
    WT_ARG_PSDU_ERROR = 4,                          //配置PSDU参数错误
    WT_ARG_MAC_ERROR = 5,                           //配置MAC参数错误
    WT_ARG_BANDWIDTH_ERROR = 6,                     //配置Bandwidth错误
    WT_ARG_FRAME_TYPE_ERROR = 7,                    //配置帧类型错误
    WT_ARG_SIGNALTYPE_ERROR = 8,                    //配置信号类型错误
    WT_ARG_UNKNOW_PARAMETER = 9,                    //功能未支持,不支持的配置
    WT_FILE_OPEN_ERROR = 10,                        //打开文件错误
    WT_DISK_CAPACITY_ERROR = 11,                    //硬盘空间不足
    WT_WAIT_TIMEOUT = 12,                           //等待超时
    WT_UNAUTHORIZED_ACCESS = 13,                    //非法访问，目前用于不允许通过协议操作的文件操作
    /**************驱动层错误码************/
    WT_SPI_WRITE_CHECK_OVERTIME = 0x11000,           //SPI写传输响应超时
    WT_CMD_FUNID_ERROR = 0x11001,                    //应用层与驱动层交互命令错误
    WT_SPI_READ_CHECK_OVERTIME = 0x11002,            //SPI读传输响应超时
    WT_I2C_WRITE_CHECK_OVERTIME = 0x11003,           //I2C写传输响应超时
    WT_I2C_READ_CHECK_OVERTIME = 0x11004,            //I2C读传输响应超时
    WT_SMBUS_WRITE_CHECK_OVERTIME = 0x11005,         //SMBUS写传输响应超时
    WT_SMBUS_READ_CHECK_OVERTIME = 0x11006,          //SMBUS读传输响应超时
    WT_SMBUS_WRITE_FAILED = 0x11007,                 //SMBUS写失败
    WT_SMBUS_READ_FAILED = 0x11008,                  //SMBUS读失败
    WT_FLASH_STATUS_CHECK_OVERTIME = 0x11009,        //FLASH状态检查超时
    WT_FLASH_WRITE_FULL_OVERTIME = 0x1100A,          //FLASH写传输响应超时
    WT_FLASH_READ_EMPTY_OVERTIME = 0x1100B,          //FLASH读传输响应超时
    WT_REG_ARGS_ERROR = 0x1100C,                     //PCI寄存器参数错误
    WT_REG_ADDR_ERROR = 0x1100D,                     //PCI寄存器地址错误
    WT_CPY_TO_USR_FAILED = 0x1100E,                  //copy_to_usr失败
    WT_CPY_FROM_USR_FAILED = 0x1100F,                //copy_from_usr失败
    WT_PN_NOT_CONFIG_ERROR = 0x11010,                //PN未配置
    WT_CAPTURE_SAMPLE_COUNT_ERROR = 0x11020,         //采样点数超出范围
    WT_I2C_WRITE_FAILED = 0x11021,                   //I2C写失败
    WT_I2C_READ_FAILED = 0x11022,                    //I2C读失败
    WT_CM_COMMUNICATION_FAILED = 0x11101,            //加密芯片通讯失败
    WT_CM_INITED_CHECK_FAILED = 0x11102,             //加密芯片未被初始化
    WT_CM_VERIFY_PWD1_FAILED = 0x11103,              //验证加密芯片write 7 password失败
    WT_CM_SET_CONFIG_REG_FAILED = 0x11104,           //加密芯片寄存器DCR配置失败
    WT_CM_INIT_CI_VALUE_FAILED = 0x11105,            //初始化加密芯片C0~C3失败
    WT_CM_INIT_GI_VALUE_FAILED = 0x11106,            //初始化加密芯片G0~G3失败
    WT_CM_DEF_PWD_FAILED = 0x11107,                  //初始化加密芯片密码区失败
    WT_CM_INIT_AR_PR_FAILED = 0x11108,               //初始化加密芯片AR、PR失败
    WT_CM_INIT_USR_ZONE_FAILED = 0x11109,            //初始化加密芯片用户区失败
    WT_CM_SET_USR_ACCESS_RIGHT_FAILED = 0x1110a,     //设置加密芯片用户区访问权限失败
    WT_CM_VERIFY_USR_ZONE0_FAILED = 0x1110b,         //验证加密芯片用户区0失败
    WT_CM_VERIFY_USR_ZONE1_FAILED = 0x1110c,         //验证加密芯片用户区1失败
    WT_CM_VERIFY_USR_ZONE2_FAILED = 0x1110d,         //验证加密芯片用户区2失败
    WT_CM_VERIFY_USR_ZONE3_FAILED = 0x1110e,         //验证加密芯片用户区3失败
    WT_CM_VERIFY_PWD2_FAILED = 0x1110f,              //再次验证加密芯片write 7 password失败
    WT_CM_SETCARD_ID_FAILED = 0x11111,               //设置加密芯片产商ID失败
    WT_REG_CHECK_OVERTIME = 0x11112,                 //检测寄存器写成功超时
    WT_WAIT_SEM_TIMEOUT = 0x11113,                   //等待信号量超时
    WT_FPGA_ERROR = 0x11114,                         //逻辑FPGA错误
    WT_MMAP_ERROR = 0X11115,                         //内存映射失败
    WT_SWITCH_FILE_ERROR = 0x11116,                  //读取开关板配置文件错误
    WT_GET_PORT_POWER_ERROR = 0x11117,               //获取端口处功率失败，未使能或选择的不是该端口。
    WT_DEVICE_NOT_SUPPORT = 0x11118,                 //仪器不支持此操作
    WT_XMDA_WR_ERROR = 0x11119,                      //XDMA数据传输错误
    WT_XMDA_STATUS_TIMEOUT = 0x11120,                //XDMA数据传输等待超时
    WT_REG_WR_ERROR = 0x11121,                       //PCI寄存器操作失败
    WT_UPGRADE_HOLD_KERNEL = 0x11122,                //升级动作占用内核
    /*************general模块的错误码*************/
    WT_CONF_ITEM_ERROR = 0x10000,                    //配置文件中指定项不存在
    WT_CONF_FILE_ERROR = 0x10001,                    //配置文件无法打开
    WT_SOCKET_CLOSED = 0x10002,                      //socket已关闭
    WT_SEND_FD_FAILED = 0x10003,                     //发送fd失败
    WT_RECV_FD_FAILED = 0x10004,                     //接收fd失败
    WT_CMD_ERROR = 0x10005,                          //命令格式错误
    WT_CMD_NO_HANDLER = 0x10006,                     //命令没有回掉函数
    WT_VSG_SETCONFIG_FAILED = 0x10007,               //VSG配置失败
    WT_VSA_SETCONFIG_FAILED = 0x10008,               //VSA配置失败
    WT_AD5611_CODE_OVER_RANGE = 0x10009,             //晶振code值超出范围
    WT_DEVFILE_OPEN_FAILED = 0x1000A,                //设备文件打开失败
    WT_IOCTL_FAILED = 0x1000B,                       //ioctl获取或设置失败
    WT_PN_ITEM_COUNT_ERROR = 0x1000C,                //PN item count值错误
    WT_UNITBOARD_TYPE_ERROR = 0x1000D,               //单元板类型错误
    WT_UNITBOARD_ID_ERROR = 0x1000E,                 //单元板模块ID错误
    WT_DEVICE_ID_ERROR = 0x1000F,                    //器件ID错误
    WT_DATA_OVER_RANGE = 0x10010,                    //参数数据超出范围
    WT_FAN_ID_ERROR = 0x10011,                       //风扇ID错误
    WT_FAN_SPEED_ERROR = 0x10012,                    //风扇速度错误

    //license错误码
    WT_LIC_FILE_ILLEGAL = 0x10013,                   //license文件非法
    WT_LIC_GENERATE_FAILED = 0x10014,                //license文件生成失败
    WT_LIC_TYPE_INVALID = 0x10015,                   //license类型无效
    WT_LIC_VALUE_INVALID = 0x10016,                  //license业务值无效
    WT_LIC_FILE_NOT_EXIST = 0x10017,                 //license文件不存在
    WT_LIC_FILE_ERROR = 0x10018,                     //license文件错误
    WT_DEV_FILE_ERROR = 0x10019,                     //设备时间文件错误
    WT_DEV_TIME_INVALID = 0x1001A,                   //当前设备时间无效
    WT_LIC_NOT_EXIST = 0x1001B,                      //指定的license不存在
    WT_LIC_TIME_EXCEED = 0x1001C,                    //指定license已过期
    WT_SN_NOT_MATCH = 0x1001D,                       //license文件的SN码与设备的SN码不匹配
    WT_CODE_NOT_MATCH = 0x1001E,                     //license文件的仪器特征码与设备的仪器特征码不匹配
    WT_LICENSE_FILE_CRC_ERROR = 0x1001F,             //license文件CRC验证错误
    WT_LICENSE_FILE_CHECKSUM_ERROR = 0x10020,        //license文件校验和验证错误
    WT_LICENSE_UPGRADE_ERROR = 0x10021,              //license升级失败
    WT_LICENSE_IS_OLD = 0x10022,                     //license文件太旧
    WT_LICENSE_REPEAT = 0x10023,                     //license重复，与仪器当前使用的相同

    //单元板操作错误码
    WT_GET_TEMPERATURE_ERR = 0x10024,                //获取温度数据失败
    WT_GET_VOLTAGE_ERR = 0x10025,                    //获取电压数据失败
    WT_GET_LED_STATUS_ERR = 0x10026,                 //获取灯状态失败

    //射频板错误码
    WT_RF_TEMPERATUR_ABNORMAL = 0x10027,             //射频板温度异常
    WT_RF_PORT_ERR = 0x10028,                        //RF端口号错误
    WT_IO_ID_ERR = 0x10029,                          //RF IO寄存器ID错误
    WT_PUMP_VALUE_ERR = 0x1002A,                     //本振pump值无效
    WT_ATTCODE_OVER_RANGE = 0x1002B,                 //ATT code超出允许范围

    //基带板错误码
    WT_DAC_GAIN_OVER_RANGE = 0x1002C,                //功率补偿超出DAC的补偿范围
    WT_LMK_INIT_FAILED = 0x1002D,                    //LMK初始化失败
    WT_ADC_INIT_FAILED = 0x1002E,                    //ADC初始化失败
    WT_DAC_INIT_FAILED = 0x1002F,                    //DAC初始化失败

    /**************背板错误码************/
    WT_SOURCE_ID_ILLEGAL = 0x10030,                  //时钟源选择错误
    WT_ADCHIP_ID_ILLEGAL = 0x10031,                  //电压侦测片选编号错误

    //本振错误码
    WT_MOD_FREQ_IS_UNLOCKED = 0x10032,               //LoMod freq未锁定
    WT_MIX_FREQ_IS_UNLOCKED = 0x10033,               //LoMix freq未锁定
    WT_LO_ID_ERROR = 0x10034,                        //本振编号错误
    WT_LO_VCO_ADDR_ERROR = 0x10035,                  //本振VCO地址错误
    WT_FREQ_OVER_RANGE = 0x10036,                    //频率超出允许范围

    //加密芯片错误码
    WT_ENCRYPT_ILLEGAL = 0x10037,                    //加密芯片不合法

    //开关板
    WT_SWITCHER_IS_NOT_EXIST = 0x10038,              //开关板不存在

    //灯板
    WT_LED_INDEX_ERROR = 0x10039,                    //灯编号错误
    WT_LED_STATUS_ERROR = 0x1003A,                   //灯编号错误

    WT_JSON_PARSE_FAILED = 0x1003B,                  //JSON文本解析失败
    WT_GET_CAL_FILE_FAILED = 0x1003C,                //获取校准文件失败

    WT_UNITBOARD_NOT_EXIST = 0x1003D,                //单元板不存在

    //初始化失败
    WT_BACK_AD7091_INIT_FAILED = 0x1003E,            //背板ADC7091初始化失败
    WT_BACK_OCXO_INIT_FAILED = 0x1003F,              //背板OCXO初始化失败
    WT_BACK_GET_OCXO_CALIBRATION_FAILED = 0x10040,   //获取OCXO校准数据失败
    WT_BACK_FAN_INIT_FAILED = 0x10041,               //背板风扇初始化失败
    WT_SWITCH_BOARD_INIT_FAILED = 0x10042,           //开关板初始化失败

    WT_BUSI_BOARD_FILE_OPEN_FAILED = 0x10043,        //业务板 wtvsg/wtvsa 文件打开失败
    WT_BUSI_BOARD_RF_NO_EXIST = 0x10044,             //业务板的射频板不存在
    WT_BUSI_BASE_AD7091_FAILED = 0x10045,            //基带板AD7091初始化失败
    WT_BUSI_BASE_IQ_SWITCH_FAILED = 0x10046,         //基带板IQ交换初始化失败

    WT_RFBOARD_AD7091_INIT_FAILED = 0x10047,         //射频板AD7091初始化失败
    WT_RFBOARD_ATT_INIT_FAILED = 0x10048,            //射频板ATT初始化失败
    WT_RFBOARD_SET_POWER_STATUS_FAILED = 0X10049,    //射频板链路电源初始化失败

    WT_VSG_BOARD_BASE_INIT_FAILED = 0x1004A,         //VSG基带板初始化失败
    WT_VSA_BOARD_BASE_INIT_FAILED = 0x1004B,         //VSA基带板初始化失败
    WT_VSG_BOARD_RF_INIT_FAILED = 0x1004C,           //VSG射频板初始化失败
    WT_VSA_BOARD_RF_INIT_FAILED = 0x1004D,           //VSA射频板初始化失败

    WT_LMX2592_IS_UNLOCKED = 0x1004E,                //LMX2592本振未锁定
    WT_SWB_AD7091_INIT_FAILED = 0x1004F,             //网口板ADC7091初始化失败
    WT_SWB_TEMPERATUR_ABNORMAL = 0x10050,            //开关板板温度异常

    WT_THREAD_CREATE_FAILE = 0x10051,                //线程创建失败
    WT_BUSI_INIT_TIMEROUT = 0x10052,                 //业务板初始化超时

    //从机链路操作数据IO错误码,
    WT_SOCKET_DATA_RECV_TIMEOUT = 0x10053,           //Socket读取指定长度数据超时
    WT_SOCKET_SLAVE_DATA_RECV_TIMEOUT = 0x10054,     //从机Socket读取指定长度数据超时
    WT_SOCKET_SLAVE_DATA_RECV_FAILED = 0x10055,      //从机Socket读数据失败
    WT_SOCKET_SLAVE_DATA_SEND_FAILED = 0x10056,      //从机Socket写数据失败
    WT_SOCKET_SLAVE_IO_ERROR = 0x10057,              //从机Socket读写数据失败

    WT_GET_TEMPERATURE_VALUE_ABNORMAL = 0x10058,     //温度数值异常
    WT_GET_TEMPERATURE_CHANGE_ABNORMAL  = 0x10059,   //温度变化异常

    WT_INNER_8318_POWER_CODE_NOT_STABLE = 0x10060,   //ByPass(内部) 8318功率CODE不稳定
    WT_FPGA_UPGRADE_FIND_FT4222_FAILED = 0X10061,

    WT_DIGITAL_PACKET_TYPE_ERROR = 0X10062,          //数字IQ接收包格式错误
    WT_DIGITAL_ETH_NOT_LINK = 0X10063,               //数字IQ网口未接网线

    WT_HMC1031_IS_UNLOCKED = 0x10064,               //HMC1031 未锁定
    WT_DOCKER_ENV_NO_EXIST = 0x10065,               //docker应用不存在
    WT_DOCKER_APP_OPT_FAILED = 0x10066,             //docker应用操作失败
    WT_HMC833_IS_UNLOCKED = 0x10067,                //HMC833 未锁定

    /**************WT-Link错误码************/
    WT_DELETE_CONECTION_INFO = 0x20000,              //删除链接信息
    WT_SOCKET_BIND_ERROR = 0x20001,                  //服务socket绑定失败
    WT_SOCKET_SET_ERROR = 0x20002,                   //Socket属性设置失败
    WT_WAIT_ACCEPT_TIMEOUT = 0x20003,                //链接超时
    WT_COMFIRM_WAIT_FAILED = 0x20004,                //等待连接合法性验证失败
    WT_COMFIRM_DECRYPT_ERROR = 0x20005,              //连接合法性验证解密不正确
    WT_SERVERID_OUT_RANGE = 0x20006,                 //连接申请的ServerID不在正确的范围内
    WT_ADMIN_CONNECTION_EXIT = 0x20007,              //已经存在管理连接
    WT_COMFIRM_WAIT_AUTH_FAILED = 0x20008,           //连接合法性等待验证失败
    WT_COMFIRM_RECV_INCORRECT_LENGTH = 0x20009,      //合法性验证时收到不正确的数据长度
    WT_COMFIRM_ACCEPT_FAILED = 0x20010,              //合法性验证通过, 但是最后的"connection ok"确认失败
    WT_FIND_LINK_FAILED = 0x20011,                   //Can not find father link
    /**************WT-Manager错误码************/
    WT_CREATE_SOCKET_FAILED = 0x30000,               //创建socket失败
    WT_CREATE_EVENTFD_FAILED = 0x30001,              //创建eventfd失败
    WT_FORK_FAILED = 0x30002,                        //fork进程失败
    WT_FORK_SERVER_FAILED = 0x30003,                 //fork Server 进程失败
    WT_LAUNCH_ERROR = 0x30004,                       //程序启动错误
    WT_ADMIN_CONN_EXIT = 0x30005,                    //已经存在管理连接
    WT_IP_ERROR = 0x30006,                           //Incorrect IP Address
    WT_RESOURCES_REPEAT_USED = 0x30007,              //子仪器划分资源冲突，重复分配
    WT_RESOURCES_OUT_OF_RANGE = 0x30008,             //子仪器划分,资源分配超过了license支持的范围
    WT_GET_LOG_ERROR = 0x30009,                      //获取日志数据失败
    WT_GET_GUI_VERSION_FAILED = 0x3000A,             //获取全部GUI文件版本信息失败
    WT_DIR_OPEN_ERROR = 0x3000B,                     //路径不存在
    WT_SAME_SEGMENT = 0x3000C,                       //虚拟IP，DUT IP，设备的IP三者之间冲突，使用了相同的网段IP
    WT_SUBIP_INSUFFICIENT = 0x3000D,                 //子网口设置时，虚拟IP没有设置全
    WT_ROLLBACK_TYPE_ERROR = 0x3000E,                //固件回退输入的回退版本序号不正确

    //升级操作的错误码
    WT_UPGRADE_PACKAGE_SIZE = 0x30010,               //升级包长度错误，不足1040字节
    WT_UPGRADE_FPGA_SIZE = 0x30011,                  //FPGA升级文件长度错误
    WT_UPGRADE_FPGA_CRC = 0x30012,                   //FPGA升级文件CRC校验错误
    WT_UPGRADE_FPGA_CHECKSUM = 0x30013,              //FPGA升级文件Checksum校验错误
    WT_UPGRADE_FPGA_VERSION = 0x30014,               //FPGA升级文件版本过低
    WT_UPGRADE_SERVER_SIZE = 0x30015,                //SERVER升级文件长度错误
    WT_UPGRADE_SERVER_CRC = 0x30016,                 //SERVER升级文件CRC校验错误
    WT_UPGRADE_SERVER_CHECKSUM = 0x30017,            //SERVER升级文件Checksum校验错误
    WT_UPGRADE_SERVER_VERSION = 0x30018,             //SERVER升级文件版本过低
    WT_UPGRADE_NO_FILE = 0x30019,                    //系统当前没有合法的升级文件
    WT_UPGRADE_NO_EPCS64 = 0x3001A,                  //无法找到EPCS64
    WT_UPGRADE_MEM_ADDR = 0x3001B,                   //内存地址不合法
    WT_UPGRADE_CONFIG_FILE_NOT_EXIT = 0x3001C,       //config文件不存在
    WT_UPGRADE_NETCONF_FILE_NOT_EXIT = 0x3001D,      //网络配置文件不存在
    WT_UPGRADE_NO_PACKAGE = 0x3001E,                 //升级包不存在
    WT_UPGRADE_NO_MEM = 0x3001F,                     //申请不到内存
    WT_UPGRADE_OPEN_FILE = 0x30020,                  //打开指定的文件失败
    WT_UPGRADE_TAR_GZ_SIZE = 0x30021,                //tar.gz升级文件长度错误
    WT_UPGRADE_TAR_GZ_CRC = 0x30022,                 //tar.gz升级文件CRC校验错误
    WT_UPGRADE_TAR_GZ_CHECKSUM = 0x30023,            //tar.gz升级文件Checksum校验错误
    WT_UPGRADE_VERSION_LOW = 0x30024,                //升级包版本号过低
    WT_UPGRADE_FILENAME_ERROR = 0x30025,             //升级包文件名错误
    WT_UPGRAED_VERSION_NOUPDATE = 0x30026,           //升级包文件内容与当前仪器使用的版本一致
    WT_UPGRADE_SAME_MANGER_VERSION = 0x30027,        //WT-Manager版本与当前仪器使用的版本一致
    WT_UPGRADE_MANAGER_VERSION_LOW = 0x30028,        //WT-Manager的版本过低
    WT_NO_BUSINESS_UNIT = 0x30029,                   //获取实际的vsg或者vsa资源为0，（或者不存在），返回对应的错误信息
    WT_REAL_RES_TOO_LESS = 0x3002A,                  //子仪器划分申请的资源比实际存在的硬件资源要多
    WT_UPGRADE_FILE_SIZE_ERR = 0x3002B,              //升级文件长度错误
    WT_UPGRADE_CRC_ERR = 0x3002C,                    //升级文件CRC校验错误
    WT_UPGRADE_CHECKSUM_ERR = 0x3002D,               //升级文件Checksum校验错误
    WT_UPGRADE_VSA_FPGA_FAIL = 0x3002E,              //VSA FPGA升级失败
    WT_UPGRADE_VSG_FPGA_FAIL = 0x3002F,              //VSG FPGA升级失败
    WT_UPGRADE_BACK_FPGA_FAIL = 0x30030,             //BACK FPGA升级失败
    WT_UPGRADE_VERSION_INVALD = 0x30031,             //升级版包本无效
    WT_UPGRADE_VERSION_ERROE = 0x30032,              //升级包与仪器类型不匹配
    WT_UPGRADE_MODULE_CHECK_ERROE = 0x30033,         //小模块升级包检查失败，按正常升级包升级

    WT_LICENSE_DEV_TYPE_ERROR = 0x30100,             //升级lic的仪器类型不对应
    WT_LICENSE_NUM_ERROR = 0x30101,                  //lic 数量错误，文件不正确~
    WT_LICENSE_NO_BASE_LIC = 0x30102,                //没有base lic
    WT_LICENSE_RELATIONSHIP_ERROR = 0x30103,         //lic依赖关系不正确
    WT_LICENSE_TOO_SHORT = 0x30104,                  //当前升级包license比仪器中的license有效时间短或者一致，没必要升级
    WT_LICENSE_RELAY_TIME_ERR = 0x30105,             //被依赖的lic有效时间段没有再依赖lic时间段内

    WT_CPLD_UPGRADE_OK = 0x30200,                    //CPLD升级操作的错误码
    WT_CPLD_CHECK_CHAIN_FAIL = 0x30201,              //Checking chain failure
    WT_CPLD_IDCODE_ERROR = 0x30202,                  //Reading IDCODE failure
    WT_CPLD_USERCODE_ERROR = 0x30203,                //Reading USERCODE failure
    WT_CPLD_USECODE_ERROR = 0x30204,                 //Reading UESCODE failure
    WT_CPLD_ENTER_ISP_FAIL = 0x30205,                //Entering ISP failure
    WT_CPLD_UNRECOGNIZED_DEVICE = 0x30206,           //Unrecognized device
    WT_CPLD_REVISION_NO_SUPPORT = 0x30207,           //Device revision is not supported
    WT_CPLD_ERASE_FAIL = 0x30208,                    //Erase failure
    WT_CPLD_NOT_BLANK = 0x30209,                     //Device is not blank
    WT_CPLD_PROGRAM_FAIL = 0x3020A,                  //Device programming failure
    WT_CPLD_VERIFY_FAIL = 0x3020B,                   //Device verify failure
    WT_CPLD_READ_FAIL = 0x3020C,                     //Read failure
    WT_CPLD_CHECKSUM_FAIL = 0x3020D,                 //Calculating checksum failure
    WT_CPLD_SET_SECURITY_FAIL = 0x3020E,             //Setting security bit failure
    WT_CPLD_QUERYING_SECURITY_FAIL = 0x3020F,        //Querying security bit failure
    WT_CPLD_EXITING_ISP_FAIL = 0x30210,              //Exiting ISP failure
    WT_CPLD_PERFORM_SYS_TEST_FAIL = 0x30211,         //Performing system test failure
    WT_CPLD_UNKONW_ERROR = 0x30212,                  //Unknown exit code
    WT_CPLD_VERSION_ERROR = 0x30213,                 //版本不支持CPLD升级
    WT_CPLD_RUNNING = 0x30214,                       //正在进行CPLD升级

    /**************FPGA升级FT4222错误码************/
    //FT4222操作错误
    WT_HW_UPGRADE_FT4222_OK = 0x30300,
    WT_HW_UPGRADE_FT4222_INVALID_HANDLE,
    WT_HW_UPGRADE_FT4222_DEVICE_NOT_FOUND,
    WT_HW_UPGRADE_FT4222_DEVICE_NOT_OPENED,
    WT_HW_UPGRADE_FT4222_IO_ERROR,
    WT_HW_UPGRADE_FT4222_INSUFFICIENT_RESOURCES,
    WT_HW_UPGRADE_FT4222_INVALID_PARAMETER,
    WT_HW_UPGRADE_FT4222_INVALID_BAUD_RATE,
    WT_HW_UPGRADE_FT4222_DEVICE_NOT_OPENED_FOR_ERASE,
    WT_HW_UPGRADE_FT4222_DEVICE_NOT_OPENED_FOR_WRITE,
    WT_HW_UPGRADE_FT4222_FAILED_TO_WRITE_DEVICE,
    WT_HW_UPGRADE_FT4222_EEPROM_READ_FAILED,
    WT_HW_UPGRADE_FT4222_EEPROM_WRITE_FAILED,
    WT_HW_UPGRADE_FT4222_EEPROM_ERASE_FAILED,
    WT_HW_UPGRADE_FT4222_EEPROM_NOT_PRESENT,
    WT_HW_UPGRADE_FT4222_EEPROM_NOT_PROGRAMMED,
    WT_HW_UPGRADE_FT4222_INVALID_ARGS,
    WT_HW_UPGRADE_FT4222_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_OTHER_ERROR,
    WT_HW_UPGRADE_FT4222_DEVICE_LIST_NOT_READY,
    // FT_STATUS extending message
    WT_HW_UPGRADE_FT4222_DEVICE_NOT_SUPPORTED = 0x30300 + 1000, 
    WT_HW_UPGRADE_FT4222_CLK_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_VENDER_CMD_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_IS_NOT_SPI_MODE,
    WT_HW_UPGRADE_FT4222_IS_NOT_I2C_MODE,
    WT_HW_UPGRADE_FT4222_IS_NOT_SPI_SINGLE_MODE,
    WT_HW_UPGRADE_FT4222_IS_NOT_SPI_MULTI_MODE,
    WT_HW_UPGRADE_FT4222_WRONG_I2C_ADDR,
    WT_HW_UPGRADE_FT4222_INVAILD_FUNCTION,
    WT_HW_UPGRADE_FT4222_INVALID_POINTER,
    WT_HW_UPGRADE_FT4222_EXCEEDED_MAX_TRANSFER_SIZE,
    WT_HW_UPGRADE_FT4222_FAILED_TO_READ_DEVICE,
    WT_HW_UPGRADE_FT4222_I2C_NOT_SUPPORTED_IN_THIS_MODE,
    WT_HW_UPGRADE_FT4222_GPIO_NOT_SUPPORTED_IN_THIS_MODE,
    WT_HW_UPGRADE_FT4222_GPIO_EXCEEDED_MAX_PORTNUM,
    WT_HW_UPGRADE_FT4222_GPIO_WRITE_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_GPIO_PULLUP_INVALID_IN_INPUTMODE,
    WT_HW_UPGRADE_FT4222_GPIO_PULLDOWN_INVALID_IN_INPUTMODE,
    WT_HW_UPGRADE_FT4222_GPIO_OPENDRAIN_INVALID_IN_OUTPUTMODE,
    WT_HW_UPGRADE_FT4222_INTERRUPT_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_GPIO_INPUT_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_EVENT_NOT_SUPPORTED,
    WT_HW_UPGRADE_FT4222_FUN_NOT_SUPPORT,

    /**************WT-Server错误码************/
    WT_SPEC_DEVID_ERROR = 0x40000,                   //指定的硬件模块ID不存在
    WT_DEV_TYPE_ERROR = 0x40001,                     //指定的设备模块类型不存在
    WT_CREATE_THREAD_FAILED = 0x40002,               //创建线程失败
    WT_LINK_THREAD_EXCEPTION = 0x40003,              //用户连接线程异常
    WT_VSA_CAPTURE_FAILED = 0x40004,                 //vsa采集数据失败
    WT_NO_SIG_DATA = 0x40005,                        //没有信号数据
    WT_OPEN_FILE_FAILED = 0x40006,                   //打开文件失败
    WT_STREAM_ID_ERROR = 0x40007,                    //信号流ID错误
    WT_NOT_ANALYSIS = 0x40008,                       //没有分析信号
    WT_RESULT_NOT_EXIST = 0x40009,                   //结果不存在
    WT_RESULT_UNVALID = 0x4000A,                     //结果无效
    WT_SET_MIMO_DEV_FAILED = 0x4000B,                //配置MIMO从机失败
    WT_VSG_FAILED = 0x4000C,                         //VSG发送失败
    WT_OPEN_WAVE_FAILED = 0x4000D,                   //打开波形文件失败
    WT_SIG_FILE_ERROR = 0x4000E,                     //信号文件内容错误
    WT_GET_FILE_SIZE_FAILED = 0x4000F,               //获取文件大小失败
    WT_MMAP_FILE_FAILED = 0x40010,                   //MMAP文件失败
    WT_MIMO_DATA_UNCOMPLETE = 0x40011,               //MIMO数据不完整

    WT_CONNECT_MIMO_FAILED = 0x40012,                //连接MIMO从机失败
    WT_VSG_GEN_DEMODE_ERROR = 0x40013,               //vsg不支持生成指定模式的信号
    WT_VSA_COMPENSATE_FAILED = 0x40014,              //补偿VSA数据失败
    WT_LINK_TYPE_ERROR = 0x40015,                    //连接类型错误
    WT_LINK_CNT_FULL = 0x40016,                      //连接数已满
    WT_NO_ENOUGH_MODS = 0x40017,                     //没有足够的硬件模块数量
    WT_GET_FAN_SPEED_ERROR = 0x40018,                //获取风扇数据失败
    WT_NO_AVG_RESULT = 0x40019,                      //没有平均结果
    WT_PARAM_NUM_ERROR = 0x4001A,                    //参数个数错误
    WT_NO_PN_ITEM = 0x4001B,                         //没有配置PN
    WT_NEED_SET_MOD = 0x4001C,                       //需要设置模块参数
    WT_MOD_NUM_ERROR = 0x4001D,                      //模块个数错误
    WT_MOD_IS_RUNNING = 0x4001E,                     //模块正在运行
    WT_ALLOC_MOD_TIMEOUT = 0x4001F,                  //申请硬件模块超时
    WT_RETRY_EXCEED_MAX_CNT = 0x40020,               //autorange重试超过最大次数
    WT_MODMASK_PORT_UNVALID = 0x40021,               //RF端口和模块掩码匹配错误，MIMO时使用PORT OFF和mask = 0
    WT_EXEC_CMD_LOCK_TIMEOUT = 0x40022,              //AGC 命令执行加锁超时
    WT_IN_CAL_NO_RF_PORT = 0x40023,                  //没有指定端口号
    WT_IN_CAL_NO_FOUND_FREQ = 0x40024,               //没有指定频率
    WT_IN_CAL_USER_STOP = 0x40025,                   //自动校准被其他线程(用户)中断
    WT_IN_CAL_SIG_FILE_LOSE = 0x40026,               //自校准信号文件不存在
    WT_CANCEL_ALLOC_MOD = 0x40027,                   //用户取消申请资源
    WT_WIDE_BAND_FREQ_ERROR = 0x40028,               //配置的频点不支持开启wideband
    WT_IN_CAL_ERROR = 0x40029,                       //自校准出错
    WT_ADD_BROADCAST_USER_ERROR = 0x40040,           //新增广播用户出错
    WT_BROADCAST_USER_PORT_CONFLICTED = 0x40041,                  //申请硬件模块超时
    
    //外部模块错误码基址
    WT_ALG_BASE_ERROR = 0x50000,                     //vsa算法错误码范围 0x50000 - 0x50FFF
    WT_CAL_BASE_ERROR = 0x51000,                     //校准模块错误码 0x51000 - 0x51FFF
    WT_VSG_ALG_BASE_ERROR = 0x52000,                 //vsg算法错误码范围0x52000 - 0x52FFF

    // 蜂窝错误码
    WT_ALG_3GPP_BASE_ERROR = 0x53000,                //3GPP algorithm error code:0x53000 - 0x54FFF
    WT_3GPP_STANDARD_MISMATCH = 0x53001,             //3GPP standard mismatch
    WT_3GPP_GET_COMMAND_NUM_FAILED = 0x53002,        //get scpi command num failed
    WT_3GPP_COMMAND_NUM_OUT_OF_RANGE = 0x53003,      //scpi command num out of range
    WT_3GPP_GET_VALUE_FAILED = 0x53004,              //get scpi value failed
    WT_3GPP_VALUE_OUT_OF_RANGE = 0x53005,            //scpi value out of range

    //listmod错误吗
    WT_LIST_TXSEQ_START_SCEN_NOT_MATCH = 0x60000,                           //在组合场景下启动listmod tx seq
    WT_LIST_TXSEQ_START_LISTNOENABLE_OR_TXSEQSIZE_IS_ZERO = 0x60001,        //启动tx seq时未使能listmod或tx seq下seg个数为0
    WT_LIST_TXSEQ_START_LIST_SEG_ISNOT_TX = 0x60002,                        //对应的seg不是tx seg
    WT_LIST_RXSEQ_START_SCEN_NOT_MATCH = 0x60003,                           //在组合场景下启动listmod rx seq
    WT_LIST_RXSEQ_START_LISTNOENABLE_OR_RXSEQSIZE_IS_ZERO = 0x60004,        //启动rx seq时未使能listmod或rx seq下seg个数为0
    WT_LIST_RXSEQ_START_LIST_SEG_ISNOT_RX = 0x60005,                        //对应的seg不是rx seg
    WT_LIST_TXRXSEQ_START_SCEN_NOT_MATCH = 0x60006,                         //在非组合场景下启动listmod txrx seq
    WT_LIST_TXRXSEQ_START_LISTNOENABLE_OR_RXSEQSIZE_IS_ZERO = 0x60007,      //启动tx rx seq时未使能listmod或txrx seq下seg个数为0
    WT_LIST_EDIT_SEQ_IN_SEQ_RUNNING = 0x60008,                              //在seq运行时编辑seq
    WT_LIST_GET_SEG_RST_IN_SEG_RUNNING = 0x60009,                           //在seg运行时获取结果
    WT_ERROR = 0xFFFFF
};

#endif
