//*****************************************************************************
//File: wtbackdefine.h
//Describe:背板硬件相关定义
//Author：yuanyongchun
//Date: 2020.10.29
//*****************************************************************************

#ifndef _WT_BACK_DEFINE_H_
#define _WT_BACK_DEFINE_H_


//AD5611晶振
#define BACK_AD5611_TX                  0x0080          //写入到AD5611的数据
#define BACK_AD5611_RX                  0x0084          //从AD5611读出的数据
#define BACK_AD5611_CTRL1               0x0088          //控制寄存器1
#define BACK_AD5611_CTRL2               0x008C          //控制寄存器2

//AD7682用于内部功率检测
#define BACK_AD7682_1_CHANNEL_SEL         0x0068                //获取内部功率，片选
#define BACK_AD7682_1_START               0x006C                //获取内部功率，启动
#define BACK_AD7682_1_RX                  0x0070                //获取内部功率，读寄存器
#define BACK_AD7682_1_STATUS              BACK_AD7682_1_START   //获取内部功率，状态

#define BACK_AD7682_2_CHANNEL_SEL         0x0074               //获取内部功率，片选
#define BACK_AD7682_2_START               0x0078               //获取内部功率，启动
#define BACK_AD7682_2_RX                  0x007C               //获取内部功率，读寄存器
#define BACK_AD7682_2_STATUS              BACK_AD7682_2_START   //获取内部功率，状态

//VSG GAP POWER PORT SELECT
#define BACK_VSG_GAP_POWER_SOLT1        0x03A0               //0x3a0  bit[4]  使能信号  bit[3:0] port选择信号
#define BACK_VSG_GAP_POWER_SOLT2        0x03A4               //0x3a0  bit[4]  使能信号  bit[3:0] port选择信号
#define BACK_VSG_GAP_POWER_SOLT3        0x03A8               //0x3a0  bit[4]  使能信号  bit[3:0] port选择信号
#define BACK_VSG_GAP_POWER_SOLT4        0x03AC               //0x3a0  bit[4]  使能信号  bit[3:0] port选择信号

enum Ad7682ChannelSel
{
    AD7682_CHANNEL_SWITCH_1_PORT_4,
    AD7682_CHANNEL_SWITCH_1_PORT_3,
    AD7682_CHANNEL_SWITCH_1_PORT_2,
    AD7682_CHANNEL_SWITCH_1_PORT_1,
    AD7682_CHANNEL_SWITCH_2_PORT_4,
    AD7682_CHANNEL_SWITCH_2_PORT_3,
    AD7682_CHANNEL_SWITCH_2_PORT_2,
    AD7682_CHANNEL_SWITCH_2_PORT_1,
};

enum Ad7682ChannelSel_VB
{
    AD7682_CHANNEL_VB_SWITCH_1_PORT_2,
    AD7682_CHANNEL_VB_SWITCH_1_PORT_1,
    AD7682_CHANNEL_VB_SWITCH_1_PORT_3,
    AD7682_CHANNEL_VB_SWITCH_1_PORT_4,
    AD7682_CHANNEL_VB_SWITCH_2_PORT_2,
    AD7682_CHANNEL_VB_SWITCH_2_PORT_1,
    AD7682_CHANNEL_VB_SWITCH_2_PORT_3,
    AD7682_CHANNEL_VB_SWITCH_2_PORT_4,
};

enum Ad7682ChannelSel_WT428_VA
{
    AD7682_CHANNEL_WT428_PORT_1,
    AD7682_CHANNEL_WT428_PORT_2,
    AD7682_CHANNEL_WT428_PORT_3,
    AD7682_CHANNEL_WT428_PORT_4,
    AD7682_CHANNEL_WT428_PORT_5,
    AD7682_CHANNEL_WT428_PORT_6,
    AD7682_CHANNEL_WT428_PORT_7,
    AD7682_CHANNEL_WT428_PORT_8,
};

//AD9228用于端口功率检测
#define BACK_AD9228_1_SPI_TX            0x00D8                  //获取端口处功率，写寄存器
#define BACK_AD9228_1_SPI_RX            0x00DC                  //获取端口处功率，读寄存器
#define BACK_AD9228_1_SPI_CR1           0x00E0                  //获取端口处功率，控制寄存器1
#define BACK_AD9228_1_SPI_CR2           0x00E4                  //获取端口处功率，控制寄存器2
#define BACK_AD9228_1_SPI_STATUS        BACK_AD9228_1_SPI_CR1   //获取端口处功率，状态

#define BACK_AD9228_2_SPI_TX            0x00E8                  //获取端口处功率，写寄存器
#define BACK_AD9228_2_SPI_RX            0x00EC                  //获取端口处功率，读寄存器
#define BACK_AD9228_2_SPI_CR1           0x00F0                  //获取端口处功率，控制寄存器1
#define BACK_AD9228_2_SPI_CR2           0x00F4                  //获取端口处功率，控制寄存器2
#define BACK_AD9228_2_SPI_STATUS        BACK_AD9228_2_SPI_CR1   //获取端口处功率，状态

//HM7043背板时钟芯
#define BACK_HM7043_SPI_TX            0x0100                  //获取端口处功率，写寄存器
#define BACK_HM7043_SPI_RX            0x0104                  //获取端口处功率，读寄存器
#define BACK_HM7043_SPI_CR1           0x0108                  //获取端口处功率，控制寄存器1
#define BACK_HM7043_SPI_CR2           0x010C                  //获取端口处功率，控制寄存器2
#define BACK_HM7043_SPI_STATUS        BACK_HM7043_SPI_CR1     //获取端口处功率，状态

//射频板版本号
#define BACK_RF_HW_VER_SEL            0x0024                  //读取射频板版本号，片选
#define BACK_RF_HW_VER                0x0020                  //读取射频板版本号，数值

//WT428射频板版本号
#define WT428_BACK_RF_HW_VER          0x03C0                  //读取射频板版本号，数值
#define WT428_BACK_LO_HW_VER          0x03C4                  //读取射频板版本号，数值
#define WT428_BACK_ETH_HW_VER         0x03C8                  //读取网口板版本号，数值
enum RfHwVerSlotSel
{
    HW_VER_SEL_SLOT1_RF_VERSION,
    HW_VER_SEL_SLOT2_RF_VERSION,
    HW_VER_SEL_SLOT3_RF_VERSION,
    HW_VER_SEL_SLOT4_RF_VERSION,
    HW_VER_SEL_SLOT1_LO_VERSION,
    HW_VER_SEL_SLOT2_LO_VERSION,
    HW_VER_SEL_SLOT3_LO_VERSION,
    HW_VER_SEL_SLOT4_LO_VERSION,
    HW_VER_SEL_SLOT_MAX,
};


//FPGA通用类
#define FPGA_VERSION                     0x0000              //单元板FPGA的版本号
#define  FPGA_COMPILE_DATE                0x0004              //单元板FPGA的版本编译日期(年月日)
#define FPGA_COMPILE_TIME                0x0008              //单元板FPGA的版本编译时间(时分秒)
#define FPGA_MODULE_VERSION              0x000C              //单元板信息
#define FPGA_BP_HW_VERSION               0x0010              //背板硬件版本号
#define FPGA_BB_POWER_OK                 0x0014              //基带板电源标志
#define FPGA_BB_HW_VERSION               0x0018              //基带板硬件版本号
#define FPGA_SW_HW_VERSION               0x001C              //开关板硬件版本号
#define FPGA_REF_CLK_LOCK                0x0028              //参考时钟锁定标志
#define FPGA_S_SYM_RESET                 0x002C              //子系统复位标志
#define FPGA_DEV_CLK_STATE               0x0030              //仪器时钟选择, 0:inter 1:exter

enum BackPlaneSymReset
{
    BACK_RESET_AD5611,
    BACK_RESET_BP_AD7091,
    BACK_RESET_SW_1_AD7091,
    BACK_RESET_SW_2_AD7091,
    BACK_RESET_SW_1_AD9228,
    BACK_RESET_SW_2_AD9228,
    BACK_RESET_HMC7034,
    BACK_RESET_PLL_1,
    BACK_RESET_PLL_2,
    BACK_RESET_SMB_1,
    BACK_RESET_SMB_3,
};

//AT88加密芯片
#define BACK_AT88_START_UP             0x0340              //加密芯片
#define BACK_AT88_I2C_TX0              0x0344              //加密芯片
#define BACK_AT88_I2C_TX1              0x0348              //加密芯片
#define BACK_AT88_I2C_TX2              0x034C              //加密芯片
#define BACK_AT88_I2C_TX3              0x0350              //加密芯片
#define BACK_AT88_I2C_CR1              0x0354              //加密芯片
#define BACK_AT88_I2C_CR2              0x0358              //加密芯片
#define BACK_AT88_I2C_RX0              0x035C              //加密芯片
#define BACK_AT88_I2C_RX1              0x0360              //加密芯片
#define BACK_AT88_I2C_RX2              0x0364              //加密芯片
#define BACK_AT88_I2C_RX3              0x0368              //加密芯片
#define BACK_AT88_I2C_RX4              0x036C              //加密芯片
#define BACK_AT88_I2C_RX5              0x0370              //加密芯片
#define BACK_AT88_I2C_RX6              0x0374              //加密芯片
#define BACK_AT88_I2C_RX7              0x0378              //加密芯片

//FLASH
#define BACK_SW_1_FLASH_WD_START       0x02CC              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_WD_DATA        0x02E8              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_WD_ADDR        0x02D0              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_RD_START       0x02D8              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_RD_LEN         0x02DC              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_RD_DATA        0x02D4              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_RD_ADDR        0x02E0              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_FULL           0x02C0              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_EMTPY          0x02C4              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_ERASE          0x02E4              //SWITCH1 FLASH
#define BACK_SW_1_FLASH_DONE           0x02C8              //SWITCH1 FLASH

#define BACK_SW_2_FLASH_WD_START       0x030C              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_WD_DATA        0x0328              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_WD_ADDR        0x0310              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_RD_START       0x0318              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_RD_LEN         0x031C              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_RD_DATA        0x0314              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_RD_ADDR        0x0320              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_FULL           0x0300              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_EMTPY          0x0304              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_ERASE          0x0324              //SWITCH2 FLASH
#define BACK_SW_2_FLASH_DONE           0x0308              //SWITCH2 FLASH

//AD7091电压检测芯片
#define BACK_BP_AD7091_SPI_TX            0x0090                  //写入到AD7091的数据
#define BACK_BP_AD7091_SPI_RX            0x0094                  //从AD7091读出的数据
#define BACK_BP_AD7091_SPI_CR1           0x0098                  //控制寄存器1
#define BACK_BP_AD7091_SPI_CR2           0x009C                  //控制寄存器2
#define BACK_BP_AD7091_SPI_CONVST        0x00A0                  //AD7091_CONVST
#define BACK_BP_AD7091_SMB_ALRTn         0x0098                  //Alert pin信号bit[2]

#define BACK_SW_1_AD7091_SPI_TX          0x00A8                  //写入到AD7091的数据
#define BACK_SW_1_AD7091_SPI_RX          0x00AC                  //从AD7091读出的数据
#define BACK_SW_1_AD7091_SPI_CR1         0x00B0                  //控制寄存器1
#define BACK_SW_1_AD7091_SPI_CR2         0x00B4                  //控制寄存器2
#define BACK_SW_1_AD7091_SPI_CONVST      0x00B8                  //AD7091_CONVST

#define BACK_SW_2_AD7091_SPI_TX          0x00C0                  //写入到AD7091的数据
#define BACK_SW_2_AD7091_SPI_RX          0x00C4                  //从AD7091读出的数据
#define BACK_SW_2_AD7091_SPI_CR1         0x00C8                  //控制寄存器1
#define BACK_SW_2_AD7091_SPI_CR2         0x00CC                  //控制寄存器2
#define BACK_SW_2_AD7091_SPI_CONVST      0x00D0                  //AD7091_CONVST

//ADT7475 FAN控制芯片
#define BACK_ADT7475_FAN_SMB_1_SLV_ADDR    0x02E0                  //写入到AD7091的数据
#define BACK_ADT7475_FAN_SMB_1_COM_REG     0x02E4                  //从AD7091读出的数据
#define BACK_ADT7475_FAN_SMB_1_TX          0x02E8                  //控制寄存器1
#define BACK_ADT7475_FAN_SMB_1_RX          0x02EC                  //控制寄存器2
#define BACK_ADT7475_FAN_SMB_1_CR1         0x02F0                  //控制寄存器1
#define BACK_ADT7475_FAN_SMB_1_CR2         0x02F4                  //控制寄存器2
#define BACK_ADT7475_FAN_SMB_1_ALRT        0x02F8                  //AD7091_CONVST

// //SW ATT CONTRAL
// #define BACK_SW_1_ATT_SHIFT_0_TX           0x0140                  //写入到shift0的数据
// #define BACK_SW_1_ATT_SHIFT_0_RX           0x0144                  //从shift0读出的数据
// #define BACK_SW_1_ATT_SHIFT_0_CR1          0x0148                  //控制寄存器1
// #define BACK_SW_1_ATT_SHIFT_0_CR2          0x014C                  //控制寄存器2
// #define BACK_SW_1_ATT_SHIFT_0_STATUS       BACK_SW_1_ATT_SHIFT_0_CR1   //shift0总线状态

// #define BACK_SW_ATT_SHIFT_OFFSET           0x0014                  //SHIFT地址偏移

//LED
#define BACK_LED_SHIFT_TX           0x0280                  //写入到shift0的数据
#define BACK_LED_SHIFT_RX           0x0284                  //从shift0读出的数据
#define BACK_LED_SHIFT_CR1          0x0288                  //控制寄存器1
#define BACK_LED_SHIFT_CR2          0x028C                  //控制寄存器2
#define BACK_LED_SHIFT_STATUS       BACK_LED_SHIFT_0_CR1    //shift0总线状态
#define BACK_LED_SHIFT_CLT            0x0294                  //bit0:Led green; bit1:Led red 

//PA AND 42553
#define BACK_SW_1_PA_EN                0X0240
#define BACK_SW_2_PA_EN                0X0244
#define BACK_SW_1_42553_CTL            0X0248
#define BACK_SW_2_42553_CTL            0X024C
#define BACK_SW_PAC_CTL_1              0X0250
#define BACK_SW_PAC_CTL_2              0X0254
#define BACK_SW_S1_CTR_2               BACK_SW_1_PA_EN
#define BACK_SW_T_CTR_3                BACK_SW_2_PA_EN

enum SwitchPaOr42533Sel
{
    SWITCH_PA_OR_42533_PORT_1,
    SWITCH_PA_OR_42533_PORT_2,
    SWITCH_PA_OR_42533_PORT_3,
    SWITCH_PA_OR_42533_PORT_4,
};

//ADF4002 时钟板，REF PLL
#define BACK_RF_PLL_1_TX           0x0110                  //写数据
#define BACK_RF_PLL_1_RX           0x0114                  //读数据
#define BACK_RF_PLL_1_CR1          0x0118                  //控制寄存器1
#define BACK_RF_PLL_1_CR2          0x011C                  //控制寄存器2
#define BACK_RF_PLL_1_STATUS       BACK_RF_PLL_1_CR1       //shift0总线状态

#define BACK_RF_PLL_2_TX           0x0120                  //写数据
#define BACK_RF_PLL_2_RX           0x0124                  //读数据
#define BACK_RF_PLL_2_CR1          0x0128                  //控制寄存器1
#define BACK_RF_PLL_2_CR2          0x012C                  //控制寄存器2
#define BACK_RF_PLL_2_STATUS       BACK_RF_PLL_2_CR1       //shift0总线状态

#define BACK_DIG_IQ_RECV_TRIGGER   0x0390                  //数字IQ接收TRIGGER数
#define BACK_DIG_IQ_SEND_TRIGGER   0x0394                  //数字IQ发送TRIGGER数
#define BACK_DIG_IQ_SEND_GAP       0x0398                  //数字IQ发送TRIGGER间隔
#define BACK_DIG_IQ_SEND_PERIOD    0x039C                  //数字IQ发送TRIGGER周期


#endif //_WT_BACK_DEFINE_H_