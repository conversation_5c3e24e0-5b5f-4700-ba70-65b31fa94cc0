#ifndef _VSA_OFDMA_USER_H_
#define _VSA_OFDMA_USER_H_
#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C" {
#endif
    scpi_result_t GetVsaRstOfdmaInfo(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_UserValid(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_ULDL(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_ToneWide(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_ToneIndex(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_Segment(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_MCS(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_Modulation(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_CodingType(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_CodingRate(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_STBC(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_DCM(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_NSTS(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_NSS(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_PSDUCRC(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_PSDULen(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_AID(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_PowerFactor(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_DataRate(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_Power(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_EVMAll(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_EVMData(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_EVMPilot(scpi_t *context);
    scpi_result_t GetVsaRstOFDMA_Beamformed(scpi_t *context);

    ///////////////////////////////////////////////
    bool GetNSSStartID_11axMUMIMO(UserInfo11ax &User, int &NSS);
    scpi_result_t GetVsaIS_11axMUMIMO(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_RUValid(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_RU_NSTS(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_ULDL(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_ToneWide(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_ToneIndex(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_Segment(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_MCS(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_Modulation(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_CodingType(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_CodingRate(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_STBC(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_DCM(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_NSTS(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_NSS(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_PSDUCRC(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_PSDULen(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_AID(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_PowerFactor(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_DataRate(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_Power(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_EVMAll(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_EVMData(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_EVMPilot(scpi_t *context);
    scpi_result_t GetVsaRst_11axMUMIMO_Beamformed(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif