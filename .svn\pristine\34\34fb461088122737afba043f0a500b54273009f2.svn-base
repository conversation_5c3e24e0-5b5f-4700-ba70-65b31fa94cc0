#ifndef __WT_RESULT_DEF_H__
#define __WT_RESULT_DEF_H__

/*=========================================================================
                                result define
                                notice: max stream length is 32(包含\n结束符)
==========================================================================*/

/* IQ data. Complex vector is returned. */
#define WT_RES_IQ                 "IQ.data"

/* IQ data mv. Complex vector is returned. */
#define WT_RES_IQ_MV               "IQ.data.mv"

/* sampling frequency. Int Value */
#define WT_RES_SMP_FREQ           "sampling.freq"

/* frame data info. for meter */
#define WT_RES_DATA_INFO           "data.info"

/* frame data information plus. for meter */
#define WT_RES_DATA_INFOPLUS        "data.infoplus"

/* data burst information. for meter */
#define WT_RES_DATA_BURST_FIELD_INFO           "data.burst.field.info" 

/* base result */
#define WT_RES_BASE_RESULT         "result.base"

/*mimo base result*/
#define WT_RES_BASE_RESULT_COMPOSITE "result.base.composite"

/*mimo stream real nstsIndex*/
#define WT_RES_STREAM_NSTS_INDEX    "stream.real.nsts.index"

/* demode. Int Value */
#define WT_RES_DEMODE              "demode"

/* signal segment number. Int Value  */
#define WT_RES_SEGMENT_NUM         "segment.num"

/* rf gain. double Value  */
#define WT_RES_RF_GAIN             "gain"

/* external gain. double Value  */
#define WT_RES_EXT_GAIN            "ext_gain"

/* reference power level. double Value  */
#define WT_RES_REF_LEVEL           "ref.level"

/* trigger power level. double Value  */
#define WT_RES_TRIG_LEVEL          "trig.level"

/* SparkLink CtrlInfo. for meter*/
#define WT_RES_SPARK_CTRLINFO      "spark.ctrlinfo"

/* SparkLink SyncSigBin. char* */  
#define WT_RES_SPARK_SYNC          "spark.SyncSigBin"

/* SparkLink CtrlBin. char* */      
#define WT_RES_SPARK_CTRL          "spark.CtrlBin"

/* SparkLink PayloadBin. char*  */      
#define WT_RES_SPARK_PAYLOAD        "spark.PayloadBin"

/*===========================================================================
  CCDF Result
  ===========================================================================*/
/* Maximum number of returned elements for WT_RES_CCDF_PROB and
WT_RES_CCDF_POWER_REL_DB. */
#define WT_MAX_CCDF_ELEMENTS       1000

/* Real vector containing CCDF probability values (Y-axis of CCDF plot) */
#define WT_RES_CCDF_PROB           "CCDF.prob"

/* Real vector containing CCDF power relative to average power in dB values
(X-axis of CCDF plot) */
#define WT_RES_CCDF_POWER_REL_DB   "CCDF.power_rel_dB"

/* ccdf start relative power. double Value dBm */
#define WT_RES_CCDF_START          "CCDF.start"

/* ccdf scale value dBm. double value */
#define WT_RES_CCDF_SCALE          "CCDF.scale"

/* Result for CCDF %10 %1 %0.1 %0.01 percentage‘s power. double vector Value is returned. */
#define WT_RES_CCDF_PERCENT_POWER    "CCDF.percent.power"
/*===========================================================================
  spectrum result
  ===========================================================================*/

/*Carrier leakage in dB.Value is returned*/
#define WT_RES_SPECTRUM_CARRIER_LEAKAGE  "Carrier_leakage"

/*OBW in Hz.Value is returned*/
#define WT_RES_SPECTRUM_OBW       "Obw"

/*OBW start Freq in Hz,int value*/
#define WT_RES_SPECTRUM_OBW_START_FREQ       "Obw_start_freq"

/*OBW end Freq in Hz,int value*/
#define WT_RES_SPECTRUM_OBW_END_FREQ       "Obw_end_freq"

//8080 第二段的obw，8080专用
/*OBW in Hz.Value is returned*/
#define WT_RES_SPECTRUM_OBW1       "Obw1"

/*OBW start Freq in Hz,int value*/
#define WT_RES_SPECTRUM_OBW_START_FREQ1       "Obw_start_freq1"

/*OBW end Freq in Hz,int value*/
#define WT_RES_SPECTRUM_OBW_END_FREQ1       "Obw_end_freq1"
//end 8080 obw

/*Spectrum mask error point in %.Value is returned*/
#define WT_RES_SPECTRUM_MASK_ERR  "Spec_mask_err"

/*Frequency of the max power in Hz.Value is returned*/
#define WT_RES_SPECTRUM_PEAK_FREQ  "Spec_peak_freq"

/*power of the max power in dBm.Value is returned*/
#define WT_RES_SPECTRUM_PEAK_POWER  "Spec_peak_power"

/* Y-axis values, power in dBm. Vector is returned. */
#define WT_RES_SPECTRUM_Y           "y"

/* spectrum maskm, power in dBm. Vector is returned */
#define WT_RES_SPECTRUM_MASK        "spectrum.mask"

/* spectrum rbw, value in HZ. Int Value */
#define WT_RES_SPECTRUM_RBW         "spectrum.rbw"

/* Resolution bandwidth used in calculations. Int Value is returned. */
#define WT_RES_SPECTRUM_RES_BW      "res_bw"

/*Center Frequency in Hz.Value is returned*/
#define WT_RES_SPECTRUM_FREQ_CENTER "Freq_center"

/*Frequency span in Hz. Int Value is returned*/
#define WT_RES_SPECTRUM_FREQ_SPAN   "Freq_span"

/*Spectrum margin data. Complex Vector is returned*/
#define  WT_RES_SPECTRUM_MARGIN_DATA  "Spec_margin_data"

//WT_RES_SPECTRUM_Y_500M和WT_RES_SPECTRUM_Y一致，WT_RES_SPECTRUM_MASK_500M和WT_RES_SPECTRUM_MASK内容一致，由于历史发展原因，都保留
/* 500M spectrum Y-axis values, power in dBm. Vector is returned. */
#define WT_RES_SPECTRUM_Y_500M           "y_500M"

/* 500M spectrum maskm, power in dBm. Vector is returned */
#define WT_RES_SPECTRUM_MASK_500M        "spectrum.mask_500M"
/*===========================================================================
  power result
  ===========================================================================*/

/* RMS Power in dB, no gap. */
#define WT_RES_RMS_DB_NO_GAP       "rms_db_nogap"

/* RMS Power in dB. */
#define WT_RES_RMS_DB              "rms_db"

/* Frame Count by Power Detectoring. Int Value is returned. */
#define WT_RES_POWER_FRAME_COUNT   "pow.frame.count"

/* Power Peak. Vector is returned. */
#define WT_RES_POWER_PEAK          "pow.peak"

/* points power. Vector is returned */
#define WT_RES_POINTS_POWER        "points.power"

/* window avg power. Vector is returned */
#define WT_RES_WIN_AVG_POWER       "avg.power"

/*===========================================================================
  11a 11ac 11n preamble freqErr
  ===========================================================================*/
/* 160 points data, vector is returned, double value*/
#define WT_RES_PREAMBLE_FREQ_ERR_HZ "preamble.freqErr_hz"

/* 160 points data, vector is returned, int value*/
#define WT_RES_PREAMBLE_FREQ_ERR_FLAG "preamble.freqErr.validFlag"

/*===========================================================================
  OFDM & 11B result
  ===========================================================================*/

/* 11b LO(DC) Leakage rbw. Int Value is returned. */
#define WT_RES_LO_LEAKAGE_RBW      "spectrum.lo.leakage.rbw"

/* 11b LO(DC) Leakage span. Int Value is returned. */
#define WT_RES_LO_LEAKAGE_SPAN      "spectrum.lo.leakage.span"

/* 11b LO(DC) Leakage .Vector is returned.double Value*/
#define WT_RES_LO_LEAKAGE          "spectrum.lo.leakage"

/* Real double vector is returned */
/* Time vector for the data points in WT_RES_11B_FREQ_ERR */
#define WT_RES_11B_FREQ_ERR_TIME    "b11.freqErrTimeVect"

/* Real double vector is returned */
/* 11b Preamble Frequency Error in Hz,Vector is returned. */
#define WT_RES_11B_FREQ_ERR     "b11.freqErr_hz"

/* Error code while analyzing EVM invalid. int Value is returned. */
#define WT_RES_EVM_INVALID_ERROR_CODE             "evm.error_code"

/* EVM for entire frame. Value is returned. */
#define WT_RES_EVM_ALL             "evm.all"

/* EVM for entire frame(%). Value is returned. */
#define WT_RES_EVM_ALL_PERCENT     "evm.all(%)"

/* EVM peak value. Value is returned. */
#define WT_RES_EVM_PEAK            "evm.pk"

/* EVM peak value(%). Value is returned. */
#define WT_RES_EVM_PEAK_PERCENT    "evm.pk(%)"

/* Frequency Error in Hz. Value is returned. */
#define WT_RES_FREQ_ERR_HZ         "signal.freqerr_hz"

/* Symbol Clock Error in ppm. Value is returned. */
#define WT_RES_SYMBOL_CLOCK_ERR    "signal.symclockerr"

/* IQ Match Amplitude Error in dB. Value is returned. */
#define WT_RES_IQ_MATCH_AMP_DB      "iqmatch.amp_dB"

/* IQ Match Phase Error in deg. Value is returned. */
#define WT_RES_IQ_MATCH_PHASE      "iqmatch.phase"

/* IQ Chanel DC Offset in dB. Value is returned. */
#define WT_RES_IQ_DC_OFFSET         "iq.offset"

/* const data in dB. Complex Vector is returned */
#define WT_RES_CONST_DATA          "const.data"

/* pilot const data in dB. Complex Vector is returned */
#define WT_RES_CONST_PILOT         "const.pilot"

/* reference const data in dB. Complex Vector is returned */
#define WT_RES_CONST_REF           "const.ref"

/* symbol evm avg. Vector is returned */
#define WT_RES_EVM_SYM_AVG         "evm.symbol.avg"

/* symbol evm. Vector is returned */
#define WT_RES_EVM_SYM             "evm.symbol"

/* symbol evm pilot. Complex Vector is returned */
#define WT_RES_EVM_SYM_PILOT       "evm.symbol.pilot"

/* carrier evm avg. Vector is returned */
#define WT_RES_EVM_CARRIER_AVG     "evm.carrier.avg"

/* pilot carrier evm avg. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT_AVG     "evm.carrier.pilot.avg"

/* carrier evm. Vector is returned */
#define WT_RES_EVM_CARRIER         "evm.carrier"

/* pilot carrier evm. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT   "evm.carrier.pilot"


/* symbol evm avg db. Vector is returned */
#define WT_RES_EVM_SYM_AVG_DB         "evm.symbol.avg.db"

/* symbol evm db. Vector is returned */
#define WT_RES_EVM_SYM_DB             "evm.symbol.db"

/* symbol evm pilot db. Vector is returned */
#define WT_RES_EVM_SYM_PILOT_DB       "evm.symbol.pilot.db"

/* carrier evm avg db. Vector is returned */
#define WT_RES_EVM_CARRIER_AVG_DB     "evm.carrier.avg.db"

/* pilot carrier evm avg db. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT_AVG_DB "evm.carrier.pilot.avg.db"

/* carrier evm db. Vector is returned */
#define WT_RES_EVM_CARRIER_DB         "evm.carrier.db"

/* pilot carrier evm db. Vector is returned */
#define WT_RES_EVM_CARRIER_PILOT_DB   "evm.carrier.pilot.db"

/* LTF SNR result,unit:dB, double value is retured*/
#define WT_RES_LTF_SNR_DB         "signal.LTF.SNR_dB"

/* PSDU SNR result,unit:dB, double value is retured*/
#define WT_RES_PSDU_SNR_DB         "signal.PSDU_SNR_dB"

/* 11ax ltf type, int value is returned */
#define WT_RES_LTF_SIZE              "LTF.size"
/*========================================================================
 * 11ax SigB result
 * =======================================================================*/
/* symbol const data in dB. Complex Vector is returned */
#define WT_RES_SIGB_CONST_DATA      "sigb.const.data"

/* symbol const pilot in dB. Complex Vector is returned */
#define WT_RES_SIGB_CONST_PILOT     "sigb.const.pilot"

/* symbol reference const data in dB. Complex Vector is returned */
#define WT_RES_SIGB_CONST_REF       "sigb.const.ref"

/*sigb symbol evm . Vector is returned */
#define WT_RES_SIGB_EVM_SYM         "sigb.evm.symbol"

/*sigb carrier evm. Vector is returned */
#define WT_RES_SIGB_EVM_CARRIER     "sigb.evm.carrier"

/* Single element double Value */
/*sigb all evm*/
#define WT_RES_SIGB_EVM_ALL         "sigb.evm.all"

/* Single element double Value */
/*sigb pilot evm*/
#define WT_RES_SIGB_EVM_PILOT_DB    "sigb.evm.pilot"

/* Single element double Value */
/*sigb data evm*/
#define WT_RES_SIGB_EVM_DATA_DB     "sigb.evm.data"

/* double Value */
#define WT_RES_SIGB_EVM_ALL_COMPOSITE         "sigb.evm.all.composite"

/* double Value */
#define WT_RES_SIGB_EVM_PILOT_DB_COMPOSITE    "sigb.evm.pilot.composite"

/* double Value */
#define WT_RES_SIGB_EVM_DATA_DB_COMPOSITE     "sigb.evm.data.composite"

/*===========================================================================
  11ax user result
  ===========================================================================*/
/*all user's const data*/
#define WT_RES_ALL_USER_CONST_DATA  "all.user.const.data"

/*all user's const ref*/
#define WT_RES_ALL_USER_CONST_REF   "all.user.const.ref"

/*all user's const pilot*/
#define WT_RES_ALL_USER_CONST_PILOT "all.user.const.pilot"

/*specific user's const. data*/
#define WT_RES_11AX_USER_CONST_DATA     "user.const.data"

/*specific user's const ref*/
#define WT_RES_11AX_USER_CONST_REF      "user.const.ref"

/*specific user's const.pilot*/
#define WT_RES_11AX_USER_CONST_PILOT    "user.const.pilot"

/*11ax psdu format*/
#define WT_RES_11AX_PSDU_FORMAT         "ax.psdu.format"
/*========================================================================
 * 11ax SigA result
 * =======================================================================*/
/*siga color bit,int Value*/
#define WT_RES_11AX_SIGA_BSS_COLOR     "siga.bss.color"

/*L-sig bit*/
#define WT_RES_11AX_L_SIG_BIT           "ax.L-sig.bit"

/*HE-Sig-A Bit*/
#define WT_RES_11AX_SIG_A_BIT           "ax.siga.bit"

/*========================================================================
 * 11ax SigB result
 * =======================================================================*/
/*HE-Sig-B bit 1,only mu ppdu have*/
#define WT_RES_11AX_SIG_B_BIT1          "ax.sigb.bit1"

/*HE-Sig-B bit 2,only mu ppdu have*/
#define WT_RES_11AX_SIG_B_BIT2          "ax.sigb.bit2"

/*========================================================================
 * 11be SigA result
 * =======================================================================*/
/*L-sig bit*/
#define WT_RES_11BE_L_SIG_BIT           "be.L-sig.bit"

/*BE-U-Sig Bit*/
#define WT_RES_11BE_U_SIG_BIT           "be.U-sig.bit"

/*========================================================================
 * 11be SigB result
 * =======================================================================*/
/*BE-Sig-ctx1 bit 1,only mu ppdu have*/
#define WT_RES_11BE_SIG_CTX1_BIT          "be.sig1.bit"

/*BE-Sig-ctx2 bit 2,only mu ppdu have*/
#define WT_RES_11BE_SIG_CTX2_BIT         "be.sigb2.bit"

/*========================================================================
 * 11ax TB result
 * =======================================================================*/
/*trigger base unused tone error,struct stTbUnusedToneError result is return*/
#define WT_RES_TB_UNUSED_TONE_ERROR    "tb.unused_tone_error"

/*trigger frame info, struct TriggerFrameSetting is return*/
#define WT_RES_TRIGGER_FRAME_INFO           "trigger_frame.info"
/*===========================================================================
  11B result
  ===========================================================================*/

/* Ramp on time. Value is returned. */
#define WT_RES_RAMP_ON_TIME        "ramp.on_time"

/* Ramp off time. Value is returned. */
#define WT_RES_RAMP_OFF_TIME       "ramp.off_time"

/* Single element double Value */
/* Phase Error */
#define WT_RES_11B_PHASE_ERR        "b11.phase_err"

/* Single element double Value */
/* Bit Rate, see 802.11b standard */
#define WT_RES_11B_BIT_RATE        "b11.PLCP_info.bit_rate"

/* Single element double Value */
/* Carrier Suppression */
#define WT_RES_11B_CARR_SUPPRESSION       "b11.carrier_suppression"

/* 11b eye data. st11B_eye Vector */
#define WT_RES_11B_EYE             "b11.eye"

/*11b 频偏余量ppm, double value is return */
#define WT_RES_11B_FREQ_ERR_MARGIN           "b11_freqerr_margin"

/*11b 采样偏余量 ppm, double value is return */
#define WT_RES_11B_CLOCK_ERR_MARGIN           "b11_clockerr_margin"

/* power on ramp data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_INST  "ramp.on_power_inst"

/* power on ramp peak data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_PEAK  "ramp.on_power_peak"

/* power on ramp mask1 data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_MARK1 "ramp.on_power_mask1"

/* power on ramp mask2 data. Complex Vector */
#define WT_RES_RAMP_ON_POWER_MARK2 "ramp.on_power_mask2"

/* power down ramp data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_INST "ramp.off_power_inst"

/* power down ramp peak data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_PEAK "ramp.off_power_peak"

/* power down ramp mask1 data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_MARK1 "ramp.off_power_mask1"

/* power down ramp mask2 data. Complex Vector */
#define WT_RES_RAMP_OFF_POWER_MARK2 "ramp.off_power_mask2"

/* evm vs time data. double vector */
#define WT_RES_EVM_TIME            "evm.time"

/* evm vs time avg data. double vector */
#define WT_RES_EVM_TIME_AVG        "evm.time.avg"

/* evm vs time chip info. st11BChip data */
#define WT_RES_EVM_TIME_CHIP       "evm.chip"

/*===========================================================================
  OFDM result
  ===========================================================================*/

/* Single element double Value */
/* Phase Error */
#define WT_RES_OFDM_PHASE_ERR        "ofdm.phase_err"

/* Number of Bytes in PSDU. Int Value */
#define WT_RES_PSDU_LENGTH          "psdu_length"

/* Single element double Value */
/* Data rate in Mbps */
#define WT_RES_OFDM_DATA_RATE_MBPS "ofdm.PLCP.Data_rate_Mbps"

/* Number of symbols. Int Value */
#define WT_RES_OFDM_NUMBER_SYMBOLS "ofdm.PLCP.Nspp"

/* EVM for data part of frame.dB. Value is returned. */
#define WT_RES_EVM_DATA_DB            "evm.data"

/* EVM for pilot part of frame.dB. Value is returned. */
#define WT_RES_EVM_PILOT_DB           "evm.pilot"

/*Spectrum Flatness Passed Or Failed. Int Value is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED    "Spec_flatness_passed"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_OFDM_SPECTRUM_FLATNESS_DATA     "Spec_flatness_data"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA      "Spec_flatness_maskup_data"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA    "Spec_flatness_maskdown_data"

/*Spectrum Flatness Section Value. Complex Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE    "Spec_flatness_section_value"

/*Spectrum Flatness Section Margin. Complex Vector is returned*/
#define WT_RES_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN    "Spec_flatness_section_margin"

#define WT_RES_OFDM_CHANNEL_PHASE_RESPONSE  "Channel_phase_response"
#define WT_RES_OFDM_CHANNEL_AMPLITUDE_RESP  "Channel_amplitude_response"
#define WT_RES_OFDM_SYMBOL_PHASE_ERROR      "Symbol.PhaseError"
#define WT_RES_OFDM_SYMBOL_AMPLITUDE        "Symbol.Amplitude"

/* APEP length. struct Value return ,support by ac ax be*/
#define WT_RES_APEP_LENGTH          "APEP_length"
/*===========================================================================
  ZigBee result
  ===========================================================================*/

/* Single element double vector */
/* Phase Error */
#define WT_RES_ZIGBEE_PHASE_ERR                "zigbee.phase_err"
#define WT_RES_ZIGBEE_EVM_PSDU                 "zigbee.evm(psdu)"
#define WT_RES_ZIGBEE_EVM_PSDU_PERCENT         "zigbee.evm(psdu)_percent"
#define WT_RES_ZIGBEE_EVM_SHRPHR               "zigbee.evm(shr+phr)"
#define WT_RES_ZIGBEE_EVM_SHRPHR_PERCENT       "zigbee.evm(shr+phr)_percent"
#define WT_RES_ZIGBEE_EVM_OFFSET_DB            "zigbee.evm_offset"
#define WT_RES_ZIGBEE_EVM_OFFSET_PERCENT       "zigbee.evm_offset_percent"
/* st11Zigbee_eye vector is returned*/
#define WT_RES_ZIGBEE_EYE_REAL                 "zigbee.eye_real"
/* st11Zigbee_eye vector is returned*/
#define WT_RES_ZIGBEE_EYE_IMAG                 "zigbee.eye_imag"
/* double vector is returned, phase_err vs chip*/
#define WT_RES_ZIGBEE_PHASE_ERR_VS_CHIP        "zigbee.phase_err.chip"
/*===========================================================================
 Bluetooth result
 ===========================================================================*/

/* freq carrier offset of each burst detected, in Hz. */
/* stBT_FreqErr vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_BUF             "freq_est buffer"

/* Initial freq carrier drift of each burst detected, in Hz. */
/* Real vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_DRIFT          "freq_drift"

/* Initial freq carrier drift of each burst detected, in Hz. */
/* Real vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_DRIFT_RATE     "freq_drift_rate"

/* Initial freq offset of each burst detected, in Hz. */
/* Real vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_CARR_FREQ_TOL            "freq_est"

/* The measurement result for deltaF1Avg as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2
Requires 00001111 data pattern. Result in Hz */
#define WT_RES_BT_DELTA_F1_AVG             "deltaF1Average"

/* WT_RES_BT_DELTA_F1_AVG is valid or not. Int Value
1 - valid
0 - invalid */
#define WT_RES_BT_DELTA_F1_VALID             "deltaF1Valid"

/* The measurement result for deltaF2Max as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2
Requires alternating data pattern. Result in Hz */
#define WT_RES_BT_DELTA_F2_MAX             "deltaF2Max"

/* The measurement result for deltaF2Avg as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2
Requires alternating data pattern. Result in Hz */
#define WT_RES_BT_DELTA_F2_AVG             "deltaF2Average"

/* WT_RES_BT_DELTA_F2_MAX(WT_RES_BT_DELTA_F2_AVG) is valid or not. Int Value
1 - valid
0 - invalid */
#define WT_RES_BT_DELTA_F2_VALID             "deltaF2Valid"

/* Indicates validity of WT_RES_BT_RMS_DEVM. Int Value */
#define WT_RES_BT_RMS_DEVM_VALID            "EdrEVMvalid"

/* RMS Differential EVM value (EDR only). */
/* See BlueTooth Testing Documentation. */
/* Single element real vector is returned. */
#define WT_RES_BT_RMS_DEVM                 "EdrEVMAv"

/* Pk Differential EVM value (EDR only). */
/* See BlueTooth Testing Documentation. */
/* Single element real vector is returned. */
#define WT_RES_BT_PK_DEVM                  "EdrEVMpk"

/* Relative power of EDR section to FM section of packet, in dB. */
#define WT_RES_BT_EDR_REL_PWR               "EdrPowDiffdB"

/* The percentage of symbols with EVM below the threshold. Threshold
for 2 Mbps is 0.3 for 3 Mbps is 0.2 ，EDR DEVM < 30%(pi/4-DQPSK),EDR DEVM < 20%(8DPSK)*/
#define WT_RES_BT_EDR_PROB_99_EVM_PASS "EdrprobEVM99pass"

/* The percentage of symbols with EVM below the threshold. 取DEVM数组中索引99%的值作为结果 ,EEDR 99% DEVM*/
#define WT_RES_BT_EDR_PROB_99_EVM_PASS_PERCENT "EdrprobEVMpassPct"

/* The percentage of symbols with EVM below the threshold. Threshold
for 2 Mbps is 0.3 for 3 Mbps is 0.2 ，EDR DEVM < 30%(pi/4-DQPSK),EDR DEVM < 20%(8DPSK)*/
#define WT_RES_BT_EDR_PROB_99_EVM_PASS_PERCENT "EdrprobEVMpassPct"

/* NEW: In release 1.2 of the Bluetooth Software:
Max DEVM Average as specified in: BLUETOOTH TEST SPECIFICATION
Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2. stBT_DEVM vector returned*/
#define WT_RES_BT_MAX_DEVM_AVG             "EdrEVMvsTime"

/* Omega_i value (EDR only). */
/* Single element real vector is returned. */
#define WT_RES_BT_EDR_OMEAG_I                   "Omega_i"

/* Omega_o value (EDR only). */
/* Single element real vector is returned. */
#define WT_RES_BT_EDR_OMEAG_O                   "Omega_o"

/* Omega_i0 value (EDR only). */
/* Single element real vector is returned. */
#define WT_RES_BT_EDR_OMEAG_IO                  "Omega_io"

/* Bandwidth-20dB Passed Or Failed. Int Value */
/* A value of 1 indicates BW20dB passed. */
/* A value of 0 indicates BW20dB doesn't passed, and thus empty. */
/* Single element vector is returned. See BlueTooth Testing Documentation. */
#define WT_RES_BT_BANDWIDTH_20DB_Passed     "BW20dB_Passed"

/* 20 dB bandwidth value Hz. See BlueTooth Testing Documentation. */
/* stBT_BW20dB vector is returned. */
#define WT_RES_BT_BANDWIDTH_20DB           "bandwidth20dB"

/* Bandwidth-20dB rbw HZ. double value */
#define WT_RES_BT_BANDWIDTH_20DB_RBW    "bandwidth20dBRbw"

/* Bandwidth-20dB obw HZ. double value */
#define WT_RES_BT_BANDWIDTH_20DB_OBW    "bandwidth20dBObw"

/* Bandwidth-20dB start freqency that meets the threshold, double valude is return*/
#define WT_RES_BT_BANDWIDTH_20DB_FREQ_LOW   "bandwidth20dB.freq_low"

/* Bandwidth-20dB end freqency that meets the threshold, double valude is return*/
#define WT_RES_BT_BANDWIDTH_20DB_FREQ_HIGH   "bandwidth20dB.freq_high"

//BT BLE FnMax,Hz
#define WT_RES_BT_BLE_FnMax                 "FnMax"

//BT BLE F0FnMax,Hz
#define WT_RES_BT_BLE_F0FnMax               "F0FnMax"

//BT BLE Delta_F1F0,Hz
#define WT_RES_BT_BLE_Delta_F1F0            "Delta_F1F0"

//BT BLE F0Fn5_Max,Hz
#define WT_RES_BT_BLE_F0Fn5_Max             "FnFn5_Max"

//BT BLE Delta_F1_MAX
#define WT_RES_BT_DELTA_F1_MAX                 "deltaF1Max"

//BT BLE Delta_F0F3,Hz
#define WT_RES_BT_BLE_Delta_F0F3            "Delta_F0F3"

//BT BLE Delta_F0Fn3,Hz
#define WT_RES_BT_BLE_Delta_F0FN3            "Delta_F0Fn3"

//BT BLE Freq Drift Detail Valid. Int Value
#define WT_RES_BT_BLE_DRIFT_DETAIL_VALID    "BLE_drift_detail_valid"

//BT BR/BLE Spectrum adjacent Channel Power,dBm
#define WT_RES_BT_SPEC_ACP             "Spectrum_Acp"

//BT BR/BLE Spectrum adjacent Channel Power mask, Complex vector
#define WT_RES_BT_SPEC_ACP_MASK     "Spectrum_Acp_mask"

//BT BR/BLE名称：DetaF2Max Pass Rate，单位：%（保留两位小数）
#define WT_RES_BT_DELTA_F2_PASS_PERCENT     "Delta_F2_pass_percent"

//BT BR/BDR Freq offset header
#define WT_RES_BT_FREQ_OFFSET_HEADER         "freq_offset_header"

//BT BLE Freq offset sync
#define WT_RES_BT_FREQ_OFFSET_SYNC          "freq_offset_sync"
/* Similar to the measurement result for deltaF1Avg as specified in
BLUETOOTH TEST SPECIFICATION Ver. 1.2/2.0/2.0 + EDR [vol 2] version 2.0.E.2.
BT BR/EDR Result measured from Header data. Result in Hz.*/
#define WT_RES_BT_RES_FREQ_DEV              "freq_deviation"

/* BT BR/EDR Peak to Peak Frequency Deviation, in Hz during header*/
#define WT_RES_BT_RES_FREQ_DEV_PK_TO_PK     "freq_deviationpktopk"

/*BT BR/EDR Delta av access*/
#define WT_RES_BT_DELTA_F2_AVG_ACCESS        "Delta_F2_Av_Access"

/*BT BR/EDR Delta max access*/
#define WT_RES_BT_DELTA_F2_MAX_ACCESS       "Delta_F2_Max_Access"

/*BT PSDU CRC result, int*/
#define WT_RES_BT_PSDU_CRC                  "BT_PSDU_CRC_Status"

#define WT_RES_BT_DELTA_F1_MIN                "deltaF1Min"

#define WT_RES_BT_DELTA_F2_MIN                "deltaF2Min"

#define WT_RES_BT_EDR_MAX_FREQ_VAR            "Max_FreqVar"

/*BT BT BLE F0FnAvg,Hz*/
#define WT_RES_BT_F0FN_AVG                   "BT_F0FnAvg"

/*BT PSDU BIN result, char*/
#define WT_RES_BT_PSDU_BIN                   "BT_PSDU_BIN"

#define WT_RES_BT_PACKET_TYPE                   "BT_Packet_type"

#define WT_RES_BT_PACKET_LENGTH                 "BT_Packet_length"

#define WT_RES_BT_PACKET_DATA_RATE              "BT_Packet_Data_Rate"

#define WT_RES_BT_PACKET_INIT_FREQ_ERR          "BT_Packet_Init_Frequency_Err"

#define WT_RES_BT_DELTA_F1_99p9_PRECENT         "Delta_F1_99.9%"

#define WT_RES_BT_DELTA_F2_99p9_PRECENT         "Delta_F2_99.9%"

#define WT_RES_BT_PACKET_PAYLOAD_HEADER_BITS    "Packet_Payload_Header_bits"

//BT5.2 result

/* BT BR/EDR result, int */
#define WT_RES_BR_EDR_LAP                     "BR_EDR_LAP"

/* BT BR/EDR result, int */
#define WT_RES_BR_EDR_UAP                     "BR_EDR_UAP"

/* BT BLE_Uncoded result, int */
#define WT_RES_BLE_CTEINFO                    "BLE_CTEInfo"

/* BT BLE_Uncoded && CTEInfo=1 result, int */
#define WT_RES_BLE_CTEType                     "BLE_CTEType"

/* BT BLE_Uncoded && CTEInfo=1 result, int */
#define WT_RES_BLE_CTE_DURATIONT                "BLE_CTE_DurationT"

/* BT EDR result, int */
#define WT_RES_BT_EDR_SYNSEQ_ERR_BIT_NUM        "EDR_SynSeq_ErrBit_Num"

/* BT EDR result(2-EV3、2-EV5、3-EV3 and 3-EV5 no such result), int */
#define WT_RES_BT_EDR_TRAILER_ERR_BIT_NUM       "EDR_Trailer_ErrBit_Num"

/* int */
#define WT_RES_BT_LT_ADDR                        "FrmInfo.LT_ADDR"

/* int */
#define WT_RES_BT_FLOW_CTRL                      "FrmInfo.Flow"

/* int */
#define WT_RES_BT_ACK_INDICATION                 "FrmInfo.ARQN"

/* int */
#define WT_RES_BT_SEQ_NUM                        "FrmInfo.SEQN"

/* int */
#define WT_RES_BT_LLID                           "FrmInfo.LLID"

/* int */
#define WT_RES_BT_FLOW                            "FrmInfo.mFlow"

/* int */
#define WT_RES_BT_PAYLOADSIZE                     "FrmInfo.PayLoadSize"

/* int */
#define WT_RES_BT_PAYLOAD_EIR                             "FrmInfo.PayLoadEIR"

/* int */
#define WT_RES_BT_PAYLOAD_SR                              "FrmInfo.PayLoadSR"

/* int */
#define WT_RES_BT_PAYLOAD_CLASS_OF_DEVICE                 "FrmInfo.PayLoadClassofDevice"

/* int */
#define WT_RES_BT_PAYLOAD_LTADDR                          "FrmInfo.PayLoadLTAddr"

/* int */
#define WT_RES_BT_PAYLOAD_CLK27B2                         "FrmInfo.PayLoadCLK27b2"

/* int */
#define WT_RES_BT_BLEMAPPERS                      "FrmInfo.BLEMapperS"

/* VoiceField[10],int */
#define WT_RES_BT_VOICE_FIELD                     "FrmInfo.VoiceField"

#define WT_RES_BT_PAYLOAD_HEADER                    "FrmInfo.PayLoadHeader"

/* int */
#define WT_RES_BT_PATTERN                       "BT_Pattern"

/* BT EDR result, double */
#define WT_RES_EDR_GFSK_POWER                   "EDR_GFSK_power"

/* BT EDR result, double */
#define WT_RES_EDR_DPSK_POWER                   "EDR_DPSK_power"

#define WT_RES_EDR_GFSK_POWER_PEAK               "EDR_GFSK_Power_Peak"

#define WT_RES_EDR_DPSK_POWER_PEAK                "EDR_DPSK_Power_Peak"

/* BT EDR result, double */
#define WT_RES_EDR_GUARD_TIME                    "EDR_GuardTime"

#define WT_RES_EDR_GFSK_POWER_PEAK               "EDR_GFSK_Power_Peak"

#define WT_RES_EDR_DPSK_POWER_PEAK                "EDR_DPSK_Power_Peak"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_PWR_AVG                "BLE_CTE_Pwr_Avg"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_PWR_PEAK               "BLE_CTE_Pwr_Peak"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_PWR_SUB_AVG            "BLE_CTE_Pwr_Sub_Avg"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_MAX                "BLE_CTE_Fsi_Max"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_MIN                 "BLE_CTE_Fsi_Min"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FS1_SUB_FP              "BLE_CTE_Fs1_Sub_Fp"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_SUB_F0_MAX              "BLE_CTE_Fsi_Sub_F0_Max"

/* BT BLE_Uncoded && CTEInfo=1 result, double */
#define WT_RES_BLE_CTE_FSI_SUB_FSI3_MAX              "BLE_CTE_Fsi_Sub_Fsi3_Max"

/* BT BLE_Uncoded && CTEInfo=1 && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_REF_AVG                    "BLE_CTE_Pwr_Ref_Avg"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_REF_Dev                    "BLE_CTE_Pwr_Ref_DevDivAvg"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_REF_Dev_MAX                 "BLE_CTE_Pwr_Ref_DevDivAvg_Max"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result, double */
#define WT_RES_BLE_CTE_FWR_AVG_SLOT                    "BLE_CTE_Pwr_Avg_Slot"

/* BT BR/EDR  result Header_bin[54], int */
#define WT_RES_BR_EDR_HEADERBIN                         "BR_EDR_Header_Bin"

/* BT BLE_Uncoded && CTEInfo=1  && (CTEType=1 || CTEType2) result CTE_Pwr_Avg_Slot[74], double */
#define WT_RES_BLE_CTE_PWR_AVG_SLOT                      "BLE_CTE_Pwr_Avg_Slot"

/* BT BLE Enhanced mode, int value is return*/
#define WT_RES_BLE_ENHANCED_MODE                          "BLE_Enhanced_Mode"

/* BT BLE Advertising result, Delta F2 Avg / Delta F1 Avg, 0.8<= res <=1,double value is return*/
#define WT_RES_BLE_DELTAF2AVG_DIV_DELTAF1AVG                "BLE.DelF2Avg_Div_DelF1Avg"

/* BT Leakage Power,unit is dBm, double value is return */
#define WT_RES_BT_LEAKAGE_POWER                             "BT.leakage_power"

/* BT Difference between Power Peak and Power Average ,unit is dBm,double value is return*/
#define WT_RES_BT_DIFF_POWER_PEAK_VS_AVG                       "BT.PowerPeakMinusPowerAvg"

/* BT EDR Phase difference view result,complex vector point data is return */
#define WT_RES_EDR_PHASE_DIFFERENCE                         "EDR.Phase_difference"

/* BT BR or BLE Freq Deviation view result,complex vector point data is return */
#define WT_RES_BR_BLE_FREQ_DEVIATION                       "BR_BLE.Freq.Deviation"

/* BT BLE advertising access address or BLE test syncWord ,unsigned int is return*/
#define WT_RES_BlE_DECODE_ACCESS_ADDR                       "BLE.Decode.Access_addr"

//====================================================================
//TBT SIFS, unit:s.
#define WT_RES_TBT_SIFS             "tbt.sifs"

//Frame start and end place is return,finally show need (/sampleRate),eg:240M,need (result/240).
#define WT_RES_FRAME_LOCATION       "Power.Frame.Location"

//Evm Specification db
#define WT_RES_EVM_SPECIFICATION_DB "evm.specification.db"

//Mimo real stream count,according to real vsg stream
#define WT_RES_STREAM_COUNT         "mimo.stream.count"

/*PSDU Scramble sequence result,u8 vetor is return,result eg:0100101,ax的不用这个获取*/
#define WT_RES_PSDU_SCRAMBLE        "psdu_scramble"

/*DUT Mac Info， struct stMacInfo is return,support 11ag 11n, 11ac*/
#define WT_RES_MAC_INFO             "Mac_info"

/*PSDU CRC result ,int value is return,只获取11a, n, ac的，ax的在ofdma info中*/
#define WT_RES_PSDU_CRC             "PSDU_CRC"

#define WT_RES_SERVICE_FIELD_INFO        "Service.field.info"

#define WT_RES_MPDU_EOF_PADDING_INFO    "AMPDU.EOF.Padding"

/*LDPC 是否纠正错误bit的int flag，目前只获取11n,ax,ac,be的，区分用户*/
#define WT_RES_PSDU_LDPC_CORRECT_FLAG   "LDPC.Correct_flag"
/*===========================================================================
 special for 11ax result
 ===========================================================================*/
/*11ax per user Mac Info，vector struct stMacInfo is return*/
#define WT_RES_11AX_USER_MAC_INFO   "Ax11_User_Mac_info"

/*11ax per user PSDU Scramble sequence result,u8 vetor is return,result eg:0100101，只是ax的*/
#define WT_RES_11AX_PSDU_SCRAMBLE   "Ax11_psdu_scramble"

/*11ax mu mimo, ofdma into,vector struct RuOfdmaInfo11ax is return*/
#define WT_RES_11AX_MU_MIMO_OFDMA_INFO      "Ax11_Mu_Mimo.Ofdma_info"

/*11ax mu mimo, all user's const data*/
#define WT_RES_MU_MIMO_ALL_USER_CONST_DATA  "mumimo.all.user.const.data"

/*11ax mu mimo, all user's const ref*/
#define WT_RES_MU_MIMO_ALL_USER_CONST_REF   "mumimo.all.user.const.ref"

/*11ax mu mimo, all user's const pilot*/
#define WT_RES_MU_MIMO_ALL_USER_CONST_PILOT "mumimo.all.user.const.pilot"

/*11ax A-mpdu信息,8080要取组合结果，所以和其他协议分开*/
#define WT_RES_11AX_A_MPDU                       "Ax11_ampdu.info"

/*11ax get RU Q-matrix information, vector struct QMatrixInformation return*/
#define WT_RES_11AX_RU_QMAT                      "Ax11_QMat.info"

/*===========================================================================
 special for 11be result
 ===========================================================================*/
/*specific user's const. data*/
#define WT_RES_11BE_USER_CONST_DATA "11be.user.const.data"

/*specific user's const ref*/
#define WT_RES_11BE_USER_CONST_REF "11be.user.const.ref"

/*specific user's const.pilot*/
#define WT_RES_11BE_USER_CONST_PILOT "11be.user.const.pilot"

/*11be psdu format*/
#define WT_RES_11BE_PSDU_FORMAT "11be.psdu.format"

/*11be per user Mac Info，vector struct stMacInfo is return*/
#define WT_RES_11BE_USER_MAC_INFO "11be.user.MAC.info"

/*11be per user PSDU Scramble sequence result,u8 vetor is return,result eg:0100101 */
#define WT_RES_11BE_PSDU_SCRAMBLE "11be.psdu.scrambler"

/*11be mu mimo, ofdma into,vector struct RuOfdmaInfo11ax is return*/
#define WT_RES_11BE_MU_MIMO_OFDMA_INFO "11be.mu-mimo.ofdma.info"

/*11be mu mimo, all user's const data*/
#define WT_RES_11BE_MU_MIMO_ALL_USER_CONST_DATA "mu-mimo.all.user.const.data"

/*11be mu mimo, all user's const ref*/
#define WT_RES_11BE_MU_MIMO_ALL_USER_CONST_REF "mu-mimo.all.user.const.ref"

/*11be mu mimo, all user's const pilot*/
#define WT_RES_11BE_MU_MIMO_ALL_USER_CONST_PILOT "mu-mimo.all.user.const.pilot"

/*11be A-mpdu信息,8080要取组合结果，所以和其他协议分开*/
#define WT_RES_11BE_A_MPDU "11be.ampdu.info"

/*11be get RU Q-matrix information, vector struct QMatrixInformation return*/
#define WT_RES_11BE_RU_QMAT "11be.QMat.info"
/*===========================================================================
 Ofdm result
 ===========================================================================*/
/*ac mu mimo, all user's const data，ac8080,也是按普通的取所以和ax分开*/
#define WT_RES_AC_MU_MIMO_ALL_USER_CONST_DATA  "ac_mumimo.all.user.const.data"

/*ac mu mimo, all user's const ref*/
#define WT_RES_AC_MU_MIMO_ALL_USER_CONST_REF   "ac_mumimo.all.user.const.ref"

/*ac mu mimo, all user's const pilot*/
#define WT_RES_AC_MU_MIMO_ALL_USER_CONST_PILOT "ac_mumimo.all.user.const.pilot"

/*11ac mu mimo 返回data info信息*/
#define WT_RES_11AC_MU_MIMO_DATA_INFO       "mumimo.data.info"

/*A-mpdu信息，n，ac*/
#define WT_RES_A_MPDU                       "ampdu.info"

/*EVM 余量 db, double value is return */
#define WT_RES_EVM_MARGIN               "evm_margin"

/* LO 余量 db, double value is return */
#define WT_RES_LEAGKAGE_MARGIN           "Leagkage_margin"

/*频偏余量ppm, double value is return */
#define WT_RES_FREQ_ERR_MARGIN           "freqerr_margin"

/*采样偏余量 ppm, double value is return */
#define WT_RES_CLOCK_ERR_MARGIN           "clockerr_margin"

/*LO参考量dB, double value is return */
#define WT_RES_LEAGKAGE_SPECIFICATION_DB    "LO.specification.db"

/*频偏参考量ppm, double value is return */
#define WT_RES_FREQ_ERR_SPECIFICATION_PPM   "Freqerr.specification.ppm"

/*采样参考量ppm, double value is return */
#define WT_RES_CLOCK_ERR_SPECIFICATION_PPM  "Clockerr.specification.ppm"

/*除了ax外的export PSDU data char vector value is return*/
#define WT_RES_EXPORT_PSDU_BIT              "Psdu_bit"

/*11ax psdu bit信息,8080要取组合结果，所以和其他协议分开*/
#define WT_RES_AX_EXPORT_PSDU_BIT              "Ax11_Psdu_bit"

/*mimo power table result ,struct vector value is return*/
#define WT_RES_POWER_TABLE                  "power.table"

#define WT_RES_PSDU_DECOMPOSED_INFO         "PSDU_Decomposed_Info"
/*===========================================================================
 zwave result
 ===========================================================================*/
/* zwave eye data. stZWave_eye Vector */
#define WT_RES_ZWAVE_EYE             "Zwave.eye"

/*===========================================================================
 11ba result
 ===========================================================================*/
/* Spectrum Y-axis values, power in dBm. Vector is returned. */
#define WT_RES_11BA_WUR_SPECTRUM_Y              "11ba_WUR_y"

/* spectrum mask, power in dBm. Vector is returned */
#define WT_RES_11BA_WUR_SPECTRUM_MASK           "11ba_WUR_spectrum.mask"

/*Spectrum mask error point in %.Value is returned*/
#define WT_RES_11BA_WUR_SPECTRUM_MASK_ERR       "11ba_WUR_Spec_mask_err"

/*OBW in Hz.Value is returned*/
#define WT_RES_11BA_WUR_SPECTRUM_OBW            "11ba_WUR_Obw"

/*OBW start Freq,int value*/
#define WT_RES_11BA_WUR_SPECTRUM_OBW_START_FREQ "11ba_WUR_Obw_start_freq"

/*OBW end Freq,int value*/
#define WT_RES_11BA_WUR_SPECTRUM_OBW_END_FREQ   "11ba_WUR_Obw_end_freq"

/* spectrum rbw, value in HZ. Int Value */
#define WT_RES_11BA_WUR_SPECTRUM_RBW            "11ba_WUR_spectrum.rbw"

/*Frequency span in Hz. Int Value is returned*/
#define WT_RES_11BA_WUR_SPECTRUM_FREQ_SPAN      "11ba_WUR_spectrum.Freq_span"

/*发送ON/OFF符号功率比测试结果.0：pass; 1 fail,Value is returned*/
#define WT_RES_SYMBOLS_POWER_RATIO              "symbols.power.tatio.result"

/*相关性测试结果.0：pass; 1 fail,Value is returned*/
#define WT_RES_CORRELATION_TEST                 "Correlation.test.result"

//11ba WUR PHY层相关信息，struct WURPHYInfo is returned*/
#define WT_RES_11BA_WUR_PHY_INFO                "11ba_WUR_PHY_Info"

//11ba Lsig bit内容
#define WT_RES_11BA_L_SIG_BIT                   "11ba.L_Sig.bit"

/*===========================================================================
 11az result
 ===========================================================================*/
//spectral flatness
/*Spectrum Flatness Passed Or Failed. Vector Value is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_PASSED             "AZ.Users.Spec_flatness_passed"

/*Spectrum Flatness Data. Vector is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_DATA               "AZ.Users.Spec_flatness_data"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA       "AZ.Users.Spec_flatness_maskup"

/*Spectrum Flatness Data. Vector is returned*/
#define  WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA     "AZ.Users.Spec_Flatness_maskdown"

/*Spectrum Flatness Section Value. Complex Vector is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_SECTION_VALUE      "AZ.Users.SpecFlat_section_value"

/*Spectrum Flatness Section Margin. Complex Vector is returned*/
#define WT_RES_11AZ_ALL_USERS_OFDM_SPECTRUM_FLATNESS_SECTION_MARGIN     "AZ.Users.SpeFlat_section_margin"

 //channel amplitude response
#define WT_RES_11AZ_ALL_USERS_OFDM_CHANNEL_PHASE_RESPONSE               "AZ.Users.Ch_phase_response"

 //channel phase response
#define WT_RES_11AZ_ALL_USERS_OFDM_CHANNEL_AMPLITUDE_RESP               "AZ.Users.Ch_amplitude_response"

/*===========================================================================
  H-Matrix initial power result
  ===========================================================================*/
/* RMS Power in dB, no gap. power frame*/
#define WT_RES_HMAT_INIT_RMS_DB_NO_GAP "Hmat.init.rms_db_nogap"

/* RMS Power in dB. power all*/
#define WT_RES_HMAT_INIT_RMS_DB "Hmat.init.rms_db"

/* Power Peak. Vector is returned. */
#define WT_RES_HMAT_INIT_POWER_PEAK "Hmat.init.pow.peak"

/* Mimo real stream count,according to real vsg stream */
#define WT_RES_HMAT_INIT_STREAM_COUNT "Hmat.init.mimo.stream.count"

/*===========================================================================
 SparkLink(GLE) result
 ===========================================================================*/
/* Contrl Info evm vs time data. double vector */
#define WT_RES_CTRINFO_EVM_TIME            "ctrinfo.evm.time"

/* Contrl Info const data in dB. Complex Vector is returned */
#define WT_RES_CTRINFO_CONST_DATA          "ctrinfo.const.data"

/* Contrl Info pilot const data in dB. Complex Vector is returned */
#define WT_RES_CTRINFO_CONST_PILOT          "ctrinfo.const.pilot"

/* reference const data in dB. Complex Vector is returned */
#define WT_RES_CTRINFO_CONST_REF            "ctrinfo.const.ref"

/* gle Eye Diagram  */
#define WT_RES_GLE_EYE                       "gle.eye"

/* gle Spectrum adjacent Channel Power,dBm */
#define WT_RES_GLE_SPEC_ACP                  "gle.Spectrum_Acp"

/* gle Spectrum adjacent Channel Power mask, Complex vector */
#define WT_RES_GLE_SPEC_ACP_MASK             "gle.Spectrum_Acp_mask"

/* gle FrmType, int */
#define WT_RES_GLE_FRAME_TYPE                 "gle.FrmType"

/* gle Bandwidth, int */
#define WT_RES_GLE_BAND_WIDTH                 "gle.Bandwidth"

/* gle PID, int */
#define WT_RES_GLE_PID                         "gle.PID"

/* gle PayloadLen, int */
#define WT_RES_GLE_PAYLOAD_LEN                 "gle.Payload_Len"

/* gle PayloadCRCType, int */
#define WT_RES_GLE_PAYLOAD_CRC_TYPE            "gle.Payload_CRC_Type"

/* gle PayloadCRC, int */
#define WT_RES_GLE_PAYLOAD_CRC                 "gle.Payload_CRC"

/* gle CtrlinfoType, int */
#define WT_RES_GLE_CTRL_INFO_TYPE              "gle.Ctrl_Info_Type"

/* gle CtrlinfoCRC, int */
#define WT_RES_GLE_CTRL_INFO_CRC               "gle.Ctrl_Info_CRC"

/* gle Delta_fd1_Avg, double */
#define WT_RES_GLE_DELTA_FD1_AVG               "gle.Delta_fd1_Avg"

/* gle Delta_fd1_Max, double */
#define WT_RES_GLE_DELTA_FD1_MAX                "gle.Delta_fd1_Max"

/* gle Delta_fd1_Min, double */
#define WT_RES_GLE_DELTA_FD1_MIN                "gle.Delta_fd1_Min"

/* gle Delta_fd2_Avg, double */
#define WT_RES_GLE_DELTA_FD2_AVG                "gle.Delta_fd2_Avg"

/* gle Delta_fd2_Min, double */
#define WT_RES_GLE_DELTA_FD2_MIN                 "gle.Delta_fd2_Min"

/* gle EvmAvg, double */
#define WT_RES_GLE_EVM_AVG                       "gle.Evm_Avg"

/* gle EvmPeak, double */
#define WT_RES_GLE_EVM_PEAK                      "gle.Evm_Peak"

/* gle Evm99PCT, double */
#define WT_RES_GLE_EVM_99PCT                     "gle.Evm_99PCT"

/* gle Init_Freq_Error, double */
#define WT_RES_GLE_INIT_FREQ_ERR                 "gle.Init_Freq_Error"

/* gle Max_Freq_Drift, double */
#define WT_RES_GLE_MAX_FREQ_DRIFT                "gle.Max_Freq_Drift"

/* gle Freq_Drift_Rate, double */
#define WT_RES_GLE_FREQ_DRIFT_RATE               "gle.Freq_Drift_Rate"

/* gle CtrlInfoEvmAvg, double */
#define WT_RES_GLE_CTRL_INFO_EVM_AVG             "gle.Ctrl_Info_Evm_Avg"

/* gle CtrlInfoEvmPeak, double */
#define WT_RES_GLE_CTRL_INFO_EVM_PEAK            "gle.Ctrl_Info_Evm_Peak"

/* gle CtrlInfoEvm99PCT, double */
#define WT_RES_GLE_CTRL_INFO_EVM_99PCT           "gle.Ctrl_Info_Evm_99PCT"

/* gle ZeroCrossingErr, double */
#define WT_RES_GLE_ZERO_CROSSING_ERR             "gle.Zero_Crossing_Err"

/* gle SymClkErr, double */
#define WT_RES_GLE_SYM_CLK_ERR                   "gle.Sym_Clk_Err"

/* gle MaxTimeDev, double */
#define WT_RES_GLE_MAX_TIME_DEV                  "gle.Max_Time_Dev"

/* gle CTRLInfo A1,  */
#define WT_RES_GLE_CTRLINFI_A1                    "gle.CtrlinfoA1"

/* gle CTRLInfo A2,  */
#define WT_RES_GLE_CTRLINFI_A2                    "gle.CtrlinfoA2"

/* gle CTRLInfo A3,  */
#define WT_RES_GLE_CTRLINFI_A3                    "gle.CtrlinfoA3"

/* gle CTRLInfo A4,  */
#define WT_RES_GLE_CTRLINFI_A4                    "gle.CtrlinfoA4"

/* gle CTRLInfo A5,  */
#define WT_RES_GLE_CTRLINFI_A5                    "gle.CtrlinfoA5"

/* gle CTRLInfo A6,  */
#define WT_RES_GLE_CTRLINFI_A6                    "gle.CtrlinfoA6"

/* gle CTRLInfo A7,  */
#define WT_RES_GLE_CTRLINFI_A7                    "gle.CtrlinfoA7"

/* gle CTRLInfo B1,  */
#define WT_RES_GLE_CTRLINFI_B1                    "gle.CtrlinfoB1"

/* gle CTRLInfo B2,  */
#define WT_RES_GLE_CTRLINFI_B2                    "gle.CtrlinfoB2"

/* gle CTRLInfo B3,  */
#define WT_RES_GLE_CTRLINFI_B3                    "gle.CtrlinfoB3"

/* gle CTRLInfo B4,  */
#define WT_RES_GLE_CTRLINFI_B4                    "gle.CtrlinfoB4"

/* gle CTRLInfo B5,  */
#define WT_RES_GLE_CTRLINFI_B5                    "gle.CtrlinfoB5"


/*===========================================================================
 3GPP result
 ===========================================================================*/
/*
<IQ signal>
*/
/* IQ data. Complex vector is returned. */
// #define WT_RES_IQ "IQ.data"

/* IQ data mv. Complex vector is returned. */
// #define WT_RES_IQ_MV "IQ.data.mv"

/*
<Spectrum>
*/
/* spectrum rbw, value in HZ. Int Value */
//#define WT_RES_SPECTRUM_RBW "spectrum.rbw"

/*Center Frequency in Hz.Value is returned*/
// #define WT_RES_SPECTRUM_FREQ_CENTER "Freq_center"

/*Frequency span in Hz. Int Value is returned*/
// #define WT_RES_SPECTRUM_FREQ_SPAN "Freq_span"

/* Y-axis values, power in dBm. Vector is returned. */
// #define WT_RES_SPECTRUM_Y "y"
/*start Freq in Hz. double is returned. */
#define WT_RES_SPECTRUM_FREQ_START "freq_start"

/*end Freq in Hz. double is returned. */
#define WT_RES_SPECTRUM_FREQ_END "freq_end"

/* Complex[0] is freq in Hz, Complex[1] is power in dBm. Vector<Complex> is returned. */
#define WT_RES_3GPP_SPECTRAL_RXSPECTEMIS "3GPP.Spectral.RxSpectEmis"

/* Complex[0] is freq in Hz, Complex[1] is power in dBm. Vector<Complex> is returned. */
#define WT_RES_3GPP_SPECTRAL_SPECTEMISMASK "3GPP.Spectral.SpectEmisMask"

/* Mask segment, Vector<int> is returned. */
#define WT_RES_3GPP_SPECTRAL_EMISMASKSEG "3GPP.Spectral.EmisMaskSeg"

/* badpointcnt, int is returned. */
#define WT_RES_3GPP_SPECTRAL_BADPOINTCNT "3GPP.Spectral.badpointcnt"

/*OBW in Hz.Value is returned*/
// #define WT_RES_SPECTRUM_OBW "Obw"

/*OBW start Freq,int value*/
// #define WT_RES_SPECTRUM_OBW_START_FREQ "Obw_start_freq"

/*OBW end Freq,int value*/
//#define WT_RES_SPECTRUM_OBW_END_FREQ "Obw_end_freq"

/*
<Summary>
*/
/* RmsEvm in %, double is return. */
#define WT_RES_3GPP_EVM_RMSEVM "3GPP.Evm.RmsEvm"

/* PeakEvm in %, double is return. */
#define WT_RES_3GPP_EVM_PEAKEVM "3GPP.Evm.PeakEvm"

/* DmrsEvm in %, double is return. */
#define WT_RES_3GPP_EVM_DMRSEVM "3GPP.Evm.DmrsEvm"

/* EVM 95 Percentile in %, double is return. */
#define WT_RES_3GPP_EVM_95TH_EVM "3GPP.Evm.Evm95th"

/* MErr 95 Percentile in %, double is return. */
#define WT_RES_3GPP_EVM_MAGNERR95TH "3GPP.Evm.MagnErr95th"

/* PhErr 95 Percentile in %, double is return. */
#define WT_RES_3GPP_EVM_PHASEERR95th "3GPP.Evm.PhaseErr95th"

/* Burst Power in %, double is return. */
#define WT_RES_3GPP_POWER_MEASBURSTPWR "3GPP.Power.MeasBurstPwr"

/* Power vs. Time Test in %, int is return. */
#define WT_RES_3GPP_POWER_TESTRESULT "3GPP.Power.TestResult"

/* Spectrum Modulation Freq. Test in %, int is return. */
#define WT_RES_3GPP_SPECTMOD_TESTRESULT "3GPP.SpectMod.TestResult"

/* Spectrum Switching Freq Test in %, int is return. */
#define WT_RES_3GPP_SPECTSWT_TESTRESULT "3GPP.SpectSwt.TestResult"


/* Frequency Error in Hz. Value is returned. */
// #define WT_RES_FREQ_ERR_HZ "signal.freqerr_hz"

/* Frames average power in dBm, double is return. */
#define WT_RES_3GPP_FRMAVGPWRDBM "3GPP.FrmAvgPwrdBm"

/* Frames peak power in dBm, double is return. */
#define WT_RES_3GPP_FRMPEAKPWRDBM "3GPP.FrmPeakPwrdBm"

/* NR-UTRA Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_NRUTRARESULT "3GPP.ACLR.NrUtraResult"

/* E-UTRA Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_EUTRARESULT "3GPP.ACLR.EutraResult"

/* UTRA Test is pass or fail , int is return. */
#define WT_RES_3GPP_ACLR_UTRARESULT "3GPP.ACLR.UtraResult"

/* Spectrum Emission Mask Test  is pass or fail , int is return. */
#define WT_RES_3GPP_SPECTRAL_SEMRESULT "3GPP.Spectral.SEMResult"

/* Inband Emission Test  is pass or fail , int is return. */
#define WT_RES_3GPP_INEMIS_INEMISRESULT "3GPP.InEmis.InEmisResult"

/* IQ Match Amplitude Error in dB. Value is returned. */
//#define WT_RES_IQ_MATCH_AMP_DB      "iqmatch.amp_dB"

/* IQ Match Phase Error in deg. Value is returned. */
//#define WT_RES_IQ_MATCH_PHASE      "iqmatch.phase"

/*Spectrum Flatness Passed Or Failed. Int Value is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_PASSED "Spec_flatness_passed"

/*Carrier leakage in dB.Value is returned*/
// #define WT_RES_SPECTRUM_CARRIER_LEAKAGE "Carrier_leakage"

/*Subcarrier Power in dBm.double is returned*/
#define WT_RES_3GPP_SCAVGPWRDBM "3GPP.SCAvgPwrdBm"

/*Reference Signal Receiving Power in dBm.double is returned*/
#define WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_POWER "3GPP.Reference_Signal_Receiving_Power"

/*Received Signal Strength Indication in dBm.double is returned*/
#define WT_RES_3GPP_RECEIVED_SIGNAL_STRENGTH_INDICATION "3GPP.Received_Signal_Strength_Indication"

/*Reference Signal Receiving Quality in dB.double is returned*/
#define WT_RES_3GPP_REFERENCE_SIGNAL_RECEIVING_QUALITY "3GPP.Reference_Signal_Receiving_Quality"

/*Signal to Noise Ratio in dB.double is returned*/
#define WT_RES_3GPP_SIGNAL_TO_NOISE_RATIO "3GPP.Signal_to_Noise_Ratio"
/*
<Data Info>
*/
/*LinkDirect, int is returned*/
#define WT_RES_3GPP_INFO_LINKDIRECT "3GPP.Info.LinkDirect"

/*Channel, int is returned*/
#define WT_RES_3GPP_INFO_CHANNEL "3GPP.Info.Channel"

/*Codeword, int is returned*/
#define WT_RES_3GPP_INFO_CODEWORD "3GPP.Info.Codeword"

/*Slot, int is returned*/
#define WT_RES_3GPP_INFO_SLOT "3GPP.Info.SlotIdx"

/*Codeword Modulate, int is returned*/
#define WT_RES_3GPP_INFO_CW_MODULATE "3GPP.Info.Codeword_Modulate"

/*Codeword Scrambling, int is returned*/
#define WT_RES_3GPP_INFO_CW_SCRAMBLING "3GPP.Info.Codeword_Scrambling"

/*TrChOnNum, vector<int> is returned*/
#define WT_RES_3GPP_DEINFO_TRCHONNUM "3GPP.Info.TrChOnNum"

/*Codeword Channel Coding Type, int is returned*/
#define WT_RES_3GPP_INFO_CW_CHANNELCODINGTYPE "3GPP.Info.Codeword_Channel_Coding_Type"

/*Codeword CRC, int is returned*/
#define WT_RES_3GPP_INFO_CW_CRC "3GPP.Info.Codeword_CRC"

/*Format, int is returned*/
#define WT_RES_3GPP_INFO_FORMAT "3GPP.Info.Format"

/*HARQ-ACK, int is returned*/
#define WT_RES_3GPP_INFO_HARQ_ACK "3GPP.Info.HarqAckInfo"

/*
<PHY Bit Information>
*/
/*Bit sequence of Codeword 0, Vector<char*> is returned*/
#define WT_RES_3GPP_INFO_CW_BITSEQ_0 "3GPP.Info.Cw.BitSeq_0"

/*Bit sequence of Codeword 1, Vector<char*> is returned*/
#define WT_RES_3GPP_INFO_CW_BITSEQ_1 "3GPP.Info.Cw.BitSeq_1"

/*Bit Length, Vector<int> is returned*/
#define WT_RES_3GPP_INFO_CW_BITLEN "3GPP.Info.Cw.BitLen"

/*
<EVM by symbol>
*/
/*Symbol Evm low Data, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_LOW_DATA "3GPP.Evm.SymbEvm.Low_Data"

/*Symbol Evm low DMRS, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_LOW_PILOT "3GPP.Evm.SymbEvm.Low_DMRS"

/*Symbol Evm high Data, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_HIGH_DATA "3GPP.Evm.SymbEvm.High_Data"

/*Symbol Evm high DMRS, Complex[0] is Symbol ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_SYMBEVM_HIGH_PILOT "3GPP.Evm.SymbEvm.High_DMRS"

/*
<EVM by Carrier>
*/
/*Max Carrier Len, X-axis Range. int is returned*/
#define WT_RES_3GPP_EVM_MAXCARRIERLEN "3GPP.Evm.MaxCarrierLen"

/*CarrierEvm, Complex[0] is Carrier ID, Complex[1] is Evm in dBm. Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_CARRIEREVM "3GPP.Evm.CarrierEvm"

/*
<Spectrum Flatness>
*/
/*Max Flat Number, X-axis Range. int is returned*/
#define WT_RES_3GPP_FLATNESS_MAXFLATNUM "3GPP.Flatness.MaxFlatNum"

/*Spectrum Flatness Data. Vector is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_DATA "Spec_flatness_data"

/*Spectrum Flatness Data. Vector is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_MASKUP_DATA "Spec_flatness_maskup_data"

/*Spectrum Flatness Data. Vector is returned*/
// #define WT_RES_OFDM_SPECTRUM_FLATNESS_MASKDOWN_DATA "Spec_flatness_maskdown_data"

/*Range Type, Complex[0] is Subcarrier ID, Complex[1] is Range Type. Vector<Complex> is returned*/
#define WT_RES_3GPP_FLATNESS_RANGETYPE "3GPP.Flatness.RangeType"

/*Ripple1 = max(range1)-min(rang1), double is returned*/
#define WT_RES_3GPP_FLATNESS_RIPPLE1 "3GPP.Flatness.Ripple1"

/*Ripple2 = max(range2)-min(rang2), double is returned*/
#define WT_RES_3GPP_FLATNESS_RIPPLE2 "3GPP.Flatness.Ripple2"

/*MaxR2SubMinR1 = max(range2)-min(rang1), double is returned*/
#define WT_RES_3GPP_FLATNESS_MAXR2SUBMINR1 "3GPP.Flatness.MaxR2SubMinR1"

/*MaxR1SubMinR2 = max(range1)-min(rang2), double is returned*/
#define WT_RES_3GPP_FLATNESS_MAXR1SUBMINR2 "3GPP.Flatness.MaxR1SubMinR2"

/*
<Power>
*/
/* points power. Vector is returned */
// #define WT_RES_POINTS_POWER "points.power"

/* window avg power. Vector is returned */
// #define WT_RES_WIN_AVG_POWER "avg.power"

// Frame start and end place is return
// #define WT_RES_FRAME_LOCATION "Power.Frame.Location"

/* Package average power in dBm, double is return. */
#define WT_RES_3GPP_PKGAVGPWRDBM "3GPP.PkgAvgPwrdBm"

/* Package average power in dBm, double is return. */
#define WT_RES_3GPP_PKGPEAKPWRDBM "3GPP.PkgPeakPwrdBm"

/*
<CCDF>
*/
/* Real vector containing CCDF probability values (Y-axis of CCDF plot) */
// #define WT_RES_CCDF_PROB "CCDF.prob"

/* Real vector containing CCDF power relative to average power in dB values
(X-axis of CCDF plot) */
// #define WT_RES_CCDF_POWER_REL_DB "CCDF.power_rel_dB"

/* Result for CCDF %10 %1 %0.1 %0.01 percentage‘s power. double vector Value is returned. */
// #define WT_RES_CCDF_PERCENT_POWER "CCDF.percent.power"

/* ccdf start relative power. double Value dBm */
// #define WT_RES_CCDF_START "CCDF.start"

/* ccdf scale value dBm. double value */
// #define WT_RES_CCDF_SCALE "CCDF.scale"

/*
<Constellation>
*/
/*RxPoint low Data, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_LOW_DATA "3GPP.Evm.RxPoint.Low_Data"

/*RxPoint low DMRS, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_LOW_PILOT "3GPP.Evm.RxPoint.Low_DMRS"

/*RxPoint high Data, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_HIGH_DATA "3GPP.Evm.RxPoint.High_Data"

/*RxPoint high DMRS, Complex[0] is I number, Complex[1] is Q number.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_RXPOINT_HIGH_PILOT "3GPP.Evm.RxPoint.High_DMRS"

/* reference const data in dB. Complex Vector is returned */
// #define WT_RES_CONST_REF "const.ref"

/*
<Inband Emission>
*/
/*RBEmis data, Complex[0] is RB number, Complex[1] is Power in dB.Vector<Complex> is returned*/
#define WT_RES_3GPP_INEMIS_RBEMIS "3GPP.InEmis.RBEmis"

/*RBEmis reference, Complex[0] is RB number, Complex[1] is Power in dB.Vector<Complex> is returned*/
#define WT_RES_3GPP_INEMIS_EMISREF "3GPP.InEmis.EmisRef"

/*RBEmis reference segment, Complex[0] is RB number, Complex[1] is segment number.Vector<Complex> is returned*/
#define WT_RES_3GPP_INEMIS_EMISREFSEG "3GPP.InEmis.EmisRefSeg"

/*MarginMin, double is returned*/
#define WT_RES_3GPP_INEMIS_MARGINMIN "3GPP.InEmis.MarginMin"

/*RB Index of MarginMin, int is returned*/
#define WT_RES_3GPP_INEMIS_MARGINMINIDX "3GPP.InEmis.MarginMinIdx"

/*
<Spectrum ACLR E-UTRA>
*/
/*ACLR E-URTA输出结果, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_POWER "3GPP.ACLR_EUtra.Power"

/*ACLR E-URTA输出参考, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_MASK "3GPP.ACLR_EUtra.Mask"

/*
E-UTRA2(△f < 0),
E-UTRA1(△f < 0),
Carrier,
E-UTRA1(△f > 0),
E-UTRA2(△f > 0),
Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_ACLRATIO "3GPP.ACLR_EUtra.AclRatio"

/*ACLR序列对应频率范围, Complex[0] is start freq in MHz, Complex[1] is end freq in MHz.Vector<Complex> is returned*/
#define WT_RES_3GPP_ACLR_EUTRA_FREQ "3GPP.ACLR_EUtra.Freq"

/*
<Spectrum ACLR NR-UTRA>
*/
/*ACLR NR-URTA输出结果, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_POWER "3GPP.ACLR_NrUtra.Power"

/*ACLR NR-URTA输出参考, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_MASK "3GPP.ACLR_NrUtra.Mask"

/*
NR-UTRA2(△f < 0),
NR-UTRA1(△f < 0),
Carrier,
NR-UTRA1(△f > 0),
NR-UTRA2(△f > 0),
Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_ACLRATIO "3GPP.ACLR_NrUtra.AclRatio"

/*ACLR序列对应频率范围, Complex[0] is start freq in MHz, Complex[1] is end freq in MHz.Vector<Complex> is returned*/
#define WT_RES_3GPP_ACLR_NRUTRA_FREQ "3GPP.ACLR_NrUtra.Freq"

/*
<Spectrum ACLR UTRA>
*/
/*ACLR URTA输出结果, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_POWER "3GPP.ACLR_Utra.Power"

/*ACLR URTA输出参考, Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_MASK "3GPP.ACLR_Utra.Mask"

/*
UTRA2(△f < 0),
UTRA1(△f < 0),
Carrier,
UTRA1(△f > 0),
UTRA2(△f > 0),
Vector<double> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_ACLRATIO "3GPP.ACLR_Utra.AclRatio"

/*ACLR序列对应频率范围, Complex[0] is start freq in MHz, Complex[1] is end freq in MHz.Vector<Complex> is returned*/
#define WT_RES_3GPP_ACLR_UTRA_FREQ "3GPP.ACLR_Utra.Freq"

/*
<Phase Error>
*/
/*Phase Error low, Complex[0] is symbol number, Complex[1] is Phase Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERR_LOW "3GPP.MPErr.PhaseErr.Low"

/*Phase Error high, Complex[0] is symbol number, Complex[1] is Phase Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERR_HIGH "3GPP.MPErr.PhaseErr.High"

/*Phase Error low and high PhaseErrRms, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRRMS "3GPP.MPErr.PhaseErrRms"

/*Phase Error low and high PhaseErrPeak, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRPEAK "3GPP.MPErr.PhaseErrPeak"

/*Phase Error low and high PhaseErrDmrs, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRDMRS "3GPP.MPErr.PhaseErrDmrs"

/*Phase Error Limit, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_PHASEERRLIMIT "3GPP.MPErr.PhaseErrLimit"

/*Phase Error Limit, Complex is returned*/
#define WT_RES_3GPP_MPERR_PHASEERROUT "3GPP.MPErr.PhaseErrOut"

/*
<Magnitude Error>
*/
/*Magnitude Error low, Complex[0] is symbol number, Complex[1] is Magnitude Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERR_LOW "3GPP.MPErr.MagnErr.Low"

/*Magnitude Error high, Complex[0] is symbol number, Complex[1] is Magnitude Erro in %.Vector<Complex> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERR_HIGH "3GPP.MPErr.MagnErr.High"

/*Magnitude Error Out MagnErrRms, Complex vector is returned*/
#define WT_RES_3GPP_MPERR_MAGNERROUT "3GPP.MPErr.MagnErrOut"

/*Magnitude Error Limit MagnErrLimit, double is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRLIMIT "3GPP.MPErr.MagnErrLimit"

/*Magnitude Error low and high MagnErrRms, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRRMS "3GPP.MPErr.MagnErrRms"

/*Magnitude Error low and high MagnErrPeak, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRPEAK "3GPP.MPErr.MagnErrPeak"

/*Magnitude Error low and high MagnErrDmrs, Vector<double> is returned*/
#define WT_RES_3GPP_MPERR_MAGNERRDMRS "3GPP.MPErr.MagnErrDmrs"

/*
<UE Power>
*/
/*UE Power Out, Vector<Complex> is returned*/
#define WT_RES_3GPP_UEPOWER_POWEROUT "3GPP.UEPower.PwrOut"

/*
<Phase Discontinuity>
*/
/*Phase Discontinuity Out, Vector<Complex> is returned*/
#define WT_RES_3GPP_PHASEDIS_DIFFOUT "3GPP.PhaseDis.PhaseDiffOut"

/*Phase Discontinuity Dynamic Limit, Vector<double> is returned*/
#define WT_RES_3GPP_PHASEDIS_DYNAMICLIMIT "3GPP.PhaseDis.DynamicLimit"

/*Phase Discontinuity Dynamic Limit, Vector<double> is returned*/
#define WT_RES_3GPP_PHASEDIS_UPPERLIMIT "3GPP.PhaseDis.UpperLimit"

/*Phase Discontinuity Max PhaseDis, double is returned*/
#define WT_RES_3GPP_PHASEDIS_MAXPHASEDIS "3GPP.PhaseDis.MaxPhaseDis"

/*Phase Discontinuity Min SlotDis, int is returned*/
#define WT_RES_3GPP_PHASEDIS_MINSLOTDIS "3GPP.PhaseDis.MinSlotDis"

/*Phase Discontinuity Exceed Upper Limit Num, int is returned*/
#define WT_RES_3GPP_PHASEDIS_EXCEEDUPPERLIMITNUM "3GPP.PhaseDis.ExceedUpperLimitNum"

/*Phase Discontinuity Exceed Dynamic Limit Num, int is returned*/
#define WT_RES_3GPP_PHASEDIS_EXCEEDDYNAMICLIMITNUM "3GPP.PhaseDis.ExceedDynamicLimitNum"

/*
<Frequency Error>
*/
/*Frequency Error Out, Vector<Complex> is returned*/
#define WT_RES_3GPP_FREQERR_FREQERROUT "3GPP.FreqErr.FreqErrOut"

/*Frequency Error Limit, Vector<double> is returned*/
#define WT_RES_3GPP_FREQERR_FREQERRLIMIT "3GPP.FreqErr.FreqErrLimit"

/*Frequency Error, Vector<double> is returned*/
#define WT_RES_3GPP_FREQERR_FREQERR "3GPP.FreqErr.FreqErr"

/*
<Power vs Time>
*/
/*Power Symbol, Vector<double> is returned*/
#define WT_RES_3GPP_POWER_SYMBOL "3GPP.Power.Symbol"

/*Power Power, Vector<double> is returned*/
#define WT_RES_3GPP_POWER_POWER "3GPP.Power.Power"

/*Power Measure Burst Num, int is returned*/
#define WT_RES_3GPP_POWER_MEASURE_BURSTNUM "3GPP.Power.MeasBurstNum"

/*Power Burst Up Mask, Vector<Complex> is returned*/
#define WT_RES_3GPP_POWER_BURST_UPMASK "3GPP.Power.Burst.UpMask"

/*Power Burst Down Mask, Vector<Complex> is returned*/
#define WT_RES_3GPP_POWER_BURST_DOWNMASK "3GPP.Power.Burst.DownMask"

/*Power Burst XMask, Vector<double> is returned*/
#define WT_RES_3GPP_POWER_BURST_XMARK "3GPP.Power.Burst.XMark"

/*Power Burst Average Power, double is returned*/
#define WT_RES_3GPP_POWER_BURST_AVERAGE_POWER "3GPP.Power.Burst.AveragePower"

/*Power Burst Tsc Type, int is returned*/
#define WT_RES_3GPP_POWER_BURST_TSC_TYPE "3GPP.Power.Burst.TscType"

/*Power Burst Type, int is returned*/
#define WT_RES_3GPP_POWER_BURST_TYPE "3GPP.Power.Burst.Type"

/*Power Measure Part Min, int is returned*/
#define WT_RES_3GPP_POWER_BURST_MEASURE_PART_MIN "3GPP.Power.Burst.MeasurePartMin"

/*Power Measure Part Max, int is returned*/
#define WT_RES_3GPP_POWER_BURST_MEASURE_PART_MAX "3GPP.Power.Burst.MeasurePartMax"

/*Power Measure SV, int is returned*/
#define WT_RES_3GPP_POWER_BURST_MEASURE_SV "3GPP.Power.Burst.MeasureSV"

/*
<Spectrum Modulation>
*/
/*Spectrum Modulation Frequency, Vector<Complex> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_FREQ "3GPP.SpecMod.Freq"

/*Spectrum Modulation Power, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_POWER "3GPP.SpecMod.Power"

/*Spectrum Modulation Mask, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_MASK "3GPP.SpecMod.Mask"

/*Spectrum Modulation Symbol, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_SYMBOL "3GPP.SpecMod.Symbol"

/*Spectrum Modulation Time, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_MODULATION_TIME "3GPP.SpecMod.Time"

/*
<Spectrum Switching>
*/
/*Spectrum Switching Frequency, Vector<Complex> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_FREQ "3GPP.SpecSwt.Freq"

/*Spectrum Switching Power, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_POWER "3GPP.SpecSwt.Power"

/*Spectrum Switching Mask, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_MASK "3GPP.SpecSwt.Mask"

/*Spectrum Switching Symbol, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_SYMBOL "3GPP.SpecSwt.Symbol"

/*Spectrum Switching Time, Vector<double> is returned*/
#define WT_RES_3GPP_SPECTRAL_SWITCH_TIME "3GPP.SpecSwt.Time"

/*
<base result>
*/
// #define WT_RES_BASE_RESULT "result.base"

/*
<Evm vs. Modulation Symbols>
*/
/*每个调制符号的EVM值, Complex[0] is Modulation Symbols number, Complex[1] is Evm in dB.Vector<Complex> is returned*/
#define WT_RES_3GPP_EVM_POINTEVM "3GPP.Evm.PointEvm"

/*
<Magnitude Error vs chip>
*/
#define WT_RES_3GPP_MPERR_CHIP "3GPP.MPErr.MagnErrChip"
#define WT_RES_3GPP_MPERR_CHIPLIMIT "3GPP.MPErr.MagnErrChipLimit"

/*
<Phase Error vs chip>
*/
#define WT_RES_3GPP_MPERR_PHASECHIP "3GPP.MPErr.PhaseErrChip"
#define WT_RES_3GPP_MPERR_PHASECHIPLIMIT "3GPP.MPErr.PhaseErrChipLimit"

/*
<Error Vector Magnitude>
*/
#define WT_RES_3GPP_EVM_EVMOUT "3GPP.Evm.EvmOut"
#define WT_RES_3GPP_EVM_LIMIT "3GPP.Evm.EvmLimit"
#define WT_RES_3GPP_EVM_CHIP "3GPP.Evm.EvmChip"
#define WT_RES_3GPP_EVM_CHIPLIMIT "3GPP.Evm.EvmChipLimit"

/*
<CDP vs Slot>
*/
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPCCHPWROUT "3GPP.CodeDomain.CDP.DPCCHPwrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPDCHPWROUT "3GPP.CodeDomain.CDP.DPDCHPwrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDP_HSDPCCHPWROUT "3GPP.CodeDomain.CDP.HSDPCCHPwrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPCCHPWR "3GPP.CodeDomain.CDP.DPCCHPwr"
#define WT_RES_3GPP_CODEDOMAIN_CDP_DPDCHPWR "3GPP.CodeDomain.CDP.DPDCHPwr"
#define WT_RES_3GPP_CODEDOMAIN_CDP_HSDPCCHPWR "3GPP.CodeDomain.CDP.HSDPCCHPwr"

/*
<Relative CDE>
*/
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHERROUT "3GPP.CodeDomain.CDE.DPCCHErrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHERROUT "3GPP.CodeDomain.CDE.DPDCHErrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHERROUT "3GPP.CodeDomain.CDE.HSDPCCHErrOut"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHERR "3GPP.CodeDomain.CDE.DPCCHErr"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHERR "3GPP.CodeDomain.CDE.DPDCHErr"
#define WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHERR "3GPP.CodeDomain.CDE.HSDPCCHErr"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPCCHSF "3GPP.CodeDomain.CDE.DPCCHSf"
#define WT_RES_3GPP_CODEDOMAIN_CDE_DPDCHSF "3GPP.CodeDomain.CDE.DPDCHSf"
#define WT_RES_3GPP_CODEDOMAIN_CDE_HSDPCCHSF "3GPP.CodeDomain.CDE.HSDPCCHSf"

/*
<CDP/CDE IQ-Signal>
*/
#define WT_RES_3GPP_CDM_CODENUM "3GPP.CDM.CodeNum"
#define WT_RES_3GPP_CDM_CDP_IMONITORVAL "3GPP.CDM.CDP.IMonitorVal"
#define WT_RES_3GPP_CDM_CDP_QMONITORVAL "3GPP.CDM.CDP.QMonitorVal"
#define WT_RES_3GPP_CDM_CDE_IMONITORVAL "3GPP.CDM.CDE.IMonitorVal"
#define WT_RES_3GPP_CDM_CDE_QMONITORVAL "3GPP.CDM.CDE.QMonitorVal"

#define WT_RES_3GPP_UEPOWER_PWR "3GPP.UEPower.Pwr"
#define WT_RES_3GPP_IQOFFSET "3GPP.IQOffset"
#define WT_RES_3GPP_IQIMBA "3GPP.IQImba"
#define WT_RES_3GPP_PHASEDIS_PHASEDIFF "3GPP.PhaseDis.PhaseDiff"

/*
<PBCH info>
*/
#define WT_RES_3GPP_SSB_NUM "3GPP.SSBNum"
#define WT_RES_3GPP_SSB_INDEX "3GPP.SSBInfo.SSBIdx"
#define WT_RES_3GPP_SSB_STATE "3GPP.SSBInfo.State"
#define WT_RES_3GPP_SSB_PSS_EVM "3GPP.SSBInfo.PSSEvm"
#define WT_RES_3GPP_SSB_SSS_EVM "3GPP.SSBInfo.SSSEvm"
#define WT_RES_3GPP_SSB_PBCH_EVM "3GPP.SSBInfo.PBCHEVM"
#define WT_RES_3GPP_SSB_CRC "3GPP.SSBInfo.Crc"
#define WT_RES_3GPP_SSB_HALF_FRAME "3GPP.SSBInfo.HalfFrm"
#define WT_RES_3GPP_SSB_SYSTEM_FRAME_NUM "3GPP.SSBInfo.SFN"
#define WT_RES_3GPP_SSB_DMRS_POS "3GPP.SSBInfo.DmrsTypeAPos"
#define WT_RES_3GPP_SSB_COMMON_SUB_SPACING "3GPP.SSBInfo.SCSCommon"
#define WT_RES_3GPP_SSB_SUB_OFFSET "3GPP.SSBInfo.SCOffset"
#define WT_RES_3GPP_SSB_CORESET_ZERO "3GPP.SSBInfo.CoresetZero"
#define WT_RES_3GPP_SSB_SEARCH_SPACE_ZERO "3GPP.SSBInfo.SSZero"
#define WT_RES_3GPP_SSB_CELL_BARRED "3GPP.SSBInfo.CellBarre"
#define WT_RES_3GPP_SSB_INTRA_FREQ_RESEL "3GPP.SSBInfo.InFreqResel"

/*===========================================================================
 WISUN result
 ===========================================================================*/
/*WISUN PHR CRC Check, int*/
#define WT_RES_WISUN_PHR_CRC_CHECK                  "WiSun.phr_crc_cheak"

/*WISUN PHR Rate Field, int*/
#define WT_RES_WISUN_PHR_MCS                         "WiSun.mcs"

/*WISUN PHR Frame Len, int*/
#define WT_RES_WISUN_PHR_FRAME_LENGTH                "WiSun.phr_frame_length"

/*WISUN PHR Scrambler, int*/
#define WT_RES_WISUN_PHR_SCRAMBLER                    "WiSun.phr_scrambler"

/*WISUN Data Rate, double*/
#define WT_RES_WISUN_DATA_RATE                        "WiSun.data_rate"

/*WISUN BW, double*/
#define WT_RES_WISUN_BW                                "WiSun.bw"

/*WISUN Symbol count, int*/
#define WT_RES_WISUN_SYMBOL_COUNT                      "WiSun.symbol_count"

/*WISUN Coding rate, int*/
#define WT_RES_WISUN_CODING_RATE                       "WiSun.coding_rate"

/*WISUN Modulation type, int*/
#define WT_RES_WISUN_MODULATION_TYPE                   "WiSun.modulation"

/*WISUN PSDU length, int*/
#define WT_RES_WISUN_PSDU_LENGTH                       "WiSun.psdu_length"

/*WISUN PSDU CRC, int*/
#define WT_RES_WISUN_PSDU_CRC                          "WiSun.psdu_crc"

/*WISUN PHR Bit, int[36]*/
#define WT_RES_WISUN_PHR_BIT                           "WiSun.phr_bit"

/*WISUN CRC Bit, int[8]*/
#define WT_RES_WISUN_PHR_CRC_BIT                       "WiSun.phr_crc_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_SHR_SFD_BIT                    "WiSun.fsk_shr_sfd_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_PHR_BIT                         "WiSun.fsk_phr_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_PHY_PAYLOAD_BIT                  "WiSun.fsk_phy_payload_bit"

/*WISUN fsk, int* */
#define WT_RES_WISUN_FSK_CRC_BIT                           "WiSun.fsk_crc_bit"


#define WT_RES_WISUN_EVM_OFFSET_DB            "wisun.evm_offset"

/* double vector is returned, phase_err vs chip*/
#define WT_RES_WISUN_PHASE_ERR_VS_CHIP        "wisun.phase_err.chip"

/* stLRWPAN_OQPSK_eye vector is returned*/
#define WT_RES_WISUN_EYE_REAL                 "wisun.eye_real"

/* stLRWPAN_OQPSK_eye vector is returned*/
#define WT_RES_WISUN_EYE_IMAG                 "wisun.eye_imag"

/* stLRWPAN_FSK_eye vector is returned  */
#define WT_RES_WISUN_FSK_EYE                   "wisun.fsk_eye"

#define WT_RES_WSUN_FSK_SPEC_ACP_MASK          "wisun.fsk_acp_mask"

#define WT_RES_WSUN_FSK_SPEC_ACP_Y              "wisun.fsk_acp_y"

#define WT_RES_WSUN_FSK_SPEC_ACP_X               "wisun.fsk_acp_x"

#define WT_RES_WSUN_FSK_SPEC_ACP_WIDTH          "wisun.fsk_acp_width"

#define WT_RES_WSUN_FSK_SPEC_ACP_VALID_NUM          "wisun.fsk_acp_Valid_num"

#endif
