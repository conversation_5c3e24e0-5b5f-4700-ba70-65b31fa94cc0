#ifndef _SCPI_GEN_WI_SUN_H_
#define _SCPI_GEN_WI_SUN_H_

#include "scpi/scpi.h"
#include "basehead.h"
#ifdef __cplusplus
extern "C" {
#endif

    scpi_result_t SetWaveGen_WISUN_SignalType(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_Option(scpi_t * context);

    scpi_result_t SetWaveGen_WISUN_MCS(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_Scrambler(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PhyOFDMInterleaving(scpi_t * context);
    
    scpi_result_t SetWaveGen_WISUN_PSDU_PsduType(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_PsduLength(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_CRCEnable(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_MacEnable(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_FrameCtrl(scpi_t * context);
   
    scpi_result_t SetWaveGen_WISUN_PSDU_Destination_PANID(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_Destination_Address(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_Source_PANID(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PSDU_Source_Address(scpi_t * context);

    scpi_result_t SetWaveGen_WISUN_FrequencyBand(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_SpreadingMode(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_RateMode(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_ChipRate(scpi_t * context);

    scpi_result_t SetWaveGen_WISUN_PhyFSKPreambleLength(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_ModeSwitch(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PhySunFSKSfd(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_FCSType(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PhyFSKFecEnabled(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PhyFSKFecScheme(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PhyFSKFecInterLeavingRsc(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_PhyFSKScramblePsdu(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_DataRate(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_Modulation(scpi_t * context);
    scpi_result_t SetWaveGen_WISUN_ModulationIndex(scpi_t * context);


#ifdef __cplusplus
}
#endif

#endif