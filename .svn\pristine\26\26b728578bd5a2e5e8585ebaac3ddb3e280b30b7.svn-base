#ifndef _WPA_COMMON_H_
#define _WPA_COMMON_H_
#include "includes.h"
#include "common.h"

#ifndef BIT
#define BIT(x) (1U << (x))
#endif

enum WPA_CIPHER_TYPE_ENUM
{
    WPA_CIPHER_NONE = 1,
    WPA_CIPHER_WEP40 = 10,
    WPA_CIPHER_WEP104,
    WPA_CIPHER_WEP128,
    WPA_CIPHER_TKIP = 20,
    WPA_CIPHER_CCMP_128 = 30,
    WPA_CIPHER_CCMP_256,
    WPA_CIPHER_GCMP_128 = 40,
    WPA_CIPHER_GCMP_256,

    WPA_CIPHER_SMS4_OFB = 50,
    WPA_CIPHER_SMS4_GCM,

    WPA_CIPHER_GTK_NOT_USED = 0xFFFF
};

#define WPA_KEY_MGMT_IEEE8021X BIT(0)
#define WPA_KEY_MGMT_PSK BIT(1)
#define WPA_KEY_MGMT_NONE BIT(2)
#define WPA_KEY_MGMT_IEEE8021X_NO_WPA BIT(3)
#define WPA_KEY_MGMT_WPA_NONE BIT(4)
#define WPA_KEY_MGMT_FT_IEEE8021X BIT(5)
#define WPA_KEY_MGMT_FT_PSK BIT(6)
#define WPA_KEY_MGMT_IEEE8021X_SHA256 BIT(7)
#define WPA_KEY_MGMT_PSK_SHA256 BIT(8)
#define WPA_KEY_MGMT_WPS BIT(9)
#define WPA_KEY_MGMT_SAE BIT(10)
#define WPA_KEY_MGMT_FT_SAE BIT(11)
#define WPA_KEY_MGMT_WAPI_PSK BIT(12)
#define WPA_KEY_MGMT_WAPI_CERT BIT(13)
#define WPA_KEY_MGMT_CCKM BIT(14)
#define WPA_KEY_MGMT_OSEN BIT(15)
#define WPA_KEY_MGMT_IEEE8021X_SUITE_B BIT(16)
#define WPA_KEY_MGMT_IEEE8021X_SUITE_B_192 BIT(17)
#define WPA_KEY_MGMT_FILS_SHA256 BIT(18)
#define WPA_KEY_MGMT_FILS_SHA384 BIT(19)
#define WPA_KEY_MGMT_FT_FILS_SHA256 BIT(20)
#define WPA_KEY_MGMT_FT_FILS_SHA384 BIT(21)
#define WPA_KEY_MGMT_OWE BIT(22)
#define WPA_KEY_MGMT_DPP BIT(23)
#define WPA_KEY_MGMT_FT_IEEE8021X_SHA384 BIT(24)
#define WPA_KEY_MGMT_PASN BIT(25)


#define PMKID_LEN 16
#define PMK_LEN 32
#define PMK_LEN_SUITE_B_192 48
#define PMK_LEN_MAX 64
#define WPA_REPLAY_COUNTER_LEN 8
#define WPA_NONCE_LEN 32
#define WPA_KEY_RSC_LEN 8
#define WPA_GMK_LEN 32
#define WPA_GTK_MAX_LEN 32
#define WPA_PASN_PMK_LEN 32
#define WPA_PASN_MAX_MIC_LEN 24
#define WPA_MAX_RSNXE_LEN 4


#define WPA_SELECTOR_LEN 4
#define WPA_VERSION 1
#define RSN_SELECTOR_LEN 4
#define RSN_VERSION 1


#define WPA_KCK_MAX_LEN 32
#define WPA_KEK_MAX_LEN 64
#define WPA_TK_MAX_LEN 32
#define WPA_KDK_MAX_LEN 32
/**
* struct wpa_ptk - WPA Pairwise Transient Key
* IEEE Std 802.11i-2004 - 8.5.1.2 Pairwise key hierarchy
*/
struct wpa_ptk 
{
    u8 kck[WPA_KCK_MAX_LEN]; /* EAPOL-Key Key Confirmation Key (KCK) */
    u8 kek[WPA_KEK_MAX_LEN]; /* EAPOL-Key Key Encryption Key (KEK) */
    u8 tk[WPA_TK_MAX_LEN]; /* Temporal Key (TK) */
    u8 kck2[WPA_KCK_MAX_LEN]; /* FT reasoc Key Confirmation Key (KCK2) */
    u8 kek2[WPA_KEK_MAX_LEN]; /* FT reassoc Key Encryption Key (KEK2) */
    u8 kdk[WPA_KDK_MAX_LEN]; /* Key Derivation Key */
    size_t kck_len;
    size_t kek_len;
    size_t tk_len;
    size_t kck2_len;
    size_t kek2_len;
    size_t kdk_len;
};

s32 wpa_cipher_key_len(int cipher);
u32 wpa_kek_len(s32 akmp, size_t pmk_len);

s32 wpa_key_mgmt_sha256(int akm);
s32 wpa_key_mgmt_sha384(int akm);
s32 wpa_key_mgmt_suite_b(int akm);

s32 wpa_pmk_to_ptk(const u8 *pmk, size_t pmk_len, const char *label,
    const u8 *addr1, const u8 *addr2,
    const u8 *nonce1, const u8 *nonce2,
    struct wpa_ptk *ptk, int akmp, int cipher,
    const u8 *z, size_t z_len, size_t kdk_len);

#endif

