/***************************************************************************************************
 * This confidential and proprietary software may be only used as authorized by a licensing 
 * agreement from iTest Technology Co., Ltd.
 * (C) COPYRIGHT 2020 iTest Technology Co., Ltd. ALL RIGHTS RESERVED
 *--------------------------------------------------------------------------------------------------
 * Filename             : algvsg_3gpp_main.h
 * Author               : Linden
 * Data                 : 2022-08
 * Description          :       
 * Modification History :
 * Data            By          Version         Change Description
 *--------------------------------------------------------------------------------------------------
 * Reference to the source file.
 **************************************************************************************************/

#ifndef ALGVSG_3GPP_MAIN_H_
#define ALGVSG_3GPP_MAIN_H_

/***************************************************************************************************
 *                                          include
 **************************************************************************************************/
#include "algvsg_3gpp_export.h"

/***************************************************************************************************
 *                                Macro or Debug Configuration
 **************************************************************************************************/


/***************************************************************************************************
 *                                      Enum Definition
 **************************************************************************************************/


/***************************************************************************************************
 *                                    Struct Definition
 **************************************************************************************************/


/***************************************************************************************************
 *                                  Extern Variable Statement
 **************************************************************************************************/


/***************************************************************************************************
 *                                  Extern Function Statement
 **************************************************************************************************/

/***************************************************************************************************
 *                                  Export Variable Statement
 **************************************************************************************************/

/***************************************************************************************************
 *                                  Export Function Statement
 **************************************************************************************************/
ALGVSG_3GPP_API int Callback_3GPP_VsgLibInit(void);

/* Release algorith lib dynamic allocation memory */
ALGVSG_3GPP_API void Callback_3GPP_VsgLibDeinit(void);

ALGVSG_3GPP_API char *Callback_3GPP_VsgDllVersion(void);

ALGVSG_3GPP_API int Callback_3GPP_VsgMain(
    Alg_3GPP_WaveGenType *input, Complex **outdat,
    int *outlen, void **pOutExtData);

#endif /* ALGVSG_3GPP_MAIN_H_ */
