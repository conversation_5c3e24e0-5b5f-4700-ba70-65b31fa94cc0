//*****************************************************************************
//  File: vsg.cpp
//  vsg业务处理
//  Data: 2016.8.11
//*****************************************************************************
#include "vsg.h"
#include <sys/types.h>
#include <fstream>
#include <iomanip>
#include <map>
#include <mutex>
#include <unistd.h>
#include <dirent.h>
#include <new>
#include <cstdio>
#include <cerrno>
#include <cmath>
#include <string.h>
#include "alg/includeAll.h"
#include "conf.h"
#include "monitor.h"
#include "analysis/algenv.h"
#include "calconf.h"
#include "secure.h" //引用该头文件，主要是为了获取信号crc用于判断是否重新加载了信号
#include "analysis/demod.h"
#include "wtlog.h"
using namespace std;

static const double MIN_TEMP_DIFF = 0.5;   //温度变化最小值，超过此值则需要做温度补偿

enum
{
    FRM_TYPE_NON_HT,
    FRM_TYPE_HT_MF,
    FRM_TYPE_HT_GF
};

enum
{
    TB_COMPUTE_BEFORE_AMEND,
    TB_COMPUTE_AFTER_AMEND,
};

enum WT_IQ_Mode
{
    RFIQ_MODE,
    ANALOGIQ_MODE,
};

bool VsgParam::IsAgumentLegal(void) const
{
    stringstream Strs;
    Strs.clear();
    Strs.str("");
    Strs << "VsgParam:{" << endl \
        <<"    Freq:" << Freq << endl \
        <<"    Freq2:" << Freq2 << endl \
        <<"    FreqOffset:" << FreqOffset << endl \
        <<"    RFPort:" << RFPort << endl \
        <<"    Type:" << Type << endl \
        <<"    MasterMode:" << MasterMode << endl \
        <<"    SignalId:" << SignalId << endl \
        <<"    VsgMask:" << VsgMask << endl \
        <<"    Power:" << Power << endl \
        <<"    ExtGain:" << ExtGain << endl \
        <<"    SignalId:" << SignalId << endl \
        <<"    AllocTimeout:" << AllocTimeout << endl \
        <<"    SamplingFreq:" << SamplingFreq << endl \
        <<"    ExtGain2:" << ExtGain2 << endl \
        <<"    Is160M:" << Is160M << endl \
        <<"    RFPort2:" << RFPort2 << endl \
        <<"    DulPortMode:" << DulPortMode << endl \
        <<"    Power2:" << Power2 << endl \
        <<"    TBTStaDelay:" << TBTStaDelay << endl \
        <<"    DCOffsetI:" << DCOffsetI << endl \
        <<"    DCOffsetQ:" << DCOffsetQ << endl \
        <<"    CommModeVolt:" << CommModeVolt << endl \
        <<"    LoopCnt:" <<LoopCnt << endl \
        <<"    IFG:" <<IFG << endl \
        <<"    IFGRamdomMode:" << IFGRamdomMode << endl \
        <<"    RandomWaveGapMax:" << RandomWaveGapMax << endl \
        <<"    RandomWaveGapMin:" << RandomWaveGapMin << endl \
        <<"}" << endl;
    const string& Str2 = Strs.str();
    WTLog::Instance().LOGOPERATE(Str2.c_str());

    if (IsDualPortMode())
    {
        if (RFPort != 0 && RFPort2 != 0)
        {
            int ModId = 0;
            int ModId2 = 0;
            DevLib::Instance().GetModId(DEV_TYPE_VSG, RFPort, ModId);
            DevLib::Instance().GetModId(DEV_TYPE_VSG, RFPort2, ModId2);
            if (ModId2 == ModId)
            {
                WTLog::Instance().LOGERR(WT_ARG_ERROR, "80+80 Dul Mode cannt use same unit!");
                return false;
            }
        }
    }
    return true;
}

void VsgParam::ParamTest()
{
    Freq = 400000000;
}

// 波形文件index和名字对应关系类
class WaveIdxName
{
public:
    static string GetWaveName(int Index)
    {
        static WaveIdxName Tabel;
        return Tabel.GetName(Index);
    }

private:
    WaveIdxName()
    {
        int Index;
        string Name;
        string Line;
        string::size_type Pos;
        ifstream Fs(WTConf::GetDir() + "signal-config.csv");  //文件index和文件名的对应关系保存在signal-config.csv文件中

        while(getline(Fs, Line))
        {
            Pos = Line.find_first_not_of(' ');

            //跳过注释行
            if (Line[Pos] == '#' || Line[Pos] == '/')
            {
                continue;
            }

            Index = atoi(Line.c_str());
            Pos = Line.find_first_of(',');
            Pos = Line.find_first_not_of(" \t", Pos + 1);
            m_WaveTbl[Index] = Line.substr(Pos, Line.find_last_not_of("\n ", Pos) - Pos + 1);
        }
    }

    ~WaveIdxName() {};

    inline string GetName(int Index)
    {
        auto iter = m_WaveTbl.find(Index);
        return iter != m_WaveTbl.end() ? iter->second : "";
    }

private:
    map<int, string> m_WaveTbl;
};

// VSG配置参数转换为devlib的VSG配置参数
VSGConfigType WTVsg::TransConfig(const VsgParam &Param, int Index)
{
    VSGConfigType Config;

    Config.DcOffsetI = TransDacCode(Param.DCOffsetI);
    Config.DcOffsetQ = TransDacCode(Param.DCOffsetQ);
    Config.CmVolt = Param.CommModeVolt;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "WTVsg::TransConfig"
         << Pout(Param.DCOffsetI) << Pout(Config.DcOffsetI)
         << Pout(Param.DCOffsetQ) << Pout(Config.DcOffsetQ)
         << Pout(Param.CommModeVolt) << Pout(Config.CmVolt)
         << endl;
    Config.Gain = 0;
    Config.FreqOffsetHz = Param.FreqOffset;
    Config.SamplingRate = Param.SamplingFreq;
    Config.RFPort = Param.RFPort;
    Config.RFPortState = WT_RF_STATE_PA_1;
    Config.DeviceMode = GetDevMode(Param, Index);
    Config.LoopCnt = Param.LoopCnt;
    Config.IFGRamdomMode = Param.IFGRamdomMode;
    Config.IFG = Param.IFG;
    Config.RandomWaveGapMax = Param.RandomWaveGapMax;
    Config.RandomWaveGapMin = Param.RandomWaveGapMin;

    // Config.VsgIfgStatus = (!IsSingleMode(GetDevModeWithout8080Trans(Param, Index)))
    Config.VsgIfgStatus = (Param.IsDualParamMode() && Index == 1)
                              ? OFF
                              : m_GapPowerEnable;
    Config.WaveBw = m_FileBw[0];

    if (!Param.IsAC8080() || Index == 0)
    {
        Config.Freq = Param.Freq;
        Config.Power = Param.Power + Param.ExtGain - AlgEnv::Instance().m_VsgPwrBaseDelta - AlgEnv::Instance().m_VsgPwrDelta[m_FileBw[0]];
    }
    else
    {
        Config.Freq = Param.Freq2;

        if (!Param.IsUseDualParam())
        {
            Config.Power = Param.Power + Param.ExtGain2 - AlgEnv::Instance().m_VsgPwrBaseDelta - AlgEnv::Instance().m_VsgPwrDelta[m_FileBw[0]];
        }
        else
        {
            Config.Power = Param.Power2 + Param.ExtGain2 - AlgEnv::Instance().m_VsgPwrBaseDelta - AlgEnv::Instance().m_VsgPwrDelta[m_FileBw[0]];
            Config.RFPort = Param.RFPort2; // 80+80双端口模式时，从机使用第二个端口
        }
    }

    // AC80+80 功率由两条链路合成， 单条链路功率值减半
    //单端口80+80模式时，校准库计算功率时，会-3，双端口模式时，由固件做-3处理。
    if (Param.IsDualPortMode())
    {
        Config.Power -= 3;
    }

    return Config;
}

void WTVsg::SendParamToMon(void)
{
    list<shared_ptr<Monitor>> Mons;
    MonitorMgr::Instance().GetMonitors(Mons);

    //如果是非连续160，需要将频率数据还原再配置回去
    bool Is160 = m_Param[0].Is160() && m_Param[0].IsAC8080();
    if (Is160)
    {
        m_Param[0].Freq += 40e6;
        m_Param[0].Freq2 = 0;
    }

    for (auto &Mon : Mons)
    {
        if (Mon->IsMonPort(m_Param[0].RFPort))
        {
            Mon->SendVsgParam(&m_Param[0], sizeof(m_Param[0]));//lint !e50
            Mon->SendPnParam(m_PnItemHead, m_ExtPnItem);
        }
    }

    if (Is160)
    {
        m_Param[0].Freq -= 40e6;
        m_Param[0].Freq2 = m_Param[0].Freq + 80e6;
    }
}

int WTVsg::SetDigPn()
{
    int Ret = WT_OK;
    Tx_Parm CalParam;
    double SamplingFreq = 0;
    if (m_PnItem.size() > DIG_PACKET_ITEM_SIZE_MAX)
    {
        for (int FileId = 0; FileId < m_PnItem.size() && FileId < m_ExtPnItem.size(); FileId++)
        {
            std::vector<DigPnItem> DigPnItemVector; // PN项
            std::vector<unique_ptr<char[]>> PnData(m_DigChanList.size());
            for (int Ch = 0; Ch < m_DigChanList.size(); Ch++)
            {
                CalParam.power = GetVsa().GetDigParam().VSGPnRatioMode == SOURE_PN_FIX_RATIO
                                     ? (PN_FIX_RATIO_POWER + 0.5) /*加0.5以保证浮点数比较正确*/
                                                                  /*: (m_Param[0].Power + m_Param[i < m_ParamNum ? i : 0].ExtGain);*/
                                     : m_Param[0].Power;
                Ret = AmendWaveData(&CalParam, Ch, FileId, SamplingFreq);
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "AmendWaveData failed");
                    return Ret;
                }

                DigPnItem PnItem;
                PnItem.Len = m_PnItem[FileId].Len;
                if (m_DigChanList.size() == 1)
                {
                    PnItem.Addr = m_PnItem[FileId].Addr;
                }
                else
                {
                    PnData[Ch].reset(new (std::nothrow) char[m_PnItem[FileId].Len]);
                    memcpy(PnData[Ch].get(), reinterpret_cast<char *>(m_PnItem[FileId].Addr), m_PnItem[FileId].Len);
                    PnItem.Addr = reinterpret_cast<unsigned long>(PnData[Ch].get());
                }
                PnItem.Loop = m_ExtPnItem[FileId].LoopCnt;
                PnItem.TBTVsgDelay = (double)m_ExtPnItem[FileId].StartDelay / m_Param[0].SamplingFreq;
                PnItem.IFGRamdomMode = FIXED_IFG_MODE;
                PnItem.IFG = (m_ExtPnItem[FileId].IFGRamdomMode == RANDOM_IFG_MODE)
                                 ? m_ExtPnItem[FileId].RandomWaveGapMin * DEFAULT_SMAPLE_RATE
                                 : m_ExtPnItem[FileId].IFG;
                DigPnItemVector.push_back(PnItem);
            }

            Ret = GetDigLib().SetPNItemHardDisk(DigPnItemVector, FileId);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "set dig pn config failed");
                return Ret;
            }
        }
    }
    else
    {
        for (int i = 0; i < m_DigChanList.size(); i++)
        {
            CalParam.power = GetVsa().GetDigParam().VSGPnRatioMode == SOURE_PN_FIX_RATIO
                                 ? (PN_FIX_RATIO_POWER + 0.5) /*加0.5以保证浮点数比较正确*/
                                                              /*: (m_Param[0].Power + m_Param[i < m_ParamNum ? i : 0].ExtGain);*/
                                 : m_Param[0].Power;
            Ret = AmendWaveData(&CalParam, i, SamplingFreq);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "AmendWaveData failed");
                return Ret;
            }

            std::vector<DigPnItem> DigPnItemVector; // PN项
            for (int j = 0; j < m_PnItem.size(); j++)
            {
                DigPnItem PnItem;
                PnItem.Addr = m_PnItem[j].Addr;
                PnItem.Len = m_PnItem[j].Len;
                PnItem.Loop = m_ExtPnItem[j].LoopCnt;
                PnItem.TBTVsgDelay = (double)m_ExtPnItem[j].StartDelay / m_Param[0].SamplingFreq;
                PnItem.IFGRamdomMode = m_ExtPnItem[j].IFGRamdomMode;
                PnItem.IFG = m_ExtPnItem[j].IFG;
                PnItem.RandomWaveGapMax = m_ExtPnItem[j].RandomWaveGapMax;
                PnItem.RandomWaveGapMin = m_ExtPnItem[j].RandomWaveGapMin;
                DigPnItemVector.push_back(PnItem);
            }

            Ret = GetDigLib().SetPNItem(DigPnItemVector, m_DigChanList[i]);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "set dig pn config failed");
                return Ret;
            }
        }
    }
    return Ret;
}

int WTVsg::SetDig()
{
    int Ret = WT_OK;
    if (m_PnItem.empty())
    {
        WTLog::Instance().LOGERR(WT_NO_PN_ITEM, "no pn config");
        return WT_NO_PN_ITEM;
    }
    SetDigConfig(false);

    if(!m_DigChanList.size())
    {
        WTLog::Instance().LOGERR(WT_PARAM_NUM_ERROR, "WTVsg::SetDig m_DigChanList = 0");
        return WT_PARAM_NUM_ERROR;
    }

    VsgDigConfigType Config;
    Config.ISTimeout = GetVsa().GetDigParam().VSGISTimeout;
    Config.TriggerTimeout = GetVsa().GetDigParam().TriggerTimeout;
    Config.ActionMask = GetVsa().GetDigParam().VsgActionMask;
    Config.MaxBitCnt = GetVsa().GetDigParam().VsgMaxBitCnt;
    Config.Loop = m_ExtPnItem[0].LoopCnt;
    Config.ChannelList = m_DigChanList;
    Config.ChannelTotal = m_DigChanList.size();
    Config.FrameTotal = m_VsgData.size();
    Config.Notify = std::bind(&WTVsg::ProcDigFin, this, std::placeholders::_1);
    
    m_Compensate->SetMaxBitCnt(Config.MaxBitCnt);
    Ret = GetDigLib().VSGSetParam(Config);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set vsg dig param failed");
        return Ret;
    }

    Ret = SetDigPn();
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set dig pn failed");
        return Ret;
    }

    SetDigConfig(true);
    return WT_OK;
}

int WTVsg::SetActualAmpl(int Index, Tx_Parm &CalParam)
{
    //实际参考电平和配置的差异大于1说明异常
    double ParamPower;
    double VsgAmpl = CalParam.tx_gain_parm.tx_sw_gain.actual_mpl;

    //参考TransConfig处理，这里要把功率加回来
    if (m_Param[0].IsDualPortMode())
    {
        VsgAmpl += 3;
    }

    if (!m_Param[0].IsAC8080() || Index == 0)
    {
        ParamPower = m_Param[0].Power;
        VsgAmpl = VsgAmpl - m_Param[0].ExtGain + AlgEnv::Instance().m_VsgPwrBaseDelta;
        if (abs(VsgAmpl - ParamPower) >= 1)
        {
            m_Param[0].Power = VsgAmpl;
        }
    }
    else if (m_Param[0].IsUseDualParam())
    {
        ParamPower = m_Param[0].Power2;
        VsgAmpl = VsgAmpl - m_Param[0].ExtGain2 + AlgEnv::Instance().m_VsgPwrBaseDelta;
        if (abs(VsgAmpl - ParamPower) >= 0.5)
        {
            m_Param[0].Power2 = VsgAmpl;
        }
    }
    return WT_OK;
}

int WTVsg::SetMod(int Index)
{
    if (DigModeLib::Instance().IsDigMode())
    {
        return SetDig();
    }

    (void)Index;
    int Ret = WT_OK;
    double SamplingFreq = 0;
    Tx_Parm CalParam;

    if (m_PnItem.empty())
    {
        WTLog::Instance().LOGERR(WT_NO_PN_ITEM, "no pn config");
        return WT_NO_PN_ITEM;
    }

    if (m_Mods.size() == 1)
    {
        m_AmendFactors[1].WaveName.clear();
    }

    for (int i = 0; i < (signed)m_Mods.size(); i++)
    {
        m_AmendFactors[i].WaveName.resize(m_ExtPnItem.size());
        memset(&CalParam, 0, sizeof(Tx_Parm));
        Ret = SetExtMode(m_Mods[i].ModId);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "set vsg ext mode failed");
            return Ret;
        }

        VSGConfigType Config = TransConfig(m_Param[0], i);
        Config.BroadcastEnable = false;
        Ret = DevLib::Instance().VSGSetConfig(m_Mods[i].ModId, Config, CalParam);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "set vsg param to mod failed");
            return Ret;
        }
        SetActualAmpl(i, CalParam);
        if (Index != SET_PARAM_WHITOUT_PN)
        {
            // WTLog::Instance().WriteLog(LOG_DEBUG, "\n\n\nm_AmendFactors[i].Freq = %f Config.Freq = %f;\n\nm_AmendFactors[i].SamplingRate = %f Config.SamplingRate = %f;\nConfig.Power = %f \n m_AmendFactors[i].Port = %d  Config.RFPort = %d\n",
            //        m_AmendFactors[i].Freq, Config.Freq, m_AmendFactors[i].SamplingRate, Config.SamplingRate, Config.Power,m_AmendFactors[i].Port, Config.RFPort); //检查PN参数
            // WTLog::Instance().WriteLog(LOG_DEBUG, "m_AmendFactors[i].Cal_tx_link_sw_stat=%d,CalParam.tx_gain_parm.sw_tx_link_state=%d,m_AmendFactors[i].Cal_is_pa_on=%d,CalParam.tx_gain_parm.is_pa_on=%d\n",
            //        m_AmendFactors[i].Cal_tx_link_sw_stat, CalParam.tx_gain_parm.sw_tx_link_state, m_AmendFactors[i].Cal_is_pa_on, CalParam.tx_gain_parm.is_pa_on); //检查频率段
            //printf("m_AmendFactors[i].timeskew=%f,CalParam.tx_iq_imb_parm.timeskew=%f,m_AmendFactors[i].gain_imb=%f,CalParam.tx_iq_imb_parm.gain_imb=%f,m_AmendFactors[i].quad_err=%f,CalParam.tx_iq_imb_parm.quad_err=%f\n",
                   //m_AmendFactors[i].timeskew, CalParam.tx_iq_imb_parm.timeskew, m_AmendFactors[i].gain_imb, CalParam.tx_iq_imb_parm.gain_imb, m_AmendFactors[i].quad_err, CalParam.tx_iq_imb_parm.quad_err); //补偿参数
            //printf("m_AmendFactors[i].ForceComp=%d\n", m_AmendFactors[i].ForceComp);
            bool OtherFactorsNeedAmend = false;
            // 對於模拟IQ的模式下，判断条件有变
            int AnalogIQMode = RFIQ_MODE;
            DevLib::Instance().GetAnalogIQSW(m_Mods[i].ModId, AnalogIQMode);
            if (AnalogIQMode == RFIQ_MODE)
			{
            // 判断：当改变采样率、频点，或者不在同一的校准功率段内（同功率段主要通过校准返回的tx_link_sw_state和is_pa_on来判断）的情况下，需要重新补偿
            if (Basefun::CompareDouble(m_AmendFactors[i].Freq, Config.Freq) != 0 ||
                Basefun::CompareDouble(m_AmendFactors[i].SamplingRate, Config.SamplingRate) != 0 ||
                    Basefun::CompareDouble(m_AmendFactors[i].timeskew, CalParam.tx_iq_imb_parm.timeskew) != 0 ||
                    Basefun::CompareDouble(m_AmendFactors[i].gain_imb, CalParam.tx_iq_imb_parm.gain_imb) != 0 ||
                    Basefun::CompareDouble(m_AmendFactors[i].quad_err, CalParam.tx_iq_imb_parm.quad_err) != 0 ||
                m_AmendFactors[i].Port != Config.RFPort ||
                m_AmendFactors[i].Cal_tx_link_sw_stat != CalParam.tx_gain_parm.tx_sw_gain.sw_tx_link_state ||
                    m_AmendFactors[i].Cal_is_pa_on != CalParam.tx_gain_parm.is_pa_on ||
                    m_AmendFactors[i].ForceComp)
            {
                    // cout << Pout(m_AmendFactors[i].Freq) << Pout(Config.Freq) << endl;
                    // cout << Pout(m_AmendFactors[i].SamplingRate) << Pout(Config.SamplingRate) << endl;
                    // cout << Pout(m_AmendFactors[i].timeskew) << Pout(CalParam.tx_iq_imb_parm.timeskew) << endl;
                    // cout << Pout(m_AmendFactors[i].gain_imb) << Pout(CalParam.tx_iq_imb_parm.gain_imb) << endl;
                    // cout << Pout(m_AmendFactors[i].quad_err) << Pout(CalParam.tx_iq_imb_parm.quad_err) << endl;
                    // cout << Pout(m_AmendFactors[i].Port) << Pout(Config.RFPort) << endl;
                    // cout << Pout(m_AmendFactors[i].Cal_tx_link_sw_stat) << Pout(CalParam.tx_link_sw_state) << endl;
                    // cout << Pout(m_AmendFactors[i].Cal_is_pa_on) << Pout(CalParam.tx_gain_parm.is_pa_on) << endl;
                    // cout << Pout(m_AmendFactors[i].ForceComp) << endl;
                OtherFactorsNeedAmend = true;
            }
            }
            else
            {
                if (Basefun::CompareDouble(m_AmendFactors[i].SamplingRate, Config.SamplingRate) != 0 ||
                    m_AmendFactors[i].Port != Config.RFPort ||
                    m_AmendFactors[i].Power != Config.Power ||
                    m_AmendFactors[i].ForceComp)
                {
                    OtherFactorsNeedAmend = true;
                }
            }
            cout << Pout(OtherFactorsNeedAmend) << endl;

            std::vector<RfPnItem> RfPnItemVector; // PN项
            for (int FileId = 0; FileId < m_PnItem.size() && FileId < m_ExtPnItem.size(); FileId++) //每个信号文件分开判断是否补偿
            {
#if DEBUG
                WTLog::Instance().WriteLog(LOG_DEBUG, "WaveName[%d]=%s, m_ExtPnItem[%d].WaveName=%s\n",
                       FileId, m_AmendFactors[i].WaveName[FileId].c_str(), FileId, m_ExtPnItem[FileId].WaveName);
#endif
                //补偿前根据模块设置补偿数据的存储地址
                m_VsgData[FileId].PnData = (m_Param[FileId].IsAC8080() && i == 1)
                                               ? m_VsgData[FileId].Buf2.get()
                                               : m_VsgData[FileId].Buf.get();
                if (m_AmendFactors[i].WaveName[FileId].compare(m_ExtPnItem[FileId].WaveName) || OtherFactorsNeedAmend)
                {
                    cout << Pout(m_AmendFactors[i].WaveName[FileId]) << Pout(m_ExtPnItem[FileId].WaveName) << "AmendWaveData" << endl;
                    Ret = AmendWaveData(&CalParam, i, FileId, SamplingFreq);
                    if (Ret != WT_OK)
                    {
                        WTLog::Instance().LOGERR(Ret, "AmendWaveData failed");
                        return Ret;
                    }
                }

                RfPnItem PnItem;
                PnItem.Addr = (unsigned long)m_VsgData[FileId].PnData;
                PnItem.Len = m_PnItem[FileId].Len;
                PnItem.TBTStartVsaLen = m_PnItem[FileId].TBTStartVsaLen;
                PnItem.Loop = m_ExtPnItem[FileId].LoopCnt;
                PnItem.IFG = m_ExtPnItem[FileId].IFG;
                PnItem.TBTVsgDelay = GetTBTStaDelay(FileId, m_Param[0].SamplingFreq);
                PnItem.PnIfg = m_VsgData[FileId].FileInfo->SigHeader->PnIfg;
                PnItem.PnHead = m_VsgData[FileId].FileInfo->SigHeader->PnHead;
                PnItem.PnTail = m_VsgData[FileId].FileInfo->SigHeader->PnTail;
                PnItem.PnRepeat = m_VsgData[FileId].FileInfo->SigHeader->Repeat;
                RfPnItemVector.push_back(PnItem);
            }

            Ret = DevLib::Instance().VSGSetPNItem(m_Mods[i].ModId, RfPnItemVector, m_Param[0].SamplingFreq);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "set vsg pn config failed");
                return Ret;
            }

            //更新补偿因子内容
            m_AmendFactors[i].Freq = Config.Freq;
            m_AmendFactors[i].SamplingRate = Config.SamplingRate;
            m_AmendFactors[i].Port = Config.RFPort;
            m_AmendFactors[i].Cal_tx_link_sw_stat = CalParam.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
            m_AmendFactors[i].Cal_is_pa_on = CalParam.tx_gain_parm.is_pa_on;
            m_AmendFactors[i].Power = Config.Power;
            m_AmendFactors[i].ForceComp = false;
            m_AmendFactors[i].timeskew = CalParam.tx_iq_imb_parm.timeskew;
            m_AmendFactors[i].gain_imb = CalParam.tx_iq_imb_parm.gain_imb;
            m_AmendFactors[i].quad_err = CalParam.tx_iq_imb_parm.quad_err;
            for (int FileId = 0; FileId < m_ExtPnItem.size(); FileId++)
            {
                m_AmendFactors[i].WaveName[FileId] = string(m_ExtPnItem[FileId].WaveName);
            }
        }
    }
    SendParamToMon();
    return Ret;
}

int WTVsg::SetBroadcastMod(int Index)
{
    (void)Index;
    int Ret = WT_OK;
    double SamplingFreq = 0;
    Tx_Parm CalParam;

    if (m_PnItem.empty())
    {
        WTLog::Instance().LOGERR(WT_NO_PN_ITEM, "no pn config");
        return WT_NO_PN_ITEM;
    }

    int i = 0;
    m_AmendFactors[i].WaveName.resize(m_ExtPnItem.size());
    memset(&CalParam, 0, sizeof(Tx_Parm));
    Ret = SetExtMode(BroadcastVsg::Instance().GetModId());
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set vsg ext mode failed");
        return Ret;
    }

    VSGConfigType Config = TransConfig(m_Param[0], i);
    Config.BroadcastEnable = true;
    BroadcastVsg::Instance().AddBroadcastConfig(Config.BroadcastPower);
    Ret = DevLib::Instance().VSGSetConfig(BroadcastVsg::Instance().GetModId(), Config, CalParam);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "set vsg param to mod failed");
        return Ret;
    }
    SetActualAmpl(i, CalParam);

    if (Index != SET_PARAM_WHITOUT_PN)
    {
        // WTLog::Instance().WriteLog(LOG_DEBUG, "\n\n\nm_AmendFactors[i].Freq = %f Config.Freq = %f;\n\nm_AmendFactors[i].SamplingRate = %f Config.SamplingRate = %f;\nConfig.Power = %f \n m_AmendFactors[i].Port = %d  Config.RFPort = %d\n",
        //        m_AmendFactors[i].Freq, Config.Freq, m_AmendFactors[i].SamplingRate, Config.SamplingRate, Config.Power,m_AmendFactors[i].Port, Config.RFPort); //检查PN参数
        // WTLog::Instance().WriteLog(LOG_DEBUG, "m_AmendFactors[i].Cal_tx_link_sw_stat=%d,CalParam.tx_link_sw_state=%d,m_AmendFactors[i].Cal_is_pa_on=%d,CalParam.tx_gain_parm.is_pa_on=%d\n",
        //        m_AmendFactors[i].Cal_tx_link_sw_stat, CalParam.tx_link_sw_state, m_AmendFactors[i].Cal_is_pa_on, CalParam.tx_gain_parm.is_pa_on); //检查频率段

        bool OtherFactorsNeedAmend = false;
        // 對於模拟IQ的模式下，判断条件有变
        int AnalogIQMode = RFIQ_MODE;
        DevLib::Instance().GetAnalogIQSW(BroadcastVsg::Instance().GetModId(), AnalogIQMode);
        if (AnalogIQMode == RFIQ_MODE)
        {
            // 判断：当改变采样率、频点，或者不在同一的校准功率段内（同功率段主要通过校准返回的tx_link_sw_state和is_pa_on来判断）的情况下，需要重新补偿
            if (Basefun::CompareDouble(m_AmendFactors[i].Freq, Config.Freq) != 0 ||
                Basefun::CompareDouble(m_AmendFactors[i].SamplingRate, Config.SamplingRate) != 0 ||
                Basefun::CompareDouble(m_AmendFactors[i].timeskew, CalParam.tx_iq_imb_parm.timeskew) != 0 ||
                Basefun::CompareDouble(m_AmendFactors[i].gain_imb, CalParam.tx_iq_imb_parm.gain_imb) != 0 ||
                Basefun::CompareDouble(m_AmendFactors[i].quad_err, CalParam.tx_iq_imb_parm.quad_err) != 0 ||
                m_AmendFactors[i].Port != Config.RFPort ||
                m_AmendFactors[i].Cal_tx_link_sw_stat != CalParam.tx_gain_parm.tx_sw_gain.sw_tx_link_state ||
                m_AmendFactors[i].Cal_is_pa_on != CalParam.tx_gain_parm.is_pa_on ||
                m_AmendFactors[i].ForceComp)
            {
                OtherFactorsNeedAmend = true;
            }
        }
        else
        {
            if (Basefun::CompareDouble(m_AmendFactors[i].SamplingRate, Config.SamplingRate) != 0 ||
                m_AmendFactors[i].Port != Config.RFPort ||
                m_AmendFactors[i].Power != Config.Power ||
                m_AmendFactors[i].ForceComp)
            {
                OtherFactorsNeedAmend = true;
            }
        }

        std::vector<RfPnItem> RfPnItemVector;                                                   // PN项
        for (int FileId = 0; FileId < m_PnItem.size() && FileId < m_ExtPnItem.size(); FileId++) // 每个信号文件分开判断是否补偿
        {
#if DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "WaveName[%d]=%s, m_ExtPnItem[%d].WaveName=%s\n",
                   FileId, m_AmendFactors[i].WaveName[FileId].c_str(), FileId, m_ExtPnItem[FileId].WaveName);
#endif
            // 补偿前根据模块设置补偿数据的存储地址
            m_VsgData[FileId].PnData = (m_Param[FileId].IsAC8080() && i == 1)
                                           ? m_VsgData[FileId].Buf2.get()
                                           : m_VsgData[FileId].Buf.get();
            if (m_AmendFactors[i].WaveName[FileId].compare(m_ExtPnItem[FileId].WaveName) || OtherFactorsNeedAmend)
            {
                Ret = AmendWaveData(&CalParam, i, FileId, SamplingFreq);
                if (Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(Ret, "AmendWaveData failed");
                    return Ret;
                }
            }

            RfPnItem PnItem;
            PnItem.Addr = (unsigned long)m_VsgData[FileId].PnData;
            PnItem.Len = m_PnItem[FileId].Len;
            PnItem.TBTStartVsaLen = m_PnItem[FileId].TBTStartVsaLen;
            PnItem.Loop = m_ExtPnItem[FileId].LoopCnt;
            PnItem.IFG = m_ExtPnItem[FileId].IFG;
            PnItem.TBTVsgDelay = GetTBTStaDelay(FileId, m_Param[0].SamplingFreq);
            RfPnItemVector.push_back(PnItem);
        }
        Ret = DevLib::Instance().VSGSetPNItem(BroadcastVsg::Instance().GetModId(), RfPnItemVector, m_Param[0].SamplingFreq);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "set vsg pn config failed");
            return Ret;
        }

        // 更新补偿因子内容
        m_AmendFactors[i].Freq = Config.Freq;
        m_AmendFactors[i].SamplingRate = Config.SamplingRate;
        m_AmendFactors[i].Port = Config.RFPort;
        m_AmendFactors[i].Cal_tx_link_sw_stat = CalParam.tx_gain_parm.tx_sw_gain.sw_tx_link_state;
        m_AmendFactors[i].Cal_is_pa_on = CalParam.tx_gain_parm.is_pa_on;
        m_AmendFactors[i].Power = Config.Power;
        m_AmendFactors[i].ForceComp = false;
        m_AmendFactors[i].timeskew = CalParam.tx_iq_imb_parm.timeskew;
        m_AmendFactors[i].gain_imb = CalParam.tx_iq_imb_parm.gain_imb;
        m_AmendFactors[i].quad_err = CalParam.tx_iq_imb_parm.quad_err;
        for (int FileId = 0; FileId < m_ExtPnItem.size(); FileId++)
        {
            m_AmendFactors[i].WaveName[FileId] = string(m_ExtPnItem[FileId].WaveName);
        }
    }

    SendParamToMon();
    return Ret;
}

int WTVsg::SetExtMode(int ModId)
{
    struct ExtModeType ExtMode;
    //扩展模式配置
    if (IsTBTApMode())
    {
        ExtMode.Mode = WT_VSG_MODE_VSG_VSA;
    }
    else if (IsTBTStaMode())
    {
        ExtMode.Mode = WT_VSG_MODE_VSA_VSG;
    }
    else if (m_FemParam.FemMode)
    {
        ExtMode.Mode = WT_VSG_MODE_DEVM;
    }
    else
    {
        ExtMode.Mode = WT_VSG_MODE_NORMAL;
    }

    switch (ExtMode.Mode)
    {
    case WT_VSG_MODE_DEVM:
    {
        ExtMode.Param.Devm.LeadTime = m_FemParam.LeadTime * DEFAULT_SMAPLE_RATE;
        ExtMode.Param.Devm.DelayTime = m_FemParam.DelayTime * DEFAULT_SMAPLE_RATE;
        double GapTimeTemp = m_FemParam.LeadTime + (double)m_PnItem[0].Len / sizeof(4) / m_Param[0].SamplingFreq + m_FemParam.DelayTime;
        GapTimeTemp = (GapTimeTemp / m_FemParam.DutyRatio) * (100 - m_FemParam.DutyRatio);
        //寄存器长度32bit, 最大GAP时间约4.47秒
        ExtMode.Param.Devm.GapTime = (GapTimeTemp > 4.47) ? (GapTimeTemp * DEFAULT_SMAPLE_RATE) : (GapTimeTemp * DEFAULT_SMAPLE_RATE);
    }
    break;
    case WT_VSG_MODE_VSG_VSA:
    case WT_VSG_MODE_VSA_VSG:
    default:
        break;
    }

    return DevLib::Instance().SetExtMode(ModId, DEV_TYPE_VSG, ExtMode);
}

int WTVsg::VsgRestartTBTStaMod(int ModId)
{
    return DevLib::Instance().VSGTBTtaStart(ModId, GetDevMode()); //开启
}

int WTVsg::StartModDig()
{
    return GetDigLib().Start(DIG_STRAR_VSG);
}

int WTVsg::StartMod(int ModId)
{
    return DevLib::Instance().VSGStart(ModId); //开启
}

int WTVsg::MimoStartFin()
{
    return WT_OK;
}

//数字IQ操作完成事件回掉函数
void WTVsg::ProcDigFin(int Status)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "WTVsg::ProcDigFin stauts = %d\n", Status);
    if (Status == WT_DIGTIAL_STATUS_DONE)
    {
        m_RunStatus = MOD_RUN_FINISH;
    }
    else
    {
        WTLog::Instance().LOGERR(Status, "vsg dig iq error");
        m_RunStatus = WT_VSG_FAILED;
    }
}

bool WTVsg::ProcModFin(int Id, int Cnt)
{
    bool Release = true;
    int Status = m_Mods[GetModIndex(Id)].Status;

    DevLib::Instance().VSGFinish(Id);
    
    if (m_FinAgent != nullptr) //lint !e1060
    {
        return m_FinAgent(Id);
    }

    if (Status == WT_RX_TX_STATE_DONE) //硬件完成
    {
        //MIMO或者从机已返回才返回最终结果
        if (m_Mods.size() == Cnt)
        {
            Release = true;
            m_RunStatus = MOD_RUN_FINISH;
        }
    }
    else
    {
        WTLog::Instance().LOGERR(Status, "vsg error");
        m_RunStatus = WT_RX_TX_STATE_ERR_DONE;
    }

    return Release;
}

void WTVsg::ClearMod(int NeedFreeCnt)
{
    if (NeedFreeCnt == -1) // NeedFreeCnt=-1, 表示TBT模式，VSA已完成，通知VSG释放资源。
    {
        if (GetVsg().IsTBTModeFinish()) // VSA/VSG都完成, 释放VSG VSA资源
        {
            for (auto &Mod : m_Mods)
            {
                DevLib::Instance().ClearExtMode(Mod.ModId);
            }
            GetVsa().GetModInfo()->clear();
            GetVsg().GetModInfo()->clear();
            GetVsa().ClearTBTMode();                //清除模式标记
            GetVsg().ClearTBTMode();
        }
    }
    else if (NeedFreeCnt == m_Mods.size()) //判断模块是否全部完成
    {
        if (!IsTBTMode()) //非TBT模式时，释放资源。
        {
            m_Mods.clear();
        }
        else // TBT模式
        {
            GetVsg().SetTBTModeVsgFinish(); //设置完成标记
            GetVsa().ClearMod(-1);         //通知VSA，VSA/VSG都完成后才能释放VSA资源。
        }
    }
}

int WTVsg::TransFileToSlave(Connector *Conn)
{
    int i = 0, j;
    m_DevStaMask = 0;

    for (auto &Slave : m_SlaveConn)
    {
        if (!Slave->IsLocal())
        {
            //同一台从机只下发一次
            auto iter = m_SlaveConn.begin();
            for (j = 0; j < i; j++, iter++)
            {
                if ((*iter)->IsSameDevIP(*Slave))
                {
                    break;
                }
            }

            if (j == i)
            {
                int Ret = Slave->ForwardCmd(Conn);
                if (Ret != WT_OK)
                {
                    return Ret;
                }
            }
            else
            {
                m_DevStaMask |= 1 << i;
            }
        }
        else
        {
            m_DevStaMask |= 1 << i;
        }

        i++;
    }

    if (!IsMimoDone())
    {
        Conn->SetRsp(false);
    }

    return WT_OK;
}

void WTVsg::CreatePath(const std::string &LocalDir, std::string &NeedCreatePath)
{
    int iPos = 0;
    string Dir = LocalDir;
    string CurCreateDir = "";
    int Result;

    while (iPos >= 0)
    {
        iPos = NeedCreatePath.find('/');
        CurCreateDir = CurCreateDir + NeedCreatePath.substr(0, iPos);
        Dir = Dir + CurCreateDir;
        if (-1 == (Result = access(Dir.c_str(), 0)))   //该目录不存在
        {
            if (-1 == (Result = mkdir(Dir.c_str(), 0755)))     //创建目录
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Create path error!" << endl;
            }
        }
        Dir = Dir + "/";
        NeedCreatePath = NeedCreatePath.substr(iPos + 1, NeedCreatePath.size());
        CurCreateDir = "";
    }
}

void WTVsg::DealFilePath(const std::string &Name)
{
    //如果是带路径的name，判断路径目录是否存在，不存在则创建目录
    if ( -1 != Name.find("/"))
    {
        int FirstDirPathPos = Name.find_first_of("/");
        string FirstDirPath = Name.substr(0, FirstDirPathPos); //获取一级目录文件名

        int Pos = Name.find_last_of("/");
        string DirPath = GetLowWaveDir() + Name.substr(0, Pos);

        struct stat FileStat;
        if((stat(DirPath.c_str(), &FileStat) != 0) || !S_ISDIR(FileStat.st_mode))
        {
            //如果连一级目录都没有，则要创建
            std::string wave("/low_wave/");
            if (-1 == access((WTConf::GetDir() + wave  + FirstDirPath).c_str(), F_OK))
            {
                if (-1 == mkdir((WTConf::GetDir() + wave + FirstDirPath).c_str(), 0755)) //建立目录
                {
                    WTLog::Instance().LOGERR(WT_ERROR, "Create Dir Failed!");
                    return ;
                }
                if (-1 == symlink((WTConf::GetDir() + wave + FirstDirPath).c_str(), (GetLowWaveDir() + FirstDirPath).c_str())) //建立软链接
                {
                    WTLog::Instance().LOGERR(WT_ERROR, "Create symbol link Failed!");
                    return ;
                }
            }

            string LinkDir = GetLowWaveDir();
            string NeedCreatePath = Name.substr(0, Pos);
            CreatePath(LinkDir, NeedCreatePath);
        }
    }
}

int WTVsg::ExtSigFile(Connector *Conn, const std::string &Name, void *Data, int Len)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        //将文件转发给从机
        if (!m_SlaveConn.empty())
        {
            Ret = TransFileToSlave(Conn);
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "transmit file to slave failed");
                return Ret;
            }
        }

        if (m_Compensate == nullptr)
        {
            m_Compensate.reset(new Compensate());
            if (m_Compensate == nullptr)
            {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
                return WT_ALLOC_FAILED;
            }
        }

        DealFilePath(Name); //文件名如果带路径的，先检查目录是否存在，不存在则创建目录~
        //将文件归一化后再保存，这样meter VSG视图显示比较好
        Ret = m_Compensate->NormalizationFile(Data, Len, GetLowWaveDir() + Name);
        if (Ret != WT_OK)
        {
            return Ret;
        }

//        string FileName = GetWaveDir() + Name;
//        Ret = SaveFile(FileName, Data, Len);
//        if (Ret != WT_OK)
//        {
//            return Ret;
//        }
    }
    else
    {
        Ret = ProcMimoAck(Conn);
    }

    return Ret;
}

int WTVsg::GenerateSig(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    //WTLog::Instance().WriteLog(LOG_DEBUG, "####Generate length=%d-%d\n", ParamLen , (int)sizeof(stGeneratorSet));
    if (ParamLen != (signed)sizeof(stGeneratorSet))
    {
        WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
        return WT_CMD_ERROR;
    }

    //协议license判断
    int Demo = WT_DEMOD_UNKNOW;
    int Standard = ((stGeneratorSet*)Param)->WaveSet.Standard;

    switch (Standard)
    {
        case IEEE802_11_a_g_OFDM:
            Demo = WT_DEMOD_11AG;
            break;
        case IEEE802_11_b_g_DSSS:
            Demo = WT_DEMOD_11B;
            break;
        case IEEE802_11_n:
            Demo = WT_DEMOD_11N_20M;
            break;
        case IEEE802_11_ac:
            Demo = WT_DEMOD_11AC_20M;
            break;
        case Bluetooth:
            Demo = WT_DEMOD_BT;
            break;
        case IEEE_Zigbee:
            Demo = WT_DEMOD_ZIGBEE;
            break;
        case ContinuousWaves:
            Demo = WT_DEMOD_CW;
            break;
        case IEEE802_11_ax:
            Demo = WT_DEMOD_11AX_20M;
            break;
        default:
            Demo = WT_DEMOD_UNKNOW;
            break;
    }
    //WTLog::Instance().WriteLog(LOG_DEBUG, "####((stGeneratorSet*)Param)->WaveSet.Standard=%d,Demo = %d\n",((stGeneratorSet*)Param)->WaveSet.Standard,Demo);
    if (!m_Alg.CheckDemodLic(Demo))
    {
        WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "demode license not exist");
        return WT_LIC_NOT_EXIST;
    }

    int Ret = VSGSignalGenerate((stGeneratorSet*)Param, (Complex **)FileData, FileLen);

    for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
    {
        *(FileLen+i) = (*(FileLen+i)) * sizeof(Complex);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "GenerateSig len%d = %d\n",i,*(FileLen+i));
    }

    if (Ret != WT_OK)
    {
        Ret += WT_VSG_ALG_BASE_ERROR;
        WTLog::Instance().LOGERR(Ret, "generate vsg signal failed");
        return Ret;
    }

    return WT_OK;
}

int WTVsg::GetDefaultGenParam(int Demode, void **Data, int &Len)
{
    const unsigned int Rate = MAX_SMAPLE_RATE;

    if (m_GenParam == nullptr)
    {
        m_GenParam.reset(new(std::nothrow) char[sizeof(stGeneratorSet)]);
        if (m_GenParam == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc generator param failed");
            return WT_ALLOC_FAILED;
        }

        memset(m_GenParam.get(), 0, sizeof(stGeneratorSet));
    }

    *Data = m_GenParam.get();
    Len = sizeof(stGeneratorSet);
    stGeneratorSet *Param = reinterpret_cast<stGeneratorSet*>(m_GenParam.get());

    switch (Demode)
    {
    case WT_DEMOD_11AG:
        Param->WaveSet.Standard = IEEE802_11_a_g_OFDM;
        Param->WaveSet.SignalType = Mbps54;
        Param->WaveSet.DACRate = Rate;
        Param->PsduSet.WaveGap = 10;
        Param->PsduSet.Scrambler = 12;
        Param->PsduSet.PSDUType  = enPSDUType_RANDOM;
        Param->PsduSet.PSDULen = 1000;
        break;

    case WT_DEMOD_11B:
        Param->WaveSet.Standard = IEEE802_11_b_g_DSSS;
        Param->WaveSet.SignalType = Mbps11;
        Param->WaveSet.DACRate = Rate;
        Param->PsduSet.WaveGap = 10;
        Param->WaveSet.Preamble = PRE_LONG_11B;
        Param->PsduSet.PSDUType  = enPSDUType_RANDOM;
        Param->PsduSet.PSDULen = 1000;
        break;

    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        Param->WaveSet.Standard = IEEE802_11_n;
        Param->WaveSet.SignalType = MCS7;
        Param->WaveSet.McsValid = TRUE;
        Param->WaveSet.Mcs = 7;
        Param->WaveSet.PacketType = FRM_TYPE_HT_MF;
        Param->WaveSet.StreamsCnt = 1;
        Param->WaveSet.DACRate = Rate;
        Param->PsduSet.WaveGap = 10;
        Param->PsduSet.Scrambler = 12;
        Param->WaveSet.Bandwidth = 20;
        Param->WaveSet.SoundingPacket = 0;
        Param->WaveSet.Aggregation = 0;
        Param->WaveSet.GuardInterval = 0;
        Param->PsduSet.PSDUType  = enPSDUType_RANDOM;
        Param->PsduSet.PSDULen = 1000;
        break;

    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        Param->WaveSet.Standard = IEEE802_11_ac;
        Param->WaveSet.SignalType = NSS1_MCS7;
        Param->WaveSet.McsValid = TRUE;
        Param->WaveSet.Mcs = 7;
        Param->WaveSet.Bandwidth = 20;
        Param->WaveSet.StreamsCnt = 1;
        Param->WaveSet.DACRate = Rate;
        Param->PsduSet.WaveGap = 10;
        Param->PsduSet.Scrambler = 12;
        Param->WaveSet.GuardInterval = 0;
        Param->PsduSet.PSDUType  = enPSDUType_RANDOM;
        Param->PsduSet.PSDULen = 1000;
        break;

    case WT_DEMOD_BT:
        Param->WaveSet.Standard = Bluetooth;
        Param->WaveSet.SignalType = Mbps3_11110000;
        Param->WaveSet.DACRate = Rate;
        //Param->PacketSet.PackType = enPacketType_DH5;  //TODO
        Param->PacketSet.payLoadType = 1;
        Param->PacketSet.ModuIdex = 0.32;
        Param->PacketSet.RolloffIdex = 0.40;
        Param->PacketSet.BTProductIdex = 0.5;
        Param->PacketSet.GuardTime = 5;
        Param->PacketSet.PowerRampTime = 2;

        Param->PacketSet.LAP = 0xC69612;
        Param->PacketSet.UAP = 0x6B;
        Param->PacketSet.NAP = 0x0000;
        Param->PacketSet.LT_ADDR = 1;
        Param->PacketSet.Flow = 0;
        Param->PacketSet.ARQN = 0;
        Param->PacketSet.SEQN = 0;
        Param->PacketSet.DataWhitening = 0;
        Param->PacketSet.LLID = 0;
        Param->PacketSet.mFlow = 0;
        Param->PsduSet.PSDUType  = enPSDUType_RANDOM;
        Param->PsduSet.PSDULen = 50;
        break;

    default:
        return WT_VSG_GEN_DEMODE_ERROR;
    }

    return WT_OK;
}

int WTVsg::GetVsgPowerRange(int Type, double Freq, double &MaxPower, double &MinPower)
{
    // TODO 需要从校准文件中获取
    (void)Type;
    (void)Freq;

    MaxPower = 10;
    MinPower = -70;

    return WT_OK;
}

string WTVsg::GetWaveFile(const ExtPnItem &Pn)
{
    string Wave("");

    if (Pn.WaveSource == WT_SOURCE_WAVE)
    {
        Wave = WaveIdxName::GetWaveName(Pn.WavePreset);
        if (!Wave.empty())
        {
            Wave = m_CurDir + "/low_wave/" + Wave;
        }
        else
        {
            Wave.clear();
        }
    }
    else
    {
        if (strlen(Pn.WaveName) > 0)
        {
            Wave = GetLowWaveDir() + Pn.WaveName;
        }
    }

    return Wave;
}

bool WTVsg::IsMultiPn(const string WaveName)
{
    size_t pos_1 = (WaveName).find(".PN");
    size_t pos_2 = (WaveName).find(".low", pos_1);
    return (string::npos != pos_1 && string::npos != pos_2);
}

int WTVsg::SetPNCfg(Connector *Conn, const PnItemHead *PnHead, int HeadNum, const ExtPnItem *Items, int ItemNum)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        //保存当前需要对外的pn协议命令
        m_CurRequest.reset(new (std::nothrow)char[Conn->GetSock().GetBufSize()]);
        memcpy(m_CurRequest.get(), Conn->GetSock().GetRxBuf(), Conn->GetSock().GetBufSize());

        if (HeadNum <= 0 || ItemNum <= 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "pn number error");
            return WT_ARG_ERROR;
        }

        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret != WT_OK)
            {
                WTLog::Instance().LOGERR(Ret, "send pn data to mimo dev failed");
                return Ret;
            }

            Conn->SetRsp(false);
        }
        // m_PnItem和m_VsgData要保留一部分原有数据所以不清除
        m_VsgData.resize(ItemNum);
        m_PnItem.resize(ItemNum);
        m_PnItemHead.clear();
        // m_ExtPnItem.clear();
        m_ExtPnItem.resize(ItemNum);
		m_FileBw.clear();
        stringstream Strs;
        Strs.clear();
        Strs.str("");
        Strs << "SetPNCfg: HeadNum = " << HeadNum << ", ItemNum = " << ItemNum << endl;
        for (int i = 0; i < HeadNum; i++)
        {
            Strs << "PnItemHead:{" << endl
                 << "    StartIdx:" << PnHead[i].StartIdx << endl
                 << "    SendCnt:" << PnHead[i].SendCnt << endl
                 << "    }" << endl;
        }
        for (int i = 0; i < ItemNum; i++)
        {
            Strs << "ExtPnItem:{" << endl
                 << "    WaveSource:" << Items[i].WaveSource << endl
                 << "    WavePreset:" << Items[i].WavePreset << endl
                 << "    WaveName:" << Items[i].WaveName << endl
                 << "}" << endl;
        }
        const string &Str2 = Strs.str();
        WTLog::Instance().LOGOPERATE(Str2.c_str());

        for (int i = 0; i < HeadNum; i++)
        {
            m_PnItemHead.push_back(PnHead[i]);
        }

        //保存下发的wave name，主要用于Mimo主机给从机发送信号文件使用
        m_FileName.clear();
        m_FileName = Items[0].WaveName;
        //#if TIME_DEBUG
        //    struct timeval tpstart, tpend;
        //    gettimeofday(&tpstart, NULL);
        //#endif

        if (DigModeLib::Instance().IsDigMode())
        {
            m_MaxPower = UNVALID_DOUBLE_VAL;
            for (auto &Ch : m_ChPower)
            {
                Ch = UNVALID_DOUBLE_VAL;
            }
        }
        else
        {
            m_Alg.GetMimoFileMaxPower(GetWaveFile(Items[0]), m_MaxPower, m_ChPower);
        }

        //#if TIME_DEBUG
        //    gettimeofday(&tpend, NULL);
        //    int timeuse = (tpend.tv_sec - tpstart.tv_sec) * 1e6 + (tpend.tv_usec - tpstart.tv_usec);
        //    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "##GetMimoFileMaxPower Used Time:" << timeuse << "us" << std::endl;
        //#endif

        for (int i = 0; i < ItemNum; i++)
        {
            std::unique_ptr<ReadFile> File;
            std::string filePath = GetWaveFile(Items[i]);
            File.reset(new (std::nothrow) ReadFile(filePath));
            if (File == NULL)
            {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "File alloc failed!");
                return WT_ALLOC_FAILED;
            }

            //只有当信号文件改变时，更改信号数据
            //由于SCPI层已经处理，这里认为不存在文件名相同但文件内容不一样的情况
            if (m_ExtPnItem[i].WaveSource != Items[i].WaveSource ||
                (Items[i].WaveSource == WT_SOURCE_WAVE && m_ExtPnItem[i].WavePreset != Items[i].WavePreset) ||
                (Items[i].WaveSource != WT_SOURCE_WAVE && strcmp(m_ExtPnItem[i].WaveName, Items[i].WaveName) != 0))
            {
                VsgDataInfo DataInfo;
                Ret = GetWaveData(filePath, DataInfo);
                if (Ret != WT_OK)
                {
                    Conn->SetRsp(true);
                    return Ret;
                }
                DataInfo.WaveSource = Items[i].WaveSource;
                m_VsgData[i] = move(DataInfo);
                m_PnItem[i].TBTStartVsaLen = TB_START_VSA_POS;
                m_PnItem[i].Addr = (unsigned long)m_VsgData[i].PnData; //数据地址在补偿前会重新设置
                m_PnItem[i].Len = m_VsgData[i].PnDataLen;
            }
            m_ExtPnItem[i] = Items[i];
            m_FileBw.push_back(m_VsgData[i].FileInfo ? DemodFun::GetSigBw(m_VsgData[i].FileInfo->SigHeader->ModType) : WT_BW_CW);
        }

    }
    else
    {
        ProcMimoAck(Conn, false);
        Ret = Conn->GetCmdResult();

        if (!IsMimoDone()) //如果没有成功完成
        {
            if (Ret != WT_OK)
            {
                if (Ret == WT_OPEN_FILE_FAILED) // WT_NO_PN_ITEM//从机返回配置pn错误，则从主机获取信号文件内容，下发一次信号文件到从机
                {
                    // WTLog::Instance().WriteLog(LOG_DEBUG, "###FileName=%s#####\n",m_FileName.c_str());
                    Ret = SendVsgFileMaterToSlave(Conn, m_FileName);
                    if (Ret != WT_OK)
                    {
                        Ret = m_ExtConn->Response(Ret);
                    }
                }
                else
                {
                    Ret = m_ExtConn->Response(Ret);
                }
            }
        }
        else
        {
            Ret = m_ExtConn->Response(Ret);
        }
    }
    return Ret;
}

int WTVsg::GetPNCfg(vector<PnItemHead> &PnHead, vector<ExtPnItem> &Item)
{
    PnHead = m_PnItemHead;
    Item = m_ExtPnItem;

    return WT_OK;
}

int WTVsg::GetWaveData(const string &WaveFile, VsgDataInfo &DataInfo)
{
    if (WaveFile.empty())
    {
        WTLog::Instance().LOGERR(WT_NO_SIG_DATA, "vsg sigfile not exist");
        return WT_NO_SIG_DATA;
    }

    DataInfo.WaveFile.reset(new(std::nothrow) SigFile(WaveFile, SigFile::READ));

    SigFileInfo *FileInfo = nullptr;
    int Ret = DataInfo.WaveFile->GetContent(&FileInfo);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "VSG sigfile error");
        return Ret;
    }

    DataInfo.FileInfo = FileInfo;

    // 带头信息的信号文件
    if (FileInfo != nullptr)
    {
        //按最大长度计算内存
        DataInfo.PointNum = 0;
        int SigNum = FileInfo->GetSigNum();

        for (int i = 0; i < SigNum; i++)
        {
            DataInfo.PointNum = max(DataInfo.PointNum, FileInfo->SigHeader[i].SampleCount);
        }
    }
    // 不带头信息的纯数据信号文件
    else
    {
        DataInfo.PointNum = DataInfo.WaveFile->GetFileSize() / (signed)sizeof(Complex);
    }

    //PN使用的数据是将原始数据从double转换为short
    DataInfo.PnDataLen = DataInfo.PointNum * sizeof(int);

    DataInfo.Buf.reset(new(std::nothrow) char[DataInfo.PnDataLen]);
    if (DataInfo.Buf == nullptr)
    {
        WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc vsg buffer failed");
        return WT_ALLOC_FAILED;
    }

    DataInfo.PnData = DataInfo.Buf.get();

    if (FileInfo->GetSigNum() >= 2)
    {
        //多流的情况下申请额外内存，主要是用来存储8080模式时第二模块的已补偿数据
        DataInfo.Buf2.reset(new (std::nothrow) char[DataInfo.PnDataLen]);
        if (DataInfo.Buf2 == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc vsg buffer failed");
            return WT_ALLOC_FAILED;
        }
    }
    return WT_OK;
}

int WTVsg::AmendWaveData(const Tx_Parm *Param, int Chain, double &SamplingFreq)
{
    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }

    double DacMargin = 0;
    if (DigModeLib::Instance().IsDigMode() == false)
    {
        Rf_Config_Parm RfParam;
        wt_calibration_get_configuration(&RfParam);
        DacMargin = RfParam.dac_margin;
    }

    int Ret = WT_OK;
    int SigIndex = 0;
    int Segment = m_Param[0].IsAC8080() ? 2 : 1;
    
    for (int i = 0; i < (signed)m_VsgData.size(); i++)
    {
        auto &VsgData = m_VsgData[i];
        // 注意这里处理2种类型的信号文件
        // 1) 带头信息的信号文件
        if (VsgData.FileInfo != nullptr)
        {
            //获取PN数据的实际长度
            if (DigModeLib::Instance().IsDigMode())
            {
                //SigIndex = Segment * m_Param[Chain].SignalId;
                SigIndex = Chain;
            }
            else
            {
                SigIndex = Chain + Segment * m_Param[0].SignalId;
            }

            if (SigIndex < VsgData.FileInfo->GetSigNum())
            {
                m_Compensate->SetCurrenAmendStream(SigIndex);  //告知当前补偿的是哪一流，配合ofdma功率按流给ru carrier 信息使用

                SamplingFreq = VsgData.FileInfo->SigHeader[SigIndex].SamplingRate;
                m_PnItem[i].Len = VsgData.FileInfo->SigHeader[SigIndex].SampleCount * sizeof(int);
                Ret = m_Compensate->AmendWaveData(&VsgData.FileInfo->SigHeader[SigIndex],
                                                   DacMargin, Param, VsgData.PnData);

#ifdef DEBUG
                WTLog::Instance().WriteLog(LOG_DEBUG, "###AmendWaveData m_MaxPower=%lf, m_ChPower=%lf, SigIndex=%d, Chain=%d, m_Param[0].SigIndex=%d\n",
                       m_MaxPower, m_ChPower[SigIndex], SigIndex, Chain, m_Param[0].SignalId);
#endif

                if(Basefun::CompareDouble(m_MaxPower, UNVALID_DOUBLE_VAL) != 0 && fabs(m_MaxPower - m_ChPower[SigIndex]) > MIMO_MAX_DIFF_POWER)
                {
                    WT_DEBUG(WT_SIG_FILE_ERROR, "Noise signal stream will send zero data.");
                    memset(VsgData.PnData, 0, VsgData.FileInfo->SigHeader[0].SampleCount * sizeof(int));
                }
            }
            //MIMO流数不足时从机发0
            else if (DigModeLib::Instance().IsDigMode() || (m_Param[0].SignalId > 0 && m_Param[0].SignalId >= VsgData.FileInfo->ChainNum))
            {
                memset(VsgData.PnData, 0, VsgData.FileInfo->SigHeader[0].SampleCount * sizeof(int));
                WT_DEBUG(WT_SIG_FILE_ERROR, "singnal file has no enough stream");
            }
            else
            {
                Ret = WT_SIG_FILE_ERROR;
            }
        }
        // 2)不带头信息的纯数据信号文件
        else if (VsgData.WaveFile != nullptr)
        {
            SamplingFreq = m_Param[0].SamplingFreq;
            Ret = m_Compensate->AmendWaveData(static_cast<Complex*>(VsgData.WaveFile->GetFileBuf()),
                                              VsgData.PointNum, m_Param[0].SamplingFreq,
                                              DacMargin, Param, VsgData.PnData);
        }
        else
        {
            Ret = WT_OPEN_WAVE_FAILED;
        }

        if (Ret != WT_OK)
        {
            return Ret;
        }
    }

    return WT_OK;   //lint !e438
}                   //lint !e550

int WTVsg::AmendWaveData(const Tx_Parm *Param, int Chain, int FileId, double &SamplingFreq)
{
    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }
    
    double DacMargin = 0;
    if (DigModeLib::Instance().IsDigMode() == false)
    {
        Rf_Config_Parm RfParam;
        wt_calibration_get_configuration(&RfParam);
        DacMargin = RfParam.dac_margin;
    }
    
    int Ret = WT_OK;
    int SigIndex = 0;
    int Segment = m_Param[0].IsAC8080() ? 2 : 1;
    auto &VsgData = m_VsgData[FileId];
    // 注意这里处理2种类型的信号文件
    // 1) 带头信息的信号文件
    if (VsgData.FileInfo != nullptr)
    {
        //获取PN数据的实际长度
        if (DigModeLib::Instance().IsDigMode())
        {
            SigIndex = Chain;
        }
        else
        {
            SigIndex = Chain + Segment * m_Param[0].SignalId;
        }

        if (SigIndex < VsgData.FileInfo->GetSigNum())
        {
            m_Compensate->SetCurrenAmendStream(SigIndex); //告知当前补偿的是哪一流，配合ofdma功率按流给ru carrier 信息使用

            SamplingFreq = VsgData.FileInfo->SigHeader[SigIndex].SamplingRate;
            m_PnItem[FileId].Len = VsgData.FileInfo->SigHeader[SigIndex].SampleCount * sizeof(int);
            Ret = m_Compensate->AmendWaveData(&VsgData.FileInfo->SigHeader[SigIndex],
                                              DacMargin, Param, VsgData.PnData);

#ifdef DEBUG
            WTLog::Instance().WriteLog(LOG_DEBUG, "###AmendWaveData m_MaxPower=%lf, m_ChPower=%lf, SigIndex=%d, Chain=%d, m_Param[0].SigIndex=%d\n",
                   m_MaxPower, m_ChPower[SigIndex], SigIndex, Chain, m_Param[0].SignalId);
#endif

            if (Basefun::CompareDouble(m_MaxPower, UNVALID_DOUBLE_VAL) != 0 && fabs(m_MaxPower - m_ChPower[SigIndex]) > MIMO_MAX_DIFF_POWER)
            {
                WT_DEBUG(WT_SIG_FILE_ERROR, "Noise signal stream will send zero data.");
                memset(VsgData.PnData, 0, VsgData.FileInfo->SigHeader[0].SampleCount * sizeof(int));
            }
        }
        // MIMO流数不足时从机发0
        else if (DigModeLib::Instance().IsDigMode() || (m_Param[0].SignalId > 0 && m_Param[0].SignalId >= VsgData.FileInfo->ChainNum))
        {
            memset(VsgData.PnData, 0, VsgData.FileInfo->SigHeader[0].SampleCount * sizeof(int));
            WT_DEBUG(WT_SIG_FILE_ERROR, "singnal file has no enough stream");
        }
        else
        {
            Ret = WT_SIG_FILE_ERROR;
        }
    }
    // 2)不带头信息的纯数据信号文件
    else if (VsgData.WaveFile != nullptr)
    {
        SamplingFreq = m_Param[0].SamplingFreq;
        Ret = m_Compensate->AmendWaveData(static_cast<Complex *>(VsgData.WaveFile->GetFileBuf()),
                                          VsgData.PointNum, m_Param[0].SamplingFreq,
                                          DacMargin, Param, VsgData.PnData);
    }
    else
    {
        Ret = WT_OPEN_WAVE_FAILED;
    }

    if (Ret != WT_OK)
    {
        return Ret;
    }

    return WT_OK; // lint !e438
} // lint !e550

int WTVsg::SetVsgIfgEnable(Connector *Conn, int Enable)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        if (Enable < 0)
        {
            WTLog::Instance().LOGERR(WT_ARG_ERROR, "Enable param <= 0");
            return WT_ARG_ERROR;
        }

        //将命令转发给从机
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "set mimo ifg failed");
            }
        }
        DevLib::Instance().VSGSetIfgCtrlEnable(GAP_POWER_NOT_DEBUG);
        m_GapPowerEnable = Enable ? 1 : 0;
    }
    else
    {
        Ret = ProcMimoAck(Conn);
    }
    return Ret;
}

void WTVsg::SaveStackData(void)
{
    m_Alg.SaveStackDataFile();
}

int WTVsg::CheckCalParam(int mode)
{
    (void)mode;
    if (m_Param.get() == nullptr || m_ParamNum == 0)
    {
        WTLog::Instance().LOGERR(WT_NEED_SET_MOD, "Vsg NO config parameter");
    }
    else if (m_Param[0].IsAC8080())
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsg parameter can not be 8080");
    }
    else if (m_Param[0].Type != TEST_SISO && m_Param[0].Type != TEST_SELF_MIMO && m_Param[0].Type != TEST_MULTI_MIMO)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "Vsg parameter Type Error");
    }
    else if (m_ExtPnItem.size() == 0 || m_ExtPnItem[0].LoopCnt != 0)
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsg m_Mods.size must be 1");
    }
    else if (m_Mods.size() != 1)
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsg m_Mods.size must be 1");
    }
    else if (!m_Mods[0].IsConfig)
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "Vsg Mods not config");
    }
    return WT_OK;
}

int WTVsg::CheckTBTParam(int mode)
{
    if (m_Param.get() == nullptr || m_ParamNum == 0)
    {
        WTLog::Instance().LOGERR(WT_NEED_SET_MOD, "Vsg NO config parameter");
    }
    else if (m_Param[0].IsAC8080())
    {
        WTLog::Instance().LOGERR(WT_MOD_NUM_ERROR, "TBT Vsg parameter can not be 8080");
    }
    else if (m_Param[0].Type != TEST_SISO && m_Param[0].Type != TEST_SELF_MIMO && m_Param[0].Type != TEST_MULTI_MIMO)
    {
        WTLog::Instance().LOGERR(WT_ARG_ERROR, "TBT Vsg parameter Type Error");
    }
    else if(m_PnItem.size() == 0 || m_ExtPnItem.size() == 0)
    {
        WTLog::Instance().LOGERR(WT_NO_PN_ITEM, "No Pn data");
    }
    else
    {
        if (!DigModeLib::Instance().IsDigMode())
        {
            if (Basefun::CompareDouble(m_Param[0].SamplingFreq, MAX_SMAPLE_RATE))
            {
                WTLog::Instance().LOGERR(WT_ARG_ERROR, "Vsg parameter SamplingFreq must be default");
                return WT_ARG_ERROR;
            }

            if (mode == TBT_MODE_AP)
            {
                if (m_ExtPnItem[0].LoopCnt != 1)
                {
                    WTLog::Instance().LOGERR(WT_ARG_ERROR, "TBT AP Vsg Pn Loop must be 1");
                    return WT_ARG_ERROR;
                }
            }
            else if (mode == TBT_MODE_STA)
            {
                if ((int)m_ExtPnItem[0].IFG != 0)
                {
                    WTLog::Instance().LOGERR(WT_ARG_ERROR, "TBT STA Vsg IFG must be 0");
                    return WT_ARG_ERROR;
                }
                // WTLog::Instance().WriteLog(LOG_DEBUG, "m_ExtPnItem[0].StartDelay = %d\n", m_ExtPnItem[0].StartDelay);
                // else if (m_ExtPnItem[0].StartDelay < 5000)
                // {
                //     WTLog::Instance().LOGERR(WT_ARG_ERROR, "TBT STA Delay must more than 5us");
                //     return WT_ARG_ERROR;
                // }
            }
        }
        return WT_OK;
    }
    return WT_ARG_ERROR;
}

int WTVsg::TbStartVsaPosCompute(int Mode)
{
    int Pos = 0;
    bool FirstFrame = false;
    int ZeroCnt = 0;
    int PnCount = 0;
    int DataThreshold = m_TBTConf.APIQDataThreshold;

    typedef short ComplexShort[2];
    typedef double ComplexDouble[2];
    ComplexDouble *pDataD = nullptr;
    ComplexShort *pDataS = nullptr;

    if (Mode == TB_COMPUTE_BEFORE_AMEND)
    {
        pDataD = reinterpret_cast<ComplexDouble *>(m_VsgData[0].FileInfo->SigHeader[0].Data);
        PnCount = m_VsgData[0].FileInfo->SigHeader[0].SampleCount;
    }
    else
    {
        pDataS = reinterpret_cast<ComplexShort *>(m_PnItem[0].Addr);
        PnCount = m_PnItem[0].Len / sizeof(ComplexShort);
    }

    for(ZeroCnt = 0; Pos < PnCount; Pos++)
    {
        //IQ均小于3，认为是0。
        if ((Mode == TB_COMPUTE_AFTER_AMEND && abs(pDataS[Pos][0]) <= DataThreshold && abs(pDataS[Pos][1]) <= DataThreshold) ||
            (Mode == TB_COMPUTE_BEFORE_AMEND && Basefun::CompareDouble(abs(pDataD[Pos][0]), 0) <= 0 && Basefun::CompareDouble(abs(pDataD[Pos][1]), 0) <= 0))
        {
            ZeroCnt++;
        }
        else
        {
            if(FirstFrame == true)
            {
                //连续1us以上为零，表示Gap
                if (ZeroCnt >= m_Param[0].SamplingFreq / 1e6)
                {
                    //以第一帧结束的位置为TB模式的VSA SATAR点
                    break;
                }
            }
            else
            {
                FirstFrame = true;
            }
            ZeroCnt = 0;
        }
    }

    int DevMode = GetDevMode();
    if (DevMode != DEVICE_MODE_SISO)
    {
        int MimoCompensate = 0;
        //从机额外延时
        if (DevMode != DEVICE_MODE_MIMO_MULTI_MASTER && DevMode != DEVICE_MODE_MIMO_SINGLE_MASTER)
        {
            MimoCompensate += m_TBTConf.APSlaveCompensate;
            //本机的从机额外延时,在SlaveCompensate补偿的基础上再次补偿
            if (DevMode == DEVICE_MODE_MIMO_SINGLE_SLAVE)
            {
                MimoCompensate += m_TBTConf.APSingleSlaveCompensate;
            }
        }
        Pos = Pos + (m_Param[0].SamplingFreq / 1e9) * MimoCompensate;
    }

    if(Pos >= m_Param[0].SamplingFreq / 1e6 && Pos <= PnCount)
    {
        Pos = Pos - ZeroCnt;
        WTLog::Instance().WriteLog(LOG_DEBUG, "TbStartVsaPosCompute Pos=%d, ZeroCnt=%d\n", Pos, ZeroCnt);
        return Pos;
    }
    else
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "TbStartVsaPosCompute Pos=End\n");
        return PnCount;
    }
}

void WTVsg::SetAndBackupParamTb(void)
{
    m_UserIfg = m_ExtPnItem[0].IFG;
    m_UserLoop = m_ExtPnItem[0].LoopCnt;
    m_TestType = m_Param[0].Type;

    m_ExtPnItem[0].IFG = (100 * 1e-6) * m_Param[0].SamplingFreq; //100us
    m_ExtPnItem[0].LoopCnt = 0;
    m_Param[0].Type = TEST_SISO;
}

void WTVsg::RestoreParam(void)
{
    m_ExtPnItem[0].IFG = m_UserIfg;
    m_ExtPnItem[0].LoopCnt = m_UserLoop;
    m_Param[0].Type = m_TestType;
    m_Mods.clear();
}

int WTVsg::GetSigFile(const std::string &Name, void **Data, int &Len)
{
    string File = GetLowWaveDir() + Name;
    if (-1 == access(File.c_str(), F_OK))   //不存在该文件
    {
        return WT_OPEN_WAVE_FAILED;
    }

    m_SigFile.reset(new(std::nothrow) SigFile(File, SigFile::READ));
    if (nullptr == m_SigFile  || nullptr ==  m_SigFile->GetFileBuf() )
    {
        return WT_OPEN_FILE_FAILED;
    }
    else
    {
        *Data = const_cast<void*>(m_SigFile->GetFileBuf());
        Len = m_SigFile->GetFileSize();
        return WT_OK;
    }
}

int WTVsg::SendVsgFileMaterToSlave(Connector *Conn, const std::string &Name)
{
    //获取主机文件及其内容,然后发送到从机
    int Ret = WT_OK;
    int FileLen = 0;
    void *FileBuf = nullptr;

    Ret = GetSigFile(Name, &FileBuf, FileLen);

    if(Ret == WT_OK)
    {
        Ret = Conn->SendVsgFileToSalve(Name, FileBuf, FileLen);
    }
    return Ret;
}

int WTVsg::SaveSigFileMasterToSlave(Connector *Conn, const std::string &Name, void *Data, int Len)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        if (m_Compensate == nullptr)
        {
            m_Compensate.reset(new Compensate());
            if (m_Compensate == nullptr)
            {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
                return WT_ALLOC_FAILED;
            }
        }

        DealFilePath(Name); //文件名如果带路径的，先检查目录是否存在，不存在则创建目录~
        //将文件归一化后再保存，这样meter VSG视图显示比较好
        Ret = m_Compensate->NormalizationFile(Data, Len, GetLowWaveDir() + Name);

        if (Ret != WT_OK)
        {
            return Ret;
        }
    }
    else
    {
        Ret = Conn->GetCmdResult();
        if (Ret == WT_OK)  //下发信号文件给从机返回ok，则再次下发配置外部命令pn
        {
            Ret = SetPnToSlave(Conn, m_CurRequest.get());
            if(Ret != WT_OK)
            {
                Ret = m_ExtConn->Response(Ret);
            }
        }
        else
        {
            Ret = m_ExtConn->Response(Ret);
        }
    }

    return Ret;
}

int WTVsg::SetPnToSlave(Connector *Conn, char *CurRequest)
{
    int Ret = WT_OK;

    Ret = Conn->SetPnMasterToSlave(CurRequest);

    return Ret;

}

//new generate function
#define ARRAYSIZE(A) (sizeof(A)/sizeof((A)[0]))

int WTVsg::GetSignalType(const int standard, const int bw, const int mcs, const int streams)
{
    switch (standard)
    {
    case  IEEE802_11_n:
        if (bw == 20)
        {
            return MCS0 - mcs;
        }
        else if (bw == 40)
        {
            return MCS0_40 - mcs;
        }
        break;
    case IEEE802_11_ac:
        if (bw == 20)
        {
            if (3 == streams && mcs < 12 && mcs >= 0)
            {
                return NSS3_MCS0 - mcs;
            }
            else if (3 != streams && mcs < 12 && mcs >= 0)
            {
                switch (streams)
                {
                case 1:
                    return NSS1_MCS0 - mcs;
                case 2:
                    return NSS2_MCS0 - mcs;
                case 4:
                    return NSS4_MCS0 - mcs;
                case 5:
                    return NSS5_MCS0 - mcs;
                case 6:
                    return NSS6_MCS0 - mcs;
                case 7:
                    return NSS7_MCS0 - mcs;
                case 8:
                    return NSS8_MCS0 - mcs;
                default:
                    break;
                }
            }
        }
        else if (bw == 40 && mcs < 12 && mcs >= 0)
        {
            switch (streams)
            {
            case 1:
                return NSS1_MCS0_40 - mcs;
            case 2:
                return NSS2_MCS0_40 - mcs;
            case 3:
                return NSS3_MCS0_40 - mcs;
            case 4:
                return NSS4_MCS0_40 - mcs;
            case 5:
                return NSS5_MCS0_40 - mcs;
            case 6:
                return NSS6_MCS0_40 - mcs;
            case 7:
                return NSS7_MCS0_40 - mcs;
            case 8:
                return NSS8_MCS0_40 - mcs;
            default:
                break;
            }

        }
        else if (bw == 80 && mcs < 12 && mcs >= 0)
        {
            switch (streams)
            {
            case 1:
                return NSS1_MCS0_80 - mcs;
            case 2:
                return NSS2_MCS0_80 - mcs;
            case 3:
                return NSS3_MCS0_80 - mcs;
            case 4:
                return NSS4_MCS0_80 - mcs;
            case 5:
                return NSS5_MCS0_80 - mcs;
            case 6:
                return NSS6_MCS0_80 - mcs;
            case 7:
                return NSS7_MCS0_80 - mcs;
            case 8:
                return NSS8_MCS0_80 - mcs;
            default:
                break;
            }
        }
        else if (bw == 160 && mcs < 12 && mcs >= 0)
        {
            switch (streams)
            {
            case 1:
                return NSS1_MCS0_160 - mcs;
            case 2:
                return NSS2_MCS0_160 - mcs;
            case 3:
                return NSS3_MCS0_160 - mcs;
            case 4:
                return NSS4_MCS0_160 - mcs;
            case 5:
                return NSS5_MCS0_160 - mcs;
            case 6:
                return NSS6_MCS0_160 - mcs;
            case 7:
                return NSS7_MCS0_160 - mcs;
            case 8:
                return NSS8_MCS0_160 - mcs;
            default:
                break;
            }
        }
        break;
    default:
        break;
    }
    return -1;
}

int WTVsg::GetStandardType(const int standard)
{
    switch (standard)
    {
    case WT_DEMOD_11B:
        return IEEE802_11_b_g_DSSS;
    case WT_DEMOD_11AG:
        return IEEE802_11_a_g_OFDM;
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        return IEEE802_11_n;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        return IEEE802_11_ac;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        return IEEE802_11_ax;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_160_160M:
    case WT_DEMOD_11BE_320M:
        return IEEE802_11_be;
    case WT_DEMOD_BT:
        return Bluetooth;
    case WT_DEMOD_CW:
        return ContinuousWaves;
    case WT_DEMOD_ZIGBEE:
        return IEEE_Zigbee;
    case WT_DEMOD_11BA_20M:
    case WT_DEMOD_11BA_40M:
    case WT_DEMOD_11BA_80M:
        return IEEE802_11_ba;
    case WT_DEMOD_11AZ_20M:
    case WT_DEMOD_11AZ_40M:
    case WT_DEMOD_11AZ_80M:
    case WT_DEMOD_11AZ_160M:
        return IEEE802_11_az;
    case WT_DEMOD_11AH_1M:
    case WT_DEMOD_11AH_2M:
    case WT_DEMOD_11AH_4M:
    case WT_DEMOD_11AH_8M:
    case WT_DEMOD_11AH_16M:
        return IEEE802_11_ah;
    case WT_DEMOD_GLE:
        return SparkLinkGle;
    case WT_DEMOD_LRWPAN_FSK:
    case WT_DEMOD_LRWPAN_OQPSK:
    case WT_DEMOD_LRWPAN_OFDM:
        return IEEE802_15_4_WiSun;
    default:
        break;
    }
    return IEEE802_11_a_g_OFDM;
}

int WTVsg::GetGenerateFinalParam(int Demod, void **Param, int &ParamLen)
{
    int Ret = WT_OK;
    switch (Demod)
    {
    case WT_DEMOD_11AZ_20M:
    case WT_DEMOD_11AZ_40M:
    case WT_DEMOD_11AZ_80M:
    case WT_DEMOD_11AZ_160M:
    {
        *Param = &m_FinalParam;
        ParamLen = sizeof(AxTbVariableParameter);
        break;
    }

    case ALG_3GPP_STD_WCDMA:
    case ALG_3GPP_STD_5G:
    case ALG_3GPP_STD_4G:
    case ALG_3GPP_STD_NB_IOT:
    {
        g_Alg3GPPSwitchCaseFlg = true;
        *Param = &m_FinalGenWaveAlzParam;
        ParamLen = sizeof(m_FinalGenWaveAlzParam);
        break;
    }

    default:
        return WT_ARG_ERROR;
    }

    return Ret;
}

int WTVsg::GetGenerateFinalGenWaveWifiParam(void **Param, int &ParamLen)
{
    int Ret = WT_OK;
    *Param = &m_FinalGenWaveWifiParam;
    ParamLen = sizeof(GenWaveWifiStruct);
    return Ret;
}

int WTVsg::GetGenerateReturnParam(int DataType, void **Param, int &ParamLen)
{
    int Ret = WT_OK;
    if(VSG_OUT_EXT_DATA_RU_CARRIER_INFO == DataType)    //目前这个不再分开获取
    {
        *Param = m_CarrierInfo;
        ParamLen = sizeof(m_CarrierInfo);
    }
    else                        //目前是直接返回算法给的全部返回内容，包括ru carrier;信道模式生成返回的PathLoss
    {
        *Param = m_AlgGenOutPutData.get();
        ParamLen = TotalOutPutData;
    }
    return Ret;
}

//mu-mimo时使用
void WTVsg::GetAxTbVariableParameter(GenWaveWifiStruct *param)
{
    if (IEEE802_11_ax == param->commonParam.standard && HE_TB_PPDU == param->commonParam.subType)
    {
        m_FinalParam.LDPCSym = param->PN11ax_TB.LDPC_Extra;
        m_FinalParam.PEDisamb = param->PN11ax_TB.PE_Disambiguity;
        m_FinalParam.AFactor = param->PN11ax_TB.Pre_FECfactor;
        m_FinalParam.Doppler = param->PN11ax_TB.Doppler;
        m_FinalParam.Midamble_Periodicity = param->PN11ax_TB.Midamble_Periodicity;
        //WTLog::Instance().WriteLog(LOG_DEBUG, "GetAxTbVariableParameter 11ax Varible value=%d,%d,%d,%d,%d\n", m_FinalParam.LDPCSym, m_FinalParam.PEDisamb, m_FinalParam.AFactor, m_FinalParam.Doppler, m_FinalParam.Midamble_Periodicity);
    }
    else if (IEEE802_11_be == param->commonParam.standard && EHT_TB_PPDU == param->commonParam.subType)
    {
        m_FinalParam.LDPCSym = param->PN11be_TB.LDPC_Extra;
        m_FinalParam.PEDisamb = param->PN11be_TB.PE_Disambiguity;
        m_FinalParam.AFactor = param->PN11be_TB.Pre_FECfactor;
        m_FinalParam.Doppler = param->PN11be_TB.Doppler;
        m_FinalParam.Midamble_Periodicity = param->PN11be_TB.Midamble_Periodicity;
        //WTLog::Instance().WriteLog(LOG_DEBUG, "GetAxTbVariableParameter 11be Varible value=%d,%d,%d,%d,%d\n", m_FinalParam.LDPCSym, m_FinalParam.PEDisamb, m_FinalParam.AFactor, m_FinalParam.Doppler, m_FinalParam.Midamble_Periodicity);
    }
    else
    {
        m_FinalParam.LDPCSym = UNVALID_INT_VAL;
        m_FinalParam.PEDisamb = UNVALID_INT_VAL;
        m_FinalParam.AFactor = UNVALID_INT_VAL;
        m_FinalParam.Doppler = UNVALID_INT_VAL;
        m_FinalParam.Midamble_Periodicity = UNVALID_INT_VAL;
    }
}

bool WTVsg::IsTriggerFrame(GenWaveWifiStruct *Param)
{
    bool IsOK = false;
    int Demode = Param->commonParam.standard;

    switch(Demode)
    {
        case WT_DEMOD_11AG:
        {
            if(Param->PN11a.psdu.psduType == PSDUType_TriggerFrame)
            {
                IsOK = true;
            }
            break;
        }
        case WT_DEMOD_11N_20M:
        case WT_DEMOD_11N_40M:
        {
            if(Param->PN11n.psdu.psduType == PSDUType_TriggerFrame)
            {
                IsOK = true;
            }
            break;
        }
        case WT_DEMOD_11AC_20M:
        case WT_DEMOD_11AC_40M:
        case WT_DEMOD_11AC_80M:
        case WT_DEMOD_11AC_160M:
        case WT_DEMOD_11AC_80_80M:
        {
            if(0 == Param->commonParam.subType && Param->PN11ac.psdu.psduType == PSDUType_TriggerFrame)
            {
                IsOK = true;
            }
            break;
        }
        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
        case WT_DEMOD_11AX_160M:
        case WT_DEMOD_11AX_80_80M:
        {
            if(HE_SU_PPDU == Param->commonParam.subType && Param->PN11ax_SU.psdu.psduType == PSDUType_TriggerFrame)
            {
                IsOK = true;
            }
            break;
        }
        default:
            break;
    }

    return IsOK;
}

int WTVsg::CheckGenLicense(GenWaveWifiStruct *Param)
{
    int Ret = WT_OK;
    do
    {
        //check license
        int Demode = Param->commonParam.standard;
        if (!m_Alg.CheckDemodLic(Demode))   //基础Demo lic 判断
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(Ret, "demode license not exist");
            break;
        }

        //mimo license
        int StreamCnt = Param->commonParam.NSS;     //mimo lic 判断
        if(StreamCnt > 1)
        {
            if(License::Instance().CheckBusinessLicItem(WT_WIFI_MIMO) != WT_OK)
            {
                Ret = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(Ret, "WT_WIFI_MIMO license not exist");
                break;
            }
            if(StreamCnt > 8)
            {
                if(License::Instance().CheckBusinessLicItem(WT_WIFI_MAS_MIMO) != WT_OK)
                {
                    Ret = WT_LIC_NOT_EXIST;
                    WTLog::Instance().LOGERR(Ret, "WT_WIFI_MAS_MIMO license not exist");
                    break;
                }
            }
        }

        //ax 交互lic，mumimolic的判断
        if(WT_DEMOD_11AX_20M <= Demode && Demode <= WT_DEMOD_11AX_160_160M)
        {
            int SubType = Param->commonParam.subType;

            if(HE_TB_PPDU == SubType)
            {
                bool IsTbMuMimo = false;
                for(int i = 0; i < MAX_SEGMENT; i++)
                {
                    for(int j = 0; j < AX_RU_COUNT; j++)
                    {
                        if(Param->PN11ax_TB.RU[i][j].UserNum > 1)
                        {
                            IsTbMuMimo = true;
                            break;
                        }
                    }
                }

                //mu mimo
                if(IsTbMuMimo)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MUMIMO license not exist");
                        break;
                    }
                    if(StreamCnt > 8)
                    {
                        if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                        {
                            Ret = WT_LIC_NOT_EXIST;
                            WTLog::Instance().LOGERR(Ret, "WT_MAS_MUMIMO license not exist");
                            break;
                        }
                    }
                }
            }

            if(HE_MU_PPDU == SubType)
            {
                bool IsMuMimo = false;
                for(int i = 0; i < AX_RU_COUNT; i++)
                {
                    if(Param->PN11ax_MU.RU[i].UserNum > 1)
                    {
                        IsMuMimo = true;
                        break;
                    }
                }

                //mu mimo
                if(IsMuMimo)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MUMIMO license not exist");
                        break;
                    }

                    if(StreamCnt > 8)
                    {
                        if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                        {
                            Ret = WT_LIC_NOT_EXIST;
                            WTLog::Instance().LOGERR(Ret, "WT_MAS_MUMIMO license not exist");
                            break;
                        }
                    }
                }
            }
        }
        //be 交互lic，mumimolic的判断
        else if(WT_DEMOD_11BE_20M <= Demode && Demode <= WT_DEMOD_11BE_160_160M)
        {
            int SubType = Param->commonParam.subType;

            if(EHT_TB_PPDU == SubType)
            {
                bool IsTbMuMimo = false;
                for(int i = 0; i < MAX_SEGMENT; i++)
                {
                    for(int j = 0; j < AX_RU_COUNT; j++)
                    {
                        if(Param->PN11be_TB.RU[i][j].UserNum > 1)
                        {
                            IsTbMuMimo = true;
                            break;
                        }
                    }
                }

                //mu mimo
                if(IsTbMuMimo)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MUMIMO license not exist");
                        break;
                    }
                    if(StreamCnt > 8)
                    {
                        if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                        {
                            Ret = WT_LIC_NOT_EXIST;
                            WTLog::Instance().LOGERR(Ret, "WT_MAS_MUMIMO license not exist");
                            break;
                        }
                    }
                }
            }

            if(EHT_MU_PPDU == SubType)
            {
                bool IsMuMimo = false;
                for(int i = 0; i < BE_RU_COUNT; i++)
                {
                    if(Param->PN11be_MU.RU[i].UserNum > 1)
                    {
                        IsMuMimo = true;
                        break;
                    }
                }

                //mu mimo
                if(IsMuMimo)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MUMIMO license not exist");
                        break;
                    }

                    if(StreamCnt > 8)
                    {
                        if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                        {
                            Ret = WT_LIC_NOT_EXIST;
                            WTLog::Instance().LOGERR(Ret, "WT_MAS_MUMIMO license not exist");
                            break;
                        }
                    }
                }
            }
        }
        //ac mumimo lic的判断
        else if(WT_DEMOD_11AC_20M <= Demode && Demode <= WT_DEMOD_11AC_80_80M)
        {
            //mu license
            if(1 == Param->commonParam.subType) //ac mu mimo
            {
                if(License::Instance().CheckBusinessLicItem(WT_MUMIMO) != WT_OK)
                {
                    Ret = WT_LIC_NOT_EXIST;
                   WTLog::Instance().LOGERR(Ret, "WT_MUMIMO license not exist");
                   break;
                }
                if(StreamCnt > 8)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MAS_MUMIMO) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MAS_MUMIMO license not exist");
                        break;
                    }
                }
            }
        }
    }while(0);

    return Ret;
}

int WTVsg::CheckNotSupportPart(GenWaveWifiStruct *Param)
{
    //目前不支持8080的tf和8080的tb
    int Ret = WT_OK;

    do
    {
        int Demode = Param->commonParam.standard;

        if(Demode == WT_DEMOD_11AC_80_80M || Demode == WT_DEMOD_11AX_80_80M)
        {
            if(IsTriggerFrame(Param))
            {
                Ret = WT_ARG_UNKNOW_PARAMETER;
                WTLog::Instance().LOGERR(Ret, "Not support 8080 tf.");
                break;
            }
        }

        if(Demode == WT_DEMOD_11AX_80_80M && Param->commonParam.subType == HE_TB_PPDU)
        {
            Ret = WT_ARG_UNKNOW_PARAMETER;
            WTLog::Instance().LOGERR(Ret, "Not support 8080 tb.");
            break;
        }
    }while(0);

    return Ret;
}

void WTVsg::SplitAlgExtOutData(const void *pData)
{
    //wave生成后，算法返回的附加信息格式为：VsgOutExtDataHdr+VsgOutExtDataStru;
    //即int TotalLen + int Reserved[31]+int Type1+ int DataLen1+ Data1 +int Type2 +int DataLen2 + Data2+...
    memset(m_CarrierInfo, 0, sizeof(m_CarrierInfo));
    int TolLen = *((int *)pData);

    //保留一份总体的返回数据，便于api直接获取所有返回
    if(m_AlgGenOutPutData == nullptr || TotalOutPutData < TolLen)
    {
        m_AlgGenOutPutData.reset(new(std::nothrow)char[TolLen+10]);
    }
    TotalOutPutData = TolLen + sizeof(int);//算法返回数据中的TolLen，不包含TotalLen本身，所以对外获取时要加上一个int
    memcpy((char *)m_AlgGenOutPutData.get(), (char *)pData, TotalOutPutData);

    //再细分详细内容
    int Len = 0;
    if(TolLen != 0)
    {
        Len = sizeof(VsgOutExtDataHdr);
        while(Len < TolLen)
        {
            int DataType = *((int *)((char *)pData + Len));
            if(VSG_OUT_EXT_DATA_RU_CARRIER_INFO == DataType)
            {
                memset(m_CarrierInfo, 0, sizeof(m_CarrierInfo));

                Len += sizeof(int);
                int DataLen = *((int *)((char *)pData + Len));

                Len += sizeof(int);
                int TmpLen = (sizeof(m_CarrierInfo) < DataLen) ? sizeof(m_CarrierInfo) : DataLen;
                memcpy((char *)m_CarrierInfo, (char *)pData + Len, TmpLen);

                Len += DataLen;
            }
//            else if(VSG_OUT_EXT_DATA_CHANNEL_MODEL_PATHLOSS == DataType)    //信道线衰值，就一个double
//            {
//                Len += sizeof(int); //DataType
//                Len += sizeof(int); //DataLen
//                double PathLoss = *((double *)((char *)pData + Len));
//                Len += sizeof(double);
//                WTLog::Instance().WriteLog(LOG_DEBUG, "Gen Get path loss =%lf\n",PathLoss);
//            }
            else    //不认识类型，直接退出循环，不再处理，否则进死循环了
            {
                break;
            }
        }
    }
}

int WTVsg::GenerateSigSLE(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    int Ret = WT_OK;

    do
    {
        //生成增加license的判断
        if (!m_Alg.CheckDemodLic(WT_DEMOD_GLE))
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "demode license not exist");
            break;
        }

        WTLog::Instance().WriteLog(LOG_DEBUG, "ParamLen = %d, sizeof(GenWaveGleStruct)=%d\n", ParamLen, (int)sizeof(GenWaveGleStruct));
        if (ParamLen != (signed)sizeof(GenWaveGleStruct))
        {
            Ret = WT_CMD_ERROR;
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            break;
        }

        GenWaveGleStruct *Parameters = (GenWaveGleStruct *)(Param);
        //转换下某些参数，主要是api下发和要给算法之间的差异，比如standard
        Parameters->commonParam.standard = GetStandardType(Parameters->commonParam.standard);
        
        stTxGLE_Out gleout;
        memset(gleout.outData, 0, sizeof(gleout.outData));
        memset(gleout.outLen, 0, sizeof(gleout.outLen));
        memset(gleout.synSeq, 0, sizeof(gleout.synSeq));
        gleout.synSeqLen = 0;
        Ret = Alg_GenerateWaveGle(Parameters, &gleout);

        if (Ret != WT_OK)
        {
            Ret += WT_VSG_ALG_BASE_ERROR;
            WTLog::Instance().LOGERR(Ret, "generate vsg gle signal failed");
            break;
        }
        else
        {
            m_synSeqLen = gleout.synSeqLen * sizeof(int);
            memcpy(m_synSeq, gleout.synSeq, m_synSeqLen);

            for (int i = 0; i < MAX_NUM_OF_CHNNEL && i < MAX_STREAM_COUNT; i++)
            {
                FileData[i] = gleout.outData[i];
                FileLen[i] = gleout.outLen[i] * sizeof(Complex);
            }
        }

    } while (0);
    return Ret;
}

int WTVsg::GenerateSigWiSun(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    int Ret = WT_OK;
    do
    {
        //生成增加license的判断
        if (!m_Alg.CheckDemodLic(WT_DEMOD_LRWPAN_OFDM) || !m_Alg.CheckDemodLic(WT_DEMOD_LRWPAN_OQPSK) || !m_Alg.CheckDemodLic(WT_DEMOD_LRWPAN_FSK))
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "demode license not exist");
            break;
        }

        printf("ParamLen = %d, sizeof(GenWaveWisunStruct)=%d\n", ParamLen, (int)sizeof(GenWaveWisunStruct));
        if (ParamLen != (signed)sizeof(GenWaveWisunStruct))
        {
            Ret = WT_CMD_ERROR;
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            break;
        }

        GenWaveWisunStruct *Parameters = (GenWaveWisunStruct *)(Param);
        // 转换下某些参数，主要是api下发和要给算法之间的差异，比如standard
        printf("Parameters->commonParam.standard = %d,subtype=%d,segment=%d\n", Parameters->commonParam.standard, Parameters->commonParam.subType, Parameters->commonParam.segment);
        Parameters->commonParam.standard = GetStandardType(Parameters->commonParam.standard);

        stTxWisun_Out WiSunout;
        Ret = Alg_GenerateWisun(Parameters, &WiSunout);
        printf("[Alg_GenerateWisun]Ret = %d\n\n\n", Ret);

        if (Ret != WT_OK)
        {
            Ret += WT_VSG_ALG_BASE_ERROR;
            WTLog::Instance().LOGERR(Ret, "generate vsg signal failed");
            break;
        }
        else
        {
            for (int i = 0; i < MAX_STREAM_COUNT; i++)
            {
                FileData[i] = WiSunout.outData[i];
                FileLen[i] = WiSunout.outLen[i] * sizeof(Complex);
            }
        }

    } while (0);
    

    return Ret;
}

int WTVsg::GenerateSigWifiV4(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    int Ret = WT_OK;
    do
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "ParamLen=%d, sizeof(GenWaveWifiStruct)=%d\n",ParamLen,(int)sizeof(GenWaveWifiStruct));
        if(ParamLen < sizeof(GenWaveWifiStruct))    //数据最小为sizeof(GenWaveWifiStruct)，带扩展参数时，大于该数值
        {
            Ret = WT_CMD_ERROR;
            WTLog::Instance().LOGERR(Ret, "cmd length error");
            break;
        }

        GenWaveWifiStruct *Parameters = (GenWaveWifiStruct *)(Param);

        //处理解析扩展参数ExtendParam的内容
        if(ParamLen > sizeof(GenWaveWifiStruct))
        {
            bool HaveUserdefinePsduData = false;

            Parameters->ExtendParam = (char *)Param + sizeof(GenWaveWifiStruct);
            ExtParamHdr *ext_hdr = (ExtParamHdr *)Parameters->ExtendParam;
            char *pData = (char *)ext_hdr;
            int offset = 0;
            int index = 0;
            int remain_len = ParamLen - sizeof(GenWaveWifiStruct);
            while (true)
            {
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "****************"
                          << "Index = " << index++ << "****************" << std::endl;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Total len = " << sizeof(ExtParamHdr) + ext_hdr->Len << std::endl;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Type = " << ext_hdr->Type << std::endl;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Len = " << ext_hdr->Len << std::endl;

                if(WT_EXT_PARAM_PSDU == ext_hdr->Type)
                {
                    HaveUserdefinePsduData = true;
                }

                offset += (sizeof(ExtParamHdr) + ext_hdr->Len);
                if (offset >= remain_len)
                {
                    ext_hdr->Field.Next = nullptr;
                    break;
                }
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "offset = " << offset << std::endl;
                ext_hdr->Field.Next = (ExtParamHdr *)(pData + offset);
                ext_hdr = (ExtParamHdr *)ext_hdr->Field.Next;
            }

            //判断扩展参数中是否包含自定义psdu的内容，如果有证明是psdu userdefine模式的，需要判断是否有mac 相关lic
            if(HaveUserdefinePsduData)
            {
                int Standard = GetStandardType(Parameters->commonParam.standard);
                if(Standard == IEEE802_11_be)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MAC_BE) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MAC_BE license not exist");
                        break;
                    }
                }
                else if(Standard == IEEE802_11_ax)
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MAC_AX) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MAC_AX license not exist");
                        break;
                    }
                }
                else
                {
                    if(License::Instance().CheckBusinessLicItem(WT_MAC_AC) != WT_OK)
                    {
                        Ret = WT_LIC_NOT_EXIST;
                        WTLog::Instance().LOGERR(Ret, "WT_MAC_AC license not exist");
                        break;
                    }
                }
            }
        }

        //检查不支持的功能配置，
        Ret = CheckNotSupportPart(Parameters);
        if(Ret != WT_OK)
        {
            break;
        }

        //转换下某些参数，主要是api下发和要给算法之间的差异，比如standard
        int Demod = Parameters->commonParam.standard;
        WTLog::Instance().WriteLog(LOG_DEBUG, "Parameters->commonParam.standard = %d,subtype=%d,segment = %d\n",Parameters->commonParam.standard,Parameters->commonParam.subType,Parameters->commonParam.segment);
        Parameters->commonParam.standard = GetStandardType(Parameters->commonParam.standard);

        //生成
        void *pOutExtData = nullptr;
        Ret = Alg_GenerateWaveWifi(Parameters, (Complex **)FileData, FileLen, &pOutExtData);
        //WTLog::Instance().WriteLog(LOG_DEBUG, "final streamcnt = %d\n",Parameters->commonParam.NSS);

        SplitAlgExtOutData(pOutExtData);    //拆分信号生成后算法返回的附加信息内容


        if(Ret != WT_OK)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Alg_GenerateWaveWifi fail, error code=%d\r\n", Ret);
            Ret += WT_VSG_ALG_BASE_ERROR;
            WTLog::Instance().LOGERR(Ret, "generate vsg signal failed");
            break;
        }

        //注意获取返回参数使用stander是算法的ieee标准，不是固件使用的demo类型
        GetAxTbVariableParameter(Parameters);   //获取生成成功后才能确定的参数
        memcpy(&m_FinalGenWaveWifiParam, Parameters, sizeof(GenWaveWifiStruct));//保存生成信号后返回的生成配置，主要用于tftb时获取使用

        //判断Demo license,生成后再判断lic，方便流数相关的lic判断，比如8x8，生成完Parameters->commonParam.NSS会返回信号的总流数，stbc或者mumimo时总流数计算复杂
        Parameters->commonParam.standard = Demod;
        Ret = CheckGenLicense(Parameters);
        if(Ret != WT_OK)
        {
            break;
        }

        for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            *(FileLen+i) = (*(FileLen+i)) * sizeof(Complex);
            WTLog::Instance().WriteLog(LOG_DEBUG, "GenerateSig len%d = %d\n",i,*(FileLen+i));
        }
    }while(0);

    return Ret;
}

int WTVsg::GenerateSigCWV4(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    int Ret = WT_OK;
    do
    {
        //生成增加license的判断
        if (!m_Alg.CheckDemodLic(WT_DEMOD_CW))
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "demode license not exist");
            break;
        }

        WTLog::Instance().WriteLog(LOG_DEBUG, "ParamLen = %d, sizeof(GenWaveCwStruct)=%d\n",ParamLen, (int)sizeof(GenWaveCwStruct));
        if (ParamLen != (signed)sizeof(GenWaveCwStruct))
        {
            Ret = WT_CMD_ERROR;
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            break;
        }

        GenWaveCwStruct *Parameters = (GenWaveCwStruct *)(Param);
        //转换下某些参数，主要是api下发和要给算法之间的差异，比如standard
        WTLog::Instance().WriteLog(LOG_DEBUG, "Parameters->commonParam.standard = %d,subtype=%d,segment=%d\n",Parameters->commonParam.standard,Parameters->commonParam.subType,Parameters->commonParam.segment);
        Parameters->commonParam.standard = GetStandardType(Parameters->commonParam.standard);

        int Ret = Alg_GenerateWaveCw(Parameters, (Complex **)FileData, FileLen);

        if(Ret != WT_OK)
        {
            Ret += WT_VSG_ALG_BASE_ERROR;
            WTLog::Instance().LOGERR(Ret, "generate vsg signal failed");
            break;
        }

        for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            *(FileLen+i) = (*(FileLen+i)) * sizeof(Complex);
            //WTLog::Instance().WriteLog(LOG_DEBUG, "GenerateSig len%d = %d\n",i,*(FileLen+i));
        }
    }while(0);

    return Ret;
}

int WTVsg::GenerateSigBlueToothV4(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    int Ret = WT_OK;
    do
    {
        //生成增加license的判断
        if (!m_Alg.CheckDemodLic(WT_DEMOD_BT))
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "demode license not exist");
            break;
        }

        WTLog::Instance().WriteLog(LOG_DEBUG, "ParamLen = %d, sizeof(GenWaveBtStruct)=%d\n",ParamLen, (int)sizeof(GenWaveBtStruct));
        if (ParamLen < sizeof(GenWaveBtStruct))
        {
            Ret = WT_CMD_ERROR;
            WTLog::Instance().LOGERR(WT_CMD_ERROR, "cmd length error");
            break;
        }

        GenWaveBtStruct *Parameters = (GenWaveBtStruct *)(Param);
        //BLE时需要判断bt5.1的lic
        if(Parameters->commonParam.subType == 1 && Parameters->BleSet.FormatType != 2 && Parameters->BleSet.TestPacket.Uncoded.CTEInfo == 1)  //BLE
        {
            if(License::Instance().CheckBusinessLicItem(WT_BT5_1) != WT_OK)
            {
                Ret = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(Ret, "BT5.1 license not exist");
                break;
            }
        }

        if (Parameters->commonParam.subType == ALG_BT_BLE && Parameters->BleSet.TestPacket.BleEnhancedMode == 1)
        {
            if (License::Instance().CheckBusinessLicItem(WT_BT_HDR) != WT_OK)
            {
                Ret = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(Ret, "BT HDR license not exist");
                break;
            }
        }

        //处理解析扩展参数ExtendParam的内容
        if(ParamLen > sizeof(GenWaveBtStruct))
        {
            // bool HaveUserdefinePsduData = false;

            Parameters->ExtendParam = (char *)Param + sizeof(GenWaveBtStruct);
            ExtParamHdr *ext_hdr = (ExtParamHdr *)Parameters->ExtendParam;
            char *pData = (char *)ext_hdr;
            int offset = 0;
            int index = 0;
            int remain_len = ParamLen - sizeof(GenWaveBtStruct);
            while (true)
            {
                std::cout << "****************"
                          << "Index = " << index++ << "****************" << std::endl;
                std::cout << "Total len = " << sizeof(ExtParamHdr) + ext_hdr->Len << std::endl;
                std::cout << "Type = " << ext_hdr->Type << std::endl;
                std::cout << "Len = " << ext_hdr->Len << std::endl;

                // char *tmpbuf = pData + sizeof(ExtParamHdr) + sizeof(int);
                // printf("fw data = \n");
                // for(int i = 0; i < ext_hdr->Len - sizeof(int); i++)
                // {
                //     printf("%.2x,", tmpbuf[i]);
                // }
                // printf("\n");
                offset += (sizeof(ExtParamHdr) + ext_hdr->Len);
                if (offset >= remain_len)
                {
                    ext_hdr->Field.Next = nullptr;
                    break;
                }

                ext_hdr->Field.Next = (ExtParamHdr *)(pData + offset);
                ext_hdr = (ExtParamHdr *)ext_hdr->Field.Next;
            }
        }

        //转换下某些参数，主要是api下发和要给算法之间的差异，比如standard
        Parameters->commonParam.standard = GetStandardType(Parameters->commonParam.standard);
        Ret = Alg_GenerateWaveBt(Parameters, (Complex **)FileData, FileLen);


        if(Ret != WT_OK)
        {
            Ret += WT_VSG_ALG_BASE_ERROR;
            WTLog::Instance().LOGERR(Ret, "generate vsg signal failed");
            break;
        }

        for(int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            *(FileLen+i) = (*(FileLen+i)) * sizeof(Complex);
            //WTLog::Instance().WriteLog(LOG_DEBUG, "GenerateSig len%d = %d\n",i,*(FileLen+i));
        }
    }while(0);

    return Ret;
}

int WTVsg::GenerateSig3GPPV4(const void *Param, int ParamLen, void **FileData, int *FileLen)
{
    int Ret = WT_OK;
    do
    {
        int Demo = *(int *)Param;
        WTLog::Instance().WriteLog(LOG_DEBUG, "Demo =%d\n", Demo);
        if (!IsAlg3GPPStandardType(Demo)) // Param第一个int值为标准类型
        {
            Ret = WT_ARG_ERROR;
            break;
        }
        //打印反馈
        WTLog::Instance().WriteLog(LOG_DEBUG, "ParamLen = %d, sizeof(Alg_3GPP_WaveGenType)=%d, sizeof(PNSetBaseType) =%d, sizeof(Alg_WCDMA_WaveGenType)= %d,sizeof(Alg_5G_WaveGenType)= %d, sizeof(Alg_4G_WaveGenType)= %d, sizeof(Alg_NBIOT_WaveGenType)= %d\n", ParamLen, (int)sizeof(Alg_3GPP_WaveGenType),(int)sizeof(PNSetBaseType),
            (int)sizeof(Alg_WCDMA_WaveGenType),(int)sizeof(Alg_5G_WaveGenType),(int)sizeof(Alg_4G_WaveGenType), (int)sizeof(Alg_NBIOT_WaveGenType));
        //参数检查
        if (ParamLen < sizeof(Alg_3GPP_WaveGenType)) //数据最小为sizeof(Alg_3GPP_WaveGenType)，带userdefinepsdu时，大于该数值
        {
            Ret = WT_CMD_ERROR;
            WTLog::Instance().LOGERR(Ret, "cmd length error");
            break;
        }

        if (Demo == ALG_3GPP_STD_NB_IOT || Demo == ALG_3GPP_STD_4G)
        {
            if(License::Instance().CheckBusinessLicItem(WT_LTE_IoT) != WT_OK)
            {
                Ret = WT_LIC_NOT_EXIST;
                WTLog::Instance().LOGERR(Ret, "Lte Iot license not exist when generate sig 3gpp");
                return Ret;
            }
        }

        //生成前处理
        void *pOutExtData = nullptr;
        Alg_3GPP_WaveGenType *Parameters = (Alg_3GPP_WaveGenType *)(Param);

        // // 参数打印
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.standard = " << __LINE__ << ":" << pnParameters->CommonParam.standard << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.subType = " << __LINE__ << ":" << pnParameters->CommonParam.subType << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.bandwidth = " << __LINE__ << ":" << pnParameters->CommonParam.bandwidth << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.samplingRate = " << __LINE__ << ":" << pnParameters->CommonParam.samplingRate << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.NSS = " << __LINE__ << ":" << pnParameters->CommonParam.NSS << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.segment = " << __LINE__ << ":" << pnParameters->CommonParam.segment << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.FreqErr = " << __LINE__ << ":" << pnParameters->CommonParam.FreqErr << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.IQImbalanceAmp = " << __LINE__ << ":" << pnParameters->CommonParam.IQImbalanceAmp << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.IQImbalancePhase = " << __LINE__ << ":" << pnParameters->CommonParam.IQImbalancePhase << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.DCOffset_I = " << __LINE__ << ":" << pnParameters->CommonParam.DCOffset_I << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.DCOffset_Q = " << __LINE__ << ":" << pnParameters->CommonParam.DCOffset_Q << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.ClockRate = " << __LINE__ << ":" << pnParameters->CommonParam.ClockRate << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.Snr = " << __LINE__ << ":" << pnParameters->CommonParam.Snr << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.Gap = " << __LINE__ << ":" << pnParameters->CommonParam.Gap << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.SpatialExtension = " << __LINE__ << ":" << pnParameters->CommonParam.SpatialExtension << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.ReducePARA = " << __LINE__ << ":" << pnParameters->CommonParam.ReducePARA << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.TailGapValidFlag = " << __LINE__ << ":" << pnParameters->CommonParam.TailGapValidFlag << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->CommonParam.TailGap = " << __LINE__ << ":" << pnParameters->CommonParam.TailGap << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->LinkDirect"<< __LINE__ << ":" << pnParameters->LinkDirect << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.Type = " << __LINE__ << ":" << pnParameters->General.Filter.Type << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.Fs = " << __LINE__ << ":" << pnParameters->General.Filter.Fs << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.MaxOrder = " << __LINE__ << ":" << pnParameters->General.Filter.MaxOrder << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.FpassFactor = " << __LINE__ << ":" << pnParameters->General.Filter.FpassFactor << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.FstopFactor = " << __LINE__ << ":" << pnParameters->General.Filter.FstopFactor << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.PassRipple = " << __LINE__ << ":" << pnParameters->General.Filter.PassRipple << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.StopAtten = " << __LINE__ << ":" << pnParameters->General.Filter.StopAtten << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.Optimization = " << __LINE__ << ":" << pnParameters->General.Filter.Optimization << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.CutOffFrqFactor = " << __LINE__ << ":" << pnParameters->General.Filter.CutOffFrqFactor << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.RollOffFactor = " << __LINE__ << ":" << pnParameters->General.Filter.RollOffFactor << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.Filter.CutOffFreqShift = " << __LINE__ << ":" << pnParameters->General.Filter.CutOffFreqShift << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.GenWaveMode = " << __LINE__ << ":" << pnParameters->General.GenWaveMode << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.GenWaveNo = " << __LINE__ << ":" << pnParameters->General.GenWaveNo << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->General.PowerRefType = " << __LINE__ << ":" << pnParameters->General.PowerRefType << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"pnParameters->UL.MultiCell.CarrAggrState = " << __LINE__ << ":" << pnParameters->UL.MultiCell.CarrAggrState << std::endl;
        // for (int i = 0; i < ALG_4G_MAX_CELL_NUM; ++i)
        // {
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].CellIdx = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].CellIdx << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].State = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].State << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].PhyCellID = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].PhyCellID << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].ChannelBW = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].ChannelBW << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].Duplexing = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].Duplexing << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].ULDLConfig = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].ULDLConfig << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].SpecialSubfrmCfg = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].SpecialSubfrmCfg << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].Power = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].Power << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.Cell["<< i << "].N1Dmrs = " << __LINE__ << ":" << pnParameters->UL.MultiCell.Cell[i].N1Dmrs << std::endl;
        // }

        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.CyclicPrefix = " << __LINE__ << ":" << pnParameters->UL.MultiCell.CyclicPrefix << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.GroupHop = " << __LINE__ << ":" << pnParameters->UL.MultiCell.GroupHop << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.SequenceHop = " << __LINE__ << ":" << pnParameters->UL.MultiCell.SequenceHop << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.MultiCell.DeltaSeqShift = " << __LINE__ << ":" << pnParameters->UL.MultiCell.DeltaSeqShift << std::endl;

        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.UeID = " << __LINE__ << ":" << pnParameters->UL.Ue.UeID << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.UePower = " << __LINE__ << ":" << pnParameters->UL.Ue.UePower << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.UseMode = " << __LINE__ << ":" << pnParameters->UL.Ue.UseMode << std::endl;
        // for (int i = 0; i < ALG_4G_MAX_CELL_NUM; ++i)
        // {
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].DataType = " << __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].DataType << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].Initialization = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].Initialization << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].TxMode = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].TxMode << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].MaxNumAP = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].MaxNumAP << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].Scramble = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].Scramble << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].ChanCodingState = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].ChanCodingState << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].ChanCodingMode = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].ChanCodingMode << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Ue.Pusch["<<i<<"].Enable256QAM = "<< __LINE__ << ":" << pnParameters->UL.Ue.Pusch[i].Enable256QAM << std::endl;
        // }
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.CchSfCfgNum = "<< __LINE__ << ":" << pnParameters->UL.CchSfCfgNum << std::endl;
        // WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.SchSfCfgNum = "<< __LINE__ << ":" << pnParameters->UL.SchSfCfgNum << std::endl;
        // for (int i = 0; i < ALG_4G_MAX_SUBFRAME_NUM; ++i)
        // {
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].SubfrmIdx = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].SubfrmIdx << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].State = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].State << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pucch.State = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pucch.State << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pucch.Formate = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pucch.Formate << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pucch.NPucch = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pucch.NPucch << std::endl;
        //     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pucch.Power = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pucch.Power << std::endl;
        //     for (int j = 0; j < ALG_4G_MAX_CELL_NUM; ++j)
        //     {
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].CellIdx = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].CellIdx << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].State = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].State << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].RBSetNum = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].RBSetNum << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].RBNum[0] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].RBNum[0] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].RBNum[1] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].RBNum[1] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].RBOffset[0] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].RBOffset[0] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].RBOffset[1] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].RBOffset[1] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Power = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Power << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].FreqHopState = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].FreqHopState << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Precoding = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Precoding << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].LayerNum = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].LayerNum << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].AntennaNum = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].AntennaNum << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].CodebookIdx = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].CodebookIdx << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].CyclicShiftField = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].CyclicShiftField << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Codeword = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Codeword << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.McsCfgMode = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.McsCfgMode << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.Mcs[0] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.Mcs[0] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.Mcs[1] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.Mcs[1] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.Modulate[0] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.Modulate[0] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.Modulate[1] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.Modulate[1] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.PayloadSize[0] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.PayloadSize[0] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.PayloadSize[1] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.PayloadSize[1] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.RedunVerIdx[0] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.RedunVerIdx[0] << std::endl;
        //         WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "pnParameters->UL.Chan["<<i<<"].Pusch["<<j<<"].Encode.RedunVerIdx[1] = "<< __LINE__ << ":" << pnParameters->UL.Chan[i].Pusch[j].Encode.RedunVerIdx[1] << std::endl;
        //     }
        // }
        
        WTLog::Instance().WriteLog(LOG_DEBUG, "Ganerating wave file ......\n");
        Ret = Algvsg_3GPP_GenerateWaveMain(Parameters, (Complex **)FileData, FileLen, &pOutExtData);

        if (Ret != WT_OK)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Algvsg_3GPP_GenerateWaveMain fail, error code=%d\r\n", Ret);
            Ret += WT_ALG_3GPP_BASE_ERROR;
            WTLog::Instance().LOGERR(Ret, "generate vsg signal failed");
            break;
        }
        else
        {
            // 保持算法返回的分析参数
            Get3GPPAlzParam(Demo, pOutExtData);
        }

        //生成后处理
        //拆分信号生成后算法返回的附加信息内容
        memcpy(&m_FinalGenWaveLTEParam, Parameters, sizeof(Alg_4G_WaveGenType));//保存生成信号后返回的生成配置，主要用于获得LTE参考分析参数时使用
        for (int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
        {
            *(FileLen + i) = (*(FileLen + i)) * sizeof(Complex);
            WTLog::Instance().WriteLog(LOG_DEBUG, "GenerateSig len%d = %d\n", i, *(FileLen + i));
        }

    } while (0);
    return Ret;
}

void WTVsg::Get3GPPAlzParam(int Demode, void *pOutExtData)
{
    int OutDemode = *(int *)pOutExtData;
    // int OutSize = *((int *)pOutExtData + 1);

    if (Demode == OutDemode)
    {
        bzero(&m_FinalGenWaveAlzParam, sizeof(m_FinalGenWaveAlzParam));

        m_FinalGenWaveAlzParam.Standard = Demode;

        g_Alg3GPPSwitchCaseFlg = true;
        switch (Demode)
        {
        case ALG_3GPP_STD_NB_IOT:
            memcpy(&m_FinalGenWaveAlzParam.NBIOT, (char *)pOutExtData + 8, sizeof(m_FinalGenWaveAlzParam.NBIOT));
            break;
        case ALG_3GPP_STD_4G:
            memcpy(&m_FinalGenWaveAlzParam.LTE, (char *)pOutExtData + 8, sizeof(m_FinalGenWaveAlzParam.LTE));
            break;
        case ALG_3GPP_STD_5G:
            memcpy(&m_FinalGenWaveAlzParam.NR, (char *)pOutExtData + 8, sizeof(m_FinalGenWaveAlzParam.NR));
            break;
        case ALG_3GPP_STD_WCDMA:
            memcpy(&m_FinalGenWaveAlzParam.WCDMA, (char *)pOutExtData + 8, sizeof(m_FinalGenWaveAlzParam.WCDMA));
            break;
        default:
            break;
        }
    }
    else
    {
        WTLog::Instance().LOGERR(WT_ARG_UNKNOW_PARAMETER, "GenerateWave returns an Alz parameter type error");
    }
}

int WTVsg::SetVsgFlatnessCal(int Enable)
{
    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }

    return m_Compensate->SetVsgFlatnessCal(Enable);
}

int WTVsg::GetVsgFlatnessCal()
{

    if (m_Compensate == nullptr)
    {
        WTLog::Instance().LOGERR(WT_OK, "normalization object not exist");
        return WT_OK;
    }

    return m_Compensate->GetVsgFlatnessCal();
}

int WTVsg::SetVsgIQImbCal(int Enable)
{
    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }

    return m_Compensate->SetVsgIQImbCal(Enable);
}

int WTVsg::GetVsgIQImbCal()
{

    if (m_Compensate == nullptr)
    {
        WTLog::Instance().LOGERR(WT_OK, "normalization object not exist");
        return WT_OK;
    }

    return m_Compensate->GetVsgIQImbCal();
}

int WTVsg::GetTBTStaDelay(int FileId, double SamplingFreq)
{
    if (DigModeLib::Instance().IsDigMode())
    {
        return m_ExtPnItem[FileId].StartDelay;
    }

#if 1 //每次读取
    int HwDelay = 0;
    WTConf Conf(WTConf::GetDir() + "/triggerbase.conf");
    GET_CONF_DATA(Conf, "STAHwCompensate", HwDelay, 0);
#else //启动固定读取一次
    int HwDelay = m_TBTConf.STAHwCompensate;
#endif

    if (m_Param != nullptr)
    {
        if (Basefun::CompareDoubleAccuracy1K(m_Param[0].Freq, RF_5G_MIN) >= 0)
        {
            HwDelay += m_TBTConf.STA5GCompensate;
        }
    }
    //原始单位为ns
    HwDelay = (double)HwDelay * SamplingFreq * 1e-9;

#if 1
    unsigned int NeedDelay = m_ExtPnItem[FileId].StartDelay;
#else
    unsigned int NeedDelay = (double)16 * (1e-6) * SamplingFreq;
#endif

    int Delay = NeedDelay + HwDelay;
    WTLog::Instance().WriteLog(LOG_DEBUG, "=========GetTBTStaDelay NeedDelay=%u, HwDelay=%d, Delay=%d\n", NeedDelay, HwDelay, Delay);
    return Delay > 0 ? Delay : 0;
}

int WTVsg::SetVsgRuCarrierInfo(Connector *Conn, void * Data)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "set the ru carrier info failed");
            }
        }

        if (m_Compensate == nullptr)
        {
            m_Compensate.reset(new Compensate());
            if (m_Compensate == nullptr)
            {
                WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
                return WT_ALLOC_FAILED;
            }
        }

        Ret = m_Compensate->SetVsgRuCarrierInfo(Data);
    }
    else
    {
        Ret = ProcMimoAck(Conn);
    }
    return Ret;
}

int WTVsg::SetVsgFemParam(Connector * Conn, FemParam FemParamTemp)
{
    int Ret = WT_OK;

    if (!Conn->IsLinkToSlave())
    {
        //license检查是否支持指定的测试类型
        if (FemParamTemp.FemMode && !CheckTestTypeLic(TEST_DEVM))
        {
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "TEST_DEVM license not exist");
            return WT_LIC_NOT_EXIST;
        }

        if (FemParamTemp.FemMode != 0 &&
            (FemParamTemp.LeadTime < 0 || FemParamTemp.LeadTime * DEFAULT_SMAPLE_RATE >= pow(2, 32) ||
                FemParamTemp.DelayTime < 0 || FemParamTemp.DelayTime * DEFAULT_SMAPLE_RATE >= pow(2, 32)))
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "fem param out range");
            return WT_ARG_ERROR;
        }

        //百分比，取值0-100
        FemParamTemp.DutyRatio = (FemParamTemp.DutyRatio > 100)
                                    ? 100
                                    : ((FemParamTemp.DutyRatio < 0) ? 0 : FemParamTemp.DutyRatio);

        //将命令转发给从机
        if (IsMIMOMaster())
        {
            Ret = TransmitMimoCmd();
            if (Ret == WT_OK)
            {
                Conn->SetRsp(false);
            }
            else
            {
                WTLog::Instance().LOGERR(Ret, "set mimo FemParam failed");
            }
        }

        m_FemParam = FemParamTemp;
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_FemParam %d, %d, %lf, %lf\n", m_FemParam.FemMode, m_FemParam.DutyRatio, m_FemParam.LeadTime, m_FemParam.DelayTime);
    }
    else
    {
        Ret = ProcMimoAck(Conn);
    }
    return Ret;
}

int WTVsg::ConvertBTDataToNew(GenWaveBtOldStruct *OldParameters, GenWaveBtStruct &Parameters)
{
    memcpy(&Parameters.commonParam, &OldParameters->oldcommonParam,sizeof(OldParameters->oldcommonParam));

    if (OldParameters->BtPacketSet.PackType <= 27)
    {
        Parameters.commonParam.subType = 0;
        Parameters.BrEdrSet.PackType = OldParameters->BtPacketSet.PackType;
    }
    else
    {
        Parameters.commonParam.subType = 1;
        Parameters.BleSet.PackType = 0;
        if (OldParameters->BtPacketSet.PackType == 28)
        {
            Parameters.BleSet.FormatType = 0;
        }
        else if (OldParameters->BtPacketSet.PackType == 29)
        {
            Parameters.BleSet.FormatType = 1;
        }
        else if (OldParameters->BtPacketSet.PackType == 30)
        {
            Parameters.BleSet.FormatType = 2;
        }
    }

    if (Parameters.commonParam.subType == 0)
    {
        Parameters.BrEdrSet.LT_ADDR = OldParameters->BtPacketSet.LT_ADDR;
        Parameters.BrEdrSet.Flow = OldParameters->BtPacketSet.Flow;
        Parameters.BrEdrSet.ARQN = OldParameters->BtPacketSet.ARQN;
        Parameters.BrEdrSet.SEQN = OldParameters->BtPacketSet.SEQN;

        Parameters.BrEdrSet.DataWhitening = OldParameters->BtPacketSet.DataWhitening;
        Parameters.BrEdrSet.ModuIdex = OldParameters->BtPacketSet.ModuIdex;
        Parameters.BrEdrSet.BTProductIdex = OldParameters->BtPacketSet.BTProductIdex;
        Parameters.BrEdrSet.RolloffIdex = OldParameters->BtPacketSet.RolloffIdex;
        Parameters.BrEdrSet.GuardTime = OldParameters->BtPacketSet.GuardTime;
        Parameters.BrEdrSet.LAP = OldParameters->BtPacketSet.LAP;
        Parameters.BrEdrSet.UAP = OldParameters->BtPacketSet.UAP;
        Parameters.BrEdrSet.NAP = OldParameters->BtPacketSet.NAP;
        Parameters.BrEdrSet.PowerRampTime = OldParameters->BtPacketSet.PowerRampTime;

        if (OldParameters->BtPacketSet.PackType == 3)
        {
            Parameters.BrEdrSet.FHSPacket.EIR = OldParameters->BtPacketSet.FHS_EIR;
            Parameters.BrEdrSet.FHSPacket.SR = OldParameters->BtPacketSet.FHS_SR;
            Parameters.BrEdrSet.FHSPacket.ClassofDevice = OldParameters->BtPacketSet.FHS_ClassofDevice;
            Parameters.BrEdrSet.FHSPacket.LTAddr = OldParameters->BtPacketSet.FHS_LT_ADDR;
            Parameters.BrEdrSet.FHSPacket.CLK27b2 = OldParameters->BtPacketSet.FHS_CLK27_2;
        }
        else if (OldParameters->BtPacketSet.PackType == 20)
        {
            memset(Parameters.BrEdrSet.DVPacket.VoiceField, 0, 10);
            Parameters.BrEdrSet.DVPacket.LLID = OldParameters->BtPacketSet.LLID;
            Parameters.BrEdrSet.DVPacket.mFlow = OldParameters->BtPacketSet.mFlow;
            Parameters.BrEdrSet.DVPacket.PayLoadSize = OldParameters->BtPacketSet.PayLoadSize;
            Parameters.BrEdrSet.DVPacket.payLoadType = OldParameters->BtPacketSet.payLoadType;
        }
        else
        {
            Parameters.BrEdrSet.NormalPacket.LLID = OldParameters->BtPacketSet.LLID;
            Parameters.BrEdrSet.NormalPacket.mFlow = OldParameters->BtPacketSet.mFlow;
            Parameters.BrEdrSet.NormalPacket.PayLoadSize = OldParameters->BtPacketSet.PayLoadSize;
            Parameters.BrEdrSet.NormalPacket.payLoadType = OldParameters->BtPacketSet.payLoadType;
        }
    }
    if (Parameters.commonParam.subType == 1)
    {
        Parameters.BleSet.ModuIdex = OldParameters->BtPacketSet.ModuIdex;
        Parameters.BleSet.BTProductIdex = OldParameters->BtPacketSet.BTProductIdex;
        Parameters.BleSet.PowerRampTime = OldParameters->BtPacketSet.PowerRampTime;
        Parameters.BleSet.TestPacket.LE_SyncWord = OldParameters->BtPacketSet.LE_SyncWord;
        Parameters.BleSet.TestPacket.PayLoadSize = OldParameters->BtPacketSet.PayLoadSize;
        Parameters.BleSet.TestPacket.payLoadType = OldParameters->BtPacketSet.payLoadType;
        if (Parameters.BleSet.FormatType == 2)
        {
            Parameters.BleSet.TestPacket.Coded.BLEMapperS = OldParameters->BtPacketSet.BLEMapperS;
        }
        else
        {
            Parameters.BleSet.TestPacket.Uncoded.CTEInfo = OldParameters->BtPacketSet.LE_CTEInfo;
            Parameters.BleSet.TestPacket.Uncoded.CTEtime = OldParameters->BtPacketSet.LE_CTEtime;
            Parameters.BleSet.TestPacket.Uncoded.CTEType = OldParameters->BtPacketSet.LE_CTEType;
            Parameters.BleSet.TestPacket.Uncoded.AntNum = 1;
            Parameters.BleSet.TestPacket.Uncoded.AntGain[0] = 0.0;
        }
    }

    return WT_OK;
}

int WTVsg::SetStaticIQImb(int Segment, double Ampl, double Phase, double TimeSkew)
{
    if (Segment < 1 || Segment > 2)
    {
        WT_DEBUG(WT_ARG_ERROR, "segment error!");
        return WT_ARG_ERROR;
    }
    
    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }

    m_Compensate->SetStaticIQParam(Segment, Ampl, Phase, TimeSkew);
    m_AmendFactors[Segment - 1].ForceComp = true;
    return WT_OK;
}

int WTVsg::ClrStaticIQImb(int Segment)
{
    if (Segment < 1 || Segment > 2)
    {
        WT_DEBUG(WT_ARG_ERROR, "segment error!");
        return WT_ARG_ERROR;
    }

    if (m_Compensate == nullptr)
    {
        m_Compensate.reset(new Compensate());
        if (m_Compensate == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc normalization object failed");
            return WT_ALLOC_FAILED;
        }
    }

    m_Compensate->ClrStaticIQParam(Segment);
    m_AmendFactors[Segment - 1].ForceComp = true;
    return WT_OK;
}

int WTVsg::GetGenerateSynSeq(void **Param, int &ParamLen)
{
    int Ret = WT_OK;
    if (m_synSeqLen == 0)
    {
        Ret = WT_ARG_ERROR;
    }
    else
    {
        *Param = &m_synSeq;
        ParamLen = m_synSeqLen;
    }

    return Ret;
}

int WTVsg::SetPowerParamToMod(double NewParam)
{
    if(BroadcastVsg::Instance().IsBroadcastUser())
    {
        return WT_ARG_UNKNOW_PARAMETER;
    }

    int Ret = WT_OK;
    for (int i = 0; i < (signed)m_Mods.size(); i++)
    {
        NewParam = NewParam + m_Param[0].ExtGain - AlgEnv::Instance().m_VsgPwrBaseDelta - AlgEnv::Instance().m_VsgPwrDelta[m_FileBw[0]];
        Ret = DevLib::Instance().SetfastVsgPower(m_Mods[i].ModId, NewParam);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "vsg SetfastVsgPower to mod failed");
            return Ret;
        }
    }
    m_Param[0].Power = NewParam;
    return Ret;
}

int WTVsg::SetPathLossParamToMod(double Param)
{
    if(BroadcastVsg::Instance().IsBroadcastUser())
    {
        return WT_ARG_UNKNOW_PARAMETER;
    }

    int Ret = WT_OK;
    for (int i = 0; i < (signed)m_Mods.size(); i++)
    {
        m_Param[0].Power = m_Param[0].Power + m_Param[0].ExtGain - AlgEnv::Instance().m_VsgPwrBaseDelta - AlgEnv::Instance().m_VsgPwrDelta[m_FileBw[0]];
        Ret = DevLib::Instance().SetfastVsgPower(m_Mods[i].ModId, m_Param[0].Power);
        if (Ret != WT_OK)
        {
            WTLog::Instance().LOGERR(Ret, "vsg SetPathLossParamToMod to mod failed");
            return Ret;
        }
    }
    m_Param[0].ExtGain = Param;
    return Ret;
}

