#include "scpi_3gpp_alz_wcdma.h"
#include <iostream>
#include "basehead.h"
#include "commonhandler.h"
#include "scpi_3gpp_base.h"

static int IsAlzWCDMA(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        if (attr->vsaAlzParam.analyzeParam3GPP.Standard != ALG_3GPP_STD_WCDMA)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

//**************************************************************************************************
// Measure
//**************************************************************************************************
scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakMagnErrLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PeakMagnErrLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakMagnErrLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 99.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PeakMagnErrLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsMagnErrLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.RmsMagnErrLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsMagnErrLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 99.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.RmsMagnErrLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakEvmLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PeakEvmLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakEvmLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 99.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PeakEvmLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsEvmLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.RmsEvmLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsEvmLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 99.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.RmsEvmLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakPhaseErrLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PeakPhaseErrLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzPeakPhaseErrLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 45.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PeakPhaseErrLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsPhaseErrLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.RmsPhaseErrLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzRmsPhaseErrLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 45.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.RmsPhaseErrLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzCarrFreqErrLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.CFErrLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzCarrFreqErrLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.CFErrLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzPhaseDiscLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.PhaseDisLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzUpperLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 90.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.UpperLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzDynamicLimit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 90.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.DynamicLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureUnit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        // attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.MeasureUnit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureAclrAbsLimitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.AclrAbsLimitMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzMeasureAbsLimitValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 33.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.AbsLimit = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrLimit1Mode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.AclrLimit1Mode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrUtraLimit1Value(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.UtraLimit1 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrLimit2Mode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.AclrLimit2Mode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzAclrUtraLimit2Value(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value = 0;
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.UtraLimit2 = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitADMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.SEMLimitADMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitADValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value[4] = {0.0};
        for (int i = 0; i < ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.LimitAD); i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < -90.0 || Value[i] > 0.0)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int i = 0; i < ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.LimitAD); i++)
        {
            attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.LimitAD[i] = Value[i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitEFMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.SEMLimitEFMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_Measure_SetAlzEmisMaskLimitEFValue(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        double Value[2] = {0.0};
        for (int i = 0; i < ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.LimitEF); i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }

            if (Value[i] < -90.0 || Value[i] > 0.0)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        for (int i = 0; i < ARRAYSIZE(attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.LimitEF); i++)
        {
            attr->vsaAlzParam.analyzeParam3GPP.WCDMA.Measure.LimitEF[i] = Value[i];
        }
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzScramblingCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int MAXNum = pow(2, 24);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > (MAXNum - 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.ScramblingCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzDPCCHSlotFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.DPCCHSlotFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzConfiguration(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.ChannelType = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzDPDCHAvailable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.DPDCHAvailable = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzMeasurementLength(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 120)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.MeasureLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzSynchronisation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.SyncSlotId = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzSlotNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 119)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.SlotNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_WCDMA_UL_General_SetAlzCDPSpreadingFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 4 && Value != 8 && Value != 16 && Value != 32 && Value != 64 && Value != 128 && Value != 256)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.UL.CDPSpreadFactor = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_General_SetAlzScramblingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        // attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.ScramblingState = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_General_SetAlzScramblingCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int MAXNum = pow(2, 13);
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > (MAXNum - 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.ScramblingCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPCPICHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.PCPICH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPCPICHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        } 

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.PCPICH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPSCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.PSCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzPSCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        } 

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.PSCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzSSCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.SSCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_CPCH_SetAlzSSCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        } 

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.SSCH.Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 32)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }   

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCHNum = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHSlotFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 16)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].SlotFormat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHSymbRate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 7500 && Value != 15000 && Value != 30000 && Value != 60000 &&
            Value != 120000 && Value != 240000 && Value != 480000 && Value != 960000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].SymbRate = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHChanCode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 511)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].ChanCode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHInterleaver2Statate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }


        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.Interleaver2Stat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTTI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 10 && Value != 20 && Value != 40 )
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].TTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTbCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].TbCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHTbSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4096)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].TbSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHCrc(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 8 && Value != 12 && Value != 16 && Value != 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].Crc = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHRmAttribute(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 1024)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].RmAttribute = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHEProtection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].EProtection = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDTCHInterleaverStat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int IdxArr[2] = {0};
        int Idx = 0;
        int Idx2 = 0;
        SCPI_CommandNumbers(context, IdxArr, 2);
        Idx = IdxArr[0];
        Idx2 = IdxArr[1];
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM ||
            Idx2 < 0 || Idx2 >= ARRAYSIZE(attr->Pn3GPP->WCDMA.DL.DPCH[0].DCH.DTCH))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DTCH[Idx2].InterleaverStat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.State = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTTI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 10 && Value != 20 && Value != 40 )
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.TTI = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTbCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.TbCount = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHTbSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4096)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.TbSize = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHCrc(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value != 0 && Value != 8 && Value != 12 && Value != 16 && Value != 24)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.Crc = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHRmAttribute(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 1 || Value > 1024)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.RmAttribute = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHEProtection(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.EProtection = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHDCHDCCHInterleaverStat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);
        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].DCH.DCCH.InterleaverStat = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_WCDMA_DL_DPCH_SetAlzDPCHPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = IsAlzWCDMA(attr);
        IF_BREAK(iRet);

        int Idx = 0;
        SCPI_CommandNumbers(context, &Idx, 1);
        if (Idx < 0 || Idx >= ALG_WCDMA_MAX_DL_DPCH_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80.0 || Value > 0.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->vsaAlzParam.analyzeParam3GPP.WCDMA.DL.DPCH[Idx].Power = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}