
#pragma once

#include "includeall.h"

namespace cellular
{
    namespace wavegen
    {
        //*****************************************************************************
        // 公共接口
        //*****************************************************************************
        void SetCellularWaveGenCommonParam(int demod, PNSetBaseType &commonParam);
        void SetCellularWaveGenDefaultParam(int standard, int linkDirect, void *param);

        //*****************************************************************************
        // 内部接口
        //*****************************************************************************
        void SetGsmWaveGenDefaultParam(int linkDirect, Alg_3GPP_WaveGenType *param);
        void SetWcdmaWaveGenDefaultParam(int linkDirect, Alg_3GPP_WaveGenType *param);
        void SetLteWaveGenDefaultParam(int linkDirect, Alg_3GPP_WaveGenType *param);
        void SetNrWaveGenDefaultParam(int linkDirect, Alg_3GPP_WaveGenType *param);
        void SetNbiotWaveGenDefaultParam(int linkDirect, Alg_3GPP_WaveGenType *param);

        //*****************************************************************************
        // WCDMA
        //*****************************************************************************
        void SetWcdmaULWaveGenDefaultParam(Alg_WCDMA_ULWaveGenType &param);
        void SetWcdmaDLWaveGenDefaultParam(Alg_WCDMA_DLWaveGenType &param);

        //*****************************************************************************
        // LTE
        //*****************************************************************************
        void SetLteULWaveGenDefaultParam(Alg_4G_ULWaveGenType &param);
        void SetLteDLWaveGenDefaultParam(Alg_4G_DLWaveGenType &param);

        //*****************************************************************************
        // NR
        //*****************************************************************************
        void SetNrULWaveGenDefaultParam(Alg_5G_ULWaveGenType &param);
        void SetNrDLWaveGenDefaultParam(Alg_5G_DLWaveGenType &param);

        //*****************************************************************************
        // NBIOT
        //*****************************************************************************
        void SetNbiotULWaveGenDefaultParam(Alg_NBIOT_ULWaveGenType &param);
        void SetNbiotDLWaveGenDefaultParam(Alg_NBIOT_DLWaveGenType &param);
    }
}
