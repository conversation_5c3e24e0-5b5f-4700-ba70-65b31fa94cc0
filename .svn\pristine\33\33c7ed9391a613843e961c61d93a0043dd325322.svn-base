//*****************************************************************************
//File: templib.h
//Describe: 需先调用DevLibInit()，再调用TempLibInit(); 仅用于SERVER进程。
//Author:yuanyongchun
//Date: 2018.9.12
//*****************************************************************************

#ifndef __TEMPLIB_H__
#define __TEMPLIB_H__

#include <vector>
#include <mutex>
#include <fstream>
#include <string>
#include <sys/time.h>
#include <functional>
#include <map>

#include "wterror.h"
#include "fifolib.h"
#include "basefun.h"
#include "conf.h"
#include "wtypes.h"
#include "devtype.h"
#include "devdef.h"
#include "backplane.h"

#define MAX_RF_TEMP 2            //一个射频板单元上最大温度传感器数
#define MAX_TEMP_RECHECK_TIMES 3 //最大检测次数
#define MAX_SAVE_TEMP 100        //记录历史温度，最大数据个数

enum BUSI_TEMPERATURE_CHANNEL
{
   MOD_LO_TEMP,
   MIX_LO_TEMP,
   DEM_TEMP,
   BUSI_TEMPERATURE_MAX,
};

struct DevTemperature
{
  /// 背板温度,固定30℃
  double BackBoardTemp;

  /// 基带板温度,固定30℃
  /// m_BussiBaseBoardTemp[模块ID]
  double BussiBaseBoardTemp[MAX_BUSINESS_NUM];

  /// 开关板温度
  /// SwitchBoardTemp[开关板ID]
  double SwitchBoardTemp[WT_RF_MAX];

  /// 业务板板振荡器温度
  /// BussiModLoDetTemp[业务板类型][模块ID]
  double BussiModLoDetTemp[BUSI_UB_TYPE_COUNT][MAX_BUSINESS_NUM];
  double BussiMixLoDetTemp[BUSI_UB_TYPE_COUNT][MAX_BUSINESS_NUM];

  /// 业务板VSA DEM温度
  /// BussiVsaDemTemp[模块ID]
  double BussiVsaDemTemp[MAX_BUSINESS_NUM];
};

struct DevTempSave
{
  int Valid;
  TIME_TYPE Time;
  DevTemperature Temperature;
  DevTempSave(int IsValid = 0) : Valid(IsValid){};
};

class TempType
{
public:
  double Temperature;
  char TempInfo[256];
  struct timeval Time;
  int Error;
  int Type;
  int ReadCnt;
  std::function<int(double &)> ReadDevHandle;

  TempType() : Temperature(TEMPERATURE_NO_INIT), Error(false), Type(0), ReadCnt(0) { Time.tv_sec = 0; }

  bool IsError() { return Error == true; }
  void CheckTemperature();
  int GetTemperature(double &);
};

class TempLib
{
public:
  //*****************************************************************************
  //TempLib
  //参数[IN] : 无
  //返回值 : TempLib
  //*****************************************************************************
  static TempLib &Instance(void);

  //*****************************************************************************
  //TempLib读取温度使能，CPLD升级时不读取温度
  //参数[IN] : Enable：是否允许读取温度。
  //返回值 : 无
  //*****************************************************************************
  void TempEnable(bool Enable);

  //*****************************************************************************
  //TempLib初始化
  //参数[IN] : 无
  //返回值 : success or error code
  //*****************************************************************************
  int TempLibInit(int ServerId, const wtev::loop_ref &loop);

  //*****************************************************************************
  //获取仪器主要器件温度值
  //参数[IN] : Temperature:保存仪器温度值的对象
  //返回值 : success or error code
  //*****************************************************************************
  int GetDevTemperater(DevTemperature &Temperature);

  //*****************************************************************************
  //获取背板、基带板温度
  //参数[IN] : DevType 单元板类型, ModId 模块ID
  //参数[OUT]: Temp 温度结果
  //返回值 : success or error code
  //*****************************************************************************
  int GetUintBoardTemperater(int DevType, int ModId, double &Temp);

  //*****************************************************************************
  //获取开关板端口处温度
  //参数[IN] : 无
  //参数[OUT]: Temp 温度结果
  //返回值 : success or error code
  //*****************************************************************************
  int GetSwbPortTemperater(int Port, double &Temp);

  //*****************************************************************************
  //获取业务板频率振荡器温度
  //参数[IN] : DevType 单元板类型, ModId 模块ID, TempType 温度类型(BUSI_TEMPERATURE_CHANNEL)
  //参数[OUT]: Temp 温度结果
  //返回值 : success or error code
  //*****************************************************************************
  int GetBusiOscillatorTemperater(int DevType, int ModId, int TempType, double &Temp);

  //*****************************************************************************
  //获取保存的温度个数温度
  //返回值 : 保存的温度个数温度
  //*****************************************************************************
  int GetSaveCnt() { return (SaveData) ? SaveData->GetCnt() : 0; }

  //*****************************************************************************
  //获取历史温度
  //参数[IN] : 最近的第Cnt个历史温度
  //返回值 : DevTempSave:历史温度
  //*****************************************************************************
  DevTempSave GetSaveData(const int &Cnt, DevTempSave &Data){if (SaveData) Data = SaveData->GetData(Cnt); return WT_OK; }

  //*****************************************************************************
  //获取保存的温度个数温度
  //参数[IN]: 温度数据地址
  //返回值 : 保存的温度个数温度
  //*****************************************************************************
  int GetSaveAllData(char *Addr) { return (SaveData) ? SaveData->GetAllData(Addr) : WT_OK; }

  //*****************************************************************************
  //读取TempType对象的温度
  //参数[IN]: Temp:要读取的温度对象
  //返回值 : success or error code
  //*****************************************************************************
  int ReadTemperature(TempType &Temp);

private:
  TempLib();
  ~TempLib();

  //初始化温度读取对象
  void InitTemperatureMap(void);

  //设置温度错误
  void SetTempErr(void);

  //清除温度错误
  void ClearTempErr(void);

  //检测是否有温度错误
  bool CheckTempErr(void);

  //判断温度值是否有效
  int CheckTempValid(int Result, double Temp, TempType &LastTemp, int CheckTimes);

  //获取设备平均温度
  int GetDevAvgTemperature(double &AvgTemp);

  //检测温度值是否需要更新
  bool CheckAndSetTempTime(TempType &Temp);

  //*****************************************************************************
  //温度读取定时器回掉函数
  //参数[IN] : 无关
  //返回值: 无
  //*****************************************************************************
  void TempReadTimerCb(wtev::timer &watcher, int revents);

  //*****************************************************************************
  //温度保存定时器回掉函数
  //参数[IN] : 无关
  //返回值: 无
  //*****************************************************************************
  void TempSaveTimerCb(wtev::timer &watcher, int revents);

  //读取本振器件温度对应的通道
  int GetOscillatorTempChannel(int DevType, int TempType);

  enum WT_TEMPERATURE_TYPE_E
  {
    WT_UNIT_BOARD_TEMP,
    WT_SWITCH_PORT_TEMP,
    WT_BUSI_OSCILLATOR_TEMP,
    WT_TEMPERATURE_TYPE_MAX,
  };

private:
  int m_LogEnable;                 //是否记录LOG到文件
  int m_TempThreshold;             //连续读温度变化阈值
  std::ofstream m_Fs;              //log记录文件
  char m_LogName[200];             //LOG文件名
  int *m_ReadTempEnable = nullptr; //是否读取温度使能地址,共享内存（CPLD升级时不读取温度）。
  int m_SaveTempEnable;

  wtev::timer m_TempTimerEv;                     //温度检测的定时器
  double m_TempCheckBuf[MAX_TEMP_RECHECK_TIMES]; //缓存检测失败的温度数值。
  int m_TempErr;                                 //温度错误报警标志
  struct timeval m_TempErrTime;                  //温度出错时间

  int m_BPHwVerion = VERSION_B;                //仪器硬件版本
  int m_SwbExist[SWTICH_PART_MAX] = {0};       //开关板类型
  int m_SWBHwVersion = VERSION_A;              //开关板硬件版本
  int m_BusiHwVersion[MAX_BUSINESS_NUM] = {0}; //业务板硬件版本
  int m_PortMask = 0;                          //端口掩码
  int m_VsaMask = 0;                           //端口掩码
  int m_VsgMask = 0;                           //端口掩码
  int m_ModNum = 0;

  std::map<int, TempType> m_TemperatureMap;      //温度对象

  std::vector<std::map<int, TempType>> m_TemperatureVector;
  wtev::timer m_TempSaveTimerEv;                  //温度保存的定时器
  std::unique_ptr<FifoLib<DevTempSave>> SaveData; //温度保存对象
};

#endif