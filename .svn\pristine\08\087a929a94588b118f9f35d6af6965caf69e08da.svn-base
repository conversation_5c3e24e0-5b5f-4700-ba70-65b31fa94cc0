#pragma once
#ifndef _PRIVATE_ALG_VSG_H_
#define _PRIVATE_ALG_VSG_H_

typedef struct
{
    int TriggerType;
    int	TBLength;
    int	MoreTF;
    int	CSRequired;
    int	TBULBW;
    int	TBGILTF;
    int	TBMMMode;
    int	TBLTFSym;
    int	TBSTBC;
    int	TBLDPCExtra;
    int	APTxPower;
    int TBAfactor;
    int	TBPE;
    int mPad;
    int TBSR[4];
    int	TBDoppler;
    int	TBUserNum;
    int	TBAID[AX_USER_COUNT];
    int TBRUIndex[AX_USER_COUNT];
    int TBSegment[AX_USER_COUNT];
    int TBCoding[AX_USER_COUNT];
    int TBMCS[AX_USER_COUNT];
    int TBDCM[AX_USER_COUNT];
    int TBSSStart[AX_USER_COUNT];
    int TBSSCount[AX_USER_COUNT];
    int TBRSSI[AX_USER_COUNT];
    int TBSpacingFactor[AX_USER_COUNT];
    int	TBAggLimit[AX_USER_COUNT];
    int	TBPreAC[AX_USER_COUNT];
    int	TBMultiplexing[AX_USER_COUNT];
    int TBRxTxMap[AX_USER_COUNT];
    int TBBARInfoLen;
    unsigned char 	TBBARControl[4];
    unsigned char 	TBBARInfo[4];
    int TBMidamble_Periodicity;
    int PE_Disambiguity;
    double Reserved[255];   //保留位
} TF11ax;
#endif