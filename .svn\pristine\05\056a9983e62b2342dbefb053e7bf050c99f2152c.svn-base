#include "includes.h"
#include <limits.h>
#include <random>
#include <cmath>
#include "common.h"

static int snprintf_error(size_t size, int res)
{
    return res < 0 || (unsigned int)res >= size;
}

static s8 hex2num(char c)
{
    if (c >= '0' && c <= '9')
    {
        return c - '0';
    }

    if (c >= 'a' && c <= 'f')
    {
        return c - 'a' + 10;
    }

    if (c >= 'A' && c <= 'F')
    {
        return c - 'A' + 10;
    }

    return -1;
}

s8 hex2byte(const char *hex)
{
    s8 a = 0;
    s8 b = 0;
    a = hex2num(*hex++);
    if (a < 0)
    {
        return -1;
    }

    b = hex2num(*hex++);
    if (b < 0)
    {
        return -1;
    }

    return (a << 4) | b;
}


int hexstr2bin(const char *hex, u8 *buf, size_t len)
{
    size_t i;
    u8 a;
    const char *ipos = hex;
    u8 *opos = buf;

    for (i = 0; i < len; i++) {
        a = hex2byte(ipos);
        *opos++ = a;
        ipos += 2;
    }
    return 0;
}

int wpa_scnprintf(char *buf, size_t size, const char *fmt, ...)
{
    va_list ap;
    int ret = 0;

    if (!size)
    {
        return 0;
    }

    va_start(ap, fmt);
    ret = vsnprintf(buf, size, fmt, ap);
    va_end(ap);

    if (ret < 0)
    {
        return 0;
    }

    if ((size_t)ret >= size)
    {
        return size - 1;
    }

    return ret;
}

static int _wpa_snprintf_hex(char *buf, size_t buf_size, const u8 *data, size_t len, int uppercase)
{
    size_t i = 0;
    char *pos = buf, *end = buf + buf_size;
    int ret = 0;
    if (buf_size == 0)
    {
        return 0;
    }

    for (i = 0; i < len; i++) 
    {
        ret = snprintf(pos, end - pos, uppercase ? "%02X" : "%02x", data[i]);
        if (snprintf_error(end - pos, ret)) 
        {
            end[-1] = '\0';
            return pos - buf;
        }
        pos += ret;
    }
    end[-1] = '\0';
    return pos - buf;
}

int wpa_snprintf_hex(char *buf, size_t buf_size, const u8 *data, size_t len)
{
    return _wpa_snprintf_hex(buf, buf_size, data, len, 0);
}

int wpa_snprintf_hex_uppercase(char *buf, size_t buf_size, const u8 *data, size_t len)
{
    return _wpa_snprintf_hex(buf, buf_size, data, len, 1);
}

int is_hex(const u8 *data, size_t len)
{
    size_t i;

    for (i = 0; i < len; i++) {
        if (data[i] < 32 || data[i] >= 127)
            return 1;
    }
    return 0;
}

u16 WPA_GET_BE16(const u8 *a)
{
    return (a[0] << 8) | a[1];
}

void WPA_PUT_BE16(u8 *a, u16 val)
{
    a[0] = val >> 8;
    a[1] = val & 0xff;
}

u16 WPA_GET_LE16(const u8 *a)
{
    return (a[1] << 8) | a[0];
}

void WPA_PUT_LE16(u8 *a, u16 val)
{
    a[1] = val >> 8;
    a[0] = val & 0xff;
}

u32 WPA_GET_BE24(const u8 *a)
{
    return (a[0] << 16) | (a[1] << 8) | a[2];
}

void WPA_PUT_BE24(u8 *a, u32 val)
{
    a[0] = (val >> 16) & 0xff;
    a[1] = (val >> 8) & 0xff;
    a[2] = val & 0xff;
}

u32 WPA_GET_BE32(const u8 *a)
{
    return ((u32)a[0] << 24) | (a[1] << 16) | (a[2] << 8) | a[3];
}

void WPA_PUT_BE32(u8 *a, u32 val)
{
    a[0] = (val >> 24) & 0xff;
    a[1] = (val >> 16) & 0xff;
    a[2] = (val >> 8) & 0xff;
    a[3] = val & 0xff;
}

u32 WPA_GET_LE32(const u8 *a)
{
    return ((u32)a[3] << 24) | (a[2] << 16) | (a[1] << 8) | a[0];
}

void WPA_PUT_LE32(u8 *a, u32 val)
{
    a[3] = (val >> 24) & 0xff;
    a[2] = (val >> 16) & 0xff;
    a[1] = (val >> 8) & 0xff;
    a[0] = val & 0xff;
}

u64 WPA_GET_BE64(const u8 *a)
{
    return (((u64)a[0]) << 56) | (((u64)a[1]) << 48) |
        (((u64)a[2]) << 40) | (((u64)a[3]) << 32) |
        (((u64)a[4]) << 24) | (((u64)a[5]) << 16) |
        (((u64)a[6]) << 8) | ((u64)a[7]);
}

void WPA_PUT_BE64(u8 *a, u64 val)
{
    a[0] = (val >> 56) & 0xFF;
    a[1] = (val >> 48) & 0xFF;
    a[2] = (val >> 40) & 0xFF;
    a[3] = (val >> 32) & 0xFF;
    a[4] = (val >> 24) & 0xFF;
    a[5] = (val >> 16) & 0xFF;
    a[6] = (val >> 8) & 0xFF;
    a[7] = (val & 0xFF);
}

u64 WPA_GET_LE64(const u8 *a)
{
    return (((u64)a[7]) << 56) | (((u64)a[6]) << 48) |
        (((u64)a[5]) << 40) | (((u64)a[4]) << 32) |
        (((u64)a[3]) << 24) | (((u64)a[2]) << 16) |
        (((u64)a[1]) << 8) | ((u64)a[0]);
}

void WPA_PUT_LE64(u8 *a, u64 val)
{
    a[7] = (val >> 56) & 0xff;
    a[6] = (val >> 48) & 0xff;
    a[5] = (val >> 40) & 0xff;
    a[4] = (val >> 32) & 0xff;
    a[3] = (val >> 24) & 0xff;
    a[2] = (val >> 16) & 0xff;
    a[1] = (val >> 8) & 0xff;
    a[0] = val & 0xff;
}

typedef struct pcap_hdr_s
{
    u32 magic_number;
    u16 version_major;
    u16 version_minor;
    s32 thiszone;
    u32 sigfigs;
    u32 snaplen;
    u32 network;
} pcap_hdr_t;

typedef struct pcaprec_hdr_s
{
    u32 ts_sec;
    u32 ts_usec;
    u32 incl_len;
    u32 orig_len;
} pcaprec_hdr_t;

s32 create_pcap(std::string fileName, u8 *data, s32 len, s32 offset)
{
    int iRet = -1;

    pcap_hdr_t global_header;
    memset(&global_header, 0, sizeof(pcap_hdr_t));
    global_header.magic_number = 0xa1b2c3d4;
    global_header.version_major = 2;
    global_header.version_minor = 4;
    global_header.snaplen = 0xFFFF;
    //    global_header.network = 1;/*	IEEE 802.3 Ethernet (10Mb, 100Mb, 1000Mb, and up); the 10MB in the DLT_ name is historical. */
    global_header.network = 105; /*	IEEE 802.11 wireless LAN. */

    pcaprec_hdr_t pack_header;
    memset(&pack_header, 0, sizeof(pcaprec_hdr_t));
    pack_header.orig_len = len;
    pack_header.incl_len = pack_header.orig_len;

    FILE *fp = fopen(fileName.c_str(), "wb");
    if (fp)
    {
        fwrite(&global_header, 1, sizeof(global_header), fp);
        fwrite(&pack_header, 1, sizeof(pack_header), fp);
        fwrite(data + offset, 1, len - offset, fp);
        fclose(fp);

        iRet = 0;
    }
    return iRet;
}

s32 create_pcap(std::string fileName, s32 index, u8 *data, s32 len, s32 offset)
{
    int iRet = -1;
    if (0 == index)
    {
        return create_pcap(fileName, data, len, offset);
    }

    FILE *fp = fopen(fileName.c_str(), "ab");
    if (fp)
    {
        pcaprec_hdr_t pack_header;
        memset(&pack_header, 0, sizeof(pcaprec_hdr_t));
        pack_header.orig_len = len - offset;
        pack_header.incl_len = pack_header.orig_len;

        fwrite(&pack_header, 1, sizeof(pack_header), fp);
        fwrite(data + offset, 1, len - offset, fp);

        fclose(fp);

        iRet = 0;
    }
    return iRet;
}

s32 get_random_int(s32 min, s32 max)
{
    // Seed with a real random value, if available
    std::random_device r;
    // Choose a random mean between min and max(include min and max)
    std::default_random_engine e1(r());
    std::uniform_int_distribution<int> uniform_dist(min, max);
    return uniform_dist(e1);
}