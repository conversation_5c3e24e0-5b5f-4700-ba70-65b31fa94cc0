//*****************************************************************************
//  File: device.h
//  describe: 设备信息
//  Data: 2016.9.1
//*****************************************************************************

#ifndef __DEVICE_INFO_H__
#define __DEVICE_INFO_H__

#include <iostream>
#include <string>
#include <string.h>
#include "devtype.h"


#define WT_DEVICE_INIT_FILEPATH      "/tmp/wt_device_inited"            //临时文件

// 不同仪器类型的最大端口数
#if WT448_FW
#define MAX_PORT_NUM 8
#elif WT428_FW
#define MAX_PORT_NUM 8
#elif WT422_FW
#define MAX_PORT_NUM 2
#else
#define MAX_PORT_NUM 8
#endif

// 是否具有交叉模式
#if WT422_FW
#define IS_CROSS 1
#else
#define IS_CROSS 0
#endif

//子网口最大数
#define MAX_SUB_NET_NUM 8
#define IP_MAX_LEN 16
#define MAC_MAX_LEN 18
#define DEVNAME_MAX_LEN 16
//IP地址定义
typedef char IPType[IP_MAX_LEN];
typedef char MacType[MAC_MAX_LEN];
typedef char DevNameType[DEVNAME_MAX_LEN];

// IP信息
struct IPInfo
{
    int Vaild; //是否有效
    IPType IP; //详细的IP

    IPInfo() : Vaild(0) { *IP = 0; }
    bool Compare(const IPInfo &Obj) const { return (Vaild != Obj.Vaild || (Vaild && Obj.Vaild && strcmp(IP, Obj.IP))); }
};

//子网口信息结构体, 成员顺序和数目需和API定义的VirtualNetType结构体保持一致。
struct SystemIPInfo
{
	IPInfo SubIP[MAX_SUB_NET_NUM];                   ///< 虚拟IP,最多8个子网口，依次对应1~8子网口。不需要配置的子网口赋值为NULL
	IPInfo DutIP;                                    ///< DUT IP,必须与仪器IP不在同一网段
	IPInfo DutAsServerIP;                            ///< DUT AS SERVER: TFTP Server IP, 
	IPInfo TftpServerIP;                             ///< PC AS SERVER : TFTP Server IP, 
	IPInfo TftpClientIP;                             ///< PC AS SERVER : TFTP Client IP, 
	IPInfo TftpServerPcIP;                           ///< PC AS SERVER : PC IP, 需确保该IP与DUT的虚拟IP在同一网络
    IPInfo TftpServerPcIP2;                          ///< PC AS SERVER : PC IP2,需确保该IP与DUT的虚拟IP在同一网络
};

enum UPGRADE_STATE
{
    UPGRADE_IDLE_STATE = 0,     /* 空闲状态 */
    UPGRADE_BUSY_STATE = 1,     /* 升级状态 */
    //UPGRADE_ERROR_STATE = 2,    /* 错误状态 */
};

//背板信息
struct BackPlaneInfo
{
    char FPGAVersion[40];                   //背板FPGA逻辑版本号
    char FPGADate[20];                      //背板FPGA编译日期(年月日)
    char FPGATime[20];                      //背板FPGA编译时间(时分秒)
    char BPHWVersion[40];                   //背板硬件版本
    char SwitchHWVersion[40];               //开关板硬件版本
    char CalDate[20];                       //校准日期 "2012-05-04  18:36:56"
    char RemarkInfo[40];                    //背板备注信息
};

//业务板信息
struct BusinessBoardInfo
{
    char Type[8];                           //业务板类型（VSA/VSG）
    char SN[80];                            //业务板SN码
    char FPGAVersion[40];                   //业务板FPGA版本
    char FPGADate[20];                      //业务板FPGA编译日期(年月日)
    char FPGATime[20];                      //业务板FPGA编译时间(时分秒)
    char BBHWVersion[40];                   //业务板基带板版本
    char RFHWVersion[40];                   //业务板射频板版本
    char CalDate[20];                       //校准日期 "2012-05-04  18:36:56"
    char RemarkInfo[40];                    //业务板备注信息
};

struct DeviceInfo
{
    char IP[16];                            //IP地址
    char SubMask[16];                       //子网掩码
    char GateWay[16];                       //网关
    char SN[80];                            //仪器SN码
    char Name[40];                          //别名  "ITEST  Tester"
    char Mac[18];                           //MAC 地址 "DC-A4-B5-C7-E1-F8"
    char FwVersion[40];                     //固件版本
    char ALGVersion[40];                    //算法版本
    int  BusiBoardCount;                    //业务板数量
    BackPlaneInfo BPInfo;                   //背板信息
    BusinessBoardInfo BusiBoardInfo[8];     //业务板信息
    char ProduceDate[20];                   //生产日期 "2012-05-04  18:36:56"
};


struct DeviceHwInfo
{
    bool BackInfoIsOk = false;
    bool BusiInfoIsOk = false;
    std::string BackFpgaVersion;
    std::string BackPlaneHwVersion;
    std::string SwitchBoardHwVersion;
    int SwbVersion;
    int BackPlaneRevisionId;

    std::string BusiFpgaVersion;
    std::string BusiHwVersion;
    std::string BusiRfHwVersion;
    int BusiRevisionId;
};

class WTDeviceInfo
{
public:
    //*****************************************************************************
    // 获取Device info对象
    // 参数[IN] : 无
    // 返回值: Device info对象指针
    //*****************************************************************************
    static WTDeviceInfo &Instance();

    //*****************************************************************************
    // 获取IP
    // 参数 : 无
    // 返回值: IP
    //*****************************************************************************

    const char *GetDeviceType();                        //获取仪器类型Str name
    int GetDevType();                                   //获取仪器类型对应的枚举值

    int SetDeviceIP(const char *IPBuf);                //设置仪器IP
    int SetSubmask(const char *MaskBuf);               //设置子码掩码
    int SetGateWay(const char *GateWayBuf);            //设置网关
    int SetDeviceName(const char *NameBuf, int NameLen);//设置仪器名称
    int SetDeviceMac(const char *MacBuf);              //仪器的真实mac

    const DeviceInfo& GetDeviceDetailedInfo();          //获取仪器的详细信息的结构
    int GetDeviceSubNetInfo(char *pBuf, int &InfoSize); //获取子网口设计的信息结构
    int GetDeviceSubNetLink(bool *pBuf, int &InfoSize);  //获取子网卡link状态
    int GetDeviceIpAddressType(bool *IsDchp, int &InfoSize);        //获取主网口ip地址类型

    int GetDevUpgradeState();                           //获取仪器升级状态
    int SetDevUpgradeState(int Flag);                   //修改仪器升级状态

    //*****************************************************************************
    // 函数: CheckNetInfo()
    // 功能:检查网络配置信息，即比较dev_info中的ip、mac地址和实际的ip、mac地址，若不一致，则修改base.conf文件，使之比实际一致
    // 参数 [IN]：second ：获取有效IP的最大等待时间(S)
    // 返回值：成功或者错误码
    //*****************************************************************************
    int CheckNetInfo(int second = 0); //manager 启动时需要检查

    //*****************************************************************************
    // 函数: ReCheckNetInfo()
    // 功能:检查设备信息的IP是否已经配置，有则返回WO_OK，否则再次从仪器获取网口信息
    // 参数 [IN]：无
    // 返回值：成功或者错误码
    //*****************************************************************************
    int ReCheckNetInfo(void);

    //恢复出厂设置，相关参数配置等的恢复
    int RestoreDeviceSetting(void);

    /**************************子网口配置部分***************************************/
    //*****************************************************************************
    // 函数: SetSubNetInfo()
    // 功能: 设置子网口
    // 参数 [IN]：pIP：IP数据的首地址
    // 参数 [IN]：IPNumber：IP数据的数目
    // 返回值：0 网段一致，1 网段不相同
    //*****************************************************************************
    int SetSubNetInfo(IPType *pIP, int IPNumber);

    //*****************************************************************************
    // 函数: GetMacInfo()
    // 功能: 获取Mac地址
    // 参数 [IN]：pIP：IP地址
    // 参数 [IN]：MacAddr：Mac地址
    // 参数 [IN]：DevName：网口
    // 返回值：错误码
    //*****************************************************************************
    int GetMacInfo(IPType pIP, MacType MacAddr, DevNameType DevName);

    //*****************************************************************************
    // 函数: CompareAddrSegment()
    // 功能:比较设备IP和配置的dut IP 网段是否一致
    // 参数 [IN]：IP:设备IP
    // 参数 [IN]：DutIP：DUT IP
    // 参数 [IN]：NetMask：子网掩码
    // 返回值：0 网段一致，1 网段不相同
    //*****************************************************************************
    int CompareAddrSegment(const char *IP, const char *DutIP, const char *NetMask);

    //*****************************************************************************
    // 函数: SetSubIP()
    // 功能:通过network.sh设置子网口的虚拟ip
    // 参数 [IN]：Index：子网口的编号
    //           SubIP：子网口对应要设置的IP
    // 返回值：0
    //*****************************************************************************
    int SetSubIP(int Index, char *SubIP);

    //*****************************************************************************
    // 函数: SetDutIP()
    // 功能:通过nat.sh来设置相关的网络信息
    // 参数 [IN]：DutIP:DUT IP
    //           TftpServerIP：DUT IP
    //           TftpClientIP：子网掩码
    //           PCAsServerIP：子网掩码
    // 返回值：0
    //*****************************************************************************
    int SetDutIP(const SystemIPInfo &SubIPInfo);

    //*****************************************************************************
    // 函数: SaveSubNetworkInfo()
    // 功能:保存子网口配置信息到subnet.conf文件中
    // 返回值：无
    //*****************************************************************************
    void SaveSubNetworkInfo();

    //*****************************************************************************
    // 函数: RereadSubNetworkInfo()
    // 功能: 重新从subnet.conf文件中读取子网口配置
    // 返回值：正确或者错误返回值
    //*****************************************************************************
    int RereadSubNetworkInfo();

    //*****************************************************************************
    // 函数: SetSubNetworkInfoWhenRestart()
    // 功能:仪器启动时从subnet.conf文件中读取子网口配置信息，并配置
    // 返回值：无
    //*****************************************************************************
    void SetSubNetworkInfoWhenRestart();

    //*****************************************************************************
    // 函数: SetDeviceInfo
    // 功能: 保存设备信息；设备设备信息，包括IP、Submask、Gateway、Tester Name
    // 参数 [IN]：
    //          SaveandSetFlag: false ：保存到base.conf配置文件，但不需要重新保存到系统的/etc/network/interfaces
    //                          true ：（默认）保存到base.conf配置文件，并设置到/etc/network/interfaces，并会重启network
    // 返回值：正确或者错误返回值
    //*****************************************************************************
    int SetDeviceInfo(bool SaveandSetFlag = true);

    //*****************************************************************************
    // 函数: DeviceSnVerify
    // 功能: 将下发的SN/CODE与加密芯片保存的SN/CODE比较
    // 参数 [IN]：
    //          CMInfo: 下发的设备信息
    //
    // 返回值：一致正确则返回0，其他返回非0
    //*****************************************************************************
    int DeviceSnVerify(const char *SN, const char *Code);

    //*****************************************************************************
    // 函数: SnSelfTest
    // 功能: 自检是否获取到了有效的SN码。
    // 返回值：成功或者-1
    //*****************************************************************************
    int SnSelfTest(char* SN = NULL);

    //*****************************************************************************
    // 函数: DeviceRegetSn
    // 功能: 重新获取背板的SN码。
    // 返回值：成功或者错误码
    //*****************************************************************************
    int DeviceRegetSn(void);

    //*****************************************************************************
    // 函数: DeviceSnVerify
    // 功能: 开机启动时，如果检测插入U盘，则输出仪器信息到U盘
    // 返回值：成功或者错误码
    //*****************************************************************************
    int GetSystemInfoByUsbStorage(void);

    //*****************************************************************************
    // 函数: DeviceSnVerify
    // 功能: 获取设备各部分的版本Info，算法、校准等
    // 参数 [OUT]：
    //          VerInfo: Json类型的信息
    // 返回值：成功或者错误码
    //*****************************************************************************
    int GetDeviceVersionInfo(std::string &JsonVerInfoStr);

    //*****************************************************************************
    // 函数: SaveBoardHwVersion
    // 功能: 开机启动时，保存仪器硬件信息，用于升级失败时，判断仪器版本
    // 返回值：成功或者错误码
    //*****************************************************************************
    int SaveBoardHwVersion();

    //*****************************************************************************
    // 函数: ReadBoardHwVersion
    // 功能: 获取开机保存的仪器硬件文件信息。
    // 参数 [OUT]：
    //          HwInfo: 获取到的版本信息
    // 返回值：成功或者错误码
    //*****************************************************************************
    int ReadBoardHwVersion(DeviceHwInfo &HwInfo);

    //*****************************************************************************
    // 函数: CheckFpgaVersionValid
    // 功能: 检查CPLD版本号是否合法。
    // 参数 [IN]：
    //          Version: 要检查的CPLD版本号
    // 返回值：成功或者错误码
    //*****************************************************************************
    static int CheckFpgaVersionValid(const char* Version);

    //*****************************************************************************
    // 函数: CheckFpgaVersionValid
    // 功能: 检查CPLD版本号是否合法。
    // 参数 [IN]：
    //          Version: 要检查的CPLD版本号
    // 返回值：成功或者错误码
    //*****************************************************************************
    static int CheckHwVersionValid(const char* Version);

private:
    WTDeviceInfo();
    ~WTDeviceInfo() {}

    //*****************************************************************************
    // 函数: DeviceInfoInit
    // 功能: 从硬件或配置文件获取仪器信息
    // 返回值：无
    //*****************************************************************************
    void DeviceInfoInit(void);

    //*****************************************************************************
    // 函数: GetDeviceRealNetInfo
    // 功能: 获取仪器实际的IP和MAC，主要用于程序启动时的检测
    // 参数 [OUT]：
    //          IP:实际获取到的IP
    //          MAC:实际获取到的MAC
    // 返回值：正确或者错误返回值
    //*****************************************************************************
    int GetDeviceRealNetInfo(char *IP, char *MAC, char *NetMask, char *Broadcast, char *GateWay);

    //获取设备信息
    int GetDeviceInfo(void);

    //获取基带板、射频板、开关板、灯板的PCB版本，版本信息在version文件中
    int GetBoardVersion(void);

    //检查子网口配置是否合法
    int CheckSystemIPInfo(const SystemIPInfo &IPInfo);

    //设置子网口信息结构
    int SetDevSubNetInfo(const SystemIPInfo &SubIPInfo);

    //获取网口link状态
    bool GetLinkStatus(char* netName);

private:
    //base device info
    char m_DevTypeStr[80];

    //system info
    int m_DevType = 0;              //设备类型
    int m_OSType;                   //操作系统类型
    int m_Fpga;                     //FPGA芯片类型
    int m_BaseBoard;                //基带板类型
    int m_ModelType;                //WT设备类型
    bool m_IsIpAssigned = false;            //IP是否配置

    std::string m_CurDir;           //当前目录
    SystemIPInfo m_SysIP;           //子网口配置信息
    int m_IsUpgrade;                //仪器是否处于升级更新状态
    DeviceInfo m_DevInfo;           //设备信息
};

#endif  //__DEVICE_INFO_H__
