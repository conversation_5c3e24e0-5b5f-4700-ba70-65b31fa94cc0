#include "includeall.h"
#include "WaveGenerator.h"
#include "WaveGenerator_11BE.h"
#include "scpi_3gpp_alz_json_format.h"

using json = nlohmann::json;

typedef struct
{
    int RUIndex;
    vector<int> userList;
} TF_RU;

static int AxCommonParamValid_SU(Set11AX_SU *PN11ax_su)
{
    s32 s32Err = WT_ERR_CODE_PARAMETER_MISMATCH;

    do
    {
        //0~1
        IF_RANGE_OUT(PN11ax_su->STBC, 0, 1);
        //0~1
        IF_RANGE_OUT(PN11ax_su->UL_DL, 0, 1);
        //0~13
        IF_RANGE_OUT(PN11ax_su->MCS, 0, 13);
        //0~1
        IF_RANGE_OUT(PN11ax_su->CodingType, 0, 1);
        //1~MAX_NUM_OF_CHNNEL
        IF_RANGE_OUT(PN11ax_su->NSS, 1, MAX_NUM_OF_CHNNEL);
        //0~4
        IF_RANGE_OUT(PN11ax_su->GILTFSize, 0, 4);
        //0~1
        IF_RANGE_OUT(PN11ax_su->PE, 0, 1);
        //0~2
        IF_RANGE_OUT(PN11ax_su->PE_Type, 0, 2);
        //0~1
        IF_RANGE_OUT(PN11ax_su->BeamChange, 0, 1);
        //0~63
        IF_RANGE_OUT(PN11ax_su->BSScolor, 0, 63);
        //0~127
        IF_RANGE_OUT(PN11ax_su->TXOP, 0, 127);
        //0~1
        IF_RANGE_OUT(PN11ax_su->Doppler, 0, 1);
        //0~1
        IF_RANGE_OUT(PN11ax_su->DCM, 0, 1);
        //1~2
        IF_RANGE_OUT(PN11ax_su->Midamble_Periodicity, 1, 2);

        s32Err = WT_ERR_CODE_OK;
    } while (0);

    return s32Err;
}

static void AxUserPowerFactor(double &factor)
{
    const double powerFactor[] = { 0.5, 0.707, 1.0, 1.414, 2.0 };
    double dEpsilon = 1e-6;
    bool validFlag = false;
    for (int k = 0; k < ARRAYSIZE(powerFactor); k++)
    {
        if (fabs(factor - powerFactor[k]) < dEpsilon)
        {
            validFlag = true;
            break;
        }
    }

    if (!validFlag)
    {
        factor = powerFactor[2]; //Ĭ��1.0
    }

    return;
}

static int AxCommonParamValid_MU(Set11AX_MU *PN11ax_mu)
{
    s32 s32Err = WT_ERR_CODE_PARAMETER_MISMATCH;
    bool InitfullBand = false;
    do
    {
        //0~1
        IF_RANGE_OUT(PN11ax_mu->FullBand, 0, 1);
        //0~1
        IF_RANGE_OUT(PN11ax_mu->STBC, 0, 1);
        //0~1
        IF_RANGE_OUT(PN11ax_mu->UL_DL, 0, 1);

        for (int i = 0; i < AX_USER_COUNT; i++)
        {
            if (PN11ax_mu->FullBand)
            {
                if (PN11ax_mu->RU[i].UserNum && !InitfullBand)
                {
                    InitfullBand = true;
                }
                else if (InitfullBand)
                {
                    PN11ax_mu->RU[i].UserNum = 0;
                }
            }

            for (int j = 0; j < MUMIMO_8_USER; j++)
            {
                MUMIMO_User *User = &PN11ax_mu->RU[i].User[j];
                //0~13
                if (0 > User->MCS || 13 < User->MCS)
                {
                    User->MCS = 7;
                }

                //0~1
                if (0 != User->CodingType && 1 != User->CodingType)
                {
                    User->CodingType = 1;
                }

                //1~MAX_NUM_OF_CHNNEL
                if (User->NSS < 1 || User->NSS > MAX_NUM_OF_CHNNEL)
                {
                    User->NSS = 1;
                }

                //0~1
                if (0 != User->DCM && 1 != User->DCM)
                {
                    User->DCM = 0;
                }

                //1~2007
                if (1 > User->AID || 2007 < User->AID)
                {
                    User->AID = 1;
                }

                AxUserPowerFactor(User->PowerFact);
            }
        }

        //0~4
        IF_RANGE_OUT(PN11ax_mu->GILTFSize, 0, 4);
        //0~1
        IF_RANGE_OUT(PN11ax_mu->PE, 0, 1);
        //0~2
        IF_RANGE_OUT(PN11ax_mu->PE_Type, 0, 2);
        //0~63
        IF_RANGE_OUT(PN11ax_mu->BSScolor, 0, 63);
        //0~127
        IF_RANGE_OUT(PN11ax_mu->TXOP, 0, 127);
        //0~1
        IF_RANGE_OUT(PN11ax_mu->Doppler, 0, 1);
        //1~2
        IF_RANGE_OUT(PN11ax_mu->Midamble_Periodicity, 1, 2);
        //0~5
        IF_RANGE_OUT(PN11ax_mu->SIGBMCS, 0, 5);
        //0~1
        IF_RANGE_OUT(PN11ax_mu->SIGBDCM, 0, 1);
        //0~1
        IF_RANGE_OUT(PN11ax_mu->SIGB_Compression, 0, 1);

        s32Err = WT_ERR_CODE_OK;

    } while (0);

    return s32Err;
}

static int AxCommonParamValid_TB(Set11AX_TB *PN11ax_tb)
{
    s32 s32Err = WT_ERR_CODE_PARAMETER_MISMATCH;

    do
    {
        //0~1
        IF_RANGE_OUT(PN11ax_tb->STBC, 0, 1);
        //0~74, segment 1 or segment 2 RU number may be zero;
        IF_RANGE_OUT(PN11ax_tb->RUNum[0], 0, 74);
        IF_RANGE_OUT(PN11ax_tb->RUNum[1], 0, 74);

        if (0 == PN11ax_tb->RUNum[0] && 0 == PN11ax_tb->RUNum[1])
        {
            break;
        }
        //0~4
        IF_RANGE_OUT(PN11ax_tb->NumLTFSymbols, 0, 4);

        for (int segmentID = 0; segmentID < MAX_SEGMENT; segmentID++)
        {
            for (int i = 0; i < AX_USER_COUNT; i++)
            {
                for (int j = 0; j < MUMIMO_8_USER; j++)
                {
                    TB_MUMIMO_User *User = &PN11ax_tb->RU[segmentID][i].User[j];
                    //0~13
                    if (0 > User->MCS || 13 < User->MCS)
                    {
                        User->MCS = 7;
                    }

                    //0~1
                    if (0 != User->CodingType && 1 != User->CodingType)
                    {
                        User->CodingType = 1;
                    }

                    //0~1
                    if (0 != User->SegmentIndex && 1 != User->SegmentIndex)
                    {
                        User->SegmentIndex = 0;
                    }

                    //1~MAX_NUM_OF_CHNNEL
                    if (User->NSS < 1 || User->NSS > MAX_NUM_OF_CHNNEL)
                    {
                        User->NSS = 1;
                    }

                    //0~1
                    if (0 != User->DCM && 1 != User->DCM)
                    {
                        User->DCM = 0;
                    }

                    //1~2007
                    if (1 > User->AID || 2007 < User->AID)
                    {
                        User->AID = 1;
                    }

                    //0~68
                    if (0 > User->RuIndex || 68 < User->RuIndex)
                    {
                        User->RuIndex = 0;
                    }
                }
            }
        }

        //0~2
        IF_RANGE_OUT(PN11ax_tb->GILTFSize, 0, 2);
        //0~1
        IF_RANGE_OUT(PN11ax_tb->PE, 0, 1);
        //0~2
        IF_RANGE_OUT(PN11ax_tb->PE_Type, 0, 2);
        //0~63
        IF_RANGE_OUT(PN11ax_tb->BSScolor, 0, 63);
        //0~127
        IF_RANGE_OUT(PN11ax_tb->TXOP, 0, 127);
        //0~1
        IF_RANGE_OUT(PN11ax_tb->Doppler, 0, 1);
        //1~2
        IF_RANGE_OUT(PN11ax_tb->Midamble_Periodicity, 1, 2);

        s32Err = WT_ERR_CODE_OK;
    } while (0);

    return s32Err;
}

static int AxCommonParamValid(GenWaveWifiStruct_API *pnParameters)
{
    s32 s32Err = WT_ERR_CODE_OK;
    PNSettingBase *base = (PNSettingBase *)pnParameters;
    do
    {
        switch (base->bandwidth)
        {
        case 10:
        case 20:
        case 40:
        case 80:
        case 160:
        case 320:
            break;
        default:
            s32Err = WT_ERR_CODE_BANDWIDTH_ERROR;
            break;
        }

        if (s32Err)
        {
            break;
        }

        switch (base->standard)
        {
        case WT_DEMOD_11AX_20M:
        case WT_DEMOD_11AX_40M:
        case WT_DEMOD_11AX_80M:
        case WT_DEMOD_11AX_160M:
        case WT_DEMOD_11AX_80_80M:
        case WT_DEMOD_11AX_160_160M:
            switch (base->subType)
            {
            case HE_SU_PPDU:
            case HE_EXTEND_PPDU:
                s32Err = AxCommonParamValid_SU(&pnParameters->PN11ax_SU);
                break;
            case HE_MU_PPDU:
                s32Err = AxCommonParamValid_MU(&pnParameters->PN11ax_MU);
                break;
            case HE_TB_PPDU:
                s32Err = AxCommonParamValid_TB(&pnParameters->PN11ax_TB);
                break;
            default:
                break;
            }
            break;
        default:
            break;
        }

    } while (0);

    return  s32Err;
}

static int AxMUCommonBitsValid(int bandwidth, int *RuAllocMap, vector<int>&OverLappedBand, vector<int> &ToneList)
{
    int iRet = WT_ERR_CODE_OK;
    const int CBW_MaxTone[][2] = {
        { 192, 242 },
        { 200, 484 },
        { 208, 996 },
        { 216, 1992 }
    };
    int bandGroup = bandwidth / 20;
    int remainToneWide = CBW_MaxTone[0][1];
    switch (bandwidth)
    {
    case 40:
        remainToneWide = CBW_MaxTone[1][1];
        break;
    case 80:
        remainToneWide = CBW_MaxTone[2][1];
        break;
    case 160:
        remainToneWide = 2 * CBW_MaxTone[2][1];
        break;
    default:
        remainToneWide = CBW_MaxTone[0][1];
        break;
    }


    do
    {
        ToneList.clear();
        OverLappedBand.clear();
        OverLappedBand.assign(bandGroup, -1);

        for (int i = 0; i < bandGroup; i++)
        {
            int commonBits = RuAllocMap[i];
            int tmpToneWide = GetRuToneWide(commonBits);
            if (0 == tmpToneWide)
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
            OverLappedBand[i] = i;
            if (tmpToneWide == CBW_MaxTone[1][1])
            {
                i += 1;
            }
            else if (tmpToneWide == CBW_MaxTone[2][1])
            {
                i += 3;
            }
            else if (tmpToneWide == CBW_MaxTone[3][1])
            {
                i += 7;
            }
        }

        if (iRet)
        {
            break;
        }

        for (int i = 0; i < bandGroup; i++)
        {
            int commonBits = RuAllocMap[i];
            int tmpUserCnt = GetRUCoutPer20M(commonBits);
            int tmpToneWide = GetRuToneWide(commonBits);
            if (OverLappedBand[i] < 0)
            {
                continue;
            }
            ToneList.push_back(tmpToneWide);

            remainToneWide -= tmpToneWide;
            if (remainToneWide < 0)
            {
                iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                break;
            }
        }

        if (iRet)
        {
            break;
        }

        for (int i = 0; i < bandGroup; i++)
        {
            if (OverLappedBand[i] < 0)
            {
                continue;
            }
            if (200 == RuAllocMap[i])
            {
                if (bandGroup < 2)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
                if (1 == (i % 2) && RuAllocMap[i - 1] < 200 && -1 != OverLappedBand[i - 1])
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
                else if (0 == (i % 2))
                {
                    if (RuAllocMap[i + 1] != 200 && -1 != OverLappedBand[i + 1])
                    {
                        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                        break;
                    }
                    i += 1;
                }

            }
            if (208 == RuAllocMap[i])
            {
                if (bandGroup < 4)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
                if (1 == (i % 2) && RuAllocMap[i - 1] < 208 && -1 != OverLappedBand[i - 1])
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
                else if (0 == (i % 2))
                {
                    if (i + 3 > bandGroup)
                    {
                        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                        break;
                    }

                    for (int j = 0; j < 3; j++)
                    {
                        if (RuAllocMap[i + j] != 208 && -1 != OverLappedBand[i + j])
                        {
                            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                            break;
                        }
                    }
                    i += 3;
                }
            }

            if (216 == RuAllocMap[i])
            {
                if (bandGroup < 8)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
                if (1 == (i % 2) && RuAllocMap[i - 1] < 216 && -1 != OverLappedBand[i - 1])
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
                else if (0 == (i % 2))
                {
                    if (i + 7 > bandGroup)
                    {
                        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                        break;
                    }

                    for (int j = 0; j < 7; j++)
                    {
                        if (RuAllocMap[i + j] != 216 && -1 != OverLappedBand[i + j])
                        {
                            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                            break;
                        }
                    }
                    i += 7;
                }
            }
        }
    } while (0);

    return iRet;
}

static void Add_TBUser(vector<TF_RU> &RUIndexVector, int RUIndex, int id)
{
    int findIndex = -1;
    for (int i = 0; i < RUIndexVector.size(); i++)
    {
        if (RUIndex == RUIndexVector[i].RUIndex)
        {
            findIndex = i;
            break;
        }
    }

    if (-1 != findIndex)
    {
        RUIndexVector[findIndex].userList.push_back(id);
    }
    else
    {
        TF_RU tmp;
        tmp.RUIndex = RUIndex;
        tmp.userList.push_back(id);
        RUIndexVector.push_back(tmp);
    }
}

static void TF2TB_RU(GenWaveWifiStruct_API *TbPN, TriggerFrameSetting *tf)
{
#ifdef LINUX 
    int MaxSegCnt = (tf->TriggerAPType == 0? MAX_SEGMENT : BE_MAX_SEGMENT);
#else
#define MaxSegCnt (BE_MAX_SEGMENT)
#endif
    vector<TF_RU>RUIndexVector[MaxSegCnt];
    int tf_userIndex = 0;

    for (int i = 0; i < tf->TBUserNum; i++)
    {
        int segment = tf->TBSegment[i];
        Add_TBUser(RUIndexVector[segment], tf->TBRUIndex[i], i);
    }

    for (int segment = 0; segment < MaxSegCnt; segment++)
    {
        if(tf->TriggerAPType == 1)  //EHT
        {
            TbPN->PN11be_TB.RUNum[segment] = RUIndexVector[segment].size();
        }

        for (int m = 0; m < RUIndexVector[segment].size(); m++)
        {
            TB_MUMIMO_RU *RU = nullptr;
            if(tf->TriggerAPType == 0)  //HE
            {
                RU = &TbPN->PN11ax_TB.RU[segment][m];
            }
            else
            {
                RU = &TbPN->PN11be_TB.RU[segment][m];
            }
            
            RU->UserNum = 0;
            for (int n = 0; n < RUIndexVector[segment][m].userList.size(); n++)
            {
                TB_MUMIMO_User *User = &RU->User[n];
                User->AID = tf->TBAID[tf_userIndex];
                User->RuIndex = tf->TBRUIndex[tf_userIndex];
                User->SegmentIndex = tf->TBSegment[tf_userIndex];
                User->CodingType = tf->TBCoding[tf_userIndex];
                User->NSS = tf->TBSSCount[tf_userIndex];
                User->NSSStart = tf->TBSSStart[tf_userIndex];
                User->MCS = tf->TBMCS[tf_userIndex];
                User->DCM = tf->TBDCM[tf_userIndex];

                tf_userIndex++;
                RU->UserNum++;
            }
            if (RU->UserNum > 1)
            {
                if(tf->TriggerAPType == 0)  //HE
                {
                    TbPN->PN11ax_TB.TBMUMIMOFlag = 1;
                }
                else
                {
                    TbPN->PN11be_TB.TBMUMIMOFlag = 1;
                }
                
            }
        }
        if(tf->TriggerAPType == 0)  //HE
        {
            TbPN->PN11ax_TB.RUNum[segment] = RUIndexVector[segment].size();
        }
        else
        {
            TbPN->PN11be_TB.RUNum[segment] = RUIndexVector[segment].size();
        }
        
    }
}

int GetRuType(s32 AllocDate, s32 MartixLowIdx[])
{
    s32 j;
    s32 PeakIdx = -1;
    for (j = 0; j < 33; j++)
    {
        if (AllocDate == MartixLowIdx[j])
        {
            PeakIdx = j;
            break;
        }
    }
    return PeakIdx;
}

int GetRuToneWide(int commonBit)
{
    //"01110001"-->113-->(242-tone RU empty)
    //"01110010"-->114-->(484-tone RU with zero)
    //"01110011"-->115-->996-tone RU with zero
    //"01110100"-->116-->Reserved
    //"01111000"-->120-->Reserved
    //"11011000"-->216-->Reserved
    //"11100000"-->224-->Reserved
    const s32 ReservedCommbit[][2] = {
        { 113, 242 },
        { 114, 484 },
        { 115, 996 },
        { 116, 242 },
        { 120, 242 },
        { 224, 242 }
    };
    for (int i = 0; i < ARRAYSIZE(ReservedCommbit); i++)
    {
        if (ReservedCommbit[i][0] == commonBit)
        {
            return ReservedCommbit[i][1];
        }
    }
    const int maxRuSubfield = 33;
    const int max26ToneCnt = 9;
    s32 MartixLowIdx[maxRuSubfield] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 112, 128, 192, 200, 208, 216 };
    s32 MartixToneWide[maxRuSubfield][max26ToneCnt] =
    {
        { 26,26,26,26,26,26,26,26,26 },
        { 26,26,26,26,26,26,26,52, 0 },
        { 26,26,26,26,26,52,26,26, 0 },
        { 26,26,26,26,26,52,52, 0, 0 },
        { 26,26,52,26,26,26,26,26, 0 },
        { 26,26,52,26,26,26,52, 0, 0 },
        { 26,26,52,26,52,26,26, 0, 0 },
        { 26,26,52,26,52,52, 0, 0, 0 },
        { 52,26,26,26,26,26,26,26, 0 },
        { 52,26,26,26,26,26,52, 0, 0 },
        { 52,26,26,26,52,26,26, 0, 0 },
        { 52,26,26,26,52,52, 0, 0, 0 },
        { 52,52,26,26,26,26,26, 0, 0 },
        { 52,52,26,26,26,52, 0, 0, 0 },
        { 52,52,26,52,26,26, 0, 0, 0 },
        { 52,52,26,52,52, 0, 0, 0, 0 },
        { 52,52,106,0, 0, 0, 0, 0, 0 },
        { 106,52,52,0, 0, 0, 0, 0, 0 },
        { 26,26,26,26,26,106,0, 0, 0 },
        { 26,26,52,26,106,0, 0, 0, 0 },
        { 52,26,26,26,106,0, 0, 0, 0 },
        { 52,52,26,106,0, 0, 0, 0, 0 },
        { 106,26,26,26,26,26,0, 0, 0 },
        { 106,26,26,26,52,0, 0, 0, 0 },
        { 106,26,52,26,26,0, 0, 0, 0 },
        { 106,26,52,52,0, 0, 0, 0, 0 },
        { 106,106,0,0, 0, 0, 0, 0, 0 },
        { 52,52,52,52, 0, 0, 0, 0, 0 },
        { 106,26,106,0,0, 0, 0, 0, 0 },
        { 242,0, 0, 0, 0, 0, 0, 0, 0 },
        { 484,0, 0, 0, 0, 0, 0, 0, 0 },
        { 996,0, 0, 0, 0, 0, 0, 0, 0 },
        { 1992,0, 0, 0, 0, 0, 0, 0, 0 },
    };

    s32 ToneWide = 0;
    s32 PeakIdx = 0;

    do
    {
        PeakIdx = GetRuType(commonBit, MartixLowIdx);
        if (PeakIdx < 0)
        {
            break;
        }
        for (s32 i = 0; i < max26ToneCnt; i++)
        {
            if (MartixToneWide[PeakIdx][i] > 0)
            {
                ToneWide += MartixToneWide[PeakIdx][i];
            }
        }
    } while (0);

    return ToneWide;

}

int GetRUCoutPer20M(int commonBit)
{
    //"01110001"-->113-->(242-tone RU empty)
    //"01110010"-->114-->(484-tone RU with zero)
    //"01110011"-->115-->996-tone RU with zero
    //"01110100"-->116-->Reserved
    //"01111000"-->120-->Reserved
    //"11011000"-->216-->Reserved
    //"11100000"-->224-->Reserved
    const s32 ReservedCommbit[] = { 113,114,115,116,120,216,224 };
    for (int i = 0; i < ARRAYSIZE(ReservedCommbit); i++)
    {
        if (ReservedCommbit[i] == commonBit)
        {
            return 0;
        }
    }

    const int maxRuSubfield = 33;
    const int max26ToneCnt = 9;
    s32 MartixLowIdx[maxRuSubfield] = { 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,24,32,40,48,56,64,72,80,88,96,112,128,192,200,208,216 };
    s32 MartixToneWide[maxRuSubfield][max26ToneCnt] =
    {
        { 26,26,26,26,26,26,26,26,26 },
        { 26,26,26,26,26,26,26,52, 0 },
        { 26,26,26,26,26,52,26,26, 0 },
        { 26,26,26,26,26,52,52, 0, 0 },
        { 26,26,52,26,26,26,26,26, 0 },
        { 26,26,52,26,26,26,52, 0, 0 },
        { 26,26,52,26,52,26,26, 0, 0 },
        { 26,26,52,26,52,52, 0, 0, 0 },
        { 52,26,26,26,26,26,26,26, 0 },
        { 52,26,26,26,26,26,52, 0, 0 },
        { 52,26,26,26,52,26,26, 0, 0 },
        { 52,26,26,26,52,52, 0, 0, 0 },
        { 52,52,26,26,26,26,26, 0, 0 },
        { 52,52,26,26,26,52, 0, 0, 0 },
        { 52,52,26,52,26,26, 0, 0, 0 },
        { 52,52,26,52,52, 0, 0, 0, 0 },
        { 52,52,106,0, 0, 0, 0, 0, 0 },
        { 106,52,52,0, 0, 0, 0, 0, 0 },
        { 26,26,26,26,26,106,0, 0, 0 },
        { 26,26,52,26,106,0, 0, 0, 0 },
        { 52,26,26,26,106,0, 0, 0, 0 },
        { 52,52,26,106,0, 0, 0, 0, 0 },
        { 106,26,26,26,26,26,0, 0, 0 },
        { 106,26,26,26,52,0, 0, 0, 0 },
        { 106,26,52,26,26,0, 0, 0, 0 },
        { 106,26,52,52,0, 0, 0, 0, 0 },
        { 106,106,0,0, 0, 0, 0, 0, 0 },
        { 52,52,52,52, 0, 0, 0, 0, 0 },
        { 106,26,106,0,0, 0, 0, 0, 0 },
        { 242,0, 0, 0, 0, 0, 0, 0, 0 },
        { 484,0, 0, 0, 0, 0, 0, 0, 0 },
        { 996,0, 0, 0, 0, 0, 0, 0, 0 },
        { 1992,0, 0, 0, 0, 0, 0, 0, 0 }
    };


    s32  PeakIdx = 0;
    s32  UserCnt = 0;

    PeakIdx = GetRuType(commonBit, MartixLowIdx);
    do
    {
        if (PeakIdx < 0)
        {
            break;
        }
        for (s32 i = 0; i < max26ToneCnt; i++)
        {
            if (MartixToneWide[PeakIdx][i] > 0)
            {
                UserCnt++;
            }
        }
    } while (0);

    return UserCnt;
}

void WIFI_MPDU_PSDU(GenWaveWifiStruct_API *pnParameters)
{
    switch (pnParameters->commonParam.standard)
    {
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        if (VHT_SU_PPDU == pnParameters->commonParam.subType)
        {
            WIFI_PSDU *psdu = &pnParameters->PN11ac.psdu;
            if (0 == psdu->MPDUCount)
            {
                psdu->MPDUCount = 1;
                psdu->MPDULength[0] = psdu->psduLen;
            }
        }
        else
        {
            for (int i = 0; i < pnParameters->PN11ac_MUMIMO.UserNum; i++)
            {
                WIFI_PSDU *psdu = &pnParameters->PN11ac_MUMIMO.User[i].psdu;
                if (0 == psdu->MPDUCount)
                {
                    psdu->MPDUCount = 1;
                    psdu->MPDULength[0] = psdu->psduLen;
                }
            }
        }
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        if (HE_SU_PPDU == pnParameters->commonParam.subType || HE_EXTEND_PPDU == pnParameters->commonParam.subType)
        {
            WIFI_PSDU *psdu = &pnParameters->PN11ax_SU.psdu;
            if (0 == psdu->MPDUCount)
            {
                psdu->MPDUCount = 1;
                psdu->MPDULength[0] = psdu->psduLen;
            }
        }
        else if (HE_MU_PPDU == pnParameters->commonParam.subType)
        {
            for (int i = 0; i < AX_USER_COUNT; i++)
            {
                for (int j = 0; j < pnParameters->PN11ax_MU.RU[i].UserNum; j++)
                {
                    WIFI_PSDU *psdu = &pnParameters->PN11ax_MU.RU[i].User[j].psdu;
                    if (0 == psdu->MPDUCount)
                    {
                        psdu->MPDUCount = 1;
                        psdu->MPDULength[0] = psdu->psduLen;
                    }
                }
            }
        }
        else if (HE_TB_PPDU == pnParameters->commonParam.subType)
        {
            for (int segment = 0; segment < MAX_SEGMENT; segment++)
            {
                for (int i = 0; i < AX_USER_COUNT; i++)
                {
                    for (int j = 0; j < pnParameters->PN11ax_TB.RU[segment][i].UserNum; j++)
                    {
                        WIFI_PSDU *psdu = &pnParameters->PN11ax_TB.RU[segment][i].User[j].psdu;
                        if (0 == psdu->MPDUCount)
                        {
                            psdu->MPDUCount = 1;
                            psdu->MPDULength[0] = psdu->psduLen;
                        }
                    }
                }
            }
        }
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_160_160M:
        if (EHT_MU_PPDU == pnParameters->commonParam.subType)
        {
            for (int i = 0; i < BE_RU_COUNT; i++)
            {
                for (int j = 0; j < pnParameters->PN11be_MU.RU[i].UserNum; j++)
                {
                    WIFI_PSDU *psdu = &pnParameters->PN11be_MU.RU[i].User[j].psdu;
                    if (0 == psdu->MPDUCount)
                    {
                        psdu->MPDUCount = 1;
                        psdu->MPDULength[0] = psdu->psduLen;
                    }
                }
            }
        }
        else if (EHT_TB_PPDU == pnParameters->commonParam.subType)
        {
            for (int segment = 0; segment < BE_MAX_SEGMENT; segment++)
            {
                for (int i = 0; i < AX_USER_COUNT; i++)
                {
                    for (int j = 0; j < pnParameters->PN11be_TB.RU[segment][i].UserNum; j++)
                    {
                        WIFI_PSDU *psdu = &pnParameters->PN11be_TB.RU[segment][i].User[j].psdu;
                        if (0 == psdu->MPDUCount)
                        {
                            psdu->MPDUCount = 1;
                            psdu->MPDULength[0] = psdu->psduLen;
                        }
                    }
                }
            }
        }
        break;
    default:
        break;
    }
}

static bool IsWiFiDemo(const int Demo)
{
    bool isWiFi = true;
    switch (Demo)
    {
    case WT_DEMOD_11AG:
    case WT_DEMOD_11B:
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11N_40M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11AC_80_80M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        break;
    default:
        isWiFi = false;
        break;
    }
    return isWiFi;
}

static void GetTBDemoandBWfromTf(const TriggerFrameSetting *tf, int *TbDemo, int *TbBW)
{
    int APType = tf->TriggerAPType;

    *TbBW = pow(2, tf->TBULBW + 1) * 10;
    if(APType == 0)  //HE
    {
        *TbDemo = WT_DEMOD_11AX_20M + tf->TBULBW;
    }
    else
    {
        *TbDemo = WT_DEMOD_11BE_20M + tf->TBULBW;
        if(tf->TBULBWExt == 2 || tf->TBULBWExt == 3)
        {
            *TbBW = 320;
            *TbDemo = WT_DEMOD_11BE_320M;
        }
    }
}

int InstrumentHandle::AxTF2TB(const char *fileName, GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    unique_ptr<GenWaveWifiStruct_API>pnSetting(new (std::nothrow) GenWaveWifiStruct_API);
    GenWaveWifiStruct_API *TbPN = pnSetting.get();
    TriggerFrameSetting *tf = &pnParameters->TrigFrame;
    int demod = pnParameters->commonParam.standard;
    int ppdu = pnParameters->commonParam.subType;
    int smpRate = pnParameters->commonParam.samplingRate;
    bool isWiFi = true;
    WIFI_PSDU *psdu = nullptr;
    int TbDemo = WT_DEMOD_11AX_20M;
    int TbBW = 20;

    memset(TbPN, 0, sizeof(GenWaveWifiStruct_API));
    isWiFi = IsWiFiDemo(demod);

    if (isWiFi)
    {
        GetTBDemoandBWfromTf(tf, &TbDemo, &TbBW);
        TbPN->commonParam.standard = TbDemo;
        TbPN->commonParam.bandwidth = TbBW;
        //printf("tb demo = %d, bw=%d\n",TbDemo, TbBW);

        if(tf->TriggerAPType == 0)  //HE
        {
            GetDefaultWaveParameterWifi(TbPN->commonParam.standard, HE_TB_PPDU, TbPN);
            TbPN->commonParam.samplingRate = smpRate;

            TbPN->PN11ax_TB.STBC = tf->TBSTBC;
            TbPN->PN11ax_TB.NumLTFSymbols = tf->TBLTFSym;
            TbPN->PN11ax_TB.GILTFSize = tf->TBGILTF;
            TbPN->PN11ax_TB.PE = tf->TBPE;
            TbPN->PN11ax_TB.Doppler = tf->TBDoppler;
            TbPN->PN11ax_TB.PE_Type = tf->mPad;
            TbPN->PN11ax_TB.Midamble_Periodicity = tf->TBMidamble_Periodicity;
            TbPN->PN11ax_TB.Pre_FECfactor = tf->TBAfactor;
            TbPN->PN11ax_TB.LDPC_Extra = tf->TBLDPCExtra;
            TbPN->PN11ax_TB.PE_Disambiguity = tf->PE_Disambiguity;
            memcpy(TbPN->PN11ax_TB.Spatial_Reuse, tf->TBSR, sizeof(tf->TBSR));

            TF2TB_RU(TbPN, tf);

            for (int i = 0; i < AX_USER_COUNT; i++)
            {
                if (tf->TBSegment[i])
                {
                    psdu = &TbPN->PN11ax_TB.RU[1][i].User[0].psdu;
                }
                else
                {
                    psdu = &TbPN->PN11ax_TB.RU[0][i].User[0].psdu;
                }
                DefaultPSDU(psdu, 128);
            }
        }
        else    //EHT be
        {
            GetDefaultWaveParameterWifi(TbPN->commonParam.standard, EHT_TB_PPDU, TbPN);
            TbPN->commonParam.samplingRate = smpRate;

            TbPN->PN11be_TB.STBC = tf->TBSTBC;
            TbPN->PN11be_TB.NumLTFSymbols = tf->TBLTFSym;
            TbPN->PN11be_TB.GILTFSize = tf->TBGILTF;
            TbPN->PN11be_TB.PE = tf->TBPE;
            TbPN->PN11be_TB.Doppler = tf->TBDoppler;
            TbPN->PN11be_TB.PE_Type = tf->mPad;
            TbPN->PN11be_TB.Midamble_Periodicity = tf->TBMidamble_Periodicity;
            TbPN->PN11be_TB.Pre_FECfactor = tf->TBAfactor;
            TbPN->PN11be_TB.LDPC_Extra = tf->TBLDPCExtra;
            TbPN->PN11be_TB.PE_Disambiguity = tf->PE_Disambiguity;
            memcpy(TbPN->PN11be_TB.Spatial_Reuse, tf->TBSR, sizeof(tf->TBSR));

            TF2TB_RU(TbPN, tf);

            for (int i = 0; i < AX_USER_COUNT; i++)
            {
                if (tf->TBSegment[i])
                {
                    psdu = &TbPN->PN11be_TB.RU[1][i].User[0].psdu;
                }
                else
                {
                    psdu = &TbPN->PN11be_TB.RU[0][i].User[0].psdu;
                }
                DefaultPSDU(psdu, 128);
            }
        }

        iRet = CreateWaveWiFi(fileName, TbPN);
    }

    return iRet;
}

s32 InstrumentHandle::SaveTFParam(GenWaveWifiStruct_API *Pn, const char *fileName)
{
    int iRet = WT_ERR_CODE_OK;
    string TF_FileName = fileName;
    string TF_FileExt = ".tf";
    size_t pos = TF_FileName.find_last_of(".");
    if (pos != string::npos)
    {
        TF_FileName = TF_FileName.substr(0, pos);
    }
    TF_FileName += TF_FileExt;

    FILE *fp = fopen(TF_FileName.c_str(), "wb");
    if (fp)
    {
        fwrite((const void *)&Pn->TrigFrame, sizeof(TriggerFrameSetting), 1, fp);
        fclose(fp);
    }
    return WT_ERR_CODE_OK;
}

static int CalRecvWaveFormTimeOut_11ax(GenWaveWifiStruct_API *pnParameters, int &recvTimeOut)
{
    const int moreTime = 250; //ms
    int demod = GetStandardType(pnParameters->commonParam.standard);
    int PPDUFormat = pnParameters->commonParam.subType;

    if (IEEE802_11_ax == demod)
    {
        if (HE_SU_PPDU == PPDUFormat)
        {
            recvTimeOut += moreTime; //??
            recvTimeOut += pnParameters->PN11ax_SU.psdu.psduLen / 100;
            recvTimeOut += (8 - ((pnParameters->PN11ax_SU.MCS + 1) % 8)) * 10;
        }
        else if (HE_MU_PPDU == PPDUFormat)
        {
            for (int i = 0; i < AX_RU_COUNT; i++)
            {
                //low data rate or long psdu only
                for (int j = 0; j < pnParameters->PN11ax_MU.RU[i].UserNum; j++)
                {
                    MUMIMO_User *User = &pnParameters->PN11ax_MU.RU[i].User[i];
                    if (User->psdu.psduLen >= 1024 || User->MCS < 7)
                    {
                        recvTimeOut += moreTime; //??
                        recvTimeOut += User->psdu.psduLen / 100;
                        recvTimeOut += (8 - ((User->MCS + 1) % 8)) * 10;
                    }
                }
            }
        }
        else if (HE_TB_PPDU == PPDUFormat)
        {
            for (int segmentID = 0; segmentID < MAX_SEGMENT; segmentID++)
            {
                for (int i = 0; i < pnParameters->PN11ax_TB.RUNum[segmentID]; i++)
                {
                    for (int j = 0; j < pnParameters->PN11ax_TB.RU[segmentID][i].UserNum; j++)
                    {
                        TB_MUMIMO_User *User = &pnParameters->PN11ax_TB.RU[segmentID][i].User[j];
                        if (User->psdu.psduLen >= 1024 || User->MCS < 7)
                        {
                            recvTimeOut += moreTime; //??
                            recvTimeOut += User->psdu.psduLen / 100;
                            recvTimeOut += (8 - ((User->MCS + 1) % 8)) * 10;
                        }
                    }
                }
            }
        }
    }
    return 0;
}

static int CalRecvWaveFormTimeOut_11be(GenWaveWifiStruct_API *pnParameters, int &recvTimeOut)
{
    const int moreTime = 250; //ms
    int demod = GetStandardType(pnParameters->commonParam.standard);
    int PPDUFormat = pnParameters->commonParam.subType;

    if (IEEE802_11_be == demod)
    {
        if (EHT_MU_PPDU == PPDUFormat)
        {
            for (int i = 0; i < BE_RU_COUNT; i++)
            {
                //low data rate or long psdu only
                for (int j = 0; j < pnParameters->PN11be_MU.RU[i].UserNum; j++)
                {
                    MUMIMO_User *User = &pnParameters->PN11be_MU.RU[i].User[i];
                    if (User->psdu.psduLen >= 512 || User->MCS < 7)
                    {
                        recvTimeOut += moreTime; //??
                        recvTimeOut += User->psdu.psduLen / 100;
                        recvTimeOut += (8 - ((User->MCS + 1) % 8)) * 10;
                    }
                }
            }
        }
        else if (EHT_TB_PPDU == PPDUFormat)
        {
            for (int segmentID = 0; segmentID < BE_MAX_SEGMENT; segmentID++)
            {
                for (int i = 0; i < pnParameters->PN11be_TB.RUNum[segmentID]; i++)
                {
                    for (int j = 0; j < pnParameters->PN11be_TB.RU[segmentID][i].UserNum; j++)
                    {
                        TB_MUMIMO_User *User = &pnParameters->PN11be_TB.RU[segmentID][i].User[j];
                        if (User->psdu.psduLen >= 512 || User->MCS < 7)
                        {
                            recvTimeOut += moreTime; //??
                            recvTimeOut += User->psdu.psduLen / 100;
                            recvTimeOut += (8 - ((User->MCS + 1) % 8)) * 10;
                        }
                    }
                }
            }
        }
    }
    return 0;
}

static int CalRecvWaveFormTimeOut_other(GenWaveWifiStruct_API *pnParameters, int &recvTimeOut)
{
    int demod = GetStandardType(pnParameters->commonParam.standard);
    int PPDUFormat = pnParameters->commonParam.subType;
    switch (demod)
    {
    case IEEE802_11_b_g_DSSS:
        recvTimeOut += pnParameters->PN11b.psdu.psduLen / 20;
        break;
    case IEEE802_11_a_g_OFDM:
        recvTimeOut += pnParameters->PN11a.psdu.psduLen / 50;
        break;
    case IEEE802_11_n:
    {
        int NSS = (pnParameters->PN11n.MCS + 1) / 8;
        int MCS = (pnParameters->PN11n.MCS + 1) % 8;
        recvTimeOut += pnParameters->PN11n.psdu.psduLen / 100;
        for(int i = 0; i < pnParameters->PN11n.psdu.MPDUCount; i++)
        {
            recvTimeOut += pnParameters->PN11n.psdu.MPDULength[i];
        }
        recvTimeOut += NSS * 10;
        recvTimeOut += (8 - MCS) * 1000;
        break;
    }

    case IEEE802_11_ac:
        if (VHT_SU_PPDU == PPDUFormat)
        {
            int MCS = (pnParameters->PN11ac.MCS + 1) % 8;
            recvTimeOut += pnParameters->PN11ac.psdu.psduLen / 100;
            if (pnParameters->PN11ac.MCS < 7)
            {
                recvTimeOut += (8 - MCS) * 10;
            }
            recvTimeOut += pnParameters->PN11ac.NSS * 10;
        }

        break;
    }

    return 0;
}

static int CalRecvWaveFormTimeOut(GenWaveWifiStruct_API *pnParameters)
{
    int recvTimeOut = 20000;   //default recv timeout ms
    const int moreTime = 250; //ms
    int smpFactor = pnParameters->commonParam.samplingRate / (120 * MHz_API);
    int clockRate = pnParameters->commonParam.ClockRate;

    int demod = GetStandardType(pnParameters->commonParam.standard);
    int PPDUFormat = pnParameters->commonParam.subType;
    if (IEEE802_11_ax == demod)
    {
        CalRecvWaveFormTimeOut_11ax(pnParameters, recvTimeOut);
    }
    else if (IEEE802_11_be == demod)
    {
        CalRecvWaveFormTimeOut_11be(pnParameters, recvTimeOut);
    }
    else
    {
        CalRecvWaveFormTimeOut_other(pnParameters, recvTimeOut);
    }

    recvTimeOut += (smpFactor * moreTime * 4);
    recvTimeOut += (clockRate * moreTime * 4);

    return recvTimeOut;
}

#define OLD_BT_STRUCT_SIZE 1984
#define NEW_BT_STRUCT_SIZE 1832
int InstrumentHandle::WaveGenerator_Common(
    int cmd,
    const char *fileName,
    vector<ExchangeBuff> &pSendList,
    void *pnParameters)
{
    int streamCnt = 0;
    s32 DataLen[MAX_NUM_OF_CHNNEL] = { 0 };
    ExchangeBuff pstRecvBuff;
    pstRecvBuff.chpHead = (char *)DataLen;
    pstRecvBuff.buff_len = sizeof(DataLen);
    pstRecvBuff.data_len = sizeof(DataLen);

    unsigned int tmpSize = 0;
    int iRet = WT_ERR_CODE_OK;
    unsigned int sendTimeOut = 1000;
    unsigned int recvTimeOut = 3000;
    string waveFileName = fileName;
    string BwvFileExt = ".bwv";
    PNSettingBase *commonParam = static_cast<PNSettingBase*>(pnParameters);
    do
    {
        if (m_GenTimeout > 0)
        {
            recvTimeOut = m_GenTimeout;
        }
        else
        {
            switch (commonParam->standard)
            {
            case WT_DEMOD_BT:
            case WT_DEMOD_CW:
            case WT_DEMOD_LRWPAN_OFDM:
		    case WT_DEMOD_LRWPAN_OQPSK:
		    case WT_DEMOD_LRWPAN_FSK:
                break;
			case WT_DEMOD_GLE:
				recvTimeOut = 30000;
                break;
            default:
                if (IsAlg3GPPStandardType(commonParam->standard))
                {
                    // 临时的超时时间确定
                    recvTimeOut = 100000;
                    break;
                }
                recvTimeOut = CalRecvWaveFormTimeOut(static_cast<GenWaveWifiStruct_API*> (pnParameters));
            }
        }

        iRet = ProGetSpeciallyAckDataSize(0, cmd, &tmpSize, &pSendList[0], pSendList.size(), IOCONTROL_VSG, sendTimeOut, recvTimeOut);
        if (WT_ERR_CODE_OK != iRet)
        {
            break;
        }

        int rcvLength = ProRecv(IOCONTROL_VSG, (char *)&streamCnt, sizeof(streamCnt), recvTimeOut);
        if (rcvLength != sizeof(streamCnt))
        {
            iRet = WT_ERR_CODE_TIMEOUT;
            break;
        }
        if (0 >= streamCnt)
        {
            Logger::WriteLog(eumLogType_Warning, "Wave generator invalid stream count(%d)\n", streamCnt);
            iRet = WT_ERR_CODE_GENERATE_FAIL;
            break;
        }
        if (m_MutiPNExInfo)
        {
            m_MutiPNExInfo->streamCnt = streamCnt;
        }

        size_t pos = waveFileName.find_last_of(".");
        if (pos == string::npos)
        {
            waveFileName += BwvFileExt;
        }

        ENTER_LOCK(PN_OPERATE_LOCK);
        for (size_t i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
        {
            ClearPNInfor(&m_pnInfos[i]);
        }

        for (int streamIndex = 0; streamIndex < streamCnt; streamIndex++)
        {
            rcvLength = ProRecv(IOCONTROL_VSG, (char *)&DataLen[streamIndex], sizeof(DataLen[streamIndex]), recvTimeOut);
            if (rcvLength != sizeof(DataLen[streamIndex]))
            {
                iRet = WT_ERR_CODE_TIMEOUT;
                break;
            }

            if (DataLen[streamIndex] <= 0)
            {
                break;
            }


            rcvLength = ProRecv(IOCONTROL_VSG, (char *)m_pnInfos[streamIndex].data, DataLen[streamIndex], recvTimeOut);
            if (rcvLength != DataLen[streamIndex])
            {
                iRet = WT_ERR_CODE_TIMEOUT;
                break;
            }
        }

        switch (commonParam->standard)
        {
        case WT_DEMOD_BT:
        case WT_DEMOD_CW:
		case WT_DEMOD_GLE:
        case WT_DEMOD_LRWPAN_OFDM:
		case WT_DEMOD_LRWPAN_OQPSK:
		case WT_DEMOD_LRWPAN_FSK:
            break;
        default:
            GetRUSubCarrier();
            break;
        }
        InitWaveGenerator_WIFI_ChannelMode(m_WaveChannelModePathLoss);
        InitWaveGenerator_ExternSetting(m_WaveExternSetting.get(), m_WaveExternSettingLen);

        for (int streamIndex = 0; streamIndex < streamCnt; streamIndex++)
        {
            m_pnInfos[streamIndex].DataType = enDataFormat_Float64;
            m_pnInfos[streamIndex].SampleCount = DataLen[streamIndex] / sizeof(stPNDat);

            switch (commonParam->standard)
            {
            case WT_DEMOD_BT:
            {
                int structSize = 0;
                if (pSendList.size() > 0)
                {
                    structSize = pSendList.front().buff_len;
                }
                m_pnInfos[streamIndex].SampleFreq = commonParam->samplingRate;
                //InitWaveGenerator_BT(static_cast<GenWaveBtStruct*>(pnParameters));
                if (structSize == OLD_BT_STRUCT_SIZE)
                {
                    InitWaveGenerator_BT(static_cast<GenWaveBtStruct_API*>(pnParameters));
                }
                else if (structSize == NEW_BT_STRUCT_SIZE)
                {
                    InitWaveGenerator_BTV2(static_cast<GenWaveBtStructV2*>(pnParameters));
                }
            }
            break;
            case WT_DEMOD_CW:
                m_pnInfos[streamIndex].SampleFreq = commonParam->samplingRate;
                InitWaveGenerator_CW(static_cast<GenWaveCwStruct*>(pnParameters));
                break;
			case WT_DEMOD_GLE:
				m_pnInfos[streamIndex].SampleFreq = commonParam->samplingRate;
				InitWaveGenerator_GLE(static_cast<GenWaveGleStruct*>(pnParameters));
				GetSyncSource(pnParameters);
				break;
            case WT_DEMOD_LRWPAN_FSK:
            case WT_DEMOD_LRWPAN_OQPSK:
            case WT_DEMOD_LRWPAN_OFDM:
				m_pnInfos[streamIndex].SampleFreq = commonParam->samplingRate;
                InitWaveGenerator_WiSun(static_cast<GenWaveWisunStruct*>(pnParameters));
                break;
            default:
                if (IsAlg3GPPStandardType(commonParam->standard))
                {
                    m_pnInfos[streamIndex].SampleFreq = commonParam->samplingRate;
                    InitWaveGenerator_3GPP(pnParameters);
                    break;
                }
                m_pnInfos[streamIndex].SampleFreq = commonParam->samplingRate;
                InitWaveGenerator_WIFI(static_cast<GenWaveWifiStruct_API*>(pnParameters));
                InitWaveGenerator_WIFI_RUCarrier(m_WaveRUSubCarrier.get(), m_RUSubCarrierCnt);
                break;
            }
            m_pnInfos[streamIndex].ModType = commonParam->standard;

            m_pnInfos[streamIndex].initializedFlag = 1;
            m_pnInfos[streamIndex].sceneMode = 0;

            //
            m_pnInfos[streamIndex].freqOffset = commonParam->FreqErr;
            m_pnInfos[streamIndex].clockRate = commonParam->ClockRate;
            m_pnInfos[streamIndex].DC_Offset_I = commonParam->DCOffset_I;
            m_pnInfos[streamIndex].DC_Offset_Q = commonParam->DCOffset_Q;
            m_pnInfos[streamIndex].IQGainImb = commonParam->IQImbalanceAmp;
            m_pnInfos[streamIndex].IQPhaseImb = commonParam->IQImbalancePhase;

            m_pnInfos[streamIndex].MutiPNExInfo = m_MutiPNExInfo;

            iRet = CreatFileByPNFileInfo(&m_pnInfos[streamIndex], waveFileName.c_str(), streamIndex);
            if (iRet != WT_ERR_CODE_OK)
            {
                break;
            }
        }
        ResetPNFileExternSettingData();

        EXIT_LOCK(PN_OPERATE_LOCK);
    } while (0);
    return iRet;
}

int InstrumentHandle::WaveGeneratorCatenateFiles(const MutiPNCatenateInfo *catenateInfo)
{
    return CatenateMutiPnfiles(catenateInfo);
}

int InstrumentHandle::SetPNFileExternSettingData(void *data, int len)
{
    ResetPNFileExternSettingData();

    if (len > 0 && data)
    {
        m_WaveExternSetting.reset(new char[len]);
        m_WaveExternSettingLen = len;
        memcpy(m_WaveExternSetting.get(), data, len);
    }
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::ResetPNFileExternSettingData()
{
    m_WaveExternSetting.reset(nullptr);
    m_WaveExternSettingLen = 0;

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::SaveTBMUMIMOParam(GenWaveWifiStruct_API *pnParameters, const char *fileName)
{
    int iRet = WT_ERR_CODE_OK;
    string TbMUMIMOFileName = fileName;
    string TbMUMIMOFileExt = ".tb";
    size_t pos = TbMUMIMOFileName.find_last_of(".");
    bool localPN = false;
    if (pos != string::npos)
    {
        TbMUMIMOFileName = TbMUMIMOFileName.substr(0, pos);
    }
    TbMUMIMOFileName += TbMUMIMOFileExt;

    do
    {
#ifdef _DEBUG
        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
            localPN = (0 == flag ? false : true);
        }
#endif
        if (!localPN)
        {
            iRet = GetTBVariableParameter();
        }

        if (iRet)
        {
            break;
        }

        if (HE_TB_PPDU != pnParameters->commonParam.subType &&
            EHT_TB_PPDU != pnParameters->commonParam.subType)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        FILE *fp = fopen(TbMUMIMOFileName.c_str(), "wb");
        if (fp)
        {
            if (pnParameters->commonParam.standard >= WT_DEMOD_11AX_20M &&
                pnParameters->commonParam.standard <= WT_DEMOD_11AX_80_80M &&
                HE_TB_PPDU == pnParameters->commonParam.subType)
            {
                const int header[2] = { WAVE_CFG_BASE_HEAD, WAVE_CFG_SUB_TB_MUMIMO };
                fwrite((const void *)header, sizeof(header), 1, fp);
                fwrite(&pnParameters->PN11ax_TB, 1, sizeof(pnParameters->PN11ax_TB), fp);
                fwrite((const void *)&m_TbVarParam, sizeof(m_TbVarParam), 1, fp);
            }
            else if (pnParameters->commonParam.standard >= WT_DEMOD_11BE_20M &&
                pnParameters->commonParam.standard <= WT_DEMOD_11BE_160_160M &&
                EHT_TB_PPDU == pnParameters->commonParam.subType)
            {
                const int header[2] = { WAVE_CFG_BASE_HEAD, WAVE_CFG_SUB_EHT_TB };
                fwrite((const void *)header, sizeof(header), 1, fp);
                fwrite(&pnParameters->PN11be_TB, 1, sizeof(pnParameters->PN11be_TB), fp);
                fwrite((const void *)&m_TbVarParam, sizeof(m_TbVarParam), 1, fp);
            }
            else if (pnParameters->commonParam.standard >= WT_DEMOD_11AZ_20M &&
                pnParameters->commonParam.standard <= WT_DEMOD_11AZ_160M &&
                HE_TB_RANGING_PPDU == pnParameters->commonParam.subType)
            {
                const int header[2] = { WAVE_CFG_BASE_HEAD, WAVE_CFG_SUB_AZ_HE_TB };
                fwrite((const void *)header, sizeof(header), 1, fp);
                fwrite(&pnParameters->PN11az_TB, 1, sizeof(pnParameters->PN11az_TB), fp);
                fwrite((const void *)&m_TbVarParam, sizeof(m_TbVarParam), 1, fp);
            }
            fclose(fp);
        }
    } while (0);

    return iRet;
}

static int ParamValid(GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    switch (pnParameters->commonParam.standard)
    {
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11AX_160_160M:
        iRet = AxCommonParamValid(pnParameters);
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_160_160M:
        iRet = BeCommonParamValid(pnParameters);
        break;
    default:
        break;
    }
    return iRet;
}

static bool IsTbTimeDomainMultiUser(GenWaveWifiStruct_API *pnParameters)
{
    int demod = pnParameters->commonParam.standard;
    int ppdu = pnParameters->commonParam.subType;
    int TimeDomainMUFlag = false;
    switch (demod)
    {
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        if (HE_TB_PPDU == ppdu && pnParameters->PN11ax_TB.UserBuildUpMode)
        {
            TimeDomainMUFlag = true;
        }
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        if (EHT_TB_PPDU == ppdu && pnParameters->PN11be_TB.UserBuildUpMode)
        {
            TimeDomainMUFlag = true;
        }
        break;
    default:
        break;
    }

    return TimeDomainMUFlag;
}

int InstrumentHandle::WaveGeneratorWiFi(const char *fileName, GenWaveWifiStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    int iRet = WT_ERR_CODE_OK;
    bool isTF = false;
    do
    {
        iRet = ParamValid(pnParameters);
        if (iRet)
        {
            break;
        }

        if (WT_ERR_CODE_OK == isTriggerFrame(pnParameters))
        {
            iRet = AxTF2TB(fileName, pnParameters);
            isTF = true;
        }

        if (iRet)
        {
            break;
        }

        m_MutiPNExInfo = MutiPnExInfo;
        if(IsTbTimeDomainMultiUser(pnParameters))
        {
            iRet = CreateWaveWifiTbDomainMultUser(fileName, pnParameters);
        }
        else
        {
            iRet = CreateWaveWiFi(fileName, pnParameters);
        }
        
        if (iRet)
        {
            if (isTF)
            {
                remove(fileName);
            }
            break;
        }

    } while (0);

    return iRet;
}

static int CheckTbTimeDomainMUParamValid(GenWaveWifiStruct_API *pnParameters)
{
    //check the user power scale parameter,At least have one user must have a scale of 1
    int iRet = WT_ERR_CODE_OK;
    bool findScaleOne = false;
    int demod = pnParameters->commonParam.standard;
    if(WT_DEMOD_11AX_20M <= demod && demod <= WT_DEMOD_11AX_160_160M)
    {
        for(int Seg = 0; Seg < 2; Seg++)
        {
            for(int i = 0; i < pnParameters->PN11ax_TB.RUNum[Seg]; i++)
            {
                for(int j = 0; j < pnParameters->PN11ax_TB.RU[Seg][i].UserNum; j++)
                {
                    if(0 == UsualKit::DoubleCompare(pnParameters->PN11ax_TB.RU[Seg][i].User[j].PowerScale, 1.0))
                    {
                        findScaleOne = true;
                        break;
                    }
                }
            }
        }
    }
    else if(WT_DEMOD_11BE_20M <= demod && demod <= WT_DEMOD_11BE_160_160M)
    {
        for(int Seg = 0; Seg < 2; Seg++)
        {
            for(int i = 0; i < pnParameters->PN11be_TB.RUNum[Seg]; i++)
            {
                for(int j = 0; j < pnParameters->PN11be_TB.RU[Seg][i].UserNum; j++)
                {
                    if(0 == UsualKit::DoubleCompare(pnParameters->PN11be_TB.RU[Seg][i].User[j].PowerScale, 1.0))
                    {
                        findScaleOne = true;
                        break;
                    }
                }
            }
        }
    }
    
    if(findScaleOne == false)
    {
        iRet = WT_ERR_CODE_TB_TIME_DOMAIN_MU_SCALE_ERR;
    }
    return iRet;
}

static string GetTbDomainMultUserWaveFileName(const char *fileName, int SegIndex, int RuIndex, int UserIndex)
{
    string TbMUMIMOUserFileName = fileName;
    char TbMUMIMOFileUserIndex[56] = {0};
    sprintf(TbMUMIMOFileUserIndex, "-%d-%d-%d", SegIndex, RuIndex, UserIndex);
    size_t pos = TbMUMIMOUserFileName.find_last_of(".");

    if (pos != string::npos)
    {
        TbMUMIMOUserFileName = TbMUMIMOUserFileName.insert(pos, TbMUMIMOFileUserIndex);
    }
    return TbMUMIMOUserFileName;
}

int InstrumentHandle::CreateTbDomainMUPerUserWave(const char *fileName, GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = pnParameters->commonParam.standard;

    do
    {
        unique_ptr<GenWaveWifiStruct_API> pnSetting(new (std::nothrow) GenWaveWifiStruct_API);
        memcpy((char *)pnSetting.get(), (char *)pnParameters,sizeof(GenWaveWifiStruct_API));
        GenWaveWifiStruct_API *TmpPn = pnSetting.get();

        bool createFail = false;
        if(WT_DEMOD_11AX_20M <= demod && demod <= WT_DEMOD_11AX_160_160M)
        {
            for(int seg = 0; seg < MAX_SEGMENT && !createFail; seg++)
            {
                for(int i = 0; i < pnParameters->PN11ax_TB.RUNum[seg] && !createFail; i++)
                {
                    for(int j = 0; j < pnParameters->PN11ax_TB.RU[seg][i].UserNum; j++)
                    {
                        TmpPn->PN11ax_TB.RUNum[seg] = 1;
                        TmpPn->PN11ax_TB.RU[seg][0].UserNum = 1;
                        memcpy((char *)&(TmpPn->PN11ax_TB.RU[seg][0].User[0]), (char *)&(pnParameters->PN11ax_TB.RU[seg][i].User[j]), sizeof(TB_MUMIMO_User));
                        TmpPn->PN11ax_TB.RU[seg][0].User[0].NSSStart = 1;
                        TmpPn->commonParam.FreqErr = TmpPn->PN11ax_TB.RU[seg][0].User[0].FreqErr;

                        string TbMUMIMOUserFileName = GetTbDomainMultUserWaveFileName(fileName, seg, i, j);

                        iRet = CreateWaveWiFi(TbMUMIMOUserFileName.c_str(), TmpPn);
                        if(iRet)
                        {
                            createFail = true;
                            break;
                        }
                    }
                }
            }
        }
        else if(WT_DEMOD_11BE_20M <= demod && demod <= WT_DEMOD_11BE_160_160M)
        {
            for(int seg = 0; seg < BE_MAX_SEGMENT && !createFail; seg++)
            {
                for(int i = 0; i < pnParameters->PN11be_TB.RUNum[seg] && !createFail; i++)
                {
                    for(int j = 0; j < pnParameters->PN11be_TB.RU[seg][i].UserNum; j++)
                    {
                        TmpPn->PN11be_TB.RUNum[seg] = 1;
                        TmpPn->PN11be_TB.RU[seg][0].UserNum = 1;
                        memcpy((char *)&(TmpPn->PN11be_TB.RU[seg][0].User[0]), (char *)&(pnParameters->PN11be_TB.RU[seg][i].User[j]), sizeof(TB_MUMIMO_User));
                        TmpPn->PN11be_TB.RU[seg][0].User[0].NSSStart = 1;
                        TmpPn->commonParam.FreqErr = TmpPn->PN11ax_TB.RU[seg][0].User[0].FreqErr;

                        string TbMUMIMOUserFileName = GetTbDomainMultUserWaveFileName(fileName, seg, i, j);

                        iRet = CreateWaveWiFi(TbMUMIMOUserFileName.c_str(), TmpPn);
                        if(iRet)
                        {
                            createFail = true;
                            break;
                        }
                    }
                }
            }
        }
    } while (0);
    return iRet;
}

int InstrumentHandle::GetTbDomainSingleUserWave(const double PowerScale, string &TbMUMIMOUserFileName, vector<stPNFileInfo> &PnInfo, unique_ptr<stPNDat[]> Data[], int &MaxSampleCnt)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int testerIndex = 0;
        stPNFileInfo *tmpPnInfo = m_pnInfos;

        for (testerIndex = 0; testerIndex < WT_SUB_TESTER_INDEX_MAX; testerIndex++)
        {
            iRet = GetPNFileInfoFromFileMIMO(TbMUMIMOUserFileName.c_str(), (int)(m_vsaParam.SamplingFreq / MHz_API + 0.1), testerIndex, 0, &tmpPnInfo[testerIndex], PN_VSG_SCENE_MODE);

            if(iRet == WT_ERR_CODE_OK)
            {
                if(MaxSampleCnt < tmpPnInfo[testerIndex].SampleCount)
                {
                    MaxSampleCnt = tmpPnInfo[testerIndex].SampleCount;
                }

                if(testerIndex >= PnInfo.size()) //add
                {
                    stPNFileInfo Pn;
                    memcpy(&Pn, &tmpPnInfo[testerIndex],sizeof(stPNFileInfo));
                    int DataLen = tmpPnInfo[testerIndex].SampleCount;

                    if(Data[testerIndex] == nullptr)
                    {
                        Data[testerIndex].reset(new (std::nothrow) stPNDat[DataLen]);
                    }
                    Pn.data = Data[testerIndex].get();
                    for(int cnt = 0; cnt < DataLen; cnt++)
                    {
                        Pn.data[cnt].dReal = tmpPnInfo[testerIndex].data[cnt].dReal * PowerScale;
                        Pn.data[cnt].dImag = tmpPnInfo[testerIndex].data[cnt].dImag * PowerScale;
                    }
                    
                    PnInfo.push_back(Pn);
                }
                else    //modify
                {
                    if(tmpPnInfo[testerIndex].SampleCount > PnInfo[testerIndex].SampleCount)
                    {
                        for(int cnt = 0; cnt < PnInfo[testerIndex].SampleCount; cnt++)
                        {
                            tmpPnInfo[testerIndex].data[cnt].dReal = tmpPnInfo[testerIndex].data[cnt].dReal * PowerScale + PnInfo[testerIndex].data[cnt].dReal;
                            tmpPnInfo[testerIndex].data[cnt].dImag = tmpPnInfo[testerIndex].data[cnt].dImag * PowerScale + PnInfo[testerIndex].data[cnt].dImag;
                        }
                        for(int cnt = PnInfo[testerIndex].SampleCount; cnt < tmpPnInfo[testerIndex].SampleCount; cnt++)
                        {
                            tmpPnInfo[testerIndex].data[cnt].dReal = tmpPnInfo[testerIndex].data[cnt].dReal * PowerScale;
                            tmpPnInfo[testerIndex].data[cnt].dImag = tmpPnInfo[testerIndex].data[cnt].dImag * PowerScale;
                        }
                        PnInfo[testerIndex].SampleCount = tmpPnInfo[testerIndex].SampleCount;
                        Data[testerIndex].reset(new (std::nothrow) stPNDat[tmpPnInfo[testerIndex].SampleCount]);
                        PnInfo[testerIndex].data = Data[testerIndex].get();
                        for(int cnt = 0; cnt < tmpPnInfo[testerIndex].SampleCount; cnt++)
                        {
                            PnInfo[testerIndex].data[cnt].dReal= tmpPnInfo[testerIndex].data[cnt].dReal;
                            PnInfo[testerIndex].data[cnt].dImag = tmpPnInfo[testerIndex].data[cnt].dImag;
                        }
                    }
                    else
                    {
                        for(int cnt = 0; cnt < tmpPnInfo[testerIndex].SampleCount; cnt++)
                        {
                            PnInfo[testerIndex].data[cnt].dReal += tmpPnInfo[testerIndex].data[cnt].dReal * PowerScale;
                            PnInfo[testerIndex].data[cnt].dImag += tmpPnInfo[testerIndex].data[cnt].dImag * PowerScale;
                        }
                    }
                }
            }
            else
            {
                break;
            }
        }
        remove(TbMUMIMOUserFileName.c_str());
    } while (0);
    return iRet;
}

int InstrumentHandle::CombineTbDomainUserWave(const char *fileName, GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = pnParameters->commonParam.standard;
    do
    {
        int MaxSampleCnt = 0;
        vector<stPNFileInfo> PnInfo;
        unique_ptr<stPNDat[]>Data[WT_SUB_TESTER_INDEX_MAX] = {nullptr};
        if(WT_DEMOD_11AX_20M <= demod && demod <= WT_DEMOD_11AX_160_160M)
        {
            for(int seg = 0; seg < MAX_SEGMENT; seg++)
            {
                for(int i = 0; i < pnParameters->PN11ax_TB.RUNum[seg]; i++)
                {
                    for(int j = 0; j < pnParameters->PN11ax_TB.RU[seg][i].UserNum; j++)
                    {
                        string TbMUMIMOUserFileName = GetTbDomainMultUserWaveFileName(fileName, seg, i, j);
                        //printf("#####Ru %d user %d,powersacle=%lf######\n",i, j, pnParameters->PN11ax_TB.RU[seg][i].User[j].PowerScale);
                        iRet = GetTbDomainSingleUserWave(pnParameters->PN11ax_TB.RU[seg][i].User[j].PowerScale, TbMUMIMOUserFileName, PnInfo, Data, MaxSampleCnt);
                    }
                }
            }
        }
        else if(WT_DEMOD_11BE_20M <= demod && demod <= WT_DEMOD_11BE_160_160M)
        {
            for(int seg = 0; seg < BE_MAX_SEGMENT; seg++)
            {
                for(int i = 0; i < pnParameters->PN11be_TB.RUNum[seg]; i++)
                {
                    for(int j = 0; j < pnParameters->PN11be_TB.RU[seg][i].UserNum; j++)
                    {
                        string TbMUMIMOUserFileName = GetTbDomainMultUserWaveFileName(fileName, seg, i, j);
                        //printf("#####Ru %d user %d,powersacle=%lf######\n",i, j, pnParameters->PN11be_TB.RU[seg][i].User[j].PowerScale);
                        iRet = GetTbDomainSingleUserWave(pnParameters->PN11be_TB.RU[seg][i].User[j].PowerScale, TbMUMIMOUserFileName, PnInfo, Data, MaxSampleCnt);
                    }
                }
            }
        }

        int Size = PnInfo.size();

        InitWaveGenerator_WIFI_ChannelMode(m_WaveChannelModePathLoss);
        InitWaveGenerator_ExternSetting(m_WaveExternSetting.get(), m_WaveExternSettingLen);
        InitWaveGenerator_WIFI(static_cast<GenWaveWifiStruct_API*>(pnParameters));
        InitWaveGenerator_WIFI_RUCarrier(m_WaveRUSubCarrier.get(), m_RUSubCarrierCnt);

        for(int i = 0; i < Size; i++)
        {
            if (PnInfo[i].SampleCount <= 0)
            {
                break;
            }
            unique_ptr<stPNDat[]>TmpData =nullptr;
            if(PnInfo[i].SampleCount < MaxSampleCnt)
            {
                TmpData.reset(new (std::nothrow) stPNDat[MaxSampleCnt]);
                memset(TmpData.get(), 0, sizeof(stPNDat) * MaxSampleCnt);
                memcpy(TmpData.get(), (char *)PnInfo[i].data, sizeof(stPNDat) * PnInfo[i].SampleCount);
                PnInfo[i].data = TmpData.get();
                PnInfo[i].SampleCount = MaxSampleCnt;
            }

            PnInfo[i].SampleFreq = (int)(m_vsaParam.SamplingFreq / MHz_API + 0.1);
            if (PnInfo[i].data != nullptr)
            {
                iRet = CreatFileByPNFileInfo(&PnInfo[i], fileName, i);
                if (iRet != WT_ERR_CODE_OK)
                {
                    break;
                }
            }
        }
    } while (0);

    return iRet;
}

int InstrumentHandle::CreateWaveWifiTbDomainMultUser(const char *fileName, GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    int demod = pnParameters->commonParam.standard;
    do
    {
        iRet = CheckTbTimeDomainMUParamValid(pnParameters);
        if(iRet)
        {
            break;
        }

        iRet = CreateTbDomainMUPerUserWave(fileName, pnParameters);
        if(iRet)
        {
            break;
        }

        iRet = CombineTbDomainUserWave(fileName, pnParameters);
        if(iRet)
        {
            break;
        }
    } while (0);

    return iRet;
}

int InstrumentHandle::WaveGeneratorGLE(const char *fileName, GenWaveGleStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
	int iRet = WT_ERR_CODE_OK;
	vector<ExchangeBuff> pSendList;
	ExchangeBuff pstSendBuff;
	do
	{

#ifdef _DEBUG
		if (1)
		{
			int flag = 0;
			UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
			if (flag)
			{
				iRet = CreateWaveLocal(fileName, pnParameters);
				break;
			}
		}
#endif
		pstSendBuff.chpHead = (char *)pnParameters;
		pstSendBuff.buff_len = sizeof(GenWaveGleStruct);
		pstSendBuff.data_len = sizeof(GenWaveGleStruct);
		pSendList.push_back(pstSendBuff);

        m_MutiPNExInfo = MutiPnExInfo;
		iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_SLE, fileName, pSendList, pnParameters);
		if (iRet)
		{
			break;
		}

	} while (0);

	if (WT_ERR_CODE_OK == iRet)
	{
		SaveSleParam(pnParameters, fileName);
	}

	return iRet;
}


int InstrumentHandle::CreateWaveWiFi(const char *fileName, GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    vector<ExchangeBuff> pSendList;
    ExchangeBuff pstSendBuff;
    do
    {
        WIFI_MPDU_PSDU(pnParameters);

        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_SAVE_PN_STRUCT", &flag);
            if (flag)
            {
                PrintParam_PN obj(pnParameters);
                obj.RUN();
            }
        }
#ifdef _DEBUG
        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
            if (flag)
            {
                iRet = CreateWaveLocal(fileName, pnParameters);
                break;
            }
        }
#endif

        pstSendBuff.chpHead = (char *)pnParameters;
        pstSendBuff.buff_len = sizeof(GenWaveWifiStruct_API);
        pstSendBuff.data_len = sizeof(GenWaveWifiStruct_API);
        pSendList.push_back(pstSendBuff);

        if (pnParameters->ExtendParam)
        {
            ExtParamHdr *ext_hdr = (ExtParamHdr *)pnParameters->ExtendParam;
            pstSendBuff.chpHead = (char *)ext_hdr;
            pstSendBuff.buff_len = sizeof(ExtParamHdr) + ext_hdr->Len;
            pstSendBuff.data_len = pstSendBuff.buff_len;
            pSendList.push_back(pstSendBuff);
            while (ext_hdr->Field.Next)
            {
                ext_hdr = (ExtParamHdr *)ext_hdr->Field.Next;
                pstSendBuff.chpHead = (char *)ext_hdr;
                pstSendBuff.buff_len = sizeof(ExtParamHdr) + ext_hdr->Len;
                pstSendBuff.data_len = pstSendBuff.buff_len;
                pSendList.push_back(pstSendBuff);
            }
        }

        iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_WIFI, fileName, pSendList, pnParameters);
        if (iRet)
        {
            break;
        }

    } while (0);

    if (WT_ERR_CODE_OK == iRet)
    {
        SaveTbTfParam(pnParameters, fileName);
    }

    return iRet;
}

s32 InstrumentHandle::SaveTbTfParam(GenWaveWifiStruct_API * pnParameters, const char * fileName)
{
    int iRet = WT_ERR_CODE_OK;

    int demod = pnParameters->commonParam.standard;
    int ppdu = pnParameters->commonParam.subType;

    if (((WT_DEMOD_11AX_20M <= demod && WT_DEMOD_11AX_80_80M >= demod) && (HE_TB_PPDU == ppdu))
            || ((WT_DEMOD_11BE_20M <= demod && WT_DEMOD_11BE_160_160M >= demod) && (EHT_TB_PPDU == ppdu))
            || ((WT_DEMOD_11AZ_20M <= demod && WT_DEMOD_11AZ_160M >= demod) && (HE_TB_RANGING_PPDU == ppdu)))
    {
        //printf("###api  save tb file####\n");
        iRet = SaveTBMUMIMOParam(pnParameters, fileName);
    }

    if (false/*WT_ERR_CODE_OK == isTriggerFrame(pnParameters)*/)
    {
        iRet = SaveTFParam(pnParameters, fileName);
    }

    return iRet;
}

int InstrumentHandle::SaveSLEParam(GenWaveGleStruct* pnParameters, const char* fileName)
{
	int iRet = WT_ERR_CODE_OK;
	string TbMUMIMOFileName = fileName;
	string TbMUMIMOFileExt = ".sle";
	size_t pos = TbMUMIMOFileName.find_last_of(".");
	if (pos != string::npos)
	{
		TbMUMIMOFileName = TbMUMIMOFileName.substr(0, pos);
	}
	TbMUMIMOFileName += TbMUMIMOFileExt;
	do
	{
		FILE* fp = fopen(TbMUMIMOFileName.c_str(), "wb");
		if (fp)
		{
			if (pnParameters->commonParam.standard == WT_DEMOD_GLE)
			{
				fwrite(pnParameters, 1, sizeof(GenWaveGleStruct), fp);
			}
			fclose(fp);
		}
	} while (0);
	return iRet;
}

s32 InstrumentHandle::SaveSleParam(GenWaveGleStruct* pnParameters, const char* fileName)
{
	int iRet = WT_ERR_CODE_OK;
	int demod = pnParameters->commonParam.standard;
	if (demod == WT_DEMOD_GLE)
	{
		iRet = SaveSLEParam(pnParameters, fileName);
	}
	return iRet;
}

// 字节加密
inline unsigned char SimpleEncryp(unsigned char c)
{
    return ~(((c & 0x93) & 0x93) | (c & 0x6c));
}

int InstrumentHandle::Save3GPPRefParam(Alg_3GPP_WaveGenType *pnParameters, const char *fileName)
{
    int iRet = WT_ERR_CODE_OK;
    string RefFileName = fileName;
    string RefFileExt = ".3gppref";
    size_t pos = RefFileName.find_last_of(".");
    if (pos != string::npos)
    {
        RefFileName = RefFileName.substr(0, pos);
    }

    RefFileName += RefFileExt;

    do
    {
        GetExtParamFromFW(pnParameters->CommonParam.standard, &m_3GPPWaveCreateAlzParam);

        int header[2] = {WAVE_CFG_BASE_HEAD, WAVE_CFG_SUB_LTE};
        switch (pnParameters->CommonParam.standard)
        {
        case ALG_3GPP_STD_WCDMA:
            header[1] = WAVE_CFG_SUB_WCDMA;
            break;
        case ALG_3GPP_STD_5G:
            header[1] = WAVE_CFG_SUB_NR;
            break;
        case ALG_3GPP_STD_4G:
            header[1] = WAVE_CFG_SUB_LTE;
            break;
        case ALG_3GPP_STD_NB_IOT:
            header[1] = WAVE_CFG_SUB_NB_IOT;
            break;
        default:
            break;
        }

#ifdef LINUX
        json root;
        cellular_param_to_json(root, m_3GPPWaveCreateAlzParam);

        std::string json_str = root.dump();
        int length = json_str.length();
        char* serialized_string = strdup(json_str.c_str());
        WTLog::Instance().WriteLog(LOG_DEBUG, "Save 3GPP Ref:\n%s\n", serialized_string);

#if 1
        // 加密, 注意必须与SCPI加载过程匹配使用
        unsigned char *p = (unsigned char *)serialized_string;
        for (int i = 0; i < length; ++i)
        {
            *(p + i) = SimpleEncryp(*(p + i));
        }
#endif

        FILE *fp = fopen(RefFileName.c_str(), "wb");
        if (fp)
        {
            fwrite((const void *)header, sizeof(header), 1, fp);
            fwrite((const void *)(&pnParameters->CommonParam.standard), sizeof(pnParameters->CommonParam.standard), 1, fp);
            fwrite((const void *)(&length), sizeof(length), 1, fp);
            fwrite(serialized_string, length, 1, fp);
            fclose(fp);
        }

        free(serialized_string);  // 释放内存
#endif
    } while (0);

    return iRet;
}

int InstrumentHandle::WaveGeneratorCW(const char *fileName, GenWaveCwStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    int iRet = WT_ERR_CODE_OK;
    vector<ExchangeBuff> pSendList;
    ExchangeBuff pstSendBuff;
    do
    {

#ifdef _DEBUG
        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
            if (flag)
            {
                iRet = CreateWaveLocal(fileName, pnParameters);
                break;
            }
        }
#endif
        pstSendBuff.chpHead = (char *)pnParameters;
        pstSendBuff.buff_len = sizeof(GenWaveCwStruct);
        pstSendBuff.data_len = sizeof(GenWaveCwStruct);
        pSendList.push_back(pstSendBuff);

        m_MutiPNExInfo = MutiPnExInfo;
        iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_CW, fileName, pSendList, pnParameters);
        if (iRet)
        {
            break;
        }

    } while (0);

    return iRet;
}

int InstrumentHandle::WaveGenerator3GPP(const char *fileName, void *pnParameters, int Paramlen)
{
    int iRet = WT_ERR_CODE_OK;
    vector<ExchangeBuff> pSendList;
    ExchangeBuff pstSendBuff;
    do
    {
        pstSendBuff.chpHead = (char *)pnParameters;
        pstSendBuff.buff_len = Paramlen;
        pstSendBuff.data_len = Paramlen;
        pSendList.push_back(pstSendBuff);

        m_MutiPNExInfo = nullptr;
        iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_3GPP, fileName, pSendList, pnParameters);
        if (iRet)
        {
            break;
        }

    } while (0);

    if (WT_ERR_CODE_OK == iRet)
    {
        Save3GPPRefParam((Alg_3GPP_WaveGenType *)pnParameters, fileName);
    }

    return iRet;
}

int InstrumentHandle::WaveGeneratorBT(const char *fileName, GenWaveBtStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    int iRet = WT_ERR_CODE_OK;
    vector<ExchangeBuff> pSendList;
    ExchangeBuff pstSendBuff;
    do
    {

#ifdef _DEBUG
        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
            if (flag)
            {
                iRet = CreateWaveLocal(fileName, pnParameters);
                break;
            }
        }
#endif
        pstSendBuff.chpHead = (char *)pnParameters;
        pstSendBuff.buff_len = sizeof(GenWaveBtStruct_API);
        pstSendBuff.data_len = sizeof(GenWaveBtStruct_API);
        pSendList.push_back(pstSendBuff);

        m_MutiPNExInfo = MutiPnExInfo;
        iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_BLUETOOTH, fileName, pSendList, pnParameters);
        if (iRet)
        {
            break;
        }

    } while (0);

    return iRet;
}

int InstrumentHandle::WaveGeneratorBTV2(const char *fileName, GenWaveBtStructV2 *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    int iRet = WT_ERR_CODE_OK;
    vector<ExchangeBuff> pSendList;
    ExchangeBuff pstSendBuff;
    do
    {

#ifdef _DEBUG
        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
            if (flag)
            {
                iRet = CreateWaveLocal(fileName, pnParameters);
                break;
            }
        }
#endif
        pstSendBuff.chpHead = (char *)pnParameters;
        pstSendBuff.buff_len = sizeof(GenWaveBtStructV2);
        pstSendBuff.data_len = sizeof(GenWaveBtStructV2);
        pSendList.push_back(pstSendBuff);
        //printf("sizeof lisst = %d, Extend = %p\n", (int)pSendList.size(),pnParameters->ExtendParam);
        if (pnParameters->ExtendParam)
        {
            ExtParamHdr *ext_hdr = (ExtParamHdr *)pnParameters->ExtendParam;
            pstSendBuff.chpHead = (char *)ext_hdr;
            pstSendBuff.buff_len = sizeof(ExtParamHdr) + ext_hdr->Len;
            pstSendBuff.data_len = pstSendBuff.buff_len;
            pSendList.push_back(pstSendBuff);
            while (ext_hdr->Field.Next)
            {
                ext_hdr = (ExtParamHdr *)ext_hdr->Field.Next;
                pstSendBuff.chpHead = (char *)ext_hdr;
                pstSendBuff.buff_len = sizeof(ExtParamHdr) + ext_hdr->Len;
                pstSendBuff.data_len = pstSendBuff.buff_len;
                pSendList.push_back(pstSendBuff);
            }
        }

        m_MutiPNExInfo = MutiPnExInfo;
        iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_BLUETOOTH, fileName, pSendList, pnParameters);
        if (iRet)
        {
            break;
        }

    } while (0);


    return iRet;
}

int InstrumentHandle::WaveGeneratorWiSun(const char* fileName, GenWaveWisunStruct* pnParameters)
{
    int iRet = WT_ERR_CODE_OK;
    vector<ExchangeBuff> pSendList;
    ExchangeBuff pstSendBuff;
    do
    {

#ifdef _DEBUG
        if (1)
        {
            int flag = 0;
            UsualKit::config_int_var("api_cfg.json", "WT_GEN_PN_LOCAL", &flag);
            if (flag)
            {
                iRet = CreateWaveLocal(fileName, pnParameters);
                break;
            }
        }
#endif
        pstSendBuff.chpHead = (char*)pnParameters;
        pstSendBuff.buff_len = sizeof(GenWaveWisunStruct);
        pstSendBuff.data_len = sizeof(GenWaveWisunStruct);
        pSendList.push_back(pstSendBuff);
        m_MutiPNExInfo = nullptr;

        iRet = WaveGenerator_Common(CMD_GEN_VSG_FILE_WI_SUN, fileName, pSendList, pnParameters);
        if (iRet)
        {
            break;
        }

    } while (0);

    return iRet;
}

int UserCntInRU(int *userInRU, int AIndex, int BIndex, int Cnt)
{
    int iRet = WT_ERR_CODE_OK;
    for (int i = 0; i < Cnt; i++)
    {
        if (userInRU[AIndex + i - 1] != userInRU[BIndex + i - 1])
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }
    }

    return iRet;
}

int InstrumentHandle::AxValidCommonBit(int demod, int *commonBits, int *userInRU, int *RUCnt, int *center26RU, int *RUPos)
{
    int iRet = WT_ERR_CODE_OK;
    int bandWidth = 20;
    vector<int>OverLappedBand;
    vector<int>ToneList;
    int bandGroup = 1;
    const int ExtraAllc[2] = { 2, 6 };
    int TotalRU = 0;
    int RUDuplication = 0;

    int RUIndex[AX_USER_COUNT] = { -1 };
    int SkipCnt = 0;
    int InsertCnt = 0;

    do
    {
        center26RU[0] = -1;
        center26RU[1] = -1;
        for (int i = 0; i < AX_USER_COUNT; i++)
        {
            RUIndex[i] = -1;
        }

        switch (demod)
        {
        case WT_DEMOD_11AX_20M:
            bandWidth = 20;
            break;
        case WT_DEMOD_11AX_40M:
            bandWidth = 40;
            break;
        case WT_DEMOD_11AX_80M:
            bandWidth = 80;
            break;
        case WT_DEMOD_11AX_160M:
        case WT_DEMOD_11AX_80_80M:
            bandWidth = 160;
            break;
        case WT_DEMOD_11AX_160_160M:
            bandWidth = 320;
            break;
        default:
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        if (iRet)
        {
            break;
        }

        iRet = AxMUCommonBitsValid(bandWidth, commonBits, OverLappedBand, ToneList);
        if (iRet)
        {
            break;
        }

        bandGroup = (bandWidth / 20);

        for (int i = 0; i < bandGroup; i++)
        {
            int tmpRUCnt = GetRUCoutPer20M(commonBits[i]);
            RUDuplication += tmpRUCnt;
            if (OverLappedBand[i] < 0)
            {
                if (i > 0)
                {
                    if (commonBits[i] != commonBits[i - 1])
                    {
                        iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                        break;
                    }
                    if (nullptr != userInRU)
                    {
                        iRet = UserCntInRU(userInRU, RUDuplication - tmpRUCnt, RUDuplication, tmpRUCnt);
                        if (iRet)
                        {
                            break;
                        }
                    }
                    SkipCnt += 1;
                }
            }
            else
            {
                for (int m = TotalRU; m < TotalRU + tmpRUCnt; m++)
                {
                    RUIndex[m] = m;
                }

                TotalRU += tmpRUCnt;
            }

            if (bandGroup >= 4 && commonBits[i] < 208)
            {
                if (i + 1 == ExtraAllc[0])
                {
                    RUIndex[TotalRU] = TotalRU;

                    center26RU[0] = TotalRU;
                    TotalRU += 1;
                }

                if (i + 1 == ExtraAllc[1])
                {
                    RUIndex[TotalRU] = TotalRU;

                    center26RU[1] = TotalRU;
                    TotalRU += 1;
                }
            }
        }
    } while (0);

    if (WT_ERR_CODE_OK == iRet)
    {
        *RUCnt = TotalRU;
        if (RUPos)
        {
            memcpy(RUPos, RUIndex, sizeof(RUIndex));
        }
    }
    return iRet;
}

int InstrumentHandle::SetWaveGeneratorTimeout(int Time)
{
    m_GenTimeout = Time;
    return WT_ERR_CODE_OK;
}