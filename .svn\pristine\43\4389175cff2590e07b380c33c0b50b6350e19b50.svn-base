#include "ethtype.h"

#include <string>
#include <cstring>
#include <arpa/inet.h>
#include "wterror.h"
#include "wtlog.h"

using namespace std;

#define CONVERT_LEN 16        //每16字节翻转一次数据

PacketFormat::PacketFormat()
{
//    Reset();
}

void PacketFormat::Reset()
{
    memset(this, 0, sizeof(PacketFormat));
    type = WThtons(ETH_TYPE);
    version = 1;
    Reserved = WThtonl(PACKET_RESERVED_CODE);
    CarrierNum = 1;
    AntennaNum = 1;
    ChanID = 0;
    SampleUnion.SampleS.SampleLen = 256;
    SampleUnion.Res = WThtons(SampleUnion.Res);
}

void PacketFormat::SetMac(uint8_t *MacArray, int Type)
{
    memcpy(Type == SRC_MAC ? src : dst, MacArray, 6);
}

void PacketFormat::SetIQData(void *PnData, int BitsCnt, int SampleCnt)
{
    IQ16Bit *pData = reinterpret_cast<IQ16Bit *>(PnData);
    int i = 0;
    unsigned int SignedBit = 0x1u << (BitsCnt - 1);
    unsigned int Mask = (0x1u << BitsCnt) - 1;
    for (i = 0; i < SampleCnt; i++, pData++)
    {
        if ((*pData)[0] & SignedBit)
        {
            (*pData)[0] &= Mask;
        }
        IQData[i][0] = WThtons((*pData)[0]);
        
        if ((*pData)[1] & SignedBit)
        {
            (*pData)[1] &= Mask;
        }
        IQData[i][1] = WThtons((*pData)[1]);
#if DIG_DEBUG_LOG_LEVEL > 4
        if (WTntohs(SubFrameCounter) < 1)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "ChanID%d IQData[%d]=[%#x, %#x],  pData[%d]=[%#x, %#x]\n",
                   ChanID, i, IQData[i][0], IQData[i][1], i, (*pData)[0], (*pData)[1]);
        }
#endif
    }
    if(i < PACKET_SAMPLE_CNT)
    {
        memset(IQData[i], 0, (PACKET_SAMPLE_CNT - i) * sizeof(IQ16Bit));
    }
}

void PacketFormat::GetIQData(void *PnData, int BitsCnt, int SamelpCnt)
{
    IQ16Bit *pData = reinterpret_cast<IQ16Bit *>(PnData);
    SamelpCnt = (SamelpCnt <= PACKET_SAMPLE_CNT) ? SamelpCnt : PACKET_SAMPLE_CNT;
    unsigned int SignedBit = 0x1u << (BitsCnt - 1);
    unsigned int Mask = ~((0x1u << BitsCnt) - 1);
    for (int i = 0; i < SamelpCnt; i++, pData++)
    {
        (*pData)[0] = WTntohs(IQData[i][0]);
        if ((*pData)[0] & SignedBit)
        {
            (*pData)[0] |= Mask;
        }

        (*pData)[1] = WTntohs(IQData[i][1]);
        if ((*pData)[1] & SignedBit)
        {
            (*pData)[1] |= Mask;
        }
#if DIG_DEBUG_LOG_LEVEL > 4
        if (WTntohs(SubFrameCounter) < 1)
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "IQData[%d]=[%#x, %#x],  pData[%d]=[%#x, %#x]\n", i, IQData[i][0], IQData[i][1], i, (*pData)[0], (*pData)[1]);
        }
#endif
    }
}

void PacketFormat::SetPacketTotal(uint32_t PacketTotal)
{
#if DIG_DEBUG_LOG_LEVEL > 1
    WTLog::Instance().WriteLog(LOG_DEBUG, "SetPacketTotal = %u\n", PacketTotal);
#endif
    SampleCounter = WThtons(PacketTotal);
}

void PacketFormat::SetPacketId(uint32_t PacketId)
{
    SubFrameCounter = WThtons(PacketId);
}

void PacketFormat::SetChannelId(uint8_t Channel)
{
    ChanID &= (~0x7F);
    ChanID |= Channel & 0x7F;
}

int PacketFormat::CheckPacket()
{
    return WT_OK;
    if (Reserved == WThtonl(0X163F0000) && type == WThtons(ETH_TYPE))
    {
        return WT_OK;
    }
    else
    {
        return WT_ARG_FRAME_TYPE_ERROR;
    }
}

void PacketFormat::Convert(int Convert, int Pos, int Len)
{
    if (!Convert)
    {
        return;
    }

    for (int i = Pos; i < Len; i += CONVERT_LEN)
    {
        char *pos = reinterpret_cast<char *>(this) + i;
        for (int j = 0; j < CONVERT_LEN / 2; j++)
        {
            swap(pos[j], pos[CONVERT_LEN - j - 1]);
        }
    }
    src[0] &= ~0x01;
}

int PacketFormat::GetChanId(int Convert)
{
    return (Convert ? this->dst[0] : this->ChanID) & 0x7f;
}

void PacketFormat::PrintfIQ()
{
#if DIG_DEBUG_LOG_LEVEL > 4
    for (int i = 0; i < PACKET_SAMPLE_CNT; i++)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "ChanID%d IQData[%d]=[%#x, %#x]\n", ChanID, i, IQData[i][0], IQData[i][1]);  
    }
#endif
}
