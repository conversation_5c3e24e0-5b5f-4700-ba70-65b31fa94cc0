/**
 * @file scpi_3gpp_common.h
 * @brief 蜂窝公共头文件
 * @version 0.1
 * @date 2024-09-20
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef SCPI_3GPP_COMMON_H_
#define SCPI_3GPP_COMMON_H_

#include <algorithm>
#include <initializer_list>
#include <vector>
#include <tuple>
#include <type_traits> // 用于std::is_same

#include "scpi/scpi.h"
#include "wterror.h"

namespace cellular
{
    namespace alz
    {

        struct Command
        {
            int min_;
            int max_;
            int value_;
            Command(int min, int max) : min_(min), max_(max), value_(0) {}
        };

        // 通用模板函数定义
        template <typename T>
        inline bool GetScpiValue(scpi_t *context, T *value, bool mandatory);

        // 针对int类型特化
        template <>
        inline bool GetScpiValue<int>(scpi_t *context, int *value, bool mandatory)
        {
            return SCPI_ParamInt(context, value, mandatory);
        }

        // 针对double类型特化
        template <>
        inline bool GetScpiValue<double>(scpi_t *context, double *value, bool mandatory)
        {
            return SCPI_ParamDouble(context, value, mandatory);
        }

        // 检查范围并赋值
        template <typename FieldType, typename MinType, typename MaxType>
        int SetVsaValueInRange(scpi_t *context,
                               FieldType &field,
                               MinType min,
                               MaxType max)
        {
            int iRet = WT_OK;

            do
            {
                FieldType value = 0;
                if (!GetScpiValue(context, &value, true))
                {
                    iRet = WT_3GPP_GET_VALUE_FAILED;
                    break;
                }

                if (value < static_cast<FieldType>(min) ||
                    value > static_cast<FieldType>(max))
                {
                    iRet = WT_3GPP_VALUE_OUT_OF_RANGE;
                    break;
                }

                field = value;
            } while (0);

            return iRet;
        }

        // 检查指定值并赋值
        template <typename FieldType>
        int SetVsaValueFromSpecified(scpi_t *context,
                                     FieldType &field,
                                     std::initializer_list<FieldType> validValues)
        {
            int iRet = WT_OK;

            do
            {
                FieldType value = 0;
                if (!GetScpiValue(context, &value, true))
                {
                    iRet = WT_3GPP_GET_VALUE_FAILED;
                    break;
                }

                // 判断 value 是否在指定的值列表中
                if (std::find(validValues.begin(), validValues.end(), value) == validValues.end())
                {
                    iRet = WT_3GPP_VALUE_OUT_OF_RANGE;
                    break;
                }

                field = value;
            } while (0);

            return iRet;
        }

        int GetScpiCommandNumbers(scpi_t *context,
                                  std::vector<cellular::alz::Command> &params);

    } // namespace alz
} // namespace cellular

namespace cellular
{
    namespace method
    {

        template <typename T, size_t N>
        inline constexpr size_t arraySize(const T (&)[N]) noexcept
        {
            return N;
        }

    } // namespace method
} // namespace cellular

#endif // SCPI_3GPP_COMMON_H_
