//*****************************************************************************
//  File: algreset.cpp
//  重置算法处理内存溢出问题
//  Data: 2024.03.03
//*****************************************************************************
//*****************************************************************************
#include "algreset.h"

#include <iostream>
#include "conf.h"
#include "threadpool.h"
#include <thread>
#include <sstream>
#include <sys/time.h>
#include "alg/includeAll.h"
#include "../../general/wtlog.h"
#include <malloc.h>

#define Pout(n) #n << "=" << n << ","

AlgReset &AlgReset::Instance(void)
{
    static AlgReset AlgRe;
    return AlgRe;
}

int AlgReset::CompareMemoryThreshold(const SysMemoryType &SysStatus)
{
    if (SysStatus.MemTotal)
    {
        double MemUsedPer = (double)(SysStatus.MemUsed + SysStatus.SwapUsed) / SysStatus.MemTotal;

#if ALG_RESET_DEBUG
        std::cout << Pout(MemUsedPer) << Pout(m_SysThreshold.MemErrorPer) << Pout(m_SysThreshold.MemWarnningPer) << std::endl;
        std::cout << Pout(SysStatus.AlgMemUsed) << Pout(m_SysThreshold.AlgUsedError) << Pout(m_SysThreshold.AlgUsedWarnning) << std::endl;
#endif

        if (m_SysThreshold.ErrorEnbale)
        {
            if (SysStatus.AlgMemUsed > m_SysThreshold.AlgUsedError)
//            if (MemUsedPer > m_SysThreshold.MemErrorPer || SysStatus.AlgMemUsed > m_SysThreshold.AlgUsedError)
            {
                std::ostringstream sin;
                sin << Pout(MemUsedPer) << Pout(m_SysThreshold.MemErrorPer)
                << Pout(SysStatus.MemUsed) << Pout(SysStatus.SwapUsed)
                << Pout(SysStatus.AlgMemUsed) << Pout(m_SysThreshold.AlgUsedError);
                WTLog::Instance().LOGSYSOPT(sin.str());
#if ALG_RESET_DEBUG
                std::cout << sin.str() << std::endl;
#endif
                return true;
            }
        }

        if (m_SysThreshold.WarnningEnbale)
        {
            if (SysStatus.AlgMemUsed > m_SysThreshold.AlgUsedWarnning)
//            if (MemUsedPer > m_SysThreshold.MemWarnningPer || SysStatus.AlgMemUsed > m_SysThreshold.AlgUsedWarnning)
            {
                if (m_AlgIdle)
                {
                    struct timeval CurTime;
                    gettimeofday(&CurTime, NULL);
#if ALG_RESET_DEBUG
                    std::cout << Pout(CurTime.tv_sec) << Pout(m_LastUseAlgTime.tv_sec) << Pout(m_SysThreshold.IdleTime) << std::endl;
#endif
                    if (CurTime.tv_sec - m_LastUseAlgTime.tv_sec > m_SysThreshold.IdleTime)
                    {
                        std::ostringstream sin;
                        sin << Pout(MemUsedPer) << Pout(m_SysThreshold.MemWarnningPer)
                            << Pout(SysStatus.MemUsed) << Pout(SysStatus.SwapUsed)
                            << Pout(SysStatus.AlgMemUsed) << Pout(m_SysThreshold.AlgUsedWarnning)
                            << Pout(CurTime.tv_sec) << Pout(m_LastUseAlgTime.tv_sec) << Pout(m_SysThreshold.IdleTime);
                        WTLog::Instance().LOGSYSOPT(sin.str());
#if ALG_RESET_DEBUG
                        std::cout << sin.str() << std::endl;
#endif
                        return true;
                    }
                }
            } 
        }
    }
    return false;
}

int AlgReset::CheckAndResetAlg(const SysMemoryType &SysStatus)
{
    if (CompareMemoryThreshold(SysStatus))
    {
        struct timeval CurTime;
        gettimeofday(&CurTime, NULL);
        if (CurTime.tv_sec - m_LastResetTime.tv_sec < m_SysThreshold.MinResetGap)
        {
#if ALG_RESET_DEBUG
            std::cout << Pout(CurTime.tv_sec) << Pout(m_LastResetTime.tv_sec) << Pout(m_SysThreshold.MinResetGap) << std::endl;
#endif
            return WT_OK;
        }

        do
        {
            if (m_ResetRunning == false)
            {
#if ALG_RESET_DEBUG
                std::cout << "Readly ResetAlgMemory" << std::endl;
#endif
                WTLog::Instance().LOGSYSOPT("Readly ResetAlgMemory");

                std::unique_lock<std::mutex> lck(m_ResetMutex, std::defer_lock);
                if (lck.try_lock())
                {
                    std::thread(&AlgReset::ResetAlgMemory, this).detach();
                    break;
                }
            }
#if AlG_LOCK_DEBUG
#define PoutRwlock(n) Pout(m_Rwlock.__data.n)\
            std::cout << PoutRwlock(__nr_readers) << PoutRwlock(__nr_readers_queued) << PoutRwlock(__readers_wakeup) << std::endl;\
            std::cout << PoutRwlock(__writer) << PoutRwlock(__nr_writers_queued) << PoutRwlock(__writer_wakeup) << std::endl;\
            std::unique_lock<std::mutex> ThreadLock(m_ThreadLockCountMutex);\ // m_ThreadLockCount对象操作锁
            for (const auto &ThreadLockCount : m_ThreadLockCount)\
            {\
                std::cout << "Thread[" << ThreadLockCount.first << "] = " << ThreadLockCount.second << std::endl;\
            }
#endif
        } while (0);
    }
    return WT_OK;
}

int AlgReset::ForceResetAlg()
{
#if ALG_RESET_DEBUG
    std::cout << "Force ResetAlgMemory" << std::endl;
#endif
    WTLog::Instance().LOGSYSOPT("Force ResetAlgMemory");
    std::thread(&AlgReset::ResetAlgMemory, this).detach();
    return WT_OK;
}

int AlgReset::RegisterAnalysisHandle(void *Obj, const AnlysisHandle &Handle)
{
    std::unique_lock<std::mutex> Lock(m_HandleMutex); // AlgReset对象操作锁
    m_ResetHandle.emplace(Obj, Handle);
    Lock.unlock();

    int ThreadId = pthread_self();
    std::unique_lock<std::mutex> ThreadLock(m_ThreadLockCountMutex); // m_ThreadLockCount对象操作锁
    if (!m_ThreadLockCount.count(ThreadId))
    {
        m_ThreadLockCount.emplace(pthread_self(), 0);
    }
    return WT_OK;
}

int AlgReset::DeleteAnalysisHandle(void *Obj)
{
    std::unique_lock<std::mutex> Lock(m_HandleMutex); // AlgReset对象操作锁
    m_ResetHandle.erase(Obj);

    int ThreadId = pthread_self();
    std::unique_lock<std::mutex> ThreadLock(m_ThreadLockCountMutex); // m_ThreadLockCount对象操作锁
    if (m_ThreadLockCount.count(ThreadId) && m_ThreadLockCount[ThreadId] == 0)
    {
        m_ThreadLockCount.erase(pthread_self());
    }
    return WT_OK;
}

int AlgReset::AlgGetReaderLock()
{
    int Ret = WT_OK;
    m_AlgIdle = false;
    int ThreadId = pthread_self();

    std::unique_lock<std::mutex> ThreadLock(m_ThreadLockCountMutex); // m_ThreadLockCount对象操作锁
    if (!m_ThreadLockCount.count(ThreadId))
    {
        m_ThreadLockCount.emplace(ThreadId, 0);
    }
    if (++m_ThreadLockCount[ThreadId] == 1)
    {
        ThreadLock.unlock();
        Ret = pthread_rwlock_rdlock(&m_Rwlock);
    }

#if AlG_LOCK_DEBUG
    std::cout << "GetThreadLock " << ThreadId << " Count " << m_ThreadLockCount[ThreadId] << std::endl;
#endif
    return Ret;
}

int AlgReset::AlgGetWirterLock()
{
    int Ret = WT_OK;
    int ThreadId = pthread_self();

    std::unique_lock<std::mutex> ThreadLock(m_ThreadLockCountMutex); // m_ThreadLockCount对象操作锁
    if (!m_ThreadLockCount.count(ThreadId))
    {
        m_ThreadLockCount.emplace(ThreadId, 0);
    }
    if (++m_ThreadLockCount[ThreadId] == 1)
    {
        ThreadLock.unlock();
        Ret = pthread_rwlock_wrlock(&m_Rwlock);
    }
    return Ret;
}

int AlgReset::AlgGetReaderLockDebug(std::string File, int Line)
{
    auto Ret = AlgGetReaderLock();
#if AlG_LOCK_DEBUG
    std::cout << File << " line " << Line << ": GetReaderLock" << std::endl;
#else
    (void)File;
    (void)Line;
#endif
    return Ret;
}

int AlgReset::AlgReleaseLock()
{
    int ThreadId = pthread_self();
    int Ret = WT_OK;
    do
    {
        std::unique_lock<std::mutex> ThreadLock(m_ThreadLockCountMutex); // m_ThreadLockCount对象操作锁
        if (m_ThreadLockCount.count(ThreadId))
        {
            if (--m_ThreadLockCount[ThreadId] > 0)
            {
#if AlG_LOCK_DEBUG
                std::cout << "ReleaseThreadLock " << ThreadId << " Count " << m_ThreadLockCount[ThreadId] << std::endl;
#endif
                break;
            }
        }
        ThreadLock.unlock();

        pthread_rwlock_unlock(&m_Rwlock);
#if AlG_LOCK_DEBUG
        std::cout << "ReleaseThreadLock " << ThreadId << " Count 0" << std::endl;
#endif
        if (m_Rwlock.__data.__readers == 0)
        {
            gettimeofday(&m_LastUseAlgTime, NULL);
            m_AlgIdle = true;
        }
    } while (0);

    return Ret;
}

int AlgReset::AlgReleaseLockDebug(std::string File, int Line)
{
    auto Ret = AlgReleaseLock();
#if AlG_LOCK_DEBUG
    std::cout << File << " line " << Line << ": ReleaseReaderLock" << std::endl;
#else
    (void)File;
    (void)Line;
#endif
    return Ret;
}

AlgReset::AlgReset()
{
    pthread_rwlockattr_t attr;
    pthread_rwlockattr_init(&attr);
    // 设置成请求写锁优先
    pthread_rwlockattr_setkind_np(&attr, PTHREAD_RWLOCK_PREFER_WRITER_NONRECURSIVE_NP);
    // PTHREAD_RWLOCK_PREFER_WRITER_NONRECURSIVE_NP 写锁优先
    // PTHREAD_RWLOCK_PREFER_READER_NP//读锁优先
    pthread_rwlock_init(&m_Rwlock, &attr);
    m_ResetRunning = false;
    memset(&m_SysThreshold, 0, sizeof(m_SysThreshold));

    WTConf Conf(WTConf::GetDir() + "/algreset.conf");
    Conf.GetItemVal("WarnningEnbale", m_SysThreshold.WarnningEnbale);
    Conf.GetItemVal("MemWarnning", m_SysThreshold.MemWarnning);
    Conf.GetItemVal("SwapWarnning", m_SysThreshold.SwapWarnning);
    Conf.GetItemVal("AlgUsedWarnning", m_SysThreshold.AlgUsedWarnning);
    Conf.GetItemVal("IdleTime", m_SysThreshold.IdleTime);

    Conf.GetItemVal("ErrorEnbale", m_SysThreshold.ErrorEnbale);
    Conf.GetItemVal("MemError", m_SysThreshold.MemError);
    Conf.GetItemVal("SwapError", m_SysThreshold.SwapError);
    Conf.GetItemVal("AlgUsedError", m_SysThreshold.AlgUsedError);

    Conf.GetItemVal("MemTotal", m_SysThreshold.MemTotal);
    Conf.GetItemVal("SwapTotal", m_SysThreshold.SwapTotal);
    Conf.GetItemVal("MinResetGap", m_SysThreshold.MinResetGap);

    if (!m_SysThreshold.MemTotal )
    {
        m_SysThreshold.WarnningEnbale = false;
        m_SysThreshold.ErrorEnbale = false;
    }
    else if (!m_SysThreshold.MemWarnning && !m_SysThreshold.SwapWarnning)
    {
        m_SysThreshold.WarnningEnbale = false;
    }
    else if (!m_SysThreshold.MemError && !m_SysThreshold.SwapError)
    {
        m_SysThreshold.ErrorEnbale = false;
    }

    if (m_SysThreshold.ErrorEnbale)
    {
        m_SysThreshold.MemErrorPer = ((double)m_SysThreshold.MemError + m_SysThreshold.SwapError) / m_SysThreshold.MemTotal;
    }
    if (m_SysThreshold.WarnningEnbale)
    {
        m_SysThreshold.MemWarnningPer = ((double)m_SysThreshold.MemWarnning + m_SysThreshold.SwapWarnning) / m_SysThreshold.MemTotal;
    }

#if ALG_RESET_DEBUG
#define PoutSys(n) Pout(m_SysThreshold.n)\
    std::cout << PoutSys(ErrorEnbale) << PoutSys(MemError) << PoutSys(SwapError) << std::endl;\
    std::cout << PoutSys(WarnningEnbale) << PoutSys(MemWarnning) << PoutSys(SwapWarnning) << PoutSys(IdleTime) << std::endl;\
    std::cout << PoutSys(MemTotal) << PoutSys(SwapTotal) << std::endl;
#endif

    gettimeofday(&m_LastUseAlgTime, NULL);
    gettimeofday(&m_LastResetTime, NULL);
    m_AlgIdle = true;
}

AlgReset::~AlgReset()
{
    pthread_rwlock_destroy(&m_Rwlock);
}

int AlgReset::ResetAlgMemory()
{
    std::unique_lock<std::mutex> Lock(m_ResetMutex); // AlgReset对象操作锁
    if (m_ResetRunning == false)
    {
        m_ResetRunning = true;
        AlgGetWirterLock(); // 算法对象操作锁

        struct timeval tpstart;
        gettimeofday(&tpstart, NULL);

#if ALG_RESET_DEBUG
        std::cout << "ResetAlgMemory running..." << std::endl;
#endif
        std::unique_lock<std::mutex> Lock(m_HandleMutex); // AlgReset对象操作锁
        MIMO_WT_Algorithm_free_all();
#if ALG_RESET_DEBUG
        std::cout << "MIMO_WT_Algorithm_free_all" << std::endl;
#endif

#if ALG_RESET_DEBUG
        std::cout << "m_ResetHandle size=" << m_ResetHandle.size() << std::endl;
#endif
        for (const auto &ResetHandle : m_ResetHandle)
        {
#if ALG_RESET_DEBUG
            std::cout << "ResetHandle ptr=" << ResetHandle.first << std::endl;
#endif
            ResetHandle.second();
        }
        malloc_trim(0);
        
        AlgReleaseLock();
        m_ResetRunning = false;

        gettimeofday(&m_LastResetTime, NULL);
        double timeuse = (m_LastResetTime.tv_sec - tpstart.tv_sec) * 1e6 + (m_LastResetTime.tv_usec - tpstart.tv_usec);

        // int MemTotal, MemUsed, AlgUsed;
        // GetMemUsed(MemTotal, MemUsed, AlgUsed);

        std::stringstream ss;
        // ss << "ResetAlgMemory Use time " << timeuse << "us," << Pout(MemTotal) << Pout(MemUsed)<< Pout(AlgUsed);
        ss << "ResetAlgMemory Use time " << timeuse << "us";
        WTLog::Instance().LOGSYSOPT(ss.str());
        //std::cout << "ResetAlgMemory Use time" = << timeuse << "us" << endl;
#if ALG_RESET_DEBUG
        std::cout << ss.str() << std::endl;
        std::cout << "ResetAlgMemory finish" << std::endl;
#endif
    }
    return 0;
}

void AlgReset::GetMemUsed(int &MemTotal, int &MemUsed, int &AlgUsed)
{
    std::ifstream FStream("/proc/meminfo");
    if (!FStream.is_open())
    {
        return;
    }

    std::string Line;
    std::string::size_type Begin;

    int MemFree = 0;
    MemTotal = 0;
    auto GetValue = [&](std::string Item, int &Obj) -> bool
    {
        std::string::size_type Begin, End;
        Begin = Line.find(Item);
        std::string Str;
        if (Begin != std::string::npos)
        {
            End = Line.find_first_not_of(": ", Begin + Item.length());
            if (End != std::string::npos)
            {
                Str = Line.substr(End, (Line.find_last_not_of(" #\t\f\v\n\r") - End + 1));
            }
            Obj = stoul(Str, nullptr, 0);
            return true;
        }
        return false;
    };

    while (getline(FStream, Line))
    {
        // 跳过空行和注释行
        Begin = Line.find_first_not_of(' ');
        if (Line.empty() || Line[Begin] == '#')
        {
            continue;
        }

        if (!MemTotal && GetValue("MemTotal:", MemTotal))
        {
            continue;
        }
        else if (!MemFree && GetValue("MemFree:", MemFree))
        {
            continue;
        }

        if (MemTotal && MemFree)
        {
            break;
        }
    }
    FStream.close();

    MemUsed = MemTotal - MemFree;
    AlgUsed = MIMO_WT_Algorithm_malloc_total_size() / 1024; // bytes to KiB
}

AlgReset::ReaderLock::ReaderLock()
{
    AlgReset::Instance().AlgGetReaderLock();
}

AlgReset::ReaderLock::~ReaderLock()
{
#if AlG_LOCK_DEBUG
    std::cout << "ReaderLock ReleaseReaderLock" << std::endl;
#endif
    AlgReset::Instance().AlgReleaseLock();
}

AlgReset::ReaderLock::ReaderLock(std::string File, int Line)
{
    AlgReset::Instance().AlgGetReaderLock();
#if AlG_LOCK_DEBUG
    std::cout << File << " line " << Line << ": ReaderLock" << std::endl;
#else
    (void)File;
    (void)Line;
#endif
}
