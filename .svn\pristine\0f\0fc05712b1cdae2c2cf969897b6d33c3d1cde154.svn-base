//*****************************************************************************
//File: wtbusilib.h
//Describe:业务板硬件驱动处理层
//Author：yuanyongchun
//Date: 2021.01.18
//*****************************************************************************
#ifndef _WT_BUSI_LIB_H_
#define _WT_BUSI_LIB_H_
#include "../general/devlib/ioctlcmd.h"
#include "wtdefine.h"

//获取基带板FPGA信息 
int WT_GetBusiFpgaInfo(int DataLength, void *arg, struct dev_unit *pdev);

//获取业务板槽位号
int WT_GetBusiBoardSlot(int DataLength,  void  *arg, struct dev_unit *pdev);

//读取业务板完成状态并清零
int WT_ReadCompleteClrStatus(int DataLength, void *arg, struct dev_unit *pdev);

//设置链路工作模式（主从模式/SISO/MIMO）
int WT_SetUnitModWorkMode(int DataLength, void *arg, struct dev_unit *pdev);

//清除工作模式
int WT_ClearWorkMode(int DataLength, void *arg, struct dev_unit *pdev);

//AD5611
int WT_WriteBusiOCXOCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadBusiOCXOCode(int DataLength, void *arg, struct dev_unit *pdev);

//AD7682 Channel
int WT_ReadBusiAD7682Channel(int DataLength, void *arg, struct dev_unit *pdev);
//AD7689 Channel
int WT_ReadSwAD7689Channel(int DataLength, void *arg, struct dev_unit *pdev);

//ADF4106
int WT_WriteADF4106Code(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadADF4106Code(int DataLength, void *arg, struct dev_unit *pdev);

//HM7044
int WT_WriteHM7044Code(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadHM7044Code(int DataLength, void *arg, struct dev_unit *pdev);

//LTC5594
int WT_WriteLTC5594Code(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadLTC5594Code(int DataLength, void *arg, struct dev_unit *pdev);

//LMX2594
int WT_WriteLMX2594Code(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadLMX2594Code(int DataLength, void *arg, struct dev_unit *pdev);

//LMX2820
int WT_WriteLMX2820Code(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadLMX2820Code(int DataLength, void *arg, struct dev_unit *pdev);

//LMXHMC833
int WT_WriteHMC833Code(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadHMC833Code(int DataLength, void *arg, struct dev_unit *pdev);

//设置共本振模式
int WT_SetLOComMode(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetLOComMode_WT418va(int DataLength, void *arg, struct dev_unit *pdev);
//获取共本振模式
int WT_GetLOComMode(int DataLength, void *arg, struct dev_unit *pdev);
int WT_GetLOComMode_WT418va(int DataLength, void *arg, struct dev_unit *pdev);
//AD7091
int wt_WriteBusiAD7091Reg(int Addr, int Data, struct dev_unit *pdev);

int wt_ReadBusiAD7091Reg(int Addr, int *Data, struct dev_unit *pdev);

int WT_WriteBusiAD7091Reg(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadBusiAD7091Reg(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadBusiChannelVoltValue(int DataLength, void *arg, struct dev_unit *pdev);

//DDS AD9912
int WT_WriteDDSCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadDDSCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetDDSFreqChannel(int DataLength, void *arg, struct dev_unit *pdev);

//AttAndShift
int WT_WriteAttAndShiftCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadAttAndShiftCode(int DataLength, void *arg, struct dev_unit *pdev);

//LoBoardShitf
int wt_WriteLoBoardShitfCode(struct dev_unit *pdev, long long Data);

int wt_ReadLoBoardShitfCode(struct dev_unit *pdev, long long* Data);

int WT_WriteLoBoardShitfCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadLoBoardShitfCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetLoBoardHMC705(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetLoBoardLoopFilter(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetLoBoardFreqChannel(int DataLength, void *arg, struct dev_unit *pdev);

//ADC9684 or DAC9142
int WT_WriteAdcOrDacCode(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadAdcOrDacCode(int DataLength, void *arg, struct dev_unit *pdev);

//重采样
int WT_SetResample(int DataLength, void *arg, struct dev_unit *pdev);

//IQ交换
int WT_SetIQSwitch(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetRFPA(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetRFLNA(int DataLength, void *arg, struct dev_unit *pdev);

//BAND
int WT_SetRXBand(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetTXBand(int DataLength, void *arg, struct dev_unit *pdev);

//ATT
int wt_WriteATT(struct dev_unit *pdev, int ATTId, int ATTCode);

int wt_ReadATT(struct dev_unit *pdev, int ATTId, int *ATTCode);

int WT_WriteATT(int DataLength, void *arg, struct dev_unit *pdev);

int WT_ReadATT(int DataLength, void *arg, struct dev_unit *pdev);

//XDMA
int WT_GetXdmaStatus(int DataLength, void *arg, struct dev_unit *pdev);

//FPGA Upgrade
int WT_BaseFpgaEarse(int DataLength, void *arg, struct dev_unit *pdev);

int WT_BaseFpgaUpgrade(int DataLength, void *arg, struct dev_unit *pdev);

int WT_BaseFpgaWriteOnePage(int DataLength, void *arg, struct dev_unit *pdev);

int WT_FPGAReload(int DataLength, void *arg, struct dev_unit *pdev);

int WT_SetFlash4ByteMode(int DataLength, void *arg, struct dev_unit *pdev);

//ATT CAL
int WT_SetATTCalConfig(int DataLength, void *arg, struct dev_unit *pdev);
//VSA
int WT_VSAStart(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSAStop(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSASetTrig(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSAGetTrig(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSAGetStatus(int DataLength, void *arg, struct dev_unit *pdev);
//数字TRIGGER电平
int WT_SetRXTrigLevelDigital(int DataLength, void *arg, struct dev_unit *pdev);
int WT_GetCaptureDataOffset(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSACaptureData(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetRXDCOffset(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetIqImbConfig(int DataLength, void *arg, struct dev_unit *pdev);
int WT_GetAttCalResult(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGSetGapPowerEnable(int DataLength, void *arg, struct dev_unit *pdev);
//TBT
int WT_SetTBTApMode(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSASetTBTStaParam(int DataLength, void *arg, struct dev_unit *pdev);

//VSG
int WT_VSGStart(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGStop(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGGetStatus(int DataLength, void *arg, struct dev_unit *pdev);
int wt_set_vsg_pn(struct dev_unit *pdev, void *arg, int num);
int WT_VSGSetPNItem(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGGetPNItem(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGSetPNLoopParam(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGSetPNHead(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGGetPNHead(int DataLength, void *arg, struct dev_unit *pdev);
int WT_SetExtMode(int DataLength, void *arg, struct dev_unit *pdev);
//TBT
int WT_VSGStartTBTMimo(int DataLength, void *arg, struct dev_unit *pdev);
int wt_ClearTBTStaMode(struct dev_unit *pdev);
int WT_ClearTBTStaMode(int DataLength, void *arg, struct dev_unit *pdev);
int WT_VSGSetTBTStaParam(int DataLength, void *arg, struct dev_unit *pdev);
//模拟IQ信号内/外链路切换开关
int WT_WriteIQ_Switch(int DataLength, void *arg, struct dev_unit *pdev);
int WT_ReadIQ_Switch(int DataLength, void *arg, struct dev_unit *pdev);
//设置MIX LO链路
int WT_Set_MixLo_Switch(int DataLength, void *arg, struct dev_unit *pdev);

#endif