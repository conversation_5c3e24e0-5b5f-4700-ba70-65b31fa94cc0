//*****************************************************************************
// File: virtual_addr_list.c
// Describe:以虚拟地址保存移位寄存器的值
// Author：mayongfeng
// Date: 2024.08.15
//*****************************************************************************
#include "wtdefine.h"

#include "virtual_addr_list.h"
#include <linux/printk.h>
#include <linux/string.h>
#include <linux/spinlock.h>

static int VirtualSwitchMask[2][9][5] =
    {
        {
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x0000FFFF, 0x000000FF, 0x00000000, 0x00000000, 0x00000000},
            {0xFFFF00FF, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x000000FF, 0x000000A4, 0xFFFF0000, 0x00000000, 0x00000000},
            {0x000000FF, 0x000000A4, 0x0000FFFF, 0x00000000, 0x00000000},
            {0x000000FF, 0x000000C8, 0x00000000, 0xFFFF0000, 0x00000000},
            {0x000000FF, 0x000000C8, 0x00000000, 0x0000FFFF, 0x00000000},
            {0x000000FF, 0x000000D0, 0x00000000, 0x00000000, 0x0000FFFF},
            {0x000000FF, 0x000000D0, 0x00000000, 0x00000000, 0xFFFF0000},
        },
        {
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x0000FF00, 0x000000FF, 0x00000000, 0x00000000, 0x00000000},
            {0xFFFF0000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
            {0x00000000, 0x00000000, 0xFFFF0000, 0x00000000, 0x00000000},
            {0x00000000, 0x00000000, 0x0000FFFF, 0x00000000, 0x00000000},
            {0x00000000, 0x00000000, 0x00000000, 0xFFFF0000, 0x00000000},
            {0x00000000, 0x00000000, 0x00000000, 0x0000FFFF, 0x00000000},
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x0000FFFF},
            {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFF0000},
        }};

static int VirtualSwitchMask_SW_VD[2][9][9] =
{
    {
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
        {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0x00000000, 0x00000000},
    },
    {
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
        {0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF},
    }};

//*****************************************************************************
// 功能: 直接读IO内存对应的virtual寄存器数据，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  RegAddr：寄存器地址
// 返回值：寄存器数据
//*****************************************************************************
int wt_read_direct_reg_virtual(struct dev_virtual_addr_struct dev_virtual_addr, int RegAddr)
{
    int value = 0;
    value = dev_virtual_addr.VirtualDrectRegList[RegAddr];
    // printk("wt_read_direct_reg_virtual 0x%x = %d\n", RegAddr, value);
    return value;
}

//*****************************************************************************
// 功能: 直接写IO内存对应的virtual寄存器数据，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  RegAddr：寄存器地址 Value：寄存器数据
// 返回值：成功为0  失败为-1
//*****************************************************************************
void wt_write_direct_reg_virtual(struct dev_virtual_addr_struct *dev_virtual_addr, int RegAddr, int Value)
{
    dev_virtual_addr->VirtualDrectRegList[RegAddr] = Value;
    // printk("wt_write_direct_reg_virtual 0x%x = %d\n", RegAddr, Value);
}

int wt_read_LoMod_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index)
{
    return dev_virtual_addr.LoModConfigList[Index];
}

int wt_get_LoMod_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.LoModConfigCount;
}

void wt_set_LoMod_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count)
{
    dev_virtual_addr->LoModConfigCount = count;
}

void wt_write_LoMod_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int TXData)
{
    dev_virtual_addr->LoModConfigList[dev_virtual_addr->LoModConfigCount] = TXData;
    dev_virtual_addr->LoModConfigCount++;
}

int wt_read_LoMix_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index)
{
    return dev_virtual_addr.LoMixConfigList[Index];
}

void wt_write_LoMix_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int TXData)
{
    dev_virtual_addr->LoMixConfigList[dev_virtual_addr->LoMixConfigCount] = TXData;
    dev_virtual_addr->LoMixConfigCount++;
}

int wt_get_LoMix_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.LoMixConfigCount;
}
void wt_set_LoMix_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count)
{
    dev_virtual_addr->LoMixConfigCount = count;
}

int wt_read_AdcOrDac_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int Index)
{
    return dev_virtual_addr.AdcOrDacConfigList[Index];
}

void wt_write_AdcOrDac_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int TXData)
{
    dev_virtual_addr->AdcOrDacConfigList[dev_virtual_addr->AdcOrDacConfigCount] = TXData;
    dev_virtual_addr->AdcOrDacConfigCount++;
}

int wt_get_AdcOrDac_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.AdcOrDacConfigCount;
}

void wt_set_AdcOrDac_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int count)
{
    dev_virtual_addr->AdcOrDacConfigCount = count;
}

int wt_read_SPI_Cfg_List(struct dev_virtual_addr_struct dev_virtual_addr, int SPIId, int Index)
{
    return dev_virtual_addr.SPIConfigList[SPIId][Index];
}

void wt_write_SPI_Cfg_List(struct dev_virtual_addr_struct *dev_virtual_addr, int SPIId, int TXData)
{
    dev_virtual_addr->SPIConfigList[SPIId][dev_virtual_addr->SPIConfigCount[SPIId]] = TXData;
    dev_virtual_addr->SPIConfigCount[SPIId]++;
}

int wt_get_SPI_Cfg_List_Count(struct dev_virtual_addr_struct dev_virtual_addr, int SPIId)
{
    return dev_virtual_addr.SPIConfigCount[SPIId];
}

extern void wt_set_SPI_Cfg_List_Count(struct dev_virtual_addr_struct *dev_virtual_addr, int SPIId, int count)
{
    dev_virtual_addr->SPIConfigCount[SPIId] = count;
}

void SetVirtualAddrMode(struct dev_virtual_addr_struct *dev_virtual_addr, int mode)
{
    if(mode && dev_virtual_addr->VirtualAddrMode == 0)
    {
        spin_lock(&(dev_virtual_addr->dev_virtual_addr_lock));
    }
    else if (mode == 0 && dev_virtual_addr->VirtualAddrMode)
    {
        spin_unlock(&(dev_virtual_addr->dev_virtual_addr_lock));
    }
    dev_virtual_addr->VirtualAddrMode = mode;
}

int GetVirtualAddrMode(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.VirtualAddrMode;
}

void SetVirtualAddrSwPort(struct dev_virtual_addr_struct *dev_virtual_addr, int Port)
{
    dev_virtual_addr->Port = Port;
}

int GetVirtualAddrSwPort(struct dev_virtual_addr_struct dev_virtual_addr)
{
    return dev_virtual_addr.Port;
}

int GetVirtualSwitchMask(int SWVersion, int type, int port, int RegId)
{
    if(SWVersion == 3)
    {
        return VirtualSwitchMask_SW_VD[type][port][RegId];
    }
    else
    {
        return VirtualSwitchMask[type][port][RegId];
    }

}

void InitVirtualAddr(struct dev_virtual_addr_struct *dev_virtual_addr)
{
    memset(dev_virtual_addr->VirtualDrectRegList, 0, sizeof(dev_virtual_addr->VirtualDrectRegList[0]) * VIRTUAL_DRECT_REG_LIST_LENGTH);
    memset(dev_virtual_addr->AdcOrDacConfigList, 0, sizeof(dev_virtual_addr->AdcOrDacConfigList[0]) * VIRTUAL_ADCORDAC_REG_LIST_LENGTH);
    memset(dev_virtual_addr->LoModConfigList, 0, sizeof(dev_virtual_addr->LoModConfigList[0]) * VIRTUAL_LO_REG_LIST_LENGTH);
    memset(dev_virtual_addr->LoMixConfigList, 0, sizeof(dev_virtual_addr->LoMixConfigList[0]) * VIRTUAL_LO_REG_LIST_LENGTH);
    memset(dev_virtual_addr->SPIConfigList[0], 0, sizeof(dev_virtual_addr->SPIConfigList[0][0]) * VIRTUAL_ADCORDAC_REG_LIST_LENGTH);
    dev_virtual_addr->LoModConfigCount = 0;
    dev_virtual_addr->LoMixConfigCount = 0;
    dev_virtual_addr->AdcOrDacConfigCount = 0;
    dev_virtual_addr->SPIConfigCount[0] = 0;
    dev_virtual_addr->Port = 0;
}

