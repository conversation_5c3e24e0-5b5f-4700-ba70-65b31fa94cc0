#先到fpgaversion.conf查找不同仪器类型要升级的FPGA版本。
#然后到对应版本的目录获取FPGA升级文件，如果升级包FPGA版本与仪器FPGA版本一致，则不执行升级动作
#如WT448_FORCE_UPGRADE为1则强制升级FPGA版本（所有仪器类型、所有硬件版本都生效）
#如果没有找到要升级的FPGA版本，则必不执行升级动作

WT448_FORCE_UPGRADE = 0

#WT448_VA
WT448_BACK_VA = 6.0.a.3
WT448_BASE_VA = 3.2.a.a

#WT448_VB
WT448_BACK_VB = 6.0.b.12
WT448_BASE_VB = 3.2.b.32
WT448_BASE_VC = 3.2.b.32

#WT428 VA VB
WT428_BACK_VA = 9.0.a.4
WT428_BACK_VB = 9.0.a.4
WT428_BASE_VB = 3.2.b.32

#WT428 VC
WT428_BACK_VC = 9.0.b.3
WT428_BASE_VC = 3.2.b.32

#WT428 VC_VD
WT428_BACK_VD = 9.0.c.4
WT428_BASE_VD = 3.2.b.32

#WT418 VA
WT418_BASE_VA = 9.2.a.31

#WT418 VB
WT418_BASE_VB = 9.2.a.31
WT418_BASE_VC = 9.2.a.31
WT418_BASE_VD = 9.2.a.31
WT418_BASE_VE = 9.2.a.31
