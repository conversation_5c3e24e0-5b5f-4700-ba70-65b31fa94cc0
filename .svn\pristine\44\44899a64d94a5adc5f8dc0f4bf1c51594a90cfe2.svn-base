//*****************************************************************************
//  File: sock_server.cpp
//  SocketSrv部分
//  Data: 2016.7.19
//*****************************************************************************
#include "sock_server.h"

#include <new>
#include <cstring>
#include <iostream>
#include <unistd.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <errno.h>
#include <sys/time.h>
#include <math.h>
#include <assert.h>

#include "socket.h"
#include "wterror.h"
#include "link_manager.h"
#include "wtlog.h"
#include "conf.h"
#include "protocol.h"
#include "secure.h"
#include "device.h"
#include "license.h"

using namespace std;
using namespace std::placeholders;

int WTLinkSockServer::StartSocketSrv(int Port, WTConnect *WTConnet)
{
    struct sockaddr_in Addr;
    int Flag = 1;

    m_WTConn = WTConnet;

    m_Sfd = socket(AF_INET, SOCK_STREAM | SOCK_CLOEXEC, IPPROTO_TCP);

    if(m_Sfd <= 0)
    {
        WTLog::Instance().LOGERR(WT_CREATE_SOCKET_FAILED, "Create link socket error");
        return m_Sfd;
    }

    fcntl(m_Sfd, F_SETFL, fcntl(m_Sfd, F_GETFL, 0) | O_NONBLOCK); //设置sock为非阻塞模式

    bzero(&Addr, sizeof(Addr));
    Addr.sin_family = AF_INET;
    Addr.sin_port = htons(Port);
    Addr.sin_addr.s_addr = INADDR_ANY;  //监听本仪器的任意地址;

    //重用是重用的地址，也就是说 同一个地址上可以绑定 若干个socket
    if( setsockopt(m_Sfd, SOL_SOCKET, SO_REUSEADDR, &Flag, sizeof(int)) == -1)
    {
        WTLog::Instance().LOGERR(WT_SOCKET_SET_ERROR, "Set Socket error");
        return WT_SOCKET_SET_ERROR;
    }

    if(bind(m_Sfd, (const sockaddr *)&Addr, sizeof(Addr)) != 0)
    {
        WTLog::Instance().LOGERR(WT_SOCKET_BIND_ERROR, "Server bind error");
        close(m_Sfd);
        return WT_SOCKET_BIND_ERROR;
    }

#ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "bind " << inet_ntoa(Addr.sin_addr) << " : " << Port << " success! " << endl;
#endif
    listen(m_Sfd, 128);

    //绑定accept事件
    m_AcceptIO.set<WTLinkSockServer, &WTLinkSockServer::AcceptCb>(this);
    m_AcceptIO.start(m_Sfd, wtev::READ);

    //启动计时定时器用于连接超时检验
    //监控间隔为1s
    const int SRV_TIMER_INTERVAL = 1;
    TimeoutTimer.set<WTLinkSockServer, &WTLinkSockServer::TimerCb>(this);
    TimeoutTimer.start(SRV_TIMER_INTERVAL, SRV_TIMER_INTERVAL);
    return WT_OK;
}

WTLinkSockServer::~WTLinkSockServer()
{
    shutdown(m_Sfd, SHUT_RDWR);
    close(m_Sfd);
}

void WTLinkSockServer::TimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;

    //从前向后显示m_LstConn中的数据
    for (auto i = ComfirmDataList.begin(); i != ComfirmDataList.end(); )
    {
        (*i)->Times++;
        if((*i)->Times > 20)        //连接合法性验证等待超时
        {
            //关闭连接并清理相关的资源
            if((*i)->State == NetWaitRamdomState || (*i)->State == NetWaitAuthState)
            {
                WTLog::Instance().LOGERR(WT_WAIT_ACCEPT_TIMEOUT, "Wait accept Timeout");
                close((*i)->ClientFd);
                ((*i)->IOWatcher).stop();
                ((*i)->IOWatcher).set_userdata(nullptr);
                i = ComfirmDataList.erase(i);
                continue;
            }
        }
        i++;
    }
}

void WTLinkSockServer::AcceptCb(wtev::io &watcher, int revents)
{
    if (EV_ERROR & revents)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Got invalid event");
        return;
    }

    int KeepIdle = 1;       // 如该连接在1秒内没有任何数据往来,则进行探测
    int KeepInterval = 1;   // 探测时发包的时间间隔为5 秒
    int KeepCount = 3;      // 探测尝试的次数.如果第1次探测包就收到响应了,则后2次的不再发
    struct sockaddr_in ClientAddr;
    socklen_t ClientLen = sizeof(ClientAddr);

    int ClientSfd = accept(watcher.fd, (struct sockaddr *)&ClientAddr, &ClientLen);

    if (ClientSfd < 0)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Accept error");
        return;
    }
    /* 设置socket属性，TCP 心跳机制 */
    WRSocket::SetKeepAlive(ClientSfd, true, KeepIdle, KeepInterval, KeepCount, 5);

    fcntl(ClientSfd, F_SETFL, fcntl(ClientSfd, F_GETFL, 0) | O_NONBLOCK | FD_CLOEXEC); //设置sock为非阻塞模式

    unique_ptr<ComfirmData> ComData(new(std::nothrow) ComfirmData(ClientSfd, NetWaitRamdomState, 0, m_Loop));
    ComfirmDataList.push_back(move(ComData));
    //绑定Confirmation事件
    ComfirmDataList.back()->IOWatcher.set<WTLinkSockServer, &WTLinkSockServer::ConfirmationCb>(this);
    ComfirmDataList.back()->IOWatcher.set_userdata(&ComfirmDataList.back());
    ComfirmDataList.back()->IOWatcher.start(ClientSfd, wtev::READ);
}

int WTLinkSockServer::QueryConnctionInfo(WRSocket &Sock, int IDType)
{
    int Ret = WT_OK;
    char Buff[1024] = {0};
    int Len = 0;
    m_WTConn->QueryWTConnectInfo(IDType, Buff);
    if(Buff[0] == 0)
    {
        strcpy(Buff, "IDLE:");
    }
    Ret = Sock.Send(Buff, sizeof(Buff), Len);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        Ret = WT_SOCKET_CLOSED;
    }
    return Ret;
}

int WTLinkSockServer::QueryDeviceInfo(WRSocket &Sock)
{
    char Buff[256] = {0};
    int Len = 0;
    int Ret = WT_OK;

    //获取设备基本信息(设备类型，设备名称，SN，iP，固件版本号)，封装，然后send
    sprintf(Buff, "TYPE:%s;NAME:%s;IP:%s;SN:%s;VERSION:%s;\r\n",
            WTDeviceInfo::Instance().GetDeviceType(), WTDeviceInfo::Instance().GetDeviceDetailedInfo().Name, WTDeviceInfo::Instance().GetDeviceDetailedInfo().IP,
            WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN, WTDeviceInfo::Instance().GetDeviceDetailedInfo().FwVersion);

    Ret = Sock.Send(Buff, sizeof(Buff), Len);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        Ret = WT_SOCKET_CLOSED;
    }

    return Ret;
}

int WTLinkSockServer::QuerySubDeviceCfg(WRSocket &Sock)
{
    int Ret = WT_OK;
    int DevNum = 0;
    int i = 0;
    char DataBuf[1024] = {0};
    int DataLen = 0;

    WTConf::DevCfg Cfg;
    //获取子仪器的配置信息
    Ret = BaseConf::Instance().GetDevNum(DevNum);
    if(Ret != WT_OK)
    {
        return Ret;
    }
    for(i = 0; i < DevNum; i++)
    {
        Ret = BaseConf::Instance().GetDevCfg(i + 1, Cfg); //DevID 从1开始
        if(Ret != WT_OK)
        {
            return Ret;
        }
        else
        {
            memcpy(DataBuf + sizeof(Cfg)*i, &Cfg, sizeof(Cfg));
            DataLen += sizeof(WTConf::DevCfg);
        }
    }
    Ret = Sock.Send(DataBuf, sizeof(DataBuf), DataLen);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "socket error or closed");
        Ret = WT_SOCKET_CLOSED;
    }

    return Ret;
}

int WTLinkSockServer::GetConnType(char *DataBuf, unsigned char *Random, int &ConnType)
{
    int Ret = WT_OK;
    char ForceConnection[17]   = "WT4xxSEForceConn";
    char NormalConnection[17]  = "WT4xxSNormalConn";
    char MultiConnection[17]   = "WT4xxSEMultiConn";
    char MangerConnection[17]  = "WT4xxManagerTool";
    char MonitorConnection[17] = "WT4xxMonitorLink";
    char DiagnosisConnection[17] = "SRVDiagnosisConn";

    //解密验证连接字符串,如果验证成功则解析出链接信息的type id等,比较字符串不区分大小
    Secure::Decrypt((int *)DataBuf, (strlen(MangerConnection) / 4), (unsigned int *)Random);

    if(memcmp(DataBuf, MangerConnection, 16) == 0)
    {
        ConnType = MGR_LINK;            //管理连接
    }
    else if(memcmp(DataBuf, ForceConnection, 16) == 0)
    {
        ConnType = SRV_EXCLUDE_LINK;    //server独占连接
    }
    else if(memcmp(DataBuf, NormalConnection, 16) == 0)
    {
        ConnType = SRV_NORMAL_LINK;     //server普通连接
    }
    else if(memcmp(DataBuf, MultiConnection, 16) == 0)
    {
        ConnType = SRV_SUB_LINK;        //server子连接
    }
    else if(memcmp(DataBuf, MonitorConnection, 16) == 0)
    {
        ConnType = MON_LINK;            //监视连接
    }
    else if(memcmp(DataBuf, DiagnosisConnection, 16) == 0)
    {
        ConnType = DIAGNOSIS_LINK;      //server诊断连接
    }
    else
    {
        WTLog::Instance().LOGERR(WT_COMFIRM_DECRYPT_ERROR, "Comfirm decrypt code failed");
        Ret = WT_COMFIRM_DECRYPT_ERROR;
    }
    return Ret;
}

void WTLinkSockServer::ConfirmationCb(wtev::io &watcher, int revents)
{
    if (EV_ERROR & revents)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "ConfirmationCb Got invalid event");
        return;
    }
    else
    {
        unique_ptr<ComfirmData> &ComData = *(unique_ptr<ComfirmData> *)watcher.userdata;

        int ClientSfd = watcher.fd;
        int Len = 0;
        char *DataBuf = NULL;
        int Ret = WT_OK;
        int ConnType = -1;
        int ServerID = -1;

        if((Ret = ComData->WrSock.Recv(Len, false)) == WT_OK)       //接收成功
        {
            if (Len < 30)
            {
                return;
            }
            else if (Len == 30)
            {
                ComData->Times = 0;
                DataBuf = ComData->WrSock.GetRxBuf();    //获取socket buff信息
                const char SCANBuf[] = "scan";
                const char CONNINFOBuf[] = "curconninfo";
                const char SUBDEVBuf[] = "subdeviceassign";
                const char LicStatusBuf[] = "GetLicStatus";

                //不需要做合法验证处理，直接返回设备信息的情况,根据协议返回连接信息和设备信息
                if(memcmp(DataBuf, SCANBuf, 4) == 0)    //获取设备信息，格式：TYPE:%s;NAME:%s;IP:%s;SN:%s;VERSION:%s;\r\n
                {
                    QueryDeviceInfo(ComData->WrSock);
                    ComData->WrSock.ResetBuf();
                    return;
                }
                else if(memcmp(DataBuf, CONNINFOBuf, 11) == 0)   //获取连接信息，返回内容格式IP1[16]:Port+IP2[16]:Port+...
                {
                    int SubDevID = 0;
                    memcpy(&SubDevID, DataBuf + strlen(CONNINFOBuf), sizeof(int));
                    QueryConnctionInfo((ComData->WrSock), SubDevID);
                    ComData->WrSock.ResetBuf();
                    return;
                }
                else if(memcmp(DataBuf, SUBDEVBuf, 15) == 0)   //获取子仪器划分情况，返回内容格式SubDev结构体的组合
                {
                    int SubDevID = 0;
                    memcpy(&SubDevID, DataBuf + strlen(SUBDEVBuf), sizeof(int));
                    QuerySubDeviceCfg(ComData->WrSock);
                    ComData->WrSock.ResetBuf();
                    return;
                }
                else if (memcmp(DataBuf, LicStatusBuf, strlen(LicStatusBuf)) == 0)   //获取仪器license情况
                {
                    int Status = License::Instance().CheckLicense();
                    WTLog::Instance().WriteLog(LOG_DEBUG, "GetLicStatus = %#x\n", Status);
                    ComData->WrSock.Send(reinterpret_cast<char *>(&Status), sizeof(Status), Len);
                    ComData->WrSock.ResetBuf();
                    return;
                }

                //开始 连接合法性验证
                else if(ComData->State == NetWaitRamdomState)
                {
                    if(memcmp(DataBuf, "WT4xxConnection", 15) == 0)     //step 1:给客户端返回 随机码
                    {
                        ComData->State = NetWaitAuthState;
                        memset(ComData->Random, 0, 16);
                        Secure::GenerateRandom(ComData->Random, 16);
                        ComData->WrSock.Send((char *)(ComData->Random), 16, Len);
                        ComData->WrSock.ResetBuf();
                        return;
                    }
                    else
                    {
                        WTLog::Instance().LOGERR(WT_COMFIRM_WAIT_FAILED, "Comfirm wait accept failed");
                    }
                }
                else if(ComData->State == NetWaitAuthState)     //step 2:验证连接字符串
                {
                    if(GetConnType(DataBuf, ComData->Random, ConnType) == WT_OK)
                    {
                        //获取连接申请的Server ID
                        memcpy(&ServerID, DataBuf + 16, sizeof(int));
#ifdef DEBUG
                        WTLog::Instance().WriteLog(LOG_DEBUG, "WT-Link authenticate connection ServerID = %d\n", ServerID);
#endif

                        //连接时申请的ServerId参数合法性检验:当连接类型为管理连接时，ServerID必须为0；当连接类型不是管理连接时，ServerID的申请的范围为1 <= ServerID <= 当前配置文件配置的子仪器数
                        if( ((ConnType == MGR_LINK) && (ServerID == 0)) || ((ConnType != MGR_LINK) && (1 <= ServerID) && (ServerID <= m_ServerCnt)) )
                        {
                            //验证成功分配连接 （调用LinkMng的接口）
                            int Ret = m_WTConn->AssignConnetion(ClientSfd, ConnType, ServerID);
                            if(Ret == WT_ADMIN_CONN_EXIT)
                            {
                                const char *Conn = "mgrlink exist";    //长度为13个字符
                                ComData->WrSock.Send(Conn, strlen(Conn), Len);
                                ComData->WrSock.ResetBuf();
                                WTLog::Instance().LOGERR(WT_ADMIN_CONNECTION_EXIT, "Admin connection exit!");
                            }
                            if(Ret == WT_LIC_FILE_ILLEGAL)
                            {
                                const char *Conn = "license error";       //长度为13个字符
                                ComData->WrSock.Send(Conn, strlen(Conn), Len);
                                ComData->WrSock.ResetBuf();
                                WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, "License file illegal!");
                            }
                        }
                        else
                        {
                            //记录错误信息，并释放相关资源
                            WTLog::Instance().LOGERR(WT_SERVERID_OUT_RANGE, "ServerID out of the range!!");
                        }
                    }
                }
            }
            else // Len > 30 是不合法的... 即客户端发送超长的数据
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "WT-Link confirm connection recv data error, data Length[%d]\n", Len);
                WTLog::Instance().LOGERR(WT_COMFIRM_RECV_INCORRECT_LENGTH, "Recv Incorrect Comfirm Data Length.");
            }
        }
        else // 读取数据失败. 下面这个不能开启打印, 心跳会每秒一次.
        {
           // const char *ErrStr = ComData->WrSock.GetLastErrorStr();
           // WTLog::Instance().WriteLog(LOG_DEBUG, "WT-Link Sock recv error %s", ErrStr);
        }

        watcher.stop();
        watcher.set_userdata(nullptr);
        close(ClientSfd);
        ComfirmDataList.remove(ComData);

        return;
    }
}
