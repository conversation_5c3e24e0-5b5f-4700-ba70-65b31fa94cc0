#ifndef _SCPI_WAVEGENERETOR_H_
#define _SCPI_WAVEGENERETOR_H_

#include "scpi/scpi.h"
#include "scpi_3gpp_base.h"

#ifdef __cplusplus
extern "C" {
#endif
    
     enum SLE_BW
    {
        SLE_BANDWIDTH_1 = 1,  //1MHz
        SLE_BANDWIDTH_2 = 2,  //2MHz
        SLE_BANDWIDTH_3 = 3,
        SLE_BANDWIDTH_4 = 4,  //3MHz
    };
    
    scpi_result_t SetWaveGenDemod(scpi_t * context);
    scpi_result_t SetWaveGenSampleRate(scpi_t * context);
    scpi_result_t SetWaveGenOFDMClockRate(scpi_t * context);
    scpi_result_t SetWaveGenAxPPDU(scpi_t * context);
    scpi_result_t SetWaveGenWifiDuplicate(scpi_t *context);
    scpi_result_t SetWaveGenAWGN(scpi_t *context);
    scpi_result_t SetWaveGenPeakAvgRatioOption(scpi_t *context);

    scpi_result_t Set11BE_EHT_PPDU(scpi_t *context);
    scpi_result_t SetWaveGenACPPDU(scpi_t *context);
    scpi_result_t SetWaveGenBandWidth(scpi_t * context);
    scpi_result_t SetWaveGenFreqOffset(scpi_t * context);
    scpi_result_t SetWaveGenIQImbAmp(scpi_t * context);
    scpi_result_t SetWaveGenIQImbPhase(scpi_t * context);
    scpi_result_t SetWaveGenDCOffsetI(scpi_t * context);
    scpi_result_t SetWaveGenDCOffsetQ(scpi_t * context);
    scpi_result_t SetWaveGenIFG(scpi_t * context);
    scpi_result_t SetWaveGenHeadandTailIFG(scpi_t * context);
    scpi_result_t SetWaveGenSNR(scpi_t *context);
    scpi_result_t SetWaveGenSpatialExtension(scpi_t *context);
    scpi_result_t SetWaveGenNtx(scpi_t * context);
    scpi_result_t SetWaveGenClockError(scpi_t * context);
    scpi_result_t SetWaveGenPhaseNoiseEnable(scpi_t * context);
    scpi_result_t SetWaveGenPhaseNoise(scpi_t * context);
    scpi_result_t SetWaveGenPhaseNoisePllBW(scpi_t * context);
    scpi_result_t SetWaveGenClockError(scpi_t * context);
    scpi_result_t SetWaveGenTimeout(scpi_t * context);
    //多PN
    scpi_result_t SetMPRepeatGap(scpi_t * context);
    scpi_result_t SetMPRepeatCount(scpi_t * context);
    scpi_result_t SetMPGap(scpi_t * context);
    scpi_result_t SetMPSaveTempPn(scpi_t * context);
    scpi_result_t SetMPCreatePn(scpi_t * context);

    //11b
    scpi_result_t Set11B_DataRate(scpi_t *context);
    scpi_result_t Set11B_Preamble(scpi_t *context);
    scpi_result_t Set11B_FilterEnable(scpi_t *context);
    //11ag
    scpi_result_t Set11AG_DataRate(scpi_t *context);
    //11n
    scpi_result_t Set11N_MCS(scpi_t *context);
    scpi_result_t Set11N_FrameType(scpi_t *context);
    scpi_result_t Set11N_Sounding(scpi_t *context);
    scpi_result_t Set11N_STBC(scpi_t *context);
    scpi_result_t Set11N_Smoothing(scpi_t *context);
    scpi_result_t Set11N_AGG(scpi_t *context);
    scpi_result_t Set11N_GI(scpi_t *context);
    scpi_result_t Set11N_Coding(scpi_t *context);
    scpi_result_t Set11N_SoundingNDP(scpi_t *context);
    scpi_result_t Set11N_ESS(scpi_t *context);
    //11n qmat
    scpi_result_t Set11N_QMat(scpi_t *context);
    scpi_result_t Set11N_QMatNtx(scpi_t *context);
    scpi_result_t Set11N_QMatType(scpi_t *context);
    scpi_result_t Set11N_QMatDelay(scpi_t *context);
    scpi_result_t Set11N_QMatMap(scpi_t *context);
    //11ac
    scpi_result_t Set11AC_MCS(scpi_t * context);
    scpi_result_t Set11AC_STBC(scpi_t *context);
    scpi_result_t Set11AC_GI(scpi_t *context);
    scpi_result_t Set11AC_Coding(scpi_t *context);
    scpi_result_t Set11AC_NSS(scpi_t *context);
    scpi_result_t Set11AC_Beamformed(scpi_t *context);
    scpi_result_t Set11AC_SoundingNDP(scpi_t *context);
    scpi_result_t Set11AC_GroupID(scpi_t *context);
    scpi_result_t Set11AC_PartialAID(scpi_t *context);
    scpi_result_t Set11AC_TXOPPS(scpi_t *context);

    //11ac su qmat
    scpi_result_t Set11AC_SU_RUQMat(scpi_t *context);
    scpi_result_t Set11AC_SU_RUQMatNtx(scpi_t *context);
    scpi_result_t Set11AC_SU_RUQMatType(scpi_t *context);
    scpi_result_t Set11AC_SU_RUQMatDelay(scpi_t *context);
    scpi_result_t Set11AC_SU_RUQMatMap(scpi_t *context);

    //cw
    scpi_result_t SetWaveGen_CW_psduLength(scpi_t *context);
    scpi_result_t SetWaveGen_CW_Type(scpi_t *context);
    //gen and aly
    scpi_result_t SCPIWaveGentoVsaLoadAsCapture(scpi_t * context);
    scpi_result_t SCPIWaveGentoSetVsgWave(scpi_t * context);

    scpi_result_t SetPNFileExternSettingData(scpi_t *context);
    scpi_result_t CleanPNFileExternSettingData(scpi_t *context);

    // 蜂窝新增Base settings
    scpi_result_t Set3GPPWaveGenFlatnessFactor(scpi_t *context);
    scpi_result_t Set3GPPWaveGenPhaseNoiseFlag(scpi_t *context);
    scpi_result_t Set3GPPWaveGenPhaseNoiseFactor(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif

