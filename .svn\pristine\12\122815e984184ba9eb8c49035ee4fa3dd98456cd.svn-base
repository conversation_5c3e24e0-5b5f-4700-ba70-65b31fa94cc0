
#ifndef SCPI_IEEE488_H
#define	SCPI_IEEE488_H

#include "scpi/types.h"

#ifdef  __cplusplus
extern "C" {
#endif

#define STB_R01 0x01    /* Not used */
#define STB_PRO 0x02    /* Protection Event Flag */
#define STB_QMA 0x04    /* Error/Event queue message available */
#define STB_QES 0x08    /* Questionable status */
#define STB_MAV 0x10    /* Message Available */
#define STB_ESR 0x20    /* Standard Event Status Register */
#define STB_SRQ 0x40    /* Service Request */
#define STB_OPS 0x80    /* Operation Status Flag */


#define ESR_OPC 0x01    /* Operation complete */
#define ESR_REQ 0x02    /* Request Control */
#define ESR_QER 0x04    /* Query Error */
#define ESR_DER 0x08    /* Device Dependent Error */
#define ESR_EER 0x10    /* Execution Error (e.g. range error) */
#define ESR_CER 0x20    /* Command error (e.g. syntax error) */
#define ESR_URQ 0x40    /* User Request */
#define ESR_PON 0x80    /* Power On */

LIBSCPI_API scpi_result_t SCPI_CoreCls(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreEse(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreEseQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreEsrQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreIdnQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreOpc(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreOpcQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreRst(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreSre(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreSreQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreStbQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreTstQ(scpi_t * context);
LIBSCPI_API scpi_result_t SCPI_CoreWai(scpi_t * context);

LIBSCPI_API scpi_reg_val_t SCPI_RegGet(scpi_t * context, scpi_reg_name_t name);
LIBSCPI_API void SCPI_RegSet(scpi_t * context, scpi_reg_name_t name, scpi_reg_val_t val);
LIBSCPI_API void SCPI_RegSetBits(scpi_t * context, scpi_reg_name_t name, scpi_reg_val_t bits);
LIBSCPI_API void SCPI_RegClearBits(scpi_t * context, scpi_reg_name_t name, scpi_reg_val_t bits);

LIBSCPI_API void SCPI_EventClear(scpi_t * context);

#ifdef  __cplusplus
}
#endif

#endif

