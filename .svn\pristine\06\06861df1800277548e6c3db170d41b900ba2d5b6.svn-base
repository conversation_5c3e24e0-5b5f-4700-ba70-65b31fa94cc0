//*****************************************************************************
//File: hwlib.c
//Describe:硬件驱动处理层
//Author：wangzhenglong
//Date: 2016.8.11
//Revise:有新的RX硬件板子，程序要兼容新旧板子--2017.08.30
//*****************************************************************************
#include "hwlib.h"
#include "wtbackdefine.h"
#include "wtbacklib.h"
#include "wtbusilib.h"
#include "cryptomemory.h"
#include "rfswitch.h"
#include "wtbusidefine.h"
#include "wtvsadefine.h"
#include "wtvsgdefine.h"
#include "wtswitch.h"

#if 1
const pFunc pFuncArray[] =
    {
        //*******************单元板(背板/VSA、VSG业务板)公共接口*******************
        //加密芯片
        [CRYPTO_MEM_INIT] = WT_CryptoMemInit,
        [CRYPTO_MEM_GET_INFO] = WT_GetCryptoMemInfo,
        [CRYPTO_MEM_GET_SN] = WT_GetCryptoMemSN,
        [CRYPTO_MEM_GET_CODE] = WT_GetCryptoMemCode,
        //单元板FPGA信息
        [GET_UNIT_BOARD_HW_VERSION] = WT_GetUnitBoardHWVersion,
        [GET_UNIT_BOARD_FPGA_INFO] = WT_GetUnitBoardFPGAInfo,
        [GET_UNIT_BOARD_REVISION_ID] = WT_GetUnitBoardRevisionId,
        //配置PCI读写延时
        [SET_REG_WR_DELAY] = WT_SetUnitBoardPciDelay,
        //Flash/EEPROM
        [WRITE_ROM] = WT_WriteRomPage,
        [READ_ROM] = WT_ReadRomPage,
        //电压侦测
        [READ_UB_VOLT_CHANNEL_VALUE] = WT_ReadUBVoltChannelValue,
        //温度传感器
        [READ_UNIT_BOARD_TEMPERATURE] = WT_ReadUBTemperature,
        //获取业务板槽位号
        [GET_BUSI_BOARD_SLOT] = WT_GetBusiBoardSlot,
        //*******************业务单元板(VSA/VSG)通用接口*******************
        //获取基带板FPGA信息
        [GET_BB_BOARD_INFO] = WT_GetBusiFpgaInfo,
        //读取业务板完成状态并清零
        [GET_COMPLETE_CLR_STATUS] = WT_ReadCompleteClrStatus,
        //设置链路工作模式（主从模式/SISO/MIMO）
        [SET_DEVICE_WORK_MODE] = WT_SetUnitModWorkMode,
        //清除驱动里缓存的工作模式
        [CLEAR_WORK_MODE] = WT_ClearWorkMode,
        //AD5611
        [WRITE_BB_AD5611] = WT_WriteBusiOCXOCode,
        [READ_BB_AD5611] = WT_ReadBusiOCXOCode,
        //AD7682
        [GET_BB_AD7682_CHANNEL] = WT_ReadBusiAD7682Channel,
        //AD7689
        [GET_SW_AD7689_CHANNEL] = WT_ReadSwAD7689Channel,
        //时钟板ADF4106
        [WRITE_BB_ADF4106] = WT_WriteADF4106Code,
        [READ_BB_ADF4106] = WT_ReadADF4106Code,
        //HM7044 基带板时钟芯片
        [WRITE_BB_HM7044] = WT_WriteHM7044Code,
        [READ_BB_HM7044] = WT_ReadHM7044Code,
        //射频板LTC5594
        [WRITE_BB_LTC5594] = WT_WriteLTC5594Code,
        [READ_BB_LTC5594] = WT_ReadLTC5594Code,
        //本振板LMX2594
        [WRITE_BB_LMX2594] = WT_WriteLMX2594Code,
        [READ_BB_LMX2594] = WT_ReadLMX2594Code,
        //本振板LMX2582
        [WRITE_BB_LMX2582] = WT_WriteLMX2582Code,
        [READ_BB_LMX2582] = WT_ReadLMX2582Code,
        //本振板LMX2820
        [WRITE_BB_LMX2820] = WT_WriteLMX2820Code,
        [READ_BB_LMX2820] = WT_ReadLMX2820Code,
        //本振板LMXHMC833
        [WRITE_BB_HMC833] = WT_WriteHMC833Code,
        [READ_BB_HMC833] = WT_ReadHMC833Code,
        //电压检测AD7091
        [WRITE_BB_AD7091] = WT_WriteBusiAD7091Reg,
        [READ_BB_AD7091] = WT_ReadBusiAD7091Reg,
        [GET_BB_VOLTAGE] = WT_ReadBusiChannelVoltValue,
        //DDS AD9912
        [WRITE_LO_DDS] = WT_WriteDDSCode,
        [READ_LO_DDS] = WT_ReadDDSCode,
        [SET_LO_DDS_FREQ_CHANNEL] = WT_SetDDSFreqChannel,
        //ATT移位寄存器
        [WRITE_RF_ATT_SHIFT] = WT_WriteAttAndShiftCode,
        [READ_RF_ATT_SHIFT] = WT_ReadAttAndShiftCode,
        //本振板移位寄存器
        [WRITE_BB_LO_SHIFT] = WT_WriteLoBoardShitfCode,
        [READ_BB_LO_SHIFT] = WT_ReadLoBoardShitfCode,
        [SET_LO_SHIFT_HMC705] = WT_SetLoBoardHMC705,
        [SET_LO_SHIFT_LOOP_FILTER] = WT_SetLoBoardLoopFilter,
        [SET_LO_SHIFT_LOOP_FILTER_INIT] = lo_loop_filter_table_init,
        [SET_LO_SHIFT_FREQ_CHANNEL] = WT_SetLoBoardFreqChannel,
        [Set_LO_COM_Mode]=WT_SetLOComMode,
        [Get_LO_COM_Mode]=WT_GetLOComMode,
        //AD9684 AD9142 AD9643
        [WRITE_RF_ADC_DAC] = WT_WriteAdcOrDacCode,
        [READ_RF_ADC_DAC] = WT_ReadAdcOrDacCode,
        //IQ交换
        [SET_IQ_SWITCH] = WT_SetIQSwitch,
        //PA
        [SET_RF_PA] = WT_SetRFPA,
        [SET_RF_LNA] = WT_SetRFLNA,
        //BAND
        [SET_RX_BAND] = WT_SetRXBand,
        [SET_TX_BAND] = WT_SetTXBand,
        //PORT
        [SET_RX_PORT] = WT_SetRXPort,
        [SET_TX_PORT] = WT_SetTXPort,
        //ATT
        [WRITE_ATT] = WT_WriteATT,
        [READ_ATT] = WT_ReadATT,
        //XMDA
        [GET_XDMA_STATUS] = WT_GetXdmaStatus,
        //FPGA Upgrade
        [WRITE_BASE_FPGA_EARSE] = WT_BaseFpgaEarse,
        [WRITE_BASE_FPGA_UPGRADE] = WT_BaseFpgaUpgrade,
        [WRITE_BASE_FPGA_WRITEONEPAGE] = WT_BaseFpgaWriteOnePage,
        [FPGA_RELOAD] = WT_FPGAReload,
        [SET_FLASH_4BYTE_MODE] = WT_SetFlash4ByteMode,
        //ATT CAL
        [SET_ATT_CAL_CONFIG] = WT_SetATTCalConfig,
        //模拟IQ信号内/外链路切换开关
        [SET_ANALOGIQ_SWITCH] = WT_WriteIQ_Switch,
        [GET_ANALOGIQ_SWITCH] = WT_ReadIQ_Switch,
        //设置MIX LO链路
        [SET_MIX_LO_SWITCH] = WT_Set_MixLo_Switch,
        //ListMode
        [SET_LIST_MODE] = WT_SetListMode,
        [INIT_VIRTUAL_ADDR] = WT_InitVirtualAddr,
        [SET_VIRTUAL_ADDR_RECORD] = WT_SetVirtualAddrRecord,
        [SET_VIRTUAL_ADDR_CONFIG] = WT_SetVirtualAddrConfig,
        [SET_SEQUENCE_SEGMENT_TIME] = WT_SetSeqSegTimeParam,
        [SET_SEQUENCE_TRIG_COM_PARAM] = WT_SetSeqTrigComParam,
        [SET_SEQUENCE_TRIG_LOOP_PARAM] = WT_SetSeqTrigLoopParam,
        [SET_SWITCH_MODE] = WT_SetSwitchMode,
        // 双工
        [GET_DUPLEX_VSG_RUN_CONFIG] = WT_GetDuplexVsgRunConfig,
        //*******************VSG业务***************************
        [VSG_START] = WT_VSGStart,
        [VSG_SET_LIST_CELL_MOD] = WT_VSGSSetListCellMod,
        [VSG_STOP] = WT_VSGStop,
        [VSG_GET_STATUS] = WT_VSGGetStatus,
        [VSG_SET_PN_HEAD] = WT_VSGSetPNHead,
        [VSG_GET_PN_HEAD] = WT_VSGGetPNHead,
        [SET_EXT_MODE]    = WT_SetExtMode,
        //IFG控制功能开关
        [VSG_SET_IFG_STATUS] = WT_VSGSetGapPowerEnable,
        //TBT
        [VSG_START_TBT_MIMO] = WT_VSGStartTBTMimo,
        [VSG_SET_TBT_STA_PARAM] = WT_VSGSetTBTStaParam,
        [VSG_CLEAR_TBT_STA_MODE] = WT_ClearTBTStaMode,
        [VSG_GET_PARAM_DMA_BUF] = WT_VSGGetParamDmaBuf,
        [VSG_RESET_DMA_FIFO] = WT_VSGResetDmaFIFO,
        [VSG_SET_PN_LOOP_IFG] = WT_VSGSetPnLoopIfg,
        //*******************VSA业务***************************
        [VSA_START] = WT_VSAStart,
        [VSA_STOP] = WT_VSAStop,
        [VSA_GET_STATUS] = WT_VSAGetStatus,
        [GET_TRIG_ADDR_START] = WT_GetTrigAddrStart,
        //数字TRIGGER
        [SET_RX_TRIG_LEVEL_DIGITAL] = WT_SetRXTrigLevelDigital,
        //快速AGC
        [SET_AGC_ENABLE] = WT_SetAGCEnable,
        [SET_AGC_GATE_VALUE] = WT_SetAGCGateValue,
        //VSA链路常规配置
        [SET_ADC_POWERDOWN] = WT_SetADCPowerDown,
        //TBT
        [SET_TBT_AP_MODE] = WT_SetTBTApMode,
        [VSA_SET_TBT_STA_PARAM] = WT_VSASetTBTStaParam,
        [SET_IQ_IMB_COMP] = WT_SetIqImbConfig,
        [GET_ATT_CAL_RESULT] = WT_GetAttCalResult,
        [GET_FPGA_CAPTURE_POWER] = WT_GetFpgaCapturePower,
        [VSA_GET_PARAM_DMA_BUF] = WT_VSAGetParamDmaBuf,
        [SET_CW_FLAG] = WT_VSASetCwFlag,
        [GET_COMPLETE_CNT] = WT_GetCompleteCnt,
        [GET_SEQ_COMPLETE_STAT] = WT_GetVsaSeqCompleteStat,
        //*******************背板业务***************************
        [GET_BACK_FPGA_INFO] = WT_GetBackFpgaInfo,
        [GET_DEV_CLK_STATE] = WT_GetDevClkState,
        [SET_SWITCH_BOARD_CFG] = WT_SetSwitchCfg,
        //AD5611晶振
        [WRITE_OCXO_CODE] = WT_WriteOCXOCode,
        [READ_OCXO_CODE] = WT_ReadOCXOCode,
        //开关板内部功率检测AD8318->AD7682
        [GET_SWITCH_INNER_POWER] = WT_GetSwitchInnerPower,
        //端口功率检测AD9228
        [WRITE_SWITCH_AD9228] = WT_WriteSwitchAD9228,
        [READ_SWITCH_AD9228] = WT_ReadSwitchAD9228,
        [GET_SWITCH_PORT_POWER] = WT_GetSwitchPortPower,
        //背板时钟HM7043
        [WRITE_HM7043_CODE] = WT_WriteBpClockHM7043,
        [READ_HM7043_CODE] = WT_ReadBpClockHM7043,
        //获取射频板版本
        [READ_RF_VERSION] = WT_ReadRfVersion,
        //加密芯片
        [INIT_BACK_CRYPTO_AT88] = WT_InitCryptoAT88,
        [WRITE_BACK_CRYPTO_AT88] = WT_WriteCryptoAT88,
        [READ_BACK_CRYPTO_AT88] = WT_ReadCryptoAT88,
        //开关板电压侦测AD7091
        [WRITE_BACK_VOLTAGE_REG] = WT_WriteBackAD7091Reg,
        [READ_BACK_VOLTAGE_REG] = WT_ReadBackAD7091Reg,
        [READ_BACK_CHANNEL_VALUE] = WT_ReadBackChannelVoltValue,
        //风扇
        [SET_FAN_REG] = WT_WriteFan,
        [GET_FAN_REG] = WT_ReadFan,
        //开关板ATT
        [SET_SWB_ATT_CODE] = WT_SetSwbAttCode,
        [GET_SWB_ATT_CODE] = WT_GetSwbAttCode,
        //开关板
        [SET_SWITCH_STATE] = WT_SetSwitchState,
        [SET_SWITCH_VSG_CTL3] = WT_SetSwitchCTL3,
        [SET_SWITCH_SHIFT_REG] = WT_SetSwitchShiftReg,
        [GET_SWITCH_SHIFT_REG] = WT_GetSwitchShiftReg,
        //LED
        [READ_LED_IO_EXT_REG] = WT_ReadLedIOExtReg,
        [WRITE_LED_IO_EXT_REG] = WT_WriteLedIOExtReg,
        [WRITE_LED_IO_EXT_BIT] = WT_WriteLedIOExtBit,
        [READ_LED_IO_EXT_BIT] = WT_ReadLedIOExtBit,
        //开关板PA
        [WRITE_SWITCH_PA] = WT_WriteSwitchPa,
        [READ_SWITCH_PA] = WT_ReadSwitchPa,
        //开关板42553
        [WRITE_SWITCH_42553] = WT_WriteSwitch42553,
        [READ_SWITCH_42553] = WT_ReadSwitch42553,
        //时钟板ADF4002
        [WRITE_REF_PLL_ADF4002] = WT_WriteRefPllAdf4002,
        [READ_REF_PLL_ADF4002] = WT_ReadRefPllAdf4002,
        //开关板(2X2/2X4)读写控制
        [SWITCH_SET_VSG_IFG_STATUS] = WT_SetSwitchVsgGapPower,
        [GET_SWITCH_VALUE_BAK] = WT_GetSwitchValueBak,
        [SWITCH_SET_VSG_IFG_CTRL_MODE] = WT_SetSwitchVsgGapPowerCtrlMode,
        //*******************CMD调试业务***************************
        //单个读写寄存器
        [WRITE_DIRECT_REG] = WT_WriteDirectReg,
        [READ_DIRECT_REG] = WT_ReadDirectReg,
        [CHECK_DIRECT_REG] = WT_CheckDirectReg,
        //批量读写寄存器
        [WRITE_DIRECT_MULTI_REG] = WT_WriteDirectMultiReg,
        [READ_DIRECT_MULTI_REG] = WT_ReadDirectMultiReg,

        //TODO:开关板Flash

        //TODO:控制板
};
#endif

struct timeval tv_start, tv_end; //临时调试延时时长用

//*******************公用基础接口*******************
//*****************************************************************************
// 功能: 回读寄存器，直到寄存器的值为设定值或超时。不相对单元板内部模块ID偏移
// 参数 [IN]：pdev:功能单元结构体指针   addr:寄存器地址   value:寄存器的设定值  mask:掩码
// 返回值：成功为0  失败错误码
//*****************************************************************************
int wt_check_direct_reg_mask(struct dev_unit *pdev, int addr, int value, int mask)
{
    int ReadCount = 0;
    while ((wt_read_direct_reg(pdev, addr) & mask) != (value & mask))
    {
        if (ReadCount++ > REG_CHECK_COUNT)
        {
            dbg("WT_REG_CHECK_OVERTIME,ADDR=0x%X,Result=x%X, !=0x%X\n", addr, wt_read_direct_reg(pdev, addr) & mask, value & mask);
            return WT_REG_CHECK_OVERTIME;
        }
        udelay(1);
    }
    return WT_OK;
}

//*****************************************************************************
// 功能: 回读寄存器，直到寄存器的值为设定值或超时。不相对单元板内部模块ID偏移
// 参数 [IN]：pdev:功能单元结构体指针   addr:寄存器地址   value:寄存器的设定值
// 返回值：成功为0  失败错误码
//*****************************************************************************
int wt_check_direct_reg(struct dev_unit *pdev, int addr, int value)
{
    int ReadCount = 0;
    if(GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        return WT_OK;
    }
    while (wt_read_direct_reg(pdev, addr) != value)
    {
        if (ReadCount++ > REG_CHECK_COUNT)
        {
            dbg("WT_REG_CHECK_OVERTIME,ADDR=0x%X,Result=x%X, !=0x%X\n", addr, wt_read_direct_reg(pdev, addr), value);
            return WT_REG_CHECK_OVERTIME;
        }
        udelay(1);
    }
    return WT_OK;
}

//*****************************************************************************
// 功能: 直接写IO内存对应的寄存器组数据，不相对单元板内部模块ID偏移(VSA/VSG/BackPlane Unit)
// 参数 [IN]：pdev:功能单元结构体指针  pRegType：寄存器组首地址 count：寄存器个数
// 返回值：成功为0  失败为-1
//*****************************************************************************
int wt_write_direct_multi_reg(struct RegType *pRegType, int count, struct dev_unit *pdev)
{
    int i = 0;

    if (pRegType == NULL || count < 1 || count > PCIE_REG_BAR_SIZE)
    {
        dbg("wt_write_direct_multi_reg param error!\n");
        return WT_REG_ARGS_ERROR;
    }

    while (i < count)
    {
        if ((pRegType[i].Addr < PCIE_REG_BAR_SIZE) && (pRegType[i].Addr % 4 == 0))
        {
            wt_write_direct_reg(pdev, pRegType[i].Addr, pRegType[i].Data);
        }
        else
        {
            dbg("wt_write_direct_multi_reg pRegType[%d].Addr error!", i);
            return WT_REG_ADDR_ERROR;
        }
        i++;
    }

    return WT_OK;
}

//*****************************************************************************
// 功能: 直接读IO内存对应的寄存器组数据，不相对单元板内部模块ID偏移(VSA/VSG/BackPlane Unit)
// 参数 [IN]：pdev:功能单元结构体指针  pRegType：寄存器组首地址 count：寄存器个数
// 返回值：寄存器数据
//*****************************************************************************
int wt_read_direct_multi_reg(struct RegType *pRegType, int count, struct dev_unit *pdev)
{
    int i = 0;

    if (pRegType == NULL || count < 1 || count > PCIE_REG_BAR_SIZE)
    {
        dbg("wt_read_direct_multi_reg param error!\n");
        return WT_REG_ARGS_ERROR;
    }

    while (i < count)
    {
        if ((pRegType[i].Addr < PCIE_REG_BAR_SIZE) && (pRegType[i].Addr % 4 == 0))
        {
            pRegType[i].Data = wt_read_direct_reg(pdev, pRegType[i].Addr);
        }
        else
        {
            dbg("wt_read_direct_multi_reg  pRegType[%d].Addr error!", i);
            return WT_REG_ADDR_ERROR;
        }
        i++;
    }

    return WT_OK;
}

//*****************************************************************************
// 功能: SPI直接写传输控制，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  SPIConfig:ctrl2寄存器配置数据
// 参数 [IN]：TXData：TX寄存器数据   Reg：传输控制寄存器组首地址
// 返回值：成功为0  失败为-1
//*****************************************************************************
int wt_bp_spi_direct_for_write(int SPIConfig, int TXData, const unsigned int *Reg, struct dev_unit *pdev)
{
    int ReadCount = 0; //读取的次数

    if(GetVirtualAddrMode(pdev->dev_virtual_addr))
    {
        wt_write_direct_reg(pdev, Reg[SPI_REG_TX_TYPE], TXData);
        return WT_OK;
    }
    //写数据到TX寄存器
    wt_write_direct_reg(pdev, Reg[SPI_REG_TX_TYPE], TXData);

    //配置SPI参数
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL2_TYPE], SPIConfig);

    //开始发送
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x0);
    wt_check_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x0);
    udelay(1);
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x1);
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x0);

    //查询，等待发送完成
    do
    {
        if (ReadCount++ > SPI_READ_COUNT)
        {
            dbg("ReadCount > SPI_READ_COUNT!\n");
            return WT_SPI_WRITE_CHECK_OVERTIME;
        }
    } while ((wt_read_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE]) & 0x02) == 0x02);

    return WT_OK;
}

//*****************************************************************************
// 功能: SPI直接读传输控制，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  SPIConfig:ctrl2寄存器配置数据
// 参数 [IN]：Reg：传输控制寄存器组首地址  TXData：TX寄存器数据
// 参数 [OUT]：pData：RX寄存器数据
// 返回值：成功为0  失败为-1
//*****************************************************************************
int wt_bp_spi_direct_for_read(int SPIConfig, int TXData, int *pData, unsigned int *Reg, struct dev_unit *pdev)
{
    int ReadCount = 0;

    //写数据到TX寄存器
    wt_write_direct_reg(pdev, Reg[SPI_REG_TX_TYPE], TXData);

    //配置SPI参数
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL2_TYPE], SPIConfig);

    //开始发送
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x0);
    wt_check_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x0);
    udelay(1);
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x1);
    wt_write_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE], 0x0);
    //查询，等待发送完成
    do
    {
        if (ReadCount++ > SPI_READ_COUNT)
        {
            dbg("ReadCount > SPI_READ_COUNT!\n");
            return WT_SPI_READ_CHECK_OVERTIME;
        }
    } while ((wt_read_direct_reg(pdev, Reg[SPI_REG_CTRL1_TYPE]) & 0x02) == 0x02);

    //读取数据,返回读回的数据
    *pData = wt_read_direct_reg(pdev, Reg[SPI_REG_RX_TYPE]);

    return WT_OK;
}

//*****************************************************************************
// 功能: 背板SMBus直接写传输控制(BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  SMBusConfig:ctrl2寄存器配置数据
// 参数 [IN]：Reg：寄存器组地址 Data：寄存器数据
// 参数 [IN]：DiveceAddr：芯片片选地址     DiveceComReg：芯片内部地址/命令
// 返回值：成功为0  失败为-1
//*****************************************************************************
int wt_smbus_for_write(int DiveceAddr, int DiveceComReg, int SMBusConfig, int Data, unsigned int *Reg, struct dev_unit *pdev)
{
    int ReadCount = 0; //读取的次数
    //器件地址，低8位有效
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_SLV_ADDR_TYPE], DiveceAddr & 0xFF);
    //器件命令寄存器ID，低8位有效
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_COM_REG_TYPE], DiveceComReg);
    //写数据到TX寄存器，低16位有效
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_TX_TYPE], Data & 0xFFFFFFFF);
    //写操作
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL2_TYPE], SMBusConfig);
    //开始发送
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
    wt_check_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
    udelay(60); //延时使FPGA抓到有效上升沿
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x1);
    //查询，等待发送完成
    do
    {
        if (ReadCount++ > SMBUS_READ_COUNT)
        {
            wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
            dbg("ReadCount++ > SMBUS_READ_COUNT!\n");
            return WT_SMBUS_WRITE_CHECK_OVERTIME;
        }
        udelay(1);
    } while ((wt_read_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE]) & 0x2) == 0x2);

    if ((wt_read_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE]) & 0x4) == 0x4)
    {
        dbg("wt_smbus_for_write Write Operation failed!\n");
        wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
        return WT_SMBUS_WRITE_FAILED;
    }
    return WT_OK;
}

//*****************************************************************************
// 功能: 背板SMBus直接读传输控制(BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  SMBusConfig:ctrl2寄存器配置数据  Reg：寄存器组地址
// 参数 [IN]：DiveceAddr：芯片片选地址     DiveceComReg：芯片内部地址/命令
// 参数 [OUT]：Data：寄存器数据
// 返回值：成功为0  失败为-1
//*****************************************************************************
int wt_smbus_for_read(int DiveceAddr, int DiveceComReg, int SMBusConfig, int *pData, unsigned int *Reg, struct dev_unit *pdev)
{
    int ReadCount = 0;
    //器件地址，低8位有效
    //wt_write_direct_reg(pdev, Reg[SMBUS_REG_SLV_ADDR_TYPE], (DiveceAddr & 0xFF) >> 0x1);//芯片地址右移一位
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_SLV_ADDR_TYPE], DiveceAddr & 0xFF);
    //器件命令寄存器ID，低8位有效
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_COM_REG_TYPE], DiveceComReg);
    //写数据到TX寄存器，低8位有效
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_TX_TYPE], *pData & 0xFFFFFFFF);
    //读操作
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL2_TYPE], SMBusConfig);
    //开始发送
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
    wt_check_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
    udelay(60); //延时使FPGA抓到有效上升沿
    wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x1);
    //do_gettimeofday(&tv_start);

    //查询，等待发送完成
    do
    {
        if (ReadCount++ > SMBUS_READ_COUNT)
        {
            dbg("ReadCount > SMBUS_READ_COUNT!\n");
            wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
            return WT_SMBUS_READ_CHECK_OVERTIME;
        }
        udelay(1);
    } while ((wt_read_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE]) & 0x2) == 0x2);

    if ((wt_read_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE]) & 0x4) == 0x4)
    {
        dbg("wt_smbus_for_read Read Operation failed!\n");
        wt_write_direct_reg(pdev, Reg[SMBUS_REG_CTRL1_TYPE], 0x0);
        //超时之后照常读取数据并返回错误
        *pData = wt_read_direct_reg(pdev, Reg[SMBUS_REG_RX_TYPE]);
        return WT_SMBUS_READ_FAILED;
    }

    //读取数据,返回读回的数据
    *pData = wt_read_direct_reg(pdev, Reg[SMBUS_REG_RX_TYPE]);
    return WT_OK;
}

//*******************单元板(背板/VSA、VSG业务板)公共接口*******************
// 获取单元板硬件版本
int wt_GetUnitBoardHWVersion(struct dev_unit *pdev)
{
    int HWVersion;
    if (pdev->type == DEV_TYPE_BACK)
    {
        HWVersion = wt_read_direct_reg(pdev, FPGA_BP_HW_VERSION);
        //pdev->testertype = HW_WT428;
        pdev->testertype = (HWVersion >> 8) & 0xFF;

        if(pdev->testertype == HW_WT428C)//FENG : WT428C_V2转 328CE的特殊操作。。。
        {
            pdev->testertype = HW_WT418;
        }

        if (pdev->testertype == HW_WT418)
        {
            // wt_write_direct_reg(pdev, BUSI_HW_VER_SEL, 0);
            // pdev->version = wt_read_direct_reg(pdev, BUSI_HW_VER);
            wt_write_direct_reg(pdev, BUSI_HW_VER_SEL, 1);
            pdev->version = wt_read_direct_reg(pdev, BUSI_HW_VER);
            pdev->swbversion = wt_read_direct_reg(pdev, BUSI_HW_VER);
        }
        //WT448 VA仪器，硬件版本号管理不完善，特殊处理
        else if (pdev->testertype == HW_WT448 && ((HWVersion & 0x7) == 0x7))
        {
            pdev->version = 0;
            pdev->swbversion = 0;
        }
        else
        {
            pdev->version = HWVersion & 0x7;
            HWVersion = wt_read_direct_reg(pdev, FPGA_SW_HW_VERSION);
            pdev->swbversion = HWVersion & 0x7;
        }
    }
    else if (pBPdev)
    {
        pdev->testertype = pBPdev->testertype;
        if(pdev->testertype == HW_WT418)
        {
            // wt_write_direct_reg(pdev, BUSI_HW_VER_SEL, 0);
            // pdev->version = wt_read_direct_reg(pdev, BUSI_HW_VER);
            wt_write_direct_reg(pBPdev, BUSI_HW_VER_SEL, 1);
            pdev->version = wt_read_direct_reg(pBPdev, BUSI_HW_VER);
            pdev->swbversion = wt_read_direct_reg(pBPdev, BUSI_HW_VER);
        }
        //WT448 VA仪器，硬件版本号管理不完善，特殊处理
        else if (pBPdev->testertype == HW_WT448 && pBPdev->version == 0)
        {
            pdev->version = 0;
        }
        else
        {
            HWVersion = wt_read_direct_reg(pBPdev, FPGA_BB_HW_VERSION);
            pdev->version = (HWVersion >> (pdev->slot * 3)) & 0x7;
        }
    }
    else
    {
        pdev->testertype = HW_WT448;
        pdev->version = 0;
        dbg("BackPlane not exist, Get BusiBoard version failed!");
        return WT_UNITBOARD_NOT_EXIST;
    }
    dbg_print("WT_GetUnitBoardHWVersion Type%d, Number%d, version%d, testertype%d\n",
           pdev->type, pdev->number, pdev->version, pdev->testertype);
    return WT_OK;
}

int WT_SetUnitBoardPciDelay(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegData;

    // 拷贝写操作信息到内核空间
    if (copy_from_user(&RegData, arg, DataLength))
    {
        dbg("WT_SetUnitBoardPciDelay RomDataInfo copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (RegData.Data > 0)
    {
        pdev->write_delay = RegData.Data;
    }

    if (RegData.Addr > 0)
    {
        pdev->read_delay = RegData.Addr;
    }
    dbg_print("WT_SetUnitBoardPciDelay Type%d, Number%d, write_delay%d, read_delay%d\n",
           pdev->type, pdev->number, pdev->write_delay, pdev->read_delay);
    return WT_OK;
}

int WT_GetUnitBoardHWVersion(int DataLength, void *arg, struct dev_unit *pdev)
{
    if (pdev->type != DEV_TYPE_BACK)
    {
        wt_GetUnitBoardHWVersion(pdev);
    }

    if (copy_to_user(arg, &pdev->version, DataLength))
    {
        dbg("WT_GetUnitBoardHWVersion info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

//单元板FPGA信息
int WT_GetUnitBoardFPGAInfo(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct UnitFPGAInfo FPGAInfo;

    //读取单元板FPGA信息
    FPGAInfo.FPGAVersion = wt_read_direct_reg(pdev, FPGA_VERSION);
    FPGAInfo.FPGADate = wt_read_direct_reg(pdev, FPGA_COMPILE_DATE);
    FPGAInfo.FPGATime = wt_read_direct_reg(pdev, FPGA_COMPILE_TIME);
    FPGAInfo.BoardInfo = wt_read_direct_reg(pdev, FPGA_MODULE_VERSION);

    if (copy_to_user(arg, &FPGAInfo, DataLength))
    {
        dbg("WT_GetUnitBoardFPGAInfo info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//单元板修订版本
int WT_GetUnitBoardRevisionId(int DataLength, void *arg, struct dev_unit *pdev)
{
    int RevisionId;
    RevisionId = pdev->revision;
    if (copy_to_user(arg, &RevisionId, DataLength))
    {
        dbg("WT_GetUnitBoardRevisionId info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

int wt_WriteRomPage(int Addr, int *pBuf, unsigned int *Reg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int i = 0;
    int ReadCount = 0;
    //往FIFO缓冲区写数据
    while (i < (Reg[ROM_PAGE_SIZE] / 4))
    {
        //每次写入4个字节
        wt_write_direct_reg(pdev, Reg[ROM_REG_WR_DATA], pBuf[i++]);
    }

    //配置目的地址(按Page的首地址开始写)
    wt_write_direct_reg(pdev, Reg[ROM_REG_WR_ADDR], Addr);

    //判断当前PAGE的首地址是否为Sector的首地址
    if (Reg[ROM_REG_SECTOR_ERASE])
    {
        if ((Addr & 0xFFFF) == 0)
        {
            //配置FLASH Sector擦除为1，表示擦除该Sector
            wt_write_direct_reg(pdev, Reg[ROM_REG_SECTOR_ERASE], 1);
        }
        else
        {
            //配置FLASH Sector擦除为0，表示不擦除该Sector
            wt_write_direct_reg(pdev, Reg[ROM_REG_SECTOR_ERASE], 0);
        }
    }

    //发送启动信号
    wt_write_direct_reg(pdev, Reg[ROM_REG_WR_START], 0x1);
    ret = wt_check_direct_reg(pdev, Reg[ROM_REG_WR_START], 0x1);
    retAssert(ret, "ROM_REG_WR_START check timeout\n");
    //当延时为1、2us时，可能出现连续写(257page)失败的情况。
    //出错时，逻辑抓到两次写Reg[ROM_REG_WR_START]的延时仅100ns.
    udelay(1);
    wt_write_direct_reg(pdev, Reg[ROM_REG_WR_START], 0x0);

    //do_gettimeofday(&tv_start);
    do
    {
        if (ReadCount++ > 4 * FLASH_WAITE_COUNT)
        {
            dbg("Check Flash status ReadCount > FLASH_WAITE_COUNT!\n");
            //do_gettimeofday(&tv_end);
            dbg("BACK_FLASH_WR_DONE Used Time is:%ld s, %ld us\n", (tv_end.tv_sec - tv_start.tv_sec), (tv_end.tv_usec - tv_start.tv_usec));
            return WT_FLASH_STATUS_CHECK_OVERTIME;
        }
        udelay(4);
    } while ((wt_read_direct_reg(pdev, Reg[ROM_REG_WR_DONE]) & 0x1) != 1);
    //do_gettimeofday(&tv_end);
    //dbg("BACK_FLASH_WR_DONE Used Time is:%ld s, %ld us\n", (tv_end.tv_sec - tv_start.tv_sec), (tv_end.tv_usec - tv_start.tv_usec));
    return WT_OK;
}

int wt_ReadRomPage(int Addr, int *pBuf, unsigned int *Reg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int i = 0;
    int ReadCount = 0;
    //配置FLASH读取长度
    wt_write_direct_reg(pdev, Reg[ROM_REG_RD_LEN], Reg[ROM_PAGE_SIZE] / 4);

    //配置基地址(不必  按Page的首地址开始读)
    wt_write_direct_reg(pdev, Reg[ROM_REG_RD_ADDR], Addr);

    //发送启动信号
    wt_write_direct_reg(pdev, Reg[ROM_REG_RD_START], 0x0);
    ret = wt_check_direct_reg(pdev, Reg[ROM_REG_RD_START], 0x0);
    retAssert(ret, "ROM_REG_WR_START check timeout\n");
    udelay(1);
    wt_write_direct_reg(pdev, Reg[ROM_REG_RD_START], 0x1);

#if 1
    //读取FLASH数据到缓冲区
    while (i < Reg[ROM_PAGE_SIZE] / 4)
    {
        //判断FIFO空状态
        while (wt_read_direct_reg(pdev, Reg[ROM_REG_RD_EMPTY]) == 0x1)
        {
            if (ReadCount++ > 4 * FLASH_WAITE_COUNT)
            {
                dbg("FLASH_SPI_RD_EMPTY ReadCount > FLASH_WAITE_COUNT!  i=%d,   ReadCount=%d\n", i, ReadCount);
                return WT_FLASH_READ_EMPTY_OVERTIME;
            }
            udelay(1);
        }
        //每次读取4个字节
        pBuf[i++] = wt_read_direct_reg(pdev, Reg[ROM_REG_RD_DATA]);
    }
#endif
    return WT_OK;
}

int WT_WriteRomPage(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int Buf[128] = {0};
    int *pBuf = Buf;
    struct RomDataInfoType RomDataInfo;
    unsigned int Reg[ROM_REG_END] = {0}; //器件所映射的寄存器地址

    //拷贝写操作信息到内核空间
    if (copy_from_user(&RomDataInfo, arg, DataLength))
    {
        dbg("WT_WriteUnitBoardFlash RomDataInfo copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (pdev->type == DEV_TYPE_BACK)
    {
        if(RomDataInfo.ChipID == SWITCH_1_FLASH)
        {
            Reg[ROM_REG_RD_START]   = BACK_SW_1_FLASH_RD_START;
            Reg[ROM_REG_RD_LEN]     = BACK_SW_1_FLASH_RD_LEN;
            Reg[ROM_REG_RD_DATA]    = BACK_SW_1_FLASH_RD_DATA;
            Reg[ROM_REG_RD_ADDR]    = BACK_SW_1_FLASH_RD_ADDR;
            Reg[ROM_REG_RD_EMPTY]   = BACK_SW_1_FLASH_EMTPY;
            Reg[ROM_PAGE_SIZE]      = FLASH_PAGE_SIZE;
        }
        else
        {
            Reg[ROM_REG_RD_START]   = BACK_SW_2_FLASH_RD_START;
            Reg[ROM_REG_RD_LEN]     = BACK_SW_2_FLASH_RD_LEN;
            Reg[ROM_REG_RD_DATA]    = BACK_SW_2_FLASH_RD_DATA;
            Reg[ROM_REG_RD_ADDR]    = BACK_SW_2_FLASH_RD_ADDR;
            Reg[ROM_REG_RD_EMPTY]   = BACK_SW_2_FLASH_EMTPY;
            Reg[ROM_PAGE_SIZE]      = FLASH_PAGE_SIZE;
        }
    }
    else
    {
        Reg[ROM_REG_RD_START]   = BUSI_FLASH_RD_START;
        Reg[ROM_REG_RD_LEN]     = BUSI_FLASH_RD_LEN;
        Reg[ROM_REG_RD_DATA]    = BUSI_FLASH_RD_DATA;
        Reg[ROM_REG_RD_ADDR]    = BUSI_FLASH_RD_ADDR;
        Reg[ROM_REG_RD_EMPTY]   = BUSI_FLASH_EMTPY;
        Reg[ROM_PAGE_SIZE]      = FLASH_PAGE_SIZE;
    }

    //拷贝缓冲区数据到内核空间
    if (copy_from_user(Buf, RomDataInfo.pData, Reg[ROM_PAGE_SIZE] > 128 ? 128 : Reg[ROM_PAGE_SIZE]))
    {
        dbg("WT_WriteUnitBoardFlash FlashData copy_from_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    dbg("WT_WriteUnitBoardFlashPage,ChipID=%d!\n", RomDataInfo.ChipID);
    dbg("write page %d pBuf[%d]=%d\n", RomDataInfo.BaseAddr / Reg[ROM_PAGE_SIZE], 0, pBuf[0]);
    dbg("write page %d pBuf[%d]=%d\n", RomDataInfo.BaseAddr / Reg[ROM_PAGE_SIZE], Reg[ROM_PAGE_SIZE] / 4 - 1, pBuf[Reg[ROM_PAGE_SIZE] / 4 - 1]);

    ret = wt_WriteRomPage(RomDataInfo.BaseAddr, pBuf, Reg, pdev);

    mdelay(5); //FLASH_STATUS仅指示通讯完成，按规格，写one PAGE需要1.4Ms-5Ms。
    return WT_OK;
}

int WT_ReadRomPage(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int Buf[FLASH_PAGE_SIZE / 4] = {0};
    int *pBuf = Buf;
    struct RomDataInfoType RomDataInfo;
    unsigned int Reg[ROM_REG_END] = {0}; //器件所映射的寄存器地址

    //拷贝写操作信息到内核空间
    if (copy_from_user(&RomDataInfo, arg, DataLength))
    {
        dbg("WT_ReadUnitBoardFlash RomDataInfo copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    dbg("WT_ReadUnitBoardFlashPage,ChipID=%d!\n", RomDataInfo.ChipID);

    if (pdev->type == DEV_TYPE_BACK)
    {
        if(RomDataInfo.ChipID == SWITCH_1_FLASH)
        {
            Reg[ROM_REG_RD_START]   = BACK_SW_1_FLASH_RD_START;
            Reg[ROM_REG_RD_LEN]     = BACK_SW_1_FLASH_RD_LEN;
            Reg[ROM_REG_RD_DATA]    = BACK_SW_1_FLASH_RD_DATA;
            Reg[ROM_REG_RD_ADDR]    = BACK_SW_1_FLASH_RD_ADDR;
            Reg[ROM_REG_RD_EMPTY]   = BACK_SW_1_FLASH_EMTPY;
            Reg[ROM_PAGE_SIZE]      = FLASH_PAGE_SIZE;
        }
        else
        {
            Reg[ROM_REG_RD_START]   = BACK_SW_2_FLASH_RD_START;
            Reg[ROM_REG_RD_LEN]     = BACK_SW_2_FLASH_RD_LEN;
            Reg[ROM_REG_RD_DATA]    = BACK_SW_2_FLASH_RD_DATA;
            Reg[ROM_REG_RD_ADDR]    = BACK_SW_2_FLASH_RD_ADDR;
            Reg[ROM_REG_RD_EMPTY]   = BACK_SW_2_FLASH_EMTPY;
            Reg[ROM_PAGE_SIZE]      = FLASH_PAGE_SIZE;
        }
    }
    else
    {
        Reg[ROM_REG_RD_START]   = BUSI_FLASH_RD_START;
        Reg[ROM_REG_RD_LEN]     = BUSI_FLASH_RD_LEN;
        Reg[ROM_REG_RD_DATA]    = BUSI_FLASH_RD_DATA;
        Reg[ROM_REG_RD_ADDR]    = BUSI_FLASH_RD_ADDR;
        Reg[ROM_REG_RD_EMPTY]   = BUSI_FLASH_EMTPY;
        Reg[ROM_PAGE_SIZE]      = FLASH_PAGE_SIZE;
    }

    //选择SPI通道
    ret = wt_ReadRomPage(RomDataInfo.BaseAddr, pBuf, Reg, pdev);

    //拷贝缓冲区数据到用户空间
    if (copy_to_user(RomDataInfo.pData, Buf, Reg[ROM_PAGE_SIZE]))
    {
        dbg("WT_ReadUnitBoardFlash FlashData copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

//电压侦测
int wt_ReadUBVoltReg(int DeviceId, int Addr, int *Data, struct dev_unit *pdev)
{
    return WT_OK;
}

int wt_WriteUBVoltReg(int DeviceId, int Addr, int Data, struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_WriteUnitBoardVoltReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_ReadUnitBoardVoltReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_ReadUBVoltChannelValue(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

//温度传感器
int WT_ReadUBTemperature(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

//*******************业务单元板(VSA/VSG)通用接口*******************
//*******************VSA单元类型*******************
int WT_GetTrigAddrStart(int DataLength, void *arg, struct dev_unit *pdev)
{
    int StartAddr = 0;

    //TODO:已预留寄存器，后续看情况添加
    if (copy_to_user(arg, &StartAddr, DataLength))
    {
        dbg("WT_GetTrigAddrStart info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return WT_OK;
}

//快速AGC
int WT_SetAGCEnable(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int WT_SetAGCGateValue(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

//VSA链路常规配置
int WT_SetADCPowerDown(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

//*******************PCI寄存器接口*******************
//PCI寄存器读写
int WT_WriteDirectReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;
    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteDirectReg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    //写寄存器数据
    wt_write_direct_reg(pdev, RegTypeTemp.Addr, RegTypeTemp.Data);

    return WT_OK;
}

int WT_ReadDirectReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    struct RegType RegTypeTemp;

    ret = copy_from_user(&RegTypeTemp, arg, DataLength);
    retAssert(ret, "WT_ReadDirectReg info copy_from_user failed!\n");

    //读寄存器组数据
    RegTypeTemp.Data = wt_read_direct_reg(pdev, RegTypeTemp.Addr);

    ret = copy_to_user(arg, &RegTypeTemp, DataLength);
    retAssert(ret, "WT_ReadDirectReg info copy_to_user failed!\n");

    return WT_OK;
}

int WT_CheckDirectReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    int i = 0;
    int kk = 0;
    int ReadBackData = 0;
    int result = 0;
    struct RegType RegTypeTemp;
    ret = copy_from_user(&RegTypeTemp, arg, DataLength);
    retAssert(ret, "WT_CheckDirectReg info copy_from_user failed!\n");

    for (kk = 0; kk < CHECK_DITECT_REG_COUNT && result == 0; kk++)
    {
        //写寄存器数据
        wt_write_direct_reg(pdev, RegTypeTemp.Addr, kk);
        //读寄存器组数据
        ReadBackData = wt_read_direct_reg(pdev, RegTypeTemp.Addr);
        for (i = 0; i < 100 && ReadBackData != kk; i++)
        {
            result = 1;
            udelay(1);
            dbg_print("WT_CheckDirectReg type%d, mod%d, addr%#x, readback=%#x, wdata=%#x\n",
                   pdev->type, pdev->id, RegTypeTemp.Addr, ReadBackData, kk);
            ReadBackData = wt_read_direct_reg(pdev, RegTypeTemp.Addr);
        }
    }
    RegTypeTemp.Data = result;
    dbg_print("WT_CheckDirectReg Type%d, Id%d, Addr%#x, count%d, result = %d\n",
           pdev->type, pdev->id, RegTypeTemp.Addr, kk, result);

    ret = copy_to_user(arg, &RegTypeTemp, DataLength);
    retAssert(ret, "WT_CheckDirectReg info copy_to_user failed!\n");
    return WT_OK;
}

int WT_WriteDirectMultiReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    int i = 0;
    int RegCount = DataLength;
    struct RegType *pRegType = (struct RegType *)kmalloc(sizeof(struct RegType) * RegCount, GFP_KERNEL);

    if (pRegType == NULL)
    {
        dbg("WT_WriteDirectMultiReg kmalloc failed!\n");
        return WT_ALLOC_FAILED;
    }

    if (copy_from_user(&pRegType, arg, sizeof(struct RegType) * RegCount) == -1)
    {
        dbg("WT_WriteMultiReg info copy_from_user failed!\n");
        kfree(pRegType);
        return WT_CPY_FROM_USR_FAILED;
    }

    //写寄存器组数据
    for (i = 0; i < RegCount; i++)
    {
        wt_write_direct_reg(pdev, pRegType[i].Addr, pRegType[i].Data);
    }

    kfree(pRegType);

    return ret;
}

int WT_ReadDirectMultiReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    int i = 0;
    int RegCount = DataLength;
    struct RegType *pRegType = (struct RegType *)kmalloc(sizeof(struct RegType) * RegCount, GFP_KERNEL);

    if (pRegType == NULL)
    {
        dbg("WT_ReadDirectMultiReg kmalloc failed!\n");
        return WT_ALLOC_FAILED;
    }

    if (copy_from_user(&pRegType, arg, sizeof(struct RegType) * DataLength) == -1)
    {
        dbg("WT_ReadMultiReg info copy_from_user failed!\n");
        kfree(pRegType);
        return WT_CPY_FROM_USR_FAILED;
    }

    //读寄存器组数据
    for (i = 0; i < RegCount; i++)
    {
        pRegType[i].Data = wt_read_direct_reg(pdev, pRegType[i].Addr);
    }

    //返回读取的数据
    if (copy_to_user(arg, pRegType, sizeof(struct RegType) * RegCount) == -1)
    {
        dbg("WT_ReadMultiReg info copy_to_user failed!\n");
        kfree(pRegType);
        return WT_CPY_TO_USR_FAILED;
    }

    kfree(pRegType);

    return ret;
}

//*******************加密芯片*******************
int WT_CryptoMemInit(int DataLength, void *arg, struct dev_unit *pdev)
{
    u8 buf[256] = {0};
    int ret = 0;
    int isinit = 0;
    s32 i, j;
    u8 ucCi[8] = {0};
    struct CryptoMemInfoType CMInfo;

    dbg_print("==========WT_CryptoMemInit");
    if (copy_from_user(&CMInfo, arg, DataLength) == -1)
    {
        dbg("WT_EncryptInit info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    //Reset all registers
    cm_ResetCrypto();
    at88sc_wait_lock(pdev, 30);

    do
    {
        /* step 1 测试芯片通讯是否正常 */
        buf[0] = 7;
        buf[1] = 1;
        at88sc_write_config_zone(pdev, 0x0a, 2, buf);
        memset(buf, 0, 256);
        at88sc_read_config_zone(pdev, 0x0a, 2, buf);
        dbg_print("buf[0]=%d,buf[1]=%d\n", buf[0], buf[1]);
        if ((buf[0] != 7) || (buf[1] != 1))
        {
            ret = WT_CM_COMMUNICATION_FAILED;
            break;
        }
        dbg_print("[step 01]AT88SC0104 communication test          [OK]\n");

        /* step 2 检查芯片是否已经被初始化 */
        isinit = at88sc_is_initial(pdev);
        dbg_print("[step 02]chip initialization check              [OK]\n");

        /* step 3 验证 write 7 password */
        if (isinit == 0)
        {
            dbg_print("[step 03]use password 1\n");
            buf[0] = 0xeb;
            buf[1] = 0xc2;
            buf[2] = 0x77;
        }
        else
        {
            dbg_print("[step 03]use password 2\n");
            buf[0] = 0xdd;
            buf[1] = 0x42;
            buf[2] = 0x97;
        }
        at88sc_verify_password(pdev, 7, buf, VerifyPpasswordWrite);
        memset(buf, 0, 256);
        at88sc_read_config_zone(pdev, 0xe8, 8, buf);
        ret = at88sc_pac_acc_warning(buf);
        if (ret != 0)
        {
            ret = WT_CM_VERIFY_PWD1_FAILED;
            retBreak(ret, "at88sc_verify_password failed!");
        }
        dbg_print("[step 03]verify write 7 password                [OK]\n");

        /* step 4 DCR配置: AAC没有限制，PAC 8次，只验证一次checksum */
        buf[0] = 0x8f; //出厂模式，只可一次写入信息
        at88sc_write_config_zone(pdev, 0x18, 1, buf);
        memset(buf, 0, 256);
        at88sc_read_config_zone(pdev, 0x18, 1, buf);
        if (buf[0] != 0x8f)
        {
            ret = WT_CM_SET_CONFIG_REG_FAILED;
            retBreak(ret, "buf[0] != 0x0f");
        }
        dbg_print("[step 04]setting Device Configuration Register  [OK]\n");

        /* step 5 采用认证模式，初始化C0~C3 */
        for (i = 0; i < 4; i++)
        {
            ucCi[0] = 0xff;

            generate_random(ucCi + 1, 7);
            at88sc_write_config_zone(pdev, 0x51 + 0x10 * i, 7, ucCi + 1);
            memset(buf, 0, 256);
            at88sc_read_config_zone(pdev, 0x50 + 0x10 * i, 8, buf);
            for (j = 0; j < 8; j++)
            {
                if (ucCi[j] != buf[j])
                {
                    dbg_print("ucCi[%d] = %x, buf[%d]=%x\n", j, ucCi[j], j, buf[j]);
                    ret = 1;
                }
            }
            if (ret == 1)
            {
                break;
            }
        }
        if (ret != 0)
        {
            ret = WT_CM_INIT_CI_VALUE_FAILED;
            retBreak(ret, "program initial values for C0~C3 failed!");
        }
        dbg_print("[step 05]program initial values for C0~C3       [OK]\n");

        /* step 6 初始化G0~G3 */
        for (i = 0; i < 4; i++)
        {
            at88sc_write_config_zone(pdev, 0x90 + 8 * i, 8, &Gi[i][0]);
            memset(buf, 0, 256);
            at88sc_read_config_zone(pdev, 0x90 + 8 * i, 8, buf);
            for (j = 0; j < 8; j++)
            {
                if (Gi[i][j] != buf[j])
                {
                    dbg_print("Gi[%d][%d] = %x, buf[%d]=%x\n", i, j, Gi[i][j], j, buf[j]);
                    ret = 1;
                }
            }
            if (ret == 1)
            {
                break;
            }
        }
        if (ret != 0)
        {
            ret = WT_CM_INIT_GI_VALUE_FAILED;
            retBreak(ret, "program initial values for G0~G3 failed!");
        }
        dbg_print("[step 06]program initial values for G0~G3       [OK]\n");

        /* step 7 初始化密码区 */
        for (i = 0; i < 16; i++)
        {
            at88sc_write_config_zone(pdev, 0xb1 + 4 * i, 3, &Password[i][0]); /* 设置写密码 */
            memset(buf, 0, 256);
            at88sc_read_config_zone(pdev, 0xb0 + 4 * i, 4, buf);
            if (buf[0] != 0xff)
                ret = 1;

            for (j = 0; j < 3; j++)
            {
                if (Password[i][j] != buf[j + 1])
                {
                    dbg_print("Password[%d][%d] = %x, buf[%d]=%x\n", i, j, Password[i][j], j + 1, buf[j + 1]);
                    ret = 1;
                }
            }
            if (ret == 1)
            {
                break;
            }
        }
        if (ret != 0)
        {
            ret = WT_CM_DEF_PWD_FAILED;
            retBreak(ret, "define the values of passwords failed!");
        }
        dbg_print("[step 07]define the values of passwords         [OK]\n");

        /* step 8 初始化AR、PR */
        for (i = 0; i < 4; i++)
        {
            buf[0] = 0xff;
            buf[1] = 0xff;
            at88sc_write_config_zone(pdev, 0x20 + 2 * i, 2, buf);
            memset(buf, 0, 256);
            at88sc_read_config_zone(pdev, 0x20 + 2 * i, 2, buf);
            for (j = 0; j < 2; j++)
            {
                if (0xff != buf[j])
                {
                    dbg_print("buf[%d]=%x\n", j, buf[j]);
                    ret = 1;
                }
            }
            if (ret != 0)
            {
                break;
            }
        }
        if (ret != 0)
        {
            ret = WT_CM_INIT_AR_PR_FAILED;
            retBreak(ret, "initialize AR0~3, PR0~4 with 0xff failed!");
        }
        dbg_print("[step 08]initialize AR0~3, PR0~4 with 0xff      [OK]\n");

        /* step 9 初始化user zone */
        //user zone 0初始化设备类型
        memset(buf, 0, 256);
        strcpy(buf, CMInfo.DevType);
        at88sc_write_user_zone(pdev, 0, 0, 16, buf);
        at88sc_write_user_zone(pdev, 0, 16, 16, buf + 16);
        memset(buf, 0, 256);
        at88sc_read_user_zone(pdev, 0, 0, 16, buf);
        at88sc_read_user_zone(pdev, 0, 16, 16, buf + 16);
        //printk("DevType=%s\n",buf);
        if (strcmp(buf, CMInfo.DevType) != 0)
        {
            ret = WT_CM_INIT_USR_ZONE_FAILED;
        }
        retBreak(ret, "Init DevType failed!");

        //user zone 1初始化设备InnerSN码
        memset(buf, 0, 256);
        strcpy(buf, CMInfo.InnerSN);
        at88sc_write_user_zone(pdev, 1, 0, 16, buf);
        at88sc_write_user_zone(pdev, 1, 16, 16, buf + 16);
        memset(buf, 0, 256);
        at88sc_read_user_zone(pdev, 1, 0, 16, buf);
        at88sc_read_user_zone(pdev, 1, 16, 16, buf + 16);
        //printk("InnerSN=%s\n",buf);
        if (strcmp(buf, CMInfo.InnerSN) != 0)
        {
            ret = WT_CM_INIT_USR_ZONE_FAILED;
        }
        retBreak(ret, "Init InnerSN failed!");

        //user zone 2写入j + 2 * 32
        memset(buf, 0, 256);
        for (j = 0; j < 32; j++)
        {
            buf[j] = j + 2 * 32;
        }
        at88sc_write_user_zone(pdev, 2, 0, 16, buf);
        at88sc_write_user_zone(pdev, 2, 16, 16, buf + 16);
        memset(buf, 0, 256);
        at88sc_read_user_zone(pdev, 2, 0, 16, buf);
        at88sc_read_user_zone(pdev, 2, 16, 16, buf + 16);
        for (j = 0; j < 32; j++)
        {
            if (j + 2 * 32 != buf[j])
            {
                ret = 1;
            }
        }
        if (ret != 0)
        {
            ret = WT_CM_INIT_USR_ZONE_FAILED;
        }
        retBreak(ret, "Init j + 2 * 32 failed!");

        //user zone 3初始化设备特征码Code
        memset(buf, 0, 256);
        strcpy(buf, CMInfo.Code);
        at88sc_write_user_zone(pdev, 3, 0, 16, buf);
        at88sc_write_user_zone(pdev, 3, 16, 16, buf + 16);
        memset(buf, 0, 256);
        at88sc_read_user_zone(pdev, 3, 0, 16, buf);
        at88sc_read_user_zone(pdev, 3, 16, 16, buf + 16);
        //printk("Code=%s\n",buf);
        if (strcmp(buf, CMInfo.Code) != 0)
        {
            ret = WT_CM_INIT_USR_ZONE_FAILED;
        }
        retBreak(ret, "Init Code failed!");
        dbg_print("[step 09]initializing user zone 0~3             [OK]\n");

        /* step 10 设置user zone的访问权限 */
        for (i = 0; i < 4; i++)
        {
            buf[0] = 0x17;
            buf[1] = (0 | i << 6 | i | 0x38);
            at88sc_write_config_zone(pdev, 0x20 + 2 * i, 2, buf);
            memset(buf, 0, 256);
            at88sc_read_config_zone(pdev, 0x20 + 2 * i, 2, buf);
            if ((0x17 != buf[0]) || ((0 | i << 6 | i | 0x38) != buf[1]))
            {
                ret = 1;
                break;
            }
        }
        if (ret != 0)
        {
            ret = WT_CM_SET_USR_ACCESS_RIGHT_FAILED;
        }
        retBreak(ret, "setting user zone access right failed!");
        dbg_print("[step 10]setting user zone access right         [OK]\n");

        /* step 11 验证user zone 0访问 */
        memset(buf, 0, 256);
        strcpy(buf, CMInfo.DevType);
        ret = at88sc_user_zone_test(pdev, 0, buf);
        if (ret != 0)
        {
            ret = WT_CM_VERIFY_USR_ZONE0_FAILED;
        }
        retBreak(ret, "verify user zone 0 failed!");
        dbg_print("[step 11]verify user zone 0                     [OK]\n");

        /* step 12 验证user zone 1访问 */
        memset(buf, 0, 256);
        strcpy(buf, CMInfo.InnerSN);
        ret = at88sc_user_zone_test(pdev, 1, buf);
        if (ret != 0)
        {
            ret = WT_CM_VERIFY_USR_ZONE1_FAILED;
        }
        retBreak(ret, "verify user zone 1 failed!");
        dbg_print("[step 12]verify user zone 1                     [OK]\n");

        /* step 13 验证user zone 2访问 */
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 2 * 32;
        }

        ret = at88sc_user_zone_test(pdev, 2, buf);
        if (ret != 0)
        {
            ret = WT_CM_VERIFY_USR_ZONE2_FAILED;
        }
        retBreak(ret, "verify user zone 2 failed!");
        dbg_print("[step 13]verify user zone 2                     [OK]\n");

        /* step 14 验证user zone 3访问 */
        memset(buf, 0, 256);
        strcpy(buf, CMInfo.Code);
        if (ret != 0)
        {
            ret = WT_CM_VERIFY_USR_ZONE3_FAILED;
        }
        ret = at88sc_user_zone_test(pdev, 3, buf);
        retBreak(ret, "verify user zone 3 failed!");
        dbg_print("[step 14]verify user zone 3                     [OK]\n");

        /* step 15 验证 write 7 password */
        buf[0] = 0xeb;
        buf[1] = 0xc2;
        buf[2] = 0x77;
        // Reset all registers
        cm_ResetCrypto();
        at88sc_verify_password(pdev, 7, buf, VerifyPpasswordWrite);
        at88sc_read_config_zone(pdev, 0xe8, 8, buf);
        ret = at88sc_pac_acc_warning(buf);
        if (ret != 0)
        {
            ret = WT_CM_VERIFY_PWD2_FAILED;
        }
        retBreak(ret, "verify write 7 password failed!");
        dbg_print("[step 15]verify write 7 password                [OK]\n");

        /* step 16 设置Card Manufacturer Code */
        buf[0] = 2;
        buf[1] = 0;
        buf[2] = 1;
        buf[3] = 2;
        at88sc_write_config_zone(pdev, 0x0C, 4, buf);
        at88sc_read_config_zone(pdev, 0x0C, 4, buf);
        if ((buf[0] != 2) || (buf[1] != 0) || (buf[2] != 1) || (buf[3] != 2))
        {
            ret = WT_CM_SETCARD_ID_FAILED;
            retBreak(ret, "setting card manufacturer code failed!");
        }
        dbg_print("[step 16]setting card manufacturer code         [OK]\n");

    } while (0);
    dbg_print("=======WT_CryptoMemInit Finish\n");
    return ret;
}

int WT_GetCryptoMemInfo(int DataLength, void *arg, struct dev_unit *pdev)
{
    s32 ret = 0;
    s32 i;
    u8 buf[CM_ZONE_SIZE] = {0};
    struct CryptoMemInfoType CMInfo;

    memset(&CMInfo, 0, sizeof(struct CryptoMemInfoType));
    dbg("Board%d Mod%d WT_GetCryptoMemInfo\n", pdev->type, pdev->number);
    do
    {
        at88sc_wait_lock(pdev, 30);
        ret = at88sc_is_initial(pdev);
        if (ret != 0)
        {
            break;
        }

        //验证user zone 2访问
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 2 * 32;
        }

        ret = at88sc_user_zone_test(pdev, 2, buf);
        if (ret != 0)
        {
            ret = 1;
            break;
        }

        //验证user zone 1，并返回InnerSN
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 1 * 32;
        }

        ret = at88sc_user_zone_test(pdev, 1, buf);
        if (ret != 0)
        {
            memcpy(CMInfo.InnerSN, buf, 32);
            ret = 0;
        }

        //验证user zone 3，并返回特征码code
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 3 * 32;
        }

        ret = at88sc_user_zone_test(pdev, 3, buf);
        if (ret != 0)
        {
            memcpy(CMInfo.Code, buf, 32);
            ret = 0;
        }

        //验证user zone 0，返回设备类型
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 0 * 32;
        }

        ret = at88sc_user_zone_test(pdev, 0, buf);
        if (ret != 0)
        {
            memcpy(CMInfo.DevType, buf, 32);
            ret = 0;
        }
    } while (0);

    retAssert(ret, "WT_GetCryptoMemInfo failed!");

    //返回读取的数据
    if (copy_to_user(arg, &CMInfo, DataLength) == -1)
    {
        dbg("WT_GetEncryptChipInfo info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return ret;
}

int WT_GetCryptoMemSN(int DataLength, void *arg, struct dev_unit *pdev)
{
    s32 ret = 0;
    s32 i;
    u8 buf[CM_ZONE_SIZE] = {0};
    u8 InnerSN[CM_ZONE_SIZE] = {0};
    u8 DevType[CM_ZONE_SIZE] = {0};
    u8 SN[CM_ZONE_SIZE] = {0};
    dbg("Board%d Mod%d WT_GetCryptoMemSN\n", pdev->type, pdev->number);
    do
    {
        at88sc_wait_lock(pdev, 30);
        ret = at88sc_is_initial(pdev);
        if (ret != 0)
        {
            break;
        }

        //验证user zone 2访问
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 2 * 32;
        }
        ret = at88sc_user_zone_test(pdev, 2, buf);
        if (ret != 0)
        {
            ret = 1;
            break;
        }

        //验证user zone 1，并返回InnerSN
        for (i = 0; i < 32; i++)
        {
            InnerSN[i] = i + 1 * 32;
        }
        ret = !at88sc_user_zone_test(pdev, 1, InnerSN);
        retBreak(ret, "Crypto InnerSN Error!");

        //验证user zone 0，返回设备类型
        for (i = 0; i < 32; i++)
        {
            DevType[i] = i + 0 * 32;
        }
        ret = !at88sc_user_zone_test(pdev, 0, DevType);
        retBreak(ret, "Crypto DevType Error!");

        snprintf(SN, CM_ZONE_SIZE, "%s-%s", DevType, InnerSN);
    } while (0);

    retAssert(ret, "WT_GetCryptoMemSN failed!");

    //返回读取的数据
    if (copy_to_user(arg, SN, DataLength) == -1)
    {
        dbg("WT_GetCryptoMemSN info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return ret;
}

int WT_GetCryptoMemCode(int DataLength, void *arg, struct dev_unit *pdev)
{
    s32 ret = 0;
    s32 i;
    u8 buf[CM_ZONE_SIZE] = {0};
    u8 Code[CM_ZONE_SIZE] = {0};
    dbg("Board%d Mod%d WT_GetCryptoMemCode\n", pdev->type, pdev->number);
    do
    {
        at88sc_wait_lock(pdev, 30);
        ret = at88sc_is_initial(pdev);
        if (ret != 0)
        {
            break;
        }

        //验证user zone 2访问
        for (i = 0; i < 32; i++)
        {
            buf[i] = i + 2 * 32;
        }

        ret = at88sc_user_zone_test(pdev, 2, buf);
        if (ret != 0)
        {
            ret = 1;
            break;
        }

        //验证user zone 3，并返回特征码code
        for (i = 0; i < 32; i++)
        {
            Code[i] = i + 3 * 32;
        }

        ret = !at88sc_user_zone_test(pdev, 3, Code);
        retBreak(ret, "Crypto Code Error!");
    } while (0);

    retAssert(ret, "WT_GetCryptoMemCode failed!");

    //返回读取的数据
    if (copy_to_user(arg, Code, DataLength) == -1)
    {
        dbg("WT_GetCryptoMemCode info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return ret;
}

// LED灯IO扩展器件初始化
int LedIOEx_init(struct dev_unit *pdev)
{
    return WT_OK;
}