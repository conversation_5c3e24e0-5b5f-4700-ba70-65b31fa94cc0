//*****************************************************************************
//File: devcmd.h
//Describe:硬件操作控制命令号定义
//yuanyongchun
//Date: 2021.01.20
//*****************************************************************************

#ifndef _DEV_CMD_H_
#define _DEV_CMD_H_

#define SET_CMD_BIND(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4))
#define GET_CMD_BIND(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4))

#define SET_CMD_BIND_1(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, std::placeholders::_1))

#define SET_CMD_BIND_2(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, std::placeholders::_2))

#define SET_CMD_BIND_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, std::placeholders::_4))
#define GET_CMD_BIND_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, std::placeholders::_4))

#define SET_CMD_BIND_3_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_3, std::placeholders::_4))
#define GET_CMD_BIND_3_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_3, std::placeholders::_4))

#define SET_CMD_BIND_2_3(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_2, std::placeholders::_3))
#define GET_CMD_BIND_2_3(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_2, std::placeholders::_3))

#define SET_CMD_BIND_2_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
	std::placeholders::_2, std::placeholders::_4))
#define GET_CMD_BIND_2_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_2, std::placeholders::_4))

#define SET_CMD_BIND_1_2(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2))
#define GET_CMD_BIND_1_2(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2))

#define SET_CMD_BIND_1_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_4))
#define GET_CMD_BIND_1_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_4))

#define SET_CMD_BIND_1_2_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2, std::placeholders::_4))
#define GET_CMD_BIND_1_2_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2, std::placeholders::_4))

#define SET_CMD_BIND_1_3_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_3, std::placeholders::_4))
#define GET_CMD_BIND_1_3_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_3, std::placeholders::_4))

#define SET_CMD_BIND_2_3_4(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_2, std::placeholders::_3, std::placeholders::_4))
#define GET_CMD_BIND_2_3_4(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_2, std::placeholders::_3, std::placeholders::_4))

#define SET_CMD_BIND_DOUBLE(CmdId, Functor) CmdSetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4))
#define GET_CMD_BIND_DOUBLE(CmdId, Functor) CmdGetFunctorBind(CmdId, bind(&Functor, this, \
    std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4))

#define GET_DEVID(Data)                    (((Data) >> 24) & 0xFF)
#define GET_DEVDATA(Data)                  ((Data)  & 0xFFFFFF)
#define SET_DEVID(DEVID,ADDR)              (((DEVID) << 24) | ((ADDR) & 0xFFFFFF))

enum LO_SHIFT_BIT
{
    LO_SHIFT_BIT0_BIT7,
    LO_SHIFT_BIT8_BIT39,
};

//WT_CONTINUE_TEST_RESULT定义
enum WT_CONTINUE_TEST_RESULT_E
{
    WT_CONTINUE_TEST_RESULT_STATE,
    WT_CONTINUE_TEST_RESULT_TOTAL,
    WT_CONTINUE_TEST_RESULT_SUCCESS,
    WT_CONTINUE_TEST_RESULT_FAILED,
};

enum WT_UNIT_BOARD_STRESS_TEST_E
{
   STRESS_TEST_UB_PCI_REG_CONN,
   STRESS_TEST_UB_PCI_REG_ALTER,
   STRESS_TEST_UB_FLOW_LED,
   STRESS_TEST_UB_REG_READ,
   STRESS_TEST_UB_MULTI_THREAD_REG_CHECK,
   STRESS_TEST_UB_MULTI_PROCESS_REG_CHECK,
   
   STRESS_TEST_BP_MULTI_THREAD_FLOW_LED = 100,
   STRESS_TEST_BP_MULTI_PROCESS_FLOW_LED,
};

enum WT_BACK_PLANE_STRESS_TEST_E
{
   STRESS_TEST_BP_AD5611,
   STRESS_TEST_BP_AD7682_CHANNEL,
   STRESS_TEST_BP_AD9228_REG,
   STRESS_TEST_BP_AD9228_CHANNEL,
   STRESS_TEST_BP_HM7043,
   STRESS_TEST_BP_CRYPTO_BRUN,
   STRESS_TEST_BP_CRYPTO_READ,
   STRESS_TEST_BP_FLASH,
   STRESS_TEST_BP_AD7091_REG,
   STRESS_TEST_BP_AD7091_CONV,
   STRESS_TEST_BP_FAN,
   STRESS_TEST_BP_ATT_SHIFT,
   STRESS_TEST_BP_LED,
   STRESS_TEST_BP_PA_42553,
   STRESS_TEST_BP_ADF4002,
   STRESS_TEST_SW_PORT_TEMP,
};

enum WT_BUSI_BAORD_STRESS_TEST_E
{
   STRESS_TEST_RF_AD5611,
   STRESS_TEST_RF_AD7682_VOLT_CHANNEL,
   STRESS_TEST_RF_AD7682_SINGLE_CHANNEL,
   STRESS_TEST_RF_ADF4106,
   STRESS_TEST_BB_HM7044,
   STRESS_TEST_RF_LTC5594,
   STRESS_TEST_LO_LMX2594_REG,
   STRESS_TEST_RF_FLASH,
   STRESS_TEST_BB_AD7091_REG,
   STRESS_TEST_BB_AD7091_CONV,
   STRESS_TEST_LO_DDS,
   STRESS_TEST_BB_ATT_SHIFT,
   STRESS_TEST_BB_ATT_CODE,
   STRESS_TEST_BB_LO_SHIFT,
   STRESS_TEST_BB_DAC,
   STRESS_TEST_BB_ADC,
   STRESS_TEST_BB_XDMA_READ,
   STRESS_TEST_BB_XDMA_WRITE,
   STRESS_TEST_BB_XDMA_WRITE_READ_VERIFY,
   STRESS_TEST_LO_MOD_FREQ,
   STRESS_TEST_LO_MIX_FREQ,
   STRESS_TEST_LO_BOARD_FREQ,
   STRESS_TEST_LO_LMX2820_REG,
   STRESS_TEST_RF_AD7689_VOLT_CHANNEL,
   STRESS_TEST_RF_AD7689_SINGLE_CHANNEL,
   STRESS_TEST_LO_HMC833_REG,
};

//调试校准命令ID枚举类定义
enum CMD_WR_FUNC_E
{
    DEV_CMD_INVALID = -1,               //无效命令编号

    //单元板公共
    DEV_CMD_PCI_REG = 0,                //PCIE寄存器
    DEV_CMD_UB_FLASH,                   //单元板FLASH
    DEV_CMD_UB_FLASH_SECTION,           //单元板FLASH
    DEV_CMD_UB_AD7091,                  //单元板电压侦测模块
    DEV_CMD_UB_VOLTAGE,                 //单元板电压获取
    DEV_CMD_UB_TEMPERATURE,             //单元板温度检测模块
    DEV_CMD_UB_HARDWARE_VERSION,        //获取单元板硬件版本
    DEV_CMD_UB_REVISION_ID,             //REVISION_ID
    DEV_CMD_UB_INFO,                    //单元板当前信息
    DEV_CMD_UB_COUNT,                   //单元板数量
    DEV_CMD_TESTER_HW_TYPE,             //仪器硬件类型数量

    //背板
    DEV_CMD_BP_AD5611 = 100,            //晶振
    DEV_CMD_SWB_INNER_POWER_CODE,       //获取内部功率CODE
    DEV_CMD_SWB_PORT_POWER_CODE,        //获取端口处功率CODE
    DEV_CMD_SWB_AD9228,                 //端口处功率9228reg
    DEV_CMD_BP_HM7043,                  //时钟HM7043
    DEV_CMD_BP_RFVERSION,               //射频板版本号
    DEV_CMD_BP_BOARD_INFO,              //硬件版本信息
    DEV_CMD_BP_AT88_INIT,               //加密芯片初始化
    DEV_CMD_BP_AT88_WR,                 //加密芯片读写
    DEV_CMD_BP_FLASH,                   //FLASH
    DEV_CMD_BP_AD7091,                  //电压侦测模块
    DEV_CMD_BP_VOLTAGE,                 //电压获取
    DEV_CMD_BP_FAN,                     //风扇
    DEV_CMD_BP_FAN_SPEED,               //风扇转速
    DEV_CMD_SWB_SWITCHER,               //开关板开关
    DEV_CMD_SWB_ATT_CODE,               //背板ATT CODE
    DEV_CMD_BP_LED,                     //LED IO扩展
    DEV_CMD_BP_SW_PA,                   //PA
    DEV_CMD_BP_SW_42553,                //42553
    DEV_CMD_BP_SW_ADF4002,              //PCIE Switch2 
    DEV_CMD_SWB_GET_MOD_BY_PORT,        //根据端口获取对应的单元
    DEV_CMD_SWB_SET_PORT_STATE,         //设置开关板端口状态
    DEV_CMD_SWB_SHIFT_SPI_REG,          //开关板6组移位寄存器(12.1寄存器配置汇总)
    DEV_CMD_BP_TCA9539_REG,             //灯板寄存器
    DEV_CMD_SWB_PORT_TEMP,              //开关板端口温度
    DEV_CMD_BP_AD7091_CHANNEL_CODE,     //AD7091 通道CODE
    DEV_CMD_BP_TRIG_DEBUG,              //trig口调试

    //业务板及本振板
    DEV_CMD_BB_AD5611 = 200,            //基带板锁相环
    DEV_CMD_BB_AD7682,                  //no use
    DEV_CMD_BB_GET_AD7682_CODE,         //获取AD7682 CODE
    DEV_CMD_BB_GET_AD7682_VALUE,        //no use
    DEV_CMD_BB_ADF4106,                 //ADF4106
    DEV_CMD_BB_HM7044,                  //时钟HM7044
    DEV_CMD_BB_LTC5594,                 //LTC5594
    DEV_CMD_BB_BOARD_INFO,              //
    DEV_CMD_BB_LMX2594,                 //LMX2594
    DEV_CMD_BB_LMX2594_FREQ,            //LMX2594频率
    DEV_CMD_BB_FLASH,                   //FLASH
    DEV_CMD_BB_AD7091,                  //AD7091
    DEV_CMD_BB_VOLTAGE,                 //电压获取
    DEV_CMD_BB_DDS,                     //DDS
    DEV_CMD_RF_ATT_SHIFT,               //ATT SHIFT
    DEV_CMD_BB_LO_SHIFT,                //LO SHIFT
    DEV_CMD_BB_LO_SHIFT_BIT,            //LO SHIFT
    DEV_CMD_RF_ADC_AD9684,              //ADC模数转换器
    DEV_CMD_RF_DAC_AD9142,              //DAC数模转换器
    DEV_CMD_RF_ATT,                     //射频板ATT衰减器
    DEV_CMD_RF_SET_PORT_STATE,          //射频板端口输出链路状态
    DEV_CMD_RF_SET_BAND,                //设置射频板BAND
    DEV_CMD_LO_DDS_FREQ,                //配置DDS输出频率
    DEV_CMD_LO_DDS_CURR,                //配置DDS输出电流
    DEV_CMD_LO_HMC705,                  //配置分配器HMC705
    DEV_CMD_LO_LOOP_FILTER,             //配置环路滤波器
    DEV_CMD_LO_FREQ_CHANNEL,            //配置本振板频率通道
    DEV_CMD_LO_MOD_FREQ,                //本振板调频频率(KHz)
    DEV_CMD_BB_FREQENCY,                //配置链路频率
    DEV_CMD_BB_START,                   //开启VSA/VSG
    DEV_CMD_BB_STOP,                    //停止VSA/VSG
    DEV_CMD_BB_LMX2820,                 //LMX2820
    DEV_CMD_BB_LMX2820_FREQ,            //LMX2820频率
    DEV_CMD_LO_MIX_FREQ,                //本振板混频频率
    DEV_CMD_MOD_REF_SEL,                //调频参考时钟选择
    DEV_CMD_LO_FREQ_CHANNEL_VB,         //配置本振板频率通道
    DEV_CMD_BB_AD7682_TEMPERATURE,      //频率相关器件温度
    DEV_CMD_BB_DOWN,                    //VSA/VSG DOWN
    DEV_CMD_LO_MOD_FREQ_HZ,             //本振板调频频率(Hz)     
    DEV_CMD_LO_COM_MODE,                //本振模式
    DEV_CMD_ANALOG_IQ_SW,               //模拟IQ信号内/外链路切换开关

    DEV_CMD_SW_GET_AD7689_CODE,         //AD7689
    DEV_CMD_BB_HMC833,                  //HMC833
    DEV_CMD_SW_GET_AD7689_VOLT,
    DEV_CMD_CHECK_LO_LOCK,              //Check Lo
    DEV_CMD_BB_ADF4368,
    //VSA = 300
    DEV_CMD_VSA_PA_PROTECT_DISABLE= 300,//写1时禁用VSA PA保护
    DEV_CMD_VSA_DC_OFFSET,              //RX直流偏移
    DEV_CMD_VSA_LNA,                    //VSA射频链路LAN分支状态控制

    //VSG = 400
    DEV_CMD_VSG_IFG_STATUS = 400,       //IFG STATUS
    DEV_CMD_VSG_IFG_ENABLE,             //IFG ENABLE
    DEV_CMD_VSG_FREQ_OUTPUT_POWER,      //设置VSG的输出频率与功率.
    DEV_CMD_VSG_BOOST,                  //VSG Boost开关
    DEV_CMD_VSG_DAC_GAIN,               //TX DAC增益设置
    DEV_CMD_VSG_DAC_GAIN_IQ_CODE,       //TX DAC IQTuneGain增益(IQ code)
    DEV_CMD_VSG_DC_I_OFFSET,            //TX I直流偏移
    DEV_CMD_VSG_DC_Q_OFFSET,            //TX Q直流偏移
    DEV_CMD_VSG_BROADCAST_PORT_POWER,//设置广播时其他端口功率

    //其他业务命令编号枚举类定义
    DEV_CMD_ENCRYPT_SN = 500,           //获取加密芯片SN码
    DEV_CMD_ENCRYPT_CODE,               //获取加密芯片特征码CODE
    DEV_CMD_XXXXXXXXXXXX,
    DEV_CMD_XDMA_WRITE,                 //XDMA读写测试
    DEV_CMD_XDMA_READ,                  //XDMA读写测试
    DEV_CMD_FPGA_UPGRADE,               //FPGA升级
    DEV_CMD_READ_BACK_FPGA_DATA,        //读回FPGA数据
    DEV_CMD_CAL_OCCUPY,                 //单元使用完后不释放(校准使用)
    DEV_CMD_DEBUG_FLAG,                 //HOLD仪器
    DEV_CMD_XDMA_TEST,                  //XDMA读写测试

    //TEST = 600
    DEV_CMD_SAVE_DMA_DATA = 600,        //保存当前链路的DMA数据
    DEV_CMD_FPGA_UPGRADE_SELF_TEST,     //FPGA升级自测
    DEV_CMD_CHECK_BOARD_INFO,           //检测单元板信息是否正确
    DEV_CMD_UB_STRESS_TEST,             //单元板通用压力测试
    DEV_CMD_BP_STRESS_TEST,             //背板板压力测试
    DEV_CMD_BB_STRESS_TEST,             //业务板压力测试
    DEV_CMD_DEV_RESPONSE_DEBUG,         //器件响应时间DEBUG配置

    //临时测试
    DEV_CMD_TEST = 1000,                //临时测试用
};
#endif