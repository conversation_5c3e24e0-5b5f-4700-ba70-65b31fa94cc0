#pragma once
#ifndef __TESTER_CAL_H__
#define __TESTER_CAL_H__


////////////////////////////////////////////////////////////////////
///                      Cal（生产校准工具特有部分）
////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////
/// WT_ManuConnect
/// 生产连接，不使用仪器内部算法
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] ipAddr:IP地址
/// @param[out] connID:连接ID
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ManuConnect(const char *ipAddr, int *connID);

////////////////////////////////////////////////////////////////////
/// WT_RebootTester
/// 重启WT_Server
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
////////////////////////////////////////////////////////////////////
///
WTTESTER_DLL_API int CALL_MODE WT_RebootTester(int connID);

////////////////////////////////////////////////////////////////////
/// WT_SendCalFile
/// 发送校准文件
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] fileName:校准文件路径
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SendCalFile(int connID, char *fileBuffer, unsigned fileBuffSize, const char *fileName);

////////////////////////////////////////////////////////////////////
/// WT_GetCalFile
/// 获取校准文件
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[out] fileData:校准文件数据
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetCalFile(int connID, const char *fileName, char *fileBuffer, unsigned fileBuffSize);

////////////////////////////////////////////////////////////////////
/// WT_SetCalData
/// 设置校准数据
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] data:需要下发的校准数据
/// @param[in] dataSize:校准数据大小
////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_SetCalData(int connID, char *data, unsigned dataSize);
////////////////////////////////////////////////////////////////////
/// WT_GetCalData
/// 获取校准数据
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[out] data:保存校准数据，数据类型struct Calibration_Option
/// @param[out] dataSize:校准数据缓存大小

////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetCalData(int connID, char *data, unsigned dataSize);
////////////////////////////////////////////////////////////////////
/// WT_SetBBGain
/// 设置基带增益
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] gain:基带增益
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetBBGain(int connID, int gain);

////////////////////////////////////////////////////////////////////
/// WT_GetTemperature
/// 获取关键器件的温度
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[out] temp:温度

////////////////////////////////////////////////////////////////////
///
WTTESTER_DLL_API int CALL_MODE WT_GetTemperature(int connID, DevTemperature *temp);
WTTESTER_DLL_API int WT_GetTemperatureHistory(int connID, int *Cnt, DevTempSave *info, unsigned int infoSize);
////////////////////////////////////////////////////////////////////
/// WT_SetTempCal
/// 设置温度校准补偿开关
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[int] value:开关值
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetTempCal(int connID, int value);

////////////////////////////////////////////////////////////////////
/// WT_SetFlatnessCal
/// 设置平坦度校准补偿开关
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] value:开关值
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetFlatnessCal(int connID, int value);

////////////////////////////////////////////////////////////////////
/// WT_GetVoltage
/// 获取关键器件电压
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] voltageCnt: 电压内存DeviceVoltage数量
/// @param[out] voltage：电压存储内存
/// @param[out] count: voltage的实际数量
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetVoltage(int connID, DeviceVoltage *voltage, int voltageCnt, int *count);

////////////////////////////////////////////////////////////////////
/// WT_GetFanSpeed
/// 获取风扇转速
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[out] speed:转速
////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetFanSpeed(int connID, int *speed);

////////////////////////////////////////////////////////////////////
/// WT_Write
/// 写SN
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID：连接ID
/// @param[in] password:写SN所需密码
/// @param[in] SN:需写入的SN
////////////////////////////////////////////////////////////////////
///
WTTESTER_DLL_API int CALL_MODE WT_WriteSN(int connID, const char *password, const char *SN);

////////////////////////////////////////////////////////////////////
/// WT_SetATT
/// 设置ATT
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] att:ATT值
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetATT(int connID, double *att);

////////////////////////////////////////////////////////////////////
/// WT_GetATT
/// 获取ATT
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] att:ATT值
////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_GetATT(int connID, double *att);

////////////////////////////////////////////////////////////////////
/// WT_GetResultRealOrImag
/// 获取IQ数据的实部或者虚部
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] anaParmString:要获取的数据名称
/// @param[in] ElementCnt:获取的数据数量
/// @param[in] IQ数据实部（虚部）标识
/// @param[out] pdBuff:用于接收获取的数据
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetResultRealOrImag(int connID, const char *anaParamString, double *pdBuff, int ElementCnt, int RealImagFlag);

////////////////////////////////////////////////////////////////////
/// WT_TesterFwCmd
/// 执行固件CMD命令
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] sendBuff:要发送的命令
/// @param[out] recvBuff:接收缓存区
/// @param[out] pRecvLen:接收缓存区大小
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_TesterFwCmd(int connID, const char *sendBuff, const char *recvBuff, int *pRecvLen);

////////////////////////////////////////////////////////////////////
/// WT_SetComponentValue
/// 设置器件参数值
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] componentCode:器件编号
/// @param[in] componentValue:器件参数值
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_SetComponentValue(int connID, ComponentLocation componentLocation, int componentValue);

////////////////////////////////////////////////////////////////////
/// WT_GetComponentValue
/// 获取器件参数值
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] componentLocation:器件编号
/// @param[in] componentValue:器件参数值内存
/// @param[in] bufSize:器件参数值内存大小，单位byte
/// @param[out] dataLen:返回数据大小，单位byte
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_GetComponentValue(int connID, ComponentLocation componentLocation, char *componentValue, int bufSize, int *dataLen);

////////////////////////////////////////////////////////////////////
/// WT_InitCalData
/// 校准数据初始化
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_InitCalData(int connID);

////////////////////////////////////////////////////////////////////
/// WT_SaveOriginalIQDataToFile
/// 保存原始数据到指定文件
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
/// @param[in] fileName:需要保存到的文件名
/// @param[in] calParam:如果需要获取校准参数，传入该值，默认为NULL
/// @param[in] calParamSize:calParam大小
/// @param[in] signalID:流ID，默认为-1
////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int CALL_MODE WT_SaveOriginalIQDataToFile(int connID, const char *fileName, char *calParam = NULL, unsigned calParamSize = 0, int signalID = -1);
////////////////////////////////////////////////////////////////////
/// WT_ReadRemoteFile
/// 读取仪器内部文件内容
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///	@param[in] filename:仪器内部文件绝对路径
///	@param[in] filesize:实际文件字节大小
///	@param[in] buffer:保存文件内容的内存
///	@param[in] buffer_size:buffer大小
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ReadRemoteFile(int connID, const char *filename, unsigned int *filesize, char *buffer, unsigned int buffer_size);

////////////////////////////////////////////////////////////////////
/// WT_WriteRemoteFile
/// 写入仪器内部文件内容
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///	@param[in] filename:仪器内部文件绝对路径
///	@param[in] buffer:保存文件内容的内存
///	@param[in] buffer_size:buffer大小
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_WriteRemoteFile(int connID, const char *filename, char *buffer, unsigned int buffer_size);

////////////////////////////////////////////////////////////////////
/// WT_ExecShellCmd
/// 仪器执行shell命令
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///	@param[in] cmd:shell命令
///	@param[in] buffer:shell返回内容的内存
///	@param[in] buffer_size:buffer大小
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_ExecShellCmd(int connID, const char *cmd, char *buffer, unsigned int buffer_size);

WTTESTER_DLL_API void WT_DLLInitialize_V2(unsigned int maxMemSize, unsigned int maxSamplePoint);
////////////////////////////////////////////////////////////////////
/// WT_SelfCalibration
/// 仪器执行自校准命令
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in]  connID:连接ID
///	@param[in] isStart:0停止自校准命令， 1开始自校准命令
////////////////////////////////////////////////////////////////////

WTTESTER_DLL_API int WT_SelfCalibration(int connID, int isStart = 0);
////////////////////////////////////////////////////////////////////
/// WT_QuerySelfCalibrationPercent
/// 仪器执行查询自校准进度百分百命令
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///	@param[in] percent:返回自校准百分百
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_QuerySelfCalibrationPercent(int connID, int *percent);
////////////////////////////////////////////////////////////////////
/// WT_SelfCalibrationAutoRunning
/// 仪器执行自校准自动运行配置
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///	@param[in] isAutoRun:1开启自动运行内校准，0禁用自动运行内校准
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_SelfCalibrationAutoRunning(int connID, int isAutoRun);
////////////////////////////////////////////////////////////////////
/// WT_StartFastAttCal
/// 仪器执行快速ATT校准，FPGA实现
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
///	@param[in] config:ATT校准配置参数
///	@param[out] result:ATT校准返回结果
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int WT_StartFastAttCal(int connID, ATTCalCfg_API *config, ATTCalResult_API *result);
////////////////////////////////////////////////////////////////////
/// WT_StopFastAttCal
/// 仪器停止快速ATT校准，FPGA实现
/// @return   成功返回 @ref WT_ERR_CODE_OK ,否则返回错误；错误码见 @ref WT_ERR_CODE_ENUM 或者 @ref WT_ERROR_E
/// @param[in] connID:连接ID
////////////////////////////////////////////////////////////////////
WTTESTER_DLL_API int CALL_MODE WT_StopFastAttCal(int connID);

#endif

