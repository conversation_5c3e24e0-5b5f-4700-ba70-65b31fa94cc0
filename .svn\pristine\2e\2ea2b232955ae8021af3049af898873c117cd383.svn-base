#ifndef __WT_SPECTRUM_MARGING_CLACULATE_H__
#define __WT_SPECTRUM_MARGING_CLACULATE_H__

#include "alg/includeAll.h"

static const Complex mask_11a[] =
{
	{ -999 * MHz, -40},
	{ -30 * MHz, -40}, { -20 * MHz, -28}, { -11 * MHz, -20}, { -9 * MHz, 0},
	{9 * MHz, 0}, {11 * MHz, -20}, {20 * MHz, -28}, {30 * MHz, -40},
	{999 * MHz, -40}
};
static const Complex mask_11b[] =
{
	{ -999 * MHz, -50},
	{ -22 * MHz - 1 * KHz, -50}, { -22 * MHz, -30}, { -11 * MHz - 1 * KHz, -30}, { -11 * MHz, 0},
	{11 * MHz, 0}, {11 * MHz + 1 * KHz, -30}, {22 * MHz, -30}, {22 * MHz + 1 * KHz, -50},
	{999 * MHz, -50}
};
static Complex mask_11n_20[] =
{
	{ -999 * MHz, -45},
	{ -30 * MHz, -45}, { -20 * MHz, -28}, { -11 * MHz, -20}, { -9 * MHz, 0},
	{9 * MHz, 0}, {11 * MHz, -20}, {20 * MHz, -28}, {30 * MHz, -45},
	{999 * MHz, -45},
};
// static Complex mask_11n_20_5G[] =
// {
// 	{ -999 * MHz, -40},
// 	{ -30 * MHz, -40}, { -20 * MHz, -28}, { -11 * MHz, -20}, { -9 * MHz, 0},
// 	{9 * MHz, 0}, {11 * MHz, -20}, {20 * MHz, -28}, {30 * MHz, -40},
// 	{999 * MHz, -40},
// };
// static Complex mask_11n_20_cmimo[] =
// {
// 	{ -999 * MHz, -64},
// 	{ -30 * MHz, -64}, { -20 * MHz, -28}, { -11 * MHz, -20}, { -9 * MHz, 0},
// 	{9 * MHz, 0}, {11 * MHz, -20}, {20 * MHz, -28}, {30 * MHz, -64},
// 	{999 * MHz, -64},
// };
static const Complex mask_11n_40[] =
{
	{ -999 * MHz, -45},
	{ -60 * MHz, -45}, { -40 * MHz, -28}, { -21 * MHz, -20}, { -19 * MHz, 0},
	{19 * MHz, 0}, {21 * MHz, -20}, {40 * MHz, -28}, {60 * MHz, -45},
	{999 * MHz, -45}
};
static const Complex mask_11n_40_5G[] =
{
	{ -999 * MHz, -40},
	{ -60 * MHz, -40}, { -40 * MHz, -28}, { -21 * MHz, -20}, { -19 * MHz, 0},
	{19 * MHz, 0}, {21 * MHz, -20}, {40 * MHz, -28}, {60 * MHz, -40},
	{999 * MHz, -40}
};
static const Complex mask_11n_40_cmimo[] =
{
	{ -999 * MHz, -64},
	{ -60 * MHz, -64}, { -40 * MHz, -28}, { -21 * MHz, -20}, { -19 * MHz, 0},
	{19 * MHz, 0}, {21 * MHz, -20}, {40 * MHz, -28}, {60 * MHz, -64},
	{999 * MHz, -64}
};
static const Complex mask_11ac_20[] =
{
	{ -999 * MHz, -40},
	{ -30 * MHz, -40}, { -20 * MHz, -28}, { -11 * MHz, -20}, { -9 * MHz,   0},
	{9 * MHz,     0}, {11 * MHz,  -20}, {20 * MHz,  -28}, {30 * MHz, -40},
	{999 * MHz, -40},
};
static const Complex mask_11ac_20_cmimo[] =
{
	{ -999 * MHz, -64},
	{ -30 * MHz, -64}, { -20 * MHz, -28}, { -11 * MHz, -20}, { -9 * MHz, 0},
	{9 * MHz, 0}, {11 * MHz, -20}, {20 * MHz, -28}, {30 * MHz, -64},
	{999 * MHz, -64},
};
static const Complex mask_11ac_40[] =
{
	{ -999 * MHz, -40},
	{ -60 * MHz, -40}, { -40 * MHz, -28}, { -21 * MHz, -20}, { -19 * MHz,  0},
	{19 * MHz,    0}, {21 * MHz,  -20}, {40 * MHz,  -28}, {60 * MHz, -40},
	{999 * MHz, -40}
};
static const Complex mask_11ac_40_cmimo[] =
{
	{ -999 * MHz, -64},
	{ -60 * MHz, -64}, { -40 * MHz, -28}, { -21 * MHz, -20}, { -19 * MHz, 0},
	{19 * MHz, 0}, {21 * MHz, -20}, {40 * MHz, -28}, {60 * MHz, -64},
	{999 * MHz, -64}
};
static const Complex mask_11ac_80[] =
{
	{ -999 * MHz, -40},
	{ -120 * MHz, -40}, { -80 * MHz, -28}, { -41 * MHz, -20}, { -39 * MHz,   0},
	{39 * MHz,     0}, {41 * MHz,  -20}, {80 * MHz,  -28}, {120 * MHz, -40},
	{999 * MHz,  -40}
};
static const Complex mask_11ac_80_cmimo[] =
{
	{ -999 * MHz, -64},
	{ -120 * MHz, -64}, { -80 * MHz, -28}, { -41 * MHz, -20}, { -39 * MHz,   0},
	{39 * MHz,     0}, {41 * MHz,  -20}, {80 * MHz,  -28}, {120 * MHz, -64},
	{999 * MHz,  -64}
};
static const Complex mask_11ac_160[] =
{
	{ -999 * MHz, -40},
	{ -240 * MHz, -40}, { -160 * MHz, -28}, { -81 * MHz, -20}, { -79 * MHz,   0},
	{79 * MHz,     0}, {81 * MHz,   -20}, {160 * MHz, -28}, {240 * MHz, -40},
	{999 * MHz,  -40}
};


static const Complex mask_11ax_20[] =
{
	{ -999 * MHz, -40},
	{ -30 * MHz, -40}, { -20 * MHz, -28}, { -10.5 * MHz, -20}, { -9.75 * MHz,   0},
	{9.75 * MHz, 0}, {10.5 * MHz,  -20}, {20 * MHz,  -28}, {30 * MHz, -40},
	{999 * MHz, -40},
};
static const Complex mask_11ax_40[] =
{
	{ -999 * MHz, -40},
	{ -60 * MHz, -40}, { -40 * MHz, -28}, { -20.5 * MHz, -20}, { -19.5 * MHz,  0},
	{19.5 * MHz,    0}, {20.5 * MHz,  -20}, {40 * MHz,  -28}, {60 * MHz, -40},
	{999 * MHz, -40}
};
static const Complex mask_11ax_80[] =
{
	{ -999 * MHz, -40},
	{ -120 * MHz, -40}, { -80 * MHz, -28}, { -40.5 * MHz, -20}, { -39.5 * MHz,   0},
	{39.5 * MHz,     0}, {40.5 * MHz,  -20}, {80 * MHz,  -28}, {120 * MHz, -40},
	{999 * MHz,  -40}
};
static const Complex mask_11ax_160[] =
{
	{ -999 * MHz, -40},
	{ -240 * MHz, -40}, { -160 * MHz, -28}, { -80.5 * MHz, -20}, { -79.5 * MHz,   0},
	{79.5 * MHz,    0}, {80.5 * MHz,  -20}, {160 * MHz, -28}, {240 * MHz, -40},
	{999 * MHz,  -40}
};

static const Complex mask_11be[5][10] =
{
    {{ -999 * MHz, -40},{ -30 * MHz, -40}, { -20 * MHz, -28}, { -10.5 * MHz, -20}, { -9.75 * MHz,   0},
    {9.75 * MHz,    0}, {10.5 * MHz,  -20}, {20 * MHz, -28}, {30 * MHz, -40},{999 * MHz,  -40}},
    {{ -999 * MHz, -40},{ -60 * MHz, -40}, { -40 * MHz, -28}, { -20.5 * MHz, -20}, { -19.5 * MHz,   0},
    {19.5 * MHz,    0}, {20.5 * MHz,  -20}, {40 * MHz, -28}, {60 * MHz, -40},{999 * MHz,  -40}},
    {{ -999 * MHz, -40},{ -120 * MHz, -40}, { -80 * MHz, -28}, { -40.5 * MHz, -20}, { -39.5 * MHz,   0},
    {39.5 * MHz,    0}, {40.5 * MHz,  -20}, {80 * MHz, -28}, {120 * MHz, -40},{999 * MHz,  -40}},
    {{ -999 * MHz, -40},{ -240 * MHz, -40}, { -160 * MHz, -28}, { -80.5 * MHz, -20}, { -79.5 * MHz,   0},
    {79.5 * MHz,    0}, {80.5 * MHz,  -20}, {160 * MHz, -28}, {240 * MHz, -40},{999 * MHz,  -40}},
    {{ -999 * MHz, -40},{ -480 * MHz, -40}, { -320 * MHz, -28}, {-160.5 * MHz, -20}, { -159.5 * MHz,   0},
    {159.5 * MHz,    0}, {160.5 * MHz,  -20}, {320 * MHz, -28}, {480 * MHz, -40},{999 * MHz,  -40}}
};

static const Complex mask_11ah_1[] =
{
	{ -999 * MHz, -40},
	{ -1.5 * MHz, -40}, { -1 * MHz,  -28}, { -0.6 * MHz, -20}, { -0.45 * MHz,   0},
	{0.45 * MHz,   0}, {0.6 * MHz, -20}, {1 * MHz,    -28}, {1.5 * MHz,   -40},
	{999 * MHz,  -40}
};
static const Complex mask_11ah_2[] =
{
	{ -999 * MHz, -40},
	{ -3 * MHz,   -40}, { -2 * MHz,  -28}, { -1.1 * MHz, -20}, { -0.9 * MHz,   0},
	{0.9 * MHz,    0}, {1.1 * MHz, -20}, {2 * MHz,    -28}, {3 * MHz,   -40},
	{999 * MHz,  -40}
};
static const Complex mask_11ah_4[] =
{
	{ -999 * MHz, -40},
	{ -6 * MHz,  -40}, { -4 * MHz,  -28}, { -2.1 * MHz, -20}, { -1.9 * MHz,   0},
	{1.9 * MHz,   0}, {2.1 * MHz, -20}, {4 * MHz,    -28}, {6 * MHz,    -40},
	{999 * MHz,  -40}
};
static const Complex mask_11ah_8[] =
{
	{ -999 * MHz, -40},
	{ -12 * MHz, -40}, { -8 * MHz,  -28}, { -4.1 * MHz, -20}, { -3.9 * MHz,   0},
	{3.9 * MHz,   0}, {4.1 * MHz, -20}, {8 * MHz,    -28}, {12 * MHz,   -40},
	{999 * MHz,  -40}
};
static const Complex mask_11ah_16[] =
{
	{ -999 * MHz, -40},
	{ -24 * MHz, -40}, { -16 * MHz, -28}, { -8.1 * MHz, -20}, { -7.9 * MHz,   0},
	{7.9 * MHz,   0}, {8.1 * MHz, -20}, {16 * MHz,   -28}, {24 * MHz,   -40},
	{999 * MHz,  -40}
};

static const Complex mask_11ba_20[] =
{
    { -999 * MHz, -40 },
    { -30 * MHz, -40 },{ -20 * MHz, -28 },{ -11 * MHz, -20 },{ -3.5 * MHz, -15 },{ -2.25 * MHz,  0 },
    { 2.25 * MHz,  0 },{ 3.5 * MHz, -15 },{ 11 * MHz,  -20 },{ 20 * MHz,  -28 },{ 30 * MHz,  -40 },
    { 999 * MHz, -40 },
};

static const Complex mask_11ba_40[] =
{
    { -999 * MHz, -60 },
    { -60 * MHz, -40 },{ -40 * MHz, -28 },{ -21 * MHz, -20 },{ -13.5 * MHz, -15 },{ -12.25 * MHz,  0 },
    { -7.75 * MHz,  0 },{ -6.5 * MHz, -15 },
    { 0 * MHz, -19.33 },
    { 6.5 * MHz, -15 },{ 7.75 * MHz,  0 },
    { 12.25 * MHz,  0 },{ 13.5 * MHz, -15 },{ 21 * MHz, -20 },{ 40 * MHz, -28 },{ 60 * MHz, -40 },
    { 999 * MHz, -40 },
};

static const Complex mask_11ba_80[] =
{
    { -999 * MHz, -40 },
    { -120 * MHz, -40 },{ -80 * MHz, -28 },{ -41 * MHz, -20 },{ -33.5 * MHz, -15 },{ -32.25 * MHz,  0 },
    { -27.75 * MHz,  0 },{ -26.5 * MHz, -15 },
    { -20 * MHz, -19.33 },
    { -13.5 * MHz, -15 },{ -12.25 * MHz,  0 },
    { -7.75 * MHz,  0 },{ -6.5 * MHz, -15 },
    { 0 * MHz, -19.33 },
    { 6.5 * MHz, -15 },{ 7.75 * MHz,  0 },
    { 12.25 * MHz,  0 },{ 13.5 * MHz, -15 },
    { 20 * MHz, -19.33 },
    { 26.5 * MHz, -15 },{ 27.75 * MHz,  0 },
    { 32.25 * MHz,  0 },{ 33.5 * MHz, -15 },{ 41 * MHz, -20 },{ 80 * MHz, -28 },{ 120 * MHz, -40 },
    { 999 * MHz, -40 },
};

typedef struct _StMaskMapping
{
	s32 demod_mode;
	Complex *mask_data;
	s32 mask_cnt;
} StMaskMapping;

static const StMaskMapping stMaskMapping[] =
{
    { WT_DEMOD_11AG, const_cast<Complex *>(mask_11a), sizeof(mask_11a) / sizeof(mask_11a[0]) },
	{ WT_DEMOD_11B, const_cast<Complex *>(mask_11b), sizeof(mask_11b) / sizeof(mask_11b[0]) },
    { WT_DEMOD_11N_20M, const_cast<Complex *>(mask_11n_20), sizeof(mask_11n_20) / sizeof(mask_11n_20[0]) },
    { WT_DEMOD_11N_40M, const_cast<Complex *>(mask_11n_40), sizeof(mask_11n_40) / sizeof(mask_11n_40[0]) },
    { WT_DEMOD_11AC_20M, const_cast<Complex *>(mask_11ac_20), sizeof(mask_11ac_20) / sizeof(mask_11ac_20[0]) },
    { WT_DEMOD_11AC_40M, const_cast<Complex *>(mask_11ac_40), sizeof(mask_11ac_40) / sizeof(mask_11ac_40[0]) },
    { WT_DEMOD_11AC_80M, const_cast<Complex *>(mask_11ac_80), sizeof(mask_11ac_80) / sizeof(mask_11ac_80[0]) },
    { WT_DEMOD_11AC_160M, const_cast<Complex *>(mask_11ac_160), sizeof(mask_11ac_160) / sizeof(mask_11ac_160[0]) },
    { WT_DEMOD_11AX_20M, const_cast<Complex *>(mask_11ax_20), sizeof(mask_11ax_20) / sizeof(mask_11ax_20[0]) },
    { WT_DEMOD_11AX_40M, const_cast<Complex *>(mask_11ax_40), sizeof(mask_11ax_40) / sizeof(mask_11ax_40[0]) },
    { WT_DEMOD_11AX_80M, const_cast<Complex *>(mask_11ax_80), sizeof(mask_11ax_80) / sizeof(mask_11ax_80[0]) },
    { WT_DEMOD_11AX_160M, const_cast<Complex *>(mask_11ax_160), sizeof(mask_11ax_160) / sizeof(mask_11ax_160[0]) },
    { WT_DEMOD_11BA_20M, const_cast<Complex *>(mask_11ba_20), sizeof(mask_11ba_20) / sizeof(mask_11ba_20[0]) },
    { WT_DEMOD_11BA_40M, const_cast<Complex *>(mask_11ba_40), sizeof(mask_11ba_40) / sizeof(mask_11ba_40[0]) },
    { WT_DEMOD_11BA_80M, const_cast<Complex *>(mask_11ba_80), sizeof(mask_11ba_80) / sizeof(mask_11ba_80[0]) },
    { WT_DEMOD_11BE_20M, const_cast<Complex *>(mask_11be[0]), sizeof(mask_11be[0]) / sizeof(mask_11be[0][0]) },
    { WT_DEMOD_11BE_40M, const_cast<Complex *>(mask_11be[1]), sizeof(mask_11be[1]) / sizeof(mask_11be[1][0]) },
    { WT_DEMOD_11BE_80M, const_cast<Complex *>(mask_11be[2]), sizeof(mask_11be[2]) / sizeof(mask_11be[2][0]) },
    { WT_DEMOD_11BE_160M, const_cast<Complex *>(mask_11be[3]), sizeof(mask_11be[3]) / sizeof(mask_11be[3][0]) },
    { WT_DEMOD_11BE_320M, const_cast<Complex *>(mask_11be[4]), sizeof(mask_11be[4]) / sizeof(mask_11be[4][0]) },
	{ WT_DEMOD_11AZ_20M, const_cast<Complex *>(mask_11ax_20), sizeof(mask_11ax_20) / sizeof(mask_11ax_20[0]) },
    { WT_DEMOD_11AZ_40M, const_cast<Complex *>(mask_11ax_40), sizeof(mask_11ax_40) / sizeof(mask_11ax_40[0]) },
    { WT_DEMOD_11AZ_80M, const_cast<Complex *>(mask_11ax_80), sizeof(mask_11ax_80) / sizeof(mask_11ax_80[0]) },
    { WT_DEMOD_11AZ_160M, const_cast<Complex *>(mask_11ax_160), sizeof(mask_11ax_160) / sizeof(mask_11ax_160[0]) },
	{ WT_DEMOD_11AH_1M, const_cast<Complex *>(mask_11ah_1), sizeof(mask_11ah_1) / sizeof(mask_11ah_1[0]) },
	{ WT_DEMOD_11AH_2M, const_cast<Complex *>(mask_11ah_2), sizeof(mask_11ah_2) / sizeof(mask_11ah_2[0]) },
    { WT_DEMOD_11AH_4M, const_cast<Complex *>(mask_11ah_4), sizeof(mask_11ah_4) / sizeof(mask_11ah_4[0]) },
    { WT_DEMOD_11AH_8M, const_cast<Complex *>(mask_11ah_8), sizeof(mask_11ah_8) / sizeof(mask_11ah_8[0]) },
    { WT_DEMOD_11AH_16M, const_cast<Complex *>(mask_11ah_16), sizeof(mask_11ah_16) / sizeof(mask_11ah_16[0]) },
};

#endif