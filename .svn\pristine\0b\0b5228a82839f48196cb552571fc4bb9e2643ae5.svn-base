//*********************************************************************************
//  File: scpi_socket.h
//  功能点：提供TCP连接服务；
//  Data: 2019.03.10
//*********************************************************************************

#ifndef __SCPI_SOCKET_H__
#define __SCPI_SOCKET_H__

#include <iostream>
#include <list>
#include <string.h>
#include <fstream>
#include <jsoncpp/json/json.h>

#include "wtev++.h"
#include "socket.h"
#include "scpi_service.h"
#include "threadpool.h"

class WTScpiSocket
{
public:
    WTScpiSocket(const wtev::loop_ref &loop, int MaxUserNum);
    ~WTScpiSocket();

    //*****************************************************************************
    // 函数: GetSockList()
    // 功能: 获取Sock列表
    // 返回值：Sock列表
    //*****************************************************************************
    static std::list<WTScpiSocket *>& GetSockList() { return m_Socket; }

    //*****************************************************************************
    // 函数: StartSocketSrv()
    // 功能: 创建服务socket，并绑定TCP服务IP，启动，然后listen，等待接收外部连接
    // 参数 [IN]：Port : WTLink服务监听端口
    // 返回值：成功或失败错误码，0表示正常
    //*****************************************************************************
    int StartSocketSrv(int Port);

    //*****************************************************************************
    // 函数: GetServiceList()
    // 功能: 返回所有连接服务
    // 返回值：服务对象列表
    //*****************************************************************************
    const std::list<std::shared_ptr<ScpiService>>& GetServiceList() { return m_SrvLink; }

private:
    //*****************************************************************************
    // 函数: AcceptCb()
    // 功能: accept回调，接收外部的连接
    // 参数 [IN]：reverse：
    // 参数 [IN]:watcher：acceptIO watcher
    // 返回值：无
    //*****************************************************************************
    void AcceptCb(wtev::io &watcher, int revents);

    //*****************************************************************************
    // 业务连接线程的处理函数
    // 参数[IN] : LinkSrv : service对象
    // 返回值: 无
    //*****************************************************************************
    void LinkService(std::shared_ptr<ScpiService> LinkSrv, void *Arg);

    //*****************************************************************************
    // 停止指定连接的服务并关闭连接
    // 参数[IN] : LinkSrv : service对象
    // 返回值: 无
    //*****************************************************************************
    void StopLinkSrv(std::shared_ptr<ScpiService> &LinkSrv);

    //*****************************************************************************
    // 停止连接的服务并关闭连接
    // 参数[IN] : 无
    // 返回值: 无
    //*****************************************************************************
    void WaitAllServiceStop(void);

    //*****************************************************************************
    // 初始化CMD配置文件
    // 参数[IN] : 无
    // 返回值: 成功或失败错误码
    //*****************************************************************************
    int CmdConfigInit(void);
private:
    static std::list<WTScpiSocket *> m_Socket;                      //监听对象列表

    int m_Sfd;                                                      //server 监听的 sock fd
    wtev::io m_AcceptIO;                                            //accept watcher
    wtev::loop_ref m_Loop;                                          //loop

    std::unique_ptr<ThreadPool> m_TaskPool;                         //线程池

    int m_Port;
    int m_UserNum;                                                  //能接收的总的用户数
    int m_UserCnt = 0;                                              //当前的用户数量
    std::mutex m_LinkMutex;                                         //管理连接对象列表使用的锁
    std::list<std::shared_ptr<ScpiService>> m_SrvLink;              //连接服务对象列表
    std::ifstream m_JsonIfstream;                                   //脚本文件流
    Json::Value m_JsonRoot;                                         //CMD配置文件
};

#endif //__SCPI_SOCKET_H__
