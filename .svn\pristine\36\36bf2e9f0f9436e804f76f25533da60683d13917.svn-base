//*****************************************************************************
//File: wtdefine.h
//Describe:所有器件的寄存器地址定义以及类型定义
//Date: 2016.10.11
//*****************************************************************************

#ifndef _WT_DEFINE_H_
#define _WT_DEFINE_H_

#include <linux/pci.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/module.h>
#include <linux/rwsem.h>
#include <linux/spinlock.h>
#include <linux/kernel.h>
#include <asm/atomic.h>
#include <linux/delay.h>
#include <linux/wait.h>
#include "../general/wtspec.h"

#ifdef DEBUG
#define dbg(format, arg...) printk(KERN_DEBUG "%s:%s:%i: " format "\n","wt_driver" , __FILE__, __LINE__, ## arg)

#define dbg_print(format, arg...) printk(KERN_DEBUG format , ##arg)

#define  retAssert(ret,format, arg...)          \
        if(ret != WT_OK) \
        {                   \
            printk(KERN_DEBUG "%s:%s:%i: " format "\n","wt_driver" , __FILE__, __LINE__, ## arg);\
            return ret;\
        }

#define  retWarnning(ret,format, arg...)            \
        if(ret != WT_OK) \
        {                   \
            printk(KERN_DEBUG "%s:%s:%i: " format "\n","wt_driver" , __FILE__, __LINE__, ## arg);\
        }

#define  retBreak(Ret,format, arg...)           \
        if(ret != WT_OK) \
        {                   \
            printk(KERN_DEBUG "%s:%s:%i: " format "\n","wt_driver" , __FILE__, __LINE__, ## arg);\
            break;\
        }

#define  retContinue(Ret,format, arg...)            \
        if(ret != WT_OK) \
        {                   \
            printk(KERN_DEBUG "%s:%s:%i: " format "\n","wt_driver" , __FILE__, __LINE__, ## arg);\
            continue;\
        }
#else
#define dbg(format, arg...)
#define dbg_print(format, arg...)
#define retAssert(Ret,format, arg...)       if(ret != WT_OK){ return ret;}
#define retWarnning(ret, format, arg...)
#define retBreak(Ret,format, arg...)        if(ret != WT_OK){ break;}
#define retContinue(Ret,format, arg...)     if(ret != WT_OK){ continue;}
#endif

#define ALIGN_TO_64(x) (((x) + 63) & ~63)
#define ALIGN_TO_4(x) (((x) + 3) & ~3)



#define UNIT_TYPE_BACK          2
#define BOARD_UNIT_NUM          2                           //每个单元板上的硬件单元数量
#define BUSI_BOARD_TYPE_COUNT   2                           //业务单元板类型数量



#define DEV_BOARD_NUM           MAX_UNIT_NUM / BOARD_UNIT_NUM
#define BOARD_IRQ_NUM           4                           //每个板上的中断数
#define MAX_BUSI_UNIT_NUM       DEV_BOARD_NUM               //每种业务类型最大单元数

#define WT_VENDOR_ID            0X3333
#define WT_BACK_DEV_ID          0x1111
#define WT_BUSI_DEV_ID          0x2222
#define WT418_BUSI_DEV_ID       0x3333

#define WT418_PARENT_BUS_NUMBER_NORMAL     0
#define WT418_BUS_NUMBER_BUSI0     1
//pdev->pcidev->bus->parent->number
#define WT448_PARENT_BUS_NUMBER_NORMAL     2
#define WT448_PARENT_BUS_NUMBER_OPTICAL    5

//pdev->pcidev->bus->number
#define WT448_BUS_NUMBER_BUSI0     3
#define WT448_BUS_NUMBER_BUSI1     4
#define WT448_BUS_NUMBER_BUSI2     5
#define WT448_BUS_NUMBER_BUSI3     6

//pdev->pcidev->bus->number
#define WT448_FORWARD_BUS_NUMBER_BUSI0     6
#define WT448_FORWARD_BUS_NUMBER_BUSI1     7
#define WT448_FORWARD_BUS_NUMBER_BUSI2     8
#define WT448_FORWARD_BUS_NUMBER_BUSI3     9

#define WT428_BUS_NUMBER_BUSI0     3
#define WT428_BUS_NUMBER_BUSI1     4

#define RF_DIV_16_ON            1                       //射频板分频数16编译开关,默认分频数为32
#define RF_DIV_NUM              16                      //射频板分频数
#define FLASH_WAITE_COUNT       400000                  //查询FLASH传输操作是否完成时读取的最多次数

#define REG_CHECK_COUNT         1000                    //查询写寄存器(SPI_CTRL1/START等)是否成功时读取的最多次数
#define SPI_READ_COUNT          1000                    //查询SPI传输操作是否完成时读取的最多次数
#define SMBUS_READ_COUNT        40000                   //查询SMBUS传输操作是否完成时读取的最多次数
#define I2C_READ_COUNT          40000                   //查询I2C传输操作是否完成时读取的最多次数
#define PCIE_REG_BAR_SIZE       2048                    //PCIE寄存器bar空间大小
#define RF_SHF_TX_ADDR_READ     1                       //WT448射频板RF板移位寄存器回读使用SPI TX ADDR

enum WT_BUSI_BOARD_SLOT
{
    WT_BUSI_BOARD_SLOT_0 = 0,
    WT_BUSI_BOARD_SLOT_1,
    WT_BUSI_BOARD_SLOT_2,
    WT_BUSI_BOARD_SLOT_3,
    WT_BUSI_BOARD_SLOT_4,
    WT_BUSI_BOARD_SLOT_5,
    WT_BUSI_BOARD_SLOT_6,
    WT_BUSI_BOARD_SLOT_7,
    WT_BUSI_BOARD_SLOT_UNKOWN,
};

enum WLAN_MODE
{
    SISO_MODE,
    MIMO_MASTER_SLOT0,
    MIMO_MASTER_SLOT1,
    MIMO_MASTER_SLOT2,
    MIMO_MASTER_SLOT3,
};

enum TRIGGER_PORT_MODE
{
    TRIGGER_PORT_DIG = 0,
    TRIGGER_PORT_DEVM,
    TRIGGER_PORT_MODE_MAX,
};

//SPI寄存器类型
enum SPIRegTypeE
{
    SPI_REG_TX_TYPE,
    SPI_REG_RX_TYPE,
    SPI_REG_CTRL1_TYPE,
    SPI_REG_CTRL2_TYPE,
    SPI_REG_CONVST_TYPE,
    SPI_REG_TX_TYPE_HIGHT,
    SPI_REG_RX_TYPE_HIGHT,
    SPI_REG_TYPE_MAX,
};

//SMBUS寄存器类型
enum SMBUSRegTypeE
{
    SMBUS_REG_SLV_ADDR_TYPE,
    SMBUS_REG_COM_REG_TYPE,
    SMBUS_REG_TX_TYPE,
    SMBUS_REG_RX_TYPE,
    SMBUS_REG_CTRL1_TYPE,
    SMBUS_REG_CTRL2_TYPE,
    SMBUS_REG_INVALD_TYPE
};

//I2C寄存器类型
enum I2CRegTypeE
{
    I2C_REG_SLV_ADDR_TYPE,
    I2C_REG_COM_REG_TYPE,
    I2C_REG_TX_TYPE,
    I2C_REG_RX_TYPE,
    I2C_REG_CTRL1_TYPE,
    I2C_REG_CTRL2_TYPE,
    I2C_REG_INVALD_TYPE
};

//EEPROM/FLASH寄存器类型
enum RomRegTypeE
{
    ROM_REG_WR_START,
    ROM_REG_WR_DATA,
    ROM_REG_WR_ADDR,
    ROM_REG_WR_FULL,
    ROM_REG_WR_DONE,

    ROM_REG_RD_START,
    ROM_REG_RD_LEN,
    ROM_REG_RD_DATA,
    ROM_REG_RD_ADDR,
    ROM_REG_RD_EMPTY,

    ROM_REG_SECTOR_ERASE,
    ROM_PAGE_SIZE,
    ROM_REG_END
};

//*****************************单元板公用寄存器地址*****************************

#define AT88SC_DIRECTION                 0x0380                 //加密芯片数据口方向
#define AT88SC_CLOCK                     0x0384                 //加密芯片时钟
#define AT88SC_TX                        0x0388                 //加密芯片发送寄存器
#define AT88SC_RX                        0x038C                 //加密芯片接收寄存器


#define AT88SC_DIRECTION_418             (0x003E<< 2)           //加密芯片数据口方向
#define AT88SC_CLOCK_418                 (0x003F<< 2)           //加密芯片时钟
#define AT88SC_TX_418                    (0x0040<< 2)           //加密芯片发送寄存器
#define AT88SC_RX_418                    (0x0041<< 2)           //加密芯片接收寄存器

#define I2C_DATA_OUT                     wt_write_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_TX_418:AT88SC_TX,               0)
#define I2C_DATA_IN                      wt_write_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_DIRECTION_418:AT88SC_DIRECTION, 1)
#define I2C_SCL_HIGH                     wt_write_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_CLOCK_418:AT88SC_CLOCK,         1)
#define I2C_SCL_LOW                      wt_write_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_CLOCK_418:AT88SC_CLOCK,         0)
#define I2C_SDA_HIGH                     wt_write_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_DIRECTION_418:AT88SC_DIRECTION, 1)
#define I2C_SDA_LOW                      wt_write_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_DIRECTION_418:AT88SC_DIRECTION, 0)
#define I2C_SDA_IN                       (wt_read_direct_reg(pdev,pdev->testertype == HW_WT418? AT88SC_RX_418:AT88SC_RX) &             1)

//***********************************背板***************************************
#define BACK_VSA_MASTER_SLOT            0x0034          //MIMO VSA master单元所在的槽位
#define BACK_VSG_MASTER_SLOT            0x0038          //MIMO VSG master单元所在的槽位
#define BACK_TRIG_SELELT                0x03B0               //0:default, 1:devm
//功能单元结构体
struct dev_unit
{
    int type;                  //单元类型，VSA/VSG/...
    int id;                    //每个业务板上有多个模块，ID表示是哪个模块
    int number;                //仪器上该业务模块的编号
    int is_open_ok;            //业务板是否正常打开，否则只能读写寄存器，不接受其他IOCTL命令      
    int testertype;            //测试仪类型 WT448/WT428/WT418
    int version;               //背板/基带板硬件版本 VA/VB,
    int swbversion;            //开关板硬件版本
    int revision;              //设备修订版本
    int slot;                  //业务板槽位号
    int wlan_mode;             //WLAN MODE
    struct fasync_struct *async_queue;   //异步事件队列
    struct pn_info *pn;        //PN信息
    int sample_cnt;            //采样点数
    int sample_rate;           //采样率
    int done_irq;              //VSA/VSG完成中断
    volatile int complete;     //是否完成，1表示完成
    atomic_t open_cnt;         //打开次数
    struct work_struct done;
    struct rw_semaphore *sem;  //防止DMA期间读写寄存器用的信号量
    wait_queue_head_t *wait_head;
    spinlock_t *lock;          //读写寄存器用的自旋锁
    void *iobase;              //所使用的bar对应的地址
    int ifgstatus;             //IFG控制功能状态
    int functionid;            //当前IOCTL任务的功能ID
    int ext_mode;              //扩展模式
    int write_delay;           //读寄存器延时
    int read_delay;            //写寄存器延时
    struct dev_unit *neighbor; //板上的另一个链路
    struct pci_dev *pcidev;    //PCI设备
    struct device *pdev;
    struct cdev cdev;
};

/*--------------公用基础接口------------------------*/
//*****************************************************************************
// 功能: 直接读IO内存对应的寄存器数据，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  RegAddr：寄存器地址
// 返回值：寄存器数据
//*****************************************************************************
static inline int wt_read_direct_reg(struct dev_unit *pdev, int RegAddr)
{
    int value = ioread32(pdev->iobase + RegAddr);
    //printk("wt_read_direct_reg Type%d, ModId%d, Adrr:0x%x, Value:0x%x!\n", pdev->type, pdev->id, RegAddr, value);
    //ndelay(500); //FPGA运行慢，需要写延时
    if(pdev->read_delay)
    {
        ndelay(pdev->read_delay); //FPGA运行慢，需要写延时
    }
    return value;
}

//*****************************************************************************
// 功能: 直接写IO内存对应的寄存器数据，不相对单元板内部模块ID偏移(VSA/VSG/BackBoard Unit)
// 参数 [IN]：pdev:功能单元结构体指针  RegAddr：寄存器地址 Value：寄存器数据
// 返回值：成功为0  失败为-1
//*****************************************************************************
static inline void wt_write_direct_reg(struct dev_unit *pdev, int RegAddr, int Value)
{
    iowrite32(Value, pdev->iobase + RegAddr);
    ndelay(pdev->write_delay); //FPGA运行慢，需要写延时
}

#endif
