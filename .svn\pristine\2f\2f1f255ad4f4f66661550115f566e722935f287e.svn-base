#include "includeall.h"
#include "WaveGenerator.h"
#include "WaveGenerator_11BE.h"

//static int PerTestSampleRate = MAX_SAMPLE_RATE;
const double RefPowerOffset = 13;
typedef struct
{
    InstrumentHandle *pInstrument;
    VsaParameter *pVsaParameter;
    volatile unsigned int Ack;
#ifndef LINUX
    volatile unsigned int VsaIsRuning;
#else
    atomic_flag VsaIsRuning = ATOMIC_FLAG_INIT;
#endif
} stPerThreadParam;

#pragma region FunRegion

unsigned char char2Byte(const char macchar)
{
    char tmp = toupper(macchar);
    if (tmp >= '0' && tmp <= '9')
    {
        return tmp - '0';
    }
    else
    {
        return tmp - 'A' + 10;
    }
}

int Int2Bytes(int *strSrc, unsigned char *macDest, int length)
{
    for (int i = 0; i < length; i++)
    {
        macDest[i] = strSrc[i] & 0xff;
    }
    return 0;
}

int HexString2Bytes(unsigned char *strSrc, unsigned char *strDest, int length)
{
    unsigned char *srcTemp = strSrc;
    int i = 0;
    unsigned char c;
    for (i = 0; i < length; i++)
    {
        c = char2Byte(*srcTemp++);
        if (c == 16)
        {
            return -1;
        }
        strDest[i] = (c << 4);
        c = char2Byte(*srcTemp++);
        if (c == 16)
        {
            return -1;
        }
        strDest[i] |= c;
    }
    return 0;
}

int Bytes2HexString(unsigned char *src, unsigned char *dest, int len)
{
    char tmp[4] = { 0 };
    for (int i = 0; i < len; i++)
    {
        memset(tmp, 0, sizeof(tmp));
        sprintf(tmp, "%02X", src[i]);
        strcat((char *)dest, tmp);
    }
    return 0;
}

int GetSignalType(const int standard, const int bw, const int mcs, const int streams)
{
    switch (standard)
    {
    case  IEEE802_11_n:
        if (bw == 20)
        {
            return MCS0 - mcs;
        }
        else if (bw == 40)
        {
            return MCS0_40 - mcs;
        }
        break;
    case IEEE802_11_ac:
        if (bw == 20)
        {
            if (3 == streams && mcs < 12 && mcs >= 0)
            {
                return NSS3_MCS0 - mcs;
            }
            else if (3 != streams && mcs < 12 && mcs >= 0)
            {
                switch (streams)
                {
                case 1:
                    return NSS1_MCS0 - mcs;
                case 2:
                    return NSS2_MCS0 - mcs;
                case 4:
                    return NSS4_MCS0 - mcs;
                case 5:
                    return NSS5_MCS0 - mcs;
                case 6:
                    return NSS6_MCS0 - mcs;
                case 7:
                    return NSS7_MCS0 - mcs;
                case 8:
                    return NSS8_MCS0 - mcs;
                default:
                    break;
                }
            }
        }
        else if (bw == 40 && mcs < 12 && mcs >= 0)
        {
            switch (streams)
            {
            case 1:
                return NSS1_MCS0_40 - mcs;
            case 2:
                return NSS2_MCS0_40 - mcs;
            case 3:
                return NSS3_MCS0_40 - mcs;
            case 4:
                return NSS4_MCS0_40 - mcs;
            case 5:
                return NSS5_MCS0_40 - mcs;
            case 6:
                return NSS6_MCS0_40 - mcs;
            case 7:
                return NSS7_MCS0_40 - mcs;
            case 8:
                return NSS8_MCS0_40 - mcs;
            default:
                break;
            }

        }
        else if (bw == 80 && mcs < 12 && mcs >= 0)
        {
            switch (streams)
            {
            case 1:
                return NSS1_MCS0_80 - mcs;
            case 2:
                return NSS2_MCS0_80 - mcs;
            case 3:
                return NSS3_MCS0_80 - mcs;
            case 4:
                return NSS4_MCS0_80 - mcs;
            case 5:
                return NSS5_MCS0_80 - mcs;
            case 6:
                return NSS6_MCS0_80 - mcs;
            case 7:
                return NSS7_MCS0_80 - mcs;
            case 8:
                return NSS8_MCS0_80 - mcs;
            default:
                break;
            }
        }
        else if (bw == 160 && mcs < 12 && mcs >= 0)
        {
            switch (streams)
            {
            case 1:
                return NSS1_MCS0_160 - mcs;
            case 2:
                return NSS2_MCS0_160 - mcs;
            case 3:
                return NSS3_MCS0_160 - mcs;
            case 4:
                return NSS4_MCS0_160 - mcs;
            case 5:
                return NSS5_MCS0_160 - mcs;
            case 6:
                return NSS6_MCS0_160 - mcs;
            case 7:
                return NSS7_MCS0_160 - mcs;
            case 8:
                return NSS8_MCS0_160 - mcs;
            default:
                break;
            }
        }
        break;
    default:
        break;
    }
    return -1;
}

int GetStandardType(const int standard)
{
    switch (standard)
    {
    case WT_DEMOD_11B:
        return IEEE802_11_b_g_DSSS;
    case WT_DEMOD_11AG:
        return IEEE802_11_a_g_OFDM;
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        return IEEE802_11_n;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        return IEEE802_11_ac;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11AX_160_160M:
        return IEEE802_11_ax;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_160_160M:
    case WT_DEMOD_11BE_320M:
        return IEEE802_11_be;
    case WT_DEMOD_BT:
        return Bluetooth;
    case WT_DEMOD_CW:
        return ContinuousWaves;
    case WT_DEMOD_ZIGBEE:
        return IEEE_Zigbee;
    default:
        break;
    }
    return IEEE802_11_a_g_OFDM;
}

void DefaultPSDU(WIFI_PSDU *psdu, int psduLen)
{
    const char *frm = "0800";
    const char *mac1 = "FFFFFFFFFFFF";
    const char *mac2 = "000000000000";
    const char *mac3 = "000000000000";
    const char *mac4 = "000000000000";
    const char *DurationID = "0000";
    const char *SequenceControl = "0000";

    memset(psdu, 0, sizeof(WIFI_PSDU));
    psdu->psduLen = psduLen;
    psdu->CRCCheckEnable = 1;
    psdu->MacHeaderEnable = 1;
    psdu->psduType = PSDUType_RANDOM;
    psdu->scrambler = 24;

    HexString2Bytes((unsigned char *)frm, psdu->FrameControl, sizeof(psdu->FrameControl));
    HexString2Bytes((unsigned char *)mac1, psdu->MacAddress1, sizeof(psdu->MacAddress1));
    HexString2Bytes((unsigned char *)mac2, psdu->MacAddress2, sizeof(psdu->MacAddress2));
    HexString2Bytes((unsigned char *)mac3, psdu->MacAddress3, sizeof(psdu->MacAddress3));
    HexString2Bytes((unsigned char *)mac4, psdu->MacAddress4, sizeof(psdu->MacAddress4));
    HexString2Bytes((unsigned char *)DurationID, psdu->DurationID, sizeof(psdu->DurationID));
    HexString2Bytes((unsigned char *)SequenceControl, psdu->SequenceControl, sizeof(psdu->SequenceControl));
}

s32 SavePnData(stPNDat *data, int len)
{
    FILE *fp = fopen("iq.csv", "ab");
    if (fp)
    {
        for (int i = 0; i < len; i++)
        {
            fprintf(fp, "%lf,%lf\r\n", data->dReal, data->dImag);
            data++;
        }
        fclose(fp);
    }
    return 0;
}

void AXDefaultWaveParam_SU(Set11AX_SU *PN11ax_su, int ppdu)
{
    PN11ax_su->STBC = 0;
    PN11ax_su->UL_DL = 0;
    PN11ax_su->MCS = 7;
    if (HE_EXTEND_PPDU == ppdu)
    {
        PN11ax_su->MCS = 0;
    }
    PN11ax_su->CodingType = 0;
    PN11ax_su->NSS = 1;
    PN11ax_su->GILTFSize = 3;
    PN11ax_su->PE = 0;
    PN11ax_su->PE_Type = 0;
    PN11ax_su->BeamChange = 1;
    PN11ax_su->BSScolor = 63;
    PN11ax_su->TXOP = 127;
    PN11ax_su->Doppler = 0;
    PN11ax_su->DCM = 0;
    PN11ax_su->Midamble_Periodicity = 1;
    DefaultPSDU(&PN11ax_su->psdu);
}

void AXDefaultWaveParam_MU(Set11AX_MU *PN11ax_mu)
{
    memset(PN11ax_mu, 0, sizeof(Set11AX_MU));

    PN11ax_mu->STBC = 0;
    PN11ax_mu->UL_DL = 0;

    for (int i = 0; i < AX_USER_COUNT; i++)
    {
        PN11ax_mu->RU[i].User[0].MCS = 7;
        PN11ax_mu->RU[i].User[0].CodingType = 0;
        PN11ax_mu->RU[i].User[0].NSS = 1;
        PN11ax_mu->RU[i].User[0].AID = 1;
        PN11ax_mu->RU[i].User[0].DCM = 0;
        PN11ax_mu->RU[i].User[0].PowerFact = 1.0;
        for (int j = 0; j < MUMIMO_8_USER; j++)
        {
            DefaultPSDU(&PN11ax_mu->RU[i].User[0].psdu);
        }
    }

    PN11ax_mu->GILTFSize = 3;
    PN11ax_mu->PE = 0;
    PN11ax_mu->PE_Type = 0;
    PN11ax_mu->BSScolor = 63;
    PN11ax_mu->TXOP = 127;
    PN11ax_mu->Doppler = 0;
    PN11ax_mu->Midamble_Periodicity = 1;

    PN11ax_mu->SIGBMCS = 2;
    PN11ax_mu->SIGBDCM = 0;
    PN11ax_mu->SIGB_Compression = 0;
}

void AXDefaultWaveParam_TB(Set11AX_TB *PN11ax_tb)
{
    memset(PN11ax_tb, 0, sizeof(Set11AX_TB));

    PN11ax_tb->STBC = 0;
    PN11ax_tb->NumLTFSymbols = 2;
    for (int segmentID = 0; segmentID < MAX_SEGMENT; segmentID++)
    {
        for (int i = 0; i < AX_USER_COUNT; i++)
        {
            PN11ax_tb->RU[segmentID][i].UserNum = 0;
            if (0 == i && 0 == segmentID)
            {
                PN11ax_tb->RU[segmentID][i].UserNum = 1;
            }
            for (int j = 0; j < MUMIMO_8_USER; j++)
            {
                PN11ax_tb->RU[segmentID][i].User[j].MCS = 7;
                PN11ax_tb->RU[segmentID][i].User[j].CodingType = 1;
                PN11ax_tb->RU[segmentID][i].User[j].NSS = 1;
                PN11ax_tb->RU[segmentID][i].User[j].AID = 1;
                PN11ax_tb->RU[segmentID][i].User[j].DCM = 0;
                PN11ax_tb->RU[segmentID][i].User[j].RuIndex = 0;
                PN11ax_tb->RU[segmentID][i].User[j].SegmentIndex = segmentID;
                PN11ax_tb->RU[segmentID][i].User[j].NSSStart = 1;
                PN11ax_tb->RU[segmentID][i].User[j].PowerFact = 1.0;
                PN11ax_tb->RU[segmentID][i].User[j].PowerScale = 1.0;
                PN11ax_tb->RU[segmentID][i].User[j].FreqErr = 0.0;
                DefaultPSDU(&PN11ax_tb->RU[segmentID][i].User[j].psdu);
            }
        }
    }
    PN11ax_tb->UserBuildUpMode = 0;
    PN11ax_tb->RUNum[0] = 1;
    PN11ax_tb->RUNum[1] = 0;

    PN11ax_tb->GILTFSize = 3;
    PN11ax_tb->PE = 0;
    PN11ax_tb->PE_Type = 0;
    PN11ax_tb->BSScolor = 63;
    PN11ax_tb->TXOP = 127;
    PN11ax_tb->Doppler = 0;
    PN11ax_tb->Midamble_Periodicity = 1;
}

void AXDefaultWaveParam_TF(int psduType, TriggerFrameSetting *tf)
{
    do
    {
        if (PSDUType_TriggerFrame != psduType)
        {
            break;
        }

        memset(tf, 0, sizeof(TriggerFrameSetting));
        tf->TBLength = 1024;
        tf->TBUserNum = 1;
        tf->TBAfactor = 4;
        for (int i = 0; i < AX_USER_COUNT; i++)
        {
            tf->TBCoding[i] = 1;
            tf->TBMCS[i] = 11;
            tf->TBSSStart[i] = 1;
            tf->TBSSCount[i] = 1;
            tf->TBRSSI[i] = 127;
            tf->TBAID[i] = 1;
        }
    } while (0);

}

void DefaultWaveParameter_11N(GenWaveWifiStruct_API *pnParameters)
{
    int demod = pnParameters->commonParam.standard;
    switch (demod)
    {
    case WT_DEMOD_11N_40M:
        pnParameters->commonParam.bandwidth = 40;
        break;
    default:
        pnParameters->commonParam.bandwidth = 20;
        break;
    }
    pnParameters->PN11n.HtFrmType = FRM_HT_MF;
    pnParameters->PN11n.isAggregation = FALSE;
    pnParameters->PN11n.isShortGI = FALSE;
    pnParameters->PN11n.MCS = 7;
    pnParameters->PN11n.NotSounding = TRUE;
    pnParameters->PN11n.NSS = 1;
    pnParameters->PN11n.SmoothingEnable = TRUE;
    pnParameters->PN11n.STBC = 0;
    DefaultPSDU(&pnParameters->PN11n.psdu);
}

void DefaultWaveParameter_11AC(GenWaveWifiStruct_API *pnParameters)
{
    int demod = pnParameters->commonParam.standard;
    switch (demod)
    {
    case WT_DEMOD_11AC_40M:
        pnParameters->commonParam.bandwidth = 40;
        break;
    case WT_DEMOD_11AC_80M:
        pnParameters->commonParam.bandwidth = 80;
        break;
    case WT_DEMOD_11AC_160M:
        pnParameters->commonParam.bandwidth = 160;
        break;
    case WT_DEMOD_11AC_80_80M:
        pnParameters->commonParam.bandwidth = 160;
        pnParameters->commonParam.segment = 2;
        break;
    default:
        pnParameters->commonParam.bandwidth = 20;
        break;
    }
    pnParameters->PN11ac.isShortGI = FALSE;
    pnParameters->PN11ac.MCS = 7;
    pnParameters->PN11ac.NSS = 1;
    pnParameters->PN11ac.STBC = 0;
    DefaultPSDU(&pnParameters->PN11ac.psdu);
}

void DefaultWaveParameter_11AX(GenWaveWifiStruct_API *pnParameters)
{
    int demod = pnParameters->commonParam.standard;
    int ppdu = pnParameters->commonParam.subType;
    switch (demod)
    {
    case WT_DEMOD_11AX_40M:
        pnParameters->commonParam.bandwidth = 40;
        break;
    case WT_DEMOD_11AX_80M:
        pnParameters->commonParam.bandwidth = 80;
        break;
    case WT_DEMOD_11AX_160M:
        pnParameters->commonParam.bandwidth = 160;
        break;
    case WT_DEMOD_11AX_80_80M:
        pnParameters->commonParam.bandwidth = 160;
        pnParameters->commonParam.segment = 2;
        break;
    case WT_DEMOD_11AX_160_160M:
        pnParameters->commonParam.bandwidth = 320;
        pnParameters->commonParam.segment = 2;
        break;
    default:
        pnParameters->commonParam.bandwidth = 20;
        break;
    }

    switch (ppdu)
    {
    case HE_SU_PPDU:
    case HE_EXTEND_PPDU:
        AXDefaultWaveParam_SU(&pnParameters->PN11ax_SU, ppdu);
        break;
    case HE_MU_PPDU:
        AXDefaultWaveParam_MU(&pnParameters->PN11ax_MU);
        break;
    case HE_TB_PPDU:
        AXDefaultWaveParam_TB(&pnParameters->PN11ax_TB);
        break;
    default:
        break;
    }
}

void BeDefaultWaveParam_MU(Set11BE_MU *PN11be_mu)
{
    memset(PN11be_mu, 0, sizeof(Set11BE_MU));

    PN11be_mu->STBC = 0;
    PN11be_mu->UL_DL = 0;

    for (int i = 0; i < BE_RU_COUNT; i++)
    {
        PN11be_mu->RU[i].User[0].MCS = 7;
        PN11be_mu->RU[i].User[0].CodingType = 0;
        PN11be_mu->RU[i].User[0].NSS = 1;
        PN11be_mu->RU[i].User[0].AID = 1;
        PN11be_mu->RU[i].User[0].DCM = 0;
        PN11be_mu->RU[i].User[0].PowerFact = 1.0;
        for (int j = 0; j < MUMIMO_8_USER; j++)
        {
            DefaultPSDU(&PN11be_mu->RU[i].User[0].psdu);
        }
    }

    PN11be_mu->GILTFSize = 3;
    PN11be_mu->PE = 0;
    PN11be_mu->PE_Type = 0;
    PN11be_mu->BSScolor = 63;
    PN11be_mu->TXOP = 127;
    PN11be_mu->Doppler = 0;
    PN11be_mu->Midamble_Periodicity = 1;

    PN11be_mu->SIGBMCS = 2;
}

void BeDefaultWaveParam_TB(Set11BE_TB *PN11be_tb)
{
    memset(PN11be_tb, 0, sizeof(Set11BE_TB));

    PN11be_tb->STBC = 0;
    PN11be_tb->NumLTFSymbols = 2;
    for (int segmentID = 0; segmentID < BE_MAX_SEGMENT; segmentID++)
    {
        for (int i = 0; i < AX_USER_COUNT; i++)
        {
            PN11be_tb->RU[segmentID][i].UserNum = 0;
            if (0 == i && 0 == segmentID)
            {
                PN11be_tb->RU[segmentID][i].UserNum = 1;
            }
            for (int j = 0; j < MUMIMO_8_USER; j++)
            {
                PN11be_tb->RU[segmentID][i].User[j].MCS = 7;
                PN11be_tb->RU[segmentID][i].User[j].CodingType = 1;
                PN11be_tb->RU[segmentID][i].User[j].NSS = 1;
                PN11be_tb->RU[segmentID][i].User[j].AID = 1;
                PN11be_tb->RU[segmentID][i].User[j].DCM = 0;
                PN11be_tb->RU[segmentID][i].User[j].RuIndex = 0;
                PN11be_tb->RU[segmentID][i].User[j].SegmentIndex = segmentID;
                PN11be_tb->RU[segmentID][i].User[j].NSSStart = 1;
                PN11be_tb->RU[segmentID][i].User[j].PowerFact = 1.0;
                PN11be_tb->RU[segmentID][i].User[j].PowerScale = 1.0;
                PN11be_tb->RU[segmentID][i].User[j].FreqErr = 0.0;
                DefaultPSDU(&PN11be_tb->RU[segmentID][i].User[j].psdu);
            }
        }
    }
    PN11be_tb->UserBuildUpMode = 0;
    PN11be_tb->RUNum[0] = 1;
    PN11be_tb->RUNum[1] = 0;

    PN11be_tb->GILTFSize = 3;
    PN11be_tb->PE = 0;
    PN11be_tb->PE_Type = 0;
    PN11be_tb->BSScolor = 63;
    PN11be_tb->TXOP = 127;
    PN11be_tb->Doppler = 0;
    PN11be_tb->Midamble_Periodicity = 1;
    PN11be_tb->SIG1_Disregard = 63;
    PN11be_tb->Valid_B2 = 1;
    PN11be_tb->SIG2_Disregard = 31;
}

void DefaultWaveParameter_11BE(GenWaveWifiStruct_API *pnParameters)
{
    int demod = pnParameters->commonParam.standard;
    int ppdu = pnParameters->commonParam.subType;

    pnParameters->commonParam.segment = 1;
    switch (demod)
    {
    case WT_DEMOD_11BE_40M:
        pnParameters->commonParam.bandwidth = 40;
        break;
    case WT_DEMOD_11BE_80M:
        pnParameters->commonParam.bandwidth = 80;
        break;
    case WT_DEMOD_11BE_160M:
        pnParameters->commonParam.bandwidth = 160;
        break;
    case WT_DEMOD_11BE_80_80M:
        pnParameters->commonParam.bandwidth = 160;
        pnParameters->commonParam.segment = 2;
        break;
    case WT_DEMOD_11BE_320M:
        pnParameters->commonParam.bandwidth = 320;
        break;
    case WT_DEMOD_11BE_160_160M:
        pnParameters->commonParam.bandwidth = 320;
        pnParameters->commonParam.segment = 2;
        break;
    default:
        pnParameters->commonParam.bandwidth = 20;
        break;
    }

    switch (ppdu)
    {
    case EHT_MU_PPDU:
        BeDefaultWaveParam_MU(&pnParameters->PN11be_MU);
        break;
    case EHT_TB_PPDU:
        BeDefaultWaveParam_TB(&pnParameters->PN11be_TB);
        break;
    default:
        break;
    }
}

static void DefaultCommonParam(int demod, int ppdu, PNSettingBase *commonParam)
{
    commonParam->samplingRate = static_cast<int>(MAX_SMAPLE_RATE_API);
    commonParam->ClockRate = 1.0;
    commonParam->Gap = 10 * Us;
    commonParam->bandwidth = 20;
    commonParam->standard = demod;
    commonParam->NSS = 1;
    commonParam->segment = 1;
    commonParam->subType = ppdu;
    commonParam->Snr = 200;
    commonParam->SpatialExtension = 0;
}

static void DefaultCommonParamGLE(int demod, int ppdu, PNSettingBase* commonParam)
{
	commonParam->samplingRate = static_cast<int>(MID_SAMPLE_RATE);
	commonParam->ClockRate = 1;
	commonParam->Gap = 10 * Us;
	commonParam->bandwidth = 1;
	commonParam->standard = demod;
	commonParam->NSS = 1;
	commonParam->segment = 1;
	commonParam->subType = ppdu;
	commonParam->Snr = 200;
	commonParam->SpatialExtension = 0;
	commonParam->Duplicate = 0;
	commonParam->ReducePARA = 0;
}

#if 0
static void DefaultFilterParam_LTE(Alg_4G_Filter &Param)
{
    Param.Type = 2;
    Param.Fs = 30720000;
    Param.MaxOrder = 512;
    Param.FpassFactor = 0.6;
    Param.FstopFactor = 0.65;
    Param.PassRipple = 0.05;
    Param.StopAtten = 45;
    Param.Optimization = 0;
    Param.RollOffFactor = 0.1;
    Param.CutOffFrqFactor = 0.11;
    Param.CutOffFreqShift = 0;
    Param.SmoothFactor = 10;
}

static void DefaultGeneralParam_LTE(Alg_4G_GeneralInfo &Param)
{
    memset(&Param, 0, sizeof(Alg_4G_GeneralInfo));
    DefaultFilterParam_LTE(Param.Filter);
    Param.GenWaveMode = 0;
    Param.GenWaveNo = 0;
    Param.GenSequenceLen = 1;
    Param.PowerRefType = 0; // 未明确
}

static void DefaultCommonParam3GPP(int demod, PNSetBaseType &commonParam)
{
    memset(&commonParam, 0, sizeof(PNSetBaseType));
    commonParam.standard = demod; // 0x1000为5G,0x1001为4G
    commonParam.subType = 0;
    commonParam.bandwidth = 0; // 未明确
    commonParam.samplingRate = 30720000;
    commonParam.NSS = 1;       // 未明确
    commonParam.segment = 1;   // 未明确
    commonParam.FreqErr = 0;
    commonParam.IQImbalanceAmp = 0;
    commonParam.IQImbalancePhase = 0;
    commonParam.DCOffset_I = 0;
    commonParam.DCOffset_Q = 0;
    commonParam.ClockErr = 0;
    commonParam.Snr = 200;
    commonParam.Gap = 0;              // 未明确
    commonParam.FlatFactor = 0;
    commonParam.ReducePARA = 0;        // 未明确
    commonParam.PhaseNoiseFlg = 0;
    commonParam.PhaseNoiseFactor[0] = -84;
    commonParam.PhaseNoiseFactor[1] = -100;
    commonParam.PhaseNoiseFactor[2] = -96;
    commonParam.PhaseNoiseFactor[3] = -109;
    commonParam.PhaseNoiseFactor[4] = -122;
    commonParam.PhaseNoiseFactor[5] = -200;

    switch (demod)
    {
    case ALG_3GPP_STD_NB_IOT:
        commonParam.samplingRate = 120 * MHz_API;
        break;

    default:
        commonParam.samplingRate = static_cast<int>(DEFAULT_3GPP_SAMPLE_RATE);
        break;
    }
}

static void DefaultCellParam_LTE(Alg_4G_ULCellType Param[ALG_4G_MAX_CELL_NUM])
{
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; ++i)
    {
        Param[i].CellIdx = i;
        if (i == 0)
        {
            Param[i].State = 1;
        }
        else
        {
            Param[i].State = 0;
        }
        Param[i].PhyCellID = 0;
        Param[i].ChannelBW = 20 * 1e6;
        Param[i].Duplexing = ALG_3GPP_FDD;
        Param[i].ULDLConfig = 0;
        Param[i].SpecialSubfrmCfg = 0;
        Param[i].Power = 0; // 未明确
        Param[i].N1Dmrs = 0;
    }
}

static void DefaultCellParam_LTE(Alg_4G_DLCellType Param[ALG_4G_MAX_CELL_NUM])
{
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; ++i)
    {
        Param[i].CellIdx = i;
        if (i == 0)
        {
            Param[i].State = 1;
        }
        else
        {
            Param[i].State = 0;
        }
        Param[i].PhyCellID = 0;
        Param[i].ChannelBW = 10 * 1e6;
        Param[i].Duplexing = ALG_3GPP_FDD;
        Param[i].ULDLConfig = 0;
        Param[i].SpecialSubfrmCfg = 0;
        Param[i].Power = 0;
        Param[i].PdschStart = 1; // 未明确
        Param[i].PhichResource = 0;
        Param[i].PhichDuration = 0;
    }
}

static void DefaultMultiCellParam_LTE(Alg_4G_ULMultiCellType &Param)
{
    Param.CarrAggrState = 0;
    DefaultCellParam_LTE(Param.Cell);
    Param.CyclicPrefix = 0;
    Param.GroupHop = 0;
    Param.SequenceHop = 0;
    Param.DeltaSeqShift = 0;
}

static void DefaultMultiCellParam_LTE(Alg_4G_DLMultiCellType &Param)
{
    Param.PdschScheduling = 0;
    Param.CarrAggrState = 0;
    DefaultCellParam_LTE(Param.Cell);
    Param.CyclicPrefix = 0;
    Param.SyncTxAntenna = 0;
    Param.PsyncPower = 0;
    Param.SsyncPower = 0;
    Param.RefSignalPower = 0;
    Param.PdschPB = 0;
    Param.PbchRatioRho = 0;
    Param.PdcchRatioRho = 0;
    Param.TxAntennaNum = 1;
}

static void DefaultUePuschParam_LTE(Alg_4G_UePuschType Pusch[ALG_4G_MAX_CELL_NUM])
{
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; ++i)
    {
        Pusch[i].DataType = 0;
        Pusch[i].Initialization = 1;
        Pusch[i].TxMode = 0;
        Pusch[i].MaxNumAP = 1;
        Pusch[i].Scramble = 1;
        Pusch[i].ChanCodingState = 1;
        Pusch[i].ChanCodingMode = 0;
        Pusch[i].Enable256QAM = 0;
    }
}

static void DefaultUeParam_LTE(Alg_4G_ULUeType &Param)
{
    Param.UeID = 0;
    Param.UePower = 0;
    Param.UseMode = 0;
    DefaultUePuschParam_LTE(Param.Pusch);
}

static void DefaultUeParam_LTE(Alg_4G_DLUeType &Param)
{
    Param.UeID = 0;
    Param.Scramble = 1;
    Param.ChanCodingState = 0;
    Param.ApMapping = 1;
    Param.UECategory = 1;
    Param.CodebookIndex = 0;
    Param.DataType = 0;
    Param.Initialization = 0;
    Param.PdschPA = 0;
    Param.TxMode = 1;
    Param.McsTable = 1;
    Param.TbsIndexAlt = 0;
}

static void DefaultPucchParam_LTE(Alg_4G_PucchType &Pucch)
{
    Pucch.State = 0;
    Pucch.Formate = 0; // 未明确
    Pucch.NPucch = 0;  // 未明确
    Pucch.Power = 0;   // 未明确
}

static void DefaultPuschParam_LTE(Alg_4G_PuschType Pusch[ALG_4G_MAX_CELL_NUM])
{
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; ++i)
    {
        Pusch[i].CellIdx = i;
        if (i == 0)
        {
            Pusch[i].State = 1;
        }
        else
        {
            Pusch[i].State = 0;
        }
        Pusch[i].RBSetNum = 1;
        Pusch[i].RBNum[0] = 100;
        Pusch[i].RBNum[1] = 100;
        Pusch[i].RBOffset[0] = 0;
        Pusch[i].RBOffset[1] = 0;
        Pusch[i].Power = 0;
        Pusch[i].FreqHopState = 0;
        Pusch[i].Precoding = 0;
        Pusch[i].LayerNum = 1;
        Pusch[i].AntennaNum = 1;
        Pusch[i].CodebookIdx = 0;
        Pusch[i].CyclicShiftField = 0;
        Pusch[i].Codeword = 1;
        Pusch[i].Encode.McsCfgMode = 0;
        Pusch[i].Encode.Mcs[0] = 0;
        Pusch[i].Encode.Mcs[1] = 0;
        Pusch[i].Encode.Modulate[0] = 2;
        Pusch[i].Encode.Modulate[1] = 2;
        Pusch[i].Encode.PayloadSize[0] = 1500;
        Pusch[i].Encode.PayloadSize[1] = 1500;
        Pusch[i].Encode.RedunVerIdx[0] = 0;
        Pusch[i].Encode.RedunVerIdx[1] = 0;
    }
}

static void DefaultChanTypeParam_LTE(Alg_4G_ULChanType Chan[ALG_4G_MAX_SUBFRAME_NUM])
{
    for (int i = 0; i < ALG_4G_MAX_SUBFRAME_NUM; ++i)
    {
        Chan[i].SubfrmIdx = i;
        if (i == 0)
        {
            Chan[i].State = 1;
        }
        else
        {
            Chan[i].State = 0;
        }
        DefaultPucchParam_LTE(Chan[i].Pucch);
        DefaultPuschParam_LTE(Chan[i].Pusch);
    }
}

static void DefaultPbchParam_LTE(Alg_4G_PbchType &PBCH)
{
    PBCH.State = 1;
    PBCH.Scrambling = 1;
    PBCH.Precoding = 0;
    PBCH.SFNOffset = 0;
    for (int i = 0; i < 16; ++i)
    {
        PBCH.SpareBit[i] = 0;
    }
}

static void DefaultDCIDLParam_LTE(Alg_4G_DCIInfo &DCI_DL)
{
    DCI_DL.User = 0;
    DCI_DL.DCIFormat = 1;
    DCI_DL.SearchSpace = 1;
    DCI_DL.Format1.ResAllocateHeader = 0;
    DCI_DL.Format1.ResBlkAssign = 1;
    DCI_DL.Format1.MCS = 0;
    DCI_DL.Format1.HarqProcNum = 0;
    DCI_DL.Format1.NewDataInd = 1;
    DCI_DL.Format1.RvIdx = -1;
    DCI_DL.Format1.TPCCommand = 0;
    DCI_DL.Format1.DLAssignment = 0;
    DCI_DL.Format1A.ResBlkAssign = 1;
    DCI_DL.Format1A.NewDataInd = 1;
    DCI_DL.PDCCHFormat = 0;
    DCI_DL.CCEIdx = 0;
}

static void DefaultDCIULParam_LTE(Alg_4G_DCIInfo &DCI_UL)
{
    DCI_UL.State = 1;
    DCI_UL.User = 0;
    DCI_UL.DCIFormat = 0;
    DCI_UL.SearchSpace = 1;
    DCI_UL.Format0.FreqHop = 0;
    DCI_UL.Format0.ResBlkAssign = 0;
    DCI_UL.Format0.MCS = 0;
    DCI_UL.Format0.NewDataInd = 0;
    DCI_UL.Format0.TPCCommand = 0;
    DCI_UL.Format0.CyclicShiftForDMRS = 0;
    DCI_UL.Format0.ULIndex = 0;
    DCI_UL.Format0.DAI = 0;
    DCI_UL.Format0.CSIResquest = 0;
    DCI_UL.Format0.ResAllocateType = 0;
    DCI_UL.PDCCHFormat = 0;
    DCI_UL.CCEIdx = 1;
}

static void DefaultPdcchParam_LTE(Alg_4G_PdcchType &PDCCH)
{
    PDCCH.Format = -2;
    PDCCH.DummyCCEType = 1;
    PDCCH.DataType = 0;
    PDCCH.PDCCHNum = 1;
    PDCCH.Power = 0;
    DefaultDCIDLParam_LTE(PDCCH.DCI[0]);
    DefaultDCIULParam_LTE(PDCCH.DCI[1]);
}

static void DefaultPdschParam_LTE(Alg_4G_PdschType &PDSCH)
{
    PDSCH.State = 1;
    PDSCH.ResAllocateType = 2;
    PDSCH.VRBAssignment = 0;
    memset(PDSCH.RBGBitmap, 0, sizeof(char)*32);
    PDSCH.RBNum = 50;
    PDSCH.AutoOffset = 1;
    PDSCH.RBOffset = 0;
    PDSCH.SymbOffset = 0;
    PDSCH.Precoding = 0;
    PDSCH.LayerNum = 1;
    PDSCH.CyclicDelayDiversity = 0;
    PDSCH.CodebookIdx = 0;
    PDSCH.Codeword = 1;
    PDSCH.MCSConfigMode = 0;
    PDSCH.Mcs[0] = 0;
    PDSCH.Mcs[1] = 0;
    PDSCH.Modulate[0] = 2;
    PDSCH.Modulate[1] = 2;
    PDSCH.PayloadSize[0] = 1500;
    PDSCH.PayloadSize[1] = 1500;
    PDSCH.RedunVerIdx[0] = 0;
    PDSCH.RedunVerIdx[1] = 0;
    PDSCH.IRConfigMode = 0;
    PDSCH.NIR[0] = 3667200;
    PDSCH.NIR[1] = 3667200;
    PDSCH.SoftChanBit[0] = 58675200;
    PDSCH.SoftChanBit[1] = 58675200;
}

static void DefaultPcfichParam_LTE(Alg_4G_PCFICHType &PCFICH)
{
    PCFICH.State = 1;
    PCFICH.Scrambling = 1;
    PCFICH.Precoding = 0;
    PCFICH.Power = 0;
    PCFICH.PDCCHSymNum = 1;
}

static void DefaultPhichParam_LTE(Alg_4G_PHICHType &PHICH)
{
    PHICH.ACKInfo = 0;
    PHICH.Power = -3.010;
}

static void DefaultChanTypeParam_LTE(Alg_4G_DLChanType Chan[ALG_4G_MAX_SUBFRAME_NUM])
{
    for (int i = 0; i < ALG_4G_MAX_SUBFRAME_NUM; ++i)
    {
        Chan[i].SubfrmIdx = i;
        DefaultPbchParam_LTE(Chan[i].PBCH);
        DefaultPdcchParam_LTE(Chan[i].PDCCH);
        DefaultPdschParam_LTE(Chan[i].PDSCH);
        DefaultPcfichParam_LTE(Chan[i].PCFICH);
        DefaultPhichParam_LTE(Chan[i].PHICH);
    }
}

static void DefaultParam_LTE(Alg_4G_ULWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_4G_ULWaveGenType));
    DefaultMultiCellParam_LTE(Param.MultiCell);
    DefaultUeParam_LTE(Param.Ue);
    Param.CchSfCfgNum = 1;
    Param.SchSfCfgNum = 1;
    DefaultChanTypeParam_LTE(Param.Chan);
}
static void DefaultParam_LTE(Alg_4G_DLWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_4G_DLWaveGenType));
    DefaultMultiCellParam_LTE(Param.MultiCell);
    DefaultUeParam_LTE(Param.Ue);
    Param.SubfrmCfgNum = 1;
    Param.OCNGFlag = 1;
    Param.DummyModulate = 2;
    for(int i = 0;i<ALG_4G_MAX_DL_FRAME_NUM;i++)
    {
        DefaultChanTypeParam_LTE(Param.Chan[i]);
    }

    for(int i = 0;i<ALG_4G_MAX_DL_FRAME_NUM;i++)
    {
        for(int j = 0;j<ALG_4G_MAX_SUBFRAME_NUM;j++)
        {
            Param.Chan[i][j].PDCCH.DCI[0].State = Param.Schedule.SchedPdsch[i][j];
        }
    }
}

static void DefaultParam_LTE(int link, Alg_3GPP_WaveGenType &Param)
{
    Param.LTE.LinkDirect = link;

    DefaultGeneralParam_LTE(Param.LTE.General);

    if (link == ALG_3GPP_UL)
    {
        DefaultParam_LTE(Param.LTE.UL);
    }
    else
    {
        DefaultParam_LTE(Param.LTE.DL);
    }
}

static void DefaultGeneralParam_NR(Alg_5G_GeneralInfo &Param)
{
    memset(&Param, 0, sizeof(Alg_5G_GeneralInfo));
    Param.Filter.Type = 2;
    Param.Filter.Fs = 122880000;
    Param.Filter.MaxOrder = 512;
    Param.Filter.FpassFactor = 0.8138;
    Param.Filter.FstopFactor = 0.85;
    Param.Filter.PassRipple = 0.05;
    Param.Filter.StopAtten = 45;
    Param.Filter.FilterMode = 1;
    Param.Filter.SmoothFactor = 1;
    Param.Filter.CutOffFrqFactor = 0.1;
}

static void DefaultParam_NR(Alg_5G_ULWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_5G_ULWaveGenType));
    Param.SlotPeriod = 5;
    Param.ULSlotNumber = 1;
    Param.CellNum = 1;

    Param.Cell[0].State = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.Cell[i].Frequency = 1.95; // 单位: GHz
        Param.Cell[i].ChannelBW = 100 * MHz_API; // 100MHz
        Param.Cell[i].DmrsTypeAPos = 2;
        Param.Cell[i].TxBW[0].SCSpacing = 15 * KHz_API;
        Param.Cell[i].TxBW[1].SCSpacing = 30 * KHz_API;
        Param.Cell[i].TxBW[2].SCSpacing = 60 * KHz_API;

        Param.Cell[i].TxBW[1].State = 1; // on

        Param.Cell[i].TxBW[0].MaxRBNumb = 270;
        Param.Cell[i].TxBW[1].MaxRBNumb = 273;
        Param.Cell[i].TxBW[2].MaxRBNumb = 135;
    }

    Param.Ue.Scramble = 1;
    Param.Ue.Initialization = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.Ue.Bwp[i].SCSpacing = 30000;
        Param.Ue.Bwp[i].RBNum = 273;
        Param.Ue.Bwp[i].Pusch.Dmrs.ConfigType = 1;
        Param.Ue.Bwp[i].Pusch.Dmrs.MaxLength = 1;
    }

    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        for (int j = 0; j < ALG_5G_MAX_SUBFRAME_NUM; j++)
        {
            Param.Subfrm[i][j].SlotCfgNum = 1;
            for (int k = 0; k < 4; k++)
            {
                Param.Subfrm[i][j].Slot[k].State = 1;
                Param.Subfrm[i][j].Slot[k].Pusch.SymbNum = 14;
                Param.Subfrm[i][j].Slot[k].Pusch.RBNum = Param.Ue.Bwp[i].RBNum;
                Param.Subfrm[i][j].Slot[k].Pusch.LayerNum = 1;
                Param.Subfrm[i][j].Slot[k].Pusch.AntennaNum = 1;
                Param.Subfrm[i][j].Slot[k].Pusch.Modulate = 2;
                Param.Subfrm[i][j].Slot[k].Pusch.CDMGrpWOData = 1;
                Param.Subfrm[i][j].Slot[k].Pusch.DmrsSymbLen = 1;
                Param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[0] = 0;
                Param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[1] = 1;
                Param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[2] = 2;
                Param.Subfrm[i][j].Slot[k].Pusch.DmrsAntPort[3] = 3;
            }
        }
    }
}

static void DefaultParam_NR(Alg_5G_DLWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_5G_DLWaveGenType));

    Param.SlotPeriod = 10;
    Param.DLSlotNumber = 1;
    Param.CellNum = 1;

    Param.Cell[0].State = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.Cell[i].Frequency = 1.95; // 单位: GHz
        Param.Cell[i].ChannelBW = 100 * MHz_API;
        Param.Cell[i].DmrsTypeAPos = 2;
        Param.Cell[i].TxBW[0].SCSpacing = 15 * KHz_API;
        Param.Cell[i].TxBW[1].SCSpacing = 30 * KHz_API;
        Param.Cell[i].TxBW[2].SCSpacing = 60 * KHz_API;

        Param.Cell[i].TxBW[1].State = 1; // on

        Param.Cell[i].TxBW[0].MaxRBNumb = 270;
        Param.Cell[i].TxBW[1].MaxRBNumb = 273;
        Param.Cell[i].TxBW[2].MaxRBNumb = 135;

        Param.Cell[i].Pbch.SCSpacing = 30000;
        Param.Cell[i].Pbch.RBOffset = 126;
        Param.Cell[i].Pbch.SCOffset = 6;
        Param.Cell[i].Pbch.PbchCase = 1;
        Param.Cell[i].Pbch.Length = 4;
        Param.Cell[i].Pbch.Position[0] = 1;
        Param.Cell[i].Pbch.BurstSetPeriod = 10;
        Param.Cell[i].Pbch.MIB.SCOffset = 1;
        Param.Cell[i].Pbch.MIB.CoresetZero = 1;
        Param.Cell[i].Pbch.MIB.SSZero = 1;
        Param.Cell[i].Pbch.MIB.CellBarre = 1;
        Param.Cell[i].Pbch.MIB.InFreqResel = 1;
    }

    Param.Ue.Scrambling = 1;
    Param.Ue.Initialization = 1;
    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.Ue.Bwp[i].SCSpacing = 30 * KHz_API;
        Param.Ue.Bwp[i].RBNum = 273;
        Param.Ue.Bwp[i].RBOffset = 0;

        Param.Ue.Bwp[i].Pdsch.ResourceAllocation = 1;
        Param.Ue.Bwp[i].Pdsch.Dmrs.ConfigType = 1;
        Param.Ue.Bwp[i].Pdsch.Dmrs.MaxLength = 2;

        Param.Ue.Bwp[i].Coreset.State = 1;
        Param.Ue.Bwp[i].Coreset.SymbNum = 1;
        Param.Ue.Bwp[i].Coreset.RBNum = 6;
    }

    for (int i = 0; i < ALG_5G_MAX_CELL_NUM; i++)
    {
        Param.SubfrmCfgNum[i] = 1;
        for (int j = 0; j < ALG_5G_MAX_SUBFRAME_NUM; j++)
        {
            Param.Subfrm[i][j].SlotCfgNum = 1;
            for (int k = 0; k < ALG_5G_MAX_SLOT_IN_SUBFRM; k++)
            {
                Param.Subfrm[i][j].Slot[k].Pdcch.State = 1;
                Param.Subfrm[i][j].Slot[k].Pdcch.UnusedCCEs = 1;
                Param.Subfrm[i][j].Slot[k].Pdcch.Initialization = 1;
                Param.Subfrm[i][j].Slot[k].Pdcch.DCIFormat = 10;
                Param.Subfrm[i][j].Slot[k].Pdcch.AggregationLevel = 1;

                Param.Subfrm[i][j].Slot[k].Pdsch.State = 1;
                Param.Subfrm[i][j].Slot[k].Pdsch.SymbNum = 13;
                Param.Subfrm[i][j].Slot[k].Pdsch.SymbOffset = 1;
                Param.Subfrm[i][j].Slot[k].Pdsch.RBNum = 273;
                Param.Subfrm[i][j].Slot[k].Pdsch.Codewords = 1;
                Param.Subfrm[i][j].Slot[k].Pdsch.LayerNum = 1;
                Param.Subfrm[i][j].Slot[k].Pdsch.AntennaNum = 1;
                Param.Subfrm[i][j].Slot[k].Pdsch.CDMGrpWOData = 1;
                Param.Subfrm[i][j].Slot[k].Pdsch.DmrsSymbLen = 1;

                for (int l = 0; l < ALG_5G_MAX_ANTENNA; l++)
                {
                    Param.Subfrm[i][j].Slot[k].Pdsch.DmrsAntPort[l] = 1000;

                }
                
                for (int l = 0; l < ARRAYSIZE(Param.Subfrm[i][j].Slot[k].Pdsch.Modulate); l++)
                {
                    Param.Subfrm[i][j].Slot[k].Pdsch.Modulate[l] = 2;
                }
            }
        }
    }
}

static void DefaultParam_NR(int link, Alg_3GPP_WaveGenType &Param)
{
    Param.NR.LinkDirect = link;

    DefaultGeneralParam_NR(Param.NR.General);

    if (link == ALG_3GPP_UL)
    {
        DefaultParam_NR(Param.NR.UL);
    }
    else
    {
        DefaultParam_NR(Param.NR.DL);
    }
}

static void DefaultGeneralParam_NB_IOT(Alg_NBIOT_GeneralInfo &Param)
{
    memset(&Param, 0, sizeof(Alg_NBIOT_GeneralInfo));
    Param.Filter.Type = 3;
    Param.Filter.Fs = 960000;
    Param.Filter.MaxOrder = 256;
    Param.Filter.FpassFactor = 0.25;
    Param.Filter.FstopFactor = 0.28;
    Param.Filter.PassRipple = 0.05;
    Param.Filter.StopAtten = 45;
    Param.Filter.RollOffFactor = 0.2;
    Param.Filter.CutOffFreqShift = 0.5;
    Param.Filter.WindowLenFactor = 0.5;
}

static void DefaultParam_NB_IOT(Alg_NBIOT_ULWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_NBIOT_ULWaveGenType));

    // cell
    Param.Cell.OperationMode = 0; // 0 Stand-alone
    Param.Cell.ChannelBW = 200000;
    Param.Cell.RBIdx = 0;
    Param.Cell.NBCellID = 0;
    Param.Cell.GrpHopping = 0;
    Param.Cell.TTCShift = 0;
    Param.Cell.STCShift = 0;
    Param.Cell.BaseSeqMode = 0;
    Param.Cell.TTBaseSeq = 0;
    Param.Cell.STBaseSeq = 0;
    Param.Cell.TWBaseSeq = 0;
    Param.Cell.DeltaSeqShift = 0;

    // UE
    Param.Ue.UeID = 0;
    Param.Ue.Scrambling = 1;
    Param.Ue.DataType = 0;
    Param.Ue.Initialization = 1;
    Param.Ue.ChanCodingState = 1;

    // Schedule
    Param.Schedule.ChanType = 0; // Npusch

    Param.Schedule.Npusch.Format = 1;
    Param.Schedule.Npusch.SCSpacing = 15000; /* 15000(15kHz), 3750(3.75kHz) */
    Param.Schedule.Npusch.StartSubfrm = 0;
    Param.Schedule.Npusch.Repetitions = 1;
    Param.Schedule.Npusch.RUs = 1;

    /* Format 1 */
    Param.Schedule.Npusch.UseIsc = 1;
    Param.Schedule.Npusch.Isc = 0; /* Subcarrier indication field */
    Param.Schedule.Npusch.SubcarrierNum = 1;
    Param.Schedule.Npusch.StartSubcarrier = 0;
    Param.Schedule.Npusch.Modulate = 1;
    Param.Schedule.Npusch.UseMcs = 1; /* 0: I_MCS; 1: I_TBS */
    Param.Schedule.Npusch.Mcs = 0;
    Param.Schedule.Npusch.TBSIdx = 0;
    Param.Schedule.Npusch.StartRVIdx = 0; /* Start slot rv_idx: 0,2*/

    /* Format 2 */
    Param.Schedule.Npusch.UseACKResField = 1;
    Param.Schedule.Npusch.ACKResField = 0;
    Param.Schedule.Npusch.SubcarrierIdx = 0;
    Param.Schedule.Npusch.HarqAckInfo = 1; /* 1, 0*/
}

static void DefaultParam_NB_IOT(Alg_NBIOT_DLWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_NBIOT_DLWaveGenType));
    // cell
    Param.Cell.ChannelBW = 200000;
    Param.Cell.Lte.PhyCellID = 0;
    Param.Cell.Lte.RARNTI = 1;
    Param.Cell.Lte.LteAntennaNum = 1;
    Param.Cell.Lte.LteREsFillState = 1;
    Param.Cell.Lte.Modulation = 2;
    Param.Cell.Lte.DataType = 0;
    Param.Cell.Lte.Initialization = 1;
    Param.Cell.NBCellID = 0;
    Param.Cell.NBAntennaNum = 1;
    Param.Cell.Anchor.OperationMode = 0;
    Param.Cell.Anchor.RBIdx = 0;
    Param.Cell.Anchor.CSSType1Rmax = 1;
    Param.Cell.Anchor.CSSType2.Rmax = 1;
    Param.Cell.Anchor.CSSType2.G = 4;
    Param.Cell.Anchor.CSSType2.Offset = 0.25;
    for (int i = 0; i < 3; i++)
    {
        Param.Cell.NonAnchor[i].Index = (i == 0) ? 1 : 0;
        Param.Cell.NonAnchor[i].State = 0;
        Param.Cell.NonAnchor[i].OperationMode = 0;
        Param.Cell.NonAnchor[i].RBIdx = 0;
    }
    // UE
    Param.Ue.UeID = 0;
    Param.Ue.UeCategory = 1;
    Param.Ue.USS.Rmax = 1;
    Param.Ue.USS.G = 4;
    Param.Ue.USS.Offset = 0.25;
    Param.Ue.DataType = 0;
    Param.Ue.Initialization = 1;
    // AhrScheduleType
    Param.AhrSchedule.Dci.User = 0;
    Param.AhrSchedule.Dci.DciFormat = 1;
    Param.AhrSchedule.Dci.SearchSpace = 0;
    Param.AhrSchedule.Dci.SearchSpace = 0;
    Param.AhrSchedule.Dci.N0.Isc = 0;
    Param.AhrSchedule.Dci.N0.Iru = 0;
    Param.AhrSchedule.Dci.N0.Idelay = 0;
    Param.AhrSchedule.Dci.N0.Imcs = 0;
    Param.AhrSchedule.Dci.N0.RedunVer = 0;
    Param.AhrSchedule.Dci.N0.Irep = 0;
    Param.AhrSchedule.Dci.N1.OrderInd = 0;
    Param.AhrSchedule.Dci.N2.PagFlg = 1;
    Param.AhrSchedule.Dci.StartSubfrm = 0;
    Param.AhrSchedule.Dci.NpdcchFormat = 0;
    Param.AhrSchedule.Dci.NcceIdx = 0;
    Param.AhrSchedule.Npbch.Precoding = 0;
    Param.AhrSchedule.Npbch.Scrambling = 0;
    Param.AhrSchedule.Npbch.ChanCodingState = 1;
    Param.AhrSchedule.Npbch.UseMIB = 1;
    Param.AhrSchedule.Npbch.MIBInfo.SFN = 0;
    Param.AhrSchedule.Npbch.MIBInfo.HyperSFN = 0;
    Param.AhrSchedule.Npbch.MIBInfo.SchedInfoSIB1 = 0;
    Param.AhrSchedule.Npbch.MIBInfo.SysInfoValueTag = 0;
    Param.AhrSchedule.Npbch.MIBInfo.ABEnabled = 1;
    memset(Param.AhrSchedule.Npbch.MIBInfo.OptModeInfo, 0, sizeof(char)*7);
    memset(Param.AhrSchedule.Npbch.MIBInfo.SpareBit, 0, sizeof(char)*11);
    Param.AhrSchedule.SIB1.Precoding = 0;
    Param.AhrSchedule.SIB1.Scrambling = 0;
    Param.AhrSchedule.SIB1.ChanCodingState = 1;
    Param.AhrSchedule.SIB1.TBSIdx = 0;
    Param.AhrSchedule.Npdcch.StartSymb = 0;
    Param.AhrSchedule.Npdcch.Scrambling = 0;
    Param.AhrSchedule.Npdcch.Precoding = 0;
    Param.AhrSchedule.Npdsch.StartSymb = 0;
    Param.AhrSchedule.Npdsch.Scrambling = 0;
    Param.AhrSchedule.Npdsch.Precoding = 0;
    Param.AhrSchedule.Npdsch.ChanCodingState = 1;

    // NonAhrScheduleType
    for (int i = 0; i < 3; i++)
    {
        Param.NonAhrSchedule[i].ConfigType = 1;
        memcpy(&Param.NonAhrSchedule[i].Dci, &Param.AhrSchedule.Dci, sizeof(Param.NonAhrSchedule[i].Dci));
        memcpy(&Param.NonAhrSchedule[i].Npdcch, &Param.AhrSchedule.Npdcch, sizeof(Param.NonAhrSchedule[i].Npdcch));
        memcpy(&Param.NonAhrSchedule[i].Npdsch, &Param.AhrSchedule.Npdsch, sizeof(Param.NonAhrSchedule[i].Npdsch));
    }
}

static void DefaultParam_NB_IOT(int link, Alg_3GPP_WaveGenType &Param)
{
    Param.NBIOT.LinkDirect = link;

    DefaultGeneralParam_NB_IOT(Param.NBIOT.General);

    if (link == ALG_3GPP_UL)
    {
        DefaultParam_NB_IOT(Param.NBIOT.UL);
        Param.NBIOT.General.Filter.Fs = 3840000;
    }
    else
    {
        DefaultParam_NB_IOT(Param.NBIOT.DL);
        Param.NBIOT.General.Filter.Fs = 30720000;
    }
}

static void DefaultParam_WCDMA(Alg_WCDMA_ULWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_WCDMA_ULWaveGenType));

    // Common Settings
    Param.ScramblingMode = 1;
    Param.DPCCH.TpcDataType = 4;

    // DPDCH
    Param.DPDCH.SymbRate = 30000;
    Param.DPDCH.DCH.Interleaver2Stat = 1;
    for (int i = 0; i < ARRAYSIZE(Param.DPDCH.DCH.DTCH); i++)
    {
        Param.DPDCH.DCH.DTCH[i].Initialization = 1;
        Param.DPDCH.DCH.DTCH[i].TTI = 10; // 未明确
        Param.DPDCH.DCH.DTCH[i].TbCount = 1;
        Param.DPDCH.DCH.DTCH[i].TbSize = 100;
        Param.DPDCH.DCH.DTCH[i].Crc = 16;
        Param.DPDCH.DCH.DTCH[i].RmAttribute = 1;
        Param.DPDCH.DCH.DTCH[i].EProtection = 3;
        Param.DPDCH.DCH.DTCH[i].InterleaverStat = 1;

        Param.DPDCH.Initialization[i] = 1; // Phy Channel Config
    }
    Param.DPDCH.DCH.DCCH.Initialization = 1;
    Param.DPDCH.DCH.DCCH.TTI = 10; // 未明确
    Param.DPDCH.DCH.DCCH.TbCount = 1;
    Param.DPDCH.DCH.DCCH.TbSize = 100;
    Param.DPDCH.DCH.DCCH.Crc = 16;
    Param.DPDCH.DCH.DCCH.RmAttribute = 1;
    Param.DPDCH.DCH.DCCH.EProtection = 3;
    Param.DPDCH.DCH.DCCH.InterleaverStat = 1;

    // HS-DPCCH
    Param.HSDPCCH.StartDelay = 101;
    Param.HSDPCCH.InterTTIDist = 5;
    memset(Param.HSDPCCH.CQIPattern, -1, sizeof(Param.HSDPCCH.CQIPattern));
}

static void DefaultParam_WCDMA(Alg_WCDMA_DLWaveGenType &Param)
{
    memset(&Param, 0, sizeof(Alg_NBIOT_DLWaveGenType));
    Param.ScramblingCode = 1;
    Param.PCPICH.State = 1;

    Param.PSCH.State = 1;
    // Param.PSCH.SymbRate = 15000;
    
    Param.PCCPCH.State = 1;
    // Param.PCCPCH.ChanCode = 1;
    Param.PCCPCH.Initialization = 1;

    Param.SSCH.State = 1;

    Param.SCCPCH.State = 1;
    Param.SCCPCH.Initialization = 1;
}

static void DefaultParam_WCDMA(int link, Alg_3GPP_WaveGenType &Param)
{
    Param.WCDMA.LinkDirect = link;
    Param.WCDMA.General.Filter.Type = 0; // 未明确
    Param.WCDMA.General.GenFrmLen = 1;

    if (link == ALG_3GPP_UL)
    {
        DefaultParam_WCDMA(Param.WCDMA.UL);
    }
    else
    {
        DefaultParam_WCDMA(Param.WCDMA.DL);
    }
}

static void DefaultParam3GPP(int demod, int link, void *Param)
{
    if (link == -1)
    {
        DefaultCommonParam3GPP(demod, *(PNSetBaseType *)(Param));
    }
    else
    {
        g_Alg3GPPSwitchCaseFlg = true;
        if (demod == ALG_3GPP_STD_4G)
        {
            DefaultParam_LTE(link, *(Alg_3GPP_WaveGenType *)(Param));
        }
        else if (demod == ALG_3GPP_STD_5G)
        {
            DefaultParam_NR(link, *(Alg_3GPP_WaveGenType *)(Param));
        }
        else if (demod == ALG_3GPP_STD_NB_IOT)
        {
            DefaultParam_NB_IOT(link, *(Alg_3GPP_WaveGenType *)(Param));
        }
        else if (demod == ALG_3GPP_STD_WCDMA)
        {
            DefaultParam_WCDMA(link, *(Alg_3GPP_WaveGenType *)(Param));
        }
    }
}

int InstrumentHandle::GetDefaultWaveParameter3GPP(int demod, int link, void *pnParameters, int Paramlen)
{
    DefaultParam3GPP(demod, link, pnParameters);

    return WT_ERR_CODE_OK;
}

#endif

// 获取3GPP默认参数 end
////////////////////////////////////////////////////////////////////////////////////

static void DefaultCommonParamWiSun(int demod, int ppdu, PNSettingBase* commonParam)
{
    commonParam->standard = demod;
    commonParam->samplingRate = static_cast<int>(MIN_SAMPLE_RATE);
    commonParam->FreqErr = 0;
    commonParam->IQImbalanceAmp = 0;
    commonParam->IQImbalancePhase = 0;
    commonParam->DCOffset_I = 0;
    commonParam->DCOffset_Q = 0;
    commonParam->Gap = 10 * Us;
    commonParam->bandwidth = 20;
    commonParam->subType = ppdu;
    commonParam->Snr = 200;
	commonParam->NSS = 1;
	commonParam->segment = 1;
}

#pragma endregion FunRegion

////////////////////////////////////////////////////////////////////////////////////
#pragma region WAVENERATOR
s32 InstrumentHandle::CreateWaveLocal(const char *fileName, void *pnParameters)
{
#ifndef LINUX
    typedef int(*Alg_GenerateWaveWifi)(GenWaveWifiStruct_API *input, Complex **outdat, int *outlen);
    typedef int(*Alg_GenerateWaveBt)(GenWaveBtStruct_API *input, Complex **outdat, int *outlen);
    typedef int(*Alg_GenerateWaveCw)(GenWaveCwStruct *input, Complex **outdat, int *outlen);

    CLoadLibrary loadDll("WLAN.Tester.Algorithm.VSG-3XX.dll");
    int iRet = WT_ERR_CODE_OK;
    Complex *m_DataPtr[MAX_NUM_OF_CHNNEL] = { 0 };
    s32 m_DataLen[MAX_NUM_OF_CHNNEL] = { 0 };
    stPNFileInfo pnInfo[MAX_NUM_OF_CHNNEL] = { 0 };

    PNSettingBase *base = static_cast<PNSettingBase *>(pnParameters);
    int demod = base->standard;
    Alg_GenerateWaveWifi GenWIFI = nullptr;
    Alg_GenerateWaveBt GenBT = nullptr;
    Alg_GenerateWaveCw GenCW = nullptr;
    do
    {
        if (!loadDll.IsLoaded())
        {
            iRet = WT_ERR_CODE_GENERAL_ERROR;
            break;
        }
        // WT_DEMOD_XXX-->IEEE_XXX
        base->standard = GetStandardType(base->standard);
        switch (demod)
        {
        case WT_DEMOD_BT:
            GenBT = (Alg_GenerateWaveBt)loadDll.GetProcAddress("Alg_GenerateWaveBt");
            iRet = GenBT(static_cast<GenWaveBtStruct_API*>(pnParameters), m_DataPtr, m_DataLen);
            break;
        case WT_DEMOD_CW:
            GenCW = (Alg_GenerateWaveCw)loadDll.GetProcAddress("Alg_GenerateWaveCw");
            iRet = GenCW(static_cast<GenWaveCwStruct*>(pnParameters), m_DataPtr, m_DataLen);
            break;
        default:
            GenWIFI = (Alg_GenerateWaveWifi)loadDll.GetProcAddress("Alg_GenerateWaveWifi");
            iRet = GenWIFI(static_cast<GenWaveWifiStruct_API*>(pnParameters), m_DataPtr, m_DataLen);
            if (WT_ERR_CODE_OK == iRet)
            {
                //save TB-MUMIMO external var
                if (demod >= WT_DEMOD_11AX_20M && demod <= WT_DEMOD_11AX_80_80M && HE_TB_PPDU == base->subType)
                {
                    //int LDPC_Extra;           --> Reserved[0]
                    //int Pre_FECfactor;        --> Reserved[1]
                    //int PE_Disambiguity;      --> Reserved[2]
                    m_TbVarParam.LDPCSym = static_cast<GenWaveWifiStruct_API*>(pnParameters)->PN11ax_TB.LDPC_Extra;
                    m_TbVarParam.AFactor = static_cast<GenWaveWifiStruct_API*>(pnParameters)->PN11ax_TB.Pre_FECfactor;
                    m_TbVarParam.PEDisamb = static_cast<GenWaveWifiStruct_API*>(pnParameters)->PN11ax_TB.PE_Disambiguity;
                    m_TbVarParam.Doppler = static_cast<GenWaveWifiStruct_API*>(pnParameters)->PN11ax_TB.Doppler;
                    m_TbVarParam.Midamble_Periodicity = static_cast<GenWaveWifiStruct_API*>(pnParameters)->PN11ax_TB.Midamble_Periodicity;
                }
            }
            break;
        }

        if (WT_ERR_CODE_OK == iRet)
        {
            bool allZeroLen = true;
            string tmpstr = ((string)fileName).c_str();
            int pos = tmpstr.find_last_of(".");

            ENTER_LOCK(PN_OPERATE_LOCK);
            if (pos != string::npos)
            {
                for (size_t i = 0; i < WT_SUB_TESTER_INDEX_MAX; i++)
                {
                    ClearPNInfor(&m_pnInfos[i]);
                }
                for (int i = 0; i < MAX_NUM_OF_CHNNEL; i++)
                {
                    if (m_DataLen[i] <= 0 || nullptr == m_DataPtr[i])
                    {
                        continue;
                    }
                    allZeroLen = false;
                    ClearPNInfor(&pnInfo[i]);

                    base->standard = demod;

                    pnInfo[i].data = (stPNDat *)m_DataPtr[i];
                    pnInfo[i].DataType = enDataFormat_Float64;
                    pnInfo[i].SampleCount = m_DataLen[i];
                    pnInfo[i].ModType = base->standard;
                    pnInfo[i].SampleFreq = base->samplingRate;


                    pnInfo[i].initializedFlag = 1;
                    pnInfo[i].sceneMode = 0;

                    pnInfo[i].freqOffset = base->FreqErr;
                    pnInfo[i].clockRate = base->ClockRate;
                    pnInfo[i].DC_Offset_I = base->DCOffset_I;
                    pnInfo[i].DC_Offset_Q = base->DCOffset_Q;
                    pnInfo[i].IQGainImb = base->IQImbalanceAmp;
                    pnInfo[i].IQPhaseImb = base->IQImbalancePhase;

                    switch (demod)
                    {
                    case WT_DEMOD_BT:
                        InitWaveGenerator_BT(static_cast<GenWaveBtStruct_API*>(pnParameters));
                        break;
                    case WT_DEMOD_CW:
                        InitWaveGenerator_CW(static_cast<GenWaveCwStruct*>(pnParameters));
                        break;
                    default:
                        InitWaveGenerator_WIFI(static_cast<GenWaveWifiStruct_API*>(pnParameters));
                        break;
                    }

                    iRet = CreatFileByPNFileInfo(&pnInfo[i], fileName, i);
                    if (iRet != WT_ERR_CODE_OK)
                    {
                        break;
                    }
                }
            }
            EXIT_LOCK(PN_OPERATE_LOCK);

            if (allZeroLen && WT_ERR_CODE_OK == iRet)
            {
                iRet = WT_ERR_CODE_GENERATE_FAIL;
                break;
            }
        }

    } while (0);

    return iRet;
#else
    return WT_ERR_CODE_OK;
#endif
}

int InstrumentHandle::GetDefaultWaveParameterBT(GenWaveBtStruct_API *pnParameters)
{
    memset(pnParameters, 0, sizeof(GenWaveBtStruct_API));
    DefaultCommonParam(WT_DEMOD_BT, 0, &pnParameters->commonParam);
    pnParameters->commonParam.samplingRate = 120 * MHz_API;

    BlueToothPacketSet *bt = &pnParameters->BtPacketSet;

    bt->LT_ADDR = 1;
    bt->PackType = 5;
    bt->LLID = 1;
    bt->mFlow = 1;
    bt->FreqDeviation = 160.0;

    bt->ModuIdex = 0.32;
    bt->BTProductIdex = 4;
    bt->RolloffIdex = 2;
    bt->GuardTime = 5;
    bt->LAP = 0xC6967E;
    bt->UAP = 0x6A;
    bt->NAP = 0;
    bt->PowerRampTime = 2.0;
    bt->PowerSettlingTime = 4.0;
    bt->PayLoadSize = 27;
    bt->LE_SyncWord = 0x94826e8e;
    bt->BLEMapperS = 2;

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetDefaultWaveParameterBTV2(GenWaveBtStructV2 *pnParameters)
{
    memset(pnParameters, 0, sizeof(GenWaveBtStructV2));
    DefaultCommonParam(WT_DEMOD_BT, 0, &pnParameters->commonParam);
    pnParameters->commonParam.samplingRate = 120 * MHz_API;
    pnParameters->commonParam.subType = 0;

    pnParameters->BrEdrSet.PackType = 0;
    pnParameters->BrEdrSet.ModuIdex = 0.32;
    pnParameters->BrEdrSet.BTProductIdex = 4;
    pnParameters->BrEdrSet.RolloffIdex = 2;
    pnParameters->BrEdrSet.GuardTime = 5;
    pnParameters->BrEdrSet.LAP = 0xC6967E;
    pnParameters->BrEdrSet.UAP = 0x6A;
    pnParameters->BrEdrSet.NAP = 0;
    pnParameters->BrEdrSet.LT_ADDR = 1;
    pnParameters->BrEdrSet.PowerRampTime = 2.0;
    pnParameters->BrEdrSet.NormalPacket.LLID = 1;
    pnParameters->BrEdrSet.NormalPacket.mFlow = 1;
    pnParameters->BrEdrSet.NormalPacket.PayLoadSize = 27;
    pnParameters->BrEdrSet.NormalPacket.payLoadType = 5;

    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetDefaultWaveParameterGLE(GenWaveGleStruct* pnParameters)
{
	memset(pnParameters, 0, sizeof(GenWaveGleStruct));
	DefaultCommonParamGLE(WT_DEMOD_GLE, 0, &pnParameters->commonParam);
	pnParameters->commonParam.samplingRate = 120 * MHz_API;
	pnParameters->commonParam.subType = 1;
	pnParameters->commonParam.bandwidth = 1;

	SparkLinkGlePacketSet* gle = &pnParameters->GlePacketSet;
	int SynSeq[32] = { 1, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0, 1 };
	gle->PhyID = 1;                                           //0~65535
	gle->SlotIndex = 0;                                       //0~2^30-1
	gle->ModIndex = 0.5;                                      //0.45~0.5
	gle->MSeqNo = 0;                                          //0~5
	gle->CrcType = 0;                                         //0~1  0:CRC24A 1:CRC32
	gle->CrcDataSeed = 0x555555;                              //CRC24A:0~0xFFFFFF    CRC32:0~0xFFFFFFFF
	gle->DataType = 0;                                        //0~7
	gle->PilotDensity = 2;                                    //0~2
	gle->BroadIndex = 1;                                      //1~3
	gle->CtrlInfoType = 6;                                    //0~11

	gle->CtrlInfoTypeA1.Mcs = 0;                              //0~10
	gle->CtrlInfoTypeA1.BroadType = 0;                        //0~3
	gle->CtrlInfoTypeA1.PacketType = 0;                       //0~7
	gle->CtrlInfoTypeA1.DataLength = 10;
	gle->Scramble = 1;
	gle->FreqBand = 0;
	gle->ChannelNo = 0;
	gle->SyncSource = 1;
	memcpy(gle->SyncSeq, SynSeq, sizeof(SynSeq));
	gle->ScheSlotLength = 0;
	gle->ScheSlotNum = 20;

	return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetDefaultWaveParameterCW(GenWaveCwStruct *pnParameters)
{
    memset(pnParameters, 0, sizeof(GenWaveCwStruct));
    DefaultCommonParam(WT_DEMOD_CW, 0, &pnParameters->commonParam);

    pnParameters->commonParam.samplingRate = MAX_SMAPLE_RATE_API / 2;
    pnParameters->commonParam.subType = Sin1MHz;
    pnParameters->psduLen = 1000;
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetDefaultWaveParameterWiSun(GenWaveWisunStruct* pnParameters)
{
    memset(pnParameters, 0, sizeof(GenWaveWisunStruct));
    DefaultCommonParamWiSun(WT_DEMOD_LRWPAN_OFDM, 1, &pnParameters->commonParam);
    
    SetWiSun_MROFDM* wisun = &pnParameters->PNWiSun_MROFDM;
    //wisun->PSDU.FrameType = 1;
    wisun->PSDU.PsduLength = 76;
    
    return WT_ERR_CODE_OK;
}

int InstrumentHandle::GetDefaultWaveParameterWifi(int demod, int ppdu, GenWaveWifiStruct_API *pnParameters)
{
    if (nullptr == pnParameters)
    {
        return WT_ERR_CODE_UNKNOW_PARAMETER;
    }
    memset(pnParameters, 0, sizeof(GenWaveWifiStruct_API));
    DefaultCommonParam(demod, ppdu, &pnParameters->commonParam);
    pnParameters->commonParam.Gap = 0;

    switch (demod)
    {
    case WT_DEMOD_11B:
        pnParameters->PN11b.DataRate = Mbps11;
        pnParameters->PN11b.Preamble = PRE_LONG_11B;
        DefaultPSDU(&pnParameters->PN11b.psdu);
        break;
    case WT_DEMOD_11AG:
        pnParameters->PN11a.DataRate = Mbps54;
        DefaultPSDU(&pnParameters->PN11a.psdu);
        break;
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        DefaultWaveParameter_11N(pnParameters);
        break;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        DefaultWaveParameter_11AC(pnParameters);
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11AX_160_160M:
        DefaultWaveParameter_11AX(pnParameters);
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        DefaultWaveParameter_11BE(pnParameters);
        break;
    default:
        break;
    }
    return WT_ERR_CODE_OK;
}

int WIFI_AX_TF_Valid(TriggerFrameSetting *TFSetting)
{
    /*0 : DL(Must DL)*/
    //pnParameters->PN11ax.UL_DL = 0;

    /*0 : Disable, 1 : Enable*/
    if (0 != TFSetting->MoreTF && 1 != TFSetting->MoreTF)
    {
        TFSetting->MoreTF = 0;
    }
    /*0 : Off, 1: ON*/
    if (0 != TFSetting->CSRequired && 1 != TFSetting->CSRequired)
    {
        TFSetting->CSRequired = 0;
    }
    /*0 : 20M, 1: 40M, 2: 80M, 3: 160M*/
    if (!(0 <= TFSetting->TBULBW && 3 >= TFSetting->TBULBW))
    {
        TFSetting->TBULBW = 0;
    }
    /*
    0 : 1xLTF + 1.6GI
    1 : 2xLTF + 1.6GI
    2: 4xLTF + 3.2GI
    */
    if (!(0 <= TFSetting->TBGILTF && 2 >= TFSetting->TBGILTF))
    {
        TFSetting->TBGILTF = 0;
    }
    /*0,1*/
    if (0 != TFSetting->TBMMMode && 1 != TFSetting->TBMMMode)
    {
        TFSetting->TBMMMode = 0;
    }
    /*
    0 : 1 HELTF
    0 : 2 HELTF
    2 : 4 HELTF
    3 : 6 HELTF
    4 : 8 HELTF
    */
    if (!(0 <= TFSetting->TBLTFSym && 4 >= TFSetting->TBLTFSym))
    {
        TFSetting->TBLTFSym = 0;
    }
    /*0 : Disable, 1: Enable*/
    if (0 != TFSetting->TBSTBC && 1 != TFSetting->TBSTBC)
    {
        TFSetting->TBSTBC = 0;
    }
    /*0 : Off, 1: ON*/
    if (0 != TFSetting->TBLDPCExtra && 1 != TFSetting->TBLDPCExtra)
    {
        TFSetting->TBLDPCExtra = 0;
    }
    /*0~60*/
    if (!(0 <= TFSetting->APTxPower && 60 >= TFSetting->APTxPower))
    {
        TFSetting->APTxPower = 0;
    }
    /*1~4*/
    if (!(1 <= TFSetting->TBAfactor && 4 >= TFSetting->TBAfactor))
    {
        TFSetting->TBAfactor = 4;
    }
    /*0 : Disable, 1: Enable*/
    if (0 != TFSetting->TBPE && 1 != TFSetting->TBPE)
    {
        TFSetting->TBPE = 0;
    }
    /*0,2,4*/
    if (0 != TFSetting->mPad && 2 != TFSetting->mPad && 4 != TFSetting->mPad)
    {
        TFSetting->mPad = 0;
    }
    /*0,1*/
    if (0 != TFSetting->TBDoppler && 1 != TFSetting->TBDoppler)
    {
        TFSetting->TBDoppler = 0;
    }

    /*1~74*/
    if (!(1 <= TFSetting->TBUserNum && 74 >= TFSetting->TBUserNum))
    {
        TFSetting->TBUserNum = 1;
    }

    for (int i = 0; i < AX_USER_COUNT; i++)
    {
        /*1~2007*/
        if (!(1 <= TFSetting->TBAID[i] && 2007 >= TFSetting->TBAID[0]))
        {
            TFSetting->TBAID[0] = 0;//AID
        }
        /*0~68*/
        if (!(0 <= TFSetting->TBRUIndex[i] && 68 >= TFSetting->TBRUIndex[i]))
        {
            TFSetting->TBRUIndex[i] = 0;//RU INDEX
        }
        /*0~1*/
        if (0 != TFSetting->TBSegment[i] && 1 != TFSetting->TBSegment[i])
        {
            TFSetting->TBSegment[i] = 0;//SEGMENT
        }
        /*0=BCC,1=LDPC*/
        if (0 != TFSetting->TBCoding[i] && 1 != TFSetting->TBCoding[i])
        {
            TFSetting->TBCoding[i] = 1;//LDPC
        }
        /*0~11*/
        if (!(0 <= TFSetting->TBMCS[i] && 11 >= TFSetting->TBMCS[i]))
        {
            TFSetting->TBMCS[i] = 7;//MCS
        }
        /*0~1*/
        if (0 != TFSetting->TBDCM[i] && 1 != TFSetting->TBDCM[i])
        {
            TFSetting->TBDCM[i] = 0;//DCM
        }
        /*1~8*/
        if (!(1 <= TFSetting->TBSSStart[i] && 8 >= TFSetting->TBSSStart[i]))
        {
            TFSetting->TBSSStart[i] = 1;//BSSStart
        }
        /*1~8*/
        if (!(1 <= TFSetting->TBSSCount[i] && 8 >= TFSetting->TBSSCount[i]))
        {
            TFSetting->TBSSCount[i] = 1;//BSSCount
        }
        /*0~127*/
        if (!(0 <= TFSetting->TBRSSI[i] && 127 >= TFSetting->TBRSSI[i]))
        {
            TFSetting->TBRSSI[i] = 127;//RSSI
        }
        /*0~3*/
        if (!(0 <= TFSetting->TBSpacingFactor[i] && 3 >= TFSetting->TBSpacingFactor[i]))
        {
            TFSetting->TBSpacingFactor[i] = 0;//SpacingFactor
        }
        /*0~7*/
        if (!(0 <= TFSetting->TBAggLimit[i] && 7 >= TFSetting->TBAggLimit[i]))
        {
            TFSetting->TBAggLimit[i] = 0;//AggLimit
        }
        /*0~3*/
        if (!(0 <= TFSetting->TBPreAC[i] && 3 >= TFSetting->TBPreAC[i]))
        {
            TFSetting->TBPreAC[i] = 0;//PreAC
        }
        /*0~1*/
        if (!(0 <= TFSetting->TBMultiplexing[i] && 1 >= TFSetting->TBMultiplexing[i]))
        {
            TFSetting->TBMultiplexing[i] = 0;//Multiplexing
        }
        /*0~255*/
        if (!(0 <= TFSetting->TBRxTxMap[i] && 255 >= TFSetting->TBRxTxMap[i]))
        {
            TFSetting->TBRxTxMap[i] = 0;//Feedback BitMap
        }
    }

    for (int i = 0; i < ARRAYSIZE(TFSetting->TBBARControl); i++)
    {
        /*0~F*/
        if (!(0 <= TFSetting->TBBARControl[i] && 0x0F >= TFSetting->TBBARControl[i]))
        {
            TFSetting->TBBARControl[i] = 0;
        }
        if (!(0 <= TFSetting->TBBARInfo[i] && 0x0F >= TFSetting->TBBARInfo[i]))
        {
            TFSetting->TBBARInfo[i] = 0;
        }
    }

    /*2~34*/
    if (!(2 <= TFSetting->TBBARInfoLen && 34 >= TFSetting->TBBARInfoLen))
    {
        TFSetting->TBBARInfoLen = 2;
    }
    return WT_ERR_CODE_OK;
}

s32 InstrumentHandle::isTriggerFrame(GenWaveWifiStruct_API *pnParameters)
{
    int iRet = WT_ERR_CODE_GENERAL_ERROR;
    int demod = pnParameters->commonParam.standard;
    int ppdu = pnParameters->commonParam.subType;
    WIFI_PSDU *psdu = nullptr;
    switch (demod)
    {
    case WT_DEMOD_11AG:
        psdu = &pnParameters->PN11a.psdu;
        break;
    case WT_DEMOD_11B:
        psdu = &pnParameters->PN11b.psdu;
        break;
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11N_40M:
        psdu = &pnParameters->PN11n.psdu;
        break;
    case WT_DEMOD_11AC_20M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AC_80_80M:
        psdu = &pnParameters->PN11ac.psdu;
        break;
    case WT_DEMOD_11AX_20M:
    case WT_DEMOD_11AX_40M:
    case WT_DEMOD_11AX_80M:
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AX_80_80M:
        if (HE_SU_PPDU == ppdu || HE_EXTEND_PPDU == ppdu)
        {
            psdu = &pnParameters->PN11ax_SU.psdu;
        }
        else if (HE_MU_PPDU == ppdu)
        {
            for (int i = 0; i < AX_RU_COUNT; i++)
            {
                if (pnParameters->PN11ax_MU.RU[i].UserNum > 0)
                {
                    psdu = &pnParameters->PN11ax_MU.RU[i].User[0].psdu;
                    break;
                }
            }
        }
        break;
    case WT_DEMOD_11BE_20M:
    case WT_DEMOD_11BE_40M:
    case WT_DEMOD_11BE_80M:
    case WT_DEMOD_11BE_160M:
    case WT_DEMOD_11BE_80_80M:
    case WT_DEMOD_11BE_320M:
    case WT_DEMOD_11BE_160_160M:
        if (EHT_MU_PPDU == ppdu && 1 == pnParameters->PN11be_MU.FullBand)
        {
            for (int i = 0; i < BE_RU_COUNT; i++)
            {
                if (pnParameters->PN11be_MU.RU[i].UserNum > 0)
                {
                    psdu = &pnParameters->PN11be_MU.RU[i].User[0].psdu;
                    break;
                }
            }
        }
        break;
    default:
        break;
    }

    if (psdu && PSDUType_TriggerFrame == psdu->psduType)
    {
        iRet = WT_ERR_CODE_OK;
    }

    return iRet;
}
#pragma endregion WAVENERATOR

#pragma region PERTOOL

static int GetDataRate(PerMacParameter *MacParameter, GenWaveWifiStruct_API *pnParameters)
{
    int Demode = MacParameter->Demode;
    int datarate = MacParameter->DataRate;
    switch (Demode)
    {
    case WT_DEMOD_11AG:
    case WT_DEMOD_11B:
    case WT_DEMOD_11N_20M:
    case WT_DEMOD_11AX_20M:
        pnParameters->commonParam.bandwidth = 20;
        break;
    case WT_DEMOD_11N_40M:
    case WT_DEMOD_11AC_40M:
    case WT_DEMOD_11AX_40M:
        pnParameters->commonParam.bandwidth = 40;
        break;
    case WT_DEMOD_11AC_80M:
    case WT_DEMOD_11AX_80M:
        pnParameters->commonParam.bandwidth = 80;
        break;
    case WT_DEMOD_11AX_160M:
    case WT_DEMOD_11AC_160M:
    case WT_DEMOD_11AX_80_80M:
    case WT_DEMOD_11AC_80_80M:
        pnParameters->commonParam.bandwidth = 160;
        break;
    case WT_DEMOD_11AX_160_160M:
        pnParameters->commonParam.bandwidth = 320;
        break;
    default:
        pnParameters->commonParam.bandwidth = 20;
        break;
    }

#define AX_NSS_20(datarate, nss)   \
	if(NSS##nss##_MCS0 >= datarate && datarate >= NSS##nss##_MCS11) \
	{   \
	pnParameters->PN11ax_SU.NSS = nss;\
	pnParameters->PN11ax_SU.MCS = NSS##nss##_MCS0 - datarate;\
	}

#define AX_NSS(datarate, bandwidth, nss)   \
	if(NSS##nss##_MCS0_##bandwidth >= datarate && datarate >= NSS##nss##_MCS11_##bandwidth) \
	{   \
	pnParameters->PN11ax_SU.NSS = nss;\
	pnParameters->PN11ax_SU.MCS = NSS##nss##_MCS0_##bandwidth - datarate;\
	}

#define AC_NSS_20(datarate, nss)   \
	if(NSS##nss##_MCS0 >= datarate && datarate >= NSS##nss##_MCS11) \
	{   \
	pnParameters->PN11ac.MCS = NSS##nss##_MCS0 - datarate;\
	pnParameters->PN11ac.NSS = nss;\
	}

#define AC_NSS(datarate, bandwidth, nss)   \
	if(NSS##nss##_MCS0_##bandwidth >= datarate && datarate >= NSS##nss##_MCS11_##bandwidth) \
	{   \
	pnParameters->PN11ac.MCS = NSS##nss##_MCS0_##bandwidth - datarate;\
	pnParameters->PN11ac.NSS = nss;\
	}

    if (WT_DEMOD_11N_20M == Demode || WT_DEMOD_11N_40M == Demode)
    {
        int maxIndex = (Demode == WT_DEMOD_11N_20M ? MCS0 : MCS0_40);
        pnParameters->PN11n.MCS = maxIndex - datarate;
        pnParameters->PN11n.NSS = (pnParameters->PN11n.MCS / 8) + 1;
    }
    else if (WT_DEMOD_11AC_20M == Demode || WT_DEMOD_11AX_20M == Demode)
    {
        AC_NSS_20(datarate, 1);
        AC_NSS_20(datarate, 2);
        AC_NSS_20(datarate, 3);
        AC_NSS_20(datarate, 4);
        AC_NSS_20(datarate, 5);
        AC_NSS_20(datarate, 6);
        AC_NSS_20(datarate, 7);
        AC_NSS_20(datarate, 8);
    }
    else if (WT_DEMOD_11AC_40M == Demode)
    {
        AC_NSS(datarate, 40, 1);
        AC_NSS(datarate, 40, 2);
        AC_NSS(datarate, 40, 3);
        AC_NSS(datarate, 40, 4);
        AC_NSS(datarate, 40, 5);
        AC_NSS(datarate, 40, 6);
        AC_NSS(datarate, 40, 7);
        AC_NSS(datarate, 40, 8);
    }
    else if (WT_DEMOD_11AC_80M == Demode)
    {
        AC_NSS(datarate, 80, 1);
        AC_NSS(datarate, 80, 2);
        AC_NSS(datarate, 80, 3);
        AC_NSS(datarate, 80, 4);
        AC_NSS(datarate, 80, 5);
        AC_NSS(datarate, 80, 6);
        AC_NSS(datarate, 80, 7);
        AC_NSS(datarate, 80, 8);
    }
    else if (WT_DEMOD_11AC_160M == Demode)
    {
        AC_NSS(datarate, 160, 1);
        AC_NSS(datarate, 160, 2);
        AC_NSS(datarate, 160, 3);
        AC_NSS(datarate, 160, 4);
        AC_NSS(datarate, 160, 5);
        AC_NSS(datarate, 160, 6);
        AC_NSS(datarate, 160, 7);
        AC_NSS(datarate, 160, 8);
    }
    else if (WT_DEMOD_11AX_20M == Demode)
    {
        AX_NSS_20(datarate, 1);
        AX_NSS_20(datarate, 2);
        AX_NSS_20(datarate, 3);
        AX_NSS_20(datarate, 4);
        AX_NSS_20(datarate, 5);
        AX_NSS_20(datarate, 6);
        AX_NSS_20(datarate, 7);
        AX_NSS_20(datarate, 8);
    }
    else if (WT_DEMOD_11AX_40M == Demode)
    {
        AX_NSS(datarate, 40, 1);
        AX_NSS(datarate, 40, 2);
        AX_NSS(datarate, 40, 3);
        AX_NSS(datarate, 40, 4);
        AX_NSS(datarate, 40, 5);
        AX_NSS(datarate, 40, 6);
        AX_NSS(datarate, 40, 7);
        AX_NSS(datarate, 40, 8);

    }
    else if (WT_DEMOD_11AX_80M == Demode)
    {
        AX_NSS(datarate, 80, 1);
        AX_NSS(datarate, 80, 2);
        AX_NSS(datarate, 80, 3);
        AX_NSS(datarate, 80, 4);
        AX_NSS(datarate, 80, 5);
        AX_NSS(datarate, 80, 6);
        AX_NSS(datarate, 80, 7);
        AX_NSS(datarate, 80, 8);
    }
    else if (WT_DEMOD_11AX_160M == Demode)
    {
        AX_NSS(datarate, 160, 1);
        AX_NSS(datarate, 160, 2);
        AX_NSS(datarate, 160, 3);
        AX_NSS(datarate, 160, 4);
        AX_NSS(datarate, 160, 5);
        AX_NSS(datarate, 160, 6);
        AX_NSS(datarate, 160, 7);
        AX_NSS(datarate, 160, 8);
    }
    else if (WT_DEMOD_11AG == Demode)
    {
        pnParameters->PN11a.DataRate = datarate;
    }
    else if (WT_DEMOD_11B == Demode)
    {
        pnParameters->PN11b.DataRate = datarate;
    }
    return 0;
}

int InstrumentHandle::GenSensetivityWave(PerMacParameter *MacParameter, char *WaveName)
{
    const char *local_Mac = "112233445566";
    const char *frameUser = "0800";
    int iRet = WT_ERR_CODE_OK;
    unique_ptr<GenWaveWifiStruct_API>pnSetting(new (std::nothrow) GenWaveWifiStruct_API);
    GenWaveWifiStruct_API *pnParameters = pnSetting.get();
    memset(pnParameters, 0, sizeof(pnParameters));
    pnParameters->commonParam.standard = MacParameter->Demode;
    if (WT_DEMOD_11B == MacParameter->Demode)
    {
        pnParameters->commonParam.samplingRate = MAX_SMAPLE_RATE_API / 2;  //
    }
    else
    {
        pnParameters->commonParam.samplingRate = MAX_SMAPLE_RATE_API;
    }
    GetDefaultWaveParameterWifi(MacParameter->Demode, 0, pnParameters);
    GetDataRate(MacParameter, pnParameters);

    do
    {
        //Only support standard <= VHT 80
        if (!(MacParameter->Demode >= WT_DEMOD_11AG && MacParameter->Demode <= WT_DEMOD_11AC_80M))
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        WIFI_PSDU *psdu = nullptr;
        switch (MacParameter->Demode)
        {
        case WT_DEMOD_11AG:
            psdu = &pnParameters->PN11a.psdu;
            break;
        case WT_DEMOD_11B:
            psdu = &pnParameters->PN11b.psdu;
            break;
        case WT_DEMOD_11N_20M:
        case WT_DEMOD_11N_40M:
            psdu = &pnParameters->PN11n.psdu;
            break;
        case WT_DEMOD_11AC_20M:
        case WT_DEMOD_11AC_40M:
        case  WT_DEMOD_11AC_80M:
            psdu = &pnParameters->PN11ac.psdu;
            break;
        default:
            break;
        }


        psdu->CRCCheckEnable = 1;
        psdu->scrambler = 12;
        //IBSS֡ framecontrol = 0800,MAC1=DA=DUT MA, MAC2=SA=local MAC, MAC3=BSSID=DUT MAC
        psdu->MacHeaderEnable = 1;
        HexString2Bytes((u8*)frameUser, (u8*)psdu->FrameControl, sizeof(psdu->FrameControl));
        HexString2Bytes((u8*)MacParameter->MACAddr, (u8*)psdu->MacAddress1, sizeof(psdu->MacAddress1));
        HexString2Bytes((u8*)local_Mac, (u8*)psdu->MacAddress2, sizeof(psdu->MacAddress2));
        HexString2Bytes((u8*)MacParameter->MACAddr, (u8*)psdu->MacAddress3, sizeof(psdu->MacAddress3));

        psdu->psduLen = MacParameter->DataLength;
        psdu->psduType = PSDUType_ALL0;

        iRet = WaveGeneratorWiFi(WaveName, pnParameters, NULL);
    } while (0);

    return iRet;
}

static void SetWaveAlzParamWifi(AlzParamWifi *analyzeParamWifi, int demod)
{
    analyzeParamWifi->AutoDetect = WT_BW_AUTO_DETECT;
    analyzeParamWifi->Method11b = WT_11B_STANDARD_TX_ACC;
    analyzeParamWifi->DCRemoval = WT_DC_REMOVAL_OFF;
    analyzeParamWifi->EqTaps = WT_DC_REMOVAL_OFF;
    analyzeParamWifi->PhsCorrMode11B = WT_PH_CORR_11b_ON;

    analyzeParamWifi->PhsCorrMode = WT_PH_CORR_SYM_BY_SYM;
    analyzeParamWifi->ChEstimate = WT_CH_EST_RAW;
    analyzeParamWifi->SynTimeCorr = WT_SYM_TIM_ON;
    analyzeParamWifi->FreqSyncMode = WT_FREQ_SYNC_AUTO;
    analyzeParamWifi->AmplTrack = WT_AMPL_TRACK_OFF;

    analyzeParamWifi->OfdmDemodOn = 1;
    analyzeParamWifi->MIMOAnalysisMode = 0;
    analyzeParamWifi->MimoMaxPowerDiff = 30;

    if (WT_DEMOD_11B == demod)
    {
        analyzeParamWifi->Demode = demod;
        analyzeParamWifi->AutoDetect = WT_USER_DEFINED;
    }
    else
    {
        analyzeParamWifi->Demode = WT_DEMOD_11AC_80M;
        analyzeParamWifi->AutoDetect = WT_AUTO_DETECT;
    }
}

int InstrumentHandle::GetSensetivityResult(int demod, int *AckCnt)
{
    A_ASSERT(AckCnt);
    int err = WT_ERR_CODE_OK;

    do
    {
        *AckCnt = 0;
        AlzParamWifi tmp_AnalyzeParamWifi;
        SetWaveAlzParamWifi(&tmp_AnalyzeParamWifi, demod);
        err = SetAnalyzeParam(WT_ALZ_PARAM_WIFI, (void *)&tmp_AnalyzeParamWifi, sizeof(tmp_AnalyzeParamWifi));
        if (err)
        {
            break;
        }
        err = Analyze(0, nullptr);
        if (err)
        {
            break;
        }

        if (1)
        {
            int signalType = 0;
            double result = 0.0;
            int psdulen = 0;
            err = GetResult(WT_RES_DEMODE, &result, 0, 0);
            if (err)
            {
                break;
            }
            signalType = static_cast<int>(result);

            if (WT_DEMOD_11AG == signalType)
            {
                DataInfo11ag tmp11ag;
                err = GetVectorResult(WT_RES_DATA_INFO, &tmp11ag, sizeof(tmp11ag), 0, 0);
                if (err)
                {
                    break;
                }
                psdulen = tmp11ag.PsduLen;
            }
            else if (WT_DEMOD_11B == signalType)
            {
                DataInfo11b tmp11b;
                err = GetVectorResult(WT_RES_DATA_INFO, &tmp11b, sizeof(tmp11b), 0, 0);
                if (err)
                {
                    break;
                }
                psdulen = tmp11b.PsduLen;
            }
            if (14 == psdulen)
            {
                *AckCnt = 1;
            }
        }
        else
        {
            ExchangeBuff pstRecvBuff;
            pstRecvBuff.chpHead = (char *)AckCnt;
            pstRecvBuff.buff_len = sizeof(int);
            pstRecvBuff.data_len = sizeof(int);
            err = Exchange(0, CMD_PER_ANALYZE, nullptr, 0, &pstRecvBuff, 1, IOCONTROL_VSA, 100, 100);
        }
    } while (0);

    return err;
}
#ifndef LINUX
static unsigned int WINAPI VsaThreadProFunc(void *pParam)
#else
static int VsaThreadProFunc(void *pParam)
#endif
{
    int iRet = WT_ERR_CODE_OK;
    stPerThreadParam *pThreadParam = static_cast<stPerThreadParam *>(pParam);
    InstrumentHandle *pInstrument = pThreadParam->pInstrument;
    int Demod = pThreadParam->pVsaParameter->Demode;
    if (pInstrument)
    {
        do
        {
            EXIT_LOCK(pThreadParam->VsaIsRuning);
            //timeval begin, finish;
            //UsualKit::gettimeofday(&begin);
            iRet = pInstrument->DataCapture();
            //UsualKit::gettimeofday(&finish);
            //cout << "DataCapture" << " use time=" << UsualKit::elapsed_time(&begin, &finish) << " ms" << endl;
            if (iRet)
            {
                break;
            }

            int Ack = 0;
            iRet = pInstrument->GetSensetivityResult(Demod, &Ack);
            pThreadParam->Ack = Ack;

        } while (0);
    }
    return iRet;
}

int InstrumentHandle::StopPerTesting()
{
    int tryMax = 50;
    if (m_PerStataus == PERSTATUS_WORKING)
    {
        m_PerStataus = PERSTATUS_CANCLEING;
        while (m_PerStataus != PERSTATUS_IDLE && tryMax--)
        {
            Sleep(200);
        }
    }
    return WT_ERR_CODE_OK;
}
#ifndef LINUX
int InstrumentHandle::PerDataCapture(PerActionParameter *perParameter, VsaParameter *vsaParam)
{
    int tryCnt = 0;
    stPerThreadParam tmpThreadParam;
    memset(&tmpThreadParam, 0, sizeof(tmpThreadParam));
    tmpThreadParam.pInstrument = this;
    tmpThreadParam.pVsaParameter = vsaParam;

    while (m_PerStataus == PERSTATUS_WORKING)
    {
        //timeval begin, finish;
        //UsualKit::gettimeofday(&begin);

        ENTER_LOCK(tmpThreadParam.VsaIsRuning);
        tmpThreadParam.Ack = 0;

        HANDLE vsaHandle = (HANDLE)_beginthreadex(NULL, NULL, VsaThreadProFunc, &tmpThreadParam, 0, nullptr);
        ENTER_LOCK(tmpThreadParam.VsaIsRuning);
        StartVSG();

        if (vsaHandle != INVALID_HANDLE_VALUE)
        {
            WaitForSingleObject(vsaHandle, INFINITE);
            CloseHandle(vsaHandle);
        }
        EXIT_LOCK(tmpThreadParam.VsaIsRuning);

        //UsualKit::gettimeofday(&finish);
        //cout << __FUNCTION__ << " use time=" << UsualKit::elapsed_time(&begin, &finish) << " ms" << endl;
        if (perParameter->BroadcastEnable && tmpThreadParam.Ack != 1)
        {
            if (tryCnt++ < 3)
            {
                continue;
            }
        }
        break;
    }
    return (tmpThreadParam.Ack == 1 ? WT_ERR_CODE_OK : WT_ERR_CODE_OK + 1);
}
#else
int InstrumentHandle::PerDataCapture(PerActionParameter *perParameter, VsaParameter *vsaParam)
{
    int tryCnt = 0;
    stPerThreadParam tmpThreadParam;
    memset(&tmpThreadParam, 0, sizeof(tmpThreadParam));
    tmpThreadParam.pInstrument = this;
    tmpThreadParam.pVsaParameter = vsaParam;

    while (m_PerStataus == PERSTATUS_WORKING)
    {
        //timeval begin, finish;
        //UsualKit::gettimeofday(&begin);

        ENTER_LOCK(tmpThreadParam.VsaIsRuning);
        tmpThreadParam.Ack = 0;

        future<int> vsaHandle = std::async(std::launch::async, VsaThreadProFunc, &tmpThreadParam);

        ENTER_LOCK(tmpThreadParam.VsaIsRuning);
        StartVSG();

        if (vsaHandle.valid())
        {
            if (vsaHandle.wait_for(chrono::seconds(0)) != future_status::deferred)
            {
                vsaHandle.get();
            }
        }
        EXIT_LOCK(tmpThreadParam.VsaIsRuning);

        //UsualKit::gettimeofday(&finish);
        //cout << __FUNCTION__ << " use time=" << UsualKit::elapsed_time(&begin, &finish) << " ms" << endl;
        if (perParameter->BroadcastEnable && tmpThreadParam.Ack != 1)
        {
            if (tryCnt++ < 3)
            {
                continue;
            }
        }
        break;
    }
    return (tmpThreadParam.Ack == 1 ? WT_ERR_CODE_OK : WT_ERR_CODE_OK + 1);
}
#endif
int InstrumentHandle::PerFindRefPower(VsaParameter *vsaParam, VsgPattern *vsgPattern, VsgParameter *vsgParam)
{
    VsaParameter tmpVsaParameter;
    VsgPattern tmpVsgPattern;

    memcpy(&tmpVsgPattern, vsgPattern, sizeof(tmpVsgPattern));
    memcpy(&tmpVsaParameter, vsaParam, sizeof(tmpVsaParameter));

    tmpVsgPattern.Repeat = 0;

    int iRet = WT_ERR_CODE_OK;
    int vsgStatu = WT_VSG_VSA_STATE_TIMEOUT;
    do
    {
        iRet = SetVSGPattern(&tmpVsgPattern, 1, nullptr, 1);
        if (iRet)
        {
            break;
        }
        iRet = SetVSGParam(vsgParam, nullptr);
        if (iRet)
        {
            break;
        }

        iRet = AsynStartVSG();
        if (iRet)
        {
            break;
        }
        while (true)
        {
            iRet = GetCurrVSGStatu(&vsgStatu);
            if (WT_ERR_CODE_OK != iRet)
            {
                break;
            }

            if (WT_VSG_VSA_STATE_DONE == vsgStatu)
            {
                iRet = WT_ERR_CODE_OK;
                break;
            }
            else if (WT_VSG_VSA_STATE_ERR_DONE == vsgStatu)
            {
                iRet = WT_ERR_CODE_VSG_ERR;
                break;
            }
            else if (WT_VSG_VSA_STATE_RUNNING == vsgStatu)
            {
                break;
            }
            else
            {
                iRet = WT_ERR_CODE_GENERAL_ERROR;
                break;
            }
        }
        if (WT_ERR_CODE_OK == iRet && WT_VSG_VSA_STATE_RUNNING == vsgStatu)
        {
            iRet = SetVSAParamAutorange(&tmpVsaParameter, nullptr);
            if (iRet)
            {
                break;
            }
            if (WT_DEMOD_11B == vsaParam->Demode)
            {
                vsaParam->MaxPower[0] = tmpVsaParameter.MaxPower[0] + RefPowerOffset;
            }
            else
            {
                vsaParam->MaxPower[0] = tmpVsaParameter.MaxPower[0];
            }
            //cout << "AutoRange Max Power=" << vsaParam->MaxPower[0] << " dBm" << endl;
        }

        StopVSG();
        iRet = SetVSGPattern(vsgPattern, 1, nullptr, 1);
        if (iRet)
        {
            break;
        }

    } while (0);

    return iRet;
}

int InstrumentHandle::StartPerTesting(PerMacParameter *MacParameter, PerActionParameter *perParameter, void(*pfunCallback)(PerResultParameter progress))
{
    const char *dirName = "./";
    const char *waveName = "PerTesting.bwv";
    int iRet = WT_ERR_CODE_OK;
    PerResultParameter progress;
    VsgWaveParameter tmpVsgWaveParameter;
    VsgParameter tmpVsgParameter;
    VsaParameter tmpVsaParameter;
    VsgPattern tmpVsgPattern;
    GetDefaultParameter(&tmpVsaParameter, nullptr, &tmpVsgParameter, &tmpVsgWaveParameter, &tmpVsgPattern);
    do
    {
        memset(&progress, 0, sizeof(progress));
        string fileNmae = string(dirName) + string(waveName);
        iRet = GenSensetivityWave(MacParameter, (char *)fileNmae.c_str());
        if (iRet)
        {
            break;
        }

        strcpy(tmpVsgWaveParameter.WaveName, fileNmae.c_str());
        strcpy(tmpVsgWaveParameter.SaveWaveName, waveName);
        iRet = SetVSGWaveParam(&tmpVsgWaveParameter);
        if (iRet)
        {
            break;
        }


        tmpVsgPattern.Repeat = 1;
        tmpVsgPattern.Wave_gap = 0.0;
        strcpy(tmpVsgPattern.WaveName, waveName);
        iRet = SetVSGPattern(&tmpVsgPattern, 1, nullptr, 1);
        if (iRet)
        {
            break;
        }

        tmpVsgParameter.Freq = perParameter->Freq;
        if (WT_DEMOD_11B == MacParameter->Demode)
        {
            tmpVsgParameter.SamplingFreq = MAX_SMAPLE_RATE_API / 2; //11B 120M
        }
        else
        {
            tmpVsgParameter.SamplingFreq = MAX_SMAPLE_RATE_API;
        }
        tmpVsgParameter.ExtPathLoss[0] = perParameter->VsgAtten;
        tmpVsgParameter.Power[0] = perParameter->FixedPower;
        tmpVsgParameter.RfPort[0] = WT_PORT_RF2;
        iRet = SetVSGParam(&tmpVsgParameter, nullptr);
        if (iRet)
        {
            break;
        }

        if (WT_DEMOD_11B == MacParameter->Demode)
        {
            tmpVsaParameter.Demode = WT_DEMOD_11B;
            tmpVsaParameter.SmpTime = 500 * Us;
        }
        else
        {
            tmpVsaParameter.Demode = WT_DEMOD_11AC_80M;
            tmpVsaParameter.SmpTime = 50 * Us;
        }
        tmpVsaParameter.Freq = perParameter->Freq;
        tmpVsaParameter.SamplingFreq = MAX_SMAPLE_RATE_API / 2;//120M
        tmpVsaParameter.ExtPathLoss[0] = perParameter->VsaAtten;
        tmpVsaParameter.RfPort[0] = WT_PORT_RF1;
        tmpVsaParameter.TrigType = WT_TRIG_TYPE_IF_API;
        tmpVsaParameter.TrigPretime = 10 * Us;
        tmpVsaParameter.TrigTimeout = 100 * Ms;
        tmpVsaParameter.MaxIFGGap = 200 * Ms;
        tmpVsaParameter.MaxPower[0] = perParameter->DutTargetPower + RefPowerOffset;
        if (perParameter->AutoRange)
        {
            iRet = PerFindRefPower(&tmpVsaParameter, &tmpVsgPattern, &tmpVsgParameter);
        }
        iRet = SetVSAParam(&tmpVsaParameter, nullptr);
        if (iRet)
        {
            break;
        }
        m_PerStataus = PERSTATUS_WORKING;

        int sendTimes = 1;
        if (perParameter->isFixedPowerLevel)
        {
            perParameter->StartLevel = perParameter->FixedPower;
            perParameter->StopLevel = perParameter->FixedPower;
            perParameter->LevelStep = 0;
            sendTimes = 1;
        }
        else
        {
            sendTimes = (s32)((perParameter->StartLevel - perParameter->StopLevel) / perParameter->LevelStep) + 2;
        }
        int beyondRange = 0;
        double lastPowerLevel = 0;
        for (int j = 0; j < sendTimes && (PERSTATUS_WORKING == m_PerStataus); j++)
        {
            const double perfectNum = 100.0;
            const double dEpsilon = 0.0001;
            if (progress.PacketsSent != 0)
            {
                if (perParameter->Threshold > perfectNum)
                {
                    iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
                    break;
                }
                double Threshold = perfectNum - perParameter->Threshold;
                if (Threshold > dEpsilon)
                {
                    double DetalThreshold = perfectNum * (progress.AcksReceived * 1.0 / progress.PacketsSent);
                    DetalThreshold -= Threshold;
                    if (DetalThreshold <= dEpsilon)
                    {
                        iRet = WT_ERR_CODE_OK;
                        break;
                    }
                }
            }
            progress.AcksReceived = 0;
            progress.PacketsSent = 0;
            progress.PowerLevel = perParameter->StartLevel - j * perParameter->LevelStep;
            progress.TotalPackets = perParameter->packetCount;
            if (progress.PowerLevel < perParameter->StopLevel)
            {
                if (beyondRange == 1)
                {
                    break;
                }
                progress.PowerLevel = perParameter->StopLevel;
                if (progress.PowerLevel == lastPowerLevel)
                {
                    break;
                }
                beyondRange = 1;
            }
            lastPowerLevel = progress.PowerLevel;

            if (NULL != pfunCallback)
            {
                pfunCallback(progress);
            }
            tmpVsgParameter.Power[0] = progress.PowerLevel;
            tmpVsgParameter.ExtPathLoss[0] = perParameter->VsgAtten;

            iRet = SetVSGParam(&tmpVsgParameter, nullptr);
            if (iRet)
            {
                break;
            }
            for (int loopCnt = 0; loopCnt < perParameter->packetCount && (PERSTATUS_WORKING == m_PerStataus); loopCnt++)
            {
                iRet = PerDataCapture(perParameter, &tmpVsaParameter);
                switch (iRet)
                {
                case WT_ERR_CODE_OK:
                    progress.PacketsSent++;
                    progress.AcksReceived++;
                    break;
                default:
                    progress.PacketsSent++;
                    break;
                }
                if (NULL != pfunCallback)
                {
                    pfunCallback(progress);
                }

                if (loopCnt == perParameter->packetCount - 1)
                {
                    iRet = WT_ERR_CODE_OK;
                }
            }
        }

    } while (0);

    if (PERSTATUS_CANCLEING == m_PerStataus)
    {
        iRet = WT_ERR_CODE_OK;
    }
    m_PerStataus = PERSTATUS_IDLE;
    return iRet;
}

#pragma endregion PERTOOL
