//*****************************************************************************
//  File: scpi_socket.cpp
//  SocketSrv部分
//  Data: 2019.03.10
//*****************************************************************************
#include <new>
#include <cstring>
#include <iostream>
#include <unistd.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <errno.h>
#include <sys/time.h>
#include <math.h>
#include <assert.h>

#include "socket.h"
#include "wterror.h"
#include "wtlog.h"
#include "scpiapiwrapper/wrapper.h"
#include "scpi_service.h"
#include "threadpool.h"
#include "scpi_socket.h"
#include "scpi_conninfo.h"
#include "wtsecurelib.h"

using namespace std;
using namespace std::placeholders;

#define WT_CMD_FILE_NAME           "wtcmd.json"

std::list<WTScpiSocket *> WTScpiSocket::m_Socket;

WTScpiSocket::WTScpiSocket(const wtev::loop_ref &loop, int MaxUserNum)
    : m_AcceptIO(loop), m_Loop(loop), m_UserNum(MaxUserNum)
{
    m_Socket.push_back(this);
}

WTScpiSocket::WTScpiSocket(const wtev::loop_ref &loop, int MaxUserNum, bool is_vxi)
    : m_AcceptIO(loop), m_Loop(loop), m_UserNum(MaxUserNum), m_is_vxi(is_vxi)
{
    m_Socket.push_back(this);
}

WTScpiSocket::~WTScpiSocket()
{
    WaitAllServiceStop();
    shutdown(m_Sfd, SHUT_RDWR);
    close(m_Sfd);
    m_Socket.remove(this);
    if (m_is_vxi)
    {
        for (auto &Iter : m_clients_fd_vec)
        {
           close(Iter);
        }
        m_clients_fd_vec.clear();
        close(m_vxi_internal_comm_fd);
        close(m_internal_scpi_fd);
        m_SrvLink.clear();
    }
    WTLog::Instance().WriteLog(LOG_DEBUG, "WTScpiSocket m_Port%d destruct\n", m_Port);
}

int WTScpiSocket::StartSocketSrv(int Port)
{
    if(Port ==0 || m_UserNum == 0)
    {
        char ErrBuf[256] = {0};
        snprintf(ErrBuf,256,"m_UserNum =%d, Port=%d\n", m_UserNum, Port);
        WTLog::Instance().LOGERR(WT_ARG_ERROR, ErrBuf);
        return WT_ARG_ERROR;
    }

    //读取CMD配置文件
    if (CmdConfigInit() != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_FILE_OPEN_ERROR, "read cmd config file failed");
    }

    //创建线程池
    try
    {
        m_TaskPool.reset(new ThreadPool(m_UserNum));
        m_TaskPool->Activate();
    }
    catch (system_error const &e)
    {
        WTLog::Instance().LOGERR(WT_CREATE_THREAD_FAILED, "create thread pool failed");
        return WT_CREATE_THREAD_FAILED;
    }

    //监听PORT
    struct sockaddr_in Addr;
    int Flag = 1;

    m_Sfd = socket(AF_INET, SOCK_STREAM | SOCK_CLOEXEC, IPPROTO_TCP);

    if (m_Sfd <= 0)
    {
        WTLog::Instance().LOGERR(WT_CREATE_SOCKET_FAILED, "Create link socket error");
        return m_Sfd;
    }

    fcntl(m_Sfd, F_SETFL, fcntl(m_Sfd, F_GETFL, 0) | O_NONBLOCK); //设置sock为非阻塞模式

    bzero(&Addr, sizeof(Addr));
    Addr.sin_family = AF_INET;
    Addr.sin_port = htons(Port);
    Addr.sin_addr.s_addr = INADDR_ANY; //监听本仪器的任意地址;

    //重用是重用的地址，也就是说 同一个地址上可以绑定 若干个socket
    if (setsockopt(m_Sfd, SOL_SOCKET, SO_REUSEADDR, &Flag, sizeof(int)) == -1)
    {
        WTLog::Instance().LOGERR(WT_SOCKET_SET_ERROR, "Set Socket error");
        return WT_SOCKET_SET_ERROR;
    }

    if (bind(m_Sfd, (const sockaddr *)&Addr, sizeof(Addr)) != 0)
    {
        WTLog::Instance().LOGERR(WT_SOCKET_BIND_ERROR, "Server bind error");
        close(m_Sfd);
        return WT_SOCKET_BIND_ERROR;
    }

#ifdef DEBUG
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "bind " << inet_ntoa(Addr.sin_addr) << " : " << Port << " success! " << endl;
#endif
    listen(m_Sfd, 128);

    m_Port = Port;

    //绑定accept事件
    m_AcceptIO.set<WTScpiSocket, &WTScpiSocket::AcceptCb>(this);
    m_AcceptIO.start(m_Sfd, wtev::READ);
    return WT_OK;
}

void WTScpiSocket::AcceptCb(wtev::io &watcher, int revents)
{
    if (EV_ERROR & revents)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Got invalid event");
        return;
    }

    struct sockaddr_in ClientAddr;
    socklen_t ClientLen = sizeof(ClientAddr);

    int client_fd = accept(watcher.fd, (struct sockaddr *)&ClientAddr, &ClientLen);

    if (client_fd < 0)
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Accept error");
        return;
    }

    if(m_UserCnt >= m_UserNum)
    {
        char Tmp[128] = {0};
        sprintf(Tmp, "m_UserCnt=%d, MaxUserNum=%d, Port=%d", m_UserCnt, m_UserNum, m_Port);
        if (m_UserNum == 1 && m_SrvLink.size() > 0)
        {
            sprintf(Tmp + strlen(Tmp), ", PeerIp=%s, PeerPort=%u", (*m_SrvLink.begin())->GetPeerIp(), (*m_SrvLink.begin())->GetPeerPort());
        }

        WTLog::Instance().LOGERR(WT_LIC_VALUE_INVALID, Tmp);
        close(client_fd);
        return;
    }

    lock_guard<mutex> Lock(m_LinkMutex);
    m_UserCnt++;

    const char* client_ip = inet_ntoa(ClientAddr.sin_addr);
    uint16_t client_port = ntohs(ClientAddr.sin_port);
    printf("AcceptCb fd=%d, ip=%s, port=%d, m_Port: %d, m_is_vxi: %d\n", 
           client_fd, client_ip, client_port, m_Port, m_is_vxi);

    if (m_is_vxi) {
        HandleVxiConnection(client_fd, client_ip);
    } else {
        HandleStandardConnection(client_fd);
    }
}

void WTScpiSocket::HandleVxiConnection(int client_fd, const char* client_ip)
{
    /**
     * scpi提供vxi服务的端口(6025-6032)可接受本机连接(session_vxi)和外部连接(直连该端口)
     * VXI进程和SCPI进程的通信使用127.0.0.1
     * VXI模式下需要额外的socket实现进程间双向通信
     * m_vxi_internal_comm_fd用于唯一实例化ScpiService
     * scpi server通过localfd和m_vxi_internal_comm_fd进行双向通信
     * m_vxi_internal_comm_fd 监控 vxi和scpi server的通信，通过vxihandler监控vxi发送的scpi命令，执行scpi命令，并记录对应查询命令的fd
     * m_vxi_internal_comm_fd 监控 localfd，监控到scpi回复，并通过fd回复vxi
     */

    if (m_clients_fd_vec.empty() && m_vxi_internal_comm_fd < 0)
    {
        // 第一个连接，需要配置和连接VXI socket
        if (!ConfigAndConnectVxiSocket()) {
            close(client_fd);
            return;
        }

        m_clients_fd_vec.emplace_back(client_fd);
        shared_ptr<ScpiService> LinkSrv = make_shared<ScpiService>(m_vxi_internal_comm_fd, m_JsonRoot, m_Port, true);

        // 设置VXI连接断开回调
        LinkSrv->SetVxiLinkCloseCallback([this](int fd) {
            this->OnVxiLinkClose(fd);
        });

        m_SrvLink.push_back(LinkSrv);
        usleep(10 * 1000);
        m_TaskPool->AddTask(nullptr, bind(&WTScpiSocket::LinkService, this, LinkSrv, placeholders::_1));
        usleep(10 * 1000);
        LinkSrv->AddFd(client_fd);
    }
    else if (strcmp(client_ip, "127.0.0.1") == 0 && !m_vxi_internal_comm_established)
    {
        // 本地的连接, 用于和m_vxi_internal_comm_fd通信
        if (!m_SrvLink.empty()) {
            m_SrvLink.back()->AddLocalFd(client_fd);
            m_internal_scpi_fd = client_fd;
            m_vxi_internal_comm_established = true;
        } else {
            printf("HandleVxiConnection: Warning - No service available for local connection\n");
            close(client_fd);
            m_UserCnt--;
        }
    }
    else
    {
        // 新的客户端连接
        m_clients_fd_vec.emplace_back(client_fd);
        if (!m_SrvLink.empty()) {
            m_SrvLink.back()->AddFd(client_fd);
        } else {
            printf("HandleVxiConnection: Warning - No service available for new client connection\n");
            close(client_fd);
            auto it = std::find(m_clients_fd_vec.begin(), m_clients_fd_vec.end(), client_fd);
            if (it != m_clients_fd_vec.end()) {
                m_clients_fd_vec.erase(it);
                m_UserCnt--;
            }
        }
    }
}

void WTScpiSocket::HandleStandardConnection(int client_fd)
{
    // 标准SCPI连接处理
    shared_ptr<ScpiService> LinkSrv = make_shared<ScpiService>(client_fd, m_JsonRoot, m_Port);
    m_SrvLink.push_back(LinkSrv);
    m_TaskPool->AddTask(nullptr, bind(&WTScpiSocket::LinkService, this, LinkSrv, placeholders::_1));
}

bool WTScpiSocket::ConfigAndConnectVxiSocket()
{
    m_vxi_internal_comm_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (m_vxi_internal_comm_fd < 0) {
        WTLog::Instance().LOGERR(WT_ERROR, "Create api socket error");
        return false;
    }

    fcntl(m_vxi_internal_comm_fd, F_SETFL, fcntl(m_vxi_internal_comm_fd, F_GETFL, 0) | O_NONBLOCK);

    // 绑定和连接
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(m_Port);
    std::string local_ip = "127.0.0.1";
    inet_pton(AF_INET, local_ip.c_str(), &server_addr.sin_addr);

    if (connect(m_vxi_internal_comm_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        if (errno != EINPROGRESS) {
            printf("m_vxi_internal_comm_fd: %d Connect to server failed\n", m_vxi_internal_comm_fd);
            close(m_vxi_internal_comm_fd);
            return false;
        }
    }
    printf("m_vxi_internal_comm_fd: %d Connect to server success, port: %d\n", m_vxi_internal_comm_fd, m_Port);

    return true;
}

// 线程函数体，调用service的线程实体函数
void WTScpiSocket::LinkService(shared_ptr<ScpiService> LinkSrv, void *Arg)
{
    (void)Arg;

    LinkSrv->Run();

    StopLinkSrv(LinkSrv);
}

// 断开客户连接
void WTScpiSocket::StopLinkSrv(shared_ptr<ScpiService> &LinkSrv)
{
    printf("StopLinkSrv m_is_vxi: %d\n", m_is_vxi);
    unique_lock<mutex> Lock(m_LinkMutex);
    ScpiConnInfo::Instance().DelLink(LinkSrv->GetPeerIp(), LinkSrv->GetPeerPort());
    if (m_is_vxi)
    {
        for (auto &Iter : m_clients_fd_vec)
        {
            close(Iter);
        }
        m_clients_fd_vec.clear();
        close(m_vxi_internal_comm_fd);
        close(m_internal_scpi_fd);
        m_SrvLink.clear();
    }
    else
    {
        close(LinkSrv->GetFd());
        m_SrvLink.remove(LinkSrv);
    }
    m_UserCnt--;
}

void WTScpiSocket::WaitAllServiceStop(void)
{
    unique_lock<mutex> Lock(m_LinkMutex);
    for (auto &Iter : m_SrvLink)
    {
        Iter->Stop();
    }
    Lock.unlock();
#define SOCKET_DESTRUCT_WATI (3 * 1e6)
    int Cnt = 0;
    while (!m_SrvLink.empty() && ++Cnt < (SOCKET_DESTRUCT_WATI / 10))
    {
        usleep(10);
    }
}

void WTScpiSocket::OnVxiLinkClose(int fd)
{
    unique_lock<mutex> Lock(m_LinkMutex);

    // 从m_clients_fd_vec中移除对应的fd
    auto it = std::find(m_clients_fd_vec.begin(), m_clients_fd_vec.end(), fd);
    if (it != m_clients_fd_vec.end()) {
        m_clients_fd_vec.erase(it);
        m_UserCnt--;  // 减少用户计数
        printf("OnVxiLinkClose: Removed fd=%d, m_UserCnt=%d, remaining clients=%ld\n",
               fd, m_UserCnt, m_clients_fd_vec.size());
        
        if (m_clients_fd_vec.empty()) {
            m_vxi_internal_comm_established = false;
            close(m_vxi_internal_comm_fd);
            close(m_internal_scpi_fd);
            m_vxi_internal_comm_fd = -1;
            m_internal_scpi_fd = -1;
            printf("OnVxiLinkClose: m_clients_fd_vec empty, closed m_vxi_internal_comm_fd and m_internal_scpi_fd\n");
        }
    } else {
        printf("OnVxiLinkClose: Warning - fd=%d not found in m_clients_fd_vec\n", fd);
    }
}

int WTScpiSocket::CmdConfigInit(void)
{
    Json::Reader JsonReader; //Json解析器对象                                         //Json解析结果对象
    WTFileSecure CmdFile(WT_CMD_FILE_NAME);
    //打开JSon文本
    m_JsonIfstream.open(CmdFile.GetDecryptName());
    if (!m_JsonIfstream.is_open())
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CMD Config Json file open failed!" << std::endl;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CmdFile.GetDecryptName()=" << CmdFile.GetDecryptName() << std::endl;
        return WT_CONF_FILE_ERROR;
    }

    //解析JSon文本
    if (!JsonReader.parse(m_JsonIfstream, m_JsonRoot))
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===========CMD Config Json file parse failed!" << std::endl;
        return WT_JSON_PARSE_FAILED;
    }
    else
    {
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "===========CMD Config Json file parse success!" << std::endl;
    }
    m_JsonIfstream.close();
    return WT_OK;
}
