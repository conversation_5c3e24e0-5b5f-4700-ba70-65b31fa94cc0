/*
 * @Description: listmod相关对象以及结构体
 * @Autor: 
 * @Date: 20240703
 */

#include <vector>

#include "basehead.h"
#include "tester.h"

#ifndef __LISTMOD_SEQUENCE_H__
#define __LISTMOD_SEQUENCE_H__


#define LIST_MODE_MIN_VSG_MEAOFFSET (1 * 1e-6)
//seq类型
enum SEQUENCETYPE
{
    SEQUENCETX,
    SEQUENCERX,
    SEQUENCETXRX,
    SEQUENCETYPEEND,
};

//listmod场景：0非组合场景，1组合场景
enum LISTSCEN
{
    LISTSCEN_NONECOMB, //非组合场景
    LISTSCEN_COMB,     //组合场景
    LISTSCENEEND,
};

//seq状态
enum SEQUENCESTATE
{
    SEQUENCEOFF,         //停止状态, 这个时候可以去获取部分seg结果，要根据seg状态去获取
    SEQUENCERUNNING,     //运行状态，不能获取相关seg结果
    SEQUENCEFAILED,      //超时或者其他失败
    SEQUENCEREADY,       //seq处理完毕，理论上所有seg结果都可以获取
    SEQUENCESTATEEND,
};

//seg状态
enum SEGSTATE
{
    SEGNOREADY,   //相应seg还未处理
    SEGREADY,     //相应seg已处理，可以获取相关seg结果
    SEGSTATEEND,
};

//全局公共trigger参数，vsa/vsg共用
//虽然是全局参数，但考虑到后续有可能改成seg局参数，且该参数占有空间有限，所以scpi侧仍做成seg级的参数，在固件或驱动侧根据实际是否需要全局下发到fpga
struct TriggerCommonParam {
    double TriggerTimeout; //超时时间，单位s
    double  OutTriggerValidTime; //外部触发信号的有效长度,只在triggertype 为外部触发时有效，单位s
    int VsgSeqTrigRepeatRum; //只vsg有效，该参数跟vsg大循环次数相等
};

//时间参数结构体：单位us
struct SeqTimeParam {
    double Duration;   //信号长度
    double Meaoffset;  //对于Rx seg而言，该参数决定了seg之间的gap
    double Meadura;    //对于Rx seg而言，该参数无效
    int Repeat;        //重复次数
};

//tx Segment结构体：
struct TxSegment {
    VsaParameter vsaParam; //抓取参数
    SCPI_AlzParam vsaAlzParam; //分析参数,跟具体协议有关
    VsaTrigParam vsaTrigParam; //trig参数
    SeqTimeParam SegTimeParam; //时间参数
    TriggerCommonParam SegTigComParam; //公共trigger参数
    //相关统计参数待补充，根具体协议相关,todo
};

// rx Segment结构体
struct RxSegment
{
    VsgParameter vsgParam;     // vsg参数
    VsgPattern waveParam;      // 信号文件参数
    SeqTimeParam SegTimeParam; // 时间参数
    int vsgSyncParam;          // vsgListMode同步参数
    TriggerCommonParam SegTigComParam; //公共trigger参数
};

//Segment结构体
struct Segment {
    bool TxFlag; //表明这个segment是tx seg还是rx seg，如果是tx seq，则seq下所有的seg都tx seg，如果是rx seq，则都是rx seg，对于组合场景，则可能是tx seg也可能是 rx seg
    SEGSTATE SegSta;
    union {
          TxSegment tx_seg;
          RxSegment rx_seg;
    };

    Segment() {}
    ~Segment() {}
};

class Sequence {
public:
    Sequence() {}
    ~Sequence() {}
    void ClearSeg();
    int GetSeqSegNum() {return m_Seg.size();}
    int GetSeqRealSegNum(); // repeat展开的大小
    SEQUENCETYPE GetSeqType() {return m_SeqType;}
    std::vector<std::unique_ptr<Segment>>::iterator GetSegBeginIter();
    std::vector<std::unique_ptr<Segment>>::iterator GetSegEndIter();
    void SetSeqStat(SEQUENCESTATE Stat) {SeqStat = Stat;}
    SEQUENCESTATE GetSeqStat() {return SeqStat;}
    void SetSeqMaxSize(int Size);
    void SetListSeqFreq(SEQUENCETYPE Type, int SegNo, double Freq);
    void SetListSeqFreqAll(SEQUENCETYPE Type, double Freq);
    void SetListSeqPower(SEQUENCETYPE Type, int SegNo, double Power);
    void SetListSeqPowerAll(SEQUENCETYPE Type, double Power);
    void SetListSeqPort(SEQUENCETYPE Type, int SegNo, double Port);
    void SetListSeqPortAll(SEQUENCETYPE Type, double Port);
    void SetListSeqSync(SEQUENCETYPE Type, int SegNo, int Sync);
    void SetListSeqSyncAll(SEQUENCETYPE Type, int Sync);
    void SetListRxSeqIncre(SEQUENCETYPE Type, int Incre);
    void SetListSeqSampleRate(SEQUENCETYPE Type, int SegNo, double SampleRate);
    void SetListSeqSampleRateAll(SEQUENCETYPE Type, double SampleRate);
    void SetListSeqExtGain(SEQUENCETYPE Type, int SegNo, double ExtGain);
    void SetListSeqExtGainAll(SEQUENCETYPE Type, double ExtGain);
    void SetListTxSeqTriggerType(SEQUENCETYPE Type, int SegNo, int TrigType);
    void SetListTxSeqTriggerTypeAll(SEQUENCETYPE Type, int TrigType);
    void SetListTxSeqTriggerLevel(SEQUENCETYPE Type, int SegNo, double TrigLevel);
    void SetListTxSeqTriggerLevelAll(SEQUENCETYPE Type, double TrigLevel);
    void SetListTxSeqTriggerGaptime(SEQUENCETYPE Type, int SegNo, double TrigGap);
    void SetListTxSeqTriggerGaptimeAll(SEQUENCETYPE Type, double TrigGap);
    void SetListTxSeqTriggerFrametime(SEQUENCETYPE Type, int SegNo, double TrigFrame);
    void SetListTxSeqTriggerFrametimeAll(SEQUENCETYPE Type, double TrigFrame);
    void SetListSeqTriggerTimeout(SEQUENCETYPE Type, int SegNo, double TriggerTimeout);
    void SetListSeqTriggerTimeoutAll(SEQUENCETYPE Type, double TriggerTimeout);
    void SetListTxSeqGenTriggerType(SEQUENCETYPE Type, int SegNo, int GenTriggerType);
    void SetListTxSeqGenTriggerTypeAll(SEQUENCETYPE Type, int GenTriggerType);
    void SetListRxSeqWave(int SegNo, std::string &LowName);
    void SetListRxSeqWaveAll(std::string &LowName);
    void SetListSeqDuration(SEQUENCETYPE Type, int SegNo, double Duration);
    void SetListSeqDurationAll(SEQUENCETYPE Type, double Duration);
    void SetListSeqMeaoffset(SEQUENCETYPE Type, int SegNo, double Meaoffset);
    void SetListSeqMeaoffsetAll(SEQUENCETYPE Type, double Meaoffset);
    void SetListSeqMeaDur(SEQUENCETYPE Type, int SegNo, double Meadura);
    void SetListSeqMeaDurAll(SEQUENCETYPE Type, double Meadura);
    void SetListSeqRepeat(SEQUENCETYPE Type, int SegNo, double Repeat);
    void SetListSeqRepeatAll(SEQUENCETYPE Type, double Repeat);
    void SetListSeqAnalDemod(SEQUENCETYPE Type, int SegNo, int AnalDemod);
    void SetListSeqAnalDemodAll(SEQUENCETYPE Type, int AnalDemod);
    void DeleteListSeqSeg(int SegNo);
    void DeleteListSeqSegAll();
    void SetListLteTxSeqParam(SEQUENCETYPE Type, int SegNo, double *LteTxPara);
    void SetListLteTxSeqTdd(SEQUENCETYPE Type, int SegNo, int UpDownLink, int SpecSubframe);
    void SetListLteTxSeqRbAllocation(SEQUENCETYPE Type, int SegNo, int Auto, int NoRb, int Offset);
    void SetListLteTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *LteTxModulation);
    void SetListLteTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *LteTxSemask);
    void SetListLteTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *LteTxAclr);
    void SetListLteTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab);
    void SetListLteTxSeqPower(SEQUENCETYPE Type, int SegNo, int *LteTxPower);
    void SetListSeqRepet(int Repet);
    void SetListTxSeqTrigerOffset(double TrigerOffset);
    int GetListSeqRepet();
    void SetListSeqEnableFlag(int EnableFlag);
    int GetListSeqEnableFlag();
    void SetListRxSeqCellMod(int CellMod);
    int GetListSeqCellMod();
    void SetListSeqIncrementFlag(int IncrementFlag);
    int GetListSeqIncrementFlag();
    double GetListTxSeqTrigerOffset();
    void SetListRxSeqArbRepet(int SegNo, int Repet);
    void SetListRxSeqArbRepetAll(int Repet);
    void SetListRxSeqArbExtend(int SegNo, int Extend);
    void SetListRxSeqArbExtendAll(int Extend);
    void CheckListSeqSegNo(int SegNo);
    void* GetBaseParam(int SegNo) {
        if (SegNo >= m_Seg.size())
            return nullptr;
        if (m_SeqType == SEQUENCETX)
            return &(m_Seg[SegNo]->tx_seg.vsaParam);
        else
            return &(m_Seg[SegNo]->rx_seg.vsgParam);
    }

private:
    void ResetSeg(std::unique_ptr<Segment> &Seg, int SegNo);
    SEQUENCETYPE m_SeqType = SEQUENCETX;
    unsigned int m_MaxSegSize = 0;
    SEQUENCESTATE SeqStat = SEQUENCEOFF;
    std::vector<std::unique_ptr<Segment>> m_Seg; //Seg数组
    int m_Repet = 1;  //整个seq重复模式，当前只有rx seq有效，0为持续发送模式
    int m_EnableFlag = 0;           // vsgListMode同步参数
    int m_IncrementFlag = 0;        // vsgListMode同步参数
    double m_TrigerOffset = 0;  //整个seq trig偏移，当前只有tx seq有效
    std::string m_LowNam; //全局vawe，只有rx seq有效 
    int m_CellMod = 0;           // vsgListMode模式
};

class ListSeq {
public:
    ListSeq() {}
    ~ListSeq() {}
    void SetTxListModEnable();
    void SetTxListModDisable();
    void SetRxListModEnable();
    void SetRxListModDisable();
    bool GetListModEnable() {return m_ListEnable;}
    void ClearListSeq();
    LISTSCEN GetListModScen() {return m_SeqScen;}
    int GetListModSeqSize(SEQUENCETYPE Type);
    int GetListModRealSeqSize(SEQUENCETYPE Type);
    std::vector<std::unique_ptr<Segment>>::iterator GetSegBeginIter(SEQUENCETYPE Type);
    std::vector<std::unique_ptr<Segment>>::iterator GetSegEndIter(SEQUENCETYPE Type);
    void SetListModScen(LISTSCEN Scene) {m_SeqScen = Scene;}
    void SetListSeqStat(SEQUENCETYPE Type, SEQUENCESTATE Stat);
    SEQUENCESTATE GetListSeqStat(SEQUENCETYPE Type);
    void SetListSeqMaxSize(SEQUENCETYPE Type, int Sizes);
    void SetListSeqFreq(SEQUENCETYPE Type, int SegNo, double Freq);
    void SetListSeqFreqAll(SEQUENCETYPE Type, double Freq);
    void SetListSeqPower(SEQUENCETYPE Type, int SegNo, double Power);
    void SetListSeqPowerAll(SEQUENCETYPE Type, double Power);
    void SetListSeqPort(SEQUENCETYPE Type, int SegNo, double Port);
    void SetListSeqPortAll(SEQUENCETYPE Type, double Port);
    void SetListSeqSync(SEQUENCETYPE Type, int SegNo, int Sync);
    void SetListSeqSyncAll(SEQUENCETYPE Type, int Sync);
    void SetListRxSeqIncre(SEQUENCETYPE Type, int Incre);
    void SetListSeqSampleRate(SEQUENCETYPE Type, int SegNo, double SampleRate);
    void SetListSeqSampleRateAll(SEQUENCETYPE Type, double SampleRate);
    void SetListSeqExtGain(SEQUENCETYPE Type, int SegNo, double ExtGain);
    void SetListSeqExtGainAll(SEQUENCETYPE Type, double ExtGain);
    void SetListTxSeqTriggerType(SEQUENCETYPE Type, int SegNo, int TrigType);
    void SetListTxSeqTriggerTypeAll(SEQUENCETYPE Type, int TrigType);
    void SetListTxSeqTriggerLevel(SEQUENCETYPE Type, int SegNo, double TrigLevel);
    void SetListTxSeqTriggerLevelAll(SEQUENCETYPE Type, double TrigLevel);
    void SetListTxSeqTriggerGaptime(SEQUENCETYPE Type, int SegNo, double TrigGap);
    void SetListTxSeqTriggerGaptimeAll(SEQUENCETYPE Type, double TrigGap);
    void SetListTxSeqTriggerFrametime(SEQUENCETYPE Type, int SegNo, double TrigFrame);
    void SetListTxSeqTriggerFrametimeAll(SEQUENCETYPE Type, double TrigFrame);
    void SetListSeqTriggerTimeout(SEQUENCETYPE Type, int SegNo, double TriggerTimeout);
    void SetListSeqTriggerTimeoutAll(SEQUENCETYPE Type, double TriggerTimeout);
    void SetListTxSeqGenTriggerType(SEQUENCETYPE Type, int SegNo, int GenTriggerType);
    void SetListTxSeqGenTriggerTypeAll(SEQUENCETYPE Type, int GenTriggerType);
    void SetListRxSeqWave(int SegNo, std::string &LowName);
    void SetListRxSeqWaveAll(std::string &LowName);
    void SetListSeqDuration(SEQUENCETYPE Type, int SegNo, double Duration);
    void SetListSeqDurationAll(SEQUENCETYPE Type, double Duration);
    void SetListSeqMeaoffset(SEQUENCETYPE Type, int SegNo, double Meaoffset);
    void SetListSeqMeaoffsetAll(SEQUENCETYPE Type, double Meaoffset);
    void SetListSeqMeaDur(SEQUENCETYPE Type, int SegNo, double Meadura);
    void SetListSeqMeaDurAll(SEQUENCETYPE Type, double Meadura);
    void SetListSeqRepeat(SEQUENCETYPE Type, int SegNo, double Repeat);
    void SetListSeqRepeatAll(SEQUENCETYPE Type, double Repeat);
    void SetListSeqAnalDemod(SEQUENCETYPE Type, int SegNo, int AnalDemod);
    void SetListSeqAnalDemodAll(SEQUENCETYPE Type, int AnalDemod);
    void DeleteListSeqSeg(SEQUENCETYPE Type, int SegNo);
    void DeleteListSeqSegAll(SEQUENCETYPE Type);
    void SetListLteTxSeqParam(SEQUENCETYPE Type, int SegNo, double *LteTxPara);
    void SetListLteTxSeqTdd(SEQUENCETYPE Type, int SegNo, int UpDownLink, int SpecSubframe);
    void SetListLteTxSeqRbAllocation(SEQUENCETYPE Type, int SegNo, int Auto, int NoRb, int Offset);
    void SetListLteTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *LteTxModulation);
    void SetListLteTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *LteTxSemask);
    void SetListLteTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *LteTxAclr);
    void SetListLteTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab);
    void SetListLteTxSeqPower(SEQUENCETYPE Type, int SegNo, int *LteTxPower);
    void SetListSeqRepet(SEQUENCETYPE Type, int Repet);
    int GetListSeqRepet(SEQUENCETYPE Type);
    void SetListSeqEnableFlag(SEQUENCETYPE Type, int EnableFlag);
    void SetListRxSeqCellMod(SEQUENCETYPE Type, int CellMod);
    int GetListSeqEnableFlag(SEQUENCETYPE Type);
    int GetListSeqCellMod(SEQUENCETYPE Type);
    void SetListSeqIncrementFlag(SEQUENCETYPE Type, int IncrementFlag);
    int GetListSeqIncrementFlag(SEQUENCETYPE Type);
    void SetListRxSeqArbRepet(int SegNo, int Repet);
    void SetListRxSeqArbRepetAll(int Repet);
    void SetListRxSeqArbExtend(int SegNo, int Extend);
    void SetListRxSeqArbExtendAll(int Extend);
    void SetListTxSeqTrigerOffset(SEQUENCETYPE Type, double TrigerOffset);
    double GetListTxSeqTrigerOffset(SEQUENCETYPE Type);
    void* GetBaseParam(SEQUENCETYPE Type, int SegNo) {
        return m_Seq[Type].GetBaseParam(SegNo);
    }

private:
    LISTSCEN m_SeqScen = LISTSCEN_NONECOMB;
    bool m_ListEnable = false;
    Sequence m_Seq[2];  //在组合场景下，只有一个seq有效，非组合场景两个都有效
};

#endif // __LISTMOD_SEQUENCE_H__
