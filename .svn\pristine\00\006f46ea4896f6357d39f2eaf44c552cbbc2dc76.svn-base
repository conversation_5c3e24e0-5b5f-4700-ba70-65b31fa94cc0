

#include <dirent.h>
#include <unistd.h>
#include <sys/stat.h>

#include <fstream>
#include <string>
#include <map>
#include <vector>

#include <jsoncpp/json/json.h>
#include "dockerutils.h"
#include "basefun.h"
#include "wterror.h"
#include "wtlog.h"

using namespace std;

/////////////////////////////////////////////////////////////////////////////
// docker_log 日志分析类的函数定义
/////////////////////////////////////////////////////////////////////////////

class DockerLog
{
public:
    std::string &trim(std::string &s);
    int match_title(string line, const string title[], int title_pos[], int count);
    int match_line_data(string line, int title_pos[], string match_result[], int count);
    int parse_log_file(string file_path);
    map<int, map<string, string>> result;

public:
    // map<string, string> & query(const string &name);
    void query(const string &name, map<string, string> &data);
};

// docker ps -a 日志信息
static const string title[]{
    "CONTAINER ID",
    "IMAGE",
    "COMMAND",
    "CREATED",
    "STATUS",
    "PORTS",
    "NAMES",
};

void DockerLog::query(const string &name, map<string, string> &data)
{
    map<int, map<string, string>>::iterator iter;
    for (iter = result.begin(); iter != result.end(); iter++)
    {
        if (iter->second["NAMES"] == name)
        {
            data = iter->second;
            break;
        }
    }
}

std::string &DockerLog::trim(std::string &s)
{
    if (s.empty())
    {
        return s;
    }

    s.erase(0, s.find_first_not_of(" "));
    s.erase(s.find_last_not_of(" ") + 1);
    return s;
}

int DockerLog::match_title(string line, const string title[], int title_pos[], int count)
{
    int start = 0;
    for (int i = 0; i < count; ++i)
    {
        start = line.find(title[i]);
        if (start == -1)
        {
            return -1;
        }
        else
        {
            title_pos[i] = start;
        }
    }
    return 0;
}

int DockerLog::match_line_data(string line, int title_pos[], string match_result[], int count)
{
    int len = line.length();
    for (int i = 0; i < count; ++i)
    {
        string tmp = "";
        // 根据分类截取字符串
        if (i == (count - 1))
        {
            tmp = line.substr(title_pos[i], len - title_pos[i]);
        }
        else
        {
            tmp = line.substr(title_pos[i], title_pos[i + 1] - title_pos[i]);
        }
        match_result[i] = trim(tmp); //去除单词两边空格
    }
    return 0;
}

int DockerLog::parse_log_file(string file_path)
{
    int ret = WT_OK;
    int index = 0;                                         // map的行数
    int title_pos[sizeof(title) / sizeof(title[0])];       // title首字母下标
    string match_result[sizeof(title) / sizeof(title[0])]; //分类数据

    for (int i = 0; i < sizeof(title) / sizeof(title[0]); ++i)
    {
        title_pos[i] = -1;
    }

    ifstream fs;
    // int start = -1;
    bool is_fisrt_list = true; //是否为第一行标题行
    fs.open(file_path);        //打开文件
    if (fs.is_open())
    {
        string line;
        while (getline(fs, line))
        {
            // 判断是否已经读取完文件
            if (line.empty())
            {
                break;
            }

            if (is_fisrt_list)
            {
                is_fisrt_list = false;
                if (match_title(line, title, title_pos, sizeof(title) / sizeof(title[0])) == -1)
                {
                    // 当找不到title时返回错误结果
                    ret = WT_DOCKER_APP_OPT_FAILED;
                    WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker log parse failed");
                    break;
                }
            }
            else
            {
                match_line_data(line, title_pos, match_result, sizeof(title) / sizeof(title[0]));
                for (int i = 0; i < sizeof(title) / sizeof(title[0]); i++)
                {
                    result[index].insert(pair<string, string>(title[i].c_str(), match_result[i].c_str()));
                }
                index++;
            }
        }

        fs.close();
    }
    else
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker log open failed");
        ret = WT_DOCKER_APP_OPT_FAILED;
    }

    return ret;
}

/////////////////////////////////////////////////////////////////////////////
// DockerMgr 容器管理的函数定义
/////////////////////////////////////////////////////////////////////////////

#define DOCKER_RES_PATH "/var/local/docker_res"
#define DOCKER_RES_CONFIG "/var/local/docker_res/config.json"
#define DOCKER_OPT_SLEEP (50000)

DockerMgr &DockerMgr::Instance(void)
{
    static DockerMgr _;
    return _;
}

DockerMgr::DockerMgr()
    : m_JsonRoot(Json::Value(Json::objectValue))
{
    if (Basefun::IsFolderExist(DOCKER_RES_PATH) == 0)
    {
        Basefun::CreateDir(DOCKER_RES_PATH);
    }

    if (Basefun::IsFileExist(DOCKER_RES_CONFIG) == 0)
    {
        ResetConfig();
    }
}

int DockerMgr::ResetConfig()
{
    if (Basefun::IsFolderExist(DOCKER_RES_PATH) == 0)
    {
        Basefun::CreateDir(DOCKER_RES_PATH);
    }

    FILE *fp = fopen(DOCKER_RES_CONFIG, "wb");
    if (fp)
    {
        fwrite("{}", 2, 1, fp);
        fclose(fp);
    }
    return 0;
}

int DockerMgr::LoadConfig()
{
    m_JsonRoot.clear();

    std::ifstream infile;
    infile.open(DOCKER_RES_CONFIG, std::ifstream::in | std::ifstream::binary);
    if (!infile.is_open())
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker config open failed");
        return WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        Json::Reader JsonReader;
        if (!JsonReader.parse(infile, m_JsonRoot))
        {
            m_JsonRoot.clear();
            WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker config parse failed");
            return WT_DOCKER_APP_OPT_FAILED;
        }
        // 调试信息
        // cout << m_JsonRoot << endl;
        infile.close();
    }

    if (!m_JsonRoot.isMember("app_list"))
    {
        Json::Value data(Json::arrayValue);
        m_JsonRoot["app_list"] = data;
    }

    return WT_OK;
}

int DockerMgr::SaveConfig()
{
    ofstream fout;
    fout.open(DOCKER_RES_CONFIG, std::ios::out);
    if (fout.is_open())
    {
        Json::StyledWriter sw;
        fout << sw.write(m_JsonRoot);
        fout.close();
    }
    else
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker config save failed");
    }
    return WT_OK;
}

int DockerMgr::QueryAppListEntry(int resv, vector<docker_app_info> &arr)
{
    (void)resv;
    // vector<docker_app_info> arr;

    int Ret = WT_OK;
    Ret = CheckDockerEnvironment();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = LoadConfig();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Basefun::LinuxSystem("rm -f .docker.log");
    Basefun::LinuxSystem("docker ps -a > .docker.log");

    DockerLog dlog;
    Ret = dlog.parse_log_file(".docker.log");
    if (Ret != 0)
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker parse ps log failed");
        Ret = WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        Json::Value::Members mem = m_JsonRoot["app_list"].getMemberNames();
        map<string, string> data;

        for (auto iter = mem.begin(); iter != mem.end(); iter++)
        {
            string key = *iter;
            string name = m_JsonRoot["app_list"][key]["name"].asString();

            data.clear();
            dlog.query(name, data);
            int status_run = STATUS_EXITED;
            if (data.empty())
            {
                status_run = STATUS_NOT_EXIST;
            }
            else
            {
                if (data["STATUS"].compare(0, 2, "Up") == 0)
                {
                    status_run = STATUS_UP;
                }
                else if (data["STATUS"].compare(0, 6, "Exited") == 0)
                {
                    status_run = STATUS_EXITED;
                }
                else
                {
                    status_run = STATUS_OTHER;
                }
            }

            docker_app_info data;
            memset(data.resv, 0, sizeof(data.resv));
            strncpy(data.name, name.c_str(), sizeof(data.name)-1);
            data.name[sizeof(data.name)-1] = 0;
            data.status_cfg = m_JsonRoot["app_list"][key]["status"].asInt();
            data.status_run = status_run;
            arr.push_back(data);
        }
    }

    return Ret;
}

int DockerMgr::CheckDockerEnvironment()
{
    // int Ret = WT_OK;
    bool is_find_client = false;
    bool is_find_server = false;

    Basefun::LinuxSystem("rm -f .docker.log");
    Basefun::LinuxSystem("docker version > .docker.log");

    ifstream fs;
    fs.open(".docker.log");
    if (fs.is_open())
    {
        string line;
        while (getline(fs, line))
        {
            if (line.find("Client: Docker Engine") != string::npos)
            {
                is_find_client = true;
            }
            else if (line.find("Server: Docker Engine") != string::npos)
            {
                is_find_server = true;
            }

            if (is_find_client && is_find_server)
            {
                break;
            }
        }
        fs.close();
    }
    else
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker version log open failed");
        return WT_DOCKER_APP_OPT_FAILED;
    }

    if (is_find_client && is_find_server)
    {
        return WT_OK;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_DOCKER_ENV_NO_EXIST, "docker run env not exist!");
        return WT_DOCKER_ENV_NO_EXIST;
    }
}

int DockerMgr::SetAppStatus(const char *name, int status)
{
    int Ret = WT_OK;
    char cmd[100] = {0};
    if (status != 0)
    {
        sprintf(cmd, "docker start %s", name);
    }
    else
    {
        sprintf(cmd, "docker stop %s -t 0", name);
    }

    Basefun::LinuxSystem(cmd);

    usleep(DOCKER_OPT_SLEEP);
    Basefun::LinuxSystem("rm -f .docker.log");
    Basefun::LinuxSystem("docker ps -a > .docker.log");

    DockerLog dlog;
    Ret = dlog.parse_log_file(".docker.log");
    if (Ret != 0)
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker parse ps log failed");
        Ret = WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        map<string, string> data;
        dlog.query(name, data);
        if (data.empty())
        {
            WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker query app not exists");
            Ret = WT_DOCKER_APP_OPT_FAILED;
        }
        else
        {
            bool cur_config_status = data["STATUS"].compare(0, 2, "Up") == 0;
            if (status != 0 && cur_config_status)
            {
                Ret = WT_OK; // 设置到开启成功
            }
            else if (status == 0 && !cur_config_status)
            {
                Ret = WT_OK; // 设置到关闭成功
            }
            else
            {
                WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker app set status failed");
                Ret = WT_DOCKER_APP_OPT_FAILED;
            }
        }
    }

    return Ret;
}

int DockerMgr::SetAppStatusEntry(void *Data)
{
    char name[16] = {0};
    strncpy(name, (const char *)Data, sizeof(name)-1);
    name[sizeof(name)-1] = 0;

    int set_status = *((int *)((char *)Data + 16));
    int Ret = WT_OK;
    Ret = CheckDockerEnvironment();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = LoadConfig();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    if (!m_JsonRoot["app_list"].isMember(name))
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker no app exist");
        return WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        if (set_status == 0)
        {
            m_JsonRoot["app_list"][name]["status"] = 0; // 禁用
        }
        else
        {
            m_JsonRoot["app_list"][name]["status"] = 1; // 开启
        }

        SaveConfig();
    }

    Basefun::LinuxSystem("rm -f .docker.log");
    Basefun::LinuxSystem("docker ps -a > .docker.log");

    DockerLog dlog;
    Ret = dlog.parse_log_file(".docker.log");
    if (Ret != 0)
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker parse ps log failed");
        Ret = WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        map<string, string> data;
        dlog.query(name, data);
        if (data.empty())
        {
            WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker query app not exists");
            Ret = WT_DOCKER_APP_OPT_FAILED;
        }
        else
        {
            Ret = SetAppStatus(name, set_status);
        }
    }

    return Ret;
}

int DockerMgr::RemoveAppEntry(void *Data)
{
    int Ret = WT_OK;
    string home = "";
    char name[16] = {0};
    strncpy(name, (const char *)Data, sizeof(name)-1);
    name[sizeof(name)-1] = 0;


    Ret = CheckDockerEnvironment();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    Ret = LoadConfig();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    if (!m_JsonRoot["app_list"].isMember(name))
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker no app exist");
        return WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        home = m_JsonRoot["app_list"][name]["path"].asString();
        m_JsonRoot["app_list"].removeMember(name);
        SaveConfig();
    }

    Basefun::LinuxSystem("rm -f .docker.log");
    Basefun::LinuxSystem("docker ps -a > .docker.log");

    DockerLog dlog;
    Ret = dlog.parse_log_file(".docker.log");
    if (Ret != 0)
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker parse ps log failed");
        Ret = WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        map<string, string> data;
        dlog.query(name, data);
        if (data.empty())
        {
            WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker query app not exists");
            Ret = WT_DOCKER_APP_OPT_FAILED;
        }
        else
        {
            Ret = RemoveApp(name);
            Basefun::RemoveDir(home.c_str());
        }
    }

    return Ret;
}

int DockerMgr::RemoveApp(const char *name)
{
    int Ret = WT_OK;
    char cmd[100] = {0};
    sprintf(cmd, "docker stop %s -t 0", name);
    Basefun::LinuxSystem(cmd);
    usleep(DOCKER_OPT_SLEEP);

    sprintf(cmd, "docker rm %s", name);
    Basefun::LinuxSystem(cmd);

    Basefun::LinuxSystem("rm -f .docker.log");
    Basefun::LinuxSystem("docker ps -a > .docker.log");

    DockerLog dlog;
    Ret = dlog.parse_log_file(".docker.log");
    if (Ret != 0)
    {
        WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker parse ps log failed");
        Ret = WT_DOCKER_APP_OPT_FAILED;
    }
    else
    {
        map<string, string> data;
        dlog.query(name, data);
        if (data.empty())
        {
            Ret = WT_OK;
        }
        else
        {
            WTLog::Instance().LOGERR(WT_DOCKER_APP_OPT_FAILED, "docker delete app failed");
            Ret = WT_DOCKER_APP_OPT_FAILED;
        }
    }

    return Ret;
}