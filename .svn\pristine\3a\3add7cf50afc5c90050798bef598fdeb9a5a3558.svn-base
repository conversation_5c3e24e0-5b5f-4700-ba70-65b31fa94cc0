#ifndef _WLAN_WEP_H_
#define _WLAN_WEP_H_
#include "includes.h"
#include "ieee80211_def.h"
#include "common.h"
#include "wlan_encryption_api.h"

typedef struct
{
    HEADER_802_11 *hdr;
    u32 hdrlen;
    u8 *data;
    u32 len;
} FrameHeaderInfo;

typedef struct rc4_key_st
{
    u8 x, y;
    u8 data[256];
} RC4_KEY;

class wlan_wep
{
public:
    wlan_wep();
    virtual ~wlan_wep();
    /**
     * @brief encrypt data
     *
     * @param pInData : plaintext data
     * @param len :plaintext data length
     * @param iv: WEP IV header
     * @param pOutData: encrpyted data
     * @param outLen: encrypted data length
     * @return s32: 0 = OK, other = fail
     */
    virtual s32 encrypt_data(u8 *pInData, u32 len, WEP_IV *iv, u8 **pOutData, u32 *outLen);

    /**
     * @brief decrypt MAC frame
     *
     * @param pInData: encrypted MPDU data
     * @param len : encrypted MPDU data length
     * @param pOutData: dcrypted data
     * @param outLen: dcrypted data length
     * @param plaintext_pos: plaintext data postion at pOutData
     * @param data_len:plaintext data length
     * @return s32: 0 = OK, other = fail
     */
    virtual s32 decrypt_frame_data(u8 *pInData, u32 len, u8 **pOutData, u32 *outLen, u32 *plaintext_pos, u32 *data_len);
    /**
     * @brief if MPDU data not encrypted, copy it to output buffer
     *
     * @param pInData : MPDU data
     * @param len : MPDU data length
     * @param pOutData : output data
     * @param outLen : output data length
     * @param plaintext_pos :plaintext data postion at pOutData
     * @param data_len:plaintext data length
     * @param decrypted_ptr: decrypted data pointer
     * @param decrypted_len:decrypted data length
     */
    virtual void copy_not_encrypted_data(u8 *pInData, u32 len, u8 **pOutData, u32 *outLen, u32 *plaintext_pos, u32 *data_len, u8 *decrypted_ptr, u32 decrypted_len);

    /**
     * @brief reassemble frame = MAC header + plaintext data + FCS
     *
     * @param pInData
     * @param len
     * @param pOutData
     * @param outLen
     * @param plaintext_pos
     * @param data_len
     * @param decrypted_ptr
     * @param decrypted_len
     */
    void reassemble_frame(u8 *pInData, u32 len, u8 **pOutData, u32 *outLen, u32 *plaintext_pos, u32 *data_len, u8 *decrypted_ptr, u32 decrypted_len);
    /**
     * @brief copy encrypted data to output data buffer
     *
     * @param encrypted_ptr : encrypted data
     * @param encrypted_len:encrypted data length
     * @param pOutData: output data buffer pointer
     * @param outLen: output data length
     */
    void copy_encrypted_data(u8 *encrypted_ptr, u32 encrypted_len, u8 **pOutData, u32 *outLen);

    /**
     * @brief Get the process data object, like error msg and other progress information
     *
     * @param err_msg : msg buffer
     * @param data : output data buffer pointer
     * @param cnt : output data length
     * @return s32 : 0=OK, other=Fail
     */
    virtual s32 get_process_data(s8 *err_msg, s8 **data, s32 *cnt);
    ;
    /**
     * @brief WEP encrypt MPDU
     *
     * @param key:wep seed
     * @param keylen:wep seed length
     * @param buf:plaintext data, will overwrite by cipher data
     * @param plen:plaintext data length, bytes
     */
    void wep_crypt(u8 *key, u32 keylen, u8 *buf, size_t plen);

    /**
     * @brief wep seed create
     *
     * @param pIV :IV, 3 bytes
     * @param pKey :Key buffer
     * @param KeyLen :Key length
     * @param seed :seed buffer, default 256 bytes
     * @param seed_len : seed length
     */
    void wep_seed(u8 *pIV, u8 *pKey, u32 KeyLen, u8 *seed, u32 *seed_len);
    /**
     * @brief WEP decrypt MPDU
     *
     * @param wep_key : wep key
     * @param wep_key_len : wep key length, bytes
     * @param data : cipher data
     * @param data_len : cipher data length, bytes.include IV + MPDU + ICV
     * @param decrypted_len : plaintext data length, bytes
     * @return u8* : plaintext data pointer on success, nullptr on failure
     */
    u8 *wep_decrypt(const u8 *wep_key, size_t wep_key_len, const u8 *data, size_t data_len, size_t *decrypted_len);

    /**
     * @brief try wep encrypt or decrypt
     *
     * @param wep_seed : wep seed --> key
     * @param seed_len : wep seed --> key length
     * @param data : cipher data
     * @param data_len : cipher data length
     * @param plain : plaintext data
     * @return s32 : 0 = OK, other = fail
     */
    s32 try_wep(u8 *wep_seed, u32 seed_len, const u8 *data, size_t data_len, u8 *plain);
    /**
     * @brief Set the cipher info object
     *
     * @param CipherType : cipher type, eg. TKIP, CCMP-128
     * @param key : cipher key
     * @param keyLen : key length
     * @return s32 : 0 = OK, other = fail
     */
    s32 set_cipher_info(const s8 *CipherType, u8 *key, s32 keyLen);

    /**
     * @brief Get the cipher type object
     *
     * @return s32 : return cipher type, -1 = fail
     */
    s32 get_cipher_type();

    /**
     * @brief Get the cipher name object
     *
     * @return const s8* : nullptr = fail, other = OK
     */
    const s8 *get_cipher_name();

    /**
     * @brief Get the cipher object
     *
     * @return CipherKey* : nullptr = fail, other = OK
     */
    CipherKey *get_cipher();

    /**
     * @brief Get the cipher object
     *
     * @param CipherType : ciphter type name
     * @return CipherKey* : nullptr = fail, other = OK
     */
    CipherKey *get_cipher(std::string CipherType);

    /**
     * @brief read MPDU data and parser it
     *
     * @param data : cipher frame data
     * @param len : cipher frame data length
     * @return s32 : 0 = OK, other = fail
     */
    s32 parsing_frame(const u8 *data, size_t len);

    /* write decrypted note */
    void write_decrypted_note(const u8 *decrypted, const u8 *tk, size_t tk_len);

    /**
     * @brief get frame header data length and frame body position
     *
     * @param data : MPDU data
     * @param len : MPDU data length
     * @return s32
     */
    s32 get_hdrlen(const u8 *data, size_t len);

    /**
     * @brief derivation psk
     *
     * @param input : psk input parameter
     * @param key : key
     * @param keylen : key length
     * @return s32 : 0 = OK, other = fail
     */
    s32 wpa_derive_psk(PSK_KeyParam *input, u8 *key, u32 *keylen);

    /**
     * @brief derivation tk
     *
     * @param input : TK input paramter
     * @param key : key
     * @param keylen : key length
     * @return s32 : 0 = OK, other = fail
     */
    s32 wpa_derive_tk(TK_KeyParam *input, u8 *key, u32 *keylen);

    /**
     * @brief save or print debug msg to buffer
     *
     * @param level : msg level
     * @param fmt : format
     * @param ...
     */
    void wep_printf(int level, const s8 *fmt, ...);

    /**
     * @brief print data to out memory hex format
     *
     * @param level : msg level
     * @param title : msg title
     * @param buf : data buffer
     * @param len : data length
     */
    void wep_hexdump(int level, const s8 *title, const void *buf, size_t len);
    /**
     * @brief expand debug msg buffer size
     *
     * @param data_len : msg data length
     */
    void wep_expand_dumpbuf(s32 data_len);
    /**
     * @brief get aa and sa in data frame(In an infrastructure BSS)
     *
     * @param pInData : frame data
     * @param len : frame data length
     * @param sa : Supplicant's MAC address (SPA) and the STA's MAC address are equal
     * @param aa : the IEEE 802.1X Authenticator MAC address (AA) and the AP's MAC address are the same
     * @return s32 : 0 = OK, other = fail
     */
    s32 get_ap_sta_addr(u8 *pInData, u32 len, u8 *sa, u8 *aa);

    /**
     * @brief get amsdu position in data frame(In an infrastructure BSS)
     *
     * @param pInData : frame data
     * @param len : frame data length
     * @param msdu_pos : MSDU position at pIndata
     * @param msdu_len : MSDU data length
     * @param have_mesh : is mesh MSDU
     * @return s32 : 0 = OK, other = fail
     */
    s32 get_amsdu_data(u8 *pInData, u32 len, u32 *msdu_pos, u32 *msdu_len, u32 *have_mesh);
    /**
    * @brief get msdu 802.3 llc data frame
    *
    * @param pInData : frame data
    * @param len : frame data length
    * @param payload_pos : payload position at pIndata
    * @param payload_len : payload data length
    * @param hdrlen : frame MAC header length
    * @param result_8023: 802.3 llc result buffer
    * @return s32 : 0 = OK, other = fail
    */
    s32 get_802_3_llc(
        u8 *pInData,
        u32 len,
        u32 *payload_pos,
        u32 *payload_len,
        s32 hdrlen,
        LLC_802_3_header *result_8023);
    /**
    * @brief get msdu ethernt II data frame
    *
    * @param pInData : frame data
    * @param len : frame data length
    * @param payload_pos : payload position at pIndata
    * @param payload_len : payload data length
    * @param hdrlen : frame MAC header length
    * @param llc_hdr_len : frame LLC header length
    * @param result_ethernet: ethernet II result buffer
    * @return s32 : 0 = OK, other = fail
    */
    s32 get_ethernet_ii(
        u8 *pInData,
        u32 len,
        u32 *payload_pos,
        u32 *payload_len,
        s32 hdrlen,
        s32 llc_hdr_len,
        EthernetDataType *result_ethernet);

    /**
    * @brief get msdu Ethernet II data frame
    *
    * @param pInData : frame data
    * @param len : frame data length
    * @param payload_pos : payload position at pIndata
    * @param payload_len : payload data length
    * @param result_type : ethernet or 802.3 llc. 0 == ethernet, 1 == 802.3 llc
    * @param result_ethernet: ethernet result buffer
    * @param result_8023: 802.3 llc result buffer
    * @return s32 : 0 = OK, other = fail
    */
    s32 get_ethernet_or_8023_data(
        u8 *pInData,
        u32 len,
        u32 *payload_pos,
        u32 *payload_len,
        u32 *result_type,
        EthernetDataType *result_ethernet,
        LLC_802_3_header *result_8023);

    /**
    * @brief get a-msdu Ethernet II data frame
    *
    * @param pInData : frame data
    * @param len : frame data length
    * @param amsdu_data : a-msdu data
    * @param ethernet_payload_pos : ethernet payload data within A-MSDU
    * @param ethernet_payload_len : ethernet payload data length
    * @param result_type : ethernet or 802.3 llc. 0 == ethernet, 1 == 802.3 llc
    * @param result_ethernet: ethernet result buffer
    * @return s32 : 0 = OK, other = fail
    */
    s32 get_amsdu_ethernet_data(
        u8 *pInData,
        u32 len,
        u8 *amsdu_data,
        u32 amsdu_data_len,
        u32 *ethernet_payload_pos,
        u32 *ethernet_payload_len,
        u32 *result_type,
        EthernetDataType *result_ethernet);
    /**
     * @brief check msdu have mesh data
     *
     * @param fc
     * @param qos_ctl
     * @return s32 : 0 = OK, other = fail
     */
    s32 has_mesh_control(u16 fc, const u8 *qos_ctl);
    /**
     * @brief XOR RC4 stream to given data with skip-stream-start
     *
     * @param key : RC4 key
     * @param keylen : RC4 key length
     * @param skip : number of bytes to skip from the beginning of the RC4 stream
     * @param data : data to be XOR'ed with RC4 stream
     * @param data_len : buf length
     */
    void rc4_skip(const u8 *key, size_t keylen, size_t skip, u8 *data, size_t data_len);

    /**
     * @brief RC4 encrypt method
     *
     * @param buf : data buffer
     * @param len : data length
     * @param key : key
     * @param key_len : key length
     */
    void rc4(u8 *buf, size_t len, const u8 *key, size_t key_len);
    /**
     * @brief : init WEP weak IV
     *
     * @param p : IV buffer
     * @param keylen : WEP key length
     * @param method : four methods for generator weak IV
     */
    void init_weak_iv_wep(u8 *p, s32 keylen, int method = 1);
    /**
     * @brief: find WEP weak IVs
     *
     * @param p : pointer to the first byte of an IV (bigendian, need to change little endian)
     * @return int : -1 = this IV is not weak for any key bytes
     * @return int : n = this IV is weak for byte n of a WEP key
     */
    int find_weak_iv_wep(u8 *data);
    /**
     * @brief Set the aad asmdu capable object
     *
     * @param value
     */
    void set_aad_asmdu_capable(int value) { m_amsdu_capab = value; };
protected:
    unique_ptr<u8[]> m_decrypt_buf = nullptr; /* tmp buffer for decrypted data */
    unique_ptr<u8[]> m_crypt_buf = nullptr;   /* tmp buffer for encrypted data */
protected:
    std::string cipher_name = "NONE";
    unique_ptr<u8[]> m_OutData = nullptr; /* buffer for output data */
    u32 m_OutLen = 0;
    unique_ptr<u8[]> m_processData = nullptr; /* buffer for decrypt detail information output */
    s32 m_processDataMemLen = 0;
    s32 m_processDataIndex = 0;
    FrameHeaderInfo m_hdrInfo;
    s8 m_err_msg[256] = {0};
    s32 m_amsdu_capab = 0;
private:
    s32 SetKey(u8 *pKey, u32 keyLen);
    std::map<std::string, CipherKey> m_CipherTable;

private:
    s32 rx_frame(const u8 *data, size_t len);
    s32 rx_data(const u8 *data, size_t len);
    s32 rx_data_bss(const HEADER_802_11 *hdr, size_t hdrlen, const u8 *qos, const u8 *dst, const u8 *src, const u8 *data, size_t len);
    s32 rx_mgmt_group(const u8 *data, size_t len);
    s32 check_mmie_mic(u32 mgmt_group_cipher, size_t hdr_len,
                       const u8 *igtk, size_t igtk_len,
                       const u8 *data, size_t len);
};

#endif