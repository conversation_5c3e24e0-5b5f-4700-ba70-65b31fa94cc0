#include "scpi_3gpp_base.h"
// extern int pusch_pb_1_4[6];
// extern int pusch_pb_3[11];
// extern int pusch_pb_5[16];
// extern int pusch_pb_10[24];
// extern int pusch_pb_15[29];
// extern int pusch_pb_20[34];

// extern int rb_num_array[34];

// PUSCH RB 分配表
int pusch_pb_1_4[6] = {1, 2, 3, 4, 5, 6};
int pusch_pb_3[11] = {1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15};
int pusch_pb_5[16] = {1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15, 16, 18, 20, 24, 25};
int pusch_pb_10[24] = {1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15, 16, 18, 20, 24, 25, 27, 30, 32, 36, 40, 45, 48, 50};
int pusch_pb_15[29] = {1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15, 16, 18, 20, 24, 25, 27, 30, 32, 36, 40, 45, 48, 50, 54, 60, 64, 72, 75};
int pusch_pb_20[34] = {1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15, 16, 18, 20, 24, 25, 27, 30, 32, 36, 40, 45, 48, 50, 54, 60, 64, 72, 75, 80, 81, 90, 96, 100};

int rb_num_array[34] = {1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15, 16, 18, 20, 24, 25, 27, 30, 32, 36, 40, 45, 48, 50, 54, 60, 64, 72, 75, 80, 81, 90, 96, 100};


int mcs_tbs_paylaod_map[29][34] = {
    {16, 32, 56, 88, 120, 152, 208, 224, 256, 328, 392, 424, 488, 536, 648, 680, 744, 808, 872, 1000, 1096, 1256, 1320, 1384, 1480, 1672, 1800, 1992, 2088, 2216, 2280, 2536, 2664, 2792},
    {24, 56, 88, 144, 176, 208, 256, 328, 344, 424, 520, 568, 632, 712, 872, 904, 968, 1064, 1160, 1288, 1416, 1608, 1736, 1800, 1992, 2152, 2344, 2600, 2728, 2856, 2984, 3240, 3496, 3624},
    {32, 72, 144, 176, 208, 256, 328, 376, 424, 520, 648, 696, 776, 872, 1064, 1096, 1192, 1320, 1416, 1608, 1800, 2024, 2152, 2216, 2408, 2664, 2856, 3240, 3368, 3624, 3624, 4008, 4264, 4584},
    {40, 104, 176, 208, 256, 328, 440, 504, 568, 680, 872, 904, 1032, 1160, 1384, 1416, 1544, 1736, 1864, 2088, 2344, 2600, 2792, 2856, 3112, 3496, 3752, 4264, 4392, 4776, 4776, 5352, 5544, 5736},
    {56, 120, 208, 256, 328, 408, 552, 632, 696, 840, 1064, 1128, 1288, 1416, 1736, 1800, 1928, 2152, 2280, 2600, 2856, 3240, 3496, 3624, 3880, 4264, 4584, 5160, 5352, 5736, 5736, 6456, 6968, 7224},
    {72, 144, 224, 328, 424, 504, 680, 776, 872, 1032, 1320, 1384, 1544, 1736, 2088, 2216, 2344, 2664, 2792, 3112, 3496, 4008, 4264, 4392, 4776, 5352, 5736, 6200, 6712, 6968, 7224, 7992, 8504, 8760},
    {328, 176, 256, 392, 504, 600, 808, 936, 1032, 1224, 1544, 1672, 1864, 2088, 2472, 2600, 2792, 3112, 3368, 3752, 4136, 4776, 4968, 5160, 5736, 6200, 6712, 7480, 7736, 8248, 8504, 9528, 9912, 10296},
    {104, 224, 328, 472, 584, 712, 968, 1096, 1224, 1480, 1800, 1928, 2216, 2472, 2984, 3112, 3368, 3624, 3880, 4392, 4968, 5544, 5992, 6200, 6712, 7224, 7736, 8760, 9144, 9912, 9912, 11064, 11832, 12216},
    {120, 256, 392, 536, 680, 808, 1096, 1256, 1384, 1672, 2088, 2216, 2536, 2792, 3368, 3496, 3752, 4264, 4584, 4968, 5544, 6200, 6712, 6968, 7480, 8504, 9144, 9912, 10680, 11064, 11448, 12576, 13536, 14112},
    {136, 296, 456, 616, 776, 936, 1256, 1416, 1544, 1864, 2344, 2536, 2856, 3112, 3752, 4008, 4264, 4776, 5160, 5736, 6200, 6968, 7480, 7992, 8504, 9528, 10296, 11448, 11832, 12576, 12960, 14112, 15264, 15840},
    {144, 328, 504, 680, 872, 1032, 1384, 1544, 1736, 2088, 2664, 2792, 3112, 3496, 4264, 4392, 4776, 5352, 5736, 6200, 6968, 7992, 8504, 8760, 9528, 10680, 11448, 12576, 12960, 14112, 14112, 15840, 16992, 17568},
    {176, 376, 584, 776, 1000, 1192, 1608, 1800, 2024, 2408, 2984, 3240, 3624, 4008, 4776, 4968, 5544, 5992, 6456, 7224, 7992, 9144, 9528, 9912, 11064, 12216, 12960, 14688, 15264, 16416, 16416, 18336, 19080, 19848},
    {208, 440, 680, 904, 1128, 1352, 1800, 2024, 2280, 2728, 3368, 3624, 4136, 4584, 5544, 5736, 6200, 6712, 7224, 8248, 9144, 10296, 11064, 11448, 12216, 13536, 14688, 16416, 16992, 18336, 18336, 20616, 22152, 22920},
    {224, 488, 744, 1000, 1256, 1544, 2024, 2280, 2536, 3112, 3880, 4136, 4584, 5160, 6200, 6456, 6968, 7736, 8248, 9144, 10296, 11448, 12216, 12960, 14112, 15264, 16416, 18336, 19080, 20616, 20616, 22920, 24496, 25456},
    {256, 552, 840, 1128, 1416, 1736, 2280, 2600, 2856, 3496, 4264, 4584, 5160, 5736, 6968, 7224, 7736, 8504, 9144, 10296, 11448, 12960, 13536, 14112, 15264, 16992, 18336, 20616, 21384, 22920, 22920, 25456, 27376, 28336},
    {280, 600, 904, 1224, 1544, 1800, 2472, 2728, 3112, 3624, 4584, 4968, 5544, 6200, 7224, 7736, 8248, 9144, 9912, 11064, 12216, 13536, 14688, 15264, 16416, 18336, 19848, 22152, 22920, 24496, 24496, 27376, 29296, 30576},
    {328, 632, 968, 1288, 1608, 1928, 2600, 2984, 3240, 3880, 4968, 5160, 5992, 6456, 7736, 7992, 8760, 9912, 10296, 11832, 12960, 14688, 15840, 16416, 17568, 19848, 20616, 23688, 24496, 26416, 26416, 29296, 31704, 32856},
    {336, 696, 1064, 1416, 1800, 2152, 2856, 3240, 3624, 4392, 5352, 5736, 6456, 7224, 8760, 9144, 9912, 10680, 11448, 12960, 14688, 16416, 17568, 18336, 19848, 21384, 22920, 26416, 27376, 29296, 29296, 32856, 35160, 36696},
    {376, 776, 1160, 1544, 1992, 2344, 3112, 3624, 4008, 4776, 5992, 6200, 7224, 7992, 9528, 9912, 10680, 11832, 12576, 14112, 15840, 17568, 19080, 19848, 21384, 23688, 25456, 28336, 29296, 31704, 31704, 35160, 37888, 39232},
    {408, 840, 1288, 1736, 2152, 2600, 3496, 3880, 4264, 5160, 6456, 6968, 7736, 8504, 10296, 10680, 11448, 12960, 13536, 15264, 16992, 19080, 20616, 21384, 22920, 25456, 27376, 30576, 32856, 34008, 35160, 39232, 40576, 43816},
    {440, 904, 1384, 1864, 2344, 2792, 3752, 4136, 4584, 5544, 6968, 7480, 8248, 9144, 11064, 11448, 12576, 14112, 14688, 16992, 18336, 20616, 22152, 22920, 25456, 28336, 29296, 34008, 35160, 36696, 37888, 42368, 45352, 46888},
    {488, 1000, 1480, 1992, 2472, 2984, 4008, 4584, 4968, 5992, 7480, 7992, 9144, 9912, 12216, 12576, 13536, 15264, 15840, 18336, 19848, 22920, 24496, 25456, 27376, 30576, 31704, 36696, 37888, 40576, 40576, 45352, 48936, 51024},
    {520, 1064, 1608, 2152, 2664, 3240, 4264, 4776, 5352, 6456, 7992, 8504, 9528, 10680, 12960, 13536, 14688, 16416, 16992, 19080, 21384, 24496, 25456, 27376, 29296, 32856, 34008, 39232, 40576, 43816, 43816, 48936, 51024, 55056},
    {552, 1128, 1736, 2280, 2856, 3496, 4584, 5160, 5736, 6968, 8504, 9144, 10296, 11448, 13536, 14112, 15264, 16992, 18336, 20616, 22920, 25456, 27376, 28336, 30576, 34008, 36696, 40576, 43816, 45352, 46888, 51024, 55056, 57336},
    {584, 1192, 1800, 2408, 2984, 3624, 4968, 5544, 5992, 7224, 9144, 9912, 11064, 12216, 14688, 15264, 16416, 18336, 19848, 22152, 24496, 27376, 29296, 30576, 32856, 36696, 39232, 43816, 45352, 48936, 48936, 55056, 59256, 61664},
    {616, 1256, 1864, 2536, 3112, 3752, 5160, 5736, 6200, 7480, 9528, 10296, 11448, 12576, 15264, 15840, 16992, 19080, 20616, 22920, 25456, 28336, 30576, 31704, 34008, 37888, 40576, 45352, 46888, 51024, 51024, 57336, 61664, 63776},
    {712, 1480, 2216, 2984, 3752, 4392, 5992, 6712, 7480, 8760, 11064, 11832, 13536, 14688, 17568, 18336, 19848, 22152, 23688, 26416, 29296, 32856, 35160, 36696, 40576, 43816, 46888, 52752, 55056, 59256, 59256, 66592, 71112, 75376}};

double DL_UE_PDSCHPA_MAP[8] = {-6.02, -4.77, -3.01, -1.77, 0, 0.97, 2.04, 3.01};
#define ALG_3GPP_UL 0
#define ALG_3GPP_DL 1
#define ALG_3GPP_SPEC 2
int UL_DL_CONFIG_MAP[7][10] = {
    {ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_UL},
    {ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL},
    {ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_DL},
    {ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL},
    {ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL, ALG_3GPP_DL},
    {ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL, ALG_3GPP_SPEC, ALG_3GPP_UL, ALG_3GPP_UL, ALG_3GPP_DL},
};

int UL_MODULE_MCS_MAP[2][5][2] = {
    {{0, 28},
     {0, 10},
     {11, 20},
     {21, 28},
     {0, 28}},
    {{0, 28},
     {0, 5},
     {6, 13},
     {14, 22},
     {23, 28}}};

int DL_MODULE_MCS_MAP[5][9][2] =
    {{{0, 28},
      {0, 28},
      {0, 28},
      {0, 28},
      {0, 28},
      {0, 28},
      {0, 28},
      {0, 28},
      {0, 28}},
      {{0, 28},
      {0, 9},
      {0, 9},
      {10, 16},
      {10, 16},
      {17, 28},
      {17, 28},
      {0, 28},
      {0, 28}},
     {{0, 28},
      {0, 4},
      {0, 4},
      {5, 10},
      {5, 10},
      {11, 19},
      {11, 19},
      {20, 27},
      {20, 27}},
     {{0, 28},
      {0, 4},
      {0, 4},
      {5, 7},
      {5, 7},
      {8, 14},
      {8, 14},
      {15, 22},
      {15, 22}},
     {{0, 28},
      {0, 9},
      {44, 45},
      {10, 16},
      {46, 48},
      {17, 29},
      {49, 52},
      {30, 38},
      {53, 56}}};

int NR_BANDWIDTH_MAP[13] =
{
    5000000,
    10000000,
    15000000,
    20000000,
    25000000,
    30000000,
    40000000,
    50000000,
    60000000,
    70000000,
    80000000,
    90000000,
    100000000
};

int NR_SCS_MAP[3] =
{
    15000,
    30000,
    60000
};

int NR_BWP_RB_MAP[13][3] =
{
    {25, 11, 0},
    {52, 24, 11},
    {79, 38, 18},
    {106, 51, 24},
    {133, 65, 31},
    {160, 78, 38},
    {216, 106, 51},
    {270, 133, 65},
    {0, 162, 79},
    {0, 189, 93},
    {0, 217, 107},
    {0, 245, 121},
    {0, 273, 135},
};

// 详情参考蜂窝接口文档附录-band
BandChanItem LTE_ULBandChanMap[BAND_CHANNEL_ITEM_MAX] ={ // UL
    // FDD
    {.Band = 1, .ChannelMin = 18000, .ChannelMax = 18599, .Delta = 1200},
    {2, 18600, 19199, -100},
    {3, 19200, 19949, -2100},
    {4, 19950, 20399, -2850},
    {5, 20400, 20649, -12160},
    {6, 20650, 20749, -12350},
    {7, 20750, 21449, 4250},
    {8, 21450, 21799, -12650},
    {9, 21800, 22149, -4301},
    {10, 22150, 22749, -5050},
    {11, 22750, 22949, -8471},
    {12, 23010, 23179, -16020},
    {13, 23180, 23279, -15410},
    {14, 23280, 23379, -15400},
    {17, 23730, 23849, -16690},
    {18, 23850, 23999, -15700},
    {19, 24000, 24149, -15700},
    {20, 24150, 24449, -15830},
    {21, 24450, 24599, -9971},
    {22, 24600, 25399, 9500},
    {23, 25500, 25699, -5500},
    {24, 25700, 26039, -9435},
    {25, 26040, 26689, -7540},
    {26, 26690, 27039, -18550},
    {27, 27040, 27209, -18970},
    {28, 27210, 27659, -20180},
    {30, 27660, 27759, -4610},
    {31, 27760, 27809, -23235},

    // TDD
    {33, 36000, 36199, -17000},
    {34, 36200, 36349, -16100},
    {35, 36350, 36949, -17850},
    {36, 36950, 37549, -17650},
    {37, 37550, 37749, -18450},
    {38, 37750, 38249, -12050},
    {39, 38250, 38649, -19450},
    {40, 38650, 39649, -15650},
    {41, 39650, 41589, -14690},
    {42, 41590, 43589, -7590},
    {43, 43590, 45589, -7590},
    {44, 45590, 46589, -38560},
    {45, 46590, 46789, -32120},
    {48, 55240, 56739, -19740},
    {50, 58240, 59089, -43920},
    {51, 59090, 59139, -44820},
    {52, 59140, 60139, -26140},

    // FDD
    {65, 131072, 131971, -111872},
    {66, 131972, 132671, -114872},
    {68, 132672, 132971, -125692},
    {70, 132972, 133121, -116022},
    {71, 133122, 133471, -126492},
    {72, 133472, 133521, -128962},
    {73, 133522, 133571, -129022},
    {74, 133572, 134001, -119302},
    {85, 134002, 134181, -127022},
};

// 4G LTE DL Band、Channel与Frequency映射关系
BandChanFreqItem LTE_DLBandChanFreqMap[BAND_CHANNEL_ITEM_MAX] = {
    {.Band = 1, .N_ref = 0, .ChannelMax = 599, .F_ref = 2110},
    {2, 600, 1199, 1930},
    {3, 1200, 1949, 1805},
    {4, 1950, 2399, 2110},
    {5, 2400, 2649, 869},
    {6, 2650, 2749, 875},
    {7, 2750, 3449, 2620},
    {8, 3450, 3799, 925},
    {9, 3800, 4149, 1844.9},
    {10, 4150, 4749, 2110},
    {11, 4750, 4949, 1475.9},
    {12, 5010, 5179, 729},
    {13, 5180, 5279, 746},
    {14, 5280, 5379, 758},
    {17, 5730, 5849, 734},
    {18, 5850, 5999, 860},
    {19, 6000, 6149, 875},
    {20, 6150, 6449, 791},
    {21, 6450, 6599, 1495.9},
    {22, 6600, 7399, 3510},
    {23, 7500, 7699, 2180},
    {24, 7700, 8039, 1525},
    {25, 8040, 8689, 1930},
    {26, 8690, 9039, 859},
    {27, 9040, 9209, 852},
    {28, 9210, 9659, 758},
    {29, 9660, 9759, 717},
    {30, 9770, 9869, 2350},
    {31, 9870, 9919, 462.5},
    {32, 9920, 10359, 1452},

    {33, 36000, 36199, 1900},
    {34, 36200, 36349, 2010},
    {35, 36350, 36949, 1850},
    {36, 36950, 37549, 1930},
    {37, 37550, 37749, 1910},
    {38, 37750, 38249, 2570},
    {39, 38250, 38649, 1880},
    {40, 38650, 39649, 2300},
    {41, 39650, 41589, 2496},
    {42, 41590, 43589, 3400},
    {43, 43590, 45589, 3600},
    {44, 45590, 46589, 703},
    {45, 46590, 46789, 1447},
    {46, 46790, 54539, 5150},
    {47, 54540, 55239, 5855},
    {48, 55240, 56739, 3550},
    {49, 56740, 58239, 3550},
    {50, 58240, 59089, 1432},
    {51, 59090, 59139, 1427},
    {52, 59140, 60139, 3300},
    {65, 65536, 66435, 2110},
    {66, 66436, 67335, 2110},
    {67, 67336, 67535, 738},
    {68, 67536, 67835, 753},
    {69, 67836, 68335, 2570},
    {70, 68336, 68585, 1995},
    {71, 68586, 68935, 617},
    {72, 68936, 68985, 461},
    {73, 68986, 69035, 460},
    {74, 69036, 69465, 1475},
    {75, 69466, 70315, 1432},
    {76, 70316, 70365, 1427},
    {85, 70366, 70545, 728},
};
