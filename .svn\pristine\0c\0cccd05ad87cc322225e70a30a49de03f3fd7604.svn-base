//*****************************************************************************
//  File: manage.h
//  实现manager的各种对外业务
//  Data: 2016.7.11
//*****************************************************************************

#ifndef __WT_MANAGE_H__
#define __WT_MANAGE_H__

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include "wtev++.h"
#include "socket.h"
#include "protocol.h"
#include "upgrade.h"

class Manager
{
public:
    Manager(int SocketFd, const wtev::loop_ref &Loop)
        : m_SocketFd(SocketFd), m_<PERSON><PERSON><PERSON><PERSON>(Loop), m_<PERSON><PERSON><PERSON><PERSON>(Loop), m_<PERSON><PERSON><PERSON><PERSON><PERSON>er(Loop), m_WRSocket(SocketFd), m_BroadcastIO(Loop)
    {
    };
    ~Manager();
    //*****************************************************************************
    // 函数: start()
    // 功能: 启动事件循环函数
    // 返回值：无
    //*****************************************************************************
    void Start(void);

    time_t GetBootTime(void) {return BootTimeTicks;};
    void SetBootTime(time_t TimeTicks) { BootTimeTicks = TimeTicks;};

private:
    // 函数: LinkMsgCb()
    // 功能: WT-Link socket回掉函数
    // 参数 [IN]：Watcher : 监听unix socket的watcher
    // 参数 [IN]: Revents :
    // 返回值：无
    //*****************************************************************************
    void LinkMsgCb(wtev::io &Watcher, int Revents);

    //*****************************************************************************
    // 函数: ProcLinkData()
    // 功能：收到新的连接，进行相应的处理
    // 参数[IN] : Sock : 连接的socket
    //            Buf : 通讯数据内容
    //            Len : Buf大小
    // 返回值: 成功或者错误码
    //*****************************************************************************
    int ProcLinkData(WRSocket &Sock, void *Buf, int Len);

    //*****************************************************************************
    // 函数: CommDataHandlerCb()
    // 功能: 监听与外部通讯的回掉函数
    // 参数 [IN]：Watcher : watcher
    // 参数 [IN]: Revents :
    // 返回值：无
    //*****************************************************************************
    void CommDataHandlerCb(wtev::io &Watcher, int Revents);

    //*****************************************************************************
    // 函数: AcceptLink()
    // 功能：收到新的连接，进行相应的处理
    // 参数[IN] : fd : 连接的socket描述符
    //           type : 连接类型，管理连接
    //           SID  : 连接序列号
    // 返回值: 无
    //*****************************************************************************
    void AcceptLink(int Fd, int Type, int SID);

    //*****************************************************************************
    // 函数: CloseLink()
    // 功能：关闭连接请求
    // 参数[IN] : fd : 连接的socket描述符
    //           type : 连接类型，管理连接
    //           SID  : 连接序列号
    // 返回值: 无
    //*****************************************************************************
    void CloseLink(int Fd, int Type, int SID);

    //*****************************************************************************
    // 函数: StartBroadcaseServer()
    // 功能: 启动udp广播服务
    // 返回值：>0 表示启动成功；否则失败
    //*****************************************************************************
    int StartBroadcaseServer(void);

    //*****************************************************************************
    // 函数: BroadcaseIOCb()
    // 功能: 局域网内manage scan后广播的回掉函数
    // 参数 [IN]：Watcher : watcher
    // 参数 [IN]: Revents :
    // 返回值：无
    //*****************************************************************************
    void BroadcaseIOCb(wtev::io &Watcher, int Revents);

    bool IsExistCachedCmd(WRSocket &Sock);

    //用于manager与外部通讯链接读取数据事件需要使用的结构体
    struct CommData
    {
        int Cmd;              //命令字，见WT_LINK_CMD
        int Type;             //连接类型，见WT_LINK_TYPE
        int SID;              //连接序列号
        std::string PeerIp;   //连接伙伴信息
        std::string PeerPort;   //连接伙伴信息
        WRSocket WrSock;      //与client通讯的socket对象

        CommData(int Cmd, int Type, int SID, std::string Ip, std::string Port, int Fd)
            : Cmd(Cmd), Type(Type), SID(SID), PeerIp(Ip), PeerPort(Port), WrSock(Fd) {}
    };

    struct PeerInfo
    {
        int Valid;
        char Ip[16];
        int Port;
    };

private:
    int m_SocketFd;                         //与WT-Link通信所用的socket fd
    wtev::loop_ref m_EvLoop;                // ev loop
    wtev::io m_LinkWatcher;                 //针对外部连接的watcher
    wtev::io m_ManagerCommWatcher;          //manager 与外部通讯的watcher
    WRSocket m_WRSocket;                    //socket读写类
    wtev::io m_BroadcastIO;                 //udp获取发送广播的IO watcher
    int m_BroadcastSockFd;                  //广播服务的socket
    struct sockaddr_in m_BroadcastAddr;     //广播地址
    struct sockaddr_in m_ClientAddr;        //保存发送scan的客户端地址
    std::unique_ptr<CommData> m_Data;       //用于与client通讯的结构体
    time_t BootTimeTicks;                   //保存开机时间~
};

#endif // !_WT_MANAGE_H__
