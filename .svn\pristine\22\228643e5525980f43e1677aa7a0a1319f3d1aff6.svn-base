#pragma once
#include <vector>
#include <string>
#include <memory>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <fstream>
#include <iostream>
#include <windows.h>
#include "tools.h"
#include "wterrorAll.h"
#include "UITypeDef.h"
#include "mac_frame.h"
using namespace std;

using base_control_frame_s = struct base_control_frame_t
{
	//控制模式-控制帧头
	u8 frameControl[2];
	//持续时间
	u8 duration[2];
	//接收MAC
	u8 rxAddress[6];
	//发送MAX
	u8 txAddress[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
};

//Request to Send (RTS) 
using CF_RTS = struct cf_rts_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xb4,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//发送地址
	u8 txAddress[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//Clear to Send (CTS)
using CF_CTS = struct cf_cts_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xc4,0x00 };
	//持续时间
	u8 duration[2];
	//接收MAC
	u8 rxAddress[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//Acknowledgment (ACK)
using CF_ACK = struct cf_ack_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xd4,0x00 };
	//持续时间
	u8 duration[2];
	//接收MAC
	u8 rxAddress[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//Power-Save Poll (PS-Poll)
//using CF_PSPoll = struct cf_PSPoll_t
//{
//	//控制模式-控制帧头
//	u8 frameControl[2] = { 0xa4,0x00 };
//	//Association ID (AID) 连接识别码
//	u8 aid[2];
//	//Address 1: BSSID
//	u8 bssid[6];
//	//Address 2: Transmitter Address
//	u8 txAddress[6];
//	//FCS帧校验[调用时，不对调用方显示]
//	u8 frameCheckSeq[4];
//	//字节向量
//	std::vector<u8> data;
//};

using CF_PSPoll = struct cf_PSPoll_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//ID
	u8 id[2];
	//BSSID(RA)接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BeamformingReportPoll = struct cf_BeamformingReportPoll_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//接收地址RA
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Feedback Segment Retransmission Bitmap 1byte
	u8 feedbackSegmentBitmap[1];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_ControlWrapper = struct cf_ControlWrapper_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//Address1
	u8 address1[6];
	//Carried Frame Control
	u8 carriedFrameControl[2];
	//HT Control
	u8 htControl[4];
	//Carried Frame Length
	int carriedFrameLen;
	//Carried Frame variable
	u8 carriedFrame[MAX_DATA_LEN];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//免竞争期间结束（CF-End）
using CF_CFEnd = struct cf_CFEnd_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xf4,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//接收MAC 0xff 0xff 0xff 0xff 0xff 0xff (广播地址)
	u8 rxAddress[6] = { 0xff,0xff,0xff,0xff,0xff,0xff };
	//Address 2: Transmitter Address
	u8 bssid[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//免竞争期间结束+免竞争期间回应(CF-End + CF-Ack)
using CF_CFEnd_CFAck = struct cf_CFEnd_CFAck_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xf4,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//接收MAC 0xff 0xff 0xff 0xff 0xff 0xff (广播地址)
	u8 rxAddress[6] = { 0xff,0xff,0xff,0xff,0xff,0xff };
	//Address 2: Transmitter Address
	u8 bssid[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//免竞争期间结束+免竞争期间回应(BlockAck frame)
using CF_BlockAck_Basic = struct cf_BlockAck_Basic_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//Block Ack Bitmap
	u8 Bitmap[128];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAckReq_Basic = struct cf_BlockAckReq_Basic_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};


using CF_BlockAck_Compressed = struct cf_BlockAck_Compressed_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//Bitmap length
	int bitmapLen = 8;
	//Block Ack Bitmap
	u8 Bitmap[128];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAckReq_Compressed = struct cf_BlockAckReq_Compressed_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAck_TID = struct cf_BlockAck_TID_t
{
	//Per TID Info
	u8 TIDInfo[2];
	//MultiSTA增加标志位，判断BA_StartSeqCtrl是否存在
	bool bStartSeqCtrlExist;
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//Bitmap length
	int bitmapLen = 8;
	//Block Ack Bitmap
	u8 Bitmap[128];

	//类型AID11值等于2045
	bool AID11Is2045Type;
	u8 Reserved[4];//仅2045使用
	u8 RA[6];      //仅2045使用
};

using CF_VHT_STAInfo = struct cf_VHT_STAInfo_t
{
	//Station Info
	u8 staInfo[2];
};

using CF_HE_STAInfo = struct cf_HE_STAInfo_t
{
	//Station Info
	u8 staInfo[4];
};

using CF_BlockAckReq_TID = struct cf_BlockAckReq_TID_t
{
	//Per TID Info
	u8 TIDInfo[2];
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];

};

using CF_BlockAck_MultiTID = struct cf_BlockAck_MultiTID_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//Multi TID length
	int len;
	//BA Infomation field
	CF_BlockAck_TID multiTID[MAX_TID_COUNT];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAck_MultiSTA = struct cf_BlockAck_MultiSTA_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];

	//Multi TID length
	int len;
	//BA Infomation field
	CF_BlockAck_TID multiTID[MAX_TID_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAckReq_MultiTID = struct cf_BlockAckReq_MultiTID_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//Multi TID length
	int len;
	//BA Infomation field
	CF_BlockAckReq_TID multiTID[MAX_TID_COUNT];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAck_ExtendedCompressed = struct cf_BlockAck_ExtendedCompressed_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//Block Ack Bitmap
	u8 Bitmap[8];
	//RBUFCAP
	u8 RBUFCAP[1];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAckReq_ExtendedCompressed = struct cf_BlockAckReq_ExtendedCompressed_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAck_GCR = struct cf_BlockAck_GCR_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//GCR Group Address
	u8 GCRAddress[6];
	//Block Ack Bitmap
	u8 Bitmap[8];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAckReq_GCR = struct cf_BlockAckReq_GCR_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//GCR Group Address
	u8 GCRAddress[6];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAckReq_GLK_GCR = struct cf_BlockAckReq_GLK_GCR_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	////GCR Group Address
	//u8 GCRAddress[6];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_BlockAck_GLK_GCR = struct cf_BlockAck_GLK_GCR_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];
	//BA Infomation field
	//Block Ack Starting Sequence Control
	u8 startSeqCtrl[2];
	//GCR Group Address
	//u8 GCRAddress[6];
	//Block Ack Bitmap
	u8 Bitmap[8];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};


using CF_BlockAck = struct cf_BlockAck_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6] = { 0xff,0xff,0xff,0xff,0xff,0xff };
	//Transmitter Address
	u8 txAddress[6];
	//BAR Control
	u8 BlockAck_BarControl[2];

	//Variable,BAR Infomaration
	u8 barInfo[4096];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_VHT_NDP_Announcement = struct cf_VHT_NDP_Announcement_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Sounding Dialog Token
	u8 SoundingToken[1];
	//Station Number
	int len;
	//STAInfoN
	CF_VHT_STAInfo staInfo[MAX_STA_INFO_COUNT];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_HE_NDP_Announcement = struct cf_HE_NDP_Announcement_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Sounding Dialog Token
	u8 SoundingToken[1];
	//Station Number
	int len;
	//STAInfoN
	CF_HE_STAInfo staInfo[MAX_STA_INFO_COUNT];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_EHT_NDP_Announcement = struct cf_EHT_NDP_Announcement_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Sounding Dialog Token
	u8 SoundingToken[1];
	//Station Number
	int len;
	//STAInfoN HE和ETH共用，都是4个byte
	CF_HE_STAInfo staInfo[MAX_STA_INFO_COUNT];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_Poll = struct cf_Ext_Poll_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Response offset
	u8 Offset[2];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_SPR = struct cf_Ext_SPR_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Dynamic Allocation Info
	u8 DynAllocInfo[5];
	//BFControl
	u8 bfControl[2];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};
using CF_Ext_Grant = struct cf_Ext_Grant_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Dynamic Allocation Info
	u8 DynAllocInfo[5];
	//BFControl
	u8 bfControl[2];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;

};

using CF_Ext_DMG_CTS = struct cf_Ext_DMG_CTS_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_DMG_DTS = struct cf_Ext_DMG_DTS_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//NAV-SA
	u8 navSAAddress[6];
	//NAV-DA
	u8 navDAAddress[6];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_SSW = struct cf_Ext_SSW_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//SSW Sector sweep
	u8 SSW[3];
	//SSW Feedback
	u8 SSWFeedback[3];//Bug 9976 (6->3(

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_Grant_Ack = struct cf_Ext_Grant_Ack_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Reserved
	u8 Reserved[5];
	//BFControl
	u8 bfControl[2];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_SSW_Feedback = struct cf_Ext_SSW_Feedback_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//SSW Feedback
	u8 SSWFeedback[3];
	//BRP Request
	u8 BRPRequest[4];
	//Beamformed Link Maintenance
	u8 BeamLinkMaintenance[1];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_Ext_SSW_Ack = struct cf_Ext_SSW_Ack_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//SSW Feedback
	u8 SSWFeedback[3];
	//BRP Request
	u8 BRPRequest[4];
	//Beamformed Link Maintenance
	u8 BeamLinkMaintenance[1];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//TACK
using CF_TACK = struct cf_TACK_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xf4,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Beacon Sequence
	u8 beaconSeq[1];
	//Pentapartial Timestamp
	u8 timestamp[5];
	//Next TWT Info/Suspend Duration(optional) 0 or 6bytes
	u8 nextTWTInfoOrSuspendDuration[6];
	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//Control_Reserved
using CF_Reserved = struct cf_Reserved_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xf4,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//UserDefineLen
	int userDefineLen;
	//ReservedUserDefineContent
	u8 userDefine[MAX_MAC_FRAME_COUNT];
	

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//Control_Trigger_Reserved
using CF_Trigger_Reserved = struct cf_Trigger_Reserved_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0xf4,0x00 };
	//持续时间
	u8 duration[2] = { 0x00,0x00 };
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//TriggerType 前4bit
	//u8 triggerType[1];
	//Common Info
	u8 CommonInfo[8];//8 or more
	//UserDefineLen
	int userDefineLen;
	//ReservedUserDefineContent
	u8 userDefine[MAX_MAC_FRAME_COUNT];


	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF = struct cf_TF_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];//8 or more
	//User Info List variable
	u8 UserInfoList[1];
	//Padding variable
	u8 Padding[1];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_Basic = struct cf_TF_Basic_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];
    //User Info List variable
	int nUserInfoListLen=0;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_BFRP = struct cf_TF_BFRP_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];
	//User Info List variable
	int nUserInfoListLen = 0;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_MUBar = struct cf_TF_MUBar_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];
	//User Info List variable
	int nUserInfoListLen = 0;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_MURTS = struct cf_TF_MURTS_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];
	//User Info List variable
	int nUserInfoListLen = 0;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_BSRP = struct cf_TF_BSRP_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8]; 
	//User Info List variable
	int nUserInfoListLen;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_GCR_MUBar = struct cf_TF_GCR_MUBar_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[12];  
	//User Info List variable
	int nUserInfoListLen;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_BQRP = struct cf_TF_BQRP_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];
	//User Info List variable
	int nUserInfoListLen;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

using CF_TF_NFRP = struct cf_TF_NFRP_t
{
	//控制模式-控制帧头
	u8 frameControl[2] = { 0x94,0x00 };
	//持续时间
	u8 duration[2];
	//接收地址
	u8 rxAddress[6];
	//Transmitter Address
	u8 txAddress[6];
	//Common Info
	u8 CommonInfo[8];
	//User Info List variable
	int nUserInfoListLen;
	u8 UserInfoList[MAX_MAC_FRAME_COUNT];
	//Padding variable
	int nPaddingLen = 0;
	u8 Padding[MAX_PADDING_COUNT];

	//FCS帧校验[调用时，不对调用方显示]
	u8 frameCheckSeq[4];
	//字节向量
	std::vector<u8> data;
};

//Control Frame Generator
void ctrl_frame();
int Generator_RTS_Frame(CF_RTS* rts);
int Generator_CTS_Frame(CF_CTS* frm);
int Generator_ACK_Frame(CF_ACK* frm);
int Generator_CFEnd_Frame(CF_CFEnd* frm);
int Generator_CFEnd_CFAck_Frame(CF_CFEnd_CFAck* frm);
int Generator_VHT_NDP_Announcement_Frame(CF_VHT_NDP_Announcement* frm);
int Generator_HE_NDP_Announcement_Frame(CF_HE_NDP_Announcement* frm);
int Generator_PSPoll_Frame(CF_PSPoll* frm);
int Generator_BeamformingReportPoll_Frame(CF_BeamformingReportPoll* frm);
int Generator_ControlWrapper_Frame(CF_ControlWrapper* frm);
int Generator_TACK_Frame(CF_TACK* frm);
int Generator_CF_Reserved_Frame(CF_Reserved* frm);

//BlockAck
int Generator_BlockAck_Frame(CF_BlockAck* frm);
int Generator_BlockAck_Basic_Frame(CF_BlockAck_Basic* frm);
int Generator_BlockAck_Compressed_Frame(CF_BlockAck_Compressed* frm);
int Generator_BlockAck_MultiTID_Frame(CF_BlockAck_MultiTID* frm);
int Generator_BlockAck_MultiSTA_Frame(CF_BlockAck_MultiSTA* frm);//ax add MultiSTA
int Generator_BlockAck_ExtendedCompressed_Frame(CF_BlockAck_ExtendedCompressed* frm);
int Generator_BlockAck_GCR_Frame(CF_BlockAck_GCR* frm);
int Generator_BlockAck_GLK_GCR_Frame(CF_BlockAck_GLK_GCR* frm);
//BlockAckReq
int Generator_BlockAckReq_Basic_Frame(CF_BlockAckReq_Basic* frm);
int Generator_BlockAckReq_Compressed_Frame(CF_BlockAckReq_Compressed* frm);
int Generator_BlockAckReq_MultiTID_Frame(CF_BlockAckReq_MultiTID* frm);
int Generator_BlockAckReq_ExtendedCompressed_Frame(CF_BlockAckReq_ExtendedCompressed* frm);
int Generator_BlockAckReq_GCR_Frame(CF_BlockAckReq_GCR* frm);
int Generator_BlockAckReq_GLK_GCR_Frame(CF_BlockAckReq_GLK_GCR* frm);
//Control Frame Extension
int Generator_Ext_Poll_Frame(CF_Ext_Poll* frm);
int Generator_Ext_SPR_Frame(CF_Ext_SPR* frm);
int Generator_Ext_Grant_Frame(CF_Ext_Grant* frm);
int Generator_Ext_DMG_CTS_Frame(CF_Ext_DMG_CTS* frm);
int Generator_Ext_DMG_DTS_Frame(CF_Ext_DMG_DTS* frm);
int Generator_Ext_SSW_Frame(CF_Ext_SSW* frm);
int Generator_Ext_SSW_Feedback_Frame(CF_Ext_SSW_Feedback* frm);
int Generator_Ext_SSW_Ack_Frame(CF_Ext_SSW_Ack* frm);
int Generator_Ext_Grant_Ack_Frame(CF_Ext_Grant_Ack* frm);

//Trigger Frame
int Generator_TF_Basic_Frame(CF_TF_Basic* frm);
int Generator_TF_BFRP_Frame(CF_TF_BFRP* frm);
int Generator_TF_MUBar_Frame(CF_TF_MUBar* frm);
int Generator_TF_MURTS_Frame(CF_TF_MURTS* frm);
int Generator_TF_BSRP_Frame(CF_TF_BSRP* frm);
int Generator_TF_GCR_MUBar_Frame(CF_TF_GCR_MUBar* frm);
int Generator_TF_BQRP_Frame(CF_TF_BQRP* frm);
int Generator_TF_NFRP_Frame(CF_TF_NFRP* frm);
int Generator_TF_Reserved_Frame(CF_Trigger_Reserved* frm);

