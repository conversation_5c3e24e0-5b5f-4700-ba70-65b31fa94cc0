
#include <string>
#include <iostream>
#include <jsoncpp/json/json.h>

#include "basehead.h"
#include "commonhandler.h"
#include "tester.h"
#include <fstream>
#include "scpi_wavegenerator.h"
#include "scpi_gen_wifi_psdu.h"
#include "vsahandler.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester_mt.h"
#include <sys/time.h>
#include "scpi_gen_bt.h"
#include "scpi_3gpp_gen_lte.h"
#include "wtlog.h"
#include "cellular_wavegen_default_param.h"
#include "scpi_3gpp_common.h"

using namespace cellular::method;
using CommandVector = std::vector<cellular::common::Command>;

#define DEBUG_SHOW_LTE (1)

namespace
{
    inline Alg_4G_WaveGenType &GetLTE(scpi_t *context)
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        return attr->Pn3GPP->LTE;
    }
}

static int BinaryFind(int array[], int len, int target)
{
    int left = 0;
    int right = len - 1;
    while (left <= right)
    {
        int mid = (left + right) / 2;

        if (array[mid] == target)
        {
            return mid;
        }
        else if (array[mid] > target)
        {
            right = mid - 1;
        }
        else
        {
            left = mid + 1;
        }
    }

    return -1;
}

scpi_result_t SCPI_LTE_QueryLTEVersion(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    const char *Ver = "Release 15"; // TODO

    SCPI_ResultText(context, Ver);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set3GPPWaveGenFlatnessFactor(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = cellular::wavegen::SetWavegenValueInRange(context,
                                                         attr->Pn3GPP->CommonParam.FlatFactor,
                                                         -3.0,
                                                         3.0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set3GPPWaveGenPhaseNoiseFlag(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = cellular::wavegen::SetWavegenValueInRange(context,
                                                         attr->Pn3GPP->CommonParam.PhaseNoiseFlg,
                                                         0,
                                                         1);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set3GPPWaveGenPhaseNoiseFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        CommandVector params{{0, static_cast<int>(arraySize(attr->Pn3GPP->CommonParam.PhaseNoiseFactor))}};
        iRet = GetScpiCommandNumbers(context, params);
        IF_BREAK(iRet);

        int idx = params.at(0).value_;
        iRet = cellular::wavegen::SetWavegenValueInRange(context,
                                                         attr->Pn3GPP->CommonParam.PhaseNoiseFactor[idx],
                                                         -200.0,
                                                         0.0);
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set3GPPWaveGenFunctionType(scpi_t *context)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int iRet = cellular::wavegen::SetWavegenValueInRange(context,
                                                         attr->Pn3GPP->CommonParam.subType,
                                                         0,
                                                         1);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// 基础部分
//**************************************************************************************************
scpi_result_t SCPI_LTE_SetLinkDirect(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = ALG_3GPP_UL;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_UL && Value != ALG_3GPP_DL)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.LinkDirect = Value;

        cellular::wavegen::SetCellularWaveGenDefaultParam(ALG_3GPP_STD_4G, Value, attr->Pn3GPP.get());
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// General
//**************************************************************************************************

scpi_result_t SCPI_LTE_SetGenWaveMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.GenWaveMode = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetGenWaveNo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        
        if(attr->Pn3GPP->LTE.General.GenWaveMode == 1 && (Value < 0 || Value > 9))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        else if(attr->Pn3GPP->LTE.General.GenWaveMode == 2 && (Value < 0 || Value > 19))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.General.GenWaveNo = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetGenWaveSequenceLen(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 16)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.GenSequenceLen = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// Filter
//**************************************************************************************************
scpi_result_t SCPI_LTE_SetFilterType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.Type = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterOverFs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1920000 && Value != 3840000 && Value != 7680000 && 
            Value != 15360000 && Value != 23040000 && Value != 30720000)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.Fs = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterMaxOrder(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 512 && Value != 1024 && Value != 2048)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.MaxOrder = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterFpassFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 1.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.FpassFactor = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterFstopFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 1.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < attr->Pn3GPP->LTE.General.Filter.FpassFactor)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.General.Filter.FstopFactor = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterPassRipple(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 0.3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.PassRipple = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterStopAtten(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.StopAtten = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterOptimization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.Optimization = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterRollOffFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.0 || Value > 1.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.RollOffFactor = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterCutOffFrqFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.02 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.CutOffFrqFactor = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterCutOffFreqShift(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -1.0 || Value > 1.0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.CutOffFreqShift = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetFilterSmoothFactor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0.1 || Value > 20)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.General.Filter.SmoothFactor = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}
//**************************************************************************************************
// CELL
//**************************************************************************************************
scpi_result_t SCPI_LTE_SetCarrierAggregation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.CarrAggrState : attr->Pn3GPP->LTE.DL.MultiCell.CarrAggrState) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != CellID)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].CellIdx : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].CellIdx) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].State : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].State) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellCid(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 503)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].PhyCellID : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].PhyCellID) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellBW(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    scpi_number_t rate;
    int CellID = 0;
    int BW = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamNumber(context, nullptr, &rate, true))
        {
            return SCPI_RES_ERR;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        BW = rate.value;
        switch (BW)
        {
        case 1400000:
        case 3000000:
        case 5000000:
        case 10000000:
        case 15000000:
        case 20000000:
            (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].ChannelBW : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].ChannelBW) = BW;
            break;
        default:
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        if(iRet != WT_ERR_CODE_OK)
        {
            break;
        }

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << rate.value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDuolexing(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != ALG_3GPP_FDD && Value != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].Duplexing : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].Duplexing) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetULDLConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].ULDLConfig : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].ULDLConfig) = Value;

        for (int i = 0; i < ALG_4G_MAX_SUBFRAME_NUM; ++i) //更新生成配置默认第一个有效子帧为ON。注意配置用户子帧要在这之后配置。
        {
            if(UL_DL_CONFIG_MAP[Value][i]^attr->Pn3GPP->LTE.LinkDirect)
            {
                attr->Pn3GPP->LTE.UL.Chan[i].State = 0;
                continue;
            }
            else
            {
                attr->Pn3GPP->LTE.UL.Chan[i].State = 1;
                break;
            }
        }

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSpecialSubframeConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].Duplexing != ALG_3GPP_TDD)
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].SpecialSubfrmCfg : attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].SpecialSubfrmCfg) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellDLPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    int CellID = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].Power = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellDLPdschStart(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].PdschStart = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellDLPhichResource(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].PhichResource = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCellDLPhichDuration(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.Cell[CellID].PhichDuration = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSyncTxAntenna(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.SyncTxAntenna = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetPsyncPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.PsyncPower = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSsyncPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.SsyncPower = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetRefSignalPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.RefSignalPower = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetPdschPB(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.PdschPB = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetPbchRatioRho(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -10 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.PbchRatioRho = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetPdcchRatioRho(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -10 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.PdcchRatioRho = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetTxAntennaNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.MultiCell.TxAntennaNum = Value;
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetN1Dmrs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;
    bool Match = false;
    static int Choice[] = {0, 2, 3, 4, 6, 8, 9, 10};

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        for (int i = 0; i < (sizeof(Choice) / sizeof(Choice[0])); ++i)
        {
            if (Choice[i] == Value)
            {
                Match = true;
                break;
            }
        }

        if (!Match)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].N1Dmrs = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetCyclicPrefix(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.MultiCell.CyclicPrefix : attr->Pn3GPP->LTE.DL.MultiCell.CyclicPrefix) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetULGroupHop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.MultiCell.GroupHop = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetULSequenceHop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.MultiCell.SequenceHop = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetULDeltaSeqShift(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 29)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.MultiCell.DeltaSeqShift = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLPdschScheduling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.PdschScheduling = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// UE
//**************************************************************************************************
scpi_result_t SCPI_LTE_SetUserID(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 65535)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        (attr->Pn3GPP->LTE.LinkDirect == ALG_3GPP_UL ? attr->Pn3GPP->LTE.UL.Ue.UeID : attr->Pn3GPP->LTE.DL.Ue.UeID) = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserScramble(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.Scramble = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserChanCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.ChanCodingState = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserAPMapping(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.ApMapping = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserUECategory(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 1 || Value > 12)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.UECategory = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_LTE_SetUserCodebookIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.CodebookIndex = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.DataType = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserInitialization(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.DL.Ue.DataType)
        {
        case 0: // PN9
            if (Value < 0 || Value > 0x1FF)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        case 1: // PN11
            if (Value < 0 || Value > 0x7FF)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        case 2: // PN15
            if (Value < 0 || Value > 0x7FFF)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        case 3: // PN23
            if (Value < 0 || Value > 0x7FFFFF)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        default:
            {
                // iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
        }
        if (iRet != WT_ERR_CODE_OK)
        {
            break;
        }

        attr->Pn3GPP->LTE.DL.Ue.Initialization = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserPdschPA(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0;
    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
        for (double value : DL_UE_PDSCHPA_MAP)
        {
            if (CompareDouble(value, Value, 1e-3) == 0)
            {
                iRet = WT_ERR_CODE_OK;
                break;
            }
        }
        if (iRet == WT_ERR_CODE_PARAMETER_MISMATCH)
        {
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.PdschPA = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserMcsTable(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.McsTable = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserTransmitMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 1 || Value > 9)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.TxMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserTBSTalternativeIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Ue.TbsIndexAlt = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSchedulePattern(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        // SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        // attr->Pn3GPP->LTE.DL.Schedule.SchedPattern = Value;
#if DEBUG_SHOW_LTE
        cout << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSchedulePdsch(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int nFrmIdx = attr->Pn3GPP->LTE.General.GenSequenceLen;
        if (nFrmIdx < 1 || nFrmIdx > ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int arr[nFrmIdx][ALG_4G_MAX_SUBFRAME_NUM] = {0};
        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                if (!SCPI_ParamInt(context, &arr[i][j], true))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }

                if (arr[i][j] < 0 || arr[i][j] > 1)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }
        }

        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                attr->Pn3GPP->LTE.DL.Schedule.SchedPdsch[i][j] = arr[i][j];
            }
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetScheduleHarqProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int nFrmIdx = attr->Pn3GPP->LTE.General.GenSequenceLen;
        if (nFrmIdx < 1 || nFrmIdx > ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int arr[nFrmIdx][ALG_4G_MAX_SUBFRAME_NUM] = {0};
        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                if (!SCPI_ParamInt(context, &arr[i][j], true))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }

                if (arr[i][j] < 0 || arr[i][j] > 14)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }
        }

        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                attr->Pn3GPP->LTE.DL.Schedule.HarqProcNum[i][j] = arr[i][j];
            }
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetScheduleNewDataIndic(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int nFrmIdx = attr->Pn3GPP->LTE.General.GenSequenceLen;
        if (nFrmIdx < 1 || nFrmIdx > ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int arr[nFrmIdx][ALG_4G_MAX_SUBFRAME_NUM] = {0};
        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                if (!SCPI_ParamInt(context, &arr[i][j], true))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }

                if (arr[i][j] < 0 || arr[i][j] > 1)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }
        }

        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                attr->Pn3GPP->LTE.DL.Schedule.NewDataInd[i][j] = arr[i][j];
            }
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetScheduleMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int nFrmIdx = attr->Pn3GPP->LTE.General.GenSequenceLen;
        if (nFrmIdx < 1 || nFrmIdx > ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int arr[nFrmIdx][ALG_4G_MAX_SUBFRAME_NUM] = {0};
        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                if (!SCPI_ParamInt(context, &arr[i][j], true))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }

                if (arr[i][j] < 0 || arr[i][j] > 58)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }
        }

        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                attr->Pn3GPP->LTE.DL.Schedule.MCS[i][j] = arr[i][j];
            }
        }
    } while (0);
    return SCPI_ResultOK(context, iRet);
}


scpi_result_t SCPI_LTE_SetDLSchedPusch(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int nFrmIdx = attr->Pn3GPP->LTE.General.GenSequenceLen;
        if (nFrmIdx < 1 || nFrmIdx > ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        int arr[nFrmIdx][ALG_4G_MAX_SUBFRAME_NUM] = {0};
        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                if (!SCPI_ParamInt(context, &arr[i][j], true))
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }

                if (arr[i][j] < 0 || arr[i][j] > 1)
                {
                    iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                    break;
                }
            }
        }

        for (int i = 0; i < nFrmIdx; ++i)
        {
            for (int j = 0; j < ALG_4G_MAX_SUBFRAME_NUM; ++j)
            {
                attr->Pn3GPP->LTE.DL.Schedule.SchedPusch[i][j] = arr[i][j];
            }
        }
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedNrPdcchSymbNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.NrSubfrm.PDCCHSymNum = Value;
    } while (0);*/

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedNrDLDciMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 58)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.NrSubfrm.MCS = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLNrDLDciPdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.NrSubfrm.DLFormat = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLNrDLDciCceIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.NrSubfrm.DLCCEIdx = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedNrULDciPdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.NrSubfrm.ULFormat = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedNrULDciCceIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.NrSubfrm.ULCCEIdx = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedSpPdcchSymbNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 1 || Value > 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.SpSubfrm.PDCCHSymNum = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedSpDLDciMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 58)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.SpSubfrm.MCS = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSpDLDciPdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.SpSubfrm.DLFormat = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSpDLDciCceIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.SpSubfrm.DLCCEIdx = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedSpULDciPdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.SpSubfrm.ULFormat = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetDLSchedSpULDciCceIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;

    /*do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int Value = 0;
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Schedule.SpSubfrm.ULCCEIdx = Value;
    } while (0);*/
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.0;

    do
    {
        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.UePower = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.UseMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserPushData(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].Scramble == 1 && (Value < 0 || Value > 6))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        else if(attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].Scramble == 0 && (Value < 0 || Value > 3))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].DataType = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserPushPattern(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    uint32_t Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamUnsignedInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].DataType)
        {
        case 0: // PN9
            if (Value > 0x1FE)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        case 1: // PN11
            if (Value > 0x7FE)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        case 2: // PN15
            if (Value > 0x7FFE)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        case 3: // PN23
            if (Value > 0x7FFFFE)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
            break;
        default:
            {
                // iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            }
        }
        if (iRet != WT_ERR_CODE_OK)
        {
            break;
        }

        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].Initialization = (Value & 0x7FFFFF);
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserPushTxMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].TxMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserPushNAPort(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].TxMode == 0)
        {
            if (Value != 1)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }
        else if (attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].TxMode == 1)
        {
            if (Value != 2 && Value != 4)
            {
                iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
                break;
            }
        }

        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].MaxNumAP = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserScramblingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].Scramble = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserCCodingState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].ChanCodingState = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserCCodingMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].ChanCodingMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetUserEnable256QAM(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellID = 0;

    do
    {
        SCPI_CommandNumbers(context, &CellID, 1);
        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Ue.Pusch[CellID].Enable256QAM = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

//**************************************************************************************************
// FRAME
//**************************************************************************************************
scpi_result_t SCPI_LTE_SetSubfUserPucchConfCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.CchSfCfgNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschConfCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.SchSfCfgNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int SubFrame = 0;

    do
    {
        if (!SCPI_CommandNumbers(context, &SubFrame, 1))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int arbSequenceLen = attr->Pn3GPP->LTE.General.GenSequenceLen;
        if (SubFrame < 0 || SubFrame >= arbSequenceLen * 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.UL.Chan[SubFrame].SubfrmIdx = SubFrame;
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].State = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPucchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pucch.State = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].CellIdx = CellID;
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].State = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschRBSETnum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].RBSetNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschRBCount(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Find = -1;
    int Number[] = {0, 0, 1};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].ChannelBW)
        {
        case 1400000:
            Find = BinaryFind(pusch_pb_1_4, sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]), Value);
            break;
        case 3000000:
            Find = BinaryFind(pusch_pb_3, sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]), Value);
            break;
        case 5000000:
            Find = BinaryFind(pusch_pb_5, sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]), Value);
            break;
        case 10000000:
            Find = BinaryFind(pusch_pb_10, sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]), Value);
            break;
        case 15000000:
            Find = BinaryFind(pusch_pb_15, sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]), Value);
            break;
        case 20000000:
            Find = BinaryFind(pusch_pb_20, sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]), Value);
            break;
        default:
            break;
        }

        if (Find == -1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].RBNum[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif

    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschVRBoffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellRBMax = -1;
    int Number[] = {0, 0, 1};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.UL.MultiCell.Cell[CellID].ChannelBW)
        {
        case 1400000:
            CellRBMax = pusch_pb_1_4[sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]) - 1];
            break;
        case 3000000:
            CellRBMax = pusch_pb_3[sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]) - 1];
            break;
        case 5000000:
            CellRBMax = pusch_pb_5[sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]) - 1];
            break;
        case 10000000:
            CellRBMax = pusch_pb_10[sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]) - 1];
            break;
        case 15000000:
            CellRBMax = pusch_pb_15[sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]) - 1];
            break;
        case 20000000:
            CellRBMax = pusch_pb_20[sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]) - 1];
            break;
        default:
            break;
        }

        WTLog::Instance().WriteLog(LOG_DEBUG, "CellRBMax=%d RBNum=%d Value=%d\n", CellRBMax, attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].RBNum[SetID], Value);
        if (CellRBMax <= 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].RBNum[SetID] + Value > CellRBMax)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].RBOffset[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    double Value = 0.0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamDouble(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Power = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschFhopState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].FreqHopState = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschPrecodingScheme(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Precoding = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschPrecodingNOLayers(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].LayerNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschPrecodingNAPused(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].AntennaNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschCBINdex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].CodebookIdx = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschDrsCYCShift(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].CyclicShiftField = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschCodewords(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Codeword = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserPuschMcsMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[2] = {0, 0};
    int &CellID = Number[0];
    int &SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.McsCfgMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserCwPuschMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.McsCfgMode != 0)
        {
            // 手动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if ((Value < UL_MODULE_MCS_MAP[attr->Pn3GPP->LTE.UL.Ue.Pusch->Enable256QAM][(attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.Modulate[SetID]) / 2][0] 
        || Value > UL_MODULE_MCS_MAP[attr->Pn3GPP->LTE.UL.Ue.Pusch->Enable256QAM][(attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.Modulate[SetID]) / 2][1]))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.Mcs[SetID] = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserCwPuschModulation(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value != 2 && Value != 4 && Value != 6 && Value != 8 && attr->Pn3GPP->LTE.UL.Ue.Pusch->Enable256QAM == 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        else if (Value != 2 && Value != 4 && Value != 6 && Value != 8 && attr->Pn3GPP->LTE.UL.Ue.Pusch->Enable256QAM == 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.Modulate[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserCwPuschCCodingTBsize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 253440)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.McsCfgMode == 0)
        {
            // 自动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.PayloadSize[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfUserCwPuschCCodingRVINdex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &CellID = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (CellID < 0 || CellID >= ALG_4G_MAX_CELL_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.McsCfgMode == 0)
        {
            // 自动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.UL.Chan[SubFrame].Pusch[CellID].Encode.RedunVerIdx[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SubFrame=" << SubFrame << ", CellID=" << CellID << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmCfgNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int arbSequenceLen = attr->Pn3GPP->LTE.General.GenSequenceLen;
        
        if (Value < 0 || Value > arbSequenceLen * 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.SubfrmCfgNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmOcngFlag(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.OCNGFlag = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmDummyModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.DummyModulate = Value;
#if DEBUG_SHOW_LTE
        cout << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmDummyDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.DummyDataType = Value;
#if DEBUG_SHOW_LTE
        cout << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetOCNGFlag(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.OCNGFlag = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPbchState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.State = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPbchScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.Scrambling = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPbchPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.Precoding = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPbchSNFOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1016)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.SFNOffset = Value-(Value%4);
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPbchSpareBit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1023)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for (int i = 0; i < sizeof(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.SpareBit) / sizeof(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.SpareBit[0]); ++i)
        {
            attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PBCH.SpareBit[i] = ((Value >> i) & 0x1u);
        }

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPcfichState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PCFICH.State = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPcfichScrambling(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PCFICH.Scrambling = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPcfichPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PCFICH.Precoding = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPcfichPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PCFICH.Power = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPcfichPDCCHSymNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        int min = 0;
        int max = 0;
        if (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW == 1400000)
        {
            min = 2;
            max = 4;
        }
        else if (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW != 1400000)
        {
            min = 1;
            max = 3;
        }
        if(attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].Duplexing == 1 && UL_DL_CONFIG_MAP[attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ULDLConfig][SubFrame] != ALG_3GPP_DL && UL_DL_CONFIG_MAP[attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ULDLConfig][SubFrame] != ALG_3GPP_UL)
        {
            max = 2;
        }
        if(attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].PhichDuration == 1 && UL_DL_CONFIG_MAP[attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ULDLConfig][SubFrame] == ALG_3GPP_DL)
        {
            min = 3;
        }
        if(attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].PhichDuration == 1 && UL_DL_CONFIG_MAP[attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ULDLConfig][SubFrame] != ALG_3GPP_DL && UL_DL_CONFIG_MAP[attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ULDLConfig][SubFrame] != ALG_3GPP_UL)
        {
            min = 2;
        }
        if((Value < min || Value > max))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PCFICH.PDCCHSymNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPhichPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PHICH.Power = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPhichACKInfo(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PHICH.ACKInfo = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < -2 || Value > -2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.Format = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDummyCCEType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DummyCCEType = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame  << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDataType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DataType = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchPower(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < -80 || Value > 10)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.Power = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchAutoSchedDLDCI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.AutoSchedDLDCI = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchSymbNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 1 || Value > 87)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.PDCCHNum = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < -1 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].State = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLUser(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].User = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 0x11)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].DCIFormat = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLSearchSpace(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].SearchSpace = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ResAllocateHeader(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value != 0 || attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].DCIFormat != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.ResAllocateHeader = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ResBlkAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    int max = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            max = DCI_ResBlkAssign_Format1_1_4_Max;
            break;
        case 3000000:
            max = DCI_ResBlkAssign_Format1_3_Max;
            break;
        case 5000000:
            max = DCI_ResBlkAssign_Format1_5_Max;
            break;
        case 10000000:
            max = DCI_ResBlkAssign_Format1_10_Max;
            break;
        case 15000000:
            max = DCI_ResBlkAssign_Format1_15_Max;
            break;
        case 20000000:
            max = DCI_ResBlkAssign_Format1_20_Max;
            break;
        default:
            break;
        }

        if (Value < -1 || Value > max)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.ResBlkAssign = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1MCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < -1 || Value > 58)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.MCS = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1HarqProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (Value < -1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.HarqProcNum = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1NewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (Value < -1 || Value > 1 || attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].DCIFormat != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.NewDataInd = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1RvIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < -1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.RvIdx = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1TPCCommand(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.TPCCommand = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1DLAssignment(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1.DLAssignment = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.Mode = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AVRBAssignment(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.VRBAssignment = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AResBlkConfig(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];

    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.ResBlkConfig = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARBNumber(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    int max = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            max = PDSCH_PB_1_4_MAX;
            break;
        case 3000000:
            max = PDSCH_PB_3_MAX;
            break;
        case 5000000:
            max = PDSCH_PB_5_MAX;
            break;
        case 10000000:
            max = PDSCH_PB_10_MAX;
            break;
        case 15000000:
            max = PDSCH_PB_15_MAX;
            break;
        case 20000000:
            max = PDSCH_PB_20_MAX;
            break;
        default:
            max = PDSCH_PB_20_MAX;
            break;
        }

        if (Value < 0 || Value > max)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.RBNumber = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < 0 || Value > 99)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.RBOffset = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AResBlkAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    int max = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            max = DCI_ResBlkAssign_Format1A_1_4_Max;
            break;
        case 3000000:
            max = DCI_ResBlkAssign_Format1A_3_Max;
            break;
        case 5000000:
            max = DCI_ResBlkAssign_Format1A_5_Max;
            break;
        case 10000000:
            max = DCI_ResBlkAssign_Format1A_10_Max;
            break;
        case 15000000:
            max = DCI_ResBlkAssign_Format1A_15_Max;
            break;
        case 20000000:
            max = DCI_ResBlkAssign_Format1A_20_Max;
            break;
        default:
            break;
        }
        if (Value < -1 || Value > max)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.ResBlkAssign = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AMCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (Value < -1 || Value > 58)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.MCS = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1AHarqProcNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (Value < -1 || Value > 14)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.HarqProcNum = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ANewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (Value < -1 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.NewDataInd = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ARvIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        if (Value < -1 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.RvIdx = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ATPCCommand(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.TPCCommand = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLFormat1ADLAssignment(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].Format1A.DLAssignment = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLPDCCHFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].PDCCHFormat = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIDLCCEIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[0].CCEIdx = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].State = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULUser(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].User = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULDCIFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].DCIFormat = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULSearchSpace(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].SearchSpace = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0FreqHop(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.FreqHop = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0ResBlkAssign(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    int max = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            max = DCI_ResBlkAssign_Format0_1_4_Max;
            break;
        case 3000000:
            max = DCI_ResBlkAssign_Format0_3_Max;
            break;
        case 5000000:
            max = DCI_ResBlkAssign_Format0_5_Max;
            break;
        case 10000000:
            max = DCI_ResBlkAssign_Format0_10_Max;
            break;
        case 15000000:
            max = DCI_ResBlkAssign_Format0_15_Max;
            break;
        case 20000000:
            max = DCI_ResBlkAssign_Format0_20_Max;
            break;
        default:
            break;
        }

        if (Value < 0 || Value > max)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.ResBlkAssign = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0MCS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 31)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.MCS = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0NewDataInd(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.NewDataInd = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0TPCCommand(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.TPCCommand = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0CyclicShiftForDMRS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 7)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.CyclicShiftForDMRS = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0ULIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.ULIndex = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0DAI(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.DAI = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0CSIResquest(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.CSIResquest = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULFormat0ResAllocateType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].Format0.ResAllocateType = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULPDCCHFormat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].PDCCHFormat = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdcchDCIULCCEIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 100)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDCCH.DCI[1].CCEIdx = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschState(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.State = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschResAllocateType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.ResAllocateType = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschVRBAssignment(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.VRBAssignment = Value;

#if DEBUG_SHOW_LTE
        cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschRBGBitmap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    char text[256] = {0};
    char Buf[256] = {0};
    size_t copyLen = 0;
    int max = 0;
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamCopyText(context, text, sizeof(text) - 1, &copyLen, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.UL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            max = PDSCH_RBGBitmap_1_4_Length;
            break;
        case 3000000:
            max = PDSCH_RBGBitmap_3_Length;
            break;
        case 5000000:
            max = PDSCH_RBGBitmap_5_Length;
            break;
        case 10000000:
            max = PDSCH_RBGBitmap_10_Length;
            break;
        case 15000000:
            max = PDSCH_RBGBitmap_15_Length;
            break;
        case 20000000:
            max = PDSCH_RBGBitmap_20_Length;
            break;
        default:
            break;
        }
        if (copyLen > max)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        for (int i = 0; i < copyLen; i++)
        {
            Buf[i] = text[copyLen - i - 1] - '0';
        }

        memcpy(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.RBGBitmap, Buf, copyLen);

#if DEBUG_SHOW_LTE
        std::cout <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=";
        for (int i = 0; i < copyLen; i++)
        {
            cout << static_cast<int>(Buf[i]);
        }
        std::cout << std::endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschRBNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Find = -1;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            if (PDSCH_PB_MIN <= Value && Value <= PDSCH_PB_1_4_MAX)
            {
                Find = 0;
            }
            break;
        case 3000000:
            if (PDSCH_PB_MIN <= Value && Value <= PDSCH_PB_3_MAX)
            {
                Find = 0;
            }
            break;
        case 5000000:
            if (PDSCH_PB_MIN <= Value && Value <= PDSCH_PB_5_MAX)
            {
                Find = 0;
            }
            break;
        case 10000000:
            if (PDSCH_PB_MIN <= Value && Value <= PDSCH_PB_10_MAX)
            {
                Find = 0;
            }
            break;
        case 15000000:
            if (PDSCH_PB_MIN <= Value && Value <= PDSCH_PB_15_MAX)
            {
                Find = 0;
            }
            break;
        case 20000000:
            if (PDSCH_PB_MIN <= Value && Value <= PDSCH_PB_20_MAX)
            {
                Find = 0;
            }
            break;
        default:
            break;
        }
        if (Find == -1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.RBNum = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschAutoOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.AutoOffset = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschRBOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int CellRBMax = -1;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        switch (attr->Pn3GPP->LTE.UL.MultiCell.Cell[0].ChannelBW)
        {
        case 1400000:
            CellRBMax = pusch_pb_1_4[sizeof(pusch_pb_1_4) / sizeof(pusch_pb_1_4[0]) - 1];
            break;
        case 3000000:
            CellRBMax = pusch_pb_3[sizeof(pusch_pb_3) / sizeof(pusch_pb_3[0]) - 1];
            break;
        case 5000000:
            CellRBMax = pusch_pb_5[sizeof(pusch_pb_5) / sizeof(pusch_pb_5[0]) - 1];
            break;
        case 10000000:
            CellRBMax = pusch_pb_10[sizeof(pusch_pb_10) / sizeof(pusch_pb_10[0]) - 1];
            break;
        case 15000000:
            CellRBMax = pusch_pb_15[sizeof(pusch_pb_15) / sizeof(pusch_pb_15[0]) - 1];
            break;
        case 20000000:
            CellRBMax = pusch_pb_20[sizeof(pusch_pb_20) / sizeof(pusch_pb_20[0]) - 1];
            break;
        default:
            break;
        }
        WTLog::Instance().WriteLog(LOG_DEBUG, "CellRBMax=%d RBNum=%d Value=%d\n", CellRBMax, attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.RBNum, Value);
        if (CellRBMax <= 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.RBNum + Value > CellRBMax)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.RBOffset = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschSymbOffset(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW == 1400000 && (Value < 0 || Value > 4))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        else if (attr->Pn3GPP->LTE.DL.MultiCell.Cell[0].ChannelBW != 1400000 && (Value < 0 || Value > 3))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.SymbOffset = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschPrecoding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Precoding = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschLayerNum(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 2 && Value != 4)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.LayerNum = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCyclicDelayDiversity(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.CyclicDelayDiversity = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCodebookIndex(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 0 || Value > 15)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.CodebookIdx = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwIRConfigMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 0 && Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.IRConfigMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCodeword(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 2)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Codeword = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschMCSConfigMode(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0};
    int& DLFrame = Number[0];
    int& SubFrame = Number[1];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value != 1 && Value != 0)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.MCSConfigMode = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwMcs(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &DLFrame = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        
        if (attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.MCSConfigMode != 0)
        {
            // 手动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        
        if (((Value < DL_MODULE_MCS_MAP[attr->Pn3GPP->LTE.DL.Ue.McsTable][(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Modulate[SetID]) - 1][0] || Value > DL_MODULE_MCS_MAP[attr->Pn3GPP->LTE.DL.Ue.McsTable][(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Modulate[SetID]) - 1][1]))
            && ((Value < DL_MODULE_MCS_MAP[attr->Pn3GPP->LTE.DL.Ue.McsTable][(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Modulate[SetID])][0] || Value > DL_MODULE_MCS_MAP[attr->Pn3GPP->LTE.DL.Ue.McsTable][(attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Modulate[SetID])][1])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Mcs[SetID] = Value;

#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwModulate(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &DLFrame = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
                SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.DL.Ue.McsTable == 1 && Value != 2 && Value != 4 && Value != 6)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        else if (attr->Pn3GPP->LTE.DL.Ue.McsTable >= 2 && attr->Pn3GPP->LTE.DL.Ue.McsTable <= 4 && Value != 2 && Value != 4 && Value != 6 && Value != 8)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.Modulate[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwPayloadSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &DLFrame = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 1 || Value > 253440)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.MCSConfigMode == 0)
        {
            // 自动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.PayloadSize[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwRedunVerIdx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &DLFrame = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (Value < 0 || Value > 3)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.RedunVerIdx[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwNIR(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &DLFrame = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 800 || Value > 3667200)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.IRConfigMode == 0 || attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.IRConfigMode == 2)
        {
            // 自动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.NIR[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}

scpi_result_t SCPI_LTE_SetSubfrmPdschCwSoftChanBit(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    int Number[] = {0, 0, 1};
    int &DLFrame = Number[0];
    int &SubFrame = Number[1];
    int &SetID = Number[2];
    do
    {
        if (!SCPI_CommandNumbers(context, Number, sizeof(Number) / sizeof(Number[0])))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (DLFrame < 0 || DLFrame >= ALG_4G_MAX_DL_FRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SubFrame < 0 || SubFrame >= ALG_4G_MAX_SUBFRAME_NUM)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (SetID != 0 && SetID != 1)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        if (Value < 3200 || Value > 58675200)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        if (attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.IRConfigMode == 0 || attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.IRConfigMode == 1)
        {
            // 自动模式
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
        attr->Pn3GPP->LTE.DL.Chan[DLFrame][SubFrame].PDSCH.SoftChanBit[SetID] = Value;
#if DEBUG_SHOW_LTE
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) <<"DLFrame="<< DLFrame << ",SubFrame=" << SubFrame << ", SetID=" << SetID << ",Value=" << Value << endl;
#endif
    } while (0);
    return SCPI_ResultOK(context, iRet);
}
