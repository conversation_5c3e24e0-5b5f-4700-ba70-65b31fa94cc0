#include "wtbusidefine.h"
#include "wtbusilib_418.h"
#include "wtbusilib.h"
#include "rfswitch.h"
#include "wtvsgdefine.h"
#define BUSILIB_DEBUG 0
static int ledvalue = 0;

static int WT_WriteRefPllAdf4002(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct DeviceConfig DevfgTemp;
    int SPIConfig = 0x1418;    // SPI配置参数
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_WriteRefPllAdf4002 info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (DevfgTemp.DeviceId == 0)
    {
        Reg[SPI_REG_TX_TYPE] = BUSI_BB_REF_PLL_1_TX;
        Reg[SPI_REG_RX_TYPE] = BUSI_BB_REF_PLL_1_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_BB_REF_PLL_1_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_BB_REF_PLL_1_CR2;
    }
    else
    {
        Reg[SPI_REG_TX_TYPE] = BUSI_RF_REF_PLL_2_TX;
        Reg[SPI_REG_RX_TYPE] = BUSI_RF_REF_PLL_2_RX;
        Reg[SPI_REG_CTRL1_TYPE] = BUSI_RF_REF_PLL_2_CR1;
        Reg[SPI_REG_CTRL2_TYPE] = BUSI_RF_REF_PLL_2_CR2;

        wt_write_direct_reg(pdev, (0x2e << 2), 2);
    }

    //设置晶振的code值
    ret = wt_bp_spi_direct_for_write(SPIConfig, DevfgTemp.RegTypeData.Data, Reg, pdev);
    dbg_print("WT_WriteRefPllAdf4002 SPIConfig = 0x%x, DevfgTemp.DeviceId = %d, DevfgTemp.RegTypeData.Data=0x%x\n",SPIConfig, DevfgTemp.DeviceId, DevfgTemp.RegTypeData.Data);
    retWarnning(ret, "wt_bp_spi_direct_for_read failed!\n");
    return ret;
}

//风扇
static int WT_WriteFan(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SMBusConfig = 0x208;
    struct DeviceConfig DevfgTemp;
    unsigned int Reg[6] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_WriteFan info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SMBUS_REG_SLV_ADDR_TYPE] = BUSI_ADT7475_FAN_SMB_1_SLV_ADDR;
    Reg[SMBUS_REG_COM_REG_TYPE] = BUSI_ADT7475_FAN_SMB_1_COM_REG;
    Reg[SMBUS_REG_TX_TYPE] = BUSI_ADT7475_FAN_SMB_1_TX;
    Reg[SMBUS_REG_RX_TYPE] = BUSI_ADT7475_FAN_SMB_1_RX;
    Reg[SMBUS_REG_CTRL1_TYPE] = BUSI_ADT7475_FAN_SMB_1_CR1;
    Reg[SMBUS_REG_CTRL2_TYPE] = BUSI_ADT7475_FAN_SMB_1_CR2;

    ret = wt_smbus_for_write(BACK_FAN_SLAVE_ADDR, DevfgTemp.RegTypeData.Addr, SMBusConfig, DevfgTemp.RegTypeData.Data, Reg, pdev);
    retAssert(ret, "WT_WriteFan failed!\n");

    return WT_OK;
}

static int WT_ReadFan(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SMBusConfig = 0x88200;
    struct DeviceConfig DevfgTemp;
    unsigned int Reg[6] = {0}; //器件所映射的寄存器地址

    if (copy_from_user(&DevfgTemp, arg, DataLength))
    {
        dbg("WT_ReadFan info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    Reg[SMBUS_REG_SLV_ADDR_TYPE] = BUSI_ADT7475_FAN_SMB_1_SLV_ADDR;
    Reg[SMBUS_REG_COM_REG_TYPE] = BUSI_ADT7475_FAN_SMB_1_COM_REG;
    Reg[SMBUS_REG_TX_TYPE] = BUSI_ADT7475_FAN_SMB_1_TX;
    Reg[SMBUS_REG_RX_TYPE] = BUSI_ADT7475_FAN_SMB_1_RX;
    Reg[SMBUS_REG_CTRL1_TYPE] = BUSI_ADT7475_FAN_SMB_1_CR1;
    Reg[SMBUS_REG_CTRL2_TYPE] = BUSI_ADT7475_FAN_SMB_1_CR2;

    ret = wt_smbus_for_read(BACK_FAN_SLAVE_ADDR, DevfgTemp.RegTypeData.Addr, SMBusConfig, &DevfgTemp.RegTypeData.Data, Reg, pdev);
    retAssert(ret, "WT_ReadFan failed!\n");

    if (copy_to_user(arg, &DevfgTemp, DataLength))
    {
        dbg("WT_ReadFan info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return WT_OK;
}

//灯(TCA9539 LED IO扩展)
static int wt_WriteLedIOExtReg(int Addr, int Data, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SPIConfig = 0x9812;
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    Reg[SPI_REG_TX_TYPE] = BUSI_LED_SHIFT_TX;
    Reg[SPI_REG_RX_TYPE] = BUSI_LED_SHIFT_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_LED_SHIFT_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_LED_SHIFT_CR2;

    ret = wt_bp_spi_direct_for_write(SPIConfig, Data, Reg, pdev);
    retWarnning(ret, "wt_WriteLedIOExtReg wt_bp_spi_direct_for_write failed!\n");

    ledvalue &= ~0x3FFFF; //保存灯板状态
    ledvalue |= Data & 0x3FFFF;

#if BUSILIB_DEBUG
    dbg_print("wt_WriteLedIOExtReg Addr = 0x%x, value=0x%x\n", Addr, Data);
#endif
    return ret;
}

static int wt_ReadLedIOExtReg(int Addr, int *Data, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int SPIConfig = 0x9812;
    int TxData = Addr;
    unsigned int Reg[4] = {0}; //器件所映射的寄存器地址

    Reg[SPI_REG_TX_TYPE] = BUSI_LED_SHIFT_TX;
    Reg[SPI_REG_RX_TYPE] = BUSI_LED_SHIFT_RX;
    Reg[SPI_REG_CTRL1_TYPE] = BUSI_LED_SHIFT_CR1;
    Reg[SPI_REG_CTRL2_TYPE] = BUSI_LED_SHIFT_CR2;

#if 1
    (void)TxData;
    (void)SPIConfig;
    *Data = wt_read_direct_reg(pdev, Reg[SPI_REG_TX_TYPE]);
#else
    ret = wt_bp_spi_direct_for_read(SPIConfig, TxData, Data, Reg, pdev);
    retWarnning(ret, "wt_ReadLedIOExtReg wt_bp_spi_direct_for_read failed!\n");
#endif

#if BUSILIB_DEBUG
    dbg_print("wt_ReadLedIOExtReg Addr = 0x%x, value=0x%x\n", Addr, *Data);
#endif

    //检查存储的值和备份的值是否一致，如不等则警告
    if ((*Data & 0x3FFFF) != (ledvalue & 0x3FFFF))
    {
        dbg_print("---Warnning! wt_ReadLedIOExtReg value bak = 0x%x, value read = 0x%x\n", ledvalue, *Data);
    }

    return ret;
}

static int WT_WriteLedIOExtReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct RegType RegTypeTemp;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_WriteLedIOExtReg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    return wt_WriteLedIOExtReg(RegTypeTemp.Addr, RegTypeTemp.Data, pdev);
}

static int WT_ReadLedIOExtReg(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    struct RegType RegTypeTemp;

    if (copy_from_user(&RegTypeTemp, arg, DataLength))
    {
        dbg("WT_ReadLedIOExtReg info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    ret = wt_ReadLedIOExtReg(RegTypeTemp.Addr, &RegTypeTemp.Data, pdev);

    if (copy_to_user(arg, &RegTypeTemp, DataLength))
    {
        dbg("WT_ReadLedIOExtReg info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return ret;
}

static int WT_ReadLedIOExtBit(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = WT_OK;
    int Data1 = 0, Data2 = 0;
    struct BitSetting LedIOStatus;

    if (copy_from_user(&LedIOStatus, arg, DataLength))
    {
        dbg("WT_ReadLedIOExtBit info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    ret = wt_ReadLedIOExtReg(0, &Data1, pdev);
    retWarnning(ret, "WT_ReadLedIOExtBit failed!\n");
    Data2 = wt_read_direct_reg(pdev, BUSI_LED_SHIFT_CLT);

    if (LedIOStatus.Index == LED_R_ALL)
    {
        LedIOStatus.Status = ((Data2 & 0x3) << LED_R_POWER_GREEN) + (Data1 & 0x3FFFF);
    }
    else if (LedIOStatus.Index < LED_R_POWER_GREEN)
    {
        LedIOStatus.Status = (Data1 >> LedIOStatus.Index) & 0x1;
    }
    else if (LedIOStatus.Index == LED_R_POWER_GREEN || LedIOStatus.Index == LED_R_POWER_RED)
    {
        LedIOStatus.Status = (Data2 >> (LedIOStatus.Index - LED_R_POWER_GREEN)) & 0x1;
    }
    else
    {
        dbg("WT_ReadLedIOExtBit Led ID error!");
        return WT_ARG_ERROR;
    }

    if (copy_to_user(arg, &LedIOStatus, DataLength))
    {
        dbg("WT_ReadLedIOExtBit info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }

    return ret;
}

static int wt_WriteLedIOExtBit(int Index, int Status, struct dev_unit *pdev)
{
    int ret = 0;
    int TXData = 0;

    if (Index == LED_R_ALL)
    {
#if BUSILIB_DEBUG
        dbg_print("----------Set all led to %d\n", Status);
#endif
        TXData = Status ? ~0 : 0;
        ret = wt_WriteLedIOExtReg(0, TXData & 0x3FFFF, pdev);
        wt_write_direct_reg(pdev, BUSI_LED_SHIFT_CLT, (TXData >> LED_R_POWER_GREEN) & 0x3);
        retWarnning(ret, "wt_WriteLedIOExtBit failed!\n");
        ledvalue = Status & 0xFFFFF; //保存灯板状态
        return ret;
    }
    else if (Index < LED_R_POWER_GREEN) //芯片1
    {
        ret = wt_ReadLedIOExtReg(0, &TXData, pdev);
        retWarnning(ret, "wt_WriteLedIOExtBit1 wt_ReadLedIOExtReg failed!\n");
        //设置对应IO端口状态
        Status ? SetBit(TXData, Index) : ClearBit(TXData, Index);

        ret = wt_WriteLedIOExtReg(0, TXData & 0x3FFFF, pdev);
        retWarnning(ret, "wt_WriteLedIOExtBit failed!\n");

        if (Index == LED_R_ERR)
        {
            dbg_print("----------Set LED_R_ERR led to %d\n", Status);
        }
    }
    else if (Index == LED_R_POWER_GREEN || Index == LED_R_POWER_RED)
    {
        TXData = wt_read_direct_reg(pdev, BUSI_LED_SHIFT_CLT);
        Status ? SetBit(TXData, Index - LED_R_POWER_GREEN) : ClearBit(TXData, Index - LED_R_POWER_GREEN);
        Status ? SetBit(ledvalue, Index) : ClearBit(ledvalue, Index); //保存灯板状态
        wt_write_direct_reg(pdev, BUSI_LED_SHIFT_CLT, TXData & 0x3);
        dbg_print("----------Set %s led to %d\n", Index == LED_R_POWER_RED ? "PWR RED" : "PWR GREEN", Status);
    }
    else
    {
        dbg("wt_WriteLedIOExtBit Led ID error!");
        return WT_ARG_ERROR;
    }
    return ret;
}

static int WT_WriteLedIOExtBit(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct BitSetting LedSetTemp;

    if (copy_from_user(&LedSetTemp, arg, DataLength))
    {
        dbg("WT_WriteLedIOExtBit info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return wt_WriteLedIOExtBit(LedSetTemp.Index, LedSetTemp.Status, pdev);
}

static int WT_ReadRFVersion(int DataLength, void *arg, struct dev_unit *pdev)
{
    int Ret = WT_OK;
    struct RegType RegTemp;

    if (copy_from_user(&RegTemp, arg, DataLength))
    {
        dbg("WT_ReadHWVersion info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (RegTemp.Addr > ChanlSel_MAX)
    {
        dbg("WT_ReadHWVersion Slot %d error. Slot invaild\n", RegTemp.Addr);
        return WT_OK;
    }

    wt_write_direct_reg(pdev, BUSI_HW_VER_SEL, RegTemp.Addr);
    //回读code值
    RegTemp.Data = wt_read_direct_reg(pdev, BUSI_HW_VER) & 0x7;

    if (copy_to_user(arg, &RegTemp, DataLength))
    {
        dbg("WT_ReadHWVersion info copy_to_user failed!\n");
        return WT_CPY_TO_USR_FAILED;
    }
    return Ret;
}

//内部功率检测
static int WT_GetSwitchInnerPower(int DataLength, void *arg, struct dev_unit *pdev)
{
    int ret = 0;
    struct DeviceConfig DeviceTemp;
    int ChannelSel = 0;
    int Reg[4] = {0};
    int SubPort = WT_RF_PORT_RF1;
    const int PortToChannelTable[8] = {AD7689_CHANNEL_Port1, AD7689_CHANNEL_Port2,
                                        AD7689_CHANNEL_Port3, AD7689_CHANNEL_Port4,
                                        AD7689_CHANNEL_Port5, AD7689_CHANNEL_Port6,
                                        AD7689_CHANNEL_Port7, AD7689_CHANNEL_Port8};
    if (copy_from_user(&DeviceTemp, arg, DataLength))
    {
        dbg("WT_GetSwitchInnerPower info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }

    if (DeviceTemp.RegTypeData.Addr < WT_RF_PORT_RF1 || DeviceTemp.RegTypeData.Addr > WT_RF_PORT_MAX)
    {
        dbg("Read Port %d inner Power error. Port invaild\n", DeviceTemp.RegTypeData.Addr);
        return WT_ARG_ERROR;
    }
    SubPort = DeviceTemp.RegTypeData.Addr - WT_RF_PORT_RF1;

    Reg[SPI_REG_TX_TYPE]        = BUSI_SW_AD7689_CH_SEL;
    Reg[SPI_REG_RX_TYPE]        = BUSI_SW_AD7689_RX;
    Reg[SPI_REG_CTRL1_TYPE]     = BUSI_SW_AD7689_CTRL1;
    Reg[SPI_REG_CTRL2_TYPE]     = BUSI_SW_AD7689_CH_SEL;

    ChannelSel = PortToChannelTable[DeviceTemp.RegTypeData.Addr - 1];
    ret = wt_bp_spi_direct_for_read(ChannelSel, ChannelSel, &DeviceTemp.RegTypeData.Data, Reg, pdev);
    //printk("WT_GetSwitchInnerPower Part=%d, Port=%d, Sel=%d, Data=%d version=%d\n",
    //       DeviceTemp.DeviceId, DeviceTemp.RegTypeData.Addr, ChannelSel, DeviceTemp.RegTypeData.Data, pdev->version);

    if (copy_to_user(arg, &DeviceTemp, DataLength))
    {
        dbg("WT_GetSwitchInnerPower info copy_from_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return ret;
}

//读取背板FPGA基本信息
static int WT_GetBackFpgaInfo(int DataLength, void *arg, struct dev_unit *pdev)
{
    struct BackPlaneFpgaInfo FPGAInfo;
    int i = 0;
    //读取单元板FPGA信息
    FPGAInfo.TesterType = pdev->testertype;
    FPGAInfo.FpgaVersion = wt_read_direct_reg(pdev, BUSI_FPGA_VERSION);
    FPGAInfo.CompileDate = wt_read_direct_reg(pdev, BUSI_FPGA_COMPILE_DATE);
    FPGAInfo.CompileTime = wt_read_direct_reg(pdev, BUSI_FPGA_COMPILE_TIME);
    FPGAInfo.ModuleVersion = wt_read_direct_reg(pdev, BUSI_FPGA_MODULE_VERSION);
    FPGAInfo.BasePowerOk = 0;
    FPGAInfo.RefClockLock = (wt_read_direct_reg(pdev, BUSI_FPGA_PLL_LOCK)&0x1u);

    wt_write_direct_reg(pdev, BUSI_HW_VER_SEL, 0);
    udelay(100); //3-8译码器需要写延时
    FPGAInfo.BackHwVersion = wt_read_direct_reg(pdev, BUSI_HW_VER) & 0x7;
    FPGAInfo.BaseHwVersion = wt_read_direct_reg(pdev, BUSI_HW_VER) & 0x7;
    wt_write_direct_reg(pdev, BUSI_HW_VER_SEL, 1);
    udelay(100); //3-8译码器需要写延时
    FPGAInfo.SwitchHwVersion = wt_read_direct_reg(pdev, BUSI_HW_VER) & 0x7;
    for(i=0;i<WT448_SLOT_NUM;i++)
    {
        FPGAInfo.RfHwVersion[i] = FPGAInfo.SwitchHwVersion;
        FPGAInfo.LoHwVersion[i] = FPGAInfo.SwitchHwVersion;
    }
    if (copy_to_user(arg, &FPGAInfo, DataLength))
    {
        dbg("WT_GetBackFpgaInfo info copy_to_user failed!\n");
        return WT_CPY_FROM_USR_FAILED;
    }
    return WT_OK;
}

static int WT418_WriteRomPage(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadRomPage(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_WriteSwitchAD9228(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadSwitchAD9228(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_GetSwitchPortPower(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}
static int WT418_WriteBpClockHM7043(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadBpClockHM7043(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_InitCryptoAT88(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_WriteCryptoAT88(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadCryptoAT88(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}


static int WT418_WriteBackAD7091Reg(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadBackAD7091Reg(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadBackChannelVoltValue(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_SetExtMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_WriteSwitch42553(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadSwitch42553(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_WriteSwitchPa(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadSwitchPa(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadUBVoltChannelValue(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ReadUBTemperature(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_GetBusiBoardSlot(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_SetUnitModWorkMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_ClearWorkMode(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

static int WT418_GetDevClkState(int DataLength, void *arg, struct dev_unit *pdev)
{
    return WT_OK;
}

int wt418_WriteLedIOExtReg(int Addr, int Data, struct dev_unit *pdev)
{
    return wt_WriteLedIOExtReg(Addr, Data, pdev);
}
int wt418_ReadLedIOExtReg(int Addr, int *Data, struct dev_unit *pdev)
{
    return wt_ReadLedIOExtReg(Addr, Data, pdev);
}
int wt418_WriteLedIOExtBit(int Index, int Status, struct dev_unit *pdev)
{
    return wt_WriteLedIOExtBit(Index, Status, pdev);
}

bool IsBusiBoardComponent(int FuncId)
{
    switch (FuncId)
    {
        case SET_FAN_REG:
        case GET_FAN_REG:
        case READ_LED_IO_EXT_REG:
        case WRITE_LED_IO_EXT_REG:
        case WRITE_LED_IO_EXT_BIT:
        case READ_LED_IO_EXT_BIT:
        case WRITE_REF_PLL_ADF4002:
        case READ_RF_VERSION:
        case WRITE_OCXO_CODE:
        case READ_OCXO_CODE:
        case GET_SWITCH_INNER_POWER:
        case GET_BACK_FPGA_INFO:
        case WRITE_ROM:
        case READ_ROM:
        case WRITE_SWITCH_AD9228:
        case READ_SWITCH_AD9228:
        case GET_SWITCH_PORT_POWER:
        case WRITE_HM7043_CODE:
        case READ_HM7043_CODE:
        case INIT_BACK_CRYPTO_AT88:
        case WRITE_BACK_CRYPTO_AT88:
        case READ_BACK_CRYPTO_AT88:
        case WRITE_BACK_VOLTAGE_REG:
        case READ_BACK_VOLTAGE_REG:
        case READ_BACK_CHANNEL_VALUE:
        case SET_EXT_MODE:
        case WRITE_SWITCH_42553:
        case READ_SWITCH_42553:
        case WRITE_SWITCH_PA:
        case READ_SWITCH_PA:
        case READ_UB_VOLT_CHANNEL_VALUE:
        case READ_UNIT_BOARD_TEMPERATURE:
        case GET_BUSI_BOARD_SLOT:
        case SET_DEVICE_WORK_MODE:
        case CLEAR_WORK_MODE:
        case GET_DEV_CLK_STATE:
            return true;
        default:
            return false;
    }
}

const pFunc pFuncArrayWT418[] =
    {
        // 风扇
        [SET_FAN_REG] = WT_WriteFan,
        [GET_FAN_REG] = WT_ReadFan,
        // LED
        [READ_LED_IO_EXT_REG] = WT_ReadLedIOExtReg,
        [WRITE_LED_IO_EXT_REG] = WT_WriteLedIOExtReg,
        [WRITE_LED_IO_EXT_BIT] = WT_WriteLedIOExtBit,
        [READ_LED_IO_EXT_BIT] = WT_ReadLedIOExtBit,
        // 时钟板ADF4002
        [WRITE_REF_PLL_ADF4002] = WT_WriteRefPllAdf4002,
        // 获取射频板版本
        [READ_RF_VERSION] = WT_ReadRFVersion,
        [WRITE_OCXO_CODE] = WT_WriteBusiOCXOCode,
        [READ_OCXO_CODE] = WT_ReadBusiOCXOCode,
        // 获取开关板内部电压
        [GET_SWITCH_INNER_POWER] = WT_GetSwitchInnerPower,
        [GET_BACK_FPGA_INFO] = WT_GetBackFpgaInfo,
        //空,覆盖未使用的背板接口。
        [WRITE_ROM] = WT418_WriteRomPage,
        [READ_ROM] = WT418_ReadRomPage,
        [WRITE_SWITCH_AD9228] = WT418_WriteSwitchAD9228,
        [READ_SWITCH_AD9228] = WT418_ReadSwitchAD9228,
        [GET_SWITCH_PORT_POWER] = WT418_GetSwitchPortPower,
        [WRITE_HM7043_CODE] = WT418_WriteBpClockHM7043,
        [READ_HM7043_CODE] = WT418_ReadBpClockHM7043,
        [INIT_BACK_CRYPTO_AT88] = WT418_InitCryptoAT88,
        [WRITE_BACK_CRYPTO_AT88] = WT418_WriteCryptoAT88,
        [READ_BACK_CRYPTO_AT88] = WT418_ReadCryptoAT88,
        [WRITE_BACK_VOLTAGE_REG] = WT418_WriteBackAD7091Reg,
        [READ_BACK_VOLTAGE_REG] = WT418_ReadBackAD7091Reg,
        [READ_BACK_CHANNEL_VALUE] = WT418_ReadBackChannelVoltValue,
        [SET_EXT_MODE] = WT418_SetExtMode,
        [WRITE_SWITCH_42553] = WT418_WriteSwitch42553,
        [READ_SWITCH_42553] = WT418_ReadSwitch42553,
        [WRITE_SWITCH_PA] = WT418_WriteSwitchPa,
        [READ_SWITCH_PA] = WT418_ReadSwitchPa,
        [READ_UB_VOLT_CHANNEL_VALUE] = WT418_ReadUBVoltChannelValue,
        [READ_UNIT_BOARD_TEMPERATURE] = WT418_ReadUBTemperature,
        [GET_BUSI_BOARD_SLOT] = WT418_GetBusiBoardSlot,
        [SET_DEVICE_WORK_MODE] = WT418_SetUnitModWorkMode,
        [CLEAR_WORK_MODE] = WT418_ClearWorkMode,
        [GET_DEV_CLK_STATE] = WT418_GetDevClkState,
};