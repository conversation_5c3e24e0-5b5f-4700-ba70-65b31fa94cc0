
#ifndef __WT_SUB_TASK_H__
#define __WT_SUB_TASK_H__
#include "threadpool.h"
#include "service/subservice.h"
#include "service/autobaking.h"

#include <list>
using namespace std;

#define SUB_TASKMGR_START_TIME (15)    // 进程启动多长秒开始启动
#define SUB_TASKMGR_TIMER_INTERVAL (2) // 计时器间隔秒
#define AUTO_BAKING_KEEP_TIME (3600)   // 自动烤机最多持续时间，超过后就不在启动自动烤机服务了

// 主要用来管理:
// 自校准、自动烤机、自动校线

// 借用LinkMgr里面的 线程池 与 独占链接信息
// 如果可行，可以把这个功能类集成到LinkMgr，做一个LinkMgrPlus
class LinkMgr;

class SubTaskmgr
{
public:
    static SubTaskmgr &Instance();
    SubTaskmgr();
    virtual ~SubTaskmgr();

    // 时钟滴答, 也是自动校准的入口函数
    void NoticeTimeTick();

    // 关联LinkMgr
    void SetLinkMgr(void *p)
    {
        m_pLinkMgr = (LinkMgr *)p;
    }

    // 自校准过程过程函数
    void AutoCalService(shared_ptr<SubService> Srv, void *Arg);

    // 删除一个已有的任务
    void DelAutoCalSrv(shared_ptr<SubService> Srv);

    // 设置自交准任务配置
    void SetInCalConfig(int state);

    // 停止所有的任务
    void StopAllTask();

    // 开始自校准
    int StartInCalAtOnce();

    // 停止自校准
    int StopInCalAtOnce();

    // 查询自校准任务
    int QueryInCalProcess(int &Process);

    // 计算下一次自校准的时间
    void CalcNextInCalTime();

    // 通知出现排他链接
    void NoticeHasExcludeLink(bool Has);

    // 重设温度相关数据
    void ResetTempData();

    // 启动自校准
    int RunInCal();

    // 采样某一个单元的温度，并与历史温度计算差异，如果超过门限值，则激活温度自校准状态
    void DetectSomeoneDevTempState();

    // 生成动态的自校准时间表
    void GenerateDynamicInCalTimeTable();

    // 清空动态生成的自校准时间表
    void ClearDynamicInCalTimeTable();

    // 历史温度检测温度突变比较大的场景
    void LongTimeTempDetect();

    void AutoBakingSeriveEntry(double ServerLiveSeconds);

    int StopAutoBakingService();

    // 自动烤机任务的入口点函数
    //void StartAutoTest(void *Arg);

    void StartAutoBakingSerive(shared_ptr<AutoBakingSerive> Srv, void *Arg);
    bool IsAutoBakingSeriveRunning();

private:
    std::list<std::shared_ptr<SubService>> m_AuotCalSrv; // 自动校准子任务列表
    std::list<std::shared_ptr<SubService>> m_PacSrv;     // APLC 子任务列表(暂时无用)

    std::list<std::shared_ptr<AutoBakingSerive>> m_AutoBakingSrvList; // 自动烤机任务列表

    int m_CurMgrState;             // 管理器当前的状态，自校准 或者 PAC测试 或者 烤机等等
    time_t m_ServerBootTime;       // 记录进程启动时刻
    LinkMgr *m_pLinkMgr = nullptr; // LinkMgr 对象, 需要用到它的线程池.
    time_t m_LastNoticeExLinkTime; // 最近一次通知排他连接的时间
    time_t m_LastStopInCalTime;    // 最近一次终止自校准的时间
    int m_NextStartIdx = -1;       // 下一次开始启动时刻索引, 见IN_AUTO_TIMES数组
    long m_NextStartTimes = 0;     // 下一次开始启动时刻
    int m_InCalErrTimes = 0;       // 连续自校出错次数
    int m_InCalSrvErrCount = 0;    // 记录当前自校准时多个线程出错的个数
    int m_InCalErrCode = 0;        // 最近一次出错代码
    int m_InCalBeenStop = 0;       // 管理器主动关闭自校业务或者存在连接
    bool m_EnableInCal = true;     // 是否启用内部自校准功能, 缺省是自动开启自校准任务
    long m_InCalStartTimes = 0;    // 自校准的开始时间，主要是考虑自校准时间可能太长，需要计时处理
    int m_InCalSpace = 20;         // 自校准间隔最大时间
    long m_InCalCount = 0;         // 自校准当前计时器
    int m_InCalUseMulThread = 0;   // 自校准是否使用多线程
    int m_InCalOnePort = 0;        // 是否加速自校准
    int m_UintMapRfCount = 2;      // 1个单元映射端口的数量

    // 温度变化大启动自校准的相关属性
    double m_TempDataSum = 0;     // 1分钟内的温度和
    double m_TempDataCount = 0;   // 1分钟内温度已经采样的次数
    double m_TempDataMax = -256;  // 自上一次自交完成以后，温度最大值
    double m_TempDataMin = 1000;  // 自上一次自交完成以后，温度最小值
    bool m_TempNeedInCal = false; // 温度变化大，激活自校准状态
    double m_TempActiveThreshold; // 温度激活自校准阈值

    int m_TempDetectTick = 0; // 温度检测的tick次数

    std::mutex m_Mutex; // 某管理连接对象列表使用的锁

    std::unique_ptr<char[]>m_HistTemperBuf = nullptr; // 历史温度缓冲区

    bool m_EnableAutoBaking = true; // 是否启用自动烤机
    std::mutex m_AutoBakingMutex;   // 自动烤机
    std::mutex m_NoticeMutex;   // 自动烤机
    long m_TimesSpaceArraySum = 0; 
    int m_TimesSpaceArrayId = 0;
};

#endif