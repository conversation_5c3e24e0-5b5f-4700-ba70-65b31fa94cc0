#!/bin/bash

TARGET_PATH=$(find /lib/modules/$(uname -r)/kernel/drivers/net/ethernet/intel -name e1000e -type d)
SRC_PATH=$(dirname $(readlink -f $0))

echo "Build the module and install"
module="e1000e"

Version=$(ethtool -i eth0 | grep -e '^version' | awk '{ print $2}')
if [[ $Version != *3.8.7* ]]; then
	echo "Backup and rename $module.ko to $module$Version.bak"
	mv $TARGET_PATH/$module.ko $TARGET_PATH/$module$Version.bak
	cp $SRC_PATH/$module.ko $TARGET_PATH/$module.ko
	chmod 644 $TARGET_PATH/$module.ko
	
	echo "DEPMOD $(uname -r)"
	depmod `uname -r`
	echo "load module $module"
	modprobe $module
	update-initramfs -u -k $(uname -r)
fi

echo "Completed."
exit 0

