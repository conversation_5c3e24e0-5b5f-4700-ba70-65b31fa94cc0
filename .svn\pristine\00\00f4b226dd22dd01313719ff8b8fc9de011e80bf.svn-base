/**
 * @file cellular_result_common_interface.cpp
 * @brief 蜂窝公共头文件, 定义了蜂窝一些常用的函数
 * @version 0.1
 * @date 2024-10-09
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#include "cellular_result_common_interface.h"

scpi_result_t GetRstIntData(scpi_t *context, const char *ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, ParamStr, &Result, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultInt(context, (int)Result);

    return SCPI_RES_OK;
}

scpi_result_t GetRstIntData(scpi_t *context, std::vector<const char *> &ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    std::vector<double> allResult;
    for (auto &item : ParamStr)
    {
        double Result = 0;
        int iRet = WT_GetResult(attr->ConnID, item, &Result, streamID, segmentID);
        IF_ERR_RETURN(iRet);
        allResult.push_back(Result);
    }

    for (auto &item : allResult)
    {
        SCPI_ResultInt(context, (int)item);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetRstDoubleData(scpi_t *context, const char *ParamStr)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));
    double Result = 0;
    int iRet = WT_GetResult(attr->ConnID, ParamStr, &Result, streamID, segmentID);
    IF_ERR_RETURN(iRet);

    SCPI_ResultDouble(context, Result);

    return SCPI_RES_OK;
}

scpi_result_t GetRstIntVectorData(scpi_t *context, const char *ParamStr, bool arb)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);
    if (!arb)
    {
        int DoubleCnt = ResultSize / sizeof(int);
        for (int i = 0; i < DoubleCnt; i++)
        {
            int *TmpData = (int *)ResultBuf.get();
            int Value = *(int *)(TmpData + i);
            SCPI_ResultInt(context, Value);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }
    return SCPI_RES_OK;
}

scpi_result_t GetRstDoubleVectorData(scpi_t *context, const char *ParamStr, bool arb)
{
    SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
    int segmentID = 0;
    int streamID = 0;
    SCPI_ParamInt(context, &streamID, false);
    SCPI_ParamInt(context, &segmentID, false);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

    ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

    std::unique_ptr<char[]> ResultBuf = nullptr;
    unsigned ElementSize = 0;
    unsigned ElementCount = 0;
    unsigned ResultSize = 0;
    int iRet = WT_ERR_CODE_OK;

    if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
        (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
    {
        ResultSize = ElementSize * ElementCount;
        ResultBuf.reset(new (std::nothrow) char[ResultSize]);
        iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
    }

    IF_ERR_RETURN(iRet);
    if (!arb)
    {
        int DoubleCnt = ResultSize / sizeof(double);
        for (int i = 0; i < DoubleCnt; i++)
        {
            double *TmpData = (double *)ResultBuf.get();
            double Value = *(double *)(TmpData + i);
            SCPI_ResultDouble(context, Value);
        }
    }
    else
    {
        SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
    }

    return SCPI_RES_OK;
}

// scpi_result_t GetRstCharVectorData(scpi_t *context, const char *ParamStr, bool arb)
// {
//     SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
//     int segmentID = 0;
//     int streamID = 0;
//     SCPI_ParamInt(context, &streamID, false);
//     SCPI_ParamInt(context, &segmentID, false);

//     WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "streamID=" << streamID << ", segmentID=" << segmentID << endl;

//     ILLEGAL_PARAM_RETURN(!validStreamSegmentID(streamID, segmentID));

//     std::unique_ptr<char[]> ResultBuf = nullptr;
//     unsigned ElementSize = 0;
//     unsigned ElementCount = 0;
//     unsigned ResultSize = 0;
//     int iRet = WT_ERR_CODE_OK;

//     if ((iRet = WT_GetVectorResultElementSize(attr->ConnID, ParamStr, &ElementSize, streamID, segmentID)) == WT_ERR_CODE_OK &&
//         (iRet = WT_GetVectorResultElementCount(attr->ConnID, ParamStr, &ElementCount, streamID, segmentID)) == WT_ERR_CODE_OK)
//     {
//         ResultSize = ElementSize * ElementCount;
//         ResultBuf.reset(new (std::nothrow) char[ResultSize]);
//         iRet = WT_GetVectorResult(attr->ConnID, ParamStr, ResultBuf.get(), ResultSize, streamID, segmentID);
//     }

//     IF_ERR_RETURN(iRet);
//     if (!arb)
//     {
//         int DoubleCnt = ResultSize / sizeof(char);
//         for (int i = 0; i < DoubleCnt; i++)
//         {
//             char *TmpData = (char *)ResultBuf.get();
//             char Value = *(char *)(TmpData + i);
//             SCPI_ResultInt(context, Value);
//         }
//     }
//     else
//     {
//         SCPI_ResultArbitraryBlock(context, (char *)(ResultBuf.get()), ResultSize);
//     }
//     return SCPI_RES_OK;
// }
