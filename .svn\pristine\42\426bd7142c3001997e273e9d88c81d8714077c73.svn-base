//*****************************************************************************
//  File: main.cpp
//  WT-Server程序入口文件，调用各模块初始化，创建主循环
//  Data: 2016.7.12
//*****************************************************************************
#include <signal.h>
#include <sys/time.h>
#include <unistd.h>
#include <cstdlib>
#include <cassert>
#include <chrono>
#include <execinfo.h>
#include <cxxabi.h>
#include "wtev++.h"
#include "wt-calibration.h"
#include "version.h"
#include "threadpool.h"
#include "link.h"
#include "conf.h"
#include "wtlog.h"
#include "wterror.h"
#include "devlib.h"
#include "templib.h"
#include "devmgr.h"
#include "mempool.h"
#include "license.h"
#include "analysis/algenv.h"
#include <signal.h>
#include <thread>
#include <mcheck.h>

#include "subtaskmgr.h"
#include "sysmonitor.h"
#include "wt-calibration.h"
#include "digitallib.h"
#include "wtspec.h"
#include "ldpc.h"
#include "service/broadcastvsg.h"

/*#include "scpiapiwrapper/scpiutils.h"
#include "scpiapiwrapper/wrapper.h"
#include "server/scpi_service.h"
#include "server/scpi_socket.h"
#include "server/scpi_config.h"
#include "server/scpi_conninfo.h"*/

using namespace std;

#ifdef DEBUG
//保证程序CTRL+C退出时能正常记录coverage信息
extern "C" void __gcov_flush(void);
static void SignalCb(wtev::sig &watcher, int revents)
{
    (void)revents;
    watcher.loop.break_loop();
    #ifdef COVERAGE
    __gcov_flush();
    #endif
}
#endif

void Getserverinfo()
{
    char Buf[1024]={0};
    int Cnt = readlink("/proc/self/exe", Buf, 1024);
    while (Cnt--)
    {
        if (Buf[Cnt] == '/')
        {
            Buf[Cnt] = '\0';
            break;
        }
    }

    char DirName[2048] = {0};
    sprintf(DirName, "%s/ServerINFO/", Buf);

    if (-1 == access(DirName, F_OK))
    {
        if (-1 == mkdir(DirName, 0755))  //建立目录
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "Create test get server info dir failed!\n");
            return;
        }
    }

    char DataBuf[512] = {0};
    struct tm *Mtm;
    time_t Now;
    time(&Now);
    Mtm = localtime(&Now);
    sprintf(DataBuf, "Sever_server_info_%d-%d-%d_%d-%d-%d.csv", 1900 + Mtm->tm_year, Mtm->tm_mon + 1, Mtm->tm_mday, Mtm->tm_hour, Mtm->tm_min, Mtm->tm_sec);

    char DataFileName[4096] = {0};
    sprintf(DataFileName,"%s%s",DirName,DataBuf);
    
    char cmd[256] = {0};
    string File = " ";
    File = WTConf::GetDir() + "/gstack_2.sh WT-Server> ";
    sprintf(cmd, "%s%s", File.c_str(), DataFileName);
    Basefun::LinuxSystem(cmd);
}

unique_ptr<LinkMgr> pLinkMgr = nullptr;    //主要为了崩溃时保存采集信息使用

int EventFd = -1;
//打印异常调用栈信息
static void Backtrace(int Sig, siginfo_t *Info, void *Arg)
{
    (void)Info;
    (void)Arg;

    if (Sig == SIGPIPE)
    {
        return;
    }

    //崩溃时保存传给算法的采集数据和参数
    if(pLinkMgr != nullptr)
    {
        pLinkMgr->SaveStackData();
    }

    printf("Backtrace sig:%d, errno:%d, code:%d, tid:%ld\n\n\n",
           Info->si_signo, Info->si_errno, Info->si_code, pthread_self());
    Getserverinfo();
    void *Addrlist[50];
    size_t AddrLen;

    AddrLen = backtrace(Addrlist, 50);
    backtrace_symbols_fd(Addrlist, AddrLen, STDOUT_FILENO);
    

    if(EventFd > 0)
    {
        uint64_t Val = ErrorEvent | (Info->si_signo & 0xFFFFFFF);
        auto _= write(EventFd, &Val, sizeof(uint64_t));
        (void)_;
    }

    _exit(0);
}

//static void Backtrace(int Sig, siginfo_t *Info, void *Arg)
//{
//    (void)Info;
//    (void)Arg;
//    if (Sig == SIGPIPE)
//    {
//        return;
//    }
//
//    //崩溃时保存传给算法的采集数据和参数
//    if(pLinkMgr != nullptr)
//    {
//        pLinkMgr->SaveStackData();
//    }
//
//    FILE *Out = stderr;
//    void *Addrlist[50];
//    size_t AddrLen;
//
//    char **Symlist;
//    AddrLen = backtrace(Addrlist, 50);
//    Symlist = backtrace_symbols(Addrlist, AddrLen);
//
//    // allocate string which will be filled with the demangled function name
//    size_t NameSize = 256;
//    char *FuncName = (char *)malloc(NameSize);
//
//    fprintf(Out, "build date: %s\nstack trace:\n", WTGetBuildDate());
//
//    // iterate over the returned symbol lines. skip the first, it is the
//    // address of this function.
//    for (int i = 1; i < (signed)AddrLen; i++)
//    {
//        char *BeginName = 0, *BeginOffset = 0, *EndOffset = 0;
//
//        char *BeginAddr = 0; // 中括号中的地址, 这个地址用addr2line 可以定位到某一行
//        char *EndAddr = 0;   // 中括号中的地址, 这个地址用addr2line 可以定位到某一行
//
//        // find parentheses and +address offset surrounding the mangled name:
//        // ./module(function+0x15c) [0x8048a6d]
//        for (char *p = Symlist[i]; *p; ++p)
//        {
//            if (*p == '(')
//                BeginName = p;
//            else if (*p == '+')
//                BeginOffset = p;
//            else if (*p == ')' && BeginOffset)
//            {
//                EndOffset = p;
//            }
//            else if (*p == '[')
//            {
//                BeginAddr = p;
//            }
//            else if (*p == ']' && BeginAddr)
//            {
//                EndAddr = p;
//                break;
//            }
//        }
//
//        if (BeginAddr && EndAddr)
//        {
//            *BeginAddr++ = '\0';
//            *EndAddr = '\0';
//        }
//        else
//        {
//            BeginAddr = 0;
//            EndAddr = 0;
//        }
//
//        if (BeginName && BeginOffset && EndOffset && BeginName < BeginOffset)
//        {
//            *BeginName++ = '\0';
//            *BeginOffset++ = '\0';
//            *EndOffset = '\0';
//
//            // mangled name is now in [BeginName, BeginOffset) and caller
//            // offset in [BeginOffset, EndOffset). now apply
//            // __cxa_demangle():
//
//            int Status;
//            char *Ret = abi::__cxa_demangle(BeginName, FuncName, &NameSize, &Status);
//            if (Status == 0)
//            {
//                FuncName = Ret; // use possibly realloc()-ed string
//                if (BeginAddr)
//                {
//                    fprintf(Out, "  %s : %s+%s [%s]\n", Symlist[i], FuncName, BeginOffset, BeginAddr);
//                }
//                else
//                {
//                    fprintf(Out, "  %s : %s+%s\n", Symlist[i], FuncName, BeginOffset);
//                }
//            }
//            else
//            {
//                // demangling failed. Output function name as a C function with
//                // no arguments.
//                if (BeginAddr)
//                {
//                    fprintf(Out, "  %s : %s()+%s [%s]\n", Symlist[i], BeginName, BeginOffset, BeginAddr);
//                }
//                else
//                {
//                    fprintf(Out, "  %s : %s()+%s\n", Symlist[i], BeginName, BeginOffset);
//                }
//            }
//        }
//        else
//        {
//            // couldn't parse the line? print the whole line.
//            fprintf(Out, "  %s\n", Symlist[i]);
//        }
//    }
//
//    free(FuncName);
//    free(Symlist);
//
//    exit(0);
//}

//注册异常回掉
static void RegExcpHandler(void)
{
    struct sigaction Action;
    Action.sa_sigaction = Backtrace;
    sigemptyset(&Action.sa_mask);

    Action.sa_flags = SA_RESTART | SA_SIGINFO;
    sigaction(SIGSEGV, &Action, NULL);//无效的内存引用
    sigaction(SIGABRT, &Action, NULL);//由ablort发出的退出指令
    sigaction(SIGPIPE, &Action, NULL);//管道破裂，写一个没有读端的管道
    sigaction(SIGFPE, &Action, NULL);//错误的算术运算，如除以零
    sigaction(SIGBUS, &Action, NULL);//总现错误，错误的内存访问
    sigaction(SIGILL, &Action, NULL);//非法指令
}

// 通过定时往eventfd中写入数据上报进程心跳
static void HeartBeat(wtev::timer &watcher, int revents)
{
    (void)revents;

    int Fd = (int)(long)watcher.data;
    uint64_t Val = 1;
    ssize_t s = write(Fd, &Val, sizeof(uint64_t));

    if(s != sizeof(uint64_t))
    {
        WTLog::Instance().LOGERR(WT_ERROR, "Server eventfd write error");
    }

    License::Instance().CheckLicExpTime(false);
    //License::Instance().CheckDevTime(false);
}

static void DevTimeCheckTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;

    License::Instance().CheckDevTime(false);//监测仪器系统时间
}

void CalibrationInitThread()
{
    int Count = DevLib::Instance().GetUnitBoardModNum(DEV_TYPE_VSG);
    int UnitMapRfport = WT_RF_1;
    for (int unit = 0; UnitMapRfport < WT_RF_MAX;++UnitMapRfport)
    {
        DevLib::Instance().GetModId(DEV_TYPE_VSG, UnitMapRfport, unit);
        if(unit != 0)
        {
            break;
        }
    }
    int Ret = wt_calibration_initial(Count, --UnitMapRfport);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_CAL_BASE_ERROR + Ret, "init calibration failed");
    }
}

void DevlibInitThread(int ServerId)
{
    WTConf::DevCfg Cfg;
    BaseConf::Instance().GetDevCfg(ServerId, Cfg);

    DevLib::SetMask(Cfg.VsaMask, Cfg.VsgMask);
    DevLib::Instance().DevLibInit();
}

#include "analysis/analysis.h"
//函数参数列表如下：arg[1] : 当前目录，argv[2] : ID, argv[3] : socket, argv[4] : eventfd
int main(int argc, char *argv[])
{
    (void)argc;

    // mtrace();
    
    RegExcpHandler();
    WTFileSecure::SetDynamicPasswd(DynamicPasswd::GetDynamicPasswd());

    // 日志类初始化
    WTLog::SetLogName(WTConf::GetDir() + "/server" +  argv[2] + ".db");
    WTLog::SetFiFoName(WTConf::GetDir() + "/fifo.server" +  argv[2]);
    WTLog::SetLogPreName("WT_SERVER");
    //WTLog::Instance();
    WTLog::Instance().SetLogPrintEnalbe((2 << LOG_TYPE_END) - 1); //日志输出都打开
    WTLog::Instance().LOGSYSOPT("WT-Server start!");

    AlgEnv::Instance().Init();

    DigModeLib::Instance();
    HwDecodeResMgr::Instance();
    //for DEBUG
    string type(argv[1]);
    if (type == "-f")
    {
        int Freq = atoi(argv[3]);
        int Demode = atoi(argv[4]);
        Analysis Alg;
        return Alg.FindBestIQParam(argv[2], Demode, Freq);
    }

#if SERVER_MULTI_INIT
    int Ret = WT_OK;
    int ServerId = atoi(argv[2]);
    thread CalibThread(CalibrationInitThread);
    thread DevlibThread(DevlibInitThread, ServerId);
    CalibThread.join();
    DevlibThread.join();

    wtev::default_loop Loop;
#else
    int Ret = WT_OK;
    wtev::default_loop Loop;
    //初始化校准库, 参数: 单元个数, 每个单元映射端口数
    int Count = DevLib::Instance().GetUnitBoardModNum(DEV_TYPE_VSG);
    int UnitMapRfport = WT_RF_1;
    for (int unit = 0; UnitMapRfport < WT_RF_MAX;++UnitMapRfport)
    {
        DevLib::Instance().GetModId(DEV_TYPE_VSG, UnitMapRfport, unit);
        if(unit != 0)
        {
            break;
        }
    }
    Ret = wt_calibration_initial(Count, --UnitMapRfport);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(WT_CAL_BASE_ERROR + Ret, "init calibration failed");
    }

    int ServerId = atoi(argv[2]);
	WTConf::DevCfg Cfg;
    BaseConf::Instance().GetDevCfg(ServerId, Cfg);

    DevLib::SetMask(Cfg.VsaMask, Cfg.VsgMask);
    DevLib::Instance().DevLibInit();
#endif

    //温度监控初始化
    TempLib::Instance().TempLibInit(ServerId, Loop);
    
#ifdef DEBUG
    //显示当前硬件信息
    //DevLib::Instance().ShowHWInfo();
#endif
    DevLib::Instance().SetPortLedOff();
    DevLib::Instance().VSGDacStatusMonitor(Loop);

    //检查license支持的PART A/B中的RF端口
    int RFNum = -1;
    License::Instance().GetRFPortNum(RFNum);

    DevMgr::SetSrvID(ServerId);
    DevMgr::Instance().SetHwCb(Loop);
    BroadcastVsg::Instance().BroadcastVsgInit(Loop);
    SysMonitor::Instance().SetHwCb(Loop);

    //内存池初始化大小1G TODO需要实际确定
    Ret = MemPool::Instance().Init(MEMORY_POOL_SIZE);
    if (Ret != WT_OK)
    {
        WTLog::Instance().LOGERR(Ret, "Init mempool failed");
    }

    pLinkMgr.reset(new LinkMgr(atoi(argv[3]), Loop));
    Ret = pLinkMgr->Activate();
    if (Ret != WT_OK)
    {
        return Ret;
    }

    //配置硬件界面接口函数给算法
    WT_Algorithm_SetLdpcHwDecodeHandle(&HwDecodeResMgr::LdpcCompute);
    WT_Algorithm_SetBccHwDecodeHandle(&HwDecodeResMgr::BccCompute);

    // 激活子任务管理器, 并关联到LinkMgr
    SubTaskmgr::Instance().SetLinkMgr(pLinkMgr.get());

#ifdef DEBUG
    wtev::sig SigEv(Loop);
    SigEv.set<SignalCb>();
    SigEv.start(SIGINT);
#endif

    //注册心跳定时器，每秒上报一次
    wtev::timer ReportEv(Loop);
    EventFd = atoi(argv[4]);
    ReportEv.set<HeartBeat>((void *)(long)EventFd);
    ReportEv.start(1, 1);

    //仪器时间是否被篡改监控定时器,间隔为1小时监测一次
    wtev::timer DevTimeEvTimer(Loop);
    const int DEV_TIME_CHECK_TIMER_INTERVAL = 1*60*60;
    DevTimeEvTimer.set<DevTimeCheckTimerCb>();
    DevTimeEvTimer.start(30, DEV_TIME_CHECK_TIMER_INTERVAL);
    DevLib::Instance().SetLedStatus(LED_POWER, LED_STATUS_GREEN);//只要仪器能起来power green灯亮
    Loop.run();

    return 0;
}
