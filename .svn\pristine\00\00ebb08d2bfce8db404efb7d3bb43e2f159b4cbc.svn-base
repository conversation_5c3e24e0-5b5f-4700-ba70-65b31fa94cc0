#pragma once
#ifndef LINUX
#include <windows.h>
#include <assert.h>
class CLoadLibrary
{
public:
    HINSTANCE m_hInst;

    CLoadLibrary(HINSTANCE hInst = NULL) : m_hInst(hInst)
    {
    }

    CLoadLibrary(const char* pstrFileName) : m_hInst(NULL)
    {
        Load(pstrFileName);
    }

    ~CLoadLibrary()
    {
        Free();
    }

    BOOL Load(const char* pstrFileName, DWORD dwFlags = 0)
    {
        assert(!::IsBadStringPtrA(pstrFileName, MAX_PATH));
        Free();
        m_hInst = ::LoadLibraryExA(pstrFileName, NULL, dwFlags);
        return m_hInst != NULL;
    }

    void Free()
    {
        if (IsLoaded())
        {
            ::FreeLibrary(m_hInst);
            m_hInst = NULL;
        }
    }

    HINSTANCE Detach()
    {
        HINSTANCE hInst = m_hInst;
        m_hInst = NULL;
        return hInst;
    }

    BOOL IsLoaded() const
    {
        return m_hInst != NULL;
    }

    FARPROC GetProcAddress(LPCSTR pszFuncName) const
    {
        assert(!::IsBadStringPtrA(pszFuncName, -1));
        assert(IsLoaded());
        return ::GetProcAddress(m_hInst, pszFuncName);
    }

    BOOL GetFileName(LPTSTR pstrFilename, DWORD cchMax = MAX_PATH) const
    {
        assert(IsLoaded());
        return ::GetModuleFileName(m_hInst, pstrFilename, cchMax);
    }

    operator HINSTANCE() const
    {
        return m_hInst;
    }
};

#endif