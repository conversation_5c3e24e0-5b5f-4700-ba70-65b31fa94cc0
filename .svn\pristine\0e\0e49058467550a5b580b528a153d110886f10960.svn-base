8080_noise_enable = 1                  //80+80底噪配置使能标志，1使能，0不使能
8080_centerfreq_space1 = 130           //80+80中心频率间隔1，单位MHz
8080_centerfreq_space2 = 800           //80+80中心频率间隔2，单位MHz
8080_noise_level1 = -100               //80+80底噪功率1，参考电平小于0，单位dBm
8080_noise_level1_ref2 = -82           //80+80底噪功率1，参考电平大于等于0，单位dBm
8080_noise_level2 = -100               //80+80底噪功率2，参考电平小于0，单位dBm
8080_noise_level2_ref2 = -100          //80+80底噪功率2，参考电平大于等于0，单位dBm
8080_noise_delta = 2                   //80+80底噪波动范围，单位dBm，例如5表示正负5dBm