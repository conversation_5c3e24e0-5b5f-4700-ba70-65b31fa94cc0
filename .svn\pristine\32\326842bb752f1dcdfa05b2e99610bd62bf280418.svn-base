#include "includeall.h"
#ifndef LINUX
#include <Windows.h>
#else
#include "protocolsub.h"
#endif

static bool IsInitFlag = false;
#ifndef LINUX
static volatile u32 DLLInitLocker = 0;
#else
static atomic_flag DLLInitLocker = ATOMIC_FLAG_INIT;
#endif

#define VALID_WRAPPER_ID(expr)          \
	if (!(expr))                        \
	{                                   \
		Logger::PrintDebug(__FUNCTION__,__LINE__, "WT_ERR_CODE_CONNECT_FAIL\n");\
		return WT_ERR_CODE_CONNECT_FAIL;              \
	}               

TesterWrapper *GetUsableWrapper(int connID)
{
    return TesterManager::GetTesterWrapper(connID);
}
void ReleaseWrapper(int connID)
{
    TesterManager::ReleaseTesterWrapper(connID);
}
//************************************************************************
//                      Measure(包括Facility,Meter以及对外的接口)
//************************************************************************
WTTESTER_DLL_API void WT_DLLInitialize()
{
    //WT4XX已经弃用
    //初始化内存池
    ENTER_LOCK(DLLInitLocker);
    if (false == IsInitFlag)
    {
#ifndef LINUX
        s32 maxSamplePoint = 4800000/2; //5ms data capture
#else
        s32 maxSamplePoint = 4800000;   //(480*1000*20)/2 = 20ms/2 = 10ms data capture
#endif
        UsualKit::config_int_var("api_cfg.json", "WT_MAX_SAMPLE_POINT", &maxSamplePoint);
        Logger::WriteLog(eumLogType_Log, "WT_MAX_SAMPLE_POINT = %d", maxSamplePoint);
        InitPNFileMaxSampleCount(maxSamplePoint);
        TesterManager::Init();
        IsInitFlag = true;
    }
    EXIT_LOCK(DLLInitLocker);
    return;
}

WTTESTER_DLL_API void WT_DLLInitialize_V2(unsigned int maxMemSize, unsigned int maxSamplePoint)
{
    //初始化内存池
    ENTER_LOCK(DLLInitLocker);
    if (false == IsInitFlag)
    {
        UsualKit::config_int_var("api_cfg.json", "WT_MAX_SAMPLE_POINT", (s32*)&maxSamplePoint);
        Logger::WriteLog(eumLogType_Log, "WT_MAX_SAMPLE_POINT = %d", maxSamplePoint);
        InitPNFileMaxSampleCount(maxSamplePoint);
        TesterManager::Init();
        IsInitFlag = true;
    }
    EXIT_LOCK(DLLInitLocker);
    return;
}

WTTESTER_DLL_API void *WT_GetPNBuf()
{
    return TesterManager::GetPnBuf();
}

WTTESTER_DLL_API void WT_SetWaveEncrypted(int set)
{
    return SetWaveEncrypted(set);
}

WTTESTER_DLL_API void WT_SetWaveSNAndFW(const char *SN, const char *FwVersion)
{
    return SetWaveSNAndFW(SN, FwVersion);
}

#ifndef LINUX
WTTESTER_DLL_API void WT_DLLTerminate(void)
{
    ENTER_LOCK(DLLInitLocker);
#ifdef _DEBUG
    TesterManager::memdump();
#endif
    TesterManager::Reset();
    IsInitFlag = false;
    EXIT_LOCK(DLLInitLocker);
}

int CALL_MODE WT_SetCrashDumpIf(int crashDumpMode)
{
    return WT_ERR_CODE_OK;
}

#else
WTTESTER_DLL_API void WT_DLLTerminate(void)
{
    ENTER_LOCK(DLLInitLocker);
    TesterManager::Reset();
    IsInitFlag = false;
    EXIT_LOCK(DLLInitLocker);
}
#endif

WTTESTER_DLL_API int WT_GetSubTesterConfig(const char *ipAddr,
    SubTesterCfg testerCfg[WT_SUB_TESTER_INDEX_MAX],
    int *subTesterCount)
{
    A_ASSERT(ipAddr && subTesterCount);
    return  TesterManager::GetSubTesterCfg(ipAddr, testerCfg, subTesterCount);
}

WTTESTER_DLL_API int WT_GetTesterConnStatus(ConnectedUnit *conUnit,
    int *statu,
    char *buffer,
    int bufferSize)
{
    A_ASSERT(conUnit && statu && bufferSize);
    return TesterManager::GetTesterConnStatus(conUnit, statu, buffer, bufferSize);
}

WTTESTER_DLL_API int WT_Connect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    A_ASSERT(connUnit && connID);
    //默认配置为多连接
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_NORMAL);
}

WTTESTER_DLL_API int WT_ForceConnect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    A_ASSERT(connUnit && connID);

#ifndef LINUX
#if _DEBUG
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_NORMAL);
    //return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_FORCE);
#else
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_FORCE);

#endif // _DEBUG
#else
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_FORCE);
#endif
}

WTTESTER_DLL_API int WT_DisConnect(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    if (nullptr == wrapper)
    {
        return WT_ERR_CODE_OK;
    }
    int err = wrapper->DisConnect();
    if (WT_ERR_CODE_OK != err)
    {
        return err;
    }
    ReleaseWrapper(connID);
    return err;
}

WTTESTER_DLL_API int WT_SwitchMode(int connID, int targetMode)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SwitchMode(targetMode);
}

WTTESTER_DLL_API int WT_AddMimoTester(int connID, ConnectedUnit connUnit)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->AddMimoTester(connUnit);
}

WTTESTER_DLL_API int WT_RemoveMimoTester(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->RemoveMimoTester();
}

WTTESTER_DLL_API int WT_SetNetInfo(int connID, VirtualNetType *netInfo)
{
    A_ASSERT(netInfo);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetNetInfo(netInfo);
}

WTTESTER_DLL_API int WT_GetNetInfo(int connID, VirtualNetType *netInfo)
{
    A_ASSERT(netInfo);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetNetInfo(netInfo);
}

WTTESTER_DLL_API int WT_GetNetLink(int connID, bool *LinkStatus)
{
    A_ASSERT(LinkStatus);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetNetLink(LinkStatus);
}

WTTESTER_DLL_API int WT_GetLicense(int connID,
    LicItemInfo_API *testerLicense,
    int licenseMaxCount,
    int *licActualcount)
{
    A_ASSERT(testerLicense && licActualcount);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetLicense(testerLicense, licenseMaxCount, licActualcount);
}

WTTESTER_DLL_API int WT_GetTesterInfo(int connID, TesterInfo *info)
{
    A_ASSERT(info);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterInfo(info);
}

WTTESTER_DLL_API int WT_GetTesterIpAddressType(int connID, bool *IsDhcp)
{
    A_ASSERT(IsDhcp);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterIpAddressType(IsDhcp);
}

WTTESTER_DLL_API int WT_GetTesterInfo_V2(int connID, TesterOverview *info)
{
    A_ASSERT(info);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    TesterInfo tmpInfo;
    memset(&tmpInfo, 0, sizeof(tmpInfo));
    int iRet = wrapper->GetTesterInfo(&tmpInfo);

    if (WT_ERR_CODE_OK == iRet)
    {
        memcpy(info->Ip, tmpInfo.IP, sizeof(info->Ip));
        memcpy(info->Name, tmpInfo.Name, sizeof(info->Name));
        memcpy(info->SN, tmpInfo.SN, sizeof(info->SN));
        memcpy(info->FwVersion, tmpInfo.FwVersion, sizeof(info->FwVersion));
        memcpy(info->Ip, tmpInfo.IP, sizeof(info->Ip));
        switch (tmpInfo.TesterType)
        {
        case TEST_TYPE_ENUM_WT448:
            strcpy(info->TesterType, "WT-448");
            break;
        case TEST_TYPE_ENUM_WT428:
            strcpy(info->TesterType, "WT-428");
            break;
        case TEST_TYPE_ENUM_WT418:
            strcpy(info->TesterType, "WT-328CE");
            break;
        default:
            strcpy(info->TesterType, "Unknown tester type");
            break;
        }
    }

    return iRet;
}


WTTESTER_DLL_API int WT_GetSlaveTesterLicense(int connID,
    int slaveTesterID,
    LicItemInfo_API *licInfo,
    int licenseMaxCount,
    int *licActualcount)
{
    A_ASSERT(licInfo && licActualcount);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSlaveTesterLicense(slaveTesterID, licInfo, licenseMaxCount, licActualcount);
}

WTTESTER_DLL_API int WT_GetSlaveTesterInfo(int connID,
    int slaveTesterID,
    TesterInfo *testerInfo)
{
    A_ASSERT(testerInfo);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSlaveTesterInfo(slaveTesterID, testerInfo);
}

WTTESTER_DLL_API int WT_GetDefaultParameter(int connID,
    VsaParameter *vsaParam,
    VsaAvgParameter *avgParam,
    VsgParameter *vsgParam,
    VsgWaveParameter *waveParam,
    VsgPattern *vsgPattern)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDefaultParameter(vsaParam, avgParam, vsgParam, waveParam, vsgPattern);
}

WTTESTER_DLL_API int WT_ClearSampRateFromFileFlag(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ClearSampRateFromFileFlag();
}

WTTESTER_DLL_API int WT_SetVSA(int connID, VsaParameter *vsaParam)
{
    A_ASSERT(vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSA(vsaParam, nullptr);
}

WTTESTER_DLL_API int WT_SetVSA_V2(int connID, VsaParameter *vsaParam, ExtendVsaParameter *extParam)
{
    A_ASSERT(vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSA(vsaParam, extParam);
}

WTTESTER_DLL_API int WT_SetVSAAverageParameter(int connID, VsaAvgParameter *avgParam)
{
    A_ASSERT(avgParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSAAverageParameter(avgParam);
}

WTTESTER_DLL_API int WT_ClrVSAAvgData(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ClrVSAAvgData();
}

WTTESTER_DLL_API int WT_SetVSATrigParam(int connID, VsaTrigParam* Param)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSATrigParam(Param);
}

WTTESTER_DLL_API int WT_GetVSATrigParam(int connID, VsaTrigParam* Param)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSATrigParam(Param);
}

WTTESTER_DLL_API int WT_SetVSAAutorange(int connID, VsaParameter *vsaParam)
{
    A_ASSERT(vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSAAutorange(vsaParam, nullptr);
}

WTTESTER_DLL_API int WT_SetVSAAutorange_V2(int connID, VsaParameter *vsaParam, ExtendVsaParameter *extParam)
{
    A_ASSERT(vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSAAutorange(vsaParam, extParam);
}

WTTESTER_DLL_API int WT_CheckConnectStatus(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetConnectStatus();
}
WTTESTER_DLL_API int WT_GetVSAParameter(int connID,
    int demodType,
    VsaParameter *vsaParam,
    void *analyzeParam,
    int paramSize)
{
    A_ASSERT(vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSAParameter(demodType, vsaParam, nullptr);
}
WTTESTER_DLL_API int WT_GetVSAParameter_V2(int connID,
    int demodType,
    VsaParameter *vsaParam,
    ExtendVsaParameter *extvsaParam)
{
    A_ASSERT(vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSAParameter(demodType, vsaParam, extvsaParam);
}

WTTESTER_DLL_API int WT_GetCurrVSAStatu(int connID, int *status)
{
    A_ASSERT(status);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetCurrVSAStatus(status);
}


WTTESTER_DLL_API int WT_SetGeneralAnalyzeParam(int connID, AlzParamComm *commonAnalyzeParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetGeneralAnalyzeParam(commonAnalyzeParam);
}

WTTESTER_DLL_API int WT_DataCapture(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->DataCapture();
}

WTTESTER_DLL_API int WT_DataCaptureAsync(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->DataCaptureAsync();
}

WTTESTER_DLL_API int WT_PauseDataCapture(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PauseDataCapture();
}

WTTESTER_DLL_API int WT_StopDataCapture(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StopDataCapture();
}

WTTESTER_DLL_API int WT_Analyze(int connID,
    int analyzeParamType,
    AnalyzeParam *analyzeParams,
    int paramsSize,
    const char *refFileName,
    int frameID,
    unsigned int timeoutMs)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->Analyze(analyzeParamType, analyzeParams, paramsSize, refFileName, frameID, timeoutMs);
}

WTTESTER_DLL_API int WT_SetAlzParam(int connID, int analyzeParamType, AnalyzeParam *analyzeParams, int paramsSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetAlzParam(analyzeParamType, analyzeParams, paramsSize);
}


WTTESTER_DLL_API int WT_SetExternAnalyzeParam(int connID, int demod, int ParamType, void *param, int paramSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetExternAnalyzeParam(demod, ParamType, param, paramSize);
}

WTTESTER_DLL_API int WT_GetResult(int connID,
    const char *anaParamString,
    double *result,
    int signalID,
    int segmentID)
{
    A_ASSERT(anaParamString && result);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    Logger::PrintDebug(__FUNCTION__, __LINE__, "anaParamString=%s\n", anaParamString);
    return wrapper->GetResult(anaParamString, result, signalID, segmentID);
}


WTTESTER_DLL_API int WT_GetBaseResult(int connID,
    VsaBaseResult *result,
    int signalID,
    int segmentID)
{
    A_ASSERT(result);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetBaseResult(result, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetVectorResultElementSize(int connID,
    const char *anaParamString,
    unsigned int *elementSize,
    int signalID,
    int segmentID, int SegNo)
{
    A_ASSERT(anaParamString && elementSize);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    int iret = wrapper->GetVectorResultElementSize(anaParamString, elementSize, signalID, segmentID, SegNo);
    Logger::PrintDebug(__FUNCTION__, __LINE__, "param=%s, result = %d\n", anaParamString, iret);
    return iret;
}

WTTESTER_DLL_API int WT_GetVectorResultElementCount(int connID,
    const char *anaParamString,
    unsigned int *elementCount,
    int signalID,
    int segmentID, int SegNo)
{
    A_ASSERT(anaParamString && elementCount);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    int iret = wrapper->GetVectorResultElementCount(anaParamString, elementCount, signalID, segmentID, SegNo);
    Logger::PrintDebug(__FUNCTION__, __LINE__, "param=%s, result = %d\n", anaParamString, iret);
    return iret;
}

WTTESTER_DLL_API int WT_GetVectorResult(int connID,
    const char *anaParamString,
    void *result,
    unsigned int resultSize,
    int signalID,
    int segmentID, int SegNo)
{
    A_ASSERT(anaParamString && result);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    //Logger::PrintDebug(__FUNCTION__, __LINE__, "Start param=%s, resultSize=%d\n", anaParamString, resultSize);
    int iret = wrapper->GetVectorResult(anaParamString, result, resultSize, signalID, segmentID, SegNo);
    Logger::PrintDebug(__FUNCTION__, __LINE__, "param=%s,signalID=%d,segmentID=%d, resultSize=%d, result = %d\n",
        anaParamString,
        signalID,
        segmentID,
        resultSize,
        iret);
    return iret;
}

WTTESTER_DLL_API int WT_SetVSGParameter(int connID, VsgParameter *vsgParam)
{
    A_ASSERT(vsgParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSGParameter(vsgParam, nullptr);
}

WTTESTER_DLL_API int WT_SetVSGParameter_V2(int connID, VsgParameter *vsgParam, ExtendVsgParameter *extParam)
{
    A_ASSERT(vsgParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSGParameter(vsgParam, extParam);
}

WTTESTER_DLL_API int WT_SetVSGWaveParameter(int connID, VsgWaveParameter *vsgWaveParam)
{
    A_ASSERT(vsgWaveParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSGWaveParameter(vsgWaveParam);
}

WTTESTER_DLL_API int WT_SetVSGPattern(int connID,
    VsgPattern *vsgPatternParam,
    unsigned int vsgPnItemCount,
    PnItemHead_API *vsgPnHead,
    unsigned int vsgPnHeadCount)
{
    A_ASSERT(vsgPatternParam &&
        vsgPatternParam->WaveName[0] &&
        strlen(vsgPatternParam->WaveName) > 0);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSGPattern(vsgPatternParam, vsgPnItemCount, vsgPnHead, vsgPnHeadCount);
}

WTTESTER_DLL_API int WT_GetVSGPattern(int connID,
    VsgPattern *vsgPattern,
    unsigned int vsgPnItemCount,
    PnItemHead_API *vsgPnHead,
    unsigned int vsgPnHeadCount,
    unsigned int *actualPnItemCount,
    unsigned int *actualPnHeadCount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSGPattern(vsgPattern,
        vsgPnItemCount,
        vsgPnHead,
        vsgPnHeadCount,
        actualPnItemCount,
        actualPnHeadCount);
}

WTTESTER_DLL_API int WT_GetVSGSendPacketCnt(int connID, int *Cnt)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSGSendPacketCnt(Cnt);
}

WTTESTER_DLL_API int WT_SetVSGFemParameter(int connID, FemParameter *param)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVSGFemParamter(param);
}

WTTESTER_DLL_API int WT_StartVSG(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StartVSG();
}

WTTESTER_DLL_API int WT_AsynStartVSG(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->AsynStartVSG();
}

WTTESTER_DLL_API int WT_GetCurrVSGStatu(int connID, int *statu)
{
    A_ASSERT(statu);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetCurrVSGStatu(statu);
}

WTTESTER_DLL_API int WT_PauseVSG(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PauseVSG();
}

WTTESTER_DLL_API int WT_StopVSG(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StopVSG();
}


WTTESTER_DLL_API int WT_DeleteTesterWaveFileOrRefFile(int connID, const char *fileName)
{
    A_ASSERT(fileName);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->DeleteTesterWaveFileOrRefFile(fileName);
}

WTTESTER_DLL_API int WT_AddTesterWaveFileOrRefFile(int connID,
    const char *fileName,
    const char *saveFileName,
    int acWave2)
{
    A_ASSERT(fileName && saveFileName);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->AddTesterWaveFileOrRefFile(fileName, saveFileName, acWave2);
}

WTTESTER_DLL_API int WT_GetTesterAllWaveFileOrRefFileNames(int connID,
    const char *path,
    char *fileNameBuffer,
    int fileNameBuffSize,
    unsigned int *fileCount)
{
    A_ASSERT(path && fileNameBuffer && fileCount);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterAllWaveFileOrRefFileNames(path, fileNameBuffer, fileNameBuffSize, fileCount);
}

WTTESTER_DLL_API int WT_QueryTesterWaveFileOrRefFile(int connID,
    const char *fileName,
    int *waveExists)
{
    A_ASSERT(fileName && waveExists);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryTesterWaveFileOrRefFile(fileName, waveExists);
}

WTTESTER_DLL_API int WT_GetTesterWaveFileOrRefFileSize(int connID,
    const char *fileName,
    unsigned int *fileSize)
{
    A_ASSERT(fileName && fileSize);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterWaveFileOrRefFileSize(fileName, fileSize);

}

WTTESTER_DLL_API int WT_GetTesterWaveFileOrRefFile(int connID,
    const char *fileName,
    char *fileBuff,
    unsigned int fileBuffSize)
{
    A_ASSERT(fileName && fileBuff);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterWaveFileOrRefFile(fileName, fileBuff, fileBuffSize);
}

WTTESTER_DLL_API int WT_BeamformingCalibrationChannelEstDutTX(int connID, int demod)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingCalibrationChannelEstDutTX(demod);
}

WTTESTER_DLL_API int WT_BeamformingCalibrationChannelEstDutRX(int connID, double *dutChannelEst, int dutChannelEstLength)
{
    A_ASSERT(dutChannelEst);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingCalibrationChannelEstDutRX(dutChannelEst, dutChannelEstLength);
}

WTTESTER_DLL_API int WT_BeamformingCalibrationResult(int connID, double *resultAngle, int *resultAngleLength)
{
    A_ASSERT(resultAngle && resultAngleLength);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingCalibrationResult(resultAngle, resultAngleLength);
}

WTTESTER_DLL_API int WT_BeamformingVerification(int connID, double *diffPower)
{
    A_ASSERT(diffPower);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingVerification(diffPower);
}

WTTESTER_DLL_API int WT_BeamformingCalculateChannelProfile_V2(int connID, int demod, double *resultBuf, int *resultLength)
{
    A_ASSERT(resultBuf);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingCalculateChannelProfile(demod, resultBuf, resultLength);
}

WTTESTER_DLL_API int WT_BeamformingCalculateChannelProfile(int connID, int demod, double *resultBuf, int resultLength)
{
    A_ASSERT(resultBuf);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingCalculateChannelProfile(demod, resultBuf, &resultLength);
}

WTTESTER_DLL_API int CALL_MODE WT_BeamformingCalculateChannelAmplitudeandAngle_BCM(int connID, int *ValidSpatialStreams, int *DataLen, double *resultBuf, int *resultLength)
{
    A_ASSERT(resultBuf);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeamformingCalculateChannelAmplitudeandAngleBCM(ValidSpatialStreams, DataLen, resultBuf, resultLength);
}

#ifndef LINUX
WTTESTER_DLL_API int WT_CablelossCal(int connID,
    CalTestingInAgrs *calParameter,
    void(__stdcall *pfunCallBack)(CalTestingCallbackArgs callbackArgs))
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->CablelossCal(calParameter, pfunCallBack);
}
#endif

WTTESTER_DLL_API int WT_DemoConnect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    return TesterManager::TesterConnect(connUnit,
        unitCount,
        connID,
        WT_CONNECT_TYPE_DEMO);
}

//************************************************************************
//                      METER(METER特有接口)
//************************************************************************

WTTESTER_DLL_API int WT_MoniConnect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    A_ASSERT(connUnit && connID);
    return TesterManager::TesterConnect(connUnit,
        unitCount,
        connID,
        WT_CONNECT_TYPE_MONITOR);
}


WTTESTER_DLL_API int WT_SetMoniObj(int connID, int obj)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetMoniObj(obj);
}

WTTESTER_DLL_API int WT_MoniConfig(int connID,
    int action,
    int dataType,
    const char *anaParamString)
{
    //A_ASSERT(anaParamString);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->MoniConfig(action, dataType, anaParamString);
}



WTTESTER_DLL_API int WT_GetMoniResultSize(int connID, unsigned int *dataSize)
{
    A_ASSERT(dataSize);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetMoniResultSize(dataSize);
}


WTTESTER_DLL_API int WT_GetMoniResult(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    int *dataType)
{
    A_ASSERT(resultBuff && dataType);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetMoniResult(resultBuff, resultBuffSize, dataType);
}

WTTESTER_DLL_API int WT_QueryMoniVSAParam(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    VsaParameter *vsaParam)
{
    A_ASSERT(resultBuff && vsaParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryMoniVSAParam(resultBuff, resultBuffSize, vsaParam);
}

WTTESTER_DLL_API int WT_QueryMoniVSGParam(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    VsgParameter *vsgParam)
{
    A_ASSERT(resultBuff && vsgParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryMoniVSGParam(resultBuff, resultBuffSize, vsgParam);
}

WTTESTER_DLL_API int WT_QueryMoniVSAAnalyzeParam(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    AlzParamComm *commonAnalyzeParam,
    void *analyzeParam,
    unsigned int paramSize,
    int *signalType)
{
    A_ASSERT(resultBuff && commonAnalyzeParam && analyzeParam && signalType);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryMoniVSAAnalyzeParam(resultBuff,
        resultBuffSize,
        commonAnalyzeParam,
        analyzeParam,
        paramSize,
        signalType);
}

WTTESTER_DLL_API int WT_QueryMoniVSGPattern(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    VsgPattern *vsgPattern,
    unsigned int vsgPnItemCount,
    PnItemHead_API *vsgPnHead,
    unsigned int vsgPnHeadCount,
    unsigned int *actualPnItemCount,
    unsigned int *actualPnHeadCount)
{
    A_ASSERT(resultBuff);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryMoniVSGPattern(resultBuff,
        resultBuffSize,
        vsgPattern,
        vsgPnItemCount,
        vsgPnHead,
        vsgPnHeadCount,
        actualPnItemCount,
        actualPnHeadCount);
}


WTTESTER_DLL_API int WT_QueryMoniVSAResult(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    const char *anaParamString,
    int signalID,
    int segmentID,
    char **dataPtr,
    unsigned int *elementCount,
    unsigned int *elementSize)
{
    A_ASSERT(resultBuff && anaParamString && elementCount && elementSize);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryMoniVSAResult(resultBuff,
        resultBuffSize,
        anaParamString,
        signalID,
        segmentID,
        dataPtr,
        elementCount,
        elementSize);
}




WTTESTER_DLL_API int WT_StartRecord(int connID, bool isLocalSave)
{
    return 0;
}

WTTESTER_DLL_API int WT_FinishRecord(int connID)
{
    return 0;
}

WTTESTER_DLL_API int WT_GetTesterAllRecordNames(int connID,
    char *recordNameBuffer,
    int recordNameBufferSize,
    int *recordCount)
{
    return 0;
}

WTTESTER_DLL_API int WT_ReadRecord(int connID, char *recordName, char *Data)
{
    return 0;
}



// WTTESTER_DLL_API int WT_GetGUIFileSize( int connID,int demodType,int *guiSize )
// {
//  return 0;
// }


WTTESTER_DLL_API int WT_GetGUIFileVersion(int connID, const char *techName, GUIVersion *guiVersion)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetGUIFileVersion(techName, guiVersion);
}

WTTESTER_DLL_API int WT_GetGUIVersion(int connID,
    GUIVersion *guiVersion,
    unsigned int maxGuiVersionCnt,
    unsigned int *actualGuiVersionCnt)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetGUIVersion(guiVersion, maxGuiVersionCnt, actualGuiVersionCnt);
}

WTTESTER_DLL_API int WT_GetGUIFile(int connID,
    const char *techName, const char *saveDir)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetGUIFile(techName, saveDir);
}

WTTESTER_DLL_API int WT_GetMonitorInfos(int connID,
    MonitorInfo *monitorInfo,
    unsigned int maxMonInfoCount,
    unsigned int *actualMonInfoCount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetMonitorInfos(monitorInfo, maxMonInfoCount, actualMonInfoCount);
}

WTTESTER_DLL_API int WT_LoadSignalAsCapture(int connID,
    const char *fileName,
    const char *saveFileName,
    int acWave2)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->LoadSignalAsCapture(fileName, saveFileName, acWave2);
}

WTTESTER_DLL_API int WT_SaveSignal(int connID, const char *fileName)
{
    return WT_SaveSignal_V2(connID, WT_SAVE_COMPENSATED_DATA, fileName);
}

WTTESTER_DLL_API int WT_SaveCurrIQDataToFile(int connID,
    int saveType,
    const char *fileName,
    char *calParam,
    unsigned int calParamSize,
    int signalID)
{
    (void)calParam;
    (void)calParamSize;
    (void)signalID;
    return WT_SaveSignal_V2(connID, saveType, fileName);
}


WTTESTER_DLL_API int WT_AnalyzeVSGData(int connID,
    const char *waveName,
    int analyzeParamType,
    AnalyzeParam *analyzeParams,
    int paramsSize, 
    int timeOutMs)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->AnalyzeVSGData(waveName, analyzeParamType, analyzeParams, paramsSize, timeOutMs);
}

WTTESTER_DLL_API int WT_GetVSGDataResultElementSize(int connID,
    const char *anaParamString,
    unsigned int *elementSize,
    int signalID,
    int segmentID)
{
    A_ASSERT(anaParamString && elementSize);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSGDataResultElementSize(anaParamString, elementSize, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetVSGDataResultElementCount(int connID,
    const char *anaParamString,
    unsigned int *elementCount,
    int signalID,
    int segmentID)
{
    A_ASSERT(anaParamString && elementCount);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSGDataResultElementCount(anaParamString, elementCount, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetVSGDataVectorResult(int connID,
    const char *anaParamString,
    void *result,
    unsigned int resultSize,
    int signalID,
    int segmentID)
{
    A_ASSERT(anaParamString && result);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVSGDataVectorResult(anaParamString, result, resultSize, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetRefLvlRange(int connID,
    double freq,
    int option,
    int *upperLimit,
    int *lowerLimit)
{
    A_ASSERT(upperLimit && lowerLimit);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetRefLvlRange(freq, option, upperLimit, lowerLimit);
}

WTTESTER_DLL_API int WT_GetTxPowerRange(int connID,
    double freq,
    int option,
    int *upperLimit,
    int *lowerLimit)
{
    A_ASSERT(upperLimit && lowerLimit);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTxPowerRange(freq, option, upperLimit, lowerLimit);
}



WTTESTER_DLL_API int WT_GetCurrSubTesterCfg(int connID, SubTesterCfg *testerCfg)
{
    A_ASSERT(testerCfg);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetCurrSubTesterCfg(testerCfg);
}

WTTESTER_DLL_API  int WT_GetSlaveTesterSubTesterCfg(int connID,
    int slaveTesterID,
    SubTesterCfg *testerCfg)
{
    A_ASSERT(testerCfg);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSlaveTesterSubTesterCfg(slaveTesterID, testerCfg);
}


WTTESTER_DLL_API int WT_GetTesterErrorCode(int connID,
    int *errCode,
    char *errMsg,
    unsigned int errMsgSize)
{
    A_ASSERT(errCode && errMsg);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterErrorCode(errCode, errMsg, errMsgSize);
}


WTTESTER_DLL_API int WT_SetMasterMode(int connID,
    int vsaMasterMode,
    int vsgMasterMode)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetMasterMode(vsaMasterMode, vsgMasterMode);
}

//WTTESTER_DLL_API int WT_DemoConnect( ConnectedUnit *connUnit,int *connID )
//{
//  return 0;
//}


//************************************************************************
//                      ADMIN(ADMIN TOOL特有接口)
//************************************************************************


//WTTESTER_DLL_API int WT_ScanConnect( char *ipAddr,int *connID )
//{
//  return 0;
//}
//
//WTTESTER_DLL_API int WT_UpgradeConnect( char *ipAddr,int *connID )
//{
//  return 0;
//}

WTTESTER_DLL_API int WT_SubConnect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    A_ASSERT(connUnit && connID);
    //默认配置为多连接
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_MultiUser);
}

WTTESTER_DLL_API int WT_DialogConnect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_DIALOG);
}

WTTESTER_DLL_API int WT_ManageConnect(ConnectedUnit *connUnit,
    int unitCount,
    int *connID)
{
    return TesterManager::TesterConnect(connUnit, unitCount, connID, WT_CONNECT_TYPE_MANAGE);
}

WTTESTER_DLL_API int WT_QuerySpecTester(const char *ipAddr,
    TesterOverview *testerInfo)
{
    A_ASSERT(ipAddr && testerInfo);

    ConnectedUnit connUnit;
    int connID = -1;
    int iRet = WT_ERR_CODE_OK;
    memset(&connUnit, 0, sizeof(connUnit));
    strcpy(connUnit.Ip, ipAddr);
    connUnit.SubTesterIndex = WT_SUB_TESTER_INDEX_AUTO;
    iRet = TesterManager::TesterConnect(&connUnit, 1, &connID, WT_CONNECT_TYPE_NORMAL);
    if (WT_ERR_CODE_OK == iRet)
    {
        iRet = WT_GetTesterInfo_V2(connID, testerInfo);
        WT_DisConnect(connID);
    }
    else
    {
        iRet = TesterManager::QuerySpecTester(ipAddr, testerInfo);
    }
    return iRet;
}

WTTESTER_DLL_API int WT_QuerySpecTester_V2(const char *ipAddr,
    ExternedTesterOverview *testInfo)
{
    return TesterManager::QuerySpecTester(ipAddr, testInfo);
}

WTTESTER_DLL_API int WT_ScanTesters(TesterOverview *info,
    int testerMaxCount,
    int *testerActualCount)
{
    return TesterManager::ScanTesters(info, testerMaxCount, testerActualCount);
}

WTTESTER_DLL_API int WT_RebootTester(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->RebootTester();
}

WTTESTER_DLL_API int WT_GetSubTesterCfg(int connID,
    SubTesterCfg testerCfg[WT_SUB_TESTER_INDEX_MAX],
    int *subTesterCount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSubTesterCfg(testerCfg, subTesterCount);
}

WTTESTER_DLL_API int WT_SetSubTesterCfg(int connID,
    SubTesterCfg *testerCfg,
    int testerCount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetSubTesterCfg(testerCfg, testerCount);
}

WTTESTER_DLL_API int WT_FirmwareUpdate(int connID,
    const char *packageFilePath,
    char *upgradePackage,
    unsigned int packageSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->FirmwareUpdate(packageFilePath, upgradePackage, packageSize);
}

WTTESTER_DLL_API int WT_LicenseUpdate(int connID,
    char *licensePackage,
    unsigned int packageSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->LicenseUpdate(licensePackage, packageSize);
}

WTTESTER_DLL_API int WT_LicensePack(int connID,
    char *licensePackage,
    unsigned int packageSize,
    LicItemInfo_API *licInfo,
    unsigned int *actualSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->LicensePack(licensePackage, packageSize, licInfo, actualSize);
}

WTTESTER_DLL_API int WT_LicPackUpdate(int connID, char *Message)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->LicPackUpdate(Message);
}

WTTESTER_DLL_API int WT_SetTesterInfo(int connID,
    const char *name,
    const char *ipAddr,
    const char *subMask,
    const char *netGate)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetTesterInfo(name, ipAddr, subMask, netGate);
}

WTTESTER_DLL_API int WT_FirmwareRestore(int connID, int restoreOption)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->FirmwareRestore(restoreOption);
}

WTTESTER_DLL_API int WT_GetTesterSystemTime(int connID, TIME_TYPE_API *sysTime)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTesterSystemTime(sysTime);
}

static tm StringToDatetime(char *str)
{
    tm tmp;
    int year, month, day, hour, minute, second;
    sscanf(str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    tmp.tm_year = year - 1900;
    tmp.tm_mon = month - 1;
    tmp.tm_mday = day;
    tmp.tm_hour = hour;
    tmp.tm_min = minute;
    tmp.tm_sec = second;
    tmp.tm_isdst = 0;
    return tmp;
}

WTTESTER_DLL_API int WT_FactoryReset(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->FactoryReset();
}

WTTESTER_DLL_API int WT_GetTesterLogSettingInfo(int connID, int *log_cfg, unsigned int size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    A_ASSERT(wrapper && log_cfg);

    return wrapper->TesterLogSettingInfo(0, log_cfg, size);
}

WTTESTER_DLL_API int CALL_MODE WT_SetTesterLogSettingInfo(int connID, int *log_cfg, unsigned int size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    A_ASSERT(wrapper && log_cfg);

    return wrapper->TesterLogSettingInfo(1, log_cfg, size);
}

WTTESTER_DLL_API int WT_ReadDialogLog(int connID,
    DialogLogFilter *filter,
    char *filterLog,
    int logMaxBuff)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    A_ASSERT(wrapper && filter);

    LogFilter tmpfilter;
    memset(&tmpfilter, 0, sizeof(LogFilter));

    tmpfilter.Logtype = filter->Logtype;
    if (LOG_QUERY_TIME == filter->Logtype)
    {
        if (filter->Starttime[0] == '\0' || filter->Endtime[0] == '\0')
        {
            return WT_ERR_CODE_PARAMETER_MISMATCH;
        }
        tmpfilter.Starttime = StringToDatetime(filter->Starttime);
        tmpfilter.Endtime = StringToDatetime(filter->Endtime);
    }
    memcpy(tmpfilter.SQL, filter->SQL, sizeof(filter->SQL));

    return WT_ReadLog(connID, &tmpfilter, filterLog, logMaxBuff);
}

WTTESTER_DLL_API int WT_ReadLog(int connID,
    LogFilter *filter,
    char *filterLog,
    int logMaxBuff)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ReadLog(filter, filterLog, logMaxBuff);
}

WTTESTER_DLL_API int WT_Diagnose(int connID,
    DiagnoseSetting setting,
    char *diaInfo,
    unsigned int infoSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->Diagnose(setting, diaInfo, infoSize);
}

WTTESTER_DLL_API int WT_AnalyzeDiagnoseLog(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->AnalyzeDiagnoseLog();
}

WTTESTER_DLL_API int WT_GetFpgaConfig(int connID, FpgaConfig *config)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetFpgaConfig(config);
}

WTTESTER_DLL_API int WT_GetOSStatus(int connID, OSStatu *osStatu)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetOSStatus(osStatu);
}

WTTESTER_DLL_API int WT_GetProcStatus(int connID, ProcStatu *procStatu)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetProcStatus(procStatu);
}

WTTESTER_DLL_API int WT_ShutDownDevice(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ShutDownDevice();
}

WTTESTER_DLL_API int WT_DeleteAllLicense(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->DeleteAllLicense();
}

WTTESTER_DLL_API int WT_ResetSubNetConfiguration(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ResetSubNetConfiguration();
}

WTTESTER_DLL_API int WT_SetMeterConfiguration(int connID, char *config, unsigned int size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetMeterConfiguration(config, size);
}

WTTESTER_DLL_API int WT_QueryMeterConfiguration(int connID,
    char *resultBuff,
    unsigned int resultBuffSize,
    char *ConfigParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryMeterConfiguration(resultBuff, resultBuffSize, ConfigParam);
}

WTTESTER_DLL_API int WT_GetPnDescription(int connID, char *fileName, char *description, unsigned int size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetPnDescription(fileName, description, size);
}

WTTESTER_DLL_API int WT_MemDump()
{
    TesterManager::memdump();
    return WT_ERR_CODE_OK;
}

#pragma region Cal（生产校准工具特有部分）

//************************************************************************
//                      Cal（生产校准工具特有部分）
//************************************************************************


WTTESTER_DLL_API int WT_SendCalFile(int connID, char *fileBuffer, unsigned int fileBuffSize, const char *fileName)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SendCalFile(fileBuffer, fileBuffSize, fileName);
}

WTTESTER_DLL_API int WT_GetCalFile(int connID, const char *fileName, char *fileBuffer, unsigned int fileBuffSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetCalFile(fileName, fileBuffer, fileBuffSize);
}

WTTESTER_DLL_API int WT_SetPathLossFile(int connID, const char *fileName)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetPathLossFile(fileName);
}

WTTESTER_DLL_API int WT_GetPathLossFileSize(int connID, unsigned int *fileSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetPathLossFileSize(fileSize);

}

WTTESTER_DLL_API int WT_GetPathLossFile(int connID, const char *fileName, unsigned int fileSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetPathLossFile(fileName, fileSize);

}

WTTESTER_DLL_API int WT_SetCalData(int connID, char *data, unsigned int dataSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetCalData(data, dataSize);
}

WTTESTER_DLL_API int WT_GetCalData(int connID, char *data, unsigned dataSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetCalData(data, dataSize);
}


WTTESTER_DLL_API int WT_SetBBGain(int connID, int gain)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetBBGain(gain);
}

WTTESTER_DLL_API int WT_GetTemperature(int connID, DevTemperature *temp)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTemperature(temp);
}

WTTESTER_DLL_API int WT_GetTemperatureHistory(int connID, int *Cnt, DevTempSave *info, unsigned int infoSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTemperatureHistory(Cnt, info, infoSize);
}

WTTESTER_DLL_API int WT_SetTempCal(int connID, int value)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetTempCal(value);
}

WTTESTER_DLL_API int WT_SetFlatnessCal(int connID, int value)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetFlatnessCal(value);
}

WTTESTER_DLL_API int WT_GetVoltage(int connID, DeviceVoltage *voltage, int voltageCnt, int *count)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVoltage(voltage, voltageCnt, count);
}

WTTESTER_DLL_API int WT_GetFanSpeed(int connID, int *speed)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetFanSpeed(speed);
}

WTTESTER_DLL_API int WT_WriteSN(int connID, const char *password, const char *SN)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WriteSN(password, SN);
}

WTTESTER_DLL_API int WT_SetATT(int connID, double *att)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetATT(att);
}

WTTESTER_DLL_API int WT_GetATT(int connID, double *att)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetATT(att);
}


WTTESTER_DLL_API int WT_SetComponentValue(int connID, ComponentLocation componentLocation, int componentValue)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetComponentValue(componentLocation, componentValue);
}

WTTESTER_DLL_API int WT_GetComponentValue(int connID, ComponentLocation componentLocation, char *componentValue, int bufSize, int *dataLen)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetComponentValue(componentLocation, componentValue, bufSize, dataLen);
}


WTTESTER_DLL_API int WT_InitCalData(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->InitCalData();
}

WTTESTER_DLL_API int WT_SetFpgaIFG(int connID, int ON_OFF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetFPGAIFG(ON_OFF);
}

WTTESTER_DLL_API int WT_GetFpgaIFG(int connID, int *ON_OFF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetFPGAIFG(ON_OFF);
}

WTTESTER_DLL_API int WT_SaveOriginalIQDataToFile(int connID, const char *fileName, char *calParam /*= nullptr*/, unsigned int calParamSize /*= 0*/, int signalID /*= 0*/)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SaveOriginalIQDataToFile(fileName, calParam, calParamSize, signalID);
}


WTTESTER_DLL_API int WT_GetAverageResult(int connID, VsaAverageResult *result, VsaAverageResult *max, VsaAverageResult *min, int times/*=0*/, int signalID/*=0*/, int segmentID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetAverageResult(result, max, min, times, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetSLEAverageResult(int connID, VsaSLECommResult* result, VsaSLECommResult* max, VsaSLECommResult* min, int times/*=0*/, int signalID/*=0*/, int segmentID)
{
	TesterWrapper* wrapper = GetUsableWrapper(connID);
	VALID_WRAPPER_ID(wrapper);
	return wrapper->GetSLEAverageResult(result, max, min, times, signalID, segmentID);
}

WTTESTER_DLL_API int WT_Get3GPPAverageResult(int connID, Vsa3GPPCommResult* result, Vsa3GPPCommResult* max, Vsa3GPPCommResult* min, int times/*=0*/, int signalID/*=0*/, int segmentID)
{
	TesterWrapper* wrapper = GetUsableWrapper(connID);
	VALID_WRAPPER_ID(wrapper);
	return wrapper->Get3GPPAverageResult(result, max, min, times, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetBTAverageResult(int connID, VsaBTCommResult* result, VsaBTCommResult* max, VsaBTCommResult* min, int times/*=0*/, int signalID/*=0*/, int segmentID)
{
	TesterWrapper* wrapper = GetUsableWrapper(connID);
	VALID_WRAPPER_ID(wrapper);
	return wrapper->GetBTAverageResult(result, max, min, times, signalID, segmentID);
}

WTTESTER_DLL_API int WT_GetAvgBaseCompositeResult(int connID, VsaBaseResult *result, int segmentID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetAvgBaseCompositeResult(result, segmentID);
}

WTTESTER_DLL_API int WT_GetCurrAverageCount(int connID, int *count)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetCurrAverageCount(count);
}

WTTESTER_DLL_API int WT_CalculateImbalance(int connID, double *imbAmp, double *imbPhase, double *timeSkew, int segmentID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->CalculateImbalance(imbAmp, imbPhase, timeSkew, segmentID);
}

WTTESTER_DLL_API int WT_SetFixedImbalance(int connID, int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetFixedImbalance(enable, imbAmp, imbPhase, timeSkew, segmentID);
}

WTTESTER_DLL_API int WT_SetVsgFixedImbalance(int connID, int enable, double imbAmp, double imbPhase, double timeSkew, int segmentID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVsgFixedImbalance(enable, imbAmp, imbPhase, timeSkew, segmentID);
}

WTTESTER_DLL_API int WT_SetCmimoRefFile(int connID, const char *fileName)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetCmimoRefFile(fileName);
}

WTTESTER_DLL_API int WT_WriteLocalLog(int logType, const char *keyString, const char *content)
{
    if (!content)
    {
        return WT_ERR_CODE_OK;
    }
    return Logger::WriteKeyLog(logType, keyString, content);
}

WTTESTER_DLL_API int WT_GetHardErrorInfoSize(int connID, int  *errorCodeAcount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetHardErrorCodeSize(errorCodeAcount);
}

WTTESTER_DLL_API int WT_GetSlaveHardErrorInfoSize(int connID, int slaveTesterID, int  *errorCodeAcount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSlaveHardErrorCodeSize(slaveTesterID, errorCodeAcount);
}

WTTESTER_DLL_API int WT_GetHardErrorCode(int connID, int *hardErrCode, int errorCodeAcount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetHardErrorCode(hardErrCode, errorCodeAcount);
}

WTTESTER_DLL_API int WT_GetSlaveHardErrorCode(int connID, int *hardErrCode, int slaveTesterID, int errorCodeAcount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSlaveHardErrorCode(hardErrCode, slaveTesterID, errorCodeAcount);
}

WTTESTER_DLL_API int WT_GetFileSampleFreq(int connID, double *SampleFreq)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetFileSampleFreq(SampleFreq);
}

WTTESTER_DLL_API int WT_GetRxGain(int connID, int ModuleID, char *buf, unsigned int bufSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetRxGain(ModuleID, buf, bufSize);
}

WTTESTER_DLL_API int WT_GetTxGain(int connID, int ModuleID, char *buf, unsigned int bufSize)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetTxGain(ModuleID, buf, bufSize);
}


WTTESTER_DLL_API int WT_ReadRemoteFile(int connID, const char *filename, unsigned int *filesize, char *buffer, unsigned int buffer_size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ReadRemoteFile(filename, filesize, buffer, buffer_size);
}

WTTESTER_DLL_API int WT_WriteRemoteFile(int connID, const char *filename, char *buffer, unsigned int buffer_size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WriteRemoteFile(filename, buffer, buffer_size);
}

WTTESTER_DLL_API int WT_ExecShellCmd(int connID, const char *cmd, char *buffer, unsigned int buffer_size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->ExecShellCmd(cmd, buffer, buffer_size);
}
#pragma endregion

#pragma region MISC_APP
WTTESTER_DLL_API int WT_GenSensetivityWave(int connID, PerMacParameter *MacParameter, char *WaveName)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GenSensetivityWave(MacParameter, WaveName);
}

WTTESTER_DLL_API int WT_GetSensetivityResult(int connID, int demod, int *AckCnt)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSensetivityResult(demod, AckCnt);
}

WTTESTER_DLL_API int WT_StartPerTesting(int connID, PerMacParameter *MacParameter, PerActionParameter *perParameter, void(*pfunCallback)(PerResultParameter progress))
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StartPerTesting(MacParameter, perParameter, pfunCallback);
}

WTTESTER_DLL_API int WT_StopPerTesting(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StopPerTesting();
}

WTTESTER_DLL_API int WT_SetAnalyzeGroupParam(int connID, char *anaParamString)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetAnalyzeGroupParam(anaParamString);
}

WTTESTER_DLL_API int WT_SaveSignal_V2(int connID, int type, const char *fileName, int startUs, int endUs)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TruncateSaveSignal(type, fileName, startUs, endUs);
}

WTTESTER_DLL_API int WT_MoniSaveSignal(int connID, int type, const char *fileName, int startUs, int endUs)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->MoniTruncateSaveSignal(type, fileName, startUs, endUs);
}

WTTESTER_DLL_API int WT_QueryTesterVersionInfo(int connID, char *testerInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryTesterVersionInfo(testerInfo);
}

WTTESTER_DLL_API int WT_PacStartGetData(int connID, PacParameter *param, PacAttribute *attribute, void(*pfunCallback)(PacProgressParameter *progress))
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PacStartGetData(param, attribute, pfunCallback);
}

WTTESTER_DLL_API int WT_PacStop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PacStop();
}

WTTESTER_DLL_API int WT_PacCalData(int connID, PacDataAvg *buf, int BufCnt, int *retCnt)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PacCalData(buf, BufCnt, retCnt);
}

WTTESTER_DLL_API int WT_PacDumpResult(int connID, int mode)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PacDumpResult(mode);
}

WTTESTER_DLL_API int WT_PacGetModeCalData(int connID, int mode, int *Cnt, PacDataInfo **data)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->PacGetModeCalData(mode, Cnt, data);
}

WTTESTER_DLL_API int WT_SetWifiFrameFilter(int connID, ResultFilter *filter)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetWifiFrameFilter(filter);
}

WTTESTER_DLL_API int WT_SetWideSpectrumEnable(int connID, int onff)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetWideSpectrumEnable(onff);
}

WTTESTER_DLL_API int WT_GetVSGParameter(int connID, VsgParameter *vsgParam, VsgWaveParameter *vsgWaveParam, VsgPattern *vsgPattern)
{
    A_ASSERT(vsgParam);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->GetVSGParam(vsgParam, vsgWaveParam, vsgPattern, nullptr);
}

WTTESTER_DLL_API int WT_GetVSGParameter_V2(int connID, VsgParameter* vsgParam, ExtendVsgParameter* extParam)
{
    A_ASSERT(vsgParam);
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->GetVSGParam(vsgParam, nullptr, nullptr, extParam);
}

WTTESTER_DLL_API int WT_TB_Init(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TB_Init();
}

WTTESTER_DLL_API int WT_TB_Release(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TB_Release();
}

WTTESTER_DLL_API int WT_TB_Start(int connID, int timeout_ms)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TB_Start(timeout_ms);
}

WTTESTER_DLL_API int WT_TB_Stop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TB_Stop();
}

WTTESTER_DLL_API int WT_TB_Status(int connID, int *status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TB_Status(status);
}

WTTESTER_DLL_API int WT_TB_SetParam(int connID, InterBindParameter *Param)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TB_SetParam(Param, WT_TRIG_TYPE_FREE_RUN_API);
}

WTTESTER_DLL_API int WT_TB_AutoRange(int connID, InterBindParameter *Param, int timeout_ms)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    int err = wrapper->TB_AutoRange(Param);
    // if (err == WT_ERR_CODE_OK) //交互模式中鉴于治具和仪表需要同步，所以在AGC后不自动启动VSA
    // {
    //     return wrapper->TB_Start(timeout_ms);
    // }
    return err;
}

WTTESTER_DLL_API int CALL_MODE WT_TF_Init(int connID)
{
    return WT_TB_Init(connID);
}

WTTESTER_DLL_API int CALL_MODE WT_TF_Release(int connID)
{
    return WT_TB_Release(connID);
}

WTTESTER_DLL_API int CALL_MODE WT_TF_Start(int connID, int timeout_ms)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TF_Start(timeout_ms);
}

WTTESTER_DLL_API int CALL_MODE WT_TF_Stop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TF_Stop();
}

WTTESTER_DLL_API int CALL_MODE WT_TF_Status(int connID, int *status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TF_GetStatus(status);
}

WTTESTER_DLL_API int CALL_MODE WT_TF_SetParam(int connID, InterBindParameter *Param)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TF_SetParam(Param, WT_TRIG_TYPE_IF_API);
}

WTTESTER_DLL_API int WT_SelfCalibration(int connID, int isStart)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SelfCalibration(0 == isStart ? false : true);
}

WTTESTER_DLL_API int WT_QuerySelfCalibrationPercent(int connID, int *percent)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QuerySelfCalibrationPercent(percent);
}

WTTESTER_DLL_API int WT_SelfCalibrationAutoRunning(int connID, int isAutoRun)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SelfCalibrationAutoRunning(isAutoRun);
}

WTTESTER_DLL_API int WT_PacSectionParameter(int connID, PacSectionParam *param)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->PacSectionParameter(param);
}

WTTESTER_DLL_API int WT_AxValidCommonBit(int connID, int demod, int *commonBit, int *userInRU, int *RUCnt, int *center26RU)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->AxValidCommonBit(demod, commonBit, userInRU, RUCnt, center26RU, nullptr);
}

WTTESTER_DLL_API int WT_AxValidCommonBit_V2(int connID, int demod, int *commonBit, int *userInRU, int *RUCnt, int *center26RU, int *RUPos)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->AxValidCommonBit(demod, commonBit, userInRU, RUCnt, center26RU, RUPos);
}

WTTESTER_DLL_API int WT_BeValidCommonBit(int connID, int demod, int fullBand, int *commonBits, int *Punctured, int *userInRU, int *TotalRU, int *RUPos)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->BeValidCommonBit(demod, fullBand, commonBits, Punctured, userInRU, TotalRU, RUPos);
}

WTTESTER_DLL_API int WT_File2TBParam(int connID, int ParamType, void *src_param, int paramSize, AlzParamAxTriggerBase *dest)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->File2TBParam(ParamType, src_param, paramSize, dest);
}

WTTESTER_DLL_API int WT_File2SLEParam(int connID, int ParamType, void* src_param, int paramSize, AlzParamSparkLink* dest)
{
	TesterWrapper* wrapper = GetUsableWrapper(connID);
	VALID_WRAPPER_ID(wrapper);
	return wrapper->File2SLEParam(ParamType, src_param, paramSize, dest);
}

WTTESTER_DLL_API int WT_FileRefParam(int connID, int ParamType, void *src_param, int paramSize, AnalyzeParam *dest)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->FileRefParam(ParamType, src_param, paramSize, dest);
}

WTTESTER_DLL_API int WT_GetDefaultWaveParameterBT(int connID, GenWaveBtStruct_API *pnParameters)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDefaultWaveParameterBT(pnParameters);
}

WTTESTER_DLL_API int WT_GetDefaultWaveParameterBTV2(int connID, GenWaveBtStructV2 *pnParameters)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDefaultWaveParameterBTV2(pnParameters);
}

WTTESTER_DLL_API int WT_GetDefaultWaveParameterCW(int connID, GenWaveCwStruct *pnParameters)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDefaultWaveParameterCW(pnParameters);
}

WTTESTER_DLL_API int WT_GetDefaultWaveParameterWifi(int connID, int demod, int ppdu, GenWaveWifiStruct_API *pnParameters)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDefaultWaveParameterWifi(demod, ppdu, pnParameters);
}

WTTESTER_DLL_API int WT_GetDefaultWaveParameterGLE(int connID, GenWaveGleStruct* pnParameters)
{
	TesterWrapper* wrapper = GetUsableWrapper(connID);
	VALID_WRAPPER_ID(wrapper);
	return wrapper->GetDefaultWaveParameterGLE(pnParameters);
}

WTTESTER_DLL_API int WT_GetDefaultWaveParameterWiSun(int connID, GenWaveWisunStruct* pnParameters)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDefaultWaveParameterWiSun(pnParameters);
}

WTTESTER_DLL_API int WT_WaveGeneratorCatenateFiles(int connID, const MutiPNCatenateInfo *catenateInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGeneratorCatenateFiles(catenateInfo);
}

WTTESTER_DLL_API int WT_WaveGeneratorWifi(int connID, const char *fileName, GenWaveWifiStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGeneratorWifi(fileName, pnParameters, MutiPnExInfo);
}

WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorSLE(int connID, const char *fileName, GenWaveGleStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
	TesterWrapper *wrapper = GetUsableWrapper(connID);
	VALID_WRAPPER_ID(wrapper);
	return wrapper->WaveGeneratorGLE(fileName, pnParameters, MutiPnExInfo);
}

WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorBT(int connID, const char *fileName, GenWaveBtStruct_API *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGeneratorBlueTooth(fileName, pnParameters, MutiPnExInfo);
}

WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorBTV2(int connID, const char *fileName, GenWaveBtStructV2 *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGeneratorBlueToothV2(fileName, pnParameters, MutiPnExInfo);
}

WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorCW(int connID, const char *fileName, GenWaveCwStruct *pnParameters, MutiPNExtendInfo *MutiPnExInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGeneratorCW(fileName, pnParameters, MutiPnExInfo);
}

WTTESTER_DLL_API int CALL_MODE WT_WaveGenerator3GPP(int connID,
                                                    const char *fileName,
                                                    void *pnParameters,
                                                    int Paramlen,
                                                    std::shared_ptr<AlzParam3GPP> &pAlz3gpp)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGenerator3GPP(fileName, pnParameters, Paramlen, pAlz3gpp);
}

WTTESTER_DLL_API int CALL_MODE WT_WaveGeneratorWiSun(int connID, const char* fileName, GenWaveWisunStruct *pnParameters)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->WaveGeneratorWiSun(fileName, pnParameters);
}

WTTESTER_DLL_API int CALL_MODE WT_SetWaveCalDataCompensate(int connID, int ON_FF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetWaveCalDataCompensate(ON_FF);
}

WTTESTER_DLL_API int CALL_MODE WT_SetVsgIQImbCompensate(int connID, int ON_FF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVsgIQImbCompensate(ON_FF);
}
WTTESTER_DLL_API int CALL_MODE WT_SetVsaCalDataCompensate(int connID, int ON_FF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVsaCalDataCompensate(ON_FF);
}
WTTESTER_DLL_API int CALL_MODE WT_SetVsaIQImbCompensate(int connID, int ON_FF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetVsaIQImbCompensate(ON_FF);
}

WTTESTER_DLL_API int WT_GetVsgFlatnessCalCompensate(int connID, int *ON_OFF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVsgFlatnessCalCompensate(ON_OFF);
}
WTTESTER_DLL_API int WT_GetVsgIQImbCompensate(int connID, int *ON_OFF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVsgIQImbCompensate(ON_OFF);
}
WTTESTER_DLL_API int WT_GetVsaFlatnessCalCompensate(int connID, int *ON_OFF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVsaFlatnessCalCompensate(ON_OFF);
}
WTTESTER_DLL_API int WT_GetVsaIQImbCompensate(int connID, int *ON_OFF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetVsaIQImbCompensate(ON_OFF);
}

WTTESTER_DLL_API int CALL_MODE WT_GetPNFileStructSize(int connID, const char *fileName, int *len)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return GetPNFileStructSize(fileName, len);
}

WTTESTER_DLL_API int CALL_MODE WT_GetPNFileStructData(int connID, const char *fileName, void *data, int len)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return GetPNFileStructData(fileName, data, len);
}

WTTESTER_DLL_API int CALL_MODE WT_GetPNFileExternSettingSize(int connID, const char *fileName, int *len)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return GetPNFileExternSetingSize(fileName, len);
}

WTTESTER_DLL_API int CALL_MODE WT_GetPNFileExternSettingData(int connID, const char *fileName, void *data, int len)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return GetPNFileExternSetingData(fileName, data, len);
}

WTTESTER_DLL_API int CALL_MODE WT_SetPNFileExternSettingData(int connID, void *data, int len)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetPNFileExternSettingData(data, len);
}


WTTESTER_DLL_API int CALL_MODE WT_QueryTesterDiskUseInfo(int connID, TesterDiskUsage *testerInfo)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->QueryTesterDiskUseInfo(testerInfo);
}

WTTESTER_DLL_API int CALL_MODE WT_CleanTesterCustomerWave(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->CleanTesterCustomerWave();
}

WTTESTER_DLL_API int CALL_MODE WT_SetSubNetAutoNeg(int connID, int Enable)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetSubNetAutoNeg(Enable);
}

WTTESTER_DLL_API int CALL_MODE WT_GetSubNetAutoNeg(int connID, int *ON_FF)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetSubNetAutoNeg(ON_FF);
}

WTTESTER_DLL_API int CALL_MODE WT_SetDigtalIQParam(int connID, DigtalIQParam *param)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetDigtalIQParam(param);
}

WTTESTER_DLL_API int CALL_MODE WT_SetDigtalIQTestFixture(int connID, DigtalIQTestFixture *param)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetDigtalIQTestFixture(param);
}

WTTESTER_DLL_API int CALL_MODE WT_SetDigtalIQMode(int connID, int mode)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetDigtalIQMode(mode);
}

WTTESTER_DLL_API int CALL_MODE WT_AckConnectInfo(int connID, int *ConnArray, int ArrayLen)
{
    TesterWrapper *wrapper = nullptr;
    char detail[MAX_BUFF_SIZE] = { 0 };
    int err = WT_ERR_CODE_OK;

    typedef struct
    {
        char IP[16];
        int port;
    }UserConnInfo;

    char ConnData[MAX_BUFF_SIZE] = {0};
    memset(ConnData, 0, sizeof(ConnData));
    if ((sizeof(UserConnInfo) * ArrayLen) > MAX_BUFF_SIZE)
    {
        return WT_ERR_CODE_ALLOC_MEMORY_FAIL;
    }

    UserConnInfo *tmpUserConnInfo = (UserConnInfo *)ConnData;
    char *ptr = nullptr;

    for (int i = 0; i < ArrayLen; ++i, ++ConnArray)
    {
        wrapper = GetUsableWrapper(*ConnArray);
        if(!wrapper)
        {
            Logger::PrintDebug(__FUNCTION__,__LINE__, "WT_ERR_CODE_CONNECT_FAIL\n");
            err = WT_ERR_CODE_CONNECT_FAIL;
            break;
        }

        err = wrapper->GetConnectDetail(detail, sizeof(detail));
        if (err)
        {
            break;
        }
        ptr = strstr(detail, ":");
        if (ptr)
        {
            memcpy(tmpUserConnInfo[i].IP, detail, ptr - detail);
            ptr++;
            tmpUserConnInfo[i].port = atoi(ptr);
        }
    }

    if (WT_ERR_CODE_OK == err)
    {
        do
        {
            wrapper = GetUsableWrapper(connID);
            if (!wrapper)
            {
                Logger::PrintDebug(__FUNCTION__, __LINE__, "WT_ERR_CODE_CONNECT_FAIL\n");
                err = WT_ERR_CODE_CONNECT_FAIL;
                break;
            }
            wrapper->AckConnectInfo(ConnData, sizeof(UserConnInfo) * ArrayLen);
        } while (0);
    }

    return err;
}

WTTESTER_DLL_API int CALL_MODE WT_StartFastAttCal(int connID, ATTCalCfg_API *config, ATTCalResult_API *result)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StartFastAttCal(config, result);
}

WTTESTER_DLL_API int CALL_MODE WT_StopFastAttCal(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->StopFastAttCal();
}

WTTESTER_DLL_API int CALL_MODE WT_SetMaxSampleRate(int connID, double maxRate)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetMaxSampleRate(maxRate);
}

WTTESTER_DLL_API int CALL_MODE WT_SubCmdHandle(int connID, SubCmdType* SubCmd)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SubCmdHandle(SubCmd);
}

WTTESTER_DLL_API int CALL_MODE WT_UpdateVsgParam(int connID, VsgParameter *param, ExtendVsgParameter *extParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    wrapper->UpdateVsgParam(param, extParam);
    return WT_ERR_CODE_OK;
}

WTTESTER_DLL_API int CALL_MODE WT_CreateLowWave(int connID, int type, const char *filename, const char *savename)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    wrapper->CreateLowWave(type, filename, savename);
    return WT_ERR_CODE_OK;
}

WTTESTER_DLL_API int CALL_MODE WT_GetPnCount(int connID, const char *filename, int &PnCount, int *PnOrder)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetPnCount(filename, PnCount, PnOrder);
}

WTTESTER_DLL_API int CALL_MODE WT_GetConnectInfo(int connID, char* Ip, int* Port)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    if (!wrapper)
    {
        strcpy(Ip, "127.0.0.1");
        *Port = 0;
        return WT_ERR_CODE_OK;
    }
    else
    {
        return wrapper->GetConnectInfo(Ip, Port);
    }
}

WTTESTER_DLL_API int CALL_MODE WT_TestConnectStatus(int connID)
{
    TesterWrapper* wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->TestConnectStatus();
}

WTTESTER_DLL_API int CALL_MODE WT_GetDockerAppList(int connID, char *buffer, int buflen, int *AppCnt)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetDockerAppList(buffer, buflen, AppCnt);
}

WTTESTER_DLL_API int CALL_MODE WT_SetDockerAppEnable(int connID, char *name, int onoff)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetDockerAppEnable(name, onoff);
}

WTTESTER_DLL_API int CALL_MODE WT_DelDockerApp(int connID, char *name)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->DelDockerApp(name);
}

WTTESTER_DLL_API int CALL_MODE WT_SetLoMode(int connID, int mode, int ModId)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetLOMode(mode ,ModId);
}

WTTESTER_DLL_API int CALL_MODE WT_GetLoMode(int connID, int *mode, int ModId)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetLOMode(mode ,ModId);
}

WTTESTER_DLL_API int CALL_MODE WT_SetIQMode(int connID, int *mode, int modcount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->SetIQMode(mode, modcount);
}
WTTESTER_DLL_API int CALL_MODE WT_GetIQMode(int connID, int *mode, int *modcount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    return wrapper->GetIQMode(mode, modcount);
}

WTTESTER_DLL_API int CALL_MODE WT_GetSpectrumPointPower(int connID,
    double Offset, 
    double* Power, 
    int signalID, 
    int segmentID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->GetSpectrumPointPower(Offset, Power, signalID, segmentID);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetWaveGeneratorTimeout(int connID,int Time)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetWaveGeneratorTimeout(Time);
    return iret;
}

WTTESTER_DLL_API int WT_GetWaveGenCBFReportField(int connID, CBFReport *ReportField)
{
    A_ASSERT(ReportField);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    return wrapper->GetWaveGenCBFReportField(ReportField);
}

WTTESTER_DLL_API int CALL_MODE WT_SetVsaNoiseCalibrationStart(int connID, int PortList[8])
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetVsaNoiseCalibrationStart(PortList);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetVsaNoiseCalibrationStop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetVsaNoiseCalibrationStop();
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetVsaNoiseCalibrationStatus(int connID, int &Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->GetVsaNoiseCalibrationStatus(Status);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetVsaNoiseCalibrationPortValid(int connID, int Status[8][8], int &TesterCount)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->GetVsaNoiseCalibrationPortValid(Status, TesterCount);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetVsaExtendEVMStatus(int connID, int Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetVsaExtendEVMStatus(Status);
    return iret;
}


WTTESTER_DLL_API int CALL_MODE WT_SetBroadcastEnable(int connID, int Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetBroadcastEnable(Status);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetBroadcastEnable(int connID, int &Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->GetBroadcastEnable(Status);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetBroadcastDebugEnable(int connID, int Status, double Power[WT_PORT_RF8 - WT_PORT_OFF])
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);
    int iret = wrapper->SetBroadcastDebugEnable(Status, Power);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetBroadcastRunStatus(int connID, int &Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->GetBroadcastRunStatus(Status);
    return iret;
}
WTTESTER_DLL_API int CALL_MODE WT_SetVsaIterativeEVMStatus(int connID, int Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetVsaIterativeEVMStatus(Status);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetVsaSncEVMStatus(int connID, int Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetVsaSncEVMStatus(Status);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetVsaCcEVMStatus(int connID, int Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int iret = wrapper->SetVsaCcEVMStatus(Status);
    return iret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetTxListModeEnable(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListModeEnable(true);
    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetTxListModeDisable(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListModeDisable(true);
    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetRxListModeEnable(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListModeEnable(false);
    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetRxListModeDisable(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListModeDisable(false);
    return Ret;
}


WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaClear(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsaClear();
    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAlzCommParam(int connID, int Segno, AlzParamComm *commonAnalyzeParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsaAlzCommParam(Segno, commonAnalyzeParam);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaCapParam(int connID, int Segno, VsaParameter *vsaParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsaCapParam(Segno, vsaParam);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaTrigParam(int connID, int Segno, VsaTrigParam *vsatrigParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsaTrigParam(Segno, vsatrigParam);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaTrigCommonParam(int connID, int Segno, void *SegTrigCommParam, int Size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegTrigCommonParam(true, Segno, SegTrigCommParam, Size);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgTrigCommonParam(int connID, int Segno, void *SegTrigCommParam, int Size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegTrigCommonParam(false, Segno, SegTrigCommParam, Size);

    return Ret;
}


WTTESTER_DLL_API int CALL_MODE WT_SetSegVsaAlzProtoParam(int connID, int Segno, int AlzType, void *ProAnalyzeParam, int Size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsaAlzProtoParam(Segno, AlzType, ProAnalyzeParam, Size);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSeqTxSegTimeParam(int connID, int Segno, void *SegTimeParam, int Size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegSeqTimeParam(true, Segno, SegTimeParam, Size);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetListTxSeqStart(int connID, double TrigerOffset)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListTxSeqStart(TrigerOffset);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetListRxSeqStart(int connID, int Repet, int EnableFlag, int IncrementFlag)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListRxSeqStart(Repet, EnableFlag, IncrementFlag);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSeqRxSegTimeParam(int connID, int Segno, void *SegTimeParam, int Size)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegSeqTimeParam(false, Segno, SegTimeParam, Size);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgParam(int connID, int Segno, VsgParameter *vsgParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsgParam(Segno, vsgParam);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgSyncParam(int connID, int Segno, int Status)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsgSyncParam(Segno, Status);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetSegVsgWaveParam(int connID, int Segno, VsgPattern *vsgWaveParam)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetSegVsgWaveParam(Segno, vsgWaveParam);

    return 0;
}

WTTESTER_DLL_API int CALL_MODE WT_SetListTxRxSeqStart(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListTxRxSeqStart();

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetListTxSeqStop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListTxSeqStop();

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetListRxSeqStop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListRxSeqStop();

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetListTxRxSeqStop(int connID)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->SetListTxRxSeqStop();

    return Ret;
}


WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllState(int connID, int *State)
{
    A_ASSERT(State);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListSeqAllState(true, State);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetListRxSeqAllState(int connID, int *State)
{
    A_ASSERT(State);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListSeqAllState(false, State);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllCapState(int connID, int *SegNo)
{
    A_ASSERT(SegNo);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListTxSeqAllCapState(SegNo);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetListRxSeqAllTransState(int connID, int *SegNo)
{
    A_ASSERT(SegNo);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListRxSeqAllTransState(SegNo);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllAnalyState(int connID, int *SegNo)
{
    A_ASSERT(SegNo);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListTxSeqAllAnalyState(SegNo);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetListTxSeqAllPowerResult(int connID, double *PowerResult, int SegNum)
{
    A_ASSERT(PowerResult);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListTxSeqAllPowerResult(PowerResult, SegNum);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetListLteTxSeqAllSegState(int connID, int *LteTxSegStat, int SegNum)
{
    A_ASSERT(LteTxSegStat);
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->GetListLteTxSeqAllSegState(LteTxSegStat, SegNum);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_SetDuplexEnable(int connID, int enable)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->DuplexSetEnable(enable);

    return Ret;
}

WTTESTER_DLL_API int CALL_MODE WT_GetDuplexEnable(int connID, int *enable)
{
    TesterWrapper *wrapper = GetUsableWrapper(connID);
    VALID_WRAPPER_ID(wrapper);

    int Ret = wrapper->DuplexGetEnable(enable);

    return Ret;
}