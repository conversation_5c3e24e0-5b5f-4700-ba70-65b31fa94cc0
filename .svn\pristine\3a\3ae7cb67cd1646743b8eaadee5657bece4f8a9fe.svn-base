#include <sys/sysinfo.h>

#include <sys/stat.h>
#include <unistd.h>

#include "subtaskmgr.h"
#include "wtlog.h"
#include "link.h"
#include "devmgr.h"
#include "devlib/devlib.h"
#include "templib.h"
#include "digitallib.h"

#define IN_CAL_DEFUALT_SPACE_TIME_MINS (60) // 自校准缺省间隔时间分钟
#define SECONDS_60S (60)                    // 一分钟的秒数

#define TEMP_IN_CAL_THRESHOLD (0.5) // 温度激活校准的门限值
// ??
#define IN_CAL_TEMP_SEC (6)

// 计算时间微妙差
#define TV_SUB(end, start) (((long long int)((end).tv_sec) - (long long int)((start).tv_sec)) * 1000000 + ((end).tv_usec - (start).tv_usec))

// 调试日志输出到文件中
#define SUB_MGR_DEBUG_LOG_TO_FILE (1)
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
static FILE *pMgrFpOut = NULL;
#endif

// 进程启动后, 计时器开启的第一次就会自校1次
// 每次校准完后, 按照间隔表的刻度，决定下一次校准的开始时间
// 如果超过了间隔表的范围, 则每间隔IN_CAL_DEFUALT_SPACE_TIME_MINS校准一次

// 仪器重启时间间隔表
static long g_InCalSpaceTimeArr1[] = {0, 3, 3, 5, 5, 5, 5, 10, 10, 10};
// 仅仅服务重启时间间隔表
static long g_InCalSpaceTimeArr2[] = {0, 3, 5, 5, 10, 10};
// 指向上面的间隔表
static long *g_TimesSpaceArray = NULL;
// 间隔表表项个数
static int g_ArrayCount = 0;
// 动态校准时刻表
static long g_InCalDynamicTimeArr[] = {0, 0, 0, 0, 0};
static int g_DynamicArrIdx = -1;

SubTaskmgr &SubTaskmgr::Instance(void)
{
    static SubTaskmgr Mgr;
    return Mgr;
}

SubTaskmgr::SubTaskmgr()
{
    int Value = 0;
    // 获取WT-Server启动时间
    time(&m_ServerBootTime);

    // 获取操作系统开机时间
    struct sysinfo info;
    sysinfo(&info);

    // 如果仪器开机时间比较长，则认为仪器比较稳定
    if (info.uptime > 25 * SECONDS_60S)
    {
        g_TimesSpaceArray = g_InCalSpaceTimeArr2;
        g_ArrayCount = sizeof(g_InCalSpaceTimeArr2) / sizeof(g_InCalSpaceTimeArr2[0]);
    }
    else
    {
        g_TimesSpaceArray = g_InCalSpaceTimeArr1;
        g_ArrayCount = sizeof(g_InCalSpaceTimeArr1) / sizeof(g_InCalSpaceTimeArr1[0]);
    }

    // 初始为校准模式
    m_CurMgrState = TASK_AUTO_CAL;
    m_InCalErrTimes = 0;
    m_InCalBeenStop = 0;
    m_InCalErrCode = 0;

    //先预先判断临时文件是否存在,存在即把自烤机自校准关闭,否则按原流程进行
    //----------------------------------------------------------------
    if (access("/tmp/notIntercal.txt", F_OK) == 0)
    {
        m_EnableInCal = false;
        m_EnableAutoBaking = false;
    }
    else
    {
        // 读取相关配置
        // 自动烤机
        m_EnableAutoBaking = true;
        Value = 0;
        DevConf::Instance().GetItemVal("AutoBaking", Value);
        if (Value == 0)
        {
            m_EnableAutoBaking = false;
        }

        // 自校准
        m_EnableInCal = true;
        Value = 0;
        DevConf::Instance().GetItemVal("InternalCal", Value, 0);
        if (Value == 0)
        {
            m_EnableInCal = false;
        }
    }

    Value = VERSION_B;
    DevLib::Instance().GetHardwareVersion(0, DEV_TYPE_BACK, Value);
    int TesterHwType = HW_WT428;
    DevLib::Instance().GetTesterHwType(TesterHwType);
    if (TesterHwType != HW_WT418 && Value == VERSION_A)
    {
        m_EnableInCal = false;
    }

    // 读取温度自校准配置
    Value = 0;
    DevConf::Instance().GetItemVal("InCalTemp", Value, 0);
    if (Value >= 40)
    {
        m_TempActiveThreshold = (double)Value / 100;
    }
    else
    {
        m_TempActiveThreshold = TEMP_IN_CAL_THRESHOLD;
    }

    Value = 0;
    string InCalSpace = (TesterHwType == HW_WT428) ? "InCalSpace_428" : "InCalSpace_448";
    DevConf::Instance().GetItemVal(InCalSpace, Value, 0);               // 再根据仪器类型获取自校准时间
    if (Value != 0)
    {
        m_InCalSpace = Value;
    }

    Value = 0;
    DevConf::Instance().GetItemVal("InCalMulThread", Value, 0);
    if (Value != 0)
    {
        m_InCalUseMulThread = Value;
    }

    Value = 0;
    if (0 == access((WTConf::GetDir() + "/BusinessBoardFlag").c_str(), F_OK))
    {
        DevConf::Instance().GetItemVal("InCalOnePortEx", Value, 0);
        WTLog::Instance().WriteLog(LOG_DEBUG, "BusinessBoardFlag exist, use InCalOnePortEx = %d\n", Value);
    }
    else
    {
        DevConf::Instance().GetItemVal("InCalOnePort", Value, 0);
        WTLog::Instance().WriteLog(LOG_DEBUG, "BusinessBoardFlag not exist, use InCalOnePort = %d\n", Value);
    }
    if (Value != 0)
    {
        m_InCalOnePort = Value;
    }

    ResetTempData();

    // 历史温度数据缓存区
    m_HistTemperBuf.reset(new char[MAX_SAVE_TEMP * sizeof(DevTempSave)]);

    WTLog::Instance().WriteLog(LOG_DEBUG, "[SubTaskmgr] In Cal config, State[%d] MulThread=[%d] InCalSpace=[%d] AutoBaking=[%d] TempThreshold[%.2f]\n",
           m_EnableInCal, m_InCalUseMulThread, m_InCalSpace, m_EnableAutoBaking, m_TempActiveThreshold);
    // WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr] Get In Cal config from file, State[%d] TempThreshold[%.2f]\n", m_EnableInCal, m_TempActiveThreshold);

    // 记录一个日志文件
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
    string path = WTConf::GetDir() + "/submgr.log";
    struct stat file_stat;
    if (stat(path.c_str(), &file_stat) == 0)
    {
        // WTLog::Instance().WriteLog(LOG_DEBUG, "file_stat.st_size = %zd\n", file_stat.st_size);
        if (file_stat.st_size > 10 * 1024 * 1024)
        {
            pMgrFpOut = fopen(path.c_str(), "w+");
        }
    }

    if (pMgrFpOut == NULL)
    {
        pMgrFpOut = fopen(path.c_str(), "a+");
    }
#endif

    // 单元关联端口数量
    int UnitMapRfport = WT_RF_1;
    for (int unit = 0; UnitMapRfport < WT_RF_MAX;++UnitMapRfport)
    {
        DevLib::Instance().GetModId(DEV_TYPE_VSG, UnitMapRfport, unit);
        if(unit != 0)
        {
            break;
        }
    }
    m_UintMapRfCount = UnitMapRfport-1;
}

SubTaskmgr::~SubTaskmgr()
{
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
    if (pMgrFpOut != NULL)
    {
        fclose(pMgrFpOut);
    }
#endif
}

// 时钟抵达, 也是自动校准的入口函数
void SubTaskmgr::NoticeTimeTick()
{
    if (DigModeLib::Instance().IsDigMode())
    {
        return;
    }

    unique_lock<mutex> Lock(m_NoticeMutex, std::try_to_lock);
    if (!Lock)
    {
        return;
    }

    time_t Now;
    time(&Now);

    // Server进程已经启动时间秒
    double ServerLiveSeconds = difftime(Now, m_ServerBootTime);

    // 自校被关闭, 不进行校准
    if (!m_EnableInCal)
    {
        AutoBakingSeriveEntry(ServerLiveSeconds);
        // WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr] InCal function not enable, m_EnableInCal=%d\n", m_EnableInCal);
        return;
    }

    // 计算温度状态
    DetectSomeoneDevTempState();

#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
    tm *local = localtime(&Now);
    if (pMgrFpOut)
    {
        fprintf(pMgrFpOut, "[%04d-%02d-%02d %02d:%02d:%02d][SubTaskmgr] temp_max[%.2f] temp_min[%.2f] NeedInCal[%d] ServerLives=%.2f NextStartTimes=%ld\n",
                local->tm_year + 1900, local->tm_mon + 1, local->tm_mday, local->tm_hour, local->tm_min, local->tm_sec,
                m_TempDataMax, m_TempDataMin, m_TempNeedInCal, ServerLiveSeconds, m_NextStartTimes);
        fflush(pMgrFpOut);
    }
#endif

    //WTLog::Instance().WriteLog(LOG_DEBUG, "m_pLinkMgr->HasExcludeLink()=%d\n", m_pLinkMgr->HasExcludeLink());
    // if (m_pLinkMgr->HasExcludeLink())
    // {
    //     //WTLog::Instance().WriteLog(LOG_DEBUG, "m_pLinkMgr->HasExcludeLink() return\n");
    //     return;
    // }

    if (m_CurMgrState == TASK_AUTO_NONE)
    {
        m_CurMgrState = TASK_AUTO_CAL;
    }

    if (m_CurMgrState == TASK_AUTO_CAL)
    {
        if ((m_TempNeedInCal == true) || (ServerLiveSeconds >= m_NextStartTimes))
        {
            RunInCal();
        }
    }
    else if (m_CurMgrState == TASK_AUTO_PAC)
    {
        // TODO: APLC
    }

    AutoBakingSeriveEntry(ServerLiveSeconds);
}

int SubTaskmgr::RunInCal()
{

    unique_lock<mutex> Lock(m_Mutex, std::try_to_lock);
    if (!Lock)
    {
        return WT_OK;
    }

#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)

    time_t Now;
    time(&Now);
    tm *local = localtime(&Now);
#endif

    if (m_AuotCalSrv.empty())
    {
        // 关闭自动烤机
        if (IsAutoBakingSeriveRunning())
        {
            fprintf(pMgrFpOut, "IsAutoBakingSeriveRunning\n");
            StopAutoBakingService();
        }

        m_InCalBeenStop = 0;

        WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr] Start new Incal subtask, in cal doing now\n");

        // 记录自校准启动时间
        WTLog::Instance().LOGSYSOPT("[SubTaskmgr] Start new Incal subtask, in cal doing now");

#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
        double ServerLiveSeconds = difftime(Now, m_ServerBootTime);
        if (pMgrFpOut)
        {
            fprintf(pMgrFpOut, "[%04d-%02d-%02d %02d:%02d:%02d] [SubTaskmgr] start incal subtask m_TempNeedInCal=%d, g_DynamicArrIdx=%d, ServerLiveSeconds=%.2f, m_NextStartTimes=%ld\n",
                    local->tm_year + 1900, local->tm_mon + 1, local->tm_mday, local->tm_hour, local->tm_min, local->tm_sec,
                    m_TempNeedInCal, g_DynamicArrIdx, ServerLiveSeconds, m_NextStartTimes);
            fflush(pMgrFpOut);
        }
#endif

        int UnitCount = WTDeviceInfo::Instance().GetDeviceDetailedInfo().BusiBoardCount;

#define IN_CAL_ONE_PORT_DATA_TO_OTHER_PORT (1)

        if (m_InCalUseMulThread == 0)
        {
            shared_ptr<SubService> SubSrv = make_shared<SubService>(TASK_AUTO_CAL);
            vector<int> port_vec;

            for (int unit = 0; unit < UnitCount; unit++)
            {
                if (m_InCalCount == 0 || m_InCalOnePort == 0)
                {
                    for (int j = 0; j < m_UintMapRfCount; ++j)
                    {
                        port_vec.push_back(unit * m_UintMapRfCount + 1 + j);
                    }
                }
                else
                {
                    port_vec.push_back(unit * m_UintMapRfCount + 1 + (m_InCalCount % m_UintMapRfCount));
                    SubSrv->SetRfDataToOtherPort(true);
                }
            }

            SubSrv->SetAutoCalRFList(port_vec);
            m_AuotCalSrv.push_back(SubSrv);
            m_pLinkMgr->GetThreadPool()->AddTask(nullptr, bind(&SubTaskmgr::AutoCalService, this, SubSrv, placeholders::_1));
        }
        else
        {
            for (int unit = 0; unit < UnitCount; unit++)
            {
                shared_ptr<SubService> SubSrv = make_shared<SubService>(TASK_AUTO_CAL);
                vector<int> port_vec;

                if (m_InCalCount == 0 || m_InCalOnePort == 0)
                {
                    for (int j = 0; j < m_UintMapRfCount; ++j)
                    {
                        port_vec.push_back(unit * m_UintMapRfCount + 1 + j);
                    }
                }
                else
                {
                    port_vec.push_back(unit * m_UintMapRfCount + 1 + (m_InCalCount % m_UintMapRfCount));
                    SubSrv->SetRfDataToOtherPort(true);
                }

                SubSrv->SetAutoCalRFList(port_vec);
                m_AuotCalSrv.push_back(SubSrv);
                m_pLinkMgr->GetThreadPool()->AddTask(nullptr, bind(&SubTaskmgr::AutoCalService, this, SubSrv, placeholders::_1));
            }
        }

        m_InCalCount += 1;
        time((time_t *)&m_InCalStartTimes); // 记录自校准开始运行的时间

        m_TempNeedInCal = false;
        m_InCalSrvErrCount = 0; //  任务开始 清0
        m_InCalErrCode = 0;
    }
    else // 已经有服务在自校准当中
    {
        // #if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
        //         if (pMgrFpOut)
        //         {
        //             fprintf(pMgrFpOut, "[%04d-%02d-%02d %02d:%02d:%02d] [SubTaskmgr] Exist Running SubService\n",
        //                     local->tm_year + 1900, local->tm_mon + 1, local->tm_mday, local->tm_hour, local->tm_min, local->tm_sec);
        //             fflush(pMgrFpOut);
        //         }
        // #endif

        // 考虑 自校准服务是否很校准了很长时间都没有结束
        // double AutoCalRunSeconds = difftime(Now, m_InCalStartTimes);
        // // 长时间未校准完，就停止掉
        // if (AutoCalRunSeconds > 600)
        // {
        //     // 异步停止所有任务
        //     StopAllTask();
        // }
    }

    return WT_OK;
}

// 线程函数体，调用service的线程实体函数
void SubTaskmgr::AutoCalService(shared_ptr<SubService> Srv, void *Arg)
{
    (void)Arg;
    char Buff[256] = {0};
    struct timeval start, end;
    gettimeofday(&start, NULL);

    Srv->Run();

    gettimeofday(&end, NULL);
    long long int diffusecs = TV_SUB(end, start);

    // 这里还需要考虑自校准的结果状态值
    int Err = 0;
    int RunStep = 0;
    Srv->GetRunResult(RunStep, Err);
    if (Err != WT_OK)
    {
        m_InCalSrvErrCount += 1; // 记录错误次数
        m_InCalErrCode = Err;
    }

    sprintf(Buff, "[SubTaskmgr] SubService Stop, took time: %5lldms, RunStep[%d], ErrorCode[%d]\n", (diffusecs / 1000), RunStep, Err);
    WTLog::Instance().WriteLog(LOG_SUB_MGR, Buff);

#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
    time_t Now;
    time(&Now);
    tm *local = localtime(&Now);

    if (pMgrFpOut)
    {
        fprintf(pMgrFpOut, "[%04d-%02d-%02d %02d:%02d:%02d] [SubTaskmgr] SubService Stop, took time: %5lldms, RunStep[%d], ErrorCode[%d]\n",
                local->tm_year + 1900, local->tm_mon + 1, local->tm_mday, local->tm_hour, local->tm_min, local->tm_sec,
                (diffusecs / 1000), RunStep, Err);
        fflush(pMgrFpOut);
    }
#endif

    WTLog::Instance().LOGSYSOPT(Buff);

    DelAutoCalSrv(Srv);
}

void SubTaskmgr::DelAutoCalSrv(shared_ptr<SubService> Srv)
{
    lock_guard<mutex> Lock(m_Mutex);
    m_AuotCalSrv.remove(Srv);

    if (m_AuotCalSrv.empty())
    {
        time_t Now;
        time(&Now);

        double ServerLiveSeconds = difftime(Now, m_ServerBootTime);

        // Manager 主动停止业务 或者 存在排他连接
        if (m_InCalBeenStop != 0)
        {
            CalcNextInCalTime();
        }

        // 最近一次校准任务，至少存在一个错误
        if (m_InCalSrvErrCount > 0)
        {
            if (m_InCalErrTimes >= 3) // 连续3次出错
            {
                // TODO 告警处理
                WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr] Three errors, incal will disable\n");

                // 要不要亮红灯?

                // 关闭自校准
                m_EnableInCal = 0;
            }
            else
            {
                m_NextStartTimes = ServerLiveSeconds + 60; // 如果出错了，等60秒后再启动校准
                m_InCalErrTimes += 1;                      // 记录错误次数

                WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr] Last in cal error, it will start in 60 seconds m_NextStartTimes=%ld\n", m_NextStartTimes);
            }
        }
        else
        {
            // 这里的策略是校准时间太长，就尽快再开始一次，这功能需要数据支持，暂时屏蔽
            // int UseSeconds = diffusecs / 1000000; // 耗时秒
            // if (UseSeconds > 200)                 // 花了200秒，时间太长
            // {
            //     m_NextStartTimes = ServerLiveSeconds + 120; // 耗时太长，等120秒后再启动校准
            //     WTLog::Instance().WriteLog(LOG_DEBUG, "[SubTaskmgr] last in cal use [%d]s, it is too long time m_NextStartTimes=%ld\n", UseSeconds, m_NextStartTimes);
            // }
            // else
            // {
            //     CalcNextInCalTime();
            // }

            CalcNextInCalTime();
        }

        ResetTempData();
    }
}

// TODO 计算下一次的自校时间，有几种考虑:
// 1) 用户设置关闭，再开启自校开关时，应该是立即开始自校。
// 2) 自校准出出错后，再重启自校时，可以设置为，当前时间 + 60秒，后开始自校。
// 3) 自校本身耗时非常长，可以考虑缩短等待时间，比如 当前时间 + 60秒，后开始自校。
// 4) 其他情况就按照 IN_AUTO_TIMES 的时间差 取值
void SubTaskmgr::CalcNextInCalTime()
{
    // 方案一
    // 计算下一次的索引
    // m_NextStartIdx += 1;
    // if (m_NextStartIdx >= g_ArrayCount)
    // {
    //     m_NextStartTimes += IN_CAL_DEFUALT_SPACE_TIME_MINS * SECONDS_60S;
    // }
    // else
    // {
    //     m_NextStartTimes += g_TimesSpaceArray[m_NextStartIdx] * SECONDS_60S;
    // }

    // 方案二
    time_t Now;
    time(&Now);
    double ServerLiveSeconds = difftime(Now, m_ServerBootTime);

    // 追加高低温变化的优先方案:
    if (g_DynamicArrIdx > -1) // 最近温度变化较大
    {
        for (int i = g_DynamicArrIdx; i < sizeof(g_InCalDynamicTimeArr) / sizeof(g_InCalDynamicTimeArr[0]); ++i)
        {
            if (ServerLiveSeconds < g_InCalDynamicTimeArr[i])
            {
                m_NextStartTimes = g_InCalDynamicTimeArr[i];
                g_DynamicArrIdx = i;
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
                if (pMgrFpOut)
                {
                    fprintf(pMgrFpOut, "[SubTaskmgr] CalcNextInCalTime m_NextStartTimes=g_InCalDynamicTimeArr[%d]=[%ld]\n", i, m_NextStartTimes);
                    fflush(pMgrFpOut);
                }
#endif
                return;
            }
        }

        // 已经遍历完了, 就清空掉动态数据表，然后使用后面的时间方案
        ClearDynamicInCalTimeTable();
    }

    long Sum = 0;
    for (int i = 0; i < g_ArrayCount - 1; ++i)
    {
        Sum += g_TimesSpaceArray[i];
        WTLog::Instance().WriteLog(LOG_DEBUG, "i=%d, Sum=%ld ServerLiveSeconds=%.2f\n", i, Sum * SECONDS_60S, ServerLiveSeconds);
        fprintf(pMgrFpOut, "i=%d, Sum=%ld ServerLiveSeconds=%.2f\n", i, Sum * SECONDS_60S, ServerLiveSeconds);
        if ((ServerLiveSeconds) < Sum * SECONDS_60S)
        {
            m_NextStartTimes = ServerLiveSeconds + g_TimesSpaceArray[i + 1] * SECONDS_60S;
            //WTLog::Instance().WriteLog(LOG_DEBUG, "[SubTaskmgr] CalcNextInCalTime next start in cal time[%ld] 22222222\n", m_NextStartTimes);
            return;
        }
    }

    // m_NextStartTimes = ServerLiveSeconds + IN_CAL_DEFUALT_SPACE_TIME_MINS * SECONDS_60S;
    m_NextStartTimes = ServerLiveSeconds + m_InCalSpace * SECONDS_60S;
    //WTLog::Instance().WriteLog(LOG_DEBUG, "[SubTaskmgr] CalcNextInCalTime next start in cal time[%ld]\n", m_NextStartTimes);
}

void SubTaskmgr::StopAllTask()
{
    lock_guard<mutex> Lock(m_Mutex);
    WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr] Stop all in cal subtask\n");
    WTLog::Instance().LOGSYSOPT("[SubTaskmgr] Stop all in cal subtask");

    if (!m_AuotCalSrv.empty())
    {
        for (auto it = m_AuotCalSrv.begin(); it != m_AuotCalSrv.end(); ++it)
        {
            (*it)->AsyncStop();
        }

        time(&m_LastStopInCalTime);
        m_InCalBeenStop = 1;
    }
}

void SubTaskmgr::SetInCalConfig(int state)
{
    m_EnableInCal = (bool)state;

    // 如果是禁用内校准, 就停止当前已经开启内校准任务.
    if (state == 0)
    {
        StopAllTask();
    }
}

void SubTaskmgr::NoticeHasExcludeLink(bool Has)
{
    //强制连接也进行自校准 20211213
#if 0
    // 记录通知时间, 应该考虑加锁, 毕竟是多进程访问
    if (Has)
    {
        // 为啥要记录这个时间呢？我自己也不记得了
        time(&m_LastNoticeExLinkTime);

        StopAllTask();

        StopAutoBakingService();
    }
    else // 独占连接退出
    {
        // 最近一次自校准被终止了
        if (m_InCalBeenStop == 1)
        {
            time_t Now;
            time(&Now);
            m_NextStartTimes = difftime(Now, m_ServerBootTime);
        }
    }
#else
    (void)Has;
#endif

}

int SubTaskmgr::StartInCalAtOnce()
{
    int Value = VERSION_B;
    int TesterHwType = HW_WT428;
    DevLib::Instance().GetHardwareVersion(0, DEV_TYPE_BACK, Value);
    DevLib::Instance().GetTesterHwType(TesterHwType);

    // if (Value >= VERSION_B)
    // {
    //     WTLog::Instance().LOGSYSOPT("[SubTaskmgr] please start in cal at once");
    //     m_InCalErrTimes = 0;
    //     RunInCal();
    // }

    // 如果是448且版本大于Vb就执行RunIncal，其他的仪器可直接执行，但是需要防止仪器类型获取不到的情况
    if((TesterHwType == HW_WT418) || (TesterHwType == HW_WT428) || (TesterHwType == HW_WT448 && Value >= VERSION_B))
    {
        WTLog::Instance().LOGSYSOPT("[SubTaskmgr] please start in cal at once");
        m_InCalErrTimes = 0;
        RunInCal();
    }
    
    return WT_OK;
}

int SubTaskmgr::StopInCalAtOnce()
{
    WTLog::Instance().LOGSYSOPT("[SubTaskmgr] please stop in cal at once");
    StopAllTask();
    return WT_OK;
}

int SubTaskmgr::QueryInCalProcess(int &Process)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "SubTaskmgr::QueryInCalProcess in\n");
    lock_guard<mutex> Lock(m_Mutex);
    WTLog::Instance().WriteLog(LOG_DEBUG, "SubTaskmgr::QueryInCalProcess get Lock\n");

    // 没法区分是否校准过
    if (m_AuotCalSrv.empty())
    {
        if (m_InCalErrTimes || m_InCalSrvErrCount)
        {
            Process = 100;
            if (m_InCalErrCode != 0)
            {
                WTLog::Instance().WriteLog(LOG_DEBUG, "SubTaskmgr::QueryInCalProcess err = %d\n", m_InCalErrCode);
                return m_InCalErrCode;
            }
        }
        else
        {
            Process = 100;
        }
    }
    else
    {
        auto it = m_AuotCalSrv.begin();
        Process = (*it)->GetInCalProcess();
        // 还在初始中, 数据可能异常
        if (Process > 100 || Process < 0)
        {
            Process = 0;
        }
    }

    return WT_OK;
}

void SubTaskmgr::ResetTempData()
{
    m_TempDataSum = 0;
    m_TempDataCount = 0;
    m_TempDataMax = -1000;
    m_TempDataMin = 1000;
    m_TempNeedInCal = false;
}

void SubTaskmgr::DetectSomeoneDevTempState()
{
    m_TempDetectTick++;
    time_t Now;
    time(&Now);
    tm *T = localtime(&Now);

#define TEMP_DETECT_FREQ_MIN (6)                                          // 1分钟检查6次
#define TEMP_DETECT_SPACE (SECONDS_60S / TEMP_DETECT_FREQ_MIN)            // 1分钟检查次数
#define TEMP_DETECT_TICK (TEMP_DETECT_SPACE / SUB_TASKMGR_TIMER_INTERVAL) // 温度检测滴答次数
    // 为毛是5？ 计时器是2秒一次，希望是10秒取一次温度，所以取值为5
    // 代码是可以优化的

    if (m_TempDetectTick >= TEMP_DETECT_TICK)
    {
        // 新添加整体时间检测机制
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
        int back = g_DynamicArrIdx;
#endif
        LongTimeTempDetect();
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
        if (back != g_DynamicArrIdx && pMgrFpOut)
        {
            fprintf(pMgrFpOut, "[SubTaskmgr][%02d-%02d %02d:%02d:%02d] LongTimeTempDetect backup=%d, g_DynamicArrIdx=%d m_NextStartTimes=%ld\n",
                    T->tm_mon + 1, T->tm_mday, T->tm_hour, T->tm_min, T->tm_sec, back, g_DynamicArrIdx, m_NextStartTimes);
            fflush(pMgrFpOut);
        }
#endif
        double Temp = 0;
        double Avg = 0;
        m_TempDetectTick = 0;

        // 取设备温度
        // Ret = TempLib::Instance().GetUintBoardTemperater(DEV_TYPE_BACK, 0, Temp);
        int Ret = DevLib::Instance().GetSwbPortTemperater(1, Temp);
        if (Ret == WT_OK)
        {
            m_TempDataSum += Temp;
            m_TempDataCount += 1;
            // WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr][%02d-%02d %02d:%02d:%02d] DetectSomeoneDevTempState Temp=%.2f\n",
            //                            T->tm_mon + 1, T->tm_mday, T->tm_hour, T->tm_min, T->tm_sec, Temp);

            if (m_TempDataCount == TEMP_DETECT_FREQ_MIN)
            {
                Avg = m_TempDataSum / m_TempDataCount;
                if (Avg > m_TempDataMax)
                {
                    m_TempDataMax = Avg;
                }
                if (Avg < m_TempDataMin)
                {
                    m_TempDataMin = Avg;
                }

                // 温度改变有点大，需要立即校准
                if (m_TempDataMax - m_TempDataMin > m_TempActiveThreshold)
                {
                    //WTLog::Instance().WriteLog(LOG_DEBUG, "SubTaskmgr::DetectSomeoneDevTempState need incal\n");
                    WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr][%02d-%02d %02d:%02d:%02d] DetectSomeoneDevTempState Need to do incal\n",
                                               T->tm_mon + 1, T->tm_mday, T->tm_hour, T->tm_min, T->tm_sec);

#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
                    if (pMgrFpOut)
                    {
                        fprintf(pMgrFpOut, "[SubTaskmgr][%02d-%02d %02d:%02d:%02d] DetectSomeoneDevTempState Need to do incal m_TempDataMax=%.2f m_TempDataMin=%.2f m_TempActiveThreshold=%.2f\n",
                                T->tm_mon + 1, T->tm_mday, T->tm_hour, T->tm_min, T->tm_sec, m_TempDataMax, m_TempDataMin, m_TempActiveThreshold);
                        fflush(pMgrFpOut);
                    }
#endif
                    // 记录当前温度为最新值
                    m_TempDataMax = Avg;
                    m_TempDataMin = Avg;
                    m_TempNeedInCal = true; // need to run in cal;
                }

                // 清除与温度平均相关数据
                m_TempDataSum = 0;
                m_TempDataCount = 0;
            }
            // WTLog::Instance().WriteLog(LOG_DEBUG, "[SubTaskmgr] temp_max[%.2f] temp_min[%.2f] m_TempNeedInCal[%d] ServerLiveSeconds=%.2f m_NextStartTimes=%ld\n",
            // m_TempDataMax, m_TempDataMin, m_TempNeedInCal, ServerLiveSeconds, m_NextStartTimes);
            //double ServerLiveSeconds = difftime(Now, m_ServerBootTime);
            // WTLog::Instance().WriteLog(LOG_SUB_MGR, "[SubTaskmgr][%02d-%02d %02d:%02d:%02d] temp_max[%.2f] temp_min[%.2f] NeedInCal[%d] NextStartTimes=%ld\n",
            //                            T->tm_mon + 1, T->tm_mday, T->tm_hour, T->tm_min, T->tm_sec, m_TempDataMax, m_TempDataMin, m_TempNeedInCal, m_NextStartTimes);
        }
    }
}

void SubTaskmgr::GenerateDynamicInCalTimeTable()
{
    long Now = 0;
    time((time_t *)&Now);
    double ServerLiveSeconds = difftime(Now, m_ServerBootTime);

    // 暂时先这么写吧
    g_InCalDynamicTimeArr[0] = ServerLiveSeconds + 0;
    g_InCalDynamicTimeArr[1] = ServerLiveSeconds + 4 * SECONDS_60S;
    g_InCalDynamicTimeArr[2] = ServerLiveSeconds + 9 * SECONDS_60S;
    g_InCalDynamicTimeArr[3] = ServerLiveSeconds + 15 * SECONDS_60S;
    g_InCalDynamicTimeArr[4] = ServerLiveSeconds + 22 * SECONDS_60S;
    g_DynamicArrIdx = 0;

    m_NextStartTimes = ServerLiveSeconds; // 立即开始自校准
}

void SubTaskmgr::ClearDynamicInCalTimeTable()
{
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
    if (pMgrFpOut)
    {
        fprintf(pMgrFpOut, "[SubTaskmgr]ClearDynamicInCalTimeTable\n");
        fflush(pMgrFpOut);
    }
#endif
    g_InCalDynamicTimeArr[0] = 0;
    g_InCalDynamicTimeArr[1] = 0;
    g_InCalDynamicTimeArr[2] = 0;
    g_InCalDynamicTimeArr[3] = 0;
    g_InCalDynamicTimeArr[4] = 0;
    g_DynamicArrIdx = -1;
}

// 温度变化比较大的时候激活自校准。
// 温度模块有缓存最近的温度数据，这里取若干数据
// 比较最大最小值超过5度，激活动态自校准
void SubTaskmgr::LongTimeTempDetect()
{
    long Now = 0;
    time((time_t *)&Now);
    double ServerLiveSeconds = difftime(Now, m_ServerBootTime);
    (void)ServerLiveSeconds;

    // 开机一段时间后才开始检测
    if (ServerLiveSeconds < 40 * SECONDS_60S)
    {
        return;
    }

    // 已经检测到温度突变, 且已经生效, 就不再检测
    if (g_DynamicArrIdx > -1)
    {
        // WTLog::Instance().WriteLog(LOG_DEBUG, "LongTimeTempDetect g_DynamicArrIdx=%d\n", g_DynamicArrIdx);
        return;
    }

    int ValidCnt = TempLib::Instance().GetSaveAllData(m_HistTemperBuf.get());
    // WTLog::Instance().WriteLog(LOG_DEBUG, "LongTimeTempDetect ValidCnt=%d\n", ValidCnt);
    if (ValidCnt < 10)
    {
        return;
    }

    DevTempSave *Array = (DevTempSave *)m_HistTemperBuf.get();
    double TempMax = -1000;
    double TempMin = 1000;
    int MaxIdx = 0;
    int MinIdx = 0;
    double CurTemp = 0;
    char Buff[1024] = {0};

    // 顺序地访问最后的若干个点, 求温度的最大最小值.
    for (int idx = ValidCnt - 10; idx < ValidCnt; ++idx)
    {
        // WTLog::Instance().WriteLog(LOG_DEBUG, "[%d-%d-%d %d:%d:%d] Valid=%d temp=%f\n", Array[idx].Time.year, Array[idx].Time.mon, Array[idx].Time.mday,
        //        Array[idx].Time.hour, Array[idx].Time.min, Array[idx].Time.sec, Array[idx].Valid, Array[idx].Temperature.SwitchBoardTemp[WT_RF_1]);

        if (Array[idx].Valid)
        {
            // 取一个单元板的温度做代表
            CurTemp = Array[idx].Temperature.SwitchBoardTemp[WT_RF_1];
            if (CurTemp > TempMax)
            {
                MaxIdx = idx;
                TempMax = CurTemp;
            }

            if (CurTemp < TempMin)
            {
                TempMin = CurTemp;
                MinIdx = idx;
            }

            // 温度变化比较大, 激活动态自校准
            if (abs(TempMax - TempMin) > 4)
            {
                // 记录启动事件
                //WTLog::Instance().LOGSYSOPT("[SubTaskmgr] please stop in cal at once");

                sprintf(Buff, "[SubTaskmgr] GenerateDynamicInCalTimeTable TempMax t[%d-%d-%d %d:%d:%d] temp[%f] TempMin t[%d-%d-%d %d:%d:%d] temp[%f]\n",
                        Array[MaxIdx].Time.year, Array[MaxIdx].Time.mon, Array[MaxIdx].Time.mday,
                        Array[MaxIdx].Time.hour, Array[MaxIdx].Time.min, Array[MaxIdx].Time.sec,
                        Array[MaxIdx].Temperature.SwitchBoardTemp[WT_RF_1],
                        Array[MinIdx].Time.year, Array[MinIdx].Time.mon, Array[MinIdx].Time.mday,
                        Array[MinIdx].Time.hour, Array[MinIdx].Time.min, Array[MinIdx].Time.sec,
                        Array[MinIdx].Temperature.SwitchBoardTemp[WT_RF_1]);

                WTLog::Instance().LOGSYSOPT(Buff);
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
                if (pMgrFpOut)
                {
                    fprintf(pMgrFpOut, "%s\n", Buff);
                    fflush(pMgrFpOut);
                }
#endif
                // 这里格式化
                GenerateDynamicInCalTimeTable();
                return;
            }
        }
    }
}

void SubTaskmgr::AutoBakingSeriveEntry(double ServerLiveSeconds)
{
    // WTLog::Instance().WriteLog(LOG_DEBUG, "SubTaskmgr::AutoBakingSeriveEntry ServerLiveSeconds=%f\n", ServerLiveSeconds);
    if (!m_EnableAutoBaking)
    {
        // WTLog::Instance().WriteLog(LOG_DEBUG, "m_EnableAutoBaking = 0\n");
        return;
    }

    // 不存在自校准服务
    if (m_AuotCalSrv.empty())
    {
        if (ServerLiveSeconds < AUTO_BAKING_KEEP_TIME)
        {
            long numerator = ServerLiveSeconds; // 被除数
            int remainder = numerator % 10;     // 余数
            //WTLog::Instance().WriteLog(LOG_DEBUG, "remainder=%d\n", remainder);
            if (remainder >= 8)
            {
                if (!IsAutoBakingSeriveRunning())
                {
                    if (m_pLinkMgr->HasLink())
                    {
                        return;
                    }

                    if (!AutoBakingSerive::Instance().QueryExistMgrLinkInfo())
                    {
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
                        if (pMgrFpOut)
                        {
                            fprintf(pMgrFpOut, "[SubTaskmgr]Start AutoBaking Service\n");
                            fflush(pMgrFpOut);
                        }
#endif
                        WTLog::Instance().WriteLog(LOG_AUTO_BAKING, "LinkMgr start running AutoBaking's service\n");

                        shared_ptr<AutoBakingSerive> SubSrv = make_shared<AutoBakingSerive>(0);
                        unique_lock<mutex> Lock(m_AutoBakingMutex);
                        m_AutoBakingSrvList.push_back(SubSrv);
                        m_pLinkMgr->GetThreadPool()->AddTask(nullptr, bind(&SubTaskmgr::StartAutoBakingSerive, this, SubSrv, placeholders::_1));

                        shared_ptr<AutoBakingSerive> SubSrv2 = make_shared<AutoBakingSerive>(1);
                        m_AutoBakingSrvList.push_back(SubSrv2);
                        Lock.unlock();
                        m_pLinkMgr->GetThreadPool()->AddTask(nullptr, bind(&SubTaskmgr::StartAutoBakingSerive, this, SubSrv2, placeholders::_1));

                        //m_pLinkMgr->GetThreadPool()->AddTask((void *)0, bind(&SubTaskmgr::StartAutoTest, this, placeholders::_1));
                        //m_pLinkMgr->GetThreadPool()->AddTask((void *)1, bind(&SubTaskmgr::StartAutoTest, this, placeholders::_1));
                    }
                }
            }
        }
    }
}

int SubTaskmgr::StopAutoBakingService()
{
    unique_lock<mutex> Lock(m_AutoBakingMutex);
    for (auto it = m_AutoBakingSrvList.begin(); it != m_AutoBakingSrvList.end(); ++it)
    {
        (*it)->Stop();
#if (SUB_MGR_DEBUG_LOG_TO_FILE == 1)
        if (pMgrFpOut)
        {
            fprintf(pMgrFpOut, "[SubTaskmgr]Stop AutoBaking Service\n");
            fflush(pMgrFpOut);
        }
#endif
    }

    return -1;
}

void SubTaskmgr::StartAutoBakingSerive(shared_ptr<AutoBakingSerive> Srv, void *Arg)
{
    (void)Arg;
    Srv->Run();

    unique_lock<mutex> Lock(m_AutoBakingMutex);
    m_AutoBakingSrvList.remove(Srv);
}

bool SubTaskmgr::IsAutoBakingSeriveRunning()
{
    unique_lock<mutex> Lock(m_AutoBakingMutex);
    return (!m_AutoBakingSrvList.empty());
}