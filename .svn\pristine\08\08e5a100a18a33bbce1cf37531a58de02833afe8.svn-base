#include "includes.h"
#include "common.h"
#include "wpa_debug.h"

static int wpa_debug_level = MSG_INFO;
static int wpa_debug_show_keys = 0;
static int wpa_debug_timestamp = 0;
static int wpa_debug_syslog = 0;

class FileLog
{
public:
    FileLog();
    ~FileLog();
    FILE *getfp() { return fp; }
private:
    FILE *fp;
};

FileLog::FileLog()
{
    const size_t max_file_size = 2097152;
    const s8 *fileName = "security_mac.log";
    fp = fopen(fileName, "ab");
    fseek(fp, 0L, SEEK_END);
    size_t sz = ftell(fp);
    if (sz >= max_file_size)
    {
        fclose(fp);
        fp = fopen(fileName, "wb");
    }
}

FileLog::~FileLog()
{
    if (fp)
    {
        fclose(fp);
    }
}

std::string getTimeString() 
{
    auto tNow = std::chrono::system_clock::now();
    auto tmNow = std::chrono::system_clock::to_time_t(tNow);
    auto tSeconds = std::chrono::duration_cast<std::chrono::seconds>(tNow.time_since_epoch());
    auto ptm = localtime(&tmNow);
    
    std::stringstream oss;
    oss << std::put_time(ptm, "%Y-%m-%d %H:%M:%S");

    auto tMilli = std::chrono::duration_cast<std::chrono::milliseconds>(tNow.time_since_epoch());
    auto ms = tMilli - tSeconds;
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return oss.str();
}

void wpa_debug_print_timestamp(void)
{
    if (!wpa_debug_timestamp)
        return;
    if (!wpa_debug_syslog)
        printf("%s: ", getTimeString().c_str());
}

/**
* wpa_printf - conditional printf
* @level: priority level (MSG_*) of the message
* @fmt: printf format string, followed by optional arguments
*
* This function is used to print conditional debugging and error messages. The
* output may be directed to stdout, stderr, and/or syslog based on
* configuration.
*
* Note: New line '\n' is added to the end of the text when printing to stdout.
*/
void wpa_printf(int level, const char *fmt, ...)
{
    va_list ap;
    if (level >= wpa_debug_level) 
    {
        wpa_debug_print_timestamp();
#ifdef DEBUG_EAPOL_KEY
        FileLog log;
        FILE *out_file = log.getfp();

        if (out_file)
        {
            va_start(ap, fmt);
            vfprintf(out_file, fmt, ap);
            fprintf(out_file, "\n");
            va_end(ap);
        }
#endif
        if (!wpa_debug_syslog) 
        {
            va_start(ap, fmt);
            vprintf(fmt, ap);
            printf("\n");
            va_end(ap);
        }
    }
}


static void _wpa_hexdump(int level, const char *title, const u8 *buf, size_t len, int show, int only_syslog)
{
    size_t i = 0;
    u32 one_line_hex = 16;

    if (level < wpa_debug_level)
    {
        return;
    }

    wpa_debug_print_timestamp();
#ifdef DEBUG_EAPOL_KEY

    FileLog log;
    FILE *out_file = log.getfp();
    if (out_file)
    {
        fprintf(out_file, "%s - hexdump(len=%lu):\n", title, (unsigned long)len);
        if (buf == nullptr)
        {
            fprintf(out_file, " [nullptr]");
        }
        else if (show)
        {
            for (i = 0; i < len; i++)
            {
                fprintf(out_file, "0x%02x, ", buf[i]);
                if (i + 1 == len || (0 == (i + 1) % one_line_hex))
                {
                    fprintf(out_file, "\n");
                }
            }
        }
        else
        {
            fprintf(out_file, " [REMOVED]");
        }
        fprintf(out_file, "\n");
    }
#endif
    if (!wpa_debug_syslog) 
    {
        printf("%s - hexdump(len=%lu):\n", title, (unsigned long)len);
        if (buf == nullptr) 
        {
            printf(" [nullptr]");
        }
        else if (show) 
        {
            for (i = 0; i < len; i++)
            {
                printf("0x%02x, ", buf[i]);
                if (i + 1 == len || (0 == (i + 1) % one_line_hex))
                {
                    printf("\n");
                }
            }
        }
        else 
        {
            printf(" [REMOVED]");
        }
        printf("\n");
    }
}

void wpa_hexdump(int level, const char *title, const void *buf, size_t len)
{
    _wpa_hexdump(level, title, (const u8*)buf, len, 1, 0);
}

void wpa_hexdump_key(int level, const char *title, const void *buf, size_t len)
{
    _wpa_hexdump(level, title, (const u8*)buf, len, wpa_debug_show_keys, 0);
}
