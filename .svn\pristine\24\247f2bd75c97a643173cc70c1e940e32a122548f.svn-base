#include "scpi_gen_11ax_su.h"

#include "commonhandler.h"
#include <iostream>
#include "basehead.h"
#include "internal.h"
#include "StructuresDef.h"
#include "tester.h"
#include "tester_mt.h"
#include <sys/time.h>
#include <math.h>

int Is11axSU(SPCIUserParam *attr)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int demod = attr->PnWifi.get()->commonParam.standard;
        int PPDU = attr->PnWifi.get()->commonParam.subType;
        if (demod < WT_DEMOD_11AX_20M || demod > WT_DEMOD_11AX_80_80M)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }

        if (HE_SU_PPDU != PPDU && HE_EXTEND_PPDU != PPDU)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

static int GetSUIntParam(scpi_t * context, int minNum, int maxNum, int &Value)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = Is11axSU(attr);
        
        IF_BREAK(iRet);

        if (!SCPI_ParamInt(context, &Value, true))
        {
            iRet = SCPI_RES_ERR;
            break;
        }

        if (Value < minNum || Value > maxNum)
        {
            iRet = WT_ERR_CODE_PARAMETER_MISMATCH;
            break;
        }
    } while (0);

    return iRet;
}

scpi_result_t Set11AX_SU_STBC(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.STBC = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_ULDL(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.UL_DL = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_SpatialReuse(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;

    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 15, Value);
        IF_BREAK(iRet);
        memset(attr->PnWifi.get()->PN11ax_SU.Spatial_Reuse, 0,
            sizeof(attr->PnWifi.get()->PN11ax_SU.Spatial_Reuse));

        attr->PnWifi.get()->PN11ax_SU.Spatial_Reuse[0] = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_GLTFSize(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 4, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.GILTFSize = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_TXOP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 127, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.TXOP = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_BSSColor(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 63, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.BSScolor = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_Doppler(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Doppler = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_MidamblePeriodicity(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 1, 2, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Midamble_Periodicity = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_PE(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.PE = Value;
        if (!Value)
        {
            break;
        }
        iRet = GetSUIntParam(context, 0, 2, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.PE_Type = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_MCS(scpi_t * context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 13, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.MCS = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_Coding(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.CodingType = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_NSS(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 1, 8, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.NSS = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_DCM(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.DCM = Value;
    } while (0);


    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_BeamChange(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.BeamChange = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_Beamformed(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Beamformed = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_SoundingNDP(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.SoundingNDP = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_RUQMat(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 1, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Qmat_Preamble.Q_Flag = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_RUQMatNtx(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 1, 8, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Qmat_Preamble.Q_NTX = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_RUQMatType(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value = 0;
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        iRet = GetSUIntParam(context, 0, 2, Value);
        IF_BREAK(iRet);
        attr->PnWifi.get()->PN11ax_SU.Qmat_Preamble.Q_Type = Value;
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_RUQMatDelay(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    int Value[8] = {0};
    do
    {
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);
        for(int i = 0; i < 8; i++)
        {
            iRet = GetSUIntParam(context, -400, 400, Value[i]);
            IF_BREAK(iRet);
        }

        memcpy(attr->PnWifi.get()->PN11ax_SU.Qmat_Preamble.QDelay, Value, sizeof(Value));
    } while (0);

    return SCPI_ResultOK(context, iRet);
}

scpi_result_t Set11AX_SU_RUQMatMap(scpi_t *context)
{
    int iRet = WT_ERR_CODE_OK;
    do
    {
        int Cnt = context->parser_state.numberOfParameters;
        double Value[Cnt] = {0};
        int Diment = (int)sqrt(Cnt / 2);

        if (0 != (Cnt % 2) || Diment < 1 || Diment > 8) //必须是成对的数据,n*n的二维数组，n（1-8）
        {
            iRet = WT_ERR_CODE_UNKNOW_PARAMETER;
            break;
        }

        for (int i = 0; i < context->parser_state.numberOfParameters; i++)
        {
            if (!SCPI_ParamDouble(context, &Value[i], true))
            {
                iRet = SCPI_RES_ERR;
                break;
            }
        }

        int index = 0;
        SPCIUserParam *attr = static_cast<SPCIUserParam *>(context->user_context);

        for (int i = 0; i < Diment; i++)
        {
            for (int j = 0; j < Diment; j++)
            {
                attr->PnWifi->PN11ax_SU.Qmat_Preamble.MatrValue[i][j][0] = Value[index++];
                attr->PnWifi->PN11ax_SU.Qmat_Preamble.MatrValue[i][j][1] = Value[index++];
            }
        }

    } while (0);

    return SCPI_ResultOK(context, iRet);
}